<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
	http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.2.xsd
	http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.2.xsd"
	default-lazy-init="true">

	<context:property-placeholder location="classpath*:/shopxx.properties" ignore-resource-not-found="true" ignore-unresolvable="true" />

	<context:component-scan base-package="net.shopxx">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<import resource="classpath:spring-activiti.xml"/>
	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">
		<property name="driverClass" value="${jdbc.driver}" />
		<property name="jdbcUrl" value="${jdbc.url}" />
		<property name="user" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />
		<property name="initialPoolSize" value="${connection_pools.initial_pool_size}" />
		<property name="minPoolSize" value="${connection_pools.min_pool_size}" />
		<property name="maxPoolSize" value="${connection_pools.max_pool_size}" />
		<property name="maxIdleTime" value="${connection_pools.max_idle_time}" />
		<property name="acquireIncrement" value="${connection_pools.acquire_increment}" />
		<property name="checkoutTimeout" value="${connection_pools.checkout_timeout}" />
	</bean>

	<bean id="entityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
		<property name="dataSource" ref="dataSource" />
		<property name="persistenceXmlLocation" value="classpath*:/persistence.xml" />
		<!--  property name="persistenceUnitName" value="persistenceUnit" / -->
		<property name="packagesToScan" value="net.shopxx.*"/>
		<property name="jpaVendorAdapter">
			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
				<property name="showSql" value="false" />
				<property name="generateDdl" value="true" />
			</bean>
		</property>  
		<property name="jpaProperties">   
			<props>
				<prop key="hibernate.dialect">${hibernate.dialect}</prop>
				<prop key="hibernate.ejb.naming_strategy">org.hibernate.cfg.ImprovedNamingStrategy</prop>
				<prop key="hibernate.cache.use_second_level_cache">${hibernate.cache.use_second_level_cache}</prop>
				<prop key="hibernate.cache.region.factory_class">${hibernate.cache.region.factory_class}</prop>
				<prop key="hibernate.cache.use_query_cache">${hibernate.cache.use_query_cache}</prop>
				<prop key="hibernate.jdbc.fetch_size">${hibernate.jdbc.fetch_size}</prop>
				<prop key="hibernate.jdbc.batch_size">${hibernate.jdbc.batch_size}</prop>
				<prop key="hibernate.connection.isolation">2</prop>
				<prop key="javax.persistence.validation.mode">none</prop>
				<prop key="hibernate.search.default.directory_provider">org.hibernate.search.store.FSDirectoryProvider</prop>
				<prop key="hibernate.search.default.indexBase">${java.io.tmpdir}/${system.project_name}/index</prop>
			</props>
		</property>  
	</bean>

	<bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
		<property name="entityManagerFactory" ref="entityManagerFactory" />
	</bean>

	<tx:annotation-driven transaction-manager="transactionManager" />

	<cache:annotation-driven cache-manager="cacheManager" />

	<!--bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="targetClass" value="java.lang.System" />
		<property name="targetMethod" value="setProperty" />
		<property name="arguments">
			<list>
				<value>system.project_name</value>
				<value>${system.project_name}</value>
			</list>
		</property>
	</bean-->

	<bean id="ehCacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
		<property name="configLocation" value="classpath:/ehcache.xml" />
		<property name="shared" value="true" />
	</bean>

	<bean id="cacheManager" class="org.springframework.cache.ehcache.EhCacheCacheManager">
		<property name="cacheManager" ref="ehCacheManager" />
	</bean>

	<bean id="freeMarkerConfigurer" class="org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer">
		<property name="templateLoaderPaths" value="${template.loader_path}" />
		<property name="freemarkerSettings">
			<props>
				<prop key="defaultEncoding">${template.encoding}</prop>
				<prop key="url_escaping_charset">${url_escaping_charset}</prop>
				<prop key="locale">${locale}</prop>
				<prop key="template_update_delay">${template.update_delay}</prop>
				<prop key="tag_syntax">auto_detect</prop>
				<prop key="whitespace_stripping">true</prop>
				<prop key="classic_compatible">true</prop>
				<prop key="number_format">${template.number_format}</prop>
				<prop key="boolean_format">${template.boolean_format}</prop>
				<prop key="datetime_format">${template.datetime_format}</prop>
				<prop key="date_format">${template.date_format}</prop>
				<prop key="time_format">${template.time_format}</prop>
				<prop key="object_wrapper">freemarker.ext.beans.BeansWrapper</prop>
			</props>
		</property>
		<property name="freemarkerVariables">
			<map>
				<entry key="systemName" value="${system.name}" />
				<entry key="systemVersion" value="${system.version}" />
				<!--<entry key="systemDescription" value="${system.description}" /> 
				<entry key="systemShowPowered" value="#{${system.show_powered}}" />-->
				<entry key="base" value="#{servletContext.contextPath}" />
				<entry key="locale" value="${locale}" />
				<entry key="setting" value="#{T(net.shopxx.base.core.util.SettingUtils).get()}" />
				<entry key="message" value-ref="messageMethod" />
				<entry key="abbreviate" value-ref="abbreviateMethod" />
				<entry key="currency" value-ref="currencyMethod" />
				<entry key="pagination" value-ref="paginationDirective" />
				<!--entry key="execute_time" value-ref="executeTimeDirective" />
				<entry key="flash_message" value-ref="flashMessageDirective" />
				<entry key="current_member" value-ref="currentMemberDirective" />
				<entry key="seo" value-ref="seoDirective" />
				<entry key="ad_position" value-ref="adPositionDirective" />
				<entry key="ad" value-ref="adDirective" />
				<entry key="store_list" value-ref="storeListDirective" />
				<entry key="member_attribute_list" value-ref="memberAttributeListDirective" />
				<entry key="navigation_list" value-ref="navigationListDirective" />
				<entry key="tag_list" value-ref="tagListDirective" />
				<entry key="friend_link_list" value-ref="friendLinkListDirective" />
				<entry key="brand_list" value-ref="brandListDirective" />
				<entry key="article_list" value-ref="articleListDirective" />
				<entry key="article_category_root_list" value-ref="articleCategoryRootListDirective" />
				<entry key="article_category_parent_list" value-ref="articleCategoryParentListDirective" />
				<entry key="article_category_children_list" value-ref="articleCategoryChildrenListDirective" />
				<entry key="product_list" value-ref="productListDirective" />
				<entry key="product_category_root_list" value-ref="productCategoryRootListDirective" />
				<entry key="product_category_parent_list" value-ref="productCategoryParentListDirective" />
				<entry key="product_category_children_list" value-ref="productCategoryChildrenListDirective" />
				<entry key="review_list" value-ref="reviewListDirective" />
				<entry key="consultation_list" value-ref="consultationListDirective" />
				<entry key="promotion_list" value-ref="promotionListDirective" />
				<entry key="he_adPosition" value-ref="haierAdPositionDirective" />
				<entry key="he_position" value-ref="haierPositionDirective" />
				<entry key="order_product" value-ref="haierProductOrderSaleDirective" />
				<entry key="product_push_manage" value-ref="productPushManageDirective" />
				<entry key="sales_rank" value-ref="salesRankDirective" />
				<entry key="qrcodePutTypeDetail" value-ref="qrcodePutTypeDirective" /-->
			</map>
		</property>
	</bean>

	<bean id="localeChangeInterceptor" class="org.springframework.web.servlet.i18n.LocaleChangeInterceptor" /> 
	<bean id="defaultUrlMapping" class="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping">                 
		<property name="interceptors" ref="localeChangeInterceptor" />                 
		<property name="order">                         
			<value>1</value>               
		</property>       
	</bean>

	<bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
		<property name="cacheSeconds" value="${message.cache_seconds}" />
		<property name="useCodeAsDefaultMessage" value="true" />
		<property name="basenames">
			<list>
				<value>${message.path}</value>
			</list>
		</property>
	</bean>

	<bean id="localeResolver" class="org.springframework.web.servlet.i18n.FixedLocaleResolver">
		<property name="defaultLocale" value="${locale}" /> 
	</bean>

	<!--
	<bean id="imageCaptchaService" class="com.octo.captcha.service.image.DefaultManageableImageCaptchaService">
		<property name="captchaEngine">
			<bean class="net.shopxx.CaptchaEngine" />
		</property>
		<property name="minGuarantedStorageDelayInSeconds" value="3600" />
	</bean>

	<bean id="javaMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.auth">${mail.smtp.auth}</prop>
				<prop key="mail.smtp.timeout">${mail.smtp.timeout}</prop>
				<prop key="mail.smtp.starttls.enable">${mail.smtp.starttls.enable}</prop>
				<prop key="mail.smtp.socketFactory.class">javax.net.ssl.SSLSocketFactory</prop>
			</props>
		</property>
	</bean>

	<bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
		<property name="corePoolSize" value="${task.core_pool_size}" />
		<property name="maxPoolSize" value="${task.max_pool_size}" />
		<property name="queueCapacity" value="${task.queue_capacity}" />
		<property name="keepAliveSeconds" value="${task.keep_alive_seconds}" />
	</bean>     -->

	<!-- 本地注释了job配置，需要再打开 -->
<!--	<task:annotation-driven />-->
	<bean id="accessFilter" class="net.shopxx.common.AccessFilter"/>
</beans>