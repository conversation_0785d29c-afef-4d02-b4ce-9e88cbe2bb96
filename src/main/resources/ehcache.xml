<?xml version="1.0" encoding="UTF-8"?>
<ehcache
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance http://www.springmodules.org/schema/cache/springmodules-cache.xsd"
	xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd">

	<diskStore path="/app/cloud_nature/ehcache" />

	<defaultCache maxElementsInMemory="10" eternal="false"
		overflowToDisk="true" timeToLiveSeconds="7200">
		<!-- <cacheEventListenerFactory
			class="net.sf.ehcache.distribution.RMICacheReplicatorFactory" /> -->
	</defaultCache>

	<!-- <cacheManagerPeerProviderFactory
		class="net.sf.ehcache.distribution.RMICacheManagerPeerProviderFactory"
		properties="peerDiscovery=manual,rmiUrls=//localhost:40001/principal|//localhost:40002/principal|//localhost:40003/principal|//localhost:40004/principal"
		propertySeparator="," />

	<cacheManagerPeerListenerFactory
		class="net.sf.ehcache.distribution.RMICacheManagerPeerListenerFactory"
		properties="hostName=localhost,port=40003,socketTimeoutMillis=2000" /> -->

	<!-- 身份信息 缓存 -->
	<!-- <cache name="principal" maxElementsInMemory="100" eternal="false"
		overflowToDisk="true" timeToLiveSeconds="0" timeToIdleSeconds="3600">
		<cacheEventListenerFactory
			class="net.sf.ehcache.distribution.RMICacheReplicatorFactory" />
	</cache> -->

	<!-- shopxx.xml 缓存 -->
	<cache name="setting" maxElementsInMemory="100" eternal="true"
		overflowToDisk="true">
	</cache>
	<!-- 执行sql返回对象的 Field 缓存 -->
	<cache name="classfield" maxElementsInMemory="100" eternal="true"
		overflowToDisk="true" />

</ehcache>