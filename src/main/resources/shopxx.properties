#------------ System ------------
system.version=3.0 RELEASE
#system.description=SHOP++\u7F51\u4E0A\u5546\u57CE\u7CFB\u7EDF\u662F\u57FA\u4E8EJ2EE\u6280\u672F\u7684\u4F01\u4E1A\u7EA7\u7535\u5B50\u5546\u52A1\u5E73\u53F0\u7CFB\u7EDF\uFF0C\u4EE5\u5176\u5B89\u5168\u7A33\u5B9A\u3001\u5F3A\u5927\u6613\u7528\u3001\u9AD8\u6548\u4E13\u4E1A\u7B49\u4F18\u52BF\u8D62\u5F97\u4E86\u7528\u6237\u7684\u5E7F\u6CDB\u597D\u8BC4\u3002SHOP++\u4E3A\u5927\u3001\u4E2D\u3001\u5C0F\u4F01\u4E1A\u63D0\u4F9B\u4E00\u4E2A\u5B89\u5168\u3001\u9AD8\u6548\u3001\u5F3A\u5927\u7684\u7535\u5B50\u5546\u52A1\u89E3\u51B3\u65B9\u6848\uFF0C\u534F\u52A9\u4F01\u4E1A\u5FEB\u901F\u6784\u5EFA\u3001\u90E8\u7F72\u548C\u7BA1\u7406\u5176\u7535\u5B50\u5546\u52A1\u5E73\u53F0\uFF0C\u62D3\u5C55\u4F01\u4E1A\u9500\u552E\u6E20\u9053\uFF0C\u7A81\u663E\u7535\u5B50\u5546\u52A1\u5546\u4E1A\u4EF7\u503C\uFF0C\u81F4\u529B\u4E8E\u63A8\u52A8J2EE\u6280\u672F\u548C\u7535\u5B50\u5546\u52A1\u884C\u4E1A\u7684\u53D1\u5C55\u800C\u4E0D\u65AD\u52AA\u529B\u3002
#system.show_powered=false
system.name=cloud_marketing
#system.project_name=cloud_i18n_test

#------------ Common ------------
locale=zh_CN
url_escaping_charset=UTF-8

#------------ Template ------------
template.datetime_format=yyyy-MM-dd
template.time_format=HH:mm:ss
template.boolean_format=true,false
template.number_format=0.######
template.date_format=yyyy-MM-dd
template.update_delay=0
template.encoding=UTF-8
template.loader_path=/WEB-INF/template
template.suffix=.ftl

#------------ Message ------------
message.cache_seconds=0
message.path=/WEB-INF/language/message

#------------ Mail ------------
#mail.smtp.auth=true
#mail.smtp.timeout=25000
#mail.smtp.starttls.enable=false

#------------ Task ------------
task.core_pool_size=5
task.max_pool_size=50
task.queue_capacity=1000
task.keep_alive_seconds=60

#------------ Job ------------
#job.static_build.cron=0 0 1 * * ?

#------------ JDBC ------------
jdbc.driver=com.mysql.jdbc.Driver
jdbc.url=************************************************************************************
jdbc.password=nature#0210
jdbc.username=cloud_nature
#jdbc.url=*****************************************************************************************************************
#jdbc.password=cloud_nature#2018

#------------ Hibernate ------------
hibernate.dialect=org.hibernate.dialect.MySQLDialect
hibernate.cache.use_query_cache=false
hibernate.cache.use_second_level_cache=false
hibernate.cache.region.factory_class=org.hibernate.cache.impl.NoCachingRegionFactory
hibernate.jdbc.fetch_size=50
hibernate.jdbc.batch_size=30

#------------ ConnectionPools ------------
connection_pools.min_pool_size=5
connection_pools.acquire_increment=5
connection_pools.max_pool_size=100
connection_pools.initial_pool_size=5
connection_pools.max_idle_time=600
connection_pools.checkout_timeout=60000

#------------ MySQL ------------
#jdbc.driver=com.mysql.jdbc.Driver
#jdbc.url=**************************************************************************
#hibernate.dialect=org.hibernate.dialect.MySQLDialect

#------------ SQL Server ------------
#jdbc.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
#jdbc.url=***************************************************
#hibernate.dialect=org.hibernate.dialect.SQLServerDialect

#------------ Oracle ------------
#jdbc.driver=oracle.jdbc.OracleDriver
#jdbc.url=***************************************
#hibernate.dialect=org.hibernate.dialect.OracleDialect

intf.sync_data_isrun=false
intf.sync_data_url=http://***********:9001/intf/a/addmsg.jhtml

sync_invoke.isrun=false
intf.sync_invoke_url=http://***********:9001/intf/a/syncInvoke.jhtml

schedule.isrun=false
schedule.core_pool_size=0

push.isrun=false
push.core_pool_size=0

#  <EMAIL>;<EMAIL>
receive.mail.address=



dbintf.url=http://**************:8082
dbintf.username=link01
dbintf.password=7iURWx3JNDQFvhT7
dbintf.ssoUrl=http://sso-test.enaturehome.com:90/Resful
dbintf.ssoUrlIntf=http://sso-test.enaturehome.com:90/Resful
dbintf.ssoKey=11AAC64B2052093A3D77C0A419680C7F0AC066D1F790AE9B4ABD58C26ADDCA5E3A018528

##-------���Ի���----------
#####token
synintf.token=ee401fd7cba84ae5b14ad6c46e40dd0e
synintf.zbUrl=http://**************:8001

#--�а�����-------
#synintf.token="3d40ac2437994a9093285f5542dd86ff"
#synintf.zbUrl = https://*************:8082

#--------------------Link5-------------------------------
#link5.product.url=http://*************:8080/nature-link5-product
link5.product.url=https://gateuat.enaturehome.com:9092/nature-link5-product
link5.product.productInsertBacth=/api/pd/product/insertBacth
link5.product.categoryInsertBacth=/api/pd/category/insertBacth
#link5.system.url=http://*************:8080/nature-link5-system
link5.system.url=https://gateuat.enaturehome.com:9092/nature-link5-system
link5.system.getSendtoMsg=/api/sys/intf/record/getSendtoMsg?id=
link5.oauth.url=https://gateuat.enaturehome.com:9092
#link5.oauth.url=http://*************:8080
link5.oauth.token=/oauth/token
link5.account.url=https://gateuat.enaturehome.com:9092/nature-link5-account
#link5.account.url=http://*************:8080/nature-link5-account
#link5.account.url=http://************:8899
link5.account.storeSynchroData=/api/account/store/storeSynchroData
link5.account.shopSynchroData=/api/account/shop/shopSynchroData
link5.sale.url=https://gateuat.enaturehome.com:9092/nature-link5-sale
#link5.sale.url=http://*************:8080/nature-link5-sale
link5.sale.depositSync=/api/so/deposit/sync
link5.purchase.url=https://gateuat.enaturehome.com:9092/nature-link5-purchase
link5.purchase.orderSync=/api/po/order/sync
link5.purchase.receiptSync=/api/po/receipt/sync
link5.purchase.b2breturnSync=/api/po/return/sync

link5.oauth.authUsername=admin
link5.oauth.authPassword=admin
link5.oauth.username=link6
#link5.oauth.username=Link5
link5.oauth.password=********
link5.oauth.appid=1
link5.oauth.grantType=user_token
link5.oauth.tenantId=1025
#-------------------Link5 end----------------------------

#????
ccs.url=https://dev.java.doscs.com/daziran/myapi
#????
#ccs.url=https://ccs.enaturehome.com/dospaas/myapi

link5.fiveGds.url=https://gateuat.enaturehome.com:9092/nature-link5-sale/api/so/quickCompensation/queryPageForExternalSystem
link5.fiveGd.url=https://gateuat.enaturehome.com:9092/nature-link5-sale/api/so/quickCompensation/infoForExternalSystem/

#??
#link4.url=http://localhost:8001/
#????
link4.url=http://linktest.enaturehome.com:8001/