<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.2.xsd"
       default-lazy-init="true">

    <bean id="processEngineConfiguration" class="org.activiti.spring.SpringProcessEngineConfiguration">
        <property name="dataSource" ref="dataSource"/>
        <property name="transactionManager" ref="transactionManager"/>
        <property name="databaseSchema" value="ACT"/>
        <property name="databaseSchemaUpdate" value="true"/>
        <property name="databaseType" value="mysql"/>
        <property name="jobExecutorActivate" value="true"/>
        <!-- 三种方式集成自定义用户、用户组（1）维护用户管理和角色管理时，将相关信息同步到act_id_**表中-->
        <!--（2）删除act_id_**表，建立同名视图,本文使用(最简单)-->
        <property name="dbIdentityUsed" value="false"/>
        <!--（3）重写相关接口，不再从act_id_**表中取数据-->
        <!--<property name="customSessionFactories">
            <list>
                <bean class="com.cnpc.framework.activiti.service.impl.CustomUserEntityManagerFactory">
                    <property name="customUserEntityManager">
                        <bean class="com.cnpc.framework.activiti.service.impl.CustomUserEntityManager">
                        </bean>
                    </property>
                </bean>
                <bean class="com.cnpc.framework.activiti.service.impl.CustomGroupEntityManagerFactory">
                    <property name="customGroupEntityManager">
                        <bean class="com.cnpc.framework.activiti.service.impl.CustomGroupEntityManager">
                        </bean>
                    </property>
                </bean>
            </list>
        </property>-->
        <!--解决部署后生成png图片中文乱码的问题-->
        <property name="xmlEncoding" value="utf-8" />
        <property name="activityFontName" value="宋体"/>
        <property name="labelFontName" value="宋体"/>
        <property name="annotationFontName" value="宋体"/>
       
    </bean>
    <bean id="processEngine" class="org.activiti.spring.ProcessEngineFactoryBean">
        <property name="processEngineConfiguration" ref="processEngineConfiguration"/>
    </bean>
    <bean id="objectMapper" class="com.fasterxml.jackson.databind.ObjectMapper"/>
    <bean id="identityService" factory-bean="processEngine" factory-method="getIdentityService"/>
    <bean id="formService" factory-bean="processEngine" factory-method="getFormService"/>
    <bean id="repositoryService" factory-bean="processEngine" factory-method="getRepositoryService"/>
    <bean id="runtimeService" factory-bean="processEngine" factory-method="getRuntimeService"/>
    <bean id="taskService" factory-bean="processEngine" factory-method="getTaskService"/>
    <bean id="historyService" factory-bean="processEngine" factory-method="getHistoryService"/>
    <bean id="managementService" factory-bean="processEngine" factory-method="getManagementService"/>
    <bean id="dynamicBpmnService" factory-bean="processEngine" factory-method="getDynamicBpmnService"/>


</beans>