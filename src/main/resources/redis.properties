redis.host=*************
redis.port=6379
redis.password=dzr20200307twkj
redis.timeout=30000
#\u6700\u5927\u80fd\u591f\u4fdd\u6301idel\u72b6\u6001\u7684\u5bf9\u8c61\u6570 
redis.maxIdle=6
#\u6700\u5927\u5206\u914d\u7684\u5bf9\u8c61\u6570    
redis.maxActive=32
redis.maxWait=15000
#\u591a\u957f\u65f6\u95f4\u68c0\u67e5\u4e00\u6b21\u8fde\u63a5\u6c60\u4e2d\u7a7a\u95f2\u7684\u8fde\u63a5 
redis.timeBetweenEvictionRunsMillis=60000  
#\u7a7a\u95f2\u8fde\u63a5\u591a\u957f\u65f6\u95f4\u540e\u4f1a\u88ab\u6536\u56de 
redis.minEvictableIdleTimeMillis=30000 
#\u5f53\u8c03\u7528borrow Object\u65b9\u6cd5\u65f6\uff0c\u662f\u5426\u8fdb\u884c\u6709\u6548\u6027\u68c0\u67e5   
redis.testOnBorrow=true
########reids\u7f16\u7801\u683c\u5f0f 
redis.encode=utf-8
######\u7f13\u5b58\u8fc7\u671f\u65f6\u95f4 \u79d2  1000*60*60*24*7 \u4e03\u5929 
redis.expire=604800000
####\u662f\u5426\u5f00\u542fRedis\u670d\u52a1\u5e94\u7528 
redis.unlock=false
redis.database=0