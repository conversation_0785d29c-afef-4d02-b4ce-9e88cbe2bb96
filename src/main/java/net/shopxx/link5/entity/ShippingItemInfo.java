package net.shopxx.link5.entity;

import java.math.BigDecimal;

public class ShippingItemInfo {

	private static final long serialVersionUID = 1L;
	
	String vonderCode;
	
	BigDecimal quantity;
	
	BigDecimal price;
	
	String productLevel;
	
	String closedQuantity;
	
	public String getClosedQuantity() {
		return closedQuantity;
	}

	public void setClosedQuantity(String closedQuantity) {
		this.closedQuantity = closedQuantity;
	}
	
	public String getProductLevel() {
		return productLevel;
	}

	public void setProductLevel(String productLevel) {
		this.productLevel = productLevel;
	}

	public String getWoodTypeOrColor() {
		return woodTypeOrColor;
	}

	public void setWoodTypeOrColor(String woodTypeOrColor) {
		this.woodTypeOrColor = woodTypeOrColor;
	}

	public String getColourNumber() {
		return colourNumber;
	}

	public void setColourNumber(String colourNumber) {
		this.colourNumber = colourNumber;
	}

	String woodTypeOrColor;
	
	String colourNumber;
	/**
	 * 支数
	 */
	 BigDecimal assistQuantity1;
	 
      public BigDecimal getAssistQuantity1() {
		return assistQuantity1;
	}

	public void setAssistQuantity1(BigDecimal assistQuantity1) {
		this.assistQuantity1 = assistQuantity1;
	}

	public BigDecimal getAssistQuantity2() {
		return assistQuantity2;
	}

	public void setAssistQuantity2(BigDecimal assistQuantity2) {
		this.assistQuantity2 = assistQuantity2;
	}

	public BigDecimal getScatteredQuantity() {
		return scatteredQuantity;
	}

	public void setScatteredQuantity(BigDecimal scatteredQuantity) {
		this.scatteredQuantity = scatteredQuantity;
	}

	/**
       * 箱数
       */
	 BigDecimal assistQuantity2;
	 
	 /**
	  * 零散支数
	  */
	 BigDecimal scatteredQuantity;
	
	/**
	 * 发货单行ID
	 */
	Long integrationId;
	
	public Long getIntegrationId() {
		return integrationId;
	}

	public void setIntegrationId(Long integrationId) {
		this.integrationId = integrationId;
	}

	public Long getIntegrationId2() {
		return integrationId2;
	}

	public void setIntegrationId2(Long integrationId2) {
		this.integrationId2 = integrationId2;
	}

	/**
	 * 订单行ID
	 */
	Long integrationId2;

	public String getVonderCode() {
		return vonderCode;
	}

	public void setVonderCode(String vonderCode) {
		this.vonderCode = vonderCode;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	
}
