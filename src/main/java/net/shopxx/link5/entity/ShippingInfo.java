package net.shopxx.link5.entity;

import java.math.BigDecimal;
import java.util.List;

public class ShippingInfo {

	public Integer getBillStatus() {
		return billStatus;
	}

	public void setBillStatus(Integer billStatus) {
		this.billStatus = billStatus;
	}

	private static final long serialVersionUID = 1L;
	
	Long orderIntegrate;
	
	Long integrationId;
	
	String integrationSn;
	
	String creatorPhone;
	
	BigDecimal discountAmount;
	
	Long storeIntegrate;
	
	String creator;
	
	String smethod;

	String warehouseName;
	
	String erpDate;
	
	String erpRemark;

	Long sourceTypeId; 
	
	Integer billStatus;

	List<ShippingItemInfo> itemList;
	
	private AccCustomer accCustomer;
	
	public String getSmethod() {
		return smethod;
	}

	public void setSmethod(String smethod) {
		this.smethod = smethod;
	}

	public String getWarehouseName() {
		return warehouseName;
	}

	public void setWarehouseName(String warehouseName) {
		this.warehouseName = warehouseName;
	}

	public String getErpDate() {
		return erpDate;
	}

	public void setErpDate(String erpDate) {
		this.erpDate = erpDate;
	}

	public String getErpRemark() {
		return erpRemark;
	}

	public void setErpRemark(String erpRemark) {
		this.erpRemark = erpRemark;
	}
	
	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Long getSourceTypeId() {
		return sourceTypeId;
	}

	public void setSourceTypeId(Long sourceTypeId) {
		this.sourceTypeId = sourceTypeId;
	}
	
	public Long getStoreIntegrate() {
		return storeIntegrate;
	}

	public void setStoreIntegrate(Long storeIntegrate) {
		this.storeIntegrate = storeIntegrate;
	}

	public Long getOrderIntegrate() {
		return orderIntegrate;
	}

	public void setOrderIntegrate(Long orderIntegrate) {
		this.orderIntegrate = orderIntegrate;
	}

	public Long getIntegrationId() {
		return integrationId;
	}

	public void setIntegrationId(Long integrationId) {
		this.integrationId = integrationId;
	}

	public String getIntegrationSn() {
		return integrationSn;
	}

	public void setIntegrationSn(String integrationSn) {
		this.integrationSn = integrationSn;
	}

	public String getCreatorPhone() {
		return creatorPhone;
	}

	public void setCreatorPhone(String creatorPhone) {
		this.creatorPhone = creatorPhone;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public List<ShippingItemInfo> getItemList() {
		return itemList;
	}

	public void setItemList(List<ShippingItemInfo> itemList) {
		this.itemList = itemList;
	}

	public AccCustomer getAccCustomer() {
		return accCustomer;
	}

	public void setAccCustomer(AccCustomer accCustomer) {
		this.accCustomer = accCustomer;
	}
	
}
