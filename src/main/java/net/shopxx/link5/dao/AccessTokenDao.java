package net.shopxx.link5.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.link5.entity.AccessToken;
import org.springframework.stereotype.Repository;

@Repository("link5AccessTokenDao")
public class AccessTokenDao extends DaoCenter{
	
	public AccessToken findAccessToken() {
		String sql = "SELECT * FROM link5_access_token ORDER BY id DESC LIMIT 1";
		try {
			return getNativeDao().findSingleNoManaged(sql, new Object[] {}, AccessToken.class);
		} catch (Exception e) {
			LogUtils.error("从数据库获取Link5Token出错：", e);
			return null;
		}
	}
	
	public int delete(Long id) {
		if(id == null) {
			return 0;
		}
		String sql = "DELETE FROM link5_access_token WHERE id = ?";
		try {
			return getNativeDao().delete(sql, new Object[] {id});
		} catch (Exception e) {
			LogUtils.error("从数据库删除Link5Token出错，id=："+id, e);
			return 0;
		}
	}
	
//	public void saveAccessToken(AccessToken token) {
//		String deleteSql = "INSERT link5_access_token";
//		getNativeDao().delete(deleteSql, new Object[] {});
//	}
//	
//	public void deleteAccessToken() {
//		String deleteSql = "DELETE FROM link5_access_token";
//		getNativeDao().delete(deleteSql, new Object[] {});
//	}

}
