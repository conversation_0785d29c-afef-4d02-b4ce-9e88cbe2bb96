package net.shopxx.link5.controller;

import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller("shippingInfoController")
@RequestMapping("/link5/shippingInfo")
public class ShippingInfoController extends BaseController{
	
	
	@Resource(name = "shippingInfoToLink5ServiceImpl")
	private ShippingInfoToLink5Service shippingInfoToLink5Service;
	
	
	  /**
     * 列表数据
     */
    @RequestMapping(value = "/sendOrderToLink5", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg sendOrderToLink5(Long[] ids) {

    	shippingInfoToLink5Service.synchronizationOrder(ids);
        return success("同步成功");
    }
    
    
    /**
     * 列表数据
     */
    @RequestMapping(value = "/sendShippingToLink5", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg sendShippingToLink5(Long[] ids) {

    	shippingInfoToLink5Service.synchronizationShipping(ids);
        return success("同步成功");
    }
    
    
    /**
     * 列表数据
     */
    @RequestMapping(value = "/sendB2bReturnsToLink5", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg sendB2bReturnsToLink5(Long[] ids) {

    	shippingInfoToLink5Service.synchronizationB2bReturns(ids);
        return success("同步成功");
    }
	

}
