package net.shopxx.link5.service;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.link5.entity.ShippingInfo;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Shipping;

import java.util.List;

public interface ShippingInfoToLink5Service extends BaseService<A1_MessageTo>{
	
	public void saveShippingInfoTo(Shipping shipping);
	
	public void saveOrderInfoTo(Order order,Integer flag);

	public List<ShippingInfo> convertShippingInfo(Shipping shipping);

	public void syncShipping(A1_MessageTo a1_MessageTo);
	
	public void findShippingInfoResult(A1_MessageToH a1_MessageTo);

	public void syncShipped(A1_MessageTo a1_MessageTo);
	
	public void syncB2bReturns(A1_MessageTo a1_MessageTo);

	public void saveB2bReturnsInfoTo(B2bReturns b2bReturns, Integer flag);
	
	void synchronizationOrder(Long[] ids);
	
	void synchronizationB2bReturns(Long[] ids);
	
	void synchronizationShipping(Long[] ids);
}
