package net.shopxx.link5.service.impl;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.link5.dao.AccessTokenDao;
import net.shopxx.link5.entity.AccessToken;
import net.shopxx.link5.service.AccessTokenService;
import net.shopxx.util.HttpUtil;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Service("link5AccessTokenService")
public class AccessTokenServiceImpl extends BaseServiceImpl<AccessToken>
implements AccessTokenService{

	@Autowired
	@Qualifier("link5AccessTokenDao")
	private AccessTokenDao accessTokenDao;

	@Autowired
	@Qualifier("stringRedisTemplate")
	private StringRedisTemplate redisTemplate;


	private static final String URL = Global.getLoader().getProperty("link5.oauth.url");
	private static final String OAUTH_TOKEN = Global.getLoader().getProperty("link5.oauth.token");
	
	private static final String USERNAME = Global.getLoader().getProperty("link5.oauth.username");
	private static final String PASSWOED = Global.getLoader().getProperty("link5.oauth.password");
	private static final String GRANT_TYPE = Global.getLoader().getProperty("link5.oauth.grantType");
	private static final String TENANT_ID = Global.getLoader().getProperty("link5.oauth.tenantId");
	private static final String APP_ID = Global.getLoader().getProperty("link5.oauth.appid");

	private static final String AUTH_USERNAME = Global.getLoader().getProperty("link5.oauth.authUsername");
	private static final String AUTH_PASSWORD = Global.getLoader().getProperty("link5.oauth.authPassword");
	
	

	@Override
	public AccessToken getAccessToken() {
		ValueOperations<String, String> opsForValue = redisTemplate.opsForValue();
		AccessToken accessToken = null;
		String tokenStr = opsForValue.get("ACCESS_TOKEN_OF_LINK5");
		LogUtils.debug("loading access token from redis...");
		if(tokenStr!=null) {
			accessToken = JsonUtils.toObject(tokenStr, AccessToken.class);
		}
		if(accessToken==null) {
			LogUtils.debug("failed to load access token from redis.");
			LogUtils.debug("loading access token from database...");
			accessToken = getAccessTokenFromDatabase();
			if(accessToken==null) {
				LogUtils.debug("failed to load access token from database.");
				LogUtils.debug("loading access token from api.");
				accessToken = getAccessTokenFromApi();
				if(accessToken==null) {
					LogUtils.debug("failed to load access token from api.");
					ExceptionUtil.throwServiceException("获取微服务平台AccessToken失败");
				}
				accessToken.setCompanyInfoId(9L);
				this.save(accessToken);
			}
			opsForValue.set("ACCESS_TOKEN_OF_LINK5", JsonUtils.toJson(accessToken),30,TimeUnit.MINUTES);
		}
		LogUtils.debug("load access token seccessfully.");
		return accessToken; 
	}

	@Override
	public AccessToken refreshAccessToken() {
		ValueOperations<String, String> opsForValue = redisTemplate.opsForValue();
		AccessToken accessToken = null;
		//重新获取token
		AccessToken newAccessToken = getAccessTokenFromApi();
		if(newAccessToken==null) {
			LogUtils.debug("failed to load access token from api.");
			ExceptionUtil.throwServiceException("获取微服务平台AccessToken失败");
		}
		newAccessToken.setCompanyInfoId(9L);
		//删除数据库中的token
		accessToken = getAccessTokenFromDatabase();
		if(accessToken!=null) {
			accessTokenDao.delete(accessToken.getId());
		}
		//删除redis中的token
		String tokenStr = opsForValue.get("ACCESS_TOKEN_OF_LINK5");
		if(tokenStr!=null) {
			redisTemplate.delete("ACCESS_TOKEN_OF_LINK5");
		}
		//持久化token到数据库
		this.save(newAccessToken);
		//保存token到redis 30分钟过期
		opsForValue.set("ACCESS_TOKEN_OF_LINK5", JsonUtils.toJson(newAccessToken),30,TimeUnit.MINUTES);

		LogUtils.debug("load access token seccessfully.");
		return newAccessToken; 
	}

	public AccessToken getAccessTokenFromDatabase() {
		return accessTokenDao.findAccessToken();
	}

	@Override
	public AccessToken getAccessTokenFromApi() {
		String auth = AUTH_USERNAME + ":" + AUTH_PASSWORD;

		Map<String, String> params = new HashMap<String, String>();
		params.put("username", USERNAME);
		params.put("password", PASSWOED);
		params.put("grant_type", GRANT_TYPE);
		params.put("tenantId",TENANT_ID);
		params.put("appId",APP_ID);
		Map<String, String> header = new HashMap<String, String>();

		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(Charset.forName("US-ASCII")));
		String authHeader = "Basic " + new String(encodedAuth);
		header.put("authorization", authHeader);
		header.put("Content-Type", "application/x-www-form-urlencoded");
		try {
			LogUtils.debug("request url:"+URL+OAUTH_TOKEN);
			LogUtils.debug("body:"+params);
			LogUtils.debug("header:"+header);
			String json = HttpUtil.httpPost(URL+OAUTH_TOKEN,params,header);
			LogUtils.debug("response:"+json);
			AccessToken token = JsonUtils.toObject(json,AccessToken.class);
//			if(token.getAccessToken()==null) {
//				return null;
//			}
			return token;
		}catch (Exception e) {
			LogUtils.error("获取微服务平台token出错",e);
			return null;
		}
	}

	public static void main(String[] args) {
		String APP_KEY = "admin";
		String SECRET_KEY = "admin";
		String auth = APP_KEY + ":" + SECRET_KEY;

		Map<String, String> params = new HashMap<String, String>();
		params.put("username", USERNAME);
		params.put("password", PASSWOED);
		params.put("grant_type", GRANT_TYPE);
		params.put("tenantId",TENANT_ID);
		Map<String, String> header = new HashMap<String, String>();

		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(Charset.forName("US-ASCII")));
		String authHeader = "Basic " + new String(encodedAuth);
		header.put("authorization", authHeader);
		header.put("Content-Type", "application/x-www-form-urlencoded");
		String json = HttpUtil.httpPost("http://gateway.gitlab.enaturehome.com:8080/oauth/token",params,header);
		AccessToken token = JsonUtils.toObject(json,AccessToken.class);
	}




}
