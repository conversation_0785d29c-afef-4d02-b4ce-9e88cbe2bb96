package net.shopxx.link5.service.impl;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.core.util.HttpClientUtil;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.A1_MessageToService;
import net.shopxx.link5.entity.*;
import net.shopxx.link5.enums.Link5B2bReturnsStatus;
import net.shopxx.link5.service.AccessTokenService;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import net.shopxx.link5.util.AccCustomerUtils;
import net.shopxx.link5.util.StoreSyncErrMsgResolver;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.service.OrderCloseService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.ShippingService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("shippingInfoToLink5ServiceImpl")
public class ShippingInfoToLink5ServiceImpl extends BaseServiceImpl<A1_MessageTo> implements  ShippingInfoToLink5Service{
	
	
	private static final String PRUCHASE_URL = Global.getLoader().getProperty("link5.purchase.url");
	private static final String ORDER_SYNC= Global.getLoader().getProperty("link5.purchase.orderSync");
	private static final String B2BRETURN_SYNC= Global.getLoader().getProperty("link5.purchase.b2breturnSync");
	private static final String RECIEPT_SYNC= Global.getLoader().getProperty("link5.purchase.receiptSync");
	private static final String SYSTEM_URL = Global.getLoader().getProperty("link5.system.url");
	private static final String GET_SEND_TO_MSG = Global.getLoader().getProperty("link5.system.getSendtoMsg");
	private static final String[] IGNORE_PROPERTIES = new String[] { BaseEntity.ID_PROPERTY_NAME,
			BaseEntity.CREATE_DATE_PROPERTY_NAME, BaseEntity.MODIFY_DATE_PROPERTY_NAME };
	private static final int MSG_TYPE = 1004;
	
	@Autowired
	private AccessTokenService accessTokenService;
	@Autowired
	private A1_MessageToService a1_MessageToService;
	@Autowired
	private CompanyInfoBaseService companyInfoBaseService;
	@Autowired
	private A1_MessageToHService a1_MessageToHService;
	@Resource(name = "orderCloseServiceImpl")
	private OrderCloseService orderCloseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "b2bReturnsServiceImpl")
	private B2bReturnsService b2bReturnsService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	
	/**
	 * 保存同步退货申请单信息
	 * @param b2bReturns 退货申请单信息
	 */
	@Override
	public void saveB2bReturnsInfoTo(B2bReturns b2bReturns,Integer flag) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("field1", b2bReturns.getSn()));
		filters.add(Filter.eq("type", 37));
		List<A1_MessageTo> list=a1_MessageToService.findList(null, filters, null);
		if(list.size()>0) {
			for(A1_MessageTo a :list) {
				a1_MessageToService.delete(a);
			}
		}
		
		A1_MessageTo a1 =  new A1_MessageTo();
		
		List<B2bReturnsInfo> b2bReturnsInfo = convertB2bReturnsInfo(b2bReturns,flag);
		String json = JsonUtils.toJson(b2bReturnsInfo);
		CompanyInfo companyInfo = companyInfoBaseService.find(9L);
		a1.setData(json);
		a1.setType(37);
		a1.setField1(b2bReturns.getSn());
		a1.setCompanyInfoId(companyInfo.getId());
		a1.setCompanytoken(companyInfo.getCompany_code());
		a1_MessageToService.save(a1);
	}
	
	/**
	 * 构造推送LINK5数据-退货申请单信息
	 * @param b2bReturns
	 * @return
	 */
	private List<B2bReturnsInfo> convertB2bReturnsInfo(B2bReturns b2bReturns,Integer flag) {
		List<B2bReturnsInfo> list= new ArrayList<B2bReturnsInfo>();
		B2bReturnsInfo b2bReturnsInfo = new B2bReturnsInfo();
		b2bReturnsInfo.setCreatorPhone(b2bReturns.getStoreMember().getMember().getMobile());
		b2bReturnsInfo.setDiscountAmount(BigDecimal.ZERO);
		b2bReturnsInfo.setIntegrationSn(b2bReturns.getSn());
		b2bReturnsInfo.setIntegrationId(b2bReturns.getId());
		/**经销商信息添加LINK5归属经销商。如果归属经销商不为空，则订单、发货、退货申请单同步租户以归属经销商，否则保持原方式同步(客户管理-客户维护-推送设置)*/
		if (b2bReturns.getStore().getStoreLink5() != null && b2bReturns.getStore().getStoreLink5().getId() != null) {
			b2bReturnsInfo.setStoreIntegrate(b2bReturns.getStore().getStoreLink5().getLinkFourId() == null ? 0
					: b2bReturns.getStore().getStoreLink5().getLinkFourId());
		} else {
			b2bReturnsInfo.setStoreIntegrate(
					b2bReturns.getStore().getLinkFourId() == null ? 0 : b2bReturns.getStore().getLinkFourId());
		}
		
		if(b2bReturns.getStatus() == 1 
				|| b2bReturns.getStatus() == 4 
				|| b2bReturns.getStatus() == 5){
			//已审核
			b2bReturnsInfo.setBillStatus(Link5B2bReturnsStatus.CHECKED.getState());
		}else if(b2bReturns.getStatus() == 8){
			//已关闭
			b2bReturnsInfo.setBillStatus(Link5B2bReturnsStatus.CLOSED.getState());
		}
		
		b2bReturnsInfo.setSourceTypeId(2L);
		b2bReturnsInfo.setCreator(b2bReturns.getStoreMember().getName());
		//发运方式
		b2bReturnsInfo.setSmethod("");
		//发货仓库
		b2bReturnsInfo.setWarehouseName(b2bReturns.getWarehouse()==null?"":b2bReturns.getWarehouse().getName());
		b2bReturnsInfo.setAccCustomer(AccCustomerUtils.create(b2bReturns));
		
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
		List<B2bReturnsItemInfo> b2bReturnsInfoItems = new ArrayList<B2bReturnsItemInfo>();
		for(B2bReturnsItem b2bReturnsItem : b2bReturnsItems) {
			B2bReturnsItemInfo b2bReturnsInfoItem= new B2bReturnsItemInfo();
			b2bReturnsInfoItem.setPrice(b2bReturnsItem.getPrice());
			b2bReturnsInfoItem.setVonderCode(b2bReturnsItem.getProduct().getVonderCode());
			b2bReturnsInfoItem.setIntegrationId(b2bReturnsItem.getId());
			b2bReturnsInfoItem.setAssistQuantity1(b2bReturnsItem.getBranchQuantity()==null? new BigDecimal("0") : b2bReturnsItem.getBranchQuantity());
			b2bReturnsInfoItem.setAssistQuantity2(b2bReturnsItem.getBoxQuantity()==null? new BigDecimal("0") : b2bReturnsItem.getBoxQuantity());
			b2bReturnsInfoItem.setScatteredQuantity(b2bReturnsItem.getScatteredQuantity());
			b2bReturnsInfoItem.setQuantity(b2bReturnsItem.getQuantity());	
			b2bReturnsInfoItem.setClosedQuantity("");
			//产品等级
			b2bReturnsInfoItem.setProductLevel(b2bReturnsItem.getProductLevel()==null?"":b2bReturnsItem.getProductLevel().getValue());
			//木种花色
			b2bReturnsInfoItem.setWoodTypeOrColor(b2bReturnsItem.getWoodTypeOrColor()==null?"":b2bReturnsItem.getWoodTypeOrColor());
			//色号
			b2bReturnsInfoItem.setColourNumber(b2bReturnsItem.getColorNumbers()==null?"":b2bReturnsItem.getColorNumbers().getValue());
			//含水率
			b2bReturnsInfoItem.setMoistureContent(b2bReturnsItem.getMoistureContents()==null?"":b2bReturnsItem.getMoistureContents().getValue());
		
			b2bReturnsInfoItems.add(b2bReturnsInfoItem);
		}
		b2bReturnsInfo.setItemList(b2bReturnsInfoItems);
		list.add(b2bReturnsInfo);
		
		return list;
	}
	
	@Override
	public void saveShippingInfoTo(Shipping shipping) {
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("field1", shipping.getSn()));
		filters.add(Filter.eq("type", 36));
		List<A1_MessageTo> list=a1_MessageToService.findList(null, filters, null);
		if(list.size()>0) {
			for(A1_MessageTo a :list) {
				a1_MessageToService.delete(a);
			}
		}
		
		A1_MessageTo a1 =  new A1_MessageTo();
		
		List<ShippingInfo> shippingInfo = convertShippingInfo(shipping);
		String json = JsonUtils.toJson(shippingInfo);
		CompanyInfo companyInfo = companyInfoBaseService.find(9L);
		a1.setData(json);
		a1.setType(36);
		a1.setField1(shipping.getSn());
		a1.setCompanyInfoId(companyInfo.getId());
		a1.setCompanytoken(companyInfo.getCompany_code());
		a1_MessageToService.save(a1);
		
	}
	
	@Override
	public void saveOrderInfoTo(Order order,Integer flag) {
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("field1", order.getSn()));
		filters.add(Filter.eq("type", 35));
		List<A1_MessageTo> list=a1_MessageToService.findList(null, filters, null);
		if(list.size()>0) {
			for(A1_MessageTo a :list) {
				a1_MessageToService.delete(a);
			}
		}
		
		A1_MessageTo a1 =  new A1_MessageTo();
		
		List<ShippingInfo> shippingInfo = convertOrderInfo(order,flag);
		String json = JsonUtils.toJson(shippingInfo);
		CompanyInfo companyInfo = companyInfoBaseService.find(9L);
		a1.setData(json);
	
		a1.setType(35);
	
		a1.setField1(order.getSn());
		a1.setCompanyInfoId(companyInfo.getId());
		a1.setCompanytoken(companyInfo.getCompany_code());
		a1_MessageToService.save(a1);
	}
	
	
	/**
	 * 构造推送LINK5数据
	 */
	public List<ShippingInfo> convertOrderInfo(Order order,Integer flag) {
		
		List<ShippingInfo> list= new ArrayList<ShippingInfo>();
		ShippingInfo shippingInfo = new ShippingInfo();
		shippingInfo.setCreatorPhone(order.getStoreMember().getMember().getMobile());
		shippingInfo.setDiscountAmount(BigDecimal.ZERO);
		shippingInfo.setIntegrationSn(order.getSn());
		shippingInfo.setIntegrationId(order.getId());
		if(order.getStore().getLinkFourId() != null) {
			/**系统经销商信息添加LINK5归属经销商。如果归属经销商不为空，则订单、发货同步租户以归属经销商，否则保持原方式同步*/
			if(order.getStore().getStoreLink5() != null && 
					order.getStore().getStoreLink5().getLinkFourId() !=null) {
				shippingInfo.setStoreIntegrate(order.getStore().getStoreLink5().getLinkFourId());
			}else {
				shippingInfo.setStoreIntegrate(order.getStore().getLinkFourId());
			}
			
		}else {
			shippingInfo.setStoreIntegrate(0L);
		}
		shippingInfo.setBillStatus(2);
		shippingInfo.setSourceTypeId(2L);
		shippingInfo.setCreator(order.getStoreMember().getName());
		if(order.getOrderStatus() == OrderStatus.cancelled) {
			shippingInfo.setBillStatus(6);
		}else {
			shippingInfo.setBillStatus(2);
		}
		shippingInfo.setAccCustomer(AccCustomerUtils.create(order));
		
		
		
		List<OrderItem> orderItems=order.getOrderItems();
		List<ShippingItemInfo> shippingInfoItems= new ArrayList<ShippingItemInfo>();
		for(OrderItem orderItem : orderItems) {
			ShippingItemInfo shippingInfoItem= new ShippingItemInfo();
			shippingInfoItem.setPrice(orderItem.getPrice());
			shippingInfoItem.setVonderCode(orderItem.getProduct().getVonderCode());
			shippingInfoItem.setIntegrationId(orderItem.getId());
			shippingInfoItem.setAssistQuantity1(orderItem.getBranchQuantity()==null? new BigDecimal("0") : orderItem.getBranchQuantity());
			shippingInfoItem.setAssistQuantity2(orderItem.getBoxQuantity()==null? new BigDecimal("0") : orderItem.getBoxQuantity());
			shippingInfoItem.setScatteredQuantity(orderItem.getScatteredQuantity());
			shippingInfoItem.setQuantity(orderItem.getQuantity());
				
			shippingInfoItem.setClosedQuantity(orderCloseService.findClosedQuantityByOrderItemId(orderItem.getId())
						.get("closed_quantity_nb").toString());
			
		
			shippingInfoItems.add(shippingInfoItem);
		}
		shippingInfo.setItemList(shippingInfoItems);
		list.add(shippingInfo);
		
		return list;
	}
	
	/**
	 * 构造推送LINK5数据
	 */
	public List<ShippingInfo> convertShippingInfo(Shipping shipping) {
		
		List<ShippingInfo> list= new ArrayList<ShippingInfo>();
		ShippingInfo shippingInfo = new ShippingInfo();
		shippingInfo.setCreatorPhone(shipping.getStoreMember().getMember().getMobile());
		shippingInfo.setDiscountAmount(BigDecimal.ZERO);
		shippingInfo.setIntegrationId(shipping.getId());
		shippingInfo.setIntegrationSn(shipping.getSn()+"/"+shipping.getErpSn());
		if(shipping.getStore().getLinkFourId()!=null){
			/**系统经销商信息添加LINK5归属经销商。如果归属经销商不为空，则订单、发货同步租户以归属经销商，否则保持原方式同步*/
			if(shipping.getStore().getStoreLink5() != null && 
					shipping.getStore().getStoreLink5().getLinkFourId() !=null) {
				shippingInfo.setStoreIntegrate(shipping.getStore().getStoreLink5().getLinkFourId());
			}else {
				shippingInfo.setStoreIntegrate(shipping.getStore().getLinkFourId());
			}
		}else {
			shippingInfo.setStoreIntegrate(0L);
		}
		shippingInfo.setBillStatus(2);
		shippingInfo.setSourceTypeId(2L);
		shippingInfo.setCreator(shipping.getStoreMember().getName());
		shippingInfo.setSmethod(shipping.getSmethod()==null? "" : shipping.getSmethod());
		shippingInfo.setErpDate("");
		shippingInfo.setErpRemark("");
		shippingInfo.setWarehouseName(shipping.getWarehouse().getName());
		shippingInfo.setAccCustomer(AccCustomerUtils.create(shipping));
		
		List<ShippingItem> shippingItems= shipping.getShippingItems();
		List<ShippingItemInfo> shippingInfoItems= new ArrayList<ShippingItemInfo>();
		for(ShippingItem shippingItem : shippingItems) {
			ShippingItemInfo shippingInfoItem= new ShippingItemInfo();
			shippingInfoItem.setPrice(shippingItem.getPrice());
			shippingInfoItem.setVonderCode(shippingItem.getProduct().getVonderCode());
			shippingInfoItem.setIntegrationId(shippingItem.getId());
			shippingInfoItem.setIntegrationId2(shippingItem.getOrderItem().getId());
			shippingInfoItem.setAssistQuantity1(shippingItem.getShippedBranchQuantity()==null? new BigDecimal("0") : shippingItem.getShippedBranchQuantity());
			shippingInfoItem.setAssistQuantity2(shippingItem.getShippedBoxQuantity()==null? new BigDecimal("0") : shippingItem.getShippedBoxQuantity());
			shippingInfoItem.setScatteredQuantity(BigDecimal.ZERO);
			shippingInfoItem.setQuantity(shippingItem.getShippedQuantity());
			shippingInfoItem.setProductLevel(shippingItem.getOrderItem().getProductLevel().getValue());
			shippingInfoItem.setWoodTypeOrColor(shippingItem.getProduct().getWoodTypeOrColor());
			shippingInfoItem.setColourNumber(shippingItem.getOrderItem().getColourNumber());
		
			shippingInfoItems.add(shippingInfoItem);
		}
		shippingInfo.setItemList(shippingInfoItems);
		list.add(shippingInfo);
		
		return list;
	}
	
	@Override
	public void syncShipping(A1_MessageTo a1) {
		
			String msgId = null;
			CompanyInfo companyInfo = companyInfoBaseService.find(9L);
			Map<String,String> headers= new  HashMap<String,String>();
			AccessToken accessToken = null;
			String msg=null;
			String h_result="2";
			String responseStr =null;
			try {
				accessToken = accessTokenService.getAccessTokenFromApi();
				char[] charArray = accessToken.getTokenType().toCharArray();
				charArray[0] -=32;//ascii值减32，实现小写英文字母转大写，非小写英文字母会出问题
				String tokenType = String.valueOf(charArray);
				
				headers.put("Content-Type", "application/json");
				headers.put("Authorization", tokenType + " " + accessToken.getAccessToken());

				LogUtils.debug("token------:"+tokenType + " " + accessToken.getAccessToken());
				LogUtils.debug("-----------------同步发货单到Link5开始------------------------");
				LogUtils.debug("url-----:"+PRUCHASE_URL+ORDER_SYNC);
				 responseStr = HttpClientUtil.post(PRUCHASE_URL+ORDER_SYNC,a1.getData(),headers,new StoreSyncErrMsgResolver());
				LogUtils.debug("response:"+responseStr);
				LogUtils.debug("-----------------同步发货单到Link5结束------------------------");
				Map<String, Object> response = JsonUtils.toObjectMap(responseStr);
				msgId = response.get("data").toString();
			} catch (Exception e) {
			   msg=e.getMessage();
			   LogUtils.debug("msq-----------:"+e.getMessage());
			   h_result="1";
			}
		
			
			A1_MessageToH intftableH = new A1_MessageToH();
			BeanUtils.copyProperties(a1, intftableH, IGNORE_PROPERTIES); // 复制
			intftableH.sethResult(h_result);
			intftableH.setField8(msgId);
			intftableH.setCompanyInfoId(companyInfo.getId());
			intftableH.setCompanytoken(companyInfo.getUniqueIdentify());
			intftableH.sethResultMsg(responseStr);
			a1_MessageToHService.save(intftableH);
			this.delete(a1.getId());
	
	}
	
	@Override
	public void syncShipped(A1_MessageTo a1) {
		
		String msgId = null;
		CompanyInfo companyInfo = companyInfoBaseService.find(9L);
		Map<String,String> headers= new  HashMap<String,String>();
		AccessToken accessToken = null;
		String msg=null;
		String h_result="2";
		String responseStr =null;
		try {
			accessToken = accessTokenService.getAccessTokenFromApi();
			char[] charArray = accessToken.getTokenType().toCharArray();
			charArray[0] -=32;//ascii值减32，实现小写英文字母转大写，非小写英文字母会出问题
			String tokenType = String.valueOf(charArray);	
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", tokenType + " " + accessToken.getAccessToken());

			LogUtils.debug("token------:"+tokenType + " " + accessToken.getAccessToken());
			LogUtils.debug("-----------------同步发货单到Link5开始------------------------");
			LogUtils.debug("url-----:"+PRUCHASE_URL+RECIEPT_SYNC);
			responseStr = HttpClientUtil.post(PRUCHASE_URL+RECIEPT_SYNC,a1.getData(),headers,new StoreSyncErrMsgResolver());
			LogUtils.debug("response:"+responseStr);
			LogUtils.debug("-----------------同步发货单到Link5结束------------------------:"+responseStr);
			Map<String, Object> response = JsonUtils.toObjectMap(responseStr);
			msgId = response.get("data").toString();
		} catch (Exception e) {
			h_result="1";
		   msg=e.getMessage();
		   LogUtils.debug("msq-----------:"+e.getMessage());
		}
	
		
		A1_MessageToH intftableH = new A1_MessageToH();
		BeanUtils.copyProperties(a1, intftableH, IGNORE_PROPERTIES); // 复制
		intftableH.sethResult(h_result);
		intftableH.setField8(msgId);
		intftableH.sethResultMsg(responseStr);
		intftableH.setCompanyInfoId(companyInfo.getId());
		intftableH.setCompanytoken(companyInfo.getUniqueIdentify());
		a1_MessageToHService.save(intftableH);
		this.delete(a1.getId());
	}
	
	@Override
	public void findShippingInfoResult(A1_MessageToH a1_MessageTo) {
		
		
		HashMap<String, Object> result = null;
		
		Map<String,String> headers= new  HashMap<String,String>();
		AccessToken accessToken = null;
		accessToken = accessTokenService.getAccessTokenFromApi();
		char[] charArray = accessToken.getTokenType().toCharArray();
		charArray[0] -=32;//ascii值减32，实现小写英文字母转大写，非小写英文字母会出问题
		String tokenType = String.valueOf(charArray);
		
		headers.put("Content-Type", "application/json");
		headers.put("Authorization", tokenType + " " + accessToken.getAccessToken());
		
		String resultStr = HttpClientUtil.get(SYSTEM_URL+GET_SEND_TO_MSG+a1_MessageTo.getField8(),headers,new StoreSyncErrMsgResolver());
		LogUtils.debug("同步结果查询*********："+a1_MessageTo.getField8());
		result = JsonUtils.toObjectMap(resultStr);
		LogUtils.debug("同步结果查询："+resultStr);
		Map<String,Object> map = (Map)result.get("data");
		
		a1_MessageTo.sethResultMsg(resultStr);
		a1_MessageTo.sethResult("success".equals(map.get("code"))?"2":"1");
		a1_MessageTo.setField2("1");
		a1_MessageToHService.update(a1_MessageTo);
		
//		 if ("nature".equals(a1_MessageTo.getCompanytoken())) {
//			 A1_MessageTo a1= new A1_MessageTo();
//       	
//     		
//     		a1.setData(a1_MessageTo.getData());
//     		a1.setType(a1_MessageTo.getType());
//     		a1.setField1(a1_MessageTo.getField1());
//     		a1.setCompanyInfoId(a1_MessageTo.getCompanyInfoId());
//     		a1.setCompanytoken(a1_MessageTo.getCompanytoken());
//     		a1.setField2("1");
//     		a1_MessageToService.save(a1);
//       	  
//         }  
		
		
	}

	@Override
	public void syncB2bReturns(A1_MessageTo a1) {
		
		String msgId = null;
		CompanyInfo companyInfo = companyInfoBaseService.find(9L);
		Map<String,String> headers= new  HashMap<String,String>();
		AccessToken accessToken = null;
		String msg=null;
		String h_result="2";
		String responseStr =null;
		try {
			accessToken = accessTokenService.getAccessTokenFromApi();
			char[] charArray = accessToken.getTokenType().toCharArray();
			charArray[0] -=32;//ascii值减32，实现小写英文字母转大写，非小写英文字母会出问题
			String tokenType = String.valueOf(charArray);
			
			headers.put("Content-Type", "application/json");
			headers.put("Authorization", tokenType + " " + accessToken.getAccessToken());

			LogUtils.debug("token------:"+tokenType + " " + accessToken.getAccessToken());
			LogUtils.debug("-----------------同步退货申请单到Link5开始------------------------");
			LogUtils.debug("url-----:"+PRUCHASE_URL+B2BRETURN_SYNC);
			 responseStr = HttpClientUtil.post(PRUCHASE_URL+B2BRETURN_SYNC,a1.getData(),headers,new StoreSyncErrMsgResolver());
			LogUtils.debug("response:"+responseStr);
			LogUtils.debug("-----------------同步退货申请单到Link5结束------------------------");
			Map<String, Object> response = JsonUtils.toObjectMap(responseStr);
			msgId = response.get("data").toString();
		} catch (Exception e) {
		   msg=e.getMessage();
		   LogUtils.debug("msq-----------:"+e.getMessage());
		   h_result="1";
		}
		
		A1_MessageToH intftableH = new A1_MessageToH();
		BeanUtils.copyProperties(a1, intftableH, IGNORE_PROPERTIES); // 复制
		intftableH.sethResult(h_result);
		intftableH.setField8(msgId);
		intftableH.setCompanyInfoId(companyInfo.getId());
		intftableH.setCompanytoken(companyInfo.getUniqueIdentify());
		intftableH.sethResultMsg(responseStr);
		a1_MessageToHService.save(intftableH);
		this.delete(a1.getId());

	}

	public void synchronizationOrder(Long[] ids) {

        for(int i=0;i<ids.length;i++) {
        	Long id = ids[i];
        	Order order = orderService.find(id);
        	if(!order.getOrderStatus().equals(OrderStatus.audited) && !order.getOrderStatus().equals(OrderStatus.completed)) {
        		ExceptionUtil.throwServiceException("只有状态为已审核或者已完成才可推送至LINK5");
        	}
        	
        	if(!(order.getStore().getIsToLink5()==null ? false : order.getStore().getIsToLink5())) {
        		ExceptionUtil.throwServiceException("该客户暂无LINK5权限");
        	}
        	saveOrderInfoTo(order,1);
        }
	}
	
	public void synchronizationShipping(Long[] ids) {

		for(int i=0;i<ids.length;i++) {
        	Long id = ids[i];
        	Shipping shipping = shippingService.find(id);
        	if(shipping.getStatus()!=3 && shipping.getStatus()!=4) {
        		ExceptionUtil.throwServiceException("只有状态为部分发货或者完全发货才可推送至LINK5");
        	}
        	
        	if(!(shipping.getStore().getIsToLink5()==null ? false : shipping.getStore().getIsToLink5())) {
        		ExceptionUtil.throwServiceException("该客户暂无LINK5权限");
        	}
        	saveShippingInfoTo(shipping);
        }
	}
	
	@Override
	public void synchronizationB2bReturns(Long[] ids) {

        for(int i=0;i<ids.length;i++) {
        	
        	Long id = ids[i];
        	
        	B2bReturns b2bReturns = b2bReturnsService.find(id);
        	
        	if(b2bReturns.getStatus() == 0 || b2bReturns.getStatus() == 6) {
        		ExceptionUtil.throwServiceException("只有状态为已审核、部分退货、完全退货或者已关闭的退货单才可推送至LINK5");
        	}
        	
        	if(!(b2bReturns.getStore().getIsToLink5()==null ? false : b2bReturns.getStore().getIsToLink5())) {
        		ExceptionUtil.throwServiceException("该客户暂无LINK5权限");
        	}
        	saveB2bReturnsInfoTo(b2bReturns,1);
        }
	}
}
