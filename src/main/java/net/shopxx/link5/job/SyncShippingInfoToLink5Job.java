package net.shopxx.link5.job;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

@Component("syncShippingInfoToLink5Job")
@Lazy(false)
public class SyncShippingInfoToLink5Job {
	
	private static final String SYNC_SHIPPING_LOCK_KEY = "syncShippingInfoToLink5Lock_#";
	
	@Scheduled(cron = "0 0/2 * * * ?")
	public void syncShippingInfoToLink5Job() {
		try {
			//StringRedisTemplate redisTemplate = SpringUtils.getBean("stringRedisTemplate", StringRedisTemplate.class);
			LogUtils.info("同步发货单数据到LINK5接口执行");
			final ShippingInfoToLink5Service shippingInfoToLink5Service = SpringUtils.getBean("shippingInfoToLink5ServiceImpl",
					ShippingInfoToLink5Service.class);
			final IntfOrderMessageToService a1_MessageToService = SpringUtils.getBean("intfOrderMessageToServiceImpl",
					IntfOrderMessageToService.class);
			String sql = "select * from a1_messageto where type = 35 ";
			List<A1_MessageTo> a1_MessageTos = a1_MessageToService.getDaoCenter()
					.getNativeDao()
					.findListManaged(sql, null, 100, A1_MessageTo.class);
			//创建线程池
			/*ExecutorService pool = Executors.newFixedThreadPool(20);
			final Semaphore semaphore = new Semaphore(20);*/
			LogUtils.info("同步发货单数据到LINK5接口执行111111111111");
			for (final A1_MessageTo a1_MessageTo : a1_MessageTos) {
				try {
					shippingInfoToLink5Service.syncShipping(a1_MessageTo);
				} catch (Exception e){
					LogUtils.error("35-1同步发货单数据", e);
				}
			}
		} catch (Exception e){
			LogUtils.error("35-2同步发货单数据", e);
		}
	}
	

}
