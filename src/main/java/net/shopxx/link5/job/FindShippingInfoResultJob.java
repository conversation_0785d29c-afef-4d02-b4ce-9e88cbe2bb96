package net.shopxx.link5.job;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;


@Component("findShippingInfoResultJob")
@Lazy(false)
public class FindShippingInfoResultJob {
	
	@Scheduled(cron = "0 0/2 * * * ?")
	public void findShippingInfoResultJob() {

		final A1_MessageToHService a1_MessageToHService = SpringUtils.getBean("a1_MessageToHServiceImpl",
				A1_MessageToHService.class);
		
		final ShippingInfoToLink5Service shippingInfoToLink5Service = SpringUtils.getBean("shippingInfoToLink5ServiceImpl",
				ShippingInfoToLink5Service.class);
		
		String sql = "select * from a1_messagetoh where type in (35,36,37) and field2 is null and field8 is not null";
		List<A1_MessageToH> a1_MessageTos = a1_MessageToHService.getDaoCenter().getNativeDao().findListManaged(sql, null, 100, A1_MessageToH.class);
		for (A1_MessageToH a1_MessageTo : a1_MessageTos) {
			try {
				LogUtils.info("查询LINK5订单、发货单、退货单数据同步结果");
				shippingInfoToLink5Service.findShippingInfoResult(a1_MessageTo);
			}catch (Exception e) {
				LogUtils.error("查询LINK5订单、发货单、退货单数据同步结果",e);
			}
		}
	}

}
