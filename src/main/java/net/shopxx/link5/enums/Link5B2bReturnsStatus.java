package net.shopxx.link5.enums;

/**
 * Link5退货单状态信息
 * <AUTHOR>
 *
 */

public enum Link5B2bReturnsStatus {
	SAVED(1, "已保存"),
	CHECKED(2, "已审核"),
	CLOSED(3, "已关闭"),
	COMPLETED(4, "已完成"),
	CHECKING(5, "审核中"),
	CANCELED(6, "已作废"),
	DELETED(7, "已删除"),
	ENABLED(8, "有效"),
	DISABLED(9, "失效"),
	DEPOSIT(86, "寄存");
	
	Integer state;
	
	String desc;

	Link5B2bReturnsStatus(Integer state , String desc) {
		this.state = state;
		this.desc = desc;
	}

	public Integer getState() {
		return state;
	}

	public String getDesc() {
		return desc;
	}	
}
