package net.shopxx.link5.util;


import net.shopxx.base.core.util.LogUtils;
import net.shopxx.util.base.ErrMsg;
import net.shopxx.util.base.ErrMsgResolver;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

public class StoreSyncErrMsgResolver implements ErrMsgResolver{
	
	private ErrMsg errMsg;
	
	@Override
	public ErrMsg getErrorMessage(HttpResponse response) {
		int statusCode = response.getStatusLine().getStatusCode();
		String msg = "";
		switch (statusCode) {
		case 404:
			msg = "404 Not Found";
			break;
		case 500:
			try {
				msg = EntityUtils.toString(response.getEntity(), "utf-8");
			} catch (Exception e) {
				LogUtils.error(e);
				msg = e.getMessage();
			}
			break;
		default:
			msg = statusCode+"";
			break;
		}
		
		
		ErrMsg errMsg = new ErrMsg(statusCode, msg);
		this.setErrMsg(errMsg);
		return errMsg; 
	}

	public ErrMsg getErrMsg() {
		return errMsg;
	}

	private void setErrMsg(ErrMsg errMsg) {
		this.errMsg = errMsg;
	}
	
	

}
