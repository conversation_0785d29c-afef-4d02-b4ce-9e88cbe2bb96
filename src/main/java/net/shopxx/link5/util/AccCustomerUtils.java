package net.shopxx.link5.util;

import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.link5.entity.AccCustomer;
import net.shopxx.link5.entity.ConvertibleToAccCustomer;

public class AccCustomerUtils {
	
	private AccCustomerUtils() {}
	
	public static AccCustomer create(ConvertibleToAccCustomer convertible) {
		Sbu sbu = convertible.getSbu();
		if(sbu==null) {
			ExceptionUtil.throwIllegalDataException("构造Link5供应商信息失败：sbu为空");
		}
		if(sbu.getUndefined10()==null) {
			ExceptionUtil.throwIllegalDataException("构造Link5供应商信息失败：sbu编码（预留字段10）为空");
		}
		if(sbu.getName()==null) {
			ExceptionUtil.throwIllegalDataException("构造Link5供应商信息失败：sbu名称为空");
		}
		String sbuName = sbu.getName();
		String sbuCode = sbu.getUndefined10();
		
		Organization organization = convertible.getOrganization();
		if(organization==null) {
			ExceptionUtil.throwIllegalDataException("构造Link5供应商信息失败：organization为空");
		}
		if(organization.getCode()==null) {
			ExceptionUtil.throwIllegalDataException("构造Link5供应商信息失败：organization编码为空");
		}
		if(organization.getName()==null) {
			ExceptionUtil.throwIllegalDataException("构造Link5供应商信息失败：organization名称为空");
		}
		String organizationName = organization.getName();
		String organizationCode = organization.getCode();
		
		AccCustomer accCustomer = new AccCustomer();
		accCustomer.setCode("AM-"+sbuCode+"-"+organizationCode);
		accCustomer.setName(sbuName+"-"+organizationName);
		return accCustomer;
	}

}
