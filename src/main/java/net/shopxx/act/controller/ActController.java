package net.shopxx.act.controller;

import net.shopxx.act.entity.WfVo;
import net.shopxx.act.service.ActService;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("actController")
@RequestMapping("/new/act/wf")
public class ActController extends BaseController{

	@Autowired
	private ActWfService actWfService;
	@Autowired
	private ActService actService;
	
	@RequestMapping(value="/list", method=RequestMethod.GET)
	public String list(ModelMap model) {
		List<Map<String, Object>> countList = actWfService.countList(
				1, 
				WebUtils.getCurrentStoreMemberId(), 
				WebUtils.getCurrentCompanyInfoId(), 
				null);
		model.addAttribute("configs1", countList);
		return "/act/wf/list";
	}
	
	@RequestMapping(value="/count", method=RequestMethod.GET)
	@ResponseBody
	public ResultMsg count(Integer flag) {
		List<Map<String, Object>> countList = actWfService.countList(
				flag, 
				WebUtils.getCurrentStoreMemberId(), 
				WebUtils.getCurrentCompanyInfoId(), 
				null);
		return success().addObjX(countList);
	}
	
	@RequestMapping(value="/list_data", method=RequestMethod.POST)
	@ResponseBody
	public ResultMsg listData(Integer flag,WfVo vo,Pageable pageable) {
		Page<Map<String, Object>> page = actService.findPage(flag, vo, pageable);
		return success(JsonUtils.toJson(page));
	}


	@RequestMapping(value="/selectRejectNode", method=RequestMethod.GET)
	public String selectRejectNode( Long wfid,@RequestParam(value = "userid", required = false) String userid,
							   ModelMap model) {


		return "/act/wf/selectRejectNode";
	}

	@RequestMapping(value="/selectRejectNodeData", method=RequestMethod.POST)
	@ResponseBody
	public ResultMsg listData( Long wfid,@RequestParam(value = "userid", required = false) String userid) {

		List<Map<String,Object>> list = new ArrayList<Map<String, Object>>();
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("activitiName", "节点1");
		map.put("assigneeName", "审核人1");
		map.put("endTime", "日期1");
		list.add(map);
		map.put("activitiName", "节点2");
		map.put("assigneeName", "审核人2");
		map.put("endTime", "日期2");
		list.add(map);

		map.put("activitiName", "节点3");
		map.put("assigneeName", "审核人3");
		map.put("endTime", "日期3");
		list.add(map);

		return success(JsonUtils.toJson(list));
	}


	
	
}
