package net.shopxx.act.service;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.act.entity.WfVo;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.StoreMember;

import java.util.List;
import java.util.Map;

public interface ActService extends BaseService<Object>{
	
	/**
	 * 查找流程明细 
	 * @param wf 流程实例
	 * @param nodeNaem 节点名称
	 * @return  0审批意见 1审批人  2审批时间
	 */
	String[] getActItem(ActWf wf, String nodeName);

	/**
	 * 根据节点任务名查找流程节点任务
	 * @param entity 流程实体
	 * @param nodeName 节点任务名
	 * */
	List<Map<String, Object>> findActHiTaskinstByNodeName(ActWfBillEntity entity, String nodeName);

	/**
	 * 根据节点任务名查找流程节点任务审核人
	 * @param entity 流程实体
	 * @param nodeName 节点任务名
	 * */
	StoreMember findActWfAssigneeByNodeName(ActWfBillEntity entity, String nodeName);

	/**
	 * 根据objTypeId查询流程、流程模版信息
	 * @param objTypeId 对象类型id
	 * @return 
	 * [
	 * 	{
	 * 		obj_type_id:对象类型id,
	 * 		obj_type_name:对象类型名称,
	 * 		model_id:流程模版id,
	 * 		model_key:流程模版关键字,
	 * 		model_name:流程模版名称,
	 * 	},
	 * 	...
	 * ]
	 * */
	List<Map<String, Object>> getActWfModelInfoByObjTypeId(long objTypeId);

	Page<Map<String, Object>> findPage(Integer flag, WfVo vo, Pageable pageable);
	
}
