package net.shopxx.act.service.impl;

import net.shopxx.act.dao.ActDao;
import net.shopxx.act.entity.ActWf;
import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.act.entity.WfVo;
import net.shopxx.act.service.ActService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("actServiceImpl")
public class ActServiceImpl extends BaseServiceImpl<Object> implements ActService {

	@Resource(name = "actDao")
	private ActDao actDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;



	public String[] getActItem(ActWf wf,String nodeName){
		List<Map<String, Object>> actItem = actDao.findActItem(wf.getProcInstId());
		if(actItem!=null){
			for(Map<String, Object> mp : actItem){

				if(mp.get("ACT_NAME_")!=null&&nodeName.equals(mp.get("ACT_NAME_").toString())){
					String storeMemberName = storeMemberService.find(Long.parseLong(mp.get("ASSIGNEE_").toString())).getName();
					String msg = mp.get("MESSAGE_")==null?"":mp.get("MESSAGE_").toString();
					String data = mp.get("END_TIME_")==null?"":mp.get("END_TIME_").toString().substring(0, 10);;
					return new String[]{msg,storeMemberName,data};
				}
			}
		}
		return null;
	}

	@Override
	public List<Map<String, Object>> findActHiTaskinstByNodeName(ActWfBillEntity entity,String nodeName){
		if(entity==null||entity.getWfId()==null||nodeName==null) {
			return null;
		}
		Long wfId = entity.getWfId();
		return actDao.findActHiTaskinstByNodeName(wfId, nodeName);
	}

	@Override
	public StoreMember findActWfAssigneeByNodeName(ActWfBillEntity entity,String nodeName){
		List<Map<String, Object>> info = findActHiTaskinstByNodeName(entity,nodeName);
		if(info.size()==0) {
			return null;
		}
		Object assignee = info.get(0).get("ASSIGNEE_");
		if(assignee==null) {
			return null;
		}
		Long storeMemberId = Long.valueOf(assignee.toString());
		return storeMemberService.find(storeMemberId);
	}

	@Override
	public List<Map<String, Object>> getActWfModelInfoByObjTypeId(long objTypeId) {
		return actDao.getActWfModelInfoByObjTypeId(objTypeId);
	}
	
	@Override
	public Page<Map<String, Object>> findPage(Integer flag,WfVo vo, Pageable pageable){
		Long currentStoreMemberId = WebUtils.getCurrentStoreMemberId();
		Page<Map<String,Object>> page = null;	
		if(flag==0) {
			//我启动的
			page = actDao.findPageForWfsStartedByStoreMember(currentStoreMemberId,vo ,pageable);
		} else if(flag==1) {
			//当前处理
			page = actDao.findPageForWfsCurrentlyProcessed(currentStoreMemberId, vo, pageable);
		} else if(flag==2) {
			//已处理未结束
			page = actDao.findPageForWfsHandledAndNotEnded(currentStoreMemberId, vo, pageable);
		} else if(flag==3) {
			//已结束
			page = actDao.findPageForWfsFinished(currentStoreMemberId, vo, pageable);
		} else {
			return new Page<Map<String,Object>>();
		}
		
		//查询节点当前处理人信息
		List<Map<String, Object>> content = page.getContent();
		List<Long> wfIds = new ArrayList<Long>();
		for (Map<String, Object> map : content) {
			Long wfId = Long.valueOf(map.get("wf_id").toString());
			wfIds.add(wfId);
		}
		List<Map<String, Object>> usersOfWf = actDao.findCurrentUserByWfIds(wfIds);
		Map<Long,Map<String,Object>> index = new HashMap<Long, Map<String,Object>>();
		for (Map<String, Object> map : usersOfWf) {
			Long wfId = Long.valueOf(map.get("wf_id").toString());
			index.put(wfId, map);
		}
		LogUtils.debug(JsonUtils.toJson(index));
		//往page查询结果添加节点当前处理人信息
		for (Map<String, Object> map : content) {
			Long wfId = Long.valueOf(map.get("wf_id").toString());
			Map<String, Object> map2 = index.get(wfId);
			if(map2!=null) {
				String currentName = map2.get("current_name")==null?null:map2.get("current_name").toString();
				map.put("current_name", currentName);
			}
		}
		return page ;
	}
	

}
