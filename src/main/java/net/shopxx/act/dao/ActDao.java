package net.shopxx.act.dao;

import net.shopxx.act.entity.WfVo;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("actDao")
public class ActDao extends DaoCenter {

	public List<Map<String, Object>> findActItem(String procInstId) {
		if(procInstId == null){
			return null;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT aha.ACT_NAME_,ahc.MESSAGE_,aha.END_TIME_,aha.ASSIGNEE_ FROM ACT_HI_ACTINST aha "
				+ " LEFT JOIN ACT_HI_COMMENT ahc ON ahc.TASK_ID_ = aha.TASK_ID_ WHERE 1=1 ");
		sql.append(" AND aha.TASK_ID_ is not null ");
		sql.append(" AND aha.PROC_INST_ID_ = ? ");
		return getNativeDao().findListMap(sql.toString(), new Object[] { procInstId }, 0);

	}
	
	/**
	 * @param wfId 流程id
	 * @param nodeName 节点名称
	 * @return 历史任务节点信息{ID_:主键ID, PROC_DEF_ID_:流程定义ID, TASK_DEF_KEY_:节点定义ID, 
	 * PROC_INST_ID_:流程实例ID, EXECUTION_ID_:执行实例ID, NAME_:节点名称, 
	 * PARENT_TASK_ID_:父节点实例ID, DESCRIPTION_:描述, OWNER_:签收人（默认为空，只有在委托时才有值）, 
	 * ASSIGNEE_:签收人或被委托, START_TIME_:开始时间, CLAIM_TIME_:提醒时间, END_TIME_:结束时间, 
	 * DURATION_:耗时, DELETE_REASON_:删除原因, PRIORITY_:优先级别, DUE_DATE_:	过期时间, FORM_KEY_: 节点定义的formkey }
	 * */
	public List<Map<String, Object>> findActHiTaskinstByNodeName(Long wfId, String nodeName) {
		StringBuilder sql = new StringBuilder("SELECT * FROM ACT_HI_TASKINST WHERE PROC_INST_ID_ = (SELECT proc_inst_id FROM act_wf WHERE id = ?) AND NAME_ LIKE ?  ORDER BY ACT_HI_TASKINST.ID_ desc");
		return getNativeDao().findListMap(sql.toString(), new Object[] { wfId , nodeName }, 0);
		
	}
	
	/**
	 * @param wfId 流程id
	 * @return 历史任务节点信息{ID_:主键ID, PROC_DEF_ID_:流程定义ID, TASK_DEF_KEY_:节点定义ID, 
	 * PROC_INST_ID_:流程实例ID, EXECUTION_ID_:执行实例ID, NAME_:节点名称, 
	 * PARENT_TASK_ID_:父节点实例ID, DESCRIPTION_:描述, OWNER_:签收人（默认为空，只有在委托时才有值）, 
	 * ASSIGNEE_:签收人或被委托, START_TIME_:开始时间, CLAIM_TIME_:提醒时间, END_TIME_:结束时间, 
	 * DURATION_:耗时, DELETE_REASON_:删除原因, PRIORITY_:优先级别, DUE_DATE_:	过期时间, FORM_KEY_: 节点定义的formkey }
	 * */
	public List<Map<String, Object>> findActHiTaskinstList(Long wfId) {
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder("SELECT * FROM ACT_HI_TASKINST WHERE 1=1");
		if(wfId!=null) {
			sql.append(" AND PROC_INST_ID_ = (SELECT proc_inst_id FROM act_wf WHERE id = ?)");
			params.add(wfId);
		}
		return getNativeDao().findListMap(sql.toString(), objs(params),0);
	}
	
	
	public Map<String, Object> findActHiActinstByNodeName(Long wfId, String nodeName) {
		StringBuilder sql = new StringBuilder("SELECT * FROM ACT_HI_ACTINST WHERE PROC_INST_ID_ = (SELECT proc_inst_id FROM act_wf WHERE id = ?) AND ACT_NAME_ LIKE ?");
		return getNativeDao().findSingleMap(sql.toString(), new Object[] {wfId,nodeName});
	}
	
	/**
	 * @param actType节点类型：0:startEvent 1:userTask 2:exclusiveGateway 3:endEvent
	 * */
	public List<Map<String, Object>> findActHiActinstList(Long wfId, Integer actType) {
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder("SELECT * FROM ACT_HI_ACTINST WHERE 1=1");
		if(wfId!=null) {
			sql.append(" AND PROC_INST_ID_ = (SELECT proc_inst_id FROM act_wf WHERE id = ?)");
			params.add(wfId);
		}
		if(actType!=null) {
			sql.append(" AND ACT_TYPE_ LIKE ?");
			if(actType==0) {
				params.add("startEvent");
			}
			if(actType==1) {
				params.add("userTask");
			}
			if(actType==2) {
				params.add("exclusiveGateway");
			}
			if(actType==3) {
				params.add("endEvent");
			} else {
				params.add("");
			}
		}	
		return getNativeDao().findListMap(sql.toString(), objs(params),0);
	}
	
	/**
	 * @param objTypeId 对象类型id
	 * @return 
	 * [
	 * 	{
	 * 		obj_type_id:对象类型id,
	 * 		obj_type_name:对象类型名称,
	 * 		model_id:流程模版id,
	 * 		model_key:流程模版关键字,
	 * 		model_name:流程模版名称,
	 * 	},
	 * 	...
	 * ]
	 * */
	public List<Map<String,Object>> getActWfModelInfoByObjTypeId(Long objTypeId) {
		StringBuilder sql = new StringBuilder("SELECT oc.obj_type_id, oc.obj_type_name, arm.ID_ model_id, arm.KEY_ model_key, arm.NAME_ model_name FROM act_wf_obj_config oc");
		sql.append(" JOIN act_wf_obj_config_line ocl ON oc.id = ocl.obj_config_id")
			.append(" JOIN ACT_RE_MODEL arm ON ocl.model_id = arm.ID_")
			.append(" WHERE oc.company_info_id = ? AND oc.obj_type_id = ?");
		return getNativeDao().findListMap(sql.toString(), new Object[] {WebUtils.getCurrentCompanyInfoId(),objTypeId}, 0);
	}
	
	private Object[] objs(List<Object> params) {
		Object[] objs = new Object[params.size()];
		for (int i = 0; i < params.size(); i++) {
			objs[i] = params.get(i);
		}
		return objs;
	}
	
	
	/**
	 * 查询用户当前处理流程
	 * */
	public Page<Map<String, Object>> findPageForWfsCurrentlyProcessed(Long storeMemberId, WfVo vo,Pageable pageable){
		Long currentCompanyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder("SELECT wf.id wf_id,wf.obj_id,wf.obj_type,wf.model_id,wf.proc_def_id,wf.proc_inst_id,wf.stat,cf.url,wf.last_time,ifnull( proc.NAME_, m.NAME_ ) wf_name,cf.obj_type_name,m.NAME_ model_name,wf.create_date,sm.username start_username,sm.NAME start_name ");
		sql.append(" FROM act_wf wf")
			.append(" JOIN ACT_RE_MODEL m ON wf.model_id = m.ID_")
			.append(" JOIN ACT_HI_PROCINST proc ON wf.proc_inst_id = proc.PROC_INST_ID_")
			.append(" JOIN act_wf_obj_config cf ON wf.obj_type = cf.obj_type_id")
			.append(" JOIN xx_store_member sm ON proc.START_USER_ID_ = sm.id ")
			.append(" WHERE wf.company_info_id = ? ")
			.append(" AND cf.company_info_id = ?");
		params.add(currentCompanyInfoId);
		params.add(currentCompanyInfoId);
		addParams(sql, vo, params);
		sql.append(" AND wf.stat IN ( 1, 3 ) ")
			.append(" AND (")
			.append(" EXISTS (SELECT 1 FROM ACT_RU_TASK rut LEFT JOIN ACT_RU_IDENTITYLINK ruid ON ruid.TASK_ID_=rut.ID_ WHERE rut.PROC_INST_ID_=proc.PROC_INST_ID_ AND (ruid.USER_ID_= ? OR rut.ASSIGNEE_= ? ))")
			.append(" OR EXISTS (")
			.append(" SELECT 1 FROM ACT_RU_TASK rut")
			.append(" JOIN ACT_RU_IDENTITYLINK ruid ON ruid.TASK_ID_ = rut.ID_")
			.append(" JOIN xx_store_member_sale_org_post smsop ON ruid.GROUP_ID_ = smsop.post")
			.append(" JOIN xx_store_member sm ON smsop.store_member = sm.id")
			.append(" WHERE rut.PROC_INST_ID_ = proc.PROC_INST_ID_ ")
			.append(" AND sm.is_enabled IS TRUE ")
			.append(" AND (ifnull( wf.sale_org_ids, '' ) = '' OR smsop.sale_org IN ( SELECT sale_org_id FROM act_wf_sale_org WHERE wf_id = wf.id )) ")
			.append(" AND (wf.store_id IS NULL OR (wf.store_id IS NOT NULL AND EXISTS (SELECT 1 FROM xx_store_member WHERE member = sm.member AND company_info_id = sm.company_info_id AND (sm.is_salesman = 0 OR ( sm.is_salesman = 1 AND store = wf.store_id )))))")
			.append(" AND sm.id = ? ")
			.append(" ))");
		params.add(storeMemberId);
		params.add(storeMemberId);
		params.add(storeMemberId);
		sql.append(" ORDER BY wf.modify_date DESC");
		//System.out.println("wf dao ==" + sql.toString());
		return getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);
	}
	
	/**
	 * 查询用户启动的流程
	 * */
	public Page<Map<String, Object>> findPageForWfsStartedByStoreMember(Long storeMemberId, WfVo vo,Pageable pageable){
		Long currentCompanyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder("SELECT wf.id wf_id,wf.obj_id,wf.obj_type,wf.model_id,wf.proc_def_id,wf.proc_inst_id,wf.stat,cf.url,wf.last_time,ifnull( proc.NAME_, m.NAME_ ) wf_name,cf.obj_type_name,m.NAME_ model_name,wf.create_date,sm.username start_username,sm.NAME start_name ");
		sql.append(" FROM act_wf wf")
			.append(" JOIN ACT_RE_MODEL m ON wf.model_id = m.ID_")
			.append(" JOIN ACT_HI_PROCINST proc ON wf.proc_inst_id = proc.PROC_INST_ID_")
			.append(" JOIN act_wf_obj_config cf ON wf.obj_type = cf.obj_type_id")
			.append(" JOIN xx_store_member sm ON proc.START_USER_ID_ = sm.id ")
			.append(" WHERE wf.company_info_id = ? ")
			.append(" AND cf.company_info_id = ?");
		params.add(currentCompanyInfoId);
		params.add(currentCompanyInfoId);
		addParams(sql, vo, params);
		sql.append(" AND wf.stat IN ( 1, 2, 3 ) ")
			.append(" AND proc.START_USER_ID_ = ?");
		params.add(storeMemberId);
		sql.append(" ORDER BY wf.modify_date DESC");
		return getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);
	}
	
	/**
	 * 查询已处理未结束的流程
	 * */
	public Page<Map<String, Object>> findPageForWfsHandledAndNotEnded(Long storeMemberId, WfVo vo,Pageable pageable){
		Long currentCompanyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder("SELECT wf.id wf_id,wf.obj_id,wf.obj_type,wf.model_id,wf.proc_def_id,wf.proc_inst_id,wf.stat,cf.url,wf.last_time,ifnull( proc.NAME_, m.NAME_ ) wf_name,cf.obj_type_name,m.NAME_ model_name,wf.create_date,sm.username start_username,sm.NAME start_name ");
		sql.append(" FROM act_wf wf")
			.append(" JOIN ACT_RE_MODEL m ON wf.model_id = m.ID_")
			.append(" JOIN ACT_HI_PROCINST proc ON wf.proc_inst_id = proc.PROC_INST_ID_")
			.append(" JOIN act_wf_obj_config cf ON wf.obj_type = cf.obj_type_id")
			.append(" JOIN xx_store_member sm ON proc.START_USER_ID_ = sm.id ")
			.append(" WHERE wf.company_info_id = ? ")
			.append(" AND cf.company_info_id = ?");
		params.add(currentCompanyInfoId);
		params.add(currentCompanyInfoId);
		addParams(sql, vo, params);
		sql.append(" AND wf.stat = 1 ")
			.append(" AND EXISTS (")
			.append(" SELECT 1 FROM ACT_HI_IDENTITYLINK hiid")
			.append(" JOIN ACT_HI_TASKINST hit ON hiid.TASK_ID_ = hit.ID_")
			.append(" JOIN ACT_RU_TASK rut ON hit.ID_ <> rut.ID_")
			.append(" WHERE hit.PROC_INST_ID_ = proc.PROC_INST_ID_ ")
			.append(" AND ( hiid.USER_ID_ = ?  OR hit.ASSIGNEE_ = ?)")
			.append(" )");
		params.add(storeMemberId);
		params.add(storeMemberId);
		sql.append(" ORDER BY wf.modify_date DESC");
		return getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);
	}
	
	/**
	 * 查询已结束的流程
	 * */
	public Page<Map<String, Object>> findPageForWfsFinished(Long storeMemberId, WfVo vo,Pageable pageable){
		Long currentCompanyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder("SELECT wf.id wf_id,wf.obj_id,wf.obj_type,wf.model_id,wf.proc_def_id,wf.proc_inst_id,wf.stat,cf.url,wf.last_time,ifnull( proc.NAME_, m.NAME_ ) wf_name,cf.obj_type_name,m.NAME_ model_name,wf.create_date,sm.username start_username,sm.NAME start_name ");
		sql.append(" FROM act_wf wf")
			.append(" JOIN ACT_RE_MODEL m ON wf.model_id = m.ID_")
			.append(" JOIN ACT_HI_PROCINST proc ON wf.proc_inst_id = proc.PROC_INST_ID_")
			.append(" JOIN act_wf_obj_config cf ON wf.obj_type = cf.obj_type_id")
			.append(" JOIN xx_store_member sm ON proc.START_USER_ID_ = sm.id ")
			.append(" WHERE wf.company_info_id = ? ")
			.append(" AND cf.company_info_id = ?");
		params.add(currentCompanyInfoId);
		params.add(currentCompanyInfoId);
		addParams(sql,vo,params);
		sql.append(" AND wf.stat = 2 ")
			.append(" AND EXISTS (")
			.append(" SELECT 1 FROM ACT_HI_IDENTITYLINK hiid")
			.append(" JOIN ACT_HI_TASKINST hit ON hiid.TASK_ID_ = hit.ID_")
			.append(" WHERE hit.PROC_INST_ID_ = proc.PROC_INST_ID_ ")
			.append(" AND ( hiid.USER_ID_ = ?  OR hit.ASSIGNEE_ = ?)")
			.append(" )");
		params.add(storeMemberId);
		params.add(storeMemberId);
		sql.append(" ORDER BY wf.modify_date DESC");
		return getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);
	}
	

	public List<Map<String,Object>> findCurrentUserByWfIds(List<Long> wfIds){
		StringBuilder idStr = new StringBuilder("0");
		for (int i = 0; i < wfIds.size(); i++) {
			Long id = wfIds.get(i);
			idStr.append(","+id);
		}
		StringBuilder sql = new StringBuilder("SELECT wf_id, modify_date, group_concat( DISTINCT username ) current_username, group_concat( DISTINCT NAME ) current_name ");
		sql.append(" FROM (")
			//子表
			.append(" SELECT wf.id wf_id, wf.modify_date, sm.username, sm.name")
			.append(" FROM act_wf wf")
			.append(" JOIN ACT_RU_TASK rut ON  rut.PROC_INST_ID_ = wf.proc_inst_id")
			.append(" JOIN ACT_RU_IDENTITYLINK ruid ON ruid.TASK_ID_ = rut.ID_")
			.append(" JOIN xx_store_member sm ON ( sm.id = ruid.USER_ID_ OR sm.id = rut.ASSIGNEE_ )")
			.append(" WHERE wf.id IN ( "+idStr+" )")
			.append(" AND sm.is_enabled IS TRUE")
			
			.append(" UNION ALL")
			
			//子表
			.append(" SELECT wf.id wf_id, wf.modify_date, sm.username, sm.name")
			.append(" FROM act_wf wf ")
			.append(" JOIN act_wf_sale_org awso ON awso.wf_id = wf.id")
			.append(" JOIN ACT_RU_TASK rut ON rut.PROC_INST_ID_ = wf.proc_inst_id")
			.append(" JOIN ACT_RU_IDENTITYLINK ruid ON ruid.TASK_ID_ = rut.ID_")
			.append(" JOIN xx_store_member_sale_org_post post ON post.post = ruid.GROUP_ID_ AND awso.sale_org_id = post.sale_org ")
			.append(" JOIN xx_store_member sm ON sm.id = post.store_member ")
			.append(" WHERE wf.id IN ( "+idStr+" )")
			.append(" AND sm.is_enabled IS TRUE")
			.append(" AND (wf.store_id IS NULL OR (wf.store_id IS NOT NULL AND EXISTS (SELECT 1 FROM xx_store_member WHERE member = sm.member AND company_info_id = sm.company_info_id AND (sm.is_salesman = 0 OR ( sm.is_salesman = 1 AND store = wf.store_id )))))")
			
			.append(" ) a GROUP BY a.wf_id");
		return getNativeDao().findListMap(sql.toString(), null, 0);
	}
	

	private void addParams(StringBuilder sql, WfVo vo, List<Object> params) {
		if(vo.getObjType()!=null) {
			sql.append(" AND wf.obj_type = ?");
			params.add(vo.getObjType());
		}
		if(vo.getWfName()!=null) {
			sql.append(" AND ifnull( proc.NAME_, m.NAME_ ) LIKE ?");
			params.add(vo.getWfName());
		}
		if(vo.getStat()!=null) {
			sql.append(" AND wf.stat = ?");
			params.add(vo.getStat());
		}
		
	}
}
