package net.shopxx.core.util;

import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.util.base.ErrMsg;
import net.shopxx.util.base.ErrMsgResolver;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;

public class HttpClientUtil {
	public static String post(String url, String jsonStr) {
		HttpPost httppost = new HttpPost(url);
		return request(jsonStr, (HttpRequestBase)httppost, url, null);
	}
	
	public static String post(String url, String jsonStr,ErrMsgResolver errMsgResolver) {
		HttpPost httppost = new HttpPost(url);
		return request(jsonStr, (HttpRequestBase)httppost, url, null,errMsgResolver);
	}
	
	public static String get(String url) {
		HttpGet httpget = new HttpGet(url);
		return request(null, (HttpRequestBase)httpget, url, null);
	}

	public static String get(String url,ErrMsgResolver errMsgResolver) {
		HttpGet httpget = new HttpGet(url);
		return request(null, (HttpRequestBase)httpget, url, null,errMsgResolver);
	}
	
	public static String post(String url, String jsonStr, Map<String, String> headers) {
		HttpPost httppost = new HttpPost(url);
		return request(jsonStr, (HttpRequestBase)httppost, url, headers);
	}
	
	public static String post(String url, String jsonStr, Map<String, String> headers,ErrMsgResolver errMsgResolver) {
		HttpPost httppost = new HttpPost(url);
		return request(jsonStr, (HttpRequestBase)httppost, url, headers,errMsgResolver);
	}

	public static String get(String url, Map<String, String> headers) {
		HttpGet httpget = new HttpGet(url);
		return request(null, (HttpRequestBase)httpget, url, headers);
	}

	public static String get(String url, Map<String, String> headers,ErrMsgResolver errMsgResolver) {
		HttpGet httpget = new HttpGet(url);
		return request(null, (HttpRequestBase)httpget, url, headers,errMsgResolver);
	}

	private static String request(String jsonStr, HttpRequestBase request, String fullurl, Map<String, String> headers) {
		return request(jsonStr, request, fullurl, headers, null);
	}
	
	private static String request(String jsonStr, HttpRequestBase request, String fullurl, Map<String, String> headers,ErrMsgResolver errMsgResolver) {
		DefaultHttpClient defaultHttpClient = null;
		HttpResponse response = null;
		HttpClient httpclient = null;
		HttpEntity entity = null;
		try {
			defaultHttpClient = new DefaultHttpClient();
			defaultHttpClient.getParams().setParameter(
					"http.connection.timeout", Integer.valueOf(10000));
			defaultHttpClient.getParams().setParameter("http.socket.timeout", 
					Integer.valueOf(120000));
			request.addHeader("Cache-Control", "no-cache");
			request.addHeader("Content-Type", "text/plain;charset=utf-8");
			if (headers != null)
				for (Map.Entry<String, String> e : headers.entrySet()) {
					if (((String)e.getKey()).equalsIgnoreCase("Content-Type"))
						request.removeHeaders("Content-Type"); 
					request.addHeader(e.getKey(), e.getValue());
				}  
			if (!ConvertUtil.isEmpty(jsonStr)) {
				StringEntity s = new StringEntity(jsonStr, "utf-8");
				((HttpPost)request).setEntity((HttpEntity)s);
			} 
			response = defaultHttpClient.execute((HttpUriRequest)request);
			entity = response.getEntity();
			int statusCode = response.getStatusLine().getStatusCode();
			if (statusCode == 200) 
				return EntityUtils.toString(entity, "utf-8"); 
			String exceptionMsg = "StatusCode:" + statusCode;
			if(errMsgResolver!=null) {
				ErrMsg errMsg = errMsgResolver.getErrorMessage(response);
				String message = errMsg.getMessage();
				exceptionMsg = exceptionMsg + ",ErrorMessage:"+ message;
			}
			throw new RuntimeException(exceptionMsg);
		} catch (Exception e) {
			throw new RuntimeException(fullurl, e);
		} finally {
			try {
				EntityUtils.consume(entity);
			} catch (IOException e) {
				LogUtils.error("EntityUtils.consume(entity) ", e);
			} 
			if (defaultHttpClient != null)
				defaultHttpClient.getConnectionManager().shutdown(); 
		} 
	}
}
