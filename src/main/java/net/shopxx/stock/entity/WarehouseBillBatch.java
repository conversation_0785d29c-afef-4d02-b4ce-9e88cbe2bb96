package net.shopxx.stock.entity;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.AmShippingItem;
import net.shopxx.order.entity.MoveLibrary;
import net.shopxx.order.entity.MoveLibraryItem;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingItem;

/**
 * Entity - 仓库单据批次
 */
@Entity
@Table(name = "xx_warehouse_bill_batch")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_warehouse_bill_batch_sequence")
public class WarehouseBillBatch extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/** 订单 */
	private Order orders;
	
	/** 订单明细 */
	private OrderItem orderItem;
	
	/** 发货单 */
	private Shipping shipping;
	
	/** 发货单明细 */
	private ShippingItem shippingItem;
	
	/** 退货单 */
	private B2bReturns b2bReturns;
	
	/** 退货单明细 */
	private B2bReturnsItem b2bReturnsItem;
	
	/** 移库单 */
	private MoveLibrary moveLibrary;
	
	/** 移库单明细 */
	private MoveLibraryItem moveLibraryItem;
	
	/** 出入库单 */
	private AmShipping amShipping;
	
	/** 出入库单明细 */
	private AmShippingItem amShippingItem;
	
	/** 批次 */
	private WarehouseBatch warehouseBatch;
	
	private SystemDict sysType;
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Order getOrders() {
		return orders;
	}

	public void setOrders(Order orders) {
		this.orders = orders;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public OrderItem getOrderItem() {
		return orderItem;
	}

	public void setOrderItem(OrderItem orderItem) {
		this.orderItem = orderItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Shipping getShipping() {
		return shipping;
	}
	
	public void setShipping(Shipping shipping) {
		this.shipping = shipping;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public ShippingItem getShippingItem() {
		return shippingItem;
	}

	public void setShippingItem(ShippingItem shippingItem) {
		this.shippingItem = shippingItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public B2bReturns getB2bReturns() {
		return b2bReturns;
	}
	
	public void setB2bReturns(B2bReturns b2bReturns) {
		this.b2bReturns = b2bReturns;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public B2bReturnsItem getB2bReturnsItem() {
		return b2bReturnsItem;
	}

	public void setB2bReturnsItem(B2bReturnsItem b2bReturnsItem) {
		this.b2bReturnsItem = b2bReturnsItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public MoveLibrary getMoveLibrary() {
		return moveLibrary;
	}

	public void setMoveLibrary(MoveLibrary moveLibrary) {
		this.moveLibrary = moveLibrary;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public MoveLibraryItem getMoveLibraryItem() {
		return moveLibraryItem;
	}
	
	public void setMoveLibraryItem(MoveLibraryItem moveLibraryItem) {
		this.moveLibraryItem = moveLibraryItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public AmShipping getAmShipping() {
		return amShipping;
	}

	public void setAmShipping(AmShipping amShipping) {
		this.amShipping = amShipping;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public AmShippingItem getAmShippingItem() {
		return amShippingItem;
	}

	public void setAmShippingItem(AmShippingItem amShippingItem) {
		this.amShippingItem = amShippingItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public WarehouseBatch getWarehouseBatch() {
		return warehouseBatch;
	}

	public void setWarehouseBatch(WarehouseBatch warehouseBatch) {
		this.warehouseBatch = warehouseBatch;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getSysType() {
		return sysType;
	}

	public void setSysType(SystemDict sysType) {
		this.sysType = sysType;
	}
	
	
	
	
	
}
