package net.shopxx.stock.entity;

import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.wf.entity.WfBillEntity;
import org.hibernate.validator.constraints.NotEmpty;

import javax.persistence.*;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 库存盘点表
 * <AUTHOR> <PERSON>
 * @Date 2021-08-17
 */
@Entity
@Table(name = "xx_inventory")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_inventory_sequence")
public class Inventory extends WfBillEntity {

    /**
     * 单号
     */
    private String sn;

    /**
     * 单号日期
     */
    private Date billDate;

    /**
     * 单据状态
     */
    private Integer status;

    /** 机构 */
    private SaleOrg saleOrg;

    /**
     * 经营组织
     */
    private Organization organization;

    /**
     * SBU
     */
    private Sbu sbu;

    /**
     * 仓库
     */
    private Warehouse warehouse;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核人
     */
    private StoreMember checkMan;

    /**
     * 库存盘点明细
     */
    private List<InventoryItem> inventoryItems=new ArrayList<InventoryItem>();

    @Column(nullable = false, updatable = false, unique = true, length = 100)
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Sbu getSbu() {
        return sbu;
    }

    public void setSbu(Sbu sbu) {
        this.sbu = sbu;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Warehouse getWarehouse() {
        return warehouse;
    }

    public void setWarehouse(Warehouse warehouse) {
        this.warehouse = warehouse;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Valid
    @NotEmpty
    @OneToMany(mappedBy = "inventory", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    public List<InventoryItem> getInventoryItems() {
        return inventoryItems;
    }

    public void setInventoryItems(List<InventoryItem> inventoryItems) {
        this.inventoryItems = inventoryItems;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    /**
     * 获取 机构
     * @return saleOrg
     */
    @ManyToOne(fetch = FetchType.LAZY)
    public SaleOrg getSaleOrg() {
        return saleOrg;
    }

    public void setSaleOrg(SaleOrg saleOrg) {
        this.saleOrg = saleOrg;
    }


    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getCheckMan() {
        return checkMan;
    }

    public void setCheckMan(StoreMember checkMan) {
        this.checkMan = checkMan;
    }
}
