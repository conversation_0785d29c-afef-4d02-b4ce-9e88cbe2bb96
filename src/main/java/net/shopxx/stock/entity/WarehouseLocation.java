package net.shopxx.stock.entity;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
/**
 * Entity - 库位
 * */
@Entity
@Table(name = "xx_warehouse_location")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_warehouse_location_sequence")
public class WarehouseLocation extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3084892044130493164L;
	
	/**仓库*/
	private Warehouse warehouse;
	
	/**编码*/
	private String code;
	
	/**是否有效*/
	private Boolean isChecks;
	
	/**备注*/
	private String remarks;
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public Warehouse getWarehouse() {
		return warehouse;
	}

	public void setWarehouse(Warehouse warehouse) {
		this.warehouse = warehouse;
	}

	public String getCode() {
		return code;
	}

	public Boolean getIsChecks() {
		return isChecks;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setIsChecks(Boolean isChecks) {
		this.isChecks = isChecks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	
}
