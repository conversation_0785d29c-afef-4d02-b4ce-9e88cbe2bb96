package net.shopxx.stock.entity;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * Entity - 仓库
 */
@Entity
@Table(name = "xx_warehouse")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_warehouse_sequence")
public class Warehouse extends ActWfBillEntity {

	private static final long serialVersionUID = 3306189781518640912L;

	private String name;

	/** 仓库编号 */
	private String sn;

	/** 外部仓库编号 */
	private String erp_warehouse_code;

	/** 仓库sbu */
	private List<WarehouseSbu> warehouseSbu = new ArrayList<WarehouseSbu>();
	
	/** 仓库用户*/
	private List<WarehouseStoreMember> warehouseStoreMember = new ArrayList<WarehouseStoreMember>();

	/** 是否全国仓 */
	private Boolean whole_country;

	/** 是否启用 */
	private Boolean isEnabled;

	/** 多地区 */
	private Set<Area> areas = new HashSet<Area>();

//	/** 组织 */
//	private SaleOrg saleOrg;

//	/** 是否可销售 */
//	private Boolean isSales;

//	/** 库存是否同步到第三方平台 */
//	private Boolean isSynchro;

	/** 优先仓关联 */
//	private List<AreaWarehouseItem> areaWarehouseItems = new ArrayList<AreaWarehouseItem>();

	/** 类型：0自建仓 1委外仓 */
	private Integer type;

	/** 寄件人**/
	private String sender;

	/** 联系电话**/
	private String mobile;

	private Area area;

	/** 省 */
	private String province;

	/** 市 */
	private String city;

	/** 区、县 */
	private String district;

	/** 详细地址 */
	private String address;

//	/** 默认物流快递 */
//	private DeliveryCorp deliveryCorp;

	/**经营组织*/
	private Organization managementOrganization;

	/**库存组织*/
	private SystemDict stockSystemDict;

	/**类型*/
	private SystemDict typeSystemDict;

	/**工厂*/
	private SystemDict factorySystemDict;
	
	//2019-05-27 冯旗 设置是否显示库存
	/** 是否显示库存 */
	private Boolean isDisplay;

	/**销售机构*/
	//private SaleOrg saleOrg;
	//是否校验库存
	private Boolean sign;
	
	//是否启用批次
	private Boolean enableBatch;
	
	//是否自动匹配库存
	private Boolean autoMatchInventory;
	
	//是否启用库位
	private Boolean enableLocation;
	
	/**生产工厂*/
	private SystemDict productionPlant;
	
	/** 发运方式 */
	private List<WarehouseSmethod> warehouseSmethodList = new ArrayList<WarehouseSmethod>();
	
	
	/**库位*/
	private List<WarehouseLocation> warehouseLocations = new ArrayList<WarehouseLocation>();
	
	
	/**是否发货库存校验*/
	private Boolean isShippedStockCheck;
	
	/**是否订货入仓*/
	private Boolean isOrderWarehousing;
	
	/**是否订货批次出仓*/
	private Boolean isOrderWarehousingBatch;

	//2020-10-22 lantianlong 设置是否仓库审核
	/** 仓库状态 0已保存、 1已生效 、2已失效 、3已作废*/
	private Integer warehouseStatus;


    /** 仓库流程类型 1.新建 2.失效 3.修改*/
	private Integer wfType;

	   /**是否推送家哇云接口*/
    private Boolean isSendJiaWaYun;

	

    public Integer getWfType() {
        return wfType;
    }

    public void setWfType(Integer wfType) {
        this.wfType = wfType;
    }

    public Integer getWarehouseStatus() {
		return warehouseStatus;
	}

	public void setWarehouseStatus(Integer warehouseStatus) {
		this.warehouseStatus = warehouseStatus;
	}




	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getFactorySystemDict() {
		return factorySystemDict;
	}

	public void setFactorySystemDict(SystemDict factorySystemDict) {
		this.factorySystemDict = factorySystemDict;
	}

	/**  
	 * 获取sender  
	 * @return sender
	 */
	public String getSender() {
		return sender;
	}

	/**  
	 * 设置sender  
	 * @param sender sender  
	 */
	public void setSender(String sender) {
		this.sender = sender;
	}

	/**  
	 * 获取mobile  
	 * @return mobile
	 */
	public String getMobile() {
		return mobile;
	}

	/**  
	 * 设置mobile  
	 * @param mobile mobile  
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	/**
	 * 获取 name
	 * @return name
	 */
	public String getName() {
		return name;
	}

	/**
	 * 设置 name
	 * @param name
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 获取 sn
	 * @return sn
	 */
	@Column(nullable = false, updatable = false, unique = true, length = 200)
	public String getSn() {
		return sn;
	}

	/**
	 * 设置 sn
	 * @param sn
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	/**
	 * 获取 area
	 * @return area
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Area getArea() {
		return area;
	}

	/**
	 * 设置 area
	 * @param area
	 */
	public void setArea(Area area) {
		this.area = area;
	}

	/**
	 * 获取 province
	 * @return the province
	 */
	public String getProvince() {
		return province;
	}

	/**
	 * 设置 province 
	 * @param province the province to set
	 */
	public void setProvince(String province) {
		this.province = province;
	}

	/**
	 * 获取 city
	 * @return the city
	 */
	public String getCity() {
		return city;
	}

	/**
	 * 设置 city 
	 * @param city the city to set
	 */
	public void setCity(String city) {
		this.city = city;
	}

	/**
	 * 获取 district
	 * @return the district
	 */
	public String getDistrict() {
		return district;
	}

	/**
	 * 设置 district 
	 * @param district the district to set
	 */
	public void setDistrict(String district) {
		this.district = district;
	}

	/**
	 * 获取 address
	 * @return the address
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * 设置 address 
	 * @param address the address to set
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	public Boolean getWhole_country() {
		return whole_country;
	}

	public void setWhole_country(Boolean whole_country) {
		this.whole_country = whole_country;
	}

	/**
	 * @return the isEnabled
	 */
	public Boolean getIsEnabled() {
		return isEnabled;
	}

	/**
	 * @param isEnabled the isEnabled to set
	 */
	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}

	/**
	 * 获取仓库设置的多地区
	 * @return	地区
	 */
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "xx_warehouse_area")
	@OrderBy("createDate desc")
	@JsonIgnore
	public Set<Area> getAreas() {
		return areas;
	}

	/**
	 * 设置 仓库设置的多地区
	 * @param areas	地区
	 */
	public void setAreas(Set<Area> areas) {
		this.areas = areas;
	}

//	/**
//	 * 获取组织
//	 * @return
//	 */
//	@ManyToOne(fetch = FetchType.LAZY)
//	public SaleOrg getSaleOrg() {
//		return saleOrg;
//	}
//
//	/**
//	 * 设置组织
//	 * @param saleOrg
//	 */
//	public void setSaleOrg(SaleOrg saleOrg) {
//		this.saleOrg = saleOrg;
//	}
//
//	/**
//	 * 获取 是否可销售
//	 * @return isSales
//	 */
//	public Boolean getIsSales() {
//		return isSales;
//	}
//
//	/**
//	 * 设置 是否可销售
//	 * @param isSales 是否可销售
//	 */
//	public void setIsSales(Boolean isSales) {
//		this.isSales = isSales;
//	}
//
//	/**
//	 * 获取 库存是否同步到第三方平台
//	 * @return isSynchro
//	 */
//	public Boolean getIsSynchro() {
//		return isSynchro;
//	}
//
//	/**
//	 * 设置 库存是否同步到第三方平台
//	 * @param isSynchro 库存是否同步到第三方平台
//	 */
//	public void setIsSynchro(Boolean isSynchro) {
//		this.isSynchro = isSynchro;
//	}

//	/**  
//	 * 获取areaWarehouseItems  
//	 * @return areaWarehouseItems
//	 */
//	@OneToMany(mappedBy = "areaWarehouse", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
//	@JsonIgnore
//	public List<AreaWarehouseItem> getAreaWarehouseItems() {
//		return areaWarehouseItems;
//	}
//
//	/**  
//	 * 设置areaWarehouseItems  
//	 * @param areaWarehouseItems areaWarehouseItems  
//	 */
//	public void setAreaWarehouseItems(List<AreaWarehouseItem> areaWarehouseItems) {
//		this.areaWarehouseItems = areaWarehouseItems;
//	}

	/**  
	 * 获取erp_warehouse_code  
	 * @return erp_warehouse_code
	 */
	public String getErp_warehouse_code() {
		return erp_warehouse_code;
	}

	/**  
	 * 设置erp_warehouse_code  
	 * @param erp_warehouse_code erp_warehouse_code  
	 */
	public void setErp_warehouse_code(String erp_warehouse_code) {
		this.erp_warehouse_code = erp_warehouse_code;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

//	/**
//	 * 获取 物流快递
//	 * @date 2017年8月18日
//	 * @return deliveryCorp
//	 */
//	@JsonIgnore
//	@ManyToOne(fetch = FetchType.LAZY)
//	public DeliveryCorp getDeliveryCorp() {
//		return deliveryCorp;
//	}
//	
//
//	/**
//	 * 设置 物流快递
//	 * @date 2017年8月18日
//	 * @param deliveryCorp 物流快递
//	 */
//	public void setDeliveryCorp(DeliveryCorp deliveryCorp) {
//		this.deliveryCorp = deliveryCorp;
//	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getManagementOrganization() {
		return managementOrganization;
	}

	public void setManagementOrganization(Organization managementOrganization) {
		this.managementOrganization = managementOrganization;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getStockSystemDict() {
		return stockSystemDict;
	}

	public void setStockSystemDict(SystemDict stockSystemDict) {
		this.stockSystemDict = stockSystemDict;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getTypeSystemDict() {
		return typeSystemDict;
	}

	public void setTypeSystemDict(SystemDict typeSystemDict) {
		this.typeSystemDict = typeSystemDict;
	}

	/*
	 * @ManyToOne(fetch = FetchType.LAZY) public SaleOrg getSaleOrg() { return
	 * saleOrg; } public void setSaleOrg(SaleOrg saleOrg) { this.saleOrg =
	 * saleOrg; }
	 */

	/**
	 * 仓库sbu中间表的关联
	 * */
	@OneToMany(mappedBy = "warehouse", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<WarehouseSbu> getWarehouseSbu() {
		return warehouseSbu;
	}

	public void setWarehouseSbu(List<WarehouseSbu> warehouseSbu) {
		this.warehouseSbu = warehouseSbu;
	}

	/**
	 * 持久化前处理
	 */
	@PrePersist
	public void prePersist() {
		if (getType() == null) {
			setType(0);
		}
	}

	/**
	 * 更新前处理
	 */
	@PreUpdate
	public void preUpdate() {
		if (getType() == null) {
			setType(0);
		}
	}

	public Boolean getIsDisplay() {
		return isDisplay;
	}

	public void setIsDisplay(Boolean isDisplay) {
		this.isDisplay = isDisplay;
	}

	@OneToMany(mappedBy = "warehouse", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<WarehouseSmethod> getWarehouseSmethodList() {
		return warehouseSmethodList;
	}

	public void setWarehouseSmethodList(List<WarehouseSmethod> warehouseSmethodList) {
		this.warehouseSmethodList = warehouseSmethodList;
	}
	
	/**
	 *  仓库用户中间表的关联
	 * @return
	 */
	@OneToMany(mappedBy = "warehouse", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<WarehouseStoreMember> getWarehouseStoreMember() {
		return warehouseStoreMember;
	}

	public void setWarehouseStoreMember(List<WarehouseStoreMember> warehouseStoreMember) {
		this.warehouseStoreMember = warehouseStoreMember;
	}

	public Boolean getSign() {
		return sign;
	}

	public void setSign(Boolean sign) {
		this.sign = sign;
	}

	public Boolean getEnableBatch() {
		return enableBatch;
	}

	public void setEnableBatch(Boolean enableBatch) {
		this.enableBatch = enableBatch;
	}
	
		
	public Boolean getEnableLocation() {
		return enableLocation;
	}

	public void setEnableLocation(Boolean enableLocation) {
		this.enableLocation = enableLocation;
	}

	public Boolean getAutoMatchInventory() {
		return autoMatchInventory;
	}

	public void setAutoMatchInventory(Boolean autoMatchInventory) {
		this.autoMatchInventory = autoMatchInventory;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getProductionPlant() {
		return productionPlant;
	}

	public void setProductionPlant(SystemDict productionPlant) {
		this.productionPlant = productionPlant;
	}
	
	@OneToMany(mappedBy = "warehouse", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<WarehouseLocation> getWarehouseLocations() {
		return warehouseLocations;
	}

	public void setWarehouseLocations(List<WarehouseLocation> warehouseLocations) {
		this.warehouseLocations = warehouseLocations;
	}

	public Boolean getIsShippedStockCheck() {
		return isShippedStockCheck;
	}

	public void setIsShippedStockCheck(Boolean isShippedStockCheck) {
		this.isShippedStockCheck = isShippedStockCheck;
	}

	public Boolean getIsOrderWarehousing() {
		return isOrderWarehousing;
	}

	public void setIsOrderWarehousing(Boolean isOrderWarehousing) {
		this.isOrderWarehousing = isOrderWarehousing;
	}

	public Boolean getIsOrderWarehousingBatch() {
		return isOrderWarehousingBatch;
	}

	public void setIsOrderWarehousingBatch(Boolean isOrderWarehousingBatch) {
		this.isOrderWarehousingBatch = isOrderWarehousingBatch;
	}
	
	   public Boolean getIsSendJiaWaYun() {
	        return isSendJiaWaYun;
	    }

	    public void setIsSendJiaWaYun(Boolean isSendJiaWaYun) {
	        this.isSendJiaWaYun = isSendJiaWaYun;
	    }
}


