package net.shopxx.stock.entity;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.AmShippingItem;
import net.shopxx.product.entity.Product;

/**
 * Entity - 仓库批次明细
 */
@Entity
@Table(name = "xx_warehouse_batch_item")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_warehouse_batch_item_sequence")
public class WarehouseBatchItem extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5046640966664259082L;
	
	/**单据类型*/
	private SystemDict billType;
	
	/**出入库单明细*/
	private AmShippingItem amShippingItem;
	
	/**出库单明细*/
	private AmShippingItem outAmShippingItem;
	
	/**入库单明细*/
	private AmShippingItem enterAmShippingItem;
	
	/**仓库*/
	private Warehouse warehouse;
	
	/**经营组织*/
	private Organization organization;
	
	/**产品*/
	private Product product;
	
	/**产品等别*/
	private SystemDict productLevel;
	
	/**数量*/
	private BigDecimal quantity;
	
	/**色号*/
	private SystemDict colorNumbers;
	
	/**含水率*/
	private SystemDict moistureContents;
	
	/**批次*/
	private WarehouseBatch warehouseBatch;
	
	/**库位*/
	private WarehouseLocation warehouseLocation;
	
	/**新旧标识*/
	private SystemDict newOldLogos;
	
	/**来源单号*/
	private String documentNo;
	/**单据状态*/
	private Integer status;
	/**单据日期*/
	private Date documentDate;
	
	/** 出入库单 */
	private AmShipping amShipping;
	
	// 实际发货箱数
	private BigDecimal shippedBoxQuantity;

	// 实际发货支数 (实际发货箱数*每箱支数)
	private BigDecimal shippedBranchQuantity;
	
	//零散支数
	private BigDecimal shippedBranchScattered;
	
	//出库关联的入库对象
	private Long warehousingId;
	
	//出库关联的入库对象
    private Long batchItemId;
	
	// 实际发货数量
    private BigDecimal shippedQuantity;
    
    //原移库发出批次ID
    private Long outBatchId;
    
	
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getBillType() {
		return billType;
	}

	public void setBillType(SystemDict billType) {
		this.billType = billType;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public AmShippingItem getAmShippingItem() {
		return amShippingItem;
	}

	public void setAmShippingItem(AmShippingItem amShippingItem) {
		this.amShippingItem = amShippingItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public AmShippingItem getOutAmShippingItem() {
		return outAmShippingItem;
	}

	public void setOutAmShippingItem(AmShippingItem outAmShippingItem) {
		this.outAmShippingItem = outAmShippingItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public AmShippingItem getEnterAmShippingItem() {
		return enterAmShippingItem;
	}

	public void setEnterAmShippingItem(AmShippingItem enterAmShippingItem) {
		this.enterAmShippingItem = enterAmShippingItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Warehouse getWarehouse() {
		return warehouse;
	}

	public void setWarehouse(Warehouse warehouse) {
		this.warehouse = warehouse;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
	
	
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Product getProduct() {
		return product;
	}
	
	public void setProduct(Product product) {
		this.product = product;
	}
	
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getProductLevel() {
		return productLevel;
	}

	public void setProductLevel(SystemDict productLevel) {
		this.productLevel = productLevel;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getColorNumbers() {
		return colorNumbers;
	}

	public void setColorNumbers(SystemDict colorNumbers) {
		this.colorNumbers = colorNumbers;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getMoistureContents() {
		return moistureContents;
	}

	public void setMoistureContents(SystemDict moistureContents) {
		this.moistureContents = moistureContents;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public WarehouseBatch getWarehouseBatch() {
		return warehouseBatch;
	}

	public void setWarehouseBatch(WarehouseBatch warehouseBatch) {
		this.warehouseBatch = warehouseBatch;
	}
	
	
	@ManyToOne(fetch = FetchType.LAZY)
	public WarehouseLocation getWarehouseLocation() {
		return warehouseLocation;
	}

	public void setWarehouseLocation(WarehouseLocation warehouseLocation) {
		this.warehouseLocation = warehouseLocation;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getNewOldLogos() {
		return newOldLogos;
	}

	public void setNewOldLogos(SystemDict newOldLogos) {
		this.newOldLogos = newOldLogos;
	}

	public String getDocumentNo() {
		return documentNo;
	}

	public void setDocumentNo(String documentNo) {
		this.documentNo = documentNo;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getDocumentDate() {
		return documentDate;
	}

	public void setDocumentDate(Date documentDate) {
		this.documentDate = documentDate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public AmShipping getAmShipping() {
		return amShipping;
	}

	public void setAmShipping(AmShipping amShipping) {
		this.amShipping = amShipping;
	}

	public BigDecimal getShippedBoxQuantity() {
		return shippedBoxQuantity;
	}

	public void setShippedBoxQuantity(BigDecimal shippedBoxQuantity) {
		this.shippedBoxQuantity = shippedBoxQuantity;
	}

	public BigDecimal getShippedBranchQuantity() {
		return shippedBranchQuantity;
	}

	public void setShippedBranchQuantity(BigDecimal shippedBranchQuantity) {
		this.shippedBranchQuantity = shippedBranchQuantity;
	}

	public BigDecimal getShippedBranchScattered() {
		return shippedBranchScattered;
	}

	public void setShippedBranchScattered(BigDecimal shippedBranchScattered) {
		this.shippedBranchScattered = shippedBranchScattered;
	}

	public Long getWarehousingId() {
		return warehousingId;
	}

	public void setWarehousingId(Long warehousingId) {
		this.warehousingId = warehousingId;
	}

	public BigDecimal getShippedQuantity() {
		return shippedQuantity;
	}

	public void setShippedQuantity(BigDecimal shippedQuantity) {
		this.shippedQuantity = shippedQuantity;
	}

	public Long getBatchItemId() {
		return batchItemId;
	}

	public void setBatchItemId(Long batchItemId) {
		this.batchItemId = batchItemId;
	}

	public Long getOutBatchId() {
		return outBatchId;
	}

	public void setOutBatchId(Long outBatchId) {
		this.outBatchId = outBatchId;
	}

	
	
	
	

}
