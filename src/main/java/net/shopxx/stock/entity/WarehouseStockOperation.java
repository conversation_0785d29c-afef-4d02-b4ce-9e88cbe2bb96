package net.shopxx.stock.entity;
import java.math.BigDecimal;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.AmShippingItem;
/**
 * Entity - 仓库库存操作
 */
@Entity
@Table(name = "xx_warehouse_stock_operation")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_warehouse_stock_operation_sequence")
public class WarehouseStockOperation extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 2521118240343629077L;
	
	/**库存*/
	private WarehouseStock warehouseStock;
	/**出入库*/
	private AmShipping amShipping;
	/**出入库明细*/
	private AmShippingItem amShippingItem;
	/**出入库明细批次*/
	private WarehouseBatchItem warehouseBatchItem;
	/**现有量（箱）*/
	private BigDecimal onhandBoxQuantity; 
	/**现有量（支）*/
	private BigDecimal onhandBranchQuantity; 
	/**现有量平方数*/
	private BigDecimal onhandQuantity; 
	/**可处理量（箱）*/
	private BigDecimal availableBoxQuantity; 
	/**可处理量（支）*/
	private BigDecimal availableBranchQuantity; 
	/**可处理量平方数*/
	private BigDecimal availableQuantity;
	/**操作类型*/
	private SystemDict actionType; 
	/**单据类型*/
	private SystemDict billType;
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public WarehouseStock getWarehouseStock() {
		return warehouseStock;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public AmShipping getAmShipping() {
		return amShipping;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public AmShippingItem getAmShippingItem() {
		return amShippingItem;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public WarehouseBatchItem getWarehouseBatchItem() {
		return warehouseBatchItem;
	}
	
	public BigDecimal getOnhandBoxQuantity() {
		return onhandBoxQuantity;
	}
	public BigDecimal getOnhandBranchQuantity() {
		return onhandBranchQuantity;
	}
	
	public BigDecimal getOnhandQuantity() {
		return onhandQuantity;
	}
	
	public BigDecimal getAvailableBoxQuantity() {
		return availableBoxQuantity;
	}
	public BigDecimal getAvailableBranchQuantity() {
		return availableBranchQuantity;
	}
	
	public BigDecimal getAvailableQuantity() {
		return availableQuantity;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getActionType() {
		return actionType;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getBillType() {
		return billType;
	}
	
	public void setWarehouseStock(WarehouseStock warehouseStock) {
		this.warehouseStock = warehouseStock;
	}
	
	public void setAmShipping(AmShipping amShipping) {
		this.amShipping = amShipping;
	}
	
	public void setAmShippingItem(AmShippingItem amShippingItem) {
		this.amShippingItem = amShippingItem;
	}
	public void setWarehouseBatchItem(WarehouseBatchItem warehouseBatchItem) {
		this.warehouseBatchItem = warehouseBatchItem;
	}
	
	public void setOnhandBoxQuantity(BigDecimal onhandBoxQuantity) {
		this.onhandBoxQuantity = onhandBoxQuantity;
	}
	
	public void setOnhandBranchQuantity(BigDecimal onhandBranchQuantity) {
		this.onhandBranchQuantity = onhandBranchQuantity;
	}
	
	public void setOnhandQuantity(BigDecimal onhandQuantity) {
		this.onhandQuantity = onhandQuantity;
	}
	public void setAvailableBoxQuantity(BigDecimal availableBoxQuantity) {
		this.availableBoxQuantity = availableBoxQuantity;
	}
	
	public void setAvailableBranchQuantity(BigDecimal availableBranchQuantity) {
		this.availableBranchQuantity = availableBranchQuantity;
	}
	
	public void setAvailableQuantity(BigDecimal availableQuantity) {
		this.availableQuantity = availableQuantity;
	}
	
	public void setActionType(SystemDict actionType) {
		this.actionType = actionType;
	}
	
	public void setBillType(SystemDict billType) {
		this.billType = billType;
	}
}
