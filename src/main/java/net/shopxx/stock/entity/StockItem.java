package net.shopxx.stock.entity;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.act.entity.ActWfBillEntity;

/**
 * Entity - 库存表20200713
 */
@Entity
@Table(name = "xx_stock_item")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_stock_item_sequence")
public class StockItem extends ActWfBillEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3602834255077706740L;

	/** 物料 */
	private String product;

	/** 产品级别 */
	private String productLevel;

	/** 仓库 */
	private String warehouse;

	/** 现有量（箱） */
	private BigDecimal onhandBox;

	/** 现有量（支） */
	private BigDecimal onhandBranch;

	/** 现有量平方数 */
	private BigDecimal onhandQuantity;

	/** 可处理量（箱） */
	private BigDecimal attBox;

	/** 可处理量（支） */
	private BigDecimal attBranch;

	/** 可处理量平方数 */
	private BigDecimal attQuantity;

	/** 经营组织 */
	private String organization;

	/** 色号 */
	private String colorNumber;

	/** 含水率 */
	private String moistureContent;

	/** 新旧标识 */
	private String newOldLogo;

	/** 批次 */
	private String warehouseBatch;

	/** 库位 */
	private String warehouseLocation;

	public String getProduct() {
		return product;
	}

	public void setProduct(String product) {
		this.product = product;
	}

	public String getProductLevel() {
		return productLevel;
	}

	public void setProductLevel(String productLevel) {
		this.productLevel = productLevel;
	}

	public String getWarehouse() {
		return warehouse;
	}

	public void setWarehouse(String warehouse) {
		this.warehouse = warehouse;
	}

	public BigDecimal getOnhandBox() {
		return onhandBox;
	}

	public void setOnhandBox(BigDecimal onhandBox) {
		this.onhandBox = onhandBox;
	}

	public BigDecimal getOnhandBranch() {
		return onhandBranch;
	}

	public void setOnhandBranch(BigDecimal onhandBranch) {
		this.onhandBranch = onhandBranch;
	}

	public BigDecimal getOnhandQuantity() {
		return onhandQuantity;
	}

	public void setOnhandQuantity(BigDecimal onhandQuantity) {
		this.onhandQuantity = onhandQuantity;
	}

	public BigDecimal getAttBox() {
		return attBox;
	}

	public void setAttBox(BigDecimal attBox) {
		this.attBox = attBox;
	}

	public BigDecimal getAttBranch() {
		return attBranch;
	}

	public void setAttBranch(BigDecimal attBranch) {
		this.attBranch = attBranch;
	}

	public BigDecimal getAttQuantity() {
		return attQuantity;
	}

	public void setAttQuantity(BigDecimal attQuantity) {
		this.attQuantity = attQuantity;
	}

	public String getOrganization() {
		return organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}

	public String getColorNumber() {
		return colorNumber;
	}

	public void setColorNumber(String colorNumber) {
		this.colorNumber = colorNumber;
	}

	public String getMoistureContent() {
		return moistureContent;
	}

	public void setMoistureContent(String moistureContent) {
		this.moistureContent = moistureContent;
	}

	public String getNewOldLogo() {
		return newOldLogo;
	}

	public void setNewOldLogo(String newOldLogo) {
		this.newOldLogo = newOldLogo;
	}

	public String getWarehouseBatch() {
		return warehouseBatch;
	}

	public void setWarehouseBatch(String warehouseBatch) {
		this.warehouseBatch = warehouseBatch;
	}

	public String getWarehouseLocation() {
		return warehouseLocation;
	}

	public void setWarehouseLocation(String warehouseLocation) {
		this.warehouseLocation = warehouseLocation;
	}

	public StockItem() {
		super();
		// TODO Auto-generated constructor stub
	}

	public StockItem(String product, String productLevel, String warehouse, BigDecimal onhandBox,
			BigDecimal onhandBranch, BigDecimal onhandQuantity, BigDecimal attBox, BigDecimal attBranch,
			BigDecimal attQuantity, String organization, String colorNumber, String moistureContent, String newOldLogo,
			String warehouseBatch, String warehouseLocation) {
		super();
		this.product = product;
		this.productLevel = productLevel;
		this.warehouse = warehouse;
		this.onhandBox = onhandBox;
		this.onhandBranch = onhandBranch;
		this.onhandQuantity = onhandQuantity;
		this.attBox = attBox;
		this.attBranch = attBranch;
		this.attQuantity = attQuantity;
		this.organization = organization;
		this.colorNumber = colorNumber;
		this.moistureContent = moistureContent;
		this.newOldLogo = newOldLogo;
		this.warehouseBatch = warehouseBatch;
		this.warehouseLocation = warehouseLocation;
	}

}
