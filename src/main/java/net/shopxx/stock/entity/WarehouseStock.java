package net.shopxx.stock.entity;
import java.math.BigDecimal;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.product.entity.Product;
/**
 * Entity - 仓库库存
 */
@Entity
@Table(name = "xx_warehouse_stock")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_warehouse_stock_sequence")
public class WarehouseStock extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 2800081027073871465L;
	/**仓库*/
	private Warehouse warehouse;
	/**产品*/
	private Product product;
	/**产品级别*/
	private SystemDict productLevel;
	/**经营组织*/
	private Organization organization;
	/**色号*/
	private SystemDict colorNumber;
	/**含水率*/
	private SystemDict moistureContent;
	/**批次*/
	private WarehouseBatch warehouseBatch;
	/**库位*/
	private WarehouseLocation warehouseLocation;
	/**新旧标识*/
	private SystemDict oldNewLogo;
	/**现有量（箱）*/
	private BigDecimal onhandBoxQuantity; 
	/**现有量（支）*/
	private BigDecimal onhandBranchQuantity; 
	/**现有量平方数*/
	private BigDecimal onhandQuantity; 
	/**可处理量（箱）*/
	private BigDecimal availableBoxQuantity; 
	/**可处理量（支）*/
	private BigDecimal availableBranchQuantity; 
	/**可处理量平方数*/
	private BigDecimal availableQuantity;
	/**标识唯一值*/
	private String onlyValue;
	/**操作类型*/
	private SystemDict actionType; 
	
	/**仓库库位操作*/
	/*private WarehouseStockOperation warehouseStockOperation;*/
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Warehouse getWarehouse() {
		return warehouse;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Product getProduct() {
		return product;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getProductLevel() {
		return productLevel;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getColorNumber() {
		return colorNumber;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getMoistureContent() {
		return moistureContent;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public WarehouseBatch getWarehouseBatch() {
		return warehouseBatch;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public WarehouseLocation getWarehouseLocation() {
		return warehouseLocation;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getOldNewLogo() {
		return oldNewLogo;
	}
	
	public BigDecimal getOnhandBoxQuantity() {
		return onhandBoxQuantity;
	}
	
	public BigDecimal getOnhandBranchQuantity() {
		return onhandBranchQuantity;
	}
	
	public BigDecimal getOnhandQuantity() {
		return onhandQuantity;
	}
	
	public BigDecimal getAvailableBoxQuantity() {
		return availableBoxQuantity;
	}
	
	public BigDecimal getAvailableBranchQuantity() {
		return availableBranchQuantity;
	}
	
	public BigDecimal getAvailableQuantity() {
		return availableQuantity;
	}
	
	public void setWarehouse(Warehouse warehouse) {
		this.warehouse = warehouse;
	}
	
	public void setProduct(Product product) {
		this.product = product;
	}
	
	public void setProductLevel(SystemDict productLevel) {
		this.productLevel = productLevel;
	}
	
	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
	
	public void setColorNumber(SystemDict colorNumber) {
		this.colorNumber = colorNumber;
	}
	public void setMoistureContent(SystemDict moistureContent) {
		this.moistureContent = moistureContent;
	}
	
	public void setWarehouseBatch(WarehouseBatch warehouseBatch) {
		this.warehouseBatch = warehouseBatch;
	}
	public void setWarehouseLocation(WarehouseLocation warehouseLocation) {
		this.warehouseLocation = warehouseLocation;
	}
	
	public void setOldNewLogo(SystemDict oldNewLogo) {
		this.oldNewLogo = oldNewLogo;
	}
	
	public void setOnhandBoxQuantity(BigDecimal onhandBoxQuantity) {
		this.onhandBoxQuantity = onhandBoxQuantity;
	}
	
	public void setOnhandBranchQuantity(BigDecimal onhandBranchQuantity) {
		this.onhandBranchQuantity = onhandBranchQuantity;
	}
	
	public void setOnhandQuantity(BigDecimal onhandQuantity) {
		this.onhandQuantity = onhandQuantity;
	}
	
	public void setAvailableBoxQuantity(BigDecimal availableBoxQuantity) {
		this.availableBoxQuantity = availableBoxQuantity;
	}
	
	public void setAvailableBranchQuantity(BigDecimal availableBranchQuantity) {
		this.availableBranchQuantity = availableBranchQuantity;
	}
	
	public void setAvailableQuantity(BigDecimal availableQuantity) {
		this.availableQuantity = availableQuantity;
	}

	public String getOnlyValue() {
		return onlyValue;
	}

	public void setOnlyValue(String onlyValue) {
		this.onlyValue = onlyValue;
	}
	
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getActionType() {
		return actionType;
	}
	
	public void setActionType(SystemDict actionType) {
		this.actionType = actionType;
	}
	
	/*@JsonIgnore
	@OneToOne(mappedBy = "warehouseStock", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public WarehouseStockOperation getWarehouseStockOperation() {
		return warehouseStockOperation;
	}

	public void setWarehouseStockOperation(WarehouseStockOperation warehouseStockOperation) {
		this.warehouseStockOperation = warehouseStockOperation;
	} */

}
