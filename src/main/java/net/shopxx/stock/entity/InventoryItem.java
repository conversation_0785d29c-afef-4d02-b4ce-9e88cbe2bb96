package net.shopxx.stock.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.factory.entity.ProductionSchedulingItem;
import net.shopxx.product.entity.Product;

import javax.persistence.*;
import java.math.BigDecimal;
/**
 * 库存盘点表明细
 * <AUTHOR> <PERSON>ian<PERSON>
 * @Date 2021-08-17
 */
@Entity
@Table(name = "xx_inventory_item")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_inventory_item_sequence")
public class InventoryItem extends BaseEntity {

    /**单据状态*/
    private Integer status;

    /** 行号 */
    private Integer lineNo;

    /**
     * 产品对应的库存
     * 入库时，所对应经销商的对应产品库存增加
     * 出库时，所对应经销商的对应产品库存减少
     */
    private Inventory inventory;

    /**产品*/
    private Product product;

    /** 厂商编号 */
    private String vonderCode;

    /**商品名称*/
    private String name;

    /**商品型号*/
    private String model;

    /**
     * 物料说明
     */
    private String description;

    /**
     * 箱数
     */
    private BigDecimal boxQuantity;
    /**
     * 每箱支数
     */
    private BigDecimal branchPerBox;

    /**
     * 支数
     */
    private BigDecimal branchQuantity;

    /**
     * 每支平方数
     */
    private BigDecimal perBranch;

    /**
     * 零散支数
     */
    private BigDecimal scatteredQuantity;

    /**
     * 平方数
     */
    private BigDecimal quantity;

    /**
     * 原支数
     */
    private BigDecimal regBranchQuantity;

    /**
     * 原数量
     */
    private BigDecimal regQuantity;

    /**
     * 差异支数
     */
    private BigDecimal differenceBranchQuantity;

    /**
     * 差异数量（平方数）
     */
    private BigDecimal differenceQuantity;

    /**
     * 色号
     */
    private SystemDict colorNumbers;

    /**
     * 含水量
     */
    private SystemDict moistureContents;

    /** 体积 */
    private BigDecimal volume;

    /**
     * 仓库
     */
    private Warehouse warehouse;

    /**
     * 产品等级
     */
    private SystemDict productLevel;

    /**
     * 新旧标识
     */
    private SystemDict newOldLogos;

    /**
     * 产品经营组织
     */
    private Organization productOrganization;

    /**
     * 仓库批次
     */
    private String warehouseBatch;

    /**
     * 批次编码
     */
    private String batchEncoding;

    /**
     * 库位
     */
    private WarehouseLocation warehouseLocation;

    /** 生产单明细 */
    private ProductionSchedulingItem productionSchedulingItem;

    /** 备注*/
    private String memo;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public Inventory getInventory() {
        return inventory;
    }

    public void setInventory(Inventory inventory) {
        this.inventory = inventory;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public String getVonderCode() {
        return vonderCode;
    }

    public void setVonderCode(String vonderCode) {
        this.vonderCode = vonderCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getBoxQuantity() {
        return boxQuantity;
    }

    public void setBoxQuantity(BigDecimal boxQuantity) {
        this.boxQuantity = boxQuantity;
    }

    public BigDecimal getBranchPerBox() {
        return branchPerBox;
    }

    public void setBranchPerBox(BigDecimal branchPerBox) {
        this.branchPerBox = branchPerBox;
    }

    public BigDecimal getBranchQuantity() {
        return branchQuantity;
    }

    public void setBranchQuantity(BigDecimal branchQuantity) {
        this.branchQuantity = branchQuantity;
    }

    public BigDecimal getPerBranch() {
        return perBranch;
    }

    public void setPerBranch(BigDecimal perBranch) {
        this.perBranch = perBranch;
    }

    public BigDecimal getScatteredQuantity() {
        return scatteredQuantity;
    }

    public void setScatteredQuantity(BigDecimal scatteredQuantity) {
        this.scatteredQuantity = scatteredQuantity;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getRegBranchQuantity() {
        return regBranchQuantity;
    }

    public void setRegBranchQuantity(BigDecimal regBranchQuantity) {
        this.regBranchQuantity = regBranchQuantity;
    }

    public BigDecimal getRegQuantity() {
        return regQuantity;
    }

    public void setRegQuantity(BigDecimal regQuantity) {
        this.regQuantity = regQuantity;
    }

    public BigDecimal getDifferenceBranchQuantity() {
        return differenceBranchQuantity;
    }

    public void setDifferenceBranchQuantity(BigDecimal differenceBranchQuantity) {
        this.differenceBranchQuantity = differenceBranchQuantity;
    }

    public BigDecimal getDifferenceQuantity() {
        return differenceQuantity;
    }

    public void setDifferenceQuantity(BigDecimal differenceQuantity) {
        this.differenceQuantity = differenceQuantity;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getColorNumbers() {
        return colorNumbers;
    }

    public void setColorNumbers(SystemDict colorNumbers) {
        this.colorNumbers = colorNumbers;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getMoistureContents() {
        return moistureContents;
    }

    public void setMoistureContents(SystemDict moistureContents) {
        this.moistureContents = moistureContents;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public Warehouse getWarehouse() {
        return warehouse;
    }

    public void setWarehouse(Warehouse warehouse) {
        this.warehouse = warehouse;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getProductLevel() {
        return productLevel;
    }

    public void setProductLevel(SystemDict productLevel) {
        this.productLevel = productLevel;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getNewOldLogos() {
        return newOldLogos;
    }

    public void setNewOldLogos(SystemDict newOldLogos) {
        this.newOldLogos = newOldLogos;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Organization getProductOrganization() {
        return productOrganization;
    }

    public void setProductOrganization(Organization productOrganization) {
        this.productOrganization = productOrganization;
    }

    public String getWarehouseBatch() {
        return warehouseBatch;
    }

    public void setWarehouseBatch(String warehouseBatch) {
        this.warehouseBatch = warehouseBatch;
    }

    public String getBatchEncoding() {
        return batchEncoding;
    }

    public void setBatchEncoding(String batchEncoding) {
        this.batchEncoding = batchEncoding;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public WarehouseLocation getWarehouseLocation() {
        return warehouseLocation;
    }

    public void setWarehouseLocation(WarehouseLocation warehouseLocation) {
        this.warehouseLocation = warehouseLocation;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public ProductionSchedulingItem getProductionSchedulingItem() {
        return productionSchedulingItem;
    }

    public void setProductionSchedulingItem(ProductionSchedulingItem productionSchedulingItem) {
        this.productionSchedulingItem = productionSchedulingItem;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getLineNo() {
        return lineNo;
    }

    public void setLineNo(Integer lineNo) {
        this.lineNo = lineNo;
    }



    @Override
    public String toString() {
        return "InventoryItem{" +
                "status=" + status +
                ", lineNo=" + lineNo +
                ", inventory=" + inventory +
                ", product=" + product +
                ", vonderCode='" + vonderCode + '\'' +
                ", name='" + name + '\'' +
                ", model='" + model + '\'' +
                ", description='" + description + '\'' +
                ", boxQuantity=" + boxQuantity +
                ", branchPerBox=" + branchPerBox +
                ", branchQuantity=" + branchQuantity +
                ", perBranch=" + perBranch +
                ", scatteredQuantity=" + scatteredQuantity +
                ", quantity=" + quantity +
                ", regBranchQuantity=" + regBranchQuantity +
                ", regQuantity=" + regQuantity +
                ", differenceBranchQuantity=" + differenceBranchQuantity +
                ", differenceQuantity=" + differenceQuantity +
                ", colorNumbers=" + colorNumbers +
                ", moistureContents=" + moistureContents +
                ", volume=" + volume +
                ", warehouse=" + warehouse +
                ", productLevel=" + productLevel +
                ", newOldLogos=" + newOldLogos +
                ", productOrganization=" + productOrganization +
                ", warehouseBatch='" + warehouseBatch + '\'' +
                ", batchEncoding='" + batchEncoding + '\'' +
                ", warehouseLocation=" + warehouseLocation +
                ", productionSchedulingItem=" + productionSchedulingItem +
                ", memo='" + memo + '\'' +
                '}';
    }
}
