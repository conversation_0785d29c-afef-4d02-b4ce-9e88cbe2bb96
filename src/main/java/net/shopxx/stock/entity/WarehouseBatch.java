package net.shopxx.stock.entity;


import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.member.entity.StoreMember;

/**
 * Entity - 仓库批次
 */
@Entity
@Table(name = "xx_warehouse_batch")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_warehouse_batch_sequence")
public class WarehouseBatch extends BaseEntity {
	
	private static final long serialVersionUID = 3306189781518640912L;
	
	private String sn; //作业单号
	
	private String batchEncoding;                //批次编码
	
	private SystemDict productionPlant;    //生产工厂
		
	private String productionNo;       //生产单号
	
	private Date productionDate;     //制单日期
	
	private Date completionDate;     //生产完成日期
			
	private Date effectiveDate;    //有效日期
	
	private Boolean batchStatus;    //批次状态

	private SystemDict categoryOfGoods;   //货物类别
	
	private Integer modifications;    //修改次数
	
	/**经营组织*/
	private Organization organization;
	
	/** 操作人 */
	private StoreMember storeMember;
	
	/** 备注 */
	private String memo;

	public String getBatchEncoding() {
		return batchEncoding;
	}

	public void setBatchEncoding(String batchEncoding) {
		this.batchEncoding = batchEncoding;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getProductionPlant() {
		return productionPlant;
	}

	public void setProductionPlant(SystemDict productionPlant) {
		this.productionPlant = productionPlant;
	}

	public String getProductionNo() {
		return productionNo;
	}

	public void setProductionNo(String productionNo) {
		this.productionNo = productionNo;
	}

	public Date getProductionDate() {
		return productionDate;
	}

	public void setProductionDate(Date productionDate) {
		this.productionDate = productionDate;
	}

	public Date getCompletionDate() {
		return completionDate;
	}

	public void setCompletionDate(Date completionDate) {
		this.completionDate = completionDate;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public Boolean getBatchStatus() {
		return batchStatus;
	}

	public void setBatchStatus(Boolean batchStatus) {
		this.batchStatus = batchStatus;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getCategoryOfGoods() {
		return categoryOfGoods;
	}

	public void setCategoryOfGoods(SystemDict categoryOfGoods) {
		this.categoryOfGoods = categoryOfGoods;
	}

	public Integer getModifications() {
		return modifications;
	}

	public void setModifications(Integer modifications) {
		this.modifications = modifications;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
	
	
}
