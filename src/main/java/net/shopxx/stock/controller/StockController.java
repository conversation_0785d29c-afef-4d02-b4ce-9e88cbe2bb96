package net.shopxx.stock.controller;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.intf.NatureOnHandQty;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.service.StockService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.util.CommonUtil;

/**
 * 库存
 */
@Controller("stockStockController")
@RequestMapping("/stock/stock")
public class StockController extends BaseController {

	@Resource(name = "stockServiceImpl")
	private StockService stockService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Integer flag, Pageable pageable, ModelMap model) {
		model.addAttribute("flag", flag);
		model.addAttribute("code", code);
		// 经营组织
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> managementOrganizations = organizationService.findList(null, filters, null);
		model.addAttribute("managementOrganizations", managementOrganizations);

		// 库存组织
		filters.clear();
		filters.add(Filter.eq("code", "stockWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> stockSystemDicts = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("stockSystemDicts", stockSystemDicts);
		return CommonUtil.getFolderPrefix(code) + "/stock/stock/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Integer flag, Pageable pageable, ModelMap model) {
		model.addAttribute("flag", flag);
		// 经营组织
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> managementOrganizations = organizationService.findList(null, filters, null);
		model.addAttribute("managementOrganizations", managementOrganizations);

		// 库存组织
		filters.clear();
		filters.add(Filter.eq("code", "stockWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> stockSystemDicts = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("stockSystemDicts", stockSystemDicts);
		return "/stock/stock/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/stockInfolist/{code}", method = RequestMethod.GET)
	public String stockInfolist(@PathVariable String code, Integer flag, Pageable pageable, ModelMap model) {
		model.addAttribute("flag", flag);
		// 经营组织
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> managementOrganizations = organizationService.findList(null, filters, null);
		model.addAttribute("managementOrganizations", managementOrganizations);

		// 库存组织
		filters.clear();
		filters.add(Filter.eq("code", "stockWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> stockSystemDicts = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("stockSystemDicts", stockSystemDicts);
		model.addAttribute("code", code);

		StoreMember storeMember = storeMemberService.getCurrent();
		// 默认外部用户
		Integer isMember = 1;
		if (storeMember.getMemberType() == 0) {
			isMember = 0;
		}
		model.addAttribute("isMember", isMember);
		return CommonUtil.getFolderPrefix(code) + "/stock/stock/stockInfolist";
	}

	/**
	 * 木种/花色查询列表
	 * 
	 * @param code
	 * @param flag
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/woodOrColorlist/{code}", method = RequestMethod.GET)
	public String woodOrColorlist(@PathVariable String code, Integer flag, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("code", code);

		// 产品级别
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null, filters, null);
		model.addAttribute("productLevelList", productLevelList);

		// 用户经营组织
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,
				filters, null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);

		// 色号
		filters.clear();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 新旧标识
		filters.clear();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("code", "oldNewLogo"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
		model.addAttribute("newOldLogosList", newOldLogosList);

		return CommonUtil.getFolderPrefix(code) + "/stock/stock/woodOrColorlist";
	}

	/**
	 * 木种/花色查询
	 * 
	 * @param ids
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/woodOrColorlist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg findwoodOrColorlist(Pageable pageable, Long[] warehouseId, Long[] organizationId,
			Long[] productId, Long[] productLevelId, Long[] colorNumbersId, Long[] moistureContentId,
			Long[] newOldLogosIds, Long[] warehouseBatchId) throws Exception {

		Page<Map<String, Object>> page = stockService.findPage(pageable, warehouseId, organizationId, productId,
				productLevelId, colorNumbersId, moistureContentId, newOldLogosIds, warehouseBatchId);

		return success(JsonUtils.toJson(page));
	}

	/**
	 * 统计木种/花色数量
	 * 
	 * @param warehouseId
	 * @param itemCode
	 * @param itemGrade
	 * @param colourNumber
	 * @param moistureContent
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/to_condition_woodOrColorCount", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionWoodOrColorCount(Pageable pageable, Long[] warehouseId,
			Long[] organizationId, Long[] productId, Long[] productLevelId, Long[] colorNumbersId,
			Long[] moistureContentId, Long[] newOldLogosIds, Long[] warehouseBatchId) throws Exception {

		Page<Map<String, Object>> page = stockService.findPage(pageable, warehouseId, organizationId, productId,
				productLevelId, colorNumbersId, moistureContentId, newOldLogosIds, warehouseBatchId);

		long count = page.getTotal();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (count == 0) {
				map.put("data", "0-0");
			} else if ((count - total) <= page_size) {
				map.put("data", (total + 1) + "-" + count);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((count - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 根据条件导出木种/花色信息
	 * 
	 * @param warehouseId
	 * @param itemCode
	 * @param itemGrade
	 * @param pageable
	 * @param colourNumber
	 * @param moistureContent
	 * @return
	 * @throws Exception
	 */

	@RequestMapping(value = "/condition_woodOrColorList", method = RequestMethod.GET)
	public ModelAndView conditionWoodOrColorList(Pageable pageable, Long[] warehouseId, Long[] organizationId,
			Long[] productId, Long[] productLevelId, Long[] colorNumbersId, Long[] moistureContentId,
			Long[] newOldLogosIds, Long[] warehouseBatchId, Integer page, ModelMap model) throws Exception {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		pageable.setPageSize(size);
		pageable.setPageNumber(page);
		Page<Map<String, Object>> pageMap = stockService.findPage(pageable, warehouseId, organizationId, productId,
				productLevelId, colorNumbersId, moistureContentId, newOldLogosIds, warehouseBatchId);

		List<Map<String, Object>> mapList = pageMap.getContent();

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "物料编码", "产品名称", "产品描述", "木种/花色", "产品级别", "型号", "规格", "单位",  "仓库名称", "仓库代码", "现有量（箱）",
				"现有量（支）", "现有量平方数", "可处理量（箱）", "可处理量（支）", "可处理量平方数", "经营组织", "库存组织", "色号", "含水率", "新旧标识", "批次编码",
				"作业单号", "完工日期", "批次备注", "库位" };

		// 设置单元格取值
		String[] properties = { "itemCode", "product_name",	"detailDescription", "woodTypeOrColor", "levelName", "model",
				"spec", "unit", "subinvName", "subinvCode", "onhandQuantity2", "onhandQuantity3", "onhandQuantity1",
				"attQuantity2", "attQuantity3", "attQuantity1", "product_organization_name", "stock_name", "color_numbers_name",
				"moisture_content_name", "new_old_logos_name", "batch_encoding", "warehouse_batch_sn", "completion_date", 
				"batch_memo", "warehouseLocationCode" };

		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, mapList, null), model);
	}

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Pageable pageable,Long menuId, ModelMap model, Long flag) {
		model.addAttribute("flag", flag);
		model.addAttribute("code", code);
        if (!ConvertUtil.isEmpty(menuId)){
            String jumpPath = menuJumpUtils.getMenuJumpPath(menuId);
            if (ConvertUtil.isEmpty(jumpPath)){
                throw new RuntimeException("请完成模板或路径配置！");
            }
            model.addAttribute("jumpPath", jumpPath);
        }
		return CommonUtil.getFolderPrefix(code) + "/stock/stock/list_tb";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Long[] productId, Long[] warehouseId, Integer isShowZero,
			String vonderCode, ModelMap model, Pageable pageable) {

		String jsonPage = JsonUtils
				.toJson(stockService.findPage(productId, warehouseId, isShowZero, vonderCode, pageable));

		return success(jsonPage);

	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(Long[] warehouseId, Long[] productId,
			Integer isShowZero, String vonderCode, Pageable pageable, ModelMap model) {

		Integer size = stockService.count(warehouseId, productId, isShowZero, vonderCode, pageable, model, null, null);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(Long[] warehouseId, Long[] productId, Integer isShowZero, String vonderCode,
			Pageable pageable, ModelMap model, Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = stockService.findItemList(warehouseId, productId, isShowZero, null, vonderCode,
				page, size);
		return this.getModelAndView(data, model);
	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = stockService.findItemList(null, null, null, ids, null, null, null);
		return getModelAndView(data, model);
	}

	public ModelAndView getModelAndView(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("actual_stock") != null) {
				BigDecimal actual_stock = new BigDecimal(str.get("actual_stock").toString());
				str.put("actual_stock", NumberFormat.getInstance().format(actual_stock));
			}
			if (str.get("useable_stock") != null) {
				BigDecimal useable_stock = new BigDecimal(str.get("useable_stock").toString());
				str.put("useable_stock", NumberFormat.getInstance().format(useable_stock));
			}
			if (str.get("lock_stock") != null) {
				BigDecimal lock_stock = new BigDecimal(str.get("lock_stock").toString());
				str.put("lock_stock", NumberFormat.getInstance().format(lock_stock));
			}
			// if (str.get("on_way_stock") != null) {
			// BigDecimal on_way_stock = new BigDecimal(
			// str.get("on_way_stock").toString());
			// str.put("on_way_stock", on_way_stock.stripTrailingZeros());
			// }
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "仓库名称", "仓库编码", "产品名称", "产品型号", "产品编码", "实际库存", "可售库存", "已锁库存" };
		// 设置单元格取值
		String[] properties = { "warehouse_name", "warehouse_sn", "product_name", "model", "vonder_code",
				"actual_stock", "useable_stock", "lock_stock" };

		return new ModelAndView(new ExcelView(filename, null, properties, header, null, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	/**
	 * 查询现有库存
	 * 
	 * @param ids
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/stockInfolist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg findOnHandQtyIntf(String organizationId, Long[] warehouseId, String[] itemCode,
			String itemGrade, String model, String woodTypeOrColor, Pageable pageable) throws Exception {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Map<String, String>> stockList = null;
		List<Map<String, String>> newstockList = new ArrayList<Map<String, String>>();
		List<Map<String, String>> productList = new ArrayList<Map<String, String>>();
		String jsonPage = null;

		if ("nature".equals(companyInfo.getCompany_code())) {

			// 大自然查询现有库存
			HashMap<String, Object> params = null;

			String itemGradeStr = "";
			String organizationIdStr = "";
			if (organizationId != null) {
				organizationIdStr = organizationId;
			} else {
				return error("库存组织为空");
			}
			if (itemGrade != null) {
				if (itemGrade.equals("无等级")) {
					itemGradeStr = "";
				} else {
					itemGradeStr = itemGrade;
				}
			}
			params = new HashMap<String, Object>();
			params.put("ORGANIZATION_ID", organizationIdStr);
			params.put("itemCode", itemCode);
			params.put("ITEM_RANK", itemGradeStr);
			List<String> warehouseList = new ArrayList<String>();
			if (warehouseId != null && warehouseId.length > 0) {
				for (int i = 0; i < warehouseId.length; i++) {
					if (warehouseId[i] != null) {
						Warehouse warehouse = warehouseBaseService.find(warehouseId[i]);
						if (warehouse != null) {
							warehouseList.add(warehouse.getErp_warehouse_code());

						}

					}
				}
			} else {
				return error("仓库编号为空");
			}
			params.put("warehouseList", warehouseList);
			stockList = new NatureOnHandQty().onHandQty(params);

			if (stockList != null) {
				newstockList.addAll(stockList);
			}
		}

		if (newstockList != null && newstockList.size() > 0) {
			HashSet h = new HashSet(newstockList);
			newstockList.clear();
			newstockList.addAll(h);
			Map<String, String> map = null;
			List<Product> products = null;
			List<Warehouse> wareHouses = null;
			List<Filter> filters = new ArrayList<Filter>();
			for (int i = 0; i < newstockList.size(); i++) {
				map = newstockList.get(i);
				BigDecimal onhandQuantity1 = map.get("onhandQuantity") == null || map.get("onhandQuantity").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("onhandQuantity").toString());// 数量
				BigDecimal onhandQuantity2 = map.get("onhandQuantity1") == null || map.get("onhandQuantity1").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("onhandQuantity1").toString());// 箱
				BigDecimal onhandQuantity3 = map.get("onhandQuantity2") == null || map.get("onhandQuantity2").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("onhandQuantity2").toString());// 支
				BigDecimal attQuantity1 = map.get("attQuantity") == null || map.get("attQuantity").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("attQuantity").toString());// 数量
				BigDecimal attQuantity2 = map.get("attQuantity1") == null || map.get("attQuantity1").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("attQuantity1").toString());// 箱
				BigDecimal attQuantity3 = map.get("attQuantity2") == null || map.get("attQuantity2").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("attQuantity2").toString());// 支
				String itemRank = map.get("itemRank").toString();

				map.put("onhandQuantity1", onhandQuantity1.toPlainString());// 数量
				map.put("onhandQuantity2", onhandQuantity2.toPlainString());// 箱
				map.put("onhandQuantity3", onhandQuantity3.toPlainString());// 支
				map.put("attQuantity1", attQuantity1.toPlainString());// 数量
				map.put("attQuantity2", attQuantity2.toPlainString());// 箱
				map.put("attQuantity3", attQuantity3.toPlainString());// 支
				map.put("itemRank", itemRank);

				filters.clear();
				filters.add(Filter.eq("erp_warehouse_code", map.get("subinvCode")));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				wareHouses = warehouseBaseService.findList(null, filters, null);
				Warehouse w = null;
				if (wareHouses != null) {
					if (wareHouses.size() > 0) {
						w = wareHouses.get(0);
						map.put("isDisplay", w.getIsDisplay().toString());
					}

				}

				if (map.get("itemCode") != null) {
					filters.clear();
					filters.add(Filter.eq("vonderCode", map.get("itemCode").toString()));
					filters.add(Filter.eq("companyInfoId", companyInfoId));
					filters.add(Filter.eq("isMarketable", 1));
					products = productBaseService.findList(null, filters, null);
					Product p = null;
					if (products != null) {
						if (products.size() > 0) {
							p = products.get(0);
							map.put("unit", p.getUnit());
							map.put("woodTypeOrColor", p.getWoodTypeOrColor() == null ? "" : p.getWoodTypeOrColor());
							map.put("model", p.getModel() == null ? "" : p.getModel());
							map.put("fullName", p.getFullName() == null ? "" : p.getFullName());
							map.put("detailDescription",
									p.getDetailDescription() == null ? "" : p.getDetailDescription());
							map.put("spec", p.getSpec() == null ? "" : p.getSpec());
						}
					} else {
						map.put("woodTypeOrColor", "");
						map.put("model", "");
						map.put("fullName", "");
						map.put("detailDescription", "");
						map.put("spec", "");
					}

				}
				productList.add(map);
			}
			jsonPage = JsonUtils.toJson(productList);
		} else {
			newstockList = new ArrayList<Map<String, String>>();
			jsonPage = JsonUtils.toJson(newstockList);
			LogUtils.info("==============返回没有数据=================");
		}

		return success(jsonPage);
	}

	@RequestMapping(value = "/to_condition_exportInfo", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExportInfo(String organizationId, Long[] warehouseId,
			String itemCode, String itemGrade, Pageable pageable, ModelMap model) throws Exception {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Map<String, String>> stockList = null;
		List<Map<String, String>> newstockList = new ArrayList<Map<String, String>>();
		List<Map<String, String>> productList = new ArrayList<Map<String, String>>();
		if ("nature".equals(companyInfo.getCompany_code())) {

			// 大自然查询现有库存
			HashMap<String, Object> params = null;
			String organizationIdStr = "";
			String itemCodeStr = "";
			String itemGradeStr = "";
			if (organizationId != null) {
				organizationIdStr = organizationId;
			}
			if (itemCode != null) {
				itemCodeStr = itemCode;
			}
			if (itemGrade != null) {
				itemGradeStr = itemGrade;
			}
			params = new HashMap<String, Object>();
			params.put("ORGANIZATION_ID", organizationIdStr);
			params.put("ITEM_CODE", itemCodeStr);
			params.put("ITEM_RANK", itemGradeStr);
			List<String> warehouseList = new ArrayList<String>();
			if (warehouseId != null && warehouseId.length > 0) {
				for (int i = 0; i < warehouseId.length; i++) {
					if (warehouseId[i] != null) {
						Warehouse warehouse = warehouseBaseService.find(warehouseId[i]);
						if (warehouse != null) {
							warehouseList.add(warehouse.getErp_warehouse_code());
						}
					}
				}
			}
			params.put("warehouseList", warehouseList);
			stockList = new NatureOnHandQty().onHandQty(params);
			if (stockList != null) {
				newstockList.addAll(stockList);
			}
		}

		// 产品级别
		List<Filter> filtersLevel = new ArrayList<Filter>();
		SystemDict productLevel = null;
		if (newstockList != null && newstockList.size() > 0) {
			HashSet h = new HashSet(newstockList);
			newstockList.clear();
			newstockList.addAll(h);
			Map<String, String> map = null;
			Product product = null;
			List<Filter> filters = new ArrayList<Filter>();
			for (int i = 0; i < newstockList.size(); i++) {
				map = newstockList.get(i);
				if (map.get("itemCode") != null) {
					filters.clear();
					filters.add(Filter.eq("vonderCode", map.get("itemCode").toString()));
					filters.add(Filter.eq("companyInfoId", companyInfoId));
					if (map.get("itemRank") != null && map.get("itemRank") != "") {
						filtersLevel.clear();
						filtersLevel.add(Filter.eq("code", "productLevel"));
						filtersLevel.add(Filter.isNotNull("parent"));
						filtersLevel.add(Filter.eq("value", map.get("itemRank").trim()));
						productLevel = systemDictService.find(filtersLevel);
						if (productLevel != null) {
							filters.add(Filter.eq("productLevel", productLevel.getId()));
						} else {
							ExceptionUtil.throwServiceException("请设置产品等级：" + map.get("itemRank").trim());
						}

					} else {
						filtersLevel.clear();
						filtersLevel.add(Filter.eq("code", "productLevel"));
						filtersLevel.add(Filter.isNotNull("parent"));
						filtersLevel.add(Filter.eq("value", "无等级"));
						productLevel = systemDictService.find(filtersLevel);
						if (productLevel != null) {
							filters.add(Filter.eq("productLevel", productLevel.getId()));
						} else {
							ExceptionUtil.throwServiceException("请设置产品等级:无等级");
						}
					}
					product = productBaseService.find(filters);

					if (product != null) {
						productList.add(map);
					}

				}

			}
		}
		int size = productList.size();
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @param saleOrgName
	 * @param storeName
	 * @param pmodel
	 * @param vonderCode
	 * @param firstTime
	 * @param lastTime
	 * @param pageable
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/condition_exportInfo", method = RequestMethod.GET)
	public ModelAndView condition_exportInfo(String organizationId, Long[] warehouseId, String itemCode,
			String itemGrade, Integer page, Pageable pageable, ModelMap model) throws Exception {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Map<String, String>> stockList = null;
		List<Map<String, String>> newstockList = new ArrayList<Map<String, String>>();
		List<Map<String, String>> productList = new ArrayList<Map<String, String>>();
		if ("nature".equals(companyInfo.getCompany_code())) {

			// 大自然查询现有库存
			HashMap<String, Object> params = null;
			String organizationIdStr = "";
			String itemCodeStr = "";
			String itemGradeStr = "";
			if (organizationId != null) {
				organizationIdStr = organizationId;
			}
			if (itemCode != null) {
				itemCodeStr = itemCode;
			}
			if (itemGrade != null) {
				itemGradeStr = itemGrade;
			}
			params = new HashMap<String, Object>();
			params.put("ORGANIZATION_ID", organizationIdStr);
			params.put("ITEM_CODE", itemCodeStr);
			params.put("ITEM_RANK", itemGradeStr);
			List<String> warehouseList = new ArrayList<String>();
			if (warehouseId != null && warehouseId.length > 0) {
				for (int i = 0; i < warehouseId.length; i++) {
					if (warehouseId[i] != null) {
						Warehouse warehouse = warehouseBaseService.find(warehouseId[i]);
						if (warehouse != null) {
							warehouseList.add(warehouse.getErp_warehouse_code());
						}
					}
				}
			}
			params.put("warehouseList", warehouseList);
			stockList = new NatureOnHandQty().onHandQty(params);
			if (stockList != null) {
				newstockList.addAll(stockList);
			}
		}

		if (newstockList != null && newstockList.size() > 0) {
			HashSet h = new HashSet(newstockList);
			newstockList.clear();
			newstockList.addAll(h);
			Map<String, String> map = null;
			Product product = null;
			List<Filter> filters = new ArrayList<Filter>();

			// 产品级别
			List<Filter> filtersLevel = new ArrayList<Filter>();
			SystemDict productLevel = null;
			for (int i = 0; i < newstockList.size(); i++) {
				map = newstockList.get(i);
				if (map.get("itemCode") != null) {
					filters.clear();
					filters.add(Filter.eq("vonderCode", map.get("itemCode").toString()));
					filters.add(Filter.eq("companyInfoId", companyInfoId));
					if (map.get("itemRank") != null && map.get("itemRank") != "") {
						filtersLevel.clear();
						filtersLevel.add(Filter.eq("code", "productLevel"));
						filtersLevel.add(Filter.isNotNull("parent"));
						filtersLevel.add(Filter.eq("value", map.get("itemRank").trim()));
						productLevel = systemDictService.find(filtersLevel);
						if (productLevel != null) {
							filters.add(Filter.eq("productLevel", productLevel.getId()));
						} else {
							ExceptionUtil.throwServiceException("请设置产品等级：" + map.get("itemRank").trim());
						}

					} else {
						filtersLevel.clear();
						filtersLevel.add(Filter.eq("code", "productLevel"));
						filtersLevel.add(Filter.isNotNull("parent"));
						filtersLevel.add(Filter.eq("value", "无等级"));
						productLevel = systemDictService.find(filtersLevel);
						if (productLevel != null) {
							filters.add(Filter.eq("productLevel", productLevel.getId()));
						} else {
							ExceptionUtil.throwServiceException("请设置产品等级:无等级");
						}
					}

					product = productBaseService.find(filters);

					if (product != null) {
						map.put("woodTypeOrColor",
								product.getWoodTypeOrColor() == null ? "" : product.getWoodTypeOrColor());
						map.put("model", product.getModel() == null ? "" : product.getModel());
						map.put("fullName", product.getFullName() == null ? "" : product.getFullName());
						map.put("detailDescription",
								product.getDetailDescription() == null ? "" : product.getDetailDescription());
						map.put("spec", product.getSpec() == null ? "" : product.getSpec());
						if (map.get("orgId") != null) {
							if (Integer.valueOf(map.get("orgId")) == 107) {
								map.put("org", "大自然家居（中国）有限公司");
							} else if (Integer.valueOf(map.get("orgId")) == 381) {
								map.put("org", "大自然家居(中国)地板");
							}

						}
						if (map.get("organizationId") != null) {
							if (Integer.valueOf(map.get("organizationId")) == 125) {
								map.put("organization", "大自然家居（中国）有限公司");
							} else if (Integer.valueOf(map.get("organizationId")) == 381) {
								map.put("organization", "广西柏景地板有限公司");
							}

						}
						BigDecimal onhandQuantity2 = new BigDecimal(map.get("onhandQuantity2").toString());
						BigDecimal attQuantity2 = new BigDecimal(map.get("attQuantity2").toString());
						BigDecimal onhandQuantity = product.getPerBranch().multiply(onhandQuantity2).setScale(6,
								BigDecimal.ROUND_HALF_UP);
						BigDecimal attQuantity = product.getPerBranch().multiply(attQuantity2).setScale(6,
								BigDecimal.ROUND_HALF_UP);
						map.put("onhandQ", onhandQuantity.toPlainString());
						map.put("attQ", attQuantity.toPlainString());
						productList.add(map);
					}
				}

			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "名称", "木种/花色", "等级", "型号", "规格", "物料编码", "产品描述", "仓库名称", "仓库代码", "现有量（箱）", "现有量（支）",
				"现有量平方数", "可处理量（箱）", "可处理量（支）", "可处理量平方数", "经营组织", "库存组织" };
		// 设置单元格取值
		String[] properties = { "fullName", "woodTypeOrColor", "itemRank", "model", "spec", "itemCode",
				"detailDescription", "subinvName", "subinvCode", "onhandQuantity1", "onhandQuantity2", "onhandQ",
				"attQuantity1", "attQuantity2", "attQ", "org", "organization" };

		return new ModelAndView(new ExcelView(filename, null, properties, header, null, null, productList, null),
				model);
	}

	/**
	 * 订单查询视图表库存数量、可用数量
	 * 
	 * @param productGrades
	 * @param vonderCodes
	 * @param warehouseId
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/findViewStock", method = RequestMethod.POST)
	public @ResponseBody ResultMsg findViewStock(Long warehouseId, Long organizationId, Long productId,
			Long productGrade, String vonderCode, String colourNumber, String moistureContent, Long[] batchIds,
			String newOldLogos, Long warehouseLocationId) {

		// 仓库
		if (ConvertUtil.isEmpty(warehouseId)) {
			return success(JsonUtils.toJson(new ArrayList<Map<String, Object>>()));
		}
		// 产品
		if (ConvertUtil.isEmpty(productId)) {
			return success(JsonUtils.toJson(new ArrayList<Map<String, Object>>()));
		}
		// 等级
		if (ConvertUtil.isEmpty(productGrade)) {
			return success(JsonUtils.toJson(new ArrayList<Map<String, Object>>()));
		}
		// 经营组织
		if (ConvertUtil.isEmpty(organizationId)) {
			return success(JsonUtils.toJson(new ArrayList<Map<String, Object>>()));
		}

		String jsonPage = "";
		List<Map<String, Object>> mapList = stockService.findViewStock(warehouseId, organizationId, productId,
				productGrade, vonderCode, colourNumber, moistureContent, batchIds, newOldLogos, warehouseLocationId,
				null);
		if (!mapList.isEmpty() && mapList.size() > 0) {
			jsonPage = JsonUtils.toJson(mapList);
		} else {
			jsonPage = JsonUtils.toJson(new ArrayList<Map<String, Object>>());
		}

		return success(jsonPage);
	}

	/**
	 * 订单查询现有库存
	 * 
	 * @param ids
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/orderStockInfolist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg findOrderOnHandQtyIntf(String orderItemIds, String organizationId,
			String warehouseId) throws Exception {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Map<String, String>> stockList = null;
		List<Map<String, String>> newstockList = new ArrayList<Map<String, String>>();
		List<Map<String, String>> productList = new ArrayList<Map<String, String>>();
		String jsonPage = null;

		Map<String, String> map = null;
		String[] result = orderItemIds.split(",");

		List<Map<String, Object>> paramsList = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < result.length; i++) {
			Map<String, Object> params = new HashMap<String, Object>();
			String orderItemId = result[i];
			OrderItem orderItem = orderItemService.find(Long.parseLong(orderItemId));
			String itemGrade = null;
			if (orderItem.getProductLevel() != null) {
				if (orderItem.getProductLevel().getValue().equals("无等级")) {
					itemGrade = "";
				} else {
					itemGrade = orderItem.getProductLevel().getValue();
				}
			} else {
				itemGrade = "";
			}
			if ("nature".equals(companyInfo.getCompany_code())) {

				// 大自然查询现有库存

				String organizationIdStr = "";
				String itemGradeStr = "";
				if (organizationId != null) {
					organizationIdStr = organizationId;
				} else {
					return error("库存组织为空");
				}
				if (itemGrade != null) {
					if (itemGrade.equals("无等级")) {
						itemGradeStr = "";
					} else {
						itemGradeStr = itemGrade;
					}
				}

				params.put("ORGANIZATION_ID", organizationIdStr);
				params.put("itemCode", orderItem.getVonderCode());
				params.put("ITEM_RANK", itemGradeStr);
				List<String> warehouseList = new ArrayList<String>();

				Warehouse warehouse = warehouseBaseService.find(Long.parseLong(warehouseId));

				params.put("warehouseId", warehouse.getErp_warehouse_code());
				paramsList.add(params);
			}
		}

		stockList = new NatureOnHandQty().onOrderHandQty(paramsList);

		if (stockList != null) {
			newstockList.addAll(stockList);
		}

		if (newstockList != null && newstockList.size() > 0) {
			HashSet h = new HashSet(newstockList);
			newstockList.clear();
			newstockList.addAll(h);

			List<Product> products = null;
			List<Warehouse> wareHouses = null;
			List<Filter> filters = new ArrayList<Filter>();
			for (int i = 0; i < newstockList.size(); i++) {
				map = newstockList.get(i);
				BigDecimal onhandQuantity1 = map.get("onhandQuantity") == null || map.get("onhandQuantity").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("onhandQuantity").toString());// 数量
				BigDecimal onhandQuantity2 = map.get("onhandQuantity1") == null || map.get("onhandQuantity1").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("onhandQuantity1").toString());// 箱
				BigDecimal onhandQuantity3 = map.get("onhandQuantity2") == null || map.get("onhandQuantity2").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("onhandQuantity2").toString());// 支
				BigDecimal attQuantity1 = map.get("attQuantity") == null || map.get("attQuantity").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("attQuantity").toString());// 数量
				BigDecimal attQuantity2 = map.get("attQuantity1") == null || map.get("attQuantity1").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("attQuantity1").toString());// 箱
				BigDecimal attQuantity3 = map.get("attQuantity2") == null || map.get("attQuantity2").equals("")
						? BigDecimal.ZERO
						: new BigDecimal(map.get("attQuantity2").toString());// 支
				String itemRank = map.get("itemRank").toString();

				map.put("onhandQuantity1", onhandQuantity1.toPlainString());// 数量
				map.put("onhandQuantity2", onhandQuantity2.toPlainString());// 箱
				map.put("onhandQuantity3", onhandQuantity3.toPlainString());// 支
				map.put("attQuantity1", attQuantity1.toPlainString());// 数量
				map.put("attQuantity2", attQuantity2.toPlainString());// 箱
				map.put("attQuantity3", attQuantity3.toPlainString());// 支
				map.put("itemRank", itemRank);

				filters.clear();
				filters.add(Filter.eq("erp_warehouse_code", map.get("subinvCode")));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				wareHouses = warehouseBaseService.findList(null, filters, null);
				Warehouse w = null;
				if (wareHouses != null) {
					if (wareHouses.size() > 0) {
						w = wareHouses.get(0);
						if (w != null && w.getIsDisplay()) {
							map.put("isDisplay", w.getIsDisplay().toString());
						}
					}

				}

				if (map.get("itemCode") != null) {
					filters.clear();
					filters.add(Filter.eq("vonderCode", map.get("itemCode").toString()));
					filters.add(Filter.eq("companyInfoId", companyInfoId));
					filters.add(Filter.eq("isMarketable", 1));
					products = productBaseService.findList(null, filters, null);
					Product p = null;
					if (products != null) {
						if (products.size() > 0) {
							p = products.get(0);
							map.put("unit", p.getUnit());
							map.put("productId", p.getId().toString());
							map.put("woodTypeOrColor", p.getWoodTypeOrColor() == null ? "" : p.getWoodTypeOrColor());
							map.put("model", p.getModel() == null ? "" : p.getModel());
							map.put("fullName", p.getFullName() == null ? "" : p.getFullName());
							map.put("detailDescription",
									p.getDetailDescription() == null ? "" : p.getDetailDescription());
							map.put("spec", p.getSpec() == null ? "" : p.getSpec());
						}
					} else {
						map.put("woodTypeOrColor", "");
						map.put("model", "");
						map.put("fullName", "");
						map.put("detailDescription", "");
						map.put("spec", "");
					}

				}
				productList.add(map);
			}
			jsonPage = JsonUtils.toJson(productList);
		} else {
			newstockList = new ArrayList<Map<String, String>>();
			jsonPage = JsonUtils.toJson(newstockList);
			LogUtils.info("==============返回没有数据=================");
		}
		return success(jsonPage);
	}

}
