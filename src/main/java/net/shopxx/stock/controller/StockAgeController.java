package net.shopxx.stock.controller;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.stock.service.StockAgeService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller("stockAgeController")
@RequestMapping("/stock/stock_age")
public class StockAgeController extends BaseController {
	
	
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "stockAgeServiceImpl")
	private StockAgeService stockAgeService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String batch_list_tb(ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/stock/stock_age/list_tb";
	}
	
	
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/stock/stock_age/list";
	}
	
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}
	
	
	@RequestMapping(value = "/toStockAgeReportExport", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toStockAgeReportExport(String startTime, String endTime, Long[] saleOrgId,
			Long[] storeId, Long[] organizationId, Long[] warehouseId, Long[] productCategoryId, Long[] vProductId,
			Long[] productId, String woodTypeOrColor, String model, Long[] ids) {
		
		Integer size = stockAgeService.findStockAgeListCount(startTime, endTime, saleOrgId, storeId, organizationId, warehouseId, productCategoryId, vProductId, vProductId, woodTypeOrColor,null, ids);		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	
	/**
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param warehouseId
	 * @param productCategoryId
	 * @param vProductId
	 * @param productId
	 * @param woodTypeOrColor
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String startTime, String endTime, Long[] saleOrgId,
			Long[] storeId, Long[] organizationId, Long[] warehouseId, Long[] productCategoryId, Long[] vProductId,
			Long[] productId, String woodTypeOrColor, String model,Pageable pageable) {
		
		Page<Map<String, Object>> page = stockAgeService.findStockAgeList(startTime, endTime, saleOrgId,
				storeId, organizationId, warehouseId, productCategoryId, 
				vProductId, productId, woodTypeOrColor, model, null, pageable);
		
		String jsonPage = JsonUtils.toJson(page);
		
		return success(jsonPage);
	}

}
