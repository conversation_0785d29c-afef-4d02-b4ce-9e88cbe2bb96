package net.shopxx.stock.controller;
import java.util.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonVariable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseStore;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseSaleOrgBaseService;
import net.shopxx.stock.service.WarehouseSmethodService;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.util.RoleJurisdictionUtil;

@Controller("warehouseController")
@RequestMapping("/stock/warehouse")
public class WarehouseController extends BaseController {

	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "warehouseSaleOrgBaseServiceImpl")
	private WarehouseSaleOrgBaseService warehouseSaleOrgBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "warehouseSmethodServiceImpl")
	private WarehouseSmethodService warehouseSmethodService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "orderFullLinkServiceImpl")
    private OrderFullLinkService orderFullLinkService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long objTypeId, Long objid,Integer isCheck,Integer flag,Long menuId,ModelMap model) {

		model.addAttribute("isCheck", isCheck);
		model.addAttribute("flag", flag);
        model.addAttribute("menuId", menuId);
        model.addAttribute("objTypeId", objTypeId);
        model.addAttribute("objid", objid);
		return "/stock/warehouse/list_tb";
	}

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb_code(@PathVariable String code, Integer isCheck,Long menuId,
			ModelMap model) {
		model.addAttribute("code", code);
		model.addAttribute("isCheck", isCheck);
		model.addAttribute("menuId", menuId);
		return "/" + code + "/stock/warehouse/list_tb";
	}
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Integer flag,Long userId,Long menuId, ModelMap model) {
		model.addAttribute("flag", flag);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/stock/warehouse/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code,Long userId,Long menuId, Pageable pageable,
			ModelMap model) {
		model.addAttribute("code", code);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/" + code + "/stock/warehouse/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn, String name, String erp_warehouse_code,
			Boolean isEnabled, Long supplierId, Integer type, Pageable pageable) {
		Page<Map<String, Object>> page = warehouseBaseService.findPage(sn,
				name,
				erp_warehouse_code,
				isEnabled,
				supplierId,
				type,
				null,
				null,
				null,
				null,
				pageable,null,null);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		List<Area> areas = areaBaseService.findRoots("ChinaCN");
		model.put("rootAreas", areas);

		List<Filter> filters = new ArrayList<Filter>();

		//sbu
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Map<String, Object>> sbuList = storeMemberService.findSbuTy(storeMember.getId());
		model.addAttribute("sbu_json", JsonUtils.toJson(sbuList));

		//用户经营组织
		List<Organization> organizationList = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("managementOrganizations",organizationList);

		//库存组织
		filters.clear();
		filters.add(Filter.eq("code", "stockWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> stockSystemDicts = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("stockSystemDicts", stockSystemDicts);

		
		// 工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "productionPlant"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productionPlantList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("productionPlantList", productionPlantList);

		//仓库类型
		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> typeSystemDicts = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("typeSystemDicts", typeSystemDicts);
		
		filters.clear();
		filters.add(Filter.eq("code", "shippingWay"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> shippingWays = systemDictService.findList(null,filters,null);
		
		model.addAttribute("shippingWays", shippingWays);

		return "/stock/warehouse/add";
	}		

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add(@PathVariable String code, ModelMap model) {

		List<Area> areas = areaBaseService.findRoots("ChinaCN");
		model.put("rootAreas", areas);

		List<Filter> filters = new ArrayList<Filter>();

		//经营组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> managementOrganizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("managementOrganizations", managementOrganizations);

		//库存组织
		filters.clear();
		filters.add(Filter.eq("code", "stockWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> stockSystemDicts = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("stockSystemDicts", stockSystemDicts);

		//工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "FactoryName"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> systemDicts = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("systemDicts", systemDicts);

		//仓库类型
		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> typeSystemDicts = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("typeSystemDicts", typeSystemDicts);
		model.addAttribute("code", code);

		return "/" + code + "/stock/warehouse/add";
	}
	
	

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(Warehouse warehouse, Long areaId, Long[] saleOrgId,
			Boolean[] isChecks,Long[] areaIds, Long stores, Long deliveryCorpId,
			Long productionPlantId, HttpServletRequest request) {

		if (areaIds == null) {
			//请选择地区
			return ResultMsg.error("请选择地区");
		}
		if(saleOrgId == null){
			return ResultMsg.error("请选择机构");
		}

		warehouseBaseService.saveWarehouse(warehouse,
				areaId,saleOrgId,isChecks,areaIds,stores,
				deliveryCorpId,productionPlantId,0);

		return success().addObjX(warehouse.getId());
	}

	/**
	 *编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {

		Warehouse warehouse = warehouseBaseService.find(id);
		model.put("warehouse", warehouse);
		model.put("areas", warehouse.getAreas());
		model.put("rootAreas", areaBaseService.findRoots("ChinaCN"));

		//SBU关联表		
		List<Map<String, Object>> sbuList = warehouseBaseService.findWarehouseSbu(warehouse);
		model.addAttribute("sbu_json", JsonUtils.toJson(sbuList));
		
		//仓库用户关联表		
		List<Map<String, Object>> warehouseStoreMemberList = warehouseBaseService.findWarehouseStoreMember(warehouse);
		model.addAttribute("storeMember_json", JsonUtils.toJson(warehouseStoreMemberList));
		

		//用户经营组织
		List<Organization> organizationList = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("managementOrganizations", organizationList);

		// 工厂 -系统词汇
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "productionPlant"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productionPlantList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("productionPlantList", productionPlantList);

		//库存组织
		filters.clear();
		filters.add(Filter.eq("code", "stockWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> stockSystemDicts = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("stockSystemDicts", stockSystemDicts);

		//仓库类型
		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> typeSystemDicts = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("typeSystemDicts", typeSystemDicts);

		//仓库组织
		List<Map<String, Object>> warehouseSaleOrgs = warehouseSaleOrgBaseService.findListByWarehouse(id);
		if (warehouseSaleOrgs != null && warehouseSaleOrgs.size() > 0) {
			String warehouseSaleOrgs_json = JsonUtils.toJson(warehouseSaleOrgs);
			model.addAttribute("warehouseSaleOrgs_json", warehouseSaleOrgs_json);
		}else {
			model.addAttribute("warehouseSaleOrgs_json", new ArrayList<Map<String, Object>>());
		}
		
		filters.clear();
		filters.add(Filter.eq("code", "shippingWay"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> shippingWays = systemDictService.findList(null,filters,null);
		
		model.addAttribute("shippingWays", shippingWays);
		
		List<Map<String, Object>>  list = warehouseSmethodService.findWarehouseSmethod(id);
		model.addAttribute("shippingMethod_json", JsonUtils.toJson(list));

		filters.clear();
		filters.add(Filter.eq("storeMember",
				storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);

		//仓库里的修改按钮角色权限控制  0 不展示 、非0 展示
        Integer warehouse_modify = roleJurisdictionUtil.getRoleCount("warehouse_modify");
        model.addAttribute("warehouse_modify", warehouse_modify);

		/* 全链路信息 */
        String warehouseFullLink_json = JsonUtils.toJson(orderFullLinkService.findListByOrderSn(warehouse.getSn()));
        model.addAttribute("warehouseFullLink_json", warehouseFullLink_json);

		return "/stock/warehouse/edit";
	}

	/**
	 *编辑
	 */
	@RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
	public String edit(@PathVariable String code, Long id, ModelMap model) {


		Warehouse warehouse = warehouseBaseService.find(id);
		model.put("warehouse", warehouse);
		model.put("areas", warehouse.getAreas());
		model.put("rootAreas", areaBaseService.findRoots("ChinaCN"));

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("warehouse", warehouse));
		List<WarehouseStore> warehouseStores = warehouseStoreBaseService.findList(1,
				filters,
				null);
		List<Store> stores = new ArrayList<Store>();
		for (WarehouseStore warehouseStore : warehouseStores) {
			stores.add(warehouseStore.getStore());
		}
		model.put("stores", stores);

		//经营组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> managementOrganizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("managementOrganizations", managementOrganizations);

		//工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "FactoryName"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> systemDicts = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("systemDicts", systemDicts);

		//库存组织
		filters.clear();
		filters.add(Filter.eq("code", "stockWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> stockSystemDicts = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("stockSystemDicts", stockSystemDicts);

		//仓库类型
		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> typeSystemDicts = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("typeSystemDicts", typeSystemDicts);

		//仓库组织
		List<Map<String, Object>> warehouseSaleOrgs = warehouseSaleOrgBaseService.findListByWarehouse(id);
		if (warehouseSaleOrgs != null && warehouseSaleOrgs.size() > 0) {
			String warehouseSaleOrgs_json = JsonUtils.toJson(warehouseSaleOrgs);
			model.addAttribute("warehouseSaleOrgs_json", warehouseSaleOrgs_json);
		}
		else {
			model.addAttribute("warehouseSaleOrgs_json",
					new ArrayList<Map<String, Object>>());
		}
		model.addAttribute(" code", code);

		return "/" + code + "/stock/warehouse/edit";
	}

	/**
	 *查看
	 */
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, ModelMap model) {

		Warehouse warehouse = warehouseBaseService.find(id);
		model.put("warehouse", warehouse);
		model.put("areas", warehouse.getAreas());
		model.put("rootAreas", areaBaseService.findRoots("ChinaCN"));

		return "/stock/warehouse/view";
	}

	/**
	 *查看
	 */
	@RequestMapping(value = "/view/{code}", method = RequestMethod.GET)
	public String view_code(@PathVariable String code, Long id, ModelMap model) {

		Warehouse warehouse = warehouseBaseService.find(id);
		model.put("warehouse", warehouse);
		model.put("areas", warehouse.getAreas());
		model.put("rootAreas", areaBaseService.findRoots("ChinaCN"));
		model.addAttribute("code", code);
		return "/" + code + "/stock/warehouse/view";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(Warehouse warehouse, Long areaId, Long[] saleOrgId,
			Boolean[] isChecks, Long[] areaIds, Long stores, Long deliveryCorpId,
			Long productionPlantId) {

		if (areaIds == null) {
			return error("请选择地区!");
		}

		//全链路信息
		List<String> warehouseSns = new ArrayList<String>();
		warehouseSns.add(warehouse.getSn());
		if(warehouse.getWarehouseStatus() == 0) {
		    warehouse.setIsEnabled(false);
            warehouseBaseService.updateWarehouse(warehouse,areaId,saleOrgId,
                    isChecks,areaIds,stores,deliveryCorpId,productionPlantId,0);
			orderFullLinkService.addFullLink(201, warehouseSns, warehouse.getSn(), "保存仓库信息", null);
		}else {
		    if(warehouse.getIsEnabled() && warehouse.getWarehouseStatus() == 1){
            warehouse.setIsEnabled(true);
		    }else {
                warehouse.setIsEnabled(false);
            }
            warehouseBaseService.updateWarehouse(warehouse,areaId,saleOrgId,
                    isChecks,areaIds,stores,deliveryCorpId,productionPlantId,1);
			orderFullLinkService.addFullLink(201, warehouseSns, warehouse.getSn(), "修改仓库信息", null);
		}
		return success();
	}


	
	/**
	 * 库位列表
	 * @param model
	 * @param warehouseId
	 * @return
	 */
	@RequestMapping(value = "select_storehouse", method = RequestMethod.GET)
	public String select_storehouse(Long warehouseId,ModelMap model) {
		//仓库id
		model.addAttribute("warehouseId", warehouseId);
				
		return "/stock/warehouse/select_storehouse";

	}

	@RequestMapping(value = "/selectLocationData", method = RequestMethod.GET)
	public @ResponseBody
	ResultMsg selectLocationData(String code, Long warehouseId,
								 String remarks) {

		//获取指定仓库
		net.shopxx.util.base.Page<Map<String, Object>> pageData = new net.shopxx.util.base.Page<Map<String,Object>>();
		//仓库信息
		if (!ConvertUtil.isEmpty(warehouseId)) {
			List<Map<String, Object>> warehouseLocations =  warehouseBaseService.findwarehouseLocationListByParam(code,warehouseId,remarks);
			pageData.setContent(warehouseLocations);
			pageData.setTotal(warehouseLocations.size());
		}else {
			//请选择参数
			return ResultMsg.error("仓库参数异常！");
		}
		String jsonPage = JsonUtils.toJson(pageData);
		return success(jsonPage);

	}


	/**
	 * 获取指定仓库的库位信息
	 * @param code
	 * @param warehouseId
	 * @param remarks
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "select_storehouse_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_storehouse_data(String code, Long warehouseId,
			String remarks,Pageable pageable) {
		
		//获取指定仓库
		net.shopxx.util.base.Page<Map<String, Object>> pageData = new net.shopxx.util.base.Page<Map<String,Object>>();
		//仓库信息
		if (!ConvertUtil.isEmpty(warehouseId)) {
			List<Map<String, Object>> warehouseLocations =  warehouseBaseService.findwarehouseLocationListByParam(code,warehouseId,remarks);
			pageData.setContent(warehouseLocations);
			pageData.setTotal(warehouseLocations.size());
		}else {
			//请选择地区
			return ResultMsg.error("仓库参数异常！");
		}		
		String jsonPage = JsonUtils.toJson(pageData);
		return success(jsonPage);
	}
	
	
	
	@RequestMapping(value = "getSBUInformation", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getSBUInformation(Long warehouseId) {
		if (warehouseId == null || warehouseId == 0) {
			return ResultMsg.error("参数不能为空！");
		}
		net.shopxx.util.base.Page<Map<String, Object>> pageData = new net.shopxx.util.base.Page<Map<String,Object>>();
		//获取指定仓库
		Warehouse warehouse = warehouseBaseService.find(warehouseId);
		//仓库信息
		if (!ConvertUtil.isEmpty(warehouse)) {
			List<Map<String, Object>> sbuList = warehouseBaseService.findWarehouseSbu(warehouse);
			pageData.setContent(sbuList);
			pageData.setTotal(sbuList.size());
		}else {
			//请选择地区
			return ResultMsg.error("仓库参数异常！");
		}		
		String jsonPage = JsonUtils.toJson(pageData);
		return success(jsonPage);
	}
	

	/**
	 * 列表
	 * @param model
	 * @param
	 * @return
	 */
	@RequestMapping(value = "select_warehouse", method = RequestMethod.GET)
	public String select_warehouse(Integer multi, Long supplierId,
			Integer type,Integer warehouseType, Long saleOrgId, Long organizationId,
			Long sbuId, ModelMap model,Long sign,Integer isNotwarehouseType) {

		model.addAttribute("multi", multi);
		model.addAttribute("supplierId", supplierId);
		model.addAttribute("type", type);
		model.addAttribute("warehouseType", warehouseType);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("sign", sign);
		model.addAttribute("isNotwarehouseType", isNotwarehouseType);
		
		return "/stock/warehouse/select_warehouse";

	}
				
	

	/**
	 * 列表
	 * @param model
	 * @param
	 * @return
	 */
	@RequestMapping(value = "select_infowarehouse", method = RequestMethod.GET)
	public String select_infowarehouse(Integer multi, Long supplierId,
			Integer type, Long saleOrgId, Long organizationId, ModelMap model) {

		model.addAttribute("multi", multi);
		model.addAttribute("supplierId", supplierId);
		model.addAttribute("type", type);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("organizationId", organizationId);
		return "/stock/warehouse/select_infowarehouse";

	}

	@RequestMapping(value = "select_warehouse_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_warehouse_data(String sn, String name,
			String erp_warehouse_code, Long supplierId, Integer type,Integer warehouseType,
			Long saleOrgId, Long organizationId,Long sbuId, Pageable pageable,
			Long sign,Integer isNotwarehouseType) {

		Page<Map<String, Object>> page = warehouseBaseService.findPage(sn,
				name,
				erp_warehouse_code,
				true,
				supplierId,
				type,
				warehouseType,
				saleOrgId,
				organizationId,
				sbuId,
				pageable,
				sign,
                isNotwarehouseType);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);

	}

	/***
	 * 通过父id查询下级地区
	 */
	@RequestMapping(value = "/getAreaByParent", method = RequestMethod.POST)
	public @ResponseBody
	Map<String, Object> getAreaByParent(Long areaId, Long id) {
		Map<String, Object> map = new HashMap<String, Object>();
		String sql = "select * from xx_area where parent = ?";
		List<Map<String, Object>> maps_area = areaBaseService.getDaoCenter()
				.getNativeDao()
				.findListMap(sql, new Object[] { areaId }, 0);
		map.put("area", maps_area);
		if (id != null) {
			sql = "select a.* from xx_area a,xx_warehouse w,xx_warehouse_area wa where"
					+ " a.id = wa.areas and wa.xx_warehouse = ? and a.parent = ?";
			List<Map<String, Object>> maps_ware = areaBaseService.getDaoCenter()
					.getNativeDao()
					.findListMap(sql, new Object[] { id, areaId }, 0);
			map.put("warehouseArea", maps_ware);
		}
		else {
			map.put("warehouseArea", "");
		}
		return map;
	}

	/**
	 * 通过工厂ID获取仓库
	 */
	@RequestMapping(value = "/selectWarehouse", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectWarehouse(Long factoryDictId) {

		LogUtils.debug("factoryDict=" + factoryDictId);
		Map<String, Object> map = new HashMap<String, Object>();
		if (factoryDictId < 1) {
			return success().addObjX(null);
		}
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("factorySystemDict", factoryDictId));
		List<Warehouse> warehouses = warehouseBaseService.findList(null,
				filters,
				null);

		if (warehouses != null && warehouses.size() > 0) {

			map.put("warehouseId", warehouses.get(0).getId());
			map.put("warehouseName", warehouses.get(0).getName());
			map.put("warehouseCode", warehouses.get(0).getErp_warehouse_code());
			return success().addObjX(map);
		}
		else {
			return success().addObjX(null);
		}
	}
	
	@RequestMapping(value = "/findWarehouseSmethod", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg findWarehouseSmethod(Long id) {
		List<Map<String, Object>>  list = warehouseSmethodService.findWarehouseSmethod(id);
		String jsonPage = JsonUtils.toJson(list);
		return success(jsonPage);
	}
	
	
	
	
	/**
	 *编辑
	 */
	@RequestMapping(value = "/storehouse_edit", method = RequestMethod.GET)
	public String storehouse_edit(Long id, ModelMap model) {

		Warehouse warehouse = warehouseBaseService.find(id);
		model.put("warehouse", warehouse);
		model.put("areas", warehouse.getAreas());
		model.put("rootAreas", areaBaseService.findRoots("ChinaCN"));

		//用户经营组织
		List<Organization> managementOrganizations = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("managementOrganizations", managementOrganizations);

		// 工厂 -系统词汇
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "productionPlant"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productionPlantList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("productionPlantList", productionPlantList);

		//库存组织
		filters.clear();
		filters.add(Filter.eq("code", "stockWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> stockSystemDicts = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("stockSystemDicts", stockSystemDicts);

		//仓库类型
		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> typeSystemDicts = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("typeSystemDicts", typeSystemDicts);
		
		//仓库库位关联表		
		List<Map<String, Object>> warehouseLocationList = warehouseBaseService.findwarehouseLocationList(warehouse);
		model.addAttribute("warehouseLocationList_json", JsonUtils.toJson(warehouseLocationList));

		
		return "/stock/warehouse/storehouse_edit";
	}
	
	
	/**
	 * 库位保存或更新
	 */
	@RequestMapping(value = "/storehouse_save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg storehouseSave(Warehouse warehouse) {
		if(!ConvertUtil.isEmpty(warehouse) && ConvertUtil.isEmpty(warehouse.getId())){
			return error("仓库Id不能为空");
		}
		warehouseBaseService.saveOrUpdateWarehouseLocation(warehouse);
		return success().addObjX(warehouse.getId());
	}

    /**
     * 作废-更新仓库状态
     * @throws Exception
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg cancel(Long id) throws Exception {

        Warehouse warehouse = warehouseBaseService.find(id);
        //校验仓库状态
        warehouseBaseService.checkWarehouseState(warehouse,0,"已保存","作废");
        warehouseBaseService.cancel(warehouse);
        return success();
    }

	/**
	 * 流程审批；
	 * @throws Exception
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId,Integer type) {

		Warehouse warehouse = warehouseBaseService.find(id);
		if(type == 1) {
            //校验仓库状态
            warehouseBaseService.checkWarehouseState(warehouse, 0, "已保存", "流程审批");
            warehouseBaseService.createWf(id, modelId, objTypeId, CommonVariable.NEW_WF_TYPE);
        }else if(type == 2){
            warehouseBaseService.checkWarehouseState(warehouse,1,"已生效","失效");
            warehouseBaseService.createWf(id, modelId, objTypeId,CommonVariable.INVALID_WF_TYPE);
        }
		return success();
	}


	
}