package net.shopxx.stock.controller;
import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.stock.service.WarehouseBillBatchService;
@Controller("warehouseBillBatchController")
@RequestMapping("/stock/warehouse_bill_batch")
public class WarehouseBillBatchController extends BaseController{
	
	@Resource(name = "warehouseBillBatchServiceImpl")
	private WarehouseBillBatchService warehouseBillBatchService;

}
