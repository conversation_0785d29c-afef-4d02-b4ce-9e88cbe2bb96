package net.shopxx.stock.controller;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.stock.entity.WarehouseBatch;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseBatchService;
import net.shopxx.stock.service.WarehouseSaleOrgBaseService;
import net.shopxx.stock.service.WarehouseSmethodService;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.util.DateUtils;
@Controller("batchController")
@RequestMapping("/stock/batch")
public class BatchController extends BaseController {

	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "warehouseSaleOrgBaseServiceImpl")
	private WarehouseSaleOrgBaseService warehouseSaleOrgBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "warehouseSmethodServiceImpl")
	private WarehouseSmethodService warehouseSmethodService;
	@Resource(name = "warehouseBatchServiceImpl")
	private WarehouseBatchService warehouseBatchService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	@RequestMapping(value = "/batch_list_tb", method = RequestMethod.GET)
	public String batch_list_tb(ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/stock/batch/batch_list_tb";
	}
	
	@RequestMapping(value = "/batch_list", method = RequestMethod.GET)
	public String batch_list(ModelMap model,Long userId,Long menuId) {
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/stock/batch/batch_list";
	}
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/batch_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg batch_list_data(String batchEncoding,
			Long[] productionPlantId, String productionNo, Boolean batchStatus,
			Long[] categoryOfGoodsId, String startProductionDate, String endProductionDate,
			String startCompletionDate,String endCompletionDate, String startEffectiveDate,
			String endEffectiveDate, String storeName,Long[] organizationId,String batchIds,String memo,Pageable pageable) {
		
		Page<Map<String, Object>> page = warehouseBatchService.findPage(batchEncoding,
				productionPlantId, productionNo, batchStatus, categoryOfGoodsId, startProductionDate, 
				endProductionDate, startCompletionDate, endCompletionDate, startEffectiveDate, 
				endEffectiveDate, storeName, organizationId,batchIds,memo, pageable);
		
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	
	
	/**
	 * 添加
	 */
	@RequestMapping(value = "/batch_add", method = RequestMethod.GET)
	public String batch_add(ModelMap model) {

		List<Area> areas = areaBaseService.findRoots("ChinaCN");
		model.put("rootAreas", areas);

		List<Filter> filters = new ArrayList<Filter>();
	
		// 工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "productionPlant"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productionPlantList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("productionPlantList", productionPlantList);

		// 货物类别
		filters.clear();
		filters.add(Filter.eq("code", "categoryOfGoods"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> categoryOfGoodsList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("categoryOfGoodsList", categoryOfGoodsList);
		//返回当前日期
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("currDate", format.format(DateUtils.getToday()));
		
		/**
		 * 用户经营组织权限
		 */
		
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						 Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						 if(organization!=null){
							 organizationList.add(organization);
						 }
					}
				}
			}
		}else{
			//经营组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			organizationList = organizationService.findList(null,filters,null);
			
		}
		model.addAttribute("managementOrganizations", organizationList);
		
		//有效日期    
		Date date = new Date();
	    Calendar cal = Calendar.getInstance();
	    cal.setTime(date);
	    cal.add(Calendar.YEAR, 10);//增加10年
		model.addAttribute("effectiveDate", format.format(cal.getTime()));  
		
		return "/stock/batch/batch_add";
	}
	
	/**
	 * 保存
	 */
	@RequestMapping(value = "/saveBatch", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg saveBatch(WarehouseBatch warehouseBatch, Long areaId, 
			Long[] saleOrgId,Boolean[] isChecks,Long[] areaIds, 
			Long stores, Long deliveryCorpId,Long factorySystemDictId, 
			HttpServletRequest request) {
		
		//获取登录人
		StoreMember storeMember = storeMemberService.getCurrent();
		//时间格式
		SimpleDateFormat formatter = new SimpleDateFormat("yyMMdd");	
		if (ConvertUtil.isEmpty(warehouseBatch.getProductionPlant().getId())) {
			//请选择生产工厂
			return ResultMsg.error("请选择生产工厂");
		}
		//获取生产工厂
		SystemDict productionPlant = systemDictBaseService.find(warehouseBatch.getProductionPlant().getId());
		if (ConvertUtil.isEmpty(productionPlant)) {
			return ResultMsg.error("未匹配到工厂信息，无法生成批次编码");
		}
		//生成批次编码
		String code = productionPlant.getLowerCode()+formatter.format(warehouseBatch.getProductionDate())+warehouseBatch.getProductionNo();
		/*List<Filter> filters = new ArrayList<Filter>();
		//按条件获取
		WarehouseBatch batchCode = new WarehouseBatch();   
		//执行保存并自动生成编号
		if(ConvertUtil.isEmpty(warehouseBatch.getBatchEncoding())) {  
			//获取生产工厂
			SystemDict productionPlant = systemDictBaseService.find(warehouseBatch.getProductionPlant().getId());
			if (ConvertUtil.isEmpty(productionPlant)) {
				return ResultMsg.error("未匹配到工厂信息，无法生成批次编码");
			}
			//生成批次编码
			code = productionPlant.getLowerCode()+formatter.format(warehouseBatch.getProductionDate())+warehouseBatch.getProductionNo();
			filters.clear();
			filters.add(Filter.eq("batchEncoding", code));
			batchCode = warehouseBatchService.find(filters);
			if (ConvertUtil.isEmpty(batchCode)) {
				warehouseBatch.setBatchEncoding(code);
			}else {
				return ResultMsg.error("生成批次编码重复，请检查工厂、单号、日期是否重复");
			}			
		}else {
			filters.clear();
			filters.add(Filter.eq("batchEncoding", warehouseBatch.getBatchEncoding()));
			batchCode = warehouseBatchService.find(filters);
			if (!ConvertUtil.isEmpty(batchCode)) {
				return ResultMsg.error("批次编码重复");
			}
		}*/
		if (ConvertUtil.isEmpty(warehouseBatch.getId())) {
			warehouseBatch.setCreateDate(new Date());
			warehouseBatch.setModifyDate(new Date());
			warehouseBatch.setModifications(0);
			warehouseBatch.setStoreMember(storeMember);
			//批次编码
			warehouseBatch.setBatchEncoding(code);
			//作业单号
			warehouseBatch.setSn(code);
			warehouseBatchService.save(warehouseBatch);
			//自动生成的编号添加流水
			if(!"".equals(code)) {        
				//编码追加ID
				warehouseBatch.setBatchEncoding(warehouseBatch.getBatchEncoding()+warehouseBatch.getId());
				warehouseBatchService.update(warehouseBatch);
			}
		}else {
			warehouseBatch.setModifyDate(new Date());
			//执行修改
			WarehouseBatch batch = warehouseBatchService.find(warehouseBatch.getId());		
			//判断当前修改次数,大于1不让修改
			if (batch.getModifications() >=1) {     
				return ResultMsg.warn("当前信息只能被修改一次！");
			}
			batch.setModifyDate(new Date());
			SystemDict plant = systemDictBaseService.find(batch.getProductionPlant().getId());
			//生产信息变更修改了则重新生成编码
			if (warehouseBatch.getProductionPlant() != plant || !batch.getProductionDate().equals(warehouseBatch.getProductionDate()) || !batch.getProductionNo().equals(warehouseBatch.getProductionNo())) {
				code = plant.getLowerCode()+warehouseBatch.getProductionNo()+formatter.format(warehouseBatch.getProductionDate());	
				//批次编码
				batch.setBatchEncoding(code+batch.getId());
				//作业单号
				batch.setSn(code);
				batch.setProductionDate(warehouseBatch.getProductionDate());
				batch.setProductionNo(warehouseBatch.getProductionNo());
				batch.setProductionPlant(plant);
			}			
			//完成时间
			batch.setCompletionDate(warehouseBatch.getCompletionDate());   
			//状态
			batch.setBatchStatus(warehouseBatch.getBatchStatus());  
			//类别
			SystemDict categoryOfGoods = systemDictBaseService.find(warehouseBatch.getCategoryOfGoods().getId());
			batch.setCategoryOfGoods(categoryOfGoods);     
			//有效日期
			batch.setEffectiveDate(warehouseBatch.getEffectiveDate());     
			//修改次数
			batch.setModifications(batch.getModifications()+1);  
			warehouseBatchService.update(batch);
		}		
		return success().addObjX(warehouseBatch.getId());
	}
	
	
	
	
	/**
	 *编辑
	 */
	@RequestMapping(value = "/batch_edit", method = RequestMethod.GET)
	public String batch_edit(Long id, ModelMap model) {

		WarehouseBatch warehouseBatch = warehouseBatchService.find(id);
		model.put("warehouseBatch", warehouseBatch);		
		model.put("rootAreas", areaBaseService.findRoots("ChinaCN"));
			
		List<Filter> filters = new ArrayList<Filter>();
		
		// 工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "productionPlant"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productionPlantList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("productionPlantList", productionPlantList);

		// 货物类别
		filters.clear();
		filters.add(Filter.eq("code", "categoryOfGoods"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> categoryOfGoodsList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("categoryOfGoodsList", categoryOfGoodsList);	
		
		/**
		 * 用户经营组织权限
		 */
		
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						 Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						 if(organization!=null){
							 organizationList.add(organization);
						 }
					}
				}
			}
		}else{
			//经营组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			organizationList = organizationService.findList(null,filters,null);
			
		}
		model.addAttribute("managementOrganizations", organizationList);

		return "/stock/batch/batch_edit";
	}
	
	
	@RequestMapping(value = "/batch_stock", method = RequestMethod.GET)
	public String batch_stock(ModelMap model,Long itemId,Long warehouseId ,
			Long productId,Long organizationId,Long productLevelId,Long colorNumbersIds,
			Long moistureContentsIds,String warehouseBatchIds,Long[] newOldLogosIds,
			Long billTypeId,Long productionPlantId,Long warehouseLocationId) {	
		
		List<Filter> filters = new ArrayList<Filter>();
		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 新旧标识
		filters.clear();
		filters.add(Filter.eq("code", "oldNewLogo"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
		model.addAttribute("newOldLogosList", newOldLogosList);
		
		model.put("itemId", itemId);
		model.put("warehouseId", warehouseId);
		model.put("productId", productId);
		model.put("organizationId", organizationId);
		model.put("productLevelId", productLevelId);
		model.put("colorNumbersIds",colorNumbersIds);
		model.put("moistureContentsIds", moistureContentsIds);
		model.put("newOldLogosIds", newOldLogosIds);
		model.put("billTypeId", billTypeId);
		model.put("warehouseLocationId", warehouseLocationId);
		List<Long> batchIds = new ArrayList<Long>();
		if(!ConvertUtil.isEmpty(warehouseBatchIds)){
			String[] warehouseBatchId = warehouseBatchIds.split(",");
			for (int i = 0; i < warehouseBatchId.length; i++) {
				if(!ConvertUtil.isEmpty(warehouseBatchId[i])){
					batchIds.add(Long.valueOf(warehouseBatchId[i]));
				}
			}
		}
		model.put("warehouseBatchIds", batchIds);
		model.put("productionPlantId", productionPlantId);
		return "/stock/batch/batch_stock";
	}
	
	@RequestMapping(value = "/add_post", method = RequestMethod.GET)
	public String add_post(ModelMap model) {				
		return "/stock/batch/edit_post";
	}
	
	
	@RequestMapping(value = "/edit_post", method = RequestMethod.GET)
	public String edit_post(String bacthIds,Long organizationId,
			Long productionPlantId,ModelMap model) {
		//获取批次信息并赋值
		List<Map<String, Object>> warehouseBatchs = warehouseBatchService.findWarehouseBatchs(bacthIds,
				organizationId,productionPlantId);
		
		warehouseBatchs = new ArrayList<Map<String, Object>>(
				new LinkedHashSet<Map<String, Object>>(warehouseBatchs));
		model.addAttribute("warehouseBatchs", JsonUtils.toJson(warehouseBatchs));
		model.addAttribute("organizationId",organizationId);
		model.addAttribute("productionPlantId",productionPlantId);
		
		return "/stock/batch/edit_post";
	}
	
	
	
	
	
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_bacth", method = RequestMethod.GET)
	public String select_menu_role(Integer multi,Long organizationId,
			Long productionPlantId,String batchIds,ModelMap model) {
		
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("productionPlantId", productionPlantId);
		model.addAttribute("batchIds", batchIds);
		model.addAttribute("multi", multi);
		List<Filter> filters = new ArrayList<Filter>();
		// 工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "productionPlant"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productionPlantList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("productionPlantList", productionPlantList);

		// 货物类别
		filters.clear();
		filters.add(Filter.eq("code", "categoryOfGoods"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> categoryOfGoodsList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("categoryOfGoodsList", categoryOfGoodsList);	

		return "/stock/batch/select_bacth";
	}

	/**
	 * 批次Excel导入
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg importFromExcel(MultipartFile file, 
			HttpServletResponse response, ModelMap model) throws Exception {

		try {
			warehouseBatchService.warehouseBatchAddImport(file);
			return ResultMsg.success();
		}catch (Exception e) {
			LogUtils.error("导入批次", e);
			return ResultMsg.error(e.getMessage());
		}
	}
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}
	
	
	public ModelAndView getModelAndView(List<Map<String, Object>> data, ModelMap model) {
	
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		
		// 设置标题
		String[] header = { "批次编码", "作业单号", "经营组织", "生产工厂", "生产单号", "制单日期", "生产完成日期",
				"是否有效", "货物类别", "有效日期", "备注", "创建人", "创建日期"};
		
		// 设置单元格宽度
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256};
		
		// 设置单元格取值
		String[] properties = { "batch_encoding", "sn", "organization_name", "factory_name", "production_no",
				"production_date", "completion_date", "batchStatus", "category_name", "effective_date", "memo" ,
				"store_name" , "create_date"};
		
		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}
	
	/**
	 * 批次单选择导出
	 * @param ids
	 * @param model
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {
		
		List<Map<String, Object>> mapList = warehouseBatchService.findWarehouseBatchList(null,
				null, null, null, null, null, null, null, null, null, null, null, null, ids);
				
		return getModelAndView(mapList, model);
	}
	
	
	/**
	 * 批次单条件导出统计数量
	 * @param batchEncoding
	 * @param productionPlantId
	 * @param productionNo
	 * @param batchStatus
	 * @param categoryOfGoodsId
	 * @param startProductionDate
	 * @param endProductionDate
	 * @param startCompletionDate
	 * @param endCompletionDate
	 * @param startEffectiveDate
	 * @param endEffectiveDate
	 * @param storeName
	 * @param organizationId
	 * @return
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(String batchEncoding,
			Long[] productionPlantId, String productionNo, Boolean batchStatus,
			Long[] categoryOfGoodsId, String startProductionDate, String endProductionDate,
			String startCompletionDate,String endCompletionDate, String startEffectiveDate,
			String endEffectiveDate, String storeName,Long[] organizationId) {
		
		List<Map<String, Object>> mapList = warehouseBatchService.findWarehouseBatchList(batchEncoding, 
				productionPlantId, productionNo, batchStatus, categoryOfGoodsId, startProductionDate, 
				endProductionDate, startCompletionDate, endCompletionDate, startEffectiveDate, 
				endEffectiveDate, storeName, organizationId, null);
		Integer size = mapList.size(); 
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	
	/**
	 * 批次单条件导出
	 * @param batchEncoding
	 * @param productionPlantId
	 * @param productionNo
	 * @param batchStatus
	 * @param categoryOfGoodsId
	 * @param startProductionDate
	 * @param endProductionDate
	 * @param startCompletionDate
	 * @param endCompletionDate
	 * @param startEffectiveDate
	 * @param endEffectiveDate
	 * @param storeName
	 * @param organizationId
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String batchEncoding, Long[] productionPlantId, 
			String productionNo, Boolean batchStatus, Long[] categoryOfGoodsId, 
			String startProductionDate, String endProductionDate, String startCompletionDate,
			String endCompletionDate, String startEffectiveDate, String endEffectiveDate, 
			String storeName, Long[] organizationId, ModelMap model){
		
		List<Map<String, Object>> mapList = warehouseBatchService.findWarehouseBatchList(batchEncoding, 
				productionPlantId, productionNo, batchStatus, categoryOfGoodsId, startProductionDate, 
				endProductionDate, startCompletionDate, endCompletionDate, startEffectiveDate, 
				endEffectiveDate, storeName, organizationId, null);
		
		return getModelAndView(mapList, model);
	}
	
	/**
	 * 批次作废
	 * @param ids
	 */
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancel(Long[] ids) {
		warehouseBatchService.cancel(ids);
		return success();
	}
	
	
}