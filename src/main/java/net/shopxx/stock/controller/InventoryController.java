package net.shopxx.stock.controller;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.stock.entity.Inventory;
import net.shopxx.stock.entity.InventoryItem;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.service.InventoryService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller("inventoryController")
@RequestMapping("/stock/inventory")
public class InventoryController extends BaseController {

    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeMemberSbuServiceImpl")
    private StoreMemberSbuService storeMemberSbuService;
    @Resource(name = "storeMemberSaleOrgBaseServiceImpl")
    private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
    @Resource(name = "roleJurisdictionUtil")
    private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;
    @Resource(name = "organizationServiceImpl")
    private OrganizationService organizationService;
    @Resource(name = "sbuServiceImpl")
    private SbuService sbuService;
    @Resource(name = "warehouseBaseServiceImpl")
    private WarehouseBaseService warehouseBaseService;
    @Resource(name = "inventoryServiceImpl")
    private InventoryService inventoryService;
    @Resource(name = "orderFullLinkServiceImpl")
    private OrderFullLinkService orderFullLinkService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgBaseService;



    
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(Integer flag, ModelMap model,Long sign){

        model.addAttribute("flag", flag);
        model.addAttribute("productionWarehousing", "生产入仓");
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        // 查询用户关联SBU
        List<Map<String, Object>> sbu = storeMemberBaseService.findSbuTy(storeMember.getId());
        if (sbu.size() > 0) {
            Long sbuIds = Long.parseLong(sbu.get(0).get("id").toString());
            model.addAttribute("sbuIds", sbuIds);

        }

        SimpleDateFormat form = new SimpleDateFormat("yyyy-MM-dd");
        model.addAttribute("GLDate", form.format(new Date()));

        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("storeMember", storeMember.getId()));
        List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
        model.addAttribute("sbus", sbus);

        // 用户默认的所属机构
        filters.clear();
        filters.add(Filter.eq("storeMember", storeMemberBaseService.getCurrent()));
        filters.add(Filter.eq("isDefault", true));
        List<StoreMemberSaleOrg> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findList(null, filters, null);
        SaleOrg saleOrg = null;
        if (storeMemberSaleOrgs != null && storeMemberSaleOrgs.size() > 0) {
            saleOrg = storeMemberSaleOrgs.get(0).getSaleOrg();
        }
        model.addAttribute("saleOrg", saleOrg);

        filters.clear();

        if (sign == null) {
            sign = 0L;
        }
        model.addAttribute("sign", sign);

        // 产品级别
        filters.clear();
        filters.add(Filter.eq("code", "productLevel"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> productLevelList = systemDictService.findList(null, filters, null);
        model.addAttribute("productLevelList", productLevelList);

        // 色号
        filters.clear();
        filters.add(Filter.eq("code", "colorNumber"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
        model.addAttribute("colorNumberList", colorNumberList);

        // 含水率
        filters.clear();
        filters.add(Filter.eq("code", "moistureContent"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
        model.addAttribute("moistureContentList", moistureContentList);

        // 新旧标识
        filters.clear();
        filters.add(Filter.eq("code", "oldNewLogo"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
        model.addAttribute("newOldLogosList", newOldLogosList);

        // 单据类别 根据类型筛选
        filters.clear();
        filters.add(Filter.eq("code", "billCategory"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> billCategoryList = systemDictService.findList(null, filters, null);
        model.addAttribute("billCategoryList", billCategoryList);

        filters.clear();
        filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
        List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
        // 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
        try {
            String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
            String[] perRole = value.split(",");
            List<String> list = Arrays.asList(perRole);
            int hiddenBatch = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    hiddenBatch++;
                    break;
                }
            }
            model.addAttribute("hiddenBatch", hiddenBatch);
        } catch (RuntimeException e) {

        }

        // 设置日期格式
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        model.addAttribute("nowDate", df.format(new Date()));

        // 是否启用LINK库存 0不启用 1启用
        String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
        if (!ConvertUtil.isEmpty(linkStockValue)) {
            Integer linkStock = Integer.valueOf(linkStockValue);
            model.addAttribute("linkStock", linkStock);
        }

        // 仓储管理是否启用库存查询 0不展示、1展示
        Integer storageStockQueryRoles = roleJurisdictionUtil.getRoleCount("storageStockQueryRoles");
        model.addAttribute("storageStockQueryRoles", storageStockQueryRoles);

        return "/stock/inventory/add";
    }

    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Pageable pageable, Integer flag, Long objTypeId, Long objid, Long billType, ModelMap model,
                          Long sign, Long menuId) {

        model.addAttribute("flag", flag);
        model.addAttribute("objTypeId", objTypeId);
        model.addAttribute("objid", objid);
        model.addAttribute("billType", billType);
        model.addAttribute("menuId", menuId);
        if (sign == null) {
            sign = 0L;
        }
        model.addAttribute("sign", sign);

        return "/stock/inventory/list_tb";
    }

    /**
     * 库存盘点列表
     * @param flag
     * @param  model
     * @param sign
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(Integer flag, ModelMap model, Long userId, Long menuId, Integer pageType, Long sign) {

        model.addAttribute("pageType", pageType);
        model.addAttribute("flag", flag);

        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
        List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
        // 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
        try {
            String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
            String[] perRole = value.split(",");
            List<String> list = Arrays.asList(perRole);
            int hiddenBatch = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    hiddenBatch++;
                    break;
                }
            }
            model.addAttribute("hiddenBatch", hiddenBatch);
        } catch (RuntimeException e) {

        }

        if (sign == null) {
            sign = 0L;
        }
        model.addAttribute("sign", sign);
        model.addAttribute("menuId", menuId);
        // 获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);
        return "/stock/inventory/list";
    }

    /**
     * 库存盘点列表数据
     * @return
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody

    ResultMsg list_data(){
        return  success();
    }

    /**
     * 审核/编辑页面
     * @param flag
     * @param model
     * @return
     */
    @RequestMapping(value = "/view", method = RequestMethod.GET)
    public String view(Long id, String sn, Integer readOnly, Integer flag, ModelMap model, Integer pageType,
                       Integer billTypeId,Long menuId,Long orderIndex,Integer sourceType){
        model.addAttribute("menuId", menuId);
        model.addAttribute("orderIndex", orderIndex);
        model.addAttribute("sourceType",sourceType);
        Inventory inventory = null;

         if (id != null) {
            inventory = inventoryService.find(id);
               } else if (sn != null) {
            List<Filter> filters = new ArrayList<Filter>();
            filters.add(Filter.eq("sn", sn));
            inventory = inventoryService.find(filters);
            id = inventory.getId();
        }
        if (inventory.getWarehouse() != null) {
            Warehouse warehouse = inventory.getWarehouse();
            model.addAttribute("warehouse", warehouse);
            model.addAttribute("smethods", warehouse.getWarehouseSmethodList());
        }

        model.addAttribute("inventory", inventory);
        model.addAttribute("creator",storeMemberBaseService.findByUsername(inventory.getbCreater(),9L).getName());

        Integer auditType = 0;
        if (!inventory.getInventoryItems().isEmpty() && inventory.getInventoryItems().size() > 0) {
            for (InventoryItem inventoryItem : inventory.getInventoryItems()) {
                if (!ConvertUtil.isEmpty(inventoryItem.getInventory()) && !ConvertUtil.isEmpty(inventoryItem.getInventory().getId())) {
                    auditType++;
                    break;
                }
            }
        }
        model.addAttribute("auditType", auditType);

        List<Map<String, Object>> list = inventoryService.findInventoryItemListById(id,
                null);

        String jsonStr = JsonUtils.toJson(list);

        model.addAttribute("jsonStr", jsonStr);

        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        if (billTypeId == null) {
            billTypeId = 0;
        }
        model.addAttribute("billTypeId", billTypeId);
        filters.add(Filter.eq("id", billTypeId));
        filters.add(Filter.eq("code", "billType"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> billTypes = systemDictService.findList(null, filters, null);
        model.addAttribute("billTypes", billTypes);

//        /** 全链路 */
//        String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(inventory.getSn(), 4));
//        model.addAttribute("fullLink_json", fullLink_json);
//        model.addAttribute("readOnly", readOnly);
//        model.addAttribute("flag", flag);
//        boolean isCheckWf = wfObjConfigBaseService.isCheckWf(57L);// test/正式:57
//
//        model.addAttribute("isCheckWf", isCheckWf);

        // 组织
        filters.clear();
        filters.add(Filter.eq("isEnabled", true));
        List<Organization> organizations = organizationService.findList(null, filters, null);
        model.addAttribute("organizations", organizations);

        // sbu
        Sbu sbu = inventory.getSbu();
        List<SbuItems> sbuItems = sbu.getShippingMethodSbuList();
        model.addAttribute("sbuItems", sbuItems);
        model.addAttribute("sbuIds", sbu.getId());
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        filters.clear();
        filters.add(Filter.eq("storeMember", storeMember));
        List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
        model.addAttribute("sbus", sbus);
        model.addAttribute("isMember", storeMember.getMemberType());

        filters.clear();
        filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
        List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
        // 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
        try {
            String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
            String[] perRole = value.split(",");
            List<String> listString = Arrays.asList(perRole);
            int hiddenBatch = 0;
            for (PcUserRole userRole : userRoles) {
                if (listString.contains(userRole.getPcRole().getName())) {
                    hiddenBatch++;
                    break;
                }
            }
            model.addAttribute("hiddenBatch", hiddenBatch);
        } catch (RuntimeException e) {

        }

        // 产品级别
        filters.clear();
        filters.add(Filter.eq("code", "productLevel"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> productLevelList = systemDictService.findList(null, filters, null);
        model.addAttribute("productLevelList", productLevelList);

        // 色号
        filters.clear();
        filters.add(Filter.eq("code", "colorNumber"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
        model.addAttribute("colorNumberList", colorNumberList);

        // 含水率
        filters.clear();
        filters.add(Filter.eq("code", "moistureContent"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
        model.addAttribute("moistureContentList", moistureContentList);

        // 新旧标识
        filters.clear();
        filters.add(Filter.eq("code", "oldNewLogo"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
        model.addAttribute("newOldLogosList", newOldLogosList);

        // 是否启用LINK库存 0不启用 1启用
        String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
        if (!ConvertUtil.isEmpty(linkStockValue)) {
            Integer linkStock = Integer.valueOf(linkStockValue);
            model.addAttribute("linkStock", linkStock);
        }

        // 仓储管理是否启用库存查询 0不展示、1展示
        Integer storageStockQueryRoles = roleJurisdictionUtil.getRoleCount("storageStockQueryRoles");
        model.addAttribute("storageStockQueryRoles", storageStockQueryRoles);

        // 是否订货批次出仓
//        Integer isOrderWarehousingBatch = getIsOrderWarehousingBatch(inventory.getWarehouse());
//        model.addAttribute("isOrderWarehousingBatch", isOrderWarehousingBatch);

        //全链路
        String shopInfoFullLink_json = JsonUtils
                .toJson(orderFullLinkService.findListByElseSnAndType(inventory.getSn(),202));
        model.addAttribute("inventoryFullLink_json", shopInfoFullLink_json);

        return "/stock/inventory/view";
    }

    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg update(Inventory inventory, Long saleOrgId, Long warehouseId, Long organizationId, Long sbuId, Integer flag){
        List<InventoryItem> inventoryItems = inventory.getInventoryItems();
        if (inventoryItems.isEmpty()) {
            // 请添加产品
            return error("15152");
        }
        if (organizationId != null) {
            inventory.setOrganization(organizationService.find(organizationId));
        }
        //SBU
        inventory.setSbu(sbuService.find(sbuId));
        //更改单据状态
        inventory.setStatus(0);
        //仓库
        Warehouse warehouse = warehouseBaseService.find(warehouseId);
        inventory.setWarehouse(warehouse);
        inventory.setSaleOrg(saleOrgBaseService.find(saleOrgId));
        inventoryService.update(inventory, warehouse, sbuId, flag);

        return success();
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg save(Inventory inventory, Long saleOrgId, Long warehouseId, Long organizationId, Long sbuId, Integer flag){

        List<InventoryItem> inventoryItems = inventory.getInventoryItems();
        if (inventoryItems.isEmpty()) {
            // 请添加产品
            return error("15152");
        }
        if (organizationId != null) {
            inventory.setOrganization(organizationService.find(organizationId));
        }
        inventory.setSbu(sbuService.find(sbuId));
        inventory.setStatus(0);
        Warehouse warehouse = warehouseBaseService.find(warehouseId);
        inventory.setWarehouse(warehouse);
        inventory.setSaleOrg(saleOrgBaseService.find(saleOrgId));
        inventoryService.save(inventory, warehouse, sbuId, 1);

        return success().addObjX(inventory.getId());
    }

    /**
     * Excel导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/import_excel", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg importFromExcel(MultipartFile file) throws Exception {
        inventoryService.importExcel(file);
        try {

            return ResultMsg.success();
        }
        catch (Exception e) {
            LogUtils.error("导入库存盘点明细", e);
            return ResultMsg.error(e.getMessage());
        }
    }

    /**
     * 审核-生成已审核的出入库单
     */
    @RequestMapping(value = "/checkWf", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg checkWf(Long id){


        try {
            inventoryService.checkWf(id);
            return ResultMsg.success();
        }
        catch (Exception e) {
            LogUtils.error("库存盘点明细审核 ", e);
            return ResultMsg.error(e.getMessage());
        }
    }

    /**作废
     *
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public @ResponseBody ResultMsg cancel(Long id) {

        Inventory inventory = inventoryService.find(id);
        //校验政策单状态
        inventoryService.cancel(inventory);
        return success();
    }
}
