/*
 * Copyright 2005-2013 shopxx.net. All rights reserved. Support:
 * http://www.shopxx.net License: http://www.shopxx.net/license
 */
package net.shopxx.stock.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.DeliveryCorp;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.stock.service.DeliveryCorpBaseService;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Controller - 物流公司
 * 
 * <AUTHOR> Team
 * @version 3.0
 */
@Controller("stockDeliveryCorpController")
@RequestMapping("/stock/delivery_corp")
public class DeliveryCorpController extends BaseController {

	@Resource(name = "deliveryCorpBaseServiceImpl")
	private DeliveryCorpBaseService deliveryCorpService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add() {
		return "/stock/delivery_corp/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(DeliveryCorp deliveryCorp) {

		deliveryCorpService.save(deliveryCorp);
		return success().addObjX(deliveryCorp.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {

		model.addAttribute("deliveryCorp", deliveryCorpService.find(id));
		return "/stock/delivery_corp/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(DeliveryCorp deliveryCorp) {

		deliveryCorpService.update(deliveryCorp);
		return success();
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/stock/delivery_corp/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/stock/delivery_corp/list_tb";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String name, String code, String waybillSn,
			Pageable pageable, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		if (StringUtils.isNotEmpty(name)) {
			filters.add(Filter.like("name", "%" + name + "%"));
		}
		if (StringUtils.isNotEmpty(code)) {
			filters.add(Filter.like("code", "%" + code + "%"));
		}
		if (StringUtils.isNotEmpty(waybillSn)) {
			filters.add(Filter.like("waybillSn", "%" + waybillSn + "%"));
		}
		String jsonPage = JsonUtils.toJson(deliveryCorpService.findPage(filters,
				null,
				pageable));

		return success(jsonPage);
	}

	@RequestMapping(value = "/select_delivery_corp", method = RequestMethod.GET)
	public String select_delivery_corp(Pageable pageable, ModelMap model) {

		return "/stock/delivery_corp/select_delivery_corp";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_delivery_corp_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_delivery_corp_data(String name, String code,
			String waybillSn, Pageable pageable, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		if (StringUtils.isNotEmpty(name)) {
			filters.add(Filter.like("name", "%" + name + "%"));
		}
		if (StringUtils.isNotEmpty(code)) {
			filters.add(Filter.like("code", "%" + code + "%"));
		}
		if (StringUtils.isNotEmpty(waybillSn)) {
			filters.add(Filter.like("waybillSn", "%" + waybillSn + "%"));
		}
		String jsonPage = JsonUtils.toJson(deliveryCorpService.findPage(filters,
				null,
				pageable));

		return success(jsonPage);
	}

	/**
	 * 删除
	 */
//	@RequestMapping(value = "/delete", method = RequestMethod.POST)
//	public @ResponseBody
//	Message delete(Long[] ids) {
//		deliveryCorpService.delete(ids);
//		return SUCCESS_MESSAGE;
//	}

}