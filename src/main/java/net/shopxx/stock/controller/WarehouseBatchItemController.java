package net.shopxx.stock.controller;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.stock.entity.WarehouseBatchItem;
import net.shopxx.stock.service.WarehouseBatchItemService;
@Controller("warehouseBatchItemController")
@RequestMapping("/stock/warehouse_batch_item")
public class WarehouseBatchItemController  extends BaseController{
	
	@Resource(name = "warehouseBatchItemServiceImpl")
	private WarehouseBatchItemService warehouseBatchItemService;
	
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Long warehouseId ,Long productId,
			Long organizationId,Long productLevelId,Long[] colorNumbersIds,String memo,
			Long[] moistureContentsIds,Long[] warehouseBatchIds,Long[] newOldLogosIds,
			String startTime, String endTime,Pageable pageable,String batchEncoding,
			String billType,String documentNo,Boolean flag,Long productionPlantId,Long warehouseLocationId) {
		
		Page<Map<String, Object>> page = warehouseBatchItemService.findPage(warehouseId, 
				productId, organizationId, productLevelId, colorNumbersIds, moistureContentsIds, 
				warehouseBatchIds, newOldLogosIds, startTime, endTime, pageable,batchEncoding,billType,
				documentNo,flag,productionPlantId,warehouseLocationId,memo);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	
	/**
	 * 批次列表数据
	 * @param amShippingId
	 * @param warehouseId
	 * @param productId
	 * @param organizationId
	 * @param productLevelId
	 * @param colorNumbersIds
	 * @param moistureContentsIds
	 * @param warehouseBatchIds
	 * @param newOldLogosIds
	 * @return
	 */
	@RequestMapping(value = "/v_warehouse_batch_item_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg findViewStock(Long amShippingId,Long warehouseId ,
			Long productId,Long organizationId,Long productLevelId,Long colorNumbersId,
			Long moistureContentsId,Long[] warehouseBatchIds,Long newOldLogosId,
			Long billTypeId,Long productionPlantId,Long warehouseLocationId){
		
		String	jsonPage = "";
		List<Map<String, Object>>  mapList = warehouseBatchItemService.findVWarehouseBatchItemList(null,
				warehouseId, productId, organizationId, productLevelId, colorNumbersId, moistureContentsId, 
				warehouseBatchIds, newOldLogosId, billTypeId, true, productionPlantId,warehouseLocationId);
		if(!ConvertUtil.isEmpty(mapList) && mapList.size()>0){
			jsonPage = JsonUtils.toJson(mapList);
		}else{
			jsonPage = JsonUtils.toJson(new ArrayList<Map<String, Object>>());
		}
		return success(jsonPage);
	}
	
	
}
