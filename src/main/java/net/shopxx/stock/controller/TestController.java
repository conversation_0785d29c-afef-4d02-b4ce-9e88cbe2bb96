package net.shopxx.stock.controller;

import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.member.service.CustomerRechargeService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/*
 * 功能测试
 */
@Controller("testController")
@RequestMapping("/stock/test")
public class TestController extends BaseController{
	
	/*@Resource(name = "customerRechargeServiceImpl")
    private CustomerRechargeService customerRechargeService;*/
	
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;

	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long flag,ModelMap model,Long menuId) {
		String jumpPath = menuJumpUtils.getMenuJumpPath(menuId);
		if (ConvertUtil.isEmpty(jumpPath)){
			throw new RuntimeException("请完成模板或路径配置！");
		}
		model.addAttribute("jumpPath", jumpPath);
		model.addAttribute("flag", flag);
		model.addAttribute("userId", WebUtils.getCurrentStoreMemberId());
		return "/text/list_tb";
	}

	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		
		//用户sbu
		List<Sbu> sbuList = roleJurisdictionUtil.getSbuList();
		model.addAttribute("sbuList", sbuList);
	
		return "/text/list";
	}


	
	
	/**
	 * 经营组织列表
	 */
	@RequestMapping(value = "/organization_list", method = RequestMethod.GET)
	public String organization_list(Pageable pageable, ModelMap model) {
		
		return "/text/organization_list";
	}
	
	
	
	
	
	
	/**
	 * 数据加载
	 * @param storeId
	 * @param sbuId
	 * @return
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Long storeId,Long sbuId) {
		
		CustomerRechargeService customerRechargeService = SpringUtils.getBean("customerRechargeServiceImpl", CustomerRechargeService.class);
		
		List<Map<String, Object>> mapList = customerRechargeService.findCustomerRechargeList(storeId, null, sbuId);
		
		if(!mapList.isEmpty() && mapList.size()>10){
			mapList = mapList.subList(0, 10);
		}
		String mapListJson = JsonUtils.toJson(mapList);
		return success(mapListJson);
	}
	
	
}
