package net.shopxx.stock.controller;

import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.stock.service.WarehouseStockService;
import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
@Controller("warehouseStockController")
@RequestMapping("/stock/warehouse_stock")
public class WarehouseStockController extends BaseController{
	
	@Resource(name = "warehouseStockServiceImpl")
	private WarehouseStockService warehouseStockService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	
	
	
	/**
	 * 查询库存列表
	 */
	@RequestMapping(value = "/select_warehouse_stock", method = RequestMethod.GET)
	public String selectWarehouseStock(Long warehouseId,ModelMap model) {
		model.addAttribute("warehouseId",warehouseId);
		//是否展示金额、价格   0 不展示、 非0 展示
		Integer hiddenAmountRolesCount =  roleJurisdictionUtil.getRoleCount("hiddenAmountRoles");
		model.addAttribute("hiddenAmountRolesCount",hiddenAmountRolesCount);
		return "/stock/warehouse_stock/select_warehouse_stock";
	}
	
	
	
	
	/**
	 * 库存列表数据
	 */
	@RequestMapping(value = "/select_warehouse_stock_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg selectWarehouseStockData(Long warehouseId, String productName, 
			String vonderCode, String model) {
		List<Map<String, Object>> saleOrgs = warehouseStockService.findWarehouseStockList(warehouseId,
				productName, vonderCode, model,null);
		String jsonPage = JsonUtils.toJson(saleOrgs);
		return ResultMsg.success(jsonPage);
	}
}	
