package net.shopxx.stock.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Repository("warehouseStockDao")
public class WarehouseStockDao extends DaoCenter{
	
	/**
	 * 根据仓库查询库存
	 * @param warehouseId
	 * @return
	 */
	public List<Map<String, Object>> findWarehouseStockList(Long warehouseId,
			String productName, String vonderCode, String model,String negative) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT vws.productid id,vws.product_name `name`,vws.itemCode vonder_code,vws.detailDescription description, ");
		sql.append(" vws.product_organization_id,vws.product_organization_name,vws.level_Id,vws.levelName,vws.attQuantity2 box_quantity, ");
		sql.append(" vws.attQuantity3 branch_quantity,vws.attQuantity1 quantity,vws.branch_per_box,vws.per_branch,vws.per_box,vws.price, ");
		sql.append(" vws.color_numbers_id,vws.color_numbers_name,vws.moisture_content_id,vws.moisture_content_name,vws.warehouse_batch_id batch, ");
		sql.append(" vws.batch_encoding,vws.volume,vws.weight,vws.model,vws.spec,vws.unit,vws.new_old_logos_id,vws.new_old_logos_name ");
		sql.append(" FROM v_warehouse_stock vws ");
		sql.append(" WHERE 1=1 ");
		if (ConvertUtil.isEmpty(warehouseId)) {
			sql.append(" AND vws.company_info_id = ?");
			list.add(companyInfoId);
		}
		//仓库
		if (!ConvertUtil.isEmpty(warehouseId)) {
			sql.append(" AND vws.warehouseId = ? ");
			list.add(warehouseId);
		}
		//产品名称
		if (!ConvertUtil.isEmpty(productName)) {
			sql.append(" AND vws.product_name like ?");
			list.add("%" + productName + "%");
		}
		//产品编码
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" AND vws.itemCode like ?");
			list.add("%" + vonderCode + "%");
		}
		//产品型号
		if (!ConvertUtil.isEmpty(model)) {
			sql.append(" AND vws.model like ?");
			list.add("%" + model + "%");
		}

		if(!ConvertUtil.isEmpty(negative)) {
			if (!negative.equals("negative")) {
				sql.append(" AND vws.attQuantity1 > 0");
			}
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

}
