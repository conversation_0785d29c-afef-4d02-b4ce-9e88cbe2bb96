package net.shopxx.stock.dao;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.util.SystemParametes;
import org.apache.commons.lang.time.DateUtils;
import org.hibernate.loader.custom.Return;
import org.springframework.stereotype.Repository;
import org.springframework.ui.ModelMap;
@Repository("stockDao")
public class StockDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	
	
	/**
	 * 根据仓库和产品查库存
	 * @param warehouseId
	 * @param productId
	 * @return
	 */
	public Map<String, Object> find(long warehouseId, long productId) {

		String sql = "select * from xx_stock s where s.product = ? and s.warehouse = ?";

		Map<String, Object> stock = getNativeDao().findSingleMap(sql,
				new Object[] { productId, warehouseId });

		return stock;
	}

	/**
	 * 根据仓库和产品查库存
	 * @param warehouseId
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> findStockList(Long warehouseId,
			Long[] productIds) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (useLockStock()) {
			sql.append("select p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,");
			sql.append(" w.name warehouse_name,w.sn warehouse_sn,");
			sql.append(" s.id, s.product, s.warehouse, s.actual_stock,");
			sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
			sql.append(" (s.actual_stock - ifnull(a.lock_stock, 0)) useable_stock");
			sql.append(" from xx_stock s");
			sql.append(" left join");
			sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
			sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
			sql.append("  and si.status <> 2 and si.company_info_id = ? group by i.product, si.warehouse) a");
			sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
			sql.append(" left join xx_product p on s.product = p.id");
			sql.append(" left join xx_warehouse w on s.warehouse = w.id");
			sql.append(" where s.company_info_id = ? and w.id = ?");

			list.add(companyInfoId);
			list.add(companyInfoId);
			list.add(warehouseId);
		}
		else {
			sql.append("select s.id, s.product, s.warehouse, s.actual_stock,");
			sql.append("s.actual_stock useable_stock, (s.actual_stock - s.actual_stock) lock_stock");
			sql.append(" from xx_stock s where s.company_info_id = ? and s.warehouse = ?");
			list.add(companyInfoId);
			list.add(warehouseId);
		}

		String os = "";
		for (int i = 0; i < productIds.length; i++) {
			if (i == productIds.length - 1)
				os += productIds[i];
			else
				os += productIds[i] + ",";
		}
		sql.append(" and s.product in (" + os + ")");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> lists = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return lists;
	}



	/**
	 * 新增库存
	 * @param warehouseId
	 * @param productId
	 * @param actualStock
	 */
	public int insert(long warehouseId, long productId, BigDecimal actualStock) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Date now = new Date();

		String sql = "insert into xx_stock (create_date,modify_date,company_info_id,actual_stock,product,warehouse) values (?, ?, ?, ?, ?, ?)";

		return getNativeDao().insert(sql,
				new Object[] { now,
						now,
						companyInfoId,
						actualStock,
						productId,
						warehouseId });

	}

	/**
	 * 更新库存
	 * @param warehouseId
	 * @param productId
	 * @param actualStock
	 * @param compareStock
	 * @return
	 */
	public int update(long warehouseId, long productId, BigDecimal actualStock,
			BigDecimal compareStock) {

		Date now = new Date();

		String sql = "update xx_stock set modify_date = ?,actual_stock = actual_stock + ? where product = ? and warehouse = ?";
		Object[] objs = null;
		if (compareStock.compareTo(BigDecimal.ZERO) == 1) {
			sql += " and actual_stock >= ?";
			objs = new Object[] { now,
					actualStock,
					productId,
					warehouseId,
					compareStock };
		}
		else {
			objs = new Object[] { now, actualStock, productId, warehouseId };
		}
		return getNativeDao().update(sql, objs);
	}

	/**
	 * 库存列表数据
	 */
	public Page<Map<String, Object>> findPage(Long[] productIds,
			Long[] warehouseIds, Integer isShowZero, String vonderCode,
			Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (useLockStock()) {
			sql.append("select p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,");
			sql.append(" w.name warehouse_name,w.sn warehouse_sn,");
			sql.append(" s.id, s.product, s.warehouse, s.actual_stock,");
			sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
			sql.append(" (s.actual_stock - ifnull(a.lock_stock, 0)) useable_stock");
			sql.append(" from xx_stock s");
			sql.append(" left join");
			sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
			sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
			sql.append("  and si.status <> 2 and si.company_info_id = ? group by i.product, si.warehouse) a");
			sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
			sql.append(" left join xx_product p on s.product = p.id");
			sql.append(" left join xx_warehouse w on s.warehouse = w.id");
			sql.append(" where s.company_info_id = ?");

			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		else {
			sql.append("select s.id, s.actual_stock, s.actual_stock useable_stock, (s.actual_stock - s.actual_stock) lock_stock,");
			sql.append("p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,w.name warehouse_name,w.sn warehouse_sn");
			sql.append(" from xx_stock s,xx_product p ,xx_warehouse w");
			sql.append(" where s.product=p.id and s.warehouse=w.id and s.company_info_id = ?");
			list.add(companyInfoId);
		}

		/*
		 * if (productId != null) { sql.append(" and p.id = ?");
		 * list.add(productId); } if (warehouseId != null) {
		 * sql.append(" and w.id = ?"); list.add(warehouseId); }
		 */
		if (productIds != null && productIds.length > 0) {
			String p = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					p += productIds[i];
				else
					p += productIds[i] + ",";
			}
			sql.append(" and p.id in (" + p + ")");
		}
		if (warehouseIds != null && warehouseIds.length > 0) {
			String w = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					w += warehouseIds[i];
				else
					w += warehouseIds[i] + ",";
			}
			sql.append(" and w.id in (" + w + ")");
		}
		if (isShowZero != null && isShowZero.intValue() == 0) {
			sql.append(" and s.actual_stock <>0");
		}
		if (vonderCode != null) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		String warehouseAuth = storeMemberService.warehouseAuth();
		if (warehouseAuth != null) {
			sql.append(" and s.warehouse in (" + warehouseAuth + ")");
		}
		sql.append(" order by s.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalSql = "select count(1) from (" + sql + ") a";
		long total = getNativeDao().findInt(totalSql, objs);
		page.setTotal(total);

		return page;
	}

	public Integer count(Long[] warehouseIds, Long[] productIds,
			Integer isShowZero, String vonderCode, Pageable pageable,
			ModelMap model, Integer page, Integer size) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (useLockStock()) {
			sql.append("select count(1) from xx_stock s");
			sql.append(" left join");
			sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
			sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
			sql.append("  and si.status <> 2 and si.company_info_id = ? group by i.product, si.warehouse) a");
			sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
			sql.append(" left join xx_product p on s.product = p.id");
			sql.append(" left join xx_warehouse w on s.warehouse = w.id");
			sql.append(" where s.company_info_id = ?");

			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		else {
			sql.append("select count(s.id) from xx_stock s");
			sql.append(" left join xx_warehouse w on s.warehouse=w.id");
			sql.append(" left join xx_product p on s.product=p.id");
			sql.append(" where s.company_info_id = ?");
			list.add(companyInfoId);
		}

		/*
		 * if (warehouseId != null) { sql.append(" and s.warehouse = ?");
		 * list.add(warehouseId); } if (productId != null) {
		 * sql.append(" and s.product = ?"); list.add(productId); }
		 */
		if (productIds != null && productIds.length > 0) {
			String p = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					p += productIds[i];
				else
					p += productIds[i] + ",";
			}
			sql.append(" and s.product in (" + p + ")");
		}
		if (warehouseIds != null && warehouseIds.length > 0) {
			String w = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					w += warehouseIds[i];
				else
					w += warehouseIds[i] + ",";
			}
			sql.append(" and s.warehouse in (" + w + ")");
		}
		if (isShowZero != null && isShowZero.intValue() == 0) {
			sql.append(" and s.actual_stock <>0");
		}
		if (vonderCode != null) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		sql.append(" order by s.create_date desc");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}

	public List<Map<String, Object>> findItemList(Long[] warehouseIds,
			Long[] productIds, Integer isShowZero, Long[] ids,
			String vonderCode, Integer page, Integer size) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (useLockStock()) {
			sql.append("select p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,");
			sql.append(" w.name warehouse_name,w.sn warehouse_sn,");
			sql.append(" s.id, s.product, s.warehouse, s.actual_stock,");
			sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
			sql.append(" (s.actual_stock - ifnull(a.lock_stock, 0)) useable_stock");
			sql.append(" from xx_stock s");
			sql.append(" left join");
			sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
			sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
			sql.append("  and si.status <> 2 and si.company_info_id = ? group by i.product, si.warehouse) a");
			sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
			sql.append(" left join xx_product p on s.product = p.id");
			sql.append(" left join xx_warehouse w on s.warehouse = w.id");
			sql.append(" where s.company_info_id = ?");

			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		else {
			sql.append("select s.id, s.actual_stock, s.actual_stock useable_stock, (s.actual_stock - s.actual_stock) lock_stock,");
			sql.append(" p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,w.name warehouse_name,w.sn warehouse_sn");
			sql.append(" from xx_stock s");
			sql.append(" left join xx_warehouse w on s.warehouse=w.id");
			sql.append(" left join xx_product p on s.product=p.id");
			sql.append(" where s.company_info_id = ?");
			list.add(companyInfoId);
		}

		/*
		 * if (warehouseId != null) { sql.append(" and s.warehouse = ?");
		 * list.add(warehouseId); }
		 */
		if (productIds != null && productIds.length > 0) {
			String p = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					p += productIds[i];
				else
					p += productIds[i] + ",";
			}
			sql.append(" and s.product in (" + p + ")");
		}
		if (warehouseIds != null && warehouseIds.length > 0) {
			String w = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					w += warehouseIds[i];
				else
					w += warehouseIds[i] + ",";
			}
			sql.append(" and s.warehouse in (" + w + ")");
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and s.id in (" + inIds + ")");
		}
		if (vonderCode != null) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		sql.append(" order by s.create_date desc");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return maps;
	}

	public Map<String, Object> findLockStock(Long productId, Long warehouseId) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select s.id, s.actual_stock,");
		sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
		sql.append(" (s.actual_stock - ifnull(a.lock_stock, 0)) useable_stock");
		sql.append(" from xx_stock s");
		sql.append(" left join");
		sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
		sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
		sql.append("  and si.status <> 2 and i.product = ? and si.warehouse = ? and si.company_info_id = ?) a");
		sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
		sql.append(" where s.product = ? and s.warehouse = ? and s.company_info_id = ?");

		Map<String, Object> map = getNativeDao().findSingleMap(sql.toString(),
				new Object[] { productId,
						warehouseId,
						companyInfoId,
						productId,
						warehouseId,
						companyInfoId });

		return map;
	}

	public Map<String, Object> findLockStock(Long productId) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select ifnull(sum(s.actual_stock),0) actual_stock,");
		sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
		sql.append(" (ifnull(sum(s.actual_stock),0) - ifnull(a.lock_stock, 0)) useable_stock");
		sql.append(" from xx_stock s");
		sql.append(" left join");
		sql.append("  (select oi.product, sum(oi.quantity - oi.shipped_quantity) lock_stock");
		sql.append("   from xx_order_item oi, xx_order o");
		sql.append("   where oi.orders = o.id and o.order_status in (6) and o.order_type <> 4 and oi.quantity > oi.shipped_quantity");
		sql.append("   and oi.product = ? and oi.company_info_id = ?) a");
		sql.append(" on s.product = a.product");
		sql.append(" where s.product = ? and s.company_info_id = ?");

		Map<String, Object> map = getNativeDao().findSingleMap(sql.toString(),
				new Object[] { productId,
						companyInfoId,
						productId,
						companyInfoId });

		return map;
	}

	public BigDecimal findActualStock(Long productId) {

		String sql = "select ifnull(sum(actual_stock),0) actual_stock from xx_stock s where s.product = ?";

		Map<String, Object> stock = getNativeDao().findSingleMap(sql,
				new Object[] { productId });
		return new BigDecimal(stock.get("actual_stock").toString());
	}

	private boolean useLockStock() {

		//是否启用锁库存模式：0 否，1 是
		int useLockStock = Integer.parseInt(SystemConfig.getConfig("useLockStock",
				WebUtils.getCurrentCompanyInfoId()));

		return useLockStock == 1;
	}

	/**
	 * 进销存报表列表数据
	 */
	public Page<Map<String, Object>> findInvoicingPage(Long productId,
			Long warehouseId, String startTime, String endTime,
			String vonderCode, String mod, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String warehouseAuth = storeMemberService.warehouseAuth();

		StringBuilder sql = new StringBuilder();
		sql.append("select  s.id, s.warehouse, s.actual_stock, s.product,p.name product_name,p.vonder_code,");
		sql.append(" p.model,w.name warehouse_name,w.sn warehouse_sn, a.in_stock, b.out_stock from xx_stock s");
		sql.append(" left join xx_warehouse w on s.warehouse = w.id");
		sql.append(" left join xx_product p on s.product = p.id");
		sql.append(" left join");
		sql.append(" (select ifnull(sum(sii.actual_in_stock), 0) in_stock, si.warehouse, sii.product");
		sql.append(" from xx_stock_in_item sii, xx_stock_in si where sii.stock_in = si.id");
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and date_format(sii.create_date,'%Y-%m') >= ?");
			list.add(startTime);
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and date_format(sii.create_date,'%Y-%m') <= ?");
			list.add(endTime);
		}
		sql.append(" group by si.warehouse, sii.product) a on a.warehouse = s.warehouse and a.product = s.product");
		sql.append(" left join");
		sql.append(" (select ifnull(sum(soi.actual_out_stock), 0) out_stock, so.warehouse, soi.product");
		sql.append(" from xx_stock_out_item soi, xx_stock_out so where soi.stock_out = so.id");
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and date_format(soi.create_date,'%Y-%m') >= ?");
			list.add(startTime);
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and date_format(soi.create_date,'%Y-%m') <= ?");
			list.add(endTime);
		}
		sql.append(" group by so.warehouse, soi.product) b on b.warehouse = s.warehouse and b.product = s.product");
		sql.append(" where 1=1");
		if (warehouseAuth != null) {
			sql.append(" and w.id in (" + warehouseAuth + ")");
		}

		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (productId != null) {
			sql.append(" and p.id = ?");
			list.add(productId);
		}
		if (warehouseId != null) {
			sql.append(" and w.id = ?");
			list.add(warehouseId);
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(mod)) {
			sql.append(" and p.model like ?");
			list.add("%" + mod + "%");
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalSql = "select count(1) from (" + sql + ") a";
		long total = getNativeDao().findInt(totalSql, objs);
		page.setTotal(total);

		return page;
	}

	/**
	 * 进销存报表列表数据导出
	 */
	public Integer count(Long productId, Long warehouseId, String startTime,
			String endTime, String vonderCode, String mod, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String warehouseAuth = storeMemberService.warehouseAuth();

		StringBuilder sql = new StringBuilder();
		sql.append("select s.warehouse, s.product, s.actual_stock ,p.name product_name,p.vonder_code,");
		sql.append(" p.model,w.name warehouse_name,w.sn warehouse_sn, a.in_stock, b.out_stock from xx_stock s");
		sql.append(" left join xx_warehouse w on s.warehouse = w.id");
		sql.append(" left join xx_product p on s.product = p.id");
		sql.append(" left join");
		sql.append(" (select ifnull(sum(sii.actual_in_stock), 0) in_stock, si.warehouse, sii.product");
		sql.append(" from xx_stock_in_item sii, xx_stock_in si where sii.stock_in = si.id");
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and date_format(sii.create_date,'%Y-%m') >= ?");
			list.add(startTime);
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and date_format(sii.create_date,'%Y-%m') <= ?");
			list.add(endTime);
		}
		sql.append(" group by si.warehouse, sii.product) a on a.warehouse = s.warehouse and a.product = s.product");
		sql.append(" left join");
		sql.append(" (select ifnull(sum(soi.actual_out_stock), 0) out_stock, so.warehouse, soi.product");
		sql.append(" from xx_stock_out_item soi, xx_stock_out so where soi.stock_out = so.id");
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and date_format(soi.create_date,'%Y-%m') >= ?");
			list.add(startTime);
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and date_format(soi.create_date,'%Y-%m') <= ?");
			list.add(endTime);
		}
		sql.append(" group by so.warehouse, soi.product) b on b.warehouse = s.warehouse and b.product = s.product");
		sql.append(" where 1=1");
		if (warehouseAuth != null) {
			sql.append(" and w.id in (" + warehouseAuth + ")");
		}

		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (productId != null) {
			sql.append(" and p.id = ?");
			list.add(productId);
		}
		if (warehouseId != null) {
			sql.append(" and w.id = ?");
			list.add(warehouseId);
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(mod)) {
			sql.append(" and p.model like ?");
			list.add("%" + mod + "%");
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		String totalSql = "select count(1) from (" + sql + ") a";
		Integer count = getNativeDao().findInt(totalSql, objs);
		return count;
	}

	/**
	 * 进销存报表列表数据导出
	 */
	public List<Map<String, Object>> findInvoicingList(Long productId,
			Long warehouseId, String startTime, String endTime,
			String vonderCode, String mod, Long[] ids, Integer page,
			Integer size) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String warehouseAuth = storeMemberService.warehouseAuth();

		StringBuilder sql = new StringBuilder();
		sql.append("select s.warehouse, s.product, s.actual_stock, p.name product_name,p.vonder_code,");
		sql.append(" p.model,w.name warehouse_name,w.sn warehouse_sn, a.in_stock, b.out_stock from xx_stock s");
		sql.append(" left join xx_warehouse w on s.warehouse = w.id");
		sql.append(" left join xx_product p on s.product = p.id");
		sql.append(" left join");
		sql.append(" (select ifnull(sum(sii.actual_in_stock), 0) in_stock, si.warehouse, sii.product");
		sql.append(" from xx_stock_in_item sii, xx_stock_in si where sii.stock_in = si.id");
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and date_format(sii.create_date,'%Y-%m') >= ?");
			list.add(startTime);
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and date_format(sii.create_date,'%Y-%m') <= ?");
			list.add(endTime);
		}
		sql.append(" group by si.warehouse, sii.product) a on a.warehouse = s.warehouse and a.product = s.product");
		sql.append(" left join");
		sql.append(" (select ifnull(sum(soi.actual_out_stock), 0) out_stock, so.warehouse, soi.product");
		sql.append(" from xx_stock_out_item soi, xx_stock_out so where soi.stock_out = so.id");
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and date_format(soi.create_date,'%Y-%m') >= ?");
			list.add(startTime);
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and date_format(soi.create_date,'%Y-%m') <= ?");
			list.add(endTime);
		}
		sql.append(" group by so.warehouse, soi.product) b on b.warehouse = s.warehouse and b.product = s.product");
		sql.append(" where 1=1");
		if (warehouseAuth != null) {
			sql.append(" and w.id in (" + warehouseAuth + ")");
		}

		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (productId != null) {
			sql.append(" and p.id = ?");
			list.add(productId);
		}
		if (warehouseId != null) {
			sql.append(" and w.id = ?");
			list.add(warehouseId);
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(mod)) {
			sql.append(" and p.model like ?");
			list.add("%" + mod + "%");
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and s.id in (" + inIds + ")");
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);
		return maps;
	}

	//未选择整机查询   则列出所有产品
	public Page<Map<String, Object>> findPage(String warehouseName,
			String productCode, Integer isShowZero, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (useLockStock()) {
			sql.append("select p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,");
			sql.append(" w.name warehouse_name,w.sn warehouse_sn,");
			sql.append(" s.id, s.product, s.warehouse, s.actual_stock,");
			sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
			sql.append(" (s.actual_stock - ifnull(a.lock_stock, 0)) useable_stock");
			sql.append(" from xx_stock s");
			sql.append(" left join");
			sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
			sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
			sql.append("  and si.status <> 2 and si.company_info_id = ? group by i.product, si.warehouse) a");
			sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
			sql.append(" left join xx_product p on s.product = p.id");
			sql.append(" left join xx_warehouse w on s.warehouse = w.id");
			sql.append(" where s.company_info_id = ?");

			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		else {
			sql.append("select s.id, s.actual_stock, s.actual_stock useable_stock, (s.actual_stock - s.actual_stock) lock_stock,");
			sql.append("p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,w.name warehouse_name,w.sn warehouse_sn");
			sql.append(" from xx_stock s,xx_product p ,xx_warehouse w");
			sql.append(" where s.product=p.id and s.warehouse=w.id and s.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (isShowZero != null && isShowZero.intValue() == 0) {
			sql.append(" and s.actual_stock <>0");
		}
		String warehouseAuth = storeMemberService.warehouseAuth();
		if (warehouseAuth != null) {
			sql.append(" and s.warehouse in (" + warehouseAuth + ")");
		}

		if (!ConvertUtil.isEmpty(warehouseName)) {
			sql.append(" and w.name like '%" + warehouseName + "%' ");
		}
		if (!ConvertUtil.isEmpty(productCode)) {
			sql.append(" and p.vonder_code like '%" + productCode + "%' ");
		}

		sql.append(" order by s.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalSql = "select count(1) from (" + sql + ") a";
		long total = getNativeDao().findInt(totalSql, objs);
		page.setTotal(total);

		return page;
	}

	//未选择整机查询   则列出所有产品
	public Page<Map<String, Object>> findPage(Long[] warehouseIds,
			Long productId, Integer isShowZero, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (useLockStock()) {
			sql.append("select p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,");
			sql.append(" w.name warehouse_name,w.sn warehouse_sn,");
			sql.append(" s.id, s.product, s.warehouse, s.actual_stock,");
			sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
			sql.append(" (s.actual_stock - ifnull(a.lock_stock, 0)) useable_stock");
			sql.append(" from xx_stock s");
			sql.append(" left join");
			sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
			sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
			sql.append("  and si.status <> 2 and si.company_info_id = ? group by i.product, si.warehouse) a");
			sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
			sql.append(" left join xx_product p on s.product = p.id");
			sql.append(" left join xx_warehouse w on s.warehouse = w.id");
			sql.append(" where s.company_info_id = ?");

			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		else {
			sql.append("select s.id, s.actual_stock, s.actual_stock useable_stock, (s.actual_stock - s.actual_stock) lock_stock,");
			sql.append("p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,w.name warehouse_name,w.sn warehouse_sn");
			sql.append(" from xx_stock s,xx_product p ,xx_warehouse w");
			sql.append(" where s.product=p.id and s.warehouse=w.id and s.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (isShowZero != null && isShowZero.intValue() == 0) {
			sql.append(" and s.actual_stock <>0");
		}
		String warehouseAuth = storeMemberService.warehouseAuth();
		if (warehouseAuth != null) {
			sql.append(" and s.warehouse in (" + warehouseAuth + ")");
		}

		if (productId != null) {

			sql.append(" and p.id = " + productId);
		}
		if (warehouseIds != null && warehouseIds.length > 0) {
			String w = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					w += warehouseIds[i];
				else
					w += warehouseIds[i] + ",";
			}
			sql.append(" and w.id in (" + w + ")");
		}

		sql.append(" order by s.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalSql = "select count(1) from (" + sql + ") a";
		long total = getNativeDao().findInt(totalSql, objs);
		page.setTotal(total);

		return page;
	}

	public Page<Map<String, Object>> findMain(Long[] warehouseIds,
			Long mainProductId, Integer isShowZero, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (useLockStock()) {
			sql.append("select p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,");
			sql.append(" w.name warehouse_name,w.sn warehouse_sn,");
			sql.append(" s.id, s.product, s.warehouse, s.actual_stock,");
			sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
			sql.append(" (s.actual_stock - ifnull(a.lock_stock, 0)) useable_stock");
			sql.append(" from xx_stock s");
			sql.append(" left join");
			sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
			sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
			sql.append("  and si.status <> 2 and si.company_info_id = ? group by i.product, si.warehouse) a");
			sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
			sql.append(" left join xx_product p on s.product = p.id");
			sql.append(" left join xx_warehouse w on s.warehouse = w.id");
			sql.append(" where s.company_info_id = ?");

			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		else {
			sql.append("select s.id, s.actual_stock, s.actual_stock useable_stock, (s.actual_stock - s.actual_stock) lock_stock,");
			sql.append("p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,w.name warehouse_name,w.sn warehouse_sn");
			sql.append(" from xx_stock s,xx_product p ,xx_warehouse w");
			sql.append(" where s.product=p.id and s.warehouse=w.id and s.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (isShowZero != null && isShowZero.intValue() == 0) {
			sql.append(" and s.actual_stock <>0");
		}
		String warehouseAuth = storeMemberService.warehouseAuth();
		if (warehouseAuth != null) {
			sql.append(" and s.warehouse in (" + warehouseAuth + ")");
		}

		if (mainProductId != null) {

			sql.append(" and p.id = " + mainProductId);
		}
		if (warehouseIds != null && warehouseIds.length > 0) {
			String w = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					w += warehouseIds[i];
				else
					w += warehouseIds[i] + ",";
			}
			sql.append(" and w.id in (" + w + ")");
		}

		sql.append(" order by s.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalSql = "select count(1) from (" + sql + ") a";
		long total = getNativeDao().findInt(totalSql, objs);
		page.setTotal(total);

		return page;
	}

	public Page<Map<String, Object>> findSuit(Long[] warehouseIds,
			Long mainProductId, Integer isShowZero, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (useLockStock()) {
			sql.append("select p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,");
			sql.append(" w.name warehouse_name,w.sn warehouse_sn,");
			sql.append(" s.id, s.product, s.warehouse, s.actual_stock,");
			sql.append(" ifnull(a.lock_stock, 0) lock_stock,");
			sql.append(" (s.actual_stock - ifnull(a.lock_stock, 0)) useable_stock");
			sql.append(" from xx_stock s");
			sql.append(" left join");
			sql.append(" (select i.product, si.warehouse, sum(i.quantity - i.shipped_quantity) lock_stock");
			sql.append("  from xx_shipping_item i, xx_shipping si where i.shipping = si.id");
			sql.append("  and si.status <> 2 and si.company_info_id = ? group by i.product, si.warehouse) a");
			sql.append(" on s.product = a.product and s.warehouse = a.warehouse");
			sql.append(" left join xx_product p on s.product = p.id");
			sql.append(" left join xx_warehouse w on s.warehouse = w.id");
			sql.append(" where s.company_info_id = ?");

			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		else {
			sql.append("select s.id, s.actual_stock, s.actual_stock useable_stock, (s.actual_stock - s.actual_stock) lock_stock,");
			sql.append("p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,w.name warehouse_name,w.sn warehouse_sn");
			sql.append(" from xx_stock s,xx_product p ,xx_warehouse w");
			sql.append(" where s.product=p.id and s.warehouse=w.id and s.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (isShowZero != null && isShowZero.intValue() == 0) {
			sql.append(" and s.actual_stock <>0");
		}
		String warehouseAuth = storeMemberService.warehouseAuth();
		if (warehouseAuth != null) {
			sql.append(" and s.warehouse in (" + warehouseAuth + ")");
		}

		if (mainProductId != null) {

			sql.append(" and p.id in (select psi.product from xx_product_suite_item psi"
					+ " left join xx_product_suite ps on ps.id = psi.product_suite "
					+ "where ps.product = "
					+ mainProductId
					+ " and ps.is_enabled = 1 )");
		}
		if (warehouseIds != null && warehouseIds.length > 0) {
			String w = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					w += warehouseIds[i];
				else
					w += warehouseIds[i] + ",";
			}
			sql.append(" and w.id in (" + w + ")");
		}

		sql.append(" order by s.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalSql = "select count(1) from (" + sql + ") a";
		long total = getNativeDao().findInt(totalSql, objs);
		page.setTotal(total);

		return page;
	}

	public Page<Map<String, Object>> findPageIgNoreStock(Long[] productIds,
			Long[] warehouseIds, Integer isShowZero, String vonderCode,
			Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();

		sql.append("select p.full_name product_name,p.sn product_sn,p.model,p.vonder_code,w.name warehouse_name,w.sn warehouse_sn,w.erp_warehouse_code");
		sql.append(" from xx_product p ,xx_warehouse w");
		sql.append(" where 1=1 and p.company_info_id = ? and w.company_info_id = ?");
		list.add(companyInfoId);
		list.add(companyInfoId);

		/*
		 * if (productId != null) { sql.append(" and p.id = ?");
		 * list.add(productId); } if (warehouseId != null) {
		 * sql.append(" and w.id = ?"); list.add(warehouseId); }
		 */
		if (productIds != null && productIds.length > 0) {
			String p = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					p += productIds[i];
				else
					p += productIds[i] + ",";
			}
			sql.append(" and p.id in (" + p + ")");
		}
		if (warehouseIds != null && warehouseIds.length > 0) {
			String w = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					w += warehouseIds[i];
				else
					w += warehouseIds[i] + ",";
			}
			sql.append(" and w.id in (" + w + ")");
		}

		if (vonderCode != null) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		String warehouseAuth = storeMemberService.warehouseAuth();
		if (warehouseAuth != null) {
			sql.append(" and s.warehouse in (" + warehouseAuth + ")");
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalSql = "select count(1) from (" + sql + ") a";
		long total = getNativeDao().findInt(totalSql, objs);
		page.setTotal(total);

		return page;
	}
	
	
	public Page<Map<String, Object>> findPage(Pageable pageable,Long[] warehouseId,
			Long[] organizationId,Long[] productId,Long[] productLevelId,Long[] colorNumbersId,
			Long[] moistureContentId,Long[] newOldLogosIds,Long[] warehouseBatchId) {
		
		List<Object> list = new ArrayList<Object>();
		//登录用户id
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		//组织主体
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM v_warehouse_stock vsk  WHERE 1=1 ");
		//主体
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and vsk.company_info_id = ?");
			list.add(companyInfoId);
		}
		//仓库
		if (!ConvertUtil.isEmpty(warehouseId) && warehouseId.length > 0) {
			String os = "";
			for (int i = 0; i < warehouseId.length; i++) {
					if (i == warehouseId.length - 1){
						os += warehouseId[i];
					}else{
						os += warehouseId[i] + ",";
					}	
			}
			sql.append(" and vsk.warehouseId in (" + os + ")");
		}
		//经营组织
		if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
					if (i == organizationId.length - 1){
						os += organizationId[i];
					}else{
						os += organizationId[i] + ",";
					}	
			}
			sql.append(" and vsk.product_organization_id in (" + os + ")");
		}
		//产品
		if (!ConvertUtil.isEmpty(productId) && productId.length > 0) {
			String os = "";
			for (int i = 0; i < productId.length; i++) {
					if (i == productId.length - 1){
						os += productId[i];
					}else{
						os += productId[i] + ",";
					}	
			}
			sql.append(" and vsk.productid in (" + os + ")");
		}
		//等级
		if (!ConvertUtil.isEmpty(productLevelId) && productLevelId.length > 0) {
			String os = "";
			for (int i = 0; i < productLevelId.length; i++) {
					if (i == productLevelId.length - 1){
						os += productLevelId[i];
					}else{
						os += productLevelId[i] + ",";
					}	
			}
			sql.append(" and vsk.level_Id in (" + os + ")");
		}
		//色号
		if (!ConvertUtil.isEmpty(colorNumbersId) && colorNumbersId.length > 0) {
			String os = "";
			for (int i = 0; i < colorNumbersId.length; i++) {
					if (i == colorNumbersId.length - 1){
						os += colorNumbersId[i];
					}else{
						os += colorNumbersId[i] + ",";
					}	
			}
			sql.append(" and vsk.color_numbers_id in (" + os + ")");
		}
		//含水率
		if (!ConvertUtil.isEmpty(moistureContentId) && moistureContentId.length > 0) {
			String os = "";
			for (int i = 0; i < moistureContentId.length; i++) {
					if (i == moistureContentId.length - 1){
						os += moistureContentId[i];
					}else{
						os += moistureContentId[i] + ",";
					}	
			}
			sql.append(" and vsk.moisture_content_id in (" + os + ")");
		}
		//新旧标识
		if (!ConvertUtil.isEmpty(newOldLogosIds) && newOldLogosIds.length > 0) {
			String os = "";
			for (int i = 0; i < newOldLogosIds.length; i++) {
					if (i == newOldLogosIds.length - 1){
						os += newOldLogosIds[i];
					}else{
						os += newOldLogosIds[i] + ",";
					}	
			}
			sql.append(" and vsk.new_old_logos_id in (" + os + ")");
		}
		//批次
		if (!ConvertUtil.isEmpty(warehouseBatchId) && warehouseBatchId.length > 0) {
			String os = "";
			for (int i = 0; i < warehouseBatchId.length; i++) {
					if (i == warehouseBatchId.length - 1){
						os += warehouseBatchId[i];
					}else{
						os += warehouseBatchId[i] + ",";
					}	
			}
			sql.append(" and vsk.warehouse_batch_id in (" + os + ")");
		}
		
		//用户仓库
		String warehouseStockIds = roleJurisdictionUtil.getWarehouseStockIds();
		if(!ConvertUtil.isEmpty(warehouseStockIds) && !warehouseStockIds.equals("-1")){
			if(!ConvertUtil.isEmpty(warehouseStockIds) && !warehouseStockIds.equals("0")){
				sql.append(" and vsk.warehouseId in (" + warehouseStockIds + ")");
			}else{
				sql.append(" and vsk.warehouseId is null");
			}
		}
		
		//用户机构
		/*String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and vsk.saleOrgId in (" + saleOrgIds + ")");
			}else{
				sql.append(" and vsk.saleOrgId is null");
			}
		}*/
		
		//用户Sbu
		/*String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and vsk.sbuId in (" + sbuIds + ")");
			}else{
				sql.append(" and vsk.sbuId is null");
			}
		}*/
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and vsk.product_organization_id in (" + organizationIdS + ")");
			}else{
				sql.append(" and vsk.product_organization_id is null");
			}
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> pageMap = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		String totalsql = "select count(1) from ( " + sql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		pageMap.setTotal(total);
		
		return pageMap;
	}
	
	
	
	
	private void neaten(Long warehouseId,Long organizationId,Long productId,
			Long productGrade,String vonderCode,String colourNumber,String moistureContent,
			Long[] batchIds,String newOldLogos,Long warehouseLocationId,String batchIdsStr,
			StringBuilder sql,List<Object> list) {
		
		Long companyInfoId = 9L;
		
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" AND vsk.company_info_id = ?");
			list.add(companyInfoId);
		}
		
		//仓库
		if (!ConvertUtil.isEmpty(warehouseId)) {
			sql.append(" and vsk.warehouseId = ?");
			list.add(warehouseId);
		}
		
		//产品
		if (!ConvertUtil.isEmpty(productId)) {
			sql.append(" and vsk.productid = ?");
			list.add(productId);
		}
		
		//编码
		if(!ConvertUtil.isEmpty(vonderCode)){
			sql.append(" and vsk.itemCode = ?");
			list.add(vonderCode);
		}
				
		//等级
		if(!ConvertUtil.isEmpty(productGrade)){
			sql.append(" and vsk.level_Id = ?");
			list.add(productGrade);
		}
		
		//经营组织
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" and vsk.product_organization_id = ?");
			list.add(organizationId);
		}
		
		//色号
		if(!ConvertUtil.isEmpty(colourNumber)){
			sql.append(" and vsk.color_numbers_id = ?");
			list.add(colourNumber);
		}
		
		//含水率
		if(!ConvertUtil.isEmpty(moistureContent)){
			sql.append(" and vsk.moisture_content_id = ?");
			list.add(moistureContent);
		}
		
		//批次
		if (!ConvertUtil.isEmpty(batchIds) && batchIds.length > 0) {
			String os = "";
			for (int i = 0; i < batchIds.length; i++) {
				if (i == batchIds.length - 1){
					os += "'"+batchIds[i]+"'";
				}else{
					os += "'"+batchIds[i] + "',";
				}	
			}
			sql.append(" and vsk.warehouse_batch_id in (" + os + ")");
		}
		
		if(!ConvertUtil.isEmpty(batchIdsStr)){
			sql.append(" and vsk.warehouse_batch_id in (" + batchIdsStr + ")");
		}
		
		// 库位
		if (!ConvertUtil.isEmpty(warehouseLocationId)) {
			sql.append(" and vsk.warehouseLocationId = ?");
			list.add(warehouseLocationId);
		}
				
		//新旧标识
		if(!ConvertUtil.isEmpty(newOldLogos)){
			sql.append(" and vsk.new_old_logos_id = ?");
			list.add(newOldLogos);
		}
	
	}
	
	public List<Map<String, Object>> findViewStock(Long warehouseId,Long organizationId,
			Long productId,Long productGrade,String vonderCode,String colourNumber,
			String moistureContent,Long[] batchIds,String newOldLogos,Long warehouseLocationId,
			String batchIdsStr) {
		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT SUM(onhandQuantity1) totalOnhandQuantity1 ,SUM(attQuantity1) totalAttQuantity1,"
				+ " SUM(attQuantity2) totalAttQuantity2,SUM(attQuantity3) totalAttQuantity3 "
				+ " FROM v_warehouse_stock vsk WHERE 1=1 ");
		
		this.neaten(warehouseId, organizationId, productId, productGrade, vonderCode, 
				colourNumber, moistureContent, batchIds, newOldLogos, warehouseLocationId, 
				batchIdsStr, sql, list);
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),objs,0);
		
		return mapList;
	}
	
	
	public BigDecimal getStockQuantity(Long warehouseId,Long organizationId,
			Long productId,Long productGrade,String colourNumber,String moistureContent,
			Long[] batchIds,String newOldLogos,Long warehouseLocationId,String batchIdsStr,
			Integer columnType) {
		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		
		if(!ConvertUtil.isEmpty(columnType) && columnType == 0){
			sql.append(" SELECT SUM(onhandQuantity1) totalOnhandQuantity1 "
					+ " FROM v_warehouse_stock vsk WHERE 1=1 ");
		}else{
			sql.append(" SELECT SUM(attQuantity1) totalAttQuantity1 "
					+ " FROM v_warehouse_stock vsk WHERE 1=1 ");
		}
		this.neaten(warehouseId, organizationId, productId, productGrade, null, 
				colourNumber, moistureContent, batchIds, newOldLogos, warehouseLocationId, 
				batchIdsStr, sql, list);
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		BigDecimal quantity = getNativeDao().findBigDecimal(sql.toString(),objs);
		
		return quantity;
	}
	
	
	/**
	 * 出入库信息审核库存校验
	 */
	public Integer findViewStockCount(Long Id,Integer sign) {
		
        if(ConvertUtil.isEmpty(Id)){
        	return 1;
        }
           
		StringBuilder sql = new StringBuilder();
		if(!ConvertUtil.isEmpty(sign) && sign==0){
			sql.append(" SELECT  COUNT(*) FROM xx_am_shipping ams JOIN xx_am_shipping_item amsi ON amsi.am_shipping = ams.id  "
			        + "left join ( SELECT amsi.id, sum( ws.attQuantity1 )  attQuantity1 FROM xx_am_shipping ams "
			        + "JOIN xx_am_shipping_item amsi ON amsi.am_shipping = ams.id "
			        + "LEFT JOIN ( SELECT * FROM xx_warehouse_bill_batch GROUP BY am_shipping_item ) wbi "
			        + "ON wbi.am_shipping_item = amsi.id JOIN v_warehouse_stock ws ON ws.warehouseId = ams.warehouse  "
			        + "AND ws.productid = amsi.product  AND ws.organizationId = amsi.product_organization  "
			        + "AND ws.level_Id = amsi.product_level  AND ws.color_numbers_id = amsi.color_numbers  "
			        + "AND ws.moisture_content_id = amsi.moisture_contents  AND ws.new_old_logos_id = amsi.new_old_logos "
			        + " AND ( ws.warehouse_batch_id = wbi.warehouse_batch OR wbi.warehouse_batch IS NULL )  "
			        + "where ws.attQuantity1>=0 and  ams.id = "+Id +" GROUP BY amsi.id   ) t on amsi.id=t.id "
			        + "where  t.id is null and  ams.id = "+Id +" ");
		}else if(!ConvertUtil.isEmpty(sign) && sign==1){
			sql.append(" SELECT COUNT(*) FROM xx_am_shipping ams ");
			sql.append(" JOIN xx_am_shipping_item amsi ON amsi.am_shipping = ams.id ");
			sql.append(" JOIN xx_warehouse_batch_item wbi ON wbi.am_shipping_item = amsi.id ");
			sql.append(" JOIN v_warehouse_stock ws ON ws.warehouseId = ams.warehouse   ");
			sql.append(" AND ws.productid = amsi.product  ");
			sql.append(" AND ws.organizationId = amsi.product_organization  ");
			sql.append(" AND ws.level_Id = amsi.product_level  ");
			sql.append(" AND ws.color_numbers_id = amsi.color_numbers  ");
			sql.append(" AND ws.moisture_content_id = amsi.moisture_contents  ");
			sql.append(" AND ws.new_old_logos_id = amsi.new_old_logos ");
			sql.append(" AND ws.warehouse_batch_id=wbi.warehouse_batch ");
			sql.append(" WHERE (ws.attQuantity1<0 OR ws.attQuantity1 IS NULL) ");
			if(!ConvertUtil.isEmpty(Id)){
				sql.append(" and ams.id = "+Id);
			}
		}else{
			return 1;
		}
		return getNativeDao().findInt(sql.toString(), null);
	}
	
}