package net.shopxx.stock.dao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
@Repository("warehouseBatchItemDao")
public class WarehouseBatchItemDao extends DaoCenter{
    
    
    public Page<Map<String, Object>> findPage(Long warehouseId ,Long productId,
            Long organizationId,Long productLevelId,Long[] colorNumbersIds,
            Long[] moistureContentsIds,Long[] warehouseBatchIds,Long[] newOldLogosIds,
            String startTime, String endTime,Pageable pageable,String batchEncoding,
            String billType,String documentNo,Boolean flag,Long productionPlantId,Long warehouseLocationId,String memo) {
        
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT vwbi.*,now() billDate, ");
        sql.append(" FLOOR( CASE WHEN vwbi.per_box IS NOT NULL THEN IFNULL(vwbi.available_quantity, 0) / vwbi.per_box ELSE 0 END ) i_available_box_quantity, ");
        sql.append(" FLOOR( CASE WHEN vwbi.per_branch IS NOT NULL THEN  IFNULL(vwbi.available_quantity, 0)  / vwbi.per_branch ELSE 0 END ) i_available_branch_quantity ");
//      sql.append(" FROM v_warehouse_batch_item vwbi left join xx_am_shipping xam on xam.id = vwbi.am_shipping ");
        sql.append(" FROM v_warehouse_batch_item_stock vwbi ");
        sql.append(" WHERE 1=1 ");
        
        
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" and vwbi.company_info_id = ? ");
            list.add(companyInfoId);
        }
        
        //生产工厂
        if (!ConvertUtil.isEmpty(productionPlantId)) {
            sql.append(" and vwbi.production_plant = ? ");
            list.add(productionPlantId);
        }
        
        //可用数量
        if(!ConvertUtil.isEmpty(flag) && flag){
            sql.append(" and vwbi.available_quantity>0 ");
        }
        
        //来源单号
//      if (!ConvertUtil.isEmpty(documentNo)) {
//          sql.append(" and vwbi.document_no like ? ");
//          list.add('%'+documentNo+'%');
//      }
        
        //仓库
        if (!ConvertUtil.isEmpty(warehouseId)) {
            sql.append(" and vwbi.warehouse = ? ");
            list.add(warehouseId);
        }
        
        //单据类型
//      if (!ConvertUtil.isEmpty(billType)) {
//          sql.append(" and vwbi.bill_type = ? ");
//          list.add(billType);
//      }
//      
        //批次编码
        if (!ConvertUtil.isEmpty(batchEncoding)) {
            sql.append(" and vwbi.batch_encoding like ? ");
            list.add('%'+batchEncoding+'%');
        }
        
        //产品
        if (!ConvertUtil.isEmpty(productId)) {
            sql.append(" and vwbi.product = ? ");
            list.add(productId);
        }
        
        //经营组织
        if (!ConvertUtil.isEmpty(organizationId)) {
            sql.append(" and vwbi.organization = ? ");
            list.add(organizationId);
        }
        
        //产品等级
        if (!ConvertUtil.isEmpty(productLevelId)) {
            sql.append(" and vwbi.product_level = ? ");
            list.add(productLevelId);
        }

        //备注
        if (!ConvertUtil.isEmpty(memo)){
            sql.append(" and vwbi.memo like ?");
            list.add('%'+memo+'%');
        }
        
        // 库位
        if (!ConvertUtil.isEmpty(warehouseLocationId)) {
            sql.append(" and vwbi.warehouseLocationId = ? ");
            list.add(warehouseLocationId);
        }
        
        //色号
        if (!ConvertUtil.isEmpty(colorNumbersIds) && colorNumbersIds.length > 0) {
            String os = "";
            for (int i = 0; i < colorNumbersIds.length; i++) {
                if (i == colorNumbersIds.length - 1){
                    os += colorNumbersIds[i];
                }else{
                    os += colorNumbersIds[i] + ",";
                }   
            }
            sql.append(" and vwbi.color_numbers in (" + os + ")");
        }
        
        //含水率
        if (!ConvertUtil.isEmpty(moistureContentsIds) && moistureContentsIds.length > 0) {
            String os = "";
            for (int i = 0; i < moistureContentsIds.length; i++) {
                if (i == moistureContentsIds.length - 1){
                    os += moistureContentsIds[i];
                }else{
                    os += moistureContentsIds[i] + ",";
                }   
            }
            sql.append(" and vwbi.moisture_contents in (" + os + ")");
        }
        
        //批次
        if (!ConvertUtil.isEmpty(warehouseBatchIds) && warehouseBatchIds.length > 0) {
            String os = "";
            for (int i = 0; i < warehouseBatchIds.length; i++) {
                if (i == warehouseBatchIds.length - 1){
                    os += warehouseBatchIds[i];
                }else{
                    os += warehouseBatchIds[i] + ",";
                }   
            }
            sql.append(" and vwbi.warehouse_batch in (" + os + ")");
        }
        
        //新旧标识
        if (!ConvertUtil.isEmpty(newOldLogosIds) && newOldLogosIds.length > 0) {
            String os = "";
            for (int i = 0; i < newOldLogosIds.length; i++) {
                if (i == newOldLogosIds.length - 1){
                    os += newOldLogosIds[i];
                }else{
                    os += newOldLogosIds[i] + ",";
                }   
            }
            sql.append(" and vwbi.new_old_logos in (" + os + ")");
        }
        
//
//      //创建时间
//      if (!ConvertUtil.isEmpty(startTime)) {
//          sql.append(" and vwbi.create_date >= ?");
//          list.add(startTime + " 00:00:00");
//      }
//      if (!ConvertUtil.isEmpty(endTime)) {
//          sql.append(" and vwbi.create_date <= ?");
//          list.add(endTime + " 23:59:59");
//      }
        
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
        String totalsql = "select count(1) from ( " + sql + ") t";
        long total = getNativeDao().findInt(totalsql, objs);
        page.setTotal(total);
        return page;
    }
    
    public List<Map<String, Object>> findVWarehouseBatchItemList(Long amShippingId,
            Long warehouseId ,Long productId,Long organizationId,Long productLevelId,
            Long colorNumbersId,Long moistureContentsId,Long[] warehouseBatchIds,
            Long newOldLogosId,Long billTypeId,Boolean flag,Long productionPlantId,Long warehouseLocationId) {
        
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = 9L;
        StringBuilder sql = new StringBuilder();
        
        //自动匹配批次
        if(!ConvertUtil.isEmpty(flag) && flag){
//            sql.append(" SELECT vwbi.*,vwbi.document_date billDate from v_warehouse_batch_item vwbi left join xx_am_shipping xas on vwbi.am_shipping = xas.id where 1=1 and vwbi.available_quantity>0 ");
            
            sql.append("SELECT DISTINCT vwbi.*, vwbi.document_date billDate  FROM xx_am_shipping xas LEFT JOIN xx_am_shipping_item amsi ON amsi.am_shipping = xas.id LEFT JOIN v_warehouse_batch_item_stock vwbi on "
                    + " vwbi.warehouse =xas.warehouse  AND vwbi.product = amsi.product  AND vwbi.organization = amsi.product_organization  AND vwbi.product_level = amsi.product_level  AND vwbi.color_numbers = amsi.color_numbers "
                    + " AND vwbi.moisture_contents = amsi.moisture_contents  AND vwbi.new_old_logos = amsi.new_old_logos   LEFT JOIN xx_warehouse_batch_item wbi ON vwbi.warehouse = wbi.warehouse  AND vwbi.product = wbi.product "
                    + " AND vwbi.organization = wbi.organization  AND vwbi.product_level = wbi.product_level  AND vwbi.color_numbers = wbi.color_numbers  AND vwbi.moisture_contents = wbi.moisture_contents  AND vwbi.new_old_logos = wbi.new_old_logos "
                    + " AND ( wbi.id IS NULL OR wbi.warehouse_batch = vwbi.warehouse_batch )  AND ( wbi.id IS NULL OR wbi.warehouse_location = vwbi.warehouseLocationId )" +
                    " left join xx_warehouse_batch wb on wb.id = vwbi.warehouse_batch  WHERE 1 = 1  AND vwbi.available_quantity > 0 ");
            //生产工厂
            if (!ConvertUtil.isEmpty(productionPlantId)) {
                sql.append(" and vwbi.production_plant = ? ");
                list.add(productionPlantId);
            }
        }else{
//            sql.append(" SELECT vwbi.*,p.per_branch,p.branch_per_box,p.per_box,pl.value AS product_level_name,wb.batch_encoding, ");
//            sql.append(" cn.value AS color_numbers_name,mc.value AS moisture_contents_name,nol.value AS new_old_logos_name, ");
//            sql.append(" wb.sn  warehouse_batch_sn,o.name organization_name,wb.completion_date,wb.memo, ");
//            sql.append(" vwb.enter_quantity i_enter_quantity,vwb.out_quantity i_out_quantity,vwb.occupy_quantity i_occupy_quantity, ");
//            sql.append(" vwb.available_quantity i_available_quantity,xwl.id warehouseLocationId,xwl.code warehouseLocationCode,inBatch.document_date billDate, ");
//            sql.append(" FLOOR( CASE WHEN vwb.per_box IS NOT NULL THEN IFNULL(vwb.available_quantity, 0) / vwb.per_box ELSE 0 END ) i_available_box_quantity,  ");
//            sql.append(" FLOOR( CASE WHEN vwb.per_branch IS NOT NULL THEN  IFNULL(vwb.available_quantity, 0)  / vwb.per_branch ELSE 0 END ) i_available_branch_quantity ");
//            sql.append(" FROM xx_warehouse_batch_item vwbi ");
//            sql.append(" LEFT JOIN xx_system_dict bt ON bt.id = vwbi.bill_type ");
//            sql.append(" LEFT JOIN xx_product p ON p.id = vwbi.product ");
//            sql.append(" LEFT JOIN xx_system_dict pl ON pl.id = vwbi.product_level ");
//            sql.append(" LEFT JOIN xx_system_dict cn ON cn.id = vwbi.color_numbers ");
//            sql.append(" LEFT JOIN xx_system_dict mc ON mc.id = vwbi.moisture_contents ");
//            sql.append(" LEFT JOIN xx_warehouse_batch wb ON wb.id = vwbi.warehouse_batch ");
//            sql.append(" LEFT JOIN xx_system_dict nol  ON nol.id = vwbi.new_old_logos ");
//            sql.append(" LEFT JOIN xx_organization o ON o.id = vwbi.organization ");
//            sql.append(" LEFT JOIN v_warehouse_batch_item_stock vwb ON vwbi.warehousing_id = vwb.id ");
//            sql.append(" LEFT JOIN xx_warehouse_location xwl ON xwl.id = vwbi.warehouse_location ");
//            sql.append(" LEFT JOIN xx_warehouse_batch_item inBatch ON inBatch.id = vwbi.warehousing_id ");
//            sql.append(" LEFT JOIN xx_am_shipping xas ON xas.id = inBatch.am_shipping ");
//-----------------------1.1-------------------------------------------------------------------
            sql.append("SELECT vwbi.*, p.per_branch, p.branch_per_box, p.per_box, pl.VALUE AS product_level_name, wb.batch_encoding, cn.VALUE AS color_numbers_name, "
                    + " mc.VALUE AS moisture_contents_name, nol.VALUE AS new_old_logos_name, wb.sn warehouse_batch_sn, o.NAME organization_name, wb.completion_date, "
                    + " wb.memo, vwb.enter_quantity i_enter_quantity, vwb.out_quantity i_out_quantity, vwb.occupy_quantity i_occupy_quantity, vwb.available_quantity i_available_quantity,"
                    + " xwl.id warehouseLocationId, xwl.CODE warehouseLocationCode, vwb.document_date billDate, FLOOR( CASE WHEN vwb.per_box IS NOT NULL THEN IFNULL( vwb.available_quantity, 0 ) / vwb.per_box ELSE 0 END ) i_available_box_quantity, "
                    + " FLOOR( CASE WHEN vwb.per_branch IS NOT NULL THEN IFNULL( vwb.available_quantity, 0 ) / vwb.per_branch ELSE 0 END ) i_available_branch_quantity"
                    + " FROM xx_am_shipping xas JOIN xx_am_shipping_item amsi ON amsi.am_shipping = xas.id JOIN xx_warehouse_batch_item vwbi ON vwbi.am_shipping_item = amsi.id "
                    + " LEFT JOIN xx_system_dict bt ON bt.id = vwbi.bill_type LEFT JOIN xx_product p ON p.id = vwbi.product LEFT JOIN xx_system_dict pl ON pl.id = vwbi.product_level LEFT JOIN xx_system_dict cn ON cn.id = vwbi.color_numbers "
                    + " LEFT JOIN xx_system_dict mc ON mc.id = vwbi.moisture_contents LEFT JOIN xx_warehouse_batch wb ON wb.id = vwbi.warehouse_batch LEFT JOIN xx_system_dict nol ON nol.id = vwbi.new_old_logos LEFT JOIN xx_organization o "
                    + " ON o.id = vwbi.organization LEFT JOIN xx_warehouse_location xwl ON xwl.id = vwbi.warehouse_location LEFT JOIN v_warehouse_batch_item_stock vwb ON vwb.warehouse = xas.warehouse AND vwb.product = amsi.product"
                    + " AND vwb.organization = amsi.product_organization AND vwb.product_level = amsi.product_level AND vwb.color_numbers = amsi.color_numbers AND vwb.moisture_contents = amsi.moisture_contents "
                    + " AND vwb.new_old_logos = amsi.new_old_logos AND (vwb.warehouse_batch = vwbi.warehouse_batch or vwbi.id is null)  ");
            sql.append(" WHERE 1=1 ");


            //生产工厂
            if (!ConvertUtil.isEmpty(productionPlantId)) {
                sql.append(" and wb.production_plant = ? ");
                list.add(productionPlantId);
            }
        }
        //主体
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" and vwbi.company_info_id = ? ");
            list.add(companyInfoId);
        }
        
        //出入库单
        if (!ConvertUtil.isEmpty(amShippingId)) {
            sql.append(" and xas.id = ? ");
            list.add(amShippingId);
        }
        //单据类型
//        if (!ConvertUtil.isEmpty(billTypeId)) {
//            sql.append(" and xas.bill_type = ? ");
//            list.add(billTypeId);
//        }
        //仓库
        if (!ConvertUtil.isEmpty(warehouseId)) {
            sql.append(" and vwbi.warehouse = ? ");
            list.add(warehouseId);
        }
        //产品
        if (!ConvertUtil.isEmpty(productId)) {
            sql.append(" and vwbi.product = ? ");
            list.add(productId);
        }
        //等级
        if (!ConvertUtil.isEmpty(productLevelId)) {
            sql.append(" and vwbi.product_level = ? ");
            list.add(productLevelId);
        }
        // 库位
        if (!ConvertUtil.isEmpty(warehouseLocationId)) {
            sql.append(" and vwbi.warehouseLocationId = ? ");
            list.add(warehouseLocationId);
        }
        
        //经营组织
        if (!ConvertUtil.isEmpty(organizationId)) {
            sql.append(" and vwbi.organization = ? ");
            list.add(organizationId);
        }
        //色号
        if (!ConvertUtil.isEmpty(colorNumbersId)) {
            sql.append(" and vwbi.color_numbers = ? ");
            list.add(colorNumbersId);
        }
        //含水率
        if (!ConvertUtil.isEmpty(moistureContentsId)) {
            sql.append(" and vwbi.moisture_contents = ? ");
            list.add(moistureContentsId);
        }
        //批次
        if (!ConvertUtil.isEmpty(warehouseBatchIds) && warehouseBatchIds.length > 0) {
            String os = "";
            for (int i = 0; i < warehouseBatchIds.length; i++) {
                if (i == warehouseBatchIds.length - 1){
                    os += warehouseBatchIds[i];
                }else{
                    os += warehouseBatchIds[i] + ",";
                }   
            }
            sql.append(" and vwbi.warehouse_batch in (" + os + ")");
        }
        //新旧标识
        if (!ConvertUtil.isEmpty(newOldLogosId)) {
            sql.append(" and vwbi.new_old_logos = ? ");
            list.add(newOldLogosId);
        }

        sql.append(" order by wb.production_date,wb.sn ");

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        
        List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),objs,0);
        
        return mapList;
    }
    
    public List<Map<String, Object>> findVWarehouseBatchItemListBySourceNoId(Long sourceNoId,
			Long billTypeId,Long[] ids) {
        
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = 9L;
        StringBuilder sql = new StringBuilder();
        


            sql.append(" SELECT"
            		+ " wbi.*,"
            		+ " p.per_branch,"
            		+ " p.branch_per_box,"
            		+ " p.per_box,"
            		+ " pl.VALUE AS product_level_name,"
            		+ " wb.completion_date,"
            		+ " wb.sn warehouse_batch_sn,"
            		+ " o.NAME organization_name,"
            		+ " cn.VALUE color_numbers_name,"
            		+ " mc.VALUE moisture_contents_name,"
            		+ " nol.VALUE new_old_logos_name,"
            		+ " wb.batch_encoding,"
            		+ " IFNULL(wbi.quantity,0)-IFNULL(wbi.shipped_quantity,0) lost_quantity,"
            		+ " wb.memo"
            		+ " FROM"
            		+ " xx_warehouse_batch_item wbi"
            		+ " LEFT JOIN xx_warehouse_batch wb ON wbi.warehouse_batch = wb.id"
            		+ " LEFT JOIN xx_product p ON p.id = wbi.product"
            		+ " LEFT JOIN xx_system_dict pl ON pl.id = wbi.product_level"
            		+ " LEFT JOIN xx_am_shipping_item ai ON wbi.out_am_shipping_item = ai.id"
            		+ " LEFT JOIN xx_am_shipping a ON a.id = ai.am_shipping"
            		+ " LEFT JOIN xx_move_library_item mi ON ai.move_library_item_issue = mi.id"
            		+ " LEFT JOIN xx_move_library m ON mi.move_library = m.id"
            		+ " LEFT JOIN xx_organization o ON wbi.organization = o.id"
            		+ " LEFT JOIN xx_system_dict cn ON cn.id = wbi.color_numbers"
            		+ " LEFT JOIN xx_system_dict mc ON mc.id = wbi.moisture_contents"
            		+ " LEFT JOIN xx_system_dict nol ON nol.id = wbi.new_old_logos");
            sql.append(" WHERE 1=1 and (wbi.shipped_quantity < wbi.quantity or wbi.shipped_quantity is null) and a.status=1");
//            //移库单
//            if (!ConvertUtil.isEmpty(sourceNoId)) {
//                sql.append(" and m.id = ? ");
//                list.add(sourceNoId);
//            }
            
          //新旧标识
            if (!ConvertUtil.isEmpty(ids) && ids.length > 0) {
                String os = "";
                for (int i = 0; i < ids.length; i++) {
                    if (i == ids.length - 1){
                        os += ids[i];
                    }else{
                        os += ids[i] + ",";
                    }   
                }
                sql.append(" and mi.id in (" + os + ")");
            }
       
       
       
      
        
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        
        List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),objs,0);
        
        return mapList;
    }
    
    

}
