package net.shopxx.stock.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository("stockAgeDao")
public class StockAgeDao extends DaoCenter {
	
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	public Page<Map<String, Object>> findStockAgeList(String startTime, String endTime,
			Long[] saleOrgId,Long[] storeId,Long[] organizationId,Long[] warehouseId,
			Long[] productCategoryId,Long[] vProductId,Long[] productId,String woodTypeOrColor,
			String model,Long[] ids,Pageable pageable){
		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		
		sql.append("select vage.`库存组织` warehouseOrganization,vage.仓库名称 warehouseName,vage.物料编码 vonder_code,"
				+ " vage.产品描述 description,vage.名称 product_name,vage.型号 model,vage.规格 spec,vage.`木种/花色` wood_type_or_color,"
				+ " vage.`0-0.5年` age_halfYear,vage.`0.5-1年` age_oneYear,vage.`1-2年` age_twoYear,vage.`2-3年` age_threeYear,"
				+ " vage.`3-5年` age_fiveYear,vage.`5-8年` age_eightYear,vage.`8-10年` age_tenYear,vage.`10年以上` age_thanTenYear,"
				+ " vage.`品类` root_product_category_name,vage.`系列` product_category_name,vage.`长度mm` erp_length,vage.`宽度mm` erp_width,vage.`厚度mm` erp_height "
				+ " from v_warehouse_age vage where 1=1 ");
		
		Date now = new Date();
		DateFormat dfStart = new SimpleDateFormat("yyyy-MM-dd");
	    String nowDate = dfStart.format(now);
	    
	  //仓库组织
	  		if (organizationId != null && organizationId.length > 0) {
	  			String os = "";
	  			for (int i = 0; i < organizationId.length; i++) {
	  				if (i == organizationId.length - 1)
	  					os += "'"+organizationId[i]+"'";
	  				else
	  					os += "'"+organizationId[i] + "',";
	  			}
	  			sql.append(" and  `vage`.`经营组织ID` in (" + os + ")");
	  		}
	  		
	  	//仓库
			if (warehouseId != null && warehouseId.length > 0) {
				String os = "";
				for (int i = 0; i < warehouseId.length; i++) {
					if (i == warehouseId.length - 1)
						os += "'"+warehouseId[i]+"'";
					else
						os += "'"+warehouseId[i] + "',";
				}
				sql.append(" and  `vage`.`仓库ID` in (" + os + ")");
			}
			
			//品类
			if (productCategoryId != null && productCategoryId.length > 0) {
				String os = "";
				for (int i = 0; i < productCategoryId.length; i++) {
					if (i == productCategoryId.length - 1)
						os += "'"+productCategoryId[i]+"'";
					else
						os += "'"+productCategoryId[i] + "',";
				}
				sql.append(" and  `vage`.`品类ID` in (" + os + ")");
			}
			
			//系列
			if (vProductId != null && vProductId.length > 0) {
				String os = "";
				for (int i = 0; i < vProductId.length; i++) {
					if (i == vProductId.length - 1)
						os += "'"+vProductId[i]+"'";
					else
						os += "'"+vProductId[i] + "',";
				}
				sql.append(" and  `vage`.`系列ID` in (" + os + ")");
			}
			
			//货物
			if (productId != null && productId.length > 0) {
				String os = "";
				for (int i = 0; i < productId.length; i++) {
					if (i == productId.length - 1)
						os += "'"+productId[i]+"'";
					else
						os += "'"+productId[i] + "',";
				}
				sql.append(" and  `vage`.`产品ID` in (" + os + ")");
			}
			
			//木种名称
			if (!ConvertUtil.isEmpty(woodTypeOrColor)) {
				sql.append(" and `vage`.`木种/花色` like ? ");
				list.add("%" + woodTypeOrColor + "%");
			}
			
			//货物型号
			if (!ConvertUtil.isEmpty(model)) {
				sql.append(" and `vage`.`型号` like ? ");
				list.add("%" + model + "%");
			}
			
			//用户仓库
			String warehouseIds = roleJurisdictionUtil.getWarehouseIds();
			if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("-1")){
				if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("0")){
					sql.append(" and `vage`.`仓库ID` in (" + warehouseIds + ")");
				}else{
					sql.append(" and `vage`.`仓库ID` is null");
				}
			}
			
//			//用户机构
//			String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
//			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
//				if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
//					sql.append(" and `vage`.`机构ID` in (" + saleOrgIds + ")");
//				}else{
//					sql.append(" and `vage`.`机构ID` is null");
//				}
//			}
			
			//用户Sbu
			String sbuIds = roleJurisdictionUtil.getSbuIds();
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
				if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
					sql.append(" and `vage`.`sbuID` in (" + sbuIds + ")");
				}else{
					sql.append(" and `vage`.`sbuID` is null");
				}
			}
			
			//用户经营组织
			String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
				if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
					sql.append(" and `vage`.`经营组织ID` in (" + organizationIdS + ")");
				}else{
					sql.append(" and `vage`.`经营组织ID` is null");
				}
			}
			
		
			
			Object[] objs = new Object[list.size()];
			for (int i = 0; i < list.size(); i++) {
				objs[i] = list.get(i);
			}
			Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
			
			String totalsql = "select count(1) from ( " + sql + ") t";
			
			long total = getNativeDao().findInt(totalsql, objs);
			
			page.setTotal(total);
			
			return page;							
	}
	
	
	
	public Integer findStockAgeListCount(String startTime, String endTime,
			Long[] saleOrgId,Long[] storeId,Long[] organizationId,Long[] warehouseId,
			Long[] productCategoryId,Long[] vProductId,Long[] productId,String woodTypeOrColor,
			String model,Long[] ids){
		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();		
		sql.append("select count(*) from v_warehouse_age vage where 1=1 ");		
	  //仓库组织
	  		if (organizationId != null && organizationId.length > 0) {
	  			String os = "";
	  			for (int i = 0; i < organizationId.length; i++) {
	  				if (i == organizationId.length - 1)
	  					os += "'"+organizationId[i]+"'";
	  				else
	  					os += "'"+organizationId[i] + "',";
	  			}
	  			sql.append(" and  `vage`.`经营组织ID` in (" + os + ")");
	  		}
	  		
	  	//仓库
			if (warehouseId != null && warehouseId.length > 0) {
				String os = "";
				for (int i = 0; i < warehouseId.length; i++) {
					if (i == warehouseId.length - 1)
						os += "'"+warehouseId[i]+"'";
					else
						os += "'"+warehouseId[i] + "',";
				}
				sql.append(" and  `vage`.`仓库ID` in (" + os + ")");
			}
			
			//品类
			if (productCategoryId != null && productCategoryId.length > 0) {
				String os = "";
				for (int i = 0; i < productCategoryId.length; i++) {
					if (i == productCategoryId.length - 1)
						os += "'"+productCategoryId[i]+"'";
					else
						os += "'"+productCategoryId[i] + "',";
				}
				sql.append(" and  `vage`.`品类ID` in (" + os + ")");
			}
			
			//系列
			if (vProductId != null && vProductId.length > 0) {
				String os = "";
				for (int i = 0; i < vProductId.length; i++) {
					if (i == vProductId.length - 1)
						os += "'"+vProductId[i]+"'";
					else
						os += "'"+vProductId[i] + "',";
				}
				sql.append(" and  `vage`.`系列ID` in (" + os + ")");
			}
			
			//货物
			if (productId != null && productId.length > 0) {
				String os = "";
				for (int i = 0; i < productId.length; i++) {
					if (i == productId.length - 1)
						os += "'"+productId[i]+"'";
					else
						os += "'"+productId[i] + "',";
				}
				sql.append(" and  `vage`.`产品ID` in (" + os + ")");
			}
			
			//木种名称
			if (!ConvertUtil.isEmpty(woodTypeOrColor)) {
				sql.append(" and `vage`.`木种/花色` like ? ");
				list.add("%" + woodTypeOrColor + "%");
			}
			
			//货物型号
			if (!ConvertUtil.isEmpty(model)) {
				sql.append(" and `vage`.`型号` like ? ");
				list.add("%" + model + "%");
			}
			
			//用户仓库
			String warehouseIds = roleJurisdictionUtil.getWarehouseIds();
			if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("-1")){
				if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("0")){
					sql.append(" and `vage`.`仓库ID` in (" + warehouseIds + ")");
				}else{
					sql.append(" and `vage`.`仓库ID` is null");
				}
			}
			
			//用户机构
			String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
				if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
					sql.append(" and `vage`.`机构ID` in (" + saleOrgIds + ")");
				}else{
					sql.append(" and `vage`.`机构ID` is null");
				}
			}
			
			//用户Sbu
			String sbuIds = roleJurisdictionUtil.getSbuIds();
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
				if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
					sql.append(" and `vage`.`sbuID` in (" + sbuIds + ")");
				}else{
					sql.append(" and `vage`.`sbuID` is null");
				}
			}
			
			//用户经营组织
			String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
				if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
					sql.append(" and `vage`.`经营组织ID` in (" + organizationIdS + ")");
				}else{
					sql.append(" and `vage`.`经营组织ID` is null");
				}
			}
			
		
			
			Object[] objs = new Object[list.size()];
			for (int i = 0; i < list.size(); i++) {
				objs[i] = list.get(i);
			}
			int count = getNativeDao().findInt(sql.toString());						
						
			return count;							
	}

}
