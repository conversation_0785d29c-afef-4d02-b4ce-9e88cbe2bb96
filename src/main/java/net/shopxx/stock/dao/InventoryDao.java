package net.shopxx.stock.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Repository("inventoryDao")
public class InventoryDao extends DaoCenter {

    @Resource(name = "roleJurisdictionUtil")
    RoleJurisdictionUtil roleJurisdictionUtil;

    public List<Map<String, Object>> findInventoryItemListById(Long inventoryId, Boolean isDefault) {
        StringBuilder sql = new StringBuilder();
        sql.append("select "
                        + " it.* ,ip.cost,ip.market_price,"
                        + " ip.per_branch,ip.manufactory_name,ip.supplier,w.name warehouse_name,ip.per_box,ip.unit, "
                        + " sdt.id level_Id,sdt.value levelName,cn.id color_numbers,cn.value color_numbers_name,mc.id moisture_contents_id,"
                        + " mc.value moisture_content_name,nol.id new_old_logos_id,nol.value new_old_logos_name,bd.value business_division_value,i.sn inventory_sn, "
                        + " it.status inventoryItemStatus,ip.unit product_unit,ip.vonder_code,ip.name product_name,ip.description product_description, "
                        + " ip.wood_type_or_color,ip.model product_model,ip.spec product_spec,ip.product_grade productGrade, "
                        + " po.id product_organization_id,po.name product_organization_name,xwl.id warehouseLocationId,xwl.code warehouseLocationCode"
                        + " from xx_inventory_item it "
                        + " left join xx_inventory i on it.inventory=i.id "
                        + " left join xx_inventory sh on sh.id=it.inventory and sh.status<>2"
                        + " left join xx_warehouse w on it.warehouse=w.id"
                        + " left join xx_product ip on it.product=ip.id "
                        + " left join xx_system_dict sdt on sdt.id = it.product_level "
                        + " left join xx_system_dict bd on ip.business_division=bd.id "
                        + " LEFT JOIN xx_organization po ON po.id = it.product_organization "
                        + " LEFT JOIN xx_system_dict cn ON cn.id = it.color_numbers "
                        + " LEFT JOIN xx_system_dict mc ON mc.id = it.moisture_contents "
                        + " LEFT JOIN xx_system_dict nol ON nol.id = it.new_old_logos "
                        + " LEFT JOIN xx_warehouse_location xwl ON xwl.id = it.warehouse_location ");
        sql.append(" where 1=1 ");
        sql.append(" and it.inventory = " + inventoryId );

        if(!ConvertUtil.isEmpty(isDefault)){
            if(isDefault){
                //用户经营组织
                String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
                if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
                    if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
                        sql.append(" and po.id in (" + organizationIdS + ")");
                    }else{
                        sql.append(" and po.id is null");
                    }
                }
            }
        }

        sql.append(" group by it.id order by ip.vonder_code,it.line_no");
        return getNativeDao().findListMap(sql.toString(),
                null,
                0);
    }
}
