package net.shopxx.stock.dao;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
@Repository("warehouseBatchDao")
public class WarehouseBatchDao extends DaoCenter {
	
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	public StringBuilder neaten() {
		StringBuilder sql = new StringBuilder();
		sql.append(" select xwb.*,factory.id factory_id,factory.value factory_name,category.id category_id, ");
		sql.append(" case xwb.batch_status when TRUE then '是'  when false then '否'  else '' end as batchStatus, ");
		sql.append(" category.value category_name,sm.name store_name,po.name organization_name ");
		sql.append(" from xx_warehouse_batch xwb ");
		sql.append(" left join xx_system_dict factory on factory.id = xwb.production_plant ");
		sql.append(" left join xx_system_dict category on category.id = xwb.category_of_goods ");
		sql.append(" left join xx_store_member sm on sm.id = xwb.store_member ");
		sql.append(" left join xx_organization po on po.id = xwb.organization  where po.id is not null ");
		
		return sql;
	}
	
	
	//获取列表数据
	public Page<Map<String, Object>> findPage(String batchEncoding, Long[] productionPlantId, 
			String productionNo, Boolean batchStatus, Long[] categoryOfGoodsId, String startProductionDate, 
			String endProductionDate, String startCompletionDate, String endCompletionDate, String startEffectiveDate,
			String endEffectiveDate, String storeName, Long[] organizationId,Long[] batchIds,String memo, Pageable pageable){
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = this.neaten();
		//主体
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and xwb.company_info_id = ?");
			list.add(companyInfoId);
		}
		//批次编码
		if (!ConvertUtil.isEmpty(batchEncoding)) {
			 sql.append(" and xwb.batch_encoding like ? ");
			 list.add("%" + batchEncoding + "%");
		}
		//生产工厂
		if (!ConvertUtil.isEmpty(productionPlantId) && productionPlantId.length > 0) {
			String ox = "";
			for (int i = 0; i < productionPlantId.length; i++) {
				if (i == productionPlantId.length - 1)
					ox += productionPlantId[i];
				else
					ox += productionPlantId[i] + ",";
			}
			sql.append(" and factory.id in (" + ox + ")");
		}
		// 批次id
		if (!ConvertUtil.isEmpty(batchIds) && batchIds.length > 0) {
			String ids = "";
			for (int i = 0; i < batchIds.length; i++) {
				if (i == batchIds.length - 1)
					ids += batchIds[i];
				else
					ids += batchIds[i] + ",";
			}
			sql.append(" and xwb.id in (" + ids + ")");
			System.out.println(ids);
		}
		//生产单号
		if (!ConvertUtil.isEmpty(productionNo)) {
			 sql.append(" and xwb.production_no like ? ");
			 list.add("%" + productionNo + "%");
		}
		//货物类型
		if (!ConvertUtil.isEmpty(categoryOfGoodsId) && categoryOfGoodsId.length > 0) {
			String ox = "";
			for (int i = 0; i < categoryOfGoodsId.length; i++) {
				if (i == categoryOfGoodsId.length - 1)
					ox += categoryOfGoodsId[i];
				else
					ox += categoryOfGoodsId[i] + ",";
			}
			sql.append(" and xwb.category_of_goods in (" + ox + ")");
		}
		//是否有效
		if (!ConvertUtil.isEmpty(batchStatus)) {
			sql.append(" and xwb.batch_status = ?");
			list.add(batchStatus);
		}
		//制单日期
		if (!ConvertUtil.isEmpty(startProductionDate)) {
			sql.append(" and xwb.production_date >= ?");
			list.add(startProductionDate + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endProductionDate)) {
			sql.append(" and xwb.production_date <= ?");
			list.add(endProductionDate + " 23:59:59");
		}
		//生产完成日期
		if (!ConvertUtil.isEmpty(startCompletionDate)) {
			sql.append(" and xwb.completion_date >= ?");
			list.add(startCompletionDate + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endCompletionDate)) {
			sql.append(" and xwb.completion_date <= ?");
			list.add(endCompletionDate + " 23:59:59");
		}
		//有效日期
		if (!ConvertUtil.isEmpty(startEffectiveDate)) {
			sql.append(" and and xwb.effective_date >= ?");
			list.add(startEffectiveDate + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endEffectiveDate)) {
			sql.append(" and and xwb.effective_date <= ?");
			list.add(endEffectiveDate + " 23:59:59");
		}
		//创建人
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" and sm.name like ?");
			 list.add("%" + storeName + "%");
		}

        //备注
        if (!ConvertUtil.isEmpty(memo)) {
            sql.append(" and xwb.memo like ? ");
            list.add("%" + memo + "%");
        }
		//经营组织
		if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
			String ox = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					ox += organizationId[i];
				else
					ox += organizationId[i] + ",";
			}
			sql.append(" and po.id in (" + ox + ")");
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
//		//用户仓库工厂
//		String productionPlantIds = roleJurisdictionUtil.getProductionPlantIds();
//		if(!ConvertUtil.isEmpty(productionPlantIds) && !productionPlantIds.equals("-1")){
//			if(!ConvertUtil.isEmpty(productionPlantIds) && !productionPlantIds.equals("0")){
//				sql.append(" and (factory.id is null or factory.id in (" + productionPlantIds + "))");
//			}else{
//				sql.append(" and factory.id is null");
//			}
//		}
		
		sql.append(" order by xwb.modify_date desc");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		String totalsql = "select count(1) from ( " + sql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}
	
	/**
	 *  
	 * @param ObjId 主体id
	 * @param itemId  主体对应的行id
	 * @param type   主体类型
	 * @return
	 */
	public Page<Map<String, Object>> findBillBacth(Long[] bacthIds,Pageable pageable){
		List<Object> list = new ArrayList<Object>();
		StringBuffer billBacthSql = new StringBuffer();
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(billBacthSql.toString(), objs, pageable);
		String totalsql = "select count(1) from ( " + billBacthSql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}
	
	public List<Map<String, Object>> findWarehouseBatchList(String batchEncoding,
			Long[] productionPlantId, String productionNo,Boolean batchStatus,
			Long[] categoryOfGoodsId,String startProductionDate,String endProductionDate,
			String startCompletionDate,String endCompletionDate,String startEffectiveDate,
			String endEffectiveDate,String storeName,Long[] organizationId,Long[] ids){
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = this.neaten();
		//批次Id
		if (!ConvertUtil.isEmpty(ids) && ids.length > 0) {
			String ox = "";
			for (int i = 0; i < ids.length; i++) {
				if (i == ids.length - 1)
					ox += ids[i];
				else
					ox += ids[i] + ",";
			}
			sql.append(" and xwb.id in (" + ox + ")");
		}
		//主体
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and xwb.company_info_id = ?");
			list.add(companyInfoId);
		}
		//批次编码
		if (!ConvertUtil.isEmpty(batchEncoding)) {
			 sql.append(" and xwb.batch_encoding like ? ");
			 list.add("%" + batchEncoding + "%");
		}
		//生产工厂
		if (!ConvertUtil.isEmpty(productionPlantId) && productionPlantId.length > 0) {
			String ox = "";
			for (int i = 0; i < productionPlantId.length; i++) {
				if (i == productionPlantId.length - 1)
					ox += productionPlantId[i];
				else
					ox += productionPlantId[i] + ",";
			}
			sql.append(" and factory.id in (" + ox + ")");
		}
		//生产单号
		if (!ConvertUtil.isEmpty(productionNo)) {
			 sql.append(" and xwb.production_no like ? ");
			 list.add("%" + productionNo + "%");
		}
		//货物类型
		if (!ConvertUtil.isEmpty(categoryOfGoodsId) && categoryOfGoodsId.length > 0) {
			String ox = "";
			for (int i = 0; i < categoryOfGoodsId.length; i++) {
				if (i == categoryOfGoodsId.length - 1)
					ox += categoryOfGoodsId[i];
				else
					ox += categoryOfGoodsId[i] + ",";
			}
			sql.append(" and xwb.category_of_goods in (" + ox + ")");
		}
		//是否有效
		if (!ConvertUtil.isEmpty(batchStatus)) {
			sql.append(" and xwb.batch_status = ?");
			list.add(batchStatus);
		}
		//制单日期
		if (!ConvertUtil.isEmpty(startProductionDate)) {
			sql.append(" and xwb.production_date >= ?");
			list.add(startProductionDate + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endProductionDate)) {
			sql.append(" and xwb.production_date <= ?");
			list.add(endProductionDate + " 23:59:59");
		}
		//生产完成日期
		if (!ConvertUtil.isEmpty(startCompletionDate)) {
			sql.append(" and xwb.completion_date >= ?");
			list.add(startCompletionDate + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endCompletionDate)) {
			sql.append(" and xwb.completion_date <= ?");
			list.add(endCompletionDate + " 23:59:59");
		}
		//有效日期
		if (!ConvertUtil.isEmpty(startEffectiveDate)) {
			sql.append(" and and xwb.effective_date >= ?");
			list.add(startEffectiveDate + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endEffectiveDate)) {
			sql.append(" and and xwb.effective_date <= ?");
			list.add(endEffectiveDate + " 23:59:59");
		}
		//创建人
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" and sm.name like %?%");
			 list.add("%" + storeName + "%");
		}
		//经营组织
		if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
			String ox = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					ox += organizationId[i];
				else
					ox += organizationId[i] + ",";
			}
			sql.append(" and po.id in (" + ox + ")");
		}
	
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		sql.append(" order by xwb.modify_date desc");
        
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);
		
		return maps;
	}
	
    /**
     * <AUTHOR>
     * @date 2020-11-4
     * 校验批次库存是否为空
     * */
    public  Integer checkWarehouseStock(Long id){
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(ws.onhand_quantity) FROM `xx_warehouse_stock` ws WHERE ws.warehouse_batch = "+id+" and onhand_quantity<>0 ");
        Integer rows=getNativeDao().findInt(sql.toString());
        return rows;
    }
}
