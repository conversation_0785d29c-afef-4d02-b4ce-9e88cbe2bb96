package net.shopxx.stock.dao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.util.RoleJurisdictionUtil;
@Repository("warehouseBaseDao")
public class WarehouseBaseDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	

	/**
	 * 查找是否存在仓库覆盖区域
	 * @param warehouseId
	 * @param
	 * @return
	 */
	public boolean existsWarehouseArea(Long warehouseId, Long areaId) {

		String sql = "select count(1) from xx_warehouse_area w where w.xx_warehouse = ? and (w.areas = ? or w.areas = (select parent from xx_area where id = ?) or w.areas = (select p.parent from xx_area a,xx_area p where a.parent = p.id and a.id = ?))";

		int count = getNativeDao().findInt(sql,
				new Object[] { warehouseId, areaId, areaId, areaId });

		return count > 0;
	}
	

	public Page<Map<String, Object>> findPage(String sn, String name,
			String erp_warehouse_code, Boolean isEnabled, Long supplierId,
			Integer type,Integer warehouseType, Long saleOrgId, 
			Long organizationId,Long sbuId, Pageable pageable,
			Long sign,Integer isNotwarehouseType) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		
		StringBuilder sql = new StringBuilder();
		sql.append("select w.*,s.id type_system_dict_id,s.value type_system_dict_value,s.flag type_system_dict_flag, "
				+ "		   so.name saleOrgName,o.name management_organization_name,o.id  management_organization_id,wb.sbu sbuId,sb.name sbuName "
				+ " from xx_warehouse w "
				+ " left join xx_system_dict s on w.type_system_dict=s.id "
				+ " left join xx_warehouse_sale_org wso on wso.warehouse =w.id "
				+ " left join xx_sale_org so on  wso.sale_org =so.id "
				+ " left join xx_organization o on o.id =w.management_organization "
				+ " left join xx_warehouse_sbu wb on wb.warehouse =w.id"
				+ " left join xx_sbu sb on sb.id =wb.sbu "
				+ " where 1 = 1 and wb.sbu is not null ");

		if (companyInfoId != null) {
			sql.append(" and w.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (warehouseType != null) {
			sql.append(" and w.type_system_dict = ?");
			list.add(warehouseType);
		}
        if (isNotwarehouseType != null) {
            sql.append(" and w.type_system_dict <> ?");
            list.add(isNotwarehouseType);
        }
		if (saleOrgId != null) {
			sql.append(" and wso.sale_org = ?");
			list.add(saleOrgId);
		}
		if (sbuId != null) {
			sql.append(" and wb.sbu = ?");
			list.add(sbuId);
		}
		if (organizationId != null) {
			sql.append(" and w.management_organization = ?");
			list.add(organizationId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and w.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and w.name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(erp_warehouse_code)) {
			sql.append(" and w.erp_warehouse_code like ?");
			list.add("%" + erp_warehouse_code + "%");
		}
		if (isEnabled != null) {
			if (isEnabled) {
				sql.append(" and w.is_enabled = 1");
			}
			else {
				sql.append(" and w.is_enabled = 0");
			}
		}
		if (supplierId != null) {
			sql.append(" and exists(select 1 from xx_warehouse_store where warehouse = w.id and store = ?)");
			list.add(supplierId);
		}
		if (type != null) {
			sql.append(" and w.type = ?");
			list.add(type);
		}
		
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				String sqlString = "select s.sale_org from xx_store s where s.id  in (" + storeAuth + ")";
				sql.append(" and so.id in (" + sqlString + ")");
			}
		}
		
		//用户仓库
		String warehouseIds = roleJurisdictionUtil.getWarehouseIds();
		if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("-1")){
			if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("0")){
				sql.append(" and w.id in (" + warehouseIds + ")");
			}else{
				sql.append(" and w.id is null");
			}
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}

		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and o.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and o.id is null");
			}
		}
		sql.append(" group by w.id order by w.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);

		return page;
	}

	public List<Map<String, Object>> findWarehouseSbu(Warehouse warehouse) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT t.*, xar.name sbu_name,xsa.name warehouse_name, xsa.id  warehouseId "
				+ " FROM xx_warehouse_sbu  t "
				+ " LEFT JOIN xx_sbu xar on xar.id=t.sbu "
				+ " LEFT JOIN xx_warehouse xsa on xsa.id= t.warehouse "
				+ "WHERE 1=1 ";
		if (warehouse != null) {
			sql += " and t.warehouse =" + warehouse.getId();
		}
		if (companyInfoId != null) {
			sql += " and t.company_info_id=" + companyInfoId;
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
		return lists;
	}
	
	/**
	 * 查找发运方式
	 * */
	public List<Map<String, Object>> findWarehouseSmethod(Long id) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT ws.smethod,ws.is_default,sd.id shippingwayId,sd.value shippingwayName FROM xx_warehouse_smethod  ws " 
				+" LEFT JOIN xx_warehouse w on w.id=ws.warehouse " 
				+" LEFT JOIN xx_system_dict sd on sd.id= ws.smethod " 
				+" WHERE 1=1 ";
		if (id != null) {
			sql += " and ws.warehouse=" + id;
		}
		if (companyInfoId != null) {
			sql += " and ws.company_info_id=" + companyInfoId;
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
		return lists;
	}
	
	/**
	 * 用户保存
	 * @param warehouse
	 * @return
	 */
	public List<Map<String, Object>> findWarehouseStoreMember(Warehouse warehouse) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT wsm.*,smr.name store_member_name,xwe.name warehouse_name, xwe.id  warehouseId  "
				+ " FROM xx_warehouse_store_member wsm "
				+ " LEFT JOIN xx_store_member smr ON smr.id=wsm.store_member "
				+ " LEFT JOIN xx_warehouse xwe ON xwe.id=wsm.warehouse "
				+ "WHERE 1=1 ";
		if (warehouse != null) {
			sql += " and wsm.warehouse =" + warehouse.getId();
		}
		if (companyInfoId != null) {
			sql += " and wsm.company_info_id=" + companyInfoId;
		}
		
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
	
		return lists;
	}
	
	
	/**
	 * 库位列表查询
	 * @param warehouse
	 * @return
	 */
	public List<Map<String, Object>> findwarehouseLocationList(Warehouse warehouse) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT wl.* FROM xx_warehouse_location wl  "
				+ " left join  xx_warehouse w on w.id=wl.warehouse "
				+ "WHERE 1=1 ";
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql += " and wl.company_info_id=" + companyInfoId;
		}
		if (!ConvertUtil.isEmpty(warehouse) && !ConvertUtil.isEmpty(warehouse.getId())) {
			sql += " and wl.warehouse =" + warehouse.getId();
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
		return lists;
	}
	
	/**
	 * 按参数查询库位信息
	 * @param code  库位编码
	 * @param warehouseId   仓库id
	 * @param remarks   库位备注
	 * @return
	 */
	public List<Map<String, Object>> findwarehouseLocationListByParam(String code, Long warehouseId, String remarks) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuffer sql = new StringBuffer();
		sql.append(" select wl.* from xx_warehouse_location wl left join  xx_warehouse w on w.id=wl.warehouse where wl.is_checks is true ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and wl.company_info_id="+companyInfoId);			
		}
		if (!ConvertUtil.isEmpty(warehouseId)) {
			sql.append(" and wl.warehouse = "+warehouseId);			
		}
		if (!ConvertUtil.isEmpty(code)) {
			sql.append(" and wl.code like '%"+code+"%'");
		}
		if (!ConvertUtil.isEmpty(remarks)) {
			sql.append(" and wl.remarks like ");
			sql.append("'%"+remarks+"%'");
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql.toString(),
				null,
				0);
		return lists;
	}
	
	
	public String findWarehouseIds() {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT GROUP_CONCAT( DISTINCT w.id ) warehouse_ids ");
		sql.append(" FROM xx_store_member sm ");
		sql.append(" JOIN xx_warehouse_store_member wsm ON sm.id = wsm.store_member ");
		sql.append(" JOIN xx_warehouse w ON wsm.warehouse= w.id ");
		sql.append(" WHERE sm.company_info_id=wsm.company_info_id ");
		sql.append(" AND sm.company_info_id=w.company_info_id ");
		sql.append(" AND w.is_enabled = TRUE ");
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" AND sm.company_info_id = ? ");
			list.add(companyInfoId);
		}
		
		if(!ConvertUtil.isEmpty(storeMemberId)){
			sql.append(" AND sm.id = ? ");
			list.add(storeMemberId);
		}
		
		sql.append(" GROUP BY sm.id	");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findString(sql.toString(), objs);
	}

    /**
     * 获取所有仓库
     * @return
     */
    public String findAllWarehouseIds() {
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT GROUP_CONCAT( DISTINCT w.id ) warehouse_ids ");
        sql.append(" from xx_warehouse w  ");
        sql.append(" where w.is_enabled = TRUE ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
            sql.append(" and w.company_info_id = ? ");
            list.add(companyInfoId);
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        return getNativeDao().findString(sql.toString(), objs);
    }
	
}