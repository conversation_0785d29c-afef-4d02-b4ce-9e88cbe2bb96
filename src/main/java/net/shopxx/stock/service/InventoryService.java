package net.shopxx.stock.service;

import net.shopxx.base.core.service.BaseService;
import net.shopxx.stock.entity.Inventory;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.wf.service.WfBillBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface InventoryService extends WfBillBaseService<Inventory> {

    public void save(Inventory inventory, Warehouse warehouse, Long sbuId, Integer flag);

    public void update(Inventory inventory, Warehouse warehouse, Long sbuId, Integer flag);

    List<Map<String, Object>> findInventoryItemListById(Long inventoryId, Boolean isDefault);

    String importExcel(MultipartFile file) throws Exception;

    void checkWf(Long inventoryId);

    void cancel(Inventory inventory);
}
