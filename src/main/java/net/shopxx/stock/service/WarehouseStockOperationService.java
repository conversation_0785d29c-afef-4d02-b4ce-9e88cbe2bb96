package net.shopxx.stock.service;
import java.math.BigDecimal;
import java.util.List;

import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.AmShippingItem;
import net.shopxx.stock.entity.WarehouseBatchItem;
import net.shopxx.stock.entity.WarehouseStock;
import net.shopxx.stock.entity.WarehouseStockOperation;
public interface WarehouseStockOperationService extends BaseService<WarehouseStockOperation>{
	
	/**
	 * 根据出入库Id执行释放库存
	 */
	public void deleteWarehouseStockOperation(Long amShippingId,SystemDict actionType);
	
	
	
	/**
	 * 保存仓库库存操作
	 */
	public void saveWarehouseStockOperation(SystemDict billType,SystemDict actionType,AmShipping amShipping,
			AmShippingItem amShippingItem,WarehouseBatchItem warehouseBatchItem,WarehouseStock warehouseStock,
			BigDecimal boxQuantity,BigDecimal branchQuantity,BigDecimal quantity);
	
	

}
