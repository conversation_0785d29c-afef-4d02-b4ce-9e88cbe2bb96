package net.shopxx.stock.service;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.stock.entity.WarehouseBatch;
public interface WarehouseBatchService extends BaseService<WarehouseBatch> {
	
	public Page<Map<String, Object>> findPage(String batchEncoding,
			Long[] productionPlantId, String productionNo,
			Boolean batchStatus,Long[] categoryOfGoodsId,
			String startProductionDate,String endProductionDate,
			String startCompletionDate,String endCompletionDate,
			String startEffectiveDate,String endEffectiveDate,
			String storeName,Long[] organizationId,String batchIds,String memo,Pageable pageable);

	
	/**
	 * 处理批次信息
	 * @param batchsIds
	 * @return
	 */
	public List<Map<String, Object>> findWarehouseBatchs(String batchsIds,
			Long organizationId,Long productionPlantId);
	
	
	/**批次excel导入
	 * 
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public String warehouseBatchAddImport(MultipartFile multipartFile) throws Exception;
	
	
	/**
	 * 批次选择、条件导出
	 * @param batchEncoding
	 * @param productionPlantId
	 * @param productionNo
	 * @param batchStatus
	 * @param categoryOfGoodsId
	 * @param startProductionDate
	 * @param endProductionDate
	 * @param startCompletionDate
	 * @param endCompletionDate
	 * @param startEffectiveDate
	 * @param endEffectiveDate
	 * @param storeName
	 * @param organizationId
	 * @param ids
	 * @return
	 */
	public List<Map<String, Object>> findWarehouseBatchList(String batchEncoding,
			Long[] productionPlantId, String productionNo,Boolean batchStatus,
			Long[] categoryOfGoodsId,String startProductionDate,String endProductionDate,
			String startCompletionDate,String endCompletionDate,String startEffectiveDate,
			String endEffectiveDate,String storeName,Long[] organizationId,Long[] ids);
	
	
	
	
	/**
	 * 批次作废
	 * @param ids
	 */
	public void cancel(Long[] ids);
	
	
	
}
