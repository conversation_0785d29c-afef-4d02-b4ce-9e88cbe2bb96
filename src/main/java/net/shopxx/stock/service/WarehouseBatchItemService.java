package net.shopxx.stock.service;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.stock.entity.WarehouseBatchItem;
public interface WarehouseBatchItemService extends BaseService<WarehouseBatchItem>{
	
	
	public Page<Map<String, Object>> findPage(Long warehouseId ,Long productId,
			Long organizationId,Long productLevelId,Long[] colorNumbersIds,
			Long[] moistureContentsIds,Long[] warehouseBatchIds,Long[] newOldLogosIds,
			String startTime, String endTime,Pageable pageable,String batchEncoding,
			String billType,String documentNo,Boolean flag,Long productionPlantId,Long warehouseLocationId,String memo);
	
	
	public List<Map<String, Object>> findVWarehouseBatchItemList(Long amShippingId,
			Long warehouseId ,Long productId,Long organizationId,Long productLevelId,
			Long colorNumbersId,Long moistureContentsId,Long[] warehouseBatchIds,
			Long newOldLogosId,Long billTypeId,Boolean flag,Long productionPlantId,Long warehouseLocationId);
	
	public List<Map<String, Object>> findVWarehouseBatchItemListBySourceNoId(Long sourceNoId,
			Long billTypeId,Long[] ids);

}
