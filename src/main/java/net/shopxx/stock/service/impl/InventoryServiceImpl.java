package net.shopxx.stock.service.impl;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.*;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.dao.SaleNatureShippingDao;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.AmShippingItem;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.service.AmShippingService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.shop.service.StoreMemberService;
import net.shopxx.stock.dao.InventoryDao;
import net.shopxx.stock.entity.*;
import net.shopxx.stock.service.*;
import net.shopxx.util.CommonVariable;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;
import org.activiti.engine.impl.util.json.JSONString;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("inventoryServiceImpl")
public class InventoryServiceImpl extends WfBillBaseServiceImpl<Inventory> implements InventoryService {

    private static Logger logger = LoggerFactory.getLogger(InventoryServiceImpl.class);
    @Resource(name = "inventoryServiceImpl")
    private InventoryService inventoryService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    @Resource(name = "orderFullLinkServiceImpl")
    private OrderFullLinkService orderFullLinkService;
    @Resource(name = "roleJurisdictionUtil")
    private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "warehouseBatchServiceImpl")
    private WarehouseBatchService warehouseBatchService;
    @Resource(name = "inventoryDao")
    private InventoryDao inventoryDao;
    @Resource(name = "organizationServiceImpl")
    private OrganizationService organizationService;
    @Resource(name = "warehouseLocationServiceImpl")
    private WarehouseLocationService warehouseLocationService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "amShippingServiceImpl")
    private AmShippingService amShippingService;
    @Resource(name = "warehouseBatchItemServiceImpl")
    private WarehouseBatchItemService warehouseBatchItemService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgService;
    @Resource(name = "sbuServiceImpl")
    private SbuService sbuService;
    @Resource(name = "warehouseBaseServiceImpl")
    private WarehouseBaseService warehouseService;
    @Resource(name = "stockServiceImpl")
    private StockService stockService;
    @Resource(name = "totalDateServiceImpl")
    private TotalDateService totalDateService;

    @Override
    public void save(Inventory inventory, Warehouse warehouse, Long sbuId, Integer flag) {

        // 系统单据类型
        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("remark", CommonVariable.INANDOUT_TYPE));
        SystemDict systemType = systemDictService.systemBillType(filters);

        if(warehouse.getIsOrderWarehousing()){
            ExceptionUtil.throwServiceException("此仓库为订货仓不支持库存盘点");
        }
        List<InventoryItem> inventoryItems = inventory.getInventoryItems();
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal tVolume = BigDecimal.ZERO;
        BigDecimal tWeight = BigDecimal.ZERO;
        List<String> orderSns = new ArrayList<String>();
        //发货单状态
        Long[] shippingIds1 = new Long[]{0L};

        for (Iterator<InventoryItem> iterator = inventoryItems.iterator(); iterator.hasNext(); ) {
            InventoryItem inventoryItem = iterator.next();
            if (ConvertUtil.isEmpty(inventoryItem.getProduct())) {
                iterator.remove();
                continue;
            }
            //校验单状态
            if (!ConvertUtil.isEmpty(inventoryItem.getInventory())) {
                this.checkInventoryState(inventoryItem, shippingIds1);
            }
             Product product = productBaseService.find(inventoryItem.getProduct().getId());
            inventoryItem.setProduct(product);
            inventoryItem.setWarehouse(warehouse);
            BigDecimal quantity = inventoryItem.getQuantity();


            // 盘点库存单明细数据处理
            BigDecimal pVolume = product == null ? BigDecimal.ZERO
                    : (product.getVolume() == null ? BigDecimal.ZERO : product.getVolume());
            BigDecimal pWeight = product == null ? BigDecimal.ZERO
                    : (product.getWeight() == null ? BigDecimal.ZERO : product.getWeight());
            inventoryItem.setVolume(pVolume);
            // 行体积=每箱体积（产品体积）* 下单箱数；
            BigDecimal volume = pVolume.multiply(
                    inventoryItem.getBoxQuantity() == null ? BigDecimal.ZERO : inventoryItem.getBoxQuantity());// 行合计体积
            tVolume = tVolume.add(volume);// 总体积
            // 行重量=行重量 * 平方数；
            BigDecimal weight = pWeight.multiply(quantity);// 行合计重量
            tWeight = tWeight.add(weight);// 总重量

            // 产品经营组织
            if (ConvertUtil.isEmpty(inventoryItem.getProductOrganization())
                    || (!ConvertUtil.isEmpty(inventoryItem.getProductOrganization())
                    && ConvertUtil.isEmpty(inventoryItem.getProductOrganization().getId()))) {
            } else {
                inventoryItem.setProductOrganization(organizationService.find(inventoryItem.getProductOrganization().getId()));
            }

            // 色号
            if (ConvertUtil.isEmpty(inventoryItem.getColorNumbers())
                    || (!ConvertUtil.isEmpty(inventoryItem.getColorNumbers())
                    && ConvertUtil.isEmpty(inventoryItem.getColorNumbers().getId()))) {
                inventoryItem.setColorNumbers(null);
            }

            // 含水率
            if (ConvertUtil.isEmpty(inventoryItem.getMoistureContents())
                    || (!ConvertUtil.isEmpty(inventoryItem.getMoistureContents())
                    && ConvertUtil.isEmpty(inventoryItem.getMoistureContents().getId()))) {
                inventoryItem.setMoistureContents(null);
            }
            // 新旧标识
            if (ConvertUtil.isEmpty(inventoryItem.getNewOldLogos())
                    || (!ConvertUtil.isEmpty(inventoryItem.getNewOldLogos())
                    && ConvertUtil.isEmpty(inventoryItem.getNewOldLogos().getId()))) {
                inventoryItem.setNewOldLogos(null);
            }
            // 库位
            if (ConvertUtil.isEmpty(inventoryItem.getWarehouseLocation())
                    || (!ConvertUtil.isEmpty(inventoryItem.getWarehouseLocation())
                    && ConvertUtil.isEmpty(inventoryItem.getWarehouseLocation().getId()))) {
                inventoryItem.setWarehouseLocation(null);
            } else {
                inventoryItem.setWarehouseLocation(warehouseLocationService.find(inventoryItem.getWarehouseLocation().getId()));
            }
            inventoryItem.setProductionSchedulingItem(null);

            inventoryItem.setInventory(inventory);
        }
        inventory.setInventoryItems(inventoryItems);

        //新增
        inventory.setSn(SnUtil.getInventorySn());
        inventory.setStatus(0);
        save(inventory);
        // 盘点库存单全链路
        List<String> inventorySns = new ArrayList<String>();
        orderSns.add(inventory.getSn());
        orderFullLinkService.addFullLink(202, inventorySns, inventory.getSn(), "新增库存盘点单", null);
        //操作类型
        List<SystemDict> actionTypeList = roleJurisdictionUtil.getSystemDictList("actionType", "新增");
        if (actionTypeList.isEmpty() || actionTypeList.size() == 0) {
            ExceptionUtil.throwServiceException("操作类型不能为空");
        }
    }

    @Override
    public void update(Inventory inventory, Warehouse warehouse, Long sbuId, Integer flag) {

        Inventory pInventory = find(inventory.getId());
        List<InventoryItem> inventoryItems = inventory.getInventoryItems();
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal tVolume = BigDecimal.ZERO;
        BigDecimal tWeight = BigDecimal.ZERO;
        List<String> orderSns = new ArrayList<String>();
        //单据状态
        Long[] shippingIds1 = new Long[]{0L};
        for (Iterator<InventoryItem> iterator = inventoryItems.iterator(); iterator.hasNext(); ) {
            InventoryItem inventoryItem = iterator.next();
            if (ConvertUtil.isEmpty(inventoryItem.getProduct())) {
                iterator.remove();
                continue;
            }
            //校验单状态
            if (!ConvertUtil.isEmpty(inventoryItem.getInventory())) {
                this.checkInventoryState(inventoryItem, shippingIds1);
            }
            Product product = productBaseService.find(inventoryItem.getProduct().getId());
            inventoryItem.setProduct(product);
            inventoryItem.setWarehouse(warehouse);
            BigDecimal quantity = inventoryItem.getQuantity();

            // 单据明细数据处理
            BigDecimal pVolume = product == null ? BigDecimal.ZERO
                    : (product.getVolume() == null ? BigDecimal.ZERO : product.getVolume());
            BigDecimal pWeight = product == null ? BigDecimal.ZERO
                    : (product.getWeight() == null ? BigDecimal.ZERO : product.getWeight());
            inventoryItem.setVolume(pVolume);
            // 行体积=每箱体积（产品体积）* 下单箱数；
            BigDecimal volume = pVolume.multiply(
                    inventoryItem.getBoxQuantity() == null ? BigDecimal.ZERO : inventoryItem.getBoxQuantity());// 行合计体积
            tVolume = tVolume.add(volume);// 总体积
            // 行重量=行重量 * 平方数；
            BigDecimal weight = pWeight.multiply(quantity);// 行合计重量
            tWeight = tWeight.add(weight);// 总重量

            // 产品经营组织
            if (ConvertUtil.isEmpty(inventoryItem.getProductOrganization())
                    || (!ConvertUtil.isEmpty(inventoryItem.getProductOrganization())
                    && ConvertUtil.isEmpty(inventoryItem.getProductOrganization().getId()))) {
            } else {
                inventoryItem.setProductOrganization(organizationService.find(inventoryItem.getProductOrganization().getId()));
            }

            // 色号
            if (ConvertUtil.isEmpty(inventoryItem.getColorNumbers())
                    || (!ConvertUtil.isEmpty(inventoryItem.getColorNumbers())
                    && ConvertUtil.isEmpty(inventoryItem.getColorNumbers().getId()))) {
                inventoryItem.setColorNumbers(null);
            }

            // 含水率
            if (ConvertUtil.isEmpty(inventoryItem.getMoistureContents())
                    || (!ConvertUtil.isEmpty(inventoryItem.getMoistureContents())
                    && ConvertUtil.isEmpty(inventoryItem.getMoistureContents().getId()))) {
                inventoryItem.setMoistureContents(null);
            }
            // 新旧标识
            if (ConvertUtil.isEmpty(inventoryItem.getNewOldLogos())
                    || (!ConvertUtil.isEmpty(inventoryItem.getNewOldLogos())
                    && ConvertUtil.isEmpty(inventoryItem.getNewOldLogos().getId()))) {
                inventoryItem.setNewOldLogos(null);
            }
            // 库位
            if (ConvertUtil.isEmpty(inventoryItem.getWarehouseLocation())
                    || (!ConvertUtil.isEmpty(inventoryItem.getWarehouseLocation())
                    && ConvertUtil.isEmpty(inventoryItem.getWarehouseLocation().getId()))) {
                inventoryItem.setWarehouseLocation(null);
            } else {
                inventoryItem.setWarehouseLocation(warehouseLocationService.find(inventoryItem.getWarehouseLocation().getId()));
            }
            inventoryItem.setProductionSchedulingItem(null);

            inventoryItem.setInventory(pInventory);
        }
        pInventory.getInventoryItems().clear();
        pInventory.getInventoryItems().addAll(inventoryItems);
        pInventory.setStatus(0);
        pInventory.setBillDate(inventory.getBillDate());
        pInventory.setRemark(inventory.getRemark());
        //更新
        update(pInventory);
        // 盘点库存单全链路
        List<String> inventorySns = new ArrayList<String>();
        orderSns.add(pInventory.getSn());
        orderFullLinkService.addFullLink(202, inventorySns, pInventory.getSn(), "修改库存盘点单", null);
        //操作类型
        List<SystemDict> actionTypeList = roleJurisdictionUtil.getSystemDictList("actionType", "保存");
        if (actionTypeList.isEmpty() || actionTypeList.size() == 0) {
            ExceptionUtil.throwServiceException("操作类型不能为空");
        }
    }

    @Override
    public List<Map<String, Object>> findInventoryItemListById(Long inventoryId, Boolean isDefault) {
        return inventoryDao.findInventoryItemListById(inventoryId, isDefault);
    }


    /**
     * 校验发货单状态
     *
     * @param inventoryItem
     * @param inventoryIds
     */
    private void checkInventoryState(InventoryItem inventoryItem, Long[] inventoryIds) {

        Inventory inventory = inventoryItem.getInventory();
        if (ConvertUtil.isEmpty(inventory) || (!ConvertUtil.isEmpty(inventory) && ConvertUtil.isEmpty(inventory.getId()))) {
            ExceptionUtil.throwServiceException("该库存盘点单编号不存在");
        }
        if (inventoryIds[0].longValue() != inventory.getId().longValue()) {
            //库存盘点单
            Inventory pInventory = inventoryService.find(inventory.getId());
            if (ConvertUtil.isEmpty(pInventory)) {
                ExceptionUtil.throwServiceException("该库存盘点单编号不存在");
            }
            if (!ConvertUtil.isEmpty(pInventory.getStatus()) && (pInventory.getStatus() == 1 || pInventory.getStatus() == 3)) {
                inventoryIds[0] = pInventory.getId();
            } else {
                ExceptionUtil.throwServiceException("发货单编号为【" + pInventory.getSn() + "】的状态必须是已审核或者部分发货的");
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public String importExcel(MultipartFile multipartFile) throws Exception {

        String msg = null;
        Workbook wb = null;
        File tempFile = null;
        Cell cell = null;
        int success = 0;
        tempFile = new File(System.getProperty("java.io.tmpdir")
                + "/upload_"
                + UUID.randomUUID()
                + ".tmp");
        if (!tempFile.getParentFile().exists()) {
            tempFile.getParentFile().mkdirs();
        }
        multipartFile.transferTo(tempFile);
        wb = Workbook.getWorkbook(tempFile);
        Sheet sheet = wb.getSheets()[0];
        int rows = sheet.getRows();
        if (rows > 501) {
            if (StringUtils.isNotBlank(sheet.getCell(0, 500).getContents())) {
                ExceptionUtil.throwServiceException("一次最多导入500条");
            } else {
                rows = 501;
            }
        }
        //==============表头===============
        String saleOrgStr;//机构
        String organizationStr;//经营组织
        String sbuStr;//SBU
        String warehouseStr;//仓库
        String billDateStr;
        String remark;//备注

        //==============明细==================
        String line_no;             //行号
        String productCode;         //产品编码
        String productName;       //产品名称
        String productDescription;  //产品描述
        String productOrganizationStr;       //经营组织
        String productLevelStr;        //产品等级
        String branchQuantity;      //盘点支数
        String colorNumbersStr;        //色号
        String moistureContentsStr;    //含水率
        String warehouseBatchStr;      //批次
        String warehouseLocationStr;   //库位
        String newOldLogosStr;         //新旧标识
        String memo;                //备注

        List<Filter> filters = new ArrayList<Filter>();
        //获取表头数据
        LogUtils.debug("=========读取excel的数据==表头========");
        Inventory inventory = new Inventory();
        int countNo = 0;
        cell = sheet.getCell(countNo++, 2);
        saleOrgStr = cell.getContents();
        if (StringUtils.isBlank(saleOrgStr)) {
            msg = "机构为空";
            ExceptionUtil.throwServiceException(msg);
        }
        filters.clear();
        filters.add(Filter.eq("companyInfoId", 9));
        filters.add(Filter.eq("name", saleOrgStr));
        List<SaleOrg> saleOrgList = saleOrgService.findList(null, filters, null);
        if (saleOrgList.isEmpty()) {
            throw new RuntimeException("在系统中找不到名为为[" + saleOrgStr + "]的机构");
        }
        SaleOrg saleOrg = saleOrgList.get(0);
        inventory.setSaleOrg(saleOrg);

        cell = sheet.getCell(countNo++, 2);
        organizationStr = cell.getContents();
        if (StringUtils.isBlank(organizationStr)) {
            msg = "经营组织为空";
            ExceptionUtil.throwServiceException(msg);
        }
        filters.clear();
        filters.add(Filter.eq("companyInfoId", 9));
        filters.add(Filter.eq("name", organizationStr));
        List<Organization> organizationList = organizationService.findList(null, filters, null);
        if (organizationList.isEmpty()) {
            throw new RuntimeException("在系统中找不到名为为[" + organizationStr + "]的经营组织");
        }
        Organization organization = organizationList.get(0);
        inventory.setOrganization(organization);

        cell = sheet.getCell(countNo++, 2);
        sbuStr = cell.getContents();
        if (StringUtils.isBlank(sbuStr)) {
            msg = "SBU为空";
            ExceptionUtil.throwServiceException(msg);
        }
        filters.clear();
        filters.add(Filter.eq("companyInfoId", 9));
        filters.add(Filter.eq("name", sbuStr));
        List<Sbu> sbuList = sbuService.findList(null, filters, null);
        if (sbuList.isEmpty()) {
            throw new RuntimeException("在系统中找不到名为为[" + sbuStr + "]的SBU");
        }
        Sbu sbu = sbuList.get(0);
        inventory.setSbu(sbu);

        cell = sheet.getCell(countNo++, 2);
        warehouseStr = cell.getContents();
        if (StringUtils.isBlank(warehouseStr)) {
            msg = "仓库为空";
            ExceptionUtil.throwServiceException(msg);
        }
        filters.clear();
        filters.add(Filter.eq("companyInfoId", 9));
        filters.add(Filter.eq("name", warehouseStr));
        List<Warehouse> warehouseList = warehouseService.findList(null, filters, null);
        if (warehouseList.isEmpty()) {
            throw new RuntimeException("系统中找不到名为为[" + warehouseStr + "]的仓库");
        }
        Warehouse warehouse = warehouseList.get(0);
        //检验仓库与经营组织是否匹配
        if(!warehouse.getManagementOrganization().getId().equals(organization.getId())){
            ExceptionUtil.throwServiceException("仓库与经营组织不匹配");
        }
        inventory.setWarehouse(warehouse);

        cell = sheet.getCell(countNo++, 2);
        billDateStr = cell.getContents();
        Date billDate = null;
        if (billDateStr != "" && !isValidDate(billDateStr)) {
            msg = "第" + 2 + "行." + "GL时间格式有误，格式应为yyyy-MM-dd";
            ExceptionUtil.throwServiceException(msg);
        }
        if (!StringUtils.isEmpty(billDateStr)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            billDate = sdf.parse(billDateStr);
            inventory.setBillDate(billDate);
        }else{
            inventory.setBillDate(new Date());
        }

        cell = sheet.getCell(countNo++, 2);
        remark = cell.getContents();
        inventory.setRemark(remark);

        //订货仓不可以使用库存盘点
        if(warehouse.getIsOrderWarehousing()){
            throw new RuntimeException("该仓库为订货仓不支持库存盘点");
        }
        //仓库的类型-是否物流仓
        boolean isHeadquarters = false;
        if(warehouse.getTypeSystemDict().getId() == 191L){
            isHeadquarters = true;
        }


        //循环获取每条明细数据
        List<InventoryItem> inventoryItemList = new ArrayList<InventoryItem>();
        for (int i = 6; i < rows; i++) {
            LogUtils.debug("=========读取excel的数据==明细========");
            countNo = 0;
            InventoryItem addInventoryItem = new InventoryItem();
            cell = sheet.getCell(countNo++, i);
            line_no = cell.getContents();
            if (!StringUtils.isBlank(line_no)) {
                addInventoryItem.setLineNo(Integer.parseInt(line_no));
            }

            cell = sheet.getCell(countNo++, i);
            productCode = cell.getContents();
            if (StringUtils.isBlank(productCode)) {
                msg = "第" + i + "行." + "产品编码为空";
                ExceptionUtil.throwServiceException(msg);
            }
            filters.clear();
            filters.add(Filter.eq("vonderCode", productCode));
            List<Product> products = productService.findList(null, 1, filters, null);
            if (products.isEmpty()) {
                throw new RuntimeException("第" + (i + 1) + "行,在系统中找不到产品编码为[" + productCode + "]的产品");
            }
            Product product = products.get(0);
            boolean isContains = false;
            for(int j= 0;j<product.getProductSbu().size();j++){
                 if(product.getProductSbu().get(j).getSbu().getId().equals(sbu.getId())){
                    isContains =true;
                    break;
                }
            }
            if(!isContains){
                ExceptionUtil.throwServiceException("第" + (i + 1) + "行,产品的SBU不包含"+sbuStr);
            }
            addInventoryItem.setProduct(product);
            addInventoryItem.setVonderCode(productCode);
            addInventoryItem.setName(product.getName());
            addInventoryItem.setDescription(product.getDetailDescription());
            addInventoryItem.setPerBranch(product.getPerBranch());
            addInventoryItem.setBranchPerBox(product.getBranchPerBox());

            cell = sheet.getCell(countNo++, i);
            productName = cell.getContents();
            if (!StringUtils.isBlank(productName)) {
                addInventoryItem.setName(productName);
            }



            cell = sheet.getCell(countNo++, i);
            productDescription = cell.getContents();
            if (!StringUtils.isBlank(productDescription)) {
                addInventoryItem.setDescription(productDescription);
            }

            cell = sheet.getCell(countNo++, i);
            productOrganizationStr = cell.getContents();
            if (StringUtils.isBlank(productOrganizationStr)) {
                msg = "第" + (i + 1) + "行." + "经营组织为空";
                ExceptionUtil.throwServiceException(msg);
            }
            filters.clear();
            filters.add(Filter.eq("name", productOrganizationStr));
            List<Organization> organizations = organizationService.findList(null, 1, filters, null);
            if (organizations.isEmpty()) {
                throw new RuntimeException("第" + (i + 1) + "行,在系统中找不到名称为[" + productOrganizationStr + "]的经营组织");
            }
            Organization productOrganization = organizations.get(0);
            //根据如是平台仓判断经营组织要都一样
            if(isHeadquarters){
                if(!productOrganization.getId().equals(warehouse.getManagementOrganization().getId())){
                    ExceptionUtil.throwServiceException("第" + (i + 1) + "行,该仓库是总部仓，导入的产品经营组织必须是仓库的经营组织");
                }
            }
            addInventoryItem.setProductOrganization(productOrganization);

            cell = sheet.getCell(countNo++, i);
            productLevelStr = cell.getContents();
            if (StringUtils.isBlank(productLevelStr)) {
                msg = "第" + (i + 1) + "行." + "产品等级为空";
                ExceptionUtil.throwServiceException(msg);
            }
            filters.clear();
            filters.add(Filter.eq("value", productLevelStr));
            filters.add(Filter.eq("code", "productLevel"));
            List<SystemDict> productLevels = systemDictService.findList(null, 1, filters, null);
            if (productLevels.isEmpty()) {
                throw new RuntimeException("第" + (i + 1) + "行,在系统中找不到名为[" + productLevelStr + "]的产品等级");
            }
            SystemDict productLevel = productLevels.get(0);
            addInventoryItem.setProductLevel(productLevel);


            cell = sheet.getCell(countNo++, i);
            branchQuantity = cell.getContents();
            if (StringUtils.isBlank(branchQuantity)) {
                msg = "第" + (i + 1) + "行." + "盘点支数为空";
                ExceptionUtil.throwServiceException(msg);
            }
            addInventoryItem.setBranchQuantity(new BigDecimal(branchQuantity));
            //产品转换率计算件数与数量
            BigDecimal branchQuantityNum = new BigDecimal(branchQuantity);
            BigDecimal boxQuantity = branchQuantityNum.divideToIntegralValue(product.getBranchPerBox());
            addInventoryItem.setBoxQuantity(boxQuantity);
            BigDecimal quantity = branchQuantityNum.multiply(product.getPerBranch());
            addInventoryItem.setQuantity(quantity);
            BigDecimal scatteredQuantity = branchQuantityNum.subtract(boxQuantity.multiply(product.getBranchPerBox()));
            addInventoryItem.setScatteredQuantity(scatteredQuantity);

            cell = sheet.getCell(countNo++, i);
            colorNumbersStr = cell.getContents();
            if (StringUtils.isBlank(colorNumbersStr)) {
                msg = "第" + (i + 1) + "行." + "色号为空";
                ExceptionUtil.throwServiceException(msg);
            }
            filters.clear();
            filters.add(Filter.eq("value", colorNumbersStr));
            filters.add(Filter.eq("code", "colorNumber"));
            List<SystemDict> colorNumbers = systemDictService.findList(null, 1, filters, null);
            if (organizations.isEmpty()) {
                throw new RuntimeException("第" + (i + 1) + "行,在系统中找不到名为[" + colorNumbersStr + "]的色号");
            }
            SystemDict colorNumber = colorNumbers.get(0);
            addInventoryItem.setColorNumbers(colorNumber);

            cell = sheet.getCell(countNo++, i);
            moistureContentsStr = cell.getContents();
            if (StringUtils.isBlank(moistureContentsStr)) {
                msg = "第" + i + "行." + "含水率为空";
                ExceptionUtil.throwServiceException(msg);
            }
            filters.clear();
            filters.add(Filter.eq("value", moistureContentsStr));
            filters.add(Filter.eq("code", "moistureContent"));
            List<SystemDict> moistureContents = systemDictService.findList(null, 1, filters, null);
            if (organizations.isEmpty()) {
                throw new RuntimeException("第" + (i + 1) + "行,在系统中找不到名为[" + moistureContentsStr + "]的含水率");
            }
            SystemDict moistureContent = moistureContents.get(0);
            addInventoryItem.setMoistureContents(moistureContent);

            cell = sheet.getCell(countNo++, i);
            warehouseBatchStr = cell.getContents();

            Long warehouseBatchIds[] = {0L};
            if(warehouse.getEnableBatch()) {
                if (StringUtils.isBlank(warehouseBatchStr)) {
                    msg = "第" + (i + 1) + "行." + "批次为空";
                    ExceptionUtil.throwServiceException(msg);
                }
                addInventoryItem.setBatchEncoding(warehouseBatchStr);
                filters.clear();
                filters.add(Filter.eq("batchEncoding", warehouseBatchStr));
                List<WarehouseBatch> warehouseBatchServiceList = warehouseBatchService.findList(null, 1, filters, null);
                if (warehouseBatchServiceList.isEmpty()) {
                    throw new RuntimeException("第" + (i + 1) + "行,在系统中找不到编码为[" + productCode + "]的批次");
                }
                WarehouseBatch warehouseBatch = warehouseBatchServiceList.get(0);
                warehouseBatchIds[0] = warehouseBatch.getId();
                if(warehouseBatch.getOrganization().getId()!=productOrganization.getId()){
                    ExceptionUtil.throwServiceException("第" + (i + 1) + "行批次的经营组织与该行产品的经营组织不一样");
                }
                addInventoryItem.setWarehouseBatch(warehouseBatch.getId().toString());
            }else{
                addInventoryItem.setWarehouseBatch(null);

            }

            cell = sheet.getCell(countNo++, i);
            WarehouseLocation warehouseLocation = null;
            if(warehouse.getEnableLocation()) {
                warehouseLocationStr = cell.getContents();
                if (StringUtils.isBlank(warehouseLocationStr)) {
                    msg = "第" + (i + 1) + "行." + "库位为空";
                    ExceptionUtil.throwServiceException(msg);
                }
                filters.clear();
                filters.add(Filter.eq("code", warehouseLocationStr));
                List<WarehouseLocation> warehouseLocations = warehouseLocationService.findList(null, 1, filters, null);
                if (organizations.isEmpty()) {
                    throw new RuntimeException("第" + (i + 1) + "行,在系统中找不到编码为[" + warehouseLocationStr + "]的库位");
                }
                warehouseLocation = warehouseLocations.get(0);
                addInventoryItem.setWarehouseLocation(warehouseLocation);
            }else{
                addInventoryItem.setWarehouseLocation(null);
            }
            cell = sheet.getCell(countNo++, i);
            newOldLogosStr = cell.getContents();
            if (StringUtils.isBlank(newOldLogosStr)) {
                msg = "第" + (i + 1) + "行." + "新旧标识为空";
                ExceptionUtil.throwServiceException(msg);
            }
            filters.clear();
            filters.add(Filter.eq("value", newOldLogosStr));
            filters.add(Filter.eq("code", "oldNewLogo"));
            List<SystemDict> newOldLogos = systemDictService.findList(null, 1, filters, null);
            if (organizations.isEmpty()) {
                throw new RuntimeException("第" + (i + 1) + "行,在系统中找不到名为[" + newOldLogosStr + "]的新旧标识");
            }
            SystemDict newOldLogo = newOldLogos.get(0);
            addInventoryItem.setNewOldLogos(newOldLogo);

            cell = sheet.getCell(countNo++, i);
            memo = cell.getContents();
            addInventoryItem.setMemo(memo);

            //查询原有库存原数量与计算差异数量
            Long warehouseLocationId = null;
            if(warehouseLocation != null){
                warehouseLocationId = warehouseLocation.getId();
            }
            List<Map<String, Object>> stockList = stockService.findViewStock(inventory.getWarehouse().getId(),addInventoryItem.getProductOrganization().getId(), product.getId(),
                    productLevel.getId(), addInventoryItem.getVonderCode(), colorNumber.getId().toString(), moistureContent.getId().toString(), warehouseBatchIds, newOldLogo.getId().toString(), warehouseLocationId,
                    null);
             BigDecimal regBranchQuantity = new BigDecimal(0);
            BigDecimal regQuantity = new BigDecimal(0);
            if(stockList.get(0).get("totalAttQuantity3") !=null) {
                 regBranchQuantity = (BigDecimal) stockList.get(0).get("totalAttQuantity3");
            }
            if(stockList.get(0).get("totalAttQuantity3") !=null) {
                regQuantity = (BigDecimal) stockList.get(0).get("totalAttQuantity1");
            }
            addInventoryItem.setRegBranchQuantity(regBranchQuantity);
            addInventoryItem.setRegQuantity(regQuantity);
            BigDecimal differenceBranchQuantity = branchQuantityNum.subtract(regBranchQuantity);
            BigDecimal differenceQuantity = quantity.subtract(regQuantity);
            addInventoryItem.setDifferenceBranchQuantity(differenceBranchQuantity);
            addInventoryItem.setDifferenceQuantity(differenceQuantity);


            addInventoryItem.setWarehouse(inventory.getWarehouse());
            addInventoryItem.setInventory(inventory);
            inventoryItemList.add(addInventoryItem);
            success++;
        }
        //插入库存盘点表
        this.inventoryImportInsert(inventory,inventoryItemList);
        int result = rows - 6;
        //返回导入行数
        msg = "msg:" + "总数" + result + "行,成功导入" + success + " 行明细. ";
        return msg;
    }

    public void inventoryImportInsert(Inventory inventory,List<InventoryItem> inventoryItemList) {
        inventory.setInventoryItems(inventoryItemList);
        //导入
        inventory.setSn(SnUtil.getInventorySn());
        inventory.setStatus(0);
        inventory.setBillDate(new Date());
        save(inventory);
        // 盘点库存单全链路
        List<String> inventorySns = new ArrayList<String>();
        inventorySns.add(inventory.getSn());
        orderFullLinkService.addFullLink(202, inventorySns, inventory.getSn(), "导入库存盘点单", null);


    }

    @Override
    public void checkWf(Long inventoryId) {

        Inventory inventory = find(inventoryId);
        //Gl日期
        if(ConvertUtil.isEmpty(inventory.getBillDate())){
            ExceptionUtil.throwServiceException("Gl日期不能为空");
        }
        //Gl日期校验
        SystemDict totalDateType =systemDictService.findSystemDictList("totalDateType", "应收账期").get(0);
        List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, inventory.getSaleOrg().getId(),
                inventory.getSbu().getId(), new Long[]{inventory.getOrganization().getId()}, inventory.getBillDate(),totalDateType);
        if(mapList.isEmpty()||mapList.size() ==0){
            throw new RuntimeException("GL日期不包含在总账日期内，请填写合适的单据日期");
        }
        List<InventoryItem> inventoryItems = inventory.getInventoryItems();
        if(inventory.getWarehouse().getEnableBatch()){
            for (InventoryItem inventoryItem : inventoryItems) {
                if(inventoryItem.getBatchEncoding() == null || inventoryItem.getBatchEncoding().equals("") ){
                    ExceptionUtil.throwServiceException("批次不能为空");
                }

            }
        }

        StoreMember storeMember = storeMemberService.getCurrent();
        //生成出库单的表头----出库单
        AmShipping exWarehouseAmShipping = new AmShipping();
        exWarehouseAmShipping.setSn(SnUtil.getAmShippingSn());
        exWarehouseAmShipping.setWarehouse(inventory.getWarehouse());
        exWarehouseAmShipping.setStatus(0);//未审核
        // 单据类型
        SystemDict exBillCategory = systemDictService.find(504L);
        exWarehouseAmShipping.setBillType(exBillCategory);
        //单据类别
        //exWarehouseAmShipping.setBillCategory();
        exWarehouseAmShipping.setCheckStoreMember(storeMember);
        exWarehouseAmShipping.setCheckDate(new Date());

        //生成入库单的表头----入库单
        AmShipping inWarehouseAmShipping = new AmShipping();
        inWarehouseAmShipping.setSn(SnUtil.getAmShippingSn());
        inWarehouseAmShipping.setWarehouse(inventory.getWarehouse());
        inWarehouseAmShipping.setStatus(0);//未审核
        // 单据类型
        SystemDict inBillCategory = systemDictService.find(505L);
        inWarehouseAmShipping.setBillType(inBillCategory);
        //单据类别
        //inWarehouseAmShipping.setBillCategory();
        inWarehouseAmShipping.setCheckStoreMember(storeMember);
        inWarehouseAmShipping.setCheckDate(new Date());


        List<AmShippingItem> exWarehouseAmShippingItems = new ArrayList<AmShippingItem>();
        List<AmShippingItem> inWarehouseAmShippingItems = new ArrayList<AmShippingItem>();
        //循环取出明细
        for (InventoryItem inventoryItem : inventoryItems) {
            if (inventoryItem.getDifferenceQuantity().compareTo(new BigDecimal(0)) == 0) {
                int i = 1;
            } else {
                if (inventoryItem.getDifferenceQuantity().compareTo(new BigDecimal(0)) == 1) {
                    //根据差异数据生成入货单明细
                    AmShippingItem inWarehouseAmShippingItem = new AmShippingItem();
                    this.updateExInWarehouseAmShipping(inWarehouseAmShipping, inWarehouseAmShippingItem, inventory, inventoryItem, inBillCategory);
                    inWarehouseAmShippingItems.add(inWarehouseAmShippingItem);
                } else {
                    //根据差异数据生成出货单明细
                    AmShippingItem exWarehouseAmShippingItem = new AmShippingItem();
                    this.updateExInWarehouseAmShipping(exWarehouseAmShipping, exWarehouseAmShippingItem, inventory, inventoryItem, exBillCategory);
                    exWarehouseAmShippingItems.add(exWarehouseAmShippingItem);
                }
            }
        }
        //保存出库单
        if (exWarehouseAmShippingItems.size() > 0) {
            exWarehouseAmShipping.getAmShippingItems().clear();
            exWarehouseAmShipping.getAmShippingItems().addAll(exWarehouseAmShippingItems);
            exWarehouseAmShipping.setMemo(inventory.getRemark());
            exWarehouseAmShipping.setSbu(inventory.getSbu());
            exWarehouseAmShipping.setOrganization(inventory.getOrganization());
            exWarehouseAmShipping.setSaleOrg(inventory.getSaleOrg());
            exWarehouseAmShipping.setBillCategory(systemDictService.find(707L));
            exWarehouseAmShipping.setBillDate(inventory.getBillDate());
            List<Filter> filters = new ArrayList<Filter>();
            filters.clear();
            filters.add(Filter.eq("username", inventory.getbCreater()));
            List<StoreMember> storeMembers = storeMemberService.findList(null, 1, filters, null);
            if (storeMembers.size() > 0) {
                exWarehouseAmShipping.setStoreMember(storeMembers.get(0));
            }
            amShippingService.save(exWarehouseAmShipping);
        }
        if (inWarehouseAmShippingItems.size() > 0) {
            //保存入库单
            inWarehouseAmShipping.getAmShippingItems().clear();
            inWarehouseAmShipping.getAmShippingItems().addAll(inWarehouseAmShippingItems);
            inWarehouseAmShipping.setMemo(inventory.getRemark());
            inWarehouseAmShipping.setSbu(inventory.getSbu());
            inWarehouseAmShipping.setOrganization(inventory.getOrganization());
            inWarehouseAmShipping.setSaleOrg(inventory.getSaleOrg());
            inWarehouseAmShipping.setBillCategory(systemDictService.find(706L));
            inWarehouseAmShipping.setBillDate(inventory.getBillDate());
            List<Filter> filters = new ArrayList<Filter>();
            filters.clear();
            filters.add(Filter.eq("username", inventory.getbCreater()));
            List<StoreMember> storeMembers = storeMemberService.findList(null, 1, filters, null);
            if (storeMembers.size() > 0) {
                inWarehouseAmShipping.setStoreMember(storeMembers.get(0));
            }
            amShippingService.save(inWarehouseAmShipping);
        }

        inventory.setStatus(2);
        inventory.setCheckMan(storeMemberService.getCurrent());
        update(inventory);
        // 盘点库存单全链路
        List<String> inventorySns = new ArrayList<String>();
        inventorySns.add(inventory.getSn());
        orderFullLinkService.addFullLink(202, inventorySns, inventory.getSn(), "审核库存盘点单", null);

    }

    @Override
    public void cancel(Inventory inventory) {
        inventory.setStatus(3);
        update(inventory);
        // 盘点库存单全链路
        List<String> inventorySns = new ArrayList<String>();
        inventorySns.add(inventory.getSn());
        orderFullLinkService.addFullLink(202, inventorySns, inventory.getSn(), "作废库存盘点单", null);

    }

    /**
     * 保存出入库单明细
     */
    public void updateExInWarehouseAmShipping(AmShipping exInWarehouseAmShipping, AmShippingItem exInWarehouseAmShippingItem, Inventory inventory, InventoryItem inventoryItem, SystemDict exInBillCategory) {
        //保存入货单明细
        exInWarehouseAmShippingItem.setAmShipping(exInWarehouseAmShipping);
        //产品编码
        exInWarehouseAmShippingItem.setProduct(inventoryItem.getProduct());
        exInWarehouseAmShippingItem.setVonderCode(inventoryItem.getVonderCode());
        //产品名称
        exInWarehouseAmShippingItem.setName(inventoryItem.getName());
        //产品描述
        exInWarehouseAmShippingItem.setDescription(inventoryItem.getDescription());
        //经营组织
        exInWarehouseAmShippingItem.setProductOrganization(inventoryItem.getProductOrganization());
        //产品级别
        exInWarehouseAmShippingItem.setProductLevel(inventoryItem.getProductLevel());
        //件数
        BigDecimal diffBoxQuantity=inventoryItem.getDifferenceBranchQuantity().divideToIntegralValue(inventoryItem.getBranchPerBox());
        exInWarehouseAmShippingItem.setBoxQuantity(diffBoxQuantity.abs());
        //支数
        exInWarehouseAmShippingItem.setBranchQuantity(inventoryItem.getDifferenceBranchQuantity().abs());
        //零散支数(=差异支数求余每箱支数)
        BigDecimal[] diffScatterQuantity = inventoryItem.getDifferenceBranchQuantity().divideAndRemainder(inventoryItem.getBranchPerBox());
        exInWarehouseAmShippingItem.setScatteredQuantity(diffScatterQuantity[1].abs());
        //数量
        exInWarehouseAmShippingItem.setQuantity(inventoryItem.getDifferenceQuantity().abs());
        //每支平方数
        exInWarehouseAmShippingItem.setPerBranch(inventoryItem.getPerBranch());
        //每箱支数
        exInWarehouseAmShippingItem.setBranchPerBox(inventoryItem.getBranchPerBox());
        //型号
        exInWarehouseAmShippingItem.setModel(inventoryItem.getModel());
        //色号
        exInWarehouseAmShippingItem.setColorNumbers(inventoryItem.getColorNumbers());
        //含水率
        exInWarehouseAmShippingItem.setMoistureContents(inventoryItem.getMoistureContents());
        //新旧标识
        exInWarehouseAmShippingItem.setNewOldLogos(inventoryItem.getNewOldLogos());
        //备注
        exInWarehouseAmShippingItem.setMemo(inventoryItem.getMemo());
        //关联盘点单
        exInWarehouseAmShippingItem.setInventory(inventory);
        //关联盘点单明细
        exInWarehouseAmShippingItem.setInventoryItem(inventoryItem);

        //判断当前仓库是否使用批次
        if (inventory.getWarehouse().getEnableBatch() && inventoryItem.getWarehouseBatch()!=null) {
            //批次
            exInWarehouseAmShippingItem.setBatch(inventoryItem.getWarehouseBatch());
            exInWarehouseAmShippingItem.setBatchEncoding(inventoryItem.getBatchEncoding());
            List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
            if (!ConvertUtil.isEmpty(exInWarehouseAmShippingItem.getBatch())) {
                String[] batchs = exInWarehouseAmShippingItem.getBatch().split(";");
                for (int i = 0; i < batchs.length; i++) {
                    if (!ConvertUtil.isEmpty(batchs[i])) {
                        WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
                        // 订单类型
                        warehouseBillBatch.setSysType(exInBillCategory);
                        // 出入库单
                        warehouseBillBatch.setAmShipping(exInWarehouseAmShipping);
                        // 出入库单明细
                        warehouseBillBatch.setAmShippingItem(exInWarehouseAmShippingItem);
                        // 批次
                        warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
                        warehouseBillBatchList.add(warehouseBillBatch);
                    }
                }
            }
            exInWarehouseAmShippingItem.getWarehouseBillBatchs().clear();
            exInWarehouseAmShippingItem.getWarehouseBillBatchs().addAll(warehouseBillBatchList);
            // 批次列表
            this.saveWarehouseBatchItem(exInWarehouseAmShipping, exInWarehouseAmShippingItem, inventoryItem.getProduct(), inventoryItem.getWarehouseBatch());
        }
    }


    /**
     * 保存或更新批次列表
     *
     * @param amShipping
     * @param amShippingItem
     * @param product
     */
    private void saveWarehouseBatchItem(AmShipping amShipping, AmShippingItem amShippingItem, Product product, String warehouseBatchId) {

        // 批次明细列表
        WarehouseBatchItem warehouseBatchItem = new WarehouseBatchItem();
        warehouseBatchItem.setCompanyInfoId(9L);
        //箱数
        warehouseBatchItem.setShippedBoxQuantity(amShippingItem.getBoxQuantity());
        //支数
        warehouseBatchItem.setShippedBranchQuantity(amShippingItem.getBranchQuantity());
        //零散支数
        warehouseBatchItem.setShippedBranchScattered(amShippingItem.getScatteredQuantity());
        // 单据类型
        warehouseBatchItem.setBillType(amShipping.getBillType());
        // 仓库
        warehouseBatchItem.setWarehouse(amShipping.getWarehouse());
        // 出入单
        warehouseBatchItem.setAmShipping(amShipping);
        // 单据状态
        warehouseBatchItem.setStatus(amShipping.getStatus());
        // 单据日期
        // warehouseBatchItem.setDocumentDate(amShipping.getBillDate());
        // 库位
        if (ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())
                || (!ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())
                && ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation().getId()))) {
            warehouseBatchItem.setWarehouseLocation(null);
        }
        // 产品
        warehouseBatchItem.setProduct(product);
        // 等级
        warehouseBatchItem.setProductLevel(amShippingItem.getProductLevel());
        //色号
        warehouseBatchItem.setColorNumbers(amShippingItem.getColorNumbers());
        //含水率
        warehouseBatchItem.setMoistureContents(amShippingItem.getMoistureContents());
        //新旧标识
        warehouseBatchItem.setNewOldLogos(amShippingItem.getNewOldLogos());
        // 经营组织
        warehouseBatchItem.setOrganization(amShippingItem.getProductOrganization());
        // 出入库明细
        warehouseBatchItem.setAmShippingItem(amShippingItem);
        // 数量
        warehouseBatchItem.setQuantity(amShippingItem.getQuantity());
         if (warehouseBatchId != null) {
            //批次
            warehouseBatchItem.setWarehouseBatch(warehouseBatchService.find(Long.valueOf(warehouseBatchId)));
        }
        warehouseBatchItemService.save(warehouseBatchItem);
        amShippingItem.getWarehouseBatchItems().clear();
        amShippingItem.getWarehouseBatchItems().add(warehouseBatchItem);

    }


    /**日期格式校验
     * @param str
     * @return
     */
    private boolean isValidDate(String str) {
        boolean convertSuccess = true;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException e) {
            convertSuccess = false;
        }
        return convertSuccess;
    }

}
