package net.shopxx.stock.service.impl;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.AmShippingItem;
import net.shopxx.product.entity.Product;
import net.shopxx.stock.dao.WarehouseStockDao;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseBatch;
import net.shopxx.stock.entity.WarehouseBatchItem;
import net.shopxx.stock.entity.WarehouseLocation;
import net.shopxx.stock.entity.WarehouseStock;
import net.shopxx.stock.entity.WarehouseStockOperation;
import net.shopxx.stock.service.WarehouseStockOperationService;
import net.shopxx.stock.service.WarehouseStockService;
@Service("warehouseStockServiceImpl")
public class WarehouseStockServiceImpl extends BaseServiceImpl<WarehouseStock> implements WarehouseStockService{
	
	@Resource(name = "warehouseStockDao")
	private WarehouseStockDao warehouseStockDao;
	@Resource(name = "warehouseStockOperationServiceImpl")
	private WarehouseStockOperationService warehouseStockOperationService;
	
	
	/**
	 * 参数不能为空
	 * @param warehouseStock
	 */
	private void checkParamIsNotNull(WarehouseStock warehouseStock){
		/**现有量（箱）*/
		if(ConvertUtil.isEmpty(warehouseStock.getOnhandBoxQuantity())){
			warehouseStock.setOnhandBoxQuantity(BigDecimal.ZERO);
		}
		/**现有量（支）*/
		if(ConvertUtil.isEmpty(warehouseStock.getOnhandBranchQuantity())){
			warehouseStock.setOnhandBranchQuantity(BigDecimal.ZERO);
		}
		/**现有量平方数*/
		if(ConvertUtil.isEmpty(warehouseStock.getOnhandQuantity())){
			warehouseStock.setOnhandQuantity(BigDecimal.ZERO);
		}
		/**可处理量（箱）*/
		if(ConvertUtil.isEmpty(warehouseStock.getAvailableBoxQuantity())){
			warehouseStock.setAvailableBoxQuantity(BigDecimal.ZERO);
		}
		/**可处理量（支）*/
		if(ConvertUtil.isEmpty(warehouseStock.getAvailableBranchQuantity())){
			warehouseStock.setAvailableBranchQuantity(BigDecimal.ZERO);
		}
		/**可处理量平方数*/
		if(ConvertUtil.isEmpty(warehouseStock.getAvailableQuantity())){
			warehouseStock.setAvailableQuantity(BigDecimal.ZERO);
		}
	}
	
	
	/**
	 * 校验参数为空 抛异常
	 * @param billType
	 * @param
	 * @param warehouse
	 * @param product
	 * @param productLevel
	 * @param organization
	 * @param colorNumber
	 * @param moistureContent
	 * @param oldNewLogo
	 * @param warehouseBatchItem
	 * @param warehouseBatch
	 * @param boxQuantity
	 * @param branchQuantity
	 * @param quantity
	 */
	private void checkParamIsNotNull(SystemDict billType,Warehouse warehouse,Product product,
			SystemDict productLevel,Organization organization,SystemDict colorNumber,
			SystemDict moistureContent,SystemDict oldNewLogo,WarehouseBatchItem warehouseBatchItem,
			WarehouseBatch warehouseBatch,BigDecimal boxQuantity, BigDecimal branchQuantity,
			BigDecimal quantity){
		
		if(ConvertUtil.isEmpty(billType)){
			ExceptionUtil.throwServiceException("单据类型不能为空");
		}
		if(ConvertUtil.isEmpty(warehouse)){
			ExceptionUtil.throwServiceException("仓库不能为空");
		}
		if(ConvertUtil.isEmpty(product)){
			ExceptionUtil.throwServiceException("产品不能为空");
		}
		if(ConvertUtil.isEmpty(productLevel)){
			ExceptionUtil.throwServiceException("产品等级不能为空");
		}
		if(ConvertUtil.isEmpty(organization)){
			ExceptionUtil.throwServiceException("产品经营组织不能为空");
		}
		if(ConvertUtil.isEmpty(colorNumber)){
			ExceptionUtil.throwServiceException("色号不能为空");
		}
		if(ConvertUtil.isEmpty(moistureContent)){
			ExceptionUtil.throwServiceException("含水率不能为空");
		}
		if(ConvertUtil.isEmpty(oldNewLogo)){
			ExceptionUtil.throwServiceException("新旧标识不能为空");
		}
		if(!ConvertUtil.isEmpty(warehouseBatchItem)){
			if(ConvertUtil.isEmpty(warehouseBatch)){
				ExceptionUtil.throwServiceException("批次不能为空");
			}
		}
	}
	
		
	@Override
	@Transactional
	public void loadWarehouseStock(AmShipping amShipping,SystemDict actionType) {
		
		if(ConvertUtil.isEmpty(actionType)){
			ExceptionUtil.throwServiceException("操作类型不能为空");
		}
		if(amShipping.getBillType().getValue().equals("入仓") && 
				(actionType.getValue().equals("保存") || actionType.getValue().equals("作废"))){
			return;
		}
		//释放库存
		warehouseStockOperationService.deleteWarehouseStockOperation(amShipping.getId(),actionType);
		//获取出入库明细
		List<AmShippingItem> amShippingItemList = amShipping.getAmShippingItems();
		//判断出库库明细是否为空      是侧返回
		if(amShippingItemList.isEmpty() || amShippingItemList.size()==0){
			return;
		}
		for (AmShippingItem amShippingItem : amShippingItemList) {
			//出入库明细批次
			List<WarehouseBatchItem> warehouseBatchItemList = amShippingItem.getWarehouseBatchItems();
			if(!warehouseBatchItemList.isEmpty() && warehouseBatchItemList.size()>0){
				for (WarehouseBatchItem warehouseBatchItem : warehouseBatchItemList) {
					//校验参数为空 抛异常
					this.checkParamIsNotNull(amShipping.getBillType(),amShipping.getWarehouse(),warehouseBatchItem.getProduct(),
							warehouseBatchItem.getProductLevel(),warehouseBatchItem.getOrganization(),warehouseBatchItem.getColorNumbers(),
							warehouseBatchItem.getMoistureContents(),warehouseBatchItem.getNewOldLogos(),warehouseBatchItem,
							warehouseBatchItem.getWarehouseBatch(),warehouseBatchItem.getShippedBoxQuantity(),warehouseBatchItem.getShippedBranchQuantity(),
							warehouseBatchItem.getQuantity());
					//查询或新增库存
					WarehouseStock warehouseStock = this.queryOrAddWarehouseStock(actionType,amShipping.getWarehouse(),warehouseBatchItem.getProduct(),
							warehouseBatchItem.getProductLevel(),warehouseBatchItem.getOrganization(),warehouseBatchItem.getColorNumbers(),
							warehouseBatchItem.getMoistureContents(),warehouseBatchItem.getWarehouseBatch(), warehouseBatchItem.getNewOldLogos(), 
							warehouseBatchItem.getWarehouseLocation());
					//保存仓库库存操作
					warehouseStockOperationService.saveWarehouseStockOperation(amShipping.getBillType(),actionType,amShipping,amShippingItem,
							warehouseBatchItem,warehouseStock,warehouseBatchItem.getShippedBoxQuantity(),warehouseBatchItem.getShippedBranchQuantity(),
							warehouseBatchItem.getQuantity());
				}
			}else{
				//校验参数为空 抛异常
				this.checkParamIsNotNull(amShipping.getBillType(),amShipping.getWarehouse(),amShippingItem.getProduct(),
						amShippingItem.getProductLevel(),amShippingItem.getProductOrganization(),amShippingItem.getColorNumbers(),
						amShippingItem.getMoistureContents(),amShippingItem.getNewOldLogos(),null,null,amShippingItem.getBoxQuantity(),
						amShippingItem.getBranchQuantity(),amShippingItem.getQuantity());
				//查询或新增库存
				WarehouseStock warehouseStock = this.queryOrAddWarehouseStock(actionType,amShipping.getWarehouse(),amShippingItem.getProduct(),
						amShippingItem.getProductLevel(),amShippingItem.getProductOrganization(),amShippingItem.getColorNumbers(),
						amShippingItem.getMoistureContents(),null,amShippingItem.getNewOldLogos(),amShippingItem.getWarehouseLocation());
				//保存仓库库存操作
				warehouseStockOperationService.saveWarehouseStockOperation(amShipping.getBillType(),actionType,amShipping,amShippingItem,null,
						warehouseStock,amShippingItem.getBoxQuantity(),amShippingItem.getBranchQuantity(),amShippingItem.getQuantity());
			}
		}
	}
	
	
	
	@Override
	public WarehouseStock queryOrAddWarehouseStock(SystemDict actionType,Warehouse warehouse, 
			Product product,SystemDict productLevel,Organization organization,SystemDict colorNumber,
			SystemDict moistureContent,WarehouseBatch warehouseBatch,SystemDict oldNewLogo, 
			WarehouseLocation warehouseLocation) {
		
		WarehouseStock warehouseStock = null;
		String str= ",";
		try {
			List<Filter> filters = new ArrayList<Filter>();
			Long companyInfoId = 9L;
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("warehouse", warehouse.getId()));
			filters.add(Filter.eq("product", product.getId()));
			filters.add(Filter.eq("productLevel", productLevel.getId()));
			filters.add(Filter.eq("organization", organization.getId()));
			filters.add(Filter.eq("colorNumber", colorNumber.getId()));
			filters.add(Filter.eq("moistureContent", moistureContent.getId()));
			filters.add(Filter.eq("oldNewLogo", oldNewLogo.getId()));
			if(!ConvertUtil.isEmpty(warehouseBatch)){
				filters.add(Filter.eq("warehouseBatch", warehouseBatch.getId()));
			}else{
				filters.add(Filter.isNull("warehouseBatch"));
			}
			if(!ConvertUtil.isEmpty(warehouseLocation)){
				filters.add(Filter.eq("warehouseLocation", warehouseLocation.getId()));
			}else{
				filters.add(Filter.isNull("warehouseLocation"));
			}
			warehouseStock = find(filters);
			if(ConvertUtil.isEmpty(warehouseStock)){
				warehouseStock = new WarehouseStock();
                /**公司id*/
                warehouseStock.setCompanyInfoId(companyInfoId);
				/**操作类型*/
				warehouseStock.setActionType(actionType);
				/**仓库*/
				warehouseStock.setWarehouse(warehouse);
				/**产品*/
				warehouseStock.setProduct(product);
				/**产品级别*/
				warehouseStock.setProductLevel(productLevel);
				/**经营组织*/
				warehouseStock.setOrganization(organization);
				/**色号*/
				warehouseStock.setColorNumber(colorNumber);
				/**含水率*/
				warehouseStock.setMoistureContent(moistureContent); 
				/**新旧标识*/
				warehouseStock.setOldNewLogo(oldNewLogo);
				/**批次*/
				warehouseStock.setWarehouseBatch(warehouseBatch);
				/**库位*/
				warehouseStock.setWarehouseLocation(warehouseLocation); 
				/**标识唯一值*/
				String onlyValue = warehouse.getId()+str+product.getId()+str+productLevel.getId()+str+organization.getId()+str
						+colorNumber.getId()+str+moistureContent.getId()+str+oldNewLogo.getId();
				if(!ConvertUtil.isEmpty(warehouseBatch)){
					onlyValue += str+warehouseBatch.getId();
				}else{
					onlyValue += str+"@";
				}
				if(!ConvertUtil.isEmpty(warehouseLocation)){
					onlyValue += str+warehouseLocation.getId();
				}else{
					onlyValue += str+"#";
				}	
				warehouseStock.setOnlyValue(onlyValue);
				//初始化现有量、可处理量
				this.checkParamIsNotNull(warehouseStock);
				save(warehouseStock);
			}
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
		return warehouseStock;
	}

	
	/**
	 * 释放减库存
	 * @param warehouseStock
	 * @param warehouseStockOperation
	 */
	private void subtractStock(WarehouseStock warehouseStock,
			WarehouseStockOperation warehouseStockOperation){
		
		/**现有量（箱）*/
		warehouseStock.setOnhandBoxQuantity(
				warehouseStock.getOnhandBoxQuantity().subtract(
						warehouseStockOperation.getOnhandBoxQuantity()));
		/**现有量（支）*/
		warehouseStock.setOnhandBranchQuantity(
				warehouseStock.getOnhandBranchQuantity().subtract(
						warehouseStockOperation.getOnhandBranchQuantity()));
		/**现有量平方数*/
		warehouseStock.setOnhandQuantity(
				warehouseStock.getOnhandQuantity().subtract(
						warehouseStockOperation.getOnhandQuantity()));
		/**可处理量（箱）*/
		warehouseStock.setAvailableBoxQuantity(
				warehouseStock.getAvailableBoxQuantity().subtract(
						warehouseStockOperation.getAvailableBoxQuantity()));
		/**可处理量（支）*/
		warehouseStock.setAvailableBranchQuantity(
				warehouseStock.getAvailableBranchQuantity().subtract(
						warehouseStockOperation.getAvailableBranchQuantity()));
		/**可处理量平方数*/
		warehouseStock.setAvailableQuantity(
				warehouseStock.getAvailableQuantity().subtract(
						warehouseStockOperation.getAvailableQuantity()));
	}
	
	/**
	 * 保存或者更新添加库存
	 * @param warehouseStock
	 * @param warehouseStockOperation
	 */
	private void addUpdateStock(WarehouseStock warehouseStock,
			WarehouseStockOperation warehouseStockOperation){
		/**现有量（箱）*/
		warehouseStock.setOnhandBoxQuantity(
				warehouseStock.getOnhandBoxQuantity().add(warehouseStockOperation.getOnhandBoxQuantity()));
		/**现有量（支）*/
		warehouseStock.setOnhandBranchQuantity(
				warehouseStock.getOnhandBranchQuantity().add(warehouseStockOperation.getOnhandBranchQuantity()));
		/**现有量平方数*/
		warehouseStock.setOnhandQuantity(
				warehouseStock.getOnhandQuantity().add(warehouseStockOperation.getOnhandQuantity()));
		/**可处理量（箱）*/
		warehouseStock.setAvailableBoxQuantity(
				warehouseStock.getAvailableBoxQuantity().add(warehouseStockOperation.getAvailableBoxQuantity()));
		/**可处理量（支）*/
		warehouseStock.setAvailableBranchQuantity(
				warehouseStock.getAvailableBranchQuantity().add(warehouseStockOperation.getAvailableBranchQuantity()));
		/**可处理量平方数*/
		warehouseStock.setAvailableQuantity(
				warehouseStock.getAvailableQuantity().add(warehouseStockOperation.getAvailableQuantity()));
	}
	
	/**
	 * 更新
	 */
	public void updateWarehouseStock(WarehouseStock warehouseStock, 
			WarehouseStockOperation warehouseStockOperation,Long type,
			SystemDict billType,SystemDict actionType) {
		try {
			if(type == 0){
				//释放减库存
				this.subtractStock(warehouseStock, warehouseStockOperation);
			}else if (type == 1) {
				if(billType.getValue().equals("出仓")){
					if(actionType.getValue().equals("保存")){
						//添加更新库存
						this.addUpdateStock(warehouseStock,warehouseStockOperation);
					}else if (actionType.getValue().equals("审核")) {
						this.addUpdateStock(warehouseStock, warehouseStockOperation);
					}
				}else if (billType.getValue().equals("入仓")) {
					if(actionType.getValue().equals("审核")){
						//添加更新库存
						this.addUpdateStock(warehouseStock,warehouseStockOperation);
					}
				}
			}
			//操作类型
			warehouseStock.setActionType(actionType);
			//更新库存
			update(warehouseStock);
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
	}


	@Override
	public List<Map<String, Object>> findWarehouseStockList(Long warehouseId,
			String productName, String vonderCode, String model, String negative) {
		return warehouseStockDao.findWarehouseStockList(warehouseId, productName, 
				vonderCode, model,negative);
	}

}
