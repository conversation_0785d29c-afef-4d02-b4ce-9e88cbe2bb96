package net.shopxx.stock.service.impl;

import java.util.*;

import javax.annotation.Resource;
import javax.servlet.ServletContext;


import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;

import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.util.CommonVariable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.stock.dao.WarehouseBaseDao;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseLocation;
import net.shopxx.stock.entity.WarehouseSaleOrg;
import net.shopxx.stock.entity.WarehouseSbu;
import net.shopxx.stock.entity.WarehouseStoreMember;
import net.shopxx.stock.entity.WarehouseSmethod;
import net.shopxx.stock.entity.WarehouseStore;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseSaleOrgBaseService;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.stock.service.warehouseSbuService;
import org.springframework.web.context.ServletContextAware;

/**
 * Service - 仓库
 */
@Service("warehouseBaseServiceImpl")
public class WarehouseBaseServiceImpl extends ActWfBillServiceImpl<Warehouse>
		implements WarehouseBaseService, ServletContextAware {

	@Resource(name = "warehouseBaseDao")
	private WarehouseBaseDao warehouseBaseDao;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "warehouseSaleOrgBaseServiceImpl")
	private WarehouseSaleOrgBaseService warehouseSaleOrgBaseService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "warehouseSbuServiceImpl")
	private warehouseSbuService warehouseSbuService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
    @Resource(name = "orderFullLinkServiceImpl")
    private OrderFullLinkService orderFullLinkService;

	@Override
	@Transactional
	public void saveWarehouse(Warehouse warehouse, Long areaId,Long[] saleOrgId,
			Boolean[] isChecks,Long[] areaIds, Long stores, Long deliveryCorpId,
			Long productionPlantId,Integer warehouseStatus) {

		warehouse.setWarehouseStatus(warehouseStatus);
		if(!ConvertUtil.isEmpty(productionPlantId)){
			SystemDict productionPlant = systemDictBaseService.find(productionPlantId);
			warehouse.setProductionPlant(productionPlant);
		}
		if (areaId != null) {
			Area area = areaBaseService.find(areaId);
			warehouse.setArea(area);
			if (area.getParent() != null) {
				Area area2 = area.getParent();
				if (area2.getParent() != null) {
					Area area3 = area2.getParent();
					warehouse.setCity(area2.getName());
					warehouse.setProvince(area3.getName());
					warehouse.setDistrict(area.getName());
				}else {
					warehouse.setProvince(area2.getName());
					warehouse.setCity(area.getName());
				}
			}
		}
		if (areaIds.length > 0) {
			List<Long> aIds = new ArrayList<Long>();
			for (Long aId : areaIds) {
				aIds.add(aId);
			}
			List<Filter> fis = new ArrayList<Filter>();
			fis.add(Filter.in("id", aIds));
			List<Area> areas = areaBaseService.findList(null, fis, null);
			warehouse.setAreas(new HashSet<Area>(areas));
		}

		/*
		 * //sbu保存 List<WarehouseSbu> warehouseSbuList =
		 * warehouse.getWarehouseSbu(); for (Iterator<WarehouseSbu> iterator =
		 * warehouseSbuList.iterator(); iterator.hasNext();) { WarehouseSbu
		 * warehouseSbu = (WarehouseSbu) iterator.next(); if (warehouseSbu ==
		 * null || warehouseSbu.getSbu() == null) { iterator.remove(); } Sbu sbu
		 * = sbuService.find(warehouseSbu.getSbu().getId());
		 * warehouseSbu.setSbu(sbu); warehouseSbu.setWarehouse(warehouse); }
		 */
		//sbu保存
		List<WarehouseSbu> warehouseSbuList = warehouse.getWarehouseSbu();
		for (Iterator<WarehouseSbu> iterator = warehouseSbuList.iterator(); iterator.hasNext();) {
			WarehouseSbu warehouseSbu = (WarehouseSbu) iterator.next();
			if (warehouseSbu == null
					|| warehouseSbu.getSbu() == null
					|| (warehouseSbu.getSbu() != null && warehouseSbu.getSbu()
							.getId() == null)) {
				iterator.remove();
				continue;
			}
			Sbu sbu = sbuService.find(warehouseSbu.getSbu().getId());
			warehouseSbu.setSbu(sbu);
			warehouseSbu.setWarehouse(warehouse);
			//warehouseSbuService.update(warehouseSbu);
		}
		warehouse.setWarehouseSbu(warehouseSbuList);
		//仓库设置保存
		List<WarehouseStoreMember> warehouseStoreMemberList = warehouse.getWarehouseStoreMember();
		if(!warehouseStoreMemberList.isEmpty() && warehouseStoreMemberList.size()>0){
			for (Iterator<WarehouseStoreMember> iterator = warehouseStoreMemberList.iterator(); iterator.hasNext();) {
				WarehouseStoreMember warehouseStoreMember = (WarehouseStoreMember) iterator.next();
				if(!ConvertUtil.isEmpty(warehouseStoreMember.getStoreMember()) && !ConvertUtil.isEmpty(warehouseStoreMember.getStoreMember().getId())){
					warehouseStoreMember.setWarehouse(warehouse);
					StoreMember storeMemberWarehouse = storeMemberService.find(warehouseStoreMember.getStoreMember().getId());
					if(ConvertUtil.isEmpty(storeMemberWarehouse)){
						iterator.remove();
						continue;
					}
					warehouseStoreMember.setStoreMember(storeMemberWarehouse);
				}else{
					iterator.remove();
					continue;
				}
			}
		}
		warehouse.setWarehouseStoreMember(warehouseStoreMemberList);
		//发运明细保存
		List<String> list = new ArrayList<String>();
		List<WarehouseSmethod> warehouseSmethodList = warehouse.getWarehouseSmethodList();
		for (Iterator<WarehouseSmethod> iterator = warehouseSmethodList.iterator();
				iterator.hasNext();) {
			WarehouseSmethod warehouseSmethod = (WarehouseSmethod) iterator.next();
			if (warehouseSmethod.getSmethod() == null
					|| warehouseSmethod.getSmethod().getId() == null) {
				iterator.remove();
				continue;
			}
			warehouseSmethod.setWarehouse(warehouse);
			SystemDict shippingWay = systemDictService.find(warehouseSmethod.getSmethod().getId());
			//不能有相同的发运方式
			if(list.contains(shippingWay.getValue())){
				ExceptionUtil.throwServiceException("不能有相同的发运方式");
			}
			list.add(shippingWay.getValue());
			warehouseSmethod.setSmethod(shippingWay);
		}
		warehouse.setWarehouseSmethodList(warehouseSmethodList);
		//warehouse.setSaleOrg(saleOrgBaseService.find(saleOrgId));
		if (warehouse.getFactorySystemDict() == null
				|| warehouse.getFactorySystemDict().getId() == null) {
			warehouse.setFactorySystemDict(null);
		}

		if (warehouse.getManagementOrganization() == null
				|| warehouse.getManagementOrganization().getId() == null) {
			warehouse.setManagementOrganization(null);
		}

		if (warehouse.getStockSystemDict() == null
				|| warehouse.getStockSystemDict().getId() == null) {
			warehouse.setStockSystemDict(null);
		}

		if (warehouse.getTypeSystemDict() == null
				|| warehouse.getTypeSystemDict().getId() == null) {
			warehouse.setTypeSystemDict(null);
		}

		warehouse.setSn(Sequence.getInstance().getSequence(null));
		save(warehouse);
		saveWarehouseOfSaleOrg(saleOrgId, isChecks, warehouse);

		//仓库跟客户关系
		if (warehouse.getType() != null
				&& warehouse.getType() == 1
				&& stores != null) {
			WarehouseStore ws = new WarehouseStore();
			Store store = storeBaseService.find(stores);
			ws.setStore(store);
			ws.setWarehouse(warehouse);
			warehouseStoreBaseService.save(ws);
		}
	}

	@Override
	@Transactional
	public void updateWarehouse(Warehouse warehouse, Long areaId, Long[] saleOrgId, 
			Boolean[] isChecks, Long[] areaIds, Long stores, Long deliveryCorpId,
			Long productionPlantId,Integer warehouseStatus) {

		warehouse.setWarehouseStatus(warehouseStatus);
		Warehouse pWarehouse = this.find(warehouse.getId());
		if (areaId != null) {
			Area area = areaBaseService.find(areaId);
			warehouse.setArea(area);
			if (area.getParent() != null) {
				Area area2 = area.getParent();
				if (area2.getParent() != null) {
					Area area3 = area2.getParent();
					warehouse.setCity(area2.getName());
					warehouse.setProvince(area3.getName());
					warehouse.setDistrict(area.getName());
				}else {
					warehouse.setProvince(area2.getName());
					warehouse.setCity(area.getName());
				}
			}
		}
		if (areaIds.length > 0) {
			List<Long> aIds = new ArrayList<Long>();
			for (Long aId : areaIds) {
				aIds.add(aId);
			}
			List<Filter> fis = new ArrayList<Filter>();
			fis.add(Filter.in("id", aIds));
			List<Area> areas = areaBaseService.findList(null, fis, null);
			warehouse.setAreas(new HashSet<Area>(areas));
		}
		//sbu保存
		List<WarehouseSbu> warehouseSbuList = warehouse.getWarehouseSbu();
		for (Iterator<WarehouseSbu> iterator = warehouseSbuList.iterator(); iterator.hasNext();) {
			WarehouseSbu warehouseSbu = (WarehouseSbu) iterator.next();
			if (warehouseSbu == null || warehouseSbu.getSbu() == null
					|| (warehouseSbu.getSbu() != null && warehouseSbu.getSbu().getId() == null)) {
				iterator.remove();
				continue;
			}
			Sbu sbu = sbuService.find(warehouseSbu.getSbu().getId());
			warehouseSbu.setSbu(sbu);
			warehouseSbu.setWarehouse(warehouse);
		}
		warehouse.setWarehouseSbu(warehouseSbuList);
		//仓库用户保存
		List<WarehouseStoreMember> warehouseStoreMemberList = warehouse.getWarehouseStoreMember();	
		if(!warehouseStoreMemberList.isEmpty() && warehouseStoreMemberList.size()>0){
			for (Iterator<WarehouseStoreMember> iterator = warehouseStoreMemberList.iterator(); iterator.hasNext();) {
				WarehouseStoreMember warehouseStoreMember = (WarehouseStoreMember) iterator.next();
				if(!ConvertUtil.isEmpty(warehouseStoreMember.getStoreMember()) && !ConvertUtil.isEmpty(warehouseStoreMember.getStoreMember().getId())){
					warehouseStoreMember.setWarehouse(warehouse);
					StoreMember storeMemberWarehouse = storeMemberService.find(warehouseStoreMember.getStoreMember().getId());
					if(ConvertUtil.isEmpty(storeMemberWarehouse)){
						iterator.remove();
						continue;
					}
					warehouseStoreMember.setStoreMember(storeMemberWarehouse);
				}else{
					iterator.remove();
					continue;
				}
			}
		}
		warehouse.setWarehouseStoreMember(warehouseStoreMemberList);
		//发运明细保存
		List<String> list = new ArrayList<String>();
		List<WarehouseSmethod> warehouseSmethodList = warehouse.getWarehouseSmethodList();
		for (Iterator<WarehouseSmethod> iterator = warehouseSmethodList.iterator();
			iterator.hasNext();) {
			WarehouseSmethod warehouseSmethod = (WarehouseSmethod) iterator.next();
			if (warehouseSmethod.getSmethod() == null|| warehouseSmethod.getSmethod().getId() == null) {
				iterator.remove();
					continue;
			}
			warehouseSmethod.setWarehouse(warehouse);
			SystemDict shippingWay = systemDictService.find(warehouseSmethod.getSmethod().getId());
			//不能有相同的发运方式
			if(list.contains(shippingWay.getValue())){
				ExceptionUtil.throwServiceException("不能有相同的发运方式");
			}
			list.add(shippingWay.getValue());
			warehouseSmethod.setSmethod(shippingWay);
		}
		warehouse.setWarehouseSmethodList(warehouseSmethodList);
		if (warehouse.getManagementOrganization() == null || warehouse.getManagementOrganization().getId() == null) {
			warehouse.setManagementOrganization(null);
		}
		if (warehouse.getStockSystemDict() == null || warehouse.getStockSystemDict().getId() == null) {
			warehouse.setStockSystemDict(null);
		}
		if (warehouse.getTypeSystemDict() == null || warehouse.getTypeSystemDict().getId() == null) {
			warehouse.setTypeSystemDict(null);
		}
		if (warehouse.getFactorySystemDict() == null || warehouse.getFactorySystemDict().getId() == null) {
			warehouse.setFactorySystemDict(null);
		}
		if(!ConvertUtil.isEmpty(productionPlantId)){
			SystemDict productionPlant = systemDictBaseService.find(productionPlantId);
			warehouse.setProductionPlant(productionPlant);
		}else{
			warehouse.setProductionPlant(null);
		}
		//库位保存
		if(!ConvertUtil.isEmpty(pWarehouse)){
			if(!pWarehouse.getWarehouseLocations().isEmpty() && pWarehouse.getWarehouseLocations().size()>0){
				warehouse.getWarehouseLocations().clear();
				warehouse.getWarehouseLocations().addAll(pWarehouse.getWarehouseLocations());
			}
		}
		update(warehouse, "sn");
		saveWarehouseOfSaleOrg(saleOrgId,isChecks,warehouse);

		//删除仓库客户关系
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("warehouse", warehouse));
		List<WarehouseStore> warehouseStores = warehouseStoreBaseService.findList(1,
				filters,
				null);
		WarehouseStore warehouseStore = null;
		if (!warehouseStores.isEmpty()) {
			warehouseStore = warehouseStores.get(0);
			warehouseStoreBaseService.delete(warehouseStore);
		}

		//仓库客户关系
		if (warehouse.getType() != null
				&& warehouse.getType() == 1
				&& stores != null) {
			WarehouseStore ws = new WarehouseStore();
			Store store = storeBaseService.find(stores);
			ws.setStore(store);
			ws.setWarehouse(warehouse);
			ws.setCreateDate(new Date());
			warehouseStoreBaseService.save(ws);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public boolean existsWarehouseArea(Long warehouseId, Long aeraId) {
		return warehouseBaseDao.existsWarehouseArea(warehouseId, aeraId);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn, String name,
			String erp_warehouse_code, Boolean isEnabled, Long supplierId,
			Integer type, Integer warehouseType, Long saleOrgId,
			Long organizationId, Long sbuId, Pageable pageable,
			Long sign,Integer isNotwarehouseType) {

		return warehouseBaseDao.findPage(sn,
				name,
				erp_warehouse_code,
				isEnabled,
				supplierId,
				type,
				warehouseType,
				saleOrgId,
				organizationId,
				sbuId,
				pageable,
				sign,
                isNotwarehouseType);
	}

	@Override
	@Transactional(readOnly = true)
	public Warehouse findByErpWarehouseCode(String erp_warehouse_code,
			Long companyInfoId) {
		Warehouse warehouse = null;
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("erp_warehouse_code", erp_warehouse_code));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<Warehouse> warehouses = this.findList(1, filters, null);
		if (warehouses != null && warehouses.size() > 0) {
			warehouse = warehouses.get(0);
		}
		return warehouse;
	}

	/**
	 * 保存仓库组织关联关系
	 */
	public void saveWarehouseOfSaleOrg(Long[] saleOrgId,Boolean[] isChecks,
			Warehouse warehouse) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("warehouse", warehouse));
		List<WarehouseSaleOrg> warehouseSaleOrgs = warehouseSaleOrgBaseService.findList(null,filters,null);
		
		List<SaleOrg> oldSaleOrgs = new ArrayList<SaleOrg>();
		for (WarehouseSaleOrg warehouseSaleOrg : warehouseSaleOrgs) {
			oldSaleOrgs.add(warehouseSaleOrg.getSaleOrg());
		}
		List<SaleOrg> saleOrgs = new ArrayList<SaleOrg>();
		if (saleOrgId != null && saleOrgId.length > 0) {
			for (Long id : saleOrgId) {
				SaleOrg saleOrg = saleOrgBaseService.find(id);
				saleOrgs.add(saleOrg);
			}
			for (int i = 0; i < saleOrgId.length; i++) {
				SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId[i]);
				Boolean isCheck = false;
				if(!ConvertUtil.isEmpty(isChecks[i])&&isChecks[i]){
					isCheck = isChecks[i];
				}
				if (!oldSaleOrgs.contains(saleOrg)) {
					WarehouseSaleOrg warehouseSaleOrg = new WarehouseSaleOrg();
					warehouseSaleOrg.setWarehouse(warehouse);
					warehouseSaleOrg.setSaleOrg(saleOrg);
					warehouseSaleOrg.setIsCheck(isCheck);
					warehouseSaleOrgBaseService.save(warehouseSaleOrg);
				}else {
					filters.clear();
					filters.add(Filter.eq("warehouse", warehouse));
					filters.add(Filter.eq("saleOrg", saleOrg));
					WarehouseSaleOrg warehouseSaleOrg = warehouseSaleOrgBaseService.find(filters);
					warehouseSaleOrg.setIsCheck(isCheck);
					warehouseSaleOrgBaseService.update(warehouseSaleOrg);
				}
			}
		}
		for (WarehouseSaleOrg warehouseSaleOrg : warehouseSaleOrgs) {
			SaleOrg saleOrg = warehouseSaleOrg.getSaleOrg();
			if (!saleOrgs.contains(saleOrg)) {
				warehouseSaleOrgBaseService.delete(warehouseSaleOrg);
			}
		}

	}

	@Override
	public List<Map<String, Object>> findWarehouseSbu(Warehouse warehouse) {

		return warehouseBaseDao.findWarehouseSbu(warehouse);
	}

	@Override
	public List<WarehouseSmethod> findWarehouseSmethod(Long id) {
		List<WarehouseSmethod> a = null;
		Warehouse warehouse = this.find(id);
		if(warehouse!=null){
			a = warehouse.getWarehouseSmethodList();
		}
		return a;
	}

	@Override
	public List<Map<String, Object>> findWarehouseStoreMember(Warehouse warehouse) {
		
		return warehouseBaseDao.findWarehouseStoreMember(warehouse);
	}
	
	
	@Override
	public List<Map<String, Object>> findwarehouseLocationList(Warehouse warehouse) {
		return warehouseBaseDao.findwarehouseLocationList(warehouse);
	}

	
	
	
	@Override
	public List<Map<String, Object>> findwarehouseLocationListByParam(String code, Long warehouseId, String remarks) {
		return warehouseBaseDao.findwarehouseLocationListByParam(code,warehouseId,remarks);
	}


    @Override
	@Transactional
	public void saveOrUpdateWarehouseLocation(Warehouse warehouse) {
		Warehouse pWarehouse = this.find(warehouse.getId());
		if(ConvertUtil.isEmpty(pWarehouse)){
			ExceptionUtil.throwServiceException("仓库不存在");
		}
		List<WarehouseLocation> warehouseLocationList = new ArrayList<WarehouseLocation>();
		List<String> stringList = new ArrayList<String>();
		if(!warehouse.getWarehouseLocations().isEmpty() && warehouse.getWarehouseLocations().size()>0){
			for (Iterator<WarehouseLocation> iterator = warehouse.getWarehouseLocations().iterator(); iterator.hasNext();) {
				WarehouseLocation warehouseLocation = (WarehouseLocation) iterator.next();
				if (ConvertUtil.isEmpty(warehouseLocation) || ConvertUtil.isEmpty(warehouseLocation.getCode())) {
					iterator.remove();
					continue;
				}
				//仓库
				warehouseLocation.setWarehouse(pWarehouse);
				if(stringList.contains(warehouseLocation.getCode())){
					ExceptionUtil.throwServiceException("库位编码【"+warehouseLocation.getCode()+"】已存在，不能存在有相同的");
				}
				stringList.add(warehouseLocation.getCode());
				warehouseLocationList.add(warehouseLocation);
			}
		}
		if(warehouseLocationList.isEmpty()){
			ExceptionUtil.throwServiceException("请添加库位");
		}
		pWarehouse.getWarehouseLocations().clear();
		pWarehouse.getWarehouseLocations().addAll(warehouseLocationList);
		update(pWarehouse);
	}

    @Override
    public void checkWarehouseState(Warehouse warehouse, Integer warehouseStatus, String statusName, String operationName) {
        if(ConvertUtil.isEmpty(warehouse)){
            ExceptionUtil.throwServiceException("该仓库不存在");
        }
        if(!ConvertUtil.isEmpty(warehouse.getWarehouseStatus()) && warehouse.getWarehouseStatus() != warehouseStatus){
            ExceptionUtil.throwServiceException("只有仓库状态为"+statusName+"的仓库才能"+operationName);
        }
    }


	/**更改仓库状态为已作废*/
    @Override
    @Transactional
    public void cancel(Warehouse warehouse) throws Exception {
        warehouse.setWarehouseStatus(3);
        warehouse.setIsEnabled(false);
        update(warehouse);
    }

	/**更改仓库状态为已失效*/
	@Override
	@Transactional
	public void close(Warehouse warehouse) throws Exception {
		warehouse.setWarehouseStatus(2);
		update(warehouse);
	}

	/**
	 * 创建流程
	 */
	@Override
	@Transactional
	public void createWf(Long id, String modelId, Long objTypeId,Integer type) {
		Warehouse warehouse = find(id);
		StoreMember storeMember = storeMemberService.getCurrent();
		// 创建流程实例
		createWf(warehouse.getSn(), String.valueOf(storeMember.getId()),
                null,null,
				modelId,
				objTypeId,
				id,
				WebUtils.getCurrentCompanyInfoId(),
				true);
        List<String> warehouseSns = new ArrayList<String>();
        warehouseSns.add(warehouse.getSn());
        //type 创建流程类型：1.新建仓库 2.仓库失效 3.仓库修改
        if(type == CommonVariable.NEW_WF_TYPE){
            orderFullLinkService.addFullLink(201, warehouseSns, warehouse.getSn(),
                ConvertUtil.convertI18nMsg("18701", new Object[] { "仓库审核流程" }), null);
            warehouse.setWfType(CommonVariable.NEW_WF_TYPE);
        }else if(type == CommonVariable.INVALID_WF_TYPE){
            orderFullLinkService.addFullLink(201, warehouseSns, warehouse.getSn(),
                    ConvertUtil.convertI18nMsg("18701", new Object[] { "仓库失效流程" }), null);
            warehouse.setWfType(CommonVariable.INVALID_WF_TYPE);
        }
	}

	/**
	 * 流程结束回调
	 */
	@Override
	@Transactional
	public void endBack(ActWf wf) {//原agreeBack()方法
		super.endBack(wf);
		Warehouse warehouse = find(wf.getObjId());
        List<String> warehouseSns = new ArrayList<String>();
        warehouseSns.add(warehouse.getSn());
        if(warehouse.getWfType() == CommonVariable.NEW_WF_TYPE) {
            //校验仓库状态
            this.checkWarehouseState(warehouse, 0, "已保存", "通过");
            //已审核-新增
            warehouse.setWarehouseStatus(1);
            warehouse.setIsEnabled(true);
            orderFullLinkService.addFullLink(201, warehouseSns, warehouse.getSn(), "仓库新建审核流程完成", null);
        }else if(warehouse.getWfType() == CommonVariable.INVALID_WF_TYPE) {
            //校验仓库状态
            this.checkWarehouseState(warehouse, 1, "已生效", "通过");
            //已审核-失效
            warehouse.setWarehouseStatus(2);
            warehouse.setIsEnabled(false);
            orderFullLinkService.addFullLink(201, warehouseSns, warehouse.getSn(),  "仓库失效审核流程完成" , null);
            //warehouse.setCheckDate(new Date());
        }

		update(warehouse);
	}


	/**
	 * 中断流程回调
	 */
	@Override
	@Transactional
	public void interruptBack(ActWf wf) {//原interruptBack()方法
		Warehouse warehouse = find(wf.getObjId());
        List<String> warehouseSns = new ArrayList<String>();
        warehouseSns.add(warehouse.getSn());
        if(warehouse.getWfType() == CommonVariable.NEW_WF_TYPE) {
            this.checkWarehouseState(warehouse, 0, "已保存", "中断");
            warehouse.setWarehouseStatus(0);
            orderFullLinkService.addFullLink(201, warehouseSns, warehouse.getSn(), "中断仓库新建审核流程" , null);
        }else if(warehouse.getWfType() == CommonVariable.INVALID_WF_TYPE) {
            this.checkWarehouseState(warehouse, 1, "已生效", "中断");
            warehouse.setWarehouseStatus(1);
            warehouse.setWfState(2);
            orderFullLinkService.addFullLink(201, warehouseSns, warehouse.getSn(),  "中断仓库失效审核流程" , null);
        }

		update(warehouse);
	}

	/**
	 * 驳回流程回调
	 */
	@Override
	@Transactional
	public void rejectBack(ActWf wf) {
		Warehouse warehouse = find(wf.getObjId());
		//校验仓库状态
        if(warehouse.getWarehouseStatus()==0) {
            this.checkWarehouseState(warehouse, 0, "已保存", "驳回");
            warehouse.setWfState(3);
        }
        if(warehouse.getWarehouseStatus()==1) {
            this.checkWarehouseState(warehouse, 1, "已生效", "驳回");
            warehouse.setWfState(3);
        }
		update(warehouse);
	}

	/**
	 * 通过流程节点回调
	 */
	@Override
	@Transactional
	public void agreeBack(ActWf wf) {
		Warehouse warehouse = find(wf.getObjId());
		if(warehouse.getWfState() == 3){
			//校验仓库状态
            if(warehouse.getWarehouseStatus()==0) {
                this.checkWarehouseState(warehouse, 0, "已保存", "通过");
                warehouse.setWfState(1);
            }else if(warehouse.getWarehouseStatus()==1){
                this.checkWarehouseState(warehouse, 1, "已生效", "通过");
                warehouse.setWfState(1);
            }
			update(warehouse);
		}
	}

	@Override
	public void setServletContext(ServletContext servletContext) {

	}
}
