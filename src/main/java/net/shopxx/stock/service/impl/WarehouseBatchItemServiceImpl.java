package net.shopxx.stock.service.impl;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.stock.dao.WarehouseBatchItemDao;
import net.shopxx.stock.entity.WarehouseBatchItem;
import net.shopxx.stock.service.WarehouseBatchItemService;
/**
 * 批次明细
 * <AUTHOR>
 *
 */
@Service("warehouseBatchItemServiceImpl")
public class WarehouseBatchItemServiceImpl extends BaseServiceImpl<WarehouseBatchItem> 
		implements WarehouseBatchItemService{
	
	@Resource(name = "warehouseBatchItemDao")
	private WarehouseBatchItemDao warehouseBatchItemDao;

	@Override
	public Page<Map<String, Object>> findPage(Long warehouseId, Long productId, Long organizationId,
			Long productLevelId, Long[] colorNumbersIds, Long[] moistureContentsIds, Long[] warehouseBatchIds,
			Long[] newOldLogosIds, String startTime, String endTime, Pageable pageable,String batchEncoding,
			String billType,String documentNo,Boolean flag,Long productionPlantId,Long warehouseLocationId,String memo) {
		
		return warehouseBatchItemDao.findPage(warehouseId, productId, organizationId, productLevelId, colorNumbersIds,
				moistureContentsIds, warehouseBatchIds, newOldLogosIds, startTime, endTime, pageable,batchEncoding,billType,
				documentNo,flag,productionPlantId,warehouseLocationId,memo);
	}

	@Override
	public List<Map<String, Object>> findVWarehouseBatchItemList(Long amShippingId,
			Long warehouseId ,Long productId,Long organizationId,Long productLevelId,
			Long colorNumbersId,Long moistureContentsId,Long[] warehouseBatchIds,
			Long newOldLogosId,Long billTypeId,Boolean flag,Long productionPlantId,Long warehouseLocationId) {
		
		return warehouseBatchItemDao.findVWarehouseBatchItemList(amShippingId, warehouseId, 
				productId, organizationId, productLevelId, colorNumbersId, moistureContentsId, 
				warehouseBatchIds, newOldLogosId, billTypeId,flag,productionPlantId,warehouseLocationId);
	}
	
	@Override
	public List<Map<String, Object>> findVWarehouseBatchItemListBySourceNoId(Long sourceNoId,
			Long billTypeId,Long[] ids) {
		
		return warehouseBatchItemDao.findVWarehouseBatchItemListBySourceNoId(sourceNoId, billTypeId,ids);
	}


}
