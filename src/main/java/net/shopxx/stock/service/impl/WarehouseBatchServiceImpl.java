package net.shopxx.stock.service.impl;
import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.annotation.Resource;

import net.shopxx.stock.service.WarehouseStockService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.aftersales.b2b.service.B2bReturnsItemService;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.order.service.AmShippingItemService;
import net.shopxx.order.service.AmShippingService;
import net.shopxx.order.service.MoveLibraryItemService;
import net.shopxx.order.service.MoveLibraryService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.product.entity.ProductPrice;
import net.shopxx.product.entity.ProductPriceHead;
import net.shopxx.stock.dao.WarehouseBatchDao;
import net.shopxx.stock.entity.WarehouseBatch;
import net.shopxx.stock.service.WarehouseBatchService;
import net.shopxx.stock.service.WarehouseBillBatchService;
@Service("warehouseBatchServiceImpl")
public class WarehouseBatchServiceImpl extends BaseServiceImpl<WarehouseBatch> implements WarehouseBatchService{
	
	@Resource(name = "warehouseBillBatchServiceImpl")
	private WarehouseBillBatchService warehouseBillBatchService;
	@Resource(name = "warehouseBatchDao")
	private WarehouseBatchDao warehouseBatchDao;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "amShippingServiceImpl")
	private AmShippingService amShippingService;
	@Resource(name = "amShippingItemServiceImpl")
	private AmShippingItemService amShippingItemService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "moveLibraryServiceImpl")
	private MoveLibraryService moveLibraryService;
	@Resource(name = "moveLibraryItemServiceImpl")
	private MoveLibraryItemService moveLibraryItemService;
	@Resource(name = "b2bReturnsServiceImpl")
	private B2bReturnsService b2bReturnsService;
	@Resource(name = "b2bReturnsItemServiceImpl")
	private B2bReturnsItemService b2bReturnsItemService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	
	@Override
	public List<Map<String, Object>> findWarehouseBatchs(String batchsIds,
			Long organizationId,Long productionPlantId) {
		
		List<Filter> filters = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		//获取批次信息并赋值
		List<Map<String, Object>> warehouseBatchs = new ArrayList<Map<String,Object>>();
		if (!ConvertUtil.isEmpty(batchsIds)) {
				String[]  bacthList = batchsIds.split(";");
				for (int i = 0; i < bacthList.length; i++) {
					String bacthId = bacthList[i];
					if (!ConvertUtil.isEmpty(bacthId)) {
						filters.clear();
						filters.add(Filter.eq("companyInfoId", companyInfoId));
						filters.add(Filter.eq("id", bacthId));
						filters.add(Filter.eq("organization", organizationId));
						if(!ConvertUtil.isEmpty(productionPlantId)){
							filters.add(Filter.eq("productionPlant", productionPlantId));
						}
						WarehouseBatch batch = find(filters);
						if (!ConvertUtil.isEmpty(batch)) {
							Map<String, Object> map = new HashMap<String, Object>();
							map.put("id", batch.getId());
							map.put("batch_encoding", batch.getBatchEncoding());
							map.put("sn", batch.getSn());
							if(!ConvertUtil.isEmpty(batch.getOrganization())){
								map.put("organization_name", batch.getOrganization().getName());
							}
							map.put("factory_name", batch.getProductionPlant().getValue());
							map.put("production_no", batch.getProductionNo());
							map.put("production_date", batch.getProductionDate());
							map.put("completion_date", batch.getCompletionDate());
							map.put("batchStatus", batch.getBatchStatus());
							map.put("memo", batch.getMemo());
							warehouseBatchs.add(map);
						}
					}
				}
		}		
		return warehouseBatchs;
	}

	
	@Override
	public Page<Map<String, Object>> findPage(String batchEncoding,
			Long[] productionPlantId, String productionNo,
			Boolean batchStatus,Long[] categoryOfGoodsId,
			String startProductionDate,String endProductionDate,
			String startCompletionDate,String endCompletionDate,
			String startEffectiveDate,String endEffectiveDate,
			String storeName,Long[] organizationId,String batchIds,String memo,Pageable pageable) {
		//处理批次参数
		Long[] longBatchIds = null;
		if (batchIds!=null && !batchIds.isEmpty() && !"null".equals(batchIds)) {
			String[] stringBatchIds = batchIds.split(";");
			if (stringBatchIds.length>0) {
				longBatchIds = new Long[stringBatchIds.length];
				for (int i = 0; i < stringBatchIds.length; i++) {
					longBatchIds[i] = Long.valueOf(stringBatchIds[i]);
				}
			}
			
		}
		
		return warehouseBatchDao.findPage(batchEncoding, productionPlantId, 
				productionNo, batchStatus, categoryOfGoodsId, startProductionDate, 
				endProductionDate, startCompletionDate, endCompletionDate, 
				startEffectiveDate, endEffectiveDate, storeName, organizationId,longBatchIds,memo, pageable);
	}
	
	
	private boolean isValidDate(String str) {
		boolean convertSuccess = true;
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			format.setLenient(false);
			format.parse(str);
		}
		catch (ParseException e) {
			convertSuccess = false;
		}
		return convertSuccess;
	}

	@Override
	@Transactional
	public String warehouseBatchAddImport(MultipartFile multipartFile) throws Exception {
		String msg = "批次导入";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir") + "/upload_" + UUID.randomUUID() + ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 5001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 500).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入5000条");
			}else {
				rows = 5001;
			}
		}
		//生产工厂
		String productionPlantStr;
		//生产单号
		String productionNo;
		//货物类别
		String categoryOfGoodsStr;
		//制单日期
		String productionDateStr;
		//生产完成日期
		String completionDateStr;
		//是否有效
		Boolean isBatchStatus = false;
		//有效日期
		String effectiveStartDateStr;
		//经营组织
		String organizationStr;
		//备注
		String memo;
		//获取登录人
		StoreMember storeMember = storeMemberService.getCurrent();
		//时间格式
		SimpleDateFormat formatter = new SimpleDateFormat("yyMMdd");
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		List<Filter> filters = new ArrayList<Filter>();
		for (int i = 1; i < rows; i++) {
			//生产工厂
			cell = sheet.getCell(0, i);
			productionPlantStr = cell.getContents().trim();
			if (StringUtils.isBlank(productionPlantStr)) {
				msg = "第" + i + "行." + "生产工厂为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("value", productionPlantStr.trim()));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("code", "productionPlant"));
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.isNotNull("parent"));
			SystemDict productionPlant = systemDictBaseService.find(filters);
			if(ConvertUtil.isEmpty(productionPlant)){
				msg = "第" + i + "行." + "根据生产工厂名称为[" + productionPlantStr + "]找不到相应生产工厂";
				ExceptionUtil.throwServiceException(msg);
			}
			//生产单号
			cell = sheet.getCell(1, i);
			productionNo = cell.getContents().trim();
			if (StringUtils.isBlank(productionNo)) {
				msg = "第" + i + "行." + "生产单号为空";
				ExceptionUtil.throwServiceException(msg);
			}
			//货物类别
			cell = sheet.getCell(2, i);
			categoryOfGoodsStr = cell.getContents().trim();
			if (StringUtils.isBlank(categoryOfGoodsStr)) {
				msg = "第" + i + "行." + "货物类别为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("value", categoryOfGoodsStr.trim()));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("code", "categoryOfGoods"));
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.isNotNull("parent"));
			SystemDict categoryOfGoods = systemDictBaseService.find(filters);
			if(ConvertUtil.isEmpty(categoryOfGoods)){
				msg = "第" + i + "行." + "根据货物类别名称为[" + categoryOfGoodsStr + "]找不到相应货物类别";
				ExceptionUtil.throwServiceException(msg);
			}
			//制单日期
			cell = sheet.getCell(3, i);
			productionDateStr = cell.getContents().trim();
			if (StringUtils.isBlank(productionDateStr)) {
				msg = "第" + i + "行." + "制单日期为空";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!isValidDate(productionDateStr)) {
				msg = "第" + i + "行." + "制单日期格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			Date productionDate = DateUtil.convert(productionDateStr + " 00:00:00");
			//生产完成日期
			cell = sheet.getCell(4, i);
			completionDateStr = cell.getContents().trim();
			if (StringUtils.isBlank(completionDateStr)) {
				msg = "第" + i + "行." + "生产完成日期为空";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!isValidDate(completionDateStr)) {
				msg = "第" + i + "行." + "生产完成日期格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			Date completionDate = DateUtil.convert(completionDateStr + " 00:00:00");
			//是否有效
			cell = sheet.getCell(5, i);
			String isBatchStatusStr = cell.getContents().trim();
			if (!StringUtils.isEmpty(isBatchStatusStr)) {
				if ("是".equals(isBatchStatusStr)) {
					isBatchStatus = true;
				}
			}
			//有效日期
			cell = sheet.getCell(6, i);
			effectiveStartDateStr = cell.getContents().trim();
			if (StringUtils.isBlank(effectiveStartDateStr)) {
				msg = "第" + i + "行." + "有效日期为空";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!isValidDate(effectiveStartDateStr)) {
				msg = "第" + i + "行." + "有效日期格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			Date effectiveStartDate = DateUtil.convert(effectiveStartDateStr + " 00:00:00");
			//经营组织
			cell = sheet.getCell(7, i);
			organizationStr = cell.getContents().trim();
			if (StringUtils.isBlank(organizationStr)) {
				msg = "第" + i + "行." + "经营组织为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			filters.add(Filter.eq("name", organizationStr.trim()));
			Organization organization = organizationService.find(filters);
			if(ConvertUtil.isEmpty(organization)){
				msg = "第" + i + "行." + "根据经营组织名称为[" + organizationStr + "]找不到相应经营组织";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("organization", organization.getId()));
		    StoreMemberOrganization storeMemberOrganization = storeMemberOrganizationService.find(filters);
		    if(ConvertUtil.isEmpty(storeMemberOrganization)){
		    	msg = "第" + i + "行." + "当前用户没有名称为[" + organizationStr + "]经营组织的权限";
				ExceptionUtil.throwServiceException(msg);
		    }
			//备注
			cell = sheet.getCell(8, i);
			memo = cell.getContents().trim();
			//作业单号
			String code = productionPlant.getLowerCode()+formatter.format(productionDate)+productionNo;
			/*Long count = count(Filter.eq("sn", code));
			if (count.longValue() > 0) {
				msg = "第" + i + "行." + "根据生产单号、制单日期生成的作业单号已存在，请对生产单号或者制单日期调整再操作";
				ExceptionUtil.throwServiceException(msg);
			}*/
			createWarehouseBatch(productionPlant, productionNo, categoryOfGoods, productionDate,
					completionDate, isBatchStatus, effectiveStartDate, organization, memo, 
					storeMember, code);
			success++;
		}
		int result = rows - 1;
		msg = "msg:" + "总数" + result + "行,成功导入" + success + " 行. ";
		return msg;
	}


	private void createWarehouseBatch(SystemDict productionPlant,String productionNo,
			SystemDict categoryOfGoods,Date productionDate,Date completionDate,
			Boolean isBatchStatus,Date effectiveStartDate,Organization organization,
			String memo,StoreMember storeMember,String code) {
		//创建批次对象
		WarehouseBatch warehouseBatch = new WarehouseBatch();
		//作业单号
		warehouseBatch.setSn(code);
		//生产工厂
		warehouseBatch.setProductionPlant(productionPlant);
		//生产单号
		warehouseBatch.setProductionNo(productionNo);
		//货物类别
		warehouseBatch.setCategoryOfGoods(categoryOfGoods);
		//制单日期
		warehouseBatch.setProductionDate(productionDate);
		//生产完成日期
		warehouseBatch.setCompletionDate(completionDate);
		//状态
		warehouseBatch.setBatchStatus(isBatchStatus);
		//有效日期
		warehouseBatch.setEffectiveDate(effectiveStartDate);
		//经营组织
		warehouseBatch.setOrganization(organization);
		//备注
		warehouseBatch.setMemo(memo);
		//修改次数
		warehouseBatch.setModifications(0);
		//操作人
		warehouseBatch.setStoreMember(storeMember);
		save(warehouseBatch);
		if(!ConvertUtil.isEmpty(warehouseBatch) && !ConvertUtil.isEmpty(warehouseBatch.getId())){
			//批次编码
			warehouseBatch.setBatchEncoding(warehouseBatch.getSn()+warehouseBatch.getId());
			update(warehouseBatch);
		}
		warehouseBatchDao.getEntityManager().flush();
	}


	@Override
	public List<Map<String, Object>> findWarehouseBatchList(String batchEncoding, Long[] productionPlantId,
			String productionNo, Boolean batchStatus, Long[] categoryOfGoodsId, String startProductionDate,
			String endProductionDate, String startCompletionDate, String endCompletionDate, String startEffectiveDate,
			String endEffectiveDate, String storeName, Long[] organizationId, Long[] ids) {
		
		return warehouseBatchDao.findWarehouseBatchList(batchEncoding, productionPlantId, productionNo, batchStatus, 
				categoryOfGoodsId, startProductionDate, endProductionDate, startCompletionDate, endCompletionDate, 
				startEffectiveDate, endEffectiveDate, storeName, organizationId, ids);
	}


	@Override
	@Transactional
	public void cancel(Long[] ids) {
		for (Long id : ids) {
			WarehouseBatch warehouseBatch = find(id);
			if(!ConvertUtil.isEmpty(warehouseBatch) ){
				if(!ConvertUtil.isEmpty(warehouseBatch.getBatchStatus()) && !warehouseBatch.getBatchStatus()) {
					ExceptionUtil.throwServiceException("批次编码为【" + warehouseBatch.getBatchEncoding() + "】的单据已失效，禁止重复操作");
				}else if(warehouseBatchDao.checkWarehouseStock(id)!=0){//校验该批次库存是否为空
					ExceptionUtil.throwServiceException("批次编码为【" + warehouseBatch.getBatchEncoding() + "】的批次还有库存，禁止失效");
				}
				warehouseBatch.setBatchStatus(false);
				update(warehouseBatch);
			}
		}
	}
}
