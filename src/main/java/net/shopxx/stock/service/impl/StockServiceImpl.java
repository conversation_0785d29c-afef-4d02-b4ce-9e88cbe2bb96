package net.shopxx.stock.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.stock.dao.StockDao;
import net.shopxx.stock.dao.StockLogDao;
import net.shopxx.stock.entity.Stock;
import net.shopxx.stock.entity.StockTask;
import net.shopxx.stock.entity.StockTaskBuilder;
import net.shopxx.stock.service.StockService;

/**
 * Service - 库存
 */
@Service("stockServiceImpl")
public class StockServiceImpl extends BaseServiceImpl<Stock> implements StockService {

	@Resource(name = "stockDao")
	private StockDao stockDao;
	@Resource(name = "stockLogDao")
	private StockLogDao stockLogDao;
	@Resource(name = "lockDataDao")
	private LockDataDao lockDataDao;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;

	@Transactional(readOnly = true)
	public Map<String, Object> find(long warehouseId, long productId) {
		return stockDao.find(warehouseId, productId);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findStockList(Long warehouseId, Long[] productIds) {
		return stockDao.findStockList(warehouseId, productIds);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Long[] productIds, Long[] warehouseIds, Integer isShowZero,
			String vonderCode, Pageable pageable) {
		return stockDao.findPage(productIds, warehouseIds, isShowZero, vonderCode, pageable);
	}

//	@Override
//	@Transactional
//	public void handleStock(StockTaskBuilder taskBuilder) {
//
//		List<StockTask> stockTasks = taskBuilder.getStockTaskList();
//		if (stockTasks.isEmpty()) return;
//
//		//锁库存
//		lockDataDao.lockStock(stockTasks);
//
//		//具体处理库存操作
//		StringBuilder sb = new StringBuilder();
//		BigDecimal sActualStock = BigDecimal.ZERO;
//		BigDecimal sUseableStock = BigDecimal.ZERO;
//		BigDecimal sOnWayStock = BigDecimal.ZERO;
//		BigDecimal sLockStock = BigDecimal.ZERO;
//		for (StockTask stockTask : stockTasks) {
//
//			Long productId = stockTask.getProductId();
//			Long warehouseId = stockTask.getWarehouseId();
//			BigDecimal stockQuantity = stockTask.getStock();
//			Integer type = stockTask.getType();
//			String memo = stockTask.getMemo();
//
//			Map<String, Object> stock = stockDao.find(warehouseId, productId);
//			if (stock != null) {
//				sActualStock = new BigDecimal(stock.get("actual_stock")
//						.toString());
//				sUseableStock = new BigDecimal(stock.get("useable_stock")
//						.toString());
//				sOnWayStock = new BigDecimal(stock.get("on_way_stock")
//						.toString());
//				sLockStock = new BigDecimal(stock.get("lock_stock").toString());
//			}
//			else {
//				sActualStock = BigDecimal.ZERO;
//				sUseableStock = BigDecimal.ZERO;
//				sOnWayStock = BigDecimal.ZERO;
//				sLockStock = BigDecimal.ZERO;
//			}
//
//			int count = 0;
//			if (type == 1) {//增加库存
//				if (stock == null) {
//					//库存不存在，新增
//					stockDao.insert(warehouseId,
//							productId,
//							stockQuantity,
//							stockQuantity,
//							BigDecimal.ZERO,
//							BigDecimal.ZERO);
//				}
//				else {
//					stockDao.update(warehouseId,
//							productId,
//							stockQuantity,
//							stockQuantity,
//							BigDecimal.ZERO,
//							BigDecimal.ZERO,
//							BigDecimal.ZERO,
//							0);
//				}
//				//新增库存日志
//				stockLogDao.insert(warehouseId,
//						productId,
//						stockQuantity,
//						stockQuantity,
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						sActualStock.add(stockQuantity),
//						sUseableStock.add(stockQuantity),
//						sLockStock,
//						sOnWayStock,
//						memo);
//			}
//			else if (type == 2) {//锁库
//				count = stockDao.update(warehouseId,
//						productId,
//						BigDecimal.ZERO,
//						stockQuantity.negate(), // -stockQuantity
//						stockQuantity,
//						BigDecimal.ZERO,
//						stockQuantity,
//						2);
//				if (count == 0) {
//					//库存不足
//					sb.append(ConvertUtil.convertI18nMsg("124009") + "\r\n");
//					continue;
//				}
//				else {
//					sb.append("\r\n");
//				}
//				//新增库存日志
//				stockLogDao.insert(warehouseId,
//						productId,
//						BigDecimal.ZERO,
//						stockQuantity.negate(), // -stockQuantity
//						stockQuantity,
//						BigDecimal.ZERO,
//						sActualStock,
//						sUseableStock.subtract(stockQuantity),
//						sLockStock.add(stockQuantity),
//						sOnWayStock,
//						memo);
//			}
//			else if (type == 3) {//取消锁库
//				count = stockDao.update(warehouseId,
//						productId,
//						BigDecimal.ZERO,
//						stockQuantity,
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						stockQuantity,
//						3);
//				if (count == 0) {
//					//库存不足
//					sb.append(ConvertUtil.convertI18nMsg("124009") + "\r\n");
//					continue;
//				}
//				else {
//					sb.append("\r\n");
//				}
//				//新增库存日志
//				stockLogDao.insert(warehouseId,
//						productId,
//						BigDecimal.ZERO,
//						stockQuantity,
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						sActualStock,
//						sUseableStock.add(stockQuantity),
//						sLockStock.subtract(stockQuantity),
//						sOnWayStock,
//						memo);
//			}
//			else if (type == 4) {//扣减库存
//				count = stockDao.update(warehouseId,
//						productId,
//						stockQuantity.negate(), // -stockQuantity
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						stockQuantity,
//						2);
//				if (count == 0) {
//					//库存不足
//					sb.append(ConvertUtil.convertI18nMsg("124009") + "\r\n");
//					continue;
//				}
//				else {
//					sb.append("\r\n");
//				}
//				//新增库存日志
//				stockLogDao.insert(warehouseId,
//						productId,
//						stockQuantity.negate(), // -stockQuantity
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						sActualStock.subtract(stockQuantity),
//						sUseableStock.subtract(stockQuantity),
//						sLockStock,
//						sOnWayStock,
//						memo);
//			}
//			else if (type == 5) {//扣减库存取消锁库
//				count = stockDao.update(warehouseId,
//						productId,
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						stockQuantity,
//						1);
//				if (count == 0) {
//					//库存不足
//					sb.append(ConvertUtil.convertI18nMsg("124009") + "\r\n");
//					continue;
//				}
//				else {
//					sb.append("\r\n");
//				}
//				//新增库存日志
//				stockLogDao.insert(warehouseId,
//						productId,
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						sActualStock.subtract(stockQuantity),
//						sUseableStock,
//						sLockStock.subtract(stockQuantity),
//						sOnWayStock,
//						memo);
//			}
//			else if (type == 6) {//扣减库存增加在途
//				count = stockDao.update(warehouseId,
//						productId,
//						stockQuantity.negate(), // -stockQuantity
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						stockQuantity,
//						stockQuantity,
//						2);
//				if (count == 0) {
//					//库存不足
//					sb.append(ConvertUtil.convertI18nMsg("124009") + "\r\n");
//					continue;
//				}
//				else {
//					sb.append("\r\n");
//				}
//				//新增库存日志
//				stockLogDao.insert(warehouseId,
//						productId,
//						stockQuantity.negate(), // -stockQuantity
//						stockQuantity.negate(), // -stockQuantity
//						BigDecimal.ZERO,
//						stockQuantity,
//						sActualStock.subtract(stockQuantity),
//						sUseableStock.subtract(stockQuantity),
//						sLockStock,
//						sOnWayStock.subtract(stockQuantity),
//						memo);
//			}
//			else if (type == 7) {//清减在途
//				count = stockDao.update(warehouseId,
//						productId,
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						stockQuantity.negate(), // -stockQuantity
//						stockQuantity,
//						4);
//				if (count == 0) {
//					//库存不足
//					sb.append(ConvertUtil.convertI18nMsg("124009") + "\r\n");
//					continue;
//				}
//				else {
//					sb.append("\r\n");
//				}
//				//新增库存日志
//				stockLogDao.insert(warehouseId,
//						productId,
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						BigDecimal.ZERO,
//						stockQuantity.negate(), // -stockQuantity
//						sActualStock,
//						sUseableStock,
//						sLockStock,
//						sOnWayStock.subtract(stockQuantity),
//						memo);
//			}
//		}
//		String msg = sb.toString().replaceAll("\r\n", "");
//		if (!ConvertUtil.isEmpty(msg)) {
//			ExceptionUtil.throwServiceException(sb.toString());
//		}
//	}

	@Override
	@Transactional
	public void handleStock(StockTaskBuilder taskBuilder) {

		List<StockTask> stockTasks = taskBuilder.getStockTaskList();
		if (stockTasks.isEmpty())
			return;

		// 锁库存
		lockDataDao.lockStock(stockTasks);

		// 具体处理库存操作
		StringBuilder sb = new StringBuilder();
		BigDecimal sActualStock = BigDecimal.ZERO;
		for (StockTask stockTask : stockTasks) {

			Long productId = stockTask.getProductId();
			Long warehouseId = stockTask.getWarehouseId();
			BigDecimal stockQuantity = stockTask.getStock();
			Integer type = stockTask.getType();
			String memo = stockTask.getMemo();

			Map<String, Object> stock = stockDao.find(warehouseId, productId);
			if (stock != null) {
				sActualStock = new BigDecimal(stock.get("actual_stock").toString());
			} else {
				sActualStock = BigDecimal.ZERO;
			}

			int count = 0;
			if (type == 1) {// 增加库存
				if (stock == null) {
					// 库存不存在，新增
					stockDao.insert(warehouseId, productId, stockQuantity);
				} else {
					stockDao.update(warehouseId, productId, stockQuantity, BigDecimal.ZERO);
				}
				// 新增库存日志
				stockLogDao.insert(warehouseId, productId, stockQuantity, sActualStock.add(stockQuantity), memo);
			} else if (type == 2) {// 扣减库存
				count = stockDao.update(warehouseId, productId, stockQuantity.negate(), stockQuantity);
				if (count == 0) {
					// 库存不足
					sb.append(ConvertUtil.convertI18nMsg("124009") + "\r\n");
					continue;
				} else {
					sb.append("\r\n");
				}
				// 新增库存日志
				stockLogDao.insert(warehouseId, productId, stockQuantity.negate(), // -stockQuantity
						sActualStock.subtract(stockQuantity), memo);
			}
		}
		String msg = sb.toString().replaceAll("\r\n", "");
		if (!ConvertUtil.isEmpty(msg)) {
			ExceptionUtil.throwServiceException(sb.toString());
		}
	}

	@Override
	@Transactional(readOnly = true)
	public Integer count(Long[] warehouseIds, Long[] productIds, Integer isShowZero, String vonderCode,
			Pageable pageable, ModelMap model, Integer page, Integer size) {
		return stockDao.count(warehouseIds, productIds, isShowZero, vonderCode, pageable, model, page, size);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findItemList(Long[] warehouseIds, Long[] productIds, Integer isShowZero,
			Long[] ids, String vonderCode, Integer page, Integer size) {
		return stockDao.findItemList(warehouseIds, productIds, isShowZero, ids, vonderCode, page, size);
	}

	@Override
	@Transactional(readOnly = true)
	public boolean isStockEnough(Long productId, Long warehouseId) {

		Map<String, Object> map = stockDao.findLockStock(productId, warehouseId);
		BigDecimal actualStock = new BigDecimal(map.get("actual_stock").toString());
		BigDecimal lockStock = new BigDecimal(map.get("lock_stock").toString());

		return actualStock.compareTo(lockStock) >= 0;
	}

	@Override
	@Transactional(readOnly = true)
	public Map<String, Object> findLockStock(Long productId, Long warehouseId) {

		Map<String, Object> map = stockDao.findLockStock(productId, warehouseId);
		return map;
	}

	@Override
	@Transactional(readOnly = true)
	public Map<String, Object> findLockStock(Long productId) {

		Map<String, Object> map = stockDao.findLockStock(productId);
		return map;
	}

	@Override
	@Transactional(readOnly = true)
	public BigDecimal findActualStock(Long productId) {
		return stockDao.findActualStock(productId);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findInvoicingPage(Long productId, Long warehouseId, String startTime,
			String endTime, String vonderCode, String mod, Pageable pageable) {
		return stockDao.findInvoicingPage(productId, warehouseId, startTime, endTime, vonderCode, mod, pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Integer count(Long productId, Long warehouseId, String startTime, String endTime, String vonderCode,
			String mod, Pageable pageable) {
		return stockDao.count(productId, warehouseId, startTime, endTime, vonderCode, mod, pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findInvoicingList(Long productId, Long warehouseId, String startTime,
			String endTime, String vonderCode, String mod, Long[] ids, Integer page, Integer size) {
		return stockDao.findInvoicingList(productId, warehouseId, startTime, endTime, vonderCode, mod, ids, page, size);
	}

	@Override
	public Page<Map<String, Object>> findPage(String warehouseName, String productCode, Integer isShowZero,
			Pageable pageable) {
		return stockDao.findPage(warehouseName, productCode, isShowZero, pageable);
	}

	@Override
	public Page<Map<String, Object>> findPage(Long[] warehouseIds, Long productId, Integer isShowZero,
			Pageable pageable) {
		return stockDao.findPage(warehouseIds, productId, isShowZero, pageable);
	}

	@Override
	public Page<Map<String, Object>> findMain(Long[] warehouseIds, Long mainProductId, Integer isShowZero,
			Pageable pageable) {
		return stockDao.findMain(warehouseIds, mainProductId, isShowZero, pageable);
	}

	@Override
	public Page<Map<String, Object>> findSuit(Long[] warehouseIds, Long mainProductId, Integer isShowZero,
			Pageable pageable) {
		return stockDao.findSuit(warehouseIds, mainProductId, isShowZero, pageable);
	}

	@Override
	public Page<Map<String, Object>> findPage(Pageable pageable, Long[] warehouseId, Long[] organizationId,
			Long[] productId, Long[] productLevelId, Long[] colorNumbersId, Long[] moistureContentId,
			Long[] newOldLogosIds, Long[] warehouseBatchId) {

		return stockDao.findPage(pageable, warehouseId, organizationId, productId, productLevelId, colorNumbersId,
				moistureContentId, newOldLogosIds, warehouseBatchId);
	}

	@Override
	public List<Map<String, Object>> findViewStock(Long warehouseId, Long organizationId, Long productId,
			Long productGrade, String vonderCode, String colourNumber, String moistureContent, Long[] batchIds,
			String newOldLogos, Long warehouseLocationId, String batchIdsStr) {

		return stockDao.findViewStock(warehouseId, organizationId, productId, productGrade, vonderCode, colourNumber,
				moistureContent, batchIds, newOldLogos, warehouseLocationId, batchIdsStr);
	}

	@Override
	public BigDecimal getStockQuantity(Long warehouseId, Long organizationId, Long productId, Long productGrade,
			String colourNumber, String moistureContent, Long[] batchIds, String newOldLogos, Long warehouseLocationId,
			String batchIdsStr, Integer columnType) {

		return stockDao.getStockQuantity(warehouseId, organizationId, productId, productGrade, colourNumber,
				moistureContent, batchIds, newOldLogos, warehouseLocationId, batchIdsStr, columnType);
	}

	@Override
	public Integer findViewStockCount(Long Id, Integer sign) {

		return stockDao.findViewStockCount(Id, sign);
	}

}
