package net.shopxx.stock.service.impl;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.stock.dao.WarehouseBillBatchDao;
import net.shopxx.stock.entity.WarehouseBillBatch;
import net.shopxx.stock.service.WarehouseBillBatchService;
@Service("warehouseBillBatchServiceImpl")
public class WarehouseBillBatchServiceImpl extends BaseServiceImpl<WarehouseBillBatch> implements WarehouseBillBatchService{
	
	@Resource(name = "warehouseBillBatchDao")
	private WarehouseBillBatchDao warehouseBillBatchDao;

}
