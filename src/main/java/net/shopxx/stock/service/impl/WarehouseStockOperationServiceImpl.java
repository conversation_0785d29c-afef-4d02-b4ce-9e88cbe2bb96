package net.shopxx.stock.service.impl;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.AmShippingItem;
import net.shopxx.stock.dao.WarehouseStockOperationDao;
import net.shopxx.stock.entity.WarehouseBatchItem;
import net.shopxx.stock.entity.WarehouseStock;
import net.shopxx.stock.entity.WarehouseStockOperation;
import net.shopxx.stock.service.WarehouseStockOperationService;
import net.shopxx.stock.service.WarehouseStockService;
@Service("warehouseStockOperationServiceImpl")
public class WarehouseStockOperationServiceImpl extends BaseServiceImpl<WarehouseStockOperation> implements  WarehouseStockOperationService{
	
	@Resource(name = "warehouseStockOperationDao")
	private WarehouseStockOperationDao warehouseStockOperationDao;
	@Resource(name = "warehouseStockServiceImpl")
	private WarehouseStockService warehouseStockService;
	
	/**
	 * 参数不能为空
	 * @param warehouseStockOperation
	 */
	private void checkParamIsNotNull(WarehouseStockOperation warehouseStockOperation){
		/**现有量（箱）*/
		if(ConvertUtil.isEmpty(warehouseStockOperation.getOnhandBoxQuantity())){
			warehouseStockOperation.setOnhandBoxQuantity(BigDecimal.ZERO);
		}
		/**现有量（支）*/
		if(ConvertUtil.isEmpty(warehouseStockOperation.getOnhandBranchQuantity())){
			warehouseStockOperation.setOnhandBranchQuantity(BigDecimal.ZERO);
		}
		/**现有量平方数*/
		if(ConvertUtil.isEmpty(warehouseStockOperation.getOnhandQuantity())){
			warehouseStockOperation.setOnhandQuantity(BigDecimal.ZERO);
		}
		/**可处理量（箱）*/
		if(ConvertUtil.isEmpty(warehouseStockOperation.getAvailableBoxQuantity())){
			warehouseStockOperation.setAvailableBoxQuantity(BigDecimal.ZERO);
		}
		/**可处理量（支）*/
		if(ConvertUtil.isEmpty(warehouseStockOperation.getAvailableBranchQuantity())){
			warehouseStockOperation.setAvailableBranchQuantity(BigDecimal.ZERO);
		}
		/**可处理量平方数*/
		if(ConvertUtil.isEmpty(warehouseStockOperation.getAvailableQuantity())){
			warehouseStockOperation.setAvailableQuantity(BigDecimal.ZERO);
		}
	}
	
	
	
	@Override
	@Transactional
	public void deleteWarehouseStockOperation(Long amShippingId,SystemDict actionType) {
		try {
			List<Filter> filters = new ArrayList<Filter>();
			filters.clear();
			filters.add(Filter.eq("amShipping", amShippingId));
			List<WarehouseStockOperation> warehouseStockOperationList = findList(null,filters,null);
			if(!warehouseStockOperationList.isEmpty() && warehouseStockOperationList.size()>0){
				for (WarehouseStockOperation warehouseStockOperation : warehouseStockOperationList) {
					//判断是否为空，初始化默认值
					this.checkParamIsNotNull(warehouseStockOperation);
					//仓库库存
					WarehouseStock warehouseStock = warehouseStockOperation.getWarehouseStock();
					if(!ConvertUtil.isEmpty(warehouseStock)){
						//更新库存
						warehouseStockService.updateWarehouseStock(warehouseStock,warehouseStockOperation,0L,actionType,null);
					}
					delete(warehouseStockOperation);
				}
			}
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
	}


	/**
	 * 现有量
	 * @param warehouseStockOperation
	 * @param boxQuantity
	 * @param branchQuantity
	 * @param quantity
	 */
	private void onHandQuantity(WarehouseStockOperation warehouseStockOperation,
			BigDecimal boxQuantity,BigDecimal branchQuantity,BigDecimal quantity){
		/*现有量=单据数量*/
		//现有量（箱）
		warehouseStockOperation.setOnhandBoxQuantity(boxQuantity);
		//现有量（支）
		warehouseStockOperation.setOnhandBranchQuantity(branchQuantity);
		//现有量平方数
		warehouseStockOperation.setOnhandQuantity(quantity);
	}
	
	/**
	 * 负现有数量
	 * @param warehouseStockOperation
	 * @param boxQuantity
	 * @param branchQuantity
	 * @param quantity
	 */
	private void negativeOnHandQuantity(WarehouseStockOperation warehouseStockOperation,
			BigDecimal boxQuantity,BigDecimal branchQuantity,BigDecimal quantity){
		//现有量=0-单据数量
		//现有量（箱）
		warehouseStockOperation.setOnhandBoxQuantity(
				warehouseStockOperation.getOnhandBoxQuantity().subtract(boxQuantity));
		//现有量（支）
		warehouseStockOperation.setOnhandBranchQuantity(
				warehouseStockOperation.getOnhandBranchQuantity().subtract(branchQuantity));
		//现有量平方数
		warehouseStockOperation.setOnhandQuantity(
				warehouseStockOperation.getOnhandQuantity().subtract(quantity));
	}
	
	
	/**
	 * 可用数量
	 * @param warehouseStockOperation
	 * @param boxQuantity
	 * @param branchQuantity
	 * @param quantity
	 */
	private void availableQuantity(WarehouseStockOperation warehouseStockOperation,
			BigDecimal boxQuantity,BigDecimal branchQuantity,BigDecimal quantity){
		//可处理量=单据数量
		//可处理量（箱）
		warehouseStockOperation.setAvailableBoxQuantity(boxQuantity);
		//可处理量（支）
		warehouseStockOperation.setAvailableBranchQuantity(branchQuantity);
		//可处理量平方数
		warehouseStockOperation.setAvailableQuantity(quantity);
	}
	
	/**
	 * 负可用数量
	 * @param warehouseStockOperation
	 * @param boxQuantity
	 * @param branchQuantity
	 * @param quantity
	 */
	private void negativeAvailableQuantity(WarehouseStockOperation warehouseStockOperation,
			BigDecimal boxQuantity,BigDecimal branchQuantity,BigDecimal quantity){
		//可处理量=0-单据数量
		//可处理量（箱）
		warehouseStockOperation.setAvailableBoxQuantity(
				warehouseStockOperation.getAvailableBoxQuantity().subtract(boxQuantity));
		//可处理量（支）
		warehouseStockOperation.setAvailableBranchQuantity(
				warehouseStockOperation.getAvailableBranchQuantity().subtract(branchQuantity));
		//可处理量平方数
		warehouseStockOperation.setAvailableQuantity(
				warehouseStockOperation.getAvailableQuantity().subtract(quantity));
	}
	
	
	  
	@Override
	@Transactional
	public void saveWarehouseStockOperation(SystemDict billType, SystemDict actionType, AmShipping amShipping,
			AmShippingItem amShippingItem, WarehouseBatchItem warehouseBatchItem, WarehouseStock warehouseStock,
			BigDecimal boxQuantity, BigDecimal branchQuantity, BigDecimal quantity) {
		
		try {
			if(ConvertUtil.isEmpty(boxQuantity)){
				boxQuantity = new BigDecimal("0");
			}
			if(ConvertUtil.isEmpty(branchQuantity)){
				branchQuantity = new BigDecimal("0");
			}
			if(ConvertUtil.isEmpty(quantity)){
				quantity = new BigDecimal("0");
			}
			WarehouseStockOperation warehouseStockOperation = new WarehouseStockOperation();
			//判断是否为空，初始化默认值
			this.checkParamIsNotNull(warehouseStockOperation);
			//出入库
			warehouseStockOperation.setAmShipping(amShipping);
			//出入库明细
			warehouseStockOperation.setAmShippingItem(amShippingItem);
			//出入库明细批次
			warehouseStockOperation.setWarehouseBatchItem(warehouseBatchItem);
			//库存
			warehouseStockOperation.setWarehouseStock(warehouseStock);
			//单据类型
			warehouseStockOperation.setBillType(billType);
			//操作类型
			warehouseStockOperation.setActionType(actionType);
			
			System.out.print("billType.getValue()="+billType.getValue());
			System.out.print("actionType.getValue()="+actionType.getValue());
			
			if(billType.getValue().equals("出仓")){
                if(actionType.getValue().equals("保存")){
                    //负可用量
                    this.negativeAvailableQuantity(warehouseStockOperation,boxQuantity,branchQuantity,quantity);
                }else if (actionType.getValue().equals("审核")) {
                    //负现有量
                    this.negativeOnHandQuantity(warehouseStockOperation,boxQuantity,branchQuantity,quantity);
                    //负可用量
                    this.negativeAvailableQuantity(warehouseStockOperation,boxQuantity,branchQuantity,quantity);
                }
            }else if (billType.getValue().equals("入仓")) {
                if (actionType.getValue().equals("审核")) {
                    //现有量
                    this.onHandQuantity(warehouseStockOperation,boxQuantity,branchQuantity,quantity);
                    //可用量
                    this.availableQuantity(warehouseStockOperation,boxQuantity,branchQuantity,quantity);
                }
            }
			
			//保存库存操作表
			save(warehouseStockOperation);
			//更新库存
			warehouseStockService.updateWarehouseStock(warehouseStock,
					warehouseStockOperation,1L,billType,actionType);
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
	}
	
}
