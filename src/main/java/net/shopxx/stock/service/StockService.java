package net.shopxx.stock.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.ui.ModelMap;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.stock.entity.Stock;
import net.shopxx.stock.entity.StockTaskBuilder;

/**
 * Service - 库存
 */
public interface StockService extends BaseService<Stock> {

	/**
	 * 根据仓库和产品查库存
	 * 
	 * @param warehouseId
	 * @param productId
	 * @return
	 */
	public Map<String, Object> find(long warehouseId, long productId);

	public List<Map<String, Object>> findStockList(Long warehouseId, Long[] productIds);

	/**
	 * 处理库存
	 * 
	 * @param taskBuilder
	 */
	public void handleStock(StockTaskBuilder taskBuilder);

	/**
	 * 库存列表数据
	 */
	public Page<Map<String, Object>> findPage(Long[] productId, Long[] warehouseId, Integer isShowZero,
			String vonderCode, Pageable pageable);

	public Integer count(Long[] warehouseId, Long[] productId, Integer isShowZero, String vonderCode, Pageable pageable,
			ModelMap model, Integer page, Integer size);

	public List<Map<String, Object>> findItemList(Long[] warehouseId, Long[] productId, Integer isShowZero, Long[] ids,
			String vonderCode, Integer page, Integer size);

	public boolean isStockEnough(Long productId, Long warehouseId);

	public Map<String, Object> findLockStock(Long productId, Long warehouseId);

	public Map<String, Object> findLockStock(Long productId);

	public BigDecimal findActualStock(Long productId);

	/**
	 * 进销存报表列表数据
	 */
	public Page<Map<String, Object>> findInvoicingPage(Long productId, Long warehouseId, String startTime,
			String endTime, String vonderCode, String mod, Pageable pageable);

	public Integer count(Long productId, Long warehouseId, String startTime, String endTime, String vonderCode,
			String mod, Pageable pageable);

	public List<Map<String, Object>> findInvoicingList(Long productId, Long warehouseId, String startTime,
			String endTime, String vonderCode, String mod, Long[] ids, Integer page, Integer size);

	public Page<Map<String, Object>> findPage(String warehouseName, String productCode, Integer isShowZero,
			Pageable pageable);

	public Page<Map<String, Object>> findPage(Long[] warehouseIds, Long productId, Integer isShowZero,
			Pageable pageable);

	public Page<Map<String, Object>> findSuit(Long[] warehouseIds, Long mainProductId, Integer isShowZero,
			Pageable pageable);

	public Page<Map<String, Object>> findMain(Long[] warehouseIds, Long mainProductId, Integer isShowZero,
			Pageable pageable);

	public Page<Map<String, Object>> findPage(Pageable pageable, Long[] warehouseId, Long[] organizationId,
			Long[] productId, Long[] productLevelId, Long[] colorNumbersId, Long[] moistureContentId,
			Long[] newOldLogosIds, Long[] warehouseBatchId);

	public List<Map<String, Object>> findViewStock(Long warehouseId, Long organizationId, Long productId,
			Long productGrade, String vonderCode, String colourNumber, String moistureContent, Long[] batchIds,
			String newOldLogos, Long warehouseLocationId, String batchIdsStr);

	public BigDecimal getStockQuantity(Long warehouseId, Long organizationId, Long productId, Long productGrade,
			String colourNumber, String moistureContent, Long[] batchIds, String newOldLogos, Long warehouseLocationId,
			String batchIdsStr, Integer columnType);

	/**
	 * 出入库信息审核库存校验
	 */
	public Integer findViewStockCount(Long Id, Integer sign);

}
