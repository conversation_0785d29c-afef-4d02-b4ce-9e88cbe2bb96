package net.shopxx.stock.service;

import java.util.List;
import java.util.Map;

import com.aspose.cells.WorkbookDesigner;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.stock.entity.Stock;
import net.shopxx.util.ResponseEx;

/**
 * 库龄
 * <AUTHOR>
 *
 */
public interface StockAgeService extends BaseService<Stock> {
	
	/**
	 * 库龄报表列表数据
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param warehouseId
	 * @param productCategoryId
	 * @param vProductId
	 * @param productId
	 * @param woodTypeOrColor
	 * @param model
	 * @param ids
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findStockAgeList(String startTime, String endTime,
			Long[] saleOrgId,Long[] storeId,Long[] organizationId,Long[] warehouseId,
			Long[] productCategoryId,Long[] vProductId,Long[] productId,String woodTypeOrColor,
			String model,Long[] ids,Pageable pageable);
	
	public int findStockAgeListCount(String startTime, String endTime,
			Long[] saleOrgId,Long[] storeId,Long[] organizationId,Long[] warehouseId,
			Long[] productCategoryId,Long[] vProductId,Long[] productId,String woodTypeOrColor,
			String model,Long[] ids);
	
	
	public List<Map<String,Object>> stockAgeReport(String startTime, String endTime,
							   Long[] saleOrgId, Long[] storeId, Long[] organizationId, Long[] warehouseId,
							   Long[] productCategoryId, Long[] vProductId, Long[] productId, String woodTypeOrColor,
							   String model, Long[] ids, Pageable pageable);

}
