package net.shopxx.stock.service;

import java.util.List;
import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseSmethod;

/**
 * Service - 仓库
 */
public interface WarehouseBaseService extends ActWfBillService<Warehouse> {

	/**
	 * 保存仓库
	 * @param warehouse
	 * @param areaId
	 * @param saleOrgId
	 * @param areaIds
	 * @param stores
	 */
	public void saveWarehouse(Warehouse warehouse, Long areaId,
			Long[] saleOrgId,Boolean[] isChecks, Long[] areaIds, 
			Long stores, Long deliveryCorpId,Long productionPlantId,Integer warehouseStatus);

	/**
	 * 更新仓库
	 * @param warehouse
	 * @param areaId
	 * @param saleOrgId
	 * @param areaIds
	 * @param stores
	 */
	public void updateWarehouse(Warehouse warehouse, Long areaId, Long[] saleOrgId, 
			Boolean[] isChecks, Long[] areaIds, Long stores, Long deliveryCorpId,
			Long productionPlantId,Integer warehouseStatus);

	/**
	 * 查找是否存在仓库覆盖区域
	 * @param warehouseId
	 * @param aeraId
	 * @return
	 */
	public boolean existsWarehouseArea(Long warehouseId, Long aeraId);

	public Page<Map<String, Object>> findPage(String sn, String name,
			String erp_warehouse_code, Boolean isEnabled, Long supplierId,
			Integer type, Integer warehouseType, Long saleOrgId,
			Long organizationId, Long sbuId, Pageable pageable,
			Long sign,Integer isNotwarehouseType);

	public Warehouse findByErpWarehouseCode(String erp_warehouse_code,
			Long companyInfoId);

	public List<Map<String, Object>> findWarehouseSbu(Warehouse warehouse);
	
	public List<WarehouseSmethod> findWarehouseSmethod(Long id);
	
	
	public List<Map<String, Object>> findWarehouseStoreMember(Warehouse warehouse);
	
	
	public void saveOrUpdateWarehouseLocation(Warehouse warehouse);
	
	
	
	public List<Map<String, Object>> findwarehouseLocationList(Warehouse warehouse);
	
	/**
	 * 按参数查询库位信息
	 * @param code  库位编码
	 * @param warehouseId   仓库id
	 * @param remarks   库位备注
	 * @return
	 */
	public List<Map<String, Object>> findwarehouseLocationListByParam(String code, Long warehouseId,
			String remarks);

    public void checkWarehouseState(Warehouse warehouse, Integer warehouseStatus,
                                     String statusName, String operationName);

    public void cancel(Warehouse warehouse) throws Exception;

	public void close(Warehouse warehouse) throws Exception;
	/** 创建流程实例 */
    public void createWf(Long id, String modelId, Long objTypeId,Integer type);
}
