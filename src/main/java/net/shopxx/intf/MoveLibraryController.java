package net.shopxx.intf;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.order.service.MoveHandleService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Controller("intfMoveLibraryController")
@RequestMapping("/intf/moveLibrary")
public class MoveLibraryController {

	@Resource(name = "moveLibrarytHandleServiceImpl")
	private MoveHandleService moveLibrarytHandleService;

	private static String token = Global.getLoader().getProperty("synintf.token");

	/**
	 * 产品
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/moveback", method = RequestMethod.POST)
	public @ResponseBody
	ModelMap sync(HttpServletRequest request, HttpServletResponse response) {
		LogUtils.debug("========== 移库 ==========");
		ModelMap model = null;

		try {


			model = new ModelMap();
			String t = request.getHeader("Authorization");
			if (ConvertUtil.isEmpty(t) || !t.equals(token)) {
				response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
				LogUtils.debug("移库同步返回：" + JsonUtils.toJson(model));
				return model;
			}
			String requestTime = DateUtil.convert(new Date(),
					"yyyy-MM-dd HH:mm:ss.SSS");

			Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
//			Map<String, Object> map = ServiceIntfConfigUtil.getServletRequestData(request);
			LogUtils.debug("移库同步接收：" + JsonUtils.toJson(map));
			Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
			Map<String, Object> esbInfo = new HashMap<String, Object>();
			if (resbInfo == null) {
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0001");
				esbInfo.put("returnMsg", "esbInfo数据有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", "");
				esbInfo.put("attr2", "");
				esbInfo.put("attr3", "");
				model.put("esbInfo", esbInfo);
				LogUtils.debug("移库同步返回：" + JsonUtils.toJson(model));
				return model;
			}
			String instId = resbInfo.get("instId") == null ? ""
					: resbInfo.get("instId").toString();
			String rTime = resbInfo.get("requestTime") == null ? null
					: resbInfo.get("requestTime").toString();
			if (ConvertUtil.isEmpty(rTime)) {
				esbInfo.put("instId", instId);
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0003");
				esbInfo.put("returnMsg", "调用接口时间有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", resbInfo.get("attr1") == null ? ""
						: resbInfo.get("attr1"));
				esbInfo.put("attr2", resbInfo.get("attr2") == null ? ""
						: resbInfo.get("attr2"));
				esbInfo.put("attr3", resbInfo.get("attr3") == null ? ""
						: resbInfo.get("attr3"));
				model.put("esbInfo", esbInfo);
				LogUtils.debug("移库同步返回：" + JsonUtils.toJson(model));
				return model;
			}

			//逻辑处理

			String msg = "";
			try {
				msg = moveLibrarytHandleService.moveSync(map);
				esbInfo.put("returnStatus", "S");
				esbInfo.put("returnCode", "A0001");
				esbInfo.put("returnMsg", "成功");
			}
			catch (Exception e) {
				String[] returnMsg = msg.split("-");
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", returnMsg[0]);
				esbInfo.put("returnMsg", returnMsg[1]);
			}

			esbInfo.put("instId", instId);
			esbInfo.put("requestTime", requestTime);
			esbInfo.put("responseTime",
					DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
			esbInfo.put("attr1",
					resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
			esbInfo.put("attr2",
					resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
			esbInfo.put("attr3",
					resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
			model.put("esbInfo", esbInfo);

			Map<String, Object> resultInfo = new HashMap<String, Object>();
			model.put("resultInfo", resultInfo);
			LogUtils.debug("移库同步返回：" + JsonUtils.toJson(model));
		}
		catch (Exception e) {
			LogUtils.error("移库", e);
		}

		LogUtils.debug("========== 移库==========");
		return model;
	}

//	public static void main(String[] args) {
//
//		Map<String, Object> map = new HashMap<String, Object>();
//		Map<String, Object> esbInfo = new HashMap<String, Object>();
//		esbInfo.put("header_id", "abcdefg1234567");
//		esbInfo.put("requestTime", new SimpleDateFormat(
//				"yyyy-MM-dd HH:mm:ss:SSS").format(new Date()));
//		map.put("esbInfo", esbInfo);
//
//		Map<String, Object> resultInfo = new HashMap<String, Object>();
//
//		resultInfo.put("request_number", "19030912");
//		resultInfo.put("header_id", 1);
//		resultInfo.put("status", "已审核");
//		resultInfo.put("LAST_RICK_DATE", DateUtil.convert(new Date()));
//		resultInfo.put("LAST_RCV_DATE", DateUtil.convert(new Date()));
//		resultInfo.put("remark", "");
//
//		List<Map<String, Object>> items = new ArrayList<Map<String, Object>>();
//		Map<String, Object> item = new HashMap<String, Object>();
//		item.put("item_number", "8010316F53560003");
//		item.put("line_id", 1);
//		item.put("RICK_QUANTITY", 1.11);
//		item.put("RICK_QUANTITY1", 10.11);
//		item.put("RICK_QUANTITY2", 10.11);
//		item.put("RCV_QUANTITY", 1.88);
//		item.put("RCV_QUANTITY1", 100.88);
//		item.put("RCV_QUANTITY2", 10.11);
//		item.put("remark", "");
//		items.add(item);
//		resultInfo.put("items", items);
//		map.put("requestInfo", resultInfo);
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		String msg = HttpClientUtil.post("http://natest.etwowin.com.cn/intf/moveLibrary/moveback.jhtml",
//				JsonUtils.toJson(map),
//				headers);
//		System.out.println("map:" + JsonUtils.toJson(map));
//		System.out.println(msg);
//	}
	/*
	 * public static void main(String[] args) { SimpleDateFormat
	 * simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd"); Date date =
	 * DateUtil.convert(simpleDateFormat.format(new Date()) + " 23:59:59");
	 * System.out.println(date); }
	 */

}
