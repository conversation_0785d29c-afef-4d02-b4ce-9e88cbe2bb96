package net.shopxx.intf;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.*;
import net.shopxx.order.service.OAProjectService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller("oAProjectController")
@RequestMapping({"/intf/OAProject"})
public class OAProjectController
{
  private static String token = Global.getLoader().getProperty("synintf.token");
  
  @Resource(name="oAProjectServiceImpl")
  private OAProjectService oAProjectService;
  
  @RequestMapping(value={"/customerProject"}, method={org.springframework.web.bind.annotation.RequestMethod.POST})
  @ResponseBody
  public ModelMap sync(HttpServletRequest request, HttpServletResponse response)
  {
    LogUtils.debug("========== OA工程报备 ==========");
    ModelMap model = null;
    try
    {
//      A1_ServiceIntf_Config a1_ServiceIntf_Config = ServiceIntfConfigUtil.getServletConfig(request);
//      String token = a1_ServiceIntf_Config.getParam1();
      
      model = new ModelMap();
      String t = request.getHeader("Authorization");
      if ((ConvertUtil.isEmpty(t)) || (!t.equals(token))) {
        response.sendError(401);
        LogUtils.debug("OA工程报备 ：" + JsonUtils.toJson(model));
        return model;
      }
      String requestTime = DateUtil.convert(new Date(), 
        "yyyy-MM-dd HH:mm:ss.SSS");
      
      Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
//      Map<String, Object> map = ServiceIntfConfigUtil.getServletRequestData(request);
      LogUtils.debug("OA工程报备接收：" + JsonUtils.toJson(map));
      System.out.println("esb:" + JsonUtils.toJson(map));
      Map<String, Object> resbInfo = (Map<String, Object>)map.get("esbInfo");
      Map<String, Object> esbInfo = new HashMap<String, Object>();
      if (resbInfo == null) {
        esbInfo.put("returnStatus", "E");
        esbInfo.put("returnCode", "E0001");
        esbInfo.put("returnMsg", "esbInfo数据有误");
        esbInfo.put("requestTime", requestTime);
        esbInfo.put("responseTime", 
          DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        esbInfo.put("attr1", "");
        esbInfo.put("attr2", "");
        esbInfo.put("attr3", "");
        model.put("esbInfo", esbInfo);
        LogUtils.debug("OA工程报备返回：" + JsonUtils.toJson(model));
        return model;
      }
      String instId = resbInfo.get("instId") == null ? "" : 
        resbInfo.get("instId").toString();
      String rTime = resbInfo.get("requestTime") == null ? null : 
        resbInfo.get("requestTime").toString();
      if (ConvertUtil.isEmpty(rTime)) {
        esbInfo.put("instId", instId);
        esbInfo.put("returnStatus", "E");
        esbInfo.put("returnCode", "E0003");
        esbInfo.put("returnMsg", "调用接口时间有误");
        esbInfo.put("requestTime", requestTime);
        esbInfo.put("responseTime", 
          DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        esbInfo.put("attr1", resbInfo.get("attr1") == null ? "" : 
          resbInfo.get("attr1"));
        esbInfo.put("attr2", resbInfo.get("attr2") == null ? "" : 
          resbInfo.get("attr2"));
        esbInfo.put("attr3", resbInfo.get("attr3") == null ? "" : 
          resbInfo.get("attr3"));
        model.put("esbInfo", esbInfo);
        LogUtils.debug("OA工程报备返回：" + JsonUtils.toJson(model));
        return model;
      }
      
      String msg = "";
      
      msg = this.oAProjectService.oaProjectSync(map);
      System.out.println("message:" + msg);
      
      esbInfo.put("instId", instId);
      esbInfo.put("requestTime", requestTime);
      esbInfo.put("responseTime", 
        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
      esbInfo.put("attr1", 
        resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
      esbInfo.put("attr2", 
        resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
      esbInfo.put("attr3", 
        resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
      model.put("esbInfo", esbInfo);
    
      Map<String, Object> resultInfo = new HashMap<String, Object>();
      resultInfo.put("returnStatus", "S");
      resultInfo.put("returnCode", "A0001");
      resultInfo.put("returnMsg", "工程单创建成功");
      resultInfo.put("requestTime", requestTime);
      resultInfo.put("responseTime", requestTime);
      model.put("resultInfo", resultInfo);
      LogUtils.debug("OA工程报备返回：" + JsonUtils.toJson(model));
    }
    catch (Exception e) {
      LogUtils.error("OA工程报备", e);
    }
    
    LogUtils.debug("========== OA工程报备==========");
    return model;
  }
  
  public static void main(String[] args)
  {
    Map<String, Object> map = new HashMap<String, Object>();
    Map<String, Object> esbInfo = new HashMap<String, Object>();
    esbInfo.put("instId", "abcdefg1234567");
    esbInfo.put("requestTime", new SimpleDateFormat(
      "yyyy-MM-dd HH:mm:ss:SSS").format(new Date()));
    map.put("esbInfo", esbInfo);
    
    Map<String, Object> resultInfo = new HashMap<String, Object>();
    resultInfo.put("BBDH", Integer.valueOf(1));
    resultInfo.put("KEHBH", Integer.valueOf(2));
    resultInfo.put("BBLX", Integer.valueOf(3));
    resultInfo.put("BBR", Integer.valueOf(4));
    resultInfo.put("BBRDJCS", Integer.valueOf(5));
    resultInfo.put("BBRJYQY", Integer.valueOf(6));
    resultInfo.put("BBRLX", Integer.valueOf(7));
    resultInfo.put("BBRLXDH", Integer.valueOf(8));
    resultInfo.put("BBRLX", Integer.valueOf(9));
    resultInfo.put("BBRQ", Integer.valueOf(10));
    resultInfo.put("BBRQSF", Integer.valueOf(11));
    resultInfo.put("BBRXQCS", "12");
    resultInfo.put("BBRYJ", "13");
    resultInfo.put("BCLYY", "14");
    resultInfo.put("BXLY", "15");
    resultInfo.put("CJKHMC", "16");
    resultInfo.put("GCLX", "17");
    resultInfo.put("GCKHMC", "18");
    resultInfo.put("GCXMBM", "19");
    resultInfo.put("GCXMDZ", "20");
    resultInfo.put("GCXMMCXSJY", "21");
    resultInfo.put("GH", "22");
    resultInfo.put("GW", "23");
    resultInfo.put("HTQYT", "24");
    resultInfo.put("HTYY", "25");
    resultInfo.put("JFLLR", "26");
    resultInfo.put("JXSXM", "27");
    resultInfo.put("JSGXM", "28");
    resultInfo.put("KHMC", "29");
    resultInfo.put("KQLX", "30");
    resultInfo.put("LXRDH", "31");
    resultInfo.put("LXRZW", "32");
    resultInfo.put("QSF", "33");
    resultInfo.put("QXCS", "34");
    resultInfo.put("SFCT", "35");
    resultInfo.put("SFHT", "36");
    resultInfo.put("SFPSTB", "37");
    resultInfo.put("SFSXCBB", "38");
    resultInfo.put("SFSYGCBB", "11");
    resultInfo.put("SQQY", "40");
    resultInfo.put("SQR", "41");
    resultInfo.put("SSBM", "42");
    resultInfo.put("SSSZ", "43");
    resultInfo.put("SSYJBM", "44");
    resultInfo.put("XMBJ", "45");
    resultInfo.put("XMJD", "46");
    resultInfo.put("XMJD", "47");
    resultInfo.put("XMZTJM", "48");
    resultInfo.put("XMZTJM", "49");
    resultInfo.put("XXJD", "50");
    resultInfo.put("YSXBBDH", "51");
    resultInfo.put("YX", Integer.valueOf(52));
    
    List<Map<String, Object>> maplist = new ArrayList<Map<String, Object>>();
    maplist.add(resultInfo);
    map.put("requestInfo", maplist);
    System.out.println("map:" + map.toString());
    Map<String, String> headers = new HashMap<String, String>();
    headers.put("Authorization", token);
    String msg = HttpClientUtil.post("http://192.168.11.177:8001/intf/OAProject/customerProject.jhtml", 
      JsonUtils.toJson(map), 
      headers);
    System.out.println(msg);
  }
}
