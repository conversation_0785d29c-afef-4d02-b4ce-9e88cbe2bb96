package net.shopxx.intf;

/**
 *同步生成售后单接口
 */

import com.alibaba.fastjson.JSON;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.entity.AftersaleJudgment;
import net.shopxx.aftersales.entity.AftersaleWfRecord;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.aftersales.service.AftersaleWfRecordService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.*;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.util.TextareaLineBreakUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static net.shopxx.util.CommonUtil.objectToLong;

/**
 * 售后政策单接口
 */
@Controller("intfAftersaleController")
@RequestMapping("/intf/intfAftersale")
public class IntfAftersaleController extends BaseController {

    @Resource(name = "aftersaleServiceImpl")
    private AftersaleService aftersaleService;
    @Resource(name = "companyInfoBaseServiceImpl")
    CompanyInfoBaseService companyInfoService;
    @Resource(name = "areaBaseServiceImpl")
    private AreaBaseService areaService;
    @Resource(name = "aftersaleWfRecordServiceImpl")
    private AftersaleWfRecordService aftersaleWfRecordService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgBaseService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeService;
    @Resource(name = "sbuServiceImpl")
    private SbuService sbuService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    @Resource(name = "organizationServiceImpl")
    private OrganizationService organizationService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    /**
     * 保存
     * 接收容大同步售后单
     */
    @RequestMapping(value = "/saveAftersale", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg save(@RequestBody(required = true) Map<String,Object> aftersalesMap, HttpServletRequest request, HttpServletResponse response, HttpSession session) {
        if (aftersalesMap==null){
            return error("未接收到参数，请检查请求参数！");
        }
        LogUtils.info("aftersalesMap:"+JSON.toJSONString(aftersalesMap));
        Aftersale aftersale = JSON.parseObject(JSON.toJSONString(aftersalesMap), Aftersale.class);
        AftersaleJudgment aftersaleJudgment = JSON.parseObject(JSON.toJSONString(aftersalesMap.get("aftersaleJudgment")), AftersaleJudgment.class);
        aftersale.setStoreFaultMessage(TextareaLineBreakUtil.lineEnter(aftersale.getStoreFaultMessage()));
        Long headNewAreaId = objectToLong(aftersalesMap.get("headNewArea.id"));
        if (headNewAreaId == null) {
            return error("请选择铺装地址！");
        }
        Area headNewArea = areaService.find(headNewAreaId);
        if(headNewArea == null){
            return error("link找不到对应的地址");
        }
        aftersale.setHeadNewArea(headNewArea);
        if (aftersale.getContactNumber() != null) {
            String regEx = "^[1][3,4,5,7,8,9,6][0-9]{9}$";
            Pattern p = Pattern.compile(regEx);
            Matcher mr = p.matcher(aftersale.getContactNumber());
            if (!mr.matches()) {
                return error("联系电话格式有误");
            }
        }
        // if (aftersale.getZbsurveyorPhone() != null) {
        // String regEx = "^[1][3,4,5,7,8][0-9]{9}$";
        // Pattern p = Pattern.compile(regEx);
        // Matcher mr = p.matcher(aftersale.getZbsurveyorPhone());
        // if (!mr.matches()) {
        // return error("勘察人联系方式格式有误");
        // }
        // }
        if (aftersale.getIsCheckIn() == true && aftersale.getCheckInTime() == null) {
            return error("请选择入住时间！");
        }
        BigDecimal oneHundred = new BigDecimal("100");
        if (aftersale.getGroundMoistureContent() != null) {
            String a = aftersale.getGroundMoistureContent().toString();
//			int b = Integer.parseInt(a);
            BigDecimal b = new BigDecimal(a);
            if (b.compareTo(oneHundred)>1) {
                return error("地面含水率不能大于100");
            }
        }
        aftersale.setReturnStatus("0");
        if (aftersale.getFloorMoistureContent() != null) {
            String a = aftersale.getFloorMoistureContent().toString();
//			int b = Integer.parseInt(a);
            BigDecimal b = new BigDecimal(a);
            if (b.compareTo(oneHundred)>1) {
                return error("地板含水率不能大于100");
            }
        }
        if (aftersale.getAirHumidity() != null) {
            String a = aftersale.getAirHumidity().toString();
//			int b = Integer.parseInt(a);
            BigDecimal b = new BigDecimal(a);
            if (b.compareTo(oneHundred)>1) {
                return error("空气湿度不能大于100");
            }
        }
        if (aftersale.getSalesContractAttachs().size() < 1) {
            return error("请上传销售合同！");
        }
        if (aftersale.getAcceptanceSheetAttachs().size() < 1) {
            return error("请上传验收单！");
        }
        if (aftersale.getScenePicturesAttachs().size() < 5) {
            return error("请上传现场照片且数量不能小于5张！");
        }
        if (aftersale.getStoreTreatmentScheme() == null) {
            return error("请选择经销商处理方案");
        }
        if (!(aftersale.getPrice().compareTo(BigDecimal.ZERO) > 0)) {
            return error("单价必须大于0！");
        }
        CompanyInfo companyInfo = companyInfoService.find(9L);
        Long companyInfoId = companyInfo.getId();
        //机构
        List<Filter> filters = new ArrayList<Filter>();
        String saleOrgName = String.valueOf(aftersalesMap.get("saleOrgName")).trim();
        filters.clear();
        filters.add(Filter.eq("companyInfoId", companyInfoId));
        filters.add(Filter.eq("name",saleOrgName));
        filters.add(Filter.eq("isEnabled",true));
        SaleOrg saleOrg = saleOrgBaseService.find(filters);
        if (saleOrg==null){
            ExceptionUtil.throwServiceException("机构不存在，请检查名称是否正确");
        }



        //根据客户编码获取经销商信息
        //String storeName = String.valueOf(aftersalesMap.get("storeName")).trim();
        String storeSn = String.valueOf(aftersalesMap.get("storeSn")).trim();
        filters.clear();
        filters.add(Filter.eq("outTradeNo", storeSn));
        filters.add(Filter.eq("companyInfoId", companyInfoId));
//        filters.add(Filter.eq("name", storeName));
        Store store = storeService.find(filters);
        if (store==null){
            ExceptionUtil.throwServiceException("AM没有"+storeSn+"客户，请管理员维护！");
        }
        /**
         * 根据编码获取产品信息
         */
        Long productId = null;
        String vonder_code = String.valueOf(aftersalesMap.get("vonderCode")).trim();
        filters.clear();
        filters.add(Filter.eq("vonderCode",vonder_code));
        List<Product> products = productBaseService.findList(null,filters,null);
        ProductCategory productCategory = new ProductCategory();
        if (products!=null && products.size() > 0){
            productCategory = products.get(0).getProductCategory();
            aftersale.setProduct(products.get(0));
            productId = products.get(0).getId();
        }else {
            ExceptionUtil.throwServiceException("AM未找到产品编号为："+vonder_code+"的产品");
        }


        String[] storeAppeal = String.valueOf(aftersalesMap.get("storeAppeal")).split(",");
        filters.clear();
        filters.add(Filter.eq("sn", aftersale.getSn()));
        List<Aftersale> aftersales = aftersaleService.findList(null,filters,null);
        Long aftersaleId = null;
        if (aftersales!=null && aftersales.size()>0){
            aftersaleId = aftersales.get(0).getId();
        }

        //经营组织
        if(aftersalesMap.get("organizationName") != null) {
            filters.clear();
            String organizationName = String.valueOf(aftersalesMap.get("organizationName")).trim();
            filters.clear();
            filters.add(Filter.eq("companyInfoId", companyInfoId));
            filters.add(Filter.eq("name", organizationName));
            filters.add(Filter.eq("isEnabled", true));
            Organization organization = organizationService.find(filters);
            aftersale.setOrganization(organization);
        }

        //sbu
        if(aftersalesMap.get("sbuName") != null) {
            filters.clear();
            String sbuName = String.valueOf(aftersalesMap.get("sbuName")).trim();
            filters.clear();
            filters.add(Filter.eq("companyInfoId", companyInfoId));
            filters.add(Filter.eq("name", sbuName));
            Sbu sbu = sbuService.find(filters);
            aftersale.setSbu(sbu);
        }
        //businessType
        if(aftersalesMap.get("businessTypeValue") != null) {
            filters.clear();
            String businessTypeValue = String.valueOf(aftersalesMap.get("businessTypeValue")).trim();
            filters.clear();
            filters.add(Filter.eq("companyInfoId", companyInfoId));
            filters.add(Filter.eq("code", "businessAfterSaleType"));
            filters.add(Filter.eq("value", businessTypeValue));
            SystemDict systemDict = systemDictBaseService.find(filters);
            aftersale.setBusinessType(systemDict);
        }
        if(aftersalesMap.get("orderManagerUsername") != null) {
            filters.clear();
            String orderManagerUsername = String.valueOf(aftersalesMap.get("orderManagerUsername")).trim();
            StoreMember storeMember = storeMemberBaseService.findByUsername(orderManagerUsername, 9L);
            aftersale.setOrderManager(storeMember);
        }
        if(aftersalesMap.get("productionSumUpUsername") != null) {
            filters.clear();
            String productionSumUpUsername = String.valueOf(aftersalesMap.get("productionSumUpUsername")).trim();
            StoreMember storeMember = storeMemberBaseService.findByUsername(productionSumUpUsername, 9L);
            aftersale.setProductionSumUp(storeMember);
        }
        if(aftersalesMap.get("surveyorUsername") != null) {
            filters.clear();
            String surveyorUsername = String.valueOf(aftersalesMap.get("surveyorUsername")).trim();
            StoreMember storeMember = storeMemberBaseService.findByUsername(surveyorUsername, 9L);
            aftersale.setProductionSumUp(storeMember);
        }
        aftersaleService.aftersaleSave(aftersale, saleOrg.getId(), productId, store==null?null:store.getId(), storeAppeal,productCategory.getId(),companyInfoId,aftersaleId,aftersaleJudgment);

        //删除旧数据
        if (aftersaleId!=null){
            aftersaleWfRecordService.deleteByAftersaleId(aftersaleId);
        }
        aftersale.setInvoiceStatus("2");
        aftersale.setWfState(2);
       /* if(aftersale.getFd() != null && (aftersale.getFd().contains("全部更换") || aftersale.getFd().contains("局部更换") || aftersale.getFd().contains("赔偿")) ){
            aftersaleService.createRdPolicyBill(aftersale);
        }*/

        //保存中板同步过来的流程记录
        List<String> arr = JSON.parseObject(JSON.toJSONString(aftersalesMap.get("wfItems")),List.class) ;
        if (arr!=null && arr.size()>0){
            for (int i=0;i<arr.size(); i++){
                AftersaleWfRecord aftersaleWfRecord = JSON.parseObject(JSON.toJSONString(arr.get(i)), AftersaleWfRecord.class);
                aftersale.setCompanyInfoId(Long.valueOf(9));
                aftersaleWfRecord.setAftersaleId(aftersale.getId());
                aftersaleWfRecord.setId(null);
                aftersaleWfRecordService.save(aftersaleWfRecord);
            }
        }
        return success().addObjX(aftersale.getId());
    }
}
