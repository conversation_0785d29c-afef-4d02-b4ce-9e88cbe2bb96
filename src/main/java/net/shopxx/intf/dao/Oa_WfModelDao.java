package net.shopxx.intf.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("oa_WfModelDao")
public class Oa_WfModelDao extends DaoCenter {
	
	public String[] findWfModel(String MODELID){
		StringBuilder sql = new StringBuilder();
//		sql.append("SELECT NAME_ requestname,KEY_ workflowname FROM act_re_model WHERE 1=1");
		sql.append("SELECT NAME_ requestname,KEY_ workflowname FROM ACT_RE_MODEL WHERE 1=1");
		if(MODELID!=null){
			sql.append(" AND ID_ = "+MODELID);
		}
		List<String> s = new ArrayList<String>();
		List<Map<String, Object>> map = getNativeDao().findListMap(sql.toString(), null, 0);
		if(map!=null){
			if(map.size()>0){
				s.add(map.get(0).get("requestname")!=null?map.get(0).get("requestname").toString():"");
				s.add(map.get(0).get("workflowname")!=null?map.get(0).get("requestname").toString():"");				
			}
		}else{
			return new String[]{"",""};
		}
		return s.toArray(new String[]{});
	}
	
	public String findPcUrl(Long OBJTYPEID){
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT oa_url FROM act_wf_obj_config WHERE 1=1 ");
		if(OBJTYPEID!=null){
			sql.append(" AND obj_type_id ="+OBJTYPEID);
		}
		return getNativeDao().findString(sql.toString(), null);
	}
	
	public String findWfStartUser(String procInstId){
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT sm.username FROM ACT_HI_PROCINST ahp,xx_store_member sm WHERE ahp.START_USER_ID_ = sm.id");
		if(procInstId!=null){
			sql.append(" AND ahp.ID_ = "+procInstId);
		}
		return getNativeDao().findString(sql.toString(), null);
	}
}
	