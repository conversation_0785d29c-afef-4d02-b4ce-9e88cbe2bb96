package net.shopxx.intf.job;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.intf.service.AmStoreToService;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
@Component("intfStoreToAmJob")
@Lazy(false)
public class IntfStoreToAmJob {
	
	@Scheduled(cron = "0 0/2 * * * ?")
	public void storeInft() {
		LogUtils.info("同步客户数据到AM接口执行");
		AmStoreToService amStoreToService = SpringUtils.getBean("amStoreToServiceImpl",AmStoreToService.class);
		String sql = "select * from a1_messageto where type = 25";
		List<A1_MessageTo> a1_MessageTos = amStoreToService.getDaoCenter().getNativeDao().findListManaged(sql, null, 100, A1_MessageTo.class);
		for (A1_MessageTo a1_MessageTo : a1_MessageTos) {
			try {
				amStoreToService.pushStoreToAm(a1_MessageTo);
			}catch (Exception e) {
				LogUtils.error("同步客户数据到AM", e);
				try {
					amStoreToService.saveHistory(a1_MessageTo,"1","失败",e.getMessage());
				}catch (Exception ex) {
					LogUtils.error("同步客户数据到AM1", ex);
				}
			}
		}
	}

}
