package net.shopxx.intf.job;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.intf.service.AmStoreMemberToService;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
@Component("intfStoreMemberToAmJob")
@Lazy(false)
public class IntfStoreMemberToAmJob {
	
	@Scheduled(cron = "0 0/2 * * * ?")
	public void storeInft() {
		LogUtils.info("同步用户数据到AM接口执行");
		AmStoreMemberToService amStoreMemberToService = SpringUtils.getBean("amStoreMemberToServiceImpl",AmStoreMemberToService.class);
		String sql = "select * from a1_messageto where type = 26";
		List<A1_MessageTo> a1_MessageTos = amStoreMemberToService.getDaoCenter().getNativeDao().findListManaged(sql, null, 100, A1_MessageTo.class);
		for (A1_MessageTo a1_MessageTo : a1_MessageTos) {
			try {
				amStoreMemberToService.pushStoreMemberToAm(a1_MessageTo);
			}catch (Exception e) {
				LogUtils.error("同步用户数据到AM", e);
				try {
					amStoreMemberToService.saveHistory(a1_MessageTo,"1","1",e.getMessage());
				}catch (Exception ex) {
					LogUtils.error("同步用户数据到AM1", ex);
				}
			}
		}
	}
}
