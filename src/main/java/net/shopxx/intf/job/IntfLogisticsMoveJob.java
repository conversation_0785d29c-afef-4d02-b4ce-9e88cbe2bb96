package net.shopxx.intf.job;

import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.intf.service.IntfOrderMessageToService;

@Component("intfLogisticsMove")
@Lazy(false)
public class IntfLogisticsMoveJob {

	@Scheduled(cron = "0 0/2 * * * ?")
	public void sendOrderToBMS() {
		try {
			shippingInft();//大自然移库物流接口
			//policyInft();
			//policyHaveInvoiceInft();

		}
		catch (Exception e) {
			LogUtils.error(e);
		}

	}
	
	
	public void shippingInft() {
		LogUtils.info("同步移库数据到家哇云接口执行");
		IntfOrderMessageToService a1_MessageToService = SpringUtils.getBean("intfOrderMessageToServiceImpl",
				IntfOrderMessageToService.class);
		String sql = "select * from a1_messageto where type=55";
		List<A1_MessageTo> a1_MessageTos = a1_MessageToService.getDaoCenter()
				.getNativeDao()
				.findListManaged(sql, null, 100, A1_MessageTo.class);
		for (A1_MessageTo a1_MessageTo : a1_MessageTos) {
			try {
				a1_MessageToService.pushLogisticsJwy(a1_MessageTo);
			}
			catch (Exception e) {
				LogUtils.error("同步移库数据到家哇云", e);
				try {
					a1_MessageToService.saveHistory(a1_MessageTo,
							"1",
							null,
							e.getMessage());
				}
				catch (Exception e1) {
					LogUtils.error("同步移库数据到家哇云1", e1);
				}
			}
		}
	}
}
