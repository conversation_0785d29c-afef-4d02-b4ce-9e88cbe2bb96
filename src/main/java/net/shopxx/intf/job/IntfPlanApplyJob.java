package net.shopxx.intf.job;
import java.util.List;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.intf.service.IntfOrderMessageToService;
@Component("intfPlanApplyJob")
@Lazy(false)
public class IntfPlanApplyJob {
	
	
	@Scheduled(cron = "0 0/3 * * * ?")
	public void storeInft() {
		LogUtils.info("同步计划提报数据到ERP接口执行");
		IntfOrderMessageToService intfOrderMessageToService = SpringUtils.getBean("intfOrderMessageToServiceImpl",IntfOrderMessageToService.class);
		String sql = "select * from a1_messageto where type = 27";
		List<A1_MessageTo> a1_MessageTos = intfOrderMessageToService.getDaoCenter().getNativeDao().findListManaged(sql, null, 100, A1_MessageTo.class);
		for (A1_MessageTo a1_MessageTo : a1_MessageTos) {
			try {
				intfOrderMessageToService.pushPlanApplyToErp(a1_MessageTo);
			}catch (Exception e) {
				LogUtils.error("同步计划提报数据到ERP0", e);
				try {
					intfOrderMessageToService.saveHistory(a1_MessageTo,"1",null,e.getMessage());
				}catch (Exception ex) {
					LogUtils.error("同步计划提报数据到ERP1", ex);
				}
			}
		}
	}
}
