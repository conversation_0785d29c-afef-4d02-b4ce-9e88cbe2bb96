package net.shopxx.intf.job;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.intf.service.ClientPushFourHundredService;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("intfDealerToFourJob")
@Lazy(false)
public class IntfDealerToFourJob {


    /**
     * 售后订单信息同步到容大
     */
    @Scheduled(cron = "0 0/2 * * * ?")
    public void sendAfterInfoInft() {
        LogUtils.info("售后订单信息到401接口执行");
        ClientPushFourHundredService clientPushFourHundredService = SpringUtils.getBean("clientPushFourHundredServiceImpl", ClientPushFourHundredService.class);
        String sql = "select * from a1_messageto where type = 401";
        List<A1_MessageTo> a1_MessageTos = clientPushFourHundredService.getDaoCenter().getNativeDao().findListManaged(sql, null, 100, A1_MessageTo.class);
        for (A1_MessageTo a1_MessageTo : a1_MessageTos) {
            try {
                clientPushFourHundredService.sendAfterInfoInft(a1_MessageTo);
            } catch (Exception e) {
                LogUtils.error("售后订单信息到容大异常:", e);
                try {
                    clientPushFourHundredService.saveHistory(a1_MessageTo, "1", "1", e.getMessage());
                } catch (Exception ex) {
                    LogUtils.error("售后订单信息到401写历史记录表异常:", ex);
                }
            }
        }
    }


}
