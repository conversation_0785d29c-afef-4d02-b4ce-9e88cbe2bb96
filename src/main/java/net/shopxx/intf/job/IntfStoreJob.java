package net.shopxx.intf.job;

import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.intf.service.IntfOrderMessageToService;

@Component("intfStoreJob")
@Lazy(false)
public class IntfStoreJob {

	@Scheduled(cron = "20 0/2 * * * ?")
	public void sendStoreToBMS() {
		try {
			storeInft();//大自然客户接口
		}
		catch (Exception e) {
			LogUtils.error(e);
		}

	}

	/**接口*/
	public void storeInft() {
		LogUtils.info("同步客户数据到ERP接口执行");
		IntfOrderMessageToService a1_MessageToService = SpringUtils.getBean(
				"intfOrderMessageToServiceImpl",
				IntfOrderMessageToService.class);
		String sql = "select * from a1_messageto where type = 5";
		List<A1_MessageTo> a1_MessageTos = a1_MessageToService.getDaoCenter()
				.getNativeDao()
				.findListManaged(sql, null, 100, A1_MessageTo.class);
		for (A1_MessageTo a1_MessageTo : a1_MessageTos) {
			try {
				a1_MessageToService.pushStoreToErp(a1_MessageTo);
			}
			catch (Exception e) {
				LogUtils.error("同步客户数据到ERP0", e);
				try {
					a1_MessageToService.saveHistory(a1_MessageTo,
							"1",
							null,
							e.getMessage());
				}
				catch (Exception e1) {
					LogUtils.error("同步客户数据到ERP1", e1);
				}
			}
		}
	}

}
