package net.shopxx.intf.job;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.intf.service.IntfOrderMessageToService;

@Component("intfOrderJob")
@Lazy(false)
public class IntfOrderJob {

	@Scheduled(cron = "0 0/2 * * * ?")
	public void sendOrderToBMS() {
		try {
			shippingInft();//大自然发货接口
			//policyInft();
			//policyHaveInvoiceInft();

		}
		catch (Exception e) {
			LogUtils.error(e);
		}

	}

	/**接口*/
	public void shippingInft() {
		LogUtils.info("同步订单数据到ERP接口执行");
		final IntfOrderMessageToService a1_MessageToService = SpringUtils.getBean("intfOrderMessageToServiceImpl",
				IntfOrderMessageToService.class);
		String sql = "select * from a1_messageto where type=0";
		List<A1_MessageTo> a1_MessageTos = a1_MessageToService.getDaoCenter()
				.getNativeDao()
				.findListManaged(sql, null, 100, A1_MessageTo.class);
		//创建线程池
		ExecutorService pool = Executors.newFixedThreadPool(20);
		final Semaphore semaphore = new Semaphore(20);
		
		for (final A1_MessageTo a1_MessageTo : a1_MessageTos) {
			try {
				
				semaphore.acquireUninterruptibly();
				pool.submit(new Callable<Integer>() {
					public Integer call() throws Exception {
						try{
							a1_MessageToService.pushShippingToErp(a1_MessageTo);
							return null;
						}finally{
							semaphore.release();
						}
					}
				});
			}catch (Exception e) {
				LogUtils.error("同步订单数据到ERP0", e);
				try {
					a1_MessageToService.saveHistory(a1_MessageTo,
							"1",
							null,
							e.getMessage());
				}
				catch (Exception e1) {
					LogUtils.error("同步订单数据到ERP1", e1);
				}
			}
		}
		try{
            semaphore.acquireUninterruptibly(20);
        }finally {
            semaphore.release(20);
        }
		pool.shutdown();
	}

/*	public void policyInft() {
		LogUtils.info("同步政策无发票数据到ERP接口执行");
		IntfOrderMessageToService a1_MessageToService = (IntfOrderMessageToService) SpringUtils.getBean("intfOrderMessageToServiceImpl",
				IntfOrderMessageToService.class);
		String sql = "select * from a1_messageto where type=20";
		List<A1_MessageTo> a1_MessageTos = a1_MessageToService.getDaoCenter()
				.getNativeDao()
				.findListManaged(sql, null, 100, A1_MessageTo.class);
		for (A1_MessageTo a1_MessageTo : a1_MessageTos)
			try {
				a1_MessageToService.pushPolicyToErp(a1_MessageTo);
			}
			catch (Exception e) {
				LogUtils.error("同步政策无发票数据到ERP0", e);
				try {
					a1_MessageToService.saveHistory(a1_MessageTo,
							"1",
							null,
							e.getMessage());
				}
				catch (Exception e1) {
					LogUtils.error("同步政策无发票数据到ERP1", e1);
				}
			}
	}

	public void policyHaveInvoiceInft() {
		LogUtils.info("同步政策有发票数据到ERP接口执行");
		IntfOrderMessageToService a1_MessageToService = (IntfOrderMessageToService) SpringUtils.getBean("intfOrderMessageToServiceImpl",
				IntfOrderMessageToService.class);
		String sql = "select * from a1_messageto where type=21";
		List<A1_MessageTo> a1_MessageTos = a1_MessageToService.getDaoCenter()
				.getNativeDao()
				.findListManaged(sql, null, 100, A1_MessageTo.class);
		for (A1_MessageTo a1_MessageTo : a1_MessageTos)
			try {
				a1_MessageToService.pushPolicyHaveInvoiceToErp(a1_MessageTo);
			}
			catch (Exception e) {
				LogUtils.error("同步政策有发票数据到ERP0", e);
				try {
					a1_MessageToService.saveHistory(a1_MessageTo,
							"1",
							null,
							e.getMessage());
				}
				catch (Exception e1) {
					LogUtils.error("同步政策有发票数据到ERP1", e1);
				}
			}
	}*/
}
