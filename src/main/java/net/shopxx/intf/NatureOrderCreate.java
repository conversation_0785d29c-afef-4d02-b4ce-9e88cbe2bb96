package net.shopxx.intf;

import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPMessage;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.service.ShippingService;

import org.w3c.dom.Document;

public class NatureOrderCreate {

	private static String ns = "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_om_create_soa_pkg/";
//	private static String wsdlUrl = "http://**************:8082/api/v1/Erp/Order/Sync";//测试
//	private static String wsdlUrl = "http://**************:8082/api/v1/Erp/Order/Sync";//正式
//	private static String username = "link01";
//	private static String password = "7iURWx3JNDQFvhT7";//测试
//	private static String password = "tzNUwOiRry3juNAR";//正式
	
	private static String wsdlUrl =  Global.getLoader().getProperty("dbintf.url")+"/api/v1/Erp/Order/Sync";
	private static String username = Global.getLoader().getProperty("dbintf.username");
	private static String password = Global.getLoader().getProperty("dbintf.password");

	public Map<String, Object> createOrder(Map<String, Object> params)
			throws Exception {

		//1、创建服务(Service)
		URL url = new URL(wsdlUrl);
		QName sname = new QName(ns, "SCUX_NATURE_OM_CREATE_SOA_PKG_Service");
		Service service = Service.create(url, sname);

		//2、创建Dispatch
		Dispatch<SOAPMessage> dispatch = service.createDispatch(new QName(ns,
				"SCUX_NATURE_OM_CREATE_SOA_PKG_Port"),
				SOAPMessage.class,
				Service.Mode.MESSAGE);
 
		//3、创建SOAPMessage
		SOAPMessage msg = MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)
				.createMessage();
		SOAPEnvelope envelope = msg.getSOAPPart().getEnvelope();
		//4、创建QName来指定消息中传递数据
		envelope.addNamespaceDeclaration("cre", "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_om_create_soa_pkg/create_order/");
		SOAPBody body = envelope.getBody();

		SOAPElement ele = body.addChildElement("InputParameters", "cre");
		
		// 传递参数 
		SOAPElement esbInfo = ele.addChildElement("esbInfo", "cre");
		esbInfo.addChildElement("instId", "cre").setValue(UUID.randomUUID()
				.toString());
		esbInfo.addChildElement("requestTime", "cre")
				.setValue(DateUtil.convert(new Date(),
						"yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.addChildElement("attr1", "cre");
		esbInfo.addChildElement("attr2", "cre");
		esbInfo.addChildElement("attr3", "cre");

		SOAPElement queryInfo = ele.addChildElement("queryInfo", "cre");
		queryInfo.addChildElement("pageSize", "cre");
		queryInfo.addChildElement("currentPage", "cre");

		SOAPElement requestInfo = ele.addChildElement("requestInfo", "cre");

		SOAPElement headerRec = requestInfo.addChildElement("HEADER_REC", "cre");
		SOAPElement lineTbl = requestInfo.addChildElement("LINE_TBL", "cre");

		//表头
		headerRec.addChildElement("SOURCE_CODE", "cre")
				.setValue(params.get("SOURCE_CODE").toString());//来源系统代码,固定“CRM”
		headerRec.addChildElement("SOURCE_NUM", "cre")
				.setValue(params.get("SOURCE_NUM").toString());//来源ID,CRM发货通知单头id
		headerRec.addChildElement("ORG_ID", "cre")
				.setValue(params.get("ORG_ID").toString());//经营组织id,根据仓库的经营组织id来取
		headerRec.addChildElement("ORDER_TYPE", "cre")
				.setValue(params.get("ORDER_TYPE").toString());//订单类型,传名称：零售订单、零售退货订单
		headerRec.addChildElement("BUSSINESS_TYPE", "cre")
				.setValue(params.get("BUSSINESS_TYPE").toString());//业务类型,客制化字段，传名称：经销商零售、商业地板、直营零售、家装、电商
		headerRec.addChildElement("CUSTOMER_NUMBER", "cre")
				.setValue(params.get("CUSTOMER_NUMBER").toString());//客户编码
		headerRec.addChildElement("SOURCE_SHIP_TO_ADDRESS", "cre")
				.setValue(params.get("SOURCE_SHIP_TO_ADDRESS").toString());//收货地址id,CRM客户收货地址id
		headerRec.addChildElement("SOURCE_BILL_TO_ADDRESS", "cre")
				.setValue(params.get("SOURCE_BILL_TO_ADDRESS").toString());//收单地址id,CRM客户收单地址id
		headerRec.addChildElement("SALESREP", "cre")
				.setValue(params.get("SALESREP").toString());//销售人员,ERP存储待定
		headerRec.addChildElement("TERMS", "cre").setValue(params.get("TERMS")
				.toString());//付款条件,传：立即
		headerRec.addChildElement("CUST_PO_NUMBER", "cre")
				.setValue(params.get("CUST_PO_NUMBER").toString());//客户PO,CRM发货通知单号
		headerRec.addChildElement("ORDERED_DATE", "cre")
				.setValue(params.get("ORDERED_DATE").toString());//订单日期,格式按照ESB标准，当前日期，2018-07-11
		headerRec.addChildElement("SBU", "cre").setValue(params.get("SBU")
				.toString());//SBU,订单头ATTRIBUTE10，家居头弹性域，传代码，CRM从机构属性里面取 地板中心、….
		headerRec.addChildElement("TRANSACTIONAL_CURR_CODE", "cre")
				.setValue(params.get("TRANSACTIONAL_CURR_CODE").toString());//币种,传:CNY
		headerRec.addChildElement("SHIPPING_METHOD", "cre")
				.setValue(params.get("SHIPPING_METHOD").toString());//发运方式,海运、汽运、铁运、海铁联运，值待补充
		headerRec.addChildElement("FREIGHT_CARRIER", "cre")
				.setValue(params.get("FREIGHT_CARRIER").toString());//承运商名称
		headerRec.addChildElement("DRIVER", "cre")
				.setValue(params.get("DRIVER").toString());//司机姓名,客制化字段
		headerRec.addChildElement("PHONE_NUMBER", "cre")
				.setValue(params.get("PHONE_NUMBER").toString());//司机手机号,客制化字段
		headerRec.addChildElement("LICENSE_PLATE", "cre")
				.setValue(params.get("LICENSE_PLATE").toString());//车号/车牌号,客制化字段
		headerRec.addChildElement("ORDER_NOTES", "cre")
				.setValue(params.get("ORDER_NOTES").toString());//订单备注,客制化字段
		headerRec.addChildElement("SHIPPING_INSTRUCTIONS", "cre")
				.setValue(params.get("SHIPPING_INSTRUCTIONS").toString());//发货备注,客制化字段
		headerRec.addChildElement("ERP_USER_NAME", "cre")
				.setValue(params.get("ERP_USER_NAME").toString());//创建人,创建人工号，对应FND_USER表
		headerRec.addChildElement("ATTRIBUTE16", "cre")
				.setValue(params.get("ATTRIBUTE16").toString());//关联订单号,CRM销售订单号
		headerRec.addChildElement("ATTRIBUTE17", "cre")
				.setValue(params.get("ATTRIBUTE17").toString());//地区,家居头弹性域，传CRM的客户所属机构名称
		headerRec.addChildElement("ATTRIBUTE3", "cre")
				.setValue(params.get("ATTRIBUTE3").toString());//工厂完工入库日期,全局头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE4", "cre")
				.setValue(params.get("ATTRIBUTE4").toString());//送货双柜数,全局头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE5", "cre")
				.setValue(params.get("ATTRIBUTE5").toString());//工程项目类型,全局头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE15", "cre")
				.setValue(params.get("ATTRIBUTE15").toString());//是否已经发送发货通知,全局头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE6", "cre")
				.setValue(params.get("ATTRIBUTE6").toString());//起始发货地点,全局头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE11", "cre")
				.setValue(params.get("ATTRIBUTE11").toString());//工程项目编号,家居头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE12", "cre")
				.setValue(params.get("ATTRIBUTE12").toString());//交货日期,家居头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE13", "cre")
				.setValue(params.get("ATTRIBUTE13").toString());//订单来源,家居头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE14", "cre")
				.setValue(params.get("ATTRIBUTE14").toString());//订单来源备注,家居头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE18", "cre")
				.setValue(params.get("ATTRIBUTE18").toString());//合同名称,家居头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE19", "cre")
				.setValue(params.get("ATTRIBUTE19").toString());//分派工厂,家居头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE20", "cre")
				.setValue(params.get("ATTRIBUTE20").toString());//是否内部交易,家居头弹性域，不传
		headerRec.addChildElement("ATTRIBUTE2", "cre")
		.setValue(params.get("ATTRIBUTE2").toString());// 甲方/经销商名称

		//明细
		List<Map<String, Object>> lineParams = (List) params.get("LINE_TBL");
		for (Map<String, Object> lineParam : lineParams) {

			SOAPElement lineTblItem = lineTbl.addChildElement("LINE_TBL_ITEM",
					"cre");

			Map<String, Object> lineData = (Map) lineParam.get("LINE_TBL_ITEM");

			lineTblItem.addChildElement("SOURCE_CODE", "cre")
					.setValue(lineData.get("SOURCE_CODE").toString());//来源系统代码,CRM
			lineTblItem.addChildElement("SOURCE_NUM", "cre")
					.setValue(lineData.get("SOURCE_NUM").toString());//来源ID,CRM发货通知单头id
			lineTblItem.addChildElement("SOURCE_LINE_NUM", "cre")
					.setValue(lineData.get("SOURCE_LINE_NUM").toString());//来源行ID,CRM发货通知单行id
			lineTblItem.addChildElement("ORDERED_ITEM", "cre")
					.setValue(lineData.get("ORDERED_ITEM").toString());//物料编码
			lineTblItem.addChildElement("MATERIAL_GRADE", "cre")
					.setValue(lineData.get("MATERIAL_GRADE").toString());//物料等级,订单行ATTRIBUTE2，传文本：优等品、二等品
			lineTblItem.addChildElement("ORDERED_QUANTITY", "cre")
					.setValue(lineData.get("ORDERED_QUANTITY").toString());//数量（平方数）
			lineTblItem.addChildElement("ORDER_QUANTITY_UOM", "cre")
					.setValue(lineData.get("ORDER_QUANTITY_UOM").toString());//单位（平方）,m2
			lineTblItem.addChildElement("ORDERED_QUANTITY2", "cre")
					.setValue(lineData.get("ORDERED_QUANTITY2")==null?"0":lineData.get("ORDERED_QUANTITY2").toString());//数量（支）
			lineTblItem.addChildElement("ORDERED_QUANTITY_UOM2", "cre")
					.setValue(lineData.get("ORDERED_QUANTITY_UOM2").toString());//辅助单位（支）,支
			lineTblItem.addChildElement("UNIT_SELLING_PRICE", "cre")
					.setValue(lineData.get("UNIT_SELLING_PRICE").toString());//单价（平方）,2位小数
			lineTblItem.addChildElement("SHIP_FROM", "cre")
					.setValue(lineData.get("SHIP_FROM").toString());//仓库代码,取CRM主表仓库代码
			lineTblItem.addChildElement("ORGANIZATION_ID", "cre")
					.setValue(lineData.get("ORGANIZATION_ID").toString());//库存组织id,根据仓库的库存组织id来取
			lineTblItem.addChildElement("SCHEDULE_ARRIVAL_DATE", "cre")
					.setValue(lineData.get("SCHEDULE_ARRIVAL_DATE").toString());//交货日期,取CRM主表交货日期
			lineTblItem.addChildElement("ATTRIBUTE17", "cre")
					.setValue(lineData.get("ATTRIBUTE17")==null?"0":lineData.get("ATTRIBUTE17").toString());//箱数,全局行弹性域
			lineTblItem.addChildElement("ATTRIBUTE18", "cre")
					.setValue(lineData.get("ATTRIBUTE18").toString());//联系人信息,全局行弹性域，传CRM主表收货人姓名和电话 前两段为姓名和电话用“；”分隔， 比如 “李工;16543060061;;;;;”
			lineTblItem.addChildElement("LINE_TYPE", "cre")
					.setValue(lineData.get("LINE_TYPE").toString());//订单行类型,不传
			lineTblItem.addChildElement("ATTRIBUTE3", "cre")
					.setValue(lineData.get("ATTRIBUTE3").toString());//物料色号,全局行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE1", "cre")
					.setValue(lineData.get("ATTRIBUTE1").toString());//合同编号,全局行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE19", "cre")
					.setValue(lineData.get("ATTRIBUTE19").toString());//实际收货数量,全局行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE20", "cre")
					.setValue(lineData.get("ATTRIBUTE20").toString());//是否已经同步,全局行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE4", "cre")
					.setValue(lineData.get("ATTRIBUTE4").toString());//发票抬头,全局行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE5", "cre")
					.setValue(lineData.get("ATTRIBUTE5").toString());//成品编码,家居行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE6", "cre")
					.setValue(lineData.get("ATTRIBUTE6").toString());//成品序号,家居行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE12", "cre")
			.setValue(lineData.get("ATTRIBUTE12").toString());//订单结算价
			lineTblItem.addChildElement("ATTRIBUTE7", "cre")
					.setValue(lineData.get("ATTRIBUTE7").toString());//门类别,家居行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE8", "cre")
					.setValue(lineData.get("ATTRIBUTE8").toString());//木门玻璃型号,家居行弹性域，不传
			lineTblItem.addChildElement("ATTRIBUTE9", "cre")
			.setValue(lineData.get("ATTRIBUTE9").toString());//明细备注
			lineTblItem.addChildElement("ATTRIBUTE10", "cre")
			.setValue(lineData.get("ATTRIBUTE10").toString());//原ERP单号
			lineTblItem.addChildElement("ATTRIBUTE11", "cre")
			.setValue(lineData.get("ATTRIBUTE11").toString());//发货单行ID

		}  

		//msg.writeTo(System.out);
		TransformerFactory transformerFactory = TransformerFactory.newInstance();
		Transformer transformer = transformerFactory.newTransformer();
		Source msgContent = msg.getSOAPPart().getContent();
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		StreamResult result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String requestXml = new String(bos.toByteArray());
		//System.out.println("xml:"+requestXml);
		LogUtils.debug("nature订单推送接口发送：" + requestXml);

		//添加Basic验证
		BindingProvider bp = (BindingProvider) dispatch;
		bp.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, username);
		bp.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, password);

		//5、通过Dispatch传递消息,会返回响应消息
		SOAPMessage response = dispatch.invoke(msg);
		response.writeTo(System.out);
		msgContent = response.getSOAPPart().getContent();
		bos = new ByteArrayOutputStream();
		result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String responseXml = new String(bos.toByteArray());
		//System.out.println("responseXml:"+responseXml);
		LogUtils.debug("nature订单推送接口返回：" + responseXml);

		//6、响应消息处理,将响应的消息转换为dom对象
		Document doc = response.getSOAPPart()
				.getEnvelope()
				.getBody()
				.extractContentAsDocument();
		String returnStatus = doc.getElementsByTagName("returnStatus")
				.item(0)
				.getTextContent();
		String returnMsg = "";
		if (returnStatus.equals("S")) {
			returnMsg = "S";
			
			String idStr = params.get("SOURCE_NUM").toString();
			Long id = Long.parseLong(idStr);
			String erpSn = doc.getElementsByTagName("HEADER_ID")
					.item(0)
					.getTextContent();
			ShippingService shippingService = SpringUtils
					.getBean("shippingServiceImpl", ShippingService.class);
			B2bReturnsService b2bReturnsService = SpringUtils
					.getBean("b2bReturnsServiceImpl", B2bReturnsService.class);
			String type = params.get("ORDER_TYPE").toString();
			if (type.equals("零售销售订单")) {
				Shipping shipping = shippingService.find(id);
				shipping.setErpSn(erpSn);
				shippingService.update(shipping);
				//物流接口
				if(!shipping.getSbu().getName().equals("壁纸")){
//					shippingService.saveIntfAtLogistics(shipping);
				}
			}
			else if (type.equals("零售退货订单")) {
				B2bReturns b2bReturns = b2bReturnsService.find(id);
				b2bReturns.setErpSn(erpSn);
				b2bReturnsService.update(b2bReturns);
				
				//物流接口
				if(!b2bReturns.getSbu().getName().equals("壁纸")){
//					b2bReturnsService.saveIntfAtLogistics(b2bReturns);
				}
			}
		}
		else {
			returnMsg = doc.getElementsByTagName("returnMsg")
					.item(0)
					.getTextContent();
		}
		Map<String, Object> resultData = new HashMap<String, Object>();
		resultData.put("returnMsg", returnMsg);
		resultData.put("resultXml", responseXml);
		return resultData;
	}

	public static void main(String[] args) throws Exception {

		HashMap<String, Object> params = new HashMap<String, Object>();
		params.put("SOURCE_CODE", "CRM");
		params.put("SOURCE_NUM", "233");
		params.put("CANCEL_DATE", DateUtil.convert(new Date()));
		params.put("CANCEL_REASON", "");
		params.put("ERP_USER_NAME", "");
		new NatureOrderCreate().createOrder(params);
	}
}
