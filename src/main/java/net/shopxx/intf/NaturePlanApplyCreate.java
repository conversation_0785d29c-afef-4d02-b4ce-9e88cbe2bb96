package net.shopxx.intf;

import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPMessage;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;

import org.w3c.dom.Document;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.LogUtils;

public class NaturePlanApplyCreate {

	private static String ns = "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_plan_soa_pkg/";
//	private static String wsdlUrl = "http://202.104.26.165:8082/api/v1/Erp/PlanOrder/Sync";//测试
//	private static String wsdlUrl = "http://202.104.26.170:7707/api/v1/Erp/PlanOrder/Sync";//测试
//	private static String wsdlUrl = "http://202.104.26.164:8082/api/v1/Erp/PlanOrder/Sync";//正式
//	private static String username = "link01";
//	private static String password = "h9b4xvNAslumcy1i";//测试
//	private static String password = "tzNUwOiRry3juNAR";//正式
	
	private static String wsdlUrl =  Global.getLoader().getProperty("dbintf.url")+"/api/v1/Erp/PlanOrder/Sync";
	private static String username = Global.getLoader().getProperty("dbintf.username");
	private static String password = Global.getLoader().getProperty("dbintf.password");

	public Map<String, Object> createPlanApply(Map<String, Object> params)
			throws Exception {

		//1、创建服务(Service)
		URL url = new URL(wsdlUrl);
		QName sname = new QName(ns, "SCUX_NATURE_PLAN_SOA_PKG_Service");
		Service service = Service.create(url, sname);
		//2、创建Dispatch
		Dispatch<SOAPMessage> dispatch = service.createDispatch(new QName(ns, "SCUX_NATURE_PLAN_SOA_PKG_Port"),
				SOAPMessage.class,Service.Mode.MESSAGE);
		//3、创建SOAPMessage
		SOAPMessage msg = MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL).createMessage();
		SOAPEnvelope envelope = msg.getSOAPPart().getEnvelope();
		//4、创建QName来指定消息中传递数据
		envelope.addNamespaceDeclaration("plan","http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_plan_soa_pkg/plan_order/");
		SOAPBody body = envelope.getBody();
		SOAPElement ele = body.addChildElement("InputParameters", "plan");
		// 传递参数 
		SOAPElement esbInfo = ele.addChildElement("esbInfo", "plan");
		esbInfo.addChildElement("instId", "plan").setValue(UUID.randomUUID().toString());
		esbInfo.addChildElement("requestTime", "plan").setValue(DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.addChildElement("attr1", "plan");
		esbInfo.addChildElement("attr2", "plan");
		esbInfo.addChildElement("attr3", "plan");

		SOAPElement queryInfo = ele.addChildElement("queryInfo", "plan");
		queryInfo.addChildElement("pageSize", "plan");
		queryInfo.addChildElement("currentPage", "plan");

		SOAPElement requestInfo = ele.addChildElement("requestInfo", "plan");
		SOAPElement headerRec = requestInfo.addChildElement("HEADER", "plan");
		SOAPElement lineTbl = requestInfo.addChildElement("LINE", "plan");

		//表头
		headerRec.addChildElement("YEAR_H", "plan").setValue(params.get("YEAR_H").toString());//计划订单所属年份
		headerRec.addChildElement("MONTH_H", "plan").setValue(params.get("MONTH_H").toString());//传计划订单所属月份
		headerRec.addChildElement("DOCUMENT_NUMBER", "plan").setValue(params.get("DOCUMENT_NUMBER").toString());//计划订单的单据号
		headerRec.addChildElement("SOURCE", "plan").setValue(params.get("SOURCE").toString());//来源默认“FORECAST”
		headerRec.addChildElement("AREA", "plan").setValue(params.get("AREA").toString());//机构名称
		headerRec.addChildElement("CUSTOMER_CODE", "plan").setValue(params.get("CUSTOMER_CODE").toString());//客户编码
		headerRec.addChildElement("CUSTOMER_NAME", "plan").setValue(params.get("CUSTOMER_NAME").toString());//客户名称
		headerRec.addChildElement("REMARKS", "plan").setValue(params.get("REMARKS").toString());//备注信息
		headerRec.addChildElement("ATTRIBUTE1", "plan").setValue(params.get("ATTRIBUTE1").toString());//创建人
        headerRec.addChildElement("ATTRIBUTE2", "plan").setValue(params.get("ATTRIBUTE2").toString());//经销商工程的项目编码
        headerRec.addChildElement("ATTRIBUTE3", "plan").setValue(params.get("ATTRIBUTE3").toString());//来源系统，来源AM/CRM必填
        
		//明细
		List<Map<String, Object>> lineParams = (List) params.get("LINE_TBL");
		for (Map<String, Object> lineParam : lineParams) {
			SOAPElement lineTblItem = lineTbl.addChildElement("LINE_ITEM","plan");
			Map<String, Object> lineData = (Map) lineParam.get("LINE_TBL_ITEM");
			lineTblItem.addChildElement("ITEM_CODE", "plan").setValue(lineData.get("ITEM_CODE").toString());//物料编码
			lineTblItem.addChildElement("DEMAND_QUANTITY", "plan").setValue(lineData.get("DEMAND_QUANTITY").toString());//计划订单的需求数量
			lineTblItem.addChildElement("DEMAND_DATE", "plan").setValue(lineData.get("DEMAND_DATE").toString());//计划订单的需求日期
			lineTblItem.addChildElement("ATTRIBUTE1", "plan").setValue(lineData.get("ATTRIBUTE1").toString());// 工厂（平台不填，总部必填
			lineTblItem.addChildElement("ATTRIBUTE2", "plan").setValue(lineData.get("ATTRIBUTE2").toString());//业务类型（经销商工程、经销商零售）
			lineTblItem.addChildElement("ATTRIBUTE3", "plan").setValue(lineData.get("ATTRIBUTE3").toString());//sbu
			lineTblItem.addChildElement("ATTRIBUTE4", "plan").setValue(lineData.get("ATTRIBUTE4").toString());//纸张数（待定））
			lineTblItem.addChildElement("ATTRIBUTE5", "plan").setValue(lineData.get("ATTRIBUTE5").toString());//含水率
		}


		TransformerFactory transformerFactory = TransformerFactory.newInstance();
		Transformer transformer = transformerFactory.newTransformer();
		Source msgContent = msg.getSOAPPart().getContent();
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		StreamResult result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String requestXml = new String(bos.toByteArray());
		LogUtils.debug("nature计划提报推送接口发送：" + requestXml);

		//添加Basic验证
		BindingProvider bp = (BindingProvider) dispatch;
		bp.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, username);
		bp.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, password);

		//5、通过Dispatch传递消息,会返回响应消息
		SOAPMessage response = dispatch.invoke(msg);
		msgContent = response.getSOAPPart().getContent();
		bos = new ByteArrayOutputStream();
		result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String responseXml = new String(bos.toByteArray());
		LogUtils.debug("nature计划提报推送接口返回：" + responseXml);

		//6、响应消息处理,将响应的消息转换为dom对象
		Document doc = response.getSOAPPart().getEnvelope().getBody().extractContentAsDocument();
		String returnStatus = doc.getElementsByTagName("returnStatus").item(0).getTextContent();
		String returnMsg = "";
		if (returnStatus.equals("S")) {
			returnMsg = "S";
		}else {
			returnMsg = doc.getElementsByTagName("returnMsg").item(0).getTextContent();
		}
		Map<String, Object> resultData = new HashMap<String, Object>();
		resultData.put("returnMsg", returnMsg);
		resultData.put("resultXml", responseXml);
		resultData.put("requestXml", requestXml);
		return resultData;
	}

	
}
