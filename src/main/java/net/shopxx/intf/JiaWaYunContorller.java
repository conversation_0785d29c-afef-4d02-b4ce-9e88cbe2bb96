package net.shopxx.intf;

import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.order.service.JiaWaYunHandleService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName : JaWaYunContorller
 * @Deseription : 家哇云信息回传
 * <AUTHOR> LanTian<PERSON>ong
 * @Date : 2020/12/16 16:04
 * @Version : 1.0
 **/
@Controller("intfJiaWaYunContorller")
@RequestMapping("/intf/jiaWaYun")
public class JiaWaYunContorller {
    private static String token = "95a1ad62c12ed0be044d5fb46cb77aac";

    @Resource(name = "jiaWaYunHandleServiceImpl")
    JiaWaYunHandleService jiaWaYunHandleService;

    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/jiaWaYunStockInOrOut", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sync(HttpServletRequest request, HttpServletResponse response){
        LogUtils.debug("========== 家哇云 ==========");
        ModelMap model = null;
        try {

            model = new ModelMap();
            String t = request.getHeader("Authorization");
            if (ConvertUtil.isEmpty(t) || !t.equals(token)) {
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
                LogUtils.debug("家哇云 同步返回：" + JsonUtils.toJson(model));
                return model;
            }
            String requestTime = DateUtil.convert(new Date(),
                    "yyyy-MM-dd HH:mm:ss.SSS");

            Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
            LogUtils.debug("家哇云同步接收：" + JsonUtils.toJson(map));

            Map<String, Object> requestInfo = (Map<String, Object>) map.get("requestInfo");
            Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
            Map<String, Object> esbInfo = new HashMap<String, Object>();
            if (requestInfo == null){
                esbInfo.put("returnStatus", "E");
                esbInfo.put("returnCode", "E");
                esbInfo.put("returnMsg", "数据格式有误-requestInfo为空");
                esbInfo.put("requestTime", requestTime);
                esbInfo.put("responseTime",
                        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
            }
            if (resbInfo == null) {
                esbInfo.put("returnStatus", "E");
                esbInfo.put("returnCode", "E0001");
                esbInfo.put("returnMsg", "esbInfo数据有误");
                esbInfo.put("requestTime", requestTime);
                esbInfo.put("responseTime",
                        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                esbInfo.put("attr1", "");
                esbInfo.put("attr2", "");
                esbInfo.put("attr3", "");
                model.put("esbInfo", esbInfo);
                LogUtils.debug("物流 同步返回：" + JsonUtils.toJson(model));
                return model;
            }
            String instId = resbInfo.get("instId") == null ? ""
                    : resbInfo.get("instId").toString();
            String rTime = resbInfo.get("requestTime") == null ? null
                    : resbInfo.get("requestTime").toString();
            if (ConvertUtil.isEmpty(rTime)) {
                esbInfo.put("instId", instId);
                esbInfo.put("returnStatus", "E");
                esbInfo.put("returnCode", "E0003");
                esbInfo.put("returnMsg", "调用接口时间有误");
                esbInfo.put("requestTime", requestTime);
                esbInfo.put("responseTime",
                        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                esbInfo.put("attr1", resbInfo.get("attr1") == null ? ""
                        : resbInfo.get("attr1"));
                esbInfo.put("attr2", resbInfo.get("attr2") == null ? ""
                        : resbInfo.get("attr2"));
                esbInfo.put("attr3", resbInfo.get("attr3") == null ? ""
                        : resbInfo.get("attr3"));
                model.put("esbInfo", esbInfo);
                LogUtils.debug("家哇云同步返回：" + JsonUtils.toJson(model));
                return model;
            }


            //逻辑处理

            String msg = "";

            try {
            msg = jiaWaYunHandleService.MessageBack(map);

                esbInfo.put("returnStatus", "S");
                esbInfo.put("returnCode", "S");
                esbInfo.put("returnMsg", "成功");
            }
            catch (Exception e) {
                System.out.println(e.toString());
//                String[] returnMsg = e.getMessage().split("-");
                esbInfo.put("returnStatus", "E");
//                esbInfo.put("returnCode", returnMsg[0]);
//                esbInfo.put("returnMsg", returnMsg[1]);
                esbInfo.put("returnCode", "E0003");
                esbInfo.put("returnMsg",e.getMessage());
            }
            esbInfo.put("instId", instId);
            esbInfo.put("requestTime", requestTime);
            esbInfo.put("responseTime",
                    DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
            esbInfo.put("attr1",
                    resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
            esbInfo.put("attr2",
                    resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
            esbInfo.put("attr3",
                    resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
            model.put("esbInfo", esbInfo);

            Map<String, Object> resultInfo = new HashMap<String, Object>();
            model.put("resultInfo", resultInfo);
            LogUtils.debug("家哇云 同步返回：" + JsonUtils.toJson(model));
        }
        catch (Exception e) {
            LogUtils.error("家哇云 ", e);
        }

        LogUtils.debug("========== 家哇云 ==========");
        return model;
    }

}
