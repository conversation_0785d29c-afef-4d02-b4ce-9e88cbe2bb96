package net.shopxx.intf;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.order.service.ShippingHandleService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Controller("intfOrderController")
@RequestMapping("/intf/order")
public class OrderController extends BaseController {

	@Resource(name = "shippingHandleServiceImpl")
	private ShippingHandleService shippingHandleService;

	private static String token = Global.getLoader().getProperty("synintf.token");

	/**
	 * 订单
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/receive", method = RequestMethod.POST)
	public @ResponseBody
	ModelMap receive(HttpServletRequest request, HttpServletResponse response) {

		LogUtils.info("========== 订单 ==========");
		ModelMap model = null;
		try {

//			A1_ServiceIntf_Config a1_ServiceIntf_Config = ServiceIntfConfigUtil.getServletConfig(request);
//			String token = a1_ServiceIntf_Config.getParam1();

			model = new ModelMap();
			String t = request.getHeader("Authorization");
			if (ConvertUtil.isEmpty(t) || !t.equals(token)) {
				response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
				LogUtils.info("实发数量回传返回：" + JsonUtils.toJson(model));
				return model;
			}
			String requestTime = DateUtil.convert(new Date(),
					"yyyy-MM-dd HH:mm:ss.SSS");
			Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
//			Map<String, Object> map = ServiceIntfConfigUtil.getServletRequestData(request);
			LogUtils.info("实发数量回传接收：" + JsonUtils.toJson(map));
			Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
			Map<String, Object> esbInfo = new HashMap<String, Object>();
			if (resbInfo == null) {
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0001");
				esbInfo.put("returnMsg", "esbInfo数据有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", "");
				esbInfo.put("attr2", "");
				esbInfo.put("attr3", "");
				model.put("esbInfo", esbInfo);
				LogUtils.info("实发数量回传返回：" + JsonUtils.toJson(model));
				return model;
			}
			String instId = resbInfo.get("instId") == null ? ""
					: resbInfo.get("instId").toString();
			String rTime = resbInfo.get("requestTime") == null ? null
					: resbInfo.get("requestTime").toString();
			if (ConvertUtil.isEmpty(rTime)) {
				esbInfo.put("instId", instId);
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0003");
				esbInfo.put("returnMsg", "调用接口时间有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", resbInfo.get("attr1") == null ? ""
						: resbInfo.get("attr1"));
				esbInfo.put("attr2", resbInfo.get("attr2") == null ? ""
						: resbInfo.get("attr2"));
				esbInfo.put("attr3", resbInfo.get("attr3") == null ? ""
						: resbInfo.get("attr3"));
				model.put("esbInfo", esbInfo);
				LogUtils.info("实发数量回传返回：" + JsonUtils.toJson(model));
				return model;
			}

			//逻辑处理
			try {
				String msg = shippingHandleService.QuantityBack(map);
				esbInfo.put("returnStatus", "S");
				esbInfo.put("returnCode", "A0001");
				esbInfo.put("returnMsg", "成功");
			}
			catch (Exception e) {
				if (e.toString().contains("-")) {
					String[] returnMsg = e.toString().split("-");
					esbInfo.put("returnStatus", "E");
					esbInfo.put("returnCode", returnMsg[0]);
					esbInfo.put("returnMsg", returnMsg[1]);
				}
				else {
					LogUtils.error("回传接口" + e);
					esbInfo.put("returnStatus", e.getMessage());
				}
			}

			esbInfo.put("instId", instId);
			esbInfo.put("requestTime", requestTime);
			esbInfo.put("responseTime",
					DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
			esbInfo.put("attr1",
					resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
			esbInfo.put("attr2",
					resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
			esbInfo.put("attr3",
					resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
			model.put("esbInfo", esbInfo);

			Map<String, Object> resultInfo = new HashMap<String, Object>();
			model.put("resultInfo", resultInfo);
			LogUtils.info("实发数量回传返回：" + JsonUtils.toJson(model));

		}
		catch (Exception e) {
			LogUtils.error("订单", e);
		}
		LogUtils.info("========== 订单 ==========");
		return model;
	}

//	public static void main(String[] args) {
//
//		Map<String, Object> map = new HashMap<String, Object>();
//		Map<String, Object> esbInfo = new HashMap<String, Object>();
//		esbInfo.put("instId", "abcdefg1234567");
//		esbInfo.put("requestTime", new SimpleDateFormat(
//				"yyyy-MM-dd HH:mm:ss:SSS").format(new Date()));
//		map.put("esbInfo", esbInfo);
//
//		Map<String, Object> resultInfo = new HashMap<String, Object>();
//		resultInfo.put("createDate", DateUtil.convert(new Date()));
//		resultInfo.put("creater", "程小刚");
//		resultInfo.put("checkDate", DateUtil.convert(new Date()));
//		resultInfo.put("checker", "程小刚");
//		resultInfo.put("status", "已审核");
//		resultInfo.put("shippingSn", "1810105");
//		resultInfo.put("shippingId", 35);
//		resultInfo.put("ebsOrderSn", "S51808215");
//		resultInfo.put("ebsTrackingNo", "S51808215");
//		resultInfo.put("returnSn", "28770");
//		resultInfo.put("sippingDate", DateUtil.convert(new Date()));
//		resultInfo.put("trackingNo", "S51808215");
//		resultInfo.put("remark", "");
//
//		List<Map<String, Object>> orderItems = new ArrayList<Map<String, Object>>();
//		Map<String, Object> orderItem = new HashMap<String, Object>();
//		orderItem.put("shippngItemNo", "S51808215");
//		orderItem.put("shippingItemId", 60);
//		orderItem.put("returnItemId", 602359);
//		orderItem.put("ebsTrackingItemId", 1);
//		orderItem.put("vonderCode", "8011401030020602");
//		orderItem.put("productGrade", "优等品");
//		orderItem.put("box", 33.93);
//		orderItem.put("piece", 100);
//		orderItem.put("square", 18.42);
//		orderItems.add(orderItem);
//		resultInfo.put("items", orderItems);
//		map.put("requestInfo", resultInfo);
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		String msg = HttpClientUtil.post("http://nature.etwowin.com.cn/intf/order/receive.jhtml",
//				JsonUtils.toJson(map),
//				headers);
//		System.out.println(msg);
//	}
}