package net.shopxx.intf.service;

import net.shopxx.act.entity.ActWf;

public interface OaToWfService {
	
	/**
	 *  推送系统流程
	 *  @param wf 流程实例
	 *  @param nodeName 0节点名称,1通知人员编码
	 *  @return
	 */
	String receiveRequestInfoByJson(ActWf wf, String[] nodeName);

	/**
	 * 推送待办
	 * @param wf 流程实例
	 * @param nodeName 0节点名称,1通知人员编码
	 * @return
	 */
	String receiveTodoRequestByJson(ActWf wf, String[] nodeName);

	/**
	 * 处理待办流程（变为已办）
	 * @param wf 流程实例
	 * @param nodeName 0节点名称,1通知人员编码
	 * @return
	 */
	String processDoneRequestByJson(ActWf wf, String[] nodeName);

	/**
	 * 处理办结流程（变为办结）
	 * @param wf 流程实例
	 * @param nodeName 0节点名称,1通知人员编码
	 * @return
	 */
	String processOverRequestByJson (ActWf wf, String[] nodeName);

	/**
	 * 删除待办流程
	 * @param wf 流程实例
	 * @return
	 */
	String deleteRequestInfoByJson(ActWf wf);

	/**
	 * 删除指定人员的待办流程
	 * @param wf 流程实例
	 * @param userId 人员的id
	 * @return
	 */
	String deleteUserRequestInfoByJson (ActWf wf, String userId);


	
}
