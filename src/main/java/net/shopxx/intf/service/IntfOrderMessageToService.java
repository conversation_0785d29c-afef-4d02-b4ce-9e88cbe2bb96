package net.shopxx.intf.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreAddress;
import net.shopxx.order.entity.MoveLibrary;
import net.shopxx.order.entity.OrderInvoice;
import net.shopxx.order.entity.PlanApply;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.AmShipping;


public interface IntfOrderMessageToService extends BaseService<A1_MessageTo> {

	public void saveHistory(A1_MessageTo intftable, String hResult,
			String field8, String hResultMsg);

	public void saveShippingIntf(Shipping shipping);
	
	public void saveLogisticsIntf(Shipping shipping,Integer flag);
	
	public void saveAmShippingIntf(AmShipping amShipping,Integer flag);
	
	public void saveLogisticsReturnIntf(B2bReturns b2bReturn,Integer flag);
	
	public void saveLogisticsMoveIntf(MoveLibrary moveLibrary,Integer flag);

	public void saveStoreIntf(Store store, StoreAddress storeAddress,
			Integer flag);
	
	
	public void pushLogisticsJwy(A1_MessageTo a1_MessageTo) throws Exception;
	
	
	public void pushLogisticsShippedJwy(A1_MessageTo a1_MessageTo) throws Exception;

	public void pushShippingToErp(A1_MessageTo a1_MessageTo) throws Exception;

	public void pushStoreToErp(A1_MessageTo a1_MessageTo) throws Exception;

	public List<Map<String, Object>> syncContract(
			Map<String, Object> requestData, String companytoken)
			throws Exception;

	public Page<Map<String, Object>> findProductPageForTJInft(Date begin_date,
			Date end_date, Long companyInfoId, Pageable pageable);

	public void IntfStoreMember();

	public void saveDepositRechargeCustomIntf(DepositRecharge depositrecharge);

	public void saveShippingCustomIntf(Shipping shipping);

	public void saveInvoiceCustomIntf(OrderInvoice orderInvoice);

	public void saveInvoiceCustomEbsIntf(OrderInvoice orderInvoice);

	public void saveReturnIntf(B2bReturns b2bReturns);

	public void savePlanApplyIntf(PlanApply planApply);

	public void pushPlanApplyToErp(A1_MessageTo a1_MessageTo) throws Exception;

	public String savePolicyIntf(DepositRecharge depositRecharge)
			throws Exception;

	public String savePolicyHaveInvoiceIntf(DepositRecharge depositRecharge)
			throws Exception;
	/*
	 * public void pushPolicyHaveInvoiceToErp(A1_MessageTo a1_MessageTo) throws
	 * Exception; public void pushPolicyToErp(A1_MessageTo a1_MessageTo) throws
	 * Exception;
	 */

}
