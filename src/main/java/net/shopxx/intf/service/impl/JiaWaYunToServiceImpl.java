package net.shopxx.intf.service.impl;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.A1_MessageToService;
import net.shopxx.intf.service.JiaWaYunToService;
import net.shopxx.member.entity.Store;
import net.shopxx.order.entity.*;
import net.shopxx.product.entity.Product;
import net.shopxx.stock.entity.Warehouse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName : JiaWaYunToServiceImpl
 * @Deseription : TODO
 * <AUTHOR> LanTianLong
 * @Date : 2020/12/17 15:14
 * @Version : 1.0
 * @remark 此类已失效
 **/
@Service("JiaWaYunToServiceImpl")
public class JiaWaYunToServiceImpl extends
        BaseServiceImpl<A1_MessageTo> implements JiaWaYunToService {
    private static String ssoUrl = Global.getLoader()
            .getProperty("dbintf.ssoUrl");
    private static String ssoUrlIntf = Global.getLoader()
            .getProperty("dbintf.ssoUrlIntf");
    private static String ssoKey = Global.getLoader()
            .getProperty("dbintf.ssoKey");
    private static final String[] IGNORE_PROPERTIES = new String[] { BaseEntity.ID_PROPERTY_NAME,
            BaseEntity.CREATE_DATE_PROPERTY_NAME,
            BaseEntity.MODIFY_DATE_PROPERTY_NAME };


    //private static String username = "link01"; //测试
    //private static String password = "7iURWx3JNDQFvhT7"; //测试

    //ESB的Basic验证
    private static String wsdlUrl =  Global.getLoader().getProperty("dbintf.url")+"/v1/nature/am/orders";
    private static String username = Global.getLoader().getProperty("dbintf.username");
    private static String password = Global.getLoader().getProperty("dbintf.password");

    @Resource(name = "a1_MessageToHServiceImpl")
    private A1_MessageToHService a1_MessageToHService;
    @Resource(name = "a1_MessageToServiceImpl")
    private A1_MessageToService a1_MessageToService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    public void saveHistory(A1_MessageTo intftable, String hResult,
                            String field8, String hResultMsg) {
        System.out.println("errorMsg:"+hResultMsg);
        A1_MessageToH intftableH = new A1_MessageToH();
        BeanUtils.copyProperties(intftable, intftableH, IGNORE_PROPERTIES); // 复制
        intftableH.sethResult(hResult);
        intftableH.sethResultMsg(hResultMsg);
        intftableH.setField8(field8);
        a1_MessageToHService.save(intftableH);
        this.delete(intftable.getId());

    }

    //家哇云传
    public void saveLogisticsIntf(Shipping shipping, Integer flag) {
        Warehouse warehouse = shipping.getWarehouse();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (warehouse.getErp_warehouse_code() == null) {
            ExceptionUtil.throwServiceException("仓库【"
                    + warehouse.getName()
                    + "】的外部编码不能为空");
        }
        Organization managementOrganization = warehouse.getManagementOrganization();
        if (managementOrganization == null) {
            ExceptionUtil.throwServiceException("仓库【"
                    + warehouse.getName()
                    + "】的经营组织不能为空");
        }
        else if (managementOrganization.getCode() == null) {
            ExceptionUtil.throwServiceException("仓库【"
                    + warehouse.getName()
                    + "】的经营组织编码不能为空");
        }

        SystemDict stockSystemDict = warehouse.getStockSystemDict();
        if (stockSystemDict == null) {
            ExceptionUtil.throwServiceException("仓库【"
                    + warehouse.getName()
                    + "】的库存组织不能为空");
        }
        else if (stockSystemDict.getRemark() == null) {
            ExceptionUtil.throwServiceException("系统词汇，库存组织【"
                    + stockSystemDict.getValue()
                    + "】的备注（ERP编号）不能为空");
        }

        Order mOrder = shipping.getShippingItems()
                .get(0)
                .getOrderItem()
                .getOrder();
        Store store = shipping.getStore();
        //获取当前订单财务审核人员（多个取最早那一个）





        Map<String, Object> requestMap = new HashMap<String, Object>();
        Map<String, Object> requestInfo = new HashMap<String, Object>();

        Map<String, Object> esbInfo = new HashMap<String, Object>();
        esbInfo.put("requestTime", DateUtil.convert(new Date(),
                "yyyy-MM-dd HH:mm:ss.SSS"));
        esbInfo.put("attr1", "");
        esbInfo.put("attr2", "");
        esbInfo.put("attr3", "");
        requestMap.put("esbInfo", esbInfo);

        Map<String, Object> queryInfo = new HashMap<String, Object>();
        queryInfo.put("pageSize", "");
        queryInfo.put("currentPage", "");
        requestMap.put("queryInfo", queryInfo);
        if(flag==0){
            requestInfo.put("shippingId", shipping.getId());// 头ID
            requestInfo.put("salesNo", shipping.getSn());// 发货单号(销售单号)
            requestInfo.put("customerName", store.getName());// 客户名称
            requestInfo.put("customerAlias", store.getAlias());// 客户别称
            requestInfo.put("type", "销售单");// 订单类型
            requestInfo.put("messageType","已登记");// 消息类型
            requestInfo.put("startTime",shipping.getCreateDate());// 订单日期
            requestInfo.put("deliveryType", shipping.getShippingMethod());// 发运方式
            if(shipping.getSmethod().equals("普通发运")){
                requestInfo.put("sendType", 1);//配送方式
            }else if(shipping.getSmethod().equals("暂缓发运")){
                requestInfo.put("sendType", 2);//配送方式
            }
            requestInfo.put("remarks", "");// 备注
            requestInfo.put("backupColumn1", "");// 备用1
            requestInfo.put("backupColumn2", "");// 备用2
            requestInfo.put("backupColumn3", "");// 备用3
            requestInfo.put("backupColumn4", "");// 备用4
            requestInfo.put("backupColumn5", "");// 备用5

            Map<String, Object> lineTblSender = new HashMap<String, Object>();


            lineTblSender.put("spWarehouseCode", warehouse.getErp_warehouse_code());
            lineTblSender.put("name", "");
            lineTblSender.put("mobile", "");
            lineTblSender.put("phone", "");
            lineTblSender.put("provinceName", "");
            lineTblSender.put("cityName", "");
            lineTblSender.put("districtName", "");
            lineTblSender.put("address", "");


            requestInfo.put("senderInfo", lineTblSender);

            Map<String, Object> lineTblReceiver = new HashMap<String, Object>();


            lineTblReceiver.put("name", shipping.getConsignee());
            lineTblReceiver.put("mobile", shipping.getPhone());
            lineTblReceiver.put("phone", "");


            //拆分省市区
            Area area = shipping.getArea();
            String districtName="";
            String cityName="";
            String provinceName="";

            if(area!=null && area.getParent().getParent()!=null){
                districtName=area.getName();
                cityName=area.getParent().getName();
                provinceName=area.getParent().getParent().getName();
            }else{
                cityName=area.getName();
                provinceName=area.getParent().getName();
            }

            lineTblReceiver.put("provinceName",provinceName );
            lineTblReceiver.put("cityName", cityName);
            lineTblReceiver.put("districtName",districtName );
            lineTblReceiver.put("detailAddress", shipping.getAddress());


            lineTblReceiver.put("spWarehouseCode", "");

            requestInfo.put("receiverInfo", lineTblReceiver);



        }else{
            requestInfo.put("salesNo", shipping.getSn());// 发货单号
            requestInfo.put("shippingId", shipping.getId());// 头ID
            requestInfo.put("deliveryType", shipping.getShippingMethod());// 发运方式
            requestInfo.put("type", "销售单");// 订单类型
            //由于整单作废SQL事务未提交状态未改变所以写上状态2
            if(flag==2){
                requestInfo.put("status", 2);//状态
            }else{
                requestInfo.put("status", shipping.getStatus());//状态
            }

            requestInfo.put("stockOutNo", shipping.getSn());// 家哇云出库单号
            requestInfo.put("subWarehouseID", warehouse.getErp_warehouse_code());// 子库库编码
            requestInfo.put("totalWeight", "");// 总重量
            requestInfo.put("fobAddrress", "");// fob地点
            requestInfo.put("contact", shipping.getConsignee());//联系人
            requestInfo.put("startTime", shipping.getErpDate());//实际出库时间
            if(shipping.getSmethod().equals("普通发运")){
                requestInfo.put("send_type", 1);//配送方式
            }else if(shipping.getSmethod().equals("暂缓发运")){
                requestInfo.put("send_type", 2);//配送方式
            }

            requestInfo.put("sale_org_name",shipping.getSaleOrg().getName());//机构
            requestInfo.put("shippingNote", "");//发运说明
            requestInfo.put("summary", "");//摘要
            requestInfo.put("remarks", "");// 备注
        }


        List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
        for (ShippingItem shippingItem : shipping.getShippingItems()) {
            Product product = shippingItem.getProduct();
            OrderItem orderItem = shippingItem.getOrderItem();
            String productGradeName = "";

            if (orderItem != null && orderItem.getProductLevel() != null) {
                if (!ConvertUtil.isEmpty(orderItem.getProductLevel().getValue())) {
                    productGradeName = orderItem.getProductLevel().getValue();
                }else{
                    ExceptionUtil.throwServiceException("产品【"+ shippingItem.getName()+ "】的物料等级不能为空");
                }
            }

            if (shippingItem.getVonderCode() == null) {
                ExceptionUtil.throwServiceException("产品【"
                        + shippingItem.getName()
                        + "】的物料编码不能为空");
            }


            //计算重量体积
            BigDecimal weight= new BigDecimal("0");
            BigDecimal voluem= new BigDecimal("0");


            if(flag==0){
                weight=(shippingItem.getQuantity()==null ? new BigDecimal("0") : shippingItem.getQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
                voluem=(shippingItem.getBoxQuantity()==null ? new BigDecimal("0") : shippingItem.getBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
            }else{
                weight=(shippingItem.getShippedQuantity()==null ? new BigDecimal("0") : shippingItem.getShippedQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
                voluem=(shippingItem.getShippedBoxQuantity()==null ? new BigDecimal("0") : shippingItem.getShippedBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
            }




            Map<String, Object> lineTblItem = new HashMap<String, Object>();

            lineTblItem.put("shippingItemId", shippingItem.getId());//行ID
            lineTblItem.put("category",product.getProductCategory().getName());//类别
            lineTblItem.put("cargNo", product.getVonderCode());// 货物编码
            lineTblItem.put("cargoName", product.getName());//货物名称
            lineTblItem.put("cargoDescription", product.getDescription());// 货物描述
            lineTblItem.put("model", product.getModel());// 型号
            lineTblItem.put("businessOrgName",shippingItem.getOrder().getOrganization().getName());//经营组织
            lineTblItem.put("grade", productGradeName);// 等级
            lineTblItem.put("colorNo", product.getWoodTypeOrColor()==null ? "" : product.getWoodTypeOrColor());// ⾊号
            lineTblItem.put("moistureContent",shippingItem.getMoistureContents());//含水率
            lineTblItem.put("newFlag","");//新旧标识
            lineTblItem.put("batch", shippingItem.getBatchEncoding());// 货物批次
            lineTblItem.put("storageLocation","");//库位
            lineTblItem.put("length",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⻓度
            lineTblItem.put("width",product.getErp_width()==null ? "" :product.getErp_width());//单件货物宽度
            lineTblItem.put("height",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⾼度
            lineTblItem.put("volume","");//体积
            lineTblItem.put("weight", weight);//单件货物重量
            lineTblItem.put("itemStatus", shippingItem.getStatus());//状态
            lineTblItem.put("remarks", shippingItem.getMemo()==null ? "" : shippingItem.getMemo());//备注
            lineTblItem.put("area",voluem);// 单件货物⾯积
            lineTblItem.put("price",shippingItem.getPrice());//货物单价
            if(flag==0){
                lineTblItem.put("num",shippingItem.getQuantity());//货物数量
                lineTblItem.put("aidedNum",shippingItem.getBranchQuantity());//辅数量（⽀数）
                lineTblItem.put("packageNum",shippingItem.getBoxQuantity());//货物件数
            }else{
                lineTblItem.put("num",shippingItem.getShippedQuantity());//货物数量
                lineTblItem.put("aidedNum",shippingItem.getShippedBranchQuantity());//辅数量（⽀数）
                lineTblItem.put("packageNum",shippingItem.getShippedBoxQuantity());//货物件数
            }

            lineTblItem.put("branch_per_box",product.getBranchPerBox());//每箱支数
            lineTblItem.put("per_box",product.getPerBox());// 每支单位数
            lineTblItem.put("per_branch",product.getPerBranch());//每箱单位数
            lineTblItem.put("unit",product.getUnit());//数量单位
            lineTblItem.put("org_id",warehouse.getStockSystemDict().getRemark());//库存组织

            lineTblItem.put("aidedUnit",product.getSpecTwo());//辅单位（个）

            lineTblItem.put("packageUnit","箱");//件数单位
            lineTblItem.put("oldSN","");//旧物料编码
            lineTblItem.put("backupColumn1","");//备用1
            lineTblItem.put("backupColumn2","");//备用2
            lineTblItem.put("backupColumn3","");//备用3
            lineTblItem.put("backupColumn4","");//备用4


            lineTbl.add(lineTblItem);


        }

        requestInfo.put("items", lineTbl);
        requestMap.put("requestInfo", requestInfo);

        /** 请求的接口数据 */
        String requestStr = JsonUtils.toJson(requestMap);

        Long companyInfoId = shipping.getCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);


        /** 写入接口表 */
        if(flag==0){
            String[] fields = new String[] { shipping.getSn() };
            a1_MessageToService.saveA1_MessageTo(15,
                    companyInfo.getUniqueIdentify(),
                    requestStr,
                    fields);
        }else{
            String[] fields = new String[] { shipping.getSn() };
            a1_MessageToService.saveA1_MessageTo(16,
                    companyInfo.getUniqueIdentify(),
                    requestStr,
                    fields);
        }





    }

    //推送移库单到家哇云
    public void saveLogisticsMoveIntf(MoveLibrary moveLibrary, Integer flag) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Map<String, Object> requestMap = new HashMap<String, Object>();
        Map<String, Object> requestInfo = new HashMap<String, Object>();

        Map<String, Object> esbInfo = new HashMap<String, Object>();
        esbInfo.put("requestTime", DateUtil.convert(new Date(),
                "yyyy-MM-dd HH:mm:ss.SSS"));
        esbInfo.put("attr1", "");
        esbInfo.put("attr2", "");
        esbInfo.put("attr3", "");
        requestMap.put("esbInfo", esbInfo);

        Map<String, Object> queryInfo = new HashMap<String, Object>();
        queryInfo.put("pageSize", "");
        queryInfo.put("currentPage", "");

        requestMap.put("queryInfo", queryInfo);


        if(flag==0){


            requestInfo.put("salesNo", moveLibrary.getSn());// 移库单号
            requestInfo.put("shippingId", moveLibrary.getId());// 头ID
            requestInfo.put("salesOrderNo","");// 销售单号
            requestInfo.put("customerName", "");// 客户名称
            requestInfo.put("customerAlias", "");// 客户别称
            requestInfo.put("type", "转库单");// 订单类型
            requestInfo.put("messageType","已登记");// 消息类型
            requestInfo.put("startTime",moveLibrary.getCreateDate());// 订单日期
            requestInfo.put("deliveryType", "");// 发运方式
//            if(shipping.getSmethod().equals("普通发运")){
//                requestInfo.put("sendType", 1);//配送方式
//            }else if(shipping.getSmethod().equals("暂缓发运")){
//                requestInfo.put("sendType", 2);//配送方式
//            }
            requestInfo.put("remarks", "");// 备注
            requestInfo.put("remarks", "");// 备注
            requestInfo.put("sale_org_name",moveLibrary.getSaleOrg().getName());//机构
            requestInfo.put("backupColumn1", "");// 备用1
            requestInfo.put("backupColumn2", "");// 备用2
            requestInfo.put("backupColumn3", "");// 备用3
            requestInfo.put("backupColumn4", "");// 备用4
            requestInfo.put("backupColumn5", "");// 备用5

            Map<String, Object> lineTblSender = new HashMap<String, Object>();


            lineTblSender.put("name", "");
            lineTblSender.put("mobile", "");
            lineTblSender.put("phone", "");
            lineTblSender.put("provinceName", "");
            lineTblSender.put("cityName", "");
            lineTblSender.put("districtName", "");
            lineTblSender.put("address", "");
            lineTblSender.put("spWarehouseCode", moveLibrary.getIssueWarehouse().getErp_warehouse_code());


            requestInfo.put("senderInfo", lineTblSender);

            Map<String, Object> lineTblReceiver = new HashMap<String, Object>();


            lineTblReceiver.put("provinceName","" );
            lineTblReceiver.put("cityName", "");
            lineTblReceiver.put("districtName","" );
            lineTblReceiver.put("detailAddress", "");
            lineTblReceiver.put("spWarehouseCode", moveLibrary.getReceiveWarehouse().getErp_warehouse_code());

            requestInfo.put("receiverInfo", lineTblReceiver);
        }else{
            requestInfo.put("salesNo", moveLibrary.getSn());// 发货单号
            requestInfo.put("shippingId", moveLibrary.getId());// 头ID
            requestInfo.put("type", "转库单");// 订单类型
            requestInfo.put("stockOutNo", moveLibrary.getSn());// 家哇云出库单号
            requestInfo.put("subWarehouseID", moveLibrary.getIssueWarehouse().getErp_warehouse_code());// 子库编码
            requestInfo.put("totalWeight", "");// 总重量
            requestInfo.put("fobAddrress", "");// fob地点
            requestInfo.put("contact", "");//联系人
            requestInfo.put("startTime", moveLibrary.getIssueDate());//实际出库时间
            requestInfo.put("sale_org_name",moveLibrary.getSaleOrg().getName());//机构
            requestInfo.put("shippingMethod", "汽运");//发运方式
            requestInfo.put("shippingNote", "");//发运说明
            requestInfo.put("summary", "");//摘要
            requestInfo.put("remarks", "");// 备注
        }



        List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
        for (MoveLibraryItem moveLibraryItem : moveLibrary.getMoveLibraryItems()) {
            Product product = moveLibraryItem.getProduct();

            String productGradeName = "";

            if (moveLibraryItem != null && moveLibraryItem.getProductLevel() != null) {
                if (!ConvertUtil.isEmpty(moveLibraryItem.getProductLevel().getValue())) {
                    productGradeName = moveLibraryItem.getProductLevel().getValue();
                }else {
                    ExceptionUtil.throwServiceException("产品【"+ product.getName()+ "】的物料等级不能为空");
                }
            }

            if (moveLibraryItem.getVonderCode() == null) {
                ExceptionUtil.throwServiceException("产品【"
                        + product.getName()
                        + "】的物料编码不能为空");
            }


            //计算重量体积
            BigDecimal weight= new BigDecimal("0");
            BigDecimal voluem= new BigDecimal("0");


            if(flag==0){
                weight=(moveLibraryItem.getQuantity()==null ? new BigDecimal("0") : moveLibraryItem.getQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
                voluem=(moveLibraryItem.getBoxQuantity()==null ? new BigDecimal("0") : moveLibraryItem.getBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
            }else{
                weight=(moveLibraryItem.getIssueQuantity()==null ? new BigDecimal("0") : moveLibraryItem.getIssueQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
                voluem=(moveLibraryItem.getIssueBoxQuantity()==null ? new BigDecimal("0") : moveLibraryItem.getIssueBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
            }






            Map<String, Object> lineTblItem = new HashMap<String, Object>();


            lineTblItem.put("cargoName", product.getName());//货物名称
            lineTblItem.put("shippingItemId", moveLibraryItem.getId());//行ID
            lineTblItem.put("cargoDescription", product.getDescription());// 货物描述
            lineTblItem.put("model", product.getModel());// 型号
            lineTblItem.put("cargo_no", product.getVonderCode());// 编码
            lineTblItem.put("grade", productGradeName);// 等级
            lineTblItem.put("colorNo", product.getWoodTypeOrColor()==null ? "" : product.getWoodTypeOrColor());// ⾊号
            lineTblItem.put("remarks", moveLibraryItem.getRemarks()==null ? "" : moveLibraryItem.getRemarks());//备注
            lineTblItem.put("batch", "");// 货物批次
            lineTblItem.put("area",voluem);// 单件货物⾯积
            lineTblItem.put("weight", weight);//单件货物重量
            lineTblItem.put("length",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⻓度
            lineTblItem.put("width",product.getErp_width()==null ? "" :product.getErp_width());//单件货物宽度
            lineTblItem.put("height",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⾼度
            lineTblItem.put("price","");//货物单价
            if(flag==0){
                lineTblItem.put("num",moveLibraryItem.getQuantity());//货物数量
                lineTblItem.put("aidedNum",moveLibraryItem.getBranchQuantity());//辅数量（⽀数）
                lineTblItem.put("packageNum",moveLibraryItem.getBoxQuantity());//货物件数
            }else{
                lineTblItem.put("num",moveLibraryItem.getIssueQuantity());//货物数量
                lineTblItem.put("aidedNum",moveLibraryItem.getIssueBranchQuantity());//辅数量（⽀数）
                lineTblItem.put("packageNum",moveLibraryItem.getIssueBoxQuantity());//货物件数
            }


            lineTblItem.put("org_id",moveLibrary.getIssueWarehouse().getStockSystemDict().getRemark());//库存组织
            lineTblItem.put("branch_per_box",product.getBranchPerBox());//每箱支数
            lineTblItem.put("per_box",product.getPerBox());// 每支单位数
            lineTblItem.put("per_branch",product.getPerBranch());//每箱单位数
            lineTblItem.put("aidedUnit",product.getSpecTwo());//辅单位（个）
            lineTblItem.put("packageUnit","箱");//件数单位
            lineTblItem.put("category",product.getProductCategory().getName());//分类
            lineTblItem.put("oldSN","");//旧物料编码
            lineTblItem.put("backupColumn1","");//备用1
            lineTblItem.put("backupColumn2","");//备用2
            lineTblItem.put("backupColumn3","");//备用3
            lineTblItem.put("backupColumn4","");//备用4
            lineTblItem.put("unit",product.getUnit());//数量单位
            lineTblItem.put("erpMemo",moveLibraryItem.getErpMemo());//ERP备注

            lineTbl.add(lineTblItem);


        }

        requestInfo.put("items", lineTbl);
        requestMap.put("requestInfo", requestInfo);

        /** 请求的接口数据 */
        String requestStr = JsonUtils.toJson(requestMap);

        Long companyInfoId = moveLibrary.getCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

        if(flag==0){
            String[] fields = new String[] { moveLibrary.getSn() };
            a1_MessageToService.saveA1_MessageTo(17,
                    companyInfo.getUniqueIdentify(),
                    requestStr,
                    fields);
        }else{
            String[] fields = new String[] { moveLibrary.getSn() };
            a1_MessageToService.saveA1_MessageTo(18,
                    companyInfo.getUniqueIdentify(),
                    requestStr,
                    fields);
        }









    }

    //家哇云传
    public void saveLogisticsReturnIntf(B2bReturns b2bReturn, Integer flag) {
        Warehouse warehouse = b2bReturn.getWarehouse();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");



        if (warehouse.getErp_warehouse_code() == null) {
            ExceptionUtil.throwServiceException("仓库【"
                    + warehouse.getName()
                    + "】的外部编码不能为空");
        }
        Organization managementOrganization = warehouse.getManagementOrganization();
        if (managementOrganization == null) {
            ExceptionUtil.throwServiceException("仓库【"
                    + warehouse.getName()
                    + "】的经营组织不能为空");
        }
        else if (managementOrganization.getCode() == null) {
            ExceptionUtil.throwServiceException("仓库【"
                    + warehouse.getName()
                    + "】的经营组织编码不能为空");
        }

        SystemDict stockSystemDict = warehouse.getStockSystemDict();
        if (stockSystemDict == null) {
            ExceptionUtil.throwServiceException("仓库【"
                    + warehouse.getName()
                    + "】的库存组织不能为空");
        }
        else if (stockSystemDict.getRemark() == null) {
            ExceptionUtil.throwServiceException("系统词汇，库存组织【"
                    + stockSystemDict.getValue()
                    + "】的备注（ERP编号）不能为空");
        }


        Store store = b2bReturn.getStore();




        Map<String, Object> requestMap = new HashMap<String, Object>();
        Map<String, Object> requestInfo = new HashMap<String, Object>();

        Map<String, Object> esbInfo = new HashMap<String, Object>();
        esbInfo.put("requestTime", DateUtil.convert(new Date(),
                "yyyy-MM-dd HH:mm:ss.SSS"));
        esbInfo.put("attr1", "");
        esbInfo.put("attr2", "");
        esbInfo.put("attr3", "");
        requestMap.put("esbInfo", esbInfo);

        Map<String, Object> queryInfo = new HashMap<String, Object>();
        queryInfo.put("pageSize", "");
        queryInfo.put("currentPage", "");

        requestMap.put("queryInfo", queryInfo);


        if(flag==0){
            requestInfo.put("salesNo", b2bReturn.getSn());// 发货单号
            requestInfo.put("shippingId", b2bReturn.getId());// 头ID
            requestInfo.put("customerOrderNo","");// 客户订单号
            requestInfo.put("salesOrderNo","");// 销售单号
            requestInfo.put("erpSn",b2bReturn.getErpSn());// 销售单号
            requestInfo.put("estimatedPickupTime","");// 预计揽货时间
            requestInfo.put("delivery_type", "");// 配送方式

            requestInfo.put("messageType", "");// 状态
            requestInfo.put("needLogisticsScheduler", "");// 是否需要物流调度协调
            requestInfo.put("customerName", store.getName());// 客户名称
            requestInfo.put("customerAlias", store.getAlias());// 客户别称
            requestInfo.put("division","地板事业总部");// 事业部
            requestInfo.put("type", "退货单");// 订单类型
            requestInfo.put("totalPrice",b2bReturn.getAmount());//总售价
            requestInfo.put("totalWeight","");//总重量
            requestInfo.put("startTime",b2bReturn.getCreateDate());// 订单日期
            requestInfo.put("messageType","已登记");// 消息类型
            requestInfo.put("sale_org_name",b2bReturn.getSaleOrg().getName());//机构
            requestInfo.put("projectNo", "");// 项⽬号
            requestInfo.put("projectName", "");// 项⽬名称
            requestInfo.put("remarks", "");// 备注
            requestInfo.put("backupColumn1", "");// 备用1
            requestInfo.put("backupColumn2", "");// 备用2
            requestInfo.put("backupColumn3", "");// 备用3
            requestInfo.put("backupColumn4", "");// 备用4
            requestInfo.put("backupColumn5", "");// 备用5

            if(b2bReturn.getSmethod().equals("普通发运")){
                requestInfo.put("send_type", 1);//配送方式
            }else if(b2bReturn.getSmethod().equals("暂缓发运")){
                requestInfo.put("send_type", 2);//配送方式
            }

            Map<String, Object> lineTblSender = new HashMap<String, Object>();


            lineTblSender.put("name", "");
            lineTblSender.put("mobile", "");
            lineTblSender.put("phone", "");
            lineTblSender.put("provinceName", "");
            lineTblSender.put("cityName", "");
            lineTblSender.put("districtName", "");
            lineTblSender.put("address", "");
            lineTblSender.put("spWarehouseCode", warehouse.getErp_warehouse_code());


            requestInfo.put("receiverInfo", lineTblSender);

            Map<String, Object> lineTblReceiver = new HashMap<String, Object>();


            lineTblReceiver.put("name", b2bReturn.getConsignee());
            lineTblReceiver.put("mobile", b2bReturn.getMobile());
            lineTblReceiver.put("phone", "");


            //拆分省市区
            Area area = b2bReturn.getArea();
            String districtName="";
            String cityName="";
            String provinceName="";

            if(area!=null && area.getParent().getParent()!=null){
                districtName=area.getName();
                cityName=area.getParent().getName();
                provinceName=area.getParent().getParent().getName();
            }else{
                cityName=area.getName();
                provinceName=area.getParent().getName();
            }

            lineTblReceiver.put("provinceName",provinceName );
            lineTblReceiver.put("cityName", cityName);
            lineTblReceiver.put("districtName",districtName );
            lineTblReceiver.put("detailAddress", b2bReturn.getAddress());


            lineTblReceiver.put("spWarehouseCode", "");

            requestInfo.put("senderInfo", lineTblReceiver);



        }else{
            requestInfo.put("salesNo", b2bReturn.getSn());// 发货单号
            requestInfo.put("shippingId", b2bReturn.getId());// 头ID
            requestInfo.put("delivery_type", "");// 配送方式
            requestInfo.put("type", "退货单");// 订单类型
            //由于整单作废SQL事务未提交状态未改变所以写上状态2


            requestInfo.put("erpSn", b2bReturn.getErpSn());// erp单号
            requestInfo.put("stockOutNo", b2bReturn.getSn());// 家哇云出库单号
            requestInfo.put("subWarehouseID", warehouse.getErp_warehouse_code());// 子库库编码
            requestInfo.put("totalWeight", "");// 总重量
            requestInfo.put("fobAddrress", "");// fob地点
            requestInfo.put("contact", b2bReturn.getConsignee());//联系人
            requestInfo.put("startTime", b2bReturn.getErpDate());//实际出库时间
            requestInfo.put("sale_org_name",b2bReturn.getSaleOrg().getName());//机构
            requestInfo.put("shippingMethod", "");//发运方式
            requestInfo.put("shippingNote", "");//发运说明
            requestInfo.put("summary", "");//摘要
            requestInfo.put("remarks", "");// 备注
            if(b2bReturn.getSmethod().equals("普通发运")){
                requestInfo.put("send_type", 1);//配送方式
            }else if(b2bReturn.getSmethod().equals("暂缓发运")){
                requestInfo.put("send_type", 2);//配送方式
            }
        }

        List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
        for (B2bReturnsItem b2bReturnsItem : b2bReturn.getB2bReturnsItems()) {
            Product product = b2bReturnsItem.getProduct();
            String productGradeName = "";
            if (b2bReturnsItem.getProductLevel() != null) {
                if (!ConvertUtil.isEmpty(b2bReturnsItem.getProductLevel().getValue())) {
                    productGradeName = b2bReturnsItem.getProductLevel().getValue();
                }else {
                    ExceptionUtil.throwServiceException("产品【"+ b2bReturnsItem.getName()+ "】的物料等级不能为空");
                }
            }

            if (b2bReturnsItem.getVonderCode() == null) {
                ExceptionUtil.throwServiceException("产品【"
                        + b2bReturnsItem.getName()
                        + "】的物料编码不能为空");
            }


            //计算重量体积
            BigDecimal weight= new BigDecimal("0");
            BigDecimal voluem= new BigDecimal("0");


            if(flag==0){
                weight=(b2bReturnsItem.getQuantity()==null ? new BigDecimal("0") : b2bReturnsItem.getQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
                voluem=(b2bReturnsItem.getBoxQuantity()==null ? new BigDecimal("0") : b2bReturnsItem.getBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
            }else{
                weight=(b2bReturnsItem.getReturnedQuantity()==null ? new BigDecimal("0") : b2bReturnsItem.getReturnedQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
                voluem=(b2bReturnsItem.getReturnedBoxQuantity()==null ? new BigDecimal("0") : b2bReturnsItem.getReturnedBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
            }




            Map<String, Object> lineTblItem = new HashMap<String, Object>();

            if(b2bReturnsItem.getShippingItem()!=null){
                requestInfo.put("source_id", b2bReturnsItem.getShippingItem().getShipping().getId());// 来源ID
                requestInfo.put("source_item_id", b2bReturnsItem.getShippingItem().getId());// 来源行ID
                requestInfo.put("source_sn", b2bReturnsItem.getShippingItem().getShipping().getErpSn());// 来源单号
            }


            lineTblItem.put("itemStatus", "");//状态
            lineTblItem.put("cargoName", product.getName());//货物名称
            lineTblItem.put("shippingItemId", b2bReturnsItem.getId());//行ID
            lineTblItem.put("cargoDescription", product.getDescription());// 货物描述
            lineTblItem.put("model", product.getModel());// 型号
            lineTblItem.put("cargo_no", product.getVonderCode());// 编码
            lineTblItem.put("grade", productGradeName);// 等级
            lineTblItem.put("colorNo", product.getWoodTypeOrColor()==null ? "" : product.getWoodTypeOrColor());// ⾊号
            lineTblItem.put("remarks", b2bReturnsItem.getMemo()==null ? "" : b2bReturnsItem.getMemo());//备注
            lineTblItem.put("batch", "");// 货物批次
            lineTblItem.put("area",voluem);// 单件货物⾯积
            lineTblItem.put("weight", weight);//单件货物重量
            lineTblItem.put("length",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⻓度
            lineTblItem.put("width",product.getErp_width()==null ? "" :product.getErp_width());//单件货物宽度
            lineTblItem.put("height",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⾼度
            lineTblItem.put("price",b2bReturnsItem.getPrice());//货物单价
            if(flag==0){
                lineTblItem.put("num",b2bReturnsItem.getQuantity());//货物数量
                lineTblItem.put("aidedNum",b2bReturnsItem.getBranchQuantity());//辅数量（⽀数）
                lineTblItem.put("packageNum",b2bReturnsItem.getBoxQuantity());//货物件数
            }else{
                lineTblItem.put("num",b2bReturnsItem.getQuantity());//货物数量
                lineTblItem.put("aidedNum",b2bReturnsItem.getReturnedBranchQuantity());//辅数量（⽀数）
                lineTblItem.put("packageNum",b2bReturnsItem.getReturnedBoxQuantity());//货物件数
            }

            lineTblItem.put("branch_per_box",product.getBranchPerBox());//每箱支数
            lineTblItem.put("per_box",product.getPerBox());// 每支单位数
            lineTblItem.put("per_branch",product.getPerBranch());//每箱单位数
            if(b2bReturnsItem.getShippingItem()!=null){
                lineTblItem.put("source_item_id",b2bReturnsItem.getShippingItem().getId());//发货单行ID
            }
            lineTblItem.put("category",product.getProductCategory().getName());//分类
            lineTblItem.put("unit",product.getUnit());//数量单位


            lineTblItem.put("aidedUnit",product.getSpecTwo());//辅单位（个）
            lineTblItem.put("org_id",warehouse.getStockSystemDict().getRemark());//库存组织
            lineTblItem.put("packageUnit","箱");//件数单位
            lineTblItem.put("oldSN","");//旧物料编码
            lineTblItem.put("backupColumn1","");//备用1
            lineTblItem.put("backupColumn2","");//备用2
            lineTblItem.put("backupColumn3","");//备用3
            lineTblItem.put("backupColumn4","");//备用4


            lineTbl.add(lineTblItem);


        }

        requestInfo.put("items", lineTbl);
        requestMap.put("requestInfo", requestInfo);

        /** 请求的接口数据 */
        String requestStr = JsonUtils.toJson(requestMap);

        Long companyInfoId = b2bReturn.getCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);


        /** 写入接口表 */
        if(flag==0){
            String[] fields = new String[] { b2bReturn.getSn() };
            a1_MessageToService.saveA1_MessageTo(19,
                    companyInfo.getUniqueIdentify(),
                    requestStr,
                    fields);
        }else{
            String[] fields = new String[] { b2bReturn.getSn() };
            a1_MessageToService.saveA1_MessageTo(20,
                    companyInfo.getUniqueIdentify(),
                    requestStr,
                    fields);
        }





    }

}
