package net.shopxx.intf.service.impl;

import javax.annotation.Resource;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.base.core.exception.IllegalDataException;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.A1_MessageToService;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 接口客户端保存数据到数据库
 */
@Service("a1_MessageToServiceImpl")
public class A1_MessageToServiceImpl extends BaseServiceImpl<A1_MessageTo>
		implements A1_MessageToService {

	private static final String[] IGNORE_PROPERTIES = new String[] { BaseEntity.ID_PROPERTY_NAME,
			BaseEntity.CREATE_DATE_PROPERTY_NAME,
			BaseEntity.MODIFY_DATE_PROPERTY_NAME };

	@Resource(name = "a1_MessageToHServiceImpl")
	private A1_MessageToHService a1_MessageToHService;

	@Transactional
	public A1_MessageTo saveA1_MessageTo(int type, String companytoken, String data,
			String[] fields) {

		A1_MessageTo intftable = new A1_MessageTo();
		this.setIntfTableParam(intftable, type, companytoken, data, fields);
		this.save(intftable);
		return intftable; 
	}

	public void saveHistory(A1_MessageTo intftable, String hResult,
			String hResultMsg) {

		A1_MessageToH intftableH = new A1_MessageToH();
		BeanUtils.copyProperties(intftable, intftableH, IGNORE_PROPERTIES); // 复制
		intftableH.sethResult(hResult);
		intftableH.sethResultMsg(hResultMsg);
		a1_MessageToHService.save(intftableH);
		this.delete(intftable.getId());

	}

	public void setIntfTableParam(A1_MessageTo bit, int type,
			String companytoken, String data, String[] fields) {

		if (ConvertUtil.isEmpty(type)
				|| ConvertUtil.isEmpty(data)
				|| ConvertUtil.isEmpty(companytoken))
			throw new IllegalDataException("value is empty!");

		bit.setType(type);
		bit.setCompanytoken(companytoken);
		bit.setData(data);
		if (fields != null && fields.length > 0) {
			for (int i = 0; i < fields.length; i++) {
				if (i == 0)
					bit.setField1(fields[i]);
				else if (i == 1)
					bit.setField2(fields[i]);
				else if (i == 2)
					bit.setField3(fields[i]);
				else if (i == 3)
					bit.setField4(fields[i]);
				else if (i == 4)
					bit.setField5(fields[i]);
				else if (i == 5)
					bit.setField6(fields[i]);
				else if (i == 6)
					bit.setField7(fields[i]);
				else if (i == 7) bit.setField8(fields[i]);
			}
		}
	}
}
