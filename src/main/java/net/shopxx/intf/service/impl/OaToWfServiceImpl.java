package net.shopxx.intf.service.impl;

import net.shopxx.act.entity.ActWf;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.intf.NatureOaWf;
import net.shopxx.intf.dao.Oa_WfModelDao;
import net.shopxx.intf.entity.*;
import net.shopxx.intf.service.OaToWfService;
import net.shopxx.order.entity.PriceApply;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service("oaToWfServiceImpl")
public class OaToWfServiceImpl implements OaToWfService {

	@Resource(name = "oa_WfModelDao")
	private Oa_WfModelDao oa_WfModelDao;

	private static final String SYSTEM_CODE = "LINKAM";

	private static final String OA_PUBLIC_ACCOUNT_NUMBER = Global.getLoader()
            .getProperty("oaintf.oaAccount");
	
	// oa单点返回链接
	private static final String OA_SSO_BACK_URL = "/intf/loginOa/login.jhtml?url=%s.%s.%s.pc";
	// oa单点返回链接H5
	private static final String OA_SSO_BACK_H5_URL = "/intf/loginOa/login.jhtml?url=%s.%s.%s.app";

	// false 关闭 true 开启
    Boolean isOa = true;

	@Override
	public String receiveRequestInfoByJson(ActWf wf, String[] nodeName) {
	    if(!isOa){
	        return "";
        }
		String msg = null;
		try {
			String[] model = oa_WfModelDao.findWfModel(wf.getModelId());
			OaReceiveRequestInfoByJson rribj = new OaReceiveRequestInfoByJson();
			rribj.setFlowid(wf.getId().toString());
//			rribj.setRequestname(model[0]+"【创建人："+wf.getStartor()==null?"":wf.getStartor().getName()+"】");
            if (nodeName[2]!=null&&nodeName[3]!=null){ //3sbu 4 单号
                rribj.setRequestname(nodeName[2]+model[0]+" "+nodeName[3]);
            }else {
                rribj.setRequestname(model[0]);
            }
			rribj.setWorkflowname(model[1]);
			rribj.setNodename(nodeName[0]);
			rribj.setPcurl(String.format(OA_SSO_BACK_URL, nodeName[1], wf.getObjType(), wf.getObjId().toString()));
			rribj.setAppurl(String.format(OA_SSO_BACK_H5_URL, nodeName[1], wf.getObjType(), wf.getId().toString()));
			rribj.setIsremark("0");
			rribj.setViewtype("0");
			rribj.setCreator(wf.getbCreater());
			rribj.setCreatedatetime(wf.getCreateDate());
			rribj.setReceiver(nodeName[1]);
			rribj.setReceivedatetime(new Date());
			rribj.setSyscode(SYSTEM_CODE);
			msg = NatureOaWf.sendFromToOas(JsonUtils.toJson(rribj), OaReceiveRequestInfoByJson.URL);
			System.out.println("msg:"+msg);
		} catch (Exception e) {
			LogUtils.error(e);
		}
		if (msg != null) {
			String c = msg.split(",")[0];
			String s = msg.split(",")[1];
			if ("0".equals(s)) {
				if (c.contains("检测接收人不存在")) {
					LogUtils.info("检测到当前节点没有对应接收人推送公共帐号");
					receiveRequestInfoByJson(wf, new String[] { nodeName[0], OA_PUBLIC_ACCOUNT_NUMBER });
				} else {
					ExceptionUtil.throwServiceException("操作失败:" + c + "!");
				}
			}
		} else {
			ExceptionUtil.throwServiceException("操作失败！");
		}
		return msg;
	}

	@Override
	public String receiveTodoRequestByJson(ActWf wf, String[] nodeName) {
		if(!isOa){
			return "";
		}
		String msg = null;
		try {
			String[] model = oa_WfModelDao.findWfModel(wf.getModelId());
			OaReceiveTodoRequestByJson rtrbj = new OaReceiveTodoRequestByJson();
			rtrbj.setFlowid(wf.getId().toString());
            if (nodeName[2]!=null&&nodeName[3]!=null){ //2sbu 3单号
                rtrbj.setRequestname(nodeName[2]+model[0]+" "+nodeName[3]);
            }else {
                rtrbj.setRequestname(model[0]);
            }
			rtrbj.setWorkflowname(model[1]);
			rtrbj.setNodename(nodeName[0]);
			rtrbj.setPcurl(String.format(OA_SSO_BACK_URL, nodeName[1], wf.getObjType(), wf.getObjId().toString()));
			rtrbj.setAppurl(String.format(OA_SSO_BACK_H5_URL, nodeName[1], wf.getObjType(), wf.getId().toString()));
//			rtrbj.setAppurl("");
			rtrbj.setCreator(wf.getbCreater());
			rtrbj.setCreatedatetime(wf.getCreateDate());
			rtrbj.setReceiver(nodeName[1]);
			rtrbj.setReceivedatetime(new Date());
			rtrbj.setSyscode(SYSTEM_CODE);
			msg = NatureOaWf.sendFromToOas(JsonUtils.toJson(rtrbj), OaReceiveTodoRequestByJson.URL);
		} catch (Exception e) {
			LogUtils.error(e);
		}
		if (msg != null) {
			String c = msg.split(",")[0];
			String s = msg.split(",")[1];
			if ("0".equals(s)) {
				if (c.contains("检测接收人不存在")) {
					LogUtils.info("检测到当前节点没有对应接收人推送公共帐号");
					receiveTodoRequestByJson(wf, new String[] { nodeName[0], OA_PUBLIC_ACCOUNT_NUMBER });
				} else {
					ExceptionUtil.throwServiceException("操作失败:" + c + "!");
				}
			}
		} else {
			ExceptionUtil.throwServiceException("操作失败！");
		}
		return msg;
	}

	@Override
	public String processDoneRequestByJson(ActWf wf, String[] nodeName) {
		if(!isOa){
			return "";
		}
		String msg = null;
		try {
			String[] model = oa_WfModelDao.findWfModel(wf.getModelId());
			OaProcessDoneRequestByJson pdrbj = new OaProcessDoneRequestByJson();
			pdrbj.setFlowid(wf.getId().toString());
			// 标题
            if (nodeName[2]!=null&&nodeName[3]!=null){ //2sbu 3单号
                pdrbj.setRequestname(nodeName[2]+model[0]+" "+nodeName[3]);
            }else {
                pdrbj.setRequestname(model[0]);
            }
			pdrbj.setWorkflowname(model[1]);// 流程类型名称
			pdrbj.setNodename(nodeName[0]);
			pdrbj.setReceiver(nodeName[1]);// 接收人
			pdrbj.setSyscode(SYSTEM_CODE);
			msg = NatureOaWf.sendFromToOas(JsonUtils.toJson(pdrbj), OaProcessDoneRequestByJson.URL);
		} catch (Exception e) {
			LogUtils.error(e);
		}
		if (msg != null) {
			String c = msg.split(",")[0];
			String s = msg.split(",")[1];
			if ("0".equals(s)) {
				if (c.contains("检测接收人不存在")) {
					LogUtils.info("检测到当前节点没有对应接收人推送公共帐号");
					processDoneRequestByJson(wf, new String[] { nodeName[0], OA_PUBLIC_ACCOUNT_NUMBER });
				} else {
					ExceptionUtil.throwServiceException("操作失败:" + c + "!");
				}
			}
		} else {
			ExceptionUtil.throwServiceException("操作失败！");
		}
		return msg;
	}

	@Override
	public String processOverRequestByJson(ActWf wf, String[] nodeName) {
		if(!isOa){
			return "";
		}
		String msg = null;
		String[] model = oa_WfModelDao.findWfModel(wf.getModelId());
		OaProcessOverRequestByJson porbj = new OaProcessOverRequestByJson();
		porbj.setFlowid(wf.getId().toString());
		porbj.setNodename("");
		porbj.setReceiver(nodeName[1]);
        // 标题
        if (nodeName[2]!=null&&nodeName[3]!=null){ //2sbu 3单号
            porbj.setRequestname(nodeName[2]+model[0]+" "+nodeName[3]);
        }else {
            porbj.setRequestname(model[0]);
        }
		porbj.setWorkflowname(model[1]);// 类型
		porbj.setSyscode(SYSTEM_CODE);
		try {
			msg = NatureOaWf.sendFromToOas(JsonUtils.toJson(porbj), OaProcessOverRequestByJson.URL);
		} catch (Exception e) {
			LogUtils.error(e);
		}
		if (msg != null) {
			String c = msg.split(",")[0];
			String s = msg.split(",")[1];
			if ("0".equals(s)) {
				if (c.contains("检测接收人不存在")) {
					LogUtils.info("检测到当前节点没有对应接收人推送公共帐号");
					processOverRequestByJson(wf, new String[] { nodeName[0], OA_PUBLIC_ACCOUNT_NUMBER });
				} else {
					ExceptionUtil.throwServiceException("操作失败:" + c + "!");
				}
			}
		} else {
			ExceptionUtil.throwServiceException("操作失败！");
		}
		return msg;
	}

	@Override
	public String deleteRequestInfoByJson(ActWf wf) {
		if(!isOa){
			return "";
		}
		String msg = null;
		OaDeleteRequestInfoByJson dribj = new OaDeleteRequestInfoByJson();
		dribj.setFlowid(wf.getId().toString());
		dribj.setSyscode(SYSTEM_CODE);
		try {
			msg = NatureOaWf.sendFromToOas(JsonUtils.toJson(dribj), OaDeleteRequestInfoByJson.URL);
		} catch (Exception e) {
			LogUtils.error(e);
		}
		if (msg != null) {
			String c = msg.split(",")[0];
			String s = msg.split(",")[1];
			if ("0".equals(s)) {
				ExceptionUtil.throwServiceException("操作失败:" + c + "!");
			}
		} else {
			ExceptionUtil.throwServiceException("操作失败！");
		}
		return msg;
	}

	@Override
	public String deleteUserRequestInfoByJson(ActWf wf, String userId) {
		if(!isOa){
			return "";
		}
		String msg = null;
		OaDeleteUserRequestInfoByJson duribj = new OaDeleteUserRequestInfoByJson();
		duribj.setSyscode(SYSTEM_CODE);
		duribj.setFlowid(wf.getId().toString());
		duribj.setUserid(userId);
		try {
			msg = NatureOaWf.sendFromToOas(JsonUtils.toJson(duribj), OaDeleteUserRequestInfoByJson.URL);
		} catch (Exception e) {
			LogUtils.error(e);
		}
		if (msg != null) {
			String c = msg.split(",")[0];
			String s = msg.split(",")[1];
			if ("0".equals(s)) {
				ExceptionUtil.throwServiceException("操作失败:" + c + "!");
			}
		} else {
			ExceptionUtil.throwServiceException("操作失败！");
		}
		return msg;
	}

	public static void main(String[] args) {
		OaProcessOverRequestByJson porbj = new OaProcessOverRequestByJson();
		porbj.setSyscode(SYSTEM_CODE);
		porbj.setFlowid("443");
		porbj.setRequestname("");
		porbj.setWorkflowname("");
		porbj.setNodename("");
		porbj.setReceiver("01061637");
		String str = JsonUtils.toJson(porbj);
		System.out.println(str);
	}

}
