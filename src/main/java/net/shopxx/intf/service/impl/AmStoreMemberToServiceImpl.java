package net.shopxx.intf.service.impl;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.HttpClientUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.A1_MessageToService;
import net.shopxx.intf.service.AmStoreMemberToService;
import net.shopxx.member.entity.StoreMember;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
@Service("amStoreMemberToServiceImpl")
public class AmStoreMemberToServiceImpl extends BaseServiceImpl<StoreMember> implements AmStoreMemberToService{
	
	
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "a1_MessageToServiceImpl")
	private A1_MessageToService a1_MessageToService;
	@Resource(name = "a1_MessageToHServiceImpl")
	private A1_MessageToHService a1_MessageToHService;
	
	
	private static final String[] IGNORE_PROPERTIES = new String[] { BaseEntity.ID_PROPERTY_NAME,
			BaseEntity.CREATE_DATE_PROPERTY_NAME,
			BaseEntity.MODIFY_DATE_PROPERTY_NAME };
	
	private static String url="http://192.168.11.188:8081/intf/storeMember/sync.jhtml";//测试	
	private static String token = Global.getLoader().getProperty("synintf.token");
	
	public Map<String, Object> getStoreMemberHashMap(StoreMember storeMember){
		Map<String, Object> requestInfo = new HashMap<String, Object>();
		//主体
		requestInfo.put("companyInfoId", storeMember.getCompanyInfoId() == null ? 0 : storeMember.getCompanyInfoId());
		//*用户名
		requestInfo.put("username", storeMember.getUsername() == null ? "" : storeMember.getUsername().trim());
		//*手机
		requestInfo.put("mobile", storeMember.getMember() == null ? "" : (storeMember.getMember().getMobile() == null ? "" : storeMember.getMember().getMobile().trim()));
		//*密码
		requestInfo.put("password", storeMember.getPassword() == null ? "" : storeMember.getPassword().trim());
		//姓名
		requestInfo.put("name", storeMember.getName() == null ? "" : storeMember.getName().trim());
		//性别
		requestInfo.put("gender", storeMember.getGender() == null ? "" : storeMember.getGender());
		//出生日期
		requestInfo.put("birth", storeMember.getBirth() == null ? "" : storeMember.getBirth());
		//邮编
		requestInfo.put("zipCode", storeMember.getZipCode() == null ? "" : storeMember.getZipCode().trim());
		//地址
		requestInfo.put("address", storeMember.getAddress() == null ? "" : storeMember.getAddress().trim());
		//*身份证
		requestInfo.put("idCard", storeMember.getIdCard() == null ? "" : storeMember.getIdCard().trim());
		//邮箱地址
		requestInfo.put("emailAddress", storeMember.getEmailAddress() == null ? "" : storeMember.getEmailAddress().trim());
		//是否启用      
		requestInfo.put("isEnabled", storeMember.getIsEnabled() == null ? "" : storeMember.getIsEnabled());
		//是否业务员    
		requestInfo.put("isSalesman", storeMember.getIsSalesman() == null ? "" : storeMember.getIsSalesman());
		//会员类型  0 企业用户，1 外部用户，99小程序帐号
		requestInfo.put("memberType", storeMember.getMemberType());
		//是否活动管理员
		requestInfo.put("isActiveAdministrator", storeMember.getIsActiveAdministrator() == null ? "" : storeMember.getIsActiveAdministrator());
		//小程序用户类型
		requestInfo.put("appType", storeMember.getAppType() == null ? "" : storeMember.getAppType());
		return requestInfo;
	}
	
	

	@Override
	public void pushAmStoreMember(StoreMember storeMember) {
		Map<String, Object> requestMap = new HashMap<String, Object>();
		Map<String, Object> esbInfo = new HashMap<String, Object>();
		esbInfo.put("requestTime", DateUtil.convert(new Date(),"yyyy-MM-dd HH:mm:ss.SSS"));
		requestMap.put("esbInfo", esbInfo);
		//用户基本信息
		Map<String, Object> requestInfo = this.getStoreMemberHashMap(storeMember);
		requestMap.put("requestInfo", requestInfo);
		LogUtils.info("requestMap："+JsonUtils.toJson(requestMap));
		String requestStr = JsonUtils.toJson(requestMap);
		String[] fields = new String[] {storeMember.getUsername()};
		Long companyInfoId = storeMember.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		a1_MessageToService.saveA1_MessageTo(26,companyInfo.getUniqueIdentify(),
				requestStr,fields);
	}

	
	public  void saveHistory(A1_MessageTo intftable, String hResult,
			String field8, String hResultMsg) {
		A1_MessageToH intftableH = new A1_MessageToH();
		//复制
		BeanUtils.copyProperties(intftable, intftableH, IGNORE_PROPERTIES);
		intftableH.setField8(field8);
		intftableH.sethResult(hResult);
		intftableH.sethResultMsg(hResultMsg);
		a1_MessageToHService.save(intftableH);
		a1_MessageToService.delete(intftable.getId());
	}
	
	@Override
	public void pushStoreMemberToAm(A1_MessageTo a1_MessageTo) throws Exception {
		String requestStr = a1_MessageTo.getData();
		Map<String,String> headers= new  HashMap<String,String>();
		headers.put("Authorization", token);
		String returnMsgs = HttpClientUtil.post(url,requestStr,headers);
		System.out.println("returnMsgs："+returnMsgs);
		Map<String, Object> params = JsonUtils.toObjectMap(returnMsgs);
		String returnStatus = params.get("returnStatus").toString();
		String returnMsg = params.get("returnMsg").toString();
		// 返回结果处理
		String hType = "2";
		if (!"0".equals(returnStatus)) { 
			hType = "1";
		}
		// 写历史表
		saveHistory(a1_MessageTo, hType, returnStatus, returnMsg);
	}

}
