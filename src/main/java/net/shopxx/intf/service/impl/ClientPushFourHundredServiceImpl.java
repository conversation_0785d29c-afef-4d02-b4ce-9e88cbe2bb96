package net.shopxx.intf.service.impl;


import com.tencent.common.MD5;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.HttpClientUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.A1_MessageToService;
import net.shopxx.intf.service.ClientPushFourHundredService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.service.ShopInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service("clientPushFourHundredServiceImpl")
public class ClientPushFourHundredServiceImpl extends BaseServiceImpl<Store> implements ClientPushFourHundredService {


    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "a1_MessageToServiceImpl")
    private A1_MessageToService a1_MessageToService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name = "a1_MessageToHServiceImpl")
    private A1_MessageToHService a1_MessageToHService;
    @Resource(name = "areaBaseServiceImpl")
    private AreaBaseService areaBaseService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;


    private static final String[] IGNORE_PROPERTIES = new String[]{BaseEntity.ID_PROPERTY_NAME,
            BaseEntity.CREATE_DATE_PROPERTY_NAME,
            BaseEntity.MODIFY_DATE_PROPERTY_NAME};

    private static String AFTERSALE_URL = Global.getLoader().getProperty("ccs.url") + "/nature/api/linkPolicyRequest";
    /**
     * 客户信息到接口表
     *
     */
    @Override
    public void pushAftersale(Aftersale aftersale) {
        //变更后客户信息
        Map<String, Object> requestInfo = this.getAftersaleHashMap(aftersale);
        String requestStr = JsonUtils.toJson(requestInfo);

        LogUtils.debug("----------同步售后订单信息到401-变更后售后订单信息------------：" + requestStr);
        System.out.println("----------同步售后订单信息到401-变更后售后订单信息------------：" + requestStr);

        String[] fields = new String[]{aftersale.getSn()};
        Long companyInfoId = aftersale.getCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

        //标记当前信息为推送给容大的售后订单信息
        LogUtils.debug("标记当前信息为推送给401的售后订单信息:" + requestStr);
        a1_MessageToService.saveA1_MessageTo(401, companyInfo.getUniqueIdentify(), requestStr, fields);
    }

    /**
     * 构建推送401售后订单信息
     *
     * @param aftersale
     * @return
     */
    public Map<String, Object> getAftersaleHashMap(Aftersale aftersale) {

        Map<String, Object> fourHundredInfo = new HashMap<String, Object>();

        /**基本信息*/
        //售后单号
        fourHundredInfo.put("afterSaleSn", ConvertUtil.isEmpty(aftersale.getSn()) ? "" : aftersale.getSn());
        //政策单号
        fourHundredInfo.put("policySn", ConvertUtil.isEmpty(aftersale.getDepositRechargeSn()) ? "" : aftersale.getDepositRechargeSn());
        //政策到账状态
        fourHundredInfo.put("policyStatus", ConvertUtil.isEmpty(aftersale.getIsReceived()) ? null : (aftersale.getIsReceived() ? 1 : 0));
        //政策到账时间
        fourHundredInfo.put("policyTime", ConvertUtil.isEmpty(aftersale.getReceiveDate()) ? null : aftersale.getReceiveDate());
        //政策到账金额
        fourHundredInfo.put("policyPrice", ConvertUtil.isEmpty(aftersale.getReceiveAmount()) ? null : aftersale.getReceiveAmount());
        //退货金额
        fourHundredInfo.put("returnAmount", ConvertUtil.isEmpty(aftersale.getActuralReturnAmount()) ? null : aftersale.getActuralReturnAmount());
        //退货数量
        fourHundredInfo.put("returnedQuantity", ConvertUtil.isEmpty(aftersale.getActuralReturnQuantity()) ? null : aftersale.getActuralReturnQuantity());
        //退货单号
        fourHundredInfo.put("sn", ConvertUtil.isEmpty(aftersale.getB2bReturnSn()) ? null : aftersale.getB2bReturnSn());
        //退货日期
        fourHundredInfo.put("boundDate", ConvertUtil.isEmpty(aftersale.getReturnDate()) ? null : aftersale.getReturnDate());
        //退货状态
        fourHundredInfo.put("b2bReturnStatus", ConvertUtil.isEmpty(aftersale.getReturnStatus()) ? null : aftersale.getReturnStatus());

        return fourHundredInfo;
    }

    @Override
    public void saveHistory(A1_MessageTo intftable, String hResult,
                            String field8, String hResultMsg) throws Exception {
        A1_MessageToH intftableH = new A1_MessageToH();
        // 复制
        BeanUtils.copyProperties(intftable, intftableH, IGNORE_PROPERTIES);
        intftableH.sethResult(hResult);
        intftableH.sethResultMsg(hResultMsg);
        intftableH.setField8(field8);
        a1_MessageToHService.save(intftableH);
        a1_MessageToService.delete(intftable.getId());
    }


    /**
     * 门店信息同步请求到400接口
     *
     * @param a1_MessageTo
     * @throws Exception
     */
    @Override
    public void sendAfterInfoInft(A1_MessageTo a1_MessageTo) throws Exception {
        String requestStr = a1_MessageTo.getData();
        Map<String, Object> map = JsonUtils.toObjectMap(requestStr);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(new Date());
        String encryptionTime = MD5.MD5Encode(date);
        String cryptographyTime = MD5.MD5Encode(encryptionTime);

        map.put("timestamp", date);
        map.put("key", cryptographyTime);
        String request = JsonUtils.toJson(map);
        Map<String, String> headers = new HashMap<String, String>();
        // 返回结果处理
        String hType = "2";
        String returnStatus = null;
        String returnMsg = null;
        try {
            headers.put("Content-Type", "application/json;charset=utf-8");
            LogUtils.info("售后订单信息到401系统开始: " + request);
            String returnMsgs = HttpClientUtil.post(AFTERSALE_URL, request, headers);
            LogUtils.info("售后订单信息到401系统结束: " + returnMsgs);
            //{"data":true,"errorCode":"0"}
            Map<String, Object> params = JsonUtils.toObjectMap(returnMsgs);

            returnStatus = params.get("code").toString();

            returnMsg = returnMsgs;

            if (!"0".equals(returnStatus)) {
                hType = "1";
            }

        } catch (Exception e) {
            hType = "1";
            if (e.getCause() != null) {
                returnMsg = e.getCause().getMessage();
            }
        }
        // 写历史表
        saveHistory(a1_MessageTo, hType, returnStatus, returnMsg);
    }
}
