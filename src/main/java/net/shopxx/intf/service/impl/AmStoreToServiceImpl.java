package net.shopxx.intf.service.impl;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.*;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.A1_MessageToService;
import net.shopxx.intf.service.AmStoreToService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
@Service("amStoreToServiceImpl")
public class AmStoreToServiceImpl extends BaseServiceImpl<Store> implements AmStoreToService{
	
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "a1_MessageToServiceImpl")
	private A1_MessageToService a1_MessageToService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "a1_MessageToHServiceImpl")
	private A1_MessageToHService a1_MessageToHService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	
	private static String url="http://192.168.11.188:8081/intf/store/sync.jhtml";//测试	
	private static String token = Global.getLoader().getProperty("synintf.token");
	
	
	private static final String[] IGNORE_PROPERTIES = new String[] { BaseEntity.ID_PROPERTY_NAME,
			BaseEntity.CREATE_DATE_PROPERTY_NAME,
			BaseEntity.MODIFY_DATE_PROPERTY_NAME };
	
	
	
	public Map<String, Object> getStoreHashMap(Store store){
		Map<String, Object> requestInfo = new HashMap<String, Object>();
		/**基本信息*/
		//主体
		requestInfo.put("companyInfoId", ConvertUtil.isEmpty(store.getCompanyInfoId()) ? 0 : store.getCompanyInfoId());
		//客户编号
		requestInfo.put("sn", ConvertUtil.isEmpty(store.getSn()) ? "" : store.getSn());
		//区域
		requestInfo.put("region", ConvertUtil.isEmpty(store.getRegion()) ? "" : store.getRegion());
		//地区
		if(store.getHeadNewArea() != null && store.getHeadNewArea().getId() != null){
			Area headNewArea = areaBaseService.find(store.getHeadNewArea().getId());
			requestInfo.put("newAreaName", ConvertUtil.isEmpty(headNewArea) ? "" : (ConvertUtil.isEmpty(headNewArea.getName()) ? "" : headNewArea.getName()));
			requestInfo.put("newAreaFullName", ConvertUtil.isEmpty(headNewArea) ? "" : (ConvertUtil.isEmpty(headNewArea.getFullName()) ? "" : headNewArea.getFullName()));
		}else{
			requestInfo.put("newAreaName", "");
			requestInfo.put("newAreaFullName", "");
		}
		//乡镇
		requestInfo.put("countryName", ConvertUtil.isEmpty(store.getCountryName()) ? "" : store.getCountryName());
		//经销商授权编号
		requestInfo.put("grantCode", ConvertUtil.isEmpty(store.getGrantCode()) ? "" : store.getGrantCode());
		//经销商姓名
		requestInfo.put("dealerName", ConvertUtil.isEmpty(store.getDealerName()) ? "" : store.getDealerName());
		//经销商性别
		requestInfo.put("dealerSex", ConvertUtil.isEmpty(store.getDealerSex()) ? "" : store.getDealerSex());
		//城市等级
		requestInfo.put("accountTypeCode", ConvertUtil.isEmpty(store.getAccountTypeCode()) ? "" : store.getAccountTypeCode());
		//客户名称
		requestInfo.put("name", ConvertUtil.isEmpty(store.getName()) ? "" : store.getName());
		//手机号
		requestInfo.put("headPhone", ConvertUtil.isEmpty(store.getHeadPhone()) ? "" : store.getHeadPhone());
		//经销商学历
		requestInfo.put("dealerGrade", ConvertUtil.isEmpty(store.getDealerGrade()) ? "" : store.getDealerGrade());
		//业务类型
		requestInfo.put("businessTypeName", ConvertUtil.isEmpty(store.getBusinessType()) ? "" : (ConvertUtil.isEmpty(store.getBusinessType().getValue()) ? "" : store.getBusinessType().getValue()));
		//客户简称
		requestInfo.put("alias",  ConvertUtil.isEmpty(store.getAlias()) ? "" :store.getAlias());
		//固定号码
		requestInfo.put("fixedNumber", ConvertUtil.isEmpty(store.getFixedNumber()) ? "" : store.getFixedNumber());
		//公司性质
		requestInfo.put("companyType", ConvertUtil.isEmpty(store.getCompanyType()) ? "" : store.getCompanyType());
		//总经销商
		requestInfo.put("franchisee",  ConvertUtil.isEmpty(store.getFranchisee()) ? "" :store.getFranchisee());
		//经销商状态
		requestInfo.put("distributorStatusName", ConvertUtil.isEmpty(store.getDistributorStatus()) ? "" : (ConvertUtil.isEmpty(store.getDistributorStatus().getValue()) ? "" : store.getDistributorStatus().getValue()));
		//身份证信息
		requestInfo.put("identity", ConvertUtil.isEmpty(store.getIdentity()) ? "" : store.getIdentity());
		//平台性质
		requestInfo.put("platformProperty", ConvertUtil.isEmpty(store.getPlatformProperty()) ? "" : store.getPlatformProperty());
		//机构
		requestInfo.put("saleOrgName", ConvertUtil.isEmpty(store.getSaleOrg()) ? "" : (ConvertUtil.isEmpty(store.getSaleOrg().getName()) ? "" : store.getSaleOrg().getName()));
		//是否可发货
		requestInfo.put("shippingState",  ConvertUtil.isEmpty(store.getShippingState()) ? "" : store.getShippingState());
		//合同主体
		requestInfo.put("contractSubject", ConvertUtil.isEmpty(store.getContractSubject()) ? "" : store.getContractSubject());
		//销售平台
		requestInfo.put("salesPlatformName", ConvertUtil.isEmpty(store.getSalesPlatform()) ? "" : store.getSalesPlatform().getName());
		//区域经理
		requestInfo.put("storeMemberName", ConvertUtil.isEmpty(store.getStoreMember()) ? "" : (ConvertUtil.isEmpty(store.getStoreMember().getUsername()) ? "" : store.getStoreMember().getUsername()));
		//sbu
		requestInfo.put("sbuName", ConvertUtil.isEmpty(store.getSbu()) ? "" : (ConvertUtil.isEmpty(store.getSbu().getValue()) ? "" : store.getSbu().getValue()));
		//法人代表
		requestInfo.put("contact", ConvertUtil.isEmpty(store.getContact()) ? "" : store.getContact());
		//销售区域
		requestInfo.put("salesAreaName", ConvertUtil.isEmpty(store.getSalesArea()) ? "" : (ConvertUtil.isEmpty(store.getSalesArea().getName()) ? "" : store.getSalesArea().getName()));
		//ERP客户编码
		requestInfo.put("outTradeNo", ConvertUtil.isEmpty(store.getOutTradeNo()) ? "" :store.getOutTradeNo());
		//经销商关系说明
		requestInfo.put("dealerRelationShip", ConvertUtil.isEmpty(store.getDealerRelationShip()) ? "" : store.getDealerRelationShip());
		//是否启用
		requestInfo.put("isEnabled", ConvertUtil.isEmpty(store.getIsEnabled()) ? "" : store.getIsEnabled());
		//价格类型
		requestInfo.put("memberRankName", ConvertUtil.isEmpty(store.getMemberRank()) ? "" : (ConvertUtil.isEmpty(store.getMemberRank().getName()) ? "" : store.getMemberRank().getName()));
		//核心经销商
		requestInfo.put("isCoreStroe", ConvertUtil.isEmpty(store.getIsCoreStroe()) ? "" : store.getIsCoreStroe());
		//经销商类别
		requestInfo.put("category", ConvertUtil.isEmpty(store.getCategory()) ? "" : store.getCategory());
		//经销商属性
		requestInfo.put("agentPropertyName", ConvertUtil.isEmpty(store.getAgentProperty()) ? "" : (ConvertUtil.isEmpty(store.getAgentProperty().getValue()) ? "" : store.getAgentProperty().getValue()));
		//经销商地址
		requestInfo.put("headAddress", ConvertUtil.isEmpty(store.getHeadAddress()) ? "" : store.getHeadAddress());
		//门店地址
		requestInfo.put("shopAddress", ConvertUtil.isEmpty(store.getShopAddress()) ? "" : store.getShopAddress());
		/**其他信息*/
		//是否加盟成功
		requestInfo.put("unJoinSucessFlag", ConvertUtil.isEmpty(store.getUnJoinSucessFlag()) ? "" :store.getUnJoinSucessFlag());
		//是否解约时是否资料齐全
		requestInfo.put("cancelInfoFlag", ConvertUtil.isEmpty(store.getCancelInfoFlag()) ? "" : store.getCancelInfoFlag());
		//应缴品牌保证金
		requestInfo.put("needCautionPaid", ConvertUtil.isEmpty(store.getNeedCautionPaid()) ? BigDecimal.ZERO : store.getNeedCautionPaid());
		//是否现金客户
		requestInfo.put("cashClientFlag", ConvertUtil.isEmpty(store.getCashClientFlag()) ? "" : store.getCashClientFlag());
		//加盟日期
		requestInfo.put("activeDate", ConvertUtil.isEmpty(store.getActiveDate()) ? "" : store.getActiveDate());
		//解约日期
		requestInfo.put("cancelDate", ConvertUtil.isEmpty(store.getCancelDate()) ? "" : store.getCancelDate());
		//实缴品牌保证金
		requestInfo.put("realCautionPaid", ConvertUtil.isEmpty(store.getRealCautionPaid()) ? BigDecimal.ZERO : store.getRealCautionPaid());
		//货币
		requestInfo.put("currencyCode", ConvertUtil.isEmpty(store.getCurrencyCode()) ? "" : store.getCurrencyCode());
		//加盟档案编号
		requestInfo.put("joinFileNumber", ConvertUtil.isEmpty(store.getJoinFileNumber()) ? "" : store.getJoinFileNumber());
		//解约档案编号
		requestInfo.put("unfileNumber", ConvertUtil.isEmpty(store.getUnfileNumber()) ? "" : store.getUnfileNumber());
		//欠缴品牌保证金
		requestInfo.put("unpaidCautionPaid", ConvertUtil.isEmpty(store.getUnpaidCautionPaid()) ? "" : store.getUnpaidCautionPaid());
		//经销商类型
		requestInfo.put("distributorType", ConvertUtil.isEmpty(store.getDistributorType()) ? "" : store.getDistributorType());
		//解约原因
		requestInfo.put("cancelReason", ConvertUtil.isEmpty(store.getCancelReason()) ? "" : store.getCancelReason());
		//销量保证金
		requestInfo.put("salesDeposit", ConvertUtil.isEmpty(store.getSalesDeposit()) ? BigDecimal.ZERO : store.getSalesDeposit());
		//销售品类
		requestInfo.put("salesCategory", ConvertUtil.isEmpty(store.getSalesCategory()) ? "" : store.getSalesCategory());
		//经销商子类型
		requestInfo.put("subType", ConvertUtil.isEmpty(store.getSubType()) ? "" : store.getSubType());
		//税率
		requestInfo.put("taxRate", ConvertUtil.isEmpty(store.getTaxRate()) ? BigDecimal.ZERO : store.getTaxRate());
		//缴纳情况/异常说明
		requestInfo.put("paymentStatus", ConvertUtil.isEmpty(store.getPaymentStatus()) ? "" : store.getPaymentStatus());
		//备注（门店地址）
		requestInfo.put("description", ConvertUtil.isEmpty(store.getDescription()) ? "" : store.getDescription());
		//客户类型 
		requestInfo.put("type", ConvertUtil.isEmpty(store.getType()) ? "distributor" : store.getType());
		//是否对应企业
		requestInfo.put("isMainStore", ConvertUtil.isEmpty(store.getIsMainStore()) ? "" : store.getIsMainStore());
		
		return requestInfo;
	}
	
	public void pushAmStore(Store store) {
		Map<String, Object> requestMap = new HashMap<String, Object>();
		Map<String, Object> esbInfo = new HashMap<String, Object>();
		esbInfo.put("requestTime", DateUtil.convert(new Date(),"yyyy-MM-dd HH:mm:ss.SSS"));
		requestMap.put("esbInfo", esbInfo);
		//客户基本信息
		Map<String, Object> requestInfo = this.getStoreHashMap(store);
		requestMap.put("requestInfo", requestInfo);
		LogUtils.info("requestMap："+JsonUtils.toJson(requestMap));
		String requestStr = JsonUtils.toJson(requestMap);
		String[] fields = new String[] {store.getOutTradeNo()};
		Long companyInfoId = store.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		a1_MessageToService.saveA1_MessageTo(25,companyInfo.getUniqueIdentify(),
				requestStr,fields);
	}
					
	public void saveHistory(A1_MessageTo intftable, String hResult,
			String field8, String hResultMsg) {
		A1_MessageToH intftableH = new A1_MessageToH();
		// 复制
		BeanUtils.copyProperties(intftable, intftableH, IGNORE_PROPERTIES); 
		intftableH.sethResult(hResult);
		intftableH.sethResultMsg(hResultMsg);
		intftableH.setField8(field8);
		a1_MessageToHService.save(intftableH);
		a1_MessageToService.delete(intftable.getId());
	}
	
	@Override
	public void pushStoreToAm(A1_MessageTo a1_MessageTo) throws Exception {
		String requestStr = a1_MessageTo.getData();
		Map<String,String> headers= new  HashMap<String,String>();
		headers.put("Authorization", token);
		String returnMsgs = HttpClientUtil.post(url,requestStr,headers);
		System.out.println("returnMsgs："+returnMsgs);
		Map<String, Object> params = JsonUtils.toObjectMap(returnMsgs);
		String returnStatus = params.get("returnStatus").toString();
		String returnMsg = params.get("returnMsg").toString();
		// 返回结果处理
		String hType = "2";
		if (!"0".equals(returnStatus)) { 
			hType = "1";
		}
		// 写历史表
		saveHistory(a1_MessageTo, hType, returnStatus, returnMsg);
	}

}


