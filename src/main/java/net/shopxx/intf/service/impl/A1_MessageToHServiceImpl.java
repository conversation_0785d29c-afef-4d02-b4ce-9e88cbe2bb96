package net.shopxx.intf.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("a1_MessageToHServiceImpl")
public class A1_MessageToHServiceImpl extends BaseServiceImpl<A1_MessageToH>
		implements A1_MessageToHService {

	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;

	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn, String type,
			String firstTime, String lastTime, String hResult, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sb = new StringBuilder();

		sb.append("select * from a1_messagetoh where 1=1 and (field7 is null or field7!=1)");

		if (companyInfoId != null) {
			CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
			sb.append(" and companytoken = ?");
			list.add(companyInfo.getUniqueIdentify());
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sb.append(" and field1 like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(type)) {
			sb.append(" and type = ?");
			list.add(type);
		}

		if (!ConvertUtil.isEmpty(hResult)) {
			sb.append(" and h_result = ?");
			list.add(hResult);
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			sb.append(" and create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sb.append(" and create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}

		sb.append(" order by create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = this.getDaoCenter()
				.getNativeDao()
				.findPageMap(sb.toString(), objs, pageable);
		return page;
	}
	
	//壁高查询
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> sbu_findPage(String sn, String type,
			String firstTime, String lastTime, String hResult, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sb = new StringBuilder();

		sb.append(" select h.*,s.id shipping_id from a1_messagetoh h left join  xx_shipping s on s.sn=h.field1  where 1=1 and (h.field7 is null or h.field7!=1) and s.sbu=3 ");

		if (companyInfoId != null) {
			CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
			sb.append(" and h.companytoken = ?");
			list.add(companyInfo.getUniqueIdentify());
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sb.append(" and h.field1 like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(type)) {
			sb.append(" and h.type = ?");
			list.add(type);
		}

		if (!ConvertUtil.isEmpty(hResult)) {
			sb.append(" and h.h_result = ?");
			list.add(hResult);
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			sb.append(" and h.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sb.append(" and h.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}

		sb.append(" order by h.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = this.getDaoCenter()
				.getNativeDao()
				.findPageMap(sb.toString(), objs, pageable);
		return page;
	}

	@Override
	public void delectSql(String shippingSn) {
		StringBuilder sqlD = new StringBuilder();
		sqlD.append(" delete from a1_messagetoh where type = 0 and h_result =1  and field1 = ? and company_info_id= ?");
		this.getDaoCenter()
		.getNativeDao().delete(sqlD.toString(),
				new Object[] { shippingSn,
						WebUtils.getCurrentCompanyInfoId() });
		
	}
    
}
