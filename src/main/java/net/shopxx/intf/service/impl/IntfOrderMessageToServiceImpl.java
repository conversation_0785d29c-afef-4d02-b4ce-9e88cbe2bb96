package net.shopxx.intf.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.shopxx.CMB.Base64;
import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.*;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.*;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.*;
import net.shopxx.intf.dao.A1_MessageToDao;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.A1_MessageToService;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.entity.*;
import net.shopxx.member.entity.StoreMember.Gender;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.entity.*;
import net.shopxx.order.service.*;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.service.WarehouseStockService;
import net.shopxx.util.HttpUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfProcBaseService;
import net.shopxx.wf.service.WfProcuserBaseService;
import net.shopxx.wf.service.WfUserOpinionBaseService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
@Service("intfOrderMessageToServiceImpl")
public class IntfOrderMessageToServiceImpl extends
		BaseServiceImpl<A1_MessageTo> implements IntfOrderMessageToService {

	private static String ssoUrl = Global.getLoader()
			.getProperty("dbintf.ssoUrl");
	private static String ssoUrlIntf = Global.getLoader()
			.getProperty("dbintf.ssoUrlIntf");
	private static String ssoKey = Global.getLoader()
			.getProperty("dbintf.ssoKey");
	private static final String[] IGNORE_PROPERTIES = new String[] { BaseEntity.ID_PROPERTY_NAME,
			BaseEntity.CREATE_DATE_PROPERTY_NAME,
			BaseEntity.MODIFY_DATE_PROPERTY_NAME };
	
	
	//private static String username = "link01"; //测试
	//private static String password = "7iURWx3JNDQFvhT7"; //测试
	
	private static String wsdlUrl =  Global.getLoader().getProperty("dbintf.url")+"/api/v1/Erp/Order/Sync";
	private static String username = Global.getLoader().getProperty("dbintf.username");
	private static String password = Global.getLoader().getProperty("dbintf.password");

	@Resource(name = "a1_MessageToHServiceImpl")
	private A1_MessageToHService a1_MessageToHService;
	@Resource(name = "a1_MessageToServiceImpl")
	private A1_MessageToService a1_MessageToService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "contractServiceImpl")
	private ContractService contractService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "a1_MessageToDao")
	private A1_MessageToDao a1_MessageToDao;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfProcuserBaseServiceImpl")
	private WfProcuserBaseService wfProcuserBaseService;
	@Resource(name = "wfProcBaseServiceImpl")
	private WfProcBaseService wfProcBaseService;
	@Resource(name = "wfUserOpinionBaseServiceImpl")
	private WfUserOpinionBaseService wfUserOpinionBaseService;
	@Resource(name = "salesareaServiceImpl")
	private SalesAreaService salesAreaService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "planApplyServiceImpl")
	private PlanApplyService planApplyService;
	@Resource(name = "amShippingServiceImpl")
	private AmShippingService amShippingService;
    @Resource(name = "orderFullLinkServiceImpl")
    private OrderFullLinkService orderFullLinkService;
    @Resource(name = "roleJurisdictionUtil")
    private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "warehouseStockServiceImpl")
    private WarehouseStockService warehouseStockService;
    @Resource(name = "amShippingItemServiceImpl")
    private  AmShippingItemService amShippingItemService;

	//获取验证账号
	private String getHeader() {
		String auth = username + ":" + password;
		String encodedAuth = Base64.encode(auth.getBytes(Charset.forName("US-ASCII")));
		String authHeader = "Basic " + new String(encodedAuth);
		return authHeader;
	}

	//日期格式
	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

	//写入接口历史表
	public void saveHistory(A1_MessageTo intftable, String hResult,
			String field8, String hResultMsg) {
		System.out.println("errorMsg:"+hResultMsg);
		A1_MessageToH intftableH = new A1_MessageToH();
		BeanUtils.copyProperties(intftable, intftableH, IGNORE_PROPERTIES); // 复制
		intftableH.sethResult(hResult);
		intftableH.sethResultMsg(hResultMsg);
		intftableH.setField8(field8);
		a1_MessageToHService.save(intftableH);
		this.delete(intftable.getId());

	}
	

	/**
	 * 生成 同步发货通知单到家哇云的报文到接口表
	 * @param shipping 发货通知单
	 * @param flag    0-同步
	 */
	public void saveLogisticsIntf(Shipping shipping,Integer flag) {
		Warehouse warehouse = shipping.getWarehouse();

		if (warehouse.getErp_warehouse_code() == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的外部编码不能为空");
		}
		Organization managementOrganization = warehouse.getManagementOrganization();
		if (managementOrganization == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的经营组织不能为空");
		}
		else if (managementOrganization.getCode() == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的经营组织编码不能为空");
		}

		SystemDict stockSystemDict = warehouse.getStockSystemDict();
		if (stockSystemDict == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的库存组织不能为空");
		}


		Store store = shipping.getStore();

		/**ESB标准开头*/
		Map<String, Object> requestMap = new HashMap<String, Object>();
		Map<String, Object> requestInfo = new HashMap<String, Object>();

		Map<String, Object> esbInfo = new HashMap<String, Object>();
		esbInfo.put("requestTime", DateUtil.convert(new Date(),
				"yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.put("attr1", "");
		esbInfo.put("attr2", "");
		esbInfo.put("attr3", "");
		requestMap.put("esbInfo", esbInfo);
		
		Map<String, Object> queryInfo = new HashMap<String, Object>();
		queryInfo.put("pageSize", "");
		queryInfo.put("currentPage", "");

		requestMap.put("queryInfo", queryInfo);
		
		
		if(flag==0){
			requestInfo.put("salesNo", shipping.getSn());// 发货单号
			requestInfo.put("shippingId", shipping.getId());// 头ID
			requestInfo.put("customerOrderNo","");// 客户订单号
			requestInfo.put("source_id","");// 客户订单号
			requestInfo.put("salesOrderNo","");// 销售单号
			requestInfo.put("erpSn","");// 销售单号
			requestInfo.put("estimatedPickupTime","");// 预计揽货时间
			requestInfo.put("delivery_type", shipping.getShippingMethod());// 发运方式
			requestInfo.put("needLogisticsScheduler", "");// 是否需要物流调度协调
			requestInfo.put("customerName", store.getName());// 客户名称
			requestInfo.put("customerAlias", store.getAlias());// 客户别称
			requestInfo.put("division","地板(AM)事业部");// 事业部
			requestInfo.put("type", "AM发货单");// 订单类型
			requestInfo.put("totalPrice",shipping.getAmount());//总售价
			requestInfo.put("sale_org_name",shipping.getSaleOrg().getName());//机构
			requestInfo.put("totalWeight",shipping.getWeight()==null?"":shipping.getWeight());//总重量
			requestInfo.put("startTime",shipping.getCreateDate());// 订单日期
			requestInfo.put("messageType","已登记");// 消息类型
			requestInfo.put("projectNo", "");// 项⽬号
			requestInfo.put("projectName", "");// 项⽬名称
			requestInfo.put("remarks", "");// 备注
			requestInfo.put("backupColumn1", "");// 备用1
			requestInfo.put("backupColumn2", "");// 备用2
			requestInfo.put("backupColumn3", "");// 备用3
			requestInfo.put("backupColumn4", "");// 备用4
			requestInfo.put("backupColumn5", "");// 备用5	
			if(shipping.getSmethod().equals("普通发运")){
				requestInfo.put("send_type", 1);//配送方式
			}else if(shipping.getSmethod().equals("暂缓发运")){
				requestInfo.put("send_type", 2);//配送方式
			}
			

			Map<String, Object> lineTblSender = new HashMap<String, Object>();

			
			lineTblSender.put("name", "");
			lineTblSender.put("mobile", "");
			lineTblSender.put("phone", "");
			lineTblSender.put("provinceName", "");
			lineTblSender.put("cityName", "");
			lineTblSender.put("districtName", "");
			lineTblSender.put("address", "");
			lineTblSender.put("spWarehouseCode", warehouse.getErp_warehouse_code());
			
		
			requestInfo.put("senderInfo", lineTblSender);
			
			Map<String, Object> lineTblReceiver = new HashMap<String, Object>();

			lineTblReceiver.put("name", shipping.getConsignee());
			lineTblReceiver.put("mobile", shipping.getPhone());
			lineTblReceiver.put("phone", "");
			
			
			//拆分省市区
			Area area = shipping.getArea();
			String districtName="";
			String cityName="";
			String provinceName="";
		
			if(area!=null && area.getParent().getParent()!=null){
				districtName=area.getName();
				cityName=area.getParent().getName();
				provinceName=area.getParent().getParent().getName();
			}else{
				cityName=area.getName();
				provinceName=area.getParent().getName();
			}
			
			lineTblReceiver.put("provinceName",provinceName );
			lineTblReceiver.put("cityName", cityName);
			lineTblReceiver.put("districtName",districtName );
			lineTblReceiver.put("detailAddress", shipping.getAddress());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
			                          
			
			lineTblReceiver.put("spWarehouseCode", "");
			
		
			requestInfo.put("receiverInfo", lineTblReceiver);
			
			
			
		}
//		else{
//			requestInfo.put("salesNo", shipping.getSn());// 发货单号
//			requestInfo.put("shippingId", shipping.getId());// 头ID
//			requestInfo.put("division","地板(AM)事业部");// 事业部
//			requestInfo.put("delivery_type", shipping.getShippingMethod());// 配送方式
//			requestInfo.put("type", "AM发货单");// 订单类型
//			//由于整单作废SQL事务未提交状态未改变所以写上状态2
//			if(flag==2){
//				requestInfo.put("status", 2);//状态
//			}else{
//				requestInfo.put("status", shipping.getStatus());//状态
//			}
//
//			requestInfo.put("erpSn", shipping.getErpSn());// erp单号
//			requestInfo.put("stockOutNo", shipping.getSn());// 家哇云出库单号
//			requestInfo.put("subWarehouseID", warehouse.getErp_warehouse_code());// 子库库编码
//			requestInfo.put("totalWeight", "");// 总重量
//			requestInfo.put("fobAddrress", "");// fob地点
//			requestInfo.put("contact", shipping.getConsignee());//联系人
//			requestInfo.put("startTime", shipping.getErpDate());//实际出库时间
//			if(shipping.getSmethod().equals("普通发运")){
//				requestInfo.put("send_type", 1);//配送方式
//			}else if(shipping.getSmethod().equals("暂缓发运")){
//				requestInfo.put("send_type", 2);//配送方式
//			}
//
//			requestInfo.put("sale_org_name",shipping.getSaleOrg().getName());//机构
//			requestInfo.put("shippingNote", "");//发运说明
//			requestInfo.put("summary", "");//摘要
//			requestInfo.put("remarks", "");//备注
//		}
		
		
		List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
		for (ShippingItem shippingItem : shipping.getShippingItems()) {
			Product product = shippingItem.getProduct();
			OrderItem orderItem = shippingItem.getOrderItem();
			String productGradeName = "";
			
			if (orderItem != null && orderItem.getProductLevel() != null) {
				if (!ConvertUtil.isEmpty(orderItem.getProductLevel().getValue())) {
					productGradeName = orderItem.getProductLevel().getValue();
				}else{
					ExceptionUtil.throwServiceException("产品【"+ shippingItem.getName()+ "】的物料等级不能为空");
				}
			}
		
			if (shippingItem.getVonderCode() == null) {
				ExceptionUtil.throwServiceException("产品【"
						+ shippingItem.getName()
						+ "】的物料编码不能为空");
			}

			BigDecimal weight= new BigDecimal("0");
			BigDecimal voluem= new BigDecimal("0");
			if(flag==0){
				weight=(shippingItem.getQuantity()==null ? new BigDecimal("0") : shippingItem.getQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
				voluem=(shippingItem.getBoxQuantity()==null ? new BigDecimal("0") : shippingItem.getBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
			}else{
				weight=(shippingItem.getShippedQuantity()==null ? new BigDecimal("0") : shippingItem.getShippedQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
				voluem=(shippingItem.getShippedBoxQuantity()==null ? new BigDecimal("0") : shippingItem.getShippedBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
			}
			
			Map<String, Object> lineTblItem = new HashMap<String, Object>();
           
			lineTblItem.put("itemStatus", shippingItem.getStatus());//状态 
			lineTblItem.put("cargoName", product.getName());//货物名称
			lineTblItem.put("shippingItemId", shippingItem.getId());//行ID
			lineTblItem.put("cargoDescription", product.getDescription());// 货物描述
			lineTblItem.put("model", product.getModel());// 型号
			lineTblItem.put("cargo_no", product.getVonderCode());// 编码
			lineTblItem.put("grade", productGradeName);// 等级
			lineTblItem.put("colorNo", product.getWoodTypeOrColor()==null ? "" : product.getWoodTypeOrColor());// ⾊号
			lineTblItem.put("remarks", shippingItem.getMemo()==null ? "" : shippingItem.getMemo());//备注
			lineTblItem.put("batch", "");// 货物批次
			lineTblItem.put("length",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⻓度
			lineTblItem.put("width",product.getErp_width()==null ? "" :product.getErp_width());//单件货物宽度
			lineTblItem.put("height",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⾼度
			lineTblItem.put("price","");//货物单价
			if(flag==0){
				lineTblItem.put("num",shippingItem.getQuantity());//货物数量
				lineTblItem.put("aidedNum",shippingItem.getBranchQuantity());//辅数量（⽀数）
				lineTblItem.put("packageNum",shippingItem.getBoxQuantity());//货物件数
			}else{
				lineTblItem.put("num",shippingItem.getShippedQuantity());//货物数量
				lineTblItem.put("aidedNum",shippingItem.getShippedBranchQuantity());//辅数量（⽀数）
				lineTblItem.put("packageNum",shippingItem.getShippedBoxQuantity());//货物件数
			}
			
			lineTblItem.put("branch_per_box",product.getBranchPerBox()==null?"":product.getBranchPerBox());//每箱支数
			lineTblItem.put("per_box",product.getPerBox());// 每支单位数 
			lineTblItem.put("per_branch",product.getPerBranch()==null?"":product.getPerBranch());//每箱单位数
			lineTblItem.put("unit",product.getUnit());//数量单位
			lineTblItem.put("org_id",warehouse.getStockSystemDict().getRemark());//库存组织
			lineTblItem.put("category",product.getProductCategory().getName());//分类
			lineTblItem.put("area",voluem);// 单件货物⾯积
			lineTblItem.put("weight", weight);//单件货物重量
			lineTblItem.put("aidedUnit",product.getSpecTwo());//辅单位（个）
			
			lineTblItem.put("packageUnit","箱");//件数单位
			lineTblItem.put("oldSN","");//旧物料编码
			lineTblItem.put("backupColumn1","");//备用1
			lineTblItem.put("backupColumn2","");//备用2
			lineTblItem.put("backupColumn3","");//备用3
			lineTblItem.put("backupColumn4","");//备用4


			lineTblItem.put("businessOrgName",shippingItem.getOrderItem().getProductOrganization().getName());//经营组织
			//lineTblItem.put("grade",shippingItem.getOrderItem().getProductLevel().getValue());//等级
			//lineTblItem.put("colorNo",shippingItem.getColourNumbers().getValue());//色号
			//lineTblItem.put("moistureContent",shippingItem.getMoistureContents().getValue());//含水率
			lineTblItem.put("newFlag","");//新旧标识
			lineTblItem.put("batch","");//批次
			lineTblItem.put("storageLocation","");//库位
            
            

			
			lineTbl.add(lineTblItem);

			
		}
		
		requestInfo.put("items", lineTbl);
		requestMap.put("requestInfo", requestInfo);

		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(requestMap);

		Long companyInfoId = shipping.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
	

		/** 写入接口表 */
		if(flag==0){
			//同步发货单
			String[] fields = new String[] { shipping.getSn() };
			a1_MessageToService.saveA1_MessageTo(51,
					companyInfo.getUniqueIdentify(),
					requestStr,
					fields);
		}
//		else{
//			//取消发货单
//			String[] fields = new String[] { shipping.getSn() };
//			a1_MessageToService.saveA1_MessageTo(52,
//					companyInfo.getUniqueIdentify(),
//					requestStr,
//					fields);
//
//		}
	}


	/**
	 * 生成 同步移库单到家哇云的报文到接口表
	 * @param moveLibrary 移库单
	 * @param flag 0-同步
	 */
	public void saveLogisticsMoveIntf(MoveLibrary moveLibrary,Integer flag) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 

		Map<String, Object> requestMap = new HashMap<String, Object>();
		Map<String, Object> requestInfo = new HashMap<String, Object>();

		Map<String, Object> esbInfo = new HashMap<String, Object>();
		esbInfo.put("requestTime", DateUtil.convert(new Date(),
				"yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.put("attr1", "");
		esbInfo.put("attr2", "");
		esbInfo.put("attr3", "");
		requestMap.put("esbInfo", esbInfo);
		
		Map<String, Object> queryInfo = new HashMap<String, Object>();
		queryInfo.put("pageSize", "");
		queryInfo.put("currentPage", "");

		requestMap.put("queryInfo", queryInfo);
		
		
		if(flag==0){
			
		
			requestInfo.put("salesNo", moveLibrary.getSn());// 移库单号
			requestInfo.put("shippingId", moveLibrary.getId());// 头ID
			requestInfo.put("customerOrderNo","");// 客户订单号
			requestInfo.put("salesOrderNo","");// 销售单号
			requestInfo.put("estimatedPickupTime","");// 预计揽货时间
			requestInfo.put("needLogisticsScheduler", "");// 是否需要物流调度协调
			requestInfo.put("customerName", "");// 客户名称
			requestInfo.put("customerAlias", "");// 客户别称
			requestInfo.put("division","地板(AM)事业部");// 事业部
			requestInfo.put("type", "AM移库单");// 订单类型
			requestInfo.put("totalPrice","");//总售价
			requestInfo.put("totalWeight","");//总重量
			requestInfo.put("startTime",moveLibrary.getCreateDate());// 订单日期
			requestInfo.put("messageType","已登记");// 消息类型
			requestInfo.put("projectNo", "");// 项⽬号
			requestInfo.put("projectName", "");// 项⽬名称
			requestInfo.put("remarks", "");// 备注
			requestInfo.put("sale_org_name",moveLibrary.getSaleOrg().getName());//机构
			requestInfo.put("backupColumn1", "");// 备用1
			requestInfo.put("backupColumn2", "");// 备用2
			requestInfo.put("backupColumn3", "");// 备用3
			requestInfo.put("backupColumn4", "");// 备用4
			requestInfo.put("backupColumn5", "");// 备用5	

			Map<String, Object> lineTblSender = new HashMap<String, Object>();

			
			lineTblSender.put("name", "");
			lineTblSender.put("mobile", "");
			lineTblSender.put("phone", "");
			lineTblSender.put("provinceName", "");
			lineTblSender.put("cityName", "");
			lineTblSender.put("districtName", "");
			lineTblSender.put("address", "");
			lineTblSender.put("spWarehouseCode", moveLibrary.getIssueWarehouse().getErp_warehouse_code());
			lineTblSender.put("toWarehouseCode", moveLibrary.getReceiveWarehouse().getErp_warehouse_code());

		
			requestInfo.put("senderInfo", lineTblSender);
			
			Map<String, Object> lineTblReceiver = new HashMap<String, Object>();

//			
//			lineTblReceiver.put("name", shipping.getConsignee());
//			lineTblReceiver.put("mobile", shipping.getPhone());
//			lineTblReceiver.put("phone", "");
//			
//			
//			//拆分省市区
//			Area area = shipping.getArea();
//			String districtName="";
//			String cityName="";
//			String provinceName="";
//		
//			if(area!=null && area.getParent().getParent()!=null){
//				districtName=area.getName();
//				cityName=area.getParent().getName();
//				provinceName=area.getParent().getParent().getName();
//			}else{
//				cityName=area.getName();
//				provinceName=area.getParent().getName();
//			}
//			
			lineTblReceiver.put("provinceName","" );
			lineTblReceiver.put("cityName", "");
			lineTblReceiver.put("districtName","" );
			lineTblReceiver.put("detailAddress", "");
			lineTblReceiver.put("spWarehouseCode", moveLibrary.getReceiveWarehouse().getErp_warehouse_code());
			
			requestInfo.put("receiverInfo", lineTblReceiver);
		}
//		else{
//			requestInfo.put("salesNo", moveLibrary.getSn());// 发货单号
//			requestInfo.put("shippingId", moveLibrary.getId());// 头ID
//			requestInfo.put("type", "AM转库单");// 订单类型
//			requestInfo.put("stockOutNo", moveLibrary.getSn());// 家哇云出库单号
//			requestInfo.put("subWarehouseID", moveLibrary.getIssueWarehouse().getErp_warehouse_code());// 子库编码
//			requestInfo.put("totalWeight", "");// 总重量
//			requestInfo.put("fobAddrress", "");// fob地点
//			requestInfo.put("contact", "");//联系人
//            requestInfo.put("division","地板事业总部");// 事业部
//			requestInfo.put("startTime", moveLibrary.getIssueDate());//实际出库时间
//			requestInfo.put("sale_org_name",moveLibrary.getSaleOrg().getName());//机构
//			requestInfo.put("shippingMethod", "汽运");//发运方式
//			requestInfo.put("shippingNote", "");//发运说明
//			requestInfo.put("summary", "");//摘要
//			requestInfo.put("remarks", "");// 备注
//		}
			
		
		List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
		for (MoveLibraryItem moveLibraryItem : moveLibrary.getMoveLibraryItems()) {
			Product product = moveLibraryItem.getProduct();
		
			String productGradeName = "";
			
			if (moveLibraryItem != null && moveLibraryItem.getProductLevel() != null) {
				if (!ConvertUtil.isEmpty(moveLibraryItem.getProductLevel().getValue())) {
					productGradeName = moveLibraryItem.getProductLevel().getValue();
				}else {
					ExceptionUtil.throwServiceException("产品【"+ product.getName()+ "】的物料等级不能为空");
				}
			}
		
			if (moveLibraryItem.getVonderCode() == null) {
				ExceptionUtil.throwServiceException("产品【"
						+ product.getName()
						+ "】的物料编码不能为空");
			}

			
			//计算重量体积
			BigDecimal weight= new BigDecimal("0");
			BigDecimal voluem= new BigDecimal("0");
			
			
			if(flag==0){
				weight=(moveLibraryItem.getQuantity()==null ? new BigDecimal("0") : moveLibraryItem.getQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
				voluem=(moveLibraryItem.getBoxQuantity()==null ? new BigDecimal("0") : moveLibraryItem.getBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
			}else{
				weight=(moveLibraryItem.getIssueQuantity()==null ? new BigDecimal("0") : moveLibraryItem.getIssueQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
				voluem=(moveLibraryItem.getIssueBoxQuantity()==null ? new BigDecimal("0") : moveLibraryItem.getIssueBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
			}
			
			Map<String, Object> lineTblItem = new HashMap<String, Object>();
			
			lineTblItem.put("cargoName", product.getName());//货物名称
			lineTblItem.put("shippingItemId", moveLibraryItem.getId());//行ID
			lineTblItem.put("cargoDescription", product.getDescription());// 货物描述
			lineTblItem.put("model", product.getModel());// 型号
			lineTblItem.put("cargo_no", product.getVonderCode());// 编码
			lineTblItem.put("grade", productGradeName);// 等级
			lineTblItem.put("colorNo", product.getWoodTypeOrColor()==null ? "" : product.getWoodTypeOrColor());// ⾊号
			lineTblItem.put("remarks", moveLibraryItem.getRemarks()==null ? "" : moveLibraryItem.getRemarks());//备注
			lineTblItem.put("batch", "");// 货物批次
	        lineTblItem.put("area",voluem);// 单件货物⾯积
			lineTblItem.put("weight", weight);//单件货物重量
			lineTblItem.put("length",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⻓度
			lineTblItem.put("width",product.getErp_width()==null ? "" :product.getErp_width());//单件货物宽度
			lineTblItem.put("height",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⾼度
			lineTblItem.put("price","");//货物单价
			if(flag==0){
				lineTblItem.put("num",moveLibraryItem.getQuantity());//货物数量
				lineTblItem.put("aidedNum",moveLibraryItem.getBranchQuantity());//辅数量（⽀数）
				lineTblItem.put("packageNum",moveLibraryItem.getBoxQuantity());//货物件数
			}else{
				lineTblItem.put("num",moveLibraryItem.getIssueQuantity());//货物数量
				lineTblItem.put("aidedNum",moveLibraryItem.getIssueBranchQuantity());//辅数量（⽀数）
				lineTblItem.put("packageNum",moveLibraryItem.getIssueBoxQuantity());//货物件数
			}
			
			
			lineTblItem.put("org_id",moveLibrary.getIssueWarehouse().getStockSystemDict().getRemark());//库存组织
			lineTblItem.put("branch_per_box",product.getBranchPerBox()==null?"":product.getBranchPerBox());//每箱支数
			lineTblItem.put("per_box",product.getPerBox());// 每支单位数 
			lineTblItem.put("per_branch",product.getPerBranch());//每箱单位数
			lineTblItem.put("aidedUnit",product.getSpecTwo());//辅单位（个）
			lineTblItem.put("packageUnit","箱");//件数单位
			lineTblItem.put("category",product.getProductCategory().getName());//分类
			lineTblItem.put("oldSN","");//旧物料编码
			lineTblItem.put("backupColumn1","");//备用1
			lineTblItem.put("backupColumn2","");//备用2
			lineTblItem.put("backupColumn3","");//备用3
			lineTblItem.put("backupColumn4","");//备用4
			lineTblItem.put("unit",product.getUnit());//数量单位
			lineTblItem.put("erpMemo",moveLibraryItem.getErpMemo());//ERP备注
			
		      /**********************AM add by liuxy 2020-12-21*********************/
            lineTblItem.put("businessOrgName",moveLibraryItem.getProductOrganization().getName());//经营组织
//            lineTblItem.put("grade",moveLibraryItem.getProductLevel().getValue());//等级
//            lineTblItem.put("colorNo",moveLibraryItem.getColourNumbers().getValue());//色号
//            lineTblItem.put("moistureContent",moveLibraryItem.getMoistureContents().getValue());//含水率
//            lineTblItem.put("newFlag","");//新旧标识
//            lineTblItem.put("batch","");//批次
            lineTblItem.put("storageLocation","");//库位

			lineTbl.add(lineTblItem);

		}
		
		requestInfo.put("items", lineTbl);
		requestMap.put("requestInfo", requestInfo);

		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(requestMap);

		Long companyInfoId = moveLibrary.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
	
		if(flag==0){
			String[] fields = new String[] { moveLibrary.getSn() };
			a1_MessageToService.saveA1_MessageTo(55,
					companyInfo.getUniqueIdentify(),
					requestStr,	
					fields);
		}
//		else{
//			String[] fields = new String[] { moveLibrary.getSn() };
//			a1_MessageToService.saveA1_MessageTo(56,
//					companyInfo.getUniqueIdentify(),
//					requestStr,
//					fields);
//		}
	}
	
	

	/**
	 * 生成 同步 退货单到家哇云的报文到接口表
	 * @param b2bReturn 退货单
	 * @param flag 0-同步
	 */
	public void saveLogisticsReturnIntf(B2bReturns b2bReturn,Integer flag) {
			Warehouse warehouse = b2bReturn.getWarehouse();
			
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
			
			
			
			if (warehouse.getErp_warehouse_code() == null) {
				ExceptionUtil.throwServiceException("仓库【"
						+ warehouse.getName()
						+ "】的外部编码不能为空");
			}
			Organization managementOrganization = warehouse.getManagementOrganization();
			if (managementOrganization == null) {
				ExceptionUtil.throwServiceException("仓库【"
						+ warehouse.getName()
						+ "】的经营组织不能为空");
			}
			else if (managementOrganization.getCode() == null) {
				ExceptionUtil.throwServiceException("仓库【"
						+ warehouse.getName()
						+ "】的经营组织编码不能为空");
			}

//			SystemDict stockSystemDict = warehouse.getStockSystemDict();
//			if (stockSystemDict == null) {
//				ExceptionUtil.throwServiceException("仓库【"
//						+ warehouse.getName()
//						+ "】的库存组织不能为空");
//			}
//			else if (stockSystemDict.getRemark() == null) {
//				ExceptionUtil.throwServiceException("系统词汇，库存组织【"
//						+ stockSystemDict.getValue()
//						+ "】的备注（ERP编号）不能为空");
//			}

			
			Store store = b2bReturn.getStore();

		
			

			Map<String, Object> requestMap = new HashMap<String, Object>();
			Map<String, Object> requestInfo = new HashMap<String, Object>();

			Map<String, Object> esbInfo = new HashMap<String, Object>();
			esbInfo.put("requestTime", DateUtil.convert(new Date(),
					"yyyy-MM-dd HH:mm:ss.SSS"));
			esbInfo.put("attr1", "");
			esbInfo.put("attr2", "");
			esbInfo.put("attr3", "");
			requestMap.put("esbInfo", esbInfo);
			
			Map<String, Object> queryInfo = new HashMap<String, Object>();
			queryInfo.put("pageSize", "");
			queryInfo.put("currentPage", "");

			requestMap.put("queryInfo", queryInfo);
			
			
			if(flag==0){
				requestInfo.put("salesNo", b2bReturn.getSn());//退货单号
				requestInfo.put("shippingId", b2bReturn.getId());// 头ID
				requestInfo.put("customerOrderNo","");// 客户订单号
				requestInfo.put("salesOrderNo","");// 销售单号
				requestInfo.put("erpSn",b2bReturn.getErpSn());// 销售单号
				requestInfo.put("estimatedPickupTime","");// 预计揽货时间
				requestInfo.put("delivery_type", "");// 配送方式
				requestInfo.put("needLogisticsScheduler", "");// 是否需要物流调度协调
				requestInfo.put("customerName", store.getName());// 客户名称
				requestInfo.put("customerAlias", store.getAlias());// 客户别称
				requestInfo.put("division","地板(AM)事业部");// 事业部
				requestInfo.put("type", "AM退货单");// 订单类型
				requestInfo.put("totalPrice",b2bReturn.getAmount());//总售价
				requestInfo.put("totalWeight","");//总重量
				requestInfo.put("startTime",b2bReturn.getCreateDate());// 订单日期
				requestInfo.put("messageType","已登记");// 消息类型
				requestInfo.put("sale_org_name",b2bReturn.getSaleOrg().getName());//机构
				requestInfo.put("projectNo", "");// 项⽬号
				requestInfo.put("projectName", "");// 项⽬名称
				requestInfo.put("remarks", "");// 备注
				requestInfo.put("backupColumn1", "");// 备用1
				requestInfo.put("backupColumn2", "");// 备用2
				requestInfo.put("backupColumn3", "");// 备用3
				requestInfo.put("backupColumn4", "");// 备用4
				requestInfo.put("backupColumn5", "");// 备用5	
				
				if(b2bReturn.getSmethod().equals("普通发运")){
					requestInfo.put("send_type", 1);//配送方式
				}else if(b2bReturn.getSmethod().equals("暂缓发运")){
					requestInfo.put("send_type", 2);//配送方式
				}

				Map<String, Object> lineTblSender = new HashMap<String, Object>();

				
				lineTblSender.put("name", "");
				lineTblSender.put("mobile", "");
				lineTblSender.put("phone", "");
				lineTblSender.put("provinceName", "");
				lineTblSender.put("cityName", "");
				lineTblSender.put("districtName", "");
				lineTblSender.put("address", "");
				lineTblSender.put("spWarehouseCode", warehouse.getErp_warehouse_code());
				
			
				requestInfo.put("receiverInfo", lineTblSender);
				
				Map<String, Object> lineTblReceiver = new HashMap<String, Object>();

				
				lineTblReceiver.put("name", b2bReturn.getConsignee());
				lineTblReceiver.put("mobile", b2bReturn.getMobile());
				lineTblReceiver.put("phone", "");
				
				
				//拆分省市区
				Area area = b2bReturn.getArea();
				String districtName="";
				String cityName="";
				String provinceName="";
			
				if(area!=null && area.getParent()!=null){
					districtName=area.getName();
					cityName=area.getParent().getName();
					provinceName=area.getParent().getParent().getName();
				}else{
					cityName=area.getName();
					provinceName=area.getParent().getName();
				}
				
				lineTblReceiver.put("provinceName",provinceName );
				lineTblReceiver.put("cityName", cityName);
				lineTblReceiver.put("districtName",districtName );
				lineTblReceiver.put("detailAddress", b2bReturn.getAddress());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
				                          
				
				lineTblReceiver.put("spWarehouseCode", "");
				
				requestInfo.put("senderInfo", lineTblReceiver);
				
				
				
			}
//			else{
//				requestInfo.put("salesNo", b2bReturn.getSn());// 发货单号
//				requestInfo.put("shippingId", b2bReturn.getId());// 头ID
//				requestInfo.put("delivery_type", "");// 配送方式
//				requestInfo.put("type", "AM退货单");// 订单类型
//				//由于整单作废SQL事务未提交状态未改变所以写上状态2
//
//
//				requestInfo.put("erpSn", b2bReturn.getErpSn());// erp单号
//				requestInfo.put("stockOutNo", b2bReturn.getSn());// 家哇云出库单号
//				requestInfo.put("subWarehouseID", warehouse.getErp_warehouse_code());// 子库库编码
//				requestInfo.put("totalWeight", "");// 总重量
//				requestInfo.put("fobAddrress", "");// fob地点
//				requestInfo.put("contact", b2bReturn.getConsignee());//联系人
//                requestInfo.put("division","地板(AM)事业部");// 事业部
//				requestInfo.put("startTime", b2bReturn.getErpDate());//实际出库时间
//				requestInfo.put("sale_org_name",b2bReturn.getSaleOrg().getName());//机构
//				requestInfo.put("shippingMethod", "");//发运方式
//				requestInfo.put("shippingNote", "");//发运说明
//				requestInfo.put("summary", "");//摘要
//				requestInfo.put("remarks", "");// 备注
//				if(b2bReturn.getSmethod().equals("普通发运")){
//					requestInfo.put("send_type", 1);//配送方式
//				}else if(b2bReturn.getSmethod().equals("暂缓发运")){
//					requestInfo.put("send_type", 2);//配送方式
//				}
//			}
			
			List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
			for (B2bReturnsItem b2bReturnsItem : b2bReturn.getB2bReturnsItems()) {
				Product product = b2bReturnsItem.getProduct();
				String productGradeName = "";
				if (b2bReturnsItem.getProductLevel() != null) {
					if (!ConvertUtil.isEmpty(b2bReturnsItem.getProductLevel().getValue())) {
						productGradeName = b2bReturnsItem.getProductLevel().getValue();
					}else {
						ExceptionUtil.throwServiceException("产品【"+ b2bReturnsItem.getName()+ "】的物料等级不能为空");
					}
				}
				
				if (b2bReturnsItem.getVonderCode() == null) {
					ExceptionUtil.throwServiceException("产品【"
							+ b2bReturnsItem.getName()
							+ "】的物料编码不能为空");
				}

				
				//计算重量体积
				BigDecimal weight= new BigDecimal("0");
				BigDecimal voluem= new BigDecimal("0");
				
				
				if(flag==0){
					weight=(b2bReturnsItem.getQuantity()==null ? new BigDecimal("0") : b2bReturnsItem.getQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
					voluem=(b2bReturnsItem.getBoxQuantity()==null ? new BigDecimal("0") : b2bReturnsItem.getBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
				}else{
					weight=(b2bReturnsItem.getReturnedQuantity()==null ? new BigDecimal("0") : b2bReturnsItem.getReturnedQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
					voluem=(b2bReturnsItem.getReturnedBoxQuantity()==null ? new BigDecimal("0") : b2bReturnsItem.getReturnedBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
				}
				
			

				
				Map<String, Object> lineTblItem = new HashMap<String, Object>();
	           
				if(b2bReturnsItem.getShippingItem()!=null){
					requestInfo.put("source_id", b2bReturnsItem.getShippingItem().getShipping().getId());// 来源ID
					requestInfo.put("source_item_id", b2bReturnsItem.getShippingItem().getId());// 来源行ID
					requestInfo.put("source_sn", b2bReturnsItem.getShippingItem().getShipping().getErpSn());// 来源单号
				}
				
				
				lineTblItem.put("itemStatus", "");//状态 
				lineTblItem.put("cargoName", product.getName());//货物名称
				lineTblItem.put("shippingItemId", b2bReturnsItem.getId());//行ID
				lineTblItem.put("cargoDescription", product.getDescription());// 货物描述
				lineTblItem.put("model", product.getModel());// 型号
				lineTblItem.put("cargo_no", product.getVonderCode());// 编码
				lineTblItem.put("grade", productGradeName);// 等级
				lineTblItem.put("colorNo", product.getWoodTypeOrColor()==null ? "" : product.getWoodTypeOrColor());// ⾊号
				lineTblItem.put("remarks", b2bReturnsItem.getMemo()==null ? "" : b2bReturnsItem.getMemo());//备注
				lineTblItem.put("batch", "");// 货物批次
		        lineTblItem.put("area",voluem);// 单件货物⾯积
				lineTblItem.put("weight", weight);//单件货物重量
				lineTblItem.put("length",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⻓度
				lineTblItem.put("width",product.getErp_width()==null ? "" :product.getErp_width());//单件货物宽度
				lineTblItem.put("height",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⾼度
				lineTblItem.put("price",b2bReturnsItem.getPrice());//货物单价
				if(flag==0){
					lineTblItem.put("num",b2bReturnsItem.getQuantity());//货物数量
					lineTblItem.put("aidedNum",b2bReturnsItem.getBranchQuantity());//辅数量（⽀数）
					lineTblItem.put("packageNum",b2bReturnsItem.getBoxQuantity());//货物件数
				}else{
					lineTblItem.put("num",b2bReturnsItem.getQuantity());//货物数量
					lineTblItem.put("aidedNum",b2bReturnsItem.getReturnedBranchQuantity());//辅数量（⽀数）
					lineTblItem.put("packageNum",b2bReturnsItem.getReturnedBoxQuantity());//货物件数
				}
				
				lineTblItem.put("branch_per_box",product.getBranchPerBox());//每箱支数
				lineTblItem.put("per_box",product.getPerBox());// 每支单位数 
				lineTblItem.put("per_branch",product.getPerBranch());//每箱单位数
				if(b2bReturnsItem.getShippingItem()!=null){
					lineTblItem.put("source_item_id",b2bReturnsItem.getShippingItem().getId());//发货单行ID
				}
				lineTblItem.put("category",product.getProductCategory().getName());//分类
				lineTblItem.put("unit",product.getUnit());//数量单位
				
				
				lineTblItem.put("aidedUnit",product.getSpecTwo());//辅单位（个）
				lineTblItem.put("org_id",warehouse.getStockSystemDict().getRemark());//库存组织
				lineTblItem.put("packageUnit","箱");//件数单位
				lineTblItem.put("oldSN","");//旧物料编码
				lineTblItem.put("backupColumn1","");//备用1
				lineTblItem.put("backupColumn2","");//备用2
				lineTblItem.put("backupColumn3","");//备用3
				lineTblItem.put("backupColumn4","");//备用4

			      /**********************AM add by liuxy 2020-12-21*********************/
	            lineTblItem.put("businessOrgName",b2bReturnsItem.getProductOrganization().getName());//经营组织
	            lineTblItem.put("grade",b2bReturnsItem.getProductLevel().getValue());//等级
//	            lineTblItem.put("colorNo",b2bReturnsItem.getColorNumbers().getValue());//色号
//	            lineTblItem.put("moistureContent",b2bReturnsItem.getMoistureContents().getValue());//含水率
//	            lineTblItem.put("newFlag","");//新旧标识
//	            lineTblItem.put("batch","");//批次
//	            lineTblItem.put("storageLocation","");//库位
	            
	             /**********************AM add by liuxy 2020-12-21*********************/
	            
				lineTbl.add(lineTblItem);

				
			}
			
			requestInfo.put("items", lineTbl);
			requestMap.put("requestInfo", requestInfo);

			/** 请求的接口数据 */
			String requestStr = JsonUtils.toJson(requestMap);

			Long companyInfoId = b2bReturn.getCompanyInfoId();
			CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		

			/** 写入接口表 */
			if(flag==0){
				String[] fields = new String[] { b2bReturn.getSn() };
				a1_MessageToService.saveA1_MessageTo(53,
						companyInfo.getUniqueIdentify(),
						requestStr,
						fields);
			}
//			else{
//				String[] fields = new String[] { b2bReturn.getSn() };
//				a1_MessageToService.saveA1_MessageTo(54,
//						companyInfo.getUniqueIdentify(),
//						requestStr,
//						fields);
//			}
		}

	/**
	 * 生成 同步 发货通知单到ERP的报文到接口表
	 * @param shipping 发货通知单
	 */
	public void saveShippingIntf(Shipping shipping) {

		Warehouse warehouse = shipping.getWarehouse();
		if (warehouse.getErp_warehouse_code() == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的外部编码不能为空");
		}
		Organization managementOrganization = warehouse.getManagementOrganization();
		if (managementOrganization == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的经营组织不能为空");
		}
		else if (managementOrganization.getCode() == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的经营组织编码不能为空");
		}

		SystemDict stockSystemDict = warehouse.getStockSystemDict();
		if (stockSystemDict == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的库存组织不能为空");
		}
		else if (stockSystemDict.getRemark() == null) {
			ExceptionUtil.throwServiceException("系统词汇，库存组织【"
					+ stockSystemDict.getValue()
					+ "】的备注（ERP编号）不能为空");
		}

		Order mOrder = shipping.getShippingItems()
				.get(0)
				.getOrderItem()
				.getOrder();
		Store store = shipping.getStore();
		//获取当前订单财务审核人员（多个取最早那一个）
		String userid = "";
		Wf wf = wfBaseService.find(mOrder.getWfId());
		List<Map<String, Object>> opinions = wfUserOpinionBaseService.findListByWf(wf.getId(),
				null);
		for (Map<String, Object> map : opinions) {
			if (map.get("proc_name").toString().contains("财务审核")
					&& "7".equals(map.get("stat").toString())) {
				userid = map.get("userid") == null ? "" : map.get("userid")
						.toString();
				break;
			}
		}
		StoreMember user = null;
		if (userid != null&&userid.trim().length()>0) {
			user = storeMemberBaseService.find(Long.valueOf(userid));
		}

		if (store.getOutTradeNo() == null) {
			ExceptionUtil.throwServiceException("客户【"
					+ store.getName()
					+ "】的外部编号不能为空");
		}

		SystemDict businessType = mOrder.getBusinessType();
		if (businessType == null) {
			ExceptionUtil.throwServiceException("订单【"
					+ mOrder.getSn()
					+ "】的业务类型不能为空");  
		}
		Sbu sbu = shipping.getSbu();
		if (sbu == null) {
			ExceptionUtil.throwServiceException("发货【"
					+ shipping.getSn()
					+ "】的SBU不能为空");
		}
		else if (sbu.getUndefined1() == null) {
			ExceptionUtil.throwServiceException("SBU【"
					+ sbu.getName()
					+ "】的备注（ERP编号）不能为空");
		}
		SystemDict saleOrgType=null;
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "SaleOrgType"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("id", shipping.getSaleOrg().getType()));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		if(freightChargeTypes.size()>0){
			 saleOrgType =freightChargeTypes.get(0);
		}

		if (shipping.getAddressOutTradeNo() == null) {
			ExceptionUtil.throwServiceException("地址外部编码不能为空");
		}

		Map<String, Object> requestMap = new HashMap<String, Object>();

		requestMap.put("SOURCE_CODE", "CRM");// 来源系统代码,固定“CRM”
		requestMap.put("SOURCE_NUM", shipping.getId());// 来源ID,CRM发货通知单头id
		requestMap.put("ORG_ID", managementOrganization.getCode());// 经营组织id,根据仓库的经营组织id来取
		requestMap.put("ORDER_TYPE", "零售销售订单");// 订单类型,传名称：零售销售订单、零售退货订单
		requestMap.put("ATTRIBUTE19", shipping.getSmethod() == null ? ""
				: shipping.getSmethod());// 发运方式
		requestMap.put("BUSSINESS_TYPE", businessType.getValue());// 业务类型,客制化字段，传名称：经销商零售、商业地板、直营零售、家装、电商
		requestMap.put("CUSTOMER_NUMBER", store.getOutTradeNo());// 客户编码
		requestMap.put("SOURCE_SHIP_TO_ADDRESS", store.getOutTradeNo()
				+ shipping.getAddressOutTradeNo());// 收货地址id,CRM客户收货地址id
		requestMap.put("SOURCE_BILL_TO_ADDRESS", store.getOutTradeNo()
				+ shipping.getAddressOutTradeNo());// 收单地址id,CRM客户收单地址id
		requestMap.put("SALESREP", "");// 销售人员,ERP存储待定
		requestMap.put("TERMS", "立即");// 付款条件,传：立即
		requestMap.put("CUST_PO_NUMBER", shipping.getSn());// 客户PO,CRM发货通知单号
		requestMap.put("ORDERED_DATE",
				DateUtil.convert(shipping.getCheckDate(), "yyyy-MM-dd"));// 订单日期,格式按照ESB标准，当前日期，2018-07-11
		requestMap.put("SBU", sbu.getUndefined1());// SBU,订单头ATTRIBUTE10，家居头弹性域，传代码，CRM从机构属性里面取
												
		requestMap.put("TRANSACTIONAL_CURR_CODE", "CNY");// 币种,传:CNY
		requestMap.put("SHIPPING_METHOD", "");// 发运方式,海运、汽运、铁运、海铁联运，值待补充
		requestMap.put("FREIGHT_CARRIER", shipping.getDelivery() == null ? ""
				: shipping.getDelivery().getName());// 承运商名称
		requestMap.put("DRIVER", shipping.getDriverInfoName() == null ? ""
				: shipping.getDriverInfoName());// 司机姓名,客制化字段
		requestMap.put("PHONE_NUMBER",
				shipping.getDriverInfoMobile() == null ? ""
						: shipping.getDriverInfoMobile());// 司机手机号,客制化字段
		requestMap.put("LICENSE_PLATE", shipping.getCarNumber() == null ? ""
				: shipping.getCarNumber());// 车号/车牌号,客制化字段
		requestMap.put("ORDER_NOTES", "");// 订单备注,客制化字段
		requestMap.put("SHIPPING_INSTRUCTIONS", shipping.getMemo() == null ? ""
				: shipping.getMemo() + "----" + shipping.getShippingMethod());// 发货备注,客制化字段
		requestMap.put("ERP_USER_NAME", shipping.getStoreMember().getUsername());// 创建人,创建人工号，对应FND_USER表
		requestMap.put("ATTRIBUTE16", mOrder.getSn());// 关联订单号,CRM销售订单号（改：取发货单号）//改回销售订单号
		requestMap.put("ATTRIBUTE17", "");// 地区,家居头弹性域，传CRM的客户所属机构名称
		requestMap.put("ATTRIBUTE3", "");// 工厂完工入库日期,全局头弹性域，不传
		if(shipping.getSbu().getId()==3){
			requestMap.put("ATTRIBUTE4", shipping.getIsPurchase() == null ? "" : shipping.getIsPurchase());
		}else{
			requestMap.put("ATTRIBUTE4", user == null ? "" : user.getUsername());// 财务审核人员工号
		}
		if(mOrder.getDistributor()!=null){
			requestMap.put("ATTRIBUTE2", mOrder.getDistributor().getName() == null ? "" : mOrder.getDistributor().getName());// 经销商名称
		}else{
			requestMap.put("ATTRIBUTE2", "");// 经销商名称
		}
		
		
		requestMap.put("ATTRIBUTE5", "");// 工程项目类型,全局头弹性域，不传
		requestMap.put("ATTRIBUTE15", "");// 是否已经发送发货通知,全局头弹性域，不传
		requestMap.put("ATTRIBUTE6", "");// 起始发货地点,全局头弹性域，不传
		requestMap.put("ATTRIBUTE11", "");// 工程项目编号,家居头弹性域，不传
		requestMap.put("ATTRIBUTE12", "");// 交货日期,家居头弹性域，不传
		requestMap.put("ATTRIBUTE13", "");// 订单来源,家居头弹性域，不传
		requestMap.put("ATTRIBUTE14", "");// 订单来源备注,家居头弹性域，不传
		requestMap.put("ATTRIBUTE18", "");// 合同名称,家居头弹性域，不传
//		requestMap.put("ATTRIBUTE19", "");// 分派工厂,家居头弹性域，不传
		requestMap.put("ATTRIBUTE20", "");// 是否内部交易,家居头弹性域，不传

		List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
		for (ShippingItem shippingItem : shipping.getShippingItems()) {
			Product product = shippingItem.getProduct();
			OrderItem orderItem = shippingItem.getOrderItem();
			String productGradeName = "";

			if (orderItem != null && orderItem.getProductLevel() != null) {
				if (!ConvertUtil.isEmpty(orderItem.getProductLevel().getValue())) {
					productGradeName = orderItem.getProductLevel().getValue();
				}else{
					ExceptionUtil.throwServiceException("产品【"+ shippingItem.getName()+ "】的物料等级不能为空");
				}
			}

			if (shippingItem.getVonderCode() == null) {
				ExceptionUtil.throwServiceException("产品【"
						+ shippingItem.getName()
						+ "】的物料编码不能为空");
			}

			/*
			 * if (shippingItem.getBranchQuantity() == null) {
			 * ExceptionUtil.throwServiceException("产品【" +
			 * shippingItem.getName() + "】的支数不能为空"); }
			 */
			if (!"壁纸".equals(shipping.getSbu().getName())) {
				if (shippingItem.getBranchQuantity() == null) {
					ExceptionUtil.throwServiceException("产品【"
							+ shippingItem.getName()
							+ "】的支数不能为空");
				}
			}

			Map<String, Object> lineData = new HashMap<String, Object>();
			Map<String, Object> lineTblItem = new HashMap<String, Object>();

			lineTblItem.put("SOURCE_CODE", "CRM");// 来源系统代码,CRM
			lineTblItem.put("SOURCE_NUM", shipping.getId());// 来源ID,CRM发货通知单头id
			lineTblItem.put("SOURCE_LINE_NUM", shippingItem.getId());// 来源行ID,CRM发货通知单行id
			lineTblItem.put("ATTRIBUTE9", shippingItem.getMemo() == null ? ""
					:shippingItem.getMemo());// 备注
			lineTblItem.put("ORDERED_ITEM", shippingItem.getVonderCode());// 物料编码
			lineTblItem.put("MATERIAL_GRADE", productGradeName);// 物料等级,订单行ATTRIBUTE2，传文本：优等品、二等品
			lineTblItem.put("ORDERED_QUANTITY", shippingItem.getQuantity());// 数量（平方数）

			BigDecimal a = shippingItem.getQuantity();
			a = a.multiply(new BigDecimal("1000000.0"));
			a = a.divideAndRemainder(new BigDecimal("10"))[1];
			if (a.compareTo(new BigDecimal("0.0")) != 0
					&& (shippingItem.getPerBranch() == null || shippingItem.getPerBranch()
							.compareTo(new BigDecimal("0")) == 0)) {

//				returnMsg="产品编码【"
//						+ shippingItem.getVonderCode()
//						+ "】不存在每支单位数，该数量小数位最多五位";
//				return returnMsg;

				/*
				 * ExceptionUtil.throwServiceException("产品编码【" +
				 * shippingItem.getVonderCode() + "】不存在每支单位数，该数量小数位最多五位");
				 */

				if (!"壁纸".equals(shipping.getSbu().getName())) {
					ExceptionUtil.throwServiceException("产品编码【"
							+ shippingItem.getVonderCode()
							+ "】不存在每支单位数，该数量小数位最多五位");
				}

			}

			lineTblItem.put("ORDER_QUANTITY_UOM", product.getUnit());// 单位（平方）,m2
			if (product.getSpecTwo() != null
					&& !product.getSpecTwo().equals("")) {
				lineTblItem.put("ORDERED_QUANTITY2",
						shippingItem.getBranchQuantity());// 数量（支）
			}
			else {
				lineTblItem.put("ORDERED_QUANTITY2", 0);// 数量（支）
			}

			lineTblItem.put("ORDERED_QUANTITY_UOM2",
					product.getSpecTwo() == null ? "" : product.getSpecTwo());// 辅助单位（支）,支
			//合资控股
	 		if (saleOrgType.getValue().equals("控股合资管理中心")) {// 控股合资管理中心
				lineTblItem.put("UNIT_SELLING_PRICE",
						orderItem.getSaleOrgPrice() == null ? 0
								: orderItem.getSaleOrgPrice());// 单价（平方）,2位小数
			}
			else {
				lineTblItem.put("UNIT_SELLING_PRICE", orderItem.getPrice());// 单价（平方）,2位小数
			}
			lineTblItem.put("SHIP_FROM", warehouse.getErp_warehouse_code());// 仓库代码,取CRM主表仓库代码
			lineTblItem.put("ORGANIZATION_ID", stockSystemDict.getRemark());// 库存组织id,根据仓库的库存组织id来取
			lineTblItem.put("SCHEDULE_ARRIVAL_DATE",
					DateUtil.convert(shipping.getShippingTime(), "yyyy-MM-dd"));// 交货日期,取CRM主表交货日期
			lineTblItem.put("ATTRIBUTE17",
					shippingItem.getBoxQuantity() == null ? ""
							: shippingItem.getBoxQuantity());//箱数,全局行弹性域
//			System.out.println("a"+shippingItem.getBranchQuantity());
//			System.out.println("b"+product.getBranchPerBox());
//			if (shippingItem != null
//					&& shippingItem.getBranchQuantity() != null
//					&& product != null
//					&& product.getBranchPerBox().compareTo(BigDecimal.ZERO)==1
//					) {
//				if(product.getBranchPerBox() != null && product.getBranchPerBox().compareTo(BigDecimal.ZERO)==1){
//					lineTblItem.put("ATTRIBUTE17",
//							shippingItem.getBranchQuantity()
//							.divide(product.getBranchPerBox(),
//									6,
//									BigDecimal.ROUND_HALF_UP));// 箱数改为支数/每箱支数					
//				}else{
//					lineTblItem.put("ATTRIBUTE17",
//							shippingItem.getBranchQuantity());
//				}
//			}

			lineTblItem.put("ATTRIBUTE18", shipping.getConsignee()
					+ ";"
					+ shipping.getPhone());// 联系人信息,全局行弹性域，传CRM主表收货人姓名和电话
											// 前两段为姓名和电话用“；”分隔，
											// 比如
											// “李工;16543060061;;;;;”
			lineTblItem.put("LINE_TYPE", "");// 订单行类型,不传
			lineTblItem.put("ATTRIBUTE3", "");// 物料色号,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE1", "");// 合同编号,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE19", "");// 实际收货数量,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE20", "");// 是否已经同步,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE4",
					shipping.getInvoiceTitle() == null ? ""
							: shipping.getInvoiceTitle());// 发票抬头,全局行弹性域，不传
//			lineTblItem.put("ATTRIBUTE5","");
//			lineTblItem.put("ATTRIBUTE6","");
			lineTblItem.put("ATTRIBUTE5", orderItem.getDiscountCode()==null?"":orderItem.getDiscountCode().getValue());// 折扣方案
			lineTblItem.put("ATTRIBUTE6", orderItem.getDiscount()==null?"":orderItem.getDiscount().toString());// 折扣率
			lineTblItem.put("ATTRIBUTE12", orderItem.getSaleOrgPrice()==null?"":orderItem.getSaleOrgPrice());//平台结算价
			
			lineTblItem.put("ATTRIBUTE7", "");// 门类别,家居行弹性域，不传
			lineTblItem.put("ATTRIBUTE8", "");// 木门玻璃型号,家居行弹性域，不传
			lineTblItem.put("ATTRIBUTE10", "");
			lineTblItem.put("ATTRIBUTE11", "");
			lineData.put("LINE_TBL_ITEM", lineTblItem);
			lineTbl.add(lineData);
		}
		LogUtils.error("111" + lineTbl.size());
		requestMap.put("LINE_TBL", lineTbl);

		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(requestMap);

		Long companyInfoId = shipping.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

		/** 写入接口表 */
		String[] fields = new String[] { shipping.getSn() };
		a1_MessageToService.saveA1_MessageTo(0,
				companyInfo.getUniqueIdentify(),
				requestStr,
				fields);

	}



	/**
	 * 	家哇云单据接口
	 * @param a1_MessageTo 定时接口表推送记录
	 * @throws Exception 接口异常
	 */
	@SuppressWarnings("unchecked")
	public void pushLogisticsJwy(A1_MessageTo a1_MessageTo) throws Exception {

		String requestStr = a1_MessageTo.getData();

		System.out.println("requestStr:"+requestStr);
		Map<String,String> headers= new  HashMap<String,String>();
	
		headers.put("Authorization",getHeader());
		headers.put("Content-Type", "application/json;charset=utf-8");
		System.out.println("header:"+getHeader());
		

		String returnMsgs=HttpUtil.send("http://*************:8083/api/v1/Link/DeliveryInstructions/Sync", headers, requestStr);//测试
//		String returnMsgs=HttpUtil.send("http://**************:8083/api/v1/Link/DeliveryInstructions/Sync", headers, requestStr);//正式




		//解析出是否撤回操作
		Map<String, Object> requestStrParams = JsonUtils.toObject(requestStr, Map.class);
		Map<String, Object> requestInfo = JSONObject.parseObject(JSON.toJSONString(requestStrParams.get("requestInfo")));
		Integer OperationStatus = (Integer)requestInfo.get("status");
		Map<String, Object> params = JsonUtils.toObject(returnMsgs, Map.class);
		Object esbInfo = params.get("esbInfo");
		Map<String, Object> esbInfoParams = JSONObject.parseObject(JSON.toJSONString(esbInfo));
		String returnStatus = (String) esbInfoParams.get("returnStatus");
		String returnMsg = (String) esbInfoParams.get("returnMsg");

		// 返回结果处理
		String hType = "2";
		String errorMsg = null;

		if (!"S".equals(returnStatus)) {
			hType = "1";
			errorMsg = returnMsg;
		}

		//出入库撤回回传成功后，将出入库的单据作废
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("sn", a1_MessageTo.getField1()));
		List<AmShipping> amShippingLists = amShippingService.findList(null, filters, null);
		if(amShippingLists.size()>0){
			AmShipping amShipping=amShippingLists.get(0);
			//amShiiping的status 6：待发货，2：作废
			if(amShipping.getStatus() == 6 && "S".equals(returnStatus) && OperationStatus == 2){
                //操作类型
                List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "作废");
                if(actionTypeList.isEmpty() || actionTypeList.size()==0){
                    ExceptionUtil.throwServiceException("操作类型不能为空");
                }
                //加载库存
                warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
                amShipping.setStatus(2);
                for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
                    amShippingItem.setStatus(2);
					amShippingItemService.update(amShippingItem);
                }
                amShippingService.update(amShipping);
			}if ("E".equals(returnStatus) && OperationStatus == 2) {
                LogUtils.info("单号为"+a1_MessageTo.getField1()+"出入库单撤回失败");
                ExceptionUtil.throwServiceException("单号为"+a1_MessageTo.getField1()+"出入库单撤回失败");
            }
		}

        //出入库推送成功后，将出入库的单据改写成待发货
        if(amShippingLists.size()>0) {
            AmShipping amShipping = amShippingLists.get(0);
            if ("S".equals(returnStatus) && OperationStatus == 0) {
                List<AmShippingItem> amShippingItemList = amShipping.getAmShippingItems();
                for (AmShippingItem amShippingItem : amShippingItemList) {
                    amShippingItem.setStatus(6);
                    amShippingItemService.update(amShippingItem);
                }

                amShipping.setStatus(6);
                amShippingService.update(amShipping);
            } else if ("E".equals(returnStatus) && OperationStatus == 0) {
                LogUtils.info("单号为"+a1_MessageTo.getField1()+"出入库单推送家哇云失败");
                ExceptionUtil.throwServiceException("单号为"+a1_MessageTo.getField1()+"出入库单推送家哇云失败");
            }
        }

		// 写历史表
		saveHistory(a1_MessageTo, hType, returnMsg, errorMsg);

	}


	/**
	 * 家哇云推送单据接口
	 * 	@param a1_MessageTo – 定时接口表推送记录
	 *  @throws Exception – 接口异常
	 */
	@SuppressWarnings("unchecked")
	public void pushLogisticsShippedJwy(A1_MessageTo a1_MessageTo) throws Exception {
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		String requestStr = a1_MessageTo.getData();
		System.out.println("requestStr:"+requestStr);
 
		Map<String,String> headers= new  HashMap<String,String>();
		
		headers.put("Authorization",getHeader());
		headers.put("Content-Type", "application/json;charset=utf-8");

		String returnMsgs=HttpUtil.send("http://*************:8083/api/v1/Link/DeliveryInstructionsReverse/Sync", headers, requestStr);//测试
		
//		String returnMsgs=HttpUtil.send("http://**************:8083/api/v1/Link/DeliveryInstructionsReverse/Sync", headers, requestStr);//正式

		Map<String, Object> params = JsonUtils.toObject(returnMsgs, Map.class);
		String returnStatus = (String) params.get("returnStatus");
		String returnMsg = (String) params.get("returnMsg");

		// 返回结果处理
		String hType = "2";
		String errorMsg = null;

		if (!"S".equals(returnStatus)) {
			hType = "1";
			errorMsg = returnMsg;
		}

		// 写历史表
		saveHistory(a1_MessageTo, hType, returnMsg, errorMsg);

	}
	

	
	@SuppressWarnings("unchecked")
	public void pushShippingToErp(A1_MessageTo a1_MessageTo) throws Exception {
		String requestStr = a1_MessageTo.getData();
		Map<String, Object> params = JsonUtils.toObject(requestStr, Map.class);
		Map<String, Object> result = new NatureOrderCreate().createOrder(params);
		String returnMsg = (String) result.get("returnMsg");
		String resultXml = (String) result.get("resultXml");

		// 返回结果处理
		String hType = "2";
		String errorMsg = null;

		if (!"S".equals(returnMsg)) {
			hType = "1";
			errorMsg = returnMsg;
		}

		// 写历史表
		saveHistory(a1_MessageTo, hType, resultXml, errorMsg);

	}

	@SuppressWarnings("unchecked")
	public List<Map<String, Object>> syncContract(Map<String, Object> requestData, String companytoken) throws Exception {

		CompanyInfo comapnyInfo = companyInfoBaseService.findByUniqueIdentify(companytoken);
		if (comapnyInfo == null) {
			ExceptionUtil.throwServiceException("企业唯一标识【"
					+ companytoken
					+ "】不存在");
		}

		List<Map<String, Object>> data = (List<Map<String, Object>>) requestData.get("data");
		List<Map<String, Object>> errorDataList = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> itemData : data) {
			String contract_no = null;
			try {
				contract_no = (String) itemData.get("contract_no");
				this.saveConcart(itemData, comapnyInfo);

				Map<String, Object> errorData = this.getContractResultJson(2,
						"成功",
						contract_no);
				errorDataList.add(errorData);

			}
			catch (Exception e) {
				LogUtils.error(e);
				String msg = e.getMessage();
				Map<String, Object> errorData = this.getContractResultJson(1,
						msg,
						contract_no);
				errorDataList.add(errorData);
			}
		}

		String[] fileds = new String[] {};

		String dataJson = JsonUtils.toJson(requestData);
		A1_MessageTo intftable = a1_MessageToService.saveA1_MessageTo(1,
				companytoken,
				dataJson,
				fileds);

		this.saveHistory(intftable, "2", JsonUtils.toJson(errorDataList), null);

		return errorDataList;
	}

	public Map<String, Object> getContractResultJson(int success, String msg,
			String contract_no) throws UnsupportedEncodingException {
		Map<String, Object> resutlt = new HashMap<String, Object>();

		resutlt.put("success", success);
		resutlt.put("msg", msg);
		resutlt.put("contract_no", contract_no);
		return resutlt;

	}

	public void saveConcart(Map<String, Object> data, CompanyInfo comapnyInfo) {

		String companytoken = comapnyInfo.getUniqueIdentify();
		Long companyInfoId = comapnyInfo.getId();

		// 合同类型
		String contract_type = (String) data.get("contract_type");
		if (ConvertUtil.isEmpty(contract_type)) {
			ExceptionUtil.throwServiceException("合同类型不能为空");
		}

		// 销售体系
		String org_name_tx = (String) data.get("org_name_tx");
		if (ConvertUtil.isEmpty(org_name_tx)) {
			ExceptionUtil.throwServiceException("销售体系不能为空");
		}
		// 合同编号
		String contract_no = (String) data.get("contract_no");
		if (ConvertUtil.isEmpty(contract_no)) {
			ExceptionUtil.throwServiceException("合同编号不能为空");
		}
		// 客户
		String store_name = (String) data.get("cust_name_qyf");
		if (ConvertUtil.isEmpty(store_name)) {
			ExceptionUtil.throwServiceException("客户名称不能为空");
		}
		Store store = findStore(store_name, companyInfoId);
		if (store == null) {
			ExceptionUtil.throwServiceException("客户【" + store_name + "】不存在");
		}

		// 项目名称
		String xmmc = (String) data.get("xmmc");
		if (ConvertUtil.isEmpty(xmmc)) {
			ExceptionUtil.throwServiceException("项目名称不能为空");
		}
		// 项目号
		String xm_no = (String) data.get("xm_no");
		if (ConvertUtil.isEmpty(xm_no)) {
			ExceptionUtil.throwServiceException("项目号不能为空");
		}
		// 项目地址
		String xmdz = (String) data.get("xmdz");
		if (ConvertUtil.isEmpty(xmdz)) {
			ExceptionUtil.throwServiceException("项目地址不能为空");
		}
		// 是否异地
		String is_yd_str = null;
		if (ConvertUtil.isEmpty(data.get("is_yd"))) {
			ExceptionUtil.throwServiceException("是否异地不能为空");
		}
		else {
			is_yd_str = data.get("is_yd").toString();
		}
		Boolean is_yd = null;
		if ("否".equals(is_yd_str)) {
			is_yd = false;
		}
		else if ("是".equals(is_yd_str)) {
			is_yd = true;
		}
		else {
			ExceptionUtil.throwServiceException("是否异地格式错误");
		}

		// 合同金额
		String contract_amt_str = (String) data.get("contract_amt");
		if (ConvertUtil.isEmpty(contract_amt_str)) {
			ExceptionUtil.throwServiceException("合同金额不能为空");
		}
		BigDecimal contract_amt = null;
		try {
			contract_amt = new BigDecimal(contract_amt_str);
		}
		catch (Exception e) {
			ExceptionUtil.throwServiceException("合同金额格式错误");
		}
		if (contract_amt.doubleValue() <= 0) {
			ExceptionUtil.throwServiceException("合同金额不能小于等于0");
		}

		Contract contract = new Contract();

		contract.setCompanyInfoId(companyInfoId);
		contract.setContract_type(contract_type);
		contract.setOrg_name_tx(org_name_tx);
		contract.setContract_no(contract_no);
		contract.setStore(store);
		contract.setXmmc(xmmc);
		contract.setXm_no(xm_no);
		contract.setXmdz(xmdz);
		contract.setIs_yd(is_yd);
		contract.setContract_amt(contract_amt);

		List<Map<String, Object>> product_list = (List<Map<String, Object>>) data.get("product_list");
		List<ContractItem> contractItems = contract.getContractItems();
		for (int i = 0; i < product_list.size(); i++) {

			Map<String, Object> itemData = product_list.get(i);

			// 产品编码
			String vonderCode = (String) itemData.get("item_code");
			if (ConvertUtil.isEmpty(vonderCode)) {
				ExceptionUtil.throwServiceException("产品编码不能为空");
			}
			Product product = findProduct(vonderCode, companyInfoId);
			if (product == null) {
				ExceptionUtil.throwServiceException("不存在产品编码为"
						+ vonderCode
						+ "】的产品");
			}

			String model = product.getModel();
			String name = product.getName();
			// 数量
			String quantity_str = (String) itemData.get("qty");
			if (ConvertUtil.isEmpty(quantity_str)) {
				ExceptionUtil.throwServiceException("数量不能为空");
			}
			BigDecimal quantity = null;
			try {
				quantity = new BigDecimal(quantity_str);
			}
			catch (Exception e) {
				ExceptionUtil.throwServiceException("数量格式错误");
			}

			// 单价
			String price_str = (String) itemData.get("price");
			if (ConvertUtil.isEmpty(price_str)) {
				ExceptionUtil.throwServiceException("单价不能为空");
			}
			BigDecimal price = null;
			try {
				price = new BigDecimal(price_str);
			}
			catch (Exception e) {
				ExceptionUtil.throwServiceException("数量格式错误");
			}

			// 备注
			String memo = (String) itemData.get("item_bate");

			ContractItem contractItem = new ContractItem();
			contractItem.setContract(contract);
			contractItem.setProduct(product);
			contractItem.setVonderCode(vonderCode);
			contractItem.setModel(model);
			contractItem.setName(name);
			contractItem.setQuantity(quantity);
			contractItem.setPrice(price);
			contractItem.setMemo(memo);
			contractItem.setCompanyInfoId(companyInfoId);

			contractItems.add(contractItem);
		}

		// 付款期次
		List<Map<String, Object>> pay_list = (List<Map<String, Object>>) data.get("pay_list");
		List<ContractPaymentBatch> contractPaymentBatchs = contract.getContractPaymentBatchs();
		for (int i = 0; i < pay_list.size(); i++) {

			Map<String, Object> payData = pay_list.get(i);

			// 序号
			Integer seq = null;
			if (payData.get("sale_seq") == null) {
				ExceptionUtil.throwServiceException("序号不能为空");
			}
			else {
				seq = new Integer(payData.get("sale_seq").toString());
			}

			// 名称
			String name = (String) payData.get("sale_name");
			if (name == null) {
				ExceptionUtil.throwServiceException("付款描述不能为空");
			}

			// 定金比例
			BigDecimal rate = null;
			if (payData.get("sale_amt") == null) {
				ExceptionUtil.throwServiceException("定金比例不能为空");
			}
			else {
				rate = new BigDecimal(payData.get("sale_amt").toString());
			}

			// 收款期限/月
			String lastMonth = (String) payData.get("sale_month");
			if (lastMonth == null) {
				ExceptionUtil.throwServiceException("收款期限不能为空");
			}

			ContractPaymentBatch contractPaymentBatch = new ContractPaymentBatch();
			contractPaymentBatch.setName(name);
			contractPaymentBatch.setRate(rate);
			contractPaymentBatch.setLastMonth(lastMonth);
			contractPaymentBatch.setContract(contract);
			contractPaymentBatch.setCompanyInfoId(companyInfoId);

			contractPaymentBatchs.add(contractPaymentBatch);
		}

		contract.setSn(SnUtil.generateSn());
		contractService.save(contract);

	}

	@Transactional(readOnly = true)
	public Product findProduct(String vonderCode, Long companyInfoId) {
		List<Filter> filters = new ArrayList<Filter>();
		List<Product> products = productBaseService.findList(null,
				filters,
				null);
		int size = products.size();
		Product product = null;
		if (size > 0) {
			product = products.get(0);
		}
		return product;
	}

	@Transactional(readOnly = true)
	public Store findStore(String name, Long companyInfoId) {
		List<Filter> filters = new ArrayList<Filter>();
		List<Store> stores = storeBaseService.findList(null, filters, null);
		int size = stores.size();
		Store store = null;
		if (size > 0) {
			store = stores.get(0);
		}
		return store;
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findProductPageForTJInft(Date begin_date,
			Date end_date, Long companyInfoId, Pageable pageable) {
		// TODO 自动生成的方法存根
		return a1_MessageToDao.findProductPageForTJInft(begin_date,
				end_date,
				companyInfoId,
				pageable);
	}

	@Override
	public void saveStoreIntf(Store store, StoreAddress storeAddress,
			Integer flag) {//唯一标识：客户ERP编码+地址外部编码（store_address的id,两者是相同的值）
		String storeName = store.getName();
		List<Filter> filters = new ArrayList<Filter>();
		if (storeName == null) {
			ExceptionUtil.throwServiceException("客户名称不能为空");
		}
		/*
		 * if (store.getType() == null)
		 * ExceptionUtil.throwServiceException("客户类型不能为空");
		 */
		if (store.getMemberRank() == null) {
			ExceptionUtil.throwServiceException("价格类型不能为空");
		}
		Map<String, Object> requestMap = new HashMap<String, Object>();

		requestMap.put("SOURCE_CODE", "CRM");// 来源系统代码,固定“CRM”
		requestMap.put("SOURCE_NUM", store.getId());//
		requestMap.put("CUSTOMER_NUMBER", store.getOutTradeNo());// 客户编码
		requestMap.put("CUSTOMER_NAME", store.getName());//
		requestMap.put("KNOW_AS", store.getAlias());// 客户简称
		filters.add(Filter.eq("name", store.getSbu() == null ? ""
				: store.getSbu().getValue()));
		filters.add(Filter.eq("status", true));
		Sbu sbu = sbuService.find(filters);
		requestMap.put("CUSTOMER_CLASS_NAME", sbu != null ? sbu.getUndefined2()
				: "");//
		requestMap.put("CUSTOMER_TYPE", "Y");//
		requestMap.put("TAX_REFERENCE", "");//
		requestMap.put("CUSTOMER_STATUS", store.getIsEnabled() ? "Active"
				: "Inactive");//
		requestMap.put("CUSTOMER_ATTRIBUTE4", "");//
		requestMap.put("CUSTOMER_ATTRIBUTE5", "");//
		SaleOrg saleOrg = saleOrgBaseService.find(store.getSaleOrg().getId());
		requestMap.put("MARKETING", saleOrg.getName());// 组织
		/*
		 * filters.clear(); filters.add(Filter.eq("code", saleOrg.getType()));
		 * filters.add(Filter.isNotNull("parent")); List<SystemDict>
		 * saleOrgTypes = systemDictBaseService.findList(null, filters, null);
		 */
		requestMap.put("ORGANIZATION_TYPE", "运营中心");//
		// requestMap.put("erp_user_name", store.getSn());//
		requestMap.put("USER_NAME", store.getCreateBy().getUsername());//01005584

		List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
		if (storeAddress != null) {
//			for (int i = 0; i < store.getStoreAddress().size(); i++) {
			Map<String, Object> lineData = new HashMap<String, Object>();
			Map<String, Object> lineTblItem = new HashMap<String, Object>();
			StoreAddress storeAddressMap = storeAddress;
			lineTblItem.put("SOURCE_CODE", "CRM");// 来源系统代码,CRM
			lineTblItem.put("SOURCE_NUM", store.getOutTradeNo());// 来源ID,CRM客户单头id
			lineTblItem.put("SOURCE_LINE_NUM",
					storeAddressMap.getOutTradeNo() == null ? storeAddressMap.getId()
							.toString()
							: storeAddressMap.getOutTradeNo());//外部编码
			lineTblItem.put("COUNTRY", "中国");//

			Area area = areaService.find(storeAddressMap.getArea().getId());
//			System.out.println("地区"+area.toString());
			try {
				Area parent = area.getParent();
				Area grandparent = parent == null ? null : parent.getParent();
				if (parent == null) {

					lineTblItem.put("CITY", "");// 城市
					lineTblItem.put("PROVINCE", area.getName());// 省
					lineTblItem.put("STATE", "");// 市
					lineTblItem.put("COUNTY", "");// 区
				}
				if (grandparent == null && parent != null) {
					String province = parent.getName();
					if (province.equals("北京")) {
						province = "北京市";
					}
					else if (province.equals("天津")) {
						province = "天津市";
					}
					else if (province.equals("上海")) {
						province = "上海市";
					}
					else if (province.equals("重庆")) {
						province = "重庆市";
					}
					// order.setCity(area.getName());
					// order.setProvince(parent.getName());
					lineTblItem.put("CITY", area.getName());// 城市
					lineTblItem.put("PROVINCE", province);// 省
					lineTblItem.put("STATE", area.getName());// 市
					lineTblItem.put("COUNTY", "");// 区
				}
				else {
					// order.setDistrict(area.getName());
					// order.setCity(parent.getName());
					// order.setProvince(grandparent.getName());
					String province = grandparent.getName();
					if (province.equals("北京")) {
						province = "北京市";
					}
					else if (province.equals("天津")) {
						province = "天津市";
					}
					else if (province.equals("上海")) {
						province = "上海市";
					}
					else if (province.equals("重庆")) {
						province = "重庆市";
					}
					lineTblItem.put("CITY", parent.getName());// 城市
					lineTblItem.put("PROVINCE", province);// 省
					lineTblItem.put("STATE", parent.getName());// 市
					lineTblItem.put("COUNTY", area.getName());// 区
				}
			}
			catch (Exception e) {
				e.printStackTrace();
			}

			// lineTblItem.put("CITY",
			// storeAddressList.getContent().get(i).get("area_full_name"));
			// lineTblItem.put("PROVINCE", "");
			// lineTblItem.put("STATE",
			// storeAddressList.getContent().get(i).get("area_full_name"));
			// lineTblItem.put("COUNTY", "");
			lineTblItem.put("ADDRESS1", storeAddressMap.getAddress());//
			lineTblItem.put("POSTAL_CODE", storeAddressMap.getZipCode());//
			if (flag == 2) {//2  删除  失效
				lineTblItem.put("STATUS", "Inactive");//
			}
			else {
				lineTblItem.put("STATUS", "Active");//
			}
			String isBill = "Y";
			String isShip = "Y";
//			if ("1".equals(storeAddressMap.getAddressType().toString())) {// 收货地址
//				isBill = "Y";
//			}
//			else if ("2".equals(storeAddressMap.getAddressType().toString())) {// 收单地址
//				isShip = "Y";
//			}
//			else if ("3".equals(storeAddressMap.getAddressType().toString())) {// 收货收单地址
//				isShip = "Y";
//				isBill = "Y";
//			}

			lineTblItem.put("BILL_FLAG", isBill);//
			lineTblItem.put("SHIP_FLAG", isShip);//
			String logicOrGeography = "";
			if (storeAddress.getSalesArea() != null) {
				SalesArea salesArea = storeAddress.getSalesArea();
				if (salesArea != null) {
					if (salesArea.getLogicType() == null
							&& salesArea.getGeographyType() == null) {
						ExceptionUtil.throwServiceException("逻辑区域和地理区域不能同时为空");
					}
					else {
						if (salesArea.getLogicType() != null) {
							logicOrGeography = String.valueOf(salesArea.getLogicType());
						}
						else if (salesArea.getGeographyType() != null) {
							logicOrGeography = String.valueOf(salesArea.getGeographyType());
						}
					}
				}
				else {
					ExceptionUtil.throwServiceException("销售区域不能为空");
				}

			}
			lineTblItem.put("ATTRIBUTE7", logicOrGeography);//区域名称
			String erpSn = "";
			if (storeAddress.getSalesArea() != null) {
				if (storeAddress.getSalesArea().getErpSn() != null) {
					erpSn = storeAddress.getSalesArea().getErpSn();
				}
				else {
					ExceptionUtil.throwServiceException(store.getName()
							+ "客户的销售区域"
							+ storeAddress.getSalesArea().getFullName()
							+ "的erpSn为空");
				}
			}
			lineTblItem.put("ATTRIBUTE8", erpSn);//区域编码

			lineData.put("LINE_TBL_ITEM", lineTblItem);
			// System.out.println("地址：" + storeAddressMap.get("address"));
			lineTbl.add(lineData);
//			}
		}
		else if (storeAddress == null && flag == null) {
			filters.clear();
			filters.add(Filter.eq("store", store));
			Pageable pageable = new Pageable();
			Page<Map<String, Object>> storeAddressList = storeBaseService.findStoreAddressPage(null,
					null,
					store.getId(),
					null,
					null,
					pageable);

			for (int i = 0; i < storeAddressList.getContent().size(); i++) {
				Map<String, Object> lineData = new HashMap<String, Object>();
				Map<String, Object> lineTblItem = new HashMap<String, Object>();
				Map<String, Object> storeAddressMap = storeAddressList.getContent()
						.get(i);
				lineTblItem.put("SOURCE_CODE", "CRM");// 来源系统代码,CRM
				lineTblItem.put("SOURCE_NUM", store.getOutTradeNo());// 来源ID,CRM客户单头id
				lineTblItem.put("SOURCE_LINE_NUM", storeAddressMap.get("id")
						.toString());//传CRM地址ID
				lineTblItem.put("COUNTRY", "中国");//

				Area area = areaService.find(storeAddressMap.get("area") == null ? null
						: Long.parseLong(storeAddressMap.get("area").toString()));

				try {
					Area parent = area.getParent();
					Area grandparent = parent == null ? null
							: parent.getParent();
					if (parent == null) {

						lineTblItem.put("CITY", "");// 城市
						lineTblItem.put("PROVINCE", area.getName());// 省
						lineTblItem.put("STATE", "");// 市
						lineTblItem.put("COUNTY", "");// 区
					}
					if (grandparent == null && parent != null) {
						// order.setCity(area.getName());
						// order.setProvince(parent.getName());
						String province = parent.getName();
						if (province.equals("北京")) {
							province = "北京市";
						}
						else if (province.equals("天津")) {
							province = "天津市";
						}
						else if (province.equals("上海")) {
							province = "上海市";
						}
						else if (province.equals("重庆")) {
							province = "重庆市";
						}
						lineTblItem.put("CITY", area.getName());// 城市
						lineTblItem.put("PROVINCE", province);// 省
						lineTblItem.put("STATE", area.getName());// 市
						lineTblItem.put("COUNTY", "");// 区
					}
					else {
						// order.setDistrict(area.getName());
						// order.setCity(parent.getName());
						// order.setProvince(grandparent.getName());
						String province = grandparent.getName();
						if (province.equals("北京")) {
							province = "北京市";
						}
						else if (province.equals("天津")) {
							province = "天津市";
						}
						else if (province.equals("上海")) {
							province = "上海市";
						}
						else if (province.equals("重庆")) {
							province = "重庆市";
						}
						lineTblItem.put("CITY", parent.getName());// 城市
						lineTblItem.put("PROVINCE", province);// 省
						lineTblItem.put("STATE", parent.getName());// 市
						lineTblItem.put("COUNTY", area.getName());// 区
					}
				}
				catch (Exception e) {
					e.printStackTrace();
				}

				// lineTblItem.put("CITY",
				// storeAddressList.getContent().get(i).get("area_full_name"));
				// lineTblItem.put("PROVINCE", "");
				// lineTblItem.put("STATE",
				// storeAddressList.getContent().get(i).get("area_full_name"));
				// lineTblItem.put("COUNTY", "");
				lineTblItem.put("ADDRESS1", storeAddressMap.get("address"));//
				lineTblItem.put("POSTAL_CODE", storeAddressMap.get("zip_code"));//
				lineTblItem.put("STATUS", "Active");//
				String isBill = "N";
				String isShip = "N";
				if ("1".equals(storeAddressMap.get("address_type").toString())) {// 收货地址
					isBill = "Y";
				}
				else if ("2".equals(storeAddressMap.get("address_type")
						.toString())) {// 收单地址
					isShip = "Y";
				}
				else if ("3".equals(storeAddressMap.get("address_type")
						.toString())) {// 收货收单地址
					isShip = "Y";
					isBill = "Y";
				}

				lineTblItem.put("BILL_FLAG", isBill);//
				lineTblItem.put("SHIP_FLAG", isShip);//

				String logicOrGeography = "";
				if (storeAddressMap != null) {
					if (storeAddressMap.get("logicType") == null
							&& storeAddressMap.get("geographyType") == null) {
						ExceptionUtil.throwServiceException("逻辑区域和地理区域不能同时为空");
					}
					else {
						if (storeAddressMap.get("logicType") != null) {
							logicOrGeography = storeAddressMap.get("geographyType")
									.toString();
						}
						else if (storeAddressMap.get("geographyType") != null) {
							logicOrGeography = storeAddressMap.get("geographyType")
									.toString();
						}
					}
				}
				lineTblItem.put("ATTRIBUTE7", logicOrGeography);//区域名称
				String erpSn = "";
				if (storeAddressMap != null) {
					if (storeAddressMap.get("erpSn") != null) {
						erpSn = storeAddressMap.get("erpSn").toString();
					}
					else {
						ExceptionUtil.throwServiceException("销售区域erpSn为空");
					}
				}
				lineTblItem.put("ATTRIBUTE8", erpSn);//区域编码

				lineData.put("LINE_TBL_ITEM", lineTblItem);
				// System.out.println("地址：" + storeAddressMap.get("address"));
				lineTbl.add(lineData);
			}
		}

		LogUtils.error("111" + lineTbl.size());
		requestMap.put("LINE_TBL", lineTbl);

		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(requestMap);

		Long companyInfoId = store.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

		/** 写入接口表 */
		String[] fields = new String[] { store.getSn() };
		a1_MessageToService.saveA1_MessageTo(5,
				companyInfo.getUniqueIdentify(),
				requestStr,
				fields);

	}

	@Override
	public void pushStoreToErp(A1_MessageTo a1_MessageTo) throws Exception {
		String requestStr = a1_MessageTo.getData();
		Map<String, Object> params = JsonUtils.toObject(requestStr, Map.class);
		Map<String, Object> result = new NatureStoreCreate().createOrder(params);
		String returnMsg = (String) result.get("returnMsg");
		String resultXml = (String) result.get("resultXml");

		// 返回结果处理
		String hType = "2";
		String errorMsg = null;

		if (!"S".equals(returnMsg)) {
			hType = "1";
			errorMsg = returnMsg;
		}

		// 写历史表
		saveHistory(a1_MessageTo, hType, resultXml, errorMsg);

	}

	/**
	 * 用户同步
	 * 
	 * @throws Exception
	 */
	@Override
	public void IntfStoreMember() {

		String hasNext = handleStoreMember();
		while (hasNext.equals("1")) {
			hasNext = handleStoreMember();

		}

	}

	private String handleStoreMember() {
//		"http://sso-test.enaturehome.com:90/accountInfoResful/queryAccountModel?key=11AAC64B2052093A3D77C0A419680C7F0AC066D1F790AE9B4ABD58C26ADDCA5E3A018528&appCode=CRM&pageNum=100", //测试
		//"http://sso.enaturehome.com:90/accountInfoResful/queryAccountModel?key=11AAC64B2052093A3D77C0A419680C7F0AC066D1F790AE9B4ABD58C26ADDCA5E3A018528&appCode=CRM&pageNum=100 //正式
//测试
//		String msg = HttpUtil.sendGet(ssoUrl
//				+ "/accountInfoResful/queryAccountModel", "key="
//					+ ssoKey
//					+ "&appCode=CRM&pageNum=100",null);
//正式
		LogUtils.error("<单点开始>"+DateUtil.convert(new Date()));
		String msg = HttpClientUtil.get(ssoUrl
				+ "/accountInfoResful/queryAccountModel?key="
				+ ssoKey
				+ "&appCode=CRM&pageNum=100", null);
		LogUtils.error("<单点结果"+DateUtil.convert(new Date())+">"+msg);
		Map<String, Object> map = JsonUtils.toObject(msg, Map.class);
		List<Map<String, Object>> lists = (List<Map<String, Object>>) map.get("retList");
		String hasNext = (String) map.get("haseNext");
		String reqLogIds = "";
		if (lists != null) {
			try {
				for (Map<String, Object> list : lists) {
					try {
						String requestLogId = (String) list.get("requestLogId"); // 用户id
																					// sso
						String operaionAct = (String) list.get("operaionAct"); // 用户状态
																				// 0：停用
																				// 1：启用
																				// 2：新增
																				// 3：修改
						List<Filter> filters = new ArrayList<Filter>();
						String username = (String) list.get("accountNo");
						filters.clear();
						filters.add(Filter.eq("username", username));
						filters.add(Filter.eq("companyInfoId", 9L));// 开发 4 测试 9 正式
																	// 9

						
						StoreMember storeMember = storeMemberBaseService.findByUsername(username,
								9L);
					
//					List<StoreMember> sms = storeMemberBaseService.findList(null, filters, null);
						if (!ConvertUtil.isEmpty(operaionAct)
								&& !ConvertUtil.isEmpty(list.get("phone1"))
								&& !ConvertUtil.isEmpty(username)) {
							if (operaionAct.equals("0")) {// 停用
								int type = 1;
								if (storeMember != null) {
									storeMember.setIsEnabled(false);
									storeMember.setSsoUser(1);
									storeMemberBaseService.update(storeMember);
								}
								else {
									type = storeMemberBaseService.intfSaveStoreMember(list,
											username);
								}
								if (type == 1
										&& !ConvertUtil.isEmpty(requestLogId)) {
									reqLogIds += "," + requestLogId;
								}
							}
							else if (operaionAct.equals("1")) {// 启用
								int type = 1;

								if (storeMember != null) {
									storeMember.setIsEnabled(true);
									storeMember.setSsoUser(1);
									storeMemberBaseService.update(storeMember);
								}
								else {
									//单点登录默认机构id 测试：630    正式：438
									type = storeMemberBaseService.intfSaveStoreMember(list,
											username);
								}
								if (type == 1
										&& !ConvertUtil.isEmpty(requestLogId)) {
									reqLogIds += "," + requestLogId;
								}
							}
							// else if (operaionAct.equals("2")) {// 新增
							// int type =
							// storeMemberBaseService.intfSaveStoreMember(list,
							// username);
							// if (type == 1 && !ConvertUtil.isEmpty(requestLogId))
							// {
							// reqLogIds += "," + requestLogId;
							// }
							// }
							else if (operaionAct.equals("3")
									|| operaionAct.equals("2")) {// 修改
								int type = 1;

								if (storeMember != null) {
									Member member = storeMember.getMember();
									member.setMobile((String) list.get("phone1"));
									String birthdate = (String) list.get("birthdate");
									storeMember.setBirth(birthdate == null ? null
											: dateFormat.parse(birthdate));
									storeMember.setName((String) list.get("userName"));
									String sex = (String) list.get("sex");
									storeMember.setGender(sex == null ? null
											: (sex.equals("M") ? Gender.male
													: Gender.female));
									String psw = (String) list.get("password");
									storeMember.setPassword(DigestUtils.md5Hex(psw));
									String idCard = (String) list.get("userIdNumber");
									storeMember.setIdCard(idCard == null ? null
											: idCard);
									String officeEmail =(String) list.get("officeEmail");
									storeMember.setEmailAddress(officeEmail == null ? null
											: officeEmail);
									storeMember.setPayPassword(DigestUtils.md5Hex(psw));
									member.setPassword(DigestUtils.md5Hex(psw));
									storeMember.setSsoUser(1);
									storeMemberBaseService.update(storeMember);
								}
								else {
									type = storeMemberBaseService.intfSaveStoreMember(list,
											username);
								}
								if (type == 1
										&& !ConvertUtil.isEmpty(requestLogId)) {
									reqLogIds += "," + requestLogId;
								}
							}
						}
					}
					catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
			catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		String callbackMsg = HttpClientUtil
//				"http://sso-test.enaturehome.com:90/accountInfoResful/callBackAccountApi?key=11AAC64B2052093A3D77C0A419680C7F0AC066D1F790AE9B4ABD58C26ADDCA5E3A018528&reqLogIds="//测试
		//"http://sso.enaturehome.com:90/accountInfoResful/callBackAccountApi?key=11AAC64B2052093A3D77C0A419680C7F0AC066D1F790AE9B4ABD58C26ADDCA5E3A018528&reqLogIds="//正式
		.get(ssoUrlIntf
				+ "/accountInfoResful/callBackAccountApi?key="
				+ ssoKey
				+ "&reqLogIds="
				+ (reqLogIds.length() > 0 ? reqLogIds.substring(1) : ""), null);
		//System.out.println("用户同步:" + reqLogIds);
		return hasNext;
	}

	// private void saveStoreMember(Map<String, Object> list, String username) {
	// try {
	// StoreMember storeMember = new StoreMember();
	// Member member = new Member();
	// MemberRank memberRank =
	// memberRankBaseService.findDefault(WebUtils.getCurrentCompanyInfoId());
	// storeMember.setMemberRank(memberRank);
	// member.setMobile((String) list.get("phone1"));
	// String birthdate = (String) list.get("birthdate");
	// storeMember.setBirth(birthdate == null ? null :
	// dateFormat.parse(birthdate));
	// storeMember.setName((String) list.get("userName"));
	// String sex = (String) list.get("sex");
	// storeMember.setGender(sex == null ? null : (sex.equals("M") ? Gender.male
	// : Gender.female));
	// String psw = (String) list.get("password");
	// storeMember.setPassword(psw);
	// storeMember.setPayPassword(psw);
	// storeMember.setUsername(username);
	// member.setPassword(psw);
	// storeMemberBaseService.saveStoreMember(storeMember, member, null, null,
	// new Long[] { 470L }, null,
	// new Integer[] { 0 }, null, false, null);
	// } catch (Exception e) {
	// // TODO Auto-generated catch block
	// e.printStackTrace();
	// }
	// }
	/** 暖通客户余额充值审核后写入EBS */
	public void saveDepositRechargeCustomIntf(DepositRecharge depositrecharge) {

		Map<String, Object> depositreChargeMap = new HashMap<String, Object>();
		depositreChargeMap.put("id", depositrecharge.getId());
		depositreChargeMap.put("receiptNumber", depositrecharge.getSn());
		depositreChargeMap.put("amount", depositrecharge.getActualAmount());
		depositreChargeMap.put("date", depositrecharge.getCheckDate());
		depositreChargeMap.put("customerCode", depositrecharge.getStore()
				.getOutTradeNo());
		depositreChargeMap.put("sn", depositrecharge.getSn());
		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(depositreChargeMap);

		Long companyInfoId = depositrecharge.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

		/** 写入接口表 */
		String[] fields = new String[] { depositrecharge.getSn() };
		a1_MessageToService.saveA1_MessageTo(6,
				companyInfo.getUniqueIdentify(),
				requestStr,
				fields);
	}

	/** 暖通销售出库后写入EBS */
	public void saveShippingCustomIntf(Shipping shipping) {

		Map<String, Object> shippingMap = new HashMap<String, Object>();
		List<Map<String, Object>> shippingItemList = new ArrayList<Map<String, Object>>();
		Date sdate = new Date();
		for (ShippingItem shippingItem : shipping.getShippingItems()) {
			Map<String, Object> shippingItemMap = new HashMap<String, Object>();

			shippingItemMap.put("sourceHeadId", shipping.getId());
			shippingItemMap.put("sourceLineId", shippingItem.getId());
			shippingItemMap.put("itemCode", shippingItem.getVonderCode());
			shippingItemMap.put("sourceWarehouseCode",
					shippingItem.getWarehouse().getErp_warehouse_code());
			shippingItemMap.put("qty", shippingItem.getShippedQuantity());
			shippingItemMap.put("date", shippingItem.getShippedTime());
			shippingItemMap.put("price", shippingItem.getPrice());
			shippingItemMap.put("name", shippingItem.getName());
			shippingItemMap.put("orderSn", shippingItem.getOrderSn());
			shippingItemMap.put("bomFlag", shippingItem.getBomFlag());
			sdate = shippingItem.getShippedTime();
			shippingItemList.add(shippingItemMap);
		}
		shippingMap.put("remark", shipping.getMemo());
		shippingMap.put("sourceHeaderId", shipping.getId());
		shippingMap.put("sourceSn", shipping.getSn());
		shippingMap.put("trackingNo", shipping.getTrackingNo());
		shippingMap.put("area", shipping.getArea().getName());
		shippingMap.put("address", shipping.getAddress());
		shippingMap.put("zipCode", shipping.getZipCode());
		shippingMap.put("phone", shipping.getPhone());
		shippingMap.put("orderSn", shipping.getOrderSn());
		shippingMap.put("consignee", shipping.getConsignee());
		shippingMap.put("customerCode", shipping.getStore().getOutTradeNo());
		shippingMap.put("sourceType", "CRM");
		shippingMap.put("sdate", sdate);
		shippingMap.put("shippingItem", shippingItemList);
		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(shippingMap);

		Long companyInfoId = shipping.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

		/** 写入接口表 */
		String[] fields = new String[] { shipping.getSn() };
		a1_MessageToService.saveA1_MessageTo(7,
				companyInfo.getUniqueIdentify(),
				requestStr,
				fields);
	}

	/** 暖通发票审核后传入税控 */
	public void saveInvoiceCustomIntf(OrderInvoice orderInvoice) {

		List<Map<String, Object>> invoiceList = new ArrayList<Map<String, Object>>();
		for (OrderInvoiceItem invoiceItem : orderInvoice.getOrderInvoiceItem()) {
			Map<String, Object> invoiceMap = new HashMap<String, Object>();
			// 申请号
			invoiceMap.put("ORDERID", orderInvoice.getId());
			// 明细行ID
			invoiceMap.put("ORDERGOODSID", invoiceItem.getId());
			// TCLShop单号
			invoiceMap.put("SHOPORDERID", orderInvoice.getSn());
			// 业务单据号
			invoiceMap.put("BILLNO", orderInvoice.getSn());
			// 是否允许拆分,0:不可拆,1:可拆
			invoiceMap.put("ISSPLIT", 0);
			// 发票抬头
			invoiceMap.put("INVOICENAME", orderInvoice.getIssuingCompany());
			// 购方税号
			invoiceMap.put("GFSN", orderInvoice.getTaxNumber());
			// 购方地址
			invoiceMap.put("GFDZ", orderInvoice.getStoreAddress());
			// 购方银行开户行
			invoiceMap.put("GFYH", orderInvoice.getStoreBank());
			// 购方银行账号
			invoiceMap.put("GFYHZH", orderInvoice.getStoreAccount());
			// 购方手机号
			invoiceMap.put("INVOICEPHONE", orderInvoice.getStoreMobile());
			// 购方电话
			invoiceMap.put("INVOICETELPHONE", orderInvoice.getStoreMobile());
			// 商品名称,应税劳务名称
			invoiceMap.put("SKUNAME", invoiceItem.getName());
			// 商品规格型号
			invoiceMap.put("PRODTYPE", invoiceItem.getModel());
			// 商品规格型号
			invoiceMap.put("PRODSPEC", invoiceItem.getProduct().getUnit());
			// 商品数量
			invoiceMap.put("QUANTITY", invoiceItem.getQuantity());
			// 商品单价(含税)
			invoiceMap.put("UNITPRICE", invoiceItem.getInvoiceValue());
			// 商品金额(含税)
			invoiceMap.put("TOTALPRICE", invoiceItem.getInvoiceValue()
					.multiply(invoiceItem.getQuantity()));
			// 备注
			invoiceMap.put("NOTES", orderInvoice.getMemo());
			// ERP制单时间
			invoiceMap.put("ORDERDATE", orderInvoice.getCheckDate());
			// 发票类型, 0:电子普票,1:纸质普票,2:纸质专票
			invoiceMap.put("INVOICETYPE", orderInvoice.getType());
			// 对应法人名称
			invoiceMap.put("CORPORATION", "广东TCL智能暖通设备有限公司");
			// 作废标记 0:正常,1:作废 Y（特殊说明：电子票冲红必填1，纸质专票票开红票为0，纸票作废为1）
			invoiceMap.put("INVALID", 0);
			// 固定税目编码
			invoiceMap.put("SMBM", invoiceItem.getProduct().getFiscalCoding());
			// 税率
			invoiceMap.put("SPSL", orderInvoice.getTaxRate());
			// 原申请号 N（特殊说明：电子票冲红和纸票作废必填，开具红字专票不填）
			invoiceMap.put("YORDERID", "");
			// 部门名称
			invoiceMap.put("BMMC", "");
			// 产品编码/商品编码
			invoiceMap.put("ITEM_CODE", invoiceItem.getVonderCode());
			// 红字通知单号 （特殊说明：开具红字专票必填，电子票冲红和纸票作废不填）
			invoiceMap.put("NEGNOTICENO", "");
			// 折扣标记,0:退货行 1:折扣行 2：正常 N,负数必传
			invoiceMap.put("DISCOUNTFLAG", 2);
			invoiceList.add(invoiceMap);
		}
		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(invoiceList);

		Long companyInfoId = orderInvoice.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

		/** 写入接口表 */
		String[] fields = new String[] { orderInvoice.getSn() };
		a1_MessageToService.saveA1_MessageTo(8,
				companyInfo.getUniqueIdentify(),
				requestStr,
				fields);

	}

	/**
	 * 生成 同步 退货单到ERP的报文到接口表
	 * 
	 * @param b2bReturns
	 */
	public void saveReturnIntf(B2bReturns b2bReturns) {

		Warehouse warehouse = b2bReturns.getWarehouse();
		if (warehouse.getErp_warehouse_code() == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的外部编码不能为空");
		}
		Organization managementOrganization = warehouse.getManagementOrganization();
		if (managementOrganization == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的经营组织不能为空");
		}
		else if (managementOrganization.getCode() == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的经营组织编码不能为空");
		}

		SystemDict stockSystemDict = warehouse.getStockSystemDict();
		if (stockSystemDict == null) {
			ExceptionUtil.throwServiceException("仓库【"
					+ warehouse.getName()
					+ "】的库存组织不能为空");
		}
		else if (stockSystemDict.getRemark() == null) {
			ExceptionUtil.throwServiceException("系统词汇，库存组织【"
					+ stockSystemDict.getValue()
					+ "】的备注（ERP编号）不能为空");
		}

		// Order mOrder =
		// shipping.getShippingItems().get(0).getOrderItem().getOrder();
		Store store = b2bReturns.getStore();

		if (store.getOutTradeNo() == null) {
			ExceptionUtil.throwServiceException("客户【"
					+ store.getName()
					+ "】的外部编号不能为空");
		}

		SystemDict businessType = b2bReturns.getBusinessType();
		if (businessType == null) {
			ExceptionUtil.throwServiceException("退货【"
					+ b2bReturns.getSn()
					+ "】的业务类型不能为空");
		}
		Sbu sbu = b2bReturns.getSbu();
		if (sbu == null) {
			ExceptionUtil.throwServiceException("退货【"
					+ b2bReturns.getSn()
					+ "】的SBU不能为空");
		}
		else if (sbu.getUndefined1() == null) {
			ExceptionUtil.throwServiceException("SBU【"
					+ sbu.getName()
					+ "】的备注（ERP编号）不能为空");
		}

		if (b2bReturns.getAddressOutTradeNo() == null) {
			ExceptionUtil.throwServiceException("地址外部编码不能为空");
		}

		Map<String, Object> requestMap = new HashMap<String, Object>();

		requestMap.put("SOURCE_CODE", "CRM");// 来源系统代码,固定“CRM”
		requestMap.put("SOURCE_NUM", b2bReturns.getId());// 来源ID,CRM发货通知单头id
		requestMap.put("ORG_ID", managementOrganization.getCode());// 经营组织id,根据仓库的经营组织id来取
		requestMap.put("ORDER_TYPE", "零售退货订单");// 订单类型,传名称：零售销售订单、零售退货订单
		requestMap.put("BUSSINESS_TYPE", businessType.getValue());// 业务类型,客制化字段，传名称：经销商零售、商业地板、直营零售、家装、电商
		requestMap.put("CUSTOMER_NUMBER", store.getOutTradeNo());// 客户编码
		requestMap.put("ATTRIBUTE19", b2bReturns.getSmethod() == null ? ""
				: b2bReturns.getSmethod());// 发运方式
		requestMap.put("SOURCE_SHIP_TO_ADDRESS", store.getOutTradeNo()
				+ b2bReturns.getAddressOutTradeNo());// 收货地址id,CRM客户收货地址id
		requestMap.put("SOURCE_BILL_TO_ADDRESS", store.getOutTradeNo()
				+ b2bReturns.getAddressOutTradeNo());// 收单地址id,CRM客户收单地址id
		requestMap.put("SALESREP", "");// 销售人员,ERP存储待定
		requestMap.put("TERMS", "立即");// 付款条件,传：立即
		requestMap.put("CUST_PO_NUMBER", b2bReturns.getSn());// 客户PO,CRM发货通知单号
		requestMap.put("ORDERED_DATE",
				DateUtil.convert(b2bReturns.getCheckDate(), "yyyy-MM-dd"));// 订单日期,格式按照ESB标准，当前日期，2018-07-11
		requestMap.put("SBU", sbu.getUndefined1());// SBU,订单头ATTRIBUTE10，家居头弹性域，传代码，CRM从机构属性里面取
													// 地板中心、….
		requestMap.put("TRANSACTIONAL_CURR_CODE", "CNY");// 币种,传:CNY
		requestMap.put("SHIPPING_METHOD", "");// 发运方式,海运、汽运、铁运、海铁联运，值待补充
		requestMap.put("FREIGHT_CARRIER", "");// 承运商名称
		requestMap.put("DRIVER", "");// 司机姓名,客制化字段
		requestMap.put("PHONE_NUMBER", "");// 司机手机号,客制化字段
		requestMap.put("LICENSE_PLATE", "");// 车号/车牌号,客制化字段
		requestMap.put("ORDER_NOTES", "");// 订单备注,客制化字段
		requestMap.put("SHIPPING_INSTRUCTIONS",
				b2bReturns.getReason() == null ? "" : b2bReturns.getReason());// 发货备注,客制化字段
		requestMap.put("ERP_USER_NAME", b2bReturns.getStoreMember()
				.getUsername());// 创建人,创建人工号，对应FND_USER表
		requestMap.put("ATTRIBUTE16", b2bReturns.getSn());// 关联订单号,CRM销售订单号（改：取发货单号）//改回销售订单号
		requestMap.put("ATTRIBUTE17", "");// 地区,家居头弹性域，传CRM的客户所属机构名称
		requestMap.put("ATTRIBUTE3", "");// 工厂完工入库日期,全局头弹性域，不传
		requestMap.put("ATTRIBUTE4", "");// 送货双柜数,全局头弹性域，不传
		requestMap.put("ATTRIBUTE5", "");// 工程项目类型,全局头弹性域，不传
		requestMap.put("ATTRIBUTE15", "");// 是否已经发送发货通知,全局头弹性域，不传
		requestMap.put("ATTRIBUTE6", "");// 起始发货地点,全局头弹性域，不传
		requestMap.put("ATTRIBUTE11", "");// 工程项目编号,家居头弹性域，不传
		requestMap.put("ATTRIBUTE12", "");// 交货日期,家居头弹性域，不传
		requestMap.put("ATTRIBUTE13", "");// 订单来源,家居头弹性域，不传
		requestMap.put("ATTRIBUTE14", "");// 订单来源备注,家居头弹性域，不传
		requestMap.put("ATTRIBUTE18", "");// 合同名称,家居头弹性域，不传
//		requestMap.put("ATTRIBUTE19", "");// 分派工厂,家居头弹性域，不传
		requestMap.put("ATTRIBUTE20", "");// 是否内部交易,家居头弹性域，不传

		List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
		for (B2bReturnsItem b2bReturnsItem : b2bReturns.getB2bReturnsItems()) {
			Product product = b2bReturnsItem.getProduct();
			// OrderItem orderItem = shippingItem.getOrderItem();
			String productGradeName = "";
			
			if (b2bReturnsItem.getProductLevel() != null) {
				if (!ConvertUtil.isEmpty(b2bReturnsItem.getProductLevel().getValue())) {
					productGradeName = b2bReturnsItem.getProductLevel().getValue();
				}else {
					ExceptionUtil.throwServiceException("产品【"+ b2bReturnsItem.getName()+ "】的物料等级不能为空");
				}
			}

			if (b2bReturnsItem.getVonderCode() == null) {
				ExceptionUtil.throwServiceException("产品【"
						+ b2bReturnsItem.getName()
						+ "】的物料编码不能为空");
			}

			/*
			 * if (b2bReturnsItem.getBranchQuantity() == null) {
			 * ExceptionUtil.throwServiceException("产品【" +
			 * b2bReturnsItem.getName() + "】的支数不能为空"); }
			 */
			if (!"壁纸".equals(b2bReturns.getSbu().getName())) {
				if (b2bReturnsItem.getBranchQuantity() == null) {
					ExceptionUtil.throwServiceException("产品【"
							+ b2bReturnsItem.getName()
							+ "】的支数不能为空");
				}
			}

			Map<String, Object> lineData = new HashMap<String, Object>();
			Map<String, Object> lineTblItem = new HashMap<String, Object>();

			lineTblItem.put("SOURCE_CODE", "CRM");// 来源系统代码,CRM
			lineTblItem.put("SOURCE_NUM", b2bReturns.getId());// 来源ID,CRM发货通知单头id
			lineTblItem.put("SOURCE_LINE_NUM", b2bReturnsItem.getId());// 来源行ID,CRM发货通知单行id
			lineTblItem.put("ORDERED_ITEM", b2bReturnsItem.getVonderCode());// 物料编码
			lineTblItem.put("MATERIAL_GRADE", productGradeName);// 物料等级,订单行ATTRIBUTE2，传文本：优等品、二等品
			lineTblItem.put("ORDERED_QUANTITY", b2bReturnsItem.getQuantity());// 数量（平方数）
			lineTblItem.put("ORDER_QUANTITY_UOM", product.getUnit());// 单位（平方）,m2
			lineTblItem.put("ATTRIBUTE9", b2bReturnsItem.getMemo() == null ? ""
					: b2bReturnsItem.getMemo());// 备注
			lineTblItem.put("ORDERED_QUANTITY2",
					b2bReturnsItem.getBranchQuantity());// 数量（支）
			lineTblItem.put("ORDERED_QUANTITY_UOM2", "支");// 辅助单位（支）,支

//			if(product.getSpecTwo()!=null && !product.getSpecTwo().equals("")){
//				
//			}else{
//				lineTblItem.put("ORDERED_QUANTITY2",
//						0);// 数量（支）
//			}
//			
//			lineTblItem.put("ORDERED_QUANTITY_UOM2",
//					product.getSpecTwo() == null ? "" : product.getSpecTwo());// 辅助单位（支）,支
			if (store.getPlatformProperty() == 1) {// 控股合资管理中心
				lineTblItem.put("UNIT_SELLING_PRICE",
						b2bReturnsItem.getSaleOrgPrice() == null ? 0
								: b2bReturnsItem.getSaleOrgPrice());// 单价（平方）,2位小数
			}
			else {
				lineTblItem.put("UNIT_SELLING_PRICE", b2bReturnsItem.getPrice());// 单价（平方）,2位小数
			}
			lineTblItem.put("SHIP_FROM", warehouse.getErp_warehouse_code());// 仓库代码,取CRM主表仓库代码
			lineTblItem.put("ORGANIZATION_ID", stockSystemDict.getRemark());// 库存组织id,根据仓库的库存组织id来取
			lineTblItem.put("SCHEDULE_ARRIVAL_DATE", "");// 交货日期,取CRM主表交货日期
			// lineTblItem.put("ATTRIBUTE17",
			// shippingItem.getBoxQuantity());//箱数,全局行弹性域
			if (b2bReturnsItem != null
					&& b2bReturnsItem.getBranchQuantity() != null
					&& product != null
					&& product.getBranchPerBox() != null) {
				lineTblItem.put("ATTRIBUTE17",
						b2bReturnsItem.getBranchQuantity()
								.divide(product.getBranchPerBox(),
										6,
										BigDecimal.ROUND_HALF_UP));// 箱数改为支数/每箱支数
			}
			lineTblItem.put("ATTRIBUTE18", b2bReturns.getConsignee()
					+ ";"
					+ b2bReturns.getConsigneeMobile());// 联系人信息,全局行弹性域，传CRM主表收货人姓名和电话
			// 前两段为姓名和电话用“；”分隔，
			// 比如
			// “李工;16543060061;;;;;”
			lineTblItem.put("LINE_TYPE", "");// 订单行类型,不传
			lineTblItem.put("ATTRIBUTE3", "");// 物料色号,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE1", "");// 合同编号,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE19", "");// 实际收货数量,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE20", "");// 是否已经同步,全局行弹性域，不传
			// lineTblItem.put("ATTRIBUTE4", shipping.getInvoiceTitle() == null
			// ? "" : shipping.getInvoiceTitle());// 发票抬头,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE4", "");// 发票抬头,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE5", "");// 成品编码,家居行弹性域，不传
			lineTblItem.put("ATTRIBUTE6", "");// 成品序号,家居行弹性域，不传
			lineTblItem.put("ATTRIBUTE7", "");// 门类别,家居行弹性域，不传
			lineTblItem.put("ATTRIBUTE8", "");// 木门玻璃型号,家居行弹性域，不传
			
			if(b2bReturns.getSbu().getName().equals("壁纸")){
				lineTblItem.put("ATTRIBUTE10", b2bReturnsItem.getErpSn()==null?"":b2bReturnsItem.getErpSn());// 原ERP单号
				lineTblItem.put("ATTRIBUTE11", b2bReturnsItem.getShippingItem()==null?"":b2bReturnsItem.getShippingItem().getId());// 发货单明细ID
			}else{
				lineTblItem.put("ATTRIBUTE10", "");// 原ERP单号
				lineTblItem.put("ATTRIBUTE11", "");// 发货单明细ID
			}
			lineTblItem.put("ATTRIBUTE12", "");
			

			lineData.put("LINE_TBL_ITEM", lineTblItem);
			lineTbl.add(lineData);
		}
		LogUtils.error("111" + lineTbl.size());
		requestMap.put("LINE_TBL", lineTbl);

		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(requestMap);

		Long companyInfoId = b2bReturns.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

		/** 写入接口表 */
		String[] fields = new String[] { b2bReturns.getSn() };
		a1_MessageToService.saveA1_MessageTo(9,
				companyInfo.getUniqueIdentify(),
				requestStr,
				fields);

	}

	/** 税控发票信息回传后 将开票信息回传给EBS */
	public void saveInvoiceCustomEbsIntf(OrderInvoice orderInvoice) {

		Map<String, Object> invoiceMap = new HashMap<String, Object>();
		Map<String, Object> invoiceHeadMap = new HashMap<String, Object>();
		List<Map<String, Object>> invoiceItemList = new ArrayList<Map<String, Object>>();
		for (OrderInvoiceItem orderInvoiceItem : orderInvoice.getOrderInvoiceItem()) {
			Map<String, Object> invoiceItemMap = new HashMap<String, Object>();

			invoiceItemMap.put("headerId", orderInvoice.getId());
			invoiceItemMap.put("orgId", 81);
			invoiceItemMap.put("lineId", orderInvoiceItem.getId());
			invoiceItemMap.put("orderSn", orderInvoiceItem.getOrderSn());
			invoiceItemMap.put("orderItem", orderInvoiceItem.getOrderItem()
					.getName());
			invoiceItemMap.put("invoiceProductNmae",
					orderInvoiceItem.getInvoiceProductNmae());
			invoiceItemMap.put("sn", orderInvoiceItem.getVonderCode());
			invoiceItemMap.put("vonderCode", orderInvoiceItem.getVonderCode());
			invoiceItemMap.put("model", orderInvoiceItem.getModel());
			invoiceItemMap.put("name", orderInvoiceItem.getName());
			invoiceItemMap.put("product", orderInvoiceItem.getProduct()
					.getName());
			invoiceItemMap.put("quantity", orderInvoiceItem.getQuantity());
			invoiceItemMap.put("price", orderInvoiceItem.getPrice());
			invoiceItemMap.put("zhekouPrice", orderInvoiceItem.getZhekouPrice());
			invoiceItemMap.put("orderAmount", orderInvoiceItem.getOrderAmount());
			invoiceItemMap.put("invoiceValue",
					orderInvoiceItem.getInvoiceValue());
			invoiceItemMap.put("orderInvoiceId", orderInvoice.getId());
			//EBS必须要发货单号  不然EBS没有成本信息  不会扣减库存
//			String shippingNo=a1_MessageToService.getDaoCenter().getNativeDao().findString("select sn from xx_shipping where id=( select shipping from xx_shipping_item where order_item="+orderInvoiceItem.getOrderItem().getId()+")", null);
//			invoiceItemMap.put("shippingNo", shippingNo!=null&&shippingNo.trim().length()>0?shippingNo:"");
			invoiceItemList.add(invoiceItemMap);
		}
		invoiceMap.put("headerId", orderInvoice.getId());
		invoiceMap.put("orgId", 81);
		invoiceMap.put("sn", orderInvoice.getSn());
		invoiceMap.put("applicant", "");
		invoiceMap.put("region", "");
		invoiceMap.put("phone", "");
		invoiceMap.put("email", "");
		invoiceMap.put("type", orderInvoice.getType());
		invoiceMap.put("taxRate", orderInvoice.getTaxRate());
		invoiceMap.put("invoiceCode", orderInvoice.getInvoiceCode());
		invoiceMap.put("invoiceNo", orderInvoice.getInvoiceNo());
		invoiceMap.put("systemType", orderInvoice.getSystemType());
		invoiceMap.put("invoiceDate", orderInvoice.getInvoiceDate());
		invoiceMap.put("tax", orderInvoice.getTax());
		invoiceMap.put("invoiceAmount", orderInvoice.getInvoiceAmount());
		invoiceMap.put("currency", "CNY");
		invoiceMap.put("customerCode", orderInvoice.getStore().getOutTradeNo());
		invoiceMap.put("sourceType", "CRM");
		invoiceMap.put("taxNumber", orderInvoice.getTaxNumber());
		invoiceMap.put("storeBank", orderInvoice.getStoreBank());
		invoiceMap.put("storeAccount", orderInvoice.getStoreAccount());
		invoiceMap.put("storeMobile", orderInvoice.getStoreMobile());
		invoiceMap.put("storePhone", orderInvoice.getStorePhone());
		invoiceMap.put("storeAddress", orderInvoice.getStoreAddress());
		invoiceMap.put("deliveryMethod", 1);
		invoiceMap.put("consignee", "");
		invoiceMap.put("receivingPhone", "");
		invoiceMap.put("delivery", "");
		invoiceMap.put("courierNumber", "");
		invoiceMap.put("receivingAddress", "");
		invoiceMap.put("introduction", "");
		invoiceMap.put("attachMark", "");
		invoiceMap.put("err", "");
		invoiceMap.put("memo", orderInvoice.getMemo());

		invoiceHeadMap.put("arTrxLine", invoiceItemList);
		invoiceHeadMap.put("arTrxHeader", invoiceMap);
		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(invoiceHeadMap);

		Long companyInfoId = orderInvoice.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

		/** 写入接口表 */
		String[] fields = new String[] { orderInvoice.getSn() };
		a1_MessageToService.saveA1_MessageTo(10,
				companyInfo.getUniqueIdentify(),
				requestStr,
				fields);

	}

	/**
	 * 大自然计划提报接口
	 */
	public void savePlanApplyIntf(PlanApply planApply) {
		//计划提报类型 
        if (ConvertUtil.isEmpty(planApply.getPlanApplyType())) {
            ExceptionUtil.throwServiceException("计划提报类型不能为空");
        }
		//头
		Map<String, Object> requestMap = new HashMap<String, Object>();
		/**要货日期*/
		Date needDate = planApply.getNeedDate();
		if (ConvertUtil.isEmpty(needDate)) {
			ExceptionUtil.throwServiceException("要货日期不能为空");
		}
		//计划订单所属年份
		requestMap.put("YEAR_H", needDate.toString().substring(0, 4));
		//传计划订单所属月份
		requestMap.put("MONTH_H", needDate.toString().substring(5, 7));
		//计划订单的单据号
		requestMap.put("DOCUMENT_NUMBER", planApply.getSn());
		//必填，来源（1-平台、2-总部、3-经销商工程，4.电商，5.家装）
		requestMap.put("SOURCE", planApply.getPlanApplyType());
		/**机构*/
		String saleOrgName = "总部";
		if(ConvertUtil.isEmpty(planApply.getSaleOrg())){
			if(planApply.getPlanApplyType() == 1){
				ExceptionUtil.throwServiceException("机构不能为空");
			}
		}else{
			saleOrgName = planApply.getSaleOrg().getName();
		}
		requestMap.put("AREA", saleOrgName);
		/**客户*/
		if(ConvertUtil.isEmpty(planApply.getStore())){
			if(planApply.getPlanApplyType() == 7){
				ExceptionUtil.throwServiceException("客户不能为空");
			}else{
				//客户名称
				requestMap.put("CUSTOMER_NAME", "");
				//客户编码
				requestMap.put("CUSTOMER_CODE", "");
			}
		}else{
			//客户编码
			requestMap.put("CUSTOMER_CODE", ConvertUtil.isEmpty(planApply.getStore().getOutTradeNo()) ? "":planApply.getStore().getOutTradeNo());
			//客户名称
			requestMap.put("CUSTOMER_NAME", ConvertUtil.isEmpty(planApply.getStore().getName()) ? "":planApply.getStore().getName());
		}
		//备注信息
		requestMap.put("REMARKS",  ConvertUtil.isEmpty(planApply.getRemark()) ? "":planApply.getRemark());
		//创建人
		requestMap.put("ATTRIBUTE1", ConvertUtil.isEmpty(planApply.getStoreMember()) ? "":planApply.getStoreMember().getName());
		/**经销商项目*/
		String projectNo = "";
		if(ConvertUtil.isEmpty(planApply.getCustomerProject())){
			if(planApply.getPlanApplyType() == 3){
				ExceptionUtil.throwServiceException("经销商项目不能为空");
			}
		}else{
			projectNo = planApply.getCustomerProject().getProjectNo();
		}
		if(planApply.getPlanApplyType() == 4 || planApply.getPlanApplyType() == 5){
			//电商、家装也要传项目号，不过这里传的是单据号
			requestMap.put("ATTRIBUTE2", planApply.getSn()); 
		}else{
			requestMap.put("ATTRIBUTE2", projectNo);
		}
		requestMap.put("ATTRIBUTE3", "AM");
		//本单id，用于同步到ERP之后修改状态
		requestMap.put("ID",planApply.getId());
		//行  
		List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
		List<PlanApplyItem> planApplyItemList = planApply.getPlanApplyItems();
		if(!planApplyItemList.isEmpty() && planApplyItemList.size() > 0){
			for (PlanApplyItem planApplyItem : planApply.getPlanApplyItems()) {
				Product product = planApplyItem.getProduct();
				if(ConvertUtil.isEmpty(product)){
					ExceptionUtil.throwServiceException("产品不能为空");
				}
				Map<String, Object> lineData = new HashMap<String, Object>();
				Map<String, Object> lineTblItem = new HashMap<String, Object>();
				//提报明细类型
				if (ConvertUtil.isEmpty(planApplyItem.getType())) {
					ExceptionUtil.throwServiceException("计划提报明细类型不能为空");
				}
				//物料编码
				if (ConvertUtil.isEmpty(product.getVonderCode())) {
					ExceptionUtil.throwServiceException("产品名称为【"+product.getName()+"】的产品编码不能为空");
				}
				lineTblItem.put("ITEM_CODE", product.getVonderCode());
				//需求数量
				if (planApplyItem.getType() == 1) {
					if (ConvertUtil.isEmpty(planApplyItem.getModifyQuantity())) {
						ExceptionUtil.throwServiceException("产品编码为【"+product.getVonderCode()+"】的要货平方数修改值不能为空");
					}
					lineTblItem.put("DEMAND_QUANTITY", planApplyItem.getModifyQuantity());
				} else {
					if (ConvertUtil.isEmpty(planApplyItem.getQuantity())) {
						ExceptionUtil.throwServiceException("产品编码为【"+product.getVonderCode()+"】的要货平方数不能为空");
					}
					lineTblItem.put("DEMAND_QUANTITY", planApplyItem.getQuantity());
				}
				//要货日期
				if(ConvertUtil.isEmpty(planApplyItem.getNeedDate())){
					ExceptionUtil.throwServiceException("产品编码为【"+product.getVonderCode()+"】的要货日期不能为空");
				}
				lineTblItem.put("DEMAND_DATE",dateFormat.format(planApplyItem.getNeedDate()));
				//工厂
				lineTblItem.put("ATTRIBUTE1", ConvertUtil.isEmpty(planApplyItem.getFactory()) ? "":planApplyItem.getFactory().getValue()); 
				//业务类型
				if (ConvertUtil.isEmpty(planApplyItem.getBusinessType())) {
	                ExceptionUtil.throwServiceException("产品编码为【"+product.getVonderCode()+"】的业务类型不能为空");
	            }
				lineTblItem.put("ATTRIBUTE2",planApplyItem.getBusinessType().getValue());
				//sbu
	            if (ConvertUtil.isEmpty(planApplyItem.getSbu())) {
	                ExceptionUtil.throwServiceException("产品编码为【"+product.getVonderCode()+"】的sbu不能为空");
	            }
	            lineTblItem.put("ATTRIBUTE3", planApplyItem.getSbu().getName());
	            //纸张数
	            lineTblItem.put("ATTRIBUTE4", 0);
	            //含水率
	            if (ConvertUtil.isEmpty(planApplyItem.getMoistureContent())) {
	                ExceptionUtil.throwServiceException("产品编码为【"+product.getVonderCode()+"】的含水率不能为空");
	            }
				lineTblItem.put("ATTRIBUTE5",planApplyItem.getMoistureContent().getValue());
				lineData.put("LINE_TBL_ITEM", lineTblItem);
				lineTbl.add(lineData);
			}
		}
		requestMap.put("LINE_TBL", lineTbl);
		/** 请求的接口数据 */
		String requestStr = JsonUtils.toJson(requestMap);
		Long companyInfoId = planApply.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		/** 写入接口表 */
		String[] fields = new String[] { planApply.getSn() };
		a1_MessageToService.saveA1_MessageTo(27,companyInfo.getUniqueIdentify(),requestStr,fields);
		//同步erp状态
		planApply.setPushErpStatus(2);
		planApplyService.update(planApply);
	}

	public void pushPlanApplyToErp(A1_MessageTo a1_MessageTo) throws Exception {
		String requestStr = a1_MessageTo.getData();
		Map<String, Object> params = JsonUtils.toObject(requestStr, Map.class);
		Map<String, Object> result = new NaturePlanApplyCreate().createPlanApply(params);
		String returnMsg = (String) result.get("returnMsg");
		String resultXml = (String) result.get("resultXml");
		
		Long id = Long.valueOf(params.get("ID").toString());
		PlanApply planApply = planApplyService.find(id);
		// 返回结果处理
		String hType = "2";
		String errorMsg = null;
		//表明同步至ERP是否成功
		if (!"S".equals(returnMsg)) {
			hType = "1";
			errorMsg = returnMsg;
			planApply.setPushErpStatus(3);
		}else{
			planApply.setPushErpStatus(1);
			planApply.setIsPushedToErp(true);
		}
		planApplyService.update(planApply);
		// 写历史表
		saveHistory(a1_MessageTo, hType, resultXml, errorMsg);
	}


	/**
	 * 大自然无发票接口（无发票：政策类型（传备注）+ 发票类型,ERP现金流项目默认空）
	 * 
	 * @param depositRecharge
	 * @throws Exception 
	 */
	public String savePolicyIntf(DepositRecharge depositRecharge)
			throws Exception {

		Organization organization = depositRecharge.getOrganization();
		if (organization == null) {
			ExceptionUtil.throwServiceException("政策单【"
					+ depositRecharge.getSn()
					+ "】的经营组织不能为空");
		}
		else if (organization.getCode() == null) {
			ExceptionUtil.throwServiceException("政策单【"
					+ depositRecharge.getSn()
					+ "】的经营组织编码不能为空");
		}

		SystemDict invoiceType = depositRecharge.getInvoiceType();
		if (invoiceType == null) {
			ExceptionUtil.throwServiceException("政策单【"
					+ depositRecharge.getSn()
					+ "】发票类型不能为空");
		}
		Store store = depositRecharge.getStore();
		if (store.getOutTradeNo() == null) {
			ExceptionUtil.throwServiceException("客户【"
					+ store.getName()
					+ "】的外部编号不能为空");
		}
		/*
		 * List<Map<String, Object>> address =
		 * storeBaseService.findStoreAddressList(store.getId()); if
		 * (address.size() < 1) { ExceptionUtil.throwServiceException("客户【" +
		 * store.getName() + "】的收单地址不能为空"); }
		 */

		/*
		 * SystemDict businessType = store.getBusinessType(); if (businessType
		 * == null) { ExceptionUtil.throwServiceException("客户【" +
		 * store.getName() + "】的业务类型不能为空"); }
		 */
		//SystemDict sbu = store.getSbu();
		Sbu depostRechargesbu = depositRecharge.getSbu();
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("value", depostRechargesbu.getName()));
		SystemDict sbu = systemDictBaseService.find(filters);
		if (sbu == null) {
			ExceptionUtil.throwServiceException("SBU不能为空");
		}
		else if (sbu.getRemark() == null) {
			ExceptionUtil.throwServiceException("系统词汇,SBU【"
					+ sbu.getValue()
					+ "】的备注（ERP编号）不能为空");
		}

		Map<String, Object> requestMap = new HashMap<String, Object>();

		requestMap.put("SOURCE_CODE", "CRM");// 来源系统代码,固定“CRM”
		requestMap.put("SOURCE_ID", depositRecharge.getId());// 来源ID,政策单id
		requestMap.put("ORG_ID", organization.getCode());// 经营组织id,
		requestMap.put("BATCH_SOURCE_NAME", "CRM发票");// 来源,传名称：CRM发票
		requestMap.put("BILL_TO_CUSTOMER_NUM", store.getOutTradeNo());// 客户编码
		//待定
		requestMap.put("BILL_TO_LOCATION", "");// 客户收单地址编码

		requestMap.put("ATTRIBUTE2", sbu.getRemark());// 业务核算组（SBU）
		requestMap.put("CURRENCY_CODE", "CNY");// 币种  固定传“CNY”
//		requestMap.put("CUST_TRX_TYPE_NAME", "经销商销售");// 发票类型  暂时写死
		requestMap.put("CUST_TRX_TYPE_NAME",
				invoiceType.getRemark() == null ? "" : invoiceType.getRemark());// 发票类型
		//发票类型为红字的,需要将<cre:TERM_NAME>立即</cre:TERM_NAME>设置为空
		if (invoiceType.getValue().toString().contains("红字")) {
			requestMap.put("TERM_NAME", "");
		}
		else {
			requestMap.put("TERM_NAME", "立即");// 付款条件名称  固定传“立即”
		}

		requestMap.put("TRX_DATE",
				DateUtil.convert(depositRecharge.getGlDate(), "yyyyMMdd"));// 事务处理日期  传申请日期 ,ERP要求以数值形式传
		requestMap.put("GL_DATE",
				DateUtil.convert(depositRecharge.getGlDate(), "yyyyMMdd"));// 总账日期  传当前日期,ERP要求以数值形式传
		requestMap.put("TRX_NUMBER", depositRecharge.getSn());// 政策单号
		requestMap.put("COMMENTS", depositRecharge.getMemo() == null ? ""
				: depositRecharge.getMemo());// 备注
		requestMap.put("ERP_USER_NAME", depositRecharge.getStoreMember()
				.getUsername());// 单据创建人（创建人账号，即工号）

		requestMap.put("SHIP_TO_CUSTOMER_NUM", "");// 客户收货方编码
		requestMap.put("SHIP_TO_LOCATION", "");// 客户收货方编码
		requestMap.put("INTERFACE_HEADER_ATTRIBUTE1", "");// 客户收货方编码
		requestMap.put("ATTRIBUTE10", "");// 发票提头
		requestMap.put("ATTRIBUTE11", "");// 项目编号
		requestMap.put("ATTRIBUTE12", "");// 最终客户
		requestMap.put("ATTRIBUTE1", "");// 工程项目
		requestMap.put("CONVERSION_TYPE", "");// 汇率类型
		requestMap.put("CONVERSION_DATE", "");// 汇率日期
		requestMap.put("CONVERSION_RATE", "");// 汇率

		Map<String, Object> lineTbl = new HashMap<String, Object>();
		lineTbl.put("LINE_NUMBER", "1");// 事务处理行号（固定传“1”）
		lineTbl.put("DESCRIPTION",
				depositRecharge.getPolicyType().getRemark() == null ? ""
						: depositRecharge.getPolicyType().getRemark());// 政策类型（备注）
		lineTbl.put("QUANTITY", "1");// 数量（固定传“1”）
		//金额为负数,需要提示信息（红字金额大于0）
		if (depositRecharge.getActualAmount() != null) {//红字，传负数
			if (invoiceType.getValue().toString().contains("红字")
					&& depositRecharge.getActualAmount()
							.compareTo(BigDecimal.ZERO) > 0) {
				lineTbl.put("UNIT_SELLING_PRICE",
						depositRecharge.getActualAmount()
								.divide(new BigDecimal("-1.00")));// 政策金额
			}
			else {//非红字，传正数
				lineTbl.put("UNIT_SELLING_PRICE",
						depositRecharge.getActualAmount()
								.divide(new BigDecimal("-1.00")));// 政策金额
			}
		}
		else {
			ExceptionUtil.throwServiceException("实际录入金额不能为空");
		}

		lineTbl.put("TAX_RATE_CODE", "OUT_VAT"
				+ depositRecharge.getTaxRate().toBigInteger());// 税率
		lineTbl.put("UOM_CODE", "");// 单位

		requestMap.put("LINE_TBL", lineTbl);

		String returnMsg = new NaturePolicyEntryCreate().createPolicy(requestMap);

		return returnMsg;

		/** 请求的接口数据 */
		/*
		 * String requestStr = JsonUtils.toJson(requestMap); Long companyInfoId
		 * = depositRecharge.getCompanyInfoId(); CompanyInfo companyInfo =
		 * companyInfoBaseService.find(companyInfoId);
		 */

		/** 写入接口表 */
		/*
		 * String[] fields = new String[] { depositRecharge.getSn() };
		 * a1_MessageToService.saveA1_MessageTo(20,
		 * companyInfo.getUniqueIdentify(), requestStr, fields);
		 */

	}

	/**
	 * 大自然有发票接口（有发票：政策类型（传备注）+erp现金流项目,发票类型 默认空）
	 * 
	 * @param depositRecharge
	 * @throws Exception 
	 */
	public String savePolicyHaveInvoiceIntf(DepositRecharge depositRecharge)
			throws Exception {

		Organization organization = depositRecharge.getOrganization();
		if (organization == null) {
			ExceptionUtil.throwServiceException("政策单【"
					+ depositRecharge.getSn()
					+ "】的经营组织不能为空");
		}
		else if (organization.getCode() == null) {
			ExceptionUtil.throwServiceException("政策单【"
					+ depositRecharge.getSn()
					+ "】的经营组织编码不能为空");
		}
		// 有发票：政策类型+erp现金流项目,发票类型 默认空
		/*
		 * SystemDict invoiceType = depositRecharge.getInvoiceType(); if
		 * (invoiceType == null) { ExceptionUtil.throwServiceException("政策单【" +
		 * depositRecharge.getSn() + "】发票类型不能为空"); }
		 */
		Store store = depositRecharge.getStore();
		if (store.getOutTradeNo() == null) {
			ExceptionUtil.throwServiceException("客户【"
					+ store.getName()
					+ "】的外部编号不能为空");
		}
		/*
		 * List<Map<String, Object>> address =
		 * storeBaseService.findStoreAddressList(store.getId()); if
		 * (address.size() < 1) { ExceptionUtil.throwServiceException("客户【" +
		 * store.getName() + "】的收单地址不能为空"); }
		 */

		/*
		 * SystemDict businessType = store.getBusinessType(); if (businessType
		 * == null) { ExceptionUtil.throwServiceException("客户【" +
		 * store.getName() + "】的业务类型不能为空"); }
		 */
		Sbu depostRechargesbu = depositRecharge.getSbu();
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("value", depostRechargesbu.getName()));
		SystemDict sbu = systemDictBaseService.find(filters);
		if (sbu == null) {
			ExceptionUtil.throwServiceException("SBU不能为空");
		}
		/*
		 * SystemDict sbu = store.getSbu(); if (sbu == null) {
		 * ExceptionUtil.throwServiceException("客户【" + store.getName() +
		 * "】的SBU不能为空"); }
		 */
		else if (sbu.getRemark() == null) {
			ExceptionUtil.throwServiceException("系统词汇，客户的SBU【"
					+ sbu.getValue()
					+ "】的备注（ERP编号）不能为空");
		}

		SystemDict policyType = depositRecharge.getPolicyType();
		if (policyType == null) {
			ExceptionUtil.throwServiceException("政策类型不能为空");
		}
		else if (policyType.getRemark() == null) {
			ExceptionUtil.throwServiceException("系统词汇，政策类型【"
					+ policyType.getValue()
					+ "】的备注不能为空");
		}

		SystemDict cashProject = depositRecharge.getCashProject();
		if (cashProject == null) {
			ExceptionUtil.throwServiceException("ERP现金流量项目不能为空");
		}
		else if (cashProject.getRemark() == null) {
			ExceptionUtil.throwServiceException("系统词汇，ERP现金流量项目【"
					+ cashProject.getValue()
					+ "】的备注（ERP编号）不能为空");
		}

		Map<String, Object> requestMap = new HashMap<String, Object>();

		requestMap.put("SOURCE_CODE", "CRM");// 来源系统代码,固定“CRM”
		requestMap.put("SOURCE_NUM", depositRecharge.getId());// 来源ID,政策单id
		requestMap.put("ORG_ID", organization.getCode());// 经营组织id,
		requestMap.put("REMIT_BANK_ACCOUNT",
				policyType.getRemark() == null ? "" : policyType.getRemark());// 政策类型（备注）
		requestMap.put("RECEIPT_NUMBER", depositRecharge.getSn());// 政策单号
		requestMap.put("CURRENCY_CODE", "CNY");// 币种  固定传“CNY”
		//金额为负数,需要提示信息（红字金额大于0）
		SystemDict invoiceType = depositRecharge.getInvoiceType();
		if (invoiceType != null) {
			ExceptionUtil.throwServiceException("不是有发票类型");
		}
		else {
			if (depositRecharge.getActualAmount() != null
					&& depositRecharge.getActualAmount()
							.compareTo(BigDecimal.ZERO) == 1) {//传正数

				requestMap.put("NET_AMOUNT", depositRecharge.getActualAmount());// 政策金额
			}
			else {
				ExceptionUtil.throwServiceException("实际录入金额不能为空或者小于0");
			}
		}
		requestMap.put("RECEIPT_TYPE", "标准");// 收款类型  固定传“标准”
		requestMap.put("ATTRIBUTE2", cashProject.getRemark() == null ? ""
				: cashProject.getRemark());// ERP现金流量项目   传编码
		requestMap.put("ATTRIBUTE3", sbu.getRemark());// 业务核算组（SBU）
		requestMap.put("CUSTOMER_NUMBER", store.getOutTradeNo());// 客户编码
		requestMap.put("LOCATION", "");// 客户收单地址编码
		requestMap.put("ERP_USER_NAME", depositRecharge.getStoreMember()
				.getUsername());// 单据创建人（创建人账号，即工号）
		requestMap.put("CONVERSION_TYPE", "");// 汇率类型
		requestMap.put("CONVERSION_DATE", "");// 汇率日期
		requestMap.put("CONVERSION_RATE", "");// 汇率
		requestMap.put("RECEIPT_DATE",
				DateUtil.convert(depositRecharge.getGlDate(), "yyyyMMdd"));// 收款日期，要求取申请时间
		requestMap.put("GL_DATE",
				DateUtil.convert(depositRecharge.getGlDate(), "yyyyMMdd"));// GL日期  
		requestMap.put("MATURITY_DATE", "");// 到期日  传当前日期
		requestMap.put("ATTRIBUTE4", "");// 收支项目
		requestMap.put("BANK_STATEMENT", "1");// 银行流水单据号  CRM默认传1
		requestMap.put("ATTRIBUTE1", "");// 工程项目
		requestMap.put("COMMENTS_CASH", depositRecharge.getMemo() == null ? ""
				: depositRecharge.getMemo());// 备注

		String returnMsg = new NaturePolicyEntryHaveInvoiceCreate().createPolicyHaveInvoice(requestMap);

		return returnMsg;
	}

    //家哇云传出入库单
    public void saveAmShippingIntf(AmShipping amShipping,Integer flag) {
        Warehouse warehouse = amShipping.getWarehouse();
        
        if (warehouse.getErp_warehouse_code() == null) {
            ExceptionUtil.throwServiceException("仓库【" + warehouse.getName() + "】的外部编码不能为空");
        }
        Organization managementOrganization = warehouse.getManagementOrganization();
        if (managementOrganization == null) {
            ExceptionUtil.throwServiceException("仓库【" + warehouse.getName() + "】的经营组织不能为空");
        }
        else if (managementOrganization.getCode() == null) {
            ExceptionUtil.throwServiceException("仓库【" + warehouse.getName() + "】的经营组织编码不能为空");
        }

        SystemDict stockSystemDict = warehouse.getStockSystemDict();
        if (stockSystemDict == null) {
            ExceptionUtil.throwServiceException("仓库【" + warehouse.getName() + "】的库存组织不能为空");
        }
        else if (stockSystemDict.getRemark() == null) {
            ExceptionUtil.throwServiceException("系统词汇，库存组织【"  + stockSystemDict.getValue() + "】的备注（ERP编号）不能为空");
        }

        Store store = amShipping.getStore();
        /**************ESB标准字段**********************/
        Map<String, Object> requestMap = new HashMap<String, Object>();
        Map<String, Object> requestInfo = new HashMap<String, Object>();
        Map<String, Object> esbInfo = new HashMap<String, Object>();
        Map<String, Object> queryInfo = new HashMap<String, Object>();
        esbInfo.put("requestTime", DateUtil.convert(new Date(),
                "yyyy-MM-dd HH:mm:ss.SSS"));
        esbInfo.put("attr1", "");
        esbInfo.put("attr2", "");
        esbInfo.put("attr3", "");
        requestMap.put("esbInfo", esbInfo);
        queryInfo.put("pageSize", "");
        queryInfo.put("currentPage", "");
        requestMap.put("queryInfo", queryInfo);
        
        /**************flag为0时新增，其它为作废******************/

            requestInfo.put("salesNo", amShipping.getSn());// 发货单号
            requestInfo.put("shippingId", amShipping.getId());// 头ID
            if(flag == 0){
                requestInfo.put("status",0);// 状态
            }else{
                //flag为2时整单作废
                requestInfo.put("status",2);// 状态
            }
            requestInfo.put("delivery_type", "");// 发运方式
            requestInfo.put("send_type", 1);//配送方式
            requestInfo.put("type", amShipping.getBillType().getValue());// 订单类型
            requestInfo.put("categorytype", amShipping.getBillCategory() == null ? null : amShipping.getBillCategory().getValue());// 订单类型
            requestInfo.put("customerName", store == null ? null:store.getName());// 客户名称
            requestInfo.put("customerAlias", store == null ? null: store.getAlias());// 客户别称
            requestInfo.put("sale_org_name", amShipping.getSaleOrg().getName());//机构
            requestInfo.put("startTime",amShipping.getCreateDate());// 订单日期
            requestInfo.put("messageType","已登记");// 消息类型
            requestInfo.put("remarks", "");// 备注
            requestInfo.put("division","地板事业总部");// 事业部

            Map<String, Object> lineTblSender = new HashMap<String, Object>();
            lineTblSender.put("name", warehouse.getName());
            lineTblSender.put("mobile", warehouse.getMobile());
            lineTblSender.put("phone", warehouse.getMobile());
            lineTblSender.put("provinceName", warehouse.getProvince());
            lineTblSender.put("cityName", warehouse.getCity());
            lineTblSender.put("districtName", "");
            lineTblSender.put("address", warehouse.getAddress());
            lineTblSender.put("spWarehouseCode", warehouse.getErp_warehouse_code());
        
            requestInfo.put("senderInfo", lineTblSender);
            
            Map<String, Object> lineTblReceiver = new HashMap<String, Object>();
            lineTblReceiver.put("name", amShipping.getConsignee());
            lineTblReceiver.put("mobile", amShipping.getPhone());
            lineTblReceiver.put("phone", amShipping.getPhone());
            
            //拆分省市区
            Area area = amShipping.getArea();
            String districtName="";
            String cityName="";
            String provinceName="";
        
            if(area!=null && area.getParent().getParent()!=null){
                districtName=area.getName();
                cityName=area.getParent().getName();
                provinceName=area.getParent().getParent().getName();
            }else if(area !=null){
                cityName=area.getName();
                provinceName=area.getParent() == null ? null: area.getParent().getName();
            }
            lineTblReceiver.put("provinceName",provinceName );
            lineTblReceiver.put("cityName", cityName);
            lineTblReceiver.put("districtName",districtName );
            lineTblReceiver.put("detailAddress", amShipping.getAddress());
                                
            lineTblReceiver.put("spWarehouseCode", "");
            requestInfo.put("receiverInfo", lineTblReceiver);
            Boolean EnableBatch=amShipping.getWarehouse().getEnableBatch();
            List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
            for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
                Product product = amShippingItem.getProduct();
                String productGradeName = "";
                
                //计算重量体积
                BigDecimal weight= new BigDecimal("0");
                BigDecimal voluem= new BigDecimal("0");
                weight=(amShippingItem.getQuantity()==null ? new BigDecimal("0") : amShippingItem.getQuantity()).multiply(product.getWeight()==null ? new BigDecimal("0") : product.getWeight());
                voluem=(amShippingItem.getBoxQuantity()==null ? new BigDecimal("0") : amShippingItem.getBoxQuantity()).multiply(product.getVolume()==null ? new BigDecimal("0") : product.getVolume());
                Map<String, Object> lineTblItem = new HashMap<String, Object>();
                if(flag == 0){
                    lineTblItem.put("itemStatus", amShippingItem.getStatus());//状态
                }else{
                    //flag为2时整单作废
                    lineTblItem.put("itemStatus", 2);//状态
                }
                lineTblItem.put("cargoName", product.getName());//货物名称
                lineTblItem.put("shippingItemId", amShippingItem.getId());//行ID
                lineTblItem.put("cargoDescription", product.getDescription());// 货物描述
                lineTblItem.put("model", product.getModel());// 型号
                lineTblItem.put("cargo_no", product.getVonderCode());// 编码
                lineTblItem.put("grade", productGradeName);// 等级
                lineTblItem.put("colorNo", product.getWoodTypeOrColor()==null ? "" : product.getWoodTypeOrColor());// ⾊号
                lineTblItem.put("remarks", amShippingItem.getMemo()==null ? "" : amShippingItem.getMemo());//备注
                lineTblItem.put("batch", "");// 货物批次
                lineTblItem.put("area",voluem);// 单件货物⾯积
                lineTblItem.put("weight", weight);//单件货物重量
                lineTblItem.put("length",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⻓度
                lineTblItem.put("width",product.getErp_width()==null ? "" :product.getErp_width());//单件货物宽度
                lineTblItem.put("height",product.getErp_length()==null ? "" :product.getErp_length());//单件货物⾼度
                lineTblItem.put("price",amShippingItem.getPrice());//货物单价
				lineTblItem.put("num",amShippingItem.getQuantity());//货物数量
				lineTblItem.put("aidedNum",amShippingItem.getBranchQuantity());//辅数量（⽀数）
				lineTblItem.put("packageNum",amShippingItem.getBoxQuantity());//货物件数
				lineTblItem.put("branch_per_box",product.getBranchPerBox());//每箱支数
                lineTblItem.put("per_box",product.getPerBox());// 每支单位数 
                lineTblItem.put("per_branch",product.getPerBranch());//每箱单位数
                lineTblItem.put("unit",product.getUnit());//数量单位
                lineTblItem.put("org_id",warehouse.getStockSystemDict().getRemark());//库存组织
                lineTblItem.put("category",product.getProductCategory().getName());//分类
                
                lineTblItem.put("aidedUnit",product.getSpecTwo());//辅单位（个）
                
                lineTblItem.put("packageUnit","箱");//件数单位
                lineTblItem.put("oldSN","");//旧物料编码
                lineTblItem.put("backupColumn1","");//备用1
                lineTblItem.put("backupColumn2","");//备用2
                lineTblItem.put("backupColumn3","");//备用3
                lineTblItem.put("backupColumn4","");//备用4

                 /**********************AM add by liuxy 2020-12-21*********************/
                lineTblItem.put("businessOrgName",amShippingItem.getProductOrganization().getName());//经营组织
                lineTblItem.put("grade",amShippingItem.getProductLevel().getValue());//等级
                lineTblItem.put("colorNo",amShippingItem.getColorNumbers().getValue());//色号
                lineTblItem.put("moistureContent",amShippingItem.getMoistureContents().getValue());//含水率
                lineTblItem.put("newFlag",amShippingItem.getNewOldLogos().getValue());//新旧标识
                if(EnableBatch) {
                    if(amShippingItem.getWarehouseBatchItems()!=null&&amShippingItem.getWarehouseBatchItems().size()==1) {
                        lineTblItem.put("batch",amShippingItem.getWarehouseBatchItems().get(0).getWarehouseBatch().getBatchEncoding());//批次
                        if(amShippingItem.getWarehouseBatchItems().get(0).getWarehouseLocation()!=null) {
                            lineTblItem.put("storageLocation",amShippingItem.getWarehouseBatchItems().get(0).getWarehouseLocation().getCode());//库位
                        }else {
                            lineTblItem.put("storageLocation","");
                        }
                    }else {
                        ExceptionUtil.throwServiceException("【"  + product.getVonderCode() + "】批次列表不能为空或者多个");
                    }
                }else {
                    lineTblItem.put("batch","");//批次
                    if(amShippingItem.getWarehouseLocation()==null) {
                        lineTblItem.put("storageLocation","");//库位
                    }else {
                        lineTblItem.put("storageLocation",amShippingItem.getWarehouseLocation().getCode());//库位
                    }
                    
                }
              
                 /**********************AM add by liuxy 2020-12-21*********************/
                lineTbl.add(lineTblItem);
            }
            
            requestInfo.put("items", lineTbl);
            requestMap.put("requestInfo", requestInfo);
        
        /** 请求的接口数据 */
        String requestStr = JsonUtils.toJson(requestMap);
        Long companyInfoId = amShipping.getCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
        /** 写入接口表 */
            String[] fields = new String[] { amShipping.getSn() };
            a1_MessageToService.saveA1_MessageTo(57,
                    companyInfo.getUniqueIdentify(),
                    requestStr,
                    fields);

    }



}
