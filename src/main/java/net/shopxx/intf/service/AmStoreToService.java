package net.shopxx.intf.service;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.member.entity.Store;
public interface AmStoreToService extends BaseService<Store>{
	
	public void pushAmStore(Store store);
	
	public void saveHistory(A1_MessageTo intftable, String hResult,
			String field8, String hResultMsg) throws Exception;

	public void pushStoreToAm(A1_MessageTo a1_MessageTo) throws Exception;

}
