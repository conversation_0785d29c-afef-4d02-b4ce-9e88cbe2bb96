package net.shopxx.intf.service;

import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.member.entity.Store;

/**
 * 同步经销商信息到400系统
 *
 * <AUTHOR>
 */
public interface ClientPushFourHundredService extends BaseService<Store> {

    void pushAftersale(Aftersale aftersale);

    void sendAfterInfoInft(A1_MessageTo a1_messageTo) throws Exception;

    void saveHistory(A1_MessageTo a1_messageTo, String hResult, String field8, String hResultMsg) throws Exception;

}
