package net.shopxx.intf.service;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.hubbase.entity.A1_MessageTo;
import net.shopxx.order.entity.MoveLibrary;
import net.shopxx.order.entity.Shipping;

/**
 * @InterfaceName JiaWaYunToService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/12/17 15:16
 * @Version 1.0
 */
public interface JiaWaYunToService {
    void saveHistory(A1_MessageTo intftable, String hResult,
                            String field8, String hResultMsg);

    void saveLogisticsIntf(Shipping shipping,Integer flag);

    void saveLogisticsReturnIntf(B2bReturns b2bReturn, Integer flag);

    void saveLogisticsMoveIntf(MoveLibrary moveLibrary, Integer flag);

//    void pushLogisticsJwy(A1_MessageTo a1_MessageTo) throws Exception;
//
//
//    void pushLogisticsShippedJwy(A1_MessageTo a1_MessageTo) throws Exception;
//
//    void pushShippingToErp(A1_MessageTo a1_MessageTo) throws Exception;
//
//    void pushStoreToErp(A1_MessageTo a1_MessageTo) throws Exception;
}
