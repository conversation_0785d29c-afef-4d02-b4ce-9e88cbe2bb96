package net.shopxx.intf.service;

import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.hubbase.entity.A1_MessageToH;


public interface A1_MessageToHService extends BaseService<A1_MessageToH> {

	public Page<Map<String, Object>> findPage(String sn, String type,
			String firstTime, String lastTime, String hResult, Pageable pageable);
	
	
	public Page<Map<String, Object>> sbu_findPage(String sn, String type,
			String firstTime, String lastTime, String hResult, Pageable pageable);
	
	public void delectSql(String shippingSn);

}
