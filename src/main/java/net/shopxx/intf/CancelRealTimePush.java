package net.shopxx.intf;

import net.shopxx.CMB.Base64;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.HttpClientUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;

import java.nio.charset.Charset;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class CancelRealTimePush {
    private static String username = Global.getLoader().getProperty("dbintf.username");
    private static String password = Global.getLoader().getProperty("dbintf.password");
    private static String WMSURL =  "http://192.168.11.35:8083/api/v1/Link/DeliveryInstructions/Sync";


    public  String cancelBill(Map<String, Object> params) {

        String token = null;//测试

        String url = WMSURL;//ESB(测试)


        // 密钥
        String secretKey = "";

        // 传递参数
        Map<String, Object> requestMap = new HashMap<String, Object>();
        Map<String, Object> requestInfo = new HashMap<String, Object>();

        Map<String, Object> esbInfo = new HashMap<String, Object>();
        esbInfo.put("requestTime", DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        esbInfo.put("attr1", "");
        esbInfo.put("attr2", "");
        esbInfo.put("attr3", "");
        requestMap.put("esbInfo", esbInfo);

        Map<String, Object> queryInfo = new HashMap<String, Object>();
        queryInfo.put("pageSize", "");
        queryInfo.put("currentPage", "");

        requestMap.put("queryInfo", queryInfo);

        String timestamp = DateUtil.convert(new Date(), "yyyyMMddHHmm");
        requestInfo.put("timestamp", DateUtil.convert(new Date(), "yyyyMMddHHmm"));

        requestMap.put("requestInfo", params);
        String requestStr = JsonUtils.toJson(requestMap);
        System.out.println("作废取消发送sign:" + requestStr);


        LogUtils.info("取消接口执行requestStr:" + requestStr);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", getHeader());
        headers.put("Content-Type", "application/json;charset=utf-8");
        String returnMsgs = HttpClientUtil.post(url, requestStr, headers);
        System.out.println("url:" + url);
        System.out.println("returnMsgs:" + returnMsgs);

        String msg = disposeReturnMsg(returnMsgs);



        return msg;
    }

    private static String getHeader() {
        String auth = username + ":" + password;
        String encodedAuth = Base64.encode(auth.getBytes(Charset.forName("US-ASCII")));
        String authHeader = "Basic " + new String(encodedAuth);
        return authHeader;
    }

    //返回的信息处理
    public  String disposeReturnMsg(String returnMsgs){
        Map<String, Object> params = JsonUtils.toObject(returnMsgs, Map.class);
        Map<String, Object> esbInfo = (Map<String, Object>) params.get("esbInfo");
        Map<String, Object> resultInfoMsg = (Map<String, Object>) params.get("resultInfo");
        String returnMsg = esbInfo.get("returnMsg")==null?"":String.valueOf(esbInfo.get("returnMsg"));
        String messageCode = esbInfo.get("returnStatus")==null?"":String.valueOf(esbInfo.get("returnStatus"));
        System.out.println("params:"+params.toString());
        LogUtils.info("取消单据数据的返回结果:"+messageCode+"原因："+returnMsg);
        if(!messageCode.equals("S")){
            return returnMsg;
        }else {
            return "S";
        }

    }
}
