package net.shopxx.intf;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import net.shopxx.base.core.Global;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.ObjectMapper;

import net.shopxx.base.core.util.LogUtils;
/**
 * OA集成待办接口推送 
 * 
 * 
 */
public class NatureOaWf {
	

	public static String sendFromToOas(String date,String C_url) throws Exception  {
		LogUtils.info("==========================推送oa接口开始===============================");
		//测试地址
		//String url = "http://202.104.26.172:88"+C_url;
		//正式地址
		String url = Global.getLoader()
                .getProperty("oaintf.oaurl")+C_url;
		
		// 第一步：创建服务地址
		URL URL = new URL(url);
		LogUtils.info("请求OA接口路径："+url);
		
		// 第二步：打开一个通向服务地址的连接
		HttpURLConnection connection = (HttpURLConnection) URL.openConnection();
		// 第三步：设置参数
		// 3.1发送方式设置：POST必须大写
		connection.setRequestMethod("POST");
		// 3.2设置数据格式：content-type
		connection.setRequestProperty("Charsert", "UTF-8");
        connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");//设置参数类型是json格式
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setRequestProperty("logType", "base");
		// 3.3设置输入输出，因为默认新创建的connection没有读写权限，
		connection.setDoInput(true);
		connection.setDoOutput(true);
		LogUtils.info("OA接口推送报文："+date);
		// 将信息以流的方式发送出去
		OutputStream os = connection.getOutputStream();
		os.write(date.getBytes("UTF-8"));
		// 第五步：接收服务端响应，打印
		int responseCode = connection.getResponseCode();
		LogUtils.info("OA接口返回状态："+responseCode);
		StringBuilder sb = new StringBuilder();
		String message = null;
		if (200 == responseCode) {// 表示服务端响应成功
			//获取当前连接请求返回的数据流
            InputStream is = connection.getInputStream();  
            InputStreamReader isr = new InputStreamReader(is);  
            BufferedReader br = new BufferedReader(isr);  
            String temp = null;  
            while(null != (temp = br.readLine())){  
                sb.append(temp);  
            } 
            LogUtils.info("OA接口返回报文："+sb);
            if(sb!=null){
            	ObjectMapper mapper = new ObjectMapper();
            	JsonNode jsonNode = mapper.readTree(sb.toString());
        		message = jsonNode.path("message").asText()+","+jsonNode.path("operResult").asText();
            }
            is.close();  
            isr.close();  
            br.close(); 
		}
		os.close();
		LogUtils.info("OA接口返回报文处理结果："+message);
		return message;
	}

}
