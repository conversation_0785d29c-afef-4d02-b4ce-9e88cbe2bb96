package net.shopxx.intf;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.order.service.LogisticsHandService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Controller("intfLogisticsBackController")
@RequestMapping("/intf/logistics")
public class LogisticsBackController{

	@Resource(name = "logisticsHandleServiceImpl")
	private LogisticsHandService logisticsHandService;

	private static String token = Global.getLoader().getProperty("synintf.token");

	/**
	 * 产品
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/logisticsBack", method = RequestMethod.POST)
	public @ResponseBody
	ModelMap sync(HttpServletRequest request, HttpServletResponse response) {
		LogUtils.debug("========== 物流 ==========");
		ModelMap model = null;

		try {
			request.setCharacterEncoding("UTF-8");
			response.setCharacterEncoding("UTF-8");
			response.setContentType("text/html; charset=UTF-8");

//			A1_ServiceIntf_Config a1_ServiceIntf_Config = ServiceIntfConfigUtil.getServletConfig(request);
//			String token = a1_ServiceIntf_Config.getParam1();
			
			model = new ModelMap();
			String t = request.getHeader("Authorization");
			if (ConvertUtil.isEmpty(t) || !t.equals(token)) {
				response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
				LogUtils.debug("物流同步返回：" + JsonUtils.toJson(model));
				return model;
			}
			String requestTime = DateUtil.convert(new Date(),
					"yyyy-MM-dd HH:mm:ss.SSS");

			Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
//			Map<String, Object> map = ServiceIntfConfigUtil.getServletRequestData(request);
			LogUtils.debug("物流同步接收：" + JsonUtils.toJson(map));
			Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
			Map<String, Object> esbInfo = new HashMap<String, Object>();
			if (resbInfo == null) {
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0001");
				esbInfo.put("returnMsg", "esbInfo数据有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", "");
				esbInfo.put("attr2", "");
				esbInfo.put("attr3", "");
				model.put("esbInfo", esbInfo);
				LogUtils.debug("物流同步返回：" + JsonUtils.toJson(model));
				return model;
			}
			String instId = resbInfo.get("instId") == null ? ""
					: resbInfo.get("instId").toString();
			String rTime = resbInfo.get("requestTime") == null ? null
					: resbInfo.get("requestTime").toString();
			if (ConvertUtil.isEmpty(rTime)) {
				esbInfo.put("instId", instId);
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0003");
				esbInfo.put("returnMsg", "调用接口时间有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", resbInfo.get("attr1") == null ? ""
						: resbInfo.get("attr1"));
				esbInfo.put("attr2", resbInfo.get("attr2") == null ? ""
						: resbInfo.get("attr2"));
				esbInfo.put("attr3", resbInfo.get("attr3") == null ? ""
						: resbInfo.get("attr3"));
				model.put("esbInfo", esbInfo);
				LogUtils.debug("移库同步返回：" + JsonUtils.toJson(model));
				return model;
			}



			
			LogUtils.debug("物流同步接收：" + JsonUtils.toJson(map));
			
			
			System.out.println("map:"+map);
		
		
			

			//逻辑处理

			String msg = "";
		
		
				msg = logisticsHandService.MessageBack(map);
				if(msg.equals("S")){
					esbInfo.put("returnStatus", "S");
					esbInfo.put("returnCode", "A0001");
					esbInfo.put("returnMsg", "成功");
				}else{
					esbInfo.put("returnStatus", "E");
					esbInfo.put("returnCode", "E0001");
					esbInfo.put("returnMsg", "失败");
				}
				
				esbInfo.put("instId", instId);
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1",
						resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
				esbInfo.put("attr2",
						resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
				esbInfo.put("attr3",
						resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
				model.put("esbInfo", esbInfo);

				Map<String, Object> resultInfo = new HashMap<String, Object>();
				model.put("resultInfo", resultInfo);
		
				
			
			

			LogUtils.debug("物流同步返回：" + JsonUtils.toJson(model));
		}
		catch (Exception e) {
			LogUtils.error("物流", e);
		}

		LogUtils.debug("========== 物流==========");
		return model;
	}

	/**
     * 产品
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/jiaWaYunStockInOrOut", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sync2(HttpServletRequest request, HttpServletResponse response) {
        LogUtils.debug("==========出入库单回传 ==========");
        ModelMap model = null;

        try {
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("text/html; charset=UTF-8");

            model = new ModelMap();
            String t = request.getHeader("Authorization");
            if (ConvertUtil.isEmpty(t) || !t.equals(token)) {
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
                LogUtils.debug("出入库单回传：" + JsonUtils.toJson(model));
                return model;
            }
            String requestTime = DateUtil.convert(new Date(),
                    "yyyy-MM-dd HH:mm:ss.SSS");

            Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
            LogUtils.debug("物流同步接收：" + JsonUtils.toJson(map));
            Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
            Map<String, Object> esbInfo = new HashMap<String, Object>();
            if (resbInfo == null) {
                esbInfo.put("returnStatus", "E");
                esbInfo.put("returnCode", "E0001");
                esbInfo.put("returnMsg", "esbInfo数据有误");
                esbInfo.put("requestTime", requestTime);
                esbInfo.put("responseTime",
                        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                esbInfo.put("attr1", "");
                esbInfo.put("attr2", "");
                esbInfo.put("attr3", "");
                model.put("esbInfo", esbInfo);
                LogUtils.debug("出入库单回传：" + JsonUtils.toJson(model));
                return model;
            }
            String instId = resbInfo.get("instId") == null ? ""
                    : resbInfo.get("instId").toString();
            String rTime = resbInfo.get("requestTime") == null ? null
                    : resbInfo.get("requestTime").toString();
            if (ConvertUtil.isEmpty(rTime)) {
                esbInfo.put("instId", instId);
                esbInfo.put("returnStatus", "E");
                esbInfo.put("returnCode", "E0003");
                esbInfo.put("returnMsg", "调用接口时间有误");
                esbInfo.put("requestTime", requestTime);
                esbInfo.put("responseTime",
                        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                esbInfo.put("attr1", resbInfo.get("attr1") == null ? ""
                        : resbInfo.get("attr1"));
                esbInfo.put("attr2", resbInfo.get("attr2") == null ? ""
                        : resbInfo.get("attr2"));
                esbInfo.put("attr3", resbInfo.get("attr3") == null ? ""
                        : resbInfo.get("attr3"));
                model.put("esbInfo", esbInfo);
                LogUtils.debug("出入库单回传：" + JsonUtils.toJson(model));
                return model;
            }
            
            System.out.println("map:"+map);

            //逻辑处理
            String msg = "";
                msg = logisticsHandService.MessageBack(map);
                if(msg.equals("S")){
                    esbInfo.put("returnStatus", "S");
                    esbInfo.put("returnCode", "A0001");
                    esbInfo.put("returnMsg", "成功");
                }else{
                    esbInfo.put("returnStatus", "E");
                    esbInfo.put("returnCode", "E0001");
                    esbInfo.put("returnMsg", "失败");
                }
                
                esbInfo.put("instId", instId);
                esbInfo.put("requestTime", requestTime);
                esbInfo.put("responseTime",
                        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                esbInfo.put("attr1",
                        resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
                esbInfo.put("attr2",
                        resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
                esbInfo.put("attr3",
                        resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
                model.put("esbInfo", esbInfo);

                Map<String, Object> resultInfo = new HashMap<String, Object>();
                model.put("resultInfo", resultInfo);
        }
        catch (Exception e) {
            LogUtils.error("出入库单回传异常", e);
        }
        return model;
    }


}
