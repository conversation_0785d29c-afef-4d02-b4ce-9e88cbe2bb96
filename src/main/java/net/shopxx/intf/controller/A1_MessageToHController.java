package net.shopxx.intf.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import net.shopxx.order.entity.MoveLibrary;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.service.MoveLibraryService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.ShippingService;

import net.shopxx.template.tempUtil.MenuJumpUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller("intfA1_MessageToHController")
@RequestMapping("/a1_intf/a1_MessageToH")
public class A1_MessageToHController extends BaseController {

	@Resource(name = "a1_MessageToHServiceImpl")
	private A1_MessageToHService a1_MessageToHService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Autowired
	private ShippingInfoToLink5Service shippingInfoToLink5Service;
	@Autowired
	private OrderService orderService;
	@Resource(name = "b2bReturnsServiceImpl")
	private B2bReturnsService b2bReturnsService;
	@Resource(name = "moveLibraryServiceImpl")
	private MoveLibraryService moveLibraryService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/intf/a1_MessageToH/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		model.addAttribute("companyInfoId", companyInfoId);
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/intf/a1_MessageToH/list_old";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, ModelMap model) {
		model.addAttribute("a1_MessageToH", a1_MessageToHService.find(id));
		return "/intf/a1_MessageToH/view";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn, String firstTime, String lastTime,
			String type, String hResult, Pageable pageable) {

		/**错误日志*/
		Page<Map<String, Object>> page = a1_MessageToHService.findPage(sn,
				type,
				firstTime,
				lastTime,
				hResult,
				pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	
	/**
	 * 壁高列表
	 */
	@RequestMapping(value = "/sbu_list_tb", method = RequestMethod.GET)
	public String sbu_list_tb(Pageable pageable, ModelMap model,Long menuId) {
        model.addAttribute("menuId",menuId);
		return "/intf/a1_MessageToH/sbu_list_tb";
	}

	/**
	 * 壁高列表
	 */
	@RequestMapping(value = "/sbu_list", method = RequestMethod.GET)
	public String sbu_list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		model.addAttribute("companyInfoId", companyInfoId);
        model.addAttribute("menuId",menuId);
        menuJumpUtils.getModelMap(model, userId, menuId);
		return "/intf/a1_MessageToH/sbu_list";
	}

	/**
	 * 壁高列表数据
	 */
	@RequestMapping(value = "/sbu_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg sbu_list_data(String sn, String firstTime, String lastTime,
			String type, String hResult, Pageable pageable) {

		/**错误日志*/
		Page<Map<String, Object>> page = a1_MessageToHService.sbu_findPage(sn,
				type,
				firstTime,
				lastTime,
				hResult,
				pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	/**
	 * 重推
	 * 
	 * @param ids
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping(value = "/reIntf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg reIntf(Long[] ids) throws Exception {
		if (ids == null || ids.length == 0) {
			return error("请选择单据");
		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Filter> filters = new ArrayList<Filter>();
		Shipping shipping = null;
		for (int i = 0; i < ids.length; i++) {
			A1_MessageToH a1_MessageToh = a1_MessageToHService.find(ids[i]);
			if (a1_MessageToh != null && "2" == a1_MessageToh.gethResult()) {
				return error("操作失败!单据已推送成功!");
			}
			if (a1_MessageToh != null) {
				if (a1_MessageToh.getField1() == null) {
					return error("单号不存在");
				}
				else {
					//接口失败重推处理：在接口日志增加重推按钮逻辑：
					//1.删除接口历史表此发货通知单号失败的数据
					a1_MessageToHService.delectSql(a1_MessageToh.getField1());
					//2.根据发货通知单号查到发货通知单
					filters.add(Filter.eq("sn", a1_MessageToh.getField1()));
					filters.add(Filter.eq("companyInfoId", companyInfoId));
					shipping = shippingService.find(filters);
					if ("nature".equals(companyInfo.getCompany_code())) {
						//3.根据发货通知单重新写入接口表
						intfOrderMessageToService.saveShippingIntf(shipping);
					}
					else {
						return error("单号不存在");
					}

				}

			}
		}
		return success();
	}
	
	 
    /**
     * LINK5订单据重推
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reexcShippingToLink5", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg reexcShippingToLink5(Long[] ids) throws Exception {
        if (ids == null || ids.length == 0) {
            return error("请选择单据");
        }
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
        List<Filter> filters = new ArrayList<Filter>();
        Order order = null;
        for (int i = 0; i < ids.length; i++) {
            A1_MessageToH a1_MessageToh = a1_MessageToHService.find(ids[i]);
            if (a1_MessageToh != null && "2" == a1_MessageToh.gethResult()) {
                return error("操作失败!单据已推送成功!");
            }
            if (a1_MessageToh != null) {
                if (a1_MessageToh.getField1() == null) {
                    return error("单号不存在");
                }
                else {
                    
                    a1_MessageToHService.delete(ids[i]);
       
                    filters.add(Filter.eq("sn", a1_MessageToh.getField1()));
					filters.add(Filter.eq("companyInfoId", companyInfoId));
					order = orderService.find(filters);
                    if ("dzrjj".equals(companyInfo.getCompany_code())) {
                     
                    	shippingInfoToLink5Service.saveOrderInfoTo(order,1);
                    }
                    else {
                        return error("单号不存在");
                    }

                }

            }
        }
        return success();
    }
    
    /**
     * LINK5发货单据重推
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reexcShippedToLink5", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg reexcShippedToLink5(Long[] ids) throws Exception {
        if (ids == null || ids.length == 0) {
            return error("请选择单据");
        }
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
        List<Filter> filters = new ArrayList<Filter>();
        Shipping shipping = null;
        for (int i = 0; i < ids.length; i++) {
            A1_MessageToH a1_MessageToh = a1_MessageToHService.find(ids[i]);
            if (a1_MessageToh != null && "2" == a1_MessageToh.gethResult()) {
                return error("操作失败!单据已推送成功!");
            }
            if (a1_MessageToh != null) {
                if (a1_MessageToh.getField1() == null) {
                    return error("单号不存在");
                }
                else {
                    
                    a1_MessageToHService.delete(ids[i]);
       
                    filters.add(Filter.eq("sn", a1_MessageToh.getField1()));
					filters.add(Filter.eq("companyInfoId", companyInfoId));
					shipping = shippingService.find(filters);
                    if ("dzrjj".equals(companyInfo.getCompany_code())) {
                     
                    	shippingInfoToLink5Service.saveShippingInfoTo(shipping);
                    }
                    else {
                        return error("单号不存在");
                    }

                }

            }
        }
        return success();
    }

    /**
     * 家哇云单据重推
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reexcBillToJWY", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg reexcBillToJWY(Long[] ids,Integer type) throws Exception {
        if (ids == null || ids.length == 0) {
            return error("请选择单据");
        }
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
        List<Filter> filters = new ArrayList<Filter>();
		if(type == 51){
			Shipping shipping = null;
			for (int i = 0; i < ids.length; i++) {
				A1_MessageToH a1_MessageToh = a1_MessageToHService.find(ids[i]);
				if (a1_MessageToh != null && "2" == a1_MessageToh.gethResult()) {
					return error("操作失败!单据已推送成功!");
				}
				if (a1_MessageToh != null) {
					if (a1_MessageToh.getField1() == null) {
						return error("单号不存在");
					}
					else {

						a1_MessageToHService.delete(ids[i]);

						filters.add(Filter.eq("sn", a1_MessageToh.getField1()));
						filters.add(Filter.eq("companyInfoId", companyInfoId));
						shipping = shippingService.find(filters);
						if ("dzrjj".equals(companyInfo.getCompany_code())) {
							intfOrderMessageToService.saveLogisticsIntf(shipping,0);
						}
						else {
							return error("单号不存在");
						}

					}

				}
			}
		}else if(type == 53){
			B2bReturns b2bReturns = null;
			for (int i = 0; i < ids.length; i++) {
				A1_MessageToH a1_MessageToh = a1_MessageToHService.find(ids[i]);
				if (a1_MessageToh != null && "2" == a1_MessageToh.gethResult()) {
					return error("操作失败!单据已推送成功!");
				}
				if (a1_MessageToh != null) {
					if (a1_MessageToh.getField1() == null) {
						return error("单号不存在");
					}
					else {

						a1_MessageToHService.delete(ids[i]);

						filters.add(Filter.eq("sn", a1_MessageToh.getField1()));
						filters.add(Filter.eq("companyInfoId", companyInfoId));
						b2bReturns = b2bReturnsService.find(filters);
						if ("dzrjj".equals(companyInfo.getCompany_code())) {
							intfOrderMessageToService.saveLogisticsReturnIntf(b2bReturns,0);
						}
						else {
							return error("单号不存在");
						}

					}

				}
			}
		}else{
			MoveLibrary moveLibrary = null;
			for (int i = 0; i < ids.length; i++) {
				A1_MessageToH a1_MessageToh = a1_MessageToHService.find(ids[i]);
				if (a1_MessageToh != null && "2" == a1_MessageToh.gethResult()) {
					return error("操作失败!单据已推送成功!");
				}
				if (a1_MessageToh != null) {
					if (a1_MessageToh.getField1() == null) {
						return error("单号不存在");
					}
					else {

						a1_MessageToHService.delete(ids[i]);

						filters.add(Filter.eq("sn", a1_MessageToh.getField1()));
						filters.add(Filter.eq("companyInfoId", companyInfoId));
						moveLibrary = moveLibraryService.find(filters);
						if ("dzrjj".equals(companyInfo.getCompany_code())) {
							intfOrderMessageToService.saveLogisticsMoveIntf(moveLibrary,0);
						}
						else {
							return error("单号不存在");
						}

					}

				}
			}
		}
		
        return success();
    }
    
    /**
	 * LINK5退货单据重推
	 *
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/reexcB2bReturnsToLink5", method = RequestMethod.POST)
	public @ResponseBody ResultMsg reexcB2bReturnsToLink5(Long[] ids)
			throws Exception {
		if (ids == null || ids.length == 0) {
			return error("请选择单据");
		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Filter> filters = new ArrayList<Filter>();
		B2bReturns b2bReturns = null;
		for (int i = 0; i < ids.length; i++) {
			A1_MessageToH a1_MessageToh = a1_MessageToHService.find(ids[i]);
			if (a1_MessageToh != null && "2" == a1_MessageToh.gethResult()) {
				return error("操作失败!单据已推送成功!");
			}
			if (a1_MessageToh != null) {
				if (a1_MessageToh.getField1() == null) {
					return error("单号不存在");
				}
				else {

					a1_MessageToHService.delete(ids[i]);

					filters.add(Filter.eq("sn", a1_MessageToh.getField1()));
					filters.add(Filter.eq("companyInfoId", companyInfoId));
					b2bReturns = b2bReturnsService.find(filters);
					if(b2bReturns != null) {
						shippingInfoToLink5Service.saveB2bReturnsInfoTo(b2bReturns, 1);
					}else {
						return error("单号不存在");
					}

				}

			}
		}
		return success();
	}
}
