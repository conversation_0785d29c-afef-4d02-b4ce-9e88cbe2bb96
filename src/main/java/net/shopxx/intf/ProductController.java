package net.shopxx.intf;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.product.service.ProductHandleService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
@Controller("intfProductController")
@RequestMapping("/intf/product")
public class ProductController extends BaseController {
	@Resource(name = "productHandleServiceImpl")
	private ProductHandleService productHandleService;

	private static String token = Global.getLoader().getProperty("synintf.token");//正式

	/**
	 * 产品
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/sync", method = RequestMethod.POST)
	public @ResponseBody
	ModelMap sync(HttpServletRequest request, HttpServletResponse response) {

		LogUtils.debug("========== 产品 ==========");
		ModelMap model = null;
		try {

//			A1_ServiceIntf_Config a1_ServiceIntf_Config = ServiceIntfConfigUtil.getServletConfig(request);
//			String token = a1_ServiceIntf_Config.getParam1();

			model = new ModelMap();
			String t = request.getHeader("Authorization");
			if (ConvertUtil.isEmpty(t) || !t.equals(token)) {
				response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
				LogUtils.debug("产品同步返回：" + JsonUtils.toJson(model));
				return model;
			}
			LogUtils.debug("产品同步接收：\"1111111111产品222222222222222\"" );
			String requestTime = DateUtil.convert(new Date(),
					"yyyy-MM-dd HH:mm:ss.SSS");
			Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
//			Map<String, Object> map = ServiceIntfConfigUtil.getServletRequestData(request);
			LogUtils.debug("产品同步接收：" + JsonUtils.toJson(map));
			Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
			Map<String, Object> esbInfo = new HashMap<String, Object>();
			if (resbInfo == null) {
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0001");
				esbInfo.put("returnMsg", "esbInfo数据有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", "");
				esbInfo.put("attr2", "");
				esbInfo.put("attr3", "");
				model.put("esbInfo", esbInfo);
				LogUtils.debug("产品同步返回：" + JsonUtils.toJson(model));
				return model;
			}
			String instId = resbInfo.get("instId") == null ? ""
					: resbInfo.get("instId").toString();
			String rTime = resbInfo.get("requestTime") == null ? null
					: resbInfo.get("requestTime").toString();
			if (ConvertUtil.isEmpty(rTime)) {
				esbInfo.put("instId", instId);
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0003");
				esbInfo.put("returnMsg", "调用接口时间有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", resbInfo.get("attr1") == null ? ""
						: resbInfo.get("attr1"));
				esbInfo.put("attr2", resbInfo.get("attr2") == null ? ""
						: resbInfo.get("attr2"));
				esbInfo.put("attr3", resbInfo.get("attr3") == null ? ""
						: resbInfo.get("attr3"));
				model.put("esbInfo", esbInfo);
				LogUtils.debug("产品同步返回：" + JsonUtils.toJson(model));
				return model;
			}

			//逻辑处理
			String msg = "";
			msg = productHandleService.productSync(map);
			try {
				
			}
			catch (Exception e) {
				if (!msg.equals("success")) {
					String[] returnMsg = msg.split("-");
					esbInfo.put("returnStatus", "E");
					esbInfo.put("returnCode", returnMsg[0]);
					esbInfo.put("returnMsg", returnMsg[1]);
				}
			}
			if (!msg.equals("success")) {
				String[] returnMsg = msg.split("-");
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", returnMsg[0]);
				esbInfo.put("returnMsg", returnMsg[1]);
			}
			else {
				esbInfo.put("returnStatus", "S");
				esbInfo.put("returnCode", "A0001");
				esbInfo.put("returnMsg", "成功");
			}
		
			esbInfo.put("instId", instId);
			esbInfo.put("requestTime", requestTime);
			esbInfo.put("responseTime",
					DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
			esbInfo.put("attr1",
					resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
			esbInfo.put("attr2",
					resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
			esbInfo.put("attr3",
					resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
			model.put("esbInfo", esbInfo);

			Map<String, Object> resultInfo = new HashMap<String, Object>();
			model.put("resultInfo", resultInfo);
			LogUtils.debug("产品同步返回：" + JsonUtils.toJson(model));

		}
		catch (Exception e) {
			LogUtils.error("产品", e);
		}
		LogUtils.debug("========== 产品 ==========");
		return model;
	}


    /**
     * 产品渠道分类
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/productChannelClassificationsync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap productChannelClassificationsync(HttpServletRequest request, HttpServletResponse response){
        LogUtils.debug("========== 产品渠道分类 ==========");
        ModelMap model = null;
        try {

            model = new ModelMap();
            String t = request.getHeader("Authorization");
            if (ConvertUtil.isEmpty(t) || !t.equals(token)) {
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
                LogUtils.debug("产品渠道分类同步返回：" + JsonUtils.toJson(model));
                return model;
            }
            LogUtils.debug("产品渠道分类同步接收：\"1111111111产品222222222222222\"" );
            String requestTime = DateUtil.convert(new Date(),
                    "yyyy-MM-dd HH:mm:ss.SSS");
            Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
            LogUtils.debug("产品渠道分类同步接收：" + JsonUtils.toJson(map));
            Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
            Map<String, Object> esbInfo = new HashMap<String, Object>();
            if (resbInfo == null) {
                esbInfo.put("returnStatus", "E");
                esbInfo.put("returnCode", "E0001");
                esbInfo.put("returnMsg", "esbInfo数据有误");
                esbInfo.put("requestTime", requestTime);
                esbInfo.put("responseTime",
                        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                esbInfo.put("attr1", "");
                esbInfo.put("attr2", "");
                esbInfo.put("attr3", "");
                model.put("esbInfo", esbInfo);
                LogUtils.debug("产品同步返回：" + JsonUtils.toJson(model));
                return model;
            }
            String instId = resbInfo.get("instId") == null ? ""
                    : resbInfo.get("instId").toString();
            String rTime = resbInfo.get("requestTime") == null ? null
                    : resbInfo.get("requestTime").toString();
            if (ConvertUtil.isEmpty(rTime)) {
                esbInfo.put("instId", instId);
                esbInfo.put("returnStatus", "E");
                esbInfo.put("returnCode", "E0003");
                esbInfo.put("returnMsg", "调用接口时间有误");
                esbInfo.put("requestTime", requestTime);
                esbInfo.put("responseTime",
                        DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                esbInfo.put("attr1", resbInfo.get("attr1") == null ? ""
                        : resbInfo.get("attr1"));
                esbInfo.put("attr2", resbInfo.get("attr2") == null ? ""
                        : resbInfo.get("attr2"));
                esbInfo.put("attr3", resbInfo.get("attr3") == null ? ""
                        : resbInfo.get("attr3"));
                model.put("esbInfo", esbInfo);
                LogUtils.debug("产品渠道分类同步返回：" + JsonUtils.toJson(model));
                return model;
            }

            //逻辑处理
            String msg = "";
            msg = productHandleService.productChannelClassificationSync(map);
            try {

            }
            catch (Exception e) {
                if (!msg.equals("success")) {
                    String[] returnMsg = msg.split("-");
                    esbInfo.put("returnStatus", "E");
                    esbInfo.put("returnCode", returnMsg[0]);
                    esbInfo.put("returnMsg", returnMsg[1]);
                }
            }
            if (!msg.equals("success")) {
                String[] returnMsg = msg.split("-");
                esbInfo.put("returnStatus", "E");
                esbInfo.put("returnCode", returnMsg[0]);
                esbInfo.put("returnMsg", returnMsg[1]);
            }
            else {
                esbInfo.put("returnStatus", "S");
                esbInfo.put("returnCode", "A0001");
                esbInfo.put("returnMsg", "成功");
            }

            esbInfo.put("instId", instId);
            esbInfo.put("requestTime", requestTime);
            esbInfo.put("responseTime",
                    DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
            esbInfo.put("attr1",
                    resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
            esbInfo.put("attr2",
                    resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
            esbInfo.put("attr3",
                    resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
            model.put("esbInfo", esbInfo);

            Map<String, Object> resultInfo = new HashMap<String, Object>();
            model.put("resultInfo", resultInfo);
            LogUtils.debug("产品渠道分类同步返回：" + JsonUtils.toJson(model));

        }
        catch (Exception e) {
            LogUtils.error("产品渠道分类", e);
        }
        LogUtils.debug("========== 产品渠道分类 ==========");
        return model;
    }
//	public static void main(String[] args) {
//
//		Map<String, Object> map = new HashMap<String, Object>();
//		Map<String, Object> esbInfo = new HashMap<String, Object>();
//		esbInfo.put("instId", "");
//		esbInfo.put("requestTime", new SimpleDateFormat(
//				"yyyy-MM-dd HH:mm:ss:SSS").format(new Date()));
//		map.put("esbInfo", esbInfo);
//		Map<String, Object> resultInfo = new HashMap<String, Object>();
//		resultInfo.put("ORGANIZATION_ID", "148");
//		resultInfo.put("ORGANIZATION_CODE", "125");
//		resultInfo.put("INVENTORY_ITEM_ID", "");
//		resultInfo.put("ITEM_CODE", "G73A2M4A1");
//		resultInfo.put("DESCRIPTION",
//				"AM-实木复合多层地板-美学仿古-地板-爱尔兰城堡-自然UV油-15*190*1210mm1A4");
//		resultInfo.put("LONG_DESCRIPTION", "G73A2M4A1-爱尔兰城堡-15*190*1210mm1A4");
//		resultInfo.put("PRIMARY_UOM_CODE", "");
//		resultInfo.put("ITEM_TYPE", "");
//		resultInfo.put("TEMPLATE_ID", "");
//		resultInfo.put("INVENTORY_ITEM_STATUS_CODE", "");
//		resultInfo.put("VOLUME_UOM_CODE", "");
//		resultInfo.put("UNIT_VOLUME", "");
//		resultInfo.put("WEIGHT_UOM_CODE", "");
//		resultInfo.put("UNIT_WEIGHT", "");
//		resultInfo.put("DIMENSION_UOM_MIR", "");
//		resultInfo.put("UNIT_LENGTH_MIR", "");
//		resultInfo.put("UNIT_WIDTH_MIR", "");
//		resultInfo.put("FIXED_DAYS_SUPPLY_MIR", "");
//		resultInfo.put("FIXED_LOT_MULTIPLIER_MIR", "");
//		resultInfo.put("MINIMUM_ORDER_QUANTITY_MIR", "");
//		resultInfo.put("MAXIMUM_ORDER_QUANTITY_MIR", "");
//		resultInfo.put("FULL_LEAD_TIME_MIR", "");
//		resultInfo.put("MUST_USE_APPROVED_VENDOR_F_MIR", "");
//		resultInfo.put("INSPECTION_REQUIRED_FLAG_MIR", "");
//		resultInfo.put("BUYER_MIR", "");
//		resultInfo.put("SALES_ACCOUNT", "");
//		resultInfo.put("COST_OF_SALES_ACCOUNT", "");
//		resultInfo.put("DEFAULT_RECEIVING_SUBINV_MIR", "");
//		resultInfo.put("ENABLED_FLAG", "");
//		resultInfo.put("CATEGORY_ID", "");
//		resultInfo.put("CATEGORY_CODE", "");
//		resultInfo.put("CATEGORY_NAME", "Nature");
//		resultInfo.put("ATTRIBUTE15", "");
//		resultInfo.put("ATTRIBUTE16", "大自然");
//		resultInfo.put("ATTRIBUTE17", "柚木");
//		resultInfo.put("ATTRIBUTE18", "");
//		resultInfo.put("ATTRIBUTE19", "2");
//		resultInfo.put("ATTRIBUTE20", "");
//		resultInfo.put("ATTRIBUTE21", "");
//		resultInfo.put("ATTRIBUTE22", "");
//		resultInfo.put("ATTRIBUTE23", "");
//		resultInfo.put("ATTRIBUTE24", "");
//		resultInfo.put("ATTRIBUTE25", "");
//		resultInfo.put("ATTRIBUTE26", "");
//		resultInfo.put("ATTRIBUTE28", "");
//		resultInfo.put("ATTRIBUTE29", "");
//		resultInfo.put("ATTRIBUTE30", "");
//		resultInfo.put("ATTRIBUTE1", "");
//		resultInfo.put("ATTRIBUTE2", "1");
//		resultInfo.put("ATTRIBUTE3", "2");
//		List<Map<String, Object>> maplist = new ArrayList<Map<String, Object>>();
//		maplist.add(resultInfo);
//		map.put("requestInfo", maplist);
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		String msg = HttpClientUtil.post("http://192.168.11.177:8081/intf/product/sync.jhtml",
//				JsonUtils.toJson(map),
//				headers);
//		System.out.println(msg);
//	}
}