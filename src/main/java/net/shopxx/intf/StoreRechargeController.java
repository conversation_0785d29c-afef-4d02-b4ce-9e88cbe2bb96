package net.shopxx.intf;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.member.service.DepositRechargeHandleService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Controller("intfStoreRechargeController")
@RequestMapping("/intf/storeRecharge")
public class StoreRechargeController extends BaseController {

	@Resource(name = "depositRechargeHandleServiceImpl")
	private DepositRechargeHandleService depositRechargeHandleService;

	private static String token = Global.getLoader().getProperty("synintf.token");

	/**
	 * 回款同步
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/receive", method = RequestMethod.POST)
	public @ResponseBody
	ModelMap receive(HttpServletRequest request, HttpServletResponse response) {

		LogUtils.info("========== 回款同步 ==========");
		ModelMap model = null;
		try {

//			A1_ServiceIntf_Config a1_ServiceIntf_Config = ServiceIntfConfigUtil.getServletConfig(request);
//			String token = a1_ServiceIntf_Config.getParam1();

			model = new ModelMap();
			String t = request.getHeader("Authorization");
			if (ConvertUtil.isEmpty(t) || !t.equals(token)) {
				response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
				LogUtils.info("回款同步返回：" + JsonUtils.toJson(model));
				return model;
			}
			String requestTime = DateUtil.convert(new Date(),
					"yyyy-MM-dd HH:mm:ss.SSS");
			Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
//			Map<String, Object> map = ServiceIntfConfigUtil.getServletRequestData(request);
			LogUtils.info("回款同步接收：" + JsonUtils.toJson(map));
			Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
			Map<String, Object> esbInfo = new HashMap<String, Object>();
			if (resbInfo == null) {
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0001");
				esbInfo.put("returnMsg", "esbInfo数据有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", "");
				esbInfo.put("attr2", "");
				esbInfo.put("attr3", "");
				model.put("esbInfo", esbInfo);
				LogUtils.info("回款同步返回：" + JsonUtils.toJson(model));
				return model;
			}
			String instId = resbInfo.get("instId") == null ? ""
					: resbInfo.get("instId").toString();
			String rTime = resbInfo.get("requestTime") == null ? null
					: resbInfo.get("requestTime").toString();
			if (ConvertUtil.isEmpty(rTime)) {
				esbInfo.put("instId", instId);
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", "E0003");
				esbInfo.put("returnMsg", "调用接口时间有误");
				esbInfo.put("requestTime", requestTime);
				esbInfo.put("responseTime",
						DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
				esbInfo.put("attr1", resbInfo.get("attr1") == null ? ""
						: resbInfo.get("attr1"));
				esbInfo.put("attr2", resbInfo.get("attr2") == null ? ""
						: resbInfo.get("attr2"));
				esbInfo.put("attr3", resbInfo.get("attr3") == null ? ""
						: resbInfo.get("attr3"));
				model.put("esbInfo", esbInfo);
				LogUtils.info("回款同步返回：" + JsonUtils.toJson(model));
				return model;
			}

			//逻辑处理
			String msg = depositRechargeHandleService.DepositRechargeBack(map);
			if (!msg.equals("success")) {
				String[] returnMsg = msg.split("-");
				esbInfo.put("returnStatus", "E");
				esbInfo.put("returnCode", returnMsg[0]);
				esbInfo.put("returnMsg", returnMsg[1]);
			}
			else {
				esbInfo.put("returnStatus", "S");
				esbInfo.put("returnCode", "A0001");
				esbInfo.put("returnMsg", "成功");
			}
			esbInfo.put("instId", instId);
			esbInfo.put("requestTime", requestTime);
			esbInfo.put("responseTime",
					DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
			esbInfo.put("attr1",
					resbInfo.get("attr1") == null ? "" : resbInfo.get("attr1"));
			esbInfo.put("attr2",
					resbInfo.get("attr2") == null ? "" : resbInfo.get("attr2"));
			esbInfo.put("attr3",
					resbInfo.get("attr3") == null ? "" : resbInfo.get("attr3"));
			model.put("esbInfo", esbInfo);

			Map<String, Object> resultInfo = new HashMap<String, Object>();
			model.put("resultInfo", resultInfo);
			LogUtils.info("回款同步返回：" + JsonUtils.toJson(model));

		}
		catch (Exception e) {
			LogUtils.error("回款同步", e);
		}
		LogUtils.info("========== 回款同步 ==========");
		return model;
	}

//	public static void main(String[] args) {
//
//		Map<String, Object> map = new HashMap<String, Object>();
//		Map<String, Object> esbInfo = new HashMap<String, Object>();
//		esbInfo.put("instId", "abcdefg1234567");
//		esbInfo.put("requestTime", new SimpleDateFormat(
//				"yyyy-MM-dd HH:mm:ss:SSS").format(new Date()));
//		map.put("esbInfo", esbInfo);
//
//		Map<String, Object> resultInfo = new HashMap<String, Object>();
//		resultInfo.put("createDate", DateUtil.convert(new Date()));
//		resultInfo.put("creater", "程小刚");
//		resultInfo.put("checkDate", DateUtil.convert(new Date()));
//		resultInfo.put("checker", "程小刚");
//		resultInfo.put("status", "已审核");
//		resultInfo.put("M_CUSTOMER_NUMBER", "123456");
//		resultInfo.put("M_NET_AMOUNT", "5000");
//		resultInfo.put("M_RECEIPT_DATE", "2018-08-18");
//		resultInfo.put("sippingDate", DateUtil.convert(new Date()));
//
//		map.put("requestInfo", resultInfo);
//		Map<String, String> headers = new HashMap<String, String>();
//		headers.put("Authorization", token);
//		String msg = HttpClientUtil.post("http://natest.etwowin.com.cn/intf/storeRecharge/receive.jhtml",
//				JsonUtils.toJson(map),
//				headers);
//		System.out.println(msg);
//	}
}