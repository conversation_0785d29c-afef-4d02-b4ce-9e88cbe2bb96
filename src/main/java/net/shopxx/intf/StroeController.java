package net.shopxx.intf;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.member.service.StoreHandService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
@Controller("intfStoreController")
@RequestMapping("/intf/store")
public class StroeController {

	@Resource(name = "storeHandServiceImpl")
	private StoreHandService storeHandService;
    private static String token = Global.getLoader().getProperty("synintf.token");
	
	
	/**
	 * 客户
	 */
	@RequestMapping(value = "/sync", method = RequestMethod.POST,produces="text/html;charset=UTF-8")
	public @ResponseBody String sync(HttpServletRequest request, HttpServletResponse response) {
		Map<String, Object> esbInfo = null;
		try {
			esbInfo = new HashMap<String, Object>();
			String authorization = request.getHeader("Authorization");
			if (ConvertUtil.isEmpty(authorization) || !authorization.equals(token)) {
				response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
				LogUtils.debug("客户同步被拦截返回");
				return "";
			}
			Map<String, Object> map = ConvertUtil.pareObject(request.getInputStream());
			LogUtils.debug("客户同步接收map：" + JsonUtils.toJson(map));
			//逻辑处理
			String msg = storeHandService.storeSync(map);
			if (msg.equals("success")) {
				esbInfo.put("returnStatus", "0");
				esbInfo.put("returnMsg", "成功");
			}else{
				esbInfo.put("returnStatus", "1");
				esbInfo.put("returnMsg", msg);
			}
			Map<String, Object> resbInfo = (Map<String, Object>) map.get("esbInfo");
			String requestTime = resbInfo.get("requestTime") == null ? null : resbInfo.get("requestTime").toString();
			esbInfo.put("requestTime", requestTime);
			esbInfo.put("responseTime",	DateUtil.convert(new Date(),"yyyy-MM-dd HH:mm:ss.SSS"));
			LogUtils.debug("客户同步返回：" + JsonUtils.toJson(esbInfo));
			LogUtils.debug("========== 客户 ==========");
		} catch (Exception e) {
			esbInfo.put("returnStatus", "1");
			esbInfo.put("returnMsg",e.getMessage());
			LogUtils.error("客户",e);
		}
		return JsonUtils.toJson(esbInfo);
	}
	

}
