package net.shopxx.intf;

import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPMessage;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.LogUtils;

import org.w3c.dom.Document;

public class NatureStoreCreate {

	private static String ns = "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_cust_crm_soa_pkg/";

//	private static String wsdlUrl = "http://202.104.26.165:8082/api/v1/Erp/Customer/Sync";//测试
//	private static String wsdlUrl = "http://202.104.26.164:8082/api/v1/Erp/Customer/Sync";//正式
//	private static String username = "link01";
//	private static String password = "7iURWx3JNDQFvhT7";// 测试
//	private static String password = "tzNUwOiRry3juNAR";//正式
	
	private static String wsdlUrl =  Global.getLoader().getProperty("dbintf.url")+"/api/v1/Erp/Customer/Sync";
	private static String username = Global.getLoader().getProperty("dbintf.username");
	private static String password = Global.getLoader().getProperty("dbintf.password");

	public Map<String, Object> createOrder(Map<String, Object> params)
			throws Exception {

		// 1、创建服务(Service)
		URL url = new URL(wsdlUrl);
		QName sname = new QName(ns, "SCUX_NATURE_CUST_CRM_SOA_PKG_Service");
		Service service = Service.create(url, sname);

		// 2、创建Dispatch
		Dispatch<SOAPMessage> dispatch = service.createDispatch(new QName(ns,
				"SCUX_NATURE_CUST_CRM_SOA_PKG_Port"),
				SOAPMessage.class,
				Service.Mode.MESSAGE);

		// 3、创建SOAPMessage
		SOAPMessage msg = MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)
				.createMessage();
		SOAPEnvelope envelope = msg.getSOAPPart().getEnvelope();
		// 4、创建QName来指定消息中传递数据
		envelope.addNamespaceDeclaration("cre",
				"http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_cust_crm_soa_pkg/create_customer_crm/");
		SOAPBody body = envelope.getBody();

		SOAPElement ele = body.addChildElement("InputParameters", "cre");

		// 传递参数
		SOAPElement esbInfo = ele.addChildElement("esbInfo", "cre");
		esbInfo.addChildElement("instId", "cre").setValue(UUID.randomUUID()
				.toString());
		esbInfo.addChildElement("requestTime", "cre")
				.setValue(DateUtil.convert(new Date(),
						"yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.addChildElement("attr1", "cre");
		esbInfo.addChildElement("attr2", "cre");
		esbInfo.addChildElement("attr3", "cre");

		SOAPElement queryInfo = ele.addChildElement("queryInfo", "cre");
		queryInfo.addChildElement("pageSize", "cre");
		queryInfo.addChildElement("currentPage", "cre");

		SOAPElement requestInfo = ele.addChildElement("requestInfo", "cre");

		SOAPElement headerRec = requestInfo.addChildElement("HEADER_REC", "cre");
		SOAPElement lineTbl = requestInfo.addChildElement("LINE_TBL", "cre");

		// 表头
		headerRec.addChildElement("SOURCE_CODE", "cre")
				.setValue(params.get("SOURCE_CODE").toString());// 来源系统代码,固定“CRM”
		headerRec.addChildElement("SOURCE_NUM", "cre")
				.setValue(params.get("SOURCE_NUM").toString());// 来源ID,客户id
		headerRec.addChildElement("CUSTOMER_NUMBER", "cre")
				.setValue(params.get("CUSTOMER_NUMBER") == null ? ""
						: params.get("CUSTOMER_NUMBER").toString());//
		headerRec.addChildElement("CUSTOMER_NAME", "cre")
				.setValue(params.get("CUSTOMER_NAME") == null ? ""
						: params.get("CUSTOMER_NAME").toString());//
		headerRec.addChildElement("KNOW_AS", "cre")
				.setValue(params.get("KNOW_AS") == null ? ""
						: params.get("KNOW_AS").toString());//
		headerRec.addChildElement("CUSTOMER_CLASS_NAME", "cre")
				.setValue(params.get("CUSTOMER_CLASS_NAME") == null ? ""
						: params.get("CUSTOMER_CLASS_NAME").toString());//
		headerRec.addChildElement("CUSTOMER_TYPE", "cre")
				.setValue(params.get("CUSTOMER_TYPE") == null ? ""
						: params.get("CUSTOMER_TYPE").toString());//
		headerRec.addChildElement("TAX_REFERENCE", "cre")
				.setValue(params.get("TAX_REFERENCE") == null ? ""
						: params.get("TAX_REFERENCE").toString());//
		headerRec.addChildElement("CUSTOMER_STATUS", "cre")
				.setValue(params.get("CUSTOMER_STATUS") == null ? ""
						: params.get("CUSTOMER_STATUS").toString());//
		headerRec.addChildElement("CUSTOMER_ATTRIBUTE4", "cre")
				.setValue(params.get("CUSTOMER_ATTRIBUTE4") == null ? ""
						: params.get("CUSTOMER_ATTRIBUTE4").toString());//
		headerRec.addChildElement("CUSTOMER_ATTRIBUTE5", "cre")
				.setValue(params.get("CUSTOMER_ATTRIBUTE5") == null ? ""
						: params.get("CUSTOMER_ATTRIBUTE5").toString());//
		headerRec.addChildElement("MARKETING", "cre")
				.setValue(params.get("MARKETING") == null ? ""
						: params.get("MARKETING").toString());//
		headerRec.addChildElement("ORGANIZATION_TYPE", "cre")
				.setValue(params.get("ORGANIZATION_TYPE") == null ? ""
						: params.get("ORGANIZATION_TYPE").toString());//
		headerRec.addChildElement("USER_NAME", "cre")
				.setValue(params.get("USER_NAME") == null ? ""
						: params.get("USER_NAME").toString());//
		// 明细
		List<Map<String, Object>> lineParams = (List) params.get("LINE_TBL");
		for (Map<String, Object> lineParam : lineParams) {

			SOAPElement lineTblItem = lineTbl.addChildElement("LINE_TBL_ITEM",
					"cre");

			Map<String, Object> lineData = (Map) lineParam.get("LINE_TBL_ITEM");

			lineTblItem.addChildElement("SOURCE_CODE", "cre")
					.setValue(lineData.get("SOURCE_CODE").toString());// 来源系统代码,CRM
			lineTblItem.addChildElement("SOURCE_NUM", "cre")
					.setValue(lineData.get("SOURCE_NUM").toString());// 来源ID,id
			lineTblItem.addChildElement("SOURCE_LINE_NUM", "cre")
					.setValue(lineData.get("SOURCE_LINE_NUM").toString());//
			lineTblItem.addChildElement("ORG_ID", "cre")
					.setValue(lineData.get("ORG_ID") == null ? ""
							: lineData.get("ORG_ID").toString());//
			lineTblItem.addChildElement("COUNTRY", "cre")
					.setValue(lineData.get("COUNTRY") == null ? ""
							: lineData.get("COUNTRY").toString());//
			lineTblItem.addChildElement("CITY", "cre")
					.setValue(lineData.get("CITY") == null ? ""
							: lineData.get("CITY").toString());//
			lineTblItem.addChildElement("PROVINCE", "cre")
					.setValue(lineData.get("PROVINCE") == null ? ""
							: lineData.get("PROVINCE").toString());//
			lineTblItem.addChildElement("STATE", "cre")
					.setValue(lineData.get("STATE") == null ? ""
							: lineData.get("STATE").toString());//
			lineTblItem.addChildElement("COUNTY", "cre")
					.setValue(lineData.get("COUNTY") == null ? ""
							: lineData.get("COUNTY").toString());//
			lineTblItem.addChildElement("ADDRESS1", "cre")
					.setValue(lineData.get("ADDRESS1") == null ? ""
							: lineData.get("ADDRESS1").toString());//
			lineTblItem.addChildElement("POSTAL_CODE", "cre")
					.setValue(lineData.get("POSTAL_CODE") == null ? ""
							: lineData.get("POSTAL_CODE").toString());//
			lineTblItem.addChildElement("STATUS", "cre")
					.setValue(lineData.get("STATUS") == null ? ""
							: lineData.get("STATUS").toString());//
			lineTblItem.addChildElement("BILL_FLAG", "cre")
					.setValue(lineData.get("BILL_FLAG") == null ? ""
							: lineData.get("BILL_FLAG").toString());//
			lineTblItem.addChildElement("SHIP_FLAG", "cre")
					.setValue(lineData.get("SHIP_FLAG") == null ? ""
							: lineData.get("SHIP_FLAG").toString());//
			  lineTblItem.addChildElement("ATTRIBUTE7", "cre")
		        .setValue(lineData.get("ATTRIBUTE7") == null ? "" : 
		        lineData.get("ATTRIBUTE7").toString());
		      lineTblItem.addChildElement("ATTRIBUTE8", "cre")
		        .setValue(lineData.get("ATTRIBUTE8") == null ? "" : 
		        lineData.get("ATTRIBUTE8").toString());

		}

//		msg.writeTo(System.out);
		TransformerFactory transformerFactory = TransformerFactory.newInstance();
		Transformer transformer = transformerFactory.newTransformer();
		Source msgContent = msg.getSOAPPart().getContent();
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		StreamResult result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String requestXml = new String(bos.toByteArray());
		LogUtils.debug("nature客户推送接口发送：" + requestXml);

		// 添加Basic验证
		BindingProvider bp = (BindingProvider) dispatch;
		bp.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, username);
		bp.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, password);

		// 5、通过Dispatch传递消息,会返回响应消息
		SOAPMessage response = dispatch.invoke(msg);
		// response.writeTo(System.out);
		msgContent = response.getSOAPPart().getContent();
		bos = new ByteArrayOutputStream();
		result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String responseXml = new String(bos.toByteArray());
		LogUtils.debug("nature客户推送接口返回：" + responseXml);

		// 6、响应消息处理,将响应的消息转换为dom对象
		Document doc = response.getSOAPPart()
				.getEnvelope()
				.getBody()
				.extractContentAsDocument();
		String returnStatus = doc.getElementsByTagName("returnStatus")
				.item(0)
				.getTextContent();
		String returnMsg = "";
		if (returnStatus.equals("S")) {
			returnMsg = "S";
		}
		else {
			returnMsg = doc.getElementsByTagName("returnMsg")
					.item(0)
					.getTextContent();
		}
		Map<String, Object> resultData = new HashMap<String, Object>();
		resultData.put("returnMsg", returnMsg);
		resultData.put("resultXml", responseXml);
		return resultData;
	}

	public static void main(String[] args) throws Exception {

		HashMap<String, Object> params = new HashMap<String, Object>();
		params.put("SOURCE_CODE", "CRM");
		params.put("SOURCE_NUM", "233");
		params.put("CANCEL_DATE", DateUtil.convert(new Date()));
		params.put("CANCEL_REASON", "");
		params.put("ERP_USER_NAME", "");
		new NatureStoreCreate().createOrder(params);
	}
}
