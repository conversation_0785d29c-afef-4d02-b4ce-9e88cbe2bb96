package net.shopxx.intf;

import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPMessage;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.LogUtils;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

public class NatureOnHandQty {

	private static String ns = "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_onhand_soa_new_pkg/";
 
//	private static String wsdlUrl = "http://202.104.26.165:8082/api/v1/Erp/OnHand/Query?wsdl";//测试
//	private static String wsdlUrl = "http://202.104.26.164:8082/api/v1/Erp/OnHand/Query?wsdl";//正式
//	private static String username = "link01";
//	private static String password = "7iURWx3JNDQFvhT7";//测试
//	private static String password = "tzNUwOiRry3juNAR";//正式

	private static String wsdlUrl =  Global.getLoader().getProperty("dbintf.url")+"/api/v1/Erp/OnHand/Query?wsdl";
	private static String username = Global.getLoader().getProperty("dbintf.username");
	private static String password = Global.getLoader().getProperty("dbintf.password");


	@SuppressWarnings("unchecked")
	public List<Map<String, String>> onHandQty(Map<String, Object> params)
			throws Exception {

		//1、创建服务(Service)
		URL url = new URL(wsdlUrl);
		QName sname = new QName(ns, "SCUX_NATURE_ONHAND_SOA_NEW_PKG_Service");
		Service service = Service.create(url, sname);

		//2、创建Dispatch
		Dispatch<SOAPMessage> dispatch = service.createDispatch(new QName(ns, 
				"SCUX_NATURE_ONHAND_SOA_NEW_PKG_Port"),
				SOAPMessage.class,
				Service.Mode.MESSAGE);

		//3、创建SOAPMessage
		SOAPMessage msg = MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)
				.createMessage();
		SOAPEnvelope envelope = msg.getSOAPPart().getEnvelope();
		envelope.addNamespaceDeclaration("can", ns + "get_onhand_qty/");

		SOAPBody body = envelope.getBody();
		//4、创建QName来指定消息中传递数据
		SOAPElement ele = body.addChildElement("InputParameters", "can");

		// 传递参数 
		SOAPElement esbInfo = ele.addChildElement("esbInfo", "can");
		esbInfo.addChildElement("instId", "can").setValue(UUID.randomUUID()
				.toString());
		esbInfo.addChildElement("requestTime", "can")
				.setValue(DateUtil.convert(new Date(),
						"yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.addChildElement("attr1", "can");
		esbInfo.addChildElement("attr2", "can");
		esbInfo.addChildElement("attr3", "can");

		//传分页信息参数
		SOAPElement queryInfo = ele.addChildElement("queryInfo", "can");
		queryInfo.addChildElement("pageSize", "can");
		queryInfo.addChildElement("currentPage", "can");

		//发送请求
		SOAPElement requestInfo = ele.addChildElement("requestInfo", "can");
		List<String> warehouseList = (List<String>) params.get("warehouseList");
		String[] itemCode = (String[]) params.get("itemCode");
		//优化for循环:1.将数量用变量表示；
		int warehouseNum = warehouseList.size();
		int itemCodeNum = itemCode == null ? 0 : itemCode.length;
		//将循环变量的实例化放到循环外，进一步减少相关循环变量的实例化次数
		int w, j;
		for (w = 0; w < warehouseNum; w++) {
			if (warehouseList.get(w) != null) {
				//2.小套大循环
				if (itemCodeNum != 0 && itemCodeNum > 0) {
					for (j = 0; j < itemCodeNum; j++) {
						SOAPElement requestinfoItem = ele.addChildElement("REQUESTINFO_ITEM",
								"can");

						requestInfo.addChildElement(requestinfoItem);
						requestinfoItem.addChildElement("ORGANIZATION_ID",
								"can").setValue(params.get("ORGANIZATION_ID")
								.toString());
						requestinfoItem.addChildElement("ITEM_CODE", "can")
								.setValue(itemCode[j]);
						requestinfoItem.addChildElement("ITEM_RANK", "can")
								.setValue(params.get("ITEM_RANK").toString());
						requestinfoItem.addChildElement("SUBINV_CODE", "can")
								.setValue(warehouseList.get(w));
					}
				}
				else {
					SOAPElement requestinfoItem = ele.addChildElement("REQUESTINFO_ITEM",
							"can");

					requestInfo.addChildElement(requestinfoItem);
					requestinfoItem.addChildElement("ORGANIZATION_ID", "can")
							.setValue(params.get("ORGANIZATION_ID").toString());
					requestinfoItem.addChildElement("ITEM_CODE", "can")
							.setValue("");
					requestinfoItem.addChildElement("ITEM_RANK", "can")
							.setValue(params.get("ITEM_RANK").toString());
					requestinfoItem.addChildElement("SUBINV_CODE", "can")
							.setValue(warehouseList.get(w));
				}

			}
		}

		msg.writeTo(System.out);
		TransformerFactory transformerFactory = TransformerFactory.newInstance();
		Transformer transformer = transformerFactory.newTransformer();
		Source msgContent = msg.getSOAPPart().getContent();
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		StreamResult result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		LogUtils.debug("nature现有库存接口发送：" + new String(bos.toByteArray()));

		//添加Basic验证
		BindingProvider bp = (BindingProvider) dispatch;
		bp.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, username);
		bp.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, password);

		//5、通过Dispatch传递消息,会返回响应消息
		SOAPMessage response = dispatch.invoke(msg);
//		System.out.println();
		response.writeTo(System.out);
		msgContent = response.getSOAPPart().getContent();
		bos = new ByteArrayOutputStream();
		result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		LogUtils.debug("nature现有库存接口返回：" + new String(bos.toByteArray()));

		//6、响应消息处理,将响应的消息转换为dom对象
		Document doc = response.getSOAPPart()
				.getEnvelope()
				.getBody()
				.extractContentAsDocument();
		String returnStatus = doc.getElementsByTagName("returnStatus")
				.item(0)
				.getTextContent();
		String returnMsg = "";
		List<Map<String, String>> itemList = new ArrayList<Map<String, String>>();
		if (returnStatus.equals("S")) {

			//库存处理
			NodeList nodeList = doc.getElementsByTagName("resultInfo");
			itemList = new ArrayList<Map<String, String>>();
			Map<String, String> data = null;
			for (int idx = 0; idx < nodeList.getLength(); ++idx) {
				Element element = (Element) nodeList.item(idx);
				//读取Element里的Attribute Node  可以用属性名来得到属性值
				//也可以用getAttributes()得到所有属性给NamedNodeMap得到Attribute键值对  
				//Name-属性名  Value-属性值
				NodeList childNode = element.getChildNodes();
				int k;
				int childNodeNum = childNode.getLength();
				for (k = 0; k < childNodeNum; k++) {
					data = new HashMap<String, String>();
					//中间有文本空白 会被识别为TextNode  属性名为#Text 属性值为文本内容
					//判断是ElementNode 输出属性名 当然还是没有属性值 
					//if (ChildNode.item(k).getNodeType() == Node.ELEMENT_NODE) {
					//中间文本会被当做第一个子节点
					//System.out.println(ChildNode.item(k).getNodeName()+" "+ChildNode.item(k).getFirstChild().getNodeValue());
					//也可以直接读取Element间文本内容；
					NodeList childNodeT = childNode.item(k).getChildNodes();
					//中间有文本空白 会被识别为TextNode  属性名为#Text 属性值为文本内容
					//判断是ElementNode 输出属性名 当然还是没有属性值 
					for (int i = 0; i < childNodeT.getLength(); i++) {
						//中间文本会被当做第一个子节点
						//System.out.println(ChildNode.item(k).getNodeName()+" "+ChildNode.item(k).getFirstChild().getNodeValue());
						//也可以直接读取Element间文本内容；
						if ("SUBINV_CODE".equals(childNodeT.item(i)
								.getNodeName())) {//仓库代码
							data.put("subinvCode", childNodeT.item(i)
									.getTextContent());
						}
						if ("SUBINV_NAME".equals(childNodeT.item(i)
								.getNodeName())) {//仓库名称

							data.put("subinvName", childNodeT.item(i)
									.getTextContent());
						}
						if ("ORG_ID".equals(childNodeT.item(i).getNodeName())) {//经营组织id

							data.put("orgId", childNodeT.item(i)
									.getTextContent());
						}
						if ("ORGANIZATION_ID".equals(childNodeT.item(i)
								.getNodeName())) {// 库存组织id

							data.put("organizationId", childNodeT.item(i)
									.getTextContent());
						}
						if ("ITEM_RANK".equals(childNodeT.item(i).getNodeName())) {// 等级

							data.put("itemRank", childNodeT.item(i)
									.getTextContent());
						}
						if ("ITEM_CODE".equals(childNodeT.item(i).getNodeName())) {// 物料编码
							data.put("itemCode", childNodeT.item(i)
									.getTextContent());
						}
						if ("ONHAND_QUANTITY1".equals(childNodeT.item(i)
								.getNodeName())) {// 现有量（箱）（实物库存）

							data.put("onhandQuantity1", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i)
											.getTextContent());
						}
						if ("ONHAND_QUANTITY2".equals(childNodeT.item(i)
								.getNodeName())) {// 现有量（支）（实物库存）

							data.put("onhandQuantity2", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i).getTextContent());
						}
						if ("ONHAND_QTY".equals(childNodeT.item(i)
								.getNodeName())) {// 现有量（数量）（实物库存）

							data.put("onhandQuantity", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i).getTextContent());
						}
						if ("ATT_QUANTITY1".equals(childNodeT.item(i)
								.getNodeName())) {// 可处理量（箱）

							data.put("attQuantity1", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i)
											.getTextContent());
						}
						if ("ATT_QUANTITY2".equals(childNodeT.item(i)
								.getNodeName())) {// 可处理量（支）

							data.put("attQuantity2", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i)
											.getTextContent());
						}
						if ("AVAILABLE_QUANTITY".equals(childNodeT.item(i)
								.getNodeName())) {// 可处理量（数量）

							data.put("attQuantity", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i)
											.getTextContent());
						}
						itemList.add(data);
					}

					//}
				}

			}

		}
		else {
			returnMsg = doc.getElementsByTagName("returnMsg")
					.item(0)
					.getTextContent();
			LogUtils.debug("nature现有库存接口发送异常：" + returnMsg);

		}
		return itemList;
	}

	@SuppressWarnings("unchecked")
	public List<Map<String, String>> onOrderHandQty(List<Map<String, Object>> paramLists)
			throws Exception {

		//1、创建服务(Service)
		URL url = new URL(wsdlUrl);
		QName sname = new QName(ns, "SCUX_NATURE_ONHAND_SOA_NEW_PKG_Service");
		Service service = Service.create(url, sname);

		//2、创建Dispatch
		Dispatch<SOAPMessage> dispatch = service.createDispatch(new QName(ns,
				"SCUX_NATURE_ONHAND_SOA_NEW_PKG_Port"),
				SOAPMessage.class,
				Service.Mode.MESSAGE);

		//3、创建SOAPMessage
		SOAPMessage msg = MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)
				.createMessage();
		SOAPEnvelope envelope = msg.getSOAPPart().getEnvelope();
		envelope.addNamespaceDeclaration("can", ns + "get_onhand_qty/");

		SOAPBody body = envelope.getBody();
		//4、创建QName来指定消息中传递数据
		SOAPElement ele = body.addChildElement("InputParameters", "can");

		// 传递参数 
		SOAPElement esbInfo = ele.addChildElement("esbInfo", "can");
		esbInfo.addChildElement("instId", "can").setValue(UUID.randomUUID()
				.toString());
		esbInfo.addChildElement("requestTime", "can")
				.setValue(DateUtil.convert(new Date(),
						"yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.addChildElement("attr1", "can");
		esbInfo.addChildElement("attr2", "can");
		esbInfo.addChildElement("attr3", "can");

		//传分页信息参数
		SOAPElement queryInfo = ele.addChildElement("queryInfo", "can");
		queryInfo.addChildElement("pageSize", "can");
		queryInfo.addChildElement("currentPage", "can");

		//发送请求
		SOAPElement requestInfo = ele.addChildElement("requestInfo", "can");
	
		//优化for循环:1.将数量用变量表示；
		
		//将循环变量的实例化放到循环外，进一步减少相关循环变量的实例化次数
		int w, j;
		for (w = 0; w < paramLists.size(); w++) {
			Map<String,Object> params= paramLists.get(w);
			SOAPElement requestinfoItem = ele.addChildElement("REQUESTINFO_ITEM",
					"can");

			requestInfo.addChildElement(requestinfoItem);
			requestinfoItem.addChildElement("ORGANIZATION_ID",
					"can").setValue(params.get("ORGANIZATION_ID")
					.toString());
			requestinfoItem.addChildElement("ITEM_CODE", "can")
					.setValue( params.get("itemCode").toString());
			requestinfoItem.addChildElement("ITEM_RANK", "can")
					.setValue(params.get("ITEM_RANK").toString());
			requestinfoItem.addChildElement("SUBINV_CODE", "can")
					.setValue( params.get("warehouseId").toString());
		}
			
		

		msg.writeTo(System.out);
		TransformerFactory transformerFactory = TransformerFactory.newInstance();
		Transformer transformer = transformerFactory.newTransformer();
		Source msgContent = msg.getSOAPPart().getContent();
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		StreamResult result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		LogUtils.debug("nature现有库存接口发送：" + new String(bos.toByteArray()));

		//添加Basic验证
		BindingProvider bp = (BindingProvider) dispatch;
		bp.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, username);
		bp.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, password);

		//5、通过Dispatch传递消息,会返回响应消息
		SOAPMessage response = dispatch.invoke(msg);
//		System.out.println();
		response.writeTo(System.out);
		msgContent = response.getSOAPPart().getContent();
		bos = new ByteArrayOutputStream();
		result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		LogUtils.debug("nature现有库存接口返回：" + new String(bos.toByteArray()));

		//6、响应消息处理,将响应的消息转换为dom对象
		Document doc = response.getSOAPPart()
				.getEnvelope()
				.getBody()
				.extractContentAsDocument();
		String returnStatus = doc.getElementsByTagName("returnStatus")
				.item(0)
				.getTextContent();
		String returnMsg = "";
		List<Map<String, String>> itemList = new ArrayList<Map<String, String>>();
		if (returnStatus.equals("S")) {

			//库存处理
			NodeList nodeList = doc.getElementsByTagName("resultInfo");
			itemList = new ArrayList<Map<String, String>>();
			Map<String, String> data = null;
			for (int idx = 0; idx < nodeList.getLength(); ++idx) {
				Element element = (Element) nodeList.item(idx);
				//读取Element里的Attribute Node  可以用属性名来得到属性值
				//也可以用getAttributes()得到所有属性给NamedNodeMap得到Attribute键值对  
				//Name-属性名  Value-属性值
				NodeList childNode = element.getChildNodes();
				int k;
				int childNodeNum = childNode.getLength();
				for (k = 0; k < childNodeNum; k++) {
					data = new HashMap<String, String>();
					//中间有文本空白 会被识别为TextNode  属性名为#Text 属性值为文本内容
					//判断是ElementNode 输出属性名 当然还是没有属性值 
					//if (ChildNode.item(k).getNodeType() == Node.ELEMENT_NODE) {
					//中间文本会被当做第一个子节点
					//System.out.println(ChildNode.item(k).getNodeName()+" "+ChildNode.item(k).getFirstChild().getNodeValue());
					//也可以直接读取Element间文本内容；
					NodeList childNodeT = childNode.item(k).getChildNodes();
					//中间有文本空白 会被识别为TextNode  属性名为#Text 属性值为文本内容
					//判断是ElementNode 输出属性名 当然还是没有属性值 
					for (int i = 0; i < childNodeT.getLength(); i++) {
						//中间文本会被当做第一个子节点
						//System.out.println(ChildNode.item(k).getNodeName()+" "+ChildNode.item(k).getFirstChild().getNodeValue());
						//也可以直接读取Element间文本内容；
						if ("SUBINV_CODE".equals(childNodeT.item(i)
								.getNodeName())) {//仓库代码
							data.put("subinvCode", childNodeT.item(i)
									.getTextContent());
						}
						if ("SUBINV_NAME".equals(childNodeT.item(i)
								.getNodeName())) {//仓库名称

							data.put("subinvName", childNodeT.item(i)
									.getTextContent());
						}
						if ("ORG_ID".equals(childNodeT.item(i).getNodeName())) {//经营组织id

							data.put("orgId", childNodeT.item(i)
									.getTextContent());
						}
						if ("ORGANIZATION_ID".equals(childNodeT.item(i)
								.getNodeName())) {// 库存组织id

							data.put("organizationId", childNodeT.item(i)
									.getTextContent());
						}
						if ("ITEM_RANK".equals(childNodeT.item(i).getNodeName())) {// 等级

							data.put("itemRank", childNodeT.item(i)
									.getTextContent());
						}
						if ("ITEM_CODE".equals(childNodeT.item(i).getNodeName())) {// 物料编码
							data.put("itemCode", childNodeT.item(i)
									.getTextContent());
						}
						if ("ONHAND_QUANTITY1".equals(childNodeT.item(i)
								.getNodeName())) {// 现有量（箱）（实物库存）

							data.put("onhandQuantity1", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i)
											.getTextContent());
						}
						if ("ONHAND_QUANTITY2".equals(childNodeT.item(i)
								.getNodeName())) {// 现有量（支）（实物库存）

							data.put("onhandQuantity2", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i).getTextContent());
						}
						if ("ONHAND_QTY".equals(childNodeT.item(i)
								.getNodeName())) {// 现有量（数量）（实物库存）

							data.put("onhandQuantity", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i).getTextContent());
						}
						if ("ATT_QUANTITY1".equals(childNodeT.item(i)
								.getNodeName())) {// 可处理量（箱）

							data.put("attQuantity1", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i)
											.getTextContent());
						}
						if ("ATT_QUANTITY2".equals(childNodeT.item(i)
								.getNodeName())) {// 可处理量（支）

							data.put("attQuantity2", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i)
											.getTextContent());
						}
						if ("AVAILABLE_QUANTITY".equals(childNodeT.item(i)
								.getNodeName())) {// 可处理量（数量）

							data.put("attQuantity", childNodeT.item(i)
									.getTextContent()==null||childNodeT.item(i).getTextContent().equals("")?"0":childNodeT.item(i)
											.getTextContent());
						}
						itemList.add(data);
					}

					//}
				}

			}

		}
		else {
			returnMsg = doc.getElementsByTagName("returnMsg")
					.item(0)
					.getTextContent();
			LogUtils.debug("nature现有库存接口发送异常：" + returnMsg);

		}
		
		return itemList;
	}

}
