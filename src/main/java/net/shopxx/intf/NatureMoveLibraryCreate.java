package net.shopxx.intf;

import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPMessage;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;

import org.w3c.dom.Document;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.LogUtils;

public class NatureMoveLibraryCreate {

	private static String ns = "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_item_move_pkg/";
 
//	private static String wsdlUrl = "http://202.104.26.165:8082/api/v1/Erp/TransferInstructions/Import02";//测试

//	private static String wsdlUrl = "http://202.104.26.164:8082/api/v1/Erp/TransferInstructions/Import02";//正式
//	private static String username = "link01";
//	private static String password = "7iURWx3JNDQFvhT7";//测试
//	private static String password = "tzNUwOiRry3juNAR";//正式

	private static String wsdlUrl = Global.getLoader().getProperty("dbintf.url")+"/api/v1/Erp/TransferInstructions/Import02";
	private static String username = Global.getLoader().getProperty("dbintf.username");
	private static String password = Global.getLoader().getProperty("dbintf.password");
	
	public String createMoveLibrary(Map<String, Object> params)
			throws Exception {

		
		//1、创建服务(Service)
		URL url = new URL(wsdlUrl);
		
		QName sname = new QName(ns, "SCUX_NATURE_ITEM_MOVE_PKG_Service");
		
		Service service = Service.create(url, sname);
		 
		//2、创建Dispatch
		Dispatch<SOAPMessage> dispatch = service.createDispatch(new QName(ns,
				"SCUX_NATURE_ITEM_MOVE_PKG_Port"),
				SOAPMessage.class,
				Service.Mode.MESSAGE);
		

		//3、创建SOAPMessage
		SOAPMessage msg = MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)
				.createMessage();
		SOAPEnvelope envelope = msg.getSOAPPart().getEnvelope();
		//4、创建QName来指定消息中传递数据
		envelope.addNamespaceDeclaration("cre", "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_item_move_pkg/create_move_order/");
		SOAPBody body = envelope.getBody();
		

		SOAPElement ele = body.addChildElement("InputParameters", "cre");
		
		// 传递参数 
		SOAPElement esbInfo = ele.addChildElement("esbInfo", "cre");
		esbInfo.addChildElement("requestTime", "cre")
				.setValue(DateUtil.convert(new Date(),
						"yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.addChildElement("attr1", "cre");
		esbInfo.addChildElement("attr2", "cre");
		esbInfo.addChildElement("attr3", "cre");

		SOAPElement queryInfo = ele.addChildElement("queryInfo", "cre");
		queryInfo.addChildElement("pageSize", "cre");
		queryInfo.addChildElement("currentPage", "cre");

		SOAPElement requestInfo = ele.addChildElement("requestInfo", "cre");

		SOAPElement headerRec = requestInfo.addChildElement("REQUESTINFO_ITEM", "cre");
		SOAPElement lineTbl = headerRec.addChildElement("LINE", "cre");
		
		
		//表头信息
		headerRec.addChildElement("ORGANIZATION_ID", "cre")
			.setValue(params.get("ORGANIZATION_ID").toString());//来源系统代码,固定“CRM”
		headerRec.addChildElement("HEADER_ID", "cre")
		.setValue(params.get("HEADER_ID").toString());//来源ID,CRM移库通知单头id
		headerRec.addChildElement("REQUEST_NUMBER", "cre")
		.setValue(params.get("REQUEST_NUMBER").toString());//经营组织
		headerRec.addChildElement("INV_SUB_FROM", "cre")
		.setValue(params.get("INV_SUB_FROM").toString());//来源仓编码
		headerRec.addChildElement("INV_SUB_TO", "cre")
		.setValue(params.get("INV_SUB_TO").toString());//目标仓编码
		headerRec.addChildElement("CREATER", "cre")
		.setValue(params.get("CREATER").toString());//创建人
		headerRec.addChildElement("CREATE_DATE", "cre")
		.setValue(params.get("CREATE_DATE").toString().substring(0,10));//审核时间
		headerRec.addChildElement("DEMAND_DATE", "cre")
		.setValue(params.get("DEMAND_DATE").toString().substring(0,10));//需求时间
		headerRec.addChildElement("STATUS", "cre")
		.setValue(params.get("STATUS").toString());//状态
		headerRec.addChildElement("REMARK", "cre")
		.setValue(params.get("REMARK").toString());//头备注
		headerRec.addChildElement("ATTRIBUTE1", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE2", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE3", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE4", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE5", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE6", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE7", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE8", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE9", "cre")
		.setValue("");//
		headerRec.addChildElement("ATTRIBUTE10", "cre")
		.setValue("");
		
		//明细
		List<Map<String, Object>> lineParams = (List) params.get("LINE_TBL");
		for (Map<String, Object> lineParam : lineParams) {
					
			SOAPElement lineTblItem = lineTbl.addChildElement("LINE_ITEM",
					"cre");

			Map<String, Object> lineData = (Map) lineParam.get("LINE_TBL_ITEM");

			
			lineTblItem.addChildElement("LINE_ID", "cre")
			.setValue(lineData.get("LINE_ID").toString());//来源行ID,CRM发货通知单行id
			lineTblItem.addChildElement("ITEM_NUMBER", "cre")
			.setValue(lineData.get("ITEM_NUMBER").toString());//物料编码
			lineTblItem.addChildElement("ORDERED_QUANTITY", "cre")
			.setValue(lineData.get("ORDERED_QUANTITY").toString());//主数量
			lineTblItem.addChildElement("QUANTITY_UOM", "cre")
			.setValue(lineData.get("QUANTITY_UOM").toString());//单位
			lineTblItem.addChildElement("ORDERED_QUANTITY1", "cre")
			.setValue(lineData.get("ORDERED_QUANTITY1").toString());//辅助数量
			lineTblItem.addChildElement("QUANTITY_UOM1", "cre")
			.setValue(lineData.get("QUANTITY_UOM1").toString());// 辅助单位(支数)
			lineTblItem.addChildElement("ORDERED_QUANTITY2", "cre")
			.setValue(lineData.get("ORDERED_QUANTITY2").toString());//辅助数量
			lineTblItem.addChildElement("QUANTITY_UOM2", "cre")
			.setValue(lineData.get("QUANTITY_UOM2").toString());//辅助单位（箱数）
			lineTblItem.addChildElement("LEVEL_NUM", "cre")
			.setValue(lineData.get("LEVEL_NUM").toString());//等级
			lineTblItem.addChildElement("REMARK", "cre")
			.setValue(lineData.get("REMARK").toString());//行备注
			lineTblItem.addChildElement("ATTRIBUTE1", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE2", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE3", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE4", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE5", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE6", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE7", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE8", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE9", "cre")
			.setValue("");//
			lineTblItem.addChildElement("ATTRIBUTE10", "cre")
			.setValue("");
			
			
		}

		
		
		//msg.writeTo(System.out);
		TransformerFactory transformerFactory = TransformerFactory.newInstance();
		Transformer transformer = transformerFactory.newTransformer();
		Source msgContent = msg.getSOAPPart().getContent();
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		StreamResult result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String requestXml = new String(bos.toByteArray());
		//System.out.println("xml:"+requestXml);
		LogUtils.debug("nature移库单推送接口发送：" + requestXml);

		//添加Basic验证
		BindingProvider bp = (BindingProvider) dispatch;
		bp.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, username);
		bp.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, password);
		

		//5、通过Dispatch传递消息,会返回响应消息
		SOAPMessage response = dispatch.invoke(msg);
		response.writeTo(System.out);
		msgContent = response.getSOAPPart().getContent();
		bos = new ByteArrayOutputStream();
		result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String responseXml = new String(bos.toByteArray());
		//System.out.println("responseXml:"+responseXml);
		LogUtils.debug("nature移库单推送接口返回：" + responseXml);
		
		
		

		//6、响应消息处理,将响应的消息转换为dom对象
		Document doc = response.getSOAPPart()
				.getEnvelope()
				.getBody()
				.extractContentAsDocument();
		String returnStatus = doc.getElementsByTagName("returnStatus")
				.item(0)
				.getTextContent();
		String returnMsg = "";
		
		if (returnStatus.equals("S")) {
			returnMsg = "S";

		}
		else {
			returnMsg = doc.getElementsByTagName("returnMsg")
					.item(0)
					.getTextContent();
		}
		
		return returnMsg;
	}
	
	public static void main(String[] args) throws Exception {
		String msg = "";
		HashMap<String, Object> params = new HashMap<String, Object>();
		params.put("HEADER_ID", 1);
		params.put("REQUEST_NUMBER", DateUtil.convert(new Date()));
		msg =new NatureMoveLibraryCreate().createMoveLibrary(params);
		System.out.println("msg:"+msg);
	}
	
}
