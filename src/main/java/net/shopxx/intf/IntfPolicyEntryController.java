package net.shopxx.intf;


import com.alibaba.fastjson.JSON;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.*;
import net.shopxx.finance.service.PolicyEntryService;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static net.shopxx.util.CommonUtil.objectToLong;

/**
 * 售后政策单接口
 */
@Controller("intfPolicyEntryController")
@RequestMapping("/intf/intfPolicyEntry")
public class IntfPolicyEntryController extends BaseController {

    @Resource(name = "policyEntryServiceImpl")
    private PolicyEntryService policyEntryService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgService;

    @Resource(name = "organizationServiceImpl")
    private OrganizationService organizationService;
    @Resource(name = "sbuServiceImpl")
    private SbuService sbuService;
    @Resource(name = "storeMemberSbuServiceImpl")
    private StoreMemberSbuService storeMemberSbuService;
    @Resource(name = "companyInfoBaseServiceImpl")
    CompanyInfoBaseService companyInfoService;



    /**
     * 接收政策信息
     */
    @RequestMapping(value = "/receptionPolicyInfo", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg receptionPolicyInfo(@RequestBody(required = true) List<Map<String,Object>> depositRecharges, HttpServletRequest request, HttpServletResponse response, HttpSession session){
        if (depositRecharges == null || depositRecharges.size() == 0){
            return error("未接收到list数据，请检查参数格式");
        }else {
            //打印接收数据
            LogUtils.info("depositRecharges:"+depositRecharges);
            for (Map<String,Object> dr : depositRecharges){
                //创建政策实体
                DepositRecharge depositRecharge = JSON.parseObject(JSON.toJSONString(dr), DepositRecharge.class);
                if (ConvertUtil.isEmpty(depositRecharge.getAmount())){
                    ExceptionUtil.throwServiceException("政策金额不能为空！");
                }
                List<Filter> filters = new ArrayList<Filter>();
                CompanyInfo companyInfo = companyInfoService.find(9L);
                Long companyInfoId = companyInfo.getId();
                //创建变量赋值
                //客户
                String storeName = String.valueOf(dr.get("storeName")).trim();
                String storeSn = String.valueOf(dr.get("storeSn")).trim();
                filters.clear();
                filters.add(Filter.eq("outTradeNo", storeSn));
                filters.add(Filter.eq("companyInfoId", companyInfoId));
                filters.add(Filter.eq("name", storeName));
                Store store = storeService.find(filters);
                depositRecharge.setStore(store);
                if (store == null) {
                    // 操作错误，客户不存在
                    ExceptionUtil.throwServiceException("客户不存在");
                }
                //机构
                String saleOrgName = String.valueOf(dr.get("saleOrgName")).trim();
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfoId));
                filters.add(Filter.eq("name",saleOrgName));
                filters.add(Filter.eq("isEnabled",true));
                SaleOrg saleOrg = saleOrgService.find(filters);
                if (saleOrg==null){
                    ExceptionUtil.throwServiceException("机构不存在，请检查名称是否正确");
                }
                depositRecharge.setSaleOrg(saleOrg);
                //经营组织
                String organizationCode = String.valueOf(dr.get("organizationCode")).trim();
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfoId));
                filters.add(Filter.eq("code",organizationCode));
                filters.add(Filter.eq("isEnabled",true));
                Organization organization = organizationService.find(filters);
                if (organization==null){
                    ExceptionUtil.throwServiceException("经营组织不存在，请检查编码或名称是否正确");
                }
                depositRecharge.setOrganization(organization);

                //充值类型
                Long rechargeTypeId = null;
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfoId));
                filters.add(Filter.eq("isEnabled",true));
                filters.add(Filter.eq("value","政策录入"));
                SystemDict rechargeType = systemDictService.find(filters);
                if (rechargeType!=null){
                    rechargeTypeId = rechargeType.getId();
                }else {
                    ExceptionUtil.throwServiceException("充值类型未查到为空，请am维护系统词汇！");
                }

                //政策类型
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfoId));
                filters.add(Filter.eq("isEnabled",true));
                filters.add(Filter.eq("value","售后赔付"));
                SystemDict policyType = systemDictService.find(filters);
                if (policyType==null){
                    ExceptionUtil.throwServiceException("默认售后赔付政策类型未查到为空，请am系统词汇维护!");
                }
                depositRecharge.setPolicyType(policyType);
                //发票类型
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfoId));
                filters.add(Filter.eq("isEnabled",true));
                filters.add(Filter.eq("value","其它"));
                SystemDict invoiceType = systemDictService.find(filters);
                if (invoiceType==null){
                    ExceptionUtil.throwServiceException("默认其它发票类型未查到为空，请am维护系统词汇!");
                }
                depositRecharge.setInvoiceType(invoiceType);
                if (invoiceType.getLowerCode()!=null && "1".equals(invoiceType.getLowerCode())){
                    depositRecharge.setHasInvoice(1);
                }else {
                    depositRecharge.setHasInvoice(0);
                }
                //erp现金流项目
                Long cashProjectId = objectToLong(dr.get("cashProjectId"));
                String cashProjectName = String.valueOf(dr.get("cashProjectName")).trim();
                SystemDict cashProject = systemDictService.find(cashProjectId);
                if ((ConvertUtil.isEmpty(cashProjectId)||cashProject==null)&&!ConvertUtil.isEmpty(cashProjectName) ){
                    filters.clear();
                    filters.add(Filter.eq("companyInfoId", companyInfoId));
                    filters.add(Filter.eq("isEnabled",true));
                    filters.add(Filter.eq("value",cashProjectName));
                    cashProject = systemDictService.find(filters);
                }
                if (cashProject!=null){
                    depositRecharge.setCashProject(cashProject);
                }

                //sbu
                String sbuName = String.valueOf(dr.get("sbuName")).trim();
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfoId));
                filters.add(Filter.eq("name",sbuName));
                Sbu sbu=sbuService.find(filters);

                if (sbu==null){
                    ExceptionUtil.throwServiceException("sbu为空，请检查名称是否正确!");
                }
                depositRecharge.setSbu(sbu);
                depositRecharge.setCompanyInfoId(companyInfoId);
                depositRecharge.setSourceType(2);  //来源类型 中板同步
                depositRecharge.setGlDate(new Date());
                depositRecharge.setApplyDate(new Date());
                //税率默认 OUT_VAT13
                depositRecharge.setTaxRate(BigDecimal.valueOf(13));
                if (depositRecharge.getTaxRate().compareTo(new BigDecimal("0")) != 0 && depositRecharge.getHasInvoice() == 1) {
                    ExceptionUtil.throwServiceException("有发票税率类型必须为0");
                }
                //保存
//                filters.clear();
//                filters.add(Filter.eq("sbu", sbu));
//                List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
//                if (sbus.size() == 0 || sbus == null) {
//                    ExceptionUtil.throwServiceException("该客户没有维护此类型SBU");
//                }
                String sn = String.valueOf(dr.get("sn")).trim();
                filters.clear();
                filters.add(Filter.eq("sn", sn));
                DepositRecharge dRecharge = policyEntryService.find(filters);
                if (ConvertUtil.isEmpty(sn)|| dRecharge==null){    //新增
                    policyEntryService.save(store, depositRecharge, 5, rechargeTypeId,0,invoiceType.getId());
                }else {        //更新数据
                    dRecharge.setCashProject(cashProject);
                    dRecharge.setOrganization(organization);
                    dRecharge.setSaleOrg(saleOrg);
                    dRecharge.setStore(store);
                    dRecharge.setSn(sn);
                    dRecharge.setSbu(sbu);
                    dRecharge.setAmount(depositRecharge.getAmount());
                    dRecharge.setRechargeType(rechargeType);
                    dRecharge.setBalanceMonth(depositRecharge.getBalanceMonth());
                    dRecharge.setApplyDate(depositRecharge.getApplyDate());
                    dRecharge.setGlDate(depositRecharge.getGlDate());
                    dRecharge.setMemo(depositRecharge.getMemo());
                    dRecharge.setSourceSn(depositRecharge.getSourceSn());
                    depositRecharge.setSourceType(2);  //来源类型 中板同步
                    policyEntryService.update(dRecharge);
                }
            }
        }
        return success().addObjX("接收数据："+depositRecharges.size()+" 条");
    }


}
