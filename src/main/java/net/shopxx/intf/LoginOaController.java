
package net.shopxx.intf;

import net.shopxx.aftersales.controller.AftersaleController;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.*;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.controller.CreditRechargeController;
import net.shopxx.member.controller.StoreApplyController;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.LoginOutBaseService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.mobile.act.controller.ActWfController;
import net.shopxx.mobile.b2b.controller.MobileCreditRechargeController;
import net.shopxx.mobile.b2b.controller.MobilePriceApplyController;
import net.shopxx.order.b2b.controller.PriceApplyController;
import net.shopxx.shop.controller.AcceptanceController;
import net.shopxx.shop.controller.AcceptanceReimburseController;
import net.shopxx.shop.controller.ShopAddedController;
import net.shopxx.shop.controller.ShopDeviseController;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@Controller("loginOaController")
@RequestMapping({"/intf/loginOa"})
public class LoginOaController extends BaseController {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "loginOutBaseServiceImpl")
	private LoginOutBaseService loginOutBaseService;
	@Resource(name = "aftersalesAftersaleController")
	private AftersaleController aftersalesController;
	@Resource(name = "b2bPriceApplyController")
	private PriceApplyController priceApplyController;
	@Resource(name = "shopAddedController")
	private ShopAddedController shopAddedController;
	@Resource(name = "shopDeviseController")
	private ShopDeviseController shopDeviseController;
	@Resource(name = "acceptanceController")
	private AcceptanceController acceptanceController;
	@Resource(name = "acceptanceReimburseController")
	private AcceptanceReimburseController acceptanceReimburseController;
	@Resource(name = "mobilePriceApplyController")
	private MobilePriceApplyController mobilePriceApplyController;
	@Resource(name = "mobileActWfController")
	private ActWfController actWfController;
	@Resource(name = "shopCreditRechargeController")
	private CreditRechargeController creditRechargeController;
	@Resource(name = "b2bMobileCreditRechargeController")
	private MobileCreditRechargeController mobileCreditRechargeController;
	@Resource(name = "storeApplyController")
	private StoreApplyController storeApplyController;








	@RequestMapping(value = "/login", method = RequestMethod.GET)
	public String add(String url,String language,HttpServletRequest request,HttpServletResponse response,ModelMap model) throws Exception {


		String[] str=url.split("\\.");
		String username =str[0];
		Integer type=Integer.parseInt(str[1]);
		Long id =Long.parseLong(str[2]);
		String isApp=str[3];
		String companyInfoName="大自然";


		CompanyInfo companyInfo = null;
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("simple_name", companyInfoName));
		try {
			companyInfo = companyInfoBaseService.find(filters);
		}
		catch (Exception e) {}
		if (companyInfo == null) {
			filters.clear();
			filters.add(Filter.eq("company_name", companyInfoName));
			companyInfo = companyInfoBaseService.find(filters);
		}


		List<StoreMember> storeMembers;
		StoreMember storeMember = null;
		storeMembers=storeMemberBaseService.findDefaultByUsername(username,
				companyInfo.getId());
		if(storeMembers.size()>0){
			storeMember = storeMembers.get(0);
		}else{
			//该用户不存在
			ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3003",
					language));
		}
		//查询是否企业管理员
		int isadmin = 0;
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		if (companyInfo == null) {
			filters.add(Filter.isNull("companyInfoId"));
		}
		else {
			filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
		}
		List<PcUserRole> pcUserRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		System.out.println("SIZE:"+pcUserRoles.size());
		for (PcUserRole pcUserRole : pcUserRoles) {
			PcRole pcRole = pcUserRole.getPcRole();
			if (pcRole.getCompanyInfoId() == null) {
				isadmin = 1;
				break;
			}
		}

		language="ChinaCN";

		WebUtils.setPrincipal(storeMember.getId(),
				storeMember.getUsername(),
				companyInfo == null ? null : companyInfo.getId(),
				companyInfo == null ? null : companyInfo.getCompany_code(),
				language,
				isadmin);

		Setting setting = SettingUtils.get();
		String loginDomain = setting.getLoginDomain();
		if (!ConvertUtil.isEmpty(loginDomain)) {
			//加入cookie及缓存
			String uuid = UUID.randomUUID().toString();
			WebUtils.addCookie(request,
					response,
					Global.PRINCIPAL_PROPERTY_NAME,
					uuid);
			EhCacheUtil.put(Global.PRINCIPAL_PROPERTY_NAME,
					uuid,
					WebUtils.getPrincipal());
		}

		String fromUrl=null;
		String code="b";
		String flag="a";

		try {
			if(type==10002 && !isApp.equals("app")){//售后申请

				fromUrl=aftersalesController.edit(id,model);

			}else if(type==100023 && !isApp.equals("app")){//特价申请

				fromUrl=priceApplyController.edit(code, id, null, null, model);

			}else if(type==100023 && isApp.equals("app")){//特价申请 移动端

				fromUrl=actWfController.wf(id, request, model);

			}else if(type==10001 && !isApp.equals("app")){//门店新增

				fromUrl=shopAddedController.edit(id,model);

			}else if(type==10001 && isApp.equals("app")){//门店新增  移动端

				fromUrl=actWfController.wf(id, request, model);

			}else if(type==10004 && !isApp.equals("app")){//门店设计

				fromUrl=shopDeviseController.edit(id,model);

			}else if(type==10004 && isApp.equals("app")){//门店设计 移动端

				fromUrl=actWfController.wf(id, request, model);

			}else if(type==10005 && !isApp.equals("app")){//门店装修验收

				fromUrl=acceptanceController.edit(id, model);

			}else if(type==10005 && isApp.equals("app")){//门店装修验收 移动端

				fromUrl=actWfController.wf(id, request, model);

			}else if(type==10003 && !isApp.equals("app")){//门店装修验收及报销

				fromUrl=acceptanceReimburseController.edit(id, model);

			}else if(type==10003 && isApp.equals("app")){//门店装修验收及报销 移动端

				fromUrl=actWfController.wf(id, request, model);

			}else if(type==100026  && !isApp.equals("app")) {//赊销申请

				fromUrl=creditRechargeController.view_code(flag,null,id,null, model);

			}else if(type==100026  && isApp.equals("app")) {//赊销申请 移动端

				fromUrl=actWfController.wf(id, request, model);

			}else if(type==100018  && !isApp.equals("app")) {//客户新增

				fromUrl=storeApplyController.edit("b", id, null, model);

			}else if(type==100018  && isApp.equals("app")) { //客户新增 移动端

				fromUrl=actWfController.wf(id, request, model);

			}else if(type==10007  && isApp.equals("app")) {    //10007 门店减少 移动端

				fromUrl=actWfController.wf(id, request, model);

			}
		}catch (Exception e) {
			LogUtils.error("------------------OA待办跳转异常", e);
		}

		return fromUrl;
	}

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, Integer flag, Long objTypeId,
						  Long objid, ModelMap model) {
		System.out.println("11111111111111");
		model.addAttribute("flag", flag);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);

		return "/b2b/shipping/list_tb";
	}

	public static void main(String[] args) {
		String url="01061969.100023.165301.app";
		String[] str=url.split("\\.");
		String username =str[0];
		Integer type=Integer.parseInt(str[1]);
		Long id =Long.parseLong(str[2]);
		String isApp=str[3];
		if(type==100023 && isApp.equals("app")){//特价申请

			System.out.println("id:"+id);
		}
	}

}
