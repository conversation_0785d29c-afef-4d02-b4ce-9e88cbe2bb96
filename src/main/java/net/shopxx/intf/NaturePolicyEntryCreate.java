package net.shopxx.intf;

import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPMessage;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;

import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.LogUtils;

import org.w3c.dom.Document;

//
public class NaturePolicyEntryCreate {

	private static String ns = "http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_ar_trx_soa_pkg/";
//	private static String wsdlUrl = "http://202.104.26.165:8082/api/v1/Erp/InvoiceReceivable/Sync";//测试
//	private static String wsdlUrl = "http://202.104.26.164:8082/api/v1/Erp/InvoiceReceivable/Sync";//正式
//	private static String username = "link01";
//	private static String password = "7iURWx3JNDQFvhT7";//测试
//	private static String password = "tzNUwOiRry3juNAR";//正式
//	private static String password = "h9b4xvNAslumcy1i";//UAT测试

	private static String wsdlUrl =  Global.getLoader().getProperty("dbintf.url")+"/api/v1/Erp/InvoiceReceivable/Sync";
	private static String username = Global.getLoader().getProperty("dbintf.username");
	private static String password = Global.getLoader().getProperty("dbintf.password");
	
	public String createPolicy(Map<String, Object> params) throws Exception {

		//1、创建服务(Service)
		URL url = new URL(wsdlUrl);
		QName sname = new QName(ns, "SCUX_NATURE_AR_TRX_SOA_PKG_Service");
		Service service = Service.create(url, sname);

		//2、创建Dispatch
		Dispatch<SOAPMessage> dispatch = service.createDispatch(new QName(ns,
				"SCUX_NATURE_AR_TRX_SOA_PKG_Port"),
				SOAPMessage.class,
				Service.Mode.MESSAGE);

		//3、创建SOAPMessage
		SOAPMessage msg = MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)
				.createMessage();
		SOAPEnvelope envelope = msg.getSOAPPart().getEnvelope();
		//4、创建QName来指定消息中传递数据
		envelope.addNamespaceDeclaration("cre",
				"http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_ar_trx_soa_pkg/create_trx/");
		SOAPBody body = envelope.getBody();

		SOAPElement ele = body.addChildElement("InputParameters", "cre");

		// 传递参数 
		SOAPElement esbInfo = ele.addChildElement("esbInfo", "cre");
		esbInfo.addChildElement("instId", "cre").setValue(UUID.randomUUID()
				.toString());
		esbInfo.addChildElement("requestTime", "cre")
				.setValue(DateUtil.convert(new Date(),
						"yyyy-MM-dd HH:mm:ss.SSS"));
		esbInfo.addChildElement("attr1", "cre");
		esbInfo.addChildElement("attr2", "cre");
		esbInfo.addChildElement("attr3", "cre");

		SOAPElement queryInfo = ele.addChildElement("queryInfo", "cre");
		queryInfo.addChildElement("pageSize", "cre");
		queryInfo.addChildElement("currentPage", "cre");

		SOAPElement requestInfo = ele.addChildElement("requestInfo", "cre");

		SOAPElement headerRec = requestInfo.addChildElement("HEADER", "cre");
		SOAPElement lineTbl = requestInfo.addChildElement("LINE", "cre");
		SOAPElement lineItem = lineTbl.addChildElement("LINE_ITEM", "cre");

		//表头
		headerRec.addChildElement("SOURCE_CODE", "cre")
				.setValue(params.get("SOURCE_CODE").toString());//来源系统代码,固定“CRM”
		headerRec.addChildElement("SOURCE_ID", "cre")
				.setValue(params.get("SOURCE_ID").toString());//来源ID,政策单id
		headerRec.addChildElement("ORG_ID", "cre")
				.setValue(params.get("ORG_ID").toString());//经营组织id
		headerRec.addChildElement("BATCH_SOURCE_NAME", "cre")
				.setValue(params.get("BATCH_SOURCE_NAME").toString());//来源,传名称：CRM发票
		headerRec.addChildElement("BILL_TO_CUSTOMER_NUM", "cre")
				.setValue(params.get("BILL_TO_CUSTOMER_NUM").toString());//客户编码
		headerRec.addChildElement("BILL_TO_LOCATION", "cre")
				.setValue(params.get("BILL_TO_LOCATION").toString());//客户收单地址编码
		headerRec.addChildElement("ATTRIBUTE2", "cre")
				.setValue(params.get("ATTRIBUTE2").toString());//业务核算组（SBU）
		headerRec.addChildElement("CURRENCY_CODE", "cre")
				.setValue(params.get("CURRENCY_CODE").toString());//币种  固定传“CNY”
		headerRec.addChildElement("CUST_TRX_TYPE_NAME", "cre")
				.setValue(params.get("CUST_TRX_TYPE_NAME").toString());//发票类型
		headerRec.addChildElement("TERM_NAME", "cre")
				.setValue(params.get("TERM_NAME").toString());//付款条件名称  固定传“立即”
		headerRec.addChildElement("TRX_DATE", "cre")
				.setValue(params.get("TRX_DATE").toString());//事务处理日期  传当前日期
		headerRec.addChildElement("GL_DATE", "cre")
				.setValue(params.get("GL_DATE").toString());//总账日期  传当前日期
		headerRec.addChildElement("TRX_NUMBER", "cre")
				.setValue(params.get("TRX_NUMBER").toString());//政策单号
		headerRec.addChildElement("COMMENTS", "cre")
				.setValue(params.get("COMMENTS").toString());//备注
		headerRec.addChildElement("ERP_USER_NAME", "cre")
				.setValue(params.get("ERP_USER_NAME").toString());//单据创建人（创建人账号，即工号）
		headerRec.addChildElement("SHIP_TO_CUSTOMER_NUM", "cre")
				.setValue(params.get("SHIP_TO_CUSTOMER_NUM").toString());//客户收货方编码
		headerRec.addChildElement("SHIP_TO_LOCATION", "cre")
				.setValue(params.get("SHIP_TO_LOCATION").toString());//客户收货方编码
		headerRec.addChildElement("INTERFACE_HEADER_ATTRIBUTE1", "cre")
				.setValue(params.get("INTERFACE_HEADER_ATTRIBUTE1").toString());//客户收货方编码
		headerRec.addChildElement("ATTRIBUTE10", "cre")
				.setValue(params.get("ATTRIBUTE10").toString());//发票提头
		headerRec.addChildElement("ATTRIBUTE11", "cre")
				.setValue(params.get("ATTRIBUTE11").toString());//项目编号
		headerRec.addChildElement("ATTRIBUTE12", "cre")
				.setValue(params.get("ATTRIBUTE12").toString());//最终客户
		headerRec.addChildElement("ATTRIBUTE1", "cre")
				.setValue(params.get("ATTRIBUTE1").toString());//工程项目
		headerRec.addChildElement("CONVERSION_TYPE", "cre")
				.setValue(params.get("CONVERSION_TYPE").toString());//汇率类型
		headerRec.addChildElement("CONVERSION_DATE", "cre")
				.setValue(params.get("CONVERSION_DATE").toString());//汇率日期
		headerRec.addChildElement("CONVERSION_RATE", "cre")
				.setValue(params.get("CONVERSION_RATE").toString());//汇率

		//明细
		Map<String, Object> lineParam = (Map) params.get("LINE_TBL");
		lineItem.addChildElement("LINE_NUMBER", "cre")
				.setValue(lineParam.get("LINE_NUMBER").toString());//事务处理行号（固定传“1”）
		lineItem.addChildElement("DESCRIPTION", "cre")
				.setValue(lineParam.get("DESCRIPTION").toString());//政策类型
		lineItem.addChildElement("QUANTITY", "cre")
				.setValue(lineParam.get("QUANTITY").toString());//数量（固定传“1”）
		lineItem.addChildElement("UNIT_SELLING_PRICE", "cre")
				.setValue(lineParam.get("UNIT_SELLING_PRICE").toString());//政策金额
		lineItem.addChildElement("TAX_RATE_CODE", "cre")
				.setValue(lineParam.get("TAX_RATE_CODE").toString());//税率
		lineItem.addChildElement("UOM_CODE", "cre")
				.setValue(lineParam.get("UOM_CODE").toString());//单位

//		for (Map<String, Object> lineParam : lineParams) {
//
//			SOAPElement lineTblItem = lineTbl.addChildElement("LINE_TBL_ITEM",
//					"cre");
//
//			Map<String, Object> lineData = (Map) lineParam.get("LINE_TBL_ITEM");
//
//			lineTblItem.addChildElement("SOURCE_CODE", "cre")
//					.setValue(lineData.get("SOURCE_CODE").toString());//来源系统代码,CRM
//			lineTblItem.addChildElement("SOURCE_NUM", "cre")
//					.setValue(lineData.get("SOURCE_NUM").toString());//来源ID,CRM发货通知单头id
//			lineTblItem.addChildElement("SOURCE_LINE_NUM", "cre")
//					.setValue(lineData.get("SOURCE_LINE_NUM").toString());//来源行ID,CRM发货通知单行id
//			lineTblItem.addChildElement("ORDERED_ITEM", "cre")
//					.setValue(lineData.get("ORDERED_ITEM").toString());//物料编码
//			lineTblItem.addChildElement("MATERIAL_GRADE", "cre")
//					.setValue(lineData.get("MATERIAL_GRADE").toString());//物料等级,订单行ATTRIBUTE2，传文本：优等品、二等品
//			lineTblItem.addChildElement("ORDERED_QUANTITY", "cre")
//					.setValue(lineData.get("ORDERED_QUANTITY").toString());//数量（平方数）
//			lineTblItem.addChildElement("ORDER_QUANTITY_UOM", "cre")
//					.setValue(lineData.get("ORDER_QUANTITY_UOM").toString());//单位（平方）,m2
//			lineTblItem.addChildElement("ORDERED_QUANTITY2", "cre")
//					.setValue(lineData.get("ORDERED_QUANTITY2").toString());//数量（支）
//			lineTblItem.addChildElement("ORDERED_QUANTITY_UOM2", "cre")
//					.setValue(lineData.get("ORDERED_QUANTITY_UOM2").toString());//辅助单位（支）,支
//			lineTblItem.addChildElement("UNIT_SELLING_PRICE", "cre")
//					.setValue(lineData.get("UNIT_SELLING_PRICE").toString());//单价（平方）,2位小数
//			lineTblItem.addChildElement("SHIP_FROM", "cre")
//					.setValue(lineData.get("SHIP_FROM").toString());//仓库代码,取CRM主表仓库代码
//			lineTblItem.addChildElement("ORGANIZATION_ID", "cre")
//					.setValue(lineData.get("ORGANIZATION_ID").toString());//库存组织id,根据仓库的库存组织id来取
//			lineTblItem.addChildElement("SCHEDULE_ARRIVAL_DATE", "cre")
//					.setValue(lineData.get("SCHEDULE_ARRIVAL_DATE").toString());//交货日期,取CRM主表交货日期
//			lineTblItem.addChildElement("ATTRIBUTE17", "cre")
//					.setValue(lineData.get("ATTRIBUTE17").toString());//箱数,全局行弹性域
//			lineTblItem.addChildElement("ATTRIBUTE18", "cre")
//					.setValue(lineData.get("ATTRIBUTE18").toString());//联系人信息,全局行弹性域，传CRM主表收货人姓名和电话 前两段为姓名和电话用“；”分隔， 比如 “李工;16543060061;;;;;”
//			lineTblItem.addChildElement("LINE_TYPE", "cre")
//					.setValue(lineData.get("LINE_TYPE").toString());//订单行类型,不传
//			lineTblItem.addChildElement("ATTRIBUTE3", "cre")
//					.setValue(lineData.get("ATTRIBUTE3").toString());//物料色号,全局行弹性域，不传
//			lineTblItem.addChildElement("ATTRIBUTE1", "cre")
//					.setValue(lineData.get("ATTRIBUTE1").toString());//合同编号,全局行弹性域，不传
//			lineTblItem.addChildElement("ATTRIBUTE19", "cre")
//					.setValue(lineData.get("ATTRIBUTE19").toString());//实际收货数量,全局行弹性域，不传
//			lineTblItem.addChildElement("ATTRIBUTE20", "cre")
//					.setValue(lineData.get("ATTRIBUTE20").toString());//是否已经同步,全局行弹性域，不传
//			lineTblItem.addChildElement("ATTRIBUTE4", "cre")
//					.setValue(lineData.get("ATTRIBUTE4").toString());//发票抬头,全局行弹性域，不传
//			lineTblItem.addChildElement("ATTRIBUTE5", "cre")
//					.setValue(lineData.get("ATTRIBUTE5").toString());//成品编码,家居行弹性域，不传
//			lineTblItem.addChildElement("ATTRIBUTE6", "cre")
//					.setValue(lineData.get("ATTRIBUTE6").toString());//成品序号,家居行弹性域，不传
//			lineTblItem.addChildElement("ATTRIBUTE7", "cre")
//					.setValue(lineData.get("ATTRIBUTE7").toString());//门类别,家居行弹性域，不传
//			lineTblItem.addChildElement("ATTRIBUTE8", "cre")
//					.setValue(lineData.get("ATTRIBUTE8").toString());//木门玻璃型号,家居行弹性域，不传
//
//		}

		msg.writeTo(System.out);
		TransformerFactory transformerFactory = TransformerFactory.newInstance();
		Transformer transformer = transformerFactory.newTransformer();
		Source msgContent = msg.getSOAPPart().getContent();
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		StreamResult result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String requestXml = new String(bos.toByteArray());
		LogUtils.debug("nature政策无发票推送接口发送：" + requestXml);

		//添加Basic验证
		BindingProvider bp = (BindingProvider) dispatch;
		bp.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, username);
		bp.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, password); 

		//5、通过Dispatch传递消息,会返回响应消息
		SOAPMessage response = dispatch.invoke(msg);
//		response.writeTo(System.out);
		msgContent = response.getSOAPPart().getContent();
		bos = new ByteArrayOutputStream();
		result = new StreamResult(bos);
		transformer.transform(msgContent, result);
		String responseXml = new String(bos.toByteArray());
		LogUtils.debug("nature政策无发票接口返回：" + responseXml);

		//6、响应消息处理,将响应的消息转换为dom对象
		Document doc = response.getSOAPPart()
				.getEnvelope()
				.getBody()
				.extractContentAsDocument();
		String returnStatus = doc.getElementsByTagName("returnStatus")
				.item(0)
				.getTextContent();
		String returnMsg = "";
		if (returnStatus.equals("S")) {
			returnMsg = "S";
		}
		else {
			returnMsg = doc.getElementsByTagName("returnMsg")
					.item(0)
					.getTextContent();
		}
		//Map<String, Object> resultData = new HashMap<String, Object>();
		//resultData.put("returnMsg", returnMsg);
		//resultData.put("resultXml", responseXml);
		return returnMsg;
	}

//	public Map<String, Object> createPolicyHaveInvoice(
//			Map<String, Object> params) throws Exception {
//
//		//1、创建服务(Service)
//		URL url = new URL(wsdlUrl);
//		QName sname = new QName(ns, "SCUX_NATURE_AR_TRX_SOA_PKG_Service");
//		Service service = Service.create(url, sname);
//
//		//2、创建Dispatch
//		Dispatch<SOAPMessage> dispatch = service.createDispatch(
//				new QName(ns, "SCUX_NATURE_AR_TRX_SOA_PKG_Port"),
//				SOAPMessage.class,
//				Service.Mode.MESSAGE);
//
//		//3、创建SOAPMessage
//		SOAPMessage msg = MessageFactory
//				.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL).createMessage();
//		SOAPEnvelope envelope = msg.getSOAPPart().getEnvelope();
//		//4、创建QName来指定消息中传递数据
//		envelope.addNamespaceDeclaration("cre",
//				"http://xmlns.oracle.com/apps/cux/soaprovider/plsql/scux_nature_ar_trx_soa_pkg/create_trx/");
//		SOAPBody body = envelope.getBody();
//
//		SOAPElement ele = body.addChildElement("InputParameters", "cre");
//
//		// 传递参数 
//		SOAPElement esbInfo = ele.addChildElement("esbInfo", "cre");
//		esbInfo.addChildElement("instId", "cre")
//				.setValue(UUID.randomUUID().toString());
//		esbInfo.addChildElement("requestTime", "cre").setValue(
//				DateUtil.convert(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
//		esbInfo.addChildElement("attr1", "cre");
//		esbInfo.addChildElement("attr2", "cre");
//		esbInfo.addChildElement("attr3", "cre");
//
//		SOAPElement queryInfo = ele.addChildElement("queryInfo", "cre");
//		queryInfo.addChildElement("pageSize", "cre");
//		queryInfo.addChildElement("currentPage", "cre");
//
//		SOAPElement requestInfo = ele.addChildElement("requestInfo", "cre");
//
//		SOAPElement headerRec = requestInfo.addChildElement("HEADER", "cre");
//		SOAPElement lineTbl = requestInfo.addChildElement("LINE", "cre");
//
//		//表头
//		headerRec.addChildElement("SOURCE_CODE", "cre")
//				.setValue(params.get("SOURCE_CODE").toString());//来源系统代码,固定“CRM”
//
//		headerRec.addChildElement("SOURCE_NUM", "cre")
//				.setValue(params.get("SOURCE_NUM").toString());//来源ID,政策单id
//
//		headerRec.addChildElement("ORG_ID", "cre")
//				.setValue(params.get("ORG_ID").toString());//经营组织id
//
//		headerRec.addChildElement("PAYMENT_METHOD", "cre")
//				.setValue(params.get("PAYMENT_METHOD").toString());//政策类型
//
//		headerRec.addChildElement("RECEIPT_NUMBER", "cre")
//				.setValue(params.get("RECEIPT_NUMBER").toString());//政策单号
//
//		headerRec.addChildElement("CURRENCY_CODE", "cre")
//				.setValue(params.get("CURRENCY_CODE").toString());//币种  固定传“CNY”
//
//		headerRec.addChildElement("NET_AMOUNT", "cre")
//				.setValue(params.get("NET_AMOUNT").toString());//政策金额
//
//		headerRec.addChildElement("RECEIPT_TYPE", "cre")
//				.setValue(params.get("RECEIPT_TYPE").toString());//收款类型  固定传“标准”
//
//		headerRec.addChildElement("ATTRIBUTE2", "cre")
//				.setValue(params.get("ATTRIBUTE2").toString());//ERP现金流量项目   传编码
//
//		headerRec.addChildElement("ATTRIBUTE3", "cre")
//				.setValue(params.get("ATTRIBUTE3").toString());//业务核算组（SBU）
//
//		headerRec.addChildElement("CUSTOMER_NUMBER", "cre")
//				.setValue(params.get("CUSTOMER_NUMBER").toString());//客户编码
//
//		headerRec.addChildElement("LOCATION", "cre")
//				.setValue(params.get("LOCATION").toString());//客户收单地址编码
//
//		headerRec.addChildElement("ERP_USER_NAME", "cre")
//				.setValue(params.get("ERP_USER_NAME").toString());//单据创建人（创建人账号，即工号）
//
//		headerRec.addChildElement("CONVERSION_TYPE", "cre")
//				.setValue(params.get("CONVERSION_TYPE").toString());//汇率类型
//
//		headerRec.addChildElement("CONVERSION_DATE", "cre")
//				.setValue(params.get("CONVERSION_DATE").toString());//汇率日期
//
//		headerRec.addChildElement("CONVERSION_RATE", "cre")
//				.setValue(params.get("CONVERSION_RATE").toString());//汇率
//
//		headerRec.addChildElement("RECEIPT_DATE", "cre")
//				.setValue(params.get("RECEIPT_DATE").toString());//收款日期
//
//		headerRec.addChildElement("GL_DATE", "cre")
//				.setValue(params.get("GL_DATE").toString());//总账日期  传当前日期
//
//		headerRec.addChildElement("MATURITY_DATE", "cre")
//				.setValue(params.get("MATURITY_DATE").toString());//到期日  传当前日期
//
//		headerRec.addChildElement("ATTRIBUTE4", "cre")
//				.setValue(params.get("ATTRIBUTE4").toString());//收支项目
//
//		headerRec.addChildElement("BANK_STATEMENT", "cre")
//				.setValue(params.get("BANK_STATEMENT").toString());//银行流水单据号  CRM默认传1
//
//		headerRec.addChildElement("ATTRIBUTE1", "cre")
//				.setValue(params.get("ATTRIBUTE1").toString());//工程项目
//
//		headerRec.addChildElement("COMMENTS_CASH", "cre")
//				.setValue(params.get("COMMENTS_CASH").toString());//备注
//
//		TransformerFactory transformerFactory = TransformerFactory
//				.newInstance();
//		Transformer transformer = transformerFactory.newTransformer();
//		Source msgContent = msg.getSOAPPart().getContent();
//		ByteArrayOutputStream bos = new ByteArrayOutputStream();
//		StreamResult result = new StreamResult(bos);
//		transformer.transform(msgContent, result);
//		String requestXml = new String(bos.toByteArray());
//		LogUtils.debug("nature政策有发票推送接口发送：" + requestXml);
//
//		//添加Basic验证
//		BindingProvider bp = (BindingProvider) dispatch;
//		bp.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, username);
//		bp.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, password);
//
//		//5、通过Dispatch传递消息,会返回响应消息
//		SOAPMessage response = dispatch.invoke(msg);
////		response.writeTo(System.out);
//		msgContent = response.getSOAPPart().getContent();
//		bos = new ByteArrayOutputStream();
//		result = new StreamResult(bos);
//		transformer.transform(msgContent, result);
//		String responseXml = new String(bos.toByteArray());
//		LogUtils.debug("nature政策有发票接口返回：" + responseXml);
//
//		//6、响应消息处理,将响应的消息转换为dom对象
//		Document doc = response.getSOAPPart()
//				.getEnvelope()
//				.getBody()
//				.extractContentAsDocument();
//		String returnStatus = doc.getElementsByTagName("returnStatus")
//				.item(0)
//				.getTextContent();
//		String returnMsg = "";
//		if (returnStatus.equals("S")) {
//			returnMsg = "S";
//		}
//		else {
//			returnMsg = doc.getElementsByTagName("returnMsg")
//					.item(0)
//					.getTextContent();
//		}
//		Map<String, Object> resultData = new HashMap<String, Object>();
//		resultData.put("returnMsg", returnMsg);
//		resultData.put("resultXml", responseXml);
//		return resultData;
//	}

	public static void main(String[] args) throws Exception {

		HashMap<String, Object> params = new HashMap<String, Object>();
		params.put("SOURCE_CODE", "CRM");
		params.put("SOURCE_NUM", "233");
		params.put("CANCEL_DATE", DateUtil.convert(new Date()));
		params.put("CANCEL_REASON", "");
		params.put("ERP_USER_NAME", "");
		new NaturePolicyEntryCreate().createPolicy(params);
	}
}
