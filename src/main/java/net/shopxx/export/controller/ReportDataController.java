package net.shopxx.export.controller;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.export.entity.ReportData;
import net.shopxx.export.service.ReportDataService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Controller - 报表数据导入
 */
@Controller("reportDataController")
@RequestMapping("/export/reportData")
public class ReportDataController extends BaseController {

    @Resource(name = "reportDataServiceImpl")
    private ReportDataService reportDataService;

    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;

    /**
     * 列表
     */
    @RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
    public String list_tb(@PathVariable String code, Integer isEdit,
                          Integer isStore, Integer readOnly, ModelMap model,Long menuId) {
        model.addAttribute("isEdit", isEdit);
        model.addAttribute("code", code);
        model.addAttribute("isStore", isStore);
        model.addAttribute("readOnly", readOnly);
        model.addAttribute("menuId",menuId);
        return CommonUtil.getFolderPrefix(code) + "/export/reportData/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
    public String list(@PathVariable String code, Integer isEdit,Long userId,Long menuId,
                       Integer isStore, Integer readOnly, Pageable pageable, ModelMap model) {
        model.addAttribute("menuId",menuId);
        menuJumpUtils.getModelMap(model, userId, menuId);
        return CommonUtil.getFolderPrefix(code) + "/export/reportData/list";
    }

    /**
     * 列表数据
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_data(String warehouseName, String outboundCategory, String outboundDateStart, String outboundDateEnd,
                        String deliveryOrderNumber, String department, String customerName,
                        String goodsNumber, String woodName, String goodsCategory,
                        String analysis, String singlePerson, String merchandiser,
                        String firstAuditor, String warehouseKeeper,
                        Pageable pageable, ModelMap model) {

        // 仓库名称	出仓类别	 出库日期  出库单编号	所属部门	客户名称	货物编号	木种名称	货物类别	分析项目	开单人 销售跟单人	第一会计审核人 仓库理货人
        Page<Map<String, Object>> page = reportDataService.findPage(warehouseName,
                outboundCategory,
                outboundDateStart,
                outboundDateEnd,
                deliveryOrderNumber,
                department,
                customerName,
                goodsNumber,
                woodName,
                goodsCategory,
                analysis,
                singlePerson,
                merchandiser,
                firstAuditor,
                warehouseKeeper,
                pageable);

        String jsonPage = JsonUtils.toJson(page);

        return success(jsonPage);
    }

    /**
     * 添加
     */
    @RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
    public String add(@PathVariable String code, ModelMap model) {
        return CommonUtil.getFolderPrefix(code) + "/export/reportData/add";
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg save(ReportData reportData) {

        // 出库日期
        if (reportData.getOutboundDate() == null) {
            return error("出库日期为空 请检查");
        }
        // 支数
        if (reportData.getSupportSeveral() == null) {
            reportData.setSupportSeveral(new BigDecimal(0));
        }
        // 平方数
        if (reportData.getSquareNumber() == null) {
            reportData.setSquareNumber(new BigDecimal(0));
        }
        // 基准价
        if (reportData.getStandardPrice() == null) {
            reportData.setStandardPrice(new BigDecimal(0));
        }
        // 优惠价差
        if (reportData.getPreferentialPrice() == null) {
            reportData.setPreferentialPrice(new BigDecimal(0));
        }
        // 单价
        if (reportData.getUnitPrice() == null) {
            reportData.setUnitPrice(new BigDecimal(0));
        }
        // 金额
        if (reportData.getAmount() == null) {
            reportData.setAmount(new BigDecimal(0));
        }
        // 箱数
        if (reportData.getCartonNumbers() == null) {
            reportData.setCartonNumbers(new BigDecimal(0));
        }
        // 厚度mm
        if (reportData.getThickness() == null) {
            reportData.setThickness(new BigDecimal(0));
        }
        // 宽度mm
        if (reportData.getBreadth() == null) {
            reportData.setBreadth(new BigDecimal(0));
        }
        // 长度mm
        if (reportData.getLength() == null) {
            reportData.setLength(new BigDecimal(0));
        }

        reportDataService.save(reportData);

        return success().addObjX(reportData.getId());
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
    public String edit(@PathVariable String code, Long id,
                       Integer isStore, ModelMap model) {

        ReportData reportData = reportDataService.find(id);
        model.addAttribute("reportData", reportData);
        model.addAttribute("code", code);
        model.addAttribute("isStore", isStore);

        return CommonUtil.getFolderPrefix(code) + "/export/reportData/edit";
    }

    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg update(ReportData reportData) {

        // 支数
        if (reportData.getSupportSeveral() == null) {
            reportData.setSupportSeveral(new BigDecimal(0));
        }
        // 平方数
        if (reportData.getSquareNumber() == null) {
            reportData.setSquareNumber(new BigDecimal(0));
        }
        // 基准价
        if (reportData.getStandardPrice() == null) {
            reportData.setStandardPrice(new BigDecimal(0));
        }
        // 优惠价差
        if (reportData.getPreferentialPrice() == null) {
            reportData.setPreferentialPrice(new BigDecimal(0));
        }
        // 单价
        if (reportData.getUnitPrice() == null) {
            reportData.setUnitPrice(new BigDecimal(0));
        }
        // 金额
        if (reportData.getAmount() == null) {
            reportData.setAmount(new BigDecimal(0));
        }
        // 箱数
        if (reportData.getCartonNumbers() == null) {
            reportData.setCartonNumbers(new BigDecimal(0));
        }
        // 厚度mm
        if (reportData.getThickness() == null) {
            reportData.setThickness(new BigDecimal(0));
        }
        // 宽度mm
        if (reportData.getBreadth() == null) {
            reportData.setBreadth(new BigDecimal(0));
        }
        // 长度mm
        if (reportData.getLength() == null) {
            reportData.setLength(new BigDecimal(0));
        }

        reportDataService.update(reportData);

        return success().addObjX(reportData.getId());
    }

    /**
     *   报表数据——Excel导入
     */
    @RequestMapping(value = "/reportData_excel", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg reportData_excel(MultipartFile file, HttpServletResponse response,
                               ModelMap model) {

        try {
            reportDataService.reportDataImport(file);
            return ResultMsg.success();
        }
        catch (Exception e) {
            LogUtils.error("导入报表数据", e);
            return ResultMsg.error(e.getMessage());
        }
    }

    /**
     * 报表作废
     *
     * @param id
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/list/cancel", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg cancel(Long[] ids) throws Exception {
        reportDataService.cancel(ids);
        return success();
    }

    @RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
    public @ResponseBody
    List<Map<String, Object>> toConditionExport(String warehouseName, String outboundCategory, String outboundDateStart,
                                                String outboundDateEnd,String deliveryOrderNumber, String department, String customerName,
                                                String goodsNumber, String woodName, String goodsCategory, String analysis,
                                                String singlePerson, String merchandiser, String firstAuditor,
                                                String warehouseKeeper, Pageable pageable,
                                                ModelMap model) {

        Integer size = reportDataService.count(warehouseName,
                outboundCategory,
                outboundDateStart,
                outboundDateEnd,
                deliveryOrderNumber,
                department,
                customerName,
                goodsNumber,
                woodName,
                goodsCategory,
                analysis,
                singlePerson,
                merchandiser,
                firstAuditor,
                warehouseKeeper,
                pageable);

//        System.out.println("reportDataService导出：" + size);
        List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
        Map<String, Integer> segments = getSegment();
        int stage = segments.get("segment");
        int page_size = segments.get("size");
        for (int i = 0; i < stage; i++) {
            Map<String, Object> map = new HashMap<String, Object>();
            int total = i * page_size;
            if (size == 0) {
                map.put("data", "0-0");
            }
            else if ((size - total) <= page_size) {
                map.put("data", (total + 1) + "-" + size);
            }
            else {
                map.put("data", (total + 1) + "-" + (i + 1) * page_size);
            }
            lists.add(map);
            if ((size - total) <= page_size) {
                break;
            }
        }
        return lists;
    }

    /**
     * 条件导出
     *
     * @param productCategoryId
     * @param sn
     * @param vonderCode
     * @param mod
     * @param name
     * @param startTime
     * @param endTime
     * @param isMarketable
     * @param pageable
     * @param model
     * @return
     */
    @RequestMapping(value = "/reportData_conditionExport", method = RequestMethod.GET)
    public ModelAndView reportData_conditionExport(String warehouseName, String outboundCategory, String outboundDateStart,
                                                   String outboundDateEnd, String deliveryOrderNumber, String department, String customerName,
                                                   String goodsNumber, String woodName, String goodsCategory, String analysis,
                                                   String singlePerson, String merchandiser, String firstAuditor,
                                                   String warehouseKeeper, Pageable pageable,
                                                   ModelMap model, Integer page) {

        Map<String, Integer> segments = getSegment();
        int size = segments.get("size");

        List<Map<String, Object>> data = reportDataService.findReportDataList(warehouseName,
                outboundCategory,
                outboundDateStart,
                outboundDateEnd,
                deliveryOrderNumber,
                department,
                customerName,
                goodsNumber,
                woodName,
                goodsCategory,
                analysis,
                singlePerson,
                merchandiser,
                firstAuditor,
                warehouseKeeper,
                null,
                page,
                size);

        return this.getModelAndView(data, model);
    }

    /**
     * 选择导出
     *
     * @param ids
     * @param model
     * @return
     */
    @RequestMapping(value = "/selected_reportData", method = RequestMethod.GET)
    public ModelAndView selected_reportData(Long[] ids, ModelMap model) {

        // 仓库名称	出仓类别	 出库日期  出库单编号	所属部门	客户名称	货物编号	木种名称	货物类别	分析项目	开单人 销售跟单人	第一会计审核人 仓库理货人
        List<Map<String, Object>> data = reportDataService.findReportDataList(null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                ids,
                null,
                null);

        return this.getModelAndView(data, model);
    }

    public ModelAndView getModelAndView(List<Map<String, Object>> data,
                                        ModelMap model) {

        // 设置导出表格名
        String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
                + ".xls";
        // 设置标题
        String[] header = { "仓库名称",
                "出仓类别",
                "出库日期",
                "出库单编号",
                "所属部门",
                "客户名称",
                "货物编号",
                "木种名称",
                "规格",
                "等级",
                "支数",
                "平方数",
                "基准价",
                "优惠价差",
                "单价",
                "金额",
                "备注",
                "箱数",
                "含水率",
                "色号",
                "新旧标识",
                "厚度",
                "宽度",
                "长度",
                "货物类别",
                "分析项目",
                "开单人",
                "销售跟单人",
                "第一会计审核人",
                "仓库理货人",
                "第二会计审核人",
                "门卫",
                "核对单号",
                "车号",
                "柜号",
                "封号",
                "客户地址",
                "电话号码",
                "货物所属部门",
                "预留1",
                "预留2",
                "预留3",
                "预留4",
                "预留5",
                "预留6",
                "预留7",
                "预留8",
                "预留9",
                "预留10",
                "预留11",
                "预留12",
                "预留13",
                "预留14",
                "预留15"};

        // 设置单元格取值
        String[] properties = {
                "warehouse_name",
                "outbound_category",
                "outbound_date",
                "delivery_order_number",
                "department",
                "customer_name",
                "goods_number",
                "wood_name",
                "specification",
                "grade",
                "support_several",
                "square_number",
                "standard_price",
                "preferential_price",
                "unit_price",
                "amount",
                "remark",
                "carton_numbers",
                "moisture_content",
                "colour_number",
                "new_old_logo",
                "thickness",
                "breadth",
                "length",
                "goods_category",
                "analysis",
                "single_person",
                "merchandiser",
                "first_auditor",
                "warehouse_keeper",
                "second_auditor",
                "doorkeeper",
                "check_number",
                "wagon_number",
                "container_number",
                "seal_number",
                "customer_address",
                "phone_number",
                "cargo_department",
                "reserved1",
                "reserved2",
                "reserved3",
                "reserved4",
                "reserved5",
                "reserved6",
                "reserved7",
                "reserved8",
                "reserved9",
                "reserved10",
                "reserved11",
                "reserved12",
                "reserved13",
                "reserved14",
                "reserved15" };

        Integer[] widths = { 25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256,
                25 * 256 };

        return new ModelAndView(new ExcelView(filename, null, properties,
                header, widths, null, data, null), model);
    }

    private Map<String, Integer> getSegment() {
        HashMap<String, Integer> map = new HashMap<String, Integer>();
        try {
            String value = SystemConfig.getConfig("SegmentExportConfig",
                    WebUtils.getCurrentCompanyInfoId());
            String[] values = value.split(",");
            map.put("segment", Integer.parseInt(values[0]));
            map.put("size", Integer.parseInt(values[1]));
        }
        catch (Exception e) {
            map.put("segment", 10);
            map.put("size", 10000);
        }
        return map;
    }


}
