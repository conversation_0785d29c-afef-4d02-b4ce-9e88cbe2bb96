package net.shopxx.export.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("reportDataDao")
public class ReportDataDao extends DaoCenter {


    public Page<Map<String, Object>> findPage(String warehouseName, String outboundCategory,
                                              String outboundDateStart, String outboundDateEnd, String deliveryOrderNumber,
                                              String department, String customerName, String goodsNumber,
                                              String woodName, String goodsCategory, String analysis, String singlePerson,
                                              String merchandiser, String firstAuditor,
                                              String warehouseKeeper, Pageable pageable) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

        List<Object> list = new ArrayList<Object>();

        String sql = "select * from xx_report_data where 1=1";

        // 仓库名称	出仓类别	 出库日期  出库单编号	所属部门	客户名称	货物编号	木种名称	货物类别	分析项目	开单人 销售跟单人	第一会计审核人 仓库理货人
        if (!ConvertUtil.isEmpty(warehouseName)) {
            sql += " and warehouse_name like ?";
            list.add("%" + warehouseName + "%");
        }
        if (!ConvertUtil.isEmpty(outboundCategory)) {
            sql += " and outbound_category like ?";
            list.add("%" + outboundCategory + "%");
        }
        if (!ConvertUtil.isEmpty(outboundDateStart)) {
            String str = outboundDateStart.substring(0, 10);
            sql += " and outbound_date >= ?";
            list.add(str + " 00:00:00");
        }
        if (!ConvertUtil.isEmpty(outboundDateEnd)) {
            String str = outboundDateEnd.substring(0, 10);
            sql += " and outbound_date < ?";
            list.add(str + " 23:59:59");
        }
        if (!ConvertUtil.isEmpty(deliveryOrderNumber)) {
            sql += " and delivery_order_number like ?";
            list.add("%" + deliveryOrderNumber + "%");
        }
        if (!ConvertUtil.isEmpty(department)) {
            sql += " and department like ?";
            list.add("%" + department + "%");
        }
        if (!ConvertUtil.isEmpty(customerName)) {
            sql += " and customer_name like ?";
            list.add("%" + customerName + "%");
        }
        if (!ConvertUtil.isEmpty(goodsNumber)) {
            sql += " and goods_number like ?";
            list.add("%" + goodsNumber + "%");
        }
        if (!ConvertUtil.isEmpty(woodName)) {
            sql += " and wood_name like ?";
            list.add("%" + woodName + "%");
        }
        if (!ConvertUtil.isEmpty(goodsCategory)) {
            sql += " and goods_category like ?";
            list.add("%" + goodsCategory + "%");
        }
        if (!ConvertUtil.isEmpty(analysis)) {
            sql += " and analysis like ?";
            list.add("%" + analysis + "%");
        }
        if (!ConvertUtil.isEmpty(singlePerson)) {
            sql += " and single_person like ?";
            list.add("%" + singlePerson + "%");
        }
        if (!ConvertUtil.isEmpty(merchandiser)) {
            sql += " and merchandiser like ?";
            list.add("%" + merchandiser + "%");
        }
        if (!ConvertUtil.isEmpty(firstAuditor)) {
            sql += " and first_auditor like ?";
            list.add("%" + firstAuditor + "%");
        }
        if (!ConvertUtil.isEmpty(warehouseKeeper)) {
            sql += " and warehouse_keeper like ?";
            list.add("%" + warehouseKeeper + "%");
        }

        if (companyInfoId != null) {
            sql += " and company_info_id = ?";
            list.add(companyInfoId);
        }

        sql += " order by create_date desc";

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        System.out.println("报表数据sql:"+sql);
        Page<Map<String, Object>> page = getNativeDao().findPageMap(sql,
                objs,
                pageable);

        return page;
    }

    public List<Map<String,Object>> findReportDataList(String warehouseName, String outboundCategory, String outboundDateStart,String outboundDateEnd, String deliveryOrderNumber, String department, String customerName, String goodsNumber, String woodName, String goodsCategory, String analysis, String singlePerson, String merchandiser, String firstAuditor, String warehouseKeeper, Long[] ids, Integer page, Integer size) {

        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

        List<Object> list = new ArrayList<Object>();

        String sql = "select * from xx_report_data where 1=1";

        // 仓库名称	出仓类别	 出库日期  出库单编号	所属部门	客户名称	货物编号	木种名称	货物类别	分析项目	开单人 销售跟单人	第一会计审核人 仓库理货人
        if (!ConvertUtil.isEmpty(warehouseName)) {
            sql += " and warehouse_name like ?";
            list.add("%" + warehouseName + "%");
        }
        if (!ConvertUtil.isEmpty(outboundCategory)) {
            sql += " and outbound_category like ?";
            list.add("%" + outboundCategory + "%");
        }
        if (!ConvertUtil.isEmpty(outboundDateStart)) {
            String str = outboundDateStart.substring(0, 10);
            sql += " and outbound_date >= ?";
            list.add(str);
        }
        if (!ConvertUtil.isEmpty(outboundDateEnd)) {
            String str = outboundDateEnd.substring(0, 10);
            sql += " and outbound_date < ?";
            list.add(str);
        }
        if (!ConvertUtil.isEmpty(deliveryOrderNumber)) {
            sql += " and delivery_order_number like ?";
            list.add("%" + deliveryOrderNumber + "%");
        }
        if (!ConvertUtil.isEmpty(department)) {
            sql += " and department like ?";
            list.add("%" + department + "%");
        }
        if (!ConvertUtil.isEmpty(customerName)) {
            sql += " and customer_name like ?";
            list.add("%" + customerName + "%");
        }
        if (!ConvertUtil.isEmpty(goodsNumber)) {
            sql += " and goods_number like ?";
            list.add("%" + goodsNumber + "%");
        }
        if (!ConvertUtil.isEmpty(woodName)) {
            sql += " and wood_name like ?";
            list.add("%" + woodName + "%");
        }
        if (!ConvertUtil.isEmpty(goodsCategory)) {
            sql += " and goods_category like ?";
            list.add("%" + goodsCategory + "%");
        }
        if (!ConvertUtil.isEmpty(analysis)) {
            sql += " and analysis like ?";
            list.add("%" + analysis + "%");
        }
        if (!ConvertUtil.isEmpty(singlePerson)) {
            sql += " and single_person like ?";
            list.add("%" + singlePerson + "%");
        }
        if (!ConvertUtil.isEmpty(merchandiser)) {
            sql += " and merchandiser like ?";
            list.add("%" + merchandiser + "%");
        }
        if (!ConvertUtil.isEmpty(firstAuditor)) {
            sql += " and first_auditor like ?";
            list.add("%" + firstAuditor + "%");
        }
        if (!ConvertUtil.isEmpty(warehouseKeeper)) {
            sql += " and warehouse_keeper like ?";
            list.add("%" + warehouseKeeper + "%");
        }

        if (companyInfoId != null) {
            sql += " and company_info_id = ?";
            list.add(companyInfoId);
        }

        if (ids != null && ids.length > 0) {
            StringBuilder inIds = new StringBuilder();
            for (int i = 0; i < ids.length; i++) {
                inIds.append("?,");
                list.add(ids[i]);
            }
            inIds.deleteCharAt(inIds.length() - 1);
            sql += " and id in (" + inIds + ")";
        }

        sql += " order by create_date desc";

        if (page != null && size != null) {
            sql += " limit " + (size * (page - 1)) + "," + size;
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
                objs,
                0);

        return maps;

    }

    public void cancel(String ids) {

        String sql = "delete from xx_report_data where id in("+ ids + ")";

        getNativeDao().delete(sql, null);

    }

    public Integer count(String warehouseName, String outboundCategory, String outboundDateStart, String outboundDateEnd,
                         String deliveryOrderNumber, String department, String customerName, String goodsNumber, String woodName,
                         String goodsCategory, String analysis, String singlePerson, String merchandiser, String firstAuditor,
                         String warehouseKeeper, Pageable pageable) {

        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

        List<Object> list = new ArrayList<Object>();

        String sql = "select count(1) from xx_report_data where 1=1";

        // 仓库名称	出仓类别	 出库日期  出库单编号	所属部门	客户名称	货物编号	木种名称	货物类别	分析项目	开单人 销售跟单人	第一会计审核人 仓库理货人
        if (!ConvertUtil.isEmpty(warehouseName)) {
            sql += " and warehouse_name like ?";
            list.add("%" + warehouseName + "%");
        }
        if (!ConvertUtil.isEmpty(outboundCategory)) {
            sql += " and outbound_category like ?";
            list.add("%" + outboundCategory + "%");
        }
        if (!ConvertUtil.isEmpty(outboundDateStart)) {
            String str = outboundDateStart.substring(0, 10);
            sql += " and outbound_date >= ?";
            list.add(str);
        }
        if (!ConvertUtil.isEmpty(outboundDateEnd)) {
            String str = outboundDateEnd.substring(0, 10);
            sql += " and outbound_date < ?";
            list.add(str);
        }
        if (!ConvertUtil.isEmpty(deliveryOrderNumber)) {
            sql += " and delivery_order_number like ?";
            list.add("%" + deliveryOrderNumber + "%");
        }
        if (!ConvertUtil.isEmpty(department)) {
            sql += " and department like ?";
            list.add("%" + department + "%");
        }
        if (!ConvertUtil.isEmpty(customerName)) {
            sql += " and customer_name like ?";
            list.add("%" + customerName + "%");
        }
        if (!ConvertUtil.isEmpty(goodsNumber)) {
            sql += " and goods_number like ?";
            list.add("%" + goodsNumber + "%");
        }
        if (!ConvertUtil.isEmpty(woodName)) {
            sql += " and wood_name like ?";
            list.add("%" + woodName + "%");
        }
        if (!ConvertUtil.isEmpty(goodsCategory)) {
            sql += " and goods_category like ?";
            list.add("%" + goodsCategory + "%");
        }
        if (!ConvertUtil.isEmpty(analysis)) {
            sql += " and analysis like ?";
            list.add("%" + analysis + "%");
        }
        if (!ConvertUtil.isEmpty(singlePerson)) {
            sql += " and single_person like ?";
            list.add("%" + singlePerson + "%");
        }
        if (!ConvertUtil.isEmpty(merchandiser)) {
            sql += " and merchandiser like ?";
            list.add("%" + merchandiser + "%");
        }
        if (!ConvertUtil.isEmpty(firstAuditor)) {
            sql += " and first_auditor like ?";
            list.add("%" + firstAuditor + "%");
        }
        if (!ConvertUtil.isEmpty(warehouseKeeper)) {
            sql += " and warehouse_keeper like ?";
            list.add("%" + warehouseKeeper + "%");
        }

        if (companyInfoId != null) {
            sql += " and company_info_id = ?";
            list.add(companyInfoId);
        }

        sql += " order by create_date desc";

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        Integer count = getNativeDao().findInt(sql.toString(), objs);

        return count;
    }
}
