package net.shopxx.factory.dao;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.util.RoleJurisdictionUtil;

@Repository("productionSchedulingDao")
public class ProductionSchedulingDao extends DaoCenter {
	
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	
	public Page<Map<String, Object>> findPage(String sn,Long[] billTypeId,Long[] billCategoryId,
			String startTime, String endTime,Long[] saleOrgId,Long[] storeId,Long[] organizationId,
			Long[] productionPlantId,String bCreater,String auditor,Long[] ids,Pageable pageable,
			Long[] sbuId,Long[] billStatus,Long[] wfStates) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT ps.id,ps.sn,bt.value billTypeName,bc.value billCategoryName,ps.bill_date,su.name sbuName, "
					+ " so.name saleOrgName,s.name storeName,o.name organizationName,pt.value productionPlantName, "
					+ " psi.product_code,psi.product_name,psi.wood_type_or_color,psi.demand_quantity,ps.wf_state,ps.status, "
					+ " psi.demand_branch_quantity,psi.demand_piece_quantity,psi.demand_date,psi.production_quantity, "
					+ " psi.production_branch_quantity,psi.production_piece_quantity,psi.reply_date,psi.embryo_model, "
					+ " psi.description,psi.spec,psi.model,psi.unit,ps.remark,psi.remark remarkItem,ps.b_creater,ps.auditor ");
		sql.append(" FROM xx_production_scheduling_item psi  ");
		sql.append(" LEFT JOIN xx_production_scheduling ps ON ps.id = psi.production_scheduling ");
		sql.append(" LEFT JOIN xx_system_dict bt ON bt.id = ps.bill_type ");
		sql.append(" LEFT JOIN xx_system_dict bc ON bc.id = ps.bill_category ");
		sql.append(" LEFT JOIN xx_sale_org so ON so.id = ps.sale_org ");
		sql.append(" LEFT JOIN xx_store s ON s.id = ps.stores ");
		sql.append(" LEFT JOIN xx_organization o ON o.id = ps.organization ");
		sql.append(" LEFT JOIN xx_system_dict pt ON pt.id = ps.production_plant ");
		sql.append(" LEFT JOIN xx_sbu su ON su.id = ps.sbu ");
		sql.append(" LEFT JOIN xx_warehouse w ON w.id = ps.warehouse ");
		sql.append(" WHERE 1=1  ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and ps.company_info_id = ?");
			list.add(companyInfoId);
		}
		//单据状态
		if (billStatus != null && billStatus.length > 0) {
			String os = "";
			for (int i = 0; i < billStatus.length; i++) {
				if (i == billStatus.length - 1)
					os += billStatus[i];
				else
					os += billStatus[i] + ",";
			}
			sql.append(" and  ps.status in (" + os + ")");
		}
		//流程状态
		if (wfStates != null && wfStates.length > 0) {
			String os = "";
			for (int i = 0; i < wfStates.length; i++) {
				if (i == wfStates.length - 1)
					os += wfStates[i];
				else
					os += wfStates[i] + ",";
			}
			sql.append(" and  ps.wf_state in (" + os + ")");
		}
		//单据编号
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and ps.sn like ?");
			list.add("%" + sn + "%");
		}
		//单据类型
		if (billTypeId != null && billTypeId.length > 0) {
			String os = "";
			for (int i = 0; i < billTypeId.length; i++) {
				if (i == billTypeId.length - 1)
					os += billTypeId[i];
				else
					os += billTypeId[i] + ",";
			}
			sql.append(" and  bt.id in (" + os + ")");
		}
		//单据类别
		if (billCategoryId != null && billCategoryId.length > 0) {
			String os = "";
			for (int i = 0; i < billCategoryId.length; i++) {
				if (i == billCategoryId.length - 1)
					os += billCategoryId[i];
				else
					os += billCategoryId[i] + ",";
			}
			sql.append(" and  bc.id in (" + os + ")");
		}
		//单据日期
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and  ps.bill_date >= ?");
			list.add(startTime + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and ps.bill_date <= ?");
			list.add(endTime + " 23:59:59");
		}
		//机构
		if (saleOrgId != null && saleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					os += "'"+saleOrgId[i]+"'";
				else
					os += "'"+saleOrgId[i] + "',";
			}
			sql.append(" and so.id in (" + os + ")");
		}
		//客户
		if (storeId != null && storeId.length > 0) {
			String os = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					os += "'"+storeId[i]+"'";
				else
					os += "'"+storeId[i] + "',";
			}
			sql.append(" and  s.id in (" + os + ")");
		}
		//经营组织
		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'"+organizationId[i]+"'";
				else
					os += "'"+organizationId[i] + "',";
			}
			sql.append(" and  o.id in (" + os + ")");
		}
		//SBU
		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'"+sbuId[i]+"'";
				else
					os += "'"+sbuId[i] + "',";
			}
			sql.append(" and  su.id in (" + os + ")");
		}
		//生产工厂
		if (productionPlantId != null && productionPlantId.length > 0) {
			String os = "";
			for (int i = 0; i < productionPlantId.length; i++) {
				if (i == productionPlantId.length - 1)
					os += productionPlantId[i];
				else
					os += productionPlantId[i] + ",";
			}
			sql.append(" and  pt.id in (" + os + ")");
		}
		//提交人
		if (!ConvertUtil.isEmpty(bCreater)) {
			sql.append(" and ps.b_creater like ?");
			list.add("%" + bCreater + "%");
		}
		//审核人
		if (!ConvertUtil.isEmpty(auditor)) {
			sql.append(" and ps.auditor like ?");
			list.add("%" + auditor + "%");
		}
		//单据Id
		if (ids != null && ids.length > 0) {
			String os = "";
			for (int i = 0; i < ids.length; i++) {
				if (i == ids.length - 1)
					os += ids[i];
				else
					os += ids[i] + ",";
			}
			sql.append(" and  ps.id in (" + os + ")");
		}
		
		//用户仓库
		String warehouseIds = roleJurisdictionUtil.getWarehouseIds();
		if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("-1")){
			if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("0")){
				sql.append(" and w.id in (" + warehouseIds + ")");
			}else{
				sql.append(" and w.id is null");
			}
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and su.id in (" + sbuIds + ")");
			}else{
				sql.append(" and su.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and o.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and o.id is null");
			}
		}
		
		sql.append(" GROUP BY psi.id ORDER BY psi.modify_date desc ");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		
		String totalsql = "select count(1) from ( " + sql + ") t";
		
		long total = getNativeDao().findInt(totalsql, objs);
		
		page.setTotal(total);
		
		return page;
	}
	
	
	/**
	 * 根据生产单id查找明细信息
	 * */
	public List<Map<String, Object>>findProductionSchedulingItemList(Long id) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " select psi.*,pt.branch_per_box,pt.per_branch,pt.per_box"
				+ " from xx_production_scheduling_item psi "
				+ " left join xx_product pt on pt.id = psi.product"
				+ " where 1=1  ";
				if(!ConvertUtil.isEmpty(companyInfoId)){
					sql+=" and psi.company_info_id ="+companyInfoId;
				}
				if(!ConvertUtil.isEmpty(id)){
					sql+=" and psi.production_scheduling ="+id;
				}
               sql+=" group by psi.id order by psi.create_date desc ";
		
		List<Map<String, Object>> productionSchedulingItemList = getNativeDao().findListMap(sql,null,0);

		return productionSchedulingItemList;
	}
	
	
	
	
	
	//查询已完成已审核生产订单的明细
	public Page<Map<String, Object>> findProductionSchedulingItem(String productionSchedulingSn,
			String vonderCode, String productName,String model,Pageable pageable,Long warehouseId,
			Long saleOrgId,Long billTypeId,Long sbuId,Long storeId,Long billCategoryId) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT  ps.id,ps.sn,psi.product_code vonder_code,psi.product_name `name`,psi.product, "
				+ " psi.unit,sdt.value levelName,sdt.id level_Id,p.per_branch,p.per_box,psi.description, "
				+ " p.branch_per_box,p.volume,p.weight,so.name saleOrgId,so.name saleOrgName,w.id warehouseId, "
				+ " w.name warehouseName,s.name storeId,s.name storeName,o.name organizationId,o.name organizationName, "
				+ " su.name sbuId,su.name sbuName,psi.remark remarks,psi.model,psi.id  productionSchedulingItemId ");
		sql.append(" FROM xx_production_scheduling ps  ");
		sql.append(" LEFT JOIN xx_production_scheduling_item psi ON ps.id = psi.production_scheduling  ");
		sql.append(" LEFT JOIN xx_sale_org so ON so.id = ps.sale_org ");
		sql.append(" LEFT JOIN xx_store s ON s.id = ps.stores ");
		sql.append(" LEFT JOIN xx_organization o ON o.id = ps.organization ");
		sql.append(" LEFT JOIN xx_sbu su ON su.id = ps.sbu ");
		sql.append(" LEFT JOIN xx_product p ON psi.product=p.id ");
		sql.append(" LEFT JOIN xx_system_dict sdt ON sdt.id = p.product_level ");
		sql.append(" LEFT JOIN xx_system_dict bt ON bt.id = ps.bill_type ");
		sql.append(" LEFT JOIN xx_system_dict bc ON bc.id = ps.bill_category ");
		sql.append(" LEFT JOIN xx_warehouse w ON w.id = ps.warehouse ");
		sql.append(" WHERE 1=1 AND ps.status in ('2','3')  ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and ps.company_info_id = ?");
			list.add(companyInfoId);
		}
		//生产单编号
		if (!ConvertUtil.isEmpty(productionSchedulingSn)) {
			sql.append(" and ps.sn like ?");
			list.add("%" + productionSchedulingSn + "%");
		}
		//产品名称
		if (!ConvertUtil.isEmpty(productName)) {
			sql.append(" and psi.product_name like ?");
			list.add("%" + productName + "%");
		}
		//产品编码
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and psi.product_code like ?");
			list.add("%" + vonderCode + "%");
		}
		//产品型号
		if (!ConvertUtil.isEmpty(model)) {
			sql.append(" and psi.model like ?");
			list.add("%" + model + "%");
		}
		//仓库
		if (!ConvertUtil.isEmpty(warehouseId)) {
			sql.append(" and w.id = ?");
			list.add(warehouseId);
		}
		//机构
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and so.id = ?");
			list.add(saleOrgId);
		}
		//单据类型
		if (!ConvertUtil.isEmpty(billTypeId)) {
			sql.append(" and bt.id = ?");
			list.add(billTypeId);
		}
		//SBU
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" and su.id = ?");
			list.add(sbuId);
		}
		//客户
		if (!ConvertUtil.isEmpty(storeId)) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		//单据类别
		if (!ConvertUtil.isEmpty(billCategoryId)) {
			sql.append(" and bc.id = ?");
			list.add(billCategoryId);
		}
		
		//用户仓库
		String warehouseIds = roleJurisdictionUtil.getWarehouseIds();
		if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("-1")){
			if(!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("0")){
				sql.append(" and w.id in (" + warehouseIds + ")");
			}else{
				sql.append(" and w.id is null");
			}
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and su.id in (" + sbuIds + ")");
			}else{
				sql.append(" and su.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and o.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and o.id is null");
			}
		}
		
        Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return page;
	}
}
