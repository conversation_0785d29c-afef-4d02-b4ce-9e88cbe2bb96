package net.shopxx.factory.controller;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.factory.entity.ProductionScheduling;
import net.shopxx.factory.entity.ProductionSchedulingItem;
import net.shopxx.factory.service.ProductionSchedulingService;
import net.shopxx.member.entity.Adjustment;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.MoveLibrary;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.service.WarehouseBaseService;
@Controller("productionSchedulingController")
@RequestMapping("/factory/production_scheduling")
public class ProductionSchedulingController  extends BaseController {
	
	
	@Resource(name = "productionSchedulingServiceImpl")
	private ProductionSchedulingService productionSchedulingService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "storeMemberSbuServiceImpl") 
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model,Integer flag,Long menuId) {
		model.addAttribute("flag", flag);
		model.addAttribute("menuId",menuId);
		return "/factory/factory/list_tb";
	}
	
	
	/**
	 * 生产列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/factory/factory/list";
	}
	
	/**
	 * 生产单列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn,Long[] billTypeId,Long[] billCategoryId,
			String startTime, String endTime,Long[] saleOrgId,Long[] storeId,Long[] organizationId,
			Long[] productionPlantId,String bCreater,String auditor,Pageable pageable,Long[] sbuId,
			Long[] billStatus,Long[] wfStates) {

		Page<Map<String, Object>> page = productionSchedulingService.findPage(sn, billTypeId, billCategoryId,
				startTime, endTime, saleOrgId, storeId, organizationId, productionPlantId,
				bCreater, auditor, null, pageable, sbuId, billStatus, wfStates);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}
	
	
	public ModelAndView getModelAndView(List<Map<String, Object>> data, ModelMap model) {
		if(!ConvertUtil.isEmpty(data)){
			for (Map<String, Object> map : data) {
				//单据状态
				if (!ConvertUtil.isEmpty(map.get("status"))) {
					Integer status = (Integer)map.get("status");
					if (status == 0){
						 map.put("status", "已保存");
					}else if (status == 1){
						 map.put("status", "审核中");
					}else if (status == 2){
						 map.put("status", "已审核");
					}else if (status == 3){
						 map.put("status", "已审核");
					}else if (status == 4){
						 map.put("status", "作废");
					}
				}
				//流程状态
				if (!ConvertUtil.isEmpty(map.get("wf_state"))) {
					Integer wfState = (Integer)map.get("wf_state");
					if (wfState == 0){
						 map.put("wf_state", "未启动");
					}else if (wfState == 1){
						 map.put("wf_state", "审核中");
					}else if (wfState == 2){
						 map.put("wf_state", "已完成");
					}else if (wfState == 3){
						 map.put("wf_state", "驳回");
					}
				}
			}
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "单据编号", "单据类型", "单据类别", "单据状态", "流程状态", "单据日期", "机构", "客户", "经营组织","SBU",
				"生产工厂","产品编码", "产品名称", "木种名称", "需求数量", "需求支数", "需求件数", "需求日期",
				"生产数量", "生产支数","生产件数", "回复日期", "胚料型号", "产品描述", "规格","型号", "单位",
				"备注", "行备注","提交人", "审核"};
		// 设置单元格宽度
		Integer[] widths = { 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 ,
				25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 ,
				25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256, 25 * 256,
				25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256};
		// 设置单元格取值
		String[] properties = { "sn", "billTypeName", "billCategoryName", "status", "wf_state", "bill_date", "saleOrgName",
				"storeName", "organizationName", "sbuName", "productionPlantName" , "product_code" , "product_name" ,
				"wood_type_or_color" , "demand_quantity" , "demand_branch_quantity", "demand_piece_quantity",
				"demand_date" , "production_quantity" , "production_branch_quantity", "production_piece_quantity" ,
				"reply_date", "embryo_model", "description" , "spec", "model" , "unit", "remark", "remarkItem" ,
				"b_creater", "auditor" };
		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}
	
	
	/**
	 * 生产单选择导出
	 * @param ids
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model,Pageable pageable) {
		
		Page<Map<String, Object>> page = productionSchedulingService.findPage(null, null, null,
				null, null, null, null, null, null,null, null, ids, pageable, null, null, null);
		
		List<Map<String, Object>> data = page.getContent();
		
		return getModelAndView(data, model);
	}
	
	
	
	
	/**
	 * 生产单条件导出统计数量
	 * @param sn
	 * @param billTypeId
	 * @param billCategoryId
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param productionPlantId
	 * @param bCreater
	 * @param auditor
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(String sn,Long[] billTypeId,
			Long[] billCategoryId,String startTime, String endTime,Long[] saleOrgId,Long[] storeId,
			Long[] organizationId,Long[] productionPlantId,String bCreater,
			String auditor,Pageable pageable,Long[] sbuId,Long[] billStatus,Long[] wfStates) {
		
		Page<Map<String, Object>> page = productionSchedulingService.findPage(sn, billTypeId, billCategoryId,
				startTime, endTime, saleOrgId, storeId, organizationId, productionPlantId,
				bCreater, auditor, null, pageable, sbuId, billStatus, wfStates);
		
		Integer size = (int) page.getTotal(); 
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}


	/**
	 * 生产单条件导出
	 * @param sn
	 * @param billTypeId
	 * @param billCategoryId
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param productionPlantId
	 * @param bCreater
	 * @param auditor
	 * @param pageable
	 * @param model
	 * @param sbuId
	 * @param billStatus
	 * @param wfStates
	 * @param page
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn,Long[] billTypeId,Long[] billCategoryId,String startTime,
			String endTime,Long[] saleOrgId,Long[] storeId,Long[] organizationId,Long[] productionPlantId,
			String bCreater,String auditor,Pageable pageable, ModelMap model,Long[] sbuId,
			Long[] billStatus,Long[] wfStates, Integer page){
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		pageable.setPageSize(size);
		pageable.setPageNumber(page);
		
		Page<Map<String, Object>> mapPage = productionSchedulingService.findPage(sn, billTypeId, billCategoryId,
				startTime, endTime, saleOrgId, storeId, organizationId, productionPlantId,
				bCreater, auditor, null, pageable, sbuId, billStatus, wfStates);
		
		List<Map<String, Object>> data = mapPage.getContent();
		
		return getModelAndView(data, model);
		
	}
	
	/**
	 * 添加生产单
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model,Pageable pageable) {
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		
		//单据类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "billType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> billTypeList = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("billTypeList", billTypeList);
		
		//单据类别
		filters.clear();
		filters.add(Filter.eq("code", "billCategory"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> billCategoryList = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("billCategoryList", billCategoryList);
		
		//用户经营组织
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);
		
		//用户SBU
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberSbu> storeMemberSbuList = storeMemberSbuService.findList(null,filters,null);
		model.addAttribute("storeMemberSbuList", storeMemberSbuList);
		
		//生产工厂
		filters.clear();
		filters.add(Filter.eq("code", "productionPlant"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productionPlantList = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("productionPlantList", productionPlantList);

		return "/factory/factory/add";
	}
	
	
	
	/*
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveProductionScheduling(ProductionScheduling productionScheduling,
			Long billTypeId,Long billCategoryId,Long storeId,Long saleOrgId,   
			Long organizationId,Long productionPlantId,Long sbuId,Long warehouseId) {
		
		//单据类型
		SystemDict billType = systemDictBaseService.find(billTypeId);
		productionScheduling.setBillType(billType);
		//单据类别
		SystemDict billCategory = systemDictBaseService.find(billCategoryId);
		productionScheduling.setBillCategory(billCategory);
		//客户
		Store store = storeService.find(storeId);
		productionScheduling.setStore(store);
		//机构
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		productionScheduling.setSaleOrg(saleOrg);
		//经营组织
		Organization organization = organizationService.find(organizationId);
		productionScheduling.setOrganization(organization);
		//生产工厂
		SystemDict productionPlant = systemDictBaseService.find(productionPlantId);
		productionScheduling.setProductionPlant(productionPlant);
		//SBU
		Sbu sbu = sbuService.find(sbuId);
		productionScheduling.setSbu(sbu);
		//仓库
		Warehouse warehouse = warehouseService.find(warehouseId);
		productionScheduling.setWarehouse(warehouse);
		
			productionSchedulingService.saveProductionScheduling(productionScheduling);
		return success().addObjX(productionScheduling.getId());
	}
	
	
	//查看
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(ModelMap model,Long id) {
		
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		
		//根据Id查询生产单数据
		ProductionScheduling productionScheduling  = productionSchedulingService.find(id);
		
		if(productionScheduling.getWfId()==null&&productionScheduling.getWfState()==0){
			productionScheduling.setStatus(0);
			for (ProductionSchedulingItem productionSchedulingItem : productionScheduling.getProductionSchedulingItems()) {
				productionSchedulingItem.setStatus(0);
			}
			productionScheduling = productionSchedulingService.update(productionScheduling);
		}
		
		model.addAttribute("productionScheduling", productionScheduling);
		//生产单明细
		String productionSchedulingItemList = JsonUtils.toJson(productionSchedulingService.findProductionSchedulingItemList(productionScheduling.getId()));
		model.addAttribute("productionSchedulingItemList", productionSchedulingItemList);
		
		//单据类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "billType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> billTypeList = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("billTypeList", billTypeList);
		
		//单据类别
		filters.clear();
		filters.add(Filter.eq("code", "billCategory"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> billCategoryList = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("billCategoryList", billCategoryList);
		
		//用户经营组织
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);
		
		//用户SBU
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberSbu> storeMemberSbuList = storeMemberSbuService.findList(null,filters,null);
		model.addAttribute("storeMemberSbuList", storeMemberSbuList);
		
		//生产工厂
		filters.clear();
		filters.add(Filter.eq("code", "productionPlant"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productionPlantList = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("productionPlantList", productionPlantList);
		
		//生产单权限控制
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("productionSchedulingUserRoles",companyInfoId);
		int productionSchedulingUserRoles = 0;
		if(!ConvertUtil.isEmpty(userRoles)){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					productionSchedulingUserRoles++;
					break;
				}
			}
		}
		
		if(productionSchedulingUserRoles>0){
			if(!ConvertUtil.isEmpty(productionScheduling.getWfId())&&productionScheduling.getStatus()>=2){
				productionSchedulingUserRoles = 1;
			}else{
				productionSchedulingUserRoles = 0;
			}
		}
		
		model.addAttribute("productionSchedulingUserRoles", productionSchedulingUserRoles);
		
		
		
		return "/factory/factory/edit";
	}


	/*
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg updateProductionScheduling(ProductionScheduling productionScheduling,
			Long billTypeId,Long billCategoryId,Long storeId,Long saleOrgId,Long organizationId,
			Long productionPlantId,Long warehouseId,Long sbuId,Integer productionSchedulingStatus) {
		
		//单据类型
		SystemDict billType = systemDictBaseService.find(billTypeId);
		productionScheduling.setBillType(billType);
		//单据类别
		SystemDict billCategory = systemDictBaseService.find(billCategoryId);
		productionScheduling.setBillCategory(billCategory);
		//客户
		Store store = storeService.find(storeId);
		productionScheduling.setStore(store);
		//机构
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		productionScheduling.setSaleOrg(saleOrg);
		//经营组织
		Organization organization = organizationService.find(organizationId);
		productionScheduling.setOrganization(organization);
		//生产工厂
		SystemDict productionPlant = systemDictBaseService.find(productionPlantId);
		productionScheduling.setProductionPlant(productionPlant);
		//SBU
		Sbu sbu = sbuService.find(sbuId);
		productionScheduling.setSbu(sbu);
		//仓库
		Warehouse warehouse = warehouseService.find(warehouseId);
		productionScheduling.setWarehouse(warehouse);
		
		productionSchedulingService.updateProductionScheduling(productionScheduling,productionSchedulingStatus);
		
		return success().addObjX(productionScheduling.getId());
	}
	
	
	/*
	 * 审核
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, Long objConfId) {
		if (id == null) {
			// 请选择订单
			return error("请选择生产单");
		}
		// 附件
		productionSchedulingService.checkProductionSchedulingWf(id,objConfId);

		return success();
	}
	
	/**
	 * 查看pdf
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkPdf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checkPdf(Long ids) throws Exception {
		ProductionScheduling productionScheduling = productionSchedulingService.find(ids);
		if (productionScheduling == null){
			 return error("生产单不存在！");
		}
		TriplicateForm triplicateForm = null;
		if (triplicateForm == null) {
			triplicateForm = productionSchedulingService.createProductionSchedulingTriplicateForm(productionScheduling);
		}
		return success(triplicateForm.getUrl());
	}
	
	
	/*
	 * 查询生产单
	 */
	@RequestMapping(value = "/selectProductionSchedulingPage", method = RequestMethod.GET)
	public String selectProductPage(Integer multi,Long warehouseId,Long saleOrgId,Long billTypeId,
			Long sbuId,Long storeId,Long billCategoryId,ModelMap model) {
		
		model.addAttribute("multi", multi);
		model.addAttribute("warehouseId",warehouseId );
		model.addAttribute("saleOrgId",saleOrgId );
		model.addAttribute("billTypeId",billTypeId );
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("storeId",storeId );
		model.addAttribute("billCategoryId",billCategoryId );
		
		return "/factory/factory/select_productionScheduling_page";
	}


	/**
	 * 查询生产单列表
	 * @param productionSchedulingSn
	 * @param vonderCode
	 * @param productName
	 * @param model
	 * @param pageable
	 * @param warehouseId
	 * @param saleOrgId
	 * @param billTypeId
	 * @param sbuId
	 * @param storeId
	 * @param billCategoryId
	 * @return
	 */
	@RequestMapping(value = "/selectProductionSchedulingList", method = RequestMethod.POST)
	public @ResponseBody ResultMsg selectProductList(String productionSchedulingSn,
			String vonderCode, String productName,String model,Pageable pageable,
			Long warehouseId,Long saleOrgId,Long billTypeId,Long sbuId,Long storeId,
			Long billCategoryId) {
		
		Page<Map<String, Object>> page = productionSchedulingService.findProductionSchedulingItem(productionSchedulingSn, vonderCode,
				productName, model, pageable, warehouseId, saleOrgId, billTypeId, sbuId, storeId, billCategoryId);
		
		String jsonPage = JsonUtils.toJson(page);
		
		return success(jsonPage);
	}
	
	
	/**
	 * 生产单作废功能
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg cancel(Long id) {

		ProductionScheduling productionScheduling = productionSchedulingService.find(id);

		if (productionScheduling == null){
			 return error("生产单不存在！");
		}
		if (productionScheduling.getStatus() == 4) {
			return error("单据已作废，请勿重复操作！");
		}
		productionSchedulingService.cancelProductionScheduling(productionScheduling);
		
		return success();
	}
	
	
	
	
}
