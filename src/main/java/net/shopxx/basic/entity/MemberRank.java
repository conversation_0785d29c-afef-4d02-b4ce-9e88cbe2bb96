package net.shopxx.basic.entity;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.PrePersist;
import javax.persistence.PreRemove;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotNull;

import net.shopxx.base.core.entity.BaseEntity;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * Entity - 会员等级
 */
@Entity
@Table(name = "xx_member_rank", uniqueConstraints = { @UniqueConstraint(columnNames = { "name",
		"companyInfoId" }) })
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_member_rank_sequence")
public class MemberRank extends BaseEntity {

	private static final long serialVersionUID = 3599029355500655209L;

	/** 名称 */
	private String name;

	/** 是否默认 */
	private Boolean isDefault;
	
	/** 是否启用 */
	private Boolean isEnabled;

	/**级别*/
	private Integer grade;

	/** 唯一标识 UUID */
	private String uniqueIdentify;
	
	/** 机构  */
	private SaleOrg saleOrg;

	/** 价格类型机构中间表 */
	List<MemberRankSaleOrg> memberRankSaleOrg = new ArrayList<MemberRankSaleOrg>();

    /**sbu*/
	private Sbu sbu;

	/** 价格类型SBU中间表 */
	List<MemberRankSbu> memberRankSbu = new ArrayList<MemberRankSbu>();
	
	/** 类型区分*/
	private SystemDict rankType;
	

	/**
	 * 获取名称
	 * 
	 * @return 名称
	 */
	@NotEmpty
	@Length(max = 100)
	@Column(nullable = false, /* unique = true, */length = 100)
	public String getName() {
		return name;
	}

	/**
	 * 设置名称
	 * 
	 * @param name
	 *            名称
	 */
	public void setName(String name) {
		this.name = name;
	}

//	/**
//	 * 获取优惠比例
//	 * 
//	 * @return 优惠比例
//	 */
//	@NotNull
//	@Min(0)
//	@Digits(integer = 3, fraction = 3)
//	@Column(nullable = false, precision = 12, scale = 6)
//	public Double getScale() {
//		return scale;
//	}
//
//	/**
//	 * 设置优惠比例
//	 * 
//	 * @param scale
//	 *            优惠比例
//	 */
//	public void setScale(Double scale) {
//		this.scale = scale;
//	}
//
//	/**
//	 * 获取消费金额
//	 * 
//	 * @return 消费金额
//	 */
//	@Min(0)
//	@Digits(integer = 12, fraction = 3)
//	@Column(/* unique = true, */precision = 21, scale = 6)
//	public BigDecimal getAmount() {
//		return amount;
//	}
//
//	/**
//	 * 设置消费金额
//	 * 
//	 * @param amount
//	 *            消费金额
//	 */
//	public void setAmount(BigDecimal amount) {
//		this.amount = amount;
//	}

	/**
	 * 获取是否默认
	 * 
	 * @return 是否默认
	 */
	@NotNull
	@Column(nullable = false)
	public Boolean getIsDefault() {
		return isDefault;
	}

	/**
	 * 设置是否默认
	 * 
	 * @param isDefault
	 *            是否默认
	 */
	public void setIsDefault(Boolean isDefault) {
		this.isDefault = isDefault;
	}

//	/**
//	 * 获取是否特殊
//	 * 
//	 * @return 是否特殊
//	 */
//	@NotNull
//	@Column(nullable = false)
//	public Boolean getIsSpecial() {
//		return isSpecial;
//	}
//
//	/**
//	 * 设置是否特殊
//	 * 
//	 * @param isSpecial
//	 *            是否特殊
//	 */
//	public void setIsSpecial(Boolean isSpecial) {
//		this.isSpecial = isSpecial;
//	}

//	/**
//	 * 获取会员
//	 * 
//	 * @return 会员
//	 */
//	@OneToMany(mappedBy = "memberRank", fetch = FetchType.LAZY)
//	public Set<Member> getMembers() {
//		return members;
//	}
//
//	/**
//	 * 设置会员
//	 * 
//	 * @param members
//	 *            会员
//	 */
//	public void setMembers(Set<Member> members) {
//		this.members = members;
//	}

	/**获取级别*/
	public Integer getGrade() {
		return grade;
	}

	/**设置级别*/
	public void setGrade(Integer grade) {
		this.grade = grade;
	}

	/**
	 * 获取 唯一标识 UUID
	 * @return uniqueIdentify
	 */
	public String getUniqueIdentify() {
		return uniqueIdentify;
	}

	/**
	 * 设置 唯一标识 UUID
	 * @param uniqueIdentify 唯一标识 UUID
	 */
	public void setUniqueIdentify(String uniqueIdentify) {
		this.uniqueIdentify = uniqueIdentify;
	}

	public Boolean getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}

	/**
	 * 持久化前
	 */
	@PrePersist
	public void prePersist() {
		if (getUniqueIdentify() == null) {
			setUniqueIdentify(UUID.randomUUID().toString());
		}
	}

	/**
	 * 删除前处理
	 */
	@PreRemove
	public void preRemove() {

	}
	
    /** 获取机构 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}
	
    /** 设置机构 */
	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}
	
	@OneToMany(mappedBy="memberRank",fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<MemberRankSaleOrg> getMemberRankSaleOrg() {
		return memberRankSaleOrg;
	}

	public void setMemberRankSaleOrg(List<MemberRankSaleOrg> memberRankSaleOrg) {
		this.memberRankSaleOrg = memberRankSaleOrg;
	}

	/**sbu */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}

	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}

	@OneToMany(mappedBy="memberRank",fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<MemberRankSbu> getMemberRankSbu() {
		return memberRankSbu;
	}

	public void setMemberRankSbu(List<MemberRankSbu> memberRankSbu) {
		this.memberRankSbu = memberRankSbu;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getRankType() {
		return rankType;
	}


	public void setRankType(SystemDict rankType) {
		this.rankType = rankType;
	}
}