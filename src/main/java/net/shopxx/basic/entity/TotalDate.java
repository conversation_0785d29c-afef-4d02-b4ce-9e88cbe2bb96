package net.shopxx.basic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;
import java.util.Date;
/**
 * Entity - 总账日期
 */
@Entity
@Table(name = "xx_total_date")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_total_date_sequence")
public class TotalDate extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**总账单编号*/
	private String sn;
	
	/** 是否启用 */
	private Boolean isEnabled;
	
	/** 机构 */
	private SaleOrg saleOrg;
	
	/** SBU */
	private Sbu sbu;
	
	/** 组织 */
	private Organization organization;
	
	/** 开始日期*/
	private Date startDate;
	
	/** 结束日期*/
	private Date endDate;
	
	/** 有效开始日期*/
	private Date effectiveStartDate;
	
	/** 有效结束日期*/
	private Date effectiveEndDate;
	
	/** 备注*/
	private String remark;
	
	///类型
	private SystemDict totalDateType;
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}
	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}
	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
	public String getSn() {
		return sn;
	}
	public Boolean getIsEnabled() {
		return isEnabled;
	}
	public Date getStartDate() {
		return startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Date getEffectiveStartDate() {
		return effectiveStartDate;
	}
	public void setEffectiveStartDate(Date effectiveStartDate) {
		this.effectiveStartDate = effectiveStartDate;
	}
	public Date getEffectiveEndDate() {
		return effectiveEndDate;
	}
	public void setEffectiveEndDate(Date effectiveEndDate) {
		this.effectiveEndDate = effectiveEndDate;
	}
   @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getTotalDateType() {
        return totalDateType;
    }

    public void setTotalDateType(SystemDict totalDateType) {
        this.totalDateType = totalDateType;
    }
}

