package net.shopxx.basic.entity;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.CreditRecharge;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.SaleOrgCredit;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.product.entity.Product;
import net.shopxx.stock.entity.Warehouse;

/**
 * SBU
 */
@Entity
@Table(name = "xx_sbu")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_sbu_sequence")
public class Sbu extends BaseEntity {

	
	

	private static final long serialVersionUID = 453475675665845634L;

	/** SBU价格类型 */
	private MemberRank memberRank;

	/**  状态:生效true和失效false */
	private Boolean status;

	/**  名称 */
	private String name;

	/**  是否默认 */
	private Boolean isDefault;

	/**  用户 */
	private StoreMember storeMembers;
	
	/** 发运方式 */
	private List<SbuItems> shippingMethodSbuList = new ArrayList<SbuItems>();
	
	/** 角色*/
	private List<MenuRoleSbu> menuRoleSbus = new ArrayList<MenuRoleSbu>();
	
	/** 政策类型*/
	private List<SbuPolicy> sbuPolicys = new ArrayList<SbuPolicy>();
	
	
	/** 发票类型*/
	private List<SbuInvoice> sbuInvoices = new ArrayList<SbuInvoice>();
	
	
	/** 发票类型*/
	private List<SbuCase> sbuCases = new ArrayList<SbuCase>();
	
	
	
	/**  产品 */
	private Product product;

	/**  仓库 */
	private Warehouse wareHouse;

	/**  客户授信 */
	private CreditRecharge creditRecharge;

	/**  平台授信 */
	private SaleOrgCredit saleOrgCredit;
	
	/** 编码前缀  */
	private String codePre;
	
	/** URL标识 */
	private String urlId;
	
	/** 预留1 */
	private String undefined1;
	
	/** 预留2 */
	private String undefined2;
	
	/** 预留3 */
	private String undefined3;
	
	/** 预留4 */
	private Organization undefined4;
	
	/** 预留5 */
	private String undefined5;
	
	/** 预留6 */
	private String undefined6;
	
	/** 预留7 */
	private String undefined7;
	
	/** 预留8 */
	private String undefined8;
	
	/** 预留9 */
	private String undefined9;
	
	/** 预留10 */
	private String undefined10;
	

	@ManyToOne(fetch = FetchType.LAZY)
	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMembers() {
		return storeMembers;
	}

	public void setStoreMembers(StoreMember storeMembers) {
		this.storeMembers = storeMembers;
	}

	public Boolean getStatus() {
		return status;
	}

	public void setStatus(Boolean status) {
		this.status = status;
	}

	public Boolean getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Boolean isDefault) {
		this.isDefault = isDefault;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public CreditRecharge getCreditRecharge() {
		return creditRecharge;
	}

	public void setCreditRecharge(CreditRecharge creditRecharge) {
		this.creditRecharge = creditRecharge;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrgCredit getSaleOrgCredit() {
		return saleOrgCredit;
	}

	public void setSaleOrgCredit(SaleOrgCredit saleOrgCredit) {
		this.saleOrgCredit = saleOrgCredit;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Warehouse getWareHouse() {
		return wareHouse;
	}

	public void setWareHouse(Warehouse wareHouse) {
		this.wareHouse = wareHouse;
	}

	@OneToMany(mappedBy = "sbu", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<SbuItems> getShippingMethodSbuList() {
		return shippingMethodSbuList;
	}

	public void setShippingMethodSbuList(List<SbuItems> shippingMethodSbuList) {
		this.shippingMethodSbuList = shippingMethodSbuList;
	}

	public String getCodePre() {
		return codePre;
	}

	public void setCodePre(String codePre) {
		this.codePre = codePre;
	}

	public String getUrlId() {
		return urlId;
	}

	public void setUrlId(String urlId) {
		this.urlId = urlId;
	}

	public String getUndefined1() {
		return undefined1;
	}

	public void setUndefined1(String undefined1) {
		this.undefined1 = undefined1;
	}

	public String getUndefined2() {
		return undefined2;
	}

	public void setUndefined2(String undefined2) {
		this.undefined2 = undefined2;
	}

	public String getUndefined3() {
		return undefined3;
	}

	public void setUndefined3(String undefined3) {
		this.undefined3 = undefined3;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getUndefined4() {
		return undefined4;
	}

	public void setUndefined4(Organization undefined4) {
		this.undefined4 = undefined4;
	}
	
	public String getUndefined5() {
		return undefined5;
	}

	public void setUndefined5(String undefined5) {
		this.undefined5 = undefined5;
	}

	public String getUndefined6() {
		return undefined6;
	}

	public void setUndefined6(String undefined6) {
		this.undefined6 = undefined6;
	}

	public String getUndefined7() {
		return undefined7;
	}

	public void setUndefined7(String undefined7) {
		this.undefined7 = undefined7;
	}

	public String getUndefined8() {
		return undefined8;
	}

	public void setUndefined8(String undefined8) {
		this.undefined8 = undefined8;
	}

	public String getUndefined9() {
		return undefined9;
	}

	public void setUndefined9(String undefined9) {
		this.undefined9 = undefined9;
	}

	public String getUndefined10() {
		return undefined10;
	}

	public void setUndefined10(String undefined10) {
		this.undefined10 = undefined10;
	}


	@OneToMany(mappedBy = "sbu", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<MenuRoleSbu> getMenuRoleSbus() {
		return menuRoleSbus;
	}

	public void setMenuRoleSbus(List<MenuRoleSbu> menuRoleSbus) {
		this.menuRoleSbus = menuRoleSbus;
	}

	@OneToMany(mappedBy = "sbu", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<SbuPolicy> getSbuPolicys() {
		return sbuPolicys;
	}

	public void setSbuPolicys(List<SbuPolicy> sbuPolicys) {
		this.sbuPolicys = sbuPolicys;
	}
	
	@OneToMany(mappedBy = "sbu", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<SbuCase> getSbuCases() {
		return sbuCases;
	}

	public void setSbuCases(List<SbuCase> sbuCases) {
		this.sbuCases = sbuCases;
	}

	 
	@OneToMany(mappedBy = "sbu", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<SbuInvoice> getSbuInvoices() {
		return sbuInvoices;
	}

	public void setSbuInvoices(List<SbuInvoice> sbuInvoices) {
		this.sbuInvoices = sbuInvoices;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public MemberRank getMemberRank() {
		return memberRank;
	}

	public void setMemberRank(MemberRank memberRank) {
		this.memberRank = memberRank;
	}
	
}
