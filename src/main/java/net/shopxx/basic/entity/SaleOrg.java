package net.shopxx.basic.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import net.shopxx.base.core.entity.BaseEntity;

import org.apache.commons.lang.StringUtils;

/**
 * Entity - 机构
 */
@Entity
@Table(name = "xx_sale_org")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_sale_org_sequence")
public class SaleOrg extends BaseEntity {

	private static final long serialVersionUID = 6719859631792431453L;

	/** 树路径分隔符 */
	public static final String ID_PATH_SEPARATOR = ",";

	/** 是否启用 */
	private Boolean isEnabled;

	/** 机构编码 */
	private String sn;

	/** 名称 */
	private String name;

	/** 上一级 */
	private SaleOrg parent;

	/** 层级 */
	private Integer grade;

	/** Id路径 */
	private String treePath;

	/** 机构类型 */
	private Long type;

	/** 是否为顶级组织 */
	private Boolean isTop;

	/** 唯一标识 UUID */
	private String uniqueIdentify;

	/** 是否叶子端 */
	private Boolean isLeaf;

	/** 费用预算 */
	private BigDecimal budget;

	/** 冻结费用预算 */
	private BigDecimal lockBudget;

	
	/** 机构价格类型 */
	private MemberRank memberRank;

	/** 是否销售机构 */
	private Boolean isSellSaleOrg;

	/** 子级 */
	private Set<SaleOrg> children = new HashSet<SaleOrg>();
	
	/**负责人*/
	private String functionary;
	
	/**类型*/
	private Boolean factoryType;
	
	/** 区域*/
	private Integer region;

	/**
	 * 获取 isEnabled
	 * 
	 * @return isEnabled
	 */
	public Boolean getIsEnabled() {
		return isEnabled;
	}

	/**
	 * 设置 isEnabled
	 * 
	 * @param isEnabled
	 */
	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	/**
	 * 获取 parent
	 * 
	 * @return parent
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getParent() {
		return parent;
	}

	/**
	 * 设置 parent
	 * 
	 * @param parent
	 */
	public void setParent(SaleOrg parent) {
		this.parent = parent;
	}

	/**
	 * 获取 children
	 * 
	 * @return children
	 */
	@OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
	public Set<SaleOrg> getChildren() {
		return children;
	}

	/**
	 * 设置 children
	 * 
	 * @param children
	 */
	public void setChildren(Set<SaleOrg> children) {
		this.children = children;
	}

	/**
	 * 获取 name
	 * 
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * 设置 name
	 * 
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 获取 grade
	 * 
	 * @return the grade
	 */
	public Integer getGrade() {
		return grade;
	}

	/**
	 * 设置 grade
	 * 
	 * @param grade
	 *            the grade to set
	 */
	public void setGrade(Integer grade) {
		this.grade = grade;
	}

	/**
	 * 获取 treePath
	 * 
	 * @return the treePath
	 */
	public String getTreePath() {
		return treePath;
	}

	/**
	 * 设置 treePath
	 * 
	 * @param treePath
	 *            the treePath to set
	 */
	public void setTreePath(String treePath) {
		this.treePath = treePath;
	}

	/**
	 * 获取 type
	 * 
	 * @return the type
	 */
	public Long getType() {
		return type;
	}

	/**
	 * 设置 type
	 * 
	 * @param type
	 *            the type to set
	 */
	public void setType(Long type) {
		this.type = type;
	}

	/**
	 * 持久化前处理
	 */
	@PrePersist
	public void prePersist() {
		SaleOrg parent = getParent();
		if (parent != null) {
			setTreePath(parent.getTreePath() + parent.getId() + ID_PATH_SEPARATOR);
		} else {
			setTreePath(ID_PATH_SEPARATOR);
		}
		setGrade(getIdPaths().size());
		if (getUniqueIdentify() == null) {
			setUniqueIdentify(UUID.randomUUID().toString());
		}
		if (getBudget() == null) {
			setBudget(BigDecimal.ZERO);
		}
		if (getLockBudget() == null) {
			setLockBudget(BigDecimal.ZERO);
		}
	}

	/**
	 * 更新前处理
	 */
	@PreUpdate
	public void preUpdate() {
		SaleOrg parent = getParent();
		if (parent != null) {
			setTreePath(parent.getTreePath() + parent.getId() + ID_PATH_SEPARATOR);
		} else {
			setTreePath(ID_PATH_SEPARATOR);
		}
		setGrade(getIdPaths().size());
	}

	/**
	 * 获取树路径
	 * 
	 * @return 树路径
	 */
	@Transient
	public List<Long> getIdPaths() {
		List<Long> treePaths = new ArrayList<Long>();
		String[] ids = StringUtils.split(getTreePath(), ID_PATH_SEPARATOR);
		if (ids != null) {
			for (String id : ids) {
				treePaths.add(Long.valueOf(id));
			}
		}
		return treePaths;
	}

	/**
	 * 获取是否为顶级组织
	 * 
	 * @date 2016年10月18日 下午4:47:24
	 * @return
	 */
	public Boolean getIsTop() {
		return isTop;
	}

	/**
	 * 设置是否为顶级组织
	 * 
	 * @date 2016年10月18日 下午4:47:21
	 * @param isTop
	 */
	public void setIsTop(Boolean isTop) {
		this.isTop = isTop;
	}

	/**
	 * 获取 唯一标识 UUID
	 * 
	 * @date 2017年8月18日
	 * @return uniqueIdentify
	 */
	@Column(updatable = false)
	public String getUniqueIdentify() {
		return uniqueIdentify;
	}

	/**
	 * 设置 唯一标识 UUID
	 * 
	 * @date 2017年8月18日
	 * @param uniqueIdentify
	 *            唯一标识 UUID
	 */
	public void setUniqueIdentify(String uniqueIdentify) {
		this.uniqueIdentify = uniqueIdentify;
	}

	/**
	 * 获取 是否叶子端
	 * 
	 * @return isLeaf
	 */
	public Boolean getIsLeaf() {
		return isLeaf;
	}

	/**
	 * 设置 是否叶子端
	 * 
	 * @param isLeaf
	 *            是否叶子端
	 */
	public void setIsLeaf(Boolean isLeaf) {
		this.isLeaf = isLeaf;
	}

	/**
	 * 获取 费用预算
	 * 
	 * @return budget
	 */
	public BigDecimal getBudget() {
		return budget;
	}

	/**
	 * 设置 费用预算
	 * 
	 * @param budget
	 *            费用预算
	 */
	public void setBudget(BigDecimal budget) {
		this.budget = budget;
	}

	/**
	 * 获取 冻结费用预算
	 * 
	 * @return lockBudget
	 */
	public BigDecimal getLockBudget() {
		return lockBudget;
	}

	/**
	 * 设置 冻结费用预算
	 * 
	 * @param lockBudget
	 *            冻结费用预算
	 */
	public void setLockBudget(BigDecimal lockBudget) {
		this.lockBudget = lockBudget;
	}

	public Boolean getIsSellSaleOrg() {
		return isSellSaleOrg;
	}

	public void setIsSellSaleOrg(Boolean isSellSaleOrg) {
		this.isSellSaleOrg = isSellSaleOrg;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public MemberRank getMemberRank() {
		return memberRank;
	}

	public void setMemberRank(MemberRank memberRank) {
		this.memberRank = memberRank;
	}

	public String getFunctionary() {
		return functionary;
	}

	public void setFunctionary(String functionary) {
		this.functionary = functionary;
	}

	public Boolean getFactoryType() {
		return factoryType;
	}

	public void setFactoryType(Boolean factoryType) {
		this.factoryType = factoryType;
	}

	public Integer getRegion() {
		return region;
	}

	public void setRegion(Integer region) {
		this.region = region;
	}
	
	
}