package net.shopxx.basic.entity;

import javax.persistence.Entity;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.base.core.entity.BaseEntity;

/**
 * Entity - 机构
 */
@Entity
@Table(name = "xx_organization")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_organization_sequence")
public class Organization extends BaseEntity {

	private static final long serialVersionUID = 1L;
	
	/**类型（0:经营组织，1:仓库组织）*/
	private Integer type;
	
	/**编号*/
	private String code;
	
	/**名称*/
	private String name;
	
	/** 是否启用 */
	private Boolean isEnabled;
	
	/** 是否平台 */
	private Boolean platformOrNot;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Boolean getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Boolean getPlatformOrNot() {
		return platformOrNot;
	}

	public void setPlatformOrNot(Boolean platformOrNot) {
		this.platformOrNot = platformOrNot;
	}
	
	
	
}
