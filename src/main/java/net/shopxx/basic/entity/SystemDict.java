package net.shopxx.basic.entity;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.PrePersist;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import net.shopxx.base.core.entity.OrderEntity;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * Entity - 系统词汇
 */
@Entity
@Table(name = "xx_system_dict", uniqueConstraints = { @UniqueConstraint(columnNames = { "code",
		"value",
		"companyInfoId" }) })
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_system_dict_sequence")
public class SystemDict extends OrderEntity {

	private static final long serialVersionUID = 3078314757304547486L;

	/** 词汇编码 */
	private String code;

	/** 词汇值 */
	private String value;
	
	/** 词汇下级编码 */
	private String lowerCode;

	/** 备注 */
	private String remark;

	/** 是否启用 */
	private Boolean isEnabled;

	/** 上级词汇 */
	private SystemDict parent;

	/** 下级词汇 */
	private Set<SystemDict> children = new HashSet<SystemDict>();

	/** 标志 */
	private Integer flag;

	/** 唯一标识 UUID */
	private String uniqueIdentify;

    /** 排序 */
    private Integer sort;

    /**
     * 列表默认过滤
     */
    private Boolean defaultFiltering;

	/**获取值*/
	@Column(nullable = false)
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	/**获取备注*/
	public String getRemark() {
		return remark;
	}

	/**设置备注*/
	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(nullable = false)
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Boolean getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}
	
	public String getLowerCode() {
		return lowerCode;
	}

	public void setLowerCode(String lowerCode) {
		this.lowerCode = lowerCode;
	}

	/**
	 * 获取 上级词汇
	 * @return parent
	 */
	@ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
	public SystemDict getParent() {
		return parent;
	}

	/**
	 * 设置 上级词汇
	 * @param parent 上级词汇
	 */
	public void setParent(SystemDict parent) {
		this.parent = parent;
	}

	/**
	 * 获取 下级词汇
	 * @return children
	 */
	@JsonIgnore
	@OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
	@OrderBy("order asc")
	public Set<SystemDict> getChildren() {
		return children;
	}

	/**
	 * 设置 下级词汇
	 * @param children 下级词汇
	 */
	public void setChildren(Set<SystemDict> children) {
		this.children = children;
	}

	/**
	 * 获取 标志
	 * @return flag
	 */
	public Integer getFlag() {
		return flag;
	}

	/**
	 * 设置 标志
	 * @param flag 标志
	 */
	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	/**
	 * 获取 唯一标识 UUID
	 * @return uniqueIdentify
	 */
	public String getUniqueIdentify() {
		return uniqueIdentify;
	}

	/**
	 * 设置 唯一标识 UUID
	 * @param uniqueIdentify 唯一标识 UUID
	 */
	public void setUniqueIdentify(String uniqueIdentify) {
		this.uniqueIdentify = uniqueIdentify;
	}

    /**
     * 获取 排序
     * @return sort
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * 设置 排序
     * @param sort 排序
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
	 * 持久化前
	 */
	@PrePersist
	public void prePersist() {
		if (getUniqueIdentify() == null) {
			setUniqueIdentify(UUID.randomUUID().toString());
		}
	}

    public Boolean getDefaultFiltering() {
        return defaultFiltering;
    }

    public void setDefaultFiltering(Boolean defaultFiltering) {
        this.defaultFiltering = defaultFiltering;
    }
}
