package net.shopxx.basic.entity;

import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;
import java.util.UUID;

@Entity
@Table(name = "xx_system_parameter", uniqueConstraints = {@UniqueConstraint(columnNames = {"code", "companyInfoId"})})
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_system_parameter_sequence")
public class SystemParameter extends BaseEntity {
  private static final long serialVersionUID = 914140705533030444L;
  
  private String name;
  
  private String value;
  
  private String remark;
  
  private String code;
  
  private Boolean isModify;
  
  private String uniqueIdentify;
  
  @Column(nullable = false)
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  @Column(nullable = false)
  public String getValue() {
    return this.value;
  }
  
  public void setValue(String value) {
    this.value = value;
  }
  
  public String getRemark() {
    return this.remark;
  }
  
  public void setRemark(String remark) {
    this.remark = remark;
  }
  
  public String getCode() {
    return this.code;
  }
  
  public void setCode(String code) {
    this.code = code;
  }
  
  public Boolean getIsModify() {
    return this.isModify;
  }
  
  public void setIsModify(Boolean isModify) {
    this.isModify = isModify;
  }
  
  public String getUniqueIdentify() {
    return this.uniqueIdentify;
  }
  
  public void setUniqueIdentify(String uniqueIdentify) {
    this.uniqueIdentify = uniqueIdentify;
  }
  
  @PrePersist
  public void prePersist() {
    if (getUniqueIdentify() == null)
      setUniqueIdentify(UUID.randomUUID().toString()); 
  }
}
