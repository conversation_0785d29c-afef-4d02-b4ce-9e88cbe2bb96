package net.shopxx.basic.entity;

/**
 * @ClassName : MemberRankSbu
 * @Deseription : TODO
 * <AUTHOR> LanTian<PERSON>ong
 * @Date : 2020/10/14 9:50
 * @Version : 1.0
 **/

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;

/**
 * 价格类型机构中间表
 *
 * */

@Entity
@Table(name = "xx_member_rank_sbu")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_member_rank_sbu_sequence")
public class MemberRankSbu extends BaseEntity {


    /**价格类型*/
    private MemberRank memberRank;

    /** SBU */
    private Sbu sbu;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(nullable = false)
    public MemberRank getMemberRank() {
        return memberRank;
    }

    public void setMemberRank(MemberRank memberRank) {
        this.memberRank = memberRank;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public Sbu getSbu() {
        return sbu;
    }

    public void setSbu(Sbu sbu) {
        this.sbu = sbu;
    }
}
