package net.shopxx.basic.util;

import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.basic.service.SystemParameterBaseService;

public class SystemConfig {
	//通过aop,交给redis缓存
	public static String getConfig(String key, Long companyid) { 
		String result = selSysConfig(key, companyid);
		return result;
	}

	public static void updateMap(String key, String svalue, Long companyid) {}
	
//	private static Map<Long, HashMap<String, String>> map = new HashMap<Long, HashMap<String, String>>();
//	  
//	  private static Object lock = new Object();
//	  
//	  public static String getConfig(String key, Long companyid) {
//	    if (key == null || 
//	      key.trim().equals("") || 
//	      companyid == null || 
//	      companyid.longValue() == 0L)
//	      throw new RuntimeException("); 
//	    HashMap<String, String> value = map.get(companyid);
//	    if (value == null)
//	      synchronized (lock) {
//	        value = map.get(companyid);
//	        if (value == null) {
//	          value = new HashMap<String, String>();
//	          map.put(companyid, value);
//	        } 
//	      }  
//	    String result = value.get(key);
//	    if (result == null)
//	      synchronized (lock) {
//	        result = value.get(key);
//	        if (result == null) {
//	          result = selSysConfig(key, companyid);
//	          if (result == null)
//	            throw new RuntimeException("+ 
//	                key + 
//	                "]); 
//	          value.put(key, result);
//	        } 
//	      }  
//	    return result;
//	  }
//	  
//	  public static void updateMap(String key, String svalue, Long companyid) {
//	    if (key == null || 
//	      key.trim().equals("") || 
//	      companyid == null || 
//	      companyid.longValue() == 0L)
//	      throw new RuntimeException("); 
//	    HashMap<String, String> value = map.get(companyid);
//	    if (value == null)
//	      synchronized (lock) {
//	        value = map.get(companyid);
//	        if (value == null) {
//	          value = new HashMap<String, String>();
//	          map.put(companyid, value);
//	        } 
//	      }  
//	    synchronized (lock) {
//	      value.put(key, svalue);
//	    } 
//	  }
	  
	  private static String selSysConfig(String key, Long companyid) {
	    SystemParameterBaseService systemParameterService = (SystemParameterBaseService)SpringUtils.getBean("systemParameterBaseServiceImpl", 
	        SystemParameterBaseService.class);
	    return systemParameterService.getValue(companyid, key);
	  }
}
