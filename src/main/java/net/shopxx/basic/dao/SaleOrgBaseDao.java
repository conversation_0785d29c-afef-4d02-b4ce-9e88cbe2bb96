package net.shopxx.basic.dao;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
/**
 * Dao - 组织
 */
@Repository("saleOrgBaseDao")
public class SaleOrgBaseDao extends DaoCenter {

	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;

	/**
	 * 查找下级组织
	 * 
	 * @param name
	 * @param isEnabled
	 * @param saleOrg
	 * @param count
	 * @return
	 */
	public List<SaleOrg> findChildren(String name, Boolean isEnabled,
			SaleOrg saleOrg, int count) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		String sql = "select * from xx_sale_org where 1=1";
		if (saleOrg != null) {
			sql += " and tree_path like ?";
			list.add("%"
					+ SaleOrg.ID_PATH_SEPARATOR
					+ saleOrg.getId()
					+ SaleOrg.ID_PATH_SEPARATOR
					+ "%");
		}
		if (companyInfoId != null) {
			sql += " and company_info_id = ?";
			list.add(companyInfoId);
		}
		else {
			sql += " and company_info_id is null";
		}
		if (name != null) {
			sql += " and name like ?";
			list.add("%" + name + "%");
		}
		if (isEnabled != null) {
			if (isEnabled)
				sql += " and is_enabled = 1";
			else
				sql += " and is_enabled = 0";
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<SaleOrg> saleOrgs = getNativeDao().findListManaged(sql,
				objs,
				count,
				SaleOrg.class);
		return sort(saleOrgs, saleOrg);
	}

	/**
	 * 排序组织
	 */
	private List<SaleOrg> sort(List<SaleOrg> saleOrgs, SaleOrg parent) {
		List<SaleOrg> result = new ArrayList<SaleOrg>();
		if (saleOrgs != null) {
			for (SaleOrg saleOrg : saleOrgs) {
				if ((saleOrg.getParent() != null
						&& saleOrg.getParent().equals(parent))
						|| (saleOrg.getParent() == null && parent == null)) {
					result.add(saleOrg);
					result.addAll(sort(saleOrgs, saleOrg));
				}
			}
		}
		return result;
	}

	public List<Map<String, Object>> findList(String saleOrgName,
			String saleOrgSn, Integer isSellSaleOrg, Boolean isEnabled,
			Integer isSelect,Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		if (ConvertUtil.isEmpty(saleOrgName)
				&& ConvertUtil.isEmpty(saleOrgSn)
				&& isSellSaleOrg == null
				&& isSelect == null) {// isSellSaleOrg选机构直接不分层级查         isSelect(弹框)选机构直接不分层级查
			sql.append(
					"select s.*,sd.value,sd.id typeId, 0 as is_search from xx_sale_org s left join xx_system_dict sd on s.type = sd.id and sd.parent is not null where s.parent is null");
		}
		else {
			sql.append(
					"SELECT s.*,sd.value,sd.id typeId, 1 as is_search from xx_sale_org s left join xx_system_dict sd on s.type = sd.id and sd.parent is not null WHERE 1=1");
		}
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id=?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(saleOrgName)) {
			sql.append(" and s.name like ?");
			list.add("%" + saleOrgName + "%");
		}
		if (!ConvertUtil.isEmpty(saleOrgSn)) {
			sql.append(" and s.sn like ?");
			list.add("%" + saleOrgSn + "%");
		}
		if (isSellSaleOrg != null && isSellSaleOrg == 1) {
			sql.append(" and s.is_sell_sale_org = 1 ");
		}
		if (isSelect != null && isSellSaleOrg == null) {
			sql.append(" and (s.id in ");
			sql.append(
					" (select so.id from xx_store_member_sale_org smo, xx_sale_org so");
			sql.append(" where smo.sale_org = so.id and smo.store_member = ?)");
			sql.append(
					" or s.id in (select  a.id from xx_sale_org a,xx_sale_org b");
			sql.append(" where a.tree_path like concat('%,', b.id, ',%')");
			sql.append(
					" and b.id in (select so.id from xx_store_member_sale_org smo,xx_sale_org so");
			sql.append(
					" where smo.sale_org = so.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());

		}
		if (isEnabled != null) {
			if (isEnabled)
				sql.append(" and s.is_enabled = 1");
			else
				sql.append(" and s.is_enabled = 0");
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> saleOrgs = getNativeDao()
				.findListMap(sql.toString(), objs, 0);
		String cSql = "";
		for (Map<String, Object> saleOrg : saleOrgs) {
			String tree_path_name = "";
			String tree_path = saleOrg.get("tree_path").toString();
			if (tree_path.length() > 1) {
				tree_path = tree_path.substring(1);
				tree_path = tree_path.substring(0, tree_path.length() - 1);
				cSql = "select group_concat(name separator ' / ') tree_path_name from xx_sale_org where id in ("
						+ "SELECT id from xx_sale_org so WHERE so.id in("
						+ tree_path
						+ ") ORDER BY so.grade asc"
						+ ")";
				tree_path_name = getNativeDao().findString(cSql, null);
			}
			saleOrg.put("tree_path_name", tree_path_name);
		}

		return saleOrgs;

	}

	public List<Map<String, Object>> findChildren(Long parentId) {

		String sql = "select p.*,sd.value, 0 as is_search from xx_sale_org p left join xx_system_dict sd on p.type = sd.id and sd.parent is not null where p.parent = ?";
		List<Map<String, Object>> productCategories = getNativeDao()
				.findListMap(sql, new Object[] { parentId }, 0);

		return productCategories;
	}

	public SaleOrg findByStoreMember(Long storeMemberId) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select so.* from xx_store_member_sale_org smso");
		sql.append(" left join xx_sale_org so on so.id=smso.sale_org");
		sql.append(" where smso.is_default = 1");
		if (companyInfoId != null) {
			sql.append(" and smso.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (storeMemberId != null) {
			sql.append(" and smso.store_member = ?");
			list.add(storeMemberId);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		SaleOrg saleOrg = getNativeDao().findSingleManaged(sql.toString(),
				objs,
				SaleOrg.class);
		return saleOrg;
	}
	
	public List<Map<String, Object>> findfactory(String name, Pageable pageable){
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT s.* FROM xx_sale_org s WHERE 1=1 "
				+ "AND s.is_enabled = 1 AND s.factory_type = true AND s.company_info_id = ?");
		list.add(companyInfoId);
		if(name != null){
			sql.append(" AND s.name LIKE ? ");
			list.add("%"+name+"%");
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
	
	/**
	 * 选择机构
	 * @param saleOrgName
	 * @param saleOrgSn
	 * @param isEnabled
	 * @return
	 */
	public Page<Map<String, Object>> findSelectSaleOrgPage(String saleOrgName,
			String saleOrgSn,Boolean isEnabled,Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT s.*,sd.value type_value,sd.id typeId ");
		sql.append(" FROM xx_sale_org s  ");
		sql.append(" LEFT JOIN xx_system_dict sd ON s.type = sd.id AND sd.parent IS NOT NULL ");
		sql.append(" WHERE 1=1 ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and s.company_info_id=?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(saleOrgName)) {
			sql.append(" and s.name like ?");
			list.add("%" + saleOrgName + "%");
		}
		if (!ConvertUtil.isEmpty(saleOrgSn)) {
			sql.append(" and s.sn like ?");
			list.add("%" + saleOrgSn + "%");
		}
		if (!ConvertUtil.isEmpty(isEnabled)) {
			if (isEnabled){
				sql.append(" and s.is_enabled = 1");
			}else{
				sql.append(" and s.is_enabled = 0");
			}	
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and s.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and s.id is null");
			}
		}
		
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		String totalsql = "select count(1) from ( " + sql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;

	}
	//获取所有机构
	public String findAllSaleOrgIds(){
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();

		sql.append(" select GROUP_CONCAT( DISTINCT s.id ) sale_org_ids ");
		sql.append(" from xx_sale_org s ");
		sql.append(" where s.is_enabled = TRUE ");

		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and s.company_info_id = ? ");
			list.add(companyInfoId);
		}

//		sql.append(" group by s.id	");


		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findString(sql.toString(), objs);
	}

	//获取用户机构
	public String findSaleOrgIds() {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT GROUP_CONCAT( DISTINCT t.sale_org_ids ) sale_org_ids FROM ( ");
			sql.append(" SELECT GROUP_CONCAT( DISTINCT s.id ) sale_org_ids,'1' AS 0_id ");
			sql.append(" FROM xx_store_member sm ");
			sql.append(" JOIN xx_store_member_sale_org smso ON sm.id = smso.store_member ");
			sql.append(" JOIN xx_sale_org s ON smso.sale_org = s.id ");
			sql.append(" WHERE sm.company_info_id=smso.company_info_id ");
			sql.append(" AND sm.company_info_id=s.company_info_id ");
			sql.append(" AND s.is_enabled = TRUE ");
			if(!ConvertUtil.isEmpty(companyInfoId)){
				sql.append(" AND sm.company_info_id = ? ");
				list.add(companyInfoId);
			}
			if(!ConvertUtil.isEmpty(storeMemberId)){
				sql.append(" AND sm.id = ? ");
				list.add(storeMemberId);
			}
			sql.append(" GROUP BY sm.id	");
		sql.append(" UNION ");	
			sql.append(" SELECT GROUP_CONCAT( DISTINCT t.sale_org_ids ) sale_org_ids,t.0_id ");
				sql.append(" FROM (SELECT a.id sale_org_ids,'1' AS 0_id ");
				sql.append(" FROM xx_sale_org a,xx_sale_org b  ");
				sql.append(" WHERE a.company_info_id=b.company_info_id ");
				sql.append(" AND a.is_enabled = b.is_enabled ");
				sql.append(" AND a.tree_path LIKE CONCAT('%,', b.id, ',%') ");
				sql.append(" AND b.is_enabled = TRUE  ");
				sql.append(" AND b.id IN (SELECT GROUP_CONCAT( DISTINCT s.id ) sale_org_ids ");
					sql.append(" FROM xx_store_member sm ");
					sql.append(" JOIN xx_store_member_sale_org smso ON sm.id = smso.store_member ");
					sql.append(" JOIN xx_sale_org s ON smso.sale_org = s.id ");
					sql.append(" WHERE sm.company_info_id=smso.company_info_id ");
					sql.append(" AND sm.company_info_id=s.company_info_id ");
					sql.append(" AND s.is_enabled = TRUE ");
					if(!ConvertUtil.isEmpty(companyInfoId)){
						sql.append(" AND sm.company_info_id = ? ");
						list.add(companyInfoId);
					}
					if(!ConvertUtil.isEmpty(storeMemberId)){
						sql.append(" AND sm.id = ? ");
						list.add(storeMemberId);
					}
					sql.append(" GROUP BY sm.id	) ");
			sql.append(" ) t GROUP BY t.0_id ");
		sql.append(" ) t GROUP BY t.0_id ");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}		
		
		return  getNativeDao().findString(sql.toString(), objs);
	}
	
}