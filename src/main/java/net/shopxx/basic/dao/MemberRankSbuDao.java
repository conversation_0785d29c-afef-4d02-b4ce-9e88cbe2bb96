package net.shopxx.basic.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : MemberRankSbuDao
 * @Deseription : TODO
 * <AUTHOR> LanTianLong
 * @Date : 2020/10/14 10:44
 * @Version : 1.0
 **/

@Repository("memberRankSbuDao")
public class MemberRankSbuDao extends DaoCenter {
    public List<Map<String, Object>> findList(Long memberRankId) {

        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

        String sql = "SELECT mrs.*,sbu.name sbu_name FROM xx_member_rank_sbu mrs ";
        sql += "LEFT JOIN xx_member_rank mr on mrs.member_rank = mr.id ";
        sql += "LEFT JOIN xx_sbu sbu on mrs.sbu = sbu.id";
        sql += " WHERE 1=1 ";

        if (ConvertUtil.isEmpty(companyInfoId)) {
            sql += " AND mrs.company_info_id = ?";
            list.add(companyInfoId);
        }
        if(memberRankId!=null){
            sql += " AND mr.id = ?";
            list.add(memberRankId);
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        return getNativeDao().findListMap(sql, objs, 0);

    }

}
