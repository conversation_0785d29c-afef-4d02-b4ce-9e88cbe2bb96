package net.shopxx.basic.dao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.basic.entity.SystemDict;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
@Repository("systemDictDao")
public class SystemDictDao extends DaoCenter{
	
	
	public String findProductionPlantIds() {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT GROUP_CONCAT( DISTINCT pP.id ) production_plant_ids ");
		sql.append(" FROM xx_store_member sm ");
		sql.append(" JOIN xx_warehouse_store_member wsm ON sm.id = wsm.store_member  ");
		sql.append(" JOIN xx_warehouse w ON wsm.warehouse = w.id  ");
		sql.append(" JOIN xx_system_dict pP ON w.production_plant = pP.id ");
		sql.append(" WHERE sm.company_info_id=wsm.company_info_id ");
		sql.append(" AND sm.company_info_id=w.company_info_id  ");
		sql.append(" AND sm.company_info_id=pP.company_info_id  ");
		sql.append(" AND w.is_enabled = TRUE  ");
		
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" AND sm.company_info_id = ? ");
			list.add(companyInfoId);
		}
		
		if(!ConvertUtil.isEmpty(storeMemberId)){
			sql.append(" AND sm.id = ? ");
			list.add(storeMemberId);
		}
		
		sql.append(" GROUP BY sm.id	");
		
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findString(sql.toString(), objs);
	}

    /**
     * 获取指定编码的值
     * @param code
     * @return
     */
	public List<Map<String,Object>> findSystemDictByCode(String code){
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        sql.append("select d.id,d.code,d.remark,d.value,d.lower_code,d.default_filtering from xx_system_dict d where company_info_id =  ? " +
                " and parent is not null and is_enabled is true ");

        list.add(companyInfoId);
        if(!ConvertUtil.isEmpty(code)){
            sql.append(" and d.code = ? ");
            list.add(code);
        }
        sql.append(" order by remark ");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        return getNativeDao().findListMap(sql.toString(),objs,0);
    }


    /**
     * 根据编码获取系统词汇
     * @param code
     * @param value
     * @return
     */
    public List<SystemDict> findSystemDictList(String code, String value){
	    List<Object> list = new ArrayList<Object>();
	    Long companyInfoId =9L;
	    StringBuilder sql=new StringBuilder();
        sql.append("select d.* from xx_system_dict d where company_info_id =  ? " +
                " and parent is not null and is_enabled is true ");

        list.add(companyInfoId);
        if(!ConvertUtil.isEmpty(code)){
            sql.append(" and d.code = ? ");
            list.add(code);
        }

        if(!ConvertUtil.isEmpty(value)){
            sql.append(" and d.value = ? ");
            list.add(value);
        }
		sql.append(" order by sort ");
	    Object[] objs =new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
	    return getNativeDao().findListManaged(sql.toString(),objs,0, SystemDict.class);
    }

}
