package net.shopxx.basic.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.SystemParameter;
import org.springframework.stereotype.Repository;

@Repository("systemParameterBaseDao")
public class SystemParameterBaseDao extends DaoCenter {
  public String getValue(Long companyInfoId, String code) {
    if (ConvertUtil.isEmpty(companyInfoId))
      ExceptionUtil.throwDaoException("value is empty!", new Object[0]); 
    String sql = "select value from xx_system_parameter where code = ? and company_info_id = ?";
    String value = getNativeDao().findString(sql, 
        new Object[] { code, companyInfoId });
    return value;
  }
  
  public SystemParameter getAdminParam(String code) {
    String sql = "select * from xx_system_parameter where company_info_id is null and code='" + 
      code + 
      "'";
    SystemParameter adminParamter = (SystemParameter)getNativeDao().findSingleManaged(sql, 
        null, 
        SystemParameter.class);
    return adminParamter;
  }
}