package net.shopxx.basic.dao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.util.RoleJurisdictionUtil;
@Repository("organizationDao")
public class OrganizationDao extends DaoCenter {
	
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	public Page<Map<String, Object>> findPage(String startTime, String endTime,
			Pageable pageable, String name, String code,Long isEnabled,Long type,
			Integer sign) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_organization xon WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and xon.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and xon.name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(code)) {
			sql.append(" and xon.code like ?");
			list.add("%" + code + "%");
		}
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and xon.create_date >= ?");
			list.add(startTime + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and xon.create_date < ?");
			list.add(endTime + " 24:00:00");
		}
		if (!ConvertUtil.isEmpty(isEnabled)) {
			sql.append(" and xon.is_enabled = ?");
			list.add(isEnabled);
		}
		if(!ConvertUtil.isEmpty(type)){
			sql.append(" and xon.type = ?");
			list.add(type);
		}
		if(!ConvertUtil.isEmpty(sign) && sign == 0){
			//用户经营组织
			String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
				if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
					sql.append(" and xon.id in (" + organizationIdS + ")");
				}else{
					sql.append(" and xon.id is null");
				}
			}
		}
		sql.append(" group by xon.id order by xon.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		String totalsql = "select count(1) from ( " + sql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}

	//获取所有经营组织id
	public String findAllOrganizationIds(){
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();

		sql.append(" SELECT GROUP_CONCAT( DISTINCT o.id ) organization_ids ");
		sql.append(" FROM xx_organization o  ");
		sql.append(" WHERE o.is_enabled = TRUE ");
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" AND o.company_info_id = ? ");
			list.add(companyInfoId);
		}
//		sql.append(" GROUP BY o.id	");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findString(sql.toString(), objs);
	}
	
	
	public String findOrganizationIds() {

			List<Object> list = new ArrayList<Object>();
			Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
			Long storeMemberId = WebUtils.getCurrentStoreMemberId();
			StringBuilder sql = new StringBuilder();
			
			sql.append(" SELECT GROUP_CONCAT( DISTINCT o.id ) organization_ids ");
			sql.append(" FROM xx_store_member sm ");
			sql.append(" JOIN xx_store_member_organization smo ON sm.id = smo.store_member ");
			sql.append(" JOIN xx_organization o ON smo.organization= o.id ");
			sql.append(" WHERE sm.company_info_id=smo.company_info_id ");
			sql.append(" AND sm.company_info_id=o.company_info_id ");
			sql.append(" AND o.is_enabled = TRUE ");
			
			if(!ConvertUtil.isEmpty(companyInfoId)){
				sql.append(" AND sm.company_info_id = ? ");
				list.add(companyInfoId);
			}
			
			if(!ConvertUtil.isEmpty(storeMemberId)){
				sql.append(" AND sm.id = ? ");
				list.add(storeMemberId);
			}
			
			sql.append(" GROUP BY sm.id	");
			
			Object[] objs = new Object[list.size()];
			for (int i = 0; i < list.size(); i++) {
				objs[i] = list.get(i);
			}

			return getNativeDao().findString(sql.toString(), objs);
	}
	
}
