package net.shopxx.basic.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;

@Repository("memberRankSaleOrgDao")
public class MemberRankSaleOrgDao extends DaoCenter {

	public List<Map<String, Object>> findList(Long memberRankId) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		String sql = "SELECT mrs.*,so.name so_name FROM xx_member_rank_sale_org mrs ";
		sql += "LEFT JOIN xx_member_rank mr on mrs.member_rank = mr.id ";
		sql += "LEFT JOIN xx_sale_org so on mrs.sale_org = so.id";
		sql += " WHERE 1=1 ";

		if (ConvertUtil.isEmpty(companyInfoId)) {
			sql += " AND mrs.company_info_id = ?";
			list.add(companyInfoId);
		}
		if(memberRankId!=null){
			sql += " AND mr.id = ?";
			list.add(memberRankId);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql, objs, 0);

	}

}
