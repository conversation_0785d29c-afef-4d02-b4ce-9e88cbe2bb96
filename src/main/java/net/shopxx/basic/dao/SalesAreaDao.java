package net.shopxx.basic.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SalesArea;

import org.springframework.stereotype.Repository;

/**
 * Dao - 销售地区
 */
@Repository("salesAreaDao")
public class SalesAreaDao extends DaoCenter {

	/**
	 * 查找顶级地区
	 * @param locale
	 * @return
	 */
	public List<SalesArea> findRoots(String locale, int count) {

		if (ConvertUtil.isEmpty(locale)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		String sql = "select * from xx_sales_area where parent is null and lower(locale) = lower(?) order by orders asc";
		List<SalesArea> salesAreas = getNativeDao().findListManaged(sql,
				new Object[] { locale },
				count,
				SalesArea.class);
		return salesAreas;
	}

	public SalesArea findAreaByFullName(String fullName) {
		if (ConvertUtil.isEmpty(fullName)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}
		String sql = "select * from xx_sales_area where full_name like ?";
		SalesArea area = getNativeDao().findSingleManaged(sql,
				new Object[] { "%" + fullName + "%" },
				SalesArea.class);
		return area;

	}

	public Page<Map<String, Object>> findAreaPage(String name, String fullName,
			Pageable pageable) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT s.* from xx_sales_area s  join v_sale_area vs on vs.id=s.id where 1=1 and vs.cities is not null ");
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(fullName)) {
			sql.append(" and s.full_name like ?");
			list.add("%" + fullName + "%");
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalsql = "select count(1) from ( " + sql + ") a";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}

	public List<Map<String, Object>> findChildren(Long parentId, String name) {

		String sql = "select s.*, 0 as is_search from xx_sales_area s where parent = ?";
		if (!ConvertUtil.isEmpty(name)) {
			sql += " and s.name like '%" + name + "%' ";
		}
		List<Map<String, Object>> productCategories = getNativeDao().findListMap(sql,
				new Object[] { parentId },
				0);

		return productCategories;
	}

	public List<Map<String, Object>> findList(String sn, String name,
			Boolean isEnabled, Integer type) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (ConvertUtil.isEmpty(sn) && ConvertUtil.isEmpty(name)) {
			sql.append("select s.*, 0 as is_search from xx_sales_area s where parent is null");
		}
		else {
			sql.append("select s.*, 1 as is_search from xx_sales_area s where 1 = 1");
		}
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and s.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}
		if (isEnabled != null) {
			if (isEnabled)
				sql.append(" and s.is_enabled = 1");
			else
				sql.append(" and s.is_enabled = 0");
		}
		sql.append(" order by s.grade, s.orders asc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> productCategories = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		String cSql = "";
		for (Map<String, Object> productCategory : productCategories) {
			String tree_path_name = "";
			String tree_path = productCategory.get("tree_path").toString();
			if (tree_path.length() > 1) {
				tree_path = tree_path.substring(1);
				tree_path = tree_path.substring(0, tree_path.length() - 1);
				cSql = "select group_concat(name separator ' / ') tree_path_name from xx_sales_area where id in ("
						+ tree_path
						+ ")";
				tree_path_name = getNativeDao().findString(cSql, null);
			}
			productCategory.put("tree_path_name", tree_path_name);
		}

		return productCategories;
	}

	/**
	 * 查找下级
	 * 
	 * @param salesArea
	 * @param count
	 * @return
	 */
	public List<SalesArea> findChildren(String sn, String name,
			Boolean isEnabled, SalesArea salesArea, int count) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		String sql = "select * from xx_sales_area where 1=1";
		if (salesArea != null) {
			sql += " and tree_path like ?";
			list.add("%"
					+ salesArea.TREE_PATH_SEPARATOR
					+ salesArea.getId()
					+ salesArea.TREE_PATH_SEPARATOR
					+ "%");
		}
		if (companyInfoId != null) {
			sql += " and company_info_id = ?";
			list.add(companyInfoId);
		}
		else {
			sql += " and company_info_id is null";
		}
		if (sn != null) {
			sql += " and sn = ?";
			list.add(sn);
		}
		if (name != null) {
			sql += " and name like ?";
			list.add("%" + name + "%");
		}
		if (isEnabled != null) {
			if (isEnabled)
				sql += " and is_enabled = 1";
			else
				sql += " and is_enabled = 0";
		}
		sql += " order by orders asc";

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<SalesArea> salesAreas = getNativeDao().findListManaged(sql,
				objs,
				count,
				SalesArea.class);

		return sort(salesAreas, salesArea);
	}

	private List<SalesArea> sort(List<SalesArea> salesAreas, SalesArea parent) {
		List<SalesArea> result = new ArrayList<SalesArea>();
		if (salesAreas != null) {
			for (SalesArea salesArea : salesAreas) {
				if ((salesArea.getParent() != null && salesArea.getParent()
						.equals(parent))
						|| (salesArea.getParent() == null && parent == null)) {
					result.add(salesArea);
					result.addAll(sort(salesAreas, salesArea));
				}
			}
		}
		return result;
	}
}