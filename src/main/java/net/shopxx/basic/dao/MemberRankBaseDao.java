package net.shopxx.basic.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.MemberRank;

import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * Dao - 会员等级
 */
@Repository("memberRankBaseDao")
public class MemberRankBaseDao extends DaoCenter {

	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	/**
	 * 根据企业id查找默认会员等级
	 * @param companyInfoId
	 * @return
	 */
	public MemberRank findDefault(Long companyInfoId) {

		if (ConvertUtil.isEmpty(companyInfoId)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		String sql = "select * from xx_member_rank where is_default = 1 and company_info_id = ?";
		MemberRank memberRank = getNativeDao().findSingleManaged(sql,
				new Object[] { companyInfoId },
				MemberRank.class);
		return memberRank;
	}

	/**
	 * 设置默认会员等级
	 * @param memberRankId
	 */
	public void setDefault(Long memberRankId) {

		Long companyInfoId = WebUtils.getPrincipal().getCompanyinfoid();
		if (companyInfoId == null) return;
		String sql = "update xx_member_rank set is_default = 0 where is_default = 1 and company_info_id = ?";
		Object[] objs = null;
		if (memberRankId != null) {
			sql += " and id <> ?";
			objs = new Object[] { companyInfoId, memberRankId };
		}
		else {
			objs = new Object[] { companyInfoId };
		}
		getNativeDao().update(sql, objs);
	}
	
	public Page<Map<String, Object>> findPage(String name, String firstTime,
			String lastTime, Integer isShow, Long saleOrgId, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT mr.*,so.name sale_org_names FROM xx_member_rank mr  ");
		sql.append(" LEFT JOIN xx_sale_org so on mr.sale_org=so.id WHERE 1=1");

		if (companyInfoId != null) {
			sql.append(" AND mr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" AND mr.name = ?");
			list.add(name);
		}
//		if(!ConvertUtil.isEmpty(firstTime)){
//			sql.append(" AND ");
//			list.add();
//		}
//		if(!ConvertUtil.isEmpty(lastTime)){
//			sql.append(" AND mr.is_show = ?");
//			list.add();
//		}
		if (isShow != null) {
			sql.append(" AND mr.is_enabled = ?");
			list.add(isShow);
		}
		if (saleOrgId != null) {
			sql.append(" AND so.id = ?");
			list.add(saleOrgId);
		}

		sql.append(" group by mr.id order by mr.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return page;
	}
	
	public List<Map<String, Object>> findPageList(Long saleOrgId,Long sbuId,String name, Integer grade) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT DISTINCT mr.* FROM xx_member_rank mr ");
		sql.append(" LEFT JOIN xx_member_rank_sbu mrs ON mrs.member_rank = mr.id ");
		sql.append("LEFT JOIN xx_member_rank_sale_org mro ON mro.member_rank = mr.id ");
		sql.append(" WHERE 1=1 and mr.is_enabled = 1 ");
		if (companyInfoId != null) {
			sql.append(" AND mr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if(name!=null){
			sql.append(" AND mr.name like ? ");
			list.add("%" + name + "%");
		
		}

		if(grade!=null){
			sql.append(" AND mr.grade = ?");
			list.add(grade);
		}

		if(saleOrgId!=null){
			sql.append(" and mro.sale_org = ? ");
			list.add(saleOrgId);
		}else{ String saleOrgIds=roleJurisdictionUtil.getSaleOrgIds();
			if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
				sql.append(" and mro.sale_org in (" + saleOrgIds + ") ");
			}
		}

		if(sbuId!=null){
			sql.append(" and mrs.sbu = ? ");
			list.add(sbuId);
		}else{ String sbuIds=roleJurisdictionUtil.getSbuIds();
			if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
				sql.append(" and mrs.sbu in (" + sbuIds + ") ");
			}

		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql.toString(), objs, 0);
	};
}