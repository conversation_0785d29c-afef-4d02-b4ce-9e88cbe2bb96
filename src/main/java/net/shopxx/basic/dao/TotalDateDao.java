package net.shopxx.basic.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Repository("totalDateDao")
public class TotalDateDao extends DaoCenter{
	
	public Page<Map<String, Object>> findPage(String sn,Boolean isEnabled,
			Long[] saleOrgId,Long[] sbuId,Long[] organizationId,String startTime,
			String endTime,Pageable pageable) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT td.*,so.id sale_org_id,so.name sale_org_name,s.id sbu_id, ");
		sql.append(" s.name sbu_name,o.id organization_id,o.name organization_name ");
		sql.append(" FROM xx_total_date td ");
		sql.append(" LEFT JOIN xx_sale_org so ON so.id = td.sale_org ");
		sql.append(" LEFT JOIN xx_sbu s ON s.id = td.sbu ");
		sql.append(" LEFT JOIN xx_organization o ON o.id = td.organization ");
		sql.append(" WHERE 1=1 ");
		
		//主体
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and td.company_info_id = ?");
			list.add(companyInfoId);
		}
		//编号
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and td.sn like ?");
			list.add("%" + sn + "%");
		}
		//是否启用
		if (!ConvertUtil.isEmpty(isEnabled)) {
			sql.append(" and td.is_enabled = ?");
			list.add(isEnabled);
		}
		//机构
		if (!ConvertUtil.isEmpty(saleOrgId) && saleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					os += saleOrgId[i];
				else
					os += saleOrgId[i] + ",";
			}
			sql.append(" and so.id in (" + os + ")");
		}
		//sbu
		if (!ConvertUtil.isEmpty(sbuId) && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += sbuId[i];
				else
					os += sbuId[i] + ",";
			}
			sql.append(" and s.id in (" + os + ")");
		}
		//经营组织
		if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += organizationId[i];
				else
					os += organizationId[i] + ",";
			}
			sql.append(" and o.id in (" + os + ")");
		}
	
		/** 创建日期 */
		//开始
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and td.create_date >= ?");
			list.add(startTime + " 00:00:00");
		}
		//结束
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and td.create_date < ?");
			list.add(endTime + " 24:00:00");
		}
		
		sql.append(" and td.effective_start_date <= now()  and td.effective_end_date > now() order by td.modify_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		String totalsql = "select count(1) from ( " + sql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}
	
	public List<Map<String, Object>> findTotalDateList(Boolean isEnabled,
			Long saleOrgId,Long sbuId,Long[] organizationId,String billDate,SystemDict totalDateType){
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = Long.valueOf(9);
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT td.* ");
		sql.append(" FROM xx_total_date td ");
		sql.append(" LEFT JOIN xx_sale_org so ON so.id = td.sale_org ");
		sql.append(" LEFT JOIN xx_sbu s ON s.id = td.sbu ");
		sql.append(" LEFT JOIN xx_organization o ON o.id = td.organization ");
		sql.append(" WHERE 1=1  ");
		//主体
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and td.company_info_id = ?");
			list.add(companyInfoId);
		}
		//是否启用
		if (!ConvertUtil.isEmpty(isEnabled)) {
			sql.append(" and td.is_enabled = ?");
			list.add(isEnabled);
		}
		//机构
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and ( so.id is null or so.id = ? ) ");
			list.add(saleOrgId);
		}
		//sbu
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" and ( s.id is null or s.id = ? ) ");
			list.add(sbuId);
		}
	      //类型
        if (!ConvertUtil.isEmpty(totalDateType)) {
            sql.append(" and total_date_type = ? ");
            list.add(totalDateType.getId());
        }
		//经营组织
		if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += organizationId[i];
				else
					os += organizationId[i] + ",";
			}
			sql.append(" and ( o.id is null or o.id in (" + os + "))" );
		}
		//开始
		if (!ConvertUtil.isEmpty(billDate)) {
			sql.append(" and td.start_date <= ?");
			list.add(billDate + " 00:00:00");
		}
		//结束
		if (!ConvertUtil.isEmpty(billDate)) {
			sql.append(" and td.end_date > ?");
			list.add(billDate + " 23:59:59");
		}
		
        sql.append(" and td.effective_start_date <= now()  and td.effective_end_date > now() order by td.modify_date desc");
        
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
	
		List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(), 
				objs, 
				0);
		return mapList;
	}
	
}
