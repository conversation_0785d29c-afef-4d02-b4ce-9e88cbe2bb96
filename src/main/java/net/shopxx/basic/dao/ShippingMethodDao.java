package net.shopxx.basic.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;

import org.springframework.stereotype.Repository;

@Repository("shippingMethodDao")
public class ShippingMethodDao extends DaoCenter {
	public Page<Map<String, Object>> findpage(String name,Pageable pageable){
	List<Object> list = new ArrayList<Object>();
	Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
	StringBuilder sql = new StringBuilder();
	sql.append(" SELECT * FROM xx_shipping_method sm WHERE 1=1 ");
	if (!ConvertUtil.isEmpty(name)) {
		sql .append( " and sm.name like '%" +name+ "%'");
	}
	Object[] objs = new Object[list.size()];
	for (int i = 0; i < list.size(); i++) {
		objs[i] = list.get(i);
	}
	return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}
}
