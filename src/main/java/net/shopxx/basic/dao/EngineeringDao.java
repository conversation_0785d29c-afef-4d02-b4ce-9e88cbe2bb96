package net.shopxx.basic.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;

/**
 * Dao - 工程
 * 
 * <AUTHOR> Team
 * @version 1.0
 *
 */
@Repository("engineeringDao")
public class EngineeringDao extends DaoCenter {
	public Page<Map<String, Object>> findPage(String name, String sn,
			String storeName, Long storeId, String oaSn, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sb = new StringBuilder();
		sb.append("select e.*, s.name store_name from xx_engineering e");
		sb.append(" left join xx_store s on e.store=s.id where 1=1");

		if (companyInfoId != null) {
			sb.append(" and e.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(name)) {
			sb.append(" and e.name like ?");
			list.add("%" + name + "%");
		}
		if (storeId != null) {
			sb.append(" and e.store =?");
			list.add(storeId);
		}
		if (sn != null) {
			sb.append(" and e.sn= ? ");
			list.add(sn);
		}
		if (storeName != null) {
			sb.append(" and s.name= ? ");
			list.add(storeName);
		}
		if (!ConvertUtil.isEmpty(oaSn)) {
			sb.append(" and e.oa_sn like ?");
			list.add("%" + oaSn + "%");
		}

		sb.append(" order by e.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sb.toString(),
				objs,
				pageable);

		return page;
	}
}