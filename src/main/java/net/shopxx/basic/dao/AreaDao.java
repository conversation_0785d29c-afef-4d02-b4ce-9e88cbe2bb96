package net.shopxx.basic.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;

import org.springframework.stereotype.Repository;

/**
 * Dao - 地区
 */
@Repository("areaDao")
public class AreaDao extends DaoCenter {

	/**
	 * 查找顶级地区
	 * @param count
	 * @return
	 */
	public List<Area> findRoots(String locale, int count) {

		if (ConvertUtil.isEmpty(locale)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "select * from xx_area where parent is null and lower(locale) = lower(?) and company_info_id= ? order by orders asc";
		List<Area> areas = getNativeDao().findListManaged(sql,
				new Object[] { locale,companyInfoId },
				count,
				Area.class);
		return areas;
	}

	public Area findAreaByFullName(String fullName) {
		if (ConvertUtil.isEmpty(fullName)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}
		String sql = "select * from xx_area where full_name like ?";
		Area area = getNativeDao().findSingleManaged(sql,
				new Object[] { "%" + fullName + "%" },
				Area.class);
		return area;

	}

	public Page<Map<String, Object>> findAreaPage(String name, String fullName,
			Pageable pageable) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("select a.* from xx_area a right join v_area va on a.id=va.id where 1=1 and va.cities is not null   and a.company_info_id=9 ");
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and a.name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(fullName)) {
			sql.append(" and a.full_name like ?");
			list.add("%" + fullName + "%");
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalsql = "select count(1) from ( " + sql + ") a";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}
	
	public Area findAreaByFullNameDD(String fullName) {
		if (ConvertUtil.isEmpty(fullName)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}
		String sql = "select * from xx_area where full_name = ?";
		Area area = getNativeDao().findSingleManaged(sql,
				new Object[] { fullName },
				Area.class);
		return area;

	}
	
}