package net.shopxx.basic.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.util.RoleJurisdictionUtil;

import org.springframework.stereotype.Repository;

/**
 * 产品新增维护查询sbu
 * 
 * */
@Repository("sbuDao")
public class SbuDao extends DaoCenter {
	
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	

	public List<Map<String, Object>> findListMap(Long id) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_sbu s WHERE 1=1 ");

		if (companyInfoId != null) {
			sql.append(" AND s.company_info_id = ?");
			list.add(companyInfoId);
		}
		sql.append(" AND s.status = true ");
		sql.append(" AND s.is_default = true ");

		if (!ConvertUtil.isEmpty(id)) {
			sql.append(" AND s.store_members = ? ");
			list.add(id);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	/**
	 * 
	 *SBU列表查询 
	 **/
	public Page<Map<String, Object>> findPage(Long sbuId, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_sbu s WHERE 1=1 ");
//		sql.append(" AND s.`status`=1 ");

		if (companyInfoId != null) {
			sql.append(" AND s.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (sbuId != null) {
			sql.append(" AND s.id = ?");
			list.add(sbuId);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	/**
	 * 商品sbu查询
	 * 
	 * */
	public List<Map<String, Object>> findProductSbu(Long id) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_sbu s WHERE 1=1 ");

		if (companyInfoId != null) {
			sql.append(" AND s.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (!ConvertUtil.isEmpty(id)) {
			sql.append(" AND s.product = ? ");
			list.add(id);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	public Page<Map<String, Object>> findPageFiltr(String sbuName,
			Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_sbu s WHERE 1=1 ");
		sql.append(" AND s.`status`=1 ");

		if (companyInfoId != null) {
			sql.append(" AND s.company_info_id = ?");
			list.add(companyInfoId);
		}
        if (!ConvertUtil.isEmpty(sbuName)) {
            sql.append(" and s.name like ?");
            list.add("%" + sbuName + "%");
        }
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and s.id in (" + sbuIds + ")");
			}else{
				sql.append(" and s.id is null");
			}
		}
		//过滤已选择的sbu
//		if (storeType != null &&storeType == 1){
//
//		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	};

	/**
	 * 查找sbu发运方式
	 * */
	public List<Map<String, Object>> findShippingMethodSbu(Long id) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT sis.*,sis.is_default,sd.id shippingwayId ,sd.value shippingwayName "
				+ " FROM xx_sbu_items_sbu  sis "
				+ " LEFT JOIN xx_sbu sb on sb.id=sis.sbu "
				+ " LEFT JOIN xx_system_dict sd on sd.id= sis.shipping_method "
				+ "WHERE 1=1 ";
		if (id != null) {
			sql += " and sis.sbu =" + id;
		}
		if (companyInfoId != null) {
			sql += " and sis.company_info_id=" + companyInfoId;
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
		return lists;
	}
	
	
	/**
	 * 查找sbu角色
	 * */
	public List<Map<String, Object>> findPcRoleSbu(Long id) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT pr.id,pr.name,pr.description "
				+ " FROM xx_menu_role_sbu  mr "
				+ " LEFT JOIN xx_sbu sb on sb.id=mr.sbu "
				+ " LEFT JOIN xx_pc_role pr on pr.id= mr.pc_role "
				+ "WHERE 1=1 ";
		if (id != null) {
			sql += " and mr.sbu =" + id;
		}
		if (companyInfoId != null) {
			sql += " and mr.company_info_id=" + companyInfoId;
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
		return lists;
	}
	
	
	/**
	 * 查找sbu政策
	 * */
	public List<Map<String, Object>> findPolicySbu(Long id) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT sp.* "
				+ " FROM xx_sbu_policy  sp "
				+ " LEFT JOIN xx_sbu sb on sb.id=sp.sbu "
				+ " LEFT JOIN xx_system_dict sd on sd.id= sp.policy_type "
				+ "WHERE 1=1 ";
		if (id != null) {
			sql += " and sp.sbu =" + id;
		}
		if (companyInfoId != null) {
			sql += " and sp.company_info_id=" + companyInfoId;
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
		return lists;
	}
	
	/**
	 * 查找sbu发票
	 * */
	public List<Map<String, Object>> findInvoiceSbu(Long id) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT si.* "
				+ " FROM xx_sbu_invoice  si "
				+ " LEFT JOIN xx_sbu sb on sb.id=si.sbu "
				+ " LEFT JOIN xx_system_dict sd on sd.id= si.invoice_type "
				+ "WHERE 1=1 ";
		if (id != null) {
			sql += " and si.sbu =" + id;
		}
		if (companyInfoId != null) {
			sql += " and si.company_info_id=" + companyInfoId;
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
		return lists;
	}
	
	/**
	 * 查找sbu现金流
	 * */
	public List<Map<String, Object>> findCashSbu(Long id) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT sc.* "
				+ " FROM xx_sbu_case  sc "
				+ " LEFT JOIN xx_sbu sb on sb.id=sc.sbu "
				+ " LEFT JOIN xx_system_dict sd on sd.id= sc.case_project "
				+ "WHERE 1=1 ";
		if (id != null) {
			sql += " and sc.sbu =" + id;
		}
		if (companyInfoId != null) {
			sql += " and sc.company_info_id=" + companyInfoId;
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql,
				null,
				0);
		return lists;
	}

	//获取所有sbu
    public String findAllSbuIds(){
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT GROUP_CONCAT( DISTINCT s.id ) sbu_ids ");
        sql.append(" FROM xx_sbu s ");
        sql.append(" WHERE s.status = TRUE ");

        if(!ConvertUtil.isEmpty(companyInfoId)){
            sql.append(" AND s.company_info_id = ? ");
            list.add(companyInfoId);
        }

//        sql.append(" GROUP BY s.id	");


        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        return getNativeDao().findString(sql.toString(), objs);
    }

	//获取用户sbu
	public String findSbuIds() {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT GROUP_CONCAT( DISTINCT s.id ) sbu_ids ");
		sql.append(" FROM xx_store_member sm ");
		sql.append(" JOIN xx_store_member_sbu sms ON sm.id = sms.store_member ");
		sql.append(" JOIN xx_sbu s ON sms.sbu= s.id ");
		sql.append(" WHERE sm.company_info_id=sms.company_info_id ");
		sql.append(" AND sm.company_info_id=s.company_info_id ");
		sql.append(" AND s.status = TRUE ");
		
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" AND sm.company_info_id = ? ");
			list.add(companyInfoId);
		}
		
		if(!ConvertUtil.isEmpty(storeMemberId)){
			sql.append(" AND sm.id = ? ");
			list.add(storeMemberId);
		}
		
		sql.append(" GROUP BY sm.id	");
		
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findString(sql.toString(), objs);
	}
	
	
}
