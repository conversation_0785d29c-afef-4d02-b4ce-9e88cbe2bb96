package net.shopxx.basic.controller;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.PaymentMethod.Method;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.member.entity.Post;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
/**
 * Controller - 经营机构
 */
@Controller("organizationController")
@RequestMapping("/member/management")
public class OrganizationController extends BaseController{
	
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/member/management/list_tb";
	}
	
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/management/list";
	}
	
	
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String name, Pageable pageable, String code,
			String startTime, String endTime,Long isEnabled, ModelMap model, Long type, 
			Integer sign) {

		Page<Map<String, Object>> page = organizationService.findPage(startTime, endTime, 
				pageable, name, code, isEnabled, type, sign);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	
	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		model.addAttribute("methods", Method.values());
		return "/member/management/add";
	}
	
	
	
	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(Organization organization) {

		if (organization.getName() == null) {
			return error("请填写组织名称");
		}
	
		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("name", organization.getName()));
	
		List<Organization> organizationList = organizationService.findList(null, fis, null);
		
		if (organizationList != null && organizationList.size() > 0) {
			return error("该组织名称已存在，请重新维护");
		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if(companyInfoId!=null){
			organization.setCompanyInfoId(companyInfoId);
		}
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if(storeMember!=null){
			organization.setbCreater(storeMember.getUsername());
			organization.setbModifier(storeMember.getUsername());
			organization.setModifyDate(new Date());
			organization.setCreateDate(new Date());
		}
		organizationService.save(organization);
		return success().addObjX(organization.getId());
	}
	
	
	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		model.addAttribute("organization",organizationService.find(id));
		return "/member/management/edit";
	}
	
	
	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(Organization organization) {
		if (organization.getName() == null) {
			return error("请填写组织名称");
		}
		List<Filter> fis = new ArrayList<Filter>();
		fis.clear();
		fis.add(Filter.ne("id",organization.getId()));
		fis.add(Filter.eq("name", organization.getName()));
		List<Organization> organizationList = organizationService.findList(null, fis, null);
		if (organizationList != null && organizationList.size() > 0) {
			return error("该组织名称已存在，请重新维护");
		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if(companyInfoId!=null){
			organization.setCompanyInfoId(companyInfoId);
		}
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if(storeMember!=null){
			organization.setbModifier(storeMember.getUsername());
			organization.setModifyDate(new Date());
		}
		organizationService.update(organization);
		return success();
	}
	
	
	/**
	 * 查询经营组织列表
	 */
	@RequestMapping(value = "/select_organization", method = RequestMethod.GET)
	public String selectOrganization(ModelMap model) {

		return "/member/management/select_organization";
	}
	
}
