package net.shopxx.basic.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.ShippingMethod;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.stock.service.DeliveryCorpBaseService;

/**
 * Controller - 配送方式
 */
@Controller("basicShippingMethodController")
@RequestMapping("/basic/shipping_method")
public class ShippingMethodController extends BaseController {

	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "deliveryCorpBaseServiceImpl")
	private DeliveryCorpBaseService deliveryCorpBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		return "/basic/shipping_method/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(ShippingMethod shippingMethod) {

		shippingMethodBaseService.save(shippingMethod);
		return success().addObjX(shippingMethod.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {

		model.addAttribute("shippingMethod",
				shippingMethodBaseService.find(id));
		return "/basic/shipping_method/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(ShippingMethod shippingMethod) {
		shippingMethodBaseService.update(shippingMethod);
		return success();
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/basic/shipping_method/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/basic/shipping_method/list_tb";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Pageable pageable, String name,
			ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		if (name != null) {
			filters.add(Filter.like("name", "%" + name + "%"));
		}
		String jsonPage = JsonUtils.toJson(
				shippingMethodBaseService.findPage(filters, null, pageable));
		return ResultMsg.success(jsonPage);
	}

	/**
	 * 删除
	 */
//	@RequestMapping(value = "/delete", method = RequestMethod.POST)
//	public @ResponseBody
//	ResultMsg delete(Long[] ids) {
//		if (ids.length >= shippingMethodService.count()) {
//			//不允许删除所有
//			return error("19030007");
//		}
//		shippingMethodService.delete(ids);
//		return success();
//	}

}