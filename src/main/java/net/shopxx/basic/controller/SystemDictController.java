package net.shopxx.basic.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Order;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SystemDictBaseService;

/**
 * Controller - 系统词汇
 */
@Controller("basicSystemDictController")
@RequestMapping("/basic/system_dict")
public class SystemDictController extends BaseController {

    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(Pageable pageable, ModelMap model) {

        return "/basic/system_dict/list";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Pageable pageable, ModelMap model) {

        return "/basic/system_dict/list_tb";
    }

    /**
     * 列表数据
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody ResultMsg list_data(String code, String value,
                                             String remark, Boolean isEnabled, Pageable pageable,
                                             ModelMap model) {

        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.isNull("parent"));
        if (!ConvertUtil.isEmpty(code)) {
            filters.add(Filter.like("code", "%" + code + "%"));
        }
        if (!ConvertUtil.isEmpty(value)) {
            filters.add(Filter.like("value", "%" + value + "%"));
        }
        if (!ConvertUtil.isEmpty(remark)) {
            filters.add(Filter.like("remark", "%" + remark + "%"));
        }
        if (isEnabled != null) {
            filters.add(Filter.eq("isEnabled", isEnabled));
        }
        List<Order> orders = new ArrayList<Order>();
        orders.add(Order.asc("code"));
        Page<SystemDict> page = systemDictBaseService.findPage(filters,
                orders,
                pageable);
        String jsonPage = JsonUtils.toJson(page);
        return ResultMsg.success(jsonPage);
    }

    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(Long ids, ModelMap model) {

        SystemDict parent = systemDictBaseService.find(ids);
        model.addAttribute("parent", parent);
        return "/basic/system_dict/add";
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/view", method = RequestMethod.GET)
    public String view(Long id, ModelMap model) {

        SystemDict systemDict = systemDictBaseService.find(id);
        model.addAttribute("systemDict", systemDict);

        return "/basic/system_dict/view";
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ResponseBody ResultMsg save(SystemDict systemDict, Long parentId) {

//        if (parentId == null) {
//            //请选择词汇编码
//            return error("17253");
//        }
        if (ConvertUtil.isEmpty(systemDict.getCode())){
            return error("词汇编码不能为空!");
        }

        String value = systemDict.getValue();
        if (ConvertUtil.isEmpty(value)) {
            //请填写词汇值
            return error("17251");
        }
        if (!ConvertUtil.isEmpty(parentId)){
            SystemDict parent = systemDictBaseService.find(parentId);
            systemDict.setParent(parent);
            String code = parent.getCode();
            systemDict.setCode(code);
        }


        Long id = systemDict.getId();
        if (id == null) {
            if (systemDictBaseService.count(Filter.eq("code", systemDict.getCode()),
                    Filter.eq("value", value)) > 0) {
                //该词汇编码对应词汇值已存在
                return error("17252");
            }
            systemDictBaseService.save(systemDict);
        }
        else {
            if (systemDictBaseService.count(Filter.eq("code", systemDict.getCode()),
                    Filter.eq("value", value),
                    Filter.ne("id", id)) > 0) {
                //该词汇编码对应词汇值已存在
                return error("17252");
            }
            systemDictBaseService.update(systemDict);
        }
        return success().addObjX(systemDict.getId());
    }

    /**
     * 获取下级词汇
     * @return
     */
    @RequestMapping(value = "/getChildren", method = RequestMethod.POST)
    public @ResponseBody ResultMsg getChildren(Long id) {

        List<Map<String, Object>> data = new ArrayList<Map<String, Object>>();
        SystemDict systemDict = systemDictBaseService.find(id);
        List<Filter> filters = new ArrayList<Filter>();
        List<Order> orders = new ArrayList<Order>();
        filters.add(Filter.eq("parent", systemDict));
        orders.add(Order.desc("order"));
        List<SystemDict> childrens = systemDictBaseService.findList(null,
                filters,
                orders);
        for (SystemDict children : childrens) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("id", children.getId());
            map.put("code", children.getCode());
            map.put("lowerCode",children.getLowerCode());
            map.put("value", children.getValue());
            map.put("remark",
                    children.getRemark() == null ? "" : children.getRemark());
            map.put("order",
                    children.getOrder() == null ? "" : children.getOrder());
            map.put("isEnabled", children.getIsEnabled());
            map.put("createDate", DateUtil.convert(children.getCreateDate()));
            map.put("parent", children.getParent().getId());
            map.put("flag", children.getFlag());
            data.add(map);
        }
        return success().addObjX(data);
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/select_system_dict", method = RequestMethod.GET)
    public String select_system_dict(String code,Boolean isChild,
                                     String value, String remark, Boolean isEnabled,Pageable pageable, ModelMap model,Integer multi) {

        model.addAttribute("code", code);
        model.addAttribute("value", value);
        model.addAttribute("remark", remark);
        model.addAttribute("isEnabled", isEnabled);
        model.addAttribute("isChild", isChild);
        model.addAttribute("multi",multi);
        return "/basic/system_dict/select_system_dict";
    }

    /**
     * 列表数据
     */
    @RequestMapping(value = "/select_system_dict_data", method = RequestMethod.POST)
    public @ResponseBody ResultMsg select_system_dict_data(String code, String value, String remark, Boolean isEnabled, Pageable pageable,Boolean isChild,
                                                           ModelMap model) {

        List<Filter> filters = new ArrayList<Filter>();
        if(isChild!=null && isChild==true) {
            filters.add(Filter.isNotNull("parent"));
        } else {
            filters.add(Filter.isNull("parent"));
        }
        if (!ConvertUtil.isEmpty(code)) {
            filters.add(Filter.like("code", "%" + code + "%"));
        }
        if (!ConvertUtil.isEmpty(value)) {
            filters.add(Filter.like("value", "%" + value + "%"));
        }
        if (!ConvertUtil.isEmpty(remark)) {
            filters.add(Filter.like("remark", "%" + remark + "%"));
        }
        if (isEnabled != null) {
            filters.add(Filter.eq("isEnabled", isEnabled));
        }
        List<Order> orders = new ArrayList<Order>();
        orders.add(Order.asc("code"));
        Page<SystemDict> page = systemDictBaseService.findPage(filters,
                orders,
                pageable);
        String jsonPage = JsonUtils.toJson(page);
        return ResultMsg.success(jsonPage);
    }
}