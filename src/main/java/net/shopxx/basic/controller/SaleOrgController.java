package net.shopxx.basic.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Controller - 机构
 */
@Controller("enterpriseSaleOrgController")
@RequestMapping("/basic/saleOrg")
public class SaleOrgController extends BaseController {

	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Pageable pageable,
			ModelMap model,Long menuId,Long userId) {
		model.addAttribute("menuId",menuId);
		model.addAttribute("code", code);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return CommonUtil.getFolderPrefix(code) + "/basic/saleOrg/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Pageable pageable,
			ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		model.addAttribute("code", code);
		return CommonUtil.getFolderPrefix(code) + "/basic/saleOrg/list_tb";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String saleOrgName, String saleOrgSn,
			Boolean isEnabled, Pageable pageable, ModelMap model) {

		List<Map<String, Object>> saleOrgs = saleOrgBaseService.findList(saleOrgName,
				saleOrgSn,
				null,
				isEnabled,
				null,
				pageable);
		String jsonPage = JsonUtils.toJson(saleOrgs);

		return ResultMsg.success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add(@PathVariable String code, Long ids, ModelMap model) {

		Long companyInfoId = companyInfoBaseService.getCurrentId();
		model.addAttribute("companyInfoId", companyInfoId);
		SaleOrg parent = saleOrgBaseService.find(ids);
		List<Filter> filters = new ArrayList<Filter>();
		if (parent == null) {
			filters.add(Filter.eq("isTop", true));
			parent = saleOrgBaseService.find(filters);
		}
		model.addAttribute("parent", parent);

		filters.clear();
		filters.add(Filter.eq("code", "SaleOrgType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgTypes", saleOrgTypes);

		filters.clear();
		filters.add(Filter.eq("code", "sbu"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> sbus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("sbus", sbus);

		return CommonUtil.getFolderPrefix(code) + "/basic/saleOrg/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(SaleOrg saleOrg, Long areaId, Long saleOrgId,
			Long memberRankId, HttpServletRequest request) {

		if (saleOrgId != null) {
			SaleOrg so = saleOrgBaseService.find(saleOrgId);
			so.setIsLeaf(false);
			saleOrgBaseService.update(so);
			saleOrg.setParent(so);
		}
		else {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("isTop", true));
			SaleOrg parent = saleOrgBaseService.find(filters);
			saleOrg.setParent(parent);
		}
		List<SaleOrg> saleOrgs = saleOrgBaseService.findChildren(null,
				null,
				saleOrg);
		if (saleOrgs != null && saleOrgs.size() == 0) {
			saleOrg.setIsLeaf(true);
		}
		else {
			saleOrg.setIsLeaf(false);
		}
		MemberRank memberRank = memberRankBaseService.find(memberRankId);
		saleOrg.setMemberRank(memberRank);

		saleOrgBaseService.save(saleOrg);

		return success().addObjX(saleOrg.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
	public String edit(@PathVariable String code, Long id, ModelMap model) {

		Long companyInfoId = companyInfoBaseService.getCurrentId();
		model.addAttribute("companyInfoId", companyInfoId);
		model.addAttribute("saleOrg", saleOrgBaseService.find(id));
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "SaleOrgType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgTypes", saleOrgTypes);

		filters.clear();
		filters.add(Filter.eq("code", "sbu"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> sbus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("sbus", sbus);

		return CommonUtil.getFolderPrefix(code) + "/basic/saleOrg/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(SaleOrg saleOrg, Long areaId, Long saleOrgId,
			Long memberRankId, HttpServletRequest request) {

		List<SaleOrg> saleOrgs = saleOrgBaseService.findChildren(null,
				null,
				saleOrg);
		if (!saleOrg.getIsEnabled()) {
			for (SaleOrg so : saleOrgs) {
				so.setIsEnabled(false);
				saleOrgBaseService.update(so);
			}
		}
		MemberRank memberRank = memberRankBaseService.find(memberRankId);
		saleOrg.setMemberRank(memberRank);

		if (saleOrgId != null) {
			SaleOrg so = saleOrgBaseService.find(saleOrgId);
			so.setIsLeaf(false);
			saleOrg.setParent(so);
			saleOrgBaseService.update(saleOrg,
					"uniqueIdentify",
					"budget",
					"lockBudget");
		}
		else {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("isTop", true));
			SaleOrg parent = saleOrgBaseService.find(filters);
			saleOrg.setParent(parent);
			saleOrgBaseService.update(saleOrg,
					"parent",
					"uniqueIdentify",
					"budget",
					"lockBudget");
		}
		return success();
	}

	/**
	 * 删除
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg delete(Long[] ids) {
		for (Long id : ids) {
			SaleOrg saleOrg = saleOrgBaseService.find(id);
			if (saleOrg.getIsTop() != null && saleOrg.getIsTop()) {
				return error("默认机构[" + saleOrg.getName() + "]不能删除！");
			}
			if (storeBaseService.exists(Filter.eq("saleOrg", saleOrg))) {
				// 该机构存在对应的客户，不允许删除
				return error("机构[" + saleOrg.getName() + "]存在对应的客户,不能删除！");
			}
			List<Map<String, Object>> saleOrgs = saleOrgBaseService.findChildren(id);
			if (saleOrgs != null && saleOrgs.size() > 0) {
				return error("机构[" + saleOrg.getName() + "]存在下级机构不能删除！");
			}
			saleOrgBaseService.deleteSaleOrg(saleOrg);
		}
		return success();
	}

	/**
	 * 弹框查询组织列表
	 * 
	 * @return
	 */
	@RequestMapping(value = "/select_saleOrg", method = RequestMethod.GET)
	public String select_saleOrg(Pageable pageable, Integer multi,
			Integer isSellSaleOrg, Integer isSelect, ModelMap model) {

		model.addAttribute("multi", multi);
		model.addAttribute("isSellSaleOrg", isSellSaleOrg);
		model.addAttribute("isSelect", isSelect);
		return "/basic/saleOrg/select_saleOrg";
	}

	/**
	 * 弹框查询组织列表数据
	 */
	@RequestMapping(value = "/select_saleOrg_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_saleOrg_data(String name,Pageable pageable,ModelMap model) {
		Page<Map<String, Object>> pagMap = saleOrgBaseService.findSelectSaleOrgPage(name,null, true, pageable);
		
		String jsonPage = JsonUtils.toJson(pagMap);

		return ResultMsg.success(jsonPage);
	}
	
	@RequestMapping(value = "/select_factory", method = RequestMethod.GET)
	public String select_factory(Pageable pageable, Integer multi,
			 ModelMap model) {

		model.addAttribute("multi", multi);
		return "/basic/saleOrg/select_factory";
	}
	
	@RequestMapping(value = "/select_factory_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_factory_data(String name,Pageable pageable,
			ModelMap model) {

		List<Map<String, Object>> saleOrgs = saleOrgBaseService.findfactory(name,
				pageable);
		String jsonPage = JsonUtils.toJson(saleOrgs);

		return ResultMsg.success(jsonPage);
	}

	@RequestMapping(value = "/getChildren", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getChildren(Long id) {

		List<Map<String, Object>> saleOrgs = saleOrgBaseService.findChildren(id);
		return success().addObjX(saleOrgs);
	}

	/**
	 * 获取分类节点
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getNodes", method = RequestMethod.POST)
	public @ResponseBody
	List<Object> getNodes(Long id, String keyword) {
		List<Object> objs = new ArrayList<Object>();
		if (keyword != null) {

		}
		else {
			SaleOrg saleOrg = saleOrgBaseService.find(id);
			if (saleOrg != null) {
				for (SaleOrg child : saleOrg.getChildren()) {
					if (child.getIsEnabled() != null && child.getIsEnabled()) {
						Map<String, Object> data = new HashMap<String, Object>();
						data.put("id", child.getId());
						data.put("name", child.getName());
						data.put("isParent", !getIsLeft(child));
						objs.add(data);
					}
				}

			}
			else {
				List<Filter> fis = new ArrayList<Filter>();
				fis.add(Filter.isNull("parent"));
				fis.add(Filter.eq("isEnabled", true));
				for (SaleOrg child : saleOrgBaseService.findList(null,
						fis,
						null)) {
					Map<String, Object> data = new HashMap<String, Object>();
					data.put("id", child.getId());
					data.put("name", child.getName());
					data.put("isParent", !getIsLeft(child));
					objs.add(data);
				}
			}
		}
		return objs;
	}

	public boolean getIsLeft(SaleOrg saleOrg) {
		boolean isLeft = saleOrg.getIsLeaf();
		if (!isLeft) {
			long count = saleOrgBaseService.count(Filter.eq("parent", saleOrg),
					Filter.eq("isEnabled", true));
			if (count == 0) {
				isLeft = true;
			}
		}
		return isLeft;
	}
}