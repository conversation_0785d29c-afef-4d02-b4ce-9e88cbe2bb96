package net.shopxx.basic.controller;

import javax.annotation.Resource;

import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.AppElement;
import net.shopxx.basic.entity.Engineering;
import net.shopxx.basic.service.EngineeringService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.SnUtil;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

/**
 * 
 */
@Controller("basicEngineeringController")
@RequestMapping("/basic/engineering")
public class EngineeringController extends BaseController {

	@Resource(name = "engineeringServiceImpl")
	private EngineeringService engineeringService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long meunId) {
		model.addAttribute("meunId",meunId);
		menuJumpUtils.getModelMap(model,userId,meunId);
		return "/basic/engineering/list";
	}

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model,Long meunId) {
		model.addAttribute("meunId",meunId);
		return "/basic/engineering/list_tb";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg listData(Pageable pageable, ModelMap model, String name,String sn,String storeName,
			Long storeId,String oaSn) {

		// List<Filter> fis = new ArrayList<Filter>();
		// if (themeColor != null) {
		// fis.add(Filter.like("themeColor", "%" + themeColor + "%"));
		// }
		String jsonPage = JsonUtils.toJson(engineeringService.findPage(name,
				sn,
				storeName,
				storeId,
				oaSn,
				pageable));

		return ResultMsg.success(jsonPage);
	}

	/**
	 * 添加
	 * 
	 * @param engineering
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(AppElement appElement, ModelMap model) {
		return "/basic/engineering/add";
	}

	/**
	 * 编辑
	 * 
	 * @param id
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		model.addAttribute("engineering", engineeringService.find(id));
		return "/basic/engineering/edit";
	}

	/**
	 * 新增编辑保存
	 * 
	 * @param engineering
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/saveUpdate", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg saveUpdate(Engineering engineering, Long storeId, ModelMap model,
			RedirectAttributes redirectAttributes) {

		Store store = storeService.find(storeId);
		engineering.setStore(store);
		if (engineering.getId() == null) {
			engineering.setSn(SnUtil.generateSn());
			engineeringService.save(engineering);
		}
		else {
			engineeringService.update(engineering,"sn");
		}
		return success().addObjX(engineering.getId());
	}

	@RequestMapping(value = "/select_list", method = RequestMethod.GET)
	public String select_list(Pageable pageable, Long storeId, ModelMap model) {

		model.addAttribute("storeId", storeId);
		return "/basic/engineering/select_list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectListData(Pageable pageable, ModelMap model, String name,String sn, String storeName,
			Long storeId) {

		String jsonPage = JsonUtils.toJson(engineeringService.findPage(name,
				sn,
				storeName,
				storeId,
				null,
				pageable));
		return ResultMsg.success(jsonPage);
	}

	/**
	 * 删除
	 * 
	 * @param ids
	 * @return
	 */
	// @RequestMapping(value = "/delete", method = RequestMethod.POST)
	// public @ResponseBody ResultMsg delete(Long[] ids) {
	// appElementService.delete(ids);
	// return success();
	// }
}
