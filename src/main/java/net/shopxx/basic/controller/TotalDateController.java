package net.shopxx.basic.controller;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.*;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.DateUtils;
import net.shopxx.util.SnUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Controller("totalDateController")
@RequestMapping("/basic/totalDate")
public class TotalDateController extends BaseController{
	
	@Resource(name = "totalDateServiceImpl")
	private TotalDateService totalDateService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "storeMemberSbuServiceImpl") 
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "systemDictBaseServiceImpl")
	SystemDictBaseService systemDictBaseService;
	
	
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/basic/totalDate/list_tb";
	}
	
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/basic/totalDate/list";
	}
	
	
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn,Boolean isEnabled,
			Long[] saleOrgId,Long[] sbuId,Long[] organizationId,String startTime,
			String endTime,Pageable pageable) {

		Page<Map<String, Object>> page = totalDateService.findPage( sn, isEnabled,
				 saleOrgId, sbuId, organizationId, startTime, endTime, pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	
	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
	
		List<Filter> filters = new ArrayList<Filter>();
		//用户经营组织
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);
		
		//用户SBU
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberSbu> storeMemberSbuList = storeMemberSbuService.findList(null,filters,null);
		model.addAttribute("storeMemberSbuList", storeMemberSbuList);
		
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("startDate", format.format(DateUtils.getFirstDay()));
		model.addAttribute("endDate", format.format(DateUtils.getToday()));

		//总账类型
		filters.clear();
		filters.add(Filter.eq("code", "totalDateType"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled",true));
		List<SystemDict> systemDictList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("totalDateTypeList", systemDictList);


		
		return "/basic/totalDate/add";
	}
	
	
	
	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(TotalDate totalDate,Long saleOrgId,
			Long sbuId,Long organizationId,Long totalDateTypeId) {
		
		if(ConvertUtil.isEmpty(totalDate.getStartDate())){
			return error("请填写开始时间");
		}
		if(ConvertUtil.isEmpty(totalDate.getEndDate())){
			return error("请填写开始时间");
		}
		//机构
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		if(!ConvertUtil.isEmpty(saleOrg)){
			totalDate.setSaleOrg(saleOrg);
		}
		//SBU
		Sbu sbu = sbuService.find(sbuId);
		if(!ConvertUtil.isEmpty(sbu)){
			totalDate.setSbu(sbu);
		}
		//经营组织
		Organization organization = organizationService.find(organizationId);
		if(!ConvertUtil.isEmpty(organization)){
			totalDate.setOrganization(organization);
		}
		//单据编号
		totalDate.setSn(SnUtil.getTotalDateSn());
		//词汇：类型
		totalDate.setTotalDateType(systemDictBaseService.find(totalDateTypeId));

		totalDateService.save(totalDate);
		return success().addObjX(totalDate.getId());
	}
	
	
	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		
		model.addAttribute("totalDate",totalDateService.find(id));
		
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
	
		List<Filter> filters = new ArrayList<Filter>();
		//用户经营组织
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);
		
		//用户SBU
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberSbu> storeMemberSbuList = storeMemberSbuService.findList(null,filters,null);
		model.addAttribute("storeMemberSbuList", storeMemberSbuList);

		//总账类型
		filters.clear();
		filters.add(Filter.eq("code", "totalDateType"));
		filters.add(Filter.eq("isEnabled",true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> systemDictList = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("totalDateTypeList", systemDictList);
		
		return "/basic/totalDate/edit";
	}
	
	
	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(TotalDate totalDate,
			Long saleOrgId,Long sbuId,Long organizationId) {
		
		if(ConvertUtil.isEmpty(totalDate.getStartDate())){
			return error("请填写开始时间");
		}
		if(ConvertUtil.isEmpty(totalDate.getEndDate())){
			return error("请填写开始时间");
		}
		//机构
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		totalDate.setSaleOrg(saleOrg);
		//SBU
		Sbu sbu = sbuService.find(sbuId);
	    totalDate.setSbu(sbu);
		
		//经营组织
		Organization organization = organizationService.find(organizationId);
		totalDate.setOrganization(organization);
		
		totalDateService.update(totalDate,"sn");
		
		return success().addObjX(totalDate.getId());
	}
	
	
	
	/**
	 * 失效操作
	 * @param id
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/invalid", method = RequestMethod.POST)
	public @ResponseBody ResultMsg invalid(Long id, ModelMap model) {
		TotalDate totalDate = totalDateService.find(id);
		if (ConvertUtil.isEmpty(totalDate)) {
			return error("失效操作失败，当前单据不存在");
		}
		if (ConvertUtil.isEmpty(totalDate.getIsEnabled()) || 
				(!ConvertUtil.isEmpty(totalDate.getIsEnabled()) && 
						!totalDate.getIsEnabled())) {
			return error("当前单据已失效，禁止重复操作");
		}
		totalDateService.invalid(totalDate);
		return success();
	}

}
