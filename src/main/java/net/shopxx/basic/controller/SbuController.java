package net.shopxx.basic.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductSbu;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductSbuService;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller("basicSbuController")
@RequestMapping("/basic/sbu")
public class SbuController extends BaseController {

	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/basic/sbu/list_tb";
	}

	/*
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
        model.addAttribute("menuId",menuId);
        menuJumpUtils.getModelMap(model, userId, menuId);
		return "/basic/sbu/list";
	}

	/*
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(Pageable pageable) {
		Page<Map<String, Object>> page = sbuService.findPage(null, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);

	}

	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		List<Filter> filterss = new ArrayList<Filter>();
		filterss.add(Filter.eq("code", "shippingWay"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> shippingWays = systemDictService.findList(null,
				filterss,
				null);
		
		model.addAttribute("shippingWays", shippingWays);
		//经营组织
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);
		
		filters.clear();
		filters.add(Filter.eq("code", "policyType"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> policyTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("policyTypes", policyTypes);

		filters.clear();
		filters.add(Filter.eq("code", "invoiceType"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> invoiceTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("invoiceTypes", invoiceTypes);

		filters.clear();
		filters.add(Filter.eq("code", "cashProject"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> cashProjects = systemDictService.findList(null, filters, null);
		model.addAttribute("cashProjects", cashProjects);

		return "/basic/sbu/add";
	}

	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		//在列表中选择一个sbu并获取其id,查找出来sbu
		Sbu sbus = sbuService.find(id);
		//查找出当前sbu有的发运方式
		List<Map<String, Object>> shippingMethodList = sbuService.findShippingMethodSbu(sbus.getId());
		model.addAttribute("shippingMethod_json",
				JsonUtils.toJson(shippingMethodList));
		model.addAttribute("sbus", sbus);
		model.addAttribute("id", id);
		//从系统词汇中查出所有的发运方式
		List<Filter> filterss = new ArrayList<Filter>();
		filterss.add(Filter.eq("code", "shippingWay"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> shippingWays = systemDictService.findList(null,
				filterss,
				null);
		model.addAttribute("shippingWays", shippingWays);
		//经营组织
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);
		
		filters.clear();
		filters.add(Filter.eq("code", "policyType"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> policyTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("policyTypes", policyTypes);

		filters.clear();
		filters.add(Filter.eq("code", "invoiceType"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> invoiceTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("invoiceTypes", invoiceTypes);

		filters.clear();
		filters.add(Filter.eq("code", "cashProject"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> cashProjects = systemDictService.findList(null, filters, null);
		model.addAttribute("cashProjects", cashProjects);
		
		
		List<Map<String, Object>> pcRoleList = sbuService.findPcRoleSbu(sbus.getId());
		model.addAttribute("pcRole_json",
				JsonUtils.toJson(pcRoleList));
		
		

		List<Map<String, Object>> policyTypeList = sbuService.findPolicySbu(sbus.getId());
		model.addAttribute("policy_json",
				JsonUtils.toJson(policyTypeList));
		
		List<Map<String, Object>> invoiceTypeList = sbuService.findInvoiceSbu(sbus.getId());
		model.addAttribute("invoice_json",
				JsonUtils.toJson(invoiceTypeList));
		
		List<Map<String, Object>> cashList = sbuService.findCashSbu(sbus.getId());
		model.addAttribute("cash_json",
				JsonUtils.toJson(cashList));
		
		return "/basic/sbu/edit";
	}

	/*
	 * 新增
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg saveUpdate(Sbu sbu,Long organizationsId, ModelMap model) {
		sbu.setUndefined4(organizationService.find(organizationsId));
		sbuService.saveSbu(sbu);

		return success().addObjX(sbu.getId());
	}

	/*
	 * 编辑
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(Sbu sbu,Long organizationsId, ModelMap model) {
		Organization or = organizationService.find(organizationsId);
		sbu.setUndefined4(or);
		sbuService.updateSbu(sbu);

		return success().addObjX(sbu.getId());
	}

	@RequestMapping(value = "/select_sbu", method = RequestMethod.GET)
	public String select_sbu(Integer multi, Long sbuId, ModelMap model) {
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("multi", multi);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("storeMemberId", storeMember.getId());
		return "/basic/sbu/select_sbu";
	}

	/*
	 * sbu选择弹出框
	 */
	@RequestMapping(value = "/select_sbu_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectSbuDate(Pageable pageable,String name) {
		Page<Map<String, Object>> page = sbuService.findPageFiltr(name,
				pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping(value = "/select_sbu_filtr", method = RequestMethod.GET)
	public String select_sbu_filtr(Integer multi, Long sbuId, ModelMap model) {
		model.addAttribute("multi", multi);
		model.addAttribute("sbuId", sbuId);
		return "/basic/sbu/select_sbu_filtr";
	}

	@RequestMapping(value = "/select_sbu_data_filtr", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectSbuDateFiltr(Long sbuId, Pageable pageable) {
		Page<Map<String, Object>> page = sbuService.findPage(sbuId, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping(value = "/select_shippingMethod_filtr", method = RequestMethod.GET)
	public String select_shippingMethod_filtr(Integer multi, ModelMap model) {
		model.addAttribute("multi", multi);
		return "/basic/shipping_method/select_shippingMethod_filtr";
	}

	@RequestMapping(value = "/select_shippingMethod_data_filtr", method = RequestMethod.POST)
	public @ResponseBody ResultMsg selectShippingMethodDateFiltr(String name, Pageable pageable) {
		Page<Map<String, Object>> page = shippingMethodBaseService.findPage(name,
				pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	
	
	
	@RequestMapping(value = "/select_sbu_by_product_id", method = RequestMethod.POST)
	public @ResponseBody ResultMsg selectSbuBySku(Long productId) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Product product = productBaseService.find(productId);
		if(!ConvertUtil.isEmpty(product)){
			List<ProductSbu> productSbuList = product.getProductSbu();
			if(!productSbuList.isEmpty() && productSbuList.size()>0){
				for (ProductSbu productSbu : productSbuList) {
					Sbu sbu = productSbu.getSbu();
					if(!ConvertUtil.isEmpty(sbu) && !ConvertUtil.isEmpty(sbu.getStatus()) && sbu.getStatus()){
						Map<String, Object> map = new HashMap<String, Object>();
					    map.put("id", sbu.getId());
					    map.put("name", sbu.getName());
					    map.put("is_default", productSbu.getIsDefault());
					    list.add(map);
					}
				}
			}
		}
	    return success().addObjX(list);
	  }

}
