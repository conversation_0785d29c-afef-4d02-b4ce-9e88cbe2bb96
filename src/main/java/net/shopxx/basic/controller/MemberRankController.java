package net.shopxx.basic.controller;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.*;
import net.shopxx.member.service.MemberBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.template.tempUtil.MenuJumpUtils;

import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Controller - 会员等级
 */
@Controller("memberMemberRankController")
@RequestMapping("/basic/member_rank")
public class MemberRankController extends BaseController {

	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "memberBaseServiceImpl")
	private MemberBaseService memberBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "memberRankSaleOrgServiceImpl")
	private MemberRankSaleOrgService memberRankSaleOrgService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;
    @Resource(name = "sbuServiceImpl")
    private SbuService sbuService;
    @Resource(name = "memberRankSbuServiceImpl")
    private MemberRankSbuService memberRankSbuService;
    @Resource(name = "roleJurisdictionUtil")
    private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long menuId, ModelMap model) {
		model.addAttribute("menuId", menuId);
		return "/basic/member_rank/list_tb";
	}
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, Long userId, Long menuId,
			ModelMap model) {
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/basic/member_rank/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String name, String firstTime, String lastTime,
			Integer isShow, Long saleOrgId, Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = memberRankBaseService.findPage(name,
				firstTime,
				lastTime,
				isShow,
				saleOrgId,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return ResultMsg.success(jsonPage);
	}
	
	

	/**    
	 * 检查名称是否唯一
	 */
	@RequestMapping(value = "/check_name", method = RequestMethod.GET)
	public @ResponseBody
	boolean checkName(String name, Long id) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (id == null) {
			return !memberBaseService.exists(Filter.eq("name", name),
					Filter.isNotNull("name"),
					Filter.eq("companyInfoId", companyInfoId));
		}
		else {
			return !memberBaseService.exists(Filter.eq("name", name),
					Filter.isNotNull("name"),
					Filter.ne("id", id),
					Filter.eq("companyInfoId", companyInfoId));
		}
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "rankType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rankTypes = systemDictService.findList(null,filters,null);
		model.addAttribute("rankTypes", rankTypes);
		return "/basic/member_rank/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(MemberRank memberRank, Long saleOrgId,Long sbuId,Long rankTypeId) {

		Long companyInfoId = companyInfoBaseService.getCurrentId();
		if (companyInfoId == null) {
			//您没有权限进行操作
			return error("16300");
		}
		if (memberRank.getName() == null) {
			//名称不能为空
			return error("16301");
		}
		Sbu sbu=sbuService.find(sbuId);
		memberRank.setSbu(sbu);
		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		memberRank.setSaleOrg(saleOrg);
		
		if(rankTypeId!=null) {
			SystemDict rankType=systemDictService.find(rankTypeId);
			memberRank.setRankType(rankType);
		}
		
		if (memberRankBaseService.exists(Filter.eq("name", memberRank.getName()),
				Filter.eq("companyInfoId", companyInfoId))) {
			//该会员等级已存在
			return error("16302");
		}if(memberRank.getGrade() != null) {
			if (memberRankBaseService.exists(Filter.eq("grade",
					memberRank.getGrade()),
					Filter.eq("companyInfoId", companyInfoId))) {
				//该会员等级级别已存在
				return error("16304");
			}
		}
		memberRankBaseService.MSave(memberRank);
		return success().addObjX(memberRank.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		MemberRank memberRank = memberRankBaseService.find(id);
		//机构
		String json = JsonUtils.toJson(memberRankSaleOrgService.findList(memberRank.getId()));
		model.addAttribute("json", json);
		//SBU
        String jsonSbu = JsonUtils.toJson(memberRankSbuService.findList(memberRank.getId()));
        model.addAttribute("jsonSbu", jsonSbu);
		model.addAttribute("memberRank", memberRank);
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "rankType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rankTypes = systemDictService.findList(null,filters,null);
		model.addAttribute("rankTypes", rankTypes);
		
		return "/basic/member_rank/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(MemberRank memberRank,Long saleOrgId,
			RedirectAttributes redirectAttributes,Long sbuId,Long rankTypeId) {


		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId == null) {
			//您没有权限进行操作
			return error("16300");
		}
		if (memberRank.getName() == null) {
			//名称不能为空
			return error("16301");
		}

		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		memberRank.setSaleOrg(saleOrg);
		Sbu sbu=sbuService.find(sbuId);
		memberRank.setSbu(sbu);
		MemberRank pMemberRank = memberRankBaseService.find(memberRank.getId());
		if (pMemberRank == null) {
			//该会员等级不存在
			return error("16303");
		}
		if (!pMemberRank.getName().equals(memberRank.getName())) {
			if (memberRankBaseService.exists(Filter.eq("name",
					memberRank.getName()),
					Filter.eq("companyInfoId", companyInfoId))) {
				//该会员等级已存在
				return error("16302");
			}
		}
		if(memberRank.getGrade() != null){
			if (memberRankBaseService.exists(Filter.eq("grade",
					memberRank.getGrade()),
					Filter.eq("companyInfoId", companyInfoId),
					Filter.ne("id", pMemberRank.getId()))) {
				//该会员等级级别已存在
				return error("16304");
			}
		}
		
		if(rankTypeId!=null) {
			SystemDict rankType=systemDictService.find(rankTypeId);
			memberRank.setRankType(rankType);
		}
		
//		if (!memberRank.getIsEnabled()) {
//			if (activityPriceHeadService
//					.exists(Filter.eq("memberRank", pMemberRank))) {
//				//该价格等级在活动价格中存在，不允许禁用
//				return error("16305");
//			}
//			if (productPriceHeadService
//					.exists(Filter.eq("memberRank", pMemberRank))
//					|| productPriceService.exists(Filter
//							.eq("storeMemberPriceKey", memberRank.getId()))) {
//				//该价格等级在产品会员价中存在，不允许禁用
//				return error("16306");
//			}
//			if (storeBaseService.exists(Filter.eq("memberRank", pMemberRank))) {
//				//该价格等级存在对应的客户，不允许禁用
//				return error("16307");
//			}
//		}
		memberRankBaseService.MUpdate(memberRank, "members");
		return success();
	}
	


	/**
	 * 删除
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg delete(Long[] ids) {
		for (int i = 0; i < ids.length; i++) {
			MemberRank memberRank = memberRankBaseService.find(ids[i]);
			if (storeBaseService.exists(Filter.eq("memberRank", memberRank))) {
				//该价格等级存在对应的客户，不允许删除
				return error("16310");
			}
		}
		memberRankBaseService.delete(ids);
		return success();
	}

	/**
	 * excel导入会员等级
	 * @param file
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file) throws Exception {

		Map<String, Object> results = memberRankBaseService.memberRankImport(file);
		int result = Integer.parseInt(results.get("result").toString());
		int success = Integer.parseInt(results.get("success").toString());
		String msg = results.get("msg").toString();

		if (ConvertUtil.isEmpty(msg)) {
			return success("总数" + result + "行，成功导入" + success + " 行");
		}
		else {
			return error("总数" + result + "行，成功导入" + success + " 行<br>" + msg);
		}
	}

	/**
	 * 列表
	 * @param model
	 * @param
	 * @return
	 */
	@RequestMapping(value = "select_memberRank", method = RequestMethod.GET)
	public String select_memberRank(Integer multi,Long saleOrgId, ModelMap model,Long sbuId) {
		model.addAttribute("multi", multi);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("sbuId", sbuId);
		return "/basic/member_rank/select_memberRank";

	}

	@RequestMapping(value = "select_memberRank_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_memberRank_data(String name, Integer grade,
			Long saleOrgId, Long sbuId) {
		//价格类型
			List<Map<String, Object>> page = memberRankBaseService.findPageList(saleOrgId,sbuId, name, grade);
        String	jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 价格类型列表查询
	 * @param model
	 * @param
	 * @return
	 */
	@RequestMapping(value = "select_memberRank_price", method = RequestMethod.GET)
	public String select_memberRank_price(Long saleOrgId,Long sbuId, ModelMap model) {
		model.addAttribute("saleOrgId", saleOrgId);
        model.addAttribute("sbuId", sbuId);
		return "/basic/member_rank/select_memberRank_price";

	}

	@RequestMapping(value = "select_memberRank_price_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_memberRank_price_data(String name, Integer grade,
			Long saleOrgId, Long sbuId) {
		List<Map<String, Object>> page = memberRankBaseService.findPageList(saleOrgId,sbuId, name, grade);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 查询客户 SBU下的价格类型
	 * 		storeId： 客户ID
	 * 		sbuId: 客户sbu_ID
	 * 		storeName: 客户名称
 	 */
	@RequestMapping(value = "/select_store_sbu_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectStoreAddressData(Long storeId, Pageable pageable, ModelMap model) {
		List<Map<String, Object>> storeSbu = storeSbuService.findStoreSbu(storeId);
		String jsonPage = JsonUtils.toJson(storeSbu);
		return success(jsonPage);
	}

}