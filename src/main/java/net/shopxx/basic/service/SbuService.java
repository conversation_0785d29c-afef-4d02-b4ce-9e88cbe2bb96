package net.shopxx.basic.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.Sbu;


public interface SbuService extends BaseService<Sbu>{
	
	/**根据id查找对应sbu*/
	public List<Map<String, Object>> findListMap(Long id);
	
	/**
	 * 
	 * SBU列表查询 
	 * */
	public Page<Map<String, Object>> findPage(Long sbuId,Pageable pageable);
	
	/**
	 * 商品sbu查询 
	 **/
	public List<Map<String, Object>> findProductSbu(Long id);
	/**
	 * sbu发运方式查询 
	 **/
	public List<Map<String, Object>> findShippingMethodSbu(Long id);
	
	/**
	 * 角色查询 
	 **/
	public List<Map<String, Object>> findPcRoleSbu(Long id);
	
	
	/**
	 * 政策类型查询
	 **/
	public List<Map<String, Object>> findPolicySbu(Long id);
	
	/**
	 * 发票类型查询
	 **/
	public List<Map<String, Object>> findInvoiceSbu(Long id);
	
	
	/**
	 * ERP现金项目查询
	 **/
	public List<Map<String, Object>> findCashSbu(Long id);
	
	/**
	 * sbu过滤列表弹框查询
	 * */
	public Page<Map<String, Object>> findPageFiltr(String sbuName,Pageable pageable);
	
	/**sbu保存*/
	public void saveSbu(Sbu sbu);
	
	/**sbu更新*/
	public void updateSbu(Sbu sbu);

}
