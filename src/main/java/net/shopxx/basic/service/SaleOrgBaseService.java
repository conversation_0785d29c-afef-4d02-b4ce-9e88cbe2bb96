/*
 * 
 */
package net.shopxx.basic.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.SaleOrg;

/**
 * Service - 机构
 */
public interface SaleOrgBaseService extends BaseService<SaleOrg> {

	/**
	 * 根据条件查找机构树
	 * @param name
	 * @param isEnabled
	 * @return
	 */
	public List<SaleOrg> findTree(String name, Boolean isEnabled);

	/**
	 * 根据条件查找下级机构
	 * @param name
	 * @param isEnabled
	 * @param saleOrg
	 * @return
	 */
	public List<SaleOrg> findChildren(String name, Boolean isEnabled,
			SaleOrg saleOrg);

	public List<Map<String, Object>> findList(String saleOrgName,
			String saleOrgSn,Integer isSellSaleOrg, Boolean isEnabled,Integer isSelect, Pageable pageable);

	public List<Map<String, Object>> findChildren(Long parentId);

	public SaleOrg findByStoreMember(Long storeMemberId);

	public void deleteSaleOrg(SaleOrg saleOrg);
	
	public List<Map<String, Object>> findfactory(String name,Pageable pageable);
	
	
	/**
	 * 选择机构
	 * @param saleOrgName
	 * @param saleOrgSn
	 * @param isEnabled
	 * @return
	 */
	public Page<Map<String, Object>> findSelectSaleOrgPage(String saleOrgName,
			String saleOrgSn,Boolean isEnabled,Pageable pageable);
	
	
	
}
