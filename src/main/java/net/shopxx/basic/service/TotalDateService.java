package net.shopxx.basic.service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.entity.TotalDate;

import java.util.Date;
import java.util.List;
import java.util.Map;
public interface TotalDateService extends BaseService<TotalDate>{
	
	
	public Page<Map<String, Object>> findPage(String sn,Boolean isEnabled,
			Long[] saleOrgId,Long[] sbuId,Long[] organizationId,String startTime,
			String endTime,Pageable pageable);
	
	
	List<Map<String, Object>> findTotalDateList(Boolean isEnabled,
			Long saleOrgId,Long sbuId,Long[] organizationId,Date billDate,SystemDict totalDateType);

	
	/**
	 * 失效
	 * @param totalDate
	 */
	public void invalid(TotalDate totalDate);
}
