package net.shopxx.basic.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.dao.SbuDao;
import net.shopxx.basic.entity.MenuRoleSbu;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SbuCase;
import net.shopxx.basic.entity.SbuInvoice;
import net.shopxx.basic.entity.SbuItems;
import net.shopxx.basic.entity.SbuPolicy;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSbuService;

@Service("sbuServiceImpl")
public class SbuServiceImpl extends BaseServiceImpl<Sbu> implements SbuService {

	@Resource(name = "sbuDao")
	private SbuDao sbuDao;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "pcRoleBaseServiceImpl")
	private PcRoleBaseService pcRoleBaseService;
	

	@Override
	public List<Map<String, Object>> findListMap(Long id) {
		// TODO Auto-generated method stub
		return sbuDao.findListMap(id);
	}

	@Override
	public List<Map<String, Object>> findProductSbu(Long id) {
		// TODO Auto-generated method stub
		return sbuDao.findProductSbu(id);
	}

	public Page<Map<String, Object>> findPageFiltr(String sbuName,
			Pageable pageable) {
		return sbuDao.findPageFiltr(sbuName, pageable);
	};

	@Override
	public Page<Map<String, Object>> findPage(Long sbuId, Pageable pageable) {

		return sbuDao.findPage(sbuId, pageable);
	}

	@Override
	public void updateSbu(Sbu sbu) {
		List<SbuItems> shippingMethodSbuList = sbu.getShippingMethodSbuList();
		 List<String> list = new ArrayList<String>();
		for (Iterator<SbuItems> iterator = shippingMethodSbuList.iterator(); iterator.hasNext();) {
			//获得每个sbu发运明细
			SbuItems sbuItem = (SbuItems) iterator.next();
			if (sbuItem.getShippingMethod() == null
					|| sbuItem.getShippingMethod().getId() == null) {
				iterator.remove();
				continue;
			}
			sbuItem.setSbu(sbu);
			SystemDict shippingWay = systemDictService.find(sbuItem.getShippingMethod()
					.getId());
			//不能有相同的发运方式
			if(list.contains(shippingWay.getValue())){
				ExceptionUtil.throwServiceException("不能有相同的发运方式");
			}
			list.add(shippingWay.getValue());
			sbuItem.setShippingMethod(shippingWay);
		}
		List<MenuRoleSbu> menuRoleSbus = sbu.getMenuRoleSbus();
		for (Iterator<MenuRoleSbu> iterator = menuRoleSbus.iterator(); iterator.hasNext();) {
			
			MenuRoleSbu menuRoleSbu= (MenuRoleSbu) iterator.next();
			if (menuRoleSbu.getPcRole() == null
					|| menuRoleSbu.getPcRole().getId() == null) {
				iterator.remove();
				continue;
			}
			menuRoleSbu.setSbu(sbu);
			PcRole pcRole = pcRoleBaseService.find(menuRoleSbu.getPcRole()
					.getId());
			
			menuRoleSbu.setPcRole(pcRole);
		}
		
		List<SbuPolicy> sbuPolicys = sbu.getSbuPolicys();
		for (Iterator<SbuPolicy> iterator = sbuPolicys.iterator(); iterator.hasNext();) {
			
			SbuPolicy sbuPolicy= (SbuPolicy) iterator.next();
			if (sbuPolicy.getPolicyType() == null
					|| sbuPolicy.getPolicyType() .getId() == null) {
				iterator.remove();
				continue;
			}
			sbuPolicy.setSbu(sbu);
			SystemDict policyType = systemDictService.find(sbuPolicy.getPolicyType()
					.getId());
			
			sbuPolicy.setPolicyType(policyType);
			
		}
		
		List<SbuInvoice> sbuInvoices = sbu.getSbuInvoices();
		for (Iterator<SbuInvoice> iterator = sbuInvoices.iterator(); iterator.hasNext();) {
			
			SbuInvoice sbuInvoice= (SbuInvoice) iterator.next();
			if (sbuInvoice.getInvoiceType() == null
					|| sbuInvoice.getInvoiceType().getId() == null) {
				iterator.remove();
				continue;
			}
			sbuInvoice.setSbu(sbu);
			SystemDict invoiceType = systemDictService.find(sbuInvoice.getInvoiceType()
					.getId());
			
			sbuInvoice.setInvoiceType(invoiceType);
			
		}
		
		List<SbuCase> sbuCases = sbu.getSbuCases();
		for (Iterator<SbuCase> iterator = sbuCases.iterator(); iterator.hasNext();) {
			
			SbuCase sbuCase= (SbuCase) iterator.next();
			if (sbuCase.getCaseProject()== null
					|| sbuCase.getCaseProject().getId() == null) {
				iterator.remove();
				continue;
			}
			sbuCase.setSbu(sbu);
			SystemDict caseProjcet = systemDictService.find(sbuCase.getCaseProject()
					.getId());
			
			sbuCase.setCaseProject(caseProjcet);
			
		}
		update(sbu);
	}

	@Transactional
	public void saveSbu(Sbu sbu) {
		//获取当前sbu的发运明细
		 List<String> list = new ArrayList<String>();
		List<SbuItems> shippingMethodSbuList = sbu.getShippingMethodSbuList();
		for (Iterator<SbuItems> iterator = shippingMethodSbuList.iterator(); iterator.hasNext();) {
			//获得每个sbu发运明细
			SbuItems sbuItem = (SbuItems) iterator.next();
			if (sbuItem.getShippingMethod() == null
					|| sbuItem.getShippingMethod().getId() == null) {
				iterator.remove();
				continue;
			}
			sbuItem.setSbu(sbu);
			SystemDict shippingWay = systemDictService.find(sbuItem.getShippingMethod()
					.getId());
			//不能有相同的发运方式
			if(list.contains(shippingWay.getValue())){
				ExceptionUtil.throwServiceException("不能有相同的发运方式");
			}
			list.add(shippingWay.getValue());
			sbuItem.setShippingMethod(shippingWay);
		}
		
		List<MenuRoleSbu> menuRoleSbus = sbu.getMenuRoleSbus();
		for (Iterator<MenuRoleSbu> iterator = menuRoleSbus.iterator(); iterator.hasNext();) {
			
			MenuRoleSbu menuRoleSbu= (MenuRoleSbu) iterator.next();
			if (menuRoleSbu.getPcRole() == null
					|| menuRoleSbu.getPcRole().getId() == null) {
				iterator.remove();
				continue;
			}
			menuRoleSbu.setSbu(sbu);
			PcRole pcRole = pcRoleBaseService.find(menuRoleSbu.getPcRole()
					.getId());
			
			menuRoleSbu.setPcRole(pcRole);
			
		}
		
		List<SbuPolicy> sbuPolicys = sbu.getSbuPolicys();
		for (Iterator<SbuPolicy> iterator = sbuPolicys.iterator(); iterator.hasNext();) {
			
			SbuPolicy sbuPolicy= (SbuPolicy) iterator.next();
			if (sbuPolicy.getPolicyType() == null
					|| sbuPolicy.getPolicyType() .getId() == null) {
				iterator.remove();
				continue;
			}
			sbuPolicy.setSbu(sbu);
			SystemDict policyType = systemDictService.find(sbuPolicy.getPolicyType()
					.getId());
			
			sbuPolicy.setPolicyType(policyType);
			
		}
		
		List<SbuInvoice> sbuInvoices = sbu.getSbuInvoices();
		for (Iterator<SbuInvoice> iterator = sbuInvoices.iterator(); iterator.hasNext();) {
			
			SbuInvoice sbuInvoice= (SbuInvoice) iterator.next();
			if (sbuInvoice.getInvoiceType() == null
					|| sbuInvoice.getInvoiceType().getId() == null) {
				iterator.remove();
				continue;
			}
			sbuInvoice.setSbu(sbu);
			SystemDict invoiceType = systemDictService.find(sbuInvoice.getInvoiceType()
					.getId());
			
			sbuInvoice.setInvoiceType(invoiceType);
			
		}
		
		List<SbuCase> sbuCases = sbu.getSbuCases();
		for (Iterator<SbuCase> iterator = sbuCases.iterator(); iterator.hasNext();) {
			
			SbuCase sbuCase= (SbuCase) iterator.next();
			if (sbuCase.getCaseProject()== null
					|| sbuCase.getCaseProject().getId() == null) {
				iterator.remove();
				continue;
			}
			sbuCase.setSbu(sbu);
			SystemDict caseProjcet = systemDictService.find(sbuCase.getCaseProject()
					.getId());
			
			sbuCase.setCaseProject(caseProjcet);
			
		}
		
		save(sbu);
	}

	@Override
	public List<Map<String, Object>> findShippingMethodSbu(Long id) {
		return sbuDao.findShippingMethodSbu(id);
	}
	
	@Override
	public List<Map<String, Object>> findPcRoleSbu(Long id) {
		return sbuDao.findPcRoleSbu(id);
	}
	
	@Override
	public List<Map<String, Object>> findPolicySbu(Long id) {
		return sbuDao.findPolicySbu(id);
	}
	
	@Override
	public List<Map<String, Object>> findInvoiceSbu(Long id) {
		return sbuDao.findInvoiceSbu(id);
	}
	
	@Override
	public List<Map<String, Object>> findCashSbu(Long id) {
		return sbuDao.findCashSbu(id);
	}
	
	
}
