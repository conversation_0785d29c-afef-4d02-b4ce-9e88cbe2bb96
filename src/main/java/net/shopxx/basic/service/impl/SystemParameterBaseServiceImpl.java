package net.shopxx.basic.service.impl;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.basic.dao.SystemParameterBaseDao;
import net.shopxx.basic.entity.SystemParameter;
import net.shopxx.basic.service.SystemParameterBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("systemParameterBaseServiceImpl")
public class SystemParameterBaseServiceImpl extends BaseServiceImpl<SystemParameter> implements SystemParameterBaseService {
	@Resource(name = "systemParameterBaseDao")
	private SystemParameterBaseDao systemParameterBaseDao;

	@Transactional(readOnly = true)
	public String getValue(Long companyInfoId, String code) {
		String value = this.systemParameterBaseDao.getValue(companyInfoId, code);
		return value;
	}

	@Transactional
	public void updateSystemParameter(SystemParameter systemParameter) {
		update(systemParameter, "companyInfoId");
	}
	
	@Transactional
	@Override
	public void updateSystemParameter(String code,String value) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", code));
		SystemParameter systemParameter = this.find(filters);
		systemParameter.setValue(value);
		this.update(systemParameter);
	}

	@Transactional(readOnly = true)
	public SystemParameter getAdminParam(String code) {
		return this.systemParameterBaseDao.getAdminParam(code);
	}
}
