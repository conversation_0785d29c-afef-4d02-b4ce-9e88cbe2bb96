package net.shopxx.basic.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.basic.dao.MemberRankSaleOrgDao;
import net.shopxx.basic.entity.MemberRankSaleOrg;
import net.shopxx.basic.service.MemberRankSaleOrgService;

@Service("memberRankSaleOrgServiceImpl")
public class MemberRankSaleOrgServiceImpl extends BaseServiceImpl<MemberRankSaleOrg> implements MemberRankSaleOrgService{
	
	@Resource(name = "memberRankSaleOrgDao")
	private MemberRankSaleOrgDao memberRankSaleOrgDao;
	
	public List<Map<String, Object>> findList(Long memberRankId){
		return memberRankSaleOrgDao.findList(memberRankId);
	}
	
}
