package net.shopxx.basic.service.impl;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.basic.dao.TotalDateDao;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.entity.TotalDate;
import net.shopxx.basic.service.TotalDateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Service("totalDateServiceImpl")
public class TotalDateServiceImpl extends BaseServiceImpl<TotalDate> implements TotalDateService{
	
	@Resource(name = "totalDateDao")
	private TotalDateDao totalDateDao;

	@Override
	public Page<Map<String, Object>> findPage(String sn,Boolean isEnabled,
			Long[] saleOrgId,Long[] sbuId,Long[] organizationId,String startTime,
			String endTime,Pageable pageable) {
		return totalDateDao.findPage(sn, isEnabled, saleOrgId, sbuId, organizationId, 
				startTime, endTime, pageable);
	}

	@Override
	public List<Map<String, Object>> findTotalDateList(Boolean isEnabled, Long saleOrgId, 
			Long sbuId, Long[] organizationId, Date billDate,SystemDict totalDateType) {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		if(ConvertUtil.isEmpty(saleOrgId)){
			throw new RuntimeException("机构不能为空");
		}
		if(ConvertUtil.isEmpty(sbuId)){
			throw new RuntimeException("sbu不能为空");
		}
		if(ConvertUtil.isEmpty(organizationId) || organizationId.length==0){
			throw new RuntimeException("经营组织不能为空");
		}
		if(ConvertUtil.isEmpty(billDate)){
			throw new RuntimeException("单据日期或者GL日期不能为空");
		}
		return totalDateDao.findTotalDateList(isEnabled, saleOrgId, sbuId, organizationId, format.format(billDate).toString(),totalDateType);
	}
	
	/**
	 * 失效操作
	 */
	@Override
	@Transactional
	public void invalid(TotalDate totalDate) {
		totalDate.setIsEnabled(false);
		update(totalDate);
	}

}
