package net.shopxx.basic.service.impl;

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.basic.dao.MemberRankSbuDao;
import net.shopxx.basic.entity.MemberRankSbu;
import net.shopxx.basic.service.MemberRankSbuService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : MemberRankSbuServiceImpl
 * @Deseription : 操作价格类型和SBU的中间表
 * <AUTHOR> LanTianLong
 * @Date : 2020/10/14 10:52
 * @Version : 1.0
 **/
@Service("memberRankSbuServiceImpl")
public class MemberRankSbuServiceImpl extends BaseServiceImpl<MemberRankSbu> implements MemberRankSbuService {

    @Resource(name="memberRankSbuDao")
    private MemberRankSbuDao memberRankSbuDao;

    @Override
    public List<Map<String, Object>> findList(Long memberRankId) {
        return memberRankSbuDao.findList(memberRankId);
    }


}
