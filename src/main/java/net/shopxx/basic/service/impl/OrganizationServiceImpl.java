package net.shopxx.basic.service.impl;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.basic.dao.OrganizationDao;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.OrganizationService;
@Service("organizationServiceImpl")
public class OrganizationServiceImpl extends BaseServiceImpl<Organization>
		implements OrganizationService {

	@Resource(name = "organizationDao")
	private OrganizationDao organizationDao;
		
	@Override
	public Page<Map<String, Object>> findPage(String startTime, String endTime, 
			Pageable pageable, String name, String code, Long isEnabled, Long type,
			Integer sign) {
		return organizationDao.findPage(startTime, endTime, pageable, name, code, isEnabled, type, sign);
	}
}
