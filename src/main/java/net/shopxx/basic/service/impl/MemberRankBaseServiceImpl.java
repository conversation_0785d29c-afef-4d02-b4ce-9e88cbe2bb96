package net.shopxx.basic.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.dao.MemberRankBaseDao;
import net.shopxx.basic.service.MemberRankBaseService;

/**
 * Service - 价格类型
 * 
 * <AUTHOR> Team
 * @version 3.0
 *
 */
@Service("memberRankBaseServiceImpl")
public class MemberRankBaseServiceImpl extends BaseServiceImpl<MemberRank>
		implements MemberRankBaseService {

	@Resource(name = "memberRankBaseDao")
	private MemberRankBaseDao memberRankBaseDao;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "sbuServiceImpl")
    private SbuService sbuService;
	@Resource(name = "saleOrgBaseServiceImpl")
    private  SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;

	@Override
	@Transactional(readOnly = true)
	public MemberRank findDefault(Long companyInfoId) {
		return memberRankBaseDao.findDefault(companyInfoId);
	}

	@Override
	@Transactional
	public void save(MemberRank memberRank) {

		if (memberRank.getIsDefault()) {
			memberRankBaseDao.setDefault(null);
		}
		super.save(memberRank);
	}

	@Override
	@Transactional
	public MemberRank update(MemberRank memberRank) {

		if (memberRank.getIsDefault()) {
			memberRankBaseDao.setDefault(memberRank.getId());
		}
		return super.update(memberRank);
	}

	@Override
	@Transactional
	public MemberRank update(MemberRank memberRank,
			String... ignoreProperties) {

		if (memberRank.getIsDefault()) {
			memberRankBaseDao.setDefault(memberRank.getId());
		}
		return super.update(memberRank, ignoreProperties);
	}

	@Override
	@Transactional
	public Map<String, Object> memberRankImport(MultipartFile multipartFile)
			throws Exception {

		Map<String, Object> map = new HashMap<String, Object>();
		StringBuilder msg = new StringBuilder();
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}
		String name; //价格类型名称
		String grade;//级别
		String defaultStr;//是否默认
		String enabledStr;//是否启用
        String sbu;//SBU
        String saleOrg;//机构
        String rankType;

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		for (int i = 1; i < rows; i++) {

			cell = sheet.getCell(0, i);
			name = cell.getContents().trim();
			if (ConvertUtil.isEmpty(name)) {
				msg.append("第" + (i + 1) + "行，" + "价格类型名称未填<br>");
				continue;
			}
			if (count(Filter.eq("name", name),
					Filter.eq("companyInfoId", companyInfoId)) > 0) {
				msg.append("第"
						+ (i + 1)
						+ "行，"
						+ "价格类型名称为["
						+ name
						+ "]的价格类型已存在<br>");
				continue;
			}

			cell = sheet.getCell(1, i);
			grade = cell.getContents().trim();
			if(grade==""){
                grade= null;
            }
//			if (ConvertUtil.isEmpty(grade)) {
//				msg.append("第" + (i + 1) + "行，" + "级别未填<br>");
//				continue;
//			}
			else {
				if (count(Filter.eq("grade", Integer.parseInt(grade)),
						Filter.eq("companyInfoId", companyInfoId)) > 0) {
					msg.append("第" + (i + 1) + "行，" + "级别[" + grade + "]已存在<br>");
					continue;
				}
			}
			cell = sheet.getCell(2, i);
			defaultStr = cell.getContents().trim();
			if (ConvertUtil.isEmpty(defaultStr)) {
				msg.append("第" + (i + 1) + "行，" + "是否默认未填<br>");
				continue;
			}
			cell = sheet.getCell(3, i);
			enabledStr = cell.getContents().trim();
			if (ConvertUtil.isEmpty(enabledStr)) {
				msg.append("第" + (i + 1) + "行，" + "是否启用未填<br>");
				continue;
			}

            cell = sheet.getCell(4, i);
            saleOrg = cell.getContents().trim();


            cell = sheet.getCell(5, i);
            sbu = cell.getContents().trim();
            
            cell = sheet.getCell(6, i);
			rankType=cell.getContents().trim();
			if (StringUtils.isEmpty(rankType)) {
				msg.append("第" + i + "行,类型区分为空");
				continue;
			}

			MemberRank memberRank = new MemberRank();
			memberRank.setName(name ==" "? null:name);
			if(grade != null){
                memberRank.setGrade(Integer.parseInt(grade));
            }
			memberRank.setIsDefault(defaultStr.equals("1") ? true : false);
			memberRank.setIsEnabled(enabledStr.equals("1") ? true : false);
            List<Filter> filter = new ArrayList<Filter>();
            filter.add(Filter.eq("name", sbu));
            Sbu sbu1= sbuService.find(filter);

            filter.clear();
            filter.add(Filter.eq("name", saleOrg));
            SaleOrg saleOrg1= saleOrgBaseService.find(filter);
            
            filter.clear();
            filter.add(Filter.eq("value", rankType));
            filter.add(Filter.eq("code", "rankType"));
			List<SystemDict> rankTypes = systemDictService.findList(null, filter, null);
			SystemDict rankTypeId =rankTypes.get(0);

			List<MemberRankSaleOrg> memberRankSaleOrgs= new ArrayList<MemberRankSaleOrg>();
			MemberRankSaleOrg memberRankSaleOrg=new MemberRankSaleOrg();
			memberRankSaleOrg.setSaleOrg(saleOrg1);
			memberRankSaleOrg.setMemberRank(memberRank);
			memberRankSaleOrgs.add(memberRankSaleOrg);
			memberRank.setMemberRankSaleOrg(memberRankSaleOrgs);

			List<MemberRankSbu> memberRankSbus= new ArrayList<MemberRankSbu>();
			MemberRankSbu memberRankSbu=new MemberRankSbu();
			memberRankSbu.setSbu(sbu1);
			memberRankSbu.setMemberRank(memberRank);
			memberRankSbus.add(memberRankSbu);
			memberRank.setMemberRankSbu(memberRankSbus);
			memberRank.setRankType(rankTypeId);
			save(memberRank);

			success++;
		}
		int result = rows - 1;

		map.put("result", result);
		map.put("success", success);
		map.put("msg", msg.toString());

		return map;
	}
	
	public Page<Map<String, Object>> findPage(String name, String firstTime, String lastTime,
			Integer isShow, Long saleOrgId, Pageable pageable){
		return memberRankBaseDao.findPage(name, firstTime, lastTime, isShow, saleOrgId, pageable);
	};
	
	@Override
	public void MSave(MemberRank memberRank) {
		List<MemberRankSaleOrg> memberRankSaleOrg = memberRank.getMemberRankSaleOrg();
		for(Iterator<MemberRankSaleOrg> iterator = memberRankSaleOrg.iterator();iterator.hasNext();){
			MemberRankSaleOrg mks = (MemberRankSaleOrg)iterator.next();
			if(mks.getMemberRank()==null&&mks.getSaleOrg()==null){
				iterator.remove();
				continue;
			}
			mks.setMemberRank(memberRank);
		}
		memberRank.setMemberRankSaleOrg(memberRankSaleOrg);

		List<MemberRankSbu> memberRankSbu = memberRank.getMemberRankSbu();
		for(Iterator<MemberRankSbu> iterator = memberRankSbu.iterator();iterator.hasNext();){
			MemberRankSbu mks = (MemberRankSbu)iterator.next();
			if(mks.getMemberRank()==null&&mks.getSbu()==null){
				iterator.remove();
				continue;
			}
			mks.setMemberRank(memberRank);
		}
		memberRank.setMemberRankSbu(memberRankSbu);

		memberRankBaseService.save(memberRank);
		
	};
	
	@Override
	public void MUpdate(MemberRank memberRank,String str){
		List<MemberRankSaleOrg> memberRankSaleOrg = memberRank.getMemberRankSaleOrg();
		for(Iterator<MemberRankSaleOrg> iterator = memberRankSaleOrg.iterator();iterator.hasNext();){
			MemberRankSaleOrg mks = (MemberRankSaleOrg)iterator.next();
			if(mks.getMemberRank()==null&&mks.getSaleOrg()==null){
				iterator.remove();
				continue;
			}
			mks.setMemberRank(memberRank);
		}
		memberRank.setMemberRankSaleOrg(memberRankSaleOrg);

		List<MemberRankSbu> memberRankSbu = memberRank.getMemberRankSbu();
		for(Iterator<MemberRankSbu> iterator = memberRankSbu.iterator();iterator.hasNext();){
			MemberRankSbu mks = (MemberRankSbu)iterator.next();
			if(mks.getMemberRank()==null&&mks.getSbu()==null){
				iterator.remove();
				continue;
			}
			mks.setMemberRank(memberRank);
		}
		memberRank.setMemberRankSbu(memberRankSbu);

		memberRankBaseService.update(memberRank,str);
	}
	
	@Override
	public List<Map<String, Object>> findPageList(Long saleOrgId,Long sbuId,String name, Integer grade) {
		return memberRankBaseDao.findPageList(saleOrgId,sbuId,name,grade);
	}
	
}