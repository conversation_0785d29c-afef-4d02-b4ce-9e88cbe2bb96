package net.shopxx.basic.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.basic.dao.AreaDao;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.service.AreaBaseService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service - 地区
 */
@Service("areaBaseServiceImpl")
public class AreaBaseServiceImpl extends BaseServiceImpl<Area> implements
		AreaBaseService {

	@Resource(name = "areaDao")
	private AreaDao AreaDao;

	@Override
	@Transactional(readOnly = true)
	public List<Area> findRoots(String locale) {
		return AreaDao.findRoots(locale, 0);
	}

	@Override
	@Transactional(readOnly = true)
	public Area findAreaByFullName(String fullName) {
		return AreaDao.findAreaByFullName(fullName);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findAreaPage(String name, String fullName,
			Pageable pageable) {
		return AreaDao.findAreaPage(name, fullName, pageable);
	}
	
	@Override
	@Transactional(readOnly = true)
	public Area findAreaByFullNameDD(String fullName) {
		return AreaDao.findAreaByFullName(fullName);
	}
	
}