package net.shopxx.basic.service.impl;
import java.util.List;
import java.util.Map;

import net.shopxx.basic.dao.SystemDictDao;
import org.springframework.stereotype.Service;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SystemDictBaseService;

import javax.annotation.Resource;

@Service("systemDictBaseServiceImpl")
public class SystemDictBaseServiceImpl extends BaseServiceImpl<SystemDict>
		implements SystemDictBaseService {

	@Resource(name = "systemDictDao")
	private SystemDictDao systemDictDao;

	@Override
	public SystemDict systemBillType(List<Filter> filters) {
		filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("code", "systemType"));
		filters.add(Filter.eq("isEnabled", true));
		SystemDict systemType = find(filters);
		return systemType;
	}

	@Override
	public List<Map<String, Object>> getSystemBillTypeByCode(String code) {
		return systemDictDao.findSystemDictByCode(code);
	}

    @Override
    public List<SystemDict> findSystemDictList(String code, String value) {
        return systemDictDao.findSystemDictList(code,value);
    }
}
