package net.shopxx.basic.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.basic.dao.SaleOrgBaseDao;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.SaleOrgBaseService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service - 组织
 *
 */
@Service("saleOrgBaseServiceImpl")
public class SaleOrgBaseServiceImpl extends BaseServiceImpl<SaleOrg> implements
		SaleOrgBaseService {

	@Resource(name = "saleOrgBaseDao")
	private SaleOrgBaseDao saleOrgBaseDao;

	@Override
	@Transactional(readOnly = true)
	public List<SaleOrg> findTree(String name, Boolean isEnabled) {
		return saleOrgBaseDao.findChildren(name, isEnabled, null, 0);
	}

	@Override
	@Transactional(readOnly = true)
	public List<SaleOrg> findChildren(String name, Boolean isEnabled,
			SaleOrg saleOrg) {
		return saleOrgBaseDao.findChildren(name, isEnabled, saleOrg, 0);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findList(String saleOrgName,
			String saleOrgSn,Integer isSellSaleOrg, Boolean isEnabled,Integer isSelect,Pageable pageable) {
		return saleOrgBaseDao.findList(saleOrgName,
				saleOrgSn,
				isSellSaleOrg,
				isEnabled,
				isSelect,
				pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findChildren(Long parentId) {
		return saleOrgBaseDao.findChildren(parentId);
	}

	@Override
	@Transactional(readOnly = true)
	public SaleOrg findByStoreMember(Long storeMemberId) {
		return saleOrgBaseDao.findByStoreMember(storeMemberId);
	}

	@Override
	@Transactional
	public void deleteSaleOrg(SaleOrg saleOrg) {
		SaleOrg parent = saleOrg.getParent();
		delete(saleOrg);
		if (parent != null
				&& (parent.getChildren() == null || parent.getChildren().size() < 2)) {
			parent.setIsLeaf(true);
			update(parent);
		}
	}

	@Override
	public List<Map<String, Object>> findfactory(String name, Pageable pageable) {
		return saleOrgBaseDao.findfactory(name, pageable);
	}

	@Override
	public Page<Map<String, Object>> findSelectSaleOrgPage(String saleOrgName, 
			String saleOrgSn, Boolean isEnabled, Pageable pageable) {
		
		return saleOrgBaseDao.findSelectSaleOrgPage(saleOrgName, saleOrgSn,
				isEnabled, pageable);
	}
}
