package net.shopxx.basic.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.MemberRankSaleOrg;

import org.springframework.web.multipart.MultipartFile;

/**
 * Service - 会员等级
 */
public interface MemberRankBaseService extends BaseService<MemberRank> {

	/**
	 * 根据企业id查找默认会员等级
	 * @param companyInfoId
	 * @return
	 */
	public MemberRank findDefault(Long companyInfoId);
	
	/**
	 * excel导入会员等级
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> memberRankImport(MultipartFile multipartFile)
			throws Exception;
	
	/**
	 * 列表查询
	 * */
	public Page<Map<String, Object>> findPage(String name, String firstTime, String lastTime,
			Integer isShow, Long saleOrgId, Pageable pageable);
	
	/**
	 * 保存方法
	 * */
	public void MSave(MemberRank memberRank);
	
	/**
	 * 更新方法
	 * */
	public void MUpdate(MemberRank memberRank,String str);
	
	/**价格类型查询*/
	public List<Map<String, Object>> findPageList(Long saleOrgId,Long sbuId,String name, Integer grade);
}