package net.shopxx.basic.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.SystemDict;

public interface SystemDictBaseService extends BaseService<SystemDict> {
	
	//处理指定类型的单据类别 0-出仓 1-入仓
	
	/**
	 * 系统单据类型
	 * @param filters
	 * @return
	 */
	public SystemDict systemBillType(List<Filter> filters);

	/**
	 * 根据编码获取系统参数
	 * @param code
	 * @return
	 */
	public List<Map<String,Object>> getSystemBillTypeByCode(String code);

    /**
     * 根据编码获取系统词汇
     * @param code
     * @param value
     * @return
     */
    public List<SystemDict> findSystemDictList(String code,String value);
}
