package net.shopxx.basic.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.Area;

/** 
 * Service - 地区
 * 
 * <AUTHOR> Team
 * @version 3.0
 */
public interface AreaBaseService extends BaseService<Area> {

	/**
	 * 查找顶级地区
	 * @param count
	 * @return
	 */
	public List<Area> findRoots(String locale);

	public Area findAreaByFullName(String fullName);

	public Page<Map<String, Object>> findAreaPage(String name, String fullName,
			Pageable pageable);
	
	public Area findAreaByFullNameDD(String fullName);
}