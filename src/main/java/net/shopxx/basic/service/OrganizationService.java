package net.shopxx.basic.service;
import java.util.Map;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.Organization;
public interface OrganizationService extends BaseService<Organization> {
	
	public Page<Map<String, Object>> findPage(String startTime, String endTime,
			Pageable pageable, String name, String code,Long isEnabled, Long type,
			Integer sign);

}
