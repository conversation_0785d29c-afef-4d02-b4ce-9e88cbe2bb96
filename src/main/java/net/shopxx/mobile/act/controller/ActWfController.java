package net.shopxx.mobile.act.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.entity.ActWfObjConfig;
import net.shopxx.act.service.ActWfObjConfigService;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

@Controller("mobileActWfController")
@RequestMapping("/mobile/act/wf")
public class ActWfController extends BaseController {

	@Resource(name = "actWfObjConfigServiceImpl")
	private ActWfObjConfigService actWfObjConfigService;

	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	/**
	* 列表 
	*/
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Integer flag, ModelMap model) {
		model.addAttribute("flag", flag);
		return "/mobile/act/wf/list";
	}

	/**
	* 列表
	*/
	@ResponseBody
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public ResultMsg list_data(Integer flag, Long objType, String wfName,
			Integer stat, Pageable pageable) {

		Page<Map<String, Object>> page = actWfService.findPage(flag,
				String.valueOf(WebUtils.getCurrentStoreMemberId()),
				objType,
				wfName,
				stat,
				WebUtils.getCurrentCompanyInfoId(),
				pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	* 列表
	*/
	@RequestMapping(value = "/wf", method = RequestMethod.GET)
	public String wf(Long wfid, HttpServletRequest request, ModelMap model) {

		ActWf wf = actWfService.find(wfid);
		model.addAttribute("wf", wf);
		

		StoreMember storeMember = storeMemberBaseService.getCurrent();
		String userid = storeMember.getId().toString();

		if (wf != null) {
			if (userid == null) {
				userid = String.valueOf(WebUtils.getCurrentStoreMemberId());
			}
			String processInstanceId = wf.getProcInstId();
			HistoricProcessInstance instance = actWfService
					.getHistoricProcessInstanceByProcessInstanceId(
							processInstanceId);
			Task task = actWfService
					.getCurrTaskByProcessInstanceId(processInstanceId, userid);
			//当前任务id
			model.addAttribute("taskId", task == null ? null : task.getId());
			//流程名称
			model.addAttribute("wfName", instance.getName());
			//流程明细
			model.addAttribute("wfProcs",
					actWfService.getWfProcList(wf.getProcInstId()));
			//流程图
			model.addAttribute("image",
					actWfService.generateImage(wf.getProcInstId(), request));
			
			ActWfObjConfig actWfObjConfig = null;
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("objTypeId", wf.getObjType()));
			filters.add(Filter.eq("companyInfoId", wf.getCompanyInfoId()));
			List<ActWfObjConfig> actWfObjConfigs = actWfObjConfigService.findList(null, filters, null);
			if(actWfObjConfigs.size()>0){
				actWfObjConfig = actWfObjConfigs.get(0);
			}
			String murl = actWfObjConfig.getMurl();
			model.addAttribute("murl", murl);
			
			
			//用户是否可提交
			model.addAttribute("canComplete",
					actWfService.canCompleteByUser(userid, wf));
			//用户是否可驳回
			model.addAttribute("canReject",
					actWfService.canRejectByUser(userid, wf));
			//用户是否可中断
			model.addAttribute("canInterrupt",
					actWfService.canInterruptByUser(userid, wf));
		}

		return "/mobile/act/wf/wf";
	}

}
