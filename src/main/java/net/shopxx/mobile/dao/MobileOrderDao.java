package net.shopxx.mobile.dao;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;

@Repository("mobileOrderDao")
public class MobileOrderDao extends DaoCenter  {

    public List<Map<String, Object>> findWfList(Integer flag, Long userid) {
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        sql.append(
                "select distinct w.*, o.url,s.name starter_name,l.name last_user_name, p.proc_name curr_proc_name,st.name store_name from wf w");
        sql.append(" left join wf_obj_config o on w.obj_type = o.obj_type_id");
        sql.append(" left join xx_store_member s on w.starter = s.id");
        sql.append(" left join xx_store_member l on w.last_user = l.id");
        sql.append(" left join wf_proc p on w.curr_proc_id = p.id");
        sql.append(" left join xx_store st on w.store = st.id");
        sql.append(" where 1 = 1");
        if (companyInfoId != null) {
            sql.append(" and w.company_info_id = ?");
            sql.append(" and o.company_info_id = ?");
            list.add(companyInfoId);
            list.add(companyInfoId);
        }

        if (flag != null) {
            if (flag == 0) {
                sql.append(" and w.stat in (1, 2, 3)");
                sql.append(" and w.starter = ?");
                list.add(userid);
            } else if (flag == 1) {
                sql.append(" and w.stat in (1, 3)");
                sql.append(
                        " and exists (select 1 from wf_procuser where wf_id = w.id and wf_proc = w.curr_proc_id and store_member = ?)");
                list.add(userid);
            } else if (flag == 2) {
                sql.append(" and w.stat = 1");
                sql.append(
                        " and exists (select 1 from wf_procuser where wf_id = w.id and is_signed = 2 and store_member = ?)");
                list.add(userid);
            } else if (flag == 3) {
                sql.append(" and w.stat = 2");
                sql.append(" and exists (select 1 from wf_procuser where wf_id = w.id and store_member = ?)");
                list.add(userid);
            }
        }

        sql.append(" order by w.last_time desc");
        Object[] objs = new Object[list.size()];

        for (int i = 0; i < list.size(); ++i) {
            objs[i] = list.get(i);
        }
        
        return this.getNativeDao().findListMap(sql.toString(), objs, 0);
    }

    public List<Map<String, Object>> findItemByProductNew(Long productId, String specialOfferNo, Long productCategoryId, Long typeSystemDictId,
            Long storeId,String businessTypeName,Integer productGrade,Object[] saleOrgId,Long soId,Long organizationId) {
        List<Object> list = new ArrayList<Object>();
        Date now = new Date();
        SimpleDateFormat form1 = new SimpleDateFormat("yyyy-MM-dd");
        String sql = "select pai.*,pa.sn,pa.type,p.name product_name,pc.name product_category_name,e.sn e_sn,e.name e_name,di.value type_name "
                + " from xx_price_apl_item pai "
                + " left join xx_price_apl pa on pai.price_apply=pa.id"
                + " left join xx_sale_org so on pa.sale_org=so.id"
                + " left join xx_product p on pai.product=p.id "
                + " left join xx_product_category pc on pai.product_category=pc.id"
                + " left join xx_engineering e on e.id=pa.engineering"
                + " left join xx_system_dict di on di.id = pa.type"
                + " left join xx_organization ot on ot.id = pa.organization"
                + " where (pai.product=? or (pai.product is null and pai.product_category=?))"
                + " and ot.id = ?  and pai.shipping_warehouse=?  and pa.start_date<=? and pa.end_date>=? and pai.doc_status=2 and pai.product_level=?";
                //+ " and pai.shipping_warehouse=? and pa.store=? and pa.start_date<=? and pa.end_date>? and pai.doc_status=2 ";
        list.add(productId);
        list.add(productCategoryId);
        list.add(organizationId);
        list.add(typeSystemDictId);
        list.add(now);
        list.add(form1.format(now)+" 23:59:59");
        list.add(productGrade);
        if("商业地板".equals(businessTypeName)){    
            sql+=" and pa.type=3";
        }   
        if(saleOrgId!=null&&saleOrgId.length>0){
            String os = "";
            for (int i = 0; i < saleOrgId.length; i++) {
                if (i == saleOrgId.length - 1)
                    os += saleOrgId[i].toString();
                else
                    os += saleOrgId[i].toString() + ",";
            }
            sql += " and ((so.id IN ("+os+") and pa.store is null) OR pa.store = ? and so.id = ?)";
            list.add(storeId);
            list.add(soId.toString());
        }
        
        if (!ConvertUtil.isEmpty(specialOfferNo)) {
            sql += " and pa.sn like ? ";
            list.add("%" + specialOfferNo + "%");
        }
        
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        List<Map<String, Object>> listMap = getNativeDao().findListMap(sql, objs, 0);
        
        return listMap;
    }

}
