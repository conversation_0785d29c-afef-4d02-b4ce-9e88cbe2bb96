package net.shopxx.mobile.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.product.entity.ProductCategory;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Dao - app商品
 */
@Repository("appProductDao")
public class AppProductDao extends DaoCenter {

    public Page<Map<String, Object>> newFindPage(Pageable pageable, Long productCategoryId, Boolean isNationwide, Boolean isGeneral, Boolean isCustomization, String modelOrName, Long companyInfoId) {

        StringBuffer sql = new StringBuffer();//sql
        List<Object> params = new ArrayList<Object>();//参数
        sql.append("select * from xx_product where 1=1 ");
        //拼接条件查询sql
        sql.append(" and company_info_id = ? ");
        params.add(companyInfoId);

        if (productCategoryId != null) {
            //sql.append(" and product_category = ?");
            sql.append("and product_category in (select id FROM xx_product_category WHERE parent = ? or product_category = ? )");
            params.add(productCategoryId);
            params.add(productCategoryId);
        }

        if (isNationwide != null) {
            sql.append(" and is_nationwide = ? ");
            params.add(isNationwide);
        } else if (isGeneral != null) {
            sql.append(" and is_general = ? ");
            params.add(isGeneral);
        } else if (isCustomization != null) {
            sql.append(" and is_customization = ? ");
            params.add(isCustomization);
        } else {
            sql.append(" and ( is_nationwide = true or is_general = true or is_customization = true) ");
        }

        if (modelOrName != null) {
            sql.append(" and (model like ? OR name like ? ) ");
            params.add("%" + modelOrName + "%");
            params.add("%" + modelOrName + "%");
        }

        Page<Map<String, Object>> pageMap = getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);

        return pageMap;
    }

    public Page<Map<String, Object>> newFindPage2(Pageable pageable, Long productCategoryId, Integer shopProductStates, String modelOrName, Long companyInfoId) {
        List<Object> params = new ArrayList<Object>();//参数

        String sql = "select p.*,pc.name product_category_name,sd.value brandname"
                + " from xx_product p "
                + " left join xx_product_category pc on p.product_category = pc.id "
                + " left join xx_system_dict sd on p.brand = sd.id "
                + "where 1=1";

        if (companyInfoId != null) {
            sql += " and p.company_info_id = ?";
            params.add(companyInfoId);
        }
        if (productCategoryId != null) {
            sql += " and (pc.id = ? or pc.tree_path like ?)";
            params.add(productCategoryId);
            params.add("%"
                    + ProductCategory.TREE_PATH_SEPARATOR
                    + productCategoryId
                    + ProductCategory.TREE_PATH_SEPARATOR
                    + "%");
        }


        if (shopProductStates != null) {
            sql += " and p.shop_product_states = ?";
            params.add(shopProductStates);
        } else {
            sql += " and p.shop_product_states IS NOT NULL ";
        }


        if (modelOrName != null) {
            sql += " and (p.model like ? OR p.name like ? ) ";
            params.add("%" + modelOrName + "%");
            params.add("%" + modelOrName + "%");
        }
//        if (modelOrName != null) {
//            sql.append(" and name like ? ");
//            params.add("%" + modelOrName + "%");
//        }

        Page<Map<String, Object>> pageMap = getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);

        return pageMap;

    }


//    public Page<Map<String, Object>> newFindPage2(Pageable pageable, Long productCategoryId, Integer shopProductStates, String modelOrName, Long companyInfoId) {
//        StringBuffer sql = new StringBuffer();//sql
//        List<Object> params = new ArrayList<Object>();//参数
//
//        sql.append("select * from xx_product where 1=1 ");
//        //拼接条件查询sql
//        sql.append(" and company_info_id = ? ");
//        params.add(companyInfoId);
//
//        //两级分类   如果分类存在三级以上需要修改
//        if (productCategoryId != null) {
//            //sql.append(" and product_category = ?");
//            sql.append(" and product_category in (select id FROM xx_product_category WHERE parent = ? or product_category = ? )");
//            params.add(productCategoryId);
//            params.add(productCategoryId);
//        }
//
//        if (shopProductStates != null) {
//            sql.append(" and shop_product_states = ?");
//            params.add(shopProductStates);
//        } else {
//            sql.append(" and shop_product_states IS NOT NULL ");
//        }
//
//
//        if (modelOrName != null) {
//            sql.append(" and (model like ? OR name like ? ) ");
//            params.add("%" + modelOrName + "%");
//            params.add("%" + modelOrName + "%");
//        }
////        if (modelOrName != null) {
////            sql.append(" and name like ? ");
////            params.add("%" + modelOrName + "%");
////        }
//
//        Page<Map<String, Object>> pageMap = getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);
//
//        return pageMap;
//
//    }


    public List<Map<String, Object>> findShopInfoByAreaManager(Long postId, Long companyInfoId, Long storeMemberId) {
        StringBuffer sql = new StringBuffer();//sql
        List<Object> params = new ArrayList<Object>();//参数

        //查找区域经理id、name和shopInfo所有列
        sql.append("SELECT storeMember.name AS areaManager,shopinfo.*,storeMember.id AS areaManagerId FROM xx_shop_info AS shopinfo ");
        sql.append("LEFT JOIN xx_store AS store ON shopinfo.store = store.id ");
        sql.append(" LEFT JOIN xx_store_member_sale_org_post AS smsop ON smsop.store_member = store.store_member ");
        sql.append("LEFT JOIN xx_store_member AS storeMember ON storeMember.id = smsop.store_member ");
        //条件：岗位id，用户id
        sql.append("WHERE 1=1 AND smsop.post = ? ");
        params.add(postId);
        sql.append("AND smsop.store_member = ? ");
        params.add(storeMemberId);
        //
        sql.append(" AND smsop.company_info_id = ? ");
        params.add(companyInfoId);
        //开店中、正在营业的门店
        //sql.append(" AND shopinfo.shop_status = ? ");
        sql.append(" AND s.shop_status in ( ? , ? ) ");
        params.add("开店中");
        params.add("正在营业");

        List<Map<String, Object>> listMap = getNativeDao().findListMap(sql.toString(), params.toArray(), 0);

        return listMap;
    }


    /**
     * 门店上样客户权限
     *
     * @return
     */
    public List<Map<String, Object>> findShopInfoBySaleOrgAndStoreMemberType(StoreMember storeMember, Long companyInfoId, String storeAuth) {
        Long currentStoreMemberId = storeMember.getId();
        StringBuffer sql = new StringBuffer();//sql
        List<Object> params = new ArrayList<Object>();//参数
        sql.append("select s.*,a.full_name area_name,so.name as saleOrgName,so.id AS saleOrgId,storeMember.name as areaManagerName,storeMember.id as areaManagerId "
                + " from xx_shop_info s "
                + " left join xx_area a on s.area = a.id"
                + " left join xx_store st on st.id = s.store"
                + " left join xx_sale_org so on so.id = st.sale_org"
                + " left join xx_store_member storeMember ON storeMember.id = st.store_member "
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and s.company_info_id = ?");
            params.add(companyInfoId);
        }


        //开店中、正在营业的门店
//        sql.append(" AND s.shop_status in ( ? , ? ) ");
//        params.add("开店中");
//        params.add("正在营业");


        if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
            if (storeAuth != null) {
                sql.append(" and (s.store in (" + storeAuth + ") or st.store_member = ?) ");
                params.add(WebUtils.getCurrentStoreMemberId());
            }
        } else {
            sql.append(" and (s.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = ?) "
                    + " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
                    + " where a.tree_path like concat('%,', b.id, ',%') "
                    + " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = ?)))");
            params.add(currentStoreMemberId);
            params.add(currentStoreMemberId);
        }
        sql.append(" order by s.create_date desc");

        List<Map<String, Object>> listMap = getNativeDao().findListMap(sql.toString(), params.toArray(), 0);

        return listMap;
    }


    /**
     * 查询门店上样分类
     * @param shopProductCategoryId
     * @param isEnable
     * @return
     */
    public List<Map<String, Object>> findShopProductCategory(Long[] shopProductCategoryId, Boolean isEnable) {

        StringBuffer sql = new StringBuffer();//sql
        List<Object> params = new LinkedList<Object>();//参数


        sql.append("SELECT spc.id AS shopProductCategoryId,spc.`name` AS shopProductCategoryName,");
        sql.append(" pc.id as productCategoryId,pc.`name` AS productCategoryName ");
        sql.append(" FROM xx_shop_product_category spc ");
        sql.append(" LEFT JOIN xx_shop_product_and_product_category spapc ON spc.id = spapc.shop_product_category ");
        sql.append(" LEFT JOIN xx_product_category pc ON pc.id = spapc.product_category ");
        sql.append(" WHERE 1=1 ");

        if (shopProductCategoryId != null && shopProductCategoryId.length > 0) {
            sql.append(" AND spc.id in ( ");
            for (int i = 0; i < shopProductCategoryId.length; i++) {
                sql.append(" ? ");
                if (i < shopProductCategoryId.length - 1) {
                    sql.append(" , ");
                }
                params.add(shopProductCategoryId[i]);
            }
            sql.append(" ) ");
        }

        if (isEnable != null) {
            sql.append(" AND spc.is_enabled = ? ");
            params.add(isEnable);
        }

        sql.append(" AND spc.company_info_id = ? ");
        params.add(WebUtils.getCurrentCompanyInfoId());

        return getNativeDao().findListMap(sql.toString(), params.toArray(), 0);

    }
}
