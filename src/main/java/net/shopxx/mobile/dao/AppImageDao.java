package net.shopxx.mobile.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;

@Repository("appImageDao")
public class AppImageDao extends DaoCenter {

    public Page<Map<String, Object>> findPage(String name, Boolean isEnabled, Integer type, Date firstTime,
            Date lastTime, Pageable pageable) {
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append(" select id, company_info_id, create_date, image, is_enabled, name, sort, type, url, icon_type "
                + " from xx_app_image " + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and company_info_id = ?");
            list.add(companyInfoId);
        }
        if (type != null) {
            sql.append(" and type = ?");
            list.add(type);
        }
        if (StringUtils.isNotEmpty(name)) {
            sql.append(" and name like ? ");
            list.add("%" + name + "%");
        }
        if (isEnabled != null) {
            if (isEnabled)
                sql.append(" and is_enabled = 1 ");
            else
                sql.append(" and is_enabled = 0 ");
        }
        sql.append(" order by create_date desc ");

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);

        return page;
    }
    
    public Page<Map<String, Object>> findPageIcom(String name, Boolean isEnabled, Integer type, Date firstTime,
            Date lastTime, Pageable pageable) {
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append(" select ai.*, s.id sbu_id, s.name sbu_name "
                + " from xx_app_image ai " 
                + " left join xx_sbu s on s.id = ai.icon_type " 
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and ai.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (type != null) {
            sql.append(" and ai.type = ?");
            list.add(type);
        }
        if (StringUtils.isNotEmpty(name)) {
            sql.append(" and ai.name like ? ");
            list.add("%" + name + "%");
        }
        if (isEnabled != null) {
            if (isEnabled)
                sql.append(" and ai.is_enabled = 1 ");
            else
                sql.append(" and ai.is_enabled = 0 ");
        }
        sql.append(" order by ai.create_date desc ");

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);

        return page;
    }

    public List<Map<String, Object>> findImage(Integer count) {
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append("select id, create_date, image, is_enabled, name, sort, type, url " 
                + " from xx_app_image "
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and company_info_id = ?");
            list.add(companyInfoId);
        }
        sql.append(" and type = 0 ");
        sql.append(" and is_enabled = 1 ");
        sql.append(" order by date_format(create_date, '%Y-%m-%d') desc, sort desc ");
        if (count != null && (count >= 0 && count <= 8)) {
            sql.append(" limit ? ");
            list.add(count);
        } else {
            sql.append(" limit 8 ");
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        List<Map<String, Object>> appImage = getNativeDao().findListMap(sql.toString(), objs, 0);
        return appImage;
    }

    public List<Map<String, Object>> findIcon(Integer iconType) {
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append("select id, create_date, icon_type, image, name, url " 
                + " from xx_app_image "
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and company_info_id = ?");
            list.add(companyInfoId);
        }
        sql.append(" and type = 1 ");
        sql.append(" and is_enabled = 1 ");
        if (iconType != null) {
            sql.append(" and icon_type = ?");
            list.add(iconType);
        }
        sql.append(" order by create_date desc ");
        sql.append(" limit 1 ");

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        List<Map<String, Object>> appImage = getNativeDao().findListMap(sql.toString(), objs, 0);
        return appImage;
    }

}
