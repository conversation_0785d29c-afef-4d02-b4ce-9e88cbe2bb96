package net.shopxx.mobile.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.DateUtil;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

@Repository("planDao")
public class PlanDao extends DaoCenter {

    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;

    /**
     * 获取已读或未读看板
     *
     * @param param
     * @param pageable
     * @return
     */
    public Page<Map<String, Object>> selectUnreadAndReadPlan(Map<String, Object> param, Pageable pageable) {
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StoreMember storeMember = storeMemberService.getCurrent();
        sql.append("select p.id, p.create_date, p.like_count, p.plan_time, p.view_count, p.store_member "
                + " from xx_plan p "
                + " left join xx_plan_sign ps on ps.plan = p.id and ps.type = 1 and ps.store_member = ? "
                + " left join xx_store_member sm on sm.id = p.store_member "
                + " left join xx_store_member_sale_org smso on smso.store_member = sm.id "
                + " left join xx_sale_org so on so.id = smso.sale_org "
                + " left join xx_cc_people cp on cp.plan = p.id"
                + " where 1 = 1 ");
        list.add(storeMember.getId());
        if (companyInfoId != null) {
            sql.append(" and p.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!StringUtils.isEmpty(param.get("name"))) {
            sql.append(" and sm.name like ?");
            list.add("%" + param.get("name").toString() + "%");
        }
//        if (!StringUtils.isEmpty(param.get("date"))) {
//            sql.append(" and DATE_FORMAT(p.create_date, '%Y-%m-%d') >= ?");
//            list.add(param.get("date").toString());
//        }

        if (!StringUtils.isEmpty(param.get("startTime"))) {
            sql.append(" and p.create_date > ? ");
            list.add(param.get("startTime"));
        }
        if (!StringUtils.isEmpty(param.get("endTime"))) {
            sql.append(" and p.create_date < ? ");
            list.add(DateUtil.convert(param.get("endTime") + " 23:59:59"));
        }


        if ("unread".equals(param.get("isRead").toString())) {
            sql.append(" and ps.id is null ");  // 过滤已读的，保留未读的
        } else if ("read".equals(param.get("isRead").toString())) {
            sql.append(" and ps.id is not null ");  // 过滤未读的，保留已读的
        }

        if (param.get("soid") != null) {
            sql.append(" and so.id in (" + param.get("soid").toString() + ")");  // 取机构
            sql.append(" and p.store_member <> ? ");
            list.add(storeMember.getId());
        }
        // 指定抄送人的
        sql.append(" or cp.cc_people = ? ");
        list.add(storeMember.getId());
        // 再进行一次过滤
        if ("unread".equals(param.get("isRead").toString())) {
            sql.append(" and ps.id is null ");
        } else if ("read".equals(param.get("isRead").toString())) {
            sql.append(" and ps.id is not null ");
        }
        if (!StringUtils.isEmpty(param.get("name"))) {
            sql.append(" and sm.name like ?");
            list.add("%" + param.get("name").toString() + "%");
        }
        if (!StringUtils.isEmpty(param.get("date"))) {
            sql.append(" and DATE_FORMAT(p.create_date, '%Y-%m-%d') >= ?");
            list.add(param.get("date").toString());
        }
        sql.append(" group by p.id ");
        sql.append(" order by p.id desc ");

//        LogUtils.info("planDaoSql: " + sql.toString());
//        LogUtils.info("objs: " + list.toString());

        //return this.getNativeDao().findListMap(sql.toString(), list.toArray(), 0);
        Page<Map<String, Object>> page = this.getNativeDao().findPageMap(sql.toString(), list.toArray(), pageable);

        String totalsql = "select count(1) from ( " + sql + ") a";
        long total = getNativeDao().findInt(totalsql, list.toArray());
        page.setTotal(total);
        return page;
    }

    public List<Map<String, Object>> selectSalesman(Map<String, Object> param) {
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append(" select s.id, s.company_info_id, s.name, s.username, s.is_enabled, s.address, m.mobile " +
                " from xx_store_member s " +
                " left join xx_member m on s.member = m.id " +
                " left join xx_store ss on s.store = ss.id " +
                " left join xx_store_member_sale_org smso on s.id = smso.store_member " +
                " where ss.is_main_store = 1 " +
                " and s.company_info_id = ? " +
                " and ss.company_info_id = ? " +
                " and smso.company_info_id = ? " +
                " and s.member_type = 0" +
                " and s.is_enabled = 1");
        list.add(companyInfoId);
        list.add(companyInfoId);
        list.add(companyInfoId);
        if (!StringUtils.isEmpty(param.get("name"))) {
            sql.append(" and s.name like ?");
            list.add("%" + param.get("name").toString() + "%");
        }
        if (!StringUtils.isEmpty(param.get("orgId"))) {
            sql.append(" and smso.sale_org = ? ");
            list.add(param.get("orgId").toString());
        }
        sql.append(" group by s.id order by s.id desc");
        return this.getNativeDao().findListMap(sql.toString(), list.toArray(), 0);
    }

}
