package net.shopxx.mobile.b2b.controller;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import net.shopxx.basic.entity.Sbu;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.entity.PlanApply;
import net.shopxx.order.service.PlanApplyService;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
/**
 * 移动端计划提报：经销商、经销商直报
 * 
 *
 */
@Controller("b2bMobilePlanApplyNotToErpController")
@RequestMapping("/mobile/b2b/planApplyNotToErp")
public class MobilePlanApplyNotToErpController extends BaseController {

	@Resource(name = "planApplyServiceImpl")
	private PlanApplyService planApplyService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long objTypeId, Long objid, Pageable pageable,
			ModelMap model) {
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		return "/mobile/b2b/plan_apply_not_to_erp/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(ModelMap model) {
		return "/mobile/b2b/plan_apply_not_to_erp/form_query";
	}

	
	/**
	 * 计划提报：经销商、经销商直报查询
	 * @param sn
	 * @param status
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn,Long[] status,Pageable pageable) {
		Page<Map<String, Object>> page = planApplyService.appFindPage(sn,status,pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	
	
	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Integer planApplyType,ModelMap model) {
		//获取ModelMap
		planApplyService.getModelMap(model,planApplyType,"planApplyBusinessType",
				null,null,"productDictRoles","moistureContent");
		String businessTypeName = "";
		Member member = storeMemberService.getCurrent().getMember();
		List<StoreMember> storeMemberList = storeMemberService.findNotDefaultByMember(member);
		if (!storeMemberList.isEmpty() && storeMemberList.size() > 0) {
			for (StoreMember storeMember : storeMemberList) {
				if(!ConvertUtil.isEmpty(storeMember.getStore())){
					Store store = storeMember.getStore();
					if (store.getType().equals(Store.Type.distributor)) {
						model.addAttribute("store", store);
						//机构
						if(!ConvertUtil.isEmpty(store.getSaleOrg())){
							model.addAttribute("saleOrg", store.getSaleOrg());
						}
						//业务类型
						if(!ConvertUtil.isEmpty(store.getBusinessType())){
							businessTypeName = store.getBusinessType().getValue();
						}
						break;
					}
				}
			}
		}
		model.addAttribute("businessTypeName", businessTypeName);
		//sbu
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Map<String, Object>> sbu = storeMemberService.findSbuTy(storeMember.getId());
		if (sbu.size() > 0) {
			Long sbuIds = Long.parseLong(sbu.get(0).get("id").toString());
			model.addAttribute("sbuIds", sbuIds);
		}

		return "/mobile/b2b/plan_apply_not_to_erp/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(PlanApply planApply,Long saleOrgId,Integer confirmStatus) {
		planApply.setPlanApplyType(0);
		if(!ConvertUtil.isEmpty(confirmStatus)){
			planApply.setStatus(confirmStatus);
		}else{
			planApply.setStatus(0);
		}
		planApplyService.saveOrUpdatePlanApply(planApply,saleOrgId,null,null,null,null,null);
		return success().addObjX(planApply.getId());
	}

	/**
	 * 查看
	 */
	@RequestMapping(value = "/form_detail", method = RequestMethod.GET)
	public String edit(Long id, Integer isCheck, ModelMap model) {
		PlanApply planApply = planApplyService.find(id);
		model.addAttribute("planApply", planApply);
		//获取ModelMap
		planApplyService.getModelMap(model,planApply.getPlanApplyType(),
				"planApplyBusinessType",null,null,"productDictRoles","moistureContent");
		List<Map<String, Object>> mapList = planApplyService.findItemById(id.toString());
		model.addAttribute("mapList", JsonUtils.toJson(mapList));
		//查询所用户所包含的所有sbu
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> storeMemberSbus = storeMemberSbuService.findList(null,
				filters,
				null);
		List<Sbu> sbuList = new ArrayList<Sbu>();
		if(storeMemberSbus!=null && storeMemberSbus.size()>0){
			for( StoreMemberSbu  sms: storeMemberSbus){
				sbuList.add(sms.getSbu());
			}
		}
		model.addAttribute("sbuList", sbuList);
		return "/mobile/b2b/plan_apply_not_to_erp/form_detail";
	}

    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
        PlanApply planApply = planApplyService.find(id);
        if (planApply.getStatus() != 0) {
            return error("只有单据状态为已保存计划提报单才能审批流程");
        }
        planApplyService.createWf(id, modelId, objTypeId);
        return success();
    }



}
