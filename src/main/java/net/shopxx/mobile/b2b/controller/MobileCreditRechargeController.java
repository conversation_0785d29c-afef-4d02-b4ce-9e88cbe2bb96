package net.shopxx.mobile.b2b.controller;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.*;
import net.shopxx.member.service.*;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 移动端授信
 * 
 *
 */
@Controller("b2bMobileCreditRechargeController")
@RequestMapping("/mobile/b2b/creditRecharge")
public class MobileCreditRechargeController extends BaseController {

	@Resource(name = "creditRechargeServiceImpl")
	private CreditRechargeService creditRechargeService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "creditAttachServiceImpl")
	private CreditAttachService creditAttachService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;





	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long objTypeId, Long objid, Pageable pageable,
			ModelMap model) {
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		return "/mobile/b2b/credit_recharge/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Integer rechargeType,
                       Integer flag, Long sbuId, Pageable pageable, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(46L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("rechargeType", rechargeType);
		model.addAttribute("sbuId", sbuId);


		/**
		 * 用户经营组织权限
		 */
		List<Filter> filters = new ArrayList<Filter>();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						if(organization!=null){
							organizationList.add(organization);
						}
					}
				}
			}
		}else{
			//经营组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			organizationList = organizationService.findList(null,filters,null);
		}
		model.addAttribute("organizations", organizationList);
		return "/mobile/b2b/credit_recharge/form_query";
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Integer rechargeType,Long sbuId,Integer flag, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("rechargeType", rechargeType);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("startDate", DateUtil.getCurrDateStr("yyyy-MM-dd"));
		model.addAttribute("endDate",
				df.format(DateUtil.getLastMonthDay(new Date())));
		Store store = null;
		Member member = storeMemberService.getCurrent().getMember();
		List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
		if (storeMembers != null) {
			for (StoreMember storeMember1 : storeMembers) {
				store = storeMember1.getStore();
				if (store.getType().equals(Store.Type.distributor)) {
					model.addAttribute("store", store);
					break;
				}
				else {
					store = null;
				}
			}
		}

		/**
		 * 用户经营组织权限
		 */
		List<Filter> filters = new ArrayList<Filter>();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						if(organization!=null){
							organizationList.add(organization);
						}
					}
				}
			}
		}else{
			//经营组织
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			organizationList = organizationService.findList(null,filters,null);

		}
		model.addAttribute("managementOrganizations", organizationList);

		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}

		}



		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);

        filters.clear();
        filters.add(Filter.eq("code", "creditType"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> creditTypes = systemDictService.findList(null,
                filters,
                null);
        model.addAttribute("creditTypes", creditTypes);

//		Map<String, Object> bal = storeBalanceService.findBalanceSbu();
//
//		if (bal.get("balance") != null) {
//			model.addAttribute("balance", bal.get("balance"));
//		}
//
//		model.addAttribute("balance", balance);
		//查询所有sbu


//		List<Map<String,Object>> sbu = storeMemberService.findSbuTy(storeMember.getId());
//		if(sbu.size()>0){
//		Long sbuIds = Long.parseLong(sbu.get(0).get("id").toString());
//			model.addAttribute("sbuIds",
//					sbuIds);
//
//		}
//
//
//		filters.clear();
//		filters.add(Filter.eq("storeMember", storeMember.getId()));
//		List<StoreMemberSbu> sbus=storeMemberSbuService.findList(null, filters, null);
//		model.addAttribute("sbus",
//				sbus);

		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles);
		}
		catch (RuntimeException e) {

		}

		return "/mobile/b2b/credit_recharge/add";
	}





	/**
	 * 查看
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(String code, Integer rechargeType,
					   Long id, Integer flag, ModelMap model) {

		CreditRecharge creditRecharge = creditRechargeService.find(id);
		model.addAttribute("cr", creditRecharge);
		model.addAttribute("flag", flag);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("rechargeType", rechargeType);
		/**付款单*/
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(creditRecharge.getSn(),
				8));
		model.addAttribute("payment_json", payment_json);

//		/**全链路*/
//		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(creditRecharge.getSn(),
//				8));
//		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(46L);
		model.addAttribute("isCheckWf", isCheckWf);

		String twContractAttach_json = JsonUtils.toJson(creditAttachService.findListByItemId(null,
				id));
		model.addAttribute("twContractAttach_json", twContractAttach_json);

		/**
		 * 用户经营组织权限
		 */
		List<Filter> filters = new ArrayList<Filter>();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						if(organization!=null){
							organizationList.add(organization);
						}
					}
				}
			}
		}else{
			//经营组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			organizationList = organizationService.findList(null,filters,null);

		}
		model.addAttribute("managementOrganizations", organizationList);

		model.addAttribute("code", code);

        filters.clear();
        filters.add(Filter.eq("code", "creditType"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> creditTypes = systemDictService.findList(null,
                filters,
                null);
        model.addAttribute("creditTypes", creditTypes);

		Map<String, Object> bal = storeBalanceService.findBalanceSbu(creditRecharge.getStore().getId(),creditRecharge.getSbu().getId(),creditRecharge.getOrganization().getId(),creditRecharge.getSaleOrg().getId());


		if (bal!=null && bal.get("balance") != null) {
			model.addAttribute("balance", bal.get("balance"));
		}

		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles);
		}
		catch (RuntimeException e) {

		}


		//授信作废，只有角色-财务才能作废
		boolean jurisdiction = creditRechargeService.findJurisdiction(storeMember);
		model.addAttribute("jurisdiction", jurisdiction);

		return "/mobile/b2b/credit_recharge/edit";
	}




}
