package net.shopxx.mobile.b2b.controller;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.*;
import net.shopxx.member.service.*;
import net.shopxx.order.entity.PriceApply;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.PriceApplyService;
import net.shopxx.order.service.TwContractAttachService;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller("mobilePriceApplyController")
@RequestMapping("/b2b/mobile/price_apply")
public class MobilePriceApplyController extends BaseController {
	
	@Resource(name = "priceApplyServiceImpl")
	private PriceApplyService priceApplyService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "twContractAttachServiceImpl")
	private TwContractAttachService twContractAttachService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	
	/*
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(ModelMap model) {
		return "/mobile/b2b/price_apply/list";
	}
	
	/**
	 * 新增页
	 * 
	 * @return
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String list_tb(ModelMap model, Long sbuId) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> shippingWarehouses = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("shippingWarehouses", shippingWarehouses);

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("isMember",storeMember.getMemberType());
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		Store store = null;
		Member member = storeMemberService.getCurrent().getMember();
		List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
		if (storeMembers != null) {
			for (StoreMember sm : storeMembers) {
				store = sm.getStore();
				if (store.getType().equals(Store.Type.distributor)) {
					model.addAttribute("store", store);
					break;
				}
				else {
					store = null;
				}
			}
		}

		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);

		filters.clear();
		filters.add(Filter.eq("code", "specialType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> specialType = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("specialTypes", specialType);
		
		/**
		 * 用户经营组织权限
		 */
//		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
//		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
//		filters.clear();
//		filters.add(Filter.eq("storeMember",storeMemberId));
//		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
//		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
//		int storeMemberOrganization = 0;
//		if(userRoles!=null&&userRoles.size()>0){
//			String[] perRole = value.split(",");
//			List<String> perRoleList = Arrays.asList(perRole);
//			for (PcUserRole userRole : userRoles) {
//				if (perRoleList.contains(userRole.getPcRole().getName())) {
//					storeMemberOrganization++;
//					break;
//				}
//			}
//		}
		
		List<Organization> organizationList = null;
//		if(storeMemberOrganization==0){
//			filters.clear();
//			filters.add(Filter.eq("storeMember", storeMemberId));
//			filters.add(Filter.eq("companyInfoId", companyInfoId));
//			organizationList = new ArrayList<Organization>();
//			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
//			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
//				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
//					if(storeMemberOrganiZation!=null){
//						 Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
//						 if(organization!=null){
//							 organizationList.add(organization);
//						 }
//					}
//				}
//			}
//		}else{
			//组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			organizationList = organizationService.findList(null, filters, null);
			
//		}
		model.addAttribute("organizations", organizationList);
		
		//产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
		model.addAttribute("productLevelList", productLevelList);
		return "/mobile/b2b/price_apply/add";
	}
	
	/*
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, String sn,Integer isCheck, ModelMap model) {
		List<Filter> filters = new ArrayList<Filter>();
		model.addAttribute("isCheck", isCheck);
		PriceApply priceApply = new PriceApply();
		if (id != null) {
			priceApply = priceApplyService.find(id);
			model.addAttribute("priceApply", priceApply);
			List<Map<String, Object>> list = priceApplyService.findItemListByApplyIds(id.toString());
			
			String jsonStr = JsonUtils.toJson(priceApplyService.findOrderItem(list));
			model.addAttribute("jsonStr", jsonStr);
		}else {
			filters.clear();
			filters.add(Filter.eq("sn", sn));
			priceApply = priceApplyService.find(filters);
			if (priceApply != null) {
				model.addAttribute("priceApply", priceApply);
				id = priceApply.getId();
				List<Map<String, Object>> list = priceApplyService.findItemListByApplyIds(priceApply.getId()
						.toString());
				
				String jsonStr = JsonUtils.toJson(list);
				model.addAttribute("jsonStr", jsonStr);
			}
		}
		priceApply.getId();
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(42L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> shippingWarehouses = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("shippingWarehouses", shippingWarehouses);

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("isMember",storeMember.getMemberType());
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		filters.clear();
		filters.add(Filter.eq("code", "specialType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> specialType = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("specialTypes", specialType);
		
		
		
		/**
		 * 用户经营组织权限
		 */
//		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
//		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
//		filters.clear();
//		filters.add(Filter.eq("storeMember",storeMemberId));
//		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
//		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
//		int storeMemberOrganization = 0;
//		if(userRoles!=null&&userRoles.size()>0){
//			String[] perRole = value.split(",");
//			List<String> perRoleList = Arrays.asList(perRole);
//			for (PcUserRole userRole : userRoles) {
//				if (perRoleList.contains(userRole.getPcRole().getName())) {
//					storeMemberOrganization++;
//					break;
//				}
//			}
//		}
//		
		List<Organization> organizationList = null;
//		if(storeMemberOrganization==0){
//			filters.clear();
//			filters.add(Filter.eq("storeMember", storeMemberId));
//			filters.add(Filter.eq("companyInfoId", companyInfoId));
//			organizationList = new ArrayList<Organization>();
//			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
//			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
//				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
//					if(storeMemberOrganiZation!=null){
//						 Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
//						 if(organization!=null){
//							 organizationList.add(organization);
//						 }
//					}
//				}
//			}
//		}else{
			//组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			organizationList = organizationService.findList(null, filters, null);
//		}
		model.addAttribute("organizations", organizationList);
		
		
		//产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
		model.addAttribute("productLevelList", productLevelList);
		
		String twContractAttach_json = JsonUtils.toJson(twContractAttachService.findListByItemId(null,
				id));
		model.addAttribute("twContractAttach_json", twContractAttach_json);
		
		return "/mobile/b2b/price_apply/edit";
	}
	
	@RequestMapping(value = "/findStoreMemberRank", method = RequestMethod.POST)
	public @ResponseBody
    ResultMsg findStoreMemberRank(Long storeId, Long sbuId){
		List<Filter> filters = new ArrayList<Filter>();
		Long memberRankId = null;
		if (storeId != null && sbuId != null) {
			Sbu sbu = sbuService.find(sbuId);
			Store store = storeBaseService.find(storeId);
			//获取客户当前默认价格类型
			filters.clear();
			filters.add(Filter.eq("store", store));
			filters.add(Filter.eq("sbu", sbu));
			List<StoreSbu> storeSbu = storeSbuService.findList(null,
					filters,
					null);
			if (storeSbu != null && storeSbu.size() > 0) {
				MemberRank memberRank = storeSbu.get(0).getMemberRank();
				if(memberRank!=null){
					memberRankId = memberRank.getId();					
				}
			}
		}
		return success().addObjX(memberRankId);
	}
	

}
