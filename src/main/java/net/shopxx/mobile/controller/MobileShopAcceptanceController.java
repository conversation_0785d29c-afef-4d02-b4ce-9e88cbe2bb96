package net.shopxx.mobile.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.shop.entity.AcceptanceReimburse;
import net.shopxx.shop.entity.ShopDevise;
import net.shopxx.shop.service.AcceptanceReimburseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.shop.service.ShopReimburseService;
import net.shopxx.wf.service.WfBaseService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description 移动端H5--门店装修及验收
 * <AUTHOR>
 * @Date2020/10/14 10:23
 * @Place:Beijiao
 * @Version V1.0
 **/
@Controller("mobileShopReimburseController")
@RequestMapping("/mobile/shop/acceptance")
public class MobileShopAcceptanceController extends BaseController {
    @Resource(name = "shopReimburseServiceImpl")
    private ShopReimburseService shopReimburseService;
    @Resource(name = "acceptanceReimburseServiceImpl")
    private AcceptanceReimburseService acceptanceReimburseService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Autowired
    private SystemDictBaseService systemDictBaseService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
    @Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        boolean isStore = false;
        boolean isSalesman = false;
        if (storeMember.getMemberType() == 0) {
            if (storeMemberSaleOrgPostService.checkPostCodeByStoreMember(storeMember, "1001")
                    || storeMemberSaleOrgPostService.checkPostCodeByStoreMember(storeMember, "1006")) {
                isSalesman = true;
            }
        } else {
            isStore = true;
        }

        //获取机构区域
        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("code", "saleOrgRegion"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,filters,null);
        model.addAttribute("saleOrgRegions", saleOrgRegions);


        model.addAttribute("isSalesman", isSalesman);
        model.addAttribute("isStore", isStore);
        model.addAttribute("wfStates", wfBaseService.getAllWfStates());
        return "/mobile/shop/acceptance/list";
    }

    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(
            String sn, String deviseSn, Integer[] status,Long shopInfoId,Long storeId,Long saleOrgId,Long storeMemberId,
            String acceptanceShop,Integer region,String firstTime,String lastTime, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(2);               // type
        param.add(sn);              // 单号
        param.add(status);          //状态
        param.add(deviseSn);        //设计单号
        param.add(shopInfoId);      //门店
        param.add(storeId);         //客户
        param.add(saleOrgId);       //机构
        param.add(storeMemberId);   //区域经理
        param.add(acceptanceShop);  //装修属性
        param.add(region);          //区域
        param.add(firstTime);       //创建时间开始
        param.add(lastTime);        //创建时间结束

        Page<Map<String, Object>> page = acceptanceReimburseService.findAcceptancePage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }


    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        return "/mobile/shop/acceptance/add";
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        AcceptanceReimburse acceptanceReimburse = acceptanceReimburseService.find(id);
        ShopDevise shopDevise = acceptanceReimburse.getShopDevise();
        model.addAttribute("acceptanceReimburse", acceptanceReimburse);
        model.addAttribute("shopDevise", shopDevise);
        // 验收申请承诺项
        List<String> acList = new ArrayList<String>();
        if (!StringUtils.isEmpty(acceptanceReimburse.getAcceptanceCommitment())) {
            String[] strs = acceptanceReimburse.getAcceptanceCommitment().split(",");
            acList = Arrays.asList(strs);
        }
        model.addAttribute("ac", acList);
        // 验收人员核实项
        /*List<String> avList = new ArrayList<String>();
        if (!StringUtils.isEmpty(acceptanceReimburse.getAcceptanceVerify())) {
            String[] strs = acceptanceReimburse.getAcceptanceVerify().split(",");
            avList = Arrays.asList(strs);
        }
        model.addAttribute("av", avList);*/
        // 初始化验收申请承诺附件
        List<Map<String, Object>> acceptanceCommitmentAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 0);
        // 门店装修验收照片附件
        List<Map<String, Object>> shopFarAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 4);
        List<Map<String, Object>> shopDecorateAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 1);
        List<Map<String, Object>> payPlatformAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 2);
        List<Map<String, Object>> plateAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 3);
        model.addAttribute("acceptanceCommitmentAttachs", JsonUtils.toJson(acceptanceCommitmentAttachs));
        model.addAttribute("shopFarAttachs", JsonUtils.toJson(shopFarAttachs));
        model.addAttribute("shopDecorateAttachs", JsonUtils.toJson(shopDecorateAttachs));
        model.addAttribute("payPlatformAttachs", JsonUtils.toJson(payPlatformAttachs));
        model.addAttribute("plateAttachs", JsonUtils.toJson(plateAttachs));


        //================================新流程
        //将当前登录用户岗位返回前台
        StoreMember storeMember = storeMemberBaseService.getCurrent();

        if(acceptanceReimburse.getWfId()!= null){
            ActWf wf = acceptanceReimburseService.getWfByWfId(acceptanceReimburse.getWfId());
            // model.addAttribute("wf", wf);


            if (wf != null) {
                //省长审核
                Boolean szsh = false;
                //渠道专员
                Boolean qdzy = false;
                //省长审核权限
                Boolean szshs = false;
                //渠道专员权限
                Boolean qdzys = false;

                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for(Map<String, Object> c : item){
                    if(c.get("suggestion")!=null){
                        //处理结果
                        String approved = c.get("approved")!=null?c.get("approved").toString():"false";
                        //节点名称
                        String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
                        //对比节点名称是否对应

                        if(rwm.contains("省长")){
                            szsh = Boolean.valueOf(approved);

                        }
                        if(rwm.contains("渠道")){
                            qdzy = Boolean.valueOf(approved);
                            if(!qdzy){
                                szsh = false;
                            }
                        }
                    }
                }

                //获取当前流程所在的节点
                Task t = acceptanceReimburseService.getCurrTaskByWf(wf);
                if(t!=null){
                    //获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());

                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
                        szshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道")){
                        szshs = true;qdzys = true;
                    }
                }
                model.addAttribute("wf", wf);
                model.addAttribute("node", t);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzys", qdzys);
            }
        }

        return "/mobile/shop/acceptance/edit";
    }



}
