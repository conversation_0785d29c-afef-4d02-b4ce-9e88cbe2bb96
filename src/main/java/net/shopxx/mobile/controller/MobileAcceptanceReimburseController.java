package net.shopxx.mobile.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.AcceptanceReimburse;
import net.shopxx.shop.entity.ShopDevise;
import net.shopxx.shop.service.AcceptanceReimburseService;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopDeviseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.wf.service.WfBaseService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description  移动端h5 -----门店装修验收及报销
 * <AUTHOR>
 * @Date2020/10/14 11:32
 * @Place:Beijiao
 * @Version V1.0
 **/
@Controller("mobileAcceptanceReimburseController")
@RequestMapping("/mobile/shop/acceptance_reimburse")
public class MobileAcceptanceReimburseController extends BaseController {
    @Resource(name = "acceptanceReimburseServiceImpl")
    private AcceptanceReimburseService acceptanceReimburseService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "shopDeviseServiceImpl")
    private ShopDeviseService shopDeviseService;
    @Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "shopAddedServiceImpl")
    private ShopAddedService shopAddedService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Autowired
    private SystemDictBaseService systemDictBaseService;


    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        StoreMember current = storeMemberBaseService.getCurrent();
        int memberType = current.getMemberType();
        Boolean isSalesman = current.getIsSalesman();
        if(memberType == 0 && isSalesman){
            //判断是区域经理
            model.addAttribute("storeMemberType","areaManager");
        }else if((memberType == 0)){
            //非经销商非区域经理
            model.addAttribute("storeMemberType","others");
        }
        //获取机构区域
        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("code", "saleOrgRegion"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,filters,null);
        model.addAttribute("saleOrgRegions", saleOrgRegions);

        model.addAttribute("wfStates", wfBaseService.getAllWfStates());
        return "/mobile/shop/acceptance_reimburse/list";
    }

    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        return "/mobile/shop/acceptance_reimburse/add";
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        shopAddedService.findPost(storeMember);
        AcceptanceReimburse acceptanceReimburse = acceptanceReimburseService.find(id);
        ShopDevise shopDevise = acceptanceReimburse.getShopDevise();
        model.addAttribute("acceptanceReimburse", acceptanceReimburse);
        model.addAttribute("shopDevise", shopDevise);
        // 验收申请承诺项
        List<String> acList = new ArrayList<String>();
        if (!StringUtils.isEmpty(acceptanceReimburse.getAcceptanceCommitment())) {
            String[] strs = acceptanceReimburse.getAcceptanceCommitment().split(",");
            acList = Arrays.asList(strs);
        }
        model.addAttribute("ac", acList);
        // 验收人员核实项
        /*
         * List<String> avList = new ArrayList<String>(); if
         * (!StringUtils.isEmpty(acceptanceReimburse.getAcceptanceVerify())) {
         * String[] strs = acceptanceReimburse.getAcceptanceVerify().split(",");
         * avList = Arrays.asList(strs); } model.addAttribute("av", avList);
         */
        // 终端设计经理意见项
        List<String> dmList = new ArrayList<String>();
        if (!StringUtils.isEmpty(acceptanceReimburse.getDesignManager())) {
            String[] strs = acceptanceReimburse.getDesignManager().split(",");
            dmList = Arrays.asList(strs);
        }
        model.addAttribute("dm", dmList);
        // 渠道总监意见项
        List<String> doList = new ArrayList<String>();
        if (!StringUtils.isEmpty(acceptanceReimburse.getDirectorOpinion())) {
            String[] strs = acceptanceReimburse.getDirectorOpinion().split(",");
            doList = Arrays.asList(strs);
        }
        model.addAttribute("do", doList);
        // 初始化验收申请承诺附件
        List<Map<String, Object>> acceptanceCommitmentAttachs = acceptanceReimburseService
                .findAttach(acceptanceReimburse.getId(), 0);
        model.addAttribute("acceptanceCommitmentAttachs", JsonUtils.toJson(acceptanceCommitmentAttachs));

        List<Map<String, Object>> storeContractAttachs = acceptanceReimburseService
                .findAttach(acceptanceReimburse.getId(), 1);
        model.addAttribute("storeContractAttachs", JsonUtils.toJson(storeContractAttachs));

        List<Map<String, Object>> storePictureAttachs = acceptanceReimburseService
                .findAttach(acceptanceReimburse.getId(), 2);
        model.addAttribute("storePictureAttachs", JsonUtils.toJson(storePictureAttachs));


        // 门店装修验收照片附件
        List<Map<String, Object>> d1Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 11);
        List<Map<String, Object>> d2Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 12);
        List<Map<String, Object>> d3Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 13);
        List<Map<String, Object>> d4Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 14);
        List<Map<String, Object>> d5Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 15);
        model.addAttribute("d1Attachs", JsonUtils.toJson(d1Attachs));
        model.addAttribute("d2Attachs", JsonUtils.toJson(d2Attachs));
        model.addAttribute("d3Attachs", JsonUtils.toJson(d3Attachs));
        model.addAttribute("d4Attachs", JsonUtils.toJson(d4Attachs));
        model.addAttribute("d5Attachs", JsonUtils.toJson(d5Attachs));
        //查历史门店报销单 选第一个 ac
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("shopInfo", acceptanceReimburse.getShopInfo()));
        List<AcceptanceReimburse> ac = acceptanceReimburseService.findList(null, filters, null);
        if(ac!=null){
            if(ac.size()>0){
                model.addAttribute("acr", ac.get(0));
            }
        }

        // model.addAttribute("post", shopAddedService.findPost(storeMember));
        // 获取流程节点
        if (acceptanceReimburse.getWfId() != null) {
            // 获取到当前流程
            ActWf wf = shopAddedService.getWfByWfId(acceptanceReimburse.getWfId());
            // 获取当前流程所在的节点
            if (wf != null) {
                // 区域经理
                Boolean qyjl = false;
                Boolean qyjls = false;
                // 省长审核
                Boolean szsh = false;
                Boolean szshs = false;
                // 渠道专员审核
                Boolean qdzy = false;
                Boolean qdzys = false;
                // 设计部审核
                Boolean sjb = false;
                Boolean sjbs = false;
                // 渠道部审核
                Boolean qdbyj = false;
                Boolean qdbyjs = false;
                // 渠道总监审核
                Boolean qdzj = false;
                Boolean qdzjs = false;
                // 销售中心副总审核
                Boolean xszx = false;
                Boolean xszxs = false;
                // 地板事业部总裁审核
                Boolean syb = false;
                Boolean sybs = false;

                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for (Map<String, Object> c : item) {
                    if (c.get("suggestion") != null) {
                        // 处理结果
                        String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
                        // 节点名称
                        String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
                        // 对比节点名称是否对应
                        if (rwm.contains("区域经理")) {
                            // 赋值处理结果到所定义的节点上
                            qyjl = Boolean.valueOf(approved);
                        }
                        if (rwm.contains("省长")){
                            szsh = Boolean.valueOf(approved);
                            // 如果父节点驳回改变上一个节点状态
                            if (!szsh) {
                                qyjl = false;
                            }
                        }
                        if (rwm.contains("渠道专员")) {
                            qdzy = Boolean.valueOf(approved);
                            if (!qdzy) {
                                szsh = false;
                            }
                        }
                        if (rwm.contains("设计部")) {
                            sjb = Boolean.valueOf(approved);
                            if (!sjb){
                                qdzy = false;
                            }
                        }
                        if(rwm.contains("渠道部")){
                            qdbyj = Boolean.valueOf(approved);
                            if(!qdbyj){
                                sjb = false;
                            }
                        }
                        if (rwm.contains("渠道总监")) {
                            qdzj = Boolean.valueOf(approved);
                            if(!qdzj){
                                qdbyj = false;
                            }
                        }
                        if (rwm.contains("销售中心")){
                            xszx = Boolean.valueOf(approved);
                            if(!xszx){
                                qdzj = false;
                            }
                        }
                        if (rwm.contains("事业部")) {
                            syb = Boolean.valueOf(approved);
                            if(!syb){
                                xszx = false;
                            }
                        }
                    }
                }
                // 获取当前流程所在的节点
                Task t = acceptanceReimburseService.getCurrTaskByWf(wf);
                if (t != null) {
                    // 获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("区域经理")) {
                        qyjls = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("省长")) {
                        qyjls = true;
                        szshs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道专员")) {
                        qyjls = true;
                        szshs = true;
                        qdzys = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("设计部")) {
                        qyjls = true;
                        szshs = true;
                        qdzys = true;
                        sjbs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道部")) {
                        qyjls = true;
                        szshs = true;
                        qdzys = true;
                        sjbs = true;
                        qdbyjs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道总监")) {
                        qyjls = true;
                        szshs = true;
                        qdzys = true;
                        sjbs = true;
                        qdbyjs = true;
                        qdzjs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("销售中心")) {
                        qyjls = true;
                        szshs = true;
                        qdzys = true;
                        sjbs = true;
                        qdbyjs = true;
                        qdzjs = true;
                        xszxs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("事业部")) {
                        qyjls = true;
                        szshs = true;
                        qdzys = true;
                        sjbs = true;
                        qdbyjs = true;
                        qdzjs = true;
                        xszxs = true;
                        sybs = true;
                    }
                }
                model.addAttribute("wf", wf);
                model.addAttribute("node", t);
                model.addAttribute("qyjl", qyjl);
                model.addAttribute("qyjls",qyjls );
                model.addAttribute("szsh", szsh);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("qdzys", qdzys);
                model.addAttribute("sjb", sjb);
                model.addAttribute("sjbs", sjbs);
                model.addAttribute("qdbyj", qdbyj);
                model.addAttribute("qdbyjs", qdbyjs);
                model.addAttribute("qdzj", qdzj);
                model.addAttribute("qdzjs", qdzjs);
                model.addAttribute("xszx", xszx);
                model.addAttribute("xszxs", xszxs);
                model.addAttribute("syb", syb);
                model.addAttribute("sybs", sybs);
            }
        }
        return "/mobile/shop/acceptance_reimburse/edit";
    }

    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(
            String sn, String deviseSn, Integer[] status,Long shopInfoId,
            Long storeId,Long saleOrgId,Long storeMemberId,String acceptanceShop,
            Integer region,String afirstTime,String alastTime,String bfirstTime,
            String blastTime,String cfirstTime,String clastTime,String dfirstTime,
            String dlastTime,String createName,String firstTime,String lastTime,
            Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(1);
        param.add(sn);
        param.add(status);
        param.add(deviseSn);
        param.add(shopInfoId);      //门店
        param.add(storeId);         //客户
        param.add(saleOrgId);       //机构
        param.add(storeMemberId);   //区域经理
        param.add(acceptanceShop);  //装修属性
        param.add(region);          //区域
        param.add(afirstTime);      //装修施工开始时间
        param.add(alastTime);       //装修施工结束时间
        param.add(bfirstTime);      //装修完成开始时间
        param.add(blastTime);       //装修完成结束时间
        param.add(cfirstTime);      //审批验收开始时间
        param.add(clastTime);       //审批验收结束时间
        param.add(dfirstTime);      //施工图完成开始时间
        param.add(dlastTime);       //施工图完成结束时间
        param.add(createName);      //创建人
        param.add(firstTime);       //创建日期开始时间
        param.add(lastTime);        //创建日期结束时间
        Page<Map<String, Object>> page = acceptanceReimburseService.findPage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }






}
