package net.shopxx.mobile.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.shop.entity.ShopDevise;
import net.shopxx.shop.service.ShopDeviseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.wf.service.WfBaseService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description 移动端H5----门店设计
 * <AUTHOR>
 * @Date2020/10/13 16:31
 * @Place:Beijiao
 * @Version V1.0
 **/
@Controller("mobileShopDeviseController")
@RequestMapping("/mobile/shop/devise")
public class MobileShopDeviseController extends BaseController {


    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "shopDeviseServiceImpl")
    private ShopDeviseService shopDeviseService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;
    @Autowired
    private SystemDictBaseService systemDictBaseService;
    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;



    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        boolean isStore = false;
        boolean isSalesman = false;
        if (storeMember.getMemberType() == 0) {
            if (storeMemberSaleOrgPostService.checkPostCodeByStoreMember(storeMember, "1001")
                    || storeMemberSaleOrgPostService.checkPostCodeByStoreMember(storeMember, "1006")) {
                isSalesman = true;
            }
        } else {
            isStore = true;
        }


        //获取机构区域
        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("code", "saleOrgRegion"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,filters,null);
        model.addAttribute("saleOrgRegions", saleOrgRegions);


        model.addAttribute("isSalesman", isSalesman);
        model.addAttribute("isStore", isStore);
        model.addAttribute("wfStates", wfBaseService.getAllWfStates());
        return "/mobile/shop/devise/list";
    }

    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(
            ShopDevise shopDevise, String[] attributes, Integer[] status, Integer[] reimburses,
            Long designerId, Long shopInfoId, Long storeId, Long saleOrgId, Long storeMemberId,
            String dfirstTime, String dlastTime, String pfirstTime, String plastTime, String wfirstTime,
            String wlastTime, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(status);      //单号
        param.add(attributes);  //装修属性
        param.add(reimburses);  //是否报销
        param.add(designerId);  //设计师
        param.add(shopInfoId);  //门店
        param.add(storeId);     //客户
        param.add(saleOrgId);   //机构
        param.add(storeMemberId);//区域经理
        param.add(dfirstTime);  //提交设计师开始时间
        param.add(dlastTime);   //提交设计师结束时间
        param.add(pfirstTime);  //平面图完成开始时间
        param.add(plastTime);   //平面图完成结束时间
        param.add(wfirstTime);  //施工图完成开始时间
        param.add(wlastTime);  //施工图完成结束时间
        Page<Map<String, Object>> page = shopDeviseService.findPage(shopDevise,param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }


    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        return "/mobile/shop/devise/add";
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) throws Exception {
        ShopDevise shopDevise = shopDeviseService.find(id);
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        // 上样计划
        List<String> spList = new ArrayList<String>();
        if (!StringUtils.isEmpty(shopDevise.getSamplePlan())) {
            String[] strs = shopDevise.getSamplePlan().split("==");
            spList = Arrays.asList(strs);
        }
        model.addAttribute("sp", spList);
        // 省运营管理中心总经理意见
        List<String> rmoList = new ArrayList<String>();
        if (!StringUtils.isEmpty(shopDevise.getRegionalManagerOpinion())) {
            String[] strs = shopDevise.getRegionalManagerOpinion().split(",");
            rmoList = Arrays.asList(strs);
        }
        model.addAttribute("rmo", rmoList);
        // 地板事业部渠道部
        List<String> fdList = new ArrayList<String>();
        if (!StringUtils.isEmpty(shopDevise.getFloorDivision())) {
            String[] strs = shopDevise.getFloorDivision().split(",");
            fdList = Arrays.asList(strs);
        }
        model.addAttribute("fd", fdList);
        // 经销商提交申请附件
        List<Map<String, Object>> dealersAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 0);
        List<Map<String, Object>> storeContractAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 3);
        List<Map<String, Object>> storePicturesAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 4);
        // 区域经理审核附件
        List<Map<String, Object>> managerDealersAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 1);
        List<Map<String, Object>> managerStoreContractAttachs = shopDeviseService
                .findShopDeviseAttach(shopDevise.getId(), 5);
        List<Map<String, Object>> managerStorePicturesAttachs = shopDeviseService
                .findShopDeviseAttach(shopDevise.getId(), 6);
        // 设计部确认附件
        List<Map<String, Object>> designAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 2);
        model.addAttribute("shopDevise", shopDevise);
        model.addAttribute("dealers_attach", JsonUtils.toJson(dealersAttachs));
        model.addAttribute("store_contract_attachs", JsonUtils.toJson(storeContractAttachs));
        model.addAttribute("store_pictures_attachs", JsonUtils.toJson(storePicturesAttachs));
        model.addAttribute("manager_dealers_attach", JsonUtils.toJson(managerDealersAttachs));
        model.addAttribute("manager_store_contract_attachs", JsonUtils.toJson(managerStoreContractAttachs));
        model.addAttribute("manager_store_pictures_attachs", JsonUtils.toJson(managerStorePicturesAttachs));
        model.addAttribute("design_attach", JsonUtils.toJson(designAttachs));

        // 获取流程节点
        if (shopDevise.getWfId() != null) {
            // 获取到当前流程
            ActWf wf = shopDeviseService.getWfByWfId(shopDevise.getWfId());
            // 获取当前流程所在的节点
            if (wf != null) {
                // 区域经理
                Boolean qyjl = false;
                // 省长审核
                Boolean szsh = false;
                // 渠道专员
                Boolean qdzy = false;
                // 渠道总监
                Boolean qdzj = false;
                // 设计部经理
                Boolean mdsjs = false;
                //设计师
                Boolean designer = false;

                // 区域经理权限
                Boolean qyjls = false;
                // 省长审核权限
                Boolean szshs = false;
                // 渠道专员权限
                Boolean qdzys = false;
                // 渠道总监
                Boolean qdzjs = false;
                // 设计部经理权限
                Boolean mdsjss = false;
                //设计师权限
                Boolean designers = false;

                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for (Map<String, Object> c : item) {
                    if (c.get("suggestion") != null) {
                        // 处理结果
                        String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
                        // 节点名称
                        String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
                        // 对比节点名称是否对应
                        if (rwm.contains("区域经理")) {
                            // 赋值处理结果到所定义的节点上
                            qyjl = Boolean.valueOf(approved);
                        }
                        if (rwm.contains("省长")) {
                            szsh = Boolean.valueOf(approved);
                            // 如果父节点驳回改变上一个节点状态
                            if (!szsh) {
                                qyjl = false;
                            }
                        }
                        if (rwm.contains("渠道专员")) {
                            qdzy = Boolean.valueOf(approved);
                            if (!qdzy) {
                                szsh = false;
                            }
                        }
                        if (rwm.contains("渠道总监")) {
                            qdzj = Boolean.valueOf(approved);
                            if (!qdzj) {
                                qdzy = false;
                            }
                        }
                        if (rwm.contains("设计部")) {
                            mdsjs = Boolean.valueOf(approved);
                            if (!mdsjs) {
                                qdzj = false;
                            }
                        }

                        if(rwm.contains("设计师")){
                            designer = Boolean.valueOf(approved);
                            if(!designer){
                                mdsjs = false;
                            }
                        }
                    }
                }
                Task t = shopDeviseService.getCurrTaskByWf(wf);
                if (t != null) {
                    // 获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("区域经理")) {
                        qyjls = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("省长")) {
                        szshs = true;
                        qyjls = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道专员")) {
                        szshs = true;
                        qyjls = true;
                        qdzys = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道总监")) {
                        szshs = true;
                        qyjls = true;
                        qdzys = true;
                        qdzjs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("设计部")) {
                        szshs = true;
                        qyjls = true;
                        qdzys = true;
                        qdzjs = true;
                        mdsjss = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("设计师")) {
                        szshs = true;
                        qyjls = true;
                        qdzys = true;
                        qdzjs = true;
                        mdsjss = true;
                        designers = true;
                    }
                }
                model.addAttribute("wf", wf);
                model.addAttribute("node", t);
                model.addAttribute("qyjl", qyjl);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("qdzj", qdzj);
                model.addAttribute("mdsjs", mdsjs);
                model.addAttribute("qyjls", qyjls);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzys", qdzys);
                model.addAttribute("qdzjs", qdzjs);
                model.addAttribute("mdsjss", mdsjss);
                model.addAttribute("designer", designer);
                model.addAttribute("designers", designers);
            }
        }
        return "/mobile/shop/devise/edit";
    }



}
