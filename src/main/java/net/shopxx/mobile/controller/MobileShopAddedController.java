package net.shopxx.mobile.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.shop.entity.Shop;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.shop.service.ShopStoreService;
import net.shopxx.wf.service.WfBaseService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description  移动端H5 --门店新增
 * <AUTHOR>
 * @Date2020/10/13 13:37
 * @Place:Beijiao
 * @Version V1.0
 **/
@Controller("mobileShopAddedController")
@RequestMapping("/mobile/shop/added")
public class MobileShopAddedController extends BaseController {
    @Resource(name = "shopAddedServiceImpl")
    private ShopAddedService shopAddedService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name = "shopStoreServiceImpl")
    private ShopStoreService shopStoreService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberPostService;
    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
    @Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;
    @Autowired
    private SbuService sbuService;



    /**
     * 门店新增列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        boolean isStore = false;
        boolean isSalesman = false;
        if (storeMember.getMemberType() == 0) {
            if (storeMemberSaleOrgPostService.checkPostCodeByStoreMember(storeMember, "1001")
                    || storeMemberSaleOrgPostService.checkPostCodeByStoreMember(storeMember, "1006")) {
                isSalesman = true;
            }
        } else {
            isStore = true;
        }
        model.addAttribute("isSalesman", isSalesman);
        model.addAttribute("isStore", isStore);
        model.addAttribute("wfStates", wfBaseService.getAllWfStates());
        return "/mobile/shop/added/list";
    }

    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(Shop shop, Integer[] status, Long storeId, Long saleOrgId, Long storeMemberId, Integer region, String firstShopTime,
                               String lastShopTime, String createName, String firstTime, String lastTime, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(status);//状态
        param.add(storeId);//客户
        param.add(saleOrgId);//机构
        param.add(storeMemberId);//区域经理
        param.add(region);//区域
        param.add(firstShopTime);//建店起始日期
        param.add(lastShopTime);//建店结束日期
        param.add(createName);//创建人
        param.add(firstTime);//创建时间起始日期
        param.add(lastTime);//创建时间结束日期

        Page<Map<String, Object>> page = shopAddedService.findPage2(shop,param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return ResultMsg.success(jsonPage);
    }

    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        Store store = shopStoreService.findStore();
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("store", store));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        List<ShopInfo> shopInfo = shopInfoService.findList(null, filters, null);
        if (shopInfo != null && shopInfo.size() > 0) {
            model.addAttribute("shopInfo", shopInfo.get(0));
        }
        filters.clear();
        filters.add(Filter.eq("code", "businessCategory"));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
        model.addAttribute("BusinessCategory", businessCategoryList);
        model.addAttribute("store", store);
        model.addAttribute("isMember", storeMemberBaseService.getCurrent().getMemberType());
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
        Date da = new Date();
        model.addAttribute("newDate", ft.format(da));

        //获取sbu
        List<Sbu> sbus = sbuService.findAll();
        model.addAttribute("sbus", sbus);

        return "/mobile/shop/added/add";
    }


    /**
     * 编辑
     *
     * @throws Exception
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) throws Exception {
        List<Filter> filters = new ArrayList<Filter>();
        Shop shop = shopAddedService.find(id);
        model.addAttribute("shop", shop);
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        shopAddedService.findPost(storeMember);
        List<Map<String, Object>> shopFarAttach = shopAddedService.findShopAttach1(shop.getId(), 0);
        List<Map<String, Object>> shopDecorateAttach = shopAddedService.findShopAttach1(shop.getId(), 1);
        List<Map<String, Object>> payPlatformAttach = shopAddedService.findShopAttach1(shop.getId(), 2);
        List<Map<String, Object>> plateAttach = shopAddedService.findShopAttach1(shop.getId(), 3);
        filters.clear();
        filters.add(Filter.eq("code", "businessCategory"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
        model.addAttribute("BusinessCategory", businessCategoryList);
        filters.clear();
        filters.add(Filter.eq("code", "shopSign"));
        filters.add(Filter.eq("companyInfoId", companyInfoBaseService.getCurrent().getId()));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> shopSignList = systemDictService.findList(null, filters, null);
        model.addAttribute("shopSign", shopSignList);
        model.addAttribute("shop", shop);
        model.addAttribute("shop_far_attach", JsonUtils.toJson(shopFarAttach));
        model.addAttribute("shop_decorate_attach", JsonUtils.toJson(shopDecorateAttach));
        model.addAttribute("pay_platform_attach", JsonUtils.toJson(payPlatformAttach));
        model.addAttribute("plate_attach", JsonUtils.toJson(plateAttach));

        model.addAttribute("isMember", storeMember.getMemberType());
        // 将当前登录用户岗位返回前台

        //获取sbu
        List<Sbu> sbus = sbuService.findAll();
        model.addAttribute("sbus", sbus);
        // 获取流程节点
        if (shop.getWfId() != null) {
            // 获取到当前流程
            ActWf wf = shopAddedService.getWfByWfId(shop.getWfId());
            if (wf != null) {
                // 区域经理
                Boolean qyjl = false;
                // 省长审核
                Boolean szsh = false;
                // 渠道专员
                Boolean qdzy = false;
                // 区域经理权限
                Boolean qyjls = false;
                // 省长审核权限
                Boolean szshs = false;
                // 渠道专员权限
                Boolean qdzys = false;
                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for (Map<String, Object> c : item) {
                    if (c.get("suggestion") != null) {
                        // 处理结果
                        String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
                        // 节点名称
                        String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
                        // 对比节点名称是否对应
                        if (rwm.contains("区域经理")) {
                            // 赋值处理结果到所定义的节点上
                            qyjl = Boolean.valueOf(approved);
                        }
                        if (rwm.contains("省长")) {
                            szsh = Boolean.valueOf(approved);
                            // 如果父节点驳回改变上一个节点状态
                            if (!szsh) {
                                qyjl = false;
                            }
                        }
                        if (rwm.contains("渠道专员")) {
                            qdzy = Boolean.valueOf(approved);
                            if (!qdzy) {
                                szsh = false;
                            }
                        }
                    }
                }
                // 获取当前流程所在的节点
                Task t = shopAddedService.getCurrTaskByWf(wf);
                if (t != null) {
                    // 获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("区域经理")) {
                        qyjls = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("省长")) {
                        szshs = true;
                        qyjls = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道专员")) {
                        szshs = true;
                        qyjls = true;
                        qdzys = true;
                    }
                }
                model.addAttribute("wf", wf);
                model.addAttribute("node", t);
                model.addAttribute("qyjl", qyjl);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("qyjls", qyjls);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzys", qdzys);

            }
        }
        return "/mobile/shop/added/edit";
    }





}
