package net.shopxx.mobile.controller;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.mobile.service.MobileShopProductService;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.entity.ShopProudct;
import net.shopxx.shop.service.ShopInfoService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 移动端门店上样
 */
@Controller("mobileShopProductController")
@RequestMapping("/mobile/shopProduct")
public class MobileShopProductController extends BaseController {

    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
//    @Resource(name = "mobileProductCategoryServiceimpl")
//    private MobileProductCategoryService mobileProductCategoryService;
//    @Resource(name = "mobileProductServiceImpl")
//    private MobileProductService mobileProductService;
//    @Resource(name = "storeMemberShopInfoServiceImpl")
//    private StoreMemberShopInfoService storeMemberShopInfoService;
    @Resource(name = "mobileShopProductServiceImpl")
    private MobileShopProductService mobileShopProductService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
//    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
//    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
//    @Resource(name = "storeBaseServiceImpl")
//    private StoreBaseService storeBaseService;
//    @Resource(name = "shopProductCategoryServiceImpl")
//    private ShopProductCategoryService shopProductCategoryService;


    /**
     * 门店上样主页
     */
    @RequestMapping(value = "/shop_product", method = RequestMethod.GET)
    public String index(Model model, Long shopInfo) {

        List<Map<String, Object>> resultList = mobileShopProductService.findShopProductCategory(null, true);
        Set<Map<String, Object>> shopProductCategoryList = new HashSet<Map<String, Object>>();
        for (Map<String, Object> map : resultList) {
            Map<String, Object> shopProductCategory = new TreeMap<String, Object>();
            shopProductCategory.put("shopProductCategoryId", map.get("shopProductCategoryId").toString());
            shopProductCategory.put("shopProductCategoryName", map.get("shopProductCategoryName").toString());
            shopProductCategoryList.add(shopProductCategory);
        }

        model.addAttribute("shopProductCategoryList", shopProductCategoryList);
        model.addAttribute("resultList", resultList);
        //门店id
        model.addAttribute("shopInfo", shopInfo);

        return "/mobile/shop_product/list";
    }

    /**
     *  上样列表
     */
    @RequestMapping(value = "/roadlist")
    public String roadlist(Model model,
                           Long productCategoryId,
                           Integer shopProductStates,
                           //Boolean isNationwide,
                           //Boolean isGeneral,
                           //Boolean isCustomization,
                           Long shopInfo
    ) {


        model.addAttribute("shopInfo", shopInfo);
        model.addAttribute("productCategoryId", productCategoryId);
        model.addAttribute("shopProductStates", shopProductStates);
//        model.addAttribute("isNationwide",isNationwide);
//        model.addAttribute("isGeneral",isGeneral);
//        model.addAttribute("isCustomization",isCustomization);
        return "/mobile/shop_product/roadlist";

    }


    /**
     * 上样、首页推荐、商品展示操作
     * @param updateStates
     * @param productId
     * @param shopInfoId
     * @return
     */
    @RequestMapping(value = "/updateStates/{updateStates}")
    public @ResponseBody
    ResultMsg updateStates(
            @PathVariable("updateStates") String updateStates,
            Long productId,
            Long shopInfoId) {

        mobileShopProductService.updateStates(updateStates, productId, shopInfoId);
        return success();
    }


    /**
     * 移动端门店上样
     */
    @RequestMapping(value = "/list_data")
    public @ResponseBody
    ResultMsg list_data(Pageable pageable,
                        Long productCategoryId,//商品分类
                        Long shopProductStates,//移动端门店上样分类
                        String modelOrName,//模型或名字
                        Long shopInfoId
    ) {

        //查询店铺商品
        List<Filter> filters = new ArrayList<Filter>();
        if (shopInfoId == null) ExceptionUtil.throwControllerException("门店不能为空，请选择门店");
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        filters.add(Filter.eq("shopInfo", shopInfo));
        List<ShopProudct> shopProudctList = mobileShopProductService.findList(null, filters, null);

        Page<Map<String, Object>> page = mobileShopProductService.findShopProductPage(pageable,
                productCategoryId,
                shopProductStates,
                modelOrName);

        List<Map<String, Object>> productListMap = page.getContent();
        //处理数据
        processProductList(shopProudctList, productListMap);

        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);

    }

    /**
     * 处理数据
     *
     * @param shopProudctList
     * @param productListMap
     */
    private void processProductList(List<ShopProudct> shopProudctList, List<Map<String, Object>> productListMap) {
        for (Map<String, Object> map : productListMap) {
            for (ShopProudct shopProudct : shopProudctList) {
                if (shopProudct.getProduct().getId().toString().equals(map.get("id").toString())) {
                    map.put("isMarketable", shopProudct.getIsMarketable() == true ? 1 : 0);
                    map.put("isShow", shopProudct.getIsShow() == true ? 1 : 0);
                    map.put("isReferrer", shopProudct.getIsReferrer() == true ? 1 : 0);
                }

            }

        }
        //LogUtils.debug("排序前："+productListMap.toString());
        //排序
        sort(productListMap);
        //LogUtils.debug("排序后："+productListMap.toString());

    }

    //排序
    private void sort(List<Map<String, Object>> productListMap) {


        Collections.sort(productListMap, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                if(o1.get("isMarketable") == null){
                    o1.put("isMarketable",0);
                }

                if(o2.get("isMarketable") == null){
                    o2.put("isMarketable",0);
                }

                int is_marketable1 = Integer.parseInt(o1.get("isMarketable").toString());
                int is_marketable2 = Integer.parseInt(o2.get("isMarketable").toString());

                if(is_marketable1 != is_marketable2){
                    if(is_marketable1 > is_marketable2){
                        return -1;
                    }else {
                        return 1;
                    }
                }
                return 0;

            }
        });
    }




    /**
     * 门店列表
     */
    @RequestMapping(value = "/selectShop", method = RequestMethod.GET)
    public String selectShop(Long shopInfoId, Model model) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        List<Map<String, Object>> shopInfo = mobileShopProductService.findShopInfoByCurrentStoreMember();
        //机构列表
        Set<Map<String, Object>> saleOrgList = new HashSet<Map<String, Object>>();
        for (Map map : shopInfo) {
            Map<String, Object> saleOrg = new HashMap<String, Object>();
            saleOrg.put("id", map.get("saleOrgId"));
            saleOrg.put("name", map.get("saleOrgName"));
            saleOrgList.add(saleOrg);
        }
        model.addAttribute("shops", shopInfo);
        model.addAttribute("saleOrgList", saleOrgList);
        return "/mobile/shop_product/shop_list";
    }

}
