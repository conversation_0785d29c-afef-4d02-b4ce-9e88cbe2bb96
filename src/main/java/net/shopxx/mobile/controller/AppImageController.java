package net.shopxx.mobile.controller;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.mobile.entity.AppImage;
import net.shopxx.mobile.service.AppImageService;

@Controller("appImageController")
@RequestMapping("/mobile/app_image")
public class AppImageController extends BaseController {
    
    @Resource(name = "appImageServiceImpl")
    private AppImageService appImageService;

    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        
        return "/mobile/app_image/add";
    }
    
    /**
     * 切换
     */
    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(String sn, String name, Boolean isEnabled,
            ModelMap model) {

        model.addAttribute("isEnabled", isEnabled);
        return "/mobile/app_image/list_tb";
    }
    
    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, String name, Boolean isEnabled, ModelMap model) {
        return "/mobile/app_image/list";
    }
    
    /**
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/saveImage", method = RequestMethod.POST)
    public ResultMsg saveImage(AppImage appImage) {
        if (appImage.getSort() == null) {
            appImage.setSort(0);
        }
        if (StringUtils.isEmpty(appImage.getImage())) {
            return error("图片不能为空");
        }
        if (appImage.getIsEnabled() == null) {
            appImage.setIsEnabled(false);
        }
        appImageService.save(appImage);
        return success().addObjX(appImage.getId());
    }
    
    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        AppImage appImage = appImageService.find(id);
        model.addAttribute("appImage", appImage);
        return "/mobile/app_image/edit";
    }
    
    /**
     * 更新
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultMsg update(AppImage image) {
        if (image.getId() == null) {
            return error("参数有误，请重新修改");
        }
        AppImage appImage = appImageService.find(image.getId());
        if (!StringUtils.isEmpty(image.getName())) {
            appImage.setName(image.getName());
        }
        if (!StringUtils.isEmpty(image.getImage())) {
            appImage.setImage(image.getImage());
        }
        if (!StringUtils.isEmpty(image.getSort())) {
            appImage.setSort(image.getSort());
        }
        if (image.getIsEnabled() != null && image.getIsEnabled()) {
            appImage.setIsEnabled(true);
        } else {
            appImage.setIsEnabled(false);
        }
        if (!StringUtils.isEmpty(image.getUrl())) {
            appImage.setUrl(image.getUrl());
        }
        appImageService.update(appImage);
        return success();
    }
    
    /**
     * 列表
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(String name, Boolean isEnabled,
            Date firstTime, Date lastTime,Pageable pageable) {
        // 轮播图
        Integer type = 0;
        String jsonPage = JsonUtils.toJson(appImageService.findPage(
                name,
                isEnabled,
                type,
                firstTime,
                lastTime,
                pageable));

        return success(jsonPage);
    }
}
