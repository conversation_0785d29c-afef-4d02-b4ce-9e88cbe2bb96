package net.shopxx.mobile.controller;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.homepage.entity.KnowledgeCategory;
import net.shopxx.homepage.entity.NoticeLikeAndComments;
import net.shopxx.homepage.entity.SystemNotice;
import net.shopxx.homepage.service.KnowledgeCategoryBaseService;
import net.shopxx.homepage.service.NoticeLikeAndCommentsService;
import net.shopxx.homepage.service.SystemNoticeService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

@Controller("mobileKnowledgeController")
@RequestMapping("/mobile/knowledge")
public class KnowledgeController extends BaseController {

    @Resource(name = "knowledgeCategoryBaseServiceImpl")
    private KnowledgeCategoryBaseService knowledgeCategoryBaseService;
    @Resource(name = "systemNoticeServiceImpl")
    private SystemNoticeService systemNoticeService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "noticeLikeAndCommentsServiceImpl")
    private NoticeLikeAndCommentsService noticeLikeAndCommentsService;

    /**
     * 知识库
     * 
     * @return
     */
    @RequestMapping(value = "/repository", method = RequestMethod.GET)
    public String repository(ModelMap model) {
        // 获取知识库顶级类目
        List<Filter> fis = new ArrayList<Filter>();
        fis.add(Filter.eq("isEnabled", true));
        List<KnowledgeCategory> knowledgeCategorys = knowledgeCategoryBaseService.findList(null, fis, null);
        model.addAttribute("knowledgeCategorys", knowledgeCategorys);

        return "/mobile/knowledge/repository";
    }

    @ResponseBody
    @RequestMapping(value = "/list_photo_data", method = RequestMethod.POST)
    public ResultMsg list_photo_data(String title, String store_member_name, Long knowledgeCategoryId,
            Integer isPublic, String knowledgeCategoryName, Integer type, Pageable pageable)  {
        // 设置每页数据量
        pageable.setPageSize(25);
        // 已发布的
        isPublic = 1;
        Page<Map<String, Object>> page = systemNoticeService.findPage(title, store_member_name, isPublic,
                knowledgeCategoryName, type, pageable);
        if (page != null && page.getContent() != null) {
            for (Map<String, Object> item : page.getContent()) {
                // 获取富文本的图片src
                if (!StringUtils.isEmpty(item.get("content"))) {
                    item.put("imgList", getImgStr(item.get("content").toString()));
                }
                // 判断用户是否点赞过该文章
                if (noticeLikeAndCommentsService.exists(Filter.eq("systemNotice", item.get("id")),
                        Filter.eq("storeMember", storeMemberBaseService.getCurrent().getId()),
                        Filter.eq("type", 0), Filter.eq("likeType", 0), Filter.eq("likeStatus", 1))) {
                    item.put("likeStatus", 1);
                } else {
                    item.put("likeStatus", 0);
                }
            }
        }
        String jsonPage = JsonUtils.toJson(page);
        return ResultMsg.success(jsonPage);
    }
    
    /**
     * 点赞/取消点赞知识库
     * @param snId 知识库文章的id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/like", method = RequestMethod.GET)
    public ResultMsg like(Long snId) {
        if (snId == null) {
            return ResultMsg.error("知识库文章的id不能为空！");
        }
        noticeLikeAndCommentsService.saveLikeOrCancel(snId);
        SystemNotice systemNotice = systemNoticeService.find(snId);
        ResultMsg resultMsg = ResultMsg.success();
//        resultMsg.setContent(systemNotice.getLikeCount().toString());
        return resultMsg;
    }
    
    /**
     * 点赞/取消点赞知识库文章的评论
     * @param snId 知识库文章的id
     * @param cId 该条评论的id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/like_comments", method = RequestMethod.GET)
    public ResultMsg likeComments(Long snId, Long cId) {
        if (snId == null) {
            return ResultMsg.error("知识库文章的id不能为空！");
        }
        if (cId == null) {
            return ResultMsg.error("评论的id不能为空！");
        }
        noticeLikeAndCommentsService.saveCommentsLikeOrCancel(snId, cId);
        NoticeLikeAndComments comments = noticeLikeAndCommentsService.find(cId);
        ResultMsg resultMsg = ResultMsg.success();
//        resultMsg.setContent(comments.getCommLikeCount().toString());
        return resultMsg;
    }
    
    /**
     * 评论知识库文章
     * @param snId 文章id
     * @param content 评论内容
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/comments", method = RequestMethod.GET)
    public ResultMsg comments(Long snId, String content) {
        if (snId == null) {
            return ResultMsg.error("知识库文章的id不能为空！");
        }
        if (StringUtils.isEmpty(content.trim())) {
            return ResultMsg.error("评论的内容不能为空！");
        }
        noticeLikeAndCommentsService.saveComments(snId, content);
        return ResultMsg.success();
    }

    /**
     * 文章列表
     * @return
     */
    @RequestMapping(value = "/article_list", method = RequestMethod.GET)
    public String article_list(ModelMap model) {

        return "/mobile/knowledge/article_list";
    }

    /**
     * 文章详情
     * @return
     */
    @RequestMapping(value = "/article_detail", method = RequestMethod.GET)
    public String article_detail(Long id, ModelMap model) {
        systemNoticeService.updateWatchCount(id);
        SystemNotice systemNotice = systemNoticeService.find(id);
        model.addAttribute("systemNotice", systemNotice);
        String createDate = systemNotice.getCreateDate().toString();
        createDate = createDate.substring(0, 10);
        model.addAttribute("createDate", createDate);
        // 判断用户是否点赞过该文章
        if (noticeLikeAndCommentsService.exists(Filter.eq("systemNotice", id),
                Filter.eq("storeMember", storeMemberBaseService.getCurrent().getId()),
                Filter.eq("type", 0), Filter.eq("likeType", 0), Filter.eq("likeStatus", 1))) {
            model.addAttribute("likeStatus", 1);
        } else {
            model.addAttribute("likeStatus", 0);
        }
        // 获取该文章的所有评论
        List<Map<String, Object>> commentsList = noticeLikeAndCommentsService.findCommentsList(id);
        for (Map<String, Object> comments : commentsList) {
            // 判断用户是否点赞过该评论
            if (noticeLikeAndCommentsService.exists(Filter.eq("systemNotice", id),
                    Filter.eq("storeMember", storeMemberBaseService.getCurrent().getId()),
                    Filter.eq("likeComments", Long.parseLong(comments.get("id").toString())),
                    Filter.eq("type", 0), Filter.eq("likeType", 1), Filter.eq("likeStatus", 1))) {
                comments.put("isLike", 1);
            } else {
                comments.put("isLike", 0);
            }
            System.out.println("创建时间：" + comments.get("create_date").toString());
        }
        model.addAttribute("commentsList", commentsList);
        return "/mobile/knowledge/article_detail";
    }

    /**
     * 公告
     * 
     * @return
     */
    @RequestMapping(value = "/notice", method = RequestMethod.GET)
    public String notice(ModelMap model) {
        // 公告
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        List<SystemNotice> systemNoticesNew = new ArrayList<SystemNotice>();
        List<SystemNotice> systemNotices = systemNoticeService.getSystemNoticeById(storeMember.getId());
        if (systemNotices.size() > 0) {
            for (SystemNotice systemNotice : systemNotices) {
                if (systemNotice.getIsPublic()) {
                    systemNoticesNew.add(systemNotice);
                }
            }
        }
        model.addAttribute("systemNotices", systemNoticesNew);
        return "/mobile/knowledge/notice";
    }

    /**
     * 公告详情
     * 
     * @return
     */
    @RequestMapping(value = "/notice_detail", method = RequestMethod.GET)
    public String notice_detail(Long id, ModelMap model) {
        SystemNotice systemNotice = systemNoticeService.find(id);
        model.addAttribute("systemNotice", systemNotice);
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        model.addAttribute("companyInfo", companyInfo);
        String createDate = systemNotice.getCreateDate().toString();
        createDate = createDate.substring(0, 10);
        model.addAttribute("createDate", createDate);
        return "/mobile/knowledge/notice_detail";
    }
    
    /**
     * 获取富文本内容的图片路径（src的内容）
     * @param htmlStr
     * @return
     */
    public Set<String> getImgStr(String htmlStr) {
        Set<String> list = new LinkedHashSet<String>();
        String img = "";
        Pattern p_image;
        Matcher m_image;
        // String regEx_img = "<img.*src=(.*?)[^>]*?>"; //图片链接地址
        String regEx_img = "<img.*src\\s*=\\s*(.*?)[^>]*?>";
        p_image = Pattern.compile(regEx_img, Pattern.CASE_INSENSITIVE);
        m_image = p_image.matcher(htmlStr);
        while (m_image.find()) {
            // 得到<img />数据
            img = m_image.group();
            // 匹配<img>中的src数据
            Matcher m = Pattern.compile("src\\s*=\\s*\"?(.*?)(\"|>|\\s+)").matcher(img);
            while (m.find()) {
                list.add(m.group(1));
            }
        }
        return list;
    }
}
