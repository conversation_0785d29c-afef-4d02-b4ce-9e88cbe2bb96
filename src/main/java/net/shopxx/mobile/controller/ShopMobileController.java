package net.shopxx.mobile.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;


import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Order;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.shop.service.ShopStoreService;

/**
 * 移动端门店操作
 */
@Controller("shopMobileController")
@RequestMapping("/mobile/shop")
public class ShopMobileController extends BaseController {

    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name="shopStoreServiceImpl")
    private ShopStoreService shopStoreService;
    
    /**
     * 门店管理
     */
    @RequestMapping(value = "/shop_manage", method = RequestMethod.GET)
    public String shop_manage(Long superId,ModelMap model) {
        model.addAttribute("superId",superId);
        // 获取当前登录客户
        Store store = shopStoreService.findStore();
        model.addAttribute("store", store);
        Long count = null;
        if(store != null){
            // 当前登录客户的门店数
            count=shopInfoService.count(Filter.eq("store", store.getId()));
        }
        model.addAttribute("count", count == null?0L:count);
        return "/mobile/shop/shop_manage";
    }

    /**
     * 门店新增
     */
    @RequestMapping(value = "/shop_added", method = RequestMethod.GET)
    public String shop_added(ModelMap model) {
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        Store store = shopStoreService.findStore();
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("store", store));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        List<ShopInfo> shopInfo = shopInfoService.findList(null, filters, null);
        if (shopInfo != null && shopInfo.size() > 0) {
            model.addAttribute("shopInfo", shopInfo.get(0));
        }
        filters.clear();
        filters.add(Filter.eq("code", "businessCategory"));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
        model.addAttribute("BusinessCategory", businessCategoryList);
        model.addAttribute("store", store);
        model.addAttribute("isMember", storeMemberBaseService.getCurrent().getMemberType());
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
        Date da = new Date();
        model.addAttribute("newDate", ft.format(da));

        return "/mobile/shop/shop_added";
    }

    /**
     * 门店设计
     */
    @RequestMapping(value = "/devise", method = RequestMethod.GET)
    public String devise(ModelMap model) {
        return "/mobile/shop/devise";
    }

    /**
     * 售后表单
     */
    @RequestMapping(value = "/aftersale", method = RequestMethod.GET)
    public String aftersale(ModelMap model) {
    	Store store = shopStoreService.findStore();
        if (store != null) {
            String Provinces = storeBaseService.findAreaCity(store.getHeadNewArea());
            model.addAttribute("Provinces", Provinces);
        }
        model.addAttribute("store", store);
        return "/mobile/shop/aftersale";
    }

    /**
     * 门店验收及报销
     */
    @RequestMapping(value = "/acceptance_reimburse", method = RequestMethod.GET)
    public String acceptance_reimburse(ModelMap model) {
        return "/mobile/shop/acceptance_reimburse";
    }

    /**
     * 门店验收
     */
    @RequestMapping(value = "/acceptance", method = RequestMethod.GET)
    public String acceptance(ModelMap model) {
        return "/mobile/shop/acceptance";
    }

    /**
     * 门店列表
     */
    @RequestMapping(value = "/shop_list", method = RequestMethod.GET)
    public String shopList(ModelMap model) {
        Store store = shopStoreService.findStore();
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("store", store.getId()));
        List<Order> orders = new ArrayList<Order>();
        orders.add(Order.desc("id"));
        List<ShopInfo> siList = shopInfoService.findList(null, filters, orders);
        model.addAttribute("siList", siList);
        return "/mobile/shop/shop_list";
    }
    
    /**
     * 门店详情
     */
    @RequestMapping(value = "/shop_detail", method = RequestMethod.GET)
    public String shopDetail(Long shopInfoId, ModelMap model) {
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        model.addAttribute("si", shopInfo);
        return "/mobile/shop/shop_detail";
    }

    /**
     * 门店变更
     */
    @RequestMapping(value = "/alteration", method = RequestMethod.GET)
    public String alteration(Long shopInfoId, ModelMap model) {
        List<Filter> filters = new ArrayList<Filter>();
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        Store store = shopStoreService.findStore();
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        filters.add(Filter.eq("code", "businessCategory"));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
        model.addAttribute("BusinessCategory", businessCategoryList);
        model.addAttribute("store", store);
        model.addAttribute("shopInfo", shopInfo);
        // 所含品牌
        List<String> ibList = new ArrayList<String>();
        if (!StringUtils.isEmpty(shopInfo.getInclusiveBrand())) {
            String[] strs = shopInfo.getInclusiveBrand().split(",");
            ibList = Arrays.asList(strs);
        }
        model.addAttribute("ib", ibList);
        return "/mobile/shop/alteration";
    }

    /**
     * 门店减少
     */
    @RequestMapping(value = "/decrease", method = RequestMethod.GET)
    public String decrease(Long shopInfoId, ModelMap model) {
        List<Filter> filters = new ArrayList<Filter>();
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        Store store = shopStoreService.findStore();
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        filters.add(Filter.eq("code", "businessCategory"));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
        model.addAttribute("BusinessCategory", businessCategoryList);
        model.addAttribute("store", store);
        model.addAttribute("shopInfo", shopInfo);
        // 所含品牌
        List<String> ibList = new ArrayList<String>();
        if (!StringUtils.isEmpty(shopInfo.getInclusiveBrand())) {
            String[] strs = shopInfo.getInclusiveBrand().split(",");
            ibList = Arrays.asList(strs);
        }
        model.addAttribute("ib", ibList);
        return "/mobile/shop/decrease";
    }

    /**
     * 门店整顿
     */
    @RequestMapping(value = "/restructuring", method = RequestMethod.GET)
    public String restructuring(Long shopInfoId, ModelMap model) {
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        Store store = shopStoreService.findStore();
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("store", store));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        model.addAttribute("shopInfo", shopInfo);
        model.addAttribute("store", store);
        return "/mobile/shop/restructuring";
    }
    
    /**
     * 门店费用申请
     */
    @RequestMapping(value = "/shop_cost_apply", method = RequestMethod.GET)
    public String shopCostApply(ModelMap model) {
        Store store = shopStoreService.findStore();
        model.addAttribute("store",store);
        return "/mobile/shop/shop_cost_apply";
    }
}
