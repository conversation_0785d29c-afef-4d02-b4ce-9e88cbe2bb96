package net.shopxx.mobile.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.homepage.entity.SystemNotice;
import net.shopxx.homepage.service.SystemNoticeService;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.LoginOutBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.mobile.entity.AppMenu;
import net.shopxx.mobile.service.AppImageService;
import net.shopxx.mobile.service.AppMenuBaseService;
import net.shopxx.mobile.service.MobileOrderService;
import net.shopxx.mobile.service.ShowProductService;
import net.shopxx.order.entity.ThemeStoreMember;
import net.shopxx.order.service.ThemeStoreMemberService;
import net.shopxx.product.entity.ParameterGroup;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.wf.service.WfBaseService;

@Controller("mobileController")
public class MobileController extends BaseController {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;
	@Resource(name = "mobileOrderServiceImpl")
    private MobileOrderService mobileOrderService;
	@Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
	@Resource(name = "productCategoryBaseServiceImpl")
    private ProductCategoryBaseService productCategoryBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;
	@Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
	@Resource(name = "systemNoticeServiceImpl")
    private SystemNoticeService systemNoticeService;
	@Resource(name = "appImageServiceImpl")
    private AppImageService appImageService;
	@Resource(name = "showProductServiceImpl")
    private ShowProductService showProductService;
	@Resource(name="themeStoreMemberServiceImpl")
	private ThemeStoreMemberService themeStoreMemberService;
	@Resource(name = "loginOutBaseServiceImpl")
	private LoginOutBaseService loginOutBaseService;

    @Resource(name = "appMenuBaseServiceImpl")
    private AppMenuBaseService appMenuBaseService;


	
	@RequestMapping(value = "/mobile/select_member", method = RequestMethod.GET)
	public String select_member(Pageable pageable,HttpServletRequest request,
			HttpServletResponse response, HttpSession session, ModelMap model)throws Exception {
		StoreMember storeMember = storeMemberService.getCurrent();
		
		List<StoreMember> sotreMembers =loginOutBaseService.findStoreMember(storeMember.getIdCard());
		model.addAttribute("storeMembers", sotreMembers);
		model.addAttribute("storeMember", storeMember);
		
		if(sotreMembers.size()>0 && sotreMembers.size()==1){
			return list_tb(null,null,pageable,storeMember.getUsername(),storeMember.getPassword(),"大自然",null,request,
					response, session,model);
		}else if(sotreMembers.size()==0){
			 return "/mobile/edit_card";
		}else{
			return "/mobile/store_member_logins";
		}
		
		
		
		
	}
	
	
	

	@RequestMapping(value = "/mobile/index", method = RequestMethod.GET)
	public String list_tb(Long storeId, String storeName, Pageable pageable,String username, String password, String companyName,
			String language, HttpServletRequest request,
			HttpServletResponse response, HttpSession session, ModelMap model) {
	    /**当前需要处理的流程*/
        List<Map<String, Object>> list = mobileOrderService.findWfList(1, WebUtils.getCurrentStoreMemberId());
        model.addAttribute("countTodo", list.size());
        
       
        if (storeId == null) {
            Member member = storeMemberBaseService.getCurrent().getMember();
            List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
            if (!storeMembers.isEmpty()) {
                Store store = storeMembers.get(0).getStore();
                model.addAttribute("storeId", store.getId());
                model.addAttribute("storeName", store.getName());
                storeId = store.getId();
                storeName = store.getName();
            }
        }
        else if (storeId != 0) {
            Store store = storeBaseService.find(storeId);
            model.addAttribute("storeId", store.getId());
            model.addAttribute("storeName", store.getName());
        }

        Long productCategoryId = null;
        ProductCategory productCategory = productCategoryBaseService.find(productCategoryId);
        model.addAttribute("selectedCategory", productCategory);
        if (productCategory != null) {// 分类非空
            if (productCategory.getIsLeaf()) {// 叶子节点，获取参数
                Set<ParameterGroup> parameterGroups = productCategory.getParameterGroups();
                model.addAttribute("parameterGroups", parameterGroups);
            }
        }

        /*List<Filter> fis = new ArrayList<Filter>();
        fis.add(Filter.eq("isEnabled", true));
        fis.add(Filter.eq("type", 0));
        List<ProductCategory> productCategorys = productCategoryBaseService.findList(null,
                fis,
                null);
        model.addAttribute("productCategorys", productCategorys);
        */
        List<Map<String,Object>> productCategorys = showProductService.findList(4);
        model.addAttribute("productCategorys", productCategorys);
        model.addAttribute("companyInfo", companyInfoBaseService.find(WebUtils.getCurrentCompanyInfoId()));
        
        //公告
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        model.addAttribute("storeMember", storeMember);
        List<SystemNotice> systemNoticesNew = new ArrayList<SystemNotice>();
        List<SystemNotice> systemNotices = systemNoticeService.getSystemNoticeById(storeMember.getId());
        if(systemNotices.size() > 0) {
            for(SystemNotice systemNotice : systemNotices) {
                if(systemNotice.getIsPublic()) {
                    systemNoticesNew.add(systemNotice);
                }
            }
        }
        model.addAttribute("systemNotices", systemNoticesNew);
        
        // 获取8个轮播图
        List<Map<String, Object>> findImage = appImageService.findImage(8);
        model.addAttribute("findImage", findImage);

        //获取菜单列表
        List<AppMenu> menus = null;
        if (storeMember.getCompanyInfoId() == null) {
            menus = appMenuBaseService.getCurrentMenuList(0);
        }
        else {
            menus = appMenuBaseService.getCurrentMenuList(1);
        }
        //model.addAttribute("menu_list", menus); // 菜单列表
        session.setAttribute("menu_list", menus);
     

        // 合并
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<ThemeStoreMember> themeStoreMembers = themeStoreMemberService.findList(null,
				filters,
				null);
		if(themeStoreMembers.size()>0){
			model.addAttribute("isHave", 0);
		}
		if(username!=null){
			int flag=1;
			loginOutBaseService.submitLogin(username,
					password,
					companyName,
					language,
					request,
					response,
					session,
					flag);
		}
		
        
		return "/mobile/index";
	}
	
	@RequestMapping(value = "/mobile/load_goods", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_photo_data(Long productCategoryId, Long[] pcIds,
            String productCategoryName, String sn, String name,
            Boolean isMarketable, String vonderCode, String mod,
            String[] parameter_strs, String orderParam, String orderDirect,
            String[] range_strs, Long storeId, String storeName,
            String keyWords, Pageable pageable) {
	    
	    Long memberRankId = null;
        Map<Long, List<Long>> dataMap = new HashMap<Long, List<Long>>();
        Map<Long, List<BigDecimal[]>> rangeMap = new HashMap<Long, List<BigDecimal[]>>();
        if (storeId == null) {
            Member member = storeMemberBaseService.getCurrent().getMember();
            List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
            if (!storeMembers.isEmpty()) {
                Store store = storeMembers.get(0).getStore();
                storeId = store.getId();
                storeName = store.getName();
            }
        }
        if (storeId != null) {
            Store store = storeBaseService.find(storeId);
            memberRankId = store.getMemberRank().getId();
        }
        
        //设置仓库价格
        String warehousePrice = "590";
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("code", "shippingWarehouse"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> warehouse = systemDictBaseService.findList(null,
                filters,
                null);
        for(SystemDict wh : warehouse){
            if(wh.getValue().equals("总部仓")){
                warehousePrice = wh.getId().toString();
            }
        }
        
        List<Filter> fis = new ArrayList<Filter>();
        fis.add(Filter.eq("isEnabled", true));
        fis.add(Filter.eq("type", 0));
        List<ProductCategory> productCategorys = productCategoryBaseService.findList(null,
                fis,
                null);
        List<ProductCategory> productCategorys2 = new ArrayList<ProductCategory>();
        for (int i = 0; i < pcIds.length; i++) {
            for (int j = 0 ; j < productCategorys.size(); j++) {
                if (pcIds[i].equals(productCategorys.get(j).getId())) {
                    productCategorys2.add(productCategorys.get(j));
                    break;
                }
            }
        }
        // 获取首页中要展示的产品
        /*List<Map<String,Object>> findList = showProductService.findList(4);
        List<ProductCategory> categoryList = new ArrayList();
        for (Map map : findList) {
            categoryList.add(showProductService.find((Long)map.get("id")).getCategory());
        }*/
        Map<String, Object> goodsData = new LinkedHashMap<String, Object>();
        Page<Map<String, Object>> page = new Page<Map<String, Object>>();
        
        for (int i = 0; i < pcIds.length; i++) {
            page = productBaseService.findShowPageForMobile(productCategorys2.get(i).getId(),
                    sn,
                    name,
                    vonderCode,
                    mod,
                    storeId,
                    dataMap,
                    rangeMap,
                    null,
                    null,
                    null,
                    null,
                    null,
                    orderParam,
                    orderDirect,
                    null,
                    false,
                    null,
                    null,
                    null,
                    warehousePrice,
                    pageable);
            goodsData.put(productCategorys2.get(i).getName(), page.getContent());
        }
        String jsonPage = JsonUtils.toJson(goodsData);
        return success(jsonPage);
	}
	


	@RequestMapping(value = "/custom/mobile/login", method = RequestMethod.GET)
	public String login(String username, String password, String companyName,
			String language, HttpServletRequest request,
			HttpServletResponse response, HttpSession session, ModelMap model) {
		
	
		
		return "/mobile/login";
	}
}