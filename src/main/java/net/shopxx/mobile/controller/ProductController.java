package net.shopxx.mobile.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.BrowseLog;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.BrowseLogService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PolicyCountService;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.DepositAttachService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.mobile.service.ShowProductService;
import net.shopxx.order.purchase.service.ContractPriceService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.product.entity.ParameterGroup;
import net.shopxx.product.entity.PartsGroup;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.product.entity.ProductParts;
import net.shopxx.product.service.ProductAttachCategoryBaseService;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.product.service.ProductPriceBaseService;
import net.shopxx.product.service.SpecificationBaseService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.wf.service.WfBaseService;

@Controller("mobileProductController")
@RequestMapping("/mobile/product")
public class ProductController extends BaseController {

	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "contractPriceServiceImpl")
	private ContractPriceService contractPriceService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseService;
	@Resource(name = "policyCountServiceImpl")
	private PolicyCountService policyCountService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "depositAttachServiceImpl")
	private DepositAttachService depositAttachService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "productCategoryBaseServiceImpl")
	private ProductCategoryBaseService productCategoryBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "productAttachCategoryBaseServiceImpl")
	private ProductAttachCategoryBaseService productAttachCategoryBaseService;
	@Resource(name = "productPriceBaseServiceImpl")
	private ProductPriceBaseService productPriceBaseService;
	@Resource(name = "specificationBaseServiceImpl")
	private SpecificationBaseService specificationBaseService;
	@Resource(name = "browseLogServiceImpl")
	private BrowseLogService browseLogService;
	@Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;
	@Resource(name = "showProductServiceImpl")
    private ShowProductService showProductService;

	/**
	 * 进入产品展示页面
	 * @param productCategoryId
	 * @param flag
	 * @param storeId
	 * @param storeName
	 * @param keyword
	 * @param keyWords
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/product_display", method = RequestMethod.GET)
	public String product_display(Long productCategoryId, Integer flag,
			Long storeId, String storeName, String keyword, String keyWords,
			ModelMap model) {

		model.addAttribute("productCategoryId", productCategoryId);
		model.addAttribute("keyword", keyword);
		model.addAttribute("keyWords", keyWords);
		model.addAttribute("flag", flag);

		if (storeId == null) {
			Member member = storeMemberBaseService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
			if (!storeMembers.isEmpty()) {
				Store store = storeMembers.get(0).getStore();
				model.addAttribute("storeId", store.getId());
				model.addAttribute("storeName", store.getName());
			}
		}
		else if (storeId != 0) {
			Store store = storeBaseService.find(storeId);
			model.addAttribute("storeId", store.getId());
			model.addAttribute("storeName", store.getName());
		}

		ProductCategory productCategory = productCategoryBaseService.find(productCategoryId);
		model.addAttribute("selectedCategory", productCategory);
		if (productCategory != null) {// 分类非空
			if (productCategory.getIsLeaf()) {// 叶子节点，获取参数
				Set<ParameterGroup> parameterGroups = productCategory.getParameterGroups();
				model.addAttribute("parameterGroups", parameterGroups);
			}
		}

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("companyInfoId", companyInfoId));
		fis.add(Filter.eq("isEnabled", true));
		fis.add(Filter.eq("type", 0));
		fis.add(Filter.isNull("parent"));
		
		List<ProductCategory> productCategorys = productCategoryBaseService.findList(null,
				fis,
				null);
//		System.out.println("productCategorys: " + productCategorys.size());
		model.addAttribute("productCategorys", productCategorys);
		model.addAttribute("companyInfo",
				companyInfoBaseService.find(WebUtils.getCurrentCompanyInfoId()));

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List shippingWarehouses = this.systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("shippingWarehouses", shippingWarehouses);

		try {
			filters.clear();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
			// 非0 展示
		}
		catch (RuntimeException e) {

		}
		return "/mobile/product/product_display";
	}

	/**
	 * 获取商品信息
	 * @param productCategoryId
	 * @param productCategoryName
	 * @param sn
	 * @param name
	 * @param isMarketable
	 * @param vonderCode
	 * @param mod
	 * @param parameter_strs
	 * @param orderParam
	 * @param orderDirect
	 * @param range_strs
	 * @param storeId
	 * @param storeName
	 * @param keyWords
	 * @param pageable
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/list_photo_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_photo_data(Long productCategoryId,
			String productCategoryName, String sn, String name,
			Boolean isMarketable, String vonderCode, String mod,
			String[] parameter_strs, String orderParam, String orderDirect,
			String[] range_strs, Long storeId, String storeName,
			String keyWords, Pageable pageable) throws Exception {
	    
//	    System.out.println("productCategoryId: " + productCategoryId);

		//设置每页数据量
		pageable.setPageSize(10);
		Long memberRankId = null;
		
		Map<Long, List<Long>> dataMap = new HashMap<Long, List<Long>>();
		/*if (parameter_strs != null) {
			for (String parameter_str : parameter_strs) {
				if (ConvertUtil.isEmpty(parameter_str)) continue;
				String[] items = parameter_str.split("_");
				long group_id = Long.valueOf(items[0]);
				long valueId = Long.valueOf(items[1]);

				if (dataMap.containsKey(group_id)) {
					List<Long> list = dataMap.get(group_id);
					if (list == null) {
						list = new ArrayList<Long>();
					}
					list.add(valueId);
					dataMap.put(group_id, list);
				}
				else {
					List<Long> list = new ArrayList<Long>();
					list.add(valueId);
					dataMap.put(group_id, list);
				}
			}
		}*/
		Map<Long, List<BigDecimal[]>> rangeMap = new HashMap<Long, List<BigDecimal[]>>();
		/*if (range_strs != null) {
			for (String range_str : range_strs) {
				if (ConvertUtil.isEmpty(range_str)) continue;
				String[] items = range_str.split("_");
				long group_id = Long.valueOf(items[0]);
				if (items.length == 1) continue;
				BigDecimal minValue = new BigDecimal(
						ConvertUtil.isEmpty(items[1]) ? "0" : items[1]);
				BigDecimal maxValue = new BigDecimal(Long.MAX_VALUE);
				if (items.length == 3) {
					maxValue = new BigDecimal(items[2]);
				}
				BigDecimal[] mv = new BigDecimal[] { minValue, maxValue };

				if (rangeMap.containsKey(group_id)) {
					List<BigDecimal[]> list = rangeMap.get(group_id);
					if (list == null) {
						list = new ArrayList<BigDecimal[]>();
					}
					list.add(mv);
					rangeMap.put(group_id, list);
				}
				else {
					List<BigDecimal[]> list = new ArrayList<BigDecimal[]>();
					list.add(mv);
					rangeMap.put(group_id, list);
				}
			}
		}*/
		
		/*Long memberRankId = null;
		if (storeId != null) {
			Store store = storeBaseService.find(storeId);
			memberRankId = store.getMemberRank().getId();
		}*/
		
		//设置仓库价格
//		String warehousePrice = null;
		/*List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> warehouse = systemDictService.findList(null,
				filters,
				null);
		for (SystemDict wh : warehouse) {
			if (wh.getValue().equals("总部仓")) {
				warehousePrice = wh.getId().toString();
			}
		}*/
		
		if (storeId != null) {
            Store store = storeBaseService.find(storeId);
            memberRankId = store.getMemberRank().getId();
        }
		
		//设置仓库价格
        String warehousePrice = "590";
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("code", "shippingWarehouse"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> warehouse = systemDictBaseService.findList(null,
                filters,
                null);
        for(SystemDict wh : warehouse){
            if(wh.getValue().equals("总部仓")){
                warehousePrice = wh.getId().toString();
            }
        }
		
		String jsonPage = "";
		if (!StringUtils.isEmpty(keyWords)) {
			jsonPage = JsonUtils.toJson(productBaseService.findShowPageForMobile(productCategoryId,
					sn,
					name,
					vonderCode,
					mod,
					storeId,
					dataMap,
					rangeMap,
					null,
					null,
					null,
					keyWords,
					null,
					orderParam,
					orderDirect,
					null,
					false,
					null,
					null,
					null,
					warehousePrice,
					pageable));
		}
		else {
			jsonPage = JsonUtils.toJson(productBaseService.findShowPageForMobile(productCategoryId,
					sn,
					name,
					vonderCode,
					mod,
					storeId,
					dataMap,
					rangeMap,
					null,
					null,
					null,
					null,
					null,
					orderParam,
					orderDirect,
					null,
					false,
					null,
					null,
					null,
					warehousePrice,
					pageable));
		}
		return success(jsonPage);
	}

	/*
	 * 产品详情
	 * @return
	 */
	@RequestMapping(value = "/product_detail", method = RequestMethod.GET)
	public String product_detail(Long id, Long storeId, Long cartItemId,
			Long demandOrderItemId, Long orderItemId, Integer flag, String product_grade,
			ModelMap model) {

		Product product = productBaseService.find(id);
		long count = product.getHits();
		product.setHits(count + 1);
		productBaseService.update(product);

		BrowseLog browseLog = new BrowseLog();
		browseLog.setBrowseId(id);
		browseLog.setStoreMember(storeMemberBaseService.getCurrent());
		browseLog.setBrowseType(1);// 浏览对象类型 1商品 2附件
		browseLog.setType(1);// 1查看商品, 2下载附件, 3上传附件
		browseLog.setMessage("浏览商品【" + product.getFullName() + "】");
		browseLogService.save(browseLog);

		List<Map<String, Object>> ProductAttachCategorys = productAttachCategoryBaseService.findCategoryByProductId(id);
		model.addAttribute("ProductAttachCategorys", ProductAttachCategorys);

		model.addAttribute("flag", flag);
		/** ------ 获取产品等级 ------ */
        /*Map<Long, List<Long>> dataMap = new HashMap<Long, List<Long>>();
        Map<Long, List<BigDecimal[]>> rangeMap = new HashMap<Long, List<BigDecimal[]>>();
        if (storeId != null) {
            Store store = storeBaseService.find(storeId);
        } else {
            Member member = storeMemberBaseService.getCurrent().getMember();
            List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
            if (!storeMembers.isEmpty()) {
                Store store = storeMembers.get(0).getStore();
                storeId = store.getId();
            }
        }
        
        //设置仓库价格
        String warehousePrice = "590";
        List<Filter> filterList = new ArrayList<Filter>();
        filterList.add(Filter.eq("code", "shippingWarehouse"));
        filterList.add(Filter.eq("isEnabled", true));
        filterList.add(Filter.isNotNull("parent"));
        List<SystemDict> warehouse = systemDictBaseService.findList(null,
                filterList,
                null);
        for(SystemDict wh : warehouse){
            if(wh.getValue().equals("总部仓")){
                warehousePrice = wh.getId().toString();
            }
        }
        Long productId = product.getId();
        List<Map<String, Object>> productList = showProductService.findMobilProduct(
                    storeId,
                    dataMap,
                    productId,
                    rangeMap,
                    false,
                    warehousePrice);
        if (productList != null && productList.size() > 0) {
            model.addAttribute("product_grade", productList.get(0).get("product_grade"));
        }*/
        /** ------ end ------ */
		
		model.addAttribute("product", product);
		
		if(product_grade==null||product_grade==""){
			product_grade = "0";
		}
		SystemDict productLevel = systemDictService.find(Long.valueOf(product_grade.toString()));
		if(!ConvertUtil.isEmpty(productLevel)){
			product_grade = productLevel.getValue();
		}else{
			product_grade = "";
		}
		
		model.addAttribute("product_grade", product_grade);
		
		ProductCategory productCategory = product.getProductCategory();
		model.addAttribute("productCategory", productCategory);
		model.addAttribute("companyInfo",
		        companyInfoBaseService.find(product.getCompanyInfoId()));
		model.addAttribute("parameterGroups",
				productCategory.getParameterGroups());

		List<ProductParts> productPartss = product.getProductParts();
		model.addAttribute("productPartss", productPartss);

		List<PartsGroup> partsGroups = new ArrayList<PartsGroup>();
		for (ProductParts parts : productPartss) {
			if (!partsGroups.contains(parts.getPartsGroup())) {
				partsGroups.add(parts.getPartsGroup());
			}
		}
		model.addAttribute("partsGroups", partsGroups);

		if (storeId != null) {
			Store store = storeBaseService.find(storeId);
			if (!store.getIsMainStore()) {

				Long memberRankId = store.getMemberRank().getId();
				Map<String, Object> productPrice = productPriceBaseService.findProductPrice(id,
						memberRankId);
				if (productPrice != null) {
					model.addAttribute("memberPrice",
							productPrice.get("store_member_price"));
				}

				model.addAttribute("storeId", store.getId());
				model.addAttribute("storeName", store.getName());
			}
		}

		Map<Long, String> partsNames = new HashMap<Long, String>();
		List<Long> partses = new ArrayList<Long>();
		model.addAttribute("partses", partses);
		model.addAttribute("partsNames", partsNames);

		List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
		model.addAttribute("bom", JsonUtils.toJson(maps));

		model.addAttribute("specifications", specificationBaseService.findAll());
		List<Map<String, Object>> productAttachs = productBaseService.findAttachListByProductId(product.getId());
		model.addAttribute("attach_json", JsonUtils.toJson(productAttachs));

		try {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
			// 非0 展示
		}
		catch (RuntimeException e) {

		}

		return "/mobile/product/product_detail";
	}
}
