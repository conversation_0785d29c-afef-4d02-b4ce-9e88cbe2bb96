package net.shopxx.mobile.controller;

import java.util.*;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.member.entity.Post;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSaleOrgPost;
import net.shopxx.member.service.PostBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.mobile.entity.Plan;
import net.shopxx.mobile.entity.PlanComment;
import net.shopxx.mobile.entity.PlanItem;
import net.shopxx.mobile.entity.PlanItemAttach;
import net.shopxx.mobile.entity.SignIn;
import net.shopxx.mobile.entity.SignInAttach;
import net.shopxx.mobile.service.PlanCommentService;
import net.shopxx.mobile.service.PlanItemService;
import net.shopxx.mobile.service.PlanService;
import net.shopxx.mobile.service.PlanSignService;
import net.shopxx.mobile.service.SignInService;

@Controller("kanbanController")
@RequestMapping("/mobile/salesman")
public class KanbanMobileController extends BaseController {

    @Resource(name = "planServiceImpl")
    private PlanService planService;
    @Resource(name = "planSignServiceImpl")
    private PlanSignService planSignService;
    @Resource(name = "planItemServiceImpl")
    private PlanItemService planItemService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name = "signInServiceImpl")
    private SignInService signInService;
    @Resource(name = "planCommentServiceImpl")
    private PlanCommentService planCommentService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgService;
    @Resource(name = "postBaseServiceImpl")
    private PostBaseService postBaseService;
    @Resource(name = "storeMemberSaleOrgBaseServiceImpl")
    private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
    
    /**
     * 看板主页（看板只有业务员（区域经理）才能操作）
     * @param tab 跳转到指定页签，1、未读  2、已读  3、我的 
     */
    @RequestMapping(value = "/kanban", method = RequestMethod.GET)
    public String kanban(Integer tab,Pageable pageable, ModelMap model) {
        pageable.setPageNumber(1);
        pageable.setPageSize(20);

        model.addAttribute("tab", tab);
        // 获取当前登录客户
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        model.addAttribute("sm", storeMember);
        // 我的
        List<Filter> filter = new ArrayList<Filter>();
        filter.add(Filter.eq("storeMember", storeMember.getId()));
        List<Plan> myPlan = planService.findList(null, filter, null);
        model.addAttribute("my", handleLike(myPlan));
        // 未读
        Page<Map<String, Object>> unreadPage = planService.unreadAndReadPlan("unread",pageable);
        List<Map<String, Object>> unread = unreadPage.getContent();

        //未读信息数量
        model.addAttribute("unReadCount",unreadPage.getTotal());

        List<Plan> unreadPlan = new ArrayList<Plan>();

        if(unread != null && unread.size() > 0){
            Set<Long> ids = new TreeSet<Long>();
            for(Map<String, Object> map : unread){
                Long id = Long.valueOf(map.get("id").toString());
                ids.add(id);
            }
            filter.clear();
            filter.add(Filter.in("id",ids));
            unreadPlan = planService.findList(null, filter, null);

        }

//        if (unread != null && unread.size() > 0) {
//            for (Map<String, Object> map : unread) {
//                Plan plan = planService.find(Long.valueOf(map.get("id").toString()));
//                unreadPlan.add(plan);
//            }
//        }
        model.addAttribute("unread", handleLike(unreadPlan));

        // 已读
        Page<Map<String, Object>> readPage = planService.unreadAndReadPlan("read", pageable);
        List<Map<String, Object>> read = readPage.getContent();
        List<Plan> readPlan = new ArrayList<Plan>();
        if (read != null && read.size() > 0) {
            Set<Long> ids = new TreeSet<Long>();
            for(Map<String, Object> map : read){
                Long id = Long.valueOf(map.get("id").toString());
                ids.add(id);
            }
            filter.clear();
            filter.add(Filter.in("id",ids));
            readPlan = planService.findList(null, filter, null);

//            for (Map<String, Object> map : read) {
//                Plan plan = planService.find(Long.valueOf(map.get("id").toString()));
//                readPlan.add(plan);
//            }
        }
        model.addAttribute("read", handleLike(readPlan));
        // 用户所绑定的省份机构
        model.addAttribute("saleOrgs", getSaleOrgs(storeMember));
        return "/mobile/salesman/kanban";
    }
    
    /**
     * 获取已读、未读计划
     * @param isRead 'unread':未读  'read':已读   必填
     * @param orgId 机构id
     * @param name 业务员名称
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/searchPlan", method = RequestMethod.POST)
    public List<Object> searchPlan(String isRead, Long orgId, String name,
                                   //String date,
                                   String startTime,
                                   String endTime,
                                   Pageable pageable){
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("isRead", isRead);
        param.put("soid", orgId);
        param.put("name", name);
//        if ("最近一周".equals(date)) {
//            param.put("date", getBeforeOrAfterDate(new Date(), -7));
//        } else if ("一个月".equals(date)) {
//            param.put("date", getBeforeOrAfterDate(new Date(), -30));
//        } else if ("三个月".equals(date)) {
//            param.put("date", getBeforeOrAfterDate(new Date(), -92));
//        } else {
//            param.put("date", null);
//        }

        param.put("startTime",startTime);
        param.put("endTime",endTime);
        Page<Map<String, Object>> planPage = planService.searchPlan(param,pageable);
        List<Map<String, Object>> plans = planPage.getContent();
        List<Plan> unreadPlan = new ArrayList<Plan>();

        if(!plans.isEmpty()){
            Set<Long> ids = new TreeSet<Long>();
            for(Map<String, Object> map : plans){
                Long id = Long.valueOf(map.get("id").toString());
                ids.add(id);
            }

            List<Filter> filters = new ArrayList<Filter>();
            filters.clear();
            filters.add(Filter.in("id",ids));
            unreadPlan = planService.findList(null, filters, null);
        }

//        for (Map<String, Object> map : plans) {
//            Plan plan = planService.find(Long.valueOf(map.get("id").toString()));
//            unreadPlan.add(plan);
//        }
        List<Object> handlePlanData = handlePlanData(unreadPlan);
        return handlePlanData;
    }
    
    private String getBeforeOrAfterDate(Date date, int num) {
        Calendar calendar = Calendar.getInstance();//获取日历
        calendar.setTime(date);       //当date的值是当前时间，则可以不用写这段代码。
        calendar.add(Calendar.DATE, num);
        Date d = calendar.getTime();  //把日历转换为Date
        return DateFormatUtils.format(d, "yyyy-MM-dd");
    }
    
    /**
     * 使用搜索过滤的数据
     * @param plans
     * @return
     */
    private List<Object> handlePlanData(List<Plan> plans) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        List<Object> tPlan = new ArrayList<Object>();
        ArrayList<Filter> filter = new ArrayList<Filter>();
        for (Plan plan : plans) {
            Map<String,Object> resultMap = new HashMap<String, Object>();
            // 封装计划
            Map<String, Object> planMap = new HashMap<String, Object>();
            planMap.put("imageName", plan.getStoreMember().getImageName());
            planMap.put("username", plan.getStoreMember().getUsername());
            planMap.put("name", plan.getStoreMember().getName());
            planMap.put("planId", plan.getId());
            planMap.put("planTime", DateFormatUtils.format(plan.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
            planMap.put("createDate", DateFormatUtils.format(plan.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
            planMap.put("viewCount", plan.getViewCount());
            planMap.put("likeCount", plan.getLikeCount());
            planMap.put("summaryTime", plan.getSummaryTime());
            // 计划项
            List<Object> planItemList = new ArrayList<Object>();
            for (PlanItem planItem : plan.getPlans()) {
                Map<String, Object> planItemMap = new HashMap<String, Object>();
                planItemMap.put("workType", planItem.getWorkType());
                planItemMap.put("customerType", planItem.getCustomerType());
                planItemMap.put("customerName", planItem.getCustomerName());
                planItemMap.put("content", planItem.getContent());
                planItemMap.put("status", planItem.getStatus());
                planItemMap.put("planItemId", planItem.getStatus());
                planItemMap.put("summary", planItem.getSummary());
                // 总结图片
                ArrayList<Object> attachList = new ArrayList<Object>();
                for (PlanItemAttach attach : planItem.getPlanItemAttachs()) {
                    Map<String,Object> attachMap = new HashMap<String, Object>();
                    attachMap.put("url", attach.getUrl());
                    attachMap.put("memo", attach.getMemo());
                    attachMap.put("fileName", attach.getFileName());
                    attachMap.put("name", attach.getName());
                    attachList.add(attachMap);
                }
                planItemMap.put("attachList", attachList);
                planItemList.add(planItemMap);
            }
            planMap.put("planItemList", planItemList);
            resultMap.put("plan", planMap);
            // 封装评论
            List<Object> commList = new ArrayList<Object>();
            filter.clear();
            filter.add(Filter.eq("plan", plan.getId()));
            for (PlanComment cItem : planCommentService.findList(null, filter, null)) {
                Map<String, Object> cMap = new HashMap<String, Object>();
                cMap.put("username", cItem.getStoreMember().getUsername());
                cMap.put("name", cItem.getStoreMember().getName());
                cMap.put("imageName", cItem.getStoreMember().getImageName());
                cMap.put("createDate", DateFormatUtils.format(cItem.getCreateDate(), "MM月dd日 HH:mm"));
                cMap.put("content", cItem.getContent());
                commList.add(cMap);
            }
            resultMap.put("comments", commList);
            // 封装是否点赞
            boolean exists = planSignService.exists(Filter.eq("plan", plan.getId()), Filter.eq("type", 2), Filter.eq("storeMember", storeMember.getId()));
            if (exists) {
                resultMap.put("like", 1);  // 当前用户点赞过该计划
            } else {
                resultMap.put("like", 0);
            }
            tPlan.add(resultMap);
        }
        return tPlan;
    }
    
    /**
     * 处理当前业务员是否点赞过计划
     */
    private List<Object> handleLike(List<Plan> plans) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        List<Object> tPlan = new ArrayList<Object>();
        ArrayList<Filter> filter = new ArrayList<Filter>();
        for (Plan plan : plans) {
            Map<String,Object> map = new HashMap<String, Object>();
            filter.clear();
            filter.add(Filter.eq("plan", plan.getId()));
            List<PlanComment> findList = planCommentService.findList(null, filter, null);
            map.put("plan", plan);
            map.put("comments", findList);
            boolean exists = planSignService.exists(Filter.eq("plan", plan.getId()), Filter.eq("type", 2), Filter.eq("storeMember", storeMember.getId()));
            if (exists) {
                map.put("like", 1);  // 当前用户点赞过该计划
            } else {
                map.put("like", 0);
            }
            tPlan.add(map);
        }
        return tPlan;
    }
    
    @ResponseBody
    @RequestMapping(value = "/comment", method = RequestMethod.POST)
    public ResultMsg comment(Long pid, String content) {
        planCommentService.saveComment(pid, content);
        return success();
    }
    
    /**
     * 填写工作计划
     */
    @RequestMapping(value = "/writePlan", method = RequestMethod.GET)
    public String writePlan(ModelMap model) {
        // 获取当前登录客户
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        model.addAttribute("sm", storeMember);
        
        return "/mobile/salesman/writePlan";
    }
    
    /**
     * 保存计划操作
     */
    @ResponseBody
    @RequestMapping(value = "/savePlan", method = RequestMethod.POST)
    public ResultMsg savePlan(Plan plan, Long[] ccPeopleIds, ModelMap model) {
        planService.savePlan(plan, ccPeopleIds);
        return success();
    }
    
    /**
     * 查看计划详情
     */
    @RequestMapping(value = "/planDetails", method = RequestMethod.GET)
    public String planDetail(Long planId, ModelMap model) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        Plan plan = planService.find(planId);
        model.addAttribute("plan", plan);
        int isSelf = 0; // 标识是否查看自己的计划
        // 如果是业务员查看自己的计划，不增加阅读量
        if (!storeMember.getId().equals(plan.getStoreMember().getId())) {
            // 不是看自己发布的
            boolean exists = planSignService.exists(Filter.eq("storeMember", storeMember.getId()), 
                        Filter.eq("type", 1), Filter.eq("plan", plan.getId()));
            if (!exists) {
                planService.handleRead(plan);
            }
            // 判断当前业务员是否省长，所查看的计划是不是自己省的，如果是自己省的可以发布点评
            if (isGovernor(plan)) {
                isSelf = 2;
            }
        } else {
            isSelf = 1; // 自己的
        }
        model.addAttribute("isSelf", isSelf);
        // 计划发布人计划日期的签到记录
        Calendar c = new GregorianCalendar();
        c.setTime(plan.getPlanTime());
        c.set(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH),0, 0, 0);
        Date date = c.getTime();
        c.add(Calendar.DAY_OF_MONTH, 1); 
        List<Filter> filter = new ArrayList<Filter>();
        filter.add(Filter.eq("storeMember", plan.getStoreMember()));
        filter.add(Filter.ge("createDate", date));
        filter.add(Filter.lt("createDate", c.getTime()));
        List<SignIn> signInList = signInService.findList(null, filter, null);
        model.addAttribute("si", signInList);
        // 图片数
        int img = 0;
        int bfCount = 0; // 拜访客户数
        for (PlanItem item : plan.getPlans()) {
            img += item.getPlanItemAttachs().size();
            if (item.getWorkType().equals(3)) {
                bfCount++;
            }
        }
        model.addAttribute("imgCount", img);
        model.addAttribute("bfCount", bfCount);
        return "/mobile/salesman/planDetails";
    }
    
    /**
     * 判断查看人是否是省长<br>
     * 是: 再判断计划是不是该省长下的
     */
    private boolean isGovernor(Plan plan) {
        // 判断当前查看人是否是省长
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        // 获取区域管理部下的所有有效的省份机构
        // 父机构
        ArrayList<Filter> filter = new ArrayList<Filter>();
        filter.add(Filter.eq("companyInfoId", companyInfoId));
        filter.add(Filter.eq("name", "区域管理部"));
        SaleOrg saleOrg = saleOrgService.find(filter);
        // 子机构 
        filter.clear();
        filter.add(Filter.eq("companyInfoId", companyInfoId));
        filter.add(Filter.eq("parent", saleOrg.getId()));
        List<SaleOrg> saleOrgList = saleOrgService.findList(null, filter, null);
        Map<Long, Object> soMap = new HashMap<Long, Object>(); // 所有省份机构
        for (SaleOrg item : saleOrgList) {
            soMap.put(item.getId(), "0");
        }
        // 判断当前用户是否是省长
        filter.clear();
        filter.add(Filter.eq("storeMember", storeMember));
        List<StoreMemberSaleOrg> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findList(null, filter, null);
        Map<Long, Object> soids = new HashMap<Long, Object>();  // 所属省长的机构
        // 获取省长职位
        filter.clear();
        filter.add(Filter.eq("companyInfoId", companyInfoId));
        filter.add(Filter.eq("name", "地板中心区域总监"));
        Post post = postBaseService.find(filter);
        for (StoreMemberSaleOrg storeMemberSaleOrg : storeMemberSaleOrgs) {
            Long sid = storeMemberSaleOrg.getSaleOrg().getId();
            if (soMap.containsKey(sid)) {
                filter.clear();
                filter.add(Filter.eq("companyInfoId", companyInfoId));
                filter.add(Filter.eq("storeMember", storeMember));
                filter.add(Filter.eq("saleOrg", storeMemberSaleOrg.getSaleOrg()));
                filter.add(Filter.eq("post", post));
                StoreMemberSaleOrgPost storeMemberSaleOrgPost = storeMemberSaleOrgPostService.find(filter);
                if (storeMemberSaleOrgPost != null) {
                    soids.put(sid, 0);
                }
            }
        }
        if (soids.size() > 0) {
            // 省长，判断查看的计划是否是属于该省下的
            StoreMember planStoreMember = plan.getStoreMember();
            filter.clear();
            filter.add(Filter.eq("storeMember", planStoreMember));
            List<StoreMemberSaleOrg> planStoreMemberSaleOrgs = storeMemberSaleOrgBaseService.findList(null, filter, null);
            for (StoreMemberSaleOrg item : planStoreMemberSaleOrgs) {
                if (soids.containsKey(item.getSaleOrg().getId())) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 获取用户所绑定的省份机构
     * @return
     */
    private List<SaleOrg> getSaleOrgs(StoreMember storeMember) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<SaleOrg> saleOrgs = new ArrayList<SaleOrg>();
        if (null == storeMember) {
            storeMember = storeMemberBaseService.getCurrent();
        }
        // 获取区域管理部下的所有有效的省份机构
        // 父机构
        ArrayList<Filter> filter = new ArrayList<Filter>();
        filter.add(Filter.eq("companyInfoId", companyInfoId));
        filter.add(Filter.eq("name", "区域管理部"));
        SaleOrg saleOrg = saleOrgService.find(filter);
        // 子机构 
        filter.clear();
        filter.add(Filter.eq("companyInfoId", companyInfoId));
        filter.add(Filter.eq("parent", saleOrg.getId()));
        List<SaleOrg> saleOrgList = saleOrgService.findList(null, filter, null);
        Map<Long, Object> soMap = new HashMap<Long, Object>(); // 所有省份机构
        for (SaleOrg item : saleOrgList) {
            soMap.put(item.getId(), "0");
        }
        filter.clear();
        filter.add(Filter.eq("storeMember", storeMember));
        List<StoreMemberSaleOrg> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findList(null, filter, null);
        for (StoreMemberSaleOrg storeMemberSaleOrg : storeMemberSaleOrgs) {
            Long sid = storeMemberSaleOrg.getSaleOrg().getId();
            if (soMap.containsKey(sid)) {
                saleOrgs.add(storeMemberSaleOrg.getSaleOrg());
            }
        }
        return saleOrgs;
    }
    
    /**
     * 总结
     */
    @RequestMapping(value = "/summary", method = RequestMethod.GET)
    public String summary(Long planItemId, ModelMap model) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        PlanItem planItem = planItemService.find(planItemId);
        model.addAttribute("pi", planItem);
        if (planItem.getStatus() != null) { // 已经总结过，不能再总结
            model.addAttribute("isSummary", 1);
        }
        // 如果是业务员对自己的计划进行写总结 
        if (!storeMember.getId().equals(planItem.getPlan().getStoreMember().getId())) {
            ExceptionUtil.throwControllerException("此计划不是该业务员提的，不能进行总结操作！");
        }
        return "/mobile/salesman/summary";
    }
    
    /**
     * 更新计划操作，增加总结
     */
    @ResponseBody
    @RequestMapping(value = "/updatePlanItem", method = RequestMethod.POST)
    public ResultMsg updatePlanItem(PlanItem planItem, ModelMap model) {
        planItemService.updatePlanItem(planItem);
        return success();
    }
    
    /**
     * 发布通知
     */
    @ResponseBody
    @RequestMapping(value = "/notice", method = RequestMethod.POST)
    public ResultMsg notice(Long pid, ModelMap model) {
        planSignService.updateNotice(pid);
        return success();
    }
    
    /**
     * 点赞或取消点赞
     */
    @ResponseBody
    @RequestMapping(value = "/like", method = RequestMethod.POST)
    public ResultMsg like(Long planId, ModelMap model) {
        Plan plan = planService.find(planId);
        planService.likeOrCancel(plan);
//        ResultMsg success = success();
//        success.setContent(plan.getLikeCount().toString());
        return success(plan.getLikeCount().toString());
    } 
    
    /**
     * 签到
     */
    @RequestMapping(value = "/signIn", method = RequestMethod.GET)
    public String signIn(ModelMap model) {
        return "/mobile/salesman/signIn";
    }
    
    /**
     * 保存签到
     */
    @ResponseBody
    @RequestMapping(value = "/saveSignIn", method = RequestMethod.POST)
    public ResultMsg saveSignIn(SignIn signIn, ModelMap model) {
    	if(signIn.getSignInAttach()==null||signIn.getSignInAttach().size()<=0){
    		return error("请拍照后签到!");
    	}
        signInService.saveSignIn(signIn);
        return success();
    }
    
    /**
     * 查看签到记录
     */
    @RequestMapping(value = "/signInRecord", method = RequestMethod.GET)
    public String signInRecord(ModelMap model) {
        List<Filter> filter = new ArrayList<Filter>();
        filter.add(Filter.eq("storeMember", storeMemberBaseService.getCurrent().getId()));
        List<SignIn> signInList = signInService.findList(50, filter, null);
        model.addAttribute("sis", signInList);
        return "/mobile/salesman/signInRecord";
    }
    
    /**
     * 查看签到记录
     */
    @ResponseBody
    @RequestMapping(value = "/searchSignIn", method = RequestMethod.GET)
    public List<Object> searchSignIn(Date date, ModelMap model) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);  
        c.add(Calendar.DAY_OF_MONTH, 1); 
        List<Filter> filter = new ArrayList<Filter>();
        filter.add(Filter.eq("storeMember", storeMemberBaseService.getCurrent().getId()));
        filter.add(Filter.ge("createDate", date));
        filter.add(Filter.lt("createDate", c.getTime()));
        List<SignIn> signInList = signInService.findList(null, filter, null);
//        model.addAttribute("sis", signInList);
        
        List<Object> dataMap = new ArrayList<Object>();
        for (SignIn item : signInList) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("createDate", DateUtil.convert(item.getCreateDate()));
            map.put("address", item.getAddress().toString());
            map.put("content", item.getContent());
            List<Object> attachs = new ArrayList<Object>();
            for (SignInAttach attach : item.getSignInAttach()) {
                Map<String, Object> attachMap = new HashMap<String, Object>();
                attachMap.put("url", attach.getUrl());
                attachMap.put("name", attach.getName());
                attachs.add(attachMap);
            }
            map.put("attach", attachs);
            dataMap.add(map);
        }
        return dataMap;
    }
    
    /**
     * 获取业务员
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getSalesman", method = RequestMethod.POST)
    public List<Map<String, Object>> getSalesman(String name, Long orgId, ModelMap model) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("name", name);
        param.put("orgId", orgId);
        List<Map<String, Object>> salesmanList = planService.findSalesman(param);
        return salesmanList;
    }
    
    /**
     * 省长点评
     */
    @ResponseBody
    @RequestMapping(value = "/governorReview", method = RequestMethod.POST)
    public ResultMsg saveGovernorReview(Long planId, Integer review, String reviewMemo) {
        if (planId == null) {
            ExceptionUtil.throwControllerException("操作有误！");
        }
        if (review == null) {
            ExceptionUtil.throwControllerException("请先选择是否同意！");
        }
        Plan plan = planService.find(planId);
        plan.setReview(review);
        plan.setReviewMemo(reviewMemo);
        plan.setReviewDate(new Date());
        plan.setGovernor(storeMemberBaseService.getCurrent());
        planService.update(plan);
        return success();
    }
    
}
