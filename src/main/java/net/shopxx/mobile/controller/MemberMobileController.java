package net.shopxx.mobile.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.StoreApply;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.StoreApplySbuService;
import net.shopxx.member.service.StoreApplyService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 移动端客户操作
 */
@Controller("memberMobileController")
@RequestMapping("/mobile/member")
public class MemberMobileController extends BaseController {

    @Resource(name = "storeApplyServiceImpl")
    private StoreApplyService storeApplyService;
    @Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "storeMemberSbuServiceImpl")
    private StoreMemberSbuService storeMemberSbuService;
    @Resource(name = "memberRankBaseServiceImpl")
    private MemberRankBaseService memberRankService;
    @Resource(name = "wfObjConfigBaseServiceImpl")
    private WfObjConfigBaseService wfObjConfigBaseService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "organizationServiceImpl")
    private OrganizationService organizationService;
    @Resource(name = "storeApplySbuServiceImpl")
    private StoreApplySbuService storeApplySbuService;

    @RequestMapping(value = "/store_apply", method = RequestMethod.GET)
    public String storeApply(ModelMap model) {
        List<Filter> filters = new ArrayList<Filter>();
        List<Integer> types = storeApplyService.getTypes();
        model.addAttribute("types", types);
        model.addAttribute("companyInfoId", WebUtils.getCurrentCompanyInfoId());
        model.addAttribute("wfStates", wfBaseService.getAllWfStates());

        // 业务类型
        filters.clear();
        filters.add(Filter.eq("code", "businessType"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessTypes = systemDictBaseService.findList(null, filters, null);
        model.addAttribute("businessTypes", businessTypes);

        // 经销商状态
        filters.clear();
        filters.add(Filter.eq("code", "distributorStatus"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> distributorStatus = systemDictBaseService.findList(null, filters, null);
        model.addAttribute("distributorStatus", distributorStatus);

//        //经营组织多选
//        List<Organization> os = new ArrayList<Organization>();
//        if(storeApply.getOrganizations()!=null){
//            for(String osId : storeApply.getOrganizations().split(",")){
//                os.add(organizationService.find(Long.parseLong(osId)));
//            }
//            model.addAttribute("os", os);
//        }

        //经销商属性
        filters.clear();
        filters.add(Filter.eq("code", "agentProperty"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> agentProperties = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("agentProperties", agentProperties);

        filters.clear();
        filters.add(Filter.eq("code", "sbu"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> sbu = systemDictBaseService.findList(null, filters, null);
        StoreMember storeMember = storeMemberService.getCurrent();
        filters.clear();
        filters.add(Filter.eq("storeMember", storeMember));
        List<StoreMemberSbu> s = storeMemberSbuService.findList(null, filters, null);
        List<SystemDict> sbus = new ArrayList<SystemDict>();
        Map<Long,Long> sbuIdMap = new HashMap<Long, Long>();//因为经销商加盟中头表的sbu字段跟从表中的sbu
                                                        // 取得是不同表中的值，故用一个map来存储对应关系
                                                            //key为头表中的sbuId，value为从表中的sbuId
        Long jinkousancengSbuId = null;
        for (int i = 0; i < s.size(); i++) {
            for (SystemDict sb : sbu) {
                String a = s.get(i).getSbu().getName();
                String b = sb.getValue();
                if(b!=null && b.equals("进口三层")){
                    jinkousancengSbuId = sb.getId();
                }

                if (a.equals(b)) {
                    sbus.add(sb);
                    sbuIdMap.put(sb.getId(),s.get(i).getSbu().getId());
                }
            }
        }
        model.addAttribute("sbus", sbus);
        model.addAttribute("jinkousancengSbuId", jinkousancengSbuId); //将进口三层的sbuId做处理
        model.addAttribute("sbuIdMapStr", JsonUtils.toJson(sbuIdMap));

        /*
        // 取当前用户默认的sbu
        String sbuNames = "";
        Long xtch = null;
        List<Map<String, Object>> sbuName = storeMemberService.findSbuTy(storeMember.getId());
        if (sbu.size() > 0) {
            sbuNames = sbuName.get(0).get("name").toString();
        }
        List<SystemDict> sbus = new ArrayList<SystemDict>();
        for (int i = 0; i < s.size(); i++) {
            for (SystemDict sb : sbu) {
                String a = s.get(i).getSbu().getName();
                String b = sb.getValue();
                if (a.equals(b)) {
                    sbus.add(sb);
                }
                if (b.equals(sbuNames)) {
                    xtch = sb.getId();
                }
            }
        }
        model.addAttribute("xtch", xtch);
        model.addAttribute("sbus", sbus);*/
        model.addAttribute("storeMember",
                storeMember.getMemberType() == 0 || storeMember.getIsSalesman() ? storeMember : 0);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");// 设置日期格式
        model.addAttribute("nowDate", df.format(new Date()));

        // 平台性质
        filters.clear();
        filters.add(Filter.eq("code", "SaleOrgType"));
        filters.add(Filter.isNotNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null, filters, null);
        model.addAttribute("saleOrgTypes", saleOrgTypes);

        //机构区域
        filters.clear();
        filters.add(Filter.eq("code", "saleOrgRegion"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("saleOrgRegions", saleOrgRegions);

        //经营组织
        filters.clear();
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.eq("type", 0));
        filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
        List<Organization> organizations = organizationService.findList(null, filters, null);
        List<String> oname = new ArrayList<String>();
        List<Organization> organization = new ArrayList<Organization>();
        for(int i=0;i<organizations.size();i++){
            if(organizations.get(i).getName().contains("大自然家居")||organizations.get(i).getName().contains("广西柏景")){
                organization.add(organizations.get(i));
                oname.add(organizations.get(i).getName());
            }
        }
        model.addAttribute("os", organization);
        model.addAttribute("osName", StringUtils.arrayToDelimitedString(oname.toArray(new String[oname.size()]), ","));

        return "/mobile/member/store_apply";
    }

    @RequestMapping(value = "/store_apply_edit", method = RequestMethod.GET)
    public String storeApplyEdit(Long id, ModelMap model) {

        StoreApply storeApply = storeApplyService.find(id);
        model.addAttribute("store", storeApply);
        Integer ShopInfoCount = storeApplyService.countShopInfo(id);//门店数量
        model.addAttribute("ShopInfoCount", ShopInfoCount);


        //三大类附件
        List<Map<String, Object>> storeAttachs0 = storeApplyService.findListByOrderId(id, 0);
        model.addAttribute("store_attach0", JsonUtils.toJson(storeAttachs0));
        List<Map<String, Object>> storeAttachs1 = storeApplyService.findListByOrderId(id, 1);
        model.addAttribute("store_attach1", JsonUtils.toJson(storeAttachs1));
        List<Map<String, Object>> storeAttachs2 = storeApplyService.findListByOrderId(id, 2);
        model.addAttribute("store_attach2", JsonUtils.toJson(storeAttachs2));

        List<Filter> filters = new ArrayList<Filter>();
        List<Integer> types = storeApplyService.getTypes();
        model.addAttribute("types", types);
        model.addAttribute("companyInfoId", WebUtils.getCurrentCompanyInfoId());
        boolean isCheckWf = wfObjConfigBaseService.isCheckWf(55L);
        model.addAttribute("isCheckWf", isCheckWf);
        model.addAttribute("wfStates", wfBaseService.getAllWfStates());
        //model.addAttribute("isCheck", isCheck);

        filters.clear();
        filters.add(Filter.eq("storeApply", storeApply));
        Pageable pageable = new Pageable();
        Page<Map<String, Object>> storeAddressList = storeApplyService.findStoreApplyAddressPage(null,
                null,
                id,
                null,
                pageable);
        model.addAttribute("storeAddressList",
                JsonUtils.toJson(storeAddressList.getContent()));
        model.addAttribute("storeAddressSize",
                storeAddressList.getContent() == null ? "0"
                        : storeAddressList.getContent().size());


        //sbu
        List<Map<String, Object>> sbuList = storeApplySbuService.findStoreApplySbu(id);
        model.addAttribute("sbu_json", JsonUtils.toJson(sbuList));


        // 业务类型
        filters.clear();
        filters.add(Filter.eq("code", "businessType"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessTypes = systemDictBaseService.findList(null, filters, null);
        model.addAttribute("businessTypes", businessTypes);

        // 经销商状态
        filters.clear();
        filters.add(Filter.eq("code", "distributorStatus"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> distributorStatus = systemDictBaseService.findList(null, filters, null);
        model.addAttribute("distributorStatus", distributorStatus);

//        //经营组织多选
//        List<Organization> os = new ArrayList<Organization>();
//        if(storeApply.getOrganizations()!=null){
//            for(String osId : storeApply.getOrganizations().split(",")){
//                os.add(organizationService.find(Long.parseLong(osId)));
//            }
//            model.addAttribute("os", os);
//        }

        //经销商属性
        filters.clear();
        filters.add(Filter.eq("code", "agentProperty"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> agentProperties = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("agentProperties", agentProperties);

        //sbu
        filters.clear();
        filters.add(Filter.eq("code", "sbu"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> sbu = systemDictBaseService.findList(null, filters, null);

        StoreMember storeMember = storeMemberService.getCurrent();
        filters.clear();
        filters.add(Filter.eq("storeMember", storeMember));
        List<StoreMemberSbu> s = storeMemberSbuService.findList(null, filters, null);
        List<SystemDict> sbus = new ArrayList<SystemDict>();
        Map<Long,Long> sbuIdMap = new HashMap<Long, Long>();//因为经销商加盟中头表的sbu字段跟从表中的sbu
                                                            // 取得是不同表中的值，故用一个map来存储对应关系
                                                            //key为头表中的sbuId，value为从表中的sbuId
        Long jinkousancengSbuId = null;
        for (int i = 0; i < s.size(); i++) {
            for (SystemDict sb : sbu) {
                String a = s.get(i).getSbu().getName();
                String b = sb.getValue();
                if(b!=null && b.equals("进口三层")){
                    jinkousancengSbuId = sb.getId();
                }

                if (a.equals(b)) {
                    sbus.add(sb);
                    sbuIdMap.put(sb.getId(),s.get(i).getSbu().getId());
                }
            }
        }
        model.addAttribute("sbus", sbus);
        model.addAttribute("jinkousancengSbuId", jinkousancengSbuId);
        model.addAttribute("sbuIdMapStr", JsonUtils.toJson(sbuIdMap));

        model.addAttribute("storeMember",
                storeMember.getMemberType() == 0 || storeMember.getIsSalesman() ? storeMember : 0);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");// 设置日期格式
        model.addAttribute("nowDate", df.format(new Date()));

        // 平台性质
        filters.clear();
        filters.add(Filter.eq("code", "SaleOrgType"));
        filters.add(Filter.isNotNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null, filters, null);
        model.addAttribute("saleOrgTypes", saleOrgTypes);

        //机构区域
        filters.clear();
        filters.add(Filter.eq("code", "saleOrgRegion"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("saleOrgRegions", saleOrgRegions);

        //经营组织多选
        List<Organization> os = new ArrayList<Organization>();
        if(!ConvertUtil.isEmpty(storeApply.getOrganizations())){
            for(String osId : storeApply.getOrganizations().split(",")){
                os.add(organizationService.find(Long.parseLong(osId)));
            }
            model.addAttribute("os", os);
        }
        if(os!=null && os.size()>0){
            StringBuilder sb = new StringBuilder();
            for(Organization o : os){
                sb.append(","+ o.getName());
            }
            model.addAttribute("organizationNames", sb.toString());
        }

        // 获取流程节点
        if (storeApply.getWfId() != null) {
            //获取到当前流程
            ActWf wf = storeApplyService.getWfByWfId(storeApply.getWfId());

            if (wf != null) {
                //省长审核
                Boolean szsh = false;
                //渠道专员
                Boolean qdzy = false;
                //省长审核权限
                Boolean szshs = false;
                //渠道专员权限
                Boolean qdzys = false;
                //跟单
                Boolean gd = false;
                Boolean gds = false;
                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for(Map<String, Object> c : item){
                    if(c.get("suggestion")!=null){
                        //处理结果
                        String approved = c.get("approved")!=null?c.get("approved").toString():"false";
                        //节点名称
                        String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
                        //对比节点名称是否对应
                        if(rwm.contains("省长")){
                            szsh = Boolean.valueOf(approved);
                        }
                        if(rwm.contains("渠道")){
                            System.out.println("mobile ，进渠道节点");
                            qdzy = Boolean.valueOf(approved);
                            if(!qdzy){
                                szsh = false;
                            }
                        }
                        if(rwm.contains("跟单")){
                            gd = Boolean.valueOf(approved);
                            if(!gd){
                                qdzy = false;
                            }
                        }
                        if(rwm.contains("销售支持部总监")){
                            if(!Boolean.valueOf(approved)){
                                gd = false;
                            }
                        }
                    }
                }
                //获取当前流程所在的节点
                Task t = storeApplyService.getCurrTaskByWf(wf);

                if(t!=null){
                    //获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
                        szshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道")){
                        szshs = true;qdzys = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("跟单")){
                        szshs = true;qdzys = true;gds = true;
                    }
                }
                model.addAttribute("node", t);
                model.addAttribute("szsh", szsh);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzys", qdzys);
                model.addAttribute("gd", gd);
                model.addAttribute("gds", gds);
            }
        }

        return "/mobile/member/store_apply_edit";
    }





    @RequestMapping(value = "/store_apply_list", method = RequestMethod.GET)
    public String storeApplyList(ModelMap model) {

        return "/mobile/member/store_apply_list";
    }


    /**
     * 列表数据
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_data(String name, String outTradeNo, String alias,
                        Integer[] type, Long memberRankId, Long saleOrgId,
                        String dealerCoding, String dealerName, String sn, Long storeMemberId,
                        Integer[] region, Integer[] docStatus, Integer[] wfState, String firstTime,
                        String lastTime, String firstTimee, String lastTimee, Long agentPropertyId,
                        Pageable pageable, ModelMap model) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        Page<Map<String, Object>> page = storeApplyService.findPage(name,outTradeNo,alias,type,companyInfoId,memberRankId,saleOrgId,pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

}
