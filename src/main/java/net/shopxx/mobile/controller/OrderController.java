package net.shopxx.mobile.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.dao.impl.NativeDao;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.MessageBoardService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.PaymentMethodBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.PolicyCountService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.homepage.service.SystemNoticeService;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.entity.StoreSbu;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.DepositAttachService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.mobile.entity.AppImage;
import net.shopxx.mobile.service.AppImageService;
import net.shopxx.mobile.service.MobileOrderService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.purchase.service.ContractPriceService;
import net.shopxx.order.service.CartItemService;
import net.shopxx.order.service.CartService;
import net.shopxx.order.service.LogisticsService;
import net.shopxx.order.service.OrderAttachService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.PriceApplyService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductPriceHeadService;
import net.shopxx.product.service.ProductStoreBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

@Controller("orderController")
@RequestMapping("/mobile/order")
public class OrderController extends BaseController {

	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "cartServiceImpl")
	private CartService cartService;
	@Resource(name = "cartItemServiceImpl")
	private CartItemService cartItemService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;
	@Resource(name = "paymentMethodBaseServiceImpl")
	private PaymentMethodBaseService paymentMethodBaseService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "orderAttachServiceImpl")
	private OrderAttachService orderAttachService;
	@Resource(name = "priceApplyServiceImpl")
	private PriceApplyService priceApplyService;
	@Resource(name = "productPriceHeadServiceImpl")
	private ProductPriceHeadService productPriceHeadService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "messageBoardServiceImpl")
	private MessageBoardService messageBoardService;
	@Resource(name = "productStoreBaseServiceImpl")
	private ProductStoreBaseService productStoreService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "contractPriceServiceImpl")
	private ContractPriceService contractPriceService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseService;
	@Resource(name = "policyCountServiceImpl")
	private PolicyCountService policyCountService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "depositAttachServiceImpl")
	private DepositAttachService depositAttachService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	@Resource(name = "mobileOrderServiceImpl")
	private MobileOrderService mobileOrderService;
	@Resource(name = "systemNoticeServiceImpl")
    private SystemNoticeService systemNoticeService;
	@Resource(name = "appImageServiceImpl")
    private AppImageService appImageService;
	@Resource(name = "logisticsServiceImpl")
    private LogisticsService logisticsService;


	//显示二级菜单
	@RequestMapping("/menuDetail")
	public String secondMenu (Long superId, Integer type, Model model){
        List<Filter> filters = new ArrayList<Filter>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        filters.add(Filter.eq("companyInfoId", companyInfoId));
        filters.add(Filter.eq("type", 1));
        filters.add(Filter.eq("isEnabled", true));
        List<AppImage> iconList = appImageService.findList(null, filters, null);
        String json = JsonUtils.toJson(iconList);
        model.addAttribute("iconList", json);
		model.addAttribute("superId",superId);
		model.addAttribute("type",type);
		return "/mobile/order/order_menu_detail";
	}
	
	/**
	 * sub
	 */
	@RequestMapping(value = "/list_sbu", method = RequestMethod.GET)
	public String list_sbu(ModelMap model, Long type) {

		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> storeMemberSbus = storeMemberSbuService.findList(null,
				filters,
				null);
		List<Sbu> sbus = new ArrayList<Sbu>();
//		for(int i=0;i<storeMemberSbus.size();i++){
//		    if(storeMemberSbus.get(i).getSbu().getName().equals("地板中心"))
//		    	storeMemberSbus.remove(i);
//		}
		for (StoreMemberSbu storeMemberSbu : storeMemberSbus) {
			sbus.add(storeMemberSbu.getSbu());
		}
		
		model.addAttribute("sbus", sbus);
		// 1、获取sbu图标
		/** 0 Nature, 1 进口三层，2 壁纸，3 地板中心 */
		/*List<Object> iconList = new ArrayList<Object>();
		for (int i = 0; i < 4; i++) {
		    List<Map<String, Object>> findIcon = appImageService.findIcon(i);
		    if (findIcon != null && findIcon.size() > 0) {
		        iconList.add(findIcon.get(0));
		    }
		}
		String json = JsonUtils.toJson(iconList);*/
		// 2、
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
        filters.add(Filter.eq("companyInfoId", companyInfoId));
        filters.add(Filter.eq("type", 1));
        filters.add(Filter.eq("isEnabled", true));
        List<AppImage> iconList = appImageService.findList(null, filters, null);
		String json = JsonUtils.toJson(iconList);
		model.addAttribute("iconList", json);
		
		/**
		 * type ： 1，表示商品下单
		 * type ： 2，表示充值申请
		 */
		model.addAttribute("type", type);
		return "/mobile/order/list_sbu";
	}

	/**
	 * 下单
	 * 
	 * @return
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model, Long sbuId,Long saleOrgId, Long organizationId) {
		Store store = null;
		List<Filter> filters = new ArrayList<Filter>();
		Member member = storeMemberService.getCurrent().getMember();
		List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
		if (storeMembers != null) {
			for (StoreMember storeMember : storeMembers) {
				store = storeMember.getStore();
				if (store.getType().equals(Store.Type.distributor)) {
					model.addAttribute("store", store);
					break;
				}
				else {
					store = null;
				}
			}
		}
		if (store != null && !store.getIsMainStore()) {
//			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
//					null,sbuId);
			model.addAttribute("balance", 0);
			filters.clear();
			filters.add(Filter.eq("store", store));
			Pageable pageable = new Pageable();
			Page<Map<String, Object>> storeAddressList = storeService.findStoreAddressPage(null,
					null,
					store.getId(),
					null,
					null,
					pageable);
			if (storeAddressList != null
					&& storeAddressList.getContent().size() > 0) {
				model.addAttribute("storeAddress",
						storeAddressList.getContent().get(0));
			}
		}
		
		//查询金额
		if (sbuId != null && organizationId != null && saleOrgId != null) {
			Map<String, Object> map = storeBalanceService.findBalanceSbu(store.getId(),
					sbuId,
					organizationId,
					saleOrgId);
			if (map != null) {
				model.addAttribute("flas", 0);
				//可用余额
				if (map.get("balance") != null) {
					model.addAttribute("balance2", map.get("balance"));
				}
			}
		}
		
		if (store != null) {
			model.addAttribute("storeManagerList", store.getStoreManagers());
		}
		//业务类型
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.eq("value", "商业地板"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,
				filters,
				null);
		SystemDict businessType = null;
		if (businessTypes != null && businessTypes.size() > 0) {
			businessType = businessTypes.get(0);
		}
		model.addAttribute("businessType", businessType);

		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.isNotNull("parent"));
		businessTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("businessTypes", businessTypes);
		//机构
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,filters,null);
		model.addAttribute("organizations", organizations);
		
		//付款方式
		model.addAttribute("paymentMethods", paymentMethodBaseService.findAll());
		//配送方式
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));

		//sbu
		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);
		
		filters.clear();
		if(sbu != null && sbu.getUndefined3() != null) {
			filters.add(Filter.eq("name", sbu.getUndefined3().substring(sbu.getUndefined3().indexOf("'") + 1, sbu.getUndefined3().lastIndexOf("'"))));
			filters.add(Filter.eq("companyInfoId", sbu.getCompanyInfoId()));
			List<Warehouse> warehouseList = warehouseService.findList(null,filters,null);
			if(warehouseList.size() > 0) {
				Warehouse warehouse = warehouseList.get(0);
				model.addAttribute("warehouse", warehouse);
			}
		}
		
		filters.clear();
		filters.add(Filter.eq("store", store));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreSbu> storeSbu=  storeSbuService.findList(null, filters, null);
		if (storeSbu != null && storeSbu.size() > 0) {
			MemberRank memberRank = storeSbu.get(0).getMemberRank();
			Long memberRankId = memberRank.getId();
			model.addAttribute("memberRankId", memberRankId);
		}
		
		
		try {
			filters.clear();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
			// 非0 展示
		}
		catch (RuntimeException e) {

		}
		
		
		//产品级别
        filters.clear();
        filters.add(Filter.eq("code", "productLevel"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
        model.addAttribute("productLevelList", productLevelList);
		
		return "/mobile/order/list_tb";
	}
	
	/**
     * 订单复制
     * @return
     */
    @RequestMapping(value = "/copy_order", method = RequestMethod.GET)
    public String copyOrder(String orderSn, Long id, String createDate, ModelMap model) {
        List<Filter> filters = new ArrayList<Filter>();
        // 指示订单复制
        model.addAttribute("copyFlag", 1);
        
        Order order = orderService.find(id);
        if (order == null) {
            // 订单有误，请重新选择
            System.out.println("订单有误！");
            return "";
        }
        model.addAttribute("order", order);
        model.addAttribute("store", order.getStore());
        
        Store store = order.getStore();
        
        if (store != null && !store.getIsMainStore()) {
            model.addAttribute("balance", 0);
            filters.clear();
            filters.add(Filter.eq("store", store));
            Pageable pageable = new Pageable();
            Page<Map<String, Object>> storeAddressList = storeService.findStoreAddressPage(null,
                    null,
                    store.getId(),
                    null,
                    null,
                    pageable);
            if (storeAddressList != null
                    && storeAddressList.getContent().size() > 0) {
                model.addAttribute("storeAddress",
                        storeAddressList.getContent().get(0));
            }
        }
        
        /** 订单明细 */
        List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
        orderItems = sort(orderItems, null);
        String orderItem_json = JsonUtils.toJson(orderItems);
        model.addAttribute("orderItem_json", orderItem_json);
        
        /** 订单附件 */
        String orderAttach_json = JsonUtils.toJson(orderAttachService.findListByOrderId(id));
        model.addAttribute("orderAttach_json", orderAttach_json);
        
        //配送方式
        filters.clear();
        filters.add(Filter.eq("isEnabled", true));
        model.addAttribute("shippingMethods", shippingMethodBaseService.findList(null, filters, null));

        // sbu
        Sbu sbu = order.getSbu();
        model.addAttribute("sbu", sbu);
        // 仓库
        model.addAttribute("warehouse", order.getWarehouse());
        // 客户
//        model.addAttribute("store", order.getStore());
        // 价格类型
        model.addAttribute("memberRank", order.getMemberRank());
        /*if (order.getMemberRank() != null) {
            model.addAttribute("memberRankId", order.getMemberRank().getId());
        }*/
        filters.clear();
        filters.add(Filter.eq("store", store));
        filters.add(Filter.eq("sbu", sbu));
        List<StoreSbu> storeSbu=  storeSbuService.findList(null, filters, null);
        if (storeSbu != null && storeSbu.size() > 0) {
            MemberRank memberRank = storeSbu.get(0).getMemberRank();
            Long memberRankId = memberRank.getId();
            model.addAttribute("memberRankId", memberRankId);
        }
        //业务类型
        filters.clear();
        filters.add(Filter.eq("code", "businessType"));
        filters.add(Filter.eq("value", "商业地板"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessTypes = systemDictService.findList(null,
                filters,
                null);
        SystemDict businessType = null;
        if (businessTypes != null && businessTypes.size() > 0) {
            businessType = businessTypes.get(0);
        }
        model.addAttribute("businessType", businessType);

        filters.clear();
        filters.add(Filter.eq("code", "businessType"));
        filters.add(Filter.isNotNull("parent"));
        businessTypes = systemDictService.findList(null, filters, null);
        model.addAttribute("businessTypes", businessTypes);
        
        try {
            filters.clear();
            filters.add(Filter.eq("storeMember",
                    storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
            List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
                    filters,
                    null);
            String value = SystemConfig.getConfig("hiddenAmountRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> list = Arrays.asList(role);
            int hiddenAmount = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    hiddenAmount++;
                    break;
                }
            }
            model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
            // 非0 展示
        }
        catch (RuntimeException e) {

        }
        
        //产品级别
        filters.clear();
        filters.add(Filter.eq("code", "productLevel"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
        model.addAttribute("productLevelList", productLevelList);
        
        return "/mobile/order/list_tb";
    }

	/**
	 * 订单查询
	 * 
	 * @return
	 */
	@RequestMapping(value = "/order_query", method = RequestMethod.GET)
	public String orderQuery(ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		try {
			filters.clear();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
																// 非0 展示
		}
		catch (RuntimeException e) {

		}
		return "/mobile/order/order_query";
	}
	
	/*
     * 列表数据
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_data(String orderSn, String outTradeNo,
                        Integer[] orderStatus,Integer[] wfState, Long sbuId, Integer[] exStatus,
                        Integer[] paymentStatus, Integer[] shippingStatus, Integer[] flag,
                        Long warehouseId, Long[] storeId, String phone, String consignee,
                        String address, Long deliveryCorpId, Long productId[],
                        String firstTime, String lastTime, Integer readOnly,
                        Integer[] confirmStatus, String store_member_name, Long[] saleOrgId,
                        Long organizationId,Long businessTypeId,String aftersaleSn, Pageable pageable, ModelMap model) {

        //设置每页数据
        pageable.setPageSize(25);
        
        int isPC=0;
        
        Page<Map<String, Object>> page = orderService.newFindPage(orderSn,
                outTradeNo,
                orderStatus,
                wfState,
                sbuId,
                shippingStatus,
                warehouseId,
                storeId,
                consignee,
                phone,
                address,
                deliveryCorpId,
                productId,
                paymentStatus,
                flag,
                2,
                firstTime,
                lastTime,
                confirmStatus,
                null,
                store_member_name,
                saleOrgId,
                organizationId,
                businessTypeId,
                isPC,
                pageable,
                null,
                null,
                null,
                null,
                null, aftersaleSn);

        List<Map<String, Object>> orders = page.getContent();

        if (!orders.isEmpty()) {
            String ids = "";
            for (int i = 0; i < orders.size(); i++) {
                Map<String, Object> map = orders.get(i);
                if (i == orders.size() - 1) {
                    ids += map.get("id");
                }
                else {
                    ids += map.get("id") + ",";
                }
            }
            List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(ids,null,null,null);
            if (readOnly == null) {
                orderItems = sort(orderItems, null);
            }
            List<Map<String, Object>> items = null;
            for (Map<String, Object> map : orders) {
                items = new ArrayList<Map<String, Object>>();
                String orderId = map.get("id").toString();
                for (Map<String, Object> itemMap : orderItems) {
                    String oid = itemMap.get("orders").toString();
                    if (readOnly != null) {
                        if (orderId.equals(oid)
                                && itemMap.get("parent") == null) {
                            items.add(itemMap);
                        }
                    }
                    else {
                        if (orderId.equals(oid)) {
                            items.add(itemMap);
                        }
                    }
                }
                map.put("order_items", items);
                // 物流信息
                List<Map<String, Object>> logisticsList = logisticsService.findLogisticsListByOrderId(map.get("id").toString());
                if (logisticsList == null || logisticsList.size() == 0) {
                    map.put("logisticsSize", 0);
                } else {
                    map.put("logisticsSize", logisticsList.size());
                }
            }
        }
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

	/**
	 * 订单详情
	 * @return
	 */
	@RequestMapping(value = "/order_detail", method = RequestMethod.GET)
	public String orderDetail(String orderSn, Long id, String sn,String createDate,
			Integer readOnly, Integer isEdit, ModelMap model) {
		StoreMember storeMember = storeMemberService.getCurrent();

		model.addAttribute("orderSn", orderSn);
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("createDate", createDate);

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}
		else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);

		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService.findShippingItemListByOrderItemId(orderItem.getId()
					.toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
							.compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);
		model.addAttribute("isEdit", isEdit);

		model.addAttribute("storeMember", storeMember);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		/** 客户经理 */
		if (order != null) {
			Store store = order.getStore();
			if (store != null) {
				model.addAttribute("storeManagerList", store.getStoreManagers());
			}
		}
		/** 订单明细 */
		List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
		orderItems = sort(orderItems, null);
		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByDetailedId(id,
				null,
				null));
		model.addAttribute("payment_json", payment_json);
		/** 发货单 */
		String shipping_json = JsonUtils.toJson(shippingService.findShippingItemListByOrderId(id.toString()));
		model.addAttribute("shipping_json", shipping_json);
		/** 订单附件 */
		String orderAttach_json = JsonUtils.toJson(orderAttachService.findListByOrderId(id));
		model.addAttribute("orderAttach_json", orderAttach_json);
		/** 订单全链路 */
		String orderFullLink_json = JsonUtils.toJson(orderFullLinkService.findListByOrderSn(order.getSn()));
		model.addAttribute("orderFullLink_json", orderFullLink_json);

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);

		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,
				filters,
				null);
		SystemDict businessType = null;
		if (businessTypes != null && businessTypes.size() > 0) {
			businessType = businessTypes.get(0);
		}
		model.addAttribute("businessType", businessType);

		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		/** 北京零微科技有限公司 */
		if (order.getCompanyInfoId() == 18) {
			model.addAttribute("isLw", true);
		}

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		try {
			int useTclAddressIntf = Integer.parseInt(SystemConfig.getConfig("useTclAddressIntf",
					WebUtils.getCurrentCompanyInfoId()));
			if (useTclAddressIntf == 1) {
				model.addAttribute("useTclAddressIntf", true);
			}
		}
		catch (Exception e) {}

		Store store = order.getStore();
		if (store != null && !store.getIsMainStore()) {
//			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
//					order.getOrganization() == null ? null
//							: order.getOrganization().getId(),order.getSbu().getId());
			model.addAttribute("balance", 0);
		}

		try {
			filters.clear();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
																// 非0 展示
		}
		catch (RuntimeException e) {

		}
		return "/mobile/order/order_detail";
	}

	/**
	 * 充值申请
	 * 
	 * @return
	 */
	@RequestMapping(value = "/store_recharge", method = RequestMethod.GET)
	public String storeRecharge(ModelMap model, Long sbuId) {

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("rechargeTypes", rechargeTypes);

		StoreMember storeMember = storeMemberService.getCurrent();
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
				BankCard bankCard = null;

				filters.clear();
				filters.add(Filter.eq("saleOrg",
						saleOrgService.find(storeMemberSaleOrg.getSaleOrg()
								.getId())));
				List<BankCard> bankCards = bankCardService.findList(null,
						filters,
						null);
				if (bankCards != null && bankCards.size() > 0) {
					bankCard = bankCards.get(0);
				}
				model.addAttribute("bankCard", bankCard == null ? "" : bankCard);
			}
		}
		else if (storeMember.getMemberType() == 1) {
			NativeDao nativeDao = storeMemberService.getDaoCenter()
					.getNativeDao();
			StringBuilder sql = new StringBuilder();
			sql.append("select s.* from xx_store_member sm, xx_store s");
			sql.append(" where sm.store = s.id");
			sql.append(" and s.is_enabled = 1 and s.is_main_store = 0");
			sql.append(" and sm.username = ?");
			Object[] objs = null;
			if (WebUtils.getCurrentCompanyInfoId() != null) {
				sql.append(" and sm.company_info_id = ?");
				objs = new Object[] { storeMember.getUsername(),
						WebUtils.getCurrentCompanyInfoId() };
			}
			else {
				sql.append(" and sm.company_info_id is null");
				objs = new Object[] { storeMember.getUsername() };
			}

			Store store = nativeDao.findSingleManaged(sql.toString(),
					objs,
					Store.class);

			model.addAttribute("store", store);
			model.addAttribute("saleOrg",
					store == null ? "" : store.getSaleOrg());
			BankCard bankCard = null;

			if (store != null) {
				filters.clear();
				filters.add(Filter.eq("saleOrg",
						saleOrgService.find(store.getSaleOrg().getId())));
				List<BankCard> bankCards = bankCardService.findList(null,
						filters,
						null);
				if (bankCards != null && bankCards.size() > 0) {
					bankCard = bankCards.get(0);
				}
			}
			model.addAttribute("bankCard", bankCard == null ? "" : bankCard);
		}
		//sbu
		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);
		return "/mobile/order/store_recharge";
	}

	/**
	 * 充值查询
	 * 
	 * @return
	 */
	@RequestMapping(value = "/store_recharge_query", method = RequestMethod.GET)
	public String storeRechargeQuery() {

		return "/mobile/order/store_recharge_query";
	}

	/**
	 * 充值详情
	 * 
	 * @return
	 */
	@RequestMapping(value = "/store_recharge_detail", method = RequestMethod.GET)
	public String storeRechargeDetail(Long id, Integer flag, Integer type,String createDate,
			Long objTypeId, ModelMap model) {
		StoreMember storeMember = storeMemberService.getCurrent();

		DepositRecharge depositRecharge = (DepositRecharge) this.depositRechargeService.find(id);
		model.addAttribute("dr", depositRecharge);
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		model.addAttribute("createDate", createDate);

		model.addAttribute("storeMember", storeMember);
		String payment_json = JsonUtils.toJson(this.paymentService.findListByElseSn(depositRecharge.getSn(),
				3));
		model.addAttribute("payment_json", payment_json);
		boolean isCheckWf = this.wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", this.wfBaseService.getAllWfStates());
		String depositAttach_json = JsonUtils.toJson(this.depositAttachService.findListByDepositRechargeId(id));
		model.addAttribute("depositAttach_json", depositAttach_json);
		String fullLink_json = JsonUtils.toJson(this.orderFullLinkService.findListByElseSnAndType(depositRecharge.getSn(),
				5));
		model.addAttribute("fullLink_json", fullLink_json);
		return "/mobile/order/store_recharge_detail";
	}

	private List<Map<String, Object>> sort(List<Map<String, Object>> items,
			Long parent) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if (items != null) {
			for (Map<String, Object> map : items) {
				if ((map.get("parent") != null && parent != null && Long.parseLong(map.get("parent")
						.toString()) == parent.longValue())
						|| (map.get("parent") == null && parent == null)) {
					result.add(map);
					result.addAll(sort(items,
							Long.parseLong(map.get("id").toString())));
				}
			}
		}
		return result;
	}
	
	/**
     * 余额
     * @return
     */
    @RequestMapping(value = "/balance", method = RequestMethod.GET)
	public String balance(ModelMap model, Pageable pageable) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        Store store = storeMember.getStore();
        model.addAttribute("memberType", storeMember.getMemberType());
        /*----- start -----*/
        if (storeMember.getMemberType() == 0) {
            // 企业用户不显示余额
            return "/mobile/order/balance";
        }
        // 外部用户 sbu
        List<Map<String, Object>> sbu = storeMemberBaseService.findSbu(storeMember.getId());
        if (sbu == null || sbu.size() == 0) {
            // 没有设置SBU时,余额不展示
            return "/mobile/order/balance";
        }
        // 对应的客户
        List<Map<String, Object>> findListByMember = storeService.findListByMember(storeMember.getMember().getId());
        if (findListByMember == null || findListByMember.size() == 0) {
            return "/mobile/order/balance";
        }
        Long storeId = Long.valueOf(findListByMember.get(0).get("id").toString());
        Long defaultSbu = null;
        // 查询余额
        Long[] sbuId = new Long[sbu.size()];
        for (int i = 0; i < sbu.size(); i++) {
            sbuId[i] = Long.valueOf(sbu.get(i).get("id").toString());
            if ((Boolean) sbu.get(i).get("is_default")) {
                defaultSbu = Long.valueOf(sbu.get(i).get("id").toString());
            }
        }
//        Object[] args1 = new Object[] { storeId, null, null, null, sbuId};
        Object[] args1 = new Object[] { storeId, null, null, null, null};
        Page<Map<String, Object>> page = storeBalanceService.findPage(pageable, args1);
        Map<String, String> balanceMap = new HashMap<String, String>();
        if (page != null && page.getContent().size() > 0) {
            for (Map<String, Object> map : page.getContent()) {
                if (balanceMap.containsKey(map.get("sbu_name"))) {
                    BigDecimal temp = new BigDecimal(balanceMap.get(map.get("sbu_name"))).add(new BigDecimal(map.get("balance").toString()));
                    balanceMap.put(map.get("sbu_name").toString(), temp.toString());
                } else {
                    balanceMap.put(map.get("sbu_name").toString(), map.get("balance").toString());
                }
            }
        }
        List<Map<String, Object>> balanceList = new LinkedList<Map<String, Object>>();
        BigDecimal total = new BigDecimal(0);
        for (String key : balanceMap.keySet()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("name", key);
            map.put("balance", balanceMap.get(key));
            balanceList.add(map);
            total = total.add(new BigDecimal(balanceMap.get(key))).setScale(2);
        }
        
        // 处理需求的sbu
        Sbu sbu2 = sbuService.find(defaultSbu);
        List<Map<String, Object>> balanceList2 = new LinkedList<Map<String, Object>>();
        for (int i = 0; i < balanceList.size(); i++) {
            if (balanceList.get(i).get("name").equals(sbu2.getName())) {
                ((LinkedList<Map<String, Object>>) balanceList2).addFirst(balanceList.get(i));
            } else {
                balanceList2.add(balanceList.get(i));
            }
        }
        
        model.addAttribute("balanceMap", balanceMap);
        model.addAttribute("balanceList", balanceList2);
        model.addAttribute("total", total);
        
        /*----- end -----*/
        /*Object[] args = new Object[] { store.getId(),
                null,
                null,
                null,
                null }; 
        Page<Map<String, Object>> page = storeBalanceService.findPage(pageable,
                args);
        Map<String, String> balanceMap = new HashMap<String, String>();
        if (page != null && page.getContent().size() > 0) {
            for (Map<String, Object> map : page.getContent()) {
                if (balanceMap.containsKey(map.get("sbu_name"))) {
                    BigDecimal temp = new BigDecimal(balanceMap.get(map.get("sbu_name"))).add(new BigDecimal((String) map.get("balance")));
                    balanceMap.put((String) map.get("sbu_name"), temp.toString());
                } else {
                    balanceMap.put((String) map.get("sbu_name"), (String) map.get("balance"));
                }
            }
        }
//        balanceMap.put("地板", "12.0");
//        balanceMap.put("壁纸", "13.0");
        List<Map<String, Object>> balanceList = new ArrayList<Map<String, Object>>();
        BigDecimal total = new BigDecimal(0);
        for (String key : balanceMap.keySet()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("name", key);
            map.put("balance", balanceMap.get(key));
            balanceList.add(map);
            total = total.add(new BigDecimal(balanceMap.get(key))).setScale(2);
        }
        
//        System.out.println("page.getContent(): " + page.getContent().toString());
//        System.out.println("balanceMap: " + balanceMap.toString());
//        System.out.println("balanceList: " + balanceList.toString());
//        System.out.println("total: " + total);
        model.addAttribute("balanceMap", balanceMap);
        model.addAttribute("balanceList", balanceList);
        model.addAttribute("total", total);*/
        
        return "/mobile/order/balance";
    }
    
    /**
     * 待办事项
     * @return
     */
    @RequestMapping(value = "/todo", method = RequestMethod.GET)
    public String todo(ModelMap model) {
        
        return "/mobile/order/todo";
    }
    
    /**
     * 获取待办事项的个数 
     * @param model
     */
    @ResponseBody
    @RequestMapping(value = "/count_todo", method = RequestMethod.GET)
    public ResultMsg countTodo(ModelMap model) {
        Map<String, String> map = new HashMap<String, String>();
        /**当前所有需要处理的流程*/
        List<Map<String, Object>> list = mobileOrderService.findWfList(1, WebUtils.getCurrentStoreMemberId());
        map.put("countTodo", String.valueOf(list.size()));
        String jsonMap = JsonUtils.toJson(map);
        return success(jsonMap);
    }
    
    
    
    /**
     * 资料设置
     * @return
     */
    @RequestMapping(value = "/set_data", method = RequestMethod.GET)
    public String setData(ModelMap model) {
        
        return "/mobile/order/set_data";
    }
    
    /**
     * 更新密码
     */
    @RequestMapping(value = "/update_data", method = RequestMethod.POST)
    public @ResponseBody
        ResultMsg updateData(StoreMember storeMember, HttpServletRequest request, ModelMap model) {
        Setting setting = SettingUtils.get();
        if (StringUtils.isNotEmpty(storeMember.getPassword())) {
            if (storeMember.getPassword().length() < setting.getPasswordMinLength()
                    || storeMember.getPassword().length() > setting.getPasswordMaxLength()) {
                // 密码长度范围有误
                return error("1601005");
            }
        }
        mobileOrderService.updateData(storeMember);
        return success();
    }
    
    /**
     * 增删首页分类
     * @return
     */
    @RequestMapping(value = "/home_classify", method = RequestMethod.GET)
    public String home_classify(ModelMap model) {
        
        return "/mobile/order/home_classify";
    }
    
    @RequestMapping(value = "select_price_apply_item_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg select_price_apply_item_data(Long productId, String specialOfferNo,
            Long productCategoryId, Long typeSystemDictId, Long storeId,
            String businessTypeName, Integer productGrade,Long saleOrgId,Long organizationId,Pageable pageable) {
        
        if(storeId==null){
            return error("客户不能为空");
        }else if(saleOrgId == null){
            return error("机构不能为空");
        }
        
        List<Map<String, Object>> lists = mobileOrderService.findItemByProductNew(productId, 
                specialOfferNo,
                productCategoryId,
                typeSystemDictId,
                storeId,
                businessTypeName,
                saleOrgId,
                productGrade,organizationId);
        
        String jsonPage = JsonUtils.toJson(lists);
        return success(jsonPage);
    }
    
    /**
     * 订单物流
     */
    @RequestMapping(value = "/order_logistics", method = RequestMethod.GET)
    public String orderLogistics(String oid, ModelMap model) {
        // 根据订单ID查找物流信息
        List<Map<String, Object>> logisticsList = logisticsService.findLogisticsListByOrderId(oid);
        if (logisticsList != null && logisticsList.size() > 0) {
            for (Map<String, Object> map : logisticsList) {
                List<Map<String,Object>> logisticsDrivers = logisticsService.findDriverList(Long.valueOf(map.get("id").toString()));
                List<Map<String,Object>> logisticsContainers = logisticsService.findContainerList(Long.valueOf(map.get("id").toString()));
                List<Map<String,Object>> logisticsTracks = logisticsService.findTrackList(Long.valueOf(map.get("id").toString()));
                List<Map<String,Object>> logisticsShippings = logisticsService.findShipping(Long.valueOf(map.get("id").toString()));
                map.put("logisticsDrivers", logisticsDrivers);
                map.put("logisticsContainers", logisticsContainers);
                Collections.reverse(logisticsTracks);
                map.put("logisticsTracks", logisticsTracks);
                map.put("logisticsShippings", logisticsShippings);
                if (map.get("statr_transport_time") != null && map.get("statr_transport_time").toString() != "") {
                    map.put("statr_transport_time_2", map.get("statr_transport_time").toString().substring(0, 10));
                }
                if (map.get("arrival_transport_time") != null && map.get("arrival_transport_time").toString() != "") {
                    map.put("arrival_transport_time_2", map.get("arrival_transport_time").toString().substring(0, 10));
                }
            }
        }
        model.addAttribute("logisticsList", logisticsList);
//        System.out.println("---------------------");
//        System.out.println("logisticsList: " + JsonUtils.toJson(logisticsList));

        return "/mobile/order/order_logistics";
    }
}