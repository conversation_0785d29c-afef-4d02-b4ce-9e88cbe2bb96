package net.shopxx.mobile.service.Impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.MemberBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.mobile.dao.MobileOrderDao;
import net.shopxx.mobile.service.MobileOrderService;
import net.shopxx.order.entity.Order;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

@Service("mobileOrderServiceImpl")
public class MobileOrderServiceImpl extends WfBillBaseServiceImpl<Order> implements MobileOrderService {
    
    @Resource(name = "mobileOrderDao")
    private MobileOrderDao mobileOrderDao;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "memberBaseServiceImpl")
    private MemberBaseService memberBaseService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgBaseService;

    @Override
    public List<Map<String, Object>> findWfList(Integer flag, Long userid) {
        return mobileOrderDao.findWfList(flag, userid);
    }

    @Override
    @Transactional
    public void updateData(StoreMember storeMember) {
        StoreMember pStoreMember = storeMemberBaseService.getCurrent();
        Member member = pStoreMember.getMember();
        
        if (StringUtils.isEmpty(storeMember.getPassword())) {
            member.setPassword(pStoreMember.getPassword());
        }
        else {
            member.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
            pStoreMember.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
            pStoreMember.setPayPassword(DigestUtils.md5Hex(storeMember.getPassword()));
        }
        
        memberBaseService.update(member);
        pStoreMember.setMember(member);
        storeMemberBaseService.update(pStoreMember);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> findItemByProductNew(Long productId, String specialOfferNo,
            Long productCategoryId, Long typeSystemDictId, Long storeId,
            String businessTypeName, Long saleOrgId, Integer productGrade,Long organizationId) {

        SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
        List<String> list = new ArrayList<String>();
        if (saleOrg != null) {
            String treePath = saleOrg.getTreePath();
            if (treePath.equals(",")) {
                list.add(saleOrg.getId().toString());
            }
            else {
                String[] names = treePath.split(",");
                for (String a1 : names) {
                    if (!a1.equals("")) {
                        list.add(a1);
                        list.add(saleOrg.getId().toString());
                    }
                }
            }
        }

        return mobileOrderDao.findItemByProductNew(productId,
                specialOfferNo,
                productCategoryId,
                typeSystemDictId,
                storeId,
                businessTypeName,
                productGrade,
                list.toArray(),
                saleOrg.getId(),
                organizationId);
    }

}
