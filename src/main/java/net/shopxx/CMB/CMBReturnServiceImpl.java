package net.shopxx.CMB;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ServletContextAware;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.entity.Order.PaymentStatus;
import net.shopxx.order.service.OrderService;
import net.shopxx.util.SnUtil;

@Service("CMBReturnServiceImpl")
public class CMBReturnServiceImpl  extends BaseServiceImpl<Order>
implements CMBReturnService, ServletContextAware{
	
	
	@Resource(name = "companyInfoBaseServiceImpl")
	CompanyInfoBaseService companyInfoService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardBaseService;
	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	
	@Override
	@Transactional
	@SuppressWarnings("unchecked")
	public void payBack(Order order,String rcvacc){
		
		
		CompanyInfo companyInfo = companyInfoService.find(9L);
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		
	
		
			
			
			
			DepositRecharge depositRecharge = new DepositRecharge();
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("rcvacc",rcvacc));
			filters.add(Filter.eq("organization",order.getOrganization()));
			filters.add(Filter.eq("saleorg",order.getSaleOrg()));
			filters.add(Filter.eq("sbu",order.getSbu()));
			filters.add(Filter.eq("companyInfoId",9L));
			List<BankCard> bankCards = bankCardBaseService.findList(null,
					filters,
					null);	
			if(bankCards.size()>0 && bankCards.size()==1){
				depositRecharge.setBankCard(bankCards.get(0));
			}
			depositRecharge.setSaleOrg(order.getSaleOrg());
			depositRecharge.setOrganization(order.getOrganization());
			depositRecharge.setSbu(order.getSbu());
			depositRecharge.setStore(order.getStore());
			depositRecharge.setOrder(order);
			depositRecharge.setCompanyInfoId(companyInfo.getId());
			depositRecharge.setAmount(order.getAmount());
			depositRecharge.setDocStatus(2);
			depositRecharge.setActualAmount(order.getAmount());
			depositRecharge.setPayStatus(1);
			depositRecharge.setSn(SnUtil.generateSn());
			try {
				depositRecharge.setGlDate(format.parse(new Date().toString()));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			depositRechargeService.save(depositRecharge);
			
			//生成充值单改变订单状态
			order.setPaymentStatus(PaymentStatus.paid);
			order.setOrderStatus(OrderStatus.audited);
			order.setDepositRecharge(depositRecharge);
			
			orderService.update(order);
			
		}


	@Override
	public void setServletContext(ServletContext arg0) {
		// TODO Auto-generated method stub
		
	}

}
