package net.shopxx.aftersales.b2b.service.impl;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletContext;

import net.shopxx.base.core.util.*;
import net.shopxx.hubbase.entity.A1_MessageToH;
//import net.shopxx.intf.CancelRealTimePush;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.util.CommonUtil;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ServletContextAware;

import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.swetake.util.Qrcode;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.aftersales.b2b.dao.B2bReturnsDao;
import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsAttach;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.aftersales.b2b.service.B2bReturnsItemService;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.intf.NatureOrderCancel;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import net.shopxx.member.entity.Adjustment;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.PriceDifference;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.PriceDifferenceService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.TriplicateFormService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductPriceService;
import net.shopxx.stock.entity.StockIn;
import net.shopxx.stock.entity.StockInItem;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseBillBatch;
import net.shopxx.stock.service.StockInService;
import net.shopxx.stock.service.WarehouseBatchService;
import net.shopxx.stock.service.WarehouseBillBatchService;
import net.shopxx.util.CommonVariable;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;

@Service("b2bReturnsServiceImpl")
public class B2bReturnsServiceImpl extends ActWfBillServiceImpl<B2bReturns>
		implements B2bReturnsService, ServletContextAware {

	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "stockInServiceImpl")
	private StockInService stockInService;
	@Resource(name = "b2bReturnsDao")
	private B2bReturnsDao b2bReturnsDao;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;
	@Resource(name = "b2bReturnsItemServiceImpl")
	private B2bReturnsItemService b2bReturnsItemService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "triplicateFormServiceImpl")
	private TriplicateFormService triplicateFormService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "aftersaleServiceImpl")
	private AftersaleService aftersaleService;
	@Resource(name = "warehouseBatchServiceImpl")
	private WarehouseBatchService warehouseBatchService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberSaleOrgPostServiceImpl")
	private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
    @Resource(name = "priceDifferenceServiceImpl")
    private PriceDifferenceService priceDifferenceService;
    @Resource(name = "storeSbuServiceImpl")
    private StoreSbuService storeSbuService;
    @Resource(name = "productPriceServiceImpl")
	private ProductPriceService productPriceService;
    @Resource(name = "shippingInfoToLink5ServiceImpl")
	private ShippingInfoToLink5Service shippingInfoToLink5Service;
    
        
	/** servletContext */
	private ServletContext servletContext;

	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}

	@Override
	@Transactional
	public void check(B2bReturns b2bReturns) {

		// 更改退货单状态
		b2bReturns.setStatus(1);
		b2bReturns.setCheckDate(new Date());
		update(b2bReturns);

		// 增加客户余额
		// Store store = b2bReturns.getStore();
		// BigDecimal amount = b2bReturns.getAmount();
		// store.setBalance(store.getBalance().add(amount));
		// storeService.update(store);
		//
		// //生成一张正的支付单
		// paymentService.initPayment(3,
		// 3,
		// 1,
		// 1,
		// 1,
		// amount,
		// null,
		// storeMemberService.getCurrent(),
		// null,
		// store,
		// null,
		// null,
		// null,
		// null,
		// null,
		// "PC后台退货");

		Order order = b2bReturns.getOrder();
		List<String> orderSns = new ArrayList<String>();
		if (order != null) {
			orderSns.add(order.getSn());
		}
		orderFullLinkService.addFullLink(15,
				orderSns,
				b2bReturns.getSn(),
				ConvertUtil.convertI18nMsg("18702", new Object[] { "退货单" }), // 审核退货单
				null);
		saveIntfAtCheck(b2bReturns);
//		saveIntfAtLogistics(b2bReturns);
	}
	
	@Override
	@Transactional
	public void createWf(Long id, String modelId, Long objTypeId) {
		B2bReturns b2bReturns = find(id);
		if (b2bReturns.getWfId() != null) {
			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		// 创建流程实例
		createWf(b2bReturns.getSn(), String.valueOf(storeMember.getId()),
				new Long[]{b2bReturns.getSaleOrg().getId()},
				b2bReturns.getStore().getId(),
				modelId,
				objTypeId,
				id,
				WebUtils.getCurrentCompanyInfoId(),
                true);
		update(b2bReturns);
		
		orderFullLinkService.addFullLink(15,
				null,
				b2bReturns.getSn(),
				ConvertUtil.convertI18nMsg("18701",
						new Object[] { "退货审核" }),
				null);
		
	}
	
	/**
	 * 流程开始 
	 */
	@Override
	public void startBack(ActWf wf) {
		Aftersale aftersale = find(wf.getObjId()).getAftersale();
		if(aftersale != null){
			aftersaleService.b2bReturnsUpdeatAftersale(aftersale, "1");
		}
	}

	/**
	 * 流程结束回调
	 */
	@Override
	@Transactional
	public void endBack(ActWf wf) {//原agreeBack()方法
		super.endBack(wf);
		B2bReturns b2bReturns = find(wf.getObjId());
		//校验退货单状态
		this.checkB2bReturnsState(b2bReturns,0,"已保存","通过");
		//已审核
		b2bReturns.setStatus(1);
		b2bReturns.setCheckDate(new Date());
		update(b2bReturns);
		
		Order order = b2bReturns.getOrder();
		List<String> orderSns = new ArrayList<String>();
		if (order != null) {
			orderSns.add(order.getSn());
		}
		
		orderFullLinkService.addFullLink(15,
				orderSns,
				b2bReturns.getSn(),
				ConvertUtil.convertI18nMsg("18702", new Object[] { "退货单" }), // 审核退货单
				null);
		saveIntfAtCheck(b2bReturns);
//		saveIntfAtLogistics(b2bReturns);
		update(b2bReturns);
		Aftersale aftersale = b2bReturns.getAftersale();
		if(aftersale != null){
			aftersaleService.b2bReturnsUpdeatAftersale(aftersale, "2");
		}
//		if(b2bReturns.getWarehouse() != null){
//			if(b2bReturns.getWarehouse().getIsSendJiaWaYun()) {
//				intfOrderMessageToService.saveLogisticsReturnIntf(b2bReturns, 0);
//			}
//		}
		
		savePriceDifference(b2bReturns);
		
		this.saveB2bReturnsIntfLink5Check(b2bReturns,1);

	}
	
	/**
	 * 同步退货单信息到link5
	 * @param b2bReturns 退货单申请信息
	 * @param flag 
	 */
	public void saveB2bReturnsIntfLink5Check(B2bReturns b2bReturns,Integer flag) {
		// 大自然同步退货申请单信息到link5
		if(b2bReturns.getStore().getIsToLink5()==null ? false: b2bReturns.getStore().getIsToLink5() 
				&& !(b2bReturns.getWarehouse().getIsOrderWarehousing()==null ? false:b2bReturns.getWarehouse().getIsOrderWarehousing())){
			shippingInfoToLink5Service.saveB2bReturnsInfoTo(b2bReturns,flag);
		}
	}
	
	/**
	 * 中断流程回调
	 */
	@Override
	@Transactional
	public void interruptBack(ActWf wf) {//原interruptBack()方法
		B2bReturns b2bReturns = find(wf.getObjId());
		//校验退货单状态
		this.checkB2bReturnsState(b2bReturns,0,"已保存","中断");
		//还原状态
		b2bReturns.setStatus(0);
		//清空工厂品管意见
		b2bReturns.setIsQuality(null);
		b2bReturns.setQCopinion(null);
		//清空质量中心意见
		b2bReturns.setIsQuality1(null);
		b2bReturns.setQAopinion(null);
		//清空工厂仓管意见
		b2bReturns.setProductQuantity(null);
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
		for (Iterator iterator = b2bReturnsItems.iterator(); iterator.hasNext();) {
			B2bReturnsItem b2bReturnsItem = (B2bReturnsItem) iterator.next();
			Long id = b2bReturnsItem.getId();
			b2bReturnsItem.setFullReturnCount(null);
			b2bReturnsItem.setIncompletelyReturnCount(null);
			b2bReturnsItem.setNotReturnCount(null);
		}
		update(b2bReturns);
		orderFullLinkService.addFullLink(15,
				null,
				b2bReturns.getSn(),
				ConvertUtil.convertI18nMsg("18704"),
				null);
		update(b2bReturns);
	}
	
	/**
	 * 驳回流程回调
	 */
	@Override
	@Transactional
	public void rejectBack(ActWf wf) {
		B2bReturns b2bReturns = find(wf.getObjId());
		//校验退货单状态
		this.checkB2bReturnsState(b2bReturns,0,"已保存","驳回");
		b2bReturns.setWfState(3);
		update(b2bReturns);
	}
	
	
	/**
	 * 通过流程节点回调
	 */
	@Override
	@Transactional
	public void agreeBack(ActWf wf) {
		B2bReturns b2bReturns = find(wf.getObjId());
		if(b2bReturns.getWfState() == 3){
			//校验退货单状态
			this.checkB2bReturnsState(b2bReturns,0,"已保存","通过");
			b2bReturns.setWfState(1);
			update(b2bReturns);
		}
	}

	/**
	 * 给流程分支节点赋值
	 */
	public Map<String, Object> setFormData(ActWf wf, String taskId) {
		Map<String,Object> dayMap=new HashMap<String, Object>();
		B2bReturns returns = this.find(wf.getObjId());
		dayMap.put("category", returns.getCategory());
		dayMap.put("factory", returns.getFactoryId());
		dayMap.put("isQuality", returns.getIsQuality());
		dayMap.put("organizationId", returns.getOrganization().getId());
		dayMap.put("organizationName", returns.getOrganization().getName());
		dayMap.put("warehouseTypeId", returns.getWarehouse().getTypeSystemDict().getId());
		dayMap.put("warehouseType", returns.getWarehouse().getTypeSystemDict().getValue());
		dayMap.put("sbuId", returns.getSbu().getId());
		dayMap.put("sbu", returns.getSbu().getName());
		dayMap.put("businessTypeId", returns.getBusinessType().getValue());
		dayMap.put("businessType", returns.getBusinessType().getId());
		dayMap.put("saleOrgId", returns.getSaleOrg().getId());
		dayMap.put("saleOrg", this.find(wf.getObjId()).getSaleOrg().getId());
		dayMap.put("isOrderWarehousing",returns.getWarehouse().getIsOrderWarehousing());
		dayMap.put("category",returns.getCategory());
		setFactory(dayMap,returns.getFactoryId());
		return dayMap;
	}

	public void setFactory(Map<String, Object> dayMap, Long getFactoryId) {
		System.out.println("getFactoryId:"+getFactoryId);
		if (getFactoryId != null) {
			//2089 工厂品管 2090工厂仓管
			List<StoreMember> storeMemberList = storeMemberSaleOrgPostService
					.findPostCodeByStoreMember(getFactoryId, "2089");
			if (storeMemberList != null) {
				dayMap.put("factoryQualityControlUser", storeMemberList.get(0).getId());
			}else {
				ExceptionUtil.throwServiceException("该工厂未找到工厂品管用户，请维护用户岗位！");
			}
			List<StoreMember> storeMemberList1 = storeMemberSaleOrgPostService
					.findPostCodeByStoreMember(getFactoryId, "2090");
			if (storeMemberList1 != null) {
				dayMap.put("factoryStorekeeperUser", storeMemberList1.get(0).getId());
			}else {
				ExceptionUtil.throwServiceException("该工厂未找到工厂仓管用户，请维护用户岗位！");
			}
		}
	}

//	/**
//	 * 给流程分支节点赋值
//	 */
//	public Map<String, Object> setFormData(ActWf wf, String taskId) {
//		Map<String,Object> dayMap=new HashMap<String, Object>();
//		dayMap.put("category", this.find(wf.getObjId()).getCategory());
//		dayMap.put("factory", this.find(wf.getObjId()).getFactoryId());
//		dayMap.put("isQuality", this.find(wf.getObjId()).getIsQuality());
//		dayMap.put("organizationId", this.find(wf.getObjId()).getOrganization().getId());
//		dayMap.put("organizationName", this.find(wf.getObjId()).getOrganization().getName());
//        dayMap.put("warehouseTypeId", this.find(wf.getObjId()).getWarehouse().getTypeSystemDict().getId());
//		dayMap.put("warehouseType", this.find(wf.getObjId()).getWarehouse().getTypeSystemDict().getValue());
//		dayMap.put("sbuId", this.find(wf.getObjId()).getSbu().getId());
//		dayMap.put("sbu", this.find(wf.getObjId()).getSbu().getName());
//		dayMap.put("businessTypeId", this.find(wf.getObjId()).getBusinessType().getValue());
//        dayMap.put("businessType", this.find(wf.getObjId()).getBusinessType().getId());
////        dayMap.put("saleOrgId", this.find(wf.getObjId()).getSaleOrg().getId());
//        dayMap.put("saleOrg", this.find(wf.getObjId()).getSaleOrg().getName());
//        dayMap.put("saleOrgId",this.find(wf.getObjId()).getSaleOrg().getId());
//		return dayMap;
//	}

	public void saveIntfAtCheck(B2bReturns b2bReturns) {
		Long companyInfoId = b2bReturns.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		if ("nature".equals(companyInfo.getCompany_code())) {//nadev:dzrjj na/natest:nature
			// 大自然退货接口
			//System.out.println("進入大自然退货接口");
			intfOrderMessageToService.saveReturnIntf(b2bReturns);
		}
	}

	@Override
	@Transactional
	public Long stockIn(B2bReturns b2bReturns, Warehouse warehouse,
			SaleOrg saleOrg) {

		// 退货单明细生成入库单
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();

		StockIn stockIn = new StockIn();
		BigDecimal total = BigDecimal.ZERO;
		List<StockInItem> stockInItems = new ArrayList<StockInItem>();
		String sn = SnUtil.generateSn();
		for (B2bReturnsItem b2bReturnsItem : b2bReturnsItems) {
			B2bReturnsItem bri = b2bReturnsItemService.find(b2bReturnsItem.getId());
			bri.setStockInQuantity(b2bReturnsItem.getStockInQuantity());
			b2bReturnsItemService.update(bri);
			if (bri.getProduct() != null) {

				StockInItem stockInItem = new StockInItem();
				stockInItem.setProduct(bri.getProduct());
				stockInItem.setProductSn(bri.getSn());
				stockInItem.setInStock(bri.getStockInQuantity());
				stockInItem.setActualInStock(BigDecimal.ZERO);
				stockInItem.setNote("退货单["
						+ bri.getB2bReturns().getSn()
						+ "]入库生成入库单明细");
				stockInItem.setStockIn(stockIn);
				stockInItem.setB2bReturnsItemId(bri.getId());
				total = total.add(bri.getStockInQuantity());
				// total += b2bReturnsItem.getQuantity();
				stockInItems.add(stockInItem);

				List<String> orderSns = new ArrayList<String>();
				String osn = bri.getB2bReturns().getOrder() == null ? null
						: bri.getB2bReturns().getOrder().getSn();
				if (osn != null) {
					orderSns.add(osn);
				}
				orderFullLinkService.addFullLink(15,
						orderSns,
						bri.getB2bReturns().getSn(),
						"产品【" + bri.getProduct().getName() + "】生成退货入库单",
						null);
			}
		}

		// String b2bReturnsSn = b2bReturns.getSn();
		// stockIn.setNote("退货单[" + b2bReturnsSn + "]退货入库生成入库单");
		stockIn.setStatus(0);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("value", "退货入库"));
		SystemDict systemDict = systemDictService.find(filters);
		stockIn.setType(systemDict.getId());
		stockIn.setActualInStock(BigDecimal.ZERO);
		stockIn.setInStock(total);
		stockIn.setWarehouse(warehouse);
		stockIn.setStockInItems(stockInItems);
		stockIn.setSourceType(2);
		// stockIn.setSourceSn(b2bReturnsSn);
		// stockIn.setB2bReturnsSn(b2bReturnsSn);
		stockIn.setSn(sn);
		stockInService.save(stockIn);
		orderFullLinkService.addFullLink(6,
				null,
				stockIn.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "入库单" }), // 创建入库单
				null);

		return stockIn.getId();
		// b2bReturns.setStatus(2);
		// update(b2bReturns);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findItemListById(Long id,
			Long[] organizationIds,Boolean isDefault) {
		return b2bReturnsDao.findItemListById(id,organizationIds,isDefault);
	}

	public BigDecimal findAmount(Long storeId, Long saleOrgId) {
		return b2bReturnsDao.findAmount(storeId, saleOrgId);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findShippingItemListById(Long orderId,
			Long[] returnId) {
		return b2bReturnsDao.findShippingItemListById(orderId, returnId);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findShippingItem(Long storeId,
			String returnSn) {
		return b2bReturnsDao.findShippingItem(storeId, returnSn);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn,String erpSn, Long[] storeId,
			Long sbuId, Integer[] status,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long organizationId, Long[] saleOrgId,String glfirstTime,String gllastTime,
			Long[] productId,Pageable pageable,String moistureContent,String colourNumber,String batch,
			String bCreater,Long[] organizationIds,String aftersaleSn, Integer catecory) {
		return b2bReturnsDao.findPage(sn,
				erpSn,
				storeId,
				sbuId,
				status,
				wfState,
				firstTime,
				lastTime,
				flag,
				organizationId,
				saleOrgId,
				glfirstTime,
				gllastTime,
				productId,
				pageable,
				moistureContent,
				colourNumber,
				batch,
				bCreater,
				organizationIds,
                aftersaleSn,
				catecory);
	}

	@Override
	@Transactional
	public void save(B2bReturns b2bReturns, Order order) {
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
		for (Iterator<B2bReturnsItem> iterator = b2bReturnsItems.iterator(); iterator.hasNext();) {
			B2bReturnsItem returnsItem = iterator.next();
			if (returnsItem == null || (returnsItem.getOrderItem() == null && returnsItem.getProduct() == null) ||
					(returnsItem.getShippingItem() == null && returnsItem.getProduct() == null)) {
				iterator.remove();
				continue;
			}
			List<Filter> filters = new ArrayList<Filter>();
			//系统单据类型
			filters.clear();
			filters.add(Filter.eq("remark", CommonVariable.RETURN_TYPE));
			SystemDict systemType = systemDictService.systemBillType(filters);
			if (returnsItem.getOrderItem().getId() != null) { 
				
				// 订单带的明细
				OrderItem orderItem = orderItemService.find(returnsItem.getOrderItem().getId());
				if ((returnsItem.getProduct() != null && returnsItem.getProduct().getId() != null)) {
					Product product = productService.find(returnsItem.getProduct().getId());
					if (orderItem != null && orderItem.getVonderCode() != null) {
						returnsItem.setVonderCode(orderItem.getVonderCode());
					}else {
						returnsItem.setVonderCode(product.getVonderCode());
					}
					if (orderItem != null && orderItem.getModel() != null) {
						returnsItem.setModel(orderItem.getModel());
					}else {
						returnsItem.setModel(product.getModel());
					}
					if (orderItem != null && orderItem.getName() != null) {
						returnsItem.setName(orderItem.getName());
					}else {
						returnsItem.setName(product.getName());
					}
					if (orderItem != null && orderItem.getFullName() != null) {
						returnsItem.setFullName(orderItem.getFullName());
					}else {
						returnsItem.setFullName(product.getFullName());
					}
					if (orderItem != null && orderItem.getThumbnail() != null) {
						returnsItem.setThumbnail(orderItem.getThumbnail());
					}else {
						returnsItem.setThumbnail(product.getThumbnail());
					}
					returnsItem.setProduct(product);
				}
				returnsItem.setOrderItem(orderItem);
				returnsItem.setShippingItem(null);
			}else if(returnsItem.getShippingItem().getId() != null){
				ShippingItem shippingItem = shippingItemService.find(returnsItem.getShippingItem().getId());
				if ((returnsItem.getProduct() != null && returnsItem.getProduct().getId() != null)) {
					Product product = productService.find(returnsItem.getProduct().getId());
					if (shippingItem != null && shippingItem.getVonderCode() != null) {
						returnsItem.setVonderCode(shippingItem.getVonderCode());
					}else {
						returnsItem.setVonderCode(product.getVonderCode());
					}
					if (shippingItem != null && shippingItem.getModel() != null) {
						returnsItem.setModel(shippingItem.getModel());
					}else {
						returnsItem.setModel(product.getModel());
					}
					if (shippingItem != null && shippingItem.getName() != null) {
						returnsItem.setName(shippingItem.getName());
					}else {
						returnsItem.setName(product.getName());
					}
					if (shippingItem != null && shippingItem.getFullName() != null) {
						returnsItem.setFullName(shippingItem.getFullName());
					}else {
						returnsItem.setFullName(product.getFullName());
					}
					if (shippingItem != null && shippingItem.getThumbnail() != null) {
						returnsItem.setThumbnail(shippingItem.getThumbnail());
					}else {
						returnsItem.setThumbnail(product.getThumbnail());
					}
					returnsItem.setProduct(product);
				}
				returnsItem.setShippingItem(shippingItem);
				returnsItem.setOrderItem(null);
			}else {// 选产品 没明细
				Product product = productService.find(returnsItem.getProduct().getId());
				returnsItem.setSn(null);
				returnsItem.setVonderCode(product.getVonderCode());
				returnsItem.setModel(product.getModel());
				returnsItem.setName(product.getName());
				returnsItem.setFullName(product.getFullName());
				returnsItem.setThumbnail(product.getThumbnail());
				returnsItem.setProduct(product);
				returnsItem.setOrderItem(null);
				returnsItem.setShippingItem(null);
			}
			//产品经营组织
			if(ConvertUtil.isEmpty(returnsItem.getProductOrganization())||
					(!ConvertUtil.isEmpty(returnsItem.getProductOrganization())&&
							ConvertUtil.isEmpty(returnsItem.getProductOrganization().getId()))){
				returnsItem.setProductOrganization(null);
			}
			//色号
			if(ConvertUtil.isEmpty(returnsItem.getColorNumbers())||
					(!ConvertUtil.isEmpty(returnsItem.getColorNumbers())&&
							ConvertUtil.isEmpty(returnsItem.getColorNumbers().getId()))){
				returnsItem.setColorNumbers(null);
			}
			//含水率
			if(ConvertUtil.isEmpty(returnsItem.getMoistureContents())||
					(!ConvertUtil.isEmpty(returnsItem.getMoistureContents())&&
							ConvertUtil.isEmpty(returnsItem.getMoistureContents().getId()))){
				returnsItem.setMoistureContents(null);
			}
			
			returnsItem.setB2bReturns(b2bReturns);
			
			//批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if(!ConvertUtil.isEmpty(returnsItem.getBatch())){
				String[] batchs = returnsItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if(!ConvertUtil.isEmpty(batchs[i])){
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						//退货类型
						warehouseBillBatch.setSysType(systemType);
						//退货
						warehouseBillBatch.setB2bReturns(b2bReturns);
						//退货明细
						warehouseBillBatch.setB2bReturnsItem(returnsItem);
						//批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			returnsItem.setWarehouseBillBatchs(warehouseBillBatchList);
			
		}
		if (b2bReturnsItems.isEmpty()) {
			// 请选择退货产品
			// return error("17200");
			ExceptionUtil.throwServiceException("17200");
		}
		b2bReturns.setSn(SnUtil.getB2bReturnsSn());
		b2bReturns.setStatus(0);
		b2bReturns.setStoreMember(storeMemberService.getCurrent());
		List<B2bReturnsAttach> b2bReturnsAttachs = b2bReturns.getB2bReturnsAttachs();
		for (Iterator<B2bReturnsAttach> iterator = b2bReturnsAttachs.iterator(); iterator.hasNext();) {
			B2bReturnsAttach b2bReturnsAttach = iterator.next();
			if (b2bReturnsAttach == null || b2bReturnsAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (b2bReturnsAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			b2bReturnsAttach.setFileName(b2bReturnsAttach.getName()
					+ "."
					+ b2bReturnsAttach.getSuffix());
			b2bReturnsAttach.setB2bReturns(b2bReturns);
			b2bReturnsAttach.setStoreMember(storeMemberService.getCurrent());
		}
		b2bReturns.setB2bReturnsAttachs(b2bReturnsAttachs);
		save(b2bReturns);
				
		
		List<String> orderSns = new ArrayList<String>();
		if (order != null) {
			orderSns.add(order.getSn());
		}
		orderFullLinkService.addFullLink(15,
				orderSns,
				b2bReturns.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "退货单" }), // 创建入库单
				null);
	}

	@Override
	@Transactional
	public void updateB2bReturns(B2bReturns b2bReturns) {
		B2bReturns cB2bReturns = find(b2bReturns.getId());
		//校验退货单状态
		this.checkB2bReturnsState(cB2bReturns,0,"已保存","保存");
		
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
		for (Iterator<B2bReturnsItem> iterator = b2bReturnsItems.iterator(); iterator.hasNext();) {
			B2bReturnsItem returnsItem = iterator.next();
			if (returnsItem == null || returnsItem.getProduct() == null) {
				iterator.remove();
				continue;
			}
			List<Filter> filters = new ArrayList<Filter>();
			//系统单据类型
			filters.clear();
			filters.add(Filter.eq("remark", CommonVariable.RETURN_TYPE));
			SystemDict systemType = systemDictService.systemBillType(filters);
			if (returnsItem.getOrderItem().getId() != null) { 
				// 订单带的明细
				OrderItem orderItem = orderItemService.find(returnsItem.getOrderItem().getId());
				if ((returnsItem.getProduct() != null && returnsItem.getProduct().getId() != null)) {
					Product product = productService.find(returnsItem.getProduct().getId());
					if (orderItem != null && orderItem.getVonderCode() != null) {
						returnsItem.setVonderCode(orderItem.getVonderCode());
					}else {
						returnsItem.setVonderCode(product.getVonderCode());
					}
					if (orderItem != null && orderItem.getModel() != null) {
						returnsItem.setModel(orderItem.getModel());
					}else {
						returnsItem.setModel(product.getModel());
					}
					if (orderItem != null && orderItem.getName() != null) {
						returnsItem.setName(orderItem.getName());
					}else {
						returnsItem.setName(product.getName());
					}
					if (orderItem != null && orderItem.getFullName() != null) {
						returnsItem.setFullName(orderItem.getFullName());
					}else {
						returnsItem.setFullName(product.getFullName());
					}
					if (orderItem != null && orderItem.getThumbnail() != null) {
						returnsItem.setThumbnail(orderItem.getThumbnail());
					}else {
						returnsItem.setThumbnail(product.getThumbnail());
					}
					returnsItem.setProduct(product);
				}
				returnsItem.setOrderItem(orderItem);
				returnsItem.setShippingItem(null);
			}else if(returnsItem.getShippingItem().getId() != null){
				ShippingItem shippingItem = shippingItemService.find(returnsItem.getShippingItem().getId());
				if ((returnsItem.getProduct() != null && returnsItem.getProduct().getId() != null)) {
					Product product = productService.find(returnsItem.getProduct().getId());
					if (shippingItem != null && shippingItem.getVonderCode() != null) {
						returnsItem.setVonderCode(shippingItem.getVonderCode());
					}else {
						returnsItem.setVonderCode(product.getVonderCode());
					}
					if (shippingItem != null && shippingItem.getModel() != null) {
						returnsItem.setModel(shippingItem.getModel());
					}else {
						returnsItem.setModel(product.getModel());
					}
					if (shippingItem != null && shippingItem.getName() != null) {
						returnsItem.setName(shippingItem.getName());
					}else {
						returnsItem.setName(product.getName());
					}
					if (shippingItem != null && shippingItem.getFullName() != null) {
						returnsItem.setFullName(shippingItem.getFullName());
					}else {
						returnsItem.setFullName(product.getFullName());
					}
					if (shippingItem != null && shippingItem.getThumbnail() != null) {
						returnsItem.setThumbnail(shippingItem.getThumbnail());
					}else {
						returnsItem.setThumbnail(product.getThumbnail());
					}
					returnsItem.setProduct(product);
				}
				returnsItem.setShippingItem(shippingItem);
				returnsItem.setOrderItem(null);
			}else {// 选产品 没明细
				Product product = productService.find(returnsItem.getProduct().getId());
				returnsItem.setSn(null);
				returnsItem.setVonderCode(product.getVonderCode());
				returnsItem.setModel(product.getModel());
				returnsItem.setName(product.getName());
				returnsItem.setFullName(product.getFullName());
				returnsItem.setThumbnail(product.getThumbnail());
				returnsItem.setProduct(product);
				returnsItem.setOrderItem(null);
				returnsItem.setShippingItem(null);
			}
			//产品经营组织
			if(ConvertUtil.isEmpty(returnsItem.getProductOrganization())||
					(!ConvertUtil.isEmpty(returnsItem.getProductOrganization())&&
							ConvertUtil.isEmpty(returnsItem.getProductOrganization().getId()))){
				returnsItem.setProductOrganization(null);
			}
			//色号
			if(ConvertUtil.isEmpty(returnsItem.getColorNumbers())||
					(!ConvertUtil.isEmpty(returnsItem.getColorNumbers())&&
							ConvertUtil.isEmpty(returnsItem.getColorNumbers().getId()))){
				returnsItem.setColorNumbers(null);
			}
			//含水率
			if(ConvertUtil.isEmpty(returnsItem.getMoistureContents())||
					(!ConvertUtil.isEmpty(returnsItem.getMoistureContents())&&
							ConvertUtil.isEmpty(returnsItem.getMoistureContents().getId()))){
				returnsItem.setMoistureContents(null);
			}
			returnsItem.setB2bReturns(cB2bReturns);
			
			//批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if(!ConvertUtil.isEmpty(returnsItem.getBatch())){
				String[] batchs = returnsItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if(!ConvertUtil.isEmpty(batchs[i])){
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						//退货类型
						warehouseBillBatch.setSysType(systemType);
						//退货
						warehouseBillBatch.setB2bReturns(b2bReturns);
						//退货明细
						warehouseBillBatch.setB2bReturnsItem(returnsItem);
						//批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			returnsItem.setWarehouseBillBatchs(warehouseBillBatchList);
		}
		if (b2bReturnsItems.isEmpty()) {
			// 请选择退货产品
			// return error("17200");
			ExceptionUtil.throwServiceException("17200");
		}
		cB2bReturns.getB2bReturnsItems().clear();
		cB2bReturns.getB2bReturnsItems().addAll(b2bReturnsItems);
		cB2bReturns.setStatus(0);
		List<B2bReturnsAttach> b2bReturnsAttachs = b2bReturns.getB2bReturnsAttachs();
		for (Iterator<B2bReturnsAttach> iterator = b2bReturnsAttachs.iterator(); iterator.hasNext();) {
			B2bReturnsAttach b2bReturnsAttach = iterator.next();
			if (b2bReturnsAttach == null || b2bReturnsAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (b2bReturnsAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			b2bReturnsAttach.setFileName(b2bReturnsAttach.getName()
					+ "."
					+ b2bReturnsAttach.getSuffix());
			b2bReturnsAttach.setB2bReturns(cB2bReturns);
			b2bReturnsAttach.setStoreMember(storeMemberService.getCurrent());
		}
		cB2bReturns.getB2bReturnsAttachs().clear();
		cB2bReturns.getB2bReturnsAttachs().addAll(b2bReturnsAttachs);
		cB2bReturns.setBusinessType(b2bReturns.getBusinessType());
		cB2bReturns.setAmount(b2bReturns.getAmount());
		cB2bReturns.setReason(b2bReturns.getReason());
		cB2bReturns.setName(b2bReturns.getName());
		cB2bReturns.setMobile(b2bReturns.getMobile());
		cB2bReturns.setConsignee(b2bReturns.getConsignee());
		cB2bReturns.setConsigneeMobile(b2bReturns.getConsigneeMobile());
		cB2bReturns.setZipCode(b2bReturns.getZipCode());
		cB2bReturns.setAddress(b2bReturns.getAddress());
		cB2bReturns.setArea(b2bReturns.getArea());
		cB2bReturns.setAddressOutTradeNo(b2bReturns.getAddressOutTradeNo());
		cB2bReturns.setWarehouse(b2bReturns.getWarehouse());
		cB2bReturns.setOrganization(b2bReturns.getOrganization());
		cB2bReturns.setStore(b2bReturns.getStore());
		cB2bReturns.setSaleOrg(b2bReturns.getSaleOrg());
		cB2bReturns.setInvoiceTitle(b2bReturns.getInvoiceTitle());
		cB2bReturns.setSbu(b2bReturns.getSbu());
		cB2bReturns.setGlDate(b2bReturns.getGlDate());
		cB2bReturns.setRegionalManager(b2bReturns.getRegionalManager());
		cB2bReturns.setFourAftersaleSn(b2bReturns.getFourAftersaleSn());
		update(cB2bReturns);
		
	}

	@Override
	@Transactional
	public void cancel(B2bReturns b2bReturns) throws Exception {

		// 更改退货单状态
		Integer status = b2bReturns.getStatus();
		b2bReturns.setStatus(6);
		update(b2bReturns);

		Order order = b2bReturns.getOrder();
		List<String> orderSns = new ArrayList<String>();
		if (order != null) {
			orderSns.add(order.getSn());
		}
		aftersaleService.b2bReturnsUpdeatAftersale(b2bReturns.getAftersale(), "0");
		orderFullLinkService.addFullLink(15,
				orderSns,
				b2bReturns.getSn(),
				ConvertUtil.convertI18nMsg("18703", new Object[] { "退货单",
						"作废本张退货单" }), // 作废退货单
				null);

//		saveIntfAtCheck(b2bReturns);
		if (status == 1) {
			Long companyInfoId = b2bReturns.getCompanyInfoId();
			String msg = "";
			CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
//			if ("dzrjj".equals(companyInfo.getCompany_code())) {
//				// 家哇云退货取消接口
//				HashMap<String, Object> params = new HashMap<String, Object>();
//				params.put("type", "AM退货单");// 订单类型
//				params.put("salesNo", b2bReturns.getSn());
//				params.put("shippingId", b2bReturns.getId());
//				params.put("cancelDate", DateUtil.convert(new Date()));
//				params.put("status", 2);
//				params.put("operator", storeMemberBaseService.getCurrent().getUsername());
//				msg = new CancelRealTimePush().cancelBill(params);
//				if (!msg.equals("S")) {
//					ExceptionUtil.throwServiceException("操作失败!返回信息：" + msg);
//				}
//
//			}
		}
	}

	@Override
	public Integer count(String sn, Long[] storeId, Integer[] status,
			Long sbuId,Integer[] wfState, String firstTime, String lastTime, Integer flag,
			Long saleOrgId, Long organizationId, Integer page, Integer size,
			String glfirstTime,String gllastTime,String moistureContent,
			String colourNumber,String batch,String bCreater,Long[] organizationIds) {
		return b2bReturnsDao.count(sn,
				storeId,
				status,
				sbuId,
				wfState,
				firstTime,
				lastTime,
				flag,
				saleOrgId,
				organizationId,
				page,
				size,
				glfirstTime,
				gllastTime,
				moistureContent,
				colourNumber,
				batch,
				bCreater,
				organizationIds);
	}

	@Override
	public List<Map<String, Object>> findList(String sn, Long[] storeId,
			Integer[] status, Long sbuId,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long saleOrgId, Long organizationId, Long[] ids,
			Integer page, Integer size,String glfirstTime,String gllastTime,
			String moistureContent,String colourNumber,String batch,
			String bCreater,Long[] organizationIds) {
		return b2bReturnsDao.findList(sn,storeId,status,sbuId,wfState,firstTime,
				lastTime,flag,saleOrgId,organizationId,ids,page,size,
				glfirstTime,gllastTime,moistureContent,colourNumber,
				batch,bCreater,organizationIds);
	}

	@Override
	public TriplicateForm getTriplicateForm(Long id) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("objId", id));
		filters.add(Filter.eq("type", 0));
		filters.add(Filter.eq("seq", 0));
		List<TriplicateForm> triplicateForms = triplicateFormService.findList(null,
				filters,
				null);
		TriplicateForm triplicateForm = null;
		if (triplicateForms.size() > 0) {
			triplicateForm = triplicateForms.get(0);
		}
		return triplicateForm;
	}

	@Override
	public TriplicateForm createTriplicateForm(Setting setting,
			B2bReturns b2bReturns) throws Exception {
		String siteUrl = setting.getSiteUrl();
		String template_path = "/pdf/b2bReturns/template/b2bReturns_nature.pdf";
		String file_name = "" + UUID.randomUUID();
		String pdfStaticPath = "/pdf/b2bReturns/file/" + file_name + ".pdf";
		this.buildPdf(b2bReturns, pdfStaticPath, template_path);

		/** 保存中间表 */
		String url = siteUrl + pdfStaticPath;
		TriplicateForm triplicateForm = new TriplicateForm(b2bReturns.getId(),
				0, url, 0);
		triplicateFormService.save(triplicateForm);
		return triplicateForm;
	}
	
	@Override
	public TriplicateForm createB2bReturnsTriplicateForm(Setting setting, 
			B2bReturns b2bReturns) throws Exception {
		String siteUrl = setting.getSiteUrl();
		String template_path = "/pdf/b2bReturns/template/b2bReturnsNoprice.pdf";
		String file_name = "" + UUID.randomUUID();
		String pdfStaticPath = "/pdf/b2bReturns/file/" + file_name + ".pdf";
		this.buildPdf(b2bReturns, pdfStaticPath, template_path);

		/** 保存中间表 */
		String url = siteUrl + pdfStaticPath;
		TriplicateForm triplicateForm = new TriplicateForm(b2bReturns.getId(),
				0, url, 0);
		triplicateFormService.save(triplicateForm);
		return triplicateForm;
	}
	
	private String FONT_PATH = "/usr/share/fonts/self/simsun.ttf";

	private void buildPdf(B2bReturns b2bReturns, String pdfStaticPath,
			String template_path) throws Exception {
		String PdfTemplatePath = servletContext.getRealPath(template_path);
		String outputFile = servletContext.getRealPath(pdfStaticPath);
		CompanyInfo companyInfo = companyInfoService.find(b2bReturns.getCompanyInfoId());
		String company_name = companyInfo.getCompany_name();
		String logo = companyInfo.getLogo();
		Store store = b2bReturns.getStore();
		String unit = "";
		String create_date = DateUtil.convert(b2bReturns.getCreateDate(),"yyyy-MM-dd");
		String alias = store.getAlias();
		Warehouse warehouse = b2bReturns.getWarehouse();
		if (alias == null || alias.length() == 0) {
			alias = store.getName();
		}
		FileOutputStream fos = new FileOutputStream(outputFile);// 需要生成PDF
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
		int itemSize = b2bReturnsItems.size();// 2
		int pageSize = 5;
		int pdfPage = itemSize % pageSize == 0 ? itemSize / pageSize : (itemSize / pageSize) + 1;
		ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pdfPage];// 用于存储每页生成PDF流
		BaseFont bf = BaseFont.createFont(FONT_PATH,BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		//订单行是否展示金额  0不展示  非0展示
		int hiddenAmount = 0;
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
		}catch (RuntimeException e) {
			
		}
		for (int i = 0; i < pdfPage; i++) {
			baos[i] = new ByteArrayOutputStream();
			PdfReader reader = new PdfReader(PdfTemplatePath);
			PdfStamper stamp = new PdfStamper(reader, baos[i]);
			/*
			 * BaseFont bf = BaseFont.createFont("F://simsun.ttf",
			 * BaseFont.IDENTITY_H,BaseFont.EMBEDDED);
			 */
			ArrayList<BaseFont> fontList = new ArrayList<BaseFont>();
			fontList.add(bf);
			AcroFields form = stamp.getAcroFields();
			int column = 0;// 列数 3 1-3 4
			int forNumber = i * pageSize + pageSize > itemSize ? itemSize : i
					* pageSize
					+ pageSize;
			form.setSubstitutionFonts(fontList);
			form.setField("sn", b2bReturns.getSn());
			form.setField("create_date", create_date);
			form.setField("warehouse_name",warehouse == null ? "" : warehouse.getName());
			form.setField("company_name", company_name);
			form.setField("page_info", (i + 1) + "");
			form.setField("totalPage", pdfPage + "");
			form.setField("store_alias", alias);
			form.setField("store_sn",ConvertUtil.toEmpty(store.getOutTradeNo()));
			form.setField("store_name", ConvertUtil.toEmpty(store.getName()));
			form.setField("address",ConvertUtil.toEmpty(b2bReturns.getAddress()));
			form.setField("memo", ConvertUtil.toEmpty(b2bReturns.getReason()));
			form.setField("consignee",ConvertUtil.toEmpty(b2bReturns.getConsignee()));
			form.setField("phone",ConvertUtil.toEmpty(b2bReturns.getConsignee())+"/"+ConvertUtil.toEmpty(b2bReturns.getConsigneeMobile()));
			form.setField("erp_remark",ConvertUtil.toEmpty(b2bReturns.getErpRemark()));
			form.setField("store_member_name", b2bReturns.getStoreMember().getName());
			BigDecimal total_quantity = new BigDecimal("0");
			BigDecimal total_box_quantity = BigDecimal.ZERO;
			BigDecimal total_branch_quantity = BigDecimal.ZERO;
			BigDecimal total_amount = BigDecimal.ZERO;
			//零散支数
			BigDecimal total_scattered_quantity = BigDecimal.ZERO;
			for (int j = i * pageSize; j < forNumber; j++) {
				B2bReturnsItem b2bReturnsItem = b2bReturnsItems.get(j);
				Product product = b2bReturnsItem.getProduct();
				unit = product.getUnit();
				BigDecimal quantity = b2bReturnsItem.getQuantity();
				BigDecimal box_quantity = b2bReturnsItem.getBoxQuantity().stripTrailingZeros();
				BigDecimal branch_quantity = b2bReturnsItem.getBranchQuantity().stripTrailingZeros();
				total_quantity = total_quantity.add(quantity);
				total_box_quantity = total_box_quantity.add(box_quantity);
				total_branch_quantity = total_branch_quantity.add(branch_quantity);
				BigDecimal price = b2bReturnsItem.getPrice();
				BigDecimal amount = price.multiply(quantity);
				amount = amount.setScale(3, BigDecimal.ROUND_DOWN);
				total_amount = total_amount.add(amount);
				String quantityString = quantity.toPlainString();
				String amountString = amount.toPlainString();
				String productSn = product == null ? "" : ConvertUtil.toEmpty(product.getVonderCode());
				form.setField("vonder_code_" + column, productSn + "");
				//产品名称 /木种花色
				form.setField("product_name_" + column,
						ConvertUtil.toEmpty(product == null ? ""
								: product.getName()+"/"+product.getWoodTypeOrColor()));
				//经营组织
				form.setField("product_organization_name_" + column,
						ConvertUtil.toEmpty(b2bReturnsItem.getProductOrganization() == null ?
								"" : b2bReturnsItem.getProductOrganization().getName()));
				//型号 
				form.setField("product_model_" + column,
						ConvertUtil.toEmpty(product == null ? ""
								: product.getModel()));
				//规格
				form.setField("product_spec_" + column,
						ConvertUtil.toEmpty(product == null ? ""
								: product.getSpec()));
				//支/件 
				BigDecimal scattered_quantity = b2bReturnsItem.getScatteredQuantity()
						.setScale( 0, BigDecimal.ROUND_DOWN );
				scattered_quantity.stripTrailingZeros();
				total_scattered_quantity = total_scattered_quantity.add(scattered_quantity);
				form.setField("scattered_quantity_" + column,scattered_quantity.toPlainString());
				//色号
				form.setField("colourNumber_" + column,
						ConvertUtil.toEmpty(b2bReturnsItem.getColorNumbers() == null ?
								"" : b2bReturnsItem.getColorNumbers().getValue()));
				//含水率
				form.setField("moistureContent_" + column,
						ConvertUtil.toEmpty(b2bReturnsItem.getMoistureContents() == null ?
								"" : b2bReturnsItem.getMoistureContents().getValue()));
				form.setField("description_" + column,
						ConvertUtil.toEmpty(product.getDescription()));
				String gradeName = b2bReturnsItem.getProductLevel()!=null?b2bReturnsItem.getProductLevel().getValue():"";
				form.setField("product_grade_" + column, gradeName);
				form.setField("quantity_" + column, quantityString);
				form.setField("unit_" + column,
						ConvertUtil.toEmpty(product == null ? ""
								: product.getUnit()));
				form.setField("box_quantity_" + column,
						box_quantity.toPlainString());
				form.setField("branch_quantity_" + column,
						branch_quantity.toPlainString());
				if(!ConvertUtil.isEmpty(hiddenAmount) && hiddenAmount>0){
					form.setField("price_" + column, ConvertUtil.toEmpty(price));
					form.setField("line_ammount_" + column, amountString);
				}else{
					form.setField("price_" + column, "***");
					form.setField("line_ammount_" + column, "***");
				}
				form.setField("warehouse_name_" + column,
						warehouse == null ? "" : warehouse.getName());
				form.setField("erp_remark_" + column, b2bReturns.getErpRemark());
				column++;
			}
			NumberFormat nf = NumberFormat.getInstance();
			form.setField("unit", unit);
			form.setField("total_quantity", total_quantity.toPlainString());
			form.setField("total_box_quantity", nf.format(total_box_quantity));
			form.setField("total_branch_quantity",nf.format(total_branch_quantity));
			if(!ConvertUtil.isEmpty(hiddenAmount) && hiddenAmount>0){
				form.setField("total_ammount", total_amount.toPlainString());
			}else{
				form.setField("total_ammount", "***");
			}
			//件/支
			form.setField("total_scattered_quantity",nf.format(total_scattered_quantity));
			if (logo != null && !"".equals(logo)) {
				try {
					int page = form.getFieldPositions("logo").get(0).page;
					Rectangle signRect = form.getFieldPositions("logo").get(0).position;
					float x = signRect.getLeft();
					float y = signRect.getBottom();
					// 读图片
					// http://pptest.etwowin.com/pdf/new_order/qrImage/qr_ccbac13a-2814-41de-9c95-252d4b443787.jpg
					Image image = Image.getInstance(logo.trim());
					// 获取操作的页面
					PdfContentByte under = stamp.getOverContent(page);
					// 根据域的大小缩放图片
					image.scaleToFit(signRect.getWidth(), signRect.getHeight());
					// 添加图片
					image.setAbsolutePosition(x, y);
					under.addImage(image);
				}
				catch (Exception e) {
					e.printStackTrace();
				}
			}
			// 二维码
			if (form.getFieldPositions("erweima") != null) {
				try {
					String imageStaticPath = "/pdf/image/"
							+ UUID.randomUUID()
							+ ".jpg";
					/*
					 * if (order != null && order.getSn() == null) {
					 * ExceptionUtil.throwServiceException("单号为空，无法生成二维码"); }
					 */
					Setting setting = SettingUtils.get();
					String pdfHttpUrl = setting.getSiteUrl()
							+ "/"
							+ pdfStaticPath;
					String url = createRQ(imageStaticPath, pdfHttpUrl);
					int page = form.getFieldPositions("erweima").get(0).page;
					Rectangle erweimaSignRect = form.getFieldPositions("erweima")
							.get(0).position;
					float erweimaX = erweimaSignRect.getLeft();
					float erweimaY = erweimaSignRect.getBottom();
					String erweima = servletContext.getRealPath(url);
					Image erweimaMage = Image.getInstance(erweima);
					PdfContentByte pdfUnder = stamp.getOverContent(page);
					erweimaMage.scaleToFit(erweimaSignRect.getWidth(),
							erweimaSignRect.getHeight());
					erweimaMage.setAbsolutePosition(erweimaX, erweimaY);
					pdfUnder.addImage(erweimaMage);
				}
				catch (Exception e) {
					e.printStackTrace();
				}
			}
			stamp.setFormFlattening(true); // 千万不漏了这句啊, */
			stamp.close();
		}
		Document doc = new Document();
		PdfCopy pdfCopy = new PdfCopy(doc, fos);
		doc.open();
		PdfImportedPage impPage = null;
		// doc.add(new Paragraph("",font));
		/** 取出之前保存的每页内容 */
		for (int i = 0; i < pdfPage; i++) {
			impPage = pdfCopy.getImportedPage(new PdfReader(
					baos[i].toByteArray()),
					1);
			pdfCopy.addPage(impPage);
		}
		doc.close();// 当文件拷贝 记得作废doc

	}

	/**
	 * 生成二维码(QRCode)图片的公共方法
	 * 
	 * @param content
	 *            存储内容
	 * @param imgType
	 *            图片类型
	 * @param size
	 *            二维码尺寸
	 * @return
	 */
	public BufferedImage qRCodeCommon(String content, String imgType, int size) {
		BufferedImage bufImg = null;
		try {
			Qrcode qrcodeHandler = new Qrcode();
			// 设置二维码排错率，可选L(7%)、M(15%)、Q(25%)、H(30%)，排错率越高可存储的信息越少，但对二维码清晰度的要求越小
			qrcodeHandler.setQrcodeErrorCorrect('M');
			qrcodeHandler.setQrcodeEncodeMode('B');
			// 设置设置二维码尺寸，取值范围1-40，值越大尺寸越大，可存储的信息越大
			qrcodeHandler.setQrcodeVersion(size);
			// 获得内容的字节数组，设置编码格式
			byte[] contentBytes = content.getBytes("utf-8");
			// 图片尺寸
			int imgSize = 67 + 12 * (size - 1);
			bufImg = new BufferedImage(imgSize, imgSize,
					BufferedImage.TYPE_INT_RGB);
			Graphics2D gs = bufImg.createGraphics();
			// 设置背景颜色
			gs.setBackground(Color.WHITE);
			gs.clearRect(0, 0, imgSize, imgSize);

			// 设定图像颜色> BLACK
			gs.setColor(Color.BLACK);
			// 设置偏移量，不设置可能导致解析出错
			int pixoff = 2;
			// 输出内容> 二维码
			if (contentBytes.length > 0 && contentBytes.length < 800) {
				boolean[][] codeOut = qrcodeHandler.calQrcode(contentBytes);
				for (int i = 0; i < codeOut.length; i++) {
					for (int j = 0; j < codeOut.length; j++) {
						if (codeOut[j][i]) {
							gs.fillRect(j * 3 + pixoff, i * 3 + pixoff, 3, 3);
						}
					}
				}
			}
			else {
				throw new Exception("QRCode content bytes  length = "
						+ contentBytes.length
						+ " not in [0, 800].");
			}
			gs.dispose();
			bufImg.flush();
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return bufImg;
	}

	public String createRQ(String url, String sn) throws IOException {

		// Setting setting = SettingUtils.get();
		// String siteUrl = setting.getSiteUrl();
		String content = sn;
		BufferedImage bufImg = qRCodeCommon(content, "jpg", 12);

		File imgFile = new File(servletContext.getRealPath(url));// 二维码
		// 生成二维码QRCode图片
		ImageIO.write(bufImg, "jpg", imgFile);
		return url;
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findOrderItem(String orderSn,
			String vonderCode, String name, Long[] storeId, String mod,
			Long sbuId,Long productOrganizationId, Pageable pageable) {
		return b2bReturnsDao.findOrderItem(orderSn,
				vonderCode,
				name,
				storeId,
				mod,
				sbuId,
				productOrganizationId,
				pageable);
	}
	
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findShippingItem(String ShippingSn,
			String vonderCode, String name, Long[] storeId, String mod,
			Long sbuId,Long productOrganizationId, Pageable pageable) {
		return b2bReturnsDao.findShippingItem(ShippingSn,
				vonderCode,
				name,
				storeId,
				mod,
				sbuId,
				productOrganizationId,
				pageable);
	}

	@Override
	public BigDecimal findReturnQuantity(Long orderItemId, Long returnsItemId) {
		return b2bReturnsDao.findReturnQuantity(orderItemId,returnsItemId);
	}
	
	@Override
	public BigDecimal findReturnQuantitys(String orderItem,String returnsItemId){
		return b2bReturnsDao.findReturnQuantitys(orderItem, returnsItemId);
	}
	
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findTable(String sn,String erpSn, Long[] storeId,
			Long sbuId, Integer[] status,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long organizationId, Long saleOrgId,String glfirstTime,String gllastTime,
			Pageable pageable) {
		return b2bReturnsDao.findTable(sn,
				erpSn,
				storeId,
				sbuId,
				status,
				wfState,
				firstTime,
				lastTime,
				flag,
				organizationId,
				saleOrgId,
				glfirstTime,
				gllastTime,
				pageable);
	}
	
	public void saveIntfAtLogistics(B2bReturns b2bReturn) {
//		Long companyInfoId = b2bReturn.getCompanyInfoId();
//		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
//		if ("nature".equals(companyInfo.getCompany_code())) {//nadev:dzrjj na/natest:nature
			// 大自然物流接口
//			intfOrderMessageToService.saveLogisticsReturnIntf(b2bReturn,0);
//		}
	}

	@Override
	public void saveform(B2bReturns b2bReturns, Integer Type) {
		B2bReturns b = find(b2bReturns.getId());
		if(Type == 0){
			if(b2bReturns.getIsQuality()== null){
				ExceptionUtil.throwServiceException("工厂品管请选择产品质量！");
			}
			List<B2bReturnsAttach> b2bReturnsAttachs = b2bReturns.getB2bReturnsGcpgAttachs();
			/*if(b2bReturnsAttachs==null||b2bReturnsAttachs.size()<3) {
				ExceptionUtil.throwServiceException("请上传不少于3张图片/视频");
			}*/
			for (Iterator<B2bReturnsAttach> iterator = b2bReturnsAttachs.iterator(); iterator.hasNext();) {
				B2bReturnsAttach b2bReturnsAttach = iterator.next();
				if (b2bReturnsAttach == null || b2bReturnsAttach.getUrl() == null) {
					iterator.remove();
					continue;
				}
				if (b2bReturnsAttach.getName() == null) {
					ExceptionUtil.throwServiceException("附件名不能为空");
				}
				b2bReturnsAttach.setFileName(b2bReturnsAttach.getName() + "." + b2bReturnsAttach.getSuffix());
				b2bReturnsAttach.setB2bReturns(b);
				b2bReturnsAttach.setStoreMember(storeMemberService.getCurrent());
			}
			b.getB2bReturnsAttachs().clear();
			b.getB2bReturnsAttachs().addAll(b2bReturnsAttachs);
			b.setIsQuality(b2bReturns.getIsQuality());
			b.setQCopinion(b2bReturns.getQCopinion());

		}
		if(Type == 1){
			if(b2bReturns.getIsQuality1()== null){
				ExceptionUtil.throwServiceException("质量中心请选择产品质量！");
			}
			if (b2bReturns.getQAopinion()==null || b2bReturns.getQAopinion().isEmpty()){
				ExceptionUtil.throwServiceException("请填写质量中心意见！");
			}
			b.setIsQuality1(b2bReturns.getIsQuality1());
			b.setQAopinion(b2bReturns.getQAopinion());
		}
		if(Type == 2){
//			if(b2bReturns.getProductQuantity()== null){
//				ExceptionUtil.throwServiceException("工厂仓管意见中请填写产品验收数量(㎡)");
//			}
//			b.setProductQuantity(b2bReturns.getProductQuantity());
			Map<Long,B2bReturnsItem> map = new HashMap<Long, B2bReturnsItem>();
			List<B2bReturnsItem> items = b2bReturns.getB2bReturnsItems();
			for (B2bReturnsItem item : items) {
				if (item.getFullReturnCount() == null) {
					ExceptionUtil.throwServiceException("请填写大于原规格1/2以上的验收数量（片）！");
				}
				if (item.getIncompletelyReturnCount() == null) {
					ExceptionUtil.throwServiceException("请填写原规格1/3至1/2的验收数量（片）！");
				}
				if (item.getNotReturnCount() == null) {
					ExceptionUtil.throwServiceException("请填写不足原规格1/3的验收数量（片）！");
				}
				map.put(item.getId(), item);
			}
			List<B2bReturnsItem> b2bReturnsItems = b.getB2bReturnsItems();
			for (Iterator iterator = b2bReturnsItems.iterator(); iterator.hasNext();) {
				B2bReturnsItem b2bReturnsItem = (B2bReturnsItem) iterator.next();
				Long id = b2bReturnsItem.getId();
				B2bReturnsItem item = map.get(id);
				b2bReturnsItem.setFullReturnCount(item.getFullReturnCount());
				b2bReturnsItem.setIncompletelyReturnCount(item.getIncompletelyReturnCount());
				b2bReturnsItem.setNotReturnCount(item.getNotReturnCount());
			}
		}
		update(b);
	}
	
	@Transactional
	@Override
	public void auditedUpdateSaleOrgPrice(B2bReturns b2bReturns) {
	
		List<B2bReturnsItem> b2bReturnsItemList = new ArrayList<B2bReturnsItem>();
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
		for (B2bReturnsItem b2bReturnsItem : b2bReturnsItems) {
			if (b2bReturnsItem == null || b2bReturnsItem.getId() == null) {
				continue;
			}
			B2bReturnsItem pB2bReturnsItem = b2bReturnsItemService.find(b2bReturnsItem.getId());
			pB2bReturnsItem.setSaleOrgPrice(b2bReturnsItem.getSaleOrgPrice());
			b2bReturnsItemList.add(pB2bReturnsItem);
		}
		
		B2bReturns pB2bReturns = find(b2bReturns.getId());
		pB2bReturns.getB2bReturnsItems().clear();
		pB2bReturns.getB2bReturnsItems().addAll(b2bReturnsItemList);
		
		update(pB2bReturns);
		
		orderFullLinkService.addFullLink(15,null,pB2bReturns.getSn(),"修改退货单明细平台结算价",null);
	}

	@Transactional
	@Override
	public void close(B2bReturns b2bReturns) throws Exception {
		Integer count = b2bReturnsDao.amShippingItemCount(b2bReturns.getId());
		if(count>0){
			ExceptionUtil.throwServiceException("该单据已入仓，禁止操作关闭按钮");
		}
		b2bReturns.setStatus(8);
		update(b2bReturns);
		orderFullLinkService.addFullLink(15,null,b2bReturns.getSn(),"关闭退货单",null);
		
		//同步退货单信息到link5
		this.saveB2bReturnsIntfLink5Check(b2bReturns,2);
	}
	
	
	/**
	 * 校验退货单状态
	 */
	@Override
	public void checkB2bReturnsState(B2bReturns b2bReturns,Integer status, 
			String statusName, String operationName) {
		
		if(ConvertUtil.isEmpty(b2bReturns)){
			ExceptionUtil.throwServiceException("该退货单不存在");
		}
		if(!ConvertUtil.isEmpty(b2bReturns.getStatus()) && b2bReturns.getStatus() != status){
			ExceptionUtil.throwServiceException("只有单据状态为"+statusName+"的退货单才能"+operationName);
		}
	}

	@Override
	public List<Map<String, Object>> findB2bReturnsItemList(Long[] Ids) {
		return b2bReturnsDao.findB2bReturnsItemList(Ids);
	}
	
	public void savePriceDifference(B2bReturns b2bReturns){
		//存入ABCD类价格
	    List<B2bReturnsItem> b2bReturnsItems=b2bReturns.getB2bReturnsItems();
	    
	    Long memberRankId = null;
	    
	    for(B2bReturnsItem b2bReturnsItem:b2bReturnsItems) {
	        PriceDifference priceDifference = new PriceDifference();
	        priceDifference.setShippingSn(b2bReturns.getSn());
	        priceDifference.setDrawer(b2bReturns.getStoreMember());
	        priceDifference.setSalesman(b2bReturns.getRegionalManager());
	        priceDifference.setCreateDate(b2bReturns.getCreateDate());
	        priceDifference.setSbu(b2bReturns.getSbu());
	        priceDifference.setOrganization(b2bReturns.getOrganization());
	        priceDifference.setStore(b2bReturns.getStore());
	        priceDifference.setWarehouse(b2bReturns.getWarehouse());
	        priceDifference.setSaleOrg(b2bReturns.getSaleOrg());
	        priceDifference.setType(b2bReturns.getBusinessType().getValue());
	        priceDifference.setProductName(b2bReturnsItem.getProduct().getName());
	        priceDifference.setVonderCode(b2bReturnsItem.getProduct().getVonderCode());
	        priceDifference.setProductTwo(b2bReturnsItem.getProduct().getProductTwo());
	        priceDifference.setCategoryName(b2bReturnsItem.getProduct().getProductCategory().getName());
	        priceDifference.setWoodTypeOrColor(b2bReturnsItem.getProduct().getWoodTypeOrColor());
	        priceDifference.setModel(b2bReturnsItem.getProduct().getModel());
	        priceDifference.setLevelName(b2bReturnsItem.getProductLevel().getValue());
	        priceDifference.setProductStructure(b2bReturnsItem.getProduct().getProductStructure()==null?null :b2bReturnsItem.getProduct().getProductStructure().getValue());
	        priceDifference.setBoxQuantity(b2bReturnsItem.getBoxQuantity());
	        priceDifference.setBranchQuantity(b2bReturnsItem.getBranchQuantity());
	        priceDifference.setQuantity(b2bReturnsItem.getQuantity());
	        priceDifference.setSaleOrgPirce(b2bReturnsItem.getSaleOrgPrice());
	        priceDifference.setReSaleOrgPirce(b2bReturnsItem.getReSaleOrgPrice());
	        priceDifference.setPrice(b2bReturnsItem.getPrice());
	        priceDifference.setSpec(b2bReturnsItem.getProduct().getSpec());
	        priceDifference.setKeyCategory(b2bReturnsItem.getProduct().getKeyCategory());
	        priceDifference.setB2bReturnsItem(b2bReturnsItem);
	        priceDifference.setProductOrgPrice(b2bReturnsItem.getProductOrgPrice());

	        priceDifference.setPriceDifference(b2bReturnsItem.getPriceDifference());
	        priceDifference.setZxgk(b2bReturnsItem.getZxgk());
	        priceDifference.setGkjsjc(b2bReturnsItem.getGkjsjc());
	        priceDifference.setZxptjc(b2bReturnsItem.getZxptjc());
	        priceDifference.setQtzx(b2bReturnsItem.getQtzx());
	        priceDifference.setDiscountProjectDescription(b2bReturnsItem.getDiscountProjectDescription() == null ? null : b2bReturnsItem.getDiscountProjectDescription().getValue());



			MemberRank memberRank = 
	        		productPriceService.find(b2bReturnsItem.getPriceId().longValue()).getProductPriceHead().getMemberRank();
	        
			String rankName = StringUtils.trim(memberRank.getName());
			
			priceDifference.setRankName(rankName);
	        
	        memberRankId = memberRank.getId();
	        
	        List<Map<String,Object>> shippedLists = null;
	        
	        //根据价格类型前缀获取ABCD价格信息,剔除所有空格符合
	        String rankNamePrefix = rankName.replaceAll(" ", "");
	        
	        if(rankName.endsWith("A")
	        		||rankName.endsWith("B")
	        		||rankName.endsWith("C")
	        		||rankName.endsWith("D")) {
	        	StringBuffer rankNameSBF = new StringBuffer(rankName);
	        	rankNameSBF.setLength(rankName.length()-1);
	        	rankNamePrefix = rankNameSBF.toString();
	        }
	        
	        if(memberRank.getRankType() == null) {
	        	shippedLists = priceDifferenceService.findShippedList(
	        			b2bReturns.getSaleOrg().getId(),
	        			b2bReturns.getSbu().getId(),
	        			b2bReturns.getWarehouse().getTypeSystemDict().getId(),
	        			b2bReturnsItem.getProductLevel().getId(),
	                    memberRankId,
	                    b2bReturnsItem.getProduct().getId(),
	                    rankNamePrefix);
	        }else {
	        	shippedLists = priceDifferenceService.findShippedList(
	        			b2bReturns.getSaleOrg().getId(),
	        			b2bReturns.getSbu().getId(), 
	        			b2bReturns.getWarehouse().getTypeSystemDict().getId(),
	        			b2bReturnsItem.getProductLevel().getId(),
						memberRankId, 
						b2bReturnsItem.getProduct().getId(),
						memberRank.getRankType().getId(),
						rankNamePrefix);
	        }
	        
	        if(shippedLists.size()>0) {
	            for(Map<String,Object> shippedMap:shippedLists) {
	                if(shippedMap.get("name").toString().contains("A")) {
	                    
	                    priceDifference.setaPrice(shippedMap.get("store_member_price")==null ? BigDecimal.ZERO:new BigDecimal(shippedMap.get("store_member_price").toString()));
	                }else if(shippedMap.get("name").toString().contains("B")) {
	                
	                    priceDifference.setbPrice(shippedMap.get("store_member_price")==null ? BigDecimal.ZERO:new BigDecimal(shippedMap.get("store_member_price").toString()));
	                }else if(shippedMap.get("name").toString().contains("C")) {
	                    
	                    priceDifference.setcPrice(shippedMap.get("store_member_price")==null ? BigDecimal.ZERO:new BigDecimal(shippedMap.get("store_member_price").toString()));
	                }else if(shippedMap.get("name").toString().contains("D")) {
	    
	                    priceDifference.setdPrice(shippedMap.get("store_member_price")==null ? BigDecimal.ZERO:new BigDecimal(shippedMap.get("store_member_price").toString()));
	                }
	                
	            }
	        }
        
	        priceDifferenceService.save(priceDifference);
        
    	}
	}

	/** 通过节点任务回调做节点表单校验 */
	@Transactional
	public void agreePre(ActWf wf) {
		B2bReturns b2bReturns = find(wf.getObjId());
		if (b2bReturns.getWfId() != null &&getCurrTaskByWf(wf) != null) {
			List<Map<String, Object>> item = getWfProcList(wf.getProcInstId());
			for (Map<String, Object> c : item) {
				String val = c.get("activityState")!=null?c.get("activityState").toString():"false";
				System.out.println("getWfState:"+b2bReturns.getWfState()+"               val"+val);
				if(val.equals("1")) {
					String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
					// 工厂品管
					if (rwm.contains("品管")) {
						if (b2bReturns.getIsQuality() == null) {
							ExceptionUtil.throwServiceException("请填写并提交工厂品管意见！");
						}
					}
					// 质量中心处理意见
					if (rwm.contains("质量中心")) {
						if (b2bReturns.getIsQuality1() == null) {
							ExceptionUtil.throwServiceException("请选择并提交质量中心意见！");
						}
					}
					// 工厂仓管
					if (rwm.contains("仓管")) {
						List<B2bReturnsItem> items = b2bReturns.getB2bReturnsItems();
						for (B2bReturnsItem b2bReturnItem : items) {
							if(b2bReturnItem.getFullReturnCount()==null) {
								ExceptionUtil.throwServiceException("请填写大于原规格1/2以上的验收数量（片）！");
							}
							if(b2bReturnItem.getIncompletelyReturnCount()==null) {
								ExceptionUtil.throwServiceException("请填写原规格1/3至1/2的验收数量（片）！");
							}
							if(b2bReturnItem.getNotReturnCount()==null) {
								ExceptionUtil.throwServiceException("请填写不足原规格1/3的验收数量（片）！");
							}
						}
					}
				}
			}
		}

	}
}
