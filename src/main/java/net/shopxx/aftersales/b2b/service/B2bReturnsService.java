package net.shopxx.aftersales.b2b.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.stock.entity.Warehouse;

/**
 * Service - 退货申请
 */
public interface B2bReturnsService extends ActWfBillService<B2bReturns> {

	/**
	 * 退货单审核
	 * 
	 * @param b2bReturns
	 */
	public void check(B2bReturns b2bReturns);

	//public void check_wf(B2bReturns b2bReturns, Long objConfId);
	
	/** 创建流程实例 */
	void createWf(Long id, String modelId, Long objTypeId);

	/**
	 * 退货单作废
	 * 
	 * @param b2bReturns
	 */
	public void cancel(B2bReturns b2bReturns) throws Exception;

	/**
	 * 退货入库
	 * 
	 * @param b2bReturns
	 */
	public Long stockIn(B2bReturns b2bReturns, Warehouse warehouse,
			SaleOrg saleOrg);

	/**
	 * 根据主表id查找对应的细表数据
	 */
	public List<Map<String, Object>> findItemListById(Long id,
			Long[] organizationIds,Boolean isDefault);

	public BigDecimal findAmount(Long storeId, Long saleOrgId);

	public List<Map<String, Object>> findShippingItemListById(Long orderId,
			Long[] returnId);

	/**
	 * 退货单保存
	 * 
	 * @param b2bReturns
	 */
	public void save(B2bReturns b2bReturns, Order order);

	public void updateB2bReturns(B2bReturns b2bReturns);

	public List<Map<String, Object>> findShippingItem(Long storeId,
			String returnSn);

	public Page<Map<String, Object>> findPage(String sn,String erpSn, Long[] storeId,
			Long sbuId, Integer[] status,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long organizationId, Long[] saleOrgId,String glfirstTime,String gllastTime,
			Long[] productId,Pageable pageable,String moistureContent,String colourNumber,String batch,
			String bCreater,Long[] organizationIds, String aftersaleSn, Integer category);

	//查询订单弹出框的数据
	public Page<Map<String, Object>> findOrderItem(String orderSn,
			String vonderCode, String name, Long[] storeId, String mod,
			Long sbuId,Long productOrganizationId, Pageable pageable);
	

	//查询订单弹出框的数据
	public Page<Map<String, Object>> findShippingItem(String shippingSn,
			String vonderCode, String name, Long[] storeId, String mod,
			Long sbuId,Long productOrganizationId, Pageable pageable);

	//查询可退货数量
	public BigDecimal findReturnQuantity(Long orderItemId, Long returnsItemId);

	/**
	 * 查询退货单数量
	 * 
	 * @param sn
	 * @param storeId
	 * @param status
	 * @param firstTime
	 * @param lastTime
	 * @param flag
	 * @param saleOrgId 
	 * @param organizationId
	 * @return
	 */
	public Integer count(String sn, Long[] storeId, Integer[] status,
			Long sbuId,Integer[] wfState, String firstTime, String lastTime, Integer flag,
			Long saleOrgId, Long organizationId, Integer page, Integer size,
			String glfirstTime,String gllastTime,String moistureContent,
			String colourNumber,String batch,String bCreater,Long[] organizationIds);

	/**
	 * 查找退货单
	 * 
	 * @param sn
	 * @param storeId
	 * @param status
	 * @param firstTime
	 * @param lastTime
	 * @param flag
	 * @param saleOrgId 
	 * @param organizationId
	 * @param ids
	 * @param page
	 * @param size
	 * @return
	 */
	public List<Map<String, Object>> findList(String sn, Long[] storeId,
			Integer[] status, Long sbuId,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long saleOrgId, Long organizationId, Long[] ids,
			Integer page, Integer size,String glfirstTime,String gllastTime,
			String moistureContent,String colourNumber,String batch,
			String bCreater,Long[] organizationIds);

	public TriplicateForm getTriplicateForm(Long id);

	public TriplicateForm createTriplicateForm(Setting setting,
			B2bReturns b2bReturns) throws Exception;
	
	public TriplicateForm createB2bReturnsTriplicateForm(Setting setting,
			B2bReturns b2bReturns) throws Exception;
	
	public BigDecimal findReturnQuantitys(String orderItem,String returnsItemId);
	
	public List<Map<String, Object>> findTable(String sn,String erpSn, Long[] storeId,
			Long sbuId, Integer[] status,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long organizationId, Long saleOrgId,String glfirstTime,String gllastTime,
			Pageable pageable);
	
	public void saveIntfAtLogistics(B2bReturns b2bReturns);

	public void saveform(B2bReturns b2bReturns, Integer Type);
	
	
	/**
	 * 退货单审核完单独修改明细平台结算价
	 * @param
	 */
	public void auditedUpdateSaleOrgPrice(B2bReturns b2bReturns);
	
	
	/**
	 * 退货单审核完操作关闭按钮
	 * @param b2bReturns
	 * @throws Exception
	 */
	public void close(B2bReturns b2bReturns) throws Exception;
	
	
	/**
	 * 校验退货单状态
	 * @param b2bReturns
	 * @param status
	 * @param statusName
	 * @param operationName
	 */
	public void checkB2bReturnsState(B2bReturns b2bReturns,Integer status,String statusName,
			String operationName);
	
	
	/**
	 * 根据退货单Id获取明细
	 * @param Ids
	 * @return
	 */
	public List<Map<String, Object>> findB2bReturnsItemList(Long[] Ids);
}
