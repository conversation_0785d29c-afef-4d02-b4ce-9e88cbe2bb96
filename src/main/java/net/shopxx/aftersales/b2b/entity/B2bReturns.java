package net.shopxx.aftersales.b2b.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.PrePersist;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.link5.entity.ConvertibleToAccCustomer;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.order.entity.CustomerContract;
import net.shopxx.order.entity.Order;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.util.SnUtil;

/*
 * Entity - 退货申请
 */
@Entity
@Table(name = "xx_b2b_returns")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_b2b_returns_sequence")
public class B2bReturns extends ActWfBillEntity implements ConvertibleToAccCustomer{

	private static final long serialVersionUID = 1L;

	/* 用户 */
	private StoreMember storeMember;

	/* SBU */
	private Sbu sbu;

	/* 业务类型 取词汇编码为businessType的词汇 */
	private SystemDict businessType;

	/* 客户 */
	private Store store;

	/* 退货状态 0已保存、 1已审核、 2入库中 、3已入库 、4部分退货(erp已回传)、5完全退货(erp已回传)、6已作废 、8已关闭*/
	private Integer status;

	/* 退货原因 */
	private String reason;

	/* 备注 */
	private String memo;

	/* 退货人姓名 */
	private String name;

	/* 退货人电话 */
	private String mobile;

	/* 图片1 */
	private String image1;

	/* 图片2 */
	private String image2;

	/* 图片3 */
	private String image3;

	/* 退款金额 */
	private BigDecimal amount;

	/* 退政策金额 */
	private BigDecimal policyAmount;

	/* 退货单编号 */
	private String sn;

	/* 订单 */
	private Order order;

	/* 机构 */
	private SaleOrg saleOrg;

	/* 业务员 */
	private StoreMember saleman;

	//新增收货人信息字段
	/* 收货人 */
	private String consignee;

	/* 收货人电话 */
	private String consigneeMobile;

	/* 收货地区编码 */
	private String zipCode;

	/* 收货地区 */
	private Area area;

	/* 收货地址 */
	private String address;

	/* 地址外部编号 */
	private String addressOutTradeNo;

	/* 仓库 */
	private Warehouse warehouse;

	/* 审核时间 */
	private Date checkDate;

	/* 组织 */
	private Organization organization;

	/* 回传退款金额 */
	private BigDecimal returnAmount;

	/* 退货明细 */
	private List<B2bReturnsItem> b2bReturnsItems = new ArrayList<B2bReturnsItem>();

	/* 附件项 */
	private List<B2bReturnsAttach> b2bReturnsAttachs = new ArrayList<B2bReturnsAttach>();

	/* erp单号 */
	private String erpSn;

	/* erp备注 */
	private String erpRemark;

	/* 发票抬头 */
	private String invoiceTitle;

	/* erp回传时间 */
	private Date erpDate;

	/* 客户余额 */
	private BigDecimal storeBalance;

	/* 发运方式 */
	private String smethod;

	// 价格类型
	private MemberRank memberRank;
	
	/** GL日期 */
	private Date glDate;
	
	/** 区域经理*/
	private StoreMember regionalManager;
	
	/*********Link四期新增字段xgc2020-01-02*****************/
	/** 退货种类  0其他退货，1售后退货*/
	private Integer category;
	
	/** 售后单据*/
	private Aftersale aftersale;

	/**
	 * link4售后单
	 */
	private String fourAftersaleSn;
	/****************************************************/
	
	/** 工厂品管*/
	private Integer isQuality;
	
	/** 质量中心*/
	private Integer isQuality1;
	
	/** 工厂仓管*/
	private BigDecimal productQuantity;
	
	/** 工厂名字*/
	private String responsibilityFactory;
	
	/** 工厂编号*/
	private Long factoryId;
	
	/** 责任工厂*/
	private String factoryName;
	
	/** 质量中心意见*/
	private String QAopinion;
	
	/** 工厂品管意见*/
	private String QCopinion;
	
	/** 经销商合同*/
	private CustomerContract customerContract;

    /* 附件项 */
    private List<B2bReturnsAttach> b2bReturnsGcpgAttachs = new ArrayList<B2bReturnsAttach>();
	
	/*
	 * 获取 erp回传时间
	 * @return erpDate erp回传时间
	 */
	public Date getErpDate() {
		return erpDate;
	}

	/*
	 * 设置 erp回传时间
	 * @param erpDate erp回传时间
	 */
	public void setErpDate(Date erpDate) {
		this.erpDate = erpDate;
	}

	/*
	 * 获取 erp备注
	 * @return erpRemark erp备注
	 */
	public String getErpRemark() {
		return erpRemark;
	}

	/*
	 * 设置 erp备注
	 * @param erpRemark erp备注
	 */
	public void setErpRemark(String erpRemark) {
		this.erpRemark = erpRemark;
	}

	/*
	 * 获取 回传退款金额
	 * @return returnAmount 回传退款金额
	 */
	public BigDecimal getReturnAmount() {
		return returnAmount;
	}

	/*
	 * 设置 回传退款金额
	 * @param returnAmount 回传退款金额
	 */
	public void setReturnAmount(BigDecimal returnAmount) {
		this.returnAmount = returnAmount;
	}

	public String getConsignee() {
		return consignee;
	}

	public void setConsignee(String consignee) {
		this.consignee = consignee;
	}

	public String getConsigneeMobile() {
		return consigneeMobile;
	}

	public void setConsigneeMobile(String consigneeMobile) {
		this.consigneeMobile = consigneeMobile;
	}

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Area getArea() {
		return area;
	}

	public void setArea(Area area) {
		this.area = area;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getSaleman() {
		return saleman;
	}

	public void setSaleman(StoreMember saleman) {
		this.saleman = saleman;
	}

	/*
	 * 获取 退货人
	 * @date 2017年9月16日
	 * @return storeMember
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	/*
	 * 设置 退货人
	 * @date 2017年9月16日
	 * @param storeMember 退货人
	 */
	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	/*
	 * 获取 客户
	 * @return store
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}

	/*
	 * 设置 客户
	 * @param store 客户
	 */
	public void setStore(Store store) {
		this.store = store;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonIgnore
	@JoinColumn(name = "orders", nullable = true, updatable = false)
	public Order getOrder() {
		return order;
	}

	/*
	 * 设置订单
	 * @param order 订单
	 */
	public void setOrder(Order order) {
		this.order = order;
	}

	/*
	 * 获取 退货状态 0未审核 1已审核 2入库中 3已入库
	 * @date 2017年9月16日
	 * @return status
	 */
	public Integer getStatus() {
		return status;
	}

	/*
	 * 设置 退货状态 0未审核 1已审核 2入库中 3已入库
	 * @date 2017年9月16日
	 * @param status 退货状态 0未审核 1已审核 2入库中 3已入库
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}

	/*
	 * 获取 退货原因
	 * @date 2017年9月16日
	 * @return reason
	 */
	public String getReason() {
		return reason;
	}

	/*
	 * 设置 退货原因
	 * @date 2017年9月16日
	 * @param reason 退货原因
	 */
	public void setReason(String reason) {
		this.reason = reason;
	}

	/*
	 * 获取 备注
	 * @date 2017年9月16日
	 * @return memo
	 */
	public String getMemo() {
		return memo;
	}

	/*
	 * 设置 备注
	 * @date 2017年9月16日
	 * @param memo 备注
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/*
	 * 获取 退货人姓名
	 * @date 2017年9月16日
	 * @return name
	 */
	public String getName() {
		return name;
	}

	/*
	 * 设置 退货人姓名
	 * @date 2017年9月16日
	 * @param name 退货人姓名
	 */
	public void setName(String name) {
		this.name = name;
	}

	/*
	 * 获取 退货人电话
	 * @date 2017年9月16日
	 * @return mobile
	 */
	public String getMobile() {
		return mobile;
	}

	/*
	 * 设置 退货人电话
	 * @date 2017年9月16日
	 * @param mobile 退货人电话
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	/*
	 * 获取 图片1
	 * @date 2017年9月16日
	 * @return image1
	 */
	public String getImage1() {
		return image1;
	}

	/*
	 * 设置 图片1
	 * @date 2017年9月16日
	 * @param image1 图片1
	 */
	public void setImage1(String image1) {
		this.image1 = image1;
	}

	/*
	 * 获取 图片2
	 * @date 2017年9月16日
	 * @return image2
	 */
	public String getImage2() {
		return image2;
	}

	/*
	 * 设置 图片2
	 * @date 2017年9月16日
	 * @param image2 图片2
	 */
	public void setImage2(String image2) {
		this.image2 = image2;
	}

	/*
	 * 获取 图片3
	 * @date 2017年9月16日
	 * @return image3
	 */
	public String getImage3() {
		return image3;
	}

	/*
	 * 设置 图片3
	 * @date 2017年9月16日
	 * @param image3 图片3
	 */
	public void setImage3(String image3) {
		this.image3 = image3;
	}

	/*
	 * 获取 退款金额
	 * @date 2017年9月16日
	 * @return amount
	 */
	public BigDecimal getAmount() {
		return amount;
	}

	/*
	 * 设置 退款金额
	 * @date 2017年9月16日
	 * @param amount 退款金额
	 */
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	/*
	 * 获取 编号
	 * @date 2017年9月16日
	 * @return sn
	 */
	public String getSn() {
		return sn;
	}

	/*
	 * 设置 编号
	 * @date 2017年9月16日
	 * @param sn 编号
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	/*
	 * 获取 机构
	 * @return saleOrg
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	/*
	 * 设置 机构
	 * @param saleOrg 机构
	 */
	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	/*
	 * 获取 退货明细
	 * @date 2017年9月16日
	 * @return b2bReturnsItems
	 */
	@JsonIgnore
	@OneToMany(mappedBy = "b2bReturns", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<B2bReturnsItem> getB2bReturnsItems() {
		return b2bReturnsItems;
	}

	/*
	 * 获取 仓库
	 * @date 2017年8月28日
	 * @return warehouse
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Warehouse getWarehouse() {
		return warehouse;
	}

	/*
	 * 设置 仓库
	 * @date 2017年8月28日
	 * @param warehouse 仓库
	 */
	public void setWarehouse(Warehouse warehouse) {
		this.warehouse = warehouse;
	}

	/*
	 * 设置 退货明细
	 * @date 2017年9月16日
	 * @param b2bReturnsItems 退货明细
	 */
	public void setB2bReturnsItems(List<B2bReturnsItem> b2bReturnsItems) {
		this.b2bReturnsItems = b2bReturnsItems;
	}

	public String getAddressOutTradeNo() {
		return addressOutTradeNo;
	}

	public void setAddressOutTradeNo(String addressOutTradeNo) {
		this.addressOutTradeNo = addressOutTradeNo;
	}

	@JsonIgnore
	@OneToMany(mappedBy = "b2bReturns", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<B2bReturnsAttach> getB2bReturnsAttachs() {
		return b2bReturnsAttachs;
	}

	public void setB2bReturnsAttachs(List<B2bReturnsAttach> b2bReturnsAttachs) {
		this.b2bReturnsAttachs = b2bReturnsAttachs;
	}

	@PrePersist
	public void prePersist() {
		if (ConvertUtil.isEmpty(getSn())) {
			setSn(SnUtil.generateSn());
		}
	}

	public BigDecimal getPolicyAmount() {
		return policyAmount;
	}

	public void setPolicyAmount(BigDecimal policyAmount) {
		this.policyAmount = policyAmount;
	}

	/*
	 * sbu列表
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}

	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}

	/*
	 * 业务类型
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getBusinessType() {
		return businessType;
	}

	public void setBusinessType(SystemDict businessType) {
		this.businessType = businessType;
	}

	/*
	 * 获取 erp单号
	 * @return erpSn erp单号
	 */
	public String getErpSn() {
		return erpSn;
	}

	/*
	 * 设置 erp单号
	 * @param erpSn erp单号
	 */
	public void setErpSn(String erpSn) {
		this.erpSn = erpSn;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	@Column(precision = 21, scale = 6)
	public BigDecimal getStoreBalance() {
		return storeBalance;
	}

	public void setStoreBalance(BigDecimal storeBalance) {
		this.storeBalance = storeBalance;
	}

	public String getSmethod() {
		return smethod;
	}

	public void setSmethod(String smethod) {
		this.smethod = smethod;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public MemberRank getMemberRank() {
		return memberRank;
	}

	public void setMemberRank(MemberRank memberRank) {
		this.memberRank = memberRank;
	}

	/**
	 * 获取商品数量
	 * 
	 * @return 商品数量
	 */
	@Transient
	public BigDecimal getQuantity() {
		BigDecimal quantity = BigDecimal.ZERO;
		if (getB2bReturnsItems() != null) {
			for (B2bReturnsItem b2bReturnsItem : getB2bReturnsItems()) {
				if (b2bReturnsItem != null
						&& b2bReturnsItem.getQuantity() != null) {
					quantity = quantity.add(b2bReturnsItem.getQuantity());
					// quantity += orderItem.getQuantity();
				}
			}
		}
		return quantity;
	}

	/**
	 * 获取已发货数量
	 * 
	 * @return 已发货数量
	 */
	@Transient
	public BigDecimal getReturnedQuantity() {
		BigDecimal returnedQuantity = BigDecimal.ZERO;
		if (getB2bReturnsItems() != null) {
			for (B2bReturnsItem b2bReturnsItem : getB2bReturnsItems()) {
				if (b2bReturnsItem != null
						&& b2bReturnsItem.getReturnedQuantity() != null) {
					returnedQuantity = returnedQuantity.add(b2bReturnsItem.getReturnedQuantity());
					// shippedQuantity += orderItem.getShippedQuantity();
				}
			}
		}
		return returnedQuantity;
	}

	public Date getGlDate() {
		return glDate;
	}

	public void setGlDate(Date glDate) {
		this.glDate = glDate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getRegionalManager() {
		return regionalManager;
	}

	public void setRegionalManager(StoreMember regionalManager) {
		this.regionalManager = regionalManager;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Aftersale getAftersale() {
		return aftersale;
	}

	public void setAftersale(Aftersale aftersale) {
		this.aftersale = aftersale;
	}

	public String getFourAftersaleSn() {
		return fourAftersaleSn;
	}

	public void setFourAftersaleSn(String fourAftersaleSn) {
		this.fourAftersaleSn = fourAftersaleSn;
	}

	public Integer getIsQuality() {
		return isQuality;
	}

	public void setIsQuality(Integer isQuality) {
		this.isQuality = isQuality;
	}
	
	public Integer getIsQuality1() {
		return isQuality1;
	}

	public void setIsQuality1(Integer isQuality1) {
		this.isQuality1 = isQuality1;
	}
	
	public BigDecimal getProductQuantity() {
		return productQuantity;
	}

	public void setProductQuantity(BigDecimal productQuantity) {
		this.productQuantity = productQuantity;
	}
	
	public String getResponsibilityFactory() {
		return responsibilityFactory;
	}

	public void setResponsibilityFactory(String responsibilityFactory) {
		this.responsibilityFactory = responsibilityFactory;
	}
	
	public String getFactoryName() {
		return factoryName;
	}

	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}

	public Long getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(Long factoryId) {
		this.factoryId = factoryId;
	}

	@Length(max = 200)
	public String getQAopinion() {
		return QAopinion;
	}

	public void setQAopinion(String qAopinion) {
		QAopinion = qAopinion;
	}

	@Length(max = 200)
	public String getQCopinion() {
		return QCopinion;
	}

	public void setQCopinion(String qCopinion) {
		QCopinion = qCopinion;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public CustomerContract getCustomerContract() {
		return customerContract;
	}

	public void setCustomerContract(CustomerContract customerContract) {
		this.customerContract = customerContract;
	}

    @JsonIgnore
    @OneToMany(mappedBy = "b2bReturns", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<B2bReturnsAttach> getB2bReturnsGcpgAttachs() {
        return b2bReturnsGcpgAttachs;
    }

    public void setB2bReturnsGcpgAttachs(List<B2bReturnsAttach> b2bReturnsGcpgAttachs) {
        this.b2bReturnsGcpgAttachs = b2bReturnsGcpgAttachs;
    }

	
}
