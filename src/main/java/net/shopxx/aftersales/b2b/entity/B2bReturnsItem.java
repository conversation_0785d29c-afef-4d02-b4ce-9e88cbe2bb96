package net.shopxx.aftersales.b2b.entity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.product.entity.Product;
import net.shopxx.stock.entity.WarehouseBillBatch;

/**
 * Entity - 退货明细
 */
@Entity
@Table(name = "xx_b2b_returns_item")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_b2b_returns_item_sequence")
public class B2bReturnsItem extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 产品编号 */
	private String sn;

	/** 厂商编号 */
	private String vonderCode;

	/** 产品型号 */
	private String model;

	/** 产品名称 */
	private String name;

	/** 产品全称 */
	private String fullName;

	/** 产品缩略图 */
	private String thumbnail;

	/** 产品 */
	private Product product;

	/** 退货单价 */
	private BigDecimal price;

	/** 退货数量 */
	private BigDecimal quantity;

	/** 退货申请 */
	private B2bReturns b2bReturns;

	/** 订单明细 */
	private OrderItem orderItem;

	/** 退货数量 */
	private BigDecimal stockInQuantity;

	/** 下单箱数 */
	private BigDecimal boxQuantity;

	/** 支数 (下单箱数*每箱支数) */
	private BigDecimal branchQuantity;

	/** 零散支数 */
	private BigDecimal scatteredQuantity;

	/** 每支单位数 */
	private BigDecimal perBranch;

	/** 每箱支数 */
	private BigDecimal branchPerBox;

	/** 产品级别:1 优等品,2 二等品 */
	private Integer productGrade;

	/** 木种/花色 */
	private String woodTypeOrColor;
	
	/** 平台结算价 */
	private BigDecimal saleOrgPrice;
	
	//大自然
	/** 回传退货数量 */
	private BigDecimal returnedQuantity;
	
	/** 回传下单箱数 */
	private BigDecimal returnedBoxQuantity;

	/** 回传支数 (下单箱数*每箱支数) */
	private BigDecimal returnedBranchQuantity;
	
	/** 备注*/
	private String memo;
	
	//2019-05-23冯旗 长
    /** 长*/
    private BigDecimal length;
    
    //2019-05-23冯旗 宽
    /** 宽*/
    private BigDecimal width;
    
    /** 发货单明细 */
	private ShippingItem shippingItem;
	
	/** ERP单号*/
	private String erpSn;
	
	/** 发货单号*/
	private String shippingSn;
	
	/** 发货仓*/
	private String warehouseName;
	
	//2019-11-19
	/**色号*/
	private String colourNumber;
	
	/**含水率*/
	private String moistureContent;
	
	/**批次*/
	private String batch;
	
	/**批次编码*/
	private String batchEncoding;

	/**产品级别*/
	private SystemDict productLevel;
	
	/**色号*/
	private SystemDict colorNumbers;
	
	/**含水率*/
	private SystemDict moistureContents;
	
	/**产品经营组织*/
	private Organization productOrganization;
	
	/** 发货单明细批次 */
	List<WarehouseBillBatch> warehouseBillBatchs = new ArrayList<WarehouseBillBatch>();
	
	/** 旗标 */
	private Integer flag;

	/**  产品部结算价 */
    private  BigDecimal productOrgPrice;

	/**  明细行价格对应的价格表ID */
	private  BigDecimal priceId;

	/** 平台结算价原价*/
    private BigDecimal reSaleOrgPrice;

	/** 价格表原价格*/
	private BigDecimal proPriceHeadPrice;

	/** 折扣率*/
	private BigDecimal discount;

	/** 价差*/
	private BigDecimal priceDifference;

    /**  原产品部结算价 */
    private  BigDecimal reProductOrgPrice;

    /** 结算价差*/
    private BigDecimal orgPriceDifference;

    //20210621 工厂仓管验收明细
    /** 按整片验收数量*/
    private Integer fullReturnCount;
    /** 按半片验收数量*/
    private Integer incompletelyReturnCount;
    /** 不做验收数量*/
    private Integer notReturnCount;
	/**
	 * 专项钢扣价差
	 */
	private BigDecimal zxgk;
	/**
	 * 钢扣结算价差
	 */
	private BigDecimal gkjsjc;
	/**
	 * 专项平台方案价差
	 */
	private BigDecimal zxptjc;
	/**
	 * 其他专项
	 */
	private BigDecimal qtzx;

	/**
	 * 优惠项目说明
	 */
	private SystemDict discountProjectDescription;
	/**
     * 获取 平台结算价原价
     * @return reSaleOrgPrice 平台结算价原价
     */
    @JsonProperty
    @Min(0)
    @Column(precision = 21, scale = 2)
    public BigDecimal getReSaleOrgPrice() {
        return reSaleOrgPrice;
    }

    /** 
     * 设置  平台结算价原价
     * @param saleOrgPrice 平台结算价 
     */
    public void setReSaleOrgPrice(BigDecimal reSaleOrgPrice) {
        this.reSaleOrgPrice = reSaleOrgPrice;
    }
    

	/** 
	 * 获取  平台结算价 
	 * @return saleOrgPrice 平台结算价 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getSaleOrgPrice() {
		return saleOrgPrice;
	}
	


	/** 
	 * 设置  平台结算价 
	 * @param saleOrgPrice 平台结算价 
	 */
	public void setSaleOrgPrice(BigDecimal saleOrgPrice) {
		this.saleOrgPrice = saleOrgPrice;
	}
	


	/** 
	 * 获取  回传退货数量 
	 * @return returnedQuantity 回传退货数量 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getReturnedQuantity() {
		return returnedQuantity;
	}
	

	/** 
	 * 设置  回传退货数量 
	 * @param returnedQuantity 回传退货数量 
	 */
	public void setReturnedQuantity(BigDecimal returnedQuantity) {
		this.returnedQuantity = returnedQuantity;
	}
	

	/** 
	 * 获取  回传下单箱数 
	 * @return returnedBoxQuantity 回传下单箱数 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getReturnedBoxQuantity() {
		return returnedBoxQuantity;
	}
	

	/** 
	 * 设置  回传下单箱数 
	 * @param returnedBoxQuantity 回传下单箱数 
	 */
	public void setReturnedBoxQuantity(BigDecimal returnedBoxQuantity) {
		this.returnedBoxQuantity = returnedBoxQuantity;
	}
	

	/** 
	 * 获取  回传支数 (下单箱数每箱支数) 
	 * @return returnedBranchQuantity 回传支数 (下单箱数每箱支数) 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getReturnedBranchQuantity() {
		return returnedBranchQuantity;
	}
	

	/** 
	 * 设置  回传支数 (下单箱数每箱支数) 
	 * @param returnedBranchQuantity 回传支数 (下单箱数每箱支数) 
	 */
	public void setReturnedBranchQuantity(BigDecimal returnedBranchQuantity) {
		this.returnedBranchQuantity = returnedBranchQuantity;
	}
	

	/**
	 * 获取 产品编号
	 * 
	 * @date 2017年9月16日
	 * @return sn
	 */
	public String getSn() {
		return sn;
	}

	/**
	 * 设置 产品编号
	 * 
	 * @date 2017年9月16日
	 * @param sn
	 *            产品编号
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	/**
	 * 获取 厂商编号
	 * 
	 * @date 2017年9月16日
	 * @return vonderCode
	 */
	public String getVonderCode() {
		return vonderCode;
	}

	/**
	 * 设置 厂商编号
	 * 
	 * @date 2017年9月16日
	 * @param vonderCode
	 *            厂商编号
	 */
	public void setVonderCode(String vonderCode) {
		this.vonderCode = vonderCode;
	}

	/**
	 * 获取 产品型号
	 * 
	 * @date 2017年9月16日
	 * @return model
	 */
	public String getModel() {
		return model;
	}

	/**
	 * 设置 产品型号
	 * 
	 * @date 2017年9月16日
	 * @param model
	 *            产品型号
	 */
	public void setModel(String model) {
		this.model = model;
	}

	/**
	 * 获取 产品名称
	 * 
	 * @date 2017年9月16日
	 * @return name
	 */
	public String getName() {
		return name;
	}

	/**
	 * 设置 产品名称
	 * 
	 * @date 2017年9月16日
	 * @param name
	 *            产品名称
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 获取 产品全称
	 * 
	 * @date 2017年9月16日
	 * @return fullName
	 */
	public String getFullName() {
		return fullName;
	}

	/**
	 * 设置 产品全称
	 * 
	 * @date 2017年9月16日
	 * @param fullName
	 *            产品全称
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	/**
	 * 获取 产品缩略图
	 * 
	 * @date 2017年9月16日
	 * @return thumbnail
	 */
	public String getThumbnail() {
		return thumbnail;
	}

	/**
	 * 设置 产品缩略图
	 * 
	 * @date 2017年9月16日
	 * @param thumbnail
	 *            产品缩略图
	 */
	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	/**
	 * 获取 产品
	 * 
	 * @date 2017年9月16日
	 * @return product
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Product getProduct() {
		return product;
	}

	/**
	 * 设置 产品
	 * 
	 * @date 2017年9月16日
	 * @param product
	 *            产品
	 */
	public void setProduct(Product product) {
		this.product = product;
	}

	/**
	 * 获取 退货数量
	 * 
	 * @date 2017年9月16日
	 * @return quantity
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getQuantity() {
		return quantity;
	}

	/**
	 * 设置 退货数量
	 * 
	 * @date 2017年9月16日
	 * @param quantity
	 *            退货数量
	 */
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	/**
	 * 获取 退货申请
	 * 
	 * @date 2017年9月16日
	 * @return b2bReturns
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public B2bReturns getB2bReturns() {
		return b2bReturns;
	}

	/**
	 * 设置 退货申请
	 * 
	 * @date 2017年9月16日
	 * @param b2bReturns
	 *            退货申请
	 */
	public void setB2bReturns(B2bReturns b2bReturns) {
		this.b2bReturns = b2bReturns;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public OrderItem getOrderItem() {
		return orderItem;
	}

	public void setOrderItem(OrderItem orderItem) {
		this.orderItem = orderItem;
	}

	public BigDecimal getStockInQuantity() {
		return stockInQuantity;
	}

	public void setStockInQuantity(BigDecimal stockInQuantity) {
		this.stockInQuantity = stockInQuantity;
	}
	

	/** 
	 * 获取  下单箱数 
	 * @return boxQuantity 下单箱数 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getBoxQuantity() {
		return boxQuantity;
	}
	

	/** 
	 * 设置  下单箱数 
	 * @param boxQuantity 下单箱数 
	 */
	public void setBoxQuantity(BigDecimal boxQuantity) {
		this.boxQuantity = boxQuantity;
	}
	

	/** 
	 * 获取  支数 (下单箱数每箱支数) 
	 * @return branchQuantity 支数 (下单箱数每箱支数) 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getBranchQuantity() {
		return branchQuantity;
	}
	

	/** 
	 * 设置  支数 (下单箱数每箱支数) 
	 * @param branchQuantity 支数 (下单箱数每箱支数) 
	 */
	public void setBranchQuantity(BigDecimal branchQuantity) {
		this.branchQuantity = branchQuantity;
	}
	

	/** 
	 * 获取  零散支数 
	 * @return scatteredQuantity 零散支数 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getScatteredQuantity() {
		return scatteredQuantity;
	}
	

	/** 
	 * 设置  零散支数 
	 * @param scatteredQuantity 零散支数 
	 */
	public void setScatteredQuantity(BigDecimal scatteredQuantity) {
		this.scatteredQuantity = scatteredQuantity;
	}
	

	/** 
	 * 获取  每支单位数 
	 * @return perBranch 每支单位数 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getPerBranch() {
		return perBranch;
	}
	

	/** 
	 * 设置  每支单位数 
	 * @param perBranch 每支单位数 
	 */
	public void setPerBranch(BigDecimal perBranch) {
		this.perBranch = perBranch;
	}
	

	/** 
	 * 获取  每箱支数 
	 * @return branchPerBox 每箱支数 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getBranchPerBox() {
		return branchPerBox;
	}
	

	/** 
	 * 设置  每箱支数 
	 * @param branchPerBox 每箱支数 
	 */
	public void setBranchPerBox(BigDecimal branchPerBox) {
		this.branchPerBox = branchPerBox;
	}
	

	/** 
	 * 获取  产品级别:1 优等品2 二等品 
	 * @return productGrade 产品级别:1 优等品2 二等品 
	 */
	public Integer getProductGrade() {
		return productGrade;
	}
	

	/** 
	 * 设置  产品级别:1 优等品2 二等品 
	 * @param productGrade 产品级别:1 优等品2 二等品 
	 */
	public void setProductGrade(Integer productGrade) {
		this.productGrade = productGrade;
	}
	

	/** 
	 * 获取  木种花色 
	 * @return woodTypeOrColor 木种花色 
	 */
	public String getWoodTypeOrColor() {
		return woodTypeOrColor;
	}
	

	/** 
	 * 设置  木种花色 
	 * @param woodTypeOrColor 木种花色 
	 */
	public void setWoodTypeOrColor(String woodTypeOrColor) {
		this.woodTypeOrColor = woodTypeOrColor;
	}
	

	/**
	 * 持久化前
	 */
	@PrePersist
	public void prePersist() {
		if (getStockInQuantity() == null) {
			setStockInQuantity(BigDecimal.ZERO);
		}
		if (getReturnedQuantity() == null) {
			setReturnedQuantity(BigDecimal.ZERO);
		}
		if (getReturnedBoxQuantity() == null) {
			setReturnedBoxQuantity(BigDecimal.ZERO);
		}
		if (getReturnedBranchQuantity() == null) {
			setReturnedBranchQuantity(BigDecimal.ZERO);
		}
	}

	/**
	 * 更新前
	 */
	@PreUpdate
	public void preUpdate() {
		if (getStockInQuantity() == null) {
			setStockInQuantity(BigDecimal.ZERO);
		}
		if (getReturnedQuantity() == null) {
			setReturnedQuantity(BigDecimal.ZERO);
		}
		if (getReturnedBoxQuantity() == null) {
			setReturnedBoxQuantity(BigDecimal.ZERO);
		}
		if (getReturnedBranchQuantity() == null) {
			setReturnedBranchQuantity(BigDecimal.ZERO);
		}
	}



	public String getMemo() {
		return memo;
	}



	public void setMemo(String memo) {
		this.memo = memo;
	}



	public BigDecimal getLength() {
		return length;
	}



	public void setLength(BigDecimal length) {
		this.length = length;
	}



	public BigDecimal getWidth() {
		return width;
	}



	public void setWidth(BigDecimal width) {
		this.width = width;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	public ShippingItem getShippingItem() {
		return shippingItem;
	}



	public void setShippingItem(ShippingItem shippingItem) {
		this.shippingItem = shippingItem;
	}



	public String getErpSn() {
		return erpSn;
	}



	public void setErpSn(String erpSn) {
		this.erpSn = erpSn;
	}



	public String getShippingSn() {
		return shippingSn;
	}



	public void setShippingSn(String shippingSn) {
		this.shippingSn = shippingSn;
	}



	public String getWarehouseName() {
		return warehouseName;
	}



	public void setWarehouseName(String warehouseName) {
		this.warehouseName = warehouseName;
	}



	public String getColourNumber() {
		return colourNumber;
	}



	public String getMoistureContent() {
		return moistureContent;
	}



	public String getBatch() {
		return batch;
	}



	public void setColourNumber(String colourNumber) {
		this.colourNumber = colourNumber;
	}



	public void setMoistureContent(String moistureContent) {
		this.moistureContent = moistureContent;
	}



	public void setBatch(String batch) {
		this.batch = batch;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getProductLevel() {
		return productLevel;
	}

	public void setProductLevel(SystemDict productLevel) {
		this.productLevel = productLevel;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getProductOrganization() {
		return productOrganization;
	}

	public void setProductOrganization(Organization productOrganization) {
		this.productOrganization = productOrganization;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getColorNumbers() {
		return colorNumbers;
	}

	public void setColorNumbers(SystemDict colorNumbers) {
		this.colorNumbers = colorNumbers;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getMoistureContents() {
		return moistureContents;
	}

	public void setMoistureContents(SystemDict moistureContents) {
		this.moistureContents = moistureContents;
	}

	@JsonIgnore
	@OneToMany(mappedBy = "b2bReturnsItem", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<WarehouseBillBatch> getWarehouseBillBatchs() {
		return warehouseBillBatchs;
	}

	public void setWarehouseBillBatchs(List<WarehouseBillBatch> warehouseBillBatchs) {
		this.warehouseBillBatchs = warehouseBillBatchs;
	}
	
	public String getBatchEncoding() {
		return batchEncoding;
	}

	public void setBatchEncoding(String batchEncoding) {
		this.batchEncoding = batchEncoding;
	}

	public Integer getFlag() {
		return flag;
	}

	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	public BigDecimal getProductOrgPrice() {
		return productOrgPrice;
	}

	public void setProductOrgPrice(BigDecimal productOrgPrice) {
		this.productOrgPrice = productOrgPrice;
	}

	public BigDecimal getPriceId() {
		return priceId;
	}

	public void setPriceId(BigDecimal priceId) {
		this.priceId = priceId;
	}

	public BigDecimal getProPriceHeadPrice() {
		return proPriceHeadPrice;
	}

	public void setProPriceHeadPrice(BigDecimal proPriceHeadPrice) {
		this.proPriceHeadPrice = proPriceHeadPrice;
	}

	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}

	public BigDecimal getPriceDifference() {
		return priceDifference;
	}

	public void setPriceDifference(BigDecimal priceDifference) {
		this.priceDifference = priceDifference;
	}

    public Integer getFullReturnCount() {
        return fullReturnCount;
    }

    public void setFullReturnCount(Integer fullReturnCount) {
        this.fullReturnCount = fullReturnCount;
    }

    public Integer getIncompletelyReturnCount() {
        return incompletelyReturnCount;
    }

    public void setIncompletelyReturnCount(Integer incompletelyReturnCount) {
        this.incompletelyReturnCount = incompletelyReturnCount;
    }

    public Integer getNotReturnCount() {
        return notReturnCount;
    }

    public void setNotReturnCount(Integer notReturnCount) {
        this.notReturnCount = notReturnCount;
    }
	public BigDecimal getZxgk() {
		return zxgk;
	}

	public void setZxgk(BigDecimal zxgk) {
		this.zxgk = zxgk;
	}

	public BigDecimal getGkjsjc() {
		return gkjsjc;
	}

	public void setGkjsjc(BigDecimal gkjsjc) {
		this.gkjsjc = gkjsjc;
	}

	public BigDecimal getZxptjc() {
		return zxptjc;
	}

	public void setZxptjc(BigDecimal zxptjc) {
		this.zxptjc = zxptjc;
	}

	public BigDecimal getQtzx() {
		return qtzx;
	}

	public void setQtzx(BigDecimal qtzx) {
		this.qtzx = qtzx;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getDiscountProjectDescription() {
		return discountProjectDescription;
	}

	public void setDiscountProjectDescription(SystemDict discountProjectDescription) {
		this.discountProjectDescription = discountProjectDescription;
	}

    /** 获取按整片验收的折合数量*/
    @Transient
    public Integer getFullReturnConvertedCount() {
        Integer count = getFullReturnCount();
        return count;
    }
    /** 获取按半片验收的折合数量*/
    @Transient
    public Integer getIncompletelyReturnConvertedCount() {
        if(getIncompletelyReturnCount()==null) {
            return null;
        }
        BigDecimal count = new BigDecimal(getIncompletelyReturnCount());
        //折半向上取整
        BigDecimal convetedCount = count.multiply(new BigDecimal("0.5")).setScale(0, RoundingMode.UP);
        int intValue = convetedCount.intValue();
        return intValue;
    }
    /** 获取不作验收的折合数量*/
    @Transient
    public Integer getNotReturnConvertedCount() {
        Integer count = getNotReturnCount();
        return count==null?null:0;
    }

    /** 获取整片验收的折合面积*/
    @Transient
    public BigDecimal getFullReturnQuantity() {
        Integer fullReturnConvertedCount = getFullReturnConvertedCount();
        if(fullReturnConvertedCount==null) {
            return null;
        }
        BigDecimal quantityPerBranch = getPerBranch();//每片面积
        BigDecimal count = new BigDecimal(fullReturnConvertedCount);//整片验收数量(片)
        BigDecimal fullReturnQuantity = count.multiply(quantityPerBranch);
        return fullReturnQuantity;
    }
    /** 获取按半片验收的折合面积*/
    @Transient
    public BigDecimal getIncompletelyReturnQuantity() {
        Integer incompletelyReturnConvertedCount = getIncompletelyReturnConvertedCount();
        if(incompletelyReturnConvertedCount==null) {
            return null;
        }
        BigDecimal quantityPerBranch = getPerBranch();//每片面积
        BigDecimal count = new BigDecimal(incompletelyReturnConvertedCount);//折半验收折合数量(片)
        BigDecimal incompletelyReturnQuantity = count.multiply(quantityPerBranch);
        return incompletelyReturnQuantity;
    }
    /** 获取不作验收的折合面积*/
    @Transient
    public BigDecimal getNotReturnQuantity() {
        Integer notReturnConvertedCount = getNotReturnConvertedCount();
        if(notReturnConvertedCount==null) {
            return null;
        }
        return BigDecimal.ZERO;
    }

    /** 获取合计验收数量（片）*/
    @Transient
    public Integer getTotalReturnCount() {
        Integer count = null;
        try {
            count = getFullReturnCount()+getIncompletelyReturnCount()+getNotReturnCount();
        }catch (Exception e) {
            LogUtils.error(e);
        }
        return count;
    }
    /** 获取合计折合数量（片）*/
    @Transient
    public Integer getTotalConvertedCount() {
        Integer count = null;
        try {
            count = getFullReturnConvertedCount()+getIncompletelyReturnConvertedCount()+getNotReturnConvertedCount();
        }catch (Exception e) {
            LogUtils.error(e);
        }
        return count;
    }
    /** 获取合计折合面积*/
    @Transient
    public BigDecimal getTotalReturnQuantity() {
        BigDecimal quantity = null;
        try {
            quantity = getFullReturnQuantity().add(getIncompletelyReturnQuantity()).add(getNotReturnQuantity());
        }catch (Exception e) {
            LogUtils.error(e);
        }
        return quantity;
    }
    /** 获取工厂验收明细文字描述*/
    @Transient
    public String getGccgMemo() {
        String code = getVonderCode();
        StringBuilder memo = new StringBuilder("产品编码:"+code+",")
                .append("原规格1/2以上："+getFullReturnCount()+"片、"+getFullReturnQuantity()+"㎡ ；")
                .append("原规格1/3至1/2："+getIncompletelyReturnCount()+"片、"+getIncompletelyReturnQuantity()+"㎡；")
                .append("原规格1/3以下："+getNotReturnCount()+"片，"+getNotReturnQuantity()+"㎡；")
                .append("折合面积"+getTotalReturnQuantity()+"㎡。");
        return memo.toString();
    }

    public BigDecimal getReProductOrgPrice() {
        return reProductOrgPrice;
    }

    public void setReProductOrgPrice(BigDecimal reProductOrgPrice) {
        this.reProductOrgPrice = reProductOrgPrice;
    }

    public BigDecimal getOrgPriceDifference() {
        return orgPriceDifference;
    }

    public void setOrgPriceDifference(BigDecimal orgPriceDifference) {
        this.orgPriceDifference = orgPriceDifference;
    }
    
}
