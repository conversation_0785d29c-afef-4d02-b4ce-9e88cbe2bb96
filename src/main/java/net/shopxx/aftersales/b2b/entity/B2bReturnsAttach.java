package net.shopxx.aftersales.b2b.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

/**
 * 订单附件
 */
@Entity
@Table(name = "xx_b2b_returns_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_b2b_returns_attach_sequence")
public class B2bReturnsAttach extends BaseEntity {

	private static final long serialVersionUID = -3295107659283633954L;

	/** 退货单 */
	private B2bReturns b2bReturns;

	/** 附件URL */
	private String url;

	/** 备注 */
	private String memo;

	/** 文件名 */
	private String fileName;

	/**文件名*/
	private String name;

	/**文件后缀*/
	private String suffix;

	/** 序号 */
	private Integer seq;

	/** 上传人 */
	private StoreMember storeMember;

    /** 类型 0附件信息 1工厂品管附件*/
    private Integer type;

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "b2bReturns", nullable = false, updatable = false)
	public B2bReturns getB2bReturns() {
		return b2bReturns;
	}

	public void setB2bReturns(B2bReturns b2bReturns) {
		this.b2bReturns = b2bReturns;
	}

	/**
	 * @return the url
	 */
	public String getUrl() {
		return url;
	}

	/**
	 * @param url the url to set
	 */
	public void setUrl(String url) {
		this.url = url;
	}

	/**
	 * @return the memo
	 */
	public String getMemo() {
		return memo;
	}

	/**
	 * @param memo the memo to set
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/**
	 * 排序
	 * @return the seq
	 */
	public Integer getSeq() {
		return seq;
	}

	/**
	 * 排序
	 * @param seq the seq to set
	 */
	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSuffix() {
		return suffix;
	}

	public void setSuffix(String suffix) {
		this.suffix = suffix;
	}

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
