package net.shopxx.aftersales.b2b.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.util.RoleJurisdictionUtil;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import math.geom2d.ColinearPoints2DException;

@Repository("b2bReturnsDao")
public class B2bReturnsDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;

	public Page<Map<String, Object>> findPage(String sn,String erpSn, Long[] storeId,
			Long sbuId, Integer[] status,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long organizationId, Long[] saleOrgId,String glfirstTime,String gllastTime,
			Long[] productId,Pageable pageable,String moistureContent,String colourNumber,String batch,
			String bCreater,Long[] organizationIds,String aftersaleSn,Integer category) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select aa.return_amount_item,br.*,so.name sale_org_name,s.name store_name,o.sn order_sn,sb.name sbu_name, "
				+ " 	s.alias store_alias,sm.name storeMemberName,bri.batch_encoding batchEncoding,af.sn af_sn "
				+ " from xx_b2b_returns br "
				+ " left join xx_order o on br.orders=o.id "
                + " left join xx_aftersale af on af.id=br.aftersale "
				+ " left join xx_sale_org so on so.id=br.sale_org "
				+ " left join xx_order_item i on i.orders=o.id"
				+ " left join xx_sbu sb on br.sbu=sb.id "
				+ " left join xx_b2b_returns_item bri on br.id=bri.b2b_returns "
				+ " left join xx_product p on bri.product=p.id "
				+ " left join xx_store s on br.store=s.id "
				+ " left join xx_organization po on po.id = bri.product_organization "
				+ " left join xx_store_member sm ON br.store_member=sm.id "
				+ " left join ("
				+ "      select a.id, sum(round(ifnull(b.price*b.returned_quantity, 0),2)) return_amount_item "
				+ "      from xx_b2b_returns_item b left join xx_b2b_returns a on b.b2b_returns=a.id and a.status in (4,5)  "
				+ "      GROUP BY a.id) aa on aa.id=br.id "
				+ " where 1=1");
		
		//经营组织
		if (!ConvertUtil.isEmpty(organizationIds) && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1) {
                    os += organizationIds[i];
                } else {
                    os += organizationIds[i] + ",";
                }
			}
			sql.append(" and po.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(bCreater) ) {
			sql.append(" and sm.name like ? ");
			list.add("%" + bCreater.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(moistureContent) ) {
			sql.append(" and bri.moisture_content like ? ");
			list.add("%" + moistureContent.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(colourNumber) ) {
			sql.append(" and bri.colour_number like ? ");
			list.add("%" + colourNumber.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(batch) ) {
			sql.append(" and bri.batch_encoding like ? ");
			list.add("%" + batch.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and br.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(erpSn)) {
			sql.append(" and br.erp_sn = ?");
			list.add(erpSn);
		}
		if (!ConvertUtil.isEmpty(category)) {
			sql.append(" and br.category = ?");
			list.add(category);
		}
        if (!ConvertUtil.isEmpty(aftersaleSn)) {
            sql.append(" and af.sn like ?");
            list.add("%" + aftersaleSn + "%");
        }
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			sql.append("  and s.id in (" + s + ")");
		}
        if (!ConvertUtil.isEmpty(saleOrgId) && saleOrgId.length > 0) {
            String so = "";
            for (int i = 0; i < saleOrgId.length; i++) {
                if (i == saleOrgId.length - 1)
                    so += saleOrgId[i];
                else
                    so += saleOrgId[i] + ",";
            }
            sql.append(" and br.sale_org in (" + so + ")");
        }
		if (organizationId != null) {
			sql.append(" and po.id = ?");
			list.add(organizationId);
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and br.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and br.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (!ConvertUtil.isEmpty(glfirstTime)) {
			sql.append(" and br.gl_date >= ?");
			list.add(DateUtil.convert(glfirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(gllastTime)) {
			sql.append(" and br.gl_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(gllastTime + " 00:00:00"),1));
		}
		// 退货入库
		if (flag != null && flag == 2) {
			sql.append(" and br.status>0");
		}
		if (wfState != null && wfState.length > 0) {
			String os = "";
			for (int i = 0; i < wfState.length; i++) {
				if (i == wfState.length - 1)
					os += wfState[i];
				else
					os += wfState[i] + ",";
			}
			sql.append(" and br.wf_state in (" + os + ")");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and br.status in (" + os + ")");
		}
		if (productId != null && productId.length > 0) {
			String fs = "";
			for (int i = 0; i < productId.length; i++) {
				if (i == productId.length - 1)
					fs += productId[i];
				else
					fs += productId[i] + ",";
			}
			sql.append(" and bri.product in (" + fs + ")");
		}
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		//基于用户
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
				String storeAuth = storeMemberService.storeAuth();
				if (storeAuth != null) {
					sql.append(" and br.store in (" + storeAuth + ")");
				}
		}
		if (companyInfoId != null) {
			sql.append(" and br.company_info_id = ?");
			list.add(companyInfoId);

		}
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		sql.append(" group by br.id ");
		sql.append(" order by br.create_date desc");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
//        System.out.println("退货申请：  "+sql.toString());
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);

		return page;
	}

	/**
	 * 根据主表id查找对应的细表数据
	 */
	public List<Map<String, Object>> findItemListById(Long id,
			Long[] organizationIds,Boolean isDefault) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		String sql = "select bri.*,bri.sn orderSn,p.weight,p.volume,p.spec,p.unit,p.manufactory_name,p.supplier,pc.name product_category_name,bri.price_id ppriceId,"
				+ " p.detail_description description,p.product_grade,sdt.id level_Id,sdt.value levelName,bri.color_numbers,bri.moisture_contents,bri.pro_price_head_price originalPrice,bri.discount,"
				+ " po.id product_organization_id,po.name product_organization_name,cn.id colour_number_id,cn.value colour_number_name, dpd.value discount_project_description_value,dpd.id discount_project_description, "
				+ " mc.id moisture_content_id,mc.value moisture_content_name "
				+ " from xx_b2b_returns_item bri"
				+ " left join xx_system_dict sdt on sdt.id = bri.product_level"
				+ " left join xx_b2b_returns br on bri.b2b_returns=br.id"
				+ " left join xx_product p on bri.product=p.id"
				+ " left join xx_product_category pc on p.product_category=pc.id"
				+ " left join xx_organization po ON po.id = bri.product_organization"
				+ " left join xx_system_dict cn on cn.id = bri.color_numbers"
				+ " left join xx_system_dict mc on mc.id = bri.moisture_contents"
				+ " left join xx_system_dict dpd on dpd.id = bri.discount_project_description"
                + " LEFT JOIN xx_order_item oi ON oi.id=bri.order_item "
                + " LEFT JOIN xx_order o ON oi.orders = o.id  "
				+ " where 1=1 ";
		
		if(!ConvertUtil.isEmpty(id)){
			sql += " and b2b_returns = ? ";
			list.add(id);
		}
		//经营组织
		if (!ConvertUtil.isEmpty(organizationIds) && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql +=" and po.id in (" + os + ")";
		}
		if(!ConvertUtil.isEmpty(isDefault)){
			if(isDefault){
				//用户经营组织
				String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
				if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
					if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
						sql += " and po.id in (" + organizationIdS + ") ";
					}else{
						sql += " and po.id is null ";
					}
				}
			}
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}

	public BigDecimal findAmount(Long storeId, Long saleOrgId) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select sum(r.amount) amount from xx_b2b_returns r");
		sql.append(" where status = 3 and company_info_id = ?");
		list.add(companyInfoId);
		if (storeId != null) {
			sql.append(" and r.store = ?");
			list.add(storeId);
		}
		if (saleOrgId != null) {
			sql.append(" and r.store in (select id from xx_store where sale_org = ?)");
			list.add(saleOrgId);
		}


		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Map<String, Object> map = getNativeDao().findSingleMap(sql.toString(),
				objs);
		BigDecimal amount = BigDecimal.ZERO;
		if (map != null && map.get("amount") != null) {
			amount = new BigDecimal(map.get("amount").toString());
		}

		return amount;
	}

	public List<Map<String, Object>> findShippingItemListById(Long id,
			Long[] returnId) {
		List<Object> list = new ArrayList<Object>();
		String sql = "SELECT oi.*,oi.shipped_quantity-sum(IFNULL(ri.quantity,0)) r_quantity,oi.id item_id,"
				+ " sdt.id level_Id,sdt.value levelName"
				+ " from xx_order_item oi "
				+ " left join xx_b2b_returns_item ri on ri.order_item=oi.id"
				+ " left join xx_system_dict sdt ON sdt.id = oi.product_level";

		if (returnId != null && returnId.length > 0) {
			String t = "";
			for (int i = 0; i < returnId.length; i++) {
				if (i == returnId.length - 1)
					t += returnId[i];
				else
					t += returnId[i] + ",";
			}
			sql += " and ri.id not in (" + t + ")";
		}

		sql += " where oi.orders=? group by oi.id having r_quantity > 0";
		list.add(id);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}

	public List<Map<String, Object>> findShippingItem(Long storeId,
			String returnSn) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "SELECT ri.*,r.sn return_sn,s.name store_name,ri.quantity-ri.stock_in_quantity s_quantity from xx_b2b_returns_item ri"
				+ " LEFT JOIN xx_b2b_returns r on ri.b2b_returns=r.id"
				+ " left join xx_store s on s.id=r.store"
				+ " WHERE r.status=1 and ri.quantity-ri.stock_in_quantity>0";
		if (companyInfoId != null) {
			sql += " and ri.company_info_id = ?";
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(returnSn)) {
			sql += " and r.sn like ?";
			list.add("%" + returnSn + "%");
		}
		if (storeId != null) {
			sql += " and s.id = ?";
			list.add(storeId);
		}
		sql += " order by ri.create_date desc";

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}

	public List<Map<String, Object>> findList(String sn, Long[] storeId,
			Integer[] status, Long sbuId,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long saleOrgId, Long organizationId, Long[] ids,
			Integer page, Integer size,String glfirstTime,String gllastTime,
			String moistureContent,String colourNumber,String batch,
			String bCreater,Long[] organizationIds) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select br.*,so.id sale_org_id,so.name sale_org_name,o.sn order_sn,s.name store_name,s.alias store_alias,s.out_trade_no store_out_trade_no,w.name warehouse_name,sd.value warehouseType,a.full_name area_full_name,"
				+ " bri.vonder_code PvonderCode,bri.name Pname,bri.model Pmodel,p.description Pdescription,bri.wood_type_or_color pwtoc,bri.product_grade pGrade,bri.price Pprice,p.spec Pspec,pc.name product_category_name,"
				+ " bri.quantity Pquantity,bri.stock_in_quantity reQuantity,bri.box_quantity,bri.branch_quantity,bri.scattered_quantity,bri.per_branch,bri.branch_per_box,bri.sale_org_price,bri.returned_quantity,"
				+ " bri.returned_box_quantity,bri.returned_branch_quantity,sii.nashuishibiehao,sm.name store_member_name,(bri.price * bri.returned_quantity) lineAmount,sb.name sbu_name,"
				+ " bri.batch_encoding,sdt.id level_Id,sdt.value levelName,sm.name storeMemberName,po.id product_organization_id,po.name product_organization_name,ywlx.value business_name, "
				+ " cn.id color_numbers_id,cn.value color_numbers_name,mc.id moisture_content_id,mc.value moisture_content_name "
				+ " from xx_b2b_returns br"
				+ " left join xx_order o on br.orders=o.id "
				+ " left join xx_order_item io on io.orders=o.id"	
				+ " left join xx_sale_org so on so.id=br.sale_org "
				+ " left join xx_store s on br.store=s.id"
				+ " left join xx_warehouse w on br.warehouse=w.id"
				+ " left join xx_system_dict sd on sd.id=w.type_system_dict"
				+ " left join xx_area a on br.area = a.id"
				+ " left join xx_b2b_returns_item bri on bri.b2b_returns = br.id"
			    + " left join xx_system_dict sdt ON sdt.id = bri.product_level"
				+ " left join xx_product p on p.id = bri.product"
				+ " left join xx_product_category pc on p.product_category = pc.id"
				+ " left join xx_store_invoice_info sii on sii.store = s.id"
				+ " left join xx_organization po on po.id = bri.product_organization"
				+ " left join xx_store_member sm on br.store_member=sm.id"
				+ " left join xx_sbu sb on br.sbu=sb.id "
				+ " left join xx_system_dict ywlx on ywlx.id = br.business_type "
				+ " left join xx_system_dict cn on cn.id = bri.color_numbers "
				+ " left join xx_system_dict mc on mc.id = bri.moisture_contents "
				+ " where 1=1");
		//经营组织
		if (!ConvertUtil.isEmpty(organizationIds) && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql.append(" and po.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(bCreater) ) {
			sql.append(" and sm.name like ? ");
			list.add("%" + bCreater.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(moistureContent) ) {
			sql.append(" and io.moisture_content like ? ");
			list.add("%" + moistureContent.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(colourNumber) ) {
			sql.append(" and io.colour_number like ? ");
			list.add("%" + colourNumber.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(batch) ) {
			sql.append(" and bri.batch_encoding like ? ");
			list.add("%" + batch.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and br.sn like ?");
			list.add("%" + sn + "%");
		}
		if (wfState != null && wfState.length > 0) {
			String os = "";
			for (int i = 0; i < wfState.length; i++) {
				if (i == wfState.length - 1)
					os += wfState[i];
				else
					os += wfState[i] + ",";
			}
			sql.append(" and br.wf_state in (" + os + ")");
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			sql.append("  and s.id in (" + s + ")");
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (saleOrgId != null) {
			sql.append(" and so.id= ?");
			list.add(saleOrgId);
		}
		if (organizationId != null) {
			sql.append(" and po.id = ?");
			list.add(organizationId);
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and br.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and br.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (!ConvertUtil.isEmpty(glfirstTime)) {
			sql.append(" and br.gl_date >= ?");
			list.add(DateUtil.convert(glfirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(gllastTime)) {
			sql.append(" and br.gl_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(gllastTime + " 00:00:00"),1));
		}
		// 退货入库
		if (flag != null && flag == 2) {
			sql.append(" and br.status>0");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and br.status in (" + os + ")");
		}

		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and br.store in (" + storeAuth + ")");
			}
		}
		if (companyInfoId != null) {
			sql.append(" and br.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and br.id in (" + inIds + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		sql.append(" group by bri.id order by br.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return maps;
	}

	public Integer count(String sn, Long[] storeId, Integer[] status,Long sbuId,Integer[] wfState,
			String firstTime, String lastTime, Integer flag,Long saleOrgId, 
			Long organizationId, Integer page, Integer size,String glfirstTime,
			String gllastTime,String moistureContent,String colourNumber,String batch,
			String bCreater,Long[] organizationIds) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select 1 from xx_b2b_returns_item bri"
				+ " left join xx_b2b_returns br on bri.b2b_returns = br.id"
				+ " left join xx_order o on br.orders = o.id "
				+ " left join xx_order_item i on i.orders=o.id"
				+ " left join xx_store s on br.store = s.id"
				+ " left join xx_warehouse w on br.warehouse = w.id"
				+ " left join xx_system_dict sd on sd.id = w.type_system_dict"
				+ " left join xx_sale_org so on so.id = br.sale_org"
				+ " left join xx_area a on br.area = a.id"
				+ " left join xx_product p on p.id = bri.product"
				+ " left join xx_product_category pc on p.product_category = pc.id"
				+ " left join xx_store_invoice_info sii on sii.store = s.id"
				+ " left join xx_organization po on po.id = bri.product_organization"
				+ " left join xx_store_member sm on br.store_member=sm.id"
				+ " left join xx_sbu sb on br.sbu=sb.id "
				+ " where 1=1");
		//经营组织
		if (!ConvertUtil.isEmpty(organizationIds) && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql.append(" and po.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(bCreater) ) {
			sql.append(" and sm.name like ? ");
			list.add("%" + bCreater.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(moistureContent) ) {
			sql.append(" and i.moisture_content like ? ");
			list.add("%" + moistureContent.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(colourNumber) ) {
			sql.append(" and i.colour_number like ? ");
			list.add("%" + colourNumber.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(batch) ) {
			sql.append(" and i.batch like ? ");
			list.add("%" + batch.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and br.sn like ?");
			list.add("%" + sn + "%");
		}
		if (wfState != null && wfState.length > 0) {
			String os = "";
			for (int i = 0; i < wfState.length; i++) {
				if (i == wfState.length - 1)
					os += wfState[i];
				else
					os += wfState[i] + ",";
			}
			sql.append(" and br.wf_state in (" + os + ")");
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			sql.append("  and s.id in (" + s + ")");
		}
		//条件查询添加sbu
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (organizationId != null) {
			sql.append(" and po.id = ?");
			list.add(organizationId);
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and br.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and br.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (!ConvertUtil.isEmpty(glfirstTime)) {
			sql.append(" and br.gl_date >= ?");
			list.add(DateUtil.convert(glfirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(gllastTime)) {
			sql.append(" and br.gl_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(gllastTime + " 00:00:00"),1));
		}
		// 退货入库
		if (flag != null && flag == 2) {
			sql.append(" and br.status>0");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and br.status in (" + os + ")");
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and br.store in (" + storeAuth + ")");
			}
		}
		if (companyInfoId != null) {
			sql.append(" and br.company_info_id = ?");
			list.add(companyInfoId);
		}
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}

		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		sql.append(" group by bri.id order by br.create_date desc");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		
		StringBuilder params = new StringBuilder();
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
			params.append(list.get(i)+" ,");
		}
		
		String totalsql = "select count(1) from ( " + sql + ") t";
		
		
		Integer count = getNativeDao().findInt(totalsql.toString(), objs);

		return count;
	}

	//查询可退货数量
	public BigDecimal findReturnQuantity(Long orderItemId, Long returnsItemId) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();

		sql.append("SELECT i.shipped_quantity - IFNULL(pp.quantity,0) quantity "
				+ " FROM xx_order_item i "
				+ "LEFT JOIN (SELECT t1.id returnsItemId, t1.order_item,SUM(t1.quantity) quantity FROM xx_b2b_returns t "
				+ "LEFT JOIN xx_b2b_returns_item t1 ON t.id = t1.b2b_returns "
				+ "WHERE t.`status` != 6 and t1.order_item is not null GROUP BY t1.order_item ) pp ON pp.order_item = i.id ");
		if(returnsItemId!=null){
			sql.append(" and pp.returnsItemId <> "+returnsItemId.toString());
		}		
		sql.append(" where 1=1");
		if (orderItemId != null) {
			sql.append(" and i.id = ?");
			list.add(orderItemId);
		}
		if (companyInfoId != null) {
			sql.append(" and i.company_info_id = ?");
			list.add(companyInfoId);

		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Map<String, Object> map = getNativeDao().findSingleMap(sql.toString(),
				objs);
		BigDecimal returnQuantity = BigDecimal.ZERO;
		if (map != null && map.get("quantity") != null) {
			returnQuantity = new BigDecimal(map.get("quantity").toString());
		}
		return returnQuantity;
	}

	//查询已完成已审核订单的明细
	public Page<Map<String, Object>> findOrderItem(String sn,
			String vonderCode, String name, Long[] storeId, String mod,
			Long sbuId,Long productOrganizationId, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();

		sql.append("SELECT o.sn orderSn,i.id order_item,i.id item_id,i.seq,i.vonder_code,i.`name`,i.model,i.discount,i.pro_price_head_price originalPrice,i.product_org_price,i.price_id ppriceId,i.re_product_org_price,i.org_price_difference,i.re_sale_org_price,"
				+ "p.id,p.detail_description description,p.unit,p.manufactory_name,p.supplier,i.price_difference price_difference,i.product_grade,i.price member_price,i.sale_org_price,i.zxgk,i.zxptjc,i.qtzx,i.gkjsjc,i.discount_project_description,dpd.value discount_project_description_value, "
				+ "i.shipped_quantity - IFNULL(pp.quantity,0) quantity,i.branch_per_box,i.length,i.width,p.erp_length,p.erp_width,"
				+ "  case pc.tree_path when pc.tree_path like '%353%' then 1 else 0 end isWare,sdt.value levelName,sdt.id level_Id, "
				+ "i.per_branch,i.branch_quantity  - IFNULL(pp.branch_quantity,0) branch_quantity,i.box_quantity  - IFNULL(pp.box_quantity,0) box_quantity, i.branch_quantity - IFNULL( pp.branch_quantity, 0 ) - i.branch_per_box * (i.box_quantity - IFNULL( pp.box_quantity, 0 ) ) scattered_quantity, "
				+ "pc.name product_category_name,i.batch,i.colour_number,i.moisture_content,po.id product_organization_id,po.name product_organization_name,  "
				+ " cn.id colour_number_id,cn.value colour_number_name,mc.id moisture_content_id,mc.value moisture_content_name "
				+ " FROM xx_order o "
				+ "left join xx_order_item i on i.orders=o.id "
				+ "left join xx_system_dict sdt on sdt.id = i.product_level "
				+ "left join xx_store s on o.stores=s.id "
				+ "left join xx_sale_org so on o.sale_org=so.id "
				+ "left join xx_sbu sb on sb.id=o.sbu "
				+ "left join xx_product p on i.product=p.id "
				+ "left join xx_product_category pc on p.product_category = pc.id "
				+ "left join xx_organization po on po.id = i.product_organization "
				+ "left join xx_system_dict cn on cn.id = i.colour_numbers "
				+ "left join xx_system_dict mc on mc.id = i.moisture_contents "
				+ "left join xx_system_dict dpd on dpd.id = i.discount_project_description "
				+ "left join (SELECT t1.order_item,SUM(t1.quantity) quantity, SUM(t1.branch_quantity) branch_quantity, SUM(t1.box_quantity) box_quantity FROM xx_b2b_returns t "
				+ "left join xx_b2b_returns_item t1 ON t.id = t1.b2b_returns "
				+ "WHERE t.`status` != 6 and t1.order_item is not null GROUP BY t1.order_item ) pp ON pp.order_item = i.id "
				+ "WHERE o.order_status IN(2,6) "
				+ "and i.shipped_quantity - IFNULL(pp.quantity,0) > 0 ");
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			sql.append("  and s.id in (" + s + ")");
		}
		if (companyInfoId != null) {
			sql.append(" and i.company_info_id = ?");
			list.add(companyInfoId);

		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and i.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and i.name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(mod)) {
			sql.append(" and i.model like ?");
			list.add("%" + mod + "%");
		}
		//产品经营组织
		if (!ConvertUtil.isEmpty(productOrganizationId)) {
			sql.append(" and po.id = ?");
			list.add(productOrganizationId);
		}
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		sql.append(" group by i.id ");

        System.out.println("退货参考订单查询的sql::"+sql.toString());
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list. get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);

		return page;
	}
	
	//查询已完成已审核发货的明细
	public Page<Map<String, Object>> findShippingItem(String sn,
				String vonderCode, String name, Long[] storeId, String mod,
				Long sbuId,Long productOrganizationId, Pageable pageable) {
			List<Object> list = new ArrayList<Object>();
			Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
			StringBuilder sql = new StringBuilder();

			sql.append("SELECT s.sn shippingSn,s.erp_sn,si.id shipping_item,si.vonder_code,si.`name`,si.model,xw.name warehouse_name,"
					+ "p.id,p.description,p.unit,p.manufactory_name,p.supplier,oi.product_grade,si.price member_price,sale_org_price,"
					+ "si.shipped_quantity - IFNULL(pp.quantity,0) quantity,si.branch_per_box,si.length,si.width,p.erp_length,p.erp_width, "
					+ "si.per_branch,si.branch_quantity  - IFNULL(pp.branch_quantity,0) branch_quantity,"
					+ "si.box_quantity  - IFNULL(pp.box_quantity,0) box_quantity,pc.name product_category_name, "
					+ "si.batch,sdt.id level_Id,sdt.value levelName,po.id product_organization_id,po.name product_organization_name, "
					+ "cn.id colour_number_id,cn.value colour_number_name,mc.id moisture_content_id,mc.value moisture_content_name "
				+ " from xx_shipping_item si  "
				+ "left join xx_shipping s on si.shipping=s.id "
				+ "left join xx_order_item oi on si.order_item=oi.id "
				+ "left join xx_system_dict sdt on sdt.id = oi.product_level "
				+ "left join xx_store xs on s.stores=xs.id "
				+ "left join xx_sale_org so on so.id=s.sale_org  "
				+ "left join xx_sbu sb on sb.id=s.sbu  "
				+ "left join xx_warehouse xw on xw.id=s.warehouse  "
				+ "left join xx_product p on si.product=p.id "
				+ "left join xx_product_category pc on p.product_category = pc.id "
				+ "left join xx_organization po on po.id = oi.product_organization "
				+ "left join xx_system_dict cn on cn.id = si.colour_numbers "
				+ "left join xx_system_dict mc on mc.id = si.moisture_contents "
				+ "left join (SELECT t1.shipping_item,SUM(t1.quantity) quantity, SUM(t1.branch_quantity) branch_quantity, SUM(t1.box_quantity) box_quantity FROM xx_b2b_returns t  "
				+ "left join xx_b2b_returns_item t1 ON t.id = t1.b2b_returns  "
				+ "WHERE t.`status` != 6 and t1.shipping_item is not null GROUP BY t1.shipping_item ) pp ON pp.shipping_item = si.id  "
				+ "WHERE s.status IN(3,4) "
				+ " and si.shipped_quantity - IFNULL(pp.quantity,0) > 0 ");
			
			if (!ConvertUtil.isEmpty(sn)) {
				sql.append(" and s.sn like ?");
				list.add("%" + sn + "%");
			}
			if (!ConvertUtil.isEmpty(sbuId)) {
				sql.append(" and sb.id = ?");
				list.add(sbuId);
			}
			if (storeId != null && storeId.length > 0) {
				String s = "";
				for (int i = 0; i < storeId.length; i++) {
					if (i == storeId.length - 1)
						s += storeId[i];
					else
						s += storeId[i] + ",";
				}
				sql.append("  and xs.id in (" + s + ")");
			}
			if (companyInfoId != null) {
				sql.append(" and si.company_info_id = ?");
				list.add(companyInfoId);
			}
			if (!ConvertUtil.isEmpty(vonderCode)) {
				sql.append(" and si.vonder_code like ?");
				list.add("%" + vonderCode + "%");
			}
			if (!ConvertUtil.isEmpty(name)) {
				sql.append(" and si.name like ?");
				list.add("%" + name + "%");
			}
			if (!ConvertUtil.isEmpty(mod)) {
				sql.append(" and si.model like ?");
				list.add("%" + mod + "%");
			}
			//产品经营组织
			if (!ConvertUtil.isEmpty(productOrganizationId)) {
				sql.append(" and po.id = ?");
				list.add(productOrganizationId);
			}
			//用户机构
			String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
				if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
					sql.append(" and so.id in (" + saleOrgIds + ")");
				}else{
					sql.append(" and so.id is null");
				}
			}
			
			//用户Sbu
			String sbuIds = roleJurisdictionUtil.getSbuIds();
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
				if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
					sql.append(" and sb.id in (" + sbuIds + ")");
				}else{
					sql.append(" and sb.id is null");
				}
			}
			
			//用户经营组织
			String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
				if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
					sql.append(" and po.id in (" + organizationIdS + ")");
				}else{
					sql.append(" and po.id is null");
				}
			}
			
			sql.append(" group by si.id ");
			
			Object[] objs = new Object[list.size()];
			for (int i = 0; i < list.size(); i++) {
				objs[i] = list.get(i);
			}

			Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
					objs,
					pageable);

			return page;
		}
	
	//查询使用数量
	public BigDecimal findReturnQuantitys(String orderItemId, String returnsItemId) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		
		sql.append("SELECT SUM(it.quantity) quantity FROM xx_b2b_returns t LEFT JOIN xx_b2b_returns_item it on it.b2b_returns = t.id WHERE 1=1 ");
		sql.append(" AND t.`status` <> 6");
		if (companyInfoId != null) {
			sql.append(" AND t.company_info_id = ?");
			list.add(companyInfoId);

		} //t.company_info_id = 9 AND it.order_item = 3255
		if (orderItemId != null) {
			sql.append(" AND it.order_item = ?");
			list.add(orderItemId);
		}
		if( returnsItemId!=null){
			sql.append(" AND it.id <> ? ");
			list.add(returnsItemId);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Map<String, Object> map = getNativeDao().findSingleMap(sql.toString(),
				objs);
		BigDecimal returnQuantity = BigDecimal.ZERO;
		if (map != null && map.get("quantity") != null) {
			returnQuantity = new BigDecimal(map.get("quantity").toString());
		}
		return returnQuantity;
	}
	
	public List<Map<String, Object>> findTable(String sn,String erpSn, Long[] storeId,
			Long sbuId, Integer[] status,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long organizationId, Long saleOrgId,String glfirstTime,String gllastTime,
			Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();

		sql.append("select aa.return_amount_item,br.*,so.name sale_org_name,s.name store_name,o.sn order_sn,sb.name sbu_name  from xx_b2b_returns br"
				+ " left join xx_order o on br.orders=o.id left join xx_sale_org so on so.id=br.sale_org "
				+ " left join xx_sbu sb on br.sbu=sb.id "
				+ " left join xx_store s on br.store=s.id "
				+ " left join ("
				+ "      select a.id, sum(round(ifnull(b.price*b.returned_quantity, 0),2)) return_amount_item "
				+ "      from xx_b2b_returns_item b left join xx_b2b_returns a on b.b2b_returns=a.id and a.status in (4,5)  "
				+ "      GROUP BY a.id) aa on aa.id=br.id "
				+ " where 1=1");
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and br.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(erpSn)) {
			sql.append(" and br.erp_sn = ?");
			list.add(erpSn);
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			sql.append("  and s.id in (" + s + ")");
		}
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and br.sale_org = ?");
			list.add(saleOrgId);
		}
		if (organizationId != null) {
			sql.append(" and br.organization= ?");
			list.add(organizationId);
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and br.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and br.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}

		if (!ConvertUtil.isEmpty(glfirstTime)) {
			sql.append(" and br.gl_date >= ?");
			list.add(DateUtil.convert(glfirstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(gllastTime)) {
			sql.append(" and br.gl_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(gllastTime + " 00:00:00"),
					1));
		}
		if (flag != null && flag == 2) {// 退货入库
			sql.append(" and br.status>0");
		}
		
		if (wfState != null && wfState.length > 0) {
			String os = "";
			for (int i = 0; i < wfState.length; i++) {
				if (i == wfState.length - 1)
					os += wfState[i];
				else
					os += wfState[i] + ",";
			}
			sql.append(" and br.wf_state in (" + os + ")");
		}

		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and br.status in (" + os + ")");
		}

		//基于用户sbu过滤
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		List<Filter> filter = new ArrayList<Filter>();
		filter.clear();
		filter.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,
				filter,
				null);
		String[] sbuIds = new String[sbus.size()];
		if (sbus != null && sbus.size() > 0) {
			for (int i = 0; i < sbus.size(); i++) {
				StoreMemberSbu storeMemberSbu = sbus.get(i);
				sbuIds[i] = storeMemberSbu.getSbu().getId().toString();
			}
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		else if (sbuIds.length > 0) {
			String os = "";
			for (int i = 0; i < sbuIds.length; i++) {
				if (i == sbuIds.length - 1)
					os += "'" + sbuIds[i] + "'";
				else
					os += "'" + sbuIds[i] + "'" + ",";
			}
			sql.append(" and sb.id in (" + os + ")");
		}

		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and br.store in (" + storeAuth + ")");
			}
		}
		else {
			sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());

		}
		if (companyInfoId != null) {
			sql.append(" and br.company_info_id = ?");
			list.add(companyInfoId);

		}
		sql.append(" order by br.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> page = getNativeDao().findListMap(sql.toString(), objs, 0);
		return page;
	}
	
	public Integer amShippingItemCount(Long id) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT COUNT(*) FROM xx_am_shipping xas  ");
		sql.append(" LEFT JOIN xx_am_shipping_item xsmi ON xas.id = xsmi.am_shipping ");
		sql.append(" LEFT JOIN xx_b2b_returns_item xbri ON xsmi.b2b_returns_item = xbri.id ");
		sql.append(" LEFT JOIN xx_b2b_returns xbr ON xbri.b2b_returns = xbr.id ");
		sql.append(" WHERE xas.status <> 2  ");
		
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and xas.company_info_id = ? ");
			list.add(companyInfoId);
		}
		if(!ConvertUtil.isEmpty(id)){
			sql.append(" and xbr.id = ? ");
			list.add(id);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}
	
	/**
	 * 根据退货单Id获取明细
	 * @param Ids
	 * @return
	 */
	public List<Map<String, Object>> findB2bReturnsItemList(Long[] Ids) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT bri.id,bri.b2b_returns,br.sn b2b_returns_sn,bri.product,p.vonder_code,p.name,p.detail_description, ");
		sql.append(" po.id product_organization_id,po.name product_organization_name,sdt.id level_Id,sdt.value levelName, ");
		sql.append(" cn.id color_numbers_id,cn.value color_numbers_name,mc.id moisture_content_id,mc.value moisture_content_name,  ");
		sql.append(" bri.batch,bri.batch_encoding,bri.memo,p.model,p.unit,p.volume,p.weight,p.branch_per_box,p.per_box,p.per_branch, ");
		sql.append(" IFNULL(bri.quantity,0) - IFNULL(am.quantity,0) quantity, ");
		sql.append(" FLOOR(CASE WHEN p.per_branch IS NOT NULL THEN (IFNULL(bri.quantity,0) - IFNULL(am.quantity,0))/p.per_branch ELSE 0 END) branch_quantity, ");
		sql.append(" FLOOR(CASE WHEN p.per_box IS NOT NULL THEN (IFNULL(bri.quantity,0) - IFNULL(am.quantity,0))/p.per_box ELSE 0 END) box_quantity ");
		sql.append(" FROM xx_b2b_returns_item bri ");
		sql.append(" LEFT JOIN xx_b2b_returns br ON bri.b2b_returns = br.id AND br.status = 1 ");
		sql.append(" LEFT JOIN xx_product p ON p.id = bri.product ");
		sql.append(" LEFT JOIN xx_system_dict sdt ON sdt.id = bri.product_level ");
		sql.append(" LEFT JOIN xx_organization po ON po.id = bri.product_organization ");
		sql.append(" LEFT JOIN xx_system_dict cn ON cn.id = bri.color_numbers ");
		sql.append(" LEFT JOIN xx_system_dict mc ON mc.id = bri.moisture_contents ");
		sql.append(" LEFT JOIN (SELECT amsi.b2b_returns_item,SUM( amsi.quantity ) quantity " +
									" FROM xx_am_shipping_item amsi "+
									" LEFT JOIN xx_am_shipping ams ON amsi.am_shipping = ams.id "+
									" WHERE ams.STATUS <> 2 GROUP BY amsi.b2b_returns_item ) am ON am.b2b_returns_item = bri.id  ");
		sql.append(" WHERE 1=1 AND IFNULL(bri.quantity,0) - IFNULL(am.quantity,0) > 0 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and br.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(Ids) && Ids.length > 0) {
			String os = "";
			for (int i = 0; i < Ids.length; i++) {
				if (i == Ids.length - 1)
					os += Ids[i];
				else
					os += Ids[i] + ",";
			}
			sql.append(" and bri.id in (" + os + ")");
		}
		sql.append(" GROUP BY bri.id ORDER BY br.modify_date DESC ");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);

		return maps;
	};
	
	
	
	
	
	
	
	
}
