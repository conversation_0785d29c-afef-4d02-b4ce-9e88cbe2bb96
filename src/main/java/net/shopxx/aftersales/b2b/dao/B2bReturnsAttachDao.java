package net.shopxx.aftersales.b2b.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.dao.impl.DaoCenter;

import org.springframework.stereotype.Repository;
@Repository("b2bReturnsAttachDao")
public class B2bReturnsAttachDao extends DaoCenter  {

	/**
	 * 根据充值单id查找对应的附件信息
	 * */
	public List<Map<String, Object>> findListByB2bReturnsId(Long id,Integer type) {
		List<Object> list = new ArrayList<Object>();
		String sql = "select b.*,s.name store_member_name from xx_b2b_returns_attach b,xx_store_member s where b.store_member=s.id and b.b2b_returns=? and b.type = ? ";
		list.add(id);
        list.add(type);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}
	
}
