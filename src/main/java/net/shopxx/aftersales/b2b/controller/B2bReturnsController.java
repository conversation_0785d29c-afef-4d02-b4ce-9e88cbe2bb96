package net.shopxx.aftersales.b2b.controller;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.aftersales.b2b.service.B2bReturnsAttachService;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SbuItems;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuItemsService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.entity.StoreSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.order.entity.CustomerContract;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.service.CustomerContractService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.stock.entity.StockIn;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.service.StockInService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

@Controller("aftersalesB2bReturnsController")
@RequestMapping("/aftersales/b2b_returns")
public class B2bReturnsController extends BaseController {

	@Resource(name = "b2bReturnsServiceImpl")
	private B2bReturnsService b2bReturnsService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "stockInServiceImpl")
	private StockInService stockInService;
	@Resource(name = "b2bReturnsAttachServiceImpl")
	private B2bReturnsAttachService b2bReturnsAttachService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "sbuItemsServiceImpl")
	private SbuItemsService sbuItemsService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "aftersaleServiceImpl")
	private AftersaleService aftersaleService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name="customerContractServiceImpl")
	private CustomerContractService customerContractService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;
	
	/*
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(Integer flag, @PathVariable String code, Long sbuId,Long userId,Long menuId,
			Pageable pageable, ModelMap model) {
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(96L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("code", code);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("flag", flag); // flag=1审核，flag=2入库
        model.addAttribute("menuId", menuId);
        //获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);
		/**
		 * 用户经营组织权限
		 */
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Filter> filters =new ArrayList<Filter>(); 
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);
		
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		// 订单下达是否展示色号、含水率、批次  0 不展示    非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch); 
		}catch (RuntimeException e) {

		}
		
		//订单行是否展示金额  0不展示  非0展示
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); 
		}catch (RuntimeException e) {
			
		}
		
		try {
			String value = SystemConfig.getConfig("b2bReturnslink5Roles",
				WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int b2bReturnslink5Roles = 0;
			for (PcUserRole userRole : userRoles) {
				 if (list.contains(userRole.getPcRole().getName())) {
					 b2bReturnslink5Roles++;
				 break;
			 }
			}
			model.addAttribute("b2bReturnslink5Roles", b2bReturnslink5Roles); 
		}
		catch (RuntimeException e) {

		}
		
		return CommonUtil.getFolderPrefix(code)+ "/aftersales/b2b_returns/list";
	}

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(Integer flag, @PathVariable String code,
			Long objTypeId, Long objid, Long sbuId, Pageable pageable,Long menuId,
			ModelMap model) {
		model.addAttribute("code", code);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("flag", flag); // flag=1审核，flag=2入库
        model.addAttribute("menuId", menuId);
		return CommonUtil.getFolderPrefix(code)
				+ "/aftersales/b2b_returns/list_tb";
	}

	/*
	 * 列表
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn,String erpSn, Long[] storeId, Integer[] status,
			Integer[] wfState,Long sbuId, String firstTime, String lastTime, 
			Integer flag,Long[] saleOrgId, Long organizationId,String glfirstTime,
			String gllastTime,Long[] productId,Pageable pageable,ModelMap model,
			String moistureContent,String colourNumber,String batch,String bCreater,
			Long[] organizationIds, String aftersaleSn, Integer category) {
			

		//2019-05-16 冯旗 增加流程状态筛选条件 erp单号搜索
        // 2021-01-11 蓝天龙 增加筛选条件 售后单号搜索 aftersaleSn
		Page<Map<String, Object>> page = b2bReturnsService.findPage(sn,
				erpSn,
				storeId,
				sbuId,
				status,
				wfState,
				firstTime,
				lastTime,
				flag,
				organizationId,
				saleOrgId,
				glfirstTime,
				gllastTime,
				productId,
				pageable,
				moistureContent,
				colourNumber,
				batch,
				bCreater,
				organizationIds,
                aftersaleSn,
                category);
		//明细
		List<Map<String, Object>> returns = page.getContent();
		if (!returns.isEmpty()) {
			List<Map<String, Object>> returnitems = null;
			Long id = null;
			for (Map<String, Object> map : returns) {
				if (map.get("id") != null) {
					id = Long.valueOf(map.get("id").toString());
					returnitems = b2bReturnsService.findItemListById(id,organizationIds,true);
				}
				if (returnitems != null && returnitems.size() > 0) {
					map.put("returns_items", returnitems);
				}
			}
		}
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/*
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add(@PathVariable String code, Long sbuId, ModelMap model) {
		model.addAttribute("code", code);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("isMember", storeMember.getMemberType());
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",
				storeMemberService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示    非0 展示
			//新增经销商零售
			filters.clear();
			filters.add(Filter.eq("code", "businessType"));
			filters.add(Filter.eq("value", "经销商零售"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> businessTypeList = systemDictService.findList(null,
					filters,
					null);
			SystemDict businessType = null;
			if (businessTypeList != null && businessTypeList.size() > 0) {
				businessType = businessTypeList.get(0);
			}
			filters.clear();
			filters.add(Filter.eq("code", "businessType"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> businessTypes = systemDictService.findList(null,
					filters,
					null);
			Long businessTypeId = businessType.getId();
			model.addAttribute("businessTypes", businessTypes);
			model.addAttribute("businessTypeId", businessTypeId);

			//优惠项目说明
			filters.clear();
			filters.add(Filter.eq("code", "discountProjectDescription"));
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> discountProjectDescriptionList = systemDictService.findList(null,filters,null);
			model.addAttribute("discountProjectDescriptionList", discountProjectDescriptionList);

			Sbu sbu = sbuService.find(sbuId);
			model.addAttribute("sbu", sbu);

			//仓库根据sbu预留字段3带出默认
			if (sbu != null && sbu.getUndefined3() != null) {
				if (sbu.getUndefined3().contains("defaultWarehouse")) {
					String string = sbu.getUndefined3();
					String warehouse = string.substring(string.indexOf("'") + 1,
							string.lastIndexOf("'"));
					Map<String, Object> findWarehouse = orderService.findWarehouse(warehouse);
					if (findWarehouse != null && findWarehouse.get("id") != null) {
						BigInteger findId = (BigInteger) findWarehouse.get("id");
						Long warehouseSbuId = findId.longValue();
						model.addAttribute("warehouseSbuId", warehouseSbuId);
					}
					if (findWarehouse != null && findWarehouse.get("name") != null) {
						String warehouseSbuName = (String) findWarehouse.get("name");
						model.addAttribute("warehouseSbuName", warehouseSbuName);
					}
					if (findWarehouse != null && findWarehouse.get("type_system_dict_id") != null) {
						BigInteger type_system_dict_id = (BigInteger) findWarehouse.get("type_system_dict_id");
						model.addAttribute("type_system_dict_id", type_system_dict_id);
					}
					if (findWarehouse != null && findWarehouse.get("type_system_dict_value") != null) {
						String type_system_dict_value = (String) findWarehouse.get("type_system_dict_value");
						model.addAttribute("type_system_dict_value", type_system_dict_value);
					}
					if (findWarehouse != null && findWarehouse.get("type_system_dict_flag") != null) {
						String type_system_dict_flag = (String) findWarehouse.get("type_system_dict_flag");
						model.addAttribute("type_system_dict_flag", type_system_dict_flag);
					}
					if (findWarehouse != null && findWarehouse.get("management_organization_id") != null) {
						BigInteger management_organization_id = (BigInteger) findWarehouse.get("management_organization_id");
						model.addAttribute("management_organization_id", management_organization_id);
					}
					if (findWarehouse != null && findWarehouse.get("management_organization_name") != null) {
						String management_organization_name = (String) findWarehouse.get("management_organization_name");
						model.addAttribute("management_organization_name", management_organization_name);
					}
					if (!ConvertUtil.isEmpty(findWarehouse) && !ConvertUtil.isEmpty(findWarehouse.get("production_plant"))) {
						String production_plant = (String) findWarehouse.get("production_plant");
						model.addAttribute("production_plant", production_plant);
					}
				}
			}

			Store store = null;
			Member member = storeMemberService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
			if (storeMembers != null) {
				for (StoreMember storeMembera : storeMembers) {
					store = storeMembera.getStore();
					if (store.getType().equals(Store.Type.distributor)) {
						model.addAttribute("store", store);
						break;
					}
					else {
						store = null;
					}
				}
			}
			filters.clear();
			filters.add(Filter.eq("sbu", sbu.getId()));
			filters.add(Filter.eq("isDefault", true));
			List<SbuItems> sbuItem = sbuItemsService.findList(null,
					filters,
					null);
			if (sbuItem.size() > 0) {
				Long shippingMethodId = Long.parseLong(sbuItem.get(0)
						.getShippingMethod()
						.getId()
						.toString());
				model.addAttribute("shippingMethodId", shippingMethodId);

			}

			List<SbuItems> sbuItems = sbu.getShippingMethodSbuList();
			model.addAttribute("sbuItems", sbuItems);
		
			boolean roles = storeService.findByRole(storeMember, "退货价格类型角色");
			if (roles == true) {
				model.addAttribute("roles", roles);
				// 获取当前客户的Sbu价格类型
				filters.clear();
				filters.add(Filter.eq("store", store));
				filters.add(Filter.eq("sbu", sbu));
				List<StoreSbu> storeSbus = storeSbuService.findList(null, filters, null);
				if (storeSbus.size() > 0) {
					for (StoreSbu storeSbu : storeSbus) {
						MemberRank memberRank = storeSbu.getMemberRank();
						model.addAttribute("memberRank", memberRank);
					}
				}
			}
		}catch (RuntimeException e) {

		}
		SimpleDateFormat form = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("GLDate", form.format(new Date()));
		
		// 订单下达是否展示色号、含水率、批次  0 不展示    非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch); 
		}catch (RuntimeException e) {

		}
		
		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles); 
		}catch (RuntimeException e) {

		}
		
		//产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
		model.addAttribute("productLevelList", productLevelList);
		
		//参考订单做多角色参数控制
		try {
			String value = SystemConfig.getConfig("referenceOrderUserRoles",WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int referenceOrder = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					referenceOrder++;
					break;
				}
			}
			model.addAttribute("referenceOrder", referenceOrder);
		}catch (RuntimeException e) {

		}
		
		//参考发货单做多角色参数控制
		try {
			String value = SystemConfig.getConfig("referenceShippingUserRoles",WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int referenceShipping = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					referenceShipping++;
					break;
				}
			}
			model.addAttribute("referenceShipping", referenceShipping);
		}catch (RuntimeException e) {

		}
		try {
			String value = SystemConfig.getConfig("editProductOrgPrices",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editProductOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editProductOrgPrice++;
					break;
				}
			}
			// 角色是否能修改产品部价格  1 可修改     非1  不可修改
			model.addAttribute("editProductOrgPrice", editProductOrgPrice);
		}
		catch (RuntimeException e) {

		}

		// 角色是否能修改平台产品价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listRole = Arrays.asList(role);
			int editSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (listRole.contains(userRole.getPcRole().getName())) {
					editSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("editSaleOrgPrice", editSaleOrgPrice); }
		catch (RuntimeException e) {

		}

		//是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock",WebUtils.getCurrentCompanyInfoId());
		if(!ConvertUtil.isEmpty(linkStockValue)){
			Integer linkStock  = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}
		
		//色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null,filters,null);
		model.addAttribute("colorNumberList", colorNumberList);
		
		//含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null,filters,null);
		model.addAttribute("moistureContentList", moistureContentList);
		
		//订单管理是否启用库存查询 0不展示、1展示
		Integer orderStockQueryRoles = roleJurisdictionUtil.getRoleCount("orderStockQueryRoles");
		model.addAttribute("orderStockQueryRoles", orderStockQueryRoles);

        // 角色是否能查看平台产品价格  1 可修改     非1  不可修改
        try {
            String value = SystemConfig.getConfig("seeSaleOrgPriceRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] roles = value.split(",");
            List<String> list = Arrays.asList(roles);
            int seeSaleOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    seeSaleOrgPrice++;
                    break;
                }
            }
            model.addAttribute("seeSaleOrgPrice", seeSaleOrgPrice);
        }
        catch (RuntimeException e) {
        }
		// 角色是否能修改平台产品价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int editSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("editSaleOrgPrice", editSaleOrgPrice);
		}catch (RuntimeException e) {
		}

		// 角色是否能修改原价
		try {
			String value = SystemConfig.getConfig("editProductPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editProductPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editProductPrice++;
					break;
				}
			}
			model.addAttribute("editProductPrice", editProductPrice); // 角色是否能修改产品价格  1 可修改     非1  不可修改
		}
		catch (RuntimeException e) {
		}
		// 角色是否能修改折扣率  1 可修改     非1  不可修改
		parameterControl("editDiscount",model);
		// 角色是否能查看价差
		parameterControl("seePriceDifference",model);
		// 角色是否能查看其他价差  1 可修改     非1  不可修改
		parameterControl("seeOrderPriceDifference",model);
		// 角色是否能修改结算价价差
		parameterControl("editOrderPriceDifference",model);
		// 角色是否能修改价差
		parameterControl("editPriceDifference",model);
        // 角色是否能查看结算价价差
        parameterControl("seeOrgPriceDifference",model);

		return CommonUtil.getFolderPrefix(code) + "/aftersales/b2b_returns/add";
	}

	@RequestMapping(value = "/add_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg add_data(Long orderId, Pageable pageable, ModelMap model) {

		List<Map<String, Object>> page = b2bReturnsService.findShippingItemListById(orderId,
				null);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/*
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(B2bReturns b2bReturns, Long orderId, Long saleOrgId,
			Long storeId, Long organizationId, Long warehouseId,
			Long salemanId, Long areaId, Long sbuId, Long businessTypeId,
			Long memberRankId,Long aftersaleId,Long contractId,Long regionalManagerId) {

		Store store = storeService.find(storeId);
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		if (store == null) {
			return error("请选择客户");
		}
		b2bReturns.setStore(store);
		if (organizationId == null) {
			return error("请选择组织");
		}
		if(b2bReturns.getGlDate()==null){
			return error("请输入GL日期");
		}
		if(b2bReturns.getCategory()==1 && b2bReturns.getFourAftersaleSn()==null){
			return error("请选择售后单据");
		}
		Aftersale aftersale = null;
		if (aftersaleId != null) {
			aftersale = aftersaleService.find(aftersaleId);
			b2bReturns.setAftersale(aftersale);
		}
		if(b2bReturns.getCategory()==1 && b2bReturns.getFactoryId()==null){
			return error("请选择工厂");
		}
		if(contractId!=null){
			CustomerContract customerContract=customerContractService.find(contractId);
			b2bReturns.setCustomerContract(customerContract);
		}
		Order order = null;
		if (orderId != null) {
			order = orderService.find(orderId);
			b2bReturns.setOrder(order);
		}
		SaleOrg saleOrg = null;
		if (saleOrgId != null) {
			saleOrg = saleOrgService.find(saleOrgId);
			b2bReturns.setSaleOrg(saleOrg);
		}
		if (warehouseId != null) {
			Warehouse warehouse = warehouseService.find(warehouseId);
			b2bReturns.setWarehouse(warehouse);
		}
		if (organizationId != null) {
			Organization organization = organizationService.find(organizationId);
			b2bReturns.setOrganization(organization);
		}
		StoreMember saleman = null;
		if (salemanId != null) {
			saleman = storeMemberService.find(salemanId);
			b2bReturns.setSaleman(saleman);
		}
		Area area = null;
		if (areaId != null) {
			area = areaService.find(areaId);
			b2bReturns.setArea(area);
		}
		//处理业务类型
		if (businessTypeId != null) {
			SystemDict businessType = systemDictService.find(businessTypeId);
			b2bReturns.setBusinessType(businessType);
		}
		if (regionalManagerId != null) {
			StoreMember regionalManager = storeMemberService.find(regionalManagerId);
			b2bReturns.setRegionalManager(regionalManager);
		}
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		filters.add(Filter.eq("sbu", sbuId));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,filters,null);
		if (sbus.size() == 0 || sbus == null) {
			return error("该客户没有维护此类型SBU");
		}
		//处理sbu
		if (sbuId == null) {
			return error("请选择sbu！");
		}else {
			Sbu sbu = sbuService.find(sbuId);
			b2bReturns.setSbu(sbu);
		}
		// 价格类型
		if (memberRankId != null) {
			MemberRank memberRank = memberRankBaseService.find(memberRankId);
			b2bReturns.setMemberRank(memberRank);
		}
		//平方数不能大于可退货数量
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
		if (b2bReturnsItems != null) {
			for (Iterator<B2bReturnsItem> iterator = b2bReturnsItems.iterator(); iterator.hasNext();) {
				B2bReturnsItem returnsItem = iterator.next();
				if (returnsItem != null && returnsItem.getOrderItem() != null && returnsItem.getOrderItem().getId() != null) {
					BigDecimal returnQuantity = b2bReturnsService.findReturnQuantitys(returnsItem.getOrderItem().getId().toString(),null);
					OrderItem orderItems = orderItemService.find(returnsItem.getOrderItem().getId());
					BigDecimal kfhsl = BigDecimal.ZERO;
					if (orderItems != null && returnQuantity != null) {
						kfhsl = orderItems.getShippedQuantity().subtract(returnQuantity);
					}
					if (returnsItem != null && returnsItem.getQuantity() != null) {
						if (returnsItem.getQuantity().compareTo(kfhsl) == 1) {
							return error("平方数不能大于可退货数量");
						}
					}
				}
			}
		}
		for(B2bReturnsItem item : b2bReturns.getB2bReturnsItems()){
			if(item.getPriceDifference() != null && !"0".equals(item.getPriceDifference().toString()) && item.getDiscountProjectDescription().getId() == null){
				// 请选择优惠项目说明
				return error("请选择优惠项目说明");
			}
			if(item.getDiscountProjectDescription() != null && item.getDiscountProjectDescription().getId() == null){
				item.setDiscountProjectDescription(null);
			}
		}
		b2bReturnsService.save(b2bReturns, order);

		return success().addObjX(b2bReturns.getId());
	}

	/*
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(B2bReturns b2bReturns, Long organizationId,
			Long warehouseId, Long orderId, Long saleOrgId, Long areaId,
			Long storeId, Long sbuId, Long businessTypeId,Long aftersaleId,
			Long contractId,Long regionalManagerId) {

		Store store = storeService.find(storeId);
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		if (store == null) {
			return error("请选择客户");
		}
		if(b2bReturns.getGlDate()==null){
			return error("请输入GL日期");
		}
		if(b2bReturns.getCategory()==1 && b2bReturns.getFourAftersaleSn()==null){
			return error("请选择售后单据");
		}
		Aftersale aftersale = null;
		if (aftersaleId != null) {
			aftersale = aftersaleService.find(aftersaleId);
			b2bReturns.setAftersale(aftersale);
		}
		if(b2bReturns.getCategory()==1 && b2bReturns.getFactoryId()==null){
			return error("请选择工厂");
		}
		b2bReturns.setStore(store);

		if (warehouseId != null) {
			Warehouse warehouse = warehouseService.find(warehouseId);
			b2bReturns.setWarehouse(warehouse);
		}

		if (organizationId != null) {
			Organization organization = organizationService.find(organizationId);
			b2bReturns.setOrganization(organization);
		}

		if(contractId!=null){
			CustomerContract customerContract=customerContractService.find(contractId);
			b2bReturns.setCustomerContract(customerContract);
		}
		SaleOrg saleOrg = null;
		if (saleOrgId != null) {
			saleOrg = saleOrgService.find(saleOrgId);
			b2bReturns.setSaleOrg(saleOrg);
		}

		Area area = null;
		if (areaId != null) {
			area = areaService.find(areaId);
			b2bReturns.setArea(area);
		}
		//处理业务类型
		if (businessTypeId != null) {
			SystemDict businessType = systemDictService.find(businessTypeId);
			b2bReturns.setBusinessType(businessType);
		}

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		filters.add(Filter.eq("sbu", sbuId));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,
				filters,
				null);
		if (sbus.size() == 0 || sbus == null) {
			return error("该客户没有维护此类型SBU");
		}
		//处理sbu
		if (sbuId == null) {
			return error("请选择sbu！");
		}else {
			Sbu sbu = sbuService.find(sbuId);
			b2bReturns.setSbu(sbu);
		}
		if (regionalManagerId != null) {
			StoreMember regionalManager = storeMemberService.find(regionalManagerId);
			b2bReturns.setRegionalManager(regionalManager);
		}
		//平方数不能大于可退货数量
		List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
		if (b2bReturnsItems != null) {
			for (Iterator<B2bReturnsItem> iterator = b2bReturnsItems.iterator(); iterator.hasNext();) {
				B2bReturnsItem returnsItem = iterator.next();
				if (!ConvertUtil.isEmpty(returnsItem) && 
						!ConvertUtil.isEmpty(returnsItem.getId()) &&
						!ConvertUtil.isEmpty(returnsItem.getOrderItem()) && 
						!ConvertUtil.isEmpty(returnsItem.getOrderItem().getId())) {
					BigDecimal returnQuantity = b2bReturnsService.findReturnQuantitys(returnsItem.getOrderItem().getId().toString(),
							returnsItem.getId().toString());
					OrderItem orderItems = orderItemService.find(returnsItem.getOrderItem().getId());
					BigDecimal kfhsl = BigDecimal.ZERO;
					if (!ConvertUtil.isEmpty(orderItems) && !ConvertUtil.isEmpty(returnQuantity)) {
						kfhsl = orderItems.getShippedQuantity().subtract(returnQuantity);
					}
					if (!ConvertUtil.isEmpty(returnsItem) && !ConvertUtil.isEmpty(returnsItem.getQuantity())) {
						if (returnsItem.getQuantity().compareTo(kfhsl) == 1) {
							return error("平方数不能大于可退货数量");
						}
					}
				}
			}
		}
		for(B2bReturnsItem item : b2bReturns.getB2bReturnsItems()){
			if(item.getPriceDifference() != null && !"0".equals(item.getPriceDifference().toString()) && item.getDiscountProjectDescription().getId() == null){
				// 请选择优惠项目说明
				return error("请选择优惠项目说明");
			}
			if(item.getDiscountProjectDescription() != null && item.getDiscountProjectDescription().getId() == null){
				item.setDiscountProjectDescription(null);
			}
		}
		b2bReturnsService.updateB2bReturns(b2bReturns);
		
		return success().addObjX(b2bReturns.getId());
	}

	@RequestMapping(value = "/view/{code}", method = RequestMethod.GET)
	public String view(@PathVariable String code, Long id, Long saleOrgId,Long isEdit,
			Integer flag, ModelMap model) {
		// flag空是可编辑，0只查看，1审核，2入库
		B2bReturns b2bReturns = b2bReturnsService.find(id);
		model.addAttribute("b2bReturns", b2bReturns);
		model.addAttribute("flag",flag);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("isMember", storeMember.getMemberType());
        model.addAttribute("isEdit", isEdit);

		Long storeId = null;
		Long sbuId = null;
		Long organizationId = null;
		if (b2bReturns.getStore() != null) {
			storeId = b2bReturns.getStore().getId();
		}
		if (b2bReturns.getSbu() != null) {
			sbuId = b2bReturns.getSbu().getId();
		}
		if (b2bReturns.getOrganization() != null) {
			organizationId = b2bReturns.getOrganization().getId();
		}
		Order order = b2bReturns.getOrder();
		Map<String, Object> map = storeBalanceService.findBalanceSbu(storeId,
				sbuId,
				organizationId,
				saleOrgId);

		if (map != null) {
			//可用余额
			if (map.get("balance") != null) {
				model.addAttribute("balance", map.get("balance"));
			}
			//余额
			if (map.get("yue") != null) {
				BigDecimal balance = new BigDecimal(map.get("balance")
						.toString());
				BigDecimal credit_amount = new BigDecimal(
						map.get("credit_amount").toString());
				BigDecimal yue = balance.subtract(credit_amount);
				model.addAttribute("yue", yue);
			}
			//授信
			if (map.get("credit_amount") != null) {
				model.addAttribute("credit", map.get("credit_amount"));
			}
			//差额
			if (map.get("balance") != null && map.get("amount_paid") != null) {
				BigDecimal balance = new BigDecimal(map.get("balance")
						.toString());
				if (order != null) {
					BigDecimal chae = balance.subtract(order.getAmount());
					model.addAttribute("chae", chae);
				}
			}
		}

		List<Map<String, Object>> list = b2bReturnsService.findItemListById(id,null,null);
		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		/* 附件 */
		String b2bReturnsAttach_json = JsonUtils.toJson(b2bReturnsAttachService.findListByB2bReturnsId(id,0));
		model.addAttribute("b2bReturnsAttach_json", b2bReturnsAttach_json);
		/* 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(b2bReturns.getSn(),
				15));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("code", code);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(96L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember",
				storeMemberService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);

        // 角色是否能修改平台产品价格  1 可修改     非1  不可修改
        try {
			String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listRole = Arrays.asList(role);
			int editSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (listRole.contains(userRole.getPcRole().getName())) {
					editSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("editSaleOrgPrice", editSaleOrgPrice);
		}
		catch (RuntimeException e) {
		}

        // 角色是否能查看平台产品价格  1 可修改     非1  不可修改
        try {
            String value = SystemConfig.getConfig("seeSaleOrgPriceRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> lists = Arrays.asList(role);
            int seeSaleOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (lists.contains(userRole.getPcRole().getName())) {
                    seeSaleOrgPrice++;
                    break;
                }
            }
            model.addAttribute("seeSaleOrgPrice", seeSaleOrgPrice);
        }
        catch (RuntimeException e) {
        }


		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listamountRole = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (listamountRole.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示  非0 展示

			//优惠项目说明
			filters.clear();
			filters.add(Filter.eq("code", "discountProjectDescription"));
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> discountProjectDescriptionList = systemDictService.findList(null,filters,null);
			model.addAttribute("discountProjectDescriptionList", discountProjectDescriptionList);

			Sbu sbu = b2bReturns.getSbu();
			List<SbuItems> sbuItems = sbu.getShippingMethodSbuList();
			model.addAttribute("sbuItems", sbuItems);

			/*
			 * 经销商零售下拉框展示
			 */
			filters.clear();
			filters.add(Filter.eq("code", "businessType"));
			filters.add(Filter.eq("value", "经销商零售"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> businessTypeList = systemDictService.findList(null,
					filters,
					null);
			SystemDict businessType = null;
			if (businessTypeList != null && businessTypeList.size() > 0) {
				businessType = businessTypeList.get(0);
			}
			Long businessTypeIdj = businessType.getId();
			model.addAttribute("businessTypeIdj", businessTypeIdj);
			filters.clear();
			filters.add(Filter.eq("code", "businessType"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> businessTypes = systemDictService.findList(null,
					filters,
					null);
			model.addAttribute("businessTypes", businessTypes);
		}
		catch (RuntimeException e) {

		}
		
		// 判断是否拥有角色
		boolean role = storeService.findByRole(storeMember, "退货价格类型角色");
		if (role == true) {
			if (b2bReturns.getMemberRank() != null) {
				MemberRank memberRank = memberRankBaseService.find(b2bReturns.getMemberRank()
						.getId());
				model.addAttribute("flag", true);
				model.addAttribute("memberRankId", memberRank.getId());
				model.addAttribute("memberRankName", memberRank.getName());
			}
		}
		else {
			model.addAttribute("memberRankId", "");
			model.addAttribute("memberRankName", "");
		}
		
		// 订单下达是否展示色号、含水率、批次  0 不展示    非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> listString = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch); 
		}
		catch (RuntimeException e) {

		}
		
		//产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
		model.addAttribute("productLevelList", productLevelList);
		
		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);		
		
		if(b2bReturns.getWfId() != null){
            /* 工厂品管附件 */
            String b2bReturnsGcpgAttach_json = JsonUtils.toJson(b2bReturnsAttachService.findListByB2bReturnsId(id,1));
            model.addAttribute("b2bReturnsGcpgAttach_json", b2bReturnsGcpgAttach_json);			ActWf wf = b2bReturnsService.getWfByWfId(b2bReturns.getWfId());
			if(wf != null){
			//工厂品管
			Boolean gcpg = false;
			Boolean gcpgs = false;
			//质量中心处理意见
			Boolean zlzx = false;
			Boolean zlzxs = false;
			//工厂仓管
			Boolean gccg = false;
			Boolean gccgs = false;
			
			List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
			for(Map<String, Object> c : item){
				if(c.get("suggestion")!=null){
					String approved = c.get("approved")!=null?c.get("approved").toString():"false";
					String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
					if(rwm.contains("品管")){
						gcpg = Boolean.valueOf(approved);
					}
					if(rwm.contains("质量中心")){
						zlzx = Boolean.valueOf(approved);
						if(!zlzx){
							gcpg = false;
						}
					}
					if(rwm.contains("仓管")){
						gccg = Boolean.valueOf(approved);
						if(!gccg){
							zlzx = false;
						}
					}
                    if(rwm.contains("跟单")){
                        Boolean gd = Boolean.valueOf(approved);
                        if(!gd){
                            gccg = false;
                        }
                    }
				}
			}
			Task t = aftersaleService.getCurrTaskByWf(wf);
			if(t!=null){
				//获取当前节点所有用户id
				List<String> userId = actWfService.getTaskUsers(t.getId());
				if(userId.contains(storeMember.getId().toString())&&t.getName().contains("品管")){
					gcpgs = true;
				}
				if(userId.contains(storeMember.getId().toString())&&t.getName().contains("质量中心")){
					gcpgs = true;zlzxs = true;
				}
				if(userId.contains(storeMember.getId().toString())&&t.getName().contains("仓管")){
					gcpgs = true;gccgs = true;//zlzxs = true;
				}
			}
			model.addAttribute("wf",wf);		
			model.addAttribute("node",t);
			model.addAttribute("gcpg",gcpg);
			model.addAttribute("gcpgs",gcpgs);
			model.addAttribute("zlzx",zlzx);
			model.addAttribute("zlzxs",zlzxs);
			model.addAttribute("gccg",gccg);
			model.addAttribute("gccgs",gccgs);
			}
		}
		
		//参考订单做多角色参数控制
		try {
			String value = SystemConfig.getConfig("referenceOrderUserRoles",WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> listString = Arrays.asList(perRole);
			int referenceOrder = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					referenceOrder++;
					break;
				}
			}
			model.addAttribute("referenceOrder", referenceOrder);
		}
		catch (RuntimeException e) {

		}
		
		//参考发货单做多角色参数控制
		try {
			String value = SystemConfig.getConfig("referenceShippingUserRoles",WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> listString = Arrays.asList(perRole);
			int referenceShipping = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					referenceShipping++;
					break;
				}
			}
			model.addAttribute("referenceShipping", referenceShipping);
		}
		catch (RuntimeException e) {

		}
		
		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> listString = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles); 
		}
		catch (RuntimeException e) {

		}
		
		// 已审核完修改平台结算价做多角色参数控制 
		try {
			String value = SystemConfig.getConfig("auditedUpdateSaleOrgPrice",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> listString = Arrays.asList(perRole);
			int apdateSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					apdateSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("apdateSaleOrgPrice", apdateSaleOrgPrice); 
		}catch (RuntimeException e) {

		}

		try {
			String value = SystemConfig.getConfig("editProductOrgPrices",
					WebUtils.getCurrentCompanyInfoId());
			String[] roles = value.split(",");
			List<String> lists = Arrays.asList(roles);
			int editProductOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (lists.contains(userRole.getPcRole().getName())) {
					editProductOrgPrice++;
					break;
				}
			}
			// 角色是否能修改产品部价格  1 可修改     非1  不可修改
			model.addAttribute("editProductOrgPrice", editProductOrgPrice);
		}
		catch (RuntimeException e) {

		}
		
		//是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock",WebUtils.getCurrentCompanyInfoId());
		if(!ConvertUtil.isEmpty(linkStockValue)){
			Integer linkStock  = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}
		
		//退货单审核完是否展示关闭按钮 0不展示、1展示
		Integer b2bReturnsCloseRoles = roleJurisdictionUtil.getRoleCount("b2bReturnsCloseRoles");
		model.addAttribute("b2bReturnsCloseRoles", b2bReturnsCloseRoles);
		
		//订单管理是否启用库存查询 0不展示、1展示
		Integer orderStockQueryRoles = roleJurisdictionUtil.getRoleCount("orderStockQueryRoles");
		model.addAttribute("orderStockQueryRoles", orderStockQueryRoles);


		// 角色是否能修改原价
		try {
			String value = SystemConfig.getConfig("editProductPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] roles = value.split(",");
			List<String> lists = Arrays.asList(roles);
			int editProductPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (lists.contains(userRole.getPcRole().getName())) {
					editProductPrice++;
					break;
				}
			}
			model.addAttribute("editProductPrice", editProductPrice); // 角色是否能修改产品价格  1 可修改     非1  不可修改
		}
		catch (RuntimeException e) {
		}
		// 角色是否能修改折扣率  1 可修改     非1  不可修改
		parameterControl("editDiscount",model);
		// 角色是否能查看价差
		parameterControl("seePriceDifference",model);
		// 角色是否能查看其他价差  1 可修改     非1  不可修改
		parameterControl("seeOrderPriceDifference",model);
		// 角色是否能修改结算价价差
		parameterControl("editOrderPriceDifference",model);
		// 角色是否能修改价差
		parameterControl("editPriceDifference",model);
        // 角色是否能查看结算价价差
        parameterControl("seeOrgPriceDifference",model);



		return CommonUtil.getFolderPrefix(code) + "/aftersales/b2b_returns/view";
	}

	/*
	 * 审核
	 * @param id
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check(Long id, ModelMap model) {

		B2bReturns b2bReturns = b2bReturnsService.find(id);
		if (b2bReturns.getStatus().intValue() != 0) {
			// 退货单已审核，请勿重复审核
			return error("17201");
		}
		b2bReturnsService.check(b2bReturns);

		return success();
	}

	/*
	 * 退货入库
	 * @param id
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/stockIn", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg stockIn(B2bReturns b2bReturns, Long warehouseId, Long saleOrgId,
			ModelMap model) {

		// B2bReturns b2bReturns = b2bReturnsService.find(id);
		// if (b2bReturns.getStatus().intValue() != 1) {
		// //只允许已审核状态的退货单进行入库
		// return error("17204");
		// }
		Warehouse warehouse = warehouseService.find(warehouseId);
		if (warehouse == null) {
			return error("请选择仓库");
		}
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		Long stockInId = b2bReturnsService.stockIn(b2bReturns,
				warehouse,
				saleOrg);

		return success().addObjX(stockInId);
	}

	@RequestMapping(value = "/returns_info", method = RequestMethod.GET)
	public String return_info(Long id, ModelMap model) {

		// flag空是可编辑，0只查看，1审核，2入库
		B2bReturns b2bReturns = b2bReturnsService.find(id);
		model.addAttribute("b2bReturns", b2bReturns);

		List<Map<String, Object>> list = b2bReturnsService.findItemListById(id,null,null);
		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		return "/aftersales/b2b_returns/returns_info";
	}

	@RequestMapping(value = "/select_shipping_item", method = RequestMethod.GET)
	public String select_shipping_item(Long orderId, Long[] returnId,
			Pageable pageable, ModelMap model) {

		model.addAttribute("returnId", returnId);
		model.addAttribute("orderId", orderId);
		return "/aftersales/b2b_returns/select_shipping_item";
	}

	@RequestMapping(value = "/select_shipping_item_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_shipping_item_data(Long orderId, Long[] returnId,
			Pageable pageable, ModelMap model) {
		List<Map<String, Object>> page = b2bReturnsService.findShippingItemListById(orderId,
				returnId);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	@RequestMapping(value = "/select_b2b_returns_item", method = RequestMethod.GET)
	public String select_b2b_return_item(Long orderId, Long[] returnId,
			Pageable pageable, ModelMap model) {

		return "/aftersales/b2b_returns/select_b2b_returns_item";
	}

	@RequestMapping(value = "/select_b2b_returns_item_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_b2b_return_item_data(String returnSn, Long storeId,
			Pageable pageable, ModelMap model) {
		List<Map<String, Object>> page = b2bReturnsService.findShippingItem(storeId,
				returnSn);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	@RequestMapping(value = "/in_list", method = RequestMethod.GET)
	public String in_list(Integer flag, Pageable pageable, ModelMap model) {

		model.addAttribute("flag", flag); // flag=1审核，flag=2入库

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("code", "StockInType"));
		List<SystemDict> systemDicts = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("systemDicts", systemDicts);
		return "/aftersales/b2b_returns/in_list";
	}

	/*
	 * 列表数据
	 */
	@RequestMapping(value = "in_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg in_list_data(String sn, Long warehouseId, String orderSn,
			Integer[] docStatus, Integer[] type, Integer[] sourceType,
			String sourceSn, Pageable pageable, ModelMap model) {

		String jsonPage = JsonUtils.toJson(stockInService.findPage(sn,
				warehouseId,
				orderSn,
				docStatus,
				type,
				new Integer[] { 2 },
				sourceSn,
				pageable));

		return success(jsonPage);
	}

	/*
	 * 添加
	 */
	@RequestMapping(value = "/in_add", method = RequestMethod.GET)
	public String in_add(ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("code", "StockInType"));
		List<SystemDict> systemDicts = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("systemDicts", systemDicts);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		return "/aftersales/b2b_returns/in_add";
	}

	@RequestMapping(value = "/in_edit", method = RequestMethod.GET)
	public String in_edit(Long id, Integer isCheck, Integer isEdit,
			ModelMap model) {

		StockIn stockIn = stockInService.find(id);
		SystemDict systemDict = systemDictService.find(stockIn.getType());
		model.addAttribute("stockIn", stockIn);
		model.addAttribute("isCheck", isCheck);
		model.addAttribute("systemDict", systemDict);
		model.addAttribute("isEdit", isEdit);

		List<Map<String, Object>> list = stockInService.findItemListById(id);
		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		/* 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(stockIn.getSn(),
				15));
		model.addAttribute("fullLink_json", fullLink_json);

		// boolean isCheckWf = wfObjConfigBaseService.isCheckWf(48L);
		// model.addAttribute("isCheckWf", isCheckWf);

		return "/aftersales/b2b_returns/in_edit";
	}

	/*
	 * 作废
	 * @param id
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg cancel(Long id, ModelMap model) throws Exception {

		B2bReturns b2bReturns = b2bReturnsService.find(id);
		//校验退货单状态
		b2bReturnsService.checkB2bReturnsState(b2bReturns,0,"已保存","作废");	
		b2bReturnsService.cancel(b2bReturns);
		return success();
	}

	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
		
		B2bReturns b2bReturns = b2bReturnsService.find(id);
		//校验退货单状态
		b2bReturnsService.checkB2bReturnsState(b2bReturns,0,"已保存","流程审批");
		b2bReturnsService.createWf(id, modelId, objTypeId);
		return success();
	}
	
	@RequestMapping(value = "/saveform", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveform(B2bReturns b2bReturns,Integer Type) {
		// type 用来定义节点
		b2bReturnsService.saveform(b2bReturns,Type);
		return success().addObjX(b2bReturns.getId());
	}
	/*
	 * 选择导出
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		//订单行是否展示金额  0不展示  非0展示
		int hiddenAmount = 0;
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
		}catch (RuntimeException e) {
			
		}
		

		List<Map<String, Object>> data = b2bReturnsService.findList(null,null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null);

		for (Map<String, Object> str : data) {
			if (str.get("status") != null) {
				Integer status = (Integer) str.get("status");
				if (status != null) {
					if (status == 2) {
						str.put("status", "入库中");
					}else if (status == 0) {
						str.put("status", "未审核");
					}else if (status == 1) {
						str.put("status", "已审核");
					}else if (status == 3) {
						str.put("status", "已入库");
					}else if (status == 4) {
						str.put("status", "部分退货");
					}else if (status == 5) {
						str.put("status", "完全退货");
					}else if (status == 6) {
						str.put("status", "已作废");
					}else if (status == 8) {
						str.put("status", "已关闭");
					}
				}
			}
			if (str.get("lineAmount") != null) {
				BigDecimal lineAmount = new BigDecimal(str.get("lineAmount").toString());
				str.put("lineAmount",lineAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("return_amount") != null) {
				BigDecimal return_amount = new BigDecimal(str.get("return_amount").toString());
				str.put("return_amount",return_amount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("amount") != null) {
				BigDecimal amount = new BigDecimal(str.get("amount").toString());
				str.put("amount", amount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("Pprice") != null && str.get("Pquantity") != null) {
				BigDecimal price = new BigDecimal(str.get("Pprice").toString());
				BigDecimal quantity = new BigDecimal(str.get("Pquantity").toString());
				str.put("Pcount",price.multiply(quantity).setScale(2,BigDecimal.ROUND_HALF_UP));
			}else {
				str.put("Pcount",BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if(!ConvertUtil.isEmpty(hiddenAmount) && hiddenAmount==0){
				//退款金额
				str.put("amount","***");
				//产品销售单价
				str.put("Pprice","***");
				//产品金额
				str.put("Pcount","***");
				//产品平台结算单价
				str.put("sale_org_price","***");
				//实退款金额
				str.put("lineAmount","***");
				//实退货总金额
				str.put("return_amount","***");
			}
		}

		// 设置导出表格名
		String filename = "退货单"+new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "退货单号",
				"ERP单号",
				"创建人",
				"sbu",
				"仓库",
				"仓库类型",
				"退货原因",
				"退货状态",
				"退款金额",
				"机构",
				"客户名称",
				"客户简称",
				"客户编码",
				"业务类型",
				"产品名称",
				"产品编码",
				"产品描述",
				"木种花色",
				"规格",
				"经营组织",
				"产品级别",
				"产品分类",
				"产品销售单价",
				"产品金额",
				"产品平台结算单价",
				"产品退货箱数",
				"产品退货支数",
				"平方数",
				"实退箱数",
				"实退支数",
				"实退平方数",
				"实退款金额",
				"抬头",
				"税号",
				"退货人姓名",
				"退货人电话",
				"收货人",
				"收货人电话",
				"收货地址",
				"开单人",
				"创建日期",
				"实退货总金额",
				"单据日期",
				"色号",
				"含水率",
				"批次"};
		
		// 设置单元格取值
		String[] properties = { "sn",
				"erp_sn",
				"store_member_name",
				"sbu_name",//sbu
				"warehouse_name",
				"warehouseType",
				"reason",
				"status",
				"amount",
				"sale_org_name",
				"store_name",
				"store_alias",
				"store_out_trade_no",
				"business_name",
				"Pname",
				"PvonderCode",
				"Pdescription",
				"pwtoc",
				"Pspec",
				"product_organization_name",
				"levelName",
				"product_category_name",
				"Pprice",
				"Pcount",//产品行金额
				"sale_org_price",
				"box_quantity",
				"branch_quantity",
				"Pquantity",
				"returned_box_quantity",
				"returned_branch_quantity",
				"returned_quantity",
				"lineAmount",
				"invoice_title",
				"nashuishibiehao",
				"name",
				"mobile",
				"consignee",
				"consignee_mobile",
				"address",
				"storeMemberName",
				"create_date",
				"return_amount",
				"gl_date",
				"color_numbers_name",//色号
				"moisture_content_name",//含水率
				"batch_encoding" };
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256};
		/*
		 * // 附加内容 String[] contents = new String[4]; contents[0] = "订单数量汇总：" +
		 * snSet.size() + "单"; contents[1] = "销售订单总金额汇总：" + totalCount + "元";
		 * contents[2] = "实际发货对应订单金额汇总：" + shippedTotalCount + "元"; contents[3]
		 * = "未发货订单金额汇总：" + totalCount.subtract(shippedTotalCount) + "元";
		 */

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String sn, Long[] storeId,
			Integer[] status, Long sbuId,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long saleOrgId, Long organizationId,String glfirstTime,
			String gllastTime,Pageable pageable, ModelMap model,String moistureContent,
			String colourNumber,String batch,String bCreater,Long[] organizationIds) {

		Integer size = b2bReturnsService.count(sn,storeId,status,sbuId,wfState,firstTime,lastTime,
				flag,saleOrgId,organizationId,null,null,glfirstTime,gllastTime,moistureContent,
				colourNumber,batch,bCreater,organizationIds);
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/*
	 * 条件导出
	 * @param sn
	 * @param storeId
	 * @param status
	 * @param firstTime
	 * @param lastTime
	 * @param flag
	 * @param organizationId
	 * @param ids
	 * @param page
	 * @param size
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, Long[] storeId,
			Integer[] status, Long sbuId,Integer[] wfState, String firstTime, String lastTime,
			Integer flag, Long saleOrgId, Long organizationId, Integer page,
			Pageable pageable, ModelMap model,String glfirstTime,String gllastTime,
			String moistureContent,String colourNumber,String batch,String bCreater,
			Long[] organizationIds) {
		
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		//订单行是否展示金额  0不展示  非0展示
		int hiddenAmount = 0;
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
		}catch (RuntimeException e) {
			
		}
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = b2bReturnsService.findList(sn,storeId,status,
				sbuId,wfState,firstTime,lastTime,flag,saleOrgId,organizationId,null,page,size,
				glfirstTime,gllastTime,moistureContent,colourNumber,batch,bCreater,
				organizationIds);
		
		for (Map<String, Object> str : data) {
			if (str.get("status") != null) {
				Integer statu = (Integer) str.get("status");
				if (statu != null) {
					if (statu == 2) {
						str.put("status", "入库中");
					}else if (statu == 0) {
						str.put("status", "未审核");
					}else if (statu == 1) {
						str.put("status", "已审核");
					}else if (statu == 3) {
						str.put("status", "已入库");
					}else if (statu == 4) {
						str.put("status", "部分退货");
					}else if (statu == 5) {
						str.put("status", "完全退货");
					}else if (statu == 6) {
						str.put("status", "已作废");
					}else if (statu == 8) {
						str.put("status", "已关闭");
					}

				}
			}
			if (str.get("Pprice") != null && str.get("Pquantity") != null) {
				BigDecimal price = new BigDecimal(str.get("Pprice").toString());
				BigDecimal quantity = new BigDecimal(str.get("Pquantity").toString());
				str.put("Pcount",price.multiply(quantity).setScale(2,BigDecimal.ROUND_HALF_UP));
			}else {
				str.put("Pcount",BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("lineAmount") != null) {
				BigDecimal lineAmount = new BigDecimal(str.get("lineAmount").toString());
				str.put("lineAmount",lineAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("return_amount") != null) {
				BigDecimal return_amount = new BigDecimal(str.get("return_amount").toString());
				str.put("return_amount",return_amount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("amount") != null) {
				BigDecimal amount = new BigDecimal(str.get("amount").toString());
				str.put("amount", amount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if(!ConvertUtil.isEmpty(hiddenAmount) && hiddenAmount==0){
				//退款金额
				str.put("amount","***");
				//产品销售单价
				str.put("Pprice","***");
				//产品金额
				str.put("Pcount","***");
				//产品平台结算单价
				str.put("sale_org_price","***");
				//实退款金额
				str.put("lineAmount","***");
				//实退货总金额
				str.put("return_amount","***");
			}
		}

		// 设置导出表格名
		String filename = "退货单"+new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "退货单号",
				"ERP单号",
				"创建人",
				"sbu",
				"仓库",
				"仓库类型",
				"退货原因",
				"退货状态",
				"退款金额",
				"机构",
				"客户名称",
				"客户简称",
				"客户编码",
				"业务类型",
				"产品名称",
				"产品编码",
				"产品描述",
				"木种花色",
				"规格",
				"经营组织",
				"产品级别",
				"产品分类",
				"产品销售单价",
				"产品金额",
				"产品平台结算单价",
				"产品退货箱数",
				"产品退货支数",
				"平方数",
				"实退箱数",
				"实退支数",
				"实退平方数",
				"实退款金额",
				"抬头",
				"税号",
				"退货人姓名",
				"退货人电话",
				"收货人",
				"收货人电话",
				"收货地址",
				"开单人",
				"创建日期",
				"实退货总金额",
				"单据日期",
				"色号",
				"含水率",
				"批次" };
		// 设置单元格取值
		String[] properties = { "sn",
				"erp_sn",
				"store_member_name",
				"sbu_name",
				"warehouse_name",
				"warehouseType",
				"reason",
				"status",
				"amount",
				"sale_org_name",
				"store_name",
				"store_alias",
				"store_out_trade_no",
				"business_name",
				"Pname",
				"PvonderCode",
				"Pdescription",
				"pwtoc",
				"Pspec",
				"product_organization_name",
				"levelName",
				"product_category_name",
				"Pprice",
				"Pcount",//产品行金额
				"sale_org_price",
				"box_quantity",
				"branch_quantity",
				"Pquantity",
				"returned_box_quantity",
				"returned_branch_quantity",
				"returned_quantity",
				"lineAmount",
				"invoice_title",
				"nashuishibiehao",
				"name",
				"mobile",
				"consignee",
				"consignee_mobile",
				"address",
				"storeMemberName",
				"create_date",
				"return_amount",
				"gl_date",
				"color_numbers_name",//色号
				"moisture_content_name",//含水率
				"batch_encoding"};
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256};

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}

	@RequestMapping(value = "/checkPdf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checkPdf(Long ids) throws Exception {
		B2bReturns b2bReturns = b2bReturnsService.find(ids);
		TriplicateForm triplicateForm = null;
		if (b2bReturns == null) {
			return error("退货单不存在！");
		}else {
			Setting setting = SettingUtils.get();
			triplicateForm = b2bReturnsService.createTriplicateForm(setting,b2bReturns);	
		}
		return success(triplicateForm.getUrl());
	}
	
	/**
	 * 查看其它Pdf
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkPdfB2bReturns", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkPdfB2bReturns(Long ids) throws Exception {
		B2bReturns b2bReturns = b2bReturnsService.find(ids);
		TriplicateForm triplicateForm = null;
		if (b2bReturns == null) {
			return error("退货单不存在！");
		}else {
			Setting setting = SettingUtils.get();
			triplicateForm = b2bReturnsService.createB2bReturnsTriplicateForm(setting,b2bReturns);	
		}
		return success(triplicateForm.getUrl());
	}

	//订单明细页面查询
	@RequestMapping(value = "/selectOrder", method = RequestMethod.GET)
	public String selectOrderItem(Integer multi, Long sbuId, Integer storeId,
			Long productOrganizationId, ModelMap model) {
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		//订单行是否展示金额  0不展示  非0展示	
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); 
		}catch (RuntimeException e) {
			
		}
		model.addAttribute("multi", multi);
		model.addAttribute("storeId", storeId);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("productOrganizationId", productOrganizationId);
		return "/aftersales/b2b_returns/select_order";
	}

	//订单明细页面数据查询
	@RequestMapping(value = "/selectOrderData", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectOrderItemData(ModelMap model, String orderSn,
			String vonderCode, String name, Long[] storeId, String mod,
			Long sbuId,Long productOrganizationId, Pageable pageable) {
		Page<Map<String, Object>> page = b2bReturnsService.findOrderItem(orderSn,
				vonderCode,
				name,
				storeId,
				mod,
				sbuId,
				productOrganizationId,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	//发货明细页面查询
	@RequestMapping(value = "/selectShipping", method = RequestMethod.GET)
	public String selectShippingItem(Integer multi, Long sbuId, 
			Integer storeId,Long productOrganizationId,ModelMap model) {
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		//订单行是否展示金额  0不展示  非0展示	
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); 
		}catch (RuntimeException e) {
			
		}
		model.addAttribute("multi", multi);
		model.addAttribute("storeId", storeId);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("productOrganizationId", productOrganizationId);
		return "/aftersales/b2b_returns/select_shipping";
	}

	//发货明细页面数据查询
	@RequestMapping(value = "/selectShippingData", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectOrderShippingData(ModelMap model, String shippingSn,
			String vonderCode, String name, Long[] storeId, String mod,
			Long sbuId,Long productOrganizationId,Pageable pageable) {
		Page<Map<String, Object>> page = b2bReturnsService.findShippingItem(shippingSn,
				vonderCode,
				name,
				storeId,
				mod,
				sbuId,
				productOrganizationId,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	
	/**
	 * 退货单审核完修改明细平台结算价
	 * @param b2bReturns
	 * @return
	 */
	@RequestMapping(value = "/apdate_SaleOrgPrice", method = RequestMethod.POST)
	public @ResponseBody ResultMsg auditedUpdateSaleOrgPrice(B2bReturns b2bReturns) {
		B2bReturns pB2bReturns = b2bReturnsService.find(b2bReturns.getId());
		if (ConvertUtil.isEmpty(pB2bReturns)){
			 return error("退货单不存在");
		}
		if(!ConvertUtil.isEmpty(pB2bReturns.getStatus()) && 
				(pB2bReturns.getStatus()==1 || pB2bReturns.getStatus()==4 || pB2bReturns.getStatus()==5)){
			b2bReturnsService.auditedUpdateSaleOrgPrice(b2bReturns);
		}else{
			return error("只有单据状态为已审核或者部分退货、完全退货的退货单才能修改价格");
		}
		return success();
	}
	
	
	
	/**
	 * 关闭
	 * 
	 * @param
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/close", method = RequestMethod.POST)
	public @ResponseBody ResultMsg close(Long id) throws Exception {
		
		B2bReturns b2bReturns = b2bReturnsService.find(id);
		//校验退货单状态
		b2bReturnsService.checkB2bReturnsState(b2bReturns,1,"已审核","关闭");	
	
		b2bReturnsService.close(b2bReturns);
	
		return success();
	}


	/**
	 * 系统词汇控制权限
	 *
	 */
	public void parameterControl(String systemParameter, ModelMap model){
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",
				storeMemberService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		try {
			String value = SystemConfig.getConfig(systemParameter,
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int ok = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					ok++;
					break;
				}
			}
			model.addAttribute(systemParameter, ok);
		}catch (RuntimeException e) {

		}
	}

}