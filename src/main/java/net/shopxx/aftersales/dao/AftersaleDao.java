package net.shopxx.aftersales.dao;

import net.shopxx.aftersales.entity.AftersaleJudgment;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("aftersaleDao")
public class AftersaleDao extends DaoCenter {

    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;

    @Resource(name = "roleJurisdictionUtil")
    private RoleJurisdictionUtil roleJurisdictionUtil;

    public Page<Map<String, Object>> findPage(Object[] params, Pageable pageable) {
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT af.*,s.name store_name,s.out_trade_no,s.grant_code,s.dealer_name,s.head_phone");
        sql.append(" ,so.name sale_org_name,region.value region_name,p.vonder_code,sbu.name sbu_name,sm.name region_manager");
        sql.append("  FROM xx_aftersale af ");
        sql.append(" LEFT JOIN xx_store s ON af.store = s.id ");
        sql.append(" LEFT JOIN xx_area a ON a.id = af.head_new_area");
        sql.append(" LEFT JOIN xx_area b ON b.id = a.parent");
        sql.append(" LEFT JOIN xx_area c ON c.id = b.parent");
        sql.append(" LEFT JOIN xx_product p on p.id = af.product");
        sql.append(" LEFT JOIN xx_sale_org so ON af.sale_org = so.id");
        sql.append(" LEFT JOIN xx_sbu sbu ON sbu.id = af.sbu");
        sql.append(" LEFT JOIN xx_system_dict region ON region.id = so.region");
        sql.append(" LEFT JOIN xx_store_member sm ON sm.id = s.store_member");
        sql.append(" WHERE 1=1 ");
        if (companyInfoId != null) {
            sql.append(" AND af.company_info_id = ?");
            list.add(companyInfoId);
        }
        conditionQuery(sql,list,params);

        sql.append(" GROUP BY af.id ORDER BY af.modify_date DESC");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }

    public List<Map<String, Object>> findAftersaleAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_aftersale_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    /**
     * 根据订单id查找对应的附件信息
     */
    public List<Map<String, Object>> findCoupleBackAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_couple_back_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    public List<Map<String, Object>> findSurveyorAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_surveyor_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    public List<Map<String, Object>> findAgreementAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_agreement_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    public List<Map<String, Object>> findQuittanceAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_quittance_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    public List<Map<String, Object>> findReturnsAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_returns_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    public List<Map<String, Object>> findSalesContractAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_sales_contract_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    public List<Map<String, Object>> findAcceptanceSheetAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_acceptance_sheet_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    public List<Map<String, Object>> findScenePicturesAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_scene_pictures_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    public Page<Map<String, Object>> findPage1(Pageable pageable) {
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append("SELECT so.id,so.`name` factory_name,sm.`name` user_names FROM xx_sale_org so "
        		+" LEFT JOIN xx_store_member_sale_org_post smsops ON smsops.sale_org = so.id "
        		+" LEFT JOIN xx_store_member sm ON smsops.store_member = sm.id "
        		+" LEFT JOIN xx_post p ON p.id = smsops.post "
        		+" WHERE 1=1 AND so.factory_type = true AND p.sn = '2088' ");
        if(companyInfoId != null){
        	sql.append(" AND so.company_info_id = ? ");
        	list.add(companyInfoId);
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }

    public Page<Map<String, Object>> findPageAftersale(Object[] args, Pageable pageable) {
        List<Object> list = new ArrayList<Object>();
        String sn = (String) args[0];
        String name = (String) args[1];
        String dealerName = (String) args[2];
        Long storeId = (Long) args[3];
        StringBuilder sql = new StringBuilder();
        sql.append("select a.*,s.`name` store_name,s.dealer_name,s.id store_id,"
                + "so.`name` sale_org_name,so.id sale_org_id from xx_aftersale a "
                + " LEFT JOIN xx_store s ON s.id = a.store LEFT JOIN xx_sale_org so ON so.id = a.sale_org "
                + " WHERE 1=1 and a.is_return = 0 "
                + " AND a.invoice_status = '2' and return_status = 0 ");
        sql.append(" and a.company_info_id = ? ");
        list.add(WebUtils.getCurrentCompanyInfoId());
        if (sn != null) {
            sql.append(" AND a.sn LIKE ? ");
            list.add("%" + sn + "%");
        }
        if (storeId != null){
            sql.append(" AND s.id = ? ");
            list.add(storeId);
        }
        if (name != null) {
            sql.append(" AND a.name LIKE ? ");
            list.add("%" + name + "%");
        }
        if (dealerName != null) {
            sql.append(" AND s.dealerName LIKE ? ");
            list.add("%" + dealerName + "%");
        }
        StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
        if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
            String storeAuth = storeMemberService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and (a.store in (" + storeAuth + ") or s.store_member = ?)");
                list.add(WebUtils.getCurrentStoreMemberId());
            }
        } else {
//            sql.append(" and (a.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
//                    + " where smo.sale_org = s.id and smo.store_member = ?) "
//                    + " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
//                    + " where a.tree_path like concat('%,', b.id, ',%') "
//                    + " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
//                    + " where smo.sale_org = s.id and smo.store_member = ?)))");
//            list.add(WebUtils.getCurrentStoreMemberId());
//            list.add(WebUtils.getCurrentStoreMemberId());
            //用户机构
            String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
            if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
                if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
                    sql.append(" and so.id in (" + saleOrgIds + ")");
                }else{
                    sql.append(" and so.id is null");
                }
            }

            String sbus = roleJurisdictionUtil.getSbuIds();
            if(!ConvertUtil.isEmpty(sbus) && !sbus.equals("-1")){
                if(!ConvertUtil.isEmpty(sbus) && !sbus.equals("0")){
                    sql.append(" and a.sbu in (" + sbus + ")");
                }else{
                    sql.append(" and a.sbu is null");
                }
            }

        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        sql.append(" ORDER BY a.modify_date DESC");
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }
    

	public Integer count(Object[] params) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();	
		sql.append("SELECT count(af.id) FROM xx_aftersale af ");
		sql.append(" LEFT JOIN xx_store s ON af.store = s.id ");
		sql.append(" LEFT JOIN xx_area a ON a.id = af.head_new_area");
        sql.append(" LEFT JOIN xx_area b ON b.id = a.parent");
        sql.append(" LEFT JOIN xx_area c ON c.id = b.parent");
        sql.append(" LEFT JOIN xx_product p on p.id = af.product WHERE 1=1 ");
        if (companyInfoId != null) {
            sql.append(" AND af.company_info_id = ?");
            list.add(companyInfoId);
        }
        conditionQuery(sql,list,params);
        sql.append(" ORDER BY af.modify_date DESC");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
		return getNativeDao().findInt(sql.toString(),objs);
	}

	public List<Map<String, Object>> findList(Object[] params, Pageable pageable, Integer page, Integer size) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT af.*,");
        sql.append(" month(end_time) end_month,s.name AS store_name,s.out_trade_no,s.grant_code,");
        sql.append("s.dealer_name,s.head_phone,so.region,p.`name` product_name,p.model,p.vonder_code,p.spec,a.tree_path area_tree_path,");
        sql.append(" CONCAT(a.full_name,af.laying_address) address,aw.create_date,sbu.name sbu_name,sm.name region_manager,so.name sale_org_name");
        sql.append("  FROM xx_aftersale af ");
        sql.append(" LEFT JOIN xx_store s ON af.store = s.id ");
        sql.append(" LEFT JOIN xx_product p on p.id = af.product ");
        sql.append(" LEFT JOIN xx_sale_org so ON so.id = af.sale_org ");
        sql.append(" LEFT JOIN xx_area a ON a.id = af.head_new_area ");
        sql.append(" LEFT JOIN xx_area b ON b.id = a.parent");
        sql.append(" LEFT JOIN xx_area c ON c.id = b.parent");
        sql.append(" LEFT JOIN act_wf aw ON aw.id = af.wf_id ");
        sql.append(" LEFT JOIN xx_sbu sbu ON sbu.id = af.sbu");
        sql.append(" LEFT JOIN xx_store_member sm ON sm.id = s.store_member");
        sql.append(" WHERE 1=1 ");
        if (companyInfoId != null) {
            sql.append(" AND af.company_info_id = ?");
            list.add(companyInfoId);
        }
        conditionQuery(sql,list,params);
        sql.append(" GROUP BY af.id  ORDER BY af.modify_date DESC");
        if (page != null && size != null) {
        	sql.append(" limit " + (size * (page - 1)) + "," + size);
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
	
	public void conditionQuery(StringBuilder sql,List<Object> list,Object[] params){
		String sn = (String) params[0];//申请单号
		Long storeId = (Long) params[1];//经销商
		String[] invoiceStatus = (String[]) params[2];//单据状态
		String name= (String) params[3];//客户姓名
		Long contactNumber= (Long) params[4];//客户电话
		String factoryName= (String) params[5];//责任工厂
		String productName= (String) params[6];//产品名称
		Integer[] wfStatus= (Integer[]) params[7];//流程状态
		String[] grades = (String[]) params[8];//售后等级
		String beginTime = (String) params[9];//创建时间开始时间
		String endTime = (String) params[10];//创建时间结束时间
		String[] processingStatus = (String[]) params[11];//处理状态
		String factoryfirstTime= (String) params[12];//工厂批复开始时间
		String factoryEndTime = (String) params[13];//工厂批复结束时间
		String category = (String) params[14];//地板品类
		String province = (String) params[15];//省
		Long[] saleOrgIds = (Long[])params[16];//机构
		String storeName = (String)params[17];
		String dealerName = (String)params[18];
		Long[] regionIds = (Long[])params[19];
		Long[] sbuIds = (Long[])params[20];
		Long storeMemberId = (Long)params[21];
		Boolean isReceived = (Boolean)params[22];
		String vonderCode = (String)params[23];
    
		if (sn != null) {
            sql.append(" AND af.sn = ?");
            list.add(sn);
        }
        if (storeId != null) {
            sql.append(" AND af.store = ?");
            list.add(storeId);
        }
        
        
        
        if (storeMemberId != null) {
            sql.append(" AND s.store_member = ?");
            list.add(storeMemberId);
        }
        
        if (isReceived != null) {
            sql.append(" AND af.is_received = ?");
            list.add(isReceived);
        }
        
        
        if (!ConvertUtil.isEmpty(beginTime)) {
			sql.append(" AND af.create_date >= ?");
			list.add(DateUtil.convert(beginTime + " 00:00:00"));
		}
        
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" AND af.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endTime + " 00:00:00"),
					1));
		}
		
		if (!ConvertUtil.isEmpty(factoryfirstTime)) {
			sql.append(" AND af.end_time >= ?");
			list.add(DateUtil.convert(factoryfirstTime + " 00:00:00"));
		}
        
		if (!ConvertUtil.isEmpty(factoryEndTime)) {
			sql.append(" AND af.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(factoryEndTime + " 00:00:00"),
					1));
		}

        if (name != null) {
            sql.append(" AND af.name like ? ");
            list.add("%" + name + "%");
        }

        if (productName != null) {
            sql.append(" AND p.name like ? ");
            list.add("%" + productName + "%");

        }

        if(factoryName != null){
            sql.append(" AND af.factory_name like ? ");
            list.add("%"+factoryName+"%");
        }

        if(contactNumber != null){
            sql.append(" AND af.contact_number like ? ");
            list.add("%" + contactNumber +"%");
        }
		
		if (!ConvertUtil.isEmpty(category)) {
			sql.append(" AND af.category like ?");
			list.add("%" + category + "%");
		}
		
		if (!ConvertUtil.isEmpty(province)) {
			sql.append(" AND c.`name` like ?");
			list.add("%" + province + "%");
		}
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" AND s.`name` like ?");
			list.add("%" + storeName + "%");
		}
		if (!ConvertUtil.isEmpty(dealerName)) {
			sql.append(" AND s.`dealer_name` like ?");
			list.add("%" + dealerName + "%");
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" AND p.`vonder_code` like ?");
			list.add("%" + vonderCode + "%");
		}
        if (invoiceStatus != null && invoiceStatus.length > 0) {
            String os = "";
            for (int i = 0; i < invoiceStatus.length; i++) {
                if (i == invoiceStatus.length - 1)
                    os += "'" + invoiceStatus[i] + "'";
                else
                    os += "'" + invoiceStatus[i] + "',";
            }
            sql.append(" AND af.invoice_status in (" + os + ") ");
        }

        if (grades != null) {
            String gradeStr = "";
            for (int i = 0; i < grades.length; i++) {
                if (i == grades.length - 1) {
                    gradeStr += "'" + grades[i] + "'";
                } else {
                    gradeStr += "'" + grades[i] + "',";
                }

            }
            sql.append(" AND af.grade in (" + gradeStr + ") ");
        }

        if (wfStatus != null && wfStatus.length > 0) {
            String wfStatusStr = "";
            for (int i = 0; i < wfStatus.length; i++) {
                if (i == wfStatus.length - 1) {
                    wfStatusStr += wfStatus[i];
                } else {
                    wfStatusStr += wfStatus[i] + ",";
                }
            }
            sql.append(" AND af.wf_state in (" + wfStatusStr + ") ");
        }
		
		if (processingStatus != null && processingStatus.length > 0) {
            String os = "";
            for (int i = 0; i < processingStatus.length; i++) {
                if (i == processingStatus.length - 1)
                    os += "'" + processingStatus[i] + "'";
                else
                    os += "'" + processingStatus[i] + "',";
            }
            sql.append(" AND af.processing_status in (" + os + ") ");
        }
		
		if(saleOrgIds!=null) {
			StringBuilder sb = new StringBuilder();
			for (int i=0;i<saleOrgIds.length;i++) {
				if(i>0) {
					sb.append(",");
				}
				sb.append(saleOrgIds[i]);
			}
			sql.append(" AND af.sale_org in (" + sb.toString() + ") ");
		}
		if(sbuIds!=null) {
			StringBuilder sb = new StringBuilder();
			for (int i=0;i<sbuIds.length;i++) {
				if(i>0) {
					sb.append(",");
				}
				sb.append(sbuIds[i]);
			}
			sql.append(" AND af.sbu in (" + sb.toString() + ") ");
		}
		if(regionIds!=null) {
			StringBuilder sb = new StringBuilder();
			for (int i=0;i<regionIds.length;i++) {
				if(i>0) {
					sb.append(",");
				}
				sb.append(regionIds[i]);
			}
			sql.append(" AND so.region in (" + sb.toString() + ") ");
		}
		
		
        StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
        if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
            String storeAuth = storeMemberService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and (af.store in (" + storeAuth + ") or s.store_member = ?)");
                list.add(WebUtils.getCurrentStoreMemberId());
            }
        } else if(storeMemberService.hasRoles(storeMember, "工厂厂长","工厂仓管品管")) {
        	sql.append(" and af.factory_id in "
        			+ "(SELECT so.id FROM xx_sale_org so "
        			+ "JOIN xx_store_member_sale_org smso ON "
        			+ "smso.sale_org = so.id "
        			+ "AND so.factory_type = TRUE "
        			+ "AND smso.store_member = ?"
        			+ ")");
        	list.add(WebUtils.getCurrentStoreMemberId());
        } else {
            sql.append(" and (af.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = ?) "
                    + " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
                    + " where a.tree_path like concat('%,', b.id, ',%') "
                    + " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = ?)))");
            list.add(WebUtils.getCurrentStoreMemberId());
            list.add(WebUtils.getCurrentStoreMemberId());
        }
	}
	
	public List<Map<String,Object>> findHistoryAftersale(Long id, String contactNumber,Long headNewAreaId,String layingAddress){
		StringBuilder sql = null;
		List<Object> list = new ArrayList<Object>();
		String attrs = "id,sn,name,contact_number,head_new_area,laying_address,create_date";
		if(contactNumber!=null) {
			if(sql==null) {
				sql = new StringBuilder();
			}
			sql.append("SELECT "+attrs+" FROM xx_aftersale WHERE contact_number LIKE ?");
			sql.append(" AND id <> ?");
			list.add(contactNumber);
			list.add(id);
		}
		if(headNewAreaId!=null && layingAddress!=null) {
			if(sql==null) {
				sql = new StringBuilder();
			} else {
                sql.append(" UNION ");
            }
			sql.append("SELECT "+attrs+" FROM xx_aftersale WHERE head_new_area = ? AND laying_address LIKE ?");
			sql.append(" AND id <> ?");
			list.add(headNewAreaId);
			list.add(layingAddress);
			list.add(id);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < objs.length; i++) {
			objs[i] = list.get(i);
		}
        if(sql == null){
            return new ArrayList<Map<String, Object>>();
        } else {
            return getNativeDao().findListMap(sql.toString(), objs, 0);
        }
	}

    public Page<Map<String, Object>> findOrganizationPage(Pageable pageable) {
        String sql = "SELECT o.id,o.`name` FROM xx_organization o WHERE o.is_enabled = 1 AND o.company_info_id = 9";
        return getNativeDao().findPageMap(sql, null, pageable);
    }


    public AftersaleJudgment findAfterJudgment(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_aftersale_judgment WHERE company_info_id = ? and aftersale = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findSingleManaged(sql, objs, AftersaleJudgment.class);
    }

    public List<Map<String, Object>> findReturnsLogisticsAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_returns_logistics_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }


    public List<Map<String, Object>> findCollectionAttach(Long id) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * FROM xx_collection_attach WHERE company_info_id = ? and aftersales = ?";
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }
}
