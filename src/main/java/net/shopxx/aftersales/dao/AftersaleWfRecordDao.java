package net.shopxx.aftersales.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("aftersaleWfRecordDao")
public class AftersaleWfRecordDao extends DaoCenter {

    public List<Map<String, Object>> findAftersaleWfRecord(Long aftersaleId) {
        List<Object> list = new ArrayList<Object>();
        String sql = "SELECT * from xx_aftersale_wf_record where aftersale_id = ? ORDER BY end_time";
        list.add(aftersaleId);
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql, objs, 0);
    }

    /**
     * 删除指定的售后记录
     * @param aftersaleId
     */
    public void deleteByAftersaleId(Long aftersaleId){
        List<Object> list = new ArrayList<Object>();
        String sql = "delete from xx_aftersale_wf_record where aftersale_id = ? ";
        list.add(aftersaleId);
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
         getNativeDao().delete(sql, objs);
    }
}
