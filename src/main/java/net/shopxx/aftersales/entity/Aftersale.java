package net.shopxx.aftersales.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.*;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.order.entity.CustomerContract;
import net.shopxx.product.entity.Product;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 售后申请
 */
/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "xx_aftersale")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_aftersale_sequence")
public class Aftersale extends ActWfBillEntity{

    private static final long serialVersionUID = -1134806012327783273L;

    /**单号*/
    private String sn;

    /**售后等级*/
    private String grade;

    /**单据状态 0已保存 1进行中 2已生效 99作废 */
    private String invoiceStatus;

    /**处理状态*/
    private String processingStatus;

    /**退货状态 未退货0   退货中1   已退货2 */
    private String returnStatus;

    /** sbu */
    private Sbu sbu;

    /**省份城市*/
    private String area;

    /**顾客姓名*/
    private String name;

    /**地板品类*/
    private String category;

    /**勘察人*/
    private String surveyorName;

    /**勘察人*/
    private StoreMember surveyor;

//	/**勘察人电话*/
//	private String surveyorPhone;

//	/**勘察说明*/
//	private String surveyorExplain;

    /**联系电话*/
    private String contactNumber;

    /**
     * 联系电话2 2022.5.6 黄智豪新增
     */
    private String contactNumberTow;

    /**铺设面积*/
    private String layingArea;

    /**铺装地址*/
    private String layingAddress;

    /**铺设防潮*/
    private String layingMoistureproof;

    /**铺设方*/
    private String layingName;

    /**铺设时间*/
    private Date layingTime;

    /**铺设方法*/
    private String layingMethod;

    /**是否已入住*/
    private Boolean isCheckIn;

    /**入住时间*/
    private Date checkInTime;

    /**销售单价*/
    private BigDecimal price;

    /**故障时间*/
    private Date downTime;

    /**紧急程度*/
    private String emergencyDegree;

    /**销售总额*/
    private BigDecimal totalSales;

    /**采暖方式*/
    private String heatingSystem;

    /**批次板底喷码*/
    private String batchNumber;

    /**其他信息*/
    private String otherInformation;

    /**装修完毕时间*/
    private Date decorationTime;

    /**地板含水率*/
    private String floorMoistureContent;

    /**地面平整度*/
    private String floorFlatness;

    /**客户述求*/
    private String storeAppeal;

    /**合同*/
    private CustomerContract customerContract;

    /**经销商描述故障信息*/
    private String faultMessage;

//	/**总部售后调查信息*/
//	private String surveyInformation;

    /**公司处理方案*/
    private String disposeScheme;

    /**勘察说明*/
    private String surveyDescription;

    /**产品型号*/
    private Product product;

    /**机构*/
    private SaleOrg saleOrg;

    /**经销商描述故障信息*/
    private String storeFaultMessage;

    /**申请是否有效*/
    private Integer isEnabled;

    /**赔偿金额*/
    private BigDecimal compensationPrice;

    /**验收单附件*/
//	private List<AftersaleAttach> aftersaleAttachs = new ArrayList<AftersaleAttach>();

    /**销售合同附件*/
    private List<SalesContractAttach> salesContractAttachs = new ArrayList<SalesContractAttach>();

    /**验收单附件*/
    private List<AcceptanceSheetAttach> acceptanceSheetAttachs = new ArrayList<AcceptanceSheetAttach>();

    /**现场照片附件*/
    private List<ScenePicturesAttach> scenePicturesAttachs = new ArrayList<ScenePicturesAttach>();

    /**勘察图片附件*/
    private List<SurveyorAttach> surveyorAttachs = new ArrayList<SurveyorAttach>();

    /**售后处理确认单附件*/
    private List<CoupleBackAttach> coupleBackAttachs = new ArrayList<CoupleBackAttach>();

    /**售后协议书*/
    private List<AgreementAttach> agreementAttachs = new ArrayList<AgreementAttach>();

    /**收据*/
    private List<QuittanceAttach> quittanceAttachs = new ArrayList<QuittanceAttach>();

    /**退货申请单*/
    private List<ReturnsAttach> returnsAttachs = new ArrayList<ReturnsAttach>();

    /**退货确认单*/
    private List<ReturnsLogisticsAttach> returnsLogisticsAttachs = new ArrayList<ReturnsLogisticsAttach>();

    /**
     * 营销政策附件
     */
    private List<CollectionAttach> collectionAttachs = new ArrayList<CollectionAttach>();

    /**经销商*/
    private Store store;

    /**备注*/
    private String memo;

    /** 铺装地区 */
    private Area headNewArea;

    /**===============================2019-12-02 xgc新增字段=================================*/
    /** 购买时间 */
    private Date purchasingDate;

    /** 勘察人姓名 */
    private String zbsurveyorName;

    /** 联系方式  */
    private String zbsurveyorPhone;

    /** 赔偿用户金额*/
    private BigDecimal compensation;

    /** 其他费用*/
    private BigDecimal  extraCharges;

    /** 响应人*/
    private String answerName;

    /** 响应时间*/
    private Date answerDate;

    /** 无效原因*/
    private String causeInvalidity;

    /** 是否现场勘察*/
    private Integer isLocaleSurveyor;

    /** 勘察时间*/
    private Date zbsurveyorDate;

    /** 工厂名字*/
    private String responsibilityFactory;

    /** 工厂编号*/
    private Long factoryId;

    /** 责任工厂*/
    private String factoryName;

    /** 质量中心处理意见*/
    private String handlingSuggestion;

    /**
     * 是否处理 1处理 2不处理
     */
    private String whetherDispose;

    /** 责任判定*/
    private String liabilityJudgments;

    /** 核价人员*/
    private String pricingName;

    /** 核价时间*/
    private Date pricingDate;

    /** 出厂价*/
    private BigDecimal factoryPrice;

    /** 合计费用*/
    private BigDecimal amount;

    /** 工厂处理赔偿平方数*/
    private BigDecimal factoryCompensation;

    /** 经销商处理方案*/
    private String storeTreatmentScheme;

    /** 经销商处理方案-局部更换*/
    private BigDecimal storePartialReplacement;

    /** 经销商处理方案-退货*/
    private BigDecimal storeReturnGoods;

    /** 经销商处理方案-赔偿*/
    private BigDecimal storeCompensation;

    /** 回访状态*/
    private String reviewStatus;

    /** 回访结果*/
    private String reviewResult;

    /** 回访时间*/
    private Date reviewDate;

    /** 处理满意度  1.非常满意  2.满意   3.一般   4.不满意*/
    private Integer satisfaction;

    /** 响应时间*/
    private Date factoryAnswerDate;

    /** 是否退货 0是 1否*/
    private Integer isReturn;

    /** 运费承担方*/
    private String freightForwarder;

    /** 工厂处理方案*/
    private String fd;

    /** 工厂处理方案  局部更换金额*/
    private BigDecimal fdPartialReplacement;

    /** 工厂处理方案  退货*/
    private BigDecimal fdReturnGoods;

    /** 工厂处理方案  换货*/
    private BigDecimal fdReplaceGoods;
    /** 工厂处理方案  赔偿金额*/
    private BigDecimal fdCompensation;

    /** 其他意见*/
    private String opinion;

    /** 故障名称*/
    private String faultName;

    /** 板底喷码*/
    private String spurtCode;

    /**地面含水率*/
    private String groundMoistureContent;

    /**空气湿度*/
    private String airHumidity;

    /**赔款用户*/
    private BigDecimal reparationUser;

    /**其他费用*/
    private BigDecimal otherExpenses;

    /** 省长意见*/
    private String szOpinion;

    /** 售后经理*/
    private String managerOpinion;

    /** 创建人*/
    private String createrName;

    /** 平台价*/
    private BigDecimal platformPrice;

    /** 平台价合计费用*/
    private BigDecimal platformAmount;

    /**
     * 是否使用营销政策 1是 2否
     */
    private Integer marketingPolicy;

    /** 铺设方式*/
    private String layingWay;

    /** 单据类别：0售前单   1售后单*/
    private Integer formType;

    /** 业务类型 取词汇编码为businessType的词汇 */
    private SystemDict businessType;

    /** 工厂处理  是否处理： 0不处理  1处理 */
    private Integer isDeal;

    /** 工厂处理 否定理由*/
    private String objection;

    /** 工厂审批时间 */
    private Date endTime;

    /** 售后专员节点审批人*/
    private String nodeApprover;

    /** 冲账金额*/
    private BigDecimal depositRechargeAmount;

    /** 冲账备注*/
    private String depositRechargeMemo;

    /** 生产时间*/
    private Date productionTime;

    /** 故障类型*/
    private String faultType;

    /** 订单经理 */
    private StoreMember orderManager;

    /** 产品总 */
    private StoreMember productionSumUp ;

    /** 辅料费*/
    private BigDecimal subsidiaryMaterialCost;

    /** 人工费*/
    private BigDecimal labourCost;

    /** 运费*/
    private BigDecimal freight;

    /** 公司处理方案*/
    private String cd;

    /** 局部更换面积(公司处理)*/
    private BigDecimal comPartialReplacement;

    /** 退货面积(公司处理)*/
    private BigDecimal comReturnGoods;

    /** 赔偿用户金额(公司处理)*/
    private BigDecimal comCompensation;

    /** 其他费用(公司处理)*/
    private BigDecimal otherCost;

    /** 退换面积(公司处理)*/
    private BigDecimal companyCompensation;

    /** 标准费用(公司处理)*/
    private BigDecimal standardAmount;

    /** 服务副总监意见(实木)*/
    private String serviceDeputyDirectorSuggestion;


    /** 经营组织id*/
    private Organization organization;

    /** 工厂财务id*/
    private StoreMember financialStaff;

    /** 政策单号*/
    private String depositRechargeSn;

    /** 是否到账*/
    private Boolean isReceived;

    /** 到账时间*/
    private Date receiveDate;

    /** 到账金额*/
    private BigDecimal receiveAmount;

    /** 退货单号*/
    private String b2bReturnSn;

    /** 退货日期*/
    private Date returnDate;

    /** 退货单状态*/
    private Integer b2bReturnStatus;

    /** 实际退货数量*/
    private BigDecimal acturalReturnQuantity;

    /** 实际退货金额*/
    private BigDecimal acturalReturnAmount;

    /** 是否送检*/
    private Boolean isSendToQualityCenter;

    /** 样板检测单*/
    private SampleTestApply sampleTestApply;

    /** 故障原因*/
    private String faultReason;

    /** am sbu*/
    private String amSbu;

    /** 经销商要货日期*/
    private Date dateForDealersToAskForGoods;

    /** 木种*/
    private SystemDict woodType;

    /** 不处理原因*/
    private String reasonForNotProcessing;

    /** 销售渠道*/
    private SystemDict saleChannel;

    /** 属地品质意见*/
    private String sdpzOpinion;

    /**
     * 客服经理意见
     */
    private String kfjlOpinion;
    /**
     * 质量总监意见
     */
    private String pzzjOpinion;
    /**
     * Link5期工单id
     */
    private Long fiveGdId;
    /**
     * Link5工单编号
     */
    private String fiveGdSn;

    /**
     * 是否是否实木快理快赔 1是 2否
     */
    private Integer isKlkp;

    public Integer getIsKlkp() {
        return isKlkp;
    }

    public void setIsKlkp(Integer isKlkp) {
        this.isKlkp = isKlkp;
    }

    public Long getFiveGdId() {
        return fiveGdId;
    }

    public void setFiveGdId(Long fiveGdId) {
        this.fiveGdId = fiveGdId;
    }

    public String getFiveGdSn() {
        return fiveGdSn;
    }

    public void setFiveGdSn(String fiveGdSn) {
        this.fiveGdSn = fiveGdSn;
    }
    public String getKfjlOpinion() {
        return kfjlOpinion;
    }

    public String getPzzjOpinion() {
        return pzzjOpinion;
    }

    public String getWhetherDispose() {
        return whetherDispose;
    }

    public void setWhetherDispose(String whetherDispose) {
        this.whetherDispose = whetherDispose;
    }

    public void setPzzjOpinion(String pzzjOpinion) {
        this.pzzjOpinion = pzzjOpinion;
    }

    public String getJfspOpinion() {
        return jfspOpinion;
    }

    public void setJfspOpinion(String jfspOpinion) {
        this.jfspOpinion = jfspOpinion;
    }

    public void setKfjlOpinion(String kfjlOpinion) {
        this.kfjlOpinion = kfjlOpinion;
    }

    /** 集服审批意见*/
    private String jfspOpinion;

    /**
     * 是否质量问题
     */
    private Integer isQuality;

    public Integer getMarketingPolicy() {
        return marketingPolicy;
    }

    public void setMarketingPolicy(Integer marketingPolicy) {
        this.marketingPolicy = marketingPolicy;
    }

    public String getSdpzOpinion() {
        return sdpzOpinion;
    }

    public void setSdpzOpinion(String sdpzOpinion) {
        this.sdpzOpinion = sdpzOpinion;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public CustomerContract getCustomerContract() {
        return customerContract;
    }

    public void setCustomerContract(CustomerContract customerContract) {
        this.customerContract = customerContract;
    }

    public BigDecimal getActuralReturnQuantity() {
        return acturalReturnQuantity;
    }

    public void setActuralReturnQuantity(BigDecimal acturalReturnQuantity) {
        this.acturalReturnQuantity = acturalReturnQuantity;
    }

    public BigDecimal getActuralReturnAmount() {
        return acturalReturnAmount;
    }

    public void setActuralReturnAmount(BigDecimal acturalReturnAmount) {
        this.acturalReturnAmount = acturalReturnAmount;
    }

    public Integer getB2bReturnStatus() {
        return b2bReturnStatus;
    }

    public void setB2bReturnStatus(Integer b2bReturnStatus) {
        this.b2bReturnStatus = b2bReturnStatus;
    }

    public String getB2bReturnSn() {
        return b2bReturnSn;
    }

    public void setB2bReturnSn(String b2bReturnSn) {
        this.b2bReturnSn = b2bReturnSn;
    }

    public Date getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(Date returnDate) {
        this.returnDate = returnDate;
    }

    public Boolean getIsReceived() {
        return isReceived;
    }

    public void setIsReceived(Boolean isReceived) {
        this.isReceived = isReceived;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public BigDecimal getReceiveAmount() {
        return receiveAmount;
    }

    public void setReceiveAmount(BigDecimal receiveAmount) {
        this.receiveAmount = receiveAmount;
    }
    @ManyToOne(fetch = FetchType.LAZY)
    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getBusinessType() {
        return businessType;
    }

    public void setBusinessType(SystemDict businessType) {
        this.businessType = businessType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getFinancialStaff() {
        return financialStaff;
    }

    public void setFinancialStaff(StoreMember financialStaff) {
        this.financialStaff = financialStaff;
    }

    @Column(nullable = false, updatable = false, unique = true, length = 100)
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    /**
     * 获取单据状态
     * return invoiceStatus 0已保存 1进行中 2已生效 99作废
     * */
    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    /**
     * 设置单据状态
     * @param invoiceStatus 0已保存 1进行中 2已生效  99作废
     * */
    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public String getProcessingStatus() {
        return processingStatus;
    }

    public void setProcessingStatus(String processingStatus) {
        this.processingStatus = processingStatus;
    }

    public String getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(String returnStatus) {
        this.returnStatus = returnStatus;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Sbu getSbu() {
        return sbu;
    }

    public void setSbu(Sbu sbu) {
        this.sbu = sbu;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSurveyorName() {
        return surveyorName;
    }

    public void setSurveyorName(String surveyorName) {
        this.surveyorName = surveyorName;
    }


    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getSurveyor() { return surveyor; }

    public void setSurveyor(StoreMember surveyor) { this.surveyor = surveyor; }

//	public String getSurveyorPhone() {
//		return surveyorPhone;
//	}
//
//	public void setSurveyorPhone(String surveyorPhone) {
//		this.surveyorPhone = surveyorPhone;
//	}
//
//	@Length(max = 4000)
//	public String getSurveyorExplain() {
//		return surveyorExplain;
//	}
//
//	public void setSurveyorExplain(String surveyorExplain) {
//		this.surveyorExplain = surveyorExplain;
//	}

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getLayingArea() {
        return layingArea;
    }

    public void setLayingArea(String layingArea) {
        this.layingArea = layingArea;
    }

    public String getLayingAddress() {
        return layingAddress;
    }

    public void setLayingAddress(String layingAddress) {
        this.layingAddress = layingAddress;
    }

    public String getLayingMoistureproof() {
        return layingMoistureproof;
    }

    public void setLayingMoistureproof(String layingMoistureproof) {
        this.layingMoistureproof = layingMoistureproof;
    }

    public String getLayingName() {
        return layingName;
    }

    public void setLayingName(String layingName) {
        this.layingName = layingName;
    }

    public Date getLayingTime() {
        return layingTime;
    }

    public void setLayingTime(Date layingTime) {
        this.layingTime = layingTime;
    }

    public String getLayingMethod() {
        return layingMethod;
    }

    public void setLayingMethod(String layingMethod) {
        this.layingMethod = layingMethod;
    }

    public Date getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(Date checkInTime) {
        this.checkInTime = checkInTime;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Date getDownTime() {
        return downTime;
    }

    public void setDownTime(Date downTime) {
        this.downTime = downTime;
    }

    public String getEmergencyDegree() {
        return emergencyDegree;
    }

    public void setEmergencyDegree(String emergencyDegree) {
        this.emergencyDegree = emergencyDegree;
    }

    public BigDecimal getTotalSales() {
        return totalSales;
    }

    public void setTotalSales(BigDecimal totalSales) {
        this.totalSales = totalSales;
    }

    public String getHeatingSystem() {
        return heatingSystem;
    }

    public void setHeatingSystem(String heatingSystem) {
        this.heatingSystem = heatingSystem;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getOtherInformation() {
        return otherInformation;
    }

    public void setOtherInformation(String otherInformation) {
        this.otherInformation = otherInformation;
    }

    public Date getDecorationTime() {
        return decorationTime;
    }

    public void setDecorationTime(Date decorationTime) {
        this.decorationTime = decorationTime;
    }

    public String getFloorMoistureContent() {
        return floorMoistureContent;
    }

    public void setFloorMoistureContent(String floorMoistureContent) {
        this.floorMoistureContent = floorMoistureContent;
    }

    public String getFloorFlatness() {
        return floorFlatness;
    }

    public void setFloorFlatness(String floorFlatness) {
        this.floorFlatness = floorFlatness;
    }

//	@OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
//	@OrderBy("seq asc")
//	public List<AftersaleAttach> getAftersaleAttachs() {
//		return aftersaleAttachs;
//	}
//
//	public void setAftersaleAttachs(List<AftersaleAttach> aftersaleAttachs) {
//		this.aftersaleAttachs = aftersaleAttachs;
//	}

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<SalesContractAttach> getSalesContractAttachs() {
        return salesContractAttachs;
    }

    public void setSalesContractAttachs(List<SalesContractAttach> salesContractAttachs) {
        this.salesContractAttachs = salesContractAttachs;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AcceptanceSheetAttach> getAcceptanceSheetAttachs() {
        return acceptanceSheetAttachs;
    }

    public void setAcceptanceSheetAttachs(List<AcceptanceSheetAttach> acceptanceSheetAttachs) {
        this.acceptanceSheetAttachs = acceptanceSheetAttachs;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ScenePicturesAttach> getScenePicturesAttachs() {
        return scenePicturesAttachs;
    }

    public void setScenePicturesAttachs(List<ScenePicturesAttach> scenePicturesAttachs) {
        this.scenePicturesAttachs = scenePicturesAttachs;
    }

    public String getFaultMessage() {
        return faultMessage;
    }

    public void setFaultMessage(String faultMessage) {
        this.faultMessage = faultMessage;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<SurveyorAttach> getSurveyorAttachs() {
        return surveyorAttachs;
    }

    public void setSurveyorAttachs(List<SurveyorAttach> surveyorAttachs) {
        this.surveyorAttachs = surveyorAttachs;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<CoupleBackAttach> getCoupleBackAttachs() {
        return coupleBackAttachs;
    }

    public void setCoupleBackAttachs(List<CoupleBackAttach> coupleBackAttachs) {
        this.coupleBackAttachs = coupleBackAttachs;
    }

//	@Length(max = 4000)
//	public String getSurveyInformation() {
//		return surveyInformation;
//	}
//
//	public void setSurveyInformation(String surveyInformation) {
//		this.surveyInformation = surveyInformation;
//	}

    @Length(max = 4000)
    public String getDisposeScheme() {
        return disposeScheme;
    }

    public void setDisposeScheme(String disposeScheme) {
        this.disposeScheme = disposeScheme;
    }

    @Length(max = 4000)
    public String getSurveyDescription() {
        return surveyDescription;
    }

    public void setSurveyDescription(String surveyDescription) {
        this.surveyDescription = surveyDescription;
    }


    @ManyToOne(fetch = FetchType.LAZY)
    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public SaleOrg getSaleOrg() {
        return saleOrg;
    }

    public void setSaleOrg(SaleOrg saleOrg) {
        this.saleOrg = saleOrg;
    }

    @Length(max = 4000)
    public String getStoreFaultMessage() {
        return storeFaultMessage;
    }

    public void setStoreFaultMessage(String storeFaultMessage) {
        this.storeFaultMessage = storeFaultMessage;
    }

    public Integer getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    public BigDecimal getCompensationPrice() {
        return compensationPrice;
    }

    public void setCompensationPrice(BigDecimal compensationPrice) {
        this.compensationPrice = compensationPrice;
    }

    public String getStoreAppeal() {
        return storeAppeal;
    }

    public void setStoreAppeal(String storeAppeal) {
        this.storeAppeal = storeAppeal;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AgreementAttach> getAgreementAttachs() {
        return agreementAttachs;
    }

    public void setAgreementAttachs(List<AgreementAttach> agreementAttachs) {
        this.agreementAttachs = agreementAttachs;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<QuittanceAttach> getQuittanceAttachs() {
        return quittanceAttachs;
    }

    public void setQuittanceAttachs(List<QuittanceAttach> quittanceAttachs) {
        this.quittanceAttachs = quittanceAttachs;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ReturnsAttach> getReturnsAttachs() {
        return returnsAttachs;
    }

    public void setReturnsAttachs(List<ReturnsAttach> returnsAttachs) {
        this.returnsAttachs = returnsAttachs;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ReturnsLogisticsAttach> getReturnsLogisticsAttachs() {
        return returnsLogisticsAttachs;
    }

    public void setReturnsLogisticsAttachs(List<ReturnsLogisticsAttach> returnsLogisticsAttachs) {
        this.returnsLogisticsAttachs = returnsLogisticsAttachs;
    }

    @OneToMany(mappedBy = "aftersale", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<CollectionAttach> getCollectionAttachs() {
        return collectionAttachs;
    }

    public void setCollectionAttachs(List<CollectionAttach> collectionAttachs) {
        this.collectionAttachs = collectionAttachs;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Store getStore() {
        return store;
    }

    public void setStore(Store store) {
        this.store = store;
    }

    @Length(max = 4000)
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Area getHeadNewArea() {
        return headNewArea;
    }

    public void setHeadNewArea(Area headNewArea) {
        this.headNewArea = headNewArea;
    }

    public Date getPurchasingDate() {
        return purchasingDate;
    }

    public void setPurchasingDate(Date purchasingDate) {
        this.purchasingDate = purchasingDate;
    }

    public String getZbsurveyorName() {
        return zbsurveyorName;
    }

    public void setZbsurveyorName(String zbsurveyorName) {
        this.zbsurveyorName = zbsurveyorName;
    }

    public String getZbsurveyorPhone() {
        return zbsurveyorPhone;
    }

    public void setZbsurveyorPhone(String zbsurveyorPhone) {
        this.zbsurveyorPhone = zbsurveyorPhone;
    }

    public BigDecimal getCompensation() {
        return compensation;
    }

    public void setCompensation(BigDecimal compensation) {
        this.compensation = compensation;
    }

    public BigDecimal getExtraCharges() {
        return extraCharges;
    }

    public void setExtraCharges(BigDecimal extraCharges) {
        this.extraCharges = extraCharges;
    }

    public String getAnswerName() {
        return answerName;
    }

    public void setAnswerName(String answerName) {
        this.answerName = answerName;
    }

    public Date getAnswerDate() {
        return answerDate;
    }

    public void setAnswerDate(Date answerDate) {
        this.answerDate = answerDate;
    }

    public String getCauseInvalidity() {
        return causeInvalidity;
    }

    public void setCauseInvalidity(String causeInvalidity) {
        this.causeInvalidity = causeInvalidity;
    }

    public Integer getIsLocaleSurveyor() {
        return isLocaleSurveyor;
    }

    public void setIsLocaleSurveyor(Integer isLocaleSurveyor) {
        this.isLocaleSurveyor = isLocaleSurveyor;
    }

    public Date getZbsurveyorDate() {
        return zbsurveyorDate;
    }

    public void setZbsurveyorDate(Date zbsurveyorDate) {
        this.zbsurveyorDate = zbsurveyorDate;
    }

    public String getResponsibilityFactory() {
        return responsibilityFactory;
    }

    public void setResponsibilityFactory(String responsibilityFactory) {
        this.responsibilityFactory = responsibilityFactory;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public String getHandlingSuggestion() {
        return handlingSuggestion;
    }

    public void setHandlingSuggestion(String handlingSuggestion) {
        this.handlingSuggestion = handlingSuggestion;
    }

    public String getLiabilityJudgments() {
        return liabilityJudgments;
    }

    public void setLiabilityJudgments(String liabilityJudgments) {
        this.liabilityJudgments = liabilityJudgments;
    }

    public String getPricingName() {
        return pricingName;
    }

    public void setPricingName(String pricingName) {
        this.pricingName = pricingName;
    }

    public Date getPricingDate() {
        return pricingDate;
    }

    public void setPricingDate(Date pricingDate) {
        this.pricingDate = pricingDate;
    }

    public BigDecimal getFactoryPrice() {
        return factoryPrice;
    }

    public void setFactoryPrice(BigDecimal factoryPrice) {
        this.factoryPrice = factoryPrice;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getFactoryCompensation() {
        return factoryCompensation;
    }

    public void setFactoryCompensation(BigDecimal factoryCompensation) {
        this.factoryCompensation = factoryCompensation;
    }

    public String getStoreTreatmentScheme() {
        return storeTreatmentScheme;
    }

    public void setStoreTreatmentScheme(String storeTreatmentScheme) {
        this.storeTreatmentScheme = storeTreatmentScheme;
    }

    public BigDecimal getStorePartialReplacement() {
        return storePartialReplacement;
    }

    public void setStorePartialReplacement(BigDecimal storePartialReplacement) {
        this.storePartialReplacement = storePartialReplacement;
    }

    public BigDecimal getStoreReturnGoods() {
        return storeReturnGoods;
    }

    public void setStoreReturnGoods(BigDecimal storeReturnGoods) {
        this.storeReturnGoods = storeReturnGoods;
    }

    public BigDecimal getStoreCompensation() {
        return storeCompensation;
    }

    public void setStoreCompensation(BigDecimal storeCompensation) {
        this.storeCompensation = storeCompensation;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getReviewResult() {
        return reviewResult;
    }

    public void setReviewResult(String reviewResult) {
        this.reviewResult = reviewResult;
    }

    public Date getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(Date reviewDate) {
        this.reviewDate = reviewDate;
    }

    public Integer getSatisfaction() {
        return satisfaction;
    }

    public void setSatisfaction(Integer satisfaction) {
        this.satisfaction = satisfaction;
    }

    public Date getFactoryAnswerDate() {
        return factoryAnswerDate;
    }

    public void setFactoryAnswerDate(Date factoryAnswerDate) {
        this.factoryAnswerDate = factoryAnswerDate;
    }

    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }

    public String getFreightForwarder() {
        return freightForwarder;
    }

    public void setFreightForwarder(String freightForwarder) {
        this.freightForwarder = freightForwarder;
    }

    public String getFd() {
        return fd;
    }

    public void setFd(String fd) {
        this.fd = fd;
    }

    public BigDecimal getFdPartialReplacement() {
        return fdPartialReplacement;
    }

    public void setFdPartialReplacement(BigDecimal fdPartialReplacement) {
        this.fdPartialReplacement = fdPartialReplacement;
    }

    public BigDecimal getFdReturnGoods() {
        return fdReturnGoods;
    }

    public void setFdReturnGoods(BigDecimal fdReturnGoods) {
        this.fdReturnGoods = fdReturnGoods;
    }

    public BigDecimal getFdReplaceGoods() {
        return fdReplaceGoods;
    }

    public void setFdReplaceGoods(BigDecimal fdReplaceGoods) {
        this.fdReplaceGoods = fdReplaceGoods;
    }

    public BigDecimal getFdCompensation() {
        return fdCompensation;
    }

    public void setFdCompensation(BigDecimal fdCompensation) {
        this.fdCompensation = fdCompensation;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getFaultName() {
        return faultName;
    }

    public void setFaultName(String faultName) {
        this.faultName = faultName;
    }

    public String getSpurtCode() {
        return spurtCode;
    }

    public void setSpurtCode(String spurtCode) {
        this.spurtCode = spurtCode;
    }

    public String getGroundMoistureContent() {
        return groundMoistureContent;
    }

    public void setGroundMoistureContent(String groundMoistureContent) {
        this.groundMoistureContent = groundMoistureContent;
    }

    public String getAirHumidity() {
        return airHumidity;
    }

    public void setAirHumidity(String airHumidity) {
        this.airHumidity = airHumidity;
    }

    public BigDecimal getReparationUser() {
        return reparationUser;
    }

    public void setReparationUser(BigDecimal reparationUser) {
        this.reparationUser = reparationUser;
    }

    public BigDecimal getOtherExpenses() {
        return otherExpenses;
    }

    public void setOtherExpenses(BigDecimal otherExpenses) {
        this.otherExpenses = otherExpenses;
    }
    public Boolean getIsCheckIn() {
        return isCheckIn;
    }
    public void setIsCheckIn(Boolean isCheckIn) {
        this.isCheckIn = isCheckIn;
    }

    @Length(max = 4000)
    public String getSzOpinion() {
        return szOpinion;
    }

    public void setSzOpinion(String szOpinion) {
        this.szOpinion = szOpinion;
    }

    @Length(max = 4000)
    public String getManagerOpinion() {
        return managerOpinion;
    }

    public void setManagerOpinion(String managerOpinion) {
        this.managerOpinion = managerOpinion;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public BigDecimal getPlatformPrice() {
        return platformPrice;
    }

    public void setPlatformPrice(BigDecimal platformPrice) {
        this.platformPrice = platformPrice;
    }

    public BigDecimal getPlatformAmount() {
        return platformAmount;
    }

    public void setPlatformAmount(BigDecimal platformAmount) {
        this.platformAmount = platformAmount;
    }

    public String getLayingWay() {
        return layingWay;
    }

    public void setLayingWay(String layingWay) {
        this.layingWay = layingWay;
    }

    public Integer getFormType() {
        return formType;
    }

    public void setFormType(Integer formType) {
        this.formType = formType;
    }

    public Integer getIsDeal() {
        return isDeal;
    }

    public void setIsDeal(Integer isDeal) {
        this.isDeal = isDeal;
    }

    @Length(max = 4000)
    public String getObjection() {
        return objection;
    }

    public void setObjection(String objection) {
        this.objection = objection;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getNodeApprover() {
        return nodeApprover;
    }

    public void setNodeApprover(String nodeApprover) {
        this.nodeApprover = nodeApprover;
    }

    public BigDecimal getDepositRechargeAmount() {
        return depositRechargeAmount;
    }

    public void setDepositRechargeAmount(BigDecimal depositRechargeAmount) {
        this.depositRechargeAmount = depositRechargeAmount;
    }

    public String getDepositRechargeMemo() {
        return depositRechargeMemo;
    }

    public void setDepositRechargeMemo(String depositRechargeMemo) {
        this.depositRechargeMemo = depositRechargeMemo;
    }

    public Date getProductionTime() {
        return productionTime;
    }

    public void setProductionTime(Date productionTime) {
        this.productionTime = productionTime;
    }

    public String getFaultType() {
        return faultType;
    }

    public void setFaultType(String faultType) {
        this.faultType = faultType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getOrderManager() {
        return orderManager;
    }

    public void setOrderManager(StoreMember orderManager) {
        this.orderManager = orderManager;
    }
    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getProductionSumUp() {
        return productionSumUp;
    }

    public void setProductionSumUp(StoreMember productionSumUp) {
        this.productionSumUp = productionSumUp;
    }


    public BigDecimal getSubsidiaryMaterialCost() {
        return subsidiaryMaterialCost;
    }

    public void setSubsidiaryMaterialCost(BigDecimal subsidiaryMaterialCost) {
        this.subsidiaryMaterialCost = subsidiaryMaterialCost;
    }

    public BigDecimal getLabourCost() {
        return labourCost;
    }

    public void setLabourCost(BigDecimal labourCost) {
        this.labourCost = labourCost;
    }

    public BigDecimal getFreight() {
        return freight;
    }

    public void setFreight(BigDecimal freight) {
        this.freight = freight;
    }

    public String getCd() {
        return cd;
    }

    public void setCd(String cd) {
        this.cd = cd;
    }

    public BigDecimal getComPartialReplacement() {
        return comPartialReplacement;
    }

    public void setComPartialReplacement(BigDecimal comPartialReplacement) {
        this.comPartialReplacement = comPartialReplacement;
    }

    public BigDecimal getComReturnGoods() {
        return comReturnGoods;
    }

    public void setComReturnGoods(BigDecimal comReturnGoods) {
        this.comReturnGoods = comReturnGoods;
    }

    public BigDecimal getComCompensation() {
        return comCompensation;
    }

    public void setComCompensation(BigDecimal comCompensation) {
        this.comCompensation = comCompensation;
    }

    public BigDecimal getOtherCost() {
        return otherCost;
    }

    public void setOtherCost(BigDecimal otherCost) {
        this.otherCost = otherCost;
    }

    public BigDecimal getCompanyCompensation() {
        return companyCompensation;
    }

    public void setCompanyCompensation(BigDecimal companyCompensation) {
        this.companyCompensation = companyCompensation;
    }

    public BigDecimal getStandardAmount() {
        return standardAmount;
    }

    public void setStandardAmount(BigDecimal standardAmount) {
        this.standardAmount = standardAmount;
    }

    public String getServiceDeputyDirectorSuggestion() {
        return serviceDeputyDirectorSuggestion;
    }

    public void setServiceDeputyDirectorSuggestion(String serviceDeputyDirectorSuggestion) {
        this.serviceDeputyDirectorSuggestion = serviceDeputyDirectorSuggestion;
    }

    public String getDepositRechargeSn() {
        return depositRechargeSn;
    }

    public void setDepositRechargeSn(String depositRechargeSn) {
        this.depositRechargeSn = depositRechargeSn;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sample_test_apply", nullable = true, updatable = true)
    public SampleTestApply getSampleTestApply() {
        return sampleTestApply;
    }

    public void setSampleTestApply(SampleTestApply sampleTestApply) {
        this.sampleTestApply = sampleTestApply;
    }

    public Boolean getIsSendToQualityCenter() {
        return isSendToQualityCenter;
    }

    public void setIsSendToQualityCenter(Boolean isSendToQualityCenter) {
        this.isSendToQualityCenter = isSendToQualityCenter;
    }

    public String getFaultReason() {
        return faultReason;
    }

    public void setFaultReason(String faultReason) {
        this.faultReason = faultReason;
    }

    public String getAmSbu() {
        return amSbu;
    }

    public void setAmSbu(String amSbu) {
        this.amSbu = amSbu;
    }

    public Date getDateForDealersToAskForGoods() {
        return dateForDealersToAskForGoods;
    }

    public void setDateForDealersToAskForGoods(Date dateForDealersToAskForGoods) {
        this.dateForDealersToAskForGoods = dateForDealersToAskForGoods;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "wood_type", nullable = true, updatable = true)
    public SystemDict getWoodType() {
        return woodType;
    }

    public void setWoodType(SystemDict woodType) {
        this.woodType = woodType;
    }

    public String getReasonForNotProcessing() {
        return reasonForNotProcessing;
    }

    public void setReasonForNotProcessing(String reasonForNotProcessing) {
        this.reasonForNotProcessing = reasonForNotProcessing;
    }

    public String getContactNumberTow() {
        return contactNumberTow;
    }

    public void setContactNumberTow(String contactNumberTow) {
        this.contactNumberTow = contactNumberTow;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sale_channel", nullable = true, updatable = true)
    public SystemDict getSaleChannel() {
        return saleChannel;
    }

    public void setSaleChannel(SystemDict saleChannel) {
        this.saleChannel = saleChannel;
    }

    public Integer getIsQuality() {
        return isQuality;
    }

    public void setIsQuality(Integer isQuality) {
        this.isQuality = isQuality;
    }

}
