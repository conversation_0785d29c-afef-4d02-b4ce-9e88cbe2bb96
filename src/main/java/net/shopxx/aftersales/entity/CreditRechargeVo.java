package net.shopxx.aftersales.entity;

import java.math.BigDecimal;
import java.util.Date;

public class CreditRechargeVo {
	
	private String aftersaleSn;
	
	private String creditRechargeSn;
	
	private Integer creditRechargeDocStatus;
	
	private Date creditRechargeCheckDate;
	
	private BigDecimal creditRechargeActualAmount;

	public String getAftersaleSn() {
		return aftersaleSn;
	}

	public void setAftersaleSn(String aftersaleSn) {
		this.aftersaleSn = aftersaleSn;
	}

	public String getCreditRechargeSn() {
		return creditRechargeSn;
	}

	public void setCreditRechargeSn(String creditRechargeSn) {
		this.creditRechargeSn = creditRechargeSn;
	}

	public Integer getCreditRechargeDocStatus() {
		return creditRechargeDocStatus;
	}

	public void setCreditRechargeDocStatus(Integer creditRechargeDocStatus) {
		this.creditRechargeDocStatus = creditRechargeDocStatus;
	}

	public Date getCreditRechargeCheckDate() {
		return creditRechargeCheckDate;
	}

	public void setCreditRechargeCheckDate(Date creditRechargeCheckDate) {
		this.creditRechargeCheckDate = creditRechargeCheckDate;
	}

	public BigDecimal getCreditRechargeActualAmount() {
		return creditRechargeActualAmount;
	}

	public void setCreditRechargeActualAmount(BigDecimal creditRechargeActualAmount) {
		this.creditRechargeActualAmount = creditRechargeActualAmount;
	}
}
