package net.shopxx.aftersales.entity;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;

/**
 * 	售后样板申请——检测委托单
 * */
@Entity
@Table(name = "xx_test_relegated_bill_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_test_relegated_bill_attach_sequence")
public class TestRelegatedBillAttach extends BaseEntity {
	
	private SampleTestApply sampleTestApply;

	/** 附件URL */
	private String url;

	/** 备注 */
	private String memo;

	/** 文件名 */
	private String fileName;
	
	/**文件名*/
	private String name;
	
	/**文件后缀*/
	private String suffix;

	/** 序号 */
	private Integer seq;

	/** 上传人 */
	private StoreMember storeMember;

	/**
	 * @return the sampleTestApply
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "sample_test_apply", nullable = false, updatable = false)
	public SampleTestApply getSampleTestApply() {
		return sampleTestApply;
	}

	public void setSampleTestApply(SampleTestApply sampleTestApply) {
		this.sampleTestApply = sampleTestApply;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSuffix() {
		return suffix;
	}

	public void setSuffix(String suffix) {
		this.suffix = suffix;
	}

	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}
	
}
