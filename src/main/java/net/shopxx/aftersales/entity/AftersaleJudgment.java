package net.shopxx.aftersales.entity;

/**
 * Entity - 售后申请
 */

import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;

/**
 * 售后判责
 */
@Entity
@Table(name = "xx_aftersale_judgment")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_aftersale_judgment_sequence")
public class AftersaleJudgment extends BaseEntity {

    private static final long serialVersionUID = -2424971667872270382L;

    //售后申请
    private Aftersale aftersale;

    /* 属地品质售后判责一 开始*/
    /**
     * 是否质量问题  1是 0否
     */
    private String afterJudgment;

    /**
     * 质量问题  1制造责任 2产品责任 2故障原因不明
     */
    private String qualityIssue;

    /**
     * 非质量问题  1经销商责任 2用户责任
     */
    private String noQualityIssue;

    /**
     * 产品责任  1原材料 2设计 3上样 4发货 5仓储 6运输
     */
    private String productLiability;

    /**
     * 经销商责任  1安装 2仓储 3运输
     */
    private String storeDuty;

    /**
     * 用户责任  1使用 2环境
     */
    private String userDuty;

    /**
     * 占比
     */
    private String afterDutyOne;
    /* 属地品质售后判责一 结束*/

    /* 属地品质售后判责二 开始*/
    /**
     * 是否质量问题  1是 0否
     */
    private String afterJudgment2;

    /**
     * 质量问题  1制造责任 2产品责任 2故障原因不明
     */
    private String qualityIssue2;

    /**
     * 非质量问题  1经销商责任 2用户责任
     */
    private String noQualityIssue2;

    /**
     * 产品责任  1原材料 2设计 3上样 4发货 5仓储 6运输
     */
    private String productLiability2;

    /**
     * 经销商责任  1安装 2仓储 3运输
     */
    private String storeDuty2;

    /**
     * 用户责任  1使用 2环境
     */
    private String userDuty2;

    /**
     * 占比
     */
    private String afterDutyOne2;
    /* 属地品质售后判责二 结束*/

    /* 属地品质售后判责三 开始*/
    /**
     * 是否质量问题  1是 0否
     */
    private String afterJudgment3;

    /**
     * 质量问题  1制造责任 2产品责任 2故障原因不明
     */
    private String qualityIssue3;

    /**
     * 非质量问题  1经销商责任 2用户责任
     */
    private String noQualityIssue3;

    /**
     * 产品责任  1原材料 2设计 3上样 4发货 5仓储 6运输
     */
    private String productLiability3;

    /**
     * 经销商责任  1安装 2仓储 3运输
     */
    private String storeDuty3;

    /**
     * 用户责任  1使用 2环境
     */
    private String userDuty3;

    /**
     * 占比
     */
    private String afterDutyOne3;
    /* 属地品质售后判责三 结束*/


    /* 质量总监售后判责一 开始*/
    /**
     * 是否质量问题  1是 0否
     */
    private String qualitySumJudgment;

    /**
     * 质量问题  1制造责任 2产品责任 2故障原因不明
     */
    private String qualitySumIssue;

    /**
     * 非质量问题  1经销商责任 2用户责任
     */
    private String noQualitySumIssue;

    /**
     * 产品责任  1原材料 2设计 3上样 4发货 5仓储 6运输
     */
    private String qualitySumProductLiability;

    /**
     * 经销商责任  1安装 2仓储 3运输
     */
    private String qualitySumStoreDuty;

    /**
     * 用户责任  1使用 2环境
     */
    private String qualitySumUserDuty;

    /**
     * 占比
     */
    private String qualitySumDutyOne;
    /* 质量总监售后判责一 结束*/


    /* 质量总监售后判责二 开始*/
    /**
     * 是否质量问题  1是 0否
     */
    private String qualitySumJudgment2;

    /**
     * 质量问题  1制造责任 2产品责任 2故障原因不明
     */
    private String qualitySumIssue2;

    /**
     * 非质量问题  1经销商责任 2用户责任
     */
    private String noQualitySumIssue2;

    /**
     * 产品责任  1原材料 2设计 3上样 4发货 5仓储 6运输
     */
    private String qualitySumProductLiability2;

    /**
     * 经销商责任  1安装 2仓储 3运输
     */
    private String qualitySumStoreDuty2;

    /**
     * 用户责任  1使用 2环境
     */
    private String qualitySumUserDuty2;

    /**
     * 占比
     */
    private String qualitySumDutyOne2;
    /* 质量总监售后判责二 结束*/


    /* 质量总监售后判责三 开始*/
    /**
     * 是否质量问题  1是 0否
     */
    private String qualitySumJudgment3;

    /**
     * 质量问题  1制造责任 2产品责任 2故障原因不明
     */
    private String qualitySumIssue3;

    /**
     * 非质量问题  1经销商责任 2用户责任
     */
    private String noQualitySumIssue3;

    /**
     * 产品责任  1原材料 2设计 3上样 4发货 5仓储 6运输
     */
    private String qualitySumProductLiability3;

    /**
     * 经销商责任  1安装 2仓储 3运输
     */
    private String qualitySumStoreDuty3;

    /**
     * 用户责任  1使用 2环境
     */
    private String qualitySumUserDuty3;

    /**
     * 占比
     */
    private String qualitySumDutyOne3;
    /* 质量总监售后判责三 结束*/


    public String getQualitySumJudgment() {
        return qualitySumJudgment;
    }

    public void setQualitySumJudgment(String qualitySumJudgment) {
        this.qualitySumJudgment = qualitySumJudgment;
    }

    public String getQualitySumIssue() {
        return qualitySumIssue;
    }

    public void setQualitySumIssue(String qualitySumIssue) {
        this.qualitySumIssue = qualitySumIssue;
    }

    public String getNoQualitySumIssue() {
        return noQualitySumIssue;
    }

    public void setNoQualitySumIssue(String noQualitySumIssue) {
        this.noQualitySumIssue = noQualitySumIssue;
    }

    public String getQualitySumProductLiability() {
        return qualitySumProductLiability;
    }

    public void setQualitySumProductLiability(String qualitySumProductLiability) {
        this.qualitySumProductLiability = qualitySumProductLiability;
    }

    public String getQualitySumStoreDuty() {
        return qualitySumStoreDuty;
    }

    public void setQualitySumStoreDuty(String qualitySumStoreDuty) {
        this.qualitySumStoreDuty = qualitySumStoreDuty;
    }

    public String getQualitySumUserDuty() {
        return qualitySumUserDuty;
    }

    public void setQualitySumUserDuty(String qualitySumUserDuty) {
        this.qualitySumUserDuty = qualitySumUserDuty;
    }

    public String getQualitySumDutyOne() {
        return qualitySumDutyOne;
    }

    public void setQualitySumDutyOne(String qualitySumDutyOne) {
        this.qualitySumDutyOne = qualitySumDutyOne;
    }

    public String getQualitySumJudgment2() {
        return qualitySumJudgment2;
    }

    public void setQualitySumJudgment2(String qualitySumJudgment2) {
        this.qualitySumJudgment2 = qualitySumJudgment2;
    }

    public String getQualitySumIssue2() {
        return qualitySumIssue2;
    }

    public void setQualitySumIssue2(String qualitySumIssue2) {
        this.qualitySumIssue2 = qualitySumIssue2;
    }

    public String getNoQualitySumIssue2() {
        return noQualitySumIssue2;
    }

    public void setNoQualitySumIssue2(String noQualitySumIssue2) {
        this.noQualitySumIssue2 = noQualitySumIssue2;
    }

    public String getQualitySumProductLiability2() {
        return qualitySumProductLiability2;
    }

    public void setQualitySumProductLiability2(String qualitySumProductLiability2) {
        this.qualitySumProductLiability2 = qualitySumProductLiability2;
    }

    public String getQualitySumStoreDuty2() {
        return qualitySumStoreDuty2;
    }

    public void setQualitySumStoreDuty2(String qualitySumStoreDuty2) {
        this.qualitySumStoreDuty2 = qualitySumStoreDuty2;
    }

    public String getQualitySumUserDuty2() {
        return qualitySumUserDuty2;
    }

    public void setQualitySumUserDuty2(String qualitySumUserDuty2) {
        this.qualitySumUserDuty2 = qualitySumUserDuty2;
    }

    public String getQualitySumDutyOne2() {
        return qualitySumDutyOne2;
    }

    public void setQualitySumDutyOne2(String qualitySumDutyOne2) {
        this.qualitySumDutyOne2 = qualitySumDutyOne2;
    }

    public String getQualitySumJudgment3() {
        return qualitySumJudgment3;
    }

    public void setQualitySumJudgment3(String qualitySumJudgment3) {
        this.qualitySumJudgment3 = qualitySumJudgment3;
    }

    public String getQualitySumIssue3() {
        return qualitySumIssue3;
    }

    public void setQualitySumIssue3(String qualitySumIssue3) {
        this.qualitySumIssue3 = qualitySumIssue3;
    }

    public String getNoQualitySumIssue3() {
        return noQualitySumIssue3;
    }

    public void setNoQualitySumIssue3(String noQualitySumIssue3) {
        this.noQualitySumIssue3 = noQualitySumIssue3;
    }

    public String getQualitySumProductLiability3() {
        return qualitySumProductLiability3;
    }

    public void setQualitySumProductLiability3(String qualitySumProductLiability3) {
        this.qualitySumProductLiability3 = qualitySumProductLiability3;
    }

    public String getQualitySumStoreDuty3() {
        return qualitySumStoreDuty3;
    }

    public void setQualitySumStoreDuty3(String qualitySumStoreDuty3) {
        this.qualitySumStoreDuty3 = qualitySumStoreDuty3;
    }

    public String getQualitySumUserDuty3() {
        return qualitySumUserDuty3;
    }

    public void setQualitySumUserDuty3(String qualitySumUserDuty3) {
        this.qualitySumUserDuty3 = qualitySumUserDuty3;
    }

    public String getQualitySumDutyOne3() {
        return qualitySumDutyOne3;
    }

    public void setQualitySumDutyOne3(String qualitySumDutyOne3) {
        this.qualitySumDutyOne3 = qualitySumDutyOne3;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Aftersale getAftersale() {
        return aftersale;
    }

    public void setAftersale(Aftersale aftersale) {
        this.aftersale = aftersale;
    }

    public String getAfterJudgment() {
        return afterJudgment;
    }

    public void setAfterJudgment(String afterJudgment) {
        this.afterJudgment = afterJudgment;
    }

    public String getQualityIssue() {
        return qualityIssue;
    }

    public void setQualityIssue(String qualityIssue) {
        this.qualityIssue = qualityIssue;
    }

    public String getNoQualityIssue() {
        return noQualityIssue;
    }

    public void setNoQualityIssue(String noQualityIssue) {
        this.noQualityIssue = noQualityIssue;
    }

    public String getProductLiability() {
        return productLiability;
    }

    public void setProductLiability(String productLiability) {
        this.productLiability = productLiability;
    }

    public String getStoreDuty() {
        return storeDuty;
    }

    public void setStoreDuty(String storeDuty) {
        this.storeDuty = storeDuty;
    }

    public String getUserDuty() {
        return userDuty;
    }

    public void setUserDuty(String userDuty) {
        this.userDuty = userDuty;
    }

    public String getAfterDutyOne() {
        return afterDutyOne;
    }

    public void setAfterDutyOne(String afterDutyOne) {
        this.afterDutyOne = afterDutyOne;
    }

    public String getAfterJudgment2() {
        return afterJudgment2;
    }

    public void setAfterJudgment2(String afterJudgment2) {
        this.afterJudgment2 = afterJudgment2;
    }

    public String getQualityIssue2() {
        return qualityIssue2;
    }

    public void setQualityIssue2(String qualityIssue2) {
        this.qualityIssue2 = qualityIssue2;
    }

    public String getNoQualityIssue2() {
        return noQualityIssue2;
    }

    public void setNoQualityIssue2(String noQualityIssue2) {
        this.noQualityIssue2 = noQualityIssue2;
    }

    public String getProductLiability2() {
        return productLiability2;
    }

    public void setProductLiability2(String productLiability2) {
        this.productLiability2 = productLiability2;
    }

    public String getStoreDuty2() {
        return storeDuty2;
    }

    public void setStoreDuty2(String storeDuty2) {
        this.storeDuty2 = storeDuty2;
    }

    public String getUserDuty2() {
        return userDuty2;
    }

    public void setUserDuty2(String userDuty2) {
        this.userDuty2 = userDuty2;
    }

    public String getAfterDutyOne2() {
        return afterDutyOne2;
    }

    public void setAfterDutyOne2(String afterDutyOne2) {
        this.afterDutyOne2 = afterDutyOne2;
    }

    public String getAfterJudgment3() {
        return afterJudgment3;
    }

    public void setAfterJudgment3(String afterJudgment3) {
        this.afterJudgment3 = afterJudgment3;
    }

    public String getQualityIssue3() {
        return qualityIssue3;
    }

    public void setQualityIssue3(String qualityIssue3) {
        this.qualityIssue3 = qualityIssue3;
    }

    public String getNoQualityIssue3() {
        return noQualityIssue3;
    }

    public void setNoQualityIssue3(String noQualityIssue3) {
        this.noQualityIssue3 = noQualityIssue3;
    }

    public String getProductLiability3() {
        return productLiability3;
    }

    public void setProductLiability3(String productLiability3) {
        this.productLiability3 = productLiability3;
    }

    public String getStoreDuty3() {
        return storeDuty3;
    }

    public void setStoreDuty3(String storeDuty3) {
        this.storeDuty3 = storeDuty3;
    }

    public String getUserDuty3() {
        return userDuty3;
    }

    public void setUserDuty3(String userDuty3) {
        this.userDuty3 = userDuty3;
    }

    public String getAfterDutyOne3() {
        return afterDutyOne3;
    }

    public void setAfterDutyOne3(String afterDutyOne3) {
        this.afterDutyOne3 = afterDutyOne3;
    }
}
