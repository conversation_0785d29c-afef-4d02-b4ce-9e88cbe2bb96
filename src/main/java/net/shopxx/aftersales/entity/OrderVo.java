package net.shopxx.aftersales.entity;

import java.math.BigDecimal;
import java.util.Date;

public class OrderVo {
	
	private String aftersaleSn;
	
	private String orderSn;
	
	private Integer orderStatus;
	
	private Date orderCheckDate;
	
	private BigDecimal orderAmount;

	private BigDecimal orderShippedQuantity;

	public String getAftersaleSn() {
		return aftersaleSn;
	}

	public void setAftersaleSn(String aftersaleSn) {
		this.aftersaleSn = aftersaleSn;
	}

	public String getOrderSn() {
		return orderSn;
	}

	public void setOrderSn(String orderSn) {
		this.orderSn = orderSn;
	}

	public Integer getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(Integer orderStatus) {
		this.orderStatus = orderStatus;
	}

	public Date getOrderCheckDate() {
		return orderCheckDate;
	}

	public void setOrderCheckDate(Date orderCheckDate) {
		this.orderCheckDate = orderCheckDate;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public BigDecimal getOrderShippedQuantity() {
		return orderShippedQuantity;
	}

	public void setOrderShippedQuantity(BigDecimal orderShippedQuantity) {
		this.orderShippedQuantity = orderShippedQuantity;
	}
}
