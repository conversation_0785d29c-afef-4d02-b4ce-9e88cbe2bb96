package net.shopxx.aftersales.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.product.entity.Product;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Entity
@Table(name = "xx_sample_test_apply")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_sample_test_apply_sequence")
public class SampleTestApply extends ActWfBillEntity{

	private static final long serialVersionUID = 1001898921230723372L;
	
	/** 单号*/
	private String sn;
	
	/** 单据状态 0已保存 1进行中 2已生效 99已作废*/
	private Integer invoiceStatus;
	
	/** 申请人*/
	private StoreMember applicant;
	
	/** 产品品类*/
	private String productCategory;
	
	/** 产品*/
	private Product product;
	
	/** 样板地址——省市区*/
	private Area area;
	
	/** 样板地址——镇街门牌号*/
	private String address;
	
	/** 样板数量*/
	private Integer sampleQuantity;
	
	/** 检测内容*/
	private String testContent;
	
	/** 经销商快递单号*/
	private String distributorDeliveryBillSn;
	
	/** 样板是否寄回 0否 1是  2质量中心自行处理*/
	private Integer isSendBack;
	
	/** 邮寄地址*/
	private String deliveryAddress;
	
	
	/** 样板检测结果*/
	private String sampleTestResult;
	
	/** 检测委托单*/
	private List<TestRelegatedBillAttach> testRelegatedBillAttach = new ArrayList<TestRelegatedBillAttach>();
	
	/** 样板检测报告*/
	private List<SampleTestReportAttach> sampleTestReportAttach = new ArrayList<SampleTestReportAttach>();
	
	/** 样板图片*/
	private List<SamplePictureAttach> samplePictureAttach = new ArrayList<SamplePictureAttach>();
	
	/** 样板邮寄快递单号*/
	private String sampleDeliveryBillSn;
	
	/** 经销商*/
	private Store store;
	
	/** 工厂*/
	private SaleOrg factory;
	
	/** 到厂日期*/
	private Date receiptDate;
	
	/** 产品标准*/
	private String productStandard;
	
	/** 产品状态*/
	private Integer productStatus;

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}


	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getApplicant() {
		return applicant;
	}

	public void setApplicant(StoreMember applicant) {
		this.applicant = applicant;
	}

	public String getProductCategory() {
		return productCategory;
	}

	public void setProductCategory(String productCategory) {
		this.productCategory = productCategory;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Area getArea() {
		return area;
	}

	public void setArea(Area area) {
		this.area = area;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Integer getSampleQuantity() {
		return sampleQuantity;
	}

	public void setSampleQuantity(Integer sampleQuantity) {
		this.sampleQuantity = sampleQuantity;
	}

	public String getTestContent() {
		return testContent;
	}

	public void setTestContent(String testContent) {
		this.testContent = testContent;
	}

	public String getDistributorDeliveryBillSn() {
		return distributorDeliveryBillSn;
	}

	public void setDistributorDeliveryBillSn(String distributorDeliveryBillSn) {
		this.distributorDeliveryBillSn = distributorDeliveryBillSn;
	}

	public Integer getIsSendBack() {
		return isSendBack;
	}

	public void setIsSendBack(Integer isSendBack) {
		this.isSendBack = isSendBack;
	}

	public String getDeliveryAddress() {
		return deliveryAddress;
	}

	public void setDeliveryAddress(String deliveryAddress) {
		this.deliveryAddress = deliveryAddress;
	}


	public String getSampleTestResult() {
		return sampleTestResult;
	}

	public void setSampleTestResult(String sampleTestResult) {
		this.sampleTestResult = sampleTestResult;
	}

	@OneToMany(mappedBy = "sampleTestApply", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<TestRelegatedBillAttach> getTestRelegatedBillAttach() {
		return testRelegatedBillAttach;
	}

	public void setTestRelegatedBillAttach(List<TestRelegatedBillAttach> testRelegatedBillAttach) {
		this.testRelegatedBillAttach = testRelegatedBillAttach;
	}

	@OneToMany(mappedBy = "sampleTestApply", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<SampleTestReportAttach> getSampleTestReportAttach() {
		return sampleTestReportAttach;
	}

	public void setSampleTestReportAttach(List<SampleTestReportAttach> sampleTestReportAttach) {
		this.sampleTestReportAttach = sampleTestReportAttach;
	}

	@OneToMany(mappedBy = "sampleTestApply", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<SamplePictureAttach> getSamplePictureAttach() {
		return samplePictureAttach;
	}

	public void setSamplePictureAttach(List<SamplePictureAttach> samplePictureAttach) {
		this.samplePictureAttach = samplePictureAttach;
	}

	public String getSampleDeliveryBillSn() {
		return sampleDeliveryBillSn;
	}

	public void setSampleDeliveryBillSn(String sampleDeliveryBillSn) {
		this.sampleDeliveryBillSn = sampleDeliveryBillSn;
	}

	public Integer getInvoiceStatus() {
		return invoiceStatus;
	}

	public void setInvoiceStatus(Integer invoiceStatus) {
		this.invoiceStatus = invoiceStatus;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}

	public void setStore(Store store) {
		this.store = store;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getFactory() {
		return factory;
	}

	public void setFactory(SaleOrg factory) {
		this.factory = factory;
	}

	public Date getReceiptDate() {
		return receiptDate;
	}

	public void setReceiptDate(Date receiptDate) {
		this.receiptDate = receiptDate;
	}

	public String getProductStandard() {
		return productStandard;
	}

	public void setProductStandard(String productStandard) {
		this.productStandard = productStandard;
	}

	public Integer getProductStatus() {
		return productStatus;
	}

	public void setProductStatus(Integer productStatus) {
		this.productStatus = productStatus;
	}
	
}
