package net.shopxx.aftersales.service.impl;

import net.shopxx.aftersales.entity.AftersaleJudgment;
import net.shopxx.aftersales.service.AftersaleJudgmentService;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ServletContextAware;

import javax.servlet.ServletContext;


@Service("aftersaleJudgmentServiceImpl")
public class AftersaleJudgmentServiceImpl extends BaseServiceImpl<AftersaleJudgment> implements AftersaleJudgmentService, ServletContextAware {

    /**
     * servletContext
     */
    private ServletContext servletContext;

    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

}
