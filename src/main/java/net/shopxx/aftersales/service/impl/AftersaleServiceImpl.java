package net.shopxx.aftersales.service.impl;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.aftersales.dao.AftersaleDao;
import net.shopxx.aftersales.entity.*;
import net.shopxx.aftersales.service.AftersaleJudgmentService;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.*;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.finance.service.PolicyEntryService;
import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.dao.Oa_WfModelDao;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.intf.service.OaToWfService;
import net.shopxx.link5.entity.AccessToken;
import net.shopxx.link5.service.AccessTokenService;
import net.shopxx.link5.util.StoreSyncErrMsgResolver;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductSbu;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.product.service.ProductSbuService;
import net.shopxx.util.SnUtil;
import net.shopxx.util.TextareaLineBreakUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("aftersaleServiceImpl")
public class AftersaleServiceImpl extends ActWfBillServiceImpl<Aftersale> implements AftersaleService {

    @Resource(name = "aftersaleDao")
    private AftersaleDao aftersaleDao;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgBaseService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "oaToWfServiceImpl")
    private OaToWfService oaToWfService;
    @Resource(name = "oa_WfModelDao")
    private Oa_WfModelDao oa_WfModelDao;
    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
    @Resource(name = "productCategoryBaseServiceImpl")
    private ProductCategoryBaseService productCategoryBaseService;
    @Resource(name = "policyEntryServiceImpl")
    private PolicyEntryService policyEntryService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "organizationServiceImpl")
    private OrganizationService organizationService;
    @Resource(name = "productSbuServiceImpl")
    private ProductSbuService productSbuService;
    @Autowired
    private AftersaleJudgmentService aftersaleJudgmentService;
    @Autowired
    private AccessTokenService accessTokenService;
    @Autowired
    private CompanyInfoBaseService companyInfoBaseService;
    @Autowired
    private A1_MessageToHService a1_MessageToHService;

    @Override
    public void aftersaleSave(Aftersale aftersale, Long saleOrgId, Long productId, Long storeId, String[] storeAppeal,
                              Long categoryId,Long companyInfoId,Long aftersaleId, AftersaleJudgment aftersaleJudgment) {
        String snTitle = productCategoryBaseService.findSnTitle(categoryId);
        SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
        Product product = productBaseService.find(productId);
        Store store = storeBaseService.find(storeId);
        //非同步数据
        if (companyInfoId==null){
            aftersale.setInvoiceStatus("0");
            String sn = SnUtil.getAftersaleSn(snTitle,companyInfoId);
            aftersale.setSn(sn);
        }else {
            aftersale.setSn(aftersale.getSn());
        }
        aftersale.setSaleOrg(saleOrg);
        aftersale.setProduct(product);
        aftersale.setStore(store);
        aftersale.setStoreAppeal(splicer(storeAppeal));
        if (companyInfoId==null){
            aftersale.setCreaterName(storeMemberService.getCurrent().getName());
        }else {
            aftersale.setCompanyInfoId(companyInfoId);
        }
        if(aftersale.getSbu() == null) {
            List<Filter> filters = new ArrayList<Filter>();
            filters.add(Filter.eq("product", product));
            List<ProductSbu> productSbu = productSbuService.findList(null, filters, null);
            if (productSbu.size() > 0) {
                aftersale.setSbu(productSbu.get(0).getSbu());
            }
        }
        // 销售合同附件
        List<SalesContractAttach> salesContractAttachs = aftersale.getSalesContractAttachs();
        for (Iterator<SalesContractAttach> iterator = salesContractAttachs.iterator(); iterator.hasNext();) {
            SalesContractAttach salesContractAttach = iterator.next();
            if (salesContractAttach == null || salesContractAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (salesContractAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            salesContractAttach.setFileName(salesContractAttach.getName() + "." + salesContractAttach.getSuffix());
            salesContractAttach.setAftersale(aftersale);
            if (companyInfoId==null){   //外部调用无法拿到当前用户
                salesContractAttach.setStoreMember(storeMemberService.getCurrent());
            }else {
                salesContractAttach.setCompanyInfoId(companyInfoId);
            }
        }
        aftersale.setSalesContractAttachs(salesContractAttachs);

        // 现场照片附件
        List<AcceptanceSheetAttach> acceptanceSheetAttachs = aftersale.getAcceptanceSheetAttachs();
        for (Iterator<AcceptanceSheetAttach> iterator = acceptanceSheetAttachs.iterator(); iterator.hasNext();) {
            AcceptanceSheetAttach acceptanceSheetAttach = iterator.next();
            if (acceptanceSheetAttach == null || acceptanceSheetAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (acceptanceSheetAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            acceptanceSheetAttach
                    .setFileName(acceptanceSheetAttach.getName() + "." + acceptanceSheetAttach.getSuffix());
            acceptanceSheetAttach.setAftersale(aftersale);
            if (companyInfoId==null){
                acceptanceSheetAttach.setStoreMember(storeMemberService.getCurrent());
            }else {
                acceptanceSheetAttach.setCompanyInfoId(companyInfoId);
            }
        }
        aftersale.setAcceptanceSheetAttachs(acceptanceSheetAttachs);

        // 现场照片附件
        List<ScenePicturesAttach> scenePicturesAttachs = aftersale.getScenePicturesAttachs();
        for (Iterator<ScenePicturesAttach> iterator = scenePicturesAttachs.iterator(); iterator.hasNext();) {
            ScenePicturesAttach scenePicturesAttach = iterator.next();
            if (scenePicturesAttach == null || scenePicturesAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (scenePicturesAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            scenePicturesAttach.setFileName(scenePicturesAttach.getName() + "." + scenePicturesAttach.getSuffix());
            scenePicturesAttach.setAftersale(aftersale);
            if (companyInfoId==null){
                scenePicturesAttach.setStoreMember(storeMemberService.getCurrent());
            }else {
                scenePicturesAttach.setCompanyInfoId(companyInfoId);
            }
        }
        aftersale.setScenePicturesAttachs(scenePicturesAttachs);

        List<SurveyorAttach> surveyorAttachs = aftersale.getSurveyorAttachs();
        for (Iterator<SurveyorAttach> iterator = surveyorAttachs.iterator(); iterator.hasNext();) {
            SurveyorAttach surveyorAttach = iterator.next();
            if (surveyorAttach == null || surveyorAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (surveyorAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            surveyorAttach.setFileName(surveyorAttach.getName() + "." + surveyorAttach.getSuffix());
            surveyorAttach.setAftersale(aftersale);
            if (companyInfoId==null){
                surveyorAttach.setStoreMember(storeMemberService.getCurrent());
            }else {
                surveyorAttach.setCompanyInfoId(companyInfoId);
            }
        }
        aftersale.setSurveyorAttachs(surveyorAttachs);

        List<CoupleBackAttach> coupleBackAttachs = aftersale.getCoupleBackAttachs();
        for (Iterator<CoupleBackAttach> iterator = coupleBackAttachs.iterator(); iterator.hasNext();) {
            CoupleBackAttach coupleBackAttach = iterator.next();
            if (coupleBackAttach == null || coupleBackAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (coupleBackAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            coupleBackAttach.setFileName(coupleBackAttach.getName() + "." + coupleBackAttach.getSuffix());
            coupleBackAttach.setAftersale(aftersale);
            if (companyInfoId==null){
                coupleBackAttach.setStoreMember(storeMemberService.getCurrent());
            }else {
                coupleBackAttach.setCompanyInfoId(companyInfoId);
            }
        }
        aftersale.setCoupleBackAttachs(coupleBackAttachs);

        List<AgreementAttach> agreementAttachs = aftersale.getAgreementAttachs();
        for (Iterator<AgreementAttach> iterator = agreementAttachs.iterator(); iterator.hasNext();) {
            AgreementAttach agreementAttach = iterator.next();
            if (agreementAttach == null || agreementAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (agreementAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            agreementAttach.setFileName(agreementAttach.getName() + "." + agreementAttach.getSuffix());
            agreementAttach.setAftersale(aftersale);
            if (companyInfoId==null){
                agreementAttach.setStoreMember(storeMemberService.getCurrent());
            }else {
                agreementAttach.setCompanyInfoId(companyInfoId);
            }
        }
        aftersale.setAgreementAttachs(agreementAttachs);

        List<QuittanceAttach> quittanceAttachs = aftersale.getQuittanceAttachs();
        for (Iterator<QuittanceAttach> iterator = quittanceAttachs.iterator(); iterator.hasNext();) {
            QuittanceAttach quittanceAttach = iterator.next();
            if (quittanceAttach == null || quittanceAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (quittanceAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            quittanceAttach.setFileName(quittanceAttach.getName() + "." + quittanceAttach.getSuffix());
            quittanceAttach.setAftersale(aftersale);
            if (companyInfoId==null){
                quittanceAttach.setStoreMember(storeMemberService.getCurrent());
            }else {
                quittanceAttach.setCompanyInfoId(companyInfoId);
            }
        }
        aftersale.setQuittanceAttachs(quittanceAttachs);

        List<ReturnsLogisticsAttach> returnsLogisticsAttachs = aftersale.getReturnsLogisticsAttachs();
        for (Iterator<ReturnsLogisticsAttach> iterator = returnsLogisticsAttachs.iterator(); iterator.hasNext();) {
            ReturnsLogisticsAttach returnsLogisticsAttach = iterator.next();
            if (returnsLogisticsAttach == null || returnsLogisticsAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (returnsLogisticsAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            returnsLogisticsAttach.setFileName(returnsLogisticsAttach.getName() + "." + returnsLogisticsAttach.getSuffix());
            returnsLogisticsAttach.setAftersale(aftersale);
            if (companyInfoId==null){
                returnsLogisticsAttach.setStoreMember(storeMemberService.getCurrent());
            }else {
                returnsLogisticsAttach.setCompanyInfoId(companyInfoId);
            }
        }
        aftersale.setReturnsLogisticsAttachs(returnsLogisticsAttachs);

        //根据单号查询有无重复订单
        if (aftersaleId!=null){   //已经有重复单号 避免重复推送报错
            aftersale.setId(aftersaleId);
            update(aftersale);
        }else {
            save(aftersale);
        }
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("aftersale",aftersale));
        AftersaleJudgment a = aftersaleJudgmentService.find(filters);
        aftersaleJudgmentService.delete(a);
        if(aftersaleJudgment != null) {
            aftersaleJudgment.setId(null);
            aftersaleJudgment.setAftersale(aftersale);
            aftersaleJudgmentService.save(aftersaleJudgment);
        }
    }

    @Override
    public void aftersaleUpdate(Aftersale aftersale, Long saleOrgId, Long productId, Long storeId,
                                String[] storeAppeal) {
        SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
        Product product = productBaseService.find(productId);
        Store store = storeBaseService.find(storeId);
        aftersale.setSaleOrg(saleOrg);
        aftersale.setProduct(product);
        aftersale.setStore(store);
        aftersale.setStoreAppeal(splicer(storeAppeal));
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("product", product));
        List<ProductSbu> productSbu = productSbuService.findList(null, filters, null);
        if(productSbu.size()>0){
            aftersale.setSbu(productSbu.get(0).getSbu());
        }
        // 现场照片附件
        // List<AftersaleAttach> aftersaleAttachs =
        // aftersale.getAftersaleAttachs();
        // for (Iterator<AftersaleAttach> iterator =
        // aftersaleAttachs.iterator(); iterator.hasNext();){
        // AftersaleAttach aftersaleAttach = iterator.next();
        // if(aftersaleAttach == null || aftersaleAttach.getUrl() ==null){
        // iterator.remove();
        // continue;
        // }
        // if(aftersaleAttach.getName() == null){
        // ExceptionUtil.throwServiceException("附件名不能为空");
        // }
        // aftersaleAttach.setFileName(aftersaleAttach.getName()
        // + "."
        // + aftersaleAttach.getSuffix());
        // aftersaleAttach.setAftersale(aftersale);
        // aftersaleAttach.setStoreMember(storeMemberService.getCurrent());
        // }
        // aftersale.setAftersaleAttachs(aftersaleAttachs);

        // 销售合同附件
        List<SalesContractAttach> salesContractAttachs = aftersale.getSalesContractAttachs();
        for (Iterator<SalesContractAttach> iterator = salesContractAttachs.iterator(); iterator.hasNext();) {
            SalesContractAttach salesContractAttach = iterator.next();
            if (salesContractAttach == null || salesContractAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (salesContractAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            salesContractAttach.setFileName(salesContractAttach.getName() + "." + salesContractAttach.getSuffix());
            salesContractAttach.setAftersale(aftersale);
            salesContractAttach.setStoreMember(storeMemberService.getCurrent());
        }
        aftersale.setSalesContractAttachs(salesContractAttachs);

        // 现场照片附件
        List<AcceptanceSheetAttach> acceptanceSheetAttachs = aftersale.getAcceptanceSheetAttachs();
        for (Iterator<AcceptanceSheetAttach> iterator = acceptanceSheetAttachs.iterator(); iterator.hasNext();) {
            AcceptanceSheetAttach acceptanceSheetAttach = iterator.next();
            if (acceptanceSheetAttach == null || acceptanceSheetAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (acceptanceSheetAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            acceptanceSheetAttach
                    .setFileName(acceptanceSheetAttach.getName() + "." + acceptanceSheetAttach.getSuffix());
            acceptanceSheetAttach.setAftersale(aftersale);
            acceptanceSheetAttach.setStoreMember(storeMemberService.getCurrent());
        }
        aftersale.setAcceptanceSheetAttachs(acceptanceSheetAttachs);

        // 现场照片附件
        List<ScenePicturesAttach> scenePicturesAttachs = aftersale.getScenePicturesAttachs();
        for (Iterator<ScenePicturesAttach> iterator = scenePicturesAttachs.iterator(); iterator.hasNext();) {
            ScenePicturesAttach scenePicturesAttach = iterator.next();
            if (scenePicturesAttach == null || scenePicturesAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (scenePicturesAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            scenePicturesAttach.setFileName(scenePicturesAttach.getName() + "." + scenePicturesAttach.getSuffix());
            scenePicturesAttach.setAftersale(aftersale);
            scenePicturesAttach.setStoreMember(storeMemberService.getCurrent());
        }
        aftersale.setScenePicturesAttachs(scenePicturesAttachs);
        update(aftersale, "sn", "invoiceStatus");
    }

    @Override
    public Page<Map<String, Object>> findPage(Object[] params, Pageable pageable) {
        return aftersaleDao.findPage(params, pageable);
    }

    // @Override
    // public List<Map<String, Object>> findAftersaleAttach(Long id) {
    // return aftersaleDao.findAftersaleAttach(id);
    // }
    @Override
    public List<Map<String, Object>> findSalesContractAttach(Long id) {
        return aftersaleDao.findSalesContractAttach(id);
    }

    @Override
    public List<Map<String, Object>> findAcceptanceSheetAttach(Long id) {
        return aftersaleDao.findAcceptanceSheetAttach(id);
    }

    @Override
    public List<Map<String, Object>> findScenePicturesAttach(Long id) {
        return aftersaleDao.findScenePicturesAttach(id);
    }

    @Override
    public List<Map<String, Object>> findCoupleBackAttach(Long id) {
        return aftersaleDao.findCoupleBackAttach(id);
    }

    @Override
    public List<Map<String, Object>> findSurveyorAttach(Long id) {
        return aftersaleDao.findSurveyorAttach(id);
    }

    @Override
    public List<Map<String, Object>> findAgreementAttach(Long id) {
        return aftersaleDao.findAgreementAttach(id);
    }

    @Override
    public List<Map<String, Object>> findQuittanceAttach(Long id) {
        return aftersaleDao.findQuittanceAttach(id);
    }

    @Override
    public List<Map<String, Object>> findReturnsAttach(Long id) {
        return aftersaleDao.findReturnsAttach(id);
    }

    public Map<String, Object> setFormData(ActWf wf, String taskId) {
        Map<String, Object> dayMap = new HashMap<String, Object>();
        Aftersale a = this.find(wf.getObjId());
        // dayMap.put("factory", a.getFactoryId());
        dayMap.put("amount", a.getAmount());
        dayMap.put("isDeal", a.getIsDeal());
        setFactory(dayMap, a);
        if(a.getFdCompensation()!=null&&a.getIsReturn()!=null){
            if(a.getIsReturn()==0){
                a.setIsReturn(1);
            }
        }
        if(a.getOrderManager()!=null){//设置订单经理
            dayMap.put("orderManagerUser", a.getOrderManager().getId());
        }
        dayMap.put("return", a.getIsReturn());
        //是否勘察分支条件
        dayMap.put("isLocaleSurveyor", a.getIsLocaleSurveyor());
        //勘察人
        if(a.getSurveyor()!=null){
            dayMap.put("surveyorUser", a.getSurveyor().getId());
        }

        return dayMap;
    }

    public void setFactory(Map<String, Object> dayMap, Aftersale a) {
        if (a.getFactoryId() != null) {
            List<StoreMember> storeMemberList = storeMemberSaleOrgPostService
                    .findPostCodeByStoreMember(a.getFactoryId(), "2088");
            if (storeMemberList != null) {
                Long userId = storeMemberList.get(0).getId();
                dayMap.put("factoryUser", userId);
            }
        }
    }

    public String splicer(String[] str) {
        String s = "";
        if (str != null && str.length > 0) {
            for (int i = 0; i < str.length; i++) {
                if (str.length - 1 == i) {
                    s += str[i];
                } else {
                    s += str[i] + ",";
                }
            }
        }
        return s;
    }

    @Override
    public void saveform(Aftersale aftersale, Integer Type, String[] fd, String[] cd, String[] storeTreatmentScheme) {
        Aftersale a = find(aftersale.getId());
        ActWf wf = getWfByWfId(a.getWfId());
        if(wf.getModelId().equals("1")) {//旧版售后流程表单
            saveformOfNormal(wf,aftersale,a,Type,fd);
        }else {//实木售后流程表单
            saveformOfShimu(wf,aftersale,a,Type,cd);
        }
    }

    /**
     * 实木表单节点校验
     *
     * @param aftersale
     *            实体
     * @param nodeName
     *            节点名称
     * @param isUpdate
     * 			  是否启用更新
     */
    private void wfPassOfShimu(Aftersale aftersale, String nodeName, boolean isUpdate) {
        Aftersale af = find(aftersale.getId());
        System.out.println("wfNodeName:"+nodeName);
        if (nodeName.equals("勘察")) {
            if (aftersale.getIsLocaleSurveyor() == null) {
                ExceptionUtil.throwServiceException("请选择是否勘察！");
            }
            if (aftersale.getIsLocaleSurveyor() == 0 && aftersale.getSurveyor().getId() == null) {
                ExceptionUtil.throwServiceException("请填写勘察人！");
            }
        }
        if (nodeName.contains("勘察人")) {
            if (aftersale.getIsLocaleSurveyor() == 0 && aftersale.getZbsurveyorDate() == null) {
                ExceptionUtil.throwServiceException("请填写勘察时间！");
            }
            if (aftersale.getIsLocaleSurveyor() == 0 && aftersale.getSurveyorAttachs().size() < 4) {
                ExceptionUtil.throwServiceException("请上传勘察图片且数量不能少于4！");
            }
            if (aftersale.getIsLocaleSurveyor() == 0 && aftersale.getSurveyDescription() == null) {
                ExceptionUtil.throwServiceException("请填写勘察说明");
            }
        }
        if (nodeName.contains("售后专员")) {
            //            if (aftersale.getIsEnabled() == null) {
            //                ExceptionUtil.throwServiceException("请选择申请是否有效！");
            //            }
            //            if (aftersale.getIsEnabled() == 1 && aftersale.getCauseInvalidity() == null) {
            //                ExceptionUtil.throwServiceException("请填写无效原因！");
            //            }
            if (aftersale.getFactoryId() == null) {
                ExceptionUtil.throwServiceException("请填写责任工厂！");
            }
            if (aftersale.getOrderManager() == null||aftersale.getOrderManager().getId() == null){
                ExceptionUtil.throwServiceException("请选择订单经理！");
            }
            if (aftersale.getDisposeScheme() == null) {
                ExceptionUtil.throwServiceException("请填写售后部调查信息！");
            }
            if (aftersale.getProcessingStatus() == null) {
                ExceptionUtil.throwServiceException("请选择处理状态！");
            }
            if (aftersale.getProductionTime() == null){
                ExceptionUtil.throwServiceException("请填写生产时间！");
            }
            aftersale.setReviewDate(new Date());
            aftersale.setNodeApprover(storeMemberService.getCurrent().getName());
        }
        if (nodeName.contains("售后经理")) {
            if (aftersale.getManagerOpinion() == null) {
                ExceptionUtil.throwServiceException("请填写售后经理意见！");
            }
        }
        if (nodeName.contains("工厂")) {
            if (aftersale.getIsDeal() == null) {
                ExceptionUtil.throwServiceException("请选择是否处理！");
            } else {
                if (aftersale.getIsDeal() == 1) {
                    if (aftersale.getIsReturn() == null) {
                        ExceptionUtil.throwServiceException("请填写是否退货！");
                    }
                    if (aftersale.getIsReturn() == 0 && aftersale.getFreightForwarder() == null) {
                        ExceptionUtil.throwServiceException("请填写运费承担方！");
                    }
                    if (aftersale.getFd() == null) {
                        ExceptionUtil.throwServiceException("请填写工厂处理方案！");
                    }
                    String fd = aftersale.getFd();
                    if(fd.contains("局部更换")&&aftersale.getFdPartialReplacement() == null){
                        ExceptionUtil.throwServiceException("请填写局部更换平方数！");
                    }
                    if(fd.contains("退货")&&aftersale.getFdReturnGoods() == null){
                        ExceptionUtil.throwServiceException("请填写退货平方数！");
                    }
                }
            }
            aftersale.setEndTime(new Date());
        }
        if (nodeName.contains("省长意见")) {
            if (aftersale.getSzOpinion() == null) {
                ExceptionUtil.throwServiceException("请填写省长意见！");
            }
        }
        if (nodeName.contains("服务副总监意见")) {
            if (aftersale.getServiceDeputyDirectorSuggestion() == null) {
                ExceptionUtil.throwServiceException("请填写服务副总监意见！");
            }
        }
        if (nodeName.contains("质量中心")) {
            if (aftersale.getHandlingSuggestion() == null) {
                ExceptionUtil.throwServiceException("请填写质量中心处理意见！");
            }
            if (aftersale.getLiabilityJudgments() == null) {
                ExceptionUtil.throwServiceException("请填写责任判断！");
            }
        }
        if (nodeName.contains("订单部核价")) {
            if (aftersale.getFactoryPrice() == null) {
                ExceptionUtil.throwServiceException("请填写出厂价！");
            }
            //            if (aftersale.getAmount() == null) {
            //                ExceptionUtil.throwServiceException("请填写合计费用！");
            //            }
            //            if (aftersale.getPlatformPrice() == null) {
            //                ExceptionUtil.throwServiceException("请填写平台价！");
            //            }
            //            if (aftersale.getPlatformAmount() == null) {
            //                ExceptionUtil.throwServiceException("请填写合计费用！");
            //            }
        }
        if (nodeName.contains("标准费用")) {
            if (aftersale.getIsDeal() == null) {
                ExceptionUtil.throwServiceException("请选择是否处理！");
            }
            if (aftersale.getIsReturn()==null) {
                ExceptionUtil.throwServiceException("请选择是否退货！");
            }
            if(aftersale.getCd()==null) {
                ExceptionUtil.throwServiceException("请选择公司处理方案！");
            }
            String cd = aftersale.getCd();
            if (cd.contains("局部更换")&&aftersale.getComPartialReplacement() == null) {
                ExceptionUtil.throwServiceException("请填写局部更换面积！");
            }
            if (cd.contains("退货")&&aftersale.getComReturnGoods() == null) {
                ExceptionUtil.throwServiceException("请填写退货面积！");
            }
            if (aftersale.getComCompensation() == null) {
                ExceptionUtil.throwServiceException("请填写赔偿用户金额！");
            }
            if (aftersale.getOtherCost() == null) {
                ExceptionUtil.throwServiceException("请填写其他费用！");
            }
            if (aftersale.getSubsidiaryMaterialCost() == null) {
                ExceptionUtil.throwServiceException("请填写辅料费！");
            }
            if (aftersale.getLabourCost() == null) {
                ExceptionUtil.throwServiceException("请填写人工费！");
            }
            if (aftersale.getFreight() == null) {
                ExceptionUtil.throwServiceException("请填写运费！");
            }
            if (aftersale.getStandardAmount() == null) {
                ExceptionUtil.throwServiceException("请填写相对费用！");
            }
            if (aftersale.getAmount() == null) {
                ExceptionUtil.throwServiceException("请填写合计费用！");
            }
        }
        if (nodeName.contains("经销商处理")) {
            if(af.getIsDeal()==1){
                String cd = af.getCd();
                String con = "";
                Boolean a = cd.contains("更换");
                Boolean b = cd.contains("赔偿");
                Boolean c = cd.contains("退货");
                Boolean d = (cd.contains("更换") && cd.contains("赔偿"));
                Boolean e = (cd.contains("退货") && cd.contains("赔偿"));
                List<CoupleBackAttach> a1 = aftersale.getCoupleBackAttachs();// 售后确认处理单
                List<AgreementAttach> a2 = aftersale.getAgreementAttachs();// 售后协议书
                List<QuittanceAttach> a3 = aftersale.getQuittanceAttachs();// 收据
                List<ReturnsAttach> a4 = aftersale.getReturnsAttachs();// 退货申请单
//				if (a) {
//					con = "请上传售后确认处理单";
//					if (a1 == null || a1.size() <= 0) {
//						ExceptionUtil.throwServiceException(con);
//					}
//				}
                if (b) {
                    con = "请上传售后协议书+收据";
                    if ((a2 == null || a2.size() <= 0) || (a3 == null || a3.size() <= 0)) {
                        ExceptionUtil.throwServiceException(con);
                    }
                }
//				if (c) {
//					con = "请上传售后处理确认单+退货申请单";
//					if ((a1 == null || a1.size() <= 0) || (a4 == null || a4.size() <= 0)) {
//						ExceptionUtil.throwServiceException(con);
//					}
//				}
                if (a||d) {
                    con = "请上传售后处理确认单+售后协议书+收据";
                    if ((a1 == null || a1.size() <= 0) || (a2 == null || a2.size() <= 0)
                            || (a3 == null || a3.size() <= 0)) {
                        ExceptionUtil.throwServiceException(con);
                    }
                }
                if (c||e) {
                    con = "请上传售后处理确认单+售后协议书+收据+退货申请单";
                    if ((a1 == null || a1.size() <= 0) || (a2 == null || a2.size() <= 0) || (a3 == null || a3.size() <= 0)
                            || (a4 == null || a4.size() <= 0)) {
                        ExceptionUtil.throwServiceException(con);
                    }
                }
            }
        }
        if (nodeName.contains("回访")) {
            if (aftersale.getReviewResult() == null) {
                ExceptionUtil.throwServiceException("回访结果不能为空");
            }
            if (aftersale.getSatisfaction() == null) {
                ExceptionUtil.throwServiceException("处理满意度不能为空");
            }
            if (aftersale.getReviewStatus() == null) {
                ExceptionUtil.throwServiceException("请选择回访状态！");
            }
            if(af.getIsDeal() == 1){
                if (af.getIsReturn() == 1){//不退货
                    if(aftersale.getDepositRechargeAmount()==null){
                        ExceptionUtil.throwServiceException("请填写冲账金额！");
                    }
                    if(aftersale.getDepositRechargeMemo()==null){
                        ExceptionUtil.throwServiceException("请填写冲账备注！");
                    }
                }
            }
            if (aftersale.getOrganization() == null||aftersale.getOrganization().getId()==null) {
                ExceptionUtil.throwServiceException("请选择经营组织");
            }
            if (aftersale.getFinancialStaff() == null||aftersale.getFinancialStaff().getId()==null) {
                ExceptionUtil.throwServiceException("请选择工厂财务");
            }
        }
        if(isUpdate){
            update(aftersale);
        }
    }


    private void saveformOfShimu(ActWf wf, Aftersale aftersale, Aftersale a, Integer Type, String[] cds) {
        if(getCurrTaskByWf(wf)!=null){
            // 通过节点校验
            wfPassOfShimu(aftersale, getCurrTaskByWf(wf).getName(),false);
        }

        // 勘察
        if (Type == 0) {
            a.setIsLocaleSurveyor(aftersale.getIsLocaleSurveyor());
            if(aftersale.getSurveyor() !=null && aftersale.getSurveyor().getId() != null){
                a.setSurveyor(aftersale.getSurveyor());
            }
            if(a.getIsLocaleSurveyor() == 0){
                a.setProcessingStatus("待勘察");
            }
        }
        // 勘察人
        if (Type == 7) {
            a.setSurveyorName(aftersale.getSurveyorName());
            a.setZbsurveyorDate(aftersale.getZbsurveyorDate());
            a.setSurveyDescription(TextareaLineBreakUtil.lineEnter(aftersale.getSurveyDescription()));
            // 勘察图片附件
            List<SurveyorAttach> surveyorAttachs = aftersale.getSurveyorAttachs();
            for (Iterator<SurveyorAttach> iterator = surveyorAttachs.iterator(); iterator.hasNext();) {
                SurveyorAttach surveyorAttach = iterator.next();
                if (surveyorAttach == null || surveyorAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (surveyorAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                surveyorAttach.setFileName(surveyorAttach.getName() + "." + surveyorAttach.getSuffix());
                surveyorAttach.setAftersale(aftersale);
                surveyorAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getSurveyorAttachs().clear();
            a.getSurveyorAttachs().addAll(surveyorAttachs);
        }
        // 公司处理信息
        if (Type == 1) {
            a.setDisposeScheme(TextareaLineBreakUtil.lineEnter(aftersale.getDisposeScheme()));
            a.setIsEnabled(aftersale.getIsEnabled());
            a.setCauseInvalidity(aftersale.getCauseInvalidity());
            a.setProcessingStatus(aftersale.getProcessingStatus());
            a.setFactoryId(aftersale.getFactoryId());
            a.setFactoryName(aftersale.getFactoryName());
            a.setCauseInvalidity(aftersale.getCauseInvalidity());
            a.setFormType(aftersale.getFormType());
            a.setFaultType(aftersale.getFaultType());
            a.setProductionTime(aftersale.getProductionTime());
            a.setOrderManager(aftersale.getOrderManager());
            if(aftersale.getFactoryId()!=null&&!"吴江居然家".equals(aftersale.getFactoryName())) {
                System.out.println("工厂id:"+aftersale.getFactoryId());
                List<StoreMember> financalStaffs = storeMemberSaleOrgPostService.findPostCodeByStoreMember(aftersale.getFactoryId(), "3001");
                if(financalStaffs!=null&&!financalStaffs.isEmpty()) {
                    a.setFinancialStaff(financalStaffs.get(0));
                    System.out.println(a.getFinancialStaff().getName());
                }
            }
        }
        // 售后经理意见
        if (Type == 8) {
            if (aftersale.getManagerOpinion() == null) {
                ExceptionUtil.throwServiceException("请填写售后经理意见！");
            }
            a.setManagerOpinion(aftersale.getManagerOpinion());
        }
        // 工厂处理
        //        if (Type == 2) {
        //            if (aftersale.getIsDeal() == null) {
        //                ExceptionUtil.throwServiceException("请选择是否处理！");
        //            } else {
        //                if (aftersale.getIsDeal() == 1) {
        //                    if (aftersale.getIsReturn() == null) {
        //                        ExceptionUtil.throwServiceException("请填写是否退货！");
        //                    }
        //                    if (aftersale.getIsReturn() == 0 && aftersale.getFreightForwarder() == null) {
        //                        ExceptionUtil.throwServiceException("请填写运费承担方！");
        //                    }
        //                    if (aftersale.getFd() == null) {
        //                        ExceptionUtil.throwServiceException("请填写工厂处理方案！");
        //                    }
        //                    a.setIsReturn(aftersale.getIsReturn());
        //                    a.setFactoryAnswerDate(new Date());
        //                    if (aftersale.getIsReturn() == 0) {
        //                        a.setReturnStatus("0");
        //                    }
        //                    a.setFreightForwarder(aftersale.getFreightForwarder());
        //                    a.setFd(splicer(fd));
        //                    a.setOpinion(aftersale.getOpinion());
        //                    a.setFdPartialReplacement(aftersale.getFdPartialReplacement());
        //                    a.setFdReturnGoods(aftersale.getFdReturnGoods());
        //                    a.setFdCompensation(aftersale.getFdCompensation());
        //                    a.setFactoryCompensation(aftersale.getFactoryCompensation());
        //                    a.setProcessingStatus("已处理");
        //                } else {
        //                    a.setObjection(aftersale.getObjection());
        //                    a.setProcessingStatus("不处理");
        //                }
        //                a.setIsDeal(aftersale.getIsDeal());
        //            }
        //
        //        }
        // 质量中心处理意见
        if (Type == 3) {
            if (aftersale.getHandlingSuggestion() == null) {
                ExceptionUtil.throwServiceException("请填写质量中心处理意见！");
            }
            if (aftersale.getLiabilityJudgments() == null) {
                ExceptionUtil.throwServiceException("请填写责任判断！");
            }
            a.setHandlingSuggestion(aftersale.getHandlingSuggestion());
            a.setLiabilityJudgments(aftersale.getLiabilityJudgments());
        }
        // 订单部核价
        if (Type == 4) {
            if (aftersale.getFactoryPrice() == null) {
                ExceptionUtil.throwServiceException("请填写分销价！");
            }
            //            if (aftersale.getAmount() == null) {
            //                ExceptionUtil.throwServiceException("请填写合计费用！");
            //            }
            //            if (aftersale.getPlatformPrice() == null) {
            //                ExceptionUtil.throwServiceException("请填写平台价！");
            //            }
            //            if (aftersale.getPlatformAmount() == null) {
            //                ExceptionUtil.throwServiceException("请填写合计费用！");
            //            }
            a.setFactoryPrice(aftersale.getFactoryPrice());
            //            a.setAmount(aftersale.getAmount());
            //            a.setPlatformPrice(aftersale.getPlatformPrice());
            //            a.setPlatformAmount(aftersale.getPlatformAmount());
        }
        // 经销商处理信息及附件
        if (Type == 5&&a.getIsDeal()==1) {

            // 售后处理确认单附件aftersale.getCoupleBackAttachs()
            // 售后协议书aftersale.getAgreementAttachs();
            // 收据aftersale.getQuittanceAttachs();
            // 退货申请单 aftersale.getReturnsAttachs();
            List<CoupleBackAttach> coupleBackAttachs = aftersale.getCoupleBackAttachs();
            for (Iterator<CoupleBackAttach> iterator = coupleBackAttachs.iterator(); iterator.hasNext();) {
                CoupleBackAttach coupleBackAttach = iterator.next();
                if (coupleBackAttach == null || coupleBackAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (coupleBackAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                coupleBackAttach.setFileName(coupleBackAttach.getName() + "." + coupleBackAttach.getSuffix());
                coupleBackAttach.setAftersale(aftersale);
                coupleBackAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getCoupleBackAttachs().clear();
            a.getCoupleBackAttachs().addAll(coupleBackAttachs);
            // 售后协议书aftersale.getAgreementAttachs();
            List<AgreementAttach> agreementAttachs = aftersale.getAgreementAttachs();
            for (Iterator<AgreementAttach> iterator = agreementAttachs.iterator(); iterator.hasNext();) {
                AgreementAttach agreementAttach = iterator.next();
                if (agreementAttach == null || agreementAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (agreementAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                agreementAttach.setFileName(agreementAttach.getName() + "." + agreementAttach.getSuffix());
                agreementAttach.setAftersale(aftersale);
                agreementAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getAgreementAttachs().clear();
            a.getAgreementAttachs().addAll(agreementAttachs);
            // 收据aftersale.getQuittanceAttachs();
            List<QuittanceAttach> quittanceAttachs = aftersale.getQuittanceAttachs();
            for (Iterator<QuittanceAttach> iterator = quittanceAttachs.iterator(); iterator.hasNext();) {
                QuittanceAttach quittanceAttach = iterator.next();
                if (quittanceAttach == null || quittanceAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (quittanceAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                quittanceAttach.setFileName(quittanceAttach.getName() + "." + quittanceAttach.getSuffix());
                quittanceAttach.setAftersale(aftersale);
                quittanceAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getQuittanceAttachs().clear();
            a.getQuittanceAttachs().addAll(quittanceAttachs);
            // 退货申请单 aftersale.getReturnsAttachs();
            List<ReturnsAttach> returnsAttachs = aftersale.getReturnsAttachs();
            for (Iterator<ReturnsAttach> iterator = returnsAttachs.iterator(); iterator.hasNext();) {
                ReturnsAttach returnsAttach = iterator.next();
                if (returnsAttach == null || returnsAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (returnsAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                returnsAttach.setFileName(returnsAttach.getName() + "." + returnsAttach.getSuffix());
                returnsAttach.setAftersale(aftersale);
                returnsAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getReturnsAttachs().clear();
            a.getReturnsAttachs().addAll(returnsAttachs);
        }
        // 售后回访
        if (Type == 6) {
            if (aftersale.getReviewResult() == null) {
                ExceptionUtil.throwServiceException("回访结果不能为空");
            }
            if (aftersale.getSatisfaction() == null) {
                ExceptionUtil.throwServiceException("处理满意度不能为空");
            }
            if (aftersale.getReviewStatus() == null) {
                ExceptionUtil.throwServiceException("请选择回访状态");
            }
            a.setReviewStatus(aftersale.getReviewStatus());
            a.setReviewResult(aftersale.getReviewResult());
            a.setSatisfaction(aftersale.getSatisfaction());
            a.setDepositRechargeAmount(aftersale.getDepositRechargeAmount());
            a.setDepositRechargeMemo(aftersale.getDepositRechargeMemo());
            a.setOrganization(aftersale.getOrganization());
            a.setFinancialStaff(aftersale.getFinancialStaff());
        }
        // 售后专员填写标准费用
        if (Type == 9) {
            if (aftersale.getIsDeal() == null) {
                ExceptionUtil.throwServiceException("请选择是否处理！");
            }
            if (aftersale.getIsReturn()==null) {
                ExceptionUtil.throwServiceException("请选择是否退货！");
            }
            String cd = splicer(cds);
            if (cds.length<=0) {
                ExceptionUtil.throwServiceException("请选择公司处理方案！");
            }
            if (cd.contains("局部更换")&&aftersale.getComPartialReplacement() == null) {
                ExceptionUtil.throwServiceException("请填写局部更换面积！");
            }
            if (cd.contains("退货")&&aftersale.getComReturnGoods() == null) {
                ExceptionUtil.throwServiceException("请填写退货面积！");
            }
            if (aftersale.getComCompensation() == null) {
                ExceptionUtil.throwServiceException("请填写赔偿用户金额！");
            }
            if (aftersale.getOtherCost() == null) {
                ExceptionUtil.throwServiceException("请填写其他费用！");
            }
            if (aftersale.getSubsidiaryMaterialCost() == null) {
                ExceptionUtil.throwServiceException("请填写辅料费！");
            }
            if (aftersale.getLabourCost() == null) {
                ExceptionUtil.throwServiceException("请填写人工费！");
            }
            if (aftersale.getFreight() == null) {
                ExceptionUtil.throwServiceException("请填写运费！");
            }
            if (aftersale.getStandardAmount() == null) {
                ExceptionUtil.throwServiceException("请填写相对费用！");
            }
            if (aftersale.getAmount() == null) {
                ExceptionUtil.throwServiceException("请填写合计费用！");
            }
            a.setIsDeal(aftersale.getIsDeal());
            a.setIsReturn(aftersale.getIsReturn());
            a.setCd(cd);
            a.setComPartialReplacement(aftersale.getComPartialReplacement());
            a.setComReturnGoods(aftersale.getComReturnGoods());
            a.setComCompensation(aftersale.getComCompensation());
            a.setOtherCost(aftersale.getOtherCost());
            a.setCompanyCompensation(aftersale.getCompanyCompensation());
            a.setStandardAmount(aftersale.getStandardAmount());
            a.setAmount(aftersale.getAmount());
            a.setSubsidiaryMaterialCost(aftersale.getSubsidiaryMaterialCost());
            a.setLabourCost(aftersale.getLabourCost());
            a.setFreight(aftersale.getFreight());
            a.setProcessingStatus(a.getIsDeal()==1?"已处理":"不处理");
        }
        // 省长意见
        if (Type == 10) {
            if (aftersale.getSzOpinion() == null) {
                ExceptionUtil.throwServiceException("请填写省长意见！");
            }
            a.setSzOpinion(aftersale.getSzOpinion());
        }
        // 服务副总监意见
        if (Type == 11) {
            if (aftersale.getServiceDeputyDirectorSuggestion() == null) {
                ExceptionUtil.throwServiceException("请填写服务副总监意见！");
            }
            a.setServiceDeputyDirectorSuggestion(aftersale.getServiceDeputyDirectorSuggestion());
        }
        update(a);
    }

    private void saveformOfNormal(ActWf wf,Aftersale aftersale,Aftersale a,Integer Type,String[] fd) {
        if(getCurrTaskByWf(wf)!=null){
            // 通过节点校验
            wfPass(aftersale, getCurrTaskByWf(wf).getName(),false);
        }

        // 勘察
        if (Type == 0) {
            a.setIsLocaleSurveyor(aftersale.getIsLocaleSurveyor());
            if(aftersale.getSurveyor() !=null && aftersale.getSurveyor().getId() != null){
                a.setSurveyor(aftersale.getSurveyor());
            }
            if(a.getIsLocaleSurveyor() == 0){
                a.setProcessingStatus("待勘察");
            }

            //20201008售后专员与勘察合并
            a.setDisposeScheme(TextareaLineBreakUtil.lineEnter(aftersale.getDisposeScheme()));
            a.setIsEnabled(aftersale.getIsEnabled());
            a.setCauseInvalidity(aftersale.getCauseInvalidity());
            a.setProcessingStatus(aftersale.getProcessingStatus());
            a.setFactoryId(aftersale.getFactoryId());
            a.setFactoryName(aftersale.getFactoryName());
            a.setCauseInvalidity(aftersale.getCauseInvalidity());
            a.setFormType(aftersale.getFormType());
            a.setFaultType(aftersale.getFaultType());
            a.setProductionTime(aftersale.getProductionTime());
            a.setOrderManager(aftersale.getOrderManager());

        }
        // 勘察人
        if (Type == 7) {
            a.setSurveyorName(aftersale.getSurveyorName());
            a.setZbsurveyorDate(aftersale.getZbsurveyorDate());
            a.setSurveyDescription(TextareaLineBreakUtil.lineEnter(aftersale.getSurveyDescription()));
            // 勘察图片附件
            List<SurveyorAttach> surveyorAttachs = aftersale.getSurveyorAttachs();
            for (Iterator<SurveyorAttach> iterator = surveyorAttachs.iterator(); iterator.hasNext();) {
                SurveyorAttach surveyorAttach = iterator.next();
                if (surveyorAttach == null || surveyorAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (surveyorAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                surveyorAttach.setFileName(surveyorAttach.getName() + "." + surveyorAttach.getSuffix());
                surveyorAttach.setAftersale(aftersale);
                surveyorAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getSurveyorAttachs().clear();
            a.getSurveyorAttachs().addAll(surveyorAttachs);
        }
        // 公司处理信息
        if (Type == 1) {
            a.setDisposeScheme(TextareaLineBreakUtil.lineEnter(aftersale.getDisposeScheme()));
            a.setIsEnabled(aftersale.getIsEnabled());
            a.setCauseInvalidity(aftersale.getCauseInvalidity());
            a.setProcessingStatus(aftersale.getProcessingStatus());
            a.setFactoryId(aftersale.getFactoryId());
            a.setFactoryName(aftersale.getFactoryName());
            a.setCauseInvalidity(aftersale.getCauseInvalidity());
            a.setFormType(aftersale.getFormType());
            a.setFaultType(aftersale.getFaultType());
            a.setProductionTime(aftersale.getProductionTime());
            a.setOrderManager(aftersale.getOrderManager());

            List<String> sbuName = Arrays.asList("实复多层","实复三层");
            if(sbuName.contains(a.getSbu().getName())&&aftersale.getFactoryId()!=null&&!"吴江居然家".equals(aftersale.getFactoryName())) {
                System.out.println("工厂id:"+aftersale.getFactoryId());
                List<StoreMember> financalStaffs = storeMemberSaleOrgPostService.findPostCodeByStoreMember(aftersale.getFactoryId(), "3001");
                if(financalStaffs!=null&&!financalStaffs.isEmpty()) {
                    a.setFinancialStaff(financalStaffs.get(0));
                    System.out.println(a.getFinancialStaff().getName());
                }
            }
        }
        // 售后经理意见
        if (Type == 8) {
            if (aftersale.getManagerOpinion() == null) {
                ExceptionUtil.throwServiceException("请填写售后经理意见！");
            }
            a.setManagerOpinion(aftersale.getManagerOpinion());
        }
        // 工厂处理
        if (Type == 2) {
            if (aftersale.getIsDeal() == null) {
                ExceptionUtil.throwServiceException("请选择是否处理！");
            } else {
                if (aftersale.getIsDeal() == 1) {
                    if (aftersale.getIsReturn() == null) {
                        ExceptionUtil.throwServiceException("请填写是否退货！");
                    }
                    if (aftersale.getIsReturn() == 0 && aftersale.getFreightForwarder() == null) {
                        ExceptionUtil.throwServiceException("请填写运费承担方！");
                    }
                    if (aftersale.getFd() == null) {
                        ExceptionUtil.throwServiceException("请填写工厂处理方案！");
                    }
                    a.setIsReturn(aftersale.getIsReturn());
                    a.setFactoryAnswerDate(new Date());
                    if (aftersale.getIsReturn() == 0) {
                        a.setReturnStatus("0");
                    }
                    a.setFreightForwarder(aftersale.getFreightForwarder());
                    a.setFd(splicer(fd));
                    a.setOpinion(aftersale.getOpinion());
                    a.setFdPartialReplacement(aftersale.getFdPartialReplacement());
                    a.setFdReturnGoods(aftersale.getFdReturnGoods());
                    a.setFdCompensation(aftersale.getFdCompensation());
                    a.setFactoryCompensation(aftersale.getFactoryCompensation());
                    a.setProcessingStatus("已处理");
                } else {
                    a.setObjection(aftersale.getObjection());
                    a.setProcessingStatus("不处理");
                }
                a.setIsDeal(aftersale.getIsDeal());
            }

        }
        // 质量中心处理意见
        if (Type == 3) {
            if (aftersale.getHandlingSuggestion() == null) {
                ExceptionUtil.throwServiceException("请填写质量中心处理意见！");
            }
            if (aftersale.getLiabilityJudgments() == null) {
                ExceptionUtil.throwServiceException("请填写责任判断！");
            }
            a.setHandlingSuggestion(aftersale.getHandlingSuggestion());
            a.setLiabilityJudgments(aftersale.getLiabilityJudgments());
        }
        // 订单部核价
        if (Type == 4) {
            if (aftersale.getFactoryPrice() == null) {
                ExceptionUtil.throwServiceException("请填写分销价！");
            }
            if (aftersale.getAmount() == null) {
                ExceptionUtil.throwServiceException("请填写合计费用！");
            }
//			if (aftersale.getPlatformPrice() == null) {
//				ExceptionUtil.throwServiceException("请填写平台价！");
//			}
//			if (aftersale.getPlatformAmount() == null) {
//				ExceptionUtil.throwServiceException("请填写合计费用！");
//			}
            a.setFactoryPrice(aftersale.getFactoryPrice());
            a.setAmount(aftersale.getAmount());
            a.setPlatformPrice(aftersale.getPlatformPrice());
            a.setPlatformAmount(aftersale.getPlatformAmount());
        }
        // 经销商处理信息及附件
        if (Type == 5&&a.getIsDeal()==1) {

            // 售后处理确认单附件aftersale.getCoupleBackAttachs()
            // 售后协议书aftersale.getAgreementAttachs();
            // 收据aftersale.getQuittanceAttachs();
            // 退货申请单 aftersale.getReturnsAttachs();
            List<CoupleBackAttach> coupleBackAttachs = aftersale.getCoupleBackAttachs();
            for (Iterator<CoupleBackAttach> iterator = coupleBackAttachs.iterator(); iterator.hasNext();) {
                CoupleBackAttach coupleBackAttach = iterator.next();
                if (coupleBackAttach == null || coupleBackAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (coupleBackAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                coupleBackAttach.setFileName(coupleBackAttach.getName() + "." + coupleBackAttach.getSuffix());
                coupleBackAttach.setAftersale(aftersale);
                coupleBackAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getCoupleBackAttachs().clear();
            a.getCoupleBackAttachs().addAll(coupleBackAttachs);
            // 售后协议书aftersale.getAgreementAttachs();
            List<AgreementAttach> agreementAttachs = aftersale.getAgreementAttachs();
            for (Iterator<AgreementAttach> iterator = agreementAttachs.iterator(); iterator.hasNext();) {
                AgreementAttach agreementAttach = iterator.next();
                if (agreementAttach == null || agreementAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (agreementAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                agreementAttach.setFileName(agreementAttach.getName() + "." + agreementAttach.getSuffix());
                agreementAttach.setAftersale(aftersale);
                agreementAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getAgreementAttachs().clear();
            a.getAgreementAttachs().addAll(agreementAttachs);
            // 收据aftersale.getQuittanceAttachs();
            List<QuittanceAttach> quittanceAttachs = aftersale.getQuittanceAttachs();
            for (Iterator<QuittanceAttach> iterator = quittanceAttachs.iterator(); iterator.hasNext();) {
                QuittanceAttach quittanceAttach = iterator.next();
                if (quittanceAttach == null || quittanceAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (quittanceAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                quittanceAttach.setFileName(quittanceAttach.getName() + "." + quittanceAttach.getSuffix());
                quittanceAttach.setAftersale(aftersale);
                quittanceAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getQuittanceAttachs().clear();
            a.getQuittanceAttachs().addAll(quittanceAttachs);
            // 退货申请单 aftersale.getReturnsAttachs();
            List<ReturnsAttach> returnsAttachs = aftersale.getReturnsAttachs();
            for (Iterator<ReturnsAttach> iterator = returnsAttachs.iterator(); iterator.hasNext();) {
                ReturnsAttach returnsAttach = iterator.next();
                if (returnsAttach == null || returnsAttach.getUrl() == null) {
                    iterator.remove();
                    continue;
                }
                if (returnsAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                returnsAttach.setFileName(returnsAttach.getName() + "." + returnsAttach.getSuffix());
                returnsAttach.setAftersale(aftersale);
                returnsAttach.setStoreMember(storeMemberService.getCurrent());
            }
            a.getReturnsAttachs().clear();
            a.getReturnsAttachs().addAll(returnsAttachs);
        }
        // 售后回访
        if (Type == 6) {
            if (aftersale.getReviewResult() == null) {
                ExceptionUtil.throwServiceException("回访结果不能为空");
            }
            if (aftersale.getSatisfaction() == null) {
                ExceptionUtil.throwServiceException("处理满意度不能为空");
            }
            if (aftersale.getReviewStatus() == null) {
                ExceptionUtil.throwServiceException("请选择回访状态");
            }
            a.setReviewStatus(aftersale.getReviewStatus());
            a.setReviewResult(aftersale.getReviewResult());
            a.setSatisfaction(aftersale.getSatisfaction());
            a.setDepositRechargeAmount(aftersale.getDepositRechargeAmount());
            a.setDepositRechargeMemo(aftersale.getDepositRechargeMemo());

            a.setOrganization(aftersale.getOrganization());
            a.setFinancialStaff(aftersale.getFinancialStaff());
        }
        update(a);

    }

    @Override
    public Page<Map<String, Object>> findStoreMemberPage(Pageable pageable) {
        return aftersaleDao.findPage1(pageable);
    }

    @Override
    public Page<Map<String, Object>> findPageAftersale(Object[] args, Pageable pageable) {

        return aftersaleDao.findPageAftersale(args, pageable);
    }

    @Override
    public Page<Map<String, Object>> findOrganizationPage(Pageable pageable) {
        return aftersaleDao.findOrganizationPage(pageable);
    }

    @Override
    public void b2bReturnsUpdeatAftersale(Aftersale aftersale, String status) {
        if (aftersale != null) {
            Aftersale a = find(aftersale.getId());
            a.setReturnStatus(status);
            update(a);
        }
    }

    @Override
    public void createWf(Long id, String modelId, Long objTypeId) {
        Aftersale a = find(id);
        if (a.getWfId() != null) {
            ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
        }
        StoreMember storeMember = storeMemberService.getCurrent();

        createWf(a.getSn(), String.valueOf(storeMember.getId()), new Long[] { a.getSaleOrg().getId() },
                a.getStore().getId(), modelId, objTypeId, id, WebUtils.getCurrentCompanyInfoId(), true);
        a.setInvoiceStatus("1");

        update(a);
    }

    /** 流程开始回调 */
    @Transactional
    public void startBack(ActWf wf) {
        List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
        for (String receiver : user) {
            oaToWfService.receiveRequestInfoByJson(wf,
                    new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
        }
    };

    /** 通过节点任务回调 发送待办 */
    @Transactional
    public void agreeBack(ActWf wf) {
        if (getCurrTaskByWf(wf) != null) {
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务回调 发送待办 */
    @Transactional
    public void rejectBack(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 通过节点任务前回调 发送待办转以办 */
    @Transactional
    public void agreePre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            // 通过节点校验
            wfPass(this.find(wf.getObjId()), getCurrTaskByWf(wf).getName(),true);
            // oa触发OA接口推送待办转以办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务前回调 发送待办转以办 */
    @Transactional
    public void rejectPre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 流程结束回调 */
    @Override
    @Transactional
    public void endBack(ActWf wf) {
        super.endBack(wf);
        Aftersale aftersale = find(wf.getObjId());
        aftersale.setInvoiceStatus("2");// 效状态
        update(aftersale);
        oaToWfService.processOverRequestByJson(wf,
                new String[] { "", oa_WfModelDao.findWfStartUser(wf.getProcInstId()) });
        if(aftersale.getIsDeal() == 1){
            createPolicyBill(aftersale);
        }
    }

    /** 流程终中断回调 0 */
    public void interruptBack(ActWf wf) {
        Aftersale aftersale = find(wf.getObjId());
        aftersale.setInvoiceStatus("0");
        update(aftersale);
    };

    /** 流程中断前回调 1 */
    @Transactional
    public void interruptPre(ActWf wf) {
        oaToWfService.deleteRequestInfoByJson(wf);
    };

    public String findUserName(String id) {
        StoreMember storeMember = storeMemberService.find(Long.parseLong(id));
        if (storeMember == null) {
            return "";
        } else {
            return storeMember.getUsername();
        }
    }

    /**
     * 售后流程结束创建政策单
     *
     */
    public void createPolicyBill(Aftersale aftersale){
       /* boolean a = aftersale.getFdCompensation() != null && aftersale.getIsReturn()==0;
        boolean b = aftersale.getIsReturn()==1;
        if(ab){*/
            List<Filter> filters = new ArrayList<Filter>();
            DepositRecharge dr = new DepositRecharge();
            dr.setAftersale(aftersale);
            filters.clear();
            filters.add(Filter.eq("code", "policyType"));
            filters.add(Filter.isNotNull("parent"));
            filters.add(Filter.eq("isEnabled", true));
            filters.add(Filter.eq("value", "208.促销返利SH"));
            List<SystemDict> policyTypes = systemDictService.findList(null, filters, null);
            if(policyTypes.size()<0){
                ExceptionUtil.throwServiceException("政策类型没有：208.促销返利SH此项 ！");
            }
            dr.setPolicyType(policyTypes.get(0));//政策类型
            filters.clear();
            filters.add(Filter.eq("code", "invoiceType"));
            filters.add(Filter.isNotNull("parent"));
            filters.add(Filter.eq("isEnabled", true));
            filters.add(Filter.eq("value", "208.经销商红字"));
            List<SystemDict> invoiceTypes = systemDictService.findList(null, filters, null);
            if(invoiceTypes.size()<0){
                ExceptionUtil.throwServiceException("发票类型没有：208.经销商红字此项 ！");
            }
            dr.setInvoiceType(invoiceTypes.get(0));//发票类型
            dr.setHasInvoice(0);//是否有发票  0无发票  1有发票
            filters.clear();
            filters.add(Filter.eq("name", "大自然家居（中国）有限公司"));
            filters.add(Filter.eq("isEnabled", true));
            filters.add(Filter.eq("type", 0));
            filters.add(Filter.eq("companyInfoId", 9L));
            Organization organization = organizationService.find(filters);
            dr.setOrganization(organization);//经营组织
            dr.setApplyDate(new Date());//申请日期
            dr.setGlDate(new Date());//GL日期
            dr.setSaleOrg(aftersale.getSaleOrg());//机构
            //dr.setBalanceMonth("2020-05");//对账月份
            dr.setMemo(aftersale.getDepositRechargeMemo());//申请备注
            dr.setTaxRate(null);//税率
            dr.setAmount(aftersale.getDepositRechargeAmount() == null ? BigDecimal.ZERO : aftersale.getDepositRechargeAmount() );//政策金额
            dr.setSbu(aftersale.getSbu());//sbu
            dr.setSourceSn(aftersale.getSn());
            dr.setSourceType(2);
            dr.setCompanyInfoId(9L);
            policyEntryService.save(aftersale.getStore(), dr, 5, 203L,0,invoiceTypes.get(0).getId());
        /*}*/
    }

    /**
     * 容大售后创建政策单
     *
     */
    public void createRdPolicyBill(Aftersale aftersale){
       /* boolean a = aftersale.getFdCompensation() != null && aftersale.getIsReturn()==0;
        boolean b = aftersale.getIsReturn()==1;
        if(ab){*/
        List<Filter> filters = new ArrayList<Filter>();
        DepositRecharge dr = new DepositRecharge();
        dr.setAftersale(aftersale);
        filters.clear();
        filters.add(Filter.eq("code", "policyType"));
        filters.add(Filter.isNotNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.eq("value", "售后赔付"));
        List<SystemDict> policyTypes = systemDictService.findList(null, filters, null);
        if(policyTypes.size()<0){
            ExceptionUtil.throwServiceException("政策类型没有：售后赔付此项 ！");
        }
        dr.setPolicyType(policyTypes.get(0));//政策类型
        filters.clear();
        filters.add(Filter.eq("code", "invoiceType"));
        filters.add(Filter.isNotNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.eq("value", "红字"));
        List<SystemDict> invoiceTypes = systemDictService.findList(null, filters, null);
        if(invoiceTypes.size()<0){
            ExceptionUtil.throwServiceException("发票类型没有：红字此项 ！");
        }
        dr.setInvoiceType(invoiceTypes.get(0));//发票类型
        dr.setHasInvoice(0);//是否有发票  0无发票  1有发票
        filters.clear();
        filters.add(Filter.eq("name", "大自然家居（中国）有限公司"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.eq("type", 0));
        filters.add(Filter.eq("companyInfoId", 9L));
        Organization organization = organizationService.find(filters);
        dr.setOrganization(organization);//经营组织
        dr.setApplyDate(new Date());//申请日期
        dr.setGlDate(new Date());//GL日期
        dr.setSaleOrg(aftersale.getSaleOrg());//机构
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        String balanceMonth = format.format(new Date());
        dr.setBalanceMonth(balanceMonth);//对账月份
        dr.setMemo(aftersale.getDepositRechargeMemo());//申请备注
        dr.setTaxRate(new BigDecimal("13"));//税率
        dr.setAmount(aftersale.getDepositRechargeAmount() == null ? BigDecimal.ZERO : aftersale.getDepositRechargeAmount() );//政策金额
        dr.setSbu(aftersale.getSbu());//sbu
        dr.setSourceSn(aftersale.getSn());
        dr.setSourceType(2);
        dr.setCompanyInfoId(9L);
        policyEntryService.save(aftersale.getStore(), dr, 5, 203L,0,invoiceTypes.get(0).getId());
        /*}*/
    }

    @Override
    public Integer count(Object[] params) {
        return aftersaleDao.count(params);
    }

    @Override
    public List<Map<String, Object>> findList(Object[] params, Pageable pageable, Integer page, Integer size) {
        return aftersaleDao.findList(params, pageable, page, size);
    }

    @Override
    public List<Map<String, Object>> findHistorySftersale(Aftersale aftersale) {
        return aftersaleDao.findHistoryAftersale(aftersale.getId(),aftersale.getContactNumber(), aftersale.getHeadNewArea().getId(), aftersale.getLayingAddress());
    }

    /**
     * 表单节点校验
     *
     * @param aftersale
     *            实体
     * @param nodeName
     *            节点名称
     * @param isUpdate
     * 			  是否启用更新
     */
    public void wfPass(Aftersale aftersale, String nodeName, Boolean isUpdate) {
        Aftersale af = find(aftersale.getId());
        if (nodeName.contains("勘察")) {
            if (aftersale.getIsLocaleSurveyor() == null) {
                ExceptionUtil.throwServiceException("请选择是否勘察！");
            }
            if (aftersale.getIsLocaleSurveyor() == 0 && aftersale.getSurveyor().getId() == null) {
                ExceptionUtil.throwServiceException("请填写勘察人！");
            }
        }
        if (nodeName.contains("勘察人")) {
            if (aftersale.getIsLocaleSurveyor() == 0 && aftersale.getZbsurveyorDate() == null) {
                ExceptionUtil.throwServiceException("请填写勘察时间！");
            }
            if (aftersale.getIsLocaleSurveyor() == 0 && aftersale.getSurveyorAttachs().size() < 4) {
                ExceptionUtil.throwServiceException("请上传勘察图片且数量不能少于4！");
            }
            if (aftersale.getIsLocaleSurveyor() == 0 && aftersale.getSurveyDescription() == null) {
                ExceptionUtil.throwServiceException("请填写勘察说明");
            }
        }
        if (nodeName.contains("售后专员")) {
//            if (aftersale.getIsEnabled() == null) {
//                ExceptionUtil.throwServiceException("请选择申请是否有效！");
//            }
//            if (aftersale.getIsEnabled() == 1 && aftersale.getCauseInvalidity() == null) {
//                ExceptionUtil.throwServiceException("请填写无效原因！");
//            }
            if (aftersale.getFactoryId() == null) {
                ExceptionUtil.throwServiceException("请填写责任工厂！");
            }
            if (aftersale.getOrderManager() == null||aftersale.getOrderManager().getId() == null){
                ExceptionUtil.throwServiceException("请选择订单经理！");
            }
            if (aftersale.getDisposeScheme() == null) {
                ExceptionUtil.throwServiceException("请填写公司处理方案！");
            }
            if (aftersale.getProcessingStatus() == null) {
                ExceptionUtil.throwServiceException("请选择处理状态！");
            }
            if (aftersale.getProductionTime() == null){
                ExceptionUtil.throwServiceException("请填写生产时间！");
            }
            aftersale.setReviewDate(new Date());
            aftersale.setNodeApprover(storeMemberService.getCurrent().getName());
        }
        if (nodeName.contains("售后经理")) {
            if (aftersale.getManagerOpinion() == null) {
                ExceptionUtil.throwServiceException("请填写售后经理意见！");
            }
        }
        if (nodeName.contains("工厂")) {
            if (aftersale.getIsDeal() == null) {
                ExceptionUtil.throwServiceException("请选择是否处理！");
            } else {
                if (aftersale.getIsDeal() == 1) {
                    if (aftersale.getIsReturn() == null) {
                        ExceptionUtil.throwServiceException("请填写是否退货！");
                    }
                    if (aftersale.getIsReturn() == 0 && aftersale.getFreightForwarder() == null) {
                        ExceptionUtil.throwServiceException("请填写运费承担方！");
                    }
                    if (aftersale.getFd() == null) {
                        ExceptionUtil.throwServiceException("请填写工厂处理方案！");
                    }
                    String fd = aftersale.getFd();
                    if(fd.contains("局部更换")&&aftersale.getFdPartialReplacement() == null){
                        ExceptionUtil.throwServiceException("请填写局部更换平方数！");
                    }
                    if(fd.contains("退货")&&aftersale.getFdReturnGoods() == null){
                        ExceptionUtil.throwServiceException("请填写退货平方数！");
                    }
                }
            }
            aftersale.setEndTime(new Date());
        }
        if (nodeName.contains("质量中心处理意见")) {
            if (aftersale.getHandlingSuggestion() == null) {
                ExceptionUtil.throwServiceException("请填写质量中心处理意见！");
            }
            if (aftersale.getLiabilityJudgments() == null) {
                ExceptionUtil.throwServiceException("请填写责任判断！");
            }
        }
        if (nodeName.contains("订单部核价")) {
            if (aftersale.getFactoryPrice() == null) {
                ExceptionUtil.throwServiceException("请填写出厂价！");
            }
            if (aftersale.getAmount() == null) {
                ExceptionUtil.throwServiceException("请填写合计费用！");
            }
            if (aftersale.getPlatformPrice() == null) {
                ExceptionUtil.throwServiceException("请填写平台价！");
            }
            if (aftersale.getPlatformAmount() == null) {
                ExceptionUtil.throwServiceException("请填写合计费用！");
            }
        }
        if (nodeName.contains("经销商处理")) {
            if(af.getIsDeal()==1){
                String fd = af.getFd();
                String con = "";
                Boolean a = fd.contains("更换");
                Boolean b = fd.contains("赔偿");
                Boolean c = fd.contains("退货");
                Boolean d = (fd.contains("更换") && fd.contains("赔偿"));
                Boolean e = (fd.contains("退货") && fd.contains("赔偿"));
                List<CoupleBackAttach> a1 = aftersale.getCoupleBackAttachs();// 售后确认处理单
                List<AgreementAttach> a2 = aftersale.getAgreementAttachs();// 售后协议书
                List<QuittanceAttach> a3 = aftersale.getQuittanceAttachs();// 收据
                List<ReturnsAttach> a4 = aftersale.getReturnsAttachs();// 退货申请单
                if (a) {
                    con = "请上传售后确认处理单";
                    if (a1 == null || a1.size() <= 0) {
                        ExceptionUtil.throwServiceException(con);
                    }
                }
                if (b) {
                    con = "请上传售后协议书+收据";
                    if ((a2 == null || a2.size() <= 0) || (a3 == null || a3.size() <= 0)) {
                        ExceptionUtil.throwServiceException(con);
                    }
                }
                if (c) {
                    con = "请上传售后处理确认单+退货申请单";
                    if ((a1 == null || a1.size() <= 0) || (a4 == null || a4.size() <= 0)) {
                        ExceptionUtil.throwServiceException(con);
                    }
                }
                if (d) {
                    con = "请上传售后处理确认单+售后协议书+收据";
                    if ((a1 == null || a1.size() <= 0) || (a2 == null || a2.size() <= 0)
                            || (a3 == null || a3.size() <= 0)) {
                        ExceptionUtil.throwServiceException(con);
                    }
                }
                if (e) {
                    con = "请上传售后处理确认单+售后协议书+收据+退货申请单";
                    if ((a1 == null || a1.size() <= 0) || (a2 == null || a2.size() <= 0) || (a3 == null || a3.size() <= 0)
                            || (a4 == null || a4.size() <= 0)) {
                        ExceptionUtil.throwServiceException(con);
                    }
                }
            }
        }
        if (nodeName.contains("回访")) {
            if (aftersale.getReviewResult() == null) {
                ExceptionUtil.throwServiceException("回访结果不能为空");
            }
            if (aftersale.getSatisfaction() == null) {
                ExceptionUtil.throwServiceException("处理满意度不能为空");
            }
            if (aftersale.getReviewStatus() == null) {
                ExceptionUtil.throwServiceException("请选择回访状态！");
            }
            if(af.getIsDeal() == 1){
                if (af.getIsReturn() == 1){//不退货
                    if(aftersale.getDepositRechargeAmount()==null){
                        ExceptionUtil.throwServiceException("请填写冲账金额！");
                    }
                    if(aftersale.getDepositRechargeMemo()==null){
                        ExceptionUtil.throwServiceException("请填写冲账备注！");
                    }
                }
            }
        }
        if(isUpdate){
            update(aftersale);
        }
    }


    @Override
    public List<Map<String, Object>> findReturnsLogisticsAttach(Long id) {
        return aftersaleDao.findReturnsLogisticsAttach(id);
    }

    @Override
    public List<Map<String, Object>> findCollectionAttach(Long id) {
        return aftersaleDao.findCollectionAttach(id);
    }

    public List<Map<String, Object>> findHistoryAftersale(Aftersale aftersale) {
        return aftersaleDao.findHistoryAftersale(aftersale.getId(), aftersale.getContactNumber(), aftersale.getHeadNewArea().getId(), aftersale.getLayingAddress());
    }

    @Override
    public AftersaleJudgment findAfterJudgment(Long id) {
        return aftersaleDao.findAfterJudgment(id);
    }

    private static final String FIVE_GD_URL = Global.getLoader().getProperty("link5.fiveGd.url");
    @Override
    public Map<String, Object> getLink5Gd(Long id) throws Exception {

        LogUtils.debug("--------------获取5期售后工单----------------");
        Map<String,String> headers= new  HashMap<String,String>();
        AccessToken accessToken = null;
        CompanyInfo companyInfo = companyInfoBaseService.find(9L);
        String sn = "";

        try {
            accessToken = accessTokenService.getAccessToken();
            LogUtils.debug(accessToken.toString());

            char[] charArray = accessToken.getTokenType().toCharArray();
            charArray[0] -=32;//ascii值减32，实现小写英文字母转大写，非小写英文字母会出问题
            String tokenType = String.valueOf(charArray);
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", tokenType + " " + accessToken.getAccessToken());

            System.out.println(tokenType + " " + accessToken.getAccessToken());

            LogUtils.debug("-----------------查询link5期工单开始------------------------");
            String responseStr = HttpClientUtil.get(FIVE_GD_URL + id,headers);
            LogUtils.debug("response:"+responseStr);
            System.out.println("response:"+responseStr);
            Map<String, Object> response = JsonUtils.toObjectMap(responseStr);
            Map<String, Object> data = (Map<String, Object>) response.get("data");

            A1_MessageToH msgH = new A1_MessageToH();
            if(Boolean.TRUE.equals(data.get("success"))) {
                msgH.sethResult("2");
            }else {
                msgH.sethResult("1");
            }
            String header = JsonUtils.toJson(headers);
            msgH.setData("{\"header\":"+header+",\"url\":"+FIVE_GD_URL + id+"}");
            msgH.setCompanyInfoId(companyInfo.getId());
            msgH.setCompanytoken(companyInfo.getUniqueIdentify());
            msgH.setType(1102);
            msgH.setField1(null);
            msgH.setField2(sn);
            msgH.setField8(responseStr);
            msgH.sethResultMsg(responseStr);
            a1_MessageToHService.save(msgH);
            LogUtils.debug("-----------------查询link5期工单结束------------------------");

            return data;
        } catch (Exception e) {
            LogUtils.debug("-----------------查询link5期工单出错------------------------");
            LogUtils.error("查询link5期工单出错",e);
            A1_MessageToH msgH = new A1_MessageToH();
            String header = JsonUtils.toJson(headers);
            msgH.setData("{\"header\":"+header+",\"url\":"+FIVE_GD_URL + id+"}");
            msgH.setCompanyInfoId(companyInfo.getId());
            msgH.setCompanytoken(companyInfo.getUniqueIdentify());
            msgH.setType(1102);
            msgH.setField1(null);
            msgH.setField2(sn);
            msgH.setField8(e.getMessage());
            Throwable t = e;
            while(t.getCause()!=null) {
                t = t.getCause();
            }
            msgH.sethResultMsg("查询link5期工单出错:"+t.getMessage());
            msgH.sethResult("1");
            a1_MessageToHService.save(msgH);
            throw e;
        }
    }

    private static final String FIVE_GDS_URL = Global.getLoader().getProperty("link5.fiveGds.url");
    @Override
    public Page<Map<String, Object>> getLink5Gds(String requestStr) throws Exception {

        LogUtils.debug("--------------获取5期售后工单----------------");
        Map<String,String> headers= new  HashMap<String,String>();
        AccessToken accessToken = null;
        CompanyInfo companyInfo = companyInfoBaseService.find(9L);
        String sn = "";

        try {
            accessToken = accessTokenService.getAccessToken();
            LogUtils.debug(accessToken.toString());

            char[] charArray = accessToken.getTokenType().toCharArray();
            charArray[0] -=32;//ascii值减32，实现小写英文字母转大写，非小写英文字母会出问题
            String tokenType = String.valueOf(charArray);
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", tokenType + " " + accessToken.getAccessToken());

            System.out.println(tokenType + " " + accessToken.getAccessToken());

            LogUtils.debug("-----------------查询link5期工单列表开始------------------------");
            String responseStr = HttpClientUtil.post(FIVE_GDS_URL,requestStr,headers);
            LogUtils.debug("response:"+responseStr);
            System.out.println("response:"+responseStr);
            Map<String, Object> response = JsonUtils.toObjectMap(responseStr);
            Map<String, Object> data = (Map<String, Object>) response.get("data");
            Long total = Long.valueOf(data.get("total").toString());
            Integer pages = (Integer)data.get("pages");
            Integer size = (Integer)data.get("size");
            List<Map<String,Object>> records = (ArrayList<Map<String,Object>>)data.get("records");
            Pageable pageable = new Pageable(pages, size);
            Page<Map<String, Object>> page = new Page<Map<String, Object>>(records, total, pageable);

            System.out.println(page);

            A1_MessageToH msgH = new A1_MessageToH();
            if(Boolean.TRUE.equals(data.get("success"))) {
                msgH.sethResult("2");
            }else {
                msgH.sethResult("1");
            }
            String header = JsonUtils.toJson(headers);
            msgH.setData("{\"header\":"+header+",\"requestBody\":"+requestStr+"}");
            msgH.setCompanyInfoId(companyInfo.getId());
            msgH.setCompanytoken(companyInfo.getUniqueIdentify());
            msgH.setType(1101);
            msgH.setField1(null);
            msgH.setField2(sn);
            msgH.setField8(responseStr);
            msgH.sethResultMsg(responseStr);
            a1_MessageToHService.save(msgH);
            LogUtils.debug("-----------------查询link5期工单列表结束------------------------");

            return page;
        } catch (Exception e) {
            LogUtils.debug("-----------------查询link5期工单列表出错------------------------");
            LogUtils.error("查询link5期工单列表出错",e);
            A1_MessageToH msgH = new A1_MessageToH();
            String header = JsonUtils.toJson(headers);
            msgH.setData("{\"header\":"+header+",\"requestBody\":"+requestStr+"}");
            msgH.setCompanyInfoId(companyInfo.getId());
            msgH.setCompanytoken(companyInfo.getUniqueIdentify());
            msgH.setType(1101);
            msgH.setField1(null);
            msgH.setField2(sn);
            msgH.setField8(e.getMessage());
            Throwable t = e;
            while(t.getCause()!=null) {
                t = t.getCause();
            }
            msgH.sethResultMsg("查询link5期工单列表出错:"+t.getMessage());
            msgH.sethResult("1");
            a1_MessageToHService.save(msgH);
            throw e;
        }
    }

    private static final String FOUR_URL = Global.getLoader().getProperty("link4.url");
    /**
     * 查询link4售后单
     */
    @Override
    public String selectFromLink4(String sn, String name,Long storeId, Pageable pageable) {

        LogUtils.debug("--------------查询link4期售后单----------------");
        Map<String,String> headers= new  HashMap<String,String>();
        Store store = storeBaseService.find(storeId);

        headers.put("Content-Type", "application/json");
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("isKlkp", 1);
        map.put("invoiceStatusList", new int[]{1,2});
        map.put("sn", sn);
        map.put("name", name);
        map.put("pageSize", pageable.getPageSize());
        map.put("pageNumber", pageable.getPageNumber());
        map.put("outTradeNo", store.getOutTradeNo());
        String requestStr = JsonUtils.toJson(map);

        LogUtils.debug("-----------------查询link4期售后单开始------------------------");

        LogUtils.debug("requestStr: "+requestStr);
        String responseStr = HttpClientUtil.post(FOUR_URL + "intf/aftersale/select_aftersale_data.jhtml",requestStr,headers);
        LogUtils.debug("response: "+responseStr);
        Map<String, Object> response = JsonUtils.toObjectMap(responseStr);

        return response.get("content").toString();
    }

    @Override
    public String syncCreditRechargeToAftersale(CreditRechargeVo vo) {

        LogUtils.debug("--------------授信单信息回传到聆客4----------------");
        Map<String,String> headers= new  HashMap<String,String>();
        headers.put("Content-Type", "application/json");
        String requestStr = JsonUtils.toJson(vo);
        LogUtils.debug("requestStr: "+requestStr);
        String responseStr = HttpClientUtil.post(FOUR_URL + "intf/aftersale/syncCreditRechargeToAftersale.jhtml",requestStr,headers);
        LogUtils.debug("response: "+responseStr);
        Map<String, Object> response = JsonUtils.toObjectMap(responseStr);

        return response.get("content").toString();
    }


    @Override
    public String syncOrderToAftersale(OrderVo vo) {

        LogUtils.debug("--------------销售单信息回传到聆客4----------------");
        Map<String,String> headers= new  HashMap<String,String>();
        headers.put("Content-Type", "application/json");
        String requestStr = JsonUtils.toJson(vo);
        LogUtils.debug("requestStr: "+requestStr);
        String responseStr = HttpClientUtil.post(FOUR_URL + "intf/aftersale/syncOrderToAftersale.jhtml",requestStr,headers);
        LogUtils.debug("response: "+responseStr);
        Map<String, Object> response = JsonUtils.toObjectMap(responseStr);

        return response.get("content").toString();
    }
}
