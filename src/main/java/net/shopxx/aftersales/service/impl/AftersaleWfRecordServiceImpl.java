package net.shopxx.aftersales.service.impl;

import net.shopxx.aftersales.dao.AftersaleWfRecordDao;
import net.shopxx.aftersales.entity.AftersaleWfRecord;
import net.shopxx.aftersales.service.AftersaleWfRecordService;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service("aftersaleWfRecordServiceImpl")
public class AftersaleWfRecordServiceImpl extends
        BaseServiceImpl<AftersaleWfRecord> implements AftersaleWfRecordService {

    @Resource(name = "aftersaleWfRecordDao")
    public AftersaleWfRecordDao aftersaleWfRecordDao;

    @Override
    public List<Map<String, Object>> findAftersaleWfRecord(Long aftersaleId) {
        return aftersaleWfRecordDao.findAftersaleWfRecord(aftersaleId);
    }

    @Override
    public void deleteByAftersaleId(Long aftersaleId) {
         aftersaleWfRecordDao.deleteByAftersaleId(aftersaleId);
    }
}
