package net.shopxx.aftersales.service;

import net.shopxx.aftersales.entity.AftersaleWfRecord;
import net.shopxx.base.core.service.BaseService;

import java.util.List;
import java.util.Map;

public interface AftersaleWfRecordService extends BaseService<AftersaleWfRecord> {

    public List<Map<String, Object>> findAftersaleWfRecord(Long aftersaleId);

    public void deleteByAftersaleId(Long aftersaleId);
}
