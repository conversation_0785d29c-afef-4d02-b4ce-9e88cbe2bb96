package net.shopxx.aftersales.service;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.entity.AftersaleJudgment;
import net.shopxx.aftersales.entity.CreditRechargeVo;
import net.shopxx.aftersales.entity.OrderVo;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;

import java.util.List;
import java.util.Map;

public interface AftersaleService extends ActWfBillService<Aftersale> {

	void aftersaleSave(Aftersale aftersale, Long saleOrgId, Long productId, Long storeId,
					   String[] storeAppeal, Long categoryId,Long companyInfoId,Long aftersaleId,
					   AftersaleJudgment aftersaleJudgment);

	void aftersaleUpdate(Aftersale aftersale, Long saleOrgId, Long productId, Long storeId, String[] storeAppeal);

	public Page<Map<String, Object>> findPage(Object[] params, Pageable pageable);

	public List<Map<String, Object>> findCoupleBackAttach(Long id);

	public List<Map<String, Object>> findSurveyorAttach(Long id);

	public List<Map<String, Object>> findAgreementAttach(Long id);

	public List<Map<String, Object>> findQuittanceAttach(Long id);

	public List<Map<String, Object>> findReturnsAttach(Long id);

	public void saveform(Aftersale aftersale, Integer Type, String[] fd, String[] cd, String[] storeTreatmentScheme);

	public List<Map<String, Object>> findSalesContractAttach(Long id);

	public List<Map<String, Object>> findAcceptanceSheetAttach(Long id);

	public List<Map<String, Object>> findScenePicturesAttach(Long id);

	Page<Map<String, Object>> findStoreMemberPage(Pageable pageable);

	/** 退货查询售后单据 */
	Page<Map<String, Object>> findPageAftersale(Object[] args, Pageable pageable);

	String selectFromLink4(String sn, String name,Long storeId, Pageable pageable);

	Page<Map<String, Object>> findOrganizationPage(Pageable pageable);


	/**
	 * 退货变更售后单
	 * @param aftersale 售后单
	 * @param status 状态  未退货0,退货中1,已退货2
	 */
	void b2bReturnsUpdeatAftersale(Aftersale aftersale, String status);

	/** 创建流程实例 */
	void createWf(Long id, String modelId, Long objTypeId);

	void createPolicyBill(Aftersale aftersale);

	Integer count(Object[] params);

	List<Map<String, Object>> findList(Object[] params, Pageable pageable, Integer page, Integer size);

	List<Map<String, Object>> findHistorySftersale(Aftersale aftersale);


	List<Map<String, Object>> findReturnsLogisticsAttach(Long id);

	List<Map<String, Object>> findCollectionAttach(Long id);

	List<Map<String, Object>> findHistoryAftersale(Aftersale aftersale);

	AftersaleJudgment findAfterJudgment(Long id);

	void createRdPolicyBill(Aftersale aftersale);

	Page<Map<String, Object>> getLink5Gds(String requestStr) throws Exception;

	Map<String, Object> getLink5Gd(Long id) throws Exception;

	String syncCreditRechargeToAftersale(CreditRechargeVo vo);

	String syncOrderToAftersale(OrderVo vo);
}
