package net.shopxx.aftersales.controller;

import net.sf.json.JSON;
import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.act.util.StrUtil;
import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.entity.AftersaleJudgment;
import net.shopxx.aftersales.entity.AftersaleWfRecord;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.aftersales.service.AftersaleWfRecordService;
import net.shopxx.appfrom.service.ShowSignInService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.*;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PolicyEntryService;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.shop.service.ShopStoreService;
import net.shopxx.shop.service.StoreMemberService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.TextareaLineBreakUtil;
import net.shopxx.wf.service.WfBaseService;
import org.activiti.engine.task.Task;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Controller("aftersalesAftersaleController")
@RequestMapping("/aftersales/aftersale")
public class AftersaleController extends BaseController {

	@Resource(name = "aftersaleServiceImpl")
	private AftersaleService aftersaleService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name = "shopStoreServiceImpl")
	private ShopStoreService shopStoreService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "storeMemberServiceImpl")
	private StoreMemberService storeMemberService;
	@Resource(name = "showSignInServiceImpl")
	private ShowSignInService showSignInService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
    @Autowired
	private SystemDictBaseService systemDictBaseService;
    @Autowired
    private PolicyEntryService policyEntryService;
    @Autowired
    private SbuService sbuService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "aftersaleWfRecordServiceImpl")
	private AftersaleWfRecordService aftersaleWfRecordService;
	@Resource(name = "b2bReturnsServiceImpl")
	private B2bReturnsService b2bReturnsService;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long objTypeId, Long objid, Pageable pageable, ModelMap model,Long menuId) {
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("menuId",menuId);
		return "/aftersales/aftersale/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(String sn, Pageable pageable,Long menuId,Long userId, ModelMap model) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "saleOrgRegion"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null, filters , null);
		model.addAttribute("saleOrgRegions", saleOrgRegions);
		model.addAttribute("sbus", sbuService.findAll());
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("menuId", menuId);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/aftersales/aftersale/list";
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		Store store = shopStoreService.findStore();
		// if (store != null) {
		// String Provinces =
		// storeBaseService.findAreaCity(store.getHeadNewArea());
		// model.addAttribute("Provinces", Provinces);
		// }
		model.addAttribute("store", store);
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		model.addAttribute("storeMember", storeMember);

		return "/aftersales/aftersale/add";
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		Aftersale aftersale = aftersaleService.find(id);
		AftersaleJudgment aftersaleJudgment = aftersaleService.findAfterJudgment(id);
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		List<Map<String, Object>> salesContractAttach = aftersaleService.findSalesContractAttach(id);
		List<Map<String, Object>> acceptanceSheetAttach = aftersaleService.findAcceptanceSheetAttach(id);
		List<Map<String, Object>> scenePicturesAttach = aftersaleService.findScenePicturesAttach(id);
		List<Map<String, Object>> coupleBackAttach = aftersaleService.findCoupleBackAttach(id);
		List<Map<String, Object>> surveyorAttach = aftersaleService.findSurveyorAttach(id);
		List<Map<String, Object>> quittanceAttach = aftersaleService.findQuittanceAttach(id);
		List<Map<String, Object>> returnsAttach = aftersaleService.findReturnsAttach(id);
		List<Map<String, Object>> collectionAttach = aftersaleService.findCollectionAttach(id);
		List<Map<String, Object>> agreementAttach = aftersaleService.findAgreementAttach(id);
		List<Map<String, Object>> returnsLogisticsAttach = aftersaleService.findReturnsLogisticsAttach(id);
		model.addAttribute("sales_contract_attach",
				salesContractAttach == null ? null : JsonUtils.toJson(salesContractAttach));
		model.addAttribute("acceptance_sheet_attach",
				acceptanceSheetAttach == null ? null : JsonUtils.toJson(acceptanceSheetAttach));
		model.addAttribute("scene_pictures_attach",
				scenePicturesAttach == null ? null : JsonUtils.toJson(scenePicturesAttach));
		model.addAttribute("coupleBack_attach", coupleBackAttach == null ? null : JsonUtils.toJson(coupleBackAttach));
		model.addAttribute("surveyor_attach", surveyorAttach == null ? null : JsonUtils.toJson(surveyorAttach));
		model.addAttribute("quittance_attach", quittanceAttach == null ? null : JsonUtils.toJson(quittanceAttach));
		model.addAttribute("returns_attach", returnsAttach == null ? null : JsonUtils.toJson(returnsAttach));
		model.addAttribute("collection_attach", collectionAttach == null ? null : JsonUtils.toJson(collectionAttach));
		model.addAttribute("agreement_attach", agreementAttach == null ? null : JsonUtils.toJson(agreementAttach));
		model.addAttribute("returns_logistics_attach", agreementAttach == null ? null : JsonUtils.toJson(returnsLogisticsAttach));
		model.addAttribute("aftersale", aftersale);
		model.addAttribute("aftersaleJudgment", aftersaleJudgment);
		aftersaleWfOfShimu(aftersale,storeMember,model);//实木售后流程
		/*// 获取流程节点
		if (aftersale.getWfId() != null) {
			ActWf wf = aftersaleService.getWfByWfId(aftersale.getWfId());
			if(wf.getModelId().equals("1")) {
				aftersaleWf(aftersale,storeMember,model);//旧版售后流程
			} else {
				aftersaleWfOfShimu(aftersale,storeMember,model);//实木售后流程
			}
		}*/

		Boolean isBanned = false;
		Store store = aftersale.getStore();
		//获取经销商的机构
		SaleOrg saleOrg = store.getSaleOrg();
		//若经销商的机构在下面获取的机构名列表中，则不显示售后单中的政策、退货信息的到账金额
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "aftersaleBanSaleOrg"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> banSaleOrgs = systemDictBaseService.findList(null,filters,null);
		for (SystemDict banSaleOrg : banSaleOrgs) {
			if(banSaleOrg.getValue().equals(saleOrg.getName())){
				isBanned = true;
				break;
			}
		}


		BigDecimal total = BigDecimal.ZERO;
		//退货单号
		System.out.println("b2bReturnSn = " + aftersale.getB2bReturnSn());
		if (!StrUtil.isEmpty(aftersale.getB2bReturnSn())) {
			filters.clear();
			filters.add(Filter.eq("sn", aftersale.getB2bReturnSn()));
			List<B2bReturns> list = b2bReturnsService.findList(null, filters, null);
			if (list != null && list.size() > 0) {
				B2bReturns b2bReturns = list.get(0);
				model.addAttribute("b2bReturns", b2bReturns);
				System.out.println("b2bReturns = " + b2bReturns);
				if (b2bReturns != null) {
					List<B2bReturnsItem> b2bReturnsItems = b2bReturns.getB2bReturnsItems();
					if (b2bReturnsItems != null) {
						for (B2bReturnsItem b2bReturnsItem : b2bReturnsItems) {
							total = total.add(b2bReturnsItem.getReturnedQuantity());
						}
					}
				}
			}
		}
		//退货单
		model.addAttribute("total", total);

		//销售渠道
		filters.clear();
		filters.add(Filter.eq("code", "saleChannel"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleChannels = systemDictBaseService.findList(null, filters, null);
		model.addAttribute("saleChannels", saleChannels);

		//业务类型-系统词汇(售后)
		filters.clear();
		filters.add(Filter.eq("code", "businessAfterSaleType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictBaseService.findList(null,filters,null);
		model.addAttribute("businessTypes", businessTypes);
		model.addAttribute("isBanned", isBanned);
		//同一个地址或者客户电话，多次申报售后的，查出历史的售后单据
		List<Map<String,Object>> historyList = aftersaleService.findHistoryAftersale(aftersale);
		for (Map<String, Object> map : historyList) {
			StringBuilder address = new StringBuilder();
			if(map.get("head_new_area")!=null) {
				Long headNewArea = Long.valueOf(map.get("head_new_area").toString());
				Area area = areaBaseService.find(headNewArea);
				address.append(area==null?"":area.getFullName());
			}
			if(map.get("laying_address")!=null) {
				address.append(map.get("laying_address").toString());
			}
			map.put("address", address.toString());
		}
		model.addAttribute("history", historyList == null ? null : JsonUtils.toJson(historyList));
		model.addAttribute("woodTypeProductOfficer",getWoodType());
		//获取流程审批信息
		List<Map<String,Object>> wfItems = aftersaleWfRecordService.findAftersaleWfRecord(aftersale.getId());
		model.addAttribute("table_technologicals",JsonUtils.toJson(wfItems));
		return "/aftersales/aftersale/edit";
	}

	private List<SystemDict> getWoodType(){
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "woodTypeProductOfficer"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> list = systemDictBaseService.findList(null, filters , null);
		return list;
	}

	private void aftersaleWfOfShimu(Aftersale aftersale, StoreMember storeMember, ModelMap model) {
		ActWf wf = aftersaleService.getWfByWfId(aftersale.getWfId());
		if (wf != null) {
			// 售后专员
			Boolean service = false;
			Boolean services = false;
			// 售后经理
			Boolean shjlsh = false;
			Boolean shjlshs = false;
			// 勘察
			Boolean explore = false;
			Boolean explores = false;
			// 勘察人
			Boolean surveyor = false;
			Boolean surveyors = false;
			//省长意见
			Boolean sz = false;
			Boolean szs = false;
			//服务副总监意见
			Boolean director = false;
			Boolean directors = false;
			// 质量中心审批
			Boolean quality = false;
			Boolean qualitys = false;
			// 订单部核价
			Boolean order = false;
			Boolean orders = false;
			//客服专员填写标准费用
			Boolean cost = false;
			Boolean costs = false;
			// 经销商提交售后处理单据
			Boolean store = false;
			Boolean stores = false;
			// 售后专员，回访
			Boolean review = false;
			Boolean reviews = false;

			List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
			for (Map<String, Object> c : item) {
				if (c.get("suggestion") != null) {
					String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
					String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
					System.out.println("activityName:"+rwm);
					if (rwm.contains("售后专员")) {
						service = Boolean.valueOf(approved);
					}
					if (rwm.contains("售后经理")) {
						shjlsh = Boolean.valueOf(approved);
						if (!shjlsh) {
							service = false;
						}
					}
					if (rwm.equals("勘察")) {
						explore = Boolean.valueOf(approved);
						if(!explore) {
							shjlsh = false;
						}
					}
					if (rwm.equals("勘察人")) {
						surveyor = Boolean.valueOf(approved);
						if(!surveyor) {
							explore = false;
						}
					}
					if (rwm.equals("省长意见")) {
						sz = Boolean.valueOf(approved);
						if(!sz) {
							surveyor = false;
						}
					}
					if (rwm.equals("服务副总监意见")) {
						director = Boolean.valueOf(approved);
						if(!director) {
							sz = false;
						}
					}
					if (rwm.contains("质量中心审批")) {
						quality = Boolean.valueOf(approved);
						if (!quality) {
							director = false;
						}
					}
					if (rwm.contains("订单部核价")) {
						order = Boolean.valueOf(approved);
						if (!order) {
							quality = false;
						}
					}
					if (rwm.contains("标准费用")) {
						cost = Boolean.valueOf(approved);
					}
					if (rwm.contains("经销商处理")) {
						store = Boolean.valueOf(approved);
					}
					if (rwm.contains("回访")) {
						review = Boolean.valueOf(approved);
					}
				}
			}
			Task t = aftersaleService.getCurrTaskByWf(wf);
			if (t != null) {
				// 获取当前节点所有用户id
				List<String> userId = actWfService.getTaskUsers(t.getId());
				System.out.println(userId);
				System.out.println(t.getName());
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("售后专员")) {
					services = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("售后经理")) {
					services = true;
					shjlshs = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("勘察")) {
					services = true;
					shjlshs = true;
					explores = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("勘察人")) {
					services = true;
					shjlshs = true;
					explores = true;
					surveyors = true;				
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("省长意见")) {
					services = true;
					shjlshs = true;
					explores = true;
					surveyors = true;
					szs = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("服务副总监意见")) {
					services = true;
					shjlshs = true;
					explores = true;
					surveyors = true;
					szs = true;
					directors = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("质量中心")) {
					explores = true;
					services = true;
					shjlshs = true;
					qualitys = true;
					surveyors = true;	
					szs = true;
					directors = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("订单部核价")) {
					explores = true;
					services = true;
					shjlshs = true;
					qualitys = true;
					orders = true;
					surveyors = true;	
					szs = true;
					directors = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("标准费用")) {
					explores = true;
					services = true;
					shjlshs = true;
					qualitys = true;
					orders = true;
					costs = true;
					surveyors = true;	
					szs = true;
					directors = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("经销商处理")) {
					explores = true;
					services = true;
					shjlshs = true;
					qualitys = true;
					orders = true;
					stores = true;
					surveyors = true;	
					costs = true;
					szs = true;
					directors = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("回访")) {
					explores = true;
					services = true;
					shjlshs = true;
					qualitys = true;
					orders = true;
					stores = true;
					reviews = true;
					surveyors = true;	
					costs = true;
					szs = true;
					directors = true;
				}
			}
			model.addAttribute("wf", wf);
			model.addAttribute("node", t);
			model.addAttribute("surveyor", surveyor);
			model.addAttribute("surveyors", surveyors);
			model.addAttribute("explore", explore);
			model.addAttribute("explores", explores);
			model.addAttribute("service", service);
			model.addAttribute("services", services);
			model.addAttribute("shjlsh", shjlsh);
			model.addAttribute("shjlshs", shjlshs);
			model.addAttribute("quality", quality);
			model.addAttribute("qualitys", qualitys);
			model.addAttribute("order", order);
			model.addAttribute("orders", orders);
			model.addAttribute("store", store);
			model.addAttribute("stores", stores);
			model.addAttribute("review", review);
			model.addAttribute("reviews", reviews);
			model.addAttribute("cost", cost);
			model.addAttribute("costs", costs);
			model.addAttribute("sz", sz);
			model.addAttribute("szs", szs);
			model.addAttribute("director", director);
			model.addAttribute("directors", directors);
			model.addAttribute("storeMember", storeMember);
		}
	}

	private void aftersaleWf(Aftersale aftersale, StoreMember storeMember, ModelMap model) {
		ActWf wf = aftersaleService.getWfByWfId(aftersale.getWfId());
		if (wf != null) {
			// 勘察
			Boolean explore = false;
			Boolean explores = false;
			// 勘察人
			Boolean surveyor = false;
			Boolean surveyors = false;
			// 售后专员 20201008 跟勘察合并
//			Boolean service = false;
//			Boolean services = false;
			// 售后经理
			Boolean shjlsh = false;
			Boolean shjlshs = false;
			// 工厂厂长
			Boolean factory = false;
			Boolean factorys = false;
			// 质量中心处理意见
			Boolean quality = false;
			Boolean qualitys = false;
			// 订单部核价
			Boolean order = false;
			Boolean orders = false;
			// 经销商处理结果提交
			Boolean store = false;
			Boolean stores = false;
			// 客服审核，回访
			Boolean review = false;
			Boolean reviews = false;

			List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
			for (Map<String, Object> c : item) {
				if (c.get("suggestion") != null) {
					String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
					String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
					if (rwm.equals("勘察")) {
						explore = Boolean.valueOf(approved);
					}
					if (rwm.equals("勘察人")) {
						surveyor = Boolean.valueOf(approved);
						if (!surveyor) {
							explore = false;
						}
					}
//					if (rwm.contains("售后专员")) {
//						service = Boolean.valueOf(approved);
//						if (!service) {
//							explore = false;
//							surveyor = false;
//						}
//					}
					if (rwm.contains("售后经理")) {
						shjlsh = Boolean.valueOf(approved);
						if (!shjlsh) {
							//service = false;
							explore = false;
							surveyor = false;
						}
					}
					if (rwm.contains("工厂")) {
						factory = Boolean.valueOf(approved);
						if (!factory) {
							shjlsh = false;
						}
					}
					if (rwm.contains("质量中心处理意见")) {
						quality = Boolean.valueOf(approved);
						if (!quality) {
							factory = false;
						}
					}
					if (rwm.contains("订单部核价")) {
						order = Boolean.valueOf(approved);
						if (!order) {
							quality = false;
						}
					}
					if (rwm.contains("经销商处理")) {
						store = Boolean.valueOf(approved);
					}
					if (rwm.contains("回访")) {
						review = Boolean.valueOf(approved);
					}
				}
			}
			Task t = aftersaleService.getCurrTaskByWf(wf);
			if (t != null) {
				// 获取当前节点所有用户id
				List<String> userId = actWfService.getTaskUsers(t.getId());

				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("勘察")) {
					explores = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("勘察人")) {
					explores = true;
					surveyors = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("售后专员")) {
					explores = true;
					surveyors = true;
//					services = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("售后经理")) {
					explores = true;
					surveyors = true;
//					services = true;
					shjlshs = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("工厂")) {
					explores = true;
					surveyors = true;
//					services = true;
					shjlshs = true;
					factorys = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("质量中心处理意见")) {
					explores = true;
					surveyors = true;
//					services = true;
					shjlshs = true;
					factorys = true;
					qualitys = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("订单部核价")) {
					explores = true;
					surveyors = true;
//					services = true;
					shjlshs = true;
					factorys = true;
					qualitys = true;
					orders = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().equals("经销商处理结果提交")) {
					explores = true;
					surveyors = true;
//					services = true;
					shjlshs = true;
					factorys = true;
					qualitys = true;
					orders = true;
					stores = true;
				}
				if (userId.contains(storeMember.getId().toString()) && t.getName().contains("回访")) {
					explores = true;
					surveyors = true;
//					services = true;
					shjlshs = true;
					factorys = true;
					qualitys = true;
					orders = true;
					stores = true;
					reviews = true;
				}
			}
			if(aftersale.getIsLocaleSurveyor()!=null&&aftersale.getIsLocaleSurveyor() == 1){
				surveyor = false;surveyors = false;
			}
			model.addAttribute("wf", wf);
			model.addAttribute("node", t);
			model.addAttribute("explore", explore);
			model.addAttribute("explores", explores);
			model.addAttribute("surveyor", surveyor);
			model.addAttribute("surveyors", surveyors);
//			model.addAttribute("service", service);
//			model.addAttribute("services", services);
			model.addAttribute("shjlsh", shjlsh);
			model.addAttribute("shjlshs", shjlshs);
			model.addAttribute("factory", factory);
			model.addAttribute("factorys", factorys);
			model.addAttribute("quality", quality);
			model.addAttribute("qualitys", qualitys);
			model.addAttribute("order", order);
			model.addAttribute("orders", orders);
			model.addAttribute("store", store);
			model.addAttribute("stores", stores);
			model.addAttribute("review", review);
			model.addAttribute("reviews", reviews);
			model.addAttribute("storeMember", storeMember);
		}
		
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
    ResultMsg save(Aftersale aftersale, Long saleOrgId, Long productId, String[] storeAppeal,
                   Long storeId, Long categoryId) {
		aftersale.setStoreFaultMessage(TextareaLineBreakUtil.lineEnter(aftersale.getStoreFaultMessage()));
		if (aftersale.getHeadNewArea().getId() == null) {
			return error("请选择铺装地址！");
		}
		if (aftersale.getContactNumber() != null) {
			String regEx = "^[1][3,4,5,7,8,9][0-9]{9}$";
			Pattern p = Pattern.compile(regEx);
			Matcher mr = p.matcher(aftersale.getContactNumber());
			if (!mr.matches()) {
				return error("联系电话格式有误");
			}
		}
		// if (aftersale.getZbsurveyorPhone() != null) {
		// String regEx = "^[1][3,4,5,7,8][0-9]{9}$";
		// Pattern p = Pattern.compile(regEx);
		// Matcher mr = p.matcher(aftersale.getZbsurveyorPhone());
		// if (!mr.matches()) {
		// return error("勘察人联系方式格式有误");
		// }
		// }
		if (aftersale.getIsCheckIn() == true && aftersale.getCheckInTime() == null) {
			return error("请选择入住时间！");
		}
		BigDecimal oneHundred = new BigDecimal("100");
		if (aftersale.getGroundMoistureContent() != null) {
			String a = aftersale.getGroundMoistureContent().toString();
//			int b = Integer.parseInt(a);
			BigDecimal b = new BigDecimal(a);
			if (b.compareTo(oneHundred)>1) {
				return error("地面含水率不能大于100");
			}
		}
		if (aftersale.getFloorMoistureContent() != null) {
			String a = aftersale.getFloorMoistureContent().toString();
//			int b = Integer.parseInt(a);
			BigDecimal b = new BigDecimal(a);
			if (b.compareTo(oneHundred)>1) {
				return error("地板含水率不能大于100");
			}
		}
		if (aftersale.getAirHumidity() != null) {
			String a = aftersale.getAirHumidity().toString();
//			int b = Integer.parseInt(a);
			BigDecimal b = new BigDecimal(a);
			if (b.compareTo(oneHundred)>1) {
				return error("空气湿度不能大于100");
			}
		}
		if (aftersale.getSalesContractAttachs().size() < 1) {
			return error("请上传销售合同！");
		}
		if (aftersale.getAcceptanceSheetAttachs().size() < 1) {
			return error("请上传验收单！");
		}
		if (aftersale.getScenePicturesAttachs().size() < 5) {
			return error("请上传现场照片且数量不能小于5张！");
		}
		if (productId == null) {
			return error("请选择产品");
		}
		if (aftersale.getStoreTreatmentScheme() == null) {
			return error("请选择经销商处理方案");
		}
		if (!(aftersale.getPrice().compareTo(BigDecimal.ZERO) > 0)) {
			return error("单价必须大于0！");
		}
		if (saleOrgId == null) {
			return error("机构不能为空");
		}

		aftersaleService.aftersaleSave(aftersale, saleOrgId, productId, storeId, storeAppeal,categoryId,null,null,null);
		return success().addObjX(aftersale.getId());
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
    ResultMsg update(Aftersale aftersale, Long saleOrgId, Long productId, String[] storeAppeal,
                     Long storeId) {
		System.out.println("0:"+aftersale.getStoreFaultMessage());
		aftersale.setStoreFaultMessage(TextareaLineBreakUtil.lineEnter(aftersale.getStoreFaultMessage()));
		System.out.println("1:"+aftersale.getStoreFaultMessage());
		if (aftersale.getHeadNewArea().getId() == null) {
			return error("请选择铺装地址！");
		}
		if (aftersale.getContactNumber() != null) {
			String regEx = "^[1][3,4,5,7,8,9][0-9]{9}$";
			Pattern p = Pattern.compile(regEx);
			Matcher mr = p.matcher(aftersale.getContactNumber());
			if (!mr.matches()) {
				return error("联系电话格式有误");
			}
		}
		// if (aftersale.getZbsurveyorPhone() != null) {
		// String regEx = "^[1][3,4,5,7,8][0-9]{9}$";
		// Pattern p = Pattern.compile(regEx);
		// Matcher mr = p.matcher(aftersale.getZbsurveyorPhone());
		// if (!mr.matches()) {
		// return error("勘察人联系方式格式有误");
		// }
		// }
		BigDecimal oneHundred = new BigDecimal("100");
		if (aftersale.getGroundMoistureContent() != null) {
			String a = aftersale.getGroundMoistureContent().toString();
//			int b = Integer.parseInt(a);
			BigDecimal b = new BigDecimal(a);
			if (b.compareTo(oneHundred)>1) {
				return error("地面含水率不能大于100");
			}
		}
		if (aftersale.getFloorMoistureContent() != null) {
			String a = aftersale.getFloorMoistureContent().toString();
//			int b = Integer.parseInt(a);
			BigDecimal b = new BigDecimal(a);
			if (b.compareTo(oneHundred)>1) {
				return error("地板含水率不能大于100");
			}
		}
		if (aftersale.getAirHumidity() != null) {
			String a = aftersale.getAirHumidity().toString();
//			int b = Integer.parseInt(a);
			BigDecimal b = new BigDecimal(a);
			if (b.compareTo(oneHundred)>1) {
				return error("空气湿度不能大于100");
			}
		}
		if (aftersale.getSalesContractAttachs().size() < 1) {
			return error("请上传销售合同！");
		}
		if (aftersale.getAcceptanceSheetAttachs().size() < 1) {
			return error("请上传验收单！");
		}
		if (aftersale.getScenePicturesAttachs().size() < 5) {
			return error("请上传现场照片且数量不能小于5张！");
		}
		if (productId == null) {
			return error("请选择产品");
		}
		if (aftersale.getStoreTreatmentScheme() == null) {
			return error("请选择经销商处理方案");
		}
		if (!(aftersale.getPrice().compareTo(BigDecimal.ZERO) > 0)) {
			return error("单价必须大于0！");
		}
		if (saleOrgId == null) {
			return error("机构不能为空");
		}

		aftersaleService.aftersaleUpdate(aftersale, saleOrgId, productId, storeId, storeAppeal);
		return success().addObjX(aftersale.getId());
	}

	/**
	 * 审核
	 * 
	 * @param
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody
    ResultMsg check_wf(Long id, String modelId, Long objTypeId) throws IOException {
		if (id == null) {
			// 请选择订单
			return error("请选择售后单");
		}
		aftersaleService.createWf(id, modelId, objTypeId);

		// dateToOAService.sendDeliveryNoteToOa("/aftersales/aftersale/edit.jhtml",
		// id,storeMember);

		return success();
	}

	/**
	 * 流程节点保存
	 */
	@RequestMapping(value = "/saveform", method = RequestMethod.POST)
	public @ResponseBody
    ResultMsg saveform(Aftersale aftersale, Integer Type, String[] fd,
                       String[] cd, String[] storeTreatmentScheme) {
		/* type 用来定义节点 
		0.勘察、1公司处理信息、2工厂处理、3、质量中心处理意见、4、订单部核价、
		5经销商处理信息及意见、6售后回访、7勘察人填写、8销售经理意见、
		9售后专员填写标准费用
		*/
		aftersaleService.saveform(aftersale, Type, fd, cd, storeTreatmentScheme);
		return success().addObjX(aftersale.getId());
	}

	@RequestMapping(value = "/select_factory", method = RequestMethod.GET)
	public String selectFactory(Long[] factoryIds, Pageable pageable, ModelMap model) {
		model.addAttribute("factoryIds", factoryIds);
		return "/aftersales/aftersale/select_factory";
	}

	@RequestMapping(value = "/select_factory_data", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg selectFactoryData(Long[] factoryIds, Pageable pageable) {
		Page<Map<String, Object>> page = aftersaleService.findStoreMemberPage(pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping(value = "/select_aftersale", method = RequestMethod.GET)
	public String selectAftersale(Integer multi,Long storeId, Pageable pageable, ModelMap model) {
		model.addAttribute("multi", multi);
		model.addAttribute("storeId",storeId);
		return "/aftersales/aftersale/select_aftersales";
	}

	@RequestMapping(value = "/select_aftersale_data", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg selectAftersaleData(String sn, String name,Long storeId, Pageable pageable) {
		return success(aftersaleService.selectFromLink4(sn, name,storeId,pageable));
	}

	protected String splicer(String[] str) {
		String s = "";
		if (str != null && str.length > 0) {
			for (int i = 0; i < str.length; i++) {
				if (str.length - 1 == i) {
					s += str[i];
				} else {
					s += str[i] + ",";
				}
			}
		}
		return s;
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
    ResultMsg list_data(String sn, Long storeId, String[] invoiceStatus, String name, // 客户姓名
                        Long contactNumber, // 客户电话
                        String factoryName, // 责任工厂
                        String productName, // 产品名称
                        Integer[] wfStatus, // 流程状态
                        String[] grade, // 售后等级
                        String beginTime,
                        String endTime,
                        String[] processingStatus,
                        String factoryfirstTime,
                        String factoryEndTime,
                        String category,
                        String province,
                        Long[] saleOrgIds,
                        String storeName,
                        String dealerName,
                        Long[] regionIds,
                        Long[] sbuIds,
                        Long storeMemberId,
                        Boolean isReceived,
                        String vonderVode,
                        Pageable pageable) {
		
		
		
		Object[] params = { sn, storeId, invoiceStatus, name, // 客户姓名
				contactNumber, // 客户电话
				factoryName, // 责任工厂
				productName, // 产品名称
				wfStatus, // 流程状态
				grade,// 售后等级
				beginTime,//开始时间
				endTime,//结束时间
				processingStatus,//处理状态
				factoryfirstTime,//工厂批复开始时间
				factoryEndTime,//工厂批复结束时间
				category,//地板品类
				province,//省
				saleOrgIds,//机构
				storeName,//经销商名称
				dealerName,//经销商姓名
				regionIds,//区域id
				sbuIds,//sbuId
				storeMemberId,//区域经理名字
				isReceived,//是否到账
				vonderVode//产品编码
		};
		Page<Map<String, Object>> page = aftersaleService.findPage(params, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);

	}

	/**
	 * 条件导出
	 * 
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
    List<Map<String, Object>> toConditionExport(String sn,
                                                Long storeId,
                                                String[] invoiceStatus,
                                                String name, // 客户姓名
                                                Long contactNumber, // 客户电话
                                                String factoryName, // 责任工厂
                                                String productName, // 产品名称
                                                Integer[] wfStatus, // 流程状态
                                                String[] grade, // 售后等级
                                                String beginTime,
                                                String endTime,
                                                String[] processingStatus,
                                                String factoryfirstTime,
                                                String factoryEndTime,
                                                String category,
                                                String province,
                                                Long[] saleOrgIds,
                                                String storeName,
                                                String dealerName,
                                                Long[] regionIds,
                                                Long[] sbuIds,
                                                Long storeMemberId,
                                                Boolean isReceived,
                                                String vonderVode,
                                                Pageable pageable,
                                                ModelMap model) {
		Object[] params = { sn, storeId, invoiceStatus, name, // 客户姓名
				contactNumber, // 客户电话
				factoryName, // 责任工厂
				productName, // 产品名称
				wfStatus, // 流程状态
				grade,// 售后等级
				beginTime,//开始时间
				endTime,//结束时间
				processingStatus,//处理状态
				factoryfirstTime,//工厂批复开始时间
				factoryEndTime,//工厂批复结束时间
				category,//地板品类
				province,//省
				saleOrgIds,//机构
				storeName,//经销商名称
				dealerName,//经销商姓名
				regionIds,//区域id
				sbuIds,//sbuId
				storeMemberId,//区域经理名字
				isReceived,//是否到账
				vonderVode//产品编码
		};
		int size = aftersaleService.count(params);
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn,
                                        Long storeId,
                                        String[] invoiceStatus,
                                        String name, // 客户姓名
                                        Long contactNumber, // 客户电话
                                        String factoryName, // 责任工厂
                                        String productName, // 产品名称
                                        Integer[] wfStatus, // 流程状态
                                        String[] grade, // 售后等级
                                        String beginTime,
                                        String endTime,
                                        String[] processingStatus,
                                        String factoryfirstTime,
                                        String factoryEndTime,
                                        String category,
                                        String province,
                                        Long[] saleOrgIds,
                                        String storeName,
                                        String dealerName,
                                        Long[] regionIds,
                                        Long[] sbuIds,
                                        Long storeMemberId,
                                        Boolean isReceived,
                                        String vonderCode,
                                        Pageable pageable, ModelMap model, Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		Object[] params = { sn, storeId, invoiceStatus, name, // 客户姓名
				contactNumber, // 客户电话
				factoryName, // 责任工厂
				productName, // 产品名称
				wfStatus, // 流程状态
				grade,// 售后等级
				beginTime,//开始时间
				endTime,//结束时间
				processingStatus,//处理状态
				factoryfirstTime,//工厂批复开始时间
				factoryEndTime,//工厂批复结束时间
				category,//地板品类
				province,//省
				saleOrgIds,//机构
				storeName,//经销商名称
				dealerName,//经销商姓名
				regionIds,//区域id
				sbuIds,//sbuId
				storeMemberId,//区域经理名字
				isReceived,//是否到账
				vonderCode//产品编码
		};
		List<Map<String, Object>> data = aftersaleService.findList(params, pageable, page, size);
		// 处理数据
		processData(data);
		return getModelAndViewForList(data, model);
	}
	
	private void processData(List<Map<String, Object>> data) {
		List<Filter> filters = new ArrayList<Filter>();
		filters .add(Filter.eq("code", "saleOrgRegion"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,filters,null);
		Map<String,String> saleOrgRegionMap = new HashMap<String, String>();
		for (SystemDict saleOrgRegion : saleOrgRegions) {
			saleOrgRegionMap.put(saleOrgRegion.getId()+"", saleOrgRegion.getValue());
		}
		for (Map<String, Object> map : data) {
			if(map.get("region") != null){
				String region = map.get("region").toString();
				map.put("region_name", saleOrgRegionMap.get(region));
//				if ("1".equals(region)) 
//					map.put("region_name", "南区");
//				else if ("0".equals(region)) 
//					map.put("region_name", "北区");						
			}
			if(map.get("invoice_status") != null){
				String invoiceStatus = map.get("invoice_status").toString();
				if (invoiceStatus.equals("0"))//0已保存 1进行中 2已生效 
					map.put("invoice_statuss", "已保存");
				if (invoiceStatus.equals("1"))
					map.put("invoice_statuss", "进行中");
				if (invoiceStatus.equals("2"))
					map.put("invoice_statuss", "已生效");
			}
			if(map.get("is_received") != null){
				Boolean isReceived = Boolean.valueOf(map.get("is_received").toString());
				if (isReceived)
					map.put("is_received", "已到账");
				else
					map.put("is_received", "未到账");
			}
			if(map.get("is_locale_surveyor") !=null){
				String is_locale_surveyor = map.get("is_locale_surveyor").toString();
				if(is_locale_surveyor.equals("0"))
					map.put("is_locale_surveyors", "是");
				if(is_locale_surveyor.equals("1"))
					map.put("is_locale_surveyors", "否");
			}
			if(map.get("is_deal")!=null){
				String is_deal = map.get("is_deal").toString();
				if(is_deal.equals("0"))
					map.put("is_deals", "否");
				if(is_deal.equals("1"))
					map.put("is_deals", "是");
			}
			if(map.get("is_return") != null ){
				String is_return = map.get("is_return").toString();
				if(is_return.equals("0"))
					map.put("is_returns", "是");
				if(is_return.equals("1"))
					map.put("is_returns", "否");
			}
			if(map.get("satisfaction") != null){
				String satisfaction = map.get("satisfaction").toString();
				if(satisfaction.equals("1"))
					map.put("satisfactions", "非常满意");
				if(satisfaction.equals("2"))
					map.put("satisfactions", "满意");
				if(satisfaction.equals("3"))
					map.put("satisfactions", "一般");
				if(satisfaction.equals("4"))
					map.put("satisfactions", "不满意");
			}
			String areaRegionName = "";
			String areaCityName = "";
			String areaProvinceName = "";
			Area area = null;
            if (map.get("head_new_area") != null) {
                Long areaId = Long.valueOf(map.get("head_new_area").toString());
                area = areaBaseService.find(areaId);
            }
            if (map.get("area_tree_path") != null && area != null) {
                String area_tree_path = map.get("area_tree_path").toString();
                Integer num = (area_tree_path.length() - area_tree_path.replace(",", "").length()) / ",".length();// ,数量
                if (num == 1) {// 省
                	map.put("areaRegionName", areaRegionName);
                	map.put("areaCityName", areaCityName);
                	map.put("areaProvinceName", area.getName() == null ? "" : area.getName());
                } else if (num == 2) {// 省市
                	map.put("areaRegionName", areaRegionName);
                	map.put("areaCityName", area.getName() == null ? "" : area.getName());
                	map.put("areaProvinceName", area.getParent().getName() == null ? "" : area.getParent().getName());
                } else if (num == 3) {// 省市区
                	map.put("areaRegionName", area.getName() == null ? "" : area.getName());
                	map.put("areaCityName", area.getParent().getName() == null ? "" : area.getParent().getName());
                    map.put("areaProvinceName", area.getParent().getParent().getName() == null ? ""
                            : area.getParent().getParent().getName());
                }
            } else {// 不填
            	map.put("areaRegionName", areaRegionName);
            	map.put("areaCityName", areaCityName);
            	map.put("areaProvinceName", areaProvinceName);
            }
            String price = "";
            String areas = "";
            if(map.get("store_partial_replacement") != null){
            	price = map.get("store_partial_replacement").toString();
            }
            if(map.get("storeReturnGoods") != null){
            	price = map.get("storeReturnGoods").toString();
            }
            if(map.get("fd_partial_replacement") != null){
            	areas = map.get("fd_partial_replacement").toString();
            }
            if(map.get("fd_return_goods")!= null){
            	areas = map.get("fd_return_goods").toString();
            }
            if(map.get("store_treatment_scheme") != null){
            	String storeTreatmentScheme = map.get("store_treatment_scheme").toString();
            	if(storeTreatmentScheme.contains("全部更换")&&map.get("laying_area") != null)
            		price = map.get("laying_area").toString();
            }
            if(map.get("fd") != null){
            	String fd = map.get("fd").toString();
            	if(fd.contains("全部更换")&&map.get("laying_area") != null)
            		areas = map.get("laying_area").toString();
            }
            map.put("prices", price);
            map.put("areas", areas);
			map.put("reserved", "是");	
		
		}
	}

	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = {
				"处理月份","申请单号","单据状态","创建时间","工厂批复时间","顾客姓名","顾客电话","经销商姓名","经销商电话",
				"工厂","sbu","机构","区域","区域经理","省份","地级城市","区县城市","详细地址","现场照片","销售单据",
				"地板品类","产品名称","产品编码","产品型号","规格","批次号","板底喷码","安装面积㎡",
				"故障名称","经销商描述故障信息","经销商处理方案","面积","赔偿","赔偿用户","其他费用",
				"是否勘察","勘察时间","勘察结果","责任人",
				"回访时间","调查信息","责任人","责任工厂","是否处理","是否 退货","工厂处理方案","面积","赔偿",
				"激励价","地板金额","平台价","地板金额","回访结果","回访时间","处理满意度","是否到账"
		};

		// 设置单元格取值
		String[] properties = {
				"end_month",//处理月份
				"sn",//申请单号
				"invoice_statuss",//单据状态
				"create_date",//创建时间
				"end_time",//工厂批复时间
				"name",//顾客姓名
				"contact_number",//顾客电话
				"dealer_name",//经销商姓名
				"head_phone",//经销商电话
				"factory_name",//工厂
				"sbu_name",//sbu
				"sale_org_name",//机构
				"region_name",//区域
				"region_manager",//区域经理
				"areaProvinceName",//省份
				"areaCityName",//地级城市
				"areaRegionName",//区县城市
				"address",//详细地址
				"reserved",//现场照片
				"reserved",//销售单据
				"category",//地板品类
				"product_name",//产品名称
				"vonder_code",//产品编码
				"model",//产品型号
				"spec",//规格
				"batch_number",//批次号
				"spurt_code",//板底喷码
				"laying_area",//安装面积㎡
				"fault_name",//故障名称
				"store_fault_message",//经销商描述故障信息
				"store_treatment_scheme",//经销商处理方案
				"prices",//面积
				"store_compensation",//赔偿
				"reparation_user",//赔偿用户
				"other_expenses",//其他费用
				"is_locale_surveyors",//是否勘察
				"zbsurveyor_date",//勘察时间
				"survey_description",//勘察结果
				"surveyor_name",//责任人
				"review_date",//回访时间
				"dispose_scheme",//调查信息
				"node_approver",//责任人
				"factory_name",//责任工厂
				"is_deals",//是否处理
				"is_returns",//是否 退货
				"fd",//工厂处理方案
				"areas",//面积
				"fd_compensation",//赔偿
				"factory_price",//激励价
				"amount",//地板金额
				"platform_price",//平台价
				"platform_amount",//地板金额
				"review_result",//回访结果
				"review_date",//回访时间
				"satisfactions",//处理满意度\
				"is_received"//是否到账
		};
		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				 25 * 256, 25 * 256, 25 * 256, 25 * 256,25 * 256, 25 * 256,25 * 256, 25 * 256,};

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

    /**
     * 作废
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg cancel(Long id) {
        if (id == null ) {
            return error("请选择单据");
        }
        Aftersale aftersale = aftersaleService.find(id);
        if(aftersale.getWfId()!=null){
        	return error("操作失败：单据流程已发起请中断后再试！");
        }

        if(aftersale.getInvoiceStatus().equals("99")) return error("请不要重复作废单据");
		aftersale.setInvoiceStatus("99");
		aftersaleService.update(aftersale);
        return success();
    }
    
    /**
     * 查询订单经理 
     */
    @RequestMapping(value = "/select_order_manager", method = RequestMethod.GET)
    public String selectOrderManager(String[] postcode, Model model) {
    	if(postcode!=null){
    		List<String> codes = Arrays.asList(postcode);
    		model.addAttribute("postcode", codes);    		
    	}
        return "/aftersales/aftersale/select_order_manager";
    }

    /**
     * 设计师列表数据
     */
    @RequestMapping(value = "/select_order_manager_data", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg selectOrderManagerData(String[] postcode, String name, Pageable pageable) {
        String json = JsonUtils.toJson(storeMemberSaleOrgPostService.findOrderManager(postcode,name, pageable));
        return success(json);
    }
    
    /**
     * 查询财务数据 
     */
    @RequestMapping(value = "/select_financial_staff", method = RequestMethod.GET)
    public String selectFinancialStaff(String[] postcode, Long id, Model model) {
    	model.addAttribute("id", id); 
    	if(postcode!=null){
    		List<String> codes = Arrays.asList(postcode);
    		model.addAttribute("postcode", codes);
    	}
        return "/aftersales/aftersale/select_financial_staff";
    }
    
    /**
     * 财务数据
     */
    @RequestMapping(value = "/select_financial_staff_data", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg selectFinancialStaffData(String name, Long id, Pageable pageable) {
    	Aftersale aftersale = aftersaleService.find(id);
    	SaleOrg saleOrg = null;
    	if(!"吴江居然家".equals(aftersale.getFactoryName())) {
    		saleOrg = aftersale.getSaleOrg();
    	}
        String json = JsonUtils.toJson(storeMemberSaleOrgPostService.findStoreMemberByPost(new String[] {"3001"},name,saleOrg, pageable));
        return success(json);
    }
    
    @RequestMapping(value = "/select_organization", method = RequestMethod.GET)
	public String selectOrganization(Pageable pageable, ModelMap model) {
		return "/aftersales/aftersale/select_organization";
	}

	@RequestMapping(value = "/select_organization_data", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg selectOrganizationData(Pageable pageable) {
		Page<Map<String, Object>> page = aftersaleService.findOrganizationPage(pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
    
    
    /**
	 * 更新时间
	 */
	@RequestMapping(value = "/update_time", method = RequestMethod.POST)
	public @ResponseBody
    ResultMsg update_time(Aftersale aftersale) {
		Aftersale a = aftersaleService.find(aftersale.getId());
		a.setDecorationTime(aftersale.getDecorationTime());
		a.setLayingTime(aftersale.getLayingTime());
		a.setCheckInTime(aftersale.getCheckInTime());
		a.setDownTime(aftersale.getDownTime());
		aftersaleService.update(a);
		return success().addObjX(aftersale.getId());
	}

	@RequestMapping(value = "/select_azd", method = RequestMethod.GET)
	public String selectStore(Long storeId, ModelMap model) {
		if(storeId == null){
			ExceptionUtil.throwControllerException("请选择经销商!");
		}
		model.addAttribute("storeId", storeId);
		return "/aftersales/aftersale/select_azd";
	}
	/**
	 * 查询5期安装单数据
	 */
	@RequestMapping(value = "/select_azd_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectStoreData(Long storeId, String contactPhone, String memberName, Pageable pageable, ModelMap model) {
		Map<String,Object> map = new HashMap<String, Object>();
		Map<String,Object> page = new HashMap<String, Object>();
		page.put("current", pageable.getPageNumber());
		page.put("size", pageable.getPageSize());
		Map<String,Object> search = new HashMap<String, Object>();
		search.put("integrate", storeId);
		if(StringUtils.isNotEmpty(memberName)) {
			search.put("memberName", memberName);
		}
		if(StringUtils.isNotEmpty(contactPhone)) {
			search.put("contactPhone", contactPhone);
		}
		map.put("page", page);
		map.put("search", search);
		String requestStr = JsonUtils.toJson(map);
		try {
			Page<Map<String, Object>> data = aftersaleService.getLink5Gds(requestStr);
			String jsonPage = JsonUtils.toJson(data);
			return success(jsonPage);
		} catch (Exception e) {
			LogUtils.error(e);
			e.printStackTrace();
			return error("查询错误");
		}
	}
	/**
	 * 安装单详情
	 */
	@RequestMapping(value = "/azd", method = RequestMethod.GET)
	public String azd(Long id, ModelMap model) {
		if(id == null){
			return "";
		}
		try {
			Map<String, Object> data = aftersaleService.getLink5Gd(id);
			model.addAttribute("data", data);
			List<Map<String, Object>> itemList = (ArrayList<Map<String, Object>>) data.get("itemList");
			List<Map<String, Object>> zcqd = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> flqd = new ArrayList<Map<String, Object>>();
			List<Map<String, Object>> fyqd = new ArrayList<Map<String, Object>>();
			for (Map<String, Object> map : itemList) {
				if(map.get("productType") != null) {
					String productType = map.get("productType").toString();
					if ("1".equals(productType)) {
						zcqd.add(map);
					}else if ("2".equals(productType)) {
						flqd.add(map);
					}else if ("3".equals(productType)) {
						fyqd.add(map);
					}
				}
			}
			model.addAttribute("zcqd", JsonUtils.toJson(zcqd));
			model.addAttribute("flqd", JsonUtils.toJson(flqd));
			model.addAttribute("fyqd", JsonUtils.toJson(fyqd));

			//附件
			List<Map<String, Object>> attachmentVoList = (ArrayList<Map<String, Object>>) data.get("attachmentVoList");
			if(!attachmentVoList.isEmpty()){
				Map<String, Object> attachmentVo = attachmentVoList.get(0);
				List<Map<String, Object>> childs = (ArrayList<Map<String, Object>>) attachmentVo.get("childs");
				List<Map<String, Object>> scfc = new ArrayList<Map<String, Object>>();//双重防潮
				List<Map<String, Object>> pem = new ArrayList<Map<String, Object>>();//PE膜及地垫胶带粘牢
				List<Map<String, Object>> fcm = new ArrayList<Map<String, Object>>();//防潮膜上扬5公分
				List<Map<String, Object>> mkgd = new ArrayList<Map<String, Object>>();//门口隔断
				List<Map<String, Object>> qbyl = new ArrayList<Map<String, Object>>();//墙边预留伸缩缝
				List<Map<String, Object>> cppc = new ArrayList<Map<String, Object>>();//产品批次号/板底字轮
				for (Map<String, Object> map : childs) {
					if(map.get("listOfKeyDict") != null) {
						List<Map<String, Object>> itemVos = (ArrayList<Map<String, Object>>) map.get("itemVos");
						String listOfKeyDict = map.get("listOfKeyDict").toString();
						if ("1".equals(listOfKeyDict)) {
							scfc.addAll(itemVos);
						}else if ("2".equals(listOfKeyDict)) {
							pem.addAll(itemVos);
						}else if ("3".equals(listOfKeyDict)) {
							fcm.addAll(itemVos);
						}else if ("4".equals(listOfKeyDict)) {
							mkgd.addAll(itemVos);
						}else if ("5".equals(listOfKeyDict)) {
							qbyl.addAll(itemVos);
						}else if ("6".equals(listOfKeyDict)) {
							cppc.addAll(itemVos);
						}
					}
				}
				model.addAttribute("scfc", JsonUtils.toJson(scfc));
				model.addAttribute("pem", JsonUtils.toJson(pem));
				model.addAttribute("fcm", JsonUtils.toJson(fcm));
				model.addAttribute("mkgd", JsonUtils.toJson(mkgd));
				model.addAttribute("qbyl", JsonUtils.toJson(qbyl));
				model.addAttribute("cppc", JsonUtils.toJson(cppc));
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return "/aftersales/aftersale/azd";
	}
}
