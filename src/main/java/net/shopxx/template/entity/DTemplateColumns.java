package net.shopxx.template.entity;
import java.util.List;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.SystemDict;
/**
 * Entity - 模板列
 */
@Entity
@Table(name = "d_template_columns")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "d_template_columns_sequence")
public class DTemplateColumns extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8130125219839212673L;
	
	/**列名称*/
	private String name;
	
	/**显示名称*/
	private String showName;
	
	/**模板*/
	private DTemplates dTemplates;
	
	/**是否显示*/
	private Boolean isShowed;
	
	/**是否允许选择*/
	private Boolean isSelected;
	
	/**列类型*/
	private SystemDict columnsType;
	
	/**是否允许多选*/
	private Boolean isMultSelected;
	
	/**是否允许导出*/
	private Boolean isExport;
	
	/**是否只读*/
	private Boolean isReadonly;
	
	/**链接*/
	private String link;
	
	/**是否默认*/
	private Boolean isDefault;
	
	/**是否列表，数据集*/
	private Boolean isList;
	
	/**父级*/
	private DTemplateColumns parentDTemplateColumns;
	
	/**校验类型*/
	private SystemDict checkedType;
	
	/**是否校验*/
	private Boolean isChecked;
	
	/**是否系统字段*/
	private Boolean isSysColumn;
	
	/**排序*/
	private Integer orderby;
	
	/**备注*/
	private String memo;
	
	/**用户列*/
	private List<DUserColumns> dUserColumnsList;


	// 2020/7/15新增字段
    /**最大宽度**/
    private Integer maximumWidth;

    /**链接图标**/
    private String linkIcon;

    /**链接方法名**/
    private String linkMethodName;

    /**弹框类型**/
    private SystemDict popUpType;

    /**弹框模板**/
    private String popUpTemplate;

    /**执行筛选**/
    private SystemDict screenType;

    /**词汇编码**/
    private String wordCoding;

    /**多值范围搜索对应id**/
    private String field;

    /**是否必选条件*/
    private Boolean requiredCondition;

    /**父级筛选字段**/
    private String parentParamName;

    /**控件是否默认值**/
    private Boolean controlIsDefault;

    /**是否默认筛查 默认显示过滤条件**/
    private Boolean defaultScreening;

    /**用户参数*/
    private List<DUserParameters> dUserParametersList;

    /**控制显示的系统参数编码*/
    private  String systemParameterValue;

    @Column(name = "name",nullable=false)
	public String getName() {
		return name;
	}


	@Column(name = "show_name",nullable=false)
	public String getShowName() {
		return showName;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public DTemplates getdTemplates() {
		return dTemplates;
	}

    @Column(name = "is_showed",nullable=false)
	public Boolean getIsShowed() {
		return isShowed;
	}

	@Column(name = "is_selected",nullable=false)
	public Boolean getIsSelected() {
		return isSelected;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getColumnsType() {
		return columnsType;
	}

    @Column(name = "is_mult_selected",nullable=false)
	public Boolean getIsMultSelected() {
		return isMultSelected;
	}

    @Column(name = "is_export",nullable=false)
	public Boolean getIsExport() {
		return isExport;
	}

	@Column(name = "is_readonly",nullable=false)
	public Boolean getIsReadonly() {
		return isReadonly;
	}

	public String getLink() {
		return link;
	}

	@Column(name = "is_default",nullable=false)
	public Boolean getIsDefault() {
		return isDefault;
	}

	@Column(name = "is_list",nullable=false)
	public Boolean getIsList() {
		return isList;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public DTemplateColumns getParentDTemplateColumns() {
		return parentDTemplateColumns;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getCheckedType() {
		return checkedType;
	}

    @Column(name = "is_checked",nullable=false)
	public Boolean getIsChecked() {
		return isChecked;
	}

	@Column(name = "is_sys_column",nullable=false)
	public Boolean getIsSysColumn() {
		return isSysColumn;
	}

	@Column(name = "orderby",nullable=false)
	public Integer getOrderby() {
		return orderby;
	}

	public String getMemo() {
		return memo;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public void setdTemplates(DTemplates dTemplates) {
		this.dTemplates = dTemplates;
	}

	public void setIsShowed(Boolean isShowed) {
		this.isShowed = isShowed;
	}

	public void setIsSelected(Boolean isSelected) {
		this.isSelected = isSelected;
	}

	public void setColumnsType(SystemDict columnsType) {
		this.columnsType = columnsType;
	}

	public void setIsMultSelected(Boolean isMultSelected) {
		this.isMultSelected = isMultSelected;
	}

	public void setIsExport(Boolean isExport) {
		this.isExport = isExport;
	}

	public void setIsReadonly(Boolean isReadonly) {
		this.isReadonly = isReadonly;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public void setIsDefault(Boolean isDefault) {
		this.isDefault = isDefault;
	}

	public void setIsList(Boolean isList) {
		this.isList = isList;
	}

	public void setParentDTemplateColumns(DTemplateColumns parentDTemplateColumns) {
		this.parentDTemplateColumns = parentDTemplateColumns;
	}

	public void setCheckedType(SystemDict checkedType) {
		this.checkedType = checkedType;
	}

	public void setIsChecked(Boolean isChecked) {
		this.isChecked = isChecked;
	}

	public void setIsSysColumn(Boolean isSysColumn) {
		this.isSysColumn = isSysColumn;
	}

	public void setOrderby(Integer orderby) {
		this.orderby = orderby;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
	
	@JsonIgnore
	@OneToMany(mappedBy = "dTemplateColumns", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<DUserColumns> getdUserColumnsList() {
		return dUserColumnsList;
	}

	public void setdUserColumnsList(List<DUserColumns> dUserColumnsList) {
		this.dUserColumnsList = dUserColumnsList;
	}

    public Integer getMaximumWidth() {
        return maximumWidth;
    }

    public void setMaximumWidth(Integer maximumWidth) {
        this.maximumWidth = maximumWidth;
    }

    public String getLinkIcon() {
        return linkIcon;
    }

    public void setLinkIcon(String linkIcon) {
        this.linkIcon = linkIcon;
    }

    public String getLinkMethodName() {
        return linkMethodName;
    }

    public void setLinkMethodName(String linkMethodName) {
        this.linkMethodName = linkMethodName;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getPopUpType() {
        return popUpType;
    }

    public void setPopUpType(SystemDict popUpType) {
        this.popUpType = popUpType;
    }

    public String getPopUpTemplate() {
        return popUpTemplate;
    }

    public void setPopUpTemplate(String popUpTemplate) {
        this.popUpTemplate = popUpTemplate;
    }

    @JsonIgnore
    @OneToMany(mappedBy = "dTemplateColumns", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    public List<DUserParameters> getdUserParametersList() {
        return dUserParametersList;
    }

    public void setdUserParametersList(List<DUserParameters> dUserParametersList) {
        this.dUserParametersList = dUserParametersList;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getScreenType() {
        return screenType;
    }

    public void setScreenType(SystemDict screenType) {
        this.screenType = screenType;
    }

    public String getWordCoding() {
        return wordCoding;
    }

    public void setWordCoding(String wordCoding) {
        this.wordCoding = wordCoding;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    @Column(name = "required_condition",nullable=false)
    public Boolean getRequiredCondition() {
        return requiredCondition;
    }

    public void setRequiredCondition(Boolean requiredCondition) {
        this.requiredCondition = requiredCondition;
    }

    public String getParentParamName() {
        return parentParamName;
    }

    public void setParentParamName(String parentParamName) {
        this.parentParamName = parentParamName;
    }

	@Column(name = "control_is_default",nullable=false)
	public Boolean getControlIsDefault() {
		return controlIsDefault;
	}

	public void setControlIsDefault(Boolean controlIsDefault) {
		this.controlIsDefault = controlIsDefault;
	}

    @Column(name = "default_screening",nullable=false)
    public Boolean getDefaultScreening() {
        return defaultScreening;
    }

    public void setDefaultScreening(Boolean defaultScreening) {
        this.defaultScreening = defaultScreening;
    }

	public String getSystemParameterValue() {
		return systemParameterValue;
	}

	public void setSystemParameterValue(String systemParameterValue) {
		this.systemParameterValue = systemParameterValue;
	}
}
