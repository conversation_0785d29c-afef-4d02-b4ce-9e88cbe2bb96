package net.shopxx.template.dao;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.dao.impl.DaoCenter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("dTemplatesDao")
public class DTemplatesDao extends DaoCenter{

     /**
      * 拼接sql
      */
     public void neaten(Long modelId, String tempName, Boolean isEnable,
    		 List<Object> list,StringBuilder sql,Long parentdmodulesId,String modelName,String creatDate){

          Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
          sql.append(" SELECT temp.*,modu.name moduName ");
          sql.append(" FROM d_modules modu ");
          sql.append(" INNER JOIN d_templates temp ON temp.d_modules = modu.id ");
          sql.append(" LEFT JOIN (SELECT dm.id FROM d_modules dm  WHERE dm.parentdmodules IS NULL) AS dms ON dms.id = modu.parentdmodules ");
          sql.append(" WHERE 1=1 ");

          if (!ConvertUtil.isEmpty(companyInfoId)) {
               sql.append(" and modu.company_info_id = ? ");
               list.add(companyInfoId);
          }
          //是否启用
          if (!ConvertUtil.isEmpty(isEnable)) {
               sql.append(" and temp.is_enable = ? ");
               list.add(isEnable);
          }
          //模板名称
          if(!ConvertUtil.isEmpty(tempName)){
               sql.append(" and temp.name like ? ");
               list.add("%" + tempName + "%");
          }
          //模块名称
          if(!ConvertUtil.isEmpty(modelName)){
               sql.append(" and modu.name like ? ");
               list.add("%" + modelName + "%");
          }
          //创建时间
          if(!ConvertUtil.isEmpty(creatDate)){
               sql.append(" and temp.create_date like ? ");
               list.add("%" + creatDate + "%");
          }
          //模块Id
          if (!ConvertUtil.isEmpty(modelId)) {
               sql.append(" and modu.id = ? ");
               list.add(modelId);
          }
          //模板父级Id
          if(!ConvertUtil.isEmpty(parentdmodulesId)){
        	  sql.append(" and dms.id = ? ");
              list.add(parentdmodulesId);
          }

          
     }

     /**
      *
      * @param modelId
      * @param tempName
      * @param isEnable
      * @return
      */
     public Page<Map<String, Object>> findTemplatesByModel(Long modelId, 
    		 String tempName,Boolean isEnable,Long parentdmodulesId,
    		 Pageable pageable,String modelName,String creatDate){
    	 
          List<Object> objList = new ArrayList<Object>();
          StringBuilder sql = new StringBuilder();
          this.neaten(modelId,tempName,isEnable,objList,sql,parentdmodulesId,modelName,creatDate);
          Object[] objs = new Object[objList.size()];
          for (int i = 0; i < objList.size(); i++) {
               objs[i] = objList.get(i);
          }
          Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
          return page;
     }

}
