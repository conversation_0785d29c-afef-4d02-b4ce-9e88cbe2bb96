package net.shopxx.template.dao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.template.service.DTemplateParametersService;
import net.shopxx.util.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;

import javax.annotation.Resource;

@Repository("dTemplateColumnsDao")
public class DTemplateColumnsDao extends DaoCenter{

    private static final Logger logger = LoggerFactory.getLogger(DTemplateColumnsDao.class);

    @Resource(name = "dTemplateParametersServiceImpl")
    private DTemplateParametersService dTemplateParametersService;


    public void neaten(Long templatesId,List<Object> list,StringBuilder sql) {
		
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append(" SELECT dtc.*,ct.value columns_name,cte.value checked_name ");
		sql.append(" FROM d_templates dt ");
		sql.append(" INNER JOIN d_template_columns dtc ON dt.id = dtc.d_templates ");
		sql.append(" LEFT JOIN xx_system_dict ct ON dtc.columns_type = ct.id ");
		sql.append(" LEFT JOIN xx_system_dict cte ON dtc.checked_type = cte.id ");
		sql.append(" WHERE 1=1 ");
		
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and dt.company_info_id = ? ");
			list.add(companyInfoId);
		}
		if(!ConvertUtil.isEmpty(templatesId)){
			sql.append(" and dt.id = ? ");
			list.add(templatesId);
		}
		
		sql.append(" order by dtc.orderby ");
	
	}
	
	
	
	public Page<Map<String, Object>> findPage(Long dTemplatesId,
			Pageable pageable) {
		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		
		this.neaten(dTemplatesId,list,sql);
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		
		return page;
	}
	
	
	public List<Map<String, Object>> findDTemplateColumnsList(Long templatesId) {
		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		
		this.neaten(templatesId, list, sql);
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return mapList;
	}



	public List<Map<String,Object>> findColumnsInfoToExport(Long templatesId){
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT " +
                " t.`name`, " +
                " t.show_name, " +
                " t.maximum_width, " +
                " t.orderby, " +
                " CASE is_default WHEN 1 THEN '是' ELSE '否' end is_default, " +
                " CASE default_screening WHEN 1 THEN '是' ELSE '否' end default_screening, " +
                " ct.`value` columns_type, " +
                " t.memo, " +
                " CASE is_showed WHEN 1 THEN '是' ELSE '否' end is_showed, " +
                " CASE required_condition WHEN 1 THEN '是' ELSE '否' end required_condition, " +
                " CASE is_selected WHEN 1 THEN '是' ELSE '否' end is_selected, " +
                " CASE is_mult_selected WHEN 1 THEN '是' ELSE '否' end is_mult_selected, " +
                " CASE is_export WHEN 1 THEN '是' ELSE '否' end is_export, " +
                " CASE is_readonly WHEN 1 THEN '是' ELSE '否' end is_readonly, " +
                " CASE is_list WHEN 1 THEN '是' ELSE '否' end is_list, " +
                " CASE is_sys_column WHEN 1 THEN '是' ELSE '否' end is_sys_column, " +
                " CASE is_checked WHEN 1 THEN '是' ELSE '否' end is_checked, " +
                " checkT.`value` checked_type, " +
                " put.`value` pop_up_type, " +
                " CASE control_is_default WHEN 1 THEN '是' ELSE '否' end control_is_default, " +
                " st.`value` screen_type, " +
                " word_coding, " +
                " dt.`name` pop_up_template, " +
                " t.`field`, " +
                " parent_param_name, " +
                " link_method_name, " +
                " link, " +
                " link_icon " +
                " FROM " +
                " d_template_columns t  " +
                "LEFT JOIN xx_system_dict ct ON t.columns_type = ct.id  " +
                "LEFT JOIN xx_system_dict checkT ON t.checked_type = checkT.id  " +
                "LEFT JOIN xx_system_dict put ON t.pop_up_type = put.id  " +
                "LEFT JOIN xx_system_dict st ON t.screen_type = st.id  " +
                "LEFT JOIN d_templates dt ON t.pop_up_template = dt.id "+
                "WHERE " +
                " t.d_templates = " +templatesId+
                " ORDER BY " +
                " t.id");
        List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),
                null,
                0);
        return mapList;
    }


    public List<Map<String, Object>> findColumnsInfoByTemplateId(Long templatesId,Long userId,
    		Boolean isShowed,Boolean isDefault) {

        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append(" select dtc.id,dtc.name,dtc.show_name,dtc.default_screening defaultScreening,dtc.memo dtcMemo,dtc.required_condition,duc.id ducId,sxd.remark columns_type,sxd.`value`,dtc.maximum_width maxWidth,dup.id paramId,dtc.link_method_name,dtc.system_parameter_value  ");
        sql.append(" from d_template_columns dtc ");
        sql.append(" left join d_template_users dtu on dtc.d_templates = dtu.d_templates and dtu.store_member = '"+userId+"' ");
        sql.append(" left join d_user_columns duc on duc.d_template_users = dtu.id and dtc.id = duc.d_template_columns ");
        sql.append(" left join xx_system_dict sxd on sxd.id = dtc.columns_type ");
        sql.append(" left join d_user_parameters dup on dup.d_template_columns = dtc.id and dup.d_template_users = dtu.id ");
        sql.append(" where 1=1 ");
        
        if(!ConvertUtil.isEmpty(companyInfoId)){
            sql.append(" and dtc.company_info_id = ? ");
            list.add(companyInfoId);
        }
        if(!ConvertUtil.isEmpty(templatesId)){
            sql.append(" and dtc.d_templates = ? ");
            list.add(templatesId);
        }
        if(!ConvertUtil.isEmpty(isShowed)){
        	sql.append(" and dtc.is_showed = ? ");
            list.add(isShowed);
        }
        if(!ConvertUtil.isEmpty(isDefault)){     //加载默认列表的时候使用模板字段排序
            sql.append(" and dtc.is_default = ? ");
            list.add(isDefault);
            sql.append(" group by dtc.id order by dtc.orderby ");
        }else {      //加载配置字段使用用户列排序
            sql.append(" group by dtc.id order by duc.orderby,dtc.orderby");
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),
                objs,
                0);

        return mapList;
    }

    /**
     * 获取弹框模板
     * @return
     */
    public List<Map<String, Object>> findFrameTemplate(Long templateId){
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append("select dtc.id tColumnId,dtc.field,dtc.parent_param_name parentParamName,screen.id screenId," +
                "screen.`value` screenType,screen.`remark` screenRemark,`name`,show_name,link,link_icon,maximum_width," +
                "put.id putId,put.`value`,put.remark pop_up_code,sxd.remark columns_type,dtc.required_condition  " +
                " from d_template_columns dtc left join xx_system_dict put on put.id = dtc.pop_up_type ");
        sql.append(" left join xx_system_dict screen on screen.id = dtc.screen_type left join xx_system_dict sxd on sxd.id = dtc.columns_type ");
        sql.append(" where dtc.is_default is true and dtc.is_showed is true  ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
            sql.append(" and dtc.company_info_id = ? ");
            list.add(companyInfoId);
        }
        if(!ConvertUtil.isEmpty(templateId)){
            sql.append(" and dtc.d_templates = ? ");
            list.add(templateId);
        }
        sql.append(" order by dtc.orderby ");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),
                objs,
                0);
        return mapList;
    }


    /**
     * 获取指定模板的弹框数据
     * @param jsonParam  筛选参数
     * @param templateId  模板id
     * @param pageable
     * @return
     */
    public Page<Map<String, Object>> findPopUpTemplateData(String jsonParam, Long templateId,Pageable pageable) {
        List<Object> objList = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();

            //拼接sql
            splicingPopUpTemplateSql(sql,templateId);
            //替换默认参数参数
            Boolean result = dTemplateParametersService.verifyTemplateParameters(sql,jsonParam,templateId);
            if (!result){
                ExceptionUtil.throwServiceException("参数校验不通过,请检查必填或参数格式!");
            }
            /**添加筛选**/
            //模板参数
            List<Map<String, Object>> mapList = findFrameTemplate(templateId);
            JSONObject jsonObj = CommonUtil.processingJSON(jsonParam);
            dTemplateParametersService.splicingScreening(sql,jsonObj,mapList);
        try{
            logger.info("获取弹框模板列Sql:"+sql.toString());     //打印sql 到info文件
            Object[] objs = new Object[objList.size()];
            for (int i = 0; i < objList.size(); i++) {
                objs[i] = objList.get(i);
            }

            Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
            return page;
        }catch (Exception e){
            logger.error("获取弹框模板列异常:"+sql.toString(),e);
            return null;
        }
    }


    /**
     * 拼接弹框模板sql
     * @param sql
     * @param templateId
     */
    public void splicingPopUpTemplateSql(StringBuilder sql,Long templateId){
        try{
            StringBuilder columns = new StringBuilder();
            //获取查询字段
            List<Map<String, Object>> columnsLists = findFrameTemplate(templateId);
            //拼接字段
            for(Map<String, Object> columnsList : columnsLists){
                columns.append(columnsList.get("name")+",");
            }
            //注意：id加最后不用去逗号，调整顺序会影响
            columns.append("id");
            //获取执行sql
            String surface = getExecuteByTemplateId(templateId);
            if (ConvertUtil.isEmpty(surface)){
                ExceptionUtil.throwServiceException("请先添加模板执行sql！");
            }
            sql.append("select ").append(columns.toString()).append(" from (").append(surface);
            sql.append(") v where 1=1 ");
        }catch (Exception e){
            logger.error("拼接弹框模板sql："+sql.toString(),e);
        }
    }

    //获取模板执行sql
    public String getExecuteByTemplateId(Long templateId){
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        sql.append("select execute from d_templates where 1 = 1 ");
        if(!ConvertUtil.isEmpty(templateId)){
            sql.append(" and id = ? ");
            list.add(templateId);
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        String resultSql = getNativeDao().findString(sql.toString(),objs);
        return resultSql;
    }

    /**
     * 获取模板数据
     * @param templatesId
     * @param name
     * @return
     */
    public List<Map<String,Object>> findDataByTempLata(Long templatesId,String name,String nameValue){
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

            //获取执行sql
            String surface = getExecuteByTemplateId(templatesId);
            if (ConvertUtil.isEmpty(surface)){
                ExceptionUtil.throwServiceException("请先添加模板执行sql！");
            }
            sql.append("select id,").append(name).append(" from (").append(surface);
            sql.append(") v where 1=1 ");
            //替换默认参数参数
            Boolean result = dTemplateParametersService.verifyTemplateParameters(sql,null,templatesId);
            if (!result){
                ExceptionUtil.throwServiceException("参数校验不通过,请检查必填或参数格式!");
            }
        try{
            if (!ConvertUtil.isEmpty(companyInfoId)){
                sql.append(" and company_info_id = ? ");
                list.add(companyInfoId);
            }
            if (!ConvertUtil.isEmpty(name)){
                sql.append(" and ").append(name).append(" like ?");
                list.add("%"+nameValue+"%");
            }
            Object[] objs = new Object[list.size()];
            for (int i = 0; i < list.size(); i++) {
                objs[i] = list.get(i);
            }
            logger.info("模糊匹配数据sql："+sql.toString());
            List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),
                    objs,
                    0);
            return mapList;
        }catch (Exception e){
            logger.error("获取数据异常:"+sql.toString(),e);
            return null;
        }

    }
}
