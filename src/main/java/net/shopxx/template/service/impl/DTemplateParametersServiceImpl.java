package net.shopxx.template.service.impl;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import net.shopxx.base.core.entity.Principal;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.shop.service.StoreMemberService;
import net.shopxx.template.dao.DUserParametersDao;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.template.dao.DTemplateParametersDao;
import net.shopxx.template.entity.DTemplateParameters;
import net.shopxx.template.service.DTemplateParametersService;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Service("dTemplateParametersServiceImpl")
public class DTemplateParametersServiceImpl extends BaseServiceImpl<DTemplateParameters> implements DTemplateParametersService{

    private final static String PARAMETER_IN = "IN";          //参数校验类型 输入
    private final static String PARAMETER_OUT = "OUT";          //参数校验类型 输出
    private final static String IS_CHECK = "1";  //需要校验

    private static final Logger logger = LoggerFactory.getLogger(DTemplateParametersServiceImpl.class);

	@Resource(name = "dTemplateParametersDao")
	private DTemplateParametersDao dTemplateParametersDao;
	@Resource(name = "dUserParametersDao")
	private DUserParametersDao dUserParametersDao;

    @Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "storeMemberServiceImpl")
    private StoreMemberService storeMemberService;


	@Override
	public Page<Map<String, Object>> findPage(Long dTemplatesId, Pageable pageable) {
		return dTemplateParametersDao.findPage(dTemplatesId, pageable);
	}

	@Override
	public List<Map<String, Object>> findDTemplateParametersList(Long dTemplatesId) {
		return dTemplateParametersDao.findDTemplateParametersList(dTemplatesId);
	}


    /**
     * json中添加参数
     * @param jsonObject
     */
    public JSONObject addParameter(JSONObject jsonObject){
        //获取系统参数并筛值
        //用户Id
        Long userId = WebUtils.getCurrentStoreMemberId();
        jsonObject.put(CommonUtil.$userId,userId);

        //组织主体
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        jsonObject.put(CommonUtil.$companyInfoId,companyInfoId);

        //sbu
        String sbuIds = roleJurisdictionUtil.getSbuIds();
        jsonObject.put(CommonUtil.$sbuId,sbuIds);

        //经营组织
        String organizationIds = roleJurisdictionUtil.getOrganizationIds();
        jsonObject.put(CommonUtil.$organizationId,organizationIds);
        //机构
        String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
        jsonObject.put(CommonUtil.$saleOrgId,saleOrgIds);

        //仓库
        String warehouseIds = roleJurisdictionUtil.getWarehouseIds();
        jsonObject.put(CommonUtil.$warehouseIds,warehouseIds);

        //仓库库存
        String warehouseStockIds = roleJurisdictionUtil.getWarehouseStockIds();
        jsonObject.put(CommonUtil.$warehouseStockIds,warehouseStockIds);

        //用户username 创建人
        StoreMember storeMember = storeMemberService.find(userId);
        jsonObject.put(CommonUtil.$username,storeMember.getUsername());

        //客户
        String storeIds = roleJurisdictionUtil.getStores();
        jsonObject.put(CommonUtil.$storeIds,storeIds);

        return jsonObject;
    }

    @Override
    public Boolean verifyTemplateParameters(StringBuilder sql, String jsonParam, Long dTemplatesId) {
	    Boolean flag = true;
	    String sqlValue = sql.toString();
        try{
            //解析并获取参数
            JSONObject jsonObject = CommonUtil.processingJSON(jsonParam);
            jsonObject = addParameter(jsonObject);
	        //获取模板参数
            List<Map<String, Object>> mapList = dTemplateParametersDao.findDTemplateParametersList(dTemplatesId);
            if(mapList!=null && mapList.size() > 0){
                for (Map<String, Object> map : mapList){
                    //参数类型													 //获取参数名
                    if (!PARAMETER_IN.equals(map.get("parameters_type_name")) || ConvertUtil.isEmpty(map.get("name"))){
                        continue;
                    }
                    //获取系统参数名
                    String parameters_name = map.get("name").toString();
                    //获取参数值
                    String parameters_value = jsonObject.getString(parameters_name);
                    //参数非系统权限参数且参数值为空，校验不通过
                    if (ConvertUtil.isEmpty(parameters_value)&&!parameters_name.contains("$")){
                        flag=false;
                        return false;
                    }
                    //判断是否校验
                    if (!ConvertUtil.isEmpty(map.get("is_checked")) && (Boolean)map.get("is_checked")){
                        //获取校验类型
                        if (ConvertUtil.isEmpty(map.get("checked_type_remark"))){
                            flag=false;
                            return false;
                        }
                        String checked_type_remark = map.get("checked_type_remark").toString();
                        // 字符串是否与正则表达式相匹配
                        Boolean checkReult = CommonUtil.regularMatching(checked_type_remark,parameters_value);
                        if(!checkReult){
                            flag=false;
                            return false;
                        }
                        //如果值包含-为时间格式需加引号
                        if (parameters_value.contains("-")){
                            parameters_value = "'"+parameters_value+"'";
                        }
                        //校验通过替换值
                        sqlValue = sqlValue.replaceAll(Matcher.quoteReplacement(parameters_name),parameters_value);
                    }else {
                        //如果值包含-为时间格式需加引号
                        if (parameters_value.contains("-")){
                            parameters_value = "'"+parameters_value+"'";
                        }
                        sqlValue = sqlValue.replaceAll(Matcher.quoteReplacement(parameters_name),parameters_value);
                    }
                }
            }

        }catch (Exception e){
        	flag = false;
        	logger.error("校验异常："+sqlValue,e);
        }finally {
            sql.delete(0, sql.length());
            sql.append(sqlValue);
            return flag;
        }
    }

    @Override
    public void sdditionalSearchCriteria(StringBuilder sql,String jsonParam, Long dTemplatesId, Long userId) {
            JSONObject jsonObj = CommonUtil.processingJSON(jsonParam);
            //获取用户参数
            List<Map<String, Object>> mapList = dUserParametersDao.findUserParameters(userId,dTemplatesId);
            if (mapList==null||mapList.size()==0){
                mapList = dUserParametersDao.findDeParameters(dTemplatesId);
            }
            //处理参数
            splicingScreening(sql,jsonObj,mapList);
    }

    @Override
    public void splicingScreening(StringBuilder sql, JSONObject jsonObj, List<Map<String, Object>> mapList) {
//        try {
            if (mapList!=null&&mapList.size()>0){
                //遍历判断值并追加
                for (Map<String,Object> map : mapList){
                    //判断是否为必选条件 是则不追加条件过滤
                    if (map.get("required_condition")!=null && Boolean.parseBoolean(String.valueOf(map.get("required_condition")))){
                        continue;
                    }
                    //参数名
                    String paramName = String.valueOf(map.get("name"));
                    //参数值
                    String paramValue = jsonObj.getString(paramName);
                    /**不能包含特殊符号'**/
                    if (!ConvertUtil.isEmpty(paramValue)&&paramValue.contains("'")){
                        ExceptionUtil.throwControllerException("筛选值不能包含特殊符号'");
                    }
                    Integer screenRemark = Integer.valueOf(String.valueOf(map.get("screenRemark")));
                    switch (screenRemark){
                        case 1001:             //模糊查询
                            if (!ConvertUtil.isEmpty(paramValue) && !"null".equals(paramValue)){
                                paramValue = paramValue.trim();
                                sql.append(" and "+paramName + " like '%"+paramValue+"%' ");
                            }
                            break;
                        case 1002:             //精确匹配
                            if (!ConvertUtil.isEmpty(paramValue) && !"null".equals(paramValue)){
                                sql.append(" and "+paramName + "='"+paramValue+"'");
                            }
                            break;
                        case 1003:           //时间、日期范围
                            String startTime = jsonObj.getString(paramName+"StartTime");     //开始时间
                            String endTime = jsonObj.getString(paramName+"EndTime");//结束时间
                            if (!ConvertUtil.isEmpty(startTime) && !ConvertUtil.isEmpty(endTime)){
                                sql.append(" and ("+paramName + ">='"+startTime+"' and "+paramName+"<='"+endTime+"')");
                            }
                            break;
                        case 1004:             //多值范围（in）
                            paramValue = jsonObj.getString(paramName+"Id");
                            if (ConvertUtil.isEmpty(paramValue)){   //没有值则跳过循环
                                continue;
                            }
                            //获取id匹配字段
                            paramName = String.valueOf(map.get("field"));
                            //获取父级匹配字段
                            String parentParam = String.valueOf(map.get("parentParamName"));
                            String endValue = paramValue.replace("[","").replace("]","");
                            //拼接参数
                            if (parentParam == null || ConvertUtil.isEmpty(parentParam)){
                                if (!ConvertUtil.isEmpty(endValue) && !"null".equals(endValue)){
                                    sql.append(" and "+paramName + " in ("+endValue+")");
                                }
                            }else {
                                sql.append(" and ("+paramName+" in ("+endValue+") or find_in_set('"+endValue+"',"+parentParam+"))");
                            }
                            break;
                        case 1005:     //布尔或数字
                            if (!ConvertUtil.isEmpty(paramValue) && !"null".equals(paramValue)) {
                                sql.append(" and " + paramName + "=" + paramValue + " ");
                            }
                            break;
                        case 1006:     //年月
                            String startTime1 = jsonObj.getString(paramName+"StartTime");     //开始时间
                            String endTime1 = jsonObj.getString(paramName+"EndTime");//结束时间
                            if (!ConvertUtil.isEmpty(startTime1) && !ConvertUtil.isEmpty(endTime1)){
                                sql.append(" and (date_format("+paramName +", '%Y-%m') >= '"+startTime1+"' and date_format("+paramName +", '%Y-%m')<='"+endTime1+"')");
                            }
                            break;
                        default:            //精确匹配
                            if (!ConvertUtil.isEmpty(paramValue) && !"null".equals(paramValue)) {
                                sql.append(" and " + paramName + "='" + paramValue + "' ");
                            }
                            break;
                    }
                }
            }
          //  sql.append(" group by id");
//        }catch (Exception e){
//            logger.error("处理筛选sql异常:"+sql.toString(),e);
//        }
    }
}
