package net.shopxx.template.controller;
import java.util.*;
import java.util.regex.Matcher;
import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.template.entity.DTemplates;
import net.shopxx.template.service.*;
import net.shopxx.util.ExportReportUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.template.entity.DTemplateUsers;
import net.shopxx.template.entity.DUserColumns;
import org.springframework.web.servlet.ModelAndView;

@Controller("dUserColumnsController")
@RequestMapping("/template/dUserColumns")
public class DUserColumnsController extends BaseController{

    private static Logger logger = LoggerFactory.getLogger(DUserColumnsController.class);
	@Resource(name = "dUserColumnsServiceImpl")
	private DUserColumnsService dUserColumnsService;
	@Resource(name = "dTemplateParametersServiceImpl")
	private DTemplateParametersService dTemplateParametersService;
	@Resource(name = "dTemplateUsersServiceImpl")
	private DTemplateUsersService dTemplateUsersService;
    @Resource(name = "dTemplateColumnsServiceImpl")
	private DTemplateColumnsService dTemplateColumnsService;
	@Resource(name = "dTemplatesServiceImpl")
	private DTemplatesService dTemplatesService;
    @Resource(name = "exportReportUtils")
	private ExportReportUtils exportReportUtils;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(Long userId,Long templateId,ModelMap model,String jumpPageUrl,String isCheck) {

        if (ConvertUtil.isEmpty(userId)){
            userId = WebUtils.getCurrentStoreMemberId();
        }
        if (ConvertUtil.isEmpty(jumpPageUrl)){
            return "跳转页面不能为空！";
        }
        if (ConvertUtil.isEmpty(templateId)){
            return "模板ID不能为空！";
        }
        model.addAttribute("userId", userId);
        model.addAttribute("templateId", templateId);
        //获取用户模板列信息
        List<Map<String,Object>> mapList = dUserColumnsService.findUserTemplateColumns(userId, templateId);
        if (mapList==null || mapList.size() == 0){  //获 取标准模板字段
            mapList = dTemplateColumnsService.findColumnsInfoByTemplateId(templateId,userId,true,true);
        }
        model.addAttribute("mapList", mapList);
        //获取模板信息 是否默认查询
        DTemplates dTemplates = dTemplatesService.find(templateId);
        if (dTemplates.getDefaultQuery()!=null&&dTemplates.getDefaultQuery()){
            model.addAttribute("defaultQuery",1);
        }else {
            model.addAttribute("defaultQuery",0);
        }
        model.addAttribute("dTemplates",dTemplates);
        model.addAttribute("pagesize", dTemplates.getPagesize());
        model.addAttribute("isCheck",isCheck);
        return jumpPageUrl;
    }

    /**
     * 获取用户模板列信息
     * @param userId
     * @param templateId
     * @return
     */
    @RequestMapping(value = "/getUserTemplateColumns", method = RequestMethod.POST)
    public @ResponseBody
    List<Map<String, Object>> getUserTemplateColumns(Long userId,Long templateId) {
        if (ConvertUtil.isEmpty(userId)){
            userId = WebUtils.getCurrentStoreMemberId();
        }
        if (ConvertUtil.isEmpty(templateId)){
            return null;
        }
        //获取用户模板列信息
        List<Map<String,Object>> mapList = dUserColumnsService.findUserTemplateColumns(userId, templateId);
        if (mapList==null || mapList.size() == 0){  //获取标准模板字段
            mapList = dTemplateColumnsService.findColumnsInfoByTemplateId(templateId,userId,true,true);
        }
        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("storeMember",
                storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
        List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
                filters,
                null);
        for(int j=0;j<mapList.size();j++){
            Map<String, Object> columnsMap = mapList.get(j);

            for(String s:columnsMap.keySet()) {
                if(s.equals("system_parameter_value") && columnsMap.get(s) !=null ) {
                    int isShow = 0;
                    try {
                        // 根据系统参数判断角色是否能查看此列  1 可显示     非1  不可显示
                        String value = SystemConfig.getConfig(String.valueOf(columnsMap.get(s)),
                                WebUtils.getCurrentCompanyInfoId());
                        String[] role = value.split(",");
                        List<String> list = Arrays.asList(role);
                        for (PcUserRole userRole : userRoles) {
                            if (list.contains(userRole.getPcRole().getName())) {
                                isShow++;
                                break;
                            }
                        }
                        if (isShow != 1) {
                            mapList.remove(j);
                            break;
                        }
                    } catch (RuntimeException e) {
                    }

                }
            }

        }
        return mapList;
    }


    /**
     * 查询列表配置
     */
    @RequestMapping(value = "/select_configuration_list", method = RequestMethod.GET)
    public String select_configuration_list(ModelMap model,Long userId,Long templateId) {
        model.addAttribute("userId", userId);
        model.addAttribute("templateId", templateId);
        return "/b2b/commModel/throughTrain/select_configuration_list";
    }

    
    @RequestMapping(value = "/user_columns_list", method = RequestMethod.GET)
    public String user_columns_list(Long usersTemplateId, ModelMap model){
    	
    	DTemplateUsers dTemplateUsers = dTemplateUsersService.find(usersTemplateId);
        model.addAttribute("dTemplateUsers", dTemplateUsers);
        
        return "/b2b/commModel/setupPage/user_columns_list";
    }
   
    /**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Long usersTemplateId,String showName,
			Boolean isShowed,Pageable pageable) {

		Page<Map<String, Object>> page = dUserColumnsService.findPage(usersTemplateId,showName,isShowed,pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
    
	/**
	 * 编辑
	 */
	@RequestMapping(value = "/user_columns_view", method = RequestMethod.GET)
	public String user_columns_view(Long id, ModelMap model) {
		
		DUserColumns dUserColumns =  dUserColumnsService.find(id);
		model.addAttribute("dUserColumns",dUserColumns);
		
		return "/b2b/commModel/setupPage/user_columns_view";
	}
	
	
	
	/**
	 * 修改
	 * @param dUserColumns
	 * @return
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(DUserColumns dUserColumns) {
		
		DUserColumns pDUserColumns = dUserColumnsService.find(dUserColumns.getId());
		if(ConvertUtil.isEmpty(pDUserColumns)){
			return error("当前用户模板列不存在");
		}
		//是否显示
		if(!ConvertUtil.isEmpty(dUserColumns.getIsShowed())){
			pDUserColumns.setIsShowed(dUserColumns.getIsShowed());
		}else{
			pDUserColumns.setIsShowed(true);
		}
		//是否允许导出
		if(!ConvertUtil.isEmpty(dUserColumns.getIsExport())){
			pDUserColumns.setIsExport(dUserColumns.getIsExport());
		}else{
			pDUserColumns.setIsExport(true);
		}
		dUserColumnsService.update(pDUserColumns);
		
		return success().addObjX(pDUserColumns.getdTemplateUsers().getId());
	}
	
	
	
	/**
	 * 删除
	 */
	@RequestMapping(value = "/deleteDUserColumns", method = RequestMethod.POST)
	public @ResponseBody ResultMsg deleteDUserColumns(Long id) {
	
		DUserColumns dUserColumns = dUserColumnsService.find(id);
		if(ConvertUtil.isEmpty(dUserColumns)){
			return error("当前用户模板列不存在");
		}
		if(ConvertUtil.isEmpty(dUserColumns.getdTemplateUsers())){
			return error("当前用户模板不存在");
		}
		Long dTemplateUsersId = dUserColumns.getdTemplateUsers().getId();
		
		dUserColumnsService.delete(dUserColumns);
		
		return success().addObjX(dTemplateUsersId);
	}
	
	
	
	/**
	 * 修改
	 */
	@RequestMapping(value = "/update_user_columns", method = RequestMethod.GET)
	public String update_user_columns(Long id, ModelMap model) {
		
		DUserColumns dUserColumns =  dUserColumnsService.find(id);
		model.addAttribute("dUserColumns",dUserColumns);
		
		return "/b2b/commModel/setupPage/user_columns_view";
	}

    /**
     * 查询用户模板列数据
     * @param jsonParam 筛选条件jsonString
     * @param userId  用户id
     * @param templateId 模板id
     * @param pageable 分页
     * @return
     */
    @RequestMapping(value = "/selectUserTemplateData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public @ResponseBody ResultMsg selectUserTemplateData(String jsonParam,Long userId, Long templateId, Pageable pageable){
	    jsonParam = StringEscapeUtils.unescapeHtml(jsonParam);
        if (ConvertUtil.isEmpty(userId)){
            return error("用户ID不能为空");
        }
        if (ConvertUtil.isEmpty(templateId)){
            return error("模板ID不能为空");
        }
        Page<Map<String, Object>> pagMap = dUserColumnsService.findUserTemplateColumnsData(jsonParam,userId, templateId, pageable);

        return ResultMsg.success(JsonUtils.toJson(pagMap));
    }


    /**
     * 用户列导出数据接口
     * @param jsonParam
     * @param userId
     * @param templateId
     * @param page
     * @param pageable
     * @param model
     * @return
     */
    @RequestMapping(value = "/userColumns_export", method = RequestMethod.GET)
    public ModelAndView conditionExport(String jsonParam,Long userId, Long templateId, Integer page, Pageable pageable, ModelMap model) {
        Map<String, Integer> segments = getSegment();
        int size = segments.get("size");
        pageable.setPageSize(size);
        pageable.setPageNumber(page);
        //json参数特殊字符转换
        jsonParam = StringEscapeUtils.unescapeHtml(jsonParam);
        //获取模板信息
        DTemplates dTemplates = dTemplatesService.find(templateId);
        String fileName = dTemplates.getName();    //文件名
        //表头
        List<Map<String,Object>> titleList = dUserColumnsService.findUserTemplateColumns(userId, templateId);
        if (titleList == null || titleList.size() == 0){
            titleList = dTemplateColumnsService.findColumnsInfoByTemplateId(templateId,userId,true,true);
        }
        //表数据
        Page<Map<String, Object>> pagMap = dUserColumnsService.findUserTemplateColumnsData(jsonParam,userId, templateId, pageable);
        List<Map<String, Object>> dataList = pagMap.getContent();
        //处理模板并导出
        return  exportReportUtils.getModelAndViewForList(titleList,dataList,fileName,model);
    }


    //获取分页信息
    private Map<String, Integer> getSegment() {
        HashMap<String, Integer> map = new HashMap<String, Integer>();
        try {
            String value = SystemConfig.getConfig("SegmentExportConfig",
                    WebUtils.getCurrentCompanyInfoId());
            String[] values = value.split(",");
            map.put("segment", Integer.parseInt(values[0]));
            map.put("size", Integer.parseInt(values[1]));
        }
        catch (Exception e) {
            map.put("segment", 10);
            map.put("size", 1000);
        }
        return map;
    }

    /**
     * 用户列导出分页数据
     * @param jsonParam
     * @param userId
     * @param templateId
     * @return
     */
    @RequestMapping(value = "/to_UserColumns_export", method = RequestMethod.POST)
    public @ResponseBody
    List<Map<String, Object>> to_UserColumns_export(String jsonParam,Long userId, Long templateId) {
        jsonParam = StringEscapeUtils.unescapeHtml(jsonParam);
        Integer size = dUserColumnsService.findUserColumnsDataCounts(jsonParam, userId, templateId);
        List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
        Map<String, Integer> segments = getSegment();
        int stage = segments.get("segment");
        int page_size = segments.get("size");
        for (int i = 0; i < stage; i++) {
            Map<String, Object> map = new HashMap<String, Object>();
            int total = i * page_size;
            if (size == 0) {
                map.put("data", "0-0");
            }
            else if ((size - total) <= page_size) {
                map.put("data", (total + 1) + "-" + size);
            }
            else {
                map.put("data", (total + 1) + "-" + (i + 1) * page_size);
            }
            lists.add(map);
            if ((size - total) <= page_size) {
                break;
            }
        }
        return lists;
    }


    /**
     * 获取下一单数据
     * @param menuId
     * @param jsonParam
     * @return
     */
    @RequestMapping(value = "/findTempDataToNextOrder", method = RequestMethod.POST)
    public @ResponseBody
    List<Map<String, Object>>  findTempDataToNextOrder(Long menuId,int nextOrder,String jsonParam,Integer type){
        jsonParam = StringEscapeUtils.unescapeHtml(jsonParam);
        List<Map<String,Object>> objectList = dUserColumnsService.findTempDataToNextOrder(menuId, jsonParam,nextOrder,type);
        return objectList;
    }

}
