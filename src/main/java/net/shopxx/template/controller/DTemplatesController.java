package net.shopxx.template.controller;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.template.entity.DTemplates;
import net.shopxx.template.service.DModulesService;
import net.shopxx.template.service.DTemplateColumnsService;
import net.shopxx.template.service.DTemplateParametersService;
import net.shopxx.template.service.DTemplateUsersService;
import net.shopxx.template.service.DTemplatesService;
import net.shopxx.util.RoleJurisdictionUtil;
@Controller("dTemplatesController")
@RequestMapping("/template/dTemplates")
public class DTemplatesController extends BaseController{

	@Resource(name = "dTemplatesServiceImpl")
	private DTemplatesService dTemplatesService;
	@Resource(name = "dModulesServiceImpl")
	private DModulesService dModulesService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "dTemplateParametersServiceImpl")
	private DTemplateParametersService dTemplateParametersService;
    @Resource(name = "dTemplateColumnsServiceImpl")
	private DTemplateColumnsService dTemplateColumnsService;
    @Resource(name = "dTemplateUsersServiceImpl")
	private DTemplateUsersService dTemplateUsersService;
	
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(ModelMap model){
		
		List<Map<String, Object>> mapList = dModulesService.findDModulesList();
		model.addAttribute("mapList",mapList);
		
		return "/b2b/commModel/setupPage/list";
	}


    @RequestMapping(value = "/select_list", method = RequestMethod.GET)
    public String select_list(Long modelId,Long parentdmodulesId,ModelMap model){
        model.addAttribute("modelId",modelId);
        model.addAttribute("parentdmodulesId",parentdmodulesId);
        model.addAttribute("isEnable",true);
        return "/b2b/commModel/throughTrain/templates_list";
    }

	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model){
		return "/b2b/commModel/setupPage/list_tb";
	}

	@RequestMapping(value = "/tem_list", method = RequestMethod.GET)
	public String tem_list(ModelMap model){
		return "/b2b/commModel/setupPage/tem_list";
	}
	
	@RequestMapping(value = "/template_add", method = RequestMethod.GET)
	public String mode_add(ModelMap model){
	
		//操作类型
		List<SystemDict> actionTypeList = roleJurisdictionUtil.getSystemDictList("actionType",null);
		model.addAttribute("actionTypeList",actionTypeList);
		
		return "/b2b/commModel/setupPage/templates_add";
	}
	
	
	/**
	 * 保存
	 * @param dTemplates
	 * @param modulesId
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(DTemplates dTemplates,
			Long modulesId,Long actionTypeId) {
		
		if(ConvertUtil.isEmpty(dTemplates.getName())){
			return error("模板名称不能为空");
		}
		if(ConvertUtil.isEmpty(modulesId)){
			return error("对应模块不能为空");
		}
		if(ConvertUtil.isEmpty(actionTypeId)){
			return error("操作类型不能为空");
		}
		dTemplatesService.saveDTemplates(dTemplates,modulesId,
				actionTypeId);
		
		return success().addObjX(dTemplates.getId());
	}
	
	

    @RequestMapping(value = "/template_view", method = RequestMethod.GET)
    public String template_view(ModelMap model,Long id){
    	
    	//模板
    	DTemplates dTemplates = dTemplatesService.find(id);
    	model.put("dTemplates",dTemplates);
    	
    	//操作类型
		List<SystemDict> actionTypeList = roleJurisdictionUtil.getSystemDictList("actionType",null);
		model.addAttribute("actionTypeList",actionTypeList);

        return "/b2b/commModel/setupPage/templates_view";
    }

    /**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(DTemplates dTemplates,
			Long modulesId,Long actionTypeId) {
		
		if(ConvertUtil.isEmpty(dTemplates.getName())){
			return error("模板名称不能为空");
		}
		if(ConvertUtil.isEmpty(modulesId)){
			return error("对应模块不能为空");
		}
		if(ConvertUtil.isEmpty(actionTypeId)){
			return error("操作类型不能为空");
		}
		dTemplatesService.updateDTemplates(dTemplates, modulesId, actionTypeId);
		
		return success().addObjX(dTemplates.getId());
	}
    
	
	/**
	 * 删除
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	public @ResponseBody ResultMsg delete(Long id) {
	
		DTemplates dTemplates = dTemplatesService.find(id);
		if(ConvertUtil.isEmpty(dTemplates)){
			return error("模板不存在");
		}
		dTemplatesService.deleteDTemplates(dTemplates);
		return success();
	}

    
    /**
     *
     * @return
     */
    @RequestMapping(value = "/select_templates_data", method = RequestMethod.POST)
    public @ResponseBody ResultMsg select_templates_data(Long modelId, 
    		String tempName,Boolean isEnable,Pageable pageable,Long parentdmodulesId,String modelName,String creatDate){
    	
        Page<Map<String, Object>> pagMap = dTemplatesService.findTemplatesByModel(modelId,tempName,
        		isEnable,pageable,parentdmodulesId,modelName,creatDate);

        return ResultMsg.success(JsonUtils.toJson(pagMap));
    }

}
