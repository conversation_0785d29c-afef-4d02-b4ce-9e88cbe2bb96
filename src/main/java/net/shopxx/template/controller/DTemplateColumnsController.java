package net.shopxx.template.controller;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import net.shopxx.base.core.util.LogUtils;
import com.alibaba.fastjson.JSONObject;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.order.b2b.controller.ReportExportController;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.template.entity.DTemplateColumns;
import net.shopxx.template.entity.DTemplates;
import net.shopxx.template.service.DTemplateColumnsService;
import net.shopxx.template.service.DTemplatesService;
import net.shopxx.util.RoleJurisdictionUtil;
@Controller("dTemplateColumnsController")
@RequestMapping("/template/dTemplateColumns")
public class DTemplateColumnsController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(ReportExportController.class);
	
	@Resource(name = "dTemplateColumnsServiceImpl")
	private DTemplateColumnsService dTemplateColumnsService;
	@Resource(name = "dTemplatesServiceImpl")
	private DTemplatesService dTemplatesService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;

	
	@RequestMapping(value = "/columns_list", method = RequestMethod.GET)
	public String columns_list(ModelMap model){
		return "/b2b/commModel/setupPage/columns_list";
	}
	
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Long dTemplatesId, Pageable pageable) {

		Page<Map<String, Object>> page = dTemplateColumnsService.findPage(dTemplatesId, pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 跳转模板列添加页面
	 * @param dTemplatesId
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/columns_add", method = RequestMethod.GET)
	public String columns_add(Long dTemplatesId,ModelMap model){
		//模板
		DTemplates dTemplates = dTemplatesService.find(dTemplatesId);
		model.put("dTemplates",dTemplates);
		//列类型
		List<SystemDict> columnsTypeList = roleJurisdictionUtil.getSystemDictList("columnsType",null);
		model.put("columnsTypeList",columnsTypeList);
        //弹框类型
        List<SystemDict> popUpTypeList = roleJurisdictionUtil.getSystemDictList("popUpType",null);
        model.put("popUpTypeList",popUpTypeList);
		//校验类型
		List<SystemDict> checkedTypeList = roleJurisdictionUtil.getSystemDictList("checkedType",null);
		model.put("checkedTypeList",checkedTypeList);
		//筛选类型
		List<SystemDict> screenTypeList = roleJurisdictionUtil.getSystemDictList("screenType",null);
		model.put("screenTypeList",screenTypeList);


		return "/b2b/commModel/setupPage/columns_add";
	}

	//获取所有启用模板信息
    public List<DTemplates> getAllDTemplates(){
        List<Filter> filters = new ArrayList<Filter>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        filters.clear();
        filters.add(Filter.eq("companyInfoId", companyInfoId));
        filters.add(Filter.eq("isEnable", true));
        List<DTemplates> dTemplatesList = dTemplatesService.findList(null,filters,null);
        return dTemplatesList;
    }
	
	/**
	 * 保存
	 * @return
	 */
	@RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(DTemplateColumns dTemplateColumns,
			Long columnsTypeId,Long dTemplatesId,Long checkedTypeId,Long popUpTypeId,Long screenTypeId) {
		
		//名称
		if(ConvertUtil.isEmpty(dTemplateColumns.getName())){
			return error("列名称不能为空");
		}
		//显示名
		if(ConvertUtil.isEmpty(dTemplateColumns.getShowName())){
			return error("列显示名称不能为空");
		}
		//列类型
		if(ConvertUtil.isEmpty(columnsTypeId)){
			return error("列类型不能为空");
		}
        //弹框类型
        if(ConvertUtil.isEmpty(popUpTypeId)){
            return error("弹框类型不能为空");
        }
		//筛选类型
		if(ConvertUtil.isEmpty(screenTypeId)){
			return error("筛选类型不能为空");
		}
		//模板
		if(ConvertUtil.isEmpty(dTemplatesId)){
			return error("模板不能为空");
		}
		//校验类型
		if(dTemplateColumns.getIsChecked() && ConvertUtil.isEmpty(checkedTypeId)){
			return error("是否校验为'是'则校验类型不能为空!");
		}
		//排序
		if(ConvertUtil.isEmpty(dTemplateColumns.getOrderby())){
			return error("排序不能为空");
		}

		
		dTemplateColumnsService.saveOrUpdateDTemplateColumns(dTemplateColumns, 
				columnsTypeId, dTemplatesId, checkedTypeId,popUpTypeId,screenTypeId);
		
		return success().addObjX(dTemplatesId);
	}
	
	
	@RequestMapping(value = "/columns_view", method = RequestMethod.GET)
    public String columns_view(ModelMap model,Long id){
    	//模板列
		DTemplateColumns dTemplateColumns = dTemplateColumnsService.find(id);
    	model.put("dTemplateColumns",dTemplateColumns);
    	//列类型
		List<SystemDict> columnsTypeList = roleJurisdictionUtil.getSystemDictList("columnsType",null);
		model.put("columnsTypeList",columnsTypeList);
		//校验类型
		List<SystemDict> checkedTypeList = roleJurisdictionUtil.getSystemDictList("checkedType",null);
		model.put("checkedTypeList",checkedTypeList);
        //弹框类型
        List<SystemDict> popUpTypeList = roleJurisdictionUtil.getSystemDictList("popUpType",null);
        model.put("popUpTypeList",popUpTypeList);
		//筛选类型
		List<SystemDict> screenTypeList = roleJurisdictionUtil.getSystemDictList("screenType",null);
		model.put("screenTypeList",screenTypeList);
        //获取所有启用模板
		if (!ConvertUtil.isEmpty(dTemplateColumns.getPopUpTemplate())){
			DTemplates dTemplates = dTemplatesService.find(Long.valueOf(dTemplateColumns.getPopUpTemplate()));
			model.put("dTemplates",dTemplates);
		}
        return "/b2b/commModel/setupPage/columns_view";
    }
	
	
	/**
	 * 保存
	 *
	 * @return
	 */
	@RequestMapping(value = "/deleteDTemplateColumns", method = RequestMethod.POST)
	public @ResponseBody ResultMsg deleteDTemplateColumns(Long id) {
		
		
		//模板列
		DTemplateColumns dTemplateColumns = dTemplateColumnsService.find(id);
		if(ConvertUtil.isEmpty(dTemplateColumns)){
			return error("模板列不能为空");
		}
		if(ConvertUtil.isEmpty(dTemplateColumns.getdTemplates())){
			return error("模板不能为空");
		}
		//获取模板信息
		DTemplates dTemplates = dTemplateColumns.getdTemplates();
        Long dTemplateId = dTemplates.getId();
		try {
			dTemplateColumnsService.delete(dTemplateColumns);
            //更新模板版本 判断是否版本号控制，否则则自增1
            if(!dTemplates.getVersionControl()){
            	dTemplates.setEdition(dTemplates.getEdition()+1);
            }
            dTemplatesService.update(dTemplates);
		} catch (Exception e) {
			return error("删除操作失败");
		}
		return success().addObjX(dTemplateId);
	}


    /**
     * 获取模板字段信息
     * @param userId
     * @param templateId
     * @return
     */
    @RequestMapping(value = "/getColumnsByTemplate", method = RequestMethod.GET)
	public @ResponseBody ResultMsg getColumnsByTemplate(Long userId,Long templateId,
			Boolean isShowed){
        if (ConvertUtil.isEmpty(templateId)){
            return error("模板Id不能为空");
        }
        if (ConvertUtil.isEmpty(userId)){
        	userId = WebUtils.getCurrentStoreMemberId();
		}
        List<Map<String,Object>> mapList = dTemplateColumnsService.findColumnsInfoByTemplateId(templateId,
        		userId,isShowed,null);
        return ResultMsg.success(JsonUtils.toJson(mapList));
    }

    /**
     * 根据id获取模板字段信息
     */
    @RequestMapping(value = "/getDtemplateColumnsById", method = RequestMethod.POST)
    public @ResponseBody ResultMsg getDtemplateColumnsById(Long columnsId) {

       DTemplateColumns dTemplateColumns = dTemplateColumnsService.find(columnsId);
        String jsonData = JsonUtils.toJson(dTemplateColumns);

        return success(jsonData);
    }

	/**
	 * 获取指定词汇编码的词汇信息
	 * @param code
	 * @return
	 */
	@RequestMapping(value = "/getSystemDict", method = RequestMethod.GET)
	public @ResponseBody ResultMsg getSystemDict(String code){
		List<Map<String,Object>> systemDictList = systemDictService.getSystemBillTypeByCode(code);
		String jsonData = JsonUtils.toJson(systemDictList);

		return success(jsonData);
	}

	/**
	 * 获取指定列的信息
	 * @param columnsId
	 * @return
	 */
	@RequestMapping(value = "/findColumnsByColumnsId", method = RequestMethod.GET)
	public @ResponseBody ResultMsg findColumnsByColumnsId(Long columnsId){
		Map<String,Object> columns = dTemplateColumnsService.findColumnsByColumnsId(columnsId);
		String jsonData = JsonUtils.toJson(columns);

		return success(jsonData);
	}

    /**
     *
     * @param templatesId
     * @param jsonParam
     * @return
     */
    @RequestMapping(value = "/findDataByTempLata", method = RequestMethod.POST)
    public @ResponseBody ResultMsg findDataByTempLata(Long templatesId,String jsonParam){
        //转化参数
        jsonParam = StringEscapeUtils.unescapeHtml(jsonParam);
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        //接收参数值
        String name = jsonObject.get("name")==null?"":String.valueOf(jsonObject.get("name"));
        String nameValue = jsonObject.get("nameValue")==null?"":String.valueOf(jsonObject.get("nameValue"));
        List<Map<String,Object>> datas = dTemplateColumnsService.findDataByTempLata(templatesId,name,nameValue);
        String jsonData = JsonUtils.toJson(datas);
        return success(jsonData);
    }
    
    /**
     *  模板列Excel导入
     * @param file
     * @param dTemplatesId
     * @param model
     * @return
     * @throws Exception
     */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg importFromExcel(MultipartFile file, 
			Long dTemplatesId,ModelMap model) throws Exception {
		try {
			DTemplates dTemplates = dTemplatesService.find(dTemplatesId);
			if(ConvertUtil.isEmpty(dTemplates)){
				ExceptionUtil.throwServiceException("模板不能为空"); 
			}
			dTemplateColumnsService.dTemplateColumnsAddImport(file,dTemplates);
			return ResultMsg.success();
		}catch (Exception e) {
			LogUtils.error("导入模板列", e);
			return ResultMsg.error(e.getMessage());
		}
	}

    /**
     * 导出模板信息
     * @param tempId
     * @param session
     * @param response
     * @return
     */
    @RequestMapping(value = "/dTemplateColumnsExport", method = RequestMethod.GET)
    public @ResponseBody ResultMsg dTemplateColumnsExport(Long tempId, HttpSession session, HttpServletResponse response) {
        try {
            //处理数据逻辑
            List<Map<String,Object>> listData  = dTemplateColumnsService.dTemplateColumnsExport(tempId);
            Map<String,Object> map = new HashMap<String, Object>();
            map.put("maplist",listData);
            System.out.println("获取到了数据："+listData.size());
            //获取文件路径
            ServletContext servletContext =session.getServletContext();
            String path= servletContext.getRealPath("/WEB-INF/excelTemplate/dTemplateColumns.xls");
            //获取模板
            TemplateExportParams params = new TemplateExportParams(path);
            //文件名
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String time = sdf.format(date);
            String fileName = "模板列字段"+time;
            //生成模板及数据
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            this.setResponseHeader(response, fileName);
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        }catch(IllegalArgumentException ex){
            logger.error("模板报表导出异常：",ex);
        }catch (Exception e) {
            logger.error("模板报表异常：",e);
            return ResultMsg.success("系统错误");
        }
        return ResultMsg.success("导出报表成功");
    }

    public void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            try {
                fileName = new String(fileName.getBytes(),"ISO8859-1");
            } catch (UnsupportedEncodingException e) {
                logger.error("导出异常命名",e);
            }
            response.setContentType("application/octet-stream;charset=ISO8859-1");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName+".xls");
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            logger.error("导出异常",ex);
        }
    }

}
