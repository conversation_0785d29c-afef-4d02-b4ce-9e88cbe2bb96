package net.shopxx.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.StringReader;
import java.io.StringWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;

import net.shopxx.base.core.Global;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;

import net.shopxx.oa.OaToDoWSServiceFactoryBean;
import net.shopxx.oa.OfsTodoDataWebService;
import net.shopxx.oa.OfsTodoDataWebServicePortType;

public class sendFromToOa {

	 private static final QName SERVICE_NAME = new QName("webservices.ofs.weaver.com.cn", "OfsTodoDataWebService");

	public static   String sendFromToOas(String date) throws Exception {
		  //第一步：创建服务地址  
	        URL url = new URL(Global.getLoader()
                    .getProperty("oaintf.oaurl")+"//services/OfsTodoDataWebService?wsdl");
	        //第二步：打开一个通向服务地址的连接  
	        HttpURLConnection connection = (HttpURLConnection) url.openConnection();  
	        //第三步：设置参数  
	        //3.1发送方式设置：POST必须大写  
	        connection.setRequestMethod("POST");  
	        //3.2设置数据格式：content-type  
	        connection.setRequestProperty("content-type", "text/xml;charset=utf-8");  
	        //3.3设置输入输出，因为默认新创建的connection没有读写权限，  
	        connection.setDoInput(true);  
	        connection.setDoOutput(true);  
	  
	        //第四步：组织SOAP数据，发送请求  
	        String soapXML = getXML(date);  
	        //将信息以流的方式发送出去
	        OutputStream os = connection.getOutputStream();  
	        os.write(soapXML.getBytes());  
	        //第五步：接收服务端响应，打印  
	        int responseCode = connection.getResponseCode();  
	        System.out.println(responseCode);  
	        if(200 == responseCode){//表示服务端响应成功  
	        	//获取当前连接请求返回的数据流
	            InputStream is = connection.getInputStream();  
	            InputStreamReader isr = new InputStreamReader(is);  
	            BufferedReader br = new BufferedReader(isr);  
	              
	            StringBuilder sb = new StringBuilder();  
	            String temp = null;  
	            while(null != (temp = br.readLine())){  
	                sb.append(temp);  
	            }  
	            
	            /**
	             * 打印结果
	             */
	            
	            
	            is.close();  
	            isr.close();  
	            br.close();  
	        }  
	        os.close();
			return soapXML;  

}
	 public static String getXML(String date) throws Exception{  
	    	
	    	String soapXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"  
	    	        +"<soapenv:Envelope xmlns:xsd=\"http://schemas.xmlsoap.org/soap/envelope/\" " 
	    			+"xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance/\">"
	    	            +"<soapenv:Body>"  
	    	              +"<receiveTodoRequestByJson  xmlns=\"webservices.ofs.weaver.com.cn\">"    
	    	              +date
	    	              +"</receiveTodoRequestByJson >"  
	    	            +"</soapenv:Body>"  
	    	        +"</soapenv:Envelope>";  
	    	
	        return soapXML;  
	    }

	

	    /**
	     * @param args
	     */
	    public static void main(String[] args) {
	        try {

	            
	            String in0 = "{'syscode':'LINK'," + "'flowid':'35'," + "'requestname':'营销系统LINK'," + "workflowname:'营销系统LINK'," + "nodename:'查看节点'," 
	            + "pcurl:'http://natest.5mall.com/aftersales/aftersale/edit.jhtml?idAndUsername=35.01061637'," + "appurl:''," + "creator:'01061637'," 
	            		+ "createdatetime:'2019-11-21 17:08:35'," + "receiver:'01061637'," + "receivedatetime:'2019-11-21 17:08:35'}";
	          	String wsdl = "http://202.104.26.172:88/services/OfsTodoDataWebService";
	            String ns = "webservices.ofs.weaver.com.cn";
	            String method = "receiveTodoRequestByJson";
//	            OfsTodoDataWebServicePortType oaToWebService= new  OfsTodoDataWebServicePortType();
//	            System.out.println("response:"+response);
//	            ofsTodoDataWebServicePortType.receiveTodoRequestByJson(in0);
	        
	            URL wsdlURL = OfsTodoDataWebService.WSDL_LOCATION;
	            if (args.length > 0 && args[0] != null && !"".equals(args[0])) { 
	                File wsdlFile = new File(args[0]);
	                try {
	                    if (wsdlFile.exists()) {
	                        wsdlURL = wsdlFile.toURI().toURL();
	                    } else {
	                        wsdlURL = new URL(args[0]);
	                    }
	                } catch (MalformedURLException e) {
	                    e.printStackTrace();
	                }
	            }
	            sendFromToOas(getXML(in0));
	            
//	            OfsTodoDataWebService ss = new OfsTodoDataWebService();
//	            OfsTodoDataWebServicePortType port = ss.getOfsTodoDataWebServiceHttpPort(); 
//	            port.receiveRequestInfoByJson(in0);
	           
//	        	OaToDoWSServiceFactoryBean  oaToDoWSServiceFactoryBean=new OaToDoWSServiceFactoryBean();
//	        	oaToDoWSServiceFactoryBean.setOaToDoWSDLLocation(in0);
	        } catch (Exception e) {
	            e.printStackTrace();
	        }
	    }

	  public  String formatXml(String inputXML) throws Exception {
	        String xml = null;
	        SAXReader reader = new SAXReader();
	        XMLWriter writer = null;
	        org.dom4j.Document document = reader.read(new StringReader(inputXML));
	        try {
	            if (document != null) {
	                StringWriter stringWriter = new StringWriter();
	                OutputFormat format = OutputFormat.createPrettyPrint();
	                format.setNewLineAfterDeclaration(false);
	                writer = new XMLWriter(stringWriter, format);
	                writer.write(document);
	                writer.flush();
	                xml = stringWriter.getBuffer().toString();
	            }
	        } finally {
	            if (writer != null) {
	                try {
	                    writer.close();

	                } catch (Exception e) {
	                    e.printStackTrace();
	                }
	            }
	        }
	        return xml;
	    }
	
}
