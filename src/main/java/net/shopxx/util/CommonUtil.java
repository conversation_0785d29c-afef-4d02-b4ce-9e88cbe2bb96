package net.shopxx.util;

import com.alibaba.fastjson.JSONObject;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.util.ConvertUtil;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {


	/** 页面访问链接通用后缀 */
	private static String url_suffix = "100";

	public static String $userId = "$userId";  //用户id
    public static String $username = "$username";  //用户名
	public static String $organizationId = "$organizationId";  //经营组织
	public static String $sbuId = "$sbuId";  //sbu
	public static String $saleOrgId = "$saleOrgId";  //机构
	public static String $companyInfoId = "$companyInfoId";
    public static String $warehouseIds = "$warehouseIds";    //仓库
    public static String $warehouseStockIds = "$warehouseStockIds";    //仓库库存
    public static String $storeIds = "$storeIds";    //客户



	public static String token = Global.getLoader()
			.getProperty("synintf.token");
	public static String host = Global.getLoader()
			.getProperty("synintf.zbUrl");
	/**
	 * 获取页面文件文件夹前缀
	 */
	public static String getFolderPrefix(String code) {

		String newCode = "";
		if (!ConvertUtil.isEmpty(code) && !code.equals(url_suffix)) {
			newCode = "/" + code;
		}
		return newCode;
	}

    /**
     * 对象转Long
     * @param obj
     * @return
     */
    public static Long objectToLong(Object obj){
        if (!ConvertUtil.isEmpty(obj)){
            return Long.parseLong(String.valueOf(obj));
        }else {
            return null;
        }
    }
	
	
	/**
	 * 根据单据类型返回报表标题和名称
	 * @param type
	 * @return
	 */
	public static String getReportByType(int type) {
		String result = "";
		switch (type) {
		case 1:           
			//发货挑库
			result = "预发货通知单";
			break;
		case 2:           
			//退货接收
			result = "退货单";
			break;
		case 3:           
			//出入库信息
			result = "出入库单";
			break;
		case 4:           
			//移库发货、移库接收
			result = "物料搬运单";
			break;		
		default:
			result = "仓储报表";
			break;
		}
		return result;
	}
	
	/**
	 * 空字符串转换
	 * @param param
	 * @return
	 */
	public static String emptyConversion(Object param) {
		if (param == null || "".equals(param)) {
			return "";
		}else {
			return String.valueOf(param);
		}
	}

	/**
	 * 正则校验值
	 * @param regular  正则
	 * @param parameterValue   参数值
	 * @return
	 */
	public static Boolean regularMatching(String regular,String parameterValue){
		//编译正则表达式
		Pattern pattern = Pattern.compile(regular);
		Matcher matcher = pattern.matcher(parameterValue);
		return matcher.matches();
	}

	/**
	 * 处理json数据并添加默认参数
	 * @param jsonParam
	 * @return
	 */
	public static JSONObject processingJSON(String jsonParam){
		JSONObject jsonObject = new JSONObject();
		if (!ConvertUtil.isEmpty(jsonParam)){
			//解析jsonParam
			jsonObject = JSONObject.parseObject(jsonParam);
		}
		return jsonObject;
	}



	
	
	
}
