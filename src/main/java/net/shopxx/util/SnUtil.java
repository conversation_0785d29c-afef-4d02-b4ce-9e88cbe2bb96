package net.shopxx.util;

import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.Sequence.Rule;

/**
 * 序列生成器
 */
public class SnUtil {

	/**
	 * type：订单  2
			发货单  3		
			通用  999
	 */
	public static String generateSn() {
		return Sequence.getInstance().getSequence(999, Rule.DAY);
	}

	/**
	 * 获取订单单号         ----   订单  2
	 * @return
	 */
	public static String getOrderSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(2, Rule.DAY));
		return sb.toString();
	}

	/**
	 * 获取发货单单号         ----   发货单  3
	 * @return
	 */
	public static String getShippingSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(3, Rule.DAY));
		return sb.toString();
	}
	
	/**
     * 获取门店设计单单号         ----   门店设计单  4
     * @return
     */
    public static String geShopDeviseSn() {
        StringBuilder sb = new StringBuilder();
        sb.append(Sequence.getInstance().getSequence(4, Rule.DAY));
        return sb.toString();
    }
    
    /**
	 * 获取退货单号         ----   退货单  5
	 * @return
	 */
	public static String getB2bReturnsSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(5, Rule.DAY));
		return sb.toString();
	}
	
	
	/**
	 * 获取计划提报单号         ----   计划提报  7
	 * @return
	 */
	public static String getPlanApplySn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(7, Rule.DAY));
		return sb.toString();
	}
	
	
	/**
	 * 获取出入库单号         ----   出入库  8
	 * @return
	 */
	public static String getAmShippingSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(8, Rule.DAY));
		return sb.toString();
	}
	
	
	
	/**
	 * 获取移库单号         ----   移库  9
	 * @return
	 */
	public static String getMoveLibrarySn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(9, Rule.DAY));
		return sb.toString();
	}
	
	
	/**
	 * 获取充值单号         ----   充值单  10
	 * @return
	 */
	public static String getDepositRechargeSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(10, Rule.DAY));
		return sb.toString();
	}
	
	/**
	 * 获取特价单号         ----   特价单  11
	 * @return
	 */
	public static String getPriceApplySn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(11, Rule.DAY));
		return sb.toString();
	}
	
	/**
	 * 获取授信单号         ----   授信单  12
	 * @return
	 */
	public static String getCreditRechargeSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(12, Rule.DAY));
		return sb.toString();
	}
	
	/**
	 * 获取政策单号         ----   政策单  13
	 * @return
	 */
	public static String getPolicyEntrySn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(13, Rule.DAY));
		return sb.toString();
	}
    /**
     * 获取政策单号         ----   政策单  13
     * @return
     */
    public static String getPolicyEntrySnType(Long companyInfoId) {
        StringBuilder sb = new StringBuilder();
        sb.append(Sequence.getInstance().getSequence(13,companyInfoId,Rule.DAY));
        return sb.toString();
    }
	
	
	/**
	 * 获取调账单号         ----   调账单  14
	 * @return
	 */
	public static String getAdjustmentSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(14, Rule.DAY));
		return sb.toString();
	}	  
	
	/**
	 * 获取总账单号         ----   总账单  16
	 * @return
	 */
	public static String getTotalDateSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(16, Rule.DAY));
		return sb.toString();
	}
	
	
	/**
	 * 获取移库关闭单号         ----   移库关闭单  17
	 * @return
	 */
	public static String getMoveLibraryCloseSn() {
		StringBuilder sb = new StringBuilder();
		sb.append(Sequence.getInstance().getSequence(17, Rule.DAY));
		return sb.toString();
	}

    /**
     * 获取售后单号       ------  售后单20
     * @return
     */
    public static String getAftersaleSn(String snTitle,Long companyInfoId){
        StringBuilder sb = new StringBuilder();
        if (companyInfoId!=null){
			sb.append(Sequence.getInstance().getSequence(20,companyInfoId,Rule.DAY));
		}else {
			sb.append(Sequence.getInstance().getSequence(20, Rule.DAY));
		}
        if(snTitle!=null){
            return snTitle+sb.substring(2, sb.length());
        }
        return sb.toString();
    }

    /**
     * 获取库存盘点单号         ----   政策单  13
     * @return
     */
    public static String getInventorySn() {
        StringBuilder sb = new StringBuilder();
        sb.append(Sequence.getInstance().getSequence(30, Rule.DAY));
        return sb.toString();
    }
}
