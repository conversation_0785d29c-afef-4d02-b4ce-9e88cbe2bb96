package net.shopxx.util;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.dao.OrganizationDao;
import net.shopxx.basic.dao.SaleOrgBaseDao;
import net.shopxx.basic.dao.SbuDao;
import net.shopxx.member.dao.StoreBaseDao;
import net.shopxx.basic.dao.SystemDictDao;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.product.entity.ProductDict;
import net.shopxx.product.service.ProductDictService;
import net.shopxx.stock.dao.WarehouseBaseDao;
@Component
public class RoleJurisdictionUtil {
	
	@Resource(name="pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name="organizationDao")
	private OrganizationDao organizationDao;
	@Resource(name="sbuDao")
	private SbuDao sbuDao;
	@Resource(name="saleOrgBaseDao")
	private SaleOrgBaseDao saleOrgBaseDao;
	@Resource(name="storeBaseDao")
	private StoreBaseDao storeBaseDao;
	@Resource(name="warehouseBaseDao")
	private WarehouseBaseDao warehouseBaseDao;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "systemDictDao")
	private SystemDictDao systemDictDao;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "productDictServiceImpl")
	private ProductDictService productDictService;
	
	
	/**
	 * 权限角色
	 * @return
	 */
	public Integer getRoleCount(String code){
		String rolesValue = null;
		Integer roleCount = 0;
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		List<Filter> filters = new ArrayList<Filter>();
	    filters.clear();
	    filters.add(Filter.eq("storeMember", storeMemberId));
	    filters.add(Filter.eq("companyInfoId", companyInfoId));
	    List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
	    rolesValue = SystemConfig.getConfig(code, companyInfoId);
	    if (!ConvertUtil.isEmpty(rolesValue)) {
		      String[] perRole = rolesValue.split(",");
		      List<String> perRoleList = Arrays.asList(perRole);
		      for (PcUserRole userRole : userRoles) {
			        if (perRoleList.contains(userRole.getPcRole().getName())) {
			        	  roleCount++;
				          break;
			        }
		      }
	    }
		return roleCount;
	}
	
	/**
	 * 用户仓库
	 * @return
	 */
	public String  getWarehouseIds(){
	    String warehouseIds = "";
//		if(this.getRoleCount("warehouseRoles") == 0){
//			String warehouseIds = warehouseBaseDao.findWarehouseIds();
//			if(ConvertUtil.isEmpty(warehouseIds)){
//				return "0";
//			}
//			return warehouseIds;
//		}
//		return "-1";
        if(this.getRoleCount("warehouseRoles") == 0){
            warehouseIds = warehouseBaseDao.findWarehouseIds();
        }else {
            warehouseIds = warehouseBaseDao.findAllWarehouseIds();
        }
        if(ConvertUtil.isEmpty(warehouseIds)){
            return "0";
        }
        return warehouseIds;
	}
	
	/**
	 * 用户仓库工厂
	 * @return
	 */
	public String getProductionPlantIds(){
		if(this.getRoleCount("warehouseRoles") == 0){
			String productionPlantIds = systemDictDao.findProductionPlantIds();
			if(ConvertUtil.isEmpty(productionPlantIds)){
				return "0";
			}
			return productionPlantIds;
		}
		return "-1";
	}
	
	/**
	 * 用户仓库库存
	 * @return
	 */
	public String  getWarehouseStockIds(){
//		//用户仓库权限
//		Integer warehouseRolesCount = this.getRoleCount("warehouseRoles");
//		//用户仓库库存权限
//		Integer rwarehouseInventoryQueryPermissionRolesCount = this.getRoleCount("warehouseInventoryQueryPermission");
//		if(warehouseRolesCount == 0 || rwarehouseInventoryQueryPermissionRolesCount == 0){
//			String warehouseIds = warehouseBaseDao.findWarehouseIds();
//			if(ConvertUtil.isEmpty(warehouseIds)){
//				return "0";
//			}
//			return warehouseIds;
//		}
//		return "-1";
        String warehouseIds = "";
        //用户仓库库存权限
        Integer rwarehouseInventoryQueryPermissionRolesCount = this.getRoleCount("warehouseInventoryQueryPermission");
        if(rwarehouseInventoryQueryPermissionRolesCount == 0){
             warehouseIds = warehouseBaseDao.findWarehouseIds();
        }else {
            warehouseIds = warehouseBaseDao.findAllWarehouseIds();
        }
        if(ConvertUtil.isEmpty(warehouseIds)){
            return "0";
        }
        return warehouseIds;
    }
	
	
	/**
	 * 用户机构
	 * @return
	 */
	public String  getSaleOrgIds(){
//		if(this.getRoleCount("storeMemberSaleOrg") == 0){
//			String saleOrgIds = saleOrgBaseDao.findSaleOrgIds();
//			if(ConvertUtil.isEmpty(saleOrgIds)){
//				return "0";
//			}
//			return saleOrgIds;
//		}
//		return "-1";
		String saleOrgIds = "";
		if(this.getRoleCount("storeMemberSaleOrg") == 0){
			saleOrgIds = saleOrgBaseDao.findSaleOrgIds();
		}else {
			saleOrgIds = saleOrgBaseDao.findAllSaleOrgIds();
		}
		if(ConvertUtil.isEmpty(saleOrgIds)){
			return "0";
		}
		return saleOrgIds;
	}
	
	/**
	 * 用户Sbu
	 * @return
	 */
	public String getSbuIds(){
//		if(this.getRoleCount("storeMemberSbu") == 0){
//			String sbuIds = sbuDao.findSbuIds();
//			if(ConvertUtil.isEmpty(sbuIds)){
//				return "0";
//			}
//			return sbuIds;
//		}
//		return "-1";
		String sbuIds;
		if(this.getRoleCount("storeMemberSbu") == 0){
			sbuIds = sbuDao.findSbuIds();
		}else {
			sbuIds = sbuDao.findAllSbuIds();
		}
		if(ConvertUtil.isEmpty(sbuIds)){
			sbuIds = "0";
		}
		return sbuIds;
	}
	
	/**
	 * 用户经营组织
	 * @return
	 */
	public String  getOrganizationIds(){
//		if(this.getRoleCount("storeMemberOrganization") == 0){
//			String organizationIds = organizationDao.findOrganizationIds();
//			if(ConvertUtil.isEmpty(organizationIds)){
//				return "0";
//			}
//			return organizationIds;
//		}
//		return "-1";
		String organizationIds;
		if(this.getRoleCount("storeMemberOrganization") == 0){
			organizationIds = organizationDao.findOrganizationIds();
		}else {
			organizationIds = organizationDao.findAllOrganizationIds();
		}
		if(ConvertUtil.isEmpty(organizationIds)){
			organizationIds = "0";
		}
		return organizationIds;
	}

	/**
	 * 调款单里面用户机构、sbu、经营组织等角色权限数据过滤
	 * @return
	 */
	public Integer getAdjustmentRoleCount(){
		//用户机构权限
		Integer saleOrgRolesCount = this.getRoleCount("storeMemberSaleOrg");
		//用户sbu权限
		Integer sbuRolesCount = this.getRoleCount("storeMemberSbu");
		//用户经组织权限
		Integer organizationRolesCount = this.getRoleCount("storeMemberOrganization");
		if(saleOrgRolesCount == 0 || sbuRolesCount == 0 || organizationRolesCount == 0){
			return 0;
		}
		return 1;
	}
	
	
	/**
	 * 用户权限Sbu 
	 * @return
	 */
	public List<Sbu> getSbuList(){
		List<Sbu> sbuList = new ArrayList<Sbu>();
		List<Filter> filters = new ArrayList<Filter>();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if(this.getRoleCount("storeMemberSbu") == 0){
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("storeMember", storeMemberId));
			List<StoreMemberSbu> storeMemberSbuList = storeMemberSbuService.findList(null,filters,null);
			if(!storeMemberSbuList.isEmpty() && storeMemberSbuList.size()>0){
				for (StoreMemberSbu storeMemberSbu : storeMemberSbuList) {
					if(ConvertUtil.isEmpty(storeMemberSbu.getIsDefault())){
						storeMemberSbu.setIsDefault(false);
					}
				}
				ListUtils.sort(storeMemberSbuList, false , "isDefault");
				for (StoreMemberSbu storeMemberSbu : storeMemberSbuList) {
					if(!ConvertUtil.isEmpty(storeMemberSbu.getSbu())){
						Sbu sbu = storeMemberSbu.getSbu();
						sbu.setIsDefault(storeMemberSbu.getIsDefault());
						if(!ConvertUtil.isEmpty(sbu.getStatus()) && sbu.getStatus()){
							sbuList.add(sbu);
						}
					}
				}
			}
		}else{
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("status", true));
			sbuList = sbuService.findList(null, filters, null);
			if(!sbuList.isEmpty() && sbuList.size() > 0){
				for (Sbu sbu: sbuList) {
					if(ConvertUtil.isEmpty(sbu.getIsDefault())){
						sbu.setIsDefault(false);
					}
				}
			}
		}
		return sbuList;
	}
	
	
	/**
	 * 用户权限经营组织
	 * @return
	 */
	public List<Organization> getOrganizationList(){
		List<Organization> organizationList = new ArrayList<Organization>();
		List<Filter> filters = new ArrayList<Filter>();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if(this.getRoleCount("storeMemberOrganization") == 0){
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("storeMember", storeMemberId));
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(!storeMemberOrganizationList.isEmpty() && storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganization : storeMemberOrganizationList) {
					if(ConvertUtil.isEmpty(storeMemberOrganization.getIsDefault())){
						storeMemberOrganization.setIsDefault(false);
					}
				}
				ListUtils.sort(storeMemberOrganizationList, false , "isDefault");
				for (StoreMemberOrganization storeMemberOrganization : storeMemberOrganizationList) {
					if(!ConvertUtil.isEmpty(storeMemberOrganization.getOrganization())){
						Organization organization = storeMemberOrganization.getOrganization();
						if(!ConvertUtil.isEmpty(organization.getType()) && organization.getType() == 0){
							if(!ConvertUtil.isEmpty(organization.getIsEnabled()) && organization.getIsEnabled()){
								organizationList.add(organization);
							}
						}
					}
				}
			}
		}else{
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("type", 0));
			filters.add(Filter.eq("isEnabled", true));
			organizationList = organizationService.findList(null, filters, null);
		}
		return organizationList;
	}
	

    /**
     * 用户权限客户
     * @return
     */
    public String getStores(){
        
        String storeIds;
        storeIds = storeBaseDao.findAllStore();
        if(ConvertUtil.isEmpty(storeIds)){
            storeIds = "-1";
        }
        return storeIds;
    }

	
	public List<Filter> getSystemDictFilterList(Long companyInfoId,
			Boolean isEnabled,Boolean parentIsNull,String code,String value){
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		if(!ConvertUtil.isEmpty(companyInfoId)){
			filters.add(Filter.eq("companyInfoId", companyInfoId));
		}
		if(!ConvertUtil.isEmpty(isEnabled)){
			filters.add(Filter.eq("isEnabled", isEnabled));
		}
		if(!ConvertUtil.isEmpty(parentIsNull) && parentIsNull){
			filters.add(Filter.isNotNull("parent"));
		}
		if(!ConvertUtil.isEmpty(code)){
			filters.add(Filter.eq("code", code));
		}
		if(!ConvertUtil.isEmpty(value)){
			filters.add(Filter.eq("value", value));
		}
		return filters;
	}
	
	
	/*
	 * 系统词汇
	 */
	public SystemDict getSystemDict(String code,String value){
		if(!ConvertUtil.isEmpty(code) && !ConvertUtil.isEmpty(value)){
			Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
			List<Filter> filters = this.getSystemDictFilterList(companyInfoId,
					true,true,code,value);
			SystemDict systemDict = systemDictService.find(filters);
			return systemDict;
		}
		return null;
	}
	
	public SystemDict getSystemDict(Long companyInfoId,String code,String value){
		if(!ConvertUtil.isEmpty(code) && !ConvertUtil.isEmpty(value)){
			List<Filter> filters = this.getSystemDictFilterList(companyInfoId,
					true,true,code,value);
			SystemDict systemDict = systemDictService.find(filters);
			return systemDict;
		}
		return null;
	}
	
	
	/*
	 * 系统词汇
	 */
	public List<SystemDict> getSystemDictList(String code,String value){
		if(!ConvertUtil.isEmpty(code)){
			List<SystemDict> systemDictList = systemDictService.findSystemDictList(code,value);
			return systemDictList;
		}
		return null;
	}
	
	
	
	
	public List<Filter> getProductDictFilterList(Long companyInfoId,
			Boolean isEnabled,Boolean parentIsNull,String code,String value){
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		if(!ConvertUtil.isEmpty(companyInfoId)){
			filters.add(Filter.eq("companyInfoId", companyInfoId));
		}
		if(!ConvertUtil.isEmpty(isEnabled)){
			filters.add(Filter.eq("isEnabled", isEnabled));
		}
		if(!ConvertUtil.isEmpty(parentIsNull) && parentIsNull){
			filters.add(Filter.isNotNull("parent"));
		}
		if(!ConvertUtil.isEmpty(code)){
			filters.add(Filter.eq("code", code));
		}
		if(!ConvertUtil.isEmpty(value)){
			filters.add(Filter.eq("value", value));
		}
		return filters;
	}
	
	/*
	 * 产品词汇
	 */
	public List<ProductDict> getProductDictList(String code,String value,Long companyInfoId){
		if(!ConvertUtil.isEmpty(code)){
			if(ConvertUtil.isEmpty(companyInfoId)){
				companyInfoId = WebUtils.getCurrentCompanyInfoId();
			}
			List<Filter> filters = this.getProductDictFilterList(companyInfoId,
					true,true,code,value);
			List<ProductDict> productDictList = productDictService.findList(null,filters,null);
			return productDictList;
		}
		return null;
	}
}
