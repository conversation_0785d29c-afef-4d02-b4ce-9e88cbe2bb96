package net.shopxx.util;

import org.springframework.stereotype.Service;

import net.shopxx.oa.AnyType2AnyTypeMap;
import net.shopxx.oa.OfsTodoDataWebServicePortType;

@Service("linkToOaServiceImpl")
public class LinkToOaServiceImpl implements OfsTodoDataWebServicePortType{

	@Override
	public AnyType2AnyTypeMap receiveTodoRequestByMap(AnyType2AnyTypeMap in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String processDoneRequestByJson(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public AnyType2AnyTypeMap processDoneRequestByMap(AnyType2AnyTypeMap in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String deleteUserRequestInfoByXML(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public AnyType2AnyTypeMap processOverRequestByMap(AnyType2AnyTypeMap in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String deleteRequestInfoByJson(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String receiveRequestInfoByJson(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public AnyType2AnyTypeMap receiveRequestInfoByMap(AnyType2AnyTypeMap in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String processOverRequestByXml(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String deleteUserRequestInfoByJson(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String receiveTodoRequestByJson(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String processDoneRequestByXml(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public AnyType2AnyTypeMap deleteUserRequestInfoByMap(AnyType2AnyTypeMap in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public AnyType2AnyTypeMap deleteRequestInfoByMap(AnyType2AnyTypeMap in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String processOverRequestByJson(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String receiveRequestInfoByXml(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String receiveTodoRequestByXml(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String deleteRequestInfoByXML(String in0) {
		// TODO Auto-generated method stub
		return null;
	}

}
