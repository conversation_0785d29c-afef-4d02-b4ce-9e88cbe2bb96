package net.shopxx.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.service.PcUserRoleBaseService;

/**
 * 系统权限控制公共类
 * <AUTHOR>
 *
 */
public class SystemParametes {
	
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private static PcUserRoleBaseService pcUserRoleBaseService;
	
	/**
	 * 处理用户仓库库存权限
	 */
	public static int getWarehouseInventoryQueryPermission() {
		int iwarehouseInventoryQueryPermission = 0;
		//登录用户id
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		//组织主体
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        String warehouseInventoryQueryPermission = SystemConfig.getConfig("warehouseInventoryQueryPermission",companyInfoId);
        List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		if(!ConvertUtil.isEmpty(warehouseInventoryQueryPermission)){
			if(!ConvertUtil.isEmpty(userRoles)&&userRoles.size()>0){
				String[] perRole = warehouseInventoryQueryPermission.split(",");
				List<String> perRoleList = Arrays.asList(perRole);
				for (PcUserRole userRole : userRoles) {
					if (perRoleList.contains(userRole.getPcRole().getName())) {
						iwarehouseInventoryQueryPermission++;
						break;
					}
				}
			}
		}
		return iwarehouseInventoryQueryPermission;
	}

}
