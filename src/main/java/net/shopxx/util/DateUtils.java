package net.shopxx.util;
import java.util.Calendar;
import java.util.Date;
public class DateUtils {
	
	/**
     * 当月第一天
     * @return
     */
    public static Date getFirstDay() {   
        Calendar todayStart = Calendar.getInstance();
        todayStart.set( Calendar.DATE, 1 );
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        return todayStart.getTime();
    }
    
    
    /**
     * 当天
     * @return
     */
    public static Date getToday() {   
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        return todayStart.getTime();
    }
    
    
    public static boolean isBetweenWithTwoDaysOfThisMonth(int from, int to) {
	      Calendar cal = Calendar.getInstance();
	      Date today = cal.getTime();
	      int thisMonth = cal.get(2);
	      cal.set(11, 0);
	      cal.set(12, 0);
	      cal.set(13, 0);
	      cal.set(14, 0);
	      cal.set(5, from);
	      long fromMills = cal.getTimeInMillis();
	      cal.set(5, to + 1);
	      if (cal.get(2) != thisMonth) {
	        cal.set(2, thisMonth + 1);
	        cal.set(5, cal.getActualMinimum(5));
	      }
	      long toMills = cal.getTimeInMillis();
	      long now = today.getTime();
	      if ((fromMills <= now) && (now < toMills)) {
	        return true;
	      }
	      return false;
    }
    
    
    public static boolean duringHalfMonth() {
	      Calendar c = Calendar.getInstance();
	      c.add(2, 0);
	      c.set(5, 1);
	      Calendar cal = Calendar.getInstance();
	      c.add(2, 0);
	      cal.set(11, 0);
	      cal.set(12, 0);
	      cal.set(13, 0);
	      cal.set(14, 0);
	      long startTime = cal.getTimeInMillis();
	      cal.set(5, 16);
	      long endTime = cal.getTimeInMillis();
	      Date currDate = new Date();
	      long currTime = currDate.getTime();
	      if ((startTime < currTime) && (currTime < endTime)) {
	        return true;
	      }
	      return false;
    }
    
    
    public static boolean duringTheMonth() {
	      Calendar c = Calendar.getInstance();
	      c.add(2, 0);
	      c.set(5, 1);
	      Calendar cal = Calendar.getInstance();
	      c.add(2, 0);
	      cal.set(11, 0);
	      cal.set(12, 0);
	      cal.set(13, 0);
	      cal.set(14, 0);
	      long startTime = cal.getTimeInMillis();
	      cal.set(5, 30);
	      long endTime = cal.getTimeInMillis();
	      Date currDate = new Date();
	      long currTime = currDate.getTime();
	      if ((startTime < currTime) && (currTime < endTime)) {
	        return true;
	      }
	      return false;
    }

}
