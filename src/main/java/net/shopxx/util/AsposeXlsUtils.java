package net.shopxx.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aspose.cells.License;

import net.shopxx.order.b2b.controller.ReportExportController;


public class AsposeXlsUtils {
	private static Logger logger = LoggerFactory.getLogger(AsposeXlsUtils.class);

	private static InputStream license;
    private static InputStream fileInput;
    private static File outputFile;

/**
     * 获取license
     *
     * @return
     */

    public static boolean getLicense(HttpSession session) {
        boolean result = false;
        try {
        	ServletContext servletContext =session.getServletContext();
			license = servletContext.getResourceAsStream("/WEB-INF/license.xml");
//            fileInput = new FileInputStream(loader.getResource("pdf/Report.pdf").getPath());// 待处理的文件
//            outputFile = new File("D:\\company\\develop\\aladdin2\\aladdin_report\\src\\main\\resources\\123.doc");// 输出路径

            License aposeLic = new License();
            aposeLic.setLicense(license);
            result = true;
        } catch (Exception e) {
            logger.error("校验失败",e);
        }
        return result;
    }
}
