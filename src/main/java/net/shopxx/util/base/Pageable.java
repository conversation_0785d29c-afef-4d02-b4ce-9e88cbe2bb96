package net.shopxx.util.base;

import java.io.Serializable;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

public class Pageable
  implements Serializable
{
  private static final long serialVersionUID = -3930180379790344299L;
  private static final int DEFAULT_PAGE_NUMBER = 1;
  private static final int DEFAULT_PAGE_SIZE = 200;
  private static final int MAX_PAGE_SIZE = 500;
  private int pageNumber = 1;
  
  private int pageSize = 200;
  
  public Pageable() {}
  
  public Pageable(Integer pageNumber, Integer pageSize)
  {
    if ((pageNumber != null) && (pageNumber.intValue() >= 1)) {
      this.pageNumber = pageNumber.intValue();
    }
    if ((pageSize != null) && (pageSize.intValue() >= 1)) {
      this.pageSize = pageSize.intValue();
    }
  }
  
  public int getPageNumber()
  {
    return this.pageNumber;
  }
  
  public void setPageNumber(int pageNumber)
  {
    if (pageNumber < 1) {
      pageNumber = 1;
    }
    this.pageNumber = pageNumber;
  }
  
  public int getPageSize()
  {
    return this.pageSize;
  }
  
  public void setPageSize(int pageSize)
  {
    if ((pageSize < 1) ) {
      pageSize = 200;
    }
    this.pageSize = pageSize;
  }
  
  public boolean equals(Object obj)
  {
    if (obj == null) {
      return false;
    }
    if (getClass() != obj.getClass()) {
      return false;
    }
    if (this == obj) {
      return true;
    }
    Pageable other = (Pageable)obj;
    return new EqualsBuilder().append(getPageNumber(), 
      other.getPageNumber())
      .append(getPageSize(), other.getPageSize())
      
      .isEquals();
  }
  
  public int hashCode()
  {
    return 
    
      new HashCodeBuilder(17, 37).append(getPageNumber()).append(getPageSize()).toHashCode();
  }
}
