package net.shopxx.util.base;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Page<T>
  implements Serializable
{
  private static final long serialVersionUID = -2053800594583879853L;
  
  private  List<T> content = new ArrayList();
  
  private long total;
  
  private final Pageable pageable;
  
  public Page()
  {
    this.total = 0L;
    this.pageable = new Pageable();
  }
  
  public Page(List<T> content, long total, Pageable pageable)
  {
    this.content.addAll(content);
    this.total = total;
    this.pageable = pageable;
  }
  
  public int getPageNumber()
  {
    return this.pageable.getPageNumber();
  }
  
  public int getPageSize()
  {
    return this.pageable.getPageSize();
  }
  
  public int getTotalPages()
  {
    return (int)Math.ceil(getTotal() / getPageSize());
  }
  
  public List<T> getContent()
  {
    return this.content;
  }
  
  public void setContent(List<T> content) {
		this.content = content;
  }
  
  public void setTotal(long total)
  {
    this.total = total;
  }
  
  public long getTotal()
  {
    return this.total;
  }
  
  public Pageable getPageable()
  {
    return this.pageable;
  }
  
  
  
}

