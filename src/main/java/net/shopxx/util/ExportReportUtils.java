package net.shopxx.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * link报表导出通用接口
 */
@Component
public class ExportReportUtils {

    private static Logger logger = LoggerFactory.getLogger(ExportReportUtils.class);

    /**
     *
     * @param map  数据源  map里面包含listMap
     * @param modeName   xml模板名
     * @param fileName  生成的报表文件名
     * @param session
     * @param response
     */
    public static void creatExPort(Map<String,Object> map, String modeName , String fileName, HttpSession session, HttpServletResponse response){
        //获取模板路径
        ServletContext servletContext =session.getServletContext();
        String path= servletContext.getRealPath("/WEB-INF/excelTemplate/"+modeName);
        //获取模板
        TemplateExportParams params = new TemplateExportParams(path);
        //获取文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String time = sdf.format(date);
       String newFileName = fileName+time;
       try {
           //生成模板及数据
           Workbook workbook = ExcelExportUtil.exportExcel(params, map);
           setResponseHeader(response, newFileName);
           OutputStream os = response.getOutputStream();
           workbook.write(os);
           os.flush();
           os.close();
       }catch (Exception e){
           logger.error(fileName+"报表导出异常：",e);
       }
    }


    public static void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            try {
                fileName = new String(fileName.getBytes(),"ISO8859-1");
            } catch (UnsupportedEncodingException e) {
                logger.error("导出异常命名",e);
            }
            response.setContentType("application/octet-stream;charset=ISO8859-1");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName+".xls");
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            logger.error("导出异常",ex);
        }
    }



    /**导出模板设置*/
    public ModelAndView getModelAndViewForList(List<Map<String, Object>> titleList,List<Map<String, Object>> dataList,String name, ModelMap model){

        // 设置导出表格名
        String filename = name+new SimpleDateFormat("yyyy-MM-dd").format(new Date())
                + ".xls";
        // 设置标题
        String[] header = new String[titleList.size()];
        // 设置单元格取值
        String[] properties = new String[titleList.size()];
        //单元格宽度
        Integer[] widths = new Integer[titleList.size()];
        for (int i = 0;i < titleList.size();i++){
            //动态生成表头
            header[i] = String.valueOf(titleList.get(i).get("show_name"));
            //列类型
            String columns_type = String.valueOf(titleList.get(i).get("columns_type"));
            //对应值
            if ("3".equals(columns_type) || "5".equals(columns_type)|| "6".equals(columns_type)){   //列类型未 加后缀 -导出转换数值格式
                properties[i] = titleList.get(i).get("name")+"@";
            }else {
                properties[i] = String.valueOf(titleList.get(i).get("name"));
            }
            //设置宽度
            widths[i] = 25 * 300;
        }

        return new ModelAndView(new ExcelView(filename, null, properties,
                header, widths, null, dataList, null), model);

    }
}
