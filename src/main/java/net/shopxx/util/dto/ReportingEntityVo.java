package net.shopxx.util.dto;

import java.util.List;

/**
 * 报表参数实体
 * 
 * <AUTHOR>
 *
 */
public class ReportingEntityVo {

	private String head; // 报表标题

	private String documentNo; // 单据编号

	private String documentDate; // 单据日期

	private String warehouse; // 仓库

	private String userCode; // 客户编码

	private String abbreviation; // 客户简介

	private String userName; // 客户名称

	private String remarks; // 发运备注

	private String contactInformation; // 联系方式

	private String collectingUnit; // 领用单位

	private String warehouseCategory; // 出仓类别

	private String cabinetNo; // 柜(车)号
	
	private String address; // 地址

    private String examineName;    //审核人

    private String establishName;   //制单人

    private String consignee;   //收货人

    private String shipVia;      //发运方式

    private String driverInfoName;   //司机

    private  String carNumber;  //车牌号

	private List<DocumentDetails> documentDetails; // 单据明细

	
	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCollectingUnit() {
		return collectingUnit;
	}

	public void setCollectingUnit(String collectingUnit) {
		this.collectingUnit = collectingUnit;
	}

	public String getWarehouseCategory() {
		return warehouseCategory;
	}

	public void setWarehouseCategory(String warehouseCategory) {
		this.warehouseCategory = warehouseCategory;
	}

	public String getCabinetNo() {
		return cabinetNo;
	}

	public void setCabinetNo(String cabinetNo) {
		this.cabinetNo = cabinetNo;
	}

	public String getHead() {
		return head;
	}

	public void setHead(String head) {
		this.head = head;
	}

	public String getDocumentNo() {
		return documentNo;
	}

	public void setDocumentNo(String documentNo) {
		this.documentNo = documentNo;
	}

	public String getDocumentDate() {
		return documentDate;
	}

	public void setDocumentDate(String documentDate) {
		this.documentDate = documentDate;
	}

	public String getWarehouse() {
		return warehouse;
	}

	public void setWarehouse(String warehouse) {
		this.warehouse = warehouse;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getAbbreviation() {
		return abbreviation;
	}

	public void setAbbreviation(String abbreviation) {
		this.abbreviation = abbreviation;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getContactInformation() {
		return contactInformation;
	}

	public void setContactInformation(String contactInformation) {
		this.contactInformation = contactInformation;
	}

	public List<DocumentDetails> getDocumentDetails() {
		return documentDetails;
	}

	public void setDocumentDetails(List<DocumentDetails> documentDetails) {
		this.documentDetails = documentDetails;
	}

    public String getExamineName() {
        return examineName;
    }

    public void setExamineName(String examineName) {
        this.examineName = examineName;
    }

    public String getEstablishName() {
        return establishName;
    }

    public void setEstablishName(String establishName) {
        this.establishName = establishName;
    }

    public String getShipVia() {
        return shipVia;
    }

    public void setShipVia(String shipVia) {
        this.shipVia = shipVia;
    }

    public String getConsignee() {
        return consignee;
    }

    public void setConsignee(String consignee) {
        this.consignee = consignee;
    }

    public String getDriverInfoName() {
        return driverInfoName;
    }

    public void setDriverInfoName(String driverInfoName) {
        this.driverInfoName = driverInfoName;
    }

    public String getCarNumber() {
        return carNumber;
    }

    public void setCarNumber(String carNumber) {
        this.carNumber = carNumber;
    }
}
