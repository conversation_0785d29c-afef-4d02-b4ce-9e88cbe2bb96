package net.shopxx.util.dto;

/**
 * 单据明细实体
 * <AUTHOR>
 *
 */
public class DocumentDetails {
	
	//产品编码
	private String  productCode;
	
	//名称
	private String  productName;
	//木种花色
	private String  woodColor;
	//名称或木种
	private String nameOrWoodColor;
	//描述
    private String detailDescription;

	//型号
	private String  model;
	//规格
	private String  specifications;
	//件
	private String  piece;
	//支数
	private String  numberOfPieces;
    //零散支数
	private String scattered;
	//支/件
	private String  branch;
	//数量
	private String  number;
	//金额
	private String  amountOfMoney;
	//单位  
	private String  company;
	//等级 
	private String  grade;
	//色号 
	private String  colorNumber;
	//含水率 
	private String  moistureContent;
	//备注
	private String  remarks;
	
	//事业部
	private String businessDepartment;
	
	
	
	public String getBusinessDepartment() {
		return businessDepartment;
	}
	public void setBusinessDepartment(String businessDepartment) {
		this.businessDepartment = businessDepartment;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public String getWoodColor() {
		return woodColor;
	}
	public void setWoodColor(String woodColor) {
		this.woodColor = woodColor;
	}
	public String getModel() {
		return model;
	}
	public void setModel(String model) {
		this.model = model;
	}
	public String getSpecifications() {
		return specifications;
	}
	public void setSpecifications(String specifications) {
		this.specifications = specifications;
	}
	public String getPiece() {
		return piece;
	}
	public void setPiece(String piece) {
		this.piece = piece;
	}
	public String getNumberOfPieces() {
		return numberOfPieces;
	}
	public void setNumberOfPieces(String numberOfPieces) {
		this.numberOfPieces = numberOfPieces;
	}
	public String getBranch() {
		return branch;
	}
	public void setBranch(String branch) {
		this.branch = branch;
	}
	public String getNumber() {
		return number;
	}
	public void setNumber(String number) {
		this.number = number;
	}
	public String getAmountOfMoney() {
		return amountOfMoney;
	}
	public void setAmountOfMoney(String amountOfMoney) {
		this.amountOfMoney = amountOfMoney;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public String getGrade() {
		return grade;
	}
	public void setGrade(String grade) {
		this.grade = grade;
	}
	public String getColorNumber() {
		return colorNumber;
	}
	public void setColorNumber(String colorNumber) {
		this.colorNumber = colorNumber;
	}
	public String getMoistureContent() {
		return moistureContent;
	}
	public void setMoistureContent(String moistureContent) {
		this.moistureContent = moistureContent;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getNameOrWoodColor() {
		return nameOrWoodColor;
	}
	public void setNameOrWoodColor(String nameOrWoodColor) {
		this.nameOrWoodColor = nameOrWoodColor;
	}

    public String getScattered() {
        return scattered;
    }

    public void setScattered(String scattered) {
        this.scattered = scattered;
    }

    public String getDetailDescription() {
        return detailDescription;
    }

    public void setDetailDescription(String detailDescription) {
        this.detailDescription = detailDescription;
    }
}
