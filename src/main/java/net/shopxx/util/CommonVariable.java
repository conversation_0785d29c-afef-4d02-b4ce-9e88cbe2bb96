package net.shopxx.util;

/**
 *  公共常量类
 * <AUTHOR>
 *
 */
public class CommonVariable {
	
	public static final int ORDER_TYPE = 1;        //订单类型
	
	public static final int NOTICE_TYPE = 2;       //通知单类型
	
	public static final int RETURN_TYPE = 3;       //退货类型
	
	public static final int MOVE_TYPE = 4;        //移库类型
	
	public static final int INANDOUT_TYPE = 5;      //出入库类型
	
	public static final String SYSTEM_CODE = "systemType";     //系统类型编码
	
	public static final int NULL_CODE = 500;      //空指针编码
	
	public static final int SUCCES_CODE = 200;     //成功编码
	
	public static final int EORR_CODE = 400;      //错误编码

	public static final int NEW_WF_TYPE = 1;		//流程类型：新建

	public static final int INVALID_WF_TYPE = 2;		//流程类型：失效

	public static final int MODIFY_WF_TYPE = 3;		//流程类型：修改

}
