package net.shopxx.util;
import java.awt.AWTException;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.Converter;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.junit.Test;
import org.springframework.util.Assert;
import org.springframework.web.servlet.view.document.AbstractExcelView;

import net.shopxx.base.core.util.ConvertUtil;

public class ExcelView extends AbstractExcelView {
    private static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DEFAULT_INTEGER_PATTERN = "^[-\\+]?[\\d]*$";
    private static final String DEFAULT_DOUBLE_PATTERN = "^(-?\\d+)(\\.\\d+)?$";
    private String filename;
    private String sheetName;
    private String[] properties;
    private String[] titles;
    private Integer[] widths;
    private Converter[] converters;
    private Collection<?> data;
    private String[] contents;

    static {
        DateConverter dateConverter = new DateConverter();
        dateConverter.setPattern("yyyy-MM-dd");
        ConvertUtils.register(dateConverter, Date.class);
    }

    public ExcelView(String filename, String sheetName, String[] properties, String[] titles, Integer[] widths, Converter[] converters, Collection<?> data, String[] contents) {
        this.filename = filename;
        this.sheetName = sheetName;
        this.properties = properties;
        this.titles = titles;
        this.widths = widths;
        this.converters = converters;
        this.data = data;
        this.contents = contents;
    }

    public ExcelView(String[] properties, String[] titles, Collection<?> data, String[] contents) {
        this.properties = properties;
        this.titles = titles;
        this.data = data;
        this.contents = contents;
    }

    public ExcelView(String[] properties, String[] titles, Collection<?> data) {
        this.properties = properties;
        this.titles = titles;
        this.data = data;
    }

    public ExcelView(String[] properties, Collection<?> data) {
        this.properties = properties;
        this.data = data;
    }

    public void buildExcelDocument(Map<String, Object> model, HSSFWorkbook workbook, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Assert.notEmpty(this.properties);
        HSSFSheet sheet;
        if (StringUtils.isNotEmpty(this.sheetName)) {
            sheet = workbook.createSheet(this.sheetName);
        } else {
            sheet = workbook.createSheet();
        }

        int rowNumber = 0;
        int i;
        if (this.titles != null && this.titles.length > 0) {
            HSSFRow header = sheet.createRow(rowNumber);
            header.setHeight((short)500);

            for(i = 0; i < this.properties.length; ++i) {
                HSSFCell cell = header.createCell(i);
                HSSFCellStyle cellStyle = workbook.createCellStyle();
                cellStyle.setFillForegroundColor((short)31);
                cellStyle.setFillPattern(FillPatternType.forInt(1));
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                HSSFFont font = workbook.createFont();
                font.setFontHeightInPoints((short)11);
                font.setBold(true);
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                if (this.titles.length > i && this.titles[i] != null) {
                    cell.setCellValue(this.titles[i]);
                } else {
                    cell.setCellValue(this.properties[i]);
                }

                if (this.widths != null && this.widths.length > i && this.widths[i] != null) {
                    sheet.setColumnWidth(i, this.widths[i]);
                } else {
                    sheet.autoSizeColumn(i);
                }
            }

            ++rowNumber;
        }

        HSSFRow row;
        if (this.data != null) {
            HSSFCellStyle cellStyle = workbook.createCellStyle();
            //此处设置数据格式
            HSSFDataFormat df = workbook.createDataFormat(); 
            HSSFFont font = workbook.createFont();
            font.setFontHeightInPoints((short)9);
            cellStyle.setFont(font);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            for(Iterator var21 = this.data.iterator(); var21.hasNext(); ++rowNumber) {
                Object item = var21.next();
                row = sheet.createRow(rowNumber);
                for(i = 0; i < this.properties.length; ++i) {
                    HSSFCell cell = row.createCell(i);
                    if (this.converters != null && this.converters.length > i && this.converters[i] != null) {
                        Class<?> clazz = PropertyUtils.getPropertyType(item, this.properties[i]);
                        ConvertUtils.register(this.converters[i], clazz);
                        cell.setCellValue(BeanUtils.getProperty(item, this.properties[i]));
                        ConvertUtils.deregister(clazz);
                        if (clazz.equals(Date.class)) {
                            DateConverter dateConverter = new DateConverter();
                            dateConverter.setPattern("yyyy-MM-dd");
                            ConvertUtils.register(dateConverter, Date.class);
                        }
                    } else {
                        int indexStr = this.properties[i].indexOf("@");
                        if(indexStr >= 0){
                        	String itemName = BeanUtils.getProperty(item, this.properties[i].substring(0, indexStr));
                            if (!ConvertUtil.isEmpty(itemName)) {
                                //判断是否为数值型
                                if (CommonUtil.regularMatching(DEFAULT_DOUBLE_PATTERN, itemName)) {
                                	//判断data是否为整数（小数部分是否为0）
                            		if(CommonUtil.regularMatching(DEFAULT_INTEGER_PATTERN, itemName)){
                            			//数据格式只显示整数
                            			cellStyle.setDataFormat(df.getBuiltinFormat("0"));
                            		}else{
                            			//保留两位小数点
                            			cellStyle.setDataFormat(df.getBuiltinFormat("0.000000"));
                            		}
                                    // 设置单元格格式
                            		cell.setCellStyle(cellStyle);
                                    // 设置单元格内容为double类型
                            		cell.setCellValue(Double.parseDouble(itemName));
                                }else{
                                	cell.setCellStyle(cellStyle);
                                    // 设置单元格内容为字符型
                                	cell.setCellValue(itemName);
                                }
                            }else{
                            	//数据格式只显示整数
                    			cellStyle.setDataFormat(df.getBuiltinFormat("0"));
                            	cell.setCellStyle(cellStyle);
                                // 设置单元格内容为字符型
                            	cell.setCellValue(itemName);
                            }
                        }else{
                        	cell.setCellStyle(cellStyle);
                            // 设置单元格内容为字符型
                        	cell.setCellValue(BeanUtils.getProperty(item, this.properties[i]));
                        }
                    }
                    if (rowNumber == 0 || rowNumber == 1) {
                        if (this.widths != null && this.widths.length > i && this.widths[i] != null) {
                            sheet.setColumnWidth(i, this.widths[i]);
                        } else {
                            sheet.autoSizeColumn(i);
                        }
                    }
                }
            }
        }

        if (this.contents != null && this.contents.length > 0) {
            ++rowNumber;
            String[] var22;
            int var20 = (var22 = this.contents).length;

            for(i = 0; i < var20; ++i) {
                String content = var22[i];
                row = sheet.createRow(rowNumber);
                HSSFCell cell = row.createCell(0);
                HSSFCellStyle cellStyle = workbook.createCellStyle();
                HSSFFont font = workbook.createFont();
                font.setColor((short)23);
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(content);
                ++rowNumber;
            }
        }

        response.setContentType("application/force-download");
        if (StringUtils.isNotEmpty(this.filename)) {
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(this.filename, "UTF-8"));
        } else {
            response.setHeader("Content-disposition", "attachment");
        }

    }

    public String getFileName() {
        return this.filename;
    }

    public void setFileName(String filename) {
        this.filename = filename;
    }

    public String getSheetName() {
        return this.sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String[] getProperties() {
        return this.properties;
    }

    public void setProperties(String[] properties) {
        this.properties = properties;
    }

    public String[] getTitles() {
        return this.titles;
    }

    public void setTitles(String[] titles) {
        this.titles = titles;
    }

    public Integer[] getWidths() {
        return this.widths;
    }

    public void setWidths(Integer[] widths) {
        this.widths = widths;
    }

    public Converter[] getConverters() {
        return this.converters;
    }

    public void setConverters(Converter[] converters) {
        this.converters = converters;
    }

    public Collection<?> getData() {
        return this.data;
    }

    public void setData(Collection<?> data) {
        this.data = data;
    }

    public String[] getContents() {
        return this.contents;
    }

    public void setContents(String[] contents) {
        this.contents = contents;
    }
}
