package net.shopxx.util;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import net.sf.json.JSONObject;
import net.shopxx.base.core.util.LogUtils;

public class AddressUtils {

	public static String getAddresses(String content, String encodingString) throws UnsupportedEncodingException {
        // 这里调用pconline的接口
        String urlStr = "http://api.map.baidu.com/location/ip";
        urlStr += "?"+content;
        // 从http://whois.pconline.com.cn取得IP所在的省市区信息
        String returnStr = getResult(urlStr, content, encodingString);
        JSONObject json = JSONObject.fromObject(returnStr);  
        if (returnStr != null) {
            // 处理返回的省市区信息
        	LogUtils.info("IP地址返回报文:"+json);
            System.out.println("IP====="+json);
            String[] temp = returnStr.split(",");
            if(temp.length<3){
                return "0";                                        //无效IP，局域网测试
            }
            
            String region = (temp[4].split(":"))[1].replaceAll("\"", "");
            region = decodeUnicode(region);                        // 省
            String cont =json.get("content").toString();
            JSONObject jsons = JSONObject.fromObject(cont);
            String address_detail =jsons.get("address_detail").toString();
            
            JSONObject address = JSONObject.fromObject(address_detail);
            String city = address.get("city").toString();
            String province = address.get("province").toString();
           
            
            
        
           
            return province+city;
        }
        return null;
    }

    
    
    /**
     * @param urlStr
     *   请求的地址
     * @param content
     *   请求的参数 格式为：name=xxx&pwd=xxx
     * @param encoding
     *   服务器端请求编码。如GBK,UTF-8等
     * @return
     */
     
	private static String getResult(String urlStr, String content, String encoding) {
         URL url = null;
         HttpURLConnection connection = null;
         try {
             url = new URL(urlStr);
             connection = (HttpURLConnection) url.openConnection();// 新建连接实例
             connection.setConnectTimeout(2000);                     // 设置连接超时时间，单位毫秒
             connection.setReadTimeout(2000);                        // 设置读取数据超时时间，单位毫秒
             connection.setDoOutput(true);                           // 是否打开输出流 true|false
             connection.setDoInput(true);                            // 是否打开输入流true|false
             connection.setRequestMethod("POST");                    // 提交方法POST|GET
             connection.setUseCaches(false);// 是否缓存true|false
             connection.connect();                                   // 打开连接端口
             DataOutputStream out = new DataOutputStream(connection.getOutputStream());// 打开输出流往对端服务器写数据                            // 写数据,也就是提交你的表单 name=xxx&pwd=xxx
             out.flush();                                            // 刷新
             out.close();                                            // 关闭输出流
             BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), encoding));// 往对端写完数据对端服务器返回数据 ,以BufferedReader流来读取
             StringBuffer buffer = new StringBuffer();
             String line = "";
             while ((line = reader.readLine()) != null) {
                 buffer.append(line);
             }
             reader.close();
             return buffer.toString();
         } catch (IOException e) {
             e.printStackTrace();
         } finally {
             if (connection != null) {
                 connection.disconnect();                            // 关闭连接
             }
         }
         return null;
     }

     /**
      * unicode 转换成 中文
      *
      * <AUTHOR> 2007-3-15
      * @param theString
      * @return
      */
      public static String decodeUnicode(String theString) {
      char aChar;
      int len = theString.length();
      StringBuffer outBuffer = new StringBuffer(len);
      for (int x = 0; x < len;) {
          aChar = theString.charAt(x++);
          if (aChar == '\\') {
              aChar = theString.charAt(x++);
                  if (aChar == 'u') {
                      int value = 0;
                      for (int i = 0; i < 4; i++) {
                          aChar = theString.charAt(x++);
                           switch (aChar) {
                           case '0':
                           case '1':
                           case '2':
                           case '3':
                           case '4':
                           case '5':
                           case '6':
                           case '7':
                           case '8':
                           case '9':
                           value = (value << 4) + aChar - '0';
                           break;
                           case 'a':
                           case 'b':
                           case 'c':
                           case 'd':
                           case 'e':
                           case 'f':
                           value = (value << 4) + 10 + aChar - 'a';
                           break;
                           case 'A':
                           case 'B':
                           case 'C':
                           case 'D':
                           case 'E':
                           case 'F':
                           value = (value << 4) + 10 + aChar - 'A';
                           break;
                           default:
                           throw new IllegalArgumentException(
                            "Malformed  encoding.");
                           }
               }
               outBuffer.append((char) value);
              } else {
                   if (aChar == 't') {
                   aChar = '\t';
                   } else if (aChar == 'r') {
                   aChar = '\r';
                   } else if (aChar == 'n') {
                   aChar = '\n';
                   } else if (aChar == 'f') {
                   aChar = '\f';
                   }
               outBuffer.append(aChar);
              }
          } else {
          outBuffer.append(aChar);
          }
      }
          return outBuffer.toString();
      }
      public static String unicodeToString(String unicode) {
  		StringBuffer sb = new StringBuffer();
  		String[] hex = unicode.split("\\\\u");
  		for (int i = 1; i < hex.length; i++) {
  			int index = Integer.parseInt(hex[i], 16);
  			sb.append((char) index);
  		}
  		return sb.toString();
  	}


	
	

}
