package net.shopxx.homepage.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.homepage.entity.KnowledgeCategory;


import org.springframework.stereotype.Repository;

@Repository("knowledgeCategoryBaseDao")
public class KnowledgeCategoryBaseDao extends DaoCenter {

	public List<Map<String, Object>> findList(String sn, String name,
			Boolean isEnabled) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (ConvertUtil.isEmpty(sn) && ConvertUtil.isEmpty(name)) {
			sql.append(
					"select p.*, 0 as is_search from xx_knowledge_category p where parent is null");
		}
		else {
			sql.append(
					"select p.*, 1 as is_search from xx_knowledge_category p where 1 = 1");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and p.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and p.name like ?");
			list.add("%" + name + "%");
		}
		if (isEnabled != null) {
			if (isEnabled)
				sql.append(" and p.is_enabled = 1");
			else
				sql.append(" and p.is_enabled = 0");
		}
		
		sql.append(" order by p.grade, p.orders asc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> knowledgeCategories = getNativeDao()
				.findListMap(sql.toString(), objs, 0);

		String cSql = "";
		for (Map<String, Object> knowledgeCategory : knowledgeCategories) {
			String tree_path_name = "";
			String tree_path = knowledgeCategory.get("tree_path").toString();
			if (tree_path.length() > 1) {
				tree_path = tree_path.substring(1);
				tree_path = tree_path.substring(0, tree_path.length() - 1);
				cSql = "select group_concat(name separator ' / ') tree_path_name from xx_knowledge_category where id in ("
						+ tree_path
						+ ")";
				tree_path_name = getNativeDao().findString(cSql, null);
			}
			knowledgeCategory.put("tree_path_name", tree_path_name);
		}

		return knowledgeCategories;
	}

	public List<Map<String, Object>> findChildren(Long parentId, String name) {

		String sql = "select p.*, 0 as is_search from xx_knowledge_category p where parent = ?";
		if (!ConvertUtil.isEmpty(name)) {
			sql += " and p.name like '%" + name + "%' ";
		}
		List<Map<String, Object>> knowledgeCategories = getNativeDao()
				.findListMap(sql, new Object[] { parentId }, 0);

		return knowledgeCategories;
	}

	/**
	 * 查找下级商品分类
	 * 
	 * @param productCategory
	 * @param count
	 * @return
	 */
	public List<KnowledgeCategory> findChildren(String sn, String name,
			Boolean isEnabled, KnowledgeCategory knowledgeCategory, int count) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		String sql = "select * from xx_knowledge_category where 1=1";
		if (knowledgeCategory != null) {
			sql += " and tree_path like ?";
			list.add("%"
					+ KnowledgeCategory.TREE_PATH_SEPARATOR
					+ knowledgeCategory.getId()
					+ KnowledgeCategory.TREE_PATH_SEPARATOR
					+ "%");
		}
		if (companyInfoId != null) {
			sql += " and company_info_id = ?";
			list.add(companyInfoId);
		}
		else {
			sql += " and company_info_id is null";
		}
		if (sn != null) {
			sql += " and sn = ?";
			list.add(sn);
		}
		if (name != null) {
			sql += " and name like ?";
			list.add("%" + name + "%");
		}
		if (isEnabled != null) {
			if (isEnabled)
				sql += " and is_enabled = 1";
			else
				sql += " and is_enabled = 0";
		}
		sql += " order by orders asc";

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<KnowledgeCategory> knowledgeCategories = getNativeDao()
				.findListManaged(sql, objs, count, KnowledgeCategory.class);

		return sort(knowledgeCategories, knowledgeCategory);
	}

	/**
	 * 排序知识库分类
	 * 
	 * @param productCategories
	 *            知识库分类
	 * @param parent
	 *            上级知识库分类
	 * @return 知识库分类
	 */
	private List<KnowledgeCategory> sort(List<KnowledgeCategory> knowledgeCategories,
			KnowledgeCategory parent) {
		List<KnowledgeCategory> result = new ArrayList<KnowledgeCategory>();
		if (knowledgeCategories != null) {
			for (KnowledgeCategory knowledgeCategory : knowledgeCategories) {
				if ((knowledgeCategory.getParent() != null
						&& knowledgeCategory.getParent().equals(parent))
						|| (knowledgeCategory.getParent() == null
								&& parent == null)) {
					result.add(knowledgeCategory);
					result.addAll(sort(knowledgeCategories, knowledgeCategory));
				}
			}
		}
		return result;
	}

	/**
	 * 查出所有顶级分类
	 * @return 
	 * */
	public List<Map<String, Object>> finTopCategory(String name) {

		String sql = " select * from xx_knowledge_category where parent is null and company_info_id = "
				+ WebUtils.getCurrentCompanyInfoId();
		if (!ConvertUtil.isEmpty(name)) {
			sql += " and name like '%" + name + "%' ";
		}
		return this.getNativeDao().findListMap(sql, null, 0);
	}
}