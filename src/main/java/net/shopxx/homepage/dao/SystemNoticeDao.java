package net.shopxx.homepage.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.homepage.entity.SystemNotice;
import net.shopxx.member.entity.PcRole;

import org.springframework.stereotype.Repository;

@Repository("systemNoticeDao")
public class SystemNoticeDao extends DaoCenter {

	public Page<Map<String, Object>> findPage(String title,
			String store_member_name, Integer isPublic,
			String knowledgeCategoryName, Integer type, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sb = new StringBuilder(
			   "select sn.id,sn.title,sn.second_title,sn.content,sn.store_member,sn.is_public,sn.public_date,sn.type,sn.knowledge_category,"
		    + "    sn.like_count, sn.comments_count, sn.watch_count, sn.create_date, "    
	        + "    sm.name store_member_name,kc.name knowledge_category_name, sm.image_name "
			+ " from xx_system_notice sn "
			+ " left join xx_store_member sm on sn.store_member=sm.id"
			+ " left join xx_knowledge_category kc on sn.knowledge_category=kc.id"
			+ " where 1=1");

		if (companyInfoId != null) {
			sb.append(" and sn.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (title != null) {
			sb.append(" and sn.title like ?");
			list.add("%" + title + "%");
		}
		if (store_member_name != null) {
			sb.append(" and sm.name like ?");
			list.add("%" + store_member_name + "%");
		}

		if (isPublic != null) {
			sb.append(" and sn.is_public = ?");
			list.add(isPublic);
		}
		if (type != null) {
			sb.append(" and sn.type = ?");
			list.add(type);
		}
		if (knowledgeCategoryName != null) {
			sb.append(" and kc.name = ?");
			list.add(knowledgeCategoryName);
		}

		sb.append(" order by sn.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sb.toString(),
				objs,
				pageable);

		return page;
	}

	public Page<Map<String, Object>> findPageForKnowledge(String title,
			String store_member_name, Integer isPublic,
			String knowledgeCategoryName, Integer type, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sb = new StringBuilder(
				"select sn.id,sn.title,sn.second_title,sn.store_member,sn.is_public,sn.public_date,sn.type,sn.knowledge_category,sm.name store_member_name,kc.name knowledge_category_name"
						+ " from xx_system_notice sn "
						+ " left join xx_store_member sm on sn.store_member=sm.id"
						+ " left join xx_knowledge_category kc on sn.knowledge_category=kc.id"
						+ " where 1=1");

		if (companyInfoId != null) {
			sb.append(" and sn.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (title != null) {
			sb.append(" and sn.title like ?");
			list.add("%" + title + "%");
		}
		if (store_member_name != null) {
			sb.append(" and sm.name like ?");
			list.add("%" + store_member_name + "%");
		}

		if (isPublic != null) {
			sb.append(" and sn.is_public = ?");
			list.add(isPublic);
		}
		if (type != null) {
			sb.append(" and sn.type = ?");
			list.add(type);
		}
		if (knowledgeCategoryName != null) {
			sb.append(" and kc.name = ?");
			list.add(knowledgeCategoryName);
		}
		sb.append(" order by sn.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sb.toString(),
				objs,
				pageable);

		return page;
	}

	/**
	 * 根据公告Id获取该公告下的用户角色
	 * @param systemNoticeId
	 * @return
	 */
	public List<PcRole> findRoleBySystemNoticeId(Long systemNoticeId) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder(
				"SELECT xpr.* FROM xx_pc_role xpr WHERE id IN(SELECT pc_role FROM xx_role_authority WHERE 1=1");
		if (systemNoticeId != null) {
			sql.append(" and system_notice = ?");
			list.add(systemNoticeId);
		}
		sql.append(")");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<PcRole> roleList = getNativeDao().findListManaged(sql.toString(),
				new Object[] { systemNoticeId },
				0,
				PcRole.class);
		return roleList;
	}

	/**
	 * 根据公告Id获取该公告下的机构
	 * @param systemNoticeId
	 * @return
	 */
	public List<Map<String, Object>> findSaleOrgBySystemNoticeId(
			Long systemNoticeId) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("select s.*,sd.value");
		sql.append(" from xx_sale_org s");
		sql.append(" LEFT JOIN xx_system_dict sd ON s.type = sd.id AND sd.parent IS NOT NULL");
		sql.append(" where 1 = 1");
		sql.append(" AND s.id IN (SELECT sale_org FROM xx_sale_org_authority WHERE 1=1");
		if (systemNoticeId != null) {
			sql.append(" and system_notice = ?");
			list.add(systemNoticeId);
		}
		sql.append(")");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return maps;
	}

	/**
	 * 根据公告Id获取该公告下的企业用户
	 * @param systemNoticeId
	 * @return
	 */
	public List<Map<String, Object>> findStoreMemberBySystemNoticeId(
			Long systemNoticeId) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT s.*,group_concat(distinct a.name) pc_role_name");
		sql.append(" FROM xx_store_member s");
		sql.append(" LEFT JOIN xx_pc_user_role uo ON s.id = uo.store_member");
		sql.append(" LEFT JOIN xx_pc_role a ON a.id=uo.pc_role");
		sql.append(" LEFT JOIN xx_store ss ON s.store=ss.id");
		sql.append(" WHERE ss.is_main_store = 1 AND s.member_type = 0 AND s.id IN (SELECT store_member FROM xx_member_authority WHERE 1=1");

		if (systemNoticeId != null) {
			sql.append("  AND system_notice = ?");
			list.add(systemNoticeId);
		}
		sql.append(") GROUP BY s.id");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return maps;
	}

	/**
	 * 根据公告Id获取该公告下的外部用户
	 * @param systemNoticeId
	 * @return
	 */
	public List<Map<String, Object>> findStoreMemberOutBySystemNoticeId(
			Long systemNoticeId) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT s.*,group_concat(distinct a.name) pc_role_name");
		sql.append(" FROM xx_store_member s");
		sql.append(" LEFT JOIN xx_pc_user_role uo ON s.id = uo.store_member");
		sql.append(" LEFT JOIN xx_pc_role a ON a.id=uo.pc_role");
		sql.append(" LEFT JOIN xx_store ss ON s.store=ss.id");
		sql.append(" WHERE ss.is_main_store = 1 AND s.member_type = 1 AND s.id IN (SELECT store_member FROM xx_member_out_authority WHERE 1=1");

		if (systemNoticeId != null) {
			sql.append("  AND system_notice = ?");
			list.add(systemNoticeId);
		}
		sql.append(") GROUP BY s.id");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return maps;
	}

	/**
	 * 根据当前登陆用户获取查看公告权限
	 * @param systemNoticeId
	 * @return
	 */
	public List<SystemNotice> getSystemNoticeById(Long storeMemberId) {

		StringBuilder sql = new StringBuilder();

		sql.append("SELECT * FROM xx_system_notice WHERE `type` = 0");
		sql.append(" AND id IN (SELECT system_notice FROM xx_member_authority WHERE store_member = "
				+ storeMemberId
				+ ")");
		sql.append(" OR id IN (SELECT system_notice FROM xx_member_out_authority WHERE store_member = "
				+ storeMemberId
				+ ")");
		sql.append(" OR id IN (SELECT system_notice FROM xx_sale_org_authority WHERE sale_org IN (SELECT id FROM xx_sale_org WHERE id IN (SELECT sale_org FROM xx_store_member_sale_org WHERE store_member = "
				+ storeMemberId
				+ ")))");
		sql.append(" OR id IN (SELECT system_notice FROM xx_role_authority WHERE pc_role IN (SELECT id FROM xx_pc_role WHERE company_info_id IS NOT NULL AND id IN (SELECT pc_role FROM xx_pc_user_role WHERE store_member = "
				+ storeMemberId
				+ ")))");
		sql.append(" ORDER BY orders ASC");

		List<SystemNotice> systemNoticeList = getNativeDao().findListManaged(sql.toString(),
				null,
				0,
				SystemNotice.class);
		return systemNoticeList;
	}
}
