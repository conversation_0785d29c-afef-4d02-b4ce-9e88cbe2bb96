package net.shopxx.homepage.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.homepage.entity.MemberAuthority;
import net.shopxx.homepage.entity.MemberOutAuthority;
import net.shopxx.homepage.entity.RoleAuthority;
import net.shopxx.homepage.entity.SaleOrgAuthority;
import net.shopxx.homepage.entity.SystemNotice;
import net.shopxx.homepage.service.SystemNoticeService;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store.Type;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcRoleBaseService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;

@Controller("homepageSystemNoticeController")
@RequestMapping( { "/homepage/systemNotice" })
public class SystemNoticeController extends BaseController {

	@Resource(name = "systemNoticeServiceImpl")
	private SystemNoticeService systemNoticeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcRoleBaseServiceImpl")
	private PcRoleBaseService pcRoleBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/homepage/systemNotice/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/homepage/systemNotice/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg list_data(String title, String store_member_name,
			Integer isPublic,Integer type,String knowledgeCategoryName, Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = systemNoticeService.findPage(title,
				store_member_name,
				isPublic,
				knowledgeCategoryName,
				type,
				pageable);
		String jsonPage = JsonUtils.toJson(page);
		return ResultMsg.success(jsonPage);
	}
	
	/**
	 * 进入添加页面
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long id, ModelMap model) {

		//角色
		List<PcRole> roles = pcRoleBaseService.findAll();
		model.addAttribute("roles", roles);
		
		return "/homepage/systemNotice/add";
	}

	/**
	 * 进入编辑页面
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		
		//公告
		SystemNotice systemNotice = systemNoticeService.find(id);
		model.addAttribute("systemNotice", systemNotice);
		
		//该公告选择的角色
		List<PcRole> uRoles = systemNoticeService.findRoleBySystemNoticeId(id);
		model.addAttribute("uRoles", uRoles);

		//所有角色
		List<PcRole> roles = pcRoleBaseService.findAll();
		model.addAttribute("roles", roles);
		
		//机构
		List<Map<String, Object>> storeMemberSaleOrgs = systemNoticeService.findSaleOrgBySystemNoticeId(id);
		String storeMemberSaleOrgs_json = JsonUtils.toJson(storeMemberSaleOrgs);
		model.addAttribute("storeMemberSaleOrgs_json", storeMemberSaleOrgs_json);
		
		//企业用户
		List<Map<String, Object>> storeMembers = systemNoticeService.findStoreMemberBySystemNoticeId(id);
		String storeMembers_json = JsonUtils.toJson(storeMembers);
		model.addAttribute("storeMembers_json", storeMembers_json);
		
		//外部用户
		List<Map<String, Object>> storeMembersOut = systemNoticeService.findStoreMemberOutBySystemNoticeId(id);
		String storeMembersOut_json = JsonUtils.toJson(storeMembersOut);
		model.addAttribute("storeMembersOut_json", storeMembersOut_json);
		
		return "/homepage/systemNotice/edit";
	}
	
	/**
	 * 保存
	 * @throws UnsupportedEncodingException 
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg save(SystemNotice systemNotice, Long[] roleId, String en_content) throws UnsupportedEncodingException {
		
		if(systemNotice.getTitle()==null)return error("请维护标题！");
		if (en_content == null)return error("请维护内容！");
		
		String de_content = URLDecoder.decode(en_content,
		"UTF-8");
		systemNotice.setContent(de_content);
		systemNotice.setStoreMember(storeMemberBaseService.getCurrent());

		//企业用户权限
		List<MemberAuthority> memberAuthorityList =  systemNotice.getMemberAuthority();
		for (Iterator<MemberAuthority> iterator = memberAuthorityList.iterator(); iterator.hasNext();) {
			MemberAuthority memberAuthority = (MemberAuthority) iterator.next();
			StoreMember sm = memberAuthority.getStoreMember();
			if (memberAuthority == null || (sm == null || sm.getId() == null)) {
				iterator.remove();
				continue;
			}
			StoreMember storeMember = storeMemberBaseService.find(memberAuthority.getStoreMember().getId());
			memberAuthority.setStoreMember(storeMember);;
			memberAuthority.setSystemNotice(systemNotice);
		}
		systemNotice.setMemberAuthority(memberAuthorityList);
		
		//外部用户权限
		List<MemberOutAuthority> memberOutAuthorityList =  systemNotice.getMemberOutAuthority();
		for (Iterator<MemberOutAuthority> iterator = memberOutAuthorityList.iterator(); iterator.hasNext();) {
			MemberOutAuthority memberOutAuthority = (MemberOutAuthority) iterator.next();
			StoreMember sm = memberOutAuthority.getStoreMember();
			if (memberOutAuthority == null || (sm == null || sm.getId() == null)) {
				iterator.remove();
				continue;
			}
			StoreMember storeMember = storeMemberBaseService.find(memberOutAuthority.getStoreMember().getId());
			memberOutAuthority.setStoreMember(storeMember);;
			memberOutAuthority.setSystemNotice(systemNotice);
		}
		systemNotice.setMemberOutAuthority(memberOutAuthorityList);
		
		//机构权限
		List<SaleOrgAuthority> saleOrgAuthorityList =  systemNotice.getSaleOrgAuthority();
		for (Iterator<SaleOrgAuthority> iterator = saleOrgAuthorityList.iterator(); iterator.hasNext();) {
			SaleOrgAuthority saleOrgAuthority = (SaleOrgAuthority) iterator.next();
			SaleOrg so = saleOrgAuthority.getSaleOrg();
			if (saleOrgAuthority == null || (so == null || so.getId() == null)) {
				iterator.remove();
				continue;
			}
			SaleOrg saleOrg = saleOrgBaseService.find(saleOrgAuthority.getSaleOrg().getId());
			saleOrgAuthority.setSaleOrg(saleOrg);
			saleOrgAuthority.setSystemNotice(systemNotice);
		}
		systemNotice.setSaleOrgAuthority(saleOrgAuthorityList);
		
		//角色权限
		List<PcRole> rolesList = new ArrayList<PcRole>();
		if (roleId != null && roleId.length > 0) {
			for (Long id : roleId) {
				PcRole pcRole = pcRoleBaseService.find(id);
				if(pcRole != null) {
					rolesList.add(pcRole);
				}
			}
		}
		List<RoleAuthority> roleAuthorityList =  systemNotice.getRoleAuthority();
		for (PcRole pcRole : rolesList) {
			RoleAuthority roleAuthority = new RoleAuthority();
			roleAuthority.setPcRole(pcRole);
			roleAuthority.setSystemNotice(systemNotice);
			roleAuthorityList.add(roleAuthority);
		}
		systemNotice.setRoleAuthority(roleAuthorityList);	
		
		systemNoticeService.save(systemNotice);

		return success().addObjX(systemNotice.getId());
	}

	/**
	 * 更新
	 * @throws UnsupportedEncodingException 
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg update(SystemNotice systemNotice, Long[] roleId, String en_content) throws UnsupportedEncodingException {

		SystemNotice systemNoticeNew = systemNoticeService.find(systemNotice.getId());
		
		if(systemNotice.getTitle()==null)return error("请维护标题！");
		if (en_content == null)return error("请维护内容！");
		
		String de_content = URLDecoder.decode(en_content,
		"UTF-8");
		systemNotice.setContent(de_content);
		systemNotice.setStoreMember(storeMemberBaseService.getCurrent());
		
		//机构权限
		List<SaleOrgAuthority> saleOrgAuthorityList =  systemNotice.getSaleOrgAuthority();
		for (Iterator<SaleOrgAuthority> iterator = saleOrgAuthorityList.iterator(); iterator.hasNext();) {
			SaleOrgAuthority saleOrgAuthority = (SaleOrgAuthority) iterator.next();
			if (saleOrgAuthority.getSaleOrg() == null || (saleOrgAuthority.getSaleOrg() != null && saleOrgAuthority.getSaleOrg().getId() == null)) {
				iterator.remove();
				continue;
			}
			SaleOrg saleOrg = saleOrgBaseService.find(saleOrgAuthority.getSaleOrg().getId());
			saleOrgAuthority.setSaleOrg(saleOrg);
			saleOrgAuthority.setSystemNotice(systemNoticeNew);
		}
		systemNoticeNew.getSaleOrgAuthority().clear();
		systemNoticeNew.getSaleOrgAuthority().addAll(saleOrgAuthorityList);
		systemNotice.setSaleOrgAuthority(saleOrgAuthorityList);
		
		//企业用户权限
		List<MemberAuthority> memberAuthorityList =  systemNotice.getMemberAuthority();
		for (Iterator<MemberAuthority> iterator = memberAuthorityList.iterator(); iterator.hasNext();) {
			MemberAuthority memberAuthority = (MemberAuthority) iterator.next();
			if (memberAuthority.getStoreMember() == null || (memberAuthority.getStoreMember() != null && memberAuthority.getStoreMember().getId() == null)) {
				iterator.remove();
				continue;
			}
			StoreMember storeMember = storeMemberBaseService.find(memberAuthority.getStoreMember().getId());
			memberAuthority.setStoreMember(storeMember);
			memberAuthority.setSystemNotice(systemNoticeNew);
		}
		systemNoticeNew.getMemberAuthority().clear();
		systemNoticeNew.getMemberAuthority().addAll(memberAuthorityList);
		systemNotice.setMemberAuthority(memberAuthorityList);
		
		//外部用户权限
		List<MemberOutAuthority> memberOutAuthorityList =  systemNotice.getMemberOutAuthority();
		for (Iterator<MemberOutAuthority> iterator = memberOutAuthorityList.iterator(); iterator.hasNext();) {
			MemberOutAuthority memberOutAuthority = (MemberOutAuthority) iterator.next();
			if (memberOutAuthority.getStoreMember() == null || (memberOutAuthority.getStoreMember() != null && memberOutAuthority.getStoreMember().getId() == null)) {
				iterator.remove();
				continue;
			}
			StoreMember storeMember = storeMemberBaseService.find(memberOutAuthority.getStoreMember().getId());
			memberOutAuthority.setStoreMember(storeMember);;
			memberOutAuthority.setSystemNotice(systemNoticeNew);
		}
		systemNoticeNew.getMemberOutAuthority().clear();
		systemNoticeNew.getMemberOutAuthority().addAll(memberOutAuthorityList);
		systemNotice.setMemberOutAuthority(memberOutAuthorityList);
		
		//角色权限
		List<PcRole> rolesList = new ArrayList<PcRole>();
		if (roleId != null && roleId.length > 0) {
			for (Long id : roleId) {
				PcRole pcRole = pcRoleBaseService.find(id);
				if(pcRole != null) {
					rolesList.add(pcRole);
				}
			}
		}
		List<RoleAuthority> roleAuthorityList =  systemNotice.getRoleAuthority();
		for (PcRole pcRole : rolesList) {
			RoleAuthority roleAuthority = new RoleAuthority();
			roleAuthority.setPcRole(pcRole);
			roleAuthority.setSystemNotice(systemNotice);
			roleAuthorityList.add(roleAuthority);
		}
		systemNotice.setRoleAuthority(roleAuthorityList);
		
		systemNoticeService.update(systemNotice);
		return success();
	}
	
	/**
	 * 查看公告详情
	 */
	@RequestMapping(value = "/notice", method = RequestMethod.GET)
	public String notice(Long id, ModelMap model) {

		SystemNotice systemNotice = systemNoticeService.find(id);
		model.addAttribute("systemNotice", systemNotice);
		return "/homepage/systemNotice/notice";
	}
	
	/**
	 * 进入选择用户列表页面
	 */
	@RequestMapping(value = "/select_store", method = RequestMethod.GET)
	public String selectStore(Pageable pageable, Integer multi, Type type,Integer memberType,
			Long saleOrgId, Integer isSelect, Integer isCustomer, ModelMap model) {

		model.addAttribute("multi", multi);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("isSelect", isSelect);
		model.addAttribute("isCustomer", isCustomer);
		model.addAttribute("memberType", memberType);
		List<Integer> types = storeBaseService.getTypes();
		model.addAttribute("types", types);
		
		return "/homepage/systemNotice/select_store";
	}
}
