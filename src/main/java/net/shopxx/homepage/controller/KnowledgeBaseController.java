package net.shopxx.homepage.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.homepage.entity.KnowledgeCategory;
import net.shopxx.homepage.entity.SystemNotice;
import net.shopxx.homepage.service.KnowledgeCategoryBaseService;
import net.shopxx.homepage.service.SystemNoticeService;
import net.shopxx.member.service.StoreMemberBaseService;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 知识库
 * <AUTHOR>
 *
 */
@Controller("knowledgeBaseController")
@RequestMapping({ "/homepage/knowledgeBase" })
public class KnowledgeBaseController extends BaseController {

	@Resource(name = "systemNoticeServiceImpl")
	private SystemNoticeService systemNoticeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "knowledgeCategoryBaseServiceImpl")
	private KnowledgeCategoryBaseService knowledgeCategoryBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer notice, ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		model.addAttribute("notice", notice);
		return "/homepage/knowledgeBase/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, Integer notice, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		model.addAttribute("notice", notice);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/homepage/knowledgeBase/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg list_data(String title, String store_member_name,
			Integer isPublic, String knowledgeCategoryName, Integer type,
			Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = systemNoticeService.findPageForKnowledge(title,
				store_member_name,
				isPublic,
				knowledgeCategoryName,
				type,
				pageable);
		String jsonPage = JsonUtils.toJson(page);
		return ResultMsg.success(jsonPage);
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long id, ModelMap model) {

		return "/homepage/knowledgeBase/add";
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {

		SystemNotice systemNotice = systemNoticeService.find(id);
		model.addAttribute("systemNotice", systemNotice);
		return "/homepage/knowledgeBase/edit";
	}

	/**
	 * 更新
	 * @throws UnsupportedEncodingException 
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg save(SystemNotice systemNotice, Long knowledgeCategoryId,
			String en_content) throws UnsupportedEncodingException {

		if (systemNotice.getTitle() == null) return error("请维护标题！");
		if (en_content == null) return error("请维护内容！");
		if (knowledgeCategoryId == null) return error("请选择知识库分类！");

		String de_content = URLDecoder.decode(en_content, "UTF-8");
		systemNotice.setContent(de_content);
		systemNotice.setStoreMember(storeMemberBaseService.getCurrent());

		// 知识库分类
		KnowledgeCategory knowledgeCategory = knowledgeCategoryBaseService.find(knowledgeCategoryId);

		systemNotice.setKnowledgeCategory(knowledgeCategory);
		systemNoticeService.save(systemNotice);

		return success().addObjX(systemNotice.getId());
	}

	/**
	 * 更新
	 * @throws UnsupportedEncodingException 
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg update(SystemNotice systemNotice,
			Long knowledgeCategoryId, String en_content)
			throws UnsupportedEncodingException {

		if (systemNotice.getTitle() == null) return error("请维护标题！");
		if (en_content == null) return error("请维护内容！");
		if (knowledgeCategoryId == null) return error("请维护知识库分类！");
		KnowledgeCategory knowledgeCategory = knowledgeCategoryBaseService.find(knowledgeCategoryId);
		systemNotice.setKnowledgeCategory(knowledgeCategory);
		String de_content = URLDecoder.decode(en_content, "UTF-8");
		systemNotice.setContent(de_content);
		systemNoticeService.update(systemNotice, "storeMember");

		return success();
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/notice", method = RequestMethod.GET)
	public String notice(Long id, ModelMap model) {

		SystemNotice systemNotice = systemNoticeService.find(id);
		model.addAttribute("systemNotice", systemNotice);
		return "/homepage/knowledgeBase/knowledge";
	}

	/**
	 * 获取分类节点
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getNodes", method = RequestMethod.POST)
	public @ResponseBody
	List<Object> getNodes(Long id, String keyword) {
		List<Object> objs = new ArrayList<Object>();
		if (keyword != null) {

		}
		else {
			KnowledgeCategory knowledgeCategory = knowledgeCategoryBaseService.find(id);
			if (knowledgeCategory != null) {
				for (KnowledgeCategory child : knowledgeCategory.getChildren()) {
					if (child.getIsEnabled() != null && child.getIsEnabled()) {
						Map<String, Object> data = new HashMap<String, Object>();
						data.put("id", child.getId());
						data.put("name", child.getName());
						data.put("isParent", !getIsLeft(child));
						objs.add(data);
					}
				}

			}
			else {
				List<Filter> fis = new ArrayList<Filter>();
				fis.add(Filter.isNull("parent"));
				fis.add(Filter.eq("isEnabled", true));
				for (KnowledgeCategory child : knowledgeCategoryBaseService.findList(null,
						fis,
						null)) {
					Map<String, Object> data = new HashMap<String, Object>();
					data.put("id", child.getId());
					data.put("name", child.getName());
					data.put("isParent", !getIsLeft(child));
					objs.add(data);
				}
			}
		}
		return objs;
	}

	public boolean getIsLeft(KnowledgeCategory knowledgeCategory) {
		boolean isLeft = knowledgeCategory.getIsLeaf();
		if (!isLeft) {
			long count = knowledgeCategoryBaseService.count(Filter.eq("parent",
					knowledgeCategory), Filter.eq("isEnabled", true));
			if (count == 0) {
				isLeft = true;
			}
		}
		return isLeft;
	}

}
