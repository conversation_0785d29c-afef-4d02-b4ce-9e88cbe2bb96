package net.shopxx.order.entity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.basic.entity.SystemDict;
import net.shopxx.product.entity.Product;
import net.shopxx.wf.entity.WfBillEntity;

/**
 * Entity - 订单关闭
 */
@Entity
@Table(name = "xx_order_close_item")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_order_close_item_sequence")
public class OrderCloseItem extends WfBillEntity {

	private static final long serialVersionUID = 8370942500343156156L;

	/** 订单编号 */
	private String orderSn;

	/** 订单明细id */
	private Long orderItemId;

	/** 产品 */
	private Product product;

	/** 下单价格 */
	private BigDecimal price;

	/** 关闭支数 */
	private BigDecimal branchQuantity;

	/** 关闭平方数 */
	private BigDecimal quantity;

	// 每支单位数
	private BigDecimal perBranch;

	/** 变更单 */
	private OrderClose orderClose;
	
	/**产品级别*/
	private SystemDict productLevel;
	

	/**
	 * 获取 订单编号
	 * 
	 * @return orderSn
	 */
	public String getOrderSn() {
		return orderSn;
	}

	/**
	 * 设置 订单编号
	 * 
	 * @param orderSn
	 *            订单编号
	 */
	public void setOrderSn(String orderSn) {
		this.orderSn = orderSn;
	}

	/**
	 * 获取 订单明细id
	 * 
	 * @return orderItemId
	 */
	public Long getOrderItemId() {
		return orderItemId;
	}

	/**
	 * 设置 订单明细id
	 * 
	 * @param orderItemId
	 *            订单明细id
	 */
	public void setOrderItemId(Long orderItemId) {
		this.orderItemId = orderItemId;
	}

	/**
	 * 获取 产品
	 * 
	 * @return product
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Product getProduct() {
		return product;
	}

	/**
	 * 设置 产品
	 * 
	 * @param product
	 *            产品
	 */
	public void setProduct(Product product) {
		this.product = product;
	}

	/**
	 * 获取下单价格
	 * 
	 * @return afterPrice
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	/**
	 * 获取关闭平方数
	 * 
	 * @return afterPrice
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	/**
	 * 获取关闭支数
	 * 
	 * @return afterPrice
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getBranchQuantity() {
		return branchQuantity;
	}

	public void setBranchQuantity(BigDecimal branchQuantity) {
		this.branchQuantity = branchQuantity;
	}

	/**
	 * 获取每支平方数
	 * 
	 * @return afterPrice
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getPerBranch() {
		return perBranch;
	}

	public void setPerBranch(BigDecimal perBranch) {
		this.perBranch = perBranch;
	}

	/**
	 * 获取 关闭单
	 * 
	 * @return orderChange
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public OrderClose getOrderClose() {
		return orderClose;
	}

	/**
	 * 设置 关闭单
	 * 
	 * @param orderChange
	 */
	public void setOrderClose(OrderClose orderClose) {
		this.orderClose = orderClose;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getProductLevel() {
		return productLevel;
	}

	public void setProductLevel(SystemDict productLevel) {
		this.productLevel = productLevel;
	}
	
}