package net.shopxx.order.entity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.finance.entity.PolicyProduct;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.product.entity.ProductStore;
import net.shopxx.stock.entity.Stock;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseBillBatch;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Entity - 订单项
 */
@Entity
@Table(name = "xx_order_item")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_order_item_sequence")
public class OrderItem extends BaseEntity {

	private static final long serialVersionUID = -4999926022604479334L;

	/** 商品编号 */
	private String sn;

	/** 厂商编号 */
	private String vonderCode;

	/** 商品型号 */
	private String model;

	/** 商品名称 */
	private String name;

	/** 商品全称 */
	private String fullName;

	/** 商品缩略图 */
	private String thumbnail;

	/** 条形码编号 */
	private String barCode;

	/** 商品 */
	private Product product;

	/** 客户商品关系 */
	private ProductStore productStore;

	/** 商品价格 */
	private BigDecimal price;

	/** 商品下单时价格 */
	private BigDecimal origPrice;

	/** 商品下单时会员价格 */
	private BigDecimal origMemberPrice;

	/** 下单数量 */
	private BigDecimal quantity;

	/** 计划发货数量 */
	private BigDecimal shipPlanQuantity;

	/** 实际发货数量 */
	private BigDecimal shippedQuantity;

	/** 退款/退货数 */
	private BigDecimal returnQuantity;

	/** 交货期 */
	private Date deliveryTime;

	/** 订单 */
	private Order order;

	/** 仓库 */
	private Warehouse warehouse;

	/** 库存 */
	private Stock stock;

	/** 外部订单号(来源单号) */
	private String outTradeNo;

	/** 来源接口明细ID */
	private String outTradeId;

	/** 其他要求(定制) */
	private String demand;

	/** 上级订单项 */
	private OrderItem parent;

	/** 0普通产品 1上级产品 2下级产品 3虚拟件 */
	private Integer bomFlag;

	/** 计划下单数量 */
	private BigDecimal planQuantity;

	/** 序号 */
	private Integer seq;

	/** 物料分类 */
	private ProductCategory productCategory;

	/** 备注 */
	private String memo;

	/** 特价单号 */
	private String priceApplySn;

	/** 特价单明细 */
	private PriceApplyItem priceApplyItem;

	/** 预计交付时间 */
	private Date deliverDate;

	/** 买家备注 */
	private String buyerMemo;

	/** 卖家备注 */
	private String sellerMemo;

	/** 采购明细来源(销售明细Id) */
	private Long sourceOrderItemId;

	/** 采购明细来源(销售单号) */
	private String sourceOrderSn;

	/** 1销售转采购 */
	private Integer isPurchase;

	/** 订单明细关联表 */
	List<OrderItemParts> orderItemPartses = new ArrayList<OrderItemParts>();

	/** 订单明细附件 */
	List<OrderItemAttach> orderItemAttachs = new ArrayList<OrderItemAttach>();
	
	/** 订单明细批次 */
	List<WarehouseBillBatch> warehouseBillBatchs = new ArrayList<WarehouseBillBatch>();
	
	
	/**
	 * 18-07-25 新加字段
	 */

	// 下单箱数
	private BigDecimal boxQuantity;

	// 支数 (下单箱数*每箱支数)
	private BigDecimal branchQuantity;

	// 零散支数
	private BigDecimal scatteredQuantity;

	// 每支单位数
	private BigDecimal perBranch;

	// 每箱支数
	private BigDecimal branchPerBox;

	/** 产品级别:1 优等品,2 二等品 */
	private Integer productGrade;

	// 木种/花色
	private String woodTypeOrColor;

	// 物料说明
	private String description;

	/** 政策类型 0.投款政策 1.提货政策 2.促销政策 3.满赠政策 4.返利（大自然） */
	private Integer applyType;

	/** 政策单号 */
	private String policySn;

	/** 政策明细 */
	private PolicyProduct policyProduct;

	/** 申请价格 */
	private BigDecimal applyPrice;

	/** 政策点数 */
	private BigDecimal policyPoint;
	/** 工厂 */
	private SystemDict factorySystemDict;

	/** 折扣后金额 */
	private BigDecimal zhekouAmount;

	/** 折扣后单价 */
	private BigDecimal zhekouPrice;

	//2018-09-14 大自然新加字段

	/** 平台结算价 */
	private BigDecimal saleOrgPrice;

	/** 部件序号 */
	private Integer partsSeq;
	
	//2019-05-17 冯旗 壁纸增加折扣率
	/** 折扣率*/
	private BigDecimal discount;
	
	//2019-05-17 冯旗 价格表原始价格
	/** 价格表原价格*/
    private BigDecimal proPriceHeadPrice;
    
    //2019-05-23冯旗 长
    /** 长*/
    private BigDecimal length;
    
    
    
    //2019-05-23冯旗 宽
    /** 宽*/
    private BigDecimal width;
    
    
	/** 折扣方案*/
	private SystemDict discountCode;
	
	
	//2019-11-07 
	/**色号*/
	private String colourNumber;
	
	/**含水率*/
	private String moistureContent;
	
	/**批次*/
	private String batch;
	
	/**批次编码*/
	private String batchEncoding;
	
	/**产品级别*/
	private SystemDict productLevel;
	
	/**经销商合同明细*/
	private CustomerContractItem customerContractItem;
	
	/**产品经营组织*/
	private Organization productOrganization;
	
	/**色号*/
	private SystemDict colourNumbers;
	
	/**含水率*/
	private SystemDict moistureContents;
	
	/** 旗标 */
	public Integer flag;

    /**  产品部结算价 */
    private  BigDecimal productOrgPrice;

     /**  明细行价格对应的价格表ID */
    private  Long priceId;

    /** 原平台结算价价*/
    private BigDecimal reSaleOrgPrice;

    /**  原产品部结算价 */
    private  BigDecimal reProductOrgPrice;

    /** 销售价差*/
    private BigDecimal priceDifference;
	/**
	 * 专项钢扣价差
	 */
    private BigDecimal zxgk;
	/**
	 * 钢扣结算价差
	 */
	private BigDecimal gkjsjc;
	/**
	 * 专项平台方案价差
	 */
    private BigDecimal zxptjc;
	/**
	 * 其他专项
	 */
    private BigDecimal qtzx;

	/**
	 * 优惠项目说明
	 */
	private SystemDict discountProjectDescription;

    /** 结算价价差*/
    private BigDecimal orgPriceDifference;
    
	/**
	 * 获取商品编号
	 * 
	 * @return 商品编号
	 */
	@JsonProperty
	@Column(nullable = false, updatable = false)
	public String getSn() {
		return sn;
	}

	/**
	 * 设置商品编号
	 * 
	 * @param sn
	 *            商品编号
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	/**
	 * 获取商品名称
	 * 
	 * @return 商品名称
	 */
	@JsonProperty
	@Column(nullable = false)
	public String getName() {
		return name;
	}

	/**
	 * 设置商品名称
	 * 
	 * @param name
	 *            商品名称
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 获取商品全称
	 * 
	 * @return 商品全称
	 */
	@JsonProperty
	@Column(nullable = false, updatable = false)
	public String getFullName() {
		return fullName;
	}

	/**
	 * 设置商品全称
	 * 
	 * @param fullName
	 *            商品全称
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	/**
	 * 获取商品价格
	 * 
	 * @return 商品价格
	 */
	@JsonProperty
	@NotNull
	@Min(0)
	@Digits(integer = 12, fraction = 3)
	@Column(nullable = false, precision = 21, scale = 6)
	public BigDecimal getPrice() {
		return price;
	}

	/**
	 * 设置商品价格
	 * 
	 * @param price
	 *            商品价格
	 */
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	/**
	 * 获取商品缩略图
	 * 
	 * @return 商品缩略图
	 */
	@JsonProperty
	@Column(updatable = false)
	public String getThumbnail() {
		return thumbnail;
	}

	/**
	 * 设置商品缩略图
	 * 
	 * @param thumbnail
	 *            商品缩略图
	 */
	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	/**
	 * 获取数量
	 * 
	 * @return 数量
	 */
	@JsonProperty
	@NotNull
	@Min(0)
	@Max(10000)
	@Column(nullable = false, precision = 21, scale = 3)
	public BigDecimal getQuantity() {
		return quantity;
	}

	/**
	 * 设置数量
	 * 
	 * @param quantity
	 *            数量
	 */
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	/**
	 * 获取已发货数量
	 * 
	 * @return 已发货数量
	 */
	@Column(nullable = false, precision = 21, scale = 3)
	public BigDecimal getShippedQuantity() {
		return shippedQuantity;
	}

	/**
	 * 设置已发货数量
	 * 
	 * @param shippedQuantity
	 *            已发货数量
	 */
	public void setShippedQuantity(BigDecimal shippedQuantity) {
		this.shippedQuantity = shippedQuantity;
	}

	/**
	 * 获取已退货数量
	 * 
	 * @return 已退货数量
	 */
	@Column(nullable = false, precision = 21, scale = 3)
	public BigDecimal getReturnQuantity() {
		return returnQuantity;
	}

	/**
	 * 设置已退货数量
	 * 
	 * @param returnQuantity
	 *            已退货数量
	 */
	public void setReturnQuantity(BigDecimal returnQuantity) {
		this.returnQuantity = returnQuantity;
	}

	/**
	 * 获取商品
	 * 
	 * @return 商品
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Product getProduct() {
		return product;
	}

	/**
	 * 设置商品
	 * 
	 * @param product
	 *            商品
	 */
	public void setProduct(Product product) {
		this.product = product;
	}

	/**
	 * 获取订单
	 * 
	 * @return 订单
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "orders", nullable = false, updatable = false)
	public Order getOrder() {
		return order;
	}

	/**
	 * 设置订单
	 * 
	 * @param order
	 *            订单
	 */
	public void setOrder(Order order) {
		this.order = order;
	}

	/**
	 * 获取小计
	 * 
	 * @return 小计
	 */
	@JsonProperty
	@Transient
	public BigDecimal getSubtotal() {
		BigDecimal subtotal = null;
		if (getPrice() != null && getQuantity() != null) {
			subtotal = getPrice().multiply(getQuantity());
		}
		else {
			subtotal = new BigDecimal(0);
		}
		return subtotal.compareTo(new BigDecimal(0)) == -1 ? new BigDecimal(0)
				: subtotal.setScale(2, RoundingMode.HALF_UP);
	}

	/**
	 * 获取店铺商品关系
	 * 
	 * @return 店铺商品关系
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public ProductStore getProductStore() {
		return productStore;
	}

	/**
	 * 设置店铺商品关系
	 * 
	 * @param product
	 *            店铺商品关系
	 */
	public void setProductStore(ProductStore productStore) {
		this.productStore = productStore;
	}

	/**
	 * 商品下单时价格
	 * 
	 * @return the origPrice
	 */
	@JsonProperty
	@NotNull
	@Min(0)
	@Digits(integer = 12, fraction = 3)
	@Column(precision = 21, scale = 6)
	public BigDecimal getOrigPrice() {
		return origPrice;
	}

	/**
	 * 商品下单时价格
	 * 
	 * @param origPrice
	 *            the origPrice to set
	 */
	public void setOrigPrice(BigDecimal origPrice) {
		this.origPrice = origPrice;
	}

	/**
	 * 商品下单时会员价格
	 * 
	 * @return the origMemberPrice
	 */
	@JsonProperty
	@NotNull
	@Min(0)
	@Digits(integer = 12, fraction = 3)
	@Column(precision = 21, scale = 6)
	public BigDecimal getOrigMemberPrice() {
		return origMemberPrice;
	}

	/**
	 * 商品下单时会员价格
	 * 
	 * @param origMemberPrice
	 *            the origMemberPrice to set
	 */
	public void setOrigMemberPrice(BigDecimal origMemberPrice) {
		this.origMemberPrice = origMemberPrice;
	}

	/**
	 * 持久化前
	 */
	@PrePersist
	public void prePersist() {
		if (getShipPlanQuantity() == null) {
			setShipPlanQuantity(BigDecimal.ZERO);
		}
		if (getShippedQuantity() == null) {
			setShippedQuantity(BigDecimal.ZERO);
		}
		if (getReturnQuantity() == null) {
			setReturnQuantity(BigDecimal.ZERO);
		}
		if (getBomFlag() == null) {
			setBomFlag(0);
		}
		if (getPlanQuantity() == null) {
			setPlanQuantity(getQuantity());
		}
	}

	/**
	 * 更新前
	 */
	@PreUpdate
	public void preUpdate() {
		if (getShipPlanQuantity() == null) {
			setShipPlanQuantity(BigDecimal.ZERO);
		}
		if (getShippedQuantity() == null) {
			setShippedQuantity(BigDecimal.ZERO);
		}
		if (getReturnQuantity() == null) {
			setReturnQuantity(BigDecimal.ZERO);
		}
		if (getPlanQuantity() == null) {
			setPlanQuantity(getQuantity());
		}
		if (getBomFlag() == null) {
			setBomFlag(0);
		}
	}

	/**
	 * 获取 来源单号
	 * 
	 * @return outTradeNo
	 */
	public String getOutTradeNo() {
		return outTradeNo;
	}

	/**
	 * 设置 来源单号
	 * 
	 * @param outTradeNo
	 *            来源单号
	 */
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	/**
	 * 
	 * 方法描述: 设置来源接口明细ID
	 * 
	 * @return
	 */
	public String getOutTradeId() {
		return outTradeId;
	}

	/**
	 * 
	 * 方法描述: 获取来源接口明细ID
	 * 
	 * @param outTradeId
	 */
	public void setOutTradeId(String outTradeId) {
		this.outTradeId = outTradeId;
	}

	/**
	 * 获取deliveryTime
	 * 
	 * @return deliveryTime
	 */
	public Date getDeliveryTime() {
		return deliveryTime;
	}

	/**
	 * 设置deliveryTime
	 * 
	 * @param deliveryTime
	 *            deliveryTime
	 */
	public void setDeliveryTime(Date deliveryTime) {
		this.deliveryTime = deliveryTime;
	}

	/**
	 * 获取 商品型号
	 * 
	 * @return model
	 */
	public String getModel() {
		return model;
	}

	/**
	 * 设置 商品型号
	 * 
	 * @param model
	 *            商品型号
	 */
	public void setModel(String model) {
		this.model = model;
	}

	/**
	 * 获取 厂商编号
	 * 
	 * @return vonderCode
	 */
	public String getVonderCode() {
		return vonderCode;
	}

	/**
	 * 设置 厂商编号
	 * 
	 * @param vonderCode
	 *            厂商编号
	 */
	public void setVonderCode(String vonderCode) {
		this.vonderCode = vonderCode;
	}

	/**
	 * 获取 仓库
	 * 
	 * @date 2017年8月28日
	 * @return warehouse
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Warehouse getWarehouse() {
		return warehouse;
	}

	/**
	 * 设置 仓库
	 * 
	 * @date 2017年8月28日
	 * @param warehouse
	 *            仓库
	 */
	public void setWarehouse(Warehouse warehouse) {
		this.warehouse = warehouse;
	}

	/**
	 * 获取 库存
	 * 
	 * @date 2017年8月28日
	 * @return stock
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Stock getStock() {
		return stock;
	}

	/**
	 * 设置 库存
	 * 
	 * @date 2017年8月28日
	 * @param stock
	 *            库存
	 */
	public void setStock(Stock stock) {
		this.stock = stock;
	}

	/**
	 * @return demand
	 */
	public String getDemand() {
		return demand;
	}

	/**
	 * @param demand
	 */
	public void setDemand(String demand) {
		this.demand = demand;
	}

	/**
	 * 获取 订单明细关联表
	 * 
	 * @return orderItemPartses
	 */
	@JsonIgnore
	@OneToMany(mappedBy = "orderItem", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<OrderItemParts> getOrderItemPartses() {
		return orderItemPartses;
	}

	/**
	 * 设置 订单明细关联表
	 * 
	 * @param orderItemPartses
	 *            订单明细关联表
	 */
	public void setOrderItemPartses(List<OrderItemParts> orderItemPartses) {
		this.orderItemPartses = orderItemPartses;
	}

	/**
	 * 获取 订单明细附件
	 * 
	 * @return orderItemAttachs
	 */
	@JsonIgnore
	@OneToMany(mappedBy = "orderItem", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<OrderItemAttach> getOrderItemAttachs() {
		return orderItemAttachs;
	}

	/**
	 * 设置 订单明细附件
	 * 
	 * @param orderItemAttachs
	 *            订单明细附件
	 */
	public void setOrderItemAttachs(List<OrderItemAttach> orderItemAttachs) {
		this.orderItemAttachs = orderItemAttachs;
	}

	/**
	 * 获取 计划发货数量
	 * 
	 * @return shipPlanQuantity
	 */
	@Column(precision = 21, scale = 3)
	public BigDecimal getShipPlanQuantity() {
		return shipPlanQuantity;
	}

	/**
	 * 设置 计划发货数量
	 * 
	 * @param shipPlanQuantity
	 *            计划发货数量
	 */
	public void setShipPlanQuantity(BigDecimal shipPlanQuantity) {
		this.shipPlanQuantity = shipPlanQuantity;
	}

	/**
	 * 获取 上级订单项
	 * 
	 * @return parent
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public OrderItem getParent() {
		return parent;
	}

	/**
	 * 设置 上级订单项
	 * 
	 * @param parent
	 *            上级订单项
	 */
	public void setParent(OrderItem parent) {
		this.parent = parent;
	}

	/**
	 * 获取 0普通产品 1上级产品 2下级产品
	 * 
	 * @return bomFlag
	 */
	public Integer getBomFlag() {
		return bomFlag;
	}

	/**
	 * 设置 0普通产品 1上级产品 2下级产品
	 * 
	 * @param bomFlag
	 *            0普通产品 1上级产品 2下级产品
	 */
	public void setBomFlag(Integer bomFlag) {
		this.bomFlag = bomFlag;
	}

	/**
	 * 获取 计划下单数量
	 * 
	 * @return planQuantity
	 */
	@Column(precision = 21, scale = 3)
	public BigDecimal getPlanQuantity() {
		return planQuantity;
	}

	/**
	 * 设置 计划下单数量
	 * 
	 * @param planQuantity
	 *            计划下单数量
	 */
	public void setPlanQuantity(BigDecimal planQuantity) {
		this.planQuantity = planQuantity;
	}

	/**
	 * 获取 序号
	 * 
	 * @return seq
	 */
	public Integer getSeq() {
		return seq;
	}

	/**
	 * 设置 序号
	 * 
	 * @param seq
	 *            序号
	 */
	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	/**
	 * 获取 物料分类
	 * 
	 * @return productCategory
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public ProductCategory getProductCategory() {
		return productCategory;
	}

	/**
	 * 设置 物料分类
	 * 
	 * @param productCategory
	 *            物料分类
	 */
	public void setProductCategory(ProductCategory productCategory) {
		this.productCategory = productCategory;
	}

	/**
	 * 获取 备注
	 * 
	 * @return memo
	 */
	public String getMemo() {
		return memo;
	}

	/**
	 * 设置 备注
	 * 
	 * @param memo
	 *            备注
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/**
	 * 获取 特价单号
	 * 
	 * @return priceApplySn
	 */
	public String getPriceApplySn() {
		return priceApplySn;
	}

	/**
	 * 设置 特价单号
	 * 
	 * @param priceApplySn
	 *            特价单号
	 */
	public void setPriceApplySn(String priceApplySn) {
		this.priceApplySn = priceApplySn;
	}

	/**
	 * 获取 特价单明细
	 * 
	 * @return priceApplyItem
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public PriceApplyItem getPriceApplyItem() {
		return priceApplyItem;
	}

	/**
	 * 设置 特价单明细
	 * 
	 * @param priceApplyItem
	 *            特价单明细
	 */
	public void setPriceApplyItem(PriceApplyItem priceApplyItem) {
		this.priceApplyItem = priceApplyItem;
	}

	/**
	 * 获取 条形码编号
	 * 
	 * @return barCode
	 */
	public String getBarCode() {
		return barCode;
	}

	/**
	 * 设置 条形码编号
	 * 
	 * @param barCode
	 *            条形码编号
	 */
	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	/**
	 * 获取 预计交付时间
	 * 
	 * @return deliverDate
	 */
	public Date getDeliverDate() {
		return deliverDate;
	}

	/**
	 * 设置 预计交付时间
	 * 
	 * @param deliverDate
	 *            预计交付时间
	 */
	public void setDeliverDate(Date deliverDate) {
		this.deliverDate = deliverDate;
	}

	public String getBuyerMemo() {
		return buyerMemo;
	}

	public void setBuyerMemo(String buyerMemo) {
		this.buyerMemo = buyerMemo;
	}

	public String getSellerMemo() {
		return sellerMemo;
	}

	public void setSellerMemo(String sellerMemo) {
		this.sellerMemo = sellerMemo;
	}

	public Long getSourceOrderItemId() {
		return sourceOrderItemId;
	}

	public void setSourceOrderItemId(Long sourceOrderItemId) {
		this.sourceOrderItemId = sourceOrderItemId;
	}

	public Integer getIsPurchase() {
		return isPurchase;
	}

	public void setIsPurchase(Integer isPurchase) {
		this.isPurchase = isPurchase;
	}

	public String getSourceOrderSn() {
		return sourceOrderSn;
	}

	public void setSourceOrderSn(String sourceOrderSn) {
		this.sourceOrderSn = sourceOrderSn;
	}

	@Column(precision = 21, scale = 6)
	public BigDecimal getBoxQuantity() {
		return boxQuantity;
	}

	public void setBoxQuantity(BigDecimal boxQuantity) {
		this.boxQuantity = boxQuantity;
	}

	@Column(precision = 21, scale = 6)
	public BigDecimal getBranchQuantity() {
		return branchQuantity;
	}

	public void setBranchQuantity(BigDecimal branchQuantity) {
		this.branchQuantity = branchQuantity;
	}

	public Integer getProductGrade() {
		return productGrade;
	}

	public void setProductGrade(Integer productGrade) {
		this.productGrade = productGrade;
	}

	public String getWoodTypeOrColor() {
		return woodTypeOrColor;
	}

	public void setWoodTypeOrColor(String woodTypeOrColor) {
		this.woodTypeOrColor = woodTypeOrColor;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(precision = 21, scale = 6)
	public BigDecimal getScatteredQuantity() {
		return scatteredQuantity;
	}

	public void setScatteredQuantity(BigDecimal scatteredQuantity) {
		this.scatteredQuantity = scatteredQuantity;
	}

	@Column(precision = 21, scale = 6)
	public BigDecimal getPerBranch() {
		return perBranch;
	}

	public void setPerBranch(BigDecimal perBranch) {
		this.perBranch = perBranch;
	}

	@Column(precision = 21, scale = 6)
	public BigDecimal getBranchPerBox() {
		return branchPerBox;
	}

	public void setBranchPerBox(BigDecimal branchPerBox) {
		this.branchPerBox = branchPerBox;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public PolicyProduct getPolicyProduct() {
		return policyProduct;
	}

	public void setPolicyProduct(PolicyProduct policyProduct) {
		this.policyProduct = policyProduct;
	}

	public Integer getApplyType() {
		return applyType;
	}

	public void setApplyType(Integer applyType) {
		this.applyType = applyType;
	}

	public String getPolicySn() {
		return policySn;
	}

	public void setPolicySn(String policySn) {
		this.policySn = policySn;
	}

	public BigDecimal getApplyPrice() {
		return applyPrice;
	}

	public void setApplyPrice(BigDecimal applyPrice) {
		this.applyPrice = applyPrice;
	}

	public BigDecimal getPolicyPoint() {
		return policyPoint;
	}

	public void setPolicyPoint(BigDecimal policyPoint) {
		this.policyPoint = policyPoint;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getFactorySystemDict() {
		return factorySystemDict;
	}

	public void setFactorySystemDict(SystemDict factorySystemDict) {
		this.factorySystemDict = factorySystemDict;
	}

	public BigDecimal getZhekouAmount() {
		return zhekouAmount;
	}

	public void setZhekouAmount(BigDecimal zhekouAmount) {
		this.zhekouAmount = zhekouAmount;
	}

	public BigDecimal getZhekouPrice() {
		return zhekouPrice;
	}

	public void setZhekouPrice(BigDecimal zhekouPrice) {
		this.zhekouPrice = zhekouPrice;
	}

	/** 
	 * 获取  平台结算价 
	 * @return saleOrgPrice 平台结算价 
	 */
	@Column(precision = 21, scale = 6)
	public BigDecimal getSaleOrgPrice() {
		return saleOrgPrice;
	}

	/** 
	 * 设置  平台结算价 
	 * @param saleOrgPrice 平台结算价 
	 */
	public void setSaleOrgPrice(BigDecimal saleOrgPrice) {
		this.saleOrgPrice = saleOrgPrice;
	}

	public Integer getPartsSeq() {
		return partsSeq;
	}

	public void setPartsSeq(Integer partsSeq) {
		this.partsSeq = partsSeq;
	}

	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}

	public BigDecimal getProPriceHeadPrice() {
		return proPriceHeadPrice;
	}

	public void setProPriceHeadPrice(BigDecimal proPriceHeadPrice) {
		this.proPriceHeadPrice = proPriceHeadPrice;
	}

	public BigDecimal getLength() {
		return length;
	}

	public void setLength(BigDecimal length) {
		this.length = length;
	}

	public BigDecimal getWidth() {
		return width;
	}

	public void setWidth(BigDecimal width) {
		this.width = width;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getDiscountCode() {
		return discountCode;
	}

	public void setDiscountCode(SystemDict discountCode) {
		this.discountCode = discountCode;
	}

	public String getColourNumber() {
		return colourNumber;
	}

	public String getMoistureContent() {
		return moistureContent;
	}

	public String getBatch() {
		return batch;
	}

	public void setColourNumber(String colourNumber) {
		this.colourNumber = colourNumber;
	}

	public void setMoistureContent(String moistureContent) {
		this.moistureContent = moistureContent;
	}

	public void setBatch(String batch) {
		this.batch = batch;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getProductLevel() {
		return productLevel;
	}

	public void setProductLevel(SystemDict productLevel) {
		this.productLevel = productLevel;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public CustomerContractItem getCustomerContractItem() {
		return customerContractItem;
	}

	public void setCustomerContractItem(CustomerContractItem customerContractItem) {
		this.customerContractItem = customerContractItem;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getProductOrganization() {
		return productOrganization;
	}

	public void setProductOrganization(Organization productOrganization) {
		this.productOrganization = productOrganization;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getColourNumbers() {
		return colourNumbers;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getMoistureContents() {
		return moistureContents;
	}

	public void setColourNumbers(SystemDict colourNumbers) {
		this.colourNumbers = colourNumbers;
	}

	public void setMoistureContents(SystemDict moistureContents) {
		this.moistureContents = moistureContents;
	}
	
	@JsonIgnore
	@OneToMany(mappedBy = "orderItem", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<WarehouseBillBatch> getWarehouseBillBatchs() {
		return warehouseBillBatchs;
	}

	public void setWarehouseBillBatchs(List<WarehouseBillBatch> warehouseBillBatchs) {
		this.warehouseBillBatchs = warehouseBillBatchs;
	}

	public String getBatchEncoding() {
		return batchEncoding;
	}

	public void setBatchEncoding(String batchEncoding) {
		this.batchEncoding = batchEncoding;
	}

	public Integer getFlag() {
		return flag;
	}

	public void setFlag(Integer flag) {
		this.flag = flag;
	}

    public BigDecimal getProductOrgPrice() {
        return productOrgPrice;
    }

    public void setProductOrgPrice(BigDecimal productOrgPrice) {
        this.productOrgPrice = productOrgPrice;
    }

	public Long getPriceId() {
		return priceId;
	}

	public void setPriceId(Long priceId) {
		this.priceId = priceId;
	}
	/**
     * 平台结算价原价
     * 
     * @return 平台结算价原价
     */
    @JsonProperty
    @Min(0)
    @Column(precision = 21, scale = 2)
    public BigDecimal getReSaleOrgPrice() {
        return reSaleOrgPrice;
    }

    /**
     * 设置平台结算价原价
     *
     */
    public void setReSaleOrgPrice(BigDecimal reSaleOrgPrice) {
        this.reSaleOrgPrice = reSaleOrgPrice;
    }

	/**
	 * 获取价差
	 */
	public BigDecimal getPriceDifference() {
		return priceDifference;
	}

	/**
	 * 设置价差
	 */
	public void setPriceDifference(BigDecimal priceDifference) {
		this.priceDifference = priceDifference;
	}

	public BigDecimal getZxgk() {
		return zxgk;
	}

	public void setZxgk(BigDecimal zxgk) {
		this.zxgk = zxgk;
	}


	public BigDecimal getGkjsjc() {
		return gkjsjc;
	}

	public void setGkjsjc(BigDecimal gkjsjc) {
		this.gkjsjc = gkjsjc;
	}

	public BigDecimal getZxptjc() {
		return zxptjc;
	}

	public void setZxptjc(BigDecimal zxptjc) {
		this.zxptjc = zxptjc;
	}

	public BigDecimal getQtzx() {
		return qtzx;
	}

	public void setQtzx(BigDecimal qtzx) {
		this.qtzx = qtzx;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getDiscountProjectDescription() {
		return discountProjectDescription;
	}

	public void setDiscountProjectDescription(SystemDict discountProjectDescription) {
		this.discountProjectDescription = discountProjectDescription;
	}

	public BigDecimal getReProductOrgPrice() {
		return reProductOrgPrice;
	}

	public void setReProductOrgPrice(BigDecimal reProductOrgPrice) {
		this.reProductOrgPrice = reProductOrgPrice;
	}

	public BigDecimal getOrgPriceDifference() {
		return orgPriceDifference;
	}

	public void setOrgPriceDifference(BigDecimal orgPriceDifference) {
		this.orgPriceDifference = orgPriceDifference;
	}
}