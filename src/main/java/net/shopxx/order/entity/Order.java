package net.shopxx.order.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.entity.ReturnGoodsApply;
import net.shopxx.basic.entity.*;
import net.shopxx.finance.entity.Deposit;
import net.shopxx.finance.entity.Payment;
import net.shopxx.finance.entity.PolicyCountContract;
import net.shopxx.link5.entity.ConvertibleToAccCustomer;
import net.shopxx.member.entity.*;
import net.shopxx.stock.entity.Warehouse;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.persistence.*;
import javax.validation.Valid;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

/**
 * Entity - 订单
 */
@Entity
@Table(name = "xx_order")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_order_sequence")
public class Order extends ActWfBillEntity implements ConvertibleToAccCustomer{

	private static final long serialVersionUID = 8370942500343156156L;

	/** 订单名称分隔符 */
	private static final String NAME_SEPARATOR = " ";

	/**
	 * 订单状态
	 */
	public enum OrderStatus {

		/** 未确认 */
		unconfirmed,

		/** 已确认 */
		confirmed,

		/** 已完成 */
		completed,

		/** 已作废 */
		cancelled,

		/** 已删除 */
		deleted,

		/** 未审核 */
		unaudited,

		/** 已审核 */
		audited,

		/** 已保存(保存状态的订单代表着这个订单还未生效，用户可以随意修改，只有用户提交-订单状态未审核，这个订单才有效) */
		saved,

		/** 已接受 */
		accepted,

		/** 已拒绝 */
		refused
	}

	/**
	 * 支付状态
	 */
	public enum PaymentStatus {

		/** 未支付 */
		unpaid,

		/** 部分支付 */
		partialPayment,

		/** 已支付 */
		paid
	}

	/**
	 * 配送状态
	 */
	public enum ShippingStatus {

		/** 未发货 */
		unshipped,

		/** 部分发货 */
		partialShipment,

		/** 已发货 */
		shipped,

		/** 确认收货 */
		confirmReceipt
	}

	/** 订单编号 */
	private String sn;

	/** 来源单号 */
	private String outTradeNo;

	/** 订单状态 */
	private OrderStatus orderStatus;

	/** 支付状态 */
	private PaymentStatus paymentStatus;

	/** 配送状态 */
	private ShippingStatus shippingStatus;

	/** 支付方式名称 */
	private String paymentMethodName;

	/** 支付方式 */
	private PaymentMethod paymentMethod;

	/** 配送方式名称 */
	private String shippingMethodName;

	/** 配送方式 */
	private ShippingMethod shippingMethod;

	/** 运费 */
	private BigDecimal freight;

	/** 促销折扣 */
	private BigDecimal promotionDiscount;

	/** 优惠券折扣 */
	private BigDecimal couponDiscount;

	/** 调整金额 */
	private BigDecimal offsetAmount;

	/** 已付金额 */
	private BigDecimal amountPaid;

	/** 订单来源 0.APP商城 2.微商城 3.PC商城 4.需求单转订单 5.电商单 6.PC后台下单 7.excel导入 */
	private String type;

	/** 到期时间 */
	private Date expire;

	// /** 会员 */
	// private Member member;

	/** 操作人 */
	private StoreMember storeMember;

	/** 区域经理*/
	private StoreMember regionalManager;

	/** 审核人 */
	private StoreMember checkStoreMember;

	/**身份证号码*/
	private String cardId;

	/** 作废人 */
	private StoreMember cencalStoreMember;

	// /** 优惠码 */
	// private CouponCode couponCode;

	/** 店铺 **/
	private Store store;

	/** 物流快递 */
	private DeliveryCorp delivery;

	// /** 运单号 */
	// private String trackingNo;

	// /** 仓库编号 */
	// private String warehouseCode;

	/** 仓库 */
	private Warehouse warehouse;

	/** 订单类型: 0 B2C, 2 B2B, 3 物料推广 4 采购订单 */
	private Integer orderType;

	/** 异常备注 */
	private String changeMemo;

	/** 买家备注 */
	private String buyerRemark;

	/** 商家备注 */
	private String sellerRemark;

	/** 物流备注 */
	private String deliveryMemo;

	/** 附言 */
	private String memo;

	/** 下单时间 */
	private Date orderDate;

	/** 审核时间 */
	private Date checkDate;

	/** 异常状态：1 已拦截, 2 物流拒绝, 3 其他 */
	private Integer exStatus;

	// 收货人信息====================
	/** 收货人 */
	private String consignee;

	/** 收货人 手机号 */
	private String mobile;

	/** 收货人 固定电话 */
	private String phone;

	/** 邮编 */
	private String zipCode;

	/** 收货地址： 省份 */
	private String province;

	/** 收货地址： 省城市 */
	private String city;

	/** 收货地址： 区、县 */
	private String district;

	/** 详细地址 */
	private String address;

	/** 地区 */
	private Area area;
	// 收货人信息====================

	// 发票信息=====================
	/** 是否开据发票 */
	private Boolean isInvoice;

	/** 发票抬头 */
	private String invoiceTitle;

	/** 发票类型: 0 普通发票, 1 增值税专用发票 */
	private Integer invoiceType;

	/** 单位名称 */
	private String dangweimingchen;

	/** 纳税人识别号 */
	private String nashuishibiehao;

	/** 注册地址 */
	private String zhucedizhi;

	/** 注册电话 */
	private String zhucedianhua;

	/** 开户银行 */
	private String kaihuyh;

	/** 增值税银行帐户 */
	private String zengzhishuiyhzh;

	/** 税金 */
	private BigDecimal tax;

	/** 发票备注 */
	private String invoiceRemark;
	// 发票信息=====================

	/** 旗标 */
	public Integer flag;

	/** 是否是微商城订单 */
	private Boolean isMobile;

	/** 机构 */
	private SaleOrg saleOrg;

	/** 费用核算对象 0客户 1机构 */
	private Integer accountObj = 0;

	/** 累计提货总额 */
	private BigDecimal goodsAmount;

	// /** 优惠券 */
	// private List<Coupon> coupons = new ArrayList<Coupon>();

	/** 毛利分析 */
	private List<GrossProfitAnalysis> grossProfitAnalysiss = new ArrayList<GrossProfitAnalysis>();

	/** 订单项 */
	private List<OrderItem> orderItems = new ArrayList<OrderItem>();

	/** 资金流水，记录所有跟资金有关信息 */
	private Set<Deposit> deposits = new HashSet<Deposit>();

	/** 支付单 */
	private Set<Payment> payments = new HashSet<Payment>();

	/** 发货单 */
	private Set<Shipping> shippings = new HashSet<Shipping>();

	/** 退款退货退换货 */
	private List<ReturnGoodsApply> returnGoodsApply = new ArrayList<ReturnGoodsApply>();

	/** 订单附件项 */
	private List<OrderAttach> orderAttachs = new ArrayList<OrderAttach>();

//	/** 备用发货地址*/
//	private List<StandbyAddress> standbyAddress = new ArrayList<StandbyAddress>();

	/** 确认状态 */
	private Integer confirmStatus;

	/** 运费承担类型 取词汇编码为FreightChargeType的词汇 */
	private SystemDict freightChargeType;

	/** 供应商 */
	private Store supplier;

	/** 客户经理 */
	private String manager;

	/** 组织 */
	private Organization organization;

	/** ----------------------------18-07-26 新加字段------------------ */

	/** 业务类型 取词汇编码为businessType的词汇 */
	private SystemDict businessType;

	/** 税率% */
	private BigDecimal taxRate;

	/** 订单类型（新）0.常规订单 1.特价订单 2.合同订单 3.项目订单 4.定制订单(TCL专用) 5.项目订单(大自然橱衣柜) */
	private Integer orderCategory;

	/** 本次使用政策 */
	private BigDecimal usePolicyPrice;

	/** 业务员 */
	private StoreMember salesman;

	/** 客户余额 */
	private BigDecimal storeBalance;

	/** ----------------------------18-08-01 新加字段 xl------------------ */
	/** 贸易类型 */
	private Integer tradeType;
	/** 销售体系 */
	private Integer saleType;

	/** 合同号 */
	private String contractSn;

	/** 合同名称 */
	private String contractName;

	/** 合同类型 */
	private String contractType;

	/** 合同客户 */
	private StoreMember contractMember;

	/** 付款方式-项目订单用 */
	private BigDecimal paymentType;

	/** tcl暖通 开始 */

	// 本单使用政策金额
	private BigDecimal policyAmount;

	// 订单底价
	private BigDecimal lowAmount;

	// 订单毛利率
	private BigDecimal earnPercent;

	// 体积
	private BigDecimal volume;

	// 订单成本价
	private BigDecimal costAmount;

	/** Tcl暖通 结束 */

	/** 是否有费用 */
	private Boolean isCost;

	/** 是否异地 */
	private Boolean isOffsite;

	/** ----------------------------18-09-06 新加字段 xl------------------ */
	/** 订单分类  取词汇编码为orderClassify的词汇*/
	private SystemDict orderClassify;

	/** 业务员*/
	//private StoreMember saleman;

	/** 订单类型  取词汇编码为orderStyle的词汇*/
	private SystemDict orderStyle;

	/** 贸易类型 取词汇编码为tradeStyle的词汇*/
	private SystemDict tradeStyle;

	/** 销售体系 取词汇编码为saleStyle的词汇*/
	private SystemDict saleStyle;

	/** 行业类别 取词汇编码为industryStyle的词汇*/
	private SystemDict industryStyle;

	/** 币种 取词汇编码为Currency的词汇*/
	private SystemDict currency;

	/** 费用(非外键关联) */
	private List<Cost> costs = new ArrayList<Cost>();

	/** 备用信息*/
	private List<StandbyAddress> standbyAddresses = new ArrayList<StandbyAddress>();

	/** 异地分公司(非外键关联) */
	private List<OffsiteBranch> offsiteBranchs = new ArrayList<OffsiteBranch>();

	/** 授信(非外键关联) */
	private List<CreditRechargeContract> creditRechargeContracts = new ArrayList<CreditRechargeContract>();

	/** 回款(非外键关联) */
	private List<DepositRechargeContract> depositRechargeContracts = new ArrayList<DepositRechargeContract>();

	/** 政策(非外键关联) */
	private List<PolicyCountContract> policyCountContracts = new ArrayList<PolicyCountContract>();

	/** 地址外部编号 */
	private String addressOutTradeNo;

	// 销售出厂价总额
	private BigDecimal chuchangCount;
	// 销售毛利总额
	private BigDecimal maoliCount;
	// 销售毛利点位
	private BigDecimal maoliPoint;

	/**明细序号*/
	private Integer lineNum;

	//合同
	private Contract contract;

	/** 经销商 */
	private Store distributor;

	/** 开票方*/
	private String ticketHolder;

	/** 付款方式-按客户 系统词汇 PaymentStyle*/
	private SystemDict paymentStyle;

	/** 议价编码*/
	private String bargainingCode;

	/** 促销编码*/
	private String salesCode;

	/** 生产工厂 取词汇编码为FactoryName的词汇*/
	private SystemDict factory;

	/** 项目阶段 取词汇编码为projectstage的词汇*/
	private SystemDict projectstage;

	/** 确认收入类型 取词汇编码为incometype的词汇*/
	private SystemDict incometype;

	/** 是否委外*/
	private Boolean isOutsourcing;

	/** 定金比例*/
	private BigDecimal fkRate;

	/** 销售区域*/
	private String salesAreaName;

	/** SBU*/
	/**备注*/
	private Sbu sbu;
	
	
	/** 订单创建IP*/
	private String orderIp;
	
	
	/** 订单创建地址*/
	private String orderDevice;
	
	/** 订单创建设备*/
	private String orderEquipment;
	
	// 价格类型
	private MemberRank memberRank;
	
	/** 折扣方案*/
	private SystemDict discountCode;
	
	/** 充值单*/
	private DepositRecharge depositRecharge;
	
	//运输方式
	private SystemDict transportType;
	
	/** 经销商合同*/
	private CustomerContract customerContract;

	/**出货类型
     *
     * AzureL
     * 2021-3-26*/
    private SystemDict outOfTheWarehouseType;

    /**
     * 售后单
     */
    private Aftersale aftersale;

	/**
	 * link4售后单
	 */
	private String fourAftersaleSn;

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getTransportType() {
		return transportType;
	}

	public void setTransportType(SystemDict transportType) {
		this.transportType = transportType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getPaymentStyle() {
		return paymentStyle;
	}

    public void setPaymentStyle(SystemDict paymentStyle) {
		this.paymentStyle = paymentStyle;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getIndustryStyle() {
		return industryStyle;
	}

	public void setIndustryStyle(SystemDict industryStyle) {
		this.industryStyle = industryStyle;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getOrderStyle() {
		return orderStyle;
	}

	public void setOrderStyle(SystemDict orderStyle) {
		this.orderStyle = orderStyle;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getTradeStyle() {
		return tradeStyle;
	}

	public void setTradeStyle(SystemDict tradeStyle) {
		this.tradeStyle = tradeStyle;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getSaleStyle() {
		return saleStyle;
	}

	public void setSaleStyle(SystemDict saleStyle) {
		this.saleStyle = saleStyle;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getOrderClassify() {
		return orderClassify;
	}

	public void setOrderClassify(SystemDict orderClassify) {
		this.orderClassify = orderClassify;
	}

	public BigDecimal getPaymentType() {
		return paymentType;
	}

	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

	public BigDecimal getCostAmount() {
		return costAmount;
	}

	public void setCostAmount(BigDecimal costAmount) {
		this.costAmount = costAmount;
	}

	public void setPaymentType(BigDecimal paymentType) {
		this.paymentType = paymentType;
	}

	//2018-8-22 冯旗 增加下达时间

	/** 下达时间*/
	private Date goTime;

	/** 保质期 */
	private Date qualityDate;

	/** 工程名称 */
	private String projectName;

	/** 付款条件 系统词汇PaymentConditions*/
	private SystemDict paymentConditions;

	/** sap单号 */
	private String sapSn;

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getPaymentConditions() {
		return paymentConditions;
	}

	public void setPaymentConditions(SystemDict paymentConditions) {
		this.paymentConditions = paymentConditions;
	}

	public Date getQualityDate() {
		return qualityDate;
	}

	public void setQualityDate(Date qualityDate) {
		this.qualityDate = qualityDate;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	/**
	 * 获取订单编号
	 * 
	 * @return 订单编号
	 */
	@Column(nullable = false, updatable = false, unique = true, length = 100)
	public String getSn() {
		return sn;
	}

	/**
	 * 设置订单编号
	 * 
	 * @param sn
	 *            订单编号
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	/**
	 * 获取订单状态
	 * 
	 * @return 订单状态
	 */
	@Column(nullable = false)
	public OrderStatus getOrderStatus() {
		return orderStatus;
	}

	/**
	 * 设置订单状态
	 * 
	 * @param orderStatus
	 *            订单状态
	 */
	public void setOrderStatus(OrderStatus orderStatus) {
		this.orderStatus = orderStatus;
	}

	/**
	 * 获取支付状态
	 * 
	 * @return 支付状态
	 */
	@Column(nullable = false)
	public PaymentStatus getPaymentStatus() {
		return paymentStatus;
	}

	/**
	 * 设置支付状态
	 * 
	 * @param paymentStatus
	 *            支付状态
	 */
	public void setPaymentStatus(PaymentStatus paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	/**
	 * 获取配送状态
	 * 
	 * @return 配送状态
	 */
	@Column(nullable = false)
	public ShippingStatus getShippingStatus() {
		return shippingStatus;
	}

	/**
	 * 设置配送状态
	 * 
	 * @param shippingStatus
	 *            配送状态
	 */
	public void setShippingStatus(ShippingStatus shippingStatus) {
		this.shippingStatus = shippingStatus;
	}

	/**
	 * 获取运费
	 * 
	 * @return 运费
	 */
	@NotNull
	@Min(0)
	@Digits(integer = 12, fraction = 3)
	@Column(nullable = false, precision = 21, scale = 6)
	public BigDecimal getFreight() {
		return freight;
	}

	/**
	 * 设置运费
	 * 
	 * @param freight
	 *            运费
	 */
	public void setFreight(BigDecimal freight) {
		this.freight = freight;
	}

	/**
	 * 获取促销折扣
	 * 
	 * @return 促销折扣
	 */
	@Column(nullable = false, updatable = false, precision = 21, scale = 6)
	public BigDecimal getPromotionDiscount() {
		return promotionDiscount;
	}

	/**
	 * 设置促销折扣
	 * 
	 * @param promotionDiscount
	 *            促销折扣
	 */
	public void setPromotionDiscount(BigDecimal promotionDiscount) {
		this.promotionDiscount = promotionDiscount;
	}

	/**
	 * 获取优惠券折扣
	 * 
	 * @return 优惠券折扣
	 */
	@Column(nullable = false, updatable = false, precision = 21, scale = 6)
	public BigDecimal getCouponDiscount() {
		return couponDiscount;
	}

	/**
	 * 设置优惠券折扣
	 * 
	 * @param couponDiscount
	 *            优惠券折扣
	 */
	public void setCouponDiscount(BigDecimal couponDiscount) {
		this.couponDiscount = couponDiscount;
	}

	/**
	 * 获取调整金额
	 * 
	 * @return 调整金额
	 */
	@NotNull
	@Digits(integer = 12, fraction = 3)
	@Column(nullable = false, precision = 21, scale = 6)
	public BigDecimal getOffsetAmount() {
		return offsetAmount;
	}

	/**
	 * 设置调整金额
	 * 
	 * @param offsetAmount
	 *            调整金额
	 */
	public void setOffsetAmount(BigDecimal offsetAmount) {
		this.offsetAmount = offsetAmount;
	}

	/**获取sbu*/
	/**备注*/
	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}

	/**设置sbu*/
	/**备注*/
	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}

	/**
	 * 获取已付金额
	 * 
	 * @return 已付金额
	 */
	@Column(nullable = false, precision = 21, scale = 6)
	public BigDecimal getAmountPaid() {
		return amountPaid;
	}

	/**
	 * 设置已付金额
	 * 
	 * @param amountPaid
	 *            已付金额
	 */
	public void setAmountPaid(BigDecimal amountPaid) {
		this.amountPaid = amountPaid;
	}

	/**
	 * 获取收货人
	 * 
	 * @return 收货人
	 */
	@NotEmpty
	@Length(max = 200)
	@Column(nullable = false)
	public String getConsignee() {
		return consignee;
	}

	/**
	 * 设置收货人
	 * 
	 * @param consignee
	 *            收货人
	 */
	public void setConsignee(String consignee) {
		this.consignee = consignee;
	}

	/**
	 * 获取地址
	 * 
	 * @return 地址
	 */
	@NotEmpty
	@Length(max = 200)
	@Column(nullable = false)
	public String getAddress() {
		return address;
	}

	/**
	 * 设置地址
	 * 
	 * @param address
	 *            地址
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	/**
	 * 获取邮编
	 * 
	 * @return 邮编
	 */
	@NotEmpty
	@Length(max = 200)
	public String getZipCode() {
		return zipCode;
	}

	/**
	 * 设置邮编
	 * 
	 * @param zipCode
	 *            邮编
	 */
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	/**
	 * 获取电话
	 * 
	 * @return 电话
	 */
	@NotEmpty
	@Length(max = 200)
	@Column(nullable = false)
	public String getPhone() {
		return phone;
	}

	/**
	 * 设置电话
	 * 
	 * @param phone
	 *            电话
	 */
	public void setPhone(String phone) {
		this.phone = phone;
	}

	/**
	 * 获取附言
	 * 
	 * @return 附言
	 */
	@Length(max = 200)
	public String getMemo() {
		return memo;
	}

	/**
	 * 设置附言
	 * 
	 * @param memo
	 *            附言
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/**
	 * 获取到期时间
	 * 
	 * @return 到期时间
	 */
	public Date getExpire() {
		return expire;
	}

	/**
	 * 设置到期时间
	 * 
	 * @param expire
	 *            到期时间
	 */
	public void setExpire(Date expire) {
		this.expire = expire;
	}

	/**
	 * 获取支付方式名称
	 * 
	 * @return 支付方式名称
	 */
	public String getPaymentMethodName() {
		return paymentMethodName;
	}

	/**
	 * 设置支付方式名称
	 * 
	 * @param paymentMethodName
	 *            支付方式名称
	 */
	public void setPaymentMethodName(String paymentMethodName) {
		this.paymentMethodName = paymentMethodName;
	}

	/**
	 * 获取配送方式名称
	 * 
	 * @return 配送方式名称
	 */
	@Column(nullable = false)
	public String getShippingMethodName() {
		return shippingMethodName;
	}

	/**
	 * 设置配送方式名称
	 * 
	 * @param shippingMethodName
	 *            配送方式名称
	 */
	public void setShippingMethodName(String shippingMethodName) {
		this.shippingMethodName = shippingMethodName;
	}

	/**
	 * 获取地区
	 * 
	 * @return 地区
	 */
	@NotNull
	@ManyToOne(fetch = FetchType.LAZY)
	public Area getArea() {
		return area;
	}

	/**
	 * 设置地区
	 * 
	 * @param area
	 *            地区
	 */
	public void setArea(Area area) {
		this.area = area;
	}

	/**
	 * 获取支付方式
	 * 
	 * @return 支付方式
	 */
	@NotNull
	@ManyToOne(fetch = FetchType.LAZY)
	public PaymentMethod getPaymentMethod() {
		return paymentMethod;
	}

	/**
	 * 设置支付方式
	 * 
	 * @param paymentMethod
	 *            支付方式
	 */
	public void setPaymentMethod(PaymentMethod paymentMethod) {
		this.paymentMethod = paymentMethod;
	}

	/**
	 * 获取配送方式
	 * 
	 * @return 配送方式
	 */
	@NotNull
	@ManyToOne(fetch = FetchType.LAZY)
	public ShippingMethod getShippingMethod() {
		return shippingMethod;
	}

	/**
	 * 设置配送方式
	 * 
	 * @param shippingMethod
	 *            配送方式
	 */
	public void setShippingMethod(ShippingMethod shippingMethod) {
		this.shippingMethod = shippingMethod;
	}

	// /**
	// * 获取会员
	// *
	// * @return 会员
	// */
	// @ManyToOne(fetch = FetchType.LAZY)
	// @JoinColumn(updatable = false)
	// public Member getMember() {
	// return member;
	// }
	//
	// /**
	// * 设置会员
	// *
	// * @param member
	// * 会员
	// */
	// public void setMember(Member member) {
	// this.member = member;
	// }

	// /**
	// * 获取优惠码
	// *
	// * @return 优惠码
	// */
	// @OneToOne(fetch = FetchType.LAZY)
	// public CouponCode getCouponCode() {
	// return couponCode;
	// }
	//
	// /**
	// * 设置优惠码
	// *
	// * @param couponCode
	// * 优惠码
	// */
	// public void setCouponCode(CouponCode couponCode) {
	// this.couponCode = couponCode;
	// }
	//
	// /**
	// * 获取优惠券
	// *
	// * @return 优惠券
	// */
	// @ManyToMany(fetch = FetchType.LAZY)
	// @JoinTable(name = "xx_order_coupon")
	// public List<Coupon> getCoupons() {
	// return coupons;
	// }
	//
	// /**
	// * 设置优惠券
	// *
	// * @param coupons
	// * 优惠券
	// */
	// public void setCoupons(List<Coupon> coupons) {
	// this.coupons = coupons;
	// }

	/**
	 * 获取订单项
	 * 
	 * @return 订单项
	 */
	@Valid
	@NotEmpty
	@OneToMany(mappedBy = "order", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	// @OrderBy("isGift asc")
	public List<OrderItem> getOrderItems() {
		return orderItems;
	}

	/**
	 * 设置订单项
	 * 
	 * @param orderItems
	 *            订单项
	 */
	public void setOrderItems(List<OrderItem> orderItems) {
		this.orderItems = orderItems;
	}

	/**
	 * 获取预存款
	 * 
	 * @return 预存款
	 */
	@OneToMany(mappedBy = "order", fetch = FetchType.LAZY)
	public Set<Deposit> getDeposits() {
		return deposits;
	}

	/**
	 * 设置预存款
	 * 
	 * @param deposits
	 *            预存款
	 */
	public void setDeposits(Set<Deposit> deposits) {
		this.deposits = deposits;
	}

	/**
	 * 获取收款单
	 * 
	 * @return 收款单
	 */
	@OneToMany(mappedBy = "order", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
	@OrderBy("createDate asc")
	public Set<Payment> getPayments() {
		return payments;
	}

	/**
	 * 设置收款单
	 * 
	 * @param payments
	 *            收款单
	 */
	public void setPayments(Set<Payment> payments) {
		this.payments = payments;
	}

	/**
	 * 获取发货单
	 * 
	 * @return 发货单
	 */
	@OneToMany(mappedBy = "order", fetch = FetchType.EAGER, cascade = CascadeType.REMOVE)
	@OrderBy("createDate asc")
	public Set<Shipping> getShippings() {
		return shippings;
	}

	/**
	 * 设置发货单
	 * 
	 * @param shippings
	 *            发货单
	 */
	public void setShippings(Set<Shipping> shippings) {
		this.shippings = shippings;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	/**
	 * 获取店铺
	 * 
	 * @return
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "stores")
	public Store getStore() {
		return store;
	}

	/**
	 * 设置店铺
	 * 
	 * @param store
	 */
	public void setStore(Store store) {
		this.store = store;
	}

	/**
	 * 获取收货地址所属省份
	 * 
	 * @return
	 */
	public String getProvince() {
		return province;
	}

	/**
	 * 设置收货地址所属省份
	 * 
	 * @param province
	 */
	public void setProvince(String province) {
		this.province = province;
	}

	/**
	 * 获取收货地址所属城市
	 * 
	 * @return
	 */
	public String getCity() {
		return city;
	}

	/**
	 * 设置收货地址所属城市
	 * 
	 * @param city
	 */
	public void setCity(String city) {
		this.city = city;
	}

	/**
	 * 获取收货地址所属区域、县
	 * 
	 * @return
	 */
	public String getDistrict() {
		return district;
	}

	/**
	 * 设置收货地址所属区域、县
	 * 
	 * @param district
	 */
	public void setDistrict(String district) {
		this.district = district;
	}

	@OneToMany(mappedBy = "orders", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<ReturnGoodsApply> getReturnGoodsApply() {
		return returnGoodsApply;
	}

	public void setReturnGoodsApply(List<ReturnGoodsApply> returnGoodsApply) {
		this.returnGoodsApply = returnGoodsApply;
	}

	/**
	 * 设置订单附件项
	 * 
	 * @return the orderAttachs
	 */
	@OneToMany(mappedBy = "order", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<OrderAttach> getOrderAttachs() {
		return orderAttachs;
	}

	/**
	 * 获取订单附件项
	 * 
	 * @param orderAttachs
	 *            the orderAttachs to set
	 */
	public void setOrderAttachs(List<OrderAttach> orderAttachs) {
		this.orderAttachs = orderAttachs;
	}

	/**
	 * 获取订单名称
	 * 
	 * @return 订单名称
	 */
	@Transient
	public String getName() {
		StringBuffer name = new StringBuffer();
		if (getOrderItems() != null) {
			for (OrderItem orderItem : getOrderItems()) {
				if (orderItem != null && orderItem.getFullName() != null) {
					name.append(NAME_SEPARATOR).append(orderItem.getFullName());
				}
			}
			if (name.length() > 0) {
				name.deleteCharAt(0);
			}
		}
		return name.toString();
	}

	// /**
	// * 获取商品重量
	// *
	// * @return 商品重量
	// */
	// @Transient
	// public BigDecimal getWeight() {
	// BigDecimal weight = new BigDecimal("0");
	// if (getOrderItems() != null) {
	// for (OrderItem orderItem : getOrderItems()) {
	// if (orderItem != null) {
	// weight = weight.add(orderItem.getTotalWeight());
	// }
	// }
	// }
	// return weight;
	// }

	/**
	 * 获取商品数量
	 * 
	 * @return 商品数量
	 */
	@Transient
	public BigDecimal getQuantity() {
		BigDecimal quantity = BigDecimal.ZERO;
		if (getOrderItems() != null) {
			for (OrderItem orderItem : getOrderItems()) {
				if (orderItem != null && orderItem.getQuantity() != null) {
					quantity = quantity.add(orderItem.getQuantity());
					// quantity += orderItem.getQuantity();
				}
			}
		}
		return quantity;
	}

	/**
	 * 获取已发货数量
	 * 
	 * @return 已发货数量
	 */
	@Transient
	public BigDecimal getShippedQuantity() {
		BigDecimal shippedQuantity = BigDecimal.ZERO;
		if (getOrderItems() != null) {
			for (OrderItem orderItem : getOrderItems()) {
				if (orderItem != null && orderItem.getShippedQuantity() != null) {
					shippedQuantity = shippedQuantity.add(orderItem.getShippedQuantity());
					// shippedQuantity += orderItem.getShippedQuantity();
				}
			}
		}
		return shippedQuantity;
	}

	/**
	 * 获取已退货数量
	 * 
	 * @return 已退货数量
	 */
	@Transient
	public BigDecimal getReturnQuantity() {
		BigDecimal returnQuantity = BigDecimal.ZERO;
		if (getOrderItems() != null) {
			for (OrderItem orderItem : getOrderItems()) {
				if (orderItem != null && orderItem.getReturnQuantity() != null) {
					returnQuantity = returnQuantity.add(orderItem.getReturnQuantity());
					// returnQuantity += orderItem.getReturnQuantity();
				}
			}
		}
		return returnQuantity;
	}

	/**
	 * 获取商品价格
	 * 
	 * @return 商品价格
	 */
	@Transient
	public BigDecimal getPrice() {
		BigDecimal price = new BigDecimal(0);
		if (getOrderItems() != null) {
			for (OrderItem orderItem : getOrderItems()) {
				if (orderItem != null && orderItem.getSubtotal() != null) {
					price = price.add(orderItem.getSubtotal());
				}
			}
		}
		return price;
	}

	/**
	 * 获取订单金额
	 * 
	 * @return 订单金额
	 */
	@Transient
	public BigDecimal getAmount() {
		BigDecimal amount = new BigDecimal(0);
		amount = amount.add(getPrice());
		if (getFreight() != null) {
			amount = amount.add(getFreight());
		}
		if (getPromotionDiscount() != null) {
			amount = amount.subtract(getPromotionDiscount());
		}
		if (getCouponDiscount() != null) {
			amount = amount.subtract(getCouponDiscount());
		}
		if (getOffsetAmount() != null) {
			amount = amount.add(getOffsetAmount());
		}
		return amount.compareTo(new BigDecimal(0)) > 0 ? amount
				: new BigDecimal(0);
	}
	
	@Transient
	public BigDecimal getOrderAmount() {
		BigDecimal amount = new BigDecimal(0);
		if (getOrderItems() != null) {
			for (OrderItem orderItem : getOrderItems()) {
				if (orderItem != null && orderItem.getSubtotal() != null) {
					amount=amount.add(orderItem.getPrice().multiply(orderItem.getQuantity()));
				}
			}
		}
		return amount;
	}

	/**
	 * 获取应付金额
	 * 
	 * @return 应付金额
	 */
	@Transient
	public BigDecimal getAmountPayable() {
		BigDecimal amountPayable = getAmount().subtract(getAmountPaid());
		return amountPayable.compareTo(new BigDecimal(0)) > 0 ? amountPayable
				: new BigDecimal(0);
	}

	/**
	 * 是否已过期
	 * 
	 * @return 是否已过期
	 */
	@Transient
	public boolean isExpired() {
		return getExpire() != null && new Date().after(getExpire());
	}

	/**
	 * 获取订单总数量
	 * 
	 * @return 订单总数量
	 */
	@Transient
	public BigDecimal getTotalQuantity() {
		BigDecimal totalQuantity = BigDecimal.ZERO;
		for (OrderItem orderItem : getOrderItems()) {
			if (orderItem != null) {
				totalQuantity = totalQuantity.add(orderItem.getQuantity());
				// totalQuantity += orderItem.getQuantity();
			}
		}
		return totalQuantity;
	}

	/**
	 * 获取订单项
	 * 
	 * @param sn
	 *            商品编号
	 * @return 订单项
	 */
	@Transient
	public OrderItem getOrderItem(String sn) {
		if (sn != null && getOrderItems() != null) {
			for (OrderItem orderItem : getOrderItems()) {
				if (orderItem != null && sn.equalsIgnoreCase(orderItem.getSn())) {
					return orderItem;
				}
			}
		}
		return null;
	}

	/**
	 * 持久化前处理
	 */
	@PrePersist
	public void prePersist() {
		if (getPaymentMethod() != null) {
			setPaymentMethodName(getPaymentMethod().getName());
		}
		if (getShippingMethod() != null) {
			setShippingMethodName(getShippingMethod().getName());
		}
		if (getIsMobile() == null) {
			setIsMobile(false);
		}
		if (getIsInvoice() == null) {
			setIsInvoice(false);
		}
		if (getGoodsAmount() == null) {
			setGoodsAmount(BigDecimal.ZERO);
		}
		if (getConfirmStatus() == null) {
			setConfirmStatus(0);
		}
		if (getIsOutsourcing() == null) {
			setIsOutsourcing(false);
		}
	}

	/**
	 * 更新前处理
	 */
	@PreUpdate
	public void preUpdate() {
		if (getPaymentMethod() != null) {
			setPaymentMethodName(getPaymentMethod().getName());
		}
		if (getShippingMethod() != null) {
			setShippingMethodName(getShippingMethod().getName());
		}
	}

	/**
	 * 删除前处理
	 */
	@PreRemove
	public void preRemove() {
		Set<Deposit> deposits = getDeposits();
		if (deposits != null) {
			for (Deposit deposit : deposits) {
				deposit.setOrder(null);
			}
		}
	}

	/**
	 * 获取 outTradeNo
	 * 
	 * @return outTradeNo
	 */
	public String getOutTradeNo() {
		return outTradeNo;
	}

	/**
	 * 设置 outTradeNo
	 * 
	 * @param outTradeNo
	 */
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	// public String getTrackingNo() {
	// return trackingNo;
	// }
	//
	// public void setTrackingNo(String trackingNo) {
	// this.trackingNo = trackingNo;
	// }
	//
	// public String getWarehouseCode() {
	// return warehouseCode;
	// }
	//
	// public void setWarehouseCode(String warehouseCode) {
	// this.warehouseCode = warehouseCode;
	// }
	//
	// public Long getWarehouseId() {
	// return warehouseId;
	// }
	//
	// public void setWarehouseId(Long warehouseId) {
	// this.warehouseId = warehouseId;
	// }

	@Transient
	public String getAmountToString() {
		DecimalFormat df = new DecimalFormat("0.00");
		return df.format(getAmount());
	}

	@Transient
	public String getPaymentSnToString() {
		try {
			Payment payment = null;
			Set<Payment> payments = this.getPayments();
			for (Payment e : payments) {
				if (e.getStatus().intValue() == 1) {
					payment = e;
					break;
				}
			}

			if (payment == null) {
				for (Payment e : payments) {
					if (e.getStatus().intValue() == 0) {
						payment = e;
						break;
					}
				}
			}

			return payment.getSn();
		}
		catch (NullPointerException e) {
			return "";
		}
	}

	@Transient
	public String getTradeNo() {
		Payment payment = null;
		Set<Payment> payments = this.getPayments();
		for (Payment e : payments) {
			if (e.getStatus().intValue() == 1) {
				payment = e;
				break;
			}
		}

		if (payment != null)
			return payment.getTradeNo();
		else
			return "";
	}

	/**
	 * 获取 订单类型: 0 B2C, 2 B2B, 3 物料推广
	 * 
	 * @return orderType
	 */
	public Integer getOrderType() {
		return orderType;
	}

	/**
	 * 设置 订单类型: 0 B2C, 2 B2B, 3 物料推广
	 * 
	 * @param orderType
	 *            订单类型: 0 B2C, 2 B2B, 3 物料推广
	 */
	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	/**
	 * 获取 物流快递
	 * 
	 * @return delivery
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public DeliveryCorp getDelivery() {
		return delivery;
	}

	/**
	 * 设置 物流快递
	 * 
	 * @param delivery
	 *            物流快递
	 */
	public void setDelivery(DeliveryCorp delivery) {
		this.delivery = delivery;
	}

	/**
	 * 获取 订单改变备注
	 * 
	 * @return changeMemo
	 */
	public String getChangeMemo() {
		return changeMemo;
	}

	/**
	 * 设置 订单改变备注
	 * 
	 * @param changeMemo
	 *            订单改变备注
	 */
	public void setChangeMemo(String changeMemo) {
		this.changeMemo = changeMemo;
	}

	/**
	 * 方法描述: 获取买家备注
	 * 
	 * @return
	 */
	public String getBuyerRemark() {
		return buyerRemark;
	}

	/**
	 * 方法描述: 设置买家备注
	 * 
	 * @param buyerRemark
	 */
	public void setBuyerRemark(String buyerRemark) {
		this.buyerRemark = buyerRemark;
	}

	/**
	 * 方法描述: 获取商家备注
	 * 
	 * @return
	 */
	public String getSellerRemark() {
		return sellerRemark;
	}

	/**
	 * 方法描述: 设置商家备注
	 * 
	 * @param sellerRemark
	 */
	public void setSellerRemark(String sellerRemark) {
		this.sellerRemark = sellerRemark;
	}

	/**
	 * 方法描述: 获取下单时间
	 * 
	 * @return
	 */
	public Date getOrderDate() {
		return orderDate;
	}

	/**
	 * 方法描述: 设置下单时间
	 * 
	 * @param orderDate
	 */
	public void setOrderDate(Date orderDate) {
		this.orderDate = orderDate;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	/**
	 * 获取deliveryMemo
	 * 
	 * @return deliveryMemo
	 */
	public String getDeliveryMemo() {
		return deliveryMemo;
	}

	/**
	 * 设置deliveryMemo
	 * 
	 * @param deliveryMemo
	 *            deliveryMemo
	 */
	public void setDeliveryMemo(String deliveryMemo) {
		this.deliveryMemo = deliveryMemo;
	}

	/**
	 * 获取mobile
	 * 
	 * @return mobile
	 */
	public String getMobile() {
		return mobile;
	}

	/**
	 * 设置mobile
	 * 
	 * @param mobile
	 *            mobile
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	/**
	 * 获取 异常状态：1 已拦截 2 物流拒绝 0 其他
	 * 
	 * @date 2017年7月20日
	 * @return exStatus
	 */
	public Integer getExStatus() {
		return exStatus;
	}

	/**
	 * 设置 异常状态：1 已拦截 2 物流拒绝 0 其他
	 * 
	 * @date 2017年7月20日
	 * @param exStatus
	 *            异常状态：1 已拦截 2 物流拒绝 0 其他
	 */
	public void setExStatus(Integer exStatus) {
		this.exStatus = exStatus;
	}

	/**
	 * 获取 收货人信息======
	 * 
	 * @date 2017年8月28日
	 * @return isInvoice
	 */
	public Boolean getIsInvoice() {
		return isInvoice;
	}

	/**
	 * 设置 收货人信息======
	 * 
	 * @date 2017年8月28日
	 * @param isInvoice
	 *            收货人信息======
	 */
	public void setIsInvoice(Boolean isInvoice) {
		this.isInvoice = isInvoice;
	}

	/**
	 * 获取 发票抬头
	 * 
	 * @date 2017年8月28日
	 * @return invoiceTitle
	 */
	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	/**
	 * 设置 发票抬头
	 * 
	 * @date 2017年8月28日
	 * @param invoiceTitle
	 *            发票抬头
	 */
	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	/**
	 * 获取 发票类型: 0 普通发票 1 增值税专用发票
	 * 
	 * @date 2017年8月28日
	 * @return invoiceType
	 */
	public Integer getInvoiceType() {
		return invoiceType;
	}

	/**
	 * 设置 发票类型: 0 普通发票 1 增值税专用发票
	 * 
	 * @date 2017年8月28日
	 * @param invoiceType
	 *            发票类型: 0 普通发票 1 增值税专用发票
	 */
	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}

	/**
	 * 获取 单位名称
	 * 
	 * @date 2017年8月28日
	 * @return dangweimingchen
	 */
	public String getDangweimingchen() {
		return dangweimingchen;
	}

	/**
	 * 设置 单位名称
	 * 
	 * @date 2017年8月28日
	 * @param dangweimingchen
	 *            单位名称
	 */
	public void setDangweimingchen(String dangweimingchen) {
		this.dangweimingchen = dangweimingchen;
	}

	/**
	 * 获取 纳税人识别号
	 * 
	 * @date 2017年8月28日
	 * @return nashuishibiehao
	 */
	public String getNashuishibiehao() {
		return nashuishibiehao;
	}

	/**
	 * 设置 纳税人识别号
	 * 
	 * @date 2017年8月28日
	 * @param nashuishibiehao
	 *            纳税人识别号
	 */
	public void setNashuishibiehao(String nashuishibiehao) {
		this.nashuishibiehao = nashuishibiehao;
	}

	/**
	 * 获取 注册地址
	 * 
	 * @date 2017年8月28日
	 * @return zhucedizhi
	 */
	public String getZhucedizhi() {
		return zhucedizhi;
	}

	/**
	 * 设置 注册地址
	 * 
	 * @date 2017年8月28日
	 * @param zhucedizhi
	 *            注册地址
	 */
	public void setZhucedizhi(String zhucedizhi) {
		this.zhucedizhi = zhucedizhi;
	}

	/**
	 * 获取 注册电话
	 * 
	 * @date 2017年8月28日
	 * @return zhucedianhua
	 */
	public String getZhucedianhua() {
		return zhucedianhua;
	}

	/**
	 * 设置 注册电话
	 * 
	 * @date 2017年8月28日
	 * @param zhucedianhua
	 *            注册电话
	 */
	public void setZhucedianhua(String zhucedianhua) {
		this.zhucedianhua = zhucedianhua;
	}

	/**
	 * 获取 开户银行
	 * 
	 * @date 2017年8月28日
	 * @return kaihuyh
	 */
	public String getKaihuyh() {
		return kaihuyh;
	}

	/**
	 * 设置 开户银行
	 * 
	 * @date 2017年8月28日
	 * @param kaihuyh
	 *            开户银行
	 */
	public void setKaihuyh(String kaihuyh) {
		this.kaihuyh = kaihuyh;
	}

	/**
	 * 获取 增值税银行帐户
	 * 
	 * @date 2017年8月28日
	 * @return zengzhishuiyhzh
	 */
	public String getZengzhishuiyhzh() {
		return zengzhishuiyhzh;
	}

	/**
	 * 设置 增值税银行帐户
	 * 
	 * @date 2017年8月28日
	 * @param zengzhishuiyhzh
	 *            增值税银行帐户
	 */
	public void setZengzhishuiyhzh(String zengzhishuiyhzh) {
		this.zengzhishuiyhzh = zengzhishuiyhzh;
	}

	/**
	 * 获取 税金
	 * 
	 * @date 2017年8月28日
	 * @return tax
	 */
	public BigDecimal getTax() {
		return tax;
	}

	/**
	 * 设置 税金
	 * 
	 * @date 2017年8月28日
	 * @param tax
	 *            税金
	 */
	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	// /**
	// * 获取 会员
	// * @date 2017年8月28日
	// * @return storeMember
	// */
	// @ManyToOne(fetch = FetchType.LAZY)
	// public Member getMember() {
	// return member;
	// }
	//
	// /**
	// * 设置 会员
	// * @date 2017年8月28日
	// * @param storeMember 会员
	// */
	// public void setMember(Member member) {
	// this.member = member;
	// }

	/**
	 * 获取 仓库
	 * 
	 * @date 2017年8月28日
	 * @return warehouse
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Warehouse getWarehouse() {
		return warehouse;
	}

	/**
	 * 设置 仓库
	 * 
	 * @date 2017年8月28日
	 * @param warehouse
	 *            仓库
	 */
	public void setWarehouse(Warehouse warehouse) {
		this.warehouse = warehouse;
	}

	/**
	 * 获取 发票备注
	 * 
	 * @date 2017年8月28日
	 * @return invoiceRemark
	 */
	public String getInvoiceRemark() {
		return invoiceRemark;
	}

	/**
	 * 设置 发票备注
	 * 
	 * @date 2017年8月28日
	 * @param invoiceRemark
	 *            发票备注
	 */
	public void setInvoiceRemark(String invoiceRemark) {
		this.invoiceRemark = invoiceRemark;
	}

	/**
	 * 获取 操作人
	 * 
	 * @date 2017年9月15日
	 * @return storeMember
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	/**
	 * 设置 操作人
	 * 
	 * @date 2017年9月15日
	 * @param storeMember
	 */
	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getCheckStoreMember() {
		return checkStoreMember;
	}

	public void setCheckStoreMember(StoreMember checkStoreMember) {
		this.checkStoreMember = checkStoreMember;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getCencalStoreMember() {
		return cencalStoreMember;
	}

	public void setCencalStoreMember(StoreMember cencalStoreMember) {
		this.cencalStoreMember = cencalStoreMember;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getBusinessType() {
		return businessType;
	}

	public void setBusinessType(SystemDict businessType) {
		this.businessType = businessType;
	}

	/**
	 * 获取 旗标
	 * 
	 * @return flag
	 */
	public Integer getFlag() {
		return flag;
	}

	/**
	 * 设置 旗标
	 * 
	 * @param flag
	 *            旗标
	 */
	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	/**
	 * 获取 是否是微商城订单
	 * 
	 * @return isMobile
	 */
	public Boolean getIsMobile() {
		return isMobile;
	}

	/**
	 * 设置 是否是微商城订单
	 * 
	 * @param isMobile
	 *            是否是微商城订单
	 */
	public void setIsMobile(Boolean isMobile) {
		this.isMobile = isMobile;
	}

	/**
	 * 获取 机构
	 * 
	 * @return saleOrg
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	/**
	 * 设置 机构
	 * 
	 * @param saleOrg
	 *            机构
	 */
	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	/**
	 * 获取 费用核算对象 0客户 1机构
	 * 
	 * @return accountObj
	 */
	public Integer getAccountObj() {
		return accountObj;
	}

	/**
	 * 设置 费用核算对象 0客户 1机构
	 * 
	 * @param accountObj
	 *            费用核算对象 0客户 1机构
	 */
	public void setAccountObj(Integer accountObj) {
		this.accountObj = accountObj;
	}

	/**
	 * 获取 累计提货总额
	 * 
	 * @return goodsAmount
	 */
	public BigDecimal getGoodsAmount() {
		return goodsAmount;
	}

	/**
	 * 设置 累计提货总额
	 * 
	 * @param goodsAmount
	 *            累计提货总额
	 */
	public void setGoodsAmount(BigDecimal goodsAmount) {
		this.goodsAmount = goodsAmount;
	}

	/**
	 * 获取 确认状态
	 * 
	 * @return confirmStatus
	 */
	public Integer getConfirmStatus() {
		return confirmStatus;
	}

	/**
	 * 设置 确认状态
	 * 
	 * @param confirmStatus
	 *            确认状态
	 */
	public void setConfirmStatus(Integer confirmStatus) {
		this.confirmStatus = confirmStatus;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getFreightChargeType() {
		return freightChargeType;
	}

	public void setFreightChargeType(SystemDict freightChargeType) {
		this.freightChargeType = freightChargeType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Store getSupplier() {
		return supplier;
	}

	public void setSupplier(Store supplier) {
		this.supplier = supplier;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public Integer getOrderCategory() {
		return orderCategory;
	}

	public void setOrderCategory(Integer orderCategory) {
		this.orderCategory = orderCategory;
	}

	/**
	 * 获取 客户经理
	 * 
	 * @return manager
	 */
	public String getManager() {
		return manager;
	}

	/**
	 * 设置 客户经理
	 * 
	 * @param manager
	 *            客户经理
	 */
	public void setManager(String manager) {
		this.manager = manager;
	}

	public BigDecimal getUsePolicyPrice() {
		return usePolicyPrice;
	}

	public void setUsePolicyPrice(BigDecimal usePolicyPrice) {
		this.usePolicyPrice = usePolicyPrice;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getSalesman() {
		return salesman;
	}

	public void setSalesman(StoreMember salesman) {
		this.salesman = salesman;
	}

	public String getContractSn() {
		return contractSn;
	}

	public void setContractSn(String contractSn) {
		this.contractSn = contractSn;
	}

	public String getContractName() {
		return contractName;
	}

	public void setContractName(String contractName) {
		this.contractName = contractName;
	}

	public String getContractType() {
		return contractType;
	}

	public void setContractType(String contractType) {
		this.contractType = contractType;
	}

	public StoreMember getContractMember() {
		return contractMember;
	}

	public void setContractMember(StoreMember contractMember) {
		this.contractMember = contractMember;
	}

	public Integer getTradeType() {
		return tradeType;
	}

	public void setTradeType(Integer tradeType) {
		this.tradeType = tradeType;
	}

	public Integer getSaleType() {
		return saleType;
	}

	public void setSaleType(Integer saleType) {
		this.saleType = saleType;
	}

	public BigDecimal getStoreBalance() {
		return storeBalance;
	}

	public void setStoreBalance(BigDecimal storeBalance) {
		this.storeBalance = storeBalance;
	}

	public BigDecimal getPolicyAmount() {
		return policyAmount;
	}

	public void setPolicyAmount(BigDecimal policyAmount) {
		this.policyAmount = policyAmount;
	}

	public BigDecimal getLowAmount() {
		return lowAmount;
	}

	public void setLowAmount(BigDecimal lowAmount) {
		this.lowAmount = lowAmount;
	}

	public BigDecimal getEarnPercent() {
		return earnPercent;
	}

	public void setEarnPercent(BigDecimal earnPercent) {
		this.earnPercent = earnPercent;
	}

	public Boolean getIsCost() {
		return isCost;
	}

	public void setIsCost(Boolean isCost) {
		this.isCost = isCost;
	}

	public Boolean getIsOffsite() {
		return isOffsite;
	}

	public void setIsOffsite(Boolean isOffsite) {
		this.isOffsite = isOffsite;
	}

	@Transient
	public List<Cost> getCosts() {
		return costs;
	}

	public void setCosts(List<Cost> costs) {
		this.costs = costs;
	}

	@Transient
	public List<OffsiteBranch> getOffsiteBranchs() {
		return offsiteBranchs;
	}

	public void setOffsiteBranchs(List<OffsiteBranch> offsiteBranchs) {
		this.offsiteBranchs = offsiteBranchs;
	}

	@Transient
	public List<CreditRechargeContract> getCreditRechargeContracts() {
		return creditRechargeContracts;
	}

	public void setCreditRechargeContracts(
			List<CreditRechargeContract> creditRechargeContracts) {
		this.creditRechargeContracts = creditRechargeContracts;
	}

	@Transient
	public List<DepositRechargeContract> getDepositRechargeContracts() {
		return depositRechargeContracts;
	}

	public void setDepositRechargeContracts(
			List<DepositRechargeContract> depositRechargeContracts) {
		this.depositRechargeContracts = depositRechargeContracts;
	}

	@Transient
	public List<PolicyCountContract> getPolicyCountContracts() {
		return policyCountContracts;
	}

	public void setPolicyCountContracts(
			List<PolicyCountContract> policyCountContracts) {
		this.policyCountContracts = policyCountContracts;
	}

	public String getAddressOutTradeNo() {
		return addressOutTradeNo;
	}

	public void setAddressOutTradeNo(String addressOutTradeNo) {
		this.addressOutTradeNo = addressOutTradeNo;
	}

	public BigDecimal getChuchangCount() {
		return chuchangCount;
	}

	public void setChuchangCount(BigDecimal chuchangCount) {
		this.chuchangCount = chuchangCount;
	}

	public BigDecimal getMaoliCount() {
		return maoliCount;
	}

	public void setMaoliCount(BigDecimal maoliCount) {
		this.maoliCount = maoliCount;
	}

	public BigDecimal getMaoliPoint() {
		return maoliPoint;
	}

	public void setMaoliPoint(BigDecimal maoliPoint) {
		this.maoliPoint = maoliPoint;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Contract getContract() {
		return contract;
	}

	public void setContract(Contract contract) {
		this.contract = contract;
	}

	/** 获取下达时间*/
	public Date getGoTime() {
		return goTime;
	}

	/** 设置下达时间*/
	public void setGoTime(Date goTime) {
		this.goTime = goTime;
	}

	public Integer getLineNum() {
		return lineNum;
	}

	public void setLineNum(Integer lineNum) {
		this.lineNum = lineNum;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getRegionalManager() {
		return regionalManager;
	}

	public void setRegionalManager(StoreMember regionalManager) {
		this.regionalManager = regionalManager;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Store getDistributor() {
		return distributor;
	}

	public void setDistributor(Store distributor) {
		this.distributor = distributor;
	}

	public String getTicketHolder() {
		return ticketHolder;
	}

	public void setTicketHolder(String ticketHolder) {
		this.ticketHolder = ticketHolder;
	}

	public String getSapSn() {
		return sapSn;
	}

	public void setSapSn(String sapSn) {
		this.sapSn = sapSn;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getCurrency() {
		return currency;
	}

	public void setCurrency(SystemDict currency) {
		this.currency = currency;
	}

	/**
	 * 获取毛利项
	 * 
	 * @return 毛利项
	 */
	@Valid
	@OneToMany(mappedBy = "order", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<GrossProfitAnalysis> getGrossProfitAnalysiss() {
		return grossProfitAnalysiss;
	}

	public void setGrossProfitAnalysiss(
			List<GrossProfitAnalysis> grossProfitAnalysiss) {
		this.grossProfitAnalysiss = grossProfitAnalysiss;
	}

	public String getBargainingCode() {
		return bargainingCode;
	}

	public void setBargainingCode(String bargainingCode) {
		this.bargainingCode = bargainingCode;
	}

	public String getSalesCode() {
		return salesCode;
	}

	public void setSalesCode(String salesCode) {
		this.salesCode = salesCode;
	}

	public String getCardId() {
		return cardId;
	}

	public void setCardId(String cardId) {
		this.cardId = cardId;
	}

	@OneToMany(mappedBy = "orders", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StandbyAddress> getStandbyAddresses() {
		return standbyAddresses;
	}

	public void setStandbyAddresses(List<StandbyAddress> standbyAddresses) {
		this.standbyAddresses = standbyAddresses;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getFactory() {
		return factory;
	}

	public void setFactory(SystemDict factory) {
		this.factory = factory;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getProjectstage() {
		return projectstage;
	}

	public void setProjectstage(SystemDict projectstage) {
		this.projectstage = projectstage;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getIncometype() {
		return incometype;
	}

	public void setIncometype(SystemDict incometype) {
		this.incometype = incometype;
	}

	public Boolean getIsOutsourcing() {
		return isOutsourcing;
	}

	public void setIsOutsourcing(Boolean isOutsourcing) {
		this.isOutsourcing = isOutsourcing;
	}

	public BigDecimal getFkRate() {
		return fkRate;
	}

	public void setFkRate(BigDecimal fkRate) {
		this.fkRate = fkRate;
	}

	public String getSalesAreaName() {
		return salesAreaName;
	}

	public void setSalesAreaName(String salesAreaName) {
		this.salesAreaName = salesAreaName;
	}

    @ManyToOne(fetch = FetchType.LAZY)
    public MemberRank getMemberRank() {
        return memberRank;
    }

    public void setMemberRank(MemberRank memberRank) {
        this.memberRank = memberRank;
    }

	public String getOrderIp() {
		return orderIp;
	}

	public void setOrderIp(String orderIp) {
		this.orderIp = orderIp;
	}

	public String getOrderDevice() {
		return orderDevice;
	}

	public void setOrderDevice(String orderDevice) {
		this.orderDevice = orderDevice;
	}

	public String getOrderEquipment() {
		return orderEquipment;
	}

	public void setOrderEquipment(String orderEquipment) {
		this.orderEquipment = orderEquipment;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getDiscountCode() {
		return discountCode;
	}

	public void setDiscountCode(SystemDict discountCode) {
		this.discountCode = discountCode;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public DepositRecharge getDepositRecharge() {
		return depositRecharge;
	}

	public void setDepositRecharge(DepositRecharge depositRecharge) {
		this.depositRecharge = depositRecharge;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public CustomerContract getCustomerContract() {
		return customerContract;
	}

	public void setCustomerContract(CustomerContract customerContract) {
		this.customerContract = customerContract;
	}

	/**获取出货类型*/
    @ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getOutOfTheWarehouseType() {
        return outOfTheWarehouseType;
    }

    /**设置出货类型*/
    public void setOutOfTheWarehouseType(SystemDict outOfTheWarehouseType) {
        this.outOfTheWarehouseType = outOfTheWarehouseType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Aftersale getAftersale() {
        return aftersale;
    }

    public void setAftersale(Aftersale aftersale) {
        this.aftersale = aftersale;
    }

	public String getFourAftersaleSn() {
		return fourAftersaleSn;
	}

	public void setFourAftersaleSn(String fourAftersaleSn) {
		this.fourAftersaleSn = fourAftersaleSn;
	}
}