package net.shopxx.order.service.impl;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.order.dao.MoveLibraryCloseItemDao;
import net.shopxx.order.entity.MoveLibraryCloseItem;
import net.shopxx.order.service.MoveLibraryCloseItemService;
@Service("moveLibraryCloseItemServiceImpl")
public class MoveLibraryCloseItemServiceImpl extends BaseServiceImpl<MoveLibraryCloseItem> 
	implements MoveLibraryCloseItemService{
	
	@Resource(name = "moveLibraryCloseItemDao")
	private MoveLibraryCloseItemDao moveLibraryCloseItemDao;
	
}
