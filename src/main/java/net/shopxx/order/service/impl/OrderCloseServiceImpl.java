package net.shopxx.order.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import net.shopxx.member.entity.CustomerRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.CustomerRechargeService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.order.dao.OrderCloseDao;
import net.shopxx.order.dao.OrderDao;
import net.shopxx.order.dao.PriceApplyDao;
import net.shopxx.order.dao.ShippingDao;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderChangeItem;
import net.shopxx.order.entity.OrderClose;
import net.shopxx.order.entity.OrderCloseItem;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.PriceApplyItem;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.service.OrderCloseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderHService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.PriceApplyItemService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductStore;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductStoreBaseService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ServletContextAware;

@Service("orderCloseServiceImpl")
public class OrderCloseServiceImpl extends BaseServiceImpl<OrderClose>
		implements OrderCloseService, ServletContextAware {

	@Resource(name = "orderCloseDao")
	private OrderCloseDao orderCloseDao;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "lockDataDao")
	private LockDataDao lockDataDao;
	@Resource(name = "productStoreBaseServiceImpl")
	private ProductStoreBaseService productStoreService;
	@Resource(name = "priceApplyItemServiceImpl")
	private PriceApplyItemService priceApplyItemService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "orderHServiceImpl")
	private OrderHService orderHService;
	@Resource(name = "priceApplyDao")
	private PriceApplyDao priceApplyDao;
	@Resource(name = "shippingDao")
	private ShippingDao shippingDao;
	@Resource(name = "orderDao")
	private OrderDao orderDao;
	@Resource(name = "customerRechargeServiceImpl")
	private CustomerRechargeService customerRechargeService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "shippingInfoToLink5ServiceImpl")
	private ShippingInfoToLink5Service shippingInfoToLink5Service;

	/** servletContext */
	private ServletContext servletContext;

	@Override
	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}

	public void saveOrderClose(OrderClose orderClose) {
		Store store = storeService.find(orderClose.getStore().getId());
		if (store == null) {
			ExceptionUtil.throwServiceException("没找到匹配的客户！");
		}
		orderClose.setStore(store);

		List<OrderCloseItem> orderCloseItems = orderClose.getOrderCloseItems();
		for (Iterator<OrderCloseItem> iterator = orderCloseItems.iterator(); iterator.hasNext();) {
			OrderCloseItem orderCloseItem = iterator.next();
			if (orderCloseItem.getOrderItemId() == null) {
				iterator.remove();
				continue;
			}
			OrderItem orderItem = orderItemService.find(orderCloseItem.getOrderItemId());
			orderCloseItem.setProduct(orderItem.getProduct());

			orderCloseItem.setOrderClose(orderClose);
			if (orderCloseItems.size() == 0) {
				ExceptionUtil.throwServiceException("请维护变更信息！");
			}
		}

		orderClose.setOrderCloseItems(orderCloseItems);
		String sn = Sequence.getInstance().getSequence(null);
		orderClose.setSn(sn);
		orderClose.setStatus(0);
		this.save(orderClose);
	}

	public void updateOrderClose(OrderClose orderClose) {
		List<OrderCloseItem> orderCloseItems = orderClose.getOrderCloseItems();
		for (Iterator<OrderCloseItem> iterator = orderCloseItems.iterator(); iterator.hasNext();) {
			OrderCloseItem orderCloseItem = iterator.next();

			if (orderCloseItem.getOrderItemId() == null) {
				iterator.remove();
				continue;
			}

			OrderItem orderItem = orderItemService.find(orderCloseItem.getOrderItemId());
			orderCloseItem.setProduct(orderItem.getProduct());

			orderCloseItem.setOrderClose(orderClose);

		}
		if (orderCloseItems.size() == 0) {
			ExceptionUtil.throwServiceException("请维护变更信息！");
		}
		this.update(orderClose);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn, String orderSn,
			Integer[] status, Long storeId, Pageable pageable) {
		return orderCloseDao.findPage(sn, orderSn, status, storeId, pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findItemListById(String ids) {
		return orderCloseDao.findItemListById(ids);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findItemListByOrderCloseId(Long id,
			Integer type) {
		return orderCloseDao.findItemListByOrderCloseId(id, type);
	}

	/**
	 * 根据订单明细id找 已 关闭支数
	 * 
	 * @param id
	 * @return
	 */
	@Override
	@Transactional(readOnly = true)
	public Map<String, Object> findClosedQuantityByOrderItemId(Long id) {
		return orderCloseDao.findClosedQuantityByOrderItemId(id);
	}

    @Override
    public List<Map<String, Object>> findOrderClosesById(Long orderId) {
        return orderCloseDao.findOrderClosesById(orderId);
    }


    @Override
	@Transactional
	public void check(OrderClose orderClose) {
		if (orderClose.getStatus() != 0) {
			ExceptionUtil.throwServiceException("关闭单不是已保存状态");
		}
		String sbu =orderClose.getSbu()==null?"地板中心":orderClose.getSbu().getName();
		if(sbu.equals("壁纸")){
			//获取关闭订单明细
			List<OrderCloseItem> orderCloseItems = orderClose.getOrderCloseItems();
			for (OrderCloseItem orderCloseItem : orderCloseItems) {
				//获取订单明细
				OrderItem orderItem = orderItemService.find(orderCloseItem.getOrderItemId());
				//获取可关闭数量
				BigDecimal closeAmount = new BigDecimal(orderCloseDao.findCloseQuantityByOrderCloseItemIdBZ(orderCloseItem.getId()).get("close_quantity").toString());
				//判断关闭数量是不是大于实时关闭数量
				if (orderCloseItem.getQuantity().compareTo(closeAmount) == 1) {
					ExceptionUtil.throwServiceException("订单行【" + orderItem.getName() + "】关闭数量【"
							+ orderCloseItem.getQuantity().setScale(2,RoundingMode.HALF_DOWN) + "】大于可关闭数量【"
									+ closeAmount.setScale(2, RoundingMode.HALF_DOWN) + "】");
				}
			}
			//设置关闭订单头状态更新为已确认
			orderClose.setStatus(1);
			update(orderClose);
			//刷新数据库
			getDaoCenter().getEntityManager().flush();
			Order order = orderClose.getOrder();
			Boolean isOk = true;
			if (order.getOrderItems() != null && order.getOrderItems().size() > 0) {
				for (OrderItem orderitem : order.getOrderItems()) {
						//以关闭数量
						BigDecimal quantity = new BigDecimal(findClosedQuantityByOrderItemId(orderitem.getId()).get("closed_quantity_nb").toString());
						//发货单回传数量
						BigDecimal shipQuantity = orderCloseDao.findShippingByOrderItemId(orderitem.getId());
						//订单行数量=回传数量+已关闭数量 那么这个订单的“完成状态”改成“已完成”
						if (orderitem.getQuantity().compareTo(quantity.add(shipQuantity)) != 0) {
							isOk = false;
							break;
						}
				}
				//流程必须走完，订单为审核状态
				if (!order.getOrderStatus().equals(OrderStatus.audited)) {
					isOk = false;
				}
				if (order.getWfState() != 2) {
					isOk = false;
				}
				if (isOk) {
					getDaoCenter().getEntityManager().flush();//刷新数据库
					getDaoCenter().getNativeDao().update("update xx_order set order_status = 2 where id=?",new Object[] { order.getId() });
				}
			}
		}else{
			List<OrderCloseItem> orderCloseItems = orderClose.getOrderCloseItems();
			for (OrderCloseItem orderCloseItem : orderCloseItems) {
				if(ConvertUtil.isEmpty(orderCloseItem.getOrderItemId())){
					ExceptionUtil.throwServiceException("订单明细Id不能为空");
				}
				OrderItem orderItem = orderItemService.find(orderCloseItem.getOrderItemId());
				if(ConvertUtil.isEmpty(orderItem)){
					ExceptionUtil.throwServiceException("订单明细不能为空");
				}
				BigDecimal closeAmount = new BigDecimal(orderCloseDao.findCloseQuantityByOrderCloseItemId(orderCloseItem.getId()).get("close_quantity").toString());
				if (orderCloseItem.getBranchQuantity().compareTo(closeAmount) == 1) {
					ExceptionUtil.throwServiceException("订单行【" + orderItem.getName() + "】关闭支数【"
							+ orderCloseItem.getBranchQuantity().setScale(2,RoundingMode.HALF_DOWN) + "】大于可关闭支数【"
									+ closeAmount.setScale(2, RoundingMode.HALF_DOWN)+ "】");
				}
				if(ConvertUtil.isEmpty(orderItem.getOrder()) || 
						(!ConvertUtil.isEmpty(orderItem.getOrder()) && 
								ConvertUtil.isEmpty(orderItem.getOrder().getId()))){
					ExceptionUtil.throwServiceException("订单不能为空");
				}
				if(ConvertUtil.isEmpty(orderCloseItem.getPrice())){
					ExceptionUtil.throwServiceException("关闭行单价不能为空");
				}
				if(ConvertUtil.isEmpty(orderCloseItem.getQuantity())){
					ExceptionUtil.throwServiceException("关闭行数量不能为空");
				}
				//客户充值
				CustomerRecharge customerRecharge = new CustomerRecharge();
				//客户
				customerRecharge.setStore(orderItem.getOrder().getStore());
				//经营组织
				customerRecharge.setOrganization(orderItem.getProductOrganization());
				//sbu
				customerRecharge.setSbu(orderItem.getOrder().getSbu());
				customerRecharge = customerRechargeService.saveOrUpdate(customerRecharge);
				if(!ConvertUtil.isEmpty(customerRecharge) && !ConvertUtil.isEmpty(customerRecharge.getId())){
					//订单金额
					LogUtils.debug("========监控关闭单=======");
					LogUtils.debug("原有余额："+customerRecharge.getBalance());
					customerRecharge.setOrderBalance(
							customerRecharge.getOrderBalance().subtract(
                                   orderCloseItem.getPrice().multiply(orderCloseItem.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP)));
					LogUtils.debug("关闭单的："+orderCloseItem.getPrice().multiply(orderCloseItem.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
					LogUtils.debug("加完以后的："+customerRecharge.getBalance());
					LogUtils.debug("========监控关闭单=======");
					customerRechargeService.update(customerRecharge);
				}else{
					throw new RuntimeException("当前审核异常，请管理员进行维护");
				}
			}
			orderClose.setStatus(1);
			update(orderClose);
			//刷新数据库
			getDaoCenter().getEntityManager().flush();
			//如果关闭订单行之后所有行名细：支数+零散支数 = 已发货支数+已关闭支数，那么这个订单的“完成状态”改成“已完成”
			Order order = orderClose.getOrder();
			Boolean isOk = true;
			if (order.getOrderItems() != null && order.getOrderItems().size() > 0) {
				for (OrderItem orderitem : order.getOrderItems()) {
					BigDecimal closeAmount = new BigDecimal(findClosedQuantityByOrderItemId(orderitem.getId()).get("closed_quantity").toString());
					if (orderitem.getProduct().getPerBranch() != null && orderitem.getProduct().getPerBranch().compareTo(BigDecimal.ZERO) == 1) {
						BigDecimal ShippedBranch = orderitem.getShippedQuantity().divide(orderitem.getProduct().getPerBranch(),0,BigDecimal.ROUND_HALF_UP);
						BigDecimal closeAndShipped = ShippedBranch.add(closeAmount);
						if (orderitem.getBranchQuantity().compareTo(closeAndShipped) != 0) {
							isOk = false;
							break;
						}
					}else {
						BigDecimal quantity = new BigDecimal(findClosedQuantityByOrderItemId(orderitem.getId()).get("closed_quantity_nb").toString());
						if (orderitem.getQuantity().compareTo(quantity) != 0) {
							isOk = false;
							break;
						}
					}
				}
				//流程必须走完，订单为审核状态
				if (!order.getOrderStatus().equals(OrderStatus.audited)) {
					isOk = false;
				}
				if (order.getWfState() != 2) {
					isOk = false;
				}
				List<String> orderSns = new ArrayList<String>();
				orderSns.add(order.getSn());
				if (isOk) {
					orderFullLinkService.addFullLink(1, orderSns, order.getSn(), "该订单已操作完全关闭，订单关闭单号为【"+orderClose.getSn()+"】", null);
					//刷新数据库
					getDaoCenter().getEntityManager().flush();
					getDaoCenter().getNativeDao().update("update xx_order set order_status = 2 where id=?",new Object[] { order.getId() });
					
				}else{
					orderFullLinkService.addFullLink(1, orderSns, order.getSn(), "该订单已操作部分关闭，订单关闭单号为【"+orderClose.getSn()+"】", null);
				}
				if(order.getStore().getIsToLink5()==null ? false: order.getStore().getIsToLink5() 
						&& !(order.getWarehouse().getIsOrderWarehousing()==null ? false:order.getWarehouse().getIsOrderWarehousing())) {
					this.saveOrderIntfLink5Check(order,3);
				}
			}
		}
	}
    
    public void saveOrderIntfLink5Check(Order order,Integer flag) {

		Long companyInfoId = order.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

			shippingInfoToLink5Service.saveOrderInfoTo(order,flag);
		
	}

	@Override
	@Transactional
	public void checkNew(OrderClose orderClose) {
		if (orderClose.getStatus() != 0) {
			ExceptionUtil.throwServiceException("关闭单不是已保存状态");
		}
		List<OrderCloseItem> orderCloseItems = orderClose.getOrderCloseItems();
		for (OrderCloseItem orderCloseItem : orderCloseItems) {
			OrderItem orderItem = orderItemService.find(orderCloseItem.getOrderItemId());
			BigDecimal closeAmount = new BigDecimal(
					orderCloseDao.findCloseQuantityByOrderCloseItemId(orderCloseItem.getId())
							.get("close_quantity_nb")
							.toString());
			if (orderCloseItem.getQuantity().compareTo(closeAmount) == 1) {
				ExceptionUtil.throwServiceException("订单行【"
						+ orderItem.getName()
						+ "】关闭数【"
						+ orderCloseItem.getQuantity().setScale(2,
								RoundingMode.HALF_DOWN)
						+ "】大于可关闭数【"
						+ closeAmount.setScale(2, RoundingMode.HALF_DOWN)
						+ "】");
			}
		}
		orderClose.setStatus(1);
		update(orderClose);
	}

	// public void checkOrderChange(OrderChange orderChange) throws Exception {
	// if (orderChange.getStatus() == 1) {
	// ExceptionUtil.throwServiceException("变更单不是未确认状态");
	// }
	//
	// // check(orderChange);
	// }

	// public void check(OrderChange orderChange) throws Exception {
	//
	// if (orderChange.getStatus() == 1) {
	// ExceptionUtil.throwServiceException("变更单不是未确认状态");
	// }
	// // 锁客户
	// Store store = orderChange.getStore();
	// lockDataDao.lockStore(store.getId().toString());
	//
	// orderChange.setStatus(1);
	// this.update(orderChange);
	//
	// /** 记录订单历史表 */
	// List<String> orderHs = new ArrayList<String>();
	// for (OrderChangeItem orderChangeItem : orderChange.getOrderChangeItems())
	// {
	// if (orderChangeItem.getOrderSn() != null &&
	// !orderHs.contains(orderChangeItem.getOrderSn())) {
	// Order order = findOrderBySn(orderChangeItem.getOrderSn());
	// // 锁订单
	// lockDataDao.lockOrder(order.getId().toString());
	//
	// orderHService.saveOrderH(order);
	// orderHs.add(orderChangeItem.getOrderSn());
	// }
	//
	// }
	//
	// // 作废行数据
	// List<Map<String, Object>> closeOrNewLines = new ArrayList<Map<String,
	// Object>>();
	//
	// Store mainStore = storeService.getMainStore();
	// StoreMember checkStoreMember = null;
	// BigDecimal oldAmount = BigDecimal.ZERO;
	// if (orderChange.getType() == 1) {
	// Order order = findOrderBySn(orderChange.getOrderSn());
	// for (OrderItem orderItem : order.getOrderItems()) {
	// boolean hasShipped = shippingDao.hasShipped(orderItem.getId());
	// if (hasShipped) {
	// ExceptionUtil.throwServiceException("订单【" + order.getSn() +
	// "】已发货，无法变更！");
	// }
	// }
	// checkStoreMember = order.getCheckStoreMember();
	// orderService.rejectOrder(order);
	// orderService.getDaoCenter().getObjDao().flush();
	// orderService.cancelOrder(new Long[] { order.getId() });
	//
	// } else {
	// Order pOrder = findOrderBySn(orderChange.getOrderSn());
	// checkStoreMember = pOrder.getCheckStoreMember();
	// BigDecimal pAmount = pOrder.getAmount();
	// oldAmount = pAmount;
	// List<Long> deleteIds = new ArrayList<Long>();
	// for (OrderChangeItem orderChangeItem : orderChange.getOrderChangeItems())
	// {
	// Order order = findOrderBySn(orderChangeItem.getOrderSn());
	// Integer type = orderChangeItem.getType();
	// if (type == 0) {
	// // 变更订单主表信息
	// order = findOrderBySn(orderChangeItem.getOrderSn());
	// this.updateOrder(orderChangeItem, order);
	// } else if (type == 2) {
	// // 新增明细
	// OrderItem orderItem = this.addOrderItem(orderChangeItem, mainStore,
	// order);
	//
	// Map<String, Object> changeLine = this.createOrderChangeLine(orderItem,
	// 0);
	// closeOrNewLines.add(changeLine);
	// } else if (type == 3) {
	// // 修改明细
	// this.upateOrderItem(orderChangeItem);
	// } else if (type == 4) {
	// // 删除明细
	// OrderItem orderItem =
	// orderItemService.find(orderChangeItem.getOrderItemId());
	// if (orderItem == null) {
	// ExceptionUtil.throwServiceException(
	// "订单【" + order.getSn() + "】,产品【" + orderChangeItem.getAfterName() +
	// "】已作废！");
	// }
	// Map<String, Object> changeLine = this.createOrderChangeLine(orderItem,
	// 1);
	// closeOrNewLines.add(changeLine);
	//
	// deleteIds.add(orderItem.getId());
	// // orderItemService.delete(orderItem);
	// }
	//
	// if (orderChangeItem.getOrderItemId() != null) {
	// // 修改或作废
	// if (type == 4) {
	// boolean hasShipped =
	// shippingDao.hasShipped(orderChangeItem.getOrderItemId());
	// if (hasShipped) {
	// ExceptionUtil.throwServiceException(
	// "订单【" + order.getSn() + "】,物料【" + orderChangeItem.getAfterName() +
	// "】已发货，无法变更！");
	// }
	// }
	// }
	// }
	//
	// Order order = findOrderBySn(orderChange.getOrderSn());
	// List<OrderItem> orderItems = order.getOrderItems();
	// for (Iterator<OrderItem> iterator = orderItems.iterator();
	// iterator.hasNext();) {
	// OrderItem orderItem = iterator.next();
	// if (deleteIds.contains(orderItem.getId())) {
	// iterator.remove();
	// continue;
	// }
	// }
	//
	// // 设置发货状态
	// BigDecimal orderQuantity = order.getQuantity();
	// BigDecimal orderShippedQuantity = order.getShippedQuantity();
	// if (orderShippedQuantity.doubleValue() == 0) {
	// order.setShippingStatus(Order.ShippingStatus.unshipped);
	// } else if (orderQuantity.compareTo(orderShippedQuantity) == 1) {
	// order.setShippingStatus(Order.ShippingStatus.partialShipment);
	// } else if (orderQuantity.compareTo(orderShippedQuantity) == 0) {
	// order.setShippingStatus(Order.ShippingStatus.shipped);
	// }
	//
	// orderService.update(order);
	//
	// // 客户余额判断
	// if (store.getIsReduceBalance() != null && store.getIsReduceBalance()) {
	// BigDecimal balance = storeBalanceService.findBalance(store.getId(),
	// order.getOrganization() == null ? null :
	// order.getOrganization().getId());
	// balance = balance.add(pAmount);
	// if (balance.compareTo(order.getAmount()) == -1) {
	// //// 客户余额不足
	// ExceptionUtil.throwServiceException("15201");
	// }
	// }
	// }
	//
	// Order order = findOrderBySn(orderChange.getOrderSn());
	//
	// // 全链路
	// List<String> orderSns = new ArrayList<String>();
	// orderSns.add(order.getSn());
	// orderFullLinkService.addFullLink(1, orderSns, order.getSn(), "变更单修改订单信息",
	// null);
	// BigDecimal subtract = BigDecimal.ZERO;
	// if (oldAmount.compareTo(order.getAmount()) == -1) {
	// subtract = oldAmount.subtract(order.getAmount()).multiply(new
	// BigDecimal("-1")).setScale(2,
	// BigDecimal.ROUND_HALF_UP);
	// } else {
	// subtract = oldAmount.subtract(order.getAmount()).setScale(2,
	// BigDecimal.ROUND_HALF_UP);
	// }
	// orderFullLinkService
	// .addFullLink(1,
	// orderSns, order.getSn(), "支付信息：当前订单变更，金额由￥" + oldAmount.setScale(2,
	// BigDecimal.ROUND_HALF_UP)
	// + "变为" + order.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP) +
	// "，差额为￥" + subtract,
	// null);
	//
	// // PDF处理
	// List<String> files = new ArrayList<String>();
	// if (OrderStatus.audited.equals(order.getOrderStatus())) {
	// // 已审核的订单需要重新生成PDF
	// TriplicateForm triplicateForm = orderService.createTriplicateForm(order);
	// files.add(triplicateForm.getUrl());
	// }
	// }

	public Map<String, Object> createOrderChangeLine(OrderItem orderItem,
			int changeType) {
		Map<String, Object> changeLine = new HashMap<String, Object>();
		changeLine.put("id", orderItem.getId().toString());
		changeLine.put("oper", changeType);
		changeLine.put("productNo", orderItem.getVonderCode());
		changeLine.put("price", orderItem.getPrice());
		changeLine.put("qty", orderItem.getQuantity());
		changeLine.put("amount",
				orderItem.getQuantity().multiply(orderItem.getPrice()));
		// closeLine.put("sendDate", "");
		if (orderItem.getDeliverDate() != null) {
			changeLine.put("deliveryDate",
					DateUtil.convert(orderItem.getDeliverDate()));
		}
		changeLine.put("sendDate", DateUtil.convert(orderItem.getCreateDate()));

		changeLine.put("note", orderItem.getSellerMemo());
		return changeLine;
	}

	/** 根据变更单明细修改订单主表 */
	public void updateOrder(OrderChangeItem orderChangeItem, Order order) {
		order.setMemo(orderChangeItem.getAfterMemo());
		order.setConsignee(orderChangeItem.getAfterConsignee());
		order.setPhone(orderChangeItem.getAfterPhone());
		order.setZipCode(orderChangeItem.getAfterZipCode());
		order.setArea(orderChangeItem.getAfterArea());
		order.setAddress(orderChangeItem.getAfterAddress());
		order.setFreightChargeType(orderChangeItem.getAfterFreightChargeType());
		order.setShippingMethod(orderChangeItem.getAfterShippingMethod());
		orderService.update(order);
	}

	/** 根据变更明细新增订单明细 */
	public OrderItem addOrderItem(OrderChangeItem orderChangeItem,
			Store mainStore, Order order) {
		Product product = orderChangeItem.getProduct();
		ProductStore productStore = null;
		if (product != null) {
			productStore = findProductStore(product, mainStore);
		}

		OrderItem orderItem = new OrderItem();
		orderItem.setSn((product != null) ? product.getSn() : null);
		orderItem.setVonderCode((product != null) ? product.getVonderCode()
				: orderChangeItem.getAfterVonderCode());
		orderItem.setModel((product != null) ? product.getModel()
				: orderChangeItem.getAfterModel());
		orderItem.setName((product != null) ? product.getName()
				: orderChangeItem.getAfterName());
		orderItem.setFullName((product != null) ? product.getName()
				: orderChangeItem.getAfterName());
		orderItem.setThumbnail((product != null) ? product.getThumbnail()
				: null);
		orderItem.setBarCode((product != null) ? product.getBarCode() : null);
		orderItem.setProduct(product);
		orderItem.setProductStore(productStore);
		orderItem.setPrice(orderChangeItem.getAfterPrice());
		orderItem.setOrigPrice(orderChangeItem.getAfterPrice());
		orderItem.setOrigMemberPrice(orderChangeItem.getAfterPrice());
		orderItem.setQuantity(orderChangeItem.getAfterQuantity());
		orderItem.setDeliveryTime(orderChangeItem.getAfterDeliveryTime());
		orderItem.setOrder(order);
		orderItem.setProductCategory((product != null) ? product.getProductCategory()
				: null);
		orderItem.setPriceApplySn(orderChangeItem.getPriceApplySn());
		orderItem.setDeliverDate(orderChangeItem.getAfterDeliverDate());
		orderItem.setBuyerMemo(orderChangeItem.getAfterBuyerMemo());

		// 特价单处理
		if (orderChangeItem.getPriceApplySn() != null
				&& orderChangeItem.getPriceApplyItemId() != null) {
			PriceApplyItem priceApplyItem = priceApplyItemService.find(orderChangeItem.getPriceApplyItemId());
			priceApplyDao.lockPriceApplyItem(priceApplyItem.getId().toString());
			BigDecimal ableQuantity = priceApplyItem.getQuantity()
					.subtract(priceApplyItem.getUsedQuantity());
			if (orderItem.getQuantity().doubleValue() > ableQuantity.doubleValue()) {
				// 订单明细产品[{0}]下单数量[{1}]已超过特价申请可使用数量[{2}]
				ExceptionUtil.throwServiceException("17553",
						orderItem.getName(),
						orderItem.getQuantity(),
						ableQuantity);
			}

			priceApplyItem.setUsedQuantity(priceApplyItem.getUsedQuantity()
					.add(orderItem.getQuantity()));
			priceApplyItemService.update(priceApplyItem);

			orderItem.setPriceApplyItem(priceApplyItem);
		}
		orderItemService.save(orderItem);
		return orderItem;
	}

	/** 根据变更明细修改订单明细 */
	public OrderItem upateOrderItem(OrderChangeItem orderChangeItem) {
		Product product = orderChangeItem.getProduct();
		OrderItem orderItem = orderItemService.find(orderChangeItem.getOrderItemId());
		BigDecimal shippedQuantity = orderItem.getShippedQuantity();
		BigDecimal oldQuantity = orderItem.getQuantity();

		if (orderChangeItem.getAfterQuantity().doubleValue() < shippedQuantity.doubleValue()) {
			ExceptionUtil.throwServiceException("订单【"
					+ orderChangeItem.getOrderSn()
					+ "】,物料【"
					+ orderChangeItem.getAfterName()
					+ "】已发货:"
					+ shippedQuantity.doubleValue()
					+ ",变更数量:"
					+ orderChangeItem.getAfterQuantity().doubleValue()
					+ "！");
		}

		if (product == null) {
			orderItem.setName(orderChangeItem.getAfterName());
			orderItem.setModel(orderChangeItem.getAfterModel());
			orderItem.setVonderCode(orderChangeItem.getAfterVonderCode());
		}
		orderItem.setPrice(orderChangeItem.getAfterPrice());
		orderItem.setQuantity(orderChangeItem.getAfterQuantity());
		orderItem.setDeliveryTime(orderChangeItem.getAfterDeliveryTime());
		orderItem.setDeliverDate(orderChangeItem.getAfterDeliverDate());
		orderItem.setBuyerMemo(orderChangeItem.getAfterBuyerMemo());

		// 特价单处理
		if (orderChangeItem.getPriceApplySn() != null
				&& orderChangeItem.getPriceApplyItemId() != null) {
			PriceApplyItem priceApplyItem = priceApplyItemService.find(orderChangeItem.getPriceApplyItemId());
			priceApplyDao.lockPriceApplyItem(priceApplyItem.getId().toString());

			BigDecimal ableQuantity = priceApplyItem.getQuantity()
					.subtract(priceApplyItem.getUsedQuantity());
			BigDecimal specQuantity = orderItem.getQuantity()
					.subtract(oldQuantity);
			if (specQuantity.doubleValue() > ableQuantity.doubleValue()) {
				// 订单明细产品[{0}]下单数量[{1}]已超过特价申请可使用数量[{2}]
				ExceptionUtil.throwServiceException("17553",
						orderItem.getName(),
						orderItem.getQuantity(),
						ableQuantity);
			}

			priceApplyItem.setUsedQuantity(priceApplyItem.getUsedQuantity()
					.add(specQuantity));
			priceApplyItemService.update(priceApplyItem);

			orderItem.setPriceApplySn(orderChangeItem.getPriceApplySn());
			orderItem.setPriceApplyItem(priceApplyItem);
		}
		else {
			PriceApplyItem priceApplyItem = orderItem.getPriceApplyItem();
			if (priceApplyItem != null) {
				priceApplyDao.lockPriceApplyItem(priceApplyItem.getId()
						.toString());

				priceApplyItem.setUsedQuantity(priceApplyItem.getUsedQuantity()
						.subtract(orderItem.getQuantity()));
				priceApplyItemService.update(priceApplyItem);
			}
			orderItem.setPriceApplySn(null);
			orderItem.setPriceApplyItem(null);
		}

		orderItemService.update(orderItem);
		return orderItem;
	}

	@Transactional(readOnly = true)
	public int deleteFile(String staticPath) {

		File staticFile = new File(servletContext.getRealPath(staticPath));
		if (staticFile.exists()) {
			staticFile.delete();
			return 1;
		}
		return 0;
	}

	@Transactional(readOnly = true)
	public Order findOrderBySn(String sn) {
		Order order = null;
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("sn", sn));
		List<Order> orders = orderService.findList(1, filters, null);
		if (orders.size() > 0) {
			order = orders.get(0);
		}
		return order;
	}

	@Transactional(readOnly = true)
	public ProductStore findProductStore(Product product, Store store) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("product", product));
		filters.add(Filter.eq("store", store));
		ProductStore productStore = productStoreService.find(filters);
		return productStore;
	}

}
