package net.shopxx.order.service.impl;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.service.B2bReturnsItemService;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.*;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.entity.*;
import net.shopxx.order.service.*;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseBatch;
import net.shopxx.stock.entity.WarehouseLocation;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseBatchService;
import net.shopxx.stock.service.WarehouseLocationService;
import net.shopxx.stock.service.WarehouseStockService;
import net.shopxx.util.SnUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : JiaWaYunHandleServiceImpl
 * @Deseription : 家哇云回传的出入库单
 * <AUTHOR> LanTianLong
 * @Date : 2020/12/16 11:29
 * @Version : 1.0
 **/
@Service("jiaWaYunHandleServiceImpl")
public class JiaWaYunHandleServiceImpl extends BaseServiceImpl<AmShipping> implements JiaWaYunHandleService {

    @Resource(name = "companyInfoBaseServiceImpl")
    CompanyInfoBaseService companyInfoService;
    @Resource(name = "amShippingServiceImpl")
    AmShippingService amShippingService;
    @Resource(name = "amShippingItemServiceImpl")
    AmShippingItemService amShippingItemService;
    @Resource(name = "storeMemberBaseServiceImpl")
    StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "systemDictBaseServiceImpl")
    SystemDictBaseService systemDictBaseService;
    @Resource(name = "areaBaseServiceImpl")
    AreaBaseService areaBaseService;
    @Resource(name = "warehouseLocationServiceImpl")
    WarehouseLocationService warehouseLocationService;
    @Resource(name = "warehouseStockServiceImpl")
    WarehouseStockService warehouseStockService;
    @Resource(name = "totalDateServiceImpl")
    private TotalDateService totalDateService;
    @Resource(name = "warehouseBatchServiceImpl")
    WarehouseBatchService warehouseBatchService;
    @Resource(name = "saleShippingNatureServiceImpl")
    SaleShippingNatureService saleShippingNatureService;
    @Resource(name = "shippingServiceImpl")
    private ShippingService shippingService;
    @Resource(name = "b2bReturnsServiceImpl")
    private B2bReturnsService b2bReturnsService;
    @Resource(name = "b2bReturnsItemServiceImpl")
    private B2bReturnsItemService b2bReturnsItemService;
    @Resource(name = "shippingItemServiceImpl")
    private ShippingItemService shippingItemService;
    @Resource(name = "orderItemServiceImpl")
    private OrderItemService orderItemService;
    @Resource(name = "orderServiceImpl")
    private OrderService orderService;
    @Resource(name = "warehouseBaseServiceImpl")
    private WarehouseBaseService warehouseService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "organizationServiceImpl")
    private OrganizationService organizationService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productService;
    @Resource(name = "sbuServiceImpl")
    private SbuService sbuService;
    @Resource(name = "moveLibraryServiceImpl")
    private MoveLibraryService moveLibraryService;
    @Resource(name = "moveLibraryItemServiceImpl")
    private MoveLibraryItemService moveLibraryItemService;
    @Resource(name = "moveLibraryCloseItemServiceImpl")
    private MoveLibraryCloseItemService moveLibraryCloseItemService;
    @Resource(name = "moveLibraryCloseServiceImpl")
    private MoveLibraryCloseService moveLibraryCloseService;
    @Resource(name = "logisticsHandleServiceImpl")
    private LogisticsHandService logisticsHandService;

    /**
     * @Deseription 处理家哇云回传的单据
     * <AUTHOR>
     */
    @Transactional
    @Override
    public String MessageBack(Map<String, Object> map) throws Exception {

            //根据单据类型选择相应的单据处理
            Map<String, Object> mapReceive = (Map<String, Object>) map.get("requestInfo");
            String documentType = mapReceive.get("billTypeName") == null ? null : mapReceive.get("documentTypeId").toString();
            if(documentType == null){
                throw new RuntimeException("E001A-单据类型billTypeName为空");
            }else if(documentType.equals("出入库")){
                //处理出入库单
                return amshippingMessageBack(mapReceive);
            }else if(documentType.equals("发货")){
                //处理发货单实发
                return shippingMessageBack(mapReceive);
            }else if(documentType.equals("退货")){
                //处理退货单实发
                return b2bReturnMessageBack(mapReceive);
            }else if(documentType.equals("移库")){
                //处理移库单实发
                return moveMessageBack(mapReceive);
            }else if(documentType.equals("移库关闭")){
                //处理移库单关闭
                 return moveCloseMessageBack(mapReceive);
            }else if(documentType.equals("库存盘点")){
                //处理库存盘点
                 return inventoryMessageBack(mapReceive);
            }else if(documentType.equals("物流信息")){
                logisticsHandService.MessageBack(map);
            }
        return "F";
    }



    /**
     *  处理家哇云回传的发货单信息-生成相应的出入库单
     * <AUTHOR>
     */

     @SuppressWarnings({ "unchecked", "null" })
     public String shippingMessageBack(Map<String, Object> mapReceive) throws Exception {
         List<Filter> filters = new ArrayList<Filter>();
         Long companyInfo = 9L;
         AmShipping amShippingNew = new AmShipping();
         Shipping shippingSource;


         //校验来源发货单
         /*********来源单据id：推送唯一性校验********/
         String sourceId = mapReceive.get("sourceId") == null ? null : mapReceive.get("sourceId").toString();
         String sourceSn = mapReceive.get("sourceSn") == null ? null : mapReceive.get("sourceSn").toString();
         if (sourceId == null) {
             throw new RuntimeException("E-来源单号ID为空");
         }
         filters.clear();
         filters.add(Filter.eq("id", Long.valueOf(sourceId)));
         filters.add(Filter.eq("sn", Long.valueOf(sourceSn)));
         List<Shipping> shippingList = shippingService.findList(null, filters, null);
         if (shippingList.size() == 1) {
             shippingSource = shippingList.get(0);
         } else {
             throw new RuntimeException("E-不存在此来源单");
         }


         amShippingNew.setSaleOrg(shippingSource.getSaleOrg());
         amShippingNew.setSbu(shippingSource.getSbu());

         //检验是否有已存在的家哇云单据生成了AM出入库单
         /*********家哇云单据ID：唯一校验，如果无则新增，有则报错********/
         String shippingId = mapReceive.get("jiawayunId") == null ? null : mapReceive.get("shippingId").toString();
         if (shippingId == null) {
             throw new RuntimeException("E-家哇云ID为空");
         }
         filters.clear();
         filters.add(Filter.eq("jiaWaYunId", Long.valueOf(shippingId)));
         List<AmShipping> amShippings = amShippingService.findList(null, filters, null);
         if (amShippings.size() < 1) {
             amShippingNew.setJiaWaYunId(Long.valueOf(shippingId));
         }else {
             throw new RuntimeException("E-家哇云ID重复");
         }

         /*********类型：校验类型是否为空********/
         String type = mapReceive.get("type") == null ? null : mapReceive.get("type").toString();
         if (type == null) {
             throw new RuntimeException("E-单据类型为空：出仓或是入仓");
         }else if(!type.equals("出仓")){
             throw new RuntimeException("E-单据类型出错：发货单应为出仓");
         }
         List<SystemDict> systemDictLists = findSystemDictListByCode(type,"billType");
         if (systemDictLists.size() < 1) {
             throw new RuntimeException("E-该单据类型不存在");
         } else {
             amShippingNew.setBillType(systemDictLists.get(0));
         }

         /*********家哇云单据号：必输，不为空********/
         String shippingSn = mapReceive.get("shippingSn") == null ? null : mapReceive.get("shippingSn").toString();
         if (shippingSn == null) {
             throw new RuntimeException("E-家哇云单据号不能为空");
         } else {
             amShippingNew.setJiaWaYunSn(shippingSn);
         }


         /*********客户编码：查询并校验客户是否变更********/
         String outTradeNo = mapReceive.get("outTradeNo") == null ? null : mapReceive.get("outTradeNo").toString();
         if(shippingSource.getStore() != null) {
             if (!shippingSource.getStore().getOutTradeNo().equals(outTradeNo)){
                 throw new RuntimeException("E-客户与来源单的不同");
             }
         }
         amShippingNew.setStore(shippingSource.getStore());

         /*********仓库：查询并校验仓库是否变更********/
         String erpWarehouseCode = mapReceive.get("erpWarehouseCode") == null ? null : mapReceive.get("erpWarehouseCode").toString();
         if (erpWarehouseCode != null) {
             if(!shippingSource.getWarehouse().getErp_warehouse_code().equals(erpWarehouseCode)){
                 throw new RuntimeException("E-仓库与来源单的不同");
             }
         }else{
             throw new RuntimeException("E-仓库编码不能为空");
         }
         amShippingNew.setWarehouse(shippingSource.getWarehouse());


         /*********单据类别：校验是必输,词汇********/
         String billCategory = mapReceive.get("billCategory") == null ? null : mapReceive.get("billCategory").toString();
         if (billCategory == null) {
             throw new RuntimeException("E-单据类别不能为空");
         }else {
              systemDictLists = findSystemDictListByCode(billCategory, "billCategory");
             if (systemDictLists.size() < 1) {
                 throw new RuntimeException("E-该单据类别不存在");
             } else {
                 amShippingNew.setBillCategory(systemDictLists.get(0));
             }
         }

         /*********设置创建人与审核人********/
         String checkMan = mapReceive.get("checkMan") == null ? null : mapReceive.get("checkMan").toString();
         if (checkMan == null) {
             throw new RuntimeException("E-审核人不能为空");
         }
         filters.clear();
         filters.add(Filter.eq("companyInfoId", companyInfo));
         filters.add(Filter.eq("username", checkMan));
         filters.add(Filter.eq("isEnabled", 1));
         List<StoreMember> storeMembers = storeMemberBaseService.findList(null, filters, null);
         if (storeMembers.size() == 1) {
             amShippingNew.setCheckStoreMember(storeMembers.get(0));
         } else {
             throw new RuntimeException("E-该审核人不存在");
         }

         /*********单据日期：校验是否在总账日期范围内********/
         String billDate = mapReceive.get("billDate") == null ? null : mapReceive.get("billDate").toString();
         if (billDate == null) {
             LogUtils.error("E-单据日期不能为空");
             throw new RuntimeException("E-单据日期不能为空");
         }
         SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
         Date date = simpleDateFormat.parse(billDate);
         Long[] organizationIds = new Long[shippingSource.getShippingItems().size()];
         List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, shippingSource.getSaleOrg().getId(),
                 shippingSource.getSbu().getId(), organizationIds, date,null);
         if (mapList.isEmpty() || mapList.size() == 0) {
             ExceptionUtil.throwServiceException("E-该单据日期不包含在总账日期内，请填写合适的单据日期");
         }
         amShippingNew.setBillDate(date);

         String containerNumber = mapReceive.get("containerNumber") == null ? null : mapReceive.get("containerNumber").toString();//柜号
         amShippingNew.setContainerNumber(containerNumber);
         String consignee = mapReceive.get("consignee") == null ? null : mapReceive.get("consignee").toString();//收货人
         amShippingNew.setConsignee(consignee);
         String phone = mapReceive.get("phone") == null ? null : mapReceive.get("phone").toString();//收货人电话
         amShippingNew.setPhone(phone);
         String areaName = mapReceive.get("areaName") == null ? null : mapReceive.get("areaName").toString();//收货地区
         filters.clear();
         filters.add(Filter.eq("fullName", areaName));
         List<Area> areas = areaBaseService.findList(null, filters, null);
         amShippingNew.setArea(areas.get(0));
         String address = mapReceive.get("address") == null ? null : mapReceive.get("address").toString();//收货地址
         amShippingNew.setAddress(address);
         String wagonNumber = mapReceive.get("wagonNumber") == null ? null : mapReceive.get("wagonNumber").toString();//车号
         amShippingNew.setWagonNumber(wagonNumber);
         String remarks = mapReceive.get("remarks") == null ? null : mapReceive.get("remarks").toString();//备注
         amShippingNew.setErpRemark(remarks);

         /******保存头单据*******/
         amShippingService.save(amShippingNew);

         //明细
         List<Map<String, Object>> amShippingItems = (List<Map<String, Object>>) mapReceive.get("items");
         for (Map<String, Object> amShippingItem : amShippingItems) {
             AmShippingItem amShippingItemNew =new AmShippingItem();
             ShippingItem shippingItemExist;

             String vonderCode = amShippingItem.get("vonderCode") == null ? null : amShippingItem.get("vonderCode").toString();
             /*********来源行id：根据类型校验ID的必输，且更新到行上的发货单 行ID********/
             String itemSourceId = amShippingItem.get("itemSourceId") == null ? null : amShippingItem.get("itemSourceId").toString();
             filters.clear();
             filters.add(Filter.eq("id", Long.valueOf(itemSourceId)));
             List<ShippingItem> shippingItemList = shippingItemService.findList(null, filters, null);
             if (shippingItemList!=null&&shippingItemList.size() == 1) {
                 shippingItemExist = shippingItemList.get(0);
             } else {
                 throw new RuntimeException("E-产品【"+vonderCode+"】明细行来源单号不存在或存在多个");
             }

             /*********家哇云行ID：根据家哇云行ID判断是否有重复推送，重复推送则报错********/
             String jiawayunItemId = amShippingItem.get("jiawayunItemId") == null ? null : amShippingItem.get("jiawayunItemId").toString();
             if (jiawayunItemId == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】不能为空");
             }
             filters.clear();
             filters.add(Filter.eq("jiaWaYunItemId", Long.valueOf(jiawayunItemId)));
             List<AmShippingItem> amShippingItemLists = amShippingItemService.findList(null, filters, null);
             if (amShippingItemLists!=null&&amShippingItemLists.size() > 0) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】ID重复不能推");
             }else{
                 amShippingItemNew.setJiaWaYunItemId(Long.valueOf(jiawayunItemId));}

             /*********经营组织：校验是否有变更经营组织********/
             String organizationName = amShippingItem.get("organizationName") == null ? null : amShippingItem.get("organizationName").toString();
             if (organizationName == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织不能为空");
             }else if(!shippingSource.getOrganization().getName().equals(organizationName)){
                 throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织与来源单号不同");
             }

             /*********产品编码：校验是否有变更产品********/
             if (vonderCode == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品编码不能为空");
             }else if(!shippingItemExist.getProduct().getVonderCode().equals(vonderCode)){
                 throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品与来源单号不同");
             }

             /*********产品级别：校验是必输,词汇********/
             String productLevel = amShippingItem.get("productLevel") == null ? null : amShippingItem.get("productLevel").toString();
             if (productLevel == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的产品级别不能为空");
             }
             systemDictLists = findSystemDictListByCode(productLevel, "productLevel");
             if (systemDictLists.size() < 1) {
                 throw new RuntimeException("E-该产品级别不存在");
             } else {
                 amShippingItemNew.setProductLevel(systemDictLists.get(0));
             }


             //件数
             String boxQuantity = amShippingItem.get("boxQuantity") == null ? null : amShippingItem.get("boxQuantity").toString();
             if (boxQuantity == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能为空");
             } else if(new BigDecimal(boxQuantity).compareTo(shippingItemExist.getBoxQuantity()) == 1){
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能大于与原单据的数量");
             } else{
                 amShippingItemNew.setPlanBoxQuantity(shippingItemExist.getBoxQuantity());
                 amShippingItemNew.setBoxQuantity(new BigDecimal(boxQuantity));
             }

             //支数
             String branchQuantity = amShippingItem.get("branchQuantity") == null ? null : amShippingItem.get("branchQuantity").toString();
             if (branchQuantity == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能为空");
             } else if(new BigDecimal(branchQuantity).compareTo(shippingItemExist.getBranchQuantity()) == 1){
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能大于与原单据的数量");
             } else{
                 amShippingItemNew.setPlanBranchQuantity(shippingItemExist.getBranchQuantity());
                 amShippingItemNew.setBranchQuantity(new BigDecimal(branchQuantity));
             }

             //零散支数
             String scatteredQuantity = amShippingItem.get("scatteredQuantity") == null ? null : amShippingItem.get("scatteredQuantity").toString();
             if (scatteredQuantity == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的零散支数不能为空");
             } else {
                 amShippingItemNew.setPlanScatteredQuantity(shippingItemExist.getBranchQuantity().divideAndRemainder(shippingItemExist.getBranchPerBox())[1]);
                 amShippingItemNew.setScatteredQuantity(new BigDecimal(scatteredQuantity));
             }

             String quantity = amShippingItem.get("quantity") == null ? null : amShippingItem.get("quantity").toString();//数量
             if (quantity == null) {
                 LogUtils.error("E-产品行【"+vonderCode+"】的数量不能为空");
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能为空");
             } else if(new BigDecimal(quantity).compareTo(shippingItemExist.getQuantity()) == 1){
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能大于与原单据的数量");
             }else{
                 amShippingItemNew.setPlanQuantity(shippingItemExist.getQuantity());
                 amShippingItemNew.setQuantity(new BigDecimal(quantity));
             }

             /*********色号:校验是必输,词汇********/
             String colourNumber = amShippingItem.get("colourNumber") == null ? null : amShippingItem.get("colourNumber").toString();
             if (colourNumber == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的色号不能为空");
             }
             systemDictLists = findSystemDictListByCode(colourNumber, "colorNumber");
             if (systemDictLists.size() < 1) {
                 throw new RuntimeException("E-该产品级别不存在");
             } else {
                 amShippingItemNew.setColorNumbers(systemDictLists.get(0));
             }
             amShippingItemNew.setColourNumber(colourNumber);

             /*********含水率：校验是必输,词汇********/
             String moistureContent = amShippingItem.get("moistureContent") == null ? null : amShippingItem.get("moistureContent").toString();
             if (moistureContent == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的含水率不能为空");
             }
             systemDictLists = findSystemDictListByCode(moistureContent, "moistureContent");
             if (systemDictLists.size() < 1) {
                 throw new RuntimeException("E-该含水率不存在");
             } else {
                 amShippingItemNew.setMoistureContents(systemDictLists.get(0));
             }
             amShippingItemNew.setMoistureContent(colourNumber);

             //库位
             String warehouseLocation = amShippingItem.get("warehouseLocation") == null ? null : amShippingItem.get("warehouseLocation").toString();
             if(warehouseLocation !=null) {
                 //无则不管，有则更新
                 filters.clear();
                 filters.add(Filter.eq("companyInfoId", companyInfo));
                 filters.add(Filter.eq("code", warehouseLocation));
                 filters.add(Filter.eq("isEnabled", 1));
                 List<WarehouseLocation> warehouseLocationList = warehouseLocationService.findList(null, filters, null);
                 if (warehouseLocationList.size() == 1) {
                     amShippingItemNew.setWarehouseLocation(warehouseLocationList.get(0));
                 } else {
                     throw new RuntimeException("E-该库位不存在");
                 }
             }

             //批次
             String batch = amShippingItem.get("batch") == null ? null : amShippingItem.get("batch").toString();
             //判断原单据是否有批次,设置单据行批次信息
             if(batch !=null){
                 //无则不管，有则更新
                 filters.clear();
                 filters.add(Filter.eq("batchEncoding", batch));
                 List<WarehouseBatch> warehouseBatchList = warehouseBatchService.findList(null, filters, null);
                 if(warehouseBatchList==null||warehouseBatchList.size() < 1){
                     throw new RuntimeException("E-该批次不存在");
                 } else{
                     amShippingItemNew.setBatch(warehouseBatchList.get(0).getId().toString());
                     amShippingItemNew.setBatchEncoding(warehouseBatchList.get(0).getBatchEncoding());
                 }
             }

             //设置批次列表
             if(amShippingNew.getWarehouse().getEnableBatch()) {
                 ///判断批次是否有修改
                 if(amShippingItemNew.getWarehouseBatchItems().size()==1) {
                     if(!amShippingItemNew.getWarehouseBatchItems().get(0).getWarehouseBatch().getBatchEncoding().equals(batch)||batch==null) {
                         throw new RuntimeException("E-请不要修改【"+vonderCode+"】的批次");
                     }else {
                         ///更新批次数量
                         amShippingItemNew.getWarehouseBatchItems().get(0).setQuantity(new  BigDecimal(quantity));
                         amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBoxQuantity(new  BigDecimal(boxQuantity));
                         amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchQuantity(new  BigDecimal(branchQuantity));
                         amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchScattered(new  BigDecimal(scatteredQuantity));
                     }
                 }else {
                     throw new RuntimeException("E-批次列表没有此批次或者存在多个："+batch);
                 }
             }

             /*********新旧标识:校验是必输,词汇********/
             String newOldLogos = amShippingItem.get("newOldLogos") == null ? null : amShippingItem.get("newOldLogos").toString();
             if (newOldLogos == null) {
                 throw new RuntimeException("E-产品行【"+vonderCode+"】的新旧标识不能为空");
             }
             systemDictLists = findSystemDictListByCode(newOldLogos, "oldNewLogo");
             if (systemDictLists.size() < 1) {
                 throw new RuntimeException("E-该新旧标识不存在");
             } else {
                 amShippingItemNew.setNewOldLogos(systemDictLists.get(0));
             }
             amShippingItemNew.setNewOldLogo(newOldLogos);

             //行备注
             String itemRemarks = amShippingItem.get("remarks") == null ? null : amShippingItem.get("remarks").toString();//行备注
             amShippingItemNew.setMemo(itemRemarks);

             //将出入库单关联上来源单据
             amShippingItemNew.setShipping(shippingSource);

             /******保存行单据*******/
             amShippingItemNew.setAmShipping(amShippingNew);
             amShippingService.update(amShippingNew);
         }

         if(amShippingNew.getAmShippingItems().get(0).getShipping()!=null){
             //发货挑库
             saleShippingNatureService.checkWf(amShippingNew.getId());
         }

            return "S";
    }



    /**
     *  处理家哇云回传的退货单实发信息-生成相应的已审核出入库单
     * <AUTHOR>
     */

    @SuppressWarnings({ "unchecked", "null" })
    public String b2bReturnMessageBack(Map<String, Object> mapReceive) throws Exception {
        List<Filter> filters = new ArrayList<Filter>();
        Long companyInfo = 9L;
        AmShipping amShippingNew = new AmShipping();
        B2bReturns b2bReturnsSource;


        //校验来源退货单
        /*********来源单据id：推送唯一性校验********/
        String sourceId = mapReceive.get("sourceId") == null ? null : mapReceive.get("sourceId").toString();
        String sourceSn = mapReceive.get("sourceSn") == null ? null : mapReceive.get("sourceSn").toString();
        if (sourceId == null) {
            throw new RuntimeException("E-来源单号ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("id", Long.valueOf(sourceId)));
        filters.add(Filter.eq("sn", Long.valueOf(sourceSn)));
        List<B2bReturns> b2bReturnsList = b2bReturnsService.findList(null, filters, null);
        if (b2bReturnsList.size() == 1) {
            b2bReturnsSource = b2bReturnsList.get(0);
        } else {
            throw new RuntimeException("E-不存在此来源单");
        }


        amShippingNew.setSaleOrg(b2bReturnsSource.getSaleOrg());
        amShippingNew.setSbu(b2bReturnsSource.getSbu());

        //检验是否有已存在的家哇云单据生成了AM出入库单
        /*********家哇云单据ID：唯一校验，如果无则新增，有则报错********/
        String shippingId = mapReceive.get("jiawayunId") == null ? null : mapReceive.get("shippingId").toString();
        if (shippingId == null) {
            throw new RuntimeException("E-家哇云ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("jiaWaYunId", Long.valueOf(shippingId)));
        List<AmShipping> amShippings = amShippingService.findList(null, filters, null);
        if (amShippings.size() < 1) {
            amShippingNew.setJiaWaYunId(Long.valueOf(shippingId));
        }else {
            throw new RuntimeException("E-家哇云ID重复");
        }

        /*********类型：校验类型是否为空********/
        String type = mapReceive.get("type") == null ? null : mapReceive.get("type").toString();
        if (type == null) {
            throw new RuntimeException("E-单据类型为空：出仓或是入仓");
        }else if(!type.equals("出仓")){
            throw new RuntimeException("E-单据类型出错：退货单应为入仓");
        }
        List<SystemDict> systemDictLists = findSystemDictListByCode(type,"billType");
        if (systemDictLists.size() < 1) {
            throw new RuntimeException("E-该单据类型不存在");
        } else {
            amShippingNew.setBillType(systemDictLists.get(0));
        }

        /*********家哇云单据号：必输，不为空********/
        String shippingSn = mapReceive.get("shippingSn") == null ? null : mapReceive.get("shippingSn").toString();
        if (shippingSn == null) {
            throw new RuntimeException("E-家哇云单据号不能为空");
        } else {
            amShippingNew.setJiaWaYunSn(shippingSn);
        }


        /*********客户编码：查询并校验客户是否变更********/
        String outTradeNo = mapReceive.get("outTradeNo") == null ? null : mapReceive.get("outTradeNo").toString();
        if(b2bReturnsSource.getStore() != null) {
            if (!b2bReturnsSource.getStore().getOutTradeNo().equals(outTradeNo)){
                throw new RuntimeException("E-客户与来源单的不同");
            }
        }
        amShippingNew.setStore(b2bReturnsSource.getStore());

        /*********仓库：查询并校验仓库是否变更********/
        String erpWarehouseCode = mapReceive.get("erpWarehouseCode") == null ? null : mapReceive.get("erpWarehouseCode").toString();
        if (erpWarehouseCode != null) {
            if(!b2bReturnsSource.getWarehouse().getErp_warehouse_code().equals(erpWarehouseCode)){
                throw new RuntimeException("E-仓库与来源单的不同");
            }
        }else{
            throw new RuntimeException("E-仓库编码不能为空");
        }
        amShippingNew.setWarehouse(b2bReturnsSource.getWarehouse());


        /*********单据类别：校验是必输,词汇********/
        String billCategory = mapReceive.get("billCategory") == null ? null : mapReceive.get("billCategory").toString();
        if (billCategory == null) {
            throw new RuntimeException("E-单据类别不能为空");
        }else {
            systemDictLists = findSystemDictListByCode(billCategory, "billCategory");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该单据类别不存在");
            } else {
                amShippingNew.setBillCategory(systemDictLists.get(0));
            }
        }

        /*********设置创建人与审核人********/
        String checkMan = mapReceive.get("checkMan") == null ? null : mapReceive.get("checkMan").toString();
        if (checkMan == null) {
            throw new RuntimeException("E-审核人不能为空");
        }
        filters.clear();
        filters.add(Filter.eq("companyInfoId", companyInfo));
        filters.add(Filter.eq("username", checkMan));
        filters.add(Filter.eq("isEnabled", 1));
        List<StoreMember> storeMembers = storeMemberBaseService.findList(null, filters, null);
        if (storeMembers.size() == 1) {
            amShippingNew.setCheckStoreMember(storeMembers.get(0));
        } else {
            throw new RuntimeException("E-该审核人不存在");
        }

        /*********单据日期：校验是否在总账日期范围内********/
        String billDate = mapReceive.get("billDate") == null ? null : mapReceive.get("billDate").toString();
        if (billDate == null) {
            LogUtils.error("E-单据日期不能为空");
            throw new RuntimeException("E-单据日期不能为空");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
        Date date = simpleDateFormat.parse(billDate);
        Long[] organizationIds = new Long[b2bReturnsSource.getB2bReturnsItems().size()];
        List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, b2bReturnsSource.getSaleOrg().getId(),
                b2bReturnsSource.getSbu().getId(), organizationIds, date,null);
        if (mapList.isEmpty() || mapList.size() == 0) {
            ExceptionUtil.throwServiceException("E-该单据日期不包含在总账日期内，请填写合适的单据日期");
        }
        amShippingNew.setBillDate(date);

        String containerNumber = mapReceive.get("containerNumber") == null ? null : mapReceive.get("containerNumber").toString();//柜号
        amShippingNew.setContainerNumber(containerNumber);
        String consignee = mapReceive.get("consignee") == null ? null : mapReceive.get("consignee").toString();//收货人
        amShippingNew.setConsignee(consignee);
        String phone = mapReceive.get("phone") == null ? null : mapReceive.get("phone").toString();//收货人电话
        amShippingNew.setPhone(phone);
        String areaName = mapReceive.get("areaName") == null ? null : mapReceive.get("areaName").toString();//收货地区
        filters.clear();
        filters.add(Filter.eq("fullName", areaName));
        List<Area> areas = areaBaseService.findList(null, filters, null);
        amShippingNew.setArea(areas.get(0));
        String address = mapReceive.get("address") == null ? null : mapReceive.get("address").toString();//收货地址
        amShippingNew.setAddress(address);
        String wagonNumber = mapReceive.get("wagonNumber") == null ? null : mapReceive.get("wagonNumber").toString();//车号
        amShippingNew.setWagonNumber(wagonNumber);
        String remarks = mapReceive.get("remarks") == null ? null : mapReceive.get("remarks").toString();//备注
        amShippingNew.setErpRemark(remarks);

        /******保存头单据*******/
        amShippingService.save(amShippingNew);

        //明细
        List<Map<String, Object>> amShippingItems = (List<Map<String, Object>>) mapReceive.get("items");
        for (Map<String, Object> amShippingItem : amShippingItems) {
            AmShippingItem amShippingItemNew =new AmShippingItem();
            ShippingItem shippingItemExist;

            String vonderCode = amShippingItem.get("vonderCode") == null ? null : amShippingItem.get("vonderCode").toString();
            /*********来源行id：根据类型校验ID的必输，且更新到行上的发货单 行ID********/
            String itemSourceId = amShippingItem.get("itemSourceId") == null ? null : amShippingItem.get("itemSourceId").toString();
            filters.clear();
            filters.add(Filter.eq("id", Long.valueOf(itemSourceId)));
            List<ShippingItem> shippingItemList = shippingItemService.findList(null, filters, null);
            if (shippingItemList!=null&&shippingItemList.size() == 1) {
                shippingItemExist = shippingItemList.get(0);
            } else {
                throw new RuntimeException("E-产品【"+vonderCode+"】明细行来源单号不存在或存在多个");
            }

            /*********家哇云行ID：根据家哇云行ID判断是否有重复推送，重复推送则报错********/
            String jiawayunItemId = amShippingItem.get("jiawayunItemId") == null ? null : amShippingItem.get("jiawayunItemId").toString();
            if (jiawayunItemId == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】不能为空");
            }
            filters.clear();
            filters.add(Filter.eq("jiaWaYunItemId", Long.valueOf(jiawayunItemId)));
            List<AmShippingItem> amShippingItemLists = amShippingItemService.findList(null, filters, null);
            if (amShippingItemLists!=null&&amShippingItemLists.size() > 0) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】ID重复不能推");
            }else{
                amShippingItemNew.setJiaWaYunItemId(Long.valueOf(jiawayunItemId));}

            /*********经营组织：校验是否有变更经营组织********/
            String organizationName = amShippingItem.get("organizationName") == null ? null : amShippingItem.get("organizationName").toString();
            if (organizationName == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织不能为空");
            }else if(!b2bReturnsSource.getOrganization().getName().equals(organizationName)){
                throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织与来源单号不同");
            }

            /*********产品编码：校验是否有变更产品********/
            if (vonderCode == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品编码不能为空");
            }else if(!shippingItemExist.getProduct().getVonderCode().equals(vonderCode)){
                throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品与来源单号不同");
            }

            /*********产品级别：校验是必输,词汇********/
            String productLevel = amShippingItem.get("productLevel") == null ? null : amShippingItem.get("productLevel").toString();
            if (productLevel == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的产品级别不能为空");
            }
            systemDictLists = findSystemDictListByCode(productLevel, "productLevel");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该产品级别不存在");
            } else {
                amShippingItemNew.setProductLevel(systemDictLists.get(0));
            }


            //件数
            String boxQuantity = amShippingItem.get("boxQuantity") == null ? null : amShippingItem.get("boxQuantity").toString();
            if (boxQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能为空");
            } else if(new BigDecimal(boxQuantity).compareTo(shippingItemExist.getBoxQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能大于与原单据的数量");
            } else{
                amShippingItemNew.setPlanBoxQuantity(shippingItemExist.getBoxQuantity());
                amShippingItemNew.setBoxQuantity(new BigDecimal(boxQuantity));
            }

            //支数
            String branchQuantity = amShippingItem.get("branchQuantity") == null ? null : amShippingItem.get("branchQuantity").toString();
            if (branchQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能为空");
            } else if(new BigDecimal(branchQuantity).compareTo(shippingItemExist.getBranchQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能大于与原单据的数量");
            } else{
                amShippingItemNew.setPlanBranchQuantity(shippingItemExist.getBranchQuantity());
                amShippingItemNew.setBranchQuantity(new BigDecimal(branchQuantity));
            }

            //零散支数
            String scatteredQuantity = amShippingItem.get("scatteredQuantity") == null ? null : amShippingItem.get("scatteredQuantity").toString();
            if (scatteredQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的零散支数不能为空");
            } else {
                amShippingItemNew.setPlanScatteredQuantity(shippingItemExist.getBranchQuantity().divideAndRemainder(shippingItemExist.getBranchPerBox())[1]);
                amShippingItemNew.setScatteredQuantity(new BigDecimal(scatteredQuantity));
            }

            String quantity = amShippingItem.get("quantity") == null ? null : amShippingItem.get("quantity").toString();//数量
            if (quantity == null) {
                LogUtils.error("E-产品行【"+vonderCode+"】的数量不能为空");
                throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能为空");
            } else if(new BigDecimal(quantity).compareTo(shippingItemExist.getQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能大于与原单据的数量");
            }else{
                amShippingItemNew.setPlanQuantity(shippingItemExist.getQuantity());
                amShippingItemNew.setQuantity(new BigDecimal(quantity));
            }

            /*********色号:校验是必输,词汇********/
            String colourNumber = amShippingItem.get("colourNumber") == null ? null : amShippingItem.get("colourNumber").toString();
            if (colourNumber == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的色号不能为空");
            }
            systemDictLists = findSystemDictListByCode(colourNumber, "colorNumber");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该产品级别不存在");
            } else {
                amShippingItemNew.setColorNumbers(systemDictLists.get(0));
            }
            amShippingItemNew.setColourNumber(colourNumber);

            /*********含水率：校验是必输,词汇********/
            String moistureContent = amShippingItem.get("moistureContent") == null ? null : amShippingItem.get("moistureContent").toString();
            if (moistureContent == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的含水率不能为空");
            }
            systemDictLists = findSystemDictListByCode(moistureContent, "moistureContent");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该含水率不存在");
            } else {
                amShippingItemNew.setMoistureContents(systemDictLists.get(0));
            }
            amShippingItemNew.setMoistureContent(colourNumber);

            //库位
            String warehouseLocation = amShippingItem.get("warehouseLocation") == null ? null : amShippingItem.get("warehouseLocation").toString();
            if(warehouseLocation !=null) {
                //无则不管，有则更新
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfo));
                filters.add(Filter.eq("code", warehouseLocation));
                filters.add(Filter.eq("isEnabled", 1));
                List<WarehouseLocation> warehouseLocationList = warehouseLocationService.findList(null, filters, null);
                if (warehouseLocationList.size() == 1) {
                    amShippingItemNew.setWarehouseLocation(warehouseLocationList.get(0));
                } else {
                    throw new RuntimeException("E-该库位不存在");
                }
            }

            //批次
            String batch = amShippingItem.get("batch") == null ? null : amShippingItem.get("batch").toString();
            //判断原单据是否有批次,设置单据行批次信息
            if(batch !=null){
                //无则不管，有则更新
                filters.clear();
                filters.add(Filter.eq("batchEncoding", batch));
                List<WarehouseBatch> warehouseBatchList = warehouseBatchService.findList(null, filters, null);
                if(warehouseBatchList==null||warehouseBatchList.size() < 1){
                    throw new RuntimeException("E-该批次不存在");
                } else{
                    amShippingItemNew.setBatch(warehouseBatchList.get(0).getId().toString());
                    amShippingItemNew.setBatchEncoding(warehouseBatchList.get(0).getBatchEncoding());
                }
            }

            //设置批次列表
            if(amShippingNew.getWarehouse().getEnableBatch()) {
                ///判断批次是否有修改
                if(amShippingItemNew.getWarehouseBatchItems().size()==1) {
                    if(!amShippingItemNew.getWarehouseBatchItems().get(0).getWarehouseBatch().getBatchEncoding().equals(batch)||batch==null) {
                        throw new RuntimeException("E-请不要修改【"+vonderCode+"】的批次");
                    }else {
                        ///更新批次数量
                        amShippingItemNew.getWarehouseBatchItems().get(0).setQuantity(new  BigDecimal(quantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBoxQuantity(new  BigDecimal(boxQuantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchQuantity(new  BigDecimal(branchQuantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchScattered(new  BigDecimal(scatteredQuantity));
                    }
                }else {
                    throw new RuntimeException("E-批次列表没有此批次或者存在多个："+batch);
                }
            }

            /*********新旧标识:校验是必输,词汇********/
            String newOldLogos = amShippingItem.get("newOldLogos") == null ? null : amShippingItem.get("newOldLogos").toString();
            if (newOldLogos == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的新旧标识不能为空");
            }
            systemDictLists = findSystemDictListByCode(newOldLogos, "oldNewLogo");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该新旧标识不存在");
            } else {
                amShippingItemNew.setNewOldLogos(systemDictLists.get(0));
            }
            amShippingItemNew.setNewOldLogo(newOldLogos);

            //行备注
            String itemRemarks = amShippingItem.get("remarks") == null ? null : amShippingItem.get("remarks").toString();//行备注
            amShippingItemNew.setMemo(itemRemarks);

            //将出入库单关联上来源单据
            amShippingItemNew.setB2bReturns(b2bReturnsSource);

            /******保存行单据*******/
            amShippingItemNew.setAmShipping(amShippingNew);
            amShippingService.update(amShippingNew);
        }

        if(amShippingNew.getAmShippingItems().get(0).getB2bReturns()!=null) {
            //退货接收
            saleShippingNatureService.returnReceiveCheckWf(amShippingNew.getId());
        }

        return "S";
    }



    /**
     *  处理家哇云回传的移库单信息-生成相应的出入库单
     * <AUTHOR>
     */

    @SuppressWarnings({ "unchecked", "null" })
    public String moveMessageBack(Map<String, Object> mapReceive) throws Exception {
        List<Filter> filters = new ArrayList<Filter>();
        Long companyInfo = 9L;
        AmShipping amShippingNew = new AmShipping();
        MoveLibrary moveLibrarySource;


        //校验来源发货单
        /*********来源单据id：推送唯一性校验********/
        String sourceId = mapReceive.get("sourceId") == null ? null : mapReceive.get("sourceId").toString();
        String sourceSn = mapReceive.get("sourceSn") == null ? null : mapReceive.get("sourceSn").toString();
        if (sourceId == null) {
            throw new RuntimeException("E-来源单号ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("id", Long.valueOf(sourceId)));
        filters.add(Filter.eq("sn", Long.valueOf(sourceSn)));
        List<MoveLibrary> moveLibraryList = moveLibraryService.findList(null, filters, null);
        if (moveLibraryList.size() == 1) {
            moveLibrarySource = moveLibraryList.get(0);
        } else {
            throw new RuntimeException("E-不存在此来源单");
        }


        amShippingNew.setSaleOrg(moveLibrarySource.getSaleOrg());
        amShippingNew.setSbu(moveLibrarySource.getSbu());

        //检验是否有已存在的家哇云单据生成了AM出入库单
        /*********家哇云单据ID：唯一校验，如果无则新增，有则报错********/
        String shippingId = mapReceive.get("jiawayunId") == null ? null : mapReceive.get("shippingId").toString();
        if (shippingId == null) {
            throw new RuntimeException("E-家哇云ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("jiaWaYunId", Long.valueOf(shippingId)));
        List<AmShipping> amShippings = amShippingService.findList(null, filters, null);
        if (amShippings.size() < 1) {
            amShippingNew.setJiaWaYunId(Long.valueOf(shippingId));
        }else {
            throw new RuntimeException("E-家哇云ID重复");
        }

        /*********类型：校验类型是否为空********/
        String type = mapReceive.get("type") == null ? null : mapReceive.get("type").toString();
        if (type == null) {
            throw new RuntimeException("E-单据类型为空：出仓或是入仓");
        }else if(!type.equals("出仓")){
            throw new RuntimeException("E-单据类型出错：移库单应为出仓");
        }
        List<SystemDict> systemDictLists = findSystemDictListByCode(type,"billType");
        if (systemDictLists.size() < 1) {
            throw new RuntimeException("E-该单据类型不存在");
        } else {
            amShippingNew.setBillType(systemDictLists.get(0));
        }

        /*********家哇云单据号：必输，不为空********/
        String shippingSn = mapReceive.get("shippingSn") == null ? null : mapReceive.get("shippingSn").toString();
        if (shippingSn == null) {
            throw new RuntimeException("E-家哇云单据号不能为空");
        } else {
            amShippingNew.setJiaWaYunSn(shippingSn);
        }



        /*********仓库：查询并校验发出仓库是否变更********/
        String erpWarehouseCode = mapReceive.get("erpWarehouseCode") == null ? null : mapReceive.get("erpWarehouseCode").toString();
        if (erpWarehouseCode != null) {
            if(!moveLibrarySource.getIssueWarehouse().getErp_warehouse_code().equals(erpWarehouseCode)){
                throw new RuntimeException("E-发出仓仓库与来源单的不同");
            }
        }else{
            throw new RuntimeException("E-发出仓仓库编码不能为空");
        }
        amShippingNew.setWarehouse(moveLibrarySource.getIssueWarehouse());


        /*********单据类别：校验是必输,词汇********/
        String billCategory = mapReceive.get("billCategory") == null ? null : mapReceive.get("billCategory").toString();
        if (billCategory == null) {
            throw new RuntimeException("E-单据类别不能为空");
        }else {
            systemDictLists = findSystemDictListByCode(billCategory, "billCategory");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该单据类别不存在");
            } else {
                amShippingNew.setBillCategory(systemDictLists.get(0));
            }
        }

        /*********设置创建人与审核人********/
        String checkMan = mapReceive.get("checkMan") == null ? null : mapReceive.get("checkMan").toString();
        if (checkMan == null) {
            throw new RuntimeException("E-审核人不能为空");
        }
        filters.clear();
        filters.add(Filter.eq("companyInfoId", companyInfo));
        filters.add(Filter.eq("username", checkMan));
        filters.add(Filter.eq("isEnabled", 1));
        List<StoreMember> storeMembers = storeMemberBaseService.findList(null, filters, null);
        if (storeMembers.size() == 1) {
            amShippingNew.setCheckStoreMember(storeMembers.get(0));
        } else {
            throw new RuntimeException("E-该审核人不存在");
        }

        /*********单据日期：校验是否在总账日期范围内********/
        String billDate = mapReceive.get("billDate") == null ? null : mapReceive.get("billDate").toString();
        if (billDate == null) {
            LogUtils.error("E-单据日期不能为空");
            throw new RuntimeException("E-单据日期不能为空");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
        Date date = simpleDateFormat.parse(billDate);
        Long[] organizationIds = new Long[moveLibrarySource.getMoveLibraryItems().size()];
        List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, moveLibrarySource.getSaleOrg().getId(),
                moveLibrarySource.getSbu().getId(), organizationIds, date,null);
        if (mapList.isEmpty() || mapList.size() == 0) {
            ExceptionUtil.throwServiceException("E-该单据日期不包含在总账日期内，请填写合适的单据日期");
        }
        amShippingNew.setBillDate(date);

        String containerNumber = mapReceive.get("containerNumber") == null ? null : mapReceive.get("containerNumber").toString();//柜号
        amShippingNew.setContainerNumber(containerNumber);
        String consignee = mapReceive.get("consignee") == null ? null : mapReceive.get("consignee").toString();//收货人
        amShippingNew.setConsignee(consignee);
        String phone = mapReceive.get("phone") == null ? null : mapReceive.get("phone").toString();//收货人电话
        amShippingNew.setPhone(phone);
        String areaName = mapReceive.get("areaName") == null ? null : mapReceive.get("areaName").toString();//收货地区
        filters.clear();
        filters.add(Filter.eq("fullName", areaName));
        List<Area> areas = areaBaseService.findList(null, filters, null);
        amShippingNew.setArea(areas.get(0));
        String address = mapReceive.get("address") == null ? null : mapReceive.get("address").toString();//收货地址
        amShippingNew.setAddress(address);
        String wagonNumber = mapReceive.get("wagonNumber") == null ? null : mapReceive.get("wagonNumber").toString();//车号
        amShippingNew.setWagonNumber(wagonNumber);
        String remarks = mapReceive.get("remarks") == null ? null : mapReceive.get("remarks").toString();//备注
        amShippingNew.setErpRemark(remarks);

        /******保存头单据*******/
        amShippingService.save(amShippingNew);

        //明细
        List<Map<String, Object>> amShippingItems = (List<Map<String, Object>>) mapReceive.get("items");
        for (Map<String, Object> amShippingItem : amShippingItems) {
            AmShippingItem amShippingItemNew =new AmShippingItem();
            MoveLibraryItem moveLibraryItemExist;

            String vonderCode = amShippingItem.get("vonderCode") == null ? null : amShippingItem.get("vonderCode").toString();
            /*********来源行id：根据类型校验ID的必输，且更新到行上的发货单 行ID********/
            String itemSourceId = amShippingItem.get("itemSourceId") == null ? null : amShippingItem.get("itemSourceId").toString();
            filters.clear();
            filters.add(Filter.eq("id", Long.valueOf(itemSourceId)));
            List<MoveLibraryItem> moveLibraryItemList = moveLibraryItemService.findList(null, filters, null);
            if (moveLibraryItemList!=null&&moveLibraryItemList.size() == 1) {
                moveLibraryItemExist = moveLibraryItemList.get(0);
            } else {
                throw new RuntimeException("E-产品【"+vonderCode+"】明细行来源单号不存在或存在多个");
            }

            /*********家哇云行ID：根据家哇云行ID判断是否有重复推送，重复推送则报错********/
            String jiawayunItemId = amShippingItem.get("jiawayunItemId") == null ? null : amShippingItem.get("jiawayunItemId").toString();
            if (jiawayunItemId == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】不能为空");
            }
            filters.clear();
            filters.add(Filter.eq("jiaWaYunItemId", Long.valueOf(jiawayunItemId)));
            List<AmShippingItem> amShippingItemLists = amShippingItemService.findList(null, filters, null);
            if (amShippingItemLists!=null&&amShippingItemLists.size() > 0) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】ID重复不能推");
            }else{
                amShippingItemNew.setJiaWaYunItemId(Long.valueOf(jiawayunItemId));}

            /*********经营组织：校验是否有变更经营组织********/
            String organizationName = amShippingItem.get("organizationName") == null ? null : amShippingItem.get("organizationName").toString();
            if (organizationName == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织不能为空");
            }else if(!moveLibraryItemExist.getProductOrganization().getName().equals(organizationName)){
                throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织与来源单号不同");
            }

            /*********产品编码：校验是否有变更产品********/
            if (vonderCode == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品编码不能为空");
            }else if(!moveLibraryItemExist.getProduct().getVonderCode().equals(vonderCode)){
                throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品与来源单号不同");
            }

            /*********产品级别：校验是必输,词汇********/
            String productLevel = amShippingItem.get("productLevel") == null ? null : amShippingItem.get("productLevel").toString();
            if (productLevel == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的产品级别不能为空");
            }
            systemDictLists = findSystemDictListByCode(productLevel, "productLevel");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该产品级别不存在");
            } else {
                amShippingItemNew.setProductLevel(systemDictLists.get(0));
            }


            //件数
            String boxQuantity = amShippingItem.get("boxQuantity") == null ? null : amShippingItem.get("boxQuantity").toString();
            if (boxQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能为空");
            } else if(new BigDecimal(boxQuantity).compareTo(moveLibraryItemExist.getBoxQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能大于与原单据的数量");
            } else{
                amShippingItemNew.setPlanBoxQuantity(moveLibraryItemExist.getBoxQuantity());
                amShippingItemNew.setBoxQuantity(new BigDecimal(boxQuantity));
            }

            //支数
            String branchQuantity = amShippingItem.get("branchQuantity") == null ? null : amShippingItem.get("branchQuantity").toString();
            if (branchQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能为空");
            } else if(new BigDecimal(branchQuantity).compareTo(moveLibraryItemExist.getBranchQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能大于与原单据的数量");
            } else{
                amShippingItemNew.setPlanBranchQuantity(moveLibraryItemExist.getBranchQuantity());
                amShippingItemNew.setBranchQuantity(new BigDecimal(branchQuantity));
            }

            //零散支数
            String scatteredQuantity = amShippingItem.get("scatteredQuantity") == null ? null : amShippingItem.get("scatteredQuantity").toString();
            if (scatteredQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的零散支数不能为空");
            } else {
                amShippingItemNew.setPlanScatteredQuantity(moveLibraryItemExist.getBranchQuantity().divideAndRemainder(moveLibraryItemExist.getBranchPerBox())[1]);
                amShippingItemNew.setScatteredQuantity(new BigDecimal(scatteredQuantity));
            }

            String quantity = amShippingItem.get("quantity") == null ? null : amShippingItem.get("quantity").toString();//数量
            if (quantity == null) {
                LogUtils.error("E-产品行【"+vonderCode+"】的数量不能为空");
                throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能为空");
            } else if(new BigDecimal(quantity).compareTo(moveLibraryItemExist.getQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能大于与原单据的数量");
            }else{
                amShippingItemNew.setPlanQuantity(moveLibraryItemExist.getQuantity());
                amShippingItemNew.setQuantity(new BigDecimal(quantity));
            }

            /*********色号:校验是必输,词汇********/
            String colourNumber = amShippingItem.get("colourNumber") == null ? null : amShippingItem.get("colourNumber").toString();
            if (colourNumber == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的色号不能为空");
            }
            systemDictLists = findSystemDictListByCode(colourNumber, "colorNumber");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该产品级别不存在");
            } else {
                amShippingItemNew.setColorNumbers(systemDictLists.get(0));
            }
            amShippingItemNew.setColourNumber(colourNumber);

            /*********含水率：校验是必输,词汇********/
            String moistureContent = amShippingItem.get("moistureContent") == null ? null : amShippingItem.get("moistureContent").toString();
            if (moistureContent == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的含水率不能为空");
            }
            systemDictLists = findSystemDictListByCode(moistureContent, "moistureContent");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该含水率不存在");
            } else {
                amShippingItemNew.setMoistureContents(systemDictLists.get(0));
            }
            amShippingItemNew.setMoistureContent(colourNumber);

            //库位
            String warehouseLocation = amShippingItem.get("warehouseLocation") == null ? null : amShippingItem.get("warehouseLocation").toString();
            if(warehouseLocation !=null) {
                //无则不管，有则更新
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfo));
                filters.add(Filter.eq("code", warehouseLocation));
                filters.add(Filter.eq("isEnabled", 1));
                List<WarehouseLocation> warehouseLocationList = warehouseLocationService.findList(null, filters, null);
                if (warehouseLocationList.size() == 1) {
                    amShippingItemNew.setWarehouseLocation(warehouseLocationList.get(0));
                } else {
                    throw new RuntimeException("E-该库位不存在");
                }
            }

            //批次
            String batch = amShippingItem.get("batch") == null ? null : amShippingItem.get("batch").toString();
            //判断原单据是否有批次,设置单据行批次信息
            if(batch !=null){
                //无则不管，有则更新
                filters.clear();
                filters.add(Filter.eq("batchEncoding", batch));
                List<WarehouseBatch> warehouseBatchList = warehouseBatchService.findList(null, filters, null);
                if(warehouseBatchList==null||warehouseBatchList.size() < 1){
                    throw new RuntimeException("E-该批次不存在");
                } else{
                    amShippingItemNew.setBatch(warehouseBatchList.get(0).getId().toString());
                    amShippingItemNew.setBatchEncoding(warehouseBatchList.get(0).getBatchEncoding());
                }
            }

            //设置批次列表
            if(amShippingNew.getWarehouse().getEnableBatch()) {
                ///判断批次是否有修改
                if(amShippingItemNew.getWarehouseBatchItems().size()==1) {
                    if(!amShippingItemNew.getWarehouseBatchItems().get(0).getWarehouseBatch().getBatchEncoding().equals(batch)||batch==null) {
                        throw new RuntimeException("E-请不要修改【"+vonderCode+"】的批次");
                    }else {
                        ///更新批次数量
                        amShippingItemNew.getWarehouseBatchItems().get(0).setQuantity(new  BigDecimal(quantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBoxQuantity(new  BigDecimal(boxQuantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchQuantity(new  BigDecimal(branchQuantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchScattered(new  BigDecimal(scatteredQuantity));
                    }
                }else {
                    throw new RuntimeException("E-批次列表没有此批次或者存在多个："+batch);
                }
            }

            /*********新旧标识:校验是必输,词汇********/
            String newOldLogos = amShippingItem.get("newOldLogos") == null ? null : amShippingItem.get("newOldLogos").toString();
            if (newOldLogos == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的新旧标识不能为空");
            }
            systemDictLists = findSystemDictListByCode(newOldLogos, "oldNewLogo");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该新旧标识不存在");
            } else {
                amShippingItemNew.setNewOldLogos(systemDictLists.get(0));
            }
            amShippingItemNew.setNewOldLogo(newOldLogos);

            //行备注
            String itemRemarks = amShippingItem.get("remarks") == null ? null : amShippingItem.get("remarks").toString();//行备注
            amShippingItemNew.setMemo(itemRemarks);

            //将出入库单关联上来源单据
            amShippingItemNew.setMovelibraryIssue(moveLibrarySource);

            /******保存行单据*******/
            amShippingItemNew.setAmShipping(amShippingNew);
            amShippingService.update(amShippingNew);
        }

        if(amShippingNew.getAmShippingItems().get(0).getShipping()!=null){
            //发货挑库
            saleShippingNatureService.checkWf(amShippingNew.getId());
        }

        return "S";
    }



    /**
     *  处理家哇云回传的盘盈盘亏的出入库单信息-生成相应的出入库单
     * <AUTHOR>
     */

    @SuppressWarnings({ "unchecked", "null" })
    public String inventoryMessageBack(Map<String, Object> mapReceive) throws Exception {
        List<Filter> filters = new ArrayList<Filter>();
        Long companyInfo = 9L;
        AmShipping amShippingNew = new AmShipping();


        //检验是否有已存在的家哇云单据生成了AM出入库单
        /*********家哇云单据ID：唯一校验，如果无则新增，有则报错********/
        String shippingId = mapReceive.get("jiawayunId") == null ? null : mapReceive.get("shippingId").toString();
        if (shippingId == null) {
            throw new RuntimeException("E-家哇云ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("jiaWaYunId", Long.valueOf(shippingId)));
        List<AmShipping> amShippings = amShippingService.findList(null, filters, null);
        if (amShippings.size() < 1) {
            amShippingNew.setJiaWaYunId(Long.valueOf(shippingId));
        }else {
            throw new RuntimeException("E-家哇云ID重复");
        }

        /*********类型：校验类型是否为空********/
        String type = mapReceive.get("type") == null ? null : mapReceive.get("type").toString();
        if (type == null) {
            throw new RuntimeException("E-单据类型为空：出仓或是入仓");
        }
        List<SystemDict> systemDictLists = findSystemDictListByCode(type,"billType");
        if (systemDictLists.size() < 1) {
            throw new RuntimeException("E-该单据类型不存在");
        } else {
            amShippingNew.setBillType(systemDictLists.get(0));
        }

        /*********家哇云单据号：必输，不为空********/
        String shippingSn = mapReceive.get("shippingSn") == null ? null : mapReceive.get("shippingSn").toString();
        if (shippingSn == null) {
            throw new RuntimeException("E-家哇云单据号不能为空");
        } else {
            amShippingNew.setJiaWaYunSn(shippingSn);
        }

        /*********SBU：查询并校验********/
        String sbuString = mapReceive.get("sbu") == null ? null : mapReceive.get("sbu").toString();
        if (sbuString != null) {
            filters.clear();
            filters.add(Filter.eq("name", sbuString));
            List<Sbu> sbuList = sbuService.findList(null, filters, null);
            if (sbuList.size() < 1) {
                throw new RuntimeException("E-该SBU不存在");
            }
            amShippingNew.setSbu(sbuList.get(0));
        }else{
            throw new RuntimeException("E-SBU不能为空");
        }

        /*********仓库：查询并校验仓库是否变更********/
        String erpWarehouseCode = mapReceive.get("erpWarehouseCode") == null ? null : mapReceive.get("erpWarehouseCode").toString();
        if (erpWarehouseCode != null) {
            filters.clear();
            filters.add(Filter.eq("erpWarehouseCode", erpWarehouseCode));
            List<Warehouse> warehouseList = warehouseService.findList(null, filters, null);
            if (warehouseList.size() < 1) {
                throw new RuntimeException("E-该仓库不存在");
            }
            amShippingNew.setWarehouse(warehouseList.get(0));
        }else{
            throw new RuntimeException("E-仓库编码不能为空");
        }



        /*********单据类别：校验是必输,词汇********/
        String billCategory = mapReceive.get("billCategory") == null ? null : mapReceive.get("billCategory").toString();
        if (billCategory == null) {
            throw new RuntimeException("E-单据类别不能为空");
        }else {
            systemDictLists = findSystemDictListByCode(billCategory, "billCategory");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该单据类别不存在");
            } else {
                amShippingNew.setBillCategory(systemDictLists.get(0));
            }
        }

        /*********设置创建人与审核人********/
        String checkMan = mapReceive.get("checkMan") == null ? null : mapReceive.get("checkMan").toString();
        if (checkMan == null) {
            throw new RuntimeException("E-审核人不能为空");
        }
        filters.clear();
        filters.add(Filter.eq("companyInfoId", companyInfo));
        filters.add(Filter.eq("username", checkMan));
        filters.add(Filter.eq("isEnabled", 1));
        List<StoreMember> storeMembers = storeMemberBaseService.findList(null, filters, null);
        if (storeMembers.size() == 1) {
            amShippingNew.setCheckStoreMember(storeMembers.get(0));
        } else {
            throw new RuntimeException("E-该审核人不存在");
        }

        /*********单据日期：校验是否在总账日期范围内********/
        String billDate = mapReceive.get("billDate") == null ? null : mapReceive.get("billDate").toString();
        if (billDate == null) {
            LogUtils.error("E-单据日期不能为空");
            throw new RuntimeException("E-单据日期不能为空");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
        Date date = simpleDateFormat.parse(billDate);
        //Gl日期校验
        SystemDict totalDateType =systemDictService.findSystemDictList("totalDateType", "应收账期").get(0);
        List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, amShippingNew.getSaleOrg().getId(),
                amShippingNew.getSbu().getId(), new Long[]{amShippingNew.getOrganization().getId()}, amShippingNew.getBillDate(),totalDateType);
        if(mapList.isEmpty()){
            throw new RuntimeException("E-该单据日期不包含在总账日期内，请填写合适的单据日期");
        }
        amShippingNew.setBillDate(date);

        String containerNumber = mapReceive.get("containerNumber") == null ? null : mapReceive.get("containerNumber").toString();//柜号
        amShippingNew.setContainerNumber(containerNumber);
        String consignee = mapReceive.get("consignee") == null ? null : mapReceive.get("consignee").toString();//收货人
        amShippingNew.setConsignee(consignee);
        String phone = mapReceive.get("phone") == null ? null : mapReceive.get("phone").toString();//收货人电话
        amShippingNew.setPhone(phone);
        String areaName = mapReceive.get("areaName") == null ? null : mapReceive.get("areaName").toString();//收货地区
        filters.clear();
        filters.add(Filter.eq("fullName", areaName));
        List<Area> areas = areaBaseService.findList(null, filters, null);
        amShippingNew.setArea(areas.get(0));
        String address = mapReceive.get("address") == null ? null : mapReceive.get("address").toString();//收货地址
        amShippingNew.setAddress(address);
        String wagonNumber = mapReceive.get("wagonNumber") == null ? null : mapReceive.get("wagonNumber").toString();//车号
        amShippingNew.setWagonNumber(wagonNumber);
        String remarks = mapReceive.get("remarks") == null ? null : mapReceive.get("remarks").toString();//备注
        if(remarks == null){
            throw new RuntimeException("E-不可以为空，要表明盘盈盘亏");
        }
        amShippingNew.setErpRemark(remarks);

        /******保存头单据*******/
        amShippingService.save(amShippingNew);

        //明细
        List<Map<String, Object>> amShippingItems = (List<Map<String, Object>>) mapReceive.get("items");
        for (Map<String, Object> amShippingItem : amShippingItems) {
            AmShippingItem amShippingItemNew =new AmShippingItem();

            String vonderCode = amShippingItem.get("vonderCode") == null ? null : amShippingItem.get("vonderCode").toString();

            /*********家哇云行ID：根据家哇云行ID判断是否有重复推送，重复推送则报错********/
            String jiawayunItemId = amShippingItem.get("jiawayunItemId") == null ? null : amShippingItem.get("jiawayunItemId").toString();
            if (jiawayunItemId == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】不能为空");
            }
            filters.clear();
            filters.add(Filter.eq("jiaWaYunItemId", Long.valueOf(jiawayunItemId)));
            List<AmShippingItem> amShippingItemLists = amShippingItemService.findList(null, filters, null);
            if (amShippingItemLists!=null&&amShippingItemLists.size() > 0) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】家哇云ID重复不能推");
            }else{
                amShippingItemNew.setJiaWaYunItemId(Long.valueOf(jiawayunItemId));}

            /*********经营组织：校验是否有变更经营组织********/
            String organizationName = amShippingItem.get("organizationName") == null ? null : amShippingItem.get("organizationName").toString();
            if (organizationName == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织不能为空");
            }
            filters.clear();
            filters.add(Filter.eq("name", organizationName));
            List<Organization> organizationList = organizationService.findList(null, filters, null);
            if (organizationList!=null&&organizationList.size() > 0) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的经营组织不存在");
            }else{
                amShippingItemNew.setProductOrganization(organizationList.get(0));}

            /*********产品编码：校验是否有变更产品********/
            if (vonderCode == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品编码不能为空");
            }
            filters.clear();
            filters.add(Filter.eq("vonderCode", vonderCode));
            List<Product> productList = productService.findList(null, filters, null);
            if (productList!=null&&productList.size() > 0) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的产品编码不存在");
            }else{
                amShippingItemNew.setProduct(productList.get(0));}

            /*********产品级别：校验是必输,词汇********/
            String productLevel = amShippingItem.get("productLevel") == null ? null : amShippingItem.get("productLevel").toString();
            if (productLevel == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的产品级别不能为空");
            }
            systemDictLists = findSystemDictListByCode(productLevel, "productLevel");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该产品级别不存在");
            } else {
                amShippingItemNew.setProductLevel(systemDictLists.get(0));
            }


            //件数
            String boxQuantity = amShippingItem.get("boxQuantity") == null ? null : amShippingItem.get("boxQuantity").toString();
            if (boxQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能为空");
            } else{
                amShippingItemNew.setBoxQuantity(new BigDecimal(boxQuantity));
            }

            //支数
            String branchQuantity = amShippingItem.get("branchQuantity") == null ? null : amShippingItem.get("branchQuantity").toString();
            if (branchQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能为空");
            }  else{
                amShippingItemNew.setBranchQuantity(new BigDecimal(branchQuantity));
            }

            //零散支数
            String scatteredQuantity = amShippingItem.get("scatteredQuantity") == null ? null : amShippingItem.get("scatteredQuantity").toString();
            if (scatteredQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的零散支数不能为空");
            } else {
                 amShippingItemNew.setScatteredQuantity(new BigDecimal(scatteredQuantity));
            }

            String quantity = amShippingItem.get("quantity") == null ? null : amShippingItem.get("quantity").toString();//数量
            if (quantity == null) {
                LogUtils.error("E-产品行【"+vonderCode+"】的数量不能为空");
                throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能为空");
            } else{
                amShippingItemNew.setQuantity(new BigDecimal(quantity));
            }

            /*********色号:校验是必输,词汇********/
            String colourNumber = amShippingItem.get("colourNumber") == null ? null : amShippingItem.get("colourNumber").toString();
            if (colourNumber == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的色号不能为空");
            }
            systemDictLists = findSystemDictListByCode(colourNumber, "colorNumber");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该产品级别不存在");
            } else {
                amShippingItemNew.setColorNumbers(systemDictLists.get(0));
            }
            amShippingItemNew.setColourNumber(colourNumber);

            /*********含水率：校验是必输,词汇********/
            String moistureContent = amShippingItem.get("moistureContent") == null ? null : amShippingItem.get("moistureContent").toString();
            if (moistureContent == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的含水率不能为空");
            }
            systemDictLists = findSystemDictListByCode(moistureContent, "moistureContent");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该含水率不存在");
            } else {
                amShippingItemNew.setMoistureContents(systemDictLists.get(0));
            }
            amShippingItemNew.setMoistureContent(colourNumber);

            //库位
            String warehouseLocation = amShippingItem.get("warehouseLocation") == null ? null : amShippingItem.get("warehouseLocation").toString();
            if(amShippingNew.getWarehouse().getEnableLocation()){
                throw new RuntimeException("E-库位不能为空");
            }
            if(warehouseLocation !=null) {
                //无则不管，有则更新
                filters.clear();
                filters.add(Filter.eq("companyInfoId", companyInfo));
                filters.add(Filter.eq("code", warehouseLocation));
                filters.add(Filter.eq("isEnabled", 1));
                List<WarehouseLocation> warehouseLocationList = warehouseLocationService.findList(null, filters, null);
                if (warehouseLocationList.size() == 1) {
                    amShippingItemNew.setWarehouseLocation(warehouseLocationList.get(0));
                } else {
                    throw new RuntimeException("E-该库位不存在");
                }
            }

            //批次
            String batch = amShippingItem.get("batch") == null ? null : amShippingItem.get("batch").toString();
            if(amShippingNew.getWarehouse().getEnableBatch()){
                throw new RuntimeException("E-批次不能为空");
            }
            //判断原单据是否有批次,设置单据行批次信息
            if(batch !=null){
                //无则不管，有则更新
                filters.clear();
                filters.add(Filter.eq("batchEncoding", batch));
                List<WarehouseBatch> warehouseBatchList = warehouseBatchService.findList(null, filters, null);
                if(warehouseBatchList==null||warehouseBatchList.size() < 1){
                    throw new RuntimeException("E-该批次不存在");
                } else{
                    amShippingItemNew.setBatch(warehouseBatchList.get(0).getId().toString());
                    amShippingItemNew.setBatchEncoding(warehouseBatchList.get(0).getBatchEncoding());
                }
            }

            //设置批次列表
            if(amShippingNew.getWarehouse().getEnableBatch()) {
                ///判断批次是否有修改
                if(amShippingItemNew.getWarehouseBatchItems().size()==1) {
                    if(!amShippingItemNew.getWarehouseBatchItems().get(0).getWarehouseBatch().getBatchEncoding().equals(batch)||batch==null) {
                        throw new RuntimeException("E-请不要修改【"+vonderCode+"】的批次");
                    }else {
                        ///更新批次数量
                        amShippingItemNew.getWarehouseBatchItems().get(0).setQuantity(new  BigDecimal(quantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBoxQuantity(new  BigDecimal(boxQuantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchQuantity(new  BigDecimal(branchQuantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchScattered(new  BigDecimal(scatteredQuantity));
                    }
                }else {
                    throw new RuntimeException("E-批次列表没有此批次或者存在多个："+batch);
                }
            }

            /*********新旧标识:校验是必输,词汇********/
            String newOldLogos = amShippingItem.get("newOldLogos") == null ? null : amShippingItem.get("newOldLogos").toString();
            if (newOldLogos == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的新旧标识不能为空");
            }
            systemDictLists = findSystemDictListByCode(newOldLogos, "oldNewLogo");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该新旧标识不存在");
            } else {
                amShippingItemNew.setNewOldLogos(systemDictLists.get(0));
            }
            amShippingItemNew.setNewOldLogo(newOldLogos);

            //行备注
            String itemRemarks = amShippingItem.get("remarks") == null ? null : amShippingItem.get("remarks").toString();//行备注
            amShippingItemNew.setMemo(itemRemarks);


            /******保存行单据*******/
            amShippingItemNew.setAmShipping(amShippingNew);
            amShippingService.update(amShippingNew);
        }

        //处理库存
        saleShippingNatureService.shippingAudit(amShippingNew.getId());

        return "S";
    }

    /**
     *  处理家哇云回传的出入库单信息
     * <AUTHOR>
     */
    public String amshippingMessageBack(Map<String, Object> amShipping) throws Exception {


        List<Filter> filters = new ArrayList<Filter>();
        Long companyInfo = 9L;
         AmShipping amShippingNew;


        /*********来源单据id：推送唯一性校验********/
        String sourceId = amShipping.get("sourceId") == null ? null : amShipping.get("sourceId").toString();
        if (sourceId == null) {
            throw new RuntimeException("E-来源单号ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("id", Long.valueOf(sourceId)));
        List<AmShipping> amShippingOldList = amShippingService.findList(null, filters, null);
        if (amShippingOldList.size() == 1) {
            amShippingNew = amShippingOldList.get(0);
        } else {
            throw new RuntimeException("E-不存在此来源单");
        }

        /*********家哇云单据ID：唯一校验，如果无则新增，有则报错********/
        String shippingId = amShipping.get("shippingId") == null ? null : amShipping.get("shippingId").toString();
        if (shippingId == null) {
            throw new RuntimeException("E-家哇云ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("jiaWaYunId", Long.valueOf(shippingId)));
        List<AmShipping> amShippings = amShippingService.findList(null, filters, null);
        if (amShippings.size() < 1) {
            amShippingNew.setJiaWaYunId(Long.valueOf(shippingId));
        }else {
            throw new RuntimeException("E-家哇云ID重复");
        }

        /*********类型：校验类型是否为空********/
        String type = amShipping.get("type") == null ? null : amShipping.get("type").toString();
        if (type == null||!type.equals(amShippingNew.getBillType().getValue())) {
            throw new RuntimeException("E-请不修改类型");
        }

        /*********家哇云单据号：必输，不为空********/
        String shippingSn = amShipping.get("shippingSn") == null ? null : amShipping.get("shippingSn").toString();
        if (shippingSn == null) {
            throw new RuntimeException("E-家哇云单据号不能为空");
        } else {
            amShippingNew.setJiaWaYunSn(shippingSn);
        }


        /*********客户编码：查询并校验客户是否变更********/
//            String outTradeNo = amShipping.get("outTradeNo") == null ? null : amShipping.get("outTradeNo").toString();
//                 if(amShippingNew.getStore() != null) {
//                     if (!amShippingNew.getStore().getOutTradeNo().equals(outTradeNo)){
//                         throw new RuntimeException("E-客户与来源单的不同");
//                     }
//                 }

        /*********仓库：查询并校验仓库是否变更********/
        String erpWarehouseCode = amShipping.get("erpWarehouseCode") == null ? null : amShipping.get("erpWarehouseCode").toString();
        if (erpWarehouseCode != null) {
            if(!amShippingNew.getWarehouse().getErp_warehouse_code().equals(erpWarehouseCode)){
                throw new RuntimeException("E-仓库与来源单的不同");
            }
        }else{
            throw new RuntimeException("E-仓库编码不能为空");
        }

        /*********单据类型：校验是必输,词汇********/
        String billTypeName = amShipping.get("billTypeName") == null ? null : amShipping.get("billTypeName").toString();
        if (billTypeName == null) {
            throw new RuntimeException("E-单据类型不能为空");
        }
        List<SystemDict> systemDictLists = findSystemDictListByCode(billTypeName,"billType");
        if (systemDictLists.size() < 1) {
            throw new RuntimeException("E-该单据类型不存在");
        } else {
            amShippingNew.setBillType(systemDictLists.get(0));
        }

        /*********单据类别：校验是必输,词汇********/
        String billCategory = amShipping.get("billCategory") == null ? null : amShipping.get("billCategory").toString();
        if (billCategory == null||!billCategory.equals(amShippingNew.getBillCategory().getValue())) {
            throw new RuntimeException("E-请不要修改单据类别："+billCategory);
        }

        /*********设置创建人与审核人********/
        String checkMan = amShipping.get("checkMan") == null ? null : amShipping.get("checkMan").toString();
        if (checkMan == null) {
            throw new RuntimeException("E-审核人不能为空");
        }
        filters.clear();
        filters.add(Filter.eq("companyInfoId", companyInfo));
        filters.add(Filter.eq("username", checkMan));
        filters.add(Filter.eq("isEnabled", 1));
        List<StoreMember> storeMembers = storeMemberBaseService.findList(null, filters, null);
        if (storeMembers.size() == 1) {
            amShippingNew.setCheckStoreMember(storeMembers.get(0));
        } else {
            throw new RuntimeException("E-该审核人不存在");
        }

        /*********单据日期：校验是否在总账日期范围内********/
        String billDate = amShipping.get("billDate") == null ? null : amShipping.get("billDate").toString();
        if (billDate == null) {
            LogUtils.error("E-单据日期不能为空");
            throw new RuntimeException("E-单据日期不能为空");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
        Date date = simpleDateFormat.parse(billDate);
        Long[] organizationIds = new Long[amShippingNew.getAmShippingItems().size()];
        List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, amShippingNew.getSaleOrg().getId(),
                amShippingNew.getSbu().getId(), organizationIds, date,null);
        if (mapList.isEmpty() || mapList.size() == 0) {
            ExceptionUtil.throwServiceException("E-该单据日期不包含在总账日期内，请填写合适的单据日期");
        }
        amShippingNew.setBillDate(date);

        String containerNumber = amShipping.get("containerNumber") == null ? null : amShipping.get("containerNumber").toString();//柜号
        amShippingNew.setContainerNumber(containerNumber);
        String consignee = amShipping.get("consignee") == null ? null : amShipping.get("consignee").toString();//收货人
        amShippingNew.setConsignee(consignee);
        String phone = amShipping.get("phone") == null ? null : amShipping.get("phone").toString();//收货人电话
        amShippingNew.setPhone(phone);
        String areaName = amShipping.get("areaName") == null ? null : amShipping.get("areaName").toString();//收货地区
        filters.clear();
        filters.add(Filter.eq("fullName", areaName));
        List<Area> areas = areaBaseService.findList(null, filters, null);
        amShippingNew.setArea(areas.get(0));
        String address = amShipping.get("address") == null ? null : amShipping.get("address").toString();//收货地址
        amShippingNew.setAddress(address);
        String wagonNumber = amShipping.get("wagonNumber") == null ? null : amShipping.get("wagonNumber").toString();//车号
        amShippingNew.setWagonNumber(wagonNumber);
        String remarks = amShipping.get("remarks") == null ? null : amShipping.get("remarks").toString();//备注
        amShippingNew.setErpRemark(remarks);

        /******保存头单据*******/
        amShippingService.update(amShippingNew);

        //明细
        List<Map<String, Object>> amShippingItems = (List<Map<String, Object>>) amShipping.get("amShippingItems");
        for (Map<String, Object> amShippingItem : amShippingItems) {
            AmShippingItem amShippingItemNew;

            String vonderCode = amShippingItem.get("vonderCode") == null ? null : amShippingItem.get("vonderCode").toString();
            /*********来源行id：根据类型校验ID的必输，且更新到行上的发货单 行ID********/
            String itemSourceId = amShippingItem.get("itemSourceId") == null ? null : amShippingItem.get("itemSourceId").toString();
            filters.clear();
            filters.add(Filter.eq("id", Long.valueOf(itemSourceId)));
            List<AmShippingItem> amShippingItemOldList = amShippingItemService.findList(null, filters, null);
            if (amShippingItemOldList!=null&&amShippingItemOldList.size() == 1) {
                amShippingItemNew = amShippingItemOldList.get(0);
                amShippingItemNew.setJiaWaYunItemId(Long.valueOf(itemSourceId));
            } else {
                throw new RuntimeException("E-产品【"+vonderCode+"】来源单号不存在或存在多个");
            }

            /*********家哇云行ID：根据家哇云行ID判断是否有重复推送，重复推送则报错********/
            String shippingItemId = amShippingItem.get("shippingItemId") == null ? null : amShippingItem.get("shippingItemId").toString();
            if (shippingItemId == null) {
                throw new RuntimeException("E-家哇云行【"+shippingItemId+"】不能为空");
            }
            filters.clear();
            filters.add(Filter.eq("jiaWaYunItemId", Long.valueOf(shippingItemId)));
            List<AmShippingItem> amShippingItemLists = amShippingItemService.findList(null, filters, null);
            if (amShippingItemLists!=null&&amShippingItemLists.size() > 0) {
                throw new RuntimeException("E-家哇云行【"+shippingItemId+"】ID重复不能推");
            }else{
                amShippingItemNew.setJiaWaYunItemId(Long.valueOf(shippingItemId));}

            /*********经营组织：校验是否有变更经营组织********/
            String organizationName = amShippingItem.get("organizationName") == null ? null : amShippingItem.get("organizationName").toString();
            if (organizationName == null) {
                throw new RuntimeException("E-家哇云行【"+shippingItemId+"】经营组织不能为空");
            }else if(!amShippingItemNew.getProductOrganization().getName().equals(organizationName)){
                throw new RuntimeException("E-家哇云行【"+shippingItemId+"】经营组织与来源单号不同");
            }

            /*********产品编码：校验是否有变更产品********/
            if (vonderCode == null) {
                throw new RuntimeException("E-家哇云行【"+shippingItemId+"】 的产品编码不能为空");
            }else if(!amShippingItemNew.getProduct().getVonderCode().equals(vonderCode)){
                throw new RuntimeException("E-家哇云行【"+shippingItemId+"】 的产品与来源单号不同");
            }

            /*********产品级别：校验是必输,词汇********/
            String productLevel = amShippingItem.get("productLevel") == null ? null : amShippingItem.get("productLevel").toString();
            if (productLevel == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的产品级别不能为空");
            }
            if(!productLevel.equals(amShippingItemNew.getProductLevel().getValue())) {
                throw new RuntimeException("E-请不要修改该"+vonderCode+"产品级别");
            }

            //件数
            String boxQuantity = amShippingItem.get("boxQuantity") == null ? null : amShippingItem.get("boxQuantity").toString();
            if (boxQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能为空");
            } else if(new BigDecimal(boxQuantity).compareTo(amShippingItemNew.getBoxQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能大于与原单据的数量");
            } else{
                amShippingItemNew.setPlanBoxQuantity(amShippingItemNew.getBoxQuantity());
                amShippingItemNew.setBoxQuantity(new BigDecimal(boxQuantity));
            }

            //支数
            String branchQuantity = amShippingItem.get("branchQuantity") == null ? null : amShippingItem.get("branchQuantity").toString();
            if (branchQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能为空");
            } else if(new BigDecimal(branchQuantity).compareTo(amShippingItemNew.getBranchQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能大于与原单据的数量");
            } else{
                amShippingItemNew.setPlanBranchQuantity(amShippingItemNew.getBranchQuantity());
                amShippingItemNew.setBranchQuantity(new BigDecimal(branchQuantity));
            }

            //零散支数
            String scatteredQuantity = amShippingItem.get("scatteredQuantity") == null ? null : amShippingItem.get("scatteredQuantity").toString();
            if (scatteredQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的零散支数不能为空");
            } else {
                amShippingItemNew.setPlanScatteredQuantity(amShippingItemNew.getScatteredQuantity());
                amShippingItemNew.setScatteredQuantity(new BigDecimal(scatteredQuantity));
            }

            String quantity = amShippingItem.get("quantity") == null ? null : amShippingItem.get("quantity").toString();//数量
            if (quantity == null) {
                LogUtils.error("E-产品行【"+vonderCode+"】的数量不能为空");
                throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能为空");
            } else if(new BigDecimal(quantity).compareTo(amShippingItemNew.getQuantity()) == 1){
                throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能大于与原单据的数量");
            }else{
                amShippingItemNew.setPlanQuantity(amShippingItemNew.getQuantity());
                amShippingItemNew.setQuantity(new BigDecimal(quantity));
            }

            /*********色号:校验是必输,词汇********/
            String colourNumber = amShippingItem.get("colourNumber") == null ? null : amShippingItem.get("colourNumber").toString();
            if (colourNumber == null||!colourNumber.equals(amShippingItemNew.getColorNumbers().getValue())) {
                throw new RuntimeException("E-请不要修改【"+vonderCode+"】的色号");
            }

            /*********含水率：校验是必输,词汇********/
            String moistureContent = amShippingItem.get("moistureContent") == null ? null : amShippingItem.get("moistureContent").toString();
            if (moistureContent == null||!moistureContent.equals(amShippingItemNew.getMoistureContents().getValue())) {
                throw new RuntimeException("E-请不要修改【"+vonderCode+"】的含水率");
            }

            //库位
            String warehouseLocation = amShippingItem.get("warehouseLocation") == null ? null : amShippingItem.get("warehouseLocation").toString();
            if(amShippingItemNew.getWarehouseLocation()==null) {
                if(warehouseLocation!=null) {
                    throw new RuntimeException("E-请不要修改【"+vonderCode+"】的库位");
                }
            }else {
                if(warehouseLocation==null || !warehouseLocation.equals(amShippingItemNew.getWarehouseLocation().getCode())) {
                    throw new RuntimeException("E-请不要修改【"+vonderCode+"】的库位");
                }
            }

            //批次
            String batch = amShippingItem.get("batch") == null ? null : amShippingItem.get("batch").toString();
            //判断原单据是否有批次,设置单据行批次信息
            if(batch !=null){
                //无则不管，有则更新
                filters.clear();
                filters.add(Filter.eq("batchEncoding", batch));
                List<WarehouseBatch> warehouseBatchList = warehouseBatchService.findList(null, filters, null);
                if(warehouseBatchList==null||warehouseBatchList.size() < 1){
                    throw new RuntimeException("E-该批次不存在");
                } else{
                    amShippingItemNew.setBatch(warehouseBatchList.get(0).getId().toString());
                    amShippingItemNew.setBatchEncoding(warehouseBatchList.get(0).getBatchEncoding());
                }
            }

            //设置批次列表
            if(amShippingNew.getWarehouse().getEnableBatch()) {
                ///判断批次是否有修改
                if(amShippingItemNew.getWarehouseBatchItems().size()==1) {
                    if(!amShippingItemNew.getWarehouseBatchItems().get(0).getWarehouseBatch().getBatchEncoding().equals(batch)||batch==null) {
                        throw new RuntimeException("E-请不要修改【"+vonderCode+"】的批次");
                    }else {
                        ///更新批次数量
                        amShippingItemNew.getWarehouseBatchItems().get(0).setQuantity(new  BigDecimal(quantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBoxQuantity(new  BigDecimal(boxQuantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchQuantity(new  BigDecimal(branchQuantity));
                        amShippingItemNew.getWarehouseBatchItems().get(0).setShippedBranchScattered(new  BigDecimal(scatteredQuantity));
                    }
                }else {
                    throw new RuntimeException("E-批次列表没有此批次或者存在多个："+batch);
                }
            }

            /*********新旧标识:校验是必输,词汇********/
            String newOldLogos = amShippingItem.get("newOldLogos") == null ? null : amShippingItem.get("newOldLogos").toString();
            if (newOldLogos == null||!newOldLogos.equals(amShippingItemNew.getNewOldLogos().getValue())) {
                throw new RuntimeException("E-请不要修改【"+vonderCode+"】的新旧标识");
            }

            //行备注
            String itemRemarks = amShippingItem.get("remarks") == null ? null : amShippingItem.get("remarks").toString();//行备注
            amShippingItemNew.setMemo(itemRemarks);

            /******保存行单据*******/
            amShippingItemNew.setAmShipping(amShippingNew);
            amShippingItemService.update(amShippingItemNew);
        }

        //将没有发货的单据明细备份并清空
        cleanItem(amShippingNew);
        //根据单据类型处理不同的原始单据和处理金额
        if(amShippingNew.getAmShippingItems().get(0).getShipping()!=null){
            //发货挑库
            saleShippingNatureService.checkWf(amShippingNew.getId());
        }else if(amShippingNew.getAmShippingItems().get(0).getMovelibraryReceive()!=null){
            //移库接收
            saleShippingNatureService.checkWfMovelibraryReceive(amShippingNew.getId());
        }else if(amShippingNew.getAmShippingItems().get(0).getMovelibraryIssue()!=null){
            //移库发货
            saleShippingNatureService.checkWfMovelibraryIssue(amShippingNew.getId());
        }else if(amShippingNew.getAmShippingItems().get(0).getB2bReturns()!=null){
            //退货接收
            saleShippingNatureService.returnReceiveCheckWf(amShippingNew.getId());
        }else{
            saleShippingNatureService.shippingAudit(amShippingNew.getId());
        }
        return "S";
    }


    /**
     *  处理家哇云同步移库关闭信息-生成相应的移库关闭单
     * <AUTHOR>
     */
    public String moveCloseMessageBack(Map<String, Object> mapReceive){
        List<Filter> filters = new ArrayList<Filter>();
        Long companyInfo = 9L;
        MoveLibraryClose moveCloseNew = new MoveLibraryClose();
        MoveLibrary moveLibrarySource;
        //校验来源移库单
        /*********来源单据id：推送唯一性校验********/
        String sourceId = mapReceive.get("sourceId") == null ? null : mapReceive.get("sourceId").toString();
        String sourceSn = mapReceive.get("sourceSn") == null ? null : mapReceive.get("sourceSn").toString();
        if (sourceId == null) {
            throw new RuntimeException("E-来源单号ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("id", Long.valueOf(sourceId)));
        filters.add(Filter.eq("sn", Long.valueOf(sourceSn)));
        List<MoveLibrary> moveLibraryList = moveLibraryService.findList(null, filters, null);
        if (moveLibraryList.size() == 1) {
            moveLibrarySource = moveLibraryList.get(0);
        } else {
            throw new RuntimeException("E-不存在此来源单");
        }
        moveCloseNew.setMoveLibrary(moveLibrarySource);
        moveCloseNew.setCompanyInfoId(9L);
        //检验是否有已存在的家哇云单据生成了AM移库关闭单
        /*********家哇云单据ID：唯一校验，如果无则新增，有则报错********/
        String jiawayunId = mapReceive.get("jiawayunId") == null ? null : mapReceive.get("shippingId").toString();
        if (jiawayunId == null) {
            throw new RuntimeException("E-家哇云ID为空");
        }
        filters.clear();
        filters.add(Filter.eq("jiaWaYunId", Long.valueOf(jiawayunId)));
        List<AmShipping> amShippings = amShippingService.findList(null, filters, null);
        if (amShippings.size() < 1) {
            moveCloseNew.setJiaWaYunId(Long.valueOf(jiawayunId));
        }else {
            throw new RuntimeException("E-家哇云ID重复");
        }

        /*********家哇云单据号：必输，不为空********/
        String jiawayunSn = mapReceive.get("jiawayunSn") == null ? null : mapReceive.get("shippingSn").toString();
        if (jiawayunSn == null) {
            throw new RuntimeException("E-家哇云单据号不能为空");
        } else {
            moveCloseNew.setJiaWaYunSn(jiawayunSn);
        }



        /*********关闭日期：必输，不为空********/
        String closeDate = mapReceive.get("closeDate") == null ? null : mapReceive.get("shippingSn").toString();
        if (closeDate == null) {
            throw new RuntimeException("E-关闭日期不能为空");
        } else {
            moveCloseNew.setCloseDate(new Date(closeDate));
        }
        moveCloseNew.setSn(SnUtil.getMoveLibraryCloseSn());
        moveCloseNew.setStatus(0);

        //明细
        List<Map<String, Object>> moveLibraryCloseItems = (List<Map<String, Object>>) mapReceive.get("items");
        for (Map<String, Object> moveLibraryCloseItem : moveLibraryCloseItems) {
            MoveLibraryItem moveLibraryItemSource;
            MoveLibraryCloseItem moveLibraryCloseItemNew = new MoveLibraryCloseItem();

            String vonderCode = moveLibraryCloseItem.get("vonderCode") == null ? null : moveLibraryCloseItem.get("vonderCode").toString();
            /*********来源行id：根据类型校验ID的必输，且更新到行上的移库单 行ID********/
            String itemSourceId = moveLibraryCloseItem.get("itemSourceId") == null ? null : moveLibraryCloseItem.get("itemSourceId").toString();
            filters.clear();
            filters.add(Filter.eq("id", Long.valueOf(itemSourceId)));
            List<MoveLibraryItem> moveLibraryItemList = moveLibraryItemService.findList(null, filters, null);
            if (moveLibraryItemList!=null&&moveLibraryItemList.size() == 1) {
                moveLibraryItemSource = moveLibraryItemList.get(0);
            } else {
                throw new RuntimeException("E-产品【"+vonderCode+"】明细行来源单号不存在或存在多个");
            }
            moveLibraryCloseItemNew.setMoveLibraryItem(moveLibraryItemSource);

            /*********家哇云行ID：根据家哇云行ID判断是否有重复推送，重复推送则报错********/
            String jiawayunItemId = moveLibraryCloseItem.get("jiawayunItemId") == null ? null : moveLibraryCloseItem.get("jiawayunItemId").toString();
            if (jiawayunItemId == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】不能为空");
            }
            filters.clear();
            filters.add(Filter.eq("jiaWaYunItemId", Long.valueOf(jiawayunItemId)));
            List<MoveLibraryCloseItem> moveLibraryCloseItemList = moveLibraryCloseItemService.findList(null, filters, null);
            if (moveLibraryCloseItemList!=null&&moveLibraryCloseItemList.size() > 0) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】ID重复不能推");
            }else{
                moveLibraryCloseItemNew.setJiaWaYunItemId(Long.valueOf(jiawayunItemId));}

            /*********经营组织：校验是否有变更经营组织********/
            String organizationName = moveLibraryCloseItem.get("organizationName") == null ? null : moveLibraryCloseItem.get("organizationName").toString();
            if (organizationName == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织不能为空");
            }else if(!moveLibraryItemSource.getProductOrganization().getName().equals(organizationName)){
                throw new RuntimeException("E-产品行【"+vonderCode+"】经营组织与来源单号不同");
            }

            /*********产品编码：校验是否有变更产品********/
            if (vonderCode == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品编码不能为空");
            }else if(!moveLibraryItemSource.getProduct().getVonderCode().equals(vonderCode)){
                throw new RuntimeException("E-产品行【"+vonderCode+"】 的产品与来源单号不同");
            }

            /*********产品级别：校验是必输,词汇********/
            String productLevel = moveLibraryCloseItem.get("productLevel") == null ? null : moveLibraryCloseItem.get("productLevel").toString();
            if (productLevel == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的产品级别不能为空");
            }
            List<SystemDict> systemDictLists = findSystemDictListByCode(productLevel, "productLevel");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该产品级别不可以更改");
            }


            //件数
            String boxQuantity = moveLibraryCloseItem.get("boxQuantity") == null ? null : moveLibraryCloseItem.get("boxQuantity").toString();
            if (boxQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的件数不能为空");
            } else{
                moveLibraryCloseItemNew.setBoxQuantity(new BigDecimal(boxQuantity));
            }

            //支数
            String branchQuantity = moveLibraryCloseItem.get("branchQuantity") == null ? null : moveLibraryCloseItem.get("branchQuantity").toString();
            if (branchQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的支数不能为空");
            }  else{
                moveLibraryCloseItemNew.setBranchQuantity(new BigDecimal(branchQuantity));
            }

            //零散支数
            String scatteredQuantity = moveLibraryCloseItem.get("scatteredQuantity") == null ? null : moveLibraryCloseItem.get("scatteredQuantity").toString();
            if (scatteredQuantity == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的零散支数不能为空");
            } else {
                moveLibraryCloseItemNew.setScatteredQuantity(new BigDecimal(scatteredQuantity));
            }

            String quantity = moveLibraryCloseItem.get("quantity") == null ? null : moveLibraryCloseItem.get("quantity").toString();//数量
            if (quantity == null) {
                LogUtils.error("E-产品行【"+vonderCode+"】的数量不能为空");
                throw new RuntimeException("E-产品行【"+vonderCode+"】的数量不能为空");
            } else{
                moveLibraryCloseItemNew.setQuantity(new BigDecimal(quantity));
            }

            //关闭原因
            String closeReason = moveLibraryCloseItem.get("closeReason") == null ? null : moveLibraryCloseItem.get("productLevel").toString();
            if (closeReason == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的关闭原因不能为空");
            }
            systemDictLists = findSystemDictListByCode(closeReason, "closeReason");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该关闭原因不可用");
            }
            moveLibraryCloseItemNew.setCloseReason(systemDictLists.get(0));

            //物流公司
            String logisticsCompany = moveLibraryCloseItem.get("logisticsCompany") == null ? null : moveLibraryCloseItem.get("productLevel").toString();
            if (logisticsCompany == null) {
                throw new RuntimeException("E-产品行【"+vonderCode+"】的物流公司不能为空");
            }
            systemDictLists = findSystemDictListByCode(logisticsCompany, "logisticsType");
            if (systemDictLists.size() < 1) {
                throw new RuntimeException("E-该物流公司不存在AM列表中");
            }

            //来源移库单已发出数量

            //来源移库单已接收数量

            //来源移库单已关闭数量

            //备注
            String remarks = moveLibraryCloseItem.get("remarks") == null ? null : moveLibraryCloseItem.get("remarks").toString();
            moveLibraryCloseItemNew.setRemarks(remarks);

            /******保存行单据*******/
            moveLibraryCloseItemNew.setMoveLibraryClose(moveCloseNew);
            moveLibraryCloseService.update(moveCloseNew);
        }


        return "S";
    }



    /**
     * @Deseription 根据编码和名字查系统词汇
     * @param code
     * @param value
     * @return 词汇的集合
     */
    private  List<SystemDict> findSystemDictListByCode(String value,String code){
        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("value",value));
        filters.add(Filter.eq("code",code));
        filters.add(Filter.eq("isEnabled",true));
        List<SystemDict> systemDicts = systemDictBaseService.findList(null,
                filters,
                null);
        return systemDicts;
    }

    /**
     * @Deseription 将没有发货的单据明细备份并清空
     * @param amShipping
     */
    private void cleanItem(AmShipping amShipping){
        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("amShipping", amShipping.getId()));
        List<AmShippingItem> amShippingItems1 = amShippingItemService.findList(null, filters, null);
        for(AmShippingItem amit : amShippingItems1){
            if(amit.getPlanBoxQuantity() == null){
                //备份
                amit.setPlanBoxQuantity(amit.getBoxQuantity());
                amit.setPlanBranchQuantity(amit.getBranchQuantity());
                amit.setPlanScatteredQuantity(amit.getScatteredQuantity());
                amit.setPlanQuantity(amit.getQuantity());
                //清空
                amit.setBoxQuantity(new BigDecimal(0));
                amit.setBranchQuantity(new BigDecimal(0));
                amit.setScatteredQuantity(new BigDecimal(0));
                amit.setQuantity(new BigDecimal(0));
            }
            amShippingItemService.update(amit);
        }
    }





}
