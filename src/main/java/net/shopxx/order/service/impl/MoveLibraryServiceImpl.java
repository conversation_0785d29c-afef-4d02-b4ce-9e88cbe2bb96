package net.shopxx.order.service.impl;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;
import javax.annotation.Resource;
import javax.servlet.ServletContext;

import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.CancelRealTimePush;
import net.shopxx.intf.service.A1_MessageToHService;
import net.shopxx.stock.service.StockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.context.ServletContextAware;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.intf.NatureMoveLibraryCancle;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.entity.Adjustment;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.SaleOrgCreditService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.dao.MoveLibraryDao;
import net.shopxx.order.entity.MoveLibrary;
import net.shopxx.order.entity.MoveLibrary.MoveReceiveStatus;
import net.shopxx.order.entity.MoveLibrary.MoveStatus;
import net.shopxx.order.entity.MoveLibrary.MoveStatuss;
import net.shopxx.order.entity.MoveLibraryItem;
import net.shopxx.order.entity.MoveLibraryItem.MoveItemStatus;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.service.MoveLibraryItemService;
import net.shopxx.order.service.MoveLibraryService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.TriplicateFormService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseBillBatch;
import net.shopxx.stock.entity.WarehouseSaleOrg;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseBatchService;
import net.shopxx.stock.service.WarehouseSaleOrgBaseService;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.CommonVariable;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;

@Service("moveLibraryServiceImpl")
public class MoveLibraryServiceImpl extends ActWfBillServiceImpl<MoveLibrary>
		implements MoveLibraryService, ServletContextAware {

	private static Logger logger = LoggerFactory.getLogger(MoveLibraryServiceImpl.class);

	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "lockDataDao")
	private LockDataDao lockDataDao;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "moveLibraryDao")
	private MoveLibraryDao moveLibraryDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "triplicateFormServiceImpl")
	private TriplicateFormService triplicateFormService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "moveLibraryItemServiceImpl")
	private MoveLibraryItemService moveLibraryItemService;
	@Resource(name = "saleOrgCreditServiceImpl")
	private SaleOrgCreditService saleOrgCreditService;
	@Resource(name = "warehouseSaleOrgBaseServiceImpl")
	private WarehouseSaleOrgBaseService warehouseSaleOrgBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "stockServiceImpl")
	private StockService stockService;
	@Resource(name = "warehouseBatchServiceImpl")
	private WarehouseBatchService warehouseBatchService;
	
	
	/** servletContext */
	private ServletContext servletContext;

	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}

	@Override
	@Transactional
	public void saveMoveLibrary(MoveLibrary moveLibrary, Long sbuId,
			Long issueWarehouseId, Long receiveWarehouseId,Long saleOrgId) {

		//来源仓
		Warehouse issueWarehouse = warehouseBaseService.find(issueWarehouseId);

		//目标仓
		Warehouse receiveWarehouse = warehouseBaseService.find(receiveWarehouseId);

		Sbu sbu = sbuService.find(sbuId);

		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		//系统单据类型
		filters.clear();
		filters.add(Filter.eq("remark", CommonVariable.MOVE_TYPE));
		SystemDict systemType = systemDictService.systemBillType(filters);
		if (moveLibrary.getNeedDate() == null) {
			ExceptionUtil.throwServiceException("请填写需求时间");
		}

		moveLibrary.setIssueWarehouse(issueWarehouse);
		moveLibrary.setReceiveWarehouse(receiveWarehouse);
		moveLibrary.setSbu(sbu);
		moveLibrary.setSn(SnUtil.getMoveLibrarySn());
		moveLibrary.setStoreMember(storeMember);
		//移库状态
		moveLibrary.setMoveStatus(MoveStatus.saved);
		//移库发货状态
		moveLibrary.setMoveStatuss(MoveStatuss.nomove);
		//移库接收状态
		moveLibrary.setMoveReceiveStatus(MoveReceiveStatus.noreceive);
		moveLibrary.setSaleOrg(saleOrgService.find(saleOrgId));
		int useLockStock = Integer.parseInt(SystemConfig.getConfig("useLockStock",
				WebUtils.getCurrentCompanyInfoId()));
		List<MoveLibraryItem> moveLibraryItems = moveLibrary.getMoveLibraryItems();
		if (useLockStock == 1) {
			// 锁库存
			String productIds = "";
			for (MoveLibraryItem moveLibraryItem : moveLibraryItems) {
				Product product = moveLibraryItem.getProduct();
				if (product != null) {
					productIds += "," + product.getId();
				}
			}
			if (!ConvertUtil.isEmpty(productIds)) {
				productIds = productIds.substring(1);
				lockDataDao.lockStockByProduct(productIds);
			}
		}
		for (Iterator<MoveLibraryItem> iterator = moveLibraryItems.iterator(); iterator.hasNext();) {
			MoveLibraryItem moveLibraryItem = iterator.next();
			if (moveLibraryItem.getProduct() == null) {
				iterator.remove();
				continue;
			}

			Product product = productBaseService.find(moveLibraryItem.getProduct().getId());
			moveLibraryItem.setProduct(product);
			moveLibraryItem.setMoveLibrary(moveLibrary);
			// 校验库存现有数量
			BigDecimal stockQuantity = stockService.getStockQuantity(issueWarehouse.getId(),
					moveLibraryItem.getProductOrganization().getId(),
					moveLibraryItem.getProduct().getId(), null, null, null, null, null, null, null, 0);
			if (!ConvertUtil.isEmpty(stockQuantity)) {
				if (stockQuantity.compareTo(BigDecimal.ZERO) != 1) {
					ExceptionUtil.throwServiceException("产品编码为【"+moveLibraryItem.getProduct().getVonderCode()+"】没有库存");
				}
			} else {
				ExceptionUtil.throwServiceException("产品编码为【"+moveLibraryItem.getProduct().getVonderCode()+"】没有库存");
			}
			moveLibraryItem.setMoveItemStatus(MoveItemStatus.nomove);
			//初始化数量参数
			this.checkParamIsNotNull(moveLibraryItem);
			//色号
			if(ConvertUtil.isEmpty(moveLibraryItem.getColourNumbers()) ||
					(!ConvertUtil.isEmpty(moveLibraryItem.getColourNumbers()) && 
							ConvertUtil.isEmpty(moveLibraryItem.getColourNumbers().getId()))){
				moveLibraryItem.setColourNumbers(null);
			}
			//含水率
			if(ConvertUtil.isEmpty(moveLibraryItem.getMoistureContents()) ||
					(!ConvertUtil.isEmpty(moveLibraryItem.getMoistureContents()) && 
							ConvertUtil.isEmpty(moveLibraryItem.getMoistureContents().getId()))){
				moveLibraryItem.setMoistureContents(null);
			}
			//批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if(!ConvertUtil.isEmpty(moveLibraryItem.getBatch())){
				String[] batchs = moveLibraryItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if(!ConvertUtil.isEmpty(batchs[i])){
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						//订单类型
						warehouseBillBatch.setSysType(systemType);
						//移库
						warehouseBillBatch.setMoveLibrary(moveLibrary);
						//移库明细
						warehouseBillBatch.setMoveLibraryItem(moveLibraryItem);
						//批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			//新旧标识
			if(ConvertUtil.isEmpty(moveLibraryItem.getNewOldLogos()) ||
					(!ConvertUtil.isEmpty(moveLibraryItem.getNewOldLogos()) && 
							ConvertUtil.isEmpty(moveLibraryItem.getNewOldLogos().getId()))){
				moveLibraryItem.setNewOldLogos(null);
			}
			moveLibraryItem.setWarehouseBillBatchs(warehouseBillBatchList);
		}
		moveLibrary.setMoveLibraryItems(moveLibraryItems);
		save(moveLibrary);
	}

	@Override
	@Transactional
	public void updateMoveLibrary(MoveLibrary moveLibrary, Long sbuId,
			Long issueWarehouseId, Long receiveWarehouseId,Long saleOrgId) {
		//来源仓
		Warehouse issueWarehouse = warehouseBaseService.find(issueWarehouseId);
		//目标仓
		Warehouse receiveWarehouse = warehouseBaseService.find(receiveWarehouseId);
		List<Filter> filters = new ArrayList<Filter>();
		//系统单据类型
		filters.clear();
		filters.add(Filter.eq("remark", CommonVariable.MOVE_TYPE));
		SystemDict systemType = systemDictService.systemBillType(filters);
		Sbu sbu = sbuService.find(sbuId);
		if (moveLibrary.getNeedDate() == null) {
			ExceptionUtil.throwServiceException("请填写需求时间");
		}
		moveLibrary.setIssueWarehouse(issueWarehouse);
		moveLibrary.setReceiveWarehouse(receiveWarehouse);
		moveLibrary.setSbu(sbu);
		//移库状态
		moveLibrary.setMoveStatus(MoveStatus.saved);
		//移库发货状态
		moveLibrary.setMoveStatuss(MoveStatuss.nomove);
		//移库接收状态
		moveLibrary.setMoveReceiveStatus(MoveReceiveStatus.noreceive);
		moveLibrary.setSaleOrg(saleOrgService.find(saleOrgId));
		int useLockStock = Integer.parseInt(SystemConfig.getConfig("useLockStock",WebUtils.getCurrentCompanyInfoId()));
		List<MoveLibraryItem> moveLibraryItems = moveLibrary.getMoveLibraryItems();
		if (useLockStock == 1) {
			// 锁库存
			String productIds = "";
			for (MoveLibraryItem moveLibraryItem : moveLibraryItems) {
				Product product = moveLibraryItem.getProduct();
				if (product != null) {
					productIds += "," + product.getId();
				}
			}
			if (!ConvertUtil.isEmpty(productIds)) {
				productIds = productIds.substring(1);
				lockDataDao.lockStockByProduct(productIds);
			}
		}
		for (Iterator<MoveLibraryItem> iterator = moveLibraryItems.iterator(); iterator.hasNext();) {
			MoveLibraryItem moveLibraryItem = iterator.next();

			if (moveLibraryItem.getProduct() == null) {
				iterator.remove();
				continue;
			}
			//初始化移库数量
			this.checkParamIsNotNull(moveLibraryItem);
			//单价
			if(ConvertUtil.isEmpty(moveLibraryItem.getPrice())){
				moveLibraryItem.setPrice(BigDecimal.ZERO);
			}
			//金额
			if(ConvertUtil.isEmpty(moveLibraryItem.getAmount())){
				moveLibraryItem.setAmount(BigDecimal.ZERO);
			}
			Product product = productBaseService.find(moveLibraryItem.getProduct().getId());
			moveLibraryItem.setProduct(product);
			moveLibraryItem.setMoveLibrary(moveLibrary);
			// 校验库存现有数量
			BigDecimal stockQuantity = stockService.getStockQuantity(issueWarehouse.getId(),
					moveLibraryItem.getProductOrganization().getId(),
					moveLibraryItem.getProduct().getId(), null, null, null, null, null, null, null, 0);
			if (!ConvertUtil.isEmpty(stockQuantity)) {
				if (stockQuantity.compareTo(BigDecimal.ZERO) != 1) {
					ExceptionUtil.throwServiceException("产品编码为【"+moveLibraryItem.getProduct().getVonderCode()+"】没有库存");
				}
			} else {
				ExceptionUtil.throwServiceException("产品编码为【"+moveLibraryItem.getProduct().getVonderCode()+"】没有库存");
			}
			moveLibraryItem.setMoveItemStatus(MoveItemStatus.nomove);
			//产品经营组织
			if(ConvertUtil.isEmpty(moveLibraryItem.getProductOrganization())||
					(!ConvertUtil.isEmpty(moveLibraryItem.getProductOrganization())&&
							ConvertUtil.isEmpty(moveLibraryItem.getProductOrganization().getId()))){
				moveLibraryItem.setProductOrganization(null);
			}
			//色号
			if(ConvertUtil.isEmpty(moveLibraryItem.getColourNumbers()) ||
					(!ConvertUtil.isEmpty(moveLibraryItem.getColourNumbers()) && 
							ConvertUtil.isEmpty(moveLibraryItem.getColourNumbers().getId()))){
				moveLibraryItem.setColourNumbers(null);
			}
			//含水率
			if(ConvertUtil.isEmpty(moveLibraryItem.getMoistureContents()) ||
					(!ConvertUtil.isEmpty(moveLibraryItem.getMoistureContents()) && 
							ConvertUtil.isEmpty(moveLibraryItem.getMoistureContents().getId()))){
				moveLibraryItem.setMoistureContents(null);
			}
			//批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if(!ConvertUtil.isEmpty(moveLibraryItem.getBatch())){
				String[] batchs = moveLibraryItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if(!ConvertUtil.isEmpty(batchs[i])){
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						//订单类型
						warehouseBillBatch.setSysType(systemType);
						//移库
						warehouseBillBatch.setMoveLibrary(moveLibrary);
						//移库明细
						warehouseBillBatch.setMoveLibraryItem(moveLibraryItem);
						//批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			moveLibraryItem.setWarehouseBillBatchs(warehouseBillBatchList);
			//新旧标识
			if(ConvertUtil.isEmpty(moveLibraryItem.getNewOldLogos()) ||
					(!ConvertUtil.isEmpty(moveLibraryItem.getNewOldLogos()) && 
							ConvertUtil.isEmpty(moveLibraryItem.getNewOldLogos().getId()))){
				moveLibraryItem.setNewOldLogos(null);
			}
		}
		moveLibrary.setMoveLibraryItems(moveLibraryItems);
		
		update(moveLibrary, "sn", "storeMember");

	}

	@Override
	public List<Map<String, Object>> findMoveLibraryItem(Long id) {
		return moveLibraryDao.findMoveLibraryItem(id);
	}

	@Transactional(readOnly = true)
	@Override
	public Page<Map<String, Object>> newFindPage(String sn,Long issueWarehouseId,Long receiveWarehouseId,
			Integer[] status,String storeMemberName,Long[] productId,String firstTime,String lastTime,
			Long[] moveSttuss,Long[] moveReceiveStatus,String firstIssueDate,String lastIssueDate,
			String firstReceiveDate,String lastReceiveDate,Long[] wfStates,Pageable pageable) {
		
		return moveLibraryDao.newFindPage(sn,issueWarehouseId,receiveWarehouseId,status,storeMemberName,
				productId,firstTime,lastTime,moveSttuss,moveReceiveStatus,firstIssueDate,lastIssueDate,
				firstReceiveDate,lastReceiveDate,wfStates,pageable);
	}

	@Override
	public List<Map<String, Object>> findItemListById(String ids,Boolean isDefault) {
		return moveLibraryDao.findItemListById(ids,isDefault);
	}
	
	@Override
	@Transactional
	public void checkMoveLibraryWf(Long id, String modelId, Long objTypeId) {
		
		MoveLibrary moveLibrary = find(id);
		if(ConvertUtil.isEmpty(moveLibrary)){
			ExceptionUtil.throwServiceException("该移库单不存在");
		}
		if(!ConvertUtil.isEmpty(moveLibrary.getMoveStatus()) && !moveLibrary.getMoveStatus().equals(MoveStatus.saved)){
			ExceptionUtil.throwServiceException("只有单据状态为已保存的移库单才能审核");
		}
		
		if (moveLibrary.getWfId() != null) {
			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
		}
		
		if(ConvertUtil.isEmpty(moveLibrary.getReceiveWarehouse())||
				(!ConvertUtil.isEmpty(moveLibrary.getReceiveWarehouse())&&
						ConvertUtil.isEmpty(moveLibrary.getReceiveWarehouse().getId()))){
			ExceptionUtil.throwServiceException("目标仓不能为空");
		}
		if(ConvertUtil.isEmpty(moveLibrary.getSaleOrg())||
				(!ConvertUtil.isEmpty(moveLibrary.getSaleOrg())&&
						ConvertUtil.isEmpty(moveLibrary.getSaleOrg().getId()))){
			ExceptionUtil.throwServiceException("机构不能为空");
		}
		
		
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("warehouse", moveLibrary.getReceiveWarehouse().getId()));
		filters.add(Filter.eq("saleOrg", moveLibrary.getSaleOrg().getId()));
		WarehouseSaleOrg warehouseSaleOrg = warehouseSaleOrgBaseService.find(filters);	
		
		if(!ConvertUtil.isEmpty(warehouseSaleOrg)&&
				(!ConvertUtil.isEmpty(warehouseSaleOrg.getIsCheck())&&
						warehouseSaleOrg.getIsCheck())){
			List<Map<String, Object>> vSaleOrgCreditList = saleOrgCreditService.
					findVSaleOrgCreditList(moveLibrary.getSaleOrg().getId());
			if(!vSaleOrgCreditList.isEmpty()&&vSaleOrgCreditList.size()>0){
				for (Map<String, Object> map : vSaleOrgCreditList) {	
					//机构
					Long soId = null;
					if(!ConvertUtil.isEmpty(map.get("so_id"))){
						soId = Long.valueOf(map.get("so_id").toString());
					}
					//sbu
					Long sbuId = null;
					if(!ConvertUtil.isEmpty(map.get("sbu_id"))){
						sbuId = Long.valueOf(map.get("sbu_id").toString());
					}
					//经营组织
					Long orgId = null;
					if(!ConvertUtil.isEmpty(map.get("org_id"))){
						orgId = Long.valueOf(map.get("org_id").toString());
					}
					//平台移库类型
					String typeName = null;
					if(!ConvertUtil.isEmpty(map.get("type_name"))){
						typeName = map.get("type_name").toString();
					}
					//数量
					BigDecimal amount = BigDecimal.ZERO;
					if (!ConvertUtil.isEmpty(map.get("amount"))) {
						amount = new BigDecimal(map.get("amount").toString());
					}
					//移库金额
					BigDecimal vmTotalAmount = BigDecimal.ZERO;
					//移库数量
					BigDecimal vmTotalQuantity = BigDecimal.ZERO;
					List<Map<String, Object>> VMoveLibraryList = moveLibraryDao.
							findTotalVMoveLibraryAmount(moveLibrary.getId(),soId,sbuId,orgId);
					if(!VMoveLibraryList.isEmpty() && VMoveLibraryList.size()>0){
						Map<String, Object> vMovelibraryCheck = VMoveLibraryList.get(0);
						if (!ConvertUtil.isEmpty(vMovelibraryCheck.get("totalAmount"))) {
							vmTotalAmount = new BigDecimal(vMovelibraryCheck.get("totalAmount").toString());
						}
						if (!ConvertUtil.isEmpty(vMovelibraryCheck.get("totalQuantity"))) {
							vmTotalQuantity = new BigDecimal(vMovelibraryCheck.get("totalQuantity").toString());
						}
					}
					List<Map<String, Object>> vSaleOrgCreditCheckList = saleOrgCreditService.
							findVSaleOrgCreditCheckList(soId,sbuId,orgId);
					if(!vSaleOrgCreditCheckList.isEmpty()&&vSaleOrgCreditCheckList.size()>0){
						Map<String, Object> mapCheck = vSaleOrgCreditCheckList.get(0);							
						//审核金额
						BigDecimal checkTotalAmount = BigDecimal.ZERO;
						if (!ConvertUtil.isEmpty(mapCheck.get("totalAmount"))) {
							checkTotalAmount = new BigDecimal(mapCheck.get("totalAmount").toString());
						}
						//审核数量
						BigDecimal checkTotalQuantity = BigDecimal.ZERO;
						if (!ConvertUtil.isEmpty(mapCheck.get("totalQuantity"))) {
							checkTotalQuantity = new BigDecimal(mapCheck.get("totalQuantity").toString());
						}
						if(!ConvertUtil.isEmpty(typeName)&&typeName.equals("平台移库金额")){
					          vmTotalAmount = vmTotalAmount.add(checkTotalAmount);
					          if(vmTotalAmount.compareTo(amount)==1){
					        	  ExceptionUtil.throwServiceException("平台移库金额大于平台额度");
					          }
				        }else if(!ConvertUtil.isEmpty(typeName)&&typeName.equals("平台移库数量")){
					          vmTotalQuantity = vmTotalQuantity.add(checkTotalQuantity);
						      if(vmTotalQuantity.compareTo(amount)==1){
						    	  ExceptionUtil.throwServiceException("平台移库数量大于平台额度");	  
						      }
				        }
					}
				}
			}
		}
		
		//更新pdf
		Setting setting = SettingUtils.get();
		try {
			this.createPdf(setting, id);
		} catch (Exception e) {
			e.printStackTrace();
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		// 创建流程实例
		createWf(moveLibrary.getSn(), String.valueOf(storeMember.getId()),
				new Long[]{moveLibrary.getSaleOrg().getId()},null,modelId,
				objTypeId,id,WebUtils.getCurrentCompanyInfoId(),true);
		moveLibrary.setMoveStatus(MoveStatus.auditing);
		update(moveLibrary);
		orderFullLinkService.addFullLink(18,null,moveLibrary.getSn(),
				ConvertUtil.convertI18nMsg("18701",new Object[] {"移库审核"}),null);

	}

	@Override
	@Transactional
	public void endBack(ActWf wf) {
		
		MoveLibrary moveLibrary = find(wf.getObjId());
		if(ConvertUtil.isEmpty(moveLibrary)){
			ExceptionUtil.throwServiceException("该移库单不存在");
		}
		if(!ConvertUtil.isEmpty(moveLibrary.getMoveStatus()) && 
				!moveLibrary.getMoveStatus().equals(MoveStatus.auditing)){
			ExceptionUtil.throwServiceException("只有单据状态为审核中的移库单才能通过");
		}
		for (MoveLibraryItem moveLibraryItem : moveLibrary.getMoveLibraryItems()) {
			if(ConvertUtil.isEmpty(moveLibraryItem.getAmount()) || 
					(!ConvertUtil.isEmpty(moveLibraryItem.getAmount()))&& moveLibraryItem.getAmount().compareTo(BigDecimal.ZERO)==0){
				ExceptionUtil.throwServiceException("产品编码为【" + moveLibraryItem.getVonderCode() + "】的金额不能为空并且大于0");
			}
		}
		moveLibrary.setMoveStatus(MoveStatus.audited);  
		update(moveLibrary);
		
		  //家哇云接口
	    if(moveLibrary.getIssueWarehouse().getIsSendJiaWaYun())  {
            // 写接口表
            //this.saveIntfAtLogistics(moveLibrary,0);
        }
	    /*String isIntegration  = SystemConfig.getConfig("isIntegration",WebUtils.getCurrentCompanyInfoId());
		if (wf.getStat()== 2) {
			MoveLibrary moveLibrary = find(wf.getObjId());
			for (MoveLibraryItem moveLibraryItem : moveLibrary.getMoveLibraryItems()) {
				if(ConvertUtil.isEmpty(moveLibraryItem.getAmount())||
						(!ConvertUtil.isEmpty(moveLibraryItem.getAmount()))&&
						moveLibraryItem.getAmount().compareTo(BigDecimal.ZERO)==0){
					ExceptionUtil.throwServiceException("产品编码为【" + 
						moveLibraryItem.getVonderCode() + 
						"】的金额不能为空并且大于0");
				}
			}
			Long companyInfoId = moveLibrary.getCompanyInfoId();
			String msg = "";
			CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
			if ("nature".equals(companyInfo.getCompany_code())) {
				SystemDict stock = moveLibrary.getIssueWarehouse()
						.getStockSystemDict();
				if (stock == null) {
					ExceptionUtil.throwServiceException("来源仓【"
							+ moveLibrary.getIssueWarehouse().getName()
							+ "】的经营组织不能为空");
				}
				else if (stock.getCode() == null) {
					ExceptionUtil.throwServiceException("来源仓【"
							+ moveLibrary.getIssueWarehouse().getName()
							+ "】的经营组织编码不能为空");
				}
				Sbu sbu = moveLibrary.getSbu();
				if (sbu == null) {
					ExceptionUtil.throwServiceException("移库单【"
							+ moveLibrary.getSn()
							+ "】的SBU不能为空");
				}
				HashMap<String, Object> requestMap = new HashMap<String, Object>();
				//requestMap.put("SOURCE_CODE", "CRM");// 来源系统代码,固定“CRM”
				requestMap.put("HEADER_ID", moveLibrary.getId());// 来源ID,移库单id
				requestMap.put("ORGANIZATION_ID", stock.getRemark());// 经营组织id,
				requestMap.put("REQUEST_NUMBER", moveLibrary.getSn());// 移库单号
				requestMap.put("INV_SUB_FROM", moveLibrary.getIssueWarehouse()
						.getErp_warehouse_code());// 来源仓编码
				requestMap.put("INV_SUB_TO", moveLibrary.getReceiveWarehouse()
						.getErp_warehouse_code());// 目标仓编码
				requestMap.put("CREATER", moveLibrary.getStoreMember()
						.getUsername());// 创建人
				requestMap.put("CREATE_DATE", DateUtil.convert(new Date()));// 审核时间
				requestMap.put("DEMAND_DATE",
						moveLibrary.getNeedDate() == null ? ""
								: moveLibrary.getNeedDate());//需求时间
				requestMap.put("STATUS", moveLibrary.getMoveStatus());//状态
				requestMap.put("REMARK", moveLibrary.getRemarks() == null ? "" : moveLibrary.getRemarks());//头备注
				List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();
				for (MoveLibraryItem moveLibraryItem : moveLibrary.getMoveLibraryItems()) {
					Product product = moveLibraryItem.getProduct();
					String productGradeName = "";
					if (moveLibraryItem != null && moveLibraryItem.getProductLevel() != null) {
						if (!ConvertUtil.isEmpty(moveLibraryItem.getProductLevel().getValue())) {
							productGradeName = moveLibraryItem.getProductLevel().getValue();
						}else {
							ExceptionUtil.throwServiceException("产品【"+ moveLibraryItem.getProductName()+ "】的物料等级不能为空");
						}
					}
					Map<String, Object> lineData = new HashMap<String, Object>();
					Map<String, Object> lineTblItem = new HashMap<String, Object>();
					//lineTblItem.put("SOURCE_CODE", "CRM");// 来源系统代码,CRM
					lineTblItem.put("LINE_ID", moveLibraryItem.getId());// 行ID
					lineTblItem.put("ITEM_NUMBER",
							moveLibraryItem.getVonderCode());// 物料编码
					lineTblItem.put("ORDERED_QUANTITY",
							moveLibraryItem.getQuantity());// 主数量
					lineTblItem.put("QUANTITY_UOM", product.getUnit());// 单位
					lineTblItem.put("ORDERED_QUANTITY1",
							moveLibraryItem.getBranchQuantity().setScale(6));// 辅助数量
					lineTblItem.put("QUANTITY_UOM1", product.getSpecTwo() == null ? "" : product.getSpecTwo());// 辅助单位(支数)
					lineTblItem.put("ORDERED_QUANTITY2",
							moveLibraryItem.getBoxQuantity().setScale(6));// 辅助数量
					lineTblItem.put("QUANTITY_UOM2", "箱");// 辅助单位（箱数）
					lineTblItem.put("LEVEL_NUM", productGradeName);// 等级
					lineTblItem.put("REMARK", moveLibraryItem.getRemarks() == null ? "" : moveLibraryItem.getRemarks());// 行备注
					lineData.put("LINE_TBL_ITEM", lineTblItem);
					lineTbl.add(lineData);
				}
				LogUtils.error("111" + lineTbl.size());
				requestMap.put("LINE_TBL", lineTbl);
				try {
				       if( isIntegration != null && "1".equals(isIntegration) ) {
			                 // 写接口表
				           msg = new NatureMoveLibraryCreate().createMoveLibrary(requestMap);
				       }
				}catch (Exception e) {
					e.printStackTrace();
				}
				if (!msg.equals("S")) {
					//ExceptionUtil.throwServiceException("操作失败!ERP返回信息：" + msg);
				}
			}
			moveLibrary.setMoveStatus(MoveStatus.audited);  
			update(moveLibrary);
			
			//物流接口
			if(!moveLibrary.getSbu().getName().equals("壁纸")){
		        if( isIntegration != null && "1".equals(isIntegration) ) {
                    // 写接口表
		            this.saveIntfAtLogistics(moveLibrary,0);
		        }
			}
		}*/
		
	}
	

	public void saveIntfAtLogistics(MoveLibrary moveLibrary,Integer falg) {
	    intfOrderMessageToService.saveLogisticsMoveIntf(moveLibrary,falg);
	}
	
	
	
	/**
	 * 中断流程回调
	 */
	@Override
	@Transactional
	public void interruptBack(ActWf wf) {
		
		MoveLibrary moveLibrary = find(wf.getObjId());
		if(ConvertUtil.isEmpty(moveLibrary)){
			ExceptionUtil.throwServiceException("该移库单不存在");
		}
		if(!ConvertUtil.isEmpty(moveLibrary.getMoveStatus()) && 
				!moveLibrary.getMoveStatus().equals(MoveStatus.auditing)){
			ExceptionUtil.throwServiceException("只有单据状态为审核中的移库单才能中断");
		}
		moveLibrary.setMoveStatus(MoveStatus.saved);
		update(moveLibrary);
		List<String> moveLibrarys = new ArrayList<String>();
		moveLibrarys.add(moveLibrary.getSn());
		orderFullLinkService.addFullLink(1,moveLibrarys,moveLibrary.getSn(),
				ConvertUtil.convertI18nMsg("18704"),null);
	}
	
	
	/**
	 * 驳回流程回调
	 */
	@Override
	@Transactional
	public void rejectBack(ActWf wf) {
		MoveLibrary moveLibrary = find(wf.getObjId());
		if(ConvertUtil.isEmpty(moveLibrary)){
			ExceptionUtil.throwServiceException("该移库单不存在");
		}
		if(!ConvertUtil.isEmpty(moveLibrary.getMoveStatus()) && 
				!moveLibrary.getMoveStatus().equals(MoveStatus.auditing)){
			ExceptionUtil.throwServiceException("只有单据状态为审核中的移库单才能驳回");
		}
		moveLibrary.setWfState(3);
		update(moveLibrary);
	}
	
	
	/**
	 * 通过流程节点回调
	 */
	@Override
	@Transactional
	public void agreeBack(ActWf wf) {
		MoveLibrary moveLibrary = find(wf.getObjId());
		if(moveLibrary.getWfState() == 3){
			if(ConvertUtil.isEmpty(moveLibrary)){
				ExceptionUtil.throwServiceException("该移库单不存在");
			}
			if(!ConvertUtil.isEmpty(moveLibrary.getMoveStatus()) && 
					!moveLibrary.getMoveStatus().equals(MoveStatus.auditing)){
				ExceptionUtil.throwServiceException("只有单据状态为审核中的移库单才能通过");
			}
			moveLibrary.setWfState(1);
			update(moveLibrary);
		}
	}
	
	@Override
	@Transactional
	public void cancel(MoveLibrary moveLibrary) throws Exception {

		moveLibrary.setMoveStatus(MoveStatus.cancelled);
		update(moveLibrary);
		orderFullLinkService.addFullLink(1,null,moveLibrary.getSn(),"作废本次单据",null);
	}

	public void cancelIntf(MoveLibrary moveLibrary) throws Exception {
		Long companyInfoId = moveLibrary.getCompanyInfoId();
		String msg = "";
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);

//		if ("dzrjj".equals(companyInfo.getCompany_code())) {
//            // 家哇云移库取消接口
//            HashMap<String, Object> params = new HashMap<String, Object>();
//			params.put("type", "AM移库单");// 订单类型
//            params.put("salesNo", moveLibrary.getSn());
//            params.put("shippingId", moveLibrary.getId());
//            params.put("cancelDate", DateUtil.convert(new Date()));
//            params.put("status", 2);
//            params.put("operator", storeMemberBaseService.getCurrent().getUsername());
//			msg = new CancelRealTimePush().cancelBill(params);
//            if (!msg.equals("S")) {
//                ExceptionUtil.throwServiceException("操作失败!返回信息：" + msg);
//            }
//
//		}
	}

	public Integer count(String sn,Long issueWarehouseId,Long receiveWarehouseId,
			Integer[] status,String storeMemberName,Long[] productId,String firstTime, 
			String lastTime,Long[] moveSttuss,Long[] moveReceiveStatus,String firstIssueDate,
			String lastIssueDate,String firstReceiveDate,String lastReceiveDate,Pageable pageable, 
			Integer page,Integer size,Long[] wfStates) {
		return moveLibraryDao.count(sn,issueWarehouseId,receiveWarehouseId,status,
				storeMemberName,productId,firstTime,lastTime,moveSttuss,moveReceiveStatus,
				firstIssueDate,lastIssueDate,firstReceiveDate,lastReceiveDate,pageable,
				page,size,wfStates);
	}

	@Override
	public List<Map<String, Object>> findItemList(String sn,Long issueWarehouseId,
			Long receiveWarehouseId,Integer[] status,String storeMemberName,Long[] productId,
			String firstTime,String lastTime,Long[] ids,Boolean isNotToErp,Integer page,Integer size, 
			Long sbuId,Long[] moveSttuss,Long[] moveReceiveStatus,String firstIssueDate,String lastIssueDate,
			String firstReceiveDate,String lastReceiveDate,Long[] wfStates) {
		return moveLibraryDao.findItemList(sn,issueWarehouseId,receiveWarehouseId,status,
				storeMemberName,productId,firstTime,lastTime,ids,isNotToErp,page,size,sbuId,
				moveSttuss,moveReceiveStatus,firstIssueDate,lastIssueDate,firstReceiveDate,
				lastReceiveDate,wfStates);
	}

	/**
	 * 获取TriplicateForm
	 */
	@Transactional(readOnly = true)
	public TriplicateForm getTriplicateForm(Long id) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("objId", id));
		filters.add(Filter.eq("type", 0));
		filters.add(Filter.eq("seq", 0));
		List<TriplicateForm> triplicateForms = triplicateFormService.findList(null,
				filters,
				null);
		TriplicateForm triplicateForm = null;
		if (triplicateForms.size() > 0) {
			triplicateForm = triplicateForms.get(0);
		}
		return triplicateForm;
	}
	
	/**
	 * 获取pdf模板
	 */
	@Transactional(readOnly = true)
	public String getPdfTemplate(Long companyInfoId) {
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		String company_code = companyInfo.getCompany_code();
		String template_path = null;
		if ("dzrjj".equals(company_code) || "nature".equals(company_code)) {
			template_path = "/pdf/moveLibrary/template/moveLibrary.pdf";
		}
		return template_path;
	}
	
	/**
	 * 创建TriplicateForm
	 */
	@Override
	public TriplicateForm createTriplicateForm(Setting setting, MoveLibrary moveLibrary) throws Exception {
		String siteUrl = setting.getSiteUrl();
		String template_path = getPdfTemplate(moveLibrary.getCompanyInfoId());
		String file_name = "" + UUID.randomUUID();
		String pdfStaticPath = "/pdf/moveLibrary/file/" + file_name + ".pdf";
		
		this.buildPdf(moveLibrary, pdfStaticPath, template_path);

		/** 保存中间表 */
		String url = siteUrl + pdfStaticPath;
		TriplicateForm triplicateForm = new TriplicateForm(moveLibrary.getId(), 0,url, 0);
		triplicateFormService.save(triplicateForm);
		return triplicateForm;
	}

	public void createPdf(Setting setting, Long... ids) throws Exception {
		List<String> files = new ArrayList<String>();
		try {
			for (Long id : ids) {
				MoveLibrary moveLibrary = find(id);
				TriplicateForm triplicateForm = createTriplicateForm(setting,moveLibrary);
				files.add(triplicateForm.getUrl());
			}
		}
		catch (Exception e) {
			for (String file : files) {
				this.deleteFile(file);
			}
			throw e;
		}
	}
	
	@Transactional(readOnly = true)
	public int deleteFile(String staticPath) {
		Assert.hasText(staticPath);
		File staticFile = new File(servletContext.getRealPath(staticPath));
		if (staticFile.exists()) {
			staticFile.delete();
			return 1;
		}
		return 0;
	}
	
	private String FONT_PATH = "/usr/share/fonts/self/simsun.ttf";
	
	public void buildPdf(MoveLibrary moveLibrary, String pdfStaticPath,
			String templateStaticPath) throws Exception {

		String PdfTemplatePath = servletContext.getRealPath(templateStaticPath);
		String outputFile = servletContext.getRealPath(pdfStaticPath);
		FileOutputStream fos = new FileOutputStream(outputFile);// 需要生成PDF
		List<MoveLibraryItem> moveLibraryItems = moveLibrary.getMoveLibraryItems();
		int itemSize = moveLibraryItems.size();
		int pageSize = 5;	//设置每页pdf行数
		int pdfPage = itemSize % pageSize == 0 ? itemSize / pageSize : (itemSize / pageSize) + 1;
		ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pdfPage];// 用于存储每页生成PDF流
		BaseFont bf = BaseFont.createFont(FONT_PATH,BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);
		NumberFormat nf = NumberFormat.getInstance();
		for (int i = 0; i < pdfPage; i++) {
			baos[i] = new ByteArrayOutputStream();
			PdfReader reader = new PdfReader(PdfTemplatePath);
			PdfStamper stamp = new PdfStamper(reader, baos[i]);

			ArrayList<BaseFont> fontList = new ArrayList<BaseFont>();
			fontList.add(bf);
			AcroFields form = stamp.getAcroFields();
			int column = 1;// 列数 3 1-3 4
			int forNumber = i * pageSize + pageSize > itemSize ? itemSize : i * pageSize + pageSize;
			form.setSubstitutionFonts(fontList);
			//最终仓
			Warehouse receiveWarehouse = moveLibrary.getReceiveWarehouse();
			if(ConvertUtil.isEmpty(receiveWarehouse)){
				receiveWarehouse = new Warehouse();
			}
			/*给pdf表单字段赋值*/
			form.setField("page_info", (i + 1) + " / " + pdfPage);											//页号
			form.setField("sn",ConvertUtil.toEmpty(moveLibrary.getSn()));									//单据编号
			form.setField("remarks",ConvertUtil.toEmpty(moveLibrary.getRemarks()));							//说明
			form.setField("issueWarehouseName",ConvertUtil.toEmpty(moveLibrary.getIssueWarehouse().getName()));		//来源仓库
			form.setField("receiveWarehouseName",ConvertUtil.toEmpty(moveLibrary.getReceiveWarehouse().getName()));	//目标仓库
			form.setField("final_warehouseName",ConvertUtil.toEmpty(receiveWarehouse.getName()));	//最终仓库
			form.setField("createDate",DateUtil.convertDateToStr(moveLibrary.getCreateDate(),"yyyy-MM-dd"));		//创建日期
			form.setField("saleOrgName",ConvertUtil.toEmpty(moveLibrary.getIssueWarehouse().getManagementOrganization().getName()));	//组织
			form.setField("sender",ConvertUtil.toEmpty(receiveWarehouse.getSender()));	//收货人
			form.setField("mobile",ConvertUtil.toEmpty(receiveWarehouse.getMobile()));	//联系方式
			form.setField("address",ConvertUtil.toEmpty(receiveWarehouse.getAddress())); //详细地址	
			form.setField("remarks",ConvertUtil.toEmpty(moveLibrary.getRemarks())); //备注
			BigDecimal total_quantity = BigDecimal.ZERO;		//出仓数量
			BigDecimal total_branch_quantity = BigDecimal.ZERO;	//出仓辅量
			BigDecimal total_box_quantity = BigDecimal.ZERO;	//件数
			BigDecimal total_quantity1 = new BigDecimal("0"); //数量
			BigDecimal total_branch_quantity1 = BigDecimal.ZERO; //支数
			BigDecimal total_box_quantity1 = BigDecimal.ZERO; //件数
			for (int j = i * pageSize; j < forNumber; j++) {
				MoveLibraryItem moveLibraryItem = moveLibraryItems.get(j);
				form.setField("row_" + column, String.valueOf(column));													//行号
				form.setField("vonder_code_" + column,ConvertUtil.toEmpty(moveLibraryItem.getVonderCode()));			//物料
				//form.setField("detail_description_" + column,ConvertUtil.toEmpty(moveLibraryItem.getProduct().getDescription()));	//说明
				form.setField("woodType_or_color_" + column,ConvertUtil.toEmpty(moveLibraryItem.getProduct().getWoodTypeOrColor()));	//木种
                if(!(moveLibraryItem.getProduct().getWoodTypeOrColor()==null)){
                    String s=ConvertUtil.toEmpty(moveLibraryItem.getProduct().getDescription());
                   	s += "-"+ConvertUtil.toEmpty(moveLibraryItem.getProduct().getWoodTypeOrColor());
                    form.setField("detail_description_" + column,s);	//说明
                }else {
                    form.setField("detail_description_" + column,ConvertUtil.toEmpty(moveLibraryItem.getProduct().getDescription()));	//说明
                }
				form.setField("product_model_" + column,ConvertUtil.toEmpty(moveLibraryItem.getProduct().getModel()));	//型号
				form.setField("unit_" + column,ConvertUtil.toEmpty(moveLibraryItem.getProduct().getUnit()));			//主单位
				form.setField("specTwo_" + column,ConvertUtil.toEmpty(moveLibraryItem.getProduct().getSpecTwo()));		//辅单位
				//数量
				BigDecimal quantity1 = null;
				if(!ConvertUtil.isEmpty(moveLibraryItem.getQuantity())){
					quantity1 = moveLibraryItem.getQuantity().stripTrailingZeros();
				}else{
					quantity1 = BigDecimal.ZERO;
				}
				form.setField("quantity_" + column,quantity1.toPlainString());	
				total_quantity1 = total_quantity1.add(quantity1);
				
				//支数
				BigDecimal branchQuantity1 = null;
				if(!ConvertUtil.isEmpty(moveLibraryItem.getBranchQuantity())){
					branchQuantity1 = moveLibraryItem.getBranchQuantity().stripTrailingZeros();
				}else{
					branchQuantity1 = BigDecimal.ZERO;
				}
				form.setField("branch_quantity_" + column,branchQuantity1.toPlainString());	
				total_branch_quantity1 = total_branch_quantity1.add(branchQuantity1);
				
				//箱数
				BigDecimal boxQuantity1 = null;
				if(!ConvertUtil.isEmpty(moveLibraryItem.getBoxQuantity())){
					boxQuantity1 = moveLibraryItem.getBoxQuantity().stripTrailingZeros();
				}else{
					boxQuantity1 = BigDecimal.ZERO;
				}
				form.setField("box_quantity_" + column,boxQuantity1.toPlainString());
				total_box_quantity1 = total_box_quantity1.add(boxQuantity1);
				
				String gradeName = moveLibraryItem.getProductLevel()!=null?moveLibraryItem.getProductLevel().getValue():"";
				//经营组织
				form.setField("product_organization_name_" + column,
						ConvertUtil.toEmpty(moveLibraryItem.getProductOrganization() == null ?
								"" : moveLibraryItem.getProductOrganization().getName()));
				//色号
				form.setField("color_" + column,
						ConvertUtil.toEmpty(moveLibraryItem.getColourNumbers() == null ?
								"" : moveLibraryItem.getColourNumbers().getValue()));
				//含水率
				form.setField("moistureContent_" + column,
						ConvertUtil.toEmpty(moveLibraryItem.getMoistureContents() == null ?
								"" : moveLibraryItem.getMoistureContents().getValue()));
				form.setField("product_grade_" + column,gradeName);		//产品等级
				form.setField("issueDate_" + column,DateUtil.convertDateToStr(moveLibrary.getIssueDate(),"yyyy-MM-dd"));		//出仓时间
				form.setField("move_library_item_remarks_" + column,ConvertUtil.toEmpty(moveLibraryItem.getRemarks()));			//参考
				
				BigDecimal quantity = moveLibraryItem.getIssueQuantity() == null ? BigDecimal.ZERO : moveLibraryItem.getIssueQuantity();
				BigDecimal branchQuantity = moveLibraryItem.getIssueBranchQuantity() == null ? BigDecimal.ZERO : moveLibraryItem.getIssueBranchQuantity();
				BigDecimal branchPerBox = moveLibraryItem.getBranchPerBox() == null ? BigDecimal.ZERO : moveLibraryItem.getBranchPerBox();
				//BigDecimal boxQuantity = moveLibraryItem.getIssueBoxQuantity() == null ? BigDecimal.ZERO : moveLibraryItem.getIssueBoxQuantity();
                BigDecimal boxQuantity =branchQuantity.divide(branchPerBox,0,BigDecimal.ROUND_DOWN);

				form.setField("issue_quantity_" + column,nf.format(quantity));				//出仓数量
				form.setField("issue_branch_quantity_" + column,nf.format(branchQuantity));	//出仓支数
				form.setField("issue_box_quantity_" + column,nf.format(boxQuantity));		//出仓箱数
				
				total_quantity = total_quantity.add(quantity);
				total_box_quantity = total_box_quantity.add(boxQuantity);
				total_branch_quantity = total_branch_quantity.add(branchQuantity);
				
				column++;
			}
			form.setField("total_quantity", nf.format(total_quantity));
			form.setField("total_box_quantity", nf.format(total_box_quantity));
			form.setField("total_branch_quantity",nf.format(total_branch_quantity));
			form.setField("total_quantity1", total_quantity1.toPlainString()); //总数量
			form.setField("total_branch_quantity1",nf.format(total_branch_quantity1)); //总支数
			form.setField("total_box_quantity1", nf.format(total_box_quantity1)); //总件数
			stamp.setFormFlattening(true); // 千万不漏了这句啊, */
			stamp.close();
		}
		Document doc = new Document();
		PdfCopy pdfCopy = new PdfCopy(doc, fos);
		doc.open();
		PdfImportedPage impPage = null;
		// doc.add(new Paragraph("",font));
		/** 取出之前保存的每页内容 */
		for (int i = 0; i < pdfPage; i++) {
			impPage = pdfCopy.getImportedPage(new PdfReader(
					baos[i].toByteArray()),
					1);
			pdfCopy.addPage(impPage);
		}
		doc.close();// 当文件拷贝 记得作废doc
	}
	
	public Map<String, Object> setFormData(ActWf wf, String taskId) {
		Map<String, Object> dayMap = new HashMap<String, Object>();
		dayMap.put("warehouseId", this.find(wf.getObjId()).getReceiveWarehouse().getId());
		dayMap.put("sbu", this.find(wf.getObjId()).getSbu().getId());
        dayMap.put("sbuName", this.find(wf.getObjId()).getSbu().getName());

        dayMap.put("receiveOrganizationId", this.find(wf.getObjId()).getReceiveWarehouse().getManagementOrganization().getId());
        dayMap.put("issueOrganizationId", this.find(wf.getObjId()).getIssueWarehouse().getManagementOrganization().getId());
        
        dayMap.put("receiveOrganization", this.find(wf.getObjId()).getReceiveWarehouse().getManagementOrganization().getName());
        dayMap.put("issueOrganization", this.find(wf.getObjId()).getIssueWarehouse().getManagementOrganization().getName());
        
        dayMap.put("receiveWarehouseTypeId", this.find(wf.getObjId()).getReceiveWarehouse().getTypeSystemDict().getId());
        dayMap.put("issueWarehousetypeId", this.find(wf.getObjId()).getIssueWarehouse().getTypeSystemDict().getId());

        dayMap.put("receiveWarehouseType", this.find(wf.getObjId()).getReceiveWarehouse().getTypeSystemDict().getValue());
        dayMap.put("issueWarehousetype", this.find(wf.getObjId()).getIssueWarehouse().getTypeSystemDict().getValue());
        dayMap.put("saleOrg", this.find(wf.getObjId()).getSaleOrg().getName());
        dayMap.put("saleOrgId",this.find(wf.getObjId()).getSaleOrg().getId());
        dayMap.put("productOrganization",this.find(wf.getObjId()).getMoveLibraryItems().get(0).getProductOrganization().getId());
		return dayMap;
	}

	@Override
	public Page<Map<String, Object>> movelibraryListData(String sn,
			Long[] saleOrgId,Long[] issueWarehouseId,Long[] receiveWarehouseId,
			Long[] sbuId,Long[] productId,String startIssueDate,String endIssueDate,
			String startReceiveDate,String endReceiveDate,Long[] creatorId,
			Boolean movelibrarySign,Pageable pageable) {
			
		
		Page<Map<String, Object>> page = moveLibraryDao.newFindMovelibraryPage(sn,saleOrgId,
				issueWarehouseId,receiveWarehouseId,sbuId,productId,startIssueDate,endIssueDate,
				startReceiveDate,endReceiveDate,creatorId,movelibrarySign,pageable) ;
		
		return page;
	}

	@Override
	public List<Map<String, Object>> findMovelibraryItemList(Long[] ids,Integer type){
		return moveLibraryDao.findMovelibraryItemList(ids,type);
	}

	@Override
	public List<Map<String, Object>> findMovelibraryList(Integer page, Integer size, Long[] ids, String sn,
			Long[] saleOrgId, Long[] issueWarehouseId, Long[] receiveWarehouseId, Long[] sbuId, Long[] productId,
			String startIssueDate, String endIssueDate, String startReceiveDate, String endReceiveDate,
			Long[] creatorId,Boolean movelibrarySign) {
		
		return moveLibraryDao.findMovelibraryList(page, size, ids, sn, saleOrgId, 
				issueWarehouseId, receiveWarehouseId, sbuId, productId, startIssueDate, 
				endIssueDate, startReceiveDate, endReceiveDate, creatorId,movelibrarySign);
	}
	
	@Transactional
	@Override
	public void auditedUpdateProductPrice(MoveLibrary moveLibrary) {
		
		List<MoveLibraryItem> moveLibraryItemList = new ArrayList<MoveLibraryItem>();
		List<MoveLibraryItem> MoveLibraryItems = moveLibrary.getMoveLibraryItems();
		for (MoveLibraryItem moveLibraryItem : MoveLibraryItems) {
			if (moveLibraryItem == null || moveLibraryItem.getId() == null) {
				continue;
			}
			MoveLibraryItem pMoveLibraryItem = moveLibraryItemService.find(moveLibraryItem.getId());
			pMoveLibraryItem.setPrice(moveLibraryItem.getPrice());
			pMoveLibraryItem.setAmount(moveLibraryItem.getPrice().multiply(pMoveLibraryItem.getQuantity()));
			moveLibraryItemList.add(pMoveLibraryItem);
		}
		MoveLibrary pMoveLibrary = find(moveLibrary.getId());
		pMoveLibrary.getMoveLibraryItems().clear();
		pMoveLibrary.getMoveLibraryItems().addAll(moveLibraryItemList);
		
		update(pMoveLibrary);
		
		orderFullLinkService.addFullLink(1,null,pMoveLibrary.getSn(),"修改移库单明细产品单价",null);
			
	}

	@Override
	public List<Map<String, Object>> newFindMovelibraryPageIds(Boolean movelibrarySign, Pageable pageable) {		
		return moveLibraryDao.newFindMovelibraryPageIds(movelibrarySign, pageable);
	}

	@Transactional
	@Override
	public void close(MoveLibrary moveLibrary) throws Exception {
		List<MoveLibraryItem> moveLibraryItems = moveLibrary.getMoveLibraryItems();
		if(!moveLibraryItems.isEmpty() && moveLibraryItems.size()>0){
			for (MoveLibraryItem moveLibraryItem : moveLibraryItems) {
				//发出数量
				if(ConvertUtil.isEmpty(moveLibraryItem.getIssueQuantity())){
					moveLibraryItem.setIssueQuantity(BigDecimal.ZERO);
				}
				//接收数量
				if(ConvertUtil.isEmpty(moveLibraryItem.getReceiveQuantity())){
					moveLibraryItem.setReceiveQuantity(BigDecimal.ZERO);
				}
				//关闭数量
				if(ConvertUtil.isEmpty(moveLibraryItem.getCloseQuantity())){
					moveLibraryItem.setCloseQuantity(BigDecimal.ZERO);
				}
				if(moveLibraryItem.getIssueQuantity().compareTo(moveLibraryItem.getReceiveQuantity().add(moveLibraryItem.getCloseQuantity())) != 0){
					ExceptionUtil.throwServiceException("产品编码为【"+moveLibraryItem.getVonderCode()+"】的发出数量与接收加关闭数量不相等，不能关闭操作");
				}
			}
		}
		moveLibrary.setMoveStatus(MoveStatus.close);
		update(moveLibrary);
		orderFullLinkService.addFullLink(1,null,moveLibrary.getSn(),"关闭本次单据",null);


            // 接口
            if(moveLibrary.getIssueWarehouse().getIsSendJiaWaYun()){
                this.cancelIntf(moveLibrary);
            }

	}

	@Override
	public Map<String, Object> findTransferInformation(Long moveLibraryId, int typeId, Pageable pageable) {
		if (moveLibraryId == null || moveLibraryId == 0) {
			ExceptionUtil.throwServiceException("参数ID不能为空");
		}
		List<Map<String, Object>> listDatas = new ArrayList<Map<String,Object>>();
		Map<String, Object> title = new HashMap<String, Object>();
		try{
			//获取移库数据信息
			Page<Map<String, Object>> pageData = moveLibraryDao.findTransferInformation(moveLibraryId,typeId,pageable);
			if (pageData != null && pageData.getTotal() > 0){
				Map<String,Object> firstData = pageData.getContent().get(0);
				title.put("head","物料搬运单");          //报表标题
				title.put("sn", CommonUtil.emptyConversion(firstData.get("sn")));						//单据编号
				title.put("projectCode",CommonUtil.emptyConversion(firstData.get("")));		//项目编号
				title.put("projectName",CommonUtil.emptyConversion(firstData.get("")));				//项目
				title.put("explain",CommonUtil.emptyConversion(firstData.get("remarks")));				//说明
				title.put("sourceWarehouse",CommonUtil.emptyConversion(firstData.get("issueW_name")));				//来源仓库
				title.put("targetWarehouse",CommonUtil.emptyConversion(firstData.get("receiveW_name")));				//目标仓库
				title.put("finalWarehouse",CommonUtil.emptyConversion(firstData.get("receiveW_name")));				//最终仓库
				title.put("creationTime",CommonUtil.emptyConversion(firstData.get("createDate")));				//创建时间
				title.put("consignee",CommonUtil.emptyConversion(firstData.get("sender")));				//收货人
				title.put("phone",CommonUtil.emptyConversion(firstData.get("mobile")));			//联系电话
				title.put("address",CommonUtil.emptyConversion(firstData.get("address")));			//联系地址
                //  合计
                BigDecimal totalBoxQuantity = new BigDecimal("0");   //箱
                BigDecimal totalBranchQuantity = new BigDecimal("0");   //支
                BigDecimal totalQuantity = new BigDecimal("0");   //数量
				for (Map<String,Object> map : pageData.getContent()){
					Map<String,Object> detail = new HashMap<String, Object>();
					detail.put("lineNo",CommonUtil.emptyConversion(map.get("id")));  //行号
					detail.put("materiel",CommonUtil.emptyConversion(map.get("vonder_code"))); //物料
					detail.put("explain",CommonUtil.emptyConversion(map.get("des")));  //说明
					detail.put("model",CommonUtil.emptyConversion(map.get("productModel")));  //型号
					detail.put("mainUnit",CommonUtil.emptyConversion(map.get("unit"))); //单位
					detail.put("number",CommonUtil.emptyConversion(map.get("quantity"))); //数量
					detail.put("count",CommonUtil.emptyConversion(map.get("branch_quantity")).substring(0, ConvertUtil.toEmpty(map.get("branch_quantity")).length()-7)); //支数
					detail.put("boxes",CommonUtil.emptyConversion(map.get("box_quantity")).substring(0, ConvertUtil.toEmpty(map.get("box_quantity")).length()-7)); //箱数
					detail.put("grade",CommonUtil.emptyConversion(map.get("grade")));  //等级
					detail.put("exitTime",CommonUtil.emptyConversion(map.get("issue_date"))); //出仓时间

					detail.put("exitNumber",CommonUtil.emptyConversion(map.get("exitNumber"))); //出仓数量
                    totalQuantity = totalQuantity.add(new BigDecimal(map.get("exitNumber")==null?"0":String.valueOf(map.get("exitNumber"))));

                    detail.put("exitCount",CommonUtil.emptyConversion(map.get("exitCount"))); //出仓支数
                    totalBranchQuantity = totalBranchQuantity.add(new BigDecimal(map.get("exitCount")==null?"0":String.valueOf(map.get("exitCount"))));

                    detail.put("exitBoxes",CommonUtil.emptyConversion(map.get("exitBoxes"))); //出仓箱数
                    totalBoxQuantity = totalBoxQuantity.add(new BigDecimal(map.get("exitBoxes")==null?"0":String.valueOf(map.get("exitBoxes"))));

                    detail.put("colorNum",CommonUtil.emptyConversion(map.get("colorNum"))); //色号
					listDatas.add(detail);
				}
				//统计合计
                Map<String,Object> detail = new HashMap<String, Object>();
				detail.put("materiel","合计");
				//出仓数量合计
                detail.put("exitNumber",CommonUtil.emptyConversion(totalQuantity)); //出仓数量
                detail.put("exitCount",CommonUtil.emptyConversion(String.valueOf(totalBranchQuantity))); //出仓支数
                detail.put("exitBoxes",CommonUtil.emptyConversion(String.valueOf(totalBoxQuantity))); //出仓支数
				listDatas.add(detail);
                //结束行
                Map<String,Object> endDetail = new HashMap<String, Object>();
                endDetail.put("lineNo","仓管:");
                endDetail.put("explain","签收人:");
                endDetail.put("model","车牌号:");
                endDetail.put("exitCount","主管:");
                endDetail.put("exitTime","组织:");
				endDetail.put("exitNumber",CommonUtil.emptyConversion(firstData.get("orgName")));
				listDatas.add(endDetail);
            }else {
				initializationData(title,listDatas);
			}
		}catch (Exception e){
			initializationData(title,listDatas);
			logger.error("移库信息报表数据处理异常",e);
		}finally {
			title.put("maplist",listDatas);
			return title;
		}
	}


	//初始化数据
	void initializationData(Map<String, Object> title,List<Map<String, Object>> listDatas){
		title.put("head","物料搬运单");          //报表标题
		title.put("sn","");						//单据编号
		title.put("projectCode","");		//项目编号
		title.put("projectName","");				//项目
		title.put("explain","");				//说明
		title.put("sourceWarehouse","");				//来源仓库
		title.put("targetWarehouse","");				//目标仓库
		title.put("finalWarehouse","");				//最终仓库
		title.put("creationTime","");				//创建时间
		title.put("consignee","");				//收货人
		title.put("phone","");			//联系电话
		title.put("address","");			//联系地址
		//单据明细
		Map<String,Object> detail = new HashMap<String, Object>();
		detail.put("lineNo","");
		detail.put("materiel","");
		detail.put("explain","");
		detail.put("model","");
		detail.put("mainUnit","");
		detail.put("number","");
		detail.put("count","");
		detail.put("boxes","");
		detail.put("grade","");
		detail.put("exitTime","");
		detail.put("exitNumber","");
		detail.put("exitCount","");
		detail.put("exitBoxes","");
		detail.put("colorNum","");
		listDatas.add(detail);
	}

	@Override
	public List<Map<String, Object>> findVMoveLibraryList(Long id) {
		return moveLibraryDao.findVMoveLibraryList(id);
	}
	
	/**
	 * 参数不能为空
	 */
	@Override
	public void checkParamIsNotNull(MoveLibraryItem moveLibraryItem) {
		
		/**数量*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getQuantity())){
			moveLibraryItem.setQuantity(BigDecimal.ZERO);
		}
		/**支数*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getBranchQuantity())){
			moveLibraryItem.setBranchQuantity(BigDecimal.ZERO);
		}
		/**箱数*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getBoxQuantity())){
			moveLibraryItem.setBoxQuantity(BigDecimal.ZERO);
		}
		/** 发出数量*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getIssueQuantity())){
			moveLibraryItem.setIssueQuantity(BigDecimal.ZERO);
		}
		/** 发出支数*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getIssueBranchQuantity())){
			moveLibraryItem.setIssueBranchQuantity(BigDecimal.ZERO);
		}
		/** 发出箱数*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getIssueBoxQuantity())){
			moveLibraryItem.setIssueBoxQuantity(BigDecimal.ZERO);
		}
		/** 接收数量*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getReceiveQuantity())){
			moveLibraryItem.setReceiveQuantity(BigDecimal.ZERO);
		}
		/** 接收支数*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getReceiveBranchQuantity())){
			moveLibraryItem.setReceiveBranchQuantity(BigDecimal.ZERO);
		}
		/** 接收箱数*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getReceiveBoxQuantity())){
			moveLibraryItem.setReceiveBoxQuantity(BigDecimal.ZERO);
		}
		/** 关闭数量*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getCloseQuantity())){
			moveLibraryItem.setCloseQuantity(BigDecimal.ZERO);
		}
		/**关闭支数*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getCloseBranchQuantity())){
			moveLibraryItem.setCloseBranchQuantity(BigDecimal.ZERO);
		}
		/** 关闭箱数*/
		if(ConvertUtil.isEmpty(moveLibraryItem.getCloseBoxQuantity())){
			moveLibraryItem.setCloseBoxQuantity(BigDecimal.ZERO);
		}
	}

    @Override
    public List<Map<String, Object>> findMovelibraryOperationList(Long id) {
        return moveLibraryDao.findMovelibraryOperationList(id);
    }
}
