package net.shopxx.order.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ServletContextAware;
import org.springframework.web.multipart.MultipartFile;

import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Ehcache;
import net.sf.ehcache.Element;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.PaymentMethod;
import net.shopxx.basic.entity.ShippingMethod;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.PaymentMethodBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.finance.entity.PolicyProduct;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.PolicyProductService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.finance.service.StoreCustomBalanceService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.CreditRechargeContractService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.dao.OrderDao;
import net.shopxx.order.dao.PriceApplyDao;
import net.shopxx.order.entity.Cart;
import net.shopxx.order.entity.CartItem;
import net.shopxx.order.entity.CartItemAttach;
import net.shopxx.order.entity.CartItemParts;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.entity.Order.PaymentStatus;
import net.shopxx.order.entity.Order.ShippingStatus;
import net.shopxx.order.entity.OrderAttach;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.OrderItemAttach;
import net.shopxx.order.entity.OrderItemParts;
import net.shopxx.order.entity.PriceApplyItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.purchase.service.ContractPriceService;
import net.shopxx.order.service.CartItemService;
import net.shopxx.order.service.CartService;
import net.shopxx.order.service.OrderCustomService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.PriceApplyItemService;
import net.shopxx.order.service.TriplicateFormService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductStore;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductStoreBaseService;
import net.shopxx.stock.entity.StockTaskBuilder;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseStore;
import net.shopxx.stock.service.StockService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.util.NumberToCN;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfProcBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

@Service("orderCustomServiceImpl")
public class OrderCustomServiceImpl extends WfBillBaseServiceImpl<Order>
		implements OrderCustomService, ServletContextAware {

	@Resource(name = "orderDao")
	private OrderDao orderDao;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseService;
	@Resource(name = "stockServiceImpl")
	private StockService stockService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;
	@Resource(name = "cartServiceImpl")
	private CartService cartService;
	@Resource(name = "cartItemServiceImpl")
	private CartItemService cartItemService;
	@Resource(name = "lockDataDao")
	private LockDataDao lockDataDao;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "productStoreBaseServiceImpl")
	private ProductStoreBaseService productStoreService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "paymentMethodBaseServiceImpl")
	private PaymentMethodBaseService paymentMethodBaseService;
	@Resource(name = "priceApplyItemServiceImpl")
	private PriceApplyItemService priceApplyItemService;
	// @Resource(name = "productSuiteServiceImpl")
	// private ProductSuiteService productSuiteService;
	@Resource(name = "priceApplyDao")
	private PriceApplyDao priceApplyDao;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "triplicateFormServiceImpl")
	private TriplicateFormService triplicateFormService;
	@Resource(name = "contractPriceServiceImpl")
	private ContractPriceService contractPriceService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	// @Resource(name = "intfParameterServiceImpl")
	// private IntfParameterService intfParameterService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "policyProductServiceImpl")
	private PolicyProductService policyProductService;
	@Resource(name = "ehCacheManager")
	private CacheManager cacheManager;
	@Resource(name = "creditRechargeContractServiceImpl")
	private CreditRechargeContractService creditRechargeContractService;
	@Resource(name = "storeCustomBalanceServiceImpl")
	private StoreCustomBalanceService storeCustomBalanceService;
	@Resource(name = "wfProcBaseServiceImpl")
	private WfProcBaseService wfProcBaseService;
	/** servletContext */
	private ServletContext servletContext;

	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}

	// 定制 只查正式订单
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPageCustom(String orderSn,
			String outTradeNo, Integer[] orderStatus, Integer[] shippingStatus,
			Long warehouseId, Long[] storeId, String consignee, String phone,
			String address, Long deliveryCorpId, Long[] productId,
			Integer[] paymentStatus, Integer[] flag, Integer orderType,
			String firstTime, String lastTime, Integer[] confirmStatus,
			Integer isReturn, String storeMemberName, Integer saleOrgId,
			Pageable pageable) {

		return orderDao.findPageCustom(orderSn,
				outTradeNo,
				orderStatus,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				orderType,
				firstTime,
				lastTime,
				confirmStatus,
				isReturn,
				storeMemberName,
				saleOrgId,
				pageable);
	}

	public Page<Map<String, Object>> findPage(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] shippingStatus, Long warehouseId,
			Long[] storeId, String consignee, String phone, String address,
			Long deliveryCorpId, Long[] productId, Integer[] paymentStatus,
			Integer[] flag, Integer orderType, String firstTime,
			String lastTime, Integer[] confirmStatus, Integer isReturn,
			Pageable pageable, Integer[] orderCategory) {

		return orderDao.findPage(orderSn,
				outTradeNo,
				orderStatus,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				orderType,
				firstTime,
				lastTime,
				confirmStatus,
				isReturn,
				pageable,
				orderCategory,
				null,
				null);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findListById(String ids) {
		return orderDao.findListById(ids);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findOrderItemListByOrderId(
			String orderIds) {
		return orderDao.findOrderItemListByOrderId(orderIds,null,null,null);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findOrderItemListByItemId(String itemIds) {
		return orderDao.findOrderItemListByItemId(itemIds);
	}

	/**
	 * 根据parent查找订单明细
	 * 
	 * @param parent
	 * @return
	 */
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findOrderItemListParent(Long parent) {
		return orderDao.findOrderItemListParent(parent);
	}

	public void check(Long orderId, List<OrderAttach> orderAttachs) {

		// 锁定订单，防止并发操作
		lockDataDao.lockOrder(orderId.toString());

		Order order = find(orderId);
		// 锁客户
		Store store = order.getStore();
		lockDataDao.lockStore(store.getId().toString());

		// 是否启用锁库存模式：0 否，1 是
		int useLockStock = Integer.parseInt(SystemConfig
				.getConfig("useLockStock", WebUtils.getCurrentCompanyInfoId()));
		List<OrderItem> orderItems = order.getOrderItems();
		if (useLockStock == 1) {
			// 锁库存
			String productIds = "";
			for (OrderItem orderItem : orderItems) {
				Product product = orderItem.getProduct();
				if (product != null) {
					Long productId = product.getId();
					productIds += "," + productId;
					// if (productSuiteService.existsProductSuite(productId)) {
					// List<Map<String, Object>> items =
					// productSuiteService.findAllItemListByProductId(productId);
					// for (Map<String, Object> item : items) {
					// Long pid = Long.parseLong(item.get("product_id")
					// .toString());
					// productIds += "," + pid;
					// }
					// }
				}
			}
			if (!ConvertUtil.isEmpty(productIds)) {
				productIds = productIds.substring(1);
				lockDataDao.lockStockByProduct(productIds);
			}
		}

		StoreMember storeMember = storeMemberService.getCurrent();
		// 附件
		if (orderAttachs != null) {
			for (Iterator<OrderAttach> iterator = orderAttachs
					.iterator(); iterator.hasNext();) {
				OrderAttach orderAttach = iterator.next();
				if (orderAttach == null || orderAttach.getUrl() == null) {
					iterator.remove();
					continue;
				}
				if (orderAttach.getName() == null) {
					ExceptionUtil.throwServiceException("附件名不能为空");
				}
				orderAttach.setFileName(
						orderAttach.getName() + "." + orderAttach.getSuffix());
				orderAttach.setOrder(order);
				orderAttach.setStoreMember(storeMember);
			}
			order.getOrderAttachs().clear();
			order.getOrderAttachs().addAll(orderAttachs);
		}

		if (!order.getOrderStatus().equals(Order.OrderStatus.unaudited)) {
			// 只允许未审核状态的订单进行审核操作
			ExceptionUtil.throwServiceException("15142");
		}
		if (order.getPaymentStatus().equals(Order.PaymentStatus.unpaid)) {

			// 订单状态改为已支付
			BigDecimal amount = order.getAmount();
			order.setPaymentStatus(Order.PaymentStatus.paid);
			order.setAmountPaid(amount);
			// BigDecimal balance =
			// storeBalanceService.findBalance(store.getId());
			// if (store.getIsReduceBalance() != null
			// && store.getIsReduceBalance()) {
			// if (balance.compareTo(order.getAmount()) == -1) {
			// //客户余额不足
			// ExceptionUtil.throwServiceException("15201");
			// }
			// }
		}

		//TCL定制订单审核时候判断销售回款是否大于等于定金金额
		if (order.getPaymentType() != null
				&& order.getPaymentType().compareTo(BigDecimal.ZERO) == 1) {
		
			BigDecimal storeBalance = storeCustomBalanceService
					.findBalance(store.getId());
			BigDecimal storeCredit = storeCustomBalanceService
					.findCreditBalance(store.getId());
	
			BigDecimal sumAmount = BigDecimal.ZERO;
			for (OrderItem orderItem : orderItems) {
				sumAmount = sumAmount.add(
						orderItem.getPrice().multiply(orderItem.getQuantity()));
			}
			
			if ((storeBalance.subtract(storeCredit))
					.compareTo(sumAmount.multiply(order.getPaymentType()
							.divide(new BigDecimal(100)))) == -1) {

				ExceptionUtil.throwServiceException("客户余额不足<br/>客户销售回款余额￥"
						+ (storeBalance.subtract(storeCredit)).setScale(2,
								BigDecimal.ROUND_HALF_UP)
						+ "，定金金额￥"
						+ sumAmount.multiply(order.getPaymentType()
								.divide(new BigDecimal(100))));
			}
		}
		order.setOrderStatus(OrderStatus.audited);
		order.setCheckStoreMember(storeMember);
		order.setCheckDate(new Date());
		//order.setSaleOrg(saleOrgService.findByStoreMember(storeMember.getId()));
		update(order);

		// 全链路
		List<String> orderSns = new ArrayList<String>();
		orderSns.add(order.getSn());
		orderFullLinkService.addFullLink(1,
				orderSns,
				order.getSn(),
				"订单审核",
				null);

		if (useLockStock == 1) {
			for (OrderItem orderItem : orderItems) {
				Product product = orderItem.getProduct();
				if (product != null) {
					Long productId = product.getId();
					String productName = product.getName();
					BigDecimal quantity = orderItem.getQuantity();
					Map<String, Object> stockMap = null;
					// if (productSuiteService.existsProductSuite(productId)) {
					// stockMap =
					// productSuiteService.getProductSuiteStock(productId);
					// }
					// else {
					// }
					stockMap = stockService.findLockStock(productId);
					BigDecimal lock_stock = BigDecimal.ZERO;
					BigDecimal useable_stock = BigDecimal.ZERO;
					if (stockMap != null) {
						lock_stock = new BigDecimal(
								stockMap.get("lock_stock").toString());
						useable_stock = new BigDecimal(
								stockMap.get("useable_stock").toString());
					}
					if (quantity.compareTo(useable_stock) == 1) {
						// 系统目前已审核未发货的产品[{0}]数量有[{1}] ,
						// 可售库存[{2}]，当前订单下单数[{3}]，库存不足，不允许审核！
						ExceptionUtil.throwServiceException("15181",
								productName,
								lock_stock.stripTrailingZeros(),
								useable_stock.stripTrailingZeros(),
								quantity.stripTrailingZeros());
					}
				}
			}
		}

		// 订单审核是否生成采购单：0 否，1 是
		int check2PurOrder = 0;
		try {
			check2PurOrder = Integer.parseInt(SystemConfig.getConfig(
					"check2PurOrder", WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		if (check2PurOrder == 1) {
			// 转采购单
			change2PurOrder(order);
		}
		// 尚高生成pdf
		/*
		 * if ("19".equals(order.getCompanyInfoId().toString()) ||
		 * "1".equals(order.getCompanyInfoId().toString())) {
		 */

		createPdf(order);

	}

	@Override
	@Transactional(readOnly = true)
	public Order build(Store store, Cart cart, List<CartItem> cartItems) {

		StoreMember storeMember = storeMemberService.getCurrent();

		Order order = new Order();
		List<OrderItem> orderItems = new ArrayList<OrderItem>();
		Store mainStore = storeService.getMainStore();
		List<Filter> filters = new ArrayList<Filter>();
		for (CartItem cartItem : cartItems) {
			OrderItem orderItem = new OrderItem();
			Long pid = cartItem.getProduct().getId();
			BigDecimal quantity = cartItem.getQuantity();
			BigDecimal price = cartItem.getPrice();
			Product product = productService.find(pid);
			filters.clear();
			filters.add(Filter.eq("product", product));
			filters.add(Filter.eq("store", mainStore));
			ProductStore productStore = productStoreService.find(filters);
			orderItem.setProductStore(productStore);
			orderItem.setOrder(order);
			orderItem.setQuantity(quantity);
			orderItem.setShippedQuantity(BigDecimal.ZERO);
			orderItem.setReturnQuantity(BigDecimal.ZERO);
			orderItem.setProduct(product);
			orderItem.setSn(product.getSn());
			orderItem.setVonderCode(product.getVonderCode());
			orderItem.setModel(product.getModel());
			orderItem.setThumbnail(product.getThumbnail());
			orderItem.setName(product.getName());
			orderItem.setFullName(product.getFullName());
			orderItem.setBarCode(product.getBarCode());
			orderItem.setPrice(price);
			orderItem.setOrigMemberPrice(price);
			orderItem.setOrigPrice(product.getPrice());
			orderItem.setSn(product.getSn());

			// 订单项配件关联
			List<CartItemParts> cartItemPartses = cartItem.getCartItemPartses();
			List<OrderItemParts> orderItemPartses = new ArrayList<OrderItemParts>();
			if (!cartItemPartses.isEmpty()) {
				for (CartItemParts cartItemParts : cartItemPartses) {
					OrderItemParts orderItemParts = new OrderItemParts();
					orderItemParts.setParts(cartItemParts.getParts());
					orderItemParts.setPartsGroup(cartItemParts.getPartsGroup());
					orderItemParts.setIsSelected(cartItemParts.getIsSelected());
					orderItemParts.setName(cartItemParts.getName());
					orderItemParts.setOrderItem(orderItem);
					orderItemPartses.add(orderItemParts);
				}
			}
			orderItem.setOrderItemPartses(orderItemPartses);

			// 订单项附件
			List<CartItemAttach> cartItemAttachs = cartItem
					.getCartItemAttachs();
			List<OrderItemAttach> orderItemAttachs = new ArrayList<OrderItemAttach>();
			if (!cartItemAttachs.isEmpty()) {
				for (CartItemAttach cartItemAttach : cartItemAttachs) {
					OrderItemAttach orderItemAttach = new OrderItemAttach();
					orderItemAttach.setUrl(cartItemAttach.getUrl());
					orderItemAttach.setMemo(cartItemAttach.getMemo());
					orderItemAttach.setSeq(cartItemAttach.getSeq());
					orderItemAttach.setOrderItem(orderItem);
					orderItemAttachs.add(orderItemAttach);
				}
			}
			orderItem.setOrderItemAttachs(orderItemAttachs);

			orderItem.setDemand(cartItem.getDemand());
			orderItems.add(orderItem);
		}
		BigDecimal zero = BigDecimal.ZERO;
		order.setOrderStatus(Order.OrderStatus.unaudited);
		order.setOrderItems(orderItems);
		order.setStoreMember(storeMember);
		order.setStore(store == null ? cart.getStore() : store);
		order.setAmountPaid(zero);
		order.setCouponDiscount(zero);
		order.setFreight(zero);
		order.setOffsetAmount(zero);
		order.setOrderStatus(OrderStatus.unaudited);
		order.setPaymentStatus(PaymentStatus.unpaid);
		order.setShippingStatus(ShippingStatus.unshipped);
		order.setPromotionDiscount(zero);
		order.setShippingStatus(ShippingStatus.unshipped);
		order.setType("6");
		order.setOrderType(2);
		order.setIsMobile(false);

		return order;
	}

	public void checkOrderWf(Long orderId, List<OrderAttach> orderAttachs,
			Long objConfId) {

		// 锁定订单，防止并发操作
		lockDataDao.lockOrder(orderId.toString());

		Order order = find(orderId);
		// 是否启用锁库存模式：0 否，1 是
		int useLockStock = Integer.parseInt(SystemConfig
				.getConfig("useLockStock", WebUtils.getCurrentCompanyInfoId()));
		List<OrderItem> orderItems = order.getOrderItems();
		if (useLockStock == 1) {
			// 锁库存
			String productIds = "";
			for (OrderItem orderItem : orderItems) {
				Product product = orderItem.getProduct();
				if (product != null) {
					Long productId = product.getId();
					productIds += "," + productId;
					// if (productSuiteService.existsProductSuite(productId)) {
					// List<Map<String, Object>> items =
					// productSuiteService.findAllItemListByProductId(productId);
					// for (Map<String, Object> item : items) {
					// Long pid = Long.parseLong(item.get("product_id")
					// .toString());
					// productIds += "," + pid;
					// }
					// }
				}
			}
			if (!ConvertUtil.isEmpty(productIds)) {
				productIds = productIds.substring(1);
				lockDataDao.lockStockByProduct(productIds);
			}
		}
		StoreMember storeMember = storeMemberService.getCurrent();
		// 附件
		for (Iterator<OrderAttach> iterator = orderAttachs.iterator(); iterator
				.hasNext();) {
			OrderAttach orderAttach = iterator.next();
			if (orderAttach == null || orderAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (orderAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			orderAttach.setFileName(
					orderAttach.getName() + "." + orderAttach.getSuffix());
			orderAttach.setOrder(order);
			orderAttach.setStoreMember(storeMember);
		}
		order.getOrderAttachs().clear();
		order.getOrderAttachs().addAll(orderAttachs);

		if (!order.getOrderStatus().equals(Order.OrderStatus.unaudited)) {
			// 只允许未审核状态的订单进行审核操作
			ExceptionUtil.throwServiceException("15142");
		}

		if (useLockStock == 1) {
			for (OrderItem orderItem : orderItems) {
				Product product = orderItem.getProduct();
				if (product != null) {
					Long productId = product.getId();
					String productName = product.getName();
					BigDecimal quantity = orderItem.getQuantity();
					Map<String, Object> stockMap = null;
					// if (productSuiteService.existsProductSuite(productId)) {
					// stockMap =
					// productSuiteService.getProductSuiteStock(productId);
					// }
					// else {
					// }
					stockMap = stockService.findLockStock(productId);
					BigDecimal lock_stock = BigDecimal.ZERO;
					BigDecimal useable_stock = BigDecimal.ZERO;
					if (stockMap != null) {
						lock_stock = new BigDecimal(
								stockMap.get("lock_stock").toString());
						useable_stock = new BigDecimal(
								stockMap.get("useable_stock").toString());
					}
					if (quantity.compareTo(useable_stock) == 1) {
						// 系统目前已审核未发货的产品[{0}]数量有[{1}] ,
						// 可售库存[{2}]，当前订单下单数[{3}]，库存不足，不允许审核！
						ExceptionUtil.throwServiceException("15181",
								productName,
								lock_stock.stripTrailingZeros(),
								useable_stock.stripTrailingZeros(),
								quantity.stripTrailingZeros());
					}
				}
			}
		}

		// 创建流程实例
		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService
				.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}

		order.setCheckStoreMember(storeMember);
		// order.setCheckDate(new Date());
		//order.setSaleOrg(saleOrgService.findByStoreMember(storeMember.getId()));
//		order.setByObjConfig(wfObjConfigLine); // 设置流程配置
//		wfBaseService.createwf(storeMemberService.getCurrent(),
//				order.getSaleOrg() == null ? null : order.getSaleOrg().getId(),
//				order.getSn(),
//				order.getWfTempId(),
//				order.getObjTypeId(),
//				order.getId());

		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());
		List<String> orderSns = new ArrayList<String>();
		orderSns.add(order.getSn());
		orderFullLinkService.addFullLink(1,
				orderSns,
				order.getSn(),
				ConvertUtil.convertI18nMsg("18701",
						new Object[] { wfTemp.getWfTempName() }),
				null);
	}

	@Override
	@Transactional(readOnly = true)
	public Integer countCreate(String orderSn, String outTradeNo, Long storeId,
			Long warehouseId, String sn, String name, String vonderCode,
			String model, Boolean isToShip, Integer[] orderStatus,
			Boolean isSuit, Long supplierId, Pageable pageable) {
		return orderDao.countCreate(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				vonderCode,
				model,
				isToShip,
				orderStatus,
				isSuit,
				supplierId,
				pageable,
				null,
				null,
				null,
				null,
				null);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findItemListCreate(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, String model, Boolean isToShip,
			Integer[] orderStatus, Boolean isSuit, Long supplierId, Long[] ids,
			Integer page, Integer size) {
		return orderDao.findItemListCreate(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				vonderCode,
				model,
				isToShip,
				orderStatus,
				isSuit,
				supplierId,
				ids,
				page,
				size);

	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findItemPage(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String consignee, String vonderCode, String model,
			Boolean isToShip, Integer[] orderStatus, Boolean isSuit,
			Long supplierId, Boolean isPurchase, Pageable pageable) {
		
		//2019-05-16 增加创建人，多机构查询公用的方法
		return orderDao.findItemPage(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				consignee,
				vonderCode,
				model,
				isToShip,
				orderStatus,
				isSuit,
				supplierId,
				null,
				null,
				isPurchase,
				pageable,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
                null);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findItemPageCustom(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, String model, Boolean isToShip,
			Integer[] orderStatus, Boolean isSuit, Long supplierId,
			Boolean isPurchase, Pageable pageable) {
		return orderDao.findItemPageCustom(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				vonderCode,
				model,
				isToShip,
				orderStatus,
				isSuit,
				supplierId,
				isPurchase,
				pageable);
	}

	public Page<Map<String, Object>> findItemPage(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, String model, Boolean isToShip,
			Integer[] orderStatus, Boolean isSuit, Long supplierId,
			Boolean isPurchase, Pageable pageable, Integer[] orderCategory) {
		return orderDao.findItemPage(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				vonderCode,
				model,
				isToShip,
				orderStatus,
				isSuit,
				supplierId,
				isPurchase,
				pageable,
				orderCategory);
	}

	@Override
	@Transactional
	public void setFlag(Long[] ids, Integer flag) {

		String idss = "";
		for (int i = 0; i < ids.length; i++) {
			if (i == ids.length - 1) {
				idss += ids[i];
			}
			else {
				idss += ids[i] + ",";
			}
		}
		orderDao.setFlag(idss, flag);
	}

	public void clearWarehouse(Long id) {

		Map<String, Object> orderMap = orderDao.findMapByItemId(id);
		Long orderId = Long.parseLong(orderMap.get("id").toString());

		// 锁定订单，防止并发操作
		lockDataDao.lockOrder(orderId.toString());

		Long itemId = Long.parseLong(orderMap.get("item_id").toString());
		Long warehouseId = orderMap.get("warehouse") == null ? null
				: Long.parseLong(orderMap.get("warehouse").toString());
		// Long stockId = orderMap.get("stock") == null ? null
		// : Long.parseLong(orderMap.get("stock").toString());
		// Long productId = Long.parseLong(orderMap.get("product").toString());
		// BigDecimal quantity = new BigDecimal(orderMap.get("quantity")
		// .toString());
		String productName = orderMap.get("full_name").toString();

		if (warehouseId != null) {
			getDaoCenter().getNativeDao().update(
					"update xx_order_item set warehouse=null,stock=null where id=?",
					new Object[] { itemId });
			// if (stockId != null) {
			// StockTaskBuilder taskBuilder = new StockTaskBuilder();
			// taskBuilder.unLockStock(productId,
			// warehouseId,
			// quantity,
			// "订单明细清除仓库");
			// stockService.handleStock(taskBuilder);
			// }
			// 全链路
			String orderSn = orderMap.get("sn").toString();
			List<String> orderSns = new ArrayList<String>();
			orderSns.add(orderSn);
			orderFullLinkService.addFullLink(1,
					orderSns,
					orderSn,
					"订单明细清除仓库，产品[" + productName + "]",
					null);
		}
	}

	// public void addChildrenItems(List<OrderItem> orderItems) {
	//
	// List<OrderItem> newOrderItems = new ArrayList<OrderItem>();
	// List<Filter> filters = new ArrayList<Filter>();
	// Store mainStore = storeService.getMainStore();
	// for (OrderItem orderItem : orderItems) {
	//
	// if (orderItem.getId() != null) continue;
	// Product pProduct = orderItem.getProduct();
	// if (pProduct == null) {//自己维护的其他产品，跳过
	// continue;
	// }
	// filters.clear();
	// filters.add(Filter.eq("product", pProduct));
	// filters.add(Filter.isNull("parent"));
	// List<Bom> boms = bomService.findList(1, filters, null);
	// Bom bom = null;
	// if (!boms.isEmpty()) {
	// bom = boms.get(0);
	// }
	// //没有维护套机信息，跳过
	// if (bom == null) continue;
	// orderItem.setBomFlag(1);
	// BigDecimal pQuantity = orderItem.getQuantity();
	// List<Bom> children = bom.getChildren();
	// for (Bom child : children) {
	// Product product = child.getProduct();
	// BigDecimal quantity = child.getQuantity();
	// filters.clear();
	// filters.add(Filter.eq("product", product));
	// filters.add(Filter.eq("store", mainStore));
	// ProductStore productStore = productstoreService.find(filters);
	// OrderItem item = new OrderItem();
	// item.setProductStore(productStore);
	// item.setQuantity(quantity.multiply(pQuantity));
	// item.setShippedQuantity(BigDecimal.ZERO);
	// item.setReturnQuantity(BigDecimal.ZERO);
	// item.setProduct(product);
	// item.setSn(product.getSn());
	// item.setVonderCode(product.getVonderCode());
	// item.setModel(product.getModel());
	// item.setThumbnail(product.getThumbnail());
	// item.setName(product.getName());
	// item.setFullName(product.getFullName());
	// item.setBarCode(product.getBarCode());
	// item.setPrice(BigDecimal.ZERO);
	// item.setOrigMemberPrice(BigDecimal.ZERO);
	// item.setOrigPrice(product.getPrice());
	// item.setSn(product.getSn());
	// item.setParent(orderItem);
	// item.setOrder(orderItem.getOrder());
	// if (child.getType() == 0) {
	// item.setBomFlag(2);
	// }
	// else {
	// item.setBomFlag(3);
	// }
	// newOrderItems.add(item);
	// }
	// }
	// if (!newOrderItems.isEmpty()) {
	// orderItems.addAll(newOrderItems);
	// }
	// }

	// 不限制可发货余额
	@Transactional
	public void saveCustom(Order order, Store store,
			ShippingMethod shippingMethod, PaymentMethod paymentMethod,
			Area area, Cart cart, List<CartItem> cartItems, Integer stat) {

		// 锁客户
		lockDataDao.lockStore(store.getId().toString());

		List<OrderItem> orderItems = order.getOrderItems();
		// 锁特价单
		// String applyItemIds = "";
		// for (OrderItem item : orderItems) {
		// if (item.getPriceApplySn() != null && item.getPriceApplyItem() !=
		// null) {
		// applyItemIds += "," + item.getPriceApplyItem().getId();
		// }
		// }
		// if (!ConvertUtil.isEmpty(applyItemIds)) {
		// applyItemIds = applyItemIds.substring(1);
		// priceApplyDao.lockPriceApplyItem(applyItemIds);
		// }
		Store mainStore = storeService.getMainStore();
		List<Filter> filters = new ArrayList<Filter>();
		for (Iterator<OrderItem> iterator = orderItems.iterator(); iterator
				.hasNext();) {
			OrderItem orderItem = iterator.next();
			Product p = orderItem.getProduct();
			if (orderItem == null
					|| ((p == null || p.getId() == null)
							&& orderItem.getName() == null)) {
				iterator.remove();
				continue;
			}
			String productName = orderItem.getName();
			BigDecimal price = orderItem.getPrice();
			if (p == null || p.getId() == null) {
				orderItem.setProduct(null);
				orderItem.setOrigPrice(price);
				orderItem.setFullName(productName);
			}
			else {
				Product product = productService
						.find(orderItem.getProduct().getId());
//				if(product.getMarketPrice()!=null && "".equals(product.getMarketPrice())){
//					BigDecimal cost = new BigDecimal(product.getCost().toString());
//					if (!cost.equals(BigDecimal.ZERO)){
//						ExceptionUtil.throwServiceException("请维护产品成本！");
//					}
//				}else if(product.getMarketPrice()==null && !"".equals(product.getMarketPrice())){
//					ExceptionUtil.throwServiceException("请维护产品成本！");
//				}
//				if(product.getCost()!=null && "".equals(product.getCost())){
//				BigDecimal marketPrice = new BigDecimal(product.getMarketPrice().toString());
//				if (marketPrice.compareTo(BigDecimal.ZERO)==0){
//					ExceptionUtil.throwServiceException("请维护产品底价！");
//				}
//				}else if(product.getCost()==null && !"".equals(product.getCost())){
//					ExceptionUtil.throwServiceException("请维护产品底价！");
//				}
				productName = product.getName();
				filters.clear();
				filters.add(Filter.eq("product", product));
				filters.add(Filter.eq("store", mainStore));
				ProductStore productStore = productStoreService.find(filters);
				orderItem.setProduct(product);
				orderItem.setProductStore(productStore);
				orderItem.setOrigPrice(product.getPrice());
				orderItem.setName(product.getName());
				orderItem.setFullName(product.getFullName());
				orderItem.setSn(product.getSn());
				orderItem.setThumbnail(product.getThumbnail());
				orderItem.setVonderCode(product.getVonderCode());
				orderItem.setModel(product.getModel());
				orderItem.setBarCode(product.getBarCode());
				orderItem.setDescription(product.getDescription());
				//
				// if (product != null
				// && productSuiteService.existsProductSuite(product.getId())) {
				// orderItem.setBomFlag(1);
				// }
				// else {
				// orderItem.setBomFlag(0);
				// }
			}
			orderItem.setApplyPrice(price);
			orderItem.setShippedQuantity(BigDecimal.ZERO);
			orderItem.setReturnQuantity(BigDecimal.ZERO);
			orderItem.setOrder(order);

			// 政策
			if (orderItem.getPolicyProduct() != null
					&& orderItem.getPolicyProduct().getId() != null) {
				PolicyProduct policyProduct = policyProductService
						.find(orderItem.getPolicyProduct().getId());
				if (policyProduct != null) {
					orderItem.setPolicyProduct(policyProduct);
					orderItem.setApplyType(
							policyProduct.getPolicy().getApplyType());
					orderItem.setPolicySn(policyProduct.getPolicy().getSn());
				}
				else {
					orderItem.setPolicyProduct(null);
					orderItem.setApplyType(null);
					orderItem.setPolicySn(null);
				}
			}

			// if (orderItem.getPriceApplySn() != null &&
			// orderItem.getPriceApplyItem() != null) {
			// PriceApplyItem priceApplyItem =
			// priceApplyItemService.find(orderItem.getPriceApplyItem().getId());
			// BigDecimal ableQuantity =
			// priceApplyItem.getQuantity().subtract(priceApplyItem.getUsedQuantity());
			// if (orderItem.getQuantity().intValue() > ableQuantity.intValue())
			// {
			// // 订单明细产品[{0}]下单数量[{1}]已超过特价申请可使用数量[{2}]
			// ExceptionUtil.throwServiceException("17553", productName,
			// orderItem.getQuantity(), ableQuantity);
			// }
			// orderItem.setPrice(orderItem.getOrigMemberPrice());
			//
			// priceApplyItem.setUsedQuantity(priceApplyItem.getUsedQuantity().add(orderItem.getQuantity()));
			// priceApplyItemService.update(priceApplyItem);
			//
			// } else {
			// orderItem.setOrigMemberPrice(price);
			// }
			if (orderItem.getPriceApplyItem() != null
					&& orderItem.getPriceApplyItem().getId() != null) {
				PriceApplyItem priceApplyItem = priceApplyItemService
						.find(orderItem.getPriceApplyItem().getId());
				if (priceApplyItem != null) {
					orderItem.setPriceApplyItem(priceApplyItem);
					orderItem.setPriceApplySn(
							priceApplyItem.getPriceApply().getSn());

				}
				else {
					orderItem.setPriceApplyItem(null);
					orderItem.setPriceApplySn(null);

				}
			}
		}
		if (orderItems.isEmpty()) {
			// 请选择下单产品
			ExceptionUtil.throwServiceException("17105");
		}

		/*
		 * int useTclBalanceIntf = 0; try { useTclBalanceIntf = Integer
		 * .parseInt(SystemConfig.getConfig("useTclBalanceIntf",
		 * WebUtils.getCurrentCompanyInfoId())); } catch (Exception e) { }
		 * BigDecimal balance = null; if (store.getIsReduceBalance() != null &&
		 * store.getIsReduceBalance()) { if (stat != null && stat == 1) { //
		 * 客户余额不足 balance = storeBalanceService.findBalance(store.getId()); } }
		 * if (balance != null && balance.compareTo(order.getAmount()) == -1) {
		 * //// 客户余额不足 BigDecimal subtract =
		 * balance.subtract(order.getAmount()).multiply(new
		 * BigDecimal("-1")).setScale(2, BigDecimal.ROUND_HALF_UP);
		 * ExceptionUtil.throwServiceException("客户余额不足<br/>客户可发货余额￥" +
		 * balance.setScale(2, BigDecimal.ROUND_HALF_UP) + "，订单总金额￥" +
		 * order.getAmount() + "，还差￥" + subtract + ""); }
		 */

		BigDecimal zero = BigDecimal.ZERO;
		order.setOrderItems(orderItems);
		order.setStore(store);
		order.setArea(area);
		order.setStoreMember(storeMemberService.getCurrent());
		order.setOrderCategory(4);
		order.setPaymentMethodName("在线支付");
		String sn = SnUtil.getOrderSn();
		order.setSn(sn);
		order.setOutTradeNo(sn);
		order.setAmountPaid(zero);
		order.setCouponDiscount(zero);
		order.setFreight(zero);
		order.setOffsetAmount(zero);
		order.setPaymentMethod(paymentMethod);
		order.setShippingMethod(shippingMethod);
		order.setPaymentStatus(PaymentStatus.unpaid);
		order.setShippingStatus(ShippingStatus.unshipped);
		order.setPromotionDiscount(zero);
		order.setShippingStatus(ShippingStatus.unshipped);
		order.setType("6");
		order.setOrderType(2);
		order.setIsMobile(false);
		Date now = new Date();
		order.setCreateDate(now);
		order.setOrderDate(now);
		order.setConfirmStatus(0);
		// 附件
		List<OrderAttach> orderAttachs = order.getOrderAttachs();
		for (Iterator<OrderAttach> iterator = orderAttachs.iterator(); iterator
				.hasNext();) {
			OrderAttach orderAttach = iterator.next();
			if (orderAttach == null || orderAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (orderAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			orderAttach.setFileName(
					orderAttach.getName() + "." + orderAttach.getSuffix());
			orderAttach.setOrder(order);
			orderAttach.setStoreMember(storeMemberService.getCurrent());
		}
		order.setOrderAttachs(orderAttachs);

		if (stat != null && stat == 1) {// flag 0保存 1保存并下达
			order.setOrderStatus(Order.OrderStatus.unaudited);
		}
		else {
			order.setOrderStatus(Order.OrderStatus.saved);
		}

		// order.setSaleOrg(store.getSaleOrg()); // 客户所属的机构

		save(order);

		List<String> orderSns = new ArrayList<String>();
		orderSns.add(order.getSn());
		orderFullLinkService.addFullLink(1,
				orderSns,
				order.getSn(),
				"手工下达订单",
				null);

		// 生成订单后删除购物车项
		for (CartItem cartItem : cartItems) {
			cartItemService.delete(cartItem);
		}
		// 购物车项为空则删除购物车
		if (cart != null && cart.getCartItems().isEmpty()) {
			cartService.delete(cart);
		}

		// 是否启用锁库存模式：0 否，1 是
		// Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		// int useLockStock =
		// Integer.parseInt(SystemConfig.getConfig("useLockStock",
		// companyInfoId));
		// if (useLockStock == 1
		// && (companyInfoId.intValue() == 1 || companyInfoId.intValue() == 18))
		// {
		// //自动分仓
		// autoSetWarehouse(order);
		// }
		/** 下达写订单接口表 */
		if (stat != null && stat == 1) {
			this.saveOrderIntfAtXd(order, order.getCompanyInfoId());
		}
	}

	// 定制不限制可发货余额
	@Override
	@Transactional
	public void updateCustom(Order order, ShippingMethod shippingMethod,
			Area area, Long[] delOrderItemId, SystemDict freightChargeType,
			SystemDict businessType, Integer stat, Long organizationId) {

		// 锁定订单，防止并发操作
		Long orderId = order.getId();
		lockDataDao.lockOrder(orderId.toString());

		Order pOrder = find(order.getId());
		// 锁客户
		Store store = pOrder.getStore();
		lockDataDao.lockStore(store.getId().toString());

		BigDecimal pAmount = pOrder.getAmount();

		// 删除了明细解锁库存
		// if (delOrderItemId != null) {
		// StockTaskBuilder taskBuilder = new StockTaskBuilder();
		// for (Long itemId : delOrderItemId) {
		// OrderItem orderItem = orderItemService.find(itemId);
		// Long warehouseId = orderItem.getWarehouse() == null ? null
		// : orderItem.getWarehouse().getId();
		// Long stockId = orderItem.getStock() == null ? null
		// : orderItem.getStock().getId();
		// Product product = orderItem.getProduct();
		// if (warehouseId != null && stockId != null) {
		// taskBuilder.unLockStock(product.getId(),
		// warehouseId,
		// orderItem.getQuantity(),
		// "订单更新删除明细");
		// stockService.handleStock(taskBuilder);
		// //全链路
		// orderFullLinkService.addFullLink(1,
		// pOrder.getSn(),
		// pOrder.getSn(),
		// "订单更新删除明细，产品[" + product.getName() + "]",
		// null);
		// }
		// }
		// stockService.handleStock(taskBuilder);
		// }

		StringBuilder msg = new StringBuilder();
		StringBuilder msg2 = new StringBuilder();
		msg.append("订单更新操作，订单号：" + pOrder.getSn());
		msg.append("，更新前：收货地区："
				+ (pOrder.getArea() == null ? ""
						: pOrder.getArea().getFullName()));
		msg.append("，收货地址：" + pOrder.getAddress());
		msg.append("，收货人：" + pOrder.getConsignee());
		msg.append("，收货人电话：" + pOrder.getPhone());
		msg.append("，手机号：" + pOrder.getMobile());
		msg.append("，邮编：" + pOrder.getZipCode());
		msg.append("，配送方式："
				+ (pOrder.getShippingMethod() == null ? ""
						: pOrder.getShippingMethod().getName()));

		msg2.append("，更新后：收货地区：" + area.getFullName());
		msg2.append("，收货地址：" + order.getAddress());
		msg2.append("，收货人：" + order.getConsignee());
		msg2.append("，收货人电话：" + order.getPhone());
		msg2.append("，手机号：" + order.getMobile());
		msg2.append("，邮编：" + order.getZipCode());
		msg.append("，配送方式："
				+ (pOrder.getShippingMethod() == null ? ""
						: pOrder.getShippingMethod().getName()));

		msg.append("；订单明细：");
		for (OrderItem orderItem : pOrder.getOrderItems()) {
			msg.append("，产品编号：" + orderItem.getSn());
			msg.append("，价格：" + orderItem.getPrice());
			msg.append("，数量：" + orderItem.getQuantity());
		}

		Store mainStore = storeService.getMainStore();
		List<Filter> filters = new ArrayList<Filter>();
		List<OrderItem> orderItems = order.getOrderItems();
		msg2.append("；订单明细：");
		for (Iterator<OrderItem> iterator = orderItems.iterator(); iterator
				.hasNext();) {
			OrderItem orderItem = iterator.next();
			Product p = orderItem.getProduct();
			if (orderItem == null
					|| ((p == null || p.getId() == null)
							&& orderItem.getName() == null)) {
				iterator.remove();
				continue;
			}
			BigDecimal price = orderItem.getPrice();
			OrderItem pOrderItem = orderItemService.find(orderItem.getId());
			if (p == null || p.getId() == null) {
				orderItem.setProduct(null);
				orderItem.setOrigPrice(price);
				orderItem.setFullName(orderItem.getName());
			}
			else {

				Product product = productService
						.find(orderItem.getProduct().getId());

//				if(product.getMarketPrice()!=null && "".equals(product.getMarketPrice())){
//					BigDecimal cost = new BigDecimal(product.getCost().toString());
//					if (!cost.equals(BigDecimal.ZERO)){
//						ExceptionUtil.throwServiceException("请维护产品成本！");
//					}
//				}else if(product.getMarketPrice()==null && !"".equals(product.getMarketPrice())){
//					ExceptionUtil.throwServiceException("请维护产品成本！");
//				}
//				if(product.getCost()!=null && "".equals(product.getCost())){
//				BigDecimal marketPrice = new BigDecimal(product.getMarketPrice().toString());
//				if (marketPrice.compareTo(BigDecimal.ZERO)==0){
//					ExceptionUtil.throwServiceException("请维护产品底价！");
//				}
//				}else if(product.getCost()==null && !"".equals(product.getCost())){
//					ExceptionUtil.throwServiceException("请维护产品底价！");
//				}
				filters.clear();
				filters.add(Filter.eq("product", product));
				filters.add(Filter.eq("store", mainStore));
				ProductStore productStore = productStoreService.find(filters);
				orderItem.setProduct(product);
				orderItem.setProductStore(productStore);
				orderItem.setOrigPrice(product.getPrice());
				// orderItem.setName(product.getName());
				orderItem.setFullName(product.getFullName());
				orderItem.setSn(product.getSn());
				orderItem.setThumbnail(product.getThumbnail());
				// orderItem.setVonderCode(product.getVonderCode());
				// orderItem.setModel(product.getModel());
				orderItem.setBarCode(product.getBarCode());
				orderItem.setDescription(product.getDescription());

				// if (product != null
				// && productSuiteService.existsProductSuite(product.getId())) {
				// orderItem.setBomFlag(1);
				// }
				// else {
				// orderItem.setBomFlag(0);
				// }
			}
			orderItem.setOrigMemberPrice(price);
			orderItem.setApplyPrice(price);
			orderItem.setShippedQuantity(BigDecimal.ZERO);
			orderItem.setReturnQuantity(BigDecimal.ZERO);
			orderItem.setBomFlag(0);

			// 政策
			if (orderItem.getPolicyProduct() != null
					&& orderItem.getPolicyProduct().getId() != null) {
				PolicyProduct policyProduct = policyProductService
						.find(orderItem.getPolicyProduct().getId());
				if (policyProduct != null) {
					orderItem.setPolicyProduct(policyProduct);
					orderItem.setApplyType(
							policyProduct.getPolicy().getApplyType());
					orderItem.setPolicySn(policyProduct.getPolicy().getSn());
				}
				else {
					orderItem.setPolicyProduct(null);
					orderItem.setApplyType(null);
					orderItem.setPolicySn(null);
				}
			}

			if (orderItem.getPriceApplyItem() != null
					&& orderItem.getPriceApplyItem().getId() != null) {
				PriceApplyItem priceApplyItem = priceApplyItemService
						.find(orderItem.getPriceApplyItem().getId());
				if (priceApplyItem != null) {
					orderItem.setPriceApplyItem(priceApplyItem);
					orderItem.setPriceApplySn(
							priceApplyItem.getPriceApply().getSn());

				}
				else {
					orderItem.setPriceApplyItem(null);
					orderItem.setPriceApplySn(null);

				}
			}

			if (pOrderItem != null) {
				orderItem.setWarehouse(pOrderItem.getWarehouse());
				orderItem.setStock(pOrderItem.getStock());
				orderItem.setBomFlag(pOrderItem.getBomFlag());
				orderItem.setParent(pOrderItem.getParent());
				orderItem.setPriceApplyItem(pOrderItem.getPriceApplyItem());
				orderItem.setPriceApplySn(pOrderItem.getPriceApplySn());
				// orderItem.setDeliveryTime(pOrderItem.getDeliveryTime());
			}
			orderItem.setOrder(pOrder);

			msg2.append("，产品编号：" + orderItem.getSn());
			msg2.append("，价格：" + orderItem.getPrice());
			msg2.append("，数量：" + orderItem.getQuantity());
		}
		if (orderItems.isEmpty()) {
			// 请选择下单产品
			ExceptionUtil.throwServiceException("17105");
		}

		/*
		 * int useTclBalanceIntf = 0; try { useTclBalanceIntf = Integer
		 * .parseInt(SystemConfig.getConfig("useTclBalanceIntf",
		 * WebUtils.getCurrentCompanyInfoId())); } catch (Exception e) { }
		 * BigDecimal balance = null; if (store.getIsReduceBalance() != null &&
		 * store.getIsReduceBalance()) { if (stat != null && stat == 1) {
		 * balance = storeBalanceService.findBalance(store.getId()); } } if
		 * (balance != null) { balance = balance.add(pAmount); } BigDecimal
		 * newAmount = order.getAmount(); //if (newAmount.compareTo(pAmount) ==
		 * 1) { if (balance != null && balance.compareTo(newAmount) == -1) {
		 * //// 客户余额不足 BigDecimal subtract =
		 * balance.subtract(newAmount).multiply(new
		 * BigDecimal("-1")).setScale(2, BigDecimal.ROUND_HALF_UP);
		 * ExceptionUtil .throwServiceException("客户余额不足<br/>客户可发货余额￥" +
		 * balance.setScale(2, BigDecimal.ROUND_HALF_UP) + "，订单总金额￥" + newAmount
		 * + "，还差￥" + subtract + ""); }
		 */
		// }

		pOrder.getOrderItems().clear();
		pOrder.getOrderItems().addAll(orderItems);

		// 附件
		List<OrderAttach> orderAttachs = order.getOrderAttachs();
		for (Iterator<OrderAttach> iterator = orderAttachs.iterator(); iterator
				.hasNext();) {
			OrderAttach orderAttach = iterator.next();
			if (orderAttach == null || orderAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (orderAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			orderAttach.setFileName(
					orderAttach.getName() + "." + orderAttach.getSuffix());
			orderAttach.setOrder(pOrder);
			orderAttach.setStoreMember(storeMemberService.getCurrent());
		}
		pOrder.getOrderAttachs().clear();
		pOrder.getOrderAttachs().addAll(orderAttachs);

		pOrder.setArea(area);
		pOrder.setAddress(order.getAddress());
		pOrder.setConsignee(order.getConsignee());
		pOrder.setPhone(order.getPhone());
		pOrder.setMobile(order.getMobile());
		pOrder.setZipCode(order.getZipCode());
		pOrder.setAddressOutTradeNo(order.getAddressOutTradeNo());
		pOrder.setMemo(order.getMemo());
		pOrder.setShippingMethod(shippingMethod);
		pOrder.setFreightChargeType(freightChargeType);
		pOrder.setBusinessType(businessType);
		pOrder.setManager(order.getManager());
		pOrder.setUsePolicyPrice(order.getUsePolicyPrice());
		pOrder.setLowAmount(order.getLowAmount());
		pOrder.setPolicyAmount(order.getPolicyAmount());
		pOrder.setVolume(order.getVolume());
		pOrder.setEarnPercent(order.getEarnPercent());
		pOrder.setCostAmount(order.getCostAmount());
		pOrder.setCardId(order.getCardId());
		// 组织
		Organization organization = organizationService.find(organizationId);
		pOrder.setOrganization(organization);

		// 是否写订单接口表
		boolean isWriteOrderIntf = false;

		if (pOrder.getOrderStatus().equals(Order.OrderStatus.unaudited)) {
			pOrder.setOrderStatus(Order.OrderStatus.unaudited);
		}
		else {
			if (stat != null && stat == 1) {// flag 0保存 1保存并下达
				isWriteOrderIntf = true;
				pOrder.setOrderStatus(Order.OrderStatus.unaudited);
				pOrder.setPaymentStatus(Order.PaymentStatus.paid);
			}
			else {
				pOrder.setOrderStatus(Order.OrderStatus.saved);
			}
		}
		update(pOrder);

		LogUtils.info(msg.toString() + msg2.toString());

		List<String> orderSns = new ArrayList<String>();
		orderSns.add(pOrder.getSn());
		orderFullLinkService.addFullLink(1,
				orderSns,
				pOrder.getSn(),
				"修改订单信息",
				null);
		// 下达写订单接口表
		if (isWriteOrderIntf) {
			this.saveOrderIntfAtXd(pOrder, pOrder.getCompanyInfoId());
		}
	}

	@Override
	@Transactional
	public void cancelOrder(Long[] ids) {
		String idss = "";
		for (int i = 0; i < ids.length; i++) {
			if (i == ids.length - 1) {
				idss += ids[i];
			}
			else {
				idss += ids[i] + ",";
			}
		}
		// 锁定订单，防止并发操作
		lockDataDao.lockOrder(idss);

		// 锁特价单
		String applyItemIds = "";
		List<Map<String, Object>> applys = priceApplyDao.findItemByOrders(idss);
		for (Map<String, Object> apply : applys) {
			String price_apply_item = apply.get("price_apply_item").toString();
			applyItemIds += "," + price_apply_item;
		}
		if (!ConvertUtil.isEmpty(applyItemIds)) {
			applyItemIds = applyItemIds.substring(1);
			priceApplyDao.lockPriceApplyItem(applyItemIds);
		}

		StockTaskBuilder taskBuilder = new StockTaskBuilder();

		for (int i = 0; i < ids.length; i++) {
			Order order = find(ids[i]);
			// String orderSn = order.getSn();
			List<OrderItem> orderItems = order.getOrderItems();
			for (OrderItem orderItem : orderItems) {
				// 解锁库存
				// Stock stock = orderItem.getStock();
				// if (stock != null) {
				// Long wId = orderItem.getWarehouse().getId();
				// Product product = orderItem.getProduct();
				// Long productId = product.getId();
				// BigDecimal quantity = orderItem.getQuantity();
				// taskBuilder.unLockStock(productId, wId, quantity, "订单["
				// + orderSn
				// + "]作废，解锁原仓库库存");
				// //全链路
				// orderFullLinkService.addFullLink(1,
				// orderSn,
				// orderSn,
				// "订单作废解锁原仓库，产品[" + product.getFullName() + "]",
				// null);
				// }
				// 还回特价单明细数量
				PriceApplyItem applyItem = orderItem.getPriceApplyItem();
				if (applyItem != null) {
					BigDecimal oQuantity = orderItem.getQuantity();
					applyItem.setUsedQuantity(
							applyItem.getUsedQuantity().subtract(oQuantity));
					priceApplyItemService.update(applyItem);
				}
			}
			order.setOrderStatus(OrderStatus.cancelled);
			order.setCencalStoreMember(storeMemberService.getCurrent());
			update(order);

			List<String> orderSns = new ArrayList<String>();
			orderSns.add(order.getSn());
			orderFullLinkService.addFullLink(1,
					orderSns,
					order.getSn(),
					"作废订单",
					null);
		}
		// 处理库存
		stockService.handleStock(taskBuilder);

	}

	@Override
	@Transactional(readOnly = true)
	public BigDecimal findAmountPaid(Long storeId, Long saleOrgId) {
		return orderDao.findAmountPaid(storeId, saleOrgId);
	}

	/**
	 * 导入订单
	 */
	public String orderImport(MultipartFile multipartFile) throws Exception {

		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}

		String outTradeNo;// 来源单号
		String storeName;// 客户
		String consignee; // 收货人
		String phone; // 电话
		// String areaName;//地区全称(省市区)
		String address; // 地址
		String shippingMethodName; // 配送方式
		String paymentMethodName; // 付款方式
		String vonderCode; // 产品编号
		String quantityStr; // 数量
		String priceStr;// 产品价格
		String memo;// 备注
		String freightChargeTypeName;// 运费承担类型

		List<Filter> filters = new ArrayList<Filter>();
		for (int i = 1; i < rows; i++) {

			filters.clear();

			// 来源单号
			cell = sheet.getCell(0, i);
			outTradeNo = cell.getContents().trim();
			if (StringUtils.isEmpty(outTradeNo)) {
				throw new RuntimeException("第" + (i + 1) + "行,来源单号为空");
			}

			// 客户
			cell = sheet.getCell(1, i);
			storeName = cell.getContents().trim();
			if (StringUtils.isEmpty(storeName)) {
				throw new RuntimeException("第" + (i + 1) + "行,客户为空");
			}
			filters.add(Filter.eq("name", storeName));
			List<Store> stores = storeService.findList(null, filters, null);
			if (stores == null || stores.size() < 1) {
				throw new RuntimeException(
						"第" + (i + 1) + "行,客户[" + storeName + "]不存在");
			}
			Store store = stores.get(0);

			// 收货人
			cell = sheet.getCell(2, i);
			consignee = cell.getContents().trim();
			if (StringUtils.isEmpty(consignee)) {
				throw new RuntimeException("第" + (i + 1) + "行,收货人为空");
			}

			// 电话
			cell = sheet.getCell(3, i);
			phone = cell.getContents().trim();
			if (StringUtils.isEmpty(phone)) {
				throw new RuntimeException("第" + (i + 1) + "行,电话为空");
			}

			// 地区
			// cell = sheet.getCell(4, i);
			// areaName = cell.getContents().trim();
			// Area area = null;
			// if (StringUtils.isNotEmpty(areaName)) {
			// area = areaBaseService.findAreaByFullName(areaName);
			// if (area == null) {
			// throw new RuntimeException(
			// "第" + (i + 1) + "行,在系统中找不到地区[" + areaName + "]");
			// }
			// }

			// 地址
			cell = sheet.getCell(4, i);
			address = cell.getContents().trim();
			if (StringUtils.isEmpty(address)) {
				throw new RuntimeException("第" + (i + 1) + "行,地址为空");
			}

			// 配送方式
			cell = sheet.getCell(5, i);
			shippingMethodName = cell.getContents().trim();
			ShippingMethod shippingMethod = null;
			if (StringUtils.isNotEmpty(shippingMethodName)) {
				filters.clear();
				filters.add(Filter.eq("name", shippingMethodName));
				List<ShippingMethod> shippingMethods = shippingMethodBaseService
						.findList(1, filters, null);
				if (shippingMethods.isEmpty()) {
					throw new RuntimeException("第"
							+ (i + 1)
							+ "行,在系统中找不到配送方式["
							+ shippingMethodName
							+ "]");
				}
				shippingMethod = shippingMethods.get(0);
			}

			// 付款方式
			cell = sheet.getCell(6, i);
			paymentMethodName = cell.getContents().trim();
			PaymentMethod paymentMethod = null;
			if (StringUtils.isNotEmpty(shippingMethodName)) {
				filters.clear();
				filters.add(Filter.eq("name", paymentMethodName));
				List<PaymentMethod> paymentMethods = paymentMethodBaseService
						.findList(1, filters, null);
				if (paymentMethods.isEmpty()) {
					throw new RuntimeException("第"
							+ (i + 1)
							+ "行,在系统中找不到付款方式["
							+ paymentMethodName
							+ "]");
				}
				paymentMethod = paymentMethods.get(0);
			}
			// 产品
			cell = sheet.getCell(7, i);
			vonderCode = cell.getContents().trim();
			Product product = null;
			if (StringUtils.isEmpty(vonderCode)) {
				throw new RuntimeException("第" + (i + 1) + "行,产品编码为空");
			}
			filters.clear();
			filters.add(Filter.eq("vonderCode", vonderCode));
			List<Product> products = productService.findList(null,
					1,
					filters,
					null);
			if (products.isEmpty()) {
				throw new RuntimeException("第"
						+ (i + 1)
						+ "行,在系统中找不到产品编码为["
						+ vonderCode
						+ "]的产品");
			}
			product = products.get(0);

			// 产品数量
			cell = sheet.getCell(8, i);
			quantityStr = cell.getContents().trim();
			if (StringUtils.isEmpty(quantityStr)) {
				throw new RuntimeException("第" + (i + 1) + "行,产品数量为空");
			}
			BigDecimal quantity = BigDecimal.ZERO;
			try {
				quantity = new BigDecimal(quantityStr);
			}
			catch (Exception e) {
				throw new RuntimeException("第" + (i + 1) + "行,产品数量格式有误");
			}
			if (quantity.compareTo(BigDecimal.ZERO) < 1) {
				throw new RuntimeException("第" + (i + 1) + "行,产品数量不能小于或等于零");
			}

			// 产品价格
			cell = sheet.getCell(9, i);
			priceStr = cell.getContents().trim();
			if (StringUtils.isEmpty(priceStr)) {
				throw new RuntimeException("第" + (i + 1) + "行,产品价格为空");
			}

			// 附言
			cell = sheet.getCell(10, i);
			memo = cell.getContents().trim();

			// 运费承担类型
			cell = sheet.getCell(11, i);
			freightChargeTypeName = cell.getContents().trim();

			List<SystemDict> freightChargeTypes = new ArrayList<SystemDict>();
			if (StringUtils.isNotEmpty(freightChargeTypeName)) {
				filters.clear();
				filters.add(Filter.eq("value", freightChargeTypeName));
				freightChargeTypes = systemDictService.findList(null,
						filters,
						null);
				if (freightChargeTypes == null
						|| freightChargeTypes.size() < 1) {
					throw new RuntimeException("第" + (i + 1) + "行,运费承担类型不存在");
				}
			}

			Order order = null;
			filters.clear();
			filters.add(Filter.eq("outTradeNo", outTradeNo));
			filters.add(Filter.ne("orderStatus", OrderStatus.cancelled));
			List<Order> orders = findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);

				// 订单中如果已存在产品则不允许再添加
				long count = orderItemService.count(Filter.eq("order", order),
						Filter.eq("product", product));
				if (count > 0) {
					throw new RuntimeException("第"
							+ (i + 1)
							+ "行,来源单号为["
							+ outTradeNo
							+ "]的订单中已存在产品编码为["
							+ vonderCode
							+ "]的产品，不允许重复添加");
				}
			}
			if (order == null) {
				order = initOrder(outTradeNo,
						store,
						shippingMethod,
						paymentMethod,
						consignee,
						phone,
						null,
						address,
						memo,
						freightChargeTypes);
			}

			// 初始化订单明细
			initOrderItem(order,
					product,
					new BigDecimal(quantityStr),
					new BigDecimal(priceStr),
					store);
			success++;

		}

		int result = rows - 1;
		return "总数" + result + "行,成功导入" + success + " 行";
	}

	public List<Map<String, Object>> orderImportData(
			MultipartFile multipartFile) throws Exception {

		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}

		String outTradeNo;// 来源单号
		String storeName;// 客户
		String consignee; // 收货人
		String phone; // 电话
		// String areaName;//地区全称(省市区)
		String address; // 地址
		String shippingMethodName; // 配送方式
		String paymentMethodName; // 付款方式
		String vonderCode; // 产品编号
		String quantityStr; // 数量
		String priceStr;// 产品价格
		String memo;// 备注
		String freightChargeTypeName;// 运费承担类型

		List<Map<String, Object>> excelOrder = new ArrayList<Map<String, Object>>();
		Ehcache cache = cacheManager.getCache("excelOrder");

		Map<String, String> outTradeNoMap = new HashMap<String, String>();
		for (int i = 1; i < rows; i++) {
			// 来源单号
			cell = sheet.getCell(0, i);
			outTradeNo = cell.getContents().trim();
			if (StringUtils.isEmpty(outTradeNo)) {
				throw new RuntimeException("第" + (i + 1) + "行,来源单号为空");
			}
			outTradeNoMap.put(outTradeNo, "");
		}
		for (String no : outTradeNoMap.keySet()) {

			List<Map<String, Object>> items = new ArrayList<Map<String, Object>>();
			for (int i = 1; i < rows; i++) {

				// 来源单号
				cell = sheet.getCell(0, i);
				outTradeNo = cell.getContents().trim();

				if (no.equals(outTradeNo)) {

					StringBuilder content = new StringBuilder();
					Map<String, Object> map = new HashMap<String, Object>();

					// 来源单号
					map.put("outTradeNo", outTradeNo);

					// 客户
					cell = sheet.getCell(1, i);
					storeName = cell.getContents().trim();
					map.put("storeName", storeName);

					// 收货人
					cell = sheet.getCell(2, i);
					consignee = cell.getContents().trim();
					map.put("consignee", consignee);

					// 电话
					cell = sheet.getCell(3, i);
					phone = cell.getContents().trim();
					map.put("phone", phone);

					// 地址
					cell = sheet.getCell(4, i);
					address = cell.getContents().trim();
					map.put("address", address);

					// 配送方式
					cell = sheet.getCell(5, i);
					shippingMethodName = cell.getContents().trim();
					map.put("shippingMethodName", shippingMethodName);

					// 付款方式
					cell = sheet.getCell(6, i);
					paymentMethodName = cell.getContents().trim();
					map.put("paymentMethodName", paymentMethodName);

					// 产品
					cell = sheet.getCell(7, i);
					vonderCode = cell.getContents().trim();
					map.put("vonderCode", vonderCode);

					// 产品数量
					cell = sheet.getCell(8, i);
					quantityStr = cell.getContents().trim();
					map.put("quantityStr", quantityStr);

					// 产品价格
					cell = sheet.getCell(9, i);
					priceStr = cell.getContents().trim();
					map.put("priceStr", priceStr);

					// 附言
					cell = sheet.getCell(10, i);
					memo = cell.getContents().trim();
					map.put("memo", memo);

					// 运费承担类型
					cell = sheet.getCell(11, i);
					freightChargeTypeName = cell.getContents().trim();
					map.put("freightChargeTypeName", freightChargeTypeName);

					content.append(outTradeNo + " ; ");
					content.append(storeName + " ; ");
					content.append(consignee + " ; ");
					content.append(phone + " ; ");
					content.append(address + " ; ");
					content.append(shippingMethodName + " ; ");
					content.append(paymentMethodName + " ; ");
					content.append(quantityStr + " ; ");
					content.append(priceStr + " ; ");
					content.append(memo + " ; ");
					content.append(freightChargeTypeName);
					map.put("content", content.toString());

					map.put("lineNo", i + 1);

					items.add(map);
				}
			}
			Map<String, Object> orderMap = new HashMap<String, Object>();
			String uuid = UUID.randomUUID().toString();
			orderMap.put("uuid", uuid);
			orderMap.put("items", items);
			excelOrder.add(orderMap);

			Element element = new Element(uuid, orderMap);
			cache.put(element);
		}

		return excelOrder;
	}

	@SuppressWarnings("unchecked")
	public void orderImportByCache(String uuid) {

		Ehcache cache = cacheManager.getCache("excelOrder");
		Element element = cache.get(uuid);
		Map<String, Object> orderMap = (Map<String, Object>) element
				.getObjectValue();
		List<Map<String, Object>> items = (List<Map<String, Object>>) orderMap
				.get("items");
		String outTradeNo;// 来源单号
		String storeName;// 客户
		String consignee; // 收货人
		String phone; // 电话
		String address; // 地址
		String shippingMethodName; // 配送方式
		String paymentMethodName; // 付款方式
		String vonderCode; // 产品编号
		String quantityStr; // 数量
		String priceStr;// 产品价格
		String memo;// 备注
		String freightChargeTypeName;// 运费承担类型

		Order order = null;
		List<Filter> filters = new ArrayList<Filter>();
		StringBuilder msg = new StringBuilder();
		for (int i = 0; i < items.size(); i++) {

			Map<String, Object> map = items.get(i);

			int lineNo = Integer.parseInt(map.get("lineNo").toString());

			// 来源单号
			outTradeNo = map.get("outTradeNo").toString();
			if (StringUtils.isEmpty(outTradeNo)) {
				msg.append("第" + lineNo + "行,来源单号为空;");
			}

			// 客户
			storeName = map.get("storeName").toString();
			if (StringUtils.isEmpty(storeName)) {
				msg.append("第" + lineNo + "行,客户为空;");
			}
			filters.clear();
			filters.add(Filter.eq("name", storeName));
			List<Store> stores = storeService.findList(null, filters, null);
			if (stores == null || stores.size() < 1) {
				msg.append("第" + lineNo + "行,客户[" + storeName + "]不存在;");
			}
			Store store = stores.get(0);

			// 收货人
			consignee = map.get("consignee").toString();
			if (StringUtils.isEmpty(consignee)) {
				msg.append("第" + lineNo + "行,收货人为空;");
			}

			// 电话
			phone = map.get("phone").toString();
			if (StringUtils.isEmpty(phone)) {
				msg.append("第" + lineNo + "行,电话为空;");
			}

			// 地址
			address = map.get("address").toString();
			if (StringUtils.isEmpty(address)) {
				msg.append("第" + lineNo + "行,地址为空;");
			}

			// 配送方式
			shippingMethodName = map.get("shippingMethodName").toString();
			ShippingMethod shippingMethod = null;
			if (StringUtils.isNotEmpty(shippingMethodName)) {
				filters.clear();
				filters.add(Filter.eq("name", shippingMethodName));
				List<ShippingMethod> shippingMethods = shippingMethodBaseService
						.findList(1, filters, null);
				if (shippingMethods.isEmpty()) {
					msg.append("第"
							+ lineNo
							+ "行,在系统中找不到配送方式["
							+ shippingMethodName
							+ "];");
				}
				shippingMethod = shippingMethods.get(0);
			}

			// 付款方式
			paymentMethodName = map.get("paymentMethodName").toString();
			PaymentMethod paymentMethod = null;
			if (StringUtils.isNotEmpty(shippingMethodName)) {
				filters.clear();
				filters.add(Filter.eq("name", paymentMethodName));
				List<PaymentMethod> paymentMethods = paymentMethodBaseService
						.findList(1, filters, null);
				if (paymentMethods.isEmpty()) {
					msg.append("第"
							+ lineNo
							+ "行,在系统中找不到付款方式["
							+ paymentMethodName
							+ "];");
				}
				paymentMethod = paymentMethods.get(0);
			}

			// 产品
			vonderCode = map.get("vonderCode").toString();
			Product product = null;
			if (StringUtils.isEmpty(vonderCode)) {
				msg.append("第" + lineNo + "行,产品编码为空;");
			}
			filters.clear();
			filters.add(Filter.eq("vonderCode", vonderCode));
			List<Product> products = productService.findList(null,
					1,
					filters,
					null);
			if (products.isEmpty()) {
				msg.append("第"
						+ lineNo
						+ "行,在系统中找不到产品编码为["
						+ vonderCode
						+ "]的产品;");
			}
			product = products.get(0);

			// 产品数量
			quantityStr = map.get("quantityStr").toString();
			if (StringUtils.isEmpty(quantityStr)) {
				msg.append("第" + lineNo + "行,产品数量为空;");
			}
			BigDecimal quantity = BigDecimal.ZERO;
			try {
				quantity = new BigDecimal(quantityStr);
			}
			catch (Exception e) {
				msg.append("第" + lineNo + "行,产品数量格式有误;");
			}
			if (quantity.compareTo(BigDecimal.ZERO) < 1) {
				msg.append("第" + lineNo + "行,产品数量不能小于零;");
			}

			// 产品价格
			priceStr = map.get("priceStr").toString();
			if (StringUtils.isEmpty(priceStr)) {
				msg.append("第" + lineNo + "行,产品价格为空;");
			}

			// 附言
			memo = map.get("memo").toString();

			// 运费承担类型
			freightChargeTypeName = map.get("freightChargeTypeName").toString();
			List<SystemDict> freightChargeTypes = new ArrayList<SystemDict>();
			if (StringUtils.isNotEmpty(freightChargeTypeName)) {
				filters.clear();
				filters.add(Filter.eq("value", freightChargeTypeName));
				freightChargeTypes = systemDictService.findList(null,
						filters,
						null);
				if (freightChargeTypes == null
						|| freightChargeTypes.size() < 1) {
					msg.append("第" + lineNo + "行,运费承担类型不存在;");
				}
			}
			if (order == null) {
				order = initOrder(outTradeNo,
						store,
						shippingMethod,
						paymentMethod,
						consignee,
						phone,
						null,
						address,
						memo,
						freightChargeTypes);
			}
			else {
				// 订单中如果已存在产品则不允许再添加
				long count = orderItemService.count(Filter.eq("order", order),
						Filter.eq("product", product));
				if (count > 0) {
					msg.append("第"
							+ lineNo
							+ "行,产品编码为["
							+ vonderCode
							+ "]的产品已存在，不允许重复添加;");
				}
			}

			// 初始化订单明细
			initOrderItem(order,
					product,
					new BigDecimal(quantityStr),
					new BigDecimal(priceStr),
					store);
		}
		if (!ConvertUtil.isEmpty(msg.toString())) {
			ExceptionUtil.throwServiceException(msg.toString());
		}

		Store store = order.getStore();
		BigDecimal balance = storeBalanceService.findBalance(store.getId(),
				order.getOrganization() == null ? null
						: order.getOrganization().getId(),
						order.getSbu() == null ? null:order.getSbu().getId());
		if (store.getIsReduceBalance() != null && store.getIsReduceBalance()) {
			if (balance.compareTo(order.getAmount()) == -1) {
				//// 客户余额不足
				ExceptionUtil.throwServiceException("15201");
			}
		}

		// 是否启用锁库存模式：0 否，1 是
		// Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		// int useLockStock =
		// Integer.parseInt(SystemConfig.getConfig("useLockStock",
		// companyInfoId));
		// if (useLockStock == 1
		// && (companyInfoId.intValue() == 1 || companyInfoId.intValue() == 18))
		// {
		// //自动分仓
		// autoSetWarehouse(order);
		// }
	}

	public Order initOrder(String outTradeNo, Store store,
			ShippingMethod shippingMethod, PaymentMethod paymentMethod,
			String consignee, String phone, Area area, String address,
			String memo, List<SystemDict> freightChargeTypes) {

		BigDecimal zero = BigDecimal.ZERO;
		Order order = new Order();
		order.setOutTradeNo(outTradeNo);

		// order.setShop(shop);
		order.setStore(store);

		order.setPaymentStatus(Order.PaymentStatus.unpaid);

		if (shippingMethod != null) {
			order.setShippingMethod(shippingMethod);
			order.setShippingMethodName(shippingMethod.getName());
		}
		if (paymentMethod != null) {
			order.setPaymentMethod(paymentMethod);
			order.setPaymentMethodName(paymentMethod.getName());
		}
		if (area != null) {
			order.setArea(area);
			order.setDistrict(area.getName());
			Area parent = area.getParent();
			if (parent != null) {
				order.setCity(parent.getName());// 市
				Area pParent = parent.getParent();
				if (pParent != null) {
					order.setProvince(pParent.getName());// 省
				}
			}
		}
		// if (warehouse != null) {
		// order.setWarehouseCode(warehouse.getSn());
		// order.setWarehouseId(warehouse.getId());
		// }
		order.setConsignee(consignee);
		order.setPhone(phone);
		order.setAddress(address);

		order.setAmountPaid(zero);// 已付金额
		order.setCouponDiscount(zero);// 优惠券折扣
		order.setFreight(zero);// 运费
		order.setIsInvoice(true);
		order.setOrderStatus(OrderStatus.unaudited);
		order.setShippingStatus(ShippingStatus.unshipped);
		order.setOrderType(2);// 2 B2B订单

		order.setOffsetAmount(zero);// 调整金额
		order.setPromotionDiscount(zero);// 促销折扣
		order.setTax(zero);// 税金
		order.setOrderDate(new Date());
		order.setMemo(memo);
		// if (orderType != null && orderType == 6) {
		// }
		// else {
		// order.setSn(snDao.generate(Sn.Type.order));
		// }
		order.setStoreMember(storeMemberService.getCurrent());
		String sn = SnUtil.getOrderSn();
		order.setSn(sn);
		order.setOrderStatus(Order.OrderStatus.unaudited);
		order.setPaymentMethod(paymentMethod);
		order.setShippingMethod(shippingMethod);
		order.setPaymentStatus(PaymentStatus.unpaid);
		order.setPromotionDiscount(zero);
		order.setType("7");
		order.setIsMobile(false);
		Date now = new Date();
		order.setCreateDate(now);
		order.setOrderDate(now);
		order.setConfirmStatus(0);
		if (freightChargeTypes.size() > 0) {
			order.setFreightChargeType(freightChargeTypes.get(0));
		}

		save(order);
		// 订单全链路
		List<String> orderSns = new ArrayList<String>();
		orderSns.add(order.getSn());
		orderFullLinkService.addFullLink(1,
				orderSns,
				order.getSn(),
				"excel导入订单",
				null);
		return order;
	}

	public void initOrderItem(Order order, Product product, BigDecimal quantity,
			BigDecimal price, Store store) {

		BigDecimal zero = BigDecimal.ZERO;
		OrderItem orderItem = new OrderItem();
		orderItem.setFullName(product.getFullName());
		orderItem.setName(product.getName());
		orderItem.setPrice(price);
		orderItem.setQuantity(quantity);
		orderItem.setShippedQuantity(zero);
		orderItem.setReturnQuantity(zero);
		orderItem.setSn(product.getSn());
		orderItem.setThumbnail(product.getThumbnail());
		orderItem.setProduct(product);
		orderItem.setOrder(order);
		orderItem.setOutTradeNo(order.getOutTradeNo());
		orderItem.setModel(product.getModel());
		orderItem.setVonderCode(product.getVonderCode());
		orderItemService.save(orderItem);
	}

	public void confirm(Long[] ids) {
		for (Long id : ids) {
			Order order = this.find(id);
			if (order == null) ExceptionUtil.throwServiceException("订单不存在");
			if (order.getConfirmStatus() != null
					&& order.getConfirmStatus() == 1) {
				ExceptionUtil.throwServiceException(
						"订单【" + order.getSn() + "】已确认，请勿重复操作");
			}

			confirmOrder(order);

			List<String> orderSns = new ArrayList<String>();
			orderSns.add(order.getSn());
			orderFullLinkService.addFullLink(1,
					orderSns,
					order.getSn(),
					"确认订单",
					null);
		}
	}

	public void confirmOrder(Order order) {

		order.setConfirmStatus(1);
		this.update(order);

		CompanyInfo companyInfo = companyInfoService
				.find(order.getCompanyInfoId());
		Map<String, Object> json_obj = new HashMap<String, Object>();
		Map<String, Object> response_obj = new HashMap<String, Object>();
		List<Map<String, Object>> data_array = new ArrayList<Map<String, Object>>();
		Map<String, Object> data_obj = new HashMap<String, Object>();
		List<Map<String, Object>> items_array = new ArrayList<Map<String, Object>>();
		response_obj.put("entcode", companyInfo.getUniqueIdentify());

		data_obj.put("order_id", order.getId());// 订单id
		data_obj.put("sn", order.getSn());// 订单号
		data_obj.put("customerCode",
				ConvertUtil.toEmpty(order.getStore().getOutTradeNo()));// 客户编码
		data_obj.put("memberName", null);// 业务员
		Warehouse warehouse = order.getWarehouse();
		data_obj.put("warehouseCode",
				(warehouse != null) ? order.getWarehouse().getSn() : "");// 仓库编码
		data_obj.put("deliver_tel", ConvertUtil.toEmpty(order.getPhone()));// 电话
		data_obj.put("deliver_mobile", ConvertUtil.toEmpty(order.getPhone()));// 手机号码
		data_obj.put("deliverAr", ConvertUtil.toEmpty(null));
		data_obj.put("deliverAddress", order.getAddress());// 收货地址
		data_obj.put("outType", 1);// 类型：1出库单 2退货单
		data_obj.put("remark", ConvertUtil.toEmpty(order.getMemo()));// 备注
		data_obj.put("deliver_man", ConvertUtil.toEmpty(order.getConsignee()));// 收货人
		data_obj.put("sum_amount", order.getAmount());// 订单金额
		data_obj.put("attribute1", ConvertUtil.toEmpty(null));
		data_obj.put("attribute2", ConvertUtil.toEmpty(null));
		data_obj.put("attribute3", ConvertUtil.toEmpty(null));
		data_obj.put("attribute4", order.getFreight());// 运费
		data_obj.put("attribute5", ConvertUtil.toEmpty(null));

		for (OrderItem orderItem : order.getOrderItems()) {
			Map<String, Object> item_obj = new HashMap<String, Object>();
			item_obj.put("itemCode", orderItem.getVonderCode());// 厂商编号编码
			item_obj.put("qty", orderItem.getQuantity());// 产品数量
			item_obj.put("realPrice", orderItem.getPrice());// 价格
			item_obj.put("note", ConvertUtil.toEmpty(orderItem.getMemo()));// 备注
			items_array.add(item_obj);
		}

		data_obj.put("items", items_array);
		data_array.add(data_obj);
		response_obj.put("data", data_array);
		json_obj.put("response", response_obj);
		String data = JsonUtils.toJson(json_obj);

		String[] fields = new String[] { order.getSn() };

	}

	@Override
	public void agreeBack(Wf wf) {

		if (wf.getStat().intValue() == 2) {

			Order order = find(wf.getObjId());
			// 是否启用锁库存模式：0 否，1 是
			int useLockStock = Integer.parseInt(SystemConfig.getConfig(
					"useLockStock", WebUtils.getCurrentCompanyInfoId()));
			List<OrderItem> orderItems = order.getOrderItems();
			if (useLockStock == 1) {
				// 锁库存
				String productIds = "";
				for (OrderItem orderItem : orderItems) {
					Product product = orderItem.getProduct();
					if (product != null) {
						Long productId = product.getId();
						productIds += "," + productId;
						// if
						// (productSuiteService.existsProductSuite(productId)) {
						// List<Map<String, Object>> items =
						// productSuiteService.findAllItemListByProductId(productId);
						// for (Map<String, Object> item : items) {
						// Long pid = Long.parseLong(item.get("product_id")
						// .toString());
						// productIds += "," + pid;
						// }
						// }
					}
				}
				if (!ConvertUtil.isEmpty(productIds)) {
					productIds = productIds.substring(1);
					lockDataDao.lockStockByProduct(productIds);
				}
			}
			// StoreMember storeMember = storeMemberService.getCurrent();
			if (order.getPaymentStatus().equals(Order.PaymentStatus.unpaid)) {

				// 锁客户
				Store store = order.getStore();
				lockDataDao.lockStore(store.getId().toString());
				// 订单状态改为已支付
				BigDecimal amount = order.getAmount();
				order.setPaymentStatus(Order.PaymentStatus.paid);
				order.setAmountPaid(amount);

				if (order.getPaymentType().compareTo(BigDecimal.ZERO) == 1) {
					BigDecimal storeBalance = storeCustomBalanceService
							.findBalance(store.getId());
					BigDecimal storeCredit = storeCustomBalanceService
							.findCreditBalance(store.getId());
					BigDecimal sumAmount = BigDecimal.ZERO;
					for (OrderItem orderItem : orderItems) {
						sumAmount = sumAmount.add(orderItem.getPrice()
								.multiply(orderItem.getQuantity()));
					}
					if ((storeBalance.subtract(storeCredit))
							.compareTo(sumAmount.multiply(order.getPaymentType()
									.divide(new BigDecimal(100)))) == -1) {

						ExceptionUtil
								.throwServiceException("客户余额不足<br/>客户销售回款余额￥"
										+ (storeBalance.subtract(storeCredit))
												.setScale(2,
														BigDecimal.ROUND_HALF_UP)
										+ "，定金金额￥"
										+ sumAmount
												.multiply(order.getPaymentType()
														.divide(new BigDecimal(
																100)))
												.setScale(2,
														BigDecimal.ROUND_HALF_UP));
					}
				}
				// BigDecimal balance =
				// storeBalanceService.findBalance(store.getId());
				// if (store.getIsReduceBalance() != null
				// && store.getIsReduceBalance()) {
				// if (balance.compareTo(order.getAmount()) == -1) {
				// ////客户余额不足
				// ExceptionUtil.throwServiceException("15201");
				// }
				// }
			}
			order.setOrderStatus(OrderStatus.audited);
			order.setCheckDate(new Date());
			update(order);

			// 全链路
			// List<String> orderSns = new ArrayList<String>();
			// orderSns.add(order.getSn());
			// orderFullLinkService.addFullLink(1,
			// orderSns,
			// order.getSn(),
			// "订单审核",
			// null);

			if (useLockStock == 1) {
				for (OrderItem orderItem : orderItems) {
					Product product = orderItem.getProduct();
					if (product != null) {
						Long productId = product.getId();
						String productName = product.getName();
						BigDecimal quantity = orderItem.getQuantity();
						Map<String, Object> stockMap = null;
						// if
						// (productSuiteService.existsProductSuite(productId)) {
						// stockMap =
						// productSuiteService.getProductSuiteStock(productId);
						// }
						// else {
						// }
						stockMap = stockService.findLockStock(productId);
						BigDecimal lock_stock = BigDecimal.ZERO;
						BigDecimal useable_stock = BigDecimal.ZERO;
						if (stockMap != null) {
							lock_stock = new BigDecimal(
									stockMap.get("lock_stock").toString());
							useable_stock = new BigDecimal(
									stockMap.get("useable_stock").toString());
						}
						if (quantity.compareTo(useable_stock) == 1) {
							// 系统目前已审核未发货的产品[{0}]数量有[{1}] ,
							// 可售库存[{2}]，当前订单下单数[{3}]，库存不足，不允许审核！
							ExceptionUtil.throwServiceException("15181",
									productName,
									lock_stock.stripTrailingZeros(),
									useable_stock.stripTrailingZeros(),
									quantity.stripTrailingZeros());
						}
					}
				}
			}

			// 订单审核是否生成采购单：0 否，1 是
			int check2PurOrder = 0;
			try {
				check2PurOrder = Integer.parseInt(SystemConfig.getConfig(
						"check2PurOrder", WebUtils.getCurrentCompanyInfoId()));
			}
			catch (Exception e) {}
			if (check2PurOrder == 1) {
				// 转采购单
				change2PurOrder(order);
			}

			/** 星光订单，写入ERP接口表 */
			// if (order.getCompanyInfoId() == 13) {
			// intfOrderTableToService.saveIntfOrderForErp(order);
			// }

			createPdf(order);

		}
	}

	// 下达写订单接口表
	public void saveOrderIntfAtXd(Order order, Long companyInfoId) {
		// TCL
		// if (companyInfoId == 25) {
		// int useTclOrderIntf = 0;
		// try {
		// useTclOrderIntf =
		// Integer.parseInt(SystemConfig.getConfig("useTclOrderIntf",
		// WebUtils.getCurrentCompanyInfoId()));
		// }
		// catch (Exception e) {}
		// if(useTclOrderIntf==1){
		// intfOrderTableToService.saveIntfOrderForTcl(order);
		// }
		//
		// }
	}

	public Integer count(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] shippingStatus, Long warehouseId,
			Long[] storeIds, String consignee, String phone, String address,
			Long deliveryCorpId, Long[] productId, Integer[] paymentStatus,
			Integer[] flag, Integer orderType, String firstTime,
			String lastTime, Integer[] confirmStatus, String store_member_name,
			Long saleOrgId, Long organizationId, Pageable pageable,
			Integer page, Integer size) {
		return orderDao.count(orderSn,
				outTradeNo,
				orderStatus,
				null,
				shippingStatus,
				warehouseId,
				storeIds,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				orderType,
				firstTime,
				lastTime,
				confirmStatus,
				store_member_name,
				saleOrgId,
				organizationId,
				pageable,
				page,
				size,
				null,
				null,
				null,
				null,
				null);
	}

	public List<Map<String, Object>> findItemList(String orderSn,
			String outTradeNo, Integer[] orderStatus, Integer[] shippingStatus,
			Long warehouseId, Long[] storeIds, String consignee, String phone,
			String address, Long deliveryCorpId, Long[] productId,
			Integer[] paymentStatus, Integer[] flag, Integer orderType,
			String firstTime, String lastTime, Integer[] confirmStatus,
			Long[] ids, String store_member_name, Long saleOrgId,
			Long organizationId, Integer page, Integer size) {
		return orderDao.findItemList(orderSn,
				outTradeNo,
				orderStatus,
				null,
				shippingStatus,
				warehouseId,
				storeIds,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				orderType,
				firstTime,
				lastTime,
				confirmStatus,
				ids,
				store_member_name,
				saleOrgId,
				organizationId,
				page,
				size,
				null,
				null,
				null,
				null,
				null);

	}

	@Override
	@Transactional
	public void rejectOrder(Order order) {
		// 锁定订单，防止并发操作
		lockDataDao.lockOrder(order.getId().toString());

		StockTaskBuilder taskBuilder = new StockTaskBuilder();
		// String orderSn = order.getSn();
		List<OrderItem> orderItems = order.getOrderItems();
		for (OrderItem orderItem : orderItems) {
			// 解锁库存
			// Stock stock = orderItem.getStock();
			// if (stock != null) {
			// Long wId = orderItem.getWarehouse().getId();
			// Product product = orderItem.getProduct();
			// Long productId = product.getId();
			// BigDecimal quantity = orderItem.getQuantity();
			// taskBuilder.unLockStock(productId, wId, quantity, "订单["
			// + orderSn
			// + "]驳回，解锁原仓库库存");
			// //全链路
			// orderFullLinkService.addFullLink(1,
			// orderSn,
			// orderSn,
			// "订单驳回解锁原仓库，产品[" + product.getFullName() + "]",
			// null);
			//
			// getDaoCenter().getNativeDao()
			// .update("update xx_order_item set
			// warehouse=null,stock=null,is_purchase=null where id=?",
			// new Object[] { orderItem.getId() });
			// }
			// else {
			getDaoCenter().getNativeDao().update(
					"update xx_order_item set is_purchase=null where id=?",
					new Object[] { orderItem.getId() });
			
			// }
		}

		order.setWfId(null);
		order.setWfState(0);
		order.setOrderStatus(OrderStatus.unaudited);
		order.setPaymentStatus(Order.PaymentStatus.unpaid);
		order.setAmountPaid(BigDecimal.ZERO);
		order.setCheckStoreMember(null);
		order.setCheckDate(null);
		order.setSaleOrg(null);
		update(order);

		List<String> orderSns = new ArrayList<String>();
		orderSns.add(order.getSn());
		orderFullLinkService.addFullLink(1,
				orderSns,
				order.getSn(),
				ConvertUtil.convertI18nMsg("18705"),
				null);
		// 处理库存
		stockService.handleStock(taskBuilder);

		// 订单审核是否生成采购单：0 否，1 是
		int check2PurOrder = 0;
		Long company_info_id = WebUtils.getCurrentCompanyInfoId();
		try {
			check2PurOrder = Integer.parseInt(
					SystemConfig.getConfig("check2PurOrder", company_info_id));
		}
		catch (Exception e) {}
		if (check2PurOrder == 1) {
			String sql = "update xx_order set order_status = 3"
					+ " where out_trade_no = ? and order_type = 4 and company_info_id = ?";
			orderDao.getNativeDao().update(sql,
					new Object[] { order.getSn(), company_info_id });
		}

	}

	@Override
	public void interruptBack(Wf wf) {
		Order order = find(wf.getObjId());
		order.setCheckStoreMember(null);
		order.setCheckDate(null);
		order.setSaleOrg(null);
		update(order);
		List<String> orderSns = new ArrayList<String>();
		orderSns.add(order.getSn());
		orderFullLinkService.addFullLink(1,
				orderSns,
				order.getSn(),
				ConvertUtil.convertI18nMsg("18704"),
				null);
	}

	private void createPdf(Order order) {
		String file = "";
		try {
			TriplicateForm triplicateForm = createTriplicateForm(order);
			file = triplicateForm.getUrl();
		}
		catch (Exception e) {
			this.deleteFile(file);
			e.printStackTrace();
			// throw e;
		}

	}

	public TriplicateForm createTriplicateForm(Order order) throws Exception {
		Setting setting = SettingUtils.get();
		String siteUrl = setting.getSiteUrl();
		String template_path = "/pdf/order/template/order.pdf";
		String file_name = "" + UUID.randomUUID();
		String pdfStaticPath = "/pdf/order/file/" + file_name + ".pdf";
		this.buildPdf(order, pdfStaticPath, template_path);
		/** 保存中间表 */
		String url = siteUrl + pdfStaticPath;
		TriplicateForm triplicateForm = new TriplicateForm(order.getId(), 1,
				url, 0);
		triplicateFormService.save(triplicateForm);
		return triplicateForm;
	}

	private String FONT_PATH = "/usr/share/fonts/self/simsun.ttf";

	private void buildPdf(Order order, String pdfStaticPath,
			String template_path) throws Exception {
		String PdfTemplatePath = servletContext.getRealPath(template_path);
		String outputFile = servletContext.getRealPath(pdfStaticPath);
		CompanyInfo companyInfo = companyInfoService
				.find(order.getCompanyInfoId());
		String company_name = companyInfo.getCompany_name();
		String logo = companyInfo.getLogo();
		String create_date = DateUtil.convert(order.getCheckDate());
		Store store = order.getStore();
		String alias = store.getAlias();
		BigDecimal total = order.getAmount().setScale(2,
				BigDecimal.ROUND_HALF_UP);
		String amount = NumberToCN.number2CNMontrayUnit(total);
		if (alias == null || alias.length() == 0) alias = store.getName();

		FileOutputStream fos = new FileOutputStream(outputFile);// 需要生成PDF
		List<OrderItem> orderItems = order.getOrderItems();
		int itemSize = orderItems.size();// 2
		int pageSize = 12;
		int pdfPage = itemSize % pageSize == 0 ? itemSize / pageSize
				: (itemSize / pageSize) + 1;
		ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pdfPage];// 用于存储每页生成PDF流
		BaseFont bf = BaseFont.createFont(FONT_PATH,
				BaseFont.IDENTITY_H,
				BaseFont.NOT_EMBEDDED);
		for (int i = 0; i < pdfPage; i++) {
			baos[i] = new ByteArrayOutputStream();
			PdfReader reader = new PdfReader(PdfTemplatePath);
			PdfStamper stamp = new PdfStamper(reader, baos[i]);
			/*
			 * BaseFont bf = BaseFont.createFont("F://simsun.ttf",
			 * BaseFont.IDENTITY_H,BaseFont.EMBEDDED);
			 */
			ArrayList<BaseFont> fontList = new ArrayList<BaseFont>();
			fontList.add(bf);
			AcroFields form = stamp.getAcroFields();
			int column = 0;// 列数 3 1-3 4
			int forNumber = i * pageSize + pageSize > itemSize ? itemSize
					: i * pageSize + pageSize;

			form.setSubstitutionFonts(fontList);

			form.setField("company_name", company_name);
			form.setField("create_date", create_date);
			form.setField("page", (i + 1) + "");
			form.setField("totalPage", pdfPage + "");
			form.setField("sn", order.getSn());
			form.setField("buyerName", alias);
			form.setField("amount", amount);
			form.setField("total", total.toString());
			form.setField("store_sn", ConvertUtil.toEmpty(""));
			form.setField("address", ConvertUtil.toEmpty(order.getAddress()));
			form.setField("memo", ConvertUtil.toEmpty(order.getMemo()));
			form.setField("consignee",
					ConvertUtil.toEmpty(order.getConsignee()));
			// BigDecimal addamount =new BigDecimal("0");
			for (int j = i * pageSize; j < forNumber; j++) {

				OrderItem orderItem = orderItems.get(j);
				Product product = orderItem.getProduct();
				String DeliveryTime = DateUtil
						.convert(orderItem.getDeliveryTime(), "yyyy-MM-dd");
				BigDecimal quantity = orderItem.getQuantity()
						.stripTrailingZeros();
				BigDecimal price = orderItem.getPrice().setScale(2,
						BigDecimal.ROUND_HALF_UP);
				String unitString = product == null ? ""
						: (product.getUnit() == null ? ""
								: "/" + product.getUnit());
				String quantityString = quantity.toPlainString() + unitString;
				String productSn = product == null ? ""
						: ConvertUtil.toEmpty(orderItem.getVonderCode());
				form.setField("productSn_" + column, productSn + "");
				form.setField("fullName_" + column, orderItem.getName());
				form.setField("model_" + column,
						ConvertUtil.toEmpty(orderItem.getModel()));
				form.setField("special_" + column,
						ConvertUtil.toEmpty(orderItem.getDemand()));
				form.setField("unit_" + column,
						ConvertUtil.toEmpty(
								product == null ? "" : product.getUnit()));
				form.setField("quantity_" + column, quantityString);
				form.setField("price_" + column, ConvertUtil.toEmpty(price));
				form.setField("delivery_time_" + column, DeliveryTime);
				String price_total = "";
				if ("0.00".equals(price.toString())) {
					price_total = "0.00";
				}
				else {
					price_total = price.multiply(orderItem.getQuantity())
							.setScale(2, BigDecimal.ROUND_HALF_UP)
							.toString();
				}
				// addamount=addamount.add(new BigDecimal(price_total));
				form.setField("price_total_" + column,
						ConvertUtil.toEmpty(price_total));
				form.setField("buyer_memo_" + column,
						ConvertUtil.toEmpty(orderItem.getBuyerMemo()));
				form.setField("seller_memo_" + column,
						ConvertUtil.toEmpty(orderItem.getSellerMemo()));
				/*
				 * if(column==11){ form.setField("total" +
				 * column,addamount.toString()); }
				 */
				column++;
			}
			if (logo != null && !"".equals(logo)) {
				try {
					int page = form.getFieldPositions("logo").get(0).page;
					Rectangle signRect = form.getFieldPositions("logo")
							.get(0).position;
					float x = signRect.getLeft();
					float y = signRect.getBottom();
					// 读图片
					// http://pptest.etwowin.com/pdf/new_order/qrImage/qr_ccbac13a-2814-41de-9c95-252d4b443787.jpg
					Image image = Image.getInstance(logo.trim());
					// 获取操作的页面
					PdfContentByte under = stamp.getOverContent(page);
					// 根据域的大小缩放图片
					image.scaleToFit(signRect.getWidth(), signRect.getHeight());
					// 添加图片
					image.setAbsolutePosition(x, y);
					under.addImage(image);
				}
				catch (Exception e) {
					e.printStackTrace();
				}
			}
			stamp.setFormFlattening(true); // 千万不漏了这句啊, */
			stamp.close();
		}
		Document doc = new Document();
		PdfCopy pdfCopy = new PdfCopy(doc, fos);
		doc.open();
		PdfImportedPage impPage = null;
		// doc.add(new Paragraph("",font));
		/** 取出之前保存的每页内容 */
		for (int i = 0; i < pdfPage; i++) {
			impPage = pdfCopy
					.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);
			pdfCopy.addPage(impPage);
		}
		doc.close();// 当文件拷贝 记得作废doc

	}

	// 生成pdf
	public void refushPdf(Order order) {

		createPdf(order);
	}

	@Transactional(readOnly = true)
	public int deleteFile(String staticPath) {

		// File staticFile = new File(servletContext.getRealPath(staticPath));
		File staticFile = new File(servletContext.getRealPath(staticPath));
		if (staticFile.exists()) {
			staticFile.delete();
			return 1;
		}
		return 0;
	}

	public TriplicateForm getTriplicateForm(Long id) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("objId", id));
		filters.add(Filter.eq("type", 1));
		filters.add(Filter.eq("seq", 0));
		ArrayList<net.shopxx.base.core.Order> orders = new ArrayList<net.shopxx.base.core.Order>();
		orders.add(net.shopxx.base.core.Order.desc("createDate"));
		List<TriplicateForm> triplicateForms = triplicateFormService
				.findList(null, filters, orders);
		TriplicateForm triplicateForm = null;
		if (triplicateForms.size() > 0) {
			triplicateForm = triplicateForms.get(0);
		}
		return triplicateForm;

	}

	/**
	 * 转采购单
	 */
	public void change2PurOrder(Order order) {

		// 审核自动生成采购单
		List<OrderItem> orderItems = order.getOrderItems();
		for (OrderItem orderItem : orderItems) {

			Product product = orderItem.getProduct();
			List<Map<String, Object>> priceList = contractPriceService
					.findItemByProduct(product.getId());
			if (priceList.isEmpty() || priceList.size() > 1) {
				// 产品[{0}]没有维护合约采购价或维护了多个合约采购价
				ExceptionUtil.throwServiceException("15251", product.getName());
			}
			BigDecimal zero = BigDecimal.ZERO;
			BigDecimal price = new BigDecimal(
					priceList.get(0).get("price").toString());
			Long supplierId = Long
					.parseLong(priceList.get(0).get("store").toString());
			Store supplier = storeService.find(supplierId);
			Warehouse warehouse = orderItem.getWarehouse();
			if (warehouse != null) {
				long count = warehouseStoreService.count(
						Filter.eq("warehouse", warehouse),
						Filter.eq("store", supplier));
				if (count == 0) {
					// 产品[{0}]合约采购价所对应供应商[{1}]没有关联仓库[{2}]
					ExceptionUtil.throwServiceException("15252",
							product.getName(),
							supplier.getName(),
							warehouse.getName());
				}
			}
			Order purOrder = new Order();
			List<OrderItem> purOrderItems = new ArrayList<OrderItem>();
			// 复制到新订单
			BeanUtils.copyProperties(order,
					purOrder,
					new String[] { "id",
							"createDate",
							"modifyDate",
							"sn",
							"orderItems",
							"deposits",
							"payments",
							"shippings",
							"returnGoodsApply",
							"orderAttachs" });
			purOrder.setSn(SnUtil.generateSn());
			purOrder.setOutTradeNo(order.getSn());
			purOrder.setOrderType(4);
			purOrder.setSupplier(supplier);
			// 复制到新订单项
			OrderItem purOrderItem = new OrderItem();
			BeanUtils.copyProperties(orderItem,
					purOrderItem,
					new String[] { "id",
							"createDate",
							"modifyDate",
							"order",
							"orderItemPartses",
							"orderItemAttachs" });
			purOrderItem.setSourceOrderItemId(orderItem.getId());
			purOrderItem.setSourceOrderSn(order.getSn());
			purOrderItem.setQuantity(orderItem.getQuantity());
			purOrderItem.setShipPlanQuantity(zero);
			purOrderItem.setShippedQuantity(zero);
			purOrderItem.setOrder(order);
			purOrderItem.setWarehouse(warehouse);
			purOrderItem.setPrice(price);
			purOrderItem.setOrder(purOrder);
			purOrderItems.add(purOrderItem);

			purOrder.setAmountPaid(purOrder.getAmount());
			purOrder.setOrderItems(purOrderItems);
			this.save(purOrder);

			orderItem.setIsPurchase(1);
			orderItemService.update(orderItem);

			// 全链路
			List<String> orderSns = new ArrayList<String>();
			orderSns.add(order.getSn());
			orderFullLinkService.addFullLink(11,
					orderSns,
					purOrder.getSn(),
					"订单审核自动转采购单",
					null);
		}
	}

	public void autoSetWarehouse(Order order) {

		List<OrderItem> orderItems = order.getOrderItems();
		String productIds = "";
		for (OrderItem orderItem : orderItems) {
			Product product = orderItem.getProduct();
			if (product != null) {
				productIds += "," + product.getId();
			}
		}
		if (!ConvertUtil.isEmpty(productIds)) {
			productIds = productIds.substring(1);
			lockDataDao.lockStockByProduct(productIds);
		}
		List<Filter> filters = new ArrayList<Filter>();
		LogUtils.info("订单[" + order.getSn() + "]自动分仓");
		List<String> orderSns = new ArrayList<String>();
		orderSns.add(order.getSn());
		for (OrderItem orderItem : orderItems) {
			Product product = orderItem.getProduct();
			if (product == null) continue;
			BigDecimal quantity = orderItem.getQuantity();
			List<Map<String, Object>> priceList = contractPriceService
					.findItemByProduct(product.getId());
			if (priceList.isEmpty() || priceList.size() > 1) {
				LogUtils.info("产品[" + product.getName() + "]找不到合约价");
				continue;
			}
			Long supplierId = Long
					.parseLong(priceList.get(0).get("store").toString());
			Store supplier = storeService.find(supplierId);
			filters.clear();
			filters.add(Filter.eq("store", supplier));
			List<WarehouseStore> warehouseStores = warehouseStoreService
					.findList(null, filters, null);
			if (warehouseStores.isEmpty()) {
				LogUtils.info("供应商[" + supplier.getName() + "]未关联仓库");
				continue;
			}
			for (WarehouseStore warehouseStore : warehouseStores) {
				Warehouse warehouse = warehouseStore.getWarehouse();
				Map<String, Object> stockMap = stockService
						.findLockStock(product.getId(), warehouse.getId());
				BigDecimal useable_stock = BigDecimal.ZERO;
				if (stockMap != null) {
					useable_stock = new BigDecimal(
							stockMap.get("useable_stock").toString());
				}
				if (quantity.compareTo(useable_stock) == 1) {
					LogUtils.info("产品["
							+ product.getName()
							+ "],仓库["
							+ warehouse.getName()
							+ "]可售库存["
							+ useable_stock
							+ "],下单数["
							+ quantity
							+ "],不够分配");
					continue;
				}
				else {
					orderItem.setWarehouse(warehouse);
					orderItemService.update(orderItem);
					LogUtils.info("产品["
							+ product.getName()
							+ "]仓库["
							+ warehouse.getName()
							+ "]可售库存["
							+ useable_stock
							+ "],下单数["
							+ quantity
							+ "],可分配");

					// 全链路
					orderFullLinkService.addFullLink(11,
							orderSns,
							order.getSn(),
							"下单自动分仓，产品["
									+ product.getName()
									+ "]，仓库["
									+ warehouse.getName()
									+ "]",
							null);
					break;
				}
			}
		}
	}

	@Override
	public void startBack(Wf wf) {

		Order order = find(wf.getObjId());

		// 订单审核是否生成采购单：0 否，1 是
		int check2PurOrder = 0;
		try {
			check2PurOrder = Integer.parseInt(SystemConfig.getConfig(
					"check2PurOrder", WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		if (check2PurOrder == 1) {
			List<OrderItem> orderItems = order.getOrderItems();
			for (OrderItem orderItem : orderItems) {

				Product product = orderItem.getProduct();
				if (product == null) continue;
				List<Map<String, Object>> priceList = contractPriceService
						.findItemByProduct(product.getId());
				if (priceList.isEmpty() || priceList.size() > 1) {
					// 产品[{0}]没有维护合约采购价或维护了多个合约采购价
					ExceptionUtil.throwServiceException("15251",
							product.getName());
				}
				Long supplierId = Long
						.parseLong(priceList.get(0).get("store").toString());
				Store supplier = storeService.find(supplierId);
				Warehouse warehouse = orderItem.getWarehouse();
				if (warehouse != null) {
					long count = warehouseStoreService.count(
							Filter.eq("warehouse", warehouse),
							Filter.eq("store", supplier));
					if (count == 0) {
						// 产品[{0}]合约采购价所对应供应商[{1}]没有关联仓库[{2}]
						ExceptionUtil.throwServiceException("15252",
								product.getName(),
								supplier.getName(),
								warehouse.getName());
					}
				}
			}
		}
		wf.setStore(order.getStore());
		wfBaseService.update(wf);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findReport(String orderSn, Long[] productId,
			String vonderCode, String mod, Long[] storeId, Long storeMemberId,
			Long supplierId, String firstTime, String lastTime,
			Integer[] shippingStatus, BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, Pageable pageable) {
		return orderDao.findReport(orderSn,
				productId,
				vonderCode,
				mod,
				storeId,
				storeMemberId,
				supplierId,
				firstTime,
				lastTime,
				shippingStatus,
				minNotShippingQuantity,
				maxNotShippingQuantity,
				minShippedQuantity,
				maxShippedQuantity,
				null,
				null,
				null,
				null,
				null,
				null,
				pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Integer countReport(String orderSn, Long[] productId,
			String vonderCode, String mod, Long[] storeId, Long storeMemberId,
			Long supplierId, String firstTime, String lastTime,
			Integer[] shippingStatus, BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, Pageable pageable, Integer page,
			Integer size) {
		return orderDao.countReport(orderSn,
				productId,
				vonderCode,
				mod,
				storeId,
				storeMemberId,
				supplierId,
				firstTime,
				lastTime,
				shippingStatus,
				minNotShippingQuantity,
				maxNotShippingQuantity,
				minShippedQuantity,
				maxShippedQuantity,
				null,
				null,
				null,
				null,
				null,
				null,
				pageable,
				page,
				size);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findReportList(String orderSn,
			Long[] productId, String vonderCode, String mod, Long[] storeId,
			Long storeMemberId, Long supplierId, String firstTime,
			String lastTime, Integer[] shippingStatus, Long[] ids,
			BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, Pageable pageable, Integer page,
			Integer size) {
		return orderDao.findReportList(orderSn,
				productId,
				vonderCode,
				mod,
				storeId,
				storeMemberId,
				supplierId,
				firstTime,
				lastTime,
				shippingStatus,
				minNotShippingQuantity,
				maxNotShippingQuantity,
				minShippedQuantity,
				maxShippedQuantity,
				null,
				null,
				null,
				null,
				null,
				null,
				pageable,
				page,
				size);
	}

	public Order findBySn(String sn) {
		Order order = null;
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("sn", sn));
		List<Order> orders = this.findList(1, filters, null);
		if (orders.size() > 0) {
			order = orders.get(0);
		}
		return order;
	}

	public OrderItem findItemByVonderCode(String orderSn, String vonderCode) {
		return orderDao.findItemByVonderCode(orderSn, vonderCode);
	}

	public void checkBalance(Wf wf) {

		Order order = find(wf.getObjId());
		Store store = order.getStore();

		if (store.getIsReduceBalance() == null || !store.getIsReduceBalance()) {
			BigDecimal balance = storeBalanceService.findCheckBalance(
					store.getId(),
					order.getOrganization() == null ? null
							: order.getOrganization().getId());
			if (balance.compareTo(order.getAmount()) == -1) {
				//// 客户余额不足
				BigDecimal subtract = balance.subtract(order.getAmount())
						.multiply(new BigDecimal("-1"))
						.setScale(2, BigDecimal.ROUND_HALF_UP);
				ExceptionUtil.throwServiceException("客户余额不足<br/>客户可发货余额￥"
						+ balance.setScale(2, BigDecimal.ROUND_HALF_UP)
						+ "，订单总金额￥"
						+ order.getAmount().setScale(2,
								BigDecimal.ROUND_HALF_UP)
						+ "，还差￥"
						+ subtract
						+ "");
			}
		}
	}

}
