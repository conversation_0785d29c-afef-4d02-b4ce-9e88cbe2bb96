package net.shopxx.order.service.impl;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletContext;

import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.base.core.util.*;
import net.shopxx.intf.job.IntfLogisticsJob;
import net.shopxx.intf.service.ClientPushFourHundredService;
import net.shopxx.stock.entity.WarehouseStock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ServletContextAware;

import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.swetake.util.Qrcode;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.aftersales.b2b.service.B2bReturnsItemService;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.service.TotalDateService;
import net.shopxx.factory.entity.ProductionSchedulingItem;
import net.shopxx.factory.service.ProductionSchedulingItemService;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import net.shopxx.member.entity.CustomerRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.CustomerRechargeService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.dao.SaleNatureShippingDao;
import net.shopxx.order.entity.AmShipping;
import net.shopxx.order.entity.AmShippingItem;
import net.shopxx.order.entity.MoveLibrary;
import net.shopxx.order.entity.MoveLibrary.MoveReceiveStatus;
import net.shopxx.order.entity.MoveLibrary.MoveStatus;
import net.shopxx.order.entity.MoveLibrary.MoveStatuss;
import net.shopxx.order.entity.MoveLibraryClose;
import net.shopxx.order.entity.MoveLibraryCloseItem;
import net.shopxx.order.entity.MoveLibraryItem;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.entity.Order.ShippingStatus;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.PriceDifference;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.service.AmShippingItemService;
import net.shopxx.order.service.MoveLibraryCloseItemService;
import net.shopxx.order.service.MoveLibraryCloseService;
import net.shopxx.order.service.MoveLibraryItemService;
import net.shopxx.order.service.MoveLibraryService;
import net.shopxx.order.service.OrderCloseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.PriceDifferenceService;
import net.shopxx.order.service.SaleShippingNatureService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.order.service.TriplicateFormService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseBatchItem;
import net.shopxx.stock.entity.WarehouseBillBatch;
import net.shopxx.stock.service.StockService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseBatchItemService;
import net.shopxx.stock.service.WarehouseBatchService;
import net.shopxx.stock.service.WarehouseStockService;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.CommonVariable;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.util.SnUtil;
import net.shopxx.util.dto.ReportingEntityVo;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

@Service("saleShippingNatureServiceImpl")
public class SaleShippingNatureServiceImpl extends WfBillBaseServiceImpl<AmShipping>
		implements SaleShippingNatureService, ServletContextAware {

	private static Logger logger = LoggerFactory.getLogger(SaleShippingNatureServiceImpl.class);

	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "saleNatureShippingDao")
	private SaleNatureShippingDao saleNatureShippingDao;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "amShippingItemServiceImpl")
	private AmShippingItemService amShippingItemService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "b2bReturnsItemServiceImpl")
	private B2bReturnsItemService b2bReturnsItemService;
	@Resource(name = "b2bReturnsServiceImpl")
	private B2bReturnsService b2bReturnsService;
	@Resource(name = "orderCloseServiceImpl")
	private OrderCloseService orderCloseService;
	@Resource(name = "triplicateFormServiceImpl")
	private TriplicateFormService triplicateFormService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "stockServiceImpl")
	private StockService stockService;
	@Resource(name = "productionSchedulingItemServiceImpl")
	private ProductionSchedulingItemService productionSchedulingItemService;
	@Resource(name = "moveLibraryItemServiceImpl")
	private MoveLibraryItemService moveLibraryItemService;
	@Resource(name = "moveLibraryServiceImpl")
	private MoveLibraryService moveLibraryService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "totalDateServiceImpl")
	private TotalDateService totalDateService;
	@Resource(name = "customerRechargeServiceImpl")
	private CustomerRechargeService customerRechargeService;
	@Resource(name = "warehouseBatchServiceImpl")
	private WarehouseBatchService warehouseBatchService;
	@Resource(name = "warehouseStockServiceImpl")
	private WarehouseStockService warehouseStockService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "moveLibraryCloseServiceImpl")
	private MoveLibraryCloseService moveLibraryCloseService;
	@Resource(name = "moveLibraryCloseItemServiceImpl")
	private MoveLibraryCloseItemService moveLibraryCloseItemService;
	@Resource(name = "aftersaleServiceImpl")
	private AftersaleService aftersaleService;
	@Resource(name = "intfOrderMessageToServiceImpl")
    private IntfOrderMessageToService intfOrderMessageToService;
    @Resource(name = "priceDifferenceServiceImpl")
    private PriceDifferenceService priceDifferenceService;
    @Resource(name = "warehouseBatchItemServiceImpl")
    private WarehouseBatchItemService warehouseBatchItemService;
    @Resource(name = "intfLogisticsJob")
    private IntfLogisticsJob intfLogisticsJob;
    @Resource(name = "shippingInfoToLink5ServiceImpl")
	private ShippingInfoToLink5Service shippingInfoToLink5Service;
	@Resource(name = "clientPushFourHundredServiceImpl")
	private ClientPushFourHundredService clientPushFourHundredService;
    
	private ServletContext servletContext;

	@Override
	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}

	/**
	 * 发货挑库保存时需要判断不能超数量
	 * 
	 * @param
	 */
	public void saveAmShippingItemQuantity(AmShipping amShipping) {

		List<AmShippingItem> amshippingItemsList = this.GroupByShippingId(amShipping.getAmShippingItems());
		for (AmShippingItem amShippingItem : amshippingItemsList) {
			// 已经生成了出入库数量
			BigDecimal amShippingItemQuantity = saleNatureShippingDao
					.getAmShippingItemQuantity(amShippingItem.getShipping().getId());
			// 当前保存时单据的数量+已经生成了出入库数量（已经除了作废单据）
			amShippingItem.setQuantity(amShippingItem.getQuantity().add(amShippingItemQuantity));
			// 已经审核的发货通知单数量
			BigDecimal shippingItemQuantity = saleNatureShippingDao
					.getShippingItemQuantity(amShippingItem.getShipping().getId());
			if (amShippingItem.getQuantity().compareTo(shippingItemQuantity) == 1) {
				throw new RuntimeException("E0008-发货编号为" + amShippingItem.getShipping().getSn() + "数量过大，请修改！");
			}
		}
	}

	@Override
	public void save(AmShipping amShipping, Warehouse warehouse, Area area, List<Long> shippingIds, Long sbuId,
			Integer flag) {

		// 系统单据类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("remark", CommonVariable.INANDOUT_TYPE));
		SystemDict systemType = systemDictService.systemBillType(filters);

		List<AmShippingItem> amShippingItems = amShipping.getAmShippingItems();
		BigDecimal amount = BigDecimal.ZERO;
		BigDecimal tVolume = BigDecimal.ZERO;
		BigDecimal tWeight = BigDecimal.ZERO;
		List<String> orderSns = new ArrayList<String>();
		//发货单状态
		Long[] shippingIds1 = new Long[]{0L};
		for (Iterator<AmShippingItem> iterator = amShippingItems.iterator(); iterator.hasNext();) {
			AmShippingItem amShippingItem = iterator.next();
			if (ConvertUtil.isEmpty(amShippingItem) || (ConvertUtil.isEmpty(amShippingItem.getProductOrganization()))) {
				iterator.remove();
				continue;
			}
			//校验发货单状态
			if(!ConvertUtil.isEmpty(amShippingItem.getShipping())){
				this.checkShippingState(amShippingItem,shippingIds1);
			}
			Product product = productBaseService.find(amShippingItem.getProduct().getId());
			if (amShippingItem.getShippingItem() != null) {
				ShippingItem shippingItem1 = amShippingItem.getShippingItem();
				Warehouse pWarehouse = null;
				if (flag == 0) {
					// 大自然
					pWarehouse = warehouse;
				} else if (flag == 1) {
					// 天加
					Long pWarehouseId = (amShippingItem.getWarehouse() != null) ? amShippingItem.getWarehouse().getId()
							: null;
					pWarehouse = warehouseBaseService.find(pWarehouseId);
					if (pWarehouse == null) {
						// 请为发货项选择发货仓库
						// ExceptionUtil.throwServiceException("15182");
					}
					warehouse = pWarehouse;
				} else if (flag == 2) {
					// 康宝
					Long pWarehouseId = (amShippingItem.getWarehouse() != null) ? amShippingItem.getWarehouse().getId()
							: null;
					pWarehouse = warehouseBaseService.find(pWarehouseId);
					if (pWarehouse == null) {
						// 请为发货项选择发货仓库
						ExceptionUtil.throwServiceException("15182");
					}
					warehouse = pWarehouse;
				}
				amShippingItem.setWarehouse(pWarehouse);
				BigDecimal quantity = amShippingItem.getQuantity();
				if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 1) {
					// 发货数量必须大于0
					ExceptionUtil.throwServiceException("15154");
				}
				ShippingItem pShippingItem = shippingItemService
						.find(shippingItem1 == null ? null : shippingItem1.getId());
				Shipping pShipping = shippingService
						.find(amShippingItem.getShipping() == null ? null : amShippingItem.getShipping().getId());

				amShippingItem.setShippingItem(pShippingItem);
				if (pShipping != null) {
					amShippingItem.setShipping(pShipping);

				}
				amShippingItem.setVonderCode(pShippingItem.getVonderCode());
				amShippingItem.setModel(pShippingItem.getModel());
				amShippingItem.setName(pShippingItem.getName());
				amShippingItem.setFullName(pShippingItem.getFullName());
				amShippingItem.setDescription(pShippingItem.getDescription());
				amShippingItem.setThumbnail(pShippingItem.getThumbnail());
				amShippingItem.setProduct(product);
				amShippingItem.setStatus(0);
				amShippingItem.setShippedQuantity(BigDecimal.ZERO);
				amShippingItem.setReturnQuantity(BigDecimal.ZERO);
				// 发货单明细数据处理
				BigDecimal pVolume = product == null ? BigDecimal.ZERO
						: (product.getVolume() == null ? BigDecimal.ZERO : product.getVolume());
				BigDecimal pWeight = product == null ? BigDecimal.ZERO
						: (product.getWeight() == null ? BigDecimal.ZERO : product.getWeight());
				amShippingItem.setVolume(pVolume);
				amShippingItem.setWeight(pWeight);
				amShippingItem.setAmShipping(amShipping);
				amShippingItem.setPrice(pShippingItem.getPrice());
				amount = amount.add(amShippingItem.getQuantity().multiply(amShippingItem.getPrice()));
				// 行体积=每箱体积（产品体积）* 下单箱数；
				BigDecimal volume = pVolume.multiply(
						amShippingItem.getBoxQuantity() == null ? BigDecimal.ZERO : amShippingItem.getBoxQuantity());// 行合计体积
				tVolume = tVolume.add(volume);// 总体积
				// 行重量=行重量 * 平方数；
				BigDecimal weight = pWeight.multiply(quantity);// 行合计重量
				tWeight = tWeight.add(weight);// 总重量

				String osn = pShipping == null ? null : pShipping.getSn();
				if (osn != null && !orderSns.contains(osn)) {
					orderSns.add(osn);
				}
			} else {
				// 生产单明细
				if (!ConvertUtil.isEmpty(amShippingItem.getProductionSchedulingItem())
						&& !ConvertUtil.isEmpty(amShippingItem.getProductionSchedulingItem().getId())) {
					ProductionSchedulingItem productionSchedulingItem = productionSchedulingItemService
							.find(amShippingItem.getProductionSchedulingItem().getId());
					amShippingItem.setProductionSchedulingItem(productionSchedulingItem);
				}
				amShippingItem.setWarehouse(warehouse);
				amShippingItem.setVonderCode(product.getVonderCode());
				amShippingItem.setModel(product.getModel());
				amShippingItem.setName(product.getName());
				amShippingItem.setFullName(product.getFullName());
				amShippingItem.setDescription(product.getDetailDescription());
				amShippingItem.setThumbnail(product.getThumbnail());
				amShippingItem.setProduct(product);
				amShippingItem.setStatus(0);
				BigDecimal pVolume = product == null ? BigDecimal.ZERO
						: (product.getVolume() == null ? BigDecimal.ZERO : product.getVolume());
				BigDecimal pWeight = product == null ? BigDecimal.ZERO
						: (product.getWeight() == null ? BigDecimal.ZERO : product.getWeight());
				amShippingItem.setVolume(pVolume);
				amShippingItem.setWeight(pWeight);
				amShippingItem.setAmShipping(amShipping);
				amShippingItem.setShippedQuantity(BigDecimal.ZERO);
				amShippingItem.setReturnQuantity(BigDecimal.ZERO);
			}
			// 产品经营组织
			if (ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
					|| (!ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
							&& ConvertUtil.isEmpty(amShippingItem.getProductOrganization().getId()))) {
				amShippingItem.setProductOrganization(null);
			}
			// 色号
			if (ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
					|| (!ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
							&& ConvertUtil.isEmpty(amShippingItem.getColorNumbers().getId()))) {
				amShippingItem.setColorNumbers(null);
			}
			// 含水率
			if (ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
					|| (!ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
							&& ConvertUtil.isEmpty(amShippingItem.getMoistureContents().getId()))) {
				amShippingItem.setMoistureContents(null);
			}
			// 新旧标识
			if (ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
					|| (!ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
							&& ConvertUtil.isEmpty(amShippingItem.getNewOldLogos().getId()))) {
				amShippingItem.setNewOldLogos(null);
			}
			// 库位
			if (ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
					|| (!ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
							&& ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation().getId()))) {
				amShippingItem.setWarehouseLocation(null);
			}
			// 批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if (!ConvertUtil.isEmpty(amShippingItem.getBatch())) {
				String[] batchs = amShippingItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if (!ConvertUtil.isEmpty(batchs[i])) {
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						// 订单类型
						warehouseBillBatch.setSysType(systemType);
						// 出入库单
						warehouseBillBatch.setAmShipping(amShipping);
						// 出入库单明细
						warehouseBillBatch.setAmShippingItem(amShippingItem);
						// 批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			amShippingItem.setWarehouseBillBatchs(warehouseBillBatchList);
		}

		// 校验总账日期
		this.isCheckTotalDate(amShipping);

		if (!ConvertUtil.isEmpty(amShipping.getBillType()) && !ConvertUtil.isEmpty(amShipping.getBillType().getId())) {
			SystemDict billType = systemDictService.find(amShipping.getBillType().getId());
			if (!ConvertUtil.isEmpty(billType) && billType.getValue().equals("出仓")) {
				this.saveAmShippingItemQuantity(amShipping);
			}
		}
		amShipping.setSn(SnUtil.getAmShippingSn());
		orderFullLinkService.addFullLink(4, orderSns, amShipping.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "出库单", }) + amShipping.getSn(), null);
		save(amShipping);
		
		//操作类型
		List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "保存");
		if(actionTypeList.isEmpty() || actionTypeList.size()==0){
			ExceptionUtil.throwServiceException("操作类型不能为空");
		}
		//加载库存
		warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
	}

	@Override
	public Map<String, Object> findOperateItem(Long orderItemId, BigDecimal quantity, BigDecimal boxQuantity,
			BigDecimal branchQuantity) {
		// TODO Auto-generated method stub
		return saleNatureShippingDao.findOperateItem(orderItemId, quantity, boxQuantity, branchQuantity);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findAmShippingItemListByShippingId(String amShippingIds, Boolean isDefault) {
		return saleNatureShippingDao.findAmShippingItemListByShippingId(amShippingIds, isDefault);
	}

	@Override
	@Transactional
	public void updateAmShipping(AmShipping amShipping, AmShippingItem wAmShippingItem) {
		
		AmShipping pAmShipping = find(amShipping.getId());
		amShipping.setStatus(pAmShipping.getStatus());
		//校验出入库单状态
		this.checkAmShippingState(pAmShipping, "保存");
		//发货单Id
		Long[] shippingIds = new Long[]{0L};
		//退货单Id
		Long[] b2bReturnsIds = new Long[]{0L};
		// 系统单据类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("remark", CommonVariable.INANDOUT_TYPE));
		SystemDict systemType = systemDictService.systemBillType(filters);
		BigDecimal amount = BigDecimal.ZERO;
		BigDecimal tVolume = BigDecimal.ZERO;
		BigDecimal tWeight = BigDecimal.ZERO;
		List<AmShippingItem> amShippingItems = new ArrayList<AmShippingItem>();
		for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
			if (ConvertUtil.isEmpty(amShippingItem)
					|| (!ConvertUtil.isEmpty(amShippingItem) && ConvertUtil.isEmpty(amShippingItem.getProduct()))) {
				continue;
			}

			//校验发货单状态
			if(!ConvertUtil.isEmpty(amShippingItem.getShipping())){
				this.checkShippingState(amShippingItem,shippingIds);
			}
			//校验退货单状态
			if(!ConvertUtil.isEmpty(amShippingItem.getB2bReturns())){
				this.checkB2bReturnsState(amShippingItem,b2bReturnsIds);
			}
			// 产品
			Product product = productBaseService.find(amShippingItem.getProduct().getId());
			// 数量判断是否大于0
			if (ConvertUtil.isEmpty(amShippingItem.getQuantity()) || (!ConvertUtil.isEmpty(amShippingItem.getQuantity())
					&& amShippingItem.getQuantity().compareTo(BigDecimal.ZERO) < 1)) {
				ExceptionUtil.throwServiceException("产品编码为[" + product.getVonderCode() + "]行数量必须大于0");
			}
			// 产品名称
			amShippingItem.setName(product.getName());
			// 产品编码
			amShippingItem.setVonderCode(product.getVonderCode());
			// 产品描述
			amShippingItem.setDescription(product.getDetailDescription());
			// 产品型号
			amShippingItem.setModel(product.getModel());
			// 商品全称
			amShippingItem.setFullName(product.getFullName());
			amShippingItem.setThumbnail(product.getThumbnail());
			// 产价格
			amShippingItem.setPrice(product.getPrice());
			// 产品级别
			if (ConvertUtil.isEmpty(amShippingItem.getProductLevel())
					|| (!ConvertUtil.isEmpty(amShippingItem.getProductLevel())
							&& ConvertUtil.isEmpty(amShippingItem.getProductLevel().getId()))) {
				ExceptionUtil.throwServiceException("产品编码为[" + product.getVonderCode() + "]行的等级不能为空");
			}
			// 产品经营组织
			if (ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
					|| (!ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
							&& ConvertUtil.isEmpty(amShippingItem.getProductOrganization().getId()))) {
				amShippingItem.setProductOrganization(null);
			}
			// 体积
			if (ConvertUtil.isEmpty(amShippingItem.getVolume())) {
				amShippingItem.setVolume(BigDecimal.ZERO);
			}
			// 重量
			if (ConvertUtil.isEmpty(amShippingItem.getWeight())) {
				amShippingItem.setWeight(BigDecimal.ZERO);
			}
			// 行合计体积
			BigDecimal volume = amShippingItem.getVolume().multiply(amShippingItem.getQuantity());
			// 总体积
			tVolume = tVolume.add(volume);
			// 行合计重量
			BigDecimal weight = amShippingItem.getWeight().multiply(amShippingItem.getQuantity());
			// 总重量
			tWeight = tWeight.add(weight);
			// 色号
			if (ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
					|| (!ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
							&& ConvertUtil.isEmpty(amShippingItem.getColorNumbers().getId()))) {
				amShippingItem.setColorNumbers(null);
			}
			// 含水率
			if (ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
					|| (!ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
							&& ConvertUtil.isEmpty(amShippingItem.getMoistureContents().getId()))) {
				amShippingItem.setMoistureContents(null);
			}
			// 新旧标识
			if (ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
					|| (!ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
							&& ConvertUtil.isEmpty(amShippingItem.getNewOldLogos().getId()))) {
				amShippingItem.setNewOldLogos(null);
			}
			// 库位
			if (ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
					|| (!ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
							&& ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation().getId()))) {
				amShippingItem.setWarehouseLocation(null);
			}
			// 批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if (!ConvertUtil.isEmpty(amShippingItem.getBatch())) {
				String[] batchs = amShippingItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if (!ConvertUtil.isEmpty(batchs[i])) {
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						// 订单类型
						warehouseBillBatch.setSysType(systemType);
						// 出入库单
						warehouseBillBatch.setAmShipping(amShipping);
						// 出入库单明细
						warehouseBillBatch.setAmShippingItem(amShippingItem);
						// 批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			amShippingItem.getWarehouseBillBatchs().clear();
			amShippingItem.getWarehouseBillBatchs().addAll(warehouseBillBatchList);
			// 批次列表
			this.saveOrUpdateWarehouseBatchItem(amShipping, amShippingItem, wAmShippingItem, product);
			amShippingItem.setAmShipping(amShipping);
			amShippingItems.add(amShippingItem);
		}
		amShipping.getAmShippingItems().clear();
		amShipping.getAmShippingItems().addAll(amShippingItems);
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		amShipping.setAmount(amount);
		amShipping.setVolume(tVolume);
		amShipping.setWeight(tWeight);
		
		amShipping = update(amShipping, "objTypeId", "wfState", "wfId", "wfTempId", "status", "outTradeNo", "orderType", "store",
				"storeMember", "acceptStatus", "saleOrg", "organization");
		//操作类型
		List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "保存");
		if(actionTypeList.isEmpty() || actionTypeList.size()==0){
			ExceptionUtil.throwServiceException("操作类型不能为空");
		}
		//加载库存
		warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
	}

	/**
	 * 列表数据查询
	 * 
	 * @param
	 * @return
	 */
	public Page<Map<String, Object>> findPage(String sn, Long[] docstatus, Long[] billTypeId, Long[] warehouseId,
			Long[] saleOrgId, Long[] sbuId, Long[] productId, String sourceTypeSn, String storeMemberName,
			String startTime, String endTime, Long[] billCategoryId, Pageable pageable) {
		return saleNatureShippingDao.findPage(sn, docstatus, billTypeId, warehouseId, saleOrgId, sbuId, productId,
				sourceTypeSn, storeMemberName, startTime, endTime, billCategoryId, pageable);
	}

	@Override
	@Transactional
	public void checkWf(Long id) throws Exception {
		AmShipping amShipping = find(id);
		//校验是否有两个相同的产品行
		int size=amShipping.getAmShippingItems().size();
		for (int i = 0;i < size-1; i++){
			for (int j = i+1;j< size;j++){
				if(
						(amShipping.getAmShippingItems().get(i).getProduct().getId().equals(amShipping.getAmShippingItems().get(j).getProduct().getId()))
								&& (amShipping.getAmShippingItems().get(i).getProductOrganization().getId().equals(amShipping.getAmShippingItems().get(j).getProductOrganization().getId()))
								&& (amShipping.getAmShippingItems().get(i).getProductLevel().getId().equals(amShipping.getAmShippingItems().get(j).getProductLevel().getId()))
								&& (amShipping.getAmShippingItems().get(i).getNewOldLogos().getId().equals(amShipping.getAmShippingItems().get(j).getNewOldLogos().getId()))
								&& (amShipping.getAmShippingItems().get(i).getMoistureContents().getId().equals(amShipping.getAmShippingItems().get(j).getMoistureContents().getId()))
								&& (amShipping.getAmShippingItems().get(i).getColorNumbers().getId().equals(amShipping.getAmShippingItems().get(j).getColorNumbers().getId()))
				){
					throw new RuntimeException("第"+(i+1)+"行于第"+(j+1)+"行的产品相同，请分开两个单开单");
				}
			}
		}
		//校验出入库单状态
		this.checkAmShippingState(amShipping,"审核");
		List<AmShippingItem> amshippingItems = amShipping.getAmShippingItems();
		if(amshippingItems.isEmpty() || amshippingItems.size()==0){
			return;
		}
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		// 批次列表数据校验
		this.isCheckWarehouseBatchItemQuantity(amShipping);
		// 库存校验
		this.isCheckStock(amShipping,0);
		Long[] shippingIds = new Long[]{0L};
	    if(amShipping.getStatus()==2) {
            throw new RuntimeException("E0009-单号【" + amShipping.getSn() + "】的已经作废，不能发货！");
        }
		
		if(amShipping.getWarehouse().getIsSendJiaWaYun()&&amShipping.getStatus()==0){
			for (AmShippingItem amShippingItem : amshippingItems) {
				this.checkShippingState(amShippingItem,shippingIds);
				ShippingItem shippingItem = amShippingItem.getShippingItem();
				OrderItem orderItem = shippingItem.getOrderItem();
				Order order = orderItem.getOrder();
				Shipping shipping = shippingItem.getShipping();
				if (amShippingItem.getQuantity().add(shippingItem.getShippedQuantity())
						.compareTo(shippingItem.getQuantity()) == 1) {
					throw new RuntimeException("E0007-产品编码为" + amShippingItem.getVonderCode() + "的出库数量过大");
				}
//				orderService.update(order);
//				this.checkShipping(shippingItem.getShipping().getId());
//				this.checkOrder(shippingItem.getOrder().getId());
				if (ConvertUtil.isEmpty(orderItem.getPrice())) {
					throw new RuntimeException("E0008-产品编码为【" + amShippingItem.getVonderCode() + "】单价不能空");
				}
		        if(shippingItem.getStatus()==2) {
                    throw new RuntimeException("E0009-产品编码为【" + amShippingItem.getVonderCode() + "】的行已经作废，不能发货！");
                }
			}
		     if(amShipping.getStatus()!=6) {
	                amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
	            }
			intfOrderMessageToService.saveAmShippingIntf(amShipping,0);
            intfLogisticsJob.shippingInft();
		}else {
			for (AmShippingItem amShippingItem : amshippingItems) {
				//            //判断是否小数
				//            if (String.valueOf(amShippingItem.getBranchQuantity()).indexOf(".")>-1){
				//                throw new RuntimeException("E0009-产品编码为【" + amShippingItem.getVonderCode() + "】的行支数不能为小数！");
				//            }
				//校验发货单状态
				this.checkShippingState(amShippingItem, shippingIds);
				ShippingItem shippingItem = amShippingItem.getShippingItem();
				OrderItem orderItem = shippingItem.getOrderItem();
				Order order = orderItem.getOrder();
				Shipping shipping = shippingItem.getShipping();
				if (amShippingItem.getQuantity().add(shippingItem.getShippedQuantity())
						.compareTo(shippingItem.getQuantity()) == 1) {
					throw new RuntimeException("E0007-产品编码为" + amShippingItem.getVonderCode() + "的出库数量过大");
				} else if (amShippingItem.getQuantity().add(shippingItem.getShippedQuantity())
						.compareTo(shippingItem.getQuantity()) == 0) {
					shippingItem.setShippedQuantity(
							amShippingItem.getQuantity().add(shippingItem.getShippedQuantity() == null ? BigDecimal.ZERO
									: shippingItem.getShippedQuantity()));
					if(amShippingItem.getBoxQuantity() != null){
						shippingItem.setShippedBoxQuantity(amShippingItem.getBoxQuantity()
                            .add(shippingItem.getShippedBoxQuantity() == null ? BigDecimal.ZERO
                                    : shippingItem.getShippedBoxQuantity()));
					}
					shippingItem.setShippedBranchQuantity(amShippingItem.getBranchQuantity()
							.add(shippingItem.getShippedBranchQuantity() == null ? BigDecimal.ZERO
									: shippingItem.getShippedBranchQuantity()));
					orderItem.setShippedQuantity(amShippingItem.getQuantity().add(
							orderItem.getShippedQuantity() == null ? BigDecimal.ZERO : orderItem.getShippedQuantity()));
				} else {
					shippingItem.setShippedQuantity(
							amShippingItem.getQuantity().add(shippingItem.getShippedQuantity() == null ? BigDecimal.ZERO
									: shippingItem.getShippedQuantity()));
                    if(amShippingItem.getBoxQuantity() != null) {
                        shippingItem.setShippedBoxQuantity(amShippingItem.getBoxQuantity()
                                .add(shippingItem.getShippedBoxQuantity() == null ? BigDecimal.ZERO
                                        : shippingItem.getShippedBoxQuantity()));
                    }
					shippingItem.setShippedBranchQuantity(amShippingItem.getBranchQuantity()
							.add(shippingItem.getShippedBranchQuantity() == null ? BigDecimal.ZERO
									: shippingItem.getShippedBranchQuantity()));
					orderItem.setShippedQuantity(amShippingItem.getQuantity().add(
							orderItem.getShippedQuantity() == null ? BigDecimal.ZERO : orderItem.getShippedQuantity()));
				}
				shippingItem.setShippedTime(new Date());
				if(shippingItem.getStatus()==2) {
				    throw new RuntimeException("E0009-产品编码为【" + amShippingItem.getVonderCode() + "】的行已经作废，不能发货！");
				}
				    
				orderItemService.update(orderItem);
				shippingService.update(shipping);
				shippingItemService.update(shippingItem);
				orderService.update(order);
				this.checkShipping(shippingItem.getShipping().getId());
				this.checkOrder(shippingItem.getOrder().getId());
				amShippingItem.setStatus(1);
				if (ConvertUtil.isEmpty(orderItem.getPrice())) {
					throw new RuntimeException("E0008-产品编码为【" + amShippingItem.getVonderCode() + "】单价不能空");
				}
				// 客户充值
				CustomerRecharge customerRecharge = new CustomerRecharge();
				// 客户
				customerRecharge.setStore(amShipping.getStore());
				// 经营组织
				customerRecharge.setOrganization(amShippingItem.getProductOrganization());
				// sbu
				customerRecharge.setSbu(amShipping.getSbu());
				customerRecharge = customerRechargeService.saveOrUpdate(customerRecharge);
				if (!ConvertUtil.isEmpty(customerRecharge) && !ConvertUtil.isEmpty(customerRecharge.getId())) {
					// 发货金额
					customerRecharge.setShipmentBalance(customerRecharge.getShipmentBalance()
							.add(orderItem.getPrice().multiply(amShippingItem.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP)));
					// 订单金额
					customerRecharge.setOrderBalance(customerRecharge.getOrderBalance()
							.subtract(orderItem.getPrice().multiply(amShippingItem.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP)));
					customerRechargeService.update(customerRecharge);
				} else {
					throw new RuntimeException("流程审批异常，请管理员进行维护");
				}
				
				//LINK5期接口
				if(shipping.getStore().getIsToLink5()==null ? false: shipping.getStore().getIsToLink5() 
						&& !(shipping.getWarehouse().getIsOrderWarehousing()==null ? false:shipping.getWarehouse().getIsOrderWarehousing())) {
					shippingInfoToLink5Service.saveShippingInfoTo(shipping);
				}
				
			}
		     if(amShipping.getStatus()!=6) {
	                amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
	            }
			amShipping.setStatus(1);
			update(amShipping);

			//操作类型
			List<SystemDict> actionTypeList = roleJurisdictionUtil.getSystemDictList("actionType", "审核");
			if (actionTypeList.isEmpty() || actionTypeList.size() == 0) {
				ExceptionUtil.throwServiceException("操作类型不能为空");
			}
			//加载库存
			warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
			updateShippingPriceDifference(amShipping);
		}
	}

	@Override
	@Transactional
	public void shippingAudit(Long id) throws Exception {
		AmShipping amShipping = find(id);
		//校验是否有两个相同的产品行
		int size=amShipping.getAmShippingItems().size();
		for (int i = 0;i < size-1; i++){
			for (int j = i+1;j< size;j++){
				if(
						(amShipping.getAmShippingItems().get(i).getProduct().getId().equals(amShipping.getAmShippingItems().get(j).getProduct().getId()))
								&& (amShipping.getAmShippingItems().get(i).getProductOrganization().getId().equals(amShipping.getAmShippingItems().get(j).getProductOrganization().getId()))
								&& (amShipping.getAmShippingItems().get(i).getProductLevel().getId().equals(amShipping.getAmShippingItems().get(j).getProductLevel().getId()))
								&& (amShipping.getAmShippingItems().get(i).getNewOldLogos().getId().equals(amShipping.getAmShippingItems().get(j).getNewOldLogos().getId()))
								&& (amShipping.getAmShippingItems().get(i).getMoistureContents().getId().equals(amShipping.getAmShippingItems().get(j).getMoistureContents().getId()))
								&& (amShipping.getAmShippingItems().get(i).getColorNumbers().getId().equals(amShipping.getAmShippingItems().get(j).getColorNumbers().getId()))
				){
					throw new RuntimeException("第"+(i+1)+"行于第"+(j+1)+"行的产品相同，请分开两个出库单出单");
				}
			}
		}
		//校验出入库单状态
		this.checkAmShippingState(amShipping,"审核");
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		// 批次列表数据校验
		this.isCheckWarehouseBatchItemQuantity(amShipping);
		// 库存校验 单据类型判断
		if (amShipping.getBillType().getValue().equals("出仓")) {
			this.isCheckStock(amShipping,0);
			if(amShipping.getArea() == null){
				ExceptionUtil.throwServiceException("请选择收货地区");
			}
		}else if(amShipping.getBillType().getValue().equals("入仓")) {
			this.isCheckStock(amShipping,1);
		}
		String isSended="no";
        List<AmShippingItem> amShippingItemList01 = amShipping.getAmShippingItems();
		if(amShippingItemList01.get(0).getMovelibraryIssue() !=null){
            if(amShippingItemList01.get(0).getMovelibraryIssue().getReceiveWarehouse().getIsSendJiaWaYun()&&amShipping.getStatus()==0) {
                intfOrderMessageToService.saveAmShippingIntf(amShipping,0);
                isSended="yes";
            }
        }
		if(isSended.equals("no")) {
	        if(amShipping.getStatus()!=6) {
                amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
            }
		    List<AmShippingItem> amShippingItemList = amShipping.getAmShippingItems();
	        for (AmShippingItem amShippingItem : amShippingItemList) {
	            amShippingItem.setStatus(1);
	        }
			amShipping.setStatus(1);
			update(amShipping);
	        //操作类型
	        List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "审核");
	        if(actionTypeList.isEmpty() || actionTypeList.size()==0){
	            ExceptionUtil.throwServiceException("操作类型不能为空");
	        }
	        //加载库存
	        warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
		}
		
	}

	@Override
	@Transactional
	public void returnReceivesave(AmShipping amShipping, Warehouse warehouse) {
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		List<AmShippingItem> amShippingItems = amShipping.getAmShippingItems();
		List<String> orderSns = new ArrayList<String>();
		//退货单Id
		Long[] b2bReturnsIds = new Long[]{0L};
		for (Iterator<AmShippingItem> iterator = amShippingItems.iterator(); iterator.hasNext();) {
			AmShippingItem amShippingItem = iterator.next();
			if (ConvertUtil.isEmpty(amShippingItem) || 
					(!ConvertUtil.isEmpty(amShippingItem) && ConvertUtil.isEmpty(amShippingItem.getProduct()))) {
				iterator.remove();
				continue;
			}
			//产品
			Product product = productBaseService.find(amShippingItem.getProduct().getId());
			if(ConvertUtil.isEmpty(product)){
				ExceptionUtil.throwServiceException("该产品不存在");
			}
			//校验退货单状态
			this.checkB2bReturnsState(amShippingItem,b2bReturnsIds);
			amShippingItem.setWarehouse(warehouse);
			amShippingItem.setVonderCode(product.getVonderCode());
			amShippingItem.setModel(product.getModel());
			amShippingItem.setName(product.getName());
			amShippingItem.setFullName(product.getFullName());
			amShippingItem.setDescription(product.getDetailDescription());
			amShippingItem.setThumbnail(product.getThumbnail());
			amShippingItem.setProduct(product);
			amShippingItem.setStatus(0);
			BigDecimal pVolume = product == null ? BigDecimal.ZERO
					: (product.getVolume() == null ? BigDecimal.ZERO : product.getVolume());
			BigDecimal pWeight = product == null ? BigDecimal.ZERO
					: (product.getWeight() == null ? BigDecimal.ZERO : product.getWeight());
			amShippingItem.setVolume(pVolume);
			amShippingItem.setWeight(pWeight);
			amShippingItem.setAmShipping(amShipping);
			amShippingItem.setShippedQuantity(BigDecimal.ZERO);
			amShippingItem.setReturnQuantity(BigDecimal.ZERO);
			// 产品经营组织
			if (ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
					|| (!ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
							&& ConvertUtil.isEmpty(amShippingItem.getProductOrganization().getId()))) {
				amShippingItem.setProductOrganization(null);
			}
			// 色号
			if (ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
					|| (!ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
							&& ConvertUtil.isEmpty(amShippingItem.getColorNumbers().getId()))) {
				amShippingItem.setColorNumbers(null);
			}
			// 含水率
			if (ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
					|| (!ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
							&& ConvertUtil.isEmpty(amShippingItem.getMoistureContents().getId()))) {
				amShippingItem.setMoistureContents(null);
			}
			// 新旧标识
			if (ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
					|| (!ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
							&& ConvertUtil.isEmpty(amShippingItem.getNewOldLogos().getId()))) {
				amShippingItem.setNewOldLogos(null);
			}
			// 库位
			if (ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
					|| (!ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
							&& ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation().getId()))) {
				amShippingItem.setWarehouseLocation(null);
			}
		}
		amShipping.setSn(SnUtil.getAmShippingSn());
		orderFullLinkService.addFullLink(4, orderSns, amShipping.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "出库单", }) + amShipping.getSn(), null);
		save(amShipping);
		//操作类型
		List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "保存");
		if(actionTypeList.isEmpty() || actionTypeList.size()==0){
			ExceptionUtil.throwServiceException("操作类型不能为空");
		}
		//加载库存
		warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
	}

	@Override
	public void checkB2b_Return(Long b2b_Return_id) throws Exception {
		/* 退货数量 */
		BigDecimal returnedQuantity = BigDecimal.ZERO;
		/* 数量 */
		BigDecimal Quantity = BigDecimal.ZERO;
		B2bReturns b2bReturns = b2bReturnsService.find(b2b_Return_id);
		if (b2bReturns != null) {
			for (B2bReturnsItem b2bReturnsItem : b2bReturns.getB2bReturnsItems()) {
				returnedQuantity = returnedQuantity.add(b2bReturnsItem.getReturnedQuantity());
				Quantity = Quantity.add(b2bReturnsItem.getQuantity());
			}

			if (returnedQuantity.compareTo(Quantity) == 0) {
				b2bReturns.setStatus(5);
			} else if (returnedQuantity.compareTo(Quantity) == -1 && returnedQuantity.compareTo(BigDecimal.ZERO) != 0) {
				b2bReturns.setStatus(4);
			}

			b2bReturnsService.update(b2bReturns);
			
		}
		
	}

	@Override
	@Transactional
	public void returnReceiveCheckWf(Long id) throws Exception {
		AmShipping amShipping = find(id);
		//校验出入库单状态
		this.checkAmShippingState(amShipping, "审核");
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		// 批次列表数据校验
		this.isCheckWarehouseBatchItemQuantity(amShipping);
		// 库存校验
		this.isCheckStock(amShipping,1);
		List<AmShippingItem> amshippingItems = amShipping.getAmShippingItems();
		if(amshippingItems.isEmpty() || amshippingItems.size()==0){
			return;
		}
		//退货单Id
		Long[] b2bReturnsIds = new Long[]{0L};
		if(amShipping.getWarehouse().getIsSendJiaWaYun()&&amShipping.getStatus()==0){
			for (AmShippingItem amShippingItem : amshippingItems) {
				//校验退货单状态
				this.checkB2bReturnsState(amShippingItem,b2bReturnsIds);
				B2bReturnsItem b2bReturnsItem = amShippingItem.getB2bReturnsItem();
				B2bReturns b2bReturns = amShippingItem.getB2bReturns();
				if (amShippingItem.getQuantity().add(b2bReturnsItem.getReturnedQuantity())
						.compareTo(b2bReturnsItem.getQuantity()) == 1) {
					ExceptionUtil.throwServiceException("E0009-产品编码为【" + b2bReturnsItem.getVonderCode() + "】退货数量过大");
				}
//				else if (amShippingItem.getQuantity().add(b2bReturnsItem.getReturnedQuantity())
//						.compareTo(b2bReturnsItem.getQuantity()) == 0) {
//					b2bReturnsItem.setReturnedQuantity(amShippingItem.getQuantity()
//							.add(b2bReturnsItem.getReturnedQuantity() == null ? BigDecimal.ZERO
//									: b2bReturnsItem.getReturnedQuantity()));
//					b2bReturnsItem.setReturnedBoxQuantity(amShippingItem.getBoxQuantity()
//							.add(b2bReturnsItem.getReturnedBoxQuantity() == null ? BigDecimal.ZERO
//									: b2bReturnsItem.getReturnedBoxQuantity()));
//					b2bReturnsItem.setReturnedBranchQuantity(amShippingItem.getBranchQuantity()
//							.add(b2bReturnsItem.getReturnedBranchQuantity() == null ? BigDecimal.ZERO
//									: b2bReturnsItem.getReturnedBranchQuantity()));
//				} else {
//					b2bReturnsItem.setReturnedQuantity(amShippingItem.getQuantity()
//							.add(b2bReturnsItem.getReturnedQuantity() == null ? BigDecimal.ZERO
//									: b2bReturnsItem.getReturnedQuantity()));
//					b2bReturnsItem.setReturnedBoxQuantity(amShippingItem.getBoxQuantity()
//							.add(b2bReturnsItem.getReturnedBoxQuantity() == null ? BigDecimal.ZERO
//									: b2bReturnsItem.getReturnedBoxQuantity()));
//					b2bReturnsItem.setReturnedBranchQuantity(amShippingItem.getBranchQuantity()
//							.add(b2bReturnsItem.getReturnedBranchQuantity() == null ? BigDecimal.ZERO
//									: b2bReturnsItem.getReturnedBranchQuantity()));
//				}
//				b2bReturnsItemService.update(b2bReturnsItem);
//				b2bReturnsService.update(b2bReturns);
//				this.checkB2b_Return(b2bReturns.getId());
				if (ConvertUtil.isEmpty(b2bReturnsItem.getPrice())) {
					ExceptionUtil.throwServiceException("E0010-产品编码为【" + b2bReturnsItem.getVonderCode() + "】单价不能空");
				}
//				// 客户充值
//				CustomerRecharge customerRecharge = new CustomerRecharge();
//				// 客户
//				customerRecharge.setStore(amShipping.getStore());
//				// 经营组织
//				customerRecharge.setOrganization(amShippingItem.getProductOrganization());
//				// sbu
//				customerRecharge.setSbu(amShipping.getSbu());
//				customerRecharge = customerRechargeService.saveOrUpdate(customerRecharge);
//				if (!ConvertUtil.isEmpty(customerRecharge) && !ConvertUtil.isEmpty(customerRecharge.getId())) {
//					// 退货金额
//					customerRecharge.setReturnBalance(customerRecharge.getReturnBalance()
//							.add(b2bReturnsItem.getPrice().multiply(amShippingItem.getQuantity())));
//					customerRechargeService.update(customerRecharge);
//					//如果是售后单
//					if (b2bReturns.getAftersale()!=null){
//						try{
//							Aftersale aftersale = b2bReturns.getAftersale();
//							//当前单实际退货接收金额
//							BigDecimal acturalReturnAmount = b2bReturnsItem.getPrice().multiply(amShippingItem.getQuantity());
//							if (aftersale.getActuralReturnAmount()!=null){
//								acturalReturnAmount = aftersale.getActuralReturnAmount().add(acturalReturnAmount);
//							}
//							//同步数据至中板
//							synchronousReturnInfo(b2bReturns,acturalReturnAmount);
//							//售后信息
//							aftersale.setActuralReturnAmount(acturalReturnAmount);
//							aftersale.setB2bReturnSn(b2bReturns.getSn());
//							aftersale.setB2bReturnStatus(b2bReturns.getStatus());
//							aftersale.setReturnDate(b2bReturns.getCheckDate());
//							aftersaleService.update(aftersale);
//
//						}catch (Exception e){
//							LogUtils.error("同步退货信息至中板异常:",e);
//						}
//					}
//				} else {
//					ExceptionUtil.throwServiceException("流程审批异常，请管理员进行维护");
//				}
			}
			amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
			update(amShipping);
			intfOrderMessageToService.saveAmShippingIntf(amShipping,0);
            intfLogisticsJob.shippingInft();
		}else{
			for (AmShippingItem amShippingItem : amshippingItems) {
				//校验退货单状态
				this.checkB2bReturnsState(amShippingItem,b2bReturnsIds);
				B2bReturnsItem b2bReturnsItem = amShippingItem.getB2bReturnsItem();
				B2bReturns b2bReturns = amShippingItem.getB2bReturns();
				if (amShippingItem.getQuantity().add(b2bReturnsItem.getReturnedQuantity())
						.compareTo(b2bReturnsItem.getQuantity()) == 1) {
					ExceptionUtil.throwServiceException("E0009-产品编码为【" + b2bReturnsItem.getVonderCode() + "】退货数量过大");
				} else if (amShippingItem.getQuantity().add(b2bReturnsItem.getReturnedQuantity())
						.compareTo(b2bReturnsItem.getQuantity()) == 0) {
					b2bReturnsItem.setReturnedQuantity(amShippingItem.getQuantity()
							.add(b2bReturnsItem.getReturnedQuantity() == null ? BigDecimal.ZERO
									: b2bReturnsItem.getReturnedQuantity()));
					b2bReturnsItem.setReturnedBoxQuantity(amShippingItem.getBoxQuantity()
							.add(b2bReturnsItem.getReturnedBoxQuantity() == null ? BigDecimal.ZERO
									: b2bReturnsItem.getReturnedBoxQuantity()));
					b2bReturnsItem.setReturnedBranchQuantity(amShippingItem.getBranchQuantity()
							.add(b2bReturnsItem.getReturnedBranchQuantity() == null ? BigDecimal.ZERO
									: b2bReturnsItem.getReturnedBranchQuantity()));
				} else {
					b2bReturnsItem.setReturnedQuantity(amShippingItem.getQuantity()
							.add(b2bReturnsItem.getReturnedQuantity() == null ? BigDecimal.ZERO
									: b2bReturnsItem.getReturnedQuantity()));
					b2bReturnsItem.setReturnedBoxQuantity(amShippingItem.getBoxQuantity()
							.add(b2bReturnsItem.getReturnedBoxQuantity() == null ? BigDecimal.ZERO
									: b2bReturnsItem.getReturnedBoxQuantity()));
					b2bReturnsItem.setReturnedBranchQuantity(amShippingItem.getBranchQuantity()
							.add(b2bReturnsItem.getReturnedBranchQuantity() == null ? BigDecimal.ZERO
									: b2bReturnsItem.getReturnedBranchQuantity()));
				}
				b2bReturnsItemService.update(b2bReturnsItem);
				b2bReturnsService.update(b2bReturns);
				this.checkB2b_Return(b2bReturns.getId());
				amShippingItem.setStatus(1);
				if (ConvertUtil.isEmpty(b2bReturnsItem.getPrice())) {
					ExceptionUtil.throwServiceException("E0010-产品编码为【" + b2bReturnsItem.getVonderCode() + "】单价不能空");
				}
				// 客户充值
				CustomerRecharge customerRecharge = new CustomerRecharge();
				// 客户
				customerRecharge.setStore(amShipping.getStore());
				// 经营组织
				customerRecharge.setOrganization(amShippingItem.getProductOrganization());
				// sbu
				customerRecharge.setSbu(amShipping.getSbu());
				customerRecharge = customerRechargeService.saveOrUpdate(customerRecharge);
				if (!ConvertUtil.isEmpty(customerRecharge) && !ConvertUtil.isEmpty(customerRecharge.getId())) {
					// 退货金额
					customerRecharge.setReturnBalance(customerRecharge.getReturnBalance()
							.add(b2bReturnsItem.getPrice().multiply(amShippingItem.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP)));
					customerRechargeService.update(customerRecharge);
					//如果是售后单
					if (b2bReturns.getAftersale()!=null){
						try{
							Aftersale aftersale = b2bReturns.getAftersale();
							//当前单实际退货接收金额
							BigDecimal acturalReturnAmount = b2bReturnsItem.getPrice().multiply(amShippingItem.getQuantity());
							if (aftersale.getActuralReturnAmount() != null){
								acturalReturnAmount = aftersale.getActuralReturnAmount().add(acturalReturnAmount);
							}
							BigDecimal acturalReturnQuantity = b2bReturns.getQuantity();
							if (aftersale.getActuralReturnQuantity() != null){
								acturalReturnQuantity = aftersale.getActuralReturnQuantity().add(acturalReturnQuantity);
							}
							//同步数据至中板
							//synchronousReturnInfo(b2bReturns,acturalReturnAmount);
							//售后信息
							aftersale.setActuralReturnAmount(acturalReturnAmount);
							aftersale.setActuralReturnQuantity(acturalReturnQuantity);
							aftersale.setB2bReturnSn(b2bReturns.getSn());
							aftersale.setB2bReturnStatus(b2bReturns.getStatus());
							aftersale.setReturnDate(b2bReturns.getCheckDate());
							aftersaleService.update(aftersale);
							clientPushFourHundredService.pushAftersale(aftersale);//推送到荣大
						}catch (Exception e){
							e.printStackTrace();
							LogUtils.error("同步退货信息至中板异常:",e);
						}

					}
				} else {
					ExceptionUtil.throwServiceException("流程审批异常，请管理员进行维护");
				}
			}
			if(amShipping.getStatus()!=6) {
			    amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
			}
			amShipping.setStatus(1);

			update(amShipping);
			
			
			//操作类型
	        List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "审核");
	        if(actionTypeList.isEmpty() || actionTypeList.size()==0){
	            ExceptionUtil.throwServiceException("操作类型不能为空");
	        }
	        //加载库存
	        warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
	        updateB2BReturnPriceDifference(amShipping);
		}
	}

    /**
     * 同步退货信息到中板
     * @param b2bReturns 退货实体
     * @param acturalReturnAmount 实际退货金额
     */
    public void synchronousReturnInfo(B2bReturns b2bReturns,BigDecimal acturalReturnAmount) {
        String path = "/intf/aftersale/syncB2bReturnToAftersale.jhtml";
        String url = CommonUtil.host +path;
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", CommonUtil.token);
        headers.put("Content-Type","application/json");
        Map<String, Object> info = new HashMap<String, Object>();
        String sn = b2bReturns.getAftersale().getSn();
        info.put("aftersaleSn",sn);     //售后单据号
        info.put("b2bReturnStatus",b2bReturns.getStatus()); //单据状态
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        info.put("b2bReturnDate",df.format(b2bReturns.getCheckDate())); //退货时间
        info.put("b2bReturnSn",b2bReturns.getSn()); //退货单号
        info.put("acturalReturnAmount",acturalReturnAmount); //实际退货金额
        LogUtils.info("同步退货单数据到中板："+JsonUtils.toJson(info));
        String msg = HttpClientUtil.post(url,
                JsonUtils.toJson(info),
                headers);

    }

	@Override
	public void checkShipping(Long shipping_id) throws Exception {
		/* 退货数量 */
		BigDecimal shippingQuantity = BigDecimal.ZERO;
		/* 数量 */
		BigDecimal Quantity = BigDecimal.ZERO;
		Shipping shipping = shippingService.find(shipping_id);
		if (shipping != null) {
			for (ShippingItem shippingItems : shipping.getShippingItems()) {
				shippingQuantity = shippingQuantity.add(shippingItems.getShippedQuantity());
				Quantity = Quantity.add(shippingItems.getQuantity());
			}
			if (shippingQuantity.compareTo(Quantity) == 0) {
				shipping.setStatus(4);
			} else if (shippingQuantity.compareTo(Quantity) == -1 && Quantity.compareTo(BigDecimal.ZERO) != 0) {
				shipping.setStatus(3);
			}
			shippingService.update(shipping);
		}
	}

	public void checkOrder(Long Order_id) throws Exception {
		/* 订单数量 */
		BigDecimal shippingQuantity = BigDecimal.ZERO;
		/* 数量 */
		BigDecimal Quantity = BigDecimal.ZERO;
		/* 关闭数量 */
		BigDecimal ClosedQuantity = BigDecimal.ZERO;
		Order order = orderService.find(Order_id);
		if (order != null) {
			for (OrderItem orderItems : order.getOrderItems()) {
				shippingQuantity = shippingQuantity.add(orderItems.getShippedQuantity());
				Quantity = Quantity.add(orderItems.getQuantity());
				String closed_quantity_nb = orderCloseService.findClosedQuantityByOrderItemId(orderItems.getId())
						.get("closed_quantity_nb").toString();
				if (closed_quantity_nb == null || closed_quantity_nb == "") {
					ClosedQuantity = ClosedQuantity.add(BigDecimal.ZERO);
				} else {
					ClosedQuantity = ClosedQuantity.add(new BigDecimal(closed_quantity_nb));
				}
			}
			if (shippingQuantity.compareTo(Quantity.subtract(ClosedQuantity)) == 0) {
				order.setOrderStatus(OrderStatus.completed);
				order.setShippingStatus(ShippingStatus.shipped);
			} else {
				order.setOrderStatus(OrderStatus.audited);
				order.setShippingStatus(ShippingStatus.partialShipment);
			}
			orderService.update(order);
		}
	}

	@Override
	public List<Map<String, Object>> findAmShippingList(String sn, Long[] docstatus, Long[] billTypeId,
			Long[] warehouseId, Long[] saleOrgId, Long[] sbuId, Long[] productId, String sourceTypeSn,
			String storeMemberName, String startTime, String endTime, Long[] ids, Integer page, Integer size,
			Long[] billCategoryId) {

		return saleNatureShippingDao.findAmShippingList(sn, docstatus, billTypeId, warehouseId, saleOrgId, sbuId,
				productId, sourceTypeSn, storeMemberName, startTime, endTime, ids, page, size, billCategoryId);
	}

	private String FONT_PATH = "/usr/share/fonts/self/simsun.ttf";

	/**
	 * 生成二维码(QRCode)图片的公共方法
	 * 
	 * @param content 存储内容
	 * @param imgType 图片类型
	 * @param size    二维码尺寸
	 * @return
	 */
	public BufferedImage qRCodeCommon(String content, String imgType, int size) {
		BufferedImage bufImg = null;
		try {
			Qrcode qrcodeHandler = new Qrcode();
			// 设置二维码排错率，可选L(7%)、M(15%)、Q(25%)、H(30%)，排错率越高可存储的信息越少，但对二维码清晰度的要求越小
			qrcodeHandler.setQrcodeErrorCorrect('M');
			qrcodeHandler.setQrcodeEncodeMode('B');
			// 设置设置二维码尺寸，取值范围1-40，值越大尺寸越大，可存储的信息越大
			qrcodeHandler.setQrcodeVersion(size);
			// 获得内容的字节数组，设置编码格式
			byte[] contentBytes = content.getBytes("utf-8");
			// 图片尺寸
			int imgSize = 67 + 12 * (size - 1);
			bufImg = new BufferedImage(imgSize, imgSize, BufferedImage.TYPE_INT_RGB);
			Graphics2D gs = bufImg.createGraphics();
			// 设置背景颜色
			gs.setBackground(Color.WHITE);
			gs.clearRect(0, 0, imgSize, imgSize);

			// 设定图像颜色> BLACK
			gs.setColor(Color.BLACK);
			// 设置偏移量，不设置可能导致解析出错
			int pixoff = 2;
			// 输出内容> 二维码
			if (contentBytes.length > 0 && contentBytes.length < 800) {
				boolean[][] codeOut = qrcodeHandler.calQrcode(contentBytes);
				for (int i = 0; i < codeOut.length; i++) {
					for (int j = 0; j < codeOut.length; j++) {
						if (codeOut[j][i]) {
							gs.fillRect(j * 3 + pixoff, i * 3 + pixoff, 3, 3);
						}
					}
				}
			} else {
				throw new Exception("QRCode content bytes  length = " + contentBytes.length + " not in [0, 800].");
			}
			gs.dispose();
			bufImg.flush();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return bufImg;
	}

	public String createRQ(String url, String sn) throws IOException {
		String content = sn;
		BufferedImage bufImg = qRCodeCommon(content, "jpg", 12);

		File imgFile = new File(servletContext.getRealPath(url));// 二维码
		// 生成二维码QRCode图片
		ImageIO.write(bufImg, "jpg", imgFile);
		return url;
	}

	private void buildBatchPdf(AmShipping amShipping, String pdfStaticPath, String template_path) throws Exception {
		String PdfTemplatePath = servletContext.getRealPath(template_path);
		String outputFile = servletContext.getRealPath(pdfStaticPath);

		// 客户
		Store store = null;
		if (amShipping.getStore() != null) {
			store = amShipping.getStore();
		}
		// 仓库
		Warehouse warehouse = null;
		if (amShipping.getWarehouse() != null) {
			warehouse = amShipping.getWarehouse();
		}
		// 机构
		SaleOrg saleOrg = null;
		if (amShipping.getSaleOrg() != null) {
			saleOrg = amShipping.getSaleOrg();
		}
		// 需要生成PDF
		FileOutputStream fos = new FileOutputStream(outputFile);
		// 出入单明细表
		List<AmShippingItem> amShippingItemList = amShipping.getAmShippingItems();
		// 统计出入单批次列表明细总记录数
		int itemSize = 0;
		if (!amShippingItemList.isEmpty() && amShippingItemList.size() > 0) {
			for (int i = 0; i < amShippingItemList.size(); i++) {
				List<WarehouseBatchItem> warehouseBatchItems = amShippingItemList.get(i).getWarehouseBatchItems();
				itemSize = itemSize + warehouseBatchItems.size();
			}
		}
		int pageSize = 5;
		int pdfPage = itemSize % pageSize == 0 ? itemSize / pageSize : (itemSize / pageSize) + 1;
		if (pdfPage == 0) {
			ExceptionUtil.throwServiceException("批次列表为空，无法查看PDF，请先添加批次");
		}
		// 用于存储每页生成PDF流
		ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pdfPage];
		BaseFont bf = BaseFont.createFont(FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
		for (int i = 0; i < pdfPage; i++) {
			baos[i] = new ByteArrayOutputStream();
			PdfReader reader = new PdfReader(PdfTemplatePath);
			PdfStamper stamp = new PdfStamper(reader, baos[i]);
			ArrayList<BaseFont> fontList = new ArrayList<BaseFont>();
			fontList.add(bf);
			AcroFields form = stamp.getAcroFields();
			// 列数 3 1-3 4
			int column = 0;
			int forNumber = i * pageSize + pageSize > itemSize ? itemSize : i * pageSize + pageSize;
			form.setSubstitutionFonts(fontList);
			// 总页码
			form.setField("totalPage", pdfPage + "");
			// 页次
			form.setField("page_info", (i + 1) + "");
			// 单据类型
			form.setField("bill_type_name", amShipping.getBillType() == null ? ""
					: ConvertUtil.toEmpty(warehouse.getName() + amShipping.getBillType().getValue() + "单"));
			// 单据编号
			form.setField("sn", amShipping.getSn());
			// 预发货通知单据编号
			form.setField("shippingSn", "");
			// 单据类别
			form.setField("billCategory", amShipping.getBillCategory() == null ? "" : amShipping.getBillCategory().getValue());
            // 备 注
            form.setField("memo", ConvertUtil.toEmpty(amShipping.getMemo()));
			// 柜号/车号
			form.setField("containerNumber",
					amShipping.getContainerNumber() == null ? ""
							: amShipping.getContainerNumber() + "/" + amShipping.getWagonNumber() == null ? ""
									: amShipping.getWagonNumber());
			// 所属事业部
			form.setField("org_name", amShipping.getWarehouse().getManagementOrganization().getName());
			// 创建日期
			String create_date = DateUtil.convert(amShipping.getCreateDate(), "yyyy-MM-dd");
			form.setField("create_date", create_date);
			// 单据日期
			if (!ConvertUtil.isEmpty(amShipping.getBillDate())) {
				form.setField("bill_date", DateUtil.convert(amShipping.getBillDate(), "yyyy-MM-dd"));
			}
			// 仓 库
			form.setField("warehouse_name", warehouse == null ? "" : ConvertUtil.toEmpty(warehouse.getName()));
			// 机构名称
			form.setField("saleOrg_name", saleOrg == null ? "" : ConvertUtil.toEmpty(saleOrg.getName()));
			// 客户编码
			form.setField("store_sn", store == null ? "" : ConvertUtil.toEmpty(store.getOutTradeNo()));
			// 客户名称
			form.setField("store_name", store == null ? "" : ConvertUtil.toEmpty(store.getName()));
			if(!ConvertUtil.isEmpty(amShipping.getBillCategory()) && amShipping.getBillCategory().getValue().equals("转仓出仓")){
				if(!amShippingItemList.isEmpty() && amShippingItemList.size()>0){
					AmShippingItem amShippingItem = amShippingItemList.get(0);
					if(!ConvertUtil.isEmpty(amShippingItem) && !ConvertUtil.isEmpty(amShippingItem.getMovelibraryIssue())){
						MoveLibrary movelibraryIssue = amShippingItem.getMovelibraryIssue();
						//接收仓库
						Warehouse receiveWarehouse = movelibraryIssue.getReceiveWarehouse();
						if(!ConvertUtil.isEmpty(receiveWarehouse)){
							form.setField("alias",ConvertUtil.toEmpty(receiveWarehouse.getName()));
							//联系人
							form.setField("consignee",ConvertUtil.toEmpty(receiveWarehouse.getSender()));
							//联系电话
							form.setField("phone",ConvertUtil.toEmpty(receiveWarehouse.getMobile()));
							//联系地址
							form.setField("address",ConvertUtil.toEmpty(receiveWarehouse.getAddress()));
						}
						// 制单人
						if (!ConvertUtil.isEmpty(movelibraryIssue.getStoreMember())) {
							form.setField("store_member_name", ConvertUtil.toEmpty(movelibraryIssue.getStoreMember().getName()));
						}
					}
				}
			}else{
				// 客户简称
				form.setField("alias", store == null ? "" : ConvertUtil.toEmpty(store.getAlias()));
				// 制单人
				if (!ConvertUtil.isEmpty(amShipping.getStoreMember())) {
					form.setField("store_member_name", ConvertUtil.toEmpty(amShipping.getStoreMember().getName()));
				}
				//联系人
				form.setField("consignee",ConvertUtil.toEmpty(amShipping.getConsignee()));
				//联系电话
				form.setField("phone",ConvertUtil.toEmpty(amShipping.getPhone()));
				//联系地址
				form.setField("address",ConvertUtil.toEmpty(amShipping.getAddress()));
			}
			// 审核人
			if (!ConvertUtil.isEmpty(amShipping.getCheckStoreMember())) {
				form.setField("checkStoreMember", ConvertUtil.toEmpty(amShipping.getCheckStoreMember().getName()));
			}
			BigDecimal total_scatteredQuantity = BigDecimal.ZERO;
			BigDecimal total_box_quantity = BigDecimal.ZERO;
			BigDecimal total_branch_quantity = BigDecimal.ZERO;
			BigDecimal total_quantity = new BigDecimal("0");
			// 统计数量
			List<WarehouseBatchItem> allWarehouseBatchItems = new ArrayList<WarehouseBatchItem>();
			for (AmShippingItem amShippingItem : amShippingItemList) {
				List<WarehouseBatchItem> warehouseBatchItems = amShippingItem.getWarehouseBatchItems();
				for (WarehouseBatchItem item : warehouseBatchItems) {
					allWarehouseBatchItems.add(item);
				}
			}
			// 遍历数据
			for (int j = i * pageSize; j < forNumber; j++) {
				WarehouseBatchItem batchItem = allWarehouseBatchItems.get(j);
				AmShippingItem amShippingItem = batchItem.getAmShippingItem();
				Product product = null;
				if (amShippingItem.getProduct() != null) {
					product = amShippingItem.getProduct();
				}
				// 产品编码
				form.setField("productSn_" + column,
						product == null ? "" : ConvertUtil.toEmpty(product.getVonderCode()));
				// 产品名称+木种花色
				form.setField("product_name_" + column,
						ConvertUtil.toEmpty(product == null ? ""
								: ConvertUtil.toEmpty(product.getName()) + "/"
										+ ConvertUtil.toEmpty(product.getWoodTypeOrColor())));
				// 经营组织
				form.setField("product_organization_name_" + column,
						ConvertUtil.toEmpty(amShippingItem.getProductOrganization() == null ? ""
								: amShippingItem.getProductOrganization().getName()));
				// 型号
				form.setField("product_model_" + column,
						product == null ? "" : ConvertUtil.toEmpty(product.getModel()));
				// 规格
				form.setField("product_spec_" + column, product == null ? "" : ConvertUtil.toEmpty(product.getSpec()));
				// 数量
				BigDecimal quantity = null;
				if (batchItem.getQuantity() != null) {
					quantity = batchItem.getQuantity().stripTrailingZeros();
				} else {
					quantity = BigDecimal.ZERO;
				}
				total_quantity = total_quantity.add(quantity.setScale(2, BigDecimal.ROUND_CEILING));
				form.setField("quantity_" + column, quantity.setScale(2, BigDecimal.ROUND_CEILING).toPlainString());
				// 单位
				form.setField("unit_" + column, product == null ? "" : ConvertUtil.toEmpty(product.getUnit()));
				// 库位
				if (!ConvertUtil.isEmpty(batchItem.getWarehouseLocation())) {
					form.setField("warehouse_location_code_" + column,
							ConvertUtil.toEmpty(batchItem.getWarehouseLocation().getCode()));
				} else {
					form.setField("warehouse_location_code_" + column, "");
				}
				// 件
				BigDecimal box_quantity = null;
				if (batchItem.getShippedBoxQuantity() != null) {
					box_quantity = batchItem.getShippedBoxQuantity().setScale(0, BigDecimal.ROUND_DOWN);
				} else {
					box_quantity = BigDecimal.ZERO;
				}
				total_box_quantity = total_box_quantity.add(box_quantity.stripTrailingZeros());
				form.setField("box_quantity_" + column, box_quantity.stripTrailingZeros().toPlainString());
				// 支数
				BigDecimal branch_quantity = null;
				if (batchItem.getShippedBranchQuantity() != null) {
					branch_quantity = batchItem.getShippedBranchQuantity().setScale(0, BigDecimal.ROUND_DOWN);
				} else {
					branch_quantity = BigDecimal.ZERO;
				}
				total_branch_quantity = total_branch_quantity.add(branch_quantity.stripTrailingZeros());
				form.setField("branch_quantity_" + column, branch_quantity.stripTrailingZeros().toPlainString());
				// 件/支
				BigDecimal scatteredQuantity = null;
				if (batchItem.getShippedBranchScattered() != null) {
					scatteredQuantity = batchItem.getShippedBranchScattered().setScale(0, BigDecimal.ROUND_DOWN);
				} else {
					scatteredQuantity = BigDecimal.ZERO;
				}
				total_scatteredQuantity = total_scatteredQuantity.add(scatteredQuantity.stripTrailingZeros());
				form.setField("scattered_quantity_" + column, scatteredQuantity.stripTrailingZeros().toPlainString());
				// 等级
				String gradeName = batchItem.getProductLevel() != null ? batchItem.getProductLevel().getValue() : "";
				form.setField("grade_" + column, gradeName);
				// 色号
				form.setField("colourNumber_" + column, ConvertUtil
						.toEmpty(batchItem.getColorNumbers() == null ? "" : batchItem.getColorNumbers().getValue()));
				// 含水率
				form.setField("moistureContent_" + column, ConvertUtil.toEmpty(
						batchItem.getMoistureContents() == null ? "" : batchItem.getMoistureContents().getValue()));
				// 备注
				form.setField("buyer_memo_" + column, ConvertUtil.toEmpty(amShippingItem.getMemo()));

				SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
				String batchInfo = ConvertUtil.toEmpty(
						batchItem.getWarehouseBatch().getSn() == null ? "" : batchItem.getWarehouseBatch().getSn())
						+ " "
						+ ConvertUtil.toEmpty(batchItem.getWarehouseBatch().getCompletionDate() == null ? ""
								: format.format(batchItem.getWarehouseBatch().getCompletionDate()))
						+ " " + ConvertUtil.toEmpty(batchItem.getWarehouseBatch().getMemo() == null ? ""
								: batchItem.getWarehouseBatch().getMemo());

				// 批次信息
				form.setField("batchInfo_" + column, batchInfo);
				column++;
			}
			NumberFormat nf = NumberFormat.getInstance();
			// 零散总数
			form.setField("scatteredQuantity_total", nf.format(total_scatteredQuantity));
			// 箱总数
			form.setField("box_quantity_total", nf.format(total_box_quantity));
			// 支总数
			form.setField("branch_quantity_total", nf.format(total_branch_quantity));
			// 数量
			form.setField("quantity_total", total_quantity.toPlainString());
			// 二维码
			if (form.getFieldPositions("erweima") != null) {
				try {
					String imageStaticPath = "/pdf/image/" + UUID.randomUUID() + ".jpg";
					if (ConvertUtil.isEmpty(amShipping.getSn())) {
						ExceptionUtil.throwServiceException("单号为空，无法生成二维码");
					}
					Setting setting = SettingUtils.get();
					String pdfHttpUrl = setting.getSiteUrl() + "/" + pdfStaticPath;
					String url = createRQ(imageStaticPath, pdfHttpUrl);
					int page = form.getFieldPositions("erweima").get(0).page;
					Rectangle erweimaSignRect = form.getFieldPositions("erweima").get(0).position;
					float erweimaX = erweimaSignRect.getLeft();
					float erweimaY = erweimaSignRect.getBottom();
					String erweima = servletContext.getRealPath(url);
					Image erweimaMage = Image.getInstance(erweima);
					PdfContentByte pdfUnder = stamp.getOverContent(page);
					erweimaMage.scaleToFit(erweimaSignRect.getWidth(), erweimaSignRect.getHeight());
					erweimaMage.setAbsolutePosition(erweimaX, erweimaY);
					pdfUnder.addImage(erweimaMage);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			// 千万不漏了这句啊
			stamp.setFormFlattening(true);
			stamp.close();
		}
		Document doc = new Document();
		PdfCopy pdfCopy = new PdfCopy(doc, fos);
		doc.open();
		PdfImportedPage impPage = null;
		/// 取出之前保存的每页内容
		for (int i = 0; i < pdfPage; i++) {
			impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);
			pdfCopy.addPage(impPage);
		}
		// 当文件拷贝 记得作废doc
		doc.close();
	}

	private void buildPdf(AmShipping amShipping, String pdfStaticPath, String template_path) throws Exception {
		String PdfTemplatePath = servletContext.getRealPath(template_path);
		String outputFile = servletContext.getRealPath(pdfStaticPath);
		// 客户
		Store store = null;
		if (amShipping.getStore() != null) {
			store = amShipping.getStore();
		}
		// 仓库
		Warehouse warehouse = null;
		if (amShipping.getWarehouse() != null) {
			warehouse = amShipping.getWarehouse();
		}
		// 机构
		SaleOrg saleOrg = null;
		if (amShipping.getSaleOrg() != null) {
			saleOrg = amShipping.getSaleOrg();
		}
		// 需要生成PDF
		FileOutputStream fos = new FileOutputStream(outputFile);
		// 出入单明细表
		List<AmShippingItem> amShippingItemList = amShipping.getAmShippingItems();
		amShippingItemList.size();
		// 统计出入单明细表总记录数
		int itemSize = amShippingItemList.size();
		int pageSize = 5;
		int pdfPage = itemSize % pageSize == 0 ? itemSize / pageSize : (itemSize / pageSize) + 1;
		// 用于存储每页生成PDF流
		ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pdfPage];
		BaseFont bf = BaseFont.createFont(FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
		for (int i = 0; i < pdfPage; i++) {
			baos[i] = new ByteArrayOutputStream();
			PdfReader reader = new PdfReader(PdfTemplatePath);
			PdfStamper stamp = new PdfStamper(reader, baos[i]);
			ArrayList<BaseFont> fontList = new ArrayList<BaseFont>();
			fontList.add(bf);
			AcroFields form = stamp.getAcroFields();
			// 列数 3 1-3 4
			int column = 0;
			int forNumber = i * pageSize + pageSize > itemSize ? itemSize : i * pageSize + pageSize;
			form.setSubstitutionFonts(fontList);
			// 总页码
			form.setField("totalPage", pdfPage + "");
			// 页次
			form.setField("page_info", (i + 1) + "");
			// 单据类型
			form.setField("bill_type_name", amShipping.getBillType() == null ? ""
					: ConvertUtil.toEmpty(warehouse.getName() + amShipping.getBillType().getValue() + "单"));
			// 单据编号
			form.setField("sn", amShipping.getSn());
			// 预发货通知单据编号
			form.setField("shippingSn", "");
			// 单据类别
			form.setField("billCategory",
					amShipping.getBillCategory() == null ? "" : amShipping.getBillCategory().getValue());
			// 柜号/车号
			form.setField("containerNumber",
					amShipping.getContainerNumber() == null ? ""
							: amShipping.getContainerNumber() + "/" + amShipping.getWagonNumber() == null ? ""
									: amShipping.getWagonNumber());
			// 所属事业部
			form.setField("org_name", amShipping.getWarehouse().getManagementOrganization().getName());
			// 创建日期
			String create_date = DateUtil.convert(amShipping.getCreateDate(), "yyyy-MM-dd");
			form.setField("create_date", create_date);
			// 单据日期
			if (!ConvertUtil.isEmpty(amShipping.getBillDate())) {
				form.setField("bill_date", DateUtil.convert(amShipping.getBillDate(), "yyyy-MM-dd"));
			} else {
				form.setField("bill_date", "");
			}
			// 仓 库 (来源仓)
			form.setField("warehouse_name", warehouse == null ? "" : ConvertUtil.toEmpty(warehouse.getName()));
			// 客户编码
			form.setField("store_sn", store == null ? "" : ConvertUtil.toEmpty(store.getOutTradeNo()));
			// 客户名称
			form.setField("store_name", store == null ? "" : ConvertUtil.toEmpty(store.getName()));
			// 备 注
			form.setField("memo", ConvertUtil.toEmpty(amShipping.getMemo()));
			if(!ConvertUtil.isEmpty(amShipping.getBillCategory()) && amShipping.getBillCategory().getValue().equals("转仓出仓")){
				if(!amShippingItemList.isEmpty() && amShippingItemList.size()>0){
					AmShippingItem amShippingItem = amShippingItemList.get(0);
					if(!ConvertUtil.isEmpty(amShippingItem) && !ConvertUtil.isEmpty(amShippingItem.getMovelibraryIssue())){
						MoveLibrary movelibraryIssue = amShippingItem.getMovelibraryIssue();
						//接收仓库
						Warehouse receiveWarehouse = movelibraryIssue.getReceiveWarehouse();
						if(!ConvertUtil.isEmpty(receiveWarehouse)){
							form.setField("alias",ConvertUtil.toEmpty(receiveWarehouse.getName()));
							//联系人
							form.setField("consignee",ConvertUtil.toEmpty(receiveWarehouse.getSender()));
							//联系电话
							form.setField("phone",ConvertUtil.toEmpty(receiveWarehouse.getMobile()));
							//联系地址
							form.setField("address",ConvertUtil.toEmpty(receiveWarehouse.getAddress()));
						}
						// 制单人
						if (!ConvertUtil.isEmpty(movelibraryIssue.getStoreMember())) {
							form.setField("store_member_name", ConvertUtil.toEmpty(movelibraryIssue.getStoreMember().getName()));
						}
					}
				}
			}else{
				// 客户简称
				form.setField("alias", store == null ? "" : ConvertUtil.toEmpty(store.getAlias()));
				// 制单人
				if (!ConvertUtil.isEmpty(amShipping.getStoreMember())) {
					form.setField("store_member_name", ConvertUtil.toEmpty(amShipping.getStoreMember().getName()));
				}
				//联系人
				form.setField("consignee",ConvertUtil.toEmpty(amShipping.getConsignee()));
				//联系电话
				form.setField("phone",ConvertUtil.toEmpty(amShipping.getPhone()));
				//联系地址
				form.setField("address",ConvertUtil.toEmpty(amShipping.getAddress()));
			}
			// 机构名称
			form.setField("saleOrg_name", saleOrg == null ? "" : ConvertUtil.toEmpty(saleOrg.getName()));
			// 审核人
			if (!ConvertUtil.isEmpty(amShipping.getCheckStoreMember())) {
				form.setField("checkStoreMember", ConvertUtil.toEmpty(amShipping.getCheckStoreMember().getName()));
			}
			
			BigDecimal total_scatteredQuantity = BigDecimal.ZERO;
			BigDecimal total_box_quantity = BigDecimal.ZERO;
			BigDecimal total_branch_quantity = BigDecimal.ZERO;
			BigDecimal total_quantity = new BigDecimal("0");
			for (int j = i * pageSize; j < forNumber; j++) {
				AmShippingItem amShippingItem = amShippingItemList.get(j);
				Product product = null;
				if (amShippingItem.getProduct() != null) {
					product = amShippingItem.getProduct();
				}
				// 产品编码
				form.setField("productSn_" + column,
						product == null ? "" : ConvertUtil.toEmpty(product.getVonderCode()));
				// 产品名称+木种花色
				form.setField("product_name_" + column,
						ConvertUtil.toEmpty(product == null ? ""
								: ConvertUtil.toEmpty(product.getName()) + "/"
										+ ConvertUtil.toEmpty(product.getWoodTypeOrColor())));
				// 经营组织
				form.setField("product_organization_name_" + column,
						ConvertUtil.toEmpty(amShippingItem.getProductOrganization() == null ? ""
								: amShippingItem.getProductOrganization().getName()));
				// 型号
				form.setField("product_model_" + column,
						product == null ? "" : ConvertUtil.toEmpty(product.getModel()));
				// 规格
				form.setField("product_spec_" + column, product == null ? "" : ConvertUtil.toEmpty(product.getSpec()));
				// 数量
				BigDecimal quantity = null;
				if (amShippingItem.getQuantity() != null) {
					quantity = amShippingItem.getQuantity().stripTrailingZeros();
				} else {
					quantity = BigDecimal.ZERO;
				}
				total_quantity = total_quantity.add(quantity.setScale(2, BigDecimal.ROUND_CEILING));
				form.setField("quantity_" + column, quantity.setScale(2, BigDecimal.ROUND_CEILING).toPlainString());
				// 单位
				form.setField("unit_" + column, product == null ? "" : ConvertUtil.toEmpty(product.getUnit()));
				// 件
				BigDecimal box_quantity = null;
				if (amShippingItem.getBoxQuantity() != null) {
					box_quantity = amShippingItem.getBoxQuantity().setScale(0, BigDecimal.ROUND_DOWN);
				} else {
					box_quantity = BigDecimal.ZERO;
				}
				total_box_quantity = total_box_quantity.add(box_quantity.stripTrailingZeros());
				form.setField("box_quantity_" + column, box_quantity.stripTrailingZeros().toPlainString());
				// 支数
				BigDecimal branch_quantity = null;
				if (amShippingItem.getBranchQuantity() != null) {
					branch_quantity = amShippingItem.getBranchQuantity().setScale(0, BigDecimal.ROUND_DOWN);
				} else {
					branch_quantity = BigDecimal.ZERO;
				}
				total_branch_quantity = total_branch_quantity.add(branch_quantity.stripTrailingZeros());
				form.setField("branch_quantity_" + column, branch_quantity.stripTrailingZeros().toPlainString());
				// 件/支
				BigDecimal scatteredQuantity = null;
				if (amShippingItem.getScatteredQuantity() != null) {
					scatteredQuantity = amShippingItem.getScatteredQuantity().setScale(0, BigDecimal.ROUND_DOWN);
				} else {
					scatteredQuantity = BigDecimal.ZERO;
				}
				total_scatteredQuantity = total_scatteredQuantity.add(scatteredQuantity.stripTrailingZeros());
				form.setField("scattered_quantity_" + column, scatteredQuantity.stripTrailingZeros().toPlainString());
				// 等级
				String gradeName = amShippingItem.getProductLevel() != null
						? amShippingItem.getProductLevel().getValue()
						: "";
				form.setField("grade_" + column, gradeName);
				// 色号
				form.setField("colourNumber_" + column, ConvertUtil.toEmpty(
						amShippingItem.getColorNumbers() == null ? "" : amShippingItem.getColorNumbers().getValue()));
				// 含水率
				form.setField("moistureContent_" + column,
						ConvertUtil.toEmpty(amShippingItem.getMoistureContents() == null ? ""
								: amShippingItem.getMoistureContents().getValue()));
				// 备注
				form.setField("buyer_memo_" + column, ConvertUtil.toEmpty(amShippingItem.getBatch()) + "/"
						+ ConvertUtil.toEmpty(amShippingItem.getMemo()));
				column++;
			}
			NumberFormat nf = NumberFormat.getInstance();
			// 零散总数
			form.setField("scatteredQuantity_total", nf.format(total_scatteredQuantity));
			// 箱总数
			form.setField("box_quantity_total", nf.format(total_box_quantity));
			// 支总数
			form.setField("branch_quantity_total", nf.format(total_branch_quantity));
			// 数量
			form.setField("quantity_total", total_quantity.toPlainString());
			// 二维码
			if (form.getFieldPositions("erweima") != null) {
				try {
					String imageStaticPath = "/pdf/image/" + UUID.randomUUID() + ".jpg";
					if (ConvertUtil.isEmpty(amShipping.getSn())) {
						ExceptionUtil.throwServiceException("单号为空，无法生成二维码");
					}
					Setting setting = SettingUtils.get();
					String pdfHttpUrl = setting.getSiteUrl() + "/" + pdfStaticPath;
					String url = createRQ(imageStaticPath, pdfHttpUrl);
					int page = form.getFieldPositions("erweima").get(0).page;
					Rectangle erweimaSignRect = form.getFieldPositions("erweima").get(0).position;
					float erweimaX = erweimaSignRect.getLeft();
					float erweimaY = erweimaSignRect.getBottom();
					String erweima = servletContext.getRealPath(url);
					Image erweimaMage = Image.getInstance(erweima);
					PdfContentByte pdfUnder = stamp.getOverContent(page);
					erweimaMage.scaleToFit(erweimaSignRect.getWidth(), erweimaSignRect.getHeight());
					erweimaMage.setAbsolutePosition(erweimaX, erweimaY);
					pdfUnder.addImage(erweimaMage);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			// 千万不漏了这句啊
			stamp.setFormFlattening(true);
			stamp.close();
		}
		Document doc = new Document();
		PdfCopy pdfCopy = new PdfCopy(doc, fos);
		doc.open();
		PdfImportedPage impPage = null;
		/// 取出之前保存的每页内容
		for (int i = 0; i < pdfPage; i++) {
			impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);
			pdfCopy.addPage(impPage);
		}
		// 当文件拷贝 记得作废doc
		doc.close();
	}

	@Override
	public TriplicateForm createAmShippingTriplicateForm(AmShipping amShipping) throws Exception {
		Setting setting = SettingUtils.get();
		String siteUrl = setting.getSiteUrl();
		String template_path = "/pdf/amShipping/template/amShipping.pdf";
		String file_name = "" + UUID.randomUUID();
		String pdfStaticPath = "/pdf/amShipping/file/" + file_name + ".pdf";
		this.buildPdf(amShipping, pdfStaticPath, template_path);
		/** 保存中间表 */
		String url = siteUrl + pdfStaticPath;
		TriplicateForm triplicateForm = new TriplicateForm(amShipping.getId(), 3, url, 3);
		triplicateFormService.save(triplicateForm);
		return triplicateForm;
	}

	@Override
	public TriplicateForm createAmShippingTriplicateFormByBatch(AmShipping amShipping) throws Exception {
		Setting setting = SettingUtils.get();
		String siteUrl = setting.getSiteUrl();
		String template_path = "/pdf/amShipping/template/amShippingByBatch.pdf";
		String file_name = "" + UUID.randomUUID();
		String pdfStaticPath = "/pdf/amShipping/file/" + file_name + ".pdf";
		this.buildBatchPdf(amShipping, pdfStaticPath, template_path);
		/** 保存中间表 */
		String url = siteUrl + pdfStaticPath;
		TriplicateForm triplicateForm = new TriplicateForm(amShipping.getId(), 3, url, 3);
		triplicateFormService.save(triplicateForm);
		return triplicateForm;
	}

	/**
	 * 出入库报表列表数据
	 */
	@Override
	public Page<Map<String, Object>> findAccessPageList(String sn, Long[] billTypeId, String[] billCategory,
			String startTime, String endTime, Long[] saleOrgId, Long[] storeId, Long[] organizationId,
			Long[] warehouseId, String storeMemberName, Long[] productCategoryId, Long[] vProductId, Long[] productId,
			String woodTypeOrColor, String model, Long[] ids, Pageable pageable) {

		return saleNatureShippingDao.findAccessPageList(sn, billTypeId, billCategory, startTime, endTime, saleOrgId,
				storeId, organizationId, warehouseId, storeMemberName, productCategoryId, vProductId, productId,
				woodTypeOrColor, model, ids, pageable);
	}

	/**
	 * 出入库报表数据导出
	 */
	public List<Map<String, Object>> findAccessList(String sn, Long[] billTypeId, String[] billCategory,
			String startTime, String endTime, Long[] saleOrgId, Long[] storeId, Long[] organizationId,
			Long[] warehouseId, String storeMemberName, Long[] productCategoryId, Long[] vProductId, Long[] productId,
			String woodTypeOrColor, String model, Long[] ids, Integer page, Integer size) {

		return saleNatureShippingDao.findAccessList(sn, billTypeId, billCategory, startTime, endTime, saleOrgId,
				storeId, organizationId, warehouseId, storeMemberName, productCategoryId, vProductId, productId,
				woodTypeOrColor, model, ids, page, size);
	}

	/**
	 * 收发存报表数据
	 */
	@Override
	public Page<Map<String, Object>> findReceiveReportPageList(String startTime, String endTime, Long[] saleOrgId,
			Long[] storeId, Long[] organizationId, Long[] warehouseId, Long[] productCategoryId, Long[] vProductId,
			Long[] productId, String woodTypeOrColor, String model, Long[] ids, Pageable pageable) {

		return saleNatureShippingDao.findReceiveReportPageList(startTime, endTime, saleOrgId, storeId, organizationId,
				warehouseId, productCategoryId, vProductId, productId, woodTypeOrColor, model, ids, pageable);
	}

	private List<AmShippingItem> GroupByShippingId(List<AmShippingItem> amshippingItems) {

		List<AmShippingItem> amshippingItemsList = new ArrayList<AmShippingItem>();
		for (AmShippingItem amShippingItem : amshippingItems) {
			if (!ConvertUtil.isEmpty(amShippingItem.getShipping())
					&& !ConvertUtil.isEmpty(amShippingItem.getShipping().getId())) {
				boolean state = false;
				for (AmShippingItem amShippingItem2 : amshippingItemsList) {
					if (amShippingItem.getShipping().getId().longValue() == amShippingItem2.getShipping().getId()
							.longValue()) {
						state = true;
					}
				}
				if (!state) {
					AmShippingItem amShippingItem2 = new AmShippingItem();
					amShippingItem2.setShipping(amShippingItem.getShipping());
					amShippingItem2.setQuantity(new BigDecimal(0));
					amshippingItemsList.add(amShippingItem2);
				}
				for (AmShippingItem amShippingItem2 : amshippingItemsList) {
					if (amShippingItem.getShipping().getId().longValue() == amShippingItem2.getShipping().getId()
							.longValue()) {
						amShippingItem2.setQuantity(amShippingItem2.getQuantity().add(amShippingItem.getQuantity()));
					}
				}
			}
		}
		return amshippingItemsList;
	}

	@Override
	public void updateAmShippingItemQuantity(AmShipping dataBaseAmShipping, AmShipping pageAmShipping) {

		List<AmShippingItem> dataBaseAmShippingItems = this.GroupByShippingId(dataBaseAmShipping.getAmShippingItems());

		List<AmShippingItem> pageAmshippingItemsList = new ArrayList<AmShippingItem>();
		for (AmShippingItem pageAmShippingItems : pageAmShipping.getAmShippingItems()) {
			if (!ConvertUtil.isEmpty(pageAmShippingItems.getId())) {
				AmShippingItem pAmShippingItem = amShippingItemService.find(pageAmShippingItems.getId());
				if (!ConvertUtil.isEmpty(pAmShippingItem.getShipping())
						&& !ConvertUtil.isEmpty(pAmShippingItem.getShipping().getId())) {
					AmShippingItem amShippingItem = new AmShippingItem();
					amShippingItem.setQuantity(pageAmShippingItems.getQuantity());
					amShippingItem.setId(pageAmShippingItems.getId());
					amShippingItem.setShipping(pAmShippingItem.getShipping());
					pageAmshippingItemsList.add(amShippingItem);
				}
			} else {
				// ExceptionUtil.throwServiceException("出库单明细Id不能为空");
				continue;
			}
		}

		List<AmShippingItem> pageAmShippingItems = this.GroupByShippingId(pageAmshippingItemsList);
		for (AmShippingItem pageAmShippingItem : pageAmShippingItems) {
			// 已经生成了出入库数量
			BigDecimal amShippingItemQuantity = saleNatureShippingDao
					.getAmShippingItemQuantity(pageAmShippingItem.getShipping().getId());
			pageAmShippingItem.setQuantity(pageAmShippingItem.getQuantity().add(amShippingItemQuantity));
		}

		for (AmShippingItem dataBaseAmShippingItem : dataBaseAmShippingItems) {
			if (!ConvertUtil.isEmpty(dataBaseAmShippingItem.getShipping())
					&& !ConvertUtil.isEmpty(dataBaseAmShippingItem.getShipping().getId())) {
				for (AmShippingItem pageAmShippingItem : pageAmShippingItems) {
					if (dataBaseAmShippingItem.getShipping().getId().longValue() == pageAmShippingItem.getShipping()
							.getId().longValue()) {
						pageAmShippingItem.setQuantity(
								pageAmShippingItem.getQuantity().subtract(dataBaseAmShippingItem.getQuantity()));
						// 已经审核的发货通知单数量
						BigDecimal shippingItemQuantity = saleNatureShippingDao
								.getShippingItemQuantity(pageAmShippingItem.getShipping().getId());
						if (pageAmShippingItem.getQuantity().compareTo(shippingItemQuantity) == 1) {
							throw new RuntimeException(
									"E0008-发货编号为" + pageAmShippingItem.getShipping().getSn() + "数量过大，请修改！");
						}
					}
				}
			}
		}
	}

	@Override
	@Transactional
	public void checkOrCancel(AmShipping amShipping) {
		//校验出入库单状态
		this.checkAmShippingState(amShipping, "作废");
		amShipping.setStatus(2);
		for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
			amShippingItem.setStatus(2);
		}
		update(amShipping);
		
		//操作类型
		List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "作废");
		if(actionTypeList.isEmpty() || actionTypeList.size()==0){
			ExceptionUtil.throwServiceException("操作类型不能为空");
		}
		//加载库存
		warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
		
		orderFullLinkService.addFullLink(4, null, amShipping.getSn(), "作废本次单据", null);
		if(amShipping.getWarehouse().getIsSendJiaWaYun()&&amShipping.getStatus()==0) {
		    intfOrderMessageToService.saveAmShippingIntf(amShipping, 2);
            intfLogisticsJob.shippingInft();
		}
	}

	@Override
	public List<Map<String, Object>> findReceiveReportList(String startTime, String endTime, Long[] saleOrgId,
			Long[] storeId, Long[] organizationId, Long[] warehouseId, Long[] productCategoryId, Long[] vProductId,
			Long[] productId, String woodTypeOrColor, String model, Long[] ids, Integer page, Integer size) {

		return saleNatureShippingDao.findReceiveReportList(startTime, endTime, saleOrgId, storeId, organizationId,
				warehouseId, productCategoryId, vProductId, productId, woodTypeOrColor, model, ids, page, size);
	}

	@Override
	@Transactional
	public void movelibraryIssueSave(AmShipping amShipping, AmShippingItem wAmShippingItem,SystemDict billType) {
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		List<Filter> filters = new ArrayList<Filter>();
		// 系统单据类型
		filters.clear();
		filters.add(Filter.eq("remark", CommonVariable.INANDOUT_TYPE));
		SystemDict systemType = systemDictService.systemBillType(filters);
		List<AmShippingItem> amShippingItems = amShipping.getAmShippingItems();
		//移库单Id
		Long[] movelibraryIds = new Long[]{0L};
		for (Iterator<AmShippingItem> iterator = amShippingItems.iterator(); iterator.hasNext();) {
			AmShippingItem amShippingItem = iterator.next();
			if (ConvertUtil.isEmpty(amShippingItem) || 
					(!ConvertUtil.isEmpty(amShippingItem) && 
							ConvertUtil.isEmpty(amShippingItem.getProduct()))) {
				iterator.remove();
				continue;
			}
			//校验移库单
			if(!ConvertUtil.isEmpty(amShipping.getBillCategory().getValue()) && !amShipping.getBillCategory().getValue().equals("返挑入仓")){
				this.checkMovelibrary(amShippingItem,billType,movelibraryIds);
			}
			
			//产品
			Product product = productBaseService.find(amShippingItem.getProduct().getId());
			if(ConvertUtil.isEmpty(product)){
				ExceptionUtil.throwServiceException("产品不存在");
			}
			if (ConvertUtil.isEmpty(amShippingItem.getQuantity()) || (!ConvertUtil.isEmpty(amShippingItem.getQuantity())
					&& amShippingItem.getQuantity().compareTo(BigDecimal.ZERO) < 1)) {
				ExceptionUtil.throwServiceException("产品编码为[" + product.getVonderCode() + "]行数量必须大于0");
			}
			// 产品名称
			amShippingItem.setName(product.getName());
			// 产品编码
			amShippingItem.setVonderCode(product.getVonderCode());
			// 产品描述
			amShippingItem.setDescription(product.getDetailDescription());
			// 产品型号
			amShippingItem.setModel(product.getModel());
			// 体积
			if (ConvertUtil.isEmpty(amShippingItem.getVolume())) {
				amShippingItem.setVolume(BigDecimal.ZERO);
			}
			// 重量
			if (ConvertUtil.isEmpty(amShippingItem.getWeight())) {
				amShippingItem.setWeight(BigDecimal.ZERO);
			}
			amShippingItem.setShippedQuantity(BigDecimal.ZERO);
			amShippingItem.setReturnQuantity(BigDecimal.ZERO);
			amShippingItem.setProduct(product);
			amShippingItem.setStatus(0);
			amShippingItem.setAmShipping(amShipping);
			// 商品全称
			amShippingItem.setFullName(product.getFullName());
			amShippingItem.setThumbnail(product.getThumbnail());
			// 产品经营组织
			if (ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
					|| (!ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
							&& ConvertUtil.isEmpty(amShippingItem.getProductOrganization().getId()))) {
				amShippingItem.setProductOrganization(null);
			}
			// 色号
			if (ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
					|| (!ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
							&& ConvertUtil.isEmpty(amShippingItem.getColorNumbers().getId()))) {
				amShippingItem.setColorNumbers(null);
			}
			// 含水率
			if (ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
					|| (!ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
							&& ConvertUtil.isEmpty(amShippingItem.getMoistureContents().getId()))) {
				amShippingItem.setMoistureContents(null);
			}
			// 新旧标识
			if (ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
					|| (!ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
							&& ConvertUtil.isEmpty(amShippingItem.getNewOldLogos().getId()))) {
				amShippingItem.setNewOldLogos(null);
			}
			// 库位
			if (ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
					|| (!ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
							&& ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation().getId()))) {
				amShippingItem.setWarehouseLocation(null);
			}
			// 批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if (!ConvertUtil.isEmpty(amShippingItem.getBatch())) {
				String[] batchs = amShippingItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if (!ConvertUtil.isEmpty(batchs[i])) {
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						// 退货类型
						warehouseBillBatch.setSysType(systemType);
						// 退货
						warehouseBillBatch.setAmShipping(amShipping);
						// 退货明细
						warehouseBillBatch.setAmShippingItem(amShippingItem);
						// 批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			amShippingItem.setWarehouseBillBatchs(warehouseBillBatchList);
//			this.saveWarehouseBatchItem(amShipping,amShippingItem, wAmShippingItem, product);
			// 批次列表
		List<WarehouseBatchItem> warehouseBatchItemList = new ArrayList<WarehouseBatchItem>();
				
			for (WarehouseBatchItem warehouseBatchItem : wAmShippingItem.getWarehouseBatchItems()) {
			
				if (ConvertUtil.isEmpty(warehouseBatchItem.getProduct())) {
					continue;
				}
			 
				// 单据类型
				warehouseBatchItem.setBillType(amShipping.getBillType());
				// 仓库
				warehouseBatchItem.setWarehouse(amShipping.getWarehouse());
				// 出入单
				warehouseBatchItem.setAmShipping(amShipping);
				// 单据状态
				warehouseBatchItem.setStatus(amShipping.getStatus());
								
//								
//				//色号
//				warehouseBatchItem.setColorNumbers(amShippingItem.getColorNumbers());
//								
//				//新旧标识
//				warehouseBatchItem.setNewOldLogos(amShippingItem.getNewOldLogos());
//								
//				//含水率
//				warehouseBatchItem.setMoistureContents(amShippingItem.getMoistureContents());
								
								// 数量
				if (ConvertUtil.isEmpty(warehouseBatchItem.getQuantity())) {
					warehouseBatchItem.setQuantity(BigDecimal.ZERO);
				}
								
				warehouseBatchItemList.add(warehouseBatchItem);
				// 单据日期
				// warehouseBatchItem.setDocumentDate(amShipping.getBillDate());
				// 库位
				if (ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())
						|| (!ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())
								&& ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation().getId()))) {
					warehouseBatchItem.setWarehouseLocation(null);
				}
						
				if (amShippingItem.getProduct().getId().longValue() == warehouseBatchItem.getProduct().getId().longValue()
						&& amShippingItem.getColorNumbers().getId().longValue() ==warehouseBatchItem.getColorNumbers().getId()
						&& amShippingItem.getMoistureContents().getId().longValue() == warehouseBatchItem.getMoistureContents().getId().longValue()
						&& amShippingItem.getNewOldLogos().getId().longValue() == warehouseBatchItem.getNewOldLogos().getId().longValue()) {

									
					// 产品
					warehouseBatchItem.setProduct(product);
					// 等级
					warehouseBatchItem.setProductLevel(amShippingItem.getProductLevel());
					// 经营组织
					warehouseBatchItem.setOrganization(amShippingItem.getProductOrganization());
					// 出入库明细
					warehouseBatchItem.setAmShippingItem(amShippingItem);
					// 数量
					if (ConvertUtil.isEmpty(warehouseBatchItem.getQuantity())) {
						warehouseBatchItem.setQuantity(BigDecimal.ZERO);
					}
					
					warehouseBatchItemList.add(warehouseBatchItem);
				}
			}
			amShippingItem.setWarehouseBatchItems(warehouseBatchItemList);
							
			
		}
		if (amShipping.getAmShippingItems().isEmpty()) {
			ExceptionUtil.throwServiceException("请添加单据行项");
		}
		amShipping.setSn(SnUtil.getAmShippingSn());
		amShipping.setStatus(0);
		orderFullLinkService.addFullLink(4, null, amShipping.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "移库发货", }) + amShipping.getSn(), null);
		save(amShipping);
		
		//操作类型
		List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "保存");
		if(actionTypeList.isEmpty() || actionTypeList.size()==0){
			ExceptionUtil.throwServiceException("操作类型不能为空");
		}
		//加载库存
		warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
	}

	@Override
	@Transactional
	public void movelibraryIssueUpdate(AmShipping amShipping,AmShippingItem wAmShippingItem) {

		AmShipping pAmShipping = find(amShipping.getId());
		//校验出入库单状态
		this.checkAmShippingState(pAmShipping,"保存");
		// 单据日期
		pAmShipping.setBillDate(amShipping.getBillDate());
		BigDecimal tVolume = BigDecimal.ZERO;
		BigDecimal tWeight = BigDecimal.ZERO;
		List<Filter> filters = new ArrayList<Filter>();
		// 系统单据类型
		filters.clear();
		filters.add(Filter.eq("remark", CommonVariable.INANDOUT_TYPE));
		SystemDict systemType = systemDictService.systemBillType(filters);
		List<AmShippingItem> amShippingItems = new ArrayList<AmShippingItem>();
		//移库单Id
		Long[] movelibraryIds = new Long[]{0L};
		for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
			if (ConvertUtil.isEmpty(amShippingItem)
					|| (!ConvertUtil.isEmpty(amShippingItem) && ConvertUtil.isEmpty(amShippingItem.getProduct()))) {
				continue;
			}
			//校验移库单
			if(!ConvertUtil.isEmpty(pAmShipping.getBillCategory().getValue()) && !pAmShipping.getBillCategory().getValue().equals("返挑入仓")){
				this.checkMovelibrary(amShippingItem,pAmShipping.getBillType(),movelibraryIds);
			}
			// 产品
			Product product = productBaseService.find(amShippingItem.getProduct().getId());
			// 数量判断是否大于0
			if (ConvertUtil.isEmpty(amShippingItem.getQuantity()) || (!ConvertUtil.isEmpty(amShippingItem.getQuantity())
					&& amShippingItem.getQuantity().compareTo(BigDecimal.ZERO) < 1)) {
				ExceptionUtil.throwServiceException("产品编码为[" + product.getVonderCode() + "]行数量必须大于0");
			}
			// 产品名称
			amShippingItem.setName(product.getName());
			// 产品编码
			amShippingItem.setVonderCode(product.getVonderCode());
			// 产品描述
			amShippingItem.setDescription(product.getDetailDescription());
			// 产品型号
			amShippingItem.setModel(product.getModel());
			// 商品全称
			amShippingItem.setFullName(product.getFullName());
			amShippingItem.setThumbnail(product.getThumbnail());
			// 产品级别
			if (ConvertUtil.isEmpty(amShippingItem.getProductLevel())
					|| (!ConvertUtil.isEmpty(amShippingItem.getProductLevel())
							&& ConvertUtil.isEmpty(amShippingItem.getProductLevel().getId()))) {
				ExceptionUtil.throwServiceException("产品编码为[" + product.getVonderCode() + "]行的等级不能为空");
			}
			// 产品经营组织
			if (ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
					|| (!ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
							&& ConvertUtil.isEmpty(amShippingItem.getProductOrganization().getId()))) {
				amShippingItem.setProductOrganization(null);
			}
			// 体积
			if (ConvertUtil.isEmpty(amShippingItem.getVolume())) {
				amShippingItem.setVolume(BigDecimal.ZERO);
			}
			// 重量
			if (ConvertUtil.isEmpty(amShippingItem.getWeight())) {
				amShippingItem.setWeight(BigDecimal.ZERO);
			}
			// 行合计体积
			BigDecimal volume = amShippingItem.getVolume().multiply(amShippingItem.getQuantity());
			// 总体积
			tVolume = tVolume.add(volume);
			// 行合计重量
			BigDecimal weight = amShippingItem.getWeight().multiply(amShippingItem.getQuantity());
			// 总重量
			tWeight = tWeight.add(weight);
			// 色号
			if (ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
					|| (!ConvertUtil.isEmpty(amShippingItem.getColorNumbers())
							&& ConvertUtil.isEmpty(amShippingItem.getColorNumbers().getId()))) {
				amShippingItem.setColorNumbers(null);
			}
			// 含水率
			if (ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
					|| (!ConvertUtil.isEmpty(amShippingItem.getMoistureContents())
							&& ConvertUtil.isEmpty(amShippingItem.getMoistureContents().getId()))) {
				amShippingItem.setMoistureContents(null);
			}
			// 新旧标识
			if (ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
					|| (!ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())
							&& ConvertUtil.isEmpty(amShippingItem.getNewOldLogos().getId()))) {
				amShippingItem.setNewOldLogos(null);
			}
			// 库位
			if (ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
					|| (!ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())
							&& ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation().getId()))) {
				amShippingItem.setWarehouseLocation(null);
			}
			// 批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if (!ConvertUtil.isEmpty(amShippingItem.getBatch())) {
				String[] batchs = amShippingItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if (!ConvertUtil.isEmpty(batchs[i])) {
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						// 退货类型
						warehouseBillBatch.setSysType(systemType);
						// 退货
						warehouseBillBatch.setAmShipping(pAmShipping);
						// 退货明细
						warehouseBillBatch.setAmShippingItem(amShippingItem);
						// 批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			amShippingItem.getWarehouseBillBatchs().clear();
			amShippingItem.getWarehouseBillBatchs().addAll(warehouseBillBatchList);
			// 批次列表
			this.saveOrUpdateWarehouseBatchItem(pAmShipping, amShippingItem, wAmShippingItem, product);
			amShippingItem.setAmShipping(pAmShipping);
			amShippingItems.add(amShippingItem);
		}
		pAmShipping.getAmShippingItems().clear();
		pAmShipping.getAmShippingItems().addAll(amShippingItems);
		// 校验总账日期
		this.isCheckTotalDate(pAmShipping);
		// 车号
		pAmShipping.setWagonNumber(amShipping.getWagonNumber());
		// 柜号
		pAmShipping.setContainerNumber(amShipping.getContainerNumber());
		// 备注
		pAmShipping.setMemo(amShipping.getMemo());
		// 总体积
		pAmShipping.setVolume(tVolume);
		// 总重量
		pAmShipping.setWeight(tWeight);

		update(pAmShipping);
		
		//操作类型
		List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "保存");
		if(actionTypeList.isEmpty() || actionTypeList.size()==0){
			ExceptionUtil.throwServiceException("操作类型不能为空");
		}
		//加载库存
		warehouseStockService.loadWarehouseStock(pAmShipping,actionTypeList.get(0));
	}

	@Override
	public void checkMovelibraryIssue(Long movelibraryIssueId, Date billDate) throws Exception {
		/* 已发数量 */
		BigDecimal issueQuantity = BigDecimal.ZERO;
		/* 数量 */
		BigDecimal quantity = BigDecimal.ZERO;
		MoveLibrary movelibraryIssue = moveLibraryService.find(movelibraryIssueId);
		if (!ConvertUtil.isEmpty(movelibraryIssue)) {
			List<MoveLibraryItem> moveLibraryItems = movelibraryIssue.getMoveLibraryItems();
			if(moveLibraryItems.isEmpty() || moveLibraryItems.size()==0){
				return;
			}
			for (MoveLibraryItem moveLibraryItem : moveLibraryItems) {
				issueQuantity = issueQuantity.add(moveLibraryItem.getIssueQuantity());
				quantity = quantity.add(moveLibraryItem.getQuantity());
			}
			if (issueQuantity.compareTo(quantity) == 0) {
				movelibraryIssue.setMoveStatuss(MoveStatuss.completely);
			} else if (issueQuantity.compareTo(quantity) == -1 && quantity.compareTo(BigDecimal.ZERO) != 0) {
				movelibraryIssue.setMoveStatuss(MoveStatuss.part);
			}
			movelibraryIssue.setIssueDate(billDate);
			moveLibraryService.update(movelibraryIssue);
		}

	}

	@Override
	public void checkMovelibraryReceive(Long movelibraryReceiveId, Date billDate) throws Exception {
		/* 接收数量 */
		BigDecimal receiveQuantity = BigDecimal.ZERO;
		/* 数量 */
		BigDecimal quantity = BigDecimal.ZERO;
		MoveLibrary movelibraryReceive = moveLibraryService.find(movelibraryReceiveId);
		if (!ConvertUtil.isEmpty(movelibraryReceive)) {
			
			List<MoveLibraryItem> moveLibraryItems = movelibraryReceive.getMoveLibraryItems();
			if(moveLibraryItems.isEmpty() || moveLibraryItems.size() == 0){
				return;
			}
			for (MoveLibraryItem moveLibraryItem : moveLibraryItems) {
				receiveQuantity = receiveQuantity.add(moveLibraryItem.getReceiveQuantity());
				quantity = quantity.add(moveLibraryItem.getQuantity());
			}
			if (receiveQuantity.compareTo(quantity) == 0) {
				movelibraryReceive.setMoveReceiveStatus(MoveReceiveStatus.completely);
			} else if (receiveQuantity.compareTo(quantity) == -1 && quantity.compareTo(BigDecimal.ZERO) != 0) {
				movelibraryReceive.setMoveReceiveStatus(MoveReceiveStatus.part);
			}
			movelibraryReceive.setReceiveDate(billDate);
			moveLibraryService.update(movelibraryReceive);
		}
	}

	@Override
	@Transactional
	public void checkWfMovelibraryIssue(Long id) throws Exception {
		AmShipping amShipping = find(id);
		List<AmShippingItem> amshippingItems = amShipping.getAmShippingItems();
		if(amshippingItems.isEmpty() || amshippingItems.size() == 0){
			return;
		}
		//校验出入库单状态
		this.checkAmShippingState(amShipping, "审核");
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		// 批次列表数据校验
		this.isCheckWarehouseBatchItemQuantity(amShipping);
		// 库存校验
		this.isCheckStock(amShipping,0);
		if(amShipping.getWarehouse().getIsSendJiaWaYun()&&amShipping.getStatus()==0){
			//移库单Id
			Long[] movelibraryIds = new Long[]{0L};
			for (int i = 0; i < amshippingItems.size(); i++) {
				AmShippingItem amShippingItem = amshippingItems.get(i);
				//校验移库单
				this.checkMovelibrary(amShippingItem,amShipping.getBillType(),movelibraryIds);
				MoveLibraryItem moveLibraryItemIssue = amShippingItem.getMoveLibraryItemIssue();
				MoveLibrary movelibraryIssue = amShippingItem.getMovelibraryIssue();
				if(!ConvertUtil.isEmpty(moveLibraryItemIssue)){
					//初始化移库接收明细数量
					moveLibraryService.checkParamIsNotNull(moveLibraryItemIssue);
					if (amShippingItem.getQuantity().add(
							moveLibraryItemIssue.getIssueQuantity()).compareTo(
							moveLibraryItemIssue.getQuantity()) == 1) {
						ExceptionUtil.throwServiceException("产品编码为" + moveLibraryItemIssue.getVonderCode() + "的出仓数量过大");
					}
//					else {
//						//数量
//						moveLibraryItemIssue.setIssueQuantity(
//								amShippingItem.getQuantity().add(
//										moveLibraryItemIssue.getIssueQuantity()));
//						//件数
//						moveLibraryItemIssue.setIssueBoxQuantity(
//								amShippingItem.getBoxQuantity().add(
//										moveLibraryItemIssue.getIssueBoxQuantity()));
//						//支数
//						moveLibraryItemIssue.setIssueBranchQuantity(
//								amShippingItem.getBranchQuantity().add(
//										moveLibraryItemIssue.getIssueBranchQuantity()));
//					}
//					moveLibraryItemService.update(moveLibraryItemIssue);
//					this.checkMovelibraryIssue(movelibraryIssue.getId(), amShipping.getBillDate());
				}
			}
//			amShipping.setAmShippingItems(amshippingItems);
			amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
			update(amShipping);
			intfOrderMessageToService.saveAmShippingIntf(amShipping,0);
            intfLogisticsJob.shippingInft();
		}else{
			//移库单Id
			Long[] movelibraryIds = new Long[]{0L};
			for (int i = 0; i < amshippingItems.size(); i++) {
				AmShippingItem amShippingItem = amshippingItems.get(i);
				//校验移库单
				this.checkMovelibrary(amShippingItem,amShipping.getBillType(),movelibraryIds);
				MoveLibraryItem moveLibraryItemIssue = amShippingItem.getMoveLibraryItemIssue();
				MoveLibrary movelibraryIssue = amShippingItem.getMovelibraryIssue();
				if(!ConvertUtil.isEmpty(moveLibraryItemIssue)){
					//初始化移库接收明细数量
					moveLibraryService.checkParamIsNotNull(moveLibraryItemIssue);
					if (amShippingItem.getQuantity().add(
							moveLibraryItemIssue.getIssueQuantity()).compareTo(
							moveLibraryItemIssue.getQuantity()) == 1) {
						ExceptionUtil.throwServiceException("产品编码为" + moveLibraryItemIssue.getVonderCode() + "的出仓数量过大");
					} else {
						//数量
						moveLibraryItemIssue.setIssueQuantity(
								amShippingItem.getQuantity().add(
										moveLibraryItemIssue.getIssueQuantity()));
						//件数
						moveLibraryItemIssue.setIssueBoxQuantity(
								amShippingItem.getBoxQuantity().add(
										moveLibraryItemIssue.getIssueBoxQuantity()));
						//支数
						moveLibraryItemIssue.setIssueBranchQuantity(
								amShippingItem.getBranchQuantity().add(
										moveLibraryItemIssue.getIssueBranchQuantity()));
					}
					moveLibraryItemService.update(moveLibraryItemIssue);
					this.checkMovelibraryIssue(movelibraryIssue.getId(), amShipping.getBillDate());
					amShippingItem.setStatus(1);
				}
			}
			amShipping.setAmShippingItems(amshippingItems);
			amShipping.setStatus(1);
			update(amShipping);

	        //操作类型
	        List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "审核");
	        if(actionTypeList.isEmpty() || actionTypeList.size()==0){
	            ExceptionUtil.throwServiceException("操作类型不能为空");
	        }
	        //加载库存
	        warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
		}

	}

	@Override
	@Transactional
	public void checkWfMovelibraryReceive(Long id) throws Exception {
		AmShipping amShipping = find(id);
		List<AmShippingItem> amshippingItems = amShipping.getAmShippingItems();
		if(amshippingItems.isEmpty() || amshippingItems.size() == 0){
			return;
		}
		//校验出入库单状态
		this.checkAmShippingState(amShipping, "审核");
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		// 批次列表数据校验
		this.isCheckWarehouseBatchItemQuantity(amShipping);
		// 库存校验
		this.isCheckStock(amShipping,1);
		if (amShipping.getWarehouse().getIsSendJiaWaYun()&&amShipping.getStatus()==0) {
			Long[] movelibraryIds = new Long[]{0L};
			for (int i = 0; i < amshippingItems.size(); i++) {
				AmShippingItem amShippingItem = amshippingItems.get(i);
				//校验移库单
				this.checkMovelibrary(amShippingItem,amShipping.getBillType(),movelibraryIds);
				MoveLibraryItem moveLibraryItemReceive = amShippingItem.getMoveLibraryItemReceive();
				MoveLibrary movelibraryReceive = amShippingItem.getMovelibraryReceive();
				if (!ConvertUtil.isEmpty(moveLibraryItemReceive)) {
					//初始化移库接收明细数量
					moveLibraryService.checkParamIsNotNull(moveLibraryItemReceive);
					if (amShippingItem.getQuantity().add(
							moveLibraryItemReceive.getReceiveQuantity().add(
									moveLibraryItemReceive.getCloseQuantity())).compareTo(
							moveLibraryItemReceive.getQuantity()) == 1) {
						ExceptionUtil.throwServiceException("产品编码为" + moveLibraryItemReceive.getVonderCode() + "入仓数量过大");
					}
					List<WarehouseBatchItem> warehouseBatchItems = amShippingItem.getWarehouseBatchItems();

					for(WarehouseBatchItem warehouseBatchItem :warehouseBatchItems) {
					if (amShippingItem.getProduct().getId().longValue() == warehouseBatchItem.getProduct().getId().longValue()) {
						
						if(amShippingItem.getColorNumbers().getId() != warehouseBatchItem.getColorNumbers().getId()) {
							
							ExceptionUtil.throwServiceException("产品：["+amShippingItem.getProduct().getVonderCode()+"]接收行与批次色号需相同！");
						}
						if(amShippingItem.getMoistureContents().getId() != warehouseBatchItem.getMoistureContents().getId()) {
							ExceptionUtil.throwServiceException("产品：["+amShippingItem.getProduct().getVonderCode()+"]接收行与批次含水率需相同！");
						}
						if(amShippingItem.getNewOldLogos().getId() != warehouseBatchItem.getNewOldLogos().getId()) {
							ExceptionUtil.throwServiceException("产品：["+amShippingItem.getProduct().getVonderCode()+"]接收行与批次新旧标识需相同！");
						}
					}
					}
//					else {
//						//数量
//						moveLibraryItemReceive.setReceiveQuantity(
//								amShippingItem.getQuantity().add(
//										moveLibraryItemReceive.getReceiveQuantity()));
//						//件数
//						moveLibraryItemReceive.setReceiveBoxQuantity(
//								amShippingItem.getBoxQuantity().add(
//										moveLibraryItemReceive.getReceiveBoxQuantity() ));
//						//支数
//						moveLibraryItemReceive.setReceiveBranchQuantity(
//								amShippingItem.getBranchQuantity().add(
//										moveLibraryItemReceive.getReceiveBranchQuantity()));
//					}
//					moveLibraryItemService.update(moveLibraryItemReceive);
//					this.checkMovelibraryReceive(movelibraryReceive.getId(), amShipping.getBillDate());
				}
			}
//			amShipping.setAmShippingItems(amshippingItems);
			amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
			update(amShipping);
			intfOrderMessageToService.saveAmShippingIntf(amShipping,0);
            intfLogisticsJob.shippingInft();
		}else{
			//移库单Id
			Long[] movelibraryIds = new Long[]{0L};
			for (int i = 0; i < amshippingItems.size(); i++) {
				AmShippingItem amShippingItem = amshippingItems.get(i);
				//校验移库单
				this.checkMovelibrary(amShippingItem,amShipping.getBillType(),movelibraryIds);
				MoveLibraryItem moveLibraryItemReceive = amShippingItem.getMoveLibraryItemReceive();
				MoveLibrary movelibraryReceive = amShippingItem.getMovelibraryReceive();
				if (!ConvertUtil.isEmpty(moveLibraryItemReceive)) {
					//初始化移库接收明细数量
					moveLibraryService.checkParamIsNotNull(moveLibraryItemReceive);
					if (amShippingItem.getQuantity().add(
							moveLibraryItemReceive.getReceiveQuantity().add(
									moveLibraryItemReceive.getCloseQuantity())).compareTo(
											moveLibraryItemReceive.getQuantity()) == 1) {
						ExceptionUtil.throwServiceException("产品编码为" + moveLibraryItemReceive.getVonderCode() + "入仓数量过大");
					} else {
						//数量
						moveLibraryItemReceive.setReceiveQuantity(
								amShippingItem.getQuantity().add(
										moveLibraryItemReceive.getReceiveQuantity()));
						//件数--改为用加完的总支数除以每箱支数算出箱数
						// 支数
						BigDecimal brabchA=amShippingItem.getBranchQuantity().add(moveLibraryItemReceive.getReceiveBranchQuantity());
                        //每箱支数
						BigDecimal branchPerBoxB=moveLibraryItemReceive.getBranchPerBox();
                        BigDecimal receiveBoxQuantity =brabchA.divide(branchPerBoxB,0,BigDecimal.ROUND_HALF_UP);
                        moveLibraryItemReceive.setReceiveBoxQuantity(receiveBoxQuantity);

						//支数
						moveLibraryItemReceive.setReceiveBranchQuantity(
								amShippingItem.getBranchQuantity().add(
										moveLibraryItemReceive.getReceiveBranchQuantity()));
					}
					moveLibraryItemService.update(moveLibraryItemReceive);
					this.checkMovelibraryReceive(movelibraryReceive.getId(), amShipping.getBillDate());
					amShippingItem.setStatus(1);
				}
				List<WarehouseBatchItem> warehouseBatchItems = amShippingItem.getWarehouseBatchItems();

				for(WarehouseBatchItem warehouseBatchItem :warehouseBatchItems) {
					WarehouseBatchItem  outWarehouseBatchItem =  warehouseBatchItemService.find(warehouseBatchItem.getOutBatchId());
			
					if(outWarehouseBatchItem!=null) {

						outWarehouseBatchItem.setShippedQuantity((outWarehouseBatchItem.getShippedQuantity()==null ? BigDecimal.ZERO: outWarehouseBatchItem.getShippedQuantity()).add((warehouseBatchItem.getQuantity()==null? BigDecimal.ZERO:warehouseBatchItem.getQuantity()) ));
					}
					if (amShippingItem.getProduct().getId().longValue() == warehouseBatchItem.getProduct().getId().longValue()) {
					
						if(amShippingItem.getColorNumbers().getId() != warehouseBatchItem.getColorNumbers().getId()) {
							
							ExceptionUtil.throwServiceException("产品：["+amShippingItem.getProduct().getVonderCode()+"]接收行与批次色号需相同！");
						}
						if(amShippingItem.getMoistureContents().getId() != warehouseBatchItem.getMoistureContents().getId()) {
							ExceptionUtil.throwServiceException("产品：["+amShippingItem.getProduct().getVonderCode()+"]接收行与批次含水率需相同！");
						}
						if(amShippingItem.getNewOldLogos().getId() != warehouseBatchItem.getNewOldLogos().getId()) {
							ExceptionUtil.throwServiceException("产品：["+amShippingItem.getProduct().getVonderCode()+"]接收行与批次新旧标识需相同！");
						}
					}
					
					warehouseBatchItemService.update(warehouseBatchItem);
				}
				
				
			}
			amShipping.setAmShippingItems(amshippingItems);
			if(amShipping.getStatus()!=6) {
			    amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
			}
			amShipping.setStatus(1);
			update(amShipping);

	        //操作类型
	        List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "审核");
	        if(actionTypeList.isEmpty() || actionTypeList.size()==0){
	            ExceptionUtil.throwServiceException("操作类型不能为空");
	        }
	        //加载库存
	        warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
		}

	}

	private void isCheckTotalDate(AmShipping amShipping) {
		// 机构
		if (ConvertUtil.isEmpty(amShipping.getSaleOrg())) {
			ExceptionUtil.throwServiceException("机构不能为空");
		}
		// sbu
		if (ConvertUtil.isEmpty(amShipping.getSbu())) {
			ExceptionUtil.throwServiceException("sbu不能为空");
		}
		// 经营组织
		Long[] organizationIds = new Long[amShipping.getAmShippingItems().size()];
		for (int i = 0; i < amShipping.getAmShippingItems().size(); i++) {
			AmShippingItem amShippingItem = amShipping.getAmShippingItems().get(i);
			if (!ConvertUtil.isEmpty(amShippingItem)) {
				if (!ConvertUtil.isEmpty(amShippingItem.getProductOrganization())
						&& !ConvertUtil.isEmpty(amShippingItem.getProductOrganization().getId())) {
					organizationIds[i] = amShippingItem.getProductOrganization().getId();
				} else {
					ExceptionUtil.throwServiceException("经营组织不能为空");
				}
			}
		}
		// 单据日期
		if (ConvertUtil.isEmpty(amShipping.getBillDate())) {
			ExceptionUtil.throwServiceException("单据日期不能为空");
		}

        SystemDict totalDateType =systemDictService.findSystemDictList("totalDateType", "库存账期").get(0);
		List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, amShipping.getSaleOrg().getId(),
				amShipping.getSbu().getId(), organizationIds, amShipping.getBillDate(),totalDateType);
		if (mapList.isEmpty() || mapList.size() == 0) {
			ExceptionUtil.throwServiceException("单据日期不包含在总账日期内，请填写合适的单据日期");
		}
	}

	/**
	 * 保存或更新批次列表
	 * 
	 * @param amShipping
	 * @param amShippingItem
	 * @param wAmShippingItem
	 * @param product
	 */
	private void saveOrUpdateWarehouseBatchItem(AmShipping amShipping, AmShippingItem amShippingItem,
			AmShippingItem wAmShippingItem, Product product) {

		if (!ConvertUtil.isEmpty(amShippingItem.getId())) {
			// 批次列表
			List<WarehouseBatchItem> warehouseBatchItemList = new ArrayList<WarehouseBatchItem>();
			if (!ConvertUtil.isEmpty(wAmShippingItem) && !wAmShippingItem.getWarehouseBatchItems().isEmpty()) {
				for (WarehouseBatchItem warehouseBatchItem : wAmShippingItem.getWarehouseBatchItems()) {
					if (ConvertUtil.isEmpty(warehouseBatchItem)
							|| (ConvertUtil.isEmpty(warehouseBatchItem.getAmShippingItem()))) {
						continue;
					}
					// 单据类型
					warehouseBatchItem.setBillType(amShipping.getBillType());
					// 仓库
					warehouseBatchItem.setWarehouse(amShipping.getWarehouse());
					// 出入单
					warehouseBatchItem.setAmShipping(amShipping);
					// 单据状态
					warehouseBatchItem.setStatus(amShipping.getStatus());
					// 单据日期
					// warehouseBatchItem.setDocumentDate(amShipping.getBillDate());
					// 库位
					if (ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())
							|| (!ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())
									&& ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation().getId()))) {
						warehouseBatchItem.setWarehouseLocation(null);
					}
					if (amShippingItem.getId().longValue() == warehouseBatchItem.getAmShippingItem().getId()) {
						// 产品
						warehouseBatchItem.setProduct(product);
						// 等级
						warehouseBatchItem.setProductLevel(amShippingItem.getProductLevel());
						// 经营组织
						warehouseBatchItem.setOrganization(amShippingItem.getProductOrganization());
						// 出入库明细
						warehouseBatchItem.setAmShippingItem(amShippingItem);
						// 数量
						if (ConvertUtil.isEmpty(warehouseBatchItem.getQuantity())) {
							warehouseBatchItem.setQuantity(BigDecimal.ZERO);
						}
					
						warehouseBatchItemList.add(warehouseBatchItem);
					}
				}
				amShippingItem.getWarehouseBatchItems().clear();
				amShippingItem.getWarehouseBatchItems().addAll(warehouseBatchItemList);
			}
		}
	}
	
	
	/**
	 * 保存或更新批次列表
	 * 
	 * @param amShipping
	 * @param amShippingItem
	 * @param wAmShippingItem
	 * @param product
	 */
	private void saveWarehouseBatchItem(AmShipping amShipping, AmShippingItem amShippingItem,
			AmShippingItem wAmShippingItem,Product product) {

		
			// 批次列表
			List<WarehouseBatchItem> warehouseBatchItemList = new ArrayList<WarehouseBatchItem>();
	
				for (WarehouseBatchItem warehouseBatchItem : wAmShippingItem.getWarehouseBatchItems()) {
					if (ConvertUtil.isEmpty(warehouseBatchItem)) {
						continue;
					}

					// 单据类型
					warehouseBatchItem.setBillType(amShipping.getBillType());
					// 仓库
					warehouseBatchItem.setWarehouse(amShipping.getWarehouse());
					// 出入单
					warehouseBatchItem.setAmShipping(amShipping);
					// 单据状态
					warehouseBatchItem.setStatus(amShipping.getStatus());
					
					
					//色号
					warehouseBatchItem.setColorNumbers(amShippingItem.getColorNumbers());
					
					//新旧标识
					warehouseBatchItem.setNewOldLogos(amShippingItem.getNewOldLogos());
					
					//含水率
					warehouseBatchItem.setMoistureContents(amShippingItem.getMoistureContents());
					
					// 数量
					if (ConvertUtil.isEmpty(warehouseBatchItem.getQuantity())) {
						warehouseBatchItem.setQuantity(BigDecimal.ZERO);
					}
					
					warehouseBatchItemList.add(warehouseBatchItem);
					// 单据日期
					// warehouseBatchItem.setDocumentDate(amShipping.getBillDate());
					// 库位
					if (ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())
							|| (!ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())
									&& ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation().getId()))) {
						warehouseBatchItem.setWarehouseLocation(null);
					}

					if (amShippingItem.getProduct().getId() == warehouseBatchItem.getProduct().getId()) {
						
						// 产品
						warehouseBatchItem.setProduct(product);
						// 等级
						warehouseBatchItem.setProductLevel(amShippingItem.getProductLevel());
						// 经营组织
						warehouseBatchItem.setOrganization(amShippingItem.getProductOrganization());
						// 出入库明细
						warehouseBatchItem.setAmShippingItem(amShippingItem);
						// 数量
						if (ConvertUtil.isEmpty(warehouseBatchItem.getQuantity())) {
							warehouseBatchItem.setQuantity(BigDecimal.ZERO);
						}
						warehouseBatchItemList.add(warehouseBatchItem);
					}
				}
				amShippingItem.getWarehouseBatchItems().clear();
				amShippingItem.getWarehouseBatchItems().addAll(warehouseBatchItemList);
			
		}
	

	/**
	 * 审核校验批次列表数据
	 * 
	 * @param amShipping
	 */
	private void isCheckWarehouseBatchItemQuantity(AmShipping amShipping) {
		List<AmShippingItem> amShippingItems = amShipping.getAmShippingItems();
		if (!ConvertUtil.isEmpty(amShipping.getWarehouse().getEnableBatch())
				&& amShipping.getWarehouse().getEnableBatch()) {
			// 如果仓库启用批次
			if (!amShippingItems.isEmpty() && amShippingItems.size() > 0) {
					BigDecimal totalQuantity = BigDecimal.ZERO;
				for (AmShippingItem amShippingItem : amShippingItems) {
                    totalQuantity = BigDecimal.ZERO;
					// 批次总数量
					List<WarehouseBatchItem> warehouseBatchItems = amShippingItem.getWarehouseBatchItems();
					if (!warehouseBatchItems.isEmpty() && warehouseBatchItems.size() > 0) {
						for (WarehouseBatchItem warehouseBatchItem : warehouseBatchItems) {
							if (ConvertUtil.isEmpty(warehouseBatchItem.getQuantity())) {
								warehouseBatchItem.setQuantity(BigDecimal.ZERO);
							}
							totalQuantity = totalQuantity.add(warehouseBatchItem.getQuantity());
						}
					}
					if (totalQuantity.compareTo(amShippingItem.getQuantity()) == -1
							|| totalQuantity.compareTo(amShippingItem.getQuantity()) == 1) {
						throw new RuntimeException(
								"产品编码为【" + amShippingItem.getProduct().getVonderCode() + "】的批次列表汇总数量必须等于需求数量");
					}
				}
//
//			// 如果订货仓入库
//            if(!ConvertUtil.isEmpty(amShipping.getWarehouse().getIsOrderWarehousing()) && amShipping.getWarehouse().getIsOrderWarehousing()==true && amShipping.getBillType().getValue().equals("入仓")) {
//				for (AmShippingItem amShippingItem : amShippingItems) {
//					//对应仓库的对应产品的可处理数量
//					List<Map<String, Object>> warehouseStockList = warehouseStockService.findWarehouseStockList(amShipping.getWarehouse().getId(), amShippingItem.getProduct().getName(), amShippingItem.getProduct().getVonderCode(), amShippingItem.getProduct().getModel(),"negative");
//					if (warehouseStockList.size() > 0) {
//						List<WarehouseBatchItem> warehouseBatchItems = amShippingItem.getWarehouseBatchItems();
//						int i=0;
//						for(Map map:warehouseStockList){
//							if (map.get("batch_encoding").equals(warehouseBatchItems.get(0).getWarehouseBatch().getBatchEncoding())){
//								break;
//							}
//							i++;
//						}
//						BigDecimal canUseQuantity = new BigDecimal(String.valueOf(warehouseStockList.get(i).get("quantity"))).abs();
//						if (totalQuantity.compareTo(canUseQuantity) == 1) {
//							throw new RuntimeException(
//									"产品编码为【" + amShippingItem.getProduct().getVonderCode() + "】的批次入库数量不可以大于负库存的数量");
//						}
//					} else {
//						throw new RuntimeException(
//								"产品编码为【" + amShippingItem.getProduct().getVonderCode() + "】的该批次不存在负库存");
//					}
//				}
//
//			}
            }
		} else {
			// 仓库不启用批次
			if (!amShippingItems.isEmpty() && amShippingItems.size() > 0) {
				for (AmShippingItem amShippingItem : amShippingItems) {
					List<WarehouseBatchItem> warehouseBatchItems = amShippingItem.getWarehouseBatchItems();
					if (!warehouseBatchItems.isEmpty() && warehouseBatchItems.size() > 0) {
						throw new RuntimeException("仓库没有启用批次，请不要添加批次列表");
					}
				}
			}
		}
	}

	/**
	 * 根据产品、等级、经营组织、色号、含水率、新旧标识、库位、批次等字段进行分组
	 */
	private List<AmShippingItem> groupbyAmShippingItem(AmShipping amShipping, 
			Integer sign, Boolean enableLocation) {
		
		List<AmShippingItem> amshippingItemsList = new ArrayList<AmShippingItem>();
		try {
			for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
				// 产品
				if (ConvertUtil.isEmpty(amShippingItem.getProduct())) {
					ExceptionUtil.throwServiceException("产品不能为空");
				}
				// 产品等级
				if (ConvertUtil.isEmpty(amShippingItem.getProductLevel())) {
					ExceptionUtil.throwServiceException("产品编码为【" + amShippingItem.getVonderCode() + "】的等级不能为空");
				}
				// 经营组织
				if (ConvertUtil.isEmpty(amShippingItem.getProductOrganization())) {
					ExceptionUtil.throwServiceException("产品编码为【" + amShippingItem.getVonderCode() + "】的经营组织不能为空");
				}
				// 色号
				if (ConvertUtil.isEmpty(amShippingItem.getColorNumbers())) {
					ExceptionUtil.throwServiceException("产品编码为【" + amShippingItem.getVonderCode() + "】的色号不能为空");
				}
				// 含水率
				if (ConvertUtil.isEmpty(amShippingItem.getMoistureContents())) {
					ExceptionUtil.throwServiceException("产品编码为【" + amShippingItem.getVonderCode() + "】的含水率不能为空");
				}
				// 新旧标识
				if (ConvertUtil.isEmpty(amShippingItem.getNewOldLogos())) {
					ExceptionUtil.throwServiceException("产品编码为【" + amShippingItem.getVonderCode() + "】的新旧标识不能为空");
				}
				// 批次Id
				String batchIds = "";
				// 库位Id
				String warehouseLocationIds = "";
				if (!ConvertUtil.isEmpty(sign) && sign == 1) {
					List<WarehouseBatchItem> warehouseBatchItemList = amShippingItem.getWarehouseBatchItems();
					if (!warehouseBatchItemList.isEmpty() && warehouseBatchItemList.size() > 0) {
						for (WarehouseBatchItem warehouseBatchItem : warehouseBatchItemList) {
							if (!ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseLocation())) {
								if (!ConvertUtil.isEmpty(warehouseLocationIds)) {
									warehouseLocationIds = warehouseLocationIds + ','+ warehouseBatchItem.getWarehouseLocation().getId().toString();
								} else {
									warehouseLocationIds = warehouseBatchItem.getWarehouseLocation().getId().toString();
								}
							} else {
								if (enableLocation) {
									ExceptionUtil.throwServiceException("产品编码为【" + amShippingItem.getVonderCode() + "】的批次列表库位不能为空");
								}
							}
							if (!ConvertUtil.isEmpty(warehouseBatchItem.getWarehouseBatch())) {
								if (!ConvertUtil.isEmpty(batchIds)) {
									batchIds = batchIds + ',' + warehouseBatchItem.getWarehouseBatch().getId().toString();
								} else {
									batchIds = warehouseBatchItem.getWarehouseBatch().getId().toString();
								}
							}
						}
					}
				} else {
					List<WarehouseBillBatch> warehouseBillBatchList = amShippingItem.getWarehouseBillBatchs();
					if (!warehouseBillBatchList.isEmpty() && warehouseBillBatchList.size() > 0) {
						for (WarehouseBillBatch warehouseBillBatch : warehouseBillBatchList) {
							if (!ConvertUtil.isEmpty(warehouseBillBatch.getWarehouseBatch())) {
								if (!ConvertUtil.isEmpty(batchIds)) {
									batchIds = batchIds + ',' + warehouseBillBatch.getWarehouseBatch().getId().toString();
								} else {
									batchIds = warehouseBillBatch.getWarehouseBatch().getId().toString();
								}
							}
						}
					}
					if (!ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())) {
						warehouseLocationIds = amShippingItem.getWarehouseLocation().getId().toString();
					}
				}
				

				boolean state = false;
				for (AmShippingItem amShippingItems : amshippingItemsList) {
					if (amShippingItem.getProduct().equals(amShippingItems.getProduct())
							&& amShippingItem.getProductLevel().equals(amShippingItems.getProductLevel())
							&& amShippingItem.getProductOrganization().equals(amShippingItems.getProductOrganization())
							&& amShippingItem.getColorNumbers().equals(amShippingItems.getColorNumbers())
							&& amShippingItem.getMoistureContents().equals(amShippingItems.getMoistureContents())
							&& amShippingItem.getNewOldLogos().equals(amShippingItems.getNewOldLogos())
							&& batchIds.equals(amShippingItems.getBatch())
							&& warehouseLocationIds.equals(amShippingItems.getMoistureContent())) {
						state = true;
						amshippingItemsList.remove(amShippingItems);
						amShippingItems.setQuantity(amShippingItems.getQuantity().add(amShippingItem.getQuantity()));
						amshippingItemsList.add(amShippingItems);
						break;
					}
				}

				if (!state) {
					AmShippingItem amShippingItems = new AmShippingItem();
					// 产品
					amShippingItems.setProduct(amShippingItem.getProduct());
					// 产品编码
					amShippingItems.setVonderCode(amShippingItem.getVonderCode());
					// 产品等级
					amShippingItems.setProductLevel(amShippingItem.getProductLevel());
					// 经营组织
					amShippingItems.setProductOrganization(amShippingItem.getProductOrganization());
					// 色号
					amShippingItems.setColorNumbers(amShippingItem.getColorNumbers());
					// 含水率
					amShippingItems.setMoistureContents(amShippingItem.getMoistureContents());
					// 批次
					amShippingItems.setBatch(batchIds);
					// 新旧标识
					amShippingItems.setNewOldLogos(amShippingItem.getNewOldLogos());
					// 库位
					amShippingItems.setMoistureContent(warehouseLocationIds);
					amShippingItems.setQuantity(amShippingItem.getQuantity());
					amshippingItemsList.add(amShippingItems);
				}
			}
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
		return amshippingItemsList;
	}
	
	/**
	 * 出入库明细库存校验
	 * @param amShipping
	 * @param sign
	 * @param enableLocation
	 * @param billType
	 */
	private void isCheckOutEnterStock(AmShipping amShipping, 
			Integer sign, Boolean enableLocation, Integer billType){
		try {
			List<AmShippingItem> amShippingItemList = this.groupbyAmShippingItem(amShipping, sign, enableLocation);
			for (AmShippingItem amShippingItem : amShippingItemList) {
				// 库位Id
				Long warehouseLocationId = null;
				if (!ConvertUtil.isEmpty(amShippingItem.getWarehouseLocation())) {
					warehouseLocationId = amShippingItem.getWarehouseLocation().getId();
				}
				// 库存数量
				BigDecimal stockQuantity = null;
				if(billType == 0){
					stockQuantity = stockService.getStockQuantity(amShipping.getWarehouse().getId(),amShippingItem.getProductOrganization().getId(), 
							amShippingItem.getProduct().getId(),amShippingItem.getProductLevel().getId(),amShippingItem.getColorNumbers().getId().toString(),
							amShippingItem.getMoistureContents().getId().toString(), null, amShippingItem.getNewOldLogos().getId().toString(),warehouseLocationId, 
							amShippingItem.getBatch(), null);
					if (!ConvertUtil.isEmpty(stockQuantity)) {
						if (stockQuantity.compareTo(BigDecimal.ZERO) == -1) {
							ExceptionUtil.throwServiceException("产品编码为【"+amShippingItem.getVonderCode()+"】出仓数量大于库存数量");
						}
					} else {
						ExceptionUtil.throwServiceException("产品编码为【"+amShippingItem.getVonderCode()+"】没有库存");
					}
				}else if (billType == 1) {
					stockQuantity = stockService.getStockQuantity(amShipping.getWarehouse().getId(),amShippingItem.getProductOrganization().getId(), 
							amShippingItem.getProduct().getId(),amShippingItem.getProductLevel().getId(),amShippingItem.getColorNumbers().getId().toString(),
							amShippingItem.getMoistureContents().getId().toString(), null, amShippingItem.getNewOldLogos().getId().toString(),warehouseLocationId, 
							amShippingItem.getBatch(), 0);
					if(ConvertUtil.isEmpty(stockQuantity) || 
							(!ConvertUtil.isEmpty(stockQuantity) && 
									(stockQuantity.add(amShippingItem.getQuantity())).compareTo(BigDecimal.ZERO) == 1)){
						ExceptionUtil.throwServiceException("产品编码为【"+amShippingItem.getVonderCode()+"】不能入仓");
					}
				}
			}
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
	}
	
	
	
	
	/**
	 * 单据行项以及批次列表库存校验
	 * 
	 * @param amShipping
	 * @param
	 */
	private void isCheckStock(AmShipping amShipping, Integer billType) {
		try {
			// 仓库
			if (ConvertUtil.isEmpty(amShipping.getWarehouse())) {
				ExceptionUtil.throwServiceException("仓库不能为空");
			}
			//单据类型
			if(billType == 0){
				//是否校验库存
				if (ConvertUtil.isEmpty(amShipping.getWarehouse().getSign())
						|| (!ConvertUtil.isEmpty(amShipping.getWarehouse().getSign())
								&& !amShipping.getWarehouse().getSign())) {
					return;
				}
			}else if (billType == 1) {
				//是否订货入仓
				if (ConvertUtil.isEmpty(amShipping.getWarehouse().getIsOrderWarehousing())
						|| (!ConvertUtil.isEmpty(amShipping.getWarehouse().getIsOrderWarehousing())
								&& !amShipping.getWarehouse().getIsOrderWarehousing())) {
					return;
				}
			}
			// 是否启用库位
			Boolean enableLocation = false;
			if (!ConvertUtil.isEmpty(amShipping.getWarehouse().getEnableLocation())
					&& amShipping.getWarehouse().getEnableLocation()) {
				enableLocation = true;
			}
			// 出入库明细库存校验
			this.isCheckOutEnterStock(amShipping, 0, enableLocation, billType);
			// 是否启用批次
			if (ConvertUtil.isEmpty(amShipping.getWarehouse().getEnableBatch())
					|| (!ConvertUtil.isEmpty(amShipping.getWarehouse().getEnableBatch())
							&& !amShipping.getWarehouse().getEnableBatch())) {
				return;
			}
			// 出入库批次列表库存校验
			this.isCheckOutEnterStock(amShipping, 1, enableLocation, billType);
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
	}

	@Override
	@Transactional
	public void setFlag(Long[] ids, Integer flag) {
		for (Long id : ids) {
			if (!ConvertUtil.isEmpty(id)) {
				AmShipping amShipping = find(id);
				if (ConvertUtil.isEmpty(amShipping)) {
					continue;
				}
				amShipping.setFlag(flag);
				update(amShipping);
			}
		}
	}

	@SuppressWarnings({ "finally", "unused" })
	@Override
	public Map<String, Object> getReceiptIssueReport(Long ObjectId, int typeId, Pageable pageable) {
		if (ObjectId == null || ObjectId == 0) {
			ExceptionUtil.throwServiceException("参数ID不能为空");
		}
		List<Map<String, Object>> listDatas = new ArrayList<Map<String, Object>>();
		Map<String, Object> title = new HashMap<String, Object>();
		// 创建参数实体
		ReportingEntityVo reportingEntityVo = new ReportingEntityVo();
		try {
			// 获取指定id的出入库单信息
			List<Map<String, Object>> lists = saleNatureShippingDao.findReceiptIssueReportById(ObjectId, typeId);
			if (lists != null && lists.size() > 0) {
				Map<String, Object> firstData = lists.get(0);
				title.put("head", CommonUtil.emptyConversion(firstData.get("warehouseName"))
						+ CommonUtil.emptyConversion(firstData.get("billCate")) + "单"); // 报表标题
				title.put("documentNo", CommonUtil.emptyConversion(firstData.get("sn"))); // 单据编号
				title.put("userName", CommonUtil.emptyConversion(firstData.get("storeName"))); // 客户名称
				title.put("address", CommonUtil.emptyConversion(firstData.get("address"))); // 地址
				title.put("collectingUnit", CommonUtil.emptyConversion(firstData.get("storeName"))); // 领用单位
				title.put("contactInformation", CommonUtil.emptyConversion(firstData.get("consignee")) + "/"
						+ CommonUtil.emptyConversion(firstData.get("phone"))); // 联系方式
				title.put("warehouseCategory", CommonUtil.emptyConversion(firstData.get("billCate"))); // 出仓类别
				title.put("cabinetNo", CommonUtil.emptyConversion(firstData.get("containerNumber")) + "/"
						+ CommonUtil.emptyConversion(firstData.get("wagonNumber"))); // 柜(车)号
				// 结尾字段
				title.put("memo", CommonUtil.emptyConversion(firstData.get("memo")));
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				// 合计
				BigDecimal totalBoxQuantity = new BigDecimal("0"); // 箱
				BigDecimal totalBranchQuantity = new BigDecimal("0"); // 支
				BigDecimal totalQuantity = new BigDecimal("0"); // 数量

				for (Map<String, Object> data : lists) {
					Map<String, Object> documentDetail = new HashMap<String, Object>();
					documentDetail.put("nameOrWoodColor", CommonUtil.emptyConversion(data.get("detailDescription"))); // 名称或木种
					documentDetail.put("model", CommonUtil.emptyConversion(data.get("model"))); // 型号
					documentDetail.put("specifications", CommonUtil.emptyConversion(data.get("spec"))); // 规格
					documentDetail.put("piece", CommonUtil.emptyConversion(data.get("box_quantity")).substring(0,
							ConvertUtil.toEmpty(data.get("box_quantity")).length() - 7)); // 件
					totalBoxQuantity = totalBoxQuantity.add(new BigDecimal(
							data.get("box_quantity") == null ? "0" : String.valueOf(data.get("box_quantity"))));

					documentDetail.put("numberOfPieces", CommonUtil.emptyConversion(data.get("branch_quantity"))
							.substring(0, ConvertUtil.toEmpty(data.get("branch_quantity")).length() - 7)); // 支数
					totalBranchQuantity = totalBranchQuantity.add(new BigDecimal(
							data.get("branch_quantity") == null ? "0" : String.valueOf(data.get("branch_quantity"))));

					documentDetail.put("branch", CommonUtil.emptyConversion(data.get("scattered_quantity")).substring(0,
							ConvertUtil.toEmpty(data.get("scattered_quantity")).length() - 7)); // 支/件
					documentDetail.put("number", CommonUtil.emptyConversion(data.get("quantity"))); // 数量
					totalQuantity = totalQuantity.add(
							new BigDecimal(data.get("quantity") == null ? "0" : String.valueOf(data.get("quantity"))));

					documentDetail.put("company", CommonUtil.emptyConversion(data.get("unit"))); // 单位
					documentDetail.put("grade", CommonUtil.emptyConversion(data.get("grade"))); // 等级
					documentDetail.put("colorNumber", CommonUtil.emptyConversion(data.get("colourNumber"))); // 色号
					documentDetail.put("moistureContent", CommonUtil.emptyConversion(data.get("moistureContent"))); // 含水率
					documentDetail.put("bacthRemarks",
							CommonUtil.emptyConversion(data.get("batchSn")) + "/"
									+ CommonUtil.emptyConversion(sdf.format(data.get("completionDate"))) + "/"
									+ CommonUtil.emptyConversion(data.get("batchMemo"))); // 备注
					documentDetail.put("businessDepartment",
							CommonUtil.emptyConversion(data.get("businessDepartment"))); // 事业部
					documentDetail.put("remarks", CommonUtil.emptyConversion(data.get("itemMemo")));
					listDatas.add(documentDetail);
				}
				// 统计合计
				Map<String, Object> documentDetail = new HashMap<String, Object>();
				documentDetail.put("businessDepartment", "合计"); // 事业部
				documentDetail.put("piece", totalBoxQuantity);
				documentDetail.put("numberOfPieces", totalBranchQuantity);
				documentDetail.put("number", totalQuantity);
				listDatas.add(documentDetail);

			} else {
				initializationData(title, listDatas);
			}
		} catch (Exception e) {
			logger.error("出入库单导出异常", e);
			initializationData(title, listDatas);
		} finally {
			title.put("maplist", listDatas);
			return title;
		}

	}

	/**
	 * 初始化数据
	 * 
	 * @param
	 * @param title
	 * @param listDatas
	 */
	@SuppressWarnings("unused")
	void initializationData(Map<String, Object> title, List<Map<String, Object>> listDatas) {
		title.put("head", ""); // 报表标题
		title.put("documentNo", ""); // 单据编号
		title.put("documentDate", ""); // 单据日期
		title.put("warehouse", ""); // 仓库
		title.put("userCode", ""); // 客户编码
		title.put("abbreviation", ""); // 客户简介
		title.put("userName", ""); // 客户名称
		title.put("remarks", ""); // 发运备注
		title.put("address", ""); // 地址
		title.put("collectingUnit", ""); // 领用单位
		title.put("contactInformation", ""); // 联系方式
		title.put("warehouseCategory", ""); // 出仓类别
		title.put("cabinetNo", ""); // 柜(车)号
		title.put("memo", "");
		Map<String, Object> documentDetail = new HashMap<String, Object>();
		documentDetail.put("productCode", ""); // 产品编码
		documentDetail.put("nameOrWoodColor", ""); // 名称或木种
		documentDetail.put("model", ""); // 型号
		documentDetail.put("specifications", ""); // 规格
		documentDetail.put("piece", ""); // 件
		documentDetail.put("numberOfPieces", ""); // 支数
		documentDetail.put("branch", ""); // 支/件
		documentDetail.put("number", ""); // 数量
		documentDetail.put("amountOfMoney", ""); // 金额
		documentDetail.put("company", ""); // 单位
		documentDetail.put("grade", ""); // 等级
		documentDetail.put("colorNumber", ""); // 色号
		documentDetail.put("moistureContent", ""); // 含水率
		documentDetail.put("remarks", ""); // 备注
		documentDetail.put("bacthRemarks", "");
		documentDetail.put("businessDepartment", ""); // 事业部
		listDatas.add(documentDetail);
	}
	
	
	/**
	 * 校验出入库单状态
	 * @param amShipping
	 * @param operationName
	 */
	private void checkAmShippingState(AmShipping amShipping,String operationName){
		
		if(ConvertUtil.isEmpty(amShipping)){
			ExceptionUtil.throwServiceException("该出入库单不存在");
		}
		if(ConvertUtil.isEmpty(amShipping.getBillType())){
			ExceptionUtil.throwServiceException("单据类型不能为空");
		}
		if(!ConvertUtil.isEmpty(amShipping.getStatus()) && (amShipping.getStatus() != 0 && amShipping.getStatus() != 6)){
			ExceptionUtil.throwServiceException("只有单据状态为已保存的出入库单才能"+operationName);
		}
	}
	
	
	
	
	
	/**
	 * 校验移库单状态
	 * @param movelibrary
	 * @param billType
	 * @param movelibraryIds
	 */
	private void checkMovelibraryState(MoveLibrary movelibrary,SystemDict billType,
			Long[] movelibraryIds){
		if(ConvertUtil.isEmpty(movelibrary) || (!ConvertUtil.isEmpty(movelibrary) 
				&& ConvertUtil.isEmpty(movelibrary.getId()))){
			ExceptionUtil.throwServiceException("来源单据号不存在");
		}
		if(movelibraryIds[0].longValue() != movelibrary.getId().longValue()){
			//移库单
			MoveLibrary pMoveLibrary = moveLibraryService.find(movelibrary.getId());
			if(ConvertUtil.isEmpty(pMoveLibrary)){
				ExceptionUtil.throwServiceException("来源单据号不存在");
			}
			if(!ConvertUtil.isEmpty(pMoveLibrary.getMoveStatus()) && !pMoveLibrary.getMoveStatus().equals(MoveStatus.audited)){
				ExceptionUtil.throwServiceException("来源单据号为【"+pMoveLibrary.getSn()+"】的状态必须是已审核的");
			}
			if(!ConvertUtil.isEmpty(billType) && billType.getValue().equals("出仓")){
				if(!ConvertUtil.isEmpty(pMoveLibrary.getMoveStatuss()) && 
						pMoveLibrary.getMoveStatuss().equals(MoveStatuss.completely)){
					ExceptionUtil.throwServiceException("来源单据号为【"+pMoveLibrary.getSn()+"】的状态必须是已审核并且未移库或者部分移库的");
				}
			}else if (!ConvertUtil.isEmpty(billType) && billType.getValue().equals("入仓")) {
				if(!ConvertUtil.isEmpty(pMoveLibrary.getMoveReceiveStatus()) && 
								pMoveLibrary.getMoveReceiveStatus().equals(MoveReceiveStatus.completely)){
					ExceptionUtil.throwServiceException("来源单据号为【"+pMoveLibrary.getSn()+"】的状态必须是已审核并且未接收或者部分接收的");
				}
			}
			movelibraryIds[0] = pMoveLibrary.getId();
		}	
	}
	
	
	/**
	 * 校验移库单
	 * @param amShippingItem
	 * @param billType
	 */
	private void checkMovelibrary(AmShippingItem amShippingItem,SystemDict billType,
			Long[] movelibraryIds){
	
		if(!ConvertUtil.isEmpty(billType) && billType.getValue().equals("出仓")){
			//移库挑库
			this.checkMovelibraryState(amShippingItem.getMovelibraryIssue(),billType,movelibraryIds);
		}else if (!ConvertUtil.isEmpty(billType) && billType.getValue().equals("入仓")) {
			//移库接收
			this.checkMovelibraryState(amShippingItem.getMovelibraryReceive(),billType,movelibraryIds);
		}
		
	}
	
	
	/**
	 * 校验发货单状态
	 * @param amShippingItem
	 * @param shippingIds
	 */
	private void checkShippingState(AmShippingItem amShippingItem,Long[] shippingIds){
		
		Shipping shipping = amShippingItem.getShipping();
		if(ConvertUtil.isEmpty(shipping) || (!ConvertUtil.isEmpty(shipping) && ConvertUtil.isEmpty(shipping.getId()))){
			ExceptionUtil.throwServiceException("该发货单编号不存在");
		}
		if(shippingIds[0].longValue() != shipping.getId().longValue()){
			//发货单
			Shipping pShipping = shippingService.find(shipping.getId());
			if(ConvertUtil.isEmpty(pShipping)){
				ExceptionUtil.throwServiceException("该发货单编号不存在");
			}
			if(!ConvertUtil.isEmpty(pShipping.getStatus()) && (pShipping.getStatus() ==1 || pShipping.getStatus() ==3)){
				shippingIds[0] = pShipping.getId();
			}else{
				ExceptionUtil.throwServiceException("发货单编号为【"+pShipping.getSn()+"】的状态必须是已审核或者部分发货的");
			}
		}
	}
	
	
	
	/**
	 * 校验退货单状态
	 * @param amShippingItem
	 */
	private void checkB2bReturnsState(AmShippingItem amShippingItem,Long[] b2bReturnsIds){
		
		B2bReturns b2bReturns = amShippingItem.getB2bReturns();
		if(ConvertUtil.isEmpty(b2bReturns) || (!ConvertUtil.isEmpty(b2bReturns) && ConvertUtil.isEmpty(b2bReturns.getId()))){
			ExceptionUtil.throwServiceException("该退货单编号不存在");
		}
		if(b2bReturnsIds[0].longValue() != b2bReturns.getId().longValue()){
			//退货单
			B2bReturns pB2bReturns = b2bReturnsService.find(b2bReturns.getId());
			if(ConvertUtil.isEmpty(pB2bReturns)){
				ExceptionUtil.throwServiceException("该退货单编号不存在");
			}
			if(!ConvertUtil.isEmpty(pB2bReturns.getStatus()) && (pB2bReturns.getStatus() != 1&&pB2bReturns.getStatus() != 4)){
				ExceptionUtil.throwServiceException("退货单编号为【"+pB2bReturns.getSn()+"】的状态必须是已审核或者部分退货");
			}
			b2bReturnsIds[0] = pB2bReturns.getId();
		}
	}
	
	
	@Override
	@Transactional
	public void checkWfMovelibraryClose(Long id) throws Exception {
		AmShipping amShipping = find(id);
		List<AmShippingItem> amshippingItems = amShipping.getAmShippingItems();
		if(amshippingItems.isEmpty() || amshippingItems.size() == 0){
			return;
		}
		//校验出入库单状态
		this.checkAmShippingState(amShipping, "审核");
		// 校验总账日期
		this.isCheckTotalDate(amShipping);
		// 批次列表数据校验
		this.isCheckWarehouseBatchItemQuantity(amShipping);
		// 库存校验
		this.isCheckStock(amShipping,1);
		for (int i = 0; i < amshippingItems.size(); i++) {
			AmShippingItem amShippingItem = amshippingItems.get(i);
			MoveLibraryCloseItem moveLibraryCloseItem = amShippingItem.getMoveLibraryCloseItem();
			MoveLibraryClose moveLibraryClose = amShippingItem.getMoveLibraryClose();
			if(!ConvertUtil.isEmpty(moveLibraryCloseItem) && !ConvertUtil.isEmpty(moveLibraryClose)){
				//初始化移库关闭单明细数量
				moveLibraryCloseService.checkParamIsNotNull(moveLibraryCloseItem);
				if (amShippingItem.getQuantity().add(moveLibraryCloseItem.getCloseQuantity()).compareTo(moveLibraryCloseItem.getQuantity()) == 1) {
					ExceptionUtil.throwServiceException("产品编码为【"+amShippingItem.getVonderCode()+"的入仓数量过大");
				} else {
					//数量
					moveLibraryCloseItem.setCloseQuantity(
							amShippingItem.getQuantity().add(
									moveLibraryCloseItem.getCloseQuantity()));
					//支数
					moveLibraryCloseItem.setCloseBoxQuantity(
							amShippingItem.getBoxQuantity().add(
									moveLibraryCloseItem.getCloseBoxQuantity()));
					//箱数
					moveLibraryCloseItem.setCloseBranchQuantity(
							amShippingItem.getBranchQuantity().add(
									moveLibraryCloseItem.getCloseBranchQuantity()));
				}
				moveLibraryCloseItemService.update(moveLibraryCloseItem);
				this.checkMovelibraryClose(moveLibraryClose.getId());
				amShippingItem.setStatus(1);
			}
		}
		amShipping.setAmShippingItems(amshippingItems);
		amShipping.setStatus(1);
	      if(amShipping.getStatus()!=6) {
              amShipping.setCheckStoreMember(storeMemberBaseService.getCurrent());
          }
		update(amShipping);
		//操作类型
		List<SystemDict> actionTypeList =  roleJurisdictionUtil.getSystemDictList("actionType", "审核");
		if(actionTypeList.isEmpty() || actionTypeList.size()==0){
			ExceptionUtil.throwServiceException("操作类型不能为空");
		}
		//加载库存
		warehouseStockService.loadWarehouseStock(amShipping, actionTypeList.get(0));
		
	}

	@Override
	public void checkMovelibraryClose(Long movelibraryCloseId) throws Exception {
		/* 关闭数量 */
		BigDecimal closeQuantity = BigDecimal.ZERO;
		/* 数量 */
		BigDecimal quantity = BigDecimal.ZERO;
		MoveLibraryClose moveLibraryClose = moveLibraryCloseService.find(movelibraryCloseId);
		if (!ConvertUtil.isEmpty(moveLibraryClose)) {
			List<MoveLibraryCloseItem> moveLibraryCloseItems = moveLibraryClose.getMoveLibraryCloseItems();
			if(moveLibraryCloseItems.isEmpty() || moveLibraryCloseItems.size() == 0){
				return;
			}
			for (MoveLibraryCloseItem moveLibraryCloseItem : moveLibraryCloseItems) {
				if(ConvertUtil.isEmpty(moveLibraryCloseItem.getCloseQuantity())){
					moveLibraryCloseItem.setCloseQuantity(BigDecimal.ZERO);
				}
				closeQuantity = closeQuantity.add(moveLibraryCloseItem.getCloseQuantity());
				quantity = quantity.add(moveLibraryCloseItem.getQuantity());
			}
			if (closeQuantity.compareTo(quantity) == 0) {
				moveLibraryClose.setStatus(5);
			} else if (closeQuantity.compareTo(quantity) == -1 && quantity.compareTo(BigDecimal.ZERO) != 0) {
				moveLibraryClose.setStatus(4);
			}
			moveLibraryCloseService.update(moveLibraryClose);
		}
		
	}
	
	/**
	 * 设置单据明细旗标
	 */
	@Override
	@Transactional
	public void setBillItemFlag(Long[] ids, Integer flag, Integer billType) {
		
		for (Long id : ids) {
			if (!ConvertUtil.isEmpty(id)) {
				if(billType == 0){
					//发货挑库
					ShippingItem shippingItem = shippingItemService.find(id);
					if (ConvertUtil.isEmpty(shippingItem)) {
						continue;
					}
					shippingItem.setFlag(flag);
					shippingItemService.update(shippingItem);
				}else if (billType == 1) {
					//退货接收
					B2bReturnsItem b2bReturnsItem = b2bReturnsItemService.find(id);
					if (ConvertUtil.isEmpty(b2bReturnsItem)) {
						continue;
					}
					b2bReturnsItem.setFlag(flag);
					b2bReturnsItemService.update(b2bReturnsItem);
				}else if (billType == 2) {
					//移库发货
					MoveLibraryItem moveLibraryItem = moveLibraryItemService.find(id);
					if (ConvertUtil.isEmpty(moveLibraryItem)) {
						continue;
					}
					moveLibraryItem.setIssueFlag(flag);
					moveLibraryItemService.update(moveLibraryItem);
				}else if (billType == 3) {
					//移库接收
					MoveLibraryItem moveLibraryItem = moveLibraryItemService.find(id);
					if (ConvertUtil.isEmpty(moveLibraryItem)) {
						continue;
					}
					moveLibraryItem.setReceiveFlag(flag);
					moveLibraryItemService.update(moveLibraryItem);
				}else if (billType == 4) {
					//移库返挑入仓
					MoveLibraryCloseItem moveLibraryCloseItem = moveLibraryCloseItemService.find(id);
					if (ConvertUtil.isEmpty(moveLibraryCloseItem)) {
						continue;
					}
					moveLibraryCloseItem.setFlag(flag);
					moveLibraryCloseItemService.update(moveLibraryCloseItem);
				}
			}
		}
	}
	void updateShippingPriceDifference(AmShipping amShipping) {
	    /**
         * 更新价差表实发数量
         */
	    PriceDifference priceDifference = null;
        List<Filter> filters = new ArrayList<Filter>();

	    for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
	        
	        ShippingItem shippingItem =amShippingItem.getShippingItem();
          
            filters.clear();
            filters.add(Filter.eq("shippingItem", shippingItem.getId()));
            List<PriceDifference> priceDifferences=priceDifferenceService.findList(null, filters, null);
            if(priceDifferences.size()>0) {
                priceDifference=priceDifferences.get(0);
                BigDecimal square = priceDifference.getShippedQuantity()==null ? BigDecimal.ZERO : priceDifference.getShippedQuantity();
                if (shippingItem != null) {
                    priceDifference.setShippedQuantity(square.add(amShippingItem.getQuantity()));
                    priceDifference.setErpSn(shippingItem.getShipping().getSn());
                    priceDifferenceService.update(priceDifference);
                }
            }
        }
	}
	void updateB2BReturnPriceDifference(AmShipping amShipping) {
	    PriceDifference priceDifference = null;
        List<Filter> filters = new ArrayList<Filter>();
	    for ( AmShippingItem amShippingItem  : amShipping.getAmShippingItems()) {
         
            B2bReturnsItem b2bReturnsItem =amShippingItem.getB2bReturnsItem();
            filters.clear();
            filters.add(Filter.eq("b2bReturnsItem", b2bReturnsItem.getId()));
            
            List<PriceDifference> priceDifferences=priceDifferenceService.findList(null, filters, null);
            if(priceDifferences.size()>0) {
                priceDifference=priceDifferences.get(0);
                BigDecimal square = priceDifference.getShippedQuantity()==null?BigDecimal.ZERO:priceDifference.getShippedQuantity();
                if (b2bReturnsItem != null) {
                    priceDifference.setShippedQuantity(square.add(amShippingItem.getQuantity()));
                    priceDifference.setErpSn(b2bReturnsItem.getB2bReturns().getSn());
                    priceDifferenceService.update(priceDifference);
                }
            }
        }

	}
}
