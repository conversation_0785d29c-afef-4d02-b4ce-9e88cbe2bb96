package net.shopxx.order.service.impl;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletContext;

import net.shopxx.hubbase.entity.A1_MessageToH;
import net.shopxx.intf.CancelRealTimePush;
import net.shopxx.intf.service.A1_MessageToHService;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.context.ServletContextAware;
import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.swetake.util.Qrcode;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.intf.service.IntfService;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.DeliveryCorp;
import net.shopxx.basic.entity.DriverInfo;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.finance.entity.PolicyCountContract;
import net.shopxx.finance.service.PolicyCountContractService;
import net.shopxx.finance.service.PolicyCountService;
import net.shopxx.intf.NatureOrderCancel;
import net.shopxx.intf.NatureOrderItemCancel;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.link5.service.ShippingInfoToLink5Service;
import net.shopxx.member.entity.CreditRecharge;
import net.shopxx.member.entity.CreditRechargeContract;
import net.shopxx.member.entity.DepositRechargeContract;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreSbu;
import net.shopxx.member.service.CreditRechargeContractService;
import net.shopxx.member.service.CreditRechargeService;
import net.shopxx.member.service.DepositRechargeContractService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.member.service.impl.StoreMemberBaseServiceImpl;
import net.shopxx.order.dao.ShippingDao;
import net.shopxx.order.entity.Cost;
import net.shopxx.order.entity.OffsiteBranch;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.entity.Order.ShippingStatus;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.PriceDifference;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingAttach;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.entity.TriplicateForm;

import net.shopxx.order.service.CostService;
import net.shopxx.order.service.OffsiteBranchService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.PriceDifferenceService;
import net.shopxx.order.service.SaleShippingNatureService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.order.service.TriplicateFormService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductPriceHead;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductPriceHeadService;
import net.shopxx.product.service.ProductPriceService;
import net.shopxx.stock.entity.StockOut;
import net.shopxx.stock.entity.StockTaskBuilder;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseBillBatch;
import net.shopxx.stock.service.StockOutService;
import net.shopxx.stock.service.StockService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseBatchService;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.CommonVariable;
import net.shopxx.util.NumberToCN;
import net.shopxx.util.SnUtil;
import net.shopxx.util.dto.DocumentDetails;
import net.shopxx.util.dto.ReportingEntityVo;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;
@Service("shippingServiceImpl")
public class ShippingServiceImpl extends WfBillBaseServiceImpl<Shipping> implements ShippingService,ServletContextAware {
	
	private static Logger logger = LoggerFactory.getLogger(ShippingServiceImpl.class);

	@Resource(name = "shippingDao")
	private ShippingDao shippingDao;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "stockServiceImpl")
	private StockService stockService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "stockOutServiceImpl")
	private StockOutService stockOutService;
	@Resource(name = "lockDataDao")
	private LockDataDao lockDataDao;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "triplicateFormServiceImpl")
	private TriplicateFormService triplicateFormService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "creditRechargeContractServiceImpl")
	private CreditRechargeContractService creditRechargeContractService;
	@Resource(name = "depositRechargeContractServiceImpl")
	private DepositRechargeContractService depositRechargeContractService;
	@Resource(name = "policyCountContractServiceImpl")
	private PolicyCountContractService policyCountContractService;
	@Resource(name = "policyCountServiceImpl")
	private PolicyCountService policyCountService;
	@Resource(name = "creditRechargeServiceImpl")
	private CreditRechargeService creditRechargeService;
	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "costServiceImpl")
	private CostService costService;
	@Resource(name = "offsiteBranchServiceImpl")
	private OffsiteBranchService offsiteBranchService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "intfServiceImpl")
	private IntfService intfService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "warehouseBatchServiceImpl")
	private WarehouseBatchService warehouseBatchService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "priceDifferenceServiceImpl")
	private PriceDifferenceService priceDifferenceService;
	@Resource(name = "storeSbuServiceImpl")
    private StoreSbuService storeSbuService;
	@Resource(name = "productPriceServiceImpl")
	private ProductPriceService productPriceService;
	
	@Resource(name = "memberRankBaseServiceImpl")
    private MemberRankBaseService memberRankBaseService;
	 @Resource(name = "shippingInfoToLink5ServiceImpl")
	private ShippingInfoToLink5Service shippingInfoToLink5Service;
	/** servletContext */
	private ServletContext servletContext;
	

	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}

	@Override
	@Transactional
	public void save(Shipping shipping,Warehouse warehouse) {
		//系统单据类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("remark", CommonVariable.NOTICE_TYPE));
		SystemDict systemType = systemDictBaseService.systemBillType(filters);
		BigDecimal amount = BigDecimal.ZERO;
		BigDecimal tVolume = BigDecimal.ZERO;
		BigDecimal tWeight = BigDecimal.ZERO;
		List<String> orderSns = new ArrayList<String>();
		//订单Id
		Long[] orderIds1 = new Long[]{0L};
		List<ShippingItem> shippingItems = shipping.getShippingItems();
		for (Iterator<ShippingItem> iterator = shippingItems.iterator(); iterator.hasNext();) {
			ShippingItem shippingItem = iterator.next();
			if(ConvertUtil.isEmpty(shippingItem)){
				iterator.remove();
				continue;
			}
			//校验订单状态
			this.checkOrderStatus(shippingItem.getOrder(),orderIds1);
			Order pOrder = orderService.find(shippingItem.getOrder().getId());
			//订单明细
			OrderItem orderItem = shippingItem.getOrderItem();
			if(ConvertUtil.isEmpty(orderItem) || (!ConvertUtil.isEmpty(orderItem) && ConvertUtil.isEmpty(orderItem.getId()))){
				ExceptionUtil.throwServiceException("该订单编号明细不存在");
			}
			OrderItem pOrderItem = orderItemService.find(orderItem.getId());
			//产品
			if(ConvertUtil.isEmpty(shippingItem.getProduct()) ||
						(!ConvertUtil.isEmpty(shippingItem.getProduct()) && 
								ConvertUtil.isEmpty(shippingItem.getProduct().getId()))){
				ExceptionUtil.throwServiceException("该产品行不存在");
			}
			Product product = productBaseService.find(shippingItem.getProduct().getId());
			//单据数量
			BigDecimal quantity = shippingItem.getQuantity();
			if (ConvertUtil.isEmpty(quantity) || (!ConvertUtil.isEmpty(quantity) && quantity.compareTo(BigDecimal.ZERO) < 1)) {
				// 发货数量必须大于0
				ExceptionUtil.throwServiceException("15154");
			}
			//色号
			if(ConvertUtil.isEmpty(shippingItem.getColourNumbers()) ||
					(!ConvertUtil.isEmpty(shippingItem.getColourNumbers()) && 
							ConvertUtil.isEmpty(shippingItem.getColourNumbers().getId()))){
				shippingItem.setColourNumbers(null);
			}
			//含水率
			if(ConvertUtil.isEmpty(shippingItem.getMoistureContents()) ||
					(!ConvertUtil.isEmpty(shippingItem.getMoistureContents()) && 
							ConvertUtil.isEmpty(shippingItem.getMoistureContents().getId()))){
				shippingItem.setMoistureContents(null);
			}
			if (!ConvertUtil.isEmpty(pOrderItem)) {
				if (getShippingItemQuantities(orderItem.getId()).add(quantity).setScale(6, BigDecimal.ROUND_HALF_DOWN)
						.compareTo(pOrderItem.getQuantity().setScale(6,BigDecimal.ROUND_HALF_DOWN)) == 1) {
					// 发货数量过大，请修改
					ExceptionUtil.throwServiceException("15163");
				}
				// 回写订单明细计划发货数量
				BigDecimal shipPlanQuantity = new BigDecimal("0");
				if(!ConvertUtil.isEmpty(pOrderItem.getShipPlanQuantity())){
					shipPlanQuantity = pOrderItem.getShipPlanQuantity();
				}
				pOrderItem.setShipPlanQuantity(shipPlanQuantity.add(quantity));
				orderItemService.update(pOrderItem);
			}
			shippingItem.setOrderItem(pOrderItem);
			shippingItem.setOrder(pOrder);
			shippingItem.setOrderSn(pOrder.getSn());
			shippingItem.setOutTradeNo(pOrder.getOutTradeNo());
			shippingItem.setOrderType(2);
			shippingItem.setSn(pOrderItem.getSn());
			shippingItem.setVonderCode(pOrderItem.getVonderCode());
			shippingItem.setModel(pOrderItem.getModel());
			shippingItem.setName(pOrderItem.getName());
			shippingItem.setFullName(pOrderItem.getFullName());
			shippingItem.setDescription(pOrderItem.getDescription());
			shippingItem.setThumbnail(pOrderItem.getThumbnail());
			shippingItem.setProduct(product);
			shippingItem.setStatus(0);
			shippingItem.setShippedQuantity(BigDecimal.ZERO);
			shippingItem.setReturnQuantity(BigDecimal.ZERO);
			// 发货单明细数据处理
			BigDecimal pVolume = product == null ? BigDecimal.ZERO
					: (product.getVolume() == null ? BigDecimal.ZERO : product.getVolume());
			BigDecimal pWeight = product == null ? BigDecimal.ZERO
					: (product.getWeight() == null ? BigDecimal.ZERO : product.getWeight());
			shippingItem.setVolume(pVolume);
			shippingItem.setWeight(pWeight);
			shippingItem.setShipping(shipping);
			shippingItem.setPrice(pOrderItem.getPrice());
			amount = amount.add(shippingItem.getQuantity().multiply(shippingItem.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
			//行体积=每箱体积（产品体积）* 下单箱数；
			BigDecimal volume = pVolume.multiply(shippingItem.getBoxQuantity() == null ? BigDecimal.ZERO
					: shippingItem.getBoxQuantity());// 行合计体积
			tVolume = tVolume.add(volume);// 总体积
			//行重量=行重量 * 平方数；
			BigDecimal weight = pWeight.multiply(quantity);// 行合计重量
			tWeight = tWeight.add(weight);// 总重量
			String osn = pOrder == null ? null : pOrder.getSn();
			if (osn != null && !orderSns.contains(osn)) {
				orderSns.add(osn);
			}
			//批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if(!ConvertUtil.isEmpty(shippingItem.getBatch())){
				String[] batchs = shippingItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if(!ConvertUtil.isEmpty(batchs[i])){
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						//单据类型
						warehouseBillBatch.setSysType(systemType);
						//发货单
						warehouseBillBatch.setShipping(shipping);
						//发货单明细
						warehouseBillBatch.setShippingItem(shippingItem);
						//批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			shippingItem.setWarehouseBillBatchs(warehouseBillBatchList);
		}
		if (shippingItems.isEmpty()) {
			// 请添加发货产品
			ExceptionUtil.throwServiceException("15152");
		}
		//是否发货库存校验
		if(!ConvertUtil.isEmpty(warehouse.getIsShippedStockCheck()) && warehouse.getIsShippedStockCheck()){
			this.IsCheckStockOnhandQuantity(shippingItems, warehouse);
		}
		shipping.setAmount(amount);
		shipping.setStatus(0);
		shipping.setVolume(tVolume);
		shipping.setWeight(tWeight);
		shipping.setStoreMember(storeMemberBaseService.getCurrent());
		shipping.setSn(SnUtil.getShippingSn());
		shipping.setShippingTime(new Date());
		// 附件
		List<ShippingAttach> shippingAttachs = shipping.getShippingAttachs();
		for (Iterator<ShippingAttach> iterator = shippingAttachs.iterator(); iterator.hasNext();) {
			ShippingAttach shippingAttach = iterator.next();
			if (shippingAttach == null || shippingAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (shippingAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			shippingAttach.setFileName(shippingAttach.getName() + "."+ shippingAttach.getSuffix());
			shippingAttach.setShipping(shipping);
			shippingAttach.setStoreMember(storeMemberBaseService.getCurrent());
		}
		shipping.setShippingAttachs(shippingAttachs);
		save(shipping);
		orderFullLinkService.addFullLink(4,orderSns,shipping.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "发货通知单", })
						+ shipping.getSn(),null);
	}

	public void saveProject(Shipping shipping, Warehouse warehouse,
			DeliveryCorp delivery, Area area, List<Long> orderIds,
			Integer flag, String[] creditRechargeSn,
			BigDecimal[] creditRechargeDistributionAmount,
			String[] depositRechargeSn,
			BigDecimal[] depositRechargeDistributionAmount,
			String[] policyCountNo, BigDecimal[] policyCountDistributionAmount,
			Long[] depositPaymentBatchId, String[] saleNode) {

		// 锁定订单表数据，防止并发
		String ids = "";
		for (int i = 0; i < orderIds.size(); i++) {
			Long id = orderIds.get(i);
			if (i == orderIds.size() - 1) {
				ids += id;
			}
			else {
				ids += id + ",";
			}
		}
		lockDataDao.lockOrder(ids);

		// 启用锁库存模式：0 否，1 是
		int useLockStock = Integer.parseInt(SystemConfig.getConfig("useLockStock",
				WebUtils.getCurrentCompanyInfoId()));
		List<ShippingItem> shippingItems = shipping.getShippingItems();
		if (useLockStock == 1) {
			// 锁库存
			String productIds = "";
			for (ShippingItem shippingItem : shippingItems) {
				Product product = shippingItem.getProduct();
				if (product != null) {
					productIds += "," + product.getId();
				}
			}
			if (!ConvertUtil.isEmpty(productIds)) {
				productIds = productIds.substring(1);
				lockDataDao.lockStockByProduct(productIds);
			}
		}

		BigDecimal amount = BigDecimal.ZERO;
		BigDecimal tVolume = BigDecimal.ZERO;
		BigDecimal tWeight = BigDecimal.ZERO;
		String contract_no = "";
		List<String> orderSns = new ArrayList<String>();
		for (Iterator<ShippingItem> iterator = shippingItems.iterator(); iterator.hasNext();) {
			ShippingItem shippingItem = iterator.next();
			OrderItem orderItem = shippingItem.getOrderItem();
			// if (orderItem == null) {
			// //选择的订单明细不存在
			// ExceptionUtil.throwServiceException("15153");
			// }

			Warehouse pWarehouse = null;
			if (flag == 0) {
				// 大自然
				pWarehouse = warehouse;
			}
			else if (flag == 1) {
				// 天加
				Long pWarehouseId = (shippingItem.getWarehouse() != null) ? shippingItem.getWarehouse()
						.getId()
						: null;
				pWarehouse = warehouseBaseService.find(pWarehouseId);
				if (pWarehouse == null) {
					// 请为发货项选择发货仓库
					ExceptionUtil.throwServiceException("15182");
				}
				warehouse = pWarehouse;
			}

			shippingItem.setWarehouse(pWarehouse);
			BigDecimal quantity = shippingItem.getQuantity();
			if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 1) {
				// 发货数量必须大于0
				ExceptionUtil.throwServiceException("15154");
			}
			OrderItem pOrderItem = orderItemService.find(orderItem == null ? null
					: orderItem.getId());
			Order pOrder = orderService.find(shippingItem.getOrder() == null ? null
					: shippingItem.getOrder().getId());
			// Long wId = pOrderItem.getWarehouse() == null ? 0L
			// : pOrderItem.getWarehouse().getId();
			Long productId = shippingItem.getProduct().getId();
			Product product = productBaseService.find(shippingItem.getProduct()
					.getId());

			if (useLockStock == 1 && productId != null && warehouse != null) {

				// if (wId.longValue() != warehouse.getId().longValue()) {
				// //必须选择同一仓库的订单明进行发货
				// ExceptionUtil.throwServiceException("15156");
				// }

				Map<String, Object> stockMap = stockService.findLockStock(productId,
						warehouse.getId());
				BigDecimal useable_stock = BigDecimal.ZERO;
				if (stockMap != null) {
					useable_stock = new BigDecimal(
							stockMap.get("useable_stock").toString());
				}
				if (quantity.compareTo(useable_stock) == 1) {
					// 产品[{0}]可售库存[{1}],发货数[{2}],库存不足
					ExceptionUtil.throwServiceException("15180",
							product.getName(),
							useable_stock.stripTrailingZeros(),
							quantity.stripTrailingZeros());
				}

			}
			if (pOrderItem != null) {
				if (getShippingItemQuantities(orderItem.getId()).add(quantity)
						.compareTo(pOrderItem.getQuantity()) == 1) {
					// if (getShippingItemQuantities(orderItem.getId()) +
					// quantity > pOrderItem.getQuantity()) {
					// 发货数量过大，请修改
					ExceptionUtil.throwServiceException("15163");
				}
				// 回写订单明细计划发货数量
				BigDecimal shipPlanQuantity = pOrderItem.getShipPlanQuantity() == null ? BigDecimal.ZERO
						: pOrderItem.getShipPlanQuantity();
				pOrderItem.setShipPlanQuantity(shipPlanQuantity.add(quantity));
				orderItemService.update(pOrderItem);
			}

			shippingItem.setOrderItem(pOrderItem);
			if (pOrder != null) {
				shippingItem.setOrder(pOrder);
				shippingItem.setOrderSn(pOrder.getSn());
				shippingItem.setOutTradeNo(pOrder.getOutTradeNo());
				contract_no = pOrder.getContractSn();
			}
			shippingItem.setOrderType(2);

			shippingItem.setSn(pOrderItem.getSn());
			shippingItem.setVonderCode(pOrderItem.getVonderCode());
			shippingItem.setModel(pOrderItem.getModel());
			shippingItem.setName(pOrderItem.getName());
			shippingItem.setFullName(pOrderItem.getFullName());
			shippingItem.setDescription(pOrderItem.getDescription());
			shippingItem.setThumbnail(pOrderItem.getThumbnail());
			shippingItem.setProduct(product);

			shippingItem.setShippedQuantity(BigDecimal.ZERO);
			shippingItem.setReturnQuantity(BigDecimal.ZERO);

			// 发货单明细数据处理
			BigDecimal pVolume = product == null ? BigDecimal.ZERO
					: (product.getVolume() == null ? BigDecimal.ZERO
							: product.getVolume());
			BigDecimal pWeight = product == null ? BigDecimal.ZERO
					: (product.getWeight() == null ? BigDecimal.ZERO
							: product.getWeight());

			shippingItem.setVolume(pVolume);
			shippingItem.setWeight(pWeight);
			shippingItem.setShipping(shipping);
			shippingItem.setPrice(pOrderItem.getPrice());
			amount = amount.add(shippingItem.getQuantity()
					.multiply(shippingItem.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));

			BigDecimal volume = pVolume.multiply(quantity);// 行合计体积
			tVolume = tVolume.add(volume);// 总体积

			BigDecimal weight = pWeight.multiply(quantity);// 行合计重量
			tWeight = tWeight.add(weight);// 总重量

			String osn = pOrder == null ? null : pOrder.getSn();
			if (osn != null && !orderSns.contains(osn)) {
				orderSns.add(osn);
			}
		}
		// 发货单数据处理
		shipping.setAmount(amount);
		shipping.setDelivery(delivery);
		shipping.setWarehouse(warehouse);
		shipping.setStatus(0);
		shipping.setArea(area);
		shipping.setVolume(tVolume);
		shipping.setWeight(tWeight);
		shipping.setStoreMember(storeMemberBaseService.getCurrent());
		shipping.setSn(SnUtil.getShippingSn());

		// 附件
		List<ShippingAttach> shippingAttachs = shipping.getShippingAttachs();
		for (Iterator<ShippingAttach> iterator = shippingAttachs.iterator(); iterator.hasNext();) {
			ShippingAttach shippingAttach = iterator.next();
			if (shippingAttach == null || shippingAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (shippingAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			shippingAttach.setFileName(shippingAttach.getName()
					+ "."
					+ shippingAttach.getSuffix());
			shippingAttach.setShipping(shipping);
			shippingAttach.setStoreMember(storeMemberBaseService.getCurrent());
		}
		shipping.setShippingAttachs(shippingAttachs);

		String shippingSn = shipping.getSn();

		Cost iCost = new Cost();
		for (Iterator<Cost> iterator = shipping.getCosts().iterator(); iterator.hasNext();) {
			Cost cost = iterator.next();
			if (cost.getCostType() == null
					|| cost.getCostType().getId() == null
					&& cost.getName() == null
					&& cost.getAmount() == null
					&& (cost.getSaleOrg() == null || cost.getSaleOrg().getId() == null)
					&& cost.getSaleOrgAmount() == null) {
				iterator.remove();
				continue;
			}

			if (cost.getCostType() == null
					|| cost.getCostType().getId() == null) {
				ExceptionUtil.throwServiceException("费用类型不能为空");
			}
			if (cost.getName() == null) {
				ExceptionUtil.throwServiceException("费用名称不能为空");
			}
			if (cost.getAmount() == null) {
				ExceptionUtil.throwServiceException("费用金额不能为空");
			}
			if (cost.getAmount().doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("费用金额不能小于等于0");
			}

//			if (cost.getSaleOrg() == null || cost.getSaleOrg().getId() == null) {
//				ExceptionUtil.throwServiceException("分公司不能为空");
//			}
//			if (cost.getSaleOrgAmount() == null) {
//				ExceptionUtil.throwServiceException("分公司金额不能为空");
//			}
//			if (cost.getSaleOrgAmount().doubleValue() <= 0) {
//				ExceptionUtil.throwServiceException("分公司金额不能小于等于0");
//			}
			cost.setBillCode(shippingSn);
			cost.setBillType(1);
			if (cost.getId() == null) {
				costService.save(cost);
			}
			costService.update(cost);
		}

		OffsiteBranch iOffsiteBranch = new OffsiteBranch();
		iOffsiteBranch.setSaleOrg(new SaleOrg());

		for (Iterator<OffsiteBranch> iterator = shipping.getOffsiteBranchs()
				.iterator(); iterator.hasNext();) {
			OffsiteBranch offsiteBranch = iterator.next();

			if ((offsiteBranch.getSaleOrg() == null || offsiteBranch.getSaleOrg()
					.getId() == null)
					&& offsiteBranch.getRate() == null
					&& offsiteBranch.getAmount() == null) {
				iterator.remove();
				continue;
			}

			if (offsiteBranch.getSaleOrg() == null
					|| offsiteBranch.getSaleOrg().getId() == null) {
				ExceptionUtil.throwServiceException("分公司不能为空");
			}

			if (offsiteBranch.getRate() == null) {
				ExceptionUtil.throwServiceException("应收占比不能为空");
			}
			if (offsiteBranch.getAmount() == null) {
				ExceptionUtil.throwServiceException("金额不能为空");
			}
			if (offsiteBranch.getAmount().doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("金额不能小于等于0");
			}

			offsiteBranch.setBillCode(shippingSn);
			offsiteBranch.setBillType(1);
			offsiteBranchService.save(offsiteBranch);
		}

		save(shipping);

		BigDecimal finalAmount = shipping.getAmount();

		BigDecimal itemTotalAmount = BigDecimal.ZERO;
		Long storeId = shipping.getStore().getId();

		for (int i = 0; creditRechargeSn != null && i < creditRechargeSn.length; i++) {

			String code = creditRechargeSn[i];
			if (code == null) {
				ExceptionUtil.throwServiceException("授信单号不能为空");
			}

			BigDecimal lineAmount = (creditRechargeDistributionAmount != null && creditRechargeDistributionAmount.length > 0) ? creditRechargeDistributionAmount[i]
					: null;
			if (lineAmount == null) {
				ExceptionUtil.throwServiceException("授信分配金额不能为空");
			}
			if (lineAmount.doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("授信分配金额不能小于等于零");
			}

			List<Map<String, Object>> obj = creditRechargeService.findList(null,
					code);
			BigDecimal actual_amount = BigDecimal.ZERO;
			BigDecimal available_amount = BigDecimal.ZERO;
			if (obj != null && obj.size() > 0) {
				Map<String, Object> data = obj.get(0);
				if (data.get("actual_amount") != null) {
					actual_amount = new BigDecimal(data.get("actual_amount")
							.toString());
				}
				if (data.get("available_amount") != null) {
					available_amount = new BigDecimal(
							data.get("available_amount").toString());
				}
			}
			else {
				ExceptionUtil.throwServiceException("授信单号【" + code + "】不存在");
			}

			BigDecimal vailAmount = actual_amount.subtract(available_amount);// 当前可分配金额
			if (lineAmount.compareTo(vailAmount) == 1) {
				ExceptionUtil.throwServiceException("授信单号【"
						+ code
						+ "】的可分配金额："
						+ vailAmount
						+ "，分配金额："
						+ lineAmount
						+ "，差额："
						+ vailAmount.subtract(lineAmount));
			}

			itemTotalAmount = itemTotalAmount.add(lineAmount);

			CreditRechargeContract creditRechargeContract = new CreditRechargeContract();
			creditRechargeContract.setCompanyInfoId(shipping.getCompanyInfoId());
			creditRechargeContract.setCreditRechargeNo(creditRechargeSn[i]);
			creditRechargeContract.setOrderAmount(lineAmount);
			creditRechargeContract.setOrderNo(shippingSn);
			creditRechargeContract.setOrderType(2);
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", creditRechargeSn[i]));
			List<CreditRecharge> creditRecharges = creditRechargeService.findList(1,
					filters,
					null);
			if (!creditRecharges.isEmpty()) {
				creditRechargeContract.setRechargeType(creditRecharges.get(0)
						.getRechargeType());
			}
			creditRechargeContract.setContract_no(contract_no);
			creditRechargeContractService.save(creditRechargeContract);

		}

		for (int i = 0; depositRechargeSn != null
				&& i < depositRechargeSn.length; i++) {

			String code = depositRechargeSn[i];
			if (code == null) {
				ExceptionUtil.throwServiceException("回款单号不能为空");
			}

			BigDecimal lineAmount = (depositRechargeDistributionAmount != null && depositRechargeDistributionAmount.length > 0) ? depositRechargeDistributionAmount[i]
					: null;
			if (lineAmount == null) {
				ExceptionUtil.throwServiceException("回款分配金额不能为空");
			}
			if (lineAmount.doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("回款分配金额不能小于等于零");
			}

			List<Map<String, Object>> obj = depositRechargeContractService.findPaymentBatch(contract_no,
					"发货通知");
			BigDecimal actual_amount = BigDecimal.ZERO;
			BigDecimal available_amount = BigDecimal.ZERO;

			if (obj != null && obj.size() > 0) {
				Map<String, Object> data = obj.get(0);
				if (data.get("actual_amount") != null) {
					actual_amount = new BigDecimal(data.get("actual_amount")
							.toString());
				}
				if (data.get("available_amount") != null) {
					available_amount = new BigDecimal(
							data.get("available_amount").toString());
				}
			}
			else {
				ExceptionUtil.throwServiceException("回款单号【" + code + "】不存在");
			}

			BigDecimal vailAmount = actual_amount.subtract(available_amount);// 可分配金额
			if (lineAmount.compareTo(vailAmount) == 1) {
				ExceptionUtil.throwServiceException("可分配回款金额："
						+ vailAmount
						+ "，分配金额："
						+ lineAmount
						+ "，差额："
						+ vailAmount.subtract(lineAmount));
			}

			itemTotalAmount = itemTotalAmount.add(lineAmount);

			DepositRechargeContract depositRechargeContract = new DepositRechargeContract();
			depositRechargeContract.setCompanyInfoId(shipping.getCompanyInfoId());
			depositRechargeContract.setDepositRechargeNo(depositRechargeSn[i]);
			depositRechargeContract.setOrderAmount(lineAmount);
			depositRechargeContract.setOrderNo(shippingSn);
			depositRechargeContract.setOrderType(2);
			if (depositPaymentBatchId != null
					&& depositPaymentBatchId.length > 0) {
				depositRechargeContract.setContractPaymentBatchId(depositPaymentBatchId[i]);
			}
			if (saleNode != null && saleNode.length > 0) {
				depositRechargeContract.setSaleNode(saleNode[i]);
			}
			depositRechargeContractService.save(depositRechargeContract);

		}

		if (policyCountDistributionAmount != null) {
			if (policyCountDistributionAmount.length > 1) {
				ExceptionUtil.throwServiceException("回款类型【政策】只能维护一条");
			}

			if (policyCountDistributionAmount.length == 1) {

				BigDecimal actualAmount = policyCountService.findTotalActualAmount(storeId);
				BigDecimal availableAmount = policyCountService.findTotalAvailableAmount(storeId);

				for (int i = 0; i < policyCountDistributionAmount.length; i++) {

					if (policyCountNo == null
							|| policyCountNo.length == 0
							|| policyCountNo[i] == null) {
						ExceptionUtil.throwServiceException("请先获取政策金额");
					}

					BigDecimal lineAmount = policyCountDistributionAmount[i];
					if (lineAmount == null) {
						ExceptionUtil.throwServiceException("政策分配金额不能为空");
					}
					if (lineAmount.doubleValue() <= 0) {
						ExceptionUtil.throwServiceException("政策分配金额不能小于等于零");
					}

					BigDecimal vailAmount = actualAmount.subtract(availableAmount);// 可分配金额
					if (lineAmount.compareTo(vailAmount) == 1) {
						ExceptionUtil.throwServiceException("可分配政策金额："
								+ vailAmount
								+ "，分配金额："
								+ lineAmount
								+ "，差额："
								+ vailAmount.subtract(lineAmount));
					}

					itemTotalAmount = itemTotalAmount.add(lineAmount);

					PolicyCountContract policyCountContract = new PolicyCountContract();
					policyCountContract.setCompanyInfoId(shipping.getCompanyInfoId());
					policyCountContract.setPolicyCountNo(policyCountNo[i]);
					policyCountContract.setOrderAmount(lineAmount);
					policyCountContract.setOrderNo(shippingSn);
					policyCountContract.setOrderType(2);
					policyCountContractService.save(policyCountContract);

				}

			}

		}
		BigDecimal paymentType = BigDecimal.ZERO;

		paymentType = shipping.getPaymentType();
		if (itemTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP)
				.compareTo(finalAmount.multiply(paymentType.divide(new BigDecimal(
						100),
						2,
						BigDecimal.ROUND_HALF_UP))
						.setScale(2, BigDecimal.ROUND_HALF_UP)) != 0) {
			ExceptionUtil.throwServiceException("本次发货通知单需要货款金额："
					+ finalAmount.multiply(paymentType.divide(new BigDecimal(
							100), 2, BigDecimal.ROUND_HALF_UP)).setScale(2,
							BigDecimal.ROUND_HALF_UP)
					+ "元，而可用款项金额为："
					+ itemTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP)
					+ "元，【款项金额不足，还差"
					+ finalAmount.subtract(itemTotalAmount.multiply(paymentType.divide(new BigDecimal(
							100)))
							.setScale(2, BigDecimal.ROUND_HALF_UP))
					+ "元】");
		}

		orderFullLinkService.addFullLink(4,
				orderSns,
				shipping.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "发货通知单", })
						+ shipping.getSn(),
				null);
	}

	@Override
	@Transactional
	public void update(Shipping shipping, Warehouse warehouse,
			DeliveryCorp delivery, Area area, Long sbuId, Integer flag) {
		
		if (flag == 0 && warehouse == null) {
			// 请选择仓库
			ExceptionUtil.throwServiceException("15141");
		}
		//系统单据类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("remark", CommonVariable.NOTICE_TYPE));
		SystemDict systemType = systemDictBaseService.systemBillType(filters);
		Shipping pshipping = find(shipping.getId());
		//校验发货通知单状态
		this.checkShippingState(pshipping,"保存");
		String sql = "select i.orders from xx_shipping_item i where i.shipping in (" + shipping.getId().toString() + ")";
		List<Map<String, Object>> items = getDaoCenter().getNativeDao().findListMap(sql, null, 0);
		String orderIds = "";
		for (Map<String, Object> item : items) {
			String oid = item.get("orders") == null ? null : item.get("orders").toString();
			if (oid != null && !orderIds.contains(oid)) {
				orderIds += "," + oid;
			}
		}
		orderIds = orderIds.substring(1);
		// 锁订单
		lockDataDao.lockOrder(orderIds);
		BigDecimal tVolume = BigDecimal.ZERO;
		BigDecimal tWeight = BigDecimal.ZERO;
		BigDecimal amount = BigDecimal.ZERO;
		//订单Id
		Long[] orderIds1 = new Long[]{0L};
		List<ShippingItem> shippingItems = new ArrayList<ShippingItem>();
		for (ShippingItem shippingItem : shipping.getShippingItems()) {
			if(ConvertUtil.isEmpty(shippingItem)){
				continue;
			}
			ShippingItem pShippingItem = shippingItemService.find(shippingItem.getId());
			//校验订单状态
			this.checkOrderStatus(pShippingItem.getOrder(),orderIds1);
			OrderItem orderItem = pShippingItem.getOrderItem();
			// 修改订单计划发货数量
			BigDecimal changeQuantity = shippingItem.getQuantity().subtract(pShippingItem.getQuantity());
			if (changeQuantity.compareTo(BigDecimal.ZERO) != 0) {
				if (orderItem != null) {
					orderItem.setShipPlanQuantity(orderItem.getShipPlanQuantity().add(changeQuantity));
					orderItemService.update(orderItem);
				}
			}
			//色号
			if(!ConvertUtil.isEmpty(shippingItem.getColourNumbers()) && !ConvertUtil.isEmpty(shippingItem.getColourNumbers().getId())){
				pShippingItem.setColourNumbers(shippingItem.getColourNumbers());
			}else{
				pShippingItem.setColourNumbers(null);
			}
			//含水率
			if(!ConvertUtil.isEmpty(shippingItem.getMoistureContents()) && !ConvertUtil.isEmpty(shippingItem.getMoistureContents().getId())){
				pShippingItem.setMoistureContents(shippingItem.getMoistureContents());
			}else{
				pShippingItem.setMoistureContents(null);
			}
			BigDecimal quantity = shippingItem.getQuantity();
			BigDecimal pVolume = pShippingItem.getVolume() == null ? BigDecimal.ZERO : pShippingItem.getVolume();
			BigDecimal pWeight = pShippingItem.getWeight() == null ? BigDecimal.ZERO : pShippingItem.getWeight();
			pShippingItem.setWarehouse(null);
			pShippingItem.setPrice(orderItem.getPrice());
			pShippingItem.setQuantity(quantity);
			pShippingItem.setVolume(pVolume);
			pShippingItem.setWeight(pWeight);
			pShippingItem.setMemo(shippingItem.getMemo());
			pShippingItem.setBatch(shippingItem.getBatch());
			pShippingItem.setBatchEncoding(shippingItem.getBatchEncoding());
			pShippingItem.setColourNumber(shippingItem.getColourNumber());
			pShippingItem.setMoistureContent(shippingItem.getMoistureContent());
			amount = amount.add(pShippingItem.getQuantity().multiply(pShippingItem.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
			// 行合计体积
			BigDecimal volume = pVolume.multiply(quantity);
			// 总体积
			tVolume = tVolume.add(volume);
			// 行合计重量
			BigDecimal weight = pWeight.multiply(quantity);
			// 总重量
			tWeight = tWeight.add(weight);
			//批次
			List<WarehouseBillBatch> warehouseBillBatchList = new ArrayList<WarehouseBillBatch>();
			if(!ConvertUtil.isEmpty(pShippingItem.getBatch())){
				String[] batchs = pShippingItem.getBatch().split(";");
				for (int i = 0; i < batchs.length; i++) {
					if(!ConvertUtil.isEmpty(batchs[i])){
						WarehouseBillBatch warehouseBillBatch = new WarehouseBillBatch();
						//单据类型
						warehouseBillBatch.setSysType(systemType);
						//发货单
						warehouseBillBatch.setShipping(shipping);
						//发货单明细
						warehouseBillBatch.setShippingItem(pShippingItem);
						//批次
						warehouseBillBatch.setWarehouseBatch(warehouseBatchService.find(Long.parseLong(batchs[i])));
						warehouseBillBatchList.add(warehouseBillBatch);
					}
				}
			}
			pShippingItem.getWarehouseBillBatchs().clear();
			pShippingItem.getWarehouseBillBatchs().addAll(warehouseBillBatchList);
			shippingItems.add(pShippingItem);
		}
		shipping.getShippingItems().clear();
		shipping.getShippingItems().addAll(shippingItems);
		shipping.setWarehouse(warehouse);
		shipping.setDelivery(delivery);
		shipping.setArea(area);
		shipping.setDelivery(delivery);
		shipping.setAmount(amount);
		shipping.setVolume(tVolume);
		shipping.setWeight(tWeight);
		shipping.setShippingTime(pshipping.getShippingTime());
		//sbu
		Sbu sbu = sbuService.find(sbuId);
		shipping.setSbu(sbu);
		//发运方式
		shipping.setSmethod(shipping.getSmethod());
		// 附件
		List<ShippingAttach> shippingAttachs = shipping.getShippingAttachs();
		for (Iterator<ShippingAttach> iterator = shippingAttachs.iterator(); iterator.hasNext();) {
			ShippingAttach shippingAttach = iterator.next();
			if (shippingAttach == null || shippingAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (shippingAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			shippingAttach.setFileName(shippingAttach.getName() + "." + shippingAttach.getSuffix());
			shippingAttach.setShipping(pshipping);
			shippingAttach.setStoreMember(storeMemberBaseService.getCurrent());
		}
		update(shipping,"objTypeId","wfState","wfId","wfTempId","status","outTradeNo",
				"orderType","store","storeMember","acceptStatus","saleOrg","organization");
	}

	@Override
	@Transactional
	public void check(Long[] shippingId) throws Exception {

		String ids = "";
		for (int i = 0; i < shippingId.length; i++) {
			if (i == shippingId.length - 1) {
				ids += shippingId[i];
			}
			else {
				ids += shippingId[i] + ",";
			}
		}
		String sql = "update xx_shipping s set status=1,accept_status=1, check_store_member="
				+ storeMemberBaseService.getCurrent().getId()
				+ " where s.status=0 and s.id in ("
				+ ids
				+ ")";
		int count = getDaoCenter().getNativeDao().update(sql, null);
		if (count < shippingId.length) {
			// 只有未审核状态的发货单才能进行审核操作
			ExceptionUtil.throwServiceException("15160");
		}
		else {
			Setting setting = SettingUtils.get();
			this.createPdf(setting, shippingId);
			// this.createPdf(shippingId);
			for (Long id : shippingId) {
				Shipping shipping = find(id);
				List<String> orderSns = new ArrayList<String>();
				for (ShippingItem shippingItem : shipping.getShippingItems()) {
					String osn = shippingItem.getOrder() == null ? null
							: shippingItem.getOrder().getSn();
					if (osn != null && !orderSns.contains(osn)) {
						orderSns.add(osn);
					}
				}
				String deliveryName = "";
				if (shipping.getDelivery() != null
						&& shipping.getDelivery().getName() != null) {
					deliveryName = shipping.getDelivery().getName();
				}
				// 发货审核写接口
				//this.saveIntfAtCheck(shipping);

				orderFullLinkService.addFullLink(4,
						orderSns,
						shipping.getSn(),
						"审核发货通知单"
								+ shipping.getSn()
								+ ",配送方式【"
								+ shipping.getShippingMethod()
								+ "】，物流快递【"
								+ deliveryName
								+ "】,运单号【"
								+ (shipping.getTrackingNo() == null ? ""
										: shipping.getTrackingNo()) + "】",
						null);
			}
		}
	}

	@Override
	@Transactional
	public void checkWf(Long[] shippingId, Long objConfId) throws Exception {

		Setting setting = SettingUtils.get();
		this.createPdf(setting, shippingId);
		// this.createPdf(shippingId);
		for (Long id : shippingId) {
			Shipping shipping = find(id);
			if (shipping.getWfId() != null) {
				ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
			}
			List<String> orderSns = new ArrayList<String>();
			for (ShippingItem shippingItem : shipping.getShippingItems()) {
				String osn = shippingItem.getOrder() == null ? null
						: shippingItem.getOrder().getSn();
				if (osn != null && !orderSns.contains(osn)) {
					orderSns.add(osn);
				}
			}
			/*
			 * String deliveryName = ""; if (shipping.getDelivery() != null &&
			 * shipping.getDelivery().getName() != null) { deliveryName =
			 * shipping.getDelivery().getName(); }
			 * orderFullLinkService.addFullLink(4, orderSns, shipping.getSn(),
			 * "审核发货通知单" + shipping.getSn() + ",配送方式【" +
			 * shipping.getShippingMethod() + "】，物流快递【" + deliveryName +
			 * "】,运单号【" + (shipping.getTrackingNo() == null ? "" :
			 * shipping.getTrackingNo()) + "】", null);
			 */

			// 创建流程实例
			WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
			if (wfObjConfigLine == null) {
				ExceptionUtil.throwServiceException("请选择审核流程");
			}
			StoreMember storeMember = storeMemberService.getCurrent();
			shipping.setCheckStoreMember(storeMember);
			shipping.setByObjConfig(wfObjConfigLine); // 设置流程配置
			update(shipping);
			wfBaseService.createwf(storeMemberService.getCurrent(),
					shipping.getSaleOrg() == null ? null
							: shipping.getSaleOrg().getId(),
					shipping.getSn(),
					shipping.getWfTempId(),
					shipping.getObjTypeId(),
					shipping.getId());

			WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());
			List<String> shippingSns = new ArrayList<String>();
			orderSns.add(shipping.getSn());
			orderFullLinkService.addFullLink(4,
					shippingSns,
					shipping.getSn(),
					ConvertUtil.convertI18nMsg("18701",
							new Object[] { wfTemp.getWfTempName() }),
					null);

		}

	}
	
	
	@Override
	@Transactional
	public void checkShipping(Long shippingId) throws Exception {
		
	    
		Setting setting = SettingUtils.get();
		this.createPdf(setting, shippingId);
		Shipping shipping = find(shippingId);
		//校验发货通知单状态
		this.checkShippingState(shipping,"审核");
		//订单Id
		Long[] orderIds = new Long[]{0L};
		List<String> orderSns = new ArrayList<String>();
		for (ShippingItem shippingItem : shipping.getShippingItems()) {
			//校验订单状态
			this.checkOrderStatus(shippingItem.getOrder(),orderIds);
			String osn = "";
			if(!ConvertUtil.isEmpty(shippingItem.getOrder())){
				osn = shippingItem.getOrder().getSn();
			}
			if (!ConvertUtil.isEmpty(osn) && !orderSns.contains(osn)) {
				orderSns.add(osn);
			}
		}
		StoreMember storeMember = storeMemberService.getCurrent();
		shipping.setCheckStoreMember(storeMember);
		Store store = shipping.getStore();
		lockDataDao.lockStore(store.getId().toString());
		shipping.setStatus(1);
		shipping.setAcceptStatus(1);
		shipping.setCheckDate(new Date());
		
		// 发货审核写接口
		//this.saveIntfAtCheck(shipping);

		update(shipping);
		//价差报表数据
		savepriceDifference(shipping);

//		this.saveIntfAtLogistics(shipping);
	}

	public void saveIntfAtCheck(Shipping shipping) {
		Long companyInfoId = shipping.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		if ("dzrjj".equals(companyInfo.getCompany_code())) {//nadev:dzrjj na/natest:nature
			// 大自然发货接口
			if(shipping.getWarehouse().getIsSendJiaWaYun()) {
				System.out.println("=进入JWY发货接口报文编写=");
				intfOrderMessageToService.saveLogisticsIntf(shipping,0);
			}
		}
	}
	
	
	public void saveIntfAtLogistics(Shipping shipping) {
		// 大自然物流接口
		if(shipping.getWarehouse().getIsSendJiaWaYun()) {
		    intfOrderMessageToService.saveLogisticsIntf(shipping,0);
		}
	}

	public void saveIntfAtOut(Shipping shipping) {
//		Long companyInfoId = shipping.getCompanyInfoId();
//		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
//		if ("TCLNT".equals(companyInfo.getCompany_code())) {
//			// 暖通出库接口
////			intfOrderMessageToService.saveShippingCustomIntf(shipping);
//		}
	}

	public void cancelIntf(Shipping shipping) throws Exception {
		Long companyInfoId = shipping.getCompanyInfoId();
		String msg = "";
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
//		if ("dzrjj".equals(companyInfo.getCompany_code())) {
//			// 家哇云发货取消接口
//			HashMap<String, Object> params = new HashMap<String, Object>();
//			params.put("type", "AM发货单");// 订单类型
//			params.put("salesNo", shipping.getSn());
//			params.put("shippingId", shipping.getId());
//			params.put("cancelDate", DateUtil.convert(new Date()));
//			params.put("status", 2);
//			params.put("operator", storeMemberBaseService.getCurrent().getUsername());
//			msg = new CancelRealTimePush().cancelBill(params);
//			if (!msg.equals("S")) {
//				ExceptionUtil.throwServiceException("操作失败!返回信息：" + msg);
//			}
//
//		}
	}

	public void cancelItemIntf(Shipping shipping, Long shippingItemId)
			throws Exception {
		Long companyInfoId = shipping.getCompanyInfoId();
		String msg = "";
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		if ("nature".equals(companyInfo.getCompany_code())) {
			// 大自然发货取消接口
			HashMap<String, Object> params = new HashMap<String, Object>();
			params.put("SOURCE_CODE", "CRM");
			params.put("SOURCE_NUM", shipping.getId());
			params.put("SOURCE_LINE_NUM", shippingItemId);
			params.put("CANCEL_DATE", DateUtil.convert(new Date()));
			params.put("CANCEL_REASON", "");
			params.put("ERP_USER_NAME",storeMemberBaseService.getCurrent().getUsername());
			msg = new NatureOrderItemCancel().cancelOrderItem(params);
			if (!msg.equals("S")) {
				ExceptionUtil.throwServiceException("操作失败!ERP返回信息：" + msg);
			}
			else {
				//重新生成pdf
				Setting setting = SettingUtils.get();
				this.createPdf(setting, shipping.getId());
			}
		}
	}

	@Override
	@Transactional
	public void reject(Long[] shippingId) throws Exception {

		String ids = "";
		for (int i = 0; i < shippingId.length; i++) {
			if (i == shippingId.length - 1) {
				ids += shippingId[i];
			}
			else {
				ids += shippingId[i] + ",";
			}
		}
		String sql = "update xx_shipping s set status=0,accept_status=0, check_store_member = null"
				+ " where s.status=1 and s.id in ("
				+ ids
				+ ")";
		int count = getDaoCenter().getNativeDao().update(sql, null);
		if (count < shippingId.length) {
			// 只有已审核状态的发货单才能进行审核操作
			ExceptionUtil.throwServiceException("15160");
		}
		else {
			for (Long id : shippingId) {
				// List<Filter> fis = new ArrayList<Filter>();
				// fis.add(Filter.eq("objId", shippingId));
				// fis.add(Filter.eq("type", 0));
				// TriplicateForm triplicateForm =
				// triplicateFormService.find(fis);
				// triplicateFormService.delete(triplicateForm);
				Shipping shipping = find(id);
				List<String> orderSns = new ArrayList<String>();
				for (ShippingItem shippingItem : shipping.getShippingItems()) {
					String osn = shippingItem.getOrder() == null ? null
							: shippingItem.getOrder().getSn();
					if (osn != null && !orderSns.contains(osn)) {
						orderSns.add(osn);
					}
				}
				orderFullLinkService.addFullLink(4,
						orderSns,
						shipping.getSn(),
						ConvertUtil.convertI18nMsg("18707") + shipping.getSn(),
						null);
			}
		}
	}

	@Override
	@Transactional
	public void cancel(Long[] shippingId) throws Exception {

//		for (int i = 0; i < shippingId.length; i++) {
//			Shipping shipping = find(shippingId[i]);
//			//校验发货通知单状态
//			this.checkShippingState(shipping, "作废");
//		}
		
		String ids = "";
		List<String> shippingList = new ArrayList<String>();
		Shipping s = null;
		for (int i = 0; i < shippingId.length; i++) {
			if (i == shippingId.length - 1) {
				ids += shippingId[i];
			}
			else {
				ids += shippingId[i] + ",";
			}
			if (shippingId[i] != null) {
				s = find(shippingId[i]);
				if (s != null && s.getStatus() == 1) {
					shippingList.add(s.getId().toString());
				}
			}
		}
		String sql = "select i.orders,i.order_item,i.quantity from xx_shipping_item i where i.shipping in ("
				+ ids
				+ ")";
		List<Map<String, Object>> items = getDaoCenter().getNativeDao()
				.findListMap(sql, null, 0);
		String orderIds = "";
		for (Map<String, Object> item : items) {
			String oid = item.get("orders") == null ? null : item.get("orders")
					.toString();
			if (oid != null && !orderIds.contains(oid)) {
				orderIds += "," + oid;
			}
		}
		orderIds = orderIds.substring(1);
		// 锁订单
		lockDataDao.lockOrder(orderIds);
		sql = "update xx_shipping set status=2,del_store_member="
				+ storeMemberBaseService.getCurrent().getId()
				+ " where status in (0,1)  and id in ("
				+ ids
				+ ")";
		int count = getDaoCenter().getNativeDao().update(sql, null);
		
	
		lockDataDao.lockOrder(orderIds);
	
		if (count < shippingId.length) {
			ExceptionUtil.throwServiceException("只有审核与未审核状态的发货单才能进行作废操作");
		}
		// 还回订单明细计划发货数量
		for (Map<String, Object> item : items) {
			String order_item_id = item.get("order_item") == null ? null
					: item.get("order_item").toString();
			if (order_item_id != null) {
				BigDecimal quantity = new BigDecimal(item.get("quantity")
						.toString());
				sql = "update xx_order_item set ship_plan_quantity = ship_plan_quantity - ? where id = ?";
				getDaoCenter().getNativeDao().update(sql,
						new Object[] { quantity, order_item_id });
				
	
			}
		}

		for (Long id : shippingId) {
			Shipping shipping = find(id);
			List<String> orderSns = new ArrayList<String>();
			for (ShippingItem shippingItem : shipping.getShippingItems()) {
				String osn = shippingItem.getOrder() == null ? null
						: shippingItem.getOrder().getSn();
				if (osn != null && !orderSns.contains(osn)) {
					orderSns.add(osn);
				}
				shippingItem.setStatus(2);
				shippingItem.setDelStoreMember(storeMemberBaseService.getCurrent());
				shippingItemService.update(shippingItem);
			}
			orderFullLinkService.addFullLink(4,
					orderSns,
					shipping.getSn(),
					ConvertUtil.convertI18nMsg("18706"),
					null);
			
			 if (shippingList.contains(id.toString())) {// 只有已审核的单才会推到家哇云
                 // 接口
                 if(shipping.getWarehouse().getIsSendJiaWaYun()){
					 this.cancelIntf(shipping);
                 }         
             }
		}
	}

	@Override
	@Transactional
	public void cancelItem(Long[] shippingId, Long shippingItemId)
			throws Exception {

	    
		String ids = "";
		List<String> shippingList = new ArrayList<String>();
		Shipping s = null;
		for (int i = 0; i < shippingId.length; i++) {
			if (i == shippingId.length - 1) {
				ids += shippingId[i];
			}
			else {
				ids += shippingId[i] + ",";
			}
			if (shippingId[i] != null) {
				s = find(shippingId[i]);
				if (s != null && (s.getStatus() == 1 || s.getStatus()==3)) {
					shippingList.add(s.getId().toString());
				}
			}
		}
		String sql = "select i.orders,i.order_item,i.quantity from xx_shipping_item i where i.shipping in ("
				+ ids
				+ ")";
		List<Map<String, Object>> items = getDaoCenter().getNativeDao()
				.findListMap(sql, null, 0);
		String orderIds = "";
		for (Map<String, Object> item : items) {
			String oid = item.get("orders") == null ? null : item.get("orders")
					.toString();
			if (oid != null && !orderIds.contains(oid)) {
				orderIds += "," + oid;
			}
		}
		orderIds = orderIds.substring(1);
		
		//校验是否有出库
		 sql = "SELECT distinct ams.sn  FROM xx_am_shipping ams JOIN xx_am_shipping_item amsi ON amsi.am_shipping = ams.id "
		         + "where ams.company_info_id=9 and ams.status<>2 and amsi.shipping_item in("
                + shippingItemId
                + ")";
        items = getDaoCenter().getNativeDao()
                .findListMap(sql, null, 0);
		if(items!=null&&items.size()>0) {
		    ExceptionUtil.throwServiceException("已经生成出库单：【"+items.get(0).get("sn")+"】,发货单无法进行关闭行"); 
		}
		// 锁订单
		lockDataDao.lockOrder(orderIds);
		ShippingItem shippingItem = shippingItemService.find(shippingItemId);
		if (shippingItem.getStatus() != null && shippingItem.getStatus() == 2) {
			ExceptionUtil.throwServiceException("该发货行已关闭，请勿重复操作");
		}
		Shipping ship = shippingItem.getShipping();
		if (ship.getStatus() != 1 && ship.getStatus() != 0 && ship.getStatus() != 3) {
			ExceptionUtil.throwServiceException("当前状态下的发货单无法进行关闭行");
		}
		shippingItem.setStatus(2);
		shippingItem.setDelStoreMember(storeMemberBaseService.getCurrent());
		shippingItemService.update(shippingItem);

		sql = "update xx_order_item set ship_plan_quantity = ship_plan_quantity - ? where id = ?";
		getDaoCenter().getNativeDao().update(sql,
				new Object[] { shippingItem.getQuantity(),
						shippingItem.getOrderItem().getId() });


		for (Long id : shippingId) {
			Shipping shipping = find(id);
			List<String> orderSns = new ArrayList<String>();
			String osn = shippingItem.getOrder() == null ? null
					: shippingItem.getOrder().getSn();
			if (osn != null && !orderSns.contains(osn)) {
				orderSns.add(osn);
			}
			orderFullLinkService.addFullLink(4,
					orderSns,
					shipping.getSn(),
					"作废发货单行",
					null);
		    if (shippingList.contains(id.toString())) {// 只有已审核的单才会推到erp
                // 接口
                this.cancelItemIntf(shipping, shippingItemId);
                if(shipping.getWarehouse().getIsSendJiaWaYun()){
//                    intfOrderMessageToService.saveLogisticsIntf(shipping,1);
                }
            } 
		}
	}

	@Override
	@Transactional
	public void out(String orderIds, Long[] itemIds, BigDecimal[] quantitys,
			Boolean isIgnoreStock) throws Exception {
		// 锁订单
		lockDataDao.lockOrder(orderIds);

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StockTaskBuilder taskBuilder = new StockTaskBuilder();
		// List<ShippingItem> shippingItems = ship.getShippingItems();
		List<Filter> filters = new ArrayList<Filter>();
		for (int i = 0; i < itemIds.length; i++) {
			Long itemId = itemIds[i];
			ShippingItem shippingItem = shippingItemService.find(itemId);
			BigDecimal quantity = quantitys[i];
			Order order = shippingItem.getOrder();

			if (quantity.compareTo(BigDecimal.ZERO) == -1) {
				// 出仓数量不能小于0
				ExceptionUtil.throwServiceException("15158");
			}
			if (quantity.compareTo(BigDecimal.ZERO) == 0) continue;
			Shipping shipping = shippingItem.getShipping();
			if (shipping.getStatus() != 1 && shipping.getStatus() != 3) {
				// 该状态不允许出仓
				ExceptionUtil.throwServiceException("15166");
			}
			OrderItem orderItem = shippingItem.getOrderItem();

			if (order != null && shippingItem.getBomFlag() != 2) {
				// 处理订单
				BigDecimal orderQuantity = order.getQuantity();
				BigDecimal orderShippedQuantity = order.getShippedQuantity()
						.add(quantity);
				if (orderQuantity.compareTo(orderShippedQuantity) == 1) {
					// if (orderQuantity > orderShippedQuantity) {
					order.setShippingStatus(ShippingStatus.partialShipment);
				}
				else if (orderQuantity.compareTo(orderShippedQuantity) == 0) {
					order.setShippingStatus(ShippingStatus.shipped);
				}
				else {
					// 出仓数量不能大于订单数量
					ExceptionUtil.throwServiceException("15157");
				}
				orderService.update(order);
			}

			if (orderItem != null) {
				// 处理订单明细
				BigDecimal orderItemQuantity = orderItem.getQuantity();
				BigDecimal orderItemShippedQuantity = orderItem.getShippedQuantity()
						.add(quantity);
				if (orderItemQuantity.compareTo(orderItemShippedQuantity) == -1) {
					// if (orderItemQuantity < orderItemShippedQuantity) {
					// 出仓数量不能大于订单数量
					ExceptionUtil.throwServiceException("15157");
				}
				orderItem.setShippedQuantity(orderItemShippedQuantity);
				orderItem.setStock(null);
				orderItemService.update(orderItem);
			}

			// 处理发货单
			BigDecimal shippingQuantity = shipping.getQuantity();
			BigDecimal shippingedQuantity = shipping.getShippingedQuantity()
					.add(quantity);
			if (shippingQuantity.compareTo(shippingedQuantity) == -1) {
				// if (shippingQuantity < shippingedQuantity) {
				// 实际发货数量不能大于计划发货数量
				ExceptionUtil.throwServiceException("15159");
			}
			else if (shippingQuantity.compareTo(shippingedQuantity) == 0) {
				shipping.setStatus(4);
			}
			else {
				shipping.setStatus(3);
			}
			update(shipping);

			// 处理发货单明细
			BigDecimal shippingItemQuantity = shippingItem.getQuantity();
			BigDecimal shippingItemedQuantity = shippingItem.getShippedQuantity()
					.add(quantity);
			if (shippingItemQuantity.compareTo(shippingItemedQuantity) == -1) {
				// if (shippingItemQuantity < shippingItemedQuantity) {
				// 实际发货数量不能大于计划发货数量
				ExceptionUtil.throwServiceException("15159");
			}
			shippingItem.setShippedQuantity(shippingItemedQuantity);
			shippingItem.setShippedTime(new Date());
			shippingItemService.update(shippingItem);

			// 内外机有出库的就把整机改为完全出库
			if (shippingItem.getBomFlag() == 2) {
				filters.clear();
				filters.add(Filter.eq("lineNo", shippingItem.getLineNo()));
				filters.add(Filter.eq("bomFlag", 1));
				filters.add(Filter.eq("order", order));
				filters.add(Filter.eq("shipping", shippingItem.getShipping()
						.getId()));
				ShippingItem item = shippingItemService.find(filters);
				if (item != null
						&& item.getQuantity()
								.compareTo(item.getShippedQuantity()) == 1) {
					item.setShippedQuantity(item.getQuantity());
					shippingItemService.update(item);

					// 处理订单
					BigDecimal orderQuantity = order.getQuantity();
					BigDecimal orderShippedQuantity = order.getShippedQuantity()
							.add(quantity);
					if (orderQuantity.compareTo(orderShippedQuantity) == 1) {
						order.setShippingStatus(ShippingStatus.partialShipment);
					}
					else if (orderQuantity.compareTo(orderShippedQuantity) == 0) {
						order.setShippingStatus(ShippingStatus.shipped);
					}
					orderService.update(order);
					//处理整机发货数量
					OrderItem oItem = item.getOrderItem();
					oItem.setShippedQuantity(oItem.getShippedQuantity()
							.add(item.getQuantity()));
					orderItemService.update(oItem);
				}
			}

			Product product = shippingItem.getProduct();
			String shippingSn = shipping.getSn();
			if (shippingItem.getBomFlag() != 1) {
				// 处理库存
				if (product != null) {
					Long productId = product.getId();
					Warehouse warehouse = shippingItem.getWarehouse();
					if (warehouse == null) {
						ExceptionUtil.throwServiceException("仓库不能为空");
					}
					Long warehouseId = warehouse.getId();

					if (isIgnoreStock == null || isIgnoreStock == false) {
						taskBuilder.delStock(productId,
								warehouseId,
								quantity,
								"发货单[" + shippingSn + "出库发货");
					}

					// 创建出库单
					StockOut stockOut = stockOutService.createByShippingItem(shipping.getSaleOrg(),
							warehouse,
							product,
							quantity,
							shippingSn,
							shippingItem,
							"出库发货时生成，发货单号：" + shippingSn);

//					CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
//					if ("gdkb".equals(companyInfo.getCompany_code())) {
//						this.saveIntfOrderKb(itemId, quantity, stockOut);
//					}
				}

			}
			String productName = product == null ? shippingItem.getName()
					: product.getName();
			// 订单全链路
			List<String> orderSns = new ArrayList<String>();
			if (order != null) {
				orderSns.add(order.getSn());
			}
			orderFullLinkService.addFullLink(4, orderSns, shippingSn, "发货单["
					+ shippingSn
					+ "]，产品["
					+ productName
					+ "]出库发货", null);
			saveIntfAtOut(shipping);
		}
		// 处理库存
		if (isIgnoreStock == null || isIgnoreStock == false) {
			stockService.handleStock(taskBuilder);
		}

	}

	public void out(List<Map<String, Object>> stockOutBarcodes) {

		String productIds = "";
		String shippingSns = "";
		for (Map<String, Object> sobc : stockOutBarcodes) {
			Long productId = Long.parseLong(sobc.get("productid").toString());
			String shippingSn = sobc.get("shippingsn").toString();
			productIds += "," + productId;
			shippingSns = ",'" + shippingSn + "'";
		}
		productIds = productIds.substring(1);
		shippingSns = shippingSns.substring(1);
		String sql = "select distinct i.orders from xx_shipping_item i,xx_shipping s"
				+ " where i.shipping = s.id and i.product in ("
				+ productIds
				+ ") and s.sn in ("
				+ shippingSns
				+ ")";
		List<Map<String, Object>> orderList = shippingDao.getNativeDao()
				.findListMap(sql, null, 0);
		String orderIds = "";
		for (Map<String, Object> orderMap : orderList) {
			Long orderId = Long.parseLong(orderMap.get("orders").toString());
			orderIds += "," + orderId;
		}
		orderIds = orderIds.substring(1);
		// 锁订单
		lockDataDao.lockOrder(orderIds);

		StockTaskBuilder taskBuilder = new StockTaskBuilder();
		for (Map<String, Object> sobc : stockOutBarcodes) {
			Long productId = Long.parseLong(sobc.get("productid").toString());
			String barcode = sobc.get("barcode").toString();
			sql = "select count(1) from xx_stock_out_barcode where product = ? and barcode = ?";
			int count = shippingDao.getNativeDao().findInt(sql,
					new Object[] { productId, barcode });
			if (count > 0) {
				sobc.put("success", 1);
				sobc.put("msg", "条形码已使用");
				continue;
			}
			String shippingSn = sobc.get("shippingsn").toString();
			sql = "select i.id from xx_shipping_item i,xx_shipping s"
					+ " where i.shipping = s.id and i.product = ? and s.sn = ?";
			Map<String, Object> item = shippingDao.getNativeDao()
					.findSingleMap(sql, new Object[] { productId, shippingSn });
			ShippingItem shippingItem = shippingItemService.find(Long.parseLong(item.get("id")
					.toString()));
			BigDecimal quantity = new BigDecimal(1);
			Order order = shippingItem.getOrder();
			if (quantity.compareTo(BigDecimal.ZERO) == -1) {
				// 出仓数量不能小于0
				ExceptionUtil.throwServiceException("15158");
			}
			if (quantity.compareTo(BigDecimal.ZERO) == 0) continue;
			Shipping shipping = shippingItem.getShipping();
			if (shipping.getStatus() != 1 && shipping.getStatus() != 3) {
				// 该状态不允许出仓
				ExceptionUtil.throwServiceException("15166");
			}
			OrderItem orderItem = shippingItem.getOrderItem();
			if (order != null && shippingItem.getBomFlag() != 2) {
				// 处理订单
				BigDecimal orderQuantity = order.getQuantity();
				BigDecimal orderShippedQuantity = order.getShippedQuantity()
						.add(quantity);
				if (orderQuantity.compareTo(orderShippedQuantity) == 1) {
					// if (orderQuantity > orderShippedQuantity) {
					order.setShippingStatus(ShippingStatus.partialShipment);
				}
				else if (orderQuantity.compareTo(orderShippedQuantity) == 0) {
					order.setShippingStatus(ShippingStatus.shipped);
				}
				else {
					// 出仓数量不能大于订单数量
					ExceptionUtil.throwServiceException("15157");
				}
				orderService.update(order);
			}
			if (orderItem != null) {
				// 处理订单明细
				BigDecimal orderItemQuantity = orderItem.getQuantity();
				BigDecimal orderItemShippedQuantity = orderItem.getShippedQuantity()
						.add(quantity);
				if (orderItemQuantity.compareTo(orderItemShippedQuantity) == -1) {
					// if (orderItemQuantity < orderItemShippedQuantity) {
					// 出仓数量不能大于订单数量
					ExceptionUtil.throwServiceException("15157");
				}
				orderItem.setShippedQuantity(orderItemShippedQuantity);
				orderItem.setStock(null);
				orderItemService.update(orderItem);
			}

			// 处理发货单
			BigDecimal shippingQuantity = shipping.getQuantity();
			BigDecimal shippingedQuantity = shipping.getShippingedQuantity()
					.add(quantity);
			if (shippingQuantity.compareTo(shippingedQuantity) == -1) {
				// 实际发货数量不能大于计划发货数量
				ExceptionUtil.throwServiceException("15159");
			}
			else if (shippingQuantity.compareTo(shippingedQuantity) == 0) {
				shipping.setStatus(4);
			}
			else {
				shipping.setStatus(3);
			}
			update(shipping);

			// 处理发货单明细
			BigDecimal shippingItemQuantity = shippingItem.getQuantity();
			BigDecimal shippingItemedQuantity = shippingItem.getShippedQuantity()
					.add(quantity);
			if (shippingItemQuantity.compareTo(shippingItemedQuantity) == -1) {
				// 实际发货数量不能大于计划发货数量
				ExceptionUtil.throwServiceException("15159");
			}
			shippingItem.setShippedQuantity(shippingItemedQuantity);
			shippingItem.setShippedTime(new Date());
			shippingItemService.update(shippingItem);

			Product product = shippingItem.getProduct();
			StockOut stockOut = null;
			Warehouse warehouse = null;
			if (shippingItem.getBomFlag() != 1) {
				// 处理库存
				if (product != null) {
					warehouse = shippingItem.getWarehouse();
					Long warehouseId = warehouse.getId();
					taskBuilder.delStock(productId,
							warehouseId,
							quantity,
							"发货单[" + shippingSn + "扫码出库发货");
					// }

					// 创建出库单
					stockOut = stockOutService.createByShippingItem(shipping.getSaleOrg(),
							warehouse,
							product,
							quantity,
							shippingSn,
							shippingItem,
							"扫码出库发货时生成，发货单号：" + shippingSn);
				}
			}
			String productName = product == null ? shippingItem.getName()
					: product.getName();
			// 订单全链路
			List<String> orderSns = new ArrayList<String>();
			if (order != null) {
				orderSns.add(order.getSn());
			}
			orderFullLinkService.addFullLink(4, orderSns, shippingSn, "发货单["
					+ shippingSn
					+ "]，产品["
					+ productName
					+ "]扫码出库发货", null);
			sobc.put("success", 2);

			/*
			 * StockOutBarcode stockOutBarcode = new StockOutBarcode();
			 * stockOutBarcode.setBarcode(barcode);
			 * stockOutBarcode.setProduct(product);
			 * stockOutBarcode.setVonderCode(product.getVonderCode());
			 * stockOutBarcode.setWarehouseSn(warehouse.getSn());
			 * stockOutBarcode.setShippingSn(shippingSn);
			 * stockOutBarcode.setStockOutSn(stockOut.getSn());
			 * stockOutBarcode.setCompanyInfoId(warehouse.getCompanyInfoId());
			 * stockOutBarcodeService.save(stockOutBarcode);
			 */
		}
		// 处理库存
		stockService.handleStock(taskBuilder);
	}

	@Override
	@Transactional
	public void undertaking(Long[] id, Long[] deliveryId, String[] trackingNo,
			int flag) {

		String ids = "";
		for (int i = 0; i < id.length; i++) {
			if (i == id.length - 1) {
				ids += id[i];
			}
			else {
				ids += id[i] + ",";
			}
		}
		String sql = "select i.orders from xx_shipping_item i where i.shipping in ("
				+ ids
				+ ")";
		List<Map<String, Object>> items = getDaoCenter().getNativeDao()
				.findListMap(sql, null, 0);
		String orderIds = "";
		for (Map<String, Object> item : items) {
			String oid = item.get("orders").toString();
			if (!orderIds.contains(oid)) {
				orderIds += "," + oid;
			}
		}
		orderIds = orderIds.substring(1);
		// 锁订单
		lockDataDao.lockOrder(orderIds);

		if (deliveryId == null || deliveryId.length == 0)
			deliveryId = new Long[] { null };
		if (trackingNo == null || trackingNo.length == 0)
			trackingNo = new String[] { null };
		for (int i = 0; i < id.length; i++) {
			if (flag == 1) {
				sql = "update xx_shipping set accept_status=1,delivery="
						+ deliveryId[i]
						+ ",tracking_no='"
						+ trackingNo[i]
						+ "' where id = "
						+ id[i];
			}
			else {
				sql = "update xx_shipping set accept_status=2,status=0,delivery="
						+ deliveryId[i]
						+ ",tracking_no='"
						+ trackingNo[i]
						+ "' where id = "
						+ id[i];
			}
			getDaoCenter().getNativeDao().update(sql, null);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public BigDecimal getShippingItemQuantities(Long orderItemId) {
		return shippingDao.getShippingItemQuantities(orderItemId);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn, String orderSn,
			String outTradeNo, String trackingNo, Integer[] status,
			Integer[] acceptStatus, Long warehouseId, Long deliveryCorpId,
			Integer warehouseType, Long storeId, Long supplierId,
			String firstTime, String lastTime, Boolean isUndertaking,
			Pageable pageable) {
		return shippingDao.findPage(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				acceptStatus,
				warehouseId,
				deliveryCorpId,
				warehouseType,
				storeId,
				supplierId,
				firstTime,
				lastTime,
				isUndertaking,
				pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findListById(String ids) {
		return shippingDao.findListById(ids);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findShippingItemListByShippingId(String shippingIds,
			Long[] organizationIds,Boolean isDefault) {
		return shippingDao.findShippingItemListByShippingId(shippingIds,organizationIds,isDefault);
	}

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> findShippingItemListsByShippingId(String shippingIds,Long[] organizationIds,Boolean isDefault,String[] statusType ) {
        return shippingDao.findShippingItemListsByShippingId(shippingIds,organizationIds,isDefault,statusType);
    }

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findShippingItemListByItemIds(String ids) {
		return shippingDao.findShippingItemListByItemIds(ids);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findOrderItemListByItemIds(String ids) {
		return shippingDao.findOrderItemListByItemIds(ids);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findShippingItemListByOrderId(
			String orderId) {
		return shippingDao.findShippingItemListByOrderId(orderId);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findItemPage(String shippingSn,
			String orderSn, String outTradeNo, String trackingNo,
			Integer[] status, Long warehouseId, Long deliveryCorpId,
			Long productId, Boolean[] isToReceipt, Integer shippeDday,
			Integer[] bomFlag, Integer[] orderType, Long storeId,
			Long supplierId, Pageable pageable) {

		return shippingDao.findItemPage(shippingSn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseId,
				deliveryCorpId,
				productId,
				isToReceipt,
				shippeDday,
				bomFlag,
				orderType,
				storeId,
				supplierId,
				pageable);
	}

	@Override
	@Transactional
	public void receipt(Long[] ids, BigDecimal[] quantity) {

		for (int i = 0; i < ids.length; i++) {
			Long id = ids[i];
			BigDecimal qty = quantity[i];
			List<Map<String, Object>> list = shippingDao.findShippingItemListByItemIds(id.toString());
			Map<String, Object> item = list.get(0);

			BigDecimal shipped_quantity = new BigDecimal(
					item.get("shipped_quantity").toString());
			BigDecimal receipt_quantity = new BigDecimal(
					item.get("receipt_quantity").toString());
			String full_name = item.get("full_name").toString();
			String shipping_sn = item.get("shipping_sn").toString();
			// 判断数量是否可签收
			if (shipped_quantity.compareTo(receipt_quantity.add(qty)) == -1) {
				// if (shipped_quantity < receipt_quantity + qty) {
				// 发货单[{0}]产品[{1}]签收数量太大，请修改后再签收
				ExceptionUtil.throwServiceException("15300",
						shipping_sn,
						full_name);
			}
			// 签收
			shippingDao.receipt(id, qty);

			Long orderId = Long.parseLong(item.get("orders").toString());
			// 全链路
			Order order = orderService.find(orderId);
			List<String> orderSns = new ArrayList<String>();
			if (order != null) {
				orderSns.add(order.getSn());
			}
			orderFullLinkService.addFullLink(4, orderSns, shipping_sn, "发货单["
					+ shipping_sn
					+ "]产品["
					+ full_name
					+ "]确认签收，签收数量["
					+ qty
					+ "]", null);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findListByOrderId(Long id) {
		return shippingDao.findListByOrderId(id);
	}

	@Transactional(readOnly = true)
	public TriplicateForm getTriplicateForm(Long id) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("objId", id));
		filters.add(Filter.eq("type", 0));
		filters.add(Filter.eq("seq", 0));
		List<TriplicateForm> triplicateForms = triplicateFormService.findList(null,
				filters,
				null);
		TriplicateForm triplicateForm = null;
		if (triplicateForms.size() > 0) {
			triplicateForm = triplicateForms.get(0);
		}
		return triplicateForm;

	}

	@Transactional(readOnly = true)
	public String getPdfTemplate(Long companyInfoId,Long sign) {
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		String company_code = companyInfo.getCompany_code();
		String template_path = null;
		if ("dzrjj".equals(company_code) || "nature".equals(company_code)) {
			if(!ConvertUtil.isEmpty(sign) && sign==0){
				template_path = "/pdf/shipping/template/shipping_nature_small.pdf";
			}else{
				template_path = "/pdf/shipping/template/shipping_nature.pdf";
			}
		}
		else if ("gdkb".equals(company_code)) {
			template_path = "/pdf/shipping/template/shipping_kb.pdf";
		}
		else {
			template_path = "/pdf/shipping/template/shipping.pdf";
		}
		return template_path;
	}

	public int getPageSizeForPDF(Long companyInfoId,Long sign) {
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		String company_code = companyInfo.getCompany_code();
		int pageSize = 10;
		if ("dzrjj".equals(company_code) || "nature".equals(company_code)) {
		    if(sign !=null && sign == 0){
                pageSize = 8;
            }
		}else if ("gdkb".equals(company_code)) {
			pageSize = 5;
		}
		return pageSize;
	}

	public TriplicateForm createTriplicateForm(Setting setting,
			Shipping shipping,Long sign) throws Exception {
		String siteUrl = setting.getSiteUrl();
		String template_path = getPdfTemplate(shipping.getCompanyInfoId(),sign);
		
		String file_name = "" + UUID.randomUUID();
		String pdfStaticPath = "/pdf/shipping/file/" + file_name + ".pdf";
		this.buildPdf(shipping, pdfStaticPath, template_path,sign);

		/** 保存中间表 */
		String url = siteUrl + pdfStaticPath;
		TriplicateForm triplicateForm = new TriplicateForm(shipping.getId(), 0,
				url, 0);
		triplicateFormService.save(triplicateForm);
		return triplicateForm;
	}

	public void createPdf(Setting setting, Long... ids) throws Exception {
		List<String> files = new ArrayList<String>();
		try {
			for (Long id : ids) {
				Shipping shipping = find(id);
				TriplicateForm triplicateForm = createTriplicateForm(setting,
						shipping,null);
				files.add(triplicateForm.getUrl());
			}
		}
		catch (Exception e) {
			for (String file : files) {
				this.deleteFile(file);
			}
			throw e;
		}
	}

	private String FONT_PATH = "/usr/share/fonts/self/simsun.ttf";

	public void buildPdf(Shipping shipping, String pdfStaticPath,
			String templateStaticPath,Long sign) throws Exception {

		String PdfTemplatePath = servletContext.getRealPath(templateStaticPath);
		String outputFile = servletContext.getRealPath(pdfStaticPath);
		Store store = shipping.getStore();
		CompanyInfo companyInfo = companyInfoBaseService.find(shipping.getCompanyInfoId());
		String logo = companyInfo.getLogo();
		String company_name = companyInfo.getCompany_name();
		FileOutputStream fos = new FileOutputStream(outputFile);// 需要生成PDF
		List<ShippingItem> shippingItems = shipping.getShippingItems();
		List<ShippingItem> nItems = new ArrayList<ShippingItem>();
		if (companyInfo.getCompany_code().equals("shinco")) {
			for (ShippingItem shippingItem : shippingItems) {
				int bomFlag = shippingItem.getBomFlag();
				if (bomFlag < 2) {
					nItems.add(shippingItem);
				}
			}
		}else {
			for (ShippingItem shippingItem : shippingItems) {
				//过滤已作废的发货单明细行
				if( shippingItem.getStatus()==null||shippingItem.getStatus()!=2){
					int bomFlag = shippingItem.getBomFlag();
					if (bomFlag == 0 || bomFlag == 2) {
						nItems.add(shippingItem);
					}
				}
			}
		}
		int itemSize = nItems.size();// 2
		int pageSize = getPageSizeForPDF(shipping.getCompanyInfoId(),sign);
		int pdfPage = itemSize % pageSize == 0 ? itemSize / pageSize : (itemSize / pageSize) + 1;
		ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pdfPage];// 用于存储每页生成PDF流
		BaseFont bf = BaseFont.createFont(FONT_PATH,BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);
		NumberFormat nf = NumberFormat.getInstance();
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		//订单行是否展示金额  0不展示  非0展示
		int hiddenAmount = 0;
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
		}catch (RuntimeException e) {
			
		}
		for (int i = 0; i < pdfPage; i++) {
			baos[i] = new ByteArrayOutputStream();
			PdfReader reader = new PdfReader(PdfTemplatePath);
			PdfStamper stamp = new PdfStamper(reader, baos[i]);
			ArrayList<BaseFont> fontList = new ArrayList<BaseFont>();
			fontList.add(bf);
			AcroFields form = stamp.getAcroFields();
			int column = 1;// 列数 3 1-3 4
			int forNumber = i * pageSize + pageSize > itemSize ? itemSize : i * pageSize + pageSize;
			form.setSubstitutionFonts(fontList);
			String create_date = DateUtil.convertDateToStr(shipping.getCreateDate(),"yyyy-MM-dd");
			String erp_date = DateUtil.convertDateToStr(shipping.getErpDate(),"yyyy-MM-dd");
			form.setField("company_name", company_name);
			form.setField("create_date", create_date);
			form.setField("erp_date", erp_date);
			form.setField("page", (i + 1) + "");
			form.setField("totalPage", pdfPage + "");
			form.setField("page_info", (i + 1) + " / " + pdfPage);
			form.setField("sn", shipping.getSn());
			form.setField("alias", ConvertUtil.toEmpty(store.getAlias()));
			form.setField("store_name", ConvertUtil.toEmpty(store.getName()));
			form.setField("store_sn",ConvertUtil.toEmpty(store.getOutTradeNo()));
			form.setField("address", ConvertUtil.toEmpty(shipping.getAddress()));
			form.setField("memo", ConvertUtil.toEmpty(shipping.getMemo()));
			form.setField("erpRemark",ConvertUtil.toEmpty(shipping.getErpRemark()));
			if (shipping.getStoreMember() != null) {
				form.setField("store_member_name",ConvertUtil.toEmpty(shipping.getStoreMember().getName()));
			}
			if (shipping.getCheckStoreMember() != null) {
				form.setField("checkStoreMember",ConvertUtil.toEmpty(shipping.getCheckStoreMember().getName()));
			}
			form.setField("consignee",ConvertUtil.toEmpty(shipping.getConsignee()));
			form.setField("phone",ConvertUtil.toEmpty(shipping.getConsignee())+"/"+ConvertUtil.toEmpty(shipping.getPhone()));
			form.setField("shipping_method",ConvertUtil.toEmpty(shipping.getShippingMethod()));
			DriverInfo driverInfo = shipping.getDriverInfo();
			if (driverInfo != null) {
				form.setField("driver_name",ConvertUtil.toEmpty(driverInfo.getName()));
				form.setField("car_number",ConvertUtil.toEmpty(driverInfo.getCarNumber()));
				form.setField("id_number",ConvertUtil.toEmpty(driverInfo.getIdNumber()));
			}
			if (shipping.getDelivery() != null) {
				form.setField("delivery_name",ConvertUtil.toEmpty(shipping.getDelivery().getName()));
			}
			Warehouse warehouse = shipping.getWarehouse();
			if (warehouse != null) {
				form.setField("warehouse_name",ConvertUtil.toEmpty(warehouse.getName()));
			}
			
			//总数量
			BigDecimal total_quantity = BigDecimal.ZERO;
			//总箱数
			BigDecimal total_box_quantity = BigDecimal.ZERO;
			//总支数
			BigDecimal total_branch_quantity = BigDecimal.ZERO;
			//总零散支数
			BigDecimal total_scattered_quantity = BigDecimal.ZERO;
			BigDecimal total_amount = BigDecimal.ZERO;
			
			BigDecimal total_shipped_quantity = BigDecimal.ZERO;		//实发总数量
			BigDecimal total_shipped_box_quantity = BigDecimal.ZERO;	//实发总箱数
			BigDecimal total_shipped_branch_quantity = BigDecimal.ZERO;	//实发总支数
			BigDecimal total_shipped_scattered_quantity = BigDecimal.ZERO;//实发总零散支数支数
			BigDecimal total_shippedPrice = BigDecimal.ZERO;			//实发总金额

			BigDecimal totalVolume = new BigDecimal(0);
			BigDecimal totalQuantity = new BigDecimal(0);
			
			for (int j = i * pageSize; j < forNumber; j++) {

				ShippingItem shippingItem = nItems.get(j);
				Product product = shippingItem.getProduct();
				OrderItem orderItem = null;
				if(!ConvertUtil.isEmpty(shippingItem.getOrderItem())){
					orderItem = shippingItem.getOrderItem();
				}		
				form.setField("order_sn_" + column, shippingItem.getOrderSn());
				form.setField("sn_" + column, shippingItem.getSn());
				
				form.setField("name_" + column, shippingItem.getName());
				//产品编码 
				form.setField("vonder_code_" + column,
						ConvertUtil.toEmpty(product == null ? ""
						: product.getVonderCode()));
				//产品名称 /木种花色
				form.setField("product_name_" + column,
						ConvertUtil.toEmpty(product == null ? ""
						: product.getName()+"/"+product.getWoodTypeOrColor()));		
				//经营组织
				form.setField("product_organization_name_" + column,
						ConvertUtil.toEmpty(orderItem == null ?
								"" : orderItem.getProductOrganization() == null ? 
								"" : orderItem.getProductOrganization().getName()));
				//型号 
				form.setField("model_" + column,
						ConvertUtil.toEmpty(product == null ? ""
						: product.getModel()));
				//规格
				form.setField("product_spec_" + column,
						ConvertUtil.toEmpty(product == null ? ""
								: product.getSpec()));
				//数量
				BigDecimal quantity = shippingItem.getQuantity();//发货数量
				form.setField("quantity_" + column, nf.format(quantity));
				form.setField("quantity_with_util_" + column,nf.format(quantity) + ConvertUtil.toEmpty(product.getUnit()));
				total_quantity = total_quantity.add(quantity);
				//单位
				form.setField("unit_" + column,
						ConvertUtil.toEmpty(product == null ? ""
								: product.getUnit()));
				//件
				BigDecimal boxQuantity = shippingItem.getBoxQuantity();	//发货箱数
				if (boxQuantity == null) {
					boxQuantity = new BigDecimal(0);
				}
				form.setField("box_quantity_" + column, nf.format(boxQuantity));
				total_box_quantity = total_box_quantity.add(boxQuantity);
				//支数 
				BigDecimal branchQuantity = shippingItem.getBranchQuantity(); //发货支数
				if (branchQuantity == null) {
					branchQuantity = new BigDecimal(0);
				}
				form.setField("branch_quantity_" + column,nf.format(branchQuantity));
				total_branch_quantity = total_branch_quantity.add(branchQuantity);
				//支/件 
				BigDecimal branchPerBox  = product.getBranchPerBox();//每箱支数
				if(branchPerBox==null || (branchPerBox!=null && branchPerBox.compareTo(BigDecimal.ZERO)==0)){
					branchPerBox = new BigDecimal(1);
				}
				BigDecimal	scatteredQuantity;
				if(shipping.getSbu().getId()!=3){
					scatteredQuantity	 = branchQuantity.divideAndRemainder(branchPerBox)[1].setScale( 0, BigDecimal.ROUND_DOWN );
				}else{
					scatteredQuantity=new BigDecimal(0);
				}
				total_scattered_quantity = total_scattered_quantity.add(scatteredQuantity.stripTrailingZeros());
				form.setField("scattered_quantity_" + column,scatteredQuantity.stripTrailingZeros().toPlainString());
				//等级 
				String productGradeStr = "";
				if(shippingItem.getOrderItem()!=null && shippingItem.getOrderItem().getProductLevel()!=null){
					productGradeStr = shippingItem.getOrderItem().getProductLevel().getValue();
				}
				form.setField("product_grade_" + column, productGradeStr);
				//色号 
				form.setField("colourNumber_" + column,
						ConvertUtil.toEmpty(shippingItem.getColourNumbers() == null ?
								"" : shippingItem.getColourNumbers().getValue()));
				//含水率 
				form.setField("moistureContent_" + column, 
						ConvertUtil.toEmpty(shippingItem.getMoistureContents() == null ?
								"" : shippingItem.getMoistureContents().getValue()));
				//备注 
				form.setField("memo_" + column, shippingItem.getMemo());
				form.setField("erpMemo" + column, shippingItem.getMemo());
				
				/*获取实际发货信息*/
				BigDecimal shippedQuantity = shippingItem.getShippedQuantity();//实际发货数量
				if (shippedQuantity == null) {
					shippedQuantity = BigDecimal.ZERO;
				}
				form.setField("shipped_quantity_" + column, nf.format(shippedQuantity));
				
				BigDecimal shippedBoxQuantity = shippingItem.getShippedBoxQuantity();//实际发货箱数
				if (shippedBoxQuantity == null) {
					shippedBoxQuantity = BigDecimal.ZERO;
				}
				form.setField("shipped_box_quantity_" + column, nf.format(shippedBoxQuantity));
				
				BigDecimal shippedBranchQuantity = shippingItem.getShippedBranchQuantity();//实际发货支数
				if (shippedBranchQuantity == null) {
					shippedBranchQuantity = BigDecimal.ZERO;
				}
				form.setField("shipped_branch_quantity_" + column,nf.format(shippedBranchQuantity));
				
				BigDecimal	shippedScatteredQuantity = BigDecimal.ZERO;//实际发货零散支数
				if(shipping.getSbu().getId()!=3){
					shippedScatteredQuantity = shippedBranchQuantity.divideAndRemainder(branchPerBox)[1].setScale( 0, BigDecimal.ROUND_DOWN );
				}
				form.setField("shipped_scattered_quantity_" + column,shippedScatteredQuantity.stripTrailingZeros().toPlainString());
				
				//单价 
				BigDecimal price = shippingItem.getOrderItem().getPrice();
				
				//发货金额
				form.setField("description_" + column,ConvertUtil.toEmpty(shippingItem.getDescription()));
				BigDecimal lineAmount = shippingItem.getQuantity().multiply(price);
				String lineAmountStr = nf.format(lineAmount);
				
				//实际发货金额
				BigDecimal shippedPrice = shippingItem.getShippedQuantity().multiply(price);
				String shippedPriceStr = nf.format(shippedPrice);
				
				String volume = null;
				if (product != null && product.getVolume() != null) {
					BigDecimal lineVolume = product.getVolume().multiply(shippingItem.getQuantity());
					totalVolume = totalVolume.add(lineVolume);
					volume = nf.format(lineVolume);
				}
				if(!ConvertUtil.isEmpty(hiddenAmount) && hiddenAmount>0){
					form.setField("price" + column, nf.format(price));
					form.setField("line_amount_" + column, lineAmountStr);
					form.setField("shippedPrice_" + column, shippedPriceStr);
				}else{
					form.setField("price" + column, "***");
					form.setField("line_amount_" + column, "***");
					form.setField("shippedPrice_" + column, "***");
				}
				totalQuantity = totalQuantity.add(shippingItem.getQuantity());
				total_amount = total_amount.add(lineAmount);
				total_shipped_quantity = total_shipped_quantity.add(shippedQuantity);		//实发总数量
				total_shipped_box_quantity = total_shipped_box_quantity.add(shippedBoxQuantity);	//实发总箱数
				total_shipped_branch_quantity = total_shipped_branch_quantity.add(shippedBranchQuantity);	//实发总支数
				total_shipped_scattered_quantity = total_shipped_scattered_quantity.add(shippedScatteredQuantity); //实发总零散支数
				total_shippedPrice = total_shippedPrice.add(shippedPrice);			//实发总金额
				form.setField("volume_" + column, ConvertUtil.toEmpty(volume));
				column++;
			}
			form.setField("total_quantity", nf.format(total_quantity));
			form.setField("total_box_quantity", nf.format(total_box_quantity));
			form.setField("total_branch_quantity",nf.format(total_branch_quantity));
			
			//零散支数
			form.setField("total_scattered_quantity",nf.format(total_scattered_quantity));
			form.setField("total_shipped_quantity", nf.format(total_shipped_quantity));//实发总数量
			form.setField("total_shipped_box_quantity", nf.format(total_shipped_box_quantity));//实发总箱数
			form.setField("total_shipped_branch_quantity",nf.format(total_shipped_branch_quantity));//实发总支数
			if(!ConvertUtil.isEmpty(hiddenAmount) && hiddenAmount>0){
				form.setField("total_amount", nf.format(total_amount)); //发货总额
				form.setField("total_amount_cn",NumberToCN.number2CNMontrayUnit(total_amount)); //发货总额
				form.setField("total_shippedPrice", nf.format(total_shippedPrice));//实发总金额
			}else{
				form.setField("total_amount", "***"); //发货总额
				form.setField("total_amount_cn","***"); //发货总额
				form.setField("total_shippedPrice", "***");//实发总金额
			}
			form.setField("totalQuantity", nf.format(totalQuantity));
			form.setField("totalVolume", nf.format(totalVolume));
			if (logo != null && !"".equals(logo)) {
				try {
					int page = form.getFieldPositions("logo").get(0).page;
					Rectangle signRect = form.getFieldPositions("logo").get(0).position;
					float x = signRect.getLeft();
					float y = signRect.getBottom();
					// 读图片
					// http://pptest.etwowin.com/pdf/new_order/qrImage/qr_ccbac13a-2814-41de-9c95-252d4b443787.jpg
					Image image = Image.getInstance(logo.trim());
					// 获取操作的页面
					PdfContentByte under = stamp.getOverContent(page);
					// 根据域的大小缩放图片
					image.scaleToFit(signRect.getWidth(), signRect.getHeight());
					// 添加图片
					image.setAbsolutePosition(x, y);
					under.addImage(image);
				}
				catch (Exception e) {
					e.printStackTrace();
				}
			}
			// 二维码
			if (form.getFieldPositions("erweima") != null) {
				try {
					String imageStaticPath = "/pdf/image/"
							+ UUID.randomUUID()
							+ ".jpg";
					if (shipping != null && shipping.getSn() == null) {
						ExceptionUtil.throwServiceException("发货单号为空，无法生成二维码");
					}
					Setting setting = SettingUtils.get();
					String pdfHttpUrl = setting.getSiteUrl()
							+ "/"
							+ pdfStaticPath;
					String url = createRQ(imageStaticPath, pdfHttpUrl);
					int page = form.getFieldPositions("erweima").get(0).page;
					Rectangle erweimaSignRect = form.getFieldPositions("erweima")
							.get(0).position;
					float erweimaX = erweimaSignRect.getLeft();
					float erweimaY = erweimaSignRect.getBottom();
					String erweima = servletContext.getRealPath(url);
					Image erweimaMage = Image.getInstance(erweima);
					PdfContentByte pdfUnder = stamp.getOverContent(page);
					erweimaMage.scaleToFit(erweimaSignRect.getWidth(),
							erweimaSignRect.getHeight());
					erweimaMage.setAbsolutePosition(erweimaX, erweimaY);
					pdfUnder.addImage(erweimaMage);
				}
				catch (Exception e) {
					e.printStackTrace();
				}
			}
			stamp.setFormFlattening(true); // 千万不漏了这句啊, */
			stamp.close();
		}
		Document doc = new Document();
		PdfCopy pdfCopy = new PdfCopy(doc, fos);
		doc.open();
		PdfImportedPage impPage = null;
		// doc.add(new Paragraph("",font));
		/** 取出之前保存的每页内容 */
		for (int i = 0; i < pdfPage; i++) {
			impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);
			pdfCopy.addPage(impPage);
		}
		// 当文件拷贝 记得作废doc
		doc.close();
	}

	@Transactional(readOnly = true)
	public int deleteFile(String staticPath) {
		Assert.hasText(staticPath);

		File staticFile = new File(servletContext.getRealPath(staticPath));
		// File staticFile = new File( "/app/cloud_marketing/"+staticPath);
		if (staticFile.exists()) {
			staticFile.delete();
			return 1;
		}
		return 0;
	}

	public String createPdfTest(Long id) throws Exception {

		List<String> files = new ArrayList<String>();
		String template_path = "/pdf/shipping/template/shipping.pdf";
		String pdfStaticPath = null;
		try {
			Shipping shipping = find(id);
			String file_name = "" + UUID.randomUUID();
			pdfStaticPath = "/pdf/shipping/file/" + file_name + ".pdf";
			this.buildPdf(shipping, pdfStaticPath, template_path,null);
			files.add(pdfStaticPath);
		}
		catch (Exception e) {
			for (String file : files) {
				this.deleteFile(file);
			}
			throw e;
		}
		return pdfStaticPath;

	}

	public Integer count(String sn, String orderSn, String outTradeNo,
			String trackingNo, Integer[] status, Long[] warehouseIds,
			Long deliveryCorpId, Integer warehouseType, Long[] storeIds,
			String firstTime, String lastTime, Pageable pageable, Integer page,
			Integer size, Long sbuId,String colourNumber,String moistureContent,
			String batch,Long[] organizationIds) {
		return shippingDao.count(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseIds,
				deliveryCorpId,
				warehouseType,
				storeIds,
				firstTime,
				lastTime,
				pageable,
				page,
				size,
				sbuId,
				colourNumber,
				moistureContent,
				batch,
				organizationIds);
	}

	public List<Map<String, Object>> findItemList(String sn, String orderSn,
			String outTradeNo, String trackingNo, Integer[] status,
			Long[] warehouseIds, Long deliveryCorpId, Integer warehouseType,
			Long[] storeIds, String firstTime, String lastTime, Long[] ids,
			Integer page, Integer size, Long sbuId,String colourNumber,
			String moistureContent,String batch,Long[] organizationIds,String[] statusType) {
		return shippingDao.findItemList(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseIds,
				deliveryCorpId,
				warehouseType,
				storeIds,
				firstTime,
				lastTime,
				ids,
				page,
				size,
				sbuId,
				colourNumber,
				moistureContent,
				batch,
				organizationIds,
                statusType);
	}

	@Override
	@Transactional(readOnly = true)
	public Map<String, Object> findOperateItem(Long orderItemId,
			BigDecimal quantity, BigDecimal boxQuantity,
			BigDecimal branchQuantity, BigDecimal closedQuantity) {
		return shippingDao.findOperateItem(orderItemId,
				quantity,
				boxQuantity,
				branchQuantity,
				closedQuantity);
	}

	@Override
	public Map<String, Object> findOperateItemNew(Long orderItemId) {
		return shippingDao.findOperateItemNew(orderItemId);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findItemPageForWin(String shippingSn,
			String orderSn, String outTradeNo, String trackingNo,
			Integer[] status, Long warehouseId, Long deliveryCorpId,
			Long productId, Boolean[] isToReceipt, Integer shippeDday,
			Integer[] bomFlag, Integer[] orderType, Long storeId,
			Long supplierId, String storeName, String warehouseName,
			String productName, String model, String vonderCode, Integer flag,
			Pageable pageable) {
		return shippingDao.findItemPageForWin(shippingSn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseId,
				deliveryCorpId,
				productId,
				isToReceipt,
				shippeDday,
				bomFlag,
				orderType,
				storeId,
				supplierId,
				storeName,
				warehouseName,
				productName,
				model,
				vonderCode,
				flag,
				pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> newfindItemPage(String shippingSn,
			String orderSn, String outTradeNo, String trackingNo,
			Integer[] status, Long[] warehouseIds, Long deliveryCorpId,
			Long productId, Boolean[] isToReceipt, Integer shippeDday,
			Integer[] bomFlag, Integer[] orderType, Long[] storeIds,
			Long supplierId, String firstTime, String lastTime,
			String productModel, Pageable pageable) {
		return shippingDao.newfindItemPage(shippingSn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseIds,
				deliveryCorpId,
				productId,
				isToReceipt,
				shippeDday,
				bomFlag,
				orderType,
				storeIds,
				supplierId,
				firstTime,
				lastTime,
				productModel,
				pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Integer count(String shippingSn, String orderSn, String outTradeNo,
			String trackingNo, Integer[] status, Long warehouseId,
			Long deliveryCorpId, Long productId, Boolean[] isToReceipt,
			Integer shippeDday, Integer[] bomFlag, Integer[] orderType,
			Long storeId, Long supplierId, Integer page, Integer size) {
		return shippingDao.count(shippingSn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseId,
				deliveryCorpId,
				productId,
				isToReceipt,
				shippeDday,
				bomFlag,
				orderType,
				storeId,
				supplierId,
				page,
				size);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findShippingReceiptItemlist(
			String shippingSn, String orderSn, String outTradeNo,
			String trackingNo, Integer[] status, Long warehouseId,
			Long deliveryCorpId, Long productId, Boolean[] isToReceipt,
			Integer shippeDday, Integer[] bomFlag, Integer[] orderType,
			Long storeId, Long supplierId, Long[] ids, Integer page,
			Integer size) {

		return shippingDao.findShippingReceiptItemlist(shippingSn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseId,
				deliveryCorpId,
				productId,
				isToReceipt,
				shippeDday,
				bomFlag,
				orderType,
				storeId,
				supplierId,
				ids,
				page,
				size);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findAttachsByShippingId(Long id) {
		return shippingDao.findAttachsByShippingId(id);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> newfindPage(String sn, String orderSn,
			String outTradeNo, String trackingNo, Integer[] status,Integer[] wfState,
			Integer[] acceptStatus, Long[] warehouseId, Long deliveryCorpId,
			Integer warehouseType, Long[] storeId, Long supplierId,
			String firstTime, String lastTime, String firstErpTime,
			String lastErpTime, Boolean isUndertaking, Long[] saleOrgId,
			Long sbuId,String erpSn,Long[] productId, Pageable pageable,
			String colourNumber,String moistureContent,String batch,
			Integer pageType,Long[] warehouseIds,Long[] organizationIds,String storeMemberName,String[] statusType) {
		return shippingDao.newfindPage(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				wfState,
				acceptStatus,
				warehouseId,
				deliveryCorpId,
				warehouseType,
				storeId,
				supplierId,
				firstTime,
				lastTime,
				firstErpTime,
				lastErpTime,
				isUndertaking,
				saleOrgId,
				sbuId,
				erpSn,
				productId,
				pageable,
				colourNumber,
				moistureContent,
				batch,
				pageType,
				warehouseIds,
				organizationIds,
				storeMemberName,
				statusType);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findShippingItemListByOrderItemId(
			String orderItemId) {

		return shippingDao.findShippingItemListByOrderItemId(orderItemId);
	}

	/**
	 * 生成二维码(QRCode)图片的公共方法
	 * 
	 * @param content
	 *            存储内容
	 * @param imgType
	 *            图片类型
	 * @param size
	 *            二维码尺寸
	 * @return
	 */
	public BufferedImage qRCodeCommon(String content, String imgType, int size) {
		BufferedImage bufImg = null;
		try {
			Qrcode qrcodeHandler = new Qrcode();
			// 设置二维码排错率，可选L(7%)、M(15%)、Q(25%)、H(30%)，排错率越高可存储的信息越少，但对二维码清晰度的要求越小
			qrcodeHandler.setQrcodeErrorCorrect('M');
			qrcodeHandler.setQrcodeEncodeMode('B');
			// 设置设置二维码尺寸，取值范围1-40，值越大尺寸越大，可存储的信息越大
			qrcodeHandler.setQrcodeVersion(size);
			// 获得内容的字节数组，设置编码格式
			byte[] contentBytes = content.getBytes("utf-8");
			// 图片尺寸
			int imgSize = 67 + 12 * (size - 1);
			bufImg = new BufferedImage(imgSize, imgSize,
					BufferedImage.TYPE_INT_RGB);
			Graphics2D gs = bufImg.createGraphics();
			// 设置背景颜色
			gs.setBackground(Color.WHITE);
			gs.clearRect(0, 0, imgSize, imgSize);

			// 设定图像颜色> BLACK
			gs.setColor(Color.BLACK);
			// 设置偏移量，不设置可能导致解析出错
			int pixoff = 2;
			// 输出内容> 二维码
			if (contentBytes.length > 0 && contentBytes.length < 800) {
				boolean[][] codeOut = qrcodeHandler.calQrcode(contentBytes);
				for (int i = 0; i < codeOut.length; i++) {
					for (int j = 0; j < codeOut.length; j++) {
						if (codeOut[j][i]) {
							gs.fillRect(j * 3 + pixoff, i * 3 + pixoff, 3, 3);
						}
					}
				}
			}
			else {
				throw new Exception("QRCode content bytes  length = "
						+ contentBytes.length
						+ " not in [0, 800].");
			}
			gs.dispose();
			bufImg.flush();
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return bufImg;
	}

	public String createRQ(String url, String sn) throws IOException {

		// Setting setting = SettingUtils.get();
		// String siteUrl = setting.getSiteUrl();
		String content = sn;
		BufferedImage bufImg = qRCodeCommon(content, "jpg", 12);

		File imgFile = new File(servletContext.getRealPath(url));// 二维码
		// 生成二维码QRCode图片
		ImageIO.write(bufImg, "jpg", imgFile);
		return url;
	}

	public void updateShippingProject(Shipping shipping, Warehouse warehouse,
			DeliveryCorp delivery, Area area, Integer flag,
			Long[] creditRechargeContractId, String[] creditRechargeSn,
			BigDecimal[] creditRechargeDistributionAmount,
			Long[] depositRechargeContractId, String[] depositRechargeSn,
			BigDecimal[] depositRechargeDistributionAmount,
			Long[] policyCountContractId, String[] policyCountNo,
			BigDecimal[] policyCountDistributionAmount,
			Long[] depositPaymentBatchId, String[] saleNode) {

		if (flag == 0 && warehouse == null) {
			// 请选择仓库
			ExceptionUtil.throwServiceException("15141");
		}

		Shipping pshipping = find(shipping.getId());
		Store store = pshipping.getStore();
		Long storeId = store.getId();
		String shippingSn = pshipping.getSn();

		if (pshipping.getStatus().intValue() != 0) {
			ExceptionUtil.throwServiceException("发货单不是未审核状态，保存失败");
		}

		String sql = "select i.orders from xx_shipping_item i where i.shipping in ("
				+ shipping.getId().toString()
				+ ")";
		List<Map<String, Object>> items = getDaoCenter().getNativeDao()
				.findListMap(sql, null, 0);
		String orderIds = "";
		for (Map<String, Object> item : items) {
			String oid = item.get("orders") == null ? null : item.get("orders")
					.toString();
			if (oid != null && !orderIds.contains(oid)) {
				orderIds += "," + oid;
			}
		}
		orderIds = orderIds.substring(1);
		// 锁订单
		lockDataDao.lockOrder(orderIds);

		BigDecimal tVolume = BigDecimal.ZERO;
		BigDecimal tWeight = BigDecimal.ZERO;
		BigDecimal amount = BigDecimal.ZERO;
		List<ShippingItem> shippingItems = new ArrayList<ShippingItem>();
		String contract_no = "";
		for (ShippingItem shippingItem : shipping.getShippingItems()) {

			Warehouse pWarehouse = null;
			if (flag == 0) {
				// 大自然
				pWarehouse = warehouse;
			}
			else if (flag == 1) {
				// 天加
				Long pWarehouseId = (shippingItem.getWarehouse() != null) ? shippingItem.getWarehouse()
						.getId()
						: null;
				pWarehouse = warehouseBaseService.find(pWarehouseId);
				if (pWarehouse == null) {
					// 请为发货项选择发货仓库
					ExceptionUtil.throwServiceException("15182");
				}
				warehouse = pWarehouse;
			}

			System.out.println(shippingItem.getId());
			ShippingItem pShippingItem = shippingItemService.find(shippingItem.getId());
			OrderItem orderItem = pShippingItem.getOrderItem();
			// 修改订单计划发货数量
			BigDecimal changeQuantity = shippingItem.getQuantity()
					.subtract(pShippingItem.getQuantity());
			if (changeQuantity.compareTo(BigDecimal.ZERO) != 0) {
				if (orderItem != null) {
					orderItem.setShipPlanQuantity(orderItem.getShipPlanQuantity()
							.add(changeQuantity));
					orderItemService.update(orderItem);
				}
			}
			BigDecimal quantity = shippingItem.getQuantity();

			BigDecimal pVolume = pShippingItem.getVolume() == null ? BigDecimal.ZERO
					: pShippingItem.getVolume();
			BigDecimal pWeight = pShippingItem.getWeight() == null ? BigDecimal.ZERO
					: pShippingItem.getWeight();

			pShippingItem.setWarehouse(pWarehouse);
			pShippingItem.setPrice(orderItem.getPrice());
			pShippingItem.setQuantity(quantity);
			pShippingItem.setVolume(pVolume);
			pShippingItem.setWeight(pWeight);

			amount = amount.add(pShippingItem.getQuantity()
					.multiply(pShippingItem.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));

			BigDecimal volume = pVolume.multiply(quantity);// 行合计体积
			tVolume = tVolume.add(volume);// 总体积

			BigDecimal weight = pWeight.multiply(quantity);// 行合计重量
			tWeight = tWeight.add(weight);// 总重量

			shippingItems.add(pShippingItem);

			contract_no = pShippingItem.getOrder().getContractSn();
		}
		shipping.getShippingItems().clear();
		shipping.getShippingItems().addAll(shippingItems);

		shipping.setWarehouse(warehouse);
		shipping.setDelivery(delivery);
		shipping.setArea(area);
		shipping.setDelivery(delivery);
		shipping.setAmount(amount);
		shipping.setVolume(tVolume);
		shipping.setWeight(tWeight);

		// 附件
		List<ShippingAttach> shippingAttachs = shipping.getShippingAttachs();
		for (Iterator<ShippingAttach> iterator = shippingAttachs.iterator(); iterator.hasNext();) {
			ShippingAttach shippingAttach = iterator.next();
			if (shippingAttach == null || shippingAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (shippingAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			shippingAttach.setFileName(shippingAttach.getName()
					+ "."
					+ shippingAttach.getSuffix());
			shippingAttach.setShipping(pshipping);
			shippingAttach.setStoreMember(storeMemberBaseService.getCurrent());
		}

		List<Long> costIds = new ArrayList<Long>();
		for (Iterator<Cost> iterator = shipping.getCosts().iterator(); iterator.hasNext();) {
			Cost cost = iterator.next();
			if (cost != null && cost.getId() != null) {
				costIds.add(cost.getId());
			}

		}
		Long[] ignoreCostId = new Long[costIds.size()];
		for (int i = 0; i < costIds.size(); i++) {
			ignoreCostId[i] = costIds.get(i);
		}
		costService.deleteByBillCode(shippingSn, 1, ignoreCostId);

		Cost iCost = new Cost();
		iCost.setSaleOrg(new SaleOrg());
		for (Iterator<Cost> iterator = shipping.getCosts().iterator(); iterator.hasNext();) {
			Cost cost = iterator.next();

			if (cost.getId() == null
					&& cost.getCostType().getId() == null
					|| cost.getCostType() == null
					&& cost.getName() == null
					&& cost.getAmount() == null
					&& (cost.getSaleOrg() == null || cost.getSaleOrg().getId() == null)
					&& cost.getSaleOrgAmount() == null) {
				iterator.remove();
				continue;
			}

			if (cost.getCostType().getId() == null
					|| cost.getCostType() == null) {
				ExceptionUtil.throwServiceException("费用类型不能为空");
			}
			if (cost.getName() == null) {
				ExceptionUtil.throwServiceException("费用名称不能为空");
			}
			if (cost.getAmount() == null) {
				ExceptionUtil.throwServiceException("费用金额不能为空");
			}
			if (cost.getAmount().doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("费用金额不能小于等于0");
			}

//			if (cost.getSaleOrg() == null || cost.getSaleOrg().getId() == null) {
//				ExceptionUtil.throwServiceException("分公司不能为空");
//			}
//			if (cost.getSaleOrgAmount() == null) {
//				ExceptionUtil.throwServiceException("分公司金额不能为空");
//			}
//			if (cost.getSaleOrgAmount().doubleValue() <= 0) {
//				ExceptionUtil.throwServiceException("分公司金额不能小于等于0");
//			}
			cost.setBillCode(shippingSn);
			cost.setBillType(1);
			if (cost.getId() == null) {
				costService.save(cost);
			}
			else {
				costService.update(cost);
			}
		}

		List<Long> offsiteBranchIds = new ArrayList<Long>();
		for (Iterator<OffsiteBranch> iterator = shipping.getOffsiteBranchs()
				.iterator(); iterator.hasNext();) {
			OffsiteBranch offsiteBranch = iterator.next();
			if (offsiteBranch != null && offsiteBranch.getId() != null) {
				offsiteBranchIds.add(offsiteBranch.getId());
			}

		}
		Long[] ignoreOffsiteBranchId = new Long[offsiteBranchIds.size()];
		for (int i = 0; i < offsiteBranchIds.size(); i++) {
			ignoreOffsiteBranchId[i] = offsiteBranchIds.get(i);
		}
		offsiteBranchService.deleteByBillCode(shippingSn,
				1,
				ignoreOffsiteBranchId);

		OffsiteBranch iOffsiteBranch = new OffsiteBranch();
		iOffsiteBranch.setSaleOrg(new SaleOrg());

		for (Iterator<OffsiteBranch> iterator = shipping.getOffsiteBranchs()
				.iterator(); iterator.hasNext();) {
			OffsiteBranch offsiteBranch = iterator.next();

			if (offsiteBranch.getId() == null
					&& (offsiteBranch.getSaleOrg() == null || offsiteBranch.getSaleOrg()
							.getId() == null)
					&& offsiteBranch.getRate() == null
					&& offsiteBranch.getAmount() == null) {
				iterator.remove();
				continue;
			}

			if (offsiteBranch.getSaleOrg() == null
					|| offsiteBranch.getSaleOrg().getId() == null) {
				ExceptionUtil.throwServiceException("分公司不能为空");
			}

			if (offsiteBranch.getRate() == null) {
				ExceptionUtil.throwServiceException("应收占比不能为空");
			}
			if (offsiteBranch.getAmount() == null) {
				ExceptionUtil.throwServiceException("金额不能为空");
			}
			if (offsiteBranch.getAmount().doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("金额不能小于等于0");
			}

			offsiteBranch.setBillCode(shippingSn);
			offsiteBranch.setBillType(1);
			if (offsiteBranch.getId() == null) {
				offsiteBranchService.save(offsiteBranch);
			}
			else {
				offsiteBranchService.update(offsiteBranch);
			}
		}

		update(shipping,
				"status",
				"outTradeNo",
				"orderType",
				"store",
				"storeMember",
				"acceptStatus",
				"saleOrg",
				"organization");

		BigDecimal finalAmount = shipping.getAmount()
				.multiply(shipping.getPaymentRate().divide(new BigDecimal(100)));

		BigDecimal itemTotalAmount = BigDecimal.ZERO;

		creditRechargeContractService.deleteContractByOrderNo(shippingSn,
				2,
				creditRechargeContractId);

		List<String> creditCodes = new ArrayList<String>();
		for (int i = 0; creditRechargeSn != null && i < creditRechargeSn.length; i++) {
			String code = creditRechargeSn[i];
			if (code == null) {
				ExceptionUtil.throwServiceException("授信单号不能为空");
			}
			if (creditCodes.contains(code)) {
				ExceptionUtil.throwServiceException("请勿维护相同的授信单号");
			}
			else {
				creditCodes.add(code);
			}
			BigDecimal lineAmount = (creditRechargeDistributionAmount != null && creditRechargeDistributionAmount.length > 0) ? creditRechargeDistributionAmount[i]
					: null;
			if (lineAmount == null) {
				ExceptionUtil.throwServiceException("授信分配金额不能为空");
			}
			if (lineAmount.doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("授信分配金额不能小于等于零");
			}
			itemTotalAmount = itemTotalAmount.add(lineAmount);

			CreditRechargeContract creditRechargeContract = new CreditRechargeContract();
			creditRechargeContract.setCreditRechargeNo(creditRechargeSn[i]);
			creditRechargeContract.setOrderAmount(lineAmount);
			creditRechargeContract.setOrderNo(shippingSn);
			creditRechargeContract.setOrderType(2);
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", creditRechargeSn[i]));
			List<CreditRecharge> creditRecharges = creditRechargeService.findList(1,
					filters,
					null);
			if (!creditRecharges.isEmpty()) {
				creditRechargeContract.setRechargeType(creditRecharges.get(0)
						.getRechargeType());
			}
			creditRechargeContract.setContract_no(contract_no);

			List<Map<String, Object>> obj = creditRechargeService.findList(null,
					code);
			BigDecimal actual_amount = BigDecimal.ZERO;
			BigDecimal available_amount = BigDecimal.ZERO;
			if (obj != null && obj.size() > 0) {
				Map<String, Object> data = obj.get(0);
				if (data.get("actual_amount") != null) {
					actual_amount = new BigDecimal(data.get("actual_amount")
							.toString());
				}
				if (data.get("available_amount") != null) {
					available_amount = new BigDecimal(
							data.get("available_amount").toString());
				}
			}
			else {
				ExceptionUtil.throwServiceException("授信单号【" + code + "】不存在");
			}
			BigDecimal vailAmount = null;
			if (creditRechargeContractId != null
					&& creditRechargeContractId[i] != 0) {

				CreditRechargeContract pCreditRechargeContract = creditRechargeContractService.find(creditRechargeContractId[i]);
				BigDecimal oldAmount = pCreditRechargeContract.getOrderAmount();
				if (lineAmount.compareTo(oldAmount) == 1) {
					available_amount = available_amount.subtract(pCreditRechargeContract.getOrderAmount());
					vailAmount = actual_amount.subtract(available_amount);// 当前可分配金额
					if (lineAmount.compareTo(vailAmount) == 1) {
						ExceptionUtil.throwServiceException("授信单号【"
								+ code
								+ "】的可分配金额："
								+ vailAmount
								+ "，分配金额："
								+ lineAmount
								+ "，差额："
								+ vailAmount.subtract(lineAmount));
					}
				}
				creditRechargeContract.setId(creditRechargeContractId[i]);
				creditRechargeContractService.update(creditRechargeContract);
			}
			else {
				vailAmount = actual_amount.subtract(available_amount);// 当前可分配金额
				if (lineAmount.compareTo(vailAmount) == 1) {
					ExceptionUtil.throwServiceException("授信单号【"
							+ code
							+ "】的可分配金额："
							+ vailAmount
							+ "，分配金额："
							+ lineAmount
							+ "，差额："
							+ vailAmount.subtract(lineAmount));
				}
				creditRechargeContractService.save(creditRechargeContract);
			}
		}

		depositRechargeContractService.deleteContractByOrderNo(shippingSn,
				2,
				depositRechargeContractId);

		List<String> depositCodes = new ArrayList<String>();
		for (int i = 0; depositRechargeSn != null
				&& i < depositRechargeSn.length; i++) {

			String code = depositRechargeSn[i];
			if (code == null) {
				ExceptionUtil.throwServiceException("回款单号不能为空");
			}
			if (depositCodes.contains(code)) {
				ExceptionUtil.throwServiceException("请勿维护相同的回款单号");
			}
			else {
				depositCodes.add(code);
			}

			BigDecimal lineAmount = (depositRechargeDistributionAmount != null && depositRechargeDistributionAmount.length > 0) ? depositRechargeDistributionAmount[i]
					: null;
			if (lineAmount == null) {
				ExceptionUtil.throwServiceException("回款分配金额不能为空");
			}
			if (lineAmount.doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("回款分配金额不能小于等于零");
			}
			itemTotalAmount = itemTotalAmount.add(lineAmount);

			DepositRechargeContract depositRechargeContract = new DepositRechargeContract();
			depositRechargeContract.setDepositRechargeNo(depositRechargeSn[i]);
			depositRechargeContract.setOrderAmount(lineAmount);
			depositRechargeContract.setOrderNo(shippingSn);
			depositRechargeContract.setOrderType(2);
			if (depositPaymentBatchId != null
					&& depositPaymentBatchId.length > 0) {
				depositRechargeContract.setContractPaymentBatchId(depositPaymentBatchId[i]);
			}
			if (saleNode != null && saleNode.length > 0) {
				depositRechargeContract.setSaleNode(saleNode[i]);
			}

			List<Map<String, Object>> obj = depositRechargeContractService.findPaymentBatch(contract_no,
					"发货");
			BigDecimal actual_amount = BigDecimal.ZERO;
			BigDecimal available_amount = BigDecimal.ZERO;
			if (obj != null && obj.size() > 0) {
				Map<String, Object> data = obj.get(0);
				if (data.get("actual_amount") != null) {
					actual_amount = new BigDecimal(data.get("actual_amount")
							.toString());
				}
				if (data.get("available_amount") != null) {
					available_amount = new BigDecimal(
							data.get("available_amount").toString());
				}
			}
			else {
				ExceptionUtil.throwServiceException("回款单号【" + code + "】不存在");
			}

			BigDecimal vailAmount = null;
			if (depositRechargeContractId != null
					&& depositRechargeContractId[i] != 0) {

				DepositRechargeContract pDepositRechargeContract = depositRechargeContractService.find(depositRechargeContractId[i]);
				BigDecimal oldAmount = pDepositRechargeContract.getOrderAmount();
				if (lineAmount.compareTo(oldAmount) == 1) {
					available_amount = available_amount.subtract(pDepositRechargeContract.getOrderAmount());
					vailAmount = actual_amount.subtract(available_amount);// 当前可分配金额
					if (lineAmount.compareTo(vailAmount) == 1) {
						ExceptionUtil.throwServiceException("回款单号【"
								+ code
								+ "】的可分配金额："
								+ vailAmount
								+ "，分配金额："
								+ lineAmount
								+ "，差额："
								+ vailAmount.subtract(lineAmount));
					}
				}

				depositRechargeContract.setId(depositRechargeContractId[i]);
				depositRechargeContractService.update(depositRechargeContract);
			}
			else {

				vailAmount = actual_amount.subtract(available_amount);// 当前可分配金额
				if (lineAmount.compareTo(vailAmount) == 1) {
					ExceptionUtil.throwServiceException("回款单号【"
							+ code
							+ "】的可分配金额："
							+ vailAmount
							+ "，分配金额："
							+ lineAmount
							+ "，差额："
							+ vailAmount.subtract(lineAmount));
				}
				depositRechargeContractService.save(depositRechargeContract);
			}

		}

		policyCountContractService.deleteContractByOrderNo(shippingSn,
				2,
				policyCountContractId);

		if (policyCountDistributionAmount != null) {
			if (policyCountDistributionAmount.length > 1) {
				ExceptionUtil.throwServiceException("回款类型【政策】只能维护一条");
			}

			if (policyCountDistributionAmount.length == 1) {

				BigDecimal actualAmount = policyCountService.findTotalActualAmount(storeId);
				BigDecimal availableAmount = policyCountService.findTotalAvailableAmount(storeId);

				for (int i = 0; i < policyCountDistributionAmount.length; i++) {

					if (policyCountNo == null
							|| policyCountNo.length == 0
							|| policyCountNo[i] == null) {
						ExceptionUtil.throwServiceException("请先获取政策金额");
					}

					BigDecimal lineAmount = policyCountDistributionAmount[i];
					if (lineAmount == null) {
						ExceptionUtil.throwServiceException("政策分配金额不能为空");
					}
					if (lineAmount.doubleValue() <= 0) {
						ExceptionUtil.throwServiceException("政策分配金额不能小于等于零");
					}
					itemTotalAmount = itemTotalAmount.add(lineAmount);

					PolicyCountContract policyCountContract = new PolicyCountContract();
					policyCountContract.setPolicyCountNo(policyCountNo[i]);
					policyCountContract.setOrderAmount(lineAmount);
					policyCountContract.setOrderNo(shippingSn);
					policyCountContract.setOrderType(2);
					if (policyCountContractId != null
							&& policyCountContractId[i] != 0) {
						PolicyCountContract pPolicyCountContract = policyCountContractService.find(policyCountContractId[i]);
						BigDecimal oldAmount = pPolicyCountContract.getOrderAmount();
						if (lineAmount.compareTo(oldAmount) == 1) {
							availableAmount = availableAmount.subtract(oldAmount);// 已分配金额
							BigDecimal vailAmount = actualAmount.subtract(availableAmount);// 可分配金额
							if (lineAmount.compareTo(vailAmount) == 1) {
								ExceptionUtil.throwServiceException("可分配政策金额："
										+ vailAmount
										+ "，分配金额："
										+ lineAmount
										+ "，差额："
										+ vailAmount.subtract(lineAmount));
							}
						}

						policyCountContract.setId(policyCountContractId[i]);
						policyCountContractService.update(policyCountContract);
					}
					else {
						BigDecimal vailAmount = actualAmount.subtract(availableAmount);// 可分配金额
						if (lineAmount.compareTo(vailAmount) == 1) {
							ExceptionUtil.throwServiceException("可分配政策金额："
									+ vailAmount
									+ "，分配金额："
									+ lineAmount
									+ "，差额："
									+ vailAmount.subtract(lineAmount));
						}
						policyCountContractService.save(policyCountContract);
					}
				}
			}
		}

		if (itemTotalAmount.compareTo(finalAmount) != 0) {
			ExceptionUtil.throwServiceException("分配金额合计："
					+ itemTotalAmount
					+ "，发货金额："
					+ shipping.getAmount()
					+ "，付款方式："
					+ shipping.getPaymentRate()
					+ "%，实际发货金额："
					+ finalAmount
					+ "，差额:"
					+ itemTotalAmount.subtract(finalAmount));
		}

	}

	public void saveOrderIntf(Shipping shipping) {

		Warehouse warehouse = shipping.getWarehouse();
		Organization managementOrganization = warehouse.getManagementOrganization();
		SystemDict stockSystemDict = warehouse.getStockSystemDict();

		Order mOrder = shipping.getShippingItems()
				.get(0)
				.getOrderItem()
				.getOrder();
		Store store = shipping.getStore();
		SystemDict businessType = store.getBusinessType();
		SystemDict sbu = store.getSbu();

		Map<String, Object> requestMap = new HashMap<String, Object>();

		requestMap.put("SOURCE_CODE", "CRM");// 来源系统代码,固定“CRM”
		requestMap.put("SOURCE_NUM", shipping.getId());// 来源ID,CRM发货通知单头id
		requestMap.put("ORG_ID", managementOrganization.getName());// 经营组织id,根据仓库的经营组织id来取
		requestMap.put("ORDER_TYPE", "零售订单");// 订单类型,传名称：零售订单、零售退货订单
		requestMap.put("BUSSINESS TYPE", businessType.getValue());// 业务类型,客制化字段，传名称：经销商零售、商业地板、直营零售、家装、电商
		requestMap.put("CUSTOMER_NUMBER", store.getOutTradeNo());// 客户编码
		requestMap.put("SOURCE_SHIP TO_ADDRESS", shipping.getAddress());// 收货地址id,CRM客户收货地址id
		requestMap.put("SOURCE_BILL TO_ADDRESS", shipping.getAddress());// 收单地址id,CRM客户收单地址id
		requestMap.put("SALESREP", "");// 销售人员,ERP存储待定
		requestMap.put("TERMS", "立即");// 付款条件,传：立即
		requestMap.put("CUST_PO_NUMBER", shipping.getSn());// 客户PO,CRM发货通知单号
		requestMap.put("ORDERED_DATE",
				DateUtil.convert(shipping.getCheckDate(), "yyyy-MM-dd"));// 订单日期,格式按照ESB标准，当前日期，2018-07-11
		requestMap.put("SBU", sbu.getValue());// SBU,订单头ATTRIBUTE10，家居头弹性域，传代码，CRM从机构属性里面取
												// 地板中心、….
		requestMap.put("TRANSACTIONAL_CURR_CODE", "CNY");// 币种,传:CNY
		requestMap.put("SHIPPING_METHOD", "");// 发运方式,海运、汽运、铁运、海铁联运，值待补充
		requestMap.put("FREIGHT_CARRIER", "");// 承运商名称
		requestMap.put("DRIVER", "");// 司机姓名,客制化字段
		requestMap.put("PHONE NUMBER", "");// 司机手机号,客制化字段
		requestMap.put("LICENSE PLATE", "");// 车号/车牌号,客制化字段
		requestMap.put("ORDER NOTES", "");// 订单备注,客制化字段
		requestMap.put("SHIPPING_INSTRUCTIONS", "");// 发货备注,客制化字段
		requestMap.put("ERP_USER_NAME", shipping.getCheckStoreMember()
				.getName());// 创建人,创建人工号，对应FND_USER表
		requestMap.put("ATTRIBUTE16", mOrder.getSn());// 关联订单号,CRM销售订单号
		requestMap.put("ATTRIBUTE17", "");// 地区,家居头弹性域，传CRM的客户所属机构名称
		requestMap.put("ATTRIBUTE3", "");// 工厂完工入库日期,全局头弹性域，不传
		requestMap.put("ATTRIBUTE4", "");// 送货双柜数,全局头弹性域，不传
		requestMap.put("ATTRIBUTE5", "");// 工程项目类型,全局头弹性域，不传
		requestMap.put("ATTRIBUTE15", "");// 是否已经发送发货通知,全局头弹性域，不传
		requestMap.put("ATTRIBUTE6", "");// 起始发货地点,全局头弹性域，不传
		requestMap.put("ATTRIBUTE11", "");// 工程项目编号,家居头弹性域，不传
		requestMap.put("ATTRIBUTE12", "");// 交货日期,家居头弹性域，不传
		requestMap.put("ATTRIBUTE13", "");// 订单来源,家居头弹性域，不传
		requestMap.put("ATTRIBUTE14", "");// 订单来源备注,家居头弹性域，不传
		requestMap.put("ATTRIBUTE18", "");// 合同名称,家居头弹性域，不传
		requestMap.put("ATTRIBUTE19", "");// 分派工厂,家居头弹性域，不传
		requestMap.put("ATTRIBUTE20", "");// 是否内部交易,家居头弹性域，不传

		List<Map<String, Object>> lineTbl = new ArrayList<Map<String, Object>>();

		for (ShippingItem shippingItem : shipping.getShippingItems()) {
			Product product = shippingItem.getProduct();
			
			String productGradeName = "";
			if (product.getProductLevel()!=null) {
				productGradeName = product.getProductLevel().getValue();
			}
			
			OrderItem orderItem = shippingItem.getOrderItem();

			Map<String, Object> lineData = new HashMap<String, Object>();
			Map<String, Object> lineTblItem = new HashMap<String, Object>();

			lineTblItem.put("SOURCE_CODE", "CRM");// 来源系统代码,CRM
			lineTblItem.put("SOURCE_NUM", shipping.getId());// 来源ID,CRM发货通知单头id
			lineTblItem.put("SOURCE_LINE_NUM", shippingItem.getId());// 来源行ID,CRM发货通知单行id
			lineTblItem.put("ORDERED_ITEM", shippingItem.getVonderCode());// 物料编码
			lineTblItem.put("MATERIAL GRADE", productGradeName);// 物料等级,订单行ATTRIBUTE2，传文本：优等品、二等品
			lineTblItem.put("ORDERED_QUANTITY", shippingItem.getQuantity());// 数量（平方数）
			lineTblItem.put("ORDER_QUANTITY_UOM", product.getUnit());// 单位（平方）,m2
			lineTblItem.put("ORDERED_QUANTITY2",
					shippingItem.getBranchQuantity());// 数量（支）
			lineTblItem.put("ORDERED_QUANTITY_UOM2", "支");// 辅助单位（支）,支
			lineTblItem.put("UNIT_SELLING_PRICE", orderItem.getPrice());// 单价（平方）,2位小数
			lineTblItem.put("SHIP_FROM", warehouse.getErp_warehouse_code());// 仓库代码,取CRM主表仓库代码
			lineTblItem.put("ORGANIZATION_ID", stockSystemDict.getValue());// 库存组织id,根据仓库的库存组织id来取
			lineTblItem.put("SCHEDULE_ARRIVAL_DATE",
					DateUtil.convert(shipping.getShippingTime(), "yyyy-MM-dd"));// 交货日期,取CRM主表交货日期

			if (product != null
					&& product.getBranchPerBox() != null
					&& shippingItem != null
					&& shippingItem.getBranchQuantity() != null) {
				lineTblItem.put("ATTRIBUTE17",
						shippingItem.getBranchQuantity()
								.divide(product.getBranchPerBox(),
										6,
										BigDecimal.ROUND_HALF_UP));// 箱数(变为 用支数/每箱支数)
			}
			else {
				lineTblItem.put("ATTRIBUTE17", BigDecimal.ZERO);// 箱数,全局行弹性域
			}
			// lineTblItem.put("ATTRIBUTE17",
			// shippingItem.getBoxQuantity());//箱数,全局行弹性域

			lineTblItem.put("ATTRIBUTE18", shipping.getConsignee()
					+ ";"
					+ shipping.getPhone());// 联系人信息,全局行弹性域，传CRM主表收货人姓名和电话
											// 前两段为姓名和电话用“；”分隔，
											// 比如
											// “李工;16543060061;;;;;”
			lineTblItem.put("LINE_TYPE", "");// 订单行类型,不传
			lineTblItem.put("ATTRIBUTE3", "");// 物料色号,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE1", "");// 合同编号,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE19", "");// 实际收货数量,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE20", "");// 是否已经同步,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE4", "");// 发票抬头,全局行弹性域，不传
			lineTblItem.put("ATTRIBUTE5", "");// 成品编码,家居行弹性域，不传
			lineTblItem.put("ATTRIBUTE6", "");// 成品序号,家居行弹性域，不传
			lineTblItem.put("ATTRIBUTE7", "");// 门类别,家居行弹性域，不传
			lineTblItem.put("ATTRIBUTE8", "");// 木门玻璃型号,家居行弹性域，不传

			lineData.put("LINE_TBL_ITEM", lineTblItem);
			lineTbl.add(lineData);
		}
		requestMap.put("LINE_TBL", lineTbl);

	}

	@Override
	public void agreeBack(Wf wf) {
		if (wf.getStat().intValue() == 2) {
			Shipping shipping = find(wf.getObjId());
			// 锁客户
			Store store = shipping.getStore();
			lockDataDao.lockStore(store.getId().toString());
			shipping.setStatus(1);
			shipping.setAcceptStatus(1);
			shipping.setCheckDate(new Date());
			update(shipping);
			// 发货审核写接口
			//this.saveIntfAtCheck(shipping);
			
			
			
		}
	}

//	public void saveIntfOrderKb(Long itemId, BigDecimal quantity,
//			StockOut stockOut) throws Exception {
//
//		List<Map<String, Object>> dataItems = new ArrayList<Map<String, Object>>();
//		ShippingItem shippingItem = shippingItemService.find(itemId);
//		Warehouse warehouse = shippingItem.getWarehouse();
//		if (warehouse == null) {
//			ExceptionUtil.throwServiceException("仓库不能为空");
//		}
//		if (warehouse.getErp_warehouse_code() == null) {
//			ExceptionUtil.throwServiceException("仓库【"
//					+ warehouse.getName()
//					+ "】的外部仓库编号不能为空");
//		}
//		Order order = shippingItem.getOrder();
//		OrderItem orderItem = shippingItem.getOrderItem();
//		Product product = orderItem.getProduct();
//		if (orderItem.getVonderCode() == null) {
//			ExceptionUtil.throwServiceException("产品编码不能为空");
//		}
//
//		String volume = "";
//		if (product.getVolume() != null) {
//			volume = product.getVolume().multiply(quantity).toString();
//		}
//		Map<String, Object> item = new HashMap<String, Object>();
//		item.put("entryId", orderItem.getId());
//		item.put("number", orderItem.getVonderCode());
//		item.put("qty", quantity);
//		item.put("price", orderItem.getPrice());
//		item.put("volume", volume);
//		item.put("stockNo", warehouse.getErp_warehouse_code());
//		dataItems.add(item);
//
//		Store store = order.getStore();
//		if (store.getOutTradeNo() == null) {
//			ExceptionUtil.throwServiceException("客户【"
//					+ store.getName()
//					+ "】的客户编码不能为空");
//		}
//
//		SaleOrg saleOrg = order.getSaleOrg();
//		if (saleOrg.getSn() == null) {
//			ExceptionUtil.throwServiceException("机构【"
//					+ saleOrg.getName()
//					+ "】的机构编码不能为空");
//		}
//
//		SystemDict businessDivision = product.getBusinessDivision();
//		if (businessDivision == null) {
//			ExceptionUtil.throwServiceException("产品【"
//					+ product.getVonderCode()
//					+ "】的事业部不能为空");
//		}
//
//		if (businessDivision.getLowerCode() == null) {
//			ExceptionUtil.throwServiceException("事业部【"
//					+ businessDivision.getValue()
//					+ "】的词汇编码不能为空");
//		}
//
//		Map<String, Object> requestData = new HashMap<String, Object>();
//		requestData.put("ctrlNo", saleOrg.getSn());
//		requestData.put("billNo", order.getSn());
//		requestData.put("date", DateUtil.convert(order.getCheckDate()));
//		requestData.put("custNo", store.getOutTradeNo());
//		requestData.put("issueBillNo", stockOut.getSn());
//		requestData.put("storageOrgNo", businessDivision.getLowerCode());
//
//		requestData.put("entry", dataItems);
//		String requestJson = JsonUtils.toJson(requestData);
//
//		Long companyInfoId = order.getCompanyInfoId();
//		String[] fields = new String[] { order.getSn(), stockOut.getSn() };
//		intfService.insert("order", companyInfoId, requestJson, fields);
//	}
	
	/**
	 * 查询订单明细分页数据
	 */
	public Page<Map<String, Object>> findItemPage(String shippingSn,Long[] storeId,
			String name, String consignee, String vonderCode, String model,
			String storeMemberName,Long[] saleOrgId,Pageable pageable,Long[] ids){
		return shippingDao.findItemPage(shippingSn,storeId,name,consignee,vonderCode,model,
				 storeMemberName,saleOrgId, pageable,ids);
	}

	@Override
	public Page<Map<String, Object>> returnReceivelist_data(String sn, Long[] storeId, String name, String consignee,
			String vonderCode, String model, String storeMemberName, Long[] saleOrgId, Pageable pageable, Long[] ids) {
		
		return shippingDao.returnReceivelist_data(sn,storeId,name,consignee,vonderCode,
				model,storeMemberName,saleOrgId,pageable,ids);
	}

	@Override
	public Page<Map<String, Object>> returnReceiveEditlist_data(Pageable pageable) {
		return shippingDao.returnReceiveEditlist_data(pageable);
	}

	@SuppressWarnings({ "finally", "unused" })
	@Override
	public Map<String, Object> getInvoiceDataToxls(Long shppingId, int typeId,Pageable pageable) {
		if (shppingId == null || shppingId == 0) {
			ExceptionUtil.throwServiceException("参数ID不能为空");
		}
		List<Map<String, Object>> listDatas = new ArrayList<Map<String,Object>>();
		Map<String, Object> title = new HashMap<String, Object>();
		//创建参数实体
		ReportingEntityVo reportingEntityVo = new ReportingEntityVo();
		try {
			Page<Map<String, Object>> pageData = shippingDao.getShppingItemByShippingId(shppingId,typeId, pageable);         //获取单据信息
			if (pageData.getTotal() > 0) {				
				Map<String, Object> firstData = pageData.getContent().get(0);
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");				
				reportingEntityVo.setHead(CommonUtil.getReportByType(typeId));
				reportingEntityVo.setUserName(ConvertUtil.toEmpty(firstData.get("store_name")));
				reportingEntityVo.setDocumentNo(ConvertUtil.toEmpty(firstData.get("sn")));
				reportingEntityVo.setDocumentDate(firstData.get("glDate") == null?"":sdf.format(firstData.get("glDate")));
				reportingEntityVo.setWarehouse(ConvertUtil.toEmpty(firstData.get("wareName")));
				reportingEntityVo.setUserCode(ConvertUtil.toEmpty(firstData.get("outTradeNo")));
				reportingEntityVo.setAbbreviation(ConvertUtil.toEmpty(firstData.get("alias")));  //客户简称
				reportingEntityVo.setShipVia(ConvertUtil.toEmpty(firstData.get("shippingMethod")));  //发运方式
				reportingEntityVo.setRemarks(ConvertUtil.toEmpty(firstData.get("shippMemo")));  //字段
				reportingEntityVo.setContactInformation(ConvertUtil.toEmpty(firstData.get("consignee"))+"/"+ConvertUtil.toEmpty(firstData.get("phone")));
				reportingEntityVo.setAddress(ConvertUtil.toEmpty(firstData.get("shippAddress")));  //地址
                reportingEntityVo.setDriverInfoName(ConvertUtil.toEmpty(firstData.get("driverInfoName")));
                reportingEntityVo.setCarNumber(ConvertUtil.toEmpty(firstData.get("carNumber")));
                //审核人
                reportingEntityVo.setExamineName(ConvertUtil.toEmpty(firstData.get("examineName")));
                //制单人
                reportingEntityVo.setEstablishName(ConvertUtil.toEmpty(firstData.get("establishName")));
                //收货人
                reportingEntityVo.setConsignee(ConvertUtil.toEmpty(firstData.get("consignee")));
				DocumentDetails documentDetails = null;
				List<DocumentDetails>  list = new  ArrayList<DocumentDetails>();
				//  合计
				BigDecimal totalBoxQuantity = new BigDecimal("0");   //箱
				BigDecimal totalBranchQuantity = new BigDecimal("0");   //支
				BigDecimal totalQuantity = new BigDecimal("0");   //数量
                BigDecimal totalScattered = new BigDecimal("0");   //零散支数
				
				for (Map<String, Object> item : pageData.getContent()) {
					documentDetails = new DocumentDetails();
					documentDetails.setProductCode(ConvertUtil.toEmpty(item.get("vonderCode")));
					documentDetails.setProductName(ConvertUtil.toEmpty(item.get("productName")));
					documentDetails.setDetailDescription(ConvertUtil.toEmpty(item.get("detail_description")));
					documentDetails.setModel(ConvertUtil.toEmpty(item.get("model")));
					documentDetails.setSpecifications(ConvertUtil.toEmpty(item.get("spec")));
					documentDetails.setPiece(ConvertUtil.toEmpty(item.get("boxQuantity")).substring(0, String.valueOf(item.get("boxQuantity")).indexOf(".")));
					//totalBoxQuantity = totalBoxQuantity.add(new BigDecimal(item.get("boxQuantity")==null?"0":String.valueOf(item.get("boxQuantity"))));
                    totalBoxQuantity = totalBoxQuantity.add(new BigDecimal(documentDetails.getPiece()));
					documentDetails.setNumberOfPieces(ConvertUtil.toEmpty(item.get("branchQuantity")).substring(0,String.valueOf(item.get("branchQuantity")).indexOf(".")));
					//支/件
					documentDetails.setBranch(ConvertUtil.toEmpty(item.get("branchPerBox")).substring(0,String.valueOf(item.get("branchPerBox")).indexOf(".")));
					//totalBranchQuantity = totalBranchQuantity.add(new BigDecimal(item.get("branchQuantity")==null?"0":String.valueOf(item.get("branchQuantity"))));
                    totalBranchQuantity = totalBranchQuantity.add(new BigDecimal(documentDetails.getNumberOfPieces()));
					//零散
                    documentDetails.setScattered(String.valueOf(Integer.valueOf(documentDetails.getNumberOfPieces())%Integer.valueOf(documentDetails.getPiece())));
                    totalScattered = totalScattered.add(new BigDecimal(documentDetails.getScattered()));


                    //数量
					documentDetails.setNumber(ConvertUtil.toEmpty(item.get("quantity")));
					totalQuantity = totalQuantity.add(new BigDecimal(item.get("quantity")==null?"0":String.valueOf(item.get("quantity"))));

					documentDetails.setAmountOfMoney(ConvertUtil.toEmpty(item.get("saleOrgPrice"))); // 金额
					documentDetails.setCompany(ConvertUtil.toEmpty(item.get("unit"))); // 单位
					documentDetails.setGrade(ConvertUtil.toEmpty(item.get("proGrade"))); // 等级
					
					documentDetails.setColorNumber(ConvertUtil.toEmpty(item.get("colorNum"))); // 色号
					documentDetails.setMoistureContent(ConvertUtil.toEmpty(item.get("moisCon"))); // 含水率
					documentDetails.setRemarks(ConvertUtil.toEmpty(item.get("memo"))); // 备注
					list.add(documentDetails);
				}

				reportingEntityVo.setDocumentDetails(list);
				initializationData(reportingEntityVo,title,listDatas,totalBoxQuantity,totalBranchQuantity,totalQuantity,totalScattered);
			}else {
				ExceptionUtil.throwServiceException("获取单据信息为null");
			}

		}catch (Exception e) {
			logger.error("发货单报表数据异常", e);
		}finally {	
			title.put("maplist",listDatas);
			return title;
		}
		
	}
	
	/**
	 * 初始化数据
	 * @param reportingEntityVo
	 * @param title
	 * @param listDatas
	 */
	@SuppressWarnings("unused")
	void initializationData(ReportingEntityVo reportingEntityVo,Map<String, Object> title,List<Map<String, Object>> listDatas,BigDecimal totalBoxQuantity,BigDecimal totalBranchQuantity,BigDecimal totalQuantity,BigDecimal totalScattered) {
		if (reportingEntityVo != null) {	
			title.put("head", CommonUtil.emptyConversion(reportingEntityVo.getHead()));     //报表标题
			title.put("documentNo", CommonUtil.emptyConversion(reportingEntityVo.getDocumentNo()));         //单据编号
			title.put("documentDate", CommonUtil.emptyConversion(reportingEntityVo.getDocumentDate()));    //单据日期
			title.put("warehouse", CommonUtil.emptyConversion(reportingEntityVo.getWarehouse()));    //仓库
			title.put("userCode", CommonUtil.emptyConversion(reportingEntityVo.getUserCode()));   //客户编码
			title.put("abbreviation", CommonUtil.emptyConversion(reportingEntityVo.getAbbreviation()));   //客户简介
			title.put("userName", CommonUtil.emptyConversion(reportingEntityVo.getUserName()));  //客户名称
			title.put("remarks", CommonUtil.emptyConversion(reportingEntityVo.getRemarks()));   //发运备注
            title.put("shipVia",CommonUtil.emptyConversion(reportingEntityVo.getShipVia()));  //发运方式
            title.put("examineName",CommonUtil.emptyConversion(reportingEntityVo.getExamineName()));
            title.put("establishName",CommonUtil.emptyConversion(reportingEntityVo.getEstablishName()));
            title.put("address",CommonUtil.emptyConversion(reportingEntityVo.getAddress()));
			title.put("contactInformation", CommonUtil.emptyConversion(reportingEntityVo.getContactInformation()));  //联系方式
            title.put("consignee",CommonUtil.emptyConversion(reportingEntityVo.getConsignee()));
            title.put("driverInfoName",CommonUtil.emptyConversion(reportingEntityVo.getDriverInfoName()));
            title.put("carNumber",CommonUtil.emptyConversion(reportingEntityVo.getCarNumber()));
            //合计
            title.put("totalBox",totalBoxQuantity);
            title.put("totalBranch",totalBranchQuantity);
            title.put("totalQuantity",totalQuantity);
            title.put("totalScattered",totalScattered);
			List<DocumentDetails> documentDetailsList = reportingEntityVo.getDocumentDetails();
			if (documentDetailsList != null && documentDetailsList.size() > 0) {				
				for(DocumentDetails documentDetails : documentDetailsList) {
					Map<String, Object> documentDetail = new HashMap<String, Object>();
					documentDetail.put("productCode", CommonUtil.emptyConversion(documentDetails.getProductCode()));   //产品编码
                    documentDetail.put("productName",CommonUtil.emptyConversion(documentDetails.getProductName())); //名称
                    documentDetail.put("detailDescription",CommonUtil.emptyConversion(documentDetails.getDetailDescription())); //描述
                    documentDetail.put("nameOrWoodColor",CommonUtil.emptyConversion(documentDetails.getNameOrWoodColor()));   //名称或木种
					documentDetail.put("model", CommonUtil.emptyConversion(documentDetails.getModel()));   //型号
					documentDetail.put("specifications",CommonUtil.emptyConversion(documentDetails.getSpecifications()));   //规格
					documentDetail.put("piece", CommonUtil.emptyConversion(documentDetails.getPiece()));   //件
					documentDetail.put("numberOfPieces", CommonUtil.emptyConversion(documentDetails.getNumberOfPieces()));  //支数
					documentDetail.put("branch", CommonUtil.emptyConversion(documentDetails.getBranch()));   //支/件
                    documentDetail.put("scattered",CommonUtil.emptyConversion(documentDetails.getScattered()));  //零散支数
					documentDetail.put("number", CommonUtil.emptyConversion(documentDetails.getNumber()));   //数量
					documentDetail.put("amountOfMoney", CommonUtil.emptyConversion(documentDetails.getAmountOfMoney()));   //金额
					documentDetail.put("company",CommonUtil.emptyConversion(documentDetails.getCompany()));		 //单位  
					documentDetail.put("grade",CommonUtil.emptyConversion(documentDetails.getGrade()));  //等级  
					documentDetail.put("colorNumber",CommonUtil.emptyConversion(documentDetails.getColorNumber()));  //色号 
					documentDetail.put("moistureContent",CommonUtil.emptyConversion(documentDetails.getMoistureContent()));  //含水率
					documentDetail.put("remarks",CommonUtil.emptyConversion(documentDetails.getRemarks()));  //备注					
					listDatas.add(documentDetail);
				}				
			}else {
				Map<String, Object> documentDetail = new HashMap<String, Object>();
				documentDetail.put("productCode", "");   //产品编码
				documentDetail.put("nameOrWoodColor","");   //名称或木种
				documentDetail.put("model","");   //型号
				documentDetail.put("specifications","");   //规格
				documentDetail.put("piece", "");   //件
				documentDetail.put("numberOfPieces", "");  //支数
                documentDetail.put("scattered","");
				documentDetail.put("branch", "");   //支/件
				documentDetail.put("number", "");   //数量
				documentDetail.put("amountOfMoney", "");   //金额
				documentDetail.put("company","");		 //单位  
				documentDetail.put("grade","");  //等级  
				documentDetail.put("colorNumber","");  //色号 
				documentDetail.put("moistureContent","");  //含水率
				documentDetail.put("remarks","");  //备注					
				listDatas.add(documentDetail);
			}
		}	
	}
	
	/**
	 * 校验发货通知单状态
	 * @param shipping
	 * @param operationName
	 */
	private void checkShippingState(Shipping shipping,String operationName){
		if(ConvertUtil.isEmpty(shipping)){
			ExceptionUtil.throwServiceException("该发货通知单不存在");
		}
		if(!ConvertUtil.isEmpty(shipping.getStatus()) && shipping.getStatus() != 0){
			ExceptionUtil.throwServiceException("只有单据状态为已保存的发货通知单才能"+operationName);
		}
	}
	
	/**
	 *  校验订单状态
	 * @param order
	 * @param orderIds
	 */
	private void checkOrderStatus(Order order,Long[] orderIds){
		if(ConvertUtil.isEmpty(order) || (!ConvertUtil.isEmpty(order) && ConvertUtil.isEmpty(order.getId()))){
			ExceptionUtil.throwServiceException("该订单编号不存在");
		}
		if(orderIds[0].longValue() != order.getId().longValue()){
			//订单
			Order pOrder = orderService.find(order.getId());
			if(ConvertUtil.isEmpty(pOrder)){
				ExceptionUtil.throwServiceException("该订单编号不存在");
			}
			if(!ConvertUtil.isEmpty(pOrder.getOrderStatus()) && !pOrder.getOrderStatus().equals(OrderStatus.audited) ){
				ExceptionUtil.throwServiceException("订单编号为【"+pOrder.getSn()+"】的状态必须是已审核的");
			}
			orderIds[0] = pOrder.getId();
		}
	}

	@Override
	@Transactional
	public void setOrderItemFlag(Long[] ids, Integer flag) {
		for (Long id : ids) {
			if (!ConvertUtil.isEmpty(id)) {
				OrderItem orderItem = orderItemService.find(id);
				if (ConvertUtil.isEmpty(orderItem)) {
					continue;
				}
				orderItem.setFlag(flag);
				orderItemService.update(orderItem);
			}
		}
	}
	
	
	/**
	 * 校验库存现有数量
	 */
	private void IsCheckStockOnhandQuantity(List<ShippingItem> shippingItems,
			Warehouse warehouse) {

		List<ShippingItem> shippingItemList = new ArrayList<ShippingItem>();
		for (ShippingItem shippingItem : shippingItems) {
			// 产品
			Product product = shippingItem.getProduct();
			if (ConvertUtil.isEmpty(product)) {
				ExceptionUtil.throwServiceException("产品不能为空");
			}
			//订单明细
			OrderItem orderItem = shippingItem.getOrderItem();
			// 经营组织
			if (ConvertUtil.isEmpty(orderItem.getProductOrganization())) {
				ExceptionUtil.throwServiceException("产品编码为【"+shippingItem.getProduct().getVonderCode()+"】的经营组织不能为空");
			}
			boolean state = false;
			for (ShippingItem shippingItemis : shippingItemList) {
				if (product.equals(shippingItemis.getProduct())
						&& orderItem.getProductOrganization().equals(shippingItemis.getOrderItem().getProductOrganization())) {
					state = true;
					break;
				}
			}
			if (!state) {
				ShippingItem shippingItemis = new ShippingItem();
				// 产品
				shippingItemis.setProduct(product);
				//订单明细
				shippingItemis.setOrderItem(orderItem);
				shippingItemList.add(shippingItemis);
			}
		}
		for (ShippingItem shippingItem : shippingItemList) {
			// 库存现有数量
			BigDecimal stockQuantity = stockService.getStockQuantity(warehouse.getId(),
					shippingItem.getOrderItem().getProductOrganization().getId(), 
					shippingItem.getProduct().getId(), null, null, null, null, null, null, null, 0);
			if (!ConvertUtil.isEmpty(stockQuantity)) {
				if (stockQuantity.compareTo(BigDecimal.ZERO) != 1) {
					ExceptionUtil.throwServiceException("产品编码为【"+shippingItem.getProduct().getVonderCode()+"】没有库存");
				}
			} else {
				ExceptionUtil.throwServiceException("产品编码为【"+shippingItem.getProduct().getVonderCode()+"】没有库存");
			}
		}
	}

	@Override
	public List<Map<String, Object>> findShippingItemList(Long[] Ids) {
		return shippingDao.findShippingItemList(Ids);
	}

	@Override
	public Page<Map<String, Object>> getShippedData(String sn,Long[] statuss,Long[] statusItems, 
			Long storeId,Long sbuId,Long organizationId,Pageable pageable) {
		return shippingDao.getShippedData(sn,statuss,statusItems,storeId,sbuId,organizationId,pageable);
	}
	
	public void savepriceDifference(Shipping shipping){
	//存入ABCD类价格
    List<ShippingItem> shippingItems=shipping.getShippingItems();
    
    Long memberRankId = null;
    
    for(ShippingItem shippingItem:shippingItems) {
        PriceDifference priceDifference = new PriceDifference();
        priceDifference.setShippingSn(shipping.getSn());
        priceDifference.setDrawer(shipping.getStoreMember());
        priceDifference.setSalesman(shipping.getRegionalManager());
        priceDifference.setCreateDate(shippingItem.getCreateDate());
        priceDifference.setSbu(shipping.getSbu());
        priceDifference.setOrganization(shipping.getOrganization());
        priceDifference.setStore(shipping.getStore());
        priceDifference.setWarehouse(shipping.getWarehouse());
        priceDifference.setSaleOrg(shipping.getSaleOrg());
        priceDifference.setType(shippingItem.getOrder().getBusinessType().getValue());
        priceDifference.setProductName(shippingItem.getProduct().getName());
        priceDifference.setVonderCode(shippingItem.getProduct().getVonderCode());
        priceDifference.setProductTwo(shippingItem.getProduct().getProductTwo());
        priceDifference.setCategoryName(shippingItem.getProduct().getProductCategory().getName());
        priceDifference.setWoodTypeOrColor(shippingItem.getProduct().getWoodTypeOrColor());
        priceDifference.setModel(shippingItem.getProduct().getModel());
        priceDifference.setLevelName(shippingItem.getOrderItem().getProductLevel().getValue());
        priceDifference.setProductStructure(shippingItem.getProduct().getProductStructure()==null?null :shippingItem.getProduct().getProductStructure().getValue());
        priceDifference.setBoxQuantity(shippingItem.getBoxQuantity());
        priceDifference.setBranchQuantity(shippingItem.getBranchQuantity());
        priceDifference.setQuantity(shippingItem.getQuantity());
        priceDifference.setSaleOrgPirce(shippingItem.getOrderItem().getSaleOrgPrice());
        priceDifference.setReSaleOrgPirce(shippingItem.getOrderItem().getReSaleOrgPrice());
        priceDifference.setPrice(shippingItem.getPrice());
        priceDifference.setSpec(shippingItem.getProduct().getSpec());
        priceDifference.setKeyCategory(shippingItem.getProduct().getKeyCategory());
        priceDifference.setShippingItem(shippingItem);
        priceDifference.setProductOrgPrice(shippingItem.getOrderItem().getProductOrgPrice());

		priceDifference.setPriceDifference(shippingItem.getOrderItem().getPriceDifference());
		priceDifference.setZxgk(shippingItem.getOrderItem().getZxgk());
		priceDifference.setGkjsjc(shippingItem.getOrderItem().getGkjsjc());
		priceDifference.setZxptjc(shippingItem.getOrderItem().getZxptjc());
		priceDifference.setQtzx(shippingItem.getOrderItem().getQtzx());
		priceDifference.setDiscountProjectDescription(shippingItem.getOrderItem().getDiscountProjectDescription() == null ? null : shippingItem.getOrderItem().getDiscountProjectDescription().getValue());

        MemberRank memberRank = 
        		productPriceService.find(shippingItem.getOrderItem().getPriceId()).getProductPriceHead().getMemberRank();
        
        String rankName = StringUtils.trim(memberRank.getName());
		
		priceDifference.setRankName(rankName);
        
        memberRankId = memberRank.getId();
        
        List<Map<String,Object>> shippedLists = null;
        
        //根据价格类型前缀获取ABCD价格信息,剔除所有空格符合
        String rankNamePrefix = rankName.replaceAll(" ", "");
        
        if(rankName.endsWith("A")
        		||rankName.endsWith("B")
        		||rankName.endsWith("C")
        		||rankName.endsWith("D")) {
        	StringBuffer rankNameSBF = new StringBuffer(rankName);
        	rankNameSBF.setLength(rankName.length()-1);
        	rankNamePrefix = rankNameSBF.toString();
        }
        
        if(memberRank.getRankType() == null) {
        	shippedLists = priceDifferenceService.findShippedList(
        			shipping.getSaleOrg().getId(),
                    shipping.getSbu().getId(),
                    shipping.getWarehouse().getTypeSystemDict().getId(),
                    shippingItem.getOrderItem().getProductLevel().getId(),
                    memberRankId,
                    shippingItem.getProduct().getId(),
                    rankNamePrefix);
        }else {
        	shippedLists = priceDifferenceService.findShippedList(
        			shipping.getSaleOrg().getId(),
					shipping.getSbu().getId(), 
					shipping.getWarehouse().getTypeSystemDict().getId(),
					shippingItem.getOrderItem().getProductLevel().getId(),
					memberRankId, 
					shippingItem.getProduct().getId(),
					memberRank.getRankType().getId(),
					rankNamePrefix);
        }
        
        if(shippedLists.size()>0) {
            for(Map<String,Object> shippedMap:shippedLists) {
                if(shippedMap.get("name").toString().contains("A")) {
                    
                    priceDifference.setaPrice(shippedMap.get("store_member_price")==null ? BigDecimal.ZERO:new BigDecimal(shippedMap.get("store_member_price").toString()));
                }else if(shippedMap.get("name").toString().contains("B")) {
                
                    priceDifference.setbPrice(shippedMap.get("store_member_price")==null ? BigDecimal.ZERO:new BigDecimal(shippedMap.get("store_member_price").toString()));
                }else if(shippedMap.get("name").toString().contains("C")) {
                    
                    priceDifference.setcPrice(shippedMap.get("store_member_price")==null ? BigDecimal.ZERO:new BigDecimal(shippedMap.get("store_member_price").toString()));
                }else if(shippedMap.get("name").toString().contains("D")) {
    
                    priceDifference.setdPrice(shippedMap.get("store_member_price")==null ? BigDecimal.ZERO:new BigDecimal(shippedMap.get("store_member_price").toString()));
                }
                
            }
        }
        
        priceDifferenceService.save(priceDifference);
        
    }
	}
	
	
	public void synchronization(Long[] ids) {

        for(int i=0;i<ids.length;i++) {
        	Long id = ids[i];
        	Shipping shipping = find(id);
        	if(shipping.getStatus()!=3 && shipping.getStatus()!=4) {
        		ExceptionUtil.throwServiceException("只有状态为部分发货或者完全发货才可推送至LINK5");
        	}
        	
        	if(!(shipping.getStore().getIsToLink5()==null ? false : shipping.getStore().getIsToLink5())) {
        		ExceptionUtil.throwServiceException("该客户暂无LINK5权限");
        	}
        	shippingInfoToLink5Service.saveShippingInfoTo(shipping);
        }
}
	
}
