package net.shopxx.order.b2b.controller;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.shopxx.basic.entity.Sbu;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSbu;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.entity.PlanApply;
import net.shopxx.order.service.PlanApplyService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
/**
 * @Description
 * <AUTHOR>
 * @Date2020/7/9 15:14
 * @Place:Beijiao
 * @Version V1.0
 **/
@Controller("planApplyForHeadController")
@RequestMapping("/b2b/planApplyForHead")
public class PlanApplyForHeadController extends BaseController {

	@Resource(name = "planApplyServiceImpl")
	private PlanApplyService planApplyService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer planApplyType, Long menuId,
			ModelMap model) {
		model.addAttribute("planApplyType",planApplyType);
		model.addAttribute("menuId", menuId);
		return "/b2b/plan_apply_for_head/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Integer planApplyType, Long menuId, Long userId,
			ModelMap model) {
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		if(ConvertUtil.isEmpty(planApplyType)){
			model.addAttribute("content", "planApplyType cound not be null");
			return errorV(model, null);
		}
		model.addAttribute("planApplyType",planApplyType);
		return "/b2b/plan_apply_for_head/list";
	}


	@RequestMapping(value = "/checkHasApplied", method = RequestMethod.GET)
	public @ResponseBody ResultMsg checkHasApplied(Integer planApplyType) {
		Map<String, Object> map = new HashMap<String, Object>();
		if(planApplyType!=null && planApplyType==2){
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
			Date date = DateUtils.addMonths(new Date(),1);
			String needDate = format.format(date);
			Long count = planApplyService.count(Filter.eq("planApplyType", planApplyType),
					Filter.eq("needDate", DateUtil.convert(needDate,"yyyy-MM")),
					Filter.ne("status",2) );
			System.out.println("count==" + count);
//			model.addAttribute("hasAppliedCount", count);//总部已提报数，用于限制每月一单
//			model.addAttribute("mydate",needDate );

			if (count != null && count >= 1) { //总部已经提报至少一单，限制不能继续提报
				map.put("hasApplied", true);
				map.put("mydate", needDate);
			} else {
				map.put("hasApplied", false);
			}
		} else{
			map.put("hasApplied", false);
		}
		return success().addObjX(map);
	}


	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Integer planApplyType, ModelMap model) {
		//获取ModelMap
		planApplyService.getModelMap(model,planApplyType,"planApplyBusinessType",
				"factory",null,"productDictRoles","moistureContent");
        //查询用户所包含的所有sbu
        List<Filter> filters = new ArrayList<Filter>();
        StoreMember storeMember = storeMemberService.getCurrent();
        filters.add(Filter.eq("storeMember", storeMember.getId()));
        List<StoreMemberSbu> storeMemberSbus = storeMemberSbuService.findList(null,
                filters,
                null);
        List<Sbu> sbuList = new ArrayList<Sbu>();
        if(storeMemberSbus!=null && storeMemberSbus.size()>0){
            for( StoreMemberSbu  sms: storeMemberSbus){
                sbuList.add(sms.getSbu());
            }
        }
        model.addAttribute("sbuList", sbuList);
		return "/b2b/plan_apply_for_head/add";
	}


	/*
	 * 查看
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id,ModelMap model) {
		PlanApply planApply = planApplyService.find(id);
		model.addAttribute("planApply", planApply);
		//获取ModelMap
		planApplyService.getModelMap(model,planApply.getPlanApplyType(),
				"planApplyBusinessType","factory",2,"productDictRoles","moistureContent");
		List<Map<String, Object>> mapList = planApplyService.findItemById(id.toString());
		model.addAttribute("mapList", JsonUtils.toJson(mapList));

        //查询用户所包含的所有sbu
        List<Filter> filters = new ArrayList<Filter>();
        StoreMember storeMember = storeMemberService.getCurrent();
        filters.add(Filter.eq("storeMember", storeMember.getId()));
        List<StoreMemberSbu> storeMemberSbus = storeMemberSbuService.findList(null,
                filters,
                null);
        List<Sbu> sbuList = new ArrayList<Sbu>();
        if(storeMemberSbus!=null && storeMemberSbus.size()>0){
            for( StoreMemberSbu  sms: storeMemberSbus){
                sbuList.add(sms.getSbu());
            }
        }
        model.addAttribute("sbuList", sbuList);
		return "/b2b/plan_apply_for_head/edit";
	}



	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
		PlanApply planApply = planApplyService.find(id);
		if (planApply.getStatus() != 0) {
			return error("只有单据状态为已保存计划提报单才能审批流程");
		}
		planApplyService.createWf(id, modelId, objTypeId);
		return success();
	}

	/**
	 * Excel导入
	 * 
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file, HttpServletResponse response,
			ModelMap model) throws Exception {

		try {
			planApplyService.planApplyImport(file, null);
			return ResultMsg.success();
		}
		catch (Exception e) {
			LogUtils.error("导入计划提报", e);
			return ResultMsg.error(e.getMessage());
		}
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String sn, String firstTime,
			String lastTime, Long saleOrgId, String month, Long storeId, Integer planApplyType,
			String areaName, String creator,Long sbuId, Pageable pageable, ModelMap model,Integer[] status) {
		Object[] con = { creator };
		Integer size = planApplyService.count(sn,
				firstTime,
				lastTime,
				saleOrgId,
				storeId,
				month,
				areaName,
				con,
				null,
				sbuId,
				null,
				null,
				planApplyType,
				status);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @param--saleOrgName
	 * @param-- storeName
	 * @param-- pmodel
	 * @param --vonderCode
	 * @param --firstTime
	 * @param --lastTime
	 * @param --pageable
	 * @param --model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, String firstTime,
			String lastTime, Long saleOrgId, String month, Long storeId,Integer planApplyType,
			String areaName, String creator,Long sbuId, Integer page, Pageable pageable,
			ModelMap model,Integer[] status) {
		Object[] con = { creator };
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = planApplyService.findList(sn,
				firstTime,
				lastTime,
				saleOrgId,
				storeId,
				month,
				areaName,
				con,
				null,
				null,
				page,
				size,
				sbuId,
				false,
				planApplyType,
				status);
		Product product = null;
		BigDecimal quantity = new BigDecimal("0");
		for (Map<String, Object> map : data) {
			Integer stat = (Integer) map.get("status");
			if (stat == 0) {
				map.put("status", "制作中");
			}else if (stat == 1) {
				map.put("status", "已确认");
			}else if (stat == 2) {
				map.put("status", "已作废");
			}

			Boolean is_pushed_to_erp = (Boolean) map.get("is_pushed_to_erp");
			if (is_pushed_to_erp !=null && is_pushed_to_erp) {
				map.put("is_pushed_to_erp", "已成功同步");
			}else  {
				map.put("is_pushed_to_erp", "待同步");
			}

			if (map.get("need_date") != null) {
				map.put("need_date",
						map.get("need_date").toString().substring(0, 7));
			}
			if (map.get("create_date") != null) {
				map.put("create_date",
						map.get("create_date").toString().substring(0, 19));
			}
			if (map.get("proNeedDate") != null) {
				map.put("proNeedDate",
						map.get("proNeedDate").toString().substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ "总部提报单导出.xls";
		// 设置标题
		String[] header = { "要货单号",
				"单据状态",
				"机构",
				"要货月份",
				"要货总数量(㎡)",
				"同步ERP情况",
				"创建人",
				"产品编码",
				"产品描述",
				"产品名称",
				"sbu",
				"工厂",
				"业务类型",
				"要货平方数",
				//"纸张数",
				"要货日期",
		"创建时间" };
		// 设置单元格取值
		String[] properties = { "sn",
				"status",
				"sale_org_name",
				"need_date",
				"total_quantity",
				"is_pushed_to_erp",
				"store_member_name",
				"proVc",
				"description",
				"proName",
				"sbu_name",
				"factory_value",
				"business_type_value",
				"proQuantity",
				"proNeedDate",
				"create_date" };
		Integer[] widths = { 4000,
				4000,
				4000,
				4000,
				4000,
				4000,
				4000,
				8000,
				8000,
				4000,
				4000,
				8000,
				4000,
				4000,
				4000,
				4000 };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model,Integer planApplyType,Integer[] status) {

		List<Map<String, Object>> data = planApplyService.findList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null,
				null,
				true,
				planApplyType,
				status);
		Product product = null;
		BigDecimal quantity = new BigDecimal("0");
		for (Map<String, Object> map : data) {
			/*if (map.get("pId") != null && map.get("proQuantity") != null) {
				quantity = new BigDecimal(map.get("proQuantity").toString());
				product = productBaseService.find(Long.valueOf(map.get("pId")
						.toString()));
				if (quantity != null
						&& product != null
						&& product.getPaperNum() != null) {
					map.put("paper", quantity.divide(product.getPaperNum(),
							2,
							BigDecimal.ROUND_HALF_UP));
				}
			}*/
			Integer stat = (Integer) map.get("status");
			if (stat == 0) {
				map.put("status", "制作中");
			}else if (stat == 1) {
				map.put("status", "已确认");
			}else if (stat == 2) {
				map.put("status", "已作废");
			}

			Boolean is_pushed_to_erp = (Boolean) map.get("is_pushed_to_erp");
			if (is_pushed_to_erp !=null && is_pushed_to_erp) {
				map.put("is_pushed_to_erp", "已成功同步");
			}else  {
				map.put("is_pushed_to_erp", "待同步");
			}

			if (map.get("need_date") != null) {
				map.put("need_date",
						map.get("need_date").toString().substring(0, 7));
			}
			if (map.get("create_date") != null) {
				map.put("create_date",
						map.get("create_date").toString().substring(0, 19));
			}
			if (map.get("proNeedDate") != null) {
				map.put("proNeedDate",
						map.get("proNeedDate").toString().substring(0, 19));
			}
		}

		String exportData =  JsonUtils.toJson(data);
		System.out.println("exportData==" + exportData);
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ "总部提报单导出.xls";
		// 设置标题
		String[] header = { "要货单号",
				"单据状态",
				"机构",
				"要货月份",
				"要货总数量(㎡)",
				"同步ERP情况",
				"创建人",
				"产品编码",
				"产品描述",
				"产品名称",
				"sbu",
				"工厂",
				"业务类型",
				"要货平方数",
				"要货日期",
				"创建时间" };
		// 设置单元格取值
		String[] properties = { "sn",
				"status",
				"sale_org_name",
				"need_date",
				"total_quantity",
				"is_pushed_to_erp",
				"store_member_name",
				"proVc",
				"description",
				"proName",
				"sbu_name",
				"factory_value",
				"business_type_value",
				"proQuantity",
				"proNeedDate",
				"create_date" };
		Integer[] widths = { 4000,
				4000,
				4000,
				4000,
				4000,
				4000,
				4000,
				8000,
				8000,
				4000,
				4000,
				8000,
				4000,
				4000,
				4000,
				4000 };
		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}


	@RequestMapping(value = "/confirm", method = RequestMethod.POST)
	public @ResponseBody ResultMsg confirm(Long[] ids) {
//		if(planApplyService.isEnabledToAddOrEdit(0) || planApplyService.isEnabledToAddOrEdit(1) ) {
//			ExceptionUtil.throwServiceException("总部不可以在经销商或平台提报周期内修改单据状态！");
//		}

		if (ids == null || ids.length == 0) {
			// 请选择计划提报
			return error("请选择计划提报");
		}
		planApplyService.confirmPlanApply(ids);

		return success();
	}

}
