package net.shopxx.order.b2b.controller;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.shopxx.basic.entity.Sbu;
import net.shopxx.member.entity.StoreMemberSbu;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.dao.impl.NativeDao;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.entity.PlanApply;
import net.shopxx.order.service.PlanApplyService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
/**
 * 计划汇总
 * 
 * <AUTHOR>
 *
 */
@Controller("b2bPlanApplyController")
@RequestMapping("/b2b/planApply")
public class PlanApplyController extends BaseController {

	@Resource(name = "planApplyServiceImpl")
	private PlanApplyService planApplyService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;


	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long menuId,ModelMap model) {
		model.addAttribute("menuId", menuId);
		return "/b2b/planApply/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Long menuId, Long userId, ModelMap model)  {
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		boolean enabledToAddOrEdit = planApplyService.isEnabledToAddOrEdit(1);
		model.addAttribute("isEnabledToAddOrEdit",enabledToAddOrEdit);
		String memo = "";
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || (!ConvertUtil.isEmpty(storeMember.getIsSalesman()) && storeMember.getIsSalesman())) {
			memo = " and sm.id = "+storeMember.getId();
		}else{
			memo = " and so.id in ("+roleJurisdictionUtil.getSaleOrgIds()+") ";
		}
		model.addAttribute("memo",memo);
		return "/b2b/planApply/list";
	}

	@RequestMapping(value = "/checkSkipMaking", method = RequestMethod.GET)
	public @ResponseBody ResultMsg checkSkipMaking(Integer planApplyType) {
		//获取当前登陆人的默认机构和默认客户
		StoreMember storeMember = storeMemberService.getCurrent();
		Map<String,Object>  infoMap = planApplyService.getUserInfoMap(storeMember);
		SaleOrg saleOrg = infoMap.get("saleOrg")==null?null:(SaleOrg) infoMap.get("saleOrg");
		Store store = infoMap.get("store")==null?null:(Store) infoMap.get("store");

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
		Date date = DateUtils.addMonths(new Date(),1);
		String needDate = format.format(date);
		Long count = planApplyService.count(Filter.eq("planApplyType", planApplyType),
				Filter.eq("needDate", DateUtil.convert(needDate,"yyyy-MM")),
				Filter.eq("status",0), //制单状态
				Filter.eq("saleOrg",saleOrg));
		System.out.println("pingtai making count==" + count);
		Map<String, Object> map = new HashMap<String, Object>();
		if (count != null && count >= 1) { //当前平台下已经有至少一张制作中的单据，不允许继续提报
			map.put("makingOverOne", true);
			map.put("mydate", needDate);
			map.put("saleOrg", saleOrg.getName());
		} else {
			map.put("makingOverOne", false);
		}
		return success().addObjX(map);

//		model.addAttribute("hasAppliedCount", count);//当前机构下已提交数，用于限制每月一单
//		model.addAttribute("mydate", needDate);
	}

	@RequestMapping(value = "/checkHasApplied", method = RequestMethod.GET)
	public @ResponseBody ResultMsg checkHasApplied() {
		//获取当前登陆人的默认机构
		StoreMember storeMember = storeMemberService.getCurrent();
		StoreMemberSaleOrg storeMemberSaleOrg = null;
		SaleOrg saleOrg = null;
		if (storeMember.getMemberType() != 1) {// 企业用户
			storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null ) {
				saleOrg = storeMemberSaleOrg.getSaleOrg();
			}
		}else if (storeMember.getMemberType() == 1) {
			NativeDao nativeDao = storeMemberService.getDaoCenter()
					.getNativeDao();
			StringBuilder sql = new StringBuilder();
			sql.append("select s.* from xx_store_member sm, xx_store s");
			sql.append(" where sm.store = s.id");
			sql.append(" and s.is_enabled = 1 and s.is_main_store = 0");
			sql.append(" and sm.username = ?");
			Object[] objs = null;
			if (WebUtils.getCurrentCompanyInfoId() != null) {
				sql.append(" and sm.company_info_id = ?");
				objs = new Object[] { storeMember.getUsername(),
						WebUtils.getCurrentCompanyInfoId() };
			}
			else {
				sql.append(" and sm.company_info_id is null");
				objs = new Object[] { storeMember.getUsername() };
			}

			Store store = nativeDao.findSingleManaged(sql.toString(),
					objs,
					Store.class);
			saleOrg = store == null ? null : store.getSaleOrg();
		}
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
		Date date = DateUtils.addMonths(new Date(),1);
		String needDate = format.format(date);
		Long count = planApplyService.count(Filter.eq("planApplyType", 1),
				Filter.eq("needDate", DateUtil.convert(needDate,"yyyy-MM")),
				Filter.ne("status",2),
				Filter.eq("saleOrg",saleOrg));
		System.out.println("pingtai count==" + count);
		Map<String, Object> map = new HashMap<String, Object>();
		if (count != null && count >= 1) { //当前平台下已经提报至少一单，限制不能继续提报
			map.put("hasApplied", true);
			map.put("mydate", needDate);
			map.put("saleOrg", saleOrg.getName());
		} else {
			map.put("hasApplied", false);
		}
		return success().addObjX(map);

//		model.addAttribute("hasAppliedCount", count);//当前机构下已提交数，用于限制每月一单
//		model.addAttribute("mydate", needDate);
	}


	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model,Integer isZeroPlan) {
		if(isZeroPlan!=null) {
			model.addAttribute("isZeroPlan", isZeroPlan);
		}
		SaleOrg saleOrg = null;
		StoreMember storeMember = storeMemberService.getCurrent();
		//否则按企业用户或非企业用户获取默认机构
		if (storeMember.getMemberType() != 1) {
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (!ConvertUtil.isEmpty(storeMemberSaleOrg) &&  !ConvertUtil.isEmpty(storeMemberSaleOrg.getSaleOrg())) {
				saleOrg = storeMemberSaleOrg.getSaleOrg();
			}
		}else if (storeMember.getMemberType() == 1) {
			NativeDao nativeDao = storeMemberService.getDaoCenter().getNativeDao();
			StringBuilder sql = new StringBuilder();
			sql.append("select s.* from xx_store_member sm, xx_store s");
			sql.append(" where sm.store = s.id");
			sql.append(" and s.is_enabled = 1 and s.is_main_store = 0");
			sql.append(" and sm.username = ?");
			Object[] objs = null;
			if (!ConvertUtil.isEmpty(WebUtils.getCurrentCompanyInfoId())) {
				sql.append(" and sm.company_info_id = ?");
				objs = new Object[] { storeMember.getUsername(),WebUtils.getCurrentCompanyInfoId() };
			}else {
				sql.append(" and sm.company_info_id is null");
				objs = new Object[] { storeMember.getUsername() };
			}
			Store store = nativeDao.findSingleManaged(sql.toString(),objs,Store.class);
			if (!ConvertUtil.isEmpty(store) &&  !ConvertUtil.isEmpty(store.getSaleOrg())) {
				saleOrg = store.getSaleOrg();
			}
		}
		model.addAttribute("saleOrg", saleOrg);
		//获取ModelMap
		planApplyService.getModelMap(model,1,"planApplyBusinessType",null,null,"productDictRoles",
				"moistureContent");
		//查询用户所包含的所有sbu
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> storeMemberSbus = storeMemberSbuService.findList(null,
				filters,
				null);
		List<Sbu> sbuList = new ArrayList<Sbu>();
		if(storeMemberSbus!=null && storeMemberSbus.size()>0){
			for( StoreMemberSbu  sms: storeMemberSbus){
				sbuList.add(sms.getSbu());
			}
		}
		model.addAttribute("sbuList", sbuList);
		return "/b2b/planApply/add";
	}



	@RequestMapping(value = "/selectAgentPage", method = RequestMethod.GET)
	public String selectAgentPage(Long saleOrgId,String month,Integer multi,
			ModelMap model) {
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("month", month);
		model.addAttribute("multi", multi);
		
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		// 业务类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("code", "planApplyBusinessType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<String> lowerCodes = new ArrayList<String>();
		lowerCodes.add("businessFloor");
		lowerCodes.add("retail");
		filters.add(Filter.in("lowerCode",lowerCodes));
		List<SystemDict> businessTypeList = systemDictBaseService.findList(null,filters,null);
		if(!businessTypeList.isEmpty() && businessTypeList.size() > 0){
			for (int i = 0; i < businessTypeList.size(); i++) {
				SystemDict businessType = (SystemDict)businessTypeList.get(i);
				if(!ConvertUtil.isEmpty(businessType.getLowerCode()) && businessType.getLowerCode().equals("retail")){
					Collections.swap(businessTypeList, 0, i);
				}
			}
		}
		model.addAttribute("businessTypes", businessTypeList);
			
		return "/b2b/planApply/select_agent_page";
	}

    @RequestMapping(value = "/findAgentData", method = RequestMethod.POST)
    public  @ResponseBody ResultMsg findAgentData(Long saleOrgId,String month,
			String name,String vonderCode,Long businessTypeId){
        List<Map<String, Object>> mapList = planApplyService.findAgentData(saleOrgId,month,
        		name,vonderCode,businessTypeId);
        return success(JsonUtils.toJson(mapList));
    }

	/**
	 * 查询经销商客户页面
	 */
	@RequestMapping(value = "/selectAgentStore", method = RequestMethod.GET)
	public String selectStore(Pageable pageable, Integer multi, Store.Type type,
							    Long saleOrgId, Integer isSelect, Integer isCustomer,String user, ModelMap model) {

		model.addAttribute("multi", multi);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("isSelect", isSelect);
		model.addAttribute("isSelect", isSelect);
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		return "/b2b/planApply/select_agent_store";
	}



	/**
	 * 经销商客户数据
	 */
	@RequestMapping(value = "/select_agent_store_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectStoreData(String name, String outTradeNo, Pageable pageable,ModelMap model) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		//获取当前登陆人的默认机构和默认客户
		StoreMember storeMember = storeMemberService.getCurrent();
		Map<String,Object>  infoMap = planApplyService.getUserInfoMap(storeMember);
		SaleOrg saleOrg = infoMap.get("saleOrg")==null?null:(SaleOrg) infoMap.get("saleOrg");

		List<Map<String, Object>> agentStoreList = planApplyService.findAgentStoreList(name,
				outTradeNo, saleOrg);

		String jsonStr = JsonUtils.toJson(agentStoreList);
		return success(jsonStr);
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(PlanApply planApply,Long saleOrgId,Long storeId,
			Long customerProjectId,Long mainFactoryId,Long sbuId,Integer isZeroPlan) {
	    if(isZeroPlan == null) {
            if (planApply.getPlanApplyItems().isEmpty() || planApply.getPlanApplyItems().size() == 0) {
                return error("请添加要货明细 ");
            }
        }
        planApplyService.saveOrUpdatePlanApply(planApply, saleOrgId, storeId,
                customerProjectId, mainFactoryId, sbuId, isZeroPlan);
        return success().addObjX(planApply.getId());
	}


	/**
	 * 查看
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {

		PlanApply planApply = planApplyService.find(id);
		model.addAttribute("planApply", planApply);
		//获取ModelMap
		planApplyService.getModelMap(model,planApply.getPlanApplyType(),
				"planApplyBusinessType",null,1,"productDictRoles","moistureContent");
		List<Map<String, Object>> mapList = planApplyService.findItemById(id.toString());
		model.addAttribute("mapList", JsonUtils.toJson(mapList));
		//当前时间是否在1-15号以内
		model.addAttribute("duringHalfMonth", net.shopxx.util.DateUtils.duringHalfMonth());

		//查询用户所包含的所有sbu
		List<Filter> filters = new ArrayList<Filter>();
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> storeMemberSbus = storeMemberSbuService.findList(null,
				filters,
				null);
		List<Sbu> sbuList = new ArrayList<Sbu>();
		if(storeMemberSbus!=null && storeMemberSbus.size()>0){
			for( StoreMemberSbu  sms: storeMemberSbus){
				sbuList.add(sms.getSbu());
			}
		}
		model.addAttribute("sbuList", sbuList);

		return "/b2b/planApply/edit";
	}

//	@RequestMapping(value = "/getMonthSummary", method = RequestMethod.POST)
//	public @ResponseBody ResultMsg getMonthSummary(Long saleOrgId,
//			String month) {
//		if (saleOrgId == null) {
//			// 请选择客户
//			return error("请维护机构");
//		}
//
//		if (month == null) {
//			return error("请维护年月");
//		}
//
//		List<Map<String, Object>> list = planSummaryService
//				.findMonthSummary(month, saleOrgId);
//
//		String json = JsonUtils.toJson(list);
//		return success(json);
//	}

//	@RequestMapping(value = "check_wf", method = RequestMethod.POST)
//	public @ResponseBody
//	ResultMsg check_wf(Long id, Long objConfId) {
//		planApplyService.check(id, objConfId);
//		return success();
//	}
	
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
		PlanApply planApply = planApplyService.find(id);
		if (planApply.getStatus() != 0) {
			return error("只有单据状态为已保存计划提报单才能审批流程");
		}
		planApplyService.createWf(id, modelId, objTypeId);
		return success();
	}

	/**
	 * Excel导入
	 * 
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file, HttpServletResponse response,
			ModelMap model) throws Exception {

		try {
			planApplyService.planApplyImport(file, null);
			return ResultMsg.success();
		}
		catch (Exception e) {
			LogUtils.error("导入计划提报", e);
			return ResultMsg.error(e.getMessage());
		}
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String sn, String firstTime,
			String lastTime, Long saleOrgId, String month, Long storeId,Integer	planApplyType,
			String areaName, String creator,Long sbuId, Pageable pageable, ModelMap model,Integer[] status) {
		Object[] con = { creator };
		Integer size = planApplyService.count(sn,
				firstTime,
				lastTime,
				saleOrgId,
				storeId,
				month,
				areaName,
				con,
				null,
				sbuId,
				null,
				null,
				planApplyType,
                status);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @param
	 * @param
	 * @param
	 * @param
	 * @param firstTime
	 * @param lastTime
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, String firstTime,
			String lastTime, Long saleOrgId, String month, Long storeId,Integer	planApplyType,
			String areaName, String creator,Long sbuId, Integer page, Pageable pageable,
			ModelMap model,Integer[] status) {
		Object[] con = { creator };
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = planApplyService.findList(sn,
				firstTime,
				lastTime,
				saleOrgId,
				storeId,
				month,
				areaName,
				con,
				null,
				null,
				page,
				size,
				sbuId,
                false,
				planApplyType,
                status);
		Product product = null;
		BigDecimal quantity = new BigDecimal("0");
		for (Map<String, Object> map : data) {

			Integer stat = (Integer) map.get("status");
			if (stat == 0) {
				map.put("status", "制作中");
			}else if (stat == 1) {
				map.put("status", "已确认");
			}else if (stat == 2) {
				map.put("status", "已作废");
			}

			if (map.get("need_date") != null) {
				map.put("need_date",
						map.get("need_date").toString().substring(0, 7));
			}
			if (map.get("create_date") != null) {
				map.put("create_date",
						map.get("create_date").toString().substring(0, 19));
			}
			if (map.get("proNeedDate") != null) {
				map.put("proNeedDate",
						map.get("proNeedDate").toString().substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ "平台提报单导出.xls";
		// 设置标题
		String[] header = { "要货单号",
				"单据状态",
				"机构",
				"要货月份",
				"要货总数量(㎡)",
				"创建人",
				"产品编码",
				"产品描述",
				"产品名称",
				"sbu",
				"业务类型",
				"要货平方数",
				"要货日期",
				"创建时间" };
		Integer[] widths = { 4000,
				4000,
				4000,
				4000,
				4000,
				4000,
				8000,
				8000,
				4000,
				4000,
				4000,
				4000,
				4000,
				4000 };

		// 设置单元格取值
		String[] properties = { "sn",
				"status",
				"sale_org_name",
				"need_date",
				"total_quantity",
				"store_member_name",
				"proVc",
				"description",
				"proName",
				"sbu_name",
				"business_type_value",
				"proQuantity",
				"proNeedDate",
				"create_date" };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model,Integer planApplyType,Integer[] status) {

		List<Map<String, Object>> data = planApplyService.findList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null,
				null,
                true,
				planApplyType,
                status);
		Product product = null;
		BigDecimal quantity = new BigDecimal("0");
		for (Map<String, Object> map : data) {

			Integer stat = (Integer) map.get("status");
			if (stat == 0) {
				map.put("status", "制作中");
			}else if (stat == 1) {
				map.put("status", "已确认");
			}else if (stat == 2) {
				map.put("status", "已作废");
			}

			if (map.get("need_date") != null) {
				map.put("need_date",
						map.get("need_date").toString().substring(0, 7));
			}
			if (map.get("create_date") != null) {
				map.put("create_date",
						map.get("create_date").toString().substring(0, 19));
			}
			if (map.get("proNeedDate") != null) {
				map.put("proNeedDate",
						map.get("proNeedDate").toString().substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ "平台提报单导出.xls";
		// 设置标题
		String[] header = { "要货单号",
				"单据状态",
				"机构",
				"要货月份",
				"要货总数量(㎡)",
				"创建人",
				"产品编码",
				"产品描述",
				"产品名称",
				"sbu",
				"业务类型",
				"要货平方数",
				"要货日期",
				"创建时间" };

		Integer[] widths = { 4000,
				4000,
				4000,
				4000,
				4000,
				4000,
				8000,
				8000,
				4000,
				4000,
				4000,
				4000,
				4000,
				4000 };
		// 设置单元格取值
		String[] properties = { "sn",
				"status",
				"sale_org_name",
				"need_date",
				"total_quantity",
				"store_member_name",
				"proVc",
				"description",
				"proName",
				"sbu_name",
				"business_type_value",
				"proQuantity",
				"proNeedDate",
				"create_date" };
		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}

	
	@RequestMapping(value = "/confirm", method = RequestMethod.POST)
	public @ResponseBody ResultMsg confirm(Long id) {
		PlanApply planApply = planApplyService.find(id);
		if(ConvertUtil.isEmpty(planApply)){
			return error("计划提报不存在");
		}
		if(ConvertUtil.isEmpty(planApply.getStatus()) || planApply.getStatus() != 0){
			return error("只有单据状态为制作中的计划提报单才能确认");
		}
		planApplyService.confirmplanApply(planApply);
		return success();
	}
	
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancel(Long id) {
		PlanApply planApply = planApplyService.find(id);
		if(ConvertUtil.isEmpty(planApply)){
			return error("计划提报不存在");
		}
		if(ConvertUtil.isEmpty(planApply.getStatus()) || 
				(!(planApply.getStatus() ==1 && !planApply.getIsPushedToErp()))){
			return error("只有单据状态为制作中或已确认，但未同步erp的计划提报单才能作废");
		}
		planApplyService.cancelplanApply(planApply);
		return success();
	}
	
	
	@RequestMapping(value = "/goBack", method = RequestMethod.POST)
	public @ResponseBody ResultMsg goBack(Long id) {
		PlanApply planApply = planApplyService.find(id);
		if(ConvertUtil.isEmpty(planApply)){
			return error("计划提报不存在");
		}
		if(ConvertUtil.isEmpty(planApply.getStatus()) || planApply.getStatus() != 1){
			return error("只有单据状态为已确认的计划提报单才能撤销确认");
		}
		planApplyService.goBack(planApply);
		return success();
	}
	
	@RequestMapping(value = "/pushToErp", method = RequestMethod.POST)
	public @ResponseBody ResultMsg pushToErp(Long[] ids) {
		if(!ConvertUtil.isEmpty(ids) && ids.length > 0){
			for(Long id : ids){
				PlanApply planApply = planApplyService.find(id);
				if(!ConvertUtil.isEmpty(planApply) && planApply.getPlanApplyType() != 0){
					if(planApply.getStatus() != 1){
						ExceptionUtil.throwServiceException("要货单号为【"+planApply.getSn()+"】的提报单状态不是已确认，不允许推送至ERP");
					}
					if(planApply.getPushErpStatus() == 2){
						ExceptionUtil.throwServiceException("要货单号为【"+planApply.getSn()+ "】的提报单同步中，禁止重复操作");
					}
					if(planApply.getPushErpStatus() == 1){
						ExceptionUtil.throwServiceException("要货单号为【"+planApply.getSn()+ "】的提报单同步已完成，禁止重复操作");
					}
					Long companyInfoId = planApply.getCompanyInfoId();
					CompanyInfo companyInfo = companyInfoService.find(companyInfoId);
					if ("dzrjj".equals(companyInfo.getCompany_code()) || "nature".equals(companyInfo.getCompany_code())) {
						// 大自然发货接口
						intfOrderMessageToService.savePlanApplyIntf(planApply);
					}
				}
			}
		}
		return success();
	}
}
