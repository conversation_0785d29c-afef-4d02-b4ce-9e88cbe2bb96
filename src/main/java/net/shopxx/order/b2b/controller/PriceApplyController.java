package net.shopxx.order.b2b.controller;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.annotation.Resource;

import net.shopxx.member.entity.*;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.entity.PriceApply;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.PriceApplyService;
import net.shopxx.order.service.TwContractAttachService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
@Controller("b2bPriceApplyController")
@RequestMapping("/b2b/priceApply")
public class PriceApplyController extends BaseController {

	@Resource(name = "priceApplyServiceImpl")
	private PriceApplyService priceApplyService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "twContractAttachServiceImpl")
	private TwContractAttachService twContractAttachService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
	
	
	/*
	 * 列表
	 */
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code,Long sbuId,Long menuId,
			Long objid,ModelMap model) {
		model.addAttribute("code", code);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("menuId", menuId);
		model.addAttribute("objid", objid);
		return CommonUtil.getFolderPrefix(code) + "/b2b/priceApply/list_tb";
	}
	
	
	/*
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Long sbuId, Long userId, 
			Long menuId, ModelMap model) {

		model.addAttribute("code", code);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("menuId", menuId);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return CommonUtil.getFolderPrefix(code) + "/b2b/priceApply/list";
	}

	

	/*
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn, Long storeMemberId, Integer[] docStatus,
			Long storeId, Long productId, String firstTime, String lastTime,
			Pageable pageable, Long sbuId,String createTimeOne,String createTimeTwo, 
			Long saleOrgId,ModelMap model) {
		Page<Map<String, Object>> page = priceApplyService.findPage(sn,
				docStatus,
				storeMemberId,
				storeId,
				productId,
				firstTime,
				lastTime,
				sbuId,
				createTimeOne,
				createTimeTwo,
				saleOrgId,
				pageable);

		List<Map<String, Object>> applys = page.getContent();

		if (!applys.isEmpty()) {
			String ids = "";
			for (int i = 0; i < applys.size(); i++) {
				Map<String, Object> map = applys.get(i);
				if (i == applys.size() - 1) {
					ids += map.get("id");
				}
				else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> applyItems = priceApplyService.findItemListByApplyIds(ids);
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : applys) {
				items = new ArrayList<Map<String, Object>>();
				String applyId = map.get("id").toString();
				for (Map<String, Object> itemMap : applyItems) {
					String pid = itemMap.get("price_apply").toString();
					if (applyId.equals(pid)) {
						items.add(itemMap);
					}
				}
				map.put("apply_items", items);
			}
		}
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "specialType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> specialType = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("specialTypes", specialType);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/*
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add(@PathVariable String code, Long sbuId, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> shippingWarehouses = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("shippingWarehouses", shippingWarehouses);

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("isMember",storeMember.getMemberType());
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		Store store = null;
		Member member = storeMemberService.getCurrent().getMember();
		List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
		if (storeMembers != null) {
			for (StoreMember sm : storeMembers) {
				store = sm.getStore();
				if (store.getType().equals(Store.Type.distributor)) {
					model.addAttribute("store", store);
					break;
				}
				else {
					store = null;
				}
			}
		}

		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);
		model.addAttribute("code", code);
		List<SystemDict> specialType = roleJurisdictionUtil.getSystemDictList("specialType",null);
		model.addAttribute("specialTypes", specialType);
		
		/**
		 * 用户经营组织权限
		 */
		List<Organization> organizationList = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("organizationList", organizationList);
		
		//产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
		model.addAttribute("productLevelList", productLevelList);

        filters.clear();
        filters.add(Filter.eq("storeMember",
                storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
        List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
                filters,
                null);
        try {
            String value = SystemConfig.getConfig("editProductOrgPrices",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> list = Arrays.asList(role);
            int editProductOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    editProductOrgPrice++;
                    break;
                }
            }
            // 角色是否能修改产品部价格  1 可修改     非1  不可修改
            model.addAttribute("editProductOrgPrice", editProductOrgPrice);
        }
        catch (RuntimeException e) {
        }

        // 角色是否能修改平台产品价格  1 可修改     非1  不可修改
        try {
            String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> listRole = Arrays.asList(role);
            int editSaleOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (listRole.contains(userRole.getPcRole().getName())) {
                    editSaleOrgPrice++;
                    break;
                }
            }
            model.addAttribute("editSaleOrgPrice", editSaleOrgPrice);
        }
        catch (RuntimeException e) {
        }

        // 角色是否能查看平台产品价格  1 可修改     非1  不可修改
        try {
            String value = SystemConfig.getConfig("seeSaleOrgPriceRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> lists = Arrays.asList(role);
            int seeSaleOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (lists.contains(userRole.getPcRole().getName())) {
                    seeSaleOrgPrice++;
                    break;
                }
            }
            model.addAttribute("seeSaleOrgPrice", seeSaleOrgPrice);
        }
        catch (RuntimeException e) {
        }

		return CommonUtil.getFolderPrefix(code) + "/b2b/priceApply/add";
	}

	/*
	 * 编辑
	 */
	@SuppressWarnings("null")
	@RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
	public String edit(@PathVariable String code, Long id, String sn,
			Integer isCheck, ModelMap model) {
		List<Filter> filters = new ArrayList<Filter>();
		model.addAttribute("isCheck", isCheck);
		PriceApply priceApply = new PriceApply();
		if (id != null) {
			priceApply = priceApplyService.find(id);
			model.addAttribute("priceApply", priceApply);
			List<Map<String, Object>> list = priceApplyService.findItemListByApplyIds(id.toString());
			
			String jsonStr = JsonUtils.toJson(priceApplyService.findOrderItem(list));
			model.addAttribute("jsonStr", jsonStr);
		}else {
			filters.clear();
			filters.add(Filter.eq("sn", sn));
			priceApply = priceApplyService.find(filters);
			if (priceApply != null) {
				model.addAttribute("priceApply", priceApply);
				id = priceApply.getId();
				List<Map<String, Object>> list = priceApplyService.findItemListByApplyIds(priceApply.getId()
						.toString());
				
				String jsonStr = JsonUtils.toJson(list);
				model.addAttribute("jsonStr", jsonStr);
			}
		}
		priceApply.getId();
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(42L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "shippingWarehouse"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> shippingWarehouses = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("shippingWarehouses", shippingWarehouses);

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("isMember",storeMember.getMemberType());
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}

		/* 附件 */
		String twContractAttach_json = JsonUtils.toJson(twContractAttachService.findListByItemId(null,
				id));
		model.addAttribute("twContractAttach_json", twContractAttach_json);

		/* 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(priceApply.getSn(),
				3));
		model.addAttribute("fullLink_json", fullLink_json);


		model.addAttribute("code", code);

		List<SystemDict> specialType = roleJurisdictionUtil.getSystemDictList("specialType",null);
		model.addAttribute("specialTypes", specialType);
		
		/**
		 * 用户经营组织权限
		 */
		List<Organization> organizationList = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("organizationList", organizationList);
		
		//产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
		model.addAttribute("productLevelList", productLevelList);

        filters.clear();
        filters.add(Filter.eq("storeMember",
                storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
        List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
                filters,
                null);
        try {
            String value = SystemConfig.getConfig("editProductOrgPrices",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> list = Arrays.asList(role);
            int editProductOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    editProductOrgPrice++;
                    break;
                }
            }
            // 角色是否能修改产品部价格  1 可修改     非1  不可修改
            model.addAttribute("editProductOrgPrice", editProductOrgPrice);
        }
        catch (RuntimeException e) {
        }

        // 角色是否能修改平台产品价格  1 可修改     非1  不可修改
        try {
            String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> listRole = Arrays.asList(role);
            int editSaleOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (listRole.contains(userRole.getPcRole().getName())) {
                    editSaleOrgPrice++;
                    break;
                }
            }
            model.addAttribute("editSaleOrgPrice", editSaleOrgPrice);
        }
        catch (RuntimeException e) {
        }

        // 角色是否能查看平台产品价格  1 可修改     非1  不可修改
        try {
            String value = SystemConfig.getConfig("seeSaleOrgPriceRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> lists = Arrays.asList(role);
            int seeSaleOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (lists.contains(userRole.getPcRole().getName())) {
                    seeSaleOrgPrice++;
                    break;
                }
            }
            model.addAttribute("seeSaleOrgPrice", seeSaleOrgPrice);
        }
        catch (RuntimeException e) {
        }

		return CommonUtil.getFolderPrefix(code) + "/b2b/priceApply/edit";
	}

	/*
	 * 保存、更新、提交
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(Integer isSubmit, PriceApply priceApply, Long sbuId, 
			Integer typeID, Long organizationId, ModelMap model) {
		
		if(!ConvertUtil.isEmpty(priceApply.getId())){
			PriceApply pPriceApply = priceApplyService.find(priceApply.getId());
			//校验特价单状态
			priceApplyService.checkPriceApplyStatus(pPriceApply,0,"已保存","保存");
		}
		StoreMember storeMember = storeMemberService.getCurrent();
		if(!ConvertUtil.isEmpty(organizationId)){
			Organization organization = organizationService.find(organizationId);
			if(ConvertUtil.isEmpty(organization)){
				return error("经营组织不能为空");
			}
			priceApply.setOrganization(organization);
		}else{
			return error("经营组织不能为空");
		}
		Sbu sbu = sbuService.find(sbuId);
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,filters,null);
		if (sbus.size() == 0 || sbus == null) {
			return error("该客户没有维护此类型SBU");
		}
		priceApplyService.savePriceApply(priceApply, isSubmit, sbuId, typeID);
		return success().addObjX(priceApply.getId());
	}

	/*
	 * 提交
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check(Long id, Integer flag, ModelMap model) {

		PriceApply priceApply = priceApplyService.find(id);
		priceApplyService.checkOrCancel(priceApply, flag);
		return success();
	}

    /**
     * 流程启动（新）
     * @param id
     * @param modelId
     * @param objTypeId
     * @return
     */
    @RequestMapping(value = "/start_check_wf", method = RequestMethod.POST)
    public @ResponseBody ResultMsg start_check_wf(Long id, String modelId, Long objTypeId) {
        priceApplyService.createWf(id, modelId, objTypeId);
        return success();
    }

	@RequestMapping(value = "/get_apply_price", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getApplyPrice(Long productId, Long storeId) {

		Map<String, Object> map = new HashMap<String, Object>();
		Map<String, Object> itemMap = priceApplyService.findItemByProduct(productId,
				storeId);
		if (itemMap != null) {
			map.put("apply_sn", itemMap.get("sn"));
			map.put("apply_price", itemMap.get("price"));
			map.put("apply_item_id", itemMap.get("id"));
			BigDecimal useable_quantity = new BigDecimal(
					itemMap.get("quantity").toString()).subtract(new BigDecimal(
					itemMap.get("used_quantity").toString()));
			map.put("apply_useable_quantity", useable_quantity);
		}

		return success().addObjX(map);
	}

	/*
	 * 条件导出
	 * @param sn
	 * @param storeMemberId
	 * @param docStatus
	 * @param storeId
	 * @param productId
	 * @param firstTime
	 * @param lastTime
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String sn, Long storeMemberId,
			Integer[] docStatus, Long storeId, Long productId,
			String firstTime, String lastTime, Long sbuId, Pageable pageable,
			ModelMap model) {

		Integer size = priceApplyService.count(sn,
				docStatus,
				storeMemberId,
				storeId,
				productId,
				firstTime,
				lastTime,
				sbuId);
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/*
	 * 导出
	 * @param username
	 * @param mobile
	 * @param name
	 * @param memberRankId
	 * @param isPartner
	 * @param storeId
	 * @param saleOrgId
	 * @param startTime
	 * @param endTime
	 * @param model
	 * @param page
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, Long storeMemberId,
			Integer[] docStatus, Long storeId, Long productId,
			String firstTime, String lastTime, ModelMap model, Long sbuId,
			Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");

		List<Map<String, Object>> applys = priceApplyService.findList(sn,
				docStatus,
				storeMemberId,
				storeId,
				productId,
				firstTime,
				lastTime,
				null,
				page,
				sbuId,
				size);

		if (!applys.isEmpty()) {
			for (Map<String, Object> map : applys) {
				List<Map<String, Object>> orderItem = priceApplyService.findOrderListItemIds(map.get("id")
						.toString());
				Double toCount = 0.0; //已发汇总
				Double toQu = 0.0; //总平方数
				Double toOciQu = 0.0;//总已关闭数
				Double toBr = 0.0;//每支数
				for (Map<String, Object> map2 : orderItem) {
					Double qu = Double.valueOf(map2.get("qu").toString());
					Double oci_qu = Double.valueOf(map2.get("oci_qu")
							.toString());
					Double br = Double.valueOf(map2.get("br").toString());
					toQu = toQu + qu;
					toOciQu = oci_qu + toOciQu;
					toBr = br;
				}
				toCount = toQu - (toOciQu * toBr);
				map.put("toCount", toCount);
			}
		}
		return this.getModelAndView(applys, model);

	}

	/*
	 * 选择导出
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> applys = priceApplyService.findList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null);

		if (!applys.isEmpty()) {
			for (Map<String, Object> map : applys) {
				List<Map<String, Object>> orderItem = priceApplyService.findOrderListItemIds(map.get("id")
						.toString());
				Double toCount = 0.0; //已发汇总
				Double toQu = 0.0; //总平方数
				Double toOciQu = 0.0;//总已关闭数
				Double toBr = 0.0;//每支数
				for (Map<String, Object> map2 : orderItem) {
					Double qu = Double.valueOf(map2.get("qu").toString());
					Double oci_qu = Double.valueOf(map2.get("oci_qu")
							.toString());
					Double br = Double.valueOf(map2.get("br").toString());
					toQu = toQu + qu;
					toOciQu = oci_qu + toOciQu;
					toBr = br;
				}
				toCount = toQu - (toOciQu * toBr);
				map.put("toCount", toCount);
			}
		}

		return getModelAndView(applys, model);
	}

	public ModelAndView getModelAndView(List<Map<String, Object>> data,
			ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("toCount") != null) {
				BigDecimal toCount = new BigDecimal(str.get("toCount")
						.toString());
				str.put("toCount",
						toCount.setScale(6, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("used_quantity") != null) {
				BigDecimal used_quantity = new BigDecimal(
						str.get("used_quantity").toString());
				str.put("used_quantity",
						NumberFormat.getInstance().format(used_quantity));
			}
			if (str.get("max_quantity") != null) {
				BigDecimal max_quantity = new BigDecimal(str.get("max_quantity")
						.toString());
				BigDecimal toCount = new BigDecimal(str.get("toCount")
						.toString());
				str.put("left_quantity",max_quantity.subtract(toCount));
			}
		
			if (str.get("sale_org_price") != null) {
				BigDecimal toCount = new BigDecimal(str.get("sale_org_price")
						.toString());
				str.put("sale_org_price",
						toCount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("sale_org_sales_price") != null) {
				BigDecimal toCount = new BigDecimal(str.get("sale_org_sales_price")
						.toString());
				str.put("sale_org_sales_price",
						toCount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		String[] header = { "特价编号",
				"特价类型",
				"机构",
				"sbu",
				"客户名称",
				"客户简称",
				"ERP客户编码",
				"工程OA单号",
				"工程名称",
				"工程地址",
				"申请人",
				"创建日期",
				"单据状态",
				"发货仓",
				"产品系列",
				"产品名称",
				"产品型号",
				"产品编码",
				"产品描述",
				"产品级别",
				"销售价",
				"申请价格",
				"平台结算价原价",
				"平台结算价特价",
				"申请时间",
				"最小开单数量(㎡)",
				"最大累计数量(㎡)",
				"已发汇总(㎡)",
				"剩余未发数量",
				"开始时间",
				"结束时间",
				"申请说明" };
		// 设置单元格取值
		String[] properties = { "sn", //特价编号
				"price_type", //特价类型
				"sale_org_name", //机构 
				"sbu_name",
				"store_name", //客户名称
				"alias", //客户简称
				"out_trade_no", //ERP客户编码
				"oa_sn", //工程OA报备单号
				"engineering_name", //工程名称
				"engineering_address", //工程地址
				"store_member_name", //申请人
				"create_date", //创建日期
				"doc_status_name", //单据状态
				"sd_warehouse", //发货仓
				"product_category_name", //产品系列
				"product_name", //产品名称
				"model", //产品型号
				"vonder_code", //产品编码
				"description", //产品描述
				"levelName", //产品级别
				"member_price", //销售价
				"price", //申请价格
				"sale_org_price",
				"sale_org_sales_price",
				"create_date", //申请时间
				"min_quantity", //最小开单数量(㎡)
				"max_quantity", //最大累计数量(㎡)
				"left_quantity",
				"toCount", //已发汇总(㎡)
				"start_date", //开始时间
				"end_date", //结束时间
				"memo" //申请说明
		};

		Integer[] widths = { 25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150,
				25 * 150 };

		for (Map<String, Object> map : data) {
			String doc_status_name = "";
			if (map.get("pa_doc_status") != null) {
				String statuts = map.get("pa_doc_status").toString();
				if ("0".equals(statuts)) {
					doc_status_name = "已保存";
				}
				else if ("1".equals(statuts)) {
					doc_status_name = "处理中";
				}
				else if ("2".equals(statuts)) {
					doc_status_name = "已处理";
				}
				else if ("3".equals(statuts)) {
					doc_status_name = "已失效";
				}
				map.put("doc_status_name", doc_status_name);
			}
			/*if (map.get("product_grade") != null) {
				String product_grade = map.get("product_grade").toString();
				if ("1".equals(product_grade)) {
					product_grade = "优等品";
					map.put("product_grade", product_grade);
				}
				else if ("2".equals(product_grade)) {
					product_grade = "二等品";
					map.put("product_grade", product_grade);
				}
				else if ("3".equals(product_grade)) {
					product_grade = "一等品";
					map.put("product_grade", product_grade);
				}
				else if ("4".equals(product_grade)) {
					product_grade = "无等级";
					map.put("product_grade", product_grade);
				}
			}*/
			if (map.get("type") != null) {
				String type = map.get("type").toString();
				if ("0".equals(type)) {
					type = "促销";
					map.put("type", type);
				}
				if ("1".equals(type)) {
					type = "二等品";
					map.put("type", type);
				}
				if ("2".equals(type)) {
					type = "定制";
					map.put("type", type);
				}
				if ("3".equals(type)) {
					type = "工程";
					map.put("type", type);
				}
			}
			if (map.get("start_date") != null) {
				String start_date = map.get("start_date").toString();
				if (!start_date.contains("1970-01-01")) {
					map.put("start_date", start_date.substring(0, 10));
				}
				else {
					map.put("start_date", "");
				}
			}
			if (map.get("end_date") != null) {
				String end_date = map.get("end_date").toString();
				if (!end_date.contains("2099-12-31")) {
					map.put("end_date", end_date.substring(0, 10));
				}
				else {
					map.put("end_date", "");
				}
			}
			if (map.get("create_date") != null) {
				String create_date = map.get("create_date").toString();
				if (!create_date.contains("2099-12-31")) {
					map.put("create_date", create_date.substring(0, 10));
				}
				else {
					map.put("create_date", "");
				}
			}

			if (map.get("min_quantity") != null) {
				BigDecimal min_quantity = new BigDecimal(
						map.get("min_quantity").toString());
				map.put("min_quantity",
						NumberFormat.getInstance().format(min_quantity));
			}
			if (map.get("max_quantity") != null) {
				BigDecimal max_quantity = new BigDecimal(
						map.get("max_quantity").toString());
				map.put("max_quantity",
						NumberFormat.getInstance().format(max_quantity));
			}
			if (map.get("price") != null) {
				BigDecimal price = new BigDecimal(map.get("price").toString());
				map.put("price", price.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (map.get("product_price") != null) {
				BigDecimal product_price = new BigDecimal(
						map.get("product_price").toString());
				map.put("product_price",
						product_price.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
		}

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	/*
	 * excel导入特价单
	 * @param file
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file) throws Exception {

		Map<String, Object> results = priceApplyService.memberRankImport(file);
		int result = Integer.parseInt(results.get("result").toString());
		int success = Integer.parseInt(results.get("success").toString());
		String msg = results.get("msg").toString();

		if (ConvertUtil.isEmpty(msg)) {
			return success("总数" + result + "行，成功导入" + success + " 行");
		}
		else {
			return error("总数" + result + "行，成功导入" + success + " 行<br>" + msg);
		}
	}

//	/*
//	 * 获取配置
//	 */
//	@RequestMapping(value = "/get_config", method = RequestMethod.POST)
//	public @ResponseBody
//	ResultMsg getConfig(Long id, Long obj_type_id) {
//
//		List<Map<String, Object>> configs = new ArrayList<Map<String, Object>>();
//		List<Map<String, Object>> list = wfObjConfigLineBaseService.getConfig(obj_type_id,
//				true);
//		for (Map<String, Object> map : list) {
//			String conditions = ConvertUtil.isEmpty(map.get("conditions")) ? null
//					: map.get("conditions").toString();
//			if (conditions == null) {
//				configs.add(map);
//			}
//			else {
//				String sql = "select count(1) from (select max(discount*10) discount from xx_price_apl_item where price_apply = ?) a where "
//						+ conditions;
//				int count = priceApplyService.getDaoCenter()
//						.getNativeDao()
//						.findInt(sql, new Object[] { id });
//				if (count > 0) {
//					configs.add(map);
//				}
//			}
//		}
//
//		return success().addObjX(configs);
//	}

	@RequestMapping(value = "select_price_apply_item", method = RequestMethod.GET)
	public String select_price_apply_item(Integer multi, Long productId,
			Long productCategoryId, Long typeSystemDictId, Long storeId,
			String businessTypeName, Integer productGrade,Long saleOrgId,Long organizationId,ModelMap model) {

		model.addAttribute("multi", multi);
		model.addAttribute("storeId", storeId);
		model.addAttribute("productId", productId);
		model.addAttribute("productCategoryId", productCategoryId);
		model.addAttribute("typeSystemDictId", typeSystemDictId);
		model.addAttribute("businessTypeName", businessTypeName);
		model.addAttribute("productGrade", productGrade);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("isMember", storeMemberService.getCurrent().getMemberType());
		model.addAttribute("organizationId", organizationId);
		StoreMember storeMember = storeMemberService.getCurrent();
		//默认外部用户
		Integer isMember = 1;
		if(storeMember.getMemberType()==0){
			isMember = 0;
		}
		model.addAttribute("isMember", isMember);

        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
        List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
        // 角色是否能显示修改平台结算价格  1 可显示修改    非1  不可显示修改
        try {
            String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> lists = Arrays.asList(role);
            int editSaleOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (lists.contains(userRole.getPcRole().getName())) {
                    editSaleOrgPrice++;
                    break;
                }
            }
            model.addAttribute("editSaleOrgPrice", editSaleOrgPrice); }
        catch (RuntimeException e) {
        }
		return "/b2b/priceApply/select_price_apply_item";

	}

	@RequestMapping(value = "select_price_apply_item_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_price_apply_item_data(Long productId,
			Long productCategoryId, Long typeSystemDictId, Long storeId,
			String businessTypeName, Integer productGrade,Long saleOrgId,Long organizationId,Pageable pageable) {
		
		if(storeId==null){
			return error("客户不能为空");
		}else if(saleOrgId == null){
			return error("机构不能为空");
		}
		
		List<Map<String, Object>> lists = priceApplyService.findItemByProductNew(productId,
				productCategoryId,
				typeSystemDictId,
				storeId,
				businessTypeName,
				saleOrgId,
				productGrade,
				organizationId);
		
		String jsonPage = JsonUtils.toJson(lists);

		return success(jsonPage);

	}
}