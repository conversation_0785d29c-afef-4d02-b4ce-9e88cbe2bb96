package net.shopxx.order.b2b.controller;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderChange;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.service.GrossProfitAnalysisService;
import net.shopxx.order.service.OrderChangeService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderService;
import net.shopxx.wf.service.WfObjConfigBaseService;

@Controller("b2bOrderChangeController")
@RequestMapping("/b2b/order_change")
public class OrderChangeController extends BaseController {

	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "orderChangeServiceImpl")
	private OrderChangeService orderChangeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "grossProfitAnalysisServiceImpl")
	private GrossProfitAnalysisService grossProfitAnalysisService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer isCheck, Long id, ModelMap model,
			Integer readOnly, Long objTypeId, Long objid, Long[] ci_ids) {

		if (ci_ids != null && ci_ids.length > 0) {
			readOnly = 1;
		}
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("ci_ids", ci_ids);

		model.addAttribute("isCheck", isCheck);
		model.addAttribute("id", id);
		return "/b2b/order_change/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, Integer isCheck, ModelMap model) {
		model.addAttribute("isCheck", isCheck);
		/**北京零微科技有限公司*/
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId != null && companyInfoId == 18) {
			model.addAttribute("isLw", true);
		}

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		return "/b2b/order_change/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn, String orderSn,
			Integer[] status, Long storeId, Pageable pageable) {

		Page<Map<String, Object>> page = orderChangeService.findPage(sn,
				orderSn,
				status,
				storeId,
				pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 查看
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, Integer isCheck, ModelMap model) {
		List<Filter> filters = new ArrayList<Filter>();

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(77L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("isCheck", isCheck);

		OrderChange orderChange = orderChangeService.find(id);
		model.addAttribute("orderChange", orderChange);

		List<Map<String, Object>> items0 = orderChangeService
				.findItemListByOrderChangeId(id, null);
		if (items0.size() > 0) {
			Map<String, Object> item = items0.get(0);
			item.put("operate_type", 0);

			Map<String, Object> item2 = new HashMap<String, Object>();
			item2.putAll(item);
			item2.put("operate_type", 1);
			items0.add(item2);
		}
		String order_json = JsonUtils.toJson(items0);
		model.addAttribute("order_json", order_json);

		List<Map<String, Object>> items1 = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> items11 = orderChangeService
				.findItemListByOrderChangeId(id, 1);
		for (Map<String, Object> item : items11) {
			if (item.get("type") != null
					&& "2".equals(item.get("type").toString())) {
				//新增
				item.put("operate_type", 1);
				items1.add(item);
			}
			else {
				item.put("operate_type", 0);
				items1.add(item);

				Map<String, Object> item2 = new HashMap<String, Object>();
				item2.putAll(item);
				item2.put("operate_type", 1);
				items1.add(item2);
			}

		}

		String item_json = JsonUtils.toJson(items1);
		model.addAttribute("item_json", item_json);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);

		int undefinedProduct2Order = 0;
		int appProductChangePrice = 0;
		try {
			undefinedProduct2Order = Integer
					.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
							WebUtils.getCurrentCompanyInfoId()));
			appProductChangePrice = Integer
					.parseInt(SystemConfig.getConfig("appProductChangePrice",
							WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);
		model.addAttribute("appProductChangePrice", appProductChangePrice);

		model.addAttribute("isCheck", isCheck);

		//工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "FactoryName"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> factorySystemDicts = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("factorySystemDicts", factorySystemDicts);

		//付款方式paymentStyle
		filters.clear();
		filters.add(Filter.eq("code", "PaymentStyle"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> paymentStyles = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("paymentStyles", paymentStyles);
		//付款条件
		filters.clear();
		filters.add(Filter.eq("code", "PaymentConditions"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> paymentConditions = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("paymentConditions", paymentConditions);

		//币种
		filters.clear();
		filters.add(Filter.eq("code", "Currency"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> currencies = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("currencies", currencies);

		//订单类型
		filters.clear();
		filters.add(Filter.eq("code", "OrderStyle"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> orderStyles = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("orderStyles", orderStyles);

		/** 订单全链路 */
		String orderFullLink_json = JsonUtils.toJson(
				orderFullLinkService.findListByOrderSn(orderChange.getSn()));
		model.addAttribute("orderFullLink_json", orderFullLink_json);

		return "/b2b/order_change/edit";
	}

	/**
	 * 添加
	 */
//	@RequestMapping(value = "/add", method = RequestMethod.GET)
//	public String add(Long id, String sn, Integer isCheck, ModelMap model) {
//
//		Order order = null;
//		if (id == null || id.longValue() <= 0) {
//			List<Filter> filters = new ArrayList<Filter>();
//			filters.add(Filter.eq("sn", sn));
//			List<Order> orders = orderService.findList(1, filters, null);
//			if (orders.size() > 0) {
//				order = orders.get(0);
//			}
//		}
//		else {
//			order = orderService.find(id);
//		}
//		id = order.getId();
//		model.addAttribute("order", order);
//
//		boolean isReject = true;
//		for (OrderItem orderItem : order.getOrderItems()) {
//			if (orderItem.getShipPlanQuantity() != null
//					&& orderItem.getShipPlanQuantity()
//							.compareTo(BigDecimal.ZERO) == 1) {
//
//				//订单已存在发货单，不允许驳回
//				isReject = false;
//				break;
//			}
//		}
//		model.addAttribute("isReject", isReject);
//
//		List<Filter> filters = new ArrayList<Filter>();
//		filters.add(Filter.eq("isEnabled", true));
//		model.addAttribute("shippingMethods",
//				shippingMethodBaseService.findList(null, filters, null));
//
//		/**订单明细*/
//		List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString());
//		orderItems = sort(orderItems, null);
//		String orderItem_json = JsonUtils.toJson(orderItems);
//		model.addAttribute("orderItem_json", orderItem_json);
//
//		model.addAttribute("appProductChangePrice",
//				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
//						WebUtils.getCurrentCompanyInfoId())));
//
//		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
//		model.addAttribute("isCheckWf", isCheckWf);
//
//		filters.clear();
//		filters.add(Filter.eq("code", "FreightChargeType"));
//		filters.add(Filter.isNotNull("parent"));
//		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
//				filters,
//				null);
//		model.addAttribute("freightChargeTypes", freightChargeTypes);
//		if (order != null) {
//			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
//			model.addAttribute("triplicateForm", triplicateForm);
//		}
//
//		/**北京零微科技有限公司*/
//		if (order.getCompanyInfoId() == 18) {
//			model.addAttribute("isLw", true);
//		}
//
//		int undefinedProduct2Order = 0;
//		try {
//			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
//					WebUtils.getCurrentCompanyInfoId()));
//		}
//		catch (Exception e) {}
//		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);
//
//		return "/b2b/order_change/add";
//	}
	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(OrderChange orderChange,
			Long afterAreaId, Long prevAreaId, Long saleOrgId,
			Long storeMemberId, Long currencyId, Long paymentConditionId,
			Long distributorId, Long orderStyleId) {

		orderChange.setSaleOrg(saleOrgBaseService.find(saleOrgId));
		orderChange.setStoreMember(storeMemberService.find(storeMemberId));
		orderChange.setCurrency(systemDictService.find(currencyId));
		orderChange.setPaymentConditions(
				systemDictService.find(paymentConditionId));
		orderChange.setAfterArea(areaService.find(afterAreaId));
		orderChange.setPrevArea(areaService.find(prevAreaId));
		orderChange.setChangeStoreMember(storeMemberService.getCurrent());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd HH:mm:ss");

		try {
			orderChange.setChangeDate(sdf.parse(sdf.format(new Date())));
		}
		catch (ParseException e) {}
		//经销商
		Store distributor = storeService.find(distributorId);
		orderChange.setDistributor(distributor);
		//订单类型
		SystemDict orderStyle = systemDictService.find(orderStyleId);
		orderChange.setOrderStyle(orderStyle);

		orderChangeService.saveOrderChange(orderChange);
		return success().addObjX(orderChange.getId());
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(OrderChange orderChange,
			Long afterAreaId, Long prevAreaId, Long saleOrgId,
			Long storeMemberId, Long currencyId, Long paymentConditionId,
			Long distributorId, Long orderStyleId) {

		orderChange.setSaleOrg(saleOrgBaseService.find(saleOrgId));
		orderChange.setStoreMember(storeMemberService.find(storeMemberId));
		orderChange.setCurrency(systemDictService.find(currencyId));
		orderChange.setPaymentConditions(
				systemDictService.find(paymentConditionId));
		orderChange.setAfterArea(areaService.find(afterAreaId));
		orderChange.setPrevArea(areaService.find(prevAreaId));
		orderChange.setChangeStoreMember(storeMemberService.getCurrent());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd HH:mm:ss");

		try {
			orderChange.setChangeDate(sdf.parse(sdf.format(new Date())));
		}
		catch (ParseException e) {}
		//经销商
		Store distributor = storeService.find(distributorId);
		orderChange.setDistributor(distributor);
		//订单类型
		SystemDict orderStyle = systemDictService.find(orderStyleId);
		orderChange.setOrderStyle(orderStyle);
		orderChangeService.updateOrderChange(orderChange);
		return success();
	}

	private List<Map<String, Object>> sort(List<Map<String, Object>> items,
			Long parent) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if (items != null) {
			for (Map<String, Object> map : items) {
				if ((map.get("parent") != null
						&& parent != null
						&& Long.parseLong(
								map.get("parent").toString()) == parent
										.longValue())
						|| (map.get("parent") == null && parent == null)) {
					result.add(map);
					result.addAll(sort(items,
							Long.parseLong(map.get("id").toString())));
				}
			}
		}
		return result;
	}

	/**
	 * 确认、作废
	 * @throws Exception 
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check(Long id, Integer status)
			throws Exception {
		OrderChange orderChange = orderChangeService.find(id);
		if (orderChange == null) {
			return this.error("变更单不存在！");
		}
		if (orderChange.getStatus() != 0) {
			return this.error("变更单不是未确认状态，不允许操作！");
		}
		if (status == 1) {
			//变更单确认
			orderChangeService.checkOrderChange(orderChange);
		}
		else if (status == 2) {
			//变更单作废
			orderChange.setStatus(2);
			orderChangeService.update(orderChange);
		}

		return success();
	}

	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long id, String sn, Integer isCheck, ModelMap model) {

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}
		else {
			order = orderService.find(id);
		}

		id = order.getId();
		model.addAttribute("order", order);

		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
							.compareTo(BigDecimal.ZERO) == 1) {

				//订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));

		/**订单主表*/
		List<Map<String, Object>> orderInfoList = orderService
				.findListById(order.getId().toString());
		if (orderInfoList.size() > 0) {
			Map<String, Object> item = orderInfoList.get(0);
			item.put("operate_type", 0);

			Map<String, Object> item2 = new HashMap<String, Object>();
			item2.putAll(item);
			item2.put("operate_type", 1);
			orderInfoList.add(item2);

		}
		model.addAttribute("order_json", JsonUtils.toJson(orderInfoList));

		/**订单明细*/
		List<Map<String, Object>> orderItems = orderService
				.findOrderItemListByOrderId(id.toString(),null,null,null);
		orderItems = sort(orderItems, null);

		List<Map<String, Object>> items1 = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> item : orderItems) {

			filters.clear();
			List<Map<String, Object>> grossProfitAnalysiss = orderService
					.findGrossProfitAnalysisListByOrderId(id.toString());
			if (grossProfitAnalysiss != null
					&& grossProfitAnalysiss.size() > 0) {
				for (int j = 0; j < grossProfitAnalysiss.size(); j++) {
					if (item.get("id")
							.equals(grossProfitAnalysiss.get(j)
									.get("order_item"))
							&& item.get("orders").equals(
									grossProfitAnalysiss.get(j).get("orders"))
							&& item.get("product").equals(grossProfitAnalysiss
									.get(j).get("product"))) {
					
						item.put("p_market_price",
								grossProfitAnalysiss.get(j)
										.get("market_price") == null ? 0
												: grossProfitAnalysiss.get(j)
														.get("market_price"));
						item.put("p_market_fa_price",
								grossProfitAnalysiss.get(j)
										.get("market_fa_price") == null ? 0
												: grossProfitAnalysiss.get(j)
														.get("market_fa_price"));
						item.put("p_original_fa_price",
								grossProfitAnalysiss.get(j)
										.get("original_fa_price") == null ? 0
												: grossProfitAnalysiss.get(j)
														.get("original_fa_price"));
						item.put("p_factory_price",
								grossProfitAnalysiss.get(j)
										.get("factory_price") == null ? 0
												: grossProfitAnalysiss.get(j)
														.get("factory_price"));
						item.put("p_service_price",
								grossProfitAnalysiss.get(j)
										.get("service_price") == null ? 0
												: grossProfitAnalysiss.get(j)
														.get("service_price"));
					}
					else {
						item.put("p_market_price", 0);
						item.put("p_market_fa_price", 0);
						item.put("p_original_fa_price", 0);
						item.put("p_factory_price", 0);
						item.put("p_service_price", 0);
					}
				}
			}
			else {
				item.put("p_market_price", 0);
				item.put("p_market_fa_price", 0);
				item.put("p_original_fa_price", 0);
				item.put("p_factory_price", 0);
				item.put("p_service_price", 0);
			}
			item.put("operate_type", 0);
			items1.add(item);

			Map<String, Object> item2 = new HashMap<String, Object>();
			item2.putAll(item);
			item2.put("operate_type", 1);
			items1.add(item2);

		}

		String orderItem_json = JsonUtils.toJson(items1);
		model.addAttribute("orderItem_json", orderItem_json);

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}

		int undefinedProduct2Order = 0;//未定义产品是否支持下单
		int appProductChangePrice = 0;//b2b业务商品是否可以改价
		try {
			undefinedProduct2Order = Integer
					.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
							WebUtils.getCurrentCompanyInfoId()));
			appProductChangePrice = Integer
					.parseInt(SystemConfig.getConfig("appProductChangePrice",
							WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);
		model.addAttribute("appProductChangePrice", appProductChangePrice);

		//工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "FactoryName"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> factorySystemDicts = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("factorySystemDicts", factorySystemDicts);
		//付款方式paymentStyle
		filters.clear();
		filters.add(Filter.eq("code", "PaymentStyle"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> paymentStyles = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("paymentStyles", paymentStyles);
		//付款条件
		filters.clear();
		filters.add(Filter.eq("code", "PaymentConditions"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> paymentConditions = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("paymentConditions", paymentConditions);

		//币种
		filters.clear();
		filters.add(Filter.eq("code", "Currency"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> currencies = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("currencies", currencies);

		//订单类型
		filters.clear();
		filters.add(Filter.eq("code", "OrderStyle"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> orderStyles = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("orderStyles", orderStyles);

		return "/b2b/order_change/add";
	}

	@RequestMapping(value = "check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, Long objConfId,
			Integer status) throws Exception {
		OrderChange orderChange = orderChangeService.find(id);
		if (orderChange == null) {
			return this.error("变更单不存在！");
		}
		if (orderChange.getStatus() != 0) {
			return this.error("变更单不是未确认状态，不允许操作！");
		}
		orderChangeService.checkWf(id, objConfId);

		return success();
	}

}
