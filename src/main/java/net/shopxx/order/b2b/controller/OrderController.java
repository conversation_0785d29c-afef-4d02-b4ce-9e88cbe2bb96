package net.shopxx.order.b2b.controller;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.shopxx.base.core.util.*;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.CMB.CMBHttpRequest;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.MessageBoard;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.PaymentMethod;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.ShippingMethod;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.MessageBoardService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.PaymentMethodBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.PolicyCountService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.finance.service.StoreDepositService;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreManager;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.entity.StoreSbu;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.order.entity.Cart;
import net.shopxx.order.entity.CartItem;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.entity.Order.PaymentStatus;
import net.shopxx.order.entity.Order.ShippingStatus;
import net.shopxx.order.entity.OrderAttach;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.purchase.service.ContractPriceService;
import net.shopxx.order.service.CartItemService;
import net.shopxx.order.service.CartService;
import net.shopxx.order.service.CostService;
import net.shopxx.order.service.CustomerContractService;
import net.shopxx.order.service.LogisticsService;
import net.shopxx.order.service.OffsiteBranchService;
import net.shopxx.order.service.OrderAttachService;
import net.shopxx.order.service.OrderCloseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.PriceApplyService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.order.service.StandbyAddressService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductStore;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductPriceHeadService;
import net.shopxx.product.service.ProductStoreBaseService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseBatchService;
import net.shopxx.util.AddressUtils;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

@Controller("b2bOrderController")
@RequestMapping("/b2b/order")
public class OrderController extends BaseController {

	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "cartServiceImpl")
	private CartService cartService;
	@Resource(name = "cartItemServiceImpl")
	private CartItemService cartItemService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;
	@Resource(name = "paymentMethodBaseServiceImpl")
	private PaymentMethodBaseService paymentMethodBaseService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "orderAttachServiceImpl")
	private OrderAttachService orderAttachService;
	@Resource(name = "priceApplyServiceImpl")
	private PriceApplyService priceApplyService;
	@Resource(name = "productPriceHeadServiceImpl")
	private ProductPriceHeadService productPriceHeadService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "messageBoardServiceImpl")
	private MessageBoardService messageBoardService;
	@Resource(name = "productStoreBaseServiceImpl")
	private ProductStoreBaseService productStoreService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "contractPriceServiceImpl")
	private ContractPriceService contractPriceService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseService;
	@Resource(name = "policyCountServiceImpl")
	private PolicyCountService policyCountService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "orderCloseServiceImpl")
	private OrderCloseService orderCloseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "offsiteBranchServiceImpl")
	private OffsiteBranchService offsiteBranchService;
	@Resource(name = "costServiceImpl")
	private CostService costService;
	@Resource(name = "standbyAddressServiceImpl")
	private StandbyAddressService standbyAddressService;
	@Resource(name = "storeMemberSbuServiceImpl") 
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "logisticsServiceImpl")
	private LogisticsService logisticsService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardBaseService;
	@Resource(name="customerContractServiceImpl")
	private CustomerContractService customerContractService;
	@Resource(name = "storeDepositServiceImpl")
	private StoreDepositService storeDepositService;
	@Resource(name = "warehouseBatchServiceImpl")
	private WarehouseBatchService warehouseBatchService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;


	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer readOnly, Long objTypeId, Long objid,
						  Long[] ci_ids,String productGrade_id, Long warehouseId,Long menuId, Long sbuId, ModelMap model) {

		if (ci_ids != null && ci_ids.length > 0) {
			readOnly = 1;
		}
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("ci_ids", ci_ids);
		model.addAttribute("warehouseId", warehouseId);
		model.addAttribute("sbuId", sbuId);
        model.addAttribute("menuId", menuId);
		model.addAttribute("product_grade",productGrade_id);
		return "/b2b/order/list_tb";
	}

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb_code(@PathVariable String code, Integer readOnly,
							   Long objTypeId, Long objid, Long[] ci_ids, ModelMap model) {

		if (ci_ids != null && ci_ids.length > 0) {
			readOnly = 1;
		}
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("ci_ids", ci_ids);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/order/list_tb";
	}

	/*
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Pageable pageable,
					   Integer readOnly, ModelMap model) {
		model.addAttribute("readOnly", readOnly);
		/* 北京零微科技有限公司 */
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId != null && companyInfoId == 18) {
			model.addAttribute("isLw", true);
		}

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}

		try {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
			// 非0 展示
		}
		catch (RuntimeException e) {

		}

		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/order/list";
	}

	/*
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, Integer readOnly, Long sbuId,Long menuId,Long userId,
					   ModelMap model) {
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("sbuId", sbuId);
		/* 北京零微科技有限公司 */
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId != null && companyInfoId == 18) {
			model.addAttribute("isLw", true);
		}

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);
		
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("businessTypes", businessTypes);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}

		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 0 不展示 非0 展示
		}
		catch (RuntimeException e) {
			
		}
		
		// 订单下达是否展示色号、含水率、批次  0 不展示    非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch); 
		}
		catch (RuntimeException e) {

		}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);
        model.addAttribute("menuId", menuId);
        //获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);
        
        //用户经营组织权限	
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);
		
		
		
		// 五期权限
		try {
			String value = SystemConfig.getConfig("link5Roles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int link5Roles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					link5Roles++;
					break;
				}
			}
			model.addAttribute("link5Roles", link5Roles); 
		 }
		catch (RuntimeException e) {

		}
        
		return "/b2b/order/list";
	}

	/*
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String orderSn, String outTradeNo,
						Integer[] orderStatus,Integer[] wfState, Long sbuId, Integer[] exStatus,
						Integer[] paymentStatus, Integer[] shippingStatus, Integer[] flag,
						Long warehouseId, Long[] storeId, String phone, String consignee,
						String address, Long deliveryCorpId, Long productId[],
						String firstTime, String lastTime, Integer readOnly,
						Integer[] confirmStatus, String store_member_name, Long[] saleOrgId,
						Long organizationId,Long businessTypeId, Pageable pageable, ModelMap model,
						String moistureContent,String colourNumber,String batch,
						Long[] warehouseIds,Long[] organizationIds,String aftersaleSn) {

		//2019-05-16 冯旗 流程状态搜索条件(status)
		Page<Map<String, Object>> page = orderService.newFindPage(orderSn,
				outTradeNo,
				orderStatus,
				wfState,
				sbuId,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				null,
				store_member_name,
				saleOrgId,
				organizationId,
				businessTypeId,
				null,
				pageable,
				moistureContent,
				colourNumber,
				batch,
				warehouseIds,
				organizationIds, aftersaleSn);

		List<Map<String, Object>> orders = page.getContent();

		if (!orders.isEmpty()) {
			String ids = "";
			for (int i = 0; i < orders.size(); i++) {
				Map<String, Object> map = orders.get(i);
				if (i == orders.size() - 1) {
					ids += map.get("id");
				}
				else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(ids,null,organizationIds,true);
			if (readOnly == null) {
				orderItems = sort(orderItems, null);
			}
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : orders) {
				items = new ArrayList<Map<String, Object>>();
				String orderId = map.get("id").toString();
				for (Map<String, Object> itemMap : orderItems) {
					String oid = itemMap.get("orders").toString();
					if (readOnly != null) {
						if (orderId.equals(oid) && itemMap.get("parent") == null) {
							items.add(itemMap);
						} 
					}else {
						if (orderId.equals(oid)) {
							items.add(itemMap);
						}
					}
				}
				map.put("order_items", items);
			}
		}

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/*
	 * 查看
	 */
	@RequestMapping(value = "/view/{code}", method = RequestMethod.GET)
	public String viewCode(@PathVariable String code, Long id, String sn,
						   Integer readOnly, Integer isEdit, ModelMap model) {

		model.addAttribute("readOnly", readOnly);

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}
		else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);

		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService.findShippingItemListByOrderItemId(orderItem.getId()
					.toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
					.compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);
		model.addAttribute("isEdit", isEdit);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);

		//发票信息

		// 政策金额
		BigDecimal actualAmount = policyCountService.findTotalActualAmount(order.getStore()
				.getId());
		BigDecimal availableAmount = policyCountService.findTotalAvailableAmount(order.getStore()
				.getId());
		model.addAttribute("policyAmount",
				actualAmount.subtract(availableAmount));

		//订单分类
		List<Filter> filterss = new ArrayList<Filter>();
		filterss.add(Filter.eq("code", "OrderClassify"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> orderClassifys = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("orderClassifys", orderClassifys);

		//币种
		filterss.clear();
		filterss.add(Filter.eq("code", "Currency"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> currencies = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("currencies", currencies);

		//订单类型
		filterss.clear();
		filterss.add(Filter.eq("code", "OrderStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> orderStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("orderStyles", orderStyles);

		//贸易类型
		filterss.clear();
		filterss.add(Filter.eq("code", "TradeStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> tradeStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("tradeStyles", tradeStyles);

		//销售体系
		filterss.clear();
		filterss.add(Filter.eq("code", "SaleStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> saleStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("saleStyles", saleStyles);

		//行业类别industryStyle
		filterss.clear();
		filterss.add(Filter.eq("code", "IndustryStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> industryStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("industryStyles", industryStyles);

		//付款方式paymentStyle
		filterss.clear();
		filterss.add(Filter.eq("code", "PaymentStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> paymentStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("paymentStyles", paymentStyles);

		//费用类型 -系统词汇
		filterss.clear();
		filterss.add(Filter.eq("code", "CostType"));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> costTypes = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("costTypes", costTypes);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		/* 客户经理 */
		if (order != null) {
			Store store = order.getStore();
			if (store != null) {
				model.addAttribute("storeManagerList", store.getStoreManagers());
			}
		}
		/* 订单明细 */
		List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
		for (int i = 0; i < orderItems.size(); i++) {
			String oid = orderItems.get(i).get("id").toString();
			orderItems.get(i)
					.put("closed_quantity",
							orderCloseService.findClosedQuantityByOrderItemId(Long.parseLong(oid))
									.get("closed_quantity_nb"));
		}
		orderItems = sort(orderItems, null);
		List<Map<String, Object>> grossProfitAnalysiss = orderService.findGrossProfitAnalysisListByOrderId(id.toString());

		for (int k = 0; k < orderItems.size(); k++) {
			Map<String, Object> map = orderItems.get(k);
			if (grossProfitAnalysiss != null && grossProfitAnalysiss.size() > 0) {
				for (int j = 0; j < grossProfitAnalysiss.size(); j++) {
					if (map.get("id").equals(grossProfitAnalysiss.get(j)
							.get("order_item"))
							&& map.get("orders")
							.equals(grossProfitAnalysiss.get(j)
									.get("orders"))
							&& map.get("product")
							.equals(grossProfitAnalysiss.get(j)
									.get("product"))) {
						map.put("market_price",
								grossProfitAnalysiss.get(j).get("market_price") == null ? 0
										: grossProfitAnalysiss.get(j)
										.get("market_price"));
						map.put("market_fa_price",
								grossProfitAnalysiss.get(j)
										.get("market_fa_price") == null ? 0
										: grossProfitAnalysiss.get(j)
										.get("market_fa_price"));
						map.put("original_fa_price",
								grossProfitAnalysiss.get(j)
										.get("original_fa_price") == null ? 0
										: grossProfitAnalysiss.get(j)
										.get("original_fa_price"));
						map.put("factory_price",
								grossProfitAnalysiss.get(j)
										.get("factory_price") == null ? 0
										: grossProfitAnalysiss.get(j)
										.get("factory_price"));
						map.put("service_price",
								grossProfitAnalysiss.get(j)
										.get("service_price") == null ? 0
										: grossProfitAnalysiss.get(j)
										.get("service_price"));
						map.put("grossProfitAnalysisId",
								grossProfitAnalysiss.get(j).get("id"));
						map.put("grossProfitAnalysisOrderId",
								grossProfitAnalysiss.get(j).get("orders"));
						map.put("grossProfitAnalysisOrderItemId",
								grossProfitAnalysiss.get(j).get("order_item"));
						map.put("grossProfitAnalysisProductId",
								grossProfitAnalysiss.get(j).get("product"));
					}
				}
			}
			else {

				map.put("market_price", 0);
				map.put("market_fa_price", 0);
				map.put("original_fa_price", 0);
				map.put("factory_price", 0);
				map.put("service_price", 0);
				map.put("grossProfitAnalysisId", "");
				map.put("grossProfitAnalysisOrderId", "");
				map.put("grossProfitAnalysisOrderItemId", "");
				map.put("grossProfitAnalysisProductId", "");
			}
		}
		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);
		/* 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByDetailedId(id,
				null,
				null));
		model.addAttribute("payment_json", payment_json);
		/* 发货单 */
		String shipping_json = JsonUtils.toJson(shippingService.findShippingItemListByOrderId(id.toString()));
		model.addAttribute("shipping_json", shipping_json);
		/* 订单附件 */
		String orderAttach_json = JsonUtils.toJson(orderAttachService.findListByOrderId(id));
		model.addAttribute("orderAttach_json", orderAttach_json);
		/* 订单全链路 */
		String orderFullLink_json = JsonUtils.toJson(orderFullLinkService.findListByOrderSn(order.getSn()));
		model.addAttribute("orderFullLink_json", orderFullLink_json);

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		/* 北京零微科技有限公司 */
		if (order.getCompanyInfoId() == 18) {
			model.addAttribute("isLw", true);
		}

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		Store store = order.getStore();
		if (store != null && !store.getIsMainStore()) {
			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
					order.getOrganization() == null ? null
							: order.getOrganization().getId(),
					order.getSbu().getId());
			model.addAttribute("balance", balance);
		}

		filters.clear();
		filters.add(Filter.eq("storeMember",
				storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示  非0 展示
		}
		catch (RuntimeException e) {

		}
		
		try {
			String value = SystemConfig.getConfig("editProductPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editProductPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editProductPrice++;
					break;
				}
			}
			model.addAttribute("editProductPrice", editProductPrice); // 角色是否能修改产品价格  1 可修改     非1  不可修改
		}
		catch (RuntimeException e) {

		}

		try {
			String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("editSaleOrgPrice", editSaleOrgPrice); // 角色是否能修改产品价格  1 可修改     非1  不可修改
		}
		catch (RuntimeException e) {

		}

		try {
			String value = SystemConfig.getConfig("editDiscount",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editDiscount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editDiscount++;
					break;
				}
			}
			model.addAttribute("editDiscount", editDiscount); // 角色是否能修改产品价格  1 可修改     非1  不可修改
		}
		catch (RuntimeException e) {

		}

		List<Map<String, Object>> costs = costService.findListByBillCode(order.getSn(),
				0);
		model.addAttribute("costJson", JsonUtils.toJson(costs));
		/* 备用地址信息 */
		String standbyAddress_json = JsonUtils.toJson(standbyAddressService.findListByOrderSn(order.getId()));
		model.addAttribute("standbyAddress_json", standbyAddress_json);

		List<Map<String, Object>> offsiteBranchs = offsiteBranchService.findListByBillCode(order.getSn(),
				0);
		model.addAttribute("offsiteBranchJson",
				JsonUtils.toJson(offsiteBranchs));

		return "/" + code + "/b2b/order/view";
	}

	/*
	 * 查看
	 */
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, String sn, Integer readOnly, Long sbuId,
					   Integer isEdit, ModelMap model) {
		 
	        
		StoreMember storeMember = storeMemberService.getCurrent();
		//默认外部用户
		Integer isMember = 1;
		if(storeMember.getMemberType()==0){
			isMember = 0;
		}
		model.addAttribute("isMember", isMember);
		model.addAttribute("readOnly", readOnly);

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);
		Long subId = null;
		Long storeId = null;
		Long organizationId = null;
		Long saleOrgId = null;
		if (order.getSbu() != null) {
			subId = order.getSbu().getId();
		}
		if (order.getStore() != null) {
			storeId = order.getStore().getId();
		}
		if (order.getOrganization() != null) {
			organizationId = order.getOrganization().getId();
		}
		if (order.getSaleOrg() != null) {
			saleOrgId = order.getSaleOrg().getId();
		}
		
		boolean isReject = true;
		
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService.findShippingItemListByOrderItemId(orderItem.getId()
					.toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity().compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
	
		model.addAttribute("isReject", isReject);
		model.addAttribute("isEdit", isEdit);
		model.addAttribute("storeMember", storeMember);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",shippingMethodBaseService.findList(null, filters, null));
		
		/* 客户经理 */
		if (order != null) {
			Store store = order.getStore();
			if (store != null) {
				model.addAttribute("storeManagerList", store.getStoreManagers());
			}
		}
		
		/* 订单明细 */
		List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString(),subId,null,null);
		for (int i = 0; i < orderItems.size(); i++) {
			String oid = orderItems.get(i).get("id").toString();
			orderItems.get(i).put("closed_quantity",orderCloseService.findClosedQuantityByOrderItemId(Long.parseLong(oid)).get("closed_quantity"));
			orderItems.get(i).put("closed_square", orderCloseService.findClosedQuantityByOrderItemId(Long.parseLong(oid)).get("closed_quantity_nb"));
		}
		orderItems = sort(orderItems, null);
		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);
		
		/* 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByDetailedId(id,null,null));
		model.addAttribute("payment_json", payment_json);
		/* 发货单 */
		String shipping_json = JsonUtils.toJson(shippingService.findShippingItemListByOrderId(id.toString()));
		model.addAttribute("shipping_json", shipping_json);
	
		/* 物流单 */
		String logistics_json = JsonUtils.toJson(logisticsService.findLogisticsListByOrderId(id.toString()));
		model.addAttribute("logistics_json", logistics_json);
		
		/* 订单附件 */
		String orderAttach_json = JsonUtils.toJson(orderAttachService.findListByOrderId(id));
		model.addAttribute("orderAttach_json", orderAttach_json);
	
		/* 备用地址信息 */
		String standbyAddress_json = JsonUtils.toJson(standbyAddressService.findListByOrderSn(order.getId()));
		model.addAttribute("standbyAddress_json", standbyAddress_json);
	
		/* 订单全链路 */
		String orderFullLink_json = JsonUtils.toJson(orderFullLinkService.findListByOrderSn(order.getSn()));
		model.addAttribute("orderFullLink_json", orderFullLink_json);
		
		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",WebUtils.getCurrentCompanyInfoId())));
		

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,filters,null);

		/*
		 * 经销商零售下拉框展示
		 */
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.eq("value", "经销商零售"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypeList = systemDictService.findList(null,filters,null);
		SystemDict businessType = null;
		if (businessTypeList != null && businessTypeList.size() > 0) {
			businessType = businessTypeList.get(0);
		}
		Long businessTypeIdj = businessType.getId();
		model.addAttribute("businessTypeIdj", businessTypeIdj);

		
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,filters,null);
		model.addAttribute("businessTypes", businessTypes);

		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}
	
		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,filters,null);
		model.addAttribute("organizations", organizations);

		/* 北京零微科技有限公司 */
		if (order.getCompanyInfoId() == 18) {
			model.addAttribute("isLw", true);
		}

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",WebUtils.getCurrentCompanyInfoId()));
		}catch (Exception e) {
			
		}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",WebUtils.getCurrentCompanyInfoId()));
		}catch (Exception e) {
			
		}
	
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		
		// 订单是否展示金额 0 不展示  非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); 
		}
		catch (RuntimeException e) {

		}
		
		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles); 
		}
		catch (RuntimeException e) {

		}
		
		// 角色是否能修改折扣率  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("editDiscount",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editDiscount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editDiscount++;
					break;
				}
			}
			model.addAttribute("editDiscount", editDiscount); 
		}
		catch (RuntimeException e) {

		}
		
		// 角色是否能修改产品价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("editProductPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editProductPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editProductPrice++;
					break;
				}
			}
			model.addAttribute("editProductPrice", editProductPrice); 
		}
		catch (RuntimeException e) {

		}
		
		// 角色是否能修改平台产品价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("editSaleOrgPrice", editSaleOrgPrice); 
		}
		catch (RuntimeException e) {
		}

        // 角色是否能查看平台产品价格  1 可修改     非1  不可修改
        try {
            String value = SystemConfig.getConfig("seeSaleOrgPriceRoles",
                    WebUtils.getCurrentCompanyInfoId());
            String[] role = value.split(",");
            List<String> list = Arrays.asList(role);
            int seeSaleOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    seeSaleOrgPrice++;
                    break;
                }
            }
            model.addAttribute("seeSaleOrgPrice", seeSaleOrgPrice);
        }
        catch (RuntimeException e) {
        }

		// 角色是否能查看价差  1 可修改     非1  不可修改
        parameterControl("seePriceDifference",model);
		// 角色是否能修改价差  1 可修改     非1  不可修改
        parameterControl("editPriceDifference", model);
		// 角色是否能查看其他价差  1 可修改     非1  不可修改
		parameterControl("seeOrderPriceDifference",model);
		// 角色是否能修改其他价差  1 可修改     非1  不可修改
		parameterControl("editOrderPriceDifference", model);

        // 角色是否能查看结算价价差  1 可修改     非1  不可修改
        parameterControl("seeOrgPriceDifference",model);

		// 订单下达是否展示色号、含水率、批次  0 不展示    非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch); 
		}
		catch (RuntimeException e) {

		}
		
		// 已审核完修改平台结算价做多角色参数控制 
		try {
			String value = SystemConfig.getConfig("auditedUpdateSaleOrgPrice",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int apdateSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					apdateSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("apdateSaleOrgPrice", apdateSaleOrgPrice); 
		}
		catch (RuntimeException e) {

		}
		
		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);
		
		// 判断是否拥有角色
		boolean role = storeService.findByRole(storeMember, "价格类型角色");
		if (role == true) {
			if (order.getMemberRank() != null) {
				MemberRank memberRank = memberRankBaseService.find(order.getMemberRank().getId());
				model.addAttribute("flag", true);
				model.addAttribute("memberRankId", memberRank.getId());
				model.addAttribute("memberRankName", memberRank.getName());
			}
		}else{
			model.addAttribute("memberRankId", "");
			model.addAttribute("memberRankName", "");
		}
		boolean seePay = storeService.findByRole(storeMember, "商务支付角色");
		model.addAttribute("seePay", seePay);
		
		filters.clear();
		filters.add(Filter.eq("code", "discountCode"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> discounts = systemDictService.findList(null,filters,null);
		model.addAttribute("discounts", discounts);
		
		//产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
		model.addAttribute("productLevelList", productLevelList);
		
		//运输方式
		filters.clear();
		filters.add(Filter.eq("code", "transportType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> transportTypeList = systemDictService.findList(null,filters,null);
		model.addAttribute("transportTypeList", transportTypeList);

		//出货类型
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("code", "outOfTheWarehouseType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> outOfTheWarehouseTypeList = systemDictService.findList(null,filters,null);
		model.addAttribute("outOfTheWarehouseTypeList", outOfTheWarehouseTypeList);

		//是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock",WebUtils.getCurrentCompanyInfoId());
		if(!ConvertUtil.isEmpty(linkStockValue)){
			Integer linkStock  = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}
		//优惠项目说明
		filters.clear();
		filters.add(Filter.eq("code", "discountProjectDescription"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> discountProjectDescriptionList = systemDictService.findList(null,filters,null);
		model.addAttribute("discountProjectDescriptionList", discountProjectDescriptionList);

		//色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null,filters,null);
		model.addAttribute("colorNumberList", colorNumberList);
		
		//含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null,filters,null);
		model.addAttribute("moistureContentList", moistureContentList);
		
		//订单管理是否启用库存查询 0不展示、1展示
		Integer orderStockQueryRoles = roleJurisdictionUtil.getRoleCount("orderStockQueryRoles");
		model.addAttribute("orderStockQueryRoles", orderStockQueryRoles);

        try {
            String value = SystemConfig.getConfig("editProductOrgPrices",
                    WebUtils.getCurrentCompanyInfoId());
            String[] roles = value.split(",");
            List<String> list = Arrays.asList(roles);
            int editProductOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    editProductOrgPrice++;
                    break;
                }
            }
            // 角色是否能修改产品部价格  1 可修改     非1  不可修改
            model.addAttribute("editProductOrgPrice", editProductOrgPrice);
        }
        catch (RuntimeException e) {

        }
		
		return "/b2b/order/view";
	}

	/*
	 * 订单列表
	 */
	@RequestMapping(value = "/orderlist", method = RequestMethod.GET)
	public String orderlist(Long[] ids, ModelMap model) {

		model.addAttribute("ids", ids);
		return "/b2b/order/orderlist";
	}

	@RequestMapping(value = "/orderlist_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg orderlist_data(Long[] ids, ModelMap model) {
		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < ids.length; i++) {
			if (i == ids.length - 1) {
				idss += ids[i];
			}
			else {
				idss += ids[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService.findListById(idss);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/*
	 * 订单明细列表
	 */
	@RequestMapping(value = "/itemlist", method = RequestMethod.GET)
	public String itemlist(Long[] itemIds, ModelMap model) {

		model.addAttribute("itemIds", itemIds);

		return "/b2b/order/itemlist";
	}

	@RequestMapping(value = "/itemlist_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg itemlist_data(Long[] itemIds, ModelMap model) {

		if (itemIds == null || itemIds.length == 0) {
			// 请选择订单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < itemIds.length; i++) {
			if (i == itemIds.length - 1) {
				idss += itemIds[i];
			}
			else {
				idss += itemIds[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService.findOrderItemListByItemId(idss);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/*
	 * 订单明细列表
	 */
	@RequestMapping(value = "/clear_itemlist", method = RequestMethod.GET)
	public String clearitemlist(Long[] itemIds, ModelMap model) {

		model.addAttribute("itemIds", itemIds);

		return "/b2b/order/clear_itemlist";
	}

	@RequestMapping(value = "/clear_itemlist_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg clear_itemlist_data(Long[] itemIds, ModelMap model) {

		if (itemIds == null || itemIds.length == 0) {
			// 请选择订单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < itemIds.length; i++) {
			if (i == itemIds.length - 1) {
				idss += itemIds[i];
			}
			else {
				idss += itemIds[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService.findOrderItemListByItemId(idss);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/*
	 * 审核
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check(Long orderId, Order order) {

		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}

		// 附件
		List<OrderAttach> orderAttachs = order.getOrderAttachs();
		orderService.check(orderId, orderAttachs);

		return success();
	}

	/*
	 * 批量审核
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/checks", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checks(Long orderId) {

		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}

		orderService.check(orderId, null);

		return success();
	}

	/**
	 * 审核
	 * @param orderId
	 * @return
	 */
	/**
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check_wf(Long orderId, Order order, Long objConfId) {
		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}
		// 附件
		List<OrderAttach> orderAttachs = order.getOrderAttachs();
		orderService.checkOrderWf(orderId, orderAttachs, objConfId);

		return success();
	}*/
	
	/**
	 * 审核
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check_wf(Long orderId, Order order, String modelId, Long objTypeId) {
		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}
		// 附件
		List<OrderAttach> orderAttachs = order.getOrderAttachs();
		orderService.checkOrderWf(orderId, orderAttachs,modelId, objTypeId);

		return success();
	}

	/*
	 * 分配仓库
	 * @param id
	 * @param warehouseId
	 * @return
	 */
	@RequestMapping(value = "/set_warehouse", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg setWarehouse(Long id, Long warehouseId) {

		if (id == null) {
			// 请选择订单
			return error("15140");
		}
		if (warehouseId == null) {
			// 请选择仓库
			return error("15141");
		}

		OrderItem orderItem = orderItemService.find(id);
		Order order = orderItem.getOrder();
		if (!order.getOrderStatus().equals(OrderStatus.unaudited)) {
			// 只允许未审核状态的订单进行分配仓库
			return error("15143");
		}
		orderService.setWarehouse(id, warehouseId);

		return success();
	}

	/*
	 * 查询订单列表
	 */
	@RequestMapping(value = "/select_order", method = RequestMethod.GET)
	public String selectOrder(Integer orderStatus, Integer paymentStatus,
							  Integer[] shippingStatus, Integer isReturn, Pageable pageable,
							  ModelMap model) {

		model.addAttribute("paymentStatus", paymentStatus);
		model.addAttribute("shippingStatus", shippingStatus);
		model.addAttribute("orderStatus", orderStatus);
		model.addAttribute("isReturn", isReturn);
		return "/b2b/order/select_order";
	}

	/*
	 * 查询订单列表数据
	 */
	@RequestMapping(value = "/select_order_data", method = RequestMethod.GET)
	public @ResponseBody
	ResultMsg select_order_data(String orderSn, String outTradeNo,
								Integer[] orderStatus, Integer[] exStatus,
								Integer[] shippingStatus, Integer[] paymentStatus, Integer[] flag,
								Long warehouseId, Long[] storeId, String phone, String consignee,
								String address, Long deliveryCorpId, Long productId[],
								String firstTime, String lastTime, Integer[] confirmStatus,
								Integer isReturn, String store_member_name, Long saleOrgId,
								Long organizationId, Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = orderService.findPage(orderSn,
				outTradeNo,
				orderStatus,
				shippingStatus,
				null,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				isReturn, // 1 退货单查订单用
				store_member_name, // 去掉已经全部做了退货单的订单
				saleOrgId,
				organizationId,
				pageable);

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/*
	 * 获取订单信息
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/get_order_info", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getOrderInfo(Long id, Integer flag) {

		Map<String, Object> order = orderService.findListById(id.toString())
				.get(0);
		List<Map<String, Object>> items = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
		order.put("items", items);

		if (flag != null && flag.intValue() == 1) {
			List<Map<String, Object>> shipping_items = shippingService.findShippingItemListByOrderId(id.toString());
			order.put("shipping_items", shipping_items);
		}

		String jsonPage = JsonUtils.toJson(order);
		return success(jsonPage);
	}

	/*
	 * 购物车生成订单预览
	 * @param cartId
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/info", method = RequestMethod.GET)
	public String info(Long cartId, Long[] ids, ModelMap model) {

		Cart cart = cartService.find(cartId);
		List<CartItem> cartItems = new ArrayList<CartItem>();
		CartItem pCartItem = cartItemService.find(ids[0]);
		if (cart == null) {
			cart = pCartItem.getCart();
		}
		if (cart == null || cart.isEmpty()) {
			// 购物车已经空了
			return errorV(model, error("15126"));
		}
		for (Long id : ids) {
			CartItem cartItem = cartItemService.find(id);
			if (!cartItem.getCart().equals(cart)) {
				// 只允许选择相同客户的产品
				return errorV(model, error("15128"));
			}
			cartItems.add(cartItem);
		}

		Order order = orderService.build(cart.getStore(), cart, cartItems);
		model.addAttribute("order", order);
		model.addAttribute("cart", cart);
		model.addAttribute("cartItems", cartItems);
		Store store = cart.getStore();
		if (!store.getIsMainStore()) {
			model.addAttribute("store", store);
		}
		else {
			Member member = storeMemberService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
			if (storeMembers != null) {
				for (StoreMember storeMember : storeMembers) {
					store = storeMember.getStore();
					if (store.getType().equals(Store.Type.distributor)) {
						model.addAttribute("store", store);
						break;
					}
				}
			}
		}

		model.addAttribute("paymentMethods", paymentMethodBaseService.findAll());

		if (store != null && !store.getIsMainStore()) {
			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
					order.getOrganization() == null ? null
							: order.getOrganization().getId(),
					order.getSbu().getId());
			model.addAttribute("balance", balance);
		}

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (OrderItem orderItem : order.getOrderItems()) {
			Product product = orderItem.getProduct();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("id", product.getId());
			data.put("vonder_code", product.getVonderCode());
			data.put("model", product.getModel());
			data.put("name", product.getName());
			data.put("price", orderItem.getPrice());
			data.put("origPrice", orderItem.getOrigPrice());
			data.put("quantity", orderItem.getQuantity());
			data.put("bar_code", product.getBarCode());

			Map<String, Object> map = priceApplyService.findItemByProduct(product.getId(),
					store.getId());
			if (map != null) {
				data.put("apply_sn", map.get("sn"));
				data.put("apply_price", map.get("price"));
				data.put("apply_item_id", map.get("id"));
				BigDecimal useable_quantity = new BigDecimal(
						map.get("quantity").toString()).subtract(new BigDecimal(
						map.get("used_quantity").toString()));
				data.put("apply_useable_quantity", useable_quantity);
			}

			if (store.getMemberRank() != null) {
				Map<String, Object> productPrice = productPriceHeadService.findPriceByProductStore(orderItem.getProductStore()
								.getId(),
						store.getMemberRank().getId());
				if (productPrice != null) {
					data.put("member_price",
							new BigDecimal(
									productPrice.get("store_member_price")
											.toString()));
				}
				else {
					data.put("member_price", product.getPrice());
				}
			}
			else {
				data.put("member_price", product.getPrice());
			}

			list.add(data);
		}
		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		return "/b2b/order/info";
	}

	/*
	 * 购物车生成订单创建
	 * @param cartId
	 * @param ids
	 * @return
	 */
	@RequestMapping(value = "/create", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg create(Long storeId, Long cartId, Long[] ids, Long areaId,
					 Long freightChargeTypeId, Order order, Long paymentMethodId,
					 Long shippingMethodId, Long organizationId) {

		Store store = storeService.find(storeId);
		if (store == null) {
			// 请选择客户
			return error("15135");
		}
		Area area = areaService.find(areaId);
		if (area == null) {
			// 请选择收货地区
			return error("15136");
		}
		if (ConvertUtil.isEmpty(order.getAddress())) {
			// 请填写收货地址
			return error("15137");
		}
		if (ConvertUtil.isEmpty(order.getConsignee())) {
			// 请填写收货人
			return error("15138");
		}
		if (ConvertUtil.isEmpty(order.getPhone())) {
			// 请填写收货人电话
			return error("15139");
		}
		PaymentMethod paymentMethod = paymentMethodBaseService.find(paymentMethodId);
		if (paymentMethod == null) {
			// 请选择支付方式
			return error("151040");
		}
		ShippingMethod shippingMethod = shippingMethodBaseService.find(shippingMethodId);
		if (shippingMethod == null) {
			// 请选择配送方式
			return error("151041");
		}
		Cart cart = cartService.find(cartId);
		if (cart == null || cart.isEmpty()) {
			// 购物车已清空
			return error("15126");
		}
		List<CartItem> cartItems = new ArrayList<CartItem>();
		for (Long id : ids) {
			cartItems.add(cartItemService.find(id));
		}
		SystemDict freightChargeType = null;
		if (freightChargeTypeId != null) {
			freightChargeType = systemDictService.find(freightChargeTypeId);
		}
		orderService.create(store,
				cart,
				cartItems,
				area,
				order,
				paymentMethod,
				shippingMethod,
				freightChargeType,
				organizationId);

		return success();
	}

	/*
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String addCode(@PathVariable String code, Long[] ids,
						  Long warehouseId, ModelMap model) {
		// 当前月最后一天
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar cale = Calendar.getInstance();
		cale.add(Calendar.MONTH, 1);
		cale.set(Calendar.DAY_OF_MONTH, 0);
		String lastday = format.format(cale.getTime());
		model.addAttribute("lastDay", lastday);

		model.addAttribute("code", code);
		Store store = null;
		if (ids != null && ids.length > 0) {
			CartItem pCartItem = cartItemService.find(ids[0]);
			Cart cart = pCartItem.getCart();
			List<CartItem> cartItems = new ArrayList<CartItem>();
			for (Long id : ids) {
				CartItem cartItem = cartItemService.find(id);
				if (!cartItem.getCart().equals(cart)) {
					// 只允许选择相同客户的产品
					return errorV(model, error("15128"));
				}
				cartItems.add(cartItem);
			}

			model.addAttribute("cartItems", cartItems);

			Order order = orderService.build(cart.getStore(), cart, cartItems);
			Warehouse warehouse = warehouseService.find(warehouseId);
			order.setWarehouse(warehouse);
			model.addAttribute("order", order);
			model.addAttribute("cart", cart);
			model.addAttribute("cartItems", cartItems);
			store = cart.getStore();
			if (!store.getIsMainStore()) {
				model.addAttribute("store", store);
			}
			else {
				Member member = storeMemberService.getCurrent().getMember();
				List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
				if (storeMembers != null) {
					for (StoreMember storeMember : storeMembers) {
						store = storeMember.getStore();
						if (store.getType().equals(Store.Type.distributor)) {
							model.addAttribute("store", store);
							break;
						}
						else {
							store = null;
						}
					}
				}
			}

			List<OrderItem> orderItems = order.getOrderItems();
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			for (int i = 0; i < orderItems.size(); i++) {
				OrderItem orderItem = orderItems.get(i);
				Product product = orderItem.getProduct();
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("id", product.getId());
				data.put("vonder_code", product.getVonderCode());
				data.put("model", product.getModel());
				data.put("name", product.getName());
				data.put("price", orderItem.getPrice());
				data.put("origPrice", orderItem.getOrigPrice());
				data.put("quantity", orderItem.getQuantity());
				data.put("discount", orderItem.getDiscount());
				data.put("bar_code", product.getBarCode());
				data.put("cartItemId", cartItems.get(i).getId());
				Map<String, Object> map = priceApplyService.findItemByProduct(product.getId(),
						store.getId());
				if (map != null) {
					data.put("apply_sn", map.get("sn"));
					data.put("apply_price", map.get("price"));
					data.put("apply_item_id", map.get("id"));
					BigDecimal useable_quantity = new BigDecimal(
							map.get("quantity").toString()).subtract(new BigDecimal(
							map.get("used_quantity").toString()));
					data.put("apply_useable_quantity", useable_quantity);
				}

				if (store.getMemberRank() != null) {
					Map<String, Object> productPrice = productPriceHeadService.findPriceByProductStore(orderItem.getProductStore()
									.getId(),
							store.getMemberRank().getId());
					if (productPrice != null) {
						data.put("member_price", new BigDecimal(
								productPrice.get("store_member_price")
										.toString()));
					}
					else {
						data.put("member_price", product.getPrice());
					}
				}
				else {
					data.put("member_price", product.getPrice());
				}

				list.add(data);
			}
			String jsonStr = JsonUtils.toJson(list);
			model.addAttribute("jsonStr", jsonStr);

		}
		else {
			Member member = storeMemberService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
			if (storeMembers != null) {
				for (StoreMember storeMember : storeMembers) {
					store = storeMember.getStore();
					if (store.getType().equals(Store.Type.distributor)) {
						model.addAttribute("store", store);
						break;
					}
					else {
						store = null;
					}
				}
			}
		}

		model.addAttribute("paymentMethods", paymentMethodBaseService.findAll());

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));
		model.addAttribute("companyId", WebUtils.getCurrentCompanyInfoId());

		if (store != null && !store.getIsMainStore()) {
			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
					null,
					null);
			model.addAttribute("balance", balance);
			filters.clear();
			filters.add(Filter.eq("store", store));
			Pageable pageable = new Pageable();
			Page<Map<String, Object>> storeAddressList = storeService.findStoreAddressPage(null,
					null,
					store.getId(),
					null,
					null,
					pageable);
			if (storeAddressList != null
					&& storeAddressList.getContent().size() > 0) {
				model.addAttribute("storeAddress",
						storeAddressList.getContent().get(0));
			}
		}
		if (store != null) {
			model.addAttribute("storeManagerList", store.getStoreManagers());
		}
		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);

		//币种
		filters.clear();
		filters.add(Filter.eq("code", "Currency"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> currencies = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("currencies", currencies);
		// 用户默认的所属机构
		// filters.clear();
		// filters.add(Filter.eq("storeMember",
		// storeMemberService.getCurrent()));
		// filters.add(Filter.eq("isDefault", true));
		// List<StoreMemberSaleOrg> storeMemberSaleOrgs =
		// storeMemberSaleOrgService.findList(null, filters, null);
		// SaleOrg saleOrg = null;
		// if (storeMemberSaleOrgs != null && storeMemberSaleOrgs.size() > 0) {
		// saleOrg = storeMemberSaleOrgs.get(0).getSaleOrg();
		// }
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		// 政策金额
		model.addAttribute("policyAmount", policyCountService.findCount());

		//订单分类
		List<Filter> filterss = new ArrayList<Filter>();
		filterss.add(Filter.eq("code", "OrderClassify"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> orderClassifys = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("orderClassifys", orderClassifys);

		//订单类型
		filterss.clear();
		filterss.add(Filter.eq("code", "OrderStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> orderStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("orderStyles", orderStyles);

		//贸易类型
		filterss.clear();
		filterss.add(Filter.eq("code", "TradeStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> tradeStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("tradeStyles", tradeStyles);

		//销售体系
		filterss.clear();
		filterss.add(Filter.eq("code", "SaleStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> saleStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("saleStyles", saleStyles);

		//行业类别industryStyle
		filterss.clear();
		filterss.add(Filter.eq("code", "IndustryStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> industryStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("industryStyles", industryStyles);

		//付款方式paymentStyle
		filterss.clear();
		filterss.add(Filter.eq("code", "PaymentStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> paymentStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("paymentStyles", paymentStyles);

		//费用类型 -系统词汇
		filterss.clear();
		filterss.add(Filter.eq("code", "CostType"));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> costTypes = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("costTypes", costTypes);

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		filters.clear();
		filters.add(Filter.eq("storeMember",
				storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示     非0 展示
		}
		catch (RuntimeException e) {

		}

		try {
			String value = SystemConfig.getConfig("editProductPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editProductPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editProductPrice++;
					break;
				}
			}
			model.addAttribute("editProductPrice", editProductPrice); // 角色是否能修改产品价格  1 可修改     非1  不可修改
		}
		catch (RuntimeException e) {

		}

		return "/" + code + "/b2b/order/add";
	}

	/*
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long[] ids, Long orderId, Long warehouseId, Long sbuId,
					  Long saleOrgId, Long organizationId,Long bussinessTypeId,
					  Long shippingMethodId,String isCopy, ModelMap model) {

		Store store = null;
		List<Filter> filters = new ArrayList<Filter>();
		if (orderId != null) {
			Order order = orderService.find(orderId);
			store = order.getStore();
			model.addAttribute("order", order);
			model.addAttribute("store", store);
			/* 订单明细 */
			List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(order.getId().toString(),null,null,null);
			StringBuilder  productIds = new StringBuilder();
			for (int i = 0; i < orderItems.size(); i++) {
				String oid = orderItems.get(i).get("id").toString();
				orderItems.get(i).put("closed_quantity",orderCloseService.findClosedQuantityByOrderItemId(Long.parseLong(oid)).get("closed_quantity"));
				productIds.append(orderItems.get(i).get("product").toString());
				if(orderItems.size()>1){
					productIds.append(",");
				}
			}
			if(productIds.length()>1){
				productIds.deleteCharAt(productIds.length()-1);
			}
			model.addAttribute("productIds", productIds);
			orderItems = sort(orderItems, null);
			String jsonStr = JsonUtils.toJson(orderItems);
			model.addAttribute("jsonStr", jsonStr);
		}else if (ids != null && ids.length > 0) {
			CartItem pCartItem = cartItemService.find(ids[0]);
			Cart cart = pCartItem.getCart();
			List<CartItem> cartItems = new ArrayList<CartItem>();
			for (Long id : ids) {
				CartItem cartItem = cartItemService.find(id);
				if (!cartItem.getCart().equals(cart)) {
					// 只允许选择相同客户的产品
					return errorV(model, error("15128"));
				}
				cartItems.add(cartItem);
			}
			model.addAttribute("cartItems", cartItems);
			Order order = orderService.build(cart.getStore(), cart, cartItems);
			Warehouse warehouse = warehouseService.find(warehouseId);
			order.setWarehouse(warehouse);
			model.addAttribute("order", order);
			model.addAttribute("cart", cart);
			model.addAttribute("cartItems", cartItems);
			store = cart.getStore();
			if (!store.getIsMainStore()) {
				model.addAttribute("store", store);
			}else {
				Member member = storeMemberService.getCurrent().getMember();
				List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
				if (storeMembers != null) {
					for (StoreMember storeMember : storeMembers) {
						store = storeMember.getStore();
						if (store.getType().equals(Store.Type.distributor)) {
							model.addAttribute("store", store);
							break;
						}else {
							store = null;
						}
					}
				}
			}
			List<OrderItem> orderItems = order.getOrderItems();
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			for (int i = 0; i < orderItems.size(); i++) {
				OrderItem orderItem = orderItems.get(i);
				Product product = orderItem.getProduct();
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("id", product.getId());
				data.put("description", product.getDescription());
				data.put("product_category_name",product.getProductCategory() == null ? "" : product.getProductCategory().getName());
				data.put("volume", product.getVolume());
				data.put("weight", product.getWeight());
				data.put("vonder_code", product.getVonderCode());
				data.put("model", product.getModel());
				data.put("name", product.getName());
				data.put("price", orderItem.getPrice());
				data.put("discount", orderItem.getDiscount());
				data.put("origPrice", orderItem.getOrigPrice());
				data.put("quantity", orderItem.getQuantity());
				data.put("unit", product.getUnit());
				data.put("box_quantity", orderItem.getBoxQuantity());
				data.put("branch_quantity", orderItem.getBranchQuantity());
				data.put("branch_per_box", product.getBranchPerBox());
				data.put("per_branch", product.getPerBranch());
				data.put("bar_code", product.getBarCode());
				data.put("sale_org_price", orderItem.getSaleOrgPrice());
				data.put("product_category_id",product.getProductCategory() == null ? "" : product.getProductCategory().getId());
				data.put("cartItemId", cartItems.get(i).getId());
				data.put("level_Id", orderItem.getProductLevel()== null ? "": orderItem.getProductLevel().getId());//订单明细带入购物车产品等级
				Map<String, Object> map = priceApplyService.findItemByProduct(product.getId(),store.getId());
				if (map != null) {
					data.put("apply_sn", map.get("sn"));
					data.put("apply_price", map.get("price"));
					data.put("apply_item_id", map.get("id"));
					BigDecimal useable_quantity = new BigDecimal(map.get("quantity").toString()).subtract(new BigDecimal(map.get("used_quantity").toString()));
					data.put("apply_useable_quantity", useable_quantity);
				}
				Sbu sbu = order.getSbu();
				//获取客户当前默认价格类型
				filters.add(Filter.eq("sbu", sbu));
				filters.add(Filter.eq("store", store));
				List<StoreSbu> storeSbu=  storeSbuService.findList(null, filters, null);
				if (storeSbu != null && storeSbu.size()>0) {
					MemberRank memberRank = storeSbu.get(0).getMemberRank();
					Map<String, Object> productPrice = productPriceHeadService.findPriceByProductStore(orderItem.getProductStore().getId(),memberRank.getId());
					if (productPrice != null) {
						data.put("member_price", new BigDecimal(productPrice.get("store_member_price").toString()));
					}else {
						data.put("member_price", product.getPrice());
					}
				}else {
					data.put("member_price", product.getPrice());
				}
				list.add(data);
			}
			String jsonStr = JsonUtils.toJson(list);
			model.addAttribute("jsonStr", jsonStr);
		}else {
			Member member = storeMemberService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
			if (storeMembers != null) {
				for (StoreMember storeMember : storeMembers) {
					store = storeMember.getStore();
					if (store.getType().equals(Store.Type.distributor)) {
						model.addAttribute("store", store);
						break;
					}else {
						store = null;
					}
				}
			}
		}
		model.addAttribute("paymentMethods", paymentMethodBaseService.findAll());
		//查询金额
		if (sbuId != null && organizationId != null && saleOrgId != null) {
			Map<String, Object> map = storeBalanceService.findBalanceSbu(store.getId(),sbuId,organizationId,saleOrgId);
			if (map != null) {
				model.addAttribute("flas", 0);
				//可用余额
				if (map.get("balance") != null) {
					model.addAttribute("balances", map.get("balance"));
				}
				//余额
				if (map.get("yue") != null) {
					BigDecimal balance = new BigDecimal(map.get("balance").toString());
					BigDecimal credit_amount = new BigDecimal(map.get("credit_amount").toString());
					BigDecimal yue = balance.subtract(credit_amount);
					model.addAttribute("yue", yue);
				}
				//授信
				if (map.get("credit_amount") != null) {
					model.addAttribute("credit", map.get("credit_amount"));
				}
				//差额
				if (map.get("balance") != null && map.get("amount_paid") != null) {
					Order order = orderService.find(orderId);
					BigDecimal balance = new BigDecimal(map.get("balance").toString());
					BigDecimal chae = balance.subtract(order.getAmount());
					model.addAttribute("chae", chae);
				}
			}
		}
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",shippingMethodBaseService.findList(null, filters, null));
		model.addAttribute("appProductChangePrice",Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",WebUtils.getCurrentCompanyInfoId())));
		model.addAttribute("companyId", WebUtils.getCurrentCompanyInfoId());
		if (store != null && !store.getIsMainStore()) {
			model.addAttribute("balance", 0);
			filters.clear();
			filters.add(Filter.eq("store", store));
			Pageable pageable = new Pageable();
			Page<Map<String, Object>> storeAddressList = storeService.findStoreAddressPage(null,null,store.getId(),null,null,pageable);
			if (storeAddressList != null && storeAddressList.getContent().size() > 0) {
				model.addAttribute("storeAddress",storeAddressList.getContent().get(0));
			}
		}
		if (store != null) {
			model.addAttribute("storeManagerList", store.getStoreManagers());
		}
		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,filters,null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);
		SystemDict businessType = null;
		if(store != null && store.getBusinessType()!= null){
			businessType=store.getBusinessType();
		}else{
			filters.clear();
			filters.add(Filter.eq("code", "businessType"));
			filters.add(Filter.eq("value", "经销商零售"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> businessTypeList = systemDictService.findList(null,filters,null);
			if (businessTypeList != null && businessTypeList.size() > 0) {
				businessType = businessTypeList.get(0);
			}
		}
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,filters,null);
		Long businessTypeId = businessType.getId();
		model.addAttribute("businessTypes", businessTypes);
		model.addAttribute("businessTypeId", businessTypeId);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("isMember", storeMember.getMemberType());
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,filters,null);
		model.addAttribute("organizations", organizations);
		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",WebUtils.getCurrentCompanyInfoId()));
		}catch (Exception e) {
			
		}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);
		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",WebUtils.getCurrentCompanyInfoId()));
		}catch (Exception e) {
			
		}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		// 订单是否展示金额 0 不展示    非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); 
		}catch (RuntimeException e) {

		}
		try {
			String value = SystemConfig.getConfig("editProductPriceRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editProductPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editProductPrice++;
					break;
				}
			}
			model.addAttribute("editProductPrice", editProductPrice); // 角色是否能修改产品价格  1 可修改     非1  不可修改
		}catch (RuntimeException e) {

		}
		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);
		Warehouse warehouses = warehouseService.find(warehouseId);
		if(warehouses!=null){
			model.addAttribute("warehouseSbuId",warehouses.getId());
			model.addAttribute("warehouseSbuName",warehouses.getName());
			if(!ConvertUtil.isEmpty(warehouses.getProductionPlant())){
				model.addAttribute("production_plant",warehouses.getProductionPlant().getId());
			}
		}else{
			//仓库根据sbu预留字段3带出默认
			if (sbu != null && sbu.getUndefined3() != null) {
				if (sbu.getUndefined3().contains("defaultWarehouse")) {
					String string=sbu.getUndefined3();
					String warehouse = string.substring(string.indexOf("'")+1,string.lastIndexOf("'"));
					Map<String, Object> findWarehouse = orderService.findWarehouse(warehouse);
					if (findWarehouse != null && findWarehouse.get("id") != null&&warehouseId==null) {
						BigInteger findId = (BigInteger) findWarehouse.get("id");
						Long warehouseSbuId=findId.longValue();
						model.addAttribute("warehouseSbuId", warehouseSbuId);
					}
					if (findWarehouse != null && findWarehouse.get("name") != null&&warehouseId==null) {
						String warehouseSbuName = (String) findWarehouse.get("name");
						model.addAttribute("warehouseSbuName", warehouseSbuName);
					}
					if (findWarehouse != null && findWarehouse.get("type_system_dict_id") != null) {
						BigInteger type_system_dict_id = (BigInteger) findWarehouse.get("type_system_dict_id");
						model.addAttribute("type_system_dict_id", type_system_dict_id);
					}
					if (findWarehouse != null && findWarehouse.get("type_system_dict_value") != null) {
						String type_system_dict_value = (String) findWarehouse.get("type_system_dict_value");
						model.addAttribute("type_system_dict_value", type_system_dict_value);
					}
					if (findWarehouse != null && findWarehouse.get("management_organization_id") != null) {
						BigInteger management_organization_id = (BigInteger) findWarehouse.get("management_organization_id");
						model.addAttribute("management_organization_id", management_organization_id);
					}
					if (findWarehouse != null && findWarehouse.get("management_organization_name") != null) {
						String management_organization_name = (String) findWarehouse.get("management_organization_name");
						model.addAttribute("management_organization_name", management_organization_name);
					}
					if (!ConvertUtil.isEmpty(findWarehouse) && !ConvertUtil.isEmpty(findWarehouse.get("production_plant"))) {
						String production_plant = (String) findWarehouse.get("production_plant");
						model.addAttribute("production_plant", production_plant);
					}
				}
			}
		}
		boolean role = storeService.findByRole(storeMember, "价格类型角色");
		if(role == true) {
			model.addAttribute("role", role);
			// 获取当前客户的Sbu价格类型
			filters.clear();
			filters.add(Filter.eq("store", store));
			filters.add(Filter.eq("sbu", sbu));
			List<StoreSbu> storeSbus = storeSbuService.findList(null, filters, null);
			if (storeSbus.size() > 0) {
				for (StoreSbu storeSbu : storeSbus) {
					MemberRank memberRank = storeSbu.getMemberRank();
					model.addAttribute("memberRank", memberRank);
				}
			}
		}
		
		// 角色是否能修改折扣率  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("editDiscount",WebUtils.getCurrentCompanyInfoId());
			String[] roles = value.split(",");
			List<String> list = Arrays.asList(roles);
			int editDiscount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editDiscount++;
					break;
				}
			}
			model.addAttribute("editDiscount", editDiscount); 
		}catch (RuntimeException e) {

		}
		
		// 订单下达是否展示色号、含水率、批次  0 不展示    非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch); 
		}catch (RuntimeException e) {

		}
		
		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles); 
		}catch (RuntimeException e) {

		}
		
		// 角色是否能修改平台产品价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int editSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("editSaleOrgPrice", editSaleOrgPrice); 
		}catch (RuntimeException e) {
		}

		// 角色是否能查看平台产品价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("seeSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] roles = value.split(",");
			List<String> list = Arrays.asList(roles);
			int seeSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					seeSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("seeSaleOrgPrice", seeSaleOrgPrice);
		}
		catch (RuntimeException e) {
		}
		// 角色是否能查看价差  1 可修改     非1  不可修改
        parameterControl("seePriceDifference", model);
		// 角色是否能修改价差  1 可修改     非1  不可修改
        parameterControl("editPriceDifference",model);
		// 角色是否能查看其他价差  1 可修改     非1  不可修改
		parameterControl("seeOrderPriceDifference",model);
		// 角色是否能修改其他价差  1 可修改     非1  不可修改
		parameterControl("editOrderPriceDifference", model);
        // 角色是否能查看结算价价差
        parameterControl("seeOrgPriceDifference",model);


        filters.clear();
		filters.add(Filter.eq("code", "discountCode"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> discounts = systemDictService.findList(null,filters,null);
		model.addAttribute("discounts", discounts);
		
		//产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null,filters,null);
		model.addAttribute("productLevelList", productLevelList);
		
		//运输方式
		filters.clear();
		filters.add(Filter.eq("code", "transportType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> transportTypeList = systemDictService.findList(null,filters,null);
		model.addAttribute("transportTypeList", transportTypeList);

        //出货类型
        filters.clear();
		filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.eq("code", "outOfTheWarehouseType"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> outOfTheWarehouseTypeList = systemDictService.findList(null,filters,null);
        model.addAttribute("outOfTheWarehouseTypeList", outOfTheWarehouseTypeList);
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("defaultFiltering", true));
		filters.add(Filter.eq("code", "outOfTheWarehouseType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> outOfTheWarehouseTypedefaultFiltering = systemDictService.findList(null,filters,null);
		model.addAttribute("outOfTheWarehouseTypedefaultFiltering", outOfTheWarehouseTypedefaultFiltering.size()<1?null:outOfTheWarehouseTypedefaultFiltering.get(0));

		//是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock",WebUtils.getCurrentCompanyInfoId());
		if(!ConvertUtil.isEmpty(linkStockValue)){
			Integer linkStock  = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}
		//优惠项目说明
		filters.clear();
		filters.add(Filter.eq("code", "discountProjectDescription"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> discountProjectDescriptionList = systemDictService.findList(null,filters,null);
		model.addAttribute("discountProjectDescriptionList", discountProjectDescriptionList);

		//色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null,filters,null);
		model.addAttribute("colorNumberList", colorNumberList);
		
		//含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null,filters,null);
		model.addAttribute("moistureContentList", moistureContentList);
		
		//订单管理是否启用库存查询 0不展示、1展示
		Integer orderStockQueryRoles = roleJurisdictionUtil.getRoleCount("orderStockQueryRoles");
		model.addAttribute("orderStockQueryRoles", orderStockQueryRoles);

        try {
            String value = SystemConfig.getConfig("editProductOrgPrices",
                    WebUtils.getCurrentCompanyInfoId());
            String[] roles = value.split(",");
            List<String> list = Arrays.asList(roles);
            int editProductOrgPrice = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    editProductOrgPrice++;
                    break;
                }
            }
            // 角色是否能修改产品部价格  1 可修改     非1  不可修改
            model.addAttribute("editProductOrgPrice", editProductOrgPrice);
        }
        catch (RuntimeException e) {

        }

		
		return "/b2b/order/add";
	}

	/*
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(Order order, Long storeId, Long salemanId, Long currencyId,
				   Long regionalManagerId, Long areaId, Long organizationId,
				   Long salesmanId, Long businessTypeId, Long freightChargeTypeId,
				   Long saleOrgId, Long paymentMethodId, Long shippingMethodId,
				   Long warehouseId, Long cartId, Long[] ids, Integer stat,
				   Long orderClassifyId, Long orderStyleId, Long distributorId,
				   Long tradeStyleId, Long saleStyleId, Long industryStyleId,
				   Date factorydate, Date qualitydate, Long paymentStyleId, 
				   Long sbuId, Long memberRankId,Long discountId,Long outOfTheWarehouseTypeId,
				   HttpServletRequest request,HttpServletResponse response, 
				   HttpSession session, Long transportTypeId,Long contractId, String aftersaleSn) {

        List<Filter> filters = new ArrayList<Filter>();
        //判断订单中的产品与产品资料中的转换率是否一样
//		for (OrderItem orderItem:order.getOrderItems()){
//            if(orderItem.getProduct()!=null) {
//                Product product = productService.find(orderItem.getProduct().getId());
//                if (orderItem.getBranchPerBox().compareTo(product.getBranchPerBox()) != 0) {
//                    ExceptionUtil.throwServiceException("订单中的【" + product.getVonderCode() + "】产品与产品资料的转换率不相符");
//                }
//            }
//		}


		Store store = storeService.find(storeId);
		Store distributor =null;
		if(distributorId!=null){
			 distributor = storeService.find(distributorId);
		}
		
	   if(!checkOrdering(store)){
//				ExceptionUtil.throwControllerException(store.getName()+"欠缴保证金，不能下单");
			ExceptionUtil.throwControllerException("您还尚未清缴大自然品牌保证金，请先清缴后再来下单");
		}
		if(StringUtils.isNotEmpty(aftersaleSn) &&
				orderService.exists(Filter.eq("fourAftersaleSn", aftersaleSn),
						Filter.ne("orderStatus", Order.OrderStatus.cancelled))){
			return error("该售后单已经被关联！");
		}
		StoreMember storeMember = storeMemberService.getCurrent();

		if (store == null) {
			// 请选择客户
			return error("15135");
		}
		StoreMember saleman = storeMemberService.find(salemanId);
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		if (saleOrg == null) {
			return error("请维护机构");
		}
		Area area = areaService.find(areaId);
		if (area == null) {
			// 请选择收货地区
			return error("15136");
		}
		if (ConvertUtil.isEmpty(order.getAddress())) {
			// 请填写收货地址
			return error("15137");
		}
		if (ConvertUtil.isEmpty(order.getConsignee())) {
			// 请填写收货人
			return error("15138");
		}
		if (ConvertUtil.isEmpty(order.getPhone())) {
			// 请填写收货人电话
			return error("15139");
		}
		for(OrderItem item : order.getOrderItems()){
			if(item.getPriceDifference() != null && !"0".equals(item.getPriceDifference().toString()) && item.getDiscountProjectDescription().getId() == null){
				// 请选择优惠项目说明
				return error("请选择优惠项目说明");
			}
			if(item.getDiscountProjectDescription() != null && item.getDiscountProjectDescription().getId() == null){
				item.setDiscountProjectDescription(null);
			}
		}
		PaymentMethod paymentMethod = paymentMethodBaseService.find(paymentMethodId);
		if (paymentMethod == null) {
			// 请选择支付方式
			return error("151040");
		}
		ShippingMethod shippingMethod = shippingMethodBaseService.find(shippingMethodId);
		if (shippingMethod == null) {
			// 请选择配送方式
			return error("151041");
		}
		if (freightChargeTypeId != null) {
			SystemDict freightChargeType = systemDictService.find(freightChargeTypeId);
			order.setFreightChargeType(freightChargeType);
		}

		if (businessTypeId != null) {
			SystemDict businessType = systemDictService.find(businessTypeId);
			order.setBusinessType(businessType);
		}
		if (warehouseId != null) {
			Warehouse warehouse = warehouseService.find(warehouseId);
			order.setWarehouse(warehouse);
		}
		if (discountId != null) {
			order.setDiscountCode(systemDictService.find(discountId));
		}
		
		if (contractId != null) {
			order.setCustomerContract(customerContractService.find(contractId));
		}

		SystemDict currency = systemDictService.find(currencyId);
		order.setCurrency(currency);
		Cart cart = cartService.find(cartId);
		if (cart != null && cart.isEmpty()) {
			// 购物车已清空
			return error("15126");
		}
		List<CartItem> cartItems = new ArrayList<CartItem>();
		if (ids != null) {
			for (Long id : ids) {
				cartItems.add(cartItemService.find(id));
			}
		}
		if (salesmanId != null) {
			order.setSalesman(storeMemberService.find(salesmanId));
		}
		StoreMember regionalManager = storeMemberService.find(regionalManagerId);
		order.setRegionalManager(regionalManager);
		order.setSaleOrg(saleOrgService.find(saleOrgId));
		Organization organization = organizationService.find(organizationId);
		order.setOrganization(organization);
		order.setQualityDate(qualitydate);
		
		 String ip = request.getHeader("x-forwarded-for"); 
	        
	        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {  
	            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
	            if( ip.indexOf(",")!=-1 ){
	                ip = ip.split(",")[0];
	            }
	        }  
	        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	            ip = request.getHeader("Proxy-Client-IP");  
	            
	        }  
	        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	            ip = request.getHeader("WL-Proxy-Client-IP");  
	            
	        }  
	        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	            ip = request.getHeader("HTTP_CLIENT_IP");  
	            
	        }  
	        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	            ip = request.getHeader("HTTP_X_FORWARDED_FOR");  
	           
	        }  
	        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	            ip = request.getHeader("X-Real-IP");  
	         
	        }  
	        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	            ip = request.getRemoteAddr();  
	           
	        }
	        AddressUtils addressUtils = new AddressUtils();  
	        String address = "";  
/*
            try {
                address = addressUtils.getAddresses("ip="+ip+"&ak="+"OvTYcoL84lHUGoZMzrhPAbCHH4eYW3jA", "utf-8");
               
               
            } catch (UnsupportedEncodingException e) {  
                
                e.printStackTrace();  
            }*/

            
	        String User_Agent = request.getHeader("User-Agent");
	        String orderEquipment = null;
	        if (User_Agent.contains("Android")||User_Agent.contains("Linux")) {
	        	orderEquipment="Android客户端";
	       
	        	if (User_Agent.contains("MicroMessenger")) {
	        		orderEquipment="Android微信";
	   
	        	}
	        } else if (User_Agent.contains("iPhone")) {
	        	orderEquipment="iPhone客户端";
	       
	        	if (User_Agent.contains("MicroMessenger")) {
	        	
	        		orderEquipment="iPhone微信";
	        	}
	        } else if (User_Agent.contains("iPad")) {
	        	orderEquipment="iPad客户端";
	       
	        	if (User_Agent.contains("MicroMessenger")) {
	        		orderEquipment="iPad微信";
	        		
	        	}
	        } else if(User_Agent.contains("Windows")){
	        	orderEquipment="Windows";
	        	
	        }
	    
	    
		order.setOrderIp(ip);
		order.setOrderDevice(address);
		order.setOrderEquipment(orderEquipment);

		Sbu sbu = sbuService.find(sbuId);
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,
				filters,
				null);
		if (sbus.size() == 0 || sbus == null) {
			return error("该用户没有维护此类型SBU");
		}

		//处理sbu
		if (sbuId == null) {
			return error("请选择sbu！");
		}else {
			order.setSbu(sbu);
		}

		// 价格类型
		if (memberRankId != null) {
			MemberRank memberRank = memberRankBaseService.find(memberRankId);
			order.setMemberRank(memberRank);
		}
		
		//运输方式
		SystemDict transportType = systemDictService.find(transportTypeId);
		if(!ConvertUtil.isEmpty(transportType)){
			order.setTransportType(transportType);
		}

		//出货类型
		SystemDict outOfTheWarehouseType = systemDictService.find(outOfTheWarehouseTypeId);
		if(!ConvertUtil.isEmpty(transportType)){
			order.setOutOfTheWarehouseType(outOfTheWarehouseType);
		}

		order.setFourAftersaleSn(aftersaleSn);

		orderService.save(order,
				store,
				distributor,
				saleman,
				shippingMethod,
				paymentMethod,
				area,
				cart,
				cartItems,
				stat,
				orderClassifyId,
				orderStyleId,
				tradeStyleId,
				saleStyleId,
				industryStyleId,
				paymentStyleId);
		String mess="";
		if(stat==0){
			mess= "订单保存成功,未支付！";
		}else if(stat==1){
			mess= "订单下达成功，已支付！";
		}
		return success(mess).addObjX(order.getId());

	}



	/*
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(Order order, Long shippingMethodId, Long areaId,
					 Long freightChargeTypeId, Long businessTypeId, Long currencyId,
					 Long[] delOrderItemId, Integer stat, Long organizationId,
					 Long warehouseId, Long orderClassifyId, Long orderStyleId,
					 Long tradeStyleId, Long saleStyleId, Long industryStyleId,
					 Date factorydate, Date qualitydate, Long salemanId,Long discountId,
					 Long distributorId, Long paymentStyleId, Long sbuId, Long memberRankId,
					 Long transportTypeId,Long contractId,Long outOfTheWarehouseTypeId, String aftersaleSn) {


		StoreMember saleman = storeMemberService.find(salemanId);
		Store distributor =null;
		if(distributorId!=null){
			 distributor = storeService.find(distributorId);
		}
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		Area area = areaService.find(areaId);
		if (area == null) {
			// 请选择收货地区
			return error("15136");
		}
		if (ConvertUtil.isEmpty(order.getAddress())) {
			// 请填写收货地址
			return error("15137");
		}
		if (ConvertUtil.isEmpty(order.getConsignee())) {
			// 请填写收货人
			return error("15138");
		}
		if (ConvertUtil.isEmpty(order.getPhone())) {
			// 请填写收货人电话
			return error("15139");
		}
		if(StringUtils.isNotEmpty(aftersaleSn) &&
				orderService.exists(Filter.eq("fourAftersaleSn", aftersaleSn),
						Filter.ne("orderStatus", OrderStatus.cancelled),
						Filter.ne("id", order.getId()))){
			return error("该售后单已经被关联！");
		}
		for(OrderItem item : order.getOrderItems()){
			if(item.getPriceDifference() != null && !"0".equals(item.getPriceDifference().toString()) && item.getDiscountProjectDescription().getId() == null){
				// 请选择优惠项目说明
				return error("请选择优惠项目说明");
			}
			if(item.getDiscountProjectDescription() != null && item.getDiscountProjectDescription().getId() == null){
				item.setDiscountProjectDescription(null);
			}
		}
		ShippingMethod shippingMethod = shippingMethodBaseService.find(shippingMethodId);
		SystemDict freightChargeType = null;
		if (freightChargeTypeId != null) {
			freightChargeType = systemDictService.find(freightChargeTypeId);
		}
		SystemDict businessType = null;
		if (businessTypeId != null) {
			businessType = systemDictService.find(businessTypeId);
		}
		SystemDict currency = systemDictService.find(currencyId);
		order.setCurrency(currency);
		Date goTime = new Date();
		order.setGoTime(goTime);
		order.setQualityDate(qualitydate);
		Sbu sbu = sbuService.find(sbuId);
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,filters,null);
		if (sbus.size() == 0 || sbus == null) {
			return error("该客户没有维护此类型SBU");
		}
		// 价格类型
		if (memberRankId != null) {
			MemberRank memberRank = memberRankBaseService.find(memberRankId);
			order.setMemberRank(memberRank);
		}
		if (contractId != null) {
			order.setCustomerContract(customerContractService.find(contractId));
		}
		//运输方式
		if(!ConvertUtil.isEmpty(transportTypeId)){
			SystemDict transportType = systemDictService.find(transportTypeId);
			order.setTransportType(transportType);
		}
		//出货类型
		if(!ConvertUtil.isEmpty(outOfTheWarehouseTypeId)){
			SystemDict outOfTheWarehouseType = systemDictService.find(outOfTheWarehouseTypeId);
			order.setOutOfTheWarehouseType(outOfTheWarehouseType);
		}
		order.setFourAftersaleSn(aftersaleSn);
		orderService.update(order,
				shippingMethod,
				area,
				distributor,
				saleman,
				delOrderItemId,
				freightChargeType,
				businessType,
				stat,
				organizationId,
				orderClassifyId,
				orderStyleId,
				tradeStyleId,
				saleStyleId,
				industryStyleId,
				paymentStyleId,
				sbuId,
				discountId);
		String mess="";
		if(stat==0){
			mess= "订单保存成功,未支付！";
		}else if(stat==1){
			mess= "订单下达成功，已支付！";
		}
		

		return success(mess);
	}

	@RequestMapping(value = "/setFlag", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg setFlag(Long[] ids, Integer flag) {
		if (ids == null || ids.length == 0) {
			// 请选择订单
			return error("15140");
		}
		if (flag == null) {
			// 请选择旗标
			return error("15250");
		}
		orderService.setFlag(ids, flag);
		return success();
	}

	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg cancel(Long[] ids) {

		if (ids == null || ids.length == 0) {
			// 请选择订单
			return error("15140");
		}
		for (Long orderId : ids) {
			Order order = orderService.find(orderId);
			for (OrderItem orderItem : order.getOrderItems()) {
				List<Map<String, Object>> shippingItemList = shippingService.findShippingItemListByOrderItemId(orderItem.getId()
						.toString());
				for (int i = 0; i < shippingItemList.size(); i++) {
					
					int a = (Integer) shippingItemList.get(i).get("status");
					
					if (a != 2) {
						return error("订单已存在发货单，不允许驳回");
					}

				}

				if (orderItem.getShipPlanQuantity() != null
						&& orderItem.getShipPlanQuantity()
						.compareTo(BigDecimal.ZERO) == 1) {

					// 订单已存在发货单，不允许驳回
					return error("15140");
				}
			}
		}

		orderService.cancelOrder(ids);

		return success();
	}

	/*
	 * 清除仓库
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/clear_warehouse", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg clearWarehouse(Long id) {

		if (id == null) {
			// 请选择订单
			return error("15140");
		}

		OrderItem orderItem = orderItemService.find(id);
		Order order = orderItem.getOrder();
		if (!order.getOrderStatus().equals(OrderStatus.unaudited)) {
			// 只允许未审核状态的订单对明细进行清除仓库
			return error("151043");
		}
		orderService.clearWarehouse(id);

		return success();
	}

	@RequestMapping(value = "/get_price", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getPrice(Long[] productId, Long storeId, ModelMap model) {

		Map<Long, Map<String, Object>> map = new HashMap<Long, Map<String, Object>>();
		Store store = storeService.find(storeId);
		Store mainStore = storeService.getMainStore();
		Long memberRankId = store.getMemberRank().getId();
		for (Long id : productId) {
			Product product = productService.find(id);
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("product", product));
			filters.add(Filter.eq("store", mainStore));
			ProductStore productStore = productStoreService.find(filters);
			Map<String, Object> item = new HashMap<String, Object>();
			Map<String, Object> productPrice = productPriceHeadService.findPriceByProductStore(productStore.getId(),
					memberRankId);
			if (productPrice != null) {
				item.put("member_price",
						new BigDecimal(productPrice.get("store_member_price")
								.toString()));
			}
			else {
				item.put("member_price", product.getPrice());
			}
			Map<String, Object> itemMap = priceApplyService.findItemByProduct(id,
					storeId);
			if (itemMap != null) {
				item.put("apply_sn", itemMap.get("sn"));
				item.put("apply_price", itemMap.get("price"));
				item.put("apply_item_id", itemMap.get("id"));
				BigDecimal useable_quantity = new BigDecimal(
						itemMap.get("quantity").toString()).subtract(new BigDecimal(
						itemMap.get("used_quantity").toString()));
				item.put("apply_useable_quantity", useable_quantity);
			}
			map.put(id, item);
		}
		return success().addObjX(map);
	}

	/*
	 * Excel导入
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file, HttpServletResponse response,
							  ModelMap model) throws Exception {

		orderService.orderImport(file);
		return ResultMsg.success();
	}

	/*
	 * 订单导入
	 * @throws Exception
	 */
	@RequestMapping(value = "/order_import", method = RequestMethod.POST, produces = "text/html; charset=UTF-8")
	public String order_import(MultipartFile file,
							   HttpServletResponse response, ModelMap model) throws Exception {

		List<Map<String, Object>> orders = orderService.orderImportData(file);
		String jsonStr = JsonUtils.toJson(orders);
		model.addAttribute("orders", jsonStr);

		return "/b2b/order/order_import";
	}

	@RequestMapping(value = "/orderImportByCache", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg orderImportByCache(String uuid) throws Exception {

		orderService.orderImportByCache(uuid);
		return ResultMsg.success();
	}

	private List<Map<String, Object>> sort(List<Map<String, Object>> items,
										   Long parent) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		
		if (items != null) {
			for (Map<String, Object> map : items) {
				if ((map.get("parent") != null && parent != null && Long.parseLong(map.get("parent")
						.toString()) == parent.longValue())
						|| (map.get("parent") == null && parent == null)) {
					result.add(map);
					result.addAll(sort(items,
							Long.parseLong(map.get("id").toString())));
				}
			}
		}
		
		return result;
	}

	@RequestMapping(value = "/confirm", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg confirm(Long[] ids) throws Exception {
		orderService.confirm(ids);
		return success();
	}

	@RequestMapping(value = "/message_board", method = RequestMethod.GET)
	public String message_board(Long id, ModelMap model) throws Exception {
		Order order = orderService.find(id);
		model.addAttribute("order", order);
		if (order != null) {
			List<Filter> filters = new ArrayList<Filter>();
			List<net.shopxx.base.core.Order> orders = new ArrayList<net.shopxx.base.core.Order>();
			filters.add(Filter.eq("orderSn", order.getSn()));
			orders.add(net.shopxx.base.core.Order.asc("createDate"));
			List<MessageBoard> messageBoards = messageBoardService.findList(null,
					filters,
					orders);
			model.addAttribute("messageBoards", messageBoards);
		}
		model.addAttribute("storeMember", storeMemberService.getCurrent());
		return "/b2b/order/message_board";
	}

	@RequestMapping(value = "/message_board_content", method = RequestMethod.GET)
	public String message_board_content(Long id, ModelMap model)
			throws Exception {
		Order order = orderService.find(id);
		model.addAttribute("order", order);
		if (order != null) {
			List<Filter> filters = new ArrayList<Filter>();
			List<net.shopxx.base.core.Order> orders = new ArrayList<net.shopxx.base.core.Order>();
			filters.add(Filter.eq("orderSn", order.getSn()));
			orders.add(net.shopxx.base.core.Order.asc("createDate"));
			List<MessageBoard> messageBoards = messageBoardService.findList(null,
					filters,
					orders);
			model.addAttribute("messageBoards", messageBoards);
		}

		return "/b2b/order/message_board_content";
	}

	@RequestMapping(value = "/save_message_board", method = RequestMethod.GET)
	public @ResponseBody
	ResultMsg save_message_board(Long id, String content) throws Exception {
		Order order = orderService.find(id);
		if (order == null) return this.error("订单不存在");
		MessageBoard messageBoard = new MessageBoard();
		messageBoard.setType(1);
		messageBoard.setOrderSn(order.getSn());
		messageBoard.setContent(content);
		messageBoard.setStore(order.getStore());
		messageBoard.setStoreMember(storeMemberService.getCurrent());
		messageBoardService.save(messageBoard);
		return this.success();
	}


	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String orderSn,
			String outTradeNo, Integer[] orderStatus, Long sbuId,
			Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			String store_member_name, Long saleOrgId, Long organizationId,
			Pageable pageable, ModelMap model,String moistureContent,
			String colourNumber,String batch,Long[] warehouseIds,
			Long[] organizationIds) {

		Integer size = orderService.count(orderSn,
				outTradeNo,
				orderStatus,
				sbuId,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				store_member_name,
				saleOrgId,
				organizationId,
				pageable,
				null,
				null,
				moistureContent,
				colourNumber,
				batch,
				warehouseIds,
				organizationIds);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/*
	 * 条件导出
	 * @param productCategoryId
	 * @param sn
	 * @param vonderCode
	 * @param mod
	 * @param name
	 * @param startTime
	 * @param endTime
	 * @param isMarketable
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String orderSn, String outTradeNo,
				Integer[] orderStatus, Long sbuId, Integer[] exStatus,
				Integer[] paymentStatus, Integer[] shippingStatus, Integer[] flag,
				Long warehouseId, Long[] storeId, String phone, String consignee,
				String address, Long deliveryCorpId, Long productId[],
				String firstTime, String lastTime, Integer readOnly,
				Integer[] confirmStatus, String store_member_name, Long saleOrgId,
				Long organizationId, Pageable pageable, ModelMap model, Integer page,
				String moistureContent,String colourNumber,String batch,
				Long[] warehouseIds,Long[] organizationIds) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = orderService.findItemList(orderSn,
				outTradeNo,orderStatus,sbuId,shippingStatus,warehouseId,storeId,
				consignee,phone,address,deliveryCorpId,productId,paymentStatus,
				flag,2,firstTime,lastTime,confirmStatus,null,store_member_name,
				saleOrgId,organizationId,page,size,moistureContent,colourNumber,
				batch,warehouseIds,organizationIds);
				
				
		return getModelAndView(data, model);

	}

	/*
	 * 选择导出
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = orderService.findItemList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null);
		return getModelAndView(data, model);
	}

	/*
	 * dzr条件导出
	 * @param productCategoryId
	 * @param sn
	 * @param vonderCode
	 * @param mod
	 * @param name
	 * @param startTime
	 * @param endTime
	 * @param isMarketable
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export_dzr", method = RequestMethod.GET)
	public ModelAndView conditionExportDzr(String orderSn, String outTradeNo,
										   Integer[] orderStatus, Integer[] exStatus, Integer[] paymentStatus,
										   Integer[] shippingStatus, Integer[] flag, Long warehouseId,
										   Long[] storeId, String phone, String consignee, String address,
										   Long deliveryCorpId, Long productId[], String firstTime,
										   String lastTime, Integer readOnly, Integer[] confirmStatus,
										   String store_member_name, Long saleOrgId, Long organizationId,
										   Pageable pageable, ModelMap model, Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = orderService.findItemList(orderSn,
				outTradeNo,
				orderStatus,
				null,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				null,
				store_member_name,
				saleOrgId,
				organizationId,
				page,
				size,
				null,
				null,
				null,
				null,
				null);
		return getModelAndView(data, model);

	}

	/*
	 * 大自然选择导出
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export_dzr", method = RequestMethod.GET)
	public ModelAndView selectedExportDzr(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = orderService.findItemList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null);
		return getModelAndView(data, model);
	}

	/* 订单列表的导出 */
	public ModelAndView getModelAndViewdzr(List<Map<String, Object>> data,
										   ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("order_status") != null) {
				Integer order_status = (Integer) str.get("order_status");
				if (order_status == 0) {
					str.put("order_status_name", "未确认");
				}
				else if (order_status == 1) {
					str.put("order_status_name", "已确认");
				}
				else if (order_status == 2) {
					str.put("order_status_name", "已完成");
				}
				else if (order_status == 3) {
					str.put("order_status_name", "已作废");
				}
				else if (order_status == 4) {
					str.put("order_status_name", "已删除");
				}
				else if (order_status == 5) {
					str.put("order_status_name", "未审核");
				}
				else if (order_status == 6) {
					str.put("order_status_name", "已审核");
				}
				else if (order_status == 7) {
					str.put("order_status_name", "已保存");
				}
				else if (order_status == 8) {
					str.put("order_status_name", "已接受");
				}
			}
			if (str.get("shipping_status") != null) {
				Integer shipping_status = (Integer) str.get("shipping_status");
				if (shipping_status == 0) {
					str.put("shipping_status_name", "未发货");
				}
				else if (shipping_status == 1) {
					str.put("shipping_status_name", "部分发货");
				}
				else if (shipping_status == 2) {
					str.put("shipping_status_name", "已发货");
				}
				else if (shipping_status == 3) {
					str.put("shipping_status_name", "确认收货");
				}
			}
			
			if (str.get("paiType") != null) {
				Integer paiType = (Integer) str.get("paiType");
				if (paiType == 0) {
					str.put("paiType", "促销");
				}
				else if (paiType == 1) {
					str.put("paiType", "二等品");
				}
				else if (paiType == 2) {
					str.put("paiType", "定制");
				}
				else if (paiType == 3) {
					str.put("paiType", "工程");
				}
			}

			if (str.get("payment_status") != null) {
				Integer payment_status = (Integer) str.get("payment_status");
				if (payment_status == 0) {
					str.put("payment_status_name", "未支付");
				}
				else if (payment_status == 1) {
					str.put("payment_status_name", "部分支付");
				}
				else if (payment_status == 2) {
					str.put("payment_status_name", "完全支付");
				}
			}
			if (str.get("order_id") != null) {
				Long orderId = Long.parseLong(str.get("order_id").toString());
				Order order = orderService.find(orderId);
				BigDecimal orderAmount = BigDecimal.ZERO;
				if (order != null) {
					orderAmount = order.getAmount().setScale(2,
							BigDecimal.ROUND_HALF_UP);
				}
				str.put("orderAmount", orderAmount);
			}
			if (str.get("order_date") != null) {

				String order_date = DateUtil.convert((Date) str.get("order_date"));
				if (order_date.length() > 10) {
					order_date = order_date.substring(0, 10);
				}
				str.put("orderDate", order_date);
			}
			if (str.get("order_create_date") != null) {
				// String order_create_date = (String)
				// str.get("order_create_date");
				String order_create_date = DateUtil.convert((Date) str.get("order_create_date"));
				if (order_create_date.length() > 19) {
					order_create_date = order_create_date.substring(0, 19);
				}
				str.put("orderCreateDate", order_create_date);
			}
			if (str.get("order_check_date") != null) {
				String order_check_date = DateUtil.convert((Date) str.get("order_check_date"));
				if (order_check_date.length() > 19) {
					order_check_date = order_check_date.substring(0, 19);
				}
				str.put("orderCheckDate", order_check_date);
			}
			if (str.get("price") != null) {
				BigDecimal price = new BigDecimal(str.get("price").toString());
				str.put("price", price.setScale(2, BigDecimal.ROUND_HALF_UP));
			}

			if (str.get("price") != null && str.get("quantity") != null) {
				BigDecimal price = new BigDecimal(str.get("price").toString());
				BigDecimal quantity = new BigDecimal(str.get("quantity")
						.toString());

				str.put("count",
						price.multiply(quantity).setScale(2,
								BigDecimal.ROUND_HALF_UP));
			}
			else {
				str.put("count", "0.00");
			}
			if (str.get("quantity") != null) {
				BigDecimal quantity = new BigDecimal(str.get("quantity")
						.toString());
				str.put("quantity", NumberFormat.getInstance().format(quantity));
			}
			if (str.get("xsStatus") != null) {
				Integer xsStatus = (Integer) str.get("xsStatus");
				if (xsStatus == 0) {
					str.put("xsStatus", "未审核");
				}
				else if (xsStatus == 1) {
					str.put("xsStatus", "已审核");
				}
				else if (xsStatus == 2) {
					str.put("xsStatus", "作废");
				}
				else if (xsStatus == 3) {
					str.put("xsStatus", "部分发货");
				}
				else if (xsStatus == 4) {
					str.put("xsStatus", "完全发货");
				}
			}
			if (str.get("paType") != null) {
				Integer paType = (Integer) str.get("paType");
				if (paType == 0) {
					str.put("paType", "促销");
				}
				else if (paType == 1) {
					str.put("paType", "二等品");
				}
				else if (paType == 2) {
					str.put("paType", "定制");
				}
				else if (paType == 3) {
					str.put("paType", "工程");
				}
			}
			if (str.get("branch_quantity") != null) {
				BigDecimal branch_quantity = new BigDecimal(
						str.get("branch_quantity").toString());
				str.put("branch_quantity",
						branch_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("box_quantity") != null) {
				BigDecimal box_quantity = new BigDecimal(
						str.get("box_quantity").toString());
				str.put("box_quantity",
						box_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("shipped_quantity") != null) {
				BigDecimal shipped_quantity = new BigDecimal(
						str.get("shipped_quantity").toString());
				str.put("shipped_quantity",
						shipped_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "订单编号",
				"下单时间",
				"审核时间",
				"订单状态",
				"客户名称",
				"客户编码",
				"订单金额",

				"产品名称",
				"产品型号",
				"产品编号",
				"订单数量",
				"单价",
				"交货期",
				"发货数量", //订单明细结束

				"下单人",
				"收货人",
				"收货人电话",
				"收货地区",
				"收货地址",
				"物流快递",
				"配送状态",
				"支付状态",

				"审核人",
				"机构",
				"创建时间",
				"来源单号"

		};
		// 设置单元格取值
		String[] properties = { "order_sn",
				"order_date",
				"order_check_date",
				"order_status_name",
				"store_name",
				"store_sn",
				"orderAmount",

				"name",
				"model",
				"vonderCode",
				"quantity",
				"price",
				"delivery_time",
				"shipped_quantity", //订单明细结束

				"store_member_name",
				"consignee",
				"phone",
				"area_full_name",
				"address",
				"delivery_corp_name",
				"shipping_status_name",
				"payment_status_name",

				"check_store_member_name",
				"sale_org_name",
				"orderCreateDate",
				"out_no", };

		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 180,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 650,
				25 * 256,
				25 * 256,

		};

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}

	/*
	 * 模拟支付
	 */
	@RequestMapping(value = "/submit", method = RequestMethod.POST)
	public String submit(HttpServletRequest request, BigDecimal payamount,
						 String payname, HttpServletResponse response, ModelMap model) {

		model.addAttribute("requestUrl", "https://mapi.alipay.com/gateway.do");
		model.addAttribute("requestMethod", "get");
		model.addAttribute("requestCharset", "UTF-8");
		Map<String, Object> data = new HashMap<String, Object>();
		data = getParameterMap(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()),
				request.getParameter("payname"),
				request);
		model.addAttribute("parameterMap", data);
		return "/member/store_recharge/submit";

	}

	public Map<String, Object> getParameterMap(String sn, String description,
											   HttpServletRequest request) {

		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("service", "create_direct_pay_by_user");
		parameterMap.put("partner", "2088211535838965");// 支付宝商户号
		parameterMap.put("_input_charset", "utf-8");
		parameterMap.put("sign_type", "MD5");
		parameterMap.put("return_url", "http://mk.5mall.com");// 支付成功同步回调地址
		parameterMap.put("notify_url", "http://mk.5mall.com");// 支付成功异步回调地址
		parameterMap.put("out_trade_no", sn);// 支付单号
		parameterMap.put("subject",
				StringUtils.abbreviate(description.replaceAll("[^0-9a-zA-Z\\u4e00-\\u9fa5 ]",
						""),
						60));
		parameterMap.put("body",
				StringUtils.abbreviate(description.replaceAll("[^0-9a-zA-Z\\u4e00-\\u9fa5 ]",
						""),
						600));
		parameterMap.put("payment_type", "1");
		parameterMap.put("seller_id", "2088211535838965");// 支付宝商户号
		parameterMap.put("total_fee", request.getParameter("payamount"));// 支付金额
		parameterMap.put("show_url", "http://mk.5mall.com");// 网站地址
		parameterMap.put("paymethod", "directPay");
		parameterMap.put("exter_invoke_ip", request.getLocalAddr());// 请求ip
		parameterMap.put("extra_common_param", "twkj remark");// 备注

		try {
			parameterMap.put("sign", generateSign(parameterMap));
		}
		catch (Exception e) {
			System.out.print("---11----" + e.getMessage());
		}

		return parameterMap;
	}

	private String generateSign(Map<String, ?> parameterMap) {

		return DigestUtils.md5Hex(joinKeyValue(new TreeMap<String, Object>(
						parameterMap),
				null,
				"********************************",
				"&",
				true,
				"sign_type",
				"sign"));
	}

	protected String joinKeyValue(Map<String, Object> map, String prefix,
								  String suffix, String separator, boolean ignoreEmptyValue,
								  String... ignoreKeys) {
		List<String> list = new ArrayList<String>();
		if (map != null) {
			for (Entry<String, Object> entry : map.entrySet()) {
				String key = entry.getKey();
				String value = ConvertUtils.convert(entry.getValue());
				if (StringUtils.isNotEmpty(key)
						&& !ArrayUtils.contains(ignoreKeys, key)
						&& (!ignoreEmptyValue || StringUtils.isNotEmpty(value))) {
					list.add(key + "=" + (value != null ? value : ""));
				}
			}
		}
		return (prefix != null ? prefix : "")
				+ StringUtils.join(list, separator)
				+ (suffix != null ? suffix : "");
	}

	@RequestMapping(value = "/rejectOrder", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg rejectOrder(Long orderId) {

		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}
		Order order = orderService.find(orderId);
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService.findShippingItemListByOrderItemId(orderItem.getId()
					.toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				return error("订单已存在发货单，不允许驳回");
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
					.compareTo(BigDecimal.ZERO) == 1) {

				// 订单已存在发货单，不允许驳回
				return error("15140");
			}
		}

		orderService.rejectOrder(order);

		return success();
	}

	@RequestMapping(value = "/refushPdf", method = RequestMethod.GET)
	public @ResponseBody
	ResultMsg refushPdf(Long id) {
		Order order = orderService.find(id);
		orderService.refushPdf(order);
		return success();

	}

	@RequestMapping(value = "/shipping2order", method = RequestMethod.GET)
	public @ResponseBody
	String shipping2order(Long[] ids) {

		int i = 0;
		List<Filter> filters = new ArrayList<Filter>();
		if (ids != null && ids.length > 0) {
			for (Long id : ids) {
				Shipping shipping = shippingService.find(id);
				Store supplier = shipping.getSupplier();
				Order order = new Order();
				List<OrderItem> orderItems = new ArrayList<OrderItem>();
				List<ShippingItem> shippingItems = shipping.getShippingItems();
				for (ShippingItem shippingItem : shippingItems) {
					OrderItem orderItem = new OrderItem();
					Product product = shippingItem.getProduct();
					OrderItem sourceOrderItem = shippingItem.getOrderItem();
					// 复制到新订单项
					BeanUtils.copyProperties(sourceOrderItem,
							orderItem,
							new String[] { "id",
									"createDate",
									"modifyDate",
									"order",
									"orderItemPartses",
									"orderItemAttachs" });
					orderItem.setSourceOrderItemId(sourceOrderItem.getId());
					orderItem.setQuantity(shippingItem.getQuantity());
					orderItem.setShipPlanQuantity(shippingItem.getQuantity());
					orderItem.setShippedQuantity(shippingItem.getShippedQuantity());
					orderItem.setOrder(order);
					orderItem.setWarehouse(shipping.getWarehouse());
					orderItem.setOutTradeId(shippingItem.getId().toString());

					Map<String, Object> priceMap = contractPriceService.findItemByProduct(product.getId(),
							supplier.getId());
					if (priceMap != null) {
						BigDecimal price = new BigDecimal(priceMap.get("price")
								.toString());
						orderItem.setPrice(price);
					}
					else {
						orderItem.setMemo("无合约价");
					}

					orderItems.add(orderItem);

					sourceOrderItem.setIsPurchase(1);
					orderItemService.update(sourceOrderItem);
				}
				order.setOrderItems(orderItems);
				BigDecimal zero = BigDecimal.ZERO;
				order.setArea(shipping.getArea());
				order.setAddress(shipping.getAddress());
				order.setConsignee(shipping.getConsignee());
				order.setCouponDiscount(zero);
				order.setFreight(zero);
				order.setOffsetAmount(zero);
				order.setOrderStatus(OrderStatus.audited);
				filters.clear();
				filters.add(Filter.eq("method", PaymentMethod.Method.offline));
				List<PaymentMethod> paymentMethods = paymentMethodBaseService.findList(1,
						filters,
						null);
				if (!paymentMethods.isEmpty()) {
					PaymentMethod paymentMethod = paymentMethods.get(0);
					order.setPaymentMethod(paymentMethod);
					order.setPaymentMethodName(paymentMethod.getName());
				}
				else {
					order.setPaymentMethodName("线下支付");
				}
				filters.clear();
				filters.add(Filter.eq("name", shipping.getShippingMethod()));
				List<ShippingMethod> shippingMethods = shippingMethodBaseService.findList(1,
						filters,
						null);
				if (!shippingMethods.isEmpty()) {
					ShippingMethod shippingMethod = shippingMethods.get(0);
					order.setShippingMethod(shippingMethod);
					order.setShippingMethodName(shippingMethod.getName());
				}
				else {
					order.setShippingMethodName("快递");
				}
				order.setPaymentStatus(PaymentStatus.paid);
				if (shipping.getStatus() < 3) {
					order.setShippingStatus(ShippingStatus.unshipped);
				}
				else if (shipping.getStatus() == 3) {
					order.setShippingStatus(ShippingStatus.partialShipment);
				}
				else {
					order.setShippingStatus(ShippingStatus.shipped);
				}
				order.setPhone(shipping.getPhone());
				order.setPromotionDiscount(zero);
				order.setZipCode(shipping.getZipCode());
				order.setStoreMember(storeMemberService.find(14781L));
				order.setStore(shipping.getStore());
				order.setSupplier(shipping.getSupplier());
				order.setType("6");
				order.setOrderType(4);
				order.setIsMobile(false);
				String sn = SnUtil.getOrderSn();
				order.setSn(sn);
				order.setOutTradeNo(shipping.getSn());
				order.setCreateDate(shipping.getCreateDate());
				order.setOrderDate(shipping.getCreateDate());
				order.setAmountPaid(order.getAmount());

				orderService.save(order);

				for (OrderItem orderItem : order.getOrderItems()) {
					Long shippingItemId = Long.parseLong(orderItem.getOutTradeId());
					ShippingItem shippingItem = shippingItemService.find(shippingItemId);
					shippingItem.setPurOrderItem(orderItem);
					shippingItem.setPurOrderSn(order.getSn());
					shippingItem.setOrderType(4);
					shippingItemService.update(shippingItem);
				}
				shipping.setOrderType(4);
				if (shipping.getStatus() == 1) {
					shipping.setStatus(0);
				}
				shippingService.update(shipping);
				i++;
			}
		}
		else {
			filters.clear();
			filters.add(Filter.ne("status", 2));
			filters.add(Filter.isNotNull("supplier"));
			filters.add(Filter.ne("orderType", 4));
			List<Shipping> shippings = shippingService.findList(null,
					filters,
					null);
			for (Shipping shipping : shippings) {

				Order order = new Order();
				Store supplier = shipping.getSupplier();
				List<OrderItem> orderItems = new ArrayList<OrderItem>();
				List<ShippingItem> shippingItems = shipping.getShippingItems();
				for (ShippingItem shippingItem : shippingItems) {
					OrderItem orderItem = new OrderItem();
					Product product = shippingItem.getProduct();
					OrderItem sourceOrderItem = shippingItem.getOrderItem();
					// 复制到新订单项
					BeanUtils.copyProperties(sourceOrderItem,
							orderItem,
							new String[] { "id",
									"createDate",
									"modifyDate",
									"order",
									"orderItemPartses",
									"orderItemAttachs" });
					orderItem.setSourceOrderItemId(sourceOrderItem.getId());
					orderItem.setQuantity(shippingItem.getQuantity());
					orderItem.setShipPlanQuantity(shippingItem.getQuantity());
					orderItem.setShippedQuantity(shippingItem.getShippedQuantity());
					orderItem.setOrder(order);
					orderItem.setWarehouse(shipping.getWarehouse());
					orderItem.setOutTradeId(shippingItem.getId().toString());

					Map<String, Object> priceMap = contractPriceService.findItemByProduct(product.getId(),
							supplier.getId());
					if (priceMap != null) {
						BigDecimal price = new BigDecimal(priceMap.get("price")
								.toString());
						orderItem.setPrice(price);
					}
					else {
						orderItem.setMemo("无合约价");
					}

					orderItems.add(orderItem);

					sourceOrderItem.setIsPurchase(1);
					orderItemService.update(sourceOrderItem);
				}
				order.setOrderItems(orderItems);
				BigDecimal zero = BigDecimal.ZERO;
				order.setArea(shipping.getArea());
				order.setAddress(shipping.getAddress());
				order.setConsignee(shipping.getConsignee());
				order.setCouponDiscount(zero);
				order.setFreight(zero);
				order.setOffsetAmount(zero);
				order.setOrderStatus(OrderStatus.audited);
				filters.clear();
				filters.add(Filter.eq("method", PaymentMethod.Method.offline));
				List<PaymentMethod> paymentMethods = paymentMethodBaseService.findList(1,
						filters,
						null);
				if (!paymentMethods.isEmpty()) {
					PaymentMethod paymentMethod = paymentMethods.get(0);
					order.setPaymentMethod(paymentMethod);
					order.setPaymentMethodName(paymentMethod.getName());
				}
				else {
					order.setPaymentMethodName("线下支付");
				}
				filters.clear();
				filters.add(Filter.eq("name", shipping.getShippingMethod()));
				List<ShippingMethod> shippingMethods = shippingMethodBaseService.findList(1,
						filters,
						null);
				if (!shippingMethods.isEmpty()) {
					ShippingMethod shippingMethod = shippingMethods.get(0);
					order.setShippingMethod(shippingMethod);
					order.setShippingMethodName(shippingMethod.getName());
				}
				else {
					order.setShippingMethodName("快递");
				}
				order.setPaymentStatus(PaymentStatus.paid);
				if (shipping.getStatus() < 3) {
					order.setShippingStatus(ShippingStatus.unshipped);
				}
				else if (shipping.getStatus() == 3) {
					order.setShippingStatus(ShippingStatus.partialShipment);
				}
				else {
					order.setShippingStatus(ShippingStatus.shipped);
				}
				order.setPhone(shipping.getPhone());
				order.setPromotionDiscount(zero);
				order.setZipCode(shipping.getZipCode());
				order.setStoreMember(storeMemberService.find(14781L));
				order.setStore(shipping.getStore());
				order.setSupplier(shipping.getSupplier());
				order.setType("6");
				order.setOrderType(4);
				order.setIsMobile(false);
				String sn = SnUtil.getOrderSn();
				order.setSn(sn);
				order.setOutTradeNo(shipping.getSn());
				order.setCreateDate(shipping.getCreateDate());
				order.setOrderDate(shipping.getCreateDate());
				order.setAmountPaid(order.getAmount());

				orderService.save(order);

				for (OrderItem orderItem : order.getOrderItems()) {
					Long shippingItemId = Long.parseLong(orderItem.getOutTradeId());
					ShippingItem shippingItem = shippingItemService.find(shippingItemId);
					shippingItem.setPurOrderItem(orderItem);
					shippingItem.setPurOrderSn(order.getSn());
					shippingItem.setOrderType(4);
					shippingItemService.update(shippingItem);
				}
				shipping.setOrderType(4);
				if (shipping.getStatus() == 1) {
					shipping.setStatus(0);
				}
				shippingService.update(shipping);
				i++;
			}
		}
		return "success:" + i;

	}

	/*
	 * 查看pdf
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkPdf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checkPdf(Long ids) throws Exception {
		Order order = orderService.find(ids);
		if (order == null) return error("订单不存在！");
		/*if ((!order.getOrderStatus().equals(Order.OrderStatus.audited))
				&& (!order.getOrderStatus().equals(Order.OrderStatus.completed))) {
			return error("订单未审核或已作废");
		}*/
		TriplicateForm triplicateForm = null;/*orderService.getTriplicateForm(id);*/
		if (triplicateForm == null) {
			triplicateForm = orderService.createTriplicateForm(order);
		}
		return success(triplicateForm.getUrl());

	}

	/*
	 * 查看其他pdf
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkPdfOther", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checkPdfOther(Long ids) throws Exception {

		Order order = orderService.find(ids);
		if (order == null) return error("订单不存在！");
		/*if ((!order.getOrderStatus().equals(Order.OrderStatus.audited))
				&& (!order.getOrderStatus().equals(Order.OrderStatus.completed))) {
			return error("订单未审核或已作废");
		}*/
		TriplicateForm triplicateForm = null;/*orderService.getOtherTriplicateForm(id);*/
		if (triplicateForm == null) {
			triplicateForm = orderService.createOtherTriplicateForm(order);
		}
		return success(triplicateForm.getUrl());

	}

	/*
	 * 查询客户相关客户经理数据
	 */
	@RequestMapping(value = "/select_manager", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_accountManager_data(long storeId, ModelMap model) {

		Store store = storeService.find(storeId);
		List<StoreManager> storeManagers = null;
		if (store != null) {

			storeManagers = store.getStoreManagers();
		}
		return this.success().addObjX(storeManagers);
	}

	/*
	 * 保存
	 */
	@RequestMapping(value = "/save/{code}", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save_code(@PathVariable String code, Order order, Long storeId,
						Long salemanId, Long currencyId, Long regionalManagerId,
						Long areaId, Long organizationId, Long salesmanId,
						Long businessTypeId, Long freightChargeTypeId, Long saleOrgId,
						Long paymentMethodId, Long shippingMethodId, Long warehouseId,
						Long cartId, Long[] ids, Integer stat, Long orderClassifyId,
						Long orderStyleId, Long distributorId, Long tradeStyleId,
						Long saleStyleId, Long industryStyleId, Date factorydate,
						Date qualitydate, Long paymentStyleId) {
		Store store = storeService.find(storeId);
		Store distributor = storeService.find(distributorId);
		if (store == null) {
			// 请选择客户
			return error("15135");
		}

		StoreMember saleman = storeMemberService.find(salemanId);

		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		if (saleOrg == null) {
			return error("请维护机构");
		}
		Area area = areaService.find(areaId);
		if (area == null) {
			// 请选择收货地区
			return error("15136");
		}
		if (ConvertUtil.isEmpty(order.getAddress())) {
			// 请填写收货地址
			return error("15137");
		}
		if (ConvertUtil.isEmpty(order.getConsignee())) {
			// 请填写收货人
			return error("15138");
		}
		if (ConvertUtil.isEmpty(order.getPhone())) {
			// 请填写收货人电话
			return error("15139");
		}

		PaymentMethod paymentMethod = paymentMethodBaseService.find(paymentMethodId);

		ShippingMethod shippingMethod = shippingMethodBaseService.find(shippingMethodId);
		if (shippingMethod == null) {
			// 请选择配送方式
			return error("151041");
		}
		if (freightChargeTypeId != null) {
			SystemDict freightChargeType = systemDictService.find(freightChargeTypeId);
			order.setFreightChargeType(freightChargeType);
		}
		if (businessTypeId != null) {
			SystemDict businessType = systemDictService.find(businessTypeId);
			order.setBusinessType(businessType);
		}
		if (warehouseId != null) {
			Warehouse warehouse = warehouseService.find(warehouseId);
			order.setWarehouse(warehouse);
		}

		SystemDict currency = systemDictService.find(currencyId);
		order.setCurrency(currency);
		Cart cart = cartService.find(cartId);
		if (cart != null && cart.isEmpty()) {
			// 购物车已清空
			return error("15126");
		}
		List<CartItem> cartItems = new ArrayList<CartItem>();
		if (ids != null) {
			for (Long id : ids) {
				cartItems.add(cartItemService.find(id));
			}
		}
		if (salesmanId != null) {
			order.setSalesman(storeMemberService.find(salesmanId));
		}
		StoreMember regionalManager = storeMemberService.find(regionalManagerId);
		order.setRegionalManager(regionalManager);
		order.setSaleOrg(saleOrgService.find(saleOrgId));
		Organization organization = organizationService.find(organizationId);
		order.setOrganization(organization);
		order.setQualityDate(qualitydate);
		orderService.save(order,
				store,
				distributor,
				saleman,
				shippingMethod,
				paymentMethod,
				area,
				cart,
				cartItems,
				stat,
				orderClassifyId,
				orderStyleId,
				tradeStyleId,
				saleStyleId,
				industryStyleId,
				paymentStyleId);
		return success().addObjX(order.getId());

	}

	/* 订单列表的导出 */
	public ModelAndView getModelAndView(List<Map<String, Object>> data,
										ModelMap model) {
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		//订单行是否展示金额  0不展示  非0展示
		int hiddenAmount = 0;
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles",WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
		}catch (RuntimeException e) {
			
		}
		for (Map<String, Object> str : data) {
			if (str.get("order_status") != null) {
				Integer order_status = (Integer) str.get("order_status");
				if (order_status == 0) {
					str.put("order_status_name", "未确认");
				}else if (order_status == 1) {
					str.put("order_status_name", "已确认");
				}else if (order_status == 2) {
					str.put("order_status_name", "已完成");
				}else if (order_status == 3) {
					str.put("order_status_name", "已作废");
				}else if (order_status == 4) {
					str.put("order_status_name", "已删除");
				}else if (order_status == 5) {
					str.put("order_status_name", "未审核");
				}else if (order_status == 6) {
					str.put("order_status_name", "已审核");
				}else if (order_status == 7) {
					str.put("order_status_name", "已保存");
				}else if (order_status == 8) {
					str.put("order_status_name", "已接受");
				}
			}
			if (str.get("shipping_status") != null) {
				Integer shipping_status = (Integer) str.get("shipping_status");
				if (shipping_status == 0) {
					str.put("shipping_status_name", "未发货");
				}else if (shipping_status == 1) {
					str.put("shipping_status_name", "部分发货");
				}else if (shipping_status == 2) {
					str.put("shipping_status_name", "已发货");
				}else if (shipping_status == 3) {
					str.put("shipping_status_name", "确认收货");
				}
			}
			if (str.get("paiType") != null) {
				Integer paiType = (Integer) str.get("paiType");
				if (paiType == 0) {
					str.put("paiType", "促销");
				}else if (paiType == 1) {
					str.put("paiType", "二等品");
				}else if (paiType == 2) {
					str.put("paiType", "定制");
				}else if (paiType == 3) {
					str.put("paiType", "工程");
				}
			}
			if (str.get("payment_status") != null) {
				Integer payment_status = (Integer) str.get("payment_status");
				if (payment_status == 0) {
					str.put("payment_status_name", "未支付");
				}else if (payment_status == 1) {
					str.put("payment_status_name", "部分支付");
				}else if (payment_status == 2) {
					str.put("payment_status_name", "完全支付");
				}
			}
			if (str.get("order_id") != null) {
				Long orderId = Long.parseLong(str.get("order_id").toString());
				Order order = orderService.find(orderId);
				BigDecimal orderAmount = BigDecimal.ZERO;
				if (order != null) {
					orderAmount = order.getAmount().setScale(2,BigDecimal.ROUND_HALF_UP);
				}
				str.put("orderAmount", orderAmount);
			}
			if (str.get("order_date") != null) {
				String order_date = DateUtil.convert((Date) str.get("order_date"));
				if (order_date.length() > 10) {
					order_date = order_date.substring(0, 10);
				}
				str.put("orderDate", order_date);
			}
			if (str.get("order_create_date") != null) {
				String order_create_date = DateUtil.convert((Date) str.get("order_create_date"));
				if (order_create_date.length() > 19) {
					order_create_date = order_create_date.substring(0, 19);
				}
				str.put("orderCreateDate", order_create_date);
			}
			if (str.get("order_check_date") != null) {
				String order_check_date = DateUtil.convert((Date) str.get("order_check_date"));
				if (order_check_date.length() > 19) {
					order_check_date = order_check_date.substring(0, 19);
				}
				str.put("orderCheckDate", order_check_date);
			}
			if (!ConvertUtil.isEmpty(str.get("price"))) {
				BigDecimal price = new BigDecimal(str.get("price").toString());
				str.put("price", price.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("price") != null && str.get("quantity") != null) {
				BigDecimal price = new BigDecimal(str.get("price").toString());
				BigDecimal quantity = new BigDecimal(str.get("quantity").toString());
				str.put("count",price.multiply(quantity).setScale(2,BigDecimal.ROUND_HALF_UP));
			}else {
				str.put("count", "0.00");
			}
			if (str.get("xsStatus") != null) {
				Integer xsStatus = (Integer) str.get("xsStatus");
				if (xsStatus == 0) {
					str.put("xsStatus", "未审核");
				}else if (xsStatus == 1) {
					str.put("xsStatus", "已审核");
				}else if (xsStatus == 2) {
					str.put("xsStatus", "作废");
				}else if (xsStatus == 3) {
					str.put("xsStatus", "部分发货");
				}else if (xsStatus == 4) {
					str.put("xsStatus", "完全发货");
				}
			}
			if (str.get("paType") != null) {
				Integer paType =Integer.valueOf(str.get("paType").toString());
				if (paType == 0) {
					str.put("paType", "促销");
				}else if (paType == 1) {
					str.put("paType", "二等品");
				}else if (paType == 2) {
					str.put("paType", "定制");
				}else if (paType == 3) {
					str.put("paType", "工程");
				}
			}
			if (str.get("branch_quantity") != null) {
				BigDecimal branch_quantity = new BigDecimal(str.get("branch_quantity").toString());
				str.put("branch_quantity",branch_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("box_quantity") != null) {
				BigDecimal box_quantity = new BigDecimal(str.get("box_quantity").toString());
				str.put("box_quantity",box_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("shipped_quantity") != null) {
				BigDecimal shipped_quantity = new BigDecimal(str.get("shipped_quantity").toString());
				str.put("shipped_quantity",shipped_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if(!ConvertUtil.isEmpty(hiddenAmount) && hiddenAmount==0){
				//订单金额
				str.put("orderAmount", "***");
				//单价
				str.put("price", "***");
				//小计
				str.put("count","***");
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "创建时间",
				"订单编号",
				"订单状态",
				"sbu",
				"仓库名称",
				"业务类型",
				"来源单号",
				"下单时间",
				"下单人",
				"审核人",
				"审核时间",
				"配送状态",
				"支付状态",
				"订单金额",
				"行备注",
				"头备注",
				"机构",
				"客户名称",
				"客户简称",
				"客户编码",
				"产品名称",
				"产品型号",
				"产品编号",
				"产品描述",
				"经营组织",
				"规格",
				"产品分类",
				"订单数量",
				"单价",
				"小计",
				"交货期",
				"发货数量",
				"收货人",
				"收货人电话",
				"收货地区",
				"收货地址",
				"配送方式",
				"特价单号",
				"色号",
				"含水率",
				"批次",
				"运输方式",
				"下单IP",
				"下单地址",
				"下单设备"

		};
		 
		
		// 设置单元格取值
		String[] properties = { "orderCreateDate", 
				"order_sn", //订单编号
				"order_status_name", //订单状态
				"sbu_name",//sbu
				"order_warehouse_name",//仓库名称
				"sdValue",
				"out_no", //来源单号
				"order_date", //下单时间
				"store_member_name", //下单人
				"check_store_member_name", //审核人
				"order_check_date", //审核时间
				"shipping_status_name", //配送状态
				"payment_status_name", //支付状态
				"orderAmount", //订单金额
				"memo", //行备注
				"order_memo",//表头备注
				"sale_org_name", //机构
				"store_name", //客户名称
				"store_alias", //客户简称
				"store_sn", //客户编码
				"name", //产品名称
				"model", //产品型号
				"vonderCode", //产品编号
				"description", //产品描述
				"product_organization_name",//经营组织
				"spec", //规格
				"product_category_name", //产品分类
				"quantity", //订单数量
				"price", //单价
				"count", //小计
				"delivery_time", //交货期
				"shipped_quantity", //发货数量
				"consignee", //收货人
				"phone", //收货人电话
				"area_full_name", //收货地区
				"address", //收获地址
				"shippingMethodName",//配送方式
				"price_apply_sn",// 特价单号
				"colour_number_name",//色号
				"moisture_content_name",//含水率
				"batch_encoding",//批次
				"transportTypeName",//运输方式
				"order_ip",
				"order_device",
				"order_equipment"
		};

		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256};

		
		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}


	/**
	 * 获取商品销售数量，按product字段分组
	 * @return
	 */
	@RequestMapping(value="/getSellTotal",method = RequestMethod.GET)
	@ResponseBody
	public List<Map<String, Object>> getSellTotalGroupByProduct(){
		List<Map<String, Object>> lists = orderService.getSellTotalGroupByProduct();
		return lists;
	}
	
	/**
	 * 招行商务支付
	 * @throws UnsupportedEncodingException 
	 */
	@RequestMapping(value="/pay_line",method = RequestMethod.POST)
	public  @ResponseBody
	ResultMsg pay_line(String code,Long orderId) throws UnsupportedEncodingException{
		
		Order order = orderService.find(orderId);
		List<BankCard> bankCards=bankCardBaseService.findListBySbu(order.getSaleOrg().getId(),order.getOrganization().getId(),order.getSbu().getId());
		String cardNo=null;
		if(bankCards.size()>0 && bankCards.size()==1){
		BankCard bancard =bankCards.get(0);
		cardNo=bancard.getBankCardNo();
		}else{
		ExceptionUtil.throwServiceException("未有收款信息，请联系管理员！");
		}
		
		String xmlPkt = CMBHttpRequest.getRequestStr(orderService.find(orderId),code,cardNo);
		System.out.println("xmlPkt:"+xmlPkt);
		String result = CMBHttpRequest.sendRequest(xmlPkt);
		System.out.println("result:"+result);

		// 处理返回的结果
		String requestUrel	= CMBHttpRequest.processResult(result);
		System.out.println("requestUrel:"+requestUrel);
		if(requestUrel.equals("1")){
			return error("支付失败！");
		}else{
			return success().addObjX(requestUrel);
		}
		
	}

	@RequestMapping(value = "/select_bank", method = RequestMethod.GET)
	public String pay_list_tb(Long orderId,ModelMap model) {

		model.addAttribute("orderId", orderId);
		
		return "/b2b/order/pay_line";
	}
	
	/**
	 * 订单审核完修改明细平台结算价
	 * @param order
	 * @return
	 */
	@RequestMapping(value = "/apdate_SaleOrgPrice", method = RequestMethod.POST)
	public @ResponseBody ResultMsg auditedUpdateSaleOrgPrice(Order order) {
		Order pOrder = orderService.find(order.getId());
		if (pOrder == null){
			 return error("订单不存在！");
		}
		if(pOrder.getOrderStatus().equals(Order.OrderStatus.audited)||pOrder.getOrderStatus().equals(Order.OrderStatus.completed)){
			orderService.auditedUpdateSaleOrgPrice(order);
		}else{
			return error("订单必须是已审核或已完成的！");
		}
		return success();
	}
	
	/**
	 *	校验用户能否下单
	 * @param store
	 * @return false 不能   true 能
	 */
	private Boolean checkOrdering(Store store) {
		Boolean flag = true;
		/**
		 * 对于未选择是否可发货的客户，根据是否有欠缴保证金判断，有欠缴，则不能下单
		 * 对于有选择是否可发货的客户，以是否可发货为准，不判断是否有欠缴保证金
		 */
		BigDecimal unpaidCautionPaid = store.getUnpaidCautionPaid();//未缴保证金
		if(store.getShippingState() == null){
//			if(unpaidCautionPaid.compareTo(BigDecimal.ZERO)>0) flag= false;

		}else {
			if (store.getShippingState() == 0) flag= false;
		}
		return flag;
	}

    /**
     * 系统参数控制权限
     * <AUTHOR>
     * @Date 2021-03-02
     * @param systemParameter 系统参数Code
     * @param model  ModelMap
     */
    public void parameterControl(String systemParameter, ModelMap model){
        List<Filter> filters = new ArrayList<Filter>();
        filters.clear();
        filters.add(Filter.eq("storeMember",
                storeMemberService.find(WebUtils.getCurrentStoreMemberId())));
        List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
                filters,
                null);
        try {
            String value = SystemConfig.getConfig(systemParameter,
                    WebUtils.getCurrentCompanyInfoId());
            String[] perRole = value.split(",");
            List<String> list = Arrays.asList(perRole);
            int ok = 0;
            for (PcUserRole userRole : userRoles) {
                if (list.contains(userRole.getPcRole().getName())) {
                    ok++;
                    break;
                }
            }
            model.addAttribute(systemParameter, ok);
        }catch (RuntimeException e) {

        }
    }
    
    
    /**
     * 列表数据
     */
    @RequestMapping(value = "/sendToLink5", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg sendToLink5(Long[] ids) {

        orderService.synchronization(ids);
        return success("同步成功");
    }

}