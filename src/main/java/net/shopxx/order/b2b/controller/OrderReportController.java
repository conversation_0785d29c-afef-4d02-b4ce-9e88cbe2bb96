package net.shopxx.order.b2b.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.ShippingService;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 销售统计报表
 * <AUTHOR>
 *
 */
@Controller("b2bOrderReportController")
@RequestMapping("/b2b/order_report")
public class OrderReportController extends BaseController {

	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		List<Filter> filterss = new ArrayList<Filter>();
		//业务类型-系统词汇
		filterss.clear();
		filterss.add(Filter.eq("code", "businessType"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,
				filterss,
				null);
		model.addAttribute("businessTypes", businessTypes);
		//经营组织
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		return "/b2b/order_report/list";
	}

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model) {

		return "/b2b/order_report/list_tb";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_datafindReport(String orderSn, Long[] productId,
			String vonderCode, String mod, Long[] storeId, Long storeMemberId,
			Long supplierId, String firstTime, String lastTime,
			Integer[] shippingStatus, BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, String organizationName,
			Long[] saleOrgId, Long[] productCategoryId, String groupby,
			Long businessTypeId, String groupBy, Pageable pageable,
			String groupProduct) {
		String pGroupBy = "";
		if (StringUtils.isNotBlank(groupBy)) {
			pGroupBy += "," + groupBy;
		}
		if (StringUtils.isNotBlank(groupProduct)) {
			pGroupBy += "," + groupProduct;
		}
		Page<Map<String, Object>> page = orderService.findReport(orderSn,
				productId,
				vonderCode,
				mod,
				storeId,
				storeMemberId,
				supplierId,
				firstTime,
				lastTime,
				shippingStatus,
				minNotShippingQuantity,
				maxNotShippingQuantity,
				minShippedQuantity,
				maxShippedQuantity,
				organizationName,
				saleOrgId,
				productCategoryId,
				groupby,
				businessTypeId,
				StringUtils.isNotBlank(pGroupBy) ? pGroupBy.split(",") : null,
				pageable);
		if (groupby != null) {
			for (Map<String, Object> str : page.getContent()) {
				if ("bystore".equals(groupby)) {
					str.put("ogName", "");
					str.put("sale_org_name", "");
					str.put("product_category_name", "");
					str.put("manager", "");
					str.put("vonder_code", "");
					str.put("description", "");
				}
				if ("bysaleorg".equals(groupby)) {
					str.put("ogName", "");
					str.put("product_category_name", "");
					str.put("manager", "");
					str.put("vonder_code", "");
					str.put("description", "");
					str.put("store_name", "");
					str.put("out_trade_no", "");
				}
				if ("byproduct".equals(groupby)) {
					if (saleOrgId != null) {
						str.put("ogName", "");
						str.put("out_trade_no", "");
						str.put("store_name", "");
					}

				}
			}

		}

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String orderSn,
			Long[] productId, String vonderCode, String mod, Long[] storeId,
			Long storeMemberId, Long supplierId, String firstTime,
			String lastTime, Integer[] shippingStatus,
			BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, String organizationName,
			Long[] saleOrgId, Long[] productCategoryId, String groupby,
			Long businessTypeId, String groupBy, Pageable pageable,
			String groupProduct, ModelMap model) {
		String pGroupBy = "";
		if (StringUtils.isNotBlank(groupBy)) {
			pGroupBy += "," + groupBy;
		}
		if (StringUtils.isNotBlank(groupProduct)) {
			pGroupBy += "," + groupProduct;
		}
		Integer size = orderService.countReport(orderSn,
				productId,
				vonderCode,
				mod,
				storeId,
				storeMemberId,
				supplierId,
				firstTime,
				lastTime,
				shippingStatus,
				minNotShippingQuantity,
				maxNotShippingQuantity,
				minShippedQuantity,
				maxShippedQuantity,
				organizationName,
				saleOrgId,
				productCategoryId,
				groupby,
				businessTypeId,
				StringUtils.isNotBlank(pGroupBy) ? pGroupBy.split(",") : null,
				pageable,
				null,
				null);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String orderSn, Long[] productId,
			String vonderCode, String mod, Long[] storeId, Long storeMemberId,
			Long supplierId, String firstTime, String lastTime,
			Integer[] shippingStatus, BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, String organizationName,
			Long[] saleOrgId, Long[] productCategoryId, String groupby,
			Long businessTypeId, String groupBy, Pageable pageable,
			String groupProduct, ModelMap model, Integer page) {
		String pGroupBy = "";
		if (StringUtils.isNotBlank(groupBy)) {
			pGroupBy += "," + groupBy;
		}
		if (StringUtils.isNotBlank(groupProduct)) {
			pGroupBy += "," + groupProduct;
		}
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = orderService.findReportList(orderSn,
				productId,
				vonderCode,
				mod,
				storeId,
				storeMemberId,
				supplierId,
				firstTime,
				lastTime,
				shippingStatus,
				minNotShippingQuantity,
				maxNotShippingQuantity,
				minShippedQuantity,
				maxShippedQuantity,
				organizationName,
				saleOrgId,
				productCategoryId,
				groupby,
				businessTypeId,
				StringUtils.isNotBlank(pGroupBy) ? pGroupBy.split(",") : null,
				pageable,
				page,
				size);
		return getModelAndViewForList(data, model);
	}

	/**
	 * 选择导出
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = orderService.findReportList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null);
		return getModelAndViewForList(data, model);
	}

	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("orderItemshippedQuantity") != null
					&& new BigDecimal(str.get("orderItemshippedQuantity") + "").compareTo(BigDecimal.ZERO) == 1) {
				str.put("averagePrice",
						new BigDecimal(str.get("amount") + "").divide(new BigDecimal(
								str.get("orderItemshippedQuantity") + ""),
								2,
								BigDecimal.ROUND_HALF_UP));
				str.put("saleaveragePrice",
						new BigDecimal(str.get("saleorgamount") + "").divide(new BigDecimal(
								str.get("orderItemshippedQuantity") + ""),
								2,
								BigDecimal.ROUND_HALF_UP));
			}
			else {
				str.put("averagePrice", BigDecimal.ZERO);
				str.put("saleaveragePrice", BigDecimal.ZERO);
			}
		}

		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		//设置标题
		String[] header = { "年", "月", "周",
//				"经营组织",
				"机构",
				"客户经理",
				"客户名称",
				"客户编码",
				"产品分类",
				"产品编码",
				"描述",
				"下单数量",
				"业务类型",
				"结算均价",
				"均价",
				"发货金额",
				"平台结算金额" };

		//设置单元格取值
		String[] properties = { "year", "month", "week",
//				"ogName",
				"sale_org_name",
				"manager",
				"store_name",
				"out_trade_no",
				"product_category_name",
				"vonder_code",
				"description",
				"orderItemshippedQuantity",
				"businessTypeName",
				"saleaveragePrice",
				"averagePrice",
				"amount",
				"saleorgamount" };
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	private List<Map<String, Object>> sort(List<Map<String, Object>> items,
			Long parent) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if (items != null) {
			for (Map<String, Object> map : items) {
				if ((map.get("parent") != null && parent != null && Long.parseLong(map.get("parent")
						.toString()) == parent.longValue())
						|| (map.get("parent") == null && parent == null)) {
					result.add(map);
					result.addAll(sort(items,
							Long.parseLong(map.get("id").toString())));
				}
			}
		}
		return result;
	}
}