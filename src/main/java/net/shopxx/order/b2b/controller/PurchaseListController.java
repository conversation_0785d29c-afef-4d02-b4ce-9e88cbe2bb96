package net.shopxx.order.b2b.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.intf.NaturePurchaseBack;

@Controller("b2bPurchaseListController")
@RequestMapping("/b2b/purchase")
public class PurchaseListController extends BaseController{
	
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;

	@RequestMapping(value = "/purchase_list", method = RequestMethod.GET)
	public String list_tb(Long objTypeId, ModelMap model) {
		//经营组织
				List<Filter> filters = new ArrayList<Filter>();
				filters.add(Filter.eq("isEnabled", true));
				filters.add(Filter.eq("type", 0));
				List<Organization> organizations = organizationService.findList(null,
						filters,
						null);
				model.addAttribute("organizations", organizations);
				
		return "/b2b/purchase/purchase_list";
	}
	
	/**
	 * ERP销售采购综合查询
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/purchase_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg findOrderOnHandQtyIntf(String organizationId,String sourcing,String storeName,String changeState,
			String supplier,String erpSN,String purchaseSn,String vonderCode,String shippingSn,String sales,String packagePrice,
			String orderFirstTime,String orderLastTime,String changeFirstTime,String changeLastTime,String needFirstTime,String needLastTime) throws Exception {
		
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Map<String, String>> stockList = null;
		List<Map<String, String>> newstockList = new ArrayList<Map<String, String>>();
		List<Map<String, String>> productList = new ArrayList<Map<String, String>>();
		String jsonPage = null;

		 List<Map<String, Object>> paramsList = new ArrayList<Map<String, Object>>();
		 HashMap<String, Object> params = null;
		 String organizationIdStr = "";
			if (organizationId != null) {
				organizationIdStr = organizationId;
			} else {
				return error("库存组织为空");
			}
		if ("nature".equals(companyInfo.getCompany_code())) {

			// 大自然ERP销售采购综合查询
			
			params = new HashMap<String, Object>();
			params.put("Org_Id", organizationIdStr);
			params.put("Supply_Pri", sourcing);
			params.put("Customer_Name", storeName);
			params.put("Status", changeState);
			params.put("Vendor_Name", supplier);
			params.put("Order_Date_From", orderFirstTime);
			params.put("Order_Date_To", orderLastTime);
			params.put("Order_Number", erpSN);
			params.put("Cust_Po_Number", shippingSn);
			params.put("Item_Code", vonderCode);
			params.put("Create_User", sales);
			params.put("Operate_Date_From", changeFirstTime);
			params.put("Operate_Date_To", changeLastTime);
			params.put("Flag", packagePrice);
			params.put("ATTRIBUTE1", purchaseSn);
			
			paramsList.add(params);
			
		
             
		}
		
		stockList = new NaturePurchaseBack().onHandQty(params);
		
		if (stockList != null) {
			newstockList.addAll(stockList);
		}
		if (newstockList != null && newstockList.size() > 0) {
			HashSet h = new HashSet(newstockList);
			newstockList.clear();
			newstockList.addAll(h);
			Map<String, String> map = null;

			
			for (int i = 0; i < newstockList.size(); i++) {
				map = newstockList.get(i);
			
				map.put("CUST_PO_NUMBER", map.get("CUST_PO_NUMBER")==null||map.get("CUST_PO_NUMBER").equals("")?"":map.get("CUST_PO_NUMBER").toString());//
				map.put("ORDER_NUMBER", map.get("ORDER_NUMBER")==null||map.get("ORDER_NUMBER").equals("")?"":map.get("ORDER_NUMBER").toString());// 
				map.put("PO_NUMBER", map.get("PO_NUMBER")==null||map.get("PO_NUMBER").equals("")?"":map.get("PO_NUMBER").toString());// 
				map.put("PO_LINE_NUMBER", map.get("PO_LINE_NUMBER")==null||map.get("PO_LINE_NUMBER").equals("")?"":map.get("PO_LINE_NUMBER").toString());// 
				map.put("ACCOUNT_NUMBER", map.get("ACCOUNT_NUMBER")==null||map.get("ACCOUNT_NUMBER").equals("")?"":map.get("ACCOUNT_NUMBER").toString());// 
				map.put("CUSTOMER_NAME", map.get("CUSTOMER_NAME")==null||map.get("CUSTOMER_NAME").equals("")?"":map.get("CUSTOMER_NAME").toString());// 
				map.put("ITEM_CODE", map.get("ITEM_CODE")==null||map.get("ITEM_CODE").equals("")?"":map.get("ITEM_CODE").toString());
				map.put("ITEM_DESC", map.get("ITEM_DESC")==null||map.get("ITEM_DESC").equals("")?"":map.get("ITEM_DESC").toString());
				map.put("ITEM_UOM", map.get("ITEM_UOM")==null||map.get("ITEM_UOM").equals("")?"":map.get("ITEM_UOM").toString());
				map.put("ORDER_QTY", map.get("ORDER_QTY")==null||map.get("ORDER_QTY").equals("")?"":map.get("ORDER_QTY").toString());
				map.put("SHIP_QTY", map.get("SHIP_QTY")==null||map.get("SHIP_QTY").equals("")?"":map.get("SHIP_QTY").toString());
				map.put("VENDOR_NAME", map.get("VENDOR_NAME")==null||map.get("VENDOR_NAME").equals("")?"":map.get("VENDOR_NAME").toString());
				map.put("VENDOR_ADDRESS", map.get("VENDOR_ADDRESS")==null||map.get("VENDOR_ADDRESS").equals("")?"":map.get("VENDOR_ADDRESS").toString());
				map.put("BUYER", map.get("BUYER")==null||map.get("BUYER").equals("")?"":map.get("BUYER").toString());
				map.put("LINE_REMARK", map.get("LINE_REMARK")==null||map.get("LINE_REMARK").equals("")?"":map.get("LINE_REMARK").toString());
				map.put("MARKET_PRICE", map.get("MARKET_PRICE")==null||map.get("MARKET_PRICE").equals("")?"":map.get("MARKET_PRICE").toString());
				map.put("UNIT_PRICE", map.get("UNIT_PRICE")==null||map.get("UNIT_PRICE").equals("")?"":map.get("UNIT_PRICE").toString());
				map.put("SBU", map.get("SBU")==null||map.get("SBU").equals("")?"":map.get("SBU").toString());
				map.put("SUPPORT_PRI", map.get("SUPPORT_PRI")==null||map.get("SUPPORT_PRI").equals("")?"":map.get("SUPPORT_PRI").toString());
				map.put("OPREATE_DATE", map.get("OPREATE_DATE")==null||map.get("OPREATE_DATE").equals("")?"":map.get("OPREATE_DATE").toString());
				map.put("NEED_BY_DATE", map.get("NEED_BY_DATE")==null||map.get("NEED_BY_DATE").equals("")?"":map.get("NEED_BY_DATE").toString());
				map.put("PO_QTY", map.get("PO_QTY")==null||map.get("PO_QTY").equals("")?"":map.get("PO_QTY").toString());
				map.put("RCV_QTY", map.get("RCV_QTY")==null||map.get("RCV_QTY").equals("")?"":map.get("RCV_QTY").toString());
				map.put("PO_HEAD_STATUS", map.get("PO_HEAD_STATUS")==null||map.get("PO_HEAD_STATUS").equals("")?"":map.get("PO_HEAD_STATUS").toString());
				map.put("PO_LINE_STATUS", map.get("PO_LINE_STATUS")==null||map.get("PO_LINE_STATUS").equals("")?"":map.get("PO_LINE_STATUS").toString());
				map.put("ORDER_HEAD_STATUS", map.get("ORDER_HEAD_STATUS")==null||map.get("ORDER_HEAD_STATUS").equals("")?"":map.get("ORDER_HEAD_STATUS").toString());
				map.put("LINE_STATUS", map.get("LINE_STATUS")==null||map.get("LINE_STATUS").equals("")?"":map.get("LINE_STATUS").toString());
				map.put("STATUS", map.get("STATUS")==null||map.get("STATUS").equals("")?"":map.get("STATUS").toString());
			
			    
				
				
				productList.add(map);
			}
			jsonPage = JsonUtils.toJson(productList);
		} else {
			newstockList = new ArrayList<Map<String, String>>();
			jsonPage = JsonUtils.toJson(newstockList);
			LogUtils.info("==============返回没有数据=================");
		}
		return success(jsonPage);
		
	}
}
