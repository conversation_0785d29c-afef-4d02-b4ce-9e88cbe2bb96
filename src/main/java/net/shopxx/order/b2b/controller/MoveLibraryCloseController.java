package net.shopxx.order.b2b.controller;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.member.entity.PcMenu;
import net.shopxx.member.service.PcMenuBaseService;
import net.shopxx.template.entity.DTemplates;
import net.shopxx.template.service.DTemplatesService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.MoveLibrary;
import net.shopxx.order.entity.MoveLibraryClose;
import net.shopxx.order.service.MoveLibraryCloseService;
import net.shopxx.order.service.MoveLibraryService;
import net.shopxx.util.RoleJurisdictionUtil;
@Controller("moveLibraryCloseController")
@RequestMapping("/b2b/move_library_close")
public class MoveLibraryCloseController extends BaseController{
	
	@Resource(name = "moveLibraryCloseServiceImpl")
	private MoveLibraryCloseService moveLibraryCloseService;
	@Resource(name = "moveLibraryServiceImpl")
	private MoveLibraryService moveLibraryService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;
    @Resource(name = "dTemplatesServiceImpl")
    private DTemplatesService dTemplatesService;
    @Resource(name = "pcMenuBaseServiceImpl")
    private PcMenuBaseService pcMenuBaseService;
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long id,Long menuId,Long objid,ModelMap model) {
		model.addAttribute("id", id);
		model.addAttribute("menuId", menuId);
		model.addAttribute("objid", objid);
		return "/b2b/move_library_close/list_tb";
	}
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Long userId,Long menuId,ModelMap model) {
		if(ConvertUtil.isEmpty(menuId)){
			Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
			List<Filter> filters = new ArrayList<Filter>();
			filters.clear();
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("menuCode", "移库关闭"));
			PcMenu pcMenu = pcMenuBaseService.find(filters);
			if(!ConvertUtil.isEmpty(pcMenu)){
				menuId = pcMenu.getId();
			}
		}
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/move_library_close/list";
	}
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Pageable pageable) {

		String jsonPage = JsonUtils.toJson(null);

		return success(jsonPage);
	}
	
	/**
	 * 添加
	 * @param id
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long id,ModelMap model) {
		
		//移库单
		MoveLibrary moveLibrary = moveLibraryService.find(id);
		model.addAttribute("moveLibrary", moveLibrary);
		//关闭原因
		List<SystemDict> closeReasonList = roleJurisdictionUtil.getSystemDictList("closeReason",null);
		model.addAttribute("closeReasonList", closeReasonList);
		//物流公司
		List<SystemDict> logisticsTypeList = roleJurisdictionUtil.getSystemDictList("logisticsType",null);
		model.addAttribute("logisticsTypeList", logisticsTypeList);
		//设置日期格式
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		//关闭日期
		model.addAttribute("closeDate", df.format(new Date()));	
		//根据移库单id查找明细
		if(!ConvertUtil.isEmpty(moveLibrary.getId())){
			List<Map<String, Object>> vMoveLibraryList = moveLibraryService.findVMoveLibraryList(moveLibrary.getId());
			model.addAttribute("vMoveLibraryList", JsonUtils.toJson(vMoveLibraryList));
		}
		return "/b2b/move_library_close/add";
	}
	
	
	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(MoveLibraryClose moveLibraryClose,
			Long moveLibraryId) {
		
		//移库单
		MoveLibrary moveLibrary = moveLibraryService.find(moveLibraryId);
		if(ConvertUtil.isEmpty(moveLibrary)){
			return error("移库单编号不存在");
		}
		moveLibraryClose.setMoveLibrary(moveLibrary);
		//关闭时间
		if(ConvertUtil.isEmpty(moveLibraryClose.getCloseDate())){
			return error("关闭时间不能为空");
		}
		//保存
		moveLibraryCloseService.saveMoveLibraryClose(moveLibraryClose);
		
		return success().addObjX(moveLibraryClose.getId());
	}
	
	
	/**
	 * 查看
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id,ModelMap model) {
		
		//移库关闭单
		MoveLibraryClose moveLibraryClose = moveLibraryCloseService.find(id);
		model.addAttribute("moveLibraryClose", moveLibraryClose);
		//关闭原因
		List<SystemDict> closeReasonList = roleJurisdictionUtil.getSystemDictList("closeReason",null);
		model.addAttribute("closeReasonList", closeReasonList);
		//物流公司
		List<SystemDict> logisticsTypeList = roleJurisdictionUtil.getSystemDictList("logisticsType",null);
		model.addAttribute("logisticsTypeList", logisticsTypeList);
		//根据移库单id查找明细
		if(!ConvertUtil.isEmpty(moveLibraryClose.getId())){
			List<Map<String, Object>> moveLibraryCloseItemList = moveLibraryCloseService.findMoveLibraryCloseItemList(id);
			model.addAttribute("moveLibraryCloseItemList", JsonUtils.toJson(moveLibraryCloseItemList));
		}
		return "/b2b/move_library_close/edit";
	}
	
	
	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(MoveLibraryClose moveLibraryClose) {
		
		//关闭时间
		if(ConvertUtil.isEmpty(moveLibraryClose.getCloseDate())){
			return error("关闭时间不能为空");
		}
		//保存
		moveLibraryCloseService.updateMoveLibraryClose(moveLibraryClose);

		return success();
	}
	
	
	/**
	 * 作废
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancel(Long id) throws Exception {

		MoveLibraryClose moveLibraryClose = moveLibraryCloseService.find(id);
		moveLibraryCloseService.cancel(moveLibraryClose);
		return success();
	}
	
	
	/**
	 * 流程审批
	 * @param id
	 * @param modelId
	 * @param objTypeId
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
		moveLibraryCloseService.checkMoveLibraryCloseWf(id, modelId, objTypeId);
		return success();
	}
	
	
}
