package net.shopxx.order.b2b.controller;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.MessageBoard;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.PaymentMethod;
import net.shopxx.basic.entity.ShippingMethod;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.MessageBoardService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.PaymentMethodBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.PolicyCountService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreManager;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.order.entity.Cart;
import net.shopxx.order.entity.CartItem;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.entity.Order.PaymentStatus;
import net.shopxx.order.entity.Order.ShippingStatus;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.purchase.service.ContractPriceService;
import net.shopxx.order.service.CartItemService;
import net.shopxx.order.service.CartService;
import net.shopxx.order.service.OrderAttachService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.PriceApplyService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductStore;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductPriceHeadService;
import net.shopxx.product.service.ProductStoreBaseService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfObjConfigBaseService;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 订单查询
 * 
 * <AUTHOR>
 *
 */
@Controller("b2bOrderSelectController")
@RequestMapping("/b2b/order_select")
public class OrderSelectController extends BaseController {

	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "cartServiceImpl")
	private CartService cartService;
	@Resource(name = "cartItemServiceImpl")
	private CartItemService cartItemService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;
	@Resource(name = "paymentMethodBaseServiceImpl")
	private PaymentMethodBaseService paymentMethodBaseService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "orderAttachServiceImpl")
	private OrderAttachService orderAttachService;
	@Resource(name = "priceApplyServiceImpl")
	private PriceApplyService priceApplyService;
	@Resource(name = "productPriceHeadServiceImpl")
	private ProductPriceHeadService productPriceHeadService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "messageBoardServiceImpl")
	private MessageBoardService messageBoardService;
	@Resource(name = "productStoreBaseServiceImpl")
	private ProductStoreBaseService productStoreService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "contractPriceServiceImpl")
	private ContractPriceService contractPriceService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseService;
	@Resource(name = "policyCountServiceImpl")
	private PolicyCountService policyCountService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer readOnly, Long objTypeId, Long objid,
			Long[] ci_ids, ModelMap model) {

		if (ci_ids != null && ci_ids.length > 0) {
			readOnly = 1;
		}
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("ci_ids", ci_ids);
		return "/b2b/order_select/list_tb";
	}

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb_code(@PathVariable String code, Integer readOnly,
			Long objTypeId, Long objid, Long[] ci_ids, ModelMap model) {

		if (ci_ids != null && ci_ids.length > 0) {
			readOnly = 1;
		}
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("ci_ids", ci_ids);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/order_select/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Pageable pageable,
			Integer readOnly, ModelMap model) {
		model.addAttribute("readOnly", readOnly);
		/** 北京零微科技有限公司 */
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId != null && companyInfoId == 18) {
			model.addAttribute("isLw", true);
		}

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}

		try {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
																// 非0 展示
		}
		catch (RuntimeException e) {

		}

		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/order_select/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, Integer readOnly, ModelMap model) {
		model.addAttribute("readOnly", readOnly);
		/** 北京零微科技有限公司 */
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId != null && companyInfoId == 18) {
			model.addAttribute("isLw", true);
		}

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}

		try {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 0 不展示 非0 展示
		}
		catch (RuntimeException e) {

		}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		return "/b2b/order_select/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			String store_member_name, Long saleOrgId, Long organizationId,
			Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = orderService.findPage(orderSn,
				outTradeNo,
				orderStatus,
				shippingStatus,
				null,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				null,
				store_member_name,
				saleOrgId,
				organizationId,
				pageable);

		List<Map<String, Object>> orders = page.getContent();

		if (!orders.isEmpty()) {
			String ids = "";
			for (int i = 0; i < orders.size(); i++) {
				Map<String, Object> map = orders.get(i);
				if (i == orders.size() - 1) {
					ids += map.get("id");
				}
				else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(ids,null,null,null);
			if (readOnly == null) {
				orderItems = sort(orderItems, null);
			}
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : orders) {
				items = new ArrayList<Map<String, Object>>();
				String orderId = map.get("id").toString();
				for (Map<String, Object> itemMap : orderItems) {
					String oid = itemMap.get("orders").toString();
					if (readOnly != null) {
						if (orderId.equals(oid)
								&& itemMap.get("parent") == null) {
							items.add(itemMap);
						}
					}
					else {
						if (orderId.equals(oid)) {
							items.add(itemMap);
						}
					}
				}
				map.put("order_items", items);
			}
		}

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 查看
	 */
	@RequestMapping(value = "/view/{code}", method = RequestMethod.GET)
	public String viewCode(@PathVariable String code, Long id, String sn,
			Integer readOnly, Integer isEdit, ModelMap model) {

		model.addAttribute("readOnly", readOnly);

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}
		else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);

		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService.findShippingItemListByOrderItemId(orderItem.getId()
					.toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
							.compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);
		model.addAttribute("isEdit", isEdit);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);

		// 政策金额
		BigDecimal actualAmount = policyCountService.findTotalActualAmount(order.getStore()
				.getId());
		BigDecimal availableAmount = policyCountService.findTotalAvailableAmount(order.getStore()
				.getId());
		model.addAttribute("policyAmount",
				actualAmount.subtract(availableAmount));

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		/** 客户经理 */
		if (order != null) {
			Store store = order.getStore();
			if (store != null) {
				model.addAttribute("storeManagerList", store.getStoreManagers());
			}
		}
		/** 订单明细 */
		List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
		orderItems = sort(orderItems, null);
		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByDetailedId(id,
				null,
				null));
		model.addAttribute("payment_json", payment_json);
		/** 发货单 */
		String shipping_json = JsonUtils.toJson(shippingService.findShippingItemListByOrderId(id.toString()));
		model.addAttribute("shipping_json", shipping_json);
		/** 订单附件 */
		String orderAttach_json = JsonUtils.toJson(orderAttachService.findListByOrderId(id));
		model.addAttribute("orderAttach_json", orderAttach_json);
		/** 订单全链路 */
		String orderFullLink_json = JsonUtils.toJson(orderFullLinkService.findListByOrderSn(order.getSn()));
		model.addAttribute("orderFullLink_json", orderFullLink_json);

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		/** 北京零微科技有限公司 */
		if (order.getCompanyInfoId() == 18) {
			model.addAttribute("isLw", true);
		}

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		try {
			int useTclAddressIntf = Integer.parseInt(SystemConfig.getConfig("useTclAddressIntf",
					WebUtils.getCurrentCompanyInfoId()));
			if (useTclAddressIntf == 1) {
				model.addAttribute("useTclAddressIntf", true);
			}
		}
		catch (Exception e) {}

		Store store = order.getStore();
		if (store != null && !store.getIsMainStore()) {
			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
					order.getOrganization() == null ? null
							: order.getOrganization().getId(),
					order.getSbu() == null ? null : order.getSbu().getId());
			model.addAttribute("balance", balance);
		}

		try {
			filters.clear();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
																// 非0 展示
		}
		catch (RuntimeException e) {

		}

		return "/" + code + "/b2b/order_select/view";
	}

	/**
	 * 查看
	 */
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, String sn, Integer readOnly, Integer isEdit,
			ModelMap model) {

		model.addAttribute("readOnly", readOnly);

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}
		else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);

		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService.findShippingItemListByOrderItemId(orderItem.getId()
					.toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
							.compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);
		model.addAttribute("isEdit", isEdit);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		/** 客户经理 */
		if (order != null) {
			Store store = order.getStore();
			if (store != null) {
				model.addAttribute("storeManagerList", store.getStoreManagers());
			}
		}
		/** 订单明细 */
		List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
		orderItems = sort(orderItems, null);
		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByDetailedId(id,
				null,
				null));
		model.addAttribute("payment_json", payment_json);
		/** 发货单 */
		String shipping_json = JsonUtils.toJson(shippingService.findShippingItemListByOrderId(id.toString()));
		model.addAttribute("shipping_json", shipping_json);
		/** 订单附件 */
		String orderAttach_json = JsonUtils.toJson(orderAttachService.findListByOrderId(id));
		model.addAttribute("orderAttach_json", orderAttach_json);
		/** 订单全链路 */
		String orderFullLink_json = JsonUtils.toJson(orderFullLinkService.findListByOrderSn(order.getSn()));
		model.addAttribute("orderFullLink_json", orderFullLink_json);

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);

		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,
				filters,
				null);
		SystemDict businessType = null;
		if (businessTypes != null && businessTypes.size() > 0) {
			businessType = businessTypes.get(0);
		}
		model.addAttribute("businessType", businessType);

		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		/** 北京零微科技有限公司 */
		if (order.getCompanyInfoId() == 18) {
			model.addAttribute("isLw", true);
		}

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		try {
			int useTclAddressIntf = Integer.parseInt(SystemConfig.getConfig("useTclAddressIntf",
					WebUtils.getCurrentCompanyInfoId()));
			if (useTclAddressIntf == 1) {
				model.addAttribute("useTclAddressIntf", true);
			}
		}
		catch (Exception e) {}

		Store store = order.getStore();
		if (store != null && !store.getIsMainStore()) {
			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
					order.getOrganization() == null ? null
							: order.getOrganization().getId(),
					order.getSbu() == null ? null : order.getSbu().getId());
			model.addAttribute("balance", balance);
		}

		try {
			filters.clear();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
																// 非0 展示
		}
		catch (RuntimeException e) {

		}

		return "/b2b/order_select/view";
	}

	/**
	 * 订单列表
	 */
	@RequestMapping(value = "/orderlist", method = RequestMethod.GET)
	public String orderlist(Long[] ids, ModelMap model) {

		model.addAttribute("ids", ids);
		return "/b2b/order_select/orderlist";
	}

	@RequestMapping(value = "/orderlist_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg orderlist_data(Long[] ids, ModelMap model) {
		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < ids.length; i++) {
			if (i == ids.length - 1) {
				idss += ids[i];
			}
			else {
				idss += ids[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService.findListById(idss);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 订单明细列表
	 */
	@RequestMapping(value = "/itemlist", method = RequestMethod.GET)
	public String itemlist(Long[] itemIds, ModelMap model) {

		model.addAttribute("itemIds", itemIds);

		return "/b2b/order_select/itemlist";
	}

	@RequestMapping(value = "/itemlist_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg itemlist_data(Long[] itemIds, ModelMap model) {

		if (itemIds == null || itemIds.length == 0) {
			// 请选择订单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < itemIds.length; i++) {
			if (i == itemIds.length - 1) {
				idss += itemIds[i];
			}
			else {
				idss += itemIds[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService.findOrderItemListByItemId(idss);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 订单明细列表
	 */
	@RequestMapping(value = "/clear_itemlist", method = RequestMethod.GET)
	public String clearitemlist(Long[] itemIds, ModelMap model) {

		model.addAttribute("itemIds", itemIds);

		return "/b2b/order_select/clear_itemlist";
	}

	@RequestMapping(value = "/clear_itemlist_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg clear_itemlist_data(Long[] itemIds, ModelMap model) {

		if (itemIds == null || itemIds.length == 0) {
			// 请选择订单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < itemIds.length; i++) {
			if (i == itemIds.length - 1) {
				idss += itemIds[i];
			}
			else {
				idss += itemIds[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService.findOrderItemListByItemId(idss);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 获取订单信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/get_order_info", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getOrderInfo(Long id, Integer flag) {

		Map<String, Object> order = orderService.findListById(id.toString())
				.get(0);
		List<Map<String, Object>> items = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
		order.put("items", items);

		if (flag != null && flag.intValue() == 1) {
			List<Map<String, Object>> shipping_items = shippingService.findShippingItemListByOrderId(id.toString());
			order.put("shipping_items", shipping_items);
		}

		String jsonPage = JsonUtils.toJson(order);
		return success(jsonPage);
	}

	/**
	 * 购物车生成订单创建
	 * 
	 * @param cartId
	 * @param ids
	 * @return
	 */
	@RequestMapping(value = "/create", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg create(Long storeId, Long cartId, Long[] ids, Long areaId,
			Long freightChargeTypeId, Order order, Long paymentMethodId,
			Long shippingMethodId, Long organizationId) {

		Store store = storeService.find(storeId);
		if (store == null) {
			// 请选择客户
			return error("15135");
		}
		Area area = areaService.find(areaId);
		if (area == null) {
			// 请选择收货地区
			return error("15136");
		}
		if (ConvertUtil.isEmpty(order.getAddress())) {
			// 请填写收货地址
			return error("15137");
		}
		if (ConvertUtil.isEmpty(order.getConsignee())) {
			// 请填写收货人
			return error("15138");
		}
		if (ConvertUtil.isEmpty(order.getPhone())) {
			// 请填写收货人电话
			return error("15139");
		}
		PaymentMethod paymentMethod = paymentMethodBaseService.find(paymentMethodId);
		if (paymentMethod == null) {
			// 请选择支付方式
			return error("151040");
		}
		ShippingMethod shippingMethod = shippingMethodBaseService.find(shippingMethodId);
		if (shippingMethod == null) {
			// 请选择配送方式
			return error("151041");
		}
		Cart cart = cartService.find(cartId);
		if (cart == null || cart.isEmpty()) {
			// 购物车已清空
			return error("15126");
		}
		List<CartItem> cartItems = new ArrayList<CartItem>();
		for (Long id : ids) {
			cartItems.add(cartItemService.find(id));
		}
		SystemDict freightChargeType = null;
		if (freightChargeTypeId != null) {
			freightChargeType = systemDictService.find(freightChargeTypeId);
		}
		orderService.create(store,
				cart,
				cartItems,
				area,
				order,
				paymentMethod,
				shippingMethod,
				freightChargeType,
				organizationId);

		return success();
	}

	@RequestMapping(value = "/setFlag", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg setFlag(Long[] ids, Integer flag) {

		if (ids == null || ids.length == 0) {
			// 请选择订单
			return error("15140");
		}
		if (flag == null) {
			// 请选择旗标
			return error("15250");
		}
		orderService.setFlag(ids, flag);

		return success();
	}

	@RequestMapping(value = "/get_price", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getPrice(Long[] productId, Long storeId, ModelMap model) {

		Map<Long, Map<String, Object>> map = new HashMap<Long, Map<String, Object>>();
		Store store = storeService.find(storeId);
		Store mainStore = storeService.getMainStore();

		//获取客户当前默认价格类型
		List<Filter> filters = new ArrayList<Filter>();

		filters.add(Filter.eq("store", store));
		List<StoreSbu> storeSbu = storeSbuService.findList(null, filters, null);

		Long memberRankId = 0L;
		if (storeSbu != null && storeSbu.size() > 0) {
			MemberRank memberRank = storeSbu.get(0).getMemberRank();
			memberRankId = memberRank.getId();
		}
		else {
			return error("该客户没有维护价格类型");
		}
		for (Long id : productId) {
			Product product = productService.find(id);
			filters.clear();
			filters.add(Filter.eq("product", product));
			filters.add(Filter.eq("store", mainStore));
			ProductStore productStore = productStoreService.find(filters);
			Map<String, Object> item = new HashMap<String, Object>();
			Map<String, Object> productPrice = productPriceHeadService.findPriceByProductStore(productStore.getId(),
					memberRankId);
			if (productPrice != null) {
				item.put("member_price",
						new BigDecimal(productPrice.get("store_member_price")
								.toString()));
			}
			else {
				item.put("member_price", product.getPrice());
			}
			Map<String, Object> itemMap = priceApplyService.findItemByProduct(id,
					storeId);
			if (itemMap != null) {
				item.put("apply_sn", itemMap.get("sn"));
				item.put("apply_price", itemMap.get("price"));
				item.put("apply_item_id", itemMap.get("id"));
				BigDecimal useable_quantity = new BigDecimal(
						itemMap.get("quantity").toString()).subtract(new BigDecimal(
						itemMap.get("used_quantity").toString()));
				item.put("apply_useable_quantity", useable_quantity);
			}
			map.put(id, item);
		}
		return success().addObjX(map);
	}

	@RequestMapping(value = "/orderImportByCache", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg orderImportByCache(String uuid) throws Exception {

		orderService.orderImportByCache(uuid);
		return ResultMsg.success();
	}

	private List<Map<String, Object>> sort(List<Map<String, Object>> items,
			Long parent) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if (items != null) {
			for (Map<String, Object> map : items) {
				if ((map.get("parent") != null && parent != null && Long.parseLong(map.get("parent")
						.toString()) == parent.longValue())
						|| (map.get("parent") == null && parent == null)) {
					result.add(map);
					result.addAll(sort(items,
							Long.parseLong(map.get("id").toString())));
				}
			}
		}
		return result;
	}

	@RequestMapping(value = "/message_board", method = RequestMethod.GET)
	public String message_board(Long id, ModelMap model) throws Exception {
		Order order = orderService.find(id);
		model.addAttribute("order", order);
		if (order != null) {
			List<Filter> filters = new ArrayList<Filter>();
			List<net.shopxx.base.core.Order> orders = new ArrayList<net.shopxx.base.core.Order>();
			filters.add(Filter.eq("orderSn", order.getSn()));
			orders.add(net.shopxx.base.core.Order.asc("createDate"));
			List<MessageBoard> messageBoards = messageBoardService.findList(null,
					filters,
					orders);
			model.addAttribute("messageBoards", messageBoards);
		}
		model.addAttribute("storeMember", storeMemberService.getCurrent());
		return "/b2b/order_select/message_board";
	}

	@RequestMapping(value = "/message_board_content", method = RequestMethod.GET)
	public String message_board_content(Long id, ModelMap model)
			throws Exception {
		Order order = orderService.find(id);
		model.addAttribute("order", order);
		if (order != null) {
			List<Filter> filters = new ArrayList<Filter>();
			List<net.shopxx.base.core.Order> orders = new ArrayList<net.shopxx.base.core.Order>();
			filters.add(Filter.eq("orderSn", order.getSn()));
			orders.add(net.shopxx.base.core.Order.asc("createDate"));
			List<MessageBoard> messageBoards = messageBoardService.findList(null,
					filters,
					orders);
			model.addAttribute("messageBoards", messageBoards);
		}

		return "/b2b/order_select/message_board_content";
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String orderSn,
			String outTradeNo, Integer[] orderStatus, Integer[] exStatus,
			Integer[] paymentStatus, Integer[] shippingStatus, Integer[] flag,
			Long warehouseId, Long[] storeId, String phone, String consignee,
			String address, Long deliveryCorpId, Long productId[],
			String firstTime, String lastTime, Integer readOnly,
			Integer[] confirmStatus, String store_member_name, Long saleOrgId,
			Long organizationId, Pageable pageable, ModelMap model) {

		Integer size = orderService.count(orderSn,
				outTradeNo,
				orderStatus,
				null,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				store_member_name,
				saleOrgId,
				organizationId,
				pageable,
				null,
				null,
				null,
				null,
				null,
				null,
				null);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @param productCategoryId
	 * @param sn
	 * @param vonderCode
	 * @param mod
	 * @param name
	 * @param startTime
	 * @param endTime
	 * @param isMarketable
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			String store_member_name, Long saleOrgId, Long organizationId,
			Pageable pageable, ModelMap model, Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = orderService.findItemList(orderSn,
				outTradeNo,
				orderStatus,
				null,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				null,
				store_member_name,
				saleOrgId,
				organizationId,
				page,
				size,
				null,
				null,
				null,
				null,
				null);
		return getModelAndView(data, model);

	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = orderService.findItemList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null);
		return getModelAndView(data, model);
	}

	/** 订单列表的导出 */
	public ModelAndView getModelAndView(List<Map<String, Object>> data,
			ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("order_status") != null) {
				Integer order_status = (Integer) str.get("order_status");
				if (order_status == 0) {
					str.put("order_status_name", "未确认");
				}
				else if (order_status == 1) {
					str.put("order_status_name", "已确认");
				}
				else if (order_status == 2) {
					str.put("order_status_name", "已完成");
				}
				else if (order_status == 3) {
					str.put("order_status_name", "已作废");
				}
				else if (order_status == 4) {
					str.put("order_status_name", "已删除");
				}
				else if (order_status == 5) {
					str.put("order_status_name", "未审核");
				}
				else if (order_status == 6) {
					str.put("order_status_name", "已审核");
				}
				else if (order_status == 7) {
					str.put("order_status_name", "已保存");
				}
				else if (order_status == 8) {
					str.put("order_status_name", "已接受");
				}
			}
			if (str.get("shipping_status") != null) {
				Integer shipping_status = (Integer) str.get("shipping_status");
				if (shipping_status == 0) {
					str.put("shipping_status_name", "未发货");
				}
				else if (shipping_status == 1) {
					str.put("shipping_status_name", "部分发货");
				}
				else if (shipping_status == 2) {
					str.put("shipping_status_name", "已发货");
				}
				else if (shipping_status == 3) {
					str.put("shipping_status_name", "确认收货");
				}
			}
			
			if (str.get("paiType") != null) {
				Integer paiType = (Integer) str.get("paiType");
				if (paiType == 0) {
					str.put("paiType", "促销");
				}
				else if (paiType == 1) {
					str.put("paiType", "二等品");
				}
				else if (paiType == 2) {
					str.put("paiType", "定制");
				}
				else if (paiType == 3) {
					str.put("paiType", "工程");
				}
			}

			if (str.get("payment_status") != null) {
				Integer payment_status = (Integer) str.get("payment_status");
				if (payment_status == 0) {
					str.put("payment_status_name", "未支付");
				}
				else if (payment_status == 1) {
					str.put("payment_status_name", "部分支付");
				}
				else if (payment_status == 2) {
					str.put("payment_status_name", "完全支付");
				}
			}
			if (str.get("order_id") != null) {
				Long orderId = Long.parseLong(str.get("order_id").toString());
				Order order = orderService.find(orderId);
				BigDecimal orderAmount = BigDecimal.ZERO;
				if (order != null) {
					orderAmount = order.getAmount().setScale(2,
							BigDecimal.ROUND_HALF_UP);
				}
				str.put("orderAmount", orderAmount);
			}
			if (str.get("order_date") != null) {

				String order_date = DateUtil.convert((Date) str.get("order_date"));
				if (order_date.length() > 10) {
					order_date = order_date.substring(0, 10);
				}
				str.put("orderDate", order_date);
			}
			if (str.get("order_create_date") != null) {
				// String order_create_date = (String)
				// str.get("order_create_date");
				String order_create_date = DateUtil.convert((Date) str.get("order_create_date"));
				if (order_create_date.length() > 19) {
					order_create_date = order_create_date.substring(0, 19);
				}
				str.put("orderCreateDate", order_create_date);
			}
			if (str.get("order_check_date") != null) {
				String order_check_date = DateUtil.convert((Date) str.get("order_check_date"));
				if (order_check_date.length() > 19) {
					order_check_date = order_check_date.substring(0, 19);
				}
				str.put("orderCheckDate", order_check_date);
			}
			if (str.get("price") != null) {
				BigDecimal price = new BigDecimal(str.get("price").toString());
				str.put("price", price.setScale(2, BigDecimal.ROUND_HALF_UP));
			}

			if (str.get("price") != null && str.get("quantity") != null) {
				BigDecimal price = new BigDecimal(str.get("price").toString());
				BigDecimal quantity = new BigDecimal(str.get("quantity")
						.toString());

				str.put("count",
						price.multiply(quantity).setScale(2,
								BigDecimal.ROUND_HALF_UP));
			}
			else {
				str.put("count", "0.00");
			}
			if (str.get("quantity") != null) {
				BigDecimal quantity = new BigDecimal(str.get("quantity")
						.toString());
				str.put("quantity", NumberFormat.getInstance().format(quantity));
			}
			if (str.get("xsStatus") != null) {
				Integer xsStatus = (Integer) str.get("xsStatus");
				if (xsStatus == 0) {
					str.put("xsStatus", "未审核");
				}
				else if (xsStatus == 1) {
					str.put("xsStatus", "已审核");
				}
				else if (xsStatus == 2) {
					str.put("xsStatus", "作废");
				}
				else if (xsStatus == 3) {
					str.put("xsStatus", "部分发货");
				}
				else if (xsStatus == 4) {
					str.put("xsStatus", "完全发货");
				}
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "创建时间",
				"订单编号",
				"订单状态",
				"来源单号",
				"业务类型",
				"下单时间",
				"下单人",
				"审核人",
				"审核时间",
				"配送状态",
				"支付状态",
				"订单金额",
				"备注",
				"经营组织",
				"机构",
				"客户名称",
				"客户简称",
				"客户编码",
				"仓库",
				"产品名称",
				"产品型号",
				"产品编号",
				"产品描述",
				"木钟花色",
				"规格",
				"产品等级",
				"产品分类",
				"特价单号",
				"特价类型",
				"工程名称",
				"订单数量",
				"订单支数",
				"订单箱数",
				"单价",
				"小计",
				"交货期",
				"发货数量",
				"发货支数",
				"发货箱数",
				"单价",
				"实际发货金额",
				"发货状态",
				"发票抬头",
				"收货人",
				"收货人电话",
				"司机",
				"车牌",
				"电话",
				"收货地区",
				"收货地址",
				"物流快递",
				"配送方式"

		};
		// 设置单元格取值
		String[] properties = { "orderCreateDate",
				"order_sn",
				"order_status_name",
				"out_no",
				"value",
				"orderDate",
				"store_member_name",
				"check_store_member_name",
				"orderCheckDate",
				"shipping_status_name",
				"payment_status_name",
				"orderAmount",
				"memo",
				"organization_name",
				"sale_org_name",
				"store_name",
				"store_alias",
				"store_sn",
				"order_warehouse_name",
				"name",
				"model",
				"vonderCode",
				"description",
				"wood_type_or_color",
				"spec",
				"levelName",
				"product_category_name",
				"paiSn",
				"paiType",
				"engineeringName",
				"quantity",
				"box_quantity",
				"branch_quantity",
				"price",
				"count",
				"delivery_time",
				"shipped_quantity",
				"shipped_branch_quantity",
				"shipped_box_quantity",
				"quantity",
				"price",
				"xsStatus",
				"invoiceTitle",
				"consignee",
				"phone",
				"driverInfoName",
				"driverInfoMobile",
				"carNumber",
				"area_full_name",
				"address",
				"delivery_corp_name",
				"shippingMethodName" };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, null, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	@RequestMapping(value = "/refushPdf", method = RequestMethod.GET)
	public @ResponseBody
	ResultMsg refushPdf(Long id) {
		Order order = orderService.find(id);
		orderService.refushPdf(order);
		return success();

	}

	@RequestMapping(value = "/shipping2order", method = RequestMethod.GET)
	public @ResponseBody
	String shipping2order(Long[] ids) {

		int i = 0;
		List<Filter> filters = new ArrayList<Filter>();
		if (ids != null && ids.length > 0) {
			for (Long id : ids) {
				Shipping shipping = shippingService.find(id);
				Store supplier = shipping.getSupplier();
				Order order = new Order();
				List<OrderItem> orderItems = new ArrayList<OrderItem>();
				List<ShippingItem> shippingItems = shipping.getShippingItems();
				for (ShippingItem shippingItem : shippingItems) {
					OrderItem orderItem = new OrderItem();
					Product product = shippingItem.getProduct();
					OrderItem sourceOrderItem = shippingItem.getOrderItem();
					// 复制到新订单项
					BeanUtils.copyProperties(sourceOrderItem,
							orderItem,
							new String[] { "id",
									"createDate",
									"modifyDate",
									"order",
									"orderItemPartses",
									"orderItemAttachs" });
					orderItem.setSourceOrderItemId(sourceOrderItem.getId());
					orderItem.setQuantity(shippingItem.getQuantity());
					orderItem.setShipPlanQuantity(shippingItem.getQuantity());
					orderItem.setShippedQuantity(shippingItem.getShippedQuantity());
					orderItem.setOrder(order);
					orderItem.setWarehouse(shipping.getWarehouse());
					orderItem.setOutTradeId(shippingItem.getId().toString());

					Map<String, Object> priceMap = contractPriceService.findItemByProduct(product.getId(),
							supplier.getId());
					if (priceMap != null) {
						BigDecimal price = new BigDecimal(priceMap.get("price")
								.toString());
						orderItem.setPrice(price);
					}
					else {
						orderItem.setMemo("无合约价");
					}

					orderItems.add(orderItem);

					sourceOrderItem.setIsPurchase(1);
					orderItemService.update(sourceOrderItem);
				}
				order.setOrderItems(orderItems);
				BigDecimal zero = BigDecimal.ZERO;
				order.setArea(shipping.getArea());
				order.setAddress(shipping.getAddress());
				order.setConsignee(shipping.getConsignee());
				order.setCouponDiscount(zero);
				order.setFreight(zero);
				order.setOffsetAmount(zero);
				order.setOrderStatus(OrderStatus.audited);
				filters.clear();
				filters.add(Filter.eq("method", PaymentMethod.Method.offline));
				List<PaymentMethod> paymentMethods = paymentMethodBaseService.findList(1,
						filters,
						null);
				if (!paymentMethods.isEmpty()) {
					PaymentMethod paymentMethod = paymentMethods.get(0);
					order.setPaymentMethod(paymentMethod);
					order.setPaymentMethodName(paymentMethod.getName());
				}
				else {
					order.setPaymentMethodName("线下支付");
				}
				filters.clear();
				filters.add(Filter.eq("name", shipping.getShippingMethod()));
				List<ShippingMethod> shippingMethods = shippingMethodBaseService.findList(1,
						filters,
						null);
				if (!shippingMethods.isEmpty()) {
					ShippingMethod shippingMethod = shippingMethods.get(0);
					order.setShippingMethod(shippingMethod);
					order.setShippingMethodName(shippingMethod.getName());
				}
				else {
					order.setShippingMethodName("快递");
				}
				order.setPaymentStatus(PaymentStatus.paid);
				if (shipping.getStatus() < 3) {
					order.setShippingStatus(ShippingStatus.unshipped);
				}
				else if (shipping.getStatus() == 3) {
					order.setShippingStatus(ShippingStatus.partialShipment);
				}
				else {
					order.setShippingStatus(ShippingStatus.shipped);
				}
				order.setPhone(shipping.getPhone());
				order.setPromotionDiscount(zero);
				order.setZipCode(shipping.getZipCode());
				order.setStoreMember(storeMemberService.find(14781L));
				order.setStore(shipping.getStore());
				order.setSupplier(shipping.getSupplier());
				order.setType("6");
				order.setOrderType(4);
				order.setIsMobile(false);
				String sn = SnUtil.getOrderSn();
				order.setSn(sn);
				order.setOutTradeNo(shipping.getSn());
				order.setCreateDate(shipping.getCreateDate());
				order.setOrderDate(shipping.getCreateDate());
				order.setAmountPaid(order.getAmount());

				orderService.save(order);

				for (OrderItem orderItem : order.getOrderItems()) {
					Long shippingItemId = Long.parseLong(orderItem.getOutTradeId());
					ShippingItem shippingItem = shippingItemService.find(shippingItemId);
					shippingItem.setPurOrderItem(orderItem);
					shippingItem.setPurOrderSn(order.getSn());
					shippingItem.setOrderType(4);
					shippingItemService.update(shippingItem);
				}
				shipping.setOrderType(4);
				if (shipping.getStatus() == 1) {
					shipping.setStatus(0);
				}
				shippingService.update(shipping);
				i++;
			}
		}
		else {
			filters.clear();
			filters.add(Filter.ne("status", 2));
			filters.add(Filter.isNotNull("supplier"));
			filters.add(Filter.ne("orderType", 4));
			List<Shipping> shippings = shippingService.findList(null,
					filters,
					null);
			for (Shipping shipping : shippings) {

				Order order = new Order();
				Store supplier = shipping.getSupplier();
				List<OrderItem> orderItems = new ArrayList<OrderItem>();
				List<ShippingItem> shippingItems = shipping.getShippingItems();
				for (ShippingItem shippingItem : shippingItems) {
					OrderItem orderItem = new OrderItem();
					Product product = shippingItem.getProduct();
					OrderItem sourceOrderItem = shippingItem.getOrderItem();
					// 复制到新订单项
					BeanUtils.copyProperties(sourceOrderItem,
							orderItem,
							new String[] { "id",
									"createDate",
									"modifyDate",
									"order",
									"orderItemPartses",
									"orderItemAttachs" });
					orderItem.setSourceOrderItemId(sourceOrderItem.getId());
					orderItem.setQuantity(shippingItem.getQuantity());
					orderItem.setShipPlanQuantity(shippingItem.getQuantity());
					orderItem.setShippedQuantity(shippingItem.getShippedQuantity());
					orderItem.setOrder(order);
					orderItem.setWarehouse(shipping.getWarehouse());
					orderItem.setOutTradeId(shippingItem.getId().toString());

					Map<String, Object> priceMap = contractPriceService.findItemByProduct(product.getId(),
							supplier.getId());
					if (priceMap != null) {
						BigDecimal price = new BigDecimal(priceMap.get("price")
								.toString());
						orderItem.setPrice(price);
					}
					else {
						orderItem.setMemo("无合约价");
					}

					orderItems.add(orderItem);

					sourceOrderItem.setIsPurchase(1);
					orderItemService.update(sourceOrderItem);
				}
				order.setOrderItems(orderItems);
				BigDecimal zero = BigDecimal.ZERO;
				order.setArea(shipping.getArea());
				order.setAddress(shipping.getAddress());
				order.setConsignee(shipping.getConsignee());
				order.setCouponDiscount(zero);
				order.setFreight(zero);
				order.setOffsetAmount(zero);
				order.setOrderStatus(OrderStatus.audited);
				filters.clear();
				filters.add(Filter.eq("method", PaymentMethod.Method.offline));
				List<PaymentMethod> paymentMethods = paymentMethodBaseService.findList(1,
						filters,
						null);
				if (!paymentMethods.isEmpty()) {
					PaymentMethod paymentMethod = paymentMethods.get(0);
					order.setPaymentMethod(paymentMethod);
					order.setPaymentMethodName(paymentMethod.getName());
				}
				else {
					order.setPaymentMethodName("线下支付");
				}
				filters.clear();
				filters.add(Filter.eq("name", shipping.getShippingMethod()));
				List<ShippingMethod> shippingMethods = shippingMethodBaseService.findList(1,
						filters,
						null);
				if (!shippingMethods.isEmpty()) {
					ShippingMethod shippingMethod = shippingMethods.get(0);
					order.setShippingMethod(shippingMethod);
					order.setShippingMethodName(shippingMethod.getName());
				}
				else {
					order.setShippingMethodName("快递");
				}
				order.setPaymentStatus(PaymentStatus.paid);
				if (shipping.getStatus() < 3) {
					order.setShippingStatus(ShippingStatus.unshipped);
				}
				else if (shipping.getStatus() == 3) {
					order.setShippingStatus(ShippingStatus.partialShipment);
				}
				else {
					order.setShippingStatus(ShippingStatus.shipped);
				}
				order.setPhone(shipping.getPhone());
				order.setPromotionDiscount(zero);
				order.setZipCode(shipping.getZipCode());
				order.setStoreMember(storeMemberService.find(14781L));
				order.setStore(shipping.getStore());
				order.setSupplier(shipping.getSupplier());
				order.setType("6");
				order.setOrderType(4);
				order.setIsMobile(false);
				String sn = SnUtil.getOrderSn();
				order.setSn(sn);
				order.setOutTradeNo(shipping.getSn());
				order.setCreateDate(shipping.getCreateDate());
				order.setOrderDate(shipping.getCreateDate());
				order.setAmountPaid(order.getAmount());

				orderService.save(order);

				for (OrderItem orderItem : order.getOrderItems()) {
					Long shippingItemId = Long.parseLong(orderItem.getOutTradeId());
					ShippingItem shippingItem = shippingItemService.find(shippingItemId);
					shippingItem.setPurOrderItem(orderItem);
					shippingItem.setPurOrderSn(order.getSn());
					shippingItem.setOrderType(4);
					shippingItemService.update(shippingItem);
				}
				shipping.setOrderType(4);
				if (shipping.getStatus() == 1) {
					shipping.setStatus(0);
				}
				shippingService.update(shipping);
				i++;
			}
		}
		return "success:" + i;

	}

	/**
	 * 查看pdf
	 * 
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkPdf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checkPdf(Long id) throws Exception {
		Order order = orderService.find(id);
		if (order == null) return error("订单不存在！");
		if (!order.getOrderStatus().equals(Order.OrderStatus.audited)) {
			return error("订单未审核或已作废");
		}
		TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
		if (triplicateForm == null) {
			triplicateForm = orderService.createTriplicateForm(order);
		}
		return success(triplicateForm.getUrl());

	}

	/**
	 * 查询客户相关客户经理数据
	 */
	@RequestMapping(value = "/select_manager", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_accountManager_data(long storeId, ModelMap model) {

		Store store = storeService.find(storeId);
		List<StoreManager> storeManagers = null;
		if (store != null) {

			storeManagers = store.getStoreManagers();
		}
		return this.success().addObjX(storeManagers);
	}

}