package net.shopxx.order.b2b.controller;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.MessageBoard;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.PaymentMethod;
import net.shopxx.basic.entity.ShippingMethod;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.MessageBoardService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.PaymentMethodBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.PolicyCountContractService;
import net.shopxx.finance.service.PolicyCountService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreManager;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.service.CreditRechargeContractService;
import net.shopxx.member.service.DepositRechargeContractService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.order.entity.Cart;
import net.shopxx.order.entity.CartItem;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.Order.OrderStatus;
import net.shopxx.order.entity.Order.PaymentStatus;
import net.shopxx.order.entity.Order.ShippingStatus;
import net.shopxx.order.entity.OrderAttach;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.purchase.service.ContractPriceService;
import net.shopxx.order.service.CartItemService;
import net.shopxx.order.service.CartService;
import net.shopxx.order.service.ContractService;
import net.shopxx.order.service.CostService;
import net.shopxx.order.service.OffsiteBranchService;
import net.shopxx.order.service.OrderAttachService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderProjectService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.PriceApplyService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.order.service.StandbyAddressService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductStore;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductPriceHeadService;
import net.shopxx.product.service.ProductStoreBaseService;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfObjConfigBaseService;

@Controller("b2bOrderProjectController")
@RequestMapping("/b2b/order_project")
public class OrderProjectController extends BaseController {

	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "cartServiceImpl")
	private CartService cartService;
	@Resource(name = "cartItemServiceImpl")
	private CartItemService cartItemService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;
	@Resource(name = "paymentMethodBaseServiceImpl")
	private PaymentMethodBaseService paymentMethodBaseService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "orderAttachServiceImpl")
	private OrderAttachService orderAttachService;
	@Resource(name = "priceApplyServiceImpl")
	private PriceApplyService priceApplyService;
	@Resource(name = "productPriceHeadServiceImpl")
	private ProductPriceHeadService productPriceHeadService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "messageBoardServiceImpl")
	private MessageBoardService messageBoardService;
	@Resource(name = "productStoreBaseServiceImpl")
	private ProductStoreBaseService productStoreService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "contractPriceServiceImpl")
	private ContractPriceService contractPriceService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;

	@Resource(name = "creditRechargeContractServiceImpl")
	private CreditRechargeContractService creditRechargeContractService;
	@Resource(name = "depositRechargeContractServiceImpl")
	private DepositRechargeContractService depositRechargeContractService;
	@Resource(name = "policyCountContractServiceImpl")
	private PolicyCountContractService policyCountContractService;
	@Resource(name = "policyCountServiceImpl")
	private PolicyCountService policyCountService;
	@Resource(name = "orderProjectServiceImpl")
	private OrderProjectService orderProjectService;
	@Resource(name = "offsiteBranchServiceImpl")
	private OffsiteBranchService offsiteBranchService;
	@Resource(name = "costServiceImpl")
	private CostService costService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "contractServiceImpl")
	private ContractService contractService;
	@Resource(name = "standbyAddressServiceImpl")
	private StandbyAddressService standbyAddressService;

	// @Resource(name = "intfOtherTableToServiceImpl")
	// private IntfOtherTableToService intfOtherTableToService;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer readOnly, Long objTypeId, Long objid,
			Long[] ci_ids, ModelMap model) {

		if (ci_ids != null && ci_ids.length > 0) {
			readOnly = 1;
		}
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("ci_ids", ci_ids);
		return "/b2b/order_project/list_tb";
	}

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb_code(@PathVariable String code, Integer readOnly,
			Long objTypeId, Long objid, Long[] ci_ids, ModelMap model) {

		if (ci_ids != null && ci_ids.length > 0) {
			readOnly = 1;
		}
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("ci_ids", ci_ids);
		model.addAttribute("code", code);
		return CommonUtil.getFolderPrefix(code) + "/b2b/order_project/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Pageable pageable,
			Integer readOnly, ModelMap model) {
		model.addAttribute("readOnly", readOnly);
		/** 北京零微科技有限公司 */
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId != null && companyInfoId == 18) {
			model.addAttribute("isLw", true);
		}

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig(
					"saveOrder2Unaudited", WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);
		model.addAttribute("code", code);
		return CommonUtil.getFolderPrefix(code) + "/b2b/order_project/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, Integer readOnly, ModelMap model) {
		model.addAttribute("readOnly", readOnly);
		/** 北京零微科技有限公司 */
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId != null && companyInfoId == 18) {
			model.addAttribute("isLw", true);
		}

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig(
					"saveOrder2Unaudited", WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		return "/b2b/order_project/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			Pageable pageable, ModelMap model, String storeMemberName,
			String contractName) {

		Page<Map<String, Object>> page = orderService.findPage(orderSn,
				outTradeNo,
				orderStatus,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				null,
				pageable,
				new Integer[] { 3 },
				storeMemberName,
				contractName);

		List<Map<String, Object>> orders = page.getContent();

		if (!orders.isEmpty()) {
			String ids = "";
			for (int i = 0; i < orders.size(); i++) {
				Map<String, Object> map = orders.get(i);
				if (i == orders.size() - 1) {
					ids += map.get("id");
				}
				else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> orderItems = orderService
					.findOrderItemListByOrderId(ids,null,null,null);
			if (readOnly == null) {
				orderItems = sort(orderItems, null);
			}
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : orders) {
				items = new ArrayList<Map<String, Object>>();
				String orderId = map.get("id").toString();
				for (Map<String, Object> itemMap : orderItems) {
					String oid = itemMap.get("orders").toString();
					if (readOnly != null) {
						if (orderId.equals(oid)
								&& itemMap.get("parent") == null) {
							items.add(itemMap);
						}
					}
					else {
						if (orderId.equals(oid)) {
							items.add(itemMap);
						}
					}
				}
				map.put("order_items", items);
			}
		}

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 查看
	 */
	@RequestMapping(value = "/view/{code}", method = RequestMethod.GET)
	public String viewCode(@PathVariable String code, Long id, String sn,
			Integer readOnly, Integer isEdit, ModelMap model) {

		model.addAttribute("readOnly", readOnly);

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}
		else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);

		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService
					.findShippingItemListByOrderItemId(
							orderItem.getId().toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
							.compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);
		model.addAttribute("isEdit", isEdit);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		/** 客户经理 */
		if (order != null) {
			Store store = order.getStore();
			if (store != null) {
				model.addAttribute("storeManagerList",
						store.getStoreManagers());
			}
		}
		/** 订单明细 */
		List<Map<String, Object>> orderItems = orderService
				.findOrderItemListByOrderId(id.toString(),null,null,null);
		orderItems = sort(orderItems, null);

		for (int i = 0; i < orderItems.size(); i++) {
			Map<String, Object> map = orderItems.get(i);
			List<Map<String, Object>> grossProfitAnalysiss = orderService
					.findGrossProfitAnalysisListByOrderId(id.toString());
			for (int j = 0; j < grossProfitAnalysiss.size(); j++) {
				if (map.get("id") == grossProfitAnalysiss.get(j)
						.get("order_item")
						&& map.get("orders") == grossProfitAnalysiss.get(j)
								.get("orders")
						&& map.get("product") == grossProfitAnalysiss.get(j)
								.get("product")) {
					map.put("market_price",
							grossProfitAnalysiss.get(j).get("market_price"));
					map.put("market_fa_price",
							grossProfitAnalysiss.get(j).get("market_fa_price"));
					map.put("original_fa_price",
							grossProfitAnalysiss.get(j)
									.get("original_fa_price"));
					map.put("service_price",
							grossProfitAnalysiss.get(j).get("service_price"));
					map.put("grossProfitAnalysisId",
							grossProfitAnalysiss.get(j).get("id"));
					map.put("grossProfitAnalysisOrderId",
							grossProfitAnalysiss.get(j).get("orders"));
					map.put("grossProfitAnalysisOrderItemId",
							grossProfitAnalysiss.get(j).get("order_item"));
					map.put("grossProfitAnalysisProductId",
							grossProfitAnalysiss.get(j).get("product"));
				}
			}
		}
		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);
		/** 付款单 */
		String payment_json = JsonUtils
				.toJson(paymentService.findListByDetailedId(id, null, null));
		model.addAttribute("payment_json", payment_json);
		/** 发货单 */
		String shipping_json = JsonUtils.toJson(
				shippingService.findShippingItemListByOrderId(id.toString()));
		model.addAttribute("shipping_json", shipping_json);
		/** 订单附件 */
		String orderAttach_json = JsonUtils
				.toJson(orderAttachService.findListByOrderId(id));
		model.addAttribute("orderAttach_json", orderAttach_json);
		/** 订单全链路 */
		String orderFullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByOrderSn(order.getSn()));
		model.addAttribute("orderFullLink_json", orderFullLink_json);

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		/** 北京零微科技有限公司 */
		if (order.getCompanyInfoId() == 18) {
			model.addAttribute("isLw", true);
		}

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer
					.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
							WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig(
					"saveOrder2Unaudited", WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		Long storeId = order.getStore().getId();
		BigDecimal actualAmount = policyCountService
				.findTotalActualAmount(storeId);
		BigDecimal availableAmount = policyCountService
				.findTotalAvailableAmount(storeId);

		//政策
		List<Map<String, Object>> list3 = policyCountContractService
				.findItemListByOrderNo(order.getSn(), 1);
		for (Map<String, Object> data : list3) {
			data.put("actual_amount", actualAmount);
			data.put("available_amount", availableAmount);
		}

		//回款
		List<Map<String, Object>> list2 = depositRechargeContractService
				.findItemListByOrderNo(order.getSn(), 1);

		//授信
		List<Map<String, Object>> list1 = creditRechargeContractService
				.findItemListByOrderNo(order.getSn(), 1);

		List<Map<String, Object>> megaList = new ArrayList<Map<String, Object>>();
		megaList.addAll(list3);
		megaList.addAll(list2);
		megaList.addAll(list1);
		model.addAttribute("megaListItems", JsonUtils.toJson(megaList));

		return "/" + code + "/b2b/order_project/view";
	}

	/**
	 * 查看
	 */
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, String sn, Integer readOnly, Integer isEdit,
			ModelMap model) {

		model.addAttribute("readOnly", readOnly);

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}
		else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);

		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService
					.findShippingItemListByOrderItemId(
							orderItem.getId().toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
							.compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);
		model.addAttribute("isEdit", isEdit);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		/** 客户经理 */
		if (order != null) {
			Store store = order.getStore();
			if (store != null) {
				model.addAttribute("storeManagerList",
						store.getStoreManagers());
			}
		}
		/** 订单明细 */
		List<Map<String, Object>> orderItems = orderService
				.findOrderItemListByOrderId(id.toString(),null,null,null);
		orderItems = sort(orderItems, null);

		List<Map<String, Object>> grossProfitAnalysiss = orderService
				.findGrossProfitAnalysisListByOrderId(id.toString());

		for (int i = 0; i < orderItems.size(); i++) {
			Map<String, Object> map = orderItems.get(i);
			for (int j = 0; j < grossProfitAnalysiss.size(); j++) {
				if (map.get("id")
						.equals(grossProfitAnalysiss.get(j).get("order_item"))
						&& map.get("orders").equals(
								grossProfitAnalysiss.get(j).get("orders"))
						&& map.get("product").equals(
								grossProfitAnalysiss.get(j).get("product"))) {

					map.put("market_price",
							grossProfitAnalysiss.get(j)
									.get("market_price") == null ? 0
											: grossProfitAnalysiss.get(j)
													.get("market_price"));
					map.put("market_fa_price",
							grossProfitAnalysiss.get(j)
									.get("market_fa_price") == null ? 0
											: grossProfitAnalysiss.get(j)
													.get("market_fa_price"));
					map.put("original_fa_price",
							grossProfitAnalysiss.get(j)
									.get("original_fa_price") == null ? 0
											: grossProfitAnalysiss.get(j)
													.get("original_fa_price"));
					map.put("factory_price",
							grossProfitAnalysiss.get(j)
									.get("factory_price") == null ? 0
											: grossProfitAnalysiss.get(j)
													.get("factory_price"));
					map.put("service_price",
							grossProfitAnalysiss.get(j)
									.get("service_price") == null ? 0
											: grossProfitAnalysiss.get(j)
													.get("service_price"));
					map.put("grossProfitAnalysisId",
							grossProfitAnalysiss.get(j).get("id"));
					map.put("grossProfitAnalysisOrderId",
							grossProfitAnalysiss.get(j).get("orders"));
					map.put("grossProfitAnalysisOrderItemId",
							grossProfitAnalysiss.get(j).get("order_item"));
					map.put("grossProfitAnalysisProductId",
							grossProfitAnalysiss.get(j).get("product"));
				}
			}
		}

		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);

		/** 订单毛利 */
		//List<Map<String, Object>> GrossProfitAnalysiss = orderService.findGrossProfitAnalysisListByOrderId(id.toString());
		//String grossProfitAnalysiss_json = JsonUtils.toJson(GrossProfitAnalysiss);
		//model.addAttribute("grossProfitAnalysiss_json", grossProfitAnalysiss_json);

		/** 付款单 */
		String payment_json = JsonUtils
				.toJson(paymentService.findListByDetailedId(id, null, null));
		model.addAttribute("payment_json", payment_json);
		/** 发货单 */
		String shipping_json = JsonUtils.toJson(
				shippingService.findShippingItemListByOrderId(id.toString()));
		model.addAttribute("shipping_json", shipping_json);
		/** 订单附件 */
		String orderAttach_json = JsonUtils
				.toJson(orderAttachService.findListByOrderId(id));
		model.addAttribute("orderAttach_json", orderAttach_json);
		/** 订单全链路 */
		String orderFullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByOrderSn(order.getSn()));
		model.addAttribute("orderFullLink_json", orderFullLink_json);

		/** 备用地址信息*/
		String standbyAddress_json = JsonUtils
				.toJson(standbyAddressService.findListByOrderSn(order.getId()));
		model.addAttribute("standbyAddress_json", standbyAddress_json);

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}

		//费用类型 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "CostType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> systemDicts = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("systemDicts", systemDicts);

		//工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "FactoryName"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> factorySystemDicts = systemDictBaseService
				.findList(null, filters, null);
		model.addAttribute("factorySystemDicts", factorySystemDicts);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		//币种
		filters.clear();
		filters.add(Filter.eq("code", "Currency"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> currencies = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("currencies", currencies);
		//订单分类
		List<Filter> filterss = new ArrayList<Filter>();
		filterss.add(Filter.eq("code", "OrderClassify"));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> orderClassifys = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("orderClassifys", orderClassifys);

		//订单类型
		filterss.clear();
		filterss.add(Filter.eq("code", "OrderStyle"));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> orderStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("orderStyles", orderStyles);

		//贸易类型
		filterss.clear();
		filterss.add(Filter.eq("code", "TradeStyle"));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> tradeStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("tradeStyles", tradeStyles);

		//行业类别industryStyle
		filterss.clear();
		filterss.add(Filter.eq("code", "IndustryStyle"));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> industryStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("industryStyles", industryStyles);

		//销售体系
		filterss.clear();
		filterss.add(Filter.eq("code", "SaleStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> saleStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("saleStyles", saleStyles);

		//付款条件
		filterss.clear();
		filterss.add(Filter.eq("code", "PaymentConditions"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> paymentConditions = systemDictBaseService
				.findList(null, filterss, null);
		model.addAttribute("paymentConditions", paymentConditions);

		/** 北京零微科技有限公司 */
		if (order.getCompanyInfoId() == 18) {
			model.addAttribute("isLw", true);
		}

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer
					.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
							WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig(
					"saveOrder2Unaudited", WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		Long storeId = order.getStore().getId();
		BigDecimal actualAmount = policyCountService
				.findTotalActualAmount(storeId);
		BigDecimal availableAmount = policyCountService
				.findTotalAvailableAmount(storeId);

		//政策
		List<Map<String, Object>> list3 = policyCountContractService
				.findItemListByOrderNo(order.getSn(), 1);
		for (Map<String, Object> data : list3) {
			data.put("actual_amount", actualAmount);
			data.put("available_amount", availableAmount);
		}

		//回款
		List<Map<String, Object>> list2 = depositRechargeContractService
				.findItemListByOrderNo(order.getSn(), 1);

		//授信
		List<Map<String, Object>> list1 = creditRechargeContractService
				.findItemListByOrderNo(order.getSn(), 1);

		List<Map<String, Object>> megaList = new ArrayList<Map<String, Object>>();
		megaList.addAll(list3);
		megaList.addAll(list2);
		megaList.addAll(list1);
		model.addAttribute("megaListItems", JsonUtils.toJson(megaList));

		List<Map<String, Object>> costs = costService
				.findListByBillCode(order.getSn(), 0);
		model.addAttribute("costJson", JsonUtils.toJson(costs));

		List<Map<String, Object>> offsiteBranchs = offsiteBranchService
				.findListByBillCode(order.getSn(), 0);
		model.addAttribute("offsiteBranchJson",
				JsonUtils.toJson(offsiteBranchs));

		return "/b2b/order_project/view";
	}

	/**
	 * 订单列表
	 */
	@RequestMapping(value = "/orderlist", method = RequestMethod.GET)
	public String orderlist(Long[] ids, ModelMap model) {

		model.addAttribute("ids", ids);
		return "/b2b/order_project/orderlist";
	}

	@RequestMapping(value = "/orderlist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg orderlist_data(Long[] ids, ModelMap model) {
		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < ids.length; i++) {
			if (i == ids.length - 1) {
				idss += ids[i];
			}
			else {
				idss += ids[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService.findListById(idss);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 订单明细列表
	 */
	@RequestMapping(value = "/itemlist", method = RequestMethod.GET)
	public String itemlist(Long[] itemIds, ModelMap model) {

		model.addAttribute("itemIds", itemIds);

		return "/b2b/order_project/itemlist";
	}

	@RequestMapping(value = "/itemlist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg itemlist_data(Long[] itemIds,
			ModelMap model) {

		if (itemIds == null || itemIds.length == 0) {
			// 请选择订单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < itemIds.length; i++) {
			if (i == itemIds.length - 1) {
				idss += itemIds[i];
			}
			else {
				idss += itemIds[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService
				.findOrderItemListByItemId(idss);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 订单明细列表
	 */
	@RequestMapping(value = "/clear_itemlist", method = RequestMethod.GET)
	public String clearitemlist(Long[] itemIds, ModelMap model) {

		model.addAttribute("itemIds", itemIds);

		return "/b2b/order_project/clear_itemlist";
	}

	@RequestMapping(value = "/clear_itemlist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg clear_itemlist_data(Long[] itemIds,
			ModelMap model) {

		if (itemIds == null || itemIds.length == 0) {
			// 请选择订单
			return ResultMsg.error("15140", model);
		}
		String idss = "";
		for (int i = 0; i < itemIds.length; i++) {
			if (i == itemIds.length - 1) {
				idss += itemIds[i];
			}
			else {
				idss += itemIds[i] + ",";
			}
		}
		List<Map<String, Object>> page = orderService
				.findOrderItemListByItemId(idss);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 审核
	 * 
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check(Long orderId, Order order) {

		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}

		// 附件
		List<OrderAttach> orderAttachs = order.getOrderAttachs();
		orderService.check(orderId, orderAttachs);

		return success();
	}

	/**
	 * 批量审核
	 * 
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/checks", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checks(Long orderId) {

		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}

		orderService.check(orderId, null);

		return success();
	}

	/**
	 * 审核
	 * 
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long orderId, Order order,
			Long objConfId) {

		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}

		// 附件
		List<OrderAttach> orderAttachs = order.getOrderAttachs();
		//orderService.checkOrderWf(orderId, orderAttachs, objConfId);

		return success();
	}

	/**
	 * 分配仓库
	 * 
	 * @param id
	 * @param warehouseId
	 * @return
	 */
	@RequestMapping(value = "/set_warehouse", method = RequestMethod.POST)
	public @ResponseBody ResultMsg setWarehouse(Long id, Long warehouseId) {

		if (id == null) {
			// 请选择订单
			return error("15140");
		}
		if (warehouseId == null) {
			// 请选择仓库
			return error("15141");
		}

		OrderItem orderItem = orderItemService.find(id);
		Order order = orderItem.getOrder();
		if (!order.getOrderStatus().equals(OrderStatus.unaudited)) {
			// 只允许未审核状态的订单进行分配仓库
			return error("15143");
		}
		orderService.setWarehouse(id, warehouseId);

		return success();
	}

	/**
	 * 查询订单列表
	 */
	@RequestMapping(value = "/select_order", method = RequestMethod.GET)
	public String selectOrder(Integer orderStatus, Integer paymentStatus,
			Integer[] shippingStatus, Integer isReturn, Pageable pageable,
			ModelMap model) {

		model.addAttribute("paymentStatus", paymentStatus);
		model.addAttribute("shippingStatus", shippingStatus);
		model.addAttribute("orderStatus", orderStatus);
		model.addAttribute("isReturn", isReturn);
		return "/b2b/order_project/select_order";
	}

	/**
	 * 查询订单列表数据
	 */
	@RequestMapping(value = "/select_order_data", method = RequestMethod.GET)
	public @ResponseBody ResultMsg select_order_data(String orderSn,
			String outTradeNo, Integer[] orderStatus, Integer[] exStatus,
			Integer[] shippingStatus, Integer[] paymentStatus, Integer[] flag,
			Long warehouseId, Long[] storeId, String phone, String consignee,
			String address, Long deliveryCorpId, Long productId[],
			String firstTime, String lastTime, Integer[] confirmStatus,
			Integer isReturn, Long saleOrgId, Long organizationId,
			Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = orderService.findPage(orderSn,
				outTradeNo,
				orderStatus,
				shippingStatus,
				null,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				isReturn, // 1 退货单查订单用 // 去掉已经全部做了退货单的订单
				null,
				saleOrgId,
				organizationId,
				pageable);

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 获取订单信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/get_order_info", method = RequestMethod.POST)
	public @ResponseBody ResultMsg getOrderInfo(Long id, Integer flag) {

		Map<String, Object> order = orderService.findListById(id.toString())
				.get(0);
		List<Map<String, Object>> items = orderService
				.findOrderItemListByOrderId(id.toString(),null,null,null);
		order.put("items", items);

		if (flag != null && flag.intValue() == 1) {
			List<Map<String, Object>> shipping_items = shippingService
					.findShippingItemListByOrderId(id.toString());
			order.put("shipping_items", shipping_items);
		}

		String jsonPage = JsonUtils.toJson(order);
		return success(jsonPage);
	}

	/**
	 * 购物车生成订单预览
	 * 
	 * @param cartId
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/info", method = RequestMethod.GET)
	public String info(Long cartId, Long[] ids, ModelMap model) {

		Cart cart = cartService.find(cartId);
		List<CartItem> cartItems = new ArrayList<CartItem>();
		CartItem pCartItem = cartItemService.find(ids[0]);
		if (cart == null) {
			cart = pCartItem.getCart();
		}
		if (cart == null || cart.isEmpty()) {
			// 购物车已经空了
			return errorV(model, error("15126"));
		}
		for (Long id : ids) {
			CartItem cartItem = cartItemService.find(id);
			if (!cartItem.getCart().equals(cart)) {
				// 只允许选择相同客户的产品
				return errorV(model, error("15128"));
			}
			cartItems.add(cartItem);
		}

		Order order = orderService.build(cart.getStore(), cart, cartItems);
		model.addAttribute("order", order);
		model.addAttribute("cart", cart);
		model.addAttribute("cartItems", cartItems);
		Store store = cart.getStore();
		if (!store.getIsMainStore()) {
			model.addAttribute("store", store);
		}
		else {
			Member member = storeMemberService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberService
					.findNotDefaultByMember(member);
			if (storeMembers != null) {
				for (StoreMember storeMember : storeMembers) {
					store = storeMember.getStore();
					if (store.getType().equals(Store.Type.distributor)) {
						model.addAttribute("store", store);
						break;
					}
				}
			}
		}

		model.addAttribute("paymentMethods",
				paymentMethodBaseService.findAll());

		if (store != null && !store.getIsMainStore()) {
			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
					order.getOrganization() == null ? null
							: order.getOrganization().getId(),
							order.getSbu() == null ? null:order.getSbu().getId());
			model.addAttribute("balance", balance);
		}

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));

		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (OrderItem orderItem : order.getOrderItems()) {
			Product product = orderItem.getProduct();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("id", product.getId());
			data.put("vonder_code", product.getVonderCode());
			data.put("model", product.getModel());
			data.put("name", product.getName());
			data.put("price", orderItem.getPrice());
			data.put("origPrice", orderItem.getOrigPrice());
			data.put("quantity", orderItem.getQuantity());
			data.put("bar_code", product.getBarCode());

			Map<String, Object> map = priceApplyService
					.findItemByProduct(product.getId(), store.getId());
			if (map != null) {
				data.put("apply_sn", map.get("sn"));
				data.put("apply_price", map.get("price"));
				data.put("apply_item_id", map.get("id"));
				BigDecimal useable_quantity = new BigDecimal(
						map.get("quantity").toString())
								.subtract(new BigDecimal(
										map.get("used_quantity").toString()));
				data.put("apply_useable_quantity", useable_quantity);
			}

			if (store.getMemberRank() != null) {
				Map<String, Object> productPrice = productPriceHeadService
						.findPriceByProductStore(
								orderItem.getProductStore().getId(),
								store.getMemberRank().getId());
				if (productPrice != null) {
					data.put("member_price",
							new BigDecimal(productPrice
									.get("store_member_price").toString()));
				}
				else {
					data.put("member_price", product.getPrice());
				}
			}
			else {
				data.put("member_price", product.getPrice());
			}

			list.add(data);
		}
		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		return "/b2b/order_project/info";
	}

	/**
	 * 购物车生成订单创建
	 * 
	 * @param cartId
	 * @param ids
	 * @return
	 */
	@RequestMapping(value = "/create", method = RequestMethod.POST)
	public @ResponseBody ResultMsg create(Long storeId, Long cartId, Long[] ids,
			Long areaId, Long freightChargeTypeId, Order order,
			Long paymentMethodId, Long shippingMethodId, Long organizationId) {

		Store store = storeService.find(storeId);
		if (store == null) {
			// 请选择客户
			return error("15135");
		}
		Area area = areaService.find(areaId);
		if (area == null) {
			// 请选择收货地区
			return error("15136");
		}
		if (ConvertUtil.isEmpty(order.getAddress())) {
			// 请填写收货地址
			return error("15137");
		}
		if (ConvertUtil.isEmpty(order.getConsignee())) {
			// 请填写收货人
			return error("15138");
		}
		if (ConvertUtil.isEmpty(order.getPhone())) {
			// 请填写收货人电话
			return error("15139");
		}
		PaymentMethod paymentMethod = paymentMethodBaseService
				.find(paymentMethodId);
		if (paymentMethod == null) {
			// 请选择支付方式
			return error("151040");
		}
		ShippingMethod shippingMethod = shippingMethodBaseService
				.find(shippingMethodId);
		if (shippingMethod == null) {
			// 请选择配送方式
			return error("151041");
		}
		Cart cart = cartService.find(cartId);
		if (cart == null || cart.isEmpty()) {
			// 购物车已清空
			return error("15126");
		}
		List<CartItem> cartItems = new ArrayList<CartItem>();
		for (Long id : ids) {
			cartItems.add(cartItemService.find(id));
		}
		SystemDict freightChargeType = null;
		if (freightChargeTypeId != null) {
			freightChargeType = systemDictService.find(freightChargeTypeId);
		}
		orderService.create(store,
				cart,
				cartItems,
				area,
				order,
				paymentMethod,
				shippingMethod,
				freightChargeType,
				organizationId);

		return success();
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String addCode(@PathVariable String code, Long[] ids,
			ModelMap model) {

		model.addAttribute("code", code);
		Store store = null;
		if (ids != null && ids.length > 0) {
			CartItem pCartItem = cartItemService.find(ids[0]);
			Cart cart = pCartItem.getCart();
			List<CartItem> cartItems = new ArrayList<CartItem>();
			for (Long id : ids) {
				CartItem cartItem = cartItemService.find(id);
				if (!cartItem.getCart().equals(cart)) {
					// 只允许选择相同客户的产品
					return errorV(model, error("15128"));
				}
				cartItems.add(cartItem);
			}

			model.addAttribute("cartItems", cartItems);

			Order order = orderService.build(cart.getStore(), cart, cartItems);
			model.addAttribute("order", order);
			model.addAttribute("cart", cart);
			model.addAttribute("cartItems", cartItems);
			store = cart.getStore();
			if (!store.getIsMainStore()) {
				model.addAttribute("store", store);
			}
			else {
				Member member = storeMemberService.getCurrent().getMember();
				List<StoreMember> storeMembers = storeMemberService
						.findNotDefaultByMember(member);
				if (storeMembers != null) {
					for (StoreMember storeMember : storeMembers) {
						store = storeMember.getStore();
						if (store.getType().equals(Store.Type.distributor)) {
							model.addAttribute("store", store);
							break;
						}
						else {
							store = null;
						}
					}
				}
			}

			List<OrderItem> orderItems = order.getOrderItems();
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			for (int i = 0; i < orderItems.size(); i++) {
				OrderItem orderItem = orderItems.get(i);
				Product product = orderItem.getProduct();
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("id", product.getId());
				data.put("vonder_code", product.getVonderCode());
				data.put("model", product.getModel());
				data.put("name", product.getName());
				data.put("price", orderItem.getPrice());
				data.put("origPrice", orderItem.getOrigPrice());
				data.put("quantity", orderItem.getQuantity());
				data.put("bar_code", product.getBarCode());
				data.put("cartItemId", cartItems.get(i).getId());
				Map<String, Object> map = priceApplyService
						.findItemByProduct(product.getId(), store.getId());
				if (map != null) {
					data.put("apply_sn", map.get("sn"));
					data.put("apply_price", map.get("price"));
					data.put("apply_item_id", map.get("id"));
					BigDecimal useable_quantity = new BigDecimal(
							map.get("quantity").toString())
									.subtract(new BigDecimal(map
											.get("used_quantity").toString()));
					data.put("apply_useable_quantity", useable_quantity);
				}

				if (store.getMemberRank() != null) {
					Map<String, Object> productPrice = productPriceHeadService
							.findPriceByProductStore(
									orderItem.getProductStore().getId(),
									store.getMemberRank().getId());
					if (productPrice != null) {
						data.put("member_price",
								new BigDecimal(productPrice
										.get("store_member_price").toString()));
					}
					else {
						data.put("member_price", product.getPrice());
					}
				}
				else {
					data.put("member_price", product.getPrice());
				}

				list.add(data);
			}
			String jsonStr = JsonUtils.toJson(list);
			model.addAttribute("jsonStr", jsonStr);

		}
		else {
			Member member = storeMemberService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberService
					.findNotDefaultByMember(member);
			if (storeMembers != null) {
				for (StoreMember storeMember : storeMembers) {
					store = storeMember.getStore();
					if (store.getType().equals(Store.Type.distributor)) {
						model.addAttribute("store", store);
						break;
					}
					else {
						store = null;
					}
				}
			}
		}

		model.addAttribute("paymentMethods",
				paymentMethodBaseService.findAll());

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));
		model.addAttribute("companyId", WebUtils.getCurrentCompanyInfoId());

		if (store != null && !store.getIsMainStore()) {
			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
					null,
					null);
			model.addAttribute("balance", balance);
			filters.clear();
			filters.add(Filter.eq("store", store));
			Pageable pageable = new Pageable();
			Page<Map<String, Object>> storeAddressList = storeService
					.findStoreAddressPage(null,
							null,
							store.getId(),
							null,
							null,
							pageable);
			if (storeAddressList != null
					&& storeAddressList.getContent().size() > 0) {
				model.addAttribute("storeAddress",
						storeAddressList.getContent().get(0));
			}
		}
		if (store != null) {
			model.addAttribute("storeManagerList", store.getStoreManagers());
		}
		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);

		// 用户默认的所属机构
		// filters.clear();
		// filters.add(Filter.eq("storeMember",
		// storeMemberService.getCurrent()));
		// filters.add(Filter.eq("isDefault", true));
		// List<StoreMemberSaleOrg> storeMemberSaleOrgs =
		// storeMemberSaleOrgService.findList(null, filters, null);
		// SaleOrg saleOrg = null;
		// if (storeMemberSaleOrgs != null && storeMemberSaleOrgs.size() > 0) {
		// saleOrg = storeMemberSaleOrgs.get(0).getSaleOrg();
		// }
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService
					.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer
					.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
							WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig(
					"saveOrder2Unaudited", WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		return "/" + code + "/b2b/order_project/add";
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long[] ids, ModelMap model) {

		Store store = null;
		if (ids != null && ids.length > 0) {
			CartItem pCartItem = cartItemService.find(ids[0]);
			Cart cart = pCartItem.getCart();
			List<CartItem> cartItems = new ArrayList<CartItem>();
			for (Long id : ids) {
				CartItem cartItem = cartItemService.find(id);
				if (!cartItem.getCart().equals(cart)) {
					// 只允许选择相同客户的产品
					return errorV(model, error("15128"));
				}
				cartItems.add(cartItem);
			}

			model.addAttribute("cartItems", cartItems);

			Order order = orderService.build(cart.getStore(), cart, cartItems);
			model.addAttribute("order", order);
			model.addAttribute("cart", cart);
			model.addAttribute("cartItems", cartItems);
			store = cart.getStore();
			if (!store.getIsMainStore()) {
				model.addAttribute("store", store);
			}
			else {
				Member member = storeMemberService.getCurrent().getMember();
				List<StoreMember> storeMembers = storeMemberService
						.findNotDefaultByMember(member);
				if (storeMembers != null) {
					for (StoreMember storeMember : storeMembers) {
						store = storeMember.getStore();
						if (store.getType().equals(Store.Type.distributor)) {
							model.addAttribute("store", store);
							break;
						}
						else {
							store = null;
						}
					}
				}
			}

			List<OrderItem> orderItems = order.getOrderItems();
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			for (int i = 0; i < orderItems.size(); i++) {
				OrderItem orderItem = orderItems.get(i);
				Product product = orderItem.getProduct();
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("id", product.getId());
				data.put("vonder_code", product.getVonderCode());
				data.put("model", product.getModel());
				data.put("name", product.getName());
				data.put("price", orderItem.getPrice());
				data.put("origPrice", orderItem.getOrigPrice());
				data.put("quantity", orderItem.getQuantity());
				data.put("bar_code", product.getBarCode());
				data.put("cartItemId", cartItems.get(i).getId());
				Map<String, Object> map = priceApplyService
						.findItemByProduct(product.getId(), store.getId());
				if (map != null) {
					data.put("apply_sn", map.get("sn"));
					data.put("apply_price", map.get("price"));
					data.put("apply_item_id", map.get("id"));
					BigDecimal useable_quantity = new BigDecimal(
							map.get("quantity").toString())
									.subtract(new BigDecimal(map
											.get("used_quantity").toString()));
					data.put("apply_useable_quantity", useable_quantity);
				}

				if (store.getMemberRank() != null) {
					Map<String, Object> productPrice = productPriceHeadService
							.findPriceByProductStore(
									orderItem.getProductStore().getId(),
									store.getMemberRank().getId());
					if (productPrice != null) {
						data.put("member_price",
								new BigDecimal(productPrice
										.get("store_member_price").toString()));
					}
					else {
						data.put("member_price", product.getPrice());
					}
				}
				else {
					data.put("member_price", product.getPrice());
				}

				list.add(data);
			}
			String jsonStr = JsonUtils.toJson(list);
			model.addAttribute("jsonStr", jsonStr);

		}
		else {
			Member member = storeMemberService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberService
					.findNotDefaultByMember(member);
			if (storeMembers != null) {
				for (StoreMember storeMember : storeMembers) {
					store = storeMember.getStore();
					if (store.getType().equals(Store.Type.distributor)) {
						model.addAttribute("store", store);
						break;
					}
					else {
						store = null;
					}
				}
			}
		}

		model.addAttribute("paymentMethods",
				paymentMethodBaseService.findAll());

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));

		model.addAttribute("appProductChangePrice",
				Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
						WebUtils.getCurrentCompanyInfoId())));
		model.addAttribute("companyId", WebUtils.getCurrentCompanyInfoId());

		if (store != null && !store.getIsMainStore()) {
			BigDecimal balance = storeBalanceService.findBalance(store.getId(),
					null,
					null);
			model.addAttribute("balance", balance);
			filters.clear();
			filters.add(Filter.eq("store", store));
			Pageable pageable = new Pageable();
			Page<Map<String, Object>> storeAddressList = storeService
					.findStoreAddressPage(null,
							null,
							store.getId(),
							null,
							null,
							pageable);
			if (storeAddressList != null
					&& storeAddressList.getContent().size() > 0) {
				model.addAttribute("storeAddress",
						storeAddressList.getContent().get(0));
			}
		}
		if (store != null) {
			model.addAttribute("storeManagerList", store.getStoreManagers());
		}
		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);

		// 用户默认的所属机构
		// filters.clear();
		// filters.add(Filter.eq("storeMember",
		// storeMemberService.getCurrent()));
		// filters.add(Filter.eq("isDefault", true));
		// List<StoreMemberSaleOrg> storeMemberSaleOrgs =
		// storeMemberSaleOrgService.findList(null, filters, null);
		// SaleOrg saleOrg = null;
		// if (storeMemberSaleOrgs != null && storeMemberSaleOrgs.size() > 0) {
		// saleOrg = storeMemberSaleOrgs.get(0).getSaleOrg();
		// }
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService
					.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}

		//费用类型 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "CostType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> systemDicts = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("systemDicts", systemDicts);

		//工厂 -系统词汇
		filters.clear();
		filters.add(Filter.eq("code", "FactoryName"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> factorySystemDicts = systemDictBaseService
				.findList(null, filters, null);
		model.addAttribute("factorySystemDicts", factorySystemDicts);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		//订单分类
		List<Filter> filterss = new ArrayList<Filter>();
		filterss.add(Filter.eq("code", "OrderClassify"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> orderClassifys = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("orderClassifys", orderClassifys);

		//订单类型
		filterss.clear();
		filterss.add(Filter.eq("code", "OrderStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> orderStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("orderStyles", orderStyles);

		//贸易类型
		filterss.clear();
		filterss.add(Filter.eq("code", "TradeStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> tradeStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("tradeStyles", tradeStyles);

		//行业类别industryStyle
		filterss.clear();
		filterss.add(Filter.eq("code", "IndustryStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> industryStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("industryStyles", industryStyles);

		//销售体系
		filterss.clear();
		filterss.add(Filter.eq("code", "SaleStyle"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> saleStyles = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("saleStyles", saleStyles);

		//付款条件
		filterss.clear();
		filterss.add(Filter.eq("code", "PaymentConditions"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> paymentConditions = systemDictBaseService
				.findList(null, filterss, null);
		model.addAttribute("paymentConditions", paymentConditions);

		//币种
		filterss.clear();
		filterss.add(Filter.eq("code", "Currency"));
		filterss.add(Filter.eq("isEnabled", true));
		filterss.add(Filter.isNotNull("parent"));
		List<SystemDict> currencies = systemDictBaseService.findList(null,
				filterss,
				null);
		model.addAttribute("currencies", currencies);

		int undefinedProduct2Order = 0;
		try {
			undefinedProduct2Order = Integer
					.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
							WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig(
					"saveOrder2Unaudited", WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);

		return "/b2b/order_project/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(Order order, Long storeId,
			Long claimStoreMemberId, Long areaId, Long organizationId,
			Long salesmanId, Long currencyId, Long businessTypeId,
			Long freightChargeTypeId, Long saleOrgId, Long paymentMethodId,
			Long shippingMethodId, Long cartId, Long[] ids, Integer stat,
			String[] creditRechargeSn,
			BigDecimal[] creditRechargeDistributionAmount,
			String[] depositRechargeSn,
			BigDecimal[] depositRechargeDistributionAmount,
			String[] policyCountNo, BigDecimal[] policyCountDistributionAmount,
			Long orderClassifyId, Long orderStyleId, Long tradeStyleId,
			Long industryStyleId, Long saleStyleId, Long paymentConditionId) {

		List<OrderItem> orderItems = order.getOrderItems();
		Store store = storeService.find(storeId);
		if (store == null) {
			// 请选择客户
			return error("15135");
		}
		StoreMember saleman = storeMemberService.find(claimStoreMemberId);

		Area area = areaService.find(areaId);
		if (area == null) {
			// 请选择收货地区
			return error("15136");
		}
		if (ConvertUtil.isEmpty(order.getAddress())) {
			// 请填写收货地址
			return error("15137");
		}
		if (ConvertUtil.isEmpty(order.getConsignee())) {
			// 请填写收货人
			return error("15138");
		}
		if (ConvertUtil.isEmpty(order.getPhone())) {
			// 请填写收货人电话
			return error("15139");
		}
//		PaymentMethod paymentMethod = paymentMethodBaseService.find(paymentMethodId);
//		if (paymentMethod == null) {
//			// 请选择支付方式
//			return error("151040");
//		}
//		ShippingMethod shippingMethod = shippingMethodBaseService.find(shippingMethodId);
//		if (shippingMethod == null) {
//			// 请选择配送方式
//			return error("151041");
//		}
		if (freightChargeTypeId != null) {
			SystemDict freightChargeType = systemDictService
					.find(freightChargeTypeId);
			order.setFreightChargeType(freightChargeType);
		}
		if (businessTypeId != null) {
			SystemDict businessType = systemDictService.find(businessTypeId);
			order.setBusinessType(businessType);
		}
		SystemDict currency = systemDictService.find(currencyId);
		order.setBusinessType(currency);

		Cart cart = cartService.find(cartId);
		if (cart != null && cart.isEmpty()) {
			// 购物车已清空
			return error("15126");
		}
		List<CartItem> cartItems = new ArrayList<CartItem>();
		if (ids != null) {
			for (Long id : ids) {
				cartItems.add(cartItemService.find(id));
			}
		}
		if (salesmanId != null) {
			order.setSalesman(storeMemberService.find(salesmanId));
		}
		order.setSaleOrg(saleOrgService.find(saleOrgId));
		Organization organization = organizationService.find(organizationId);
		order.setOrganization(organization);
		orderProjectService.saveProject(order,
				store,
				saleman,
				area,
				cart,
				cartItems,
				stat,
				creditRechargeSn,
				creditRechargeDistributionAmount,
				depositRechargeSn,
				depositRechargeDistributionAmount,
				policyCountNo,
				policyCountDistributionAmount,
				orderClassifyId,
				orderStyleId,
				tradeStyleId,
				industryStyleId,
				saleStyleId,
				paymentConditionId);
		return success().addObjX(order.getId());

	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(Order order, Long shippingMethodId,
			Long areaId, Long freightChargeTypeId, Long businessTypeId,
			Long currencyId, Long[] delOrderItemId, Integer stat,
			Long organizationId, Long[] creditRechargeContractId,
			String[] creditRechargeSn,
			BigDecimal[] creditRechargeDistributionAmount,
			Long[] depositRechargeContractId, String[] depositRechargeSn,
			BigDecimal[] depositRechargeDistributionAmount,
			Long[] policyCountContractId, String[] policyCountNo,
			BigDecimal[] policyCountDistributionAmount, Long paymentConditionId,
			Long distributorId, Long claimStoreMemberId) {
		Store distributor = storeService.find(distributorId);
		Area area = areaService.find(areaId);
		if (area == null) {
			// 请选择收货地区
			return error("15136");
		}
		if (ConvertUtil.isEmpty(order.getAddress())) {
			// 请填写收货地址
			return error("15137");
		}
		if (ConvertUtil.isEmpty(order.getConsignee())) {
			// 请填写收货人
			return error("15138");
		}
		if (ConvertUtil.isEmpty(order.getPhone())) {
			// 请填写收货人电话
			return error("15139");
		}
		ShippingMethod shippingMethod = shippingMethodBaseService
				.find(shippingMethodId);
		SystemDict freightChargeType = null;
		if (freightChargeTypeId != null) {
			freightChargeType = systemDictService.find(freightChargeTypeId);
		}
		SystemDict businessType = null;
		if (businessTypeId != null) {
			businessType = systemDictService.find(businessTypeId);
		}
		SystemDict currency = systemDictService.find(currencyId);
		order.setCurrency(currency);
		StoreMember salesman = storeMemberService.find(claimStoreMemberId);
		order.setSalesman(salesman);
		orderProjectService.updateProject(order,
				distributor,
				shippingMethod,
				area,
				delOrderItemId,
				freightChargeType,
				businessType,
				stat,
				organizationId,
				creditRechargeContractId,
				creditRechargeSn,
				creditRechargeDistributionAmount,
				depositRechargeContractId,
				depositRechargeSn,
				depositRechargeDistributionAmount,
				policyCountContractId,
				policyCountNo,
				policyCountDistributionAmount,
				paymentConditionId);

		return success();
	}

	@RequestMapping(value = "/setFlag", method = RequestMethod.POST)
	public @ResponseBody ResultMsg setFlag(Long[] ids, Integer flag) {

		if (ids == null || ids.length == 0) {
			// 请选择订单
			return error("15140");
		}
		if (flag == null) {
			// 请选择旗标
			return error("15250");
		}
		orderService.setFlag(ids, flag);

		return success();
	}

	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancel(Long[] ids) {

		if (ids == null || ids.length == 0) {
			// 请选择订单
			return error("15140");
		}
		for (int i = 0; i < ids.length; i++) {
			Order order = orderService.find(ids[i]);
			if (order.getOrderStatus() != OrderStatus.unaudited) {
				return error("订单【" + order.getSn() + "】不是未审核状态，无法作废");
			}
		}
		orderService.cancelOrder(ids);

		return success();
	}

	/**
	 * 清除仓库
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/clear_warehouse", method = RequestMethod.POST)
	public @ResponseBody ResultMsg clearWarehouse(Long id) {

		if (id == null) {
			// 请选择订单
			return error("15140");
		}

		OrderItem orderItem = orderItemService.find(id);
		Order order = orderItem.getOrder();
		if (!order.getOrderStatus().equals(OrderStatus.unaudited)) {
			// 只允许未审核状态的订单对明细进行清除仓库
			return error("151043");
		}
		orderService.clearWarehouse(id);

		return success();
	}

	@RequestMapping(value = "/get_price", method = RequestMethod.POST)
	public @ResponseBody ResultMsg getPrice(Long[] productId, Long storeId,
			ModelMap model) {

		Map<Long, Map<String, Object>> map = new HashMap<Long, Map<String, Object>>();
		Store store = storeService.find(storeId);
		Store mainStore = storeService.getMainStore();
		Long memberRankId = store.getMemberRank().getId();
		for (Long id : productId) {
			Product product = productService.find(id);
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("product", product));
			filters.add(Filter.eq("store", mainStore));
			ProductStore productStore = productStoreService.find(filters);
			Map<String, Object> item = new HashMap<String, Object>();
			Map<String, Object> productPrice = productPriceHeadService
					.findPriceByProductStore(productStore.getId(),
							memberRankId);
			if (productPrice != null) {
				item.put("member_price",
						new BigDecimal(productPrice.get("store_member_price")
								.toString()));
			}
			else {
				item.put("member_price", product.getPrice());
			}
			Map<String, Object> itemMap = priceApplyService
					.findItemByProduct(id, storeId);
			if (itemMap != null) {
				item.put("apply_sn", itemMap.get("sn"));
				item.put("apply_price", itemMap.get("price"));
				item.put("apply_item_id", itemMap.get("id"));
				BigDecimal useable_quantity = new BigDecimal(
						itemMap.get("quantity").toString())
								.subtract(new BigDecimal(itemMap
										.get("used_quantity").toString()));
				item.put("apply_useable_quantity", useable_quantity);
			}
			map.put(id, item);
		}
		return success().addObjX(map);
	}

	/**
	 * Excel导入
	 * 
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg importFromExcel(MultipartFile file,
			HttpServletResponse response, ModelMap model) throws Exception {

		orderService.orderImport(file);
		return ResultMsg.success();
	}

	/**
	 * 订单导入
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/order_import", method = RequestMethod.POST, produces = "text/html; charset=UTF-8")
	public String order_import(MultipartFile file, HttpServletResponse response,
			ModelMap model) throws Exception {

		List<Map<String, Object>> orders = orderService.orderImportData(file);
		String jsonStr = JsonUtils.toJson(orders);
		model.addAttribute("orders", jsonStr);

		return "/b2b/order_project/order_import";
	}

	@RequestMapping(value = "/orderImportByCache", method = RequestMethod.POST)
	public @ResponseBody ResultMsg orderImportByCache(String uuid)
			throws Exception {

		orderService.orderImportByCache(uuid);
		return ResultMsg.success();
	}

	private List<Map<String, Object>> sort(List<Map<String, Object>> items,
			Long parent) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if (items != null) {
			for (Map<String, Object> map : items) {
				if ((map.get("parent") != null
						&& parent != null
						&& Long.parseLong(
								map.get("parent").toString()) == parent
										.longValue())
						|| (map.get("parent") == null && parent == null)) {
					result.add(map);
					result.addAll(sort(items,
							Long.parseLong(map.get("id").toString())));
				}
			}
		}
		return result;
	}

	@RequestMapping(value = "/confirm", method = RequestMethod.POST)
	public @ResponseBody ResultMsg confirm(Long[] ids) throws Exception {
		orderService.confirm(ids);
		return success();
	}

	@RequestMapping(value = "/message_board", method = RequestMethod.GET)
	public String message_board(Long id, ModelMap model) throws Exception {
		Order order = orderService.find(id);
		model.addAttribute("order", order);
		if (order != null) {
			List<Filter> filters = new ArrayList<Filter>();
			List<net.shopxx.base.core.Order> orders = new ArrayList<net.shopxx.base.core.Order>();
			filters.add(Filter.eq("orderSn", order.getSn()));
			orders.add(net.shopxx.base.core.Order.asc("createDate"));
			List<MessageBoard> messageBoards = messageBoardService
					.findList(null, filters, orders);
			model.addAttribute("messageBoards", messageBoards);
		}
		model.addAttribute("storeMember", storeMemberService.getCurrent());
		return "/b2b/order_project/message_board";
	}

	@RequestMapping(value = "/message_board_content", method = RequestMethod.GET)
	public String message_board_content(Long id, ModelMap model)
			throws Exception {
		Order order = orderService.find(id);
		model.addAttribute("order", order);
		if (order != null) {
			List<Filter> filters = new ArrayList<Filter>();
			List<net.shopxx.base.core.Order> orders = new ArrayList<net.shopxx.base.core.Order>();
			filters.add(Filter.eq("orderSn", order.getSn()));
			orders.add(net.shopxx.base.core.Order.asc("createDate"));
			List<MessageBoard> messageBoards = messageBoardService
					.findList(null, filters, orders);
			model.addAttribute("messageBoards", messageBoards);
		}

		return "/b2b/order_project/message_board_content";
	}

	@RequestMapping(value = "/save_message_board", method = RequestMethod.GET)
	public @ResponseBody ResultMsg save_message_board(Long id, String content)
			throws Exception {
		Order order = orderService.find(id);
		if (order == null) return this.error("订单不存在");
		MessageBoard messageBoard = new MessageBoard();
		messageBoard.setType(1);
		messageBoard.setOrderSn(order.getSn());
		messageBoard.setContent(content);
		messageBoard.setStore(order.getStore());
		messageBoard.setStoreMember(storeMemberService.getCurrent());
		messageBoardService.save(messageBoard);
		return this.success();
	}

	/** 零微 */
	// @RequestMapping(value = "/sendOrderToBMS", method = RequestMethod.GET)
	// public @ResponseBody String sendOrderToBMS(ModelMap model) {
	// List<Filter> filters = new ArrayList<Filter>();
	// filters.add(Filter.eq("type", "0"));
	// filters.add(Filter.eq("companytoken",
	// "ee71750c-580b-4c44-9131-7595a1754797"));
	// List<IntfOrderTableTo> intfOrderTableTos = intfOrderTableToService
	// .findList(100, filters, null);
	// int success = 0;
	// int fail = 0;
	// for (IntfOrderTableTo intfOrderTableTo : intfOrderTableTos) {
	// try {
	// intfOrderTableToService.sendOrderToBMS(intfOrderTableTo);
	// success++;
	// }
	// catch (Exception e) {
	// fail++;
	// LogUtils.error("同步订单数据到BMS", e);
	// }
	// }
	// return "成功：" + success + ";失败：" + fail;
	//
	// }
	/** 美博 */
	// @RequestMapping(value = "/sendOrderToErp", method = RequestMethod.GET)
	// public @ResponseBody String sendOrderToErp(ModelMap model) {
	// String companytoken = "0ac39180-d5ea-43e8-a153-2af5cfe74e1d";//同望
	// //String companytoken = "81027c8e-b059-4d27-9ffc-4e909439bb40";//美博
	// List<Filter> filters = new ArrayList<Filter>();
	// filters.add(Filter.eq("type", "0"));
	// filters.add(Filter.eq("companytoken", companytoken));
	// List<IntfOrderTableTo> intfOrderTableTos = intfOrderTableToService
	// .findList(100, filters, null);
	// int success = 0;
	// int fail = 0;
	// for (IntfOrderTableTo intfOrderTableTo : intfOrderTableTos) {
	// try {
	// intfOrderTableToService.pushOrderToErp(intfOrderTableTo);
	// success++;
	// }
	// catch (Exception e) {
	// fail++;
	// LogUtils.error("同步订单数据到BMS", e);
	// }
	// }
	// return "成功：" + success + ";失败：" + fail;
	//
	// }
	// @RequestMapping(value = "/saveIntfOrderForErp", method =
	// RequestMethod.POST)
	// public @ResponseBody ResultMsg saveIntfOrderForErp(Long ids)
	// throws Exception {
	// Order order = orderService.find(ids);
	// if (order.getCompanyInfoId() != 1) return error("请选择一张测试单");
	// intfOrderTableToService.saveIntfOrderForErp(order);
	// return success();
	// }

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(
			String orderSn, String outTradeNo, Integer[] orderStatus,
			Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			Long saleOrgId, Long organizationId, Pageable pageable,
			ModelMap model) {

		Integer size = orderService.count(orderSn,
				outTradeNo,
				orderStatus,
				null,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				null,
				saleOrgId,
				organizationId,
				pageable,
				null,
				null,
				null,
				null,
				null,
				null,
				null);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @param productCategoryId
	 * @param sn
	 * @param vonderCode
	 * @param mod
	 * @param name
	 * @param startTime
	 * @param endTime
	 * @param isMarketable
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			Long saleOrgId, Long organizationId, Pageable pageable,
			ModelMap model, Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = orderService.findItemList(orderSn,
				outTradeNo,
				orderStatus,
				null,
				shippingStatus,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				null,
				null,
				saleOrgId,
				organizationId,
				page,
				size,
				null,
				null,
				null,
				null,
				null);
		return getModelAndView(data, model);

	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = orderService.findItemList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null);
		return getModelAndView(data, model);
	}

	/** 订单列表的导出 */
	public ModelAndView getModelAndView(List<Map<String, Object>> data,
			ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("order_status") != null) {
				Integer order_status = (Integer) str.get("order_status");
				if (order_status == 0) {
					str.put("order_status_name", "未确认");
				}
				else if (order_status == 1) {
					str.put("order_status_name", "已确认");
				}
				else if (order_status == 2) {
					str.put("order_status_name", "已完成");
				}
				else if (order_status == 3) {
					str.put("order_status_name", "已作废");
				}
				else if (order_status == 4) {
					str.put("order_status_name", "已删除");
				}
				else if (order_status == 5) {
					str.put("order_status_name", "未审核");
				}
				else if (order_status == 6) {
					str.put("order_status_name", "已审核");
				}
				else if (order_status == 7) {
					str.put("order_status_name", "已保存");
				}
				else if (order_status == 8) {
					str.put("order_status_name", "已接受");
				}
			}
			if (str.get("shipping_status") != null) {
				Integer shipping_status = (Integer) str.get("shipping_status");
				if (shipping_status == 0) {
					str.put("shipping_status_name", "未发货");
				}
				else if (shipping_status == 1) {
					str.put("shipping_status_name", "部分发货");
				}
				else if (shipping_status == 2) {
					str.put("shipping_status_name", "已发货");
				}
				else if (shipping_status == 3) {
					str.put("shipping_status_name", "确认收货");
				}
			}
			if (str.get("payment_status") != null) {
				Integer payment_status = (Integer) str.get("payment_status");
				if (payment_status == 0) {
					str.put("payment_status_name", "未支付");
				}
				else if (payment_status == 1) {
					str.put("payment_status_name", "部分支付");
				}
				else if (payment_status == 2) {
					str.put("payment_status_name", "完全支付");
				}
			}
			if (str.get("order_id") != null) {
				Long orderId = Long.parseLong(str.get("order_id").toString());
				Order order = orderService.find(orderId);
				BigDecimal orderAmount = BigDecimal.ZERO;
				if (order != null) {
					orderAmount = order.getAmount().setScale(2,
							BigDecimal.ROUND_HALF_UP);
				}
				str.put("orderAmount", orderAmount);
			}
			if (str.get("order_date") != null) {

				String order_date = DateUtil
						.convert((Date) str.get("order_date"));
				if (order_date.length() > 10) {
					order_date = order_date.substring(0, 10);
				}
				str.put("orderDate", order_date);
			}
			if (str.get("order_create_date") != null) {
				// String order_create_date = (String)
				// str.get("order_create_date");
				String order_create_date = DateUtil
						.convert((Date) str.get("order_create_date"));
				if (order_create_date.length() > 19) {
					order_create_date = order_create_date.substring(0, 19);
				}
				str.put("orderCreateDate", order_create_date);
			}
			if (str.get("order_check_date") != null) {
				String order_check_date = DateUtil
						.convert((Date) str.get("order_check_date"));
				if (order_check_date.length() > 19) {
					order_check_date = order_check_date.substring(0, 19);
				}
				str.put("orderCheckDate", order_check_date);
			}
			if (str.get("price") != null) {
				BigDecimal price = new BigDecimal(str.get("price").toString());
				str.put("price", price.setScale(2, BigDecimal.ROUND_HALF_UP));
			}

			if (str.get("price") != null && str.get("quantity") != null) {
				BigDecimal price = new BigDecimal(str.get("price").toString());
				BigDecimal quantity = new BigDecimal(
						str.get("quantity").toString());

				str.put("count",
						price.multiply(quantity).setScale(2,
								BigDecimal.ROUND_HALF_UP));
			}
			else {
				str.put("count", "0.00");
			}
			if (str.get("quantity") != null) {
				BigDecimal quantity = new BigDecimal(
						str.get("quantity").toString());
				str.put("quantity",
						NumberFormat.getInstance().format(quantity));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "订单编号",
				"来源单号",
				"客户名称",
				"客户其他名称",
				"客户编码",
				"客户余额",
				"订单金额",
				"产品名称",
				"产品型号",
				"厂商编号",
				"发货仓",
				"数量",
				"单价",
				"小计",
				"交货期",
				"收货人",
				"收货人电话",
				"收货地区",
				"收货地址",
				"下单时间",
				"下单人",
				"审核人",
				"审核时间",
				"机构",
				"仓库",
				"物流快递",
				"订单状态",
				"配送状态",
				"支付状态",
				"附言",
				"创建时间" };
		// 设置单元格取值
		String[] properties = { "order_sn",
				"out_no",
				"store_name",
				"store_alias",
				"store_sn",
				"balance",
				"orderAmount",
				"name",
				"model",
				"vonder_code",
				"warehouse_name",
				"quantity",
				"price",
				"count",
				"delivery_time",
				"consignee",
				"phone",
				"area_full_name",
				"address",
				"orderDate",
				"store_member_name",
				"check_store_member_name",
				"orderCheckDate",
				"sale_org_name",
				"order_warehouse_name",
				"delivery_corp_name",
				"order_status_name",
				"shipping_status_name",
				"payment_status_name",
				"order_memo",
				"orderCreateDate" };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, null, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	/**
	 * 模拟支付
	 */
	@RequestMapping(value = "/submit", method = RequestMethod.POST)
	public String submit(HttpServletRequest request, BigDecimal payamount,
			String payname, HttpServletResponse response, ModelMap model) {

		model.addAttribute("requestUrl", "https://mapi.alipay.com/gateway.do");
		model.addAttribute("requestMethod", "get");
		model.addAttribute("requestCharset", "UTF-8");
		Map<String, Object> data = new HashMap<String, Object>();
		data = getParameterMap(
				new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()),
				request.getParameter("payname"),
				request);
		model.addAttribute("parameterMap", data);
		return "/member/store_recharge/submit";

	}

	public Map<String, Object> getParameterMap(String sn, String description,
			HttpServletRequest request) {

		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("service", "create_direct_pay_by_user");
		parameterMap.put("partner", "2088211535838965");// 支付宝商户号
		parameterMap.put("_input_charset", "utf-8");
		parameterMap.put("sign_type", "MD5");
		parameterMap.put("return_url", "http://mk.5mall.com");// 支付成功同步回调地址
		parameterMap.put("notify_url", "http://mk.5mall.com");// 支付成功异步回调地址
		parameterMap.put("out_trade_no", sn);// 支付单号
		parameterMap
				.put("subject",
						StringUtils.abbreviate(
								description.replaceAll(
										"[^0-9a-zA-Z\\u4e00-\\u9fa5 ]", ""),
								60));
		parameterMap
				.put("body",
						StringUtils.abbreviate(
								description.replaceAll(
										"[^0-9a-zA-Z\\u4e00-\\u9fa5 ]", ""),
								600));
		parameterMap.put("payment_type", "1");
		parameterMap.put("seller_id", "2088211535838965");// 支付宝商户号
		parameterMap.put("total_fee", request.getParameter("payamount"));// 支付金额
		parameterMap.put("show_url", "http://mk.5mall.com");// 网站地址
		parameterMap.put("paymethod", "directPay");
		parameterMap.put("exter_invoke_ip", request.getLocalAddr());// 请求ip
		parameterMap.put("extra_common_param", "twkj remark");// 备注

		try {
			parameterMap.put("sign", generateSign(parameterMap));
		}
		catch (Exception e) {
			System.out.print("---11----" + e.getMessage());
		}

		return parameterMap;
	}

	private String generateSign(Map<String, ?> parameterMap) {

		return DigestUtils
				.md5Hex(joinKeyValue(new TreeMap<String, Object>(parameterMap),
						null,
						"********************************",
						"&",
						true,
						"sign_type",
						"sign"));
	}

	protected String joinKeyValue(Map<String, Object> map, String prefix,
			String suffix, String separator, boolean ignoreEmptyValue,
			String... ignoreKeys) {
		List<String> list = new ArrayList<String>();
		if (map != null) {
			for (Entry<String, Object> entry : map.entrySet()) {
				String key = entry.getKey();
				String value = ConvertUtils.convert(entry.getValue());
				if (StringUtils.isNotEmpty(key)
						&& !ArrayUtils.contains(ignoreKeys, key)
						&& (!ignoreEmptyValue
								|| StringUtils.isNotEmpty(value))) {
					list.add(key + "=" + (value != null ? value : ""));
				}
			}
		}
		return (prefix != null ? prefix : "")
				+ StringUtils.join(list, separator)
				+ (suffix != null ? suffix : "");
	}

	@RequestMapping(value = "/rejectOrder", method = RequestMethod.POST)
	public @ResponseBody ResultMsg rejectOrder(Long orderId) {

		if (orderId == null) {
			// 请选择订单
			return error("15140");
		}
		Order order = orderService.find(orderId);
		for (OrderItem orderItem : order.getOrderItems()) {
			List<Map<String, Object>> shippingItemList = shippingService
					.findShippingItemListByOrderItemId(
							orderItem.getId().toString());
			if (shippingItemList != null && shippingItemList.size() > 0) {
				return error("订单已存在发货单，不允许驳回");
			}
			if (orderItem.getShipPlanQuantity() != null
					&& orderItem.getShipPlanQuantity()
							.compareTo(BigDecimal.ZERO) == 1) {

				// 订单已存在发货单，不允许驳回
				return error("15140");
			}
		}

		orderService.rejectOrder(order);

		return success();
	}

	@RequestMapping(value = "/refushPdf", method = RequestMethod.GET)
	public @ResponseBody ResultMsg refushPdf(Long id) {
		Order order = orderService.find(id);
		orderService.refushPdf(order);
		return success();

	}

	@RequestMapping(value = "/shipping2order", method = RequestMethod.GET)
	public @ResponseBody String shipping2order(Long[] ids) {

		int i = 0;
		List<Filter> filters = new ArrayList<Filter>();
		if (ids != null && ids.length > 0) {
			for (Long id : ids) {
				Shipping shipping = shippingService.find(id);
				Store supplier = shipping.getSupplier();
				Order order = new Order();
				List<OrderItem> orderItems = new ArrayList<OrderItem>();
				List<ShippingItem> shippingItems = shipping.getShippingItems();
				for (ShippingItem shippingItem : shippingItems) {
					OrderItem orderItem = new OrderItem();
					Product product = shippingItem.getProduct();
					OrderItem sourceOrderItem = shippingItem.getOrderItem();
					// 复制到新订单项
					BeanUtils.copyProperties(sourceOrderItem,
							orderItem,
							new String[] { "id",
									"createDate",
									"modifyDate",
									"order",
									"orderItemPartses",
									"orderItemAttachs" });
					orderItem.setSourceOrderItemId(sourceOrderItem.getId());
					orderItem.setQuantity(shippingItem.getQuantity());
					orderItem.setShipPlanQuantity(shippingItem.getQuantity());
					orderItem.setShippedQuantity(
							shippingItem.getShippedQuantity());
					orderItem.setOrder(order);
					orderItem.setWarehouse(shipping.getWarehouse());
					orderItem.setOutTradeId(shippingItem.getId().toString());

					Map<String, Object> priceMap = contractPriceService
							.findItemByProduct(product.getId(),
									supplier.getId());
					if (priceMap != null) {
						BigDecimal price = new BigDecimal(
								priceMap.get("price").toString());
						orderItem.setPrice(price);
					}
					else {
						orderItem.setMemo("无合约价");
					}

					orderItems.add(orderItem);

					sourceOrderItem.setIsPurchase(1);
					orderItemService.update(sourceOrderItem);
				}
				order.setOrderItems(orderItems);
				BigDecimal zero = BigDecimal.ZERO;
				order.setArea(shipping.getArea());
				order.setAddress(shipping.getAddress());
				order.setConsignee(shipping.getConsignee());
				order.setCouponDiscount(zero);
				order.setFreight(zero);
				order.setOffsetAmount(zero);
				order.setOrderStatus(OrderStatus.audited);
				filters.clear();
				filters.add(Filter.eq("method", PaymentMethod.Method.offline));
				List<PaymentMethod> paymentMethods = paymentMethodBaseService
						.findList(1, filters, null);
				if (!paymentMethods.isEmpty()) {
					PaymentMethod paymentMethod = paymentMethods.get(0);
					order.setPaymentMethod(paymentMethod);
					order.setPaymentMethodName(paymentMethod.getName());
				}
				else {
					order.setPaymentMethodName("线下支付");
				}
				filters.clear();
				filters.add(Filter.eq("name", shipping.getShippingMethod()));
				List<ShippingMethod> shippingMethods = shippingMethodBaseService
						.findList(1, filters, null);
				if (!shippingMethods.isEmpty()) {
					ShippingMethod shippingMethod = shippingMethods.get(0);
					order.setShippingMethod(shippingMethod);
					order.setShippingMethodName(shippingMethod.getName());
				}
				else {
					order.setShippingMethodName("快递");
				}
				order.setPaymentStatus(PaymentStatus.paid);
				if (shipping.getStatus() < 3) {
					order.setShippingStatus(ShippingStatus.unshipped);
				}
				else if (shipping.getStatus() == 3) {
					order.setShippingStatus(ShippingStatus.partialShipment);
				}
				else {
					order.setShippingStatus(ShippingStatus.shipped);
				}
				order.setPhone(shipping.getPhone());
				order.setPromotionDiscount(zero);
				order.setZipCode(shipping.getZipCode());
				order.setStoreMember(storeMemberService.find(14781L));
				order.setStore(shipping.getStore());
				order.setSupplier(shipping.getSupplier());
				order.setType("6");
				order.setOrderType(4);
				order.setIsMobile(false);
				String sn = SnUtil.getOrderSn();
				order.setSn(sn);
				order.setOutTradeNo(shipping.getSn());
				order.setCreateDate(shipping.getCreateDate());
				order.setOrderDate(shipping.getCreateDate());
				order.setAmountPaid(order.getAmount());

				orderService.save(order);

				for (OrderItem orderItem : order.getOrderItems()) {
					Long shippingItemId = Long
							.parseLong(orderItem.getOutTradeId());
					ShippingItem shippingItem = shippingItemService
							.find(shippingItemId);
					shippingItem.setPurOrderItem(orderItem);
					shippingItem.setPurOrderSn(order.getSn());
					shippingItem.setOrderType(4);
					shippingItemService.update(shippingItem);
				}
				shipping.setOrderType(4);
				if (shipping.getStatus() == 1) {
					shipping.setStatus(0);
				}
				shippingService.update(shipping);
				i++;
			}
		}
		else {
			filters.clear();
			filters.add(Filter.ne("status", 2));
			filters.add(Filter.isNotNull("supplier"));
			filters.add(Filter.ne("orderType", 4));
			List<Shipping> shippings = shippingService.findList(null,
					filters,
					null);
			for (Shipping shipping : shippings) {

				Order order = new Order();
				Store supplier = shipping.getSupplier();
				List<OrderItem> orderItems = new ArrayList<OrderItem>();
				List<ShippingItem> shippingItems = shipping.getShippingItems();
				for (ShippingItem shippingItem : shippingItems) {
					OrderItem orderItem = new OrderItem();
					Product product = shippingItem.getProduct();
					OrderItem sourceOrderItem = shippingItem.getOrderItem();
					// 复制到新订单项
					BeanUtils.copyProperties(sourceOrderItem,
							orderItem,
							new String[] { "id",
									"createDate",
									"modifyDate",
									"order",
									"orderItemPartses",
									"orderItemAttachs" });
					orderItem.setSourceOrderItemId(sourceOrderItem.getId());
					orderItem.setQuantity(shippingItem.getQuantity());
					orderItem.setShipPlanQuantity(shippingItem.getQuantity());
					orderItem.setShippedQuantity(
							shippingItem.getShippedQuantity());
					orderItem.setOrder(order);
					orderItem.setWarehouse(shipping.getWarehouse());
					orderItem.setOutTradeId(shippingItem.getId().toString());

					Map<String, Object> priceMap = contractPriceService
							.findItemByProduct(product.getId(),
									supplier.getId());
					if (priceMap != null) {
						BigDecimal price = new BigDecimal(
								priceMap.get("price").toString());
						orderItem.setPrice(price);
					}
					else {
						orderItem.setMemo("无合约价");
					}

					orderItems.add(orderItem);

					sourceOrderItem.setIsPurchase(1);
					orderItemService.update(sourceOrderItem);
				}
				order.setOrderItems(orderItems);
				BigDecimal zero = BigDecimal.ZERO;
				order.setArea(shipping.getArea());
				order.setAddress(shipping.getAddress());
				order.setConsignee(shipping.getConsignee());
				order.setCouponDiscount(zero);
				order.setFreight(zero);
				order.setOffsetAmount(zero);
				order.setOrderStatus(OrderStatus.audited);
				filters.clear();
				filters.add(Filter.eq("method", PaymentMethod.Method.offline));
				List<PaymentMethod> paymentMethods = paymentMethodBaseService
						.findList(1, filters, null);
				if (!paymentMethods.isEmpty()) {
					PaymentMethod paymentMethod = paymentMethods.get(0);
					order.setPaymentMethod(paymentMethod);
					order.setPaymentMethodName(paymentMethod.getName());
				}
				else {
					order.setPaymentMethodName("线下支付");
				}
				filters.clear();
				filters.add(Filter.eq("name", shipping.getShippingMethod()));
				List<ShippingMethod> shippingMethods = shippingMethodBaseService
						.findList(1, filters, null);
				if (!shippingMethods.isEmpty()) {
					ShippingMethod shippingMethod = shippingMethods.get(0);
					order.setShippingMethod(shippingMethod);
					order.setShippingMethodName(shippingMethod.getName());
				}
				else {
					order.setShippingMethodName("快递");
				}
				order.setPaymentStatus(PaymentStatus.paid);
				if (shipping.getStatus() < 3) {
					order.setShippingStatus(ShippingStatus.unshipped);
				}
				else if (shipping.getStatus() == 3) {
					order.setShippingStatus(ShippingStatus.partialShipment);
				}
				else {
					order.setShippingStatus(ShippingStatus.shipped);
				}
				order.setPhone(shipping.getPhone());
				order.setPromotionDiscount(zero);
				order.setZipCode(shipping.getZipCode());
				order.setStoreMember(storeMemberService.find(14781L));
				order.setStore(shipping.getStore());
				order.setSupplier(shipping.getSupplier());
				order.setType("6");
				order.setOrderType(4);
				order.setIsMobile(false);
				String sn = SnUtil.getOrderSn();
				order.setSn(sn);
				order.setOutTradeNo(shipping.getSn());
				order.setCreateDate(shipping.getCreateDate());
				order.setOrderDate(shipping.getCreateDate());
				order.setAmountPaid(order.getAmount());

				orderService.save(order);

				for (OrderItem orderItem : order.getOrderItems()) {
					Long shippingItemId = Long
							.parseLong(orderItem.getOutTradeId());
					ShippingItem shippingItem = shippingItemService
							.find(shippingItemId);
					shippingItem.setPurOrderItem(orderItem);
					shippingItem.setPurOrderSn(order.getSn());
					shippingItem.setOrderType(4);
					shippingItemService.update(shippingItem);
				}
				shipping.setOrderType(4);
				if (shipping.getStatus() == 1) {
					shipping.setStatus(0);
				}
				shippingService.update(shipping);
				i++;
			}
		}
		return "success:" + i;

	}

	/**
	 * 查看pdf
	 * 
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkPdf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkPdf(Long id) throws Exception {
		Order order = orderService.find(id);
		if (order == null) return error("订单不存在！");
		if (!order.getOrderStatus().equals(Order.OrderStatus.audited)) {
			return error("订单未审核或已作废");
		}
		TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
		if (triplicateForm == null) {
			triplicateForm = orderService.createTriplicateForm(order);
		}
		return success(triplicateForm.getUrl());

	}

	/**
	 * 查询客户相关客户经理数据
	 */
	@RequestMapping(value = "/select_manager", method = RequestMethod.POST)
	public @ResponseBody ResultMsg select_accountManager_data(long storeId,
			ModelMap model) {

		Store store = storeService.find(storeId);
		List<StoreManager> storeManagers = null;
		if (store != null) {

			storeManagers = store.getStoreManagers();
		}
		return this.success().addObjX(storeManagers);
	}

	@RequestMapping(value = "/getPolicyAmount", method = RequestMethod.POST)
	public @ResponseBody ResultMsg getPolicyAmount(Long storeId)
			throws Exception {
		//政策总金额
		BigDecimal actualAmount = policyCountService
				.findTotalActualAmount(storeId);
		//政策已用金额
		BigDecimal availableAmount = policyCountService
				.findTotalAvailableAmount(storeId);
		//政策可用金额
		BigDecimal validAmount = actualAmount.subtract(availableAmount);

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("actualAmount", actualAmount);
		data.put("availableAmount", availableAmount);
		data.put("validAmount", validAmount);
		return success().addObjX(data);

	}

	@RequestMapping(value = "/getCostsByContractNo", method = RequestMethod.POST)
	public @ResponseBody ResultMsg getCostsByContractNo(String contract_no) {

		List<Map<String, Object>> costs = costService
				.findListByContractNo(contract_no, 1);
		String itemJson = JsonUtils.toJson(costs);
		return this.success(itemJson);
	}

	@RequestMapping(value = "/findPaymentBatchListByContractId", method = RequestMethod.POST)
	public @ResponseBody ResultMsg findPaymentBatchListByContractId(
			Long contract_id) {

		List<Map<String, Object>> pbcList = contractService
				.findPaymentBatchListByContractId(contract_id);
		String itemJson = JsonUtils.toJson(pbcList);
		return this.success(itemJson);
	}

	@RequestMapping(value = "/replaceOrderItemPro", method = RequestMethod.POST)
	public @ResponseBody ResultMsg replaceOrderItemPro(Long pId, Long orderItem,
			Long orderId, Long changeProductId) {
		Product product = productService.find(pId);
		if (product == null) {
			return this.error("产品不存在");
		}
		Product changeProduct = productService.find(changeProductId);
		if (changeProduct == null) {
			return this.error("要替换的产品不存在");
		}
		Order order = orderService.find(orderId);
		if (order == null) {
			return this.error("订单不存在");
		}
		orderProjectService.replaceOrderItemPro(pId,
				orderItem,
				orderId,
				changeProductId);
		return success();
	}

}