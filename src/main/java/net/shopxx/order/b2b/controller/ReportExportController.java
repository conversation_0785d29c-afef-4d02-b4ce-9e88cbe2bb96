package net.shopxx.order.b2b.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.order.service.MoveLibraryService;
import net.shopxx.order.service.SaleShippingNatureService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.stock.service.StockAgeService;
import net.shopxx.template.service.DTemplateColumnsService;
import net.shopxx.util.CommonUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller - 报表导出controller
 */
@Controller("reportExportController")
@RequestMapping("/b2b/report_export")
public class ReportExportController extends BaseController{
	
	private static Logger logger = LoggerFactory.getLogger(ReportExportController.class);
	
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	
	@Resource(name = "stockAgeServiceImpl")
	private StockAgeService stockAgeService;
	
	@Resource(name = "saleShippingNatureServiceImpl")
	private SaleShippingNatureService saleShippingNatureService;

	@Resource(name = "moveLibraryServiceImpl")
	private MoveLibraryService moveLibraryService;

	@Resource(name = "dTemplateColumnsServiceImpl")
	private DTemplateColumnsService dTemplateColumnsService;



	
		
	@SuppressWarnings("unused")
	@RequestMapping(value = "/stockAgeReport", method = RequestMethod.GET)
	public @ResponseBody ResultMsg stockAgeReport(String startTime, String endTime, Long[] saleOrgId, Long[] storeId,Long[] organizationId, Long[] warehouseId, Long[] productCategoryId, Long[] vProductId, Long[] productId,
			String woodTypeOrColor, String model, Long[] ids, Pageable pageable,HttpSession session,HttpServletResponse response) {
		try {
			//获取分页参数
			Map<String, Integer> segments = getSegment();
			int stage = segments.get("segment");
			int page_size = segments.get("size");
			pageable.setPageSize(page_size);
			//处理数据逻辑
			List<Map<String,Object>> listData  = stockAgeService.stockAgeReport(startTime, endTime, saleOrgId, storeId, organizationId, warehouseId, productCategoryId, vProductId, productId, woodTypeOrColor, model, ids, pageable);
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("maplist",listData);
			//获取文件路径
			ServletContext servletContext =session.getServletContext();
			String path= servletContext.getRealPath("/WEB-INF/excelTemplate/stockAgeReport.xls");
			//获取模板
			TemplateExportParams params = new TemplateExportParams(path);
			//文件名
			Date date = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			String time = sdf.format(date);
			String fileName = "库龄报表"+time;
			//生成模板及数据
			Workbook workbook = ExcelExportUtil.exportExcel(params, map);
			this.setResponseHeader(response, fileName);
			OutputStream os = response.getOutputStream();
			workbook.write(os);
			os.flush();
			os.close();
		}catch(IllegalArgumentException ex){
			logger.error("库龄报表下标异常：",ex);
		}catch (Exception e) {
			logger.error("库龄报表异常：",e);
			return ResultMsg.success("系统错误");
		}
		return ResultMsg.success("导出报表成功");
	}

	public void setResponseHeader(HttpServletResponse response, String fileName) {
		try {
			try {
				fileName = new String(fileName.getBytes(),"ISO8859-1");
			} catch (UnsupportedEncodingException e) {
				logger.error("导出异常命名",e);
			}
            response.setContentType("application/octet-stream;charset=ISO8859-1");
			response.setHeader("Content-Disposition", "attachment;filename="+ fileName+".xls");
			response.addHeader("Pargam", "no-cache");
			response.addHeader("Cache-Control", "no-cache");
		} catch (Exception ex) {
			logger.error("导出异常",ex);
		}
	}
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
	
	
	/**
	 * 预发货通知单 和 退货单
	 * @param objectId
	 * @param type
	 * @param session
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/warehousing_report", method = RequestMethod.GET)
	public @ResponseBody ResultMsg warehousing_report(Long objectId,int type, Pageable pageable,HttpSession session,HttpServletResponse response) {
		try {
			Map<String, Integer> segments = getSegment();
			int stage = segments.get("segment");
			int page_size = segments.get("size");
			pageable.setPageSize(page_size);
			Map<String,Object> map  = shippingService.getInvoiceDataToxls(objectId, type,pageable);

			ServletContext servletContext =session.getServletContext();
            String path= servletContext.getRealPath("/WEB-INF/excelTemplate/advanceDeliveryNotice.xls");

            //获取模板
            TemplateExportParams params = new TemplateExportParams(path);
            //文件名
			Date date = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			String time = sdf.format(date);
			String reportName = CommonUtil.getReportByType(type);
			String fileName = reportName+time;

            //生成模板及数据
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            this.setResponseHeader(response, fileName);
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        }catch(IllegalArgumentException ex){
            logger.error("预发货通知单报表下标异常：",ex);
        }catch (Exception e) {
            logger.error("预发货通知单报表异常：",e);
            return ResultMsg.success("系统错误");
        }
        return ResultMsg.success("导出报表成功");
	}
	
	
	/**
	 * 出入库报表
	 * @param objectId
	 * @param type
	 * @param session
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/receipt_issue_report", method = RequestMethod.GET)
	public @ResponseBody ResultMsg receipt_issue_report(Long objectId,int type, Pageable pageable,HttpSession session,HttpServletResponse response) {
		try {
		    //获取分页信息
			Map<String, Integer> segments = getSegment();
			int stage = segments.get("segment");
			int page_size = segments.get("size");
			pageable.setPageSize(page_size);
			//处理数据
            Map<String, Object> map  = saleShippingNatureService.getReceiptIssueReport(objectId, type, pageable);
            //获取文件路径
			ServletContext servletContext =session.getServletContext();
			String path= servletContext.getRealPath("/WEB-INF/excelTemplate/receiptIssueReport.xls");
            //获取模板
            TemplateExportParams params = new TemplateExportParams(path);
			//处理文件名
			Date date = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			String time = sdf.format(date);
			String reportName = CommonUtil.getReportByType(type);
			String fileName = reportName+time;
            //生成模板及数据
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            this.setResponseHeader(response, fileName);
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        }catch(IllegalArgumentException ex){
            logger.error("出入库报表下标异常：",ex);
        }catch (Exception e) {
            logger.error("出入库报表异常：",e);
            return ResultMsg.success("系统错误");
        }
        return ResultMsg.success("导出报表成功");
	}


	@RequestMapping(value = "/move_libray_report", method = RequestMethod.GET)
	public @ResponseBody ResultMsg move_libray_report(Long objectId,int type, Pageable pageable,HttpSession session,HttpServletResponse response) {
		try {
			//获取分页信息
			Map<String, Integer> segments = getSegment();
			int stage = segments.get("segment");
			int page_size = segments.get("size");
			pageable.setPageSize(page_size);
            Map<String, Object> map  = moveLibraryService.findTransferInformation(objectId, type, pageable);
			//模板路径
			ServletContext servletContext =session.getServletContext();
			String path= servletContext.getRealPath("/WEB-INF/excelTemplate/transferTemplate.xls");

            //获取模板
            TemplateExportParams params = new TemplateExportParams(path);
			//获取文件名
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Date date = new Date();
			String time = sdf.format(date);
			String reportName = CommonUtil.getReportByType(4);
			String fileName = reportName+time;

            //生成模板及数据
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            this.setResponseHeader(response, fileName);
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        }catch(IllegalArgumentException ex){
            logger.error("移库报表下标异常：",ex);
        }catch (Exception e) {
            logger.error("移库报表异常：",e);
            return ResultMsg.success("系统错误");
        }
        return ResultMsg.success("导出报表成功");
	}

}
