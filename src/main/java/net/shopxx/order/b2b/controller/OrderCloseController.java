package net.shopxx.order.b2b.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.order.dao.OrderCloseDao;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderClose;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.service.OrderCloseService;
import net.shopxx.order.service.OrderService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.wf.service.WfObjConfigBaseService;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller("b2bOrderCloseController")
@RequestMapping("/b2b/order_close")
public class OrderCloseController extends BaseController {

	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "orderCloseServiceImpl")
	private OrderCloseService orderCloseService;
	@Resource(name = "orderCloseDao")
	private OrderCloseDao orderCloseDao;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer isCheck, Long id, ModelMap model,Long menuId) {

		model.addAttribute("isCheck", isCheck);
		model.addAttribute("id", id);
		model.addAttribute("menuId", menuId);
		return "/b2b/order_close/list_tb";
	}

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb_code(@PathVariable String code, Integer isCheck,
			Long id, ModelMap model) {

		model.addAttribute("isCheck", isCheck);
		model.addAttribute("id", id);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/order_close/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, Integer isCheck, ModelMap model,Long menuId,Long userId) {
		model.addAttribute("isCheck", isCheck);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		return "/b2b/order_close/list";
	}

	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String listCode(@PathVariable String code, Pageable pageable,
			Integer isCheck, ModelMap model) {
		model.addAttribute("isCheck", isCheck);
		/** 北京零微科技有限公司 */
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (companyInfoId != null && companyInfoId == 18) {
			model.addAttribute("isLw", true);
		}

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/order_close/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn, String orderSn, Integer[] status,
			Long storeId, Pageable pageable) {

		Page<Map<String, Object>> page = orderCloseService.findPage(sn,
				orderSn,
				status,
				storeId,
				pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 查看
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, Integer isCheck, ModelMap model) {

		OrderClose orderClose = orderCloseService.find(id);
		model.addAttribute("orderClose", orderClose);

		List<Map<String, Object>> items0 = orderCloseService.findItemListByOrderCloseId(id,
				0);
		String item_json = JsonUtils.toJson(items0);
		model.addAttribute("item_json", item_json);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);

		model.addAttribute("isCheck", isCheck);
		return "/b2b/order_close/edit";
	}

	@RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
	public String editCode(@PathVariable String code, Long id, Integer isCheck,
			ModelMap model) {

		OrderClose orderClose = orderCloseService.find(id);
		model.addAttribute("orderClose", orderClose);

		List<Map<String, Object>> items0 = orderCloseService.findItemListByOrderCloseId(id,
				0);
		String item_json = JsonUtils.toJson(items0);
		model.addAttribute("item_json", item_json);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);

		model.addAttribute("isCheck", isCheck);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/order_close/edit";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(OrderClose orderClose, Long orderId, Long sbuId) {

		Order order = orderService.find(orderId);
		Sbu sbu = sbuService.find(sbuId);
		orderClose.setOrder(order);
		orderClose.setSbu(sbu);
        List<Map<String,Object>> shippings=orderService.findShippingsById(orderId);
        String shippingSn="";
        if(shippings.size()>0) {
            for(int i=0;i<shippings.size();i++) {
                shippingSn+=shippings.get(i).get("sn").toString()+",";
            }
            ExceptionUtil.throwServiceException("尚有未处理发货单:"+shippingSn);

        }
        List<Map<String,Object>> orderCloses=orderCloseService.findOrderClosesById(orderId);
        String orderCloseSn = "";
        if(orderCloses.size()>0) {
            for(int i=0;i<orderCloses.size();i++) {
                orderCloseSn+=orderCloses.get(i).get("sn").toString()+",";
            }
            ExceptionUtil.throwServiceException("尚有未处理关闭单:"+orderCloseSn);

        }
        
      //订单关闭单保存审核的时候，要判断是否存在未审核的出库单，若存在，需要提示，不能进行审核
		existUnauditedAmShipping(orderClose.getOrder().getId());
		
		orderCloseService.saveOrderClose(orderClose);
		return success().addObjX(orderClose.getId());
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(OrderClose orderClose) {

		orderCloseService.updateOrderClose(orderClose);
		return success();
	}

	private List<Map<String, Object>> sort(List<Map<String, Object>> items,
			Long parent) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if (items != null) {
			for (Map<String, Object> map : items) {
				if ((map.get("parent") != null && parent != null && Long.parseLong(map.get("parent")
						.toString()) == parent.longValue())
						|| (map.get("parent") == null && parent == null)) {
					result.add(map);
					result.addAll(sort(items,
							Long.parseLong(map.get("id").toString())));
				}
			}
		}
		return result;
	}

	/**
	 * 确认、作废
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check(Long id, Integer status) throws Exception {
		OrderClose orderClose = orderCloseService.find(id);
		if (orderClose == null) {
			return this.error("关闭单不存在！");
		}
		if (orderClose.getStatus() != 0) {
			return this.error("关闭单不是未确认状态，不允许操作！");
		}
		if (status == 1) {
			//订单关闭单保存审核的时候，要判断是否存在未审核的出库单，若存在，需要提示，不能进行审核
			existUnauditedAmShipping(orderClose.getOrder().getId());
			
			// 关闭单确认
			orderCloseService.check(orderClose);
		}else if (status == 2) {
            // 作废
            orderClose.setStatus(3);
            orderCloseService.updateOrderClose(orderClose);
        }

		return success();
	}

	private void existUnauditedAmShipping(Long orderId) {
		List<Map<String,Object>> amShippings = orderService.findAmShippingById(orderId);
		if(amShippings != null && amShippings.size()>0) {
			StringBuffer eSnSbf = new StringBuffer();
			for(Map<String, Object> map : amShippings){
				if(map.get("e_sn") !=null) {
					eSnSbf.append(map.get("e_sn").toString()).append(",");
				}
			}
			
			if(eSnSbf.length()>0) {
				eSnSbf.setLength(eSnSbf.length()-1);
				StringBuffer errorMsgSbf = new StringBuffer("存在未审核的出库单【")
				.append(eSnSbf.toString())
				.append("】,不允许操作！");
				ExceptionUtil.throwServiceException(errorMsgSbf.toString());
			}
		}
	}

	@RequestMapping(value = "/checkNew", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checkNew(Long id, Integer status) throws Exception {
		OrderClose orderClose = orderCloseService.find(id);
		if (orderClose == null) {
			return this.error("关闭单不存在！");
		}
		if (orderClose.getStatus() != 0) {
			return this.error("关闭单不是未确认状态，不允许操作！");
		}
		if (status == 1) {
			// 关闭单确认
			// orderCloseService.checkOrderClose(orderClose);
			orderCloseService.checkNew(orderClose);
		}if (status == 2) {
            // 作废
            orderClose.setStatus(3);
        }

		return success();
	}

	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long id, String sn, Integer isCheck, Long sbuId,
			ModelMap model) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);
		model.addAttribute("sbuId", sbuId);
		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			if (orderItem.getShipPlanQuantity() != null && orderItem.getShipPlanQuantity().compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",shippingMethodBaseService.findList(null, filters, null));
		/** 订单主表 */
		List<Map<String, Object>> orderInfoList = orderService.findListById(order.getId()
				.toString());
		if (orderInfoList.size() > 0) {
			Map<String, Object> item = orderInfoList.get(0);
			item.put("operate_type", 0);

			Map<String, Object> item2 = new HashMap<String, Object>();
			item2.putAll(item);
			item2.put("operate_type", 1);
			orderInfoList.add(item2);
		}
		model.addAttribute("order_json", JsonUtils.toJson(orderInfoList));
		/** 订单明细 */
		List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
		orderItems = sort(orderItems, null);
		List<Map<String, Object>> items1 = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> item : orderItems) {
			Map<String, Object> map = orderCloseDao.findCloseMaxQuantityByOrderItemId(Long.valueOf(item.get("id").toString()));
			System.out.println("maps:"+map.toString());
			BigDecimal max_quantity = new BigDecimal(map.get("close_branch_available").toString());
			// 可关闭数量
			item.put("max_quantity", max_quantity);
			BigDecimal close_square = new BigDecimal(map.get("close_quantity_available").toString());
			item.put("close_square", close_square);
			BigDecimal ship_branch_quantity = new BigDecimal(map.get("shipping_quantity").toString());
			// 发货通知数量(作废除外)
			item.put("havent_ship_quantity", ship_branch_quantity);
			BigDecimal havent_ship_branch_quantity = new BigDecimal(map.get("ship_branch_quantity").toString());
			// 发货通知数量(已审核  未审核)
			item.put("havent_ship_branch_quantity", havent_ship_branch_quantity);
			// 发货通知数量(已审核  未审核)
			BigDecimal shipped_branch_quantity = null ;
			if(!ConvertUtil.isEmpty(map.get("shipped_branch"))){
				shipped_branch_quantity = new BigDecimal(map.get("shipped_branch").toString());
			}else{
				shipped_branch_quantity = BigDecimal.ZERO;
			}	
			item.put("shipped_branch_quantity",shipped_branch_quantity);
			BigDecimal shipped_quantity = new BigDecimal(map.get("shipped_quantity").toString());
			// 发货通知数量(已审核  未审核)
			item.put("shipped_quantity",shipped_quantity);
			BigDecimal closedAmount = new BigDecimal(orderCloseDao.findClosedQuantityByOrderItemId(Long.valueOf(item.get("id").toString())).get("closed_quantity_nb").toString());
			item.put("closed_amount", closedAmount);
            BigDecimal closedBranch = new BigDecimal(orderCloseDao.findClosedQuantityByOrderItemId(Long.valueOf(item.get("id").toString())).get("closed_quantity").toString());
            item.put("closed_branch", closedBranch);
			item.put("operate_type", 0);
			items1.add(item);
			Map<String, Object> item2 = new HashMap<String, Object>();
			item2.putAll(item);
			item2.put("operate_type", 1);
			items1.add(item2);
		}
		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);
		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,filters,null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);
		// 未定义产品是否支持下单
		int undefinedProduct2Order = 0;
		// b2b业务商品是否可以改价
		int appProductChangePrice = 0;
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",WebUtils.getCurrentCompanyInfoId()));
			appProductChangePrice = Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",WebUtils.getCurrentCompanyInfoId()));
		}catch (Exception e) {
			
		}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);
		model.addAttribute("appProductChangePrice", appProductChangePrice);
		return "/b2b/order_close/add";
	}

	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String addCode(@PathVariable String code, Long id, String sn,
			Integer isCheck, ModelMap model) {

		Order order = null;
		if (id == null || id.longValue() <= 0) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			List<Order> orders = orderService.findList(1, filters, null);
			if (orders.size() > 0) {
				order = orders.get(0);
			}
		}else {
			order = orderService.find(id);
		}
		id = order.getId();
		model.addAttribute("order", order);
		boolean isReject = true;
		for (OrderItem orderItem : order.getOrderItems()) {
			if (orderItem.getShipPlanQuantity() != null && orderItem.getShipPlanQuantity().compareTo(BigDecimal.ZERO) == 1) {
				// 订单已存在发货单，不允许驳回
				isReject = false;
				break;
			}
		}
		model.addAttribute("isReject", isReject);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",shippingMethodBaseService.findList(null, filters, null));
		/** 订单主表 */
		List<Map<String, Object>> orderInfoList = orderService.findListById(order.getId().toString());
		if (orderInfoList.size() > 0) {
			Map<String, Object> item = orderInfoList.get(0);
			item.put("operate_type", 0);
			Map<String, Object> item2 = new HashMap<String, Object>();
			item2.putAll(item);
			item2.put("operate_type", 1);
			orderInfoList.add(item2);
		}
		model.addAttribute("order_json", JsonUtils.toJson(orderInfoList));
		/** 订单明细 */
		List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(id.toString(),null,null,null);
		orderItems = sort(orderItems, null);
		List<Map<String, Object>> items1 = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> item : orderItems) {
			Map<String, Object> map = orderCloseDao.findCloseMaxQuantityByOrderItemId(Long.valueOf(item.get("id").toString()));
			BigDecimal max_quantity = new BigDecimal(map.get("close_quantity_nb").toString());
			// 可关闭数量
			item.put("max_quantity", max_quantity);
			BigDecimal ship_branch_quantity = new BigDecimal(map.get("ship_quantity_nb").toString());
			// 发货通知数量(作废除外)
			item.put("ship_branch_quantity", ship_branch_quantity);
			BigDecimal havent_ship_branch_quantity = new BigDecimal(map.get("havent_ship_quantity_nb").toString());
			// 发货通知数量(已审核  未审核)
			item.put("havent_ship_branch_quantity", havent_ship_branch_quantity);
			BigDecimal shipped_part_branch_quantity = new BigDecimal(map.get("shipped_part_quantity_nb").toString());
			// 部分发货数量
			BigDecimal shipped_full_branch_quantity = new BigDecimal(map.get("shipped_full_quantity_nb").toString());
			// 完全发货数量
			item.put("shipped_branch_quantity",shipped_full_branch_quantity.add(shipped_part_branch_quantity));
			BigDecimal closedAmount = new BigDecimal(
					orderCloseDao.findClosedQuantityByOrderItemId(Long.valueOf(item.get("id").toString())).get("closed_quantity_nb").toString());
			item.put("closed_amount", closedAmount);
			BigDecimal closedBrach = new BigDecimal(
					orderCloseDao.findClosedQuantityByOrderItemId(Long.valueOf(item.get("id").toString())).get("closed_quantity").toString());
			item.put("closed_amount", closedAmount);
			item.put("closed_branch", closedBrach);
			item.put("operate_type", 0);
			items1.add(item);
			Map<String, Object> item2 = new HashMap<String, Object>();
			item2.putAll(item);
			item2.put("operate_type", 1);
			items1.add(item2);
		}

		String orderItem_json = JsonUtils.toJson(orderItems);
		model.addAttribute("orderItem_json", orderItem_json);
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);
		filters.clear();
		filters.add(Filter.eq("code", "FreightChargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> freightChargeTypes = systemDictService.findList(null,filters,null);
		model.addAttribute("freightChargeTypes", freightChargeTypes);
		if (order != null) {
			TriplicateForm triplicateForm = orderService.getTriplicateForm(id);
			model.addAttribute("triplicateForm", triplicateForm);
		}
		int undefinedProduct2Order = 0;// 未定义产品是否支持下单
		int appProductChangePrice = 0;// b2b业务商品是否可以改价
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
			appProductChangePrice = Integer.parseInt(SystemConfig.getConfig("appProductChangePrice",
					WebUtils.getCurrentCompanyInfoId()));
		}catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);
		model.addAttribute("appProductChangePrice", appProductChangePrice);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/order_close/add";
	}

}
