package net.shopxx.order.b2b.controller;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.PriceDifferenceService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller("b2bPriceDifferenceController")
@RequestMapping("/b2b/priceDifference")  
public class PriceDifferenceController extends BaseController{
	
	
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "priceDifferenceServiceImpl")
	private PriceDifferenceService priceDifferenceService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model, Integer flag,Long menuId) {
		model.addAttribute("flag", flag);
		model.addAttribute("menuId", menuId);
		return "/b2b/priceDifference/list_tb";
	}
	
	
	/*
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable,
			ModelMap model,Long userId,Long menuId) {
		/**
		 * 用户经营组织权限
		 */
		List<Filter> filters = new ArrayList<Filter>();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						 Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						 if(organization!=null){
							 organizationList.add(organization);
						 }
					}
				}
			}
		}else{
			//经营组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			organizationList = organizationService.findList(null,filters,null);
		}
		model.addAttribute("organizations", organizationList);
		

		//sbu
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,
				filters,
				null);
		model.addAttribute("sbus", sbus);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);

		return  "/b2b/priceDifference/list";
	}
	
	/*
	 * 列表
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(Long saleOrgId, Long storeId, String storeCode,
			Long[] sbuId, Long[] organizationId, String firstTime, String lastTime,
			Pageable pageable, ModelMap model) {
			

		Page<Map<String, Object>> page = priceDifferenceService.findPage(saleOrgId,
				storeId,
				sbuId,
				organizationId,
				firstTime,
				lastTime,
				pageable);

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	
	
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(Long saleOrgId, Long storeId,
			String storeCode, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime) {
				

		Integer size = priceDifferenceService.findTable(saleOrgId,
				storeId,
				sbuId,
				organizationId,
				firstTime,
				lastTime,null).size();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(Long saleOrgId, Long storeId,
			String storeCode, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime, ModelMap model) {
			

		List<Map<String, Object>> data = priceDifferenceService.findTable(saleOrgId,
				storeId,
				sbuId,
				organizationId,
				firstTime,
				lastTime,
				null);
		return getModelAndViewForList(data, model);
	}
	
	
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {

		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		//设置标题
		String[] header = { "发货单号",
				"ERP单号",
				"开单人",
				"业务员编码",
				"业务员",
				"单据日期",
				"SBU",
				"组织",
				"客户编码",
				"客户名称",
				"客户简称",
				"仓库",
				"大区",
				"机构",
				"明细渠道",
				"产品名称",
				"产品编码",
				"品类",
				"系列",
				"木种/花色",
				"产品型号",
				"产品等级",
				"规格",
				"结构",
				"重点品类",
				"价格类型",
				"数量",
				"单价",
				"平台结算原价",
				"平台结算特价",
				"产品部结算价",
				"A价",
				"B价",
				"C价",
				"D价",
				"A价价差",
				"B价价差",
				"C价价差",
				"D价价差"};
		//设置单元格取值
		String[] properties = {"shipping_sn",
				"erp_sn",
				"drawer_name",
				"sales_username",
				"sales_name",
				"create_date",
				"sbu",
				"organization",
				"out_trade_no",
				"store_name",
				"alias",
				"warehouse",
				"region",
				"sale_org",
				"type",
				"product_name",
				"vonder_code",
				"product_two",
				"category_name",
				"wood_type_or_color",
				"model",
				"level_name",
				"spec",
				"product_structure",
				"key_category",
				"rank_name",
				"quantity",
				"price",
				"re_sale_org_pirce",
				"sale_org_pirce",
				"product_org_pirce",
				"a_price",
				"b_price",
				"c_price",
				"d_price",
				"a_cj",
				"b_cj",
				"c_cj",
				"d_cj"};
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256};
		ModelAndView a = new ModelAndView(new ExcelView(filename, null,
				properties, header, widths, null, data, null), model);
		return a;
	}
	
	
	

}
