package net.shopxx.order.b2b.controller;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import net.shopxx.product.service.ProductBaseService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.DeliveryCorp;
import net.shopxx.basic.entity.DriverInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SbuItems;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.DriverInfoService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.PaymentMethodBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PolicyCountContractService;
import net.shopxx.finance.service.PolicyCountService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.intf.NatureLockWarehouseOnHandQty;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.CreditRechargeContractService;
import net.shopxx.member.service.DepositRechargeContractService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingAttach;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.purchase.service.PurchaseShippingService;
import net.shopxx.order.service.CostService;
import net.shopxx.order.service.OffsiteBranchService;
import net.shopxx.order.service.OrderCloseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.product.entity.Product;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseStore;
import net.shopxx.stock.service.DeliveryCorpBaseService;
import net.shopxx.stock.service.StockService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseSmethodService;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.NumberToCN;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.util.kualidi.DeliveryUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

@Controller("b2bShippingController")
@RequestMapping("/b2b/shipping")
public class ShippingController extends BaseController {

	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "deliveryCorpBaseServiceImpl")
	private DeliveryCorpBaseService deliveryCorpBaseService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "driverInfoServiceImpl")
	private DriverInfoService driverInfoBaseService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreBaseService;
	@Resource(name = "stockServiceImpl")
	private StockService stockService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "purchaseShippingServiceImpl")
	private PurchaseShippingService purchaseShippingService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "paymentMethodBaseServiceImpl")
	private PaymentMethodBaseService paymentMethodBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "creditRechargeContractServiceImpl")
	private CreditRechargeContractService creditRechargeContractService;
	@Resource(name = "depositRechargeContractServiceImpl")
	private DepositRechargeContractService depositRechargeContractService;
	@Resource(name = "policyCountContractServiceImpl")
	private PolicyCountContractService policyCountContractService;
	@Resource(name = "policyCountServiceImpl")
	private PolicyCountService policyCountService;
	@Resource(name = "orderCloseServiceImpl")
	private OrderCloseService orderCloseService;
	@Resource(name = "offsiteBranchServiceImpl")
	private OffsiteBranchService offsiteBranchService;
	@Resource(name = "costServiceImpl")
	private CostService costService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "warehouseSmethodServiceImpl")
	private WarehouseSmethodService warehouseSmethodService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods", shippingMethodBaseService.findList(null, filters, null));

		String useLockStock = "0";
		try {
			// 启用锁库存模式：0 否，1 是
			useLockStock = SystemConfig.getConfig("useLockStock", WebUtils.getCurrentCompanyInfoId());
		} catch (Exception e) {
		}
		model.addAttribute("useLockStock", useLockStock);

		return "/b2b/shipping/add";
	}

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, Integer flag, Long objTypeId, Long menuId, Long objid, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("menuId", menuId);
	
		return "/b2b/shipping/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model, Long menuId, Long userId, Integer pageType,Long orderIndex) {

		model.addAttribute("pageType", pageType);

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		
		
		// 五期权限
		try {
			String value = SystemConfig.getConfig("link5Roles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int link5Roles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					link5Roles++;
					break;
				}
			}
		model.addAttribute("link5Roles", link5Roles); 
		}
		catch (RuntimeException e) {

		}
		/**
		 * 用户经营组织权限
		 */
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);

		return "/b2b/shipping/list";
	}

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Pageable pageable, Integer flag, Long objid, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("code", code);
		model.addAttribute("objid", objid);
		return "/" + code + "/b2b/shipping/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Pageable pageable, ModelMap model) {

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(62L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/shipping/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn, String orderSn, String outTradeNo, String trackingNo,
											 Integer[] status, Integer[] wfState, Integer[] acceptStatus, Long[] warehouseId, String warehouseName,
											 Long deliveryCorpId, Integer warehouseType, Long[] storeId, Long supplierId, Long[] saleOrgId,
											 String firstTime, String lastTime, String firstErpTime, String lastErpTime, Long sbuId, String erpSn,
											 Long[] productId, Pageable pageable, ModelMap model, String colourNumber, String moistureContent,
											 String batch, Integer pageType, Long[] warehouseIds, Long[] organizationIds, String storeMemberName ,String[] statusType) {

		// 2019-05-16 冯旗 加ERP单号搜索条件,流程状态(erpSn，status)
		Page<Map<String, Object>> page = shippingService.newfindPage(sn, orderSn, outTradeNo, trackingNo, status,
				wfState, acceptStatus, warehouseId, deliveryCorpId, warehouseType, storeId, supplierId, firstTime,
				lastTime, firstErpTime, lastErpTime, null, saleOrgId, sbuId, erpSn, productId, pageable, colourNumber,
				moistureContent, batch, pageType, warehouseIds, organizationIds, storeMemberName,statusType);

		List<Map<String, Object>> shippings = page.getContent();

		if (!shippings.isEmpty()) {
			String ids = "";
			for (int i = 0; i < shippings.size(); i++) {
				Map<String, Object> map = shippings.get(i);
				if (i == shippings.size() - 1) {
					ids += map.get("id");
				} else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> shippingItems = shippingService.findShippingItemListsByShippingId(ids,
					organizationIds, true, statusType);
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : shippings) {
				items = new ArrayList<Map<String, Object>>();
				String shippingId = map.get("id").toString();
				for (Map<String, Object> itemMap : shippingItems) {
					String oid = itemMap.get("shipping").toString();
					int bom_flag = Integer.parseInt(itemMap.get("bom_flag").toString());
					if (shippingId.equals(oid) && bom_flag != 1) {
						items.add(itemMap);
					}
				}
				map.put("shipping_items", items);
			}
		}
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_out/{code}", method = RequestMethod.GET)
	public String list_out(@PathVariable String code, Pageable pageable, ModelMap model) {
		model.addAttribute("code", code);
		return CommonUtil.getFolderPrefix(code) + "/b2b/shipping/list_out";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_out_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg listOutData(String sn, String orderSn, String outTradeNo, String trackingNo,
											   Integer[] status, Long[] warehouseId, String warehouseName, Long deliveryCorpId, Integer warehouseType,
											   Long[] storeId, Long supplierId, Long productId, String firstTime, String lastTime, String productModel,
											   Pageable pageable, ModelMap model) {

		if (status == null || status.length == 0)
			status = new Integer[] { 1, 2, 3, 4 };
		Page<Map<String, Object>> page = shippingService.newfindItemPage(sn, orderSn, outTradeNo, trackingNo, status,
				warehouseId, deliveryCorpId, productId, null, null, new Integer[] { 0, 2 }, new Integer[] { 2, 3 },
				storeId, supplierId, null, null, productModel, pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_shipping_item", method = RequestMethod.GET)
	public String select_shipping_item(Integer flag, Integer multi, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("multi", multi);
		return "/b2b/shipping/select_shipping_item";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_shipping_item_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg select_shipping_item_data(String sn, String orderSn, String trackingNo,
															 Integer[] status, String warehouseName, String storeName, String productName, String model,
															 String vonderCode, Integer flag, Pageable pageable) {

		if (flag != null && flag == 1) {
			// 销售发票，完全发货
			status = new Integer[] { 4 };
		} else if (status == null || status.length == 0) {
			status = new Integer[] { 1, 2, 3, 4 };
		}
		Page<Map<String, Object>> page = shippingService.findItemPageForWin(sn, orderSn, null, trackingNo, status, null,
				null, null, null, null, new Integer[] { 0, 1 }, new Integer[] { 2 }, null, null, storeName,
				warehouseName, productName, model, vonderCode, flag, pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	/**
	 * 查看
	 *
	 * @throws Exception
	 */
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, String sn, Integer readOnly, Integer flag, ModelMap model, Integer pageType,Long menuId,Long isEdit,Long orderIndex)
			throws Exception {
		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
		model.addAttribute("isEdit",isEdit);

		if (!ConvertUtil.isEmpty(pageType)) {
			String typeName = "";
			if (pageType == 1) {
				typeName = "销售发货";
			} else if (pageType == 2) {
				typeName = "销售退货";
			}
			model.addAttribute("pageType", pageType);
			model.addAttribute("typeName", typeName);
		}

		Shipping shipping = null;

		if (id != null) {
			shipping = shippingService.find(id);
		} else if (sn != null) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			shipping = shippingService.find(filters);
			id = shipping.getId();
		}
		if (shipping.getWarehouse() != null) {
			Warehouse warehouse = shipping.getWarehouse();
			model.addAttribute("warehouse", warehouse);
			model.addAttribute("smethods", warehouse.getWarehouseSmethodList());
		}

		model.addAttribute("shipping", shipping);
		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods", shippingMethodBaseService.findList(null, filters, null));
		if (shipping != null) {
			TriplicateForm triplicateForm = shippingService.getTriplicateForm(id);
			if (triplicateForm == null && shipping.getStatus() == 1) {// 已审核且不存在发货单PDF,重新生成一个pdf
				Setting setting = SettingUtils.get();
				triplicateForm = shippingService.createTriplicateForm(setting, shipping, null);
			}
			model.addAttribute("triplicateForm", triplicateForm);
		}
		List<Map<String, Object>> list = shippingService.findShippingItemListByShippingId(id.toString(), null, null);
		String ids = "";
		for (int i = 0; i < list.size(); i++) {
			String bom_flag = list.get(i).get("bom_flag").toString();
			if (!bom_flag.equals("2")) {
				ids += list.get(i).get("order_item").toString() + ",";
			}
		}

		List<Map<String, Object>> orderlist = shippingService
				.findOrderItemListByItemIds(ids.length() > 0 ? ids.substring(0, ids.length() - 1) : ids);
		for (int i = 0; i < list.size(); i++) {
			String bom_flag = list.get(i).get("bom_flag").toString();
			if (!bom_flag.equals("2")) {
				for (int j = 0; j < orderlist.size(); j++) {
					if (list.get(i).get("order_item").toString().equals(orderlist.get(j).get("id").toString())) {
						orderlist.get(j).put("line_no",
								list.get(i).get("line_no") == null ? "" : list.get(i).get("line_no").toString());
					}
				}
			}
			if (bom_flag.equals("1")) {
				list.remove(i);
			}
		}
		for (int i = 0; i < list.size(); i++) {
			String oid = list.get(i).get("order_item").toString();
			list.get(i).put("closed_quantity",
					orderCloseService.findClosedQuantityByOrderItemId(Long.parseLong(oid)).get("closed_quantity"));
		}

		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		String orderjsonStr = JsonUtils.toJson(orderlist);
		model.addAttribute("orderjsonStr", orderjsonStr);

		/** 订单附件 */
		String shippingAttach_json = JsonUtils.toJson(shippingService.findAttachsByShippingId(id));
		model.addAttribute("shippingAttach_json", shippingAttach_json);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(shipping.getSn(), 4));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("flag", flag);
		// test/正式:57// nadev:59
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(57L);
		model.addAttribute("isCheckWf", isCheckWf);
		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null, filters, null);
		model.addAttribute("organizations", organizations);

		// sbu
		Sbu sbu = shipping.getSbu();
		List<SbuItems> sbuItems = sbu.getShippingMethodSbuList();
		model.addAttribute("sbuItems", sbuItems);
		model.addAttribute("sbuIds", sbu.getId());
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		model.addAttribute("sbus", sbus);
		model.addAttribute("isMember", storeMember.getMemberType());

		// 获取发货单中订单明细的业务类型
		List<ShippingItem> shippingItems = shipping.getShippingItems();
		if (shippingItems != null) {
			Order order = shippingItems.get(0).getOrder();
			if (order != null) {
				// 是否包含业务类型备注
				if (order.getBusinessType().getRemark().toString().contains("NeedFee=Y")) {
					// 判断是否启用
					if (order.getBusinessType().getIsEnabled() == true) {
						model.addAttribute("isServiceCharge", 1);
					}
				} else {
					model.addAttribute("isServiceCharge", 0);
				}
			}
		}

		if (shipping.getServiceCharge() != null && shipping.getServiceCharge() == true) {
			model.addAttribute("isServiceCharge", 1);
		} else {
			model.addAttribute("isServiceCharge", 0);
		}

		if (shipping.getServiceProvider() != null) {
			Store store = storeBaseService.find(Long.valueOf(shipping.getServiceProvider()));
			model.addAttribute("serviceProvider", store.getName());
		}

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> listString = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}

		// 订单行是否展示金额 0不展示 非0展示
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles", WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount);
		} catch (RuntimeException e) {

		}

		// 运输方式
		filters.clear();
		filters.add(Filter.eq("code", "transportType"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> transportTypeList = systemDictService.findList(null, filters, null);
		model.addAttribute("transportTypeList", transportTypeList);

		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(linkStockValue)) {
			Integer linkStock = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}

		// 发运管理是否启用库存查询 0不展示、1展示
		Integer shipmentStockQueryRoles = roleJurisdictionUtil.getRoleCount("shipmentStockQueryRoles");
		model.addAttribute("shipmentStockQueryRoles", shipmentStockQueryRoles);

		// 角色是否能修改平台结算价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> lists = Arrays.asList(role);
			int editSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (lists.contains(userRole.getPcRole().getName())) {
					editSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("editSaleOrgPrice", editSaleOrgPrice); // 角色是否能修改产品价格  1 可修改     非1  不可修改
		}
		catch (RuntimeException e) {

		}

		// 角色是否能查看平台产品价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("seeSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> lists = Arrays.asList(role);
			int seeSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (lists.contains(userRole.getPcRole().getName())) {
					seeSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("seeSaleOrgPrice", seeSaleOrgPrice);
		}
		catch (RuntimeException e) {
		}

		return "/b2b/shipping/view";
	}

	/**
	 * 查看
	 *
	 * @throws Exception
	 */
	@RequestMapping(value = "/view/{code}", method = RequestMethod.GET)
	public String view(@PathVariable String code, Long id, String sn, Integer readOnly, Integer flag, ModelMap model)
			throws Exception {

		Shipping shipping = null;
		if (id != null) {
			shipping = shippingService.find(id);
		} else if (sn != null) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			shipping = shippingService.find(filters);
			id = shipping.getId();
		}

		model.addAttribute("shipping", shipping);
		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods", shippingMethodBaseService.findList(null, filters, null));
		if (shipping != null) {
			TriplicateForm triplicateForm = shippingService.getTriplicateForm(id);
			if (triplicateForm == null && shipping.getStatus() == 1) {// 已审核且不存在发货单PDF,重新生成一个pdf
				Setting setting = SettingUtils.get();
				triplicateForm = shippingService.createTriplicateForm(setting, shipping, null);
			}
			model.addAttribute("triplicateForm", triplicateForm);
		}
		List<Map<String, Object>> list = shippingService.findShippingItemListByShippingId(id.toString(), null, null);
		String ids = "";
		for (int i = 0; i < list.size(); i++) {
			String bom_flag = list.get(i).get("bom_flag").toString();
			if (!bom_flag.equals("2")) {
				ids += list.get(i).get("order_item").toString() + ",";
			}
		}

		List<Map<String, Object>> orderlist = shippingService
				.findOrderItemListByItemIds(ids.length() > 0 ? ids.substring(0, ids.length() - 1) : ids);

		for (int i = 0; i < list.size(); i++) {
			String bom_flag = list.get(i).get("bom_flag").toString();
			if (!bom_flag.equals("2")) {
				for (int j = 0; j < orderlist.size(); j++) {
					if (list.get(i).get("order_item").toString().equals(orderlist.get(j).get("id").toString())) {
						orderlist.get(j).put("line_no",
								list.get(i).get("line_no") == null ? "" : list.get(i).get("line_no").toString());
					}
				}
			}
			if (bom_flag.equals("1")) {
				list.remove(i);
			}
		}

		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		String orderjsonStr = JsonUtils.toJson(orderlist);
		model.addAttribute("orderjsonStr", orderjsonStr);

		/** 订单附件 */
		String shippingAttach_json = JsonUtils.toJson(shippingService.findAttachsByShippingId(id));
		model.addAttribute("shippingAttach_json", shippingAttach_json);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(shipping.getSn(), 4));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("flag", flag);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null, filters, null);
		model.addAttribute("organizations", organizations);
		model.addAttribute("code", code);
		model.addAttribute("paymentMethods", paymentMethodBaseService.findAll());
		Long storeId = shipping.getStore().getId();
		BigDecimal actualAmount = policyCountService.findTotalActualAmount(storeId);
		BigDecimal availableAmount = policyCountService.findTotalAvailableAmount(storeId);
		// 政策
		List<Map<String, Object>> list3 = policyCountContractService.findItemListByOrderNo(shipping.getSn(), 2);
		for (Map<String, Object> data : list3) {
			data.put("actual_amount", actualAmount);
			data.put("available_amount", availableAmount);
		}

		// 回款
		List<Map<String, Object>> list2 = depositRechargeContractService.findItemListByOrderNo(shipping.getSn(), 2);

		// 授信
		List<Map<String, Object>> list1 = creditRechargeContractService.findItemListByOrderNo(shipping.getSn(), 2);

		List<Map<String, Object>> megaList = new ArrayList<Map<String, Object>>();
		megaList.addAll(list3);
		megaList.addAll(list2);
		megaList.addAll(list1);
		model.addAttribute("megaListItems", JsonUtils.toJson(megaList));

		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(62L);
		model.addAttribute("isCheckWf", isCheckWf);

		return "/" + code + "/b2b/shipping/view";
	}

	/**
	 * 发货单列表
	 */
	@RequestMapping(value = "/shippinglist", method = RequestMethod.GET)
	public String shippinglist(Long[] ids, ModelMap model) {
		model.addAttribute("ids", ids);
		return "/b2b/shipping/shippinglist";
	}

	/**
	 * 发货单列表数据
	 */
	@RequestMapping(value = "/shippinglist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg shippinglist_data(Long[] ids, ModelMap model) {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return ResultMsg.error("15162", model);
		}
		String idss = "";
		for (int i = 0; i < ids.length; i++) {
			if (i == ids.length - 1) {
				idss += ids[i];
			} else {
				idss += ids[i] + ",";
			}
		}
		List<Map<String, Object>> shippings = shippingService.findListById(idss);
		String jsonPage = JsonUtils.toJson(shippings);

		return success(jsonPage);
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/itemlist", method = RequestMethod.GET)
	public String itemlist(Long[] ids, Long[] itemIds, BigDecimal[] quantity, ModelMap model) {
		model.addAttribute("ids", ids);
		model.addAttribute("itemIds", itemIds);
		model.addAttribute("quantity", quantity);
		return "/b2b/shipping/itemlist";
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/itemlist/{code}", method = RequestMethod.GET)
	public String itemlistCode(@PathVariable String code, Long[] ids, Long[] itemIds, BigDecimal[] quantity,
							   ModelMap model) {
		model.addAttribute("ids", ids);
		model.addAttribute("itemIds", itemIds);
		model.addAttribute("quantity", quantity);
		model.addAttribute("code", code);
		return "/" + code + "/b2b/shipping/itemlist";
	}

	/**
	 * 发货明细列表数据
	 */
	@RequestMapping(value = "/itemlist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg itemlistData(Long[] ids, Long[] itemIds, BigDecimal[] quantity, ModelMap model) {

		// if (ids == null || ids.length == 0) {
		// //请选择发货单
		// return ResultMsg.error("15162", model);
		// }
		// String idss = "";
		// for (int i = 0; i < ids.length; i++) {
		// if (i == ids.length - 1) {
		// idss += ids[i];
		// }
		// else {
		// idss += ids[i] + ",";
		// }
		// }
		// List<Map<String, Object>> shippings =
		// shippingService.findListById(idss);
		// if (!shippings.isEmpty()) {
		// String itemids = "";
		// for (int i = 0; i < shippings.size(); i++) {
		// Map<String, Object> map = shippings.get(i);
		// if (i == shippings.size() - 1) {
		// itemids += map.get("id");
		// }
		// else {
		// itemids += map.get("id") + ",";
		// }
		// }
		// List<Map<String, Object>> shippingItems =
		// shippingService.findShippingItemListByShippingId(itemids);
		// List<Map<String, Object>> items = null;
		// for (Map<String, Object> map : shippings) {
		// items = new ArrayList<Map<String, Object>>();
		// String shippingId = map.get("id").toString();
		// for (Map<String, Object> itemMap : shippingItems) {
		// String oid = itemMap.get("shipping").toString();
		// if (shippingId.equals(oid)) {
		// items.add(itemMap);
		// }
		// }
		// map.put("shipping_items", items);
		// }
		// }
		// String jsonPage = JsonUtils.toJson(shippings);

		List<ShippingItem> items = new ArrayList<ShippingItem>();
		for (int i = 0; i < itemIds.length; i++) {
			ShippingItem shippingItem = shippingItemService.find(itemIds[i]);
			shippingItem.setReturnQuantity(quantity[i]);
			shippingItem.setCompanyInfoId(shippingItem.getOrder() == null ? 0L : shippingItem.getOrder().getId());
			shippingItem.setOrderSn(shippingItem.getShipping().getSn());
			if (shippingItem.getWarehouse() != null && shippingItem.getWarehouse().getName() != null) {
				shippingItem.setOutTradeNo(shippingItem.getWarehouse().getName());
			}
			items.add(shippingItem);
		}
		String jsonPage = JsonUtils.toJson(items);

		return success(jsonPage);
	}

	/**
	 * 保存
	 *
	 * @param shipping
	 * @param warehouseId
	 * @param deliveryId
	 * @param areaId
	 * @param saleOrgId
	 * @param driverInfoId
	 * @param storeId
	 * @param supplierId
	 * @param organizationId
	 * @param flag           0:大自然，1:天加
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(Shipping shipping, Long warehouseId, Long deliveryId, Long areaId,
										Long saleOrgId, Long driverInfoId, Long storeId, Long supplierId, Long organizationId, Long sbuId,
										Long regionalManagerId, Integer flag, Long transportTypeId,Long outOfTheWarehouseTypeId) {

		//判断发货单中的产品与产品资料中的转换率是否一样
//		for (ShippingItem shippingItem:shipping.getShippingItems()){
//			if(shippingItem.getProduct()!=null) {
//				Product product = productService.find(shippingItem.getProduct().getId());
//				if (shippingItem.getBranchPerBox().compareTo(product.getBranchPerBox()) != 0) {
//					ExceptionUtil.throwServiceException("发货单中的【" + product.getVonderCode() + "】产品与产品资料的转换率不相符");
//				}
//			}
//		}
		if (ConvertUtil.isEmpty(shipping.getGlDate())) {
			return error("请输入GL日期");
		}
		// 仓库
		Warehouse warehouse = warehouseBaseService.find(warehouseId);
		if (ConvertUtil.isEmpty(warehouse)) {
			return error("15141");
		}
		shipping.setWarehouse(warehouse);
		// 客户
		Store store = storeBaseService.find(storeId);
		if (ConvertUtil.isEmpty(store)) {
			// 请选择客户
			return error("15135");
		}
		shipping.setStore(store);
		// SBU
		Sbu sbu = sbuService.find(sbuId);
		if (!ConvertUtil.isEmpty(sbu)) {
			shipping.setSbu(sbu);
		}
		// 物流快递
		DeliveryCorp delivery = deliveryCorpBaseService.find(deliveryId);
		if (!ConvertUtil.isEmpty(delivery)) {
			shipping.setDelivery(delivery);
		}
		// 地区
		Area area = areaBaseService.find(areaId);
		if (ConvertUtil.isEmpty(area)) {
			return error("请选择收货地区");
		}
		shipping.setArea(area);
		if (ConvertUtil.isEmpty(shipping.getAddress())) {
			// 请填写收货人地址
			return error("15168");
		}
		if (ConvertUtil.isEmpty(shipping.getConsignee())) {
			// 请填写收货人
			return error("15167");
		}
		if (ConvertUtil.isEmpty(shipping.getPhone())) {
			// 请填写收货人电话
			return error("15169");
		}
		// 车主信息
		DriverInfo driverInfo = driverInfoBaseService.find(driverInfoId);
		if (!ConvertUtil.isEmpty(driverInfo)) {
			shipping.setDriverInfo(driverInfo);
		}
		// 机构
		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		if (!ConvertUtil.isEmpty(saleOrg)) {
			shipping.setSaleOrg(saleOrg);
		}
		// 区域经理
		StoreMember regionalManager = storeMemberBaseService.find(regionalManagerId);
		if (!ConvertUtil.isEmpty(regionalManager)) {
			shipping.setRegionalManager(regionalManager);
		}
		// 经营组织
		Organization organization = organizationService.find(organizationId);
		if (!ConvertUtil.isEmpty(organization)) {
			shipping.setOrganization(organization);
		}
		// 发运方式
		if (ConvertUtil.isEmpty(shipping.getSmethod())) {
			return error("请选择发运方式");
		}
		// 运输方式
		SystemDict transportType = systemDictService.find(transportTypeId);
		if (!ConvertUtil.isEmpty(transportType)) {
			shipping.setTransportType(transportType);
		}
		// 出货类型
		SystemDict outOfTheWarehouseType = systemDictService.find(outOfTheWarehouseTypeId);
		if (!ConvertUtil.isEmpty(outOfTheWarehouseType)) {
			shipping.setOutOfTheWarehouseType(outOfTheWarehouseType);
		}

		shippingService.save(shipping, warehouse);

		// 2019-05-16 冯旗 发货单保存后跳转view页面
		return success().addObjX(shipping.getId());
	}

	/**
	 * 更新
	 *
	 * @param shipping
	 * @param warehouseId
	 * @param deliveryId
	 * @param areaId
	 * @param saleOrgId
	 * @param driverInfoId
	 * @param supplierId
	 * @param organizationId
	 * @param flag           0:大自然，1:天加
	 * @return
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(Shipping shipping, Long warehouseId, Long deliveryId, Long areaId,
										  Long saleOrgId, Long driverInfoId, Long supplierId, Long organizationId, Long sbuId, Long regionalManagerId,
										  Integer flag, Long transportTypeId,Long outOfTheWarehouseTypeId) {

		//判断发货单中的产品与产品资料中的转换率是否一样
//		for (ShippingItem shippingItem:shipping.getShippingItems()){
//			if(shippingItem.getProduct() !=null) {
//				Product product = productService.find(shippingItem.getProduct().getId());
//				if (shippingItem.getBranchPerBox().compareTo(product.getBranchPerBox()) != 0) {
//					ExceptionUtil.throwServiceException("发货单中的【" + product.getVonderCode() + "】产品与产品资料的转换率不相符");
//				}
//			}
//		}
		if (shipping.getGlDate() == null) {
			return error("请输入GL时间");
		}
		Warehouse warehouse = warehouseBaseService.find(warehouseId);
		DeliveryCorp delivery = deliveryCorpBaseService.find(deliveryId);
		Area area = areaBaseService.find(areaId);
		if (area == null) {
			return error("请选择收货地区");
		}
		if (ConvertUtil.isEmpty(shipping.getAddress())) {
			// 请填写收货人地址
			return error("15168");
		}
		if (ConvertUtil.isEmpty(shipping.getConsignee())) {
			// 请填写收货人
			return error("15167");
		}
		if (ConvertUtil.isEmpty(shipping.getPhone())) {
			// 请填写收货人电话
			return error("15169");
		}
		List<ShippingItem> shippingItems = shipping.getShippingItems();
		if (shippingItems.isEmpty()) {
			// 请添加发货产品
			return error("15152");
		}
		if (shipping.getSmethod() == null && shipping.getSmethod() != "普通发运" && shipping.getSmethod() != "直接发运") {
			return error("请选择发运方式");
		}
		shipping.setDriverInfo(driverInfoBaseService.find(driverInfoId));
		shipping.setRegionalManager(storeMemberBaseService.find(regionalManagerId));
		// 运输方式
		if (!ConvertUtil.isEmpty(transportTypeId)) {
			SystemDict transportType = systemDictService.find(transportTypeId);
			shipping.setTransportType(transportType);
		}
		// 出货类型
		SystemDict outOfTheWarehouseType = systemDictService.find(outOfTheWarehouseTypeId);
		if (!ConvertUtil.isEmpty(outOfTheWarehouseType)) {
			shipping.setOutOfTheWarehouseType(outOfTheWarehouseType);
		}
		shippingService.update(shipping, warehouse, delivery, area, sbuId, flag);

		return success();
	}

	/**
	 * 流程审核
	 *
	 * @param ids
	 * @return
	 * @throws Exception
	 * @throws Exception
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkWf(Long[] ids, Long objConfId) throws Exception {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		shippingService.checkWf(ids, objConfId);
		return success();
	}

	/**
	 * 流程审核
	 *
	 * @param id
	 * @return
	 * @throws Exception
	 * @throws Exception
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check(Long id) throws Exception {

		shippingService.checkShipping(id);
		return success();
	}

	@RequestMapping(value = "/checkPdf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkPdf(Long ids, Long sign) throws Exception {
		Shipping shipping = shippingService.find(ids);
		TriplicateForm triplicateForm = null;
		if (shipping == null)
			return error("发货单不存在！");
		if (shipping.getStatus() == 0 || shipping.getStatus() == 2) {
			return error("发货单未审核或已作废");
		} else {
			Setting setting = SettingUtils.get();
			triplicateForm = shippingService.createTriplicateForm(setting, shipping, sign);
		}

		return success(triplicateForm.getUrl());
	}

	@RequestMapping(value = "/create", method = RequestMethod.GET)
	public @ResponseBody ResultMsg create(Long id) throws Exception {
		String url = shippingService.createPdfTest(id);
		return success(url);
	}

	/**
	 * 作废
	 *
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancel(Long[] ids) throws Exception {
		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		shippingService.cancel(ids);

		return success();
	}

	/**
	 * 行作废
	 *
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/cancelItem", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancelItem(Long[] ids, Long shippingItemId) throws Exception {
		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		for (int i = 0; i < ids.length; i++) {
			Shipping shipping = shippingService.find(ids[i]);
			if (shipping != null && 2 == shipping.getStatus()) {
				return error("操作失败!发货通知单已作废!");
			}
		}
		shippingService.cancelItem(ids, shippingItemId);

		return success();
	}

	/**
	 * 驳回
	 *
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/reject", method = RequestMethod.POST)
	public @ResponseBody ResultMsg reject(Long[] ids) throws Exception {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		shippingService.reject(ids);
		return success();
	}

	/**
	 * 出仓
	 * @param itemIds
	 * @param orders
	 * @param quantitys
	 * @param isIgnoreStock
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/out", method = RequestMethod.POST)
	public @ResponseBody ResultMsg out(Long[] itemIds, Long[] orders, BigDecimal[] quantitys, Boolean isIgnoreStock)
			throws Exception {

		String orderIds = "";
		// List<ShippingItem> shippingItems = shipping.getShippingItems();
		// for (int i = 0; i < shippingItems.size(); i++) {
		// ShippingItem shippingItem = shippingItems.get(i);
		// if (i == shippingItems.size() - 1) {
		// orderIds += shippingItem.getOrder().getId();
		// }
		// else {
		// orderIds += shippingItem.getOrder().getId() + ",";
		// }
		// }

		for (int i = 0; i < orders.length; i++) {
			if (orders[i] != null) {
				orderIds += "," + orders[i];
			}
		}
		orderIds = orderIds.substring(1);
		shippingService.out(orderIds, itemIds, quantitys, isIgnoreStock);

		return success();
	}

	// 锁库查询
	@RequestMapping(value = "/lock_warehouse_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg lockWarehouse(String shippingSn) throws Exception {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Map<String, String>> lockWarehouseList = null;
		List<Map<String, String>> newlockWarehouseList = new ArrayList<Map<String, String>>();
		List<Map<String, String>> lwList = new ArrayList<Map<String, String>>();
		String jsonPage = null;
		if ("nature".equals(companyInfo.getCompany_code())) {
			// 存值步骤
			HashMap<String, Object> params = null;
			params = new HashMap<String, Object>();
			params.put("ITEM_CODE", null);
			params.put("CUST_PO_NUMBER", null);
			// 调用接口查询
			lockWarehouseList = new NatureLockWarehouseOnHandQty().lockWarehouse(params);
			if (lockWarehouseList != null) {
				newlockWarehouseList.addAll(lockWarehouseList);
			}
		}

		if (newlockWarehouseList != null && newlockWarehouseList.size() > 0) {
			HashSet h = new HashSet(newlockWarehouseList);
			for (int i = 0; i < newlockWarehouseList.size(); i++) {

			}

			jsonPage = JsonUtils.toJson(lwList);
		} else {
			newlockWarehouseList = new ArrayList<Map<String, String>>();
			jsonPage = JsonUtils.toJson(newlockWarehouseList);
		}
		return success(jsonPage);
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/shipping_operate_list", method = RequestMethod.GET)
	public String shipping_operate_list(Long[] ids, BigDecimal[] quantity, BigDecimal[] boxQuantity,
										BigDecimal[] branchQuantity, BigDecimal[] closedQuantity, Long warehouseId, Long saleOrgId, Long areaId,
										Long[] supplierId, String[] supplierName, String treePath, String address, String consignee, String phone,
										Long storeId, Integer pageType, String addressOutTradeNo, Long sbuId, ModelMap model, Long menuId,
										Long orderIndex) {

		SimpleDateFormat form = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("GLDate", form.format(new Date()));
		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods", shippingMethodBaseService.findList(null, filters, null));
		model.addAttribute("ids", ids);
		model.addAttribute("quantity", quantity);
		model.addAttribute("boxQuantity", boxQuantity);
		model.addAttribute("branchQuantity", branchQuantity);
		model.addAttribute("closedQuantity", closedQuantity);
		if (!ConvertUtil.isEmpty(warehouseId)) {
			Warehouse warehouse = warehouseService.find(warehouseId);
			model.addAttribute("warehouse", warehouse);
		}
		model.addAttribute("areaId", areaId);
		model.addAttribute("treePath", treePath);
		model.addAttribute("area", areaBaseService.find(areaId));
		model.addAttribute("address", address);
		model.addAttribute("addressOutTradeNo", addressOutTradeNo);
		model.addAttribute("consignee", consignee);
		model.addAttribute("phone", phone);
		model.addAttribute("store", storeBaseService.find(storeId));
		model.addAttribute("saleOrg", saleOrgBaseService.find(saleOrgId));
		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
		if (pageType != null) {
			String typeName = "";
			if (pageType == 1) {
				typeName = "销售发货";
			} else if (pageType == 2) {
				typeName = "销售退货";
			}
			model.addAttribute("pageType", pageType);
			model.addAttribute("typeName", typeName);
		}

		if (supplierId != null && supplierName != null) {
			model.addAttribute("supplierId", supplierId.length > 0 ? supplierId[0] : null);
			model.addAttribute("supplierName", supplierName.length > 0 ? supplierName[0] : null);
			Store supplier = storeBaseService.find(supplierId[0]);
			if (supplier != null) {
				List<Filter> fis = new ArrayList<Filter>();
				fis.add(Filter.eq("store", supplier));
				List<WarehouseStore> warehouseStores = warehouseStoreBaseService.findList(null, fis, null);
				if (warehouseStores != null && warehouseStores.size() > 0) {
					model.addAttribute("warehouseStore", warehouseStores.get(0));
				}
			}
		}
		if (warehouseId != null) {
			Warehouse warehouse = warehouseBaseService.find(warehouseId);
			model.addAttribute("smethods", warehouse.getWarehouseSmethodList());
		}
		List<Long> orderIds = new ArrayList<Long>();
		String orderMemo = "";
		for (Long id : ids) {
			OrderItem orderItem = orderItemService.find(id);
			Order order = orderItem.getOrder();

			Long oid = order == null ? null : order.getId();
			if (oid != null && !orderIds.contains(oid)) {
				orderIds.add(oid);
				if (order.getMemo() != null) {
					orderMemo = order.getMemo();
				}
			}

			model.addAttribute("order", order);
			String contractSn = order.getContractSn();
			filters.clear();
			filters.add(Filter.eq("contract_no", contractSn));

			List<Map<String, Object>> costs = costService.findListByBillCode(order.getSn(), 0);
			model.addAttribute("costJson", JsonUtils.toJson(costs));

			List<Map<String, Object>> offsiteBranchs = offsiteBranchService.findListByBillCode(order.getSn(), 0);
			model.addAttribute("offsiteBranchJson", JsonUtils.toJson(offsiteBranchs));
		}
		int useLockStock = Integer.parseInt(SystemConfig.getConfig("useLockStock", WebUtils.getCurrentCompanyInfoId()));
		model.addAttribute("useLockStock", useLockStock);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null, filters, null);
		model.addAttribute("organizations", organizations);
		model.addAttribute("orderMemo", orderMemo);

		// sbu
		Sbu sbua = sbuService.find(sbuId);
		model.addAttribute("sbua", sbua);

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("isMember", storeMember.getMemberType());
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		model.addAttribute("sbus", sbus);

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}

		// 订单行是否展示金额 0不展示 非0展示
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles", WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount);
		} catch (RuntimeException e) {

		}

		// 运输方式
		filters.clear();
		filters.add(Filter.eq("code", "transportType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> transportTypeList = systemDictService.findList(null, filters, null);
		model.addAttribute("transportTypeList", transportTypeList);

		// 是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(linkStockValue)) {
			Integer linkStock = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}

		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 发运管理是否启用库存查询 0不展示、1展示
		Integer shipmentStockQueryRoles = roleJurisdictionUtil.getRoleCount("shipmentStockQueryRoles");
		model.addAttribute("shipmentStockQueryRoles", shipmentStockQueryRoles);

		// 角色是否能显示修改平台结算价格  1 可显示修改    非1  不可显示修改
		try {
			String value = SystemConfig.getConfig("editSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int editSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					editSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("editSaleOrgPrice", editSaleOrgPrice); }
		catch (RuntimeException e) {
		}

		// 角色是否能查看平台产品价格  1 可修改     非1  不可修改
		try {
			String value = SystemConfig.getConfig("seeSaleOrgPriceRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> lists = Arrays.asList(role);
			int seeSaleOrgPrice = 0;
			for (PcUserRole userRole : userRoles) {
				if (lists.contains(userRole.getPcRole().getName())) {
					seeSaleOrgPrice++;
					break;
				}
			}
			model.addAttribute("seeSaleOrgPrice", seeSaleOrgPrice);
		}
		catch (RuntimeException e) {
		}

		return "/b2b/shipping/shipping_operate_list";
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/shipping_operate_list/{code}", method = RequestMethod.GET)
	public String shipping_operate_list(@PathVariable String code, Long[] ids, BigDecimal[] quantity, Long warehouseId,
										String warehouseName, Long saleOrgId, Long areaId, Long[] supplierId, String[] supplierName,
										Long organizationId, String organizationName, String treePath, String address, String consignee,
										String phone, Long storeId, ModelMap model) {
		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods", shippingMethodBaseService.findList(null, filters, null));
		model.addAttribute("ids", ids);
		model.addAttribute("quantity", quantity);
		model.addAttribute("warehouseId", warehouseId);
		model.addAttribute("warehouseName", warehouseName);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("organizationName", organizationName);
		model.addAttribute("areaId", areaId);
		model.addAttribute("treePath", treePath);
		model.addAttribute("address", address);
		model.addAttribute("consignee", consignee);
		model.addAttribute("phone", phone);
		model.addAttribute("store", storeBaseService.find(storeId));
		model.addAttribute("saleOrg", saleOrgBaseService.find(saleOrgId));
		if (supplierId != null && supplierName != null) {
			model.addAttribute("supplierId", supplierId.length > 0 ? supplierId[0] : null);
			model.addAttribute("supplierName", supplierName.length > 0 ? supplierName[0] : null);
			Store supplier = storeBaseService.find(supplierId[0]);
			if (supplier != null) {
				List<Filter> fis = new ArrayList<Filter>();
				fis.add(Filter.eq("store", supplier));
				List<WarehouseStore> warehouseStores = warehouseStoreBaseService.findList(null, fis, null);
				if (warehouseStores != null && warehouseStores.size() > 0) {
					model.addAttribute("warehouseStore", warehouseStores.get(0));
				}
			}
		}

		int useLockStock = Integer.parseInt(SystemConfig.getConfig("useLockStock", WebUtils.getCurrentCompanyInfoId()));
		model.addAttribute("useLockStock", useLockStock);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null, filters, null);
		model.addAttribute("organizations", organizations);
		model.addAttribute("code", code);
		model.addAttribute("paymentMethods", paymentMethodBaseService.findAll());
		return "/" + code + "/b2b/shipping/shipping_operate_list";
	}

	/**
	 * 发货明细列表
	 */
	/*@RequestMapping(value = "/shipping_operate_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg shipping_operate_list_data(Long[] ids) {

		Map<String, Object> map = new HashMap<String, Object>();
		List<Map<String, Object>> orderItems = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> shippingItems = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < ids.length; i++) {
			Map<String, Object> item = shippingService.findOperateItemNew(ids[i]);
			item.put("line_no", i + 1);
			Long productId = 0L;
			if (!ConvertUtil.isEmpty(item.get("product")))
				productId = Long.parseLong(item.get("product").toString());
			item.put("bom_flag", 0);
			orderItems.add(item);
			shippingItems.add(item);
		}
		map.put("orderItems", orderItems);
		map.put("shippingItems", shippingItems);
		String jsonPage = JsonUtils.toJson(map);

		return success(jsonPage);
	}*/


	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/shipping_operate_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg shipping_operate_list_data(Long[] ids, BigDecimal[] quantity,
										 BigDecimal[] boxQuantity, BigDecimal[] branchQuantity,
										 BigDecimal[] closedQuantity) {
		Map<String, Object> map = new HashMap<String, Object>();
		List<Map<String, Object>> orderItems = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> shippingItems = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < ids.length; i++) {
			Map<String, Object> item = shippingService.findOperateItem(ids[i],
					quantity == null ? null :quantity[i],
					boxQuantity == null ? null : boxQuantity[i],
					branchQuantity == null ? null : branchQuantity[i],
					closedQuantity == null ? null : closedQuantity[i]);
			item.put("line_no", i + 1);
			Long productId = 0L;
			if (!ConvertUtil.isEmpty(item.get("product")))
				productId = Long.parseLong(item.get("product").toString());
			item.put("bom_flag", 0);
			orderItems.add(item);
			shippingItems.add(item);
		}
		map.put("orderItems", orderItems);
		map.put("shippingItems", shippingItems);
		String jsonPage = JsonUtils.toJson(map);

		return success(jsonPage);
	}



	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/order_item_list", method = RequestMethod.GET)
	public String order_item_list(ModelMap model, Integer pageType, Long menuId, Long userId) {

		boolean isAdmin = WebUtils.isAdmin();
		Store store = null;
		if (isAdmin) {
			List<Store> stores = storeBaseService.findList(1, null, null);
			if (!stores.isEmpty())
				store = stores.get(0);
		} else {
			Member member = storeMemberBaseService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
			if (!storeMembers.isEmpty()) {
				store = storeMembers.get(0).getStore();
			}
		}
		if (store != null) {
			model.addAttribute("storeId", store.getId());
			model.addAttribute("storeName", store.getName());
		}
		model.addAttribute("isMember", storeMemberBaseService.getCurrent().getMemberType());

		/* 获取当前登陆人的名字 */
		StoreMember storeMember = storeMemberService.getCurrent();
		String storeMemberName=storeMember.getName();
		model.addAttribute("storeMemberName",storeMemberName);

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}
		model.addAttribute("pageType", pageType);
		model.addAttribute("menuId", menuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);

		/**
		 * 用户经营组织权限
		 */
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		model.addAttribute("storeMemberOrganizationList", storeMemberOrganizationList);

		return "/b2b/shipping/order_item_list";
	}

	/**
	 * 销售退货列表
	 */
	@RequestMapping(value = "/sale_return_list", method = RequestMethod.GET)
	public String sale_return_list(ModelMap model, Integer pageType) {

		boolean isAdmin = WebUtils.isAdmin();
		Store store = null;
		if (isAdmin) {
			List<Store> stores = storeBaseService.findList(1, null, null);
			if (!stores.isEmpty())
				store = stores.get(0);
		} else {
			Member member = storeMemberBaseService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
			if (!storeMembers.isEmpty()) {
				store = storeMembers.get(0).getStore();
			}
		}
		if (store != null) {
			model.addAttribute("storeId", store.getId());
			model.addAttribute("storeName", store.getName());
		}
		model.addAttribute("isMember", storeMemberBaseService.getCurrent().getMemberType());

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}
		model.addAttribute("pageType", pageType);
		return "/b2b/shipping/sale_return_list";
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/order_item_list/{code}", method = RequestMethod.GET)
	public String order_item_list(@PathVariable String code, ModelMap model, Long menuId, Long userId) {

		boolean isAdmin = WebUtils.isAdmin();
		Store store = null;
		if (isAdmin) {
			List<Store> stores = storeBaseService.findList(1, null, null);
			if (!stores.isEmpty())
				store = stores.get(0);
		} else {
			Member member = storeMemberBaseService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
			if (!storeMembers.isEmpty()) {
				store = storeMembers.get(0).getStore();
			}
		}
		if (store != null) {
			model.addAttribute("storeId", store.getId());
			model.addAttribute("storeName", store.getName());
		}
		model.addAttribute("code", code);
		return "/" + code + "/b2b/shipping/order_item_list";
	}

	/**
	 * 发货明细列表数据
	 */
	@RequestMapping(value = "/order_item_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg order_item_list_data(String orderSn, String outTradeNo, String consignee,
														Long storeId, Long warehouseId, String sn, String name, String vonderCode, Long supplierId,
														String storeMemberName, Long[] saleOrgId, String model, Pageable pageable, Integer[] status,
														String moistureContent, String colourNumber, String batch, Integer pageType, Long[] warehouseIds,
														Long[] organizationIds, Long[] sbuId) {

		// 2019-05-16 增加创建人，多机构查询
		Page<Map<String, Object>> page = orderService.findItemPage(orderSn, outTradeNo, storeId, warehouseId, sn, name,
				consignee, vonderCode, model, true, new Integer[] { 6 }, false, supplierId, storeMemberName, saleOrgId,
				false, pageable, status, moistureContent, colourNumber, batch, pageType, warehouseIds, organizationIds,
				null, sbuId);

		List<Map<String, Object>> orderItems = page.getContent();

		for (Iterator<Map<String, Object>> iterator = orderItems.iterator(); iterator.hasNext();) {
			Map<String, Object> orderItem = iterator.next();
			BigDecimal closedQuantityNb = new BigDecimal(
					orderCloseService.findClosedQuantityByOrderItemId(Long.valueOf(orderItem.get("id").toString()))
							.get("closed_quantity_nb").toString());
			BigDecimal closedQuantity = new BigDecimal(
					orderCloseService.findClosedQuantityByOrderItemId(Long.valueOf(orderItem.get("id").toString()))
							.get("closed_quantity").toString());
			BigDecimal branch_quantity = new BigDecimal(
					orderItem.get("quantity") == null ? "0" : orderItem.get("quantity").toString());
			BigDecimal ship_branch_quantity = new BigDecimal(
					orderItem.get("ship_quantity") == null ? "0" : orderItem.get("ship_quantity").toString());
			if (branch_quantity.compareTo(ship_branch_quantity.add(closedQuantityNb)) < 1) {
				iterator.remove();
				continue;
			} else {
				orderItem.put("closed_quantity_nb", closedQuantityNb);
				orderItem.put("closed_quantity", closedQuantity);
			}
		}

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 发货明细列表数据
	 */
	@RequestMapping(value = "/order_item_list_data_code", method = RequestMethod.POST)
	public @ResponseBody ResultMsg order_item_list_data_code(String orderSn, String outTradeNo, String consignee,
															 Long storeId, Long warehouseId, String sn, String name, String vonderCode, Long supplierId, String model,
															 Pageable pageable) {

		Page<Map<String, Object>> page = orderService.findItemPage(orderSn, outTradeNo, storeId, warehouseId, sn, name,
				consignee, vonderCode, model, true, new Integer[] { 6 }, false, supplierId, null, null, false, pageable,
				null, null, null, null, null, null, null, null,null);
		List<Map<String, Object>> orderItems = page.getContent();

		for (Iterator<Map<String, Object>> iterator = orderItems.iterator(); iterator.hasNext();) {
			Map<String, Object> orderItem = iterator.next();
			BigDecimal closedQuantity = new BigDecimal(
					orderCloseService.findClosedQuantityByOrderItemId(Long.valueOf(orderItem.get("id").toString()))
							.get("closed_quantity_nb").toString());
			BigDecimal branch_quantity = new BigDecimal(
					orderItem.get("quantity") == null ? "0" : orderItem.get("quantity").toString());
			BigDecimal ship_branch_quantity = new BigDecimal(
					orderItem.get("ship_quantity") == null ? "0" : orderItem.get("ship_quantity").toString());
			if (branch_quantity.compareTo(ship_branch_quantity.add(closedQuantity)) < 1) {
				iterator.remove();
				continue;
			} else {
				orderItem.put("closed_quantity", closedQuantity);
			}
		}

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/check_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_data(Long[] ids, BigDecimal[] quantity) {

		Long warehouseId = 0L;
		Long storeId = 0L;
		Long saleOrgId = 0L;
		Long sbuId = 0L;
		Long businessTypeId = 0L;
		String address = "";
		String pConsignee = "";
		String pPhone = "";
		String addressOutTradeNo = "";
		HashMap<String, Object> map = new HashMap<String, Object>();
		for (int i = 0; i < ids.length; i++) {
			OrderItem orderItem = orderItemService.find(ids[i]);
			Order order = orderItem.getOrder();
			Warehouse warehouse = null;
			if (!ConvertUtil.isEmpty(order.getWarehouse()) && !ConvertUtil.isEmpty(order.getWarehouse().getId())) {
				warehouse = warehouseService.find(order.getWarehouse().getId());
				if (ConvertUtil.isEmpty(warehouse)) {
					return error("仓库不能为空");
				}
				if (warehouseId.intValue() == 0) {
					warehouseId = warehouse.getId();
					map.put("warehouseId", warehouseId);
					if (!ConvertUtil.isEmpty(warehouse.getTypeSystemDict())
							&& !ConvertUtil.isEmpty(warehouse.getTypeSystemDict().getId())) {
						SystemDict typeSystemDict = systemDictService.find(warehouse.getTypeSystemDict().getId());
						if (ConvertUtil.isEmpty(typeSystemDict)) {
							return error("仓库类型不能为空");
						}
						if (ConvertUtil.isEmpty(typeSystemDict.getFlag())) {
							return error("请管理员维护仓库类型标识");
						}
					} else {
						return error("仓库类型不能为空");
					}
				}
			} else {
				return error("仓库不能为空");
			}
			if (warehouseId.intValue() != warehouse.getId().intValue()) {
				// 所选订单明细仓库不一致
				return error("15171");
			}
			if (storeId.intValue() == 0) {
				storeId = order.getStore().getId();
			}
			if (storeId.intValue() != order.getStore().getId().intValue()) {
				// 所选订单客户不一致
				return error("15174");
			}
			if (saleOrgId.intValue() == 0) {
				saleOrgId = order.getSaleOrg().getId();
			}
			if (saleOrgId.intValue() != order.getSaleOrg().getId().intValue()) {
				return error("所选订单机构不一致");
			}
			if (businessTypeId.intValue() == 0) {
				businessTypeId = order.getBusinessType().getId();
			}
			if (businessTypeId.intValue() != order.getBusinessType().getId().intValue()) {
				return error("所选订单业务类型不一致");
			}
			if (sbuId.intValue() == 0 && !ConvertUtil.isEmpty(order.getSbu())) {
				sbuId = order.getSbu().getId();
			}
			if (!ConvertUtil.isEmpty(order.getSbu()) && sbuId.intValue() != order.getSbu().getId().intValue()) {
				return error("所选SBU不一致");
			}
			Area area = order.getArea();
			String addr = order.getAddress() == null ? "" : order.getAddress();
			String consignee = order.getConsignee() == null ? "" : order.getConsignee();
			String phone = order.getPhone() == null ? "" : order.getPhone();
			if (ConvertUtil.isEmpty(address)) {
				address = addr;
			}
			if (!address.equals(addr)) {
				// 所选订单明细收货地址不一致
				return error("15172");
			}
			if (ConvertUtil.isEmpty(pConsignee)) {
				pConsignee = consignee;
			}
			if (!pConsignee.equals(consignee)) {
				// 所选订单明细收货人不一致
				return error("15176");
			}
			if (ConvertUtil.isEmpty(pPhone)) {
				pPhone = phone;
			}
			if (!pPhone.equals(phone)) {
				// 所选订单明细收货人电话不一致
				return error("15177");
			}
			if (!ConvertUtil.isEmpty(area)) {
				if (!map.containsKey("areaId")) {
					map.put("areaId", area.getId());
				}
				if (!map.containsKey("treePath")) {
					map.put("treePath", area.getTreePath());
				}
			}
			if (ConvertUtil.isEmpty(addressOutTradeNo)) {
				addressOutTradeNo = order.getAddressOutTradeNo();
			}
		}
		map.put("address", address);
		map.put("sbuId", sbuId);
		map.put("consignee", pConsignee);
		map.put("phone", pPhone);
		map.put("storeId", storeId);
		map.put("saleOrgId", saleOrgId);
		map.put("addressOutTradeNo", addressOutTradeNo);

		return success(JsonUtils.toJson(map));
	}

	/**
	 * 替代件列表
	 */
	@RequestMapping(value = "/replace_product_list", method = RequestMethod.GET)
	public String replace_product_list(Long suiteItemId, Integer multi, ModelMap model) {

		model.addAttribute("suiteItemId", suiteItemId);
		model.addAttribute("multi", multi);
		return "/b2b/shipping/replace_product_list";
	}

	/**
	 * 发货单生成明细
	 */
	@RequestMapping(value = "/to_condition_export_create", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExportCreate(String orderSn, String outTradeNo,
																		   Long storeId, Long warehouseId, String sn, String name, String vonderCode, Long supplierId, String model,
																		   Pageable pageable, String moistureContent, String colourNumber, String batch, Long[] warehouseIds,
																		   Long[] organizationIds) {

		Integer size = orderService.countCreate(orderSn, outTradeNo, storeId, warehouseId, sn, name, vonderCode, model,
				true, new Integer[] { 6 }, false, supplierId, pageable, moistureContent, colourNumber, batch,
				warehouseIds, organizationIds);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}







	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(String sn, String orderSn, String outTradeNo,
																	 String trackingNo, Integer[] status, Long[] warehouseIds, String warehouseName, Long deliveryCorpId,
																	 Integer warehouseType, Long[] storeId, String firstTime, String lastTime, Long sbuId, Pageable pageable,
																	 ModelMap model, String colourNumber, String moistureContent, String batch, Long[] organizationIds) {

		Integer size = shippingService.count(sn, orderSn, outTradeNo, trackingNo, status, warehouseIds, deliveryCorpId,
				warehouseType, storeId, firstTime, lastTime, null, null, null, sbuId, colourNumber, moistureContent,
				batch, organizationIds);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}


	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	/**
	 * 查看物流信息
	 *
	 * @throws Exception
	 */
	@RequestMapping(value = "/queryDelivery", method = RequestMethod.GET)
	public String queryDelivery(Long id, ModelMap model) throws Exception {
		Shipping shipping = shippingService.find(id);
		String trackingNo = shipping.getTrackingNo();

		HashMap<String, Object> bill = new HashMap<String, Object>();
		DeliveryCorp delivery = shipping.getDelivery();
		String waybillSn = (delivery != null) ? delivery.getWaybillSn() : null;
		if (delivery == null) {
			bill.put("msg", "未维护物流快递");
		} else if (waybillSn == null || "".equals(waybillSn)) {
			bill.put("msg", "未维护物流查询编码");
		} else {
			DeliveryUtil deliveryUtil = new DeliveryUtil();
			bill = deliveryUtil.getDeliveryInfo(waybillSn, trackingNo);
		}
		model.addAttribute("bill", bill);
		model.addAttribute("shipping", shipping);
		return "/b2b/shipping/queryDelivery";
	}

	@RequestMapping(value = "/getStock", method = RequestMethod.POST)
	public @ResponseBody ResultMsg getStock(Long[] productIds, Long warehouseId, ModelMap model) {
		List<Map<String, Object>> items = stockService.findStockList(warehouseId, productIds);
		String jsonPage = JsonUtils.toJson(items);

		return success(jsonPage);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_change_tb", method = RequestMethod.GET)
	public String list_change_tb(Pageable pageable, ModelMap model) {

		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		return "/b2b/shipping/list_change_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_change", method = RequestMethod.GET)
	public String list_change(Pageable pageable, ModelMap model) {

		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		return "/b2b/shipping/list_change";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_change_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_change_data(String sn, String orderSn, String outTradeNo, String trackingNo,
													Integer[] status, Long[] warehouseId, String productModel, String warehouseName, Long deliveryCorpId,
													Integer warehouseType, Long[] storeId, Long supplierId, String firstTime, String lastTime,
													Pageable pageable, ModelMap model) {

		int orderType = 2;
		if (status == null || status.length == 0)
			status = new Integer[] { 1, 2, 3, 4 };
		Page<Map<String, Object>> page = purchaseShippingService.findPage(sn, orderSn, outTradeNo, trackingNo, status,
				new Integer[] { 1 }, warehouseId, deliveryCorpId, warehouseType, storeId, supplierId, firstTime,
				lastTime, null, orderType, null, pageable);

		List<Map<String, Object>> shippings = page.getContent();

		if (!shippings.isEmpty()) {
			String ids = "";
			for (int i = 0; i < shippings.size(); i++) {
				Map<String, Object> map = shippings.get(i);
				if (i == shippings.size() - 1) {
					ids += map.get("id");
				} else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> shippingItems = purchaseShippingService.findShippingItemListByShippingId(ids);
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : shippings) {
				items = new ArrayList<Map<String, Object>>();
				String shippingId = map.get("id").toString();
				for (Map<String, Object> itemMap : shippingItems) {
					String oid = itemMap.get("shipping").toString();
					if (shippingId.equals(oid)) {
						items.add(itemMap);
					}
				}
				map.put("shipping_items", items);
			}
		}
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 运单补录
	 *
	 * @param ids
	 * @return
	 */
	@RequestMapping(value = "/change", method = RequestMethod.POST)
	public @ResponseBody ResultMsg change(Long[] ids, Date[] shippingTimes, String[] trackingNos, Long[] deliveryIds,
										  BigDecimal[] freights) {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		if ((shippingTimes == null || shippingTimes.length == 0) && (trackingNos == null || trackingNos.length == 0)
				&& (deliveryIds == null || deliveryIds.length == 0) && (freights == null || freights.length == 0)) {
			return error("请填写信息");
		}
		purchaseShippingService.change(ids, shippingTimes, trackingNos, deliveryIds, freights);
		return success();
	}

	/**
	 * 运单补录 导出
	 */
	@RequestMapping(value = "/to_condition_export_change", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExportChange(String sn, String orderSn, String outTradeNo,
																		   String trackingNo, Integer[] status, Long[] warehouseId, String warehouseName, Long deliveryCorpId,
																		   Integer warehouseType, Long[] storeId, Long supplierId, String firstTime, String lastTime,
																		   String productModel, Pageable pageable, ModelMap model) {

		int orderType = 2;
		if (status == null || status.length == 0)
			status = new Integer[] { 1, 2, 3, 4 };
		Integer size = purchaseShippingService.change_count(sn, orderSn, outTradeNo, trackingNo, status,
				new Integer[] { 1 }, warehouseId, deliveryCorpId, warehouseType, storeId, supplierId, firstTime,
				lastTime, null, pageable, null, null, orderType, productModel);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/condition_export_change", method = RequestMethod.GET)
	public ModelAndView conditionExportChange(String sn, String orderSn, String outTradeNo, String trackingNo,
											  Integer[] status, Long[] warehouseId, String warehouseName, Long deliveryCorpId, Integer warehouseType,
											  Long[] storeId, Long supplierId, String firstTime, String lastTime, String productModel, Pageable pageable,
											  ModelMap model, Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		int orderType = 2;
		if (status == null || status.length == 0)
			status = new Integer[] { 1, 2, 3, 4 };
		List<Map<String, Object>> data = purchaseShippingService.findChangeList(sn, orderSn, outTradeNo, trackingNo,
				status, new Integer[] { 1 }, warehouseId, deliveryCorpId, warehouseType, storeId, supplierId, firstTime,
				lastTime, null, null, page, size, orderType, productModel);

		return getModelAndViewForListChange(data, model);
	}

	/**
	 * 选择导出
	 *
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export_change", method = RequestMethod.GET)
	public ModelAndView selectedExportChange(Long[] ids, ModelMap model) {

		int orderType = 2;
		List<Map<String, Object>> data = purchaseShippingService.findChangeList(null, null, null, null, null, null,
				null, null, null, null, null, null, null, null, ids, null, null, orderType, null);
		return getModelAndViewForListChange(data, model);
	}

	/**
	 * 运单补录 数据
	 */
	public ModelAndView getModelAndViewForListChange(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("status") != null) {
				Integer statuss = (Integer) str.get("status");
				if (statuss == 0) {
					str.put("status_name", "未审核");
				} else if (statuss == 1) {
					str.put("status_name", "已审核");
				} else if (statuss == 2) {
					str.put("status_name", "作废");
				} else if (statuss == 3) {
					str.put("status_name", "部分发货");
				} else if (statuss == 4) {
					str.put("status_name", "完全发货");
				}
			}
			BigDecimal quantity = new BigDecimal(str.get("quantity").toString());
			str.put("quantity", NumberFormat.getInstance().format(quantity));

			if (str.get("volume") != null) {
				BigDecimal volume = new BigDecimal(str.get("volume").toString());
				str.put("volume", NumberFormat.getInstance().format(volume));
			}
			if (str.get("shipping_time") != null) {
				String shipping_time = DateUtil.convert((Date) str.get("shipping_time"));
				if (shipping_time.length() > 19) {
					shipping_time = shipping_time.substring(0, 19);
				}
				str.put("shipping_time", shipping_time);
			}

		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "发货单编号", "运单号", "物流快递", "发货单状态", "仓库", "客户", "产品名称", "产品型号", "产品编码", "销售订单编号", "体积",
				"实际发货数", "收货人", "收货人电话", "收货地址", "配送方式", "运费承担", "备注", "预计到货时间" };

		// 设置单元格取值
		String[] properties = { "shipping_sn", "tracking_no", "delivery_corp_name", "status_name", "warehouse_name",
				"store_name", "name", "model", "vonder_code", "order_sn", "volume", "quantity", "consignee", "phone",
				"address", "shipping_method", "freight_charge_name", "memo", "shipping_time" };
		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}

	@RequestMapping(value = "/saveAttach", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveAttach(Shipping shipping) {
		Shipping pShipping = shippingService.find(shipping.getId());
		if (pShipping.getStatus() == 0) {
			return this.error("发货单未审核，不允许上传附件");
		}
		// 附件
		List<ShippingAttach> shippingAttachs = shipping.getShippingAttachs();
		for (Iterator<ShippingAttach> iterator = shippingAttachs.iterator(); iterator.hasNext();) {
			ShippingAttach shippingAttach = iterator.next();
			if (shippingAttach == null || shippingAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (shippingAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			shippingAttach.setFileName(shippingAttach.getName() + "." + shippingAttach.getSuffix());
			shippingAttach.setShipping(pShipping);
			shippingAttach.setStoreMember(storeMemberBaseService.getCurrent());
		}
		pShipping.getShippingAttachs().clear();
		pShipping.getShippingAttachs().addAll(shippingAttachs);
		shippingService.update(pShipping);
		return success();
	}

	// 获取客户可发货余额
	@RequestMapping(value = "/getBalance", method = RequestMethod.POST)
	public @ResponseBody ResultMsg getBalance(Long storeId, Integer[] type, Pageable pageable, ModelMap model) {

		Object[] args = new Object[] { storeId, type };
		Page<Map<String, Object>> page = storeBalanceService.findPage(pageable, args);
		Map<String, Object> map = new HashMap<String, Object>();
		map = page.getContent().get(0);
		return ResultMsg.success().addObjX(map);
	}

	/**
	 * 大自然生成发货通知选择导出
	 *
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export_create", method = RequestMethod.GET)
	public ModelAndView selectedExportCreate(Long[] ids, Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = orderService.findItemPage(null, null, null, null, null, null, null, null, null,
				true, new Integer[] { 6 }, false, null, null, null, false, pageable, null, null, null, null, null, null,
				null, ids,null);

		List<Map<String, Object>> data = page.getContent();

		return getModelAndViewForCreate(data, model);

	}

	/**
	 * 大自然生成发货通知单条件导出统计数量
	 *
	 * @param orderSn
	 * @param outTradeNo
	 * @param consignee
	 * @param storeId
	 * @param warehouseId
	 * @param sn
	 * @param name
	 * @param vonderCode
	 * @param supplierId
	 * @param storeMemberName
	 * @param saleOrgId
	 * @param model
	 * @param pageable
	 * @param status
	 * @param moistureContent
	 * @param colourNumber
	 * @param batch
	 * @param pageType
	 * @param warehouseIds
	 * @param organizationIds
	 * @return
	 */
	@RequestMapping(value = "/to_condition_export_create_order", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExportCreateOrder(String orderSn, String outTradeNo,
																				String consignee, Long storeId, Long warehouseId, String sn, String name, String vonderCode,
																				Long supplierId, String storeMemberName, Long[] saleOrgId, String model, Pageable pageable,
																				Integer[] status, String moistureContent, String colourNumber, String batch, Integer pageType,
																				Long[] warehouseIds, Long[] organizationIds,Long[] sbuId) {

		Page<Map<String, Object>> page = orderService.findItemPage(orderSn, outTradeNo, storeId, warehouseId, sn, name,
				consignee, vonderCode, model, true, new Integer[] { 6 }, false, supplierId, storeMemberName, saleOrgId,
				false, pageable, status, moistureContent, colourNumber, batch, pageType, warehouseIds, organizationIds,
				null,sbuId);

		List<Map<String, Object>> orderItems = page.getContent();
		for (Iterator<Map<String, Object>> iterator = orderItems.iterator(); iterator.hasNext();) {
			Map<String, Object> orderItem = iterator.next();
			BigDecimal closedQuantityNb = new BigDecimal(
					orderCloseService.findClosedQuantityByOrderItemId(Long.valueOf(orderItem.get("id").toString()))
							.get("closed_quantity_nb").toString());
			BigDecimal branch_quantity = new BigDecimal(
					orderItem.get("quantity") == null ? "0" : orderItem.get("quantity").toString());
			BigDecimal ship_branch_quantity = new BigDecimal(
					orderItem.get("ship_quantity") == null ? "0" : orderItem.get("ship_quantity").toString());
			if (branch_quantity.compareTo(ship_branch_quantity.add(closedQuantityNb)) < 1) {
				iterator.remove();
				continue;
			}
		}

		Integer size = orderItems.size();
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 大自然生成发货通知单条件导出
	 *
	 * @param orderSn
	 * @param outTradeNo
	 * @param consignee
	 * @param storeId
	 * @param warehouseId
	 * @param sn
	 * @param name
	 * @param vonderCode
	 * @param supplierId
	 * @param storeMemberName
	 * @param saleOrgId
	 * @param model
	 * @param status
	 * @param moistureContent
	 * @param colourNumber
	 * @param batch
	 * @param pageType
	 * @param warehouseIds
	 * @param organizationIds
	 * @param modelMap
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/condition_export_create_order", method = RequestMethod.GET)
	public ModelAndView conditionExportCreateOrder(String orderSn, String outTradeNo, String consignee, Long storeId,
												   Long warehouseId, String sn, String name, String vonderCode, Long supplierId, String storeMemberName,
												   Long[] saleOrgId, String model, Integer[] status, String moistureContent, String colourNumber, String batch,
												   Integer pageType, Long[] warehouseIds, Long[] organizationIds, Integer page, ModelMap modelMap,
												   Pageable pageable,Long[] sbuId) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		pageable.setPageSize(size);
		pageable.setPageNumber(page);

		Page<Map<String, Object>> pageMap = orderService.findItemPage(orderSn, outTradeNo, storeId, warehouseId, sn,
				name, consignee, vonderCode, model, true, new Integer[] { 6 }, false, supplierId, storeMemberName,
				saleOrgId, false, pageable, status, moistureContent, colourNumber, batch, pageType, warehouseIds,
				organizationIds, null,sbuId);

		List<Map<String, Object>> mapList = pageMap.getContent();

		for (Iterator<Map<String, Object>> iterator = mapList.iterator(); iterator.hasNext();) {
			Map<String, Object> orderItem = iterator.next();
			BigDecimal closedQuantityNb = new BigDecimal(
					orderCloseService.findClosedQuantityByOrderItemId(Long.valueOf(orderItem.get("id").toString()))
							.get("closed_quantity_nb").toString());
			BigDecimal branch_quantity = new BigDecimal(
					orderItem.get("quantity") == null ? "0" : orderItem.get("quantity").toString());
			BigDecimal ship_branch_quantity = new BigDecimal(
					orderItem.get("ship_quantity") == null ? "0" : orderItem.get("ship_quantity").toString());
			if (branch_quantity.compareTo(ship_branch_quantity.add(closedQuantityNb)) < 1) {
				iterator.remove();
				continue;
			}
		}

		return getModelAndViewForCreate(mapList, modelMap);

	}

	/**
	 * 大自然生成发货通知条件导出
	 * @param sn
	 * @param orderSn
	 * @param outTradeNo
	 * @param trackingNo
	 * @param status
	 * @param warehouseId
	 * @param warehouseName
	 * @param deliveryCorpId
	 * @param warehouseType
	 * @param storeId
	 * @param firstTime
	 * @param lastTime
	 * @param sbuId
	 * @param pageable
	 * @param model
	 * @param page
	 * @param colourNumber
	 * @param moistureContent
	 * @param batch
	 * @param warehouseIds
	 * @param organizationIds
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, String orderSn, String outTradeNo, String trackingNo,
										Integer[] status, Long[] warehouseId, String warehouseName, Long deliveryCorpId, Integer warehouseType,
										Long[] storeId, String firstTime, String lastTime, Long sbuId, Pageable pageable, ModelMap model,
										Integer page, String colourNumber, String moistureContent, String batch, Long[] warehouseIds,
										Long[] organizationIds,String[] statusType) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = shippingService.findItemList(sn, orderSn, outTradeNo, trackingNo, status,
				warehouseIds, deliveryCorpId, warehouseType, storeId, firstTime, lastTime, null, page, size, sbuId,
				colourNumber, moistureContent, batch, organizationIds,statusType);
		return getModelAndViewForList(data, model);
	}

	/**
	 * 大自然发货通知审核选择导出
	 *
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, Long sbuId, ModelMap model) {

		List<Map<String, Object>> data = shippingService.findItemList(null, null, null, null, null, null, null, null,
				null, null, null, ids, null, null, null, null, null, null, null,null);
		return getModelAndViewForList(data, model);
	}

	/** 发货单审核页面的导出大自然 */
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单行是否展示金额 0不展示 非0展示
		int hiddenAmount = 0;
		try {
			String value = SystemConfig.getConfig("hiddenAmountRoles", WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> listString = Arrays.asList(role);
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
		} catch (RuntimeException e) {

		}

		for (Map<String, Object> str : data) {
			if (str.get("status") != null) {
				Integer statuss = (Integer) str.get("status");
				if (statuss == 0) {
					str.put("status_name", "未审核");
				} else if (statuss == 1) {
					str.put("status_name", "已审核");
				} else if (statuss == 2) {
					str.put("status_name", "作废");
				} else if (statuss == 3) {
					str.put("status_name", "部分发货");
				} else if (statuss == 4) {
					str.put("status_name", "完全发货");
				}
			}
			if (str.get("status_type") != null) {
				Integer statusType = (Integer) str.get("status_type");
				if (statusType == 0) {
					str.put("status_type","有效");
				} else if (statusType == 1) {
					str.put("status_type","有效");
				} else if (statusType == 2) {
					str.put("status_type","无效");
				} else if (statusType == 3) {
					str.put("status_type","有效");
				} else if (statusType == 4) {
					str.put("status_type","有效");
				}
			}
			if (str.get("quantity") != null) {
				BigDecimal quantity = new BigDecimal(str.get("quantity").toString());
				str.put("quantity", quantity.setScale(6, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("volume") != null) {
				BigDecimal volume = new BigDecimal(str.get("volume").toString());
				str.put("volume", NumberFormat.getInstance().format(volume));
			}
			if (str.get("branch_quantity") != null) {
				BigDecimal branch_quantity = new BigDecimal(str.get("branch_quantity").toString());
				str.put("branch_quantity", branch_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("box_quantity") != null) {
				BigDecimal box_quantity = new BigDecimal(str.get("box_quantity").toString());
				str.put("box_quantity", box_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("shipped_quantity") != null) {
				BigDecimal shipped_quantity = new BigDecimal(str.get("shipped_quantity").toString());
				str.put("shipped_quantity", shipped_quantity.setScale(6, BigDecimal.ROUND_HALF_UP));
				BigDecimal item_amount = BigDecimal.ZERO;
				if (str.get("order_price") != null) {
					BigDecimal order_price = new BigDecimal(str.get("order_price").toString());
					str.put("order_price", order_price.setScale(2, BigDecimal.ROUND_HALF_UP));
					item_amount = shipped_quantity.multiply(order_price.setScale(2, BigDecimal.ROUND_HALF_UP))
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					str.put("item_amount", item_amount);
				}
				BigDecimal sale_org_amount = BigDecimal.ZERO;
				if (str.get("sale_org_price") != null) {
					BigDecimal sale_org_price = new BigDecimal(str.get("sale_org_price").toString());
					str.put("sale_org_price", sale_org_price.setScale(2, BigDecimal.ROUND_HALF_UP));
					sale_org_amount = shipped_quantity.multiply(sale_org_price.setScale(2, BigDecimal.ROUND_HALF_UP))
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					str.put("sale_org_amount", sale_org_amount);
				}
				str.put("amount_sub", item_amount.subtract(sale_org_amount));
			}
			if (str.get("shipped_branch_quantity") != null) {
				BigDecimal shipped_branch_quantity = new BigDecimal(str.get("shipped_branch_quantity").toString());
				str.put("shipped_branch_quantity", shipped_branch_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("shipped_box_quantity") != null) {
				BigDecimal shipped_box_quantity = new BigDecimal(str.get("shipped_box_quantity").toString());
				str.put("shipped_box_quantity", shipped_box_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("order_quantity") != null) {
				BigDecimal order_quantity = new BigDecimal(str.get("order_quantity").toString());
				str.put("order_quantity", order_quantity.setScale(6, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("order_branch_quantity") != null) {
				BigDecimal order_branch_quantity = new BigDecimal(str.get("order_branch_quantity").toString());
				str.put("order_branch_quantity", order_branch_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("order_box_quantity") != null) {
				BigDecimal order_box_quantity = new BigDecimal(str.get("order_box_quantity").toString());
				str.put("order_box_quantity", order_box_quantity.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("totalAmount") != null) {
				BigDecimal totalAmount = new BigDecimal(str.get("totalAmount").toString());
				str.put("totalAmount", totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("create_date") != null) {
				String create_date = str.get("create_date").toString();
				str.put("create_date", create_date.substring(0, 19));
			}
			if (str.get("order_create_date") != null) {
				String order_create_date = str.get("order_create_date").toString();
				str.put("order_create_date", order_create_date.substring(0, 19));
			}
			if (str.get("shipping_time") != null) {
				String shipping_time = str.get("shipping_time").toString();
				str.put("shipping_time", shipping_time.substring(0, 10));
			}
			if (!ConvertUtil.isEmpty(hiddenAmount) && hiddenAmount == 0) {
				// 总金额
				str.put("amount", "***");
				// 销售单价
				str.put("order_price", "***");
				// 金额
				str.put("totalAmount", "***");
				// 结算单价
				str.put("sale_org_price", "***");
				// 实际发货金额
				str.put("item_amount", "***");
				// 结算金额
				str.put("sale_org_amount", "***");
				// 预收金额
				str.put("amount_sub", "***");
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "发货单编号", "ERP单号", "发货通知单制单人", "仓库", "sbu", "发运方式", "仓库类型", "备注", "发货单状态", "总金额", "机构","状态",
				"客户名称", "客户简称", "客户编码", "业务类型", "产品名称", "产品编码", "产品描述", "木种花色", "规格", "经营组织", "产品级别", "产品分类", "销售单价",
				"金额", "结算单价", "计划发货箱数", "订单支数", "订单数量", "实际发货箱数", "实际发货支数", "实际发货数", "实际发货金额", "抬头", "税号", "结算金额",
				"预收金额", "收货人", "收货人电话", "收货地址", "发货单生成日期", "总体积", "总重量", "订单制单人", "实际发货日期", "配送方式", "承运商", "计划发货日期",
				"司机", "车牌", "电话", "订单制单人", "订单创建时间", "来源订单编号", "单据日期", "色号", "含水率", "批次", "运输方式" };

		// 设置单元格取值
		String[] properties = { "sn", // 发货单编号
				"erp_sn", // ERP单号
				"shipping_store_member_name", // 发货通知单制单人
				"warehouse_name", // 仓库
				"sbu_name", // sbu
				"smethod", // 发货方式
				"ssValue", // 仓库类型
				"memo", // 备注
				"status_name", // 发货单状态
				"amount", // 总金额
				"sale_org_name", // 机构
				"status_type", //状态
				"store_name", // "客户名称",
				"store_alias", // "客户简称",
				"store_out_trade_no", // "客户编码",
				"business_division_value", // 业务类型
				"name", // "产品名称",
				"vonder_code", // "产品编码",
				"description", // "产品描述",
				"wood_type_or_color", // 木种花色
				"spec", // 规格
				"product_organization_name", // 经营组织
				"levelName", // 产品等级
				"product_category_name", // 产品分类
				"order_price", // 销售单价
				"totalAmount", // 金额
				"sale_org_price", // 结算单价
				"box_quantity", // "计划发货箱数",
				"order_branch_quantity", // "订单支数",
				"order_quantity", // "订单数量",
				"shipped_box_quantity", // "实际发货箱数",
				"shipped_branch_quantity", // "实际发货支数",
				"shipped_quantity", // "实际发货数",
				"item_amount", // "实际发货金额",
				"invoice_title", // "抬头",
				"nashuishibiehao", // "税号",
				"sale_org_amount", // 结算金额
				"amount_sub", // "预收金额",
				"consignee", // "收货人",
				"phone", // "收货人电话",
				"address", // "收货地址",
				"create_date", // "发货单生成日期",
				"volume", // "总体积",
				"weight", // "总重量",
				"store_member_name", // "订单制单人",
				"shipped_time", // "实际发货日期",
				"shippingMethodName", // "配送方式",
				"delivery_corp_name", // "承运商",
				"shipping_time", // "计划发货日期",
				"driverInfoName", // "司机",
				"driverInfoMobile", // "车牌",
				"carNumber", // "电话",
				"store_member_name", // "订单制单人",
				"order_create_date", // 订单创建时间
				"orderSn", // 来源订单编号
				"gl_date", "colour_number_name", // 色号
				"moisture_content_name", // 含水率
				"batch_encoding", // 批次
				"transportTypeName"// 运输方式
		};
		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256};

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}

	/** 发货通知单生成页面的导出 大自然 */
	public ModelAndView getModelAndViewForCreate(List<Map<String, Object>> data, ModelMap model) {

		for (Map<String, Object> str : data) {
			if (str.get("order_date") != null) {
				String orderDate = DateUtil.convert((Date) str.get("order_date"));
				if (orderDate.length() > 10) {
					orderDate = orderDate.substring(0, 10);
				}
				str.put("orderDate", orderDate);
			}
			// 发货日期
			if (str.get("shippedTime") != null) {
				String shippedTime = DateUtil.convert((Date) str.get("shippedTime"));
				if (shippedTime.length() > 10) {
					shippedTime = shippedTime.substring(0, 10);
				}
				str.put("shippedTime", shippedTime);
			}

			if (str.get("check_date") != null) {
				String checkDate = DateUtil.convert((Date) str.get("check_date"));
				if (checkDate.length() > 19) {
					checkDate = checkDate.substring(0, 19);
				}
				str.put("checkDate", checkDate);
			}
			if (str.get("quantity") != null) {
				BigDecimal quantity_int = new BigDecimal(str.get("quantity").toString());
				str.put("quantity_int", NumberFormat.getInstance().format(quantity_int));
			}
			if (str.get("volume") != null) {
				BigDecimal volume = new BigDecimal(str.get("volume").toString());
				str.put("volume", NumberFormat.getInstance().format(volume));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "订单编号", "下单日期", "仓库名称", "工厂/供应商", "产品名称", "产品编码", "产品型号", "业务类型", "经营组织", "数量", "体积",
				"客户名称", "客户简称", "收货人", "收货人电话", "收货地址", "实际发货日期", "配送方式", "备注", "审核人", "订单审核日期", "机构", "色号", "含水率",
				"批次", "运输方式" };

		// 设置单元格取值
		String[] properties = { "order_sn", "orderDate", "warehouse_name", "manufactory_name", "name", "vonder_code",
				"model", "business_type_value", "product_organization_name", "quantity_int", "volume", "store_name",
				"store_alias", "consignee", "phone", "address", "shippedTime", "shipping_method_name", "memo",
				"check_store_member_name", "checkDate", "sale_org_name", "colour_number_name", // 色号
				"moisture_content_name", // 含水率
				"batch_encoding", "transport_type_name" };

		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 450, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}

	@RequestMapping(value = "/getCaiNiaoJson", method = RequestMethod.POST)
	public @ResponseBody ResultMsg getCaiNiaoJson(Long[] ids, int type) {

		String templateURL = null;

		try {
			templateURL = SystemConfig.getConfig("kbShippingTemplate", WebUtils.getCurrentCompanyInfoId());
		} catch (Exception e) {
		}
		if (ConvertUtil.isEmpty(templateURL)) {
			templateURL = "http://cloudprint.cainiao.com/template/standard/267620/9";
		}

		Map<String, Object> requestData = new HashMap<String, Object>();
		requestData.put("cmd", "print");
		requestData.put("requestID", "3456");
		requestData.put("version", "1.0");

		Map<String, Object> task = new HashMap<String, Object>();
		task.put("taskID", UUID.randomUUID().toString());
		task.put("printer", "");

		if (type == 0) {
			// 查看
			task.put("preview", true);
			task.put("previewType", "pdf");
		} else {
			// 打印
			task.put("preview", false);
			task.put("previewType", "image");
		}

		List<Map<String, Object>> documents = new ArrayList<Map<String, Object>>();
		Map<String, Object> document = new HashMap<String, Object>();
		document.put("documentID", "0123456789");

		List<Map<String, Object>> contents = new ArrayList<Map<String, Object>>();

		for (Long id : ids) {
			Shipping shipping = shippingService.find(id);
			Store store = shipping.getStore();
			CompanyInfo companyInfo = companyInfoBaseService.find(shipping.getCompanyInfoId());
			String logo = companyInfo.getLogo();
			String company_name = companyInfo.getCompany_name();
			String alias = store.getAlias();
			if (alias == null || alias.length() == 0)
				alias = store.getName();

			List<ShippingItem> shippingItems = shipping.getShippingItems();
			List<ShippingItem> nItems = new ArrayList<ShippingItem>();
			if (companyInfo.getCompany_code().equals("shinco")) {
				for (ShippingItem shippingItem : shippingItems) {
					int bomFlag = shippingItem.getBomFlag();
					if (bomFlag < 2) {
						nItems.add(shippingItem);
					}
				}
			} else {
				for (ShippingItem shippingItem : shippingItems) {
					int bomFlag = shippingItem.getBomFlag();
					if (bomFlag == 0 || bomFlag == 2) {
						nItems.add(shippingItem);
					}
				}
			}
			int itemSize = nItems.size();// 2
			int pageSize = 5;
			int pdfPage = itemSize % pageSize == 0 ? itemSize / pageSize : (itemSize / pageSize) + 1;
			ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pdfPage];// 用于存储每页生成PDF流

			NumberFormat nf = NumberFormat.getInstance();
			for (int i = 0; i < pdfPage; i++) {

				Map<String, Object> content = new HashMap<String, Object>();

				Map<String, Object> data = new HashMap<String, Object>();

				int column = 1;// 列数 3 1-3 4
				int forNumber = i * pageSize + pageSize > itemSize ? itemSize : i * pageSize + pageSize;

				String create_date = DateUtil.convertDateToStr(shipping.getCreateDate(), "yyyy-MM-dd");
				data.put("company_name", company_name);
				data.put("create_date", create_date);
				data.put("page", (i + 1) + "");
				data.put("totalPage", pdfPage + "");
				data.put("page_info", (i + 1) + " / " + pdfPage);
				data.put("sn", shipping.getSn());
				data.put("store_name", alias);
				data.put("store_sn", ConvertUtil.toEmpty(store.getOutTradeNo()));
				data.put("address", ConvertUtil.toEmpty(shipping.getAddress()));
				if (shipping.getStoreMember() != null) {
					data.put("store_member_name", ConvertUtil.toEmpty(shipping.getStoreMember().getName()));
				}
				if (shipping.getCheckStoreMember() != null) {
					data.put("checkStoreMember", ConvertUtil.toEmpty(shipping.getCheckStoreMember().getName()));
				}
				data.put("consignee", ConvertUtil.toEmpty(shipping.getConsignee()));
				data.put("phone", ConvertUtil.toEmpty(shipping.getPhone()));
				data.put("shipping_method", ConvertUtil.toEmpty(shipping.getShippingMethod()));
				DriverInfo driverInfo = shipping.getDriverInfo();
				if (driverInfo != null) {
					data.put("driver_name", ConvertUtil.toEmpty(driverInfo.getName()));
					data.put("car_number", ConvertUtil.toEmpty(driverInfo.getCarNumber()));
					data.put("id_number", ConvertUtil.toEmpty(driverInfo.getIdNumber()));
				}
				if (shipping.getDelivery() != null) {
					data.put("delivery_name", ConvertUtil.toEmpty(shipping.getDelivery().getName()));
				}
				Warehouse warehouse = shipping.getWarehouse();
				if (warehouse != null) {
					data.put("warehouse_name", ConvertUtil.toEmpty(warehouse.getName()));
				}

				BigDecimal total_quantity = BigDecimal.ZERO;
				BigDecimal total_box_quantity = BigDecimal.ZERO;
				BigDecimal total_branch_quantity = BigDecimal.ZERO;
				BigDecimal total_amount = BigDecimal.ZERO;

				BigDecimal totalVolume = new BigDecimal(0);
				BigDecimal totalQuantity = new BigDecimal(0);
				BigDecimal total_shippinged_quantity = new BigDecimal(0);
				for (int j = i * pageSize; j < forNumber; j++) {

					ShippingItem shippingItem = nItems.get(j);
					Product product = shippingItem.getProduct();

					data.put("order_sn_" + column, shippingItem.getOrderSn());
					data.put("sn_" + column, shippingItem.getSn());
					data.put("name_" + column, shippingItem.getName());
					data.put("model_" + column, ConvertUtil.toEmpty(shippingItem.getModel()));
					data.put("vonder_code_" + column, ConvertUtil.toEmpty(shippingItem.getVonderCode()));

					BigDecimal quantity = shippingItem.getQuantity();
					data.put("quantity_" + column, nf.format(quantity));

					data.put("quantity_with_util_" + column,
							nf.format(quantity) + ConvertUtil.toEmpty(product.getUnit()));

					BigDecimal shippedQuantity = shippingItem.getShippedQuantity();
					if (shippedQuantity == null) {
						shippedQuantity = BigDecimal.ZERO;
					}
					data.put("shipped_quantity_with_util_" + column,
							nf.format(shippedQuantity) + ConvertUtil.toEmpty(product.getUnit()));

					BigDecimal boxQuantity = shippingItem.getBoxQuantity();
					if (boxQuantity == null) {
						boxQuantity = BigDecimal.ZERO;
					}
					data.put("box_quantity_" + column, nf.format(boxQuantity));

					BigDecimal branchQuantity = shippingItem.getBranchQuantity();
					if (branchQuantity == null) {
						branchQuantity = BigDecimal.ZERO;
					}
					data.put("branch_quantity_" + column, nf.format(branchQuantity));

					data.put("description_" + column, ConvertUtil.toEmpty(shippingItem.getDescription()));

					String productGradeStr = "";
					if (product.getProductLevel() != null) {
						productGradeStr = product.getProductLevel().getValue();
					} else {
						productGradeStr = "优等品";
					}

					data.put("product_grade_" + column, productGradeStr);

					BigDecimal price = shippingItem.getOrderItem().getPrice();
					data.put("price_" + column, nf.format(price));

					BigDecimal lineAmount = shippingItem.getQuantity().multiply(price);
					String lineAmountStr = nf.format(lineAmount);
					data.put("line_amount_" + column, lineAmountStr);

					// Warehouse warehouse = shippingItem.getWarehouse();
					// String warehouseName = warehouse.getName();成品库列都写成成品库，不显示具体仓库
					data.put("warehouse_name_" + column, "成品库");
					String volume = null;
					if (product != null && product.getVolume() != null) {
						BigDecimal lineVolume = product.getVolume().multiply(shippingItem.getQuantity());
						totalVolume = totalVolume.add(lineVolume);
						volume = nf.format(lineVolume);
					}
					totalQuantity = totalQuantity.add(shippingItem.getQuantity());

					total_quantity = total_quantity.add(quantity);
					total_shippinged_quantity = total_shippinged_quantity.add(shippedQuantity);
					total_box_quantity = total_box_quantity.add(boxQuantity);
					total_branch_quantity = total_branch_quantity.add(branchQuantity);
					total_amount = total_amount.add(lineAmount);

					data.put("volume_" + column, ConvertUtil.toEmpty(volume));
					column++;
				}
				data.put("total_quantity", nf.format(total_quantity));
				data.put("total_shippinged_quantity", nf.format(total_shippinged_quantity));
				data.put("total_box_quantity", nf.format(total_box_quantity));
				data.put("total_branch_quantity", nf.format(total_branch_quantity));
				data.put("total_amount", nf.format(total_amount));
				data.put("total_amount_cn", "  " + NumberToCN.number2CNMontrayUnit(total_amount));

				data.put("totalQuantity", nf.format(totalQuantity));
				data.put("totalVolume", nf.format(totalVolume));
				content.put("data", data);
				content.put("templateURL", templateURL);
				contents.add(content);
			}

		}

		document.put("contents", contents);
		documents.add(document);
		task.put("documents", documents);
		requestData.put("task", task);

		return ResultMsg.success().addObjX(requestData);
	}

	@RequestMapping(value = "/select_lock_list", method = RequestMethod.GET)
	public String selectLockList(String shippingSn, String[] params, ModelMap model) {
		List<String> str = new ArrayList<String>();
		for (String pr : params) {

			str.add(pr);
		}
		model.addAttribute("shippingSn", shippingSn);
		model.addAttribute("param", str);
		return "/b2b/shipping/select_lock_list";
	}

	/**
	 * 锁库查询
	 *
	 * @param param
	 * @param shippingSn
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/select_lock_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg selectLockData(String[] param, String shippingSn) throws Exception {

//		for(String pr : param){

//		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		List<Map<String, String>> stockList = null;
		List<Map<String, String>> newstockList = new ArrayList<Map<String, String>>();
		// 接收接口返回值
		List<Map<String, String>> productList = new ArrayList<Map<String, String>>();
		String jsonPage = null;
		if ("nature".equals(companyInfo.getCompany_code())) {

			// 大自然查询现有库存
			HashMap<String, Object> params = null;
			String shippingsn = "";
			if (shippingSn != null && shippingSn != "") {
				shippingsn = shippingSn;
			}

			params = new HashMap<String, Object>();
			if (param != null) {
				for (String pr : param) {
					params.put("ITEM_CODE", pr);
				}
			}

			params.put("CUST_PO_NUMBER", shippingsn);

			// 接口回传list<Map>
			stockList = new NatureLockWarehouseOnHandQty().lockWarehouse(params);

			if (stockList != null) {
				for (Map<String, String> mp : stockList) {
					if (mp.get("cust_po_number") == null) {
						mp.remove(mp);
					}
				}
				newstockList.addAll(stockList);
			}
		}

		if (newstockList != null && newstockList.size() > 0) {
			jsonPage = JsonUtils.toJson(newstockList);
		} else {
			newstockList = new ArrayList<Map<String, String>>();
			jsonPage = JsonUtils.toJson(productList);
		}
		return success(jsonPage);
	}

	/**
	 * 设置旗标
	 *
	 * @param ids
	 * @param flag
	 * @return
	 */
	@RequestMapping(value = "/setFlag", method = RequestMethod.POST)
	public @ResponseBody ResultMsg setFlag(Long[] ids, Integer flag) {
		if (ConvertUtil.isEmpty(ids) || ids.length == 0) {
			return error("请选择单据行");
		}
		if (ConvertUtil.isEmpty(flag)) {
			return error("15250");
		}
		shippingService.setOrderItemFlag(ids, flag);
		return success();
	}
	
	   /**
     * 列表数据
     */
    @RequestMapping(value = "/sendToLink5", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg sendToLink5(Long[] ids) {

    	shippingService.synchronization(ids);
        return success("同步成功");
    }


}