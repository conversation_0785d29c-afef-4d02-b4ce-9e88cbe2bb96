package net.shopxx.order.b2b.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;

import net.shopxx.base.core.util.*;
import net.shopxx.basic.service.*;
import net.shopxx.intf.job.IntfLogisticsJob;
import net.shopxx.order.entity.*;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.util.SystemParametes;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.aftersales.b2b.entity.B2bReturnsItem;
import net.shopxx.aftersales.b2b.service.B2bReturnsItemService;
import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SbuItems;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.service.AmShippingItemService;
import net.shopxx.order.service.AmShippingService;
import net.shopxx.order.service.MoveLibraryCloseItemService;
import net.shopxx.order.service.MoveLibraryCloseService;
import net.shopxx.order.service.MoveLibraryItemService;
import net.shopxx.order.service.MoveLibraryService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.SaleShippingNatureService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseBatchItem;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseBatchItemService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

/**
 * 出库单
 * 
 * <AUTHOR>
 *
 */
@Controller("saleShippingNatureController")
@RequestMapping("/b2b/sale_shipping_nature")
public class SaleShippingNatureController extends BaseController {

	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "saleShippingNatureServiceImpl")
	private SaleShippingNatureService saleShippingNatureService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "b2bReturnsItemServiceImpl")
	private B2bReturnsItemService b2bReturnsItemService;
	@Resource(name = "amShippingServiceImpl")
	private AmShippingService amShippingService;
	@Resource(name = "amShippingItemServiceImpl")
	private AmShippingItemService amShippingItemService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "moveLibraryServiceImpl")
	private MoveLibraryService moveLibraryService;
	@Resource(name = "moveLibraryItemServiceImpl")
	private MoveLibraryItemService moveLibraryItemService;
	@Resource(name = "totalDateServiceImpl")
	private TotalDateService totalDateService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "warehouseBatchItemServiceImpl")
	private WarehouseBatchItemService warehouseBatchItemService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "moveLibraryCloseServiceImpl")
	private MoveLibraryCloseService moveLibraryCloseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "b2bReturnsServiceImpl")
	private B2bReturnsService b2bReturnsService;
	@Resource(name = "moveLibraryCloseItemServiceImpl")
	private MoveLibraryCloseItemService moveLibraryCloseItemService;
    @Resource(name = "intfOrderMessageToServiceImpl")
    private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "intfLogisticsJob")
    private IntfLogisticsJob intfLogisticsJob;
 

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, Integer flag, Long objTypeId, Long objid, Long billType, ModelMap model,
			Long sign, Long menuId) {

		model.addAttribute("flag", flag);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("billType", billType);
		model.addAttribute("menuId", menuId);
		if (sign == null) {
			sign = 0L;
		}
		model.addAttribute("sign", sign);

		return "/b2b/sale_shipping/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Integer flag, ModelMap model, Long userId, Long menuId, Integer pageType, Long sign) {

		model.addAttribute("pageType", pageType);
		model.addAttribute("flag", flag);

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}

		if (sign == null) {
			sign = 0L;
		}
		model.addAttribute("sign", sign);
		model.addAttribute("menuId", menuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/sale_shipping/list";
	}

	/**
	 * <AUTHOR>
	 * @param categories 单据类型
	 * @return
	 */
	@RequestMapping(value = "/categories_list", method = RequestMethod.POST)
	public @ResponseBody ResultMsg categories_list(String categories) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "billCategory"));
		filters.add(Filter.eq("remark", 1));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> billCategoryList = systemDictService.findList(null, filters, null);
		Map<String, Object> returnMap = new HashMap<String, Object>();
		returnMap.put("data", billCategoryList);
		String jsonPage = JsonUtils.toJson(returnMap);
		return success(jsonPage);
	}

	/**
	 * 获取指定id的出入库信息
	 * 
	 * @param itemId
	 * @return
	 */
	@RequestMapping(value = "/getJumpPath", method = RequestMethod.POST)
	public @ResponseBody ResultMsg getJumpPath(Long itemId, Long id,Long menuId,Integer orderIndex) {
		String url = "";
		Map<String, Object> map = amShippingItemService.getAmShippingItemInfo(itemId);
		if (map != null) {
			if (map.get("shipping_item") != null) {
				url = "view.jhtml?id="+id+"&menuId="+menuId+"&sourceType=1&orderIndex="+orderIndex;
			} else if (map.get("b2b_returns_item") != null) {
				url = "returnReceiveview.jhtml?id="+id;
			} else if (map.get("move_library_item_issue") != null) {
				url = "movelibrary_issue_view.jhtml?id=" + id+"&menuId="+menuId+"&sourceType=1&orderIndex="+orderIndex;
			} else if (map.get("move_library_item_receive") != null) {
				url = "movelibrary_receive_view.jhtml?id=" + id+"&menuId="+menuId+"&sourceType=1&orderIndex="+orderIndex;
			} else if (map.get("move_library_close_item") != null) {
				url = "movelibrary_close_view.jhtml?moveLibraryCloseSn=null&id=" + id;
			} else {
				url = "view.jhtml?id="+id+"&menuId="+menuId+"&sourceType=1&orderIndex="+orderIndex;
			}
		}
		String jsonPage = JsonUtils.toJson(url);
		return success(jsonPage);
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn, Long[] docstatus, Long[] billTypeId, Long[] warehouseId,
			Long[] saleOrgId, Long[] sbuId, Long[] productId, String sourceTypeSn, String storeMemberName,
			String startTime, String endTime, Long[] billCategoryId, Pageable pageable) {

		Page<Map<String, Object>> page = saleShippingNatureService.findPage(sn, docstatus, billTypeId, warehouseId,
				saleOrgId, sbuId, productId, sourceTypeSn, storeMemberName, startTime, endTime, billCategoryId,
				pageable);

		List<Map<String, Object>> amShippings = page.getContent();

		if (!amShippings.isEmpty()) {
			String ids = "";
			for (int i = 0; i < amShippings.size(); i++) {
				Map<String, Object> map = amShippings.get(i);
				if (i == amShippings.size() - 1) {
					ids += map.get("id");
				} else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> amShippingsItems = saleShippingNatureService
					.findAmShippingItemListByShippingId(ids, true);
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : amShippings) {
				items = new ArrayList<Map<String, Object>>();
				String shippingId = map.get("id").toString();
				for (Map<String, Object> itemMap : amShippingsItems) {
					String oid = itemMap.get("am_shipping").toString();

					if (shippingId.equals(oid)) {
						items.add(itemMap);
					}
				}
				map.put("am_shipping_items", items);
			}
		}
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Integer flag, Long billType, ModelMap model, Long sign) {

		model.addAttribute("flag", flag);
		model.addAttribute("productionWarehousing", "生产入仓");
		model.addAttribute("billType", billType);
		StoreMember storeMember = storeMemberService.getCurrent();
		// 查询用户关联SBU
		List<Map<String, Object>> sbu = storeMemberService.findSbuTy(storeMember.getId());
		if (sbu.size() > 0) {
			Long sbuIds = Long.parseLong(sbu.get(0).get("id").toString());
			model.addAttribute("sbuIds", sbuIds);

		}

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		model.addAttribute("sbus", sbus);

		// 用户默认的所属机构
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberService.getCurrent()));
		filters.add(Filter.eq("isDefault", true));
		List<StoreMemberSaleOrg> storeMemberSaleOrgs = storeMemberSaleOrgService.findList(null, filters, null);
		SaleOrg saleOrg = null;
		if (storeMemberSaleOrgs != null && storeMemberSaleOrgs.size() > 0) {
			saleOrg = storeMemberSaleOrgs.get(0).getSaleOrg();
		}
		model.addAttribute("saleOrg", saleOrg);

		filters.clear();

		filters.add(Filter.eq("id", billType));
		filters.add(Filter.eq("code", "billType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> billTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("billTypes", billTypes);

		SystemDict billTyped = systemDictService.find(billType);
		model.addAttribute("billTyped", billTyped);

		if (sign == null) {
			sign = 0L;
		}
		model.addAttribute("sign", sign);

		// 产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null, filters, null);
		model.addAttribute("productLevelList", productLevelList);

		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 新旧标识
		filters.clear();
		filters.add(Filter.eq("code", "oldNewLogo"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
		model.addAttribute("newOldLogosList", newOldLogosList);

		// 单据类别 根据类型筛选
		filters.clear();
		filters.add(Filter.eq("code", "billCategory"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		if (billTyped.getValue() != null && "入仓".equals(billTyped.getValue())) {
			filters.add(Filter.eq("remark", 1));
		} else if (billTyped.getValue() != null && "出仓".equals(billTyped.getValue())) {
			filters.add(Filter.eq("remark", 0));
		}
		List<SystemDict> billCategoryList = systemDictService.findList(null, filters, null);
		model.addAttribute("billCategoryList", billCategoryList);

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}

		// 设置日期格式
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("nowDate", df.format(new Date()));

		// 是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(linkStockValue)) {
			Integer linkStock = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}

		// 仓储管理是否启用库存查询 0不展示、1展示
		Integer storageStockQueryRoles = roleJurisdictionUtil.getRoleCount("storageStockQueryRoles");
		model.addAttribute("storageStockQueryRoles", storageStockQueryRoles);

		return "/b2b/sale_shipping/add";
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/shipping_item_list", method = RequestMethod.GET)
	public String shipping_item_list(ModelMap model, Long userId, Long menuId) {
		model.addAttribute("menuId",menuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/sale_shipping/shipping_item_list";
	}

	/**
	 * 发货明细列表数据
	 */
	@RequestMapping(value = "/shipping_item_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg order_item_list_data(String shippingSn, String consignee, Long[] storeId,
			String name, String vonderCode, String storeMemberName, Long[] saleOrgId, String model, Pageable pageable) {

		// 2019-05-16 增加创建人，多机构查询
		Page<Map<String, Object>> page = shippingService.findItemPage(shippingSn, storeId, name, consignee, vonderCode,
				model, storeMemberName, saleOrgId, pageable, null);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	@RequestMapping(value = "/shipment_picking_edit", method = RequestMethod.GET)
	public String shipmentPickingEdit(Long[] ids) {
		// 获取列表数据信息
		Page<Map<String, Object>> page = shippingService.findItemPage(null, null, null, null, null, null, null, null,
				null, ids);
		//
		return "/b2b/sale_shipping/am_shipping_operate_list";
	}

	/**
	 * 校验单据参数
	 * 
	 * @param warehouse
	 * @param warehouseIds
	 * @param saleOrg
	 * @param saleOrgIds
	 * @param sbu
	 * @param sbuIds
	 * @param sn
	 */
	private void checkBillParams(Warehouse warehouse, Long[] warehouseIds, SaleOrg saleOrg, Long[] saleOrgIds, Sbu sbu,
			Long[] sbuIds, String sn) {
		// 仓库
		if (ConvertUtil.isEmpty(warehouse)) {
			ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的仓库不能为空");
		} else {
			if (warehouseIds[0].intValue() == 0) {
				// 仓库类型
				SystemDict typeSystemDict = warehouse.getTypeSystemDict();
				if (ConvertUtil.isEmpty(typeSystemDict)) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的仓库类型不能为空");
				}
				if (ConvertUtil.isEmpty(typeSystemDict.getFlag())) {
					ExceptionUtil.throwServiceException("请管理员对单据编号为【" + sn + "】的仓库类型标识进行维护");
				}
				warehouseIds[0] = warehouse.getId();
			} else {
				if (warehouseIds[0].intValue() != warehouse.getId().intValue()) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的所选仓库不一致");
				}
			}
		}
		// 机构
		if (ConvertUtil.isEmpty(saleOrg)) {
			ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的机构不能为空");
		} else {
			if (saleOrgIds[0].intValue() == 0) {
				saleOrgIds[0] = saleOrg.getId();
			} else {
				if (saleOrgIds[0].intValue() != saleOrg.getId().longValue()) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的所选机构不一致");
				}
			}
		}
		// sbu
		if (ConvertUtil.isEmpty(sbu)) {
			ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的sbu不能为空");
		} else {
			if (sbuIds[0].intValue() == 0) {
				sbuIds[0] = sbu.getId();
			} else {
				if (sbuIds[0].intValue() != sbu.getId().intValue()) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的所选sbu不一致");
				}
			}
		}
	}

	/**
	 * 校验客户参数
	 * 
	 * @param store
	 * @param storeIds
	 */
	private void checkStoreParams(Store store, Long[] storeIds, String sn) {
		// 客户
		if (ConvertUtil.isEmpty(store)) {
			ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的客户不能为空");
		} else {
			if (storeIds[0].intValue() == 0) {
				storeIds[0] = store.getId();
			} else {
				if (storeIds[0].intValue() != store.getId().intValue()) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的所选客户不一致");
				}
			}
		}
	}

	/**
	 * 校验收货地址参数
	 * 
	 * @param consignees
	 * @param consignee
	 * @param phones
	 * @param phone
	 * @param areaIds
	 * @param area
	 * @param addresss
	 * @param address
	 * @param sn
	 */
	private void checkAddressParams(String[] consignees, String consignee, String[] phones, String phone,
			Long[] areaIds, Area area, String[] addresss, String address, String sn) {
		// 收货人
		if (ConvertUtil.isEmpty(consignee)) {
			ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的收货人不能为空");
		} else {
			if (ConvertUtil.isEmpty(consignees[0])) {
				consignees[0] = consignee;
			} else {
				if (!consignees[0].equals(consignee)) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的所选收货人不一致");
				}
			}
		}
		// 收货电话
		if (ConvertUtil.isEmpty(phone)) {
			ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的收货人电话不能为空");
		} else {
			if (ConvertUtil.isEmpty(phones[0])) {
				phones[0] = phone;
			} else {
				if (!phones[0].equals(phone)) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的所选收货人电话不一致");
				}
			}
		}
		// 收货地区
		if (ConvertUtil.isEmpty(area)) {
			ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的收货地区不能为空");
		} else {
			if (areaIds[0].intValue() == 0) {
				areaIds[0] = area.getId();
			} else {
				if (areaIds[0] != area.getId().intValue()) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的所选收货地区不一致");
				}
			}
		}
		// 收货地址
		if (ConvertUtil.isEmpty(address)) {
			ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的收货地址不能为空");
		} else {
			if (ConvertUtil.isEmpty(addresss[0])) {
				addresss[0] = address;
			} else {
				if (!addresss[0].equals(address)) {
					ExceptionUtil.throwServiceException("单据编号为【" + sn + "】的所选收货地址不一致");
				}
			}
		}
	}

	/**
	 * 获取HashMap
	 * 
	 * @param ids
	 * @return
	 */
	private HashMap<String, Object> getBillHashMap(Long[] ids, Integer type) {
		// 仓库
		Long[] warehouseIds = new Long[] { 0L };
		// 客户
		Long[] storeIds = new Long[] { 0L };
		// 机构
		Long[] saleOrgIds = new Long[] { 0L };
		// sbu
		Long[] sbuIds = new Long[] { 0L };
		// 收货人
		String[] consignees = new String[] { "" };
		// 收货人电话
		String[] phones = new String[] { "" };
		// 收货地区邮编
		String zipCode = "";
		// 地址外部编码
		String addressOutTradeNo = "";
		// 收货地区
		Long[] areaIds = new Long[] { 0L };
		// 收货地址
		String[] addresss = new String[] { "" };
		HashMap<String, Object> map = new HashMap<String, Object>();
		for (int i = 0; i < ids.length; i++) {
			// 发货挑库
			if (type == 0) {
				ShippingItem shippingItem = shippingItemService.find(ids[i]);
				if (ConvertUtil.isEmpty(shippingItem)) {
					ExceptionUtil.throwServiceException("发货挑库单明细不存在");
				}
				Shipping shipping = shippingItem.getShipping();
				if (ConvertUtil.isEmpty(shipping)) {
					ExceptionUtil.throwServiceException("发货挑库单不存在");
				}
				// 校验客户参数
				this.checkStoreParams(shipping.getStore(), storeIds, shipping.getSn());
				// 校验单据参数
				this.checkBillParams(shipping.getWarehouse(), warehouseIds, shipping.getSaleOrg(), saleOrgIds,
						shipping.getSbu(), sbuIds, shipping.getSn());
				// 校验收货地址参数
				this.checkAddressParams(consignees, shipping.getConsignee(), phones, shipping.getPhone(), areaIds,
						shipping.getArea(), addresss, shipping.getAddress(), shipping.getSn());
				// 收货地区邮编
				zipCode = shipping.getZipCode();
				// 地址外部编码
				addressOutTradeNo = shipping.getAddressOutTradeNo();
			} else if (type == 1) {
				// 退货接收
				B2bReturnsItem b2bReturnsItem = b2bReturnsItemService.find(ids[i]);
				if (ConvertUtil.isEmpty(b2bReturnsItem)) {
					ExceptionUtil.throwServiceException("退货接收单明细不存在");
				}
				B2bReturns b2bReturns = b2bReturnsItem.getB2bReturns();
				if (ConvertUtil.isEmpty(b2bReturns)) {
					ExceptionUtil.throwServiceException("退货接收单不存在");
				}
				// 校验客户参数
				this.checkStoreParams(b2bReturns.getStore(), storeIds, b2bReturns.getSn());
				// 校验单据参数
				this.checkBillParams(b2bReturns.getWarehouse(), warehouseIds, b2bReturns.getSaleOrg(), saleOrgIds,
						b2bReturns.getSbu(), sbuIds, b2bReturns.getSn());
				// 校验收货地址参数
				this.checkAddressParams(consignees, b2bReturns.getConsignee(), phones, b2bReturns.getConsigneeMobile(),
						areaIds, b2bReturns.getArea(), addresss, b2bReturns.getAddress(), b2bReturns.getSn());
				// 收货地区邮编
				zipCode = b2bReturns.getZipCode();
				// 地址外部编码
				addressOutTradeNo = b2bReturns.getAddressOutTradeNo();
			}
		}
		// 仓库
		map.put("warehouseId", warehouseIds[0]);
		// 客户
		map.put("storeId", storeIds[0]);
		// 机构
		map.put("saleOrgId", saleOrgIds[0]);
		// sbu
		map.put("sbuId", sbuIds[0]);
		// 收货人
		map.put("consignee", consignees[0]);
		// 收货人电话
		map.put("phone", phones[0]);
		// 收货地区邮编
		map.put("addressOutTradeNo", addressOutTradeNo);
		// 地址外部编码
		map.put("zipCode", zipCode);
		// 收货地区
		map.put("areaId", areaIds[0]);
		// 收货地址
		map.put("address", addresss[0]);
		return map;
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/check_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_data(Long[] ids) {
		// 获取HashMap
		HashMap<String, Object> map = this.getBillHashMap(ids, 0);

		return success(JsonUtils.toJson(map));
	}

	/**
	 * 添加单据ModelMap
	 * 
	 * @param storeId
	 * @param warehouseId
	 * @param saleOrgId
	 * @param sbuId
	 * @param billTypeName
	 * @param billCategoryName
	 * @param consignee
	 * @param phone
	 * @param zipCode
	 * @param addressOutTradeNo
	 * @param areaId
	 * @param address
	 * @param model
	 */
	private void addBillModelMap(Long storeId, Long warehouseId, Long saleOrgId, Long sbuId, String billTypeName,
			String billCategoryName, String consignee, String phone, String zipCode, String addressOutTradeNo,
			Long areaId, String address, ModelMap model) {

		// 设置日期格式
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("nowDate", df.format(new Date()));
		// 客户
		if (!ConvertUtil.isEmpty(storeId)) {
			Store store = storeBaseService.find(storeId);
			model.addAttribute("store", store);
			// 收货地址
			model.addAttribute("consignee", consignee);
			model.addAttribute("phone", phone);
			model.addAttribute("addressOutTradeNo", addressOutTradeNo);
			model.addAttribute("zipCode", zipCode);
			Area area = areaBaseService.find(areaId);
			model.addAttribute("area", area);
			model.addAttribute("address", address);
		}
		// 仓库
		if (!ConvertUtil.isEmpty(warehouseId)) {
			Warehouse warehouse = warehouseBaseService.find(warehouseId);
			model.addAttribute("warehouse", warehouse);
		}
		// 机构
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
			model.addAttribute("saleOrg", saleOrg);
		}
		// sbu
		if (!ConvertUtil.isEmpty(sbuId)) {
			Sbu sbu = sbuService.find(sbuId);
			model.addAttribute("sbu", sbu);
		}
		if (!ConvertUtil.isEmpty(billTypeName)) {
			// 单据类型
			SystemDict billType = roleJurisdictionUtil.getSystemDict("billType", billTypeName);
			model.addAttribute("billType", billType);
		}
		// 单据类别
		if (!ConvertUtil.isEmpty(billCategoryName)) {
			SystemDict billCategory = roleJurisdictionUtil.getSystemDict("billCategory", billCategoryName);
			model.addAttribute("billCategory", billCategory);
		}
		// 产品级别
		List<SystemDict> productLevelList = roleJurisdictionUtil.getSystemDictList("productLevel", null);
		model.addAttribute("productLevelList", productLevelList);
		// 色号
		List<SystemDict> colorNumberList = roleJurisdictionUtil.getSystemDictList("colorNumber", null);
		model.addAttribute("colorNumberList", colorNumberList);
		// 含水率
		List<SystemDict> moistureContentList = roleJurisdictionUtil.getSystemDictList("moistureContent", null);
		model.addAttribute("moistureContentList", moistureContentList);
		// 新旧标识
		List<SystemDict> newOldLogosList = roleJurisdictionUtil.getSystemDictList("oldNewLogo", null);
		model.addAttribute("newOldLogosList", newOldLogosList);
		// 是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(linkStockValue)) {
			Integer linkStock = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		} else {
			model.addAttribute("linkStock", 0);
		}
		// 仓储管理是否启用库存查询 0不展示、1展示
		Integer storageStockQueryRoles = roleJurisdictionUtil.getRoleCount("storageStockQueryRoles");
		model.addAttribute("storageStockQueryRoles", storageStockQueryRoles);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		Integer hiddenBatch = roleJurisdictionUtil.getRoleCount("hiddenBatchRoles");
		model.addAttribute("hiddenBatch", hiddenBatch);
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/am_shipping_operate_list", method = RequestMethod.GET)
	public String shipping_operate_list(Long[] ids, Long storeId, Long warehouseId, Long saleOrgId, Long sbuId,
			String consignee, String phone, String zipCode, String addressOutTradeNo, Long areaId, String address,
			ModelMap model, Long menuId, Long orderIndex) {

		model.addAttribute("ids", ids);
		// 添加单据ModelMap
		this.addBillModelMap(storeId, warehouseId, saleOrgId, sbuId, "出仓", "销售出仓", consignee, phone, zipCode,
				addressOutTradeNo, areaId, address, model);
		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
		// 备注
		List<Long> billIds = new ArrayList<Long>();
		String memo = "";
		String shippingSn = "";
		String shippingSnId = "";
		for (Long id : ids) {
			ShippingItem shippingItem = shippingItemService.find(id);
			Shipping shipping = shippingItem.getShipping();
			if (!ConvertUtil.isEmpty(shipping.getId()) && !billIds.contains(shipping.getId())) {
				billIds.add(shipping.getId());
				if (!ConvertUtil.isEmpty(shipping.getMemo())) {
					memo = shipping.getMemo();
				}
			}
			if (!ConvertUtil.isEmpty(shipping.getSn()) && !shippingSn.contains(shipping.getSn())) {
				if (ConvertUtil.isEmpty(shippingSn)) {
					shippingSn = shipping.getSn();
				} else {
					shippingSn = shippingSn + "、" + shipping.getSn();
				}
			}
			shippingSnId = String.valueOf(shipping.getId());
		}
		model.addAttribute("shippingSnId", shippingSnId);
		model.addAttribute("shippingSn", shippingSn);
		model.addAttribute("memo", memo);
		return "/b2b/sale_shipping/am_shipping_operate_list";
	}

	/**
	 * 保存
	 * 
	 * @param amShipping
	 * @param warehouseId
	 * @param deliveryId
	 * @param areaId
	 * @param saleOrgId
	 * @param driverInfoId
	 * @param storeId
	 * @param supplierId
	 * @param organizationId
	 * @param flag           0:大自然，1:天加
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(AmShipping amShipping, Long warehouseId, Long deliveryId, Long areaId,
			Long saleOrgId, Long driverInfoId, Long storeId, Long supplierId, Long organizationId, Long sbuId,
			Long regionalManagerId, Long billTypeId, Integer flag, Long billCategoryId) {

		List<Long> shippingIds = new ArrayList<Long>();
		List<AmShippingItem> amShippingItems = amShipping.getAmShippingItems();
		if (amShippingItems.isEmpty()) {
			// 请添加发货产品
			return error("15152");
		}
		for (AmShippingItem amShippingItem : amShippingItems) {
			Long shippingId = null;
			if (!ConvertUtil.isEmpty(amShippingItem.getShipping())
					&& !ConvertUtil.isEmpty(amShippingItem.getShipping().getId())) {
				shippingId = amShippingItem.getShipping().getId();
			}
			if (!ConvertUtil.isEmpty(shippingId)) {
				shippingIds.add(shippingId);
			}
		}
		if (storeId != null) {
			amShipping.setStore(storeBaseService.find(storeId));
		}
		if (organizationId != null) {
			amShipping.setOrganization(organizationService.find(organizationId));
		}
		amShipping.setSaleOrg(saleOrgBaseService.find(saleOrgId));
		amShipping.setRegionalManager(storeMemberBaseService.find(regionalManagerId));
		amShipping.setSbu(sbuService.find(sbuId));
		amShipping.setStatus(0);
		Warehouse warehouse = warehouseBaseService.find(warehouseId);
		amShipping.setWarehouse(warehouse);
		Area area = null;
		if (!ConvertUtil.isEmpty(areaId)) {
			area = areaBaseService.find(areaId);
		}
		amShipping.setArea(area);
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		amShipping.setStoreMember(storeMember);
		amShipping.setBillType(systemDictService.find(billTypeId));
		// 单据类别
		amShipping.setBillCategory(systemDictService.find(billCategoryId));
		saleShippingNatureService.save(amShipping, warehouse, area, shippingIds, sbuId, flag);

		// 2019-05-16 冯旗 发货单保存后跳转view页面
		return success().addObjX(amShipping.getId());
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/am_shipping_operate_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg shipping_operate_list_data(Long[] ids) {
		List<Map<String, Object>> mapList = shippingService.findShippingItemList(ids);
		String jsonPage = JsonUtils.toJson(mapList);
		return success(jsonPage);
	}

	/**
	 * 查看
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, String sn, Integer readOnly, Integer flag, ModelMap model, Integer pageType,
			Integer billTypeId,Long menuId,Long orderIndex,Integer sourceType) throws Exception {

        model.addAttribute("menuId", menuId);
        model.addAttribute("orderIndex", orderIndex);
        model.addAttribute("sourceType",sourceType);
		AmShipping amShipping = null;

		if (id != null) {
			amShipping = saleShippingNatureService.find(id);
		} else if (sn != null) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			amShipping = saleShippingNatureService.find(filters);
			id = amShipping.getId();
		}
		if (amShipping.getWarehouse() != null) {
			Warehouse warehouse = amShipping.getWarehouse();
			model.addAttribute("warehouse", warehouse);
			model.addAttribute("smethods", warehouse.getWarehouseSmethodList());
		}

		model.addAttribute("amShipping", amShipping);

		Integer auditType = 0;
		if (!amShipping.getAmShippingItems().isEmpty() && amShipping.getAmShippingItems().size() > 0) {
            if(amShipping.getAmShippingItems().get(0).getInventory() != null){
                model.addAttribute("isInventory", true);
            }else{
				model.addAttribute("isInventory", false);
			}
			for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
				if (!ConvertUtil.isEmpty(amShippingItem.getShipping()) && !ConvertUtil.isEmpty(amShippingItem.getShipping().getId())) {
					auditType++;
					break;
				}
			}
		}
		model.addAttribute("auditType", auditType);

		List<Map<String, Object>> list = saleShippingNatureService.findAmShippingItemListByShippingId(id.toString(),
				null);

		String jsonStr = JsonUtils.toJson(list);

		model.addAttribute("jsonStr", jsonStr);

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		if (billTypeId == null) {
			billTypeId = 0;
		}
		model.addAttribute("billTypeId", billTypeId);
		filters.add(Filter.eq("id", billTypeId));
		filters.add(Filter.eq("code", "billType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> billTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("billTypes", billTypes);

		/** 订单附件 */
		String shippingAttach_json = JsonUtils.toJson(shippingService.findAttachsByShippingId(id));
		model.addAttribute("shippingAttach_json", shippingAttach_json);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(amShipping.getSn(), 4));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("flag", flag);
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(57L);// test/正式:57
																	// nadev:59
		model.addAttribute("isCheckWf", isCheckWf);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null, filters, null);
		model.addAttribute("organizations", organizations);

		// sbu
		Sbu sbu = amShipping.getSbu();
		List<SbuItems> sbuItems = sbu.getShippingMethodSbuList();
		model.addAttribute("sbuItems", sbuItems);
		model.addAttribute("sbuIds", sbu.getId());
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		model.addAttribute("sbus", sbus);
		model.addAttribute("isMember", storeMember.getMemberType());

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> listString = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}

        //订单行是否展示隐藏批次  0不隐藏‘选择批次’、1隐藏‘选择批次’
		String hiddenSelectBacthValue = SystemConfig.getConfig("hiddenSelectBacth", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(hiddenSelectBacthValue)) {
			Integer hiddenSelectBacth = Integer.valueOf(hiddenSelectBacthValue);
			model.addAttribute("hiddenSelectBacth", hiddenSelectBacth);
		}


		// 产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null, filters, null);
		model.addAttribute("productLevelList", productLevelList);

		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 新旧标识
		filters.clear();
		filters.add(Filter.eq("code", "oldNewLogo"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
		model.addAttribute("newOldLogosList", newOldLogosList);

		// 是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(linkStockValue)) {
			Integer linkStock = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}

		// 批次列表
		Integer warehouseBatchItemIndex = 0;
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("amShipping", amShipping.getId()));
		List<WarehouseBatchItem> warehouseBatchItemList = warehouseBatchItemService.findList(null, filters, null);
		if (!warehouseBatchItemList.isEmpty() && warehouseBatchItemList.size() > 0) {
			List<Map<String, Object>> vWarehouseBatchItemList = warehouseBatchItemService.findVWarehouseBatchItemList(
					amShipping.getId(), null, null, null, null, null, null, null, null, null, null, null, null);
			warehouseBatchItemIndex = warehouseBatchItemList.size();
			model.addAttribute("vWarehouseBatchItemList", JsonUtils.toJson(vWarehouseBatchItemList));
		}
		model.addAttribute("warehouseBatchItemIndex", warehouseBatchItemIndex);

		// 仓储管理是否启用库存查询 0不展示、1展示
		Integer storageStockQueryRoles = roleJurisdictionUtil.getRoleCount("storageStockQueryRoles");
		model.addAttribute("storageStockQueryRoles", storageStockQueryRoles);

		// 是否订货批次出仓
		Integer isOrderWarehousingBatch = getIsOrderWarehousingBatch(amShipping.getWarehouse());
		model.addAttribute("isOrderWarehousingBatch", isOrderWarehousingBatch);

		return "/b2b/sale_shipping/view";
	}

	/**
	 * 更新
	 * 
	 * @param amShipping
	 * @param warehouseId
	 * @param deliveryId
	 * @param areaId
	 * @param saleOrgId
	 * @param driverInfoId
	 * @param supplierId
	 * @param organizationId
	 * @param sbuId
	 * @param regionalManagerId
	 * @param billTypeId
	 * @param flag
	 * @param billCategoryId
	 * @param wAmShippingItem
	 * @return
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(AmShipping amShipping, Long warehouseId, Long deliveryId, Long areaId,
			Long saleOrgId, Long driverInfoId, Long supplierId, Long organizationId, Long sbuId, Long regionalManagerId,
			Long billTypeId, Integer flag, Long billCategoryId, AmShippingItem wAmShippingItem) {

        //判断订单中的产品与产品资料中的转换率是否一样
//        for (AmShippingItem amShippingItem:amShipping.getAmShippingItems()){
//            if(amShippingItem.getProduct()!=null) {
//                Product product = productService.find(amShippingItem.getProduct().getId());
//                if (amShippingItem.getBranchPerBox().compareTo(product.getBranchPerBox()) != 0) {
//                    ExceptionUtil.throwServiceException("订单中的【" + product.getVonderCode() + "】产品与产品资料的转换率不相符");
//                }
//            }
//        }
		//校验是否有明细行
		if (amShipping.getAmShippingItems().isEmpty()) {
			return error("请添加单据行项");
		}
		for (Iterator<AmShippingItem> iterator = amShipping.getAmShippingItems().iterator(); iterator.hasNext();) {
			AmShippingItem amShippingItem = iterator.next();
			if (ConvertUtil.isEmpty(amShippingItem) || (ConvertUtil.isEmpty(amShippingItem.getProductOrganization()))) {
				iterator.remove();
			}
		}
		//校验是否有两个相同的产品行
		int size=amShipping.getAmShippingItems().size();
		for (int i = 0;i < size-1; i++){
			for (int j = i+1;j< size;j++){
				if(
						(amShipping.getAmShippingItems().get(i).getProduct().getId().equals(amShipping.getAmShippingItems().get(j).getProduct().getId()))
								&& (amShipping.getAmShippingItems().get(i).getProductOrganization().getId().equals(amShipping.getAmShippingItems().get(j).getProductOrganization().getId()))
								&& (amShipping.getAmShippingItems().get(i).getProductLevel().getId().equals(amShipping.getAmShippingItems().get(j).getProductLevel().getId()))
								&& (amShipping.getAmShippingItems().get(i).getNewOldLogos().getId().equals(amShipping.getAmShippingItems().get(j).getNewOldLogos().getId()))
								&& (amShipping.getAmShippingItems().get(i).getMoistureContents().getId().equals(amShipping.getAmShippingItems().get(j).getMoistureContents().getId()))
								&& (amShipping.getAmShippingItems().get(i).getColorNumbers().getId().equals(amShipping.getAmShippingItems().get(j).getColorNumbers().getId()))
				){
					return error("第"+(i+1)+"行于第"+(j+1)+"行的产品相同，请分开两个单开单");
				}
			}
		}
		// 地区
		Area area = areaBaseService.find(areaId);
		amShipping.setArea(area);
		// 仓库
		Warehouse warehouse = warehouseBaseService.find(warehouseId);
		amShipping.setWarehouse(warehouse);
		// 区域经理
		StoreMember regionalManager = storeMemberBaseService.find(regionalManagerId);
		amShipping.setRegionalManager(regionalManager);
		// 单据类别
		SystemDict billCategory = systemDictService.find(billCategoryId);
		amShipping.setBillCategory(billCategory);
		// sbu
		Sbu sbu = sbuService.find(sbuId);
		amShipping.setSbu(sbu);
		// 经营组织
		Organization organization = organizationService.find(organizationId);
		amShipping.setOrganization(organization);
		// 机构
		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		amShipping.setSaleOrg(saleOrg);
		AmShipping dataBaseAmShipping = saleShippingNatureService.find(amShipping.getId());
		// 单据类型
		if (!ConvertUtil.isEmpty(billTypeId)) {
			SystemDict billType = systemDictService.find(billTypeId);
			if (!ConvertUtil.isEmpty(billType) && billType.getValue().equals("出仓")) {
				saleShippingNatureService.updateAmShippingItemQuantity(dataBaseAmShipping, amShipping);
			}
			amShipping.setBillType(billType);
		}
		saleShippingNatureService.updateAmShipping(amShipping, wAmShippingItem);

		return success();
	}

	/**
	 * 流程审核
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 * @throws Exception
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkWf(Long id) throws Exception {
		saleShippingNatureService.checkWf(id);
		return success();
	}

	/**
	 * 流程审核
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 * @throws Exception
	 */
	@RequestMapping(value = "/shipping_audit", method = RequestMethod.POST)
	public @ResponseBody ResultMsg shippingAudit(Long id) throws Exception {
		saleShippingNatureService.shippingAudit(id);
		return success();
	}

	/**
	 * 退货接收列表
	 */
	@RequestMapping(value = "/returnReceivelist", method = RequestMethod.GET)
	public String returnReceivelist(ModelMap model, Long userId, Long menuId) {
		model.addAttribute("menuId", menuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/sale_shipping/returnReceivelist";
	}

	/**
	 * 退货接收列表数据
	 */
	@RequestMapping(value = "/returnReceivelist_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg returnReceivelist_data(String sn, String consignee, Long[] storeId, String name,
			String vonderCode, String storeMemberName, Long[] saleOrgId, String model, Pageable pageable) {

		Page<Map<String, Object>> page = shippingService.returnReceivelist_data(sn, storeId, name, consignee,
				vonderCode, model, storeMemberName, saleOrgId, pageable, null);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 退货接收列表
	 */
	@RequestMapping(value = "/check_returnReceiveData", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_returnReceiveData(Long[] ids) {
		// 获取HashMap
		HashMap<String, Object> map = this.getBillHashMap(ids, 1);
		return success(JsonUtils.toJson(map));
	}

	/**
	 * 退货接收添加页面
	 */
	@RequestMapping(value = "/returnReceiveadd", method = RequestMethod.GET)
	public String returnReceive_add(Long[] ids, Long storeId, Long warehouseId, Long menuId, Long orderIndex,
			Long saleOrgId, Long sbuId, String consignee, String phone, String zipCode, String addressOutTradeNo,
			Long areaId, String address, ModelMap model) {

		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
		model.addAttribute("ids", ids);
		// 添加单据ModelMap
		this.addBillModelMap(storeId, warehouseId, saleOrgId, sbuId, "入仓", "销售退货", consignee, phone, zipCode,
				addressOutTradeNo, areaId, address, model);
		// 备注
		List<Long> billIds = new ArrayList<Long>();
		String memo = "";
		String sourceNo = "";
		String sourceNoId = "";
		StringBuilder b2bReturnsAftersale= new StringBuilder();
		Set<String> b2bResturnsSet = new TreeSet<String>();
		for (Long id : ids) {
			B2bReturnsItem b2bReturnsItem = b2bReturnsItemService.find(id);
			B2bReturns b2bReturns = b2bReturnsItem.getB2bReturns();
			if (!ConvertUtil.isEmpty(b2bReturns.getId()) && !billIds.contains(b2bReturns.getId())) {
				billIds.add(b2bReturns.getId());
				if (!ConvertUtil.isEmpty(b2bReturns.getReason())) {
					memo = b2bReturns.getReason();
				}
			}
			if (!ConvertUtil.isEmpty(b2bReturns.getSn()) && !sourceNo.contains(b2bReturns.getSn())) {
				if (ConvertUtil.isEmpty(sourceNo)) {
					sourceNo = b2bReturns.getSn();
				} else {
					sourceNo = sourceNo + "、" + b2bReturns.getSn();
				}
			}
			// 来源单id
			sourceNoId = String.valueOf(b2bReturns.getId());
			if(b2bReturns.getAftersale() != null) {
				b2bResturnsSet.add(b2bReturns.getAftersale().getSn());
			}
		}
		for(String sn : b2bResturnsSet){
			b2bReturnsAftersale.append(sn + ",");
		}
		if(b2bReturnsAftersale.length()>0){
			model.addAttribute("b2bReturnsAftersale",b2bReturnsAftersale.deleteCharAt(b2bReturnsAftersale.lastIndexOf(",")).toString());
		}else{
			model.addAttribute("b2bReturnsAftersale",b2bReturnsAftersale);
		}
		model.addAttribute("memo", memo);
		model.addAttribute("sourceNo", sourceNo);
		model.addAttribute("sourceNoId", sourceNoId);
		return "/b2b/sale_shipping/returnReceiveadd";
	}

	/**
	 * 退货接收添加页面列表数据
	 */
	@RequestMapping(value = "/returnReceiveadd_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg returnReceiveadd_list_data(Long[] ids) {
		List<Map<String, Object>> mapList = b2bReturnsService.findB2bReturnsItemList(ids);
		String jsonPage = JsonUtils.toJson(mapList);
		return success(jsonPage);
	}

	/**
	 * 退货接收保存接口
	 * 
	 * @param amShipping
	 * @param warehouseId
	 * @param deliveryId
	 * @param areaId
	 * @param saleOrgId
	 * @param driverInfoId
	 * @param storeId
	 * @param supplierId
	 * @param organizationId
	 * @param flag
	 * @return
	 */
	@RequestMapping(value = "/returnReceivesave", method = RequestMethod.POST)
	public @ResponseBody ResultMsg returnReceivesave(AmShipping amShipping, Long warehouseId, Long deliveryId,
			Long areaId, Long saleOrgId, Long driverInfoId, Long storeId, Long supplierId, Long organizationId,
			Long sbuId, Long regionalManagerId, Long billTypeId, Integer flag, Long billCategoryId) {

		if (!ConvertUtil.isEmpty(areaId)) {
			Area area = areaBaseService.find(areaId);
			amShipping.setArea(area);
		}
		if (!ConvertUtil.isEmpty(storeId)) {
			amShipping.setStore(storeBaseService.find(storeId));
		}
		if (!ConvertUtil.isEmpty(organizationId)) {
			amShipping.setOrganization(organizationService.find(organizationId));
		}
		List<AmShippingItem> amShippingItems = amShipping.getAmShippingItems();

		if (amShippingItems.isEmpty()) {
			// 请添加发货产品
			return error("15152");
		}
		amShipping.setSaleOrg(saleOrgBaseService.find(saleOrgId));
		amShipping.setRegionalManager(storeMemberBaseService.find(regionalManagerId));
		amShipping.setSbu(sbuService.find(sbuId));
		amShipping.setStatus(0);
		Warehouse warehouse = warehouseBaseService.find(warehouseId);
		amShipping.setWarehouse(warehouse);
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		amShipping.setStoreMember(storeMember);
		amShipping.setBillType(systemDictService.find(billTypeId));
		amShipping.setBillCategory(systemDictService.find(billCategoryId));
		saleShippingNatureService.returnReceivesave(amShipping, warehouse);

		return success().addObjX(amShipping.getId());
	}

	/**
	 * 退货接收查看页面
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/returnReceiveview", method = RequestMethod.GET)
	public String returnReceiveview(Long id, String sn, Integer readOnly, Integer flag, Long menuId, Long orderIndex,
			ModelMap model, Integer pageType) throws Exception {


		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
		AmShipping amShipping = null;

		if (id != null) {
			amShipping = saleShippingNatureService.find(id);
		} else if (sn != null) {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("sn", sn));
			amShipping = saleShippingNatureService.find(filters);
			id = amShipping.getId();
		}
		model.addAttribute("amShipping", amShipping);
		if (amShipping.getWarehouse() != null) {
			Warehouse warehouse = amShipping.getWarehouse();
			model.addAttribute("warehouse", warehouse);
			model.addAttribute("smethods", warehouse.getWarehouseSmethodList());
		}

		Integer auditType = 0;
		if (!amShipping.getAmShippingItems().isEmpty() && amShipping.getAmShippingItems().size() > 0) {
			for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
				if (!ConvertUtil.isEmpty(amShippingItem.getB2bReturns())
						&& !ConvertUtil.isEmpty(amShippingItem.getB2bReturns().getId())) {
					auditType++;
					break;
				}

			}
		}
		model.addAttribute("auditType", auditType);

		List<Map<String, Object>> listMap = saleShippingNatureService.findAmShippingItemListByShippingId(id.toString(),
				null);
		String jsonStr = JsonUtils.toJson(listMap);
		model.addAttribute("jsonStr", jsonStr);

		/** 订单附件 */
		String shippingAttach_json = JsonUtils.toJson(shippingService.findAttachsByShippingId(id));
		model.addAttribute("shippingAttach_json", shippingAttach_json);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(amShipping.getSn(), 4));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("flag", flag);
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(57L);
		model.addAttribute("isCheckWf", isCheckWf);

		List<Filter> filters = new ArrayList<Filter>();
		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null, filters, null);
		model.addAttribute("organizations", organizations);

		// sbu
		Sbu sbu = amShipping.getSbu();
		List<SbuItems> sbuItems = sbu.getShippingMethodSbuList();
		model.addAttribute("sbuItems", sbuItems);
		model.addAttribute("sbuIds", sbu.getId());
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		model.addAttribute("sbus", sbus);
		model.addAttribute("isMember", storeMember.getMemberType());

		if (amShipping.getBillType() != null) {
			model.addAttribute("billTypeId", amShipping.getBillType().getId());
		}

		// 产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null, filters, null);
		model.addAttribute("productLevelList", productLevelList);

		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 新旧标识
		filters.clear();
		filters.add(Filter.eq("code", "oldNewLogo"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
		model.addAttribute("newOldLogosList", newOldLogosList);

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> list = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}

		// 是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(linkStockValue)) {
			Integer linkStock = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}

		List<Map<String, Object>> vWarehouseBatchItemList = warehouseBatchItemService.findVWarehouseBatchItemList(
				amShipping.getId(), null, null, null, null, null, null, null, null, null, null, null, null);
		model.addAttribute("vWarehouseBatchItemList", JsonUtils.toJson(vWarehouseBatchItemList));

		// 仓储管理是否启用库存查询 0不展示、1展示
		Integer storageStockQueryRoles = roleJurisdictionUtil.getRoleCount("storageStockQueryRoles");
		model.addAttribute("storageStockQueryRoles", storageStockQueryRoles);

		//关联的售后单
		StringBuilder b2bReturnsAftersale= new StringBuilder();
		Set<String> b2bResturnsSet = new TreeSet<String>();
		if (!amShipping.getAmShippingItems().isEmpty() && amShipping.getAmShippingItems().size() > 0) {
			for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
				if (!ConvertUtil.isEmpty(amShippingItem.getB2bReturns())
						&& !ConvertUtil.isEmpty(amShippingItem.getB2bReturns().getId())) {
					if(amShippingItem.getB2bReturns().getAftersale() != null) {
						b2bResturnsSet.add(amShippingItem.getB2bReturns().getAftersale().getSn());
					}
					}

			}
			for(String sns : b2bResturnsSet){
				b2bReturnsAftersale.append(sns + ",");
			}
		}
		if(b2bReturnsAftersale.indexOf(",") != -1){
            model.addAttribute("b2bReturnsAftersale",b2bReturnsAftersale.deleteCharAt(b2bReturnsAftersale.lastIndexOf(",")).toString());
        }else{
            model.addAttribute("b2bReturnsAftersale",b2bReturnsAftersale.toString());
        }

		return "/b2b/sale_shipping/returnReceiveview";
	}

	/**
	 * 退货接收流程审核
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 * @throws Exception
	 */
	@RequestMapping(value = "/returnReceive_check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg returnReceiveCheckWf(Long id) throws Exception {

		saleShippingNatureService.returnReceiveCheckWf(id);
		return success();
	}

	/**
	 * 出入库信息选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = saleShippingNatureService.findAmShippingList(null, null, null, null, null,
				null, null, null, null, null, null, ids, null, null, null);

		return getModelAndView(data, model);

	}

	public ModelAndView getModelAndView(List<Map<String, Object>> data, ModelMap model) {

		if (data != null && data.size() > 0) {
			for (Map<String, Object> str : data) {
				// 单据状态
				if (!ConvertUtil.isEmpty(str.get("status"))) {
					Integer amshippingStatus = (Integer) str.get("status");
					if (amshippingStatus == 0) {
						str.put("amshippingStatus", "未审核");
					} else if (amshippingStatus == 1) {
						str.put("amshippingStatus", "已审核");
					} else if (amshippingStatus == 2) {
						str.put("amshippingStatus", "作废");
					} else if (amshippingStatus == 3) {
						str.put("amshippingStatus", "部分发货");
					} else if (amshippingStatus == 4) {
						str.put("amshippingStatus", "完全发货");
					} else if (amshippingStatus == 5) {
						str.put("amshippingStatus", "审核中");
					}
				}
				// 来源ID、单号、单据类型
				if (!ConvertUtil.isEmpty(str.get("shipping_id"))) {
					str.put("sourceTypeId", str.get("shipping_id").toString());
					if (!ConvertUtil.isEmpty(str.get("shipping_sn"))) {
						str.put("sourceTypeSn", str.get("shipping_sn").toString());
					}
					str.put("sourceBillType", "发货挑库");
				} else if (!ConvertUtil.isEmpty(str.get("b2bReturns_id"))) {
					str.put("sourceTypeId", str.get("b2bReturns_id").toString());
					if (!ConvertUtil.isEmpty(str.get("b2bReturns_sn"))) {
						str.put("sourceTypeSn", str.get("b2bReturns_sn").toString());
					}
					str.put("sourceBillType", "退货接收");
				} else if (!ConvertUtil.isEmpty(str.get("movelibrary_issue_id"))) {
					str.put("sourceTypeId", str.get("movelibrary_issue_id").toString());
					if (!ConvertUtil.isEmpty(str.get("movelibrary_issue_sn"))) {
						str.put("sourceTypeSn", str.get("movelibrary_issue_sn").toString());
					}
					str.put("sourceBillType", "移库发货");
				} else if (!ConvertUtil.isEmpty(str.get("movelibrary_receive_id"))) {
					str.put("sourceTypeId", str.get("movelibrary_receive_id").toString());
					if (!ConvertUtil.isEmpty(str.get("movelibrary_receive_sn"))) {
						str.put("sourceTypeSn", str.get("movelibrary_receive_sn").toString());
					}
					str.put("sourceBillType", "移库接收");
				} else if (!ConvertUtil.isEmpty(str.get("move_library_close_id"))) {
					str.put("sourceTypeId", str.get("move_library_close_id").toString());
					if (!ConvertUtil.isEmpty(str.get("move_library_close_sn"))) {
						str.put("sourceTypeSn", str.get("move_library_close_sn").toString());
					}
					str.put("sourceBillType", "移库返挑入仓");
				} else {
					if (!ConvertUtil.isEmpty(str.get("sd_remark")) && str.get("sd_remark").equals("出仓")) {
						str.put("sourceBillType", "出库");
					} else {
						str.put("sourceBillType", "入库");
					}
				}
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "出库单编号", "出库单状态", "单据类型", "单据类别", "仓库名称", "机构名称", "仓库编码", "SBU", "产品名称", "产品描述", "产品编码",
				"产品型号", "经营组织", "产品级别", "木种/花色", "规格", "单位", "数量", "支数", "箱数", "色号", "含水率", "批次", "新旧标识", "来源单据号",
				"来源ID", "来源单据类型", "收货人", "收货人电话", "收货人地址", "备注", "创建人", "创建时间" };

		// 设置单元格宽度
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		// 设置单元格取值
		String[] properties = { "sn", "amshippingStatus", "sd_remark", "billCategoryName", "warehouse_name",
				"sale_org_name", "warehouse_sn", "sbu_name", "product_name", "descri_ption", "vonder_code",
				"product_model", "product_organization_name", "levelName", "wood_type_or_color", "product_spec",
				"product_unit", "quantity", "branch_quantity", "box_quantity", "color_numbers_name",
				"moisture_content_name", "batch_encoding", "new_old_logos_name", "sourceTypeSn", "sourceTypeId",
				"sourceBillType", "consignee", "phone", "address", "memo", "b_creater", "create_date" };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}

	/**
	 * 出入库信息条件导出统计数量
	 * 
	 * @param sn
	 * @param docstatus
	 * @param billTypeId
	 * @param warehouseId
	 * @param saleOrgId
	 * @param sbuId
	 * @param productId
	 * @param sourceTypeSn
	 * @param storeMemberName
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(String sn, Long[] docstatus, Long[] billTypeId,
			Long[] warehouseId, Long[] saleOrgId, Long[] sbuId, Long[] productId, String sourceTypeSn,
			String storeMemberName, String startTime, String endTime, Long[] billCategoryId) {

		List<Map<String, Object>> data = saleShippingNatureService.findAmShippingList(sn, docstatus, billTypeId,
				warehouseId, saleOrgId, sbuId, productId, sourceTypeSn, storeMemberName, startTime, endTime, null, null,
				null, billCategoryId);

		Integer size = 0;
		if (!data.isEmpty() && data.size() > 0) {
			size = data.size();
		}

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出入库信息
	 * 
	 * @param sn
	 * @param docstatus
	 * @param billTypeId
	 * @param warehouseId
	 * @param saleOrgId
	 * @param sbuId
	 * @param productId
	 * @param sourceTypeSn
	 * @param storeMemberName
	 * @param startTime
	 * @param endTime
	 * @param page
	 * @param model
	 * @param billCategoryId
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, Long[] docstatus, Long[] billTypeId, Long[] warehouseId,
			Long[] saleOrgId, Long[] sbuId, Long[] productId, String sourceTypeSn, String storeMemberName,
			String startTime, String endTime, Integer page, ModelMap model, Long[] billCategoryId) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");

		List<Map<String, Object>> data = saleShippingNatureService.findAmShippingList(sn, docstatus, billTypeId,
				warehouseId, saleOrgId, sbuId, productId, sourceTypeSn, storeMemberName, startTime, endTime, null, page,
				size, billCategoryId);

		return getModelAndView(data, model);

	}

	/**
	 * 查看pdf
	 * 
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkPdf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkPdf(Long ids) throws Exception {
		AmShipping amShipping = amShippingService.find(ids);
		if (amShipping == null) {
			return error("出入单不存在！");
		}
		TriplicateForm triplicateForm = null;
		if (triplicateForm == null) {
			triplicateForm = saleShippingNatureService.createAmShippingTriplicateForm(amShipping);
		}
		return success(triplicateForm.getUrl());
	}

	/**
	 * 查看批次pdf
	 * 
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkBatchPdf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkBatchPdf(Long ids) throws Exception {
		AmShipping amShipping = amShippingService.find(ids);
		if (amShipping == null) {
			return error("出入单不存在！");
		}
		TriplicateForm triplicateForm = null;
		if (triplicateForm == null) {
			triplicateForm = saleShippingNatureService.createAmShippingTriplicateFormByBatch(amShipping);
		}
		return success(triplicateForm.getUrl());
	}

	/**
	 * 出入库查询报表
	 * 
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/access_query_list", method = RequestMethod.GET)
	public String access_query_list(ModelMap model, Long userId, Long menuId) {
		model.addAttribute("menuId", menuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/sale_shipping/access_query_list";
	}

	/**
	 * 出入库查询列表数据
	 * 
	 * @param sn
	 * @param billTypeId
	 * @param billCategory
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param warehouseId
	 * @param storeMemberName
	 * @param productCategoryId
	 * @param vProductId
	 * @param productId
	 * @param woodTypeOrColor
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/access_query_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg access_query_list_data(String sn, Long[] billTypeId, String[] billCategory,
			String startTime, String endTime, Long[] saleOrgId, Long[] storeId, Long[] organizationId,
			Long[] warehouseId, String storeMemberName, Long[] productCategoryId, Long[] vProductId, Long[] productId,
			String woodTypeOrColor, String model, Pageable pageable) {

		Page<Map<String, Object>> page = saleShippingNatureService.findAccessPageList(sn, billTypeId, billCategory,
				startTime, endTime, saleOrgId, storeId, organizationId, warehouseId, storeMemberName, productCategoryId,
				vProductId, productId, woodTypeOrColor, model, null, pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	public ModelAndView getAccessModelAndView(List<Map<String, Object>> data, ModelMap model) {

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "单据编号", "单据类型", "单据日期", "单据类别", "事业部", "单位", "仓库组织名称", "仓库名称", "仓管员", "柜号", "车号", "品类名称",
				"系列名称", "货物全名", "货物编号", "货物型号", "规格", "件数", "支数", "平方数", "木种名称", "厚度mm", "宽度mm", "长度mm", "等级", "含水率",
				"新旧标识", "色号", "批次编码", "作业单号", "完工日期", "批次备注" };

		// 设置单元格宽度
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		// 设置单元格取值
		String[] properties = { "sn", "billType", "billDate", "billCategory", "saleOrgnName", "storeName",
				"organizationName", "warehouseName", "storeMemberName", "cabinetNumber", "vehicleNumber",
				"root_product_category_name", "product_category_name", "full_name", "vonder_code", "model", "spec",
				"piece_quantity", "per_branch", "quantity", "wood_type_or_color", "erp_height", "erp_width",
				"erp_length", "levelName", "moisture_content", "new_old_logo", "colour_number", "batch_encoding",
				"jobNumber", "completion_date", "batch_memo" };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}

	/**
	 * 出入库查询报表选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_access_export", method = RequestMethod.GET)
	public ModelAndView selectedAccessExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = saleShippingNatureService.findAccessList(null, null, null, null, null, null,
				null, null, null, null, null, null, null, null, null, ids, null, null);

		return getAccessModelAndView(data, model);

	}

	/**
	 * 出入库查询条件导出统计数量
	 * 
	 * @param sn
	 * @param billTypeId
	 * @param billCategory
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param warehouseId
	 * @param storeMemberName
	 * @param productCategoryId
	 * @param vProductId
	 * @param productId
	 * @param woodTypeOrColor
	 * @param model
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/to_access_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toAccessConditionExport(String sn, Long[] billTypeId,
			String[] billCategory, String startTime, String endTime, Long[] saleOrgId, Long[] storeId,
			Long[] organizationId, Long[] warehouseId, String storeMemberName, Long[] productCategoryId,
			Long[] vProductId, Long[] productId, String woodTypeOrColor, String model) {

		List<Map<String, Object>> data = saleShippingNatureService.findAccessList(sn, billTypeId, billCategory,
				startTime, endTime, saleOrgId, storeId, organizationId, warehouseId, storeMemberName, productCategoryId,
				vProductId, productId, woodTypeOrColor, model, null, null, null);
		Integer size = 0;
		if (!data.isEmpty() && data.size() > 0) {
			size = data.size();
		}
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 出入库查询报表条件导出
	 * 
	 * @param sn
	 * @param billTypeId
	 * @param billCategory
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param warehouseId
	 * @param storeMemberName
	 * @param productCategoryId
	 * @param vProductId
	 * @param productId
	 * @param woodTypeOrColor
	 * @param model
	 * @param page
	 * @param modelMap
	 * @return
	 */
	@RequestMapping(value = "/access_condition_export", method = RequestMethod.GET)
	public ModelAndView accessConditionExport(String sn, Long[] billTypeId, String[] billCategory, String startTime,
			String endTime, Long[] saleOrgId, Long[] storeId, Long[] organizationId, Long[] warehouseId,
			String storeMemberName, Long[] productCategoryId, Long[] vProductId, Long[] productId,
			String woodTypeOrColor, String model, Integer page, ModelMap modelMap) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");

		List<Map<String, Object>> data = saleShippingNatureService.findAccessList(sn, billTypeId, billCategory,
				startTime, endTime, saleOrgId, storeId, organizationId, warehouseId, storeMemberName, productCategoryId,
				vProductId, productId, woodTypeOrColor, model, null, page, size);

		return getAccessModelAndView(data, modelMap);

	}

	/**
	 * 收发存报表
	 * 
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/receive_report_list", method = RequestMethod.GET)
	public String receive_report_list(ModelMap model, Long userId, Long menuId) {
		model.addAttribute("menuId", menuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/sale_shipping/receive_report_list";
	}

	/**
	 * 收发存列表数据
	 * 
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param warehouseId
	 * @param productCategoryId
	 * @param vProductId
	 * @param productId
	 * @param woodTypeOrColor
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/receive_report_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg receive_report_list_data(String startTime, String endTime, Long[] saleOrgId,
			Long[] storeId, Long[] organizationId, Long[] warehouseId, Long[] productCategoryId, Long[] vProductId,
			Long[] productId, String woodTypeOrColor, String model, Pageable pageable) {

		Page<Map<String, Object>> page = saleShippingNatureService.findReceiveReportPageList(startTime, endTime,
				saleOrgId, storeId, organizationId, warehouseId, productCategoryId, vProductId, productId,
				woodTypeOrColor, model, null, pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	public ModelAndView getReceiveReportModelAndView(List<Map<String, Object>> data, ModelMap model) {

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "仓库组织", "仓库名称", "等级", "含水率", "新旧标识", "色号", "批次编码", "作业单号", "完工日期", "批次备注", "品类", "系列",
				"货物编码", "货物名称", "货物全名", "型号", "规格", "木种花色", "长度mm", "宽度mm", "厚度mm", "期初数量", "期初支数", "期初件数", "入库数量",
				"入库支数", "入库件数", "出库数量", "出库支数", "出库件数", "期末数量", "期末支数", "期末件数" };

		// 设置单元格宽度
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		// 设置单元格取值
		String[] properties = { "organizationName", "warehouseName", "levelName", "moisture_content", "new_old_logo",
				"colour_number", "batch_encoding", "jobNumber", "completion_date", "batch_memo",
				"root_product_category_name", "product_category_name", "vonder_code", "product_name", "full_name",
				"model", "spec", "wood_type_or_color", "erp_length", "erp_width", "erp_height", "initial_quantity",
				"initial_branch", "initial_piece", "enter_quantity", "enter_branch", "enter_piece", "out_quantity",
				"out_branch", "out_piece", "final_quantity", "final_branch", "final_piece" };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}

	/**
	 * 收发存报表选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_receiveReport_export", method = RequestMethod.GET)
	public ModelAndView selectedReceiveReportExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = saleShippingNatureService.findReceiveReportList(null, null, null, null, null,
				null, null, null, null, null, null, ids, null, null);

		return getReceiveReportModelAndView(data, model);
	}

	/**
	 * 收发存条件导出统计数量
	 * 
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param warehouseId
	 * @param productCategoryId
	 * @param vProductId
	 * @param productId
	 * @param woodTypeOrColor
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/to_receiveReport_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toReceiveReportConditionExport(String startTime, String endTime,
			Long[] saleOrgId, Long[] storeId, Long[] organizationId, Long[] warehouseId, Long[] productCategoryId,
			Long[] vProductId, Long[] productId, String woodTypeOrColor, String model) {

		List<Map<String, Object>> data = saleShippingNatureService.findReceiveReportList(startTime, endTime, saleOrgId,
				storeId, organizationId, warehouseId, productCategoryId, vProductId, productId, woodTypeOrColor, model,
				null, null, null);
		Integer size = 0;
		if (!data.isEmpty() && data.size() > 0) {
			size = data.size();
		}
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 收发存报表条件导出
	 * 
	 * @param startTime
	 * @param endTime
	 * @param saleOrgId
	 * @param storeId
	 * @param organizationId
	 * @param warehouseId
	 * @param productCategoryId
	 * @param page
	 * @param vProductId
	 * @param productId
	 * @param woodTypeOrColor
	 * @param model
	 * @param modelMap
	 * @return
	 */
	@RequestMapping(value = "/receiveReport_condition_export", method = RequestMethod.GET)
	public ModelAndView receiveReportConditionExport(String startTime, String endTime, Long[] saleOrgId, Long[] storeId,
			Long[] organizationId, Long[] warehouseId, Long[] productCategoryId, Integer page, Long[] vProductId,
			Long[] productId, String woodTypeOrColor, String model, ModelMap modelMap) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");

		List<Map<String, Object>> data = saleShippingNatureService.findReceiveReportList(startTime, endTime, saleOrgId,
				storeId, organizationId, warehouseId, productCategoryId, vProductId, productId, woodTypeOrColor, model,
				null, page, size);

		return getReceiveReportModelAndView(data, modelMap);

	}

	/**
	 * 发货挑库导出列表
	 * 
	 * @param data
	 * @param model
	 * @return
	 */
	public ModelAndView getDeliverModelAndView(List<Map<String, Object>> data, ModelMap model) {

		if (!ConvertUtil.isEmpty(data)) {
			for (Map<String, Object> map : data) {
				// 零散支数
				if (!ConvertUtil.isEmpty(map.get("branch_quantity"))
						&& !ConvertUtil.isEmpty(map.get("branch_per_box"))) {
					BigDecimal branchQuantity = new BigDecimal(map.get("branch_quantity").toString());
					BigDecimal branchPerBox = new BigDecimal(map.get("branch_per_box").toString());
					BigDecimal scatteredQuantity = branchQuantity.divideAndRemainder(branchPerBox)[1];
					map.put("scattered_quantity", scatteredQuantity.setScale(0, BigDecimal.ROUND_DOWN).toString());
				}
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "发货单编号", "客户名称", "仓库", "机构", "产品名称", "产品描述", "产品编码", "产品型号", "经营组织", "产品级别", "制单箱数", "支数",
				"零散支数", "数量", "收货人", "收货人电话", "收货地区", "收货地址", "体积", "创建人", "审核人", "机构", "创建日期", "含水率", "色号", "批次" };

		// 设置单元格宽度
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		// 设置单元格取值
		String[] properties = { "shipping_sn", "shipping_sn", "warehouse_name", "sale_org_name", "name", "description",
				"vonder_code", "model", "product_organization_name", "levelName", "box_quantity", "branch_quantity",
				"scattered_quantity", "quantity", "consignee", "phone", "area_full_name", "address", "volume",
				"store_member_name", "check_store_member_name", "sale_org_name", "create_date", "moisture_content_name",
				"colour_number_name", "batch_encoding" };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}

	/**
	 * 发货挑库选择导出
	 * 
	 * @param ids
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/deliver_selected_export_create", method = RequestMethod.GET)
	public ModelAndView deliverSelectedExportCreate(Long[] ids, ModelMap model, Pageable pageable) {

		Page<Map<String, Object>> page = shippingService.findItemPage(null, null, null, null, null, null, null, null,
				pageable, ids);

		List<Map<String, Object>> data = page.getContent();

		return getDeliverModelAndView(data, model);
	}

	/**
	 * 发货挑库条件导出统计数量
	 * 
	 * @param shippingSn
	 * @param consignee
	 * @param storeId
	 * @param name
	 * @param vonderCode
	 * @param storeMemberName
	 * @param saleOrgId
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/to_deliver_condition_export_create", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toDeliverConditionExportCreate(String shippingSn, String consignee,
			Long[] storeId, String name, String vonderCode, String storeMemberName, Long[] saleOrgId, String model,
			Pageable pageable) {

		Page<Map<String, Object>> page = shippingService.findItemPage(shippingSn, storeId, name, consignee, vonderCode,
				model, storeMemberName, saleOrgId, pageable, null);

		Integer size = (int) page.getContent().size();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 发货挑库条件导出
	 * 
	 * @param shippingSn
	 * @param consignee
	 * @param storeId
	 * @param name
	 * @param vonderCode
	 * @param storeMemberName
	 * @param saleOrgId
	 * @param model
	 * @param pageable
	 * @param modelMap
	 * @return
	 */
	@RequestMapping(value = "/deliver_condition_export", method = RequestMethod.GET)
	public ModelAndView deliverConditionExport(String shippingSn, String consignee, Long[] storeId, String name,
			String vonderCode, String storeMemberName, Long[] saleOrgId, String model, Pageable pageable,
			ModelMap modelMap, int page) {

		Map<String, Integer> segments = getSegment();
		int page_size = segments.get("size");
		pageable.setPageNumber(page);
		pageable.setPageSize(page_size);
		Page<Map<String, Object>> pageMap = shippingService.findItemPage(shippingSn, storeId, name, consignee,
				vonderCode, model, storeMemberName, saleOrgId, pageable, null);

		List<Map<String, Object>> data = pageMap.getContent();

		return getDeliverModelAndView(data, modelMap);

	}

	/**
	 * 退货接收导出列表
	 * 
	 * @param data
	 * @param model
	 * @return
	 */
	public ModelAndView getReturnModelAndView(List<Map<String, Object>> data, ModelMap model) {

		if (!ConvertUtil.isEmpty(data)) {
			for (Map<String, Object> map : data) {
				// 零散支数
				if (!ConvertUtil.isEmpty(map.get("branch_quantity"))
						&& !ConvertUtil.isEmpty(map.get("branch_per_box"))) {
					BigDecimal branchQuantity = new BigDecimal(map.get("branch_quantity").toString());
					BigDecimal branchPerBox = new BigDecimal(map.get("branch_per_box").toString());
					BigDecimal scatteredQuantity = branchQuantity.divideAndRemainder(branchPerBox)[1];
					map.put("scattered_quantity", scatteredQuantity.setScale(0, BigDecimal.ROUND_DOWN).toString());
				}
			}
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "退货编号", "客户名称", "仓库", "机构", "产品名称", "产品描述", "产品编码", "产品型号", "经营组织", "产品级别", "制单箱数", "支数",
				"零散支数", "数量", "收货人", "体积", "创建人", "机构", "创建日期", "含水率", "色号", "批次" };
		// 设置单元格宽度
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256 };
		// 设置单元格取值
		String[] properties = { "returnsSn", "store_name", "warehouse_name", "sale_org_name", "name", "description",
				"vonderCode", "model", "product_organization_name", "levelName", "box_quantity", "branch_quantity",
				"scattered_quantity", "quantity", "consignee", "volume", "store_member_name", "sale_org_name",
				"create_date", "moisture_content_name", "color_numbers_name", "batch_encoding" };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}

	/**
	 * 退货接收选择导出
	 * 
	 * @param ids
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/return_selected_export_create", method = RequestMethod.GET)
	public ModelAndView ReturnSelectedExportCreate(Long[] ids, ModelMap model, Pageable pageable) {

		Page<Map<String, Object>> page = shippingService.returnReceivelist_data(null, null, null, null, null, null,
				null, null, pageable, ids);

		List<Map<String, Object>> data = page.getContent();

		return getReturnModelAndView(data, model);
	}

	/**
	 * 退货接收条件导出统计数量
	 * 
	 * @param sn
	 * @param consignee
	 * @param storeId
	 * @param name
	 * @param vonderCode
	 * @param storeMemberName
	 * @param saleOrgId
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/to_return_condition_export_create", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toReturnConditionExportCreate(String sn, String consignee,
			Long[] storeId, String name, String vonderCode, String storeMemberName, Long[] saleOrgId, String model,
			Pageable pageable) {

		Page<Map<String, Object>> page = shippingService.returnReceivelist_data(sn, storeId, name, consignee,
				vonderCode, model, storeMemberName, saleOrgId, pageable, null);

		Integer size = (int) page.getContent().size();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 退货接收条件导出
	 * 
	 * @param sn
	 * @param consignee
	 * @param storeId
	 * @param name
	 * @param vonderCode
	 * @param storeMemberName
	 * @param saleOrgId
	 * @param model
	 * @param pageable
	 * @param modelMap
	 * @return
	 */
	@RequestMapping(value = "/return_condition_export", method = RequestMethod.GET)
	public ModelAndView returnConditionExport(String sn, String consignee, Long[] storeId, String name,
			String vonderCode, String storeMemberName, Long[] saleOrgId, String model, Pageable pageable, int page,
			ModelMap modelMap) {

		Map<String, Integer> segments = getSegment();
		int page_size = segments.get("size");
		pageable.setPageNumber(page);
		pageable.setPageSize(page_size);
		Page<Map<String, Object>> pageData = shippingService.returnReceivelist_data(sn, storeId, name, consignee,
				vonderCode, model, storeMemberName, saleOrgId, pageable, null);

		List<Map<String, Object>> data = pageData.getContent();

		return getReturnModelAndView(data, modelMap);
	}

	/**
	 * 出入库信息查看页面作废功能
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check(Long id) {

		AmShipping amShipping = saleShippingNatureService.find(id);

		saleShippingNatureService.checkOrCancel(amShipping);

		return success();
	}

	/*
	 * 移库发货列表
	 */
	@RequestMapping(value = "/movelibrary_issue_list", method = RequestMethod.GET)
	public String movelibraryIssueList(Long menuId, Long userId, ModelMap model) {
		model.addAttribute("menuId", menuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/sale_shipping/movelibrary_issue_list";
	}

	/*
	 * 移库发货、接收列表数据
	 */
	@RequestMapping(value = "/movelibrary_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg movelibraryIssueListData(String sn, Long[] saleOrgId, Long[] issueWarehouseId,
			Long[] receiveWarehouseId, Long[] sbuId, Long[] productId, String startIssueDate, String endIssueDate,
			String startReceiveDate, String endReceiveDate, Long[] creatorId, Boolean movelibrarySign,
			Pageable pageable) {

		Page<Map<String, Object>> page = moveLibraryService.movelibraryListData(sn, saleOrgId, issueWarehouseId,
				receiveWarehouseId, sbuId, productId, startIssueDate, endIssueDate, startReceiveDate, endReceiveDate,
				creatorId, movelibrarySign, pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);

	}

	public ModelAndView getMovelibraryIssueModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {

			// 需求日期
			if (!ConvertUtil.isEmpty(str.get("need_date"))) {
				String need_date = str.get("need_date").toString();
				str.put("need_date", need_date.substring(0, 19));
			}
			// 发出日期
			if (!ConvertUtil.isEmpty(str.get("issue_date"))) {
				String issue_date = str.get("issue_date").toString();
				str.put("issue_date", issue_date.substring(0, 19));
			}
			// 创建日期
			if (!ConvertUtil.isEmpty(str.get("create_date"))) {
				String create_date = str.get("create_date").toString();
				str.put("create_date", create_date.substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "单据编号", "机构", "来源仓", "SBU", "需求日期", "产品名称", "产品描述", "产品型号", "产品规格", "产品编码", "经营组织", "产品等级",
				"制单箱数", "支数", "零散支数", "数量", "发出数量", "发出箱数", "发出支数", "发出日期", "含水率", "色号", "批次", "创建人", "创建日期", "备注" };

		// 设置单元格取值
		String[] properties = { "sn", "sale_org_name", "issue_name", "sbu_name", "need_date", "product_name",
				"detail_description", "product_model", "product_spec", "vonder_code", "product_organization_name",
				"levelName", "available_issue_box_quantity", "available_issue_branch_quantity",
				"available_issue_scattered_quantity", "available_issue_quantity", "issue_quantity",
				"issue_box_quantity", "issue_branch_quantity", "issue_date", "moisture_content_name",
				"colour_number_name", "batch_encoding", "store_member_name", "create_date",
				"move_library_item_remarks" };

		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}

	/**
	 * 移库发货选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_movelibrary_issue_export", method = RequestMethod.GET)
	public ModelAndView selectedMovelibraryIssueExport(Long[] ids, Boolean movelibrarySign, ModelMap model) {

		List<Map<String, Object>> data = moveLibraryService.findMovelibraryList(null, null, ids, null, null, null, null,
				null, null, null, null, null, null, null, movelibrarySign);

		return getMovelibraryIssueModelAndViewForList(data, model);
	}

	/**
	 * 移库发货、接收导出统计总记录数
	 * 
	 * @param sn
	 * @param saleOrgId
	 * @param issueWarehouseId
	 * @param receiveWarehouseId
	 * @param sbuId
	 * @param productId
	 * @param startIssueDate
	 * @param endIssueDate
	 * @param startReceiveDate
	 * @param endReceiveDate
	 * @param creatorId
	 * @return
	 */
	@RequestMapping(value = "/to_movelibrary_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toMovelibraryConditionExport(String sn, Long[] saleOrgId,
			Long[] issueWarehouseId, Long[] receiveWarehouseId, Long[] sbuId, Long[] productId, String startIssueDate,
			String endIssueDate, String startReceiveDate, String endReceiveDate, Long[] creatorId,
			Boolean movelibrarySign) {

		List<Map<String, Object>> data = moveLibraryService.findMovelibraryList(null, null, null, sn, saleOrgId,
				issueWarehouseId, receiveWarehouseId, sbuId, productId, startIssueDate, endIssueDate, startReceiveDate,
				endReceiveDate, creatorId, movelibrarySign);

		Integer size = 0;

		if (!data.isEmpty() && data.size() > 0) {
			size = data.size();
		}

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 移库发货条件导出
	 * 
	 * @param page
	 * @param sn
	 * @param saleOrgId
	 * @param issueWarehouseId
	 * @param receiveWarehouseId
	 * @param sbuId
	 * @param productId
	 * @param startIssueDate
	 * @param endIssueDate
	 * @param startReceiveDate
	 * @param endReceiveDate
	 * @param creatorId
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/movelibrary_issue_condition_export", method = RequestMethod.GET)
	public ModelAndView movelibraryIssueConditionExport(Integer page, String sn, Long[] saleOrgId,
			Long[] issueWarehouseId, Long[] receiveWarehouseId, Long[] sbuId, Long[] productId, String startIssueDate,
			String endIssueDate, String startReceiveDate, String endReceiveDate, Long[] creatorId,
			Boolean movelibrarySign, ModelMap model) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");

		List<Map<String, Object>> data = moveLibraryService.findMovelibraryList(page, size, null, sn, saleOrgId,
				issueWarehouseId, receiveWarehouseId, sbuId, productId, startIssueDate, endIssueDate, startReceiveDate,
				endReceiveDate, creatorId, movelibrarySign);

		return getMovelibraryIssueModelAndViewForList(data, model);
	}

	/**
	 * 获取HashMap
	 * 
	 * @param ids
	 * @return
	 */
	private HashMap<String, Object> getMovelibraryBillHashMap(Long[] ids, Integer type) {
		// 仓库
		Long[] warehouseIds = new Long[] { 0L };
		// 机构
		Long[] saleOrgIds = new Long[] { 0L };
		// sbu
		Long[] sbuIds = new Long[] { 0L };
		HashMap<String, Object> map = new HashMap<String, Object>();
		for (int i = 0; i < ids.length; i++) {
			// 移库发货
			if (type == 0) {
				MoveLibraryItem moveLibraryItem = moveLibraryItemService.find(ids[i]);
				if (ConvertUtil.isEmpty(moveLibraryItem)) {
					ExceptionUtil.throwServiceException("移库发货单明细不存在");
				}
				MoveLibrary moveLibrary = moveLibraryItem.getMoveLibrary();
				if (ConvertUtil.isEmpty(moveLibrary)) {
					ExceptionUtil.throwServiceException("移库发货单不存在");
				}
				// 校验单据参数
				this.checkBillParams(moveLibrary.getIssueWarehouse(), warehouseIds, moveLibrary.getSaleOrg(),
						saleOrgIds, moveLibrary.getSbu(), sbuIds, moveLibrary.getSn());
			} else if (type == 1) {
				MoveLibraryItem moveLibraryItem = moveLibraryItemService.find(ids[i]);
				if (ConvertUtil.isEmpty(moveLibraryItem)) {
					ExceptionUtil.throwServiceException("移库接收单明细不存在");
				}
				MoveLibrary moveLibrary = moveLibraryItem.getMoveLibrary();
				if (ConvertUtil.isEmpty(moveLibraryItem)) {
					ExceptionUtil.throwServiceException("移库接收单不存在");
				}
				// 校验单据参数
				this.checkBillParams(moveLibrary.getReceiveWarehouse(), warehouseIds, moveLibrary.getSaleOrg(),
						saleOrgIds, moveLibrary.getSbu(), sbuIds, moveLibrary.getSn());
			} else if (type == 2) {
				MoveLibraryCloseItem moveLibraryCloseItem = moveLibraryCloseItemService.find(ids[i]);
				if (ConvertUtil.isEmpty(moveLibraryCloseItem)) {
					ExceptionUtil.throwServiceException("移库返挑入仓单明细不存在");
				}
				MoveLibraryItem moveLibraryItem = moveLibraryCloseItem.getMoveLibraryItem();
				if (ConvertUtil.isEmpty(moveLibraryItem)) {
					ExceptionUtil.throwServiceException("移库单明细不存在");
				}
				MoveLibrary moveLibrary = moveLibraryItem.getMoveLibrary();
				if (ConvertUtil.isEmpty(moveLibraryItem)) {
					ExceptionUtil.throwServiceException("移库单不存在");
				}
				// 校验单据参数
				this.checkBillParams(moveLibrary.getIssueWarehouse(), warehouseIds, moveLibrary.getSaleOrg(),
						saleOrgIds, moveLibrary.getSbu(), sbuIds, moveLibrary.getSn());
			}
		}
		// 仓库
		map.put("warehouseId", warehouseIds[0]);
		// 机构
		map.put("saleOrgId", saleOrgIds[0]);
		// sbu
		map.put("sbuId", sbuIds[0]);
		return map;
	}

	/**
	 * 移库发货选择列表
	 */
	@RequestMapping(value = "/check_movelibrary_issue", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkMovelibraryIssue(Long[] ids) {
		HashMap<String, Object> map = this.getMovelibraryBillHashMap(ids, 0);
		return success(JsonUtils.toJson(map));
	}

	/**
	 * 移库发货添加页面
	 */
	@RequestMapping(value = "/movelibrary_issue_add", method = RequestMethod.GET)
	public String movelibraryIssueadd(Long[] ids, Long warehouseId, Long sbuId, Long menuId, Long orderIndex,
			Long saleOrgId, ModelMap model) {
		model.addAttribute("menuId", menuId);
		model.addAttribute("ids", ids);
		model.addAttribute("orderIndex", orderIndex);
		// 添加单据ModelMap
		this.addBillModelMap(null, warehouseId, saleOrgId, sbuId, "出仓", "转仓出仓", null, null, null, null, null, null,
				model);
		// 备注
		List<Long> billIds = new ArrayList<Long>();
		String memo = "";
		String sourceNo = "";
		String sourceNoId = "";
		for (Long id : ids) {
			MoveLibraryItem moveLibraryItem = moveLibraryItemService.find(id);
			MoveLibrary moveLibrary = moveLibraryItem.getMoveLibrary();
			if (!ConvertUtil.isEmpty(moveLibrary.getId()) && !billIds.contains(moveLibrary.getId())) {
				billIds.add(moveLibrary.getId());
				if (!ConvertUtil.isEmpty(moveLibrary.getRemarks())) {
					memo = moveLibrary.getRemarks();
				}
			}
			// 来源单
			if (!ConvertUtil.isEmpty(moveLibrary.getSn()) && !sourceNo.contains(moveLibrary.getSn())) {
				if (ConvertUtil.isEmpty(sourceNo)) {
					sourceNo = moveLibrary.getSn();
				} else {
					sourceNo = sourceNo + "、" + moveLibrary.getSn();
				}
			}
			// 来源单id
			sourceNoId = String.valueOf(moveLibrary.getId());
		}
		model.addAttribute("sourceNo", sourceNo);
		model.addAttribute("sourceNoId", sourceNoId);
		model.addAttribute("memo", memo);
		return "/b2b/sale_shipping/movelibrary_issue_add";
	}

	/**
	 * 移库发货、接收添加页列表数据
	 */
	@RequestMapping(value = "/movelibraryItem_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg movelibraryItemListData(Long[] ids, Integer type) {
		List<Map<String, Object>> mapList = moveLibraryService.findMovelibraryItemList(ids, type);
		String jsonPage = JsonUtils.toJson(mapList);
		return success(jsonPage);
	}

	/**
	 * 移库发货保存接口
	 * 
	 * @param amShipping
	 * @param storeId
	 * @param warehouseId
	 * @param saleOrgId
	 * @param organizationId
	 * @param sbuId
	 * @param billTypeId
	 * @param billCategoryId
	 * @return
	 */
	@RequestMapping(value = "/movelibrary_issue_save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg movelibraryIssueSave(AmShipping amShipping, Long storeId, Long warehouseId,
			Long saleOrgId, Long organizationId, Long sbuId, Long billTypeId, Long billCategoryId,AmShippingItem amShippingItem) {

		// 客户
		if (!ConvertUtil.isEmpty(storeId)) {
			Store store = storeBaseService.find(storeId);
			amShipping.setStore(store);
		}
		// 仓库
		if (!ConvertUtil.isEmpty(warehouseId)) {
			Warehouse warehouse = warehouseBaseService.find(warehouseId);
			amShipping.setWarehouse(warehouse);
		}
		// 机构
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
			amShipping.setSaleOrg(saleOrg);
		}
		// 经营组织
		if (!ConvertUtil.isEmpty(organizationId)) {
			Organization organization = organizationService.find(organizationId);
			amShipping.setOrganization(organization);

		}
		// Sbu
		if (!ConvertUtil.isEmpty(organizationId)) {
			Sbu sbu = sbuService.find(sbuId);
			amShipping.setSbu(sbu);
		}
		// 单据类型
		SystemDict billType = systemDictService.find(billTypeId);
		if (ConvertUtil.isEmpty(billType)) {
			return error("单据类型不能为空");
		}
		amShipping.setBillType(billType);
		if (amShipping.getAmShippingItems().isEmpty()) {
			return error("请添加单据行项");
		}
		// 单据类别
		SystemDict billCategory = systemDictService.find(billCategoryId);
		if (ConvertUtil.isEmpty(billCategory)) {
			return error("单据类别不能为空");
		}
		amShipping.setBillCategory(billCategory);
		// 创建人
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if (!ConvertUtil.isEmpty(storeMember)) {
			amShipping.setStoreMember(storeMember);
		}

		saleShippingNatureService.movelibraryIssueSave(amShipping,amShippingItem, billType);

		return success().addObjX(amShipping.getId());
	}

	/**
	 * 移库发货查看页面
	 * 
	 * @param id
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/movelibrary_issue_view", method = RequestMethod.GET)
	public String movelibrary_issue_view(Long id, ModelMap model, Long menuId, Long orderIndex,Integer sourceType) throws Exception {

		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
		AmShipping amShipping = saleShippingNatureService.find(id);
		model.addAttribute("amShipping", amShipping);
        model.addAttribute("sourceType", sourceType);

		Integer auditType = 0;
		if (!amShipping.getAmShippingItems().isEmpty() && amShipping.getAmShippingItems().size() > 0) {
			for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
				if (!ConvertUtil.isEmpty(amShippingItem.getMovelibraryIssue())
						&& !ConvertUtil.isEmpty(amShippingItem.getMovelibraryIssue().getId())) {
					auditType++;
					break;
				}
			}
		}
		model.addAttribute("auditType", auditType);
		List<Map<String, Object>> list = saleShippingNatureService.findAmShippingItemListByShippingId(id.toString(),
				null);
		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(amShipping.getSn(), 4));
		model.addAttribute("fullLink_json", fullLink_json);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(57L);
		model.addAttribute("isCheckWf", isCheckWf);

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("isMember", storeMember.getMemberType());

		// sbu
		if (!ConvertUtil.isEmpty(amShipping.getSbu())) {
			Sbu sbu = amShipping.getSbu();
			model.addAttribute("sbu", sbu.getId());
		}

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> listString = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}

		// 产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null, filters, null);
		model.addAttribute("productLevelList", productLevelList);

		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 新旧标识
		filters.clear();
		filters.add(Filter.eq("code", "oldNewLogo"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
		model.addAttribute("newOldLogosList", newOldLogosList);

		// 是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(linkStockValue)) {
			Integer linkStock = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}

		// 批次列表
		Integer warehouseBatchItemIndex = 0;
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("amShipping", amShipping.getId()));
		List<WarehouseBatchItem> warehouseBatchItemList = warehouseBatchItemService.findList(null, filters, null);
		if (!warehouseBatchItemList.isEmpty() && warehouseBatchItemList.size() > 0) {
			List<Map<String, Object>> vWarehouseBatchItemList = warehouseBatchItemService.findVWarehouseBatchItemList(
					amShipping.getId(), null, null, null, null, null, null, null, null, null, null, null, null);
			warehouseBatchItemIndex = warehouseBatchItemList.size();
			model.addAttribute("vWarehouseBatchItemList", JsonUtils.toJson(vWarehouseBatchItemList));
		} else {
			model.addAttribute("vWarehouseBatchItemList", JsonUtils.toJson(new ArrayList<Map<String, Object>>()));
		}
		model.addAttribute("warehouseBatchItemIndex", warehouseBatchItemIndex);

		// 仓储管理是否启用库存查询 0不展示、1展示
		Integer storageStockQueryRoles = roleJurisdictionUtil.getRoleCount("storageStockQueryRoles");
		model.addAttribute("storageStockQueryRoles", storageStockQueryRoles);

		// 是否订货批次出仓
		Integer isOrderWarehousingBatch = getIsOrderWarehousingBatch(amShipping.getWarehouse());
		model.addAttribute("isOrderWarehousingBatch", isOrderWarehousingBatch);

		return "/b2b/sale_shipping/movelibrary_issue_view";
	}

	/**
	 * 移库发货更新接口
	 * 
	 * @param amShipping
	 * @param billTypeId
	 * @return
	 */
	@RequestMapping(value = "/movelibrary_issue_update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg movelibraryIssueUpdate(AmShipping amShipping, Long billTypeId,
			AmShippingItem amShippingItem) {

		//判断订单中的产品与产品资料中的转换率是否一样
//		for (AmShippingItem amShippingItems:amShipping.getAmShippingItems()){
//		    if(amShippingItems.getProduct()!=null) {
//                Product product = productService.find(amShippingItems.getProduct().getId());
//                if (amShippingItems.getBranchPerBox().compareTo(product.getBranchPerBox()) != 0) {
//                    ExceptionUtil.throwServiceException("订单中的【" + product.getVonderCode() + "】产品与产品资料的转换率不相符");
//                }
//            }
//		}
		if (amShipping.getAmShippingItems().isEmpty()) {
			return error("请添单据行项");
		}
		saleShippingNatureService.movelibraryIssueUpdate(amShipping, amShippingItem);
		return success();
	}

	/*
	 * 移库接收列表
	 */
	@RequestMapping(value = "/movelibrary_receive_list", method = RequestMethod.GET)
	public String movelibraryReceiveList(ModelMap model, Long userId, Long menuId) {
		model.addAttribute("menuId", menuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/sale_shipping/movelibrary_receive_list";
	}

	public ModelAndView getMovelibraryReceiveModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {

			// 需求日期
			if (!ConvertUtil.isEmpty(str.get("need_date"))) {
				String need_date = str.get("need_date").toString();
				str.put("need_date", need_date.substring(0, 19));
			}
			// 发出日期
			if (!ConvertUtil.isEmpty(str.get("issue_date"))) {
				String issue_date = str.get("issue_date").toString();
				str.put("issue_date", issue_date.substring(0, 19));
			}
			// 创建日期
			if (!ConvertUtil.isEmpty(str.get("create_date"))) {
				String create_date = str.get("create_date").toString();
				str.put("create_date", create_date.substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "单据编号", "机构", "目的仓", "SBU", "需求日期", "产品名称", "产品描述", "产品型号", "产品规格", "产品编码", "经营组织", "产品等级",
				"制单箱数", "支数", "零散支数", "数量", "接收数量", "接收箱数", "接收支数", "接收日期", "含水率", "色号", "批次", "创建人", "创建日期", "备注" };

		// 设置单元格取值
		String[] properties = { "sn", "sale_org_name", "receive_name", "sbu_name", "need_date", "product_name",
				"detail_description", "product_model", "product_spec", "vonder_code", "product_organization_name",
				"levelName", "available_receive_box_quantity", "available_receive_branch_quantity",
				"available_receive_scattered_quantity", "available_receive_quantity", "receive_quantity",
				"receive_box_quantity", "receive_branch_quantity", "receive_date", "moisture_content_name",
				"colour_number_name", "batch_encoding", "store_member_name", "create_date",
				"move_library_item_remarks" };

		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}

	/**
	 * 移库接收选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_movelibrary_receive_export", method = RequestMethod.GET)
	public ModelAndView selectedMovelibraryReceiveExport(Long[] ids, Boolean movelibrarySign, ModelMap model) {

		List<Map<String, Object>> data = moveLibraryService.findMovelibraryList(null, null, ids, null, null, null, null,
				null, null, null, null, null, null, null, movelibrarySign);

		return getMovelibraryReceiveModelAndViewForList(data, model);
	}

	/**
	 * 移库接收条件导出
	 * 
	 * @param page
	 * @param sn
	 * @param saleOrgId
	 * @param issueWarehouseId
	 * @param receiveWarehouseId
	 * @param sbuId
	 * @param productId
	 * @param startIssueDate
	 * @param endIssueDate
	 * @param startReceiveDate
	 * @param endReceiveDate
	 * @param creatorId
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/movelibrary_receive_condition_export", method = RequestMethod.GET)
	public ModelAndView movelibraryReceiveConditionExport(Integer page, String sn, Long[] saleOrgId,
			Long[] issueWarehouseId, Long[] receiveWarehouseId, Long[] sbuId, Long[] productId, String startIssueDate,
			String endIssueDate, String startReceiveDate, String endReceiveDate, Long[] creatorId,
			Boolean movelibrarySign, ModelMap model) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");

		List<Map<String, Object>> data = moveLibraryService.findMovelibraryList(page, size, null, sn, saleOrgId,
				issueWarehouseId, receiveWarehouseId, sbuId, productId, startIssueDate, endIssueDate, startReceiveDate,
				endReceiveDate, creatorId, movelibrarySign);

		return getMovelibraryReceiveModelAndViewForList(data, model);
	}

	/**
	 * 移库接收选择列表
	 */
	@RequestMapping(value = "/check_movelibrary_receive", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkMovelibraryReceive(Long[] ids) {

		HashMap<String, Object> map = this.getMovelibraryBillHashMap(ids, 1);

		return success(JsonUtils.toJson(map));
	}

	/**
	 * 移库接收添加页面
	 */
	@RequestMapping(value = "/movelibrary_receive_add", method = RequestMethod.GET)
	public String movelibraryReceiveadd(Long[] ids, Long warehouseId, Long saleOrgId, Long menuId, Long orderIndex,
			Long sbuId, ModelMap model) {
		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
		model.addAttribute("ids", ids);
		
		
		// 添加单据ModelMap
		this.addBillModelMap(null, warehouseId, saleOrgId, sbuId, "入仓", "转仓入仓", null, null, null, null, null, null,
				model);
		// 备注
		List<Long> billIds = new ArrayList<Long>();
		String memo = "";
		String sourceNo = "";
		String sourceNoId = "";
		for (Long id : ids) {
			MoveLibraryItem moveLibraryItem = moveLibraryItemService.find(id);
			MoveLibrary moveLibrary = moveLibraryItem.getMoveLibrary();
			if (!ConvertUtil.isEmpty(moveLibrary.getId()) && !billIds.contains(moveLibrary.getId())) {
				billIds.add(moveLibrary.getId());
				if (!ConvertUtil.isEmpty(moveLibrary.getRemarks())) {
					memo = moveLibrary.getRemarks();
				}
			}
			if (!ConvertUtil.isEmpty(moveLibrary.getSn()) && !sourceNo.contains(moveLibrary.getSn())) {
				if (ConvertUtil.isEmpty(sourceNo)) {
					sourceNo = moveLibrary.getSn();
				} else {
					sourceNo = sourceNo + "、" + moveLibrary.getSn();
				}
			}
			// 来源单id
			sourceNoId = String.valueOf(moveLibrary.getId());
			
		}
		model.addAttribute("memo", memo);
		model.addAttribute("sourceNo", sourceNo);
		model.addAttribute("sourceNoId", sourceNoId);
		
		// 单据类型
		SystemDict billType = roleJurisdictionUtil.getSystemDict("billType", "入仓");
						
					
					
		// 批次列表
		Integer warehouseBatchItemIndex = 0;
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
					
		List<Map<String, Object>> vWarehouseBatchItemList = warehouseBatchItemService.findVWarehouseBatchItemListBySourceNoId(
		    Long.valueOf(sourceNoId),billType.getId(),ids );
			warehouseBatchItemIndex = vWarehouseBatchItemList.size();
			model.addAttribute("vWarehouseBatchItemList", JsonUtils.toJson(vWarehouseBatchItemList));
					
		model.addAttribute("warehouseBatchItemIndex", warehouseBatchItemIndex);
		
		return "/b2b/sale_shipping/movelibrary_receive_add";
	}

	/**
	 * 移库接收查看页面
	 * 
	 * @param id
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/movelibrary_receive_view", method = RequestMethod.GET)
	public String movelibrary_receive_view(Long id, ModelMap model, Long menuId, Long orderIndex,Integer sourceType) throws Exception {

		AmShipping amShipping = saleShippingNatureService.find(id);
		model.addAttribute("amShipping", amShipping);
		model.addAttribute("menuId", menuId);
		model.addAttribute("orderIndex", orderIndex);
        model.addAttribute("sourceType",sourceType);

		Integer auditType = 0;
		if (!amShipping.getAmShippingItems().isEmpty() && amShipping.getAmShippingItems().size() > 0) {
			for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
				if (!ConvertUtil.isEmpty(amShippingItem.getMovelibraryReceive())
						&& !ConvertUtil.isEmpty(amShippingItem.getMovelibraryReceive().getId())) {
					auditType++;
					break;
				}

			}
		}
		model.addAttribute("auditType", auditType);

		List<Map<String, Object>> list = saleShippingNatureService.findAmShippingItemListByShippingId(id.toString(),
				null);
		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(amShipping.getSn(), 4));
		model.addAttribute("fullLink_json", fullLink_json);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(57L);
		model.addAttribute("isCheckWf", isCheckWf);

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("isMember", storeMember.getMemberType());

		// sbu
		if (!ConvertUtil.isEmpty(amShipping.getSbu())) {
			Sbu sbu = amShipping.getSbu();
			model.addAttribute("sbu", sbu.getId());
		}

		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		// 订单下达是否展示色号、含水率、批次 0 不展示 非0 展示
		try {
			String value = SystemConfig.getConfig("hiddenBatchRoles", WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> listString = Arrays.asList(perRole);
			int hiddenBatch = 0;
			for (PcUserRole userRole : userRoles) {
				if (listString.contains(userRole.getPcRole().getName())) {
					hiddenBatch++;
					break;
				}
			}
			model.addAttribute("hiddenBatch", hiddenBatch);
		} catch (RuntimeException e) {

		}

		// 产品级别
		filters.clear();
		filters.add(Filter.eq("code", "productLevel"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> productLevelList = systemDictService.findList(null, filters, null);
		model.addAttribute("productLevelList", productLevelList);

		// 色号
		filters.clear();
		filters.add(Filter.eq("code", "colorNumber"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> colorNumberList = systemDictService.findList(null, filters, null);
		model.addAttribute("colorNumberList", colorNumberList);

		// 含水率
		filters.clear();
		filters.add(Filter.eq("code", "moistureContent"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> moistureContentList = systemDictService.findList(null, filters, null);
		model.addAttribute("moistureContentList", moistureContentList);

		// 新旧标识
		filters.clear();
		filters.add(Filter.eq("code", "oldNewLogo"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> newOldLogosList = systemDictService.findList(null, filters, null);
		model.addAttribute("newOldLogosList", newOldLogosList);

		// 是否启用LINK库存 0不启用 1启用
		String linkStockValue = SystemConfig.getConfig("linkStock", WebUtils.getCurrentCompanyInfoId());
		if (!ConvertUtil.isEmpty(linkStockValue)) {
			Integer linkStock = Integer.valueOf(linkStockValue);
			model.addAttribute("linkStock", linkStock);
		}
		
		try {
			String value = SystemConfig.getConfig("batchButton",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = value.split(",");
			List<String> lists = Arrays.asList(perRole);
			int batchButton = 0;
			if (lists.contains(amShipping.getBillType().getValue())) {
				batchButton++;
			}

			model.addAttribute("batchButton", batchButton); 
		}catch (RuntimeException e) {

		}

		List<Map<String, Object>> vWarehouseBatchItemList = warehouseBatchItemService.findVWarehouseBatchItemList(
				amShipping.getId(), null, null, null, null, null, null, null, null, null, null, null, null);
		model.addAttribute("vWarehouseBatchItemList", JsonUtils.toJson(vWarehouseBatchItemList));

		// 仓储管理是否启用库存查询 0不展示、1展示
		Integer storageStockQueryRoles = roleJurisdictionUtil.getRoleCount("storageStockQueryRoles");
		model.addAttribute("storageStockQueryRoles", storageStockQueryRoles);

		return "/b2b/sale_shipping/movelibrary_receive_view";
	}

	/**
	 * 移库发货流程审批
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/move_library_issue_check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg movelibraryIssueCheckWf(Long id) throws Exception {
		saleShippingNatureService.checkWfMovelibraryIssue(id);
		return success();
	}

	/**
	 * 移库接收流程审批
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/move_library_receive_check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg movelibraryReceiveCheckWf(Long id) throws Exception {
		saleShippingNatureService.checkWfMovelibraryReceive(id);
		return success();
	}

	/**
	 * 设置旗标
	 * 
	 * @param ids
	 * @param flag
	 * @return
	 */
	@RequestMapping(value = "/setFlag", method = RequestMethod.POST)
	public @ResponseBody ResultMsg setFlag(Long[] ids, Integer flag) {
		if (ConvertUtil.isEmpty(ids) || ids.length == 0) {
			return error("请选择订单");
		}
		if (ConvertUtil.isEmpty(flag)) {
			// 请选择旗标
			return error("15250");
		}
		saleShippingNatureService.setFlag(ids, flag);
		return success();
	}

	/*
	 * 移库关闭列表
	 */
	@RequestMapping(value = "/movelibrary_close_list", method = RequestMethod.GET)
	public String movelibrary_close_list(ModelMap model, Long menuId, Long userId) {
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b2b/sale_shipping/movelibrary_close_list";
	}

	/*
	 * 移库关闭列表数据
	 */
	@RequestMapping(value = "/movelibrary_close_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg movelibrary_close_list_data(String sn, Long[] saleOrgId, Long[] issueWarehouseId,
			Long[] sbuId, Long[] productId, String startCloseDate, String endCloseDate, Pageable pageable) {

		Page<Map<String, Object>> page = moveLibraryCloseService.newFindMovelibraryClosePage(sn, saleOrgId,
				issueWarehouseId, sbuId, productId, startCloseDate, endCloseDate, null, pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);

	}

	public ModelAndView getMovelibraryCloseModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			// 需求日期
			if (!ConvertUtil.isEmpty(str.get("need_date"))) {
				String need_date = str.get("need_date").toString();
				str.put("need_date", need_date.substring(0, 19));
			}
			// 关闭日期
			if (!ConvertUtil.isEmpty(str.get("close_date"))) {
				String issue_date = str.get("close_date").toString();
				str.put("close_date", issue_date.substring(0, 19));
			}
			// 创建日期
			if (!ConvertUtil.isEmpty(str.get("create_date"))) {
				String create_date = str.get("create_date").toString();
				str.put("create_date", create_date.substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "单据编号", "机构", "来源仓", "SBU", "需求日期", "产品名称", "产品描述", "产品型号", "产品规格", "产品编码", "经营组织", "产品等级",
				"件数", "支数", "零散支数", "数量", "发出数量", "接收箱数", "关闭支数", "关闭日期", "含水率", "色号", "批次", "退货原因", "物流单号", "物流公司",
				"创建人", "创建日期", "备注" };

		// 设置单元格取值
		String[] properties = { "sn", "sale_org_name", "issue_name", "sbu_name", "need_date", "product_name",
				"detail_description", "product_model", "product_spec", "vonder_code", "product_organization_name",
				"levelName", "box_quantity", "branch_quantity", "scattered_quantity", "quantity", "issue_quantity",
				"receive_quantity", "mli_close_quantity", "close_date", "moisture_content_name", "colour_number_name",
				"batch_encoding", "close_reason_name", "logistics_sn", "logistics_type_name", "b_creater",
				"create_date", "remarks" };

		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}

	/**
	 * 移库关闭选择导出
	 * 
	 * @param ids
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/selected_movelibrary_close_export", method = RequestMethod.GET)
	public ModelAndView selected_movelibrary_close_export(Long[] ids, ModelMap model, Pageable pageable) {

		Page<Map<String, Object>> page = moveLibraryCloseService.newFindMovelibraryClosePage(null, null, null, null,
				null, null, null, ids, pageable);

		List<Map<String, Object>> data = page.getContent();

		return getMovelibraryCloseModelAndViewForList(data, model);
	}

	/**
	 * 移库关闭导出统计总记录数
	 * 
	 * @param sn
	 * @param saleOrgId
	 * @param issueWarehouseId
	 * @param sbuId
	 * @param productId
	 * @param startCloseDate
	 * @param endCloseDate
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/to_movelibrary_close_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> to_movelibrary_close_condition_export(String sn, Long[] saleOrgId,
			Long[] issueWarehouseId, Long[] sbuId, Long[] productId, String startCloseDate, String endCloseDate,
			Pageable pageable) {

		Page<Map<String, Object>> page = moveLibraryCloseService.newFindMovelibraryClosePage(sn, saleOrgId,
				issueWarehouseId, sbuId, productId, startCloseDate, endCloseDate, null, pageable);

		Integer size = (int) page.getTotal();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 移库关闭条件导出
	 * 
	 * @param sn
	 * @param saleOrgId
	 * @param issueWarehouseId
	 * @param sbuId
	 * @param productId
	 * @param startCloseDate
	 * @param endCloseDate
	 * @param page
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/movelibrary_close_condition_export", method = RequestMethod.GET)
	public ModelAndView movelibrary_close_condition_export(String sn, Long[] saleOrgId, Long[] issueWarehouseId,
			Long[] sbuId, Long[] productId, String startCloseDate, String endCloseDate, Integer page, Pageable pageable,
			ModelMap model) {

		Map<String, Integer> segments = getSegment();
		// 每页显示多少条
		pageable.setPageSize(segments.get("size"));
		// 当前页码
		pageable.setPageNumber(page);

		Page<Map<String, Object>> pageMap = moveLibraryCloseService.newFindMovelibraryClosePage(sn, saleOrgId,
				issueWarehouseId, sbuId, productId, startCloseDate, endCloseDate, null, pageable);

		List<Map<String, Object>> data = pageMap.getContent();

		return getMovelibraryCloseModelAndViewForList(data, model);
	}

	/**
	 * 移库关闭选择列表
	 */
	@RequestMapping(value = "/check_movelibrary_close", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_movelibrary_close(Long[] ids) {

		HashMap<String, Object> map = this.getMovelibraryBillHashMap(ids, 2);

		return success(JsonUtils.toJson(map));
	}

	/**
	 * 移库关闭添加页面
	 */
	@RequestMapping(value = "/movelibrary_close_add", method = RequestMethod.GET)
	public String movelibrary_close_add(Long[] ids, Long sbuId, Long warehouseId, Long saleOrgId, ModelMap model,String id) {
        String[] split = id.split(",");//以逗号分割
        StringBuilder moveLibraryCloseSn=new StringBuilder();
        for (String idStr : split) {
            //移库关闭单
            MoveLibraryClose moveLibraryClose = moveLibraryCloseService.find(Long.valueOf(idStr));
            moveLibraryCloseSn=moveLibraryCloseSn.append(moveLibraryClose.getMoveLibrary().getSn()+",");
        }
		//移库关闭单
		model.addAttribute("moveLibraryCloseSn", moveLibraryCloseSn.deleteCharAt(moveLibraryCloseSn.length()-1).toString());
        model.addAttribute("ids", ids);
		// 添加单据ModelMap
		this.addBillModelMap(null, warehouseId, saleOrgId, sbuId, "入仓", "返挑入仓", null, null, null, null, null, null,
				model);
		return "/b2b/sale_shipping/movelibrary_close_add";
	}

	/**
	 * 移库关闭添加页面列表数据
	 */
	@RequestMapping(value = "/movelibrary_close_add_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg movelibrary_close_add_list_data(Long[] ids) {

		List<Map<String, Object>> mapList = moveLibraryCloseService.findMoveLibraryCloseItemListData(ids);
		String jsonPage = JsonUtils.toJson(mapList);
		return success(jsonPage);
	}

	/**
	 * 移库关闭查看页面
	 * 
	 * @param id
	 * @param model
	 * @param moveLibraryCloseSn
	 * @return
	 * @throws Exception
	 * @Memo 当从出入库信息入口进入时，moveLibraryCloseSn是为空的所以要从新去数据库再找一次
	 */
	@RequestMapping(value = "/movelibrary_close_view", method = RequestMethod.GET)
	public String movelibrary_close_view(Long id, ModelMap model,String moveLibraryCloseSn) throws Exception {

		AmShipping amShipping = saleShippingNatureService.find(id);
		model.addAttribute("amShipping", amShipping);

		if(moveLibraryCloseSn.equals("null")) {
			List<Map<String,Object>> amShippingItem = amShippingItemService.getAmShippingItemMoveClose(id);
			StringBuilder moveLibraryCloseSnS = new StringBuilder();
			for(int i=0;i<amShippingItem.size();i++) {
				for (Map.Entry<String, Object> closeId : amShippingItem.get(i).entrySet()) {
					//移库关闭单
					MoveLibraryClose moveLibraryClose = moveLibraryCloseService.find(Long.valueOf(closeId.getValue().toString()));
					moveLibraryCloseSnS = moveLibraryCloseSnS.append(moveLibraryClose.getMoveLibrary().getSn() + ",");
				}
			}
			//移库关闭单
			model.addAttribute("moveLibraryCloseSn", moveLibraryCloseSnS.deleteCharAt(moveLibraryCloseSnS.length() - 1).toString());
		}else {
			model.addAttribute("moveLibraryCloseSn", moveLibraryCloseSn);
		}
		Integer auditType = 0;
		if (!amShipping.getAmShippingItems().isEmpty() && amShipping.getAmShippingItems().size() > 0) {
			for (AmShippingItem amShippingItem : amShipping.getAmShippingItems()) {
				if (!ConvertUtil.isEmpty(amShippingItem.getMoveLibraryClose())) {
					auditType++;
					break;
				}
			}
		}
		model.addAttribute("auditType", auditType);

		List<Map<String, Object>> list = saleShippingNatureService.findAmShippingItemListByShippingId(id.toString(),
				null);
		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(amShipping.getSn(), 4));
		model.addAttribute("fullLink_json", fullLink_json);

		// 添加单据ModelMap
		this.addBillModelMap(null, null, null, null, null, null, null, null, null, null, null, null, model);

		// 批次列表
		Integer warehouseBatchItemIndex = 0;
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("amShipping", amShipping.getId()));
		List<WarehouseBatchItem> warehouseBatchItemList = warehouseBatchItemService.findList(null, filters, null);
		if (!warehouseBatchItemList.isEmpty() && warehouseBatchItemList.size() > 0) {
			List<Map<String, Object>> vWarehouseBatchItemList = warehouseBatchItemService.findVWarehouseBatchItemList(
					amShipping.getId(), null, null, null, null, null, null, null, null, null, null, null, null);
			warehouseBatchItemIndex = warehouseBatchItemList.size();
			model.addAttribute("vWarehouseBatchItemList", JsonUtils.toJson(vWarehouseBatchItemList));
		}
		model.addAttribute("warehouseBatchItemIndex", warehouseBatchItemIndex);

		return "/b2b/sale_shipping/movelibrary_close_view";
	}

	/**
	 * 移库关闭流程审批
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/move_library_close_check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg move_library_close_check_wf(Long id) throws Exception {
		saleShippingNatureService.checkWfMovelibraryClose(id);
		return success();
	}

	/**
	 * 设置单据明细旗标
	 * 
	 * @param ids
	 * @param flag
	 * @param billType
	 * @return
	 */
	@RequestMapping(value = "/setBillItemFlag", method = RequestMethod.POST)
	public @ResponseBody ResultMsg setBillItemFlag(Long[] ids, Integer flag, Integer billType) {
		if (ConvertUtil.isEmpty(ids) || ids.length == 0) {
			return error("请选择单据行");
		}
		if (ConvertUtil.isEmpty(flag)) {
			return error("15250");
		}
		if (ConvertUtil.isEmpty(billType)) {
			return error("单据类型不存在，请联系管理员进行维护");
		}
		saleShippingNatureService.setBillItemFlag(ids, flag, billType);
		return success();
	}

	/**
	 * 是否订货批次出仓
	 * 
	 * @param warehouse
	 * @return
	 */
	public Integer getIsOrderWarehousingBatch(Warehouse warehouse) {
		if (!ConvertUtil.isEmpty(warehouse) && !ConvertUtil.isEmpty(warehouse.getIsOrderWarehousingBatch())
				&& warehouse.getIsOrderWarehousingBatch()) {
			return 1;
		}
		return 0;
	}



    /**
     * 撤回待发货的出入库单（调用java接口）
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/withdraw", method = RequestMethod.POST)
    public @ResponseBody ResultMsg withdraw(Long id) {

        AmShipping amShipping = saleShippingNatureService.find(id);

        intfOrderMessageToService.saveAmShippingIntf(amShipping,2);
		intfLogisticsJob.shippingInft();
        return success();
    }



    /**
     * Excel导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/import_excel", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg importFromExcel(MultipartFile file) throws Exception {
        amShippingService.importExcel(file);
        try {
            return ResultMsg.success();
        }
        catch (Exception e) {
            LogUtils.error("导入出入库单", e);
            return ResultMsg.error(e.getMessage());
        }
    }
}
