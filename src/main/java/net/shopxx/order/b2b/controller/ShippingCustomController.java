package net.shopxx.order.b2b.controller;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.DeliveryCorp;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.DriverInfoService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.PaymentMethodBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.ShippingMethodBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PolicyCountContractService;
import net.shopxx.finance.service.PolicyCountService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.CreditRechargeContractService;
import net.shopxx.member.service.DepositRechargeContractService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.order.entity.Shipping;
import net.shopxx.order.entity.ShippingAttach;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.entity.TriplicateForm;
import net.shopxx.order.purchase.service.PurchaseShippingService;
import net.shopxx.order.service.OrderCustomService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.order.service.OrderItemService;
import net.shopxx.order.service.ShippingCustomService;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.product.service.ProductSuiteService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseStore;
import net.shopxx.stock.service.DeliveryCorpBaseService;
import net.shopxx.stock.service.StockService;
import net.shopxx.stock.service.WarehouseBaseService;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.util.kualidi.DeliveryUtil;
import net.shopxx.wf.service.WfObjConfigBaseService;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

@Controller("b2bShippingCustomController")
@RequestMapping("/b2b/shipping_custom")
public class ShippingCustomController extends BaseController {

	@Resource(name = "shippingCustomServiceImpl")
	private ShippingCustomService shippingCustomService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "orderCustomServiceImpl")
	private OrderCustomService orderCustomService;
	@Resource(name = "deliveryCorpBaseServiceImpl")
	private DeliveryCorpBaseService deliveryCorpBaseService;
	@Resource(name = "shippingMethodBaseServiceImpl")
	private ShippingMethodBaseService shippingMethodBaseService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "driverInfoServiceImpl")
	private DriverInfoService driverInfoBaseService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreBaseService;
	@Resource(name = "stockServiceImpl")
	private StockService stockService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "purchaseShippingServiceImpl")
	private PurchaseShippingService purchaseShippingService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "paymentMethodBaseServiceImpl")
	private PaymentMethodBaseService paymentMethodBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "creditRechargeContractServiceImpl")
	private CreditRechargeContractService creditRechargeContractService;
	@Resource(name = "depositRechargeContractServiceImpl")
	private DepositRechargeContractService depositRechargeContractService;
	@Resource(name = "policyCountContractServiceImpl")
	private PolicyCountContractService policyCountContractService;
	@Resource(name = "policyCountServiceImpl")
	private PolicyCountService policyCountService;
	@Resource(name = "productSuiteServiceImpl")
	private ProductSuiteService productSuiteService;

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));

		String useLockStock = "0";
		try {
			// 启用锁库存模式：0 否，1 是
			useLockStock = SystemConfig.getConfig("useLockStock",
					WebUtils.getCurrentCompanyInfoId());
		}
		catch (Exception e) {}
		model.addAttribute("useLockStock", useLockStock);

		return "/b2b/shipping_custom/add";
	}

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, Integer flag, Long objTypeId,
			Long objid, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		return "/b2b/shipping_custom/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {

		return "/b2b/shipping_custom/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn, String orderSn, String outTradeNo,
			String trackingNo, Integer[] status, Integer[] acceptStatus,
			Long[] warehouseId, String warehouseName, Long deliveryCorpId,
			Integer warehouseType, Long[] storeId, Long supplierId,
			String firstTime, String lastTime, Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = shippingCustomService.newfindPage(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				acceptStatus,
				warehouseId,
				deliveryCorpId,
				warehouseType,
				storeId,
				supplierId,
				firstTime,
				lastTime,
				null,
				pageable);

		List<Map<String, Object>> shippings = page.getContent();

		if (!shippings.isEmpty()) {
			String ids = "";
			for (int i = 0; i < shippings.size(); i++) {
				Map<String, Object> map = shippings.get(i);
				if (i == shippings.size() - 1) {
					ids += map.get("id");
				}
				else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> shippingItems = shippingCustomService.findShippingItemListByShippingId(ids);
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : shippings) {
				items = new ArrayList<Map<String, Object>>();
				String shippingId = map.get("id").toString();
				for (Map<String, Object> itemMap : shippingItems) {
					String oid = itemMap.get("shipping").toString();
					int bom_flag = Integer.parseInt(itemMap.get("bom_flag")
							.toString());
					if (shippingId.equals(oid) && bom_flag != 1) {
						items.add(itemMap);
					}
				}
				map.put("shipping_items", items);
			}
		}
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_out", method = RequestMethod.GET)
	public String list_out(Pageable pageable, ModelMap model) {

		return "/b2b/shipping_custom/list_out";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_out_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg listOutData(String sn, String orderSn, String outTradeNo,
			String trackingNo, Integer[] status, Long[] warehouseId,
			String warehouseName, Long deliveryCorpId, Integer warehouseType,
			Long[] storeId, Long supplierId, Long productId, String firstTime,
			String lastTime, String productModel, Pageable pageable,
			ModelMap model) {

		if (status == null || status.length == 0)
			status = new Integer[] { 1, 2, 3, 4 };
		Page<Map<String, Object>> page = shippingCustomService.newfindItemPage(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseId,
				deliveryCorpId,
				productId,
				null,
				null,
				new Integer[] { 0, 2 },
				new Integer[] { 2, 3 },
				storeId,
				supplierId,
				null,
				null,
				productModel,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_shipping_item", method = RequestMethod.GET)
	public String select_shipping_item(Integer flag, Integer multi,
			ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("multi", multi);
		return "/b2b/shipping_custom/select_shipping_item";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_shipping_item_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_shipping_item_data(String sn, String orderSn,
			String trackingNo, Integer[] status, String warehouseName,
			String storeName, String productName, String model,
			String vonderCode, Integer flag, Pageable pageable) {

		if (flag != null && flag == 1) {
			// 销售发票，完全发货
			status = new Integer[] { 4 };
		}
		else if (status == null || status.length == 0) {
			status = new Integer[] { 1, 2, 3, 4 };
		}
		Page<Map<String, Object>> page = shippingCustomService.findItemPageForWin(sn,
				orderSn,
				null,
				trackingNo,
				status,
				null,
				null,
				null,
				null,
				null,
				new Integer[] { 0, 1 },
				new Integer[] { 2 },
				null,
				null,
				storeName,
				warehouseName,
				productName,
				model,
				vonderCode,
				flag,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 查看
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, Integer readOnly, Integer flag, ModelMap model)
			throws Exception {

		Shipping shipping = shippingCustomService.find(id);
		model.addAttribute("shipping", shipping);
		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		if (shipping != null) {
			TriplicateForm triplicateForm = shippingCustomService.getTriplicateForm(id);
			if (triplicateForm == null && shipping.getStatus() == 1) {// 已审核且不存在发货单PDF,重新生成一个pdf
				Setting setting = SettingUtils.get();
				triplicateForm = shippingCustomService.createTriplicateForm(setting,
						shipping);
			}
			model.addAttribute("triplicateForm", triplicateForm);
		}
		List<Map<String, Object>> list = shippingCustomService.findShippingItemListByShippingId(id.toString());
		String ids = "";
		for (int i = 0; i < list.size(); i++) {
			String bom_flag = list.get(i).get("bom_flag").toString();
			if (!bom_flag.equals("2")) {
				ids += list.get(i).get("order_item").toString() + ",";
			}
		}

		List<Map<String, Object>> orderlist = shippingCustomService.findOrderItemListByItemIds(ids.length() > 0 ? ids.substring(0,
				ids.length() - 1)
				: ids);

		for (int i = 0; i < list.size(); i++) {
			String bom_flag = list.get(i).get("bom_flag").toString();
			if (!bom_flag.equals("2")) {
				for (int j = 0; j < orderlist.size(); j++) {
					if (list.get(i)
							.get("order_item")
							.toString()
							.equals(orderlist.get(j).get("id").toString())) {
						orderlist.get(j)
								.put("line_no",
										list.get(i).get("line_no") == null ? ""
												: list.get(i)
														.get("line_no")
														.toString());
					}
				}
			}
			if (bom_flag.equals("1")) {
				list.remove(i);
			}
		}

		String jsonStr = JsonUtils.toJson(list);
		model.addAttribute("jsonStr", jsonStr);

		String orderjsonStr = JsonUtils.toJson(orderlist);
		model.addAttribute("orderjsonStr", orderjsonStr);

		/** 订单附件 */
		String shippingAttach_json = JsonUtils.toJson(shippingCustomService.findAttachsByShippingId(id));
		model.addAttribute("shippingAttach_json", shippingAttach_json);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(shipping.getSn(),
				4));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("flag", flag);
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(57L);//test/正式:57 nadev:59
		model.addAttribute("isCheckWf", isCheckWf);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		return "/b2b/shipping_custom/view";
	}

	/**
	 * 发货单列表
	 */
	@RequestMapping(value = "/shippinglist", method = RequestMethod.GET)
	public String shippinglist(Long[] ids, ModelMap model) {
		model.addAttribute("ids", ids);
		return "/b2b/shipping_custom/shippinglist";
	}

	/**
	 * 发货单列表数据
	 */
	@RequestMapping(value = "/shippinglist_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg shippinglist_data(Long[] ids, ModelMap model) {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return ResultMsg.error("15162", model);
		}
		String idss = "";
		for (int i = 0; i < ids.length; i++) {
			if (i == ids.length - 1) {
				idss += ids[i];
			}
			else {
				idss += ids[i] + ",";
			}
		}
		List<Map<String, Object>> shippings = shippingCustomService.findListById(idss);
		String jsonPage = JsonUtils.toJson(shippings);

		return success(jsonPage);
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/itemlist", method = RequestMethod.GET)
	public String itemlist(Long[] ids, Long[] itemIds, BigDecimal[] quantity,
			ModelMap model) {
		model.addAttribute("ids", ids);
		model.addAttribute("itemIds", itemIds);
		model.addAttribute("quantity", quantity);
		return "/b2b/shipping_custom/itemlist";
	}

	/**
	 * 发货明细列表数据
	 */
	@RequestMapping(value = "/itemlist_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg itemlistData(Long[] ids, Long[] itemIds, BigDecimal[] quantity,
			ModelMap model) {

		// if (ids == null || ids.length == 0) {
		// //请选择发货单
		// return ResultMsg.error("15162", model);
		// }
		// String idss = "";
		// for (int i = 0; i < ids.length; i++) {
		// if (i == ids.length - 1) {
		// idss += ids[i];
		// }
		// else {
		// idss += ids[i] + ",";
		// }
		// }
		// List<Map<String, Object>> shippings =
		// shippingCustomService.findListById(idss);
		// if (!shippings.isEmpty()) {
		// String itemids = "";
		// for (int i = 0; i < shippings.size(); i++) {
		// Map<String, Object> map = shippings.get(i);
		// if (i == shippings.size() - 1) {
		// itemids += map.get("id");
		// }
		// else {
		// itemids += map.get("id") + ",";
		// }
		// }
		// List<Map<String, Object>> shippingItems =
		// shippingCustomService.findShippingItemListByShippingId(itemids);
		// List<Map<String, Object>> items = null;
		// for (Map<String, Object> map : shippings) {
		// items = new ArrayList<Map<String, Object>>();
		// String shippingId = map.get("id").toString();
		// for (Map<String, Object> itemMap : shippingItems) {
		// String oid = itemMap.get("shipping").toString();
		// if (shippingId.equals(oid)) {
		// items.add(itemMap);
		// }
		// }
		// map.put("shipping_items", items);
		// }
		// }
		// String jsonPage = JsonUtils.toJson(shippings);

		List<ShippingItem> items = new ArrayList<ShippingItem>();
		for (int i = 0; i < itemIds.length; i++) {
			ShippingItem shippingItem = shippingItemService.find(itemIds[i]);
			shippingItem.setReturnQuantity(quantity[i]);
			shippingItem.setCompanyInfoId(shippingItem.getOrder() == null ? 0L
					: shippingItem.getOrder().getId());
			shippingItem.setOrderSn(shippingItem.getShipping().getSn());
			shippingItem.setOutTradeNo(shippingItem.getWarehouse().getName());
			items.add(shippingItem);
		}
		String jsonPage = JsonUtils.toJson(items);

		return success(jsonPage);
	}

	/**
	 * (定制)由正式订单生成发货单的保存方法
	 * @param shipping
	 * @param warehouseId
	 * @param deliveryId
	 * @param areaId
	 * @param saleOrgId
	 * @param driverInfoId
	 * @param storeId
	 * @param supplierId
	 * @param organizationId
	 * @param flag
	 * @return
	 */
	@RequestMapping(value = "/saveCustom", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg saveCustom(Shipping shipping, Long warehouseId, Long deliveryId,
			Long areaId, Long saleOrgId, Long driverInfoId, Long storeId,
			Long supplierId, Long organizationId, Integer flag,String cardId,
			String[] creditRechargeSn,
			BigDecimal[] creditRechargeDistributionAmount,
			String[] depositRechargeSn,
			BigDecimal[] depositRechargeDistributionAmount,
			String[] policyCountNo, BigDecimal[] policyCountDistributionAmount) {

		Warehouse warehouse = warehouseBaseService.find(warehouseId);

		if (storeId == null) {
			// 请选择客户
			return error("15135");
		}
		DeliveryCorp delivery = deliveryCorpBaseService.find(deliveryId);

		Area area = areaBaseService.find(areaId);
		if (area == null) {
			return error("请选择收货地区");
		}

		if (ConvertUtil.isEmpty(shipping.getAddress())) {
			// 请填写收货人地址
			return error("15168");
		}
		if (ConvertUtil.isEmpty(shipping.getConsignee())) {
			// 请填写收货人
			return error("15167");
		}
		if (ConvertUtil.isEmpty(shipping.getPhone())) {
			// 请填写收货人电话
			return error("15169");
		}

		List<Long> orderIds = new ArrayList<Long>();
		List<ShippingItem> shippingItems = shipping.getShippingItems();
		for (Iterator<ShippingItem> iterator = shippingItems.iterator(); iterator.hasNext();) {
			ShippingItem shippingItem = iterator.next();
			if (ConvertUtil.isEmpty(shippingItem.getLineNo())) {
				iterator.remove();
			}
		}
		if (shippingItems.isEmpty()) {
			// 请添加发货产品
			return error("15152");
		}
		for (ShippingItem shippingItem : shippingItems) {
			Long orderId = shippingItem.getOrder() == null ? null
					: shippingItem.getOrder().getId();
			if (orderId != null) {
				orderIds.add(orderId);
			}
		}
		
		
		shipping.setDriverInfo(driverInfoBaseService.find(driverInfoId));
		shipping.setStore(storeBaseService.find(storeId));
		shipping.setSaleOrg(saleOrgBaseService.find(saleOrgId));

		shipping.setOrganization(organizationService.find(organizationId));

		// shipping.setSupplier(supplier);
		shippingCustomService.save(shipping,
				warehouse,
				delivery,
				area,
				orderIds,
				flag);

		return success();
	}

	/**
	 * 定制订单的更新
	 * @param shipping
	 * @param warehouseId
	 * @param deliveryId
	 * @param areaId
	 * @param saleOrgId
	 * @param driverInfoId
	 * @param supplierId
	 * @param organizationId
	 * @return
	 */
	@RequestMapping(value = "/updateCustom", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg updateCustom(Shipping shipping, Long warehouseId, Long deliveryId,
			Long areaId, Long saleOrgId, Long driverInfoId, Long supplierId,
			Long organizationId, Integer flag, Long[] creditRechargeContractId,
			String[] creditRechargeSn,
			BigDecimal[] creditRechargeDistributionAmount,
			Long[] depositRechargeContractId, String[] depositRechargeSn,
			BigDecimal[] depositRechargeDistributionAmount,
			Long[] policyCountContractId, String[] policyCountNo,
			BigDecimal[] policyCountDistributionAmount) {

		Warehouse warehouse = warehouseBaseService.find(warehouseId);

		DeliveryCorp delivery = deliveryCorpBaseService.find(deliveryId);

		Area area = areaBaseService.find(areaId);
		if (area == null) {
			return error("请选择收货地区");
		}

		if (ConvertUtil.isEmpty(shipping.getAddress())) {
			// 请填写收货人地址
			return error("15168");
		}
		if (ConvertUtil.isEmpty(shipping.getConsignee())) {
			// 请填写收货人
			return error("15167");
		}
		if (ConvertUtil.isEmpty(shipping.getPhone())) {
			// 请填写收货人电话
			return error("15169");
		}

		List<ShippingItem> shippingItems = shipping.getShippingItems();
		if (shippingItems.isEmpty()) {
			// 请添加发货产品
			return error("15152");
		}

		shipping.setDriverInfo(driverInfoBaseService.find(driverInfoId));
		shippingCustomService.updateCustom(shipping,
				warehouse,
				delivery,
				area,
				flag,
				creditRechargeContractId,
				creditRechargeSn,
				creditRechargeDistributionAmount,
				depositRechargeContractId,
				depositRechargeSn,
				depositRechargeDistributionAmount,
				policyCountContractId,
				policyCountNo,
				policyCountDistributionAmount);

		return success();
	}

	/**
	 * 审核
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 * @throws Exception
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check(Long[] ids) throws Exception {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		shippingCustomService.check(ids);
		return success();
	}

	/**
	 * 流程审核
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 * @throws Exception
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checkWf(Long[] ids, Long objConfId) throws Exception {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		shippingCustomService.checkWf(ids, objConfId);
		return success();
	}

	@RequestMapping(value = "/checkPdf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg checkPdf(Long ids) throws Exception {
		Shipping shipping = shippingCustomService.find(ids);
		TriplicateForm triplicateForm = null;
		if (shipping == null) return error("发货单不存在！");
		if (shipping.getStatus() == 0 || shipping.getStatus() == 2) {
			return error("发货单未审核或已作废");
		}
		else {
			triplicateForm = shippingCustomService.getTriplicateForm(ids);
			if (triplicateForm == null) {
				Setting setting = SettingUtils.get();
				triplicateForm = shippingCustomService.createTriplicateForm(setting,
						shipping);
			}
		}

		return success(triplicateForm.getUrl());
	}

	@RequestMapping(value = "/create", method = RequestMethod.GET)
	public @ResponseBody
	ResultMsg create(Long id) throws Exception {
		String url = shippingCustomService.createPdfTest(id);
		return success(url);
	}

	/**
	 * 作废
	 * 
	 * @param id
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg cancel(Long[] ids) throws Exception {
		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		for (int i = 0; i < ids.length; i++) {
			Shipping shipping = shippingCustomService.find(ids[i]);
			if (shipping != null && 2 == shipping.getStatus()) {
				return error("操作失败!发货通知单已作废!");
			}
		}
		shippingCustomService.cancel(ids);

		return success();
	}

	/**
	 * 驳回
	 * 
	 * @param id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/reject", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg reject(Long[] ids) throws Exception {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		shippingCustomService.reject(ids);
		return success();
	}

	/**
	 * 出仓
	 * 
	 * @param shipping
	 * @return
	 */
	@RequestMapping(value = "/out", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg out(Long[] itemIds, Long[] orders, BigDecimal[] quantitys) {

		String orderIds = "";
		// List<ShippingItem> shippingItems = shipping.getShippingItems();
		// for (int i = 0; i < shippingItems.size(); i++) {
		// ShippingItem shippingItem = shippingItems.get(i);
		// if (i == shippingItems.size() - 1) {
		// orderIds += shippingItem.getOrder().getId();
		// }
		// else {
		// orderIds += shippingItem.getOrder().getId() + ",";
		// }
		// }

		for (int i = 0; i < orders.length; i++) {
			if (orders[i] != null) {
				orderIds += "," + orders[i];
			}
		}
		orderIds = orderIds.substring(1);
		shippingCustomService.out(orderIds, itemIds, quantitys);

		return success();
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/shipping_operate_list", method = RequestMethod.GET)
	public String shipping_operate_list(Long[] ids, BigDecimal[] quantity,
			BigDecimal[] boxQuantity, BigDecimal[] branchQuantity,
			Long warehouseId, String warehouseName, Long saleOrgId,
			Long areaId, Long[] supplierId, String[] supplierName,
			Long organizationId, String organizationName, String treePath,
			String address, String consignee, String phone, Long storeId,
			BigDecimal paymentType, String addressOutTradeNo,String cardId, ModelMap model) {
		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		model.addAttribute("ids", ids);
		model.addAttribute("quantity", quantity);
		model.addAttribute("boxQuantity", boxQuantity);
		model.addAttribute("branchQuantity", branchQuantity);
		model.addAttribute("warehouseId", warehouseId);
		model.addAttribute("warehouseName", warehouseName);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("organizationName", organizationName);
		model.addAttribute("areaId", areaId);
		model.addAttribute("treePath", treePath);
		model.addAttribute("area", areaBaseService.find(areaId));
		model.addAttribute("address", address);
		model.addAttribute("addressOutTradeNo", addressOutTradeNo);
		model.addAttribute("consignee", consignee);
		model.addAttribute("phone", phone);
		model.addAttribute("paymentType",
				new BigDecimal(100).subtract(paymentType));
		model.addAttribute("store", storeBaseService.find(storeId));
		model.addAttribute("saleOrg", saleOrgBaseService.find(saleOrgId));
		model.addAttribute("cardId", cardId);
		if (supplierId != null && supplierName != null) {
			model.addAttribute("supplierId",
					supplierId.length > 0 ? supplierId[0] : null);
			model.addAttribute("supplierName",
					supplierName.length > 0 ? supplierName[0] : null);
			Store supplier = storeBaseService.find(supplierId[0]);
			if (supplier != null) {
				List<Filter> fis = new ArrayList<Filter>();
				fis.add(Filter.eq("store", supplier));
				List<WarehouseStore> warehouseStores = warehouseStoreBaseService.findList(null,
						fis,
						null);
				if (warehouseStores != null && warehouseStores.size() > 0) {
					model.addAttribute("warehouseStore", warehouseStores.get(0));
				}
			}
		}

		int useLockStock = Integer.parseInt(SystemConfig.getConfig("useLockStock",
				WebUtils.getCurrentCompanyInfoId()));
		model.addAttribute("useLockStock", useLockStock);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);

		return "/b2b/shipping_custom/shipping_operate_list";
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/shipping_operate_list/{code}", method = RequestMethod.GET)
	public String shipping_operate_list(@PathVariable String code, Long[] ids,
			BigDecimal[] quantity, Long warehouseId, String warehouseName,
			Long saleOrgId, Long areaId, Long[] supplierId,
			String[] supplierName, Long organizationId,
			String organizationName, String treePath, String address,
			String consignee, String phone, Long storeId, ModelMap model) {
		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("shippingMethods",
				shippingMethodBaseService.findList(null, filters, null));
		model.addAttribute("ids", ids);
		model.addAttribute("quantity", quantity);
		model.addAttribute("warehouseId", warehouseId);
		model.addAttribute("warehouseName", warehouseName);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("organizationName", organizationName);
		model.addAttribute("areaId", areaId);
		model.addAttribute("treePath", treePath);
		model.addAttribute("address", address);
		model.addAttribute("consignee", consignee);
		model.addAttribute("phone", phone);
		model.addAttribute("store", storeBaseService.find(storeId));
		model.addAttribute("saleOrg", saleOrgBaseService.find(saleOrgId));
		if (supplierId != null && supplierName != null) {
			model.addAttribute("supplierId",
					supplierId.length > 0 ? supplierId[0] : null);
			model.addAttribute("supplierName",
					supplierName.length > 0 ? supplierName[0] : null);
			Store supplier = storeBaseService.find(supplierId[0]);
			if (supplier != null) {
				List<Filter> fis = new ArrayList<Filter>();
				fis.add(Filter.eq("store", supplier));
				List<WarehouseStore> warehouseStores = warehouseStoreBaseService.findList(null,
						fis,
						null);
				if (warehouseStores != null && warehouseStores.size() > 0) {
					model.addAttribute("warehouseStore", warehouseStores.get(0));
				}
			}
		}

		int useLockStock = Integer.parseInt(SystemConfig.getConfig("useLockStock",
				WebUtils.getCurrentCompanyInfoId()));
		model.addAttribute("useLockStock", useLockStock);

		// 组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);
		model.addAttribute("code", code);
		model.addAttribute("paymentMethods", paymentMethodBaseService.findAll());
		return "/" + code + "/b2b/shipping_custom/shipping_operate_list";
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/shipping_operate_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg shipping_operate_list_data(Long[] ids, BigDecimal[] quantity,
			BigDecimal[] boxQuantity, BigDecimal[] branchQuantity) {

		Map<String, Object> map = new HashMap<String, Object>();
		List<Map<String, Object>> orderItems = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> shippingItems = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < ids.length; i++) {
			Map<String, Object> item = shippingCustomService.findOperateItem(ids[i],
					quantity[i],
					boxQuantity == null ? null : boxQuantity[i],
					branchQuantity == null ? null : branchQuantity[i]);
			item.put("line_no", i + 1);
			Long productId = 0L;
			if (!ConvertUtil.isEmpty(item.get("product")))
				productId = Long.parseLong(item.get("product").toString());
			if (productSuiteService.existsProductSuite(productId)) {
				item.put("bom_flag", 1);

				List<Map<String, Object>> suiteItems = productSuiteService.findItemListByProductId(productId);
				for (Map<String, Object> suiteItemMap : suiteItems) {
					suiteItemMap.put("line_no", i + 1);
					suiteItemMap.put("bom_flag", 2);
					suiteItemMap.put("orders", item.get("orders"));
					suiteItemMap.put("order_sn", item.get("order_sn"));
					suiteItemMap.put("ship_quantity",
							new BigDecimal(suiteItemMap.get("quantity")
									.toString()).multiply(quantity[i]));
					suiteItemMap.put("suite_item_id", suiteItemMap.get("id"));
					suiteItemMap.put("suite_quantity", quantity[i]);
					shippingItems.add(suiteItemMap);
				}
			}
			else {
				item.put("bom_flag", 0);
			}
			orderItems.add(item);
			shippingItems.add(item);
		}
		map.put("orderItems", orderItems);
		map.put("shippingItems", shippingItems);
		String jsonPage = JsonUtils.toJson(map);

		return success(jsonPage);
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/order_item_list", method = RequestMethod.GET)
	public String order_item_list(ModelMap model) {

		boolean isAdmin = WebUtils.isAdmin();
		Store store = null;
		if (isAdmin) {
			List<Store> stores = storeBaseService.findList(1, null, null);
			if (!stores.isEmpty()) store = stores.get(0);
		}
		else {
			Member member = storeMemberBaseService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
			if (!storeMembers.isEmpty()) {
				store = storeMembers.get(0).getStore();
			}
		}
		if (store != null) {
			model.addAttribute("storeId", store.getId());
			model.addAttribute("storeName", store.getName());
		}
		return "/b2b/shipping_custom/order_item_list";
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/order_item_list/{code}", method = RequestMethod.GET)
	public String order_item_list(@PathVariable String code, ModelMap model) {

		boolean isAdmin = WebUtils.isAdmin();
		Store store = null;
		if (isAdmin) {
			List<Store> stores = storeBaseService.findList(1, null, null);
			if (!stores.isEmpty()) store = stores.get(0);
		}
		else {
			Member member = storeMemberBaseService.getCurrent().getMember();
			List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
			if (!storeMembers.isEmpty()) {
				store = storeMembers.get(0).getStore();
			}
		}
		if (store != null) {
			model.addAttribute("storeId", store.getId());
			model.addAttribute("storeName", store.getName());
		}
		model.addAttribute("code", code);
		return "/" + code + "/b2b/shipping_custom/order_item_list";
	}

	/**
	 * 发货明细列表数据
	 */
	@RequestMapping(value = "/order_item_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg order_item_list_data(String orderSn, String outTradeNo,
			String consignee, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, Long supplierId, String model,
			Pageable pageable) {

		// if (storeId == null) {
		// //请选择一个客户进行搜索
		// return error("15170");
		// }

		Page<Map<String, Object>> page = orderCustomService.findItemPage(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				consignee,
				vonderCode,
				model,
				true,
				new Integer[] { 6 },
				false,
				supplierId,
				false,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 发货明细列表数据
	 */
	@RequestMapping(value = "/order_item_list_dataCustom", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg order_item_list_dataCustom(String orderSn, String outTradeNo,
			Long storeId, Long warehouseId, String sn, String name,
			String vonderCode, Long supplierId, String model, Pageable pageable) {

		// if (storeId == null) {
		// //请选择一个客户进行搜索
		// return error("15170");
		// }

		Page<Map<String, Object>> page = orderCustomService.findItemPageCustom(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				vonderCode,
				model,
				true,
				new Integer[] { 6 },
				false,
				supplierId,
				false,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 发货明细列表
	 */
	@RequestMapping(value = "/check_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check_data(Long[] ids, BigDecimal[] quantity) {

		// int useLockStock =
		// Integer.parseInt(SystemConfig.getConfig("useLockStock",
		// WebUtils.getCurrentCompanyInfoId()));

		Long warehouseId = 0L;
		Long storeId = 0L;
		Long saleOrgId = 0L;
		Long organizationId = 0L;
		String address = "";
		String pConsignee = "";
		String pPhone = "";
		String addressOutTradeNo = "";
		String cardId = "";
		BigDecimal paymentType = BigDecimal.ZERO;
		HashMap<String, Object> map = new HashMap<String, Object>();
		for (int i = 0; i < ids.length; i++) {
			OrderItem orderItem = orderItemService.find(ids[i]);

			Order order = orderItem.getOrder();

			Warehouse warehouse = order.getWarehouse();
			if (warehouseId.intValue() == 0 && warehouse != null) {
				warehouseId = warehouse.getId();
				map.put("warehouseId", warehouseId);
				map.put("warehouseName", warehouse.getName());
			}

			if (warehouse != null
					&& warehouseId.intValue() != warehouse.getId().intValue()) {
				// 所选订单明细仓库不一致
				return error("15171");
			}

			Organization organization = order.getOrganization();
			if (organizationId.intValue() == 0 && organization != null) {
				organizationId = organization.getId();
				map.put("organizationId", organizationId);
				map.put("organizationName", organization.getName());
			}

			if (organization != null
					&& organizationId.intValue() != organization.getId()
							.intValue()) {
				// 所选订单明细仓库不一致
				return error("所选订单明细的经营组织不一致");
			}
			
			if (storeId.intValue() == 0) {
				storeId = order.getStore().getId();
			}
			if (storeId.intValue() != order.getStore().getId().intValue()) {
				// 所选订单客户不一致
				return error("15174");
			}
			if (saleOrgId.intValue() == 0) {
				saleOrgId = order.getSaleOrg().getId();
			}
			if (saleOrgId.intValue() != order.getSaleOrg().getId().intValue()) {
				return error("所选订单机构不一致");
			}
			Area area = order.getArea();
//			String areaName = area == null ? "" : area.getFullName();
			String addr = order.getAddress() == null ? "" : order.getAddress();
//			addr = areaName + addr;
			String consignee = order.getConsignee() == null ? ""
					: order.getConsignee();
			String phone = order.getPhone() == null ? "" : order.getPhone();
			if (ConvertUtil.isEmpty(address)) {
				address = addr;
			}
			
			if(ConvertUtil.isEmpty(cardId)){
				cardId = order.getCardId();
			}
			if (!address.equals(addr)) {
				// 所选订单明细收货地址不一致
				return error("15172");
			}
			if (ConvertUtil.isEmpty(pConsignee)) {
				pConsignee = consignee;
			}
			if (!pConsignee.equals(consignee)) {
				// 所选订单明细收货人不一致
				return error("15176");
			}
			if (ConvertUtil.isEmpty(pPhone)) {
				pPhone = phone;
			}
			if (!pPhone.equals(phone)) {
				// 所选订单明细收货人电话不一致
				return error("15177");
			}
			if (area != null) {
				if (!map.containsKey("areaId")) {
					map.put("areaId", area.getId());
				}
				if (!map.containsKey("treePath")) {
					map.put("treePath", area.getTreePath());
				}
			}

			if (ConvertUtil.isEmpty(addressOutTradeNo)) {
				addressOutTradeNo = order.getAddressOutTradeNo();
			}
			paymentType = order.getPaymentType();

		}
		map.put("address", address);
		map.put("consignee", pConsignee);
		map.put("phone", pPhone);
		map.put("storeId", storeId);
		map.put("saleOrgId", saleOrgId);
		map.put("paymentType", paymentType);
		map.put("addressOutTradeNo", addressOutTradeNo);
		map.put("cardId",cardId);
		return success(JsonUtils.toJson(map));
	}

	/**
	 * 替代件列表
	 */
	@RequestMapping(value = "/replace_product_list", method = RequestMethod.GET)
	public String replace_product_list(Long suiteItemId, Integer multi,
			ModelMap model) {

		model.addAttribute("suiteItemId", suiteItemId);
		model.addAttribute("multi", multi);
		return "/b2b/shipping_custom/replace_product_list";
	}

	/**
	 * 发货单生成明细
	 */
	@RequestMapping(value = "/to_condition_export_create", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExportCreate(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, Long supplierId, String model,
			Pageable pageable) {

		Integer size = orderCustomService.countCreate(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				vonderCode,
				model,
				true,
				new Integer[] { 6 },
				false,
				supplierId,
				pageable);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/condition_export_create", method = RequestMethod.GET)
	public ModelAndView conditionExportCreate(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, Long supplierId, String mod,
			Pageable pageable, Integer page, ModelMap model) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");

		List<Map<String, Object>> data = orderCustomService.findItemListCreate(orderSn,
				outTradeNo,
				storeId,
				warehouseId,
				sn,
				name,
				vonderCode,
				mod,
				true,
				new Integer[] { 6 },
				false,
				supplierId,
				null,
				page,
				size);

		return getModelAndViewForCreate(data, model);

	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export_create", method = RequestMethod.GET)
	public ModelAndView selectedExportCreate(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = orderCustomService.findItemListCreate(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null);
		return getModelAndViewForCreate(data, model);

	}

	/** 发货通知单生成页面的导出 */
	public ModelAndView getModelAndViewForCreate(
			List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("order_date") != null) {
				String orderDate = DateUtil.convert((Date) str.get("order_date"));
				if (orderDate.length() > 10) {
					orderDate = orderDate.substring(0, 10);
				}
				str.put("orderDate", orderDate);
			}
			if (str.get("check_date") != null) {
				String checkDate = DateUtil.convert((Date) str.get("check_date"));
				if (checkDate.length() > 19) {
					checkDate = checkDate.substring(0, 19);
				}
				str.put("checkDate", checkDate);
			}
			if (str.get("quantity") != null) {
				BigDecimal quantity_int = new BigDecimal(str.get("quantity")
						.toString());
				str.put("quantity_int",
						NumberFormat.getInstance().format(quantity_int));
			}
			if (str.get("volume") != null) {
				BigDecimal volume = new BigDecimal(str.get("volume").toString());
				str.put("volume", NumberFormat.getInstance().format(volume));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "订单编号",
				"下单日期",
				"供应商",
				"产品名称",
				"产品型号",
				"产品编码",
				"数量",
				"体积",
				"客户名称",
				"收货人",
				"收货人电话",
				"收货地址",
				"配送方式",
				"备注",
				"审核人",
				"订单审核日期",
				"机构" };
		// 设置单元格取值
		String[] properties = { "order_sn",
				"orderDate",
				"supplier_name",
				"name",
				"model",
				"vonder_code",
				"quantity_int",
				"volume",
				"store_name",
				"consignee",
				"phone",
				"address",
				"shipping_method_name",
				"memo",
				"check_store_member_name",
				"checkDate",
				"sale_org_name" };
		return new ModelAndView(new ExcelView(filename, null, properties,
				header, null, null, data, null), model);
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String sn, String orderSn,
			String outTradeNo, String trackingNo, Integer[] status,
			Long[] warehouseId, String warehouseName, Long deliveryCorpId,
			Integer warehouseType, Long[] storeId, String firstTime,
			String lastTime, Pageable pageable, ModelMap model) {

		Integer size = shippingCustomService.count(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseId,
				deliveryCorpId,
				warehouseType,
				storeId,
				firstTime,
				lastTime,
				null,
				null,
				null);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @param productCategoryId
	 * @param sn
	 * @param vonderCode
	 * @param mod
	 * @param name
	 * @param startTime
	 * @param endTime
	 * @param isMarketable
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, String orderSn,
			String outTradeNo, String trackingNo, Integer[] status,
			Long[] warehouseId, String warehouseName, Long deliveryCorpId,
			Integer warehouseType, Long[] storeId, String firstTime,
			String lastTime, Pageable pageable, ModelMap model, Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = shippingCustomService.findItemList(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				warehouseId,
				deliveryCorpId,
				warehouseType,
				storeId,
				firstTime,
				lastTime,
				null,
				page,
				size);
		return getModelAndViewForList(data, model);
	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {

		List<Map<String, Object>> data = shippingCustomService.findItemList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null);
		return getModelAndViewForList(data, model);
	}

	/** 发货单审核页面的导出 */
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("status") != null) {
				Integer statuss = (Integer) str.get("status");
				if (statuss == 0) {
					str.put("status_name", "未审核");
				}
				else if (statuss == 1) {
					str.put("status_name", "已审核");
				}
				else if (statuss == 2) {
					str.put("status_name", "作废");
				}
				else if (statuss == 3) {
					str.put("status_name", "部分发货");
				}
				else if (statuss == 4) {
					str.put("status_name", "完全发货");
				}
			}
			BigDecimal quantity = new BigDecimal(str.get("quantity").toString());
			str.put("quantity", NumberFormat.getInstance().format(quantity));

			if (str.get("volume") != null) {
				BigDecimal volume = new BigDecimal(str.get("volume").toString());
				str.put("volume", NumberFormat.getInstance().format(volume));
			}

		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "发货单编号",
				"发货单生成日期",
				"发货单状态",
				"仓库",
				"客户",
				"产品名称",
				"产品型号",
				"产品编码",
				"实际发货数",
				"体积",
				"收货人",
				"收货人电话",
				"收货地址",
				"配送方式",
				"运费承担",
				"备注",
				"物流快递",
				"运单号" };

		// 设置单元格取值
		String[] properties = { "sn",
				"create_date",
				"status_name",
				"warehouse_name",
				"store_name",
				"name",
				"model",
				"vonder_code",
				"quantity",
				"volume",
				"consignee",
				"phone",
				"address",
				"shipping_method",
				"freight_charge_name",
				"memo",
				"delivery_corp_name",
				"tracking_no" };
		// 设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	/**
	 * 查看物流信息
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/queryDelivery", method = RequestMethod.GET)
	public String queryDelivery(Long id, ModelMap model) throws Exception {
		Shipping shipping = shippingCustomService.find(id);
		String trackingNo = shipping.getTrackingNo();

		HashMap<String, Object> bill = new HashMap<String, Object>();
		DeliveryCorp delivery = shipping.getDelivery();
		String waybillSn = (delivery != null) ? delivery.getWaybillSn() : null;
		if (delivery == null) {
			bill.put("msg", "未维护物流快递");
		}
		else if (waybillSn == null || "".equals(waybillSn)) {
			bill.put("msg", "未维护物流查询编码");
		}
		else {
			DeliveryUtil deliveryUtil = new DeliveryUtil();
			bill = deliveryUtil.getDeliveryInfo(waybillSn, trackingNo);
		}
		model.addAttribute("bill", bill);
		model.addAttribute("shipping", shipping);
		return "/b2b/shipping_custom/queryDelivery";
	}

	@RequestMapping(value = "/getStock", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getStock(Long[] productIds, Long warehouseId, ModelMap model) {
		List<Map<String, Object>> items = stockService.findStockList(warehouseId,
				productIds);
		String jsonPage = JsonUtils.toJson(items);

		return success(jsonPage);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_change_tb", method = RequestMethod.GET)
	public String list_change_tb(Pageable pageable, ModelMap model) {

		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		return "/b2b/shipping_custom/list_change_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_change", method = RequestMethod.GET)
	public String list_change(Pageable pageable, ModelMap model) {

		List<DeliveryCorp> deliveryCorps = deliveryCorpBaseService.findAll();
		model.addAttribute("deliveryCorps", deliveryCorps);
		return "/b2b/shipping_custom/list_change";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_change_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_change_data(String sn, String orderSn, String outTradeNo,
			String trackingNo, Integer[] status, Long[] warehouseId,
			String productModel, String warehouseName, Long deliveryCorpId,
			Integer warehouseType, Long[] storeId, Long supplierId,
			String firstTime, String lastTime, Pageable pageable, ModelMap model) {

		int orderType = 2;
		if (status == null || status.length == 0)
			status = new Integer[] { 1, 2, 3, 4 };
		Page<Map<String, Object>> page = purchaseShippingService.findPage(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				new Integer[] { 1 },
				warehouseId,
				deliveryCorpId,
				warehouseType,
				storeId,
				supplierId,
				firstTime,
				lastTime,
				null,
				orderType,
				null,
				pageable);

		List<Map<String, Object>> shippings = page.getContent();

		if (!shippings.isEmpty()) {
			String ids = "";
			for (int i = 0; i < shippings.size(); i++) {
				Map<String, Object> map = shippings.get(i);
				if (i == shippings.size() - 1) {
					ids += map.get("id");
				}
				else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> shippingItems = purchaseShippingService.findShippingItemListByShippingId(ids);
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : shippings) {
				items = new ArrayList<Map<String, Object>>();
				String shippingId = map.get("id").toString();
				for (Map<String, Object> itemMap : shippingItems) {
					String oid = itemMap.get("shipping").toString();
					if (shippingId.equals(oid)) {
						items.add(itemMap);
					}
				}
				map.put("shipping_items", items);
			}
		}
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 运单补录
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/change", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg change(Long[] ids, Date[] shippingTimes, String[] trackingNos,
			Long[] deliveryIds, BigDecimal[] freights) {

		if (ids == null || ids.length == 0) {
			// 请选择发货单
			return error("15162");
		}
		if ((shippingTimes == null || shippingTimes.length == 0)
				&& (trackingNos == null || trackingNos.length == 0)
				&& (deliveryIds == null || deliveryIds.length == 0)
				&& (freights == null || freights.length == 0)) {
			return error("请填写信息");
		}
		purchaseShippingService.change(ids,
				shippingTimes,
				trackingNos,
				deliveryIds,
				freights);
		return success();
	}

	/**
	 * 运单补录 导出
	 */
	@RequestMapping(value = "/to_condition_export_change", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExportChange(String sn,
			String orderSn, String outTradeNo, String trackingNo,
			Integer[] status, Long[] warehouseId, String warehouseName,
			Long deliveryCorpId, Integer warehouseType, Long[] storeId,
			Long supplierId, String firstTime, String lastTime,
			String productModel, Pageable pageable, ModelMap model) {

		int orderType = 2;
		if (status == null || status.length == 0)
			status = new Integer[] { 1, 2, 3, 4 };
		Integer size = purchaseShippingService.change_count(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				new Integer[] { 1 },
				warehouseId,
				deliveryCorpId,
				warehouseType,
				storeId,
				supplierId,
				firstTime,
				lastTime,
				null,
				pageable,
				null,
				null,
				orderType,
				productModel);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/condition_export_change", method = RequestMethod.GET)
	public ModelAndView conditionExportChange(String sn, String orderSn,
			String outTradeNo, String trackingNo, Integer[] status,
			Long[] warehouseId, String warehouseName, Long deliveryCorpId,
			Integer warehouseType, Long[] storeId, Long supplierId,
			String firstTime, String lastTime, String productModel,
			Pageable pageable, ModelMap model, Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		int orderType = 2;
		if (status == null || status.length == 0)
			status = new Integer[] { 1, 2, 3, 4 };
		List<Map<String, Object>> data = purchaseShippingService.findChangeList(sn,
				orderSn,
				outTradeNo,
				trackingNo,
				status,
				new Integer[] { 1 },
				warehouseId,
				deliveryCorpId,
				warehouseType,
				storeId,
				supplierId,
				firstTime,
				lastTime,
				null,
				null,
				page,
				size,
				orderType,
				productModel);

		return getModelAndViewForListChange(data, model);
	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export_change", method = RequestMethod.GET)
	public ModelAndView selectedExportChange(Long[] ids, ModelMap model) {

		int orderType = 2;
		List<Map<String, Object>> data = purchaseShippingService.findChangeList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				orderType,
				null);
		return getModelAndViewForListChange(data, model);
	}

	/**
	 * 运单补录 数据
	 */
	public ModelAndView getModelAndViewForListChange(
			List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("status") != null) {
				Integer statuss = (Integer) str.get("status");
				if (statuss == 0) {
					str.put("status_name", "未审核");
				}
				else if (statuss == 1) {
					str.put("status_name", "已审核");
				}
				else if (statuss == 2) {
					str.put("status_name", "作废");
				}
				else if (statuss == 3) {
					str.put("status_name", "部分发货");
				}
				else if (statuss == 4) {
					str.put("status_name", "完全发货");
				}
			}
			BigDecimal quantity = new BigDecimal(str.get("quantity").toString());
			str.put("quantity", NumberFormat.getInstance().format(quantity));

			if (str.get("volume") != null) {
				BigDecimal volume = new BigDecimal(str.get("volume").toString());
				str.put("volume", NumberFormat.getInstance().format(volume));
			}
			if (str.get("shipping_time") != null) {
				String shipping_time = DateUtil.convert((Date) str.get("shipping_time"));
				if (shipping_time.length() > 19) {
					shipping_time = shipping_time.substring(0, 19);
				}
				str.put("shipping_time", shipping_time);
			}

		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "发货单编号",
				"运单号",
				"物流快递",
				"发货单状态",
				"仓库",
				"客户",
				"产品名称",
				"产品型号",
				"产品编码",
				"销售订单编号",
				"体积",
				"实际发货数",
				"收货人",
				"收货人电话",
				"收货地址",
				"配送方式",
				"运费承担",
				"备注",
				"预计到货时间" };

		// 设置单元格取值
		String[] properties = { "shipping_sn",
				"tracking_no",
				"delivery_corp_name",
				"status_name",
				"warehouse_name",
				"store_name",
				"name",
				"model",
				"vonder_code",
				"order_sn",
				"volume",
				"quantity",
				"consignee",
				"phone",
				"address",
				"shipping_method",
				"freight_charge_name",
				"memo",
				"shipping_time" };
		// 设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}

	@RequestMapping(value = "/saveAttach", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg saveAttach(Shipping shipping) {
		Shipping pShipping = shippingCustomService.find(shipping.getId());
		if (pShipping.getStatus() == 0) {
			return this.error("发货单未审核，不允许上传附件");
		}
		// 附件
		List<ShippingAttach> shippingAttachs = shipping.getShippingAttachs();
		for (Iterator<ShippingAttach> iterator = shippingAttachs.iterator(); iterator.hasNext();) {
			ShippingAttach shippingAttach = iterator.next();
			if (shippingAttach == null || shippingAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (shippingAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			shippingAttach.setFileName(shippingAttach.getName()
					+ "."
					+ shippingAttach.getSuffix());
			shippingAttach.setShipping(pShipping);
			shippingAttach.setStoreMember(storeMemberBaseService.getCurrent());
		}
		pShipping.getShippingAttachs().clear();
		pShipping.getShippingAttachs().addAll(shippingAttachs);
		shippingCustomService.update(pShipping);
		return success();
	}

	//获取客户可发货余额
	@RequestMapping(value = "/getBalance", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getBalance(Long storeId, Integer[] type, Pageable pageable,
			ModelMap model) {

		Object[] args = new Object[] { storeId, type };
		Page<Map<String, Object>> page = storeBalanceService.findPage(pageable,
				args);
		Map<String, Object> map = new HashMap<String, Object>();
		map = page.getContent().get(0);
		return ResultMsg.success().addObjX(map);
	}

}