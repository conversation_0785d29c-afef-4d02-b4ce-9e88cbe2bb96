package net.shopxx.order.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.entity.OrderItem;
import net.shopxx.util.RoleJurisdictionUtil;

@Repository("orderDao")
public class OrderDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	

	/**
	 * 查找订单分页数据
	 * 
	 * @param orderSn
	 * @param outTradeNo
	 * @param orderStatus
	 * @param shippingStatus
	 * @param warehouseId
	 * @param storeIds
	 * @param deliveryCorpId
	 * @param firstTime
	 * @param lastTime
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findPage(String orderSn,
			String outTradeNo, Integer[] orderStatus, Long sbuId,
			Integer[] shippingStatus, Long warehouseId, Long[] storeIds,
			String consignee, String phone, String address,
			Long deliveryCorpId, Long[] productIds, Integer[] paymentStatus,
			Integer[] flag, Integer orderType, String firstTime,
			String lastTime, Integer[] confirmStatus, Integer isReturn,
			String store_member_name, Long saleOrgId, Long organizationId,
			Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder pSql = new StringBuilder();
		pSql.append("select oo.*,oc.amount_closed,dc.name delivery_corp_name,a.full_name area_name, (sum(ROUND(i.price * i.quantity,2)) + oo.offset_amount + oo.freight - oo.coupon_discount - oo.promotion_discount) amount from ");

		StringBuilder sql = new StringBuilder();
		sql.append(" (select o.id,o.create_date,o.order_date,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.payment_status,o.ex_status,o.ticket_holder,"
				+ "o.order_type,o.change_memo,o.buyer_remark,o.seller_remark,o.delivery_memo,o.memo,o.consignee,o.phone,o.address,o.delivery,o.area,"
				+ "o.offset_amount,o.freight,o.coupon_discount,o.promotion_discount,o.flag,o.wf_id,o.wf_state,o.confirm_status,w.name warehouse_name,"
				+ "s.name store_name,s.out_trade_no store_code,s.balance store_balance,s.budget store_budget,so.name sale_org_name,so.id sale_org_id, csm.name check_store_member_name,o.check_date,"
				+ "so.budget sale_org_budget,sd.value system_value,sm.name store_member_name,o.stores,rsm.name regional_manager_name,xs.name distributor_name,o.zip_code,ii.shipped_quantity"
				+ "  from xx_order o"
				+ " left join xx_store s on s.id = o.stores"
				+ " left join xx_sale_org so on so.id = o.sale_org"
				+ " left join xx_warehouse w on w.id = o.warehouse"
				+ " left join xx_order_item ii on o.id=ii.orders"
				+ " left join xx_product p on ii.product=p.id"
				+ " left join xx_product_category pc on p.product_category=pc.id"
				+ " left join xx_system_dict sd on o.freight_charge_type=sd.id"
				+ " left join xx_store_member sm on o.store_member = sm.id"
				+ " left join xx_store_member rsm on o.regional_manager = rsm.id"
				+ " left join xx_store_member csm on csm.id = o.check_store_member"
				+ " left join xx_store xs on xs.id = o.distributor"
				+ " left join xx_sbu sb on sb.id = o.sbu"
				+ " left join xx_organization po on ii.product_organization = po.id"
				+ " where 1=1");

		/**2018年9月23日 02:18:10 lj add  类型3为项目订单 查询客户不应该查到项目订单**/
		sql.append(" and (o.order_category<>3 or o.order_category is null)");

		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn = ?");
			list.add(orderSn.trim());
		}
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(consignee)) {
			sql.append(" and o.consignee like ?");
			list.add("%" + consignee + "%");
		}
		if (!ConvertUtil.isEmpty(phone)) {
			sql.append(" and o.phone like ?");
			list.add("%" + phone + "%");
		}
		if (!ConvertUtil.isEmpty(store_member_name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + store_member_name.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(address)) {
			sql.append(" and o.address like ?");
			list.add("%" + address + "%");
		}
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and o.sale_org = ?");
			list.add(saleOrgId);
		}
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" and o.organization = ?");
			list.add(organizationId);
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		if (shippingStatus != null && shippingStatus.length > 0) {
			String ss = "";
			for (int i = 0; i < shippingStatus.length; i++) {
				if (i == shippingStatus.length - 1)
					ss += shippingStatus[i];
				else
					ss += shippingStatus[i] + ",";
			}
			sql.append(" and o.shipping_status in (" + ss + ")");
		}
		if (paymentStatus != null && paymentStatus.length > 0) {
			String ps = "";
			for (int i = 0; i < paymentStatus.length; i++) {
				if (i == paymentStatus.length - 1)
					ps += paymentStatus[i];
				else
					ps += paymentStatus[i] + ",";
			}
			sql.append(" and o.payment_status in (" + ps + ")");
		}
		if (flag != null && flag.length > 0) {
			String fs = "";
			for (int i = 0; i < flag.length; i++) {
				if (i == flag.length - 1)
					fs += flag[i];
				else
					fs += flag[i] + ",";
			}
			sql.append(" and o.flag in (" + fs + ")");
		}
		if (storeIds != null && storeIds.length > 0) {
			String s = "";
			for (int i = 0; i < storeIds.length; i++) {
				if (i == storeIds.length - 1)
					s += storeIds[i];
				else
					s += storeIds[i] + ",";
			}
			sql.append("  and o.stores in (" + s + ")");
		}
		if (deliveryCorpId != null) {
			sql.append(" and o.delivery=?");
			list.add(deliveryCorpId);
		}
		if (orderType != null) {
			sql.append(" and o.order_type=?");
			list.add(orderType);
		}
		if (warehouseId != null) {
			sql.append(" and o.id in (select orders from xx_order_item where warehouse = ?)");
			list.add(warehouseId);
		}
		if (productIds != null && productIds.length > 0) {
			String fs = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					fs += productIds[i];
				else
					fs += productIds[i] + ",";
			}
			sql.append(" and ii.product in (" + fs + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and o.order_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and o.order_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		if (confirmStatus != null && confirmStatus.length > 0) {
			String os = "";
			for (int i = 0; i < confirmStatus.length; i++) {
				if (i == confirmStatus.length - 1)
					os += confirmStatus[i];
				else
					os += confirmStatus[i] + ",";
			}
			sql.append(" and o.confirm_status in (" + os + ")");
		}

		if (isReturn != null && isReturn == 1) {
			sql.append(" and exit(SELECT 1,oi.shipped_quantity - sum(IFNULL(ri.quantity, 0)) r_quantity"
					+ " from xx_order_item oi left join xx_b2b_returns_item ri on ri.order_item = oi.id"
					+ " where oi.orders = o.id group by oi.id having r_quantity > 0)");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		int pageNumber = pageable.getPageNumber();
		int pageSize = pageable.getPageSize();
		int num = (pageNumber - 1) * pageSize;
		sql.append(" group by o.id order by o.create_date desc limit "
				+ num
				+ ","
				+ (pageSize + 1)
				+ ") oo");

		sql.append(" left join");
		sql.append("  (select b.orders, sum(round(b.price * ci.quantity,2)) amount_closed");
		sql.append("  from  xx_order a,xx_order_item b, xx_order_close_item ci,xx_order_close c");
		sql.append("  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		sql.append("  and a.company_info_id = ?");
		list.add(companyInfoId);
		sql.append(" group by  b.orders ) oc on oc.orders = oo.id");

		pSql.append(sql);
		pSql.append(" left join xx_delivery_corp dc on dc.id = oo.delivery"
				+ " left join xx_area a on a.id = oo.area"
				+ " left join xx_order_item i on i.orders = oo.id"
				+ " group by oo.id"
				+ " order by oo.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> result = getNativeDao().findListMap(pSql.toString(),
				objs,
				0);
		long total = 0;
		if (result.size() == pageSize + 1) {
			total = (pageNumber + 1) * pageSize;
			result.remove(result.size() - 1);
		}
		else
			total = ((pageNumber - 1) * pageSize) + result.size();

		return new Page<Map<String, Object>>(result, total, pageable);
	}

	//定制  只查正式订单
	public Page<Map<String, Object>> findPageCustom(String orderSn,
			String outTradeNo, Integer[] orderStatus, Integer[] shippingStatus,
			Long warehouseId, Long[] storeIds, String consignee, String phone,
			String address, Long deliveryCorpId, Long[] productIds,
			Integer[] paymentStatus, Integer[] flag, Integer orderType,
			String firstTime, String lastTime, Integer[] confirmStatus,
			Integer isReturn, String storeMemberName, Integer saleOrgId,
			Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder pSql = new StringBuilder();
		pSql.append("select oo.*,dc.name delivery_corp_name,a.full_name area_name, (sum(i.price * i.quantity) + oo.offset_amount + oo.freight - oo.coupon_discount - oo.promotion_discount) amount from ");

		StringBuilder sql = new StringBuilder();
		sql.append(" (select o.id,o.create_date,o.order_date,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.payment_status,o.ex_status,"
				+ "o.order_type,o.change_memo,o.payment_type,o.expire,o.buyer_remark,o.seller_remark,o.delivery_memo,o.memo,o.consignee,o.phone,o.address,o.delivery,o.area,"
				+ "o.offset_amount,o.freight,o.coupon_discount,o.promotion_discount,o.flag,o.wf_id,o.wf_state,o.confirm_status,w.name warehouse_name,"
				+ "s.name store_name,s.out_trade_no store_code,s.balance store_balance,s.budget store_budget,so.name sale_org_name,so.id sale_org_id, csm.name check_store_member_name,o.check_date,"
				+ "so.budget sale_org_budget,sd.value system_value,sm.name store_member_name,o.stores "
				+ "  from xx_order o"
				+ " left join xx_store s on s.id = o.stores"
				+ " left join xx_sale_org so on so.id = o.sale_org"
				+ " left join xx_warehouse w on w.id = o.warehouse"
				+ " left join xx_order_item ii on o.id=ii.orders"
				+ " left join xx_product p on ii.product=p.id"
				+ " left join xx_product_category pc on p.product_category=pc.id"
				+ " left join xx_system_dict sd on o.freight_charge_type=sd.id"
				+ " left join xx_store_member sm on o.store_member = sm.id"
				+ " left join xx_store_member csm on csm.id = o.check_store_member"
				+ " where 1=1 and o.order_category=4 ");

		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(consignee)) {
			sql.append(" and o.consignee like ?");
			list.add("%" + consignee + "%");
		}
		if (!ConvertUtil.isEmpty(phone)) {
			sql.append(" and o.phone like ?");
			list.add("%" + phone + "%");
		}
		if (!ConvertUtil.isEmpty(address)) {
			sql.append(" and o.address like ?");
			list.add("%" + address + "%");
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		if (shippingStatus != null && shippingStatus.length > 0) {
			String ss = "";
			for (int i = 0; i < shippingStatus.length; i++) {
				if (i == shippingStatus.length - 1)
					ss += shippingStatus[i];
				else
					ss += shippingStatus[i] + ",";
			}
			sql.append(" and o.shipping_status in (" + ss + ")");
		}
		if (paymentStatus != null && paymentStatus.length > 0) {
			String ps = "";
			for (int i = 0; i < paymentStatus.length; i++) {
				if (i == paymentStatus.length - 1)
					ps += paymentStatus[i];
				else
					ps += paymentStatus[i] + ",";
			}
			sql.append(" and o.payment_status in (" + ps + ")");
		}
		if (flag != null && flag.length > 0) {
			String fs = "";
			for (int i = 0; i < flag.length; i++) {
				if (i == flag.length - 1)
					fs += flag[i];
				else
					fs += flag[i] + ",";
			}
			sql.append(" and o.flag in (" + fs + ")");
		}
		/*
		 * if (storeId != null) { sql.append(" and o.stores=?");
		 * list.add(storeId); }
		 */
		if (storeIds != null && storeIds.length > 0) {
			String s = "";
			for (int i = 0; i < storeIds.length; i++) {
				if (i == storeIds.length - 1)
					s += storeIds[i];
				else
					s += storeIds[i] + ",";
			}
			sql.append("  and o.stores in (" + s + ")");
		}
		if (deliveryCorpId != null) {
			sql.append(" and o.delivery=?");
			list.add(deliveryCorpId);
		}
		if (orderType != null) {
			sql.append(" and o.order_type=?");
			list.add(orderType);
		}
		if (warehouseId != null) {
			sql.append(" and o.id in (select orders from xx_order_item where warehouse = ?)");
			list.add(warehouseId);
		}
		if (productIds != null && productIds.length > 0) {
			String fs = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					fs += productIds[i];
				else
					fs += productIds[i] + ",";
			}
			sql.append(" and ii.product in (" + fs + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and o.order_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and o.order_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		else {

			sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());

//			Long saleOrgId = null;
//			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
//			if (storeMemberSaleOrg != null) {
//				saleOrgId = storeMemberSaleOrg.getSaleOrg().getId();
//			}
//			sql.append(" and (so.id="
//					+ saleOrgId
//					+ " or so.tree_path like '%,"
//					+ saleOrgId
//					+ ",%')");
		}

		if (confirmStatus != null && confirmStatus.length > 0) {
			String os = "";
			for (int i = 0; i < confirmStatus.length; i++) {
				if (i == confirmStatus.length - 1)
					os += confirmStatus[i];
				else
					os += confirmStatus[i] + ",";
			}
			sql.append(" and o.confirm_status in (" + os + ")");
		}

		if (isReturn != null && isReturn == 1) {
			sql.append(" and exit(SELECT 1,oi.shipped_quantity - sum(IFNULL(ri.quantity, 0)) r_quantity"
					+ " from xx_order_item oi left join xx_b2b_returns_item ri on ri.order_item = oi.id"
					+ " where oi.orders = o.id group by oi.id having r_quantity > 0)");
		}

//		if (orderType != null && orderType == 2) {
//			if (this.existsStoreMemberProductCategory(storeMember.getId())) {
//				sql.append(" and ("
//						+ " o.store_member=? or"
//						+ " exists ("
//						+ " select 1 from xx_store_member_product_category sc where sc.type=0 and sc.store_member=?"
//						+ " and ("
//						+ " (sc.product_category is null and ii.product is null)"
//						+ " or ( sc.product_category is not null and (sc.product_category=pc.id or pc.tree_path like CONCAT('%,', sc.product_category,',%'))) "
//						+ " )"
//						+ " )"
//						+ " )");
//				list.add(storeMember.getId());
//				list.add(storeMember.getId());
//			}
//		}

		if (storeMemberName != null) {
			sql.append(" and sm.name like ?");
			list.add("%" + storeMemberName + "%");

		}

		if (saleOrgId != null) {
			sql.append(" and so.id=?");
			list.add(saleOrgId);
		}

		int pageNumber = pageable.getPageNumber();
		int pageSize = pageable.getPageSize();
		int num = (pageNumber - 1) * pageSize;
		sql.append(" group by o.id order by o.create_date desc limit "
				+ num
				+ ","
				+ (pageSize + 1)
				+ ") oo");

		pSql.append(sql);
		pSql.append(" left join xx_delivery_corp dc on dc.id = oo.delivery"
				+ " left join xx_area a on a.id = oo.area"
				+ " left join xx_order_item i on i.orders = oo.id"
				+ " group by oo.id"
				+ " order by oo.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> result = getNativeDao().findListMap(pSql.toString(),
				objs,
				0);
		long total = 0;
		if (result.size() == pageSize + 1) {
			total = (pageNumber + 1) * pageSize;
			result.remove(result.size() - 1);
		}
		else
			total = ((pageNumber - 1) * pageSize) + result.size();

		return new Page<Map<String, Object>>(result, total, pageable);
	}

	/**
	 * 查找订单分页数据(区分订单类型)
	 * 
	 * @param orderSn
	 * @param outTradeNo
	 * @param orderStatus
	 * @param shippingStatus
	 * @param warehouseId
	 * @param storeIds
	 * @param deliveryCorpId
	 * @param firstTime
	 * @param lastTime
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findPage(String orderSn,
			String outTradeNo, Integer[] orderStatus, Integer[] shippingStatus,
			Long warehouseId, Long[] storeIds, String consignee, String phone,
			String address, Long deliveryCorpId, Long[] productIds,
			Integer[] paymentStatus, Integer[] flag, Integer orderType,
			String firstTime, String lastTime, Integer[] confirmStatus,
			Integer isReturn, Pageable pageable, Integer[] orderCategory,
			String storeMemberName, String contractName) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder pSql = new StringBuilder();
		pSql.append("select oo.*,dc.name delivery_corp_name,a.full_name area_name, (sum(i.price * i.quantity) + oo.offset_amount + oo.freight - oo.coupon_discount - oo.promotion_discount) amount from ");

		StringBuilder sql = new StringBuilder();
		sql.append(" (select o.id,o.create_date,o.order_date,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.payment_status,o.ex_status,"
				+ " o.contract_sn,o.contract_name,contract_member,o.payment_method_name,"
				+ "o.order_type,o.change_memo,o.buyer_remark,o.seller_remark,o.delivery_memo,o.memo,o.consignee,o.phone,o.address,o.delivery,o.area,"
				+ "o.offset_amount,o.freight,o.coupon_discount,o.promotion_discount,o.flag,o.wf_id,o.wf_state,o.confirm_status,w.name warehouse_name,"
				+ "s.name store_name,s.out_trade_no store_code,s.balance store_balance,s.budget store_budget,so.name sale_org_name,so.id sale_org_id, csm.name check_store_member_name,o.check_date,"
				+ "so.budget sale_org_budget,sd.value system_value,sm.name store_member_name,o.stores "
				+ "  from xx_order o"
				+ " left join xx_store s on s.id = o.stores"
				+ " left join xx_sale_org so on so.id = o.sale_org"
				+ " left join xx_warehouse w on w.id = o.warehouse"
				+ " left join xx_order_item ii on o.id=ii.orders"
				+ " left join xx_product p on ii.product=p.id"
				+ " left join xx_product_category pc on p.product_category=pc.id"
				+ " left join xx_system_dict sd on o.freight_charge_type=sd.id"
				+ " left join xx_store_member sm on o.store_member = sm.id"
				+ " left join xx_store_member csm on csm.id = o.check_store_member"
				+ " where 1=1");

		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (!ConvertUtil.isEmpty(storeMemberName)) {
			sql.append(" and sm.name like ?");
			list.add("%" + storeMemberName + "%");
		}

		if (!ConvertUtil.isEmpty(contractName)) {
			sql.append(" and o.contract_name like ?");
			list.add("%" + contractName + "%");
		}

		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(consignee)) {
			sql.append(" and o.consignee like ?");
			list.add("%" + consignee + "%");
		}
		if (!ConvertUtil.isEmpty(phone)) {
			sql.append(" and o.phone like ?");
			list.add("%" + phone + "%");
		}
		if (!ConvertUtil.isEmpty(address)) {
			sql.append(" and o.address like ?");
			list.add("%" + address + "%");
		}
		if (orderCategory != null && orderCategory.length > 0) {
			String os = "";
			for (int i = 0; i < orderCategory.length; i++) {
				if (i == orderCategory.length - 1)
					os += orderCategory[i];
				else
					os += orderCategory[i] + ",";
			}
			sql.append(" and o.order_category in (" + os + ")");
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		if (shippingStatus != null && shippingStatus.length > 0) {
			String ss = "";
			for (int i = 0; i < shippingStatus.length; i++) {
				if (i == shippingStatus.length - 1)
					ss += shippingStatus[i];
				else
					ss += shippingStatus[i] + ",";
			}
			sql.append(" and o.shipping_status in (" + ss + ")");
		}
		if (paymentStatus != null && paymentStatus.length > 0) {
			String ps = "";
			for (int i = 0; i < paymentStatus.length; i++) {
				if (i == paymentStatus.length - 1)
					ps += paymentStatus[i];
				else
					ps += paymentStatus[i] + ",";
			}
			sql.append(" and o.payment_status in (" + ps + ")");
		}
		if (flag != null && flag.length > 0) {
			String fs = "";
			for (int i = 0; i < flag.length; i++) {
				if (i == flag.length - 1)
					fs += flag[i];
				else
					fs += flag[i] + ",";
			}
			sql.append(" and o.flag in (" + fs + ")");
		}
		/*
		 * if (storeId != null) { sql.append(" and o.stores=?");
		 * list.add(storeId); }
		 */
		if (storeIds != null && storeIds.length > 0) {
			String s = "";
			for (int i = 0; i < storeIds.length; i++) {
				if (i == storeIds.length - 1)
					s += storeIds[i];
				else
					s += storeIds[i] + ",";
			}
			sql.append("  and o.stores in (" + s + ")");
		}
		if (deliveryCorpId != null) {
			sql.append(" and o.delivery=?");
			list.add(deliveryCorpId);
		}
		if (orderType != null) {
			sql.append(" and o.order_type=?");
			list.add(orderType);
		}
		if (warehouseId != null) {
			sql.append(" and o.id in (select orders from xx_order_item where warehouse = ?)");
			list.add(warehouseId);
		}
		if (productIds != null && productIds.length > 0) {
			String fs = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					fs += productIds[i];
				else
					fs += productIds[i] + ",";
			}
			sql.append(" and ii.product in (" + fs + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and o.order_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and o.order_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		else {

			sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());

//			Long saleOrgId = null;
//			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
//			if (storeMemberSaleOrg != null) {
//				saleOrgId = storeMemberSaleOrg.getSaleOrg().getId();
//			}
//			sql.append(" and (so.id="
//					+ saleOrgId
//					+ " or so.tree_path like '%,"
//					+ saleOrgId
//					+ ",%')");
		}

		if (confirmStatus != null && confirmStatus.length > 0) {
			String os = "";
			for (int i = 0; i < confirmStatus.length; i++) {
				if (i == confirmStatus.length - 1)
					os += confirmStatus[i];
				else
					os += confirmStatus[i] + ",";
			}
			sql.append(" and o.confirm_status in (" + os + ")");
		}

		if (isReturn != null && isReturn == 1) {
			sql.append(" and exit(SELECT 1,oi.shipped_quantity - sum(IFNULL(ri.quantity, 0)) r_quantity"
					+ " from xx_order_item oi left join xx_b2b_returns_item ri on ri.order_item = oi.id"
					+ " where oi.orders = o.id group by oi.id having r_quantity > 0)");
		}

//		if (orderType != null && orderType == 2) {
//			if (this.existsStoreMemberProductCategory(storeMember.getId())) {
//				sql.append(" and ("
//						+ " o.store_member=? or"
//						+ " exists ("
//						+ " select 1 from xx_store_member_product_category sc where sc.type=0 and sc.store_member=?"
//						+ " and ("
//						+ " (sc.product_category is null and ii.product is null)"
//						+ " or ( sc.product_category is not null and (sc.product_category=pc.id or pc.tree_path like CONCAT('%,', sc.product_category,',%'))) "
//						+ " )"
//						+ " )"
//						+ " )");
//				list.add(storeMember.getId());
//				list.add(storeMember.getId());
//			}
//		}

		int pageNumber = pageable.getPageNumber();
		int pageSize = pageable.getPageSize();
		int num = (pageNumber - 1) * pageSize;
		sql.append(" group by o.id order by o.create_date desc limit "
				+ num
				+ ","
				+ (pageSize + 1)
				+ ") oo");

		pSql.append(sql);
		pSql.append(" left join xx_delivery_corp dc on dc.id = oo.delivery"
				+ " left join xx_area a on a.id = oo.area"
				+ " left join xx_order_item i on i.orders = oo.id"
				+ " group by oo.id"
				+ " order by oo.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> result = getNativeDao().findListMap(pSql.toString(),
				objs,
				0);
		long total = 0;
		if (result.size() == pageSize + 1) {
			total = (pageNumber + 1) * pageSize;
			result.remove(result.size() - 1);
		}
		else
			total = ((pageNumber - 1) * pageSize) + result.size();

		return new Page<Map<String, Object>>(result, total, pageable);
	}

	public boolean existsStoreMemberProductCategory(Long storeMemberId) {
		String sql = "select count(1) from xx_store_member_product_category where type=0 and store_member="
				+ storeMemberId;
		int count = getNativeDao().findInt(sql);
		return count > 0;
	}

	public Page<Map<String, Object>> newfindPage(String orderSn,
			String outTradeNo, Integer[] orderStatus, Integer[] shippingStatus,
			Long warehouseId, Long[] storeIds, String consignee, String phone,
			String address, Long deliveryCorpId, Long[] productIds,
			Integer[] paymentStatus, Integer[] flag, Integer orderType,
			String firstTime, String lastTime, Integer[] confirmStatus,
			Integer isReturn, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder pSql = new StringBuilder();
		pSql.append("select oo.*,dc.name delivery_corp_name,a.full_name area_name, (sum(i.price * i.quantity) + oo.offset_amount + oo.freight - oo.coupon_discount - oo.promotion_discount) amount from ");

		StringBuilder sql = new StringBuilder();
		sql.append(" (select o.id,o.create_date,o.order_date,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.payment_status,o.ex_status,"
				+ "o.order_type,o.change_memo,o.buyer_remark,o.seller_remark,o.delivery_memo,o.memo,o.consignee,o.phone,o.address,o.delivery,o.area,"
				+ "o.offset_amount,o.freight,o.coupon_discount,o.promotion_discount,o.flag,o.wf_id,o.wf_state,o.confirm_status,w.name warehouse_name,"
				+ "s.name store_name,s.out_trade_no store_code,s.balance store_balance,s.budget store_budget,so.name sale_org_name, csm.name check_store_member_name,o.check_date,"
				+ "so.budget sale_org_budget,sd.value system_value,sm.name store_member_name,o.stores "
				+ "  from xx_order o"
				+ " left join xx_store s on s.id = o.stores"
				+ " left join xx_sale_org so on so.id = o.sale_org"
				+ " left join xx_warehouse w on w.id = o.warehouse"
				+ " left join xx_order_item ii on o.id=ii.orders"
				+ " left join xx_system_dict sd on o.freight_charge_type=sd.id"
				+ " left join xx_store_member sm on o.store_member = sm.id"
				+ " left join xx_store_member csm on csm.id = o.check_store_member"
				+ " where 1=1");

		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(consignee)) {
			sql.append(" and o.consignee like ?");
			list.add("%" + consignee + "%");
		}
		if (!ConvertUtil.isEmpty(phone)) {
			sql.append(" and o.phone like ?");
			list.add("%" + phone + "%");
		}
		if (!ConvertUtil.isEmpty(address)) {
			sql.append(" and o.address like ?");
			list.add("%" + address + "%");
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		if (shippingStatus != null && shippingStatus.length > 0) {
			String ss = "";
			for (int i = 0; i < shippingStatus.length; i++) {
				if (i == shippingStatus.length - 1)
					ss += shippingStatus[i];
				else
					ss += shippingStatus[i] + ",";
			}
			sql.append(" and o.shipping_status in (" + ss + ")");
		}
		if (paymentStatus != null && paymentStatus.length > 0) {
			String ps = "";
			for (int i = 0; i < paymentStatus.length; i++) {
				if (i == paymentStatus.length - 1)
					ps += paymentStatus[i];
				else
					ps += paymentStatus[i] + ",";
			}
			sql.append(" and o.payment_status in (" + ps + ")");
		}
		if (flag != null && flag.length > 0) {
			String fs = "";
			for (int i = 0; i < flag.length; i++) {
				if (i == flag.length - 1)
					fs += flag[i];
				else
					fs += flag[i] + ",";
			}
			sql.append(" and o.flag in (" + fs + ")");
		}
		/*
		 * if (storeId != null) { sql.append(" and o.stores=?");
		 * list.add(storeId); }
		 */
		if (storeIds != null && storeIds.length > 0) {
			String s = "";
			for (int i = 0; i < storeIds.length; i++) {
				if (i == storeIds.length - 1)
					s += storeIds[i];
				else
					s += storeIds[i] + ",";
			}
			sql.append("  and o.stores in (" + s + ")");
		}
		if (deliveryCorpId != null) {
			sql.append(" and o.delivery=?");
			list.add(deliveryCorpId);
		}
		if (orderType != null) {
			sql.append(" and o.order_type=?");
			list.add(orderType);
		}
		if (warehouseId != null) {
			sql.append(" and o.id in (select orders from xx_order_item where warehouse = ?)");
			list.add(warehouseId);
		}
		if (productIds != null && productIds.length > 0) {
			String fs = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					fs += productIds[i];
				else
					fs += productIds[i] + ",";
			}
			sql.append(" and ii.product in (" + fs + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and o.order_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and o.order_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		String storeAuth = storeMemberService.storeAuth();
		if (storeAuth != null) {
			sql.append(" and o.stores in (" + storeAuth + ")");
		}

		if (confirmStatus != null && confirmStatus.length > 0) {
			String os = "";
			for (int i = 0; i < confirmStatus.length; i++) {
				if (i == confirmStatus.length - 1)
					os += confirmStatus[i];
				else
					os += confirmStatus[i] + ",";
			}
			sql.append(" and o.confirm_status in (" + os + ")");
		}

		if (isReturn != null && isReturn == 1) {
			sql.append(" and exit(SELECT 1,oi.shipped_quantity - sum(IFNULL(ri.quantity, 0)) r_quantity"
					+ " from xx_order_item oi left join xx_b2b_returns_item ri on ri.order_item = oi.id"
					+ " where oi.orders = o.id group by oi.id having r_quantity > 0)");
		}

		int pageNumber = pageable.getPageNumber();
		int pageSize = pageable.getPageSize();
		int num = (pageNumber - 1) * pageSize;
		sql.append(" group by o.id order by o.create_date desc limit "
				+ num
				+ ","
				+ (pageSize + 1)
				+ ") oo");

		pSql.append(sql);
		pSql.append(" left join xx_delivery_corp dc on dc.id = oo.delivery"
				+ " left join xx_area a on a.id = oo.area"
				+ " left join xx_order_item i on i.orders = oo.id"
				+ " group by oo.id"
				+ " order by oo.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> result = getNativeDao().findListMap(pSql.toString(),
				objs,
				0);
		long total = 0;
		if (result.size() == pageSize + 1) {
			total = (pageNumber + 1) * pageSize;
			result.remove(result.size() - 1);
		}
		else
			total = ((pageNumber - 1) * pageSize) + result.size();

		return new Page<Map<String, Object>>(result, total, pageable);
	}

	/**
	 * 根据订单id查找订单
	 * 
	 * <AUTHOR>
	 * @date 2017年8月31日
	 * @param ids
	 * @return
	 */
	public List<Map<String, Object>> findListById(String ids) {
		StringBuilder sql = new StringBuilder();
		sql.append("select o.id,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.ex_status,o.order_type,"
				+ " o.change_memo,o.buyer_remark,o.seller_remark,o.delivery_memo,o.memo,o.consignee,o.phone,o.address,"
				+ " o.zip_code,w.id warehouse_id, w.name warehouse_name,a.id area_id,a.full_name area_name, a.tree_path area_tree_path,"
				+ " (sum(i.price * i.quantity) + o.offset_amount + o.freight - o.coupon_discount - o.promotion_discount) amount,s.name store_name,"
				+ " sd.id system_dict_id,sd.value system_dict_value"
				+ " from xx_order o"
				+ " left join xx_warehouse w on o.warehouse=w.id left join xx_area a on o.area=a.id"
				+ " left join xx_order_item i on i.orders=o.id left join xx_store s on o.stores=s.id"
				+ " left join xx_system_dict sd on o.freight_charge_type=sd.id"
				+ " where 1=1 ");
		sql.append(" and o.id in (" + ids + ") group by o.id");
		List<Map<String, Object>> orders = getNativeDao().findListMap(sql.toString(),
				null,
				0);
		return orders;
	}

	/**
	 * 根据订单id查找订单明细
	 * 
	 * @param orderIds
	 * @return
	 */
	public List<Map<String, Object>> findOrderItemListByOrderId(String orderIds,
			Long sbuId,Long[] organizationIds,Boolean isDefault) {

		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		
		sql.append("select i.*,xsd.lower_code,p.cost,p.market_price,i.price apply_pricex,i.policy_point,i.price_id ppriceId,i.product_org_price,i.price_difference, "
				+ "p.volume,p.weight,p.name p_name,p.vonder_code p_vonder_code,p.price origprice,p.is_gift,p.unit pUnit,"
				+ "p.per_box,p.product_grade,p.spec,p.manufactory_name,p.supplier,pc.id product_category_id,pc.name product_category_name,"
				+ "w.id warehouse_id, w.name warehouse_name,w.erp_warehouse_code,p.erp_length,p.erp_width,"
				+ "substring(group_concat(concat(pg.name,':',ip.name) separator ';'),1,300) parts_name,"
				+ "oi.quantity,oi.invoice_value invoice_value,xoi.invoice_code invoice_code,oi.quantity invoiceQuantity,");
		sql.append(" case pc.tree_path when pc.tree_path like '%5141%' then 1 else 0 end isWare,sdt.id level_Id,sdt.value levelName,"
				+ "  po.id product_organization_id,po.name product_organization_name,p.wood_type_or_color woodTypeOrColor,"
				+ "  cn.id colour_number_id,cn.value colour_number_name,mc.id moisture_content_id,mc.value moisture_content_name ");
		sql.append(" from xx_order_item i");
		sql.append(" left join xx_warehouse w on i.warehouse=w.id");
		sql.append(" left join xx_system_dict xsd on i.factory_system_dict=xsd.id");
		sql.append(" left join xx_product p on i.product=p.id");
		sql.append(" left join xx_order_item_parts ip on i.id = ip.order_item");
		sql.append(" left join xx_parts_group pg on ip.parts_group = pg.id");
		sql.append(" left join xx_product_category pc on p.product_category=pc.id");
		sql.append(" left join xx_order_invoice_item oi on i.id=oi.order_item");
		sql.append(" left join xx_order_invoice xoi on xoi.id=oi.order_invoice");
		sql.append(" left join xx_system_dict sdt on sdt.id = i.product_level");
		sql.append(" left join xx_organization po ON po.id = i.product_organization ");
		sql.append(" left join xx_system_dict cn on cn.id = i.colour_numbers ");
		sql.append(" left join xx_system_dict mc on mc.id = i.moisture_contents ");
		
		sql.append(" where i.orders in (" + orderIds + ") ");
		
		//经营组织
		if (!ConvertUtil.isEmpty(organizationIds) && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql.append(" and po.id in (" + os + ")");
		}
		
		if(!ConvertUtil.isEmpty(isDefault)){
			if(isDefault){
				//用户经营组织
				String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
				if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
					if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
						sql.append(" and po.id in (" + organizationIdS + ")");
					}else{
						sql.append(" and po.id is null");
					}
				}
			}
		}
		
		sql.append("group by i.id");
		
		if(sbuId!=null &&sbuId==3){
			sql.append(" order by p.manufactory_name desc");
		}
		
		List<Map<String, Object>> orderItems = getNativeDao().findListMap(sql.toString(),
				null,
				0);
		return orderItems;
	}

	/**
	 * 根据订单id查找订单毛利
	 * 
	 * @param orderIds
	 * @return
	 */
	public List<Map<String, Object>> findGrossProfitAnalysisListByOrderId(
			String orderIds) {

		StringBuilder sql = new StringBuilder();
		sql.append("select i.*,p.cost,p.market_price PmarketPrice,i.price apply_pricex,p.volume,p.weight,p.name p_name,p.vonder_code p_vonder_code,p.price origprice,p.is_gift,p.per_box,p.spec");
		sql.append(" from xx_gross_profit_analysis i");
		sql.append(" left join xx_product p on i.product=p.id");
		sql.append(" where i.orders in (" + orderIds + ") group by i.id");
	
		List<Map<String, Object>> orderItems = getNativeDao().findListMap(sql.toString(),
				null,
				0);
		return orderItems;
	}

	/**
	 * 根据订单明细id查找订单明细
	 * 
	 * @param itemIds
	 * @return
	 */
	public List<Map<String, Object>> findOrderItemListByItemId(String itemIds) {

		StringBuilder sql = new StringBuilder();
		sql.append("select o.sn order_sn,s.name store_name,w.id warehouse_id,w.name warehouse_name, i.*");
		sql.append(" from xx_order_item i");
		sql.append(" left join xx_order o on i.orders=o.id");
		sql.append(" left join xx_store s on o.stores=s.id");
		sql.append(" left join xx_warehouse w on i.warehouse=w.id");
		sql.append(" where i.id in (" + itemIds + ")");

		List<Map<String, Object>> orderItems = getNativeDao().findListMap(sql.toString(),
				null,
				0);

		return orderItems;
	}

	/**
	 * 根据parent查找订单明细
	 * 
	 * @param parent
	 * @return
	 */
	public List<Map<String, Object>> findOrderItemListParent(Long parent) {

		StringBuilder sql = new StringBuilder();
		sql.append("select o.id order_id, o.sn order_sn,s.name store_name,w.id warehouse_id,w.name warehouse_name, i.*");
		sql.append(" from xx_order_item i");
		sql.append(" left join xx_order o on i.orders=o.id");
		sql.append(" left join xx_store s on o.stores=s.id");
		sql.append(" left join xx_warehouse w on i.warehouse=w.id");
		sql.append(" where i.parent = ?");

		List<Map<String, Object>> orderItems = getNativeDao().findListMap(sql.toString(),
				new Object[] { parent },
				0);

		return orderItems;
	}

	/**
	 * 根据明细查找订单
	 * 
	 * @param id
	 * @return
	 */
	public Map<String, Object> findMapByItemId(Long id) {

		StringBuilder sql = new StringBuilder();
		sql.append("select o.id,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.area,i.id item_id,i.product,i.full_name,i.quantity,i.warehouse,i.stock,i.bom_flag");
		sql.append(" from xx_order o,xx_order_item i,xx_product p where o.id=i.orders and i.product=p.id and i.id=?");

		List<Map<String, Object>> orders = getNativeDao().findListMap(sql.toString(),
				new Object[] { id },
				1);
		if (!orders.isEmpty()) {
			return orders.get(0);
		}
		return null;
	}

	/**
	 * 根据id查找订单
	 * 
	 * @param id
	 * @return
	 */
	public Map<String, Object> findMapById(Long id) {

		String sql = "select o.id,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.area from xx_order o where o.id=?";

		List<Map<String, Object>> orders = getNativeDao().findListMap(sql,
				new Object[] { id },
				1);
		if (!orders.isEmpty()) {
			return orders.get(0);
		}
		return null;
	}

	/**
	 * 更新订单状态
	 * 
	 * @param orderId
	 * @return
	 */
	public int updateOrderStatus(Long orderId) {

		String sql = "update xx_order set order_status = 6 where order_status = 5 and payment_status = 2 and id = ?";
		int count = getNativeDao().update(sql, new Object[] { orderId });
		return count;
	}

	/**
	 * 查找没有分配仓库的订单明细数量
	 * 
	 * @param orderId
	 * @return
	 */
	public int countItemHasnotWarehouse(Long orderId) {

		String sql = "select count(1) from xx_order_item i,xx_product p where i.orders = ? and i.warehouse is null and i.bom_flag <> 1";
		int count = getNativeDao().findInt(sql, new Object[] { orderId });
		return count;
	}

	/**
	 * 更新订单明细仓库
	 * 
	 * @param warehouseId
	 * @param itemId
	 * @param useLockStock
	 */
	public int updateItemWarehouse(Long warehouseId, Long itemId,
			int useLockStock, Boolean isSuit) {

		String sql = "";
		if (isSuit != null && isSuit) {
			sql = "update xx_order_item set warehouse=? where id=?";
		}
		else if (useLockStock == 1) {
			sql = "update xx_order_item i,xx_stock s set i.warehouse=s.warehouse,i.stock=s.id where i.product=s.product and s.warehouse=? and i.id=?";
		}
		else {
			sql = "update xx_order_item set warehouse=? where id=?";
		}
		int count = getNativeDao().update(sql,
				new Object[] { warehouseId, itemId });
		return count;
	}

	/**
	 * 设置订单旗标
	 * 
	 * @param ids
	 * @param flag
	 */
	public void setFlag(String ids, Integer flag) {

		StringBuilder sql = new StringBuilder();
		sql.append("update xx_order set flag = ? where id in (" + ids + ")");

		getNativeDao().update(sql.toString(), new Object[] { flag });
	}

	public BigDecimal findAmountPaid(Long storeId, Long saleOrgId) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select sum(o.amount_paid) amount_paid from xx_order o");
		sql.append(" where order_status = 6 and order_type = 2 and company_info_id = ?");
		list.add(companyInfoId);
		if (storeId != null) {
			sql.append(" and o.stores = ?");
			list.add(storeId);
		}
		if (saleOrgId != null) {
			sql.append(" and o.stores in (select id from xx_store where sale_org = ?)");
			list.add(saleOrgId);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Map<String, Object> map = getNativeDao().findSingleMap(sql.toString(),
				objs);
		BigDecimal amount_paid = BigDecimal.ZERO;
		if (map != null && map.get("amount_paid") != null) {
			amount_paid = new BigDecimal(map.get("amount_paid").toString());
		}

		return amount_paid;
	}

	public Integer countCreate(String orderSn, String outTradeNo, Long storeId,
			Long warehouseId, String sn, String name, String vonderCode,
			String model, Boolean isToShip, Integer[] orderStatus,
			Boolean isSuit, Long supplierId, Pageable pageable,
			String moistureContent,String colourNumber,String batch,
			Long[] warehouseIds,Long[] organizationIds) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();

		StringBuilder sql = new StringBuilder();
		sql.append(" select count(ii.id) from (");
		sql.append(" select i.id from xx_order_item i");
		sql.append(" left join xx_order o on i.orders = o.id");
		sql.append(" left join xx_store s on o.stores = s.id");
		sql.append(" left join xx_area a on o.area = a.id");
		sql.append(" left join xx_warehouse w on w.id = i.warehouse");
		sql.append(" left join xx_product p on p.id = i.product");
		sql.append(" left join xx_contract_price_item cpi on i.product = cpi.product and exists (");
		sql.append(" select 1 from xx_contract_price cp where cpi.contract_price = cp.id and cpi.doc_status=2)");
		sql.append(" left join xx_contract_price cp on cpi.contract_price = cp.id and cpi.doc_status=2 ");
		sql.append(" left join xx_store st on st.id = cp.store");
		sql.append(" left join xx_organization po on po.id = i.product_organization");
		sql.append(" left join xx_warehouse wo on wo.id = o.warehouse");
		sql.append(" where 1=1");
		
		//仓库
		if (warehouseIds != null && warehouseIds.length > 0) {
			String os = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					os += warehouseIds[i];
				else
					os += warehouseIds[i] + ",";
			}
			sql.append(" and wo.id in (" + os + ")");
		}
		//经营组织
		if (organizationIds != null && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql.append(" and po.id in (" + os + ")");
		}
		
		if (!ConvertUtil.isEmpty(moistureContent) ) {
			sql.append(" and i.moisture_content like ? ");
			list.add("%" + moistureContent.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(colourNumber) ) {
			sql.append(" and i.colour_number like ? ");
			list.add("%" + colourNumber.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(batch) ) {
			sql.append(" and i.batch like ? ");
			list.add("%" + batch.trim() + "%");
		}
		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (supplierId != null) {
			sql.append(" and st.id = ?");
			list.add(supplierId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and i.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and i.full_name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and i.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(model)) {
			sql.append(" and i.model like ?");
			list.add("%" + model + "%");
		}
		if (isToShip != null && isToShip) {
			sql.append(" and i.quantity > i.ship_plan_quantity");
		}
		if (storeId != null) {
			sql.append(" and o.stores = ?");
			list.add(storeId);
		}
		if (warehouseId != null) {
			sql.append(" and i.warehouse = ?");
			list.add(warehouseId);
		}
		if (isSuit != null) {
			if (isSuit) {
				sql.append(" and i.bom_flag = 1");
			}
			else {
				sql.append(" and i.bom_flag <> 1");
			}
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		String storeAuth = storeMemberService.storeAuth();
		if (storeAuth != null) {
			sql.append(" and o.stores in (" + storeAuth + ")");
		}
		
		/**
		 * 用户经营组织权限
		 */
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		if(storeMemberOrganization==0){
			if(!ConvertUtil.isEmpty(storeMemberId)){
				//用户经营组织
				sql.append(" AND po.id in (SELECT DISTINCT smo.organization FROM xx_store_member_organization smo WHERE 1=1 "
						+ " AND smo.company_info_id ="+companyInfoId+" AND smo.store_member ="+storeMemberId+") ");	
			}	
		}
		
		sql.append(" group by i.id order by o.id desc)ii");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}

	public List<Map<String, Object>> findItemListCreate(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, String model, Boolean isToShip,
			Integer[] orderStatus, Boolean isSuit, Long supplierId, Long[] ids,
			Integer page, Integer size) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select i.*, o.id order_id, o.sn order_sn, o.out_trade_no out_no,o.consignee,o.mobile,o.phone, o.address,o.order_date,o.check_date,o.shipping_method_name, ");
		sql.append("s.id store_id, s.name store_name, a.id area_id, a.full_name area_full_name, w.name warehouse_name,p.volume,wo.name order_warehouse_name, ");
		sql.append(" substring(group_concat(st.name separator ';'),1,300) supplier_name, csm.name check_store_member_name, so.name sale_org_name,bd.value business_division_value, ");
		sql.append(" s.alias store_alias,tT.value transportTypeName,po.id product_organization_id,po.name product_organization_name,si.shipped_time as shippedTime, "
				+ "  cn.id colour_number_id,cn.value colour_number_name,mc.id moisture_content_id,mc.value moisture_content_name ");
		sql.append(" from xx_order_item i");
		sql.append(" left join xx_order o on i.orders = o.id");
		sql.append(" left join xx_store s on o.stores = s.id");
		sql.append(" left join xx_shipping_item si on si.order_item=i.id ");
		sql.append(" left join xx_area a on o.area = a.id");
		sql.append(" left join xx_warehouse w on w.id = i.warehouse");
		sql.append(" left join xx_product p on p.id = i.product");
		sql.append(" left join xx_contract_price_item cpi on i.product = cpi.product and exists (");
		sql.append(" select 1 from xx_contract_price cp where cpi.contract_price = cp.id and cpi.doc_status=2)");
		sql.append(" left join xx_contract_price cp on cpi.contract_price = cp.id and cpi.doc_status=2 ");
		sql.append(" left join xx_store st on st.id = cp.store");
		sql.append(" left join xx_store_member csm on csm.id = o.check_store_member");
		sql.append(" left join xx_sale_org so on so.id = o.sale_org");
		sql.append(" left join xx_system_dict bd on o.business_type = bd.id");
		sql.append(" left join xx_organization po on po.id = i.product_organization");
		sql.append(" left join xx_warehouse wo on wo.id = o.warehouse");
		sql.append(" left join xx_system_dict tT on tT.id = o.transport_type");
		sql.append(" left join xx_system_dict cn on cn.id = i.colour_numbers ");
		sql.append(" left join xx_system_dict mc on mc.id = i.moisture_contents ");
		sql.append(" left join xx_sbu sb on sb.id = o.sbu ");
		sql.append(" where 1=1");
		
		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (supplierId != null) {
			sql.append(" and st.id = ?");
			list.add(supplierId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and i.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and i.full_name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and i.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(model)) {
			sql.append(" and i.model like ?");
			list.add("%" + model + "%");
		}

		if (isToShip != null && isToShip) {
			sql.append(" and i.quantity > i.ship_plan_quantity");
		}
		if (storeId != null) {
			sql.append(" and o.stores = ?");
			list.add(storeId);
		}
		if (warehouseId != null) {
			sql.append(" and i.warehouse = ?");
			list.add(warehouseId);
		}
		if (isSuit != null) {
			if (isSuit) {
				sql.append(" and i.bom_flag = 1");
			}else {
				sql.append(" and i.bom_flag <> 1");
			}
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		String storeAuth = storeMemberService.storeAuth();
		if (storeAuth != null) {
			sql.append(" and o.stores in (" + storeAuth + ")");
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and i.id in (" + inIds + ")");
		}

		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		sql.append(" group by i.id order by o.id desc");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return maps;
	}

	/**
	 * 查询订单明细分页数据
	 */
	public Page<Map<String, Object>> findItemPage(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String consignee, String vonderCode, String model,
			Boolean isToShip, Integer[] orderStatus, Boolean isSuit,
			Long supplierId,String storeMemberName,Long[] saleOrgId,
			Boolean isPurchase, Pageable pageable,Integer[] status,
			String moistureContent,String colourNumber,String batch,
			Integer pageType,Long[] warehouseIds,Long[] organizationIds,
			Long[] ids,Long[] sbuId) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" select i.*, o.id order_id, o.sn order_sn, o.create_date order_date, o.out_trade_no out_no,o.consignee,o.mobile,o.phone, o.address,o.check_date, ");
		sql.append(" s.id store_id, s.name store_name, a.id area_id, a.full_name area_full_name, w.id warehouse_id, w.name warehouse_name,");
		sql.append(" so.name sale_org_name, csm.name check_store_member_name,xsm.name store_member_name,wi.id item_warehouse_id,wi.name item_warehouse_name, p.volume, ");
		sql.append(" ifnull(sum(si.quantity),0) ship_quantity,ifnull(sum(si.box_quantity),0) ship_box_quantity,sdt.id level_Id,sdt.value levelName,s.alias store_alias, ");
		sql.append(" ifnull(sum(si.branch_quantity),0) ship_branch_quantity,p.per_box,bd.value business_division_value,sb.name sb_name,p.manufactory_name,p.supplier, ");
		sql.append(" si.shipped_quantity shipping_item_shipped_quantity,si.shipped_branch_quantity,si.shipped_box_quantity,po.id product_organization_id,po.name product_organization_name, ");
		sql.append(" si.shipped_time shippedTime,cn.id colour_number_id,cn.value colour_number_name,mc.id moisture_content_id,mc.value moisture_content_name,sh.shipping_time, ");
		sql.append(" bt.value business_type_value,o.shipping_method_name,tt.value transport_type_name ");
		sql.append(" from xx_order_item i");
		sql.append(" left join xx_order o on i.orders = o.id");
		sql.append(" left join xx_system_dict sdt on sdt.id = i.product_level");
		sql.append(" left join xx_store s on o.stores = s.id");
		sql.append(" left join xx_area a on o.area = a.id");
		sql.append(" left join xx_warehouse w on w.id = o.warehouse");
		sql.append(" left join xx_warehouse wi on wi.id = i.warehouse");
		sql.append(" left join xx_product p on p.id = i.product");
		sql.append(" left join xx_sale_org so on o.sale_org=so.id");
		sql.append(" left join xx_store_member csm on csm.id = o.check_store_member");
		sql.append(" left join xx_store_member xsm on xsm.id = o.store_member");
		sql.append(" left join xx_shipping_item si on si.order_item=i.id and (si.status is null or si.status!=2)");
		sql.append("  and not exists (select 1 from xx_shipping where id=si.shipping and status=2)");
		sql.append(" left join xx_shipping sh on sh.id=si.shipping and sh.status<>2");
		sql.append(" left join xx_system_dict bd on p.business_division=bd.id");
		sql.append(" left join xx_sbu sb on o.sbu=sb.id");
		sql.append(" left join xx_organization po on po.id = i.product_organization");
		sql.append(" left join xx_system_dict cn on cn.id = i.colour_numbers ");
		sql.append(" left join xx_system_dict mc on mc.id = i.moisture_contents ");
		sql.append(" left join xx_system_dict bt on o.business_type = bt.id");
		sql.append(" left join xx_system_dict tt on tt.id = o.transport_type");
		sql.append(" where i.is_purchase is null and o.order_type <> 4");
		
		if (!ConvertUtil.isEmpty(ids) && ids.length >0) {
			String os = "";
			for (int i = 0; i < ids.length; i++) {
				if (i == ids.length - 1)
					os += ids[i];
				else
					os += ids[i] + ",";
			}
			sql.append(" and i.id in (" + os + ")");
		}
		//仓库
		if (warehouseIds != null && warehouseIds.length > 0) {
			String os = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					os += warehouseIds[i];
				else
					os += warehouseIds[i] + ",";
			}
			sql.append(" and w.id in (" + os + ")");
		}
		//经营组织
		if (organizationIds != null && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql.append(" and po.id in (" + os + ")");
		}
		if(pageType!=null&&pageType==2){
			sql.append(" and o.shipping_status in (1,2) and si.shipped_quantity>0");
			isToShip=false;
		}
		if (!ConvertUtil.isEmpty(pageType)) {
			sql.append(" and si.bill_type = ?");
			list.add(pageType);
		}
		if (!ConvertUtil.isEmpty(moistureContent) ) {
			sql.append(" and i.moisture_content like ? ");
			list.add("%" + moistureContent.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(colourNumber) ) {
			sql.append(" and i.colour_number like ? ");
			list.add("%" + colourNumber.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(batch) ) {
			sql.append(" and i.batch like ? ");
			list.add("%" + batch.trim() + "%");
		}
		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (supplierId != null) {
			sql.append(" and st.id = ?");
			list.add(supplierId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and i.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and i.full_name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(consignee)) {
			sql.append(" and o.consignee like ?");
			list.add("%" + consignee + "%");
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and i.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(model)) {
			sql.append(" and i.model like ?");
			list.add("%" + model + "%");
		}
		if (isToShip != null && isToShip) {
			sql.append(" and i.quantity > i.ship_plan_quantity");
		}
		if (storeId != null) {
			sql.append(" and o.stores = ?");
			list.add(storeId);
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and sh.status in (" + os + ")");
		}
		//2019-05-16 增加创建人，多机构查询
		if (!ConvertUtil.isEmpty(saleOrgId) && saleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					os += "'" + saleOrgId[i] + "'";
				else
					os += "'" + saleOrgId[i] + "'" + ",";
			}
			sql.append(" and so.id in (" + os + ")");
		}
        //2020-11-5 增加多SBU查询
        if (!ConvertUtil.isEmpty(sbuId) && sbuId.length > 0) {
            String os = "";
            for (int i = 0; i < sbuId.length; i++) {
                if (i == sbuId.length - 1)
                    os += "'" + sbuId[i] + "'";
                else
                    os += "'" + sbuId[i] + "'" + ",";
            }
            sql.append(" and sb.id in (" + os + ")");
        }
		if (storeMemberName != null) {
			sql.append(" and xsm.name = ?");
			list.add(storeMemberName);
		}

		if (warehouseId != null) {
			sql.append(" and i.warehouse = ?");
			list.add(warehouseId);
		}
		if (isSuit != null) {
			if (isSuit) {
				sql.append(" and i.bom_flag = 1");
			}else {
				sql.append(" and i.bom_flag <> 1");
			}
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		//获取用户
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}

		sql.append(" group by i.id order by o.check_date desc");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalsql = "select count(1) from ( " + sql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
	  
		return page;
	}
	

	/**
	 * 查询订单明细分页数据
	 */
	public Page<Map<String, Object>> findItemPageCustom(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, String model, Boolean isToShip,
			Integer[] orderStatus, Boolean isSuit, Long supplierId,
			Boolean isPurchase, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select i.*, o.id order_id, o.sn order_sn,o.payment_type,o.expire, o.out_trade_no out_no,o.consignee,o.mobile,o.phone, o.address,o.check_date, ");
		sql.append("s.id store_id, s.name store_name, a.id area_id, a.full_name area_full_name, w.id warehouse_id, w.name warehouse_name,p.volume,");
		sql.append("so.name sale_org_name, csm.name check_store_member_name,og.name organization_name, og.id organization_id,");
		sql.append("ifnull(sum(si.quantity),0) ship_quantity,ifnull(sum(si.box_quantity),0) ship_box_quantity,");
		sql.append("ifnull(sum(si.branch_quantity),0) ship_branch_quantity,p.per_box,p.per_branch,p.branch_per_box");
		sql.append(" from xx_order_item i");
		sql.append(" left join xx_order o on i.orders = o.id");
		sql.append(" left join xx_store s on o.stores = s.id");
		sql.append(" left join xx_area a on o.area = a.id");
		sql.append(" left join xx_warehouse w on w.id = o.warehouse");
		sql.append(" left join xx_organization og on og.id = o.organization");
		sql.append(" left join xx_product p on p.id = i.product");
		sql.append(" left join xx_sale_org so on o.sale_org=so.id");
		sql.append(" left join xx_store_member csm on csm.id = o.check_store_member");
		sql.append(" left join xx_shipping_item si on si.order_item=i.id");
		sql.append(" left join xx_shipping sh on sh.id=si.shipping and sh.status<>2");
		sql.append(" where i.is_purchase is null and o.order_type <> 4");
		sql.append(" and o.order_category =4 ");
		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (supplierId != null) {
			sql.append(" and st.id = ?");
			list.add(supplierId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and i.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and i.full_name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and i.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(model)) {
			sql.append(" and i.model like ?");
			list.add("%" + model + "%");
		}
		if (isToShip != null && isToShip) {
			sql.append(" and i.quantity > i.ship_plan_quantity");
		}
		if (storeId != null) {
			sql.append(" and o.stores = ?");
			list.add(storeId);
		}
		if (warehouseId != null) {
			sql.append(" and i.warehouse = ?");
			list.add(warehouseId);
		}
		if (isSuit != null) {
			if (isSuit) {
				sql.append(" and i.bom_flag = 1");
			}
			else {
				sql.append(" and i.bom_flag <> 1");
			}
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		else {

			sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());

//			Long saleOrgId = null;
//			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
//			if (storeMemberSaleOrg != null) {
//				saleOrgId = storeMemberSaleOrg.getSaleOrg().getId();
//			}
//			sql.append(" and (so.id="
//					+ saleOrgId
//					+ " or so.tree_path like '%,"
//					+ saleOrgId
//					+ ",%')");
		}
		sql.append(" group by i.id order by o.check_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);

		return page;
	}

	/**
	 * 查询订单明细分页数据 (区分订单类型)
	 */
	public Page<Map<String, Object>> findItemPage(String orderSn,
			String outTradeNo, Long storeId, Long warehouseId, String sn,
			String name, String vonderCode, String model, Boolean isToShip,
			Integer[] orderStatus, Boolean isSuit, Long supplierId,
			Boolean isParts, Pageable pageable, Integer[] orderCategory) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select i.*, o.id order_id, o.sn order_sn, o.out_trade_no out_no,o.consignee,o.mobile,o.phone, o.address,o.check_date, ");
		sql.append(" o.contract_sn,o.contract_name,contract_member,o.payment_method_name,");
		sql.append("s.id store_id, s.name store_name, a.id area_id, a.full_name area_full_name, w.id warehouse_id, w.name warehouse_name,p.volume,");
		sql.append("so.name sale_org_name, csm.name check_store_member_name,og.name organization_name, og.id organization_id,");
		sql.append("ifnull(sum(si.quantity),0) ship_quantity,ifnull(sum(si.box_quantity),0) ship_box_quantity,");
		sql.append("ifnull(sum(si.branch_quantity),0) ship_branch_quantity,p.per_box,p.per_branch,p.branch_per_box");
		sql.append(" from xx_order_item i");
		sql.append(" left join xx_order o on i.orders = o.id");
		sql.append(" left join xx_store s on o.stores = s.id");
		sql.append(" left join xx_area a on o.area = a.id");
		sql.append(" left join xx_warehouse w on w.id = o.warehouse");
		sql.append(" left join xx_organization og on og.id = o.organization");
		sql.append(" left join xx_product p on p.id = i.product");
		sql.append(" left join xx_sale_org so on o.sale_org=so.id");
		sql.append(" left join xx_store_member csm on csm.id = o.check_store_member");
		sql.append(" left join xx_shipping_item si on si.order_item=i.id");
		sql.append(" left join xx_shipping sh on sh.id=si.shipping and sh.status<>2");
		sql.append(" where i.is_purchase is null and o.order_type <> 4");

		if (orderCategory != null && orderCategory.length > 0) {
			String os = "";
			for (int i = 0; i < orderCategory.length; i++) {
				if (i == orderCategory.length - 1)
					os += orderCategory[i];
				else
					os += orderCategory[i] + ",";
			}
			sql.append(" and o.order_category in (" + os + ")");
		}
		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (supplierId != null) {
			sql.append(" and st.id = ?");
			list.add(supplierId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and i.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and i.full_name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and i.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(model)) {
			sql.append(" and i.model like ?");
			list.add("%" + model + "%");
		}
		if (isToShip != null && isToShip) {
			sql.append(" and i.quantity > i.ship_plan_quantity");
		}
		if (storeId != null) {
			sql.append(" and o.stores = ?");
			list.add(storeId);
		}
		if (warehouseId != null) {
			sql.append(" and i.warehouse = ?");
			list.add(warehouseId);
		}
		if (isSuit != null) {
			if (isSuit) {
				sql.append(" and i.bom_flag = 1");
			}
			else {
				sql.append(" and i.bom_flag <> 1");
			}
		}
		if (isParts != null && isParts) {
			sql.append(" and i.bom_flag = 2");
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		else {

			sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());

//			Long saleOrgId = null;
//			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
//			if (storeMemberSaleOrg != null) {
//				saleOrgId = storeMemberSaleOrg.getSaleOrg().getId();
//			}
//			sql.append(" and (so.id="
//					+ saleOrgId
//					+ " or so.tree_path like '%,"
//					+ saleOrgId
//					+ ",%')");
		}
		sql.append(" group by i.id order by o.check_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);

		return page;
	}

	/**
	 * 查询订单明细分页数据
	 */
	public Integer count(String orderSn, String outTradeNo,
			Integer[] orderStatus, Long sbuId, Integer[] shippingStatus,
			Long warehouseId, Long[] storeIds, String consignee, String phone,
			String address, Long deliveryCorpId, Long[] productId,
			Integer[] paymentStatus, Integer[] flag, Integer orderType,
			String firstTime, String lastTime, Integer[] confirmStatus,
			String store_member_name, Long saleOrgId, Long organizationId,
			Pageable pageable, Integer page, Integer size,
			String moistureContent,String colourNumber,String batch,
			Long[] warehouseIds,Long[] organizationIds) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();

		StringBuilder sql = new StringBuilder();
		sql.append(" select count(i.id) from xx_order_item i");
		sql.append(" left join xx_order o on i.orders = o.id");
		sql.append(" left join xx_sale_org so on o.sale_org = so.id");
		sql.append(" left join xx_store s on o.stores = s.id");
		sql.append(" left join xx_area a on o.area = a.id");
		sql.append(" left join xx_warehouse w on w.id = i.warehouse");
		sql.append(" left join xx_warehouse wo on wo.id = o.warehouse");
		sql.append(" left join xx_product p on p.id = i.product");
		sql.append(" left join xx_delivery_corp dc on dc.id = o.delivery");
		sql.append(" left join xx_store_member sm on o.store_member = sm.id");
		sql.append(" left join xx_sbu sb on o.sbu=sb.id");
		sql.append(" left join xx_organization po on po.id = i.product_organization");
		sql.append(" where 1 = 1");
		//仓库
		if (warehouseIds != null && warehouseIds.length > 0) {
			String os = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					os += warehouseIds[i];
				else
					os += warehouseIds[i] + ",";
			}
			sql.append(" and wo.id in (" + os + ")");
		}
		//经营组织
		if (organizationIds != null && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql.append(" and po.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(moistureContent) ) {
			sql.append(" and i.moisture_content like ? ");
			list.add("%" + moistureContent.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(colourNumber) ) {
			sql.append(" and i.colour_number like ? ");
			list.add("%" + colourNumber.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(batch) ) {
			sql.append(" and i.batch like ? ");
			list.add("%" + batch.trim() + "%");
		}
		if (companyInfoId != null) {
			sql.append(" and i.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (orderSn != null) {
			sql.append(" and o.sn = ?");
			list.add(orderSn);
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (outTradeNo != null) {
			sql.append(" and o.out_trade_no = ?");
			list.add(outTradeNo);
		}
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and o.sale_org = ?");
			list.add(saleOrgId);
		}
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" and po.id = ?");
			list.add(organizationId);
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		if (shippingStatus != null && shippingStatus.length > 0) {
			String ss = "";
			for (int i = 0; i < shippingStatus.length; i++) {
				if (i == shippingStatus.length - 1)
					ss += shippingStatus[i];
				else
					ss += shippingStatus[i] + ",";
			}
			sql.append(" and o.shipping_status in (" + ss + ")");
		}
		if (warehouseId != null) {
			sql.append(" and i.warehouse = ?");
			list.add(warehouseId);
		}
		if (storeIds != null && storeIds.length > 0) {
			String s = "";
			for (int i = 0; i < storeIds.length; i++) {
				if (i == storeIds.length - 1)
					s += storeIds[i];
				else
					s += storeIds[i] + ",";
			}
			sql.append(" and  o.stores in (" + s + ")");
		}
		if (!ConvertUtil.isEmpty(consignee)) {
			sql.append(" and o.consignee like ?");
			list.add("%" + consignee + "%");
		}
		if (!ConvertUtil.isEmpty(phone)) {
			sql.append(" and o.phone like ?");
			list.add("%" + phone + "%");
		}
		if (!ConvertUtil.isEmpty(store_member_name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + store_member_name.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(address)) {
			sql.append(" and o.address like ?");
			list.add("%" + address + "%");
		}
		if (deliveryCorpId != null) {
			sql.append(" and o.delivery = ?");
			list.add(deliveryCorpId);
		}
		if (productId != null && productId.length > 0) {
			String ss = "";
			for (int i = 0; i < productId.length; i++) {
				if (i == productId.length - 1)
					ss += productId[i];
				else
					ss += productId[i] + ",";
			}
			sql.append(" and i.product in (" + ss + ")");
		}
		if (paymentStatus != null && paymentStatus.length > 0) {
			String ps = "";
			for (int i = 0; i < paymentStatus.length; i++) {
				if (i == paymentStatus.length - 1)
					ps += paymentStatus[i];
				else
					ps += paymentStatus[i] + ",";
			}
			sql.append(" and o.payment_status in (" + ps + ")");
		}
		if (flag != null && flag.length > 0) {
			String fs = "";
			for (int i = 0; i < flag.length; i++) {
				if (i == flag.length - 1)
					fs += flag[i];
				else
					fs += flag[i] + ",";
			}
			sql.append(" and o.flag in (" + fs + ")");
		}
		if (orderType != null) {
			sql.append(" and o.order_type=?");
			list.add(orderType);
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and o.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and o.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}

		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		if (confirmStatus != null && confirmStatus.length > 0) {
			String os = "";
			for (int i = 0; i < confirmStatus.length; i++) {
				if (i == confirmStatus.length - 1)
					os += confirmStatus[i];
				else
					os += confirmStatus[i] + ",";
			}
			sql.append(" and o.confirm_status in (" + os + ")");
		}

		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		sql.append(" order by i.id desc");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}

	/**
	 * 导出查询
	 */
	public List<Map<String, Object>> findItemList(String orderSn,
			String outTradeNo, Integer[] orderStatus, Long sbuId,
			Integer[] shippingStatus, Long warehouseId, Long[] storeIds,
			String consignee, String phone, String address,
			Long deliveryCorpId, Long[] productId, Integer[] paymentStatus,
			Integer[] flag, Integer orderType, String firstTime,
			String lastTime, Integer[] confirmStatus, Long[] ids,
			String store_member_name, Long saleOrgId, Long organizationId,
			Integer page, Integer size,String moistureContent,String colourNumber,
			String batch,Long[] warehouseIds,Long[] organizationIds) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append(" select i.*, o.id order_id, o.sn order_sn, o.out_trade_no out_no,o.consignee,o.mobile,o.phone,sm.name store_member_name,");
		sql.append(" o.address,o.order_date,dc.name delivery_corp_name,o.memo order_memo,s.id store_id, s.name store_name,s.alias store_alias,");
		sql.append(" s.out_trade_no store_sn,s.balance,w.name warehouse_name, a.id area_id, a.full_name area_full_name, w.name warehouse_name,");
		sql.append(" wo.name order_warehouse_name,o.order_status,o.payment_status,o.shipping_status,o.create_date order_create_date,");
		sql.append(" o.check_date order_check_date,o.order_date,csm.name check_store_member_name,so.name sale_org_name,p.spec,");
		sql.append(" pc.name product_category_name,pa.type paType,o.shipping_method_name shippingMethodName,o.invoice_title invoiceTitle,"
				+ "  o.check_date,sd.value sdValue,ssd.value ssValue,p.vonder_code vonderCode,e.name engineeringName,sb.name sbu_name,"
				+ "  o.order_ip,o.order_device,order_equipment,sdt.value levelName,tT.value transportTypeName,po.id product_organization_id, ");
		sql.append(" po.name product_organization_name,cn.id colour_number_id,cn.value colour_number_name,mc.id moisture_content_id, ");
		sql.append(" mc.value moisture_content_name ");
		sql.append(" from xx_order_item i");
		sql.append(" left join xx_order o on i.orders = o.id");
		sql.append(" left join xx_store s on o.stores = s.id");
		sql.append(" left join xx_system_dict sdt on sdt.id = i.product_level");
		sql.append(" left join xx_area a on o.area = a.id");
		sql.append(" left join xx_system_dict sd on o.business_type = sd.id");
		sql.append(" left join xx_warehouse w on w.id = i.warehouse");
		sql.append(" left join xx_warehouse wo on wo.id = o.warehouse");
		sql.append(" left join xx_product p on p.id = i.product");
		sql.append(" left join xx_product_category pc on p.product_category = pc.id");
		sql.append(" left join xx_price_apl_item pai on i.price_apply_item = pai.id");
		sql.append(" left join xx_price_apl pa on pa.id = pai.price_apply");
		sql.append(" left join xx_delivery_corp dc on dc.id = o.delivery");
		sql.append(" left join xx_engineering e on pa.engineering = e.id");
		sql.append(" left join xx_store_member sm on sm.id = o.store_member");
		sql.append(" left join xx_store_member csm on csm.id = o.check_store_member");
		sql.append(" left join xx_sale_org so on so.id = o.sale_org");
		sql.append(" left join xx_organization po on po.id = i.product_organization");
		sql.append(" left join xx_system_dict ssd on wo.type_system_dict= ssd.id");
		sql.append(" left join xx_sbu sb on o.sbu=sb.id");
		sql.append(" left join xx_system_dict tT on tT.id = o.transport_type");
		sql.append(" left join xx_system_dict cn on cn.id = i.colour_numbers ");
		sql.append(" left join xx_system_dict mc on mc.id = i.moisture_contents ");
		sql.append(" where 1 = 1");
		
		//仓库
		if (warehouseIds != null && warehouseIds.length > 0) {
			String os = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					os += warehouseIds[i];
				else
					os += warehouseIds[i] + ",";
			}
			sql.append(" and wo.id in (" + os + ")");
		}
		//经营组织
		if (organizationIds != null && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql.append(" and po.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(moistureContent) ) {
			sql.append(" and i.moisture_content like ? ");
			list.add("%" + moistureContent.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(colourNumber) ) {
			sql.append(" and i.colour_number like ? ");
			list.add("%" + colourNumber.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(batch) ) {
			sql.append(" and i.batch like ? ");
			list.add("%" + batch.trim() + "%");
		}
		if (companyInfoId != null) {
			sql.append(" and i.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (orderSn != null) {
			sql.append(" and o.sn = ?");
			list.add(orderSn);
		}
		if (outTradeNo != null) {
			sql.append(" and o.out_trade_no = ?");
			list.add(outTradeNo);
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and o.sale_org = ?");
			list.add(saleOrgId);
		}
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" and po.id = ?");
			list.add(organizationId);
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		if (shippingStatus != null && shippingStatus.length > 0) {
			String ss = "";
			for (int i = 0; i < shippingStatus.length; i++) {
				if (i == shippingStatus.length - 1)
					ss += shippingStatus[i];
				else
					ss += shippingStatus[i] + ",";
			}
			sql.append(" and o.shipping_status in (" + ss + ")");
		}
		if (warehouseId != null) {
			sql.append(" and i.warehouse = ?");
			list.add(warehouseId);
		}
		if (storeIds != null && storeIds.length > 0) {
			String s = "";
			for (int i = 0; i < storeIds.length; i++) {
				if (i == storeIds.length - 1)
					s += storeIds[i];
				else
					s += storeIds[i] + ",";
			}
			sql.append(" and  o.stores in (" + s + ")");
		}
		if (!ConvertUtil.isEmpty(consignee)) {
			sql.append(" and o.consignee like ?");
			list.add("%" + consignee + "%");
		}
		if (!ConvertUtil.isEmpty(phone)) {
			sql.append(" and o.phone like ?");
			list.add("%" + phone + "%");
		}
		if (!ConvertUtil.isEmpty(store_member_name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + store_member_name.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(address)) {
			sql.append(" and o.address like ?");
			list.add("%" + address + "%");
		}
		if (deliveryCorpId != null) {
			sql.append(" and o.delivery = ?");
			list.add(deliveryCorpId);
		}
		if (productId != null && productId.length > 0) {
			String ss = "";
			for (int i = 0; i < productId.length; i++) {
				if (i == productId.length - 1)
					ss += productId[i];
				else
					ss += productId[i] + ",";
			}
			sql.append(" and i.product in (" + ss + ")");
		}
		if (paymentStatus != null && paymentStatus.length > 0) {
			String ps = "";
			for (int i = 0; i < paymentStatus.length; i++) {
				if (i == paymentStatus.length - 1)
					ps += paymentStatus[i];
				else
					ps += paymentStatus[i] + ",";
			}
			sql.append(" and o.payment_status in (" + ps + ")");
		}
		if (flag != null && flag.length > 0) {
			String fs = "";
			for (int i = 0; i < flag.length; i++) {
				if (i == flag.length - 1)
					fs += flag[i];
				else
					fs += flag[i] + ",";
			}
			sql.append(" and o.flag in (" + fs + ")");
		}
		if (orderType != null) {
			sql.append(" and o.order_type=?");
			list.add(orderType);
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and o.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and o.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		if (confirmStatus != null && confirmStatus.length > 0) {
			String os = "";
			for (int i = 0; i < confirmStatus.length; i++) {
				if (i == confirmStatus.length - 1)
					os += confirmStatus[i];
				else
					os += confirmStatus[i] + ",";
			}
			sql.append(" and o.confirm_status in (" + os + ")");
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and o.id in (" + inIds + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		sql.append(" order by o.id desc");

		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return maps;
	}

	public Page<Map<String, Object>> findReport(String orderSn,
			Long[] productId, String vonderCode, String mod, Long[] storeId,
			Long storeMemberId, Long supplierId, String firstTime,
			String lastTime, Integer[] shippingStatus,
			BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, String organizationName,
			Long[] saleOrgId, Long[] productCategoryId, String groupby,
			Long businessTypeId, String[] groupBy, Pageable pageable) {
		List<String> groupBylist = groupBy == null ? null
				: Arrays.asList(groupBy);//将string数组转成list,判断分组包含关系

		LogUtils.debug("groupby=" + groupby + "=list=" + groupBylist);

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		StringBuilder nocontationsql = new StringBuilder();
		StringBuilder contationsql = new StringBuilder();
		contationsql.append(" ,sum(oi.shipped_quantity) orderItemshippedQuantity, sum(ifnull(round(oi.price * oi.shipped_quantity,2),0)) amount,sum(ifnull(round(oi.sale_org_price * oi.shipped_quantity,2),0)) saleorgamount,sum(ifnull(round(oi.price * ci.quantity,2),0)) amount_closed ");
		StringBuilder nosumql = new StringBuilder();
		nosumql.append(" ,oi.shipped_quantity orderItemshippedQuantity,ifnull(round(oi.price * oi.shipped_quantity,2),0) amount,ifnull(round(oi.sale_org_price * oi.shipped_quantity,2),0) saleorgamount,ifnull(round(oi.price * ci.quantity,2),0) amount_closed ");

		nocontationsql.append("SELECT DISTINCT oi.id,oi.quantity orderItemQuantity,oi.price,p.vonder_code,p.brand_name,p.model,p.name,sm.name sotre_member_name,o.check_date,csm.name check_store_member_name,so.name sale_org_name,st.name store_name,");
		nocontationsql.append(" pc.name product_category_name,o.sn order_sn,o.memo order_memo,p.description,og.name ogName,rm.name manager,st.out_trade_no,oi.sale_org_price,businessType.value  businessTypeName, date_format(o.create_date,'%Y') year,"
				+ "date_format(o.create_date,'%Y-%m') month,DATE_FORMAT(o.`create_date`,'%Y%u') week ");
		sql.append(" from xx_order_item oi ");
		sql.append(" left JOIN xx_order o on oi.orders=o.id");
		sql.append(" left join xx_order_close_item ci  on  ci.order_item_id = oi.id  ");
		sql.append(" left join  xx_order_close c on  ci.order_close = c.id and c.status = 1");
		sql.append(" left join xx_store_member csm on csm.id = o.check_store_member");
		sql.append(" left join xx_sale_org so on so.id = o.sale_org");
		sql.append(" left JOIN xx_store st on o.stores = st.id");
		sql.append(" LEFT JOIN xx_store_member sm on o.store_member = sm.id");
		sql.append(" LEFT JOIN xx_store_member rm on o.regional_manager = rm.id");
		sql.append(" LEFT JOIN xx_product p on oi.product = p.id");
		sql.append(" LEFT JOIN xx_product_category pc on p.product_category = pc.id");
		sql.append(" LEFT JOIN xx_warehouse xw on oi.warehouse = xw.id");
		sql.append(" left join xx_organization og on og.id = o.organization");
		sql.append(" left join xx_system_dict businessType on o.business_type = businessType.id");
		sql.append(" left join xx_shipping_item xsi  on oi.id=xsi.order_item  left join xx_shipping xs  on  xsi.shipping=xs.id ");
		sql.append(" WHERE  o.order_status in (6,2)");//审核+已完成

		if (companyInfoId != null) {
			sql.append(" and oi.company_info_id = ?");
			list.add(companyInfoId);
		}
		//模糊查询
		if (productId != null && productId.length > 0) {
			String os = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == productId.length - 1)
					os += productId[i];
				else
					os += productId[i] + ",";
			}
			sql.append(" and oi.product in (" + os + ")");
		}

		if (productCategoryId != null && productCategoryId.length > 0) {
			String os = "";
			for (int i = 0; i < productCategoryId.length; i++) {
				if (i == productCategoryId.length - 1)
					os += productCategoryId[i];
				else
					os += productCategoryId[i] + ",";
			}
			sql.append(" and pc.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(organizationName)) {
			sql.append(" and og.name like ?");
			list.add("%" + organizationName + "%");
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (businessTypeId != null) {
			sql.append(" and businessType.id = ?");
			list.add(businessTypeId);
		}
		if (storeId != null && storeId.length > 0) {
			String os = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					os += storeId[i];
				else
					os += storeId[i] + ",";
			}
			sql.append(" and st.id in (" + os + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					os += saleOrgId[i];
				else
					os += saleOrgId[i] + ",";
			}
			sql.append(" and o.sale_org in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(mod)) {
			sql.append(" and p.model like ?");
			list.add("%" + mod + "%");
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and xs.erp_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and xs.erp_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		//分组查询
		if (groupby != null) {//按年月周
			if ("byyear".equals(groupby)) {//按年(产品，品类，客户，机构)
				if (groupBylist != null && groupBylist.size() > 0) {

					nocontationsql.setLength(0);
					nocontationsql.append("  select  DISTINCT date_format(o.create_date,'%Y') year ");
					sql.append(" group by  year  ");
//					nocontationsql.append("SELECT DISTINCT oi.id,oi.quantity orderItemQuantity,oi.price,p.vonder_code,p.brand_name,p.model,p.name,sm.name sotre_member_name,o.check_date,csm.name check_store_member_name,so.name sale_org_name,st.name store_name,");
//					nocontationsql.append(" pc.name product_category_name,o.sn order_sn,o.memo order_memo,p.description,og.name ogName,rm.name manager,st.out_trade_no,oi.sale_org_price,businessType.value  businessTypeName, date_format(o.create_date,'%Y') year,"
//							+ "date_format(o.create_date,'%Y-%m') month,DATE_FORMAT(o.`create_date`,'%Y%u') week ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构 , og.name ogName
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户 , og.name ogName
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}
					//sql.append(" ORDER BY o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					String psql = "";
					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);
					Page<Map<String, Object>> page = getNativeDao().findPageMap(psql,
							objs,
							pageable);

					return page;

				}
				else {//按 年
					sql.append(" group by date_format(o.create_date,'%Y') ");
					sql.append(" ORDER BY sm.name,o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					nocontationsql.setLength(0);
					nocontationsql.append("  select   date_format(o.create_date,'%Y') year ");
					Page<Map<String, Object>> page = getNativeDao().findPageMap(nocontationsql.append(contationsql)
							.append(sql)
							.toString(),
							objs,
							pageable);

					return page;
				}

			}
			else if ("bymonth".equals(groupby)) {//按月(产品，品类，客户，机构)
				LogUtils.debug("进来默认:");
				if (groupBylist != null && groupBylist.size() > 0) {

					nocontationsql.setLength(0);
					nocontationsql.append("  select  DISTINCT date_format(o.create_date,'%Y') year, date_format(o.create_date,'%Y-%m') month  ");
					sql.append(" group by  year,month  ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}
					sql.append(" ORDER BY month desc ");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					String psql = "";
					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);
					Page<Map<String, Object>> page = getNativeDao().findPageMap(psql,
							objs,
							pageable);

					return page;
				}
				else {//按 月
					LogUtils.debug("没有选择");
					sql.append(" group by date_format(o.create_date,'%Y-%m') ");
					sql.append(" ORDER BY month desc , sm.name,o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					nocontationsql.setLength(0);
					nocontationsql.append(" select  date_format(o.create_date,'%Y') year ,"
							+ " date_format(o.create_date,'%Y-%m') month  ");

					Page<Map<String, Object>> page = getNativeDao().findPageMap(nocontationsql.append(contationsql)
							.append(sql)
							.toString(),
							objs,
							pageable);

					return page;
				}
			}
			else if ("byweek".equals(groupby)) {//按周(产品，品类，客户，机构)
				if (groupBylist != null && groupBylist.size() > 0) {
					nocontationsql.setLength(0);
					nocontationsql.append("  select  DISTINCT date_format(o.create_date,'%Y') year, date_format(o.create_date,'%Y-%m') month ,date_format(o.`create_date`,'%Y%u') week ");
					sql.append(" group by  year,month,week  ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}
					sql.append(" ORDER BY month desc ,week desc  ");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					String psql = "";
					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);
					Page<Map<String, Object>> page = getNativeDao().findPageMap(psql,
							objs,
							pageable);

					return page;
				}
				else {//按 周
					sql.append(" group by date_format(o.create_date,'%Y')  ,"
							+ " date_format(o.create_date,'%Y-%m') ,date_format(o.`create_date`,'%Y%u')  ");
					sql.append(" ORDER BY month desc ,week desc  ,sm.name,o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}

					nocontationsql.setLength(0);
					nocontationsql.append(" select  date_format(o.create_date,'%Y') year ,"
							+ " date_format(o.create_date,'%Y-%m') month,date_format(o.`create_date`,'%Y%u') week ");
					Page<Map<String, Object>> page = getNativeDao().findPageMap(nocontationsql.append(contationsql)
							.append(sql)
							.toString(),
							objs,
							pageable);

					return page;
				}
			}
		}
		sql.append(" ORDER BY month desc sm.name,o.check_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(nocontationsql.append(nosumql)
				.append(sql)
				.toString(),
				objs,
				pageable);

		return page;

	}

	public Integer countReport(String orderSn, Long[] productId,
			String vonderCode, String mod, Long[] storeId, Long storeMemberId,
			Long supplierId, String firstTime, String lastTime,
			Integer[] shippingStatus, BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, String organizationName,
			Long[] saleOrgId, Long[] productCategoryId, String groupby,
			Long businessTypeId, String[] groupBy, Pageable pageable,
			Integer page, Integer size) {

		List<String> groupBylist = groupBy == null ? null
				: Arrays.asList(groupBy);//将string数组转成list,判断分组包含关系

		LogUtils.debug("groupby=" + groupby + "=list=" + groupBylist);

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		StringBuilder nocontationsql = new StringBuilder();
		StringBuilder contationsql = new StringBuilder();
		contationsql.append("");
		StringBuilder nosumql = new StringBuilder();
		nosumql.append("");

		nocontationsql.append("  ");
		sql.append(" from xx_order_item oi ");
		sql.append(" left JOIN xx_order o on oi.orders=o.id");
		sql.append(" left join xx_order_close_item ci  on  ci.order_item_id = oi.id  ");
		sql.append(" left join  xx_order_close c on  ci.order_close = c.id and c.status = 1");
		sql.append(" left join xx_store_member csm on csm.id = o.check_store_member");
		sql.append(" left join xx_sale_org so on so.id = o.sale_org");
		sql.append(" left JOIN xx_store st on o.stores = st.id");
		sql.append(" LEFT JOIN xx_store_member sm on o.store_member = sm.id");
		sql.append(" LEFT JOIN xx_store_member rm on o.regional_manager = rm.id");
		sql.append(" LEFT JOIN xx_product p on oi.product = p.id");
		sql.append(" LEFT JOIN xx_product_category pc on p.product_category = pc.id");
		sql.append(" LEFT JOIN xx_warehouse xw on oi.warehouse = xw.id");
		sql.append(" left join xx_organization og on og.id = o.organization");
		sql.append(" left join xx_system_dict businessType on o.business_type = businessType.id");
		sql.append(" left join xx_shipping_item xsi  on oi.id=xsi.order_item  left join xx_shipping xs  on  xsi.shipping=xs.id ");
		sql.append(" WHERE  o.order_status in (6,2)");

		if (companyInfoId != null) {
			sql.append(" and oi.company_info_id = ?");
			list.add(companyInfoId);
		}
		//模糊查询
		if (productId != null && productId.length > 0) {
			String os = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == productId.length - 1)
					os += productId[i];
				else
					os += productId[i] + ",";
			}
			sql.append(" and oi.product in (" + os + ")");
		}

		if (productCategoryId != null && productCategoryId.length > 0) {
			String os = "";
			for (int i = 0; i < productCategoryId.length; i++) {
				if (i == productCategoryId.length - 1)
					os += productCategoryId[i];
				else
					os += productCategoryId[i] + ",";
			}
			sql.append(" and pc.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(organizationName)) {
			sql.append(" and og.name like ?");
			list.add("%" + organizationName + "%");
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (businessTypeId != null) {
			sql.append(" and businessType.id = ?");
			list.add(businessTypeId);
		}
		if (storeId != null && storeId.length > 0) {
			String os = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					os += storeId[i];
				else
					os += storeId[i] + ",";
			}
			sql.append(" and st.id in (" + os + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					os += saleOrgId[i];
				else
					os += saleOrgId[i] + ",";
			}
			sql.append(" and o.sale_org in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(mod)) {
			sql.append(" and p.model like ?");
			list.add("%" + mod + "%");
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and xs.erp_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and xs.erp_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		String psql = "";
		//分组查询
		if (groupby != null) {//按年月周
			if ("byyear".equals(groupby)) {//按年(产品，品类，客户，机构)
				if (groupBylist != null && groupBylist.size() > 0) {

					nocontationsql.setLength(0);
					nocontationsql.append("  select  count(1) ");
					sql.append(" group by  date_format(o.create_date,'%Y')  ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}
					//sql.append(" ORDER BY o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}

					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);

				}
				else {//按 年
					sql.append(" group by date_format(o.create_date,'%Y') ");

					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					nocontationsql.setLength(0);
					nocontationsql.append("   select  count(1) ");
					Integer count = getNativeDao().findInt("select sum(1) from ("
							+ nocontationsql.append(contationsql)
									.append(sql)
									.toString()
							+ ") a ",
							objs);

					return count;
				}

			}
			else if ("bymonth".equals(groupby)) {//按月(产品，品类，客户，机构)
				LogUtils.debug("进来默认:");
				if (groupBylist != null && groupBylist.size() > 0) {

					nocontationsql.setLength(0);
					nocontationsql.append("   select  count(1) ");
					sql.append(" group by   date_format(o.create_date,'%Y'),date_format(o.create_date,'%Y-%m')   ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}

					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);

				}
				else {//按 月
					LogUtils.debug("没有选择");
					sql.append(" group by date_format(o.create_date,'%Y-%m') ");

					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					nocontationsql.setLength(0);
					nocontationsql.append(" select  count(1) ");

					Integer count = getNativeDao().findInt("select sum(1) from ("
							+ nocontationsql.append(contationsql)
									.append(sql)
									.toString()
							+ ") a ",
							objs);

					return count;

				}
			}
			else if ("byweek".equals(groupby)) {//按周(产品，品类，客户，机构)
				if (groupBylist != null && groupBylist.size() > 0) {
					nocontationsql.setLength(0);
					nocontationsql.append("  select  count(1)   ");
					sql.append(" group by  date_format(o.create_date,'%Y') , date_format(o.create_date,'%Y-%m')  ,date_format(o.`create_date`,'%Y%u')   ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}

					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}

					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);

				}
				else {//按 周
					sql.append(" group by date_format(o.create_date,'%Y')  ,"
							+ " date_format(o.create_date,'%Y-%m') ,date_format(o.`create_date`,'%Y%u') ");

					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}

					nocontationsql.setLength(0);
					nocontationsql.append(" select   count(1) ");

					Integer count = getNativeDao().findInt("select sum(1) from ("
							+ nocontationsql.append(contationsql)
									.append(sql)
									.toString()
							+ ") a ",
							objs);

					return count;
				}
			}
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt("select sum(1) from ("
				+ psql
				+ ") a",
				objs);

		return count;
	}

	public List<Map<String, Object>> findReportList(String orderSn,
			Long[] productId, String vonderCode, String mod, Long[] storeId,
			Long storeMemberId, Long supplierId, String firstTime,
			String lastTime, Integer[] shippingStatus,
			BigDecimal minNotShippingQuantity,
			BigDecimal maxNotShippingQuantity, BigDecimal minShippedQuantity,
			BigDecimal maxShippedQuantity, String organizationName,
			Long[] saleOrgId, Long[] productCategoryId, String groupby,
			Long businessTypeId, String[] groupBy, Pageable pageable,
			Integer page, Integer size) {

		List<String> groupBylist = groupBy == null ? null
				: Arrays.asList(groupBy);//将string数组转成list,判断分组包含关系

		LogUtils.debug("groupby=" + groupby + "=list=" + groupBylist);

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		StringBuilder nocontationsql = new StringBuilder();
		StringBuilder contationsql = new StringBuilder();
		contationsql.append(" ,sum(oi.shipped_quantity) orderItemshippedQuantity, sum(ifnull(round(oi.price * oi.shipped_quantity,2),0)) amount,sum(ifnull(round(oi.sale_org_price * oi.shipped_quantity,2),0)) saleorgamount,sum(ifnull(round(oi.price * ci.quantity,2),0)) amount_closed ");
		StringBuilder nosumql = new StringBuilder();
		nosumql.append(" ,oi.shipped_quantity orderItemshippedQuantity,ifnull(round(oi.price * oi.shipped_quantity,2),0) amount,ifnull(round(oi.sale_org_price * oi.shipped_quantity,2),0) saleorgamount,ifnull(round(oi.price * ci.quantity,2),0) amount_closed ");

		nocontationsql.append("SELECT DISTINCT oi.id,oi.quantity orderItemQuantity,oi.price,p.vonder_code,p.brand_name,p.model,p.name,sm.name sotre_member_name,o.check_date,csm.name check_store_member_name,so.name sale_org_name,st.name store_name,");
		nocontationsql.append(" pc.name product_category_name,o.sn order_sn,o.memo order_memo,p.description,og.name ogName,rm.name manager,st.out_trade_no,oi.sale_org_price,businessType.value  businessTypeName, date_format(o.create_date,'%Y') year,"
				+ "date_format(o.create_date,'%Y-%m') month,DATE_FORMAT(o.`create_date`,'%Y%u') week ");
		sql.append(" from xx_order_item oi ");
		sql.append(" left join xx_order o on oi.orders=o.id");
		sql.append(" left join xx_order_close_item ci  on  ci.order_item_id = oi.id  ");
		sql.append(" left join xx_order_close c on ci.order_close = c.id and c.status = 1");
		sql.append(" left join xx_store_member csm on csm.id = o.check_store_member");
		sql.append(" left join xx_sale_org so on so.id = o.sale_org");
		sql.append(" left JOIN xx_store st on o.stores = st.id");
		sql.append(" LEFT JOIN xx_store_member sm on o.store_member = sm.id");
		sql.append(" LEFT JOIN xx_store_member rm on o.regional_manager = rm.id");
		sql.append(" LEFT JOIN xx_product p on oi.product = p.id");
		sql.append(" LEFT JOIN xx_product_category pc on p.product_category = pc.id");
		sql.append(" LEFT JOIN xx_warehouse xw on oi.warehouse = xw.id");
		sql.append(" left join xx_organization og on og.id = o.organization");
		sql.append(" left join xx_system_dict businessType on o.business_type = businessType.id");
		sql.append(" left join xx_shipping_item xsi  on oi.id=xsi.order_item  left join xx_shipping xs  on  xsi.shipping=xs.id ");
		sql.append(" WHERE  o.order_status in (6,2)");

		if (companyInfoId != null) {
			sql.append(" and oi.company_info_id = ?");
			list.add(companyInfoId);
		}
		//模糊查询
		if (productCategoryId != null && productCategoryId.length > 0) {
			String os = "";
			for (int i = 0; i < productCategoryId.length; i++) {
				if (i == productCategoryId.length - 1)
					os += productCategoryId[i];
				else
					os += productCategoryId[i] + ",";
			}
			sql.append(" and pc.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(organizationName)) {
			sql.append(" and og.name like ?");
			list.add("%" + organizationName + "%");
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (businessTypeId != null) {
			sql.append(" and businessType.id = ?");
			list.add(businessTypeId);
		}
		if (storeId != null && storeId.length > 0) {
			String os = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					os += storeId[i];
				else
					os += storeId[i] + ",";
			}
			sql.append(" and st.id in (" + os + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					os += saleOrgId[i];
				else
					os += saleOrgId[i] + ",";
			}
			sql.append(" and o.sale_org in (" + os + ")");
		}
		if (productId != null && productId.length > 0) {
			String os = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == productId.length - 1)
					os += productId[i];
				else
					os += productId[i] + ",";
			}
			sql.append(" and oi.product in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(vonderCode)) {
			sql.append(" and p.vonder_code like ?");
			list.add("%" + vonderCode + "%");
		}
		if (!ConvertUtil.isEmpty(mod)) {
			sql.append(" and p.model like ?");
			list.add("%" + mod + "%");
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and xs.erp_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and xs.erp_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		String psql = "";
		//分组查询
		if (groupby != null) {//按年月周
			if ("byyear".equals(groupby)) {//按年(产品，品类，客户，机构)
				if (groupBylist != null && groupBylist.size() > 0) {

					nocontationsql.setLength(0);
					nocontationsql.append("  select  DISTINCT date_format(o.create_date,'%Y') year ");
					sql.append(" group by  year  ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}
					//sql.append(" ORDER BY o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}

					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);

				}
				else {//按 年
					sql.append(" group by date_format(o.create_date,'%Y') ");
					sql.append(" ORDER BY sm.name,o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					nocontationsql.setLength(0);
					nocontationsql.append("  select   date_format(o.create_date,'%Y') year ");
					psql = nocontationsql.append(contationsql)
							.append(sql)
							.toString();
				}

			}
			else if ("bymonth".equals(groupby)) {//按月(产品，品类，客户，机构)
				LogUtils.debug("进来默认:");
				if (groupBylist != null && groupBylist.size() > 0) {

					nocontationsql.setLength(0);
					nocontationsql.append("  select  DISTINCT date_format(o.create_date,'%Y') year, date_format(o.create_date,'%Y-%m') month  ");
					sql.append(" group by  year,month  ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}
					sql.append(" ORDER BY month desc ");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}

					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);

				}
				else {//按 月
					LogUtils.debug("没有选择");
					sql.append(" group by date_format(o.create_date,'%Y-%m') ");
					sql.append(" ORDER BY month desc , sm.name,o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					nocontationsql.setLength(0);
					nocontationsql.append(" select  date_format(o.create_date,'%Y') year ,"
							+ " date_format(o.create_date,'%Y-%m') month  ");

					psql = nocontationsql.append(contationsql)
							.append(sql)
							.toString();
				}
			}
			else if ("byweek".equals(groupby)) {//按周(产品，品类，客户，机构)
				if (groupBylist != null && groupBylist.size() > 0) {
					nocontationsql.setLength(0);
					nocontationsql.append("  select  DISTINCT date_format(o.create_date,'%Y') year, date_format(o.create_date,'%Y-%m') month ,date_format(o.`create_date`,'%Y%u') week ");
					sql.append(" group by  year,month,week  ");

					if (groupBylist.contains("byproductCategory")) {//按 年 +品类
						nocontationsql.append(",pc.name product_category_name");
						sql.append(" , p.product_category ");
					}
					if (groupBylist.contains("byproduct")) {//按 年 + 产品 
						nocontationsql.append(",pc.name product_category_name,  p.vonder_code,p.brand_name,p.model,p.name,p.description ");
						sql.append(" ,oi.product ");
					}
					// 年+机构
					if (groupBylist.contains("bysaleorg")) {//按机构
						nocontationsql.append(",  so.name sale_org_name ");
						sql.append(" ,o.sale_org");

					}
					if (groupBylist.contains("bystore")) {//按年+客户
						nocontationsql.append(",  so.name sale_org_name,  st.out_trade_no, st.name store_name, businessType.value businessTypeName,  sm.name sotre_member_name,  csm.name check_store_member_name,  rm.name manager ");
						sql.append(",o.stores ");

					}
					sql.append(" ORDER BY month desc ,week desc  ");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}

					if (groupBylist.contains("byproductCategory")
							|| groupBylist.contains("bystore")
							|| groupBylist.contains("bysaleorg")) {
						psql = nocontationsql.append(contationsql)
								.append(sql)
								.toString();
					}
					else {
						psql = nocontationsql.append(nosumql)
								.append(sql)
								.toString();
					}
					LogUtils.debug("psql=" + psql);

				}
				else {//按 周
					sql.append(" group by DATE_FORMAT(o.`create_date`,'%Y%u') ");
					sql.append(" ORDER BY month desc ,week desc  ,sm.name,o.check_date desc");
					Object[] objs = new Object[list.size()];
					for (int i = 0; i < list.size(); i++) {
						objs[i] = list.get(i);
					}
					nocontationsql.setLength(0);
					nocontationsql.append(" select  date_format(o.create_date,'%Y') year ,"
							+ " date_format(o.create_date,'%Y-%m') month,date_format(o.`create_date`,'%Y%u') week ");
					psql = nocontationsql.append(contationsql)
							.append(sql)
							.toString();
				}
			}
		}
		if (page != null && size != null) {
			psql += (" limit " + (size * (page - 1)) + "," + size);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(psql,
				objs,
				0);

		return maps;
	}

	public OrderItem findItemByVonderCode(String orderSn, String vonderCode) {
		String sql = "select i.* from xx_order_item i"
				+ " left join xx_order o on i.orders=o.id"
				+ " where o.sn = ? and i.vonder_code = ?";
		OrderItem orderItem = getNativeDao().findSingleManaged(sql,
				new Object[] { orderSn, vonderCode },
				OrderItem.class);
		return orderItem;
	}

	/**
	 * 查找订单分页数据
	 * 
	 * @param orderSn
	 * @param outTradeNo
	 * @param orderStatus
	 * @param shippingStatus
	 * @param warehouseId
	 * @param storeIds
	 * @param deliveryCorpId
	 * @param firstTime
	 * @param lastTime
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> newFindPage(String orderSn,
			String outTradeNo, Integer[] orderStatus,Integer[] wfState, Long sbuId,
			Integer[] shippingStatus, Long warehouseId, Long[] storeIds,
			String consignee, String phone, String address,
			Long deliveryCorpId, Long[] productIds, Integer[] paymentStatus,
			Integer[] flag, Integer orderType, String firstTime,
			String lastTime, Integer[] confirmStatus, Integer isReturn,
			String store_member_name, Long[] saleOrgId, Long organizationId,
			Long businessTypeId,Integer isPc,Pageable pageable,
			String moistureContent,String colourNumber,String batch,
			Long[] warehouseIds,Long[] organizationIds,String aftersaleSn) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder pSql = new StringBuilder();
		pSql.append("select oo.*,oc.amount_closed,dc.name delivery_corp_name,a.full_name area_name, (sum(ROUND(i.price * i.quantity,2)) + oo.offset_amount + oo.freight - oo.coupon_discount - oo.promotion_discount) amount from ");

		StringBuilder sql = new StringBuilder();
		sql.append(" (select o.four_aftersale_sn,o.id,o.create_date,o.order_date,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.payment_status,o.ex_status,o.ticket_holder,"
				+ "o.order_type,o.change_memo,o.buyer_remark,o.seller_remark,o.delivery_memo,o.memo,o.consignee,o.phone,o.address,o.delivery,o.area,"
				+ "o.offset_amount,o.freight,o.coupon_discount,o.promotion_discount,o.flag,o.wf_id,o.wf_state,o.confirm_status,w.name warehouse_name,"
				+ "s.name store_name,s.alias store_alias,s.out_trade_no store_code,s.balance store_balance,s.budget store_budget,so.name sale_org_name,so.id sale_org_id, csm.name check_store_member_name,o.check_date,"
				+ "so.budget sale_org_budget,sd.value system_value,sm.name store_member_name,o.stores,rsm.name regional_manager_name,xs.name distributor_name,o.zip_code,ii.shipped_quantity,sb.name sbu_name,ii.price_apply_sn "
				+ " from xx_order o"
				+ " left join xx_store s on s.id = o.stores"
				+ " left join xx_sale_org so on so.id = o.sale_org"
				+ " left join xx_warehouse w on w.id = o.warehouse"
				+ " left join xx_order_item ii on o.id=ii.orders"
				+ " left join xx_product p on ii.product=p.id"
				+ " left join xx_product_category pc on p.product_category=pc.id"
				+ " left join xx_system_dict sd on o.freight_charge_type=sd.id"
				+ " left join xx_store_member sm on o.store_member = sm.id"
				+ " left join xx_store_member rsm on o.regional_manager = rsm.id"
				+ " left join xx_store_member csm on csm.id = o.check_store_member"
				+ " left join xx_store xs on xs.id = o.distributor"
				+ " left join xx_sbu sb on o.sbu = sb.id "
 			    + " left join xx_organization po on po.id = ii.product_organization "
				+ " where 1=1");

		/**2018年9月23日 02:18:10 lj add  类型3为项目订单 查询客户不应该查到项目订单**/
		sql.append(" and (o.order_category<>3 or o.order_category is null)");
		if (!ConvertUtil.isEmpty(aftersaleSn)) {
			sql.append(" and o.four_aftersale_sn = ?");
			list.add(aftersaleSn);
		}

		//仓库
		if (warehouseIds != null && warehouseIds.length > 0) {
			String os = "";
			for (int i = 0; i < warehouseIds.length; i++) {
				if (i == warehouseIds.length - 1)
					os += warehouseIds[i];
				else
					os += warehouseIds[i] + ",";
			}
			sql.append(" and w.id in (" + os + ")");
		}
		//经营组织
		if (organizationIds != null && organizationIds.length > 0) {
			String os = "";
			for (int i = 0; i < organizationIds.length; i++) {
				if (i == organizationIds.length - 1)
					os += organizationIds[i];
				else
					os += organizationIds[i] + ",";
			}
			sql.append(" and po.id in (" + os + ")");
		}
		if (!ConvertUtil.isEmpty(moistureContent) ) {
			sql.append(" and ii.moisture_content like ? ");
			list.add("%" + moistureContent.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(colourNumber) ) {
			sql.append(" and ii.colour_number like ? ");
			list.add("%" + colourNumber.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(batch) ) {
			sql.append(" and ii.batch like ? ");
			list.add("%" + batch.trim() + "%");
		}
		if (companyInfoId != null) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(orderSn) ) {
			sql.append(" and o.sn = ?");
			list.add(orderSn.trim());
		}
		if( isPc!= null && isPc==0 && orderSn==null){
			sql.append(" and o.order_status !=3 ");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and o.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(consignee)) {
			sql.append(" and o.consignee like ?");
			list.add("%" + consignee + "%");
		}
		if (!ConvertUtil.isEmpty(phone)) {
			sql.append(" and o.phone like ?");
			list.add("%" + phone + "%");
		}
		if (!ConvertUtil.isEmpty(businessTypeId)) {
			sql.append(" and o.business_type = ?");
			list.add(businessTypeId);
		}
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (!ConvertUtil.isEmpty(store_member_name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + store_member_name.trim() + "%");
		}
		if (!ConvertUtil.isEmpty(address)) {
			sql.append(" and o.address like ?");
			list.add("%" + address + "%");
		}
        if (!ConvertUtil.isEmpty(saleOrgId) && saleOrgId.length > 0) {
            String os = "";
            for (int i = 0; i < saleOrgId.length; i++) {
                if (i == saleOrgId.length - 1)
                    os += saleOrgId[i];
                else
                    os += saleOrgId[i] + ",";
            }
            sql.append(" and o.sale_org in (" + os + ")");
        }
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" and po.id = ?");
			list.add(organizationId);
		}
		
		if (wfState != null && wfState.length > 0) {
			String os = "";
			for (int i = 0; i < wfState.length; i++) {
				if (i == wfState.length - 1)
					os += wfState[i];
				else
					os += wfState[i] + ",";
			}
			sql.append(" and o.wf_state in (" + os + ")");
		}
		if (orderStatus != null && orderStatus.length > 0) {
			String os = "";
			for (int i = 0; i < orderStatus.length; i++) {
				if (i == orderStatus.length - 1)
					os += orderStatus[i];
				else
					os += orderStatus[i] + ",";
			}
			sql.append(" and o.order_status in (" + os + ")");
		}
		if (shippingStatus != null && shippingStatus.length > 0) {
			String ss = "";
			for (int i = 0; i < shippingStatus.length; i++) {
				if (i == shippingStatus.length - 1)
					ss += shippingStatus[i];
				else
					ss += shippingStatus[i] + ",";
			}
			sql.append(" and o.shipping_status in (" + ss + ")");
		}
		if (paymentStatus != null && paymentStatus.length > 0) {
			String ps = "";
			for (int i = 0; i < paymentStatus.length; i++) {
				if (i == paymentStatus.length - 1)
					ps += paymentStatus[i];
				else
					ps += paymentStatus[i] + ",";
			}
			sql.append(" and o.payment_status in (" + ps + ")");
		}
		if (flag != null && flag.length > 0) {
			String fs = "";
			for (int i = 0; i < flag.length; i++) {
				if (i == flag.length - 1)
					fs += flag[i];
				else
					fs += flag[i] + ",";
			}
			sql.append(" and o.flag in (" + fs + ")");
		}
		if (storeIds != null && storeIds.length > 0) {
			String s = "";
			for (int i = 0; i < storeIds.length; i++) {
				if (i == storeIds.length - 1)
					s += storeIds[i];
				else
					s += storeIds[i] + ",";
			}
			sql.append("  and o.stores in (" + s + ")");
		}
		if (deliveryCorpId != null) {
			sql.append(" and o.delivery=?");
			list.add(deliveryCorpId);
		}
		if (orderType != null) {
			sql.append(" and o.order_type=?");
			list.add(orderType);
		}
		if (warehouseId != null) {
			sql.append(" and o.id in (select orders from xx_order_item where warehouse = ?)");
			list.add(warehouseId);
		}
		if (productIds != null && productIds.length > 0) {
			String fs = "";
			for (int i = 0; i < productIds.length; i++) {
				if (i == productIds.length - 1)
					fs += productIds[i];
				else
					fs += productIds[i] + ",";
			}
			sql.append(" and ii.product in (" + fs + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and o.order_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and o.order_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		//获取用户
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		if (confirmStatus != null && confirmStatus.length > 0) {
			String os = "";
			for (int i = 0; i < confirmStatus.length; i++) {
				if (i == confirmStatus.length - 1)
					os += confirmStatus[i];
				else
					os += confirmStatus[i] + ",";
			}
			sql.append(" and o.confirm_status in (" + os + ")");
		}
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and po.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and po.id is null");
			}
		}
		
		if (isReturn != null && isReturn == 1) {
			sql.append(" and exit(SELECT 1,oi.shipped_quantity - sum(IFNULL(ri.quantity, 0)) r_quantity"
					+ " from xx_order_item oi left join xx_b2b_returns_item ri on ri.order_item = oi.id"
					+ " where oi.orders = o.id group by oi.id having r_quantity > 0)");
		}
		int pageNumber = pageable.getPageNumber();
		int pageSize = pageable.getPageSize();
		int num = (pageNumber - 1) * pageSize;
		sql.append(" group by o.id order by o.create_date desc limit "
				+ num
				+ ","
				+ (pageSize + 1)
				+ ") oo");

		sql.append(" left join");
		sql.append("  (select b.orders, sum(round(b.price * ci.quantity,2)) amount_closed");
		sql.append("  from  xx_order a,xx_order_item b, xx_order_close_item ci,xx_order_close c");
		sql.append("  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		sql.append("  and a.company_info_id = ?");
		list.add(companyInfoId);
		sql.append(" group by  b.orders ) oc on oc.orders = oo.id");

		pSql.append(sql);
		pSql.append(" left join xx_delivery_corp dc on dc.id = oo.delivery"
				+ " left join xx_area a on a.id = oo.area"
				+ " left join xx_order_item i on i.orders = oo.id"
				+ " group by oo.id"
				+ " order by oo.create_date desc");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
        List<Map<String, Object>> result = getNativeDao().findListMap(pSql.toString(),
				objs,
				0);
		long total = 0;
		if (result.size() == pageSize + 1) {
			total = (pageNumber + 1) * pageSize;
			result.remove(result.size() - 1);
		}else{
			total = ((pageNumber - 1) * pageSize) + result.size();
		}
		return new Page<Map<String, Object>>(result, total, pageable);
	}

	public Map<String, Object> findWarehouse(String warehouse) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT w.*,s.id type_system_dict_id,s.value type_system_dict_value,s.flag type_system_dict_flag, "
				+ "  o.id management_organization_id,o.name management_organization_name "
				+ " FROM xx_warehouse w"
				+ " left join xx_system_dict s on w.type_system_dict=s.id "
				+ " left join xx_organization o on o.id =w.management_organization "
				+ " WHERE 1 = 1 ");
		if (warehouse != null) {
			sql.append(" and w.name like ?");
			list.add("%" + warehouse + "%");
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> result = getNativeDao().findListMap(sql.toString(),
				objs,
				0);
        if (result!=null && result.size() > 0){
            return  result.get(0);
        }else {
            return null;
        }
	}
	
	/**
	 * 获取商品销售数量，按product字段分组
	 * @return
	 */
	public List<Map<String, Object>> getSellTotalGroupByProduct() {
		StringBuilder sql = new StringBuilder();
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT p.name,cast(sum(i.shipped_quantity) as decimal(9,0)) sell_total FROM xx_shipping_item i left join xx_product p on p.id = i.product  WHERE i.company_info_id = ? GROUP BY i.product ORDER BY sell_total desc limit 10");
		list.add(companyInfoId);
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql.toString(),objs, 0);
		return lists;
	}
	
	public BigDecimal findAvailMony(String orderSn,String orderItem){
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT sum(CASE WHEN ifnull( oi.shipped_quantity, 0.0 ) <> 0.0 THEN ifnull( oi.shipped_quantity, 0 ) ELSE ifnull( oi.quantity, 0 ) END ) - sum( ifnull( oc.quantity, 0 ) ) qu  "
		        +"FROM xx_price_apl_item pai LEFT JOIN xx_order_item oi ON pai.id = oi.price_apply_item "
		        +"LEFT JOIN xx_order o ON oi.orders = o.id LEFT JOIN ( SELECT oci.order_item_id, sum( ifnull(oci.quantity,0) ) quantity  FROM xx_order_close_item oci"
		        +" LEFT JOIN xx_order_close oc ON oci.order_close = oc.id  WHERE oc.`status` = 1  GROUP BY oci.order_item_id  ) oc ON oi.id = oc.order_item_id "
		        +" WHERE o.order_status IN ( 2, 6, 7, 5 )  AND oi.company_info_id = "+companyInfoId+"  AND pai.id = "+orderSn);
		if(orderItem!=null){
			sql.append(" AND oi.id <>"+orderItem);
		}
		sql.append("  GROUP BY oi.price_apply_item ");
		return getNativeDao().findBigDecimal(sql.toString(), null);	
	}
	
	public List<Map<String, Object>> findTable(String orderSn,
			String outTradeNo, Integer[] orderStatus, Long sbuId,
			Integer[] shippingStatus, Long warehouseId, Long[] storeIds,
			String consignee, String phone, String address,
			Long deliveryCorpId, Long[] productIds, Integer[] paymentStatus,
			Integer[] flag, Integer orderType, String firstTime,
			String lastTime, Integer[] confirmStatus, Integer isReturn,
			String store_member_name, Long saleOrgId, Long organizationId,
			Pageable pageable,Integer page,Integer size) {

			List<Object> list = new ArrayList<Object>();
			Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

			StringBuilder pSql = new StringBuilder();
			pSql.append("select oo.*,oc.amount_closed,dc.name delivery_corp_name,a.full_name area_name, (sum(i.price * i.quantity) + oo.offset_amount + oo.freight - oo.coupon_discount - oo.promotion_discount) amount from ");

			StringBuilder sql = new StringBuilder();
			sql.append(" (select o.id,o.create_date,o.order_date,o.sn,o.out_trade_no,o.order_status,o.shipping_status,o.payment_status,o.ex_status,o.ticket_holder,"
					+ "o.order_type,o.change_memo,o.buyer_remark,o.seller_remark,o.delivery_memo,o.memo,o.consignee,o.phone,o.address,o.delivery,o.area,"
					+ "o.offset_amount,o.freight,o.coupon_discount,o.promotion_discount,o.flag,o.wf_id,o.wf_state,o.confirm_status,w.name warehouse_name,"
					+ "s.name store_name,s.out_trade_no store_code,s.balance store_balance,s.budget store_budget,so.name sale_org_name,so.id sale_org_id, csm.name check_store_member_name,o.check_date,"
					+ "so.budget sale_org_budget,sd.value system_value,sm.name store_member_name,o.stores,rsm.name regional_manager_name,xs.name distributor_name,o.zip_code,ii.shipped_quantity"
					+ "  from xx_order o"
					+ " left join xx_store s on s.id = o.stores"
					+ " left join xx_sale_org so on so.id = o.sale_org"
					+ " left join xx_warehouse w on w.id = o.warehouse"
					+ " left join xx_order_item ii on o.id=ii.orders"
					+ " left join xx_product p on ii.product=p.id"
					+ " left join xx_product_category pc on p.product_category=pc.id"
					+ " left join xx_system_dict sd on o.freight_charge_type=sd.id"
					+ " left join xx_store_member sm on o.store_member = sm.id"
					+ " left join xx_store_member rsm on o.regional_manager = rsm.id"
					+ " left join xx_store_member csm on csm.id = o.check_store_member"
					+ " left join xx_store xs on xs.id = o.distributor"
					+ " left join xx_sbu sb on sb.id = o.sbu"
					+ " left join xx_organization po on ii.product_organization = po.id"
					+ " where 1=1");

			/**2018年9月23日 02:18:10 lj add  类型3为项目订单 查询客户不应该查到项目订单**/
			sql.append(" and (o.order_category<>3 or o.order_category is null)");

			if (companyInfoId != null) {
				sql.append(" and o.company_info_id = ?");
				list.add(companyInfoId);
			}
			if (!ConvertUtil.isEmpty(orderSn)) {
				sql.append(" and o.sn = ?");
				list.add(orderSn.trim());
			}
			if (!ConvertUtil.isEmpty(sbuId)) {
				sql.append(" and sb.id = ?");
				list.add(sbuId);
			}
			if (!ConvertUtil.isEmpty(outTradeNo)) {
				sql.append(" and o.out_trade_no like ?");
				list.add("%" + outTradeNo + "%");
			}
			if (!ConvertUtil.isEmpty(consignee)) {
				sql.append(" and o.consignee like ?");
				list.add("%" + consignee + "%");
			}
			if (!ConvertUtil.isEmpty(phone)) {
				sql.append(" and o.phone like ?");
				list.add("%" + phone + "%");
			}
			if (!ConvertUtil.isEmpty(store_member_name)) {
				sql.append(" and sm.name like ?");
				list.add("%" + store_member_name.trim() + "%");
			}
			if (!ConvertUtil.isEmpty(address)) {
				sql.append(" and o.address like ?");
				list.add("%" + address + "%");
			}
			if (!ConvertUtil.isEmpty(saleOrgId)) {
				sql.append(" and o.sale_org = ?");
				list.add(saleOrgId);
			}
			if (!ConvertUtil.isEmpty(organizationId)) {
				sql.append(" and o.organization = ?");
				list.add(organizationId);
			}
			if (orderStatus != null && orderStatus.length > 0) {
				String os = "";
				for (int i = 0; i < orderStatus.length; i++) {
					if (i == orderStatus.length - 1)
						os += orderStatus[i];
					else
						os += orderStatus[i] + ",";
				}
				sql.append(" and o.order_status in (" + os + ")");
			}
			if (shippingStatus != null && shippingStatus.length > 0) {
				String ss = "";
				for (int i = 0; i < shippingStatus.length; i++) {
					if (i == shippingStatus.length - 1)
						ss += shippingStatus[i];
					else
						ss += shippingStatus[i] + ",";
				}
				sql.append(" and o.shipping_status in (" + ss + ")");
			}
			if (paymentStatus != null && paymentStatus.length > 0) {
				String ps = "";
				for (int i = 0; i < paymentStatus.length; i++) {
					if (i == paymentStatus.length - 1)
						ps += paymentStatus[i];
					else
						ps += paymentStatus[i] + ",";
				}
				sql.append(" and o.payment_status in (" + ps + ")");
			}
			if (flag != null && flag.length > 0) {
				String fs = "";
				for (int i = 0; i < flag.length; i++) {
					if (i == flag.length - 1)
						fs += flag[i];
					else
						fs += flag[i] + ",";
				}
				sql.append(" and o.flag in (" + fs + ")");
			}
			if (storeIds != null && storeIds.length > 0) {
				String s = "";
				for (int i = 0; i < storeIds.length; i++) {
					if (i == storeIds.length - 1)
						s += storeIds[i];
					else
						s += storeIds[i] + ",";
				}
				sql.append("  and o.stores in (" + s + ")");
			}
			if (deliveryCorpId != null) {
				sql.append(" and o.delivery=?");
				list.add(deliveryCorpId);
			}
			if (orderType != null) {
				sql.append(" and o.order_type=?");
				list.add(orderType);
			}
			if (warehouseId != null) {
				sql.append(" and o.id in (select orders from xx_order_item where warehouse = ?)");
				list.add(warehouseId);
			}
			if (productIds != null && productIds.length > 0) {
				String fs = "";
				for (int i = 0; i < productIds.length; i++) {
					if (i == productIds.length - 1)
						fs += productIds[i];
					else
						fs += productIds[i] + ",";
				}
				sql.append(" and ii.product in (" + fs + ")");
			}
			if (!ConvertUtil.isEmpty(firstTime)) {
				sql.append(" and o.order_date >= ?");
				list.add(DateUtil.convert(firstTime + " 00:00:00"));

			}
			if (!ConvertUtil.isEmpty(lastTime)) {
				sql.append(" and o.order_date < ?");
				list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
			}
			StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
			if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
				String storeAuth = storeMemberService.storeAuth();
				if (storeAuth != null) {
					sql.append(" and o.stores in (" + storeAuth + ")");
				}
			}
			

			if (confirmStatus != null && confirmStatus.length > 0) {
				String os = "";
				for (int i = 0; i < confirmStatus.length; i++) {
					if (i == confirmStatus.length - 1)
						os += confirmStatus[i];
					else
						os += confirmStatus[i] + ",";
				}
				sql.append(" and o.confirm_status in (" + os + ")");
			}

			if (isReturn != null && isReturn == 1) {
				sql.append(" and exit(SELECT 1,oi.shipped_quantity - sum(IFNULL(ri.quantity, 0)) r_quantity"
						+ " from xx_order_item oi left join xx_b2b_returns_item ri on ri.order_item = oi.id"
						+ " where oi.orders = o.id group by oi.id having r_quantity > 0)");
			}
			
			//用户机构
			String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
				if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
					sql.append(" and so.id in (" + saleOrgIds + ")");
				}else{
					sql.append(" and so.id is null");
				}
			}
			
			//用户Sbu
			String sbuIds = roleJurisdictionUtil.getSbuIds();
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
				if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
					sql.append(" and sb.id in (" + sbuIds + ")");
				}else{
					sql.append(" and sb.id is null");
				}
			}
			
			//用户经营组织
			String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
				if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
					sql.append(" and po.id in (" + organizationIdS + ")");
				}else{
					sql.append(" and po.id is null");
				}
			}
			
			sql.append(" group by o.id order by o.create_date desc) oo");

			sql.append(" left join");
			sql.append("  (select b.orders, sum(round(b.price * ci.quantity,2)) amount_closed");
			sql.append("  from  xx_order a,xx_order_item b, xx_order_close_item ci,xx_order_close c");
			sql.append("  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
			sql.append("  and a.company_info_id = ?");
			list.add(companyInfoId);
			sql.append(" group by  b.orders ) oc on oc.orders = oo.id");

			pSql.append(sql);
			pSql.append(" left join xx_delivery_corp dc on dc.id = oo.delivery"
					+ " left join xx_area a on a.id = oo.area"
					+ " left join xx_order_item i on i.orders = oo.id"
					+ " group by oo.id"
					+ " order by oo.create_date desc");
			Object[] objs = new Object[list.size()];
			for (int i = 0; i < list.size(); i++) {
				objs[i] = list.get(i);
			}

			List<Map<String, Object>> result = getNativeDao().findListMap(pSql.toString(),
					objs,
					0);

			return result;
	}

	/**
	 * 根据顶顶那ID获取未处理发货单
	 * @return
	 */
	public List<Map<String, Object>> findShippingsById(Long id) {
		StringBuilder sql = new StringBuilder();
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("select s.sn from xx_shipping s left join xx_shipping_item si on si.shipping=s.id  where si.orders=? and s.status=0 and s.company_info_id=9");
		list.add(id);
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql.toString(),objs, 0);
		return lists;
	}
	
	/**
	 * 根据订单ID,查询未审核的出库单信息,
	 * 返回信息 qaz 出库单ID、e_sn 出库单编号、shipping_sn 发货单编号、amStatusName 出库单状态 
	 * @param id
	 * @return
	 */
	public List<Map<String, Object>> findAmShippingById(Long id) {
		StringBuilder sql = new StringBuilder();
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT "
				+ "	s.id qaz,"
				+ "	s.sn e_sn,"
				+ "	sd.remark,"
				+ "	xs.sn shipping_sn,"
				+ "	CASE s.STATUS "
				+ "WHEN 0 THEN "
				+ "	'未审核' "
				+ "WHEN 1 THEN "
				+ "	'已审核' "
				+ "WHEN 2 THEN "
				+ "	'作废' "
				+ "WHEN 3 THEN "
				+ "	'部分发货' "
				+ "WHEN 4 THEN "
				+ "	'完全发货' "
				+ "WHEN 5 THEN "
				+ "	'审核中' "
				+ "END amStatusName "
				+ "FROM xx_am_shipping s "
				+ "LEFT JOIN xx_warehouse w ON s.warehouse = w.id "
				+ "LEFT JOIN xx_store st ON s.stores = st.id "
				+ "LEFT JOIN xx_sale_org so ON s.sale_org = so.id "
				+ "LEFT JOIN xx_store_member sm ON s.store_member = sm.id "
				+ "LEFT JOIN xx_sbu sb ON s.sbu = sb.id "
				+ "LEFT JOIN xx_am_shipping_item si ON si.am_shipping = s.id "
				+ "LEFT JOIN xx_shipping_item oi ON oi.id = si.shipping_item "
				+ "LEFT JOIN xx_shipping xs ON xs.id = si.shipping "
				+ "LEFT JOIN xx_area a ON a.id = s.area "
				+ "LEFT JOIN xx_organization po ON po.id = si.product_organization "
				+ "LEFT JOIN xx_b2b_returns br ON br.id = si.b2b_returns "
				+ "LEFT JOIN xx_b2b_returns_item bri ON bri.id = si.b2b_returns_item "
				+ "LEFT JOIN xx_system_dict sd ON sd.id = s.bill_type "
				+ "WHERE 1 = 1 "
				+ "AND s.company_info_id = ? "
				+ "AND s.`status` = 0 "
				+ "AND sd.remark = '出仓' "
				+ "AND xs.sn IN ( SELECT "
				+ "		t.sn "
				+ "	FROM xx_shipping t "
				+ "	JOIN xx_shipping_item t2 ON t2.shipping = t.id "
				+ "	WHERE 1 = 1"
				+ "	AND t2.orders = ? "
				+ ")");
		
		list.add(companyInfoId);
		list.add(id);
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql.toString(),objs, 0);
		return lists;
	}
}