package net.shopxx.order.dao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.util.RoleJurisdictionUtil;
@Repository("moveLibraryCloseDao")
public class MoveLibraryCloseDao extends DaoCenter{
	
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	public void neaten(List<Object> list,StringBuilder sql) {
		
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append(" SELECT IFNULL(mlci.quantity, 0) - IFNULL(am.quantity, 0) quantity,  ");
		sql.append(" FLOOR( CASE WHEN p.per_branch IS NOT NULL THEN ( IFNULL(mlci.quantity, 0) - IFNULL(am.quantity, 0) ) / p.per_branch ELSE 0 END ) branch_quantity, ");
		sql.append(" FLOOR( CASE WHEN p.per_box IS NOT NULL THEN ( IFNULL(mlci.quantity, 0) - IFNULL(am.quantity, 0) ) / p.per_box ELSE 0 END ) box_quantity, ");
		sql.append(" mlci.id,mlci.move_library_close,mlc.sn,mlci.remarks,mlci.close_box_quantity,mlci.close_branch_quantity,mlci.close_quantity, ");
		sql.append(" mlci.logistics_sn,cr.id close_reason_id,cr.value close_reason_name,mlc.sn,mlc.close_date, ");
		sql.append(" so.id sale_org_id,so.name sale_org_name,iw.id issue_warehouse_id,iw.name issue_name, ");
		sql.append(" sb.id sbu_id,sb.name sbu_name,ml.need_date,p.id product_id,p.name product_name,p.detail_description,p.model product_model, ");
		sql.append(" p.unit,p.vonder_code,po.id product_organization_id,po.name product_organization_name,sdt.id level_Id,mli.batch, ");
		sql.append(" sdt.value levelName,IFNULL(mli.issue_quantity,0) AS issue_quantity,IFNULL(mli.receive_quantity,0) AS receive_quantity, ");
		sql.append(" IFNULL(mli.close_quantity,0) AS mli_close_quantity,cn.id colour_number_id,cn.value colour_number_name,mli.batch_encoding, ");
		sql.append(" p.spec product_spec,mc.id moisture_content_id,mc.value moisture_content_name,p.branch_per_box,p.per_box,p.per_branch, ");
		sql.append(" lt.id logistics_type_id,lt.value logistics_type_name ");
		sql.append(" FROM  xx_move_library_close_item mlci ");
		sql.append(" LEFT JOIN xx_move_library_close mlc ON mlc.id = mlci.move_library_close ");
		sql.append(" LEFT JOIN xx_system_dict cr ON cr.id  = mlci.close_reason ");
		sql.append(" LEFT JOIN xx_system_dict lt ON lt.id  = mlci.logistics_type ");
		sql.append(" LEFT JOIN xx_move_library_item mli ON mli.id = mlci.move_library_item ");
		sql.append(" LEFT JOIN xx_move_library ml ON ml.id = mlc.move_library ");
		sql.append(" LEFT JOIN xx_sale_org so ON so.id = ml.sale_org ");
		sql.append(" LEFT JOIN xx_warehouse iw ON iw.id = ml.issue_warehouse ");
		sql.append(" LEFT JOIN xx_sbu sb ON sb.id = ml.sbu ");
		sql.append(" LEFT JOIN xx_product p ON mli.product = p.id ");
		sql.append(" LEFT JOIN xx_organization po ON po.id = mli.product_organization ");
		sql.append(" LEFT JOIN xx_system_dict sdt ON sdt.id = mli.product_level ");
		sql.append(" LEFT JOIN xx_system_dict cn  ON cn.id = mli.colour_numbers ");
		sql.append(" LEFT JOIN xx_system_dict mc ON mc.id = mli.moisture_contents ");
		sql.append(" LEFT JOIN (SELECT amsi.move_library_close_item,SUM(amsi.quantity) quantity "+
									" FROM xx_am_shipping_item amsi "+ 
									" LEFT JOIN xx_am_shipping ams ON amsi.am_shipping = ams.id "+ 
									" WHERE ams.STATUS <> 2 GROUP BY amsi.move_library_close_item ) am ON am.move_library_close_item = mlci.id  ");
		sql.append(" WHERE 1=1 AND IFNULL(mlci.quantity, 0) - IFNULL(am.quantity, 0) > 0  ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and mlc.company_info_id = ? ");
			list.add(companyInfoId);
		}
	}
	
	
	
	/**
	 * 移库关闭列表数据
	 * @param sn
	 * @param saleOrgId
	 * @param sbuId
	 * @param productId
	 * @param startCloseDate
	 * @param endCloseDate
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> newFindMovelibraryClosePage(String sn,Long[] saleOrgId,
			Long[] issueWarehouseId,Long[] sbuId,Long[] productId,String startCloseDate,
			String endCloseDate,Long[] ids,Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		this.neaten(list, sql);
		sql.append(" AND mlc.status in (2,4) ");
		// 单据编号
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and mlc.sn like ?");
			list.add("%" + sn + "%");
		}
		// 关闭明细Ids
		if (!ConvertUtil.isEmpty(ids) && ids.length > 0) {
			String os = "";
			for (int i = 0; i < ids.length; i++) {
				if (i == ids.length - 1)
					os += ids[i];
				else
					os += ids[i] + ",";
			}
			sql.append(" and mlci.id in (" + os + ")");
		}
		// 来源仓库
		if (!ConvertUtil.isEmpty(issueWarehouseId) && issueWarehouseId.length > 0) {
			String os = "";
			for (int i = 0; i < issueWarehouseId.length; i++) {
				if (i == issueWarehouseId.length - 1)
					os += issueWarehouseId[i];
				else
					os += issueWarehouseId[i] + ",";
			}
			sql.append(" and iw.id in (" + os + ")");
		}
		// 机构
		if (!ConvertUtil.isEmpty(saleOrgId) && saleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					os += saleOrgId[i];
				else
					os += saleOrgId[i] + ",";
			}
			sql.append(" and so.id  in (" + os + ")");
		}
		// SBU
		if (!ConvertUtil.isEmpty(sbuId) && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += sbuId[i];
				else
					os += sbuId[i] + ",";
			}
			sql.append(" and sb.id in (" + os + ")");
		}
		// 产品
		if (!ConvertUtil.isEmpty(productId) && productId.length > 0) {
			String os = "";
			for (int i = 0; i < productId.length; i++) {
				if (i == productId.length - 1)
					os += productId[i];
				else
					os += productId[i] + ",";
			}
			sql.append(" and p.id in (" + os + ")");
		}
		// 关闭日期
		if (!ConvertUtil.isEmpty(startCloseDate)) {
			sql.append(" and  mlc.close_date >= ? ");
			list.add(startCloseDate + " 00:00:00 ");
		}
		if (!ConvertUtil.isEmpty(endCloseDate)) {
			sql.append(" and mlc.close_date <= ? ");
			list.add(endCloseDate + " 23:59:59 ");
		}
		// 用户仓库
		String warehouseIds = roleJurisdictionUtil.getWarehouseIds();
		if (!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("-1")) {
			if (!ConvertUtil.isEmpty(warehouseIds) && !warehouseIds.equals("0")) {
				sql.append(" and iw.id in (" + warehouseIds + ")");
			} else {
				sql.append(" and iw.id is null");
			}
		}
		// 用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")) {
			if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
				sql.append(" and so.id in (" + saleOrgIds + ")");
			} else {
				sql.append(" and so.id is null");
			}
		}
		// 用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")) {
			if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
				sql.append(" and sb.id in (" + sbuIds + ")");
			} else {
				sql.append(" and sb.id is null");
			}
		}
		// 用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")) {
			if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")) {
				sql.append(" and po.id in (" + organizationIdS + ")");
			} else {
				sql.append(" and po.id is null");
			}
		}

		sql.append(" group by mlci.id order by mlci.modify_date desc  ");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		
		String sqlToString = sql.toString().replaceAll("SELECT.*FROM"," SELECT 1 FROM ");

		String totalsql = "select count(1) from ( " + sqlToString + ") t";
			
		long total = getNativeDao().findInt(totalsql, objs);
		
		page.setTotal(total);
		
		return page;
	}
	
	
	
	/**
	 * 根据移库关闭明细ids查找明细
	 * @param id
	 * @return
	 */
	public List<Map<String, Object>> findMoveLibraryCloseItemListData(Long[] ids) {

		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		this.neaten(list, sql);
		sql.append(" AND mlc.status in (2,4) ");
		if (!ConvertUtil.isEmpty(ids) && ids.length > 0) {
			String os = "";
			for (int i = 0; i < ids.length; i++) {
				if (i == ids.length - 1)
					os += ids[i];
				else
					os += ids[i] + ",";
			}
			sql.append(" and mlci.id in (" + os + ")");
		}
		sql.append(" group by mlci.id order by mlci.modify_date desc  ");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(), objs, 0);

		return mapList;
	}
	
	
	
	
	
	
	/**
	 * 根据移库关闭单id查找明细
	 * @param id
	 * @return
	 */
	public List<Map<String, Object>> findMoveLibraryCloseItemList(Long id) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT mlci.id move_library_close_item_id,mlci.box_quantity mlci_box_quantity, ");
			sql.append(" mlci.branch_quantity mlci_branch_quantity,mlci.scattered_quantity mlci_scattered_quantity, ");
			sql.append(" mlci.quantity mlci_quantity,cr.id close_reason_id,cr.value close_reason_name, ");
			sql.append(" mlci.remarks mlci_remarks,mlci.logistics_sn mlci_logistics_sn,lt.id logistics_type_id, ");
			sql.append(" lt.value logistics_type_name,vml.* ");
		sql.append(" FROM  xx_move_library_close_item mlci ");
		sql.append(" LEFT JOIN xx_move_library_close mlc ON mlc.id = mlci.move_library_close  ");
		sql.append(" LEFT JOIN xx_system_dict cr ON cr.id  = mlci.close_reason ");
		sql.append(" LEFT JOIN xx_system_dict lt ON lt.id  = mlci.logistics_type ");
		sql.append(" LEFT JOIN v_move_library vml ON vml.id = mlci.move_library_item ");
		sql.append(" WHERE 1=1 ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and mlc.company_info_id = ? ");
			list.add(companyInfoId);
		}
		
		if (!ConvertUtil.isEmpty(id)) {
			sql.append(" and mlc.id = ? ");
			list.add(id);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(), objs, 0);

		return mapList;
	}
}
