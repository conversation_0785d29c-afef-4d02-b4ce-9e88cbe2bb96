package net.shopxx.member.service;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.member.entity.Adjustment;
public interface AdjustmentService  extends ActWfBillService<Adjustment> {
	
	public Page<Map<String, Object>> findPage(String sn,Long[] states,
			Long[] wfStates,String sourceSn,Long[] sourceStoreId,
			Long[] adjustmentStoreId,Long[] sourceSbuId,Long[] adjustmentSbuId,
			Long[] sourceOrganizationId,Long[] adjustmentOrganizationId,String adjustmentSn,
			Long[] sourceSaleOrgId,Long[] adjustmentSaleOrgId,Long[] ids,Pageable pageable);
	
	
	public void saveAdjustment(Adjustment adjustment,
			Long sourceStoreId,BigDecimal adjustmentAmount,Long sourceSbuId,
			Long sourceBankCardId,Long sourceOrganizationId,Long adjustmentStoreId,
			BigDecimal adjustmentActualAmount,Long adjustmentSbuId,Long adjustmentBankCardId,
			Long sourceSaleOrgId,Long adjustmentSaleOrgId,Long adjustmentOrganizationId);
	
	
	public List<Map<String, Object>> findListByAdjustmentId(Long id);
	
	
	
	public void  updateAdjustment(Adjustment adjustment,
			Long sourceStoreId,BigDecimal adjustmentAmount,Long sourceSbuId,
			Long sourceBankCardId,Long sourceOrganizationId,Long adjustmentStoreId,
			BigDecimal adjustmentActualAmount,Long adjustmentSbuId,Long adjustmentBankCardId,
			Long sourceSaleOrgId,Long adjustmentSaleOrgId,Long adjustmentOrganizationId);
	
	
	public void cancel(Adjustment adjustment);
	
	
	/**
	 * 创建流程实例 
	 * @param id
	 * @param modelId
	 * @param objTypeId
	 */
	public void createWf(Adjustment adjustment, String modelId, Long objTypeId);
	
	/**
	 * 根据来源Id以及调账Id查询调账信息
	 * @param depositRechargeId
	 * @return
	 */
	public  List<Map<String, Object>> findAdjustmentList(Long depositRechargeId);
	

}
