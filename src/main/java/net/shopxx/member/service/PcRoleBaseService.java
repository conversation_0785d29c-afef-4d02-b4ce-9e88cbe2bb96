package net.shopxx.member.service;

import java.util.List;

import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.PcMenu;
import net.shopxx.member.entity.PcRole;

/**
 * Service - PC角色
 */
public interface PcRoleBaseService extends BaseService<PcRole> {
	public PcRole findAdministrator();
	
	/**
	 * 获取当前用户角色
	 * @return
	 */
	public List<PcRole> findByStoreMemberId(Long storeMemberId);
}