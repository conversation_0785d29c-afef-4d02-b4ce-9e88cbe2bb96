package net.shopxx.member.service;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.BalanceReport;
public interface BalanceReportService extends BaseService<BalanceReport>{
	
	
	/**
	 * 参数不能为空
	 * @param balanceReport
	 */
	public void checkParamIsNotNull(BalanceReport balanceReport);
	
	
	/**
	 * 查询或新增客户余额报表
	 * @param balanceReport
	 * @return
	 */
	public BalanceReport queryOrInsert(BalanceReport balanceReport);
	
}