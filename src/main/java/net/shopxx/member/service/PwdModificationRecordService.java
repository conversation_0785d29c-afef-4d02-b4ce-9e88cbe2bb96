package net.shopxx.member.service;

import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.PwdModificationRecord;

/**
 * 修改密码记录
 */
public interface PwdModificationRecordService extends BaseService<PwdModificationRecord> {

    //获取指定用户的30天内修改记录
    public int getPwdModificationCount(Long storeMemberId);

    //保存提交密码修改
    public String saveUserPasswordRecord(Long storeMemberId,String oldpwd, String newPwd, String confirmNewPwd,String language);

}
