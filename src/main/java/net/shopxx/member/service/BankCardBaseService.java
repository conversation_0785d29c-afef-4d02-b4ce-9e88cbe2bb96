package net.shopxx.member.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import jxl.read.biff.BiffException;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.BankCard;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface BankCardBaseService extends BaseService<BankCard> {

	Page<Map<String, Object>> findPage(Pageable pageable, Long saleOrgId, String bankName, 
			String bankCardNo,String mobile, Boolean isEnabled,Long sbuId,Boolean isTotalAccount,
			String bankCode);
	
	public List<BankCard> findListBySbu(Long saleOrgId,Long organizationId,Long sbuId);

    void importFromExcel(MultipartFile file) throws IOException, BiffException;
}