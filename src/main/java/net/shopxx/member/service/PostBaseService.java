/*
 * 
 */
package net.shopxx.member.service;

import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.Post;

/**
 * Service - 岗位
 */
public interface PostBaseService extends BaseService<Post> {

	public Page<Map<String, Object>> findPage(String startTime, String endTime, Pageable pageable, String name,
			String sn, Long saleOrgId,Long isEnabled);
}
