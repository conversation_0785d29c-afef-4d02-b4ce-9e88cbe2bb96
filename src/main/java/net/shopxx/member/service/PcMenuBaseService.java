package net.shopxx.member.service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.PcMenu;

import java.util.List;
import java.util.Map;

/**
 * Service - PC角色
 */
public interface PcMenuBaseService extends BaseService<PcMenu> {

	/**
	 * 获取当前用户菜单
	 * @return
	 */
	public List<PcMenu> getCurrentMenuList(Integer type);

	public Integer findMenuCount(Integer type, String code);

	/**
	 * 获取所有二级菜单
	 * @return
	 */
	public List<Map<String,Object>> getAllSecondaryMenus(String menuName);

    /**
     * 获取菜单维护列表
     * @param type 菜单类型
     * @param superId 父菜单
     * @param menuName 菜单名
     * @param isEnabled 活动状态
     * @param pageable
     * @return
     */
    public Page<Map<String,Object>> findMenuMaintenanceList(Integer type,int isSuper, Long superId, String menuName, Boolean isEnabled,String createDate, Pageable pageable);

    /**
     * 保存菜单
     * @param pcMenu
     * @param parentdmenuId
     * @return
     */
    public Long saveMenu(PcMenu pcMenu, Long parentdmenuId);

	/**
	 * 删除菜单
	 * @param menuId
	 * @param pcRole
	 */
	public void deleteMenu(Long menuId,Long pcRole);

	/**
	 * 删除角色
	 * @param menuId
	 * @param pcRole
	 */
	public void deleteRole(Long menuId,Long pcRole);

    /**
     * 获取所有菜单
     * @param type
     * @return
     */
	public List<PcMenu> findAllMenuList(Integer type);
}