package net.shopxx.member.service;
import java.util.List;
import java.util.Map;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.CustomerRecharge;
public interface CustomerRechargeService extends BaseService<CustomerRecharge>{
	
	public CustomerRecharge saveOrUpdate(CustomerRecharge customerRecharge);
	
	public List<Map<String, Object>> findCustomerRechargeList(Long storeId,Long organizationId,Long sbuId);
		
	/**
	 * 初始化参数
	 * @param moveLibrary
	 */
	public CustomerRecharge checkParamIsNotNull(CustomerRecharge customerRecharge);
	
}
