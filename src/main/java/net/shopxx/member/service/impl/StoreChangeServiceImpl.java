package net.shopxx.member.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.dao.StoreChangeDao;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.Store.Type;
import net.shopxx.member.entity.StoreAddress;
import net.shopxx.member.entity.StoreAddressChange;
import net.shopxx.member.entity.StoreAddressH;
import net.shopxx.member.entity.StoreChange;
import net.shopxx.member.entity.StoreH;
import net.shopxx.member.entity.StoreInvoiceInfo;
import net.shopxx.member.entity.StoreInvoiceInfoChange;
import net.shopxx.member.entity.StoreInvoiceInfoH;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreAddressService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreChangeService;
import net.shopxx.member.service.StoreHService;
import net.shopxx.member.service.StoreInvoiceInfoService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service - 客户变更
 *
 */
@Service("storeChangeServiceImpl")
public class StoreChangeServiceImpl extends WfBillBaseServiceImpl<StoreChange>
		implements StoreChangeService {

	@Resource(name = "storeChangeDao")
	private StoreChangeDao storeChangeDao;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeHServiceImpl")
	private StoreHService storeHService;
	@Resource(name = "storeAddressServiceImpl")
	private StoreAddressService storeAddressService;
	@Resource(name = "storeInvoiceInfoServiceImpl")
	private StoreInvoiceInfoService storeInvoiceInfoService;

	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;

	@Override
	@Transactional
	public void save(StoreChange storeChange, Long storeMemberId, Long areaId,
			Long saleOrgId, Long salesPlatformId, Long businessTypeId,
			BankCard bankCard, Long bAreaId, String bAddress, String bMobile,
			Long changeMemberRankId) {

		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		if (saleOrgId != null) {
			storeChange.setSaleOrg(saleOrg);
		}
		else {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("isTop", true));
			List<SaleOrg> saleOrgs = saleOrgBaseService.findList(1,
					filters,
					null);
			if (saleOrgs.size() > 0) {
				storeChange.setSaleOrg(saleOrgs.get(0));
			}
		}
		StoreMember storeMember = storeMemberBaseService.find(storeMemberId);
		if (storeMemberId != null) {
			storeChange.setStoreMember(storeMember);
		}

		SaleOrg salesPlatform = saleOrgBaseService.find(salesPlatformId);
		storeChange.setSalesPlatform(salesPlatform);

		storeChange.setMemberRank(null);
		storeChange.setChangeMemberRank(
				memberRankBaseService.find(changeMemberRankId));
		Area area = areaBaseService.find(areaId);
		storeChange.setArea(area);
		storeChange.setIsEnabled(true);
		storeChange.setIsMainStore(false);
		if (!storeChange.getType().equals(Type.onlineShop)) {
			storeChange.setOutShopName(null);
			storeChange.setOnlineShopType(null);
		}

		List<StoreAddressChange> storeAddressChanges = storeChange
				.getStoreAddressChanges();
		for (Iterator<StoreAddressChange> iterator = storeAddressChanges
				.iterator(); iterator.hasNext();) {
			StoreAddressChange storeAddressChange = iterator.next();
			if (storeAddressChange == null) {
				iterator.remove();
			}
			if (storeAddressChange.getArea() != null &&storeAddressChange.getArea().getId() != null) {
				Area a = areaService.find(storeAddressChange.getArea().getId());
				storeAddressChange.setArea(a);
			}else{
				storeAddressChange.setArea(null);
			}
			storeAddressChange.setStoreChange(storeChange);
		}
		storeChange.setStoreAddressChanges(storeAddressChanges);

		List<StoreInvoiceInfoChange> storeInvoiceInfoChanges = storeChange
				.getStoreInvoiceInfoChanges();
		for (Iterator<StoreInvoiceInfoChange> iterator = storeInvoiceInfoChanges
				.iterator(); iterator.hasNext();) {
			StoreInvoiceInfoChange storeInvoiceInfoChange = iterator.next();
			if (storeInvoiceInfoChange == null
					|| storeInvoiceInfoChange.getInvoiceTitle() == null) {
				iterator.remove();
			}
			storeInvoiceInfoChange.setStoreChange(storeChange);
		}
		storeChange.setStoreAddressChanges(storeAddressChanges);

		storeChange.setCreateBy(storeMemberBaseService.getCurrent());
		storeChange.setUnJoinSucessFlag(false);

		if (businessTypeId != null) {
			SystemDict businessType = systemDictBaseService
					.find(businessTypeId);
			storeChange.setBusinessType(businessType);
		}

		storeChange.setStatus(0);
		storeChange.setSn(Sequence.getInstance().getSequence(null));
		save(storeChange);

	}

	@Override
	@Transactional
	public void update(StoreChange storeChange, Long storeMemberId,
			Long saleStyleId, Long areaId, Long salesPlatformId, Long saleOrgId,
			Long businessTypeId, BankCard bankCard, Long bAreaId,
			String bAddress, Boolean bIsEnabled, String bMobile,
			Long changeMemberRankId, Long sbuId, Long customerTypeId,
			Long gradenId) {

		StoreChange pStoreChange = this.find(storeChange.getId());
		if (pStoreChange.getStatus() != 0) {
			ExceptionUtil.throwServiceException("保存失败，变更单非已保存状态");
		}
		if (storeBaseService.exists(Filter.eq("name", storeChange.getName()),
				Filter.ne("id", storeChange.getStoreId()),
				Filter.eq("companyInfoId",
						WebUtils.getCurrentCompanyInfoId()))) {
			// 客户名称已存在
			ExceptionUtil.throwServiceException("客户名称已存在");
		}
		if (storeChange.getTaxRate() != null) {
			storeChange.setTaxRate(
					storeChange.getTaxRate().divide(new BigDecimal(100)));
		}

		SystemDict sbu = systemDictBaseService.find(sbuId);
		storeChange.setSbu(sbu);
		SystemDict graden = systemDictBaseService.find(gradenId);
		storeChange.setGraden(graden);
		SystemDict customerType = systemDictBaseService.find(customerTypeId);
		storeChange.setCustomerType(customerType);
		SystemDict saleStyle = systemDictBaseService.find(saleStyleId);
		storeChange.setSaleStyle(saleStyle);

		Store pStore = storeBaseService.find(storeChange.getStoreId());

		SaleOrg salesPlatform = saleOrgBaseService.find(salesPlatformId);
		storeChange.setSalesPlatform(salesPlatform);

		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		List<Filter> filters = new ArrayList<Filter>();
		if (saleOrgId != null) {
			storeChange.setSaleOrg(saleOrg);
		}
		else {
			filters.add(Filter.eq("isTop", true));
			List<SaleOrg> saleOrgs = saleOrgBaseService.findList(1,
					filters,
					null);
			if (saleOrgs.size() > 0) {
				storeChange.setSaleOrg(saleOrgs.get(0));
			}
		}
		StoreMember storeMember = storeMemberBaseService.find(storeMemberId);
		if (storeMemberId != null) {
			storeChange.setStoreMember(storeMember);
		}
		storeChange.setMemberRank(null);
		storeChange.setChangeMemberRank(
				memberRankBaseService.find(changeMemberRankId));
		Area area = areaBaseService.find(areaId);
		storeChange.setArea(area);
		storeChange.setIsMainStore(pStore.getIsMainStore());
		if (!storeChange.getType().equals(Type.onlineShop)) {
			storeChange.setOutShopName(null);
			storeChange.setOnlineShopType(null);
		}

		List<StoreAddressChange> storeAddressChanges = storeChange
				.getStoreAddressChanges();
		for (Iterator<StoreAddressChange> iterator = storeAddressChanges
				.iterator(); iterator.hasNext();) {
			StoreAddressChange storeAddressChange = iterator.next();
			if (storeAddressChange == null) {
				iterator.remove();
			}
			if (storeAddressChange.getArea() != null && storeAddressChange.getArea().getId() != null) {
				Area a = areaService.find(storeAddressChange.getArea().getId());
				storeAddressChange.setArea(a);
			}else{
				storeAddressChange.setArea(null);
			}
			storeAddressChange.setStoreChange(storeChange);
		}
		storeChange.setStoreAddressChanges(storeAddressChanges);

		List<StoreInvoiceInfoChange> storeInvoiceInfoChanges = storeChange
				.getStoreInvoiceInfoChanges();
		for (Iterator<StoreInvoiceInfoChange> iterator = storeInvoiceInfoChanges
				.iterator(); iterator.hasNext();) {
			StoreInvoiceInfoChange storeInvoiceInfoChange = iterator.next();
			if (storeInvoiceInfoChange == null
					|| storeInvoiceInfoChange.getInvoiceTitle() == null) {
				iterator.remove();
			}
			storeInvoiceInfoChange.setStoreChange(storeChange);
		}
		storeChange.setStoreAddressChanges(storeAddressChanges);

		if (businessTypeId != null) {
			SystemDict businessType = systemDictBaseService
					.find(businessTypeId);
			storeChange.setBusinessType(businessType);
		}

		storeChange.setStatus(0);
		update(storeChange, "sn");

	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String name, String sn,
			String outTradeNo, String mobile, String serviceTelephone,
			Integer[] type, String outShopName, Long saleOrgId,
			Long companyInfoId, Long memberRankId, Boolean isEnabled,
			Integer isSelect, Integer isCustomer, Integer[] status,
			Pageable pageable,String signingTime) {

		return storeChangeDao.findPage(name,
				sn,
				outTradeNo,
				mobile,
				serviceTelephone,
				type,
				outShopName,
				saleOrgId,
				companyInfoId,
				memberRankId,
				isEnabled,
				isSelect,
				isCustomer,
				status,
				pageable,
				signingTime);
	}

	@Override
	public void check(StoreChange storeChange, Long storeMemberId,
			Long saleStyleId, Long areaId, Long salesPlatformId, Long saleOrgId,
			Long businessTypeId, Long memberRankId, BankCard bankCard,
			Long bAreaId, String bAddress, Boolean bIsEnabled, String bMobile,
			Long changeMemberRankId, Long sbuId, Long customerTypeId,
			Long gradenId) {

		this.update(storeChange,
				storeMemberId,
				saleStyleId,
				areaId,
				salesPlatformId,
				saleOrgId,
				businessTypeId,
				bankCard,
				bAreaId,
				bAddress,
				bIsEnabled,
				bMobile,
				changeMemberRankId,
				sbuId,
				customerTypeId,
				gradenId);

		StoreChange pStoreChange = this.find(storeChange.getId());
		pStoreChange.setStatus(1);
		this.update(pStoreChange);

		//变更数据写入客户表
		this.updateStoreByStoreChangeWithHistory(pStoreChange);

	}

	@Override
	public void checkWf(StoreChange storeChange, Long storeMemberId,
			Long saleStyleId, Long areaId, Long salesPlatformId, Long saleOrgId,
			Long businessTypeId, Long memberRankId, BankCard bankCard,
			Long bAreaId, String bAddress, Boolean bIsEnabled, String bMobile,
			Long changeMemberRankId, Long sbuId, Long customerTypeId,
			Long gradenId, Long objConfId) {

		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService
				.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}

		this.update(storeChange,
				storeMemberId,
				saleStyleId,
				areaId,
				salesPlatformId,
				saleOrgId,
				businessTypeId,
				bankCard,
				bAreaId,
				bAddress,
				bIsEnabled,
				bMobile,
				changeMemberRankId,
				sbuId,
				customerTypeId,
				gradenId);

		StoreChange pStoreChange = this.find(storeChange.getId());
		pStoreChange.setByObjConfig(wfObjConfigLine); // 设置流程配置
		this.update(pStoreChange);

		// 创建流程实例
		wfBaseService.createwf(storeMemberBaseService.getCurrent(),
				pStoreChange.getSaleOrg() == null ? null
						: pStoreChange.getSaleOrg().getId(),
				null,
				pStoreChange.getWfTempId(),
				pStoreChange.getObjTypeId(),
				pStoreChange.getId());

	}

	@Override
	public void agreeBack(Wf wf) {
		if (wf.getStat().intValue() == 2) {
			StoreChange storeChange = find(wf.getObjId());
			storeChange.setStatus(1);
			this.update(storeChange);
			//变更数据写入客户表
			this.updateStoreByStoreChangeWithHistory(storeChange);
		}
	}

	public void updateStoreByStoreChangeWithHistory(StoreChange storeChange) {
		Store store = storeBaseService.find(storeChange.getStoreId());
		//写历史表
		this.saveStoreH(store);
		//同步变更单数据到客户表
		this.updateStoreByStoreChange(storeChange, store);
	}

	public void updateStoreByStoreChange(StoreChange storeChange, Store store) {

		//复制主表
		BeanUtils.copyProperties(storeChange,
				store,
				new String[] { "id",
						"sn",
						"uniqueIdentify",
						"createDate",
						"modifyDate",
						"storeAddressChanges",
						"storeInvoiceInfoChanges", });

		//复制地址明细
		List<StoreAddress> storeAddresss = new ArrayList<StoreAddress>();
		for (StoreAddressChange storeAddressChange : storeChange
				.getStoreAddressChanges()) {

			StoreAddress storeAddress = storeAddressService
					.find(storeAddressChange.getStoreAddressId());
			if (storeAddress == null) {
				storeAddress = new StoreAddress();
			}
			BeanUtils.copyProperties(storeAddressChange,
					storeAddress,
					new String[] { "id",
							"createDate",
							"modifyDate",
							"storeChange",
							"storeAddressId" });
			storeAddress.setStore(store);
			storeAddresss.add(storeAddress);
		}
		store.getStoreAddress().clear();
		store.getStoreAddress().addAll(storeAddresss);

		//复制发票明细
		List<StoreInvoiceInfo> storeInvoiceInfos = new ArrayList<StoreInvoiceInfo>();
		for (StoreInvoiceInfoChange storeInvoiceInfoChange : storeChange
				.getStoreInvoiceInfoChanges()) {
			StoreInvoiceInfo storeInvoiceInfo = storeInvoiceInfoService
					.find(storeInvoiceInfoChange.getStoreInvoiceInfoId());
			if (storeInvoiceInfo == null) {
				storeInvoiceInfo = new StoreInvoiceInfo();
			}
			BeanUtils.copyProperties(storeInvoiceInfoChange,
					storeInvoiceInfo,
					new String[] { "id",
							"createDate",
							"modifyDate",
							"storeChange",
							"storeInvoiceInfoId" });
			storeInvoiceInfo.setStore(store);
			storeInvoiceInfos.add(storeInvoiceInfo);
		}
		store.getStoreInvoiceInfos().clear();
		store.getStoreInvoiceInfos().addAll(storeInvoiceInfos);

		storeBaseService.update(store);

	}

	public void saveStoreH(Store store) {

		//复制主表
		StoreH storeH = new StoreH();
		BeanUtils.copyProperties(store,
				storeH,
				new String[] { "id",
						"createDate",
						"modifyDate",
						"storeAddress",
						"storeInvoiceInfos",
						"storeManagers",
						"businessCategorys",
						"cautionMoneies",
						"businessRecords" });
		storeH.setStoreId(store.getId());

		//复制地址明细
		List<StoreAddressH> storeAddressHs = new ArrayList<StoreAddressH>();
		for (StoreAddress storeAddress : store.getStoreAddress()) {

			StoreAddressH storeAddressH = new StoreAddressH();
			BeanUtils.copyProperties(storeAddress,
					storeAddressH,
					new String[] { "id", "createDate", "modifyDate", });
			storeAddressH.setStoreAddressId(storeAddress.getId());
			storeAddressH.setStoreH(storeH);
			storeAddressHs.add(storeAddressH);
		}
		storeH.setStoreAddressHs(storeAddressHs);

		//复制发票明细
		List<StoreInvoiceInfoH> storeInvoiceInfoHs = new ArrayList<StoreInvoiceInfoH>();
		for (StoreInvoiceInfo storeInvoiceInfo : store.getStoreInvoiceInfos()) {

			StoreInvoiceInfoH storeInvoiceInfoH = new StoreInvoiceInfoH();
			BeanUtils.copyProperties(storeInvoiceInfo,
					storeInvoiceInfoH,
					new String[] { "id", "createDate", "modifyDate", });
			storeInvoiceInfoH.setStoreInvoiceInfoId(storeInvoiceInfo.getId());
			storeInvoiceInfoH.setStoreH(storeH);
			storeInvoiceInfoHs.add(storeInvoiceInfoH);
		}
		storeH.setStoreInvoiceInfoHs(storeInvoiceInfoHs);

		storeHService.save(storeH);
	}

}
