package net.shopxx.member.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.member.dao.DepositRechargeDao;
import net.shopxx.member.entity.DepositAttach;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.DepositRecharge.Status;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.BudgetService;
import net.shopxx.member.service.ExpenseBudgetService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("expenseBudgetServiceImpl")
public class ExpenseBudgetServiceImpl extends WfBillBaseServiceImpl<DepositRecharge> implements ExpenseBudgetService {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "depositRechargeDao")
	private DepositRechargeDao depositRechargeDao;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "budgetServiceImpl")
	private BudgetService budgetService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;

	@Override
	@Transactional
	public void check(DepositRecharge depositRecharge, Integer flag, String note, BigDecimal actualAmount) {

		depositRecharge.setNote(note);
		depositRecharge.setCheckDate(new Date());
		depositRecharge.setOperator(storeMemberService.getCurrent());
		if (flag == 2) {
			depositRecharge.setStatus(DepositRecharge.Status.failure); // 审核未通过
		} else if (flag == 1) {
			depositRecharge.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
												// 2.已处理(流程走完) 3.关闭
			if (actualAmount == null) {
				actualAmount = depositRecharge.getAmount();
			}
			depositRecharge.setActualAmount(actualAmount);
			update(depositRecharge);
			orderFullLinkService.addFullLink(7, null, depositRecharge.getSn(),
					ConvertUtil.convertI18nMsg("18702", new Object[] { "费用预算" }), null);
		}
	}

	@Override
	@Transactional
	public void checkExpenseBudgetWf(DepositRecharge depositRecharge, String note, BigDecimal actualAmount,
			Long objConfId) {

		depositRecharge.setNote(note);
		if (actualAmount == null) {
			actualAmount = depositRecharge.getAmount();
		}
		depositRecharge.setActualAmount(actualAmount);

		// 创建流程实例
		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}
		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());

        ExceptionUtil.throwServiceException("创建流程的代码被注释，请联系管理员");
//		depositRecharge.setByObjConfig(wfObjConfigLine); // 设置流程配置
//		wfBaseService.createwf(storeMemberService.getCurrent(),
//				depositRecharge.getSaleOrg() == null ? null : depositRecharge.getSaleOrg().getId(),
//				depositRecharge.getSn(), depositRecharge.getWfTempId(), depositRecharge.getObjTypeId(),
//				depositRecharge.getId());

		depositRecharge.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
											// 2.已处理(流程走完) 3.关闭
		update(depositRecharge);
		orderFullLinkService.addFullLink(7, null, depositRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18701", new Object[] { wfTemp.getWfTempName() }), null);
	}

	/**
	 * 机构预算充值
	 */
	public void saleOrgBudget(BigDecimal amount, DepositRecharge depositRecharge) {

		Integer budgetType = depositRecharge.getBudgetType();
		SaleOrg saleOrg = depositRecharge.getSaleOrg();
		if (budgetType == 1) {
			// 扣减锁定机构费用预算
			budgetService.deductSaleOrgLockBudget(amount, saleOrg.getId());
			amount = amount.multiply(new BigDecimal(-1));
		} else {
			saleOrg.setBudget(saleOrg.getBudget().add(amount));
			saleOrgService.update(saleOrg);
		}
		// 生成一张付款单
		paymentService.initPayment(6, 3, 6, 1, 1, amount, null, null, null, null, depositRecharge.getSaleOrg(),
				depositRecharge.getSn(), null, null, null, "机构费用预算申请");
	}

	/**
	 * 客户预算充值
	 */
	public void storeBudget(BigDecimal amount, DepositRecharge depositRecharge) {

		Integer budgetType = depositRecharge.getBudgetType();
		Store store = depositRecharge.getStore();
		if (budgetType == 1) {
			// 扣减锁定机构费用预算
			budgetService.deductStoreLockBudget(amount, store.getId());
			amount = amount.multiply(new BigDecimal(-1));
		} else {
			store.setBudget(store.getBudget().add(amount));
			storeService.update(store);
		}
		// 生成一张付款单
		paymentService.initPayment(7, 3, 7, 1, 1, amount, null, null, null, depositRecharge.getStore(), null,
				depositRecharge.getSn(), null, null, null, "客户费用预算申请");

	}

	/**
	 * 用户、客户余额充值列表数据
	 */
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Integer[] type, String sn, Long storeMemberId, Long storeId,
			Long saleOrgId, Integer[] docstatus, Long[] rechargeTypeId, Long creatorId, Long operatorId,
			Integer[] budgetType, Pageable pageable) {
		return depositRechargeDao.findPage(type, sn, storeMemberId, storeId, null, docstatus, rechargeTypeId, creatorId,
				operatorId, budgetType, null, null, pageable);
	}

	@Override
	public void agreeBack(Wf wf) {

		if (wf.getStat().intValue() == 2) {

			DepositRecharge depositRecharge = find(wf.getObjId());

			depositRecharge.setCheckDate(new Date());
			depositRecharge.setOperator(storeMemberService.getCurrent());
			depositRecharge.setStatus(DepositRecharge.Status.success); // 审核通过
			depositRecharge.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
												// 2.已处理(流程走完) 3.关闭
			update(depositRecharge);
		}
	}

	@Override
	public void interruptBack(Wf wf) {

		DepositRecharge depositRecharge = find(wf.getObjId());
		depositRecharge.setDocStatus(0); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
											// 2.已处理(流程走完) 3.关闭
		update(depositRecharge);
		orderFullLinkService.addFullLink(7, null, depositRecharge.getSn(), ConvertUtil.convertI18nMsg("18704"), null);
	}

	@Override
	@Transactional
	public void saveOrUpdate(DepositRecharge depositRecharge, Integer fullLinkType, Long rechargeTypeId) {
		DepositRecharge dr = depositRecharge;

		// 附件
		List<DepositAttach> depositAttachs = dr.getDepositAttachs();
		for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext();) {
			DepositAttach depositAttach = iterator.next();
			if (depositAttach == null || depositAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (depositAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			depositAttach.setFileName(depositAttach.getName() + "." + depositAttach.getSuffix());
			depositAttach.setDepositRecharge(dr);
			depositAttach.setStoreMember(storeMemberService.getCurrent());
		}

		if (depositRecharge.getId() != null) {// 更新
			dr = find(depositRecharge.getId());

			Store store = depositRecharge.getStore();
			if (store != null) {
				dr.setStore(depositRecharge.getStore());
			} else {
				dr.setSaleOrg(depositRecharge.getSaleOrg());
			}
			dr.setBudgetType(depositRecharge.getBudgetType());
			dr.setImage(depositRecharge.getImage());
			dr.setImage2(depositRecharge.getImage2());
			dr.setImage3(depositRecharge.getImage3());
			dr.setAmount(depositRecharge.getAmount());
			dr.setMemo(depositRecharge.getMemo());

			dr.getDepositAttachs().clear();
			dr.getDepositAttachs().addAll(depositAttachs);

			update(dr);
		} else {// 保存
			dr.setRechargeType(systemDictService.find(rechargeTypeId));
			dr.setStatus(Status.wait);
			dr.setDocStatus(0); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.关闭
			dr.setCreator(storeMemberService.getCurrent());
			dr.setStoreMember(storeMemberService.getCurrent());
			dr.setActualAmount(BigDecimal.ZERO);
			dr.setSn(SnUtil.generateSn());

			dr.setDepositAttachs(depositAttachs);

			save(dr);
			orderFullLinkService.addFullLink(fullLinkType, null, dr.getSn(),
					ConvertUtil.convertI18nMsg("18700", new Object[] { "费用预算申请" }), null);
		}
	}

}
