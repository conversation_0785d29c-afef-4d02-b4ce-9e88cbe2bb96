package net.shopxx.member.service.impl;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.dao.CustomerRechargeDao;
import net.shopxx.member.entity.CustomerRecharge;
import net.shopxx.member.service.CustomerRechargeService;
@Service("customerRechargeServiceImpl")
public class CustomerRechargeServiceImpl extends BaseServiceImpl<CustomerRecharge> implements CustomerRechargeService{
	
	@Resource(name = "customerRechargeDao")
    private CustomerRechargeDao customerRechargeDao;
	
	
	/**
	 *初始化参数
	 */
	@Override
	@Transactional
	public CustomerRecharge checkParamIsNotNull(CustomerRecharge customerRecharge) {
		//订单余额
		if(ConvertUtil.isEmpty(customerRecharge.getOrderBalance())){
			customerRecharge.setOrderBalance(BigDecimal.ZERO);
		}
		//退货金额
		if(ConvertUtil.isEmpty(customerRecharge.getReturnBalance())){
			customerRecharge.setReturnBalance(BigDecimal.ZERO);
		}
		//发货金额
		if(ConvertUtil.isEmpty(customerRecharge.getShipmentBalance())){
			customerRecharge.setShipmentBalance(BigDecimal.ZERO);
		}
		//客户充值金额 
		if(ConvertUtil.isEmpty(customerRecharge.getBalance())){
			customerRecharge.setBalance(BigDecimal.ZERO);
		}
		//政策金额
		if(ConvertUtil.isEmpty(customerRecharge.getPolicyBalance())){
			customerRecharge.setPolicyBalance(BigDecimal.ZERO);
		}
		//授信金额
		if(ConvertUtil.isEmpty(customerRecharge.getCreditBalance())){
			customerRecharge.setCreditBalance(BigDecimal.ZERO);
		}
		return customerRecharge;
	}

	
	
	@Override
	public CustomerRecharge saveOrUpdate(CustomerRecharge customerRecharge) {
		//客户
		if(ConvertUtil.isEmpty(customerRecharge.getStore())){
			ExceptionUtil.throwServiceException("客户不能为空");
		}
		//sbu
		if(ConvertUtil.isEmpty(customerRecharge.getSbu())){
			throw new RuntimeException("sbu不能为空");
		}
		//经营组织
		if(ConvertUtil.isEmpty(customerRecharge.getOrganization()) || 
				(!ConvertUtil.isEmpty(customerRecharge.getOrganization()) && 
						ConvertUtil.isEmpty(customerRecharge.getOrganization().getId()))){
			ExceptionUtil.throwServiceException("经营组织不能为空");
		}
		Long companyInfoId = 9L;
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("store",customerRecharge.getStore().getId()));
		filters.add(Filter.eq("organization", customerRecharge.getOrganization().getId()));
		filters.add(Filter.eq("sbu", customerRecharge.getSbu().getId()));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		List<CustomerRecharge> customerRechargeList = findList(null,filters,null);
		if(customerRechargeList.isEmpty() || customerRechargeList.size()==0){
			//初始化参数
			customerRecharge = this.checkParamIsNotNull(customerRecharge);
			save(customerRecharge);
		}else if(!customerRechargeList.isEmpty() && customerRechargeList.size()==1){
			customerRecharge = customerRechargeList.get(0);
		}else{
			ExceptionUtil.throwServiceException("客户充值数据查询返回多条异常，请管理员进行维护");
		}
		return customerRecharge;
	}

	@Override
	public List<Map<String, Object>> findCustomerRechargeList(Long storeId,Long organizationId,Long sbuId) {
		
		return customerRechargeDao.findCustomerRechargeList(storeId, organizationId, sbuId);
	}

}
