package net.shopxx.member.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;

/**
 * Service - 会员等级
 * 
 */
@Service("pcUserRoleBaseServiceImpl")
public class PcUserRoleBaseServiceImpl extends BaseServiceImpl<PcUserRole> implements PcUserRoleBaseService {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	@Override
	public String findRole(Long storeMemberId) {
		String str = "";
		StoreMember storeMember = storeMemberBaseService.find(storeMemberId);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
		List<PcUserRole> pur = this.findList(null, filters, null);
		if (pur != null && pur.size() > 0) {
			for (PcUserRole p : pur) {
				if (p.getPcRole() != null) {
					String name = p.getPcRole().getName();
					str += name;
				}
			}
		}
		return str;
	}

}