package net.shopxx.member.service.impl;

import java.util.*;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.dao.StoreMemberSaleOrgPostDao;
import net.shopxx.member.entity.Post;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSaleOrgPost;
import net.shopxx.member.service.PostBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;

@Service("storeMemberSaleOrgPostServiceImpl")
public class StoreMemberSaleOrgPostServiceImpl extends BaseServiceImpl<StoreMemberSaleOrgPost>
		implements StoreMemberSaleOrgPostService {

	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "postBaseServiceImpl")
	private PostBaseService postBaseService;
	@Resource(name = "storeMemberSaleOrgPostDao")
	private StoreMemberSaleOrgPostDao storeMemberSaleOrgPostDao;
	
	
	/**
	 * 根据登陆用户检查用户是否具有某个岗位
	 */
	@Override
	@Transactional
	public Boolean checkPostByStoreMember(StoreMember storeMember,String postNames) {
		Boolean flag = false;
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember",storeMember));

		//获取用户绑定的机构
		List<StoreMemberSaleOrg> list = storeMemberSaleOrgBaseService.findList(null, filters, null);
		Set<Long> saleOrgIds = new HashSet<Long>();
		for(StoreMemberSaleOrg storeMemberSaleOrg:list){
			SaleOrg saleOrg = storeMemberSaleOrg.getSaleOrg();
			Long saleOrgId = saleOrg.getId();
			saleOrgIds.add(saleOrgId);
		}
		//查找岗位
		filters.clear();
		filters.add(Filter.eq("companyInfoId", storeMember.getCompanyInfoId()));
		filters.add(Filter.like("name","%"+postNames+"%"));
		List<Post> postList = postBaseService.findList(null, filters, null);

		if(postList.isEmpty()) {
			ExceptionUtil.throwServiceException("岗位不存在！");
		}else if (postList.size()>1){
			ExceptionUtil.throwServiceException(postNames+"岗位重复！");
		}

		//通过用户查找StoreMemberSaleOrgPost
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMember));
		List<StoreMemberSaleOrgPost> storeMemberSaleOrgPosts = findList(null, filters, null);

		//遍历storeMemberSaleOrgPosts，校验post和saleOrg是否和storeMemberSaleOrgPost一致
		for(StoreMemberSaleOrgPost storeMemberSaleOrgPost:storeMemberSaleOrgPosts){
			for(Post post:postList){//校验post
				if(storeMemberSaleOrgPost.getPost() == post){
					for(Long saleOrgId:saleOrgIds){//校验saleOrg
						if(storeMemberSaleOrgPost.getSaleOrg().getId() == saleOrgId){
							flag = true;
						}
					}
				}
			}
		}
		return flag;
	}
	
	@Override
	public Boolean checkPostCodeByStoreMember(StoreMember storeMember, String postCode) {
		return storeMemberSaleOrgPostDao.checkPostCodeByStoreMember(storeMember, postCode);
	}

	@Override
	public List<StoreMember> findPostCodeByStoreMember(Long saleOrgId, String postCode) {
		return storeMemberSaleOrgPostDao.findPostCodeByStoreMember(saleOrgId,postCode);
	}

    @Override
    public Page<Map<String, Object>> findPageByPost(Post post, Long saleOrgId, Pageable pageable) {
        return storeMemberSaleOrgPostDao.findPageByPost(post.getId(),saleOrgId,pageable);
    }

	@Override
	public Page<Map<String, Object>> findOrderManager(String[] postCode, String name,Pageable pageable) {
		return storeMemberSaleOrgPostDao.findOrderManager(postCode, name,pageable);
	}

	@Override
	public Page<Map<String, Object>> findStoreMemberByPost(String[] postcode, String name, SaleOrg saleOrg,
														   Pageable pageable) {
		return storeMemberSaleOrgPostDao.findStoreMemberByPost(postcode,name,saleOrg,pageable);
	}
}
