package net.shopxx.member.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.member.dao.SaleOrgCreditDao;
import net.shopxx.member.entity.SaleOrgCredit;
import net.shopxx.member.entity.SaleOrgCreditItem;
import net.shopxx.member.service.SaleOrgCreditService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("saleOrgCreditServiceImpl")
public class SaleOrgCreditServiceImpl extends WfBillBaseServiceImpl<SaleOrgCredit> implements SaleOrgCreditService {

	@Resource(name = "saleOrgCreditDao")
	private SaleOrgCreditDao saleOrgCreditDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "lockDataDao")
	private LockDataDao lockDataDao;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;

	/**
	 * 平台授信列表数据
	 */
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Integer type, String sn, Long storeMemberId, Long storeId,
			Integer[] status, Long creatorId, Long operatorId,Long sbuId, Pageable pageable) {
		return saleOrgCreditDao.findPage(sn, storeMemberId, storeId, status, creatorId, operatorId,sbuId, pageable);
	}

	@Override
	@Transactional
	public void check(SaleOrgCredit saleOrgCredit, Integer flag, String note, BigDecimal totalAmount) {
		saleOrgCredit.setNote(note);
		saleOrgCredit.setCheckDate(new Date());
		saleOrgCredit.setOperator(storeMemberService.getCurrent());
		if (flag == 2) {
			saleOrgCredit.setStatus(2); // 审核未通过
			saleOrgCredit.setDocStatus(3);
		} else if (flag == 1) {
			saleOrgCredit.setStatus(1); // 审核通过
			saleOrgCredit.setDocStatus(2);
			if (totalAmount == null) {
				totalAmount = saleOrgCredit.getTotalAmount();
			}
			saleOrgCredit.setTotalAmount(totalAmount);
			Date startDate = saleOrgCredit.getStartDate();
			Date endDate = DateUtils.addDays(saleOrgCredit.getEndDate(), 1);
			Date nowDate = new Date();
			if (startDate != null && endDate != null) {
				if (nowDate.compareTo(startDate) >= 0 && nowDate.compareTo(endDate) < 0) {
					saleOrgCredit.setType(1);
				}
			}
		}
		update(saleOrgCredit);

		orderFullLinkService.addFullLink(18, null, saleOrgCredit.getSn(),
				ConvertUtil.convertI18nMsg("18702", new Object[] { "平台授信" }), null);

	}

	@Override
	@Transactional
	public void checkSaleOrgCreditWf(SaleOrgCredit saleOrgCredit, Integer flag, String note, BigDecimal totalAmount,
			Long objConfId) {
		SaleOrgCredit sc = find(saleOrgCredit.getId());
		sc.setNote(note);
		if (totalAmount == null) {
			totalAmount = saleOrgCredit.getTotalAmount();
		}
		sc.setTotalAmount(totalAmount);
		sc.setStartDate(saleOrgCredit.getStartDate());
		sc.setEndDate(saleOrgCredit.getEndDate());
		// 创建流程实例
		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}
		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());
		sc.setByObjConfig(wfObjConfigLine); // 设置流程配置
		wfBaseService.createwf(storeMemberService.getCurrent(), null, sc.getSn(), sc.getWfTempId(), sc.getObjTypeId(),
				sc.getId());

		sc.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.作废\
		this.update(sc);
		orderFullLinkService.addFullLink(18, null, sc.getSn(),
				ConvertUtil.convertI18nMsg("18701", new Object[] { wfTemp.getWfTempName() }), null);
	}

	@Override
	public void agreeBack(Wf wf) {

		if (wf.getStat().intValue() == 2) {
			SaleOrgCredit saleOrgCredit = find(wf.getObjId());
			Date startDate = saleOrgCredit.getStartDate();
			Date endDate = DateUtils.addDays(saleOrgCredit.getEndDate(), 1);
			Date nowDate = new Date();
			if (startDate != null && endDate != null) {
				if (nowDate.compareTo(startDate) >= 0 && nowDate.compareTo(endDate) < 0) {
					saleOrgCredit.setType(1);
				}
			}
			saleOrgCredit.setCheckDate(new Date());
			saleOrgCredit.setOperator(storeMemberService.getCurrent());
			saleOrgCredit.setStatus(1); // 审核通过
			saleOrgCredit.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
											// 2.已处理(流程走完) 3.作废
			this.update(saleOrgCredit);

		}
	}

	@Override
	public void startBack(Wf wf) {
		wf.setStore(storeMemberService.getCurrent().getStore());
		wfBaseService.update(wf);
	}

	@Override
	public void interruptBack(Wf wf) {
		SaleOrgCredit saleOrgCredit = find(wf.getObjId());
		saleOrgCredit.setDocStatus(0); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
										// 2.已处理(流程走完) 3.作废
		update(saleOrgCredit);
		orderFullLinkService.addFullLink(19, null, saleOrgCredit.getSn(), ConvertUtil.convertI18nMsg("平台授信"), null);
	}

	public List<Map<String, Object>> findListBySaleOrg(Long saleOrgId) {
		return saleOrgCreditDao.findListBySaleOrg(saleOrgId);
	}

	@Override
	public List<Map<String, Object>> findScItemList(Long saleOrgId, String saleOrgCreditIds, Boolean isDefault) {

		return saleOrgCreditDao.findScItemList(saleOrgId, saleOrgCreditIds, isDefault);
	}

	@Override
	public List<Map<String, Object>> checkScItemList(Long saleOrgId, Long saleOrgCreditId) {

		return saleOrgCreditDao.checkScItemList(saleOrgId, saleOrgCreditId);
	}

	@Override
	public void saveOrUpdateSaleOrgCredit(SaleOrgCredit saleOrgCredit) {
		List<SaleOrgCreditItem> saleOrgCreditItems = saleOrgCredit.getSaleOrgCreditItems();
		for (Iterator<SaleOrgCreditItem> iterator = saleOrgCreditItems.iterator(); iterator.hasNext();) {
			SaleOrgCreditItem saleOrgCreditItem = iterator.next();
			if (saleOrgCreditItem == null || saleOrgCreditItem.getSaleOrg() == null) {
				iterator.remove();
				continue;
			}
			SaleOrg saleOrg = saleOrgService.find(saleOrgCreditItem.getSaleOrg().getId());
			if (saleOrg == null) {
				ExceptionUtil.throwServiceException("机构不能为空");
			}
			saleOrgCreditItem.setSaleOrgCredit(saleOrgCredit);
		}
		if (saleOrgCreditItems.isEmpty()) {
			ExceptionUtil.throwServiceException("请维护平台授信明细");
		}
		saleOrgCredit.setStatus(0);
		saleOrgCredit.setDocStatus(0);// 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完)
										// 3.作废
		saleOrgCredit.setType(0);
		saleOrgCredit.setCreator(storeMemberService.getCurrent());
		saleOrgCredit.setSn(SnUtil.generateSn());
		if (saleOrgCredit.getId() == null) {
			this.save(saleOrgCredit);
			orderFullLinkService.addFullLink(18, null, saleOrgCredit.getSn(),
					ConvertUtil.convertI18nMsg("18700", new Object[] { "平台授信额度分配" }), null);
		} else {
			this.update(saleOrgCredit, "sn");
			SaleOrgCredit pHead = find(saleOrgCredit.getId());
			orderFullLinkService.addFullLink(18, null, pHead.getSn(), "修改平台授信额度分配", null);
		}

	}

	@Override
	public List<Map<String, Object>> findVSaleOrgCreditList(Long saleOrgId) {
		return saleOrgCreditDao.findVSaleOrgCreditList(saleOrgId);
	}

	@Override
	public List<Map<String, Object>> findVSaleOrgCreditCheckList(Long saleOrgId, 
			Long sbuId, Long organizationId) {
		return saleOrgCreditDao.findVSaleOrgCreditCheckList(saleOrgId, 
				sbuId, organizationId);
	}

	@Override
	public void checkSaleOrgCreditStatus(SaleOrgCredit saleOrgCredit, Integer status, String statusName,
			String operationName) {
		if(ConvertUtil.isEmpty(saleOrgCredit)){
        	ExceptionUtil.throwServiceException("该平台授信单不存在");
        }
        if(!ConvertUtil.isEmpty(saleOrgCredit.getDocStatus()) && saleOrgCredit.getDocStatus() != status){
        	ExceptionUtil.throwServiceException("只有单据状态为"+statusName+"的平台授信单才能"+operationName);
        }
		
	}
}
