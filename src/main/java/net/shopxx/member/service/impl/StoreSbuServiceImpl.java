package net.shopxx.member.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.member.dao.StoreSbuDao;
import net.shopxx.member.entity.StoreSbu;
import net.shopxx.member.service.StoreSbuService;

import org.springframework.stereotype.Service;

@Service("storeSbuServiceImpl")
public class StoreSbuServiceImpl extends BaseServiceImpl<StoreSbu> implements
		StoreSbuService {
	@Resource(name = "storeSbuDao")
	private StoreSbuDao storeSbuDao;

	@Override
	public List<Map<String, Object>> findStoreSbu(Long id) {
		return storeSbuDao.findStoreSbu(id);
	}

}
