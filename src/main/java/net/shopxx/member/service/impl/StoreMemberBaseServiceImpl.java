package net.shopxx.member.service.impl;
import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.member.entity.*;
import net.shopxx.member.service.*;
import net.shopxx.util.CommonUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.entity.Principal;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.dao.MemberBaseDao;
import net.shopxx.member.dao.StoreMemberBaseDao;
import net.shopxx.member.entity.StoreMember.Gender;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.service.WarehouseBaseService;

/**
 * Service - 会员
 */
@Service("storeMemberBaseServiceImpl")
public class StoreMemberBaseServiceImpl extends BaseServiceImpl<StoreMember>
		implements StoreMemberBaseService {

	@Resource(name = "storeMemberBaseDao")
	private StoreMemberBaseDao storeMemberBaseDao;
	@Resource(name = "memberBaseDao")
	private MemberBaseDao memberBaseDao;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "memberBaseServiceImpl")
	private MemberBaseService memberBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "pcRoleBaseServiceImpl")
	private PcRoleBaseService pcRoleBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "smWarehouseBaseServiceImpl")
	private SmWarehouseBaseService smWarehouseBaseService;
	@Resource(name = "warehouseBaseServiceImpl")
	private WarehouseBaseService warehouseBaseService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "storeMemberSaleOrgPostServiceImpl")
	private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
	@Resource(name = "productCategoryBaseServiceImpl")
	private ProductCategoryBaseService productCategoryBaseService;
	@Resource(name = "partnerCategoryBaseServiceImpl")
	private PartnerCategoryBaseService partnerCategoryBaseService;
	@Resource(name = "postBaseServiceImpl")
	private PostBaseService postBaseService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "storeMemberShopInfoServiceImpl")
	private StoreMemberShopInfoService storeMemberShopInfoService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "storeMemberShopPostPostServiceImpl")
	private StoreMemberShopPostService storeMemberShopPostService;
	@Resource(name = "loginOutBaseServiceImpl")
	private LoginOutBaseService loginOutBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "pwdModificationRecordServiceImpl")
	private PwdModificationRecordService pwdModificationRecordService;
	/*@Resource(name = "amStoreMemberToServiceImpl")
	private AmStoreMemberToService amStoreMemberToService;*/
	
	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

	@Override
	@Transactional(readOnly = true)
	public StoreMember getCurrent() {

		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		StoreMember storeMember = find(storeMemberId);
		return storeMember;
	}

	@Override
	@Transactional(readOnly = true)
	public List<StoreMember> findDefaultByUsername(String username, Long companyInfoId) {
		return storeMemberBaseDao.findDefaultByUsername(username, companyInfoId);
	}
	
	@Override
	@Transactional(readOnly = true)
	public StoreMember findByUsername(String username, Long companyInfoId) {
		return storeMemberBaseDao.findByUsername(username, companyInfoId);
	}

	@Override
	@Transactional(readOnly = true)
	public StoreMember findDefaultByMember(Member member) {

		if (member == null) return null;
		StoreMember storeMember = null;
		Principal principal = WebUtils.getPrincipal();
		if (principal != null) {
			storeMember = storeMemberBaseDao.findDefaultByMember(member.getId(),
					principal.getCompanyinfoid());
		}
		return storeMember;
	}

	@Override
	@Transactional(readOnly = true)
	public List<StoreMember> findNotDefaultByMember(Member member) {

		if (member == null) return null;
		List<StoreMember> storeMembers = null;
		Principal principal = WebUtils.getPrincipal();
		if (principal != null) {
			storeMembers = storeMemberBaseDao.findNotDefaultByMember(member.getId(),
					principal.getCompanyinfoid());
		}
		return storeMembers;
	}

	@Override
	@Transactional(readOnly = true)
	public StoreMember findByMemberAndCompanyInfoId(Member member,
			Long companyInfoId) {
		return storeMemberBaseDao.findDefaultByMember(member.getId(),
				companyInfoId);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findStoreMemberByMember(Long memberId) {
		return storeMemberBaseDao.findStoreMemberByMember(memberId);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findSbu(Long id) {
		return storeMemberBaseDao.findSbu(id);
	}
	
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findShop(Long id) {
		return storeMemberBaseDao.findShop(id);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findSbuTy(Long id) {
		return storeMemberBaseDao.findSbuTy(id);
	}

	@Override
	@Transactional(readOnly = true)
	public String generateCode() {
		String sn = null;
		do {
			sn = randNum(8);
		}
		while (count(Filter.eq("inviteCode", sn)) > 0);

		return sn.toString();
	}

	@Override
	@Transactional
	public Long saveStoreMember(StoreMember storeMember, Member member,
			Long[] storeId, Long[] roleId, Long[] saleOrgId, String[] postId,
			Integer[] isDefault, Long[] warehouseId, Long[] sbuId,
			Integer[] isDefaults, Boolean isAdministrator,
			Long[] shopId,String[] postIds,HttpServletRequest request,
			Long[] organizationId,Integer[] isDeFaults) {

		// 保存用户
		Member m = memberBaseService.findByMobile(member.getMobile());
		if (m != null) {
			// 手机号已存在
			ExceptionUtil.throwServiceException("1601006");
			member = m;
		}else {
			member.setIsLocked(false);
			member.setLoginFailureCount(0);
			member.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
			member.setUsername(Sequence.getInstance().getSequence(null));
			member.setIdCard(storeMember.getIdCard());
			memberBaseService.save(member);
		}

		List<StoreMember> sm = storeMemberBaseDao.findDefaultByUsername(storeMember.getUsername(),
				9L);
		if (sm.size()>0) {
			// 用户已存在
			ExceptionUtil.throwServiceException("1601010");
		}

		storeMember.setCompanyInfoId(9L);
		// 保存用户
		storeMember.setMember(member);
		storeMember.setBalance(new BigDecimal(0));
		storeMember.setStore(storeBaseService.getMainStore());
		storeMember.setIsEnabled(true);
		storeMember.setInviteCode(generateCode());
		storeMember.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
		storeMember.setPayPassword(DigestUtils.md5Hex(storeMember.getPassword()));
		storeMember.setLoginIp(null);
		storeMember.setLoginDate(null);
		if (storeMemberBaseDao.existsIsDefaultStoreMember(member.getId())) {
			storeMember.setIsDefault(false);
		}
		else {
			storeMember.setIsDefault(true);
		}
		if(storeMember.getParent()==null||storeMember.getParent().getId()==null)
			storeMember.setParent(null);
		save(storeMember);
		
		//保存报文
		/*amStoreMemberToService.pushAmStoreMember(storeMember);*/
		
		storeSaveStoremember(storeMember,storeId,null);

		// 保存用户组织关联关系
		saveStoreMemberOfSaleOrg(saleOrgId, isDefault, storeMember);

		// 保存用户组织岗位关联关系
		saveStoreMemberOfSaleOrg(postId, storeMember);

		// 保存选择的客户
		saveSelectedStore(storeId, storeMember, member);

		// 保存用户角色
		saveUserRole(roleId, member, storeMember, isAdministrator);

		// 保存用户仓库关系
		saveSmWarehouse(warehouseId, storeMember);

		// 保存用户sbu关系
		saveSmSbu(sbuId, isDefaults, storeMember);
		
		
		// 保存用户经营组织关系
		saveSmOrganization(organizationId, isDeFaults, storeMember);
		
		// 保存用户门店关系
		saveSmShop(shopId, storeMember);

		// 保存用户门店岗位关系
		saveSmShopPost(postIds, storeMember);
		
		return storeMember.getId();
	}

	@Transactional
	public void updateStoreMember(StoreMember storeMember, Member member,
			Long[] storeId, Long[] roleId, Long[] saleOrgId, String[] postId,
			Integer[] isDefault, Long[] warehouseId, Boolean isAdministrator,
			Long pc_id, Long parentId, Long[] sbuId, Integer[] isDefaults,
			Long[] shopId,String[] postIds,HttpServletRequest request,
			Long[] organizationId,Integer[] isDeFaults) {

		StoreMember pStoreMember = this.find(storeMember.getId());
		Long[] s_ = null;
		if(storeMember.getIsSalesman()!=null&&storeMember.getIsSalesman()){
			s_ = storeMemberBaseDao.findListManageStore(pStoreMember.getMember().getId());
		}
		if(storeMember.getParent()==null||storeMember.getParent().getId()==null)
			storeMember.setParent(null);
		Member pMember = pStoreMember.getMember();
		MemberRank memberRank = memberRankBaseService.findDefault(pStoreMember.getCompanyInfoId());
		if (memberRank == null) {
			ExceptionUtil.throwServiceException("价格类型没有默认启用");
		}
		PartnerCategory partnerCategory = null;
		if (storeMember.getIsPartner() != null && storeMember.getIsPartner()) {
			if (pc_id == null) {
				// 合伙人必须选择合伙人类型
				ExceptionUtil.throwServiceException("1601008");
			}
			partnerCategory = partnerCategoryBaseService.find(pc_id);
		}
		StoreMember recommend = this.find(parentId);
		if (pStoreMember.equals(recommend)) {
			// 不能设置自己为上级合伙人
			ExceptionUtil.throwServiceException("1601009");
		}
		//密码修改记录
        PwdModificationRecord pwdRecord = null;

		if (StringUtils.isEmpty(storeMember.getPassword())) {
			member.setPassword(pStoreMember.getPassword());
			storeMember.setPassword(pStoreMember.getPassword());
			storeMember.setPayPassword(pStoreMember.getPayPassword());
		} else {
			/**正则判断密码是否为数字或字母组成的6-16位数**/
			// 字符串是否与正则表达式相匹配
			//String passRegex = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$";
            String passRegex = "^(?![0-9]+$)(?![a-zA-Z]+$)(?![0-9a-zA-Z]+$)(?![0-9\\W]+$)(?![a-zA-Z\\W]+$)[0-9A-Za-z\\W]{6,16}$";
            Boolean checkReult = CommonUtil.regularMatching(passRegex,storeMember.getPassword());
			if(storeMember.getPassword().length()<6 || !checkReult){
				ExceptionUtil.throwServiceException("密码必须由数字、字母和特殊符号组成的6-16位!");
			}
			member.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
			storeMember.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
			storeMember.setPayPassword(DigestUtils.md5Hex(storeMember.getPassword()));
			//密码不一致 添加密码修改记录
            if (!pStoreMember.getPassword().equals(DigestUtils.md5Hex(storeMember.getPassword()))){
                pwdRecord = new PwdModificationRecord();
                pwdRecord.setStoreMember(pStoreMember);
                pwdRecord.setUsername(pStoreMember.getUsername());
                pwdRecord.setPwd(DigestUtils.md5Hex(storeMember.getPassword()));
                pwdRecord.setRevisionSource(2);
            }
		}
		Member m = memberBaseService.findByMobile(member.getMobile());
		if (m != null) {
			if (this.storeMemberExists(m.getId(), storeMember.getId())) {
				// 手机号已存在
				ExceptionUtil.throwServiceException("1601006");
			}
			pMember = m;
		}
		else {
			BeanUtils.copyProperties(member, pMember, new String[] { "id",
					"username",
					"registerIp",
					"loginIp",
					"loginDate",
					"memberOfSaleOrgs" });
		}

		storeMember.setMemberRank(memberRank);
		storeMember.setPartnerCategory(partnerCategory);
		storeMember.setRecommendStoreMember(recommend);
		/**是否活动管理员默认赋值false**/
		if (pStoreMember.getIsActiveAdministrator()==null){
			storeMember.setIsActiveAdministrator(false);
			pStoreMember.setIsActiveAdministrator(false);
		}
		BeanUtils.copyProperties(storeMember,
				pStoreMember,
				new String[] { "username",
						"inviteCode",
						"income",
						"balance",
						"profit",
						"store",
						"member",
						"treePath",
						"isDefault",
						"companyInfoId",
						"storeMemberProductCategorys" });

		// 更新用户
		pMember.setIsLocked(false);
		pMember.setLoginFailureCount(0);
		pMember.setIdCard(storeMember.getIdCard());
		memberBaseService.update(pMember);
		pStoreMember.setMember(pMember);
		
		update(pStoreMember);
		if (pwdRecord!=null){
            pwdModificationRecordService.save(pwdRecord);
        }
        //保存报文
		/*amStoreMemberToService.pushAmStoreMember(pStoreMember);*/
		
		storeSaveStoremember(pStoreMember,storeId,s_);

		// 保存用户组织关联关系
		saveStoreMemberOfSaleOrg(saleOrgId, isDefault, pStoreMember);

		// 保存用户组织岗位关联关系
		saveStoreMemberOfSaleOrg(postId, pStoreMember);

		// 保存选择的客户
		saveSelectedStore(storeId, pStoreMember, pMember);

		// 保存用户角色
		saveUserRole(roleId, pMember, pStoreMember, isAdministrator);

		// 保存用户仓库关系
		saveSmWarehouse(warehouseId, pStoreMember);

		// 保存用户sbu关系
		saveSmSbu(sbuId, isDefaults, pStoreMember);
		
		// 保存用户经营组织
		saveSmOrganization(organizationId, isDeFaults, pStoreMember);
		
		// 保存用户门店关系
		saveSmShop(shopId, storeMember);
		
		// 保存用户门店岗位关系
		saveSmShopPost(postIds, storeMember);

	}
	
	public void storeSaveStoremember(StoreMember sm,Long[] storeId,Long[] s_){
		if(sm.getMemberType()==0&&storeId!=null){
			if(sm.getIsSalesman()&&storeId.length>0){
				for(Long s:storeId){
					Store store_ = storeBaseService.find(s);
					if(store_.getStoreMember()==null){//当客户没有区域经理
						store_.setStoreMember(sm);
						storeBaseService.update(store_);
						deleteManagerStore(store_.getId(),sm.getMember().getId());
					}else if(store_.getStoreMember()==sm){//当客户区域经理和当前的用户相同
						
					}else{//当客户有区域经理替换之前的
						//ExceptionUtil.throwServiceException("客户 '"+store_.getName()+"' 已经绑定对应区域经理不可再次绑定！");
						store_.setStoreMember(sm);
						storeBaseService.update(store_);
						deleteManagerStore(store_.getId(),sm.getMember().getId());
					}
					if(s_ != null){
						for(Long sid : s_){
							if(!Arrays.asList(storeId).contains(sid)){
								Store store = storeBaseService.find(sid);
								store.setStoreMember(null);
								storeBaseService.update(store);
							}
						}
					}
				}
			}
		}
	}
	
	/**
	 * 删除其他区域经理绑定当前客户的数据  
	 */
	public void deleteManagerStore(Long storeId,Long memberId) {
		List<StoreMember> manager = storeMemberBaseDao.findAreaManager(storeId, memberId);
		for(StoreMember m : manager ){
//			System.out.println("删除用户数据："+m.getId());
			delete(m.getId());
		}
	};

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args) {
		return storeMemberBaseDao.findPage(pageable, args);
	}

	/**
	 * 保存用户组织关联关系
	 */
	public void saveStoreMemberOfSaleOrg(Long[] saleOrgId, Integer[] isDefault,
			StoreMember storeMember) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSaleOrg> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findList(null,
				filters,
				null);
		List<SaleOrg> oldSaleOrgs = new ArrayList<SaleOrg>();
		for (StoreMemberSaleOrg storeMemberSaleOrg : storeMemberSaleOrgs) {
			oldSaleOrgs.add(storeMemberSaleOrg.getSaleOrg());
		}
		List<SaleOrg> saleOrgs = new ArrayList<SaleOrg>();
		if (saleOrgId != null && saleOrgId.length > 0) {
			for (Long id : saleOrgId) {
				SaleOrg saleOrg = saleOrgBaseService.find(id);
				saleOrgs.add(saleOrg);
			}
			for (int i = 0; i < saleOrgId.length; i++) {
				SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId[i]);
				if (!oldSaleOrgs.contains(saleOrg)) {
					StoreMemberSaleOrg storeMemberSaleOrg = new StoreMemberSaleOrg();
					storeMemberSaleOrg.setStoreMember(storeMember);
					storeMemberSaleOrg.setSaleOrg(saleOrg);
					if (isDefault[i] == 1) {
						storeMemberSaleOrg.setIsDefault(true);
					}
					else {
						storeMemberSaleOrg.setIsDefault(false);
					}
					storeMemberSaleOrgBaseService.save(storeMemberSaleOrg);
				}
				else {
					filters.clear();
					filters.add(Filter.eq("storeMember", storeMember));
					filters.add(Filter.eq("saleOrg", saleOrg));
					StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgBaseService.find(filters);
					if (isDefault[i] == 1) {
						storeMemberSaleOrg.setIsDefault(true);
					}
					else {
						storeMemberSaleOrg.setIsDefault(false);
					}
					storeMemberSaleOrgBaseService.update(storeMemberSaleOrg);
				}
			}
		}

		for (StoreMemberSaleOrg storeMemberSaleOrg : storeMemberSaleOrgs) {
			SaleOrg saleOrg = storeMemberSaleOrg.getSaleOrg();
			if (!saleOrgs.contains(saleOrg)) {
				storeMemberSaleOrgBaseService.delete(storeMemberSaleOrg);

			}
		}

		// SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		// List<Filter> filters = new ArrayList<Filter>();
		// filters.add(Filter.eq("storeMember", storeMember));
		// List<StoreMemberSaleOrg> storeMemberSaleOrgs =
		// storeMemberSaleOrgBaseService.findList(null,
		// filters,
		// null);
		// StoreMemberSaleOrg storeMemberSaleOrg = null;
		// for (StoreMemberSaleOrg org : storeMemberSaleOrgs) {
		// if (saleOrg != null && org.getSaleOrg().equals(saleOrg)) {
		// storeMemberSaleOrg = org;
		// }
		// else {
		// storeMemberSaleOrgBaseService.delete(org);
		// }
		// }
		// if (storeMemberSaleOrg == null && saleOrg != null) {
		// storeMemberSaleOrg = new StoreMemberSaleOrg();
		// storeMemberSaleOrg.setSaleOrg(saleOrg);
		// storeMemberSaleOrg.setStoreMember(storeMember);
		// storeMemberSaleOrgBaseService.save(storeMemberSaleOrg);
		// }
	}

	/**
	 * 保存用户组织岗位关联关系
	 */
	public void saveStoreMemberOfSaleOrg(String[] postId,
			StoreMember storeMember) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSaleOrgPost> storeMemberSaleOrgPosts = storeMemberSaleOrgPostService.findList(null,
				filters,
				null);
		// List<Post> oldPosts = new ArrayList<Post>();
		List<Map<String, String>> oldSsop = new ArrayList<Map<String, String>>();
		for (StoreMemberSaleOrgPost storeMemberSaleOrgPost : storeMemberSaleOrgPosts) {
			String pId = storeMemberSaleOrgPost.getPost().getId().toString();
			String soId = storeMemberSaleOrgPost.getSaleOrg()
					.getId()
					.toString();
			Map<String, String> map = new HashMap<String, String>();
			map.put(pId, soId);
			oldSsop.add(map);
			// oldPosts.add(storeMemberSaleOrgPost.getPost());
		}
		// List<Post> posts = new ArrayList<Post>();
		List<Map<String, String>> ssop = new ArrayList<Map<String, String>>();
		if (postId != null && postId.length > 0) {

			for (String id : postId) {
				Map<String, String> map = new HashMap<String, String>();
				String pId = id.split("_")[0];
				String soId = id.split("_")[1];
				map.put(pId, soId);
				ssop.add(map);

				if (!oldSsop.contains(map)) {
					StoreMemberSaleOrgPost storeMemberSaleOrgPost = new StoreMemberSaleOrgPost();
					storeMemberSaleOrgPost.setStoreMember(storeMember);
					storeMemberSaleOrgPost.setSaleOrg(saleOrgBaseService.find(Long.parseLong(soId)));
					storeMemberSaleOrgPost.setPost(postBaseService.find(Long.parseLong(pId)));
					storeMemberSaleOrgPostService.save(storeMemberSaleOrgPost);
				}

				// Post post =
				// postBaseService.find(Long.parseLong(id.split("_")[0]));
				// posts.add(post);
			}
			// for (int i = 0; i < postId.length; i++) {
			// String[] id = postId[i].split("_");
			// Post post = postBaseService.find(Long.parseLong(id[0]));
			// if (!oldPosts.contains(post)) {
			// StoreMemberSaleOrgPost storeMemberSaleOrgPost = new
			// StoreMemberSaleOrgPost();
			// storeMemberSaleOrgPost.setStoreMember(storeMember);
			// storeMemberSaleOrgPost.setSaleOrg(saleOrgBaseService.find(Long.parseLong(id[1])));
			// storeMemberSaleOrgPost.setPost(post);
			// storeMemberSaleOrgPostService.save(storeMemberSaleOrgPost);
			// }
			// }
		}

		for (StoreMemberSaleOrgPost storeMemberSaleOrgPost : storeMemberSaleOrgPosts) {
			String pId = storeMemberSaleOrgPost.getPost().getId().toString();
			String soId = storeMemberSaleOrgPost.getSaleOrg()
					.getId()
					.toString();
			Map<String, String> map = new HashMap<String, String>();
			map.put(pId, soId);
			if (!ssop.contains(map)) {
				storeMemberSaleOrgPostService.delete(storeMemberSaleOrgPost);
			}
		}

	}
	
	
	/**
	 * 保存用户组织岗位关联关系
	 */
	public void saveSmShopPost(String[] postId,
			StoreMember storeMember) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberShopPost> storeMemberShopPosts = storeMemberShopPostService.findList(null,
				filters,
				null);
		// List<Post> oldPosts = new ArrayList<Post>();
		List<Map<String, String>> oldSsop = new ArrayList<Map<String, String>>();
		for (StoreMemberShopPost storeMemberShopPost : storeMemberShopPosts) {
			String pId = storeMemberShopPost.getPost().getId().toString();
			String soId = storeMemberShopPost.getShopInfo()
					.getId()
					.toString();
			Map<String, String> map = new HashMap<String, String>();
			map.put(pId, soId);
			oldSsop.add(map);
			// oldPosts.add(storeMemberSaleOrgPost.getPost());
		}
		// List<Post> posts = new ArrayList<Post>();
		List<Map<String, String>> ssop = new ArrayList<Map<String, String>>();
		if (postId != null && postId.length > 0) {

			for (String id : postId) {
				Map<String, String> map = new HashMap<String, String>();
				String pId = id.split("_")[0];
				String soId = id.split("_")[1];
				map.put(pId, soId);
				ssop.add(map);

				if (!oldSsop.contains(map)) {
					StoreMemberShopPost storeMemberShopPost = new StoreMemberShopPost();
					storeMemberShopPost.setStoreMember(storeMember);
					storeMemberShopPost.setShopInfo(shopInfoService.find(Long.parseLong(soId)));
					storeMemberShopPost.setPost(postBaseService.find(Long.parseLong(pId)));
					storeMemberShopPostService.save(storeMemberShopPost);
				}

				// Post post =
				// postBaseService.find(Long.parseLong(id.split("_")[0]));
				// posts.add(post);
			}
			// for (int i = 0; i < postId.length; i++) {
			// String[] id = postId[i].split("_");
			// Post post = postBaseService.find(Long.parseLong(id[0]));
			// if (!oldPosts.contains(post)) {
			// StoreMemberSaleOrgPost storeMemberSaleOrgPost = new
			// StoreMemberSaleOrgPost();
			// storeMemberSaleOrgPost.setStoreMember(storeMember);
			// storeMemberSaleOrgPost.setSaleOrg(saleOrgBaseService.find(Long.parseLong(id[1])));
			// storeMemberSaleOrgPost.setPost(post);
			// storeMemberSaleOrgPostService.save(storeMemberSaleOrgPost);
			// }
			// }
		}

		for (StoreMemberShopPost storeMemberShopPost : storeMemberShopPosts) {
			String pId = storeMemberShopPost.getPost().getId().toString();
			String soId = storeMemberShopPost.getShopInfo()
					.getId()
					.toString();
			Map<String, String> map = new HashMap<String, String>();
			map.put(pId, soId);
			if (!ssop.contains(map)) {
				storeMemberShopPostService.delete(storeMemberShopPost);
			}
		}

	}

	/**
	 * 保存选择的客户
	 */
	public void saveSelectedStore(Long[] storeId, StoreMember storeMember,
			Member member) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("member", member));
		List<StoreMember> storeMembers = findList(null, filters, null);
		List<Store> oldStores = new ArrayList<Store>();
		for (StoreMember oldStoreMember : storeMembers) {
			Store store = oldStoreMember.getStore();
			if(store!=null){
				if (!store.getIsMainStore()) {
					oldStores.add(store);
				}				
			}
		}
		List<Store> newStores = new ArrayList<Store>();
		if (storeId != null && storeId.length > 0) {
			for (Long id : storeId) {
				Store store = storeBaseService.find(id);
				newStores.add(store);
			}
		}
		for (Store newStore : newStores) {
			if (!oldStores.contains(newStore)) {
				StoreMember sm = new StoreMember();
				sm.setBalance(new BigDecimal(0));
				sm.setStore(newStore);
				sm.setIsEnabled(true);
				sm.setMemberRank(storeMember.getMemberRank());
				sm.setIsPartner(storeMember.getIsPartner());
				sm.setRecommendStoreMember(storeMember.getRecommendStoreMember());
				sm.setPartnerCategory(storeMember.getPartnerCategory());
				sm.setInviteCode(generateCode());
				sm.setPassword(storeMember.getPassword());
				sm.setPayPassword(storeMember.getPayPassword());
				sm.setUsername(storeMember.getUsername());
				sm.setName(storeMember.getName());
				sm.setImageName(storeMember.getImageName());
				sm.setBirth(storeMember.getBirth());
				sm.setLoginIp(storeMember.getLoginIp());
				sm.setLoginDate(storeMember.getLoginDate());
				sm.setGender(storeMember.getGender());
				sm.setIsDefault(false);
				sm.setMember(member);
				save(sm);
			}
		}
		for (StoreMember oldStoreMember : storeMembers) {
			Store store = oldStoreMember.getStore();
			if(store!=null){
				if (!store.getIsMainStore() && !newStores.contains(store)) {
					delete(oldStoreMember);
				}			
			}
		}
	}
	
	public void saveSalesmanStore(Long[] storeId, StoreMember storeMember,
			Member member) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("member", member));
		List<StoreMember> storeMembers = findList(null, filters, null);
		List<Store> oldStores = new ArrayList<Store>();
		if (storeMembers!=null && storeMembers.size() > 0){
            for (StoreMember oldStoreMember : storeMembers) {
                Store store = oldStoreMember.getStore();
                if(store!=null){
                    if (!store.getIsMainStore()) {
                        oldStores.add(store);
                    }
                }
            }
        }
		List<Store> newStores = new ArrayList<Store>();
		if (storeId != null && storeId.length > 0) {
			for (Long id : storeId) {
				Store store = storeBaseService.find(id);
				newStores.add(store);
			}
		}
		for (Store newStore : newStores) {
			if (!oldStores.contains(newStore)) {
				StoreMember sm = new StoreMember();
				sm.setBalance(new BigDecimal(0));
				sm.setStore(newStore);
				sm.setIsEnabled(true);
				sm.setMemberRank(storeMember.getMemberRank());
				sm.setIsPartner(storeMember.getIsPartner());
				sm.setRecommendStoreMember(storeMember.getRecommendStoreMember());
				sm.setPartnerCategory(storeMember.getPartnerCategory());
				sm.setInviteCode(generateCode());
				sm.setPassword(storeMember.getPassword());
				sm.setPayPassword(storeMember.getPayPassword());
				sm.setUsername(storeMember.getUsername());
				sm.setName(storeMember.getName());
				sm.setImageName(storeMember.getImageName());
				sm.setBirth(storeMember.getBirth());
				sm.setLoginIp(storeMember.getLoginIp());
				sm.setLoginDate(storeMember.getLoginDate());
				sm.setGender(storeMember.getGender());
				sm.setIsDefault(false);
				sm.setMember(member);
				save(sm);
			}
		}
	}

	/**
	 * 保存用户角色
	 */
	public void saveUserRole(Long[] roleId, Member member,
			StoreMember storeMember, Boolean isAdministrator) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<PcUserRole> nUserRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		List<PcRole> oldRoles = new ArrayList<PcRole>();
		for (PcUserRole userRole : nUserRoles) {
			oldRoles.add(userRole.getPcRole());
		}
		List<PcRole> roles = new ArrayList<PcRole>();
		if (roleId != null && roleId.length > 0) {
			for (Long id : roleId) {
				PcRole pcRole = pcRoleBaseService.find(id);
				roles.add(pcRole);
			}
		}

		for (PcRole role : roles) {
			if (!oldRoles.contains(role)) {
				PcUserRole pcUserRole = new PcUserRole();
				// pcUserRole.setMember(member);
				pcUserRole.setStoreMember(storeMember);
				pcUserRole.setPcRole(role);
				pcUserRole.setCompanyInfoId(storeMember.getCompanyInfoId());
				pcUserRoleBaseService.save(pcUserRole);
			}
		}
		// 是否企业管理员
		// PcRole arole = pcRoleBaseService.findAdministrator();
		// List<Filter> fis = new ArrayList<Filter>();
		// fis.add(Filter.eq("pcRole", arole));
		// fis.add(Filter.eq("storeMember", storeMember));
		// PcUserRole pcUserRole = pcUserRoleBaseService.find(fis);
		// if (isAdministrator != null && isAdministrator) {
		// if (pcUserRole == null) {
		//
		// pcUserRole = new PcUserRole();
		// // pcUserRole.setMember(member);
		// pcUserRole.setStoreMember(storeMember);
		// pcUserRole.setPcRole(arole);
		// pcUserRole.setCompanyInfoId(storeMember.getCompanyInfoId());
		// pcUserRoleBaseService.save(pcUserRole);
		// }
		// } else {
		//
		// if (pcUserRole != null) {
		// pcUserRoleBaseService.delete(pcUserRole);
		// }
		// }
		for (PcUserRole userRole : nUserRoles) {
			if (!roles.contains(userRole.getPcRole())
					&& userRole.getPcRole().getCompanyInfoId() != null) {
				pcUserRoleBaseService.delete(userRole);
			}
		}
	}

	/**
	 * 保存用户SBU
	 */
	public void saveSmSbu(Long[] sbuId, Integer[] isDefaults,
			StoreMember storeMember) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> storeMemberSbus = storeMemberSbuService.findList(null,
				filters,
				null);
		List<Sbu> oldSbus = new ArrayList<Sbu>();
		for (StoreMemberSbu storeMemberSbu : storeMemberSbus) {
			oldSbus.add(storeMemberSbu.getSbu());
		}
		List<Sbu> sbus = new ArrayList<Sbu>();
		if (sbuId != null && sbuId.length > 0) {
			for (Long id : sbuId) {
				Sbu sbu = sbuService.find(id);
				sbus.add(sbu);
			}
			for (int i = 0; i < sbuId.length; i++) {
				Sbu sbu = sbuService.find(sbuId[i]);

				if (!oldSbus.contains(sbu)) {
					StoreMemberSbu storeMemberSbu = new StoreMemberSbu();
					storeMemberSbu.setStoreMember(storeMember);
					storeMemberSbu.setSbu(sbu);
					if (isDefaults[i] == 1) {
						storeMemberSbu.setIsDefault(true);
					}
					else {
						storeMemberSbu.setIsDefault(false);
					}
					storeMemberSbuService.save(storeMemberSbu);
				}
				else {
					filters.clear();
					filters.add(Filter.eq("storeMember", storeMember));
					filters.add(Filter.eq("sbu", sbu));
					StoreMemberSbu storeMemberSbu = storeMemberSbuService.find(filters);

					if (isDefaults[i] == 1) {
						storeMemberSbu.setIsDefault(true);
					}
					else {
						storeMemberSbu.setIsDefault(false);
					}
					storeMemberSbuService.update(storeMemberSbu);
				}
			}
		}

		for (StoreMemberSbu storeMemberSbu : storeMemberSbus) {
			Sbu sbu = storeMemberSbu.getSbu();
			if (!sbus.contains(sbu)) {
				storeMemberSbuService.delete(storeMemberSbu);

			}
		}
	}
	
	
	/**
	 * 保存用户经营组织
	 */
	public void saveSmOrganization(Long[] organizationId, Integer[] isDeFaults,
			StoreMember storeMember) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
		
		List<Organization> organizationList = new ArrayList<Organization>();
		for (StoreMemberOrganization storeMemberOrganization : storeMemberOrganizationList) {
			organizationList.add(storeMemberOrganization.getOrganization());
		}
		
		List<Organization> addOrganization = new ArrayList<Organization>();
		if (organizationId != null && organizationId.length > 0) {
			for (Long id : organizationId) {
				Organization organization = organizationService.find(id);
				addOrganization.add(organization);
			}
			for (int i = 0; i < organizationId.length; i++) {
				Organization organization = organizationService.find(organizationId[i]);
				if (!organizationList.contains(organization)) {
					StoreMemberOrganization storeMemberOrganization = new StoreMemberOrganization();
					storeMemberOrganization.setStoreMember(storeMember);
					storeMemberOrganization.setOrganization(organization);
					if (isDeFaults[i] == 1) {
						storeMemberOrganization.setIsDefault(true);
					}
					else {
						storeMemberOrganization.setIsDefault(false);
					}
					storeMemberOrganization.setModifyDate(new Date());
					storeMemberOrganization.setCreateDate(new Date());
					storeMemberOrganization.setCompanyInfoId(WebUtils.getCurrentCompanyInfoId());
					//用户
					StoreMember storeUserMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
					if(storeUserMember!=null){
						storeMemberOrganization.setbCreater(storeUserMember.getName());
						storeMemberOrganization.setbModifier(storeUserMember.getName());
					}
					storeMemberOrganizationService.save(storeMemberOrganization);
					
				}else {
					filters.clear();
					filters.add(Filter.eq("storeMember", storeMember));
					filters.add(Filter.eq("organization", organization));
					StoreMemberOrganization storeMemberOrganization = storeMemberOrganizationService.find(filters);
					if (isDeFaults[i] == 1) {
						storeMemberOrganization.setIsDefault(true);
					}
					else {
						storeMemberOrganization.setIsDefault(false);
					}
					storeMemberOrganization.setModifyDate(new Date());
					storeMemberOrganization.setCompanyInfoId(WebUtils.getCurrentCompanyInfoId());
					//用户
					StoreMember storeUserMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
					if(storeUserMember!=null){
						storeMemberOrganization.setbModifier(storeUserMember.getName());
					}
					storeMemberOrganizationService.update(storeMemberOrganization);
				}
			}
		}

		for (StoreMemberOrganization storeMemberOrganization : storeMemberOrganizationList) {
			Organization organization = storeMemberOrganization.getOrganization();
			if (!addOrganization.contains(organization)) {
				storeMemberOrganizationService.delete(storeMemberOrganization);
			}
		}
	}
	
	
	/**
	 * 保存用户SBU
	 */
	public void saveSmShop(Long[] shopId,
			StoreMember storeMember) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberShopInfo> storeMemberShopInfos = storeMemberShopInfoService.findList(null,
				filters,
				null);
		List<ShopInfo> oldShops = new ArrayList<ShopInfo>();
		for (StoreMemberShopInfo storeMemberShopInfo : storeMemberShopInfos) {
			oldShops.add(storeMemberShopInfo.getShopInfo());
		}
		List<ShopInfo> shopInfos = new ArrayList<ShopInfo>();
		if (shopId != null && shopId.length > 0) {
			for (Long id : shopId) {
				ShopInfo shopInfo = shopInfoService.find(id);
				shopInfos.add(shopInfo);
			}
			for (int i = 0; i < shopId.length; i++) {
				ShopInfo shopInfo = shopInfoService.find(shopId[i]);

				if (!oldShops.contains(shopInfo)) {
					StoreMemberShopInfo storeMemberShopInfo = new StoreMemberShopInfo();
					storeMemberShopInfo.setStoreMember(storeMember);
					storeMemberShopInfo.setShopInfo(shopInfo);
					
					storeMemberShopInfoService.save(storeMemberShopInfo);
				}
				else {
					filters.clear();
					filters.add(Filter.eq("storeMember", storeMember));
					filters.add(Filter.eq("shopInfo", shopInfo));
					StoreMemberShopInfo storeMemberShopInfo = storeMemberShopInfoService.find(filters);

				
					storeMemberShopInfoService.update(storeMemberShopInfo);
				}
			}
		}

		for (StoreMemberShopInfo storeMemberShopInfo : storeMemberShopInfos) {
			ShopInfo shopInfo = storeMemberShopInfo.getShopInfo();
			if (!shopInfos.contains(shopInfo)) {
				storeMemberShopInfoService.delete(storeMemberShopInfo);

			}
		}
	}

	/**
	 * 保存用户仓库关系
	 */
	public void saveSmWarehouse(Long[] warehouseId, StoreMember storeMember) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<SmWarehouse> smWarehouses = smWarehouseBaseService.findList(null,
				filters,
				null);
		List<Warehouse> oldWarehouses = new ArrayList<Warehouse>();
		for (SmWarehouse smWarehouse : smWarehouses) {
			oldWarehouses.add(smWarehouse.getWarehouse());
		}
		List<Warehouse> warehouses = new ArrayList<Warehouse>();
		if (warehouseId != null && warehouseId.length > 0) {
			for (Long id : warehouseId) {
				Warehouse warehouse = warehouseBaseService.find(id);
				warehouses.add(warehouse);
			}
		}
		for (Warehouse warehouse : warehouses) {
			if (!oldWarehouses.contains(warehouse)) {
				SmWarehouse smWarehouse = new SmWarehouse();
				smWarehouse.setStoreMember(storeMember);
				smWarehouse.setWarehouse(warehouse);
				smWarehouseBaseService.save(smWarehouse);
			}
		}
		for (SmWarehouse smWarehouse : smWarehouses) {
			Warehouse warehouse = smWarehouse.getWarehouse();
			if (!warehouses.contains(warehouse)) {
				smWarehouseBaseService.delete(smWarehouse);
			}
		}
	}

	/**
	 * 根据设定的长度随机生成序列
	 * 
	 * @param len
	 * @return
	 */
	private String randNum(int len) {
		if (len == 0) return null;
		Random random = new Random();
		StringBuilder sn = new StringBuilder();
		for (int i = 0; i < len; i++) {
			sn.append(random.nextInt(10));
		}
		return sn.toString();
	}

	@Override
	@Transactional
	public Map<String, Object> storeMemberImport(MultipartFile multipartFile,
			Integer memberType) throws Exception {

		Map<String, Object> map = new HashMap<String, Object>();
		StringBuilder msg = new StringBuilder();
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}

		if (memberType == 1) {
			String username; // 用户名
			String mobile; // 手机号
			String name; // 姓名
			String password;// 密码
			String stores;// 所属客户名称(多个以;隔开)
			String roles;// 用户角色(多个以;隔开)
			Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
			List<Filter> filters = new ArrayList<Filter>();
			for (int i = 1; i < rows; i++) {

				cell = sheet.getCell(0, i);
				username = cell.getContents().trim();
				if (ConvertUtil.isEmpty(username)) {
					msg.append("第" + (i + 1) + "行，" + "用户名未填<br>");
					continue;
				}
				if (count(Filter.eq("username", username),
						Filter.eq("companyInfoId", companyInfoId)) > 0) {
					msg.append("第"
							+ (i + 1)
							+ "行，"
							+ "用户名为["
							+ username
							+ "]的用户已存在<br>");
					continue;
				}

				cell = sheet.getCell(1, i);
				mobile = cell.getContents().trim();
				if (ConvertUtil.isEmpty(mobile)) {
					msg.append("第" + (i + 1) + "行，" + "手机号未填<br>");
					continue;
				}
				if (memberBaseService.exists(Filter.eq("mobile", mobile),
						Filter.eq("companyInfoId", companyInfoId))) {
					msg.append("第"
							+ (i + 1)
							+ "行，"
							+ "手机号["
							+ mobile
							+ "]已存在<br>");
					continue;
				}

				cell = sheet.getCell(2, i);
				name = cell.getContents().trim();

				cell = sheet.getCell(3, i);
				password = cell.getContents().trim();
				if (ConvertUtil.isEmpty(password)) {
					msg.append("第" + (i + 1) + "行，" + "用户密码未填<br>");
					continue;
				}

				MemberRank memberRank = memberRankBaseService.findDefault(companyInfoId);

				Boolean is_partner = false;

				PartnerCategory partnerCategory = null;

				StoreMember recommendStoreMember = null;
				boolean isOut = false;
				List<Store> storess = new ArrayList<Store>();
				cell = sheet.getCell(4, i);
				stores = cell.getContents().trim();
				if (!ConvertUtil.isEmpty(stores)) {
					String[] storeNames = stores.split(";");
					for (String storeName : storeNames) {
						if (!ConvertUtil.isEmpty(storeName)) {
							filters.clear();
							filters.add(Filter.eq("name", storeName));
							filters.add(Filter.eq("companyInfoId",
									companyInfoId));
							Store store = storeBaseService.find(filters);
							if (store == null) {
								msg.append("第["
										+ (i + 1)
										+ "]行，"
										+ "客户["
										+ storeName
										+ "]不存在<br>");
								// continue;
								isOut = true;
							}
							else {
								storess.add(store);
							}
						}
					}
					if (isOut) {
						continue;
					}
				}

				List<PcRole> pcRoless = new ArrayList<PcRole>();
				cell = sheet.getCell(5, i);
				roles = cell.getContents().trim();
				if (!ConvertUtil.isEmpty(roles)) {
					String[] roleNames = roles.split(";");
					for (String roleName : roleNames) {
						if (!ConvertUtil.isEmpty(roleName)) {
							filters.clear();
							filters.add(Filter.eq("name", roleName));
							filters.add(Filter.eq("companyInfoId",
									companyInfoId));
							PcRole pcRole = pcRoleBaseService.find(filters);
							if (pcRole == null) {
								msg.append("第["
										+ (i + 1)
										+ "]行，"
										+ "角色["
										+ roleName
										+ "]不存在<br>");
								// continue;
								isOut = true;
							}
							else {
								pcRoless.add(pcRole);
							}
						}
					}
					if (isOut) {
						continue;
					}
				}

				// 外部用户新增用户
				outInsertStoreMember(username,
						mobile,
						name,
						password,
						memberRank,
						is_partner,
						partnerCategory,
						recommendStoreMember,
						storess,
						pcRoless,
						memberType);
			}

		}
		else {
			String username; // 用户名
			String mobile; // 手机号
			String name; // 姓名
			String password;// 密码
			String stores;// 所属客户名称(多个以;隔开)
			String roles;// 用户角色(多个以;隔开)
			String warehouses;// 管理仓库(多个以;隔开)
			String saleOrgs;// 管理仓库

			Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
			List<Filter> filters = new ArrayList<Filter>();
			for (int i = 1; i < rows; i++) {

				cell = sheet.getCell(0, i);
				username = cell.getContents().trim();
				if (ConvertUtil.isEmpty(username)) {
					msg.append("第" + (i + 1) + "行，" + "用户名未填<br>");
					continue;
				}
				if (count(Filter.eq("username", username),
						Filter.eq("companyInfoId", companyInfoId)) > 0) {
					msg.append("第"
							+ (i + 1)
							+ "行，"
							+ "用户名为["
							+ username
							+ "]的用户已存在<br>");
					continue;
				}

				cell = sheet.getCell(1, i);
				mobile = cell.getContents().trim();
				if (ConvertUtil.isEmpty(mobile)) {
					msg.append("第" + (i + 1) + "行，" + "手机号未填<br>");
					continue;
				}
				if (memberBaseService.exists(Filter.eq("mobile", mobile),
						Filter.eq("companyInfoId", companyInfoId))) {
					msg.append("第"
							+ (i + 1)
							+ "行，"
							+ "手机号["
							+ mobile
							+ "]已存在<br>");
					continue;
				}

				cell = sheet.getCell(2, i);
				name = cell.getContents().trim();

				cell = sheet.getCell(3, i);
				password = cell.getContents().trim();
				if (ConvertUtil.isEmpty(password)) {
					msg.append("第" + (i + 1) + "行，" + "用户密码未填<br>");
					continue;
				}

				MemberRank memberRank = memberRankBaseService.findDefault(companyInfoId);

				Boolean is_partner = false;

				PartnerCategory partnerCategory = null;

				StoreMember recommendStoreMember = null;
				boolean isOut = false;

				cell = sheet.getCell(4, i);
				saleOrgs = cell.getContents().trim();
				Long[] saleOrgss = null;
				if (!ConvertUtil.isEmpty(saleOrgs)) {
					String[] saleOrgNames = saleOrgs.split(";");
					saleOrgss = new Long[saleOrgNames.length];
					Integer countS = 0;
					for (String saleOrgName : saleOrgNames) {
						if (!ConvertUtil.isEmpty(saleOrgName)) {
							filters.clear();
							filters.add(Filter.eq("name", saleOrgName));
							filters.add(Filter.eq("companyInfoId",
									companyInfoId));
							SaleOrg saleOrg = saleOrgBaseService.find(filters);
							if (saleOrg == null) {
								msg.append("第["
										+ (i + 1)
										+ "]行，"
										+ "机构["
										+ saleOrgName
										+ "]不存在<br>");
								isOut = true;
							}
							else {
								saleOrgss[countS] = saleOrg.getId();
								countS++;
							}
						}
					}
					if (isOut) {
						continue;
					}
				}

				cell = sheet.getCell(5, i);
				warehouses = cell.getContents().trim();
				Long[] warehousess = null;
				if (!ConvertUtil.isEmpty(warehouses)) {
					String[] warehouseNames = warehouses.split(";");
					warehousess = new Long[warehouseNames.length];
					Integer countW = 0;
					for (String warehouseName : warehouseNames) {
						if (!ConvertUtil.isEmpty(warehouseName)) {
							filters.clear();
							filters.add(Filter.eq("name", warehouseName));
							filters.add(Filter.eq("companyInfoId",
									companyInfoId));
							Warehouse warehouse = warehouseBaseService.find(filters);
							if (warehouse == null) {
								msg.append("第["
										+ (i + 1)
										+ "]行，"
										+ "仓库["
										+ warehouseName
										+ "]不存在<br>");
								isOut = true;
							}
							else {
								warehousess[countW] = warehouse.getId();
								countW++;
							}
						}
					}
					if (isOut) {
						continue;
					}
				}

				List<Store> storess = new ArrayList<Store>();
				cell = sheet.getCell(6, i);
				stores = cell.getContents().trim();
				if (!ConvertUtil.isEmpty(stores)) {
					String[] storeNames = stores.split(";");
					for (String storeName : storeNames) {
						if (!ConvertUtil.isEmpty(storeName)) {
							filters.clear();
							filters.add(Filter.eq("name", storeName));
							filters.add(Filter.eq("companyInfoId",
									companyInfoId));
							Store store = storeBaseService.find(filters);
							if (store == null) {
								msg.append("第["
										+ (i + 1)
										+ "]行，"
										+ "客户["
										+ storeName
										+ "]不存在<br>");
								// continue;
								isOut = true;
							}
							else {
								storess.add(store);
							}
						}
					}
					if (isOut) {
						continue;
					}
				}

				List<PcRole> pcRoless = new ArrayList<PcRole>();
				cell = sheet.getCell(7, i);
				roles = cell.getContents().trim();
				if (!ConvertUtil.isEmpty(roles)) {
					String[] roleNames = roles.split(";");
					for (String roleName : roleNames) {
						if (!ConvertUtil.isEmpty(roleName)) {
							filters.clear();
							filters.add(Filter.eq("name", roleName));
							filters.add(Filter.eq("companyInfoId",
									companyInfoId));
							PcRole pcRole = pcRoleBaseService.find(filters);
							if (pcRole == null) {
								msg.append("第["
										+ (i + 1)
										+ "]行，"
										+ "角色["
										+ roleName
										+ "]不存在<br>");
								// continue;
								isOut = true;
							}
							else {
								pcRoless.add(pcRole);
							}
						}
					}
					if (isOut) {
						continue;
					}
				}

				// 新增用户
				insertStoreMember(username,
						mobile,
						name,
						password,
						memberRank,
						is_partner,
						partnerCategory,
						recommendStoreMember,
						storess,
						pcRoless,
						warehousess,
						saleOrgss,
						memberType);
			}

			success++;
		}
		int result = rows - 1;

		map.put("result", result);
		map.put("success", success);
		map.put("msg", msg.toString());

		return map;
	}

	/**
	 * 新增用户
	 */
	public void insertStoreMember(String username, String mobile, String name,
			String password, MemberRank memberRank, Boolean is_partner,
			PartnerCategory partnerCategory, StoreMember recommendStoreMember,
			List<Store> storess, List<PcRole> pcRoless, Long[] warehousess,
			Long[] saleOrgss, Integer memberType) {

		password = DigestUtils.md5Hex(password);

		Member member = null;
		// Member pMember = memberBaseService.findByUsername(username);
		Member m = memberBaseService.findByMobile(mobile);
		if (m != null) {
			member = m;
		}
		// List<Filter> fis = new ArrayList<Filter>();
		// fis.add(Filter.eq("mobile", mobile));
		// List<Member> members = memberBaseService.findList(null, fis, null);
		// if (members != null && members.size() > 0) {
		// member = members.get(0);
		// member.setIsLocked(false);
		// member.setLoginFailureCount(0);
		// memberBaseService.update(members.get(0));
		// }
		else {
			member = new Member();
			member.setMobile(mobile);
			member.setUsername(username);
			member.setPassword(password);
			member.setIsLocked(false);
			member.setLoginFailureCount(0);
			member.setLockedDate(null);
			memberBaseService.save(member);
		}

		StoreMember storeMember = new StoreMember();
		storeMember.setBalance(new BigDecimal(0));
		storeMember.setStore(storeBaseService.getMainStore());
		storeMember.setIsEnabled(true);
		storeMember.setMemberRank(memberRank);
		storeMember.setIsPartner(is_partner);
		storeMember.setPartnerCategory(partnerCategory);
		storeMember.setRecommendStoreMember(recommendStoreMember);
		storeMember.setInviteCode(generateCode());
		storeMember.setPassword(password);
		storeMember.setPayPassword(password);
		storeMember.setUsername(username);
		storeMember.setName(name);
		storeMember.setLoginIp("127.0.0.1");
		storeMember.setLoginDate(null);
		if (storeMemberBaseDao.existsIsDefaultStoreMember(member.getId())) {
			storeMember.setIsDefault(false);
		}
		else {
			storeMember.setIsDefault(true);
		}
		storeMember.setMember(member);
		storeMember.setMemberType(memberType == null ? 0 : memberType);
		save(storeMember);

		for (Store store : storess) {
			StoreMember sm = new StoreMember();
			sm.setBalance(new BigDecimal(0));
			sm.setStore(store);
			sm.setIsEnabled(true);
			sm.setMemberRank(memberRank);
			sm.setIsPartner(is_partner);
			sm.setPartnerCategory(partnerCategory);
			sm.setRecommendStoreMember(recommendStoreMember);
			sm.setInviteCode(generateCode());
			sm.setPassword(password);
			sm.setPayPassword(password);
			sm.setUsername(username);
			sm.setName(name);
			sm.setLoginIp("127.0.0.1");
			sm.setLoginDate(null);
			sm.setIsDefault(false);
			sm.setMember(member);
			sm.setMemberType(memberType == null ? 0 : memberType);
			save(sm);
		}
		if (warehousess != null && warehousess.length > 0) {
			saveSmWarehouse(warehousess, storeMember);
		}
		if (saleOrgss != null && saleOrgss.length > 0) {
			Integer[] isDefault = new Integer[saleOrgss.length];
			isDefault[0] = 1;
			for (int i = 1; i < isDefault.length; i++) {
				isDefault[i] = 0;
			}
			// 保存用户组织关联关系
			saveStoreMemberOfSaleOrg(saleOrgss, isDefault, storeMember);
		}
		Long[] pcRoleId = new Long[pcRoless.size()];
		for (int i = 0; i < pcRoless.size(); i++) {
			pcRoleId[i] = pcRoless.get(i).getId();
		}
		saveUserRole(pcRoleId, member, storeMember, false);
		memberBaseDao.getEntityManager().flush();
		storeMemberBaseDao.getEntityManager().flush();
	}

	/**
	 * 外部用户新增用户
	 */
	public void outInsertStoreMember(String username, String mobile,
			String name, String password, MemberRank memberRank,
			Boolean is_partner, PartnerCategory partnerCategory,
			StoreMember recommendStoreMember, List<Store> storess,
			List<PcRole> pcRoless, Integer memberType) {

		password = DigestUtils.md5Hex(password);

		Member member = null;
		Member m = memberBaseService.findByMobile(mobile);
		if (m != null) {
			member = m;
		}
		else {
			member = new Member();
			member.setMobile(mobile);
			member.setUsername(username);
			member.setPassword(password);
			member.setIsLocked(false);
			member.setLoginFailureCount(0);
			member.setLockedDate(null);
			memberBaseService.save(member);
		}

		StoreMember storeMember = new StoreMember();
		storeMember.setBalance(new BigDecimal(0));
		storeMember.setStore(storeBaseService.getMainStore());
		storeMember.setIsEnabled(true);
		storeMember.setMemberRank(memberRank);
		storeMember.setIsPartner(is_partner);
		storeMember.setPartnerCategory(partnerCategory);
		storeMember.setRecommendStoreMember(recommendStoreMember);
		storeMember.setInviteCode(generateCode());
		storeMember.setPassword(password);
		storeMember.setPayPassword(password);
		storeMember.setUsername(username);
		storeMember.setName(name);
		storeMember.setLoginIp("127.0.0.1");
		storeMember.setLoginDate(null);
		if (storeMemberBaseDao.existsIsDefaultStoreMember(member.getId())) {
			storeMember.setIsDefault(false);
		}
		else {
			storeMember.setIsDefault(true);
		}
		storeMember.setMember(member);
		storeMember.setMemberType(memberType == null ? 0 : memberType);
		save(storeMember);

		for (Store store : storess) {
			StoreMember sm = new StoreMember();
			sm.setBalance(new BigDecimal(0));
			sm.setStore(store);
			sm.setIsEnabled(true);
			sm.setMemberRank(memberRank);
			sm.setIsPartner(is_partner);
			sm.setPartnerCategory(partnerCategory);
			sm.setRecommendStoreMember(recommendStoreMember);
			sm.setInviteCode(generateCode());
			sm.setPassword(password);
			sm.setPayPassword(password);
			sm.setUsername(username);
			sm.setName(name);
			sm.setLoginIp("127.0.0.1");
			sm.setLoginDate(null);
			sm.setIsDefault(false);
			sm.setMember(member);
			sm.setMemberType(memberType == null ? 0 : memberType);
			save(sm);
		}
		Long[] pcRoleId = new Long[pcRoless.size()];
		for (int i = 0; i < pcRoless.size(); i++) {
			pcRoleId[i] = pcRoless.get(i).getId();
		}
		saveUserRole(pcRoleId, member, storeMember, false);
		memberBaseDao.getEntityManager().flush();
		storeMemberBaseDao.getEntityManager().flush();
	}

	@Override
	@Transactional
	public void updateInfo(StoreMember storeMember, Member member) {

		StoreMember pStoreMember = find(storeMember.getId());
		Member pMember = pStoreMember.getMember();

		if (StringUtils.isEmpty(storeMember.getPassword())) {
			member.setPassword(pStoreMember.getPassword());
			storeMember.setPassword(pStoreMember.getPassword());
			storeMember.setPayPassword(pStoreMember.getPayPassword());
		}
		else {
			member.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
			storeMember.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
			storeMember.setPayPassword(DigestUtils.md5Hex(storeMember.getPassword()));
		}
		
		 List<Map<String, Object>> listStoremebers = storeMemberBaseDao.findLoginStoreMember( member.getId(),  
		                                                                                      storeMember.getId(),  
		                                                                                      storeMember.getUsername(),  
		                                                                                      member.getMobile(),
		                                                                                      storeMember.getIdCard(), 
		                                                                                      member.getMobile(),
		                                                                                      storeMember.getIdCard());
		 
	        if(listStoremebers.size()==1){
	            
	        }else if(listStoremebers.size()>1) {
	            
	            String ErrorMsg="";
	            String sstoreMemberId="";
	            String smemberId="";
	            String susername="";
	            String sidCard="";
	            String smobile="";
	            
	            for(int i=0;i<listStoremebers.size();i++){
	                sstoreMemberId = listStoremebers.get(i).get("storeMemberId").toString();
	                smemberId = listStoremebers.get(i).get("memberId").toString();
	                susername = listStoremebers.get(i).get("username").toString();
	                sidCard =listStoremebers.get(i).get("idCard").toString();
	                smobile = listStoremebers.get(i).get("mobile").toString();
	                ErrorMsg += susername+"/"+sidCard+"/"+smobile+";";
	            }      
	            System.out.print(ErrorMsg);
	         // 手机号已存在
	            ExceptionUtil.throwServiceException("信息重复，请联系渠道部."+ErrorMsg);
	        }else{
	         // 用户不存在
	            ExceptionUtil.throwServiceException("用户不存在!!!");
	        }
		
//		
//		List<StoreMember> sotreMembers =loginOutBaseService.findStoreMember(storeMember.getIdCard());
//		if(sotreMembers.size()>0){
//			for(StoreMember sm : sotreMembers){
//				if (storeMemberBaseService.idCardExists(sm.getIdCard(),sm.getId())) {
//					// 手机号已存在
//					ExceptionUtil.throwServiceException("身份证已存在，请联系渠道部");
//				}
//			}
//		}
//		Member m = memberBaseService.findByMobile(member.getMobile());
//		if (m != null) {
//			if (storeMemberBaseService.storeMemberExists(m.getId(), 0L)) {
//				// 手机号已存在
//				ExceptionUtil.throwServiceException("手机号已存在，请联系渠道部");
//			}
//		}

		pMember.setPassword(member.getPassword());
		pMember.setIsLocked(false);
		pMember.setLoginFailureCount(0);
		pMember.setMobile(member.getMobile());
		pMember.setIdCard(storeMember.getIdCard());
		memberBaseService.update(pMember);

		pStoreMember.setPassword(storeMember.getPassword());
		pStoreMember.setPayPassword(storeMember.getPayPassword());
		pStoreMember.setName(storeMember.getName());
		pStoreMember.setAddress(storeMember.getAddress());
		pStoreMember.setZipCode(storeMember.getZipCode());
		pStoreMember.setIdCard(storeMember.getIdCard());
		pStoreMember.setEmailAddress(storeMember.getEmailAddress());
		pStoreMember.setGender(storeMember.getGender());
		pStoreMember.setBirth(storeMember.getBirth());
		pStoreMember.setImageName(storeMember.getImageName());
		update(pStoreMember);
	}

	@Override
	@Transactional(readOnly = true)
	public String storeAuth() {

		boolean isAdmin = WebUtils.isAdmin();
		if (isAdmin) return null;
		Member member = getCurrent().getMember();
		String sql = "select s1.store from xx_store_member s1,xx_store s2 where s1.store=s2.id and s2.is_main_store=0 and s2.is_enabled=1 and s1.member="
				+ member.getId();
		return sql;
	}

	@Override
	@Transactional(readOnly = true)
	public String warehouseAuth() {

		boolean isAdmin = WebUtils.isAdmin();
		if (isAdmin) return null;
		StoreMember storeMember = getCurrent();
		String sql = "select warehouse from xx_sm_warehouse where store_member = "
				+ storeMember.getId();
		return sql;
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findList(String username, String mobile,
			String name, Long memberRankId, Boolean isPartner, Long storeId,
			Long saleOrgId, String startTime, String endTime,
			Integer memberType, Long[] ids, Integer page, Integer size) {
		return storeMemberBaseDao.findList(username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				memberType,
				ids,
				page,
				size);
	}

	@Override
	@Transactional(readOnly = true)
	public Integer count(String username, String mobile, String name,
			Long memberRankId, Boolean isPartner, Long storeId, Long saleOrgId,
			String startTime, String endTime, Long[] ids) {
		return storeMemberBaseDao.count(username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				ids);
	}

	@Override
	@Transactional(readOnly = true)
	public boolean storeMemberExists(Long memberId, Long storeMemberId) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		return storeMemberBaseDao.storeMemberExists(companyInfoId,
				memberId,
				storeMemberId);
	}

	@Override
	@Transactional(readOnly = true)
	public boolean idCardExists(String idCard,Long storeMemberId) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		return storeMemberBaseDao.idCardExists(companyInfoId,
				idCard,
				storeMemberId);
	}
	
	
	@Override
	@Transactional(readOnly = true)
	public Map<String, Object> findListByStore(Long storeId) {
		return storeMemberBaseDao.findListByStore(storeId);
	}

	@Override
	public int intfSaveStoreMember(Map<String, Object> list, String username) {
		try {
			StoreMember storeMember = new StoreMember();
			Member member = new Member();
			MemberRank memberRank = memberRankBaseService.findDefault(9L);
			storeMember.setMemberRank(memberRank);
			member.setMobile((String) list.get("phone1"));
			String birthdate = (String) list.get("birthdate");
			storeMember.setBirth(birthdate == null ? null
					: dateFormat.parse(birthdate));
			storeMember.setName((String) list.get("userName"));
			String sex = (String) list.get("sex");
			storeMember.setGender(sex == null ? null
					: (sex.equals("M") ? Gender.male : Gender.female));
			String psw = (String) list.get("password");
			storeMember.setPassword(psw);
			storeMember.setPayPassword(psw);
			storeMember.setUsername(username);
			storeMember.setSsoUser(1);
			String idCard = (String) list.get("userIdNumber");
			storeMember.setIdCard(idCard == null ? null
					: idCard);
			String officeEmail =(String) list.get("officeEmail");
			storeMember.setEmailAddress(officeEmail == null ? null
					: officeEmail);
			member.setPassword(psw);
			saveStoreMember(storeMember,
					member,
					null,
					null,
					new Long[] { 438L }/* 机构 待定机构   测试：630   正式：438*/,
					null,
					new Integer[] { 0 },
					null,
					null,
					null,
					false,
					null,
					null,
					null,
					null,
					null);
		}
		catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return 0;
		}
		return 1;
	}

	@Override
	@Transactional
	public void createStoreMember(Store store) {
		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		// 保存用户
		Member member = new Member();
		Member m = memberBaseService.findByMobile(store.getHeadPhone());
		
		if (m != null) {
			member = m;
		}
		else {
			member.setIsLocked(false);
			member.setLoginFailureCount(0);
			member.setPassword(DigestUtils.md5Hex("123456"));
			member.setUsername(Sequence.getInstance().getSequence(null));
			member.setMobile(store.getHeadPhone());
			memberBaseService.save(member);
		}
		
		List<StoreMember> smList = storeMemberBaseDao.findDefaultByUsername(store.getHeadPhone(),
				9L);
		if(smList.size()>0){
			StoreMember sm = smList.get(0);
			if (sm != null) {
				// 用户已存在
				ExceptionUtil.throwServiceException("1601010");
			}
		}
				
		StoreMember storeMember = new StoreMember();
		storeMember.setCompanyInfoId(9L);
		// 保存用户
		storeMember.setMember(member);
		storeMember.setBalance(new BigDecimal(0));
		storeMember.setStore(storeBaseService.getMainStore());
		storeMember.setIsEnabled(true);
		storeMember.setInviteCode(generateCode());
		storeMember.setUsername(store.getHeadPhone());
		storeMember.setPassword(DigestUtils.md5Hex("123456"));
		storeMember.setPayPassword(DigestUtils.md5Hex("888888"));
		storeMember.setLoginIp(null);
		storeMember.setLoginDate(null);
		storeMember.setIdCard(store.getIdentity());
		if(store.getDealerSex()==0){
			storeMember.setGender(Gender.male);
		}else if(store.getDealerSex()==1){
			storeMember.setGender(Gender.female);
		}
		storeMember.setName(store.getDealerName());
		if (storeMemberBaseDao.existsIsDefaultStoreMember(member.getId())) {
			storeMember.setIsDefault(false);
		}
		else {
			storeMember.setIsDefault(true);
		}
		storeMember.setMemberType(1);
		save(storeMember);
		
		Long[] storeId = {store.getId()};

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("name", store.getSbu().getValue()));
		filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
		List<Sbu> sbuList = sbuService.findList(null, filters, null);
		Sbu sbu = sbuList==null?null:sbuList.get(0);
		
		// 保存选择的客户
		saveSelectedStore(storeId, storeMember, member);
		
		if(sbu!=null){
			Long[] sbuId = {sbu.getId()};
			Integer[] isDefaults = {1};
//			Long[] roleId = {};
			List<Long> roleId = new ArrayList<Long>();
			List<Map<String,Object>> roleSbu = storeMemberBaseDao.findRoleSbu(sbu.getId());
			for(Map<String,Object> r : roleSbu){
				if(r.get("pc_role")!=null){
					roleId.add(Long.parseLong(r.get("pc_role").toString()));
				}
			}
			
			// 保存用户sbu关系
			saveSmSbu(sbuId, isDefaults, storeMember);
			
			// 保存用户角色
			saveUserRole(roleId.toArray(new Long[]{}), member, storeMember, null);
		}

	}
	
	
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findListByShop(Long storeMemberId,
			String shopIds) {
		return storeMemberBaseDao.findListByShop(storeMemberId,
				shopIds);
	}

	@Override
	public Long findStoreId(Long storeMemberId) {
		if(find(storeMemberId).getMemberType()==0){
			return null;			
		}
		return storeMemberBaseDao.findStoreId(storeMemberId);
	}

	@Override
	public List<Map<String, Object>> findOrganization(Long id) {
		if(id!=null&&id>0){
			return storeMemberBaseDao.findOrganization(id);		
		}
		return null;
	}

    @Override
    public Boolean hasRoles(StoreMember storeMember, String... roleName) {
		if(storeMember==null||roleName==null) {
			return false;
		}
		Set<String> roleNameSet = new HashSet<String>(Arrays.asList(roleName));
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember",storeMember));
		filters.add(Filter.eq("companyInfoId",storeMember.getCompanyInfoId()));
		List<PcUserRole> pcUserRole = pcUserRoleBaseService.findList(null, filters, null);
		for (PcUserRole pcRole : pcUserRole) {
			String pcRoleName = pcRole.getPcRole().getName();
			if(roleNameSet.contains(pcRoleName)) {
				return true;
			}
		}
		return false;
    }

    @Override
	public String findSalesman(Long id){
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("parent", id));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("isSalesman", true));
		filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
		List<StoreMember> storeMembers = findList(null, filters, null);
		String ids = "";
		if(storeMembers.size()>0){
			for(int i=0;i<storeMembers.size();i++){
				if (storeMembers.size() - 1 == i) {
					ids += storeMembers.get(i).getId().toString();
				} else {
					ids += storeMembers.get(i).getId().toString() + ",";
				}
			}		
		}else{
			ids += id.toString();
		}
		return ids;
	}

    @Override
    public boolean storeMemberExists(Long memberId, Long storeMemberId, String username, String oldMobile,
                                     String oldIdCard, String newMobile, String newIdCard) {
        // TODO Auto-generated method stub
        List<Map<String, Object>> listStoremebers = storeMemberBaseDao.findLoginStoreMember( memberId,storeMemberId,username, oldMobile, oldIdCard,  newMobile,  newIdCard);
        if(listStoremebers.size()==1){
            return true;
        }else if(listStoremebers.size()>1) {
            
            String ErrorMsg="";
            String sstoreMemberId="";
            String smemberId="";
            String susername="";
            String sidCard="";
            String smobile="";
            
            for(int i=0;i<listStoremebers.size();i++){
                sstoreMemberId = listStoremebers.get(i).get("storeMemberId").toString();
                smemberId = listStoremebers.get(i).get("memberId").toString();
                susername = listStoremebers.get(i).get("username").toString();
                sidCard =listStoremebers.get(i).get("idCard").toString();
                smobile = listStoremebers.get(i).get("mobile").toString();
                ErrorMsg += ErrorMsg+":"+susername+"/"+sidCard+"/"+smobile+";";
            }      
         // 手机号已存在
            ExceptionUtil.throwServiceException("信息已存在，请联系渠道部.信息如下:"+ErrorMsg);
        }else{
         // 用户不存在
            ExceptionUtil.throwServiceException("用户不存在!!!");
        }
        return false;
    }

	@Override
	public Map<String, Object> findAppStoreMember(Long id) {
		return storeMemberBaseDao.findAppStoreMember(id);
		
	}
	
	/**
	 * 判断手机号是否存在
	 * @return
	 */
	public Boolean isMobileExistence(String mobile,Long a){
		Member member = memberBaseService.findByMobile(mobile);
		if (!ConvertUtil.isEmpty(member)) {
			if (storeMemberBaseService.storeMemberExists(member.getId(), 0L)) {
				// 手机号已存在
				return true;
			}
		}
		return false;
	}
	
	
	
	
	@Override
	@Transactional
	public String storeMemberSync(Map<String, Object> map) {
		try {
			Map<String, Object>  requestInfo = (Map<String, Object>) map.get("requestInfo");
			//AM用户同步接收是否修改密码 1:是  0:否
			Integer isReceiveUpdatePassword = Integer.parseInt(SystemConfig.getConfig("isReceiveUpdatePassword",9L));
			if(!ConvertUtil.isEmpty(requestInfo)){
				//主体
				if(ConvertUtil.isEmpty(requestInfo.get("companyInfoId"))){
					ExceptionUtil.throwServiceException("当前企业不能为空");
				}
				CompanyInfo companyInfo = companyInfoBaseService.find(Long.valueOf(requestInfo.get("companyInfoId").toString()));
				if(ConvertUtil.isEmpty(companyInfo)){
					ExceptionUtil.throwServiceException("当前企业不存在");
				}
				//*用户名
				if(ConvertUtil.isEmpty(requestInfo.get("username"))){
					ExceptionUtil.throwServiceException("用户名不能空");
				}
				String username = requestInfo.get("username").toString().trim();
				//*手机
				if(ConvertUtil.isEmpty(requestInfo.get("mobile"))){
					ExceptionUtil.throwServiceException("手机号不能空");
				}
				String mobile = requestInfo.get("mobile").toString().trim();
				StoreMember storeMember = null;
				List<StoreMember> storeMemberList = storeMemberBaseDao.findStoreMemberList(companyInfo.getId(),username,null);
				if(storeMemberList.isEmpty() || storeMemberList.size() == 0){
					storeMember = new StoreMember();
				}else if (!storeMemberList.isEmpty() && storeMemberList.size() == 1) {
					storeMember = storeMemberList.get(0);
				}else{
					// 用户已存在
					ExceptionUtil.throwServiceException("1601010");	
				}
				storeMember.setCompanyInfoId(companyInfo.getId());
				storeMember.setUsername(username);
				//*密码
				if(ConvertUtil.isEmpty(requestInfo.get("password"))){
					ExceptionUtil.throwServiceException("密码不能空");
				}
				if(ConvertUtil.isEmpty(storeMember.getId()) || isReceiveUpdatePassword == 1){
					String password = requestInfo.get("password").toString().trim();
					if (!password.equals(storeMember.getPassword())){
						PwdModificationRecord pwdRecord = new PwdModificationRecord();
						pwdRecord.setCompanyInfoId(companyInfo.getId());
						pwdRecord.setStoreMember(storeMember);
						pwdRecord.setUsername(storeMember.getUsername());
						pwdRecord.setPwd(storeMember.getPassword());
						pwdRecord.setRevisionSource(3);
						//新增密码同步记录
						pwdModificationRecordService.save(pwdRecord);
					}
					storeMember.setPassword(password);
					storeMember.setPayPassword(password);
				}
				//姓名
				String name = requestInfo.get("name") == null ? "" : requestInfo.get("name").toString().trim();
				storeMember.setName(name);
				//性别
				if(ConvertUtil.isEmpty(requestInfo.get("gender"))){
					storeMember.setGender(null);
				}else{
					String gender = requestInfo.get("gender").toString().trim();
					storeMember.setGender(gender.equals("male") ? Gender.male : Gender.female);
				}
				//出生日期
				if(ConvertUtil.isEmpty(requestInfo.get("birth"))){
					storeMember.setBirth(null);
				}else{
					storeMember.setBirth(dateFormat.parse(requestInfo.get("birth").toString()));
				}
				//邮编
				String zipCode = requestInfo.get("zipCode") == null ? "" : requestInfo.get("zipCode").toString().trim();
				storeMember.setZipCode(zipCode);
				//地址
				String address = requestInfo.get("address") == null ? "" : requestInfo.get("address").toString().trim();
				storeMember.setAddress(address);
				//*身份证
				if(ConvertUtil.isEmpty(requestInfo.get("idCard"))){
					ExceptionUtil.throwServiceException("身份证号不能为空");
				}
				String idCard = requestInfo.get("idCard").toString().trim();
				storeMember.setIdCard(idCard);
				//邮箱地址
				String emailAddress = requestInfo.get("emailAddress") == null ? "" : requestInfo.get("emailAddress").toString().trim();
				storeMember.setEmailAddress(emailAddress);
				//是否启用      
				Boolean isEnabled = requestInfo.get("isEnabled") == null ? false : Boolean.parseBoolean(requestInfo.get("isEnabled").toString());
				storeMember.setIsEnabled(isEnabled);
				//是否业务员    
//				Boolean isSalesman = requestInfo.get("isSalesman") == null ? false : Boolean.parseBoolean(requestInfo.get("isSalesman").toString());
//				storeMember.setIsSalesman(isSalesman);
				//会员类型  0 企业用户，1 外部用户，99小程序帐号
				int memberType = requestInfo.get("memberType") == null ? 0 : Integer.parseInt(requestInfo.get("memberType").toString());
				storeMember.setMemberType(memberType);
				//是否活动管理员
				Boolean isActiveAdministrator = requestInfo.get("isActiveAdministrator") == null ? false : Boolean.parseBoolean(requestInfo.get("isActiveAdministrator").toString());
				storeMember.setIsActiveAdministrator(isActiveAdministrator);
				//小程序用户类型
				if(!ConvertUtil.isEmpty(requestInfo.get("appType"))){
					Integer appType = Integer.parseInt(requestInfo.get("appType").toString());
					storeMember.setAppType(appType);
				}else{
					storeMember.setAppType(null);
				}
				//会员
				Member member = null;
				if(ConvertUtil.isEmpty(storeMember.getMember())){
					member = new Member();
				}else{
					member = storeMember.getMember();
				}
				member.setCompanyInfoId(storeMember.getCompanyInfoId());
				member.setMobile(mobile);
				member.setIsLocked(false);
				member.setLoginFailureCount(0);
				member.setLockedDate(new Date());
				member.setPassword(DigestUtils.md5Hex(storeMember.getPassword()));
				member.setUsername(username);
				member.setIdCard(storeMember.getIdCard());
				if(ConvertUtil.isEmpty(storeMember.getId())){
					// 用户信息
					memberBaseService.save(member);
					storeMember.setMember(member);
					//用户余额
					storeMember.setBalance(new BigDecimal(0));
					//会员等级
					MemberRank memberRank = memberRankBaseService.findDefault(companyInfo.getId());
					storeMember.setMemberRank(memberRank);
					//企业
					Store store =  storeBaseService.getMainStore();
					storeMember.setStore(store);
					save(storeMember);
				}else{
					memberBaseService.update(member);
					storeMember.setMember(member);
					update(storeMember);
				}
			}
			return "success";
		} catch (Exception e) {
			LogUtils.error("中板同步用户异常："+e);
			return e.getMessage();
		}
	}

    @Override
    public List<StoreMember> findStoreByMember(Long storeId) {
        return storeMemberBaseDao.findStoreByMember(storeId);
    }

    @Override
    @Transactional
    public void closeStoreByMember(Long storeId) {
        List<StoreMember> smList = findStoreByMember(storeId);
        for(StoreMember sm : smList){
            sm.setIsEnabled(false);
            update(sm);
        }
    }
}
