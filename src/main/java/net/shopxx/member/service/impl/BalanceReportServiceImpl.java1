package net.shopxx.member.service.impl;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.member.dao.BalanceReportDao;
import net.shopxx.member.entity.BalanceReport;
import net.shopxx.member.service.BalanceReportService;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
@Service("balanceReportServiceImpl")
public class BalanceReportServiceImpl extends BaseServiceImpl<BalanceReport> implements BalanceReportService{
	
	@Resource(name = "balanceReportDao")
    private BalanceReportDao balanceReportDao;
	
	@Override
	public void checkParamIsNotNull(BalanceReport balanceReport) {
		
		/**订单金额*/
		if(ConvertUtil.isEmpty(balanceReport.getOrderBalance())){
			balanceReport.setOrderBalance(BigDecimal.ZERO);
		}
		/**订单关闭金额*/
		if(ConvertUtil.isEmpty(balanceReport.getOrderCloseBalance())){
			balanceReport.setOrderCloseBalance(BigDecimal.ZERO);
		}
		/**发货金额*/
		if(ConvertUtil.isEmpty(balanceReport.getShipmentBalance())){
			balanceReport.setShipmentBalance(BigDecimal.ZERO);
		}
		/**退货金额*/
		if(ConvertUtil.isEmpty(balanceReport.getReturnBalance())){
			balanceReport.setReturnBalance(BigDecimal.ZERO);
		}
		/**充值金额*/
		if(ConvertUtil.isEmpty(balanceReport.getBalance())){
			balanceReport.setBalance(BigDecimal.ZERO);
		}
		/**政策金额*/
		if(ConvertUtil.isEmpty(balanceReport.getPolicyBalance())){
			balanceReport.setPolicyBalance(BigDecimal.ZERO);
		}
	}
	

	@Override
	public BalanceReport queryOrInsert(BalanceReport balanceReport) {
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			//客户
			if(ConvertUtil.isEmpty(balanceReport.getStore())){
				ExceptionUtil.throwServiceException("客户不能为空");
			}
			//sbu
			if(ConvertUtil.isEmpty(balanceReport.getSbu())){
				ExceptionUtil.throwServiceException("sbu不能为空");
			}
			//经营组织
			if(ConvertUtil.isEmpty(balanceReport.getOrganization())){
				ExceptionUtil.throwServiceException("经营组织不能为空");
			}
			//单据日期
			if(ConvertUtil.isEmpty(balanceReport.getBillDate())){
				ExceptionUtil.throwServiceException("单据日期不能为空");
			}
			//根据客户、sub、经营组织、单据日期查找客户余额报表
			List<BalanceReport> BalanceReportList =  balanceReportDao.findBalanceReportList(balanceReport.getStore().getId(),
									balanceReport.getSbu().getId(),balanceReport.getOrganization().getId(),
									format.format(balanceReport.getBillDate()).toString());
			if(BalanceReportList.isEmpty() || BalanceReportList.size()==0){
				//初始化客户余额报表数量
				this.checkParamIsNotNull(balanceReport);
				save(balanceReport);
			}else if(!BalanceReportList.isEmpty() && BalanceReportList.size()==1){
				balanceReport = BalanceReportList.get(0);
			}else{
				throw new RuntimeException("客户余额报表数据异常，请管理员进行维护");
			}
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
		return balanceReport;
	}
}
