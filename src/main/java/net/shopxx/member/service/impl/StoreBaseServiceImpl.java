package net.shopxx.member.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import net.shopxx.act.entity.ActWf;
import net.shopxx.intf.service.AmStoreToService;
import net.shopxx.member.entity.*;
import net.shopxx.member.service.*;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopInfoService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SalesArea;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SalesAreaService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.finance.entity.AccountParameter;
import net.shopxx.finance.entity.StoreBalance;
import net.shopxx.finance.service.AccountParameterService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.dao.StoreBaseDao;
import net.shopxx.member.entity.Store.Type;
import net.shopxx.product.service.ProductCategoryBaseService;

/**
 * Service - 客户
 */
@Service("storeBaseServiceImpl")
public class StoreBaseServiceImpl extends BaseServiceImpl<Store> implements
		StoreBaseService {

	@Resource(name = "storeBaseDao")
	private StoreBaseDao storeBaseDao;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardBaseService;
	@Resource(name = "storeAddressServiceImpl")
	private StoreAddressService storeAddressService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "accountParameterServiceImpl")
	private AccountParameterService accountParameterService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeExitServiceImpl")
	private StoreExitService storeExitService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "productCategoryBaseServiceImpl")
	private ProductCategoryBaseService ProductCategoryBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "storeApplyServiceImpl")
	private StoreApplyService storeApplyService;
	@Resource(name = "salesareaServiceImpl")
	private SalesAreaService salesAreaService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "productCategoryBaseServiceImpl")
	private ProductCategoryBaseService productCategoryBaseService;
	@Resource(name = "storeInvoiceInfoServiceImpl")
	private StoreInvoiceInfoService storeInvoiceInfoService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	

	@Override
	@Transactional(readOnly = true)
	public Store getMainStore() {
		Long companyInfoId = companyInfoBaseService.getCurrentId();
		return storeBaseDao.getMainStore(companyInfoId);
	}

	@Override
	@Transactional(readOnly = true)
	public Store getMainStore(Long companyInfoId) {
		return storeBaseDao.getMainStore(companyInfoId);
	}

	@Override
	@Transactional
	public void save(Store store, Long storeMemberId, Long areaId,
			Long saleOrgId, Long memberRankId, Long salesPlatformId,
			Long businessTypeId, Long distributorStatusId, BankCard bankCard,
			Long bAreaId, String bAddress, String bMobile,
			Long changeMemberRankId, Long salesAreaId, Long storeSalesAreaId) {

		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		if (saleOrgId != null) {
			store.setSaleOrg(saleOrg);
		}
		else {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("isTop", true));
			List<SaleOrg> saleOrgs = saleOrgBaseService.findList(1,
					filters,
					null);
			if (saleOrgs.size() > 0) {
				store.setSaleOrg(saleOrgs.get(0));
			}
		}
		StoreMember storeMember = storeMemberBaseService.find(storeMemberId);
		if (storeMemberId != null) {
			store.setStoreMember(storeMember);
		}

		SaleOrg salesPlatform = saleOrgBaseService.find(salesPlatformId);
		store.setSalesPlatform(salesPlatform);

		// 处理经营品类
		List<BusinessCategory> bc = store.getBusinessCategorys();
		for (Iterator<BusinessCategory> iterator = bc.iterator(); iterator.hasNext();) {
			BusinessCategory businessCategory = (BusinessCategory) iterator.next();
			if (businessCategory == null
					|| businessCategory.getProductBigType() == null) {
				iterator.remove();
			}
			if (businessCategory.getProductBigType() != null) {
				businessCategory.setProductBigType(ProductCategoryBaseService.find(businessCategory.getProductBigType()
						.getId()));
			}
			if (businessCategory.getProductCenterType() != null) {
				businessCategory.setProductCenterType(ProductCategoryBaseService.find(businessCategory.getProductCenterType()
						.getId()));
			}
			businessCategory.setStore(store);
		}
		store.setBusinessCategorys(bc);

		// 处理经营活动记录
		List<BusinessRecord> br = store.getBusinessRecords();
		for (Iterator<BusinessRecord> iterator = br.iterator(); iterator.hasNext();) {
			BusinessRecord businessRecord = (BusinessRecord) iterator.next();
			if (businessRecord == null || businessRecord.getAmount() == null) {
				iterator.remove();
			}
			businessRecord.setStore(store);
			if (ConvertUtil.isEmpty(businessRecord.getSn())) {
				businessRecord.setSn("R"
						+ Sequence.getInstance().getSequence(null));
			}
		}
		store.setBusinessRecords(br);

		// 处理保证金
		List<CautionMoney> cm = store.getCautionMoneies();
		for (Iterator<CautionMoney> iterator = cm.iterator(); iterator.hasNext();) {
			CautionMoney cautionMoney = (CautionMoney) iterator.next();
			if (cautionMoney == null || cautionMoney.getPayable() == null) {
				iterator.remove();
			}
			cautionMoney.setStore(store);
			if (ConvertUtil.isEmpty(cautionMoney.getSn())) {
				cautionMoney.setSn("A"
						+ Sequence.getInstance().getSequence(null));
			}
		}
		store.setCautionMoneies(cm);

		store.setMemberRank(memberRankBaseService.find(memberRankId));
		store.setChangeMemberRank(memberRankBaseService.find(changeMemberRankId));
		Area area = areaBaseService.find(areaId);
		store.setArea(area);
		store.setIsEnabled(true);
		store.setIsMainStore(false);
		if (!store.getType().equals(Type.onlineShop)) {
			store.setOutShopName(null);
			store.setOnlineShopType(null);
		}
		store.setSn(Sequence.getInstance().getSequence(null));
		List<StoreAddress> addressList = store.getStoreAddress();
		for (Iterator<StoreAddress> iterator = addressList.iterator(); iterator.hasNext();) {
			StoreAddress storeAddress = (StoreAddress) iterator.next();
			if (storeAddress == null) {
				iterator.remove();
			}
			if (storeAddress.getArea() != null
					&& storeAddress.getArea().getId() != null) {
				Area a = areaBaseService.find(storeAddress.getArea().getId());
				storeAddress.setArea(a);
			}
			if (storeAddress.getSalesArea() != null
					&& storeAddress.getSalesArea().getId() != null) {
				SalesArea salesArea = salesAreaService.find(storeAddress.getSalesArea()
						.getId());
				storeAddress.setSalesArea(salesArea);
			}
			storeAddress.setStore(store);

		}
		store.setStoreAddress(addressList);

		List<StoreInvoiceInfo> storeInvoiceInfos = store.getStoreInvoiceInfos();
		for (Iterator<StoreInvoiceInfo> iterator = storeInvoiceInfos.iterator(); iterator.hasNext();) {
			StoreInvoiceInfo storeInvoiceInfo = iterator.next();
			if (storeInvoiceInfo == null
					|| storeInvoiceInfo.getInvoiceTitle() == null) {
				iterator.remove();
			}
			storeInvoiceInfo.setStore(store);
		}
		store.setStoreInvoiceInfos(storeInvoiceInfos);

		List<StoreContract> storeContracts = store.getStoreContract();
		for (Iterator<StoreContract> iterator = storeContracts.iterator(); iterator.hasNext();) {
			StoreContract storeContract = iterator.next();
			if (storeContract == null || storeContract.getName() == null) {
				iterator.remove();
			}
			storeContract.setStore(store);
		}
		store.setStoreContract(storeContracts);

		//合作单位
		List<StoreCooperation> storeCooperations = store.getStoreCooperation();
		for (Iterator<StoreCooperation> iterator = storeCooperations.iterator(); iterator.hasNext();) {
			StoreCooperation storeCooperation = iterator.next();
			if (storeCooperation == null) {
				iterator.remove();
			}
			storeCooperation.setStore(store);
		}
		store.setStoreCooperation(storeCooperations);
		SalesArea storeSalesArea = salesAreaService.find(storeSalesAreaId);
		if (storeSalesArea != null) {
			store.setSalesArea(storeSalesArea);
		}

		List<StoreManager> storeManagers = store.getStoreManagers();
		for (Iterator<StoreManager> iterator = storeManagers.iterator(); iterator.hasNext();) {
			StoreManager storeManager = iterator.next();
			if (storeManager == null
					|| ConvertUtil.isEmpty(storeManager.getManager())) {
				iterator.remove();
				continue;
			}
			storeManager.setStore(store);
		}
		store.setStoreManagers(storeManagers);
		store.setCreateBy(storeMemberBaseService.getCurrent());
		store.setUnJoinSucessFlag(false);

		if (businessTypeId != null) {
			SystemDict businessType = systemDictBaseService.find(businessTypeId);
			store.setBusinessType(businessType);
		}

		if (distributorStatusId != null) {
			SystemDict distributorStatus = systemDictBaseService.find(distributorStatusId);
			store.setDistributorStatus(distributorStatus);
		}

		// 附件
		List<StoreAttach> storeAttachs = store.getStoreAttachs();
		for (Iterator<StoreAttach> iterator = storeAttachs.iterator(); iterator.hasNext();) {
			StoreAttach storeAttach = iterator.next();
			if (storeAttach == null || storeAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (storeAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			storeAttach.setFileName(storeAttach.getName()
					+ "."
					+ storeAttach.getSuffix());
			storeAttach.setStore(store);
			storeAttach.setStoreMember(storeMemberService.getCurrent());
		}
		store.setStoreAttachs(storeAttachs);

		if (store.getHeadNewArea() == null
				|| (store.getHeadNewArea() != null && store.getHeadNewArea()
						.getId() == null)) {
			store.setHeadNewArea(null);
		}

		//sbu保存
		List<StoreSbu> storeSbuList = store.getStoreSbu();
		for (Iterator<StoreSbu> iterator = storeSbuList.iterator(); iterator.hasNext();) {
			StoreSbu storeSbu = (StoreSbu) iterator.next();
			if (storeSbu == null
					|| storeSbu.getSbu() == null
					|| (storeSbu.getSbu() != null && storeSbu.getSbu().getId() == null)) {
				iterator.remove();
				continue;
			}
			Sbu sbu = sbuService.find(storeSbu.getSbu().getId());
			MemberRank memberRank = memberRankBaseService.find(storeSbu.getMemberRank()
					.getId());
			storeSbu.setSbu(sbu);
			storeSbu.setStore(store);
			storeSbu.setMemberRank(memberRank);
		}
		store.setStoreSbu(storeSbuList);
		if(store.getStoreMember()!=null){//客户绑定区域经理帐号回写到区域经理上
			StoreMember sm = store.getStoreMember();
			if(sm.getMember()!=null){
				storeMemberBaseService.saveSelectedStore(new Long[]{sm.getId()}, sm, sm.getMember());				
			}
		}
		save(store);

		/*
		 * if (bankCard != null || !ConvertUtil.isEmpty(bAreaId) ||
		 * !ConvertUtil.isEmpty(bMobile) || !ConvertUtil.isEmpty(bAddress)) {
		 * Area bArea = areaBaseService.find(bAreaId); bankCard.setArea(bArea);
		 * bankCard.setAddress(bAddress); bankCard.setMobile(bMobile);
		 * bankCard.setIsEnabled(true); bankCard.setStore(store);
		 * bankCardBaseService.save(bankCard); }
		 */

		// 可发货余额
		saveStoreBalance(store);
	}

	@Override
	@Transactional
	public void update(Store store, Long storeMemberId, Long areaId,
			Long saleOrgId, Long memberRankId, Long salesPlatformId,
			Long businessTypeId, Long distributorStatusId, BankCard bankCard,
			Long bAreaId, String bAddress, String bMobile, Boolean bIsEnabled,
			Long changeMemberRankId, Long salesAreaId, Long storeSalesAreaId,
			Long createById,Long storeIdLink5) {

		Store pStore = find(store.getId());
		
		/*String sql = "insert into xx_store_record select * from xx_store where id="+pStore.getId();
		getDaoCenter().getNativeDao().insert(sql);*/

		SaleOrg salesPlatform = saleOrgBaseService.find(salesPlatformId);
		store.setSalesPlatform(salesPlatform);

		// 处理经营品类
		List<BusinessCategory> bc = store.getBusinessCategorys();
		for (Iterator<BusinessCategory> iterator = bc.iterator(); iterator.hasNext();) {
			BusinessCategory businessCategory = (BusinessCategory) iterator.next();
			if (businessCategory == null
					|| businessCategory.getProductBigType() == null) {
				iterator.remove();
			}
			if (businessCategory.getProductBigType() != null) {
				businessCategory.setProductBigType(ProductCategoryBaseService.find(businessCategory.getProductBigType()
						.getId()));
			}
			if (businessCategory.getProductCenterType() != null) {
				businessCategory.setProductCenterType(ProductCategoryBaseService.find(businessCategory.getProductCenterType()
						.getId()));
			}
			businessCategory.setStore(store);
		}
		store.setBusinessCategorys(bc);
		// 处理经营活动记录
		List<BusinessRecord> br = store.getBusinessRecords();
		for (Iterator<BusinessRecord> iterator = br.iterator(); iterator.hasNext();) {
			BusinessRecord businessRecord = (BusinessRecord) iterator.next();
			if (businessRecord == null || businessRecord.getAmount() == null) {
				iterator.remove();
			}
			businessRecord.setStore(store);
			if (ConvertUtil.isEmpty(businessRecord.getSn())) {
				businessRecord.setSn("R"
						+ Sequence.getInstance().getSequence(null));
			}
		}
		store.setBusinessRecords(br);

		// 处理保证金
		List<CautionMoney> cm = store.getCautionMoneies();
		for (Iterator<CautionMoney> iterator = cm.iterator(); iterator.hasNext();) {
			CautionMoney cautionMoney = (CautionMoney) iterator.next();
			if (cautionMoney == null || cautionMoney.getPayable() == null) {
				iterator.remove();
			}
			cautionMoney.setStore(store);
			if (ConvertUtil.isEmpty(cautionMoney.getSn())) {
				cautionMoney.setSn("A"
						+ Sequence.getInstance().getSequence(null));
			}
		}
		store.setCautionMoneies(cm);

		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		List<Filter> filters = new ArrayList<Filter>();
		if (saleOrgId != null) {
			store.setSaleOrg(saleOrg);
		}
		else {
			filters.add(Filter.eq("isTop", true));
			List<SaleOrg> saleOrgs = saleOrgBaseService.findList(1,
					filters,
					null);
			if (saleOrgs.size() > 0) {
				store.setSaleOrg(saleOrgs.get(0));
			}
		}
		StoreMember storeMember = storeMemberBaseService.find(storeMemberId);
		if (storeMemberId != null) {
			store.setStoreMember(storeMember);
		}
		
		if(storeIdLink5 != null) {
			store.setStoreLink5(storeBaseService.find(storeIdLink5));
		}else{
			//清除link5归属经销商信息
			store.setStoreLink5(null);
		}
		
		store.setMemberRank(memberRankBaseService.find(memberRankId));
		store.setChangeMemberRank(memberRankBaseService.find(changeMemberRankId));
		Area area = areaBaseService.find(areaId);
		store.setArea(area);
		store.setIsMainStore(pStore.getIsMainStore());
		if (!store.getType().equals(Type.onlineShop)) {
			store.setOutShopName(null);
			store.setOnlineShopType(null);
		}
		List<StoreAddress> addressList = store.getStoreAddress();
		for (Iterator<StoreAddress> iterator = addressList.iterator(); iterator.hasNext();) {
			StoreAddress storeAddress = (StoreAddress) iterator.next();
			if (storeAddress == null || storeAddress.getAddress() == null) {
				iterator.remove();
			}
			if (storeAddress.getArea() != null
					&& storeAddress.getArea().getId() != null) {
				Area a = areaBaseService.find(storeAddress.getArea().getId());
				storeAddress.setArea(a);
			}
			if (storeAddress.getSalesArea() != null
					&& storeAddress.getSalesArea().getId() != null) {
				SalesArea salesArea = salesAreaService.find(storeAddress.getSalesArea()
						.getId());
				storeAddress.setSalesArea(salesArea);
			}
			storeAddress.setStore(store);
			//以地址明细id作为外部编号传到ERP,作为KA在ERP的地址外部编码的唯一标识
			if (storeAddress.getOutTradeNo() == null) {//原先的外部编号不修改，在ERP已存在唯一
				if (storeAddress.getId() != null) {
					storeAddress.setOutTradeNo(storeAddress.getId().toString());
				}
			}
			storeAddressService.update(storeAddress);
		}

		List<StoreInvoiceInfo> storeInvoiceInfos = store.getStoreInvoiceInfos();
		for (Iterator<StoreInvoiceInfo> iterator = storeInvoiceInfos.iterator(); iterator.hasNext();) {
			StoreInvoiceInfo storeInvoiceInfo = iterator.next();
			if (storeInvoiceInfo == null
					|| storeInvoiceInfo.getInvoiceTitle() == null) {
				iterator.remove();
			}
			storeInvoiceInfo.setStore(store);
		}
		store.setStoreInvoiceInfos(storeInvoiceInfos);

		List<StoreContract> storeContracts = store.getStoreContract();
		for (Iterator<StoreContract> iterator = storeContracts.iterator(); iterator.hasNext();) {
			StoreContract storeContract = iterator.next();
			if (storeContract == null || storeContract.getName() == null) {
				iterator.remove();
			}
			storeContract.setStore(store);
		}
		store.setStoreContract(storeContracts);

		//合作单位
		List<StoreCooperation> storeCooperations = store.getStoreCooperation();
		for (Iterator<StoreCooperation> iterator = storeCooperations.iterator(); iterator.hasNext();) {
			StoreCooperation storeCooperation = iterator.next();
			if (storeCooperation == null) {
				iterator.remove();
			}
			storeCooperation.setStore(store);
		}
		store.setStoreCooperation(storeCooperations);
		store.setCreateBy(storeMemberBaseService.find(createById));
		SalesArea storeSalesArea = salesAreaService.find(storeSalesAreaId);
		if (storeSalesArea != null) {
			store.setSalesArea(storeSalesArea);
		}

		List<StoreManager> storeManagers = store.getStoreManagers();
		for (Iterator<StoreManager> iterator = storeManagers.iterator(); iterator.hasNext();) {
			StoreManager storeManager = iterator.next();
			if (storeManager == null
					|| ConvertUtil.isEmpty(storeManager.getManager())) {
				iterator.remove();
				continue;
			}
			storeManager.setStore(store);
		}
		store.setStoreManagers(storeManagers);

		if (businessTypeId != null) {
			SystemDict businessType = systemDictBaseService.find(businessTypeId);
			store.setBusinessType(businessType);
		}
		if (distributorStatusId != null) {
			SystemDict distributorStatus = systemDictBaseService.find(distributorStatusId);
			store.setDistributorStatus(distributorStatus);
		}

		// 附件
		List<StoreAttach> storeAttachs = store.getStoreAttachs();
		for (Iterator<StoreAttach> iterator = storeAttachs.iterator(); iterator.hasNext();) {
			StoreAttach storeAttach = iterator.next();
			if (storeAttach == null || storeAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (storeAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			storeAttach.setFileName(storeAttach.getName()
					+ "."
					+ storeAttach.getSuffix());
			storeAttach.setStore(store);
			storeAttach.setStoreMember(storeMemberService.getCurrent());
		}
		store.setStoreAttachs(storeAttachs);

		if (store.getHeadNewArea() == null
				|| (store.getHeadNewArea() != null && store.getHeadNewArea()
						.getId() == null)) {
			store.setHeadNewArea(null);
		}
		//sbu保存
		List<StoreSbu> storeSbuList = store.getStoreSbu();
		for (Iterator<StoreSbu> iterator = storeSbuList.iterator(); iterator.hasNext();) {
			StoreSbu storeSbu = (StoreSbu) iterator.next();
			if (storeSbu == null
					|| storeSbu.getSbu() == null
					|| (storeSbu.getSbu() != null && storeSbu.getSbu().getId() == null)) {
				iterator.remove();
				continue;
			}
			Sbu sbu = sbuService.find(storeSbu.getSbu().getId());
			MemberRank memberRank = memberRankBaseService.find(storeSbu.getMemberRank()
					.getId());
			storeSbu.setSbu(sbu);
			storeSbu.setStore(pStore);
			storeSbu.setMemberRank(memberRank);
		}
		store.setStoreSbu(storeSbuList);
		Store store_ = find(store.getId());
		if(store_.getStoreMember()!=null&&store_.getStoreMember().getIsSalesman()){
			filters.clear();
			filters.add(Filter.eq("member", store_.getStoreMember().getMember()));
			List<StoreMember> storeMembers = storeMemberService.findList(null, filters, null);
			for(StoreMember sor:storeMembers){
				if(sor.getStore()!=null){
					if(!sor.getStore().getIsMainStore()){
						if(sor.getStore()==store_){
							storeMemberService.delete(sor);
						}
					}
				}
			}
		}
		store = update(store,
				"sn",
				"balance",
				"credit",
				"budget",
				"lockBudget",
				"lockBalance");
		
		Store s = find(store.getId());
		if(s.getStoreMember()!=null){//客户绑定区域经理帐号回写到区域经理上
			StoreMember sm = store.getStoreMember();
			if(sm.getMember()!=null){
				//判断用户是否业务员 如果不是业务员赋值无效
				if(sm.getIsSalesman()){
					storeMemberService.saveSalesmanStore(new Long[]{s.getId()}, sm, sm.getMember());
				}
			}
		}
		/*
		 * filters.clear(); filters.add(Filter.eq("store", store)); BankCard
		 * pBankCard = bankCardBaseService.find(filters); if (pBankCard != null)
		 * { bankCard.setId(pBankCard.getId()); Area bArea =
		 * areaBaseService.find(bAreaId); bankCard.setArea(bArea);
		 * bankCard.setAddress(bAddress); bankCard.setMobile(bMobile);
		 * bankCard.setIsEnabled(bIsEnabled); bankCard.setStore(store);
		 * bankCardBaseService.update(bankCard); } else { if
		 * (!ConvertUtil.isEmpty(bankCard.getBankName()) ||
		 * !ConvertUtil.isEmpty(bankCard.getBankCode()) ||
		 * !ConvertUtil.isEmpty(bankCard.getBankCardNo()) ||
		 * !ConvertUtil.isEmpty(bankCard.getBankAccount()) ||
		 * !ConvertUtil.isEmpty(bAreaId) || !ConvertUtil.isEmpty(bAddress) ||
		 * !ConvertUtil.isEmpty(bankCard.getMemo()) ||
		 * !ConvertUtil.isEmpty(bMobile)) { Area bArea =
		 * areaBaseService.find(bAreaId); bankCard.setId(null);
		 * bankCard.setArea(bArea); bankCard.setAddress(bAddress);
		 * bankCard.setMobile(bMobile); bankCard.setIsEnabled(bIsEnabled);
		 * bankCard.setStore(store); bankCardBaseService.save(bankCard); } }
		 */
		// 更新收货地址
		if (WebUtils.getCurrentCompanyInfoId() == 9) {
			saveIntfAtCheck(s, null, 1);//flag 0 新增并同步  1 保存并同步 2失效并同步
		}
	}

	public void saveIntfAtCheck(Store store, StoreAddress storeAddress,
			Integer flag) {
		Long companyInfoId = store.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		if ("nature".equals(companyInfo.getCompany_code())) {// nadev:dzrjj
																// na/natest:nature
			// 大自然创建客户接口
			//System.out.println("进入接口");
			intfOrderMessageToService.saveStoreIntf(store, storeAddress, flag);//flag 0 新增并同步  1 保存并同步 2失效并同步
		}
	}

	@Override
	@Transactional
	public void storeAddressIntf(Long storeId, Integer flag, Long areaId,
			Long salesAreaId, StoreAddress storeAddress) {

		Store store = find(storeId);
		if (areaId != null
				&& storeAddress.getConsignee() != null
				&& storeAddress.getMobile() != null) {
			if (flag == 0) {// 新增并同步
				List<StoreAddress> storeAddressList = store.getStoreAddress();
				Area area = areaBaseService.find(areaId);
				storeAddress.setArea(area);
				SalesArea salesArea = salesAreaService.find(salesAreaId);
				area.setSalesAreaId(salesArea.getId());
				storeAddress.setSalesArea(salesArea);
				storeAddress.setStore(store);
				storeAddressService.save(storeAddress);
				storeAddressList.add(storeAddress);
				store.setStoreAddress(storeAddressList);
				update(store);
				//外部编号取明细id作为在ERP的唯一标识
				storeAddress.setOutTradeNo(storeAddress.getId().toString());
				saveIntfAtCheck(store, storeAddress, flag);
				storeAddressService.update(storeAddress);

			}
			else if (flag == 1) {// 修改(保存并同步)
				Area area = areaBaseService.find(areaId);
				storeAddress.setArea(area);
				SalesArea salesArea = salesAreaService.find(salesAreaId);
				area.setSalesAreaId(salesArea.getId());
				storeAddress.setSalesArea(salesArea);
				storeAddress.setStore(store);
				if (storeAddress.getOutTradeNo() == null) {//原先的外部编号不修改，在ERP已存在
					storeAddress.setOutTradeNo(storeAddress.getId().toString());
				}
				storeAddressService.update(storeAddress);
				saveIntfAtCheck(store, storeAddress, flag);

			}
			else if (flag == 2) {//  失效(失效并同步)

				StoreAddress sa = storeAddressService.find(storeAddress.getId());
				sa.setIsInvalid(1);//1失效
				if (sa.getOutTradeNo() == null) {//原先的外部编号不修改，在ERP已存在
					sa.setOutTradeNo(sa.getId().toString());
				}
				storeAddressService.update(sa);
				saveIntfAtCheck(store, sa, flag);
			}
			else if (flag == 3) {//  生效

				StoreAddress sa = storeAddressService.find(storeAddress.getId());
				sa.setIsInvalid(null);
				if (storeAddress.getOutTradeNo() == null) {//原先的外部编号不修改，在ERP已存在
					storeAddress.setOutTradeNo(storeAddress.getId().toString());
				}
				storeAddressService.update(sa);
				saveIntfAtCheck(store, sa, 1);
			}
		}

	}

	/**康宝，创维产品导入*/
	@Override
	@Transactional
	public String storeImportKb(MultipartFile multipartFile) throws Exception {

		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];

		int rows = sheet.getRows();

		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}

		// 客户
		String outTradeNo; // EAS客户编码
		String name; // 客户名称
		String alias; // 客户助记码
		String customer;//客户类型
		String memberRankName;// 价格类型
		String saleOrgName;// 机构
		String taxRate;//税率
		String isSigning;//是否签约 0是 1否
		String date;//签约时间

		List<Filter> filters = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		for (int i = 1; i < rows; i++) {

			cell = sheet.getCell(0, i);
			outTradeNo = cell.getContents().trim();

			CompanyInfo companyInfo = companyInfoBaseService.find(WebUtils.getCurrentCompanyInfoId());
			if (ConvertUtil.isEmpty(outTradeNo)) {
				msg = "第" + i + "行." + "ERP客户编码未填";
				ExceptionUtil.throwServiceException(msg);
			}
			if (count(Filter.eq("sn", outTradeNo)) > 0) {
				msg = "第" + i + "行." + "ERP客户编码[" + outTradeNo + "]已存在";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(1, i);
			name = cell.getContents().trim();
			if (ConvertUtil.isEmpty(name)) {
				msg = "第" + i + "行." + "客户名称未填";
				ExceptionUtil.throwServiceException(msg);
			}
			if (count(Filter.eq("name", name)) > 0) {
				msg = "第" + i + "行." + "客户[" + name + "]已存在";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(2, i);
			alias = cell.getContents().trim();

			cell = sheet.getCell(3, i);
			customer = cell.getContents().trim();
			filters.clear();
			filters.add(Filter.eq("value", customer));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			SystemDict customerType = systemDictBaseService.find(filters);

			cell = sheet.getCell(4, i);
			memberRankName = cell.getContents().trim();
			if (ConvertUtil.isEmpty(memberRankName)) {
				msg = "第" + i + "行." + "会员等级名称未填";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("name", memberRankName));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			MemberRank memberRank = memberRankBaseService.find(filters);
			if (memberRank == null) {
				msg = "第" + i + "行." + "名称为[" + memberRankName + "]的会员等级不存在";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(5, i);
			saleOrgName = cell.getContents().trim();
			if (StringUtils.isBlank(saleOrgName)) {
				msg = "第" + i + "行." + "机构名称为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("name", saleOrgName));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			SaleOrg saleOrg = saleOrgService.find(filters);

			if (saleOrg == null) {
				msg = "第" + i + "行." + "机构【" + saleOrgName + "】不存在";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(6, i);//税率
			taxRate = cell.getContents().trim();
			BigDecimal taxRates = new BigDecimal(taxRate);

			cell = sheet.getCell(7, i);//是否签约
			isSigning = cell.getContents().trim();
			Integer isSignings = null;
			if (isSigning.equals("是")) {
				isSignings = 0;
			}
			else if (isSigning.equals("否")) {
				isSignings = 1;
			}

			cell = sheet.getCell(8, i);//签约时间
			date = cell.getContents().trim();
			SimpleDateFormat simple = new SimpleDateFormat("yyyy-MM-dd");
			Date qydate = simple.parse(date);

			saveStores(outTradeNo,
					name,
					alias,
					customerType,
					memberRank,
					saleOrg,
					taxRates,
					isSignings,
					qydate);

			success++;
		}
		int result = rows - 1;
		msg = "msg:" + "总数" + result + "行,成功导入" + success + " 行. ";
		return msg;
	}

	public Store saveStores(String outTradeNo, String name, String alias,
			SystemDict customerType, MemberRank memberRank, SaleOrg saleOrg,
			BigDecimal taxRates, Integer isSignings, Date qydate) {

		Store store = new Store();

		store.setOutTradeNo(outTradeNo);
		store.setName(name);
		store.setAlias(alias);
		store.setMemberRank(memberRank);
		store.setSaleOrg(saleOrg);
		store.setTaxRate(taxRates);
		store.setIsSigning(isSignings);
		store.setSigningTime(qydate);
		store.setCustomerType(customerType);

		store.setIsEnabled(true);
		store.setIsMainStore(false);
		store.setSn(Sequence.getInstance().getSequence(null));
		store.setIsReduceBalance(true);

		store.setType(Store.Type.distributor);
		save(store);
		storeBaseDao.getEntityManager().flush();
		return store;
	}
	
	@Override
	@Transactional
	public String storeImport(MultipartFile multipartFile) throws Exception {
		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}
		
		String outTradeNo; //客户编码
		String name; // 客户名称
		String alias; // 客户简称	
		String introduction;//客户介绍
		String grantCode; // 授权编码
		String dealerName;// 经销商姓名
		String accountStatusName;// 经销商状态
		String storeMemberName;//区域经理
		String businessTypeName;// 业务类型
		
		
		String memberRankName;// 价格类型
		String isEnabled;//是否启用    
		String platformPropertyName;// 平台性质   10
		String salesPlatformName;// 销售平台
		String saleOrgName;// 机构
		String taxRateStr;//税率
		String accountTypeCodeName;// 城市等级
		String areaProvinceName;// 省份
		String areaCityName;// 地级城市
		String areaRegionName;// 区县城市
		
		String countryName;//乡镇
		String headSaleAreaName;//销售区域
		String region;//区域
		String sbuName;// SBU
		String distributorTypeName;// 经销商类型
		String subTypeName;// 经销商子类型
		String salesCategory;//销售品类
		String contact;// 法人代表
		String franchisee;//总经销商
		String dealerRelationShip;// 经销商关系说明
		
		String identity; // 身份证信息
		String fixedNumber;// 固定号码
		String storePhone;//手机号码
		String activeDate;// 加盟时间
		String joinFileNumber;// 加盟档案编号
		String isJoinFalse;//加盟是否成功    30
		String cancelDate;// 解约时间
		String unfileNumber;// 解约档案编号
		String cancelInfoFlag;//解约时是否资料齐全
		String cancelReason;// 解约原因
		
		String needCautionPaidStr;// 应缴品牌保证金
		String realCautionPaidStr;// 实缴品牌保证金
		String unpaidCautionPaidStr;// 欠缴品牌保证金
		String salesDepositStr;//销量保证金
		String paymentStatus;// 缴纳情况/异常说明
		String cashClientFlagName;// 是否现金客户
		String currencyCode;// 货币
		String description;// 备注（门店地址）
		String contractSubject;//合同主体	
		String headAddress;//经销商地址
		String shippingStateStr;//是否可发货


		List<Filter> filters = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		for (int i = 1; i < rows; i++) {
			StoreMember storeMember = null;
			Area area = null;
			SalesArea salesArea = null;
			Boolean isEnabledFlag = false;
			Boolean isJoinFalseFlag = false;
			Boolean cancelInfoFlagFlag = false;
			BigDecimal needCautionPaid = BigDecimal.ZERO;
			BigDecimal realCautionPaid = BigDecimal.ZERO;
			BigDecimal unpaidCautionPaid = BigDecimal.ZERO;
			BigDecimal salesDeposit = BigDecimal.ZERO;
			BigDecimal taxRate = BigDecimal.ZERO;
			
			//客户编码
			cell = sheet.getCell(0, i);
			outTradeNo = cell.getContents().trim();
			if (ConvertUtil.isEmpty(outTradeNo)) {
				msg = "第" + i + "行." + "客户编码未填";
				ExceptionUtil.throwServiceException(msg);
			}
			Long count = count(Filter.eq("outTradeNo", outTradeNo));
			if (count.longValue() > 1) {
				msg = "第" + i + "行." + "ERP客户编码[" + outTradeNo + "]数据库已存在多个";
				ExceptionUtil.throwServiceException(msg);
			}
			// 客户名称
			cell = sheet.getCell(1, i);
			name = cell.getContents().trim();
			if (ConvertUtil.isEmpty(name)) {
				msg = "第" + i + "行." + "客户名称未填";
				ExceptionUtil.throwServiceException(msg);
			}
			if (count.longValue() == 1) {
				if (count(Filter.eq("name", name)) > 1) {
					msg = "第" + i + "行." + "客户[" + name + "]数据库已存在多个";
					ExceptionUtil.throwServiceException(msg);
				}
			}else{
				if (count(Filter.eq("name", name)) > 0) {
					msg = "第" + i + "行." + "客户[" + name + "]已存在";
					ExceptionUtil.throwServiceException(msg);
				}
			}
			// 客户简称	
			cell = sheet.getCell(2, i);
			alias = cell.getContents().trim();
			//客户介绍
			cell = sheet.getCell(3, i);
			introduction = cell.getContents().trim();
			// 授权编码
			cell = sheet.getCell(4, i);
			grantCode = cell.getContents().trim();
			// 经销商姓名
			cell = sheet.getCell(5, i);
			dealerName = cell.getContents().trim();
			// 经销商状态
			cell = sheet.getCell(6, i);
			accountStatusName = cell.getContents();
			SystemDict distributorStatus = null;
			if (!StringUtils.isBlank(accountStatusName)) {
				filters.clear();
				filters.add(Filter.eq("code", "distributorStatus"));
				filters.add(Filter.eq("value", accountStatusName.trim()));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				filters.add(Filter.isNotNull("parent"));
				List<SystemDict> distributorStatusList = systemDictBaseService.findList(null,filters,null);
				if (distributorStatusList != null && distributorStatusList.size() > 0) {
					distributorStatus = distributorStatusList.get(0);
				}
				else {
					msg = "第" + i + "行." + "经销商状态不存在";
					ExceptionUtil.throwServiceException(msg);
				}
			}
			//区域经理
			cell = sheet.getCell(7, i);
			storeMemberName = cell.getContents().trim();
			if (!StringUtils.isEmpty(storeMemberName)) {
				filters.clear();
				filters.add(Filter.eq("name", storeMemberName));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				filters.add(Filter.eq("isDefault", Boolean.valueOf(true)));
				filters.add(Filter.eq("isEnabled", Boolean.valueOf(true)));
				storeMember = storeMemberService.find(filters);
				if (storeMember == null) {
					msg = "第"+ i + "行." + "用户名为["+ storeMemberName+ "]的区域经理不存在";
					ExceptionUtil.throwServiceException(msg);
				}
			}else {
				msg = "第" + i + "行." + "用户名为[" + storeMemberName + "]的区域经理不能为空";
				ExceptionUtil.throwServiceException(msg);
			}
			// 业务类型
			cell = sheet.getCell(8, i);
			businessTypeName = cell.getContents().trim();
			if (StringUtils.isBlank(businessTypeName)) {
				msg = "第" + i + "行." + "业务类型不能为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("code", "businessType"));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("value",businessTypeName.trim()));
			filters.add(Filter.isNotNull("parent"));
			SystemDict businessTypeSystemDict = systemDictBaseService.find(filters);
			if(ConvertUtil.isEmpty(businessTypeSystemDict)){
				msg = "第"+ i + "行." + "业务类型为["+ businessTypeName+ "]的不存在";
				ExceptionUtil.throwServiceException(msg);
			}
			// 价格类型
			cell = sheet.getCell(9, i);
			memberRankName = cell.getContents().trim();
			if (ConvertUtil.isEmpty(memberRankName)) {
				msg = "第" + i + "行." + "会员等级名称未填";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("name", memberRankName));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			MemberRank memberRank = memberRankBaseService.find(filters);
			if (memberRank == null) {
				msg = "第" + i + "行." + "名称为[" + memberRankName + "]的会员等级不存在";
				ExceptionUtil.throwServiceException(msg);
			}
			//是否启用    
			cell = sheet.getCell(10, i);
			isEnabled = cell.getContents().trim();
			if (!StringUtils.isEmpty(isEnabled)) {
				if ("是".equals(isEnabled)) {
					isEnabledFlag = true;
				}
			}
			// 平台性质  
			cell = sheet.getCell(11, i);
			platformPropertyName = cell.getContents().trim();
			Integer platformProperty = 0;
			if (platformPropertyName != null) {
				if ("运营管理中心".equals(platformPropertyName)) {
					platformProperty = 0;
				}
				else if ("控股合资管理中心".equals(platformPropertyName)) {
					platformProperty = 1;
				}
				else if ("营销服务中心".equals(platformPropertyName)) {
					platformProperty = 2;
				}
				else if ("营销管理中心".equals(platformPropertyName)) {
					platformProperty = 3;
				}
			}
			// 销售平台
			cell = sheet.getCell(12, i);
			salesPlatformName = cell.getContents().trim();
			if (StringUtils.isBlank(salesPlatformName)) {
				msg = "第" + i + "行." + "销售平台名称为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("name", salesPlatformName));
			filters.add(Filter.eq("isSellSaleOrg", 1));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			SaleOrg salesPlatform = saleOrgService.find(filters);
			if (salesPlatform == null) {
				msg = "第" + i + "行." + "销售平台不存在";
				ExceptionUtil.throwServiceException(msg);
			}
			// 机构
			cell = sheet.getCell(13, i);
			saleOrgName = cell.getContents().trim();
			if (StringUtils.isBlank(saleOrgName)) {
				msg = "第" + i + "行." + "机构名称为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("name", saleOrgName));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			SaleOrg saleOrg = saleOrgService.find(filters);
			if (saleOrg == null) {
				msg = "第" + i + "行." + "机构【" + saleOrgName + "】不存在";
				ExceptionUtil.throwServiceException(msg);
			}
			//税率
			cell = sheet.getCell(14, i);
			taxRateStr = cell.getContents();
			if (!StringUtils.isEmpty(taxRateStr)) {
				try {
					taxRate = new BigDecimal(taxRateStr);
				}catch (Exception e) {
					msg = "第" + i + "行." + "行,税率格式有误;";
					ExceptionUtil.throwServiceException(msg);
				}
			}
			// 城市等级
			cell = sheet.getCell(15, i);
			accountTypeCodeName = cell.getContents();
			Integer accountTypeCode = 2;
			if (accountTypeCodeName != null) {
				if ("省级".equals(accountTypeCodeName)) {
					accountTypeCode = 0;
				}
				else if ("地市级".equals(accountTypeCodeName)) {
					accountTypeCode = 1;
				}
				else if ("区县级".equals(accountTypeCodeName)) {
					accountTypeCode = 2;
				}
				else if ("乡镇级".equals(accountTypeCodeName)) {
					accountTypeCode = 3;
				}
			}
			// 省份
			String area_full_name = "";
			cell = sheet.getCell(16, i);
			areaProvinceName = cell.getContents();
			if (!StringUtils.isEmpty(areaProvinceName)) {
				area_full_name = areaProvinceName;
			}
			// 地级城市
			cell = sheet.getCell(17, i);
			areaCityName = cell.getContents();
			if (!StringUtils.isEmpty(areaCityName)) {
				area_full_name += areaCityName;
			}
			// 区县城市
			cell = sheet.getCell(18, i);
			areaRegionName = cell.getContents();
			if (!StringUtils.isEmpty(areaRegionName)) {
				area_full_name += areaRegionName;
			}
			if (area_full_name != null) {
				filters.clear();
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				filters.add(Filter.eq("fullName", area_full_name));
				area = areaBaseService.find(filters);
			}
			//乡镇
			cell = sheet.getCell(19, i);
			countryName = cell.getContents().trim();
			//销售区域
			cell = sheet.getCell(20, i);
			headSaleAreaName = cell.getContents().trim();
			if (headSaleAreaName != null) {
				filters.clear();
				filters.add(Filter.eq("fullName", headSaleAreaName));
				salesArea = salesAreaService.find(filters);
			}
			//区域
			cell = sheet.getCell(21, i);
			region = cell.getContents().trim();
			// SBU
			cell = sheet.getCell(22, i);
			sbuName = cell.getContents().trim();
			if (StringUtils.isBlank(sbuName)) {
				msg = "第" + i + "行." + "SBU不能为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("code", "sbu"));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.eq("value",sbuName.trim()));
			filters.add(Filter.isNotNull("parent"));
			SystemDict sbuSystemDict = systemDictBaseService.find(filters);
			if(ConvertUtil.isEmpty(sbuSystemDict)){
				msg = "第"+ i + "行." + "SBU为["+ sbuName+ "]的不存在";
				ExceptionUtil.throwServiceException(msg);
			}
			// 经销商类型
			cell = sheet.getCell(23, i);
			distributorTypeName = cell.getContents().trim();
			Integer distributorType = 0;
			if (distributorTypeName != null) {
				if ("国内经销商".equals(distributorTypeName)) {
					distributorType = 0;
				}
				else if ("国际经销商".equals(distributorTypeName)) {
					distributorType = 1;
				}
				else if ("国产产品经销商".equals(distributorTypeName)) {
					distributorType = 2;
				}
				else if ("进口产品经销商".equals(distributorTypeName)) {
					distributorType = 3;
				}
			}
			// 经销商子类型
			cell = sheet.getCell(24, i);
			subTypeName = cell.getContents().trim();
			Integer subType = null;
			if (subTypeName != null) {
				if ("总经销商".equals(subTypeName)) {
					subType = 0;
				}
				else if ("省会城市经销商".equals(subTypeName)) {
					subType = 1;
				}
				else if ("平台经销商".equals(subTypeName)) {
					subType = 2;
				}
				else if ("经销商".equals(subTypeName)) {
					subType = 3;
				}
				else if ("分销商".equals(subTypeName)) {
					subType = 4;
				}
			}
			//销售品类
			cell = sheet.getCell(25, i);
			salesCategory = cell.getContents();
			// 法人代表
			cell = sheet.getCell(26, i);
			contact = cell.getContents().trim();
			//总经销商
			cell = sheet.getCell(27, i);
			franchisee = cell.getContents().trim();
			// 经销商关系说明
			cell = sheet.getCell(28, i);
			dealerRelationShip = cell.getContents().trim();
			// 身份证信息
			cell = sheet.getCell(29, i);
			identity = cell.getContents().trim();
			if (StringUtils.isBlank(identity)) {
				msg = "第" + i + "行." + "身份证为空";
				ExceptionUtil.throwServiceException(msg);
			}
			// 固定号码
			cell = sheet.getCell(30, i);
			fixedNumber = cell.getContents().trim();
			//手机号码
			cell = sheet.getCell(31, i);
			storePhone = cell.getContents().trim();
			if (StringUtils.isBlank(storePhone)) {
				msg = "第" + i + "行." + "手机号为空";
				ExceptionUtil.throwServiceException(msg);
			}
			// 加盟时间
			cell = sheet.getCell(32, i);
			activeDate = cell.getContents();
			Date activeDateStr = null;
			if (activeDate != "" && !isValidDate(activeDate)) {
				msg = "第" + i + "行." + "加盟时间格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			if (activeDate != "") {
				activeDateStr = DateUtil.convert(activeDate.trim()+ " 00:00:00");
			}
			// 加盟档案编号
			cell = sheet.getCell(33, i);
			joinFileNumber = cell.getContents().trim();
			//加盟是否成功   
			cell = sheet.getCell(34, i);
			isJoinFalse = cell.getContents().trim();
			if (!StringUtils.isEmpty(isJoinFalse)) {
				if ("是".equals(isJoinFalse)) {
					isJoinFalseFlag = true;
				}
			}
			// 解约时间
			cell = sheet.getCell(35, i);
			cancelDate = cell.getContents();
			Date cancelDateStr = null;
			if (cancelDate != "" && !isValidDate(cancelDate)) {
				msg = "第" + i + "行." + "解约时间格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			if (cancelDate != "") {	
				cancelDateStr = DateUtil.convert(cancelDate.trim()+ " 00:00:00");
			}
			// 解约档案编号
			cell = sheet.getCell(36, i);
			unfileNumber = cell.getContents().trim();
			//解约时是否资料齐全
			cell = sheet.getCell(37, i);
			cancelInfoFlag = cell.getContents().trim();
			if (!StringUtils.isEmpty(cancelInfoFlag)) {
				if ("是".equals(cancelInfoFlag)) {
					cancelInfoFlagFlag = true;
				}
			}
			// 解约原因
			cell = sheet.getCell(38, i);
			cancelReason = cell.getContents().trim();
			// 应缴品牌保证金
			cell = sheet.getCell(39, i);
			needCautionPaidStr = cell.getContents().trim();
			if (!StringUtils.isEmpty(needCautionPaidStr)) {
				try {
					needCautionPaid = new BigDecimal(needCautionPaidStr);
				}
				catch (Exception e) {
					msg = "第" + i + "行." + "行,应缴品牌保证金格式有误;";
					ExceptionUtil.throwServiceException(msg);
				}
			}
			// 实缴品牌保证金
			cell = sheet.getCell(40, i);
			realCautionPaidStr = cell.getContents().trim();
			if (!StringUtils.isEmpty(realCautionPaidStr)) {
				try {
					realCautionPaid = new BigDecimal(realCautionPaidStr);
				}
				catch (Exception e) {
					msg = "第" + i + "行." + "行,实缴品牌保证金格式有误;";
					ExceptionUtil.throwServiceException(msg);
				}
			}
			// 欠缴品牌保证金
			cell = sheet.getCell(41, i);
			unpaidCautionPaidStr = cell.getContents().trim();
			if (!StringUtils.isEmpty(unpaidCautionPaidStr)) {
				try {
					unpaidCautionPaid = new BigDecimal(unpaidCautionPaidStr);
				}
				catch (Exception e) {
					msg = "第" + i + "行." + "行,欠缴品牌保证金格式有误;";
					ExceptionUtil.throwServiceException(msg);
				}
			}
			//销量保证金
			cell = sheet.getCell(42, i);
			salesDepositStr = cell.getContents().trim();
			if (!StringUtils.isEmpty(salesDepositStr)) {
				try {
					salesDeposit = new BigDecimal(salesDepositStr);
				}
				catch (Exception e) {
					msg = "第" + i + "行." + "行,销量保证金格式有误;";
					ExceptionUtil.throwServiceException(msg);
				}
			}
			// 缴纳情况/异常说明
			cell = sheet.getCell(43, i);
			paymentStatus = cell.getContents().trim();
			// 是否现金客户
			cell = sheet.getCell(44, i);
			cashClientFlagName = cell.getContents().trim();
			Boolean cashClientFlag = false;
			if (cashClientFlag != null && "是".equals(cashClientFlagName)) {
				cashClientFlag = true;
			}
			// 货币
			cell = sheet.getCell(45, i);
			currencyCode = cell.getContents().trim();
			// 备注（门店地址）
			cell = sheet.getCell(46, i);
			description = cell.getContents().trim();
			//合同主体	
			cell = sheet.getCell(47, i);
			contractSubject = cell.getContents().trim();
			//经销商地址
			cell = sheet.getCell(48, i);
			headAddress = cell.getContents().trim();
			//是否可发货   
			Integer shippingState = 0;
			cell = sheet.getCell(49, i);
			shippingStateStr = cell.getContents().trim();
			if (!StringUtils.isEmpty(shippingStateStr)) {
				if ("是".equals(shippingStateStr)) {
					shippingState = 1;
				}
			}
			
			Store store = saveStore(outTradeNo,
					name,
					alias,
					memberRank,
					sbuSystemDict,
					saleOrg,
					platformProperty,
					salesPlatform,
					businessTypeSystemDict,
					accountTypeCode,
					dealerName,
					grantCode,
					currencyCode,
					cashClientFlag,
					distributorStatus,
					identity,
					distributorType,
					subType,
					activeDateStr,
					cancelDateStr,
					contact,
					dealerRelationShip,
					joinFileNumber,
					cancelReason,
					unfileNumber,
					fixedNumber,
					description,
					introduction,
					storeMember,
					isEnabledFlag,
					taxRate,
					area,
					countryName,
					salesArea,
					region,
					salesCategory,
					franchisee,
					storePhone,
					isJoinFalseFlag,
					cancelInfoFlagFlag,
					needCautionPaid,
					realCautionPaid,
					unpaidCautionPaid,
					salesDeposit,
					paymentStatus,
					headAddress,
					contractSubject,
					shippingState);
			
			success++;
		}
		int result = rows - 1;
		msg = "msg:" + "总数" + result + "行,成功导入" + success + " 行. ";
		return msg;
	}

	public Store saveStore(String outTradeNo, String name, String alias,
			MemberRank memberRank, SystemDict sbu, SaleOrg saleOrg,
			Integer platformProperty, SaleOrg salesPlatform,
			SystemDict businessType, Integer accountTypeCode,
			String dealerName, String grantCode, String currencyCode,
			Boolean cashClientFlag, SystemDict distributorStatus,
			String identity, Integer distributorType, Integer subType,
			Date activeDate, Date cancelDate, String contact,
			String dealerRelationShip, String joinFileNumber,
			String cancelReason, String unfileNumber, String fixedNumber,
			String description, String introduction, StoreMember storeMember,
			Boolean isEnabledFlag, BigDecimal taxRate, Area area,
			String countryName, SalesArea salesArea, String region,
			String salesCategory, String franchisee, String storePhone,
			Boolean isJoinFalseFlag, Boolean cancelInfoFlag,
			BigDecimal needCautionPaid, BigDecimal realCautionPaid,
			BigDecimal unpaidCautionPaid, BigDecimal salesDeposit,
			String paymentStatus, String headAddress,String contractSubject,
			Integer shippingState) {
		
		List<Filter> filters = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("outTradeNo", outTradeNo));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		Store store = storeBaseService.find(filters);
		if(ConvertUtil.isEmpty(store)){
			store = new Store();
		}
		store.setOutTradeNo(outTradeNo);
		store.setName(name);
		store.setAlias(alias);
		store.setMemberRank(memberRank);
		store.setSbu(sbu);
		store.setSaleOrg(saleOrg);
		store.setPlatformProperty(platformProperty);
		store.setSalesPlatform(salesPlatform);
		store.setBusinessType(businessType);
		store.setAccountTypeCode(accountTypeCode);
		store.setDealerName(dealerName);
		store.setGrantCode(grantCode);
		store.setCurrencyCode(currencyCode);
		store.setCashClientFlag(cashClientFlag);
		store.setDistributorStatus(distributorStatus);
		store.setIdentity(identity);
		store.setDistributorType(distributorType);
		store.setSubType(subType);
		store.setActiveDate(activeDate);
		store.setCancelDate(cancelDate);
		store.setContact(contact);
		store.setDealerRelationShip(dealerRelationShip);
		store.setJoinFileNumber(joinFileNumber);
		store.setCancelReason(cancelReason);
		store.setUnfileNumber(unfileNumber);
		store.setFixedNumber(fixedNumber);
		store.setDescription(description);
		store.setSalesDeposit(salesDeposit);
		store.setPaymentStatus(paymentStatus);
		store.setIntroduction(introduction);
		store.setStoreMember(storeMember);
		store.setIsEnabled(isEnabledFlag);
		store.setTaxRate(taxRate);
		store.setHeadNewArea(area);
		store.setCountryName(countryName);
		store.setSalesArea(salesArea);
		store.setRegion(region);
		store.setSalesCategory(salesCategory);
		store.setFranchisee(franchisee);
		store.setHeadPhone(storePhone);
		store.setIsJoinFalse(isJoinFalseFlag);
		store.setCancelInfoFlag(cancelInfoFlag);
		store.setNeedCautionPaid(needCautionPaid);
		store.setRealCautionPaid(realCautionPaid);
		store.setUnpaidCautionPaid(unpaidCautionPaid);
		store.setPaymentStatus(paymentStatus);
		store.setHeadAddress(headAddress);
		store.setIsMainStore(false);
		store.setSn(Sequence.getInstance().getSequence(null));
		store.setIsReduceBalance(true);
		store.setCreateBy(storeMemberBaseService.getCurrent());
		store.setCreateDate(new Date());
		store.setType(Store.Type.distributor);
		store.setContractSubject(contractSubject);
		store.setShippingState(shippingState);
		if(ConvertUtil.isEmpty(store.getId())){
			save(store);
		}else{
			update(store);
		}
		storeBaseDao.getEntityManager().flush();
		return store;
	}

	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String name, String sn,
			String outTradeNo, String mobile, String serviceTelephone,
			Integer[] type, String outShopName, Long saleOrgId,
			Long companyInfoId, Long memberRankId, String createBy,
			String saleOrgName, Boolean isEnabled, Integer isSelect,
			Integer isCustomer, String alias,String grantCode,
			Long storeMemberId, String region, Long[] distributorStatusId,
			String headAddress,String user, Pageable pageable, Long sbuId) {
		return storeBaseDao.findPage(name,
				sn,
				outTradeNo,
				mobile,
				serviceTelephone,
				type,
				outShopName,
				saleOrgId,
				companyInfoId,
				memberRankId,
				createBy,
				saleOrgName,
				isEnabled,
				isSelect,
				isCustomer,
				alias,
				grantCode,
				storeMemberId,
				region,
				distributorStatusId,
				headAddress,
				user,
				pageable,
				sbuId);
	}
	
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findMobileData(Map<String, Object> param) {
	    return storeBaseDao.findMobileData(param);
	}

	@Transactional(readOnly = true)
	public List<Integer> getTypes() {
		List<Integer> types = new Store().getAllTypeInt();
		for (Iterator<Integer> iterator = types.iterator(); iterator.hasNext();) {
			int type = iterator.next();
			if (type == 0 || type == 1 || type == 2 || type == 3) {
				iterator.remove();
			}
		}
		return types;
	}

	@Override
	@Transactional(readOnly = true)
	public Integer count(String name, String sn, String outTradeNo,
			String mobile, String serviceTelephone, Integer[] type,
			String outShopName, Long saleOrgId, Long companyInfoId,
			Long memberRankId, Boolean isEnabled, Pageable pageable,
			Integer page, Integer size) {
		return storeBaseDao.count(name,
				sn,
				outTradeNo,
				mobile,
				serviceTelephone,
				type,
				outShopName,
				saleOrgId,
				companyInfoId,
				memberRankId,
				isEnabled,
				pageable,
				page,
				size);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findItemList(String name,String sn, 
			String outTradeNo,String mobile,String serviceTelephone,
			Integer[] type,String outShopName,Long saleOrgId,Long companyInfoId,
			Long memberRankId,Boolean isEnabled,Long[] ids,Integer page,Integer size,
			Long storeMemberId,String region,Long[] distributorStatusId,String headAddress) {
		
		return storeBaseDao.findItemList(name, sn, outTradeNo, mobile, serviceTelephone, 
				type, outShopName, saleOrgId, companyInfoId, memberRankId, isEnabled, ids, 
				page, size, storeMemberId, region, distributorStatusId, headAddress);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findStoreAddressPage(String mobile,
			String consignee, Long storeId, String address,String areaName, Pageable pageable) {

		return storeBaseDao.findStoreAddressPage(mobile,
				consignee,
				storeId,
				address,
				areaName,
				pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findStoreAddressList(Long storeId) {
		return storeBaseDao.findStoreAddressList(storeId);
	}

	public void saveStoreBalance(Store store) {

		List<AccountParameter> accountParameters = accountParameterService.findList(1,
				null,
				null);
		AccountParameter accountParameter = accountParameters.get(0);
		String currentDate = DateUtil.convert(DateUtil.addDate("MM",
				1,
				DateUtil.convert(accountParameter.getBalanceDate(), "yyyy-MM")),
				"yyyy-MM");

		StoreBalance storeBalance = new StoreBalance();
		storeBalance.setStore(store);
		storeBalance.setBeginAmount(BigDecimal.ZERO);
		storeBalance.setBalanceDate(currentDate);
		storeBalanceService.save(storeBalance);
	}

	@Override
	@Transactional(readOnly = true)
	public Store findStoreByOutTradeNo(String outTradeNo, Long companyInfoId) {
		return storeBaseDao.findStoreByOutTradeNo(outTradeNo, companyInfoId);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findListByMember(Long memberId) {
		return storeBaseDao.findListByMember(memberId);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findStorePageWithBalance(String name,
			String outTradeNo,String grantCode, Pageable pageable) {
		// TODO Auto-generated method stub
		return storeBaseDao.findStorePageWithBalance(name, outTradeNo, grantCode, pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findBusinessCategory(Store store) {

		return storeBaseDao.findBusinessCategory(store);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findBusinessRecord(Store store) {

		return storeBaseDao.findBusinessRecord(store);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findCautionMoney(Store store) {

		return storeBaseDao.findCautionMoney(store);
	}

	private boolean isValidDate(String str) {
		boolean convertSuccess = true;
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			format.setLenient(false);
			format.parse(str);
		}
		catch (ParseException e) {
			convertSuccess = false;
		}
		return convertSuccess;
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findListByStoreId(Long id) {
		return storeBaseDao.findListByStoreId(id);
	}

	@Override
	@Transactional(readOnly = true)
	public Integer findMaxSn() {
		return storeBaseDao.findMaxSn();
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findStoreContract(Store store) {
		return storeBaseDao.findStoreContract(store);
	}

	@Override
	public List<Map<String, Object>> findStoreCooperation(Store store) {
		return storeBaseDao.findStoreCooperation(store);
	}

	// 客户地址excel导入大自然
	@Override
	@Transactional
	public String addressImport(MultipartFile multipartFile) throws Exception {
		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];

		int rows = sheet.getRows();

		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}

		String name; // 客户名称
		String outTradeNo; // ERP客户编码
		String consignee; // 收货人
		String mobile; // 收货人电话
		String areaName; // 收货地区
		String salesAreaName; // 销售区域
		String address; // 收货地址
		String zipCode; // 收货地区邮编
		String addressType; // 地址类型
		String externalNumber; // 外部编号
		String isDefault; // 是否默认

		List<Filter> filters = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		int er = 0;

		for (int i = 1; i < rows; i++) {

			cell = sheet.getCell(0, i);
			outTradeNo = cell.getContents().trim();

			CompanyInfo companyInfo = companyInfoBaseService.find(WebUtils.getCurrentCompanyInfoId());
			if (ConvertUtil.isEmpty(outTradeNo)) {
				msg = "第" + i + "行." + "ERP客户编码未填";
				ExceptionUtil.throwServiceException(msg);
			}
			Store storeByNo = storeBaseDao.findStoreByErpNo(outTradeNo);
			if (storeByNo == null) {
				msg = "第" + i + "行." + "ERP客户编码[" + outTradeNo + "]不存在";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(1, i);
			name = cell.getContents().trim();
			/*
			 * filters.clear(); filters.add(Filter.eq("name", name));
			 * List<Store> findStoreName = storeBaseService.findList(null,
			 * filters, null); if (findStoreName.size() == 0) { msg = "第" + i +
			 * "行." + "客户名称不存在"; ExceptionUtil.throwServiceException(msg); }
			 */

			cell = sheet.getCell(2, i);
			consignee = cell.getContents().trim();

			cell = sheet.getCell(3, i);
			mobile = cell.getContents().trim();

			cell = sheet.getCell(4, i);
			areaName = cell.getContents().trim();
			Area receivingArea = null;
			if (ConvertUtil.isEmpty(areaName)) {
				msg = "第" + i + "行." + "收货地区未填";
				ExceptionUtil.throwServiceException(msg);
			}
			else {
				filters.clear();
				filters.add(Filter.like("fullName", "%" + areaName + "%"));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				List<Area> area = areaBaseService.findList(null, filters, null);
				if (area != null) {
					if (area.size() > 0) {
						receivingArea = area.get(0);
					}
					else {
						msg = "第" + i + "行." + "收货地区不存在";
						ExceptionUtil.throwServiceException(msg);
					}
				}

			}

			cell = sheet.getCell(5, i);
			salesAreaName = cell.getContents().trim();
			SalesArea receivingSalesArea = null;
			if (ConvertUtil.isEmpty(salesAreaName)) {
				msg = "第" + i + "行." + "销售区域未填";
				ExceptionUtil.throwServiceException(msg);
			}
			else {
				filters.clear();
				filters.add(Filter.like("fullName", "%" + salesAreaName + "%"));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				List<SalesArea> areas = salesAreaService.findList(null,
						filters,
						null);

				if (areas != null) {
					if (areas.size() > 0) {
						receivingSalesArea = areas.get(0);
					}
					else if (areas.size() == 0) {
						msg = "第" + i + "行." + "销售区域不存在";
						ExceptionUtil.throwServiceException(msg);
					}
				}
			}

			cell = sheet.getCell(6, i);
			address = cell.getContents().trim();

			cell = sheet.getCell(7, i);
			zipCode = cell.getContents().trim();

			cell = sheet.getCell(8, i);
			addressType = cell.getContents().trim();

			Integer addressTypeName = 0;
			if (addressType != null) {
				if ("经销商地址".equals(addressType)) {
					addressTypeName = 0;
				}
				else if ("收货地址".equals(addressType)) {
					addressTypeName = 1;
				}
				else if ("收单地址".equals(addressType)) {
					addressTypeName = 2;
				}
				else if ("收单收货地址".equals(addressType)) {
					addressTypeName = 3;
				}
			}

			cell = sheet.getCell(9, i);
			externalNumber = cell.getContents().trim();
			if (ConvertUtil.isEmpty(externalNumber)) {
				msg = "第" + i + "行." + "外部编号未填";
				ExceptionUtil.throwServiceException(msg);
			}
			else {
				filters.clear();
				filters.add(Filter.like("outTradeNo", externalNumber));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				List<StoreAddress> findListByAddress = storeAddressService.findList(null,
						filters,
						null);
				// 查询是否存在外部编号
				if (findListByAddress.size() > 0) {
					msg = "第" + i + "行." + "外部编号已存在";
					ExceptionUtil.throwServiceException(msg);
				}
			}

			cell = sheet.getCell(10, i);
			isDefault = cell.getContents().trim();
			Integer isDefaults = null;
			Store store = storeBaseDao.findStoreByErpNo(outTradeNo);
			filters.clear();
			filters.add(Filter.eq("store", store));
			filters.add(Filter.eq("isDefault", 1));
			List<StoreAddress> findStoreByAddressDefault = storeAddressService.findList(null,
					filters,
					null);
			if (findStoreByAddressDefault.size() > 0) {
				// 集合中有默认数据，exl导入全部默认为否
				isDefaults = 0;
			}
			else {
				// 没有默认数据
				if (isDefault.equals("是")) {
					filters.clear();
					filters.add(Filter.eq("store", store));
					filters.add(Filter.eq("isDefault", 1));
					List<StoreAddress> findStoreByAddressDefault1 = storeAddressService.findList(null,
							filters,
							null);
					// 集合长度大于0证明有默认数据
					if (findStoreByAddressDefault1.size() > 0) {
						isDefaults = 0;
					}
					else {
						isDefaults = 1;
					}

				}
				else {
					msg = "第" + i + "行." + "该客户没有指定默认的收货地址";
					ExceptionUtil.throwServiceException(msg);
				}
			}

			System.out.println(er + 1);

			saveStoreDZ(name,
					outTradeNo,
					consignee,
					mobile,
					receivingArea,
					receivingSalesArea,
					address,
					zipCode,
					addressTypeName,
					externalNumber,
					isDefaults,
					1);

			success++;
		}

		int result = rows - 1;
		msg = "msg:" + "总数" + result + "行,成功导入" + success + " 行. ";
		return msg;

	}

	// 更新解析到的exl文件并保存
	private void saveStoreDZ(String name, String outTradeNo, String consignee,
			String mobile, Area receivingArea, SalesArea receivingSalesArea,
			String address, String zipCode, Integer addressTypeName,
			String externalNumber, Integer isDefaults, Integer isImport) {

		Store store = storeBaseDao.findStoreByErpNo(outTradeNo);

		StoreAddress storeAddress = new StoreAddress();
		storeAddress.setConsignee(consignee);
		storeAddress.setStore(store);
		storeAddress.setArea(receivingArea);
		storeAddress.setSalesArea(receivingSalesArea);
		storeAddress.setZipCode(zipCode);
		storeAddress.setAddress(address);
		storeAddress.setMobile(mobile);
		storeAddress.setAddressType(addressTypeName);
		storeAddress.setOutTradeNo(externalNumber);
		storeAddress.setIsDefault(isDefaults == 1 ? true : false);
		storeAddress.setIsImport(isImport);

		storeAddressService.save(storeAddress);

		List<StoreAddress> storeAddressList = new ArrayList<StoreAddress>();
		storeAddressList.add(storeAddress);

		store.getStoreAddress().addAll(storeAddressList);
		update(store);
	}

	public void synchronization(String startTime, String endTime,
			String currentTime) {
//		// 获取昨天日期
//		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//		Calendar c = Calendar.getInstance();
//		c.setTime(new Date());
//		c.add(Calendar.DATE, -1);
//		Date d = c.getTime();
//		String day = format.format(d);
		store(startTime, endTime, currentTime);
		storeAddress(startTime, endTime, currentTime);
	}

	public void store(String startTime, String endTime, String currentTime) {
		List<Map<String, Object>> storeList = storeBaseDao.findStore(startTime,
				endTime,
				currentTime);
		if (storeList.size() > 0) {
			for (Map<String, Object> sl : storeList) {
				if (sl.get("id") != null) {
					Store st = find(Long.parseLong(sl.get("id").toString()));
					//intfOrderMessageToService.saveStoreIntf(st, null, 1);
				}
			}
		}
	}

	public void storeAddress(String startTime, String endTime,
			String currentTime) {
		List<Map<String, Object>> storeAddressList = storeBaseDao.findStoreAddress(startTime,
				endTime,
				currentTime);
		if (storeAddressList.size() > 0) {
			for (Map<String, Object> sl : storeAddressList) {
				if (sl.get("id") != null) {
					StoreAddress sa = storeAddressService.find(Long.parseLong(sl.get("id")
							.toString()));
					intfOrderMessageToService.saveStoreIntf(sa.getStore(),
							sa,
							1);
				}
			}
		}
	}

	// 发票抬头excel导入大自然
	@Override
	@Transactional
	public String invoiceImport(MultipartFile multipartFile) throws Exception {
		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];

		int rows = sheet.getRows();
		System.out.println("rows : " + rows);

		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}

		String name; // 客户名称
		String outTradeNo; // ERP客户编码
		String invoiceTitle; // 发票抬头
		String zengzhishuiyhzh; // 银行帐户
		String kaihuyh; // 开户行名称
		String nashuishibiehao; // 纳税人识别号
		String zhucedianhua; // 开票电话
		String zhucedizhi; // 开票地址
		String isDefault; // 是否默认

		List<Filter> filters = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		for (int i = 1; i < rows; i++) {

			cell = sheet.getCell(0, i);
			outTradeNo = cell.getContents().trim();

			CompanyInfo companyInfo = companyInfoBaseService.find(WebUtils.getCurrentCompanyInfoId());
			if (ConvertUtil.isEmpty(outTradeNo)) {
				msg = "第" + i + "行." + "ERP客户编码未填";
				ExceptionUtil.throwServiceException(msg);
			}
			Store storeByNo = storeBaseDao.findStoreByErpNo(outTradeNo);
			if (storeByNo == null) {
				msg = "第" + i + "行." + "ERP客户编码[" + outTradeNo + "]不存在";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(1, i);
			name = cell.getContents().trim();
			/*
			 * filters.clear(); filters.add(Filter.eq("name", name));
			 * List<Store> findStoreName = storeBaseService.findList(null,
			 * filters, null); if (findStoreName.size() == 0) { msg = "第" + i +
			 * "行." + "客户名称不存在"; ExceptionUtil.throwServiceException(msg); }
			 */

			cell = sheet.getCell(2, i);
			invoiceTitle = cell.getContents().trim();

			cell = sheet.getCell(3, i);
			zengzhishuiyhzh = cell.getContents().trim();

			cell = sheet.getCell(4, i);
			kaihuyh = cell.getContents().trim();

			cell = sheet.getCell(5, i);
			nashuishibiehao = cell.getContents().trim();

			cell = sheet.getCell(6, i);
			zhucedianhua = cell.getContents().trim();

			cell = sheet.getCell(7, i);
			zhucedizhi = cell.getContents().trim();

			cell = sheet.getCell(8, i);
			isDefault = cell.getContents().trim();
			Integer isDefaults = null;
			Store store = storeBaseDao.findStoreByErpNo(outTradeNo);
			filters.clear();
			filters.add(Filter.eq("store", store));
			filters.add(Filter.eq("isDefault", 1));
			List<StoreInvoiceInfo> findStoreByAddressDefault = storeInvoiceInfoService.findList(null,
					filters,
					null);
			if (findStoreByAddressDefault.size() > 0) {
				// 集合中有默认数据，exl导入全部默认为否
				isDefaults = 0;
			}
			else {
				// 没有默认数据
				if (isDefault.equals("是")) {
					filters.clear();
					filters.add(Filter.eq("store", store));
					filters.add(Filter.eq("isDefault", 1));
					List<StoreInvoiceInfo> findStoreByAddressDefault1 = storeInvoiceInfoService.findList(null,
							filters,
							null);
					// 集合长度大于0证明有默认数据
					if (findStoreByAddressDefault1.size() > 0) {
						isDefaults = 0;
					}
					else {
						isDefaults = 1;
					}

				}
				else {
					msg = "第" + i + "行." + "该客户没有指定默认的发票抬头";
					ExceptionUtil.throwServiceException(msg);
				}
			}

			// 保存
			storeInvoiceSave(name,
					outTradeNo,
					invoiceTitle,
					zengzhishuiyhzh,
					kaihuyh,
					nashuishibiehao,
					zhucedianhua,
					zhucedizhi,
					isDefaults);

			success++;

		}

		int result = rows - 1;
		msg = "msg:" + "总数" + result + "行,成功导入" + success + " 行. ";
		return msg;
	}

	private void storeInvoiceSave(String name, String outTradeNo,
			String invoiceTitle, String zengzhishuiyhzh, String kaihuyh,
			String nashuishibiehao, String zhucedianhua, String zhucedizhi,
			Integer isDefaults) {
		// 获取当前解析的xls文件内容中的ERP客户编码
		Store store = storeBaseDao.findStoreByErpNo(outTradeNo);
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("store", store));
		filters.add(Filter.eq("invoiceTitle", invoiceTitle));
		filters.add(Filter.eq("nashuishibiehao", nashuishibiehao));
		List<StoreInvoiceInfo> storeInvoiceInfos = storeInvoiceInfoService.findList(null,
				filters,
				null);
		StoreInvoiceInfo sInfo = new StoreInvoiceInfo();
		// 集合中不存在重复的发货信息
		if (storeInvoiceInfos.size() == 0) {
			// 添加
			sInfo.setStore(store);
			sInfo.setInvoiceTitle(invoiceTitle);
			sInfo.setZengzhishuiyhzh(zengzhishuiyhzh);
			sInfo.setKaihuyh(kaihuyh);
			sInfo.setNashuishibiehao(nashuishibiehao);
			sInfo.setZhucedianhua(zhucedianhua);
			sInfo.setZhucedizhi(zhucedizhi);
			sInfo.setIsDefault(isDefaults == 1 ? true : false);
			sInfo.setIsImport(1);

			storeInvoiceInfoService.save(sInfo);

			List<StoreInvoiceInfo> sInvoiceInfos = new ArrayList<StoreInvoiceInfo>();
			sInvoiceInfos.add(sInfo);

			store.getStoreInvoiceInfos().addAll(sInvoiceInfos);
		}
		else {
			sInfo = storeInvoiceInfos.get(0);
			sInfo.setStore(store);
			sInfo.setInvoiceTitle(invoiceTitle);
			sInfo.setZengzhishuiyhzh(zengzhishuiyhzh);
			sInfo.setKaihuyh(kaihuyh);
			sInfo.setNashuishibiehao(nashuishibiehao);
			sInfo.setZhucedianhua(zhucedianhua);
			sInfo.setZhucedizhi(zhucedizhi);
			sInfo.setIsDefault(isDefaults == 1 ? true : false);
			sInfo.setIsImport(1);
			storeInvoiceInfoService.update(sInfo);

		}
		update(store);
	}

	@Override
	@Transactional(readOnly = true)
	public boolean findByRole(StoreMember storeMember, String roleName) {
		return storeBaseDao.findByRole(storeMember, roleName);
	}

	@Override
	public String findAreaCity(Area area) {
		String str = "";
		if(area.getParent()!=null){
			//市级
			Area city = areaBaseService.find(area.getParent().getId());
			str = city.getFullName();
		}else{
			str = area.getFullName();
		}
		return str;
	}

    @Override
    public List<Map<String, Object>> findStoreAttach(Long id, Integer type) {
        return storeBaseDao.findStoreAttach(id, type);
    }

	/**
	 * 查找客户状态系统词汇
	 * @param str 词汇值
	 * @return SystemDict
	 */
	public SystemDict findDistributorStatus(String str){
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "distributorStatus"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("value", str));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> systemDict = systemDictBaseService.findList(null,filters,null);
		if(systemDict==null||systemDict.size()<=0){
			ExceptionUtil.throwServiceException("找不到对应客户状态的系统词汇！");
		}
		return systemDictBaseService.findList(null,
				filters,
				null).get(0);
	}

	@Override
	public void storeStop(Store store, ActWf wf) {
		//客户状态变成终止
		store.setDistributorStatus(findDistributorStatus("终止"));
		//客户设置成未启用
		store.setIsEnabled(false);
		//客户是否可发货
		store.setShippingState(0);
		update(store);
		//查询客户关联的所有外部帐号禁用
		storeMemberBaseService.closeStoreByMember(store.getId());
		//查询出当前客户所有门店资料
		List<ShopInfo> si = findShopInfo(store);
		//调用门店停业整顿方法》》》结束
		shopInfoService.restructuring(si, "经销商终止，门店停业整顿",new Date(),wf);
	}

	/**
	 * 查询客户所有的门店资料
	 * @param store
	 *
	 */
	public List<ShopInfo> findShopInfo(Store store){
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("shopStatus", "正在营业"));
		filters.add(Filter.eq("store", store));
		filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
		return shopInfoService.findList(null, filters, null);
	}

	@Override
	public void updateStoreFinance(Store store) {
		Store pStore = find(store.getId());

		List<StoreInvoiceInfo> storeInvoiceInfos = store.getStoreInvoiceInfos();
		for (Iterator<StoreInvoiceInfo> iterator = storeInvoiceInfos.iterator(); iterator.hasNext();) {
			StoreInvoiceInfo storeInvoiceInfo = iterator.next();
			if (storeInvoiceInfo == null
					|| storeInvoiceInfo.getInvoiceTitle() == null) {
				iterator.remove();
			}
			storeInvoiceInfo.setStore(pStore);
		}
		pStore.getStoreInvoiceInfos().clear();
		pStore.getStoreInvoiceInfos().addAll(storeInvoiceInfos);

		//sbu保存
		List<StoreSbu> storeSbuList = store.getStoreSbu();
		for (Iterator<StoreSbu> iterator = storeSbuList.iterator(); iterator.hasNext();) {
			StoreSbu storeSbu = (StoreSbu) iterator.next();
			if (storeSbu == null
					|| storeSbu.getSbu() == null
					|| (storeSbu.getSbu() != null && storeSbu.getSbu().getId() == null)) {
				iterator.remove();
				continue;
			}
			Sbu sbu = sbuService.find(storeSbu.getSbu().getId());
			MemberRank memberRank = memberRankBaseService.find(storeSbu.getMemberRank()
					.getId());
			storeSbu.setSbu(sbu);
			storeSbu.setStore(pStore);
			storeSbu.setMemberRank(memberRank);
		}
		pStore.getStoreSbu().clear();
		pStore.getStoreSbu().addAll(storeSbuList);

		//字段变更检索
//		fieldAddFullLink(pStore,store);
		update(pStore);

		//saveIntfAtAm(pStore);
	}

	public void saveIntfAtAm(Store store) {
//		Long companyInfoId = store.getCompanyInfoId();
//		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
//		if ("nature".equals(companyInfo.getCompany_code())) {// nadev:dzrj
//			//amStoreToService.pushAmStore(store);//flag 0 新增并同步  1 保存并同步 2失效并同步
//		}
	}

	@Override
	public void updateStore1(Store store) {
		Store pStore = find(store.getId());
		List<StoreAddress> addressList = store.getStoreAddress();
		for (Iterator<StoreAddress> iterator = addressList.iterator(); iterator.hasNext();) {
			StoreAddress storeAddress = (StoreAddress) iterator.next();
			if (storeAddress == null || storeAddress.getAddress() == null) {
				iterator.remove();
			}
			if (storeAddress.getArea() != null
					&& storeAddress.getArea().getId() != null) {
				Area a = areaBaseService.find(storeAddress.getArea().getId());
				storeAddress.setArea(a);
			}
			if (storeAddress.getSalesArea() != null
					&& storeAddress.getSalesArea().getId() != null) {
				SalesArea salesArea = salesAreaService.find(storeAddress.getSalesArea()
						.getId());
				storeAddress.setSalesArea(salesArea);
			}
			storeAddress.setStore(pStore);
			//以地址明细id作为外部编号传到ERP,作为KA在ERP的地址外部编码的唯一标识
			if (storeAddress.getOutTradeNo() == null) {//原先的外部编号不修改，在ERP已存在唯一
				if (storeAddress.getId() != null) {
					storeAddress.setOutTradeNo(storeAddress.getId().toString());
				}
			}
			storeAddressService.update(storeAddress);
		}
		//字段变更检索
		update(pStore);
		// 更新收货地址
		if (WebUtils.getCurrentCompanyInfoId() == 9) {
			saveIntfAtCheck(pStore, null, 1);//flag 0 新增并同步  1 保存并同步 2失效并同步
		}
	}

	@Override
	public void storeExit(Store store, StoreExit storeExit, ActWf wf) {
		//客户状态变成退出
		store.setDistributorStatus(findDistributorStatus("退出"));
		//客户设置成未启用
		store.setIsEnabled(false);
		//停业整顿时间
		Date reorganizeDate = storeExit.getExitTime();
		store.setCancelReason(storeExit.getCancelReason());
		store.setUnfileNumber(storeExit.getUnfileNumber());
		store.setCancelDate(storeExit.getCancelDate());
		store.setShippingState(0);
		update(store);
		//查询客户关联的所有外部帐号禁用
		storeMemberBaseService.closeStoreByMember(store.getId());
		//定义关闭门店容器
		List<ShopInfo> shopInfoByCloseList = storeExitService.findAllCloseShopList(storeExit);
		//定义退出门店容器
		List<ShopInfo> shopInfoByExitList = storeExitService.findAllExitShopList(storeExit);
		if(shopInfoByExitList!=null){
			//调用门店停业整顿方法》》》结束
			shopInfoService.restructuring(shopInfoByExitList, "经销商退出，门店停业整顿",reorganizeDate,wf);
		}
		if(shopInfoByCloseList!=null){
			//调用门店批量减少方法》》》结束
			shopInfoService.decrease(shopInfoByCloseList, "经销商退出，门店终止",wf);
		}
	}
}
