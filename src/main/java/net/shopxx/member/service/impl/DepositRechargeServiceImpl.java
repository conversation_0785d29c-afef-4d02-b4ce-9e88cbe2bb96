package net.shopxx.member.service.impl;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.service.TotalDateService;
import net.shopxx.finance.entity.Payment;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.dao.DepositRechargeDao;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.CustomerRecharge;
import net.shopxx.member.entity.DepositAttach;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.DistributorRechargeItem;
import net.shopxx.member.entity.DepositRecharge.Status;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.CustomerRechargeService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfUserOpinionBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service("depositRechargeServiceImpl")
public class DepositRechargeServiceImpl extends ActWfBillServiceImpl<DepositRecharge>
        implements DepositRechargeService {

    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoService;
    @Resource(name = "paymentServiceImpl")
    private PaymentService paymentService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeService;
    @Resource(name = "depositRechargeDao")
    private DepositRechargeDao depositRechargeDao;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgService;
    @Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;
    @Resource(name = "orderFullLinkServiceImpl")
    private OrderFullLinkService orderFullLinkService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "intfOrderMessageToServiceImpl")
    private IntfOrderMessageToService intfOrderMessageToService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name = "sbuServiceImpl")
    private SbuService sbuService;
    @Resource(name = "wfUserOpinionBaseServiceImpl")
    private WfUserOpinionBaseService wfUserOpinionBaseService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardService;
    @Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
    @Resource(name = "totalDateServiceImpl")
	private TotalDateService totalDateService;
    @Resource(name = "customerRechargeServiceImpl")
    private CustomerRechargeService customerRechargeService;
    @Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;

    @Override
    @Transactional
    public void check(DepositRecharge depositRecharge, Integer flag, String note, BigDecimal actualAmount) {
    	
    	//总账日期校验
    	this.isCheckTotalDate(depositRecharge);
        depositRecharge.setNote(note);
        depositRecharge.setCheckDate(new Date());
        depositRecharge.setOperator(storeMemberService.getCurrent());
        //客户金额回填
      	this.isCheckCustomerRecharge(depositRecharge);
        if (flag == 2) {
        	// 审核未通过
            depositRecharge.setStatus(DepositRecharge.Status.failure); 
            // 客户余额充值
            if (depositRecharge.getType() == 1) {
            	// 单据状态 0.已保存(没有流程) 1.处理中(有流程)
                depositRecharge.setDocStatus(3); 
                // 2.已处理(流程走完) 3.关闭
            }
            update(depositRecharge);
        } else if (flag == 1) {
        	// 审核通过
            depositRecharge.setStatus(DepositRecharge.Status.success); 
            if (actualAmount == null) {
                actualAmount = depositRecharge.getAmount();
            }
            depositRecharge.setActualAmount(actualAmount);
            // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
            depositRecharge.setDocStatus(2); 
            // 2.已处理(流程走完) 3.关闭
            update(depositRecharge);
        	orderFullLinkService.addFullLink(5, null, depositRecharge.getSn(),
                    ConvertUtil.convertI18nMsg("18702", new Object[]{"客户余额充值"}), null);
            // 写接口表
            saveIntfAtCheck(depositRecharge);
        }
    }



    // 写接口表
    public void saveIntfAtCheck(DepositRecharge depositRecharge) {
        Long companyInfoId = depositRecharge.getCompanyInfoId();
        CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
        if ("TCLNT".equals(companyInfo.getCompany_code())) {
            // TCL暖通接口
            intfOrderMessageToService.saveDepositRechargeCustomIntf(depositRecharge);
        }
    }

//	@Override
//	@Transactional
//	public void checkStoreRechargeWf(DepositRecharge depositRecharge, String note, BigDecimal actualAmount,
//			Long objConfId) {
//		if (depositRecharge.getWfId() != null) {
//			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
//		}
//		depositRecharge.setNote(note);
//		if (actualAmount == null) {
//			actualAmount = depositRecharge.getAmount();
//		}
//		depositRecharge.setActualAmount(actualAmount);
//		// 创建流程实例
//		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
//		if (wfObjConfigLine == null) {
//			ExceptionUtil.throwServiceException("请选择审核流程");
//		}
//		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());
//		depositRecharge.setByObjConfig(wfObjConfigLine); // 设置流程配置
//		wfBaseService.createwf(storeMemberService.getCurrent(),
//				depositRecharge.getSaleOrg() == null ? null : depositRecharge.getSaleOrg().getId(),
//				depositRecharge.getSn(), depositRecharge.getWfTempId(), depositRecharge.getObjTypeId(),
//				depositRecharge.getId());
//
//		depositRecharge.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.已提交 2.已处理(流程走完)
//											// 3.关闭
//		// 大自然 防止组织丢失 暂时
//		if (depositRecharge.getOrganization() == null && WebUtils.getCurrentCompanyInfoId() == 9) {
//			ExceptionUtil.throwServiceException("经营组织为空");
//		}
//		update(depositRecharge);
//		orderFullLinkService.addFullLink(5, null, depositRecharge.getSn(),
//				ConvertUtil.convertI18nMsg("18701", new Object[] { wfTemp.getWfTempName() }), null);
//
//	}

    /**
     * 用户余额充值
     */
    public void storeMemberRecharge(Long companyInfoId, StoreMember storeMember, Payment payment,
                                    BigDecimal modifyBalance, DepositRecharge depositRecharge) {

        // StoreMember pStoreMember =
        // storeMemberService.findByMemberAndCompanyInfoId(member,
        // companyInfoId);
        // 生成一张付款单
        initPayment(companyInfoId, storeMember, payment, modifyBalance, depositRecharge);
        // 修改用户余额
        storeMember.setBalance(storeMember.getBalance().add(modifyBalance));
        storeMemberService.update(storeMember);
    }

    /**
     * 客户余额充值
     */
    public void storeRecharge(Long companyInfoId, StoreMember storeMember, Payment payment, BigDecimal modifyBalance,
                              DepositRecharge depositRecharge) {

        // 生成一张付款单
        initPayment(companyInfoId, storeMember, payment, modifyBalance, depositRecharge);
        // 修改客户余额
        Store store = depositRecharge.getStore();
        store.setBalance(store.getBalance().add(modifyBalance));
        storeService.update(store);

    }

    /**
     * 机构预算充值
     */
    public void saleOrgBudget(Long companyInfoId, StoreMember storeMember, Payment payment, BigDecimal modifyBalance,
                              DepositRecharge depositRecharge) {
        // 生成一张付款单
        initPayment(companyInfoId, storeMember, payment, modifyBalance, depositRecharge);
        SaleOrg saleOrg = depositRecharge.getSaleOrg();
        saleOrg.setBudget(modifyBalance);
        saleOrgService.update(saleOrg);
    }

    /**
     * 客户预算充值
     */
    public void storeBudget(Long companyInfoId, StoreMember storeMember, Payment payment, BigDecimal modifyBalance,
                            DepositRecharge depositRecharge) {

        // 生成一张付款单
        initPayment(companyInfoId, storeMember, payment, modifyBalance, depositRecharge);
        Store store = depositRecharge.getStore();
        store.setBudget(modifyBalance);
        storeService.update(store);

    }

    public void initPayment(Long companyInfoId, StoreMember storeMember, Payment payment, BigDecimal modifyBalance,
                            DepositRecharge depositRecharge) {

        CompanyInfo companyInfo = companyInfoService.find(companyInfoId);
        Integer paymentPrefix = companyInfo.getPaymentPrefix();
        String hexString = Integer.toHexString(paymentPrefix).toUpperCase();
        if (hexString.length() == 1) {
            hexString = "0" + hexString;
        }
        payment = new Payment();
        String sn = SnUtil.generateSn();
        payment.setSn(sn);
        payment.setUnifiedSn(sn);
        payment.setStatus(1);// 支付成功
        payment.setMethod(1);
        payment.setFee(new BigDecimal(0));
        payment.setAmount(modifyBalance);
        payment.setPaymentDate(new Date());
        payment.setPaymentPluginId(null);
        payment.setExpire(null);
        if (depositRecharge.getType() == 0) { // 用户余额充值
            payment.setType(1);
            payment.setPayType(0);
            payment.setStoreMember(storeMember);
        } else if (depositRecharge.getType() == 1) { // 客户余额充值
            payment.setType(3);
            payment.setPayType(1);
            payment.setStore(depositRecharge.getStore());
        } else if (depositRecharge.getType() == 2) { // 机构预算充值
            payment.setType(6);
            payment.setPayType(6);
            payment.setSaleOrg(depositRecharge.getSaleOrg());
        } else if (depositRecharge.getType() == 3) { // 客户预算充值
            payment.setType(7);
            payment.setPayType(7);
            payment.setStore(depositRecharge.getStore());
        }
        payment.setTermType(3);
        payment.setElseSn(depositRecharge.getSn());
        payment.setOperator(storeMemberService.getCurrent().getUsername());
        payment.setOperateStoreMember(storeMemberService.getCurrent());
        payment.setCompanyInfoId(companyInfoId);
        paymentService.save(payment);
    }

    /**
     * 用户、客户余额充值列表数据
     */
    @Override
    @Transactional(readOnly = true)
    public Page<Map<String, Object>> findPage(Integer[] type, String sn, Long storeMemberId, Long storeId,
                                              Long saleOrgId, Integer[] status, Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
                                              Long operatorId, BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable) {
        return depositRechargeDao.findPage(type, sn, storeMemberId, storeId, status, docstatus, rechargeTypeId,
                creatorId, operatorId, null, minPrice, maxPrice, pageable);
    }


    @Override
    @Transactional
    public void save(Store store, DepositRecharge depositRecharge, 
    		Integer fullLinkType, Long rechargeTypeId) {
    
    	//总账日期校验
    	this.isCheckTotalDate(depositRecharge);
    	
        depositRecharge.setType(1);
        depositRecharge.setRechargeType(systemDictService.find(rechargeTypeId));
        depositRecharge.setStatus(Status.wait);
        // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
        depositRecharge.setDocStatus(0); 
        // 2.已处理(流程走完) 3.关闭
        depositRecharge.setStore(store);
        depositRecharge.setCreator(storeMemberService.getCurrent());
        depositRecharge.setStoreMember(storeMemberService.getCurrent());
        depositRecharge.setActualAmount(depositRecharge.getAmount());
        depositRecharge.setSn(SnUtil.getDepositRechargeSn());
        if (depositRecharge.getBankCard() == null || depositRecharge.getBankCard().getId() == null) {
            depositRecharge.setBankCard(null);
        }
        // 附件
        List<DepositAttach> depositAttachs = depositRecharge.getDepositAttachs();
        for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext(); ) {
            DepositAttach depositAttach = iterator.next();
            if (depositAttach == null || depositAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (depositAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            depositAttach.setFileName(depositAttach.getName() + "." + depositAttach.getSuffix());
            depositAttach.setDepositRecharge(depositRecharge);
            depositAttach.setStoreMember(storeMemberService.getCurrent());
        }
        depositRecharge.setDepositAttachs(depositAttachs);

        // 大自然 防止组织丢失 暂时
        if (depositRecharge.getOrganization() == null && WebUtils.getCurrentCompanyInfoId() == 9) {
            ExceptionUtil.throwServiceException("经营组织为空");
        }
        save(depositRecharge);
        orderFullLinkService.addFullLink(fullLinkType, null, depositRecharge.getSn(),
                ConvertUtil.convertI18nMsg("18700", new Object[]{"客户余额充值"}), null);
    }

    @Override
    @Transactional
    public void update(Store store, DepositRecharge depositRecharge,
    		Integer fullLinkType, Integer isSubmit) {
    	
    	//总账日期校验
    	this.isCheckTotalDate(depositRecharge);
        DepositRecharge dr = find(depositRecharge.getId());
        //校验充值单状态
        this.checkDepositRechargeStatus(dr,0,"已保存","保存");
        //GL日期
        dr.setGlDate(depositRecharge.getGlDate());
        //经营组织
        dr.setOrganization(depositRecharge.getOrganization());
        //银行水单号
        dr.setBankSlip(depositRecharge.getBankSlip());
        //汇款人
        dr.setRemitter(depositRecharge.getRemitter());
        //汇款账号
        dr.setRemittanceAccount(depositRecharge.getRemittanceAccount());
        dr.setStore(store);
        dr.setSaleOrg(depositRecharge.getSaleOrg());
        dr.setImage(depositRecharge.getImage());
        dr.setImage2(depositRecharge.getImage2());
        dr.setImage3(depositRecharge.getImage3());
        dr.setAmount(depositRecharge.getAmount());
        // 实际充值金额
        dr.setActualAmount(depositRecharge.getAmount());
        dr.setMemo(depositRecharge.getMemo());
        dr.setApplyDate(depositRecharge.getApplyDate());
        if (depositRecharge.getBankCard() == null || depositRecharge.getBankCard().getId() == null) {
            dr.setBankCard(null);
        } else {
            dr.setBankCard(depositRecharge.getBankCard());
        }
        //sbu
        dr.setSbu(depositRecharge.getSbu());
        // 附件
        List<DepositAttach> depositAttachs = depositRecharge.getDepositAttachs();
        for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext(); ) {
            DepositAttach depositAttach = iterator.next();
            if (depositAttach == null || depositAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (depositAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            depositAttach.setFileName(depositAttach.getName() + "." + depositAttach.getSuffix());
            depositAttach.setDepositRecharge(dr);
            depositAttach.setStoreMember(storeMemberService.getCurrent());
        }
        dr.getDepositAttachs().clear();
        dr.getDepositAttachs().addAll(depositAttachs);

        // 大自然 防止组织丢失 暂时
        if (dr.getOrganization() == null && WebUtils.getCurrentCompanyInfoId() == 9) {
            ExceptionUtil.throwServiceException("经营组织为空");
        }

        update(dr);
    }

    @Override
    @Transactional
    public void cancel(DepositRecharge depositRecharge, Integer fullLinkType) {
    	//单据状态 0.已保存(没有流程) 1.处理中(有流程)
        depositRecharge.setDocStatus(3);
        //收款账号列表 
    	List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
        for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
        	DistributorRechargeItem distributorRechargeItem = iterator.next();
            if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
                iterator.remove();
                continue;
            }
            if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
            	DepositRecharge  pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
            	if(!ConvertUtil.isEmpty(pageDepositRecharge)){
            		if(!ConvertUtil.isEmpty(pageDepositRecharge.getDocStatus()) && pageDepositRecharge.getDocStatus() != 0){
            			ExceptionUtil.throwServiceException("单据明细编号为【"+pageDepositRecharge.getSn()+"】的状态不是已保存，禁止作废操作");
            		}
            		pageDepositRecharge.setDocStatus(3);
                	update(pageDepositRecharge);
            	}
            }
        }
        // 2.已处理(流程走完) 3.关闭
        update(depositRecharge);
        
        if(!distributorRechargeItems.isEmpty() &&  distributorRechargeItems.size()>0){
        	orderFullLinkService.addFullLink(fullLinkType, null, depositRecharge.getSn(),
                    ConvertUtil.convertI18nMsg("18703", new Object[]{"经销商充值", "作废本次充值申请"}), null);
        }else{
        	orderFullLinkService.addFullLink(fullLinkType, null, depositRecharge.getSn(),
                    ConvertUtil.convertI18nMsg("18703", new Object[]{"客户余额充值", "作废本次充值申请"}), null);
        }
    }

    @Override
    public Page<Map<String, Object>> newfindPage(Integer[] type, String sn, Long storeMemberId, Long storeId,
                 Long saleOrgId, Integer[] status, Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
                 Long operatorId, BigDecimal minPrice, BigDecimal maxPrice, String firstTime, String lastTime,
                 Long organizationId, Long sbuId,String sourceSn,String adjustmentSn,Long[] bankCardId,
                 Long[] bcOrganizationId,Long[] bcSbuId,Pageable pageable) {
                 
                 
                 
        return depositRechargeDao.newfindPage(type, sn, storeMemberId, storeId, saleOrgId, status, docstatus,
                rechargeTypeId, creatorId, operatorId, null, minPrice, maxPrice, firstTime, lastTime, organizationId,
                sbuId, sourceSn, adjustmentSn, bankCardId, bcOrganizationId, bcSbuId, pageable);

    }

    @Override
    public Integer countDepositRecharge(Integer[] type, String sn, Long storeMemberId, Long storeId, Long saleOrgId,
                        Integer[] status, Integer[] docstatus, Long[] rechargeTypeId, Long creatorId, Long operatorId,
                        BigDecimal minPrice, BigDecimal maxPrice, Long sbuId, String firstTime, String lastTime, Long[] ids,
                        Long organizationId, Pageable pageable, Integer page, Integer size,String sourceSn,String adjustmentSn,
                        Long[] bankCardId,Long[] bcOrganizationId,Long[] bcSbuId) {
    	
        return depositRechargeDao.countDepositRecharge(type, sn, storeMemberId, storeId, saleOrgId, status, docstatus,
                rechargeTypeId, creatorId, operatorId, null, minPrice, maxPrice, sbuId, firstTime, lastTime, ids, organizationId,
                pageable, page, size, sourceSn, adjustmentSn, bankCardId, bcOrganizationId, bcSbuId);
    }

    @Override
    public List<Map<String, Object>> findDepositRechargeList(Integer[] type, String sn, Long storeMemberId,
				     Long storeId, Long saleOrgId, Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
				     Long creatorId,Long operatorId, BigDecimal minPrice, BigDecimal maxPrice, Long sbuId, 
				     String firstTime, String lastTime, Long[] ids, Long organizationId, Pageable pageable,
				     Integer page, Integer size,String sourceSn,String adjustmentSn,Long[] bankCardId,
				     Long[] bcOrganizationId,Long[] bcSbuId) {
    	
        return depositRechargeDao.findDepositRechargeList(type, sn, storeMemberId, storeId, saleOrgId, status,
                docstatus, rechargeTypeId, creatorId, operatorId, null, minPrice, maxPrice, sbuId, firstTime, ids,
                organizationId,lastTime, pageable, page, size,sourceSn,adjustmentSn,bankCardId,bcOrganizationId,bcSbuId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Map<String, Object>> findShowPage(Long storeId, String sn, Pageable page) {
        return depositRechargeDao.findShowPage(storeId, sn, page);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> findList(Long storeId, String sn) {
        return depositRechargeDao.findList(storeId, sn);
    }

    /**
     * 创建流程--普通充值
     *
     * @param id
     * @param modelId
     * @param objTypeId
     */
    @Override
    @Transactional
    public void createWf(Long id, String modelId, Long objTypeId,
    		DepositRecharge depositRecharge, String note, 
    		BigDecimal actualAmount) {
    	
    	//总账日期校验
    	this.isCheckTotalDate(depositRecharge);
        depositRecharge.setNote(note);
        if (ConvertUtil.isEmpty(actualAmount)) {
            actualAmount = depositRecharge.getAmount();
        }
        depositRecharge.setActualAmount(actualAmount);
        StoreMember storeMember = storeMemberService.getCurrent();
        // 创建流程实例
        createWf(depositRecharge.getSn(),String.valueOf(storeMember.getId()),
                new Long[]{depositRecharge.getSaleOrg().getId()},
                depositRecharge.getStore().getId(),modelId,
                objTypeId,id,WebUtils.getCurrentCompanyInfoId(),true);
        
        // 单据状态 0.已保存(没有流程) 1.已提交 2.已处理(流程走完)
        depositRecharge.setDocStatus(1);
        // 大自然 防止组织丢失 暂时
        if (ConvertUtil.isEmpty(depositRecharge.getOrganization()) && WebUtils.getCurrentCompanyInfoId() == 9) {
            ExceptionUtil.throwServiceException("经营组织为空");
        }
        update(depositRecharge);
        orderFullLinkService.addFullLink(5, null, depositRecharge.getSn(),
                ConvertUtil.convertI18nMsg("18701", new Object[]{"充值单"}), null);

    }

    @Override
    @Transactional
    public void saveform(DepositRecharge dr, Integer Type) {

        DepositRecharge depositRecharge = find(dr.getId());

        //如果需要保存审核意见，再补充

    }

    
	/**
	 * 给流程分支节点赋值
	 */
	public Map<String, Object> setFormData(ActWf wf, String taskId) {
		Map<String,Object> dayMap=new HashMap<String, Object>();
		dayMap.put("organizationId", this.find(wf.getObjId()).getOrganization().getId());
		dayMap.put("saleOrgId", this.find(wf.getObjId()).getSaleOrg().getId());
		dayMap.put("organizationName", this.find(wf.getObjId()).getOrganization().getName());
        dayMap.put("sbuId", this.find(wf.getObjId()).getSbu().getId());
        dayMap.put("sbu", this.find(wf.getObjId()).getSbu().getName());
        dayMap.put("isInternalAccount", this.find(wf.getObjId()).getBankCard().getIsInternalAccount());
        dayMap.put("isTotalAccount", this.find(wf.getObjId()).getBankCard().getIsTotalAccount());
        dayMap.put("isNewBankCode", this.find(wf.getObjId()).getBankCard().getIsNewBankCode());
        dayMap.put("bankCode", this.find(wf.getObjId()).getBankCard().getBankCode());
		return dayMap;
	}

    // 保存放行人
    public void saveFxr(DepositRecharge depositRecharge) {
        if (depositRecharge.getDocStatus() == 2) {
        	StoreMember storeMember = storeMemberService.getCurrent();
            depositRecharge.setPermitThroughName(storeMember.getName());
        }
    }

    /**
     * 流程结束回调 原agreeBack()方法
     */
    @Override
    @Transactional
    public void endBack(ActWf wf) {
        super.endBack(wf);
        DepositRecharge depositRecharge = find(wf.getObjId());
        //校验充值单状态
        this.checkDepositRechargeStatus(depositRecharge,1,"已提交","通过");
        //已审核
        depositRecharge.setDocStatus(2); 
        depositRecharge.setCheckDate(new Date());
        //来源总账日期校验
        this.isCheckTotalDate(depositRecharge);
            if(depositRecharge.getRechargeTypes() == 1) {//1-经销商充值（有明细列表的）
                //收款账号列表金额
                BigDecimal totalAmount = BigDecimal.ZERO;
                //收款账号列表
                List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
                for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext(); ) {
                    DistributorRechargeItem distributorRechargeItem = iterator.next();
                    if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
                        iterator.remove();
                        continue;
                    }
                    if (!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())) {
                        DepositRecharge pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
                        if (!ConvertUtil.isEmpty(pageDepositRecharge)) {
                            if (!ConvertUtil.isEmpty(pageDepositRecharge.getDocStatus()) && pageDepositRecharge.getDocStatus() != 1) {
                                ExceptionUtil.throwServiceException("单据明细编号为【" + pageDepositRecharge.getSn() + "】的状态不是已提交，禁止通过操作");
                            }
                            //客户金额回填-经销商充值（有明细列表的）
                            this.isCheckCustomerRecharge(pageDepositRecharge);
                            pageDepositRecharge.setDocStatus(2);
                            pageDepositRecharge.setWfState(2);
                            totalAmount = totalAmount.add(pageDepositRecharge.getAmount());
                            update(pageDepositRecharge);
                        }
                    }
                }
                //判断收款账号列表金额不能大于充值金额
                if (!ConvertUtil.isEmpty(depositRecharge.getDistributorRechargeItems()) && depositRecharge.getDistributorRechargeItems().size() > 0) {
                    if (totalAmount.compareTo(depositRecharge.getAmount()) == -1 || totalAmount.compareTo(depositRecharge.getAmount()) == 1) {
                        throw new RuntimeException("收款账号列表金额必须等于充值金额");
                    }
                }
            }else {
                //客户金额回填---普通充值（没有明细列表的）
                this.isCheckCustomerRecharge(depositRecharge);
            }
        update(depositRecharge);
        Order order = depositRecharge.getOrder();
        List<String> orderSns = new ArrayList<String>();
        if (order != null) {
            orderSns.add(order.getSn());
        }
        orderFullLinkService.addFullLink(15,
                orderSns,
                depositRecharge.getSn(),
                ConvertUtil.convertI18nMsg("18702", new Object[]{"充值单"}),
                null);
        
        saveIntfAtCheck(depositRecharge);

        // 把财务审核节点人员保存(调用旧流程方法，可能会冲突)
        saveFxr(depositRecharge);

        update(depositRecharge);

        // 写接口表（eas收款单推送）
        saveIntfAtCheck(depositRecharge);
    }

    /**
     * 中断流程回调 原interruptBack()方法
     */
    @Override
    @Transactional
    public void interruptBack(ActWf wf) {
        DepositRecharge depositRecharge = find(wf.getObjId());
        //校验充值单状态
        this.checkDepositRechargeStatus(depositRecharge,1,"已提交","中断");
        depositRecharge.setDocStatus(0);
        //收款账号列表 
        List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
        for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
        	DistributorRechargeItem distributorRechargeItem = iterator.next();
        	if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
        		iterator.remove();
        		continue;
        	}
        	if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
        		DepositRecharge  pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
        		if(!ConvertUtil.isEmpty(pageDepositRecharge)){
        			if(!ConvertUtil.isEmpty(pageDepositRecharge.getDocStatus()) &&  pageDepositRecharge.getDocStatus() != 1){
        				ExceptionUtil.throwServiceException("单据明细编号为【"+pageDepositRecharge.getSn()+"】的状态不是已提交，禁止中断操作");
        			}
        			pageDepositRecharge.setDocStatus(0);
        			pageDepositRecharge.setWfState(0);
            		update(pageDepositRecharge);
        		}
        	}
        }
        
      
        update(depositRecharge);
        orderFullLinkService.addFullLink(15,
                null,
                depositRecharge.getSn(),
                ConvertUtil.convertI18nMsg("18704"),
                null);
        update(depositRecharge);
    }

    
    
    /**
	 * 驳回流程回调
	 */
	@Override
	@Transactional
	public void rejectBack(ActWf wf) {
        DepositRecharge depositRecharge = find(wf.getObjId());
        //校验充值单状态
        this.checkDepositRechargeStatus(depositRecharge,1,"已提交","驳回");
        depositRecharge.setWfState(3);
        //收款账号列表 
        List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
        for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
        	DistributorRechargeItem distributorRechargeItem = iterator.next();
        	if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
        		iterator.remove();
        		continue;
        	}
        	if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
        		DepositRecharge  pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
        		if(!ConvertUtil.isEmpty(pageDepositRecharge)){
        			if(!ConvertUtil.isEmpty(pageDepositRecharge.getDocStatus()) &&  pageDepositRecharge.getDocStatus() != 1){
        				ExceptionUtil.throwServiceException("单据明细编号为【"+pageDepositRecharge.getSn()+"】的状态不是已提交，禁止驳回操作");
        			}
        			pageDepositRecharge.setWfState(depositRecharge.getWfState());
            		update(pageDepositRecharge);
        		}
        	}
        }
        update(depositRecharge);
    }
	
	
	/**
	 * 通过流程节点回调
	 */
	@Override
	@Transactional
	public void agreeBack(ActWf wf) {
        DepositRecharge depositRecharge = find(wf.getObjId());
        if(depositRecharge.getWfState() == 3){
        	//校验充值单状态
            this.checkDepositRechargeStatus(depositRecharge,1,"已提交","通过");
            depositRecharge.setWfState(1);
            //收款账号列表 
            List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
            for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
            	DistributorRechargeItem distributorRechargeItem = iterator.next();
            	if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
            		iterator.remove();
            		continue;
            	}
            	if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
            		DepositRecharge  pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
            		if(!ConvertUtil.isEmpty(pageDepositRecharge)){
            			if(!ConvertUtil.isEmpty(pageDepositRecharge.getDocStatus()) &&  pageDepositRecharge.getDocStatus() != 1){
            				ExceptionUtil.throwServiceException("单据明细编号为【"+pageDepositRecharge.getSn()+"】的状态不是已提交，禁止通过操作");
            			}
            			pageDepositRecharge.setWfState(depositRecharge.getWfState());
                		update(pageDepositRecharge);
            		}
            	}
            }
            update(depositRecharge);
        }
    }

	@Override
	@Transactional
	public void saveDepositRecharge(DepositRecharge depositRecharge) {
		//总账日期校验
		this.isCheckTotalDate(depositRecharge);
        depositRecharge.setType(1);
        depositRecharge.setStatus(Status.wait);
        depositRecharge.setDocStatus(0); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.关闭
        depositRecharge.setCreator(storeMemberService.getCurrent());
        depositRecharge.setStoreMember(storeMemberService.getCurrent());
        depositRecharge.setActualAmount(depositRecharge.getAmount());
        depositRecharge.setSn(SnUtil.getDepositRechargeSn());
        
        //收款账号列表
        List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
        for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
        	DistributorRechargeItem distributorRechargeItem = iterator.next();
            if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getBankCard())) {
                iterator.remove();
                continue;
            }
            DepositRecharge dealerRechargeItem = new DepositRecharge();
            //单据编号
            dealerRechargeItem.setSn(SnUtil.getDepositRechargeSn());
            //类型
            dealerRechargeItem.setType(depositRecharge.getType());
            //充值状态
            dealerRechargeItem.setStatus(Status.wait);
            //充值客户
            dealerRechargeItem.setStore(depositRecharge.getStore());
            //充值金额
            dealerRechargeItem.setAmount(distributorRechargeItem.getAmountCollected());
            //实际充值金额
            dealerRechargeItem.setActualAmount(distributorRechargeItem.getAmountCollected());
            //机构
            dealerRechargeItem.setSaleOrg(depositRecharge.getSaleOrg());
            //充值类型
            dealerRechargeItem.setRechargeType(depositRecharge.getRechargeType());
            //对账月份
            dealerRechargeItem.setBalanceMonth(depositRecharge.getBalanceMonth());
            //收款账户
            if(!ConvertUtil.isEmpty(distributorRechargeItem.getBankCard().getId())){
            	BankCard bankCard = bankCardService.find(distributorRechargeItem.getBankCard().getId());
            	dealerRechargeItem.setBankCard(bankCard);
            	//sbu
        		if (!ConvertUtil.isEmpty(bankCard.getSbu())
        				&&!ConvertUtil.isEmpty(bankCard.getSbu().getId())) {
        			Sbu sbu = sbuService.find(bankCard.getSbu().getId());
        			dealerRechargeItem.setSbu(sbu);
        		 }
        		//经营组织
        		if (!ConvertUtil.isEmpty(bankCard.getOrganization())
        				&&!ConvertUtil.isEmpty(bankCard.getOrganization().getId())) {
        			 Organization organization = organizationService.find(bankCard.getOrganization().getId());
        			 dealerRechargeItem.setOrganization(organization);
        		 }
            }
            //申请日期
            dealerRechargeItem.setApplyDate(depositRecharge.getApplyDate());
            //创建人
            dealerRechargeItem.setCreator(depositRecharge.getCreator());
            //银行水单号
            dealerRechargeItem.setBankSlip(depositRecharge.getBankSlip());
            //汇款人
            dealerRechargeItem.setRemitter(depositRecharge.getRemitter());
            //汇款帐号
            dealerRechargeItem.setRemittanceAccount(depositRecharge.getRemittanceAccount());
            //到款状态
            dealerRechargeItem.setErpStatus(depositRecharge.getErpStatus());
            //单据状态
            dealerRechargeItem.setDocStatus(depositRecharge.getDocStatus());
            //GL日期
            dealerRechargeItem.setGlDate(depositRecharge.getGlDate());
            //区域经理
            dealerRechargeItem.setRegionalManager(depositRecharge.getRegionalManager());
            //来源单号
            dealerRechargeItem.setSourceDepositRecharge(depositRecharge);
            //申请人
            dealerRechargeItem.setStoreMember(depositRecharge.getStoreMember());
            //申请备注
            dealerRechargeItem.setMemo(distributorRechargeItem.getMemo());
            
            save(dealerRechargeItem);
            distributorRechargeItem.setDepositRecharge(depositRecharge);
            distributorRechargeItem.setDealerRechargeItem(dealerRechargeItem);
        }
        
        depositRecharge.setDistributorRechargeItems(distributorRechargeItems);
        
        // 附件
        List<DepositAttach> depositAttachs = depositRecharge.getDepositAttachs();
        for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext();) {
            DepositAttach depositAttach = iterator.next();
            if (depositAttach == null || depositAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (depositAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            depositAttach.setFileName(depositAttach.getName() + "." + depositAttach.getSuffix());
            depositAttach.setDepositRecharge(depositRecharge);
            depositAttach.setStoreMember(storeMemberService.getCurrent());
        }
        depositRecharge.setDepositAttachs(depositAttachs);
        
        save(depositRecharge);
        orderFullLinkService.addFullLink(5, null, depositRecharge.getSn(),
                ConvertUtil.convertI18nMsg("18700", new Object[]{"经销商充值"}), null);
	}



	@Override
	public List<Map<String, Object>> findDistributorRechargeItemList(String ids) {
		
		return depositRechargeDao.findDistributorRechargeItemList(ids);
	}



	@Override
	@Transactional
	public void updateDepositRecharge(DepositRecharge depositRecharge) {
		
		//总账日期校验
		this.isCheckTotalDate(depositRecharge);
		DepositRecharge dr = find(depositRecharge.getId());
		//校验充值单状态
		this.checkDepositRechargeStatus(dr,0,"已保存","保存");
		//充值客户
		 dr.setStore(depositRecharge.getStore());
		//充值金额
		 dr.setAmount(depositRecharge.getAmount());
		//实际充值金额
		 dr.setActualAmount(depositRecharge.getAmount());
		//机构
		 dr.setSaleOrg(depositRecharge.getSaleOrg());
		//收款账户
		 dr.setBankCard(depositRecharge.getBankCard());
		//Sbu
		 dr.setSbu(depositRecharge.getSbu());
		//银行水单号
		 dr.setBankSlip(depositRecharge.getBankSlip());
		//汇款人
		 dr.setRemitter(depositRecharge.getRemitter());
		//汇款账号
		 dr.setRemittanceAccount(depositRecharge.getRemittanceAccount());
		//经营组织
		 dr.setOrganization(depositRecharge.getOrganization());
		//GL日期
		 dr.setGlDate(depositRecharge.getGlDate());
		//区域经理
		 dr.setRegionalManager(depositRecharge.getRegionalManager());
		 //申请备注
	     dr.setMemo(depositRecharge.getMemo());
	     // 附件
         List<DepositAttach> depositAttachs = depositRecharge.getDepositAttachs();
         for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext(); ) {
            DepositAttach depositAttach = iterator.next();
            if (depositAttach == null || depositAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (depositAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            depositAttach.setFileName(depositAttach.getName() + "." + depositAttach.getSuffix());
            depositAttach.setDepositRecharge(dr);
            depositAttach.setStoreMember(storeMemberService.getCurrent());
        }
        dr.getDepositAttachs().clear();
        dr.getDepositAttachs().addAll(depositAttachs);
        
        //数据库经销商充值明细列表
        List<DepositRecharge> dataBasedDepositRechargeList = new  ArrayList<DepositRecharge>();
        List<DistributorRechargeItem> dataBasedLstributorRechargeItems = dr.getDistributorRechargeItems();
	    for (DistributorRechargeItem distributorRechargeItem : dataBasedLstributorRechargeItems) {
	    	 if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
	                continue;
	         }
	    	 if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
	    		 DepositRecharge dataBasedDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
	    		 if(!ConvertUtil.isEmpty(dataBasedDepositRecharge)){
	    			 if(dataBasedDepositRecharge.getDocStatus() != 0){
	    				 ExceptionUtil.throwServiceException("单据明细编号为【"+dataBasedDepositRecharge.getSn()+"】的状态不是已保存，禁止保存操作");
	    			 }
	    			 dataBasedDepositRechargeList.add(dataBasedDepositRecharge);
	    		 }
	    	 }
		 }
	    
	    
	    //收款账号列表金额
	    BigDecimal totalAmount =  BigDecimal.ZERO;
	    
	    //页面经销商充值明细列表
	    List<DepositRecharge> pageDepositRechargeList = new  ArrayList<DepositRecharge>();
        List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
        for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
        	DistributorRechargeItem distributorRechargeItem = iterator.next();
            if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getBankCard())) {
                iterator.remove();
                continue;
            }
            DepositRecharge pageDepositRecharge = null;
            if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())&&!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
            	pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
            }else{
            	pageDepositRecharge = new DepositRecharge();
            }
            //收款金额
            if(ConvertUtil.isEmpty(distributorRechargeItem.getAmountCollected())){
            	distributorRechargeItem.setAmountCollected(new BigDecimal("0"));
            }
            //类型
            pageDepositRecharge.setType(dr.getType());
            //充值状态
            pageDepositRecharge.setStatus(Status.wait);
            //充值客户
            pageDepositRecharge.setStore(depositRecharge.getStore());
            //充值金额
            pageDepositRecharge.setAmount(distributorRechargeItem.getAmountCollected());
            //实际充值金额
            pageDepositRecharge.setActualAmount(distributorRechargeItem.getAmountCollected());
            //机构
            pageDepositRecharge.setSaleOrg(depositRecharge.getSaleOrg());
            //充值类型
            pageDepositRecharge.setRechargeType(depositRecharge.getRechargeType());
            //对账月份
            pageDepositRecharge.setBalanceMonth(depositRecharge.getBalanceMonth());
            //收款账户
            if(!ConvertUtil.isEmpty(distributorRechargeItem.getBankCard().getId())){
            	BankCard bankCard = bankCardService.find(distributorRechargeItem.getBankCard().getId());
            	pageDepositRecharge.setBankCard(bankCard);
            	//sbu
        		if (!ConvertUtil.isEmpty(bankCard.getSbu())
        				&&!ConvertUtil.isEmpty(bankCard.getSbu().getId())) {
        			Sbu sbu = sbuService.find(bankCard.getSbu().getId());
        			pageDepositRecharge.setSbu(sbu);
        		 }
        		//经营组织
        		if (!ConvertUtil.isEmpty(bankCard.getOrganization())
        				&&!ConvertUtil.isEmpty(bankCard.getOrganization().getId())) {
        			 Organization organization = organizationService.find(bankCard.getOrganization().getId());
        			 pageDepositRecharge.setOrganization(organization);
        		 }
            }
            //申请日期
            pageDepositRecharge.setApplyDate(depositRecharge.getApplyDate());
            //银行水单号
            pageDepositRecharge.setBankSlip(depositRecharge.getBankSlip());
            //汇款人
            pageDepositRecharge.setRemitter(depositRecharge.getRemitter());
            //汇款帐号
            pageDepositRecharge.setRemittanceAccount(depositRecharge.getRemittanceAccount());
            //到款状态
            pageDepositRecharge.setErpStatus(dr.getErpStatus());
            //单据状态
            pageDepositRecharge.setDocStatus(dr.getDocStatus());
            //GL日期
            pageDepositRecharge.setGlDate(depositRecharge.getGlDate());
            //区域经理
            pageDepositRecharge.setRegionalManager(depositRecharge.getRegionalManager());
            //来源单号
            pageDepositRecharge.setSourceDepositRecharge(dr);
            //申请备注
            pageDepositRecharge.setMemo(distributorRechargeItem.getMemo());
            if(!ConvertUtil.isEmpty(pageDepositRecharge)&&!ConvertUtil.isEmpty(pageDepositRecharge.getId())){
            	 update(pageDepositRecharge);
            }else{
            	//单据编号
                pageDepositRecharge.setSn(SnUtil.getDepositRechargeSn());
                //创建人
                pageDepositRecharge.setCreator(storeMemberService.getCurrent());
                //申请人
                pageDepositRecharge.setStoreMember(storeMemberService.getCurrent());
                save(pageDepositRecharge);
            }
            totalAmount = totalAmount.add(pageDepositRecharge.getAmount());
            distributorRechargeItem.setDepositRecharge(depositRecharge);
            distributorRechargeItem.setDealerRechargeItem(pageDepositRecharge);
            pageDepositRechargeList.add(pageDepositRecharge);
        }
        
        //判断收款账号列表金额不能大于充值金额
        if(totalAmount.compareTo(depositRecharge.getAmount())==-1||totalAmount.compareTo(depositRecharge.getAmount())==1){
        	throw new RuntimeException("收款账号列表金额必须等于充值金额");
        }
        
        for (DepositRecharge dataBasedDepositRecharge : dataBasedDepositRechargeList) {
			if (!pageDepositRechargeList.contains(dataBasedDepositRecharge)) {
				dataBasedDepositRecharge.setDocStatus(3);
				 update(dataBasedDepositRecharge);
			}
		}
        
        dr.getDistributorRechargeItems().clear();
        dr.getDistributorRechargeItems().addAll(distributorRechargeItems);
	   
        update(dr);
	}



	@Override
	public Page<Map<String, Object>> newfindDepositRechargePage(Integer[] type,
			 Long[] rechargeTypeId,Long[] sbuId,String sn,Integer[] docstatus,
			 Long[] wfStates,Long[] storeId,Long[]  saleOrgId,Long[] bankCardId,
			 Long[] organizationId,String glDateStartTime,String glDateEndTime,
			 Long[] creatorId,String sourceSn,Integer page,
			 Integer size,Long[] ids,Pageable pageable) {
		
		return depositRechargeDao.newfindDepositRechargePage(type, rechargeTypeId, sbuId, sn, docstatus, 
				wfStates, storeId, saleOrgId, bankCardId, organizationId, glDateStartTime, 
				glDateEndTime, creatorId, sourceSn, page, size, ids, pageable);
	}



	@Override
	@Transactional
	public void checkWfUpdateDepositRecharge(DepositRecharge depositRecharge) {

		DepositRecharge dr = find(depositRecharge.getId());
		//校验充值单状态
		this.checkDepositRechargeStatus(dr,1,"已提交","保存");
        //数据库经销商充值明细列表
        List<DepositRecharge> dataBasedDepositRechargeList = new  ArrayList<DepositRecharge>();
        List<DistributorRechargeItem> dataBasedLstributorRechargeItems = dr.getDistributorRechargeItems();
	    for (DistributorRechargeItem distributorRechargeItem : dataBasedLstributorRechargeItems) {
	    	 if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
	                continue;
	         }
	    	 if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
	    		 DepositRecharge dataBasedDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
	    		 if(!ConvertUtil.isEmpty(dataBasedDepositRecharge)){
	    			 if(dataBasedDepositRecharge.getDocStatus() != 1){
	    				 ExceptionUtil.throwServiceException("单据明细编号为【"+dataBasedDepositRecharge.getSn()+"】的状态不是已提交，禁止保存操作");
	    			 }
		    		 dataBasedDepositRechargeList.add(dataBasedDepositRecharge);
	    		 }
	    	 }
		}
	    
	    //收款账号列表金额
	    BigDecimal totalAmount = new BigDecimal("0");
	    
	    //页面经销商充值明细列表
	    List<DepositRecharge> pageDepositRechargeList = new  ArrayList<DepositRecharge>();
        List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
        for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
        	DistributorRechargeItem distributorRechargeItem = iterator.next();
            if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getBankCard())) {
                iterator.remove();
                continue;
            }
            DepositRecharge pageDepositRecharge = null;
            if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())&&!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
            	pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
            }else{
            	pageDepositRecharge = new DepositRecharge();
            }
            //收款金额
            if(ConvertUtil.isEmpty(distributorRechargeItem.getAmountCollected())){
            	distributorRechargeItem.setAmountCollected(new BigDecimal("0"));
            }
            //类型
            pageDepositRecharge.setType(dr.getType());
            //充值状态
            pageDepositRecharge.setStatus(dr.getStatus());
            //充值客户
            pageDepositRecharge.setStore(dr.getStore());
            //充值金额
            pageDepositRecharge.setAmount(distributorRechargeItem.getAmountCollected());
            //实际充值金额
            pageDepositRecharge.setActualAmount(distributorRechargeItem.getAmountCollected());
            //机构
            pageDepositRecharge.setSaleOrg(dr.getSaleOrg());
            //充值类型
            pageDepositRecharge.setRechargeType(dr.getRechargeType());
            //对账月份
            pageDepositRecharge.setBalanceMonth(dr.getBalanceMonth());
            //收款账户
            if(!ConvertUtil.isEmpty(distributorRechargeItem.getBankCard().getId())){
            	BankCard bankCard = bankCardService.find(distributorRechargeItem.getBankCard().getId());
            	pageDepositRecharge.setBankCard(bankCard);
            	//sbu
        		if (!ConvertUtil.isEmpty(bankCard.getSbu())
        				&&!ConvertUtil.isEmpty(bankCard.getSbu().getId())) {
        			Sbu sbu = sbuService.find(bankCard.getSbu().getId());
        			pageDepositRecharge.setSbu(sbu);
        		 }
        		//经营组织
        		if (!ConvertUtil.isEmpty(bankCard.getOrganization())
        				&&!ConvertUtil.isEmpty(bankCard.getOrganization().getId())) {
        			 Organization organization = organizationService.find(bankCard.getOrganization().getId());
        			 pageDepositRecharge.setOrganization(organization);
        		 }
            }
            //申请日期
            pageDepositRecharge.setApplyDate(dr.getApplyDate());
            //银行水单号
            pageDepositRecharge.setBankSlip(dr.getBankSlip());
            //汇款人
            pageDepositRecharge.setRemitter(dr.getRemitter());
            //汇款帐号
            pageDepositRecharge.setRemittanceAccount(dr.getRemittanceAccount());
            //到款状态
            pageDepositRecharge.setErpStatus(dr.getErpStatus());
            //单据状态
            pageDepositRecharge.setDocStatus(dr.getDocStatus());
            //GL日期
            pageDepositRecharge.setGlDate(dr.getGlDate());
            //区域经理
            pageDepositRecharge.setRegionalManager(dr.getRegionalManager());
            //来源单号
            pageDepositRecharge.setSourceDepositRecharge(dr);
            //申请备注
            pageDepositRecharge.setMemo(distributorRechargeItem.getMemo());
            if(!ConvertUtil.isEmpty(pageDepositRecharge)&&!ConvertUtil.isEmpty(pageDepositRecharge.getId())){
            	 update(pageDepositRecharge);
            }else{
            	//单据编号
                pageDepositRecharge.setSn(SnUtil.getDepositRechargeSn());
                //创建人
                pageDepositRecharge.setCreator(storeMemberService.getCurrent());
                //申请人
                pageDepositRecharge.setStoreMember(storeMemberService.getCurrent());
                save(pageDepositRecharge);
            }
            totalAmount = totalAmount.add(pageDepositRecharge.getAmount());
            distributorRechargeItem.setDepositRecharge(depositRecharge);
            distributorRechargeItem.setDealerRechargeItem(pageDepositRecharge);
            pageDepositRechargeList.add(pageDepositRecharge);
        }
        
        //判断收款账号列表金额不能大于充值金额
        if(totalAmount.compareTo(depositRecharge.getAmount())==-1||totalAmount.compareTo(depositRecharge.getAmount())==1){
        	throw new RuntimeException("收款账号列表金额必须等于充值金额");
        }
        
        for (DepositRecharge dataBasedDepositRecharge : dataBasedDepositRechargeList) {
			if (!pageDepositRechargeList.contains(dataBasedDepositRecharge)) {
				dataBasedDepositRecharge.setDocStatus(3);
				 update(dataBasedDepositRecharge);
			}
		}
        
        dr.getDistributorRechargeItems().clear();
        dr.getDistributorRechargeItems().addAll(distributorRechargeItems);
	   
        update(dr);
		
	}

	/*审核*/
	@Override
	@Transactional
	public void checkDepositRecharge(DepositRecharge depositRecharge, Integer flag, String note,
			BigDecimal actualAmount) {
		
		//总账日期校验
		this.isCheckTotalDate(depositRecharge);
		depositRecharge.setNote(note);
        depositRecharge.setCheckDate(new Date());
        depositRecharge.setOperator(storeMemberService.getCurrent());
        depositRecharge.setStatus(DepositRecharge.Status.success); // 审核通过
        if (actualAmount == null) {
            actualAmount = depositRecharge.getAmount();
        }
        depositRecharge.setActualAmount(actualAmount);
        // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
        depositRecharge.setDocStatus(2); 
        //收款账号列表金额
        BigDecimal totalAmount = new BigDecimal("0");
        //收款账号列表 
        List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
        for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
        	DistributorRechargeItem distributorRechargeItem = iterator.next();
        	if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
        		iterator.remove();
        		continue;
        	}
        	if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
        		DepositRecharge  pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
        		//客户金额回填
        		this.isCheckCustomerRecharge(pageDepositRecharge);
        		//已审核
        		pageDepositRecharge.setDocStatus(2);
        		// 审核通过
        		pageDepositRecharge.setStatus(DepositRecharge.Status.success); 
        		totalAmount = totalAmount.add(pageDepositRecharge.getAmount());
        		update(pageDepositRecharge);
        	}
        }
        //判断收款账号列表金额不能大于充值金额
        if(!ConvertUtil.isEmpty(depositRecharge.getDistributorRechargeItems())&&depositRecharge.getDistributorRechargeItems().size()>0){
        	if(totalAmount.compareTo(depositRecharge.getAmount())==-1||totalAmount.compareTo(depositRecharge.getAmount())==1){
        		throw new RuntimeException("收款账号列表金额必须等于充值金额");
        	}
        }
        //客户金额回填
    	this.isCheckCustomerRecharge(depositRecharge);
        // 2.已处理(流程走完) 3.关闭
        update(depositRecharge);
    	orderFullLinkService.addFullLink(5, null, depositRecharge.getSn(),
                ConvertUtil.convertI18nMsg("18702", new Object[]{"经销商充值"}), null);
        // 写接口表
        saveIntfAtCheck(depositRecharge);
	}
	
	
	/*经销商充值-流程审批*/
	@Override
	@Transactional
	public void createWfDepositRecharge(Long id, String modelId, Long objTypeId,
			DepositRecharge depositRecharge, String note, BigDecimal actualAmount) {
		
		 	//总账日期校验
		 	this.isCheckTotalDate(depositRecharge);
	        depositRecharge.setNote(note);
	        if (ConvertUtil.isEmpty(actualAmount)) {
	            actualAmount = depositRecharge.getAmount();
	        }
	        depositRecharge.setActualAmount(actualAmount);
	        
	        StoreMember storeMember = storeMemberService.getCurrent();
	        // 创建流程实例
	        createWf(depositRecharge.getSn(),
	                String.valueOf(storeMember.getId()),
	                new Long[]{depositRecharge.getSaleOrg().getId()},
	                depositRecharge.getStore().getId(),
	                modelId,
	                objTypeId,
	                id,
	                WebUtils.getCurrentCompanyInfoId(),
	                true);
	        
	        // 单据状态 0.已保存(没有流程) 1.已提交 2.已处理(流程走完)
	        depositRecharge.setDocStatus(1); 
	        
	        //收款账号列表金额
	        BigDecimal totalAmount = new BigDecimal("0");
	        //收款账号列表 
	        List<DistributorRechargeItem> distributorRechargeItems = depositRecharge.getDistributorRechargeItems();
	        for (Iterator<DistributorRechargeItem> iterator = distributorRechargeItems.iterator(); iterator.hasNext();) {
	        	DistributorRechargeItem distributorRechargeItem = iterator.next();
	        	if (ConvertUtil.isEmpty(distributorRechargeItem) || ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem())) {
	        		iterator.remove();
	        		continue;
	        	}
	        	if(!ConvertUtil.isEmpty(distributorRechargeItem.getDealerRechargeItem().getId())){
	        		DepositRecharge  pageDepositRecharge = find(distributorRechargeItem.getDealerRechargeItem().getId());
	        		if(!ConvertUtil.isEmpty(pageDepositRecharge)){
	        			if(pageDepositRecharge.getDocStatus() != 0){
	        				ExceptionUtil.throwServiceException("单据明细编号为【"+pageDepositRecharge.getSn()+"】的状态不是已保存，禁止流程审批操作");
	        			}
	        			//已提交
		        		pageDepositRecharge.setDocStatus(1);
		        		pageDepositRecharge.setWfState(1);
		        		totalAmount = totalAmount.add(pageDepositRecharge.getAmount());
		        		update(pageDepositRecharge);
	        		}
	        	}
	        }
	        
	        //判断收款账号列表金额不能大于充值金额
	        if(!ConvertUtil.isEmpty(depositRecharge.getDistributorRechargeItems())&&depositRecharge.getDistributorRechargeItems().size()>0){
	        	if(totalAmount.compareTo(depositRecharge.getAmount())==-1||totalAmount.compareTo(depositRecharge.getAmount())==1){
	        		throw new RuntimeException("收款账号列表金额必须等于充值金额");
	        	}
	        }
	        
	        // 大自然 防止组织丢失 暂时
	        if (depositRecharge.getOrganization() == null && WebUtils.getCurrentCompanyInfoId() == 9) {
	            ExceptionUtil.throwServiceException("经营组织为空");
	        }
	        
	        update(depositRecharge);
	        
	        orderFullLinkService.addFullLink(5, null, depositRecharge.getSn(),
	                ConvertUtil.convertI18nMsg("18701", new Object[]{"充值单"}), null);
		
	}
	
	private  void isCheckTotalDate(DepositRecharge depositRecharge){
		//机构
		if(ConvertUtil.isEmpty(depositRecharge.getSaleOrg())){
			throw new RuntimeException("机构不能为空");
		}
		//sbu
		if(ConvertUtil.isEmpty(depositRecharge.getSbu())){
			throw new RuntimeException("sbu不能为空");
		}
		//经营组织
		if(ConvertUtil.isEmpty(depositRecharge.getOrganization())){
			throw new RuntimeException("经营组织不能为空");
		}
		//Gl日期
		if(ConvertUtil.isEmpty(depositRecharge.getGlDate())){
			throw new RuntimeException("Gl日期不能为空");
		}

        SystemDict totalDateType =systemDictService.findSystemDictList("totalDateType", "应收账期").get(0);
		List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, depositRecharge.getSaleOrg().getId(),
				depositRecharge.getSbu().getId(), new Long[]{depositRecharge.getOrganization().getId()}, depositRecharge.getGlDate(),totalDateType);
		if(mapList.isEmpty()||mapList.size() ==0){
			throw new RuntimeException("GL日期不包含在总账日期内，请填写合适的单据日期");
		}
	}
	
	private void isCheckCustomerRecharge(DepositRecharge depositRecharge){
		//客户充值
		CustomerRecharge customerRecharge = new CustomerRecharge();
		//客户
		customerRecharge.setStore(depositRecharge.getStore());
		//经营组织
		customerRecharge.setOrganization(depositRecharge.getOrganization());
		//sbu
		customerRecharge.setSbu(depositRecharge.getSbu());
		customerRecharge = customerRechargeService.saveOrUpdate(customerRecharge);
		if(!ConvertUtil.isEmpty(customerRecharge) && !ConvertUtil.isEmpty(customerRecharge.getId())){
            LogUtils.debug("========监控充值单=======");
		    //充值金额
            LogUtils.debug("原有的："+customerRecharge.getBalance());
			customerRecharge.setBalance(
					customerRecharge.getBalance().add(
							depositRecharge.getActualAmount()));
            LogUtils.debug("充值单的："+depositRecharge.getActualAmount());
            LogUtils.debug("加完以后的："+customerRecharge.getBalance());

            customerRechargeService.update(customerRecharge);
            LogUtils.debug("加完以后的："+customerRechargeService.find(customerRecharge.getId()).getBalance());
            LogUtils.debug("========监控充值单=======");
		}else{
			throw new RuntimeException("流程审批或者审核异常，请管理员进行维护");
		}
	}



	@Override
	public String getRechargeTypeIds(Integer flag) {
		//充值类型
		String rechargeTypeIds = "";
		try {
			List<SystemDict> depositRechargeType = null;
			if (!ConvertUtil.isEmpty(flag) && flag == 1) {
				depositRechargeType = roleJurisdictionUtil.getSystemDictList("DepositRechargeType", null);
			}else{
				depositRechargeType = roleJurisdictionUtil.getSystemDictList("DepositRechargeType", "客户充值");
			}
			if(!depositRechargeType.isEmpty() && depositRechargeType.size()>0){
				for (SystemDict rechargeType : depositRechargeType) {
					if(!ConvertUtil.isEmpty(rechargeType.getValue()) && !rechargeType.getValue().equals("政策录入")){
						if(ConvertUtil.isEmpty(rechargeTypeIds)){
							rechargeTypeIds = rechargeType.getId().toString();
						}else{
							rechargeTypeIds = rechargeTypeIds+","+rechargeType.getId().toString();
						}
					}
				}
			}
			if(ConvertUtil.isEmpty(rechargeTypeIds)){
				ExceptionUtil.throwServiceException("充值类型不能为空");
			}
		} catch (Exception e) {
			ExceptionUtil.throwServiceException(e.getMessage());
		}
		return rechargeTypeIds;
	}


	/**
	 * 校验充值单状态
	 */
	@Override
	public void checkDepositRechargeStatus(DepositRecharge depositRecharge, Integer status,
			String statusName, String operationName) {
		if(ConvertUtil.isEmpty(depositRecharge)){
        	ExceptionUtil.throwServiceException("该充值单不存在");
        }
        if(!ConvertUtil.isEmpty(depositRecharge.getDocStatus()) && depositRecharge.getDocStatus() != status){
        	ExceptionUtil.throwServiceException("只有单据状态为"+statusName+"的充值单才能"+operationName);
        }
		
	}
}
