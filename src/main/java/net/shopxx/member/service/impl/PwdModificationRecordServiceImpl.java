package net.shopxx.member.service.impl;

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.member.dao.PwdModificationRecordDao;
import net.shopxx.member.entity.PwdModificationRecord;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PwdModificationRecordService;
import net.shopxx.shop.service.StoreMemberService;
import net.shopxx.util.CommonUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("pwdModificationRecordServiceImpl")
public class PwdModificationRecordServiceImpl extends BaseServiceImpl<PwdModificationRecord> implements PwdModificationRecordService {

    @Resource(name = "pwdModificationRecordDao")
    private PwdModificationRecordDao pwdModificationRecordDao;

    @Resource(name = "storeMemberServiceImpl")
    private StoreMemberService storeMemberService;

    @Override
    public int getPwdModificationCount(Long storeMemberId) {
        return pwdModificationRecordDao.getPwdModificationCount(storeMemberId);
    }

    @Override
    public String saveUserPasswordRecord(Long storeMemberId, String oldpwd, String newPwd, String confirmNewPwd,String language) {
       /**校验参数**/
        if (StringUtils.isEmpty(oldpwd)) {
            //请输入原密码
            ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("请输入原密码!",
                    language));
        }
        if (StringUtils.isEmpty(newPwd)) {
            //请输入新密码
            ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("请输入新密码!",
                    language));
        }
        if (StringUtils.isEmpty(confirmNewPwd)) {
            //请确认新密码
            ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("请确认新密码!",
                    language));
        }
        if (!confirmNewPwd.equals(newPwd)){
            ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("新密码和确认密码不一致,请重新输入!",
                    language));
        }
        if (ConvertUtil.isEmpty(storeMemberId)){
            ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("企业用户ID不能为空!",
                    language));
        }
        /**判断新密码和原密码不能相同**/
        if (DigestUtils.md5Hex(oldpwd).equals(DigestUtils.md5Hex(newPwd))){
            ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("新密码和原密码不能相同,请重新输入!",
                    language));
        }
        /**正则判断密码是否为数字或字母组成的6-16位数**/
        // 字符串是否与正则表达式相匹配
        //String passRegex = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$";   //数字字母组成的6-16位数密码
        String passRegex = "^(?![0-9]+$)(?![a-zA-Z]+$)(?![0-9a-zA-Z]+$)(?![0-9\\W]+$)(?![a-zA-Z\\W]+$)[0-9A-Za-z\\W]{6,16}$";
        Boolean checkReult = CommonUtil.regularMatching(passRegex,newPwd);
        if(newPwd.length()<6 || !checkReult){
            ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("密码必须由数字、字母和特殊符号组成的6-16位!",
                    language));
        }
        StoreMember storeMember = storeMemberService.find(storeMemberId);
        /**判断原密码是否正确**/
        if (!DigestUtils.md5Hex(oldpwd).equals(storeMember.getPassword())) {
            //密码错误
            ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("原密码输入错误！",
                    language));
        }

        /**修改是否需要修改密码为否**/
        storeMember.setPassword(DigestUtils.md5Hex(newPwd));
        storeMember.setIsNeedToChangePwd(false);
        /**保存修改记录**/
        PwdModificationRecord pwdRecord = new PwdModificationRecord();
        pwdRecord.setStoreMember(storeMember);
        pwdRecord.setUsername(storeMember.getUsername());
        pwdRecord.setPwd(DigestUtils.md5Hex(newPwd));
        pwdRecord.setRevisionSource(1);
        save(pwdRecord);
        return pwdRecord.getUsername();
    }
}
