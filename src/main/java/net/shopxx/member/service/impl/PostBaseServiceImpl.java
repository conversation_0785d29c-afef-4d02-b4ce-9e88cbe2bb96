package net.shopxx.member.service.impl;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.member.dao.PostBaseDao;
import net.shopxx.member.entity.Post;
import net.shopxx.member.service.PostBaseService;

/**
 * Service - 岗位
 *
 */
@Service("postBaseServiceImpl")
public class PostBaseServiceImpl extends BaseServiceImpl<Post> implements PostBaseService {

	@Resource(name = "postBaseDao")
	private PostBaseDao postBaseDao;

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String startTime, String endTime, Pageable pageable, String name,
			String sn, Long saleOrgId,Long isEnabled) {
		return postBaseDao.findPage(startTime, endTime, pageable, name, sn, saleOrgId,isEnabled);
	}
}
