package net.shopxx.member.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.aftersales.entity.CreditRechargeVo;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.service.SystemParameterBaseService;
import net.shopxx.intf.service.OaToWfService;
import net.shopxx.order.entity.PriceApply;
import net.shopxx.order.entity.PriceApplyItem;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.finance.entity.Payment;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.member.dao.CreditRechargeDao;
import net.shopxx.member.entity.Adjustment;
import net.shopxx.member.entity.CreditRecharge;
import net.shopxx.member.entity.CustomerRecharge;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.SaleOrgCredit;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.CreditRechargeService;
import net.shopxx.member.service.CustomerRechargeService;
import net.shopxx.member.service.SaleOrgCreditService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.entity.Order;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

@Service("creditRechargeServiceImpl")
public class CreditRechargeServiceImpl extends
        ActWfBillServiceImpl<CreditRecharge> implements CreditRechargeService {

	@Resource(name = "creditRechargeDao")
	private CreditRechargeDao cepositRechargeDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "lockDataDao")
	private LockDataDao lockDataDao;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "saleOrgCreditServiceImpl")
	private SaleOrgCreditService saleOrgCreditService;
	@Resource(name = "customerRechargeServiceImpl")
	private CustomerRechargeService customerRechargeService;
	@Resource(name = "oaToWfServiceImpl")
	private OaToWfService oaToWfService;
	@Resource(name = "systemParameterBaseServiceImpl")
	private SystemParameterBaseService systemParameterBaseService;
	@Resource(name = "aftersaleServiceImpl")
	private AftersaleService aftersaleService;
	/**
	 * 用户、客户余额充值列表数据
	 */
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Integer type, String sn,
			Long storeMemberId, Long storeId, Integer[] status, Long creatorId,
			Long operatorId, Integer rechargeType, Long[] organizationId,
			Long sbuId,Long saleOrgId,String storeMemberName,String startFirstTime,
			String startLastTime,String endFirstTime,String endLastTime, Pageable pageable) {
		return cepositRechargeDao.findPage(sn,
				storeMemberId,
				storeId,
				status,
				creatorId,
				operatorId,
				rechargeType,
				organizationId,
				sbuId,
				saleOrgId,
				storeMemberName,
				startFirstTime,
				startLastTime,
				endFirstTime,
				endLastTime,
				pageable);
	}
	
	/**
	 * 用户、客户余额充值列表数据导出
	 */
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findTable(Integer[] status,Long storeId,Long organizationId,Long sbuId) {
		return cepositRechargeDao.findTable(status,storeId,organizationId,sbuId);
	}

	@Override
	@Transactional
	public void check(CreditRecharge creditRecharge, Integer flag, String note,
			BigDecimal actualAmount) {
		creditRecharge.setNote(note);
		creditRecharge.setCheckDate(new Date());
		creditRecharge.setOperator(storeMemberService.getCurrent());
		if (flag == 2) {
			creditRecharge.setStatus(2); // 审核未通过
			creditRecharge.setDocStatus(3);
		}
		else if (flag == 1) {
			creditRecharge.setStatus(1); // 审核通过
			creditRecharge.setDocStatus(2);
			if (actualAmount == null) {
				actualAmount = creditRecharge.getAmount();
			}
			creditRecharge.setActualAmount(actualAmount);

			Date startDate = creditRecharge.getStartDate();
			Date endDate = DateUtils.addDays(creditRecharge.getEndDate(), 1);
			Date nowDate = new Date();
			if (startDate != null && endDate != null) {
				if (nowDate.compareTo(startDate) >= 0
						&& nowDate.compareTo(endDate) < 0) {
					creditRecharge.setType(1);
				}
			}
		}
		update(creditRecharge);
		List<Map<String, Object>> saleOrgCreditList = saleOrgCreditService.findListBySaleOrg(creditRecharge.getSaleOrg()
				.getId());
		if (saleOrgCreditList != null && saleOrgCreditList.size() > 0) {
			Long saleOrgCreditId = Long.valueOf(saleOrgCreditList.get(0)
					.get("id")
					.toString());
			SaleOrgCredit saleOrgCredit = saleOrgCreditService.find(saleOrgCreditId);
			saleOrgCredit.setUsedAmount(actualAmount.add(saleOrgCredit.getUsedAmount()));
			saleOrgCreditService.update(saleOrgCredit);
		}

		orderFullLinkService.addFullLink(8,
				null,
				creditRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18702", new Object[] { "临时额度申请" }),
				null);
		syncCreditRechargeToAftersale(creditRecharge);

	}





	public void creditRecharge(Long companyInfoId, Payment payment,
			BigDecimal modifyBalance, CreditRecharge creditRecharge) {

		Store store = creditRecharge.getStore();
		lockDataDao.lockStore(store.getId().toString());

		// 生成一张付款单
		paymentService.initPayment(8,
				3,
				5,
				1,
				1,
				modifyBalance,
				new Date(),
				null,
				null,
				creditRecharge.getStore(),
				null,
				creditRecharge.getSn(),
				null,
				null,
				null,
				"临时额度生效");

		// 修改客户余额
		store.setBalance(store.getBalance().add(modifyBalance));
		store.setCredit(store.getCredit().add(modifyBalance));
		storeService.update(store);
	}

	@Override
	@Transactional
	public void effective(CreditRecharge creditRecharge) {

		creditRecharge = find(creditRecharge.getId());
		creditRecharge.setType(1);
		update(creditRecharge);
	}

	@Override
	@Transactional
	public void invalid(CreditRecharge creditRecharge) {

		creditRecharge = find(creditRecharge.getId());
		creditRecharge.setType(2);
		update(creditRecharge);
	}

	public Payment initPayment(Integer type, Integer termType, Integer payType,
			Integer method, Integer status, BigDecimal amount,
			Date paymentDate, StoreMember storeMember, Order order,
			Store store, SaleOrg saleOrg, String elseSn, String bank,
			String account, String tradeNo, String memo) {

		Long companyInfoId = null;
		if (storeMember != null) {
			companyInfoId = storeMember.getCompanyInfoId();
		}
		else if (store != null) {
			companyInfoId = store.getCompanyInfoId();
		}
		else if (saleOrg != null) {
			companyInfoId = saleOrg.getCompanyInfoId();
		}
		else {
			companyInfoId = order.getCompanyInfoId();
		}
		CompanyInfo companyInfo = companyInfoService.find(companyInfoId);
		Integer paymentPrefix = companyInfo.getPaymentPrefix();
		String hexString = Integer.toHexString(paymentPrefix).toUpperCase();
		if (hexString.length() == 1) {
			hexString = "0" + hexString;
		}
		Payment payment = new Payment();
		String sn = SnUtil.generateSn();
		payment.setSn(sn);
		payment.setUnifiedSn(sn);
		payment.setType(type);
		payment.setTermType(termType);
		payment.setPayType(payType);
		payment.setStatus(status);
		payment.setMethod(method);
		payment.setAmount(amount);
		payment.setFee(new BigDecimal(0));
		payment.setStoreMember(storeMember);
		payment.setOrder(order);
		payment.setStore(store);
		payment.setSaleOrg(saleOrg);
		payment.setElseSn(elseSn);
		payment.setPaymentDate(new Date());
		if (status == 1 && paymentDate == null) {
			payment.setPaymentDate(new Date());
		}
		else {
			payment.setPaymentDate(paymentDate);
		}
		payment.setBank(bank);
		payment.setAccount(account);
		payment.setTradeNo(tradeNo);
		payment.setMemo(memo);
		payment.setOperateStoreMember(storeMember);
		payment.setCompanyInfoId(companyInfoId);
		paymentService.save(payment);

		return payment;
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findShowPage(Long storeId, String sn,
			Integer rechargeType, Pageable pageable) {
		return cepositRechargeDao.findShowPage(storeId,
				sn,
				rechargeType,
				pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findList(Long storeId, String sn) {
		return cepositRechargeDao.findList(storeId, sn);
	}

	@Override
	public List<Map<String, Object>> findCreditRechargeList(Integer[] type,
			String sn, Long storeMemberId, Long storeId, Long saleOrgId,
			Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, BigDecimal minPrice,
			BigDecimal maxPrice, String firstTime, String lastTime, Long sbuId,
			Long[] ids, Pageable pageable, Integer page, Integer size) {
		return cepositRechargeDao.findCreditRechargeList(type,
				sn,
				storeMemberId,
				storeId,
				saleOrgId,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				null,
				minPrice,
				maxPrice,
				firstTime,
				ids,
				lastTime,
				sbuId,
				pageable,
				page,
				size);
	}

	public Integer countCreditRecharge(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			Long sbuId, String firstTime, String lastTime, Long[] ids,
			Pageable pageable, Integer page, Integer size) {
		return cepositRechargeDao.countCreditRecharge(type,
				sn,
				storeMemberId,
				storeId,
				saleOrgId,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				null,
				minPrice,
				maxPrice,
				sbuId,
				firstTime,
				lastTime,
				ids,
				pageable,
				page,
				size);
	}

	public List<Map<String, Object>> findIsHaveSameStroe(Long storeId,
			Long storeMemberId, String startDate, Integer rechargeType,
			Long organizationId) {
		return cepositRechargeDao.findIsHaveSameStroe(storeId,
				storeMemberId,
				startDate,
				rechargeType,
				organizationId);
	}

	public boolean findJurisdiction(StoreMember storeMember) {
		return cepositRechargeDao.findJurisdiction(storeMember);
	}

	public List<Map<String, Object>> findSbu(Long id) {
		return cepositRechargeDao.findSbu(id);
	}

    /**
     * 新流程启动
     * @param id
     * @param modelId
     * @param objTypeId
     * @param creditRecharge
     * @param flag
     * @param note
     * @param actualAmount
     */
	@Transactional
    @Override
    public void createWf(Long id, String modelId, Long objTypeId, CreditRecharge creditRecharge, Integer flag, String note, BigDecimal actualAmount) {

        creditRecharge.setNote(note);
        if (actualAmount == null) {
            actualAmount = creditRecharge.getAmount();
        }
        creditRecharge.setActualAmount(actualAmount);
        StoreMember storeMember = storeMemberService.getCurrent();
        // 创建流程实例
        createWf(creditRecharge.getSn(),
                String.valueOf(storeMember.getId()),
                new Long[]{creditRecharge.getSaleOrg().getId()},
                creditRecharge.getStore().getId(),modelId,objTypeId,
                id,WebUtils.getCurrentCompanyInfoId(),true);
        // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
        creditRecharge.setDocStatus(1); 
        // 2.已处理(流程走完) 3.关闭
        update(creditRecharge);
        List<Map<String, Object>> saleOrgCreditList = saleOrgCreditService.findListBySaleOrg(creditRecharge.getSaleOrg().getId());
        if (saleOrgCreditList != null && saleOrgCreditList.size() > 0) {
            Long saleOrgCreditId = Long.valueOf(saleOrgCreditList.get(0).get("saleOrgCreditId").toString());
            SaleOrgCredit saleOrgCredit = saleOrgCreditService.find(saleOrgCreditId);
            BigDecimal usedAmount = saleOrgCredit.getUsedAmount() == null ? BigDecimal.ZERO : saleOrgCredit.getUsedAmount();
            saleOrgCredit.setUsedAmount(actualAmount.add(usedAmount));
            saleOrgCreditService.update(saleOrgCredit);
        }
        orderFullLinkService.addFullLink(8,null,creditRecharge.getSn(),
                ConvertUtil.convertI18nMsg("18701",new Object[] { "授信申请" }),null);

		syncCreditRechargeToAftersale(creditRecharge);
	}

	private void syncCreditRechargeToAftersale(CreditRecharge creditRecharge) {
		if(creditRecharge.getFourAftersaleSn() != null){
			CreditRechargeVo vo = new CreditRechargeVo();
			vo.setAftersaleSn(creditRecharge.getFourAftersaleSn());
			vo.setCreditRechargeSn(creditRecharge.getSn());
			vo.setCreditRechargeDocStatus(creditRecharge.getDocStatus());
			vo.setCreditRechargeActualAmount(creditRecharge.getActualAmount());
			vo.setCreditRechargeCheckDate(creditRecharge.getCheckDate());
			aftersaleService.syncCreditRechargeToAftersale(vo);
		}
	}

	/**
	 * 流程结束回调  原agreeBack()方法
	 */
	@Override
	@Transactional
	public void endBack(ActWf wf) {
		super.endBack(wf);
		if (wf.getStat() == 2) {
			CreditRecharge creditRecharge = find(wf.getObjId());
			//校验授信单状态
			this.checkCreditRechargeStatus(creditRecharge,1,"处理中","通过");
			Date startDate = creditRecharge.getStartDate();
			Date endDate = DateUtils.addDays(creditRecharge.getEndDate(), 1);
			Date nowDate = new Date();
			if (!ConvertUtil.isEmpty(startDate) && !ConvertUtil.isEmpty(endDate)) {
				if (nowDate.compareTo(startDate) >= 0 && nowDate.compareTo(endDate) < 0) {
					creditRecharge.setType(1);
				}
			}
			creditRecharge.setCheckDate(new Date());
			creditRecharge.setOperator(storeMemberService.getCurrent());
			/**客户充值*/
			CustomerRecharge customerRecharge = new CustomerRecharge();
			// 客户
			customerRecharge.setStore(creditRecharge.getStore());
			// 经营组织
			customerRecharge.setOrganization(creditRecharge.getOrganization());
			// sbu
			customerRecharge.setSbu(creditRecharge.getSbu());
			customerRechargeService.saveOrUpdate(customerRecharge);
			// 审核通过
			creditRecharge.setStatus(1); 
			// 单据状态 0.已保存(没有流程) 1.处理中(有流程)
			creditRecharge.setDocStatus(2); 
			// 2.已处理(流程走完) 3.关闭
			update(creditRecharge);

			syncCreditRechargeToAftersale(creditRecharge);
		}
	}

	/**
	 * 中断流程回调
	 */
	@Override
	@Transactional
	public void interruptBack(ActWf wf) {//原interruptBack()方法
		CreditRecharge creditRecharge = find(wf.getObjId());
		//校验授信单状态
		this.checkCreditRechargeStatus(creditRecharge,1,"处理中","中断");
		// 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.关闭
		creditRecharge.setDocStatus(0); 
		creditRecharge.setCheckDate(null);
		creditRecharge.setOperator(null);
		update(creditRecharge);
		orderFullLinkService.addFullLink(8,null,creditRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18704"),null);
		syncCreditRechargeToAftersale(creditRecharge);

	}

	
	/**
	 * 驳回流程回调
	 */
	@Override
	@Transactional
	public void rejectBack(ActWf wf) {
		CreditRecharge creditRecharge = find(wf.getObjId());
		//校验授信单状态
		this.checkCreditRechargeStatus(creditRecharge,1,"处理中","驳回");
		creditRecharge.setWfState(3);
		update(creditRecharge);
	}

	/** 流程开始回调 */
	@Transactional
	public void startBack(ActWf wf){
		//获取系统参数
		Long currentCompanyInfoId = WebUtils.getCurrentCompanyInfoId();
		System.out.println("currentCompanyInfoId:"+currentCompanyInfoId);
		Integer flagNum = Integer.valueOf(systemParameterBaseService.getValue(currentCompanyInfoId, "creditPushOA"));
		if (flagNum!=null && flagNum==1){
			if(getCurrTaskByWf(wf)!=null){
				List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
				for(String receiver : user){
					System.out.println("receiver:"+receiver);
					oaToWfService.receiveRequestInfoByJson(wf,new String[]{getCurrTaskByWf(wf).getName(),findUserName(receiver)});
				}
			}
		}
	};

	public String findUserName(String id) {
		StoreMember storeMember = storeMemberService.find(Long.parseLong(id));
		if (storeMember == null) {
			return "";
		} else {
			return storeMember.getUsername();
		}
	}




	/**
	 * 通过流程节点回调
	 */
	@Override
	@Transactional
	public void agreeBack(ActWf wf) {
		CreditRecharge creditRecharge = find(wf.getObjId());
		if(creditRecharge.getWfState() == 3){
			//校验授信单状态
			this.checkCreditRechargeStatus(creditRecharge,1,"处理中","通过");
			creditRecharge.setWfState(1);
			update(creditRecharge);
		}
	}
	
	
	@Override
	public Map<String, Object> setFormData(ActWf wf, String taskId) {
		Map<String,Object> map = new HashMap<String, Object>();
		CreditRecharge creditRecharge = this.find(wf.getObjId());
		map.put("organizationId",creditRecharge.getOrganization().getId());
		map.put("saleOrgId",creditRecharge.getSaleOrg().getId());
		return map;
	}
	
	/**
	 * 校验授信单状态
	 */
	@Override
	public void checkCreditRechargeStatus(CreditRecharge creditRecharge, 
			Integer status, String statusName, String operationName) {
		if(ConvertUtil.isEmpty(creditRecharge)){
			ExceptionUtil.throwServiceException("该授信单不存在");
        }
        if (!ConvertUtil.isEmpty(creditRecharge.getDocStatus()) && creditRecharge.getDocStatus() != status) {
        	ExceptionUtil.throwServiceException("只有单据状态为"+statusName+"的授信单才能"+operationName);
        }
	}
}
