package net.shopxx.member.service.impl;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import net.sf.ehcache.CacheManager;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Global;
import net.shopxx.base.core.entity.Principal;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.EhCacheUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.HttpClientUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.dao.StoreMemberBaseDao;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.LoginOutBaseService;
import net.shopxx.member.service.MemberBaseService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;

/**
 * Service - 会员
 */
@Service("loginOutBaseServiceImpl")
public class LoginOutBaseServiceImpl extends BaseServiceImpl<StoreMember>
		implements LoginOutBaseService {

	@Resource(name = "memberBaseServiceImpl")
	private MemberBaseService memberBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "ehCacheManager")
	private CacheManager cacheManager;
	@Resource(name = "storeMemberBaseDao")
	private StoreMemberBaseDao storeMemberBaseDao;
	

	public void submitLogin(String username, String password,
			String companyName, String language, HttpServletRequest request,
			HttpServletResponse response, HttpSession session,Integer flag) {

		language = StringUtils.isEmpty(language) ? "ChinaCN" : language;

		if (StringUtils.isEmpty(username)) {
			//请输入用户名
			ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3001",
					language));
		}
		if (StringUtils.isEmpty(password)) {
			//请输入密码
			ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3002",
					language));
		}
		StoreMember storeMember = null;
		CompanyInfo companyInfo = null;
		List<StoreMember> storeMembers;
		List<Filter> filters = new ArrayList<Filter>();
		if (StringUtils.isEmpty(companyName)) {
			storeMembers=storeMemberBaseService.findDefaultByUsername(username,
					null);
			if(storeMembers.size()>0){
				storeMember = storeMembers.get(0);
			}else{
				//该用户不存在
				ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3003",
						language));
			}
		}
		else {
			filters.clear();
			filters.add(Filter.eq("simple_name", companyName));
			try {
				companyInfo = companyInfoBaseService.find(filters);
			}
			catch (Exception e) {}
			if (companyInfo == null) {
				filters.clear();
				filters.add(Filter.eq("company_name", companyName));
				companyInfo = companyInfoBaseService.find(filters);
			}
			if (companyInfo == null) {
				//根据输入的企业简称或全称找不到企业
				ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3005",
						language));
			}
			if (companyInfo.getIsEnabled() == null
					|| !companyInfo.getIsEnabled()) {
				//该企业已禁用
				ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3009",
						language));
			}
			storeMembers=storeMemberBaseService.findDefaultByUsername(username,
					companyInfo.getId());
			if(storeMembers.size()>0){
				storeMember = storeMembers.get(0);
			}else{
				//该用户不存在
				ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3003",
						language));
			}
		}
		if (storeMember != null) {
			if (!storeMember.getIsEnabled()) {
				//该账号已被禁用
				ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3006",
						language));
			}
			//获取storeMember里面的密码
			if(flag!=1){
				if (!DigestUtils.md5Hex(password).equals(storeMember.getPassword())) {
					//密码错误
					ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3007",
							language));
				}
			}
			
			storeMember.setLoginIp(request.getRemoteAddr());
			storeMember.setLoginDate(new Date());
			storeMemberBaseService.update(storeMember);
		}
		else {
			//该用户不存在
			ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("3003",
					language));
		}

		//查询是否企业管理员
		int isadmin = 0;
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		if (companyInfo == null) {
			filters.add(Filter.isNull("companyInfoId"));
		}
		else {
			filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
		}
		List<PcUserRole> pcUserRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		for (PcUserRole pcUserRole : pcUserRoles) {
			PcRole pcRole = pcUserRole.getPcRole();
			if (pcRole.getCompanyInfoId() == null) {
				isadmin = 1;
				break;
			}
		}

		WebUtils.setPrincipal(storeMember.getId(),
				storeMember.getUsername(),
				companyInfo == null ? null : companyInfo.getId(),
				companyInfo == null ? null : companyInfo.getCompany_code(),
				language,
				isadmin);

		Setting setting = SettingUtils.get();
		String loginDomain = setting.getLoginDomain();
		if (!ConvertUtil.isEmpty(loginDomain)) {
			//加入cookie及缓存
			String uuid = UUID.randomUUID().toString();
			WebUtils.addCookie(request,
					response,
					Global.PRINCIPAL_PROPERTY_NAME,
					uuid);
			EhCacheUtil.put(Global.PRINCIPAL_PROPERTY_NAME,
					uuid,
					WebUtils.getPrincipal());
		}
	}

	public String oauth(String userInfo, String language,
			HttpServletRequest request) {
		try {
			String company = "大自然家居(中国)有限公司";
			LogUtils.info("企业：" + company + "，单点登录用户信息：" + userInfo);
//			Map<String, Object> userMap = JsonUtils.toObjectMap(userInfo);
			String username = userInfo;
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("company_name", company));
			CompanyInfo companyInfo = companyInfoBaseService.find(filters);
			if (companyInfo == null) {
				return "根据企业简称或全称找不到企业";
			}
			if (companyInfo.getIsEnabled() == null
					|| !companyInfo.getIsEnabled()) {
				return "该企业已禁用";
			}
			List<StoreMember> storeMembers = storeMemberBaseService.findDefaultByUsername(username,
					companyInfo.getId());
			StoreMember storeMember;
			if (storeMembers.size()==0) {
				return "用户[" + username + "]不存在";
			}else{
				storeMember=storeMembers.get(0);
				storeMember.setLoginIp(request.getRemoteAddr());
				storeMember.setLoginDate(new Date());
				storeMemberBaseService.update(storeMember);
			}

			//查询是否企业管理员
			int isadmin = 0;
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMember));
			filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
			List<PcUserRole> pcUserRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			for (PcUserRole pcUserRole : pcUserRoles) {
				PcRole pcRole = pcUserRole.getPcRole();
				if (pcRole.getCompanyInfoId() == null) {
					isadmin = 1;
					break;
				}
			}
			WebUtils.setPrincipal(storeMember.getId(),
					storeMember.getUsername(),
					companyInfo == null ? null : companyInfo.getId(),
					companyInfo == null ? null : companyInfo.getCompany_code(),
					language,
					isadmin);
			
			String msg = "";
			List<StoreMember> sms = findStoreMember(storeMember.getIdCard());
			if(sms.size()==1 && sms.get(0).getIdCard()!=null){
				msg= "success";
			}else if(sms.size()>0 && sms.size()!=1){
				LogUtils.info("当前用户不唯一");
				msg= "moreThanOne";
			}else if(sms.size()==0){
				msg= "登陆失败，请维护身份信息";
			}
			return msg;
			
		} catch (Exception e) {
			LogUtils.error("门户单点登录", e);
			return "获取门户用户名失败";
		}
		
	}

	public String SSO(String company, String loginguid, String language,
			HttpServletRequest request) {
		try {
			loginguid = URLEncoder.encode(loginguid, "UTF-8");
			String userInfo = HttpClientUtil.get("http://cloud.sinocc.com/openservice/?action=getUserInfo&loginguid="
					+ loginguid);
			LogUtils.info("企业：" + company + "，单点登录用户信息：" + userInfo);
			Map<String, Object> userMap = JsonUtils.toObjectMap(userInfo);
			String username = userMap.get("username").toString();
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("company_name", company));
			CompanyInfo companyInfo = companyInfoBaseService.find(filters);
			if (companyInfo == null) {
				return "根据企业简称或全称找不到企业";
			}
			if (companyInfo.getIsEnabled() == null
					|| !companyInfo.getIsEnabled()) {
				return "该企业已禁用";
			}
			List<StoreMember> storeMembers = storeMemberBaseService.findDefaultByUsername(username,
					companyInfo.getId());
			StoreMember storeMember;
			if (storeMembers.size()==0) {
				return "用户[" + username + "]不存在";
			}else{
				storeMember=storeMembers.get(0);
				storeMember.setLoginIp(request.getRemoteAddr());
				storeMember.setLoginDate(new Date());
				storeMemberBaseService.update(storeMember);
			}

			//查询是否企业管理员
			int isadmin = 0;
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMember));
			filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
			List<PcUserRole> pcUserRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			for (PcUserRole pcUserRole : pcUserRoles) {
				PcRole pcRole = pcUserRole.getPcRole();
				if (pcRole.getCompanyInfoId() == null) {
					isadmin = 1;
					break;
				}
			}
			WebUtils.setPrincipal(storeMember.getId(),
					storeMember.getUsername(),
					companyInfo == null ? null : companyInfo.getId(),
					companyInfo == null ? null : companyInfo.getCompany_code(),
					language,
					isadmin);
			String msg = null;
			List<StoreMember> sms = findStoreMember(storeMember.getIdCard());
			if(sms.size()==1 && sms.get(0).getIdCard()!=null){
				msg= "success";
				
			}else if(sms.size()>0 && sms.size()!=1){
				msg= "moreThanOne";
			}else if(sms.size()==0){
				msg= "登陆失败，请维护身份信息";
			}
			return msg;
		}
		catch (Exception e) {
			LogUtils.error("门户单点登录", e);
			return "获取门户用户名失败";
		}
		
	}

	public String logout(HttpServletRequest request,
			HttpServletResponse response) {

		Principal principal = WebUtils.getPrincipal();
		String language = StringUtils.isEmpty(principal.getLanguage()) ? "ChinaCN"
				: principal.getLanguage();
		WebUtils.removePrincipal();

		Setting setting = SettingUtils.get();
		String loginDomain = setting.getLoginDomain();
		if (!ConvertUtil.isEmpty(loginDomain)) {
			String uuid = WebUtils.getCookie(request,
					Global.PRINCIPAL_PROPERTY_NAME);
			if (!ConvertUtil.isEmpty(uuid)) {
				EhCacheUtil.remove(Global.PRINCIPAL_PROPERTY_NAME, uuid);
				WebUtils.removeCookie(request,
						response,
						Global.PRINCIPAL_PROPERTY_NAME);
			}
		}

		return language;
	}
	
	@Override
	public List<StoreMember> findStoreMember(String username) {

		return storeMemberBaseDao.findStoreMember(username);
	}
}
