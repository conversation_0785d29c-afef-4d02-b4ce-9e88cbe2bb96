package net.shopxx.member.service.impl;

import java.util.*;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.entity.Principal;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.dao.PcMenuBaseDao;
import net.shopxx.member.entity.PcMenu;
import net.shopxx.member.entity.PcMenuRole;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcMenuBaseService;
import net.shopxx.member.service.PcMenuRoleService;
import net.shopxx.member.service.PcRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service - 会员等级
 */
@Service("pcMenuBaseServiceImpl")
public class PcMenuBaseServiceImpl extends BaseServiceImpl<PcMenu>
		implements PcMenuBaseService {

	@Resource(name = "pcMenuBaseDao")
	private PcMenuBaseDao pcMenuBaseDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	@Resource(name = "pcMenuRoleServiceImpl")
	private PcMenuRoleService pcMenuRoleService;

    @Resource(name = "pcRoleBaseServiceImpl")
	private PcRoleBaseService pcRoleBaseService;

	@Override
	@Transactional(readOnly = true)
	public List<PcMenu> getCurrentMenuList(Integer type) {

		List<PcMenu> menus = new ArrayList<PcMenu>();
		Principal principal = WebUtils.getPrincipal();
		if (principal != null) {
			StoreMember storeMember = storeMemberBaseService
					.find(principal.getId());
			menus = pcMenuBaseDao.findMenuList(storeMember.getId().toString(),
					principal.getCompanyinfoid(),
					type);
		}
		return menus;
	}

	@Override
	@Transactional(readOnly = true)
	public Integer findMenuCount(Integer type, String code) {

		Integer count = 0;
		Principal principal = WebUtils.getPrincipal();
		if (principal != null) {
			StoreMember storeMember = storeMemberBaseService
					.find(principal.getId());
			count = pcMenuBaseDao.findMenuCount(storeMember.getId().toString(),
					principal.getCompanyinfoid(),
					type,
					code);
		}
		return count;
	}

	@Override
	public List<Map<String,Object>> getAllSecondaryMenus(String menuName) {
		return pcMenuBaseDao.getAllSecondaryMenus(menuName);
	}

    @Override
    public Page<Map<String, Object>> findMenuMaintenanceList(Integer type,int isSuper, Long superId, String menuName, Boolean isEnabled,String createDate, Pageable pageable) {
        return pcMenuBaseDao.findMenuMaintenanceList(type,isSuper,superId, menuName, isEnabled, createDate, pageable);
    }

    @Override
    public Long saveMenu(PcMenu pcMenu, Long parentdmenuId) {
        if(ConvertUtil.isEmpty(pcMenu.getMenuName())){
            throw new RuntimeException("菜单名称不能为空");
        }
        if(!ConvertUtil.isEmpty(parentdmenuId)){
            PcMenu parentPcMenu = find(parentdmenuId);
            if(!ConvertUtil.isEmpty(parentPcMenu)){
                if(pcMenu.getMenuName().equals(parentPcMenu.getMenuName())){
                    throw new RuntimeException("父菜单不能与菜单名称一致");
                }
                pcMenu.setSuperId(parentPcMenu);
            }
        }
        if (!ConvertUtil.isEmpty(pcMenu.getNumber())){
            pcMenu.setOrder(pcMenu.getNumber());
        }
		save(pcMenu);
        /**默认添加管理员权限**/
        //获取管理员信息
        List<Filter> fis = new ArrayList<Filter>();
        fis.add(Filter.eq("name", "管理员"));
        PcRole pcRoles = pcRoleBaseService.find(fis);
		pcMenuBaseDao.saveMenuRole(pcMenu.getId(),pcRoles.getId());
        return pcMenu.getId();
    }

	@Override
	public void deleteMenu(Long menuId,Long pcRole) {
		PcMenu pcMenu = find(menuId);
		if (ConvertUtil.isEmpty(pcMenu)){
			throw new RuntimeException("菜单不存在");
		}else {
			//删除菜单角色关联
			pcMenuBaseDao.deleteMenuRole(menuId,pcRole);
			//删除菜单
			delete(pcMenu);
		}
	}

	@Override
	public void deleteRole(Long menuId, Long pcRole) {
		if (ConvertUtil.isEmpty(menuId)||ConvertUtil.isEmpty(pcRole)){
			throw new RuntimeException("菜单ID或角色ID不能为空!");
		}
		//删除菜单角色关联
		pcMenuBaseDao.deleteMenuRole(menuId,pcRole);
	}

    @Override
    public List<PcMenu> findAllMenuList(Integer type) {
        List<PcMenu> menus = pcMenuBaseDao.findAllMenuList(type);
        return menus;
    }
}