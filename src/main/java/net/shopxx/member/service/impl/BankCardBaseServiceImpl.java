package net.shopxx.member.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.member.dao.BankCardBaseDao;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.BankCardBaseService;

import net.shopxx.member.service.StoreBaseService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service("bankCardBaseServiceImpl")
public class BankCardBaseServiceImpl extends BaseServiceImpl<BankCard> implements BankCardBaseService {

	@Resource(name = "bankCardBaseDao")
	private BankCardBaseDao bankCardBaseDao;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Pageable pageable, Long saleOrgId, String bankName, 
			String bankCardNo,String mobile, Boolean isEnabled,Long sbuId,Boolean isTotalAccount,
			String bankCode) {
			
		return bankCardBaseDao.findPage(pageable, saleOrgId, bankName, bankCardNo, mobile, isEnabled,
				sbuId,isTotalAccount,bankCode);
	}

	@Override
	public List<BankCard> findListBySbu(Long saleOrgId,Long organizationId,Long sbuId) {

		return bankCardBaseDao.findListBySbu(saleOrgId,organizationId,sbuId);
	}

    @Override
    public void importFromExcel(MultipartFile multipartFile) throws IOException, BiffException {
		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}

		//导入的字段
		String bankName;//*银行名称
		String bankCode;// 银行代号
		String createy;// 开户人
		String mobile;// 预留手机号码
		String bankCardNo;// 帐号
		String address;// 银行地址
		String organizationStr;// 经营组织
		String saleOrgStr;//机构
		String areaName;// 大区
		String isEnabledStr;// 是否启用
		String sbuStr;// sbu
		String isInternalAccountStr;// *是否内部账号
		String isTotalAccountStr;//*是否总账号
		String isNewBankCodeStr;// *是否新账号
		String memo;// 备注



		List<Filter> filters = new ArrayList<Filter>();
        List<Filter> filterAll = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		for (int i = 1; i < rows; i++) {

			BankCard bankCard = new BankCard();
			int countCell = 0;

			//*银行名称
			cell = sheet.getCell(countCell++, i);
			bankName = cell.getContents().trim();
			if (ConvertUtil.isEmpty(bankName)) {
				msg = "第" + i + "行." + "银行名称未填";
				ExceptionUtil.throwServiceException(msg);
			}



			// 银行代号
			cell = sheet.getCell(countCell++, i);
			bankCode = cell.getContents().trim();

			// 开户人
			cell = sheet.getCell(countCell++, i);
			createy = cell.getContents().trim();

			// 预留手机号码
			cell = sheet.getCell(countCell++, i);
			mobile = cell.getContents().trim();

			// 帐号
			cell = sheet.getCell(countCell++, i);
			bankCardNo = cell.getContents().trim();
			if (ConvertUtil.isEmpty(bankCardNo)) {
				msg = "第" + i + "行." + "帐号未填";
				ExceptionUtil.throwServiceException(msg);
			}


			// 银行地址
			cell = sheet.getCell(countCell++, i);
			address = cell.getContents().trim();

			// 经营组织
			cell = sheet.getCell(countCell++, i);
			organizationStr = cell.getContents().trim();
			if (!ConvertUtil.isEmpty(organizationStr)) {
				filters.clear();
				filters.add(Filter.eq("name", organizationStr));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				Organization organization = organizationService.find(filters);
				if (organization == null) {
					msg = "第" + i + "行." + "名称为[" + organizationStr + "]的经营组织不存在";
					ExceptionUtil.throwServiceException(msg);
				}
				bankCard.setOrganization(organization);
                filterAll.add(Filter.eq("organization",organization));
			}

			//机构
			cell = sheet.getCell(countCell++, i);
			saleOrgStr = cell.getContents().trim();
			if (!ConvertUtil.isEmpty(saleOrgStr)) {
				filters.clear();
				filters.add(Filter.eq("name", saleOrgStr));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				SaleOrg saleOrg = saleOrgService.find(filters);
				if (saleOrg == null) {
					msg = "第" + i + "行." + "名称为[" + saleOrgStr + "]的机构不存在";
					ExceptionUtil.throwServiceException(msg);
				}
				bankCard.setSaleOrg(saleOrg);
                filterAll.add(Filter.eq("saleOrg",saleOrg));
			}

			// 大区
			cell = sheet.getCell(countCell++, i);
			areaName = cell.getContents().trim();

			// 是否启用
			cell = sheet.getCell(countCell++, i);
			isEnabledStr = cell.getContents().trim();
			Boolean isEnabled = null;
			if (isEnabledStr.equals("是")) {
				isEnabled = true;
			} else {
				isEnabled = false;
			}


			// sbu
			cell = sheet.getCell(countCell++, i);
			sbuStr = cell.getContents().trim();
			if (!ConvertUtil.isEmpty(sbuStr)) {
				filters.clear();
				filters.add(Filter.eq("name", sbuStr));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				Sbu sbu = sbuService.find(filters);
				if (sbu == null) {
					msg = "第" + i + "行." + "名称为[" + sbuStr + "]的SBU不存在";
					ExceptionUtil.throwServiceException(msg);
				}
				bankCard.setSbu(sbu);
                filterAll.add(Filter.eq("sbu",sbu));
			}

			// *是否内部账号
			cell = sheet.getCell(countCell++, i);
			isInternalAccountStr = cell.getContents().trim();
			if (isInternalAccountStr.isEmpty()) {
				msg = "第" + i + "行." + "是否内部账号未填";
				ExceptionUtil.throwServiceException(msg);
			}else {
				Boolean isInternalAccount = null;
				if (isInternalAccountStr.equals("是")) {
					isInternalAccount = true;
				} else {
					isInternalAccount = false;
				}
				bankCard.setIsInternalAccount(isInternalAccount);
			}

			// *是否总账号
			cell = sheet.getCell(countCell++, i);
			isTotalAccountStr = cell.getContents().trim();
			if (isTotalAccountStr.isEmpty()) {
				msg = "第" + i + "行." + "是否总账号未填";
				ExceptionUtil.throwServiceException(msg);
			} else {
				Boolean isTotalAccount = null;
				if (isTotalAccountStr.equals("是")) {
					isTotalAccount = true;
				} else {
					isTotalAccount = false;
				}
				bankCard.setIsTotalAccount(isTotalAccount);
			}

			// *是否新账号
			cell = sheet.getCell(countCell++, i);
			isNewBankCodeStr = cell.getContents().trim();
			if (isNewBankCodeStr.isEmpty()) {
				msg = "第" + i + "行." + "是否新账号未填";
				ExceptionUtil.throwServiceException(msg);
			} else {
				Boolean isNewBankCode = null;
				if (isNewBankCodeStr.equals("是")) {
					isNewBankCode = true;
				} else {
					isNewBankCode = false;
				}
				bankCard.setIsNewBankCode(isNewBankCode);
			}

			// 备注
			cell = sheet.getCell(countCell++, i);
			memo = cell.getContents().trim();


			bankCard.setStore(storeBaseService.getMainStore());
			bankCard.setBankName(bankName);//*银行名称
            bankCard.setBankCode(bankCode);// 银行代号
			bankCard.setCreatey(createy); // 开户人
			bankCard.setMobile(mobile);// 预留手机号码
			bankCard.setBankCardNo(bankCardNo);// 帐号
			bankCard.setAddress(address);// 银行地址
			bankCard.setAreaName(areaName);// 大区
			bankCard.setIsEnabled(isEnabled);// 是否启用
			bankCard.setMemo(memo);// 备注
            //判断是否有重复记录
            filterAll.add(Filter.eq("bankName", bankName));
            filterAll.add(Filter.eq("bankCardNo", bankCardNo));
            filterAll.add(Filter.eq("bankCode", bankCode));
            BankCard bankCardExist=find(filterAll);
            if (bankCardExist == null) {
                save(bankCard);
            }else {
                msg = "第" + i + "行:该收款账号已存在" ;
                ExceptionUtil.throwServiceException(msg);
            }
            filterAll.clear();
		}

    }
}
