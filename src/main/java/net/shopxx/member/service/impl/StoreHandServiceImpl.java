package net.shopxx.member.service.impl;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SalesArea;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SalesAreaService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.dao.StoreMemberBaseDao;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.Store.Type;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreHandService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
@Service("storeHandServiceImpl")
public class StoreHandServiceImpl extends BaseServiceImpl<Store> implements StoreHandService{
	
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "storeMemberBaseDao")
	private StoreMemberBaseDao storeMemberBaseDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "salesareaServiceImpl")
	private SalesAreaService salesAreaService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	
	@Override
	@Transactional
	public String storeSync(Map<String, Object> map) {
		
		try {
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Map<String, Object>  requestInfo = (Map<String, Object>) map.get("requestInfo");
			if(!ConvertUtil.isEmpty(requestInfo)){
				CompanyInfo companyInfo = companyInfoBaseService.find(9L);
				if(ConvertUtil.isEmpty(companyInfo)){
					ExceptionUtil.throwServiceException("当前企业不存在");
				}
				Long companyInfoId = companyInfo.getId();
				//客户编码
				if(ConvertUtil.isEmpty(requestInfo.get("outTradeNo"))){
					ExceptionUtil.throwServiceException("客户编码不能为空");
				}
				String outTradeNo = requestInfo.get("outTradeNo").toString().trim();
				Store store = null;
				List<Filter> filters = new ArrayList<Filter>();
				filters.clear();
				filters.add(Filter.eq("outTradeNo", outTradeNo));
				filters.add(Filter.eq("companyInfoId",companyInfoId));
				List<Store> storeList = storeBaseService.findList(null, filters, null);
				if(storeList.isEmpty() || storeList.size() == 0){
					store = new Store();
				}else if (!storeList.isEmpty() && storeList.size() == 1) {
					store = storeList.get(0);
				}else{
					ExceptionUtil.throwServiceException("客户编码【"+outTradeNo+"】已存在");
				}
				/**基本信息*/
				//客户编号
				String sn = ConvertUtil.isEmpty(requestInfo.get("sn")) ? "" : requestInfo.get("sn").toString();
				store.setSn(sn);
				//区域
				String region = ConvertUtil.isEmpty(requestInfo.get("region")) ? "" : requestInfo.get("region").toString();
				store.setRegion(region);
				//地区
				if(!ConvertUtil.isEmpty(requestInfo.get("newAreaName"))){
					try {
						String newAreaName = requestInfo.get("newAreaName").toString();
						String newAreaFullName = ConvertUtil.isEmpty(requestInfo.get("newAreaFullName")) ? "" : requestInfo.get("newAreaFullName").toString();
						filters.clear();
						filters.add(Filter.eq("name", newAreaName));
						if(!ConvertUtil.isEmpty(newAreaFullName)){
							filters.add(Filter.like("fullName", "%" + newAreaFullName + "%"));
						}
						filters.add(Filter.eq("companyInfoId",companyInfoId));
						Area headNewArea = areaBaseService.find(filters);
						if(!ConvertUtil.isEmpty(headNewArea)){
							store.setHeadNewArea(headNewArea);
						}else{
							store.setHeadNewArea(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("地区查询有误");
					}
				}else{
					store.setHeadNewArea(null);
				}
				//乡镇
				String countryName = ConvertUtil.isEmpty(requestInfo.get("countryName")) ? "" : requestInfo.get("countryName").toString();
				store.setCountryName(countryName);
				//经销商授权编号
				String grantCode = ConvertUtil.isEmpty(requestInfo.get("grantCode")) ? "" : requestInfo.get("grantCode").toString();
				store.setGrantCode(grantCode);
				//经销商姓名
				String dealerName = ConvertUtil.isEmpty(requestInfo.get("dealerName")) ? "" : requestInfo.get("dealerName").toString();
				store.setDealerName(dealerName);
				//经销商性别
				if(!ConvertUtil.isEmpty(requestInfo.get("dealerSex"))){
					Integer dealerSex = Integer.parseInt(requestInfo.get("dealerSex").toString());
					store.setDealerSex(dealerSex);
				}else{
					store.setDealerSex(null);
				}
				//城市等级
				if(!ConvertUtil.isEmpty(requestInfo.get("accountTypeCode"))){
					Integer accountTypeCode = Integer.parseInt(requestInfo.get("accountTypeCode").toString());
					store.setAccountTypeCode(accountTypeCode);
				}else{
					store.setAccountTypeCode(null);
				}
				//客户名称
				String name = ConvertUtil.isEmpty(requestInfo.get("name")) ? "" : requestInfo.get("name").toString();
				store.setName(name);
				//手机号
				String headPhone = ConvertUtil.isEmpty(requestInfo.get("headPhone")) ? "" : requestInfo.get("headPhone").toString();
				store.setHeadPhone(headPhone);
				//经销商学历
				String dealerGrade = ConvertUtil.isEmpty(requestInfo.get("dealerGrade")) ? "" : requestInfo.get("dealerGrade").toString();
				store.setDealerGrade(dealerGrade);
				//业务类型
				if(!ConvertUtil.isEmpty(requestInfo.get("businessTypeName"))){
					try {
						SystemDict businessType = roleJurisdictionUtil.getSystemDict(companyInfoId,"businessType",
								requestInfo.get("businessTypeName").toString());
						if(!ConvertUtil.isEmpty(businessType)){
							store.setBusinessType(businessType);
						}else{
							store.setBusinessType(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("业务类型查询有误");
					}
				}else{
					store.setBusinessType(null);
				}
				//客户简称
				String alias = ConvertUtil.isEmpty(requestInfo.get("alias")) ? "" : requestInfo.get("alias").toString();
				store.setAlias(alias);
				//固定号码
				String fixedNumber = ConvertUtil.isEmpty(requestInfo.get("fixedNumber")) ? "" : requestInfo.get("fixedNumber").toString();
				store.setFixedNumber(fixedNumber);
				//公司性质
				if(!ConvertUtil.isEmpty(requestInfo.get("companyType"))){
					Integer companyType = Integer.parseInt(requestInfo.get("companyType").toString());
					store.setCompanyType(companyType);
				}else{
					store.setCompanyType(null);
				}
				//总经销商
				String franchisee = ConvertUtil.isEmpty(requestInfo.get("franchisee")) ? "" : requestInfo.get("franchisee").toString();
				store.setFranchisee(franchisee);
				//经销商状态
				if(!ConvertUtil.isEmpty(requestInfo.get("distributorStatusName"))){
					try {
						SystemDict distributorStatus = roleJurisdictionUtil.getSystemDict(companyInfoId,"distributorStatus",
								requestInfo.get("distributorStatusName").toString());
						if(!ConvertUtil.isEmpty(distributorStatus)){
							store.setDistributorStatus(distributorStatus);
						}else{
							store.setDistributorStatus(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("经销商状态查询有误");
					}
				}else{
					store.setDistributorStatus(null);
				}
				//身份证信息
				String identity = ConvertUtil.isEmpty(requestInfo.get("identity")) ? "" : requestInfo.get("identity").toString();
				store.setIdentity(identity);
				//平台性质
				if(!ConvertUtil.isEmpty(requestInfo.get("platformProperty"))){
					Integer platformProperty = Integer.parseInt(requestInfo.get("platformProperty").toString());
					store.setPlatformProperty(platformProperty);
				}else{
					store.setPlatformProperty(null);
				}
				//机构
				if(!ConvertUtil.isEmpty(requestInfo.get("saleOrgName"))){
					try {
						filters.clear();
						filters.add(Filter.eq("name", requestInfo.get("saleOrgName").toString()));
						filters.add(Filter.eq("companyInfoId", companyInfoId));
						SaleOrg saleOrg = saleOrgBaseService.find(filters);						
						if(!ConvertUtil.isEmpty(saleOrg)){
							store.setSaleOrg(saleOrg);
						}else{
							store.setSaleOrg(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("机构查询有误");
					}
				}else{
					store.setSaleOrg(null);
				}
				//是否可发货
				if(!ConvertUtil.isEmpty(requestInfo.get("shippingState"))){
					Integer shippingState = Integer.parseInt(requestInfo.get("shippingState").toString());
					store.setShippingState(shippingState);
				}else{
					store.setShippingState(null);
				}
				//合同主体
				String contractSubject = ConvertUtil.isEmpty(requestInfo.get("contractSubject")) ? "" : requestInfo.get("contractSubject").toString();
				store.setContractSubject(contractSubject);
				//销售平台
				if(!ConvertUtil.isEmpty(requestInfo.get("salesPlatformName"))){
					try {
						filters.clear();
						filters.add(Filter.eq("name", requestInfo.get("salesPlatformName").toString()));
						filters.add(Filter.eq("companyInfoId", companyInfoId));
						SaleOrg salesPlatform = saleOrgBaseService.find(filters);
						if(!ConvertUtil.isEmpty(salesPlatform)){
							store.setSalesPlatform(salesPlatform);
						}else{
							store.setSalesPlatform(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("销售平台查询有误");
					}
				}else{
					store.setSalesPlatform(null);
				}
				//区域经理
				if(!ConvertUtil.isEmpty(requestInfo.get("storeMemberName"))){
					List<StoreMember> storeMemberList = storeMemberBaseDao.findStoreMemberList(companyInfoId,requestInfo.get("storeMemberName").toString(),null);
					if(!storeMemberList.isEmpty() && storeMemberList.size()==1){
						store.setStoreMember(storeMemberList.get(0));
					}else{
						store.setStoreMember(null);
					}
				}else{
					store.setStoreMember(null);
				}
				//sbu
				if(!ConvertUtil.isEmpty(requestInfo.get("sbuName"))){
					try {
						SystemDict sbu = roleJurisdictionUtil.getSystemDict(companyInfoId,"sbu",
								requestInfo.get("sbuName").toString());
						if(!ConvertUtil.isEmpty(sbu)){
							store.setSbu(sbu);
						}else{
							store.setSbu(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("sbu查询有误");
					}
				}else{
					store.setSbu(null);
				}
				//法人代表
				String contact = ConvertUtil.isEmpty(requestInfo.get("contact")) ? "" : requestInfo.get("contact").toString();
				store.setContact(contact);
				//销售区域
				if(!ConvertUtil.isEmpty(requestInfo.get("salesAreaName"))){
					try {
						filters.clear();
						filters.add(Filter.eq("name", requestInfo.get("salesAreaName").toString()));
						filters.add(Filter.eq("companyInfoId", companyInfoId));
						SalesArea salesArea = salesAreaService.find(filters);
						if(!ConvertUtil.isEmpty(salesArea)){
							store.setSalesArea(salesArea);
						}else{
							store.setSalesArea(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("AM地区数据有误");
					}
				}else{
					store.setSalesArea(null);
				}
				//经销商关系说明
				String dealerRelationShip = ConvertUtil.isEmpty(requestInfo.get("dealerRelationShip")) ? "" : requestInfo.get("dealerRelationShip").toString();
				store.setDealerRelationShip(dealerRelationShip);
				//是否启用
				Boolean isEnabled = ConvertUtil.isEmpty(requestInfo.get("isEnabled")) ? false : Boolean.parseBoolean(requestInfo.get("isEnabled").toString());
				store.setIsEnabled(isEnabled);
				//价格类型
				if(!ConvertUtil.isEmpty(requestInfo.get("memberRankName"))){
					try {
						filters.clear();
						filters.add(Filter.eq("name", requestInfo.get("memberRankName").toString()));
						filters.add(Filter.eq("companyInfoId", companyInfoId));
						MemberRank memberRank = memberRankBaseService.find(filters);
						if(!ConvertUtil.isEmpty(memberRank)){
							store.setMemberRank(memberRank);
						}else{
							store.setMemberRank(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("价格类型查询有误");
					}
				}else{
					store.setMemberRank(null);
				}
				//核心经销商
				Boolean isCoreStroe = ConvertUtil.isEmpty(requestInfo.get("isCoreStroe")) ? false : Boolean.parseBoolean(requestInfo.get("isCoreStroe").toString());
				store.setIsCoreStroe(isCoreStroe);
				//经销商类别
				if(!ConvertUtil.isEmpty(requestInfo.get("category"))){
					Integer category = Integer.parseInt(requestInfo.get("category").toString());
					store.setCategory(category);
				}else{
					store.setCategory(null);
				}
				//经销商属性
				if(!ConvertUtil.isEmpty(requestInfo.get("agentPropertyName"))){
					try {
						SystemDict agentProperty = roleJurisdictionUtil.getSystemDict(companyInfoId,"agentProperty",
								requestInfo.get("agentPropertyName").toString());
						if(!ConvertUtil.isEmpty(agentProperty)){
							store.setAgentProperty(agentProperty);
						}else{
							store.setAgentProperty(null);
						}
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("经销商属性查询有误");
					}
				}else{
					store.setAgentProperty(null);
				}
				//经销商地址
				String headAddress = ConvertUtil.isEmpty(requestInfo.get("headAddress")) ? "" : requestInfo.get("headAddress").toString();
				store.setHeadAddress(headAddress);
				//门店地址
				String shopAddress = ConvertUtil.isEmpty(requestInfo.get("shopAddress")) ? "" : requestInfo.get("shopAddress").toString();
				store.setShopAddress(shopAddress);
				/**其他信息*/
				//是否加盟成功
				Boolean unJoinSucessFlag = ConvertUtil.isEmpty(requestInfo.get("unJoinSucessFlag")) ? false : Boolean.parseBoolean(requestInfo.get("unJoinSucessFlag").toString());
				store.setUnJoinSucessFlag(unJoinSucessFlag);
				//是否解约时是否资料齐全
				Boolean cancelInfoFlag = ConvertUtil.isEmpty(requestInfo.get("cancelInfoFlag")) ? false : Boolean.parseBoolean(requestInfo.get("cancelInfoFlag").toString());
				store.setCancelInfoFlag(cancelInfoFlag);
				//应缴品牌保证金
				if(!ConvertUtil.isEmpty(requestInfo.get("needCautionPaid"))){
					try {
						store.setNeedCautionPaid(new BigDecimal(requestInfo.get("needCautionPaid").toString()));
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("应缴品牌保证金格式有误");
					}
				}else{
					store.setNeedCautionPaid(BigDecimal.ZERO);
				}
				//是否现金客户
				Boolean cashClientFlag = ConvertUtil.isEmpty(requestInfo.get("cashClientFlag")) ? false : Boolean.parseBoolean(requestInfo.get("cashClientFlag").toString());
				store.setCashClientFlag(cashClientFlag);
				//加盟日期
				if(!ConvertUtil.isEmpty(requestInfo.get("activeDate"))){
					try {
						String activeDate = requestInfo.get("activeDate").toString();
						store.setActiveDate(df.parse(activeDate));
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("加盟日期格式有误");
					}
				}else{
					store.setActiveDate(null);
				}	
				//解约日期
				if(!ConvertUtil.isEmpty(requestInfo.get("cancelDate"))){
					try {
						String cancelDate = requestInfo.get("cancelDate").toString();
						store.setCancelDate(df.parse(cancelDate));
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("解约日期格式有误");
					}
				}else{
					store.setCancelDate(null);
				}	
				//实缴品牌保证金
				if(!ConvertUtil.isEmpty(requestInfo.get("realCautionPaid"))){
					try {
						store.setRealCautionPaid(new BigDecimal(requestInfo.get("realCautionPaid").toString()));
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("实缴品牌保证金格式有误");
					}
				}else{
					store.setRealCautionPaid(BigDecimal.ZERO);
				}
				//货币
				String currencyCode = ConvertUtil.isEmpty(requestInfo.get("currencyCode")) ? "" : requestInfo.get("currencyCode").toString();
				store.setCurrencyCode(currencyCode);
				//加盟档案编号
				String joinFileNumber = ConvertUtil.isEmpty(requestInfo.get("joinFileNumber")) ? "" : requestInfo.get("joinFileNumber").toString();
				store.setJoinFileNumber(joinFileNumber);
				//解约档案编号
				String unfileNumber = ConvertUtil.isEmpty(requestInfo.get("unfileNumber")) ? "" : requestInfo.get("unfileNumber").toString();
				store.setUnfileNumber(unfileNumber);
				//欠缴品牌保证金
				if(!ConvertUtil.isEmpty(requestInfo.get("unpaidCautionPaid"))){
					try {
						store.setUnpaidCautionPaid(new BigDecimal(requestInfo.get("unpaidCautionPaid").toString()));
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("欠缴品牌保证金格式有误");
					}
				}else{
					store.setUnpaidCautionPaid(BigDecimal.ZERO);
				}
				//经销商类型
				if(!ConvertUtil.isEmpty(requestInfo.get("distributorType"))){
					Integer distributorType = Integer.parseInt(requestInfo.get("distributorType").toString());
					store.setDistributorType(distributorType);
				}else{
					store.setDistributorType(null);
				}
				//解约原因
				String cancelReason = ConvertUtil.isEmpty(requestInfo.get("cancelReason")) ? "" : requestInfo.get("cancelReason").toString();
				store.setCancelReason(cancelReason);
				//销量保证金
				if(!ConvertUtil.isEmpty(requestInfo.get("salesDeposit"))){
					try {
						store.setSalesDeposit(new BigDecimal(requestInfo.get("salesDeposit").toString()));
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("销量保证金格式有误");
					}
				}else{
					store.setSalesDeposit(BigDecimal.ZERO);
				}
				//销售品类
				String salesCategory = ConvertUtil.isEmpty(requestInfo.get("salesCategory")) ? "" : requestInfo.get("salesCategory").toString();
				store.setSalesCategory(salesCategory);
				//经销商子类型
				if(!ConvertUtil.isEmpty(requestInfo.get("subType"))){
					Integer subType = Integer.parseInt(requestInfo.get("subType").toString());
					store.setSubType(subType);
				}else{
					store.setSubType(null);
				}
				//税率
				if(!ConvertUtil.isEmpty(requestInfo.get("taxRate"))){
					try {
						store.setTaxRate(new BigDecimal(requestInfo.get("taxRate").toString()));
					}catch (Exception e) {
						ExceptionUtil.throwServiceException("税率格式有误");
					}
				}else{
					store.setTaxRate(BigDecimal.ZERO);
				}
				//缴纳情况/异常说明
				String paymentStatus = ConvertUtil.isEmpty(requestInfo.get("paymentStatus")) ? "" : requestInfo.get("paymentStatus").toString();
				store.setPaymentStatus(paymentStatus);
				//备注（门店地址）
				String description = ConvertUtil.isEmpty(requestInfo.get("description")) ? "" : requestInfo.get("description").toString();
				store.setDescription(description);
				//客户类型 
				String type = ConvertUtil.isEmpty(requestInfo.get("type")) ? "distributor" : requestInfo.get("type").toString();
				if(!ConvertUtil.isEmpty(type)){
					if(ConvertUtil.isEmpty(Enum.valueOf(Type.class, type))){
						store.setType(Enum.valueOf(Type.class, type));
					}else{
						store.setType(Enum.valueOf(Type.class, "distributor"));
					}
				}else{
					store.setType(Enum.valueOf(Type.class, "distributor"));
				}
				//是否对应企业
				Boolean isMainStore = ConvertUtil.isEmpty(requestInfo.get("isMainStore")) ? false : Boolean.parseBoolean(requestInfo.get("isMainStore").toString());
				store.setIsMainStore(isMainStore);
				//四期ID
				Long linkFourId = Long.parseLong(ConvertUtil.isEmpty(requestInfo.get("linkFourId")) ? new Long(0).toString() : requestInfo.get("linkFourId").toString());
				store.setLinkFourId(linkFourId);
				//是否推送五期
				Boolean isToLink5 =  (Boolean) (ConvertUtil.isEmpty(requestInfo.get("isToLink5")) ? false : requestInfo.get("isToLink5"));
				
				Long linkFiveId = Long.parseLong(ConvertUtil.isEmpty(requestInfo.get("linkFiveId")) ? new Long(0).toString() : requestInfo.get("linkFiveId").toString());
				
				store.setLinkFiveId(linkFiveId);
				store.setIsToLink5(isToLink5);
				
				if(!ConvertUtil.isEmpty(requestInfo.get("storeLink5Id"))){
					filters.clear();
					filters.add(Filter.eq("isEnabled", Boolean.TRUE));
					filters.add(Filter.eq("outTradeNo",requestInfo.get("outTradeNoStoreLink5").toString()));
					filters.add(Filter.eq("companyInfoId",requestInfo.get("companyInfoIdStoreLink5").toString()));
					List<Store> storeLink5List = storeBaseService.findList(null, filters, null);
					if(storeLink5List == null || storeLink5List.size()<1) {
						ExceptionUtil.throwServiceException(
								new StringBuffer("AM中outTradeNo为")
								.append(requestInfo.get("outTradeNoStoreLink5"))
								.append(",companyInfoId为")
								.append(requestInfo.get("companyInfoIdStoreLink5"))
								.append("的LINK5归属经销商不存在").toString());
					}
					store.setStoreLink5(storeLink5List.get(0));
				}else {
					//清空LINK5归属经销商信息
					store.setStoreLink5(null);
				}
				
				if(ConvertUtil.isEmpty(store.getId())){
					store.setOutTradeNo(outTradeNo);
					store.setCompanyInfoId(companyInfo.getId());
					save(store);
				}else{
					update(store);
				}
			}
			return "success";
		} catch (Exception e) {
			e.printStackTrace();
			return e.getMessage();
		}
	}

}
