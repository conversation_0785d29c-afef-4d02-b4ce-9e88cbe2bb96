package net.shopxx.member.service.impl;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ServletContextAware;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.service.TotalDateService;
import net.shopxx.member.dao.AdjustmentDao;
import net.shopxx.member.entity.Adjustment;
import net.shopxx.member.entity.AdjustmentAttach;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.CustomerRecharge;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.AdjustmentService;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.CustomerRechargeService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.util.SnUtil;
@Service("adjustmentServiceImpl")
public class AdjustmentServiceImpl extends ActWfBillServiceImpl<Adjustment> implements AdjustmentService , ServletContextAware{
	
	@Resource(name = "adjustmentDao")
	private AdjustmentDao adjustmentDao;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardService;
	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "totalDateServiceImpl")
	private TotalDateService totalDateService;
	@Resource(name = "customerRechargeServiceImpl")
	private CustomerRechargeService customerRechargeService;
	
	/** servletContext */
	private ServletContext servletContext;

	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}
	
	@Override
	public Page<Map<String, Object>> findPage(String sn, Long[] states, Long[] wfStates, String sourceSn,
			Long[] sourceStoreId, Long[] adjustmentStoreId, Long[] sourceSbuId, Long[] adjustmentSbuId,
			Long[] sourceOrganizationId, Long[] adjustmentOrganizationId, String adjustmentSn, Long[] sourceSaleOrgId,
			Long[] adjustmentSaleOrgId, Long[] ids, Pageable pageable) {
		
		return adjustmentDao.findPage(sn, states, wfStates, sourceSn, sourceStoreId, adjustmentStoreId, 
				sourceSbuId, adjustmentSbuId, sourceOrganizationId, adjustmentOrganizationId,
				adjustmentSn, sourceSaleOrgId, adjustmentSaleOrgId, ids, pageable);
	}

	@Transactional
	@Override
	public void saveAdjustment(Adjustment adjustment,
			Long sourceStoreId,BigDecimal adjustmentAmount,Long sourceSbuId,
			Long sourceBankCardId,Long sourceOrganizationId,Long adjustmentStoreId,
			BigDecimal adjustmentActualAmount,Long adjustmentSbuId,Long adjustmentBankCardId,
			Long sourceSaleOrgId,Long adjustmentSaleOrgId,Long adjustmentOrganizationId) {
		
		//客户余额充值类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.eq("value", "客户充值"));
		filters.add(Filter.isNotNull("parent"));
		SystemDict rechargeTypes = systemDictService.find(filters);
		
		//来源
		DepositRecharge sourceDepositRecharge = new DepositRecharge();
		sourceDepositRecharge.setSn(SnUtil.getDepositRechargeSn());
		sourceDepositRecharge.setDocStatus(0);
		sourceDepositRecharge.setType(1);
		sourceDepositRecharge.setRechargeType(rechargeTypes);
		//客户
		Store sourceStore = storeService.find(sourceStoreId);
		sourceDepositRecharge.setStore(sourceStore);
		//SBU
		Sbu sourceSbu = sbuService.find(sourceSbuId);
		sourceDepositRecharge.setSbu(sourceSbu);
		//账号
		BankCard sourceBankCard = bankCardService.find(sourceBankCardId);
		sourceDepositRecharge.setBankCard(sourceBankCard);
		//经营组织
		Organization sourceOrganization = organizationService.find(sourceOrganizationId);
		sourceDepositRecharge.setOrganization(sourceOrganization);
		//机构
		SaleOrg sourceSaleOrg = saleOrgService.find(sourceSaleOrgId);
		sourceDepositRecharge.setSaleOrg(sourceSaleOrg);
		//申请调款金额、实际调款金额
		if(!ConvertUtil.isEmpty(adjustmentAmount)){
			sourceDepositRecharge.setAmount(adjustmentAmount);
			sourceDepositRecharge.setActualAmount(adjustmentAmount);
		}else{
			sourceDepositRecharge.setAmount(new BigDecimal(0));
			sourceDepositRecharge.setActualAmount(new BigDecimal(0));
		}
		//GL日期
		sourceDepositRecharge.setGlDate(adjustment.getGlDate());
		//来源总账日期校验
		this.isCheckTotalDate(sourceDepositRecharge);
		depositRechargeService.save(sourceDepositRecharge);
		adjustment.setSourceDepositRecharge(sourceDepositRecharge);
		
		//调款
		DepositRecharge adjustmentDepositRecharge = new DepositRecharge();
		adjustmentDepositRecharge.setSn(SnUtil.getDepositRechargeSn());
		adjustmentDepositRecharge.setDocStatus(0);
		adjustmentDepositRecharge.setType(1);
		adjustmentDepositRecharge.setRechargeType(rechargeTypes);
		//客户
		Store adjustmentStore = storeService.find(adjustmentStoreId);
		adjustmentDepositRecharge.setStore(adjustmentStore);
		//SBU
		Sbu adjustmentSbu = sbuService.find(adjustmentSbuId);
		adjustmentDepositRecharge.setSbu(adjustmentSbu);
		//账号
		BankCard adjustmentBankCard = bankCardService.find(adjustmentBankCardId);
		adjustmentDepositRecharge.setBankCard(adjustmentBankCard);
		//经营组织
		Organization adjustmentOrganization = organizationService.find(adjustmentOrganizationId);
		adjustmentDepositRecharge.setOrganization(adjustmentOrganization);
		//机构
		SaleOrg adjustmentSaleOrg = saleOrgService.find(adjustmentSaleOrgId);
		adjustmentDepositRecharge.setSaleOrg(adjustmentSaleOrg);
		//申请调款金额、实际调款金额
		if(!ConvertUtil.isEmpty(adjustmentActualAmount)){
			adjustmentDepositRecharge.setAmount(adjustmentActualAmount);
			adjustmentDepositRecharge.setActualAmount(adjustmentActualAmount);
		}else{
			adjustmentDepositRecharge.setAmount(new BigDecimal(0));
			adjustmentDepositRecharge.setActualAmount(new BigDecimal(0));
		}
		//GL日期
		adjustmentDepositRecharge.setGlDate(adjustment.getGlDate());
		//调账总账日期校验
		this.isCheckTotalDate(adjustmentDepositRecharge);
		depositRechargeService.save(adjustmentDepositRecharge);
		adjustment.setAdjustmentDepositRecharge(adjustmentDepositRecharge);
		adjustment.setSn(SnUtil.getAdjustmentSn());
		adjustment.setStatus(0);
		
		// 附件
		List<AdjustmentAttach> adjustmentAttachs = adjustment.getAdjustmentAttachs();
		for (Iterator<AdjustmentAttach> iterator = adjustmentAttachs.iterator(); iterator.hasNext();) {
			AdjustmentAttach adjustmentAttach = iterator.next();
			if (adjustmentAttach == null || adjustmentAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (adjustmentAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			adjustmentAttach.setFileName(adjustmentAttach.getName() + "." + adjustmentAttach.getSuffix());
			adjustmentAttach.setAdjustment(adjustment);
			adjustmentAttach.setStoreMember(storeMemberService.getCurrent());
		}
		save(adjustment);
		orderFullLinkService.addFullLink(5, null, adjustment.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "系统调账" }), null);
	}

	@Override
	public List<Map<String, Object>> findListByAdjustmentId(Long id) {
		return adjustmentDao.findListByAdjustmentId(id);
	}

	
	@Transactional
	@Override
	public void updateAdjustment(Adjustment adjustment, Long sourceStoreId, BigDecimal adjustmentAmount,
			Long sourceSbuId, Long sourceBankCardId, Long sourceOrganizationId, Long adjustmentStoreId,
			BigDecimal adjustmentActualAmount, Long adjustmentSbuId, Long adjustmentBankCardId, Long sourceSaleOrgId,
			Long adjustmentSaleOrgId, Long adjustmentOrganizationId) {
		
		Adjustment at = find(adjustment.getId());
		if(ConvertUtil.isEmpty(at)){
			ExceptionUtil.throwServiceException("该调账单不存在");
		}
		if(at.getStatus() != 0){
			ExceptionUtil.throwServiceException("只有单据状态为已保存的调账单才能保存");
		}
		at.setMemo(adjustment.getMemo());
		at.setGlDate(adjustment.getGlDate());
		//来源
		DepositRecharge sourceDepositRecharge = null;
		if(!ConvertUtil.isEmpty(at.getSourceDepositRecharge())&&
				!ConvertUtil.isEmpty(at.getSourceDepositRecharge().getId())){
			sourceDepositRecharge = depositRechargeService.find(at.getSourceDepositRecharge().getId());
		}else{
			ExceptionUtil.throwServiceException("来源单据不能在");
		}
		if(sourceDepositRecharge.getDocStatus() != 0){
			ExceptionUtil.throwServiceException("来源单据状态不是已保存，禁止保存操作");
		}
		//客户
		Store sourceStore = storeService.find(sourceStoreId);
		sourceDepositRecharge.setStore(sourceStore);
		//SBU
		Sbu sourceSbu = sbuService.find(sourceSbuId);
		sourceDepositRecharge.setSbu(sourceSbu);
		//账号
		BankCard sourceBankCard = bankCardService.find(sourceBankCardId);
		sourceDepositRecharge.setBankCard(sourceBankCard);
		//经营组织
		Organization sourceOrganization = organizationService.find(sourceOrganizationId);
		sourceDepositRecharge.setOrganization(sourceOrganization);
		//机构
		SaleOrg sourceSaleOrg = saleOrgService.find(sourceSaleOrgId);
		sourceDepositRecharge.setSaleOrg(sourceSaleOrg);
		//申请调款金额、实际调款金额
		if(!ConvertUtil.isEmpty(adjustmentAmount)){
			sourceDepositRecharge.setAmount(adjustmentAmount);
			sourceDepositRecharge.setActualAmount(adjustmentAmount);
		}else{
			sourceDepositRecharge.setAmount(new BigDecimal(0));
			sourceDepositRecharge.setActualAmount(new BigDecimal(0));
		}
		//GL日期
		sourceDepositRecharge.setGlDate(at.getGlDate());
		//来源总账日期校验
		this.isCheckTotalDate(sourceDepositRecharge);
		depositRechargeService.update(sourceDepositRecharge);
		
		//调款
		DepositRecharge adjustmentDepositRecharge = null;
		if(!ConvertUtil.isEmpty(at.getAdjustmentDepositRecharge())&&
				!ConvertUtil.isEmpty(at.getAdjustmentDepositRecharge().getId())){
			adjustmentDepositRecharge = depositRechargeService.find(at.getAdjustmentDepositRecharge().getId());
		}else{
			ExceptionUtil.throwServiceException("调款单据不存在");
		}
		if(adjustmentDepositRecharge.getDocStatus() != 0){
			ExceptionUtil.throwServiceException("调款单据状态不是已保存，禁止保存操作");
		}
		//客户
		Store adjustmentStore = storeService.find(adjustmentStoreId);
		adjustmentDepositRecharge.setStore(adjustmentStore);
		//SBU
		Sbu adjustmentSbu = sbuService.find(adjustmentSbuId);
		adjustmentDepositRecharge.setSbu(adjustmentSbu);
		//账号
		BankCard adjustmentBankCard = bankCardService.find(adjustmentBankCardId);
		adjustmentDepositRecharge.setBankCard(adjustmentBankCard);
		//经营组织
		Organization adjustmentOrganization = organizationService.find(adjustmentOrganizationId);
		adjustmentDepositRecharge.setOrganization(adjustmentOrganization);
		//机构
		SaleOrg adjustmentSaleOrg = saleOrgService.find(adjustmentSaleOrgId);
		adjustmentDepositRecharge.setSaleOrg(adjustmentSaleOrg);
		//申请调款金额、实际调款金额
		if(!ConvertUtil.isEmpty(adjustmentActualAmount)){
			adjustmentDepositRecharge.setAmount(adjustmentActualAmount);
			adjustmentDepositRecharge.setActualAmount(adjustmentActualAmount);
		}else{
			adjustmentDepositRecharge.setAmount(new BigDecimal(0));
			adjustmentDepositRecharge.setActualAmount(new BigDecimal(0));
		}
		//GL日期
		adjustmentDepositRecharge.setGlDate(at.getGlDate());
		//来源总账日期校验
		this.isCheckTotalDate(adjustmentDepositRecharge);
		depositRechargeService.update(adjustmentDepositRecharge);
		
		// 附件
		List<AdjustmentAttach> adjustmentAttachs = adjustment.getAdjustmentAttachs();
		for (Iterator<AdjustmentAttach> iterator = adjustmentAttachs.iterator(); iterator.hasNext();) {
			AdjustmentAttach adjustmentAttach = iterator.next();
			if (adjustmentAttach == null || adjustmentAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (adjustmentAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			adjustmentAttach.setFileName(adjustmentAttach.getName() + "." + adjustmentAttach.getSuffix());
			adjustmentAttach.setAdjustment(adjustment);
			adjustmentAttach.setStoreMember(storeMemberService.getCurrent());
		}
		at.getAdjustmentAttachs().clear();
		at.getAdjustmentAttachs().addAll(adjustmentAttachs);
		
		update(at);
		
		orderFullLinkService.addFullLink(5,null,at.getSn(),"修改系统调账",null);
		
	}
	
	@Transactional
	@Override
	public void cancel(Adjustment adjustment) {
		adjustment.setStatus(3);
		//来源
		DepositRecharge sourceDepositRecharge = adjustment.getSourceDepositRecharge();
		if(ConvertUtil.isEmpty(sourceDepositRecharge)){
			ExceptionUtil.throwServiceException("该来源单据不存在");
		}
		if(sourceDepositRecharge.getDocStatus() != 0){
			ExceptionUtil.throwServiceException("来源单据状态不是已保存，禁止作废操作");
		}
		sourceDepositRecharge.setDocStatus(3);
		depositRechargeService.update(sourceDepositRecharge);
		
		
		//调款
		DepositRecharge adjustmentDepositRecharge = adjustment.getAdjustmentDepositRecharge();
		if(ConvertUtil.isEmpty(adjustmentDepositRecharge)){
			ExceptionUtil.throwServiceException("该调款单据不存在");
		}
		if(adjustmentDepositRecharge.getDocStatus() != 0){
			ExceptionUtil.throwServiceException("调款单据状态不是已保存，禁止作废操作");
		}
		adjustmentDepositRecharge.setDocStatus(3);
		depositRechargeService.update(adjustmentDepositRecharge);
		
		update(adjustment);
		
		orderFullLinkService.addFullLink(5,null,adjustment.getSn(),"作废本次系统调账",null);
		
	}

	@Override
	@Transactional
	public void createWf(Adjustment adjustment, String modelId, Long objTypeId) {
	
		//审核中
		adjustment.setStatus(1);
		//来源充值单
		DepositRecharge sourceDepositRecharge = adjustment.getSourceDepositRecharge();
		if(ConvertUtil.isEmpty(sourceDepositRecharge)){
			ExceptionUtil.throwServiceException("该来源单据不存在");
		}
		if(sourceDepositRecharge.getDocStatus() != 0){
			ExceptionUtil.throwServiceException("来源单据状态不是已保存，禁止流程审批操作");
		}
		//已提交  
		sourceDepositRecharge.setDocStatus(1);
		sourceDepositRecharge.setWfState(1);
		//来源总账日期校验
		this.isCheckTotalDate(sourceDepositRecharge);
		depositRechargeService.update(sourceDepositRecharge);
		
		//调款充值单
		DepositRecharge adjustmentDepositRecharge = adjustment.getAdjustmentDepositRecharge();
		if(ConvertUtil.isEmpty(adjustmentDepositRecharge)){
			ExceptionUtil.throwServiceException("该调款单据不存在");
		}
		if(adjustmentDepositRecharge.getDocStatus() != 0){
			ExceptionUtil.throwServiceException("调款单据状态不是已保存，禁止流程审批操作");
		}
		//已提交  
		adjustmentDepositRecharge.setDocStatus(1);
		adjustmentDepositRecharge.setWfState(1);
		//来源总账日期校验
		this.isCheckTotalDate(adjustmentDepositRecharge);
		depositRechargeService.update(adjustmentDepositRecharge);
		StoreMember storeMember = storeMemberBaseService.getCurrent();

		createWf(adjustment.getSn(), String.valueOf(storeMember.getId()),
				new Long[]{},null,modelId,objTypeId,adjustment.getId(),
				WebUtils.getCurrentCompanyInfoId());
		update(adjustment);
		
		 orderFullLinkService.addFullLink(5, null, adjustment.getSn(),
	                ConvertUtil.convertI18nMsg("18701", new Object[]{"系统调账"}), null);
		
	}
	
	
	/**
	 * 流程结束回调
	 */
	//原agreeBack()方法
	@Override
	@Transactional
	public void endBack(ActWf wf) {
		super.endBack(wf);
		Adjustment adjustment = find(wf.getObjId());
		if(ConvertUtil.isEmpty(adjustment)){
			ExceptionUtil.throwServiceException("该调账单不存在");
		}
		if(adjustment.getStatus() != 1){
			ExceptionUtil.throwServiceException("只有单据状态为审核中的调账单才能通过");
		}
		//已审核
		adjustment.setStatus(2); 
		//来源充值单
		DepositRecharge sourceDepositRecharge = adjustment.getSourceDepositRecharge();
		if(ConvertUtil.isEmpty(sourceDepositRecharge)){
			ExceptionUtil.throwServiceException("该来源单据不存在");
		}
		if(sourceDepositRecharge.getDocStatus() != 1){
			ExceptionUtil.throwServiceException("来源单据状态不是审核中，禁止通过操作");
		}
		//来源总账日期校验
		this.isCheckTotalDate(sourceDepositRecharge);
		//来源客户金额回填
		this.isCheckCustomerRecharge(sourceDepositRecharge);
		//已审核
		sourceDepositRecharge.setDocStatus(2);
		sourceDepositRecharge.setWfState(2);
		depositRechargeService.update(sourceDepositRecharge);
		
		//调款充值单
		DepositRecharge adjustmentDepositRecharge = adjustment.getAdjustmentDepositRecharge();
		if(ConvertUtil.isEmpty(adjustmentDepositRecharge)){
			ExceptionUtil.throwServiceException("该调款单据不存在");
		}
		if(adjustmentDepositRecharge.getDocStatus() != 1){
			ExceptionUtil.throwServiceException("调款单据状态不是审核中，禁止通过操作");
		}
		//调款总账日期校验
		this.isCheckTotalDate(adjustmentDepositRecharge);
		//调款客户金额回填
		this.isCheckCustomerRecharge(adjustmentDepositRecharge);
		//已审核
		adjustmentDepositRecharge.setDocStatus(2);
		adjustmentDepositRecharge.setWfState(2);
		depositRechargeService.update(adjustmentDepositRecharge);
		
		update(adjustment);
		orderFullLinkService.addFullLink(15,null,adjustment.getSn(),
                ConvertUtil.convertI18nMsg("18702", new Object[]{"系统调账"}), null);
	}
	
	/**
	 * 中断流程回调
	 */
	//原interruptBack()方法
	@Override
	@Transactional
	public void interruptBack(ActWf wf) {
		Adjustment adjustment = find(wf.getObjId());
		if(ConvertUtil.isEmpty(adjustment)){
			ExceptionUtil.throwServiceException("该调账单不存在");
		}
		if(adjustment.getStatus() != 1){
			ExceptionUtil.throwServiceException("只有单据状态为审核中的调账单才能中断");
		}
		//已保存
		adjustment.setStatus(0);
		
		//来源充值单
		DepositRecharge sourceDepositRecharge = adjustment.getSourceDepositRecharge();
		if(ConvertUtil.isEmpty(sourceDepositRecharge)){
			ExceptionUtil.throwServiceException("该来源单据不存在");
		}
		if(sourceDepositRecharge.getDocStatus() != 1){
			ExceptionUtil.throwServiceException("来源单据状态不是审核中，禁止中断操作");
		}
		//已保存
		sourceDepositRecharge.setDocStatus(0);
		sourceDepositRecharge.setWfState(0);
		depositRechargeService.update(sourceDepositRecharge);
		
		
		//调款充值单
		DepositRecharge adjustmentDepositRecharge = adjustment.getAdjustmentDepositRecharge();
		if(ConvertUtil.isEmpty(adjustmentDepositRecharge)){
			ExceptionUtil.throwServiceException("该调款单据不存在");
		}
		if(adjustmentDepositRecharge.getDocStatus() != 1){
			ExceptionUtil.throwServiceException("调款单据状态不是审核中，禁止中断操作");
		}
		//已保存
		adjustmentDepositRecharge.setDocStatus(0);
		adjustmentDepositRecharge.setWfState(0);
		depositRechargeService.update(adjustmentDepositRecharge);
		
		update(adjustment);
		
		orderFullLinkService.addFullLink(15,null,adjustment.getSn(),
	                ConvertUtil.convertI18nMsg("18704"),null);
	}
	
	
	/**
	 * 驳回流程回调
	 */
	@Override
	@Transactional
	public void rejectBack(ActWf wf) {
		Adjustment adjustment = find(wf.getObjId());
		if(ConvertUtil.isEmpty(adjustment)){
			ExceptionUtil.throwServiceException("该调账单不存在");
		}
		if(adjustment.getStatus() != 1){
			ExceptionUtil.throwServiceException("只有单据状态为审核中的调账单才能驳回");
		}
		adjustment.setWfState(3);
		//来源充值单
		DepositRecharge sourceDepositRecharge = adjustment.getSourceDepositRecharge();
		if(ConvertUtil.isEmpty(sourceDepositRecharge)){
			ExceptionUtil.throwServiceException("该来源单据不存在");
		}
		if(sourceDepositRecharge.getDocStatus() != 1){
			ExceptionUtil.throwServiceException("来源单据状态不是审核中，禁止驳回操作");
		}
		sourceDepositRecharge.setWfState(adjustment.getWfState());
		depositRechargeService.update(sourceDepositRecharge);
		//调款充值单
		DepositRecharge adjustmentDepositRecharge = adjustment.getAdjustmentDepositRecharge();
		if(ConvertUtil.isEmpty(adjustmentDepositRecharge)){
			ExceptionUtil.throwServiceException("该调款单据不存在");
		}
		if(adjustmentDepositRecharge.getDocStatus() != 1){
			ExceptionUtil.throwServiceException("调款单据状态不是审核中，禁止驳回操作");
		}
		adjustmentDepositRecharge.setWfState(adjustment.getWfState());
		depositRechargeService.update(adjustmentDepositRecharge);
		update(adjustment);
	}
	
	
	/**
	 * 通过流程节点回调
	 */
	@Override
	@Transactional
	public void agreeBack(ActWf wf) {
		Adjustment adjustment = find(wf.getObjId());
		if(adjustment.getWfState() == 3){
			if(ConvertUtil.isEmpty(adjustment)){
				ExceptionUtil.throwServiceException("该调账单不存在");
			}
			if(adjustment.getStatus() != 1){
				ExceptionUtil.throwServiceException("只有单据状态为审核中的调账单才能通过");
			}
			adjustment.setWfState(1);
			//来源充值单
			DepositRecharge sourceDepositRecharge = adjustment.getSourceDepositRecharge();
			if(ConvertUtil.isEmpty(sourceDepositRecharge)){
				ExceptionUtil.throwServiceException("该来源单据不存在");
			}
			if(sourceDepositRecharge.getDocStatus() != 1){
				ExceptionUtil.throwServiceException("来源单据状态不是审核中，禁止通过操作");
			}
			sourceDepositRecharge.setWfState(adjustment.getWfState());
			depositRechargeService.update(sourceDepositRecharge);
			//调款充值单
			DepositRecharge adjustmentDepositRecharge = adjustment.getAdjustmentDepositRecharge();
			if(ConvertUtil.isEmpty(adjustmentDepositRecharge)){
				ExceptionUtil.throwServiceException("该调款单据不存在");
			}
			if(adjustmentDepositRecharge.getDocStatus() != 1){
				ExceptionUtil.throwServiceException("调款单据状态不是审核中，禁止通过操作");
			}
			adjustmentDepositRecharge.setWfState(adjustment.getWfState());
			depositRechargeService.update(adjustmentDepositRecharge);
			update(adjustment);
		}
	}
	
	
	private  void isCheckTotalDate(DepositRecharge depositRecharge){
		//机构
		if(ConvertUtil.isEmpty(depositRecharge.getSaleOrg())){
			throw new RuntimeException("机构不能为空");
		}
		//sbu
		if(ConvertUtil.isEmpty(depositRecharge.getSbu())){
			throw new RuntimeException("sbu不能为空");
		}
		//经营组织
		if(ConvertUtil.isEmpty(depositRecharge.getOrganization())){
			throw new RuntimeException("经营组织不能为空");
		}
		//Gl日期
		if(ConvertUtil.isEmpty(depositRecharge.getGlDate())){
			throw new RuntimeException("Gl日期不能为空");
		}

        SystemDict totalDateType =systemDictService.findSystemDictList("totalDateType", "应收账期").get(0);
		List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, depositRecharge.getSaleOrg().getId(),
				depositRecharge.getSbu().getId(),new Long[]{depositRecharge.getOrganization().getId()}, depositRecharge.getGlDate(),totalDateType);
		if(mapList.isEmpty()||mapList.size() ==0){
			throw new RuntimeException("GL日期不包含在总账日期内，请填写合适的单据日期");
		}
	}

	@Override
	public  List<Map<String, Object>> findAdjustmentList(Long depositRechargeId) {
		return adjustmentDao.findAdjustmentList(depositRechargeId);
	}
	
	
	
	private void isCheckCustomerRecharge(DepositRecharge depositRecharge){
		//客户充值
		CustomerRecharge customerRecharge = new CustomerRecharge();
		//客户
		customerRecharge.setStore(depositRecharge.getStore());
		//经营组织
		customerRecharge.setOrganization(depositRecharge.getOrganization());
		//sbu
		customerRecharge.setSbu(depositRecharge.getSbu());
		customerRecharge = customerRechargeService.saveOrUpdate(customerRecharge);
		if(!ConvertUtil.isEmpty(customerRecharge) && !ConvertUtil.isEmpty(customerRecharge.getId())){
			//充值金额
			customerRecharge.setBalance(
					customerRecharge.getBalance().add(
							depositRecharge.getActualAmount()));
			customerRechargeService.update(customerRecharge);
		}else{
			throw new RuntimeException("流程审批或者审核异常，请管理员进行维护");
		}
	}	
	
	/**
	 * 给流程分支节点赋值
	 */
	public Map<String, Object> setFormData(ActWf wf, String taskId) {
		Map<String,Object> dayMap=new HashMap<String, Object>();
		Adjustment adjustment = find(wf.getObjId());
		//来源
		DepositRecharge sourceDepositRecharge = adjustment.getSourceDepositRecharge();
		dayMap.put("sourceOrganizationId",sourceDepositRecharge.getOrganization().getId());
		dayMap.put("sourceSaleOrgId", sourceDepositRecharge.getSaleOrg().getId());
        dayMap.put("sourceSbuId", sourceDepositRecharge.getSbu().getId());
        dayMap.put("sourceIsInternalAccount", sourceDepositRecharge.getBankCard().getIsInternalAccount());
        dayMap.put("sourceIsTotalAccount", sourceDepositRecharge.getBankCard().getIsTotalAccount());
		//调款
		DepositRecharge adjustmentDepositRecharge = adjustment.getAdjustmentDepositRecharge();
		dayMap.put("adjustmentOrganizationId",adjustmentDepositRecharge.getOrganization().getId());
		dayMap.put("adjustmentSaleOrgId", adjustmentDepositRecharge.getSaleOrg().getId());
        dayMap.put("adjustmentSbuId", adjustmentDepositRecharge.getSbu().getId());
        dayMap.put("adjustmentIsInternalAccount", adjustmentDepositRecharge.getBankCard().getIsInternalAccount());
        dayMap.put("adjustmentIsTotalAccount", adjustmentDepositRecharge.getBankCard().getIsTotalAccount());
		return dayMap;
	}
}
