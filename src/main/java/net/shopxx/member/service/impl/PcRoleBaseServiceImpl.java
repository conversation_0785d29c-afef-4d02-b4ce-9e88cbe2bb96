package net.shopxx.member.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.entity.Principal;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.dao.PcRoleBaseDao;
import net.shopxx.member.entity.PcMenu;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcRoleBaseService;

/**
 * Service - 会员等级
 */
@Service("pcRoleBaseServiceImpl")
public class PcRoleBaseServiceImpl extends BaseServiceImpl<PcRole>
		implements PcRoleBaseService {

	@Resource(name = "pcRoleBaseDao")
	private PcRoleBaseDao pcRoleBaseDao;

	@Override
	@Transactional(readOnly = true)
	public PcRole findAdministrator() {
		return pcRoleBaseDao.findAdministrator();
	}
	
	@Override
	@Transactional(readOnly = true)
	public List<PcRole> findByStoreMemberId(Long StoreMemberId) {

		List<PcRole> roles = new ArrayList<PcRole>();
		roles = pcRoleBaseDao.findRoleList(StoreMemberId);
		
		return roles;
	}
}