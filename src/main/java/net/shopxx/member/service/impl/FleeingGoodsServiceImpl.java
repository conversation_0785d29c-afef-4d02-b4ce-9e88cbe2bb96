package net.shopxx.member.service.impl;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.intf.dao.Oa_WfModelDao;
import net.shopxx.intf.service.OaToWfService;
import net.shopxx.member.dao.FleeingGoodsDao;
import net.shopxx.member.entity.FleeingGoods;
import net.shopxx.member.entity.FleeingGoodsAttach;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.FleeingGoodsService;
import net.shopxx.member.service.StoreMemberBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service("fleeingGoodsServiceImpl")
public class FleeingGoodsServiceImpl extends ActWfBillServiceImpl<FleeingGoods>
        implements FleeingGoodsService {

    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "fleeingGoodsDao")
    private FleeingGoodsDao fleeingGoodsDao;
    @Resource(name = "oaToWfServiceImpl")
    private OaToWfService oaToWfService;
    @Resource(name = "oa_WfModelDao")
    private Oa_WfModelDao oa_WfModelDao;
    
    @Override
    @Transactional
    public void saveFleeingGoods(FleeingGoods fleeingGoods) {
        // 单号
    	fleeingGoods.setSn(Sequence.getInstance().getSequence("CH"));    	
        // 处理附件
        List<FleeingGoodsAttach> bb0Attach = handleAttach(fleeingGoods, fleeingGoods.getBlowBy0Attachs(), 0,1);
        fleeingGoods.setBlowBy0Attachs(bb0Attach);
        List<FleeingGoodsAttach> bb1Attach = handleAttach(fleeingGoods, fleeingGoods.getBlowBy1Attachs(), 1,1);
        fleeingGoods.setBlowBy1Attachs(bb1Attach);
        List<FleeingGoodsAttach> bb2Attach = handleAttach(fleeingGoods, fleeingGoods.getBlowBy2Attachs(), 2,1);
        fleeingGoods.setBlowBy2Attachs(bb2Attach);
        List<FleeingGoodsAttach> bb3Attach = handleAttach(fleeingGoods, fleeingGoods.getBlowBy3Attachs(), 3,1);
        fleeingGoods.setBlowBy3Attachs(bb3Attach);
        
        
        this.save(fleeingGoods);
    }
    
    /**
     * 处理附件
     * 
     * @param fg
     * @param attachs
     * @param type    1、不限制附件数量，2、限制附件数量
     * @return
     */
    public List<FleeingGoodsAttach> handleAttach(FleeingGoods fg, List<FleeingGoodsAttach> attachs, Integer type, Integer count) {
        if (type == 2 && attachs != null && attachs.size() < 1) {
            ExceptionUtil.throwServiceException("附件照片不能少于 4 张");
        }
        Iterator<FleeingGoodsAttach> iterator = attachs.iterator();
        while (iterator.hasNext()) {
            FleeingGoodsAttach attach = iterator.next();
            if (attach == null || attach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (attach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            attach.setFileName(attach.getName() + "." + attach.getSuffix());
            attach.setType(type);
            attach.setFleeingGoods(fg);
            attach.setStoreMember(storeMemberService.getCurrent());
        }
        return attachs;
    }

    @Override
    public Page<Map<String, Object>> findSelectPage(FleeingGoods fg, List<Object> param, Pageable pageable) {
        return fleeingGoodsDao.findSelectPage(fg,param, pageable);
    }

	@Override
	public List<Map<String, Object>> findFleeingGoodsAttach(Long id,Integer type) {
		return fleeingGoodsDao.findFleeingGoodsAttach(id,type);
	}

	@Override
	public void updateFleeingGoods(FleeingGoods fleeingGoods) {
		FleeingGoods f = find(fleeingGoods.getId());
		f.setFleeingGoodsTime(fleeingGoods.getFleeingGoodsTime());
		f.setPunishType(fleeingGoods.getPunishType());
		f.setFleeingGoodsDealers(fleeingGoods.getFleeingGoodsDealers());
		f.setFleeingGoodsNumber(fleeingGoods.getFleeingGoodsNumber());
		f.setFgRegionArea(fleeingGoods.getFgRegionArea());
		f.setFleeingGoodsRegion(fleeingGoods.getFleeingGoodsRegion());
		f.setByFleeingGoodStore(fleeingGoods.getByFleeingGoodStore());
		f.setProduct(fleeingGoods.getProduct());
		f.setFleeingGoodsArea(fleeingGoods.getFleeingGoodsArea());
		f.setCustomerName(fleeingGoods.getCustomerName());
		f.setCustomerPhone(fleeingGoods.getCustomerPhone());
		f.setCustomerInstallAddress(fleeingGoods.getCustomerInstallAddress());
		
        // 处理附件
        List<FleeingGoodsAttach> bb0Attach = handleAttach(fleeingGoods, fleeingGoods.getBlowBy0Attachs(), 0,1);
        //f.setBlowBy0Attachs(bb0Attach);
        f.getBlowBy0Attachs().clear();
        f.getBlowBy0Attachs().addAll(bb0Attach);
        List<FleeingGoodsAttach> bb1Attach = handleAttach(fleeingGoods, fleeingGoods.getBlowBy1Attachs(), 1,1);
        //f.setBlowBy1Attachs(bb1Attach);
        f.getBlowBy1Attachs().clear();
        f.getBlowBy1Attachs().addAll(bb1Attach);
        List<FleeingGoodsAttach> bb2Attach = handleAttach(fleeingGoods, fleeingGoods.getBlowBy2Attachs(), 2,1);
        //f.setBlowBy2Attachs(bb2Attach);
        f.getBlowBy2Attachs().clear();
        f.getBlowBy2Attachs().addAll(bb2Attach);
        List<FleeingGoodsAttach> bb3Attach = handleAttach(fleeingGoods, fleeingGoods.getBlowBy3Attachs(), 3,1);
        //f.setBlowBy3Attachs(bb3Attach);
        f.getBlowBy3Attachs().clear();
        f.getBlowBy3Attachs().addAll(bb3Attach);

        this.update(f);
		
	}

	@Override
	@Transactional
	public void saveform(Integer type, FleeingGoods fleeingGoods) {
		FleeingGoods fg = find(fleeingGoods.getId());
		if(getCurrTaskByWf(getWfByWfId(fg.getWfId()))!=null){
			wfPass(fleeingGoods,getCurrTaskByWf(getWfByWfId(fg.getWfId())).getName());
		}
		if(type == 1){
            fg.setGovernorOpinion(fleeingGoods.getGovernorOpinion());
        }
		if(type == 2){
			fg.setChannelOpinion(fleeingGoods.getChannelOpinion());
            List<FleeingGoodsAttach> f1Attach = handleAttach(fleeingGoods,fleeingGoods.getBlowBy4Attachs(),4,1);
		    fg.getBlowBy4Attachs().clear();
		    fg.getBlowBy4Attachs().addAll(f1Attach);
		    List<FleeingGoodsAttach> d2Attach = handleAttach(fleeingGoods,fleeingGoods.getBlowBy5Attachs(),5,1);
		    fg.getBlowBy5Attachs().clear();
			fg.getBlowBy5Attachs().addAll(d2Attach);			
        }
		if(type == 99){
			fg.setFileNumber(fleeingGoods.getFileNumber());
			fg.setHandleTime(fleeingGoods.getHandleTime());
			fg.setFleeingGoodsType(fleeingGoods.getFleeingGoodsType());
			fg.setLettersSentOutTime(fleeingGoods.getLettersSentOutTime());
			fg.setTrackingNumber(fleeingGoods.getTrackingNumber());
			fg.setConsignee(fleeingGoods.getConsignee());
			fg.setConsigneeType(fleeingGoods.getConsigneeType());
			fg.setConsigneePhone(fleeingGoods.getConsigneePhone());
			fg.setExpressAddress(fleeingGoods.getExpressAddress());
			fg.setProcessingResult(fleeingGoods.getProcessingResult());
		}
		update(fg);
	}
	
	 /**
     * 表单节点校验
     *
     * @param fleeingGoods     实体
     * @param nodeName 节点名称
     */
    public void wfPass(FleeingGoods fleeingGoods, String nodeName) {
    	if(nodeName.contains("省长")){
    		if(fleeingGoods.getGovernorOpinion()==null){
    			ExceptionUtil.throwServiceException("请填写省长意见！");
    		}
    	}
    }

    @Override
    public void createWf(Long id, String modelId, Long objTypeId) {
        FleeingGoods fleeingGoods = find(id);
        fleeingGoods.setStatus(3);
        createWf(fleeingGoods.getSn(),
                String.valueOf(storeMemberService.getCurrent().getId()),
                new Long[]{fleeingGoods.getByFleeingGoodStore().getSaleOrg().getId()},
                fleeingGoods.getByFleeingGoodStore().getId(),
                modelId,
                objTypeId,
                id,
                WebUtils.getCurrentCompanyInfoId(),
                true);
        update(fleeingGoods);
    }

    /** 流程开始回调 */
    @Transactional
    public void startBack(ActWf wf) {
        List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
        for (String receiver : user) {
            oaToWfService.receiveRequestInfoByJson(wf,
                    new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
        }
    };

    /** 通过节点任务回调 发送待办 */
    @Transactional
    public void agreeBack(ActWf wf) {
        if (getCurrTaskByWf(wf) != null) {
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务回调 发送待办 */
    @Transactional
    public void rejectBack(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 通过节点任务前回调 发送待办转以办 */
    @Transactional
    public void agreePre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            // 通过节点校验
            wfPass(find(wf.getObjId()), getCurrTaskByWf(wf).getName());
            // oa触发OA接口推送待办转以办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务前回调 发送待办转以办 */
    @Transactional
    public void rejectPre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 流程中断前回调 1 */
    @Transactional
    public void interruptPre(ActWf wf) {
        oaToWfService.deleteRequestInfoByJson(wf);
    };

    @Override
    @Transactional
    public void interruptBack(ActWf wf) {
        super.interruptBack(wf);
        FleeingGoods fleeingGoods = find(wf.getObjId());
        fleeingGoods.setStatus(0);//保存状态
        update(fleeingGoods);
    }

    
    @Override
    @Transactional
    public void endBack(ActWf wf) {
    	super.endBack(wf);
        oaToWfService.processOverRequestByJson(wf,
                new String[] { "", oa_WfModelDao.findWfStartUser(wf.getProcInstId()) });
    	FleeingGoods fleeingGoods = find(wf.getObjId());
        fleeingGoods.setStatus(1);
        update(fleeingGoods);
    }

    public String findUserName(String id) {
        StoreMember storeMember = storeMemberService.find(Long.parseLong(id));
        if (storeMember == null) {
            return "";
        } else {
            return storeMember.getUsername();
        }
    }

	@Override
	public Integer count(FleeingGoods fg, List<Object> param) {
		return fleeingGoodsDao.count(fg, param);
	}

	@Override
	public List<Map<String, Object>> findList(FleeingGoods fg, List<Object> param, Pageable pageable, Integer page,
                                              Integer size) {
		return fleeingGoodsDao.findList(fg, param, pageable, page, size);
	}

}
