package net.shopxx.member.service.impl;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.intf.dao.Oa_WfModelDao;
import net.shopxx.intf.service.OaToWfService;
import net.shopxx.member.dao.StoreExitDao;
import net.shopxx.member.entity.*;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreExitService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.ShopInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service("storeExitServiceImpl")
public class StoreExitServiceImpl extends ActWfBillServiceImpl<StoreExit>
        implements StoreExitService {
    
    @Resource(name = "storeExitDao")
    private StoreExitDao storeExitDao;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "storeExitServiceImpl")
    private StoreExitService storeExitService;
    @Resource(name = "oaToWfServiceImpl")
    private OaToWfService oaToWfService;
    @Resource(name = "oa_WfModelDao")
    private Oa_WfModelDao oa_WfModelDao;

    @Override
    @Transactional
    public void saveStoreExit(StoreExit storeExit, Long sid) {
        storeExit.setSn(Sequence.getInstance().getSequence(null));
        Store store = storeBaseService.find(sid);
        if (store == null) {
            ExceptionUtil.throwControllerException("经销商有误！");
        }
        // 修改经销状态为无效
        List<Filter> fis = new ArrayList<Filter>();
        fis.add(Filter.eq("code", "distributorStatus"));
        fis.add(Filter.eq("isEnabled", true));
        fis.add(Filter.eq("value", "无效"));
        fis.add(Filter.isNotNull("parent"));
        SystemDict distributor = systemDictBaseService.find(fis);
        if (distributor != null) {
            store.setDistributorStatus(distributor);
            storeBaseService.update(store);
        }
        storeExit.setStore(store);
        
        List<StoreExitAttach> storeExitAttachs = storeExit.getStoreExitAttachs();
        Iterator<StoreExitAttach> storeExitIterator = storeExitAttachs.iterator();
        while (storeExitIterator.hasNext()) {
        	StoreExitAttach se = storeExitIterator.next();
            if (se == null || se.getUrl() == null) {
            	storeExitIterator.remove();
                continue;
            }
            if (se.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            se.setFileName(se.getName() + "." + se.getSuffix());
            se.setStoreExit(storeExit);
            se.setStoreMember(storeMemberService.getCurrent());
        }
        storeExit.setStoreExitAttachs(storeExitAttachs);
        
        List<StoreExitAttach> storeReceiptAttachs = storeExit.getStoreReceiptAttachs();
        Iterator<StoreExitAttach> dealersIterator = storeReceiptAttachs.iterator();
        while (dealersIterator.hasNext()) {
        	StoreExitAttach dealersAttach = dealersIterator.next();
            if (dealersAttach == null || dealersAttach.getUrl() == null) {
                dealersIterator.remove();
                continue;
            }
            if (dealersAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            dealersAttach.setFileName(dealersAttach.getName() + "." + dealersAttach.getSuffix());
            dealersAttach.setStoreExit(storeExit);
            dealersAttach.setStoreMember(storeMemberService.getCurrent());
        }
        storeExit.setStoreReceiptAttachs(storeReceiptAttachs);
        
        //客户退出门店资料关联表
        List<StoreExitShopInfo> seList = storeExit.getSeList();
        for (Iterator<StoreExitShopInfo> iterator = seList.iterator(); iterator.hasNext();) {
        	StoreExitShopInfo se = (StoreExitShopInfo)iterator.next();
        	if (se == null) {
				iterator.remove();
				continue;
			}
        	se.setStoreExit(storeExit);
        }
		storeExit.setSeList(seList);
        this.save(storeExit);
    }

    @Override
    public Page<Map<String, Object>> findPage(StoreExit se, List<Object> param, Pageable pageable) {
        return storeExitDao.findPage(se,param, pageable);
    }

    @Override
    @Transactional
    public void updateStoreExit(StoreExit se, Long sid, Integer[] dealerContacts) {
        Store store = storeBaseService.find(sid);
        StoreExit storeExit = this.find(se.getId());
        Integer status = storeExit.getStatus();
        //BeanUtils.copyProperties(se,storeExit);
        
        // 修改经销状态为无效
        List<Filter> fis = new ArrayList<Filter>();
        fis.add(Filter.eq("code", "distributorStatus"));
        fis.add(Filter.eq("isEnabled", true));
        fis.add(Filter.eq("value", "无效"));
        fis.add(Filter.isNotNull("parent"));
        SystemDict distributor = systemDictBaseService.find(fis);
        if (distributor != null) {
            store.setDistributorStatus(distributor);
            storeBaseService.update(store);
        }
        // 经销商无法联系
        if (dealerContacts != null && dealerContacts.length > 0) {
            String dealerContact = StringUtils.arrayToDelimitedString(dealerContacts, ",");
            se.setDealerContact(dealerContact);
        }
        se.setStore(store);
        se.setStatus(status);
        
        List<StoreExitAttach> storeExitAttachs = se.getStoreExitAttachs();
        Iterator<StoreExitAttach> storeExitIterator = storeExitAttachs.iterator();
        while (storeExitIterator.hasNext()) {
        	StoreExitAttach se1 = storeExitIterator.next();
            if (se1 == null || se1.getUrl() == null) {
            	storeExitIterator.remove();
                continue;
            }
            if (se1.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            se1.setFileName(se1.getName() + "." + se1.getSuffix());
            se1.setStoreExit(se);
            se1.setStoreMember(storeMemberService.getCurrent());
        }
//        storeExit.getStoreExitAttachs().clear();
        se.setStoreExitAttachs(storeExitAttachs);
//        storeExit.setStoreExitAttachs(se.getStoreExitAttachs());
        
        List<StoreExitAttach> storeReceiptAttachs = se.getStoreReceiptAttachs();
        Iterator<StoreExitAttach> dealersIterator = storeReceiptAttachs.iterator();
        while (dealersIterator.hasNext()) {
        	StoreExitAttach dealersAttach = dealersIterator.next();
            if (dealersAttach == null || dealersAttach.getUrl() == null) {
                dealersIterator.remove();
                continue;
            }
            if (dealersAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            dealersAttach.setFileName(dealersAttach.getName() + "." + dealersAttach.getSuffix());
            dealersAttach.setStoreExit(se);
            dealersAttach.setStoreMember(storeMemberService.getCurrent());
        }
//        storeExit.getStoreReceiptAttachs().clear();
        se.setStoreReceiptAttachs(storeReceiptAttachs);
        
 
        //客户退出门店资料关联表
        List<StoreExitShopInfo> seList = se.getSeList();
        for (Iterator<StoreExitShopInfo> iterator = seList.iterator(); iterator.hasNext();) {
        	 StoreExitShopInfo storeExitShopInfo = (StoreExitShopInfo)iterator.next();
        	 if (storeExitShopInfo == null) {
				 iterator.remove();
				 continue;
			 }
        	 storeExitShopInfo.setStoreExit(se);
        }
        se.setSeList(seList);
        
        update(se);
    }

    @Override
    @Transactional
    public void saveform(Integer type, StoreExit se) {
        StoreExit storeExit = find(se.getId());
        String nodeName = getCurrTaskByWf(getWfByWfId(storeExit.getWfId())).getName();
        wfPass(se,nodeName);

        /**
         * type:
         * 1省长
         * 2渠道部
         * 3销售中心副总
         * 4事业部总裁
         */
        if (type == 1) {
            if (se.getGovernorOpinion() != null) {
                storeExit.setGovernorOpinion(se.getGovernorOpinion());
            }
        }

        if (type == 2) {
            storeExit.setIsCoreStore(se.getIsCoreStore());
            storeExit.setChannelOpinion(se.getChannelOpinion());
            /** 解约原因 */
            storeExit.setCancelReason(se.getCancelReason());
        	/** 解约档案编号 */
            storeExit.setUnfileNumber(se.getUnfileNumber());
        	/** 解约日期 */
            storeExit.setCancelDate(se.getCancelDate());
            /** 门店关系 */
            //客户退出门店资料关联表
            List<StoreExitShopInfo> seList = se.getSeList();
            for (Iterator<StoreExitShopInfo> iterator = seList.iterator(); iterator.hasNext();) {
                StoreExitShopInfo storeExitShopInfo = (StoreExitShopInfo)iterator.next();
                if (storeExitShopInfo == null) {
                    iterator.remove();
                    continue;
                }
                storeExitShopInfo.setDecreaseCode(storeExitShopInfo.getDecreaseCode());
                storeExitShopInfo.setOffTime(storeExitShopInfo.getOffTime());
                storeExitShopInfo.setCause(storeExitShopInfo.getCause());
                storeExitShopInfo.setStoreExit(storeExit);
            }
            storeExit.getSeList().clear();
            storeExit.getSeList().addAll(seList);
        }
        update(storeExit);

    }
    
    /**
     * 表单节点校验
     *
     * @param storeExit     实体
     * @param nodeName 节点名称
     */
    public void wfPass(StoreExit storeExit, String nodeName) {
    	if(nodeName.contains("省长")){
    		if(storeExit.getGovernorOpinion()==null){
    			ExceptionUtil.throwServiceException("请填写省长意见！");
    		}
    	}
        if (nodeName.contains("渠道专员")) {
//        	if(storeExit.getCancelReason()==null){
//        		ExceptionUtil.throwServiceException("请填写解约原因！");
//        	}
//        	if(storeExit.getUnfileNumber()==null){
//        		ExceptionUtil.throwServiceException("请填写解约档案编号！");
//        	}
//			if(storeExit.getCancelDate()==null){
//				ExceptionUtil.throwServiceException("请填写解约日期！");
//			}
        }
    }

    /**
     * 创建流程实例
     * @param id
     * @param modelId
     * @param objTypeId
     */
    @Override
    @Transactional
    public void createWf(Long id, String modelId, Long objTypeId) {
        StoreMember storeMember = storeMemberService.getCurrent();
        StoreExit storeExit = storeExitService.find(id);

        storeExitService.createWf(storeExit.getSn(),
                String.valueOf(storeMember.getId()),
                new Long[]{storeExit.getStore().getSaleOrg().getId()},
                storeExit.getStore().getId(),
                modelId,
                objTypeId,
                id,
                WebUtils.getCurrentCompanyInfoId(),
                true);

        storeExit.setStatus(3);//单据状态：进行中
        update(storeExit);
    }


    /**
     * 设置流程分支参数
     */
    @Override
    public Map<String, Object> setFormData(ActWf wf, String taskId) {
        Map<String, Object> dayMap = new HashMap<String, Object>();
        StoreExit storeExit = this.find(wf.getObjId());
        dayMap.put("region",storeExit.getStore().getSaleOrg().getRegion());
        dayMap.put("isCore", storeExit.getStore().getIsCoreStroe());
        return dayMap;
    }

    @Override
    @Transactional
    public void startBack(ActWf wf) {
        super.startBack(wf);
        List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
        for (String receiver : user) {
            oaToWfService.receiveRequestInfoByJson(wf,
                    new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
        }
        StoreExit storeExit = find(wf.getObjId());
        /** 状态 0已保存 1已提交 2已终止 3进行中 4已完成 */
        storeExit.setStatus(3);
        update(storeExit);

    }

    /** 通过节点任务回调 发送待办 */
    @Transactional
    public void agreeBack(ActWf wf) {
        if (getCurrTaskByWf(wf) != null) {
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务回调 发送待办 */
    @Transactional
    public void rejectBack(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务前回调 发送待办转以办 */
    @Transactional
    public void rejectPre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };


    /**
     * 中断流程回调
     */
    @Override
    @Transactional
    public void interruptBack(ActWf wf) {
        super.interruptBack(wf);
        StoreExit storeExit = find(wf.getObjId());
        /** 状态 0已保存 1已提交 2已终止 3进行中 4已完成 */
        storeExit.setStatus(0);
        update(storeExit);
    }

    /** 流程中断前回调 1 */
    @Transactional
    public void interruptPre(ActWf wf) {
        oaToWfService.deleteRequestInfoByJson(wf);
    };

    /**
     * 流程结束回调
     */
    @Override
    @Transactional
    public void endBack(ActWf wf) {
        super.endBack(wf);
        oaToWfService.processOverRequestByJson(wf,
                new String[] { "", oa_WfModelDao.findWfStartUser(wf.getProcInstId()) });
        StoreExit storeExit = find(wf.getObjId());
        /** 状态 0已保存 1已提交 2已终止 3进行中 4已完成 */
        storeExit.setStatus(4);
        update(storeExit);
        storeBaseService.storeExit(storeExit.getStore(),storeExit,wf);
    }
    
    @Override
    @Transactional
    public void agreePre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            //通过节点校验
            wfPass(find(wf.getObjId()), getCurrTaskByWf(wf).getName());
            // oa触发OA接口推送待办转以办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    }

    public String findUserName(String id) {
        StoreMember storeMember = storeMemberService.find(Long.parseLong(id));
        if (storeMember == null) {
            return "";
        } else {
            return storeMember.getUsername();
        }
    }

	@Override
	public List<Map<String, Object>> findStoreExitAttach(Long id, Integer type) {
		return storeExitDao.findStoreExitAttach(id, type);
	}

	@Override
	public List<Map<String, Object>> findStoreExitShopInfo(Long id){
		return storeExitDao.findStoreExitShopInfo(id);
	}
	
	@Override
	public List<ShopInfo> findAllCloseShopList(StoreExit storeExit){
		List<ShopInfo> a = new ArrayList<ShopInfo>();
		List<StoreExitShopInfo> seList = storeExit.getSeList();
		if(seList == null){
			return null;
		}
		for (Iterator<StoreExitShopInfo> iterator = seList.iterator(); iterator.hasNext();) {
			StoreExitShopInfo storeExitShopInfo = (StoreExitShopInfo)iterator.next();
			if (storeExitShopInfo.getIsClose()!=null&&storeExitShopInfo.getIsClose()) {
				ShopInfo shopinfo = storeExitShopInfo.getShopInfo();
				shopinfo.setClosingTime(storeExitShopInfo.getOffTime());
				shopinfo.setShutDownMenu(storeExitShopInfo.getCause());
				shopinfo.setDecreaseArchivesCode(storeExitShopInfo.getDecreaseCode());
				a.add(shopinfo);
			}
        }
		return a;
	}
	
	@Override
	public List<ShopInfo> findAllExitShopList(StoreExit storeExit){
		List<ShopInfo> a = new ArrayList<ShopInfo>();
		List<StoreExitShopInfo> seList = storeExit.getSeList();
		if(seList == null){
			return null;
		}
		for (Iterator<StoreExitShopInfo> iterator = seList.iterator(); iterator.hasNext();) {
			StoreExitShopInfo storeExitShopInfo = (StoreExitShopInfo)iterator.next();
			if(storeExitShopInfo.getIsExit()!=null&&storeExitShopInfo.getIsExit()){
                ShopInfo shopinfo = storeExitShopInfo.getShopInfo();
                shopinfo.setReorganizeDate(storeExitShopInfo.getOffTime());
                shopinfo.setReorganizeReason(storeExitShopInfo.getCause());
				a.add(shopinfo);
			}
        }
		return a;
	}

	@Override
	public Integer count(StoreExit se, List<Object> param) {
		return storeExitDao.count(se, param);
	}

	@Override
	public List<Map<String, Object>> findList(StoreExit se, List<Object> param, Pageable pageable, Integer page,
                                              Integer size) {
		return storeExitDao.findList(se, param, pageable, page, size);
	}

}
