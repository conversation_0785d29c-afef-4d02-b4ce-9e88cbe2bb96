package net.shopxx.member.service.impl;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.intf.dao.Oa_WfModelDao;
import net.shopxx.intf.service.OaToWfService;
import net.shopxx.member.dao.StoreStopDao;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreStop;
import net.shopxx.member.entity.StoreStopAttach;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreStopService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service("storeStopServiceImpl")
public class StoreStopServiceImpl extends ActWfBillServiceImpl<StoreStop>
        implements StoreStopService {
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "storeStopDao")
    private StoreStopDao storeStopDao;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "oaToWfServiceImpl")
    private OaToWfService oaToWfService;
    @Resource(name = "oa_WfModelDao")
    private Oa_WfModelDao oa_WfModelDao;


    @Override
    public void saveStoreStop(StoreStop storeStop) {
        storeStop.setSn(Sequence.getInstance().getSequence("ZZ"));
        storeStop.setStatus(0);

        //处理附件
        // 处理附件
        List<StoreStopAttach> r0Attach = handleAttach(storeStop, storeStop.getStoreStop0Attachs(), 0, 1);
        storeStop.setStoreStop0Attachs(r0Attach);
        List<StoreStopAttach> r1Attach = handleAttach(storeStop, storeStop.getStoreStop1Attachs(), 1, 1);
        storeStop.setStoreStop1Attachs(r1Attach);
        List<StoreStopAttach> r2Attach = handleAttach(storeStop, storeStop.getStoreStop2Attachs(), 2, 1);
        storeStop.setStoreStop2Attachs(r2Attach);
        List<StoreStopAttach> r3Attach = handleAttach(storeStop, storeStop.getStoreStop3Attachs(), 3, 1);
        storeStop.setStoreStop3Attachs(r3Attach);

        this.save(storeStop);
    }

    @Override
    public Page<Map<String, Object>> findPage(StoreStop ss, List<Object> param, Pageable pageable) {
        return storeStopDao.findPage(ss,param, pageable);
    }

    @Override
    public void updateStoreStop(StoreStop ss, Long sid) {
        StoreStop storeStop = find(ss.getId());

        //storeStop.setStatus(ss.getStatus());
        storeStop.setLetters(ss.getLetters());
        storeStop.setWhetherSignedContract(ss.getWhetherSignedContract());
        storeStop.setWhetherSignature(ss.getWhetherSignature());
        storeStop.setWhetherSubmitPlan(ss.getWhetherSubmitPlan());
        storeStop.setRectificationContent(ss.getRectificationContent());

        // 处理附件
        List<StoreStopAttach> r0Attach = handleAttach(storeStop, ss.getStoreStop0Attachs(), 0, 1);
        storeStop.getStoreStop0Attachs().clear();
        storeStop.getStoreStop0Attachs().addAll(r0Attach);
        List<StoreStopAttach> r1Attach = handleAttach(storeStop, ss.getStoreStop1Attachs(), 1, 1);
        storeStop.getStoreStop1Attachs().clear();
        storeStop.getStoreStop1Attachs().addAll(r1Attach);
        List<StoreStopAttach> r2Attach = handleAttach(storeStop, ss.getStoreStop2Attachs(), 2, 1);
        storeStop.getStoreStop2Attachs().clear();
        storeStop.getStoreStop2Attachs().addAll(r2Attach);
        List<StoreStopAttach> r3Attach = handleAttach(storeStop, ss.getStoreStop3Attachs(), 3, 1);
        storeStop.getStoreStop3Attachs().clear();
        storeStop.getStoreStop3Attachs().addAll(r3Attach);

        update(storeStop);

    }

    @Override
    @Transactional
    public void saveform(Integer type, StoreStop ss) {
        StoreStop storeStop = find(ss.getId());
        ActWf wf = getWfByWfId(storeStop.getWfId());
        if(getCurrTaskByWf(wf)!=null){
        	wfPass(ss, getCurrTaskByWf(wf).getName());
        }
        ss.setStore(storeStop.getStore());
        ss.setSn(storeStop.getSn());

        /**
         * type:
         * 1省长
         * 2渠道部
         * 3销售中心副总
         * 4事业部总裁
         */
        if (type == 1) {
            if (ss.getGovernorOpinion() != null) {
                storeStop.setGovernorOpinion(ss.getGovernorOpinion());
            }
        }

        if (type == 2) {
            if (ss.getChannelOpinion() != null) {
                storeStop.setChannelOpinion(ss.getChannelOpinion());
            }
            // 处理附件
            List<StoreStopAttach> r4Attach = handleAttach(storeStop, ss.getStoreStop4Attachs(), 4, 1);
            storeStop.getStoreStop4Attachs().clear();
            storeStop.getStoreStop4Attachs().addAll(r4Attach);
        }

        if (type == 3) {
            if (ss.getSalesCenterOpinion() != null) {
                storeStop.setSalesCenterOpinion(ss.getSalesCenterOpinion());
            }
        }

        if (type == 4) {
            if (ss.getBusinessOpinion() != null) {
                storeStop.setBusinessOpinion(ss.getBusinessOpinion());
            }
        }
        if (type == 99){
        	if (ss.getArchivesNo() != null) {
                storeStop.setArchivesNo(ss.getArchivesNo());
            }

            if (ss.getConsigneeType() != null) {
                storeStop.setConsigneeType(ss.getConsigneeType());
            }

            if (ss.getFileNo() != null) {
                storeStop.setFileNo(ss.getFileNo());
            }
            if (ss.getReorganizeTimeLimit() != null) {
                storeStop.setReorganizeTimeLimit(ss.getReorganizeTimeLimit());
            }
            if (ss.getDealerReceivedTime() != null) {
                storeStop.setDealerReceivedTime(ss.getDealerReceivedTime());
            }
            if (ss.getSendOutTime() != null) {
                storeStop.setSendOutTime(ss.getSendOutTime());
            }
            if (ss.getConsignee() != null) {
                storeStop.setConsignee(ss.getConsignee());
            }
            if (ss.getConsigneePhone() != null) {
                storeStop.setConsigneePhone(ss.getConsigneePhone());
            }
            if (ss.getTrackingNumber() != null) {
                storeStop.setTrackingNumber(ss.getTrackingNumber());
            }
            if (ss.getCourierReceivedTime() != null) {
                storeStop.setCourierReceivedTime(ss.getCourierReceivedTime());
            }
            if (ss.getWhetherSignature() != null) {
                storeStop.setWhetherReceipt(ss.getWhetherReceipt());
            }
            if (ss.getExpressArea() != null&&ss.getExpressArea().getId() != null) {
                storeStop.setExpressArea(ss.getExpressArea());
            }
            if (ss.getExpressAddress() != null) {
                storeStop.setExpressAddress(ss.getExpressAddress());
            }
            if (ss.getWhetherStopDealer() != null) {
                storeStop.setWhetherStopDealer(ss.getWhetherStopDealer());
            }
            if (ss.getOpinion() != null) {
                storeStop.setOpinion(ss.getOpinion());
            }
        }
        update(storeStop);
    }

    @Override
    public void createWf(Long id, String modelId, Long objTypeId) {
        StoreStop storeStop = find(id);
        
        if (storeStop.getWfId() != null) {
            ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
        }

        StoreMember storeMember = storeMemberBaseService.getCurrent();

        createWf(storeStop.getSn(),
                String.valueOf(storeMember.getId()),
                new Long[]{storeStop.getStore().getSaleOrg().getId()},
                storeStop.getStore().getId(),
                modelId,
                objTypeId,
                id,
                WebUtils.getCurrentCompanyInfoId(),
                true);
    }


    /**
     * 处理附件方法
     *
     * @param storeStop
     * @param attachs
     * @param count     1、不限制附件数量，2、限制附件数量
     * @param type      0、三年销量数据签字版，
     *                  1、函件省长签字版
     *                  2、函件Word版
     *                  3、其他
     *                  4、渠道附件
     * @return
     */
    private List<StoreStopAttach> handleAttach(StoreStop storeStop, List<StoreStopAttach> attachs, Integer type, Integer count) {
        if (count == 2 && attachs != null && attachs.size() < 4) {
            ExceptionUtil.throwServiceException("附件照片不能少于 4 张");
        }
        Iterator<StoreStopAttach> iterator = attachs.iterator();
        while (iterator.hasNext()) {
            StoreStopAttach attach = iterator.next();
            if (attach == null || attach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (attach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            attach.setFileName(attach.getName() + "." + attach.getSuffix());
            attach.setType(type);
            attach.setStoreStop(storeStop);
            attach.setStoreMember(storeMemberService.getCurrent());
        }
        return attachs;
    }




    /**
     * 设置流程分支参数
     */
    @Override
    public Map<String, Object> setFormData(ActWf wf, String taskId) {
        Map<String, Object> dayMap = new HashMap<String, Object>();
        StoreStop storeStop = this.find(wf.getObjId());
        dayMap.put("isCore", storeStop.getStore().getIsCoreStroe());
        return dayMap;
    }

    @Override
    @Transactional
    public void startBack(ActWf wf) {
        super.startBack(wf);
        List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
        for (String receiver : user) {
            oaToWfService.receiveRequestInfoByJson(wf,
                    new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
        }
        StoreStop storeStop = find(wf.getObjId());
        storeStop.setStatus(2);
        update(storeStop);
    }

    /** 通过节点任务回调 发送待办 */
    @Transactional
    public void agreeBack(ActWf wf) {
        if (getCurrTaskByWf(wf) != null) {
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务回调 发送待办 */
    @Transactional
    public void rejectBack(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 通过节点任务前回调 发送待办转以办 */
    @Transactional
    public void agreePre(ActWf wf) {
    	super.agreePre(wf);
    	if(getCurrTaskByWf(wf)!=null){
			wfPass(find(wf.getObjId()), this.getCurrTaskByWf(wf).getName());
            // oa触发OA接口推送待办转以办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
		}
    	
    }

    /** 驳回节点任务前回调 发送待办转以办 */
    @Transactional
    public void rejectPre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };


    /**
     * 中断流程回调
     */
    @Override
    @Transactional
    public void interruptBack(ActWf wf) {
        super.interruptBack(wf);
        oaToWfService.deleteRequestInfoByJson(wf);
        StoreStop storeStop = find(wf.getObjId());
        storeStop.setStatus(0);
        update(storeStop);
    }

    /**
     * 流程结束回调
     */
    @Override
    @Transactional
    public void endBack(ActWf wf) {
        super.endBack(wf);
        StoreStop storeStop = find(wf.getObjId());
        storeStop.setStatus(1);
        update(storeStop);
        oaToWfService.processOverRequestByJson(wf,
                new String[] { "", oa_WfModelDao.findWfStartUser(wf.getProcInstId()) });
        storeBaseService.storeStop(storeStop.getStore(),wf);
    }

    public String findUserName(String id) {
        StoreMember storeMember = storeMemberService.find(Long.parseLong(id));
        if (storeMember == null) {
            return "";
        } else {
            return storeMember.getUsername();
        }
    }
    
    /**
	 * 必填校验
	 */
	public void wfPass(StoreStop storeStop, String nodeName) {
		if (nodeName.contains("省长")) {
			if (ConvertUtil.isEmpty(storeStop.getGovernorOpinion())) {
				ExceptionUtil.throwServiceException("请填写省长意见！");
			}
		}
	}

	@Override
	public Integer count(StoreStop ss, List<Object> param) {
		return storeStopDao.count(ss, param);
	}

	@Override
	public List<Map<String, Object>> findList(StoreStop ss, List<Object> param, Pageable pageable, Integer page,
                                              Integer size) {
		return storeStopDao.findList(ss, param, pageable, page, size);
	}
}