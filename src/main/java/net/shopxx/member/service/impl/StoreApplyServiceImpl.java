package net.shopxx.member.service.impl;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.Sequence.Rule;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.*;
import net.shopxx.finance.entity.AccountParameter;
import net.shopxx.finance.entity.StoreBalance;
import net.shopxx.finance.service.AccountParameterService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.dao.StoreApplyDao;
import net.shopxx.member.entity.*;
import net.shopxx.member.entity.StoreApply.Type;
import net.shopxx.member.service.*;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.shop.entity.Shop;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.task.Task;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;

@Service("storeApplyServiceImpl")
public class StoreApplyServiceImpl extends ActWfBillServiceImpl<StoreApply>
		implements StoreApplyService {

	@Resource(name = "storeApplyDao")
	private StoreApplyDao storeApplyDao;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "accountParameterServiceImpl")
	private AccountParameterService accountParameterService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
//	@Resource(name = "wfTempBaseServiceImpl")
//	private WfTempBaseService wfTempBaseService;
//	@Resource(name = "wfBaseServiceImpl")
//	private WfBaseService wfBaseService;
//	@Resource(name = "wfObjConfigLineBaseServiceImpl")
//	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "productCategoryBaseServiceImpl")
	private ProductCategoryBaseService productCategoryService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeAddressServiceImpl")
	private StoreAddressService storeAddressService;
	@Resource(name = "salesareaServiceImpl")
	private SalesAreaService salesAreaService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Autowired
	private TaskService taskService;

	@Override
	public List<Integer> getTypes() {
		List types = (new Store()).getAllTypeInt();
		for (Iterator iterator = types.iterator(); iterator.hasNext();) {
			int type = ((Integer) iterator.next()).intValue();
			if (type == 0 || type == 1 || type == 2 || type == 3)
				iterator.remove();
		}

		return types;
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String name, String outTradeNo,
			String alias, Integer[] type, Long companyInfoId,
			Long memberRankId, Long saleOrgId, Pageable pageable) {

		return storeApplyDao.findPage(name,
				outTradeNo,
				alias,
				type,
				companyInfoId,
				memberRankId,
				saleOrgId,
				pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findStoreApplyAddressPage(String mobile,
			String consignee, Long storeId, String address, Pageable pageable) {

		return storeApplyDao.findStoreApplyAddressPage(mobile,
				consignee,
				storeId,
				address,
				pageable);
	}

	@Override
	public List<Map<String, Object>> findBusinessCategoryApply(
			StoreApply storeApply) {

		return storeApplyDao.findBusinessCategoryApply(storeApply);
	}

	@Override
	public List<Map<String, Object>> findBusinessRecordApply(
			StoreApply storeApply) {

		return storeApplyDao.findBusinessRecordApply(storeApply);
	}

	@Override
	public List<Map<String, Object>> findCautionMoneyApply(StoreApply storeApply) {

		return storeApplyDao.findCautionMoneyApply(storeApply);
	}

	@Override
	public List<Map<String, Object>> findInvoiceInfoListByStoreApply(
			Long storeId) {

		return storeApplyDao.findInvoiceInfoListByStoreApply(storeId);
	}

	@Override
	@Transactional
	public void save(StoreApply storeApply, Long storeMemberId, Long saleOrgId,
			Long memberRankId, Long salesPlatformId, Long businessTypeId,
			Long areaId, String saleType, Long storeApplySalesAreaId, Long sbuId,
			Long distributorStatusId) {
		if (storeApply.getHeadNewArea() == null
				|| storeApply.getHeadNewArea().getId() == null) {
			storeApply.setHeadNewArea(null);
		}
		// 设置客户编码:销售体系词汇编码+八位数
		if (saleType != null) {
			/*
			 * NativeDao nativeDao = getDaoCenter().getNativeDao(); List<Object>
			 * list = new ArrayList<Object>(); StringBuilder sql = new
			 * StringBuilder(); StringBuilder sqlShipping = new StringBuilder();
			 * sql.append(
			 * "select MAX(sa.out_trade_no) maxOutTradeNo from xx_store_apply sa where 1 = 1  and length(out_trade_no) = 10"
			 * ); Long companyInfoId = WebUtils.getCurrentCompanyInfoId(); if
			 * (companyInfoId != null) {
			 * sql.append(" and sa.company_info_id = ?");
			 * list.add(companyInfoId); } if (saleType != null) {
			 * sql.append(" and sa.out_trade_no like ?"); list.add(saleType +
			 * "%"); }
			 */

			/*
			 * String storeAuth = storeMemberBaseService.storeAuth(); if
			 * (storeAuth != null) { sql.append(" and a.stores in (" + storeAuth
			 * + ")"); sqlShipping.append(" and a.stores in (" + storeAuth +
			 * ")"); }
			 */
			/*
			 * Object[] objs = new Object[list.size()]; for (int i = 0; i <
			 * list.size(); i++) { objs[i] = list.get(i); } List<Map<String,
			 * Object>> saList = nativeDao.findListMap(sql.toString(), objs, 0);
			 */
			//String newOutTradeNo = "00000001";
//			if (saList != null
//					&& saList.size() > 0
//					&& saList.get(0).get("maxOutTradeNo") != null) {
//				newOutTradeNo = saList.get(0).get("maxOutTradeNo").toString();
//				if (newOutTradeNo.length() == 10) {
//					String outTradeNoN = newOutTradeNo.substring(2, 10);
//					int newOutTradeNoN = Integer.parseInt(outTradeNoN) + 1;
//					String outTradeNo = String.format(saleType + "%08d",
//							newOutTradeNoN);// 补齐0
//					storeApply.setOutTradeNo(outTradeNo);
//				}
//				else {
//					newOutTradeNo = "00000001";
//					storeApply.setOutTradeNo(String.format(saleType + "%08d",
//							Integer.parseInt(newOutTradeNo)));
//				}
//			}
//			else {
//				newOutTradeNo = "00000001";
//				storeApply.setOutTradeNo(String.format(saleType + "%08d",
//						Integer.parseInt(newOutTradeNo)));
//			}
		}

		if (storeApply.getOutTradeNo() == null) {
			StringBuilder sbString = new StringBuilder();
			sbString.append(Sequence.getInstance().getSequence(6,
					WebUtils.getCurrentCompanyInfoId(),
					Rule.NONE));
			String maxSn = String.format("%06d",
					Integer.valueOf(sbString.toString()));
			storeApply.setMaxSn(maxSn.toString());
			String bh = "";
			Long sbuIds = null;
			List<Filter> filters = new ArrayList<Filter>();
			StoreMember storeMember = storeMemberService.getCurrent();
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMember));
			List<StoreMemberSbu> sbu = storeMemberSbuService.findList(null,
					filters,
					null);
			if (sbuId != null) {
				SystemDict systemDict = systemDictBaseService.find(sbuId);
				for (StoreMemberSbu sb : sbu) {
					if (sb.getSbu().getName().equals(systemDict.getValue())) {
						sbuIds = sb.getSbu().getId();
					}
				}
				bh = sbuService.find(sbuIds).getCodePre();
			}
			storeApply.setOutTradeNo(bh + maxSn);
		}

		if (distributorStatusId != null) {
			SystemDict distributorStatus = systemDictBaseService.find(distributorStatusId);
			storeApply.setDistributorStatus(distributorStatus);
		}
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		if (saleOrgId != null) {
			storeApply.setSaleOrg(saleOrg);
		}
		else {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("isTop", true));
			List<SaleOrg> saleOrgs = saleOrgService.findList(1, filters, null);
			if (saleOrgs.size() > 0) {
				storeApply.setSaleOrg(saleOrgs.get(0));
			}
		}
		StoreMember storeMember = storeMemberService.find(storeMemberId);
		if (storeMember != null) {
			storeApply.setStoreMember(storeMember);
		}

		SaleOrg salesPlatform = saleOrgService.find(salesPlatformId);
		storeApply.setSalesPlatform(salesPlatform);

		// 处理经营品类
		List<BusinessCategoryApply> bc = storeApply.getBusinessCategoryApplys();
		for (Iterator<BusinessCategoryApply> iterator = bc.iterator(); iterator.hasNext();) {
			BusinessCategoryApply businessCategory = (BusinessCategoryApply) iterator.next();
			if (businessCategory == null
					|| businessCategory.getProductBigType() == null) {
				iterator.remove();
			}
			if (businessCategory.getProductBigType() != null) {
				businessCategory.setProductBigType(productCategoryService.find(businessCategory.getProductBigType()
						.getId()));
			}
			if (businessCategory.getProductCenterType() != null) {
				businessCategory.setProductCenterType(productCategoryService.find(businessCategory.getProductCenterType()
						.getId()));
			}
			businessCategory.setStoreApply(storeApply);
		}
		storeApply.setBusinessCategoryApplys(bc);

		// 处理经营活动记录
		List<BusinessRecordApply> br = storeApply.getBusinessRecordApplys();
		for (Iterator<BusinessRecordApply> iterator = br.iterator(); iterator.hasNext();) {
			BusinessRecordApply businessRecord = (BusinessRecordApply) iterator.next();
			if (businessRecord == null || businessRecord.getAmount() == null) {
				iterator.remove();
			}
			businessRecord.setStoreApply(storeApply);
			if (ConvertUtil.isEmpty(businessRecord.getSn())) {
				businessRecord.setSn("R"
						+ Sequence.getInstance().getSequence(null));
			}
		}
		storeApply.setBusinessRecordApplys(br);

		// 处理保证金
		List<CautionMoneyApply> cm = storeApply.getCautionMoneieApplys();
		for (Iterator<CautionMoneyApply> iterator = cm.iterator(); iterator.hasNext();) {
			CautionMoneyApply cautionMoney = (CautionMoneyApply) iterator.next();
			if (cautionMoney == null || cautionMoney.getPayable() == null) {
				iterator.remove();
			}
			cautionMoney.setStoreApply(storeApply);
			if (ConvertUtil.isEmpty(cautionMoney.getSn())) {
				cautionMoney.setSn("A"
						+ Sequence.getInstance().getSequence(null));
			}
		}
		storeApply.setCautionMoneieApplys(cm);

		if (memberRankId != null) {
			storeApply.setMemberRank(memberRankService.find(memberRankId));
		}
		else {
			storeApply.setMemberRank(null);
		}
		// storeApply.setChangeMemberRank(
		// memberRankService.find(changeMemberRankId));

		Area area = areaService.find(areaId);
		storeApply.setArea(area);
		storeApply.setIsEnabled(true);
		storeApply.setIsMainStore(false);
		if (!storeApply.getType().equals(Type.onlineShop)) {
			storeApply.setOutShopName(null);
			storeApply.setOnlineShopType(null);
		}
		storeApply.setSn(Sequence.getInstance().getSequence(null));
		List<StoreApplyAddress> addressList = storeApply.getStoreApplyAddress();
		for (Iterator<StoreApplyAddress> iterator = addressList.iterator(); iterator.hasNext();) {
			StoreApplyAddress storeAddress = (StoreApplyAddress) iterator.next();
			if (storeAddress == null) {
				iterator.remove();
				continue;
			}
			if (storeAddress.getArea() != null) {
				Area a = areaService.find(storeAddress.getArea().getId());
				storeAddress.setArea(a);
				SalesArea salesArea = salesAreaService.find(storeAddress.getSalesArea()
						.getId());
				a.setSalesAreaId(salesArea.getId());
				storeAddress.setSalesArea(salesArea);
			}
			else {
				storeAddress.setArea(null);
				storeAddress.setSalesArea(null);
			}
			if (storeAddress.getSalesArea() != null) {
				SalesArea salesArea = salesAreaService.find(storeAddress.getSalesArea()
						.getId());
				storeAddress.setSalesArea(salesArea);
			}
			else {
				storeAddress.setSalesArea(null);
			}
			storeAddress.setStoreApply(storeApply);
		}

		List<StoreApplyInvoiceInfo> storeInvoiceInfos = storeApply.getStoreApplyInvoiceInfos();
		for (Iterator<StoreApplyInvoiceInfo> iterator = storeInvoiceInfos.iterator(); iterator.hasNext();) {
			StoreApplyInvoiceInfo storeInvoiceInfo = iterator.next();
			if (storeInvoiceInfo == null
					|| storeInvoiceInfo.getInvoiceTitle() == null) {
				iterator.remove();
			}
			storeInvoiceInfo.setStoreApply(storeApply);
		}

		//storeApply.setStoreApplyInvoiceInfos(storeInvoiceInfos);

		//客户联系人
		List<StoreApplyContract> storeApplyContracts = storeApply.getStoreApplyContract();
		for (Iterator<StoreApplyContract> iterator = storeApplyContracts.iterator(); iterator.hasNext();) {
			StoreApplyContract storeApplyContract = iterator.next();
			if (storeApplyContract == null
					|| storeApplyContract.getName() == null) {
				iterator.remove();
			}
			storeApplyContract.setStoreApply(storeApply);
		}
		storeApply.setStoreApplyContract(storeApplyContracts);
		SalesArea storeApplySalesArea = salesAreaService.find(storeApplySalesAreaId);
		if (storeApplySalesArea != null) {
			storeApply.setSalesArea(storeApplySalesArea);
		}

		//合作单位
		List<StoreApplyCooperation> storeApplyCooperations = storeApply.getStoreApplyCooperation();
		for (Iterator<StoreApplyCooperation> iterator = storeApplyCooperations.iterator(); iterator.hasNext();) {
			StoreApplyCooperation storeApplyCooperation = iterator.next();
			if (storeApplyCooperation == null) {
				iterator.remove();
			}
			storeApplyCooperation.setStoreApply(storeApply);
		}
		storeApply.setStoreApplyCooperation(storeApplyCooperations);

		storeApply.setCreateBy(storeMemberService.getCurrent());
		storeApply.setUnJoinSucessFlag(false);
		storeApply.setDocStatus(0);

		if (businessTypeId != null) {
			SystemDict businessType = systemDictService.find(businessTypeId);
			storeApply.setBusinessType(businessType);
		}

		// 附件
		List<StoreApplyAttach> storeApplyAttachs = storeApply.getStoreApplyAttachs();
		for (Iterator<StoreApplyAttach> iterator = storeApplyAttachs.iterator(); iterator.hasNext();) {
			StoreApplyAttach storeApplyAttach = iterator.next();
			if (storeApplyAttach == null || storeApplyAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (storeApplyAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			storeApplyAttach.setFileName(storeApplyAttach.getName()
					+ "."
					+ storeApplyAttach.getSuffix());
			storeApplyAttach.setStoreApply(storeApply);
			storeApplyAttach.setStoreMember(storeMemberService.getCurrent());
		}
		storeApply.setStoreApplyAttachs(storeApplyAttachs);
		if (storeApply.getHeadNewArea() == null
				|| (storeApply.getHeadNewArea() != null && storeApply.getHeadNewArea()
						.getId() == null)) {
			storeApply.setHeadNewArea(null);
		}
		//sbu保存
		List<StoreApplySbu> storeApplySbuList = storeApply.getStoreApplySbu();
		for (Iterator<StoreApplySbu> iterator = storeApplySbuList.iterator(); iterator.hasNext();) {
			StoreApplySbu storeApplySbu = (StoreApplySbu) iterator.next();
			if (storeApplySbu == null
					|| storeApplySbu.getSbu() == null
					|| (storeApplySbu.getSbu() != null && storeApplySbu.getSbu()
							.getId() == null)) {
				iterator.remove();
				continue;
			}
			Sbu sbu = sbuService.find(storeApplySbu.getSbu().getId());
			MemberRank memberRank = memberRankService.find(storeApplySbu.getMemberRank()
					.getId());
			storeApplySbu.setSbu(sbu);
			storeApplySbu.setStoreApply(storeApply);
			storeApplySbu.setMemberRank(memberRank);
		}
		storeApply.setStoreApplySbu(storeApplySbuList);

		/*
		 * if (storeApply.getOutTradeNo() != null &&
		 * (exists(Filter.eq("outTradeNo", storeApply.getOutTradeNo()),
		 * Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId())) ||
		 * storeService.exists(Filter.eq("outTradeNo",
		 * storeApply.getOutTradeNo()), Filter.eq("companyInfoId",
		 * WebUtils.getCurrentCompanyInfoId())))) { // 客户名称已存在
		 * ExceptionUtil.throwServiceException("客户编码[" +
		 * storeApply.getOutTradeNo() + "]已存在"); }
		 */
		save(storeApply);
	}

	@Override
	@Transactional
	public void update(StoreApply storeApply, Long storeMemberId, Long areaId,
			Long saleOrgId, Long memberRankId, Long salesPlatformId,
			Long businessTypeId, BankCard bankCard, Long bAreaId,
			String bAddress, String bMobile, Boolean bIsEnabled,
			Long changeMemberRankId, Long sbuId, Long storeApplySalesAreaId,
			Long createById,Long distributorStatusId) {
		if (storeApply.getHeadNewArea() == null
				|| storeApply.getHeadNewArea().getId() == null) {
			storeApply.setHeadNewArea(null);
		}
		StoreApply pStoreApply = find(storeApply.getId());
		SaleOrg salesPlatform = saleOrgService.find(salesPlatformId);
		storeApply.setSalesPlatform(salesPlatform);
		
		if (distributorStatusId != null) {
			SystemDict distributorStatus = systemDictBaseService.find(distributorStatusId);
			storeApply.setDistributorStatus(distributorStatus);
		}
		// 处理经营品类
		List<BusinessCategoryApply> bc = storeApply.getBusinessCategoryApplys();
		for (Iterator<BusinessCategoryApply> iterator = bc.iterator(); iterator.hasNext();) {
			BusinessCategoryApply businessCategory = (BusinessCategoryApply) iterator.next();
			if (businessCategory == null
					|| businessCategory.getProductBigType() == null) {
				iterator.remove();
			}
			if (businessCategory.getProductBigType() != null) {
				businessCategory.setProductBigType(productCategoryService.find(businessCategory.getProductBigType()
						.getId()));
			}
			if (businessCategory.getProductCenterType() != null) {
				businessCategory.setProductCenterType(productCategoryService.find(businessCategory.getProductCenterType()
						.getId()));
			}
			businessCategory.setStoreApply(storeApply);
		}
		storeApply.setBusinessCategoryApplys(bc);
		// 处理经营活动记录
		List<BusinessRecordApply> br = storeApply.getBusinessRecordApplys();
		for (Iterator<BusinessRecordApply> iterator = br.iterator(); iterator.hasNext();) {
			BusinessRecordApply businessRecord = (BusinessRecordApply) iterator.next();
			if (businessRecord == null || businessRecord.getAmount() == null) {
				iterator.remove();
			}
			businessRecord.setStoreApply(storeApply);
			if (ConvertUtil.isEmpty(businessRecord.getSn())) {
				businessRecord.setSn("R"
						+ Sequence.getInstance().getSequence(null));
			}
		}
		storeApply.setBusinessRecordApplys(br);

		// 处理保证金
		List<CautionMoneyApply> cm = storeApply.getCautionMoneieApplys();
		for (Iterator<CautionMoneyApply> iterator = cm.iterator(); iterator.hasNext();) {
			CautionMoneyApply cautionMoney = (CautionMoneyApply) iterator.next();
			if (cautionMoney == null || cautionMoney.getPayable() == null) {
				iterator.remove();
			}
			cautionMoney.setStoreApply(storeApply);
			if (ConvertUtil.isEmpty(cautionMoney.getSn())) {
				cautionMoney.setSn("A"
						+ Sequence.getInstance().getSequence(null));
			}
		}
		storeApply.setCautionMoneieApplys(cm);

		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		List<Filter> filters = new ArrayList<Filter>();
		if (saleOrgId != null) {
			storeApply.setSaleOrg(saleOrg);
		}
		else {
			filters.add(Filter.eq("isTop", true));
			List<SaleOrg> saleOrgs = saleOrgService.findList(1, filters, null);
			if (saleOrgs.size() > 0) {
				storeApply.setSaleOrg(saleOrgs.get(0));
			}
		}

		StoreMember storeMember = storeMemberService.find(storeMemberId);
		if (storeMember != null) {
			storeApply.setStoreMember(storeMember);
		}

		if (memberRankId != null) {
			storeApply.setMemberRank(memberRankService.find(memberRankId));
		}
		else {
			storeApply.setMemberRank(null);
		}
		storeApply.setChangeMemberRank(memberRankService.find(changeMemberRankId));
		if (areaId != null) {
			Area area = areaService.find(areaId);
			storeApply.setArea(area);
		}
		storeApply.setArea(null);

		storeApply.setIsMainStore(pStoreApply.getIsMainStore());
		if (storeApply.getType() != null
				&& !storeApply.getType().equals(Type.onlineShop)) {
			storeApply.setOutShopName(null);
			storeApply.setOnlineShopType(null);
		}
		List<StoreApplyAddress> addressList = storeApply.getStoreApplyAddress();
		for (Iterator<StoreApplyAddress> iterator = addressList.iterator(); iterator.hasNext();) {
			StoreApplyAddress storeAddress = (StoreApplyAddress) iterator.next();
			if (storeAddress == null || storeAddress.getAddress() == null) {
				iterator.remove();
				continue;
			}
			if (storeAddress.getArea().getId() != null) {
				Area a = areaService.find(storeAddress.getArea().getId());
				storeAddress.setArea(a);
			}
			else {
				storeAddress.setArea(null);
			}
			if (storeAddress.getSalesArea() != null) {
				SalesArea salesArea = salesAreaService.find(storeAddress.getSalesArea()
						.getId());
				storeAddress.setSalesArea(salesArea);
			}
			else {
				storeAddress.setSalesArea(null);
			}

			storeAddress.setStoreApply(storeApply);
		}
		storeApply.setStoreApplyAddress(addressList);

		List<StoreApplyInvoiceInfo> storeInvoiceInfos = storeApply.getStoreApplyInvoiceInfos();
		for (Iterator<StoreApplyInvoiceInfo> iterator = storeInvoiceInfos.iterator(); iterator.hasNext();) {
			StoreApplyInvoiceInfo storeInvoiceInfo = iterator.next();
			if (storeInvoiceInfo == null
					|| storeInvoiceInfo.getInvoiceTitle() == null) {
				iterator.remove();
			}

			storeInvoiceInfo.setStoreApply(storeApply);
		}
		storeApply.setStoreApplyInvoiceInfos(storeInvoiceInfos);

		//客户联系人
		List<StoreApplyContract> storeApplyContracts = storeApply.getStoreApplyContract();
		for (Iterator<StoreApplyContract> iterator = storeApplyContracts.iterator(); iterator.hasNext();) {
			StoreApplyContract storeApplyContract = iterator.next();
			if (storeApplyContract == null
					|| storeApplyContract.getName() == null) {
				iterator.remove();
			}
			storeApplyContract.setStoreApply(storeApply);
		}
		storeApply.setStoreApplyContract(storeApplyContracts);

		//合作单位
		List<StoreApplyCooperation> storeApplyCooperations = storeApply.getStoreApplyCooperation();
		for (Iterator<StoreApplyCooperation> iterator = storeApplyCooperations.iterator(); iterator.hasNext();) {
			StoreApplyCooperation storeApplyCooperation = iterator.next();
			if (storeApplyCooperation == null) {
				iterator.remove();
			}
			storeApplyCooperation.setStoreApply(storeApply);
		}
		storeApply.setStoreApplyCooperation(storeApplyCooperations);
		storeApply.setCreateBy(storeMemberService.find(createById));
		SalesArea storeApplySalesArea = salesAreaService.find(storeApplySalesAreaId);
		if (storeApplySalesArea != null) {
			storeApply.setSalesArea(storeApplySalesArea);
		}

		storeApply.setDocStatus(0);

		if (businessTypeId != null) {
			SystemDict businessType = systemDictService.find(businessTypeId);
			storeApply.setBusinessType(businessType);
		}
		SystemDict sbu = systemDictService.find(sbuId);
		storeApply.setSbu(sbu);

		// 附件
		List<StoreApplyAttach> storeApplyAttachs = storeApply.getStoreApplyAttachs();

		for (Iterator<StoreApplyAttach> iterator = storeApplyAttachs.iterator(); iterator.hasNext();) {
			StoreApplyAttach storeApplyAttach = iterator.next();
			if (storeApplyAttach == null || storeApplyAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (storeApplyAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			storeApplyAttach.setFileName(storeApplyAttach.getName()
					+ "."
					+ storeApplyAttach.getSuffix());
			storeApplyAttach.setStoreApply(storeApply);
			storeApplyAttach.setStoreMember(storeMemberService.getCurrent());
		}
		storeApply.setStoreApplyAttachs(storeApplyAttachs);
		if (storeApply.getHeadNewArea() == null
				|| (storeApply.getHeadNewArea() != null && storeApply.getHeadNewArea()
						.getId() == null)) {
			storeApply.setHeadNewArea(null);
		}
		//sbu保存
		List<StoreApplySbu> storeApplySbuList = storeApply.getStoreApplySbu();
		for (Iterator<StoreApplySbu> iterator = storeApplySbuList.iterator(); iterator.hasNext();) {
			StoreApplySbu storeApplySbu = (StoreApplySbu) iterator.next();
			if (storeApplySbu == null
					|| storeApplySbu.getSbu() == null
					|| (storeApplySbu.getSbu() != null && storeApplySbu.getSbu()
							.getId() == null)) {
				iterator.remove();
				continue;
			}
			Sbu newSbu = sbuService.find(storeApplySbu.getSbu().getId());
			MemberRank memberRank = memberRankService.find(storeApplySbu.getMemberRank()
					.getId());
			storeApplySbu.setSbu(newSbu);
			storeApplySbu.setStoreApply(storeApply);
			storeApplySbu.setMemberRank(memberRank);
		}
		storeApply.setStoreApplySbu(storeApplySbuList);
		update(storeApply,
				"sn",
				"balance",
				"credit",
				"budget",
				"lockBudget",
				"lockBalance");

		// filters.clear();
		// filters.add(Filter.eq("store", store));
		// BankCard pBankCard = bankCardBaseService.find(filters);
		// if (pBankCard != null) {
		// bankCard.setId(pBankCard.getId());
		// Area bArea = areaBaseService.find(bAreaId);
		// bankCard.setArea(bArea);
		// bankCard.setAddress(bAddress);
		// bankCard.setMobile(bMobile);
		// bankCard.setIsEnabled(bIsEnabled);
		// bankCard.setStore(store);
		// bankCardBaseService.update(bankCard);
		// }
		// else {
		// if (!ConvertUtil.isEmpty(bankCard.getBankName())
		// || !ConvertUtil.isEmpty(bankCard.getBankCode())
		// || !ConvertUtil.isEmpty(bankCard.getBankCardNo())
		// || !ConvertUtil.isEmpty(bankCard.getBankAccount())
		// || !ConvertUtil.isEmpty(bAreaId)
		// || !ConvertUtil.isEmpty(bAddress)
		// || !ConvertUtil.isEmpty(bankCard.getMemo())
		// || !ConvertUtil.isEmpty(bMobile)) {
		// Area bArea = areaBaseService.find(bAreaId);
		// bankCard.setId(null);
		// bankCard.setArea(bArea);
		// bankCard.setAddress(bAddress);
		// bankCard.setMobile(bMobile);
		// bankCard.setIsEnabled(bIsEnabled);
		// bankCard.setStore(store);
		// bankCardBaseService.save(bankCard);
		// }
		// }
		// 更新收货地址
	}

	// @Override
	// @Transactional
	// public void update(StoreApply storeApply, Long saleOrgId, Long
	// memberRankId) {
	// storeApply.setSaleOrg(saleOrgService.find(saleOrgId));
	// storeApply.setMemberRank(memberRankService.find(memberRankId));
	// storeApply.setDocStatus(0);
	// update(storeApply);
	//
	// }

	@Override
	public String storeImport1(MultipartFile multipartFile) throws Exception {

		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(
				(new StringBuilder(
						String.valueOf(System.getProperty("java.io.tmpdir")))).append("/upload_")
						.append(UUID.randomUUID())
						.append(".tmp")
						.toString());
		if (!tempFile.getParentFile().exists())
			tempFile.getParentFile().mkdirs();
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001)
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents()))
				ExceptionUtil.throwServiceException("\u4E00\u6B21\u6700\u591A\u5BFC\u51651000\u6761",
						new Object[0]);
			else
				rows = 1001;
		List<Filter> filters = new ArrayList<Filter>();
		for (int i = 1; i < rows; i++) {
			cell = sheet.getCell(0, i);
			String name = cell.getContents().trim();

			if (count(new Filter[] { Filter.eq("name", name) }) > 0L) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u5BA2\u6237[")
						.append(name)
						.append("]\u5DF2\u5B58\u5728")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}

			cell = sheet.getCell(1, i);
			String outTradeNo = cell.getContents().trim();
			cell = sheet.getCell(2, i);
			String alias = cell.getContents().trim();
			cell = sheet.getCell(3, i);
			String typeStr = cell.getContents().trim();
			if (ConvertUtil.isEmpty(typeStr)) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u5BA2\u6237\u7C7B\u578B\u672A\u586B")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			Type type = null;
			if (typeStr.equals("1"))
				type = Type.onlineShop;
			else if (typeStr.equals("2"))
				type = Type.offlineStore;
			else if (typeStr.equals("3"))
				type = Type.provider;
			else if (typeStr.equals("4"))
				type = Type.distributor;
			else if (typeStr.equals("5"))
				type = Type.directDealer;
			else if (typeStr.equals("6"))
				type = Type.reseller;
			else {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u5BA2\u6237\u7C7B\u578B\u6709\u8BEF")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			cell = sheet.getCell(4, i);
			BigDecimal taxRate = new BigDecimal(cell.getContents());
			cell = sheet.getCell(5, i);
			String memberRankName = cell.getContents().trim();
			if (ConvertUtil.isEmpty(memberRankName)) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u4F1A\u5458\u7B49\u7EA7\u540D\u79F0\u672A\u586B")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			filters.clear();
			filters.add(Filter.eq("name", memberRankName));
			MemberRank memberRank = (MemberRank) memberRankService.find(filters);
			if (memberRank == null) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u540D\u79F0\u4E3A[")
						.append(memberRankName)
						.append("]\u7684\u4F1A\u5458\u7B49\u7EA7\u4E0D\u5B58\u5728")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			cell = sheet.getCell(6, i);
			String saleOrgName = cell.getContents().trim();
			if (ConvertUtil.isEmpty(saleOrgName)) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u4F1A\u5458\u7B49\u7EA7\u540D\u79F0\u672A\u586B")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			filters.clear();
			filters.add(Filter.eq("name", saleOrgName));
			filters.add(Filter.eq("isTop", Boolean.valueOf(true)));
			SaleOrg saleOrg = saleOrgService.find(filters);
			if (saleOrg == null) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u540D\u79F0\u4E3A[")
						.append(saleOrgName)
						.append("]\u7684\u4F1A\u5458\u7B49\u7EA7\u4E0D\u5B58\u5728")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			saveStoreApply(name,
					outTradeNo,
					alias,
					type,
					taxRate,
					memberRank,
					saleOrg,
					true,
					true);
			success++;
		}
		int result = rows - 1;
		msg = (new StringBuilder("msg:\u603B\u6570")).append(result)
				.append("\u884C,\u6210\u529F\u5BFC\u5165")
				.append(success)
				.append(" \u884C. ")
				.toString();
		return msg;
	}

	@Override
	public String storeImport(MultipartFile multipartFile) throws Exception {

		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(
				(new StringBuilder(
						String.valueOf(System.getProperty("java.io.tmpdir")))).append("/upload_")
						.append(UUID.randomUUID())
						.append(".tmp")
						.toString());
		if (!tempFile.getParentFile().exists())
			tempFile.getParentFile().mkdirs();
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001)
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents()))
				ExceptionUtil.throwServiceException("\u4E00\u6B21\u6700\u591A\u5BFC\u51651000\u6761",
						new Object[0]);
			else
				rows = 1001;
		List<Filter> filters = new ArrayList<Filter>();
		for (int i = 1; i < rows; i++) {
			cell = sheet.getCell(0, i);
			String name = cell.getContents().trim();
			if (ConvertUtil.isEmpty(name)) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u5BA2\u6237\u540D\u79F0\u672A\u586B")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			if (count(new Filter[] { Filter.eq("name", name) }) > 0L) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u5BA2\u6237[")
						.append(name)
						.append("]\u5DF2\u5B58\u5728")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			if (storeService.exists(Filter.eq("name", name),
					Filter.eq("companyInfoId",
							WebUtils.getCurrentCompanyInfoId()))) {
				// 客户名称已存在
				ExceptionUtil.throwServiceException("160001");
			}
			cell = sheet.getCell(1, i);
			String outTradeNo = cell.getContents().trim();
			cell = sheet.getCell(2, i);
			String alias = cell.getContents().trim();
			cell = sheet.getCell(3, i);
			String typeStr = cell.getContents().trim();
			if (ConvertUtil.isEmpty(typeStr)) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u5BA2\u6237\u7C7B\u578B\u672A\u586B")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			Type type = null;
			if (typeStr.equals("1"))
				type = Type.onlineShop;
			else if (typeStr.equals("2"))
				type = Type.offlineStore;
			else if (typeStr.equals("3"))
				type = Type.provider;
			else if (typeStr.equals("4"))
				type = Type.distributor;
			else if (typeStr.equals("5"))
				type = Type.directDealer;
			else if (typeStr.equals("6"))
				type = Type.reseller;
			else {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u5BA2\u6237\u7C7B\u578B\u6709\u8BEF")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			cell = sheet.getCell(4, i);
			BigDecimal taxRate = new BigDecimal(cell.getContents());
			cell = sheet.getCell(5, i);
			String memberRankName = cell.getContents().trim();
			if (ConvertUtil.isEmpty(memberRankName)) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u4F1A\u5458\u7B49\u7EA7\u540D\u79F0\u672A\u586B")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			filters.clear();
			filters.add(Filter.eq("name", memberRankName));
			MemberRank memberRank = (MemberRank) memberRankService.find(filters);
			if (memberRank == null) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u540D\u79F0\u4E3A[")
						.append(memberRankName)
						.append("]\u7684\u4F1A\u5458\u7B49\u7EA7\u4E0D\u5B58\u5728")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			cell = sheet.getCell(6, i);
			String saleOrgName = cell.getContents().trim();
			if (ConvertUtil.isEmpty(saleOrgName)) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u4F1A\u5458\u7B49\u7EA7\u540D\u79F0\u672A\u586B")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			filters.clear();
			filters.add(Filter.eq("name", saleOrgName));
			filters.add(Filter.eq("isTop", Boolean.valueOf(true)));
			SaleOrg saleOrg = saleOrgService.find(filters);
			if (saleOrg == null) {
				msg = (new StringBuilder("\u7B2C")).append(i)
						.append("\u884C.")
						.append("\u540D\u79F0\u4E3A[")
						.append(saleOrgName)
						.append("]\u7684\u4F1A\u5458\u7B49\u7EA7\u4E0D\u5B58\u5728")
						.toString();
				ExceptionUtil.throwServiceException(msg, new Object[0]);
			}
			saveStoreApply(name,
					outTradeNo,
					alias,
					type,
					taxRate,
					memberRank,
					saleOrg,
					true,
					true);
			success++;
		}
		int result = rows - 1;
		msg = (new StringBuilder("msg:\u603B\u6570")).append(result)
				.append("\u884C,\u6210\u529F\u5BFC\u5165")
				.append(success)
				.append(" \u884C. ")
				.toString();
		return msg;
	}

	@Override
	@Transactional
	public void saveStoreApply(String name, String outTradeNo, String alias,
			Type type, BigDecimal taxRate, MemberRank memberRank,
			SaleOrg saleOrg, boolean isEnabled, boolean isReduceBalance) {
		StoreApply storeApply = new StoreApply();
		storeApply.setSaleOrg(saleOrg);
		storeApply.setName(name);
		storeApply.setOutTradeNo(outTradeNo);
		storeApply.setAlias(alias);
		storeApply.setType(type);
		storeApply.setTaxRate(taxRate);
		storeApply.setMemberRank(memberRank);
		storeApply.setIsEnabled(isEnabled);
		storeApply.setIsReduceBalance(isReduceBalance);
		storeApply.setDocStatus(0);
		storeApply.setType(Type.distributor);
		save(storeApply);
	}

	@Override
	@Transactional
	public void createStore(Long id) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("id", id));
		filters.add(Filter.eq("docStatus", 2));
		if (find(filters) != null) {
			ExceptionUtil.throwServiceException("该客户已审批");
		}
		StoreApply storeApply = find(id);

		if (storeService.exists(Filter.eq("name", storeApply.getName()),
				Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()))) {
			// 客户名称已存在
			ExceptionUtil.throwServiceException("160001");
		}

		if (storeService.exists(Filter.eq("outTradeNo",
				storeApply.getOutTradeNo()),
				Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()))) {
			// 客户名称已存在
			ExceptionUtil.throwServiceException("客户编码已存在");
		}

		storeApply.setDocStatus(2);
		storeApply.setType(Type.distributor);
		update(storeApply);
		Store store = new Store();
		// 复制到正式客户
		BeanUtils.copyProperties(storeApply, store, new String[] { "id",
				"createDate",
				"modifyDate",
				"sn",
				"type" });
		store.setName(storeApply.getName());
		store.setAlias(storeApply.getAlias());
		if (storeApply.getType() != null) {
			if (storeApply.getType().equals(StoreApply.Type.directDealer)) {
				store.setType(Store.Type.directDealer);
			}
			else if (storeApply.getType().equals(StoreApply.Type.distributor)) {
				store.setType(Store.Type.distributor);
			}
			else if (storeApply.getType().equals(StoreApply.Type.enterprise)) {
				store.setType(Store.Type.enterprise);
			}
			else if (storeApply.getType().equals(StoreApply.Type.offlineStore)) {
				store.setType(Store.Type.offlineStore);
			}
			else if (storeApply.getType().equals(StoreApply.Type.onlineShop)) {
				store.setType(Store.Type.onlineShop);
			}
			else if (storeApply.getType().equals(StoreApply.Type.provider)) {
				store.setType(Store.Type.provider);
			}
			else if (storeApply.getType().equals(StoreApply.Type.reseller)) {
				store.setType(Store.Type.reseller);
			}
		}
		/**lj 修改 2018年9月23日 00:29:51
		 * 客户类型 不知道其他企业怎么用，先写死天加type=4 能正常走流程  不等于4 在客户变更中查不出来数据
		 * **/
		if (storeApply.getCompanyInfoId() == 5) {
			store.setType(Store.Type.distributor);
		}

		store.setIsMainStore(false);
		store.setSn(Sequence.getInstance().getSequence(null));
		store.setCreateBy(storeMemberService.getCurrent());
		store.setUnJoinSucessFlag(false);
		store.setIsEnabled(true);
		store.setIsReduceBalance(true);

		// 复制经营品类
		List<BusinessCategoryApply> bc = storeApply.getBusinessCategoryApplys();
		List<BusinessCategory> businessCategorys = new ArrayList<BusinessCategory>();
		for (BusinessCategoryApply businessCategoryApply : bc) {
			BusinessCategory businessCategory = new BusinessCategory();
			BeanUtils.copyProperties(businessCategoryApply,
					businessCategory,
					new String[] { "id", "createDate", "modifyDate" });
			businessCategory.setStore(store);
			businessCategorys.add(businessCategory);
		}
		store.setBusinessCategorys(businessCategorys);

		// 复制storeSbu
		List<StoreApplySbu> storeApplySbuList = storeApply.getStoreApplySbu();
		List<StoreSbu> storeSbuList = new ArrayList<StoreSbu>();
		for (StoreApplySbu storeApplySbu : storeApplySbuList) {
			System.out.println("CCC" + storeApplySbu.getSbu());
			StoreSbu storeSbu = new StoreSbu();
			BeanUtils.copyProperties(storeApplySbu,
					storeSbu,
					new String[] { "id",
							"isDefault",
							"createDate",
							"modifyDate" });
			storeSbu.setStore(store);
			storeSbu.setSbu(storeApplySbu.getSbu());
			storeSbu.setMemberRank(storeApplySbu.getMemberRank());
			storeSbuList.add(storeSbu);
		}
		store.setStoreSbu(storeSbuList);

		// 复制经营活动记录
		List<BusinessRecordApply> br = storeApply.getBusinessRecordApplys();
		List<BusinessRecord> businessRecords = new ArrayList<BusinessRecord>();
		for (BusinessRecordApply businessRecordApply : br) {
			BusinessRecord businessRecord = new BusinessRecord();
			BeanUtils.copyProperties(businessRecordApply,
					businessRecord,
					new String[] { "id", "createDate", "modifyDate" });
			businessRecord.setStore(store);
			businessRecords.add(businessRecord);
		}
		store.setBusinessRecords(businessRecords);

		// 复制保证金
		List<CautionMoneyApply> cm = storeApply.getCautionMoneieApplys();
		List<CautionMoney> cautionMoneys = new ArrayList<CautionMoney>();
		for (CautionMoneyApply cautionMoneyApply : cm) {
			CautionMoney cautionMoney = new CautionMoney();
			BeanUtils.copyProperties(cautionMoneyApply,
					cautionMoney,
					new String[] { "id", "createDate", "modifyDate" });
			cautionMoney.setStore(store);
			cautionMoneys.add(cautionMoney);
		}
		store.setCautionMoneies(cautionMoneys);

		List<StoreApplyAddress> saa = storeApply.getStoreApplyAddress();
		List<StoreAddress> storeAddressList = new ArrayList<StoreAddress>();
		for (StoreApplyAddress storeApplyAddress : saa) {
			StoreAddress storeAddress = new StoreAddress();
			BeanUtils.copyProperties(storeApplyAddress,
					storeAddress,
					new String[] { "id", "createDate", "modifyDate" });
			storeAddress.setStore(store);
			storeAddressList.add(storeAddress);
		}
		store.setStoreAddress(storeAddressList);

		List<StoreApplyInvoiceInfo> saii = storeApply.getStoreApplyInvoiceInfos();
		List<StoreInvoiceInfo> storeInvoiceInfos = new ArrayList<StoreInvoiceInfo>();
		for (StoreApplyInvoiceInfo storeApplyInvoiceInfo : saii) {
			StoreInvoiceInfo storeInvoiceInfo = new StoreInvoiceInfo();
			BeanUtils.copyProperties(storeApplyInvoiceInfo,
					storeInvoiceInfo,
					new String[] { "id", "createDate", "modifyDate" });
			storeInvoiceInfo.setStore(store);
			storeInvoiceInfos.add(storeInvoiceInfo);
		}
		store.setStoreInvoiceInfos(storeInvoiceInfos);

		storeService.save(store);

		// 可发货余额
		saveStoreBalance(store);
	}

	

	public void saveStoreBalance(Store store) {

		List<AccountParameter> accountParameters = accountParameterService.findList(1,
				null,
				null);
		AccountParameter accountParameter = accountParameters.get(0);
		String currentDate = DateUtil.convert(DateUtil.addDate("MM",
				1,
				DateUtil.convert(accountParameter.getBalanceDate(), "yyyy-MM")),
				"yyyy-MM");

		StoreBalance storeBalance = new StoreBalance();
		storeBalance.setStore(store);
		storeBalance.setBeginAmount(BigDecimal.ZERO);
		storeBalance.setBalanceDate(currentDate);
		storeBalanceService.save(storeBalance);
	}

	


	public void saveIntfAtCheck(Store store) {
		Long companyInfoId = store.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoBaseService.find(companyInfoId);
		if ("nature".equals(companyInfo.getCompany_code())) {// nadev:dzrjj
																// na/natest:nature
			// 大自然创建客户接口
			intfOrderMessageToService.saveStoreIntf(store, null, null);
		}
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findListByOrderId(Long id,Integer type) {
		return storeApplyDao.findListByOrderId(id,type);
	}

	@Override
	@Transactional(readOnly = true)
	public Integer findMaxSn() {
		return storeApplyDao.findMaxSn();
	}

	@Override
	public Integer countShopInfo(Long id) {

		return storeApplyDao.countShopInfo(id);
	}

	@Override
	public List<Map<String, Object>> findStoreContract(StoreApply storeApply) {
		return storeApplyDao.findStoreContract(storeApply);
	}

	@Override
	public List<Map<String, Object>> findStoreApplyCooperation(
			StoreApply storeApply) {
		return storeApplyDao.findStoreApplyCooperation(storeApply);
	}
	
//	@Override
//	@Transactional
//	public void createStoreWf(StoreApply storeApply, Long objConfId) {
//		if (storeApply.getWfId() != null) {
//			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
//		}
//
//		// 创建流程实例
//		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
//		if (wfObjConfigLine == null) {
//			ExceptionUtil.throwServiceException("请选择审核流程");
//		}
//		// WfTemp wfTemp =
//		// wfTempBaseService.find(wfObjConfigLine.getWfTempId());
//		storeApply.setByObjConfig(wfObjConfigLine); // 设置流程配置
//		wfBaseService.createwf(storeMemberService.getCurrent(),
//				storeApply.getSaleOrg() == null ? null
//						: storeApply.getSaleOrg().getId(),
//				null,
//				storeApply.getWfTempId(),
//				storeApply.getObjTypeId(),
//				storeApply.getId());
//
//		if (storeService.exists(Filter.eq("name", storeApply.getName()),
//				Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()))) {
//			// 客户名称已存在
//			ExceptionUtil.throwServiceException("160001");
//		}
//		storeApply.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
//		// 2.已处理(流程走完) 3.关闭
//		update(storeApply);
//
//	}
	
//	@Override
//	public void startBack(Wf wf) {
//		
//		// StoreApply storeApply = find(wf.getObjId());
//		// wf.setStore(depositRecharge.getStore());
//		// wfBaseService.update(wf);
//	}
//	
//	@Override
//	public void interruptBack(Wf wf) {
//		StoreApply storeApply = find(wf.getObjId());
//		storeApply.setDocStatus(0);// 单据状态 0.已保存(没有流程) 1.处理中(有流程)
//		// 2.已处理(流程走完) 3.关闭
//		update(storeApply);
//		
//	}
//	
//	@Override
//	public void agreeBack(Wf wf) {
//
//		if (wf.getStat().intValue() == 2) {
//			StoreApply storeApply = find(wf.getObjId());
//			storeApply.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
//			storeApply.setAccountStatus("1"); // 2.已处理(流程走完) 3.关闭
//			storeApply.setType(Type.distributor);
//			update(storeApply);
//
//			Store store = new Store();
//			// 复制到正式客户
//			BeanUtils.copyProperties(storeApply, store, new String[] { "id",
//					"createDate",
//					"modifyDate",
//					"sn",
//					"type" });
//			store.setName(storeApply.getName());
//			store.setAlias(storeApply.getAlias());
//			if (storeApply.getType() != null) {
//				if (storeApply.getType().equals(StoreApply.Type.directDealer)) {
//					store.setType(Store.Type.directDealer);
//				}
//				else if (storeApply.getType()
//						.equals(StoreApply.Type.distributor)) {
//					store.setType(Store.Type.distributor);
//				}
//				else if (storeApply.getType()
//						.equals(StoreApply.Type.enterprise)) {
//					store.setType(Store.Type.enterprise);
//				}
//				else if (storeApply.getType()
//						.equals(StoreApply.Type.offlineStore)) {
//					store.setType(Store.Type.offlineStore);
//				}
//				else if (storeApply.getType()
//						.equals(StoreApply.Type.onlineShop)) {
//					store.setType(Store.Type.onlineShop);
//				}
//				else if (storeApply.getType().equals(StoreApply.Type.provider)) {
//					store.setType(Store.Type.provider);
//				}
//				else if (storeApply.getType().equals(StoreApply.Type.reseller)) {
//					store.setType(Store.Type.reseller);
//				}
//			}
//
//			store.setIsMainStore(false);
//			store.setSn(Sequence.getInstance().getSequence(null));
//			store.setCreateBy(storeMemberService.getCurrent());
//			store.setUnJoinSucessFlag(false);
//			store.setIsEnabled(true);
//			store.setIsReduceBalance(true);
//			store.setStoreMember(storeApply.getStoreMember());
//			store.setDealerSex(storeApply.getDealerSex());
//			store.setDealerGrade(storeApply.getDealerGrade());
//			store.setShopAddress(storeApply.getShopAddress());
//			store.setCompanyType(storeApply.getCompanyType());
//			store.setDealerBackground(storeApply.getDealerBackground());
//			store.setDealerLevel(storeApply.getDealerLevel());
//			store.setSalesChannelsVal1(storeApply.getSalesChannelsVal1());
//			store.setSalesChannelsVal2(storeApply.getSalesChannelsVal2());
//			store.setSalesChannelsVal3(storeApply.getSalesChannelsVal3());
//			store.setSalesChannelsVal4(storeApply.getSalesChannelsVal4());
//			store.setSalesChannelsVal5(storeApply.getSalesChannelsVal5());
//			store.setSalesChannelsVal6(storeApply.getSalesChannelsVal6());
//			store.setPropagandaAwareness(storeApply.getPropagandaAwareness());
//			store.setBrandAwareness(storeApply.getBrandAwareness());
//			store.setAfterService(storeApply.getAfterService());
//			store.setMarketersNumber(storeApply.getMarketersNumber());
//			store.setAfterSaleNumber(storeApply.getAfterSaleNumber());
//			store.setInstallBodyNumber(storeApply.getInstallBodyNumber());
//			store.setWarehouseArea(storeApply.getWarehouseArea());
//			store.setTruck(storeApply.getTruck());
//			store.setSmallCar(storeApply.getSmallCar());
//			store.setPcOrBroadband(storeApply.getPcOrBroadband());
//			store.setJoiningFormalities1(storeApply.getJoiningFormalities1());
//			store.setJoiningFormalities2(storeApply.getJoiningFormalities2());
//			store.setGrantCode(storeApply.getDealerCoding());
//
//			// 复制经营品类
//			List<BusinessCategoryApply> bc = storeApply.getBusinessCategoryApplys();
//			List<BusinessCategory> businessCategorys = new ArrayList<BusinessCategory>();
//			for (BusinessCategoryApply businessCategoryApply : bc) {
//				BusinessCategory businessCategory = new BusinessCategory();
//				BeanUtils.copyProperties(businessCategoryApply,
//						businessCategory,
//						new String[] { "id", "createDate", "modifyDate" });
//				businessCategory.setStore(store);
//				businessCategorys.add(businessCategory);
//			}
//			store.setBusinessCategorys(businessCategorys);
//
//			// 复制经营活动记录
//			List<BusinessRecordApply> br = storeApply.getBusinessRecordApplys();
//			List<BusinessRecord> businessRecords = new ArrayList<BusinessRecord>();
//			for (BusinessRecordApply businessRecordApply : br) {
//				BusinessRecord businessRecord = new BusinessRecord();
//				BeanUtils.copyProperties(businessRecordApply,
//						businessRecord,
//						new String[] { "id", "sn", "createDate", "modifyDate" });
//				businessRecord.setSn("R"
//						+ Sequence.getInstance().getSequence(null));
//				businessRecord.setStore(store);
//				businessRecords.add(businessRecord);
//			}
//			store.setBusinessRecords(businessRecords);
//
//			// 复制保证金
//			List<CautionMoneyApply> cm = storeApply.getCautionMoneieApplys();
//			List<CautionMoney> cautionMoneys = new ArrayList<CautionMoney>();
//			for (CautionMoneyApply cautionMoneyApply : cm) {
//				CautionMoney cautionMoney = new CautionMoney();
//				BeanUtils.copyProperties(cautionMoneyApply,
//						cautionMoney,
//						new String[] { "id", "sn", "createDate", "modifyDate" });
//				cautionMoney.setSn("A"
//						+ Sequence.getInstance().getSequence(null));
//				cautionMoney.setStore(store);
//				cautionMoneys.add(cautionMoney);
//			}
//			store.setCautionMoneies(cautionMoneys);
//
//			// 复制storeSbu
//			List<StoreApplySbu> storeApplySbuList = storeApply.getStoreApplySbu();
//			List<StoreSbu> storeSbuList = new ArrayList<StoreSbu>();
//			for (StoreApplySbu storeApplySbu : storeApplySbuList) {
//				StoreSbu storeSbu = new StoreSbu();
//				BeanUtils.copyProperties(storeApplySbu,
//						storeSbu,
//						new String[] { "id",
//								"isDefault",
//								"createDate",
//								"modifyDate" });
//				storeSbu.setStore(store);
//				storeSbu.setSbu(storeApplySbu.getSbu());
//				storeSbu.setMemberRank(storeApplySbu.getMemberRank());
//				storeSbuList.add(storeSbu);
//			}
//			store.setStoreSbu(storeSbuList);
//
//			List<StoreApplyAddress> saa = storeApply.getStoreApplyAddress();
//			List<StoreAddress> storeAddressList = new ArrayList<StoreAddress>();
//			for (StoreApplyAddress storeApplyAddress : saa) {
//				StoreAddress storeAddress = new StoreAddress();
//				BeanUtils.copyProperties(storeApplyAddress,
//						storeAddress,
//						new String[] { "id", "createDate", "modifyDate" });
//				storeAddress.setStore(store);
//				storeAddressList.add(storeAddress);
//			}
//			store.setStoreAddress(storeAddressList);
//			store.setAccountStatus("1");
//
//			List<StoreApplyInvoiceInfo> saii = storeApply.getStoreApplyInvoiceInfos();
//			List<StoreInvoiceInfo> storeInvoiceInfos = new ArrayList<StoreInvoiceInfo>();
//			for (StoreApplyInvoiceInfo storeApplyInvoiceInfo : saii) {
//				StoreInvoiceInfo storeInvoiceInfo = new StoreInvoiceInfo();
//				BeanUtils.copyProperties(storeApplyInvoiceInfo,
//						storeInvoiceInfo,
//						new String[] { "id", "createDate", "modifyDate" });
//				storeInvoiceInfo.setStore(store);
//				storeInvoiceInfos.add(storeInvoiceInfo);
//			}
//			store.setStoreInvoiceInfos(storeInvoiceInfos);
//
//			storeService.save(store);
//
//			List<StoreAddress> lists = store.getStoreAddress();
//			for (StoreAddress sas : lists) {
//				sas.setOutTradeNo(sas.getId().toString());
//				storeAddressService.update(sas);
//			}
//
//			// 可发货余额
//			saveStoreBalance(store);
//			this.saveIntfAtCheck(store);
//			
//			//创建用户
//			storeMemberBaseService.createStoreMember(store);
//
//		}
//	}
	/**
	 * 创建流程实例 
	 */
	@Override
	@Transactional
	public void createStoreWf(Long id, String modelId, Long objTypeId) {
		StoreApply storeApply = find(id);
		if (storeApply.getWfId() != null) {
			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		// 创建流程实例
//		createWf(storeApply.getSn(),
//				String.valueOf(storeMember.getId()),
//				modelId,
//				objTypeId,
//				id,
//				WebUtils.getCurrentCompanyInfoId());

        createWf(storeApply.getSn(),
                String.valueOf(storeMember.getId()),
                new Long[]{storeApply.getSaleOrg().getId()},
                null,//storeId
                modelId,
                objTypeId,
                id,
                WebUtils.getCurrentCompanyInfoId(),
                true);

		if (storeService.exists(Filter.eq("name", storeApply.getName()),
				Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()))) {
			// 客户名称已存在
			ExceptionUtil.throwServiceException("160001");
		}
		storeApply.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
		// 2.已处理(流程走完) 3.关闭
		update(storeApply);
	}
	
	/**
	 * 中断流程回调
	 */
	@Override
    @Transactional
	public void interruptBack(ActWf wf){
	    super.interruptBack(wf);
		StoreApply sa = find(wf.getObjId());
		sa.setDocStatus(0);// 单据状态 0.已保存(没有流程) 1.处理中(有流程)
		// 2.已处理(流程走完) 3.关闭
		update(sa);
	}
	
	/** 流程结束回调 */
	@Override
    @Transactional
	public void endBack(ActWf wf){
	    super.endBack(wf);
		StoreApply storeApply = find(wf.getObjId());
		storeApply.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
		storeApply.setAccountStatus("1"); // 2.已处理(流程走完) 3.关闭
		storeApply.setType(Type.distributor);
		update(storeApply);

		Store store = new Store();
		// 复制到正式客户
		BeanUtils.copyProperties(storeApply, store, new String[] { "id",
				"createDate",
				"modifyDate",
				"sn",
				"type" });
		store.setName(storeApply.getName());
		store.setAlias(storeApply.getAlias());
		if (storeApply.getType() != null) {
			if (storeApply.getType().equals(StoreApply.Type.directDealer)) {
				store.setType(Store.Type.directDealer);
			}
			else if (storeApply.getType()
					.equals(StoreApply.Type.distributor)) {
				store.setType(Store.Type.distributor);
			}
			else if (storeApply.getType()
					.equals(StoreApply.Type.enterprise)) {
				store.setType(Store.Type.enterprise);
			}
			else if (storeApply.getType()
					.equals(StoreApply.Type.offlineStore)) {
				store.setType(Store.Type.offlineStore);
			}
			else if (storeApply.getType()
					.equals(StoreApply.Type.onlineShop)) {
				store.setType(Store.Type.onlineShop);
			}
			else if (storeApply.getType().equals(StoreApply.Type.provider)) {
				store.setType(Store.Type.provider);
			}
			else if (storeApply.getType().equals(StoreApply.Type.reseller)) {
				store.setType(Store.Type.reseller);
			}
		}

		store.setIsMainStore(false);
		store.setSn(Sequence.getInstance().getSequence(null));
		store.setCreateBy(storeApply.getCreateBy());
		store.setUnJoinSucessFlag(false);
		store.setIsEnabled(true);
		store.setIsReduceBalance(true);
		store.setStoreMember(storeApply.getStoreMember());
		store.setDealerSex(storeApply.getDealerSex());
		store.setDealerGrade(storeApply.getDealerGrade());
		store.setShopAddress(storeApply.getShopAddress());
		store.setCompanyType(storeApply.getCompanyType());
		store.setDealerBackground(storeApply.getDealerBackground());
		store.setDealerLevel(storeApply.getDealerLevel());
		store.setSalesChannelsVal1(storeApply.getSalesChannelsVal1());
		store.setSalesChannelsVal2(storeApply.getSalesChannelsVal2());
		store.setSalesChannelsVal3(storeApply.getSalesChannelsVal3());
		store.setSalesChannelsVal4(storeApply.getSalesChannelsVal4());
		store.setSalesChannelsVal5(storeApply.getSalesChannelsVal5());
		store.setSalesChannelsVal6(storeApply.getSalesChannelsVal6());
		store.setPropagandaAwareness(storeApply.getPropagandaAwareness());
		store.setBrandAwareness(storeApply.getBrandAwareness());
		store.setAfterService(storeApply.getAfterService());
		store.setMarketersNumber(storeApply.getMarketersNumber());
		store.setAfterSaleNumber(storeApply.getAfterSaleNumber());
		store.setInstallBodyNumber(storeApply.getInstallBodyNumber());
		store.setWarehouseArea(storeApply.getWarehouseArea());
		store.setTruck(storeApply.getTruck());
		store.setSmallCar(storeApply.getSmallCar());
		store.setPcOrBroadband(storeApply.getPcOrBroadband());
		store.setJoiningFormalities1(storeApply.getJoiningFormalities1());
		store.setJoiningFormalities2(storeApply.getJoiningFormalities2());
		store.setGrantCode(storeApply.getDealerCoding());//经销商授权编号
		//加盟日期
		if(storeApply.getActiveDate() == null){
			store.setActiveDate(new Date());
		}else {
			store.setActiveDate(storeApply.getActiveDate());//加盟日期
		}
		store.setPlatformProperty(storeApply.getPlatformProperty());
		store.setJoinFileNumber(storeApply.getAddArchivesCoding());//加盟档案编号
		store.setIsCoreStroe(storeApply.getIsCoreStroe());
		store.setDealerBackgroundMemo(storeApply.getDealerBackgroundMemo());//经销商背景备注
		store.setUnJoinSucessFlag(storeApply.getApplyResult() == 0? true:false);
		store.setPaymentStatus(storeApply.getPaymentStatus());//缴纳说明
		// 复制经营品类
		List<BusinessCategoryApply> bc = storeApply.getBusinessCategoryApplys();
		List<BusinessCategory> businessCategorys = new ArrayList<BusinessCategory>();
		for (BusinessCategoryApply businessCategoryApply : bc) {
			BusinessCategory businessCategory = new BusinessCategory();
			BeanUtils.copyProperties(businessCategoryApply,
					businessCategory,
					new String[] { "id", "createDate", "modifyDate" });
			businessCategory.setStore(store);
			businessCategorys.add(businessCategory);
		}
		store.setBusinessCategorys(businessCategorys);

		// 复制经营活动记录
		List<BusinessRecordApply> br = storeApply.getBusinessRecordApplys();
		List<BusinessRecord> businessRecords = new ArrayList<BusinessRecord>();
		for (BusinessRecordApply businessRecordApply : br) {
			BusinessRecord businessRecord = new BusinessRecord();
			BeanUtils.copyProperties(businessRecordApply,
					businessRecord,
					new String[] { "id", "sn", "createDate", "modifyDate" });
			businessRecord.setSn("R"
					+ Sequence.getInstance().getSequence(null));
			businessRecord.setStore(store);
			businessRecords.add(businessRecord);
		}
		store.setBusinessRecords(businessRecords);

		// 复制保证金
        CautionMoneyApply cma = new CautionMoneyApply();
        cma.setPayable(storeApply.getNeedCautionPaid());
        cma.setPaidIn(storeApply.getRealCautionPaid());
        cma.setUnpaid(storeApply.getUnpaidCautionPaid());
        cma.setStoreApply(storeApply);
		cma.setAccountType(2);

		List<CautionMoney> cautionMoneys = new ArrayList<CautionMoney>();
		CautionMoney cautionMoney = new CautionMoney();
		BeanUtils.copyProperties(cma,
				cautionMoney,
				new String[] { "id", "sn", "createDate", "modifyDate" });
		cautionMoney.setSn("A"
				+ Sequence.getInstance().getSequence(null));
		cautionMoney.setStore(store);
		cautionMoney.setPayable(storeApply.getNeedCautionPaid());
		cautionMoney.setPaidIn(storeApply.getRealCautionPaid());
		cautionMoney.setUnpaid(storeApply.getUnpaidCautionPaid());
		cautionMoney.setPaidNote(storeApply.getPaymentStatus());//缴纳说明
		cautionMoney.setAccountType(2);//品牌保证金账户
		cautionMoneys.add(cautionMoney);
		store.setCautionMoneies(cautionMoneys);


//		List<CautionMoneyApply> cm = storeApply.getCautionMoneieApplys();
//
//		for (CautionMoneyApply cautionMoneyApply : cm) {
//			CautionMoney cautionMoney = new CautionMoney();
//			BeanUtils.copyProperties(cautionMoneyApply,
//					cautionMoney,
//					new String[] { "id", "sn", "createDate", "modifyDate" });
//			cautionMoney.setSn("A"
//					+ Sequence.getInstance().getSequence(null));
//			cautionMoney.setStore(store);
//			cautionMoney.setPayable(storeApply.getNeedCautionPaid());
//			cautionMoney.setPaidIn(storeApply.getRealCautionPaid());
//			cautionMoney.setUnpaid(storeApply.getUnpaidCautionPaid());
//			cautionMoneys.add(cautionMoney);
//		}
//		store.setCautionMoneies(cautionMoneys);


		//复制应缴品牌保证金
		store.setNeedCautionPaid(storeApply.getNeedCautionPaid());
		//实缴品牌保证金
		store.setRealCautionPaid(storeApply.getRealCautionPaid());
		//欠缴品牌保证金
		store.setUnpaidCautionPaid(storeApply.getUnpaidCautionPaid());
		//销量保证金
		store.setSalesDeposit(storeApply.getSalesDeposit());


		// 复制storeSbu
		List<StoreApplySbu> storeApplySbuList = storeApply.getStoreApplySbu();
		List<StoreSbu> storeSbuList = new ArrayList<StoreSbu>();
		for (StoreApplySbu storeApplySbu : storeApplySbuList) {
			StoreSbu storeSbu = new StoreSbu();
			BeanUtils.copyProperties(storeApplySbu,
					storeSbu,
					new String[] { "id",
							"isDefault",
							"createDate",
							"modifyDate" });
			storeSbu.setStore(store);
			storeSbu.setSbu(storeApplySbu.getSbu());
			storeSbu.setMemberRank(storeApplySbu.getMemberRank());
			storeSbuList.add(storeSbu);
		}
		store.setStoreSbu(storeSbuList);

		List<StoreApplyAddress> saa = storeApply.getStoreApplyAddress();
		List<StoreAddress> storeAddressList = new ArrayList<StoreAddress>();
		for (StoreApplyAddress storeApplyAddress : saa) {
			StoreAddress storeAddress = new StoreAddress();
			BeanUtils.copyProperties(storeApplyAddress,
					storeAddress,
					new String[] { "id", "createDate", "modifyDate" });
			storeAddress.setStore(store);
			storeAddressList.add(storeAddress);
		}
		store.setStoreAddress(storeAddressList);
		store.setAccountStatus("1");

		List<StoreApplyInvoiceInfo> saii = storeApply.getStoreApplyInvoiceInfos();
		List<StoreInvoiceInfo> storeInvoiceInfos = new ArrayList<StoreInvoiceInfo>();
		for (StoreApplyInvoiceInfo storeApplyInvoiceInfo : saii) {
			StoreInvoiceInfo storeInvoiceInfo = new StoreInvoiceInfo();
			BeanUtils.copyProperties(storeApplyInvoiceInfo,
					storeInvoiceInfo,
					new String[] { "id", "createDate", "modifyDate" });
			storeInvoiceInfo.setStore(store);
			storeInvoiceInfos.add(storeInvoiceInfo);
		}
		store.setStoreInvoiceInfos(storeInvoiceInfos);

		storeService.save(store);

		List<StoreAddress> lists = store.getStoreAddress();
		for (StoreAddress sas : lists) {
			sas.setOutTradeNo(sas.getId().toString());
			storeAddressService.update(sas);
		}

		// 可发货余额
		saveStoreBalance(store);
		this.saveIntfAtCheck(store);
		
		//创建用户
//		storeMemberBaseService.createStoreMember(store);
	}
	
	/**
	 * 设置流程分支参数 
	 */
	public Map<String, Object> setFormData(ActWf wf, String taskId) {
		Map<String,Object> dayMap=new HashMap<String, Object>();
		dayMap.put("isCore", this.find(wf.getObjId()).getIsCoreStroe());
		dayMap.put("sbuId", this.find(wf.getObjId()).getSbu().getId());
		return dayMap;
	}
	
//	/**
//	 * 提交流程任务
//	 */
//	public void submit(String taskId, String userid, String comment,
//			Map<String, Object> formData){
//		try {
//			Task task = findTaskById(taskId);
//			if(task.getName().contains("省长")){
//				
//			}
//			
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		
//	}
	
	protected TaskEntity findTaskById(String taskId) throws Exception {

		TaskEntity task = (TaskEntity) taskService.createTaskQuery().taskId(

				taskId).singleResult();

		if (task == null) {

			throw new Exception("任务实例未找到!");

		}

		return task;

	}
	

	@Override
	public void saveform(StoreApply storeApply, Integer Type) {
		StoreApply sa = find(storeApply.getId());
        wfPass(storeApply,"",Type);
		if(Type==1){
			sa.setGovernorOpinion(storeApply.getGovernorOpinion());
		}
		if(Type==2){
			sa.setIsCoreStroe(storeApply.getIsCoreStroe());
			sa.setDealerCoding(storeApply.getDealerCoding());	
			sa.setAddArchivesCoding(storeApply.getAddArchivesCoding());
			sa.setAddTime(storeApply.getAddTime());
			sa.setActiveDate(storeApply.getActiveDate());
			sa.setDealerCaseNote(storeApply.getDealerCaseNote());
			sa.setApplyResult(storeApply.getApplyResult());
		}
		update(sa);
	}

    @Override
	public void agreePre(ActWf wf) {
		StoreApply storeApply = find(wf.getObjId());
		Task t = this.getCurrTaskByWf(wf);
		String nodeName = t.getName();
        wfPass(storeApply,nodeName,0);
	}

    /**
     * 必填校验
     */
    public void wfPass(StoreApply storeApply, String nodeName,Integer type) {
        if(nodeName.contains("省长")||type == 1){
            if(ConvertUtil.isEmpty(storeApply.getGovernorOpinion())){
                ExceptionUtil.throwServiceException("请填写省长意见");
            }
        }
        if(nodeName.contains("渠道专员")|| type == 2){
            if(storeApply.getIsCoreStroe()==null){
                ExceptionUtil.throwServiceException("请填写和提交是否核心经销商");
            }
        }
    }
}
