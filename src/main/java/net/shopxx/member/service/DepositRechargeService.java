package net.shopxx.member.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.aftersales.b2b.entity.B2bReturns;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.order.entity.PriceApply;
import net.shopxx.wf.service.WfBillBaseService;

public interface DepositRechargeService extends
        ActWfBillService<DepositRecharge> {

	/**
	 * 余额充值审核
	 * @param depositRecharge
	 * @param flag
	 * @param note
	 * @return
	 */
	public void check(DepositRecharge depositRecharge, Integer flag,
			String note, BigDecimal actualAmount);

//	/**
//	 * 流程审批（旧）
//	 * @param depositRecharge
//	 * @param note
//	 * @param actualAmount
//	 * @param objConfId
//	 */
//	public void checkStoreRechargeWf(DepositRecharge depositRecharge,
//			String note, BigDecimal actualAmount, Long objConfId);

	/**
	 * 用户、客户余额充值列表数据
	 */
	public Page<Map<String, Object>> findPage(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			Pageable pageable);

	public void update(Store store, DepositRecharge depositRecharge,
			Integer fullLinkType, Integer isSubmit);

	/**
	 * 作废（旧）
	 * @param depositRecharge
	 * @param fullLinkType
	 */
	public void cancel(DepositRecharge depositRecharge, Integer fullLinkType);

	public void save(Store store, DepositRecharge depositRecharge,
			Integer fullLinkType, Long rechargeTypeId);

	/**
	 * 销售回款列表数据
	 */
	public Page<Map<String, Object>> newfindPage(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			String firstTime, String lastTime, Long organizationId,Long sbuId,
			String sourceSn,String adjustmentSn,Long[] bankCardId,Long[] bcOrganizationId,
			Long[] bcSbuId,Pageable pageable);

	/**
	 * 销售回款列表数据导出数量
	 */
	public Integer countDepositRecharge(Integer[] type, String sn,Long storeMemberId, Long storeId, 
			Long saleOrgId, Integer[] status,Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,Long sbuId,String firstTime, String lastTime, 
			Long[] ids, Long organizationId,Pageable pageable,Integer page, Integer size, String sourceSn,
			String adjustmentSn,Long[] bankCardId,Long[] bcOrganizationId,Long[] bcSbuId);

	/**
	 * 销售回款列表数据导出
	 */
	public List<Map<String, Object>> findDepositRechargeList(Integer[] type,String sn, Long storeMemberId, 
			Long storeId, Long saleOrgId,Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, BigDecimal minPrice,BigDecimal maxPrice,Long sbuId, String firstTime,
			String lastTime, Long[] ids,Long organizationId,Pageable pageable, Integer page, Integer size,
			String sourceSn,String adjustmentSn,Long[] bankCardId,Long[] bcOrganizationId,Long[] bcSbuId);
	
	public Page<Map<String, Object>> findShowPage(Long storeId, String sn, Pageable page);
	
	public List<Map<String, Object>> findList(Long storeId, String sn);

	/** 创建流程实例 */
	void createWf(Long id, String modelId, Long objTypeId, DepositRecharge depositRecharge, String note, BigDecimal actualAmount);

//	/**
//	 * 审核
//	 * @param depositRecharge
//	 */
//	void check(DepositRecharge depositRecharge);



	public void saveform(DepositRecharge depositRecharge, Integer Type);
	
	
	/**
	 * 经销商充值保存
	 * @param depositRecharge
	 */
	public void saveDepositRecharge(DepositRecharge depositRecharge);
	
	
	/**
	 * 经销商充值收款账号列表
	 * @param id
	 * @return
	 */
	public List<Map<String, Object>> findDistributorRechargeItemList(String ids);
	
	
	/**
	 * 经销商充值更新
	 * @param depositRecharge
	 */
	public void updateDepositRecharge(DepositRecharge depositRecharge);
	
	
	/**
	 *经销商充值列表数据
	 */
	public Page<Map<String, Object>> newfindDepositRechargePage(Integer[] type,
			 Long[] rechargeTypeId,Long[] sbuId,String sn,Integer[] docstatus,
			 Long[] wfStates,Long[] storeId,Long[]  saleOrgId,Long[] bankCardId,
			 Long[] organizationId,String glDateStartTime,String glDateEndTime,
			 Long[] creatorId,String sourceSn,Integer page,
			 Integer size,Long[] ids,Pageable pageable);
	
	
	
	
	/**
	 * 经销商充值审批流程更新
	 * @param depositRecharge
	 */
	public void checkWfUpdateDepositRecharge(DepositRecharge depositRecharge);
	
	
	
	 /**
	  *  经销商充值审核	
	  * @param depositRecharge
	  * @param flag
	  * @param note
	  * @param actualAmount
	  */
	 public void checkDepositRecharge(DepositRecharge depositRecharge, 
			 Integer flag, String note, BigDecimal actualAmount);
	
	
	
	/**
	 * 经销商充值创建新流程
	 * @param id
	 * @param modelId
	 * @param objTypeId
	 * @param depositRecharge
	 * @param note
	 * @param actualAmount
	 */
	public void createWfDepositRecharge(Long id, String modelId, Long objTypeId, 
			DepositRecharge depositRecharge, String note, BigDecimal actualAmount);
	
	
	/**
	 * 获取充值类型Ids
	 * @param flag
	 * @return
	 */
	public String getRechargeTypeIds(Integer flag);
	
	
	
	/**
	 * 校验充值单状态
	 * @param depositRecharge
	 * @param status
	 * @param statusName
	 * @param operationName
	 */
	public void checkDepositRechargeStatus(DepositRecharge depositRecharge, Integer status, 
			String statusName, String operationName);
	
	
	
	
}