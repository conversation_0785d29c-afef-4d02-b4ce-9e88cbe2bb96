/*
 * 
 */
package net.shopxx.member.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.StoreMember;

/**
 * Service - 会员
 */
public interface LoginOutBaseService extends BaseService<StoreMember> {

	/**
	 * 提交登录
	 */
	public void submitLogin(String username, String password,
			String companyName, String language, HttpServletRequest request,
			HttpServletResponse response, HttpSession session,Integer flag);

	/**
	 * 单点登录
	 */
	public String SSO(String company, String loginguid, String language,
			HttpServletRequest request);
	
	/**
	 * 单点登录  (oauth)
	 */
	public String oauth( String userInfo, String language,
			HttpServletRequest request);

	/**
	 * 退出
	 */
	public String logout(HttpServletRequest request,
			HttpServletResponse response);
	
	public List<StoreMember> findStoreMember(String username);
}