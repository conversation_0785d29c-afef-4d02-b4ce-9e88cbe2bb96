package net.shopxx.member.service;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.member.entity.StoreStop;

import java.util.List;
import java.util.Map;

public interface StoreStopService extends ActWfBillService<StoreStop> {

    void saveStoreStop(StoreStop storeStop);

    Page<Map<String, Object>> findPage(StoreStop ss, List<Object> param, Pageable pageable);

    void updateStoreStop(StoreStop storeStop, Long sid);

    void saveform(Integer type, StoreStop storeStop);

    /** 创建流程实例 */
    void createWf(Long id, String modelId, Long objTypeId);

	/**
     * 查询导出数量
     */
    Integer count(StoreStop ss, List<Object> param);

    /**
     * 导出查询
     */
	List<Map<String, Object>> findList(StoreStop ss, List<Object> param, Pageable pageable, Integer page, Integer size);

}
