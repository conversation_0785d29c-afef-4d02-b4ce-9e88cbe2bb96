package net.shopxx.member.service;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.member.entity.FleeingGoods;

import java.util.List;
import java.util.Map;

public interface FleeingGoodsService extends ActWfBillService<FleeingGoods> {

    

    Page<Map<String, Object>> findSelectPage(FleeingGoods fg, List<Object> param, Pageable pageable);

	List<Map<String, Object>> findFleeingGoodsAttach(Long id, Integer type);

	void updateFleeingGoods(FleeingGoods fleeingGoods);

	void saveFleeingGoods(FleeingGoods fleeingGoods);

	void saveform(Integer type, FleeingGoods fleepingGoods);

	/** 创建流程实例 */
	void createWf(Long id, String modelId, Long objTypeId);

	/**
     * 查询导出数量
     */
    Integer count(FleeingGoods fg, List<Object> param);

    /**
     * 导出查询
     */
	List<Map<String, Object>> findList(FleeingGoods fg, List<Object> param, Pageable pageable, Integer page, Integer size);

}
