package net.shopxx.member.service;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.member.entity.StoreExit;
import net.shopxx.shop.entity.ShopInfo;

import java.util.List;
import java.util.Map;

public interface StoreExitService extends ActWfBillService<StoreExit> {

    void saveStoreExit(StoreExit storeExit, Long sid);

    Page<Map<String, Object>> findPage(StoreExit se, List<Object> param, Pageable pageable);

    void updateStoreExit(StoreExit se, Long sid, Integer[] dealerContacts);

    void saveform(Integer type, StoreExit storeExit);

    /** 创建流程实例 */
    void createWf(Long id, String modelId, Long objTypeId);

    List<Map<String, Object>> findStoreExitAttach(Long id, Integer type);

    /**
     * 查询客户退出门店资料明细
     * @param id 客户退出ID
     * @return
     */
    List<Map<String, Object>> findStoreExitShopInfo(Long id);

    /**
     * 查询需要关闭的门店
     * @param storeExit 客户退出
     * @return
     */
    List<ShopInfo> findAllCloseShopList(StoreExit storeExit);

    /**
     * 查询需要退出的门店
     * @param storeExit 客户退出
     * @return
     */
    List<ShopInfo> findAllExitShopList(StoreExit storeExit);


    Integer count(StoreExit se, List<Object> param);

	List<Map<String, Object>> findList(StoreExit se, List<Object> param, Pageable pageable, Integer page, Integer size);

    
}
