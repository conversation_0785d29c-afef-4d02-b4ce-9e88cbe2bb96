package net.shopxx.member.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreApply;
import net.shopxx.member.entity.StoreApply.Type;

public interface StoreApplyService extends ActWfBillService<StoreApply> {

	List<Integer> getTypes();

	Page<Map<String, Object>> findPage(String name, String outTradeNo, String alias, Integer[] type, Long companyInfoId,
			Long memberRankId, Long saleOrgId, Pageable pageable);

	void save(StoreApply storeApply, Long storeMemberId, Long saleOrgId, Long memberRankId, Long salesPlatformId,
			Long businessTypeId, Long areaId, String saleType, Long storeApplySalesAreaId,Long sbuId,Long distributorStatusId);

	public void update(StoreApply storeApply, Long storeMemberId, Long areaId, Long saleOrgId, Long memberRankId,
			Long salesPlatformId, Long businessTypeId, BankCard bankCard, Long bAreaId, String bAddress, String bMobile,
			Boolean bIsEnabled, Long changeMemberRankId, Long sbuId, Long storeApplySalesAreaId, Long createById,Long distributorStatusId);

	// void update(StoreApply storeApply, Long saleOrgId, Long memberRankId);

	String storeImport(MultipartFile file) throws Exception;

	String storeImport1(MultipartFile file) throws Exception;

	void saveStoreApply(String name, String outTradeNo, String alias, Type type, BigDecimal taxRate,
			MemberRank memberRank, SaleOrg saleOrg, boolean isEnabled, boolean isMainStore);

	public void createStore(Long id);

	public void createStoreWf(Long id, String modelId, Long objTypeId);

	/**
	 * 收货地址查询
	 * 
	 * @param mobile
	 * @param consignee
	 * @param storeId
	 * @param address
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findStoreApplyAddressPage(String mobile, String consignee, Long storeId,
			String address, Pageable pageable);

	public List<Map<String, Object>> findBusinessCategoryApply(StoreApply storeApply);

	public List<Map<String, Object>> findBusinessRecordApply(StoreApply storeApply);

	public List<Map<String, Object>> findCautionMoneyApply(StoreApply storeApply);

	public List<Map<String, Object>> findInvoiceInfoListByStoreApply(Long storeId);

	/**
	 * 根据id查找对应的附件信息
	 */
	public List<Map<String, Object>> findListByOrderId(Long id,Integer type);

	/**
	 * 查找外部编码最大流水号
	 * 
	 * @return
	 */
	public Integer findMaxSn();

	public void saveIntfAtCheck(Store store);

	Integer countShopInfo(Long id);

	List<Map<String, Object>> findStoreContract(StoreApply storeApply);
	
	//获取合作单位
	List<Map<String, Object>> findStoreApplyCooperation(StoreApply storeApply);
	
	void saveform(StoreApply storeApply, Integer Type);
}