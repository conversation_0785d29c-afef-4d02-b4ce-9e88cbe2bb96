package net.shopxx.member.service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.Post;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrgPost;

import java.util.List;
import java.util.Map;

public interface StoreMemberSaleOrgPostService extends BaseService<StoreMemberSaleOrgPost> {
    /**
     * 根据登陆用户检查用户是否具有某个岗位
     */
    Boolean checkPostByStoreMember (StoreMember storeMember,String postNames);
    
    /**
     * 登录用户和岗位编码查找对应岗位
     * @param storeMember 当前登录用户
     * @param postCode 岗位编码
     * @return Boolean
     */
    Boolean  checkPostCodeByStoreMember (StoreMember storeMember,String postCode);

    /**
     * 通过 机构和岗位编码 查找对应用户
     * @param saleOrgId 机构id
     * @param postCode 岗位编码
     * @return 用户集合
     */
    List<StoreMember> findPostCodeByStoreMember (Long saleOrgId, String postCode);

    /**
     * 分页查找岗位数据
     * @param post
     * @param saleOrgId
     * @param pageable
     * @return
     */
    Page<Map<String, Object>> findPageByPost(Post post, Long saleOrgId, Pageable pageable);

    /**
     * 岗位编码查找用户
     * @param postCode 岗位编码
     * @param name 用户名称
     * @return
     */
    Page<Map<String,Object>> findOrderManager(String[] postCode,String name,Pageable pageable);

    Page<Map<String,Object>> findStoreMemberByPost(String[] postcode, String name, SaleOrg saleOrg, Pageable pageable);

}