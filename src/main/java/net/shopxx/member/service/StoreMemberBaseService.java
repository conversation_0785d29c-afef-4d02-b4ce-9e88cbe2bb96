/*
 * 
 */
package net.shopxx.member.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * Service - 会员
 */
public interface StoreMemberBaseService extends BaseService<StoreMember> {

	public StoreMember getCurrent();

	/**
	 * 获取默认用户
	 * 
	 * @param username
	 * @param companyInfoId
	 * @return
	 */
	public List<StoreMember> findDefaultByUsername(String username, Long companyInfoId);
	
	/**
	 * 获取用户
	 * 
	 * @param username
	 * @param companyInfoId
	 * @return
	 */
	public StoreMember findByUsername(String username, Long companyInfoId);

	/**
	 * 获取默认用户
	 * 
	 * @param member
	 * @return
	 */
	public StoreMember findDefaultByMember(Member member);

    /**
     * 用客户id禁用 客户关联的账号
     * @param storeId
     * @return
     */
    public void closeStoreByMember(Long storeId);

    /**
     * 用客户id查询客户关联的外部用户
     * @param storeId
     * @return
     */
    public List<StoreMember> findStoreByMember(Long storeId);

	/**
	 * 获取用户客户关系
	 * 
	 * @param
	 * @return
	 */
	public List<StoreMember> findNotDefaultByMember(Member member);

	/**
	 * 根据企业id查找用户
	 * 
	 * @param member
	 * @param companyInfoId
	 * @return
	 */
	public StoreMember findByMemberAndCompanyInfoId(Member member, Long companyInfoId);

	/**
	 * 生成唯一、随机编码
	 * 
	 * @return
	 */
	public String generateCode();

	/**
	 * 保存用户
	 * 
	 * @param storeMember
	 * @param member
	 * @param storeId
	 * @param roleId
	 * @param saleOrgId
	 * @param request
	 */
	public Long saveStoreMember(StoreMember storeMember, Member member, Long[] storeId, Long[] roleId, Long[] saleOrgId,
			String[] postId, Integer[] isDefault, Long[] warehouseId, Long[] sbuId,Integer[] isDefaults,Boolean isAdministrator,
			Long[] shopId,String[] postIds,HttpServletRequest request,Long[] organizationId,Integer[] isDeFaults);

	/**
	 * 更新用户
	 * 
	 * @param storeMember
	 * @param member
	 * @param storeId
	 * @param roleId
	 * @param saleOrgId
	 * @param request
	 */
	public void updateStoreMember(StoreMember storeMember, Member member, Long[] storeId, Long[] roleId,
			Long[] saleOrgId, String[] postId, Integer[] isDefault, Long[] warehouseId, Boolean isAdministrator,
			Long pc_id, Long parentId, Long[] sbuId,Integer[] isDefaults,Long[] shopId,String[] postIds,
			HttpServletRequest request,Long[] organizationId,Integer[] isDeFaults);

	/**
	 * 查找用户分页数据
	 * 
	 * @return
	 */
	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args);

	/**
	 * excel导入用户
	 * 
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> storeMemberImport(MultipartFile multipartFile, Integer memberType) throws Exception;

	/**
	 * 更新用户信息
	 * 
	 * @param storeMember
	 * @param member
	 */
	public void updateInfo(StoreMember storeMember, Member member);

	public String storeAuth();

	public String warehouseAuth();

	public Integer count(String username, String mobile, String name, Long memberRankId, Boolean isPartner,
			Long storeId, Long saleOrgId, String startTime, String endTime, Long[] ids);

	public List<Map<String, Object>> findList(String username, String mobile, String name, Long memberRankId,
			Boolean isPartner, Long storeId, Long saleOrgId, String startTime, String endTime, Integer memberType,
			Long[] ids, Integer page, Integer size);

	public boolean storeMemberExists(Long memberId, Long storeMemberId);
	
	public boolean storeMemberExists(Long memberId, Long storeMemberId,String username, String oldMobile,String oldIdCard,String newMobile,String newIdCard);
	
	public boolean idCardExists(String idCard,Long storeMemberId);

	public List<Map<String, Object>> findStoreMemberByMember(Long memberId);

	public Map<String, Object> findListByStore(Long storeId);

	public int intfSaveStoreMember(Map<String, Object> list, String username);
	
	public List<Map<String, Object>> findSbu(
			Long id);
	
	
	public List<Map<String, Object>> findShop(
			Long id);
	
	public List<Map<String, Object>> findSbuTy(
			Long id);
	
	void createStoreMember(Store store);
	
	public List<Map<String, Object>> findListByShop(Long storeMemberId,
			String shopIds);
	
	public void saveUserRole(Long[] roleId, Member member,
			StoreMember storeMember, Boolean isAdministrator);
	
	public void saveSelectedStore(Long[] storeId, StoreMember storeMember,
			Member member);
	
	public void saveSmShop(Long[] shopId,
			StoreMember storeMember);
	
	/** 经销商查帐号客户id*/
	Long findStoreId(Long storeMemberId);
	
	/**
	 * 保存用户经营组织
	 * @param id
	 * @return
	 */
	public List<Map<String, Object>> findOrganization(Long id);
	
	/**
	 * 大区经理查下属业务员 
	 */
	public String findSalesman(Long id);
	
	public void saveSalesmanStore(Long[] storeId, StoreMember storeMember,
			Member member);
	
	/**
	 * 查找当前登录用户在小程序的角色 （link四期）
	 */
	public Map<String, Object> findAppStoreMember(Long id);
	
	
	
	
	
	public String storeMemberSync(Map<String, Object> map);

	/**
	 * 判断用户是否具有某些角色中的一个
	 * @param storeMember 用户
	 * @param roleName 角色名
	 * */
	Boolean hasRoles(StoreMember storeMember, String... roleName);
	
}