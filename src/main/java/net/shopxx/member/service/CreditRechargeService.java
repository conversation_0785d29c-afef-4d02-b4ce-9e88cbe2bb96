package net.shopxx.member.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.member.entity.CreditRecharge;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.wf.service.WfBillBaseService;

public interface CreditRechargeService
		extends ActWfBillService<CreditRecharge> {

	/**
	 * 用户、客户余额充值列表数据
	 */
	public Page<Map<String, Object>> findPage(Integer type, String sn,
			Long storeMemberId, Long storeId, Integer[] status, Long creatorId,
			Long operatorId, Integer rechargeType,Long[] organizationId,Long sbuId,
			Long saleOrgId,String storeMemberName,String startFirstTime,
			String startLastTime,String endFirstTime,String endLastTime, Pageable pageable);
	
	/**
	 * 用户、客户余额充值列表数据导出
	 */
	public List<Map<String, Object>> findTable(Integer[] status,Long storeId,Long organizationId,Long sbuId);

	public void check(CreditRecharge creditRecharge, Integer flag, String note,
			BigDecimal actualAmount);

	public void effective(CreditRecharge creditRecharge);

	public void invalid(CreditRecharge creditRecharge);

	public Page<Map<String, Object>> findShowPage(Long storeId, String sn,
			Integer rechargeType, Pageable pageable);

	public List<Map<String, Object>> findList(Long storeId, String sn);

	public List<Map<String, Object>> findCreditRechargeList(Integer[] type,
			String sn, Long storeMemberId, Long storeId, Long saleOrgId,
			Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, BigDecimal minPrice,
			BigDecimal maxPrice, String firstTime, String lastTime,Long sbuId, Long[] ids,
			Pageable pageable, Integer page, Integer size);

	public Integer countCreditRecharge(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,Long sbuId,
			String firstTime, String lastTime, Long[] ids, Pageable pageable,
			Integer page, Integer size);

	public List<Map<String, Object>> findIsHaveSameStroe(Long storeId,
			Long storeMemberId, String startDate, Integer rechargeType,Long organizationId);

	public boolean findJurisdiction(StoreMember storeMember);
	
	public List<Map<String,Object>> findSbu(Long id);

    /**
     * 流程启动（新）
     * @param id
     * @param modelId
     * @param objTypeId
     */
    void createWf(Long id, String modelId, Long objTypeId, CreditRecharge creditRecharge, 
    		Integer flag, String note, BigDecimal actualAmount);
    
    
	/**
	 * 校验授信单状态
	 * @param creditRecharge
	 * @param status
	 * @param statusName
	 * @param operationName
	 */
	public void checkCreditRechargeStatus(CreditRecharge creditRecharge, Integer status, 
			String statusName, String operationName);
    
    
}