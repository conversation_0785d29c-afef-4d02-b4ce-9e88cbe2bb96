package net.shopxx.member.service;

import java.util.List;
import java.util.Map;

import net.shopxx.act.entity.ActWf;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.basic.entity.Area;
import net.shopxx.member.entity.*;

import org.springframework.web.multipart.MultipartFile;

/**
 * Service - 门店
 */
public interface StoreBaseService extends BaseService<Store> {

	/**
	 * 获取默认客户
	 * 
	 * @return
	 */
	public Store getMainStore();

	/**
	 * 根据企业获取企业默认客户
	 * 
	 * @param companyInfoId
	 * @return
	 */
	public Store getMainStore(Long companyInfoId);

	/**
	 * 保存客户
	 * 
	 * @param store
	 * @param areaId
	 * @param saleOrgId
	 * @param memberRankId
	 * @param bankCard
	 * @param bAreaId
	 * @param bAddress
	 * @param storeSalesAreaId 
	 */
	public void save(Store store, Long storeMemberId, Long areaId,
			Long saleOrgId, Long memberRankId, Long salesPlatformId,
			Long businessTypeId, Long distributorStatusId, BankCard bankCard,
			Long bAreaId, String bAddress, String bMobile,
			Long changeMemberRankId, Long salesAreaId, Long storeSalesAreaId);

	/**
	 * 更新客户
	 * 
	 * @param store
	 * @param areaId
	 * @param saleOrgId
	 * @param memberRankId
	 * @param bankCard
	 * @param bId
	 * @param bAreaId
	 * @param bAddress
	 * @param bMobile
	 * @param bIsEnabled
	 * @param storeSalesAreaId 
	 * @param createById 
	 * @param storeIdLink5
	 */
	public void update(Store store, Long storeMemberId, Long areaId,
			Long saleOrgId, Long memberRankId, Long salesPlatformId,
			Long businessTypeId, Long distributorStatusId, BankCard bankCard,
			Long bAreaId, String bAddress, String bMobile, Boolean bIsEnabled,
			Long changeMemberRankId, Long salesAreaId, Long storeSalesAreaId,
			Long createById,Long storeIdLink5);

	/**
	 * 客户excel导入大自然
	 * 
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public String storeImport(MultipartFile multipartFile) throws Exception;

	/**
	 * 客户excel导入
	 * 
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public String storeImportKb(MultipartFile multipartFile) throws Exception;

	public Page<Map<String, Object>> findPage(String name, String sn,
			String outTradeNo, String mobile,String serviceTelephone,
			Integer[] type, String outShopName,Long saleOrgId,
			Long companyInfoId, Long memberRankId,String createBy,
			String saleOrgName, Boolean isEnabled,Integer isSelect,
			Integer isCustomer, String alias,String grantCode,
			Long storeMemberId,String region,Long[] distributorStatusId,
			String headAddress,String user,Pageable pageable,Long sbuId);

	public Page<Map<String, Object>> findStorePageWithBalance(String name,
			String outTradeNo,String grantCode, Pageable pageable);

	/** 获取客 户类型 */
	public List<Integer> getTypes();

	public Integer count(String name, String sn, String outTradeNo,
			String mobile, String serviceTelephone, Integer[] type,
			String outShopName, Long saleOrgId, Long companyInfoId,
			Long memberRankId, Boolean isEnabled, Pageable pageable,
			Integer page, Integer size);

	/**
	 * 导出查询
	 * 
	 * @param name
	 * @param sn
	 * @param outTradeNo
	 * @param mobile
	 * @param serviceTelephone
	 * @param type
	 * @param outShopName
	 * @param saleOrgId
	 * @param companyInfoId
	 * @param memberRankId
	 * @param isEnabled
	 * @param pageable
	 * @param page
	 * @param size
	 * @return
	 */
	public List<Map<String, Object>> findItemList(String name,String sn, 
			String outTradeNo,String mobile,String serviceTelephone,
			Integer[] type,String outShopName,Long saleOrgId,Long companyInfoId,
			Long memberRankId,Boolean isEnabled,Long[] ids,Integer page,Integer size,
			Long storeMemberId,String region,Long[] distributorStatusId,String headAddress);

	/**
	 * 收货地址查询
	 * 
	 * @param mobile
	 * @param consignee
	 * @param storeId
	 * @param address
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findStoreAddressPage(String mobile,
			String consignee, Long storeId, String address,String areaName, Pageable pageable);

	public Store findStoreByOutTradeNo(String outTradeNo, Long companyInfoId);

	public List<Map<String, Object>> findListByMember(Long memberId);

	public List<Map<String, Object>> findBusinessCategory(Store store);

	public List<Map<String, Object>> findBusinessRecord(Store store);

	public List<Map<String, Object>> findCautionMoney(Store store);

	public List<Map<String, Object>> findStoreContract(Store store);

	/**
	 * 根据id查找对应的附件信息
	 */
	public List<Map<String, Object>> findListByStoreId(Long id);

	/**
	 * 查找外部编码最大流水号
	 * 
	 * @return
	 */
	public Integer findMaxSn();

	public List<Map<String, Object>> findStoreAddressList(Long storeId);

	public void storeAddressIntf(Long storeId, Integer flag, Long areaId,
			Long salesAreaId, StoreAddress storeAddress);

	//合作单位
	public List<Map<String, Object>> findStoreCooperation(Store store);

	/**
	 * 客户地址excel导入大自然
	 *
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public String addressImport(MultipartFile multipartFile) throws Exception;

	/**
	 * 客户&客户地址一键同步
	 * */
	void synchronization(String startTime, String endTime, String currentTime);

	/**
	 * 发票抬头excel导入大自然
	 *
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public String invoiceImport(MultipartFile multipartFile) throws Exception;

	/**
	 * 给管理员权限
	 * @param storemember
	 * @param roleName
	 * @return
	 */
	public boolean findByRole(StoreMember storemember, String roleName);
	
	/**
	 * 查客户省市
	 * @param area
	 * @return
	 */
	public String findAreaCity(Area area);

    public List<Map<String, Object>> findMobileData(Map<String, Object> param);


    /** 客户附件*/
    public List<Map<String, Object>> findStoreAttach(Long id, Integer type);

	/**
	 * 客户终止调用
	 *
	 */
	public void storeStop(Store store, ActWf wf);

	/** 客户信息变更-财务*/
	void updateStoreFinance(Store store);

	/** 客户信息变更*/
	void updateStore1(Store store);

	/**
	 * 客户退出调用
	 *
	 */
	public void storeExit(Store store, StoreExit storeExit, ActWf wf);
}
