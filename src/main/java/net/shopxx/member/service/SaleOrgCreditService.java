package net.shopxx.member.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.SaleOrgCredit;
import net.shopxx.wf.service.WfBillBaseService;

public interface SaleOrgCreditService extends WfBillBaseService<SaleOrgCredit> {
	public Page<Map<String, Object>> findPage(Integer type, String sn,
			Long storeMemberId, Long storeId, Integer[] status, Long creatorId,
			Long operatorId, Long sbuId, Pageable pageable);

	public void check(SaleOrgCredit SaleOrgCredit, Integer flag, String note,
			BigDecimal actualAmount);

	public void checkSaleOrgCreditWf(SaleOrgCredit SaleOrgCredit, Integer flag,
			String note, BigDecimal actualAmount, Long objConfId);

	public List<Map<String, Object>> findListBySaleOrg(Long saleOrgId);

	public List<Map<String, Object>> findScItemList(Long saleOrgId,
			String saleOrgCreditIds, Boolean isDefault);

	public void saveOrUpdateSaleOrgCredit(SaleOrgCredit saleOrgCredit);

	public List<Map<String, Object>> checkScItemList(Long saleOrgId,
			Long saleOrgCreditId);
	
	public List<Map<String, Object>> findVSaleOrgCreditList(Long saleOrgId);
	
	public List<Map<String, Object>> findVSaleOrgCreditCheckList(Long saleOrgId,
			Long sbuId,Long organizationId);
	
	/**
	 * 校验平台授信单状态
	 * @param saleOrgCredit
	 * @param status
	 * @param statusName
	 * @param operationName
	 */
	public void checkSaleOrgCreditStatus(SaleOrgCredit saleOrgCredit, Integer status,
			String statusName, String operationName);
}