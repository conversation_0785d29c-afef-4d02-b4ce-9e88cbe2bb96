/*
 * Copyright 2005-2013 shopxx.net. All rights reserved. Support:
 * http://www.shopxx.net License: http://www.shopxx.net/license
 */
package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.PcMenu;

import org.springframework.stereotype.Repository;

/**
 * Dao - PC菜单
 */
@Repository("pcMenuBaseDao")
public class PcMenuBaseDao extends DaoCenter {

	/**
	 * 根据用户和企业查找菜单
	 * @param companyInfoId
	 * @return
	 */
	public List<PcMenu> findMenuList(String storeMemberId, Long companyInfoId,
			Integer type) {

		if (ConvertUtil.isEmpty(storeMemberId)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}
		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("select distinct m.id,m.company_info_id,m.b_creater,m.b_modifier,m.create_date,m.modify_date,");
		sql.append("m.orders,m.is_cat,m.is_enabled,m.menu_img,m.super_id,m.grade,m.is_leaf,m.type,");
		//sql.append("ifnull(mc.url,m.url) url,ifnull(mc.menu_code,m.menu_code) menu_code,ifnull(mc.menu_name,m.menu_name) menu_name,m.d_templates,m.jump_path  ");
		sql.append(" m.url url,m.menu_code menu_code,m.menu_name menu_name,m.d_templates,m.jump_path,m.number  ");
        sql.append(" from xx_pc_menu m");
		sql.append(" left join xx_pc_menu_role mr on m.id = mr.pc_menus");
		sql.append(" left join xx_pc_role r on mr.pc_roles = r.id");
		sql.append(" left join xx_pc_user_role ur on r.id = ur.pc_role");
		sql.append(" left join xx_pc_menu_custom mc on m.id = mc.pc_menu_id");
		if (companyInfoId != null) {
			sql.append(" and mc.company_info_id = ?");
			list.add(companyInfoId);
		}
		else {
			sql.append(" and mc.company_info_id is null");
		}
		sql.append(" where ur.store_member = ?");

		list.add(storeMemberId);
		if (companyInfoId != null) {
			sql.append(" and ur.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (type != null) {
			sql.append(" and m.type = ?");
			list.add(type);
		}
		sql.append(" order by m.orders");

		//System.out.println("sql1111："+sql.toString());
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<PcMenu> menus = getNativeDao().findListManaged(sql.toString(),
				objs,
				0,
				PcMenu.class);

		return menus;
	}

	public Integer findMenuCount(String storeMemberId, Long companyInfoId,
			Integer type, String code) {

		if (ConvertUtil.isEmpty(storeMemberId)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}
		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("select count(m.id)");
		sql.append(" from xx_pc_menu m, xx_pc_menu_role mr, xx_pc_role r, xx_pc_user_role ur,xx_pc_menu_custom mc");
		sql.append(" where m.id = mr.pc_menus and mr.pc_roles = r.id and r.id = ur.pc_role and m.id = mc.pc_menu_id and ur.store_member = ? and m.menu_code = ?");

		list.add(storeMemberId);
		list.add(code);
		if (companyInfoId != null) {
			sql.append(" and ur.company_info_id = ?");
			sql.append(" and mc.company_info_id = ?");
			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		else {
			sql.append(" and mc.company_info_id is null");
		}
		if (type != null) {
			sql.append(" and m.type = ?");
			list.add(type);
		}
		sql.append(" order by m.orders");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}



	/**
	 * 查询所有菜单
	 * @param
	 * @return
	 */
	public List<PcMenu> findAllMenuList(Integer type) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("select distinct m.id,m.company_info_id,m.b_creater,m.b_modifier,m.create_date,m.modify_date,");
		sql.append("m.orders,m.is_cat,m.is_enabled,m.menu_img,m.super_id,m.grade,m.is_leaf,m.type,");
		sql.append("m.url,m.menu_code,m.menu_name,m.d_templates,m.jump_path,m.number  ");
		sql.append(" from xx_pc_menu m");
		sql.append(" left join xx_pc_menu_role mr on m.id = mr.pc_menus");
		sql.append(" left join xx_pc_role r on mr.pc_roles = r.id");
		sql.append(" where m.is_enabled is true ");
		if (type != null) {
			sql.append(" and m.type = ?");
			list.add(type);
		}
		sql.append(" order by m.orders");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<PcMenu> menus = getNativeDao().findListManaged(sql.toString(),
				objs,
				0,
				PcMenu.class);

		return menus;
	}


	/**
	 * 获取所有二级菜单
	 * @return
	 */
	public List<Map<String,Object>> getAllSecondaryMenus(String menuName){
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append(" select xpm.id,xpm.menu_code,xpm.menu_name,xpm.type,xpm.url,super_xpm.`menu_name` super_name");
        sql.append(" from xx_pc_menu xpm left join xx_pc_menu super_xpm on xpm.`super_id` = super_xpm.id and super_xpm.`super_id` is not null and super_xpm.`type` = 1 ");
        sql.append(" where xpm.super_id is not null and xpm.type = 1 and xpm.url is not null and xpm.is_enabled is true ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
            sql.append(" and xpm.company_info_id = ? ");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(menuName)){
			sql.append(" and xpm.menu_name = ? ");
			list.add(menuName);
		}
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),
                objs,
                0);
        return mapList;
	}


	//获取菜单维护列表数据
    public Page<Map<String,Object>> findMenuMaintenanceList(Integer type,int isSuper, Long superId, String menuName, Boolean isEnabled,String createDate, Pageable pageable){
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        sql.append("select distinct m.id,m.company_info_id,m.b_creater,m.b_modifier,m.create_date,m.modify_date," +
                " m.orders,m.is_cat,m.is_enabled,m.menu_img,m.super_id,m.grade,m.is_leaf,m.type," +
                " m.url,m.menu_code,m.menu_name,super_menu.menu_name superName,temp.`name` as tempName,m.d_templates,m.jump_path " +
                " from xx_pc_menu m left join xx_pc_menu_custom mc on m.id = mc.pc_menu_id " +
				" left join xx_pc_menu super_menu on super_menu.id = m.super_id " +
				" left join d_templates temp on temp.id = m.d_templates where 1=1 ");
        if (!ConvertUtil.isEmpty(type)){
            sql.append(" and m.type = ?");
            list.add(type);
        }
        if (!ConvertUtil.isEmpty(isSuper)&&isSuper==1){
			sql.append(" and m.super_id is null");
		}else if(!ConvertUtil.isEmpty(isSuper)&&isSuper==0){
			sql.append(" and m.super_id is not null");
		}
        //父菜单
        if (!ConvertUtil.isEmpty(superId)){
            if (superId==0){
                sql.append(" and m.super_id is null");
            }else {
                sql.append(" and m.super_id = ?");
                list.add(superId);
            }
        }
        //菜单名
        if (!ConvertUtil.isEmpty(menuName)){
            sql.append(" and m.menu_name like ?");
            list.add("%"+menuName+"%");
        }
        //是否启用
        if (!ConvertUtil.isEmpty(isEnabled)){
            sql.append(" and m.is_enabled = ?");
            list.add(isEnabled);
        }
        //创建时间
		if (!ConvertUtil.isEmpty(createDate)){
			sql.append(" and m.create_date like ?");
			list.add("%"+createDate+"%");
		}
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        Page<Map<String, Object>> mapList = getNativeDao().findPageMap(sql.toString(),objs,pageable);
        return mapList;
    }

	/**
	 * 保存菜单角色
	 * @param menu
	 * @param role
	 */
	public void saveMenuRole(Long menu,Long role){
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(" insert into xx_pc_menu_role(pc_roles,pc_menus) VALUES(?,?)");
		list.add(role);
		list.add(menu);
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		getNativeDao().insert(sql.toString(),objs);
	}

	/**
	 * 删除菜单角色
	 * @param pcMenus 菜单id
	 */
	public void deleteMenuRole(Long pcMenus,Long pcRole){
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(" DELETE FROM xx_pc_menu_role WHERE pc_menus = ? ");
		list.add(pcMenus);
		if (!ConvertUtil.isEmpty(pcRole)){
			sql.append(" and pc_roles = ?");
			list.add(pcRole);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		getNativeDao().delete(sql.toString(),objs);
	}

}