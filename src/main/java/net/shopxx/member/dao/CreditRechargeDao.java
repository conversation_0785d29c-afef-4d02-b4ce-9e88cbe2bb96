package net.shopxx.member.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import net.shopxx.basic.util.SystemConfig;
import javax.annotation.Resource;
import net.shopxx.base.core.Filter;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.util.RoleJurisdictionUtil;

@Repository("creditRechargeDao")
public class CreditRechargeDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	

	/**
	 * 临时额度列表数据
	 */
	public Page<Map<String, Object>> findPage(String sn, Long storeMemberId,
			Long storeId, Integer[] status, Long creatorId, Long operatorId,
			Integer rechargeType, Long[] organizationId,Long sbuId,Long saleOrgId,
			String storeMemberName,String startFirstTime,String startLastTime,
			String endFirstTime,String endLastTime, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select cr.*,s.name store_name,s.sn store_sn, smc.username creator_username,smc.name creator_name, smo.username operator_name,");
		sql.append("so.name sale_org_name,csm.username store_member_name,s.alias store_alias,s.out_trade_no outTradeNo,ot.name organization_name,sb.name sbu_name ");
		sql.append(" from xx_credit_recharge cr");
		sql.append(" left join xx_store s on cr.store=s.id");
		sql.append(" left join xx_store_member smc on cr.creator = smc.id");
		sql.append(" left join xx_store_member smo on cr.operator = smo.id");
		sql.append(" left join xx_store_member csm on cr.store_member = csm.id");
		sql.append(" left join xx_sale_org so on cr.sale_org = so.id");
		sql.append(" left join xx_sbu sb  on cr.sbu = sb.id");
		sql.append(" left join xx_organization ot on cr.organization = ot.id ");
		sql.append(" where 1 = 1");
		
		if (companyInfoId != null) {
			sql.append(" and cr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (creatorId != null) {
			sql.append(" and smc.id = ?");
			list.add(creatorId);
		}
		if (operatorId != null) {
			sql.append(" and smo.id = ?");
			list.add(operatorId);
		}
		if(sbuId!=null){
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if(storeMemberName!=null){
			sql.append(" and smc.name = ?");
			list.add(storeMemberName);
		}
		if(saleOrgId!=null){
			sql.append(" and so.id = ?");
			list.add(saleOrgId);
		}
		if (!ConvertUtil.isEmpty(startFirstTime)) {
			sql.append(" and cr.start_date >= ?");
			list.add(DateUtil.convert(startFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(startLastTime)) {
			sql.append(" and cr.start_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(startLastTime + " 00:00:00"),1));
		}
		
		if (!ConvertUtil.isEmpty(endFirstTime)) {
			sql.append(" and cr.end_date  >= ?");
			list.add(DateUtil.convert(endFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endLastTime)) {
			sql.append(" and cr.end_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endLastTime + " 00:00:00"),1));
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and cr.sn like ?");
			list.add("%" + sn + "%");
		}
		if (rechargeType == null) {
			rechargeType = 0;
		}
		
		sql.append(" and cr.recharge_type = ?");
		list.add(rechargeType);

		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and cr.store in (" + storeAuth + ")");
			}
		}

		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and cr.doc_status in (" + os + ")");
		}
		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += organizationId[i];
				else
					os += organizationId[i] + ",";
			}
			sql.append(" and cr.organization in (" + os + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and ot.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and ot.id is null");
			}
		}
		
		sql.append(" order by cr.create_date desc");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> listItems = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return listItems;

	}
	
	/**
	 * 临时额度列表数据导出
	 */
	public List<Map<String, Object>> findTable(Integer[] status,Long storeId,Long organizationId,Long sbuId) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select cr.*,s.name store_name,s.sn store_sn, smc.username creator_username,smc.name creator_name, smo.username operator_name,");
		sql.append("so.name sale_org_name,csm.username store_member_name,s.sn store_sn,s.out_trade_no outTradeNo,ot.name organization_name,sb.name sbu_name ");
		sql.append(" from xx_credit_recharge cr");
		sql.append(" left join xx_store s on cr.store=s.id");
		sql.append(" left join xx_store_member smc on cr.creator = smc.id");
		sql.append(" left join xx_store_member smo on cr.operator = smo.id");
		sql.append(" left join xx_store_member csm on cr.store_member = csm.id");
		sql.append(" left join xx_sale_org so on cr.sale_org = so.id");
		sql.append(" left join xx_sbu sb  on cr.sbu = sb.id");
		sql.append(" left join xx_organization ot on cr.organization = ot.id ");
		sql.append(" where 1 = 1");
		if (companyInfoId != null) {
			sql.append(" and cr.company_info_id = ?");
			list.add(companyInfoId);
		}
//		if (storeMemberId != null) {
//			sql.append(" and sm.id = ?");
//			list.add(storeMemberId);
//		}
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
//		if (creatorId != null) {
//			sql.append(" and smc.id = ?");
//			list.add(creatorId);
//		}
//		if (operatorId != null) {
//			sql.append(" and smo.id = ?");
//			list.add(operatorId);
//		}
		if(sbuId!=null){
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (organizationId != null) {
			sql.append(" and cr.organization = ?");
			list.add(organizationId);
		}
//		if (!ConvertUtil.isEmpty(sn)) {
//			sql.append(" and cr.sn like ?");
//			list.add("%" + sn + "%");
//		}
//		if (rechargeType == null) {
//			rechargeType = 0;
//		}
//		sql.append(" and cr.recharge_type = ?");
//		list.add(rechargeType);

		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and cr.store in (" + storeAuth + ")");
			}
		}
		else {

			sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
		}

		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and cr.doc_status in (" + os + ")");
		}
		sql.append(" order by cr.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> listItems = getNativeDao().findListMap(sql.toString(), objs, 0);
		return listItems;

	}

	public Page<Map<String, Object>> findShowPage(Long storeId, String sn,
			Integer rechargeType, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select xcr.id,xcr.sn,xcr.actual_amount,"
				+ " ifnull((select sum(order_amount) from xx_credit_recharge_contract xcrc  where xcrc.credit_recharge_no=xcr.sn ),0) available_amount from xx_credit_recharge  xcr "
				+ " where xcr.doc_status=2 and xcr.company_info_id=?");
		list.add(companyInfoId);

		if (storeId != null) {
			sql.append("  and xcr.store = ?");
			list.add(storeId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and xcr.sn like ?");
			list.add("%" + sn + "%");
		}
		if (rechargeType != null) {
			sql.append(" and xcr.recharge_type = ?");
			list.add(rechargeType);
		}

		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (!(storeMember.getMemberType() == 1 || storeMember.getIsSalesman())) {
			Long saleOrgId = null;
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				saleOrgId = storeMemberSaleOrg.getSaleOrg().getId();
			}
			sql.append(" and exists (select 1 from xx_sale_org so where xcr.sale_org = so.id  and (so.id="
					+ saleOrgId
					+ " or so.tree_path like '%,"
					+ saleOrgId
					+ ",%')"
					+ ") ");
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		// String totalSql = "select count(1) from (" + sql + ") a";
		// long total = getNativeDao().findInt(totalSql, objs);
		// page.setTotal(total);
		return page;
	}

	public Integer countCreditRecharge(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Integer[] budgetType, BigDecimal minPrice,
			BigDecimal maxPrice,Long sbuId, String firstTime, String lastTime, Long[] ids,
			Pageable pageable, Integer size, Integer page) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select count(cr.id)");
		sql.append(" from xx_credit_recharge cr");
		sql.append(" left join xx_store s on cr.store=s.id");
		sql.append(" left join xx_store_member sm on cr.store_member = sm.id");
		sql.append(" left join xx_store_member smc on cr.creator = smc.id");
		sql.append(" left join xx_store_member smo on cr.operator = smo.id");
		sql.append(" left join xx_system_dict sd on cr.recharge_type = sd.id and sd.parent is not null");
		sql.append(" left join xx_sale_org so on cr.sale_org = so.id");
		sql.append(" left join xx_sbu sb on cr.sbu = sb.id");
		sql.append(" left join xx_organization ot on cr.organization = ot.id ");
		sql.append(" where 1 = 1");
		
		if (companyInfoId != null) {
			sql.append(" and cr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and cr.store in (" + storeAuth + ")");
			}
		}
		if (creatorId != null) {
			sql.append(" and smc.id = ?");
			list.add(creatorId);
		}
		if (operatorId != null) {
			sql.append(" and smo.id = ?");
			list.add(operatorId);
		}
		if (minPrice != null) {
			sql.append(" and cr.amount >= ?");
			list.add(minPrice);
		}
		if (maxPrice != null) {
			sql.append(" and cr.amount <= ?");
			list.add(maxPrice);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and cr.sn like ?");
			list.add("%" + sn + "%");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and cr.status in (" + os + ")");
		}
		if (docstatus != null && docstatus.length > 0) {
			String os = "";
			for (int i = 0; i < docstatus.length; i++) {
				if (i == docstatus.length - 1)
					os += docstatus[i];
				else
					os += docstatus[i] + ",";
			}
			sql.append(" and cr.doc_status in (" + os + ")");
		}
		if (budgetType != null && budgetType.length > 0) {
			String bt = "";
			for (int i = 0; i < budgetType.length; i++) {
				if (i == budgetType.length - 1)
					bt += budgetType[i];
				else
					bt += budgetType[i] + ",";
			}
			sql.append(" and cr.budget_type in (" + bt + ")");
		}
		if (rechargeTypeId != null && rechargeTypeId.length > 0) {
			String rt = "";
			for (int i = 0; i < rechargeTypeId.length; i++) {
				if (i == rechargeTypeId.length - 1)
					rt += rechargeTypeId[i];
				else
					rt += rechargeTypeId[i] + ",";
			}
			sql.append(" and cr.recharge_type in (" + rt + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and cr.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and cr.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and cr.id in (" + inIds + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and ot.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and ot.id is null");
			}
		}
		
		sql.append(" order by cr.create_date desc");
		
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Integer count = getNativeDao().findInt(sql.toString(), objs);
		
		return count;

	}

	public List<Map<String, Object>> findList(Long storeId, String sn) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		System.out.println("sn:" + sn);
		StringBuilder sql = new StringBuilder();
		sql.append("select xcr.id,xcr.sn,xcr.actual_amount,"
				+ " ifnull((select sum(order_amount) from xx_credit_recharge_contract xcrc  where xcrc.credit_recharge_no=xcr.sn ),0) available_amount from xx_credit_recharge  xcr "
				+ " where xcr.doc_status=2 and xcr.company_info_id=?");
		if (storeId != null) {
			sql.append(" and xcr.store=?");
			list.add(storeId);
		}
		list.add(companyInfoId);

		sql.append(" and xcr.sn=?");
		list.add(sn);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	/** 客户授信导出 */
	public List<Map<String, Object>> findCreditRechargeList(Integer[] type,
			String sn, Long storeMemberId, Long storeId, Long saleOrgId,
			Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, Integer[] budgetType,
			BigDecimal minPrice, BigDecimal maxPrice, String firstTime,
			Long[] ids, String lastTime,Long sbuId, Pageable pageable, Integer page,
			Integer size) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select cr.*,so.name sale_org_name,s.name store_name, sm.name, smc.username creator_name, smo.name operator_name,");
		sql.append("sd.id recharge_type_id, sd.value recharge_type_value,ot.name organization_name,s.out_trade_no,sb.name sbu_name,"
				+ " s.alias store_alias");
		sql.append(" from xx_credit_recharge cr");
		sql.append(" left join xx_store s on cr.store=s.id");
		sql.append(" left join xx_store_member sm on cr.store_member = sm.id");
		sql.append(" left join xx_store_member smc on cr.creator = smc.id");
		sql.append(" left join xx_store_member smo on cr.operator = smo.id");
		sql.append(" left join xx_system_dict sd on cr.recharge_type = sd.id and sd.parent is not null");
		sql.append(" left join xx_sale_org so on cr.sale_org = so.id");
		sql.append(" left join xx_sbu sb on cr.sbu = sb.id");
		sql.append(" left join xx_organization ot on cr.organization = ot.id ");
		sql.append(" where 1 = 1");
		
		if (companyInfoId != null) {
			sql.append(" and cr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and cr.store in (" + storeAuth + ")");
			}
		}
		if (creatorId != null) {
			sql.append(" and smc.id = ?");
			list.add(creatorId);
		}
		if (operatorId != null) {
			sql.append(" and smo.id = ?");
			list.add(operatorId);
		}
		if (minPrice != null) {
			sql.append(" and cr.amount >= ?");
			list.add(minPrice);
		}
		if (maxPrice != null) {
			sql.append(" and cr.amount <= ?");
			list.add(maxPrice);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and cr.sn like ?");
			list.add("%" + sn + "%");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and cr.status in (" + os + ")");
		}
		if (docstatus != null && docstatus.length > 0) {
			String os = "";
			for (int i = 0; i < docstatus.length; i++) {
				if (i == docstatus.length - 1)
					os += docstatus[i];
				else
					os += docstatus[i] + ",";
			}
			sql.append(" and cr.doc_status in (" + os + ")");
		}
		if (budgetType != null && budgetType.length > 0) {
			String bt = "";
			for (int i = 0; i < budgetType.length; i++) {
				if (i == budgetType.length - 1)
					bt += budgetType[i];
				else
					bt += budgetType[i] + ",";
			}
			sql.append(" and cr.budget_type in (" + bt + ")");
		}
		if (rechargeTypeId != null && rechargeTypeId.length > 0) {
			String rt = "";
			for (int i = 0; i < rechargeTypeId.length; i++) {
				if (i == rechargeTypeId.length - 1)
					rt += rechargeTypeId[i];
				else
					rt += rechargeTypeId[i] + ",";
			}
			sql.append(" and cr.recharge_type in (" + rt + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and cr.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and cr.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and cr.id in (" + inIds + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and ot.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and ot.id is null");
			}
		}
		
		sql.append(" order by cr.create_date desc");
		
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);
		return maps;

	}

	public List<Map<String, Object>> findIsHaveSameStroe(Long storeId,
			Long storeMemberId, String startDate, Integer rechargeType,
			Long organizationId) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append(" select cr.* ");
		sql.append(" from xx_credit_recharge cr");
		sql.append(" left join xx_store_member s on cr.store_member=s.id");
		sql.append(" left join xx_store xs on cr.store=xs.id");
		sql.append(" where 1=1");
		if (companyInfoId != null) {
			sql.append(" and cr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(storeMemberId)) {
			sql.append(" and s.id = ?");
			list.add(storeMemberId);
		}
		if (!ConvertUtil.isEmpty(storeId)) {
			sql.append(" and xs.id = ?");
			list.add(storeId);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and cr.end_date >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(rechargeType)) {
			sql.append(" and cr.recharge_type = ?");
			list.add(rechargeType);
		}
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" and cr.organization = ?");
			list.add(organizationId);
		}
		sql.append(" and cr.doc_status != 3 ");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);

	}

	public Page<Map<String, Object>> findCreditRechargeContractPage(
			Pageable pageable, Object[] args) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		String creditRechargeNo = (String) args[0];
		String orderNo = (String) args[1];
		Integer orderType = (Integer) args[2];
		Integer rechargeType = (Integer) args[3];
		String contract_no = (String) args[4];

		StringBuilder sql = new StringBuilder();
		sql.append("select c.* from xx_credit_recharge_contract c where 1=1");
		if (companyInfoId != null) {
			sql.append(" and c.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(creditRechargeNo)) {
			sql.append(" and c.credit_recharge_no like ?");
			list.add("%" + creditRechargeNo + "%");
		}
		if (!ConvertUtil.isEmpty(orderNo)) {
			sql.append(" and c.order_no like ?");
			list.add("%" + orderNo + "%");
		}
		if (orderType != null) {
			sql.append(" and c.order_type = ?");
			list.add(orderType);
		}
		if (rechargeType != null) {
			sql.append(" and c.recharge_type = ?");
			list.add(rechargeType);
		}
		if (!ConvertUtil.isEmpty(contract_no)) {
			sql.append(" and c.contract_no = ?");
			list.add(contract_no);
		}
		if (rechargeType != null && !ConvertUtil.isEmpty(contract_no)) {
			String sqlc = "select sum(ifnull(actual_amount,0)) from xx_deposit_recharge"
					+ " where amount_type=? and type=1 and doc_status=2 and contract_no=?";
			int amount_type = 3;
			if (rechargeType == 1) {
				amount_type = 4;
			}
			BigDecimal actual_amount = getNativeDao().findBigDecimal(sqlc,
					new Object[] { amount_type, contract_no });
			if (actual_amount == null) actual_amount = BigDecimal.ZERO;

			sql.append(" and c.order_amount > ?");
			list.add(actual_amount);

		}
		sql.append(" order by c.create_date desc");

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				list.toArray(),
				pageable);
		return page;
	}

	public boolean findJurisdiction(StoreMember storeMember) {
		Long storeMemberId = storeMember.getId();
		String sql = "select count(*) from xx_pc_role  where id in "
				+ "(select pc_role from xx_pc_user_role "
				+ "where company_info_id = 5 and store_member = "
				+ storeMemberId
				+ ")   and name = '财务' ";
		int count = getNativeDao().findInt(sql);
		return count > 0;
	}
	public List<Map<String, Object>> findSbu(Long id) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append(" select s.* ");
		sql.append(" from xx_sub s");
		sql.append(" where 1=1");
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (id != null) {
			sql.append(" and s.credit_recharge = ?");
			list.add(id);
		}
		
		
		sql.append(" and cr.is_default =true ");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);

	}
	
}