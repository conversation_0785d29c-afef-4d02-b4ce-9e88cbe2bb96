package net.shopxx.member.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.service.SystemParameterBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository("pwdModificationRecordDao")
public class PwdModificationRecordDao extends DaoCenter {

    @Autowired
    private SystemParameterBaseService systemParameterBaseService;

    /**
     * 获取指定用户的30天内修改记录数量
     * @param storeMemberId
     * @return
     */
    public int getPwdModificationCount(Long storeMemberId) {
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        Integer days = Integer.valueOf(systemParameterBaseService.getValue(companyInfoId, "changePasswordControlDays"));
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) from xx_pwd_modification_record p where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and p.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (storeMemberId != null) {
            sql.append(" and p.store_member = ?");
            list.add(storeMemberId);
        }

        sql.append(" and DATE_SUB(CURDATE(), INTERVAL "+days+" DAY) <= date(p.create_date) ");
        LogUtils.info("查询密码修改记录sql："+sql.toString());
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        Integer count = getNativeDao().findInt(sql.toString(),objs);
        return count;
    }

}
