package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreApply;
import net.shopxx.util.RoleJurisdictionUtil;

@Repository("storeApplyDao")
public class StoreApplyDao extends DaoCenter {
	
	@Resource(name="roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	

	public Page<Map<String, Object>> findPage(String name, String outTradeNo,
			String alias, Integer[] type, Long companyInfoId,
			Long memberRankId, Long saleOrgId, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		String sql = "select sa.*,mr.name member_rank_name,so.name sale_org_name,sm.name storeMemberName,sdd.value customer_type_value "
				+ "from xx_store_apply sa "
				+ "left join xx_member_rank mr on sa.member_rank=mr.id "
				+ "left join xx_sale_org so on sa.sale_org=so.id "
				+ "left join xx_store_apply_sbu sas on sa.id = sas.store_apply "
				+ "left join xx_sbu sb on sb.id = sas.sbu "
				+ "left join xx_store_member sm on sa.store_member=sm.id "
				+ "left join xx_system_dict sdd on sdd.id=sa.customer_type  "
				+ "where sa.company_info_id=?";
		list.add(companyInfoId);
		if (!ConvertUtil.isEmpty(name)) {
			sql += " and sa.name like ?";
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql += " and sa.out_trade_no like ?";
			list.add("%" + outTradeNo + "%");
		}
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql += " and sa.customer_type in (" + os + ")";
		}
		if (!ConvertUtil.isEmpty(memberRankId)) {
			sql += " and sa.member_rank=?";
			list.add(memberRankId);
		}
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql += " and sa.sale_org=?";
			list.add(saleOrgId);
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql += " and so.id in (" + saleOrgIds + ") ";
			}else{
				sql += " and so.id is null ";
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql += " and (sb.id is null or sb.id in (" + sbuIds + "))";
			}else{
				sql += " and sb.id is null ";
			}
		}
		
		sql += " group by sa.id order by sa.create_date desc";
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		
		
		Page<Map<String, Object>> listItems = getNativeDao().findPageMap(sql,
				objs,
				pageable);
		return listItems;

	}

	public Page<Map<String, Object>> findStoreApplyAddressPage(String mobile,
			String consignee, Long storeId, String address, Pageable pageable) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT t.*,xar.tree_path, xar.full_name area_full_name, xsa.full_name sales_area_full_name,xsa.geography_type geographyType,"
				+ " xsa.logic_type logicType,xsa.erp_sn erpSn, xsa.id  salesAreaId  "
				+ " FROM xx_store_apply_address  t "
				+ " LEFT JOIN xx_sales_area xsa on xsa.id= t.sales_area "
				+ " LEFT JOIN xx_area xar on xar.id=t.area WHERE 1=1 ";
		if (storeId != null) {
			sql += " and t.store_apply=" + storeId;
		}
		if (companyInfoId != null) {
			sql += " and t.company_info_id=" + companyInfoId;
		}
		if (address != null) {
			sql += " and t.address like '%" + address + "%'";
		}
		if (consignee != null) {
			sql += " and t.consignee like '%" + consignee + "%'";
		}
		if (mobile != null) {
			sql += " and t.mobile like '%" + mobile + "%'";
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql,
				null,
				pageable);
		return page;
	}

	public List<Map<String, Object>> findBusinessCategoryApply(
			StoreApply storeApply) {

		StringBuilder sql = new StringBuilder();
		sql.append(" select p1.id id,p1.name name,p2.id cid,p2.name cname from xx_business_category_apply c ");
		sql.append(" left join xx_product_category p1  on p1.id =c.product_big_type");
		sql.append(" left join xx_product_category p2 on p2.id = c.product_center_type ");
		sql.append(" where c.store_apply = " + storeApply.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findBusinessRecordApply(
			StoreApply storeApply) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select b.* from xx_business_record_apply b  where b.store_apply = "
				+ storeApply.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findCautionMoneyApply(StoreApply storeApply) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select c.* from xx_caution_money_apply c  where c.store_apply = "
				+ storeApply.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findInvoiceInfoListByStoreApply(
			Long storeId) {
		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_store_apply_invoice_info where store_apply=?");
		return getNativeDao().findListMap(sql.toString(),
				new Object[] { storeId },
				0);
	}

	/**
	 * 根据id查找对应的附件信息
	 * */
	public List<Map<String, Object>> findListByOrderId(Long id,Integer type) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "select * from xx_store_apply_attach where company_info_id = ? and store_applys=? and type = ?";
		list.add(companyInfoId);
		list.add(id);
		list.add(type);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}

	/**
	 * 查找外部编码最大流水号
	 * 
	 * @return
	 */
	public Integer findMaxSn() {
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append(" select ifnull(max(max_sn),100000) from xx_store_apply  where company_info_id = "
				+ companyInfoId);
		return getNativeDao().findInt(sql.toString());
	}

	public Integer countShopInfo(Long id) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(" select count(*) ");
		sql.append(" from xx_store_apply s ");
		sql.append(" left join xx_shop_info si on s.id = si.store");
		sql.append(" where 1=1 and si.store_apply = " + id + " ");

		sql.append(" order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}

	//获取联系人
	public List<Map<String, Object>> findStoreContract(StoreApply storeApply) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select sc.* from xx_store_apply_contract sc  where sc.store_apply = "
				+ storeApply.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findStoreApplyCooperation(
			StoreApply storeApply) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select sac.* from xx_store_apply_cooperation sac  where sac.store_apply = "
				+ storeApply.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}
}
