package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;

import org.springframework.stereotype.Repository;
import org.springframework.ui.ModelMap;

@Repository("storeInvoiceInfoDao")
public class StoreInvoiceInfoDao extends DaoCenter {

	public List<Map<String, Object>> findListByStore(Long storeId,String invoiceTitle) {
		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_store_invoice_info t where 1=1 ");
		if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and t.store = ? ");
			list.add(storeId);
		}
		if(!ConvertUtil.isEmpty(invoiceTitle)){
			sql.append(" and t.invoice_title  like ?");
			list.add("%"+invoiceTitle+"%");
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(),objs,0);
	}

	public Page<Map<String, Object>> findPage(Long companyInfoId, String name,
			String outTradeNo, String invoiceTitle, Pageable pageable,
			ModelMap model) {

		List<Object> list = new ArrayList<Object>();

		String sql = "SELECT st.name,st.out_trade_no,sii.* from xx_store_invoice_info sii"
				+ "	left join xx_store st on st.id = sii.store where 1 = 1 and sii.is_import = '1' ";

		if (!ConvertUtil.isEmpty(name)) {
			sql += " and st.name like ?";
			list.add("%" + name + "%");
		}

		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql += " and st.out_trade_no like ?";
			list.add("%" + outTradeNo + "%");
		}

		if (!ConvertUtil.isEmpty(invoiceTitle)) {
			sql += " and sii.invoice_title like ?";
			list.add("%" + invoiceTitle + "%");
		}

		if (companyInfoId != null) {
			sql += " and sii.company_info_id = ?";
			list.add(companyInfoId);
		}

		sql += " ORDER BY sii.modify_date desc ";

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql,
				objs,
				pageable);

		return page;
	}
}
