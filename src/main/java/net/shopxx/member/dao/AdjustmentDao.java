package net.shopxx.member.dao;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
@Repository("adjustmentDao")
public class AdjustmentDao extends DaoCenter {
	
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	
	public Page<Map<String, Object>> findPage(String sn,Long[] states,
			Long[] wfStates,String sourceSn,Long[] sourceStoreId,
			Long[] adjustmentStoreId,Long[] sourceSbuId,Long[] adjustmentSbuId,
			Long[] sourceOrganizationId,Long[] adjustmentOrganizationId,String adjustmentSn,
			Long[] sourceSaleOrgId,Long[] adjustmentSaleOrgId,Long[] ids,Pageable pageable) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT ad.id,ad.sn,ad.status,ad.wf_state,ad.b_creater,ad.create_date,dre.sn sourceSn,sourceSg.name sourceSaleOrgName, "
					+ " sourceS.out_trade_no sourceOutTradeNo,sourceS.name sourceStoreName,sourceS.alias sourceStoreAlias,dre.amount, "
					+ " sourceSu.name sourceSbuName,sourceBc.bank_card_no sourceBankCardNo,sourceBc.bank_name sourceBankName, "
					+ " sourceBc.bank_code sourceBankCode,sourceO.name sourceOrganizationName, "
					+ " dte.sn adjustmentSn,adjustmentSg.name adjustmentSaleOrgName,adjustmentS.out_trade_no adjustmentOutTradeNo, "
					+ " adjustmentS.name adjustmentStoreName,adjustmentS.alias adjustmentStoreAlias,dte.amount actual_amount,"
					+ " adjustmentSu.name adjustmentSbuName,adjustmentBc.bank_card_no adjustmentBankCardNo, "
					+ " adjustmentBc.bank_name adjustmentBankName,adjustmentBc.bank_code adjustmentBankCode, "
					+ " adjustmentO.name adjustmentOrganizationName,ad.memo ");
		sql.append(" FROM xx_adjustment ad   ");
		sql.append(" LEFT JOIN xx_deposit_recharge dre ON  dre.id = ad.source_deposit_recharge  ");
		sql.append(" LEFT JOIN xx_sale_org sourceSg ON sourceSg.id = dre.sale_org ");
		sql.append(" LEFT JOIN xx_store sourceS ON sourceS.id = dre.stores ");
		sql.append(" LEFT JOIN xx_sbu sourceSu ON sourceSu.id = dre.sbu   ");
		sql.append(" LEFT JOIN xx_bank_card sourceBc ON sourceBc.id = dre.bank_card  ");
		sql.append(" LEFT JOIN xx_organization sourceO ON sourceO.id = dre.organization ");
		
		sql.append(" LEFT JOIN xx_deposit_recharge dte ON dte.id = ad.adjustment_deposit_recharge ");
		sql.append(" LEFT JOIN xx_sale_org adjustmentSg ON adjustmentSg.id = dte.sale_org   ");
		sql.append(" LEFT JOIN xx_store adjustmentS ON adjustmentS.id = dte.stores ");
		sql.append(" LEFT JOIN xx_sbu adjustmentSu ON adjustmentSu.id = dte.sbu   ");
		sql.append(" LEFT JOIN xx_bank_card adjustmentBc ON adjustmentBc.id = dte.bank_card  ");
		sql.append(" LEFT JOIN xx_organization adjustmentO ON adjustmentO.id = dte.organization  ");
		sql.append(" WHERE 1=1  ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and ad.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (ids != null && ids.length > 0) {
			String os = "";
			for (int i = 0; i < ids.length; i++) {
				if (i == ids.length - 1)
					os += ids[i];
				else
					os += ids[i] + ",";
			}
			sql.append(" and  ad.id in (" + os + ")");
		}
		//单据编号
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and ad.sn like ?");
			list.add("%" + sn + "%");
		}
		//单据状态
		if (states != null && states.length > 0) {
			String os = "";
			for (int i = 0; i < states.length; i++) {
				if (i == states.length - 1)
					os += states[i];
				else
					os += states[i] + ",";
			}
			sql.append(" and  ad.status in (" + os + ")");
		}
		//流程状态
		if (wfStates != null && wfStates.length > 0) {
			String os = "";
			for (int i = 0; i < wfStates.length; i++) {
				if (i == wfStates.length - 1)
					os += wfStates[i];
				else
					os += wfStates[i] + ",";
			}
			sql.append(" and  ad.wf_state in (" + os + ")");
		}
		//来源单据编号
		if (!ConvertUtil.isEmpty(sourceSn)) {
			sql.append(" and dre.sn like ?");
			list.add("%" + sourceSn + "%");
		}
		//来源客户
		if (sourceStoreId != null && sourceStoreId.length > 0) {
			String os = "";
			for (int i = 0; i < sourceStoreId.length; i++) {
				if (i == sourceStoreId.length - 1)
					os += "'"+sourceStoreId[i]+"'";
				else
					os += "'"+sourceStoreId[i] + "',";
			}
			sql.append(" and  sourceS.id in (" + os + ")");
		}
		//调款客户
		if (adjustmentStoreId != null && adjustmentStoreId.length > 0) {
			String os = "";
			for (int i = 0; i < adjustmentStoreId.length; i++) {
				if (i == adjustmentStoreId.length - 1)
					os += "'"+adjustmentStoreId[i]+"'";
				else
					os += "'"+adjustmentStoreId[i] + "',";
			}
			sql.append(" and  adjustmentS.id in (" + os + ")");
		}
		//来源Sbu
		if (sourceSbuId != null && sourceSbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sourceSbuId.length; i++) {
				if (i == sourceSbuId.length - 1)
					os += "'"+sourceSbuId[i]+"'";
				else
					os += "'"+sourceSbuId[i] + "',";
			}
			sql.append(" and  sourceSu.id in (" + os + ")");
		}
		//调款Sbu
		if (adjustmentSbuId != null && adjustmentSbuId.length > 0) {
			String os = "";
			for (int i = 0; i < adjustmentSbuId.length; i++) {
				if (i == adjustmentSbuId.length - 1)
					os += "'"+adjustmentSbuId[i]+"'";
				else
					os += "'"+adjustmentSbuId[i] + "',";
			}
			sql.append(" and  adjustmentSu.id in (" + os + ")");
		}
		//来源经营组织
		if (sourceOrganizationId != null && sourceOrganizationId.length > 0) {
			String os = "";
			for (int i = 0; i < sourceOrganizationId.length; i++) {
				if (i == sourceOrganizationId.length - 1)
					os += "'"+sourceOrganizationId[i]+"'";
				else
					os += "'"+sourceOrganizationId[i] + "',";
			}
			sql.append(" and  sourceO.id in (" + os + ")");
		}
		//调款经营组织
		if (adjustmentOrganizationId != null && adjustmentOrganizationId.length > 0) {
			String os = "";
			for (int i = 0; i < adjustmentOrganizationId.length; i++) {
				if (i == adjustmentOrganizationId.length - 1)
					os += "'"+adjustmentOrganizationId[i]+"'";
				else
					os += "'"+adjustmentOrganizationId[i] + "',";
			}
			sql.append(" and  adjustmentO.id in (" + os + ")");
		}
		//调款单编号
		if (!ConvertUtil.isEmpty(adjustmentSn)) {
			sql.append(" and dte.sn like ?");
			list.add("%" + adjustmentSn + "%");
		}
		//来源机构
		if (sourceSaleOrgId != null && sourceSaleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < sourceSaleOrgId.length; i++) {
				if (i == sourceSaleOrgId.length - 1)
					os += "'"+sourceSaleOrgId[i]+"'";
				else
					os += "'"+sourceSaleOrgId[i] + "',";
			}
			sql.append(" and sourceSg.id in (" + os + ")");
		}
		//调款机构
		if (adjustmentSaleOrgId != null && adjustmentSaleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < adjustmentSaleOrgId.length; i++) {
				if (i == adjustmentSaleOrgId.length - 1)
					os += "'"+adjustmentSaleOrgId[i]+"'";
				else
					os += "'"+adjustmentSaleOrgId[i] + "',";
			}
			sql.append(" and adjustmentSg.id in (" + os + ")");
		}
		
		//调款单里面用户机构、sbu、经营组织等角色权限数据过滤
		Integer adjustmentRoleCount = roleJurisdictionUtil.getAdjustmentRoleCount();
		if(adjustmentRoleCount == 0){
			sql.append(" and (( 1=1 ");
		}
		//来源
			//用户sbu
			String sourceSbuIds = roleJurisdictionUtil.getSbuIds();
			if(!ConvertUtil.isEmpty(sourceSbuIds) && !sourceSbuIds.equals("-1")){
				if(!ConvertUtil.isEmpty(sourceSbuIds) && !sourceSbuIds.equals("0")){
					sql.append(" and sourceSu.id in (" + sourceSbuIds + ")");
				}else{
					sql.append(" and sourceSu.id is null");
				}
			}
			//用户机构
			String sourceSaleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
			if(!ConvertUtil.isEmpty(sourceSaleOrgIds) && !sourceSaleOrgIds.equals("-1")){
				if(!ConvertUtil.isEmpty(sourceSaleOrgIds) && !sourceSaleOrgIds.equals("0")){
					sql.append(" and sourceSg.id in (" + sourceSaleOrgIds + ")  ");
				}else{
					sql.append(" and sourceSg.id is null  ");
				}
			}
		    //用户经营组织
			String sourceOrganizationIdS = roleJurisdictionUtil.getOrganizationIds();
			if(!ConvertUtil.isEmpty(sourceOrganizationIdS) && !sourceOrganizationIdS.equals("-1")){
				if(!ConvertUtil.isEmpty(sourceOrganizationIdS) && !sourceOrganizationIdS.equals("0")){
					sql.append(" and sourceO.id in (" + sourceOrganizationIdS + ")");
				}else{
					sql.append(" and sourceO.id is null");
				}
			}
		if(adjustmentRoleCount == 0){
			sql.append(" ) or (1=1 ");		
		}	
		//调款
			//用户sbu
			String adjustmentSbuIds = roleJurisdictionUtil.getSbuIds();
			if(!ConvertUtil.isEmpty(adjustmentSbuIds) && !adjustmentSbuIds.equals("-1")){
				if(!ConvertUtil.isEmpty(adjustmentSbuIds) && !adjustmentSbuIds.equals("0")){
					sql.append(" and adjustmentSu.id in (" + adjustmentSbuIds + ")");
				}else{
					sql.append(" and adjustmentSu.id is null");
				}
			}
			//用户机构
			String adjustmentSaleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
			if(!ConvertUtil.isEmpty(adjustmentSaleOrgIds) && !adjustmentSaleOrgIds.equals("-1")){
				if(!ConvertUtil.isEmpty(adjustmentSaleOrgIds) && !adjustmentSaleOrgIds.equals("0")){
					sql.append(" and adjustmentSg.id in (" + adjustmentSaleOrgIds + ")  ");
				}else{
					sql.append(" and adjustmentSg.id is null  ");
				}
			}
		    //用户经营组织
			String adjustmentOrganizationIdS = roleJurisdictionUtil.getOrganizationIds();
			if(!ConvertUtil.isEmpty(adjustmentOrganizationIdS) && !adjustmentOrganizationIdS.equals("-1")){
				if(!ConvertUtil.isEmpty(adjustmentOrganizationIdS) && !adjustmentOrganizationIdS.equals("0")){
					sql.append(" and adjustmentO.id in (" + adjustmentOrganizationIdS + ")");
				}else{
					sql.append(" and adjustmentO.id is null");
				}
			}
		if(adjustmentRoleCount == 0){
			sql.append(" )) ");		
		}	
		/**
		 * 用户经营组织权限
		 */
		/*List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String organizationRolesValue = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(!ConvertUtil.isEmpty(organizationRolesValue)){
			if(!ConvertUtil.isEmpty(userRoles)&&userRoles.size()>0){
				String[] perRole = organizationRolesValue.split(",");
				List<String> perRoleList = Arrays.asList(perRole);
				for (PcUserRole userRole : userRoles) {
					if (perRoleList.contains(userRole.getPcRole().getName())) {
						storeMemberOrganization++;
						break;
					}
				}
			}
		}
		sql.append(" AND (( ");
		//来源
		if(storeMemberOrganization==0){
			sql.append(" sourceO.id in (SELECT DISTINCT smo.organization FROM xx_store_member_organization smo WHERE 1=1 "
				+ " AND smo.company_info_id ="+companyInfoId+" AND smo.store_member ="+storeMemberId+") AND ");	
		}
		//用户SBU
		sql.append(" sourceSu.id in (SELECT DISTINCT smu.sbu FROM xx_store_member_sbu smu WHERE 1=1 "
				+ " AND smu.company_info_id ="+companyInfoId+" AND smu.store_member ="+storeMemberId+")");
		sql.append(" and (sourceSg.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
			+ " where smo.sale_org = s.id and smo.store_member = ?) "
			+ " or sourceSg.id in (select  a.id from xx_sale_org a,xx_sale_org b "
			+ " where a.tree_path like concat('%,', b.id, ',%') "
			+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
			+ " where smo.sale_org = s.id and smo.store_member = ?)))) or ( ");
		list.add(WebUtils.getCurrentStoreMemberId());
		list.add(WebUtils.getCurrentStoreMemberId());
		
		
		//调款
		if(storeMemberOrganization==0){
			sql.append(" adjustmentO.id in (SELECT DISTINCT smo.organization FROM xx_store_member_organization smo WHERE 1=1 "
				+ " AND smo.company_info_id ="+companyInfoId+" AND smo.store_member ="+storeMemberId+") AND ");	
		}
		//用户SBU
		sql.append("  adjustmentSu.id in (SELECT DISTINCT smu.sbu FROM xx_store_member_sbu smu WHERE 1=1 "
				+ " AND smu.company_info_id ="+companyInfoId+" AND smu.store_member ="+storeMemberId+")");
		sql.append(" and (adjustmentSg.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
			+ " where smo.sale_org = s.id and smo.store_member = ?) "
			+ " or adjustmentSg.id in (select  a.id from xx_sale_org a,xx_sale_org b "
			+ " where a.tree_path like concat('%,', b.id, ',%') "
			+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
			+ " where smo.sale_org = s.id and smo.store_member = ?)))) ) ");
		list.add(WebUtils.getCurrentStoreMemberId());
		list.add(WebUtils.getCurrentStoreMemberId());*/
		
		sql.append(" GROUP BY ad.id ORDER BY ad.modify_date desc ");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		
		String totalsql = "select count(1) from ( " + sql + ") t";
		
		long total = getNativeDao().findInt(totalsql, objs);
		
		page.setTotal(total);
		
		return page;
	}
	
	/**
	 * 根据系统调账ID查找对应的附件信息
	 * */
	public List<Map<String, Object>> findListByAdjustmentId(Long id) {
		List<Object> list = new ArrayList<Object>();
		String sql = "SELECT a.*,s.name store_member_name FROM xx_adjustment_attach a,xx_store_member s WHERE a.store_member=s.id AND a.adjustment = ? ";
		list.add(id);
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}
	
	
	
	public  List<Map<String, Object>> findAdjustmentList(Long depositRechargeId) {
		
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		sql.append(" SELECT * FROM xx_adjustment ad WHERE 1=1 ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and ad.company_info_id = ?");
			list.add(companyInfoId);
		}
		
		if(!ConvertUtil.isEmpty(depositRechargeId)){
			sql.append(" AND (ad.source_deposit_recharge = ? or ad.adjustment_deposit_recharge = ?) ");
			list.add(depositRechargeId);
			list.add(depositRechargeId);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);
		
		return maps;
	}
}
