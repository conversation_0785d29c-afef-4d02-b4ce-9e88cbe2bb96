package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;

import org.springframework.stereotype.Repository;

@Repository("postBaseDao")
public class PostBaseDao extends DaoCenter {

	public Page<Map<String, Object>> findPage(String startTime, String endTime, Pageable pageable, String name,
			String sn, Long saleOrgId,Long isEnabled) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_post p where 1=1 ");
		if (companyInfoId != null) {
			sql.append(" and p.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and p.name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and p.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" and p.create_date >= ?");
			list.add(startTime + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" and p.create_date < ?");
			list.add(endTime + " 24:00:00");
		}
		if (isEnabled != null) {
			sql.append(" and p.is_enabled = ?");
			list.add(isEnabled);
		}
		sql.append(" order by p.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);

		return page;
	}
}
