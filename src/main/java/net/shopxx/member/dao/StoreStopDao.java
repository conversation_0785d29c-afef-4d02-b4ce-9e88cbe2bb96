package net.shopxx.member.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreStop;
import net.shopxx.member.service.StoreMemberBaseService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("storeStopDao")
public class StoreStopDao extends DaoCenter {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

    public Page<Map<String, Object>> findPage(StoreStop ss, List<Object> param, Pageable pageable) {
        StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Object storeId = param.get(0);
        Object isCoreStroe = param.get(1);
        String createName= (String) param.get(2);
        String firstTime = (String) param.get(3);
        String lastTime = (String) param.get(4);
        sql.append(" SELECT ss.* ");
        sql.append(" ,s.`name` store_name,s.dealer_name dealer_names,s.grant_code,s.alias,s.region region1, ");
        sql.append("( CASE s.is_core_stroe WHEN 1 THEN '是' WHEN 0 THEN '否' END ) is_core");
        sql.append(" ,sm.`name` create_name ");
        sql.append(" from xx_store_stop ss ");
        sql.append(" LEFT JOIN xx_store s ON s.id = ss.store ");
        sql.append(" LEFT JOIN xx_sale_org so ON so.id = s.sale_org ");
        sql.append(" LEFT JOIN xx_store_member sm ON sm.username = ss.b_creater ");
        sql.append(" where 1=1 ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and ss.company_info_id = ? ");
			list.add(companyInfoId);
		}
        if(!ConvertUtil.isEmpty(ss.getSn())){
			sql.append(" and ss.sn = ?");
			list.add(ss.getSn());
		}
        if(!ConvertUtil.isEmpty(ss.getStatus())){
			sql.append(" and ss.status = ? ");
			list.add(ss.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(isCoreStroe)){
			sql.append(" and s.is_core_stroe = ? ");
			list.add(isCoreStroe);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and ss.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and ss.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        sql.append(" GROUP BY ss.id order by ss.id desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }
    
    public Integer count(StoreStop ss, List<Object> param) {
    	StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Object storeId = param.get(0);
        Object isCoreStroe = param.get(1);
        String createName= (String) param.get(2);
        String firstTime = (String) param.get(3);
        String lastTime = (String) param.get(4);
        sql.append(" SELECT count(distinct ss.id) from xx_store_stop ss ");
        sql.append(" LEFT JOIN xx_store s ON s.id = ss.store ");
        sql.append(" LEFT JOIN xx_sale_org so ON so.id = s.sale_org ");
        sql.append(" LEFT JOIN xx_store_member sm ON sm.username = ss.b_creater ");
        sql.append(" where 1=1 ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and ss.company_info_id = ? ");
			list.add(companyInfoId);
		}
        if(!ConvertUtil.isEmpty(ss.getSn())){
			sql.append(" and ss.sn = ?");
			list.add(ss.getSn());
		}
        if(!ConvertUtil.isEmpty(ss.getStatus())){
			sql.append(" and ss.status = ? ");
			list.add(ss.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(isCoreStroe)){
			sql.append(" and s.is_core_stroe = ? ");
			list.add(isCoreStroe);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and ss.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and ss.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findInt(sql.toString(), objs);
	}
    

	public List<Map<String, Object>> findList(StoreStop ss, List<Object> param, Pageable pageable, Integer page,
                                              Integer size) {
    	StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Object storeId = param.get(0);
        Object isCoreStroe = param.get(1);
        String createName= (String) param.get(2);
        String firstTime = (String) param.get(3);
        String lastTime = (String) param.get(4);
        sql.append(" SELECT ss.* ");
        sql.append(" ,s.`name` store_name,s.dealer_name dealer_names,s.grant_code,s.alias,s.region, ");
        sql.append("( CASE s.is_core_stroe WHEN 1 THEN '是' WHEN 0 THEN '否' END ) is_core");
        sql.append(" ,sm.`name` create_name ");
        sql.append(" from xx_store_stop ss ");
        sql.append(" LEFT JOIN xx_store s ON s.id = ss.store ");
        sql.append(" LEFT JOIN xx_sale_org so ON so.id = s.sale_org ");
        sql.append(" LEFT JOIN xx_store_member sm ON sm.username = ss.b_creater ");
        sql.append(" where 1=1 ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and ss.company_info_id = ? ");
			list.add(companyInfoId);
		}
        if(!ConvertUtil.isEmpty(ss.getSn())){
			sql.append(" and ss.sn = ?");
			list.add(ss.getSn());
		}
        if(!ConvertUtil.isEmpty(ss.getStatus())){
			sql.append(" and ss.status = ? ");
			list.add(ss.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(isCoreStroe)){
			sql.append(" and s.is_core_stroe = ? ");
			list.add(isCoreStroe);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and ss.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and ss.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        sql.append(" GROUP BY ss.id order by ss.id desc");
        if (page != null && size != null) {
        	sql.append(" limit " + (size * (page - 1)) + "," + size);
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

}
