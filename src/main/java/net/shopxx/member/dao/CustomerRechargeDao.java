package net.shopxx.member.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Repository("customerRechargeDao")
public class CustomerRechargeDao extends DaoCenter{
	
	
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	
	
	public List<Map<String, Object>> findCustomerRechargeList(Long storeId,
			Long organizationId,Long sbuId){
	
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" SELECT org.id organization_id,org.name organization_name,ifnull(cre.actual_amount,0) credit_balance,cr.order_balance, ");
		sql.append(" cr.balance+cr.policy_balance+cr.return_balance-cr.shipment_balance-cr.order_balance balance,cr.return_balance,  ");
		sql.append(" cr.balance+cr.policy_balance+cr.return_balance-cr.shipment_balance-cr.order_balance+ifnull(cre.actual_amount,0) available_balance  ");
		sql.append(" FROM xx_customer_recharge cr  ");
		sql.append(" JOIN xx_store s ON cr.store = s.id  ");
		sql.append(" JOIN xx_organization org ON cr.organization = org.id  ");
		sql.append(" JOIN xx_sbu su ON su.id = cr.sbu ");
		sql.append(" LEFT JOIN (SELECT cre.store,cre.organization,cre.sbu,SUM(cre.actual_amount) actual_amount  ");
		sql.append(" FROM xx_credit_recharge cre WHERE cre.doc_status = 2 AND cre.start_date <= NOW() AND NOW() <  DATE_ADD(cre.end_date, INTERVAL 1 DAY) ");
		//客户
		if (!ConvertUtil.isEmpty(storeId)) {
			sql.append(" AND cre.store  = ?");
			list.add(storeId);
		} 
		//用户经营组织权限
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and cre.organization in (" + organizationIdS + ")");
			}else{
				sql.append(" and cre.organization is null");
			}
		}
		//经营组织
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" AND cre.organization  = ?");
			list.add(organizationId);
		}
		//sbu
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" AND cre.sbu  = ?");
			list.add(sbuId);
		}
		sql.append(" GROUP BY cre.store,cre.organization,cre.sbu) cre ON cre.store = s.id AND cre.sbu = su.id AND cre.organization = org.id ");
		sql.append(" WHERE 1=1 ");
		//客户
		if (!ConvertUtil.isEmpty(storeId)) {
			sql.append(" AND s.id = ?");
			list.add(storeId);
		}
		//用户经营组织
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and org.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and org.id is null");
			}
		}
		//经营组织
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" AND org.id  = ?");
			list.add(organizationId);
		}
		//sbu
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" AND su.id  = ?");
			list.add(sbuId);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> mapList = getNativeDao().findListMap(
				sql.toString(),
				objs, 
				0);
		return mapList;	
	}

}
