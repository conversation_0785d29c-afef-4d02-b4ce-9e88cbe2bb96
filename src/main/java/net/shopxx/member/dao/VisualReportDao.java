package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;

@Repository("visualReportDao")
public class VisualReportDao extends DaoCenter {
	
	/**平台销量*/
	public List<Map<String, Object>> findTaskCompletion(List<Long> sbu,List<Long> saleOrg,List<Long> store) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT v.机构 name,sum( v.`发货数量` ) value FROM v_link_shipping v WHERE	1=1 ");

		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND v.company_info_id = "+companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sbu)){
			String os = "";
			for (int i = 0; i < sbu.size(); i++) {
				if (i == sbu.size() - 1)
					os += sbu.get(i);
				else
					os += sbu.get(i) + ",";
			}
			sql.append(" AND v.SBUID IN("+os+") ");
		}
		if(!ConvertUtil.isEmpty(saleOrg)&&saleOrg.size()>0){
			String os = "";
			for (int i = 0; i < saleOrg.size(); i++) {
				if (i == saleOrg.size() - 1)
					os += saleOrg.get(i);
				else
					os += saleOrg.get(i) + ",";
			}
			sql.append(" AND v.`机构ID` IN("+os+")	");
		}
		if(!ConvertUtil.isEmpty(store)&&store.size()>0){
			String os = "";
			for (int i = 0; i < store.size(); i++) {
				if (i == store.size() - 1)
					os += store.get(i);
				else
					os += store.get(i) + ",";
			}
			sql.append(" AND v.`客户ID` IN("+os+")	");
		}
		sql.append(" AND v.`单据状态` IN ( '完全发货', '部分发货' ) ");
		sql.append(" GROUP BY v.`机构` ORDER BY SUM( v.`发货数量` ) DESC ");
		return getNativeDao().findListMap(sql.toString(), null, 20);
	}
	
	/**品类销量*/
	public List<Map<String, Object>> findTaskCompletionCategory(List<Long> sbu,List<Long> saleOrg,List<Long> store) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT v.`品类` name,SUM( v.`发货数量` ) value FROM v_link_shipped v WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND v.company_info_id = "+companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sbu)){
			String os = "";
			for (int i = 0; i < sbu.size(); i++) {
				if (i == sbu.size() - 1)
					os += sbu.get(i);
				else
					os += sbu.get(i) + ",";
			}
			sql.append(" AND v.SBUID IN("+os+") ");
		}
		if(!ConvertUtil.isEmpty(saleOrg)&&saleOrg.size()>0){
			String os = "";
			for (int i = 0; i < saleOrg.size(); i++) {
				if (i == saleOrg.size() - 1)
					os += saleOrg.get(i);
				else
					os += saleOrg.get(i) + ",";
			}
			sql.append(" AND v.`机构ID` IN("+os+")	");
		}
		if(!ConvertUtil.isEmpty(store)&&store.size()>0){
			String os = "";
			for (int i = 0; i < store.size(); i++) {
				if (i == store.size() - 1)
					os += store.get(i);
				else
					os += store.get(i) + ",";
			}
			sql.append(" AND v.`客户ID` IN("+os+")	");
		}
		sql.append(" AND v.`单据状态` IN ( '完全发货', '部分发货' ) ");

		sql.append(" GROUP BY v.`品类` order by sum( v.`发货数量` ) desc ");
		return getNativeDao().findListMap(sql.toString(), null, 20);
	}
	
	/**产品销量*/
	public List<Map<String, Object>> findTaskCompletionProduct(List<Long> sbu,List<Long> saleOrg,List<Long> store) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT v.`预留4` name,SUM( v.`发货数量` )  value FROM v_link_shipped v WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND v.company_info_id = "+companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sbu)){
			String os = "";
			for (int i = 0; i < sbu.size(); i++) {
				if (i == sbu.size() - 1)
					os += sbu.get(i);
				else
					os += sbu.get(i) + ",";
			}
			sql.append(" AND v.SBUID IN("+os+") ");
		}
		if(!ConvertUtil.isEmpty(saleOrg)&&saleOrg.size()>0){
			String os = "";
			for (int i = 0; i < saleOrg.size(); i++) {
				if (i == saleOrg.size() - 1)
					os += saleOrg.get(i);
				else
					os += saleOrg.get(i) + ",";
			}
			sql.append(" AND v.`机构ID` IN("+os+")	");
		}
		if(!ConvertUtil.isEmpty(store)&&store.size()>0){
			String os = "";
			for (int i = 0; i < store.size(); i++) {
				if (i == store.size() - 1)
					os += store.get(i);
				else
					os += store.get(i) + ",";
			}
			sql.append(" AND v.`客户ID` IN("+os+")	");
		}
		sql.append(" AND v.`单据状态` IN ( '完全发货', '部分发货' ) ");

		sql.append(" GROUP BY v.`预留4` ORDER BY SUM( v.`发货数量` ) DESC ");
		return getNativeDao().findListMap(sql.toString(), null, 20);
	}
	
	/**客户销量*/
	public List<Map<String, Object>> findTaskCompletionStore(List<Long> sbu,List<Long> saleOrg,List<Long> store) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT v.`预留5` name,SUM( v.`发货数量` )  value FROM v_link_shipped v WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND v.company_info_id = "+companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sbu)){
			String os = "";
			for (int i = 0; i < sbu.size(); i++) {
				if (i == sbu.size() - 1)
					os += sbu.get(i);
				else
					os += sbu.get(i) + ",";
			}
			sql.append(" AND v.SBUID IN("+os+") ");
		}
		if(!ConvertUtil.isEmpty(saleOrg)&&saleOrg.size()>0){
			String os = "";
			for (int i = 0; i < saleOrg.size(); i++) {
				if (i == saleOrg.size() - 1)
					os += saleOrg.get(i);
				else
					os += saleOrg.get(i) + ",";
			}
			sql.append(" AND v.`机构ID` IN("+os+")	");
		}
		if(!ConvertUtil.isEmpty(store)&&store.size()>0){
			String os = "";
			for (int i = 0; i < store.size(); i++) {
				if (i == store.size() - 1)
					os += store.get(i);
				else
					os += store.get(i) + ",";
			}
			sql.append(" AND v.`客户ID` IN("+os+")	");
		}
		sql.append(" AND v.`单据状态` IN ( '完全发货', '部分发货' ) ");

		sql.append(" GROUP BY v.`预留5` ORDER BY SUM( v.`发货数量` ) DESC ");
		return getNativeDao().findListMap(sql.toString(), null, 20);
	}
	
	public List<Map<String, Object>> findList(Boolean isDefault) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM xx_visual_report v WHERE 1=1");

		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND v.company_info_id = ?");
			list.add(companyInfoId);
		}
		if(isDefault){
			sql.append(" AND is_default=true");
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		sql.append(" ORDER BY v.serial_number ASC ");
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

}
