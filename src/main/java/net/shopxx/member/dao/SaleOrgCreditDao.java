package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.util.RoleJurisdictionUtil;

import org.springframework.stereotype.Repository;

@Repository("saleOrgCreditDao")
public class SaleOrgCreditDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	

	/**
	 * 临时额度列表数据
	 */
	public Page<Map<String, Object>> findPage(String sn, Long storeMemberId,
			Long storeId, Integer[] status, Long creatorId, Long operatorId,
			Long sbuId,Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select cr.*, smc.username creator_name, smo.username operator_name,cri.amount amount,cri.memo itemMemo,so.name sale_org_name,");
		sql.append(" sb.name sbu_name,o.id organization_id,o.name organization_name,soc.id  sale_org_credit_type_id,soc.value sale_org_credit_type_name ");
		sql.append(" from xx_sale_org_credit_item cri");
		sql.append(" left join xx_sale_org_credit cr on cr.id = cri.sale_org_credit");
		sql.append(" left join xx_store_member smc on cr.creator = smc.id");
		sql.append(" left join xx_store_member smo on cr.operator = smo.id");
		sql.append(" left join xx_sbu sb on cr.sbu = sb.id");
		sql.append(" left join xx_sale_org so on cri.sale_org = so.id");
		sql.append(" left join xx_organization o on o.id = cr.organization ");
		sql.append(" left join xx_system_dict soc on soc.id = cr.sale_org_credit_type ");
		
		sql.append(" where 1 = 1");
		if (companyInfoId != null) {
			sql.append(" and cr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (creatorId != null) {
			sql.append(" and smc.id = ?");
			list.add(creatorId);
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (operatorId != null) {
			sql.append(" and smo.id = ?");
			list.add(operatorId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and cr.sn like ?");
			list.add("%" + sn + "%");
		}
		
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				String sqlString = "select s.sale_org from xx_store s where s.id  in (" + storeAuth + ")";
				sql.append(" and so.id in (" + sqlString + ")");
			}
		}

		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and cr.doc_status in (" + os + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and (sb.id is null or sb.id in (" + sbuIds + "))");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and (o.id is null or o.id in (" + organizationIdS + "))");
			}else{
				sql.append(" and o.id is null");
			}
		}
		
		
		sql.append(" order by cr.create_date desc");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> listItems = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return listItems;
	}

	public List<Map<String, Object>> findListBySaleOrg(Long saleOrgId) {
		if (saleOrgId == null) {
			ExceptionUtil.throwServiceException("机构不能为空");
		}
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("select sci.*,sc.total_amount totalAmount,sc.used_amount usedAmount,sc.id saleOrgCreditId from xx_sale_org_credit_item sci");
		sql.append(" left join  xx_sale_org_credit sc on sc.id = sci.sale_org_credit");
		sql.append(" where sc.doc_status = 2 and sc.type = 1 and sc.status =1 and sci.sale_org = ? ");
		if (companyInfoId != null) {
			sql.append("and sc.company_info_id = ?");
		}
		return getNativeDao().findListMap(sql.toString(),
				new Object[] { saleOrgId, companyInfoId },
				0);
	}

	public List<Map<String, Object>> findScItemList(Long saleOrgId,
			String saleOrgCreditIds, Boolean isDefault) {
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		sql.append("select sci.*,sc.total_amount totalAmount,sc.used_amount usedAmount,so.name sale_org_name,so.id sale_org_id from  xx_sale_org_credit_item sci");
		sql.append(" left join xx_sale_org_credit  sc on sc.id = sci.sale_org_credit");
		sql.append(" left join xx_sale_org so on sci.sale_org = so.id");
		sql.append(" where 1=1 and sc.doc_status in(0,1,2,3)");
		if (companyInfoId != null) {
			sql.append(" and sc.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (saleOrgId != null) {
			sql.append(" and sci.sale_org = ?");
			list.add(saleOrgId);
		}
		if (saleOrgCreditIds != null) {
			sql.append(" and sci.sale_org_credit in (" + saleOrgCreditIds + ")");
		}
		if(!ConvertUtil.isEmpty(isDefault) && isDefault){
			//用户机构
			String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
				if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
					sql.append(" and so.id in (" + saleOrgIds + ")");
				}else{
					sql.append(" and so.id is null");
				}
			}
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	public List<Map<String, Object>> checkScItemList(Long saleOrgId,
			Long saleOrgCreditId) {
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		sql.append("select sci.*,sc.total_amount totalAmount,sc.used_amount usedAmount,so.name sale_org_name,so.id sale_org_id from  xx_sale_org_credit_item sci");
		sql.append(" left join xx_sale_org_credit  sc on sc.id = sci.sale_org_credit");
		sql.append(" left join xx_sale_org so on sci.sale_org = so.id");
		sql.append(" where 1=1 and sc.doc_status in(0,1,2)");
		if (companyInfoId != null) {
			sql.append(" and sc.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (saleOrgId != null) {
			sql.append(" and sci.sale_org = ?");
			list.add(saleOrgId);
		}
		if (saleOrgCreditId != null) {
			sql.append(" and sci.sale_org_credit <> ? ");
			list.add(saleOrgCreditId);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
	
	
	public List<Map<String, Object>> findVSaleOrgCreditList(Long saleOrgId) {
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		sql.append(" SELECT * FROM v_sale_org_credit soc "
				+ "  WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and soc.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and soc.so_id = ?");
			list.add(saleOrgId);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),objs,0);
		return mapList;
	}
	
	public List<Map<String, Object>> findVSaleOrgCreditCheckList(Long saleOrgId,
			Long sbuId,Long organizationId) {
		
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		sql.append(" SELECT SUM(socc.Quantity) totalQuantity,SUM(socc.amount) totalAmount "
				+ "  FROM v_sale_org_credit_check socc "
				+ "  WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and socc.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and socc.id = ?");
			list.add(saleOrgId);
		}
		if (!ConvertUtil.isEmpty(sbuId)) {
			sql.append(" and socc.sbuId = ?");
			list.add(sbuId);
		}
		if (!ConvertUtil.isEmpty(organizationId)) {
			sql.append(" and socc.organizationId = ?");
			list.add(organizationId);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		List<Map<String, Object>> mapList = getNativeDao().findListMap(sql.toString(),objs,0);
		return mapList;
	}
	
}