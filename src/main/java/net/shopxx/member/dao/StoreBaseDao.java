package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.Store.Type;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.template.service.impl.DTemplateParametersServiceImpl;
import net.shopxx.util.RoleJurisdictionUtil;

/**
 * Dao - 客户
 */
@Repository("storeBaseDao")
public class StoreBaseDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "storeMemberBaseDao")
    private StoreMemberBaseDao storeMemberBaseDao;

    private static final Logger logger = LoggerFactory.getLogger(DTemplateParametersServiceImpl.class);


	/**
	 * 获取默认客户
	 * 
	 * @return
	 */
	public Store getMainStore(Long companyInfoId) {

		Store store = null;
		if (companyInfoId != null) {

			String sql = "select * from xx_store where is_main_store = 1 and company_info_id = ?";
			store = getNativeDao().findSingleManaged(sql, new Object[] { companyInfoId }, Store.class);
		} else {
			String sql = "select * from xx_store where is_main_store = 1 and company_info_id= 9";
			store = getNativeDao().findSingleManaged(sql, null, Store.class);
		}
		return store;
	}

	public Page<Map<String, Object>> findPage(String name, String sn, String outTradeNo, String mobile,
			String serviceTelephone, Integer[] type, String outShopName, Long saleOrgId, Long companyInfoId,
			Long memberRankId, String createBy, String saleOrgName, Boolean isEnabled, Integer isSelect,
			Integer isCustomer, String alias, String grantCode, Long storeMemberId, String region,
			Long[] distributorStatusId, String headAddress,String user, Pageable pageable, Long sbuId) {
		
		List<Object> list = new ArrayList<Object>();
		String sql = "select s.*,mr.name rank_name,a.full_name area_full_name,a.tree_path area_tree_path,sd.value,"
				+ "  xsa.address store_address,xsa.area  store_area,xsa.consignee store_consignee,sm.name store_member_name,sm.id store_member_id,"
				+ " xsa.mobile store_mobile,xsa.zip_code store_zip_code ,sd.id business_type_id,sd.value business_type_value,sdd.value customer_type_value,"
				+ " sdd.id customer_type_id,so.id sale_org_id,so.name sale_org_name,s.region sale_org_region,ha.id ha_id,ha.full_name harea_full_name,ha.tree_path harea_tree_path "
				+ " from xx_store s " 
				+ " left join xx_member_rank mr on s.member_rank=mr.id "
				+ " left join xx_store_address xsa on xsa.store=s.id and xsa.is_default=1 and (xsa.is_invalid is null or xsa.is_invalid!=1)  "
				+ " left join xx_system_dict sd on sd.id=s.business_type  "
				+ " left join xx_store_member sm on s.store_member=sm.id "
				+ " left join xx_store_member sc on s.create_by=sc.id "
				+ " left join xx_system_dict sdd on sdd.id=s.customer_type  " 
				+ " left join xx_area a on xsa.area=a.id "
				+ " left join xx_area ha on s.head_new_area = ha.id "
				+ " left join xx_system_dict ds on s.distributor_status = ds.id "
				+ " left join xx_sale_org so on so.id = s.sale_org "
				+ " left join xx_store_sbu ss on s.id = ss.store "
				+ " left join xx_sbu sb on sb.id = ss.sbu "
				+ " where 1=1";

		sql += " and s.type !=" + Type.enterprise.ordinal();

		if (isCustomer != null) {
			sql += " and sdd.value like '%经销商%'";
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql += " and (s.name like '%" + name + "%' or s.alias like '%" + name + "%' ) ";
		}
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql += " and (so.id=" + saleOrgId + " or so.tree_path like '%," + saleOrgId + ",%')";
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql += " and s.sn like ?";
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(alias)) {
			sql += " and s.alias like ?";
			list.add("%" + alias + "%");
		}
		if (!ConvertUtil.isEmpty(createBy)) {
			sql += " and sc.name like ?";
			list.add("%" + createBy + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql += " and s.out_trade_no like ?";
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(grantCode)) {
			sql += " and s.grant_code like ?";
			list.add("%" + grantCode + "%");
		}
		if (!ConvertUtil.isEmpty(mobile)) {
			sql += " and s.mobile like ?";
			list.add("%" + mobile + "%");
		}
		if (!ConvertUtil.isEmpty(serviceTelephone)) {
			sql += " and s.service_telephone like ?";
			list.add("%" + serviceTelephone + "%");
		}
		if (!ConvertUtil.isEmpty(outShopName)) {
			sql += " and s.out_shop_name like ?";
			list.add("%" + outShopName + "%");
		}
		if (!ConvertUtil.isEmpty(headAddress)) {
			sql += " and s.head_address like ?";
			list.add("%" + headAddress + "%");
		}
		if (!ConvertUtil.isEmpty(region)) {
			sql += " and s.region like ?";
			list.add("%" + region + "%");
		}
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql += " and s.customer_type in (" + os + ")";
		}
		if (distributorStatusId != null && distributorStatusId.length > 0) {
			String os = "";
			for (int i = 0; i < distributorStatusId.length; i++) {
				if (i == distributorStatusId.length - 1)
					os += distributorStatusId[i];
				else
					os += distributorStatusId[i] + ",";
			}
			sql += " and ds.id in (" + os + ")";
		}
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if ((storeMember.getMemberType() == 1 || storeMember.getIsSalesman())&&user==null) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql += " and (s.id in (" + storeAuth + ") or s.store_member = ?) ";
				list.add(WebUtils.getCurrentStoreMemberId());
			}
		}
		// 选择客户
		if (isSelect != null) { 
			sql += " and (so.id=" + saleOrgId + " or so.tree_path like '%," + saleOrgId + ",%')";
		} else {
			if (saleOrgId != null) {
				sql += " and s.sale_org = ?";
				list.add(saleOrgId);
			}
		}
		if (companyInfoId != null) {
			sql += " and s.company_info_id = ?";
			list.add(companyInfoId);
		}
		if (memberRankId != null) {
			sql += " and mr.id = ?";
			list.add(memberRankId);
		}
		if (storeMemberId != null) {
			sql += " and sm.id = ?";
			list.add(storeMemberId);
		}
		if (isEnabled != null) {
			if (isEnabled) {
				sql += " and s.is_enabled = 1";
			} else {
				sql += " and s.is_enabled = 0";
			}
		}
		if(!ConvertUtil.isEmpty(sbuId)){
			sql += " and sb.id = ?";
			list.add(sbuId);
		}

		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql += " and so.id in (" + saleOrgIds + ") ";
			}else{
				sql += " and so.id is null ";
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql += " and (sb.id is null or sb.id in (" + sbuIds + "))";
			}else{
				sql += " and sb.id is null ";
			}
		}
		
		sql += " group by s.id order by s.modify_date desc";
		
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql, objs, pageable);
		String totalsql = "select count(1) from ( " + sql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}

	public List<Map<String, Object>> findMobileData(Map<String, Object> param) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StoreMember storeMember = storeMemberBaseService.getCurrent();

		String sql = " select s.*,mr.name rank_name,a.full_name area_full_name,a.tree_path area_tree_path,sd.value,"
				+ "     xsa.address store_address,xsa.area  store_area,xsa.consignee store_consignee,sm.name store_member_name,sm.id store_member_id,"
				+ "     xsa.mobile store_mobile,xsa.zip_code store_zip_code ,sd.id business_type_id,sd.value business_type_value,sdd.value customer_type_value,"
				+ "     sdd.id customer_type_id,so.id sale_org_id,so.name sale_org_name,ha.id ha_id,ha.full_name harea_full_name,ha.tree_path harea_tree_path "
				+ " from xx_store s " + " left join xx_member_rank mr on s.member_rank=mr.id "
				+ " left join xx_store_address xsa on xsa.store=s.id and xsa.is_default=1 and (xsa.is_invalid is null or xsa.is_invalid!=1)  "
				+ " left join xx_system_dict sd on sd.id=s.business_type  "
				+ " left join xx_store_member sm on s.store_member=sm.id "
				+ " left join xx_store_member sc on s.create_by=sc.id "
				+ " left join xx_system_dict sdd on sdd.id=s.customer_type  " + " left join xx_area a on xsa.area=a.id "
				+ " left join xx_area ha on s.head_new_area = ha.id "
				+ " left join xx_system_dict ds on s.distributor_status = ds.id "
				+ " left join xx_sale_org so on so.id = s.sale_org where 1=1";

		sql += " and s.type !=" + Type.enterprise.ordinal();
		if (param.get("distributorStatusId") != null) {
			sql += " and ds.id in (" + param.get("distributorStatusId").toString() + ")";
		}

        if(param.get("name") != null){
            sql += " and s.name like ? ";
            list.add("%" +param.get("name")+"%");

        }

		/*if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql += " and s.id in (" + storeAuth + ") or s.store_member = ?";
				list.add(WebUtils.getCurrentStoreMemberId());
			}
			
		}*/
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				String str = storeMemberBaseService.findSalesman(storeMember.getId());
				sql+=" and (s.id in (" + storeAuth + ") or s.store_member in("+str+") ) ";
			}
		}else {
			sql += " and (s.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))";
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
		}

		/*
		 * if (distributorStatusId != null && distributorStatusId.length > 0) {
		 * String os = ""; for (int i = 0; i < distributorStatusId.length; i++)
		 * { if (i == distributorStatusId.length - 1) os +=
		 * distributorStatusId[i]; else os += distributorStatusId[i] + ","; }
		 * sql += " and ds.id in (" + os + ")"; }
		 */
		/*
		 * StoreMember storeMember =
		 * storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId()); if
		 * (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
		 * String storeAuth = storeMemberBaseService.storeAuth(); if (storeAuth
		 * != null) { sql += " and s.id in (" + storeAuth + ")"; } } else { sql
		 * +=
		 * " and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
		 * + " where smo.sale_org = s.id and smo.store_member = ?) " +
		 * " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b " +
		 * " where a.tree_path like concat('%,', b.id, ',%') " +
		 * " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
		 * + " where smo.sale_org = s.id and smo.store_member = ?)))";
		 * list.add(WebUtils.getCurrentStoreMemberId());
		 * list.add(WebUtils.getCurrentStoreMemberId()); }
		 */
		/*
		 * if (isSelect != null) { // 选择客户 sql += " and (so.id=" + saleOrgId +
		 * " or so.tree_path like '%," + saleOrgId + ",%')"; } else { if
		 * (saleOrgId != null) { sql += " and s.sale_org = ?";
		 * list.add(saleOrgId); } }
		 */

		if (companyInfoId != null) {
			sql += " and s.company_info_id = ?";
			list.add(companyInfoId);
		}
		/*if (storeMember.getId() != null) {
			sql += " and sm.id = ?";
			list.add(storeMember.getId());
		}*/


		if (param.get("saleOrgId") != null) {
			sql += " or so.id in (" + param.get("saleOrgId").toString() + ")";
		}


		/*
		 * if (isEnabled != null) { if (isEnabled) { sql +=
		 * " and s.is_enabled = 1"; } else { sql += " and s.is_enabled = 0"; } }
		 */
		sql += " group by s.id order by s.modify_date desc";

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		return getNativeDao().findListMap(sql, list.toArray(), 0);
	}

	public Page<Map<String, Object>> findStorePageWithBalance(String name, String outTradeNo, String grantCode,
			Pageable pageable) {

		Integer isMember;
		if (WebUtils.isAdmin()) {
			isMember = null;
		} else {
			isMember = 1;
		}

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();

		sql.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sql.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");

		Date now = new Date();
		sql.setLength(0);
		sql.append("select");
		sql.append(" s.*,");
		sql.append("mr.name rank_name,ar.full_name area_full_name,ar.tree_path area_tree_path,"
				+ "  xsa.address store_address,xsa.area  store_area,xsa.consignee store_consignee,"
				+ " xsa.mobile store_mobile,xsa.zip_code store_zip_code ,"
				+ " so.id sale_org_id,so.name sale_org_name,");

		sql.append("  ifnull(cr.credit_amount,0) credit_amount,");
		sql.append("  ifnull(dr.deposit_amount,0) deposit_amount,");
		sql.append("  ifnull(fee.fee_amount,0) fee_amount,");
		sql.append("  ifnull(o.amount_paid,0) amount_paid,");
		sql.append("  ifnull(r.return_amount,0) return_amount,");
		sql.append("  ifnull(sb.begin_amount,0) begin_amount,");
		sql.append(
				"  (ifnull(sb.begin_amount,0) + ifnull(dr.deposit_amount,0) + ifnull(fee.fee_amount,0) + ifnull(r.return_amount,0) - ifnull(o.amount_paid,0)) end_amount,");
		sql.append(
				"  (ifnull(sb.begin_amount,0) + ifnull(cr.credit_amount,0) + ifnull(dr.deposit_amount,0) + ifnull(fee.fee_amount,0) + ifnull(r.return_amount,0) - ifnull(o.amount_paid,0)) store_balance");
		sql.append(" from xx_store s");
		sql.append(" left join");
		sql.append("  xx_store_balance sb on s.id = sb.store and sb.company_info_id = ? and sb.balance_date = ?");
		sql.append(" left join");
		sql.append("  (select a.store, sum(ifnull(a.actual_amount, 0)) credit_amount");
		sql.append("  from xx_credit_recharge a");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");
		sql.append("  group by a.store) cr on cr.store = s.id");
		sql.append(" left join");
		sql.append("  (select a.stores, sum(ifnull(a.actual_amount, 0)) deposit_amount");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b");
		sql.append("  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id and b.value <> '费用转货款'");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		sql.append("  group by a.stores) dr on dr.stores = s.id");
		sql.append(" left join");
		sql.append("  (select a.stores, sum(ifnull(a.actual_amount, 0)) fee_amount");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b");
		sql.append("  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id and b.value = '费用转货款'");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		sql.append("  group by a.stores) fee on fee.stores = s.id");
		sql.append(" left join");
		sql.append("  (select a.stores, sum(b.price * b.quantity) amount_paid");
		sql.append("  from xx_order a, xx_order_item b");
		sql.append("  where a.id = b.orders and a.order_status in (5, 6) and a.order_type = 2");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append("  group by a.stores) o on o.stores = s.id");
		sql.append(" left join");
		sql.append("  (select a.store, sum(a.amount) return_amount");
		sql.append("  from xx_b2b_returns a");
		sql.append("  where a.status in (1,2,3)");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append("  group by a.store) r on r.store = s.id");

		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(now);
		list.add(now);
		list.add(companyInfoId);
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(companyInfoId);
		list.add(balanceDate);

		sql.append(" left join xx_member_rank mr on s.member_rank=mr.id "
				+ " left join xx_store_address xsa on xsa.store=s.id and xsa.is_default=1  "
				+ " left join xx_area ar on xsa.area=ar.id left join xx_sale_org so on so.id = s.sale_org");

		if (isMember != null) {
			sql.append(" left join xx_store_member sm  on s.id=sm.store where sm.member = ?");
			Member member = storeMemberBaseService.getCurrent().getMember();
			list.add(member.getId());
		} else {
			sql.append(" where 1=1 ");
		}

		sql.append(" and s.company_info_id = ? and s.is_main_store = 0");
		list.add(companyInfoId);

		sql.append(" and s.type !=" + Type.enterprise.ordinal());

		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}

		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and s.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(grantCode)) {
			sql.append(" and s.grant_code like ?");
			list.add("%" + grantCode + "%");
		}

		sql.append(" and s.is_enabled = 1");
		sql.append(" order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);

		return page;
	}

	public Integer count(String name, String sn, String outTradeNo, String mobile, String serviceTelephone,
			Integer[] type, String outShopName, Long saleOrgId, Long companyInfoId, Long memberRankId,
			Boolean isEnabled, Pageable pageable, Integer page, Integer size) {

		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("select count(s.id) from xx_store s  ");
		sql.append(" left join xx_member_rank mr on s.member_rank=mr.id");
		sql.append(" left join xx_sale_org so on so.id = s.sale_org");
		sql.append(" left join xx_store_sbu ss on s.id = ss.store");
		sql.append(" left join xx_sbu sb on sb.id = ss.sbu");
		sql.append(" where 1=1");
		
		sql.append(" and s.type !=" + Type.enterprise.ordinal());

		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and s.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and s.out_trade_no like ?");
			list.add("%" + outTradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(mobile)) {
			sql.append(" and s.mobile like ?");
			list.add("%" + mobile + "%");
		}
		if (!ConvertUtil.isEmpty(serviceTelephone)) {
			sql.append(" and s.service_telephone like ?");
			list.add("%" + serviceTelephone + "%");
		}
		if (!ConvertUtil.isEmpty(outShopName)) {
			sql.append(" and s.out_shop_name like ?");
			list.add("%" + outShopName + "%");
		}
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql.append(" and s.type in (" + os + ")");
		// 客户的查询不要查出供应商	
		} else {
			sql.append(" and s.type != 3 ");
		}
		if (saleOrgId != null) {
			sql.append(" and s.sale_org = ?");
			list.add(saleOrgId);
		}
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (memberRankId != null) {
			sql.append(" and mr.id = ?");
			list.add(companyInfoId);
		}
		if (isEnabled != null) {
			if (isEnabled) {
				sql.append(" and s.is_enabled = 1");
			} else {
				sql.append(" and s.is_enabled = 0");
			}
		}

		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and s.id in (" + storeAuth + ")");
			}
		} 
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and (sb.id is null or sb.id in (" + sbuIds + "))");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		sql.append(" group by s.id order by s.create_date desc");

		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}

	public List<Map<String, Object>> findItemList(String name,String sn, 
			String outTradeNo,String mobile,String serviceTelephone,
			Integer[] type,String outShopName,Long saleOrgId,Long companyInfoId,
			Long memberRankId,Boolean isEnabled,Long[] ids,Integer page,Integer size,
			Long storeMemberId,String region,Long[] distributorStatusId,String headAddress) {

		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(" select s.*,mr.name rank_name,a.full_name area_full_name,a.tree_path area_tree_path,a.id area_Id,"
				+ "  so.id sale_org_id,so.name sale_org_name,distributorStatus.value distributorStatusName,sbu.value sbuName, "
				+ "  stm.name store_member_name,salesPlatform.name sales_platform_name,ha.full_name head_area,"
				+ "  xsa.full_name head_salesarea,ha.tree_path harea_tree_path,ha.id harea_Id,bte.value businessTypeName " 
				+ "  from xx_store s "
				+ "  left join xx_member_rank mr on s.member_rank=mr.id ");
		sql.append(" left join xx_area a on s.area=a.id " 
				+ "  left join xx_area ha on s.head_new_area=ha.id "
				+ "  left join xx_sales_area  xsa on xsa.id=s.sales_area "
				+ "  left join xx_sale_org so on so.id = s.sale_org "
				+ "  left join xx_system_dict sbu on sbu.id = s.sbu "
				+ "  left join xx_store_sbu ss on s.id = ss.store "
				+ "  left join xx_sbu sb on sb.id = ss.sbu "	
				+ "  left join xx_sale_org salesPlatform on salesPlatform.id = s.sales_platform");
		sql.append(" left join xx_system_dict bte ON bte.id = s.business_type ");
		sql.append(" left join xx_store_member stm on stm.id=s.store_member ");
		sql.append(" left join xx_system_dict distributorStatus on s.distributor_status=distributorStatus.id ");
		sql.append(" where 1=1");

		sql.append(" and s.type !=" + Type.enterprise.ordinal());
		
		
		//机构
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and s.sale_org = ?");
			list.add(saleOrgId);
		}
		//区域经理
		if (!ConvertUtil.isEmpty(storeMemberId)) {
			sql.append(" and stm.id = ? ");
			list.add(storeMemberId);
		}
		//客户编码
		if (!ConvertUtil.isEmpty(outTradeNo)) {
			sql.append(" and s.out_trade_no like ? ");
			list.add("%" + outTradeNo + "%");
		}
		//客户名称
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and s.name like ? ");
			list.add("%" + name + "%");
		}
		//区域
		if (!ConvertUtil.isEmpty(region)) {
			sql.append(" and s.region like ? ");
			list.add("%" + region + "%");
		}
		//经销商状态
		if (distributorStatusId != null && distributorStatusId.length > 0) {
			String os = "";
			for (int i = 0; i < distributorStatusId.length; i++) {
				if (i == distributorStatusId.length - 1)
					os += distributorStatusId[i];
				else
					os += distributorStatusId[i] + ",";
			}
			sql.append(" and distributorStatus.id in (" + os + ") ");
		}
		//经销商地址
		if (!ConvertUtil.isEmpty(headAddress)) {
			sql.append(" and s.head_address like ? ");
			list.add("%" + headAddress + "%");
		}
		//价格类型
		if (!ConvertUtil.isEmpty(memberRankId)) {
			sql.append(" and mr.id = ?");
			list.add(memberRankId);
		}
	
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and s.sn like ?");
			list.add("%" + sn + "%");
		}
		
		if (!ConvertUtil.isEmpty(mobile)) {
			sql.append(" and s.mobile like ?");
			list.add("%" + mobile + "%");
		}
		if (!ConvertUtil.isEmpty(serviceTelephone)) {
			sql.append(" and s.service_telephone like ?");
			list.add("%" + serviceTelephone + "%");
		}
		if (!ConvertUtil.isEmpty(outShopName)) {
			sql.append(" and s.out_shop_name like ?");
			list.add("%" + outShopName + "%");
		}
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql.append(" and s.type in (" + os + ")");
		} else {
			sql.append(" and s.type !=3");
		}
		
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		
		if (isEnabled != null) {
			if (isEnabled) {
				sql.append(" and s.is_enabled = 1");
			} else {
				sql.append(" and s.is_enabled = 0");
			}
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and s.id in (" + inIds + ")");
		}
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and s.id in (" + storeAuth + ")");
			}
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and (sb.id is null or sb.id in (" + sbuIds + "))");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		sql.append(" group by s.id order by s.create_date desc");
		
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
	
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);

		return maps;
	}

	public Page<Map<String, Object>> findStoreAddressPage(String mobile, String consignee, Long storeId, String address,String areaName,
			Pageable pageable) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT t.*,xar.tree_path, xar.full_name area_full_name,xsa.full_name sales_area_full_name,xsa.geography_type geographyType,"
				+ " xsa.logic_type logicType,xsa.erp_sn erpSn, xsa.id  salesAreaId " + " FROM xx_store_address  t"
				+ " LEFT JOIN xx_area xar on xar.id=t.area " + " LEFT JOIN xx_sales_area xsa on xsa.id= t.sales_area "
				+ " WHERE 1=1 and (t.is_invalid is null or t.is_invalid!=1) ";
		if (storeId != null) {
			sql += " and t.store=" + storeId;
		}
		if (companyInfoId != null) {
			sql += " and t.company_info_id=" + companyInfoId;
		}
		if (address != null) {
			sql += " and t.address like '%" + address + "%'";
		}
		//添加地区搜索
		if(areaName != null) {
			sql += " and xar.full_name like '%" + areaName + "%'";
		}
		
		if (consignee != null) {
			sql += " and t.consignee like '%" + consignee + "%'";
		}
		if (mobile != null) {
			sql += " and t.mobile like '%" + mobile + "%'";
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql, null, pageable);
		return page;
	}

	public List<Map<String, Object>> findStoreAddressList(Long storeId) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT t.*,xar.tree_path, xar.full_name area_full_name,xsa.full_name sales_area_full_name, xsa.id  salesAreaId FROM xx_store_address  t"
				+ " LEFT JOIN xx_area xar on xar.id=t.area " + " LEFT JOIN xx_sales_area xsa on xsa.id= t.sales_area  "
				+ "WHERE 1=1  and (t.is_invalid is null or t.is_invalid!=1)  ";
		if (storeId != null) {
			sql += " and t.store=" + storeId;
		}
		if (companyInfoId != null) {
			sql += " and t.company_info_id=" + companyInfoId;
		}
		// System.out.println(sql);
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql, null, 0);
		return lists;
	}

	/**
	 * 获取默认客户
	 * 
	 * @return
	 */
	public Store findStoreByOutTradeNo(String outTradeNo, Long companyInfoId) {

		String sql = "select * from xx_store where out_trade_no = ? and company_info_id = ?";
		Store store = getNativeDao().findSingleManaged(sql, new Object[] { outTradeNo, companyInfoId }, Store.class);
		return store;
	}

	public List<Map<String, Object>> findListByMember(Long memberId) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select s.*,m.name rank_name");
		sql.append(" from xx_store s");
		sql.append(" left join xx_member_rank m on m.id=s.member_rank");
		sql.append(" left join xx_store_member sm on sm.store=s.id");
		sql.append(" where s.type!=0");

		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (memberId != null) {
			sql.append(" and sm.member = ?");
			list.add(memberId);
		}
		sql.append(" order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);

		return maps;
	}

	public List<Map<String, Object>> findBusinessCategory(Store store) {

		StringBuilder sql = new StringBuilder();
		sql.append(" select p1.id id,p1.name name,p2.id cid,p2.name cname from xx_business_category c ");
		sql.append(" left join xx_product_category p1  on p1.id =c.product_big_type");
		sql.append(" left join xx_product_category p2 on p2.id = c.product_center_type ");
		sql.append(" where c.store = " + store.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findBusinessRecord(Store store) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select b.* from xx_business_record b  where b.store = " + store.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findCautionMoney(Store store) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select c.* from xx_caution_money c  where c.store = " + store.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findStoreContract(Store store) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select sc.* from xx_store_contract sc  where sc.store = " + store.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	/**
	 * 根据id查找对应的附件信息
	 */
	public List<Map<String, Object>> findListByStoreId(Long id) {
		List<Object> list = new ArrayList<Object>();
		String sql = "select * from xx_store_attach where stores=?";
		list.add(id);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}

	/**
	 * 查找外部编码最大流水号
	 * 
	 * @return
	 */
	public Integer findMaxSn() {
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append(" select ifnull(max(max_sn),100000) from xx_store   where company_info_id = " + companyInfoId);
		return getNativeDao().findInt(sql.toString());
	}

	/**
	 * 查找合作单位
	 */
	public List<Map<String, Object>> findStoreCooperation(Store store) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select sc.* from xx_store_cooperation sc  where sc.store = " + store.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findStore(String startTime, String endTime, String currentTime) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		sql.append("SELECT * FROM xx_store s WHERE 1=1");

		if (companyInfoId != null) {
			sql.append(" AND s.company_info_id = ? ");
			list.add(companyInfoId);
		}

		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" AND s.modify_date >= ?");
			list.add(DateUtil.convert(startTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" AND s.modify_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endTime + " 23:59:59"), 1));
		}

//		if (!ConvertUtil.isEmpty(currentTime)) {
//			sql.append(" AND s.modify_date LIKE '%" + currentTime + "%'");
//		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return this.getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	public List<Map<String, Object>> findStoreAddress(String startTime, String endTime, String currentTime) {

		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		sql.append("SELECT * FROM xx_store_address s WHERE 1=1");

		if (companyInfoId != null) {
			sql.append(" AND s.company_info_id = ? ");
			list.add(companyInfoId);
		}

		// if (!ConvertUtil.isEmpty(startTime)) {
		// sql.append(" AND s.modify_date >= '?' ");
		// list.add(startTime);
		// }
		//
		// if (!ConvertUtil.isEmpty(endTime)) {
		// sql.append(" AND s.modify_date <= '?' ");
		// list.add(endTime);
		// }

		if (!ConvertUtil.isEmpty(startTime)) {
			sql.append(" AND s.modify_date >= ?");
			list.add(DateUtil.convert(startTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(endTime)) {
			sql.append(" AND s.modify_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endTime + " 23:59:59"), 1));
		}

//		if (!ConvertUtil.isEmpty(currentTime)) {
//			sql.append(" AND s.modify_date LIKE '%" + currentTime + "%'");
//		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return this.getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	/**
	 * 获取默认Erp编码
	 * 
	 * @return
	 */
	public Store findStoreByErpNo(String outTradeNo) {

		String sql = "select * from xx_store where out_trade_no = ?";
		Store store = getNativeDao().findSingleManaged(sql, new Object[] { outTradeNo }, Store.class);
		return store;
	}

	public boolean findByRole(StoreMember storeMember, String roleName) {
		Long storeMemberId = storeMember.getId();
		String sql = "select count(*) from xx_pc_role  where id in " + "(select pc_role from xx_pc_user_role "
				+ "where company_info_id = 9 and store_member = " + storeMemberId + ")   and name = '" + roleName
				+ "' ";
		int count = getNativeDao().findInt(sql);
		return count > 0;
	}

	public List<Store> findListByDefaults(String name, int isDefault) {
		String sql = "SELECT * from xx_store st left join xx_store_address sta "
				+ "on st.id = sta.store where st.name = ? and sta.is_default = ?";
		List<Store> store = (List<Store>) getNativeDao().findSingleManaged(sql, new Object[] { name, isDefault },
				Store.class);
		return store;
	}

	public List<Map<String, Object>> findStoreAttach(Long id, Integer type) {
		StringBuilder sql = new StringBuilder();
		List<Object> list = new ArrayList<Object>();
		sql.append("SELECT * FROM xx_store_attach WHERE company_info_id = ? AND stores = ? AND type = ? ");
		list.add(WebUtils.getCurrentCompanyInfoId());
		list.add(id);
		list.add(type);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	

    //获取所有经营组织id
    public String findAllStore(){
        
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        Long StoreMemberId = WebUtils.getCurrentStoreMemberId();
        
        StoreMember storeMember = storeMemberBaseService.find(StoreMemberId);
                
        if(storeMember == null) {
            ExceptionUtil.throwControllerException("用户查询为空'");
        }
            
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        
        ///MemberType为1时，为外部用户，为0时则为内部 用户
        if(storeMember.getMemberType()==1||(storeMember.getMemberType()==0&&storeMember.getIsSalesman())){
            sql.append(" SELECT GROUP_CONCAT( DISTINCT s.id ) store_Ids ");
            sql.append(" from xx_store_member sm JOIN xx_store s ON sm.store = s.id and sm.company_info_id=s.company_info_id"
                    + " where s.is_main_store!=TRUE ");
            sql.append(" and  s.is_enabled = TRUE ");
            if(!ConvertUtil.isEmpty(companyInfoId)){
                sql.append(" AND sm.company_info_id = ? ");
                list.add(companyInfoId);
            }
            if(!ConvertUtil.isEmpty(StoreMemberId)){
                sql.append(" AND sm.id = ? ");
                list.add(StoreMemberId);
            }else {
                sql.append(" AND 1=2 ");
            }
        }else if(storeMember.getMemberType()==0) {
            return "-1 ) or (1 = 1 ";
        }else {
            return "-1";
        }
    

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        return getNativeDao().findString(sql.toString(), objs);
    }
}
