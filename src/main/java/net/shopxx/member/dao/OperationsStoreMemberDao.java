package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;

@Repository("operationsStoreMemberDao")
public class OperationsStoreMemberDao extends DaoCenter{
	
	/**
	 * 查找用户分页数据
	 * 
	 * @param username
	 * @param mobile
	 * @param name
	 * @param memberRankId
	 * @param startTime
	 * @param endTime
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		
		String username = (String) args[0];
		String mobile = (String) args[1];
		String name = (String) args[2];
		Long memberRankId = (Long) args[3];
		Boolean isPartner = (Boolean) args[4];
		Long storeId = (Long) args[5];
		Long saleOrgId = (Long) args[6];
		String startTime = (String) args[7];
		String endTime = (String) args[8];
		Integer memberType = (Integer) args[9];

		StringBuilder sql = new StringBuilder();
		sql.append("select s.*,mr.name rank_name,m.mobile,group_concat(distinct ss.name) store_name,");
		sql.append("rs.username recommend_username,so.name sale_org_name,group_concat(distinct a.name) pc_role_name,xs.name sbu_name");
		sql.append(" from xx_store_member s");
		sql.append(" left join xx_member m on s.member = m.id");
		sql.append(" left join xx_store ss on s.store=ss.id");
		sql.append(" left join xx_member_rank mr on s.member_rank=mr.id");
		sql.append(" left join xx_store_member rs on s.recommend_store_member = rs.id");
		//sql.append(" left join xx_store_member sm on s.member = sm.member and s.company_info_id = sm.company_info_id");
		//sql.append(" left join xx_store st on sm.store=st.id and st.is_main_store = 0");
		sql.append(" left join xx_store_member_sale_org smso on s.id=smso.store_member");
		sql.append(" left join xx_sale_org so on so.id=smso.sale_org");
		sql.append(" left join xx_pc_user_role uo on s.id = uo.store_member");
		sql.append(" left join xx_pc_role a on a.id=uo.pc_role");
		sql.append(" left join xx_store_member_sbu xss on s.id=xss.store_member");
		sql.append(" left join xx_sbu xs on xs.id=xss.sbu");
		sql.append(" where ss.is_main_store = 1");
		//角色
		if (args.length > 11) {
			Integer role = (Integer) args[11];
			if (role != null) {
				sql.append(" and a.name = '额度申请'");
			}
		}
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ? and ss.company_info_id = ? ");
			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		if (StringUtils.isNotEmpty(username)) {
			sql.append(" and s.username like ?");
			list.add("%" + username + "%");
		}
		if (StringUtils.isNotEmpty(mobile)) {
			sql.append(" and m.mobile like ?");
			list.add("%" + mobile + "%");
		}
		if (StringUtils.isNotEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}
		if (memberRankId != null) {
			sql.append(" and s.member_rank = ?");
			list.add(memberRankId);
		}
		if (isPartner != null) {
			if (isPartner) {
				sql.append(" and s.is_partner = 1");
			}
			else {
				sql.append(" and s.is_partner = 0");
			}
		}
		if (storeId != null) {
			sql.append(" and s.store = ?");
			list.add(storeId);
		}
		if (saleOrgId != null) {
			sql.append(" and smso.sale_org = ?");
			list.add(saleOrgId);
		}
		if (startTime != null) {
			sql.append(" and s.create_date >= ?");
			list.add(DateUtil.convert(startTime + " 00:00:00"));
		}
		if (endTime != null) {
			sql.append(" and s.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endTime + " 00:00:00"),
					1));
		}
		if (memberType != null) {
			sql.append(" and s.member_type = ?");
			list.add(memberType);
		}
		sql.append(" and so.id in(SELECT sale_org FROM xx_store_member_sale_org smso WHERE company_info_id = ? AND store_member = ? )");
		list.add(companyInfoId);
		list.add(WebUtils.getCurrentStoreMemberId());

		sql.append(" group by s.id order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		//System.out.println("sql:"+sql.toString());
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalsql = "select count(1) from ( " + sql + ") a";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}

}
