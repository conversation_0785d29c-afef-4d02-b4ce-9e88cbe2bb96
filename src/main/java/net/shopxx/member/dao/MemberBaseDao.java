package net.shopxx.member.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.member.entity.Member;

import org.springframework.stereotype.Repository;

/**
 * Dao - 会员
 */
@Repository("memberBaseDao")
public class MemberBaseDao extends DaoCenter {

	/**
	 * 根据用户名查找用户
	 * @param username
	 * @return
	 */
	public Member findByUsername(String username) {

		if (ConvertUtil.isEmpty(username)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		String sql = "select * from xx_member where username = ?";
		Member member = getNativeDao().findSingleManaged(sql,
				new Object[] { username },
				Member.class);
		return member;
	}

	/**
	 * 根据用户名查找用户
	 * @param username
	 * @return
	 */
	public Member findByMobile(String mobile) {

		if (ConvertUtil.isEmpty(mobile)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		String sql = "select m.* from xx_member m left join xx_store_member sm on sm.member = m.id"
				+ "  where  sm.member_type != 99 and sm.is_enabled =1 and m.mobile = ?";
		Member member = getNativeDao().findSingleManaged(sql,
				new Object[] { mobile },
				Member.class);
		return member;
	}

	/**
	 * 根据用户名或手机号查找用户
	 * @param username
	 * @param companyInfoId
	 * @return
	 */
	public Member findByUsernameOrMobileAndCompany(String username,
			Long companyInfoId) {

		if (ConvertUtil.isEmpty(username)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		String sql = "";
		Member member = null;
		if (companyInfoId == null) {
			sql = "select * from xx_member where username = ? or mobile = ? ";
			member = getNativeDao().findSingleManaged(sql,
					new Object[] { username, username },
					Member.class);
		}
		else {
			sql = "select * from xx_member where (username = ? or mobile = ?) and company_info_id = ?";
			member = getNativeDao().findSingleManaged(sql,
					new Object[] { username, companyInfoId },
					Member.class);
		}
		return member;
	}

	/**
	 * 判断手机号是否已存在
	 * @param mobile
	 * @return
	 */
	public boolean mobileExists(String mobile, Long memberId) {

		if (ConvertUtil.isEmpty(mobile)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		String sql = "select count(1) from xx_member where mobile = ?";
		Object[] objs = null;
		if (memberId != null) {
			sql += " and id <> ?";
			objs = new Object[] { mobile, memberId };
		}
		else {
			objs = new Object[] { mobile };
		}
		int count = getNativeDao().findInt(sql, objs);

		return count > 0;
	}
}