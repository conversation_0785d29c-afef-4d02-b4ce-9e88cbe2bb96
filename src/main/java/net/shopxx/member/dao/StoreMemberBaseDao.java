package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;

/**
 * Dao - 会员
 */
@Repository("storeMemberBaseDao")
public class StoreMemberBaseDao extends DaoCenter {

	/**
	 * 获取默认用户
	 */
	public List<StoreMember> findDefaultByUsername(String username, Long companyInfoId) {

		if (ConvertUtil.isEmpty(username)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		StringBuilder sql = new StringBuilder();
		sql.append("select sm.* from xx_store_member sm, xx_store s,xx_member m ");
		sql.append(" where sm.store = s.id and sm.member=m.id ");
		sql.append(" and s.is_enabled = 1 and s.is_main_store = 1");
		sql.append(" and ( sm.username = ? ");
		sql.append(" or sm.id_card = ? ");
		sql.append(" or m.mobile = ? )");
		Object[] objs = null;
		if (companyInfoId != null) {
			sql.append(" and sm.company_info_id = ?");
			objs = new Object[] { username, username, username, companyInfoId };
		} else {
			sql.append(" and sm.company_info_id is null");
			objs = new Object[] { username, username, username };
		}

		sql.append(" group by sm.id ");

		List<StoreMember> storeMembers = getNativeDao().findListManaged(sql.toString(), objs, 0, StoreMember.class);

		return storeMembers;
	}

	public StoreMember findByUsername(String username, Long companyInfoId) {

		if (ConvertUtil.isEmpty(username)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		StringBuilder sql = new StringBuilder();
		sql.append("select sm.* from xx_store_member sm, xx_store s,xx_member m ");
		sql.append(" where sm.store = s.id and sm.member=m.id ");
		sql.append(" and s.is_enabled = 1 and s.is_main_store = 1");
		sql.append(" and  sm.username = ? ");

		Object[] objs = null;
		if (companyInfoId != null) {
			sql.append(" and sm.company_info_id = ?");
			objs = new Object[] { username, companyInfoId };
		} else {
			sql.append(" and sm.company_info_id is null");
			objs = new Object[] { username };
		}

		sql.append(" group by sm.id ");

		StoreMember storeMember = getNativeDao().findSingleManaged(sql.toString(), objs, StoreMember.class);

		return storeMember;
	}

	/**
	 * 获取默认用户
	 * 
	 * @param memberId
	 * @param companyInfoId
	 * @return
	 */
	public StoreMember findDefaultByMember(Long memberId, Long companyInfoId) {

		if (ConvertUtil.isEmpty(companyInfoId) || ConvertUtil.isEmpty(memberId)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_store_member sm, xx_store s");
		sql.append(" where sm.store = s.id");
		sql.append(" and s.is_enabled = 1 and s.is_main_store = 1");
		sql.append(" and sm.member = ? and sm.company_info_id = ?");

		StoreMember storeMember = getNativeDao().findSingleManaged(sql.toString(),
				new Object[] { memberId, companyInfoId }, StoreMember.class);
		return storeMember;
	}

	/**
	 * 获取用户客户关系
	 * 
	 * @param memberId
	 * @param companyInfoId
	 * @return
	 */
	public List<StoreMember> findNotDefaultByMember(Long memberId, Long companyInfoId) {

		if (ConvertUtil.isEmpty(companyInfoId) || ConvertUtil.isEmpty(memberId)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_store_member sm, xx_store s");
		sql.append(" where sm.store = s.id");
		sql.append(" and s.is_enabled = 1 and s.is_main_store = 0");
		sql.append(" and sm.member = ? and sm.company_info_id = ?");

		List<StoreMember> storeMembers = getNativeDao().findListManaged(sql.toString(),
				new Object[] { memberId, companyInfoId }, 0, StoreMember.class);

		return storeMembers;
	}

	/**
	 * 查找用户分页数据
	 * 
	 * @param username
	 * @param mobile
	 * @param name
	 * @param memberRankId
	 * @param startTime
	 * @param endTime
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		String username = (String) args[0];
		String mobile = (String) args[1];
		String name = (String) args[2];
		Long memberRankId = (Long) args[3];
		Boolean isPartner = (Boolean) args[4];
		Long storeId = (Long) args[5];
		Long saleOrgId = (Long) args[6];
		String startTime = (String) args[7];
		String endTime = (String) args[8];
		Integer memberType = (Integer) args[9];

		StringBuilder sql = new StringBuilder();
		sql.append("select s.*,mr.name rank_name,m.mobile,group_concat(distinct ss.name) store_name,");
		sql.append(
				"rs.username recommend_username,so.name sale_org_name,group_concat(distinct a.name) pc_role_name,xs.name sbu_name");
		sql.append(" from xx_store_member s");
		sql.append(" left join xx_member m on s.member = m.id");
		sql.append(" left join xx_store ss on s.store=ss.id");
		sql.append(" left join xx_member_rank mr on s.member_rank=mr.id");
		sql.append(" left join xx_store_member rs on s.recommend_store_member = rs.id");
		// sql.append(" left join xx_store_member sm on s.member = sm.member and
		// s.company_info_id = sm.company_info_id");
		// sql.append(" left join xx_store st on sm.store=st.id and
		// st.is_main_store = 0");
		sql.append(" left join xx_store_member_sale_org smso on s.id=smso.store_member");
		sql.append(" left join xx_sale_org so on so.id=smso.sale_org");
		sql.append(" left join xx_pc_user_role uo on s.id = uo.store_member");
		sql.append(" left join xx_pc_role a on a.id=uo.pc_role");
		sql.append(" left join xx_store_member_sbu xss on s.id=xss.store_member");
		sql.append(" left join xx_sbu xs on xs.id=xss.sbu");
		sql.append(" where ss.is_main_store = 1");
		// 角色
		if (args.length > 11) {
			Integer role = (Integer) args[11];
			if (role != null) {
				sql.append(" and a.name = '额度申请'");
			}
		}
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ? and ss.company_info_id = ? ");
			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		if (StringUtils.isNotEmpty(username)) {
			sql.append(" and s.username like ?");
			list.add("%" + username + "%");
		}
		if (StringUtils.isNotEmpty(mobile)) {
			sql.append(" and m.mobile like ?");
			list.add("%" + mobile + "%");
		}
		if (StringUtils.isNotEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}
		if (memberRankId != null) {
			sql.append(" and s.member_rank = ?");
			list.add(memberRankId);
		}
		if (isPartner != null) {
			if (isPartner) {
				sql.append(" and s.is_partner = 1");
			} else {
				sql.append(" and s.is_partner = 0");
			}
		}
		if (storeId != null) {
			sql.append(" and s.store = ?");
			list.add(storeId);
		}
		if (saleOrgId != null) {
			sql.append(" and smso.sale_org = ?");
			list.add(saleOrgId);
		}
		if (startTime != null) {
			sql.append(" and s.create_date >= ?");
			list.add(DateUtil.convert(startTime + " 00:00:00"));
		}
		if (endTime != null) {
			sql.append(" and s.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endTime + " 00:00:00"), 1));
		}
		if (memberType != null) {
			sql.append(" and s.member_type = ?");
			list.add(memberType);
		}
		if (args.length > 10) {
			boolean isSalesman = (Boolean) args[10];
			sql.append(" and s.is_salesman = ?");
			list.add(isSalesman);
		}

		sql.append(" group by s.id order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		//System.out.println("sql:" + sql.toString());
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		String totalsql = "select count(1) from ( " + sql + ") a";
		long total = getNativeDao().findInt(totalsql, objs);
		page.setTotal(total);
		return page;
	}

	public boolean existsDefaultByMember(Long memberId) {

		if (ConvertUtil.isEmpty(memberId)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select count(1) from xx_store_member sm, xx_store s");
		sql.append(" where sm.store = s.id");
		sql.append(" and s.is_main_store = 1 and sm.member = ? and sm.company_info_id = ? ");

		int count = getNativeDao().findInt(sql.toString(), new Object[] { memberId, companyInfoId });

		return count > 0;
	}

	/**
	 * 根据用户名查找用户
	 * 
	 * @param username
	 * @return
	 */
	public StoreMember findDefault(String username) {

		if (ConvertUtil.isEmpty(username)) {
			ExceptionUtil.throwDaoException("username is empty!");
		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_store_member sm, xx_store s");
		sql.append(" where sm.store = s.id");
		sql.append(" and s.is_main_store = 1");
		sql.append(" and sm.username = ? and sm.company_info_id = ?");

		StoreMember storeMember = getNativeDao().findSingleManaged(sql.toString(),
				new Object[] { username, companyInfoId }, StoreMember.class);

		return storeMember;
	}

	public boolean existsIsDefaultStoreMember(Long memberId) {
		StringBuilder sql = new StringBuilder();
		sql.append("select count(1) from xx_store_member sm, xx_member m");
		sql.append(" where sm.member = m.id");
		sql.append(" and sm.is_default =1 and m.id=?");
		int count = getNativeDao().findInt(sql.toString(), new Object[] { memberId });

		return count > 0;
	}

	public Integer count(String username, String mobile, String name, Long memberRankId, Boolean isPartner,
			Long storeId, Long saleOrgId, String startTime, String endTime, Long[] ids) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select count(1) from ( ");
		sql.append(
				"select s.*,mr.name rank_name,m.mobile, group_concat(distinct ss.name) store_name, rs.username recommend_username,so.name sale_org_name");
		sql.append(" from xx_store_member s");
		sql.append(" left join xx_member m on s.member = m.id");
		sql.append(" left join xx_store ss on s.store=ss.id");
		sql.append(" left join xx_member_rank mr on s.member_rank=mr.id");
		sql.append(" left join xx_store_member rs on s.recommend_store_member = rs.id");
		// sql.append(" left join xx_store_member sm on s.member = sm.member and
		// s.company_info_id = sm.company_info_id");
		// sql.append(" left join xx_store st on sm.store=st.id and
		// st.is_main_store = 0");
		sql.append(" left join xx_store_member_sale_org smso on s.id = smso.store_member");
		sql.append(" left join xx_sale_org so on so.id = smso.sale_org");
		sql.append(" where ss.is_main_store = 1");

		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ? and ss.company_info_id = ? ");
			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		if (StringUtils.isNotEmpty(username)) {
			sql.append(" and s.username like ?");
			list.add("%" + username + "%");
		}
		if (StringUtils.isNotEmpty(mobile)) {
			sql.append(" and m.mobile like ?");
			list.add("%" + mobile + "%");
		}
		if (StringUtils.isNotEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}
		if (memberRankId != null) {
			sql.append(" and s.member_rank = ?");
			list.add(memberRankId);
		}
		if (isPartner != null) {
			if (isPartner) {
				sql.append(" and s.is_partner = 1");
			} else {
				sql.append(" and s.is_partner = 0");
			}

		}
		if (storeId != null) {
			sql.append(" and s.store = ?");
			list.add(storeId);
		}
		if (saleOrgId != null) {
			sql.append(" and smso.sale_org = ?");
			list.add(saleOrgId);
		}
		if (startTime != null) {
			sql.append(" and s.create_date >= ?");
			list.add(DateUtil.convert(startTime + " 00:00:00"));
		}
		if (endTime != null) {
			sql.append(" and s.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endTime + " 00:00:00"), 1));
		}

		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and s.id in (" + inIds + ")");
		}

		sql.append(" group by s.member order by s.create_date desc");
		sql.append(") pc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}

	public List<Map<String, Object>> findList(String username, String mobile, String name, Long memberRankId,
			Boolean isPartner, Long storeId, Long saleOrgId, String startTime, String endTime, Integer memberType,
			Long[] ids, Integer page, Integer size) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append(
				"select s.*,mr.name rank_name,m.mobile, group_concat(distinct ss.name) store_name, rs.username recommend_username,so.name sale_org_name,xs.name sbu_name");
		sql.append(" from xx_store_member s");
		sql.append(" left join xx_member m on s.member = m.id");
		sql.append(" left join xx_store ss on s.store=ss.id");
		sql.append(" left join xx_member_rank mr on s.member_rank=mr.id");
		sql.append(" left join xx_store_member rs on s.recommend_store_member = rs.id");
		// sql.append(" left join xx_store_member sm on s.member = sm.member and
		// s.company_info_id = sm.company_info_id");
		// sql.append(" left join xx_store st on sm.store=st.id and
		// st.is_main_store = 0");
		sql.append(" left join xx_store_member_sale_org smso on s.id = smso.store_member");
		sql.append(" left join xx_sale_org so on so.id = smso.sale_org");
		sql.append(" left join xx_store_member_sbu xss on s.id = xss.store_member");
		sql.append(" left join xx_sbu xs on xs.id=xss.sbu");
		sql.append(" where ss.is_main_store = 1");

		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ? " + " and ss.company_info_id = ? ");
			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		if (StringUtils.isNotEmpty(username)) {
			sql.append(" and s.username like ?");
			list.add("%" + username + "%");
		}
		if (StringUtils.isNotEmpty(mobile)) {
			sql.append(" and m.mobile like ?");
			list.add("%" + mobile + "%");
		}
		if (StringUtils.isNotEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}
		if (memberRankId != null) {
			sql.append(" and s.member_rank = ?");
			list.add(memberRankId);
		}
		if (isPartner != null) {
			if (isPartner) {
				sql.append(" and s.is_partner = 1");
			} else {
				sql.append(" and s.is_partner = 0");
			}

		}
		if (storeId != null) {
			sql.append(" and s.store = ?");
			list.add(storeId);
		}
		if (saleOrgId != null) {
			sql.append(" and smso.sale_org = ?");
			list.add(saleOrgId);
		}
		if (startTime != null) {
			sql.append(" and s.create_date >= ?");
			list.add(DateUtil.convert(startTime + " 00:00:00"));
		}
		if (endTime != null) {
			sql.append(" and s.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endTime + " 00:00:00"), 1));
		}
		if (memberType != null) {
			sql.append(" and s.member_type = ?");
			list.add(memberType);
		}

		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and s.id in (" + inIds + ")");
		}

		sql.append(" group by s.member order by s.create_date desc");

		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);

		return maps;
	}

	public boolean storeMemberExists(Long companyInfoId, Long memberId, Long storeMemberId) {

		if (ConvertUtil.isEmpty(memberId)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}

		StringBuilder sql = new StringBuilder();
		sql.append("select count(1) from xx_store_member sm, xx_store s");
		sql.append(" where sm.store = s.id");
		sql.append(" and s.is_enabled = 1 and s.is_main_store = 1");
		sql.append(" and sm.member = ? and sm.company_info_id = ?");
		sql.append(" and sm.id <> ?");
		sql.append(" and sm.member_type != 99 ");// 排除小程序帐号
		Object[] objs = null;
		objs = new Object[] { memberId, companyInfoId, storeMemberId };
		int count = getNativeDao().findInt(sql.toString(), objs);

		return count > 0;
	}

	public boolean idCardExists(Long companyInfoId, String idCard, Long storeMemberId) {

		StringBuilder sql = new StringBuilder();
		sql.append("select count(1) from xx_store_member sm");
		sql.append(" where 1=1 ");
		sql.append(" and sm.id_card = ? and sm.company_info_id = ?");
		sql.append(" and sm.id <> ?");
		Object[] objs = null;
		objs = new Object[] { idCard, companyInfoId, storeMemberId };
		int count = getNativeDao().findInt(sql.toString(), objs);

		return count > 0;
	}

	public List<Map<String, Object>> findStoreMemberByMember(Long memberId) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(
				"select sm.*,m.mobile from xx_store_member sm left join xx_member m on sm.member=m.id where sm.store="
						+ memberId + " and sm.company_info_id=" + WebUtils.getCurrentCompanyInfoId());
		sql.append(" and sm.member_type= 1");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);

		return maps;
	}

	public Map<String, Object> findListByStore(Long storeId) {
		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_store_member where store=" + storeId);

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), null, 1);
		if (!maps.isEmpty()) {
			return maps.get(0);
		}
		return null;

	}

	public List<Map<String, Object>> findSbu(Long id) {

		String sql = " select s.name,sm.is_default,s.id " + " from xx_store_member_sbu sm "
				+ " left join xx_sbu s  on s.id=sm.sbu " + " left join xx_store_member xs on xs.id=sm.store_member "
				+ " where 1=1 and sm.store_member= " + id;
		List<Map<String, Object>> sbuItems = getNativeDao().findListMap(sql, null, 0);
		return sbuItems;
	}

	public List<Map<String, Object>> findShop(Long id) {

		String sql = " select s.address,s.id,s.sn " + " from xx_store_member_shop_info sm "
				+ " left join xx_shop_info s  on s.id=sm.shop_info "
				+ " left join xx_store_member xs on xs.id=sm.store_member " + " where 1=1 and sm.store_member= " + id;
		List<Map<String, Object>> sbuItems = getNativeDao().findListMap(sql, null, 0);
		return sbuItems;
	}

	public List<Map<String, Object>> findSbuTy(Long id) {

		String sql = " select s.name,sm.is_default,s.id " + " from xx_store_member_sbu sm "
				+ " left join xx_sbu s  on s.id=sm.sbu " + " left join xx_store_member xs on xs.id=sm.store_member "
				+ " where 1=1 and sm.is_default=true and sm.store_member= " + id;
		List<Map<String, Object>> sbuItems = getNativeDao().findListMap(sql, null, 0);
		return sbuItems;
	}

	public List<StoreMember> findStoreMember(String username) {

		// String sql = " select sm.* from xx_store_member sm left join
		// xx_member m on m.id=sm.member where (sm.id_card = ? or m.mobile= ? )"
		// + " and sm.company_info_id=9 group by sm.id ";
		StringBuilder sql = new StringBuilder();
		sql.append(" select sm.* from xx_store_member sm left join xx_member m on m.id=sm.member ");
		sql.append(" left join xx_store s on  s.id= sm.store   ");
		sql.append(" where sm.id_card = ?   ");
		sql.append(" and sm.company_info_id=9 and sm.is_enabled=1 and  s.is_main_store = 1 group by sm.id");

		Object[] objs = null;
		objs = new Object[] { username };

		List<StoreMember> storeMembers = getNativeDao().findListManaged(sql.toString(), objs, 0, StoreMember.class);

		return storeMembers;
	}

	public List<Map<String, Object>> findListByShop(Long storeMemberId, String shopIds) {

		StringBuilder sql = new StringBuilder();
		sql.append("select p.*,s.shop_info shopInfo from xx_store_member_shop_post s, xx_post p");
		sql.append(" where p.id = s.post and s.store_member=" + storeMemberId);
		if (!ConvertUtil.isEmpty(shopIds)) {
			sql.append(" and s.shop_info in (" + shopIds + ")");
		}
		List<Map<String, Object>> posts = getNativeDao().findListMap(sql.toString(), null, 0);
		return posts;
	}

	public Long findStoreId(Long storeMemberId) {
		StringBuilder sql = new StringBuilder();
		if (!ConvertUtil.isEmpty(storeMemberId)) {
			sql.append("SELECT s.id FROM xx_store s  LEFT JOIN xx_store_member sm ON sm.store = s.id WHERE 1=1 ");
			sql.append(" AND s.company_info_id = 9 AND s.type != 0");
			sql.append(" AND sm.member = ( SELECT member FROM xx_store_member WHERE id = " + storeMemberId + " )");
		} else {
			return null;
		}
		List<Map<String, Object>> list = getNativeDao().findListMap(sql.toString(), null, 0);
		return list == null ? null : Long.parseLong(list.get(0).get("id").toString());
	}

	/**
	 * 保存用户经营组织
	 * 
	 * @param id
	 * @return
	 */
	public List<Map<String, Object>> findOrganization(Long id) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		String sql = " SELECT o.name,smo.is_default,o.id  " + " FROM xx_store_member_organization smo "
				+ " LEFT JOIN xx_organization o  ON o.id=smo.organization  "
				+ " LEFT JOIN xx_store_member sm ON sm.id=smo.store_member  " + " WHERE 1=1 AND smo.company_info_id =  "
				+ companyInfoId;

		if (id != null) {
			sql += " AND sm.id = " + id;
		}

		List<Map<String, Object>> organizationItems = getNativeDao().findListMap(sql, null, 0);
		return organizationItems;
	}

	/**
	 * 查找sbu对应的角色
	 */
	public List<Map<String, Object>> findRoleSbu(Long sbuId) {
		if (sbuId == null) {
			return null;
		}
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "SELECT pc_role FROM xx_menu_role_sbu WHERE company_info_id = ? and sbu = ? ";

		return getNativeDao().findListMap(sql, new Object[] { companyInfoId, sbuId }, 0);
	}

	/**
	 * 修改用户时查询是否有多个用户
	 */
	public List<Map<String, Object>> findLoginStoreMember(Long memberId, Long storeMemberId, String username,
			String oldMobile, String oldIdCard, String newMobile, String newIdCard) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "SELECT sm.id storeMemberId,m.id memberId,sm.username,sm.id_card idCard,m.mobile "
				+ "FROM xx_store_member sm JOIN xx_member m ON sm.member = m.id "
				+ "JOIN xx_store s ON s.id = sm.store AND s.is_main_store =1 "
				+ "where sm.company_info_id=? and s.company_info_id=? and m.company_info_id=? "
				+ "and ( sm.id=? or m.mobile in (?,?) or sm.id_card in (?,?) )";

		return getNativeDao().findListMap(sql, new Object[] { companyInfoId, companyInfoId, companyInfoId,
				storeMemberId, oldMobile, newMobile, oldIdCard, newIdCard }, 0);
	}

	public Map<String, Object> findAppStoreMember(Long id) {
		// Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ticket_store,type FROM ticket_store_member WHERE 1=1 ");
		sql.append(" AND company_info_id = 9 ");
		sql.append(" AND  store_member = " + id);
		Map<String, Object> appstoremember = getNativeDao().findSingleMap(sql.toString(), null);
		if (appstoremember == null) {
			return appstoremember;
		}
		if (appstoremember.get("type") == null || appstoremember.get("ticket_store") == null) {
			return null;
		} else {
			return appstoremember;
		}
	}

	public List<StoreMember> findAreaManager(Long storeId, Long memberId) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT sm.* FROM xx_store_member sm");
		sql.append(" LEFT JOIN xx_store_member sm1 ON sm1.member = sm.member");
		sql.append(" LEFT JOIN xx_store s ON s.id = sm.store WHERE 1=1");
		sql.append(" AND sm.is_enabled = TRUE");
		sql.append(" AND sm.member_type = 0");
		sql.append(" AND s.is_main_store != 1");
		sql.append(" AND sm1.is_salesman = TRUE");
		if (companyInfoId != null) {
			sql.append(" AND sm.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (storeId != null) {
			sql.append(" AND sm.store = ?");
			list.add(storeId);
		}
		if (memberId != null) {
			sql.append(" AND sm.member != ?");
			list.add(memberId);
		}
		sql.append(" GROUP BY sm.id ");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		// System.out.println("客户id:"+storeId +"member :"+ memberId);
		// System.out.println("sql:"+sql.toString());
		return getNativeDao().findListManaged(sql.toString(), objs, 0, StoreMember.class);
	}

	public Long[] findListManageStore(Long memberId) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT s.* FROM xx_store s LEFT JOIN xx_store_member sm ON sm.store = s.id WHERE s.type != 0");

		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (memberId != null) {
			sql.append(" and sm.member = ?");
			list.add(memberId);
		}
		sql.append(" order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<net.shopxx.member.entity.Store> stores = getNativeDao().findListManaged(sql.toString(), objs,0, net.shopxx.member.entity.Store.class);
		Set<Long> store = new HashSet<Long>();
		if(stores != null){
			for(net.shopxx.member.entity.Store s : stores ){
				store.add(s.getId());
			}
		}
		return store.toArray(new Long[]{});
	}
	
	
	/**
	 * 根据用户名、手机号查找用户
	 * @param username
	 * @param mobile
	 * @return
	 */
	public List<StoreMember> findStoreMemberList(Long companyInfoId,String username,String mobile) {
		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT sm.* FROM xx_store_member  sm ");
		sql.append(" JOIN xx_member m ON sm.member = m.id ");
		sql.append(" JOIN xx_store s ON s.id=sm.store AND s.is_main_store = 1 AND s.company_info_id = sm.company_info_id ");
		sql.append(" WHERE 1=1 AND sm.company_info_id = m.company_info_id ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and sm.company_info_id = ?");
			list.add(companyInfoId);
		}
		if(!ConvertUtil.isEmpty(username)){
			sql.append(" and sm.username = ?");
			list.add(username);
		}
		if(!ConvertUtil.isEmpty(mobile)){
			sql.append(" and m.mobile = ?");
			list.add(mobile);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		List<StoreMember> storeMemberList = getNativeDao().findListManaged(sql.toString(), objs, 0, StoreMember.class);
		
		return storeMemberList;
	}

    public List<StoreMember> findStoreByMember(Long storeId) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT sm.* FROM xx_store_member sm,xx_store s WHERE sm.company_info_id = ?"
                + " AND sm.store = s.id AND s.is_main_store = 1 "
                + " AND sm.member_type = 1 AND sm.is_enabled = 1 ");
        sql.append("AND exists(SELECT 1 FROM xx_store_member WHERE member = sm.member AND store = ?)");
        list.add(companyInfoId);
        list.add(storeId);
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListManaged(sql.toString(), objs, 0, StoreMember.class);
    }
	
}
