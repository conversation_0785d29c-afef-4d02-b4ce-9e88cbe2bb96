package net.shopxx.member.dao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.util.RoleJurisdictionUtil;
import org.springframework.stereotype.Repository;
@Repository("bankCardBaseDao")
public class BankCardBaseDao extends DaoCenter {
	
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;

	public Page<Map<String, Object>> findPage(Pageable pageable,Long saleOrgId,
			String bankName, String bankCardNo, String mobile, Boolean isEnabled,
			Long sbuId,Boolean isTotalAccount,String bankCode) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        Long storeMemberId = WebUtils.getCurrentStoreMemberId();
        
		StringBuilder sql = new StringBuilder();
		sql.append("select bc.*,so.name saleOrgName,ot.id orgId,ot.name organization_name,sb.name sbu_name from xx_bank_card bc");
		sql.append(" left join xx_sale_org so on bc.sale_org = so.id");
		sql.append(" left join xx_organization ot on bc.organization = ot.id ");
		sql.append(" left join xx_sbu sb on bc.sbu=sb.id");
		sql.append(" left join xx_store s on bc.store=s.id where s.is_main_store=1");
		if (companyInfoId != null) {
			sql.append(" and bc.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(bankCode)) {
			sql.append(" and bc.bank_code like ?");
			list.add("%" + bankCode + "%");
		}
		if(sbuId !=null){
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		if (saleOrgId != null) {
			sql.append(" and so.id = ?");
			list.add(saleOrgId);
		}
		if (!ConvertUtil.isEmpty(bankName)) {
			sql.append(" and bc.bank_name like ?");
			list.add("%" + bankName + "%");
		}
		if (!ConvertUtil.isEmpty(bankCardNo)) {
			sql.append(" and bc.bank_card_no like ?");
			list.add("%" + bankCardNo + "%");
		}
		if (!ConvertUtil.isEmpty(mobile)) {
			sql.append(" and bc.mobile like ?");
			list.add("%" + mobile + "%");
		}
		if (isEnabled != null) {
			sql.append(" and bc.is_enabled = ?");
			if (isEnabled) {
				list.add(1);
			}
			else {
				list.add(0);
			}
		}
		if (!ConvertUtil.isEmpty(isTotalAccount)) {
			if (isTotalAccount) {
				sql.append(" and bc.is_total_account = ?");
				list.add(1);
			}else {
				sql.append(" AND (bc.is_total_account IS NULL OR  bc.is_total_account = ? ) ");
				list.add(0);
			}
		}
		
		//用户经营组织
		String organizationIds = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIds) && !organizationIds.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIds) && !organizationIds.equals("0")){
				sql.append(" and (ot.id is null or ot.id in (" + organizationIds + "))");
			}else{
				sql.append(" and ot.id is null");
			}
		}
		
		sql.append(" order by bc.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		//System.out.println(sql);
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);

		return page;
	}
	
	public List<BankCard> findListBySbu(Long saleOrgId,Long organizationId,Long sbuId) {
		
		

		StringBuilder sql = new StringBuilder();
		sql.append(" select * from xx_bank_card  ");
		sql.append(" where sale_org = ?   ");
		sql.append(" and sbu= ?");
		sql.append(" and organization= ?");

		Object[] objs = null;
		objs = new Object[] { saleOrgId,sbuId,organizationId};
		
		List<BankCard> bankCards =	getNativeDao().findListManaged(sql.toString(), objs, 0, BankCard.class);


		return bankCards;
	}
}
