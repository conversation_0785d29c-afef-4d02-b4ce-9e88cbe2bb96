/*
 * Copyright 2005-2013 shopxx.net. All rights reserved. Support:
 * http://www.shopxx.net License: http://www.shopxx.net/license
 */
package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.member.entity.PcMenu;
import net.shopxx.member.entity.PcRole;

/**
 * Dao - PcRole
 */
@Repository("pcRoleBaseDao")
public class PcRoleBaseDao extends DaoCenter {

	public PcRole findAdministrator() {

		StringBuffer sql = new StringBuffer();
		sql.append(
				"select * from xx_pc_role where name = ? and company_info_id is null");
		PcRole pcRole = getNativeDao().findSingleManaged(sql.toString(),
				new Object[] { "企业负责人" },
				PcRole.class);
		return pcRole;
	}
	
	
	public List<PcRole> findRoleList(Long storeMemberId) {

		if (ConvertUtil.isEmpty(storeMemberId)) {
			ExceptionUtil.throwDaoException("value is empty!");
		}
		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("select distinct pr.* ");
		sql.append(" from xx_pc_role  pr ");
		sql.append(" left join xx_menu_role_sbu mr on mr.pc_role=pr.id");
		sql.append(" left join xx_store_member_sbu smb on smb.sbu=mr.sbu");
	
		sql.append(" where smb.store_member= ?");

		list.add(storeMemberId);
	
		sql.append(" and pr.company_info_id = 9 ");
			
		
	
		sql.append(" order by pr.id");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<PcRole> menus = getNativeDao().findListManaged(sql.toString(),
				objs,
				0,
				PcRole.class);

		return menus;
	}
}