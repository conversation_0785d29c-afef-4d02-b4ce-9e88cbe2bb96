package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;

import org.springframework.stereotype.Repository;

@Repository("storeSbuDao")
public class StoreSbuDao extends DaoCenter {
	public List<Map<String, Object>> findStoreSbu(Long id) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT ss.*,sb.id sbuId,mr.id memberRankId,sb.name sbu_name,mr.name memberRank FROM xx_store_sbu ss "
				+ "left join xx_sbu sb on sb.id=ss.sbu "
				+ "left join xx_member_rank mr on mr.id=ss.member_rank "
				+ "WHERE 1=1 ");

		if (companyInfoId != null) {
			sql.append(" AND ss.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (!ConvertUtil.isEmpty(id)) {
			sql.append(" AND ss.store = ? ");
			list.add(id);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

}
