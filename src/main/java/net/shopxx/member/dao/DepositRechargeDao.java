package net.shopxx.member.dao;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import net.shopxx.basic.util.SystemConfig;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
@Repository("depositRechargeDao")
public class DepositRechargeDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;

	/**
	 * 用户、客户余额充值列表数据
	 */
	public Page<Map<String, Object>> findPage(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Integer[] budgetType, BigDecimal minPrice,
			BigDecimal maxPrice, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select dr.*,so.name sale_org_name,s.name store_name, sm.username, smc.username creator_name, smo.username operator_name, sd.id recharge_type_id, sd.value recharge_type_value");
		sql.append(" from xx_deposit_recharge dr");
		sql.append(" left join xx_store s on dr.stores=s.id");
		sql.append(" left join xx_store_member sm on dr.store_member = sm.id");
		sql.append(" left join xx_store_member smc on dr.creator = smc.id");
		sql.append(" left join xx_store_member smo on dr.operator = smo.id");
		sql.append(" left join xx_system_dict sd on dr.recharge_type = sd.id and sd.parent is not null");
		sql.append(" left join xx_sale_org so on dr.sale_org = so.id");
		sql.append(" where 1 = 1");
		if (companyInfoId != null) {
			sql.append(" and dr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql.append(" and dr.type in (" + os + ")");
		}
		else {
			sql.append(" and dr.type in (2,3)");
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (creatorId != null) {
			sql.append(" and smc.id = ?");
			list.add(creatorId);
		}
		if (operatorId != null) {
			sql.append(" and smo.id = ?");
			list.add(operatorId);
		}
		if (minPrice != null) {
			sql.append(" and dr.amount >= ?");
			list.add(minPrice);
		}
		if (maxPrice != null) {
			sql.append(" and dr.amount <= ?");
			list.add(maxPrice);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and dr.sn like ?");
			list.add("%" + sn + "%");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and dr.status in (" + os + ")");
		}
		if (docstatus != null && docstatus.length > 0) {
			String os = "";
			for (int i = 0; i < docstatus.length; i++) {
				if (i == docstatus.length - 1)
					os += docstatus[i];
				else
					os += docstatus[i] + ",";
			}
			sql.append(" and dr.doc_status in (" + os + ")");
		}
		if (budgetType != null && budgetType.length > 0) {
			String bt = "";
			for (int i = 0; i < budgetType.length; i++) {
				if (i == budgetType.length - 1)
					bt += budgetType[i];
				else
					bt += budgetType[i] + ",";
			}
			sql.append(" and dr.budget_type in (" + bt + ")");
		}
		if (rechargeTypeId != null && rechargeTypeId.length > 0) {
			String rt = "";
			for (int i = 0; i < rechargeTypeId.length; i++) {
				if (i == rechargeTypeId.length - 1)
					rt += rechargeTypeId[i];
				else
					rt += rechargeTypeId[i] + ",";
			}
			sql.append(" and dr.recharge_type in (" + rt + ")");
		}

		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and dr.stores in (" + storeAuth + ")");
			}
		}
		else {
			
			sql.append( " and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
			
//			Long saleOrgId = null;
//			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
//			if (storeMemberSaleOrg != null) {
//				saleOrgId = storeMemberSaleOrg.getSaleOrg().getId();
//			}
//			sql.append(" and (so.id="
//					+ saleOrgId
//					+ " or so.tree_path like '%,"
//					+ saleOrgId
//					+ ",%')");
		}

		sql.append(" order by dr.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> listItems = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return listItems;

	}

	/**
	 * 销售回款列表数据
	 */
	public Page<Map<String, Object>> newfindPage(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId,Integer[] status, 
			Integer[] docstatus, Long[] rechargeTypeId,Long creatorId, Long operatorId, 
			Integer[] budgetType,BigDecimal minPrice, BigDecimal maxPrice, String firstTime,
			String lastTime,Long organizationId,Long sbuId,String sourceSn,String adjustmentSn,
			Long[] bankCardId,Long[] bcOrganizationId,Long[] bcSbuId,Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select dr.*,so.name sale_org_name,s.name store_name,s.sn sn, sm.username, smc.name creator_name, "
				+ "	smo.username operator_name, sd.id recharge_type_id, sd.value recharge_type_value,og.name organization_name, "
				+ " s.out_trade_no outTradeNo,sb.name sbu_name,s.alias store_alias, dre.sn sourceSn,ad.sn adjustmentSn, ");
		
		sql.append(" bc.bank_name,bc.bank_card_no,bc.bank_code,bco.name  bc_organization_name,bcs.name bc_sbu_name, ");
		
		sql.append(" bcd.bank_name source_bank_name,bcd.bank_card_no source_bank_card_no,bcd.bank_code source_bank_code, ");
		sql.append(" bcdo.name  bcd_organization_name,bcds.name bcd_sbu_name,dr.gl_date ");
		
		sql.append(" from xx_deposit_recharge dr");
		sql.append(" left join xx_store s on dr.stores=s.id");
		sql.append(" left join xx_store_member sm on dr.store_member = sm.id");
		sql.append(" left join xx_store_member smc on dr.creator = smc.id");
		sql.append(" left join xx_store_member smo on dr.operator = smo.id");
		sql.append(" left join xx_system_dict sd on dr.recharge_type = sd.id and sd.parent is not null");
		sql.append(" left join xx_sale_org so on dr.sale_org = so.id");
		sql.append(" left join xx_organization og on og.id = dr.organization ");
		sql.append(" left join xx_sbu sb on dr.sbu = sb.id ");
		
		sql.append(" left join xx_bank_card bc on bc.id = dr.bank_card");
		sql.append(" left join xx_organization bco on bco.id = bc.organization ");
		sql.append(" left join xx_sbu bcs on bcs.id = bc.sbu ");
		
		sql.append(" left join xx_deposit_recharge dre on dre.id = dr.source_deposit_recharge ");
		sql.append(" left join xx_bank_card bcd on bcd.id = dre.bank_card ");
		sql.append(" left join xx_organization bcdo on bcdo.id = bcd.organization ");
		sql.append(" left join xx_sbu bcds on bcds.id = bcd.sbu ");
		
		sql.append(" left join xx_adjustment ad on (ad.source_deposit_recharge = dr.id OR ad.adjustment_deposit_recharge = dr.id) ");
		
		
		sql.append(" where 1 = 1 and (bc.is_total_account =0 OR bc.is_total_account IS NULL OR bc.is_total_account ='' ) ");
		if (companyInfoId != null) {
			sql.append(" and dr.company_info_id = ?");
			list.add(companyInfoId);
		}
		
		//来源账号
		if (!ConvertUtil.isEmpty(sourceSn)) {
			sql.append(" and dre.sn like ?");
			list.add("%" + sourceSn + "%");
		}
		
		//调款单号
		if (!ConvertUtil.isEmpty(adjustmentSn)) {
			sql.append(" and ad.sn like ?");
			list.add("%" + adjustmentSn + "%");
		}
		
		//收款账户
		if (!ConvertUtil.isEmpty(bankCardId) && bankCardId.length > 0) {
			String os = "";
			for (int i = 0; i < bankCardId.length; i++) {
				if (i == bankCardId.length - 1)
					os += bankCardId[i];
				else
					os += bankCardId[i] + ",";
			}
			sql.append(" and  bc.id in (" + os + ")");
		}	
			
			
		//收款经营组织
		if (!ConvertUtil.isEmpty(bcOrganizationId) && bcOrganizationId.length > 0) {
			String os = "";
			for (int i = 0; i < bcOrganizationId.length; i++) {
				if (i == bcOrganizationId.length - 1)
					os += bcOrganizationId[i];
				else
					os += bcOrganizationId[i] + ",";
			}
			sql.append(" and  bco.id in (" + os + ")");
		}
		
		//收款sbu
		if (!ConvertUtil.isEmpty(bcSbuId) && bcSbuId.length > 0) {
			String os = "";
			for (int i = 0; i < bcSbuId.length; i++) {
				if (i == bcSbuId.length - 1)
					os += bcSbuId[i];
				else
					os += bcSbuId[i] + ",";
			}
			sql.append(" and  bcs.id in (" + os + ")");
		}	
		
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql.append(" and dr.type in (" + os + ")");
		}else {
			sql.append(" and dr.type in (2,3)");
		}
		
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		
		if (creatorId != null) {
			sql.append(" and smc.id = ?");
			list.add(creatorId);
		}
		
		if (operatorId != null) {
			sql.append(" and smo.id = ?");
			list.add(operatorId);
		}
		
		if (organizationId != null) {
			sql.append(" and dr.organization = ?");
			list.add(organizationId);
		}
		
		if (minPrice != null) {
			sql.append(" and dr.amount >= ?");
			list.add(minPrice);
		}
		
		if (maxPrice != null) {
			sql.append(" and dr.amount <= ?");
			list.add(maxPrice);
		}
		
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and dr.sn like ?");
			list.add("%" + sn + "%");
		}
		
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			sql.append(" and dr.sale_org = ?");
			list.add(saleOrgId);
		}
		
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and dr.status in (" + os + ")");
		}
		
		if (docstatus != null && docstatus.length > 0) {
			String os = "";
			for (int i = 0; i < docstatus.length; i++) {
				if (i == docstatus.length - 1)
					os += docstatus[i];
				else
					os += docstatus[i] + ",";
			}
			sql.append(" and dr.doc_status in (" + os + ")");
		}
		
		if (budgetType != null && budgetType.length > 0) {
			String bt = "";
			for (int i = 0; i < budgetType.length; i++) {
				if (i == budgetType.length - 1)
					bt += budgetType[i];
				else
					bt += budgetType[i] + ",";
			}
			sql.append(" and dr.budget_type in (" + bt + ")");
		}
		
		if (rechargeTypeId != null && rechargeTypeId.length > 0) {
			String rt = "";
			for (int i = 0; i < rechargeTypeId.length; i++) {
				if (i == rechargeTypeId.length - 1)
					rt += rechargeTypeId[i];
				else
					rt += rechargeTypeId[i] + ",";
			}
			sql.append(" and dr.recharge_type in (" + rt + ")");
		}

		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and dr.stores in (" + storeAuth + ")");
			}
		}
		
		/**GL日期筛选**/
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and dr.gl_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and dr.gl_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and og.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and og.id is null");
			}
		}
		
		sql.append(" order by dr.create_date desc");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> listItems = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return listItems;

	}

	/**
	 * 销售回款列表数据导出
	 */
	public List<Map<String, Object>> findDepositRechargeList(Integer[] type,String sn, 
			Long storeMemberId, Long storeId, Long saleOrgId,Integer[] status, Integer[] docstatus, 
			Long[] rechargeTypeId,Long creatorId, Long operatorId, Integer[] budgetType,BigDecimal minPrice, 
			BigDecimal maxPrice,Long sbuId, String firstTime,Long[] ids,Long organizationId, String lastTime, 
			Pageable pageable, Integer page,Integer size,String sourceSn,String adjustmentSn,Long[] bankCardId,
			Long[] bcOrganizationId,Long[] bcSbuId) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append(" select dr.*,so.name sale_org_name,s.name store_name,s.sn store_sn, sm.username, smc.name creator_name, "
				+ "   smo.username operator_name,sd.id recharge_type_id, sd.value recharge_type_value,ot.name organization_name, ");
		sql.append("  s.out_trade_no,bc.bank_card_no,sb.name sbu_name,s.alias store_alias,dre.sn sourceSn,ad.sn adjustmentSn, ");
		
		sql.append(" bc.bank_name,bc.bank_card_no,bc.bank_code,bco.name  bc_organization_name,bcs.name bc_sbu_name, ");
		
		sql.append(" bcd.bank_name source_bank_name,bcd.bank_card_no source_bank_card_no,bcd.bank_code source_bank_code, ");
		sql.append(" bcdo.name  bcd_organization_name,bcds.name bcd_sbu_name ");
		
		sql.append(" from xx_deposit_recharge dr");
		sql.append(" left join xx_store s on dr.stores=s.id");
		sql.append(" left join xx_store_member sm on dr.store_member = sm.id");
		sql.append(" left join xx_store_member smc on dr.creator = smc.id");
		sql.append(" left join xx_store_member smo on dr.operator = smo.id");
		sql.append(" left join xx_system_dict sd on dr.recharge_type = sd.id and sd.parent is not null");
		sql.append(" left join xx_sale_org so on dr.sale_org = so.id");
		sql.append(" left join xx_organization ot on dr.organization = ot.id ");
		sql.append(" left join xx_sbu sb on dr.sbu=sb.id");
		
		sql.append(" left join xx_bank_card bc on dr.bank_card=bc.id");
		sql.append(" left join xx_organization bco on bco.id = bc.organization ");
		sql.append(" left join xx_sbu bcs on bcs.id = bc.sbu ");
		
		sql.append(" left join xx_deposit_recharge dre on dre.id = dr.source_deposit_recharge ");
		sql.append(" left join xx_bank_card bcd on bcd.id = dre.bank_card ");
		sql.append(" left join xx_organization bcdo on bcdo.id = bcd.organization ");
		sql.append(" left join xx_sbu bcds on bcds.id = bcd.sbu ");
		
		sql.append(" left join xx_adjustment ad on (ad.source_deposit_recharge = dr.id OR ad.adjustment_deposit_recharge = dr.id) ");
		sql.append(" where 1 = 1 and (bc.is_total_account =0 OR bc.is_total_account IS NULL OR bc.is_total_account ='' ) ");
		
		if (companyInfoId != null) {
			sql.append(" and dr.company_info_id = ?");
			list.add(companyInfoId);
		}
		//来源账号
		if (!ConvertUtil.isEmpty(sourceSn)) {
			sql.append(" and dre.sn like ?");
			list.add("%" + sourceSn + "%");
		}
		//调款单号
		if (!ConvertUtil.isEmpty(adjustmentSn)) {
			sql.append(" and ad.sn like ?");
			list.add("%" + adjustmentSn + "%");
		}
		//收款账户
		if (!ConvertUtil.isEmpty(bankCardId) && bankCardId.length > 0) {
			String os = "";
			for (int i = 0; i < bankCardId.length; i++) {
				if (i == bankCardId.length - 1)
					os += bankCardId[i];
				else
					os += bankCardId[i] + ",";
			}
			sql.append(" and  bc.id in (" + os + ")");
		}		
		//收款经营组织
		if (!ConvertUtil.isEmpty(bcOrganizationId) && bcOrganizationId.length > 0) {
			String os = "";
			for (int i = 0; i < bcOrganizationId.length; i++) {
				if (i == bcOrganizationId.length - 1)
					os += bcOrganizationId[i];
				else
					os += bcOrganizationId[i] + ",";
			}
			sql.append(" and  bco.id in (" + os + ")");
		}
		//收款sbu
		if (!ConvertUtil.isEmpty(bcSbuId) && bcSbuId.length > 0) {
			String os = "";
			for (int i = 0; i < bcSbuId.length; i++) {
				if (i == bcSbuId.length - 1)
					os += bcSbuId[i];
				else
					os += bcSbuId[i] + ",";
			}
			sql.append(" and  bcs.id in (" + os + ")");
		}	
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql.append(" and dr.type in (" + os + ")");
		}else {
			sql.append(" and dr.type in (2,3)");
		}
		if (organizationId != null) {
			sql.append(" and dr.organization = ?");
			list.add(organizationId);
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and dr.stores in (" + storeAuth + ")");
			}
		}
		
		if (creatorId != null) {
			sql.append(" and smc.id = ?");
			list.add(creatorId);
		}
		if (operatorId != null) {
			sql.append(" and smo.id = ?");
			list.add(operatorId);
		}
		if (minPrice != null) {
			sql.append(" and dr.amount >= ?");
			list.add(minPrice);
		}
		if (maxPrice != null) {
			sql.append(" and dr.amount <= ?");
			list.add(maxPrice);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and dr.sn like ?");
			list.add("%" + sn + "%");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and dr.status in (" + os + ")");
		}
		if (docstatus != null && docstatus.length > 0) {
			String os = "";
			for (int i = 0; i < docstatus.length; i++) {
				if (i == docstatus.length - 1)
					os += docstatus[i];
				else
					os += docstatus[i] + ",";
			}
			sql.append(" and dr.doc_status in (" + os + ")");
		}
		if (budgetType != null && budgetType.length > 0) {
			String bt = "";
			for (int i = 0; i < budgetType.length; i++) {
				if (i == budgetType.length - 1)
					bt += budgetType[i];
				else
					bt += budgetType[i] + ",";
			}
			sql.append(" and dr.budget_type in (" + bt + ")");
		}
		if (rechargeTypeId != null && rechargeTypeId.length > 0) {
			String rt = "";
			for (int i = 0; i < rechargeTypeId.length; i++) {
				if (i == rechargeTypeId.length - 1)
					rt += rechargeTypeId[i];
				else
					rt += rechargeTypeId[i] + ",";
			}
			sql.append(" and dr.recharge_type in (" + rt + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and dr.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and dr.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and dr.id in (" + inIds + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and ot.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and ot.id is null");
			}
		}
		
		sql.append(" order by dr.create_date desc");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);
		return maps;

	}

	/**
	 * 销售回款列表数据导出
	 */
	public Integer countDepositRecharge(Integer[] type, String sn,Long storeMemberId, 
			Long storeId, Long saleOrgId, Integer[] status,Integer[] docstatus, 
			Long[] rechargeTypeId, Long creatorId,Long operatorId, Integer[] budgetType, 
			BigDecimal minPrice,BigDecimal maxPrice, Long sbuId,String firstTime, String lastTime,
			Long[] ids,Long organizationId,Pageable pageable, Integer size, Integer page,
			String sourceSn,String adjustmentSn,Long[] bankCardId,Long[] bcOrganizationId,Long[] bcSbuId) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("select count(dr.id)");
		sql.append(" from xx_deposit_recharge dr");
		sql.append(" left join xx_store s on dr.stores=s.id");
		sql.append(" left join xx_store_member sm on dr.store_member = sm.id");
		sql.append(" left join xx_store_member smc on dr.creator = smc.id");
		sql.append(" left join xx_store_member smo on dr.operator = smo.id");
		sql.append(" left join xx_system_dict sd on dr.recharge_type = sd.id and sd.parent is not null");
		sql.append(" left join xx_sale_org so on dr.sale_org = so.id");
		sql.append(" left join xx_sbu sb on dr.sbu = sb.id");
		sql.append(" left join xx_organization ot on dr.organization = ot.id ");
		
		sql.append(" left join xx_bank_card bc on bc.id = dr.bank_card");
		sql.append(" left join xx_organization bco on bco.id = bc.organization ");
		sql.append(" left join xx_sbu bcs on bcs.id = bc.sbu ");
		
		sql.append(" left join xx_deposit_recharge dre on dre.id = dr.source_deposit_recharge ");
		sql.append(" left join xx_bank_card bcd on bcd.id = dre.bank_card ");
		sql.append(" left join xx_organization bcdo on bcdo.id = bcd.organization ");
		sql.append(" left join xx_sbu bcds on bcds.id = bcd.sbu ");
		
		sql.append(" left join xx_adjustment ad on (ad.source_deposit_recharge = dr.id OR ad.adjustment_deposit_recharge = dr.id) ");
		sql.append(" where 1 = 1 and (bc.is_total_account =0 OR bc.is_total_account IS NULL OR bc.is_total_account ='' ) ");
		
		if (companyInfoId != null) {
			sql.append(" and dr.company_info_id = ?");
			list.add(companyInfoId);
		}
		
		//来源账号
		if (!ConvertUtil.isEmpty(sourceSn)) {
			sql.append(" and dre.sn like ?");
			list.add("%" + sourceSn + "%");
		}
		//调款单号
		if (!ConvertUtil.isEmpty(adjustmentSn)) {
			sql.append(" and ad.sn like ?");
			list.add("%" + adjustmentSn + "%");
		}
		//收款账户
		if (!ConvertUtil.isEmpty(bankCardId) && bankCardId.length > 0) {
			String os = "";
			for (int i = 0; i < bankCardId.length; i++) {
				if (i == bankCardId.length - 1)
					os += bankCardId[i];
				else
					os += bankCardId[i] + ",";
			}
			sql.append(" and  bc.id in (" + os + ")");
		}		
		//收款经营组织
		if (!ConvertUtil.isEmpty(bcOrganizationId) && bcOrganizationId.length > 0) {
			String os = "";
			for (int i = 0; i < bcOrganizationId.length; i++) {
				if (i == bcOrganizationId.length - 1)
					os += bcOrganizationId[i];
				else
					os += bcOrganizationId[i] + ",";
			}
			sql.append(" and  bco.id in (" + os + ")");
		}
		//收款sbu
		if (!ConvertUtil.isEmpty(bcSbuId) && bcSbuId.length > 0) {
			String os = "";
			for (int i = 0; i < bcSbuId.length; i++) {
				if (i == bcSbuId.length - 1)
					os += bcSbuId[i];
				else
					os += bcSbuId[i] + ",";
			}
			sql.append(" and  bcs.id in (" + os + ")");
		}	
		
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql.append(" and dr.type in (" + os + ")");
		}else {
			sql.append(" and dr.type in (2,3)");
		}
		if (organizationId != null) {
			sql.append(" and dr.organization = ?");
			list.add(organizationId);
		}
		if (storeMemberId != null) {
			sql.append(" and sm.id = ?");
			list.add(storeMemberId);
		}
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (sbuId != null) {
			sql.append(" and sb.id = ?");
			list.add(sbuId);
		}
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and dr.stores in (" + storeAuth + ")");
			}
		}
		
		if (creatorId != null) {
			sql.append(" and smc.id = ?");
			list.add(creatorId);
		}
		if (operatorId != null) {
			sql.append(" and smo.id = ?");
			list.add(operatorId);
		}
		if (minPrice != null) {
			sql.append(" and dr.amount >= ?");
			list.add(minPrice);
		}
		if (maxPrice != null) {
			sql.append(" and dr.amount <= ?");
			list.add(maxPrice);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and dr.sn like ?");
			list.add("%" + sn + "%");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and dr.status in (" + os + ")");
		}
		if (docstatus != null && docstatus.length > 0) {
			String os = "";
			for (int i = 0; i < docstatus.length; i++) {
				if (i == docstatus.length - 1)
					os += docstatus[i];
				else
					os += docstatus[i] + ",";
			}
			sql.append(" and dr.doc_status in (" + os + ")");
		}
		if (budgetType != null && budgetType.length > 0) {
			String bt = "";
			for (int i = 0; i < budgetType.length; i++) {
				if (i == budgetType.length - 1)
					bt += budgetType[i];
				else
					bt += budgetType[i] + ",";
			}
			sql.append(" and dr.budget_type in (" + bt + ")");
		}
		if (rechargeTypeId != null && rechargeTypeId.length > 0) {
			String rt = "";
			for (int i = 0; i < rechargeTypeId.length; i++) {
				if (i == rechargeTypeId.length - 1)
					rt += rechargeTypeId[i];
				else
					rt += rechargeTypeId[i] + ",";
			}
			sql.append(" and dr.recharge_type in (" + rt + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and dr.gl_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and dr.gl_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and dr.id in (" + inIds + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and ot.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and ot.id is null");
			}
		}
		
		sql.append(" order by dr.create_date desc");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Integer count = getNativeDao().findInt(sql.toString(), objs);
		return count;

	}

	public Page<Map<String, Object>> findShowPage(Long storeId, String sn,
			Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		if (storeId != null) {
			sql.append("select xdr.id,xdr.sn,xdr.actual_amount,"
					+ " ifnull((select sum(order_amount) from xx_deposit_recharge_contract xdrc  where xdrc.deposit_recharge_no=xdr.sn ),0) available_amount"
					+ " from xx_deposit_recharge  xdr "
					+ " where xdr.doc_status=2 and xdr.stores=? and xdr.company_info_id=?");
			list.add(storeId);
			list.add(companyInfoId);
			
			if (!ConvertUtil.isEmpty(sn)) {
				sql.append(" and xdr.sn like ?");
				list.add("%" + sn + "%");
			}
			
			StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
			if (!(storeMember.getMemberType() == 1 || storeMember.getIsSalesman())) {
				Long saleOrgId = null;
				StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
				if (storeMemberSaleOrg != null) {
					saleOrgId = storeMemberSaleOrg.getSaleOrg().getId();
				}
				sql.append(" and exists (select 1 from xx_sale_org so where xdr.sale_org = so.id  and (so.id="
						+ saleOrgId
						+ " or so.tree_path like '%,"
						+ saleOrgId
						+ ",%')"
						+ ") ");
			}

		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		String totalSql = "select count(1) from (" + sql + ") a";
		long total = getNativeDao().findInt(totalSql, objs);
		page.setTotal(total);
		return page;
	}
	
	public List<Map<String, Object>> findList(Long storeId, String sn){

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append("select xdr.id,xdr.sn,xdr.actual_amount,"
				+ " ifnull((select sum(order_amount) from xx_deposit_recharge_contract xdrc  where xdrc.deposit_recharge_no=xdr.sn ),0) available_amount"
				+ " from xx_deposit_recharge  xdr "
				+ " where xdr.doc_status=2 and xdr.stores=? and xdr.company_info_id=?");
		list.add(storeId);
		list.add(companyInfoId);
		
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and xdr.sn=?");
			list.add(sn);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
	
	
	public List<SystemDict> findcashProject(Long sbuId) {
		
		
//		String sql = " select sm.* from xx_store_member sm left join xx_member m on m.id=sm.member where (sm.id_card = ?  or m.mobile= ? )"
//				+ " and sm.company_info_id=9  group by sm.id ";
		StringBuilder sql = new StringBuilder();
		sql.append(" select sm.* from xx_store_member sm left join xx_member m on m.id=sm.member ");
		sql.append(" where (sm.id_card = ?  or m.mobile= ?  or sm.username= ? ) ");
		sql.append(" and sm.company_info_id=9  group by sm.id");

		Object[] objs = null;
//		objs = new Object[] { username,username,username};
		
		List<SystemDict> storeMembers =	getNativeDao().findListManaged(sql.toString(), objs, 0, SystemDict.class);

		return storeMembers;
	}
	
	public List<SystemDict> findInvoiceType(Long sbuId) {
		
		
//		String sql = " select sm.* from xx_store_member sm left join xx_member m on m.id=sm.member where (sm.id_card = ?  or m.mobile= ? )"
//				+ " and sm.company_info_id=9  group by sm.id ";
		StringBuilder sql = new StringBuilder();
		sql.append(" select sm.* from xx_store_member sm left join xx_member m on m.id=sm.member ");
		sql.append(" where (sm.id_card = ?  or m.mobile= ?  or sm.username= ? ) ");
		sql.append(" and sm.company_info_id=9  group by sm.id");

		Object[] objs = null;
//		objs = new Object[] { username,username,username};
		
		List<SystemDict> storeMembers =	getNativeDao().findListManaged(sql.toString(), objs, 0, SystemDict.class);

		return storeMembers;
	}
	
	
	public List<SystemDict> findPolicyType(Long sbuId) {
		
		
//		String sql = " select sm.* from xx_store_member sm left join xx_member m on m.id=sm.member where (sm.id_card = ?  or m.mobile= ? )"
//				+ " and sm.company_info_id=9  group by sm.id ";
		StringBuilder sql = new StringBuilder();
		sql.append(" select sm.* from xx_store_member sm left join xx_member m on m.id=sm.member ");
		sql.append(" where (sm.id_card = ?  or m.mobile= ?  or sm.username= ? ) ");
		sql.append(" and sm.company_info_id=9  group by sm.id");

		Object[] objs = null;
//		objs = new Object[] { username,username,username};
		
		List<SystemDict> storeMembers =	getNativeDao().findListManaged(sql.toString(), objs, 0, SystemDict.class);

		return storeMembers;
	}
	
	/**
	 * 根据经销商充值id查找收款账号列表
	 * */
	public List<Map<String, Object>> findDistributorRechargeItemList(String ids) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String sql = "";
		sql += " SELECT  dtr.id,dtr.sn,bc.id bank_card_id,bc.bank_card_no,o.id organization_id, "
				+ " 	 o.name organization_name,sb.name sbu_name,dtr.amount,dtr.memo  "
				+ " FROM xx_deposit_recharge dt "
				+ " INNER JOIN xx_distributor_recharge_item drr ON drr.deposit_recharge = dt.id  "
				+ " LEFT JOIN xx_deposit_recharge dtr ON dtr.id = drr.dealer_recharge_item  "
				+ " LEFT JOIN xx_bank_card bc ON bc.id = dtr.bank_card "
				+ " LEFT JOIN xx_organization o ON o.id = dtr.organization "
				+ " LEFT JOIN xx_sbu sb ON dtr.sbu = sb.id "
				+ " WHERE 1=1  ";
				if(!ConvertUtil.isEmpty(companyInfoId)){
					sql+=" AND dt.company_info_id = "+companyInfoId;
				}
				if(!ConvertUtil.isEmpty(ids)){
					sql+=" AND dt.id in (" + ids + ")";
				}
               sql+=" GROUP BY dtr.id ORDER BY dtr.create_date DESC ";
		
		List<Map<String, Object>> productionSchedulingItemList = getNativeDao().findListMap(sql,null,0);

		return productionSchedulingItemList;
	}
	
	
	
	/**
	 *经销商充值列表数据
	 */
	public Page<Map<String, Object>> newfindDepositRechargePage(Integer[] type,
			 Long[] rechargeTypeId,Long[] sbuId,String sn,Integer[] docstatus,
			 Long[] wfStates,Long[] storeId,Long[]  saleOrgId,Long[] bankCardId,
			 Long[] organizationId,String glDateStartTime,String glDateEndTime,
			 Long[] creatorId,String sourceSn, Integer page,
			 Integer size,Long[] ids,Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT dr.*,s.name store_name,s.alias store_alias,s.out_trade_no outTradeNo,so.name sale_org_name, ");
		sql.append(" sd.value recharge_type_value,sb.name sbu_name,smc.name creator_name,og.name organization_name, ");
		sql.append(" rmr.name regionalManagerName,sm.username,smo.username operator_name,dre.sn sourceSn,ad.sn adjustmentSn, ");
		
		sql.append(" bc.bank_name,bc.bank_card_no,bc.bank_code,bco.name  bc_organization_name,bcs.name bc_sbu_name, ");
		
		sql.append(" bcd.bank_name source_bank_name,bcd.bank_card_no source_bank_card_no,bcd.bank_code source_bank_code, ");
		sql.append(" bcdo.name  bcd_organization_name,bcds.name bcd_sbu_name ");
		
		sql.append(" FROM xx_deposit_recharge dr ");
		sql.append(" LEFT JOIN xx_store s ON dr.stores=s.id ");
		sql.append(" LEFT JOIN xx_store_member sm ON dr.store_member = sm.id ");
		sql.append(" LEFT JOIN xx_store_member smc ON dr.creator = smc.id ");
		sql.append(" LEFT JOIN xx_store_member smo ON dr.operator = smo.id ");
		sql.append(" LEFT JOIN xx_store_member rmr ON dr.regional_manager = rmr.id ");
		sql.append(" LEFT JOIN xx_system_dict sd ON dr.recharge_type = sd.id AND sd.parent IS NOT NULL ");
		sql.append(" LEFT JOIN xx_sale_org so ON dr.sale_org = so.id ");
		sql.append(" LEFT JOIN xx_organization og ON og.id = dr.organization ");
		sql.append(" LEFT JOIN xx_sbu sb ON dr.sbu = sb.id ");
		
		sql.append(" LEFT JOIN xx_bank_card bc ON bc.id = dr.bank_card ");
		sql.append(" LEFT JOIN xx_organization bco ON bco.id = bc.organization ");
		sql.append(" LEFT JOIN xx_sbu bcs ON bcs.id = bc.sbu ");
		
		sql.append(" LEFT JOIN xx_deposit_recharge dre ON dre.id = dr.source_deposit_recharge ");
		sql.append(" LEFT JOIN xx_bank_card bcd ON bcd.id = dre.bank_card ");
		sql.append(" LEFT JOIN xx_organization bcdo ON bcdo.id = bcd.organization ");
		sql.append(" LEFT JOIN xx_sbu bcds ON bcds.id = bcd.sbu ");
	
		sql.append(" LEFT JOIN xx_adjustment ad ON (ad.source_deposit_recharge = dr.id OR ad.adjustment_deposit_recharge = dr.id) ");
	
		
		sql.append(" where 1 = 1 and bc.is_total_account =1 ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and dr.company_info_id = ?");
			list.add(companyInfoId);
		}
		//经销商充值Id
		if ( !ConvertUtil.isEmpty(ids) && ids.length > 0) {
			String os = "";
			for (int i = 0; i < ids.length; i++) {
				if (i == ids.length - 1)
					os += ids[i];
				else
					os += ids[i] + ",";
			}
			sql.append(" and dr.id in (" + os + ")");
		}
		//类型 
		if ( !ConvertUtil.isEmpty(type) && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql.append(" and dr.type in (" + os + ")");
		}
		//充值类型
		if ( !ConvertUtil.isEmpty(rechargeTypeId)  && rechargeTypeId.length > 0) {
			String rt = "";
			for (int i = 0; i < rechargeTypeId.length; i++) {
				if (i == rechargeTypeId.length - 1)
					rt += rechargeTypeId[i];
				else
					rt += rechargeTypeId[i] + ",";
			}
			sql.append(" and dr.recharge_type in (" + rt + ")");
		}
		//单据编码
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and dr.sn like ?");
			list.add("%" + sn + "%");
		}
		//来源账号
		if (!ConvertUtil.isEmpty(sourceSn)) {
			sql.append(" and dre.sn like ?");
			list.add("%" + sourceSn + "%");
		}
		//单据状态
		if ( !ConvertUtil.isEmpty(docstatus) && docstatus.length > 0) {
			String os = "";
			for (int i = 0; i < docstatus.length; i++) {
				if (i == docstatus.length - 1)
					os += docstatus[i];
				else
					os += docstatus[i] + ",";
			}
			sql.append(" and dr.doc_status in (" + os + ")");
		}
		//流程状态
		if ( !ConvertUtil.isEmpty(wfStates) && wfStates.length > 0) {
			String os = "";
			for (int i = 0; i < wfStates.length; i++) {
				if (i == wfStates.length - 1)
					os += wfStates[i];
				else
					os += wfStates[i] + ",";
			}
			sql.append(" and dr.wf_state in (" + os + ")");
		}
		//充值客户
		if (!ConvertUtil.isEmpty(storeId) && storeId.length > 0) {
			String os = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					os += storeId[i];
				else
					os += storeId[i] + ",";
			}
			sql.append(" and  s.id in (" + os + ")");
		}
		//机构
		if (!ConvertUtil.isEmpty(saleOrgId) && saleOrgId.length > 0) {
			String os = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					os += saleOrgId[i];
				else
					os += saleOrgId[i] + ",";
			}
			sql.append(" and  so.id in (" + os + ")");
		}
		//收款账户
		if (!ConvertUtil.isEmpty(bankCardId) && bankCardId.length > 0) {
			String os = "";
			for (int i = 0; i < bankCardId.length; i++) {
				if (i == bankCardId.length - 1)
					os += bankCardId[i];
				else
					os += bankCardId[i] + ",";
			}
			sql.append(" and  bc.id in (" + os + ")");
		}
		//收款经营组织
		if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += organizationId[i];
				else
					os += organizationId[i] + ",";
			}
			sql.append(" and  bco.id in (" + os + ")");
		}
		//收款sbu
		if (!ConvertUtil.isEmpty(sbuId) && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += sbuId[i];
				else
					os += sbuId[i] + ",";
			}
			sql.append(" and  bcs.id in (" + os + ")");
		}
		
		//GL日期
		if (!ConvertUtil.isEmpty(glDateStartTime)) {
			sql.append(" and  dr.gl_date >= ?");
			list.add(glDateStartTime + " 00:00:00");
		}
		if (!ConvertUtil.isEmpty(glDateEndTime)) {
			sql.append(" and dr.gl_date <= ?");
			list.add(glDateEndTime + " 23:59:59");
		}
		
		//创建人
		if (!ConvertUtil.isEmpty(creatorId) && creatorId.length > 0) {
			String os = "";
			for (int i = 0; i < creatorId.length; i++) {
				if (i == creatorId.length - 1)
					os += creatorId[i];
				else
					os += creatorId[i] + ",";
			}
			sql.append(" and  smc.id in (" + os + ")");
		}

		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and dr.stores in (" + storeAuth + ")");
			}
		}

		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and so.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and so.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and sb.id in (" + sbuIds + ")");
			}else{
				sql.append(" and sb.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and og.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and og.id is null");
			}
		}
		
		sql.append(" order by dr.create_date desc");
		
		if (!ConvertUtil.isEmpty(page) && !ConvertUtil.isEmpty(size)) {
			 pageable.setPageNumber(page);
			 pageable.setPageSize(size);
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		Page<Map<String, Object>> pageMap = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		
		
		
		String totalsql = "select count(1) from ( " + sql + ") t";
		long total = getNativeDao().findInt(totalsql, objs);
		pageMap.setTotal(total);
		
		return pageMap;

	}
	

}