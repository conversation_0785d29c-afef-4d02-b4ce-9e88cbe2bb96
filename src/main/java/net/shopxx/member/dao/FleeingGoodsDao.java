package net.shopxx.member.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.FleeingGoods;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("fleeingGoodsDao")
public class FleeingGoodsDao extends DaoCenter {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	

    public Page<Map<String, Object>> findSelectPage(FleeingGoods fg, List<Object> param, Pageable pageable) {
        Object sid = param.get(0);
        Object createName = param.get(1);
        String firstTime = (String) param.get(2);
        String lastTime = (String) param.get(3);
        String firstTimef = (String) param.get(4);
        String lastTimef = (String) param.get(5);
        Object productSpec = param.get(6);
        Object productName = param.get(7);
        Object productModel = param.get(8);
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select fg.*,p.model,p.`name`,"
        		+ " p.spec, "
        		+ " s.`name` store_name,s.grant_code,sm.`name` create_name"
                + " from xx_fleeing_goods fg "
                + " left join xx_store s on s.id = fg.by_fleeing_good_store "
                + " left join xx_sale_org so on so.id = s.sale_org "
                + " left join xx_product p on p.id = fg.product "
                + " left join xx_store_member sm on sm.username = fg.b_creater "
                + " where 1=1 ");
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" and fg.company_info_id = ? ");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sid)) {
            sql.append(" and s.id = ? ");
            list.add(sid);
        }
        if (!ConvertUtil.isEmpty(fg.getSn())) {
            sql.append(" and fg.sn = ? ");
            list.add(fg.getSn());
        }
        if (!ConvertUtil.isEmpty(fg.getStatus())) {
            sql.append(" and fg.status = ? ");
            list.add(fg.getStatus());
        }
        if (!ConvertUtil.isEmpty(fg.getByFleeingGoodsDealers())) {
            sql.append(" and fg.by_fleeing_goods_dealers = ? ");
            list.add(fg.getByFleeingGoodsDealers());
        }
        if (!ConvertUtil.isEmpty(fg.getFleeingGoodsNumber())) {
            sql.append(" and fg.fleeing_goods_number = ? ");
            list.add(fg.getFleeingGoodsNumber());
        }
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and fg.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and fg.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (!ConvertUtil.isEmpty(firstTimef)) {
			sql.append(" and fg.fleeing_goods_time >= ?");
			list.add(DateUtil.convert(firstTimef + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTimef)) {
			sql.append(" and fg.fleeing_goods_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTimef + " 00:00:00"),1));
		}
        if (!ConvertUtil.isEmpty(productSpec)) {
            sql.append(" and p.spec = ? ");
            list.add(productSpec);
        }
        if (!ConvertUtil.isEmpty(productName)) {
            sql.append(" and p.`name` like ? ");
            list.add("%"+productName+"%");
        }
        if (!ConvertUtil.isEmpty(productModel)) {
            sql.append(" and p.model like ? ");
            list.add("%"+productModel+"%");
        }
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        sql.append(" GROUP BY fg.id order by fg.create_date desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
          objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }

	public List<Map<String, Object>> findFleeingGoodsAttach(Long id,
			Integer type) {
		StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select * "
                + " from xx_fleeing_goods_attach "
                + " where company_info_id = ? and fleeing_goods = ? and type = ? ");
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);
        list.add(type);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
	
	public Integer count(FleeingGoods fg, List<Object> param) {
		Object sid = param.get(0);
        Object createName = param.get(1);
        String firstTime = (String) param.get(2);
        String lastTime = (String) param.get(3);
        String firstTimef = (String) param.get(4);
        String lastTimef = (String) param.get(5);
        Object productSpec = param.get(6);
        Object productName = param.get(7);
        Object productModel = param.get(8);
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select count(distinct fg.id)  from xx_fleeing_goods fg "
                + " left join xx_store s on s.id = fg.by_fleeing_good_store "
                + " left join xx_sale_org so on so.id = s.sale_org "
                + " left join xx_product p on p.id = fg.product "
                + " left join xx_store_member sm on sm.username = fg.b_creater "
                + " where 1=1 ");
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" and fg.company_info_id = ? ");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sid)) {
            sql.append(" and s.id = ? ");
            list.add(sid);
        }
        if (!ConvertUtil.isEmpty(fg.getSn())) {
            sql.append(" and fg.sn = ? ");
            list.add(fg.getSn());
        }
        if (!ConvertUtil.isEmpty(fg.getStatus())) {
            sql.append(" and fg.status = ? ");
            list.add(fg.getStatus());
        }
        if (!ConvertUtil.isEmpty(fg.getByFleeingGoodsDealers())) {
            sql.append(" and fg.by_fleeing_goods_dealers = ? ");
            list.add(fg.getByFleeingGoodsDealers());
        }
        if (!ConvertUtil.isEmpty(fg.getFleeingGoodsNumber())) {
            sql.append(" and fg.fleeing_goods_number = ? ");
            list.add(fg.getFleeingGoodsNumber());
        }
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and fg.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and fg.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (!ConvertUtil.isEmpty(firstTimef)) {
			sql.append(" and fg.fleeing_goods_time >= ?");
			list.add(DateUtil.convert(firstTimef + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTimef)) {
			sql.append(" and fg.fleeing_goods_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTimef + " 00:00:00"),1));
		}
        if (!ConvertUtil.isEmpty(productSpec)) {
            sql.append(" and p.spec = ? ");
            list.add(productSpec);
        }
        if (!ConvertUtil.isEmpty(productName)) {
            sql.append(" and p.`name` like ? ");
            list.add("%"+productName+"%");
        }
        if (!ConvertUtil.isEmpty(productModel)) {
            sql.append(" and p.model like ? ");
            list.add("%"+productModel+"%");
        }
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
          objs[i] = list.get(i);
        }
        return getNativeDao().findInt(sql.toString(), objs);
	}

	
	public List<Map<String, Object>> findList(FleeingGoods fg, List<Object> param, Pageable pageable, Integer page,
                                              Integer size) {
		Object sid = param.get(0);
        Object createName = param.get(1);
        String firstTime = (String) param.get(2);
        String lastTime = (String) param.get(3);
        String firstTimef = (String) param.get(4);
        String lastTimef = (String) param.get(5);
        Object productSpec = param.get(6);
        Object productName = param.get(7);
        Object productModel = param.get(8);
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select fg.*,p.model,p.`name`,"
        		+ " p.spec, "
        		+ " s.`name` store_name,s.grant_code,sm.`name` create_name"
                + " from xx_fleeing_goods fg "
                + " left join xx_store s on s.id = fg.by_fleeing_good_store "
                + " left join xx_sale_org so on so.id = s.sale_org "
                + " left join xx_product p on p.id = fg.product "
                + " left join xx_store_member sm on sm.username = fg.b_creater "
                + " where 1=1 ");
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" and fg.company_info_id = ? ");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sid)) {
            sql.append(" and s.id = ? ");
            list.add(sid);
        }
        if (!ConvertUtil.isEmpty(fg.getSn())) {
            sql.append(" and fg.sn = ? ");
            list.add(fg.getSn());
        }
        if (!ConvertUtil.isEmpty(fg.getStatus())) {
            sql.append(" and fg.status = ? ");
            list.add(fg.getStatus());
        }
        if (!ConvertUtil.isEmpty(fg.getByFleeingGoodsDealers())) {
            sql.append(" and fg.by_fleeing_goods_dealers = ? ");
            list.add(fg.getByFleeingGoodsDealers());
        }
        if (!ConvertUtil.isEmpty(fg.getFleeingGoodsNumber())) {
            sql.append(" and fg.fleeing_goods_number = ? ");
            list.add(fg.getFleeingGoodsNumber());
        }
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and fg.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and fg.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
		if (!ConvertUtil.isEmpty(firstTimef)) {
			sql.append(" and fg.fleeing_goods_time >= ?");
			list.add(DateUtil.convert(firstTimef + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTimef)) {
			sql.append(" and fg.fleeing_goods_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTimef + " 00:00:00"),1));
		}
        if (!ConvertUtil.isEmpty(productSpec)) {
            sql.append(" and p.spec = ? ");
            list.add(productSpec);
        }
        if (!ConvertUtil.isEmpty(productName)) {
            sql.append(" and p.`name` like ? ");
            list.add("%"+productName+"%");
        }
        if (!ConvertUtil.isEmpty(productModel)) {
            sql.append(" and p.model like ? ");
            list.add("%"+productModel+"%");
        }
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        sql.append(" GROUP BY fg.id order by fg.create_date desc");
        if (page != null && size != null) {
        	sql.append(" limit " + (size * (page - 1)) + "," + size);
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
          objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

}
