package net.shopxx.member.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.StoreMemberSaleOrgPost;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;

@Repository("storeMemberSaleOrgPostDao")
public class StoreMemberSaleOrgPostDao extends DaoCenter{
	
	public Boolean checkPostCodeByStoreMember(StoreMember storeMember, String postCode) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT count(1) FROM xx_store_member_sale_org_post smsop ,xx_post p WHERE 1=1 AND p.id = smsop.post ");
		if(companyInfoId != null){
			sql.append(" AND smsop.company_info_id = ?");
			list.add(companyInfoId);
		}
		if(storeMember != null){
			sql.append(" AND smsop.store_member = ? ");
			list.add(storeMember);
		}
		if(postCode != null){
			sql.append(" AND p.sn = ?");
			list.add(postCode);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		int i = getNativeDao().findInt(sql.toString(), objs);
		return i>0;
	}

	public Page<Map<String,Object>> findPageByPost(Long postId, Long saleOrgId, Pageable pageable) {
		StringBuffer sql = new StringBuffer();
		List<Object> params = new ArrayList<Object>();
		sql.append("select p.name post_name,sm.name,sm.id store_member_id,so.name sale_org_name ");
		sql.append("FROM xx_store_member_sale_org_post smsop "
				+ "LEFT JOIN xx_post p ON p.id = smsop.post "
				+ " LEFT JOIN xx_store_member sm ON smsop.store_member = sm.id "
				+ " LEFT JOIN xx_sale_org so on so.id = smsop.sale_org "
				+ "WHERE 1 = 1 "
		);

		sql.append(" AND smsop.company_info_id = ? ");
		params.add(WebUtils.getCurrentCompanyInfoId());

		if(postId != null){
			sql.append(" AND smsop.post = ? ");
			params.add(postId);
		}

		if(saleOrgId != null){
			sql.append(" AND smsop.sale_org = ? ");
			params.add(saleOrgId);
		}
		return getNativeDao().findPageMap(sql.toString(),params.toArray(),pageable);
	}

	public List<StoreMember> findPostCodeByStoreMember(Long saleOrgId, String postCode) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT sm.* FROM xx_store_member_sale_org_post smsop "
				+" LEFT JOIN xx_store_member sm ON sm.id = smsop.store_member "
				+" LEFT JOIN xx_post p ON p.id = smsop.post "
				+" LEFT JOIN xx_sale_org so ON so.id = smsop.sale_org "
				+" WHERE 1=1 ");
		if (companyInfoId != null) {
			sql.append(" AND smsop.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (saleOrgId != null) {
			sql.append(" AND smsop.sale_org = ? ");
			list.add(saleOrgId);
		}
		if (postCode != null) {
			sql.append(" AND p.sn = ?");
			list.add(postCode);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<StoreMember> s = getNativeDao().findListManaged(sql.toString(), objs, 0, StoreMember.class);
		if(s==null){
			return null;
		}
		if(s.size()<=0){
			return null;
		}
		return s;
	}

	public Page<Map<String,Object>> findOrderManager(String[] postCode,String name,Pageable pageable){
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT sm.`name`,sm.id,p.`name` post_name,so.`name` sale_org_name FROM  xx_store_member_sale_org_post smsop ");
		sql.append(" INNER JOIN xx_post p ON p.id = smsop.post ");
		sql.append(" INNER JOIN xx_store_member sm ON sm.id = smsop.store_member ");
		sql.append(" INNER JOIN xx_sale_org so ON so.id = smsop.sale_org ");
		sql.append(" WHERE 1=1 ");
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" AND smsop.company_info_id = ? ");
			list.add(companyInfoId);
		}
		if(!ConvertUtil.isEmpty(name)){
			sql.append(" AND sm.`name` LIKE ? ");
			list.add("%"+name+"%");
		}
		if (postCode != null && postCode.length > 0) {
			String os = "";
			for (int i = 0; i < postCode.length; i++) {
				if (i == postCode.length - 1)
					os += "'" + postCode[i] + "'";
				else
					os += "'" + postCode[i] + "',";
			}
			sql.append(" AND p.sn in (" + os + ") ");
		}
		sql.append(" GROUP BY sm.id  ");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public Page<Map<String, Object>> findStoreMemberByPost(String[] postCode, String name, SaleOrg saleOrg,
														   Pageable pageable) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT sm.`name`,sm.id,p.`name` post_name,so.`name` sale_org_name FROM  xx_store_member_sale_org_post smsop ");
		sql.append(" INNER JOIN xx_post p ON p.id = smsop.post ");
		sql.append(" INNER JOIN xx_store_member sm ON sm.id = smsop.store_member AND sm.is_enabled = 1  ");
		sql.append(" INNER JOIN xx_sale_org so ON so.id = smsop.sale_org ");
		sql.append(" WHERE 1=1 ");
		if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" AND smsop.company_info_id = ? ");
			list.add(companyInfoId);
		}
		if(!ConvertUtil.isEmpty(name)){
			sql.append(" AND sm.`name` LIKE ? ");
			list.add("%"+name+"%");
		}
		if (postCode != null && postCode.length > 0) {
			String os = "";
			for (int i = 0; i < postCode.length; i++) {
				if (i == postCode.length - 1)
					os += "'" + postCode[i] + "'";
				else
					os += "'" + postCode[i] + "',";
			}
			sql.append(" AND p.sn in (" + os + ") ");
		}
		if (saleOrg!=null) {
			sql.append(" AND smsop.sale_org = ? ");
			list.add(saleOrg.getId());
		}
		sql.append(" GROUP BY sm.id  ");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}
}
