package net.shopxx.member.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreExit;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("storeExitDao")
public class StoreExitDao extends DaoCenter {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

    public Page<Map<String, Object>> findPage(StoreExit se, List<Object> param, Pageable pageable) {
        StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Object storeId = param.get(0);
        Object isCoreStroe = param.get(1);
        Object saleOrg = param.get(2);
        Object areaManager = param.get(3);
        String firstTimee = (String) param.get(4);
        String lastTimee = (String) param.get(5);
        String firstTime = (String) param.get(6);
        String lastTime = (String) param.get(7);
        Object createName = param.get(8);
        sql.append("select se.*, "
        		+ " s.dealer_name,s.`name`,s.grant_code,s.alias,s.region, "
        		+ " ( CASE s.is_core_stroe WHEN 1 THEN '是' WHEN 0 THEN '否' END ) is_core,"
        		+ " so.`name` sale_org_name,sam.`name` manager_name,sm.`name` create_name "
        		+ " from xx_store_exit se "
                + " left join xx_store s on s.id = se.store "
                + " left join xx_sale_org so on so.id = s.sale_org "
                + " LEFT JOIN xx_store_member sm ON sm.username = se.b_creater "
                + " left join xx_store_member sam on sam.id = s.store_member "
                + " where 1=1 ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and se.company_info_id = ?");
			list.add(companyInfoId);
		}
        if(!ConvertUtil.isEmpty(se.getSn())){
			sql.append(" and se.sn = ? ");
			list.add(se.getSn());
		}
        if(!ConvertUtil.isEmpty(se.getStatus())){
			sql.append(" and se.status = ? ");
			list.add(se.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(saleOrg)){
			sql.append(" and so.`name` like ? ");
			list.add("%"+saleOrg+"%");
		}
        if(!ConvertUtil.isEmpty(areaManager)){
			sql.append(" and sam.`name` like ? ");
			list.add("%"+areaManager+"%");
		}
        if(!ConvertUtil.isEmpty(isCoreStroe)){
			sql.append(" and s.is_core_stroe = ? ");
			list.add(isCoreStroe);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTimee)) {
			sql.append(" and se.exit_time >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTimee)) {
			sql.append(" and se.exit_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTimee + " 00:00:00"),1));
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and se.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and se.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        sql.append(" GROUP BY se.id order by se.id desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }
    
    public List<Map<String, Object>> findStoreExitAttach(Long id, Integer type) {
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select * "
                + " from xx_store_exit_attach "
                + " where company_info_id = ? and store_exits = ? and type = ? ");
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);
        list.add(type);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }
    
    public List<Map<String, Object>> findStoreExitShopInfo(Long id){
    	if(id==null){
    		return null;
    	}
    	List<Object> list = new ArrayList<Object>();
    	Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
    	StringBuilder sql = new StringBuilder();
    	sql.append("SELECT si.sn,si.authorization_code,si.shop_name,si.id,"
                + "a.full_name,sesi.is_close,sesi.is_exit,sesi.cause,sesi.off_time,sesi.decrease_code"
    			+ " FROM xx_store_exit_shop_info sesi "
    			+ " LEFT JOIN xx_shop_info si ON si.id = sesi.shop_info "
    			+ " LEFT JOIN xx_area a ON a.id = si.area "
    			+ " WHERE 1=1 ");
    	
    	if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND sesi.company_info_id =  ? ");
			sql.append(" AND si.company_info_id = ? ");
			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		sql.append(" AND store_exit = ? ");
		list.add(id);
    	sql.append(" GROUP BY sesi.id ORDER BY si.id ASC");
    	Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
    	return getNativeDao().findListMap(sql.toString(), objs, 0);
    }
    
    public List<Map<String, Object>> findList(StoreExit se, List<Object> param, Pageable pageable, Integer page,
                                              Integer size) {
        StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Object storeId = param.get(0);
        Object isCoreStroe = param.get(1);
        Object saleOrg = param.get(2);
        Object areaManager = param.get(3);
        String firstTimee = (String) param.get(4);
        String lastTimee = (String) param.get(5);
        String firstTime = (String) param.get(6);
        String lastTime = (String) param.get(7);
        Object createName = param.get(8);
        sql.append("select se.*, "
        		+ " s.dealer_name,s.`name`,s.grant_code,s.alias,s.region, "
        		+ " ( CASE s.is_core_stroe WHEN 1 THEN '是' WHEN 0 THEN '否' END ) is_core,"
        		+ " so.`name` sale_org_name,sam.`name` manager_name,sm.`name` create_name "
        		+ " from xx_store_exit se "
                + " left join xx_store s on s.id = se.store "
                + " left join xx_sale_org so on so.id = s.sale_org "
                + " LEFT JOIN xx_store_member sm ON sm.username = se.b_creater "
                + " left join xx_store_member sam on sam.id = s.store_member "
                + " where 1=1 ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and se.company_info_id = ?");
			list.add(companyInfoId);
		}
        if(!ConvertUtil.isEmpty(se.getSn())){
			sql.append(" and se.sn = ? ");
			list.add(se.getSn());
		}
        if(!ConvertUtil.isEmpty(se.getStatus())){
			sql.append(" and se.status = ? ");
			list.add(se.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(saleOrg)){
			sql.append(" and so.`name` like ? ");
			list.add("%"+saleOrg+"%");
		}
        if(!ConvertUtil.isEmpty(areaManager)){
			sql.append(" and sam.`name` like ? ");
			list.add("%"+areaManager+"%");
		}
        if(!ConvertUtil.isEmpty(isCoreStroe)){
			sql.append(" and s.is_core_stroe = ? ");
			list.add(isCoreStroe);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTimee)) {
			sql.append(" and se.exit_time >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTimee)) {
			sql.append(" and se.exit_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTimee + " 00:00:00"),1));
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and se.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and se.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        sql.append(" GROUP BY se.id order by se.id desc");
        if (page != null && size != null) {
        	sql.append(" limit " + (size * (page - 1)) + "," + size);
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }
    
    
    public Integer count(StoreExit se, List<Object> param) {
        StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Object storeId = param.get(0);
        Object isCoreStroe = param.get(1);
        Object saleOrg = param.get(2);
        Object areaManager = param.get(3);
        String firstTimee = (String) param.get(4);
        String lastTimee = (String) param.get(5);
        String firstTime = (String) param.get(6);
        String lastTime = (String) param.get(7);
        Object createName = param.get(8);
        sql.append("select count(distinct se.id) from xx_store_exit se "
                + " left join xx_store s on s.id = se.store "
                + " left join xx_sale_org so on so.id = s.sale_org "
                + " LEFT JOIN xx_store_member sm ON sm.username = se.b_creater "
                + " left join xx_store_member sam on sam.id = s.store_member "
                + " where 1=1 ");
        if(!ConvertUtil.isEmpty(companyInfoId)){
			sql.append(" and se.company_info_id = ?");
			list.add(companyInfoId);
		}
        if(!ConvertUtil.isEmpty(se.getSn())){
			sql.append(" and se.sn = ? ");
			list.add(se.getSn());
		}
        if(!ConvertUtil.isEmpty(se.getStatus())){
			sql.append(" and se.status = ? ");
			list.add(se.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(saleOrg)){
			sql.append(" and so.`name` like ? ");
			list.add("%"+saleOrg+"%");
		}
        if(!ConvertUtil.isEmpty(areaManager)){
			sql.append(" and sam.`name` like ? ");
			list.add("%"+areaManager+"%");
		}
        if(!ConvertUtil.isEmpty(isCoreStroe)){
			sql.append(" and s.is_core_stroe = ? ");
			list.add(isCoreStroe);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTimee)) {
			sql.append(" and se.exit_time >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTimee)) {
			sql.append(" and se.exit_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTimee + " 00:00:00"),1));
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and se.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and se.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findInt(sql.toString(), objs);
    }
 
}
