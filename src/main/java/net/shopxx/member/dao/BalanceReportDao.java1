package net.shopxx.member.dao;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.BalanceReport;
@Repository("memberBalanceReportDao")
public class BalanceReportDao extends DaoCenter{
	
	
	/**
	 * 根据客户、sub、经营组织、单据时间查找客户余额报表
	 * @param storeId
	 * @param sbuId
	 * @param organizationId
	 * @param billDate
	 * @return
	 */
	public List<BalanceReport> findBalanceReportList(Long storeId,
			Long sbuId,Long organizationId,String billDate) {

		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append(" SELECT * FROM xx_balance_report br WHERE 1=1 ");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and br.company_info_id = ?");
			list.add(companyInfoId);
		}
		//客户
		if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and br.store = ?");
			list.add(storeId);
		}
		//sub
		if(!ConvertUtil.isEmpty(sbuId)){
			sql.append(" and br.sbu = ?");
			list.add(sbuId);
		}
		//经营组织
		if(!ConvertUtil.isEmpty(organizationId)){
			sql.append(" and br.organization = ?");
			list.add(organizationId);
		}
		if (!ConvertUtil.isEmpty(billDate)) {
			sql.append(" and br.bill_date = ?");
			list.add(billDate);
		}
		sql.append(" order by br.modify_date desc ");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		List<BalanceReport> balanceReportList = getNativeDao().findListManaged(sql.toString(),objs,0,BalanceReport.class);

		return balanceReportList;
	}


}
