package net.shopxx.member.job;

import java.util.Date;
import java.util.List;

import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.member.entity.CreditRecharge;
import net.shopxx.member.service.CreditRechargeService;

import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component("creditRechargeJob")
@Lazy(false)
public class CreditRechargeJob {

	@Scheduled(cron = "0 10 0,1,2 * * ?")
	public void effective() {

		LogUtils.info("授信额度生效失效调度。。。");

		CreditRechargeService creditRechargeService = SpringUtils.getBean("creditRechargeServiceImpl",
				CreditRechargeService.class);
		String sql = "select * from xx_credit_recharge where type = 0 and doc_status=2 and start_date <= ? and end_date > ?";
		Date now = new Date();
		List<CreditRecharge> creditRecharges = creditRechargeService.getDaoCenter()
				.getNativeDao()
				.findListManaged(sql,
						new Object[] { now, now },
						0,
						CreditRecharge.class);
		for (CreditRecharge creditRecharge : creditRecharges) {
			try {
				creditRechargeService.effective(creditRecharge);
			}
			catch (Exception e) {
				LogUtils.error("授信额度生效调度", e);
			}
		}

		sql = "select * from xx_credit_recharge where type = 1 and doc_status=2 and date_add(end_date,interval 1 day) <= ?";
		now = new Date();
		List<CreditRecharge> crs = creditRechargeService.getDaoCenter()
				.getNativeDao()
				.findListManaged(sql,
						new Object[] { now },
						0,
						CreditRecharge.class);
		for (CreditRecharge creditRecharge : crs) {
			try {
				creditRechargeService.invalid(creditRecharge);
			}
			catch (Exception e) {
				LogUtils.error("授信额度失效调度", e);
			}
		}
	}
}
