package net.shopxx.member.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.StoreAddressService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreInvoiceInfoService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.order.service.ContractService;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductStoreBaseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.util.CommonUtil;

/**
 * Controller - 客户地址
 */
@Controller("storeAddressController")
@RequestMapping("/member/store/store_address")
public class StoreAddressController extends BaseController{
	
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardBaseService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreBaseService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "productStoreBaseServiceImpl")
	private ProductStoreBaseService productStoreBaseService;
	@Resource(name = "storeInvoiceInfoServiceImpl")
	private StoreInvoiceInfoService storeInvoiceInfoService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "storeAddressServiceImpl")
	private StoreAddressService storeAddressService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	@Resource(name = "contractServiceImpl")
	private ContractService contractService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Integer isEdit,Long menuId,
			Integer isStore, Integer readOnly, ModelMap model) {
		model.addAttribute("isEdit", isEdit);
		model.addAttribute("code", code);
		model.addAttribute("isStore", isStore);
		model.addAttribute("readOnly", readOnly);
        model.addAttribute("menuId", menuId);
		return CommonUtil.getFolderPrefix(code) + "/member/store/store_address/list_tb";
	}
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Integer isEdit,Long userId, Long menuId,
			Integer isStore, Integer readOnly, Pageable pageable, ModelMap model) {

		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, fis, null));
		model.addAttribute("isEdit", isEdit);
		//新增客户
		fis.clear();
		fis.add(Filter.eq("code", "CustomerType"));
		fis.add(Filter.eq("isEnabled", true));
		fis.add(Filter.isNotNull("parent"));
		List<SystemDict> customerTypes = systemDictBaseService.findList(null,
				fis,
				null);
		model.addAttribute("customerTypes", customerTypes);
		List<Integer> types = storeBaseService.getTypes();
		model.addAttribute("types", types);
		model.addAttribute("code", code);
		model.addAttribute("isStore", isStore);
		model.addAttribute("readOnly", readOnly);
		//销售面价
		boolean role = storeBaseService.findByRole(storeMember, "管理员");
		model.addAttribute("role",role);
        //获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);
		return CommonUtil.getFolderPrefix(code) + "/member/store/store_address/list";
	}
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String name, String outTradeNo,
			String address, Pageable pageable, ModelMap model) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Page<Map<String, Object>> page = storeAddressService.findPage(name,
				outTradeNo,
				address,
				companyInfoId,
				pageable,
				model);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	

}
