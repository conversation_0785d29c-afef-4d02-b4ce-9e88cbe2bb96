package net.shopxx.member.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreStop;
import net.shopxx.member.entity.StoreStopAttach;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreStopAttachService;
import net.shopxx.member.service.StoreStopService;
import net.shopxx.util.CommonUtil;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 经销商终止
 */
@Controller("storeStopController")
@RequestMapping("/member/store_stop")
public class StoreStopController extends BaseController {


    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeStopServiceImpl")
    private StoreStopService storeStopService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "storeStopAttachServiceImpl")
    private StoreStopAttachService storeStopAttachService;
    
    /*
     * 列表
     */
    @RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
    public String list_tb(@PathVariable String code, Integer isCheck,
            Long objTypeId, Long objid, Long sid, Integer type, ModelMap model) {
        model.addAttribute("isCheck", isCheck);
        model.addAttribute("objTypeId", objTypeId);
        model.addAttribute("objid", objid);
        model.addAttribute("type", type);
        model.addAttribute("code", code);
        model.addAttribute("sid", sid);
        return CommonUtil.getFolderPrefix(code) + "/member/store_stop/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
    public String list(@PathVariable String code,Pageable pageable, ModelMap model) {
        return CommonUtil.getFolderPrefix(code) + "/member/store_stop/list";
    }


    
    /**
     * 添加
     */
    @RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
    public String add(@PathVariable String code, ModelMap model, Long sid, 
            Integer type, Pageable pageable) {
        model.addAttribute("code", code);
        Store store = storeBaseService.find(sid);
        if (store != null) {
            model.addAttribute("store", store);
        }
        return CommonUtil.getFolderPrefix(code) + "/member/store_stop/add";

    }

    
    /*
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(StoreStop ss, Long storeId, String isCoreStroe,
                               String createName, String firstTime, String lastTime,
                               Pageable pageable, ModelMap model) {
        List<Object> param = new ArrayList<Object>();
        param.add(storeId);
        param.add(isCoreStroe);
        param.add(createName);
        param.add(firstTime);
        param.add(lastTime);
        Page<Map<String, Object>> page = storeStopService.findPage(ss,param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

    /**
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMsg save(StoreStop storeStop, Long sid) {
        Store store = storeBaseService.find(sid);
        if (store == null) {
            return error("客户不能为空！");
        }
        if (store.getIsCoreStroe() == null) {
            return error("当前客户没有维护是否核心经销商！");
        }
        storeStop.setStore(store);
        storeStopService.saveStoreStop(storeStop);
        return success().addObjX(storeStop.getId());
    }

    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg update(StoreStop storeStop	) {
    	Store store = storeStopService.find(storeStop.getId()).getStore();
        if (store == null) {
            return error("客户不能为空！");
        }
        if (store.getIsCoreStroe() == null) {
            return error("当前客户没有维护是否核心经销商！");
        }
        storeStop.setStore(store);
        storeStopService.updateStoreStop(storeStop, null);
        return success().addObjX(storeStop.getId());
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
    public String edit(@PathVariable String code,Long id, ModelMap model) {
        StoreStop storeStop = storeStopService.find(id);
        model.addAttribute("storeStop",storeStop);
        model.addAttribute("store",storeStop.getStore());
        model.addAttribute("code",code);
        //查询附件
        List<StoreStopAttach> storeStopAttaches0 = this.findAttachs(storeStop, 0);
        List<StoreStopAttach> storeStopAttaches1 = this.findAttachs(storeStop, 1);
        List<StoreStopAttach> storeStopAttaches2 = this.findAttachs(storeStop, 2);
        List<StoreStopAttach> storeStopAttaches3 = this.findAttachs(storeStop, 3);
        List<StoreStopAttach> storeStopAttaches4 = this.findAttachs(storeStop, 4);
        model.addAttribute("storeStopAttachs_0",JsonUtils.toJson(storeStopAttaches0));
        model.addAttribute("storeStopAttachs_1",JsonUtils.toJson(storeStopAttaches1));
        model.addAttribute("storeStopAttachs_2",JsonUtils.toJson(storeStopAttaches2));
        model.addAttribute("storeStopAttachs_3",JsonUtils.toJson(storeStopAttaches3));
        model.addAttribute("storeStopAttachs_4",JsonUtils.toJson(storeStopAttaches4));

        //审核流程
        StoreMember storeMember = storeMemberBaseService.getCurrent();

        if(storeStop.getWfId()!= null){
            ActWf wf = storeStopService.getWfByWfId(storeStop.getWfId());

            if (wf != null) {
                //省长审核
                Boolean szsh = false;
                //渠道专员
                Boolean qdzy = false;
                //省长审核权限
                Boolean szshs = false;
                //渠道专员权限
                Boolean qdzys = false;
                //渠道部
                Boolean qdbsh = false;
                Boolean qdbshs = false;
                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for(Map<String, Object> c : item){
                    if(c.get("suggestion")!=null){
                        //处理结果
                        String approved = c.get("approved")!=null?c.get("approved").toString():"false";
                        //节点名称
                        String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
                        //对比节点名称是否对应
                        if(rwm.contains("省长")){
                            szsh = Boolean.valueOf(approved);
                        }
                        if(rwm.contains("渠道专员")){
                            qdzy = Boolean.valueOf(approved);
                            if(!qdzy){
                                szsh = false;
                            }
                        }
                        if(rwm.contains("渠道部")){
                        	qdbsh = Boolean.valueOf(approved);
                        }
                    }
                }
                //获取当前流程所在的节点
                Task t = storeStopService.getCurrTaskByWf(wf);
                if(t!=null){
                    //获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
                        szshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道专员")){
                        szshs = true;qdzys = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道部")){
                        szshs = true;qdzys = true;qdbshs = true;
                    }
                }
                model.addAttribute("node", t);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzys", qdzys);
                model.addAttribute("qdbsh", qdbsh);
                model.addAttribute("qdbshs", qdbshs);
            }
        }
        return CommonUtil.getFolderPrefix(code) + "/member/store_stop/edit";
    }

    /**
     * 审核
     * @param id shopId
     * @return
     */
    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public @ResponseBody ResultMsg check_wf(Long id,String modelId,Long objTypeId) {
        if (id == null) {
            // 请选择订单
            return error("请选择单据");
        }
//        StoreMember storeMember = storeMemberBaseService.getCurrent();
//        StoreStop storeStop = storeStopService.find(id);
//
//        storeStopService.createWf(storeStop.getSn(),
//                String.valueOf(storeMember.getId()),
//                modelId,
//                objTypeId,
//                id,
//                WebUtils.getCurrentCompanyInfoId());

        storeStopService.createWf(id, modelId, objTypeId);

        return success();
    }


    /**
     * 流程节点保存
     */
    @RequestMapping(value = "/saveform", method = RequestMethod.POST)
    public @ResponseBody ResultMsg saveform(StoreStop storeStop, Integer type) {
        // type 用来定义节点
        //type 1区域经理 2省长 3财务部
        storeStopService.saveform(type,storeStop);
        return success().addObjX(storeStop.getId());
    }

    //查找附件
    private List<StoreStopAttach> findAttachs (StoreStop storeStop, Integer type){
        List<Filter> filterss = new ArrayList<Filter>();
        filterss.add(Filter.eq("storeStop",storeStop));
        filterss.add(Filter.eq("type",type));
        List<StoreStopAttach> storeStopAttachs = storeStopAttachService.findList(null, filterss, null);
        return storeStopAttachs;
    }
    
    @RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(StoreStop ss, Long storeId, String isCoreStroe,
                                        String createName, String firstTime, String lastTime, String[] header,
                                        String[] properties, Pageable pageable, ModelMap model, Integer page){
    	 List<Object> param = new ArrayList<Object>();
         param.add(storeId);
         param.add(isCoreStroe);
         param.add(createName);
         param.add(firstTime);
         param.add(lastTime);
    	Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = storeStopService.findList(ss, param, pageable, page, size);
		return getModelAndViewForList(data,header,properties, model);
    }
    
    /**
	 * 条件导出
	 * 
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(StoreStop ss, Long storeId, String isCoreStroe,
                                                                     String createName, String firstTime, String lastTime){
		List<Object> param = new ArrayList<Object>();
        param.add(storeId);
        param.add(isCoreStroe);
        param.add(createName);
        param.add(firstTime);
        param.add(lastTime);
		int size = storeStopService.count(ss,param);
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,String[] header,String[] properties, ModelMap model) {
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置列宽
		Integer[] widths = {};
		if(header==null){
			header = new String[]{};
		}
		if(properties==null){
			properties = new String[]{};
		}else{
			List<Integer> w = new ArrayList<Integer>();
			for(int i=0;i<properties.length;i++){
				w.add(25 * 256);
			}
			widths = w.toArray(new Integer[]{w.size()});
		}
		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}
    
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
}
