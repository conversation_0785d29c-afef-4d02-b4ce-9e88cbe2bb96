package net.shopxx.member.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.activiti.engine.task.Task;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.dao.impl.NativeDao;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.AdjustmentService;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.DepositAttachService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.entity.CustomerContract;
import net.shopxx.order.service.ContractService;
import net.shopxx.order.service.CustomerContractService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
@Controller("shopMemberStoreRechargeController")
@RequestMapping("/member/store_recharge")
public class StoreRechargeController extends BaseController {

	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "depositAttachServiceImpl")
	private DepositAttachService depositAttachService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardService;
	@Resource(name = "contractServiceImpl")
	private ContractService contractService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name="customerContractServiceImpl")
	private CustomerContractService customerContractService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "adjustmentServiceImpl") 
	private AdjustmentService adjustmentService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer flag,Long sbuId,Long objid,Long menuId,
			ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("objid", objid);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("menuId", menuId);
		return "/member/store_recharge/list_tb";
	}
 
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb_code(@PathVariable String code, Integer flag,
			Integer type, Long objTypeId, Long objid,Long sbuId, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("code", code);
		model.addAttribute("sbuId", sbuId);
		return CommonUtil.getFolderPrefix(code)
				+ "/member/store_recharge/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Integer flag, Long sbuId, Long userId,
			Long menuId, ModelMap model) {
		
		model.addAttribute("flag", flag);
		model.addAttribute("sbuId", sbuId);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		//充值类型
		String rechargeTypeIds = depositRechargeService.getRechargeTypeIds(flag);
		model.addAttribute("rechargeTypeIds", rechargeTypeIds);
		return "/member/store_recharge/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list_code(@PathVariable String code, Integer flag,
			Integer type,Long sbuId, Pageable pageable, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("rechargeTypes", rechargeTypes);
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("code", code);
		model.addAttribute("sbuId", sbuId);
		return CommonUtil.getFolderPrefix(code) + "/member/store_recharge/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn, Integer[] status, Integer[] docstatus,Long[] rechargeTypeId, 
			Long creatorId, Long operatorId,Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice, Integer flag,Long organizationId,Long sbuId,
			String sourceSn,String adjustmentSn,Long[] bankCardId,Long[] bcOrganizationId,Long[] bcSbuId,
			Pageable pageable, ModelMap model) {

		if (!Integer.valueOf(1).equals(flag)) {
			//非审核页面进入
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("code", "DepositRechargeType"));
			filters.add(Filter.eq("value", "客户充值"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> rechargeTypes = systemDictService.findList(null,
					filters,
					null);
			if (rechargeTypes.size() == 0) {
				return this.error("查询失败，充值类型【客户充值】不存在");
			}
			else if (rechargeTypes.size() > 1) {
				return this.error("查询失败，充值类型【客户充值】存在多个");
			}
			rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		}

		String jsonPage = JsonUtils.toJson(depositRechargeService.newfindPage(new Integer[] { 1 },
				sn,null,storeId,null,status,docstatus,rechargeTypeId,creatorId,operatorId,minPrice,
				maxPrice,firstTime,lastTime,organizationId,sbuId,sourceSn,adjustmentSn,bankCardId,
				bcOrganizationId,bcSbuId,pageable));
		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Integer flag, Integer type,Long sbuId, ModelMap model) {
		
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("rechargeTypes", rechargeTypes);
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("companyId", WebUtils.getCurrentCompanyInfoId());
		model.addAttribute("nowDate", df.format(new Date()));

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
				BankCard bankCard = null;

				filters.clear();
				filters.add(Filter.eq("saleOrg",
						saleOrgService.find(storeMemberSaleOrg.getSaleOrg()
								.getId())));
				List<BankCard> bankCards = bankCardService.findList(null,
						filters,
						null);
				if (bankCards != null && bankCards.size() > 0) {
					bankCard = bankCards.get(0);
				}
				model.addAttribute("bankCard", bankCard == null ? "" : bankCard);
			}
		}else if (storeMember.getMemberType() == 1) {
			NativeDao nativeDao = storeMemberService.getDaoCenter().getNativeDao();
			StringBuilder sql = new StringBuilder();
			sql.append("select s.* from xx_store_member sm, xx_store s");
			sql.append(" where sm.store = s.id");
			sql.append(" and s.is_enabled = 1 and s.is_main_store = 0");
			sql.append(" and sm.username = ?");
			Object[] objs = null;
			if (WebUtils.getCurrentCompanyInfoId() != null) {
				sql.append(" and sm.company_info_id = ?");
				objs = new Object[] { storeMember.getUsername(),WebUtils.getCurrentCompanyInfoId() };
			}else {
				sql.append(" and sm.company_info_id is null");
				objs = new Object[] { storeMember.getUsername() };
			}
			Store store = nativeDao.findSingleManaged(sql.toString(),objs,Store.class);
			model.addAttribute("store", store);
			model.addAttribute("saleOrg",store == null ? "" : store.getSaleOrg());
			BankCard bankCard = null;

			if (store != null) {
				filters.clear();
				filters.add(Filter.eq("saleOrg",
				saleOrgService.find(store.getSaleOrg().getId())));
				List<BankCard> bankCards = bankCardService.findList(null,filters,null);
				if (bankCards != null && bankCards.size() > 0) {
					bankCard = bankCards.get(0);
				}
			}
			model.addAttribute("bankCard", bankCard == null ? "" : bankCard);
		}
		
		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);
		
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles); 
		}
		catch (RuntimeException e) {

		}
		
		return "/member/store_recharge/add";
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add_code(@PathVariable String code, Integer flag,
			Integer type, ModelMap model) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("rechargeTypes", rechargeTypes);
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("companyId", WebUtils.getCurrentCompanyInfoId());
		model.addAttribute("nowDate", df.format(new Date()));

		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		else if (storeMember.getMemberType() == 1) {
			NativeDao nativeDao = storeMemberService.getDaoCenter()
					.getNativeDao();
			StringBuilder sql = new StringBuilder();
			sql.append("select s.* from xx_store_member sm, xx_store s");
			sql.append(" where sm.store = s.id");
			sql.append(" and s.is_enabled = 1 and s.is_main_store = 0");
			sql.append(" and sm.username = ?");
			Object[] objs = null;
			if (WebUtils.getCurrentCompanyInfoId() != null) {
				sql.append(" and sm.company_info_id = ?");
				objs = new Object[] { storeMember.getUsername(),
						WebUtils.getCurrentCompanyInfoId() };
			}
			else {
				sql.append(" and sm.company_info_id is null");
				objs = new Object[] { storeMember.getUsername() };
			}

			Store store = nativeDao.findSingleManaged(sql.toString(),
					objs,
					Store.class);

			model.addAttribute("store", store);
			model.addAttribute("saleOrg",
					store == null ? "" : store.getSaleOrg());
		}
		model.addAttribute("code", code);
		return CommonUtil.getFolderPrefix(code) + "/member/store_recharge/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(DepositRecharge depositRecharge, Long saleOrgId,
			Long storeId, Long rechargeTypeId, Long organizationId,
			Long contractId,Long sbuId,Long regionalManagerId) {
		
			
		Store store = storeService.find(storeId);
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		
		if(regionalManagerId!=null){
			StoreMember regionalManager = storeMemberService.find(regionalManagerId);
			depositRecharge.setRegionalManager(regionalManager);
		}
		if (store == null) {
			// 操作错误，客户不存在
			return error("16400");

		}
		if (organizationId != null) {
			Organization organization = organizationService.find(organizationId);
			depositRecharge.setOrganization(organization);
		}
		if(contractId!=null){
			CustomerContract customerContract=customerContractService.find(contractId);
			depositRecharge.setCustomerContract(customerContract);
		}
		Sbu sbu = sbuService.find(sbuId);
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,
				filters,
				null);
		if(sbus.size()==0 || sbus==null){
			return error("该用户没有维护此类型SBU");
		}
		//处理sbu
		if(sbuId==null){
			return error("请选择sbu！");
		}else{
			depositRecharge.setSbu(sbu);
		}
		depositRecharge.setSaleOrg(saleOrgService.find(saleOrgId));
		depositRechargeService.save(store, depositRecharge, 5, rechargeTypeId);
		return success().addObjX(depositRecharge.getId());
	}

	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, Integer flag, Integer type, Long objTypeId,
			ModelMap model) {

		DepositRecharge depositRecharge = depositRechargeService.find(id);
		model.addAttribute("dr", depositRecharge);
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(depositRecharge.getSn(),
				3));
		model.addAttribute("payment_json", payment_json);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		/** 附件 */
		String depositAttach_json = JsonUtils.toJson(depositAttachService.findListByDepositRechargeId(id));
		model.addAttribute("depositAttach_json", depositAttach_json);
		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(depositRecharge.getSn(),
				5));
		model.addAttribute("fullLink_json", fullLink_json);
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> sbus=storeMemberSbuService.findList(null, filters, null);
		model.addAttribute("sbus",
				sbus);

		Map<String, Object> bal = storeBalanceService.findBalanceSbu(depositRecharge.getStore().getId(),depositRecharge.getSbu().getId(),depositRecharge.getOrganization().getId(),depositRecharge.getSaleOrg().getId());
		Integer balance=0;
		if (bal !=null && bal.get("balance") != null) {
			model.addAttribute("balance", bal.get("balance"));
			
		}else{
			model.addAttribute("balance", balance);
		}

		//流程审核相关
		if(depositRecharge.getWfId() != null){
			ActWf wf = depositRechargeService.getWfByWfId(depositRecharge.getWfId());
			if(wf != null){

				//财务放行
				Boolean cw = false;
				Boolean cws = false;

				List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
				for(Map<String, Object> c : item){
					if(c.get("suggestion")!=null){
						String approved = c.get("approved")!=null?c.get("approved").toString():"false";
						String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
						if(rwm.contains("财务")){
							cw = Boolean.valueOf(approved);
						}

					}
				}
				Task t = depositRechargeService.getCurrTaskByWf(wf);
				if(t!=null){
					//获取当前节点所有用户id
					List<String> userId = actWfService.getTaskUsers(t.getId());
					if(userId.contains(storeMember.getId().toString())&&t.getName().contains("财务")){
						cws = true;
					}

				}
				model.addAttribute("wf",wf);
				model.addAttribute("node",t);
				model.addAttribute("cw",cw);
				model.addAttribute("cws",cws);

			}

		}
		
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles); 
		}
		catch (RuntimeException e) {

		}
		//系统调账
		List<Map<String, Object>> findAdjustmentList = adjustmentService.findAdjustmentList(depositRecharge.getId());
		model.addAttribute("adjustmentCount", findAdjustmentList.size()); 
		
		return "/member/store_recharge/view";
	}

	@RequestMapping(value = "/view/{code}", method = RequestMethod.GET)
	public String view_code(@PathVariable String code, Long id, Integer flag,
			Integer type, Long objTypeId, ModelMap model) {

		DepositRecharge depositRecharge = depositRechargeService.find(id);
		model.addAttribute("dr", depositRecharge);
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(depositRecharge.getSn(),
				3));
		model.addAttribute("payment_json", payment_json);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		/** 附件 */
		String depositAttach_json = JsonUtils.toJson(depositAttachService.findListByDepositRechargeId(id));
		model.addAttribute("depositAttach_json", depositAttach_json);
		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(depositRecharge.getSn(),
				5));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("code", code);
		return "/" + code + "/member/store_recharge/view";
	}

	@RequestMapping(value = "/viewSn", method = RequestMethod.GET)
	public String viewSn(String sn, Integer isAdd, Integer type,
			Long objTypeId, ModelMap model) {

		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("sn", sn));
		DepositRecharge depositRecharge = depositRechargeService.find(fis);
		model.addAttribute("dr", depositRecharge);
		model.addAttribute("isAdd", isAdd);
		model.addAttribute("type", type);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(sn,
				3));
		model.addAttribute("payment_json", payment_json);

		/** 附件 */
		String depositAttach_json = JsonUtils.toJson(depositAttachService.findListByDepositRechargeId(depositRecharge.getId()));
		model.addAttribute("depositAttach_json", depositAttach_json);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(depositRecharge.getSn(),
				5));
		model.addAttribute("fullLink_json", fullLink_json);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		return "/member/store_recharge/view";
	}

	@RequestMapping(value = "/viewSn/{code}", method = RequestMethod.GET)
	public String viewSn(@PathVariable String code, String sn, Integer isAdd,
			Integer type, Long objTypeId, ModelMap model) {

		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("sn", sn));
		DepositRecharge depositRecharge = depositRechargeService.find(fis);
		model.addAttribute("dr", depositRecharge);
		model.addAttribute("isAdd", isAdd);
		model.addAttribute("type", type);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(sn,
				3));
		model.addAttribute("payment_json", payment_json);

		/** 附件 */
		String depositAttach_json = JsonUtils.toJson(depositAttachService.findListByDepositRechargeId(depositRecharge.getId()));
		model.addAttribute("depositAttach_json", depositAttach_json);

		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(depositRecharge.getSn(),
				5));
		model.addAttribute("fullLink_json", fullLink_json);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("code", code);
		return CommonUtil.getFolderPrefix(code) + "/member/store_recharge/view";
	}

	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check(Long id, Integer flag, String note,
			BigDecimal actualAmount, ModelMap model) {

		DepositRecharge depositRecharge = depositRechargeService.find(id);
		if (depositRecharge.getDocStatus() != 1) {
			return error("只有单据状态为已提交的申请单才能审批");
		}
		if (actualAmount == null || actualAmount.compareTo(BigDecimal.ZERO) == 0) {
			// 实际充值金额不能为空
			return error("16405");
		}
		BigDecimal amount = depositRecharge.getAmount();
		if (amount.compareTo(BigDecimal.ZERO) == 1) {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == 1) {
				// 实际充值金额不能大于计划充值金额
				return error("16404");
			}
		}else {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == -1
					|| actualAmount.compareTo(BigDecimal.ZERO) == 1) {
				return error("16406");
			}
		}
		depositRechargeService.check(depositRecharge, flag, note, actualAmount);
        return success();
	}



    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg check_wf(Long id, String modelId, Long objTypeId,Integer flag, 
    		String note,BigDecimal actualAmount) {
    	
		DepositRecharge depositRecharge = depositRechargeService.find(id);
		//校验充值单状态
		depositRechargeService.checkDepositRechargeStatus(depositRecharge,0,"已保存","审批流程");
		if (ConvertUtil.isEmpty(actualAmount) || actualAmount.compareTo(BigDecimal.ZERO) == 0) {
			// 实际充值金额不能为空
			return error("16405");
		}
		BigDecimal amount = depositRecharge.getAmount();
		if (amount.compareTo(BigDecimal.ZERO) == 1) {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == 1) {
				// 实际充值金额不能大于计划充值金额
				return error("16404");
			}
		}else {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == -1
					|| actualAmount.compareTo(BigDecimal.ZERO) == 1) {
				return error("16406");
			}
		}
        depositRecharge.setRechargeTypes(0);
        depositRechargeService.createWf(id,modelId,
        		objTypeId,depositRecharge,note,actualAmount);
        return success();
    }


	@RequestMapping(value = "/saveform", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveform(DepositRecharge depositRecharge, Integer Type) {
		// type 用来定义节点
		depositRechargeService.saveform(depositRecharge,Type);
		return success().addObjX(depositRecharge.getId());
	}



	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg cancel(Long id, String note, ModelMap model) {

		DepositRecharge depositRecharge = depositRechargeService.find(id);
		//校验充值单状态
		depositRechargeService.checkDepositRechargeStatus(depositRecharge,0,"已保存","作废");
		depositRechargeService.cancel(depositRecharge, 5);
		return success();
	}

	@RequestMapping(value = "/returnCheck", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg returnCheck(Long id, ModelMap model) {

		DepositRecharge depositRecharge = depositRechargeService.find(id);

		if (depositRecharge.getDocStatus() != 2) {
			return error("单据不是已处理状态！");
		}
		if (depositRecharge.getWfId() != null
				&& depositRecharge.getDocStatus() != 2) {
			return error("流程没完成，不允许确认！");
		}
		depositRecharge.setErpStatus(1);
		depositRechargeService.update(depositRecharge);
		return success();
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/updata", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg updata(DepositRecharge depositRecharge, Long storeId,
			Long saleOrgId, Long rechargeTypeId, Integer isSubmit,
			Long organizationId, Long contractId,Long sbuId,
			Long regionalManagerId) {

		if(!ConvertUtil.isEmpty(regionalManagerId)){
			StoreMember regionalManager = storeMemberService.find(regionalManagerId);
			depositRecharge.setRegionalManager(regionalManager);
		}
		Store store = storeService.find(storeId);
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		if (ConvertUtil.isEmpty(store)) {
			// 操作错误，客户不存在
			return error("16400");
		}
		Organization organization = organizationService.find(organizationId);
		depositRecharge.setOrganization(organization);
		if(!ConvertUtil.isEmpty(contractId)){
			CustomerContract customerContract=customerContractService.find(contractId);
			depositRecharge.setCustomerContract(customerContract);
		}
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		depositRecharge.setSaleOrg(saleOrg);
		Sbu sbu = sbuService.find(sbuId);
		depositRecharge.setSbu(sbu);
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,filters,null);
		if(sbus.size()==0 || sbus==null){
			//return error("该客户没有维护此类型SBU");
		}
		depositRechargeService.update(store, depositRecharge, 5, isSubmit);
		return success().addObjX(depositRecharge.getId());
	}

	/**
	 * 模拟支付
	 */
	@RequestMapping(value = "/submit", method = RequestMethod.POST)
	public String submit(HttpServletRequest request, BigDecimal payamount,
			String payname, HttpServletResponse response, ModelMap model) {

		model.addAttribute("requestUrl", "https://mapi.alipay.com/gateway.do");
		model.addAttribute("requestMethod", "get");
		model.addAttribute("requestCharset", "UTF-8");
		Map<String, Object> data = new HashMap<String, Object>();
		data = getParameterMap(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()),
				request.getParameter("payname"),
				request);
		model.addAttribute("parameterMap", data);
		return "/member/store_recharge/submit";

	}

	public Map<String, Object> getParameterMap(String sn, String description,
			HttpServletRequest request) {

		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("service", "create_direct_pay_by_user");
		parameterMap.put("partner", "2088211535838965");// 支付宝商户号
		parameterMap.put("_input_charset", "utf-8");
		parameterMap.put("sign_type", "MD5");
		parameterMap.put("return_url", "http://mk.5mall.com");// 支付成功同步回调地址
		parameterMap.put("notify_url", "http://mk.5mall.com");// 支付成功异步回调地址
		parameterMap.put("out_trade_no", sn);// 支付单号
		parameterMap.put("subject",
				StringUtils.abbreviate(description.replaceAll("[^0-9a-zA-Z\\u4e00-\\u9fa5 ]",
						""),
						60));
		parameterMap.put("body",
				StringUtils.abbreviate(description.replaceAll("[^0-9a-zA-Z\\u4e00-\\u9fa5 ]",
						""),
						600));
		parameterMap.put("payment_type", "1");
		parameterMap.put("seller_id", "2088211535838965");// 支付宝商户号
		parameterMap.put("total_fee", request.getParameter("payamount"));// 支付金额
		parameterMap.put("show_url", "http://mk.5mall.com");// 网站地址
		parameterMap.put("paymethod", "directPay");
		parameterMap.put("exter_invoke_ip", request.getLocalAddr());// 请求ip
		parameterMap.put("extra_common_param", "twkj remark");// 备注

		try {
			parameterMap.put("sign", generateSign(parameterMap));
		}
		catch (Exception e) {
			System.out.print("---11----" + e.getMessage());
		}

		return parameterMap;
	}

	private String generateSign(Map<String, ?> parameterMap) {

		return DigestUtils.md5Hex(joinKeyValue(new TreeMap<String, Object>(
				parameterMap),
				null,
				"********************************",
				"&",
				true,
				"sign_type",
				"sign"));
	}

	protected String joinKeyValue(Map<String, Object> map, String prefix,
			String suffix, String separator, boolean ignoreEmptyValue,
			String... ignoreKeys) {
		List<String> list = new ArrayList<String>();
		if (map != null) {
			for (Entry<String, Object> entry : map.entrySet()) {
				String key = entry.getKey();
				String value = ConvertUtils.convert(entry.getValue());
				if (StringUtils.isNotEmpty(key)
						&& !ArrayUtils.contains(ignoreKeys, key)
						&& (!ignoreEmptyValue || StringUtils.isNotEmpty(value))) {
					list.add(key + "=" + (value != null ? value : ""));
				}
			}
		}
		return (prefix != null ? prefix : "")
				+ StringUtils.join(list, separator)
				+ (suffix != null ? suffix : "");
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> countDepositRecharge(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice,Long sbuId, Pageable pageable,
			Long[] ids, ModelMap model,String sourceSn,String adjustmentSn,Long[] bankCardId,
			Long[] bcOrganizationId,Long[] bcSbuId,Integer flag) {
		
		if (!Integer.valueOf(1).equals(flag)) {
			//非审核页面进入
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("code", "DepositRechargeType"));
			filters.add(Filter.eq("value", "客户充值"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> rechargeTypes = systemDictService.findList(null,filters,null);
			if (rechargeTypes.size() == 0 || rechargeTypes.size() >1) {
				List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("data", "0-0");
				return lists;
			}
			rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		}
		
		Integer size = depositRechargeService.countDepositRecharge(new Integer[] { 1 },
				sn,null,storeId,null,status,docstatus,rechargeTypeId,creatorId,operatorId,
				minPrice,maxPrice,sbuId,firstTime,lastTime,ids,null,pageable,null,null,
				sourceSn,adjustmentSn,bankCardId,bcOrganizationId,bcSbuId);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView findDepositRechargeList(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice,Long sbuId, Pageable pageable,
			Integer page, ModelMap model,String sourceSn,String adjustmentSn,Long[] bankCardId,
			Long[] bcOrganizationId,Long[] bcSbuId,Integer flag) {
		
		if (!Integer.valueOf(1).equals(flag)) {
			//非审核页面进入
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("code", "DepositRechargeType"));
			filters.add(Filter.eq("value", "客户充值"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> rechargeTypes = systemDictService.findList(null,filters,null);
			if (rechargeTypes.size() == 0 || rechargeTypes.size() >1) {
				return getModelAndViewForList(null, model);
			}
			rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		}
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = depositRechargeService.findDepositRechargeList(new Integer[] { 1 },
				sn,null,storeId,null,status,docstatus,rechargeTypeId,creatorId,operatorId,minPrice,maxPrice,
				sbuId,firstTime,lastTime,null,null,pageable,page,size,sourceSn,adjustmentSn,bankCardId,
				bcOrganizationId,bcSbuId);
		
		return getModelAndViewForList(data, model);
	}

	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView findDepositRechargeList(Long[] ids, ModelMap model) {
		
		List<Map<String, Object>> data = depositRechargeService.findDepositRechargeList(new Integer[] { 1 },
				null,null,null,null,null,null,null,null,null,null,null,null,null,null,ids,null,null,null,null,
				null,null,null,null,null);
				
		return getModelAndViewForList(data, model);
	}

	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("amount") != null) {
				str.put("amount",new BigDecimal(str.get("amount").toString()).setScale(2,BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("actual_amount") != null) {
				str.put("actual_amount", new BigDecimal(str.get("actual_amount").toString()).setScale(2,BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("doc_status") != null) {
				if ((Integer) str.get("doc_status") == 0) {
					str.put("doc_status", "已保存");
				}else if ((Integer) str.get("doc_status") == 1) {
					str.put("doc_status", "	已提交");
				}else if ((Integer) str.get("doc_status") == 2) {
					str.put("doc_status", "已处理");
				}else if ((Integer) str.get("doc_status") == 3) {
					str.put("doc_status", "已作废");
				}else {
					str.put("doc_status", "-");
				}
			}
			if (str.get("wf_state") != null) {
				if ((Integer) str.get("wf_state") == 0) {
					str.put("wf_state", "未启动");
				}else if ((Integer) str.get("wf_state") == 1) {
					str.put("wf_state", "审核中");
				}else if ((Integer) str.get("wf_state") == 2) {
					str.put("wf_state", "已完成");
				}else if ((Integer) str.get("wf_state") == 3) {
					str.put("wf_state", "驳回");
				}else if ((Integer) str.get("wf_state") == 4) {
					str.put("wf_state", "中断");
				}else if ((Integer) str.get("wf_state") == 5) {
					str.put("wf_state", "禁用");
				}else {
					str.put("wf_state", "-");
				}
			}
			if (str.get("create_date") != null) {
				String check_date = str.get("create_date").toString();
				str.put("create_date", check_date.substring(0, 19));
			}
			if (str.get("apply_date") != null) {
				String check_date = str.get("apply_date").toString();
				str.put("apply_date", check_date.substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		// 设置标题
		String[] header = { "充值编号",
				"充值客户",
				"客户简称",
				"客户编码",
				"机构",
				"sbu",
				"经营组织",
				"充值金额",
				"实际充值金额",
				"充值类型",
				"单据状态",
				"流程状态",
				"申请日期",
				"创建日期",
				"GL日期",
				"收款账户",
				"收款银行",
				"收款账号代码",
				"收款账号SBU",
				"收款账号经营组织",
				"调款单号",
				"来源单号",
				"来源账户",
				"来源账号银行",
				"来源账号代码",
				"来源账号SBU",
				"来源账号经营组织",
				"创建人",
				"放行人",
				"申请备注" };

		// 设置单元格取值
		String[] properties = { "sn",
				"store_name",
				"store_alias",
				"out_trade_no",
				"sale_org_name",
				"sbu_name",
				"organization_name",
				"amount",
				"actual_amount",
				"recharge_type_value",
				"doc_status",
				"wf_state",
				"apply_date",
				"create_date",
				"gl_date",
				"bank_card_no",
				"bank_name",
				"bank_code",
				"bc_sbu_name",
				"bc_organization_name",
				"adjustmentSn",
				"sourceSn",
				"source_bank_card_no",
				"source_bank_name",
				"source_bank_code",
				"bcd_sbu_name",
				"bcd_organization_name",
				"creator_name",
				"permit_through_name",
				"memo" };
		// 设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256};

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
}