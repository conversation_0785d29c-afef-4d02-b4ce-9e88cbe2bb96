package net.shopxx.member.controller;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.service.AdjustmentService;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.DepositAttachService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.service.ContractService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

@Controller("distributorRechargeController")
@RequestMapping("/member/distributor_recharge")
public class DistributorRechargeController extends BaseController { 

	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "depositAttachServiceImpl")
	private DepositAttachService depositAttachService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardService;
	@Resource(name = "contractServiceImpl")
	private ContractService contractService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "adjustmentServiceImpl") 
	private AdjustmentService adjustmentService;
	@Resource(name = "roleJurisdictionUtil") 
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer flag,Long objid,Long menuId, ModelMap model) {
        model.addAttribute("flag", flag);
        model.addAttribute("objid", objid);
		model.addAttribute("menuId", menuId);
		return "/member/distributor_recharge/list_tb";
	}
 
	

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list( Long userId, Long menuId, ModelMap model) {
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/distributor_recharge/list";
	}



	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data( Long[] rechargeTypeId,Long[] sbuId,String sn,Integer[] docstatus,
			 Long[] wfStates,Long[] storeId,Long[]  saleOrgId,Long[] bankCardId,
			 Long[] organizationId,String glDateStartTime,String glDateEndTime,
			 Long[] creatorId,Integer flag,String sourceSn,Pageable pageable, ModelMap model) {

		if (!Integer.valueOf(1).equals(flag)) {
			//非审核页面进入
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("code", "DepositRechargeType"));
			filters.add(Filter.eq("value", "客户充值"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> rechargeTypes = systemDictService.findList(null,
					filters,
					null);
			if (rechargeTypes.size() == 0) {
				return this.error("查询失败，充值类型【客户充值】不存在");
			}
			else if (rechargeTypes.size() > 1) {
				return this.error("查询失败，充值类型【客户充值】存在多个");
			}
			rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		}
		
		Page<Map<String, Object>> page = depositRechargeService.newfindDepositRechargePage(new Integer[] { 1 }, 
				rechargeTypeId, sbuId, sn, docstatus, wfStates, storeId, saleOrgId, bankCardId, organizationId,
				glDateStartTime,glDateEndTime, creatorId, sourceSn, null, null, null, pageable);
		
		String jsonPage = JsonUtils.toJson(page);
				
		return success(jsonPage);
	}

	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
	
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {
		for (Map<String, Object> str : data) {
			
			if (!ConvertUtil.isEmpty(str.get("amount"))) {
				str.put("amount",new BigDecimal(str.get("amount").toString()).setScale(2,BigDecimal.ROUND_HALF_UP));
			}
			if (!ConvertUtil.isEmpty(str.get("actual_amount"))) {
				str.put("actual_amount", new BigDecimal(str.get("actual_amount").toString()).setScale(2,BigDecimal.ROUND_HALF_UP));
			}
			if (!ConvertUtil.isEmpty(str.get("erp_status"))) {
				Integer	erpStatus = (Integer) str.get("erp_status");
				if (erpStatus == 0) {
					str.put("erp_status", "已到账");
				
				}else {
					str.put("erp_status", "未到账");
				}
			}
			if (!ConvertUtil.isEmpty(str.get("doc_status"))) {
				Integer	docStatus = (Integer) str.get("doc_status");
				if (docStatus == 0) {
					str.put("doc_status", "已保存");
				}else if (docStatus == 1) {
					str.put("doc_status", "	已提交");
				}else if (docStatus == 2) {
					str.put("doc_status", "已处理");
				}else if (docStatus == 3) {
					str.put("doc_status", "已作废");
				}else {
					str.put("doc_status", "-");
				}
			}
			if (!ConvertUtil.isEmpty(str.get("wf_state"))) {
				Integer	wfState = (Integer)str.get("wf_state");
				if (wfState == 0) {
					str.put("wf_state", "未启动");
				}else if (wfState == 1) {
					str.put("wf_state", "审核中");
				}else if (wfState == 2) {
					str.put("wf_state", "已完成");
				}else if (wfState == 3) {
					str.put("wf_state", "驳回");
				}else {
					str.put("wf_state", "-");
				}
			}
			if (!ConvertUtil.isEmpty(str.get("gl_date"))) {
				String gl_date = str.get("gl_date").toString();
				str.put("gl_date", gl_date.substring(0, 19));
			}
			if (!ConvertUtil.isEmpty(str.get("create_date"))) {
				String check_date = str.get("create_date").toString();
				str.put("create_date", check_date.substring(0, 19));
			}
			if (!ConvertUtil.isEmpty(str.get("apply_date"))) {
				String check_date = str.get("apply_date").toString();
				str.put("apply_date", check_date.substring(0, 19));
			}

		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		
		// 设置标题
		String[] header = { "单据编号","单据状态","流程状态","机构","充值客户","客户简称","客户编码",
				"充值类型","充值金额","实际充值金额","区域经理","收款账户","收款银行","收款账号代码",
				"收款账号SBU","收款账号经营组织","对账月份","申请日期","汇款人","汇款账号",
				"银行水单号","到款状态","GL日期","来源单号","来源账户","来源账号银行","来源账号代码",
				"来源账号SBU","来源账号经营组织","创建人","创建日期","申请备注"};

		// 设置单元格取值
		String[] properties = { "sn","doc_status","wf_state","sale_org_name","store_name","store_alias",
				"outTradeNo","recharge_type_value","amount","actual_amount","regionalManagerName",
				"bank_card_no","bank_name","bank_code","bc_sbu_name","bc_organization_name","balance_month",
				"apply_date","remitter","remittance_account","bank_slip","erp_status","gl_date","sourceSn",
				"source_bank_card_no","source_bank_name","source_bank_code","bcd_sbu_name","bcd_organization_name",
				"creator_name","create_date","memo"};
		
		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256,	25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256};

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}
	
	
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView findDepositRechargeList(Long[] ids, ModelMap model, 
			Pageable pageable) {

		Page<Map<String, Object>> page = depositRechargeService.newfindDepositRechargePage(new Integer[] { 1 }, 
				null, null, null, null, null, null, null, null, null,null, null, null, null, null, null, ids, pageable);
		
		List<Map<String, Object>> data = page.getContent();
		
		return getModelAndViewForList(data, model);
	}

	
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> countDepositRecharge(Long[] rechargeTypeId,Long[] sbuId,String sn,
			Integer[] docstatus,Long[] wfStates,Long[] storeId,Long[]  saleOrgId,Long[] bankCardId,
			Long[] organizationId,String glDateStartTime,String glDateEndTime,Long[] creatorId,
			String sourceSn,Pageable pageable,Integer flag) {
		
		
		if (!Integer.valueOf(1).equals(flag)) {
			//非审核页面进入
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("code", "DepositRechargeType"));
			filters.add(Filter.eq("value", "客户充值"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> rechargeTypes = systemDictService.findList(null,filters,null);
			if (rechargeTypes.size() == 0 || rechargeTypes.size() > 1) {
				List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("data", "0-0");
				return lists;
			}
			rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		}
		
		Page<Map<String, Object>> page = depositRechargeService.newfindDepositRechargePage(new Integer[] { 1 }, 
				rechargeTypeId, sbuId, sn, docstatus, wfStates, storeId, saleOrgId, bankCardId, organizationId, 
				glDateStartTime, glDateEndTime, creatorId, sourceSn, null, null, null, pageable);
		
		Integer size = (int)page.getTotal();
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView findDepositRechargeList(Long[] rechargeTypeId,Long[] sbuId,Integer page,String sn,
			Integer[] docstatus,Long[] wfStates,Long[] storeId,Long[]  saleOrgId,Long[] bankCardId,
			Long[] organizationId,String glDateStartTime,String glDateEndTime,Long[] creatorId,
			String sourceSn,ModelMap model,Pageable pageable,Integer flag) {
		
		if (!Integer.valueOf(1).equals(flag)) {
			//非审核页面进入
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("code", "DepositRechargeType"));
			filters.add(Filter.eq("value", "客户充值"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> rechargeTypes = systemDictService.findList(null,filters,null);
			if (rechargeTypes.size() == 0 || rechargeTypes.size() > 1) {
				return getModelAndViewForList(null, model);
			}
			rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		}
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		
		Page<Map<String, Object>> pageMap = depositRechargeService.newfindDepositRechargePage(new Integer[] { 1 }, 
				rechargeTypeId, sbuId, sn, docstatus, wfStates, storeId, saleOrgId, bankCardId, organizationId, 
				glDateStartTime, glDateEndTime, creatorId, sourceSn, page, size, null, pageable);
		
		List<Map<String, Object>> data = pageMap.getContent();
		
		
		return getModelAndViewForList(data, model);
	}
	
	
	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		
		//设置日期格式
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("nowDate", df.format(new Date()));
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.eq("value", "客户充值"));
		filters.add(Filter.isNotNull("parent"));
		SystemDict rechargeType = systemDictService.find(filters);
		model.addAttribute("rechargeType", rechargeType);
		
		return "/member/distributor_recharge/add";
	}



	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(DepositRecharge depositRecharge, 
			Long saleOrgId,Long storeId, Long rechargeTypeId,Long sbuId,
			Long organizationId,Long bankCardId,Long regionalManagerId) {
		
		//充值客户
		Store store = storeService.find(storeId);
		if (ConvertUtil.isEmpty(store)) {
			return error("操作错误，客户不存在");
		}
		depositRecharge.setStore(store);
		//机构
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		if (ConvertUtil.isEmpty(saleOrg)) {
			return error("操作错误，机构不存在");
		}
		depositRecharge.setSaleOrg(saleOrg);
		//充值类型
		SystemDict rechargeType = systemDictService.find(rechargeTypeId);    
		if (ConvertUtil.isEmpty(rechargeType)) {
				return error("操作错误，充值类型不存在");
		}
		depositRecharge.setRechargeType(rechargeType);
		//收款账户
		BankCard bankCard = bankCardService.find(bankCardId);
		if (ConvertUtil.isEmpty(bankCard)) {
				return error("操作错误，收款账户不存在");
		}
		depositRecharge.setBankCard(bankCard);
		//Sbu
		Sbu sbu = sbuService.find(sbuId);
		if (ConvertUtil.isEmpty(sbu)) {
				return error("操作错误，Sbu不存在");
		 }
		 depositRecharge.setSbu(sbu);
		 //经营组织
		 Organization organization = organizationService.find(organizationId);
		 if (ConvertUtil.isEmpty(organization)) {
				return error("操作错误，经营组织不存在");
		 }
		 depositRecharge.setOrganization(organization);
		 //区域经理
		 StoreMember regionalManager = storeMemberService.find(regionalManagerId);
		 depositRecharge.setRegionalManager(regionalManager);
		
		depositRechargeService.saveDepositRecharge(depositRecharge);
		return success().addObjX(depositRecharge.getId());
	}

	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, Integer flag, Integer type, Long objTypeId,
			ModelMap model) {

		DepositRecharge depositRecharge = depositRechargeService.find(id);
		
		model.addAttribute("dr", depositRecharge);
		model.addAttribute("flag", flag);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		
		/**经销商充值收款账号列表 */
		String distributorRechargeItemList = JsonUtils.toJson(depositRechargeService.findDistributorRechargeItemList(depositRecharge.getId().toString()));
		
		model.addAttribute("distributorRechargeItemList",distributorRechargeItemList);

		/** 附件 */
		String depositAttach_json = JsonUtils.toJson(depositAttachService.findListByDepositRechargeId(id));
		model.addAttribute("depositAttach_json", depositAttach_json);
		
		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(depositRecharge.getSn(),5));
		model.addAttribute("fullLink_json", fullLink_json);
		
		Map<String, Object> bal = storeBalanceService.findBalanceSbu(depositRecharge.getStore().getId(),depositRecharge.getSbu().getId(),depositRecharge.getOrganization().getId(),depositRecharge.getSaleOrg().getId());
		if (!ConvertUtil.isEmpty(bal) && !ConvertUtil.isEmpty(bal.get("balance"))) {
			model.addAttribute("balance", bal.get("balance").toString());
		}else{
			model.addAttribute("balance",0);
		}
		
		//经销商充值保存权限控制
		Integer distributorSaveUserRoles = roleJurisdictionUtil.getRoleCount("productionSchedulingUserRoles");
		model.addAttribute("distributorSaveUserRoles", distributorSaveUserRoles);
		
		//系统调账
		List<Map<String, Object>> findAdjustmentList = adjustmentService.findAdjustmentList(depositRecharge.getId());
		model.addAttribute("adjustmentCount", findAdjustmentList.size()); 
		
		return "/member/distributor_recharge/view";
	}
	
	
	/**
	 * 保存
	 */
	@RequestMapping(value = "/updata", method = RequestMethod.POST)
	public @ResponseBody ResultMsg updata(DepositRecharge depositRecharge, 
			Long saleOrgId,Long storeId, Long rechargeTypeId,Long sbuId,
			Long organizationId,Long bankCardId,Long regionalManagerId) {

		//充值客户
		Store store = storeService.find(storeId);
		if (ConvertUtil.isEmpty(store)) {
			return error("操作错误，客户不存在");
		}
		depositRecharge.setStore(store);
		//机构
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		if (ConvertUtil.isEmpty(saleOrg)) {
			return error("操作错误，机构不存在");
		}
		depositRecharge.setSaleOrg(saleOrg);
		//充值类型
		SystemDict rechargeType = systemDictService.find(rechargeTypeId);    
		if (ConvertUtil.isEmpty(rechargeType)) {
				return error("操作错误，充值类型不存在");
		}
		depositRecharge.setRechargeType(rechargeType);
		//收款账户
		BankCard bankCard = bankCardService.find(bankCardId);
		if (ConvertUtil.isEmpty(bankCard)) {
				return error("操作错误，收款账户不存在");
		}
		depositRecharge.setBankCard(bankCard);
		//Sbu
		Sbu sbu = sbuService.find(sbuId);
		if (ConvertUtil.isEmpty(sbu)) {
				return error("操作错误，Sbu不存在");
		}
		depositRecharge.setSbu(sbu);
		//经营组织
		Organization organization = organizationService.find(organizationId);
		if (ConvertUtil.isEmpty(organization)) {
				return error("操作错误，经营组织不存在");
		}
		depositRecharge.setOrganization(organization);
		//区域经理
		StoreMember regionalManager = storeMemberService.find(regionalManagerId);
		depositRecharge.setRegionalManager(regionalManager);
		
		depositRechargeService.updateDepositRecharge(depositRecharge);
		
		return success().addObjX(depositRecharge.getId());
	}
	
	/**
	 *  经销商充值审批流程更新
	 * @param depositRecharge
	 * @return
	 */
	@RequestMapping(value = "/checkWf_update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkWfUpdate(DepositRecharge depositRecharge) {
		depositRechargeService.checkWfUpdateDepositRecharge(depositRecharge);
		return success().addObjX(depositRecharge.getId());
	}
	
	/**
	 * 经销商审核
	 * @param id
	 * @param flag
	 * @param note
	 * @param actualAmount
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check(Long id, Integer flag, String note,
			BigDecimal actualAmount, ModelMap model) {

		DepositRecharge depositRecharge = depositRechargeService.find(id);
		if (depositRecharge.getDocStatus() != 1) {
			return error("只有单据状态为已提交的申请单才能审批");
		}
		if (actualAmount == null || actualAmount.compareTo(BigDecimal.ZERO) == 0) {
			// 实际充值金额不能为空
			return error("16405");
		}
		BigDecimal amount = depositRecharge.getAmount();
		if (amount.compareTo(BigDecimal.ZERO) == 1) {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == 1) {
				// 实际充值金额不能大于计划充值金额
				return error("16404");
			}
		}else {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == -1 || actualAmount.compareTo(BigDecimal.ZERO) == 1) {
				return error("16406");
			}
		}
		depositRechargeService.checkDepositRecharge(depositRecharge, flag, note, actualAmount);
        return success();
	}
	
	
	
	/**
	 * 经销商充值审批流程
	 * @param id
	 * @param modelId
	 * @param objTypeId
	 * @param flag
	 * @param note
	 * @param actualAmount
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg check_wf(Long id, String modelId, Long objTypeId,Integer flag,
    		String note,BigDecimal actualAmount) {
		
		DepositRecharge depositRecharge = depositRechargeService.find(id);
		//校验充值单状态
		depositRechargeService.checkDepositRechargeStatus(depositRecharge,0,"已保存","审批流程");
		if (ConvertUtil.isEmpty(actualAmount) || actualAmount.compareTo(BigDecimal.ZERO) == 0) {
			// 实际充值金额不能为空
			return error("16405");
		}
		if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) == 1) {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == 1) {
				// 实际充值金额不能大于计划充值金额
				return error("16404");
			}
		}else {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == -1 || actualAmount.compareTo(BigDecimal.ZERO) == 1) {
				return error("16406");
			}
		}
        depositRecharge.setRechargeTypes(1);
        depositRechargeService.createWfDepositRecharge(id,modelId,objTypeId,depositRecharge,note,actualAmount);
        return success();
    }
	
	
}
