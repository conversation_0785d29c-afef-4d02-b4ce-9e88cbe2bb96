package net.shopxx.member.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.member.entity.Post;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.service.PostBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;

/**
 * Controller - 岗位
 */
@Controller("memberPostController")
@RequestMapping("/member/post")
public class PostController extends BaseController {

	@Resource(name = "postBaseServiceImpl")
	private PostBaseService postBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/member/post/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model,userId,menuId);
		return "/member/post/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String name, Pageable pageable, String sn, Long saleOrgId,
			String startTime, String endTime,Long isEnabled, ModelMap model) {

		Page<Map<String, Object>> page = postBaseService.findPage(startTime, endTime, pageable, name, sn, saleOrgId,isEnabled);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		if (companyInfo != null) {
			model.addAttribute("companyInfo", companyInfo);
		}
		return "/member/post/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(Post post, Long saleOrgId) {

		if (post.getName() == null) {
			return error("请填写岗位名称");
		}
		// SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("name", post.getName()));
		// fis.add(Filter.eq("saleOrg", saleOrg));
		List<Post> posts = postBaseService.findList(null, fis, null);
		if (posts != null && posts.size() > 0) {
			return error("该岗位名称已存在，请重新维护");
		}

		// post.setSaleOrg(saleOrg);
		postBaseService.save(post);
		return success().addObjX(post.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {

		model.addAttribute("post", postBaseService.find(id));
		return "/member/post/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(Post post, Long saleOrgId) {

		if (post.getName() == null) {
			return error("请填写岗位名称");
		}
		List<Filter> fis = new ArrayList<Filter>();
//		Post p = postBaseService.find(post.getId());
//		fis.add(Filter.ne("post", p));
//		List<StoreMemberSaleOrg> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findList(null, fis, null);
//		if (storeMemberSaleOrgs != null && storeMemberSaleOrgs.size() > 0) {
//			return error("该岗位所属的机构已存在用户机构关系，不能修改所属机构");
//		}
		fis.clear();
		fis.add(Filter.ne("id", post.getId()));
		fis.add(Filter.eq("name", post.getName()));
		List<Post> posts = postBaseService.findList(null, fis, null);
		if (posts != null && posts.size() > 0) {
			return error("该岗位名称已存在，请重新维护");
		}

		postBaseService.update(post);
		return success();
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_post", method = RequestMethod.GET)
	public String select_menu_role(Long saleOrgId, Integer multi, ModelMap model) {

		model.addAttribute("multi", multi);
		model.addAttribute("saleOrgId", saleOrgId);
		return "/member/post/select_post";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_post_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg select_menu_role_data(String name, Pageable pageable, String sn, Long saleOrgId,
			String startTime, String endTime, ModelMap model) {

		Page<Map<String, Object>> page = postBaseService.findPage(startTime, endTime, pageable, name, sn, saleOrgId,null);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

}