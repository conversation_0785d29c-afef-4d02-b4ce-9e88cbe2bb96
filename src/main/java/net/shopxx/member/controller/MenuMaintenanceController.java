package net.shopxx.member.controller;

import com.alibaba.fastjson.JSONObject;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.member.entity.PcMenu;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.service.PcMenuBaseService;
import net.shopxx.template.entity.DTemplates;
import net.shopxx.template.service.DTemplatesService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller - 菜单维护
 */
@Controller("menuMaintenanceController")
@RequestMapping("/member/menu_maintenance")
public class MenuMaintenanceController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(MenuMaintenanceController.class);

    @Resource(name = "pcMenuBaseServiceImpl")
    private PcMenuBaseService pcMenuBaseService;

    @Resource(name = "dTemplatesServiceImpl")
    private DTemplatesService dTemplatesService;

    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;

    /**
     * 父页面
     * @param model
     * @return
     */
    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(ModelMap model,Long menuId) {
        model.addAttribute("menuId",menuId);
        return "/member/menu_maintenance/list_tb";
    }

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(ModelMap model,Long userId,Long menuId) {
        model.addAttribute("menuId",menuId);
        menuJumpUtils.getModelMap(model, userId, menuId);
        return "/member/menu_maintenance/list";
    }

    @RequestMapping(value = "/meun_popUpList", method = RequestMethod.GET)
    public String meun_popUpList(ModelMap model) {
        return "/member/menu_maintenance/meun_popUpList";
    }

    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        return "/member/menu_maintenance/add";
    }

    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id,ModelMap model) {
        //获取菜单信息
        PcMenu pcMenu = pcMenuBaseService.find(id);
        model.addAttribute("pcMenu",pcMenu);
        //获取模板信息
        DTemplates dTemplates = new DTemplates();
        if (!ConvertUtil.isEmpty(pcMenu.getdTemplates())){
            dTemplates = dTemplatesService.find(pcMenu.getdTemplates());
        }
        model.addAttribute("dTemplates",dTemplates);
        //子菜单
        List<Map<String,Object>> menuList = new ArrayList<Map<String, Object>>();
        if (pcMenu.getChildren()!=null&&pcMenu.getChildren().size()>0){
            for (PcMenu pm:pcMenu.getChildren()){
                Map<String,Object> map = new HashMap<String, Object>();
                map.put("id",pm.getId());
                map.put("menuName",pm.getMenuName());
                map.put("menuCode",pm.getMenuCode());
                map.put("createDate",pm.getCreateDate());
                map.put("url",pm.getUrl());
                menuList.add(map);
            }
        }
        model.addAttribute("menuList",JsonUtils.toJson(menuList));
        //角色
        List<Map<String,Object>> pcRoleList = new ArrayList<Map<String, Object>>();
        List<PcRole> prList = new ArrayList<PcRole>(pcMenu.getPcRoles());
        if (prList!=null&&prList.size()>0){
            for (PcRole pr:prList){
                Map<String,Object> map = new HashMap<String, Object>();
                map.put("id",pr.getId());
                map.put("name",pr.getName());
                map.put("description",pr.getDescription());
                map.put("isSystem",pr.getIsSystem());   //是否内置
                pcRoleList.add(map);
            }
        }
        model.addAttribute("pcRoleList",JsonUtils.toJson(pcRoleList));
        return "/member/menu_maintenance/edit";
    }


    /**
     * 列表数据
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_data(String jsonParam, Pageable pageable){
        jsonParam = StringEscapeUtils.unescapeHtml(jsonParam);
        //解析参数
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        //获取参数
        Integer type = jsonObject.getInteger("type");
        Long superId = jsonObject.getLong("superId");
        String menuName = jsonObject.getString("menuName");
        Boolean isEnabled = jsonObject.getBoolean("isEnabled");
        int isSuper = jsonObject.getInteger("isSuper");
        String createDate = jsonObject.getString("createDate");
        Page<Map<String, Object>> page = pcMenuBaseService.findMenuMaintenanceList(type,isSuper, superId, menuName, isEnabled,createDate, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

    /**
     * 保存
     * @param pcMenu
     * @param parentdmenuId 父菜单
     * @return
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ResponseBody ResultMsg save(PcMenu pcMenu, Long parentdmenuId) {
        Long menuId = pcMenuBaseService.saveMenu(pcMenu, parentdmenuId);
        return success().addObjX(menuId);
    }

    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public @ResponseBody ResultMsg update(PcMenu pcMenu,Long parentdmenuId) {

        if(ConvertUtil.isEmpty(pcMenu.getId())){
            return error("模块ID不能为空");
        }

        if(!ConvertUtil.isEmpty(parentdmenuId)){
            PcMenu parentPcMenu =  pcMenuBaseService.find(parentdmenuId);
            if(!ConvertUtil.isEmpty(parentPcMenu)){
                if(pcMenu.getMenuName().equals(parentPcMenu.getMenuName())){
                    return error("父菜单不能与菜单名称一致");
                }
                pcMenu.setSuperId(parentPcMenu);
            }
        }else{
            pcMenu.setSuperId(null);
        }
        if (!ConvertUtil.isEmpty(pcMenu.getNumber())){
            pcMenu.setOrder(pcMenu.getNumber());
        }
        pcMenuBaseService.update(pcMenu);
        return success().addObjX(pcMenu.getId());
    }

    /**
     * 删除菜单
     * @param menuId 菜单id
     * @return
     */
    @RequestMapping(value = "/deleteMenu", method = RequestMethod.GET)
    public @ResponseBody ResultMsg deleteMenu(Long menuId) {
        pcMenuBaseService.deleteMenu(menuId,null);
        return success();
    }


    /**
     * 删除角色
     * @param menuId
     * @return roleId
     */
    @RequestMapping(value = "/deleteRole", method = RequestMethod.GET)
    public @ResponseBody ResultMsg deleteRole(Long menuId,Long roleId) {
        pcMenuBaseService.deleteRole(menuId, roleId);
        return success();
    }

}
