package net.shopxx.member.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreExit;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreExitService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.util.CommonUtil;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 客户退出
 */
@Controller("storeExitController")
@RequestMapping("/member/store_exit")
public class StoreExitController extends BaseController {


    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeExitServiceImpl")
    private StoreExitService storeExitService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;

    /*
     * 列表
     */
    @RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
    public String list_tb(@PathVariable String code, Integer isCheck,
            Long objTypeId, Long objid, Long sid, Integer type, ModelMap model) {
        model.addAttribute("isCheck", isCheck);
        model.addAttribute("objTypeId", objTypeId);
        model.addAttribute("objid", objid);
        model.addAttribute("code", code);
        model.addAttribute("sid", sid);
        model.addAttribute("type", type);
        return CommonUtil.getFolderPrefix(code) + "/member/store_exit/list_tb";
    }
    
    /*
     * 添加
     */
    @RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
    public String add(@PathVariable String code, ModelMap model, Long sid, 
            Integer type, Pageable pageable) {
        model.addAttribute("code", code);
        model.addAttribute("type", type);
        SimpleDateFormat formatter =  new SimpleDateFormat( "yyyyMMdd");
        Store store = storeBaseService.find(sid);
        String startTime = null;
        if (store != null) {
            model.addAttribute("store", store);
        }
        // 门店数量 
        Integer ShopInfoCount = shopInfoService.countShopInfo(sid);
        model.addAttribute("shopInfoCount", ShopInfoCount);
        if(store.getActiveDate()!=null){
        	startTime = formatter.format(store.getActiveDate());            	
        }
    	String endTime = formatter.format(new Date());
    	Integer dateLength = 0;
    	if(!"".equals(startTime)&&startTime != null){
    		dateLength = getDateLength( startTime, endTime)<0?0:getDateLength( startTime, endTime);    		
    	}
        model.addAttribute("dateLength", dateLength);
		model.addAttribute("shopInfos", JsonUtils.toJson(shopInfoService.findStoreByShopInfo(sid)));

        //如果客户没有提交过单据，跳转到add页面，否则转到edit页面
//        List<Filter> filterss = new ArrayList<Filter>();
//        filterss.add(Filter.eq("store",store));
//        StoreExit storeExit = storeExitService.find(filterss);
//        if(storeExit == null){
//            return CommonUtil.getFolderPrefix(code) + "/member/store_exit/add";
//        }else {
//            model.addAttribute("storeExit",storeExit);
//            //return CommonUtil.getFolderPrefix(code) + "/member/store_exit/edit";
//            return this.edit(code,storeExit.getId(),type,model);
//
//        }

        return CommonUtil.getFolderPrefix(code) + "/member/store_exit/add";

    }
    
    /** 查询日期间隔方法 */
    public Integer getDateLength(String fromDate, String toDate){
    	Calendar c1 = getCal(fromDate);
    	Calendar c2 = getCal(toDate);
    	int[] p1 = { c1.get(Calendar.YEAR), c1.get(Calendar.MONTH), c1.get(Calendar.DAY_OF_MONTH) };
    	int[] p2 = { c2.get(Calendar.YEAR), c2.get(Calendar.MONTH), c2.get(Calendar.DAY_OF_MONTH) };
    	int[] ret = new int[] { p2[0] - p1[0] };
    	Integer day = (Integer)ret[0];
    	return day;
    }
    public Calendar getCal(String date) {
    	Calendar cal = Calendar.getInstance();
    	cal.clear();
    	cal.set(Integer.parseInt(date.substring(0, 4)), Integer.parseInt(date.substring(4, 6)) - 1, Integer.parseInt(date.substring(6, 8)));
    	return cal;
    }
    
    /*
     * 列表
     */
    @RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
    public String list(@PathVariable String code, Integer isCheck,
            Pageable pageable, ModelMap model) {
        model.addAttribute("code", code);
        model.addAttribute("isCheck", isCheck);
        
        return CommonUtil.getFolderPrefix(code) + "/member/store_exit/list";
    }
    
    /*
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(StoreExit se, Long storeId, String isCoreStroe,
                               String saleOrg, String areaManager, String firstTimee, String lastTimee,
                               String firstTime, String lastTime, String createName, Pageable pageable,
                               ModelMap model) {
        List<Object> params = new ArrayList<Object>();
        params.add(storeId);
        params.add(isCoreStroe);
        params.add(saleOrg);
        params.add(areaManager);
        params.add(firstTimee);
        params.add(lastTimee);
        params.add(firstTime);
        params.add(lastTime);
        params.add(createName);
        Page<Map<String, Object>> page = storeExitService.findPage(se,params, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }
    
    /*
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMsg save(StoreExit storeExit, Long sid, Integer[] dealerContacts) {
            if (storeExit.getType() == null) {
                return error("类型不明确！");
            }
            if (storeExit.getExitCause() == 3&&StringUtils.isEmpty(storeExit.getExitCauseOther())){
            	return error("请填写退出原因中的其他原因！");
            }
            // 经销商无法联系
            if (dealerContacts != null && dealerContacts.length > 0) {
                String dealerContact = StringUtils.arrayToDelimitedString(dealerContacts, ",");
                storeExit.setDealerContact(dealerContact);
            }
            if(storeExit.getStoreExitAttachs()==null||storeExit.getStoreExitAttachs().size()<=0){            	
            	return error("请上传退出申请签字版！");
            }
            if(storeExit.getBankArea()==null||storeExit.getBankArea().getId()==null){
            	storeExit.setBankArea(null);
            }
            //执行添加操作
            storeExit.setStatus(0);
            storeExitService.saveStoreExit(storeExit, sid);
            return success().addObjX(storeExit.getId());
    }


    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg update(StoreExit storeExit , Long sid, Integer[] dealerContacts) {
    	if(storeExit.getStoreExitAttachs()==null||storeExit.getStoreExitAttachs().size()<=0){            	
        	return error("请上传退出申请签字版！");
        }
    	if (storeExit.getExitCause() == 3&&StringUtils.isEmpty(storeExit.getExitCauseOther())){
        	return error("请填写退出原因中的其他原因！");
        }
    	if(storeExit.getBankArea()==null||storeExit.getBankArea().getId()==null){
        	storeExit.setBankArea(null);
        }
//        if(storeExit.getStoreReceiptAttachs() == null||storeExit.getStoreReceiptAttachs().size()<=0){
//        	return error("请上传品牌保证金收据！");
//        }
        storeExitService.updateStoreExit(storeExit, sid,dealerContacts);
        return success().addObjX(storeExit.getId());
    }

    /**
     * 客户退出详情页
     * @param code b
     * @param id 单据id
     * @param type 单点默认为空null
     * @return
     */
    @RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
    public String edit(@PathVariable String code,Long id, Integer type,ModelMap model) {
        StoreExit storeExit = storeExitService.find(id);
        model.addAttribute("type", type);
        model.addAttribute("storeExit", storeExit);
        // 门店数量
        Integer ShopInfoCount = shopInfoService.countShopInfo(storeExit.getStore().getId());
        model.addAttribute("shopInfoCount", ShopInfoCount);

        //审核流程
        StoreMember storeMember = storeMemberBaseService.getCurrent();

        if(storeExit.getWfId()!= null){
            ActWf wf = storeExitService.getWfByWfId(storeExit.getWfId());

            if (wf != null) {
                //省长审核
                Boolean szsh = false;
                //渠道专员
                Boolean qdzy = false;
                //省长审核权限
                Boolean szshs = false;
                //渠道专员权限
                Boolean qdzys = false;
                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for(Map<String, Object> c : item){
                    if(c.get("suggestion")!=null){
                        //处理结果
                        String approved = c.get("approved")!=null?c.get("approved").toString():"false";
                        //节点名称
                        String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
                        //对比节点名称是否对应
                        if(rwm.contains("省长")){
                            szsh = Boolean.valueOf(approved);
                        }
                        if(rwm.contains("渠道")){
                            qdzy = Boolean.valueOf(approved);
                            if(!qdzy){
                                szsh = false;
                            }
                        }
                    }
                }
                //获取当前流程所在的节点
                Task t = storeExitService.getCurrTaskByWf(wf);
                if(t!=null){
                    //获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
                        szshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道")){
                        szshs = true;qdzys = true;
                    }
                }
                model.addAttribute("node", t);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzys", qdzys);
            }
        }
        model.addAttribute("store_exit_attach", JsonUtils.toJson(storeExitService.findStoreExitAttach(storeExit.getId(), 1)));
        model.addAttribute("store_receipt_attach", JsonUtils.toJson(storeExitService.findStoreExitAttach(storeExit.getId(), 2)));
        model.addAttribute("shopInfos",JsonUtils.toJson(storeExitService.findStoreExitShopInfo(id)));
        return CommonUtil.getFolderPrefix(code) + "/member/store_exit/edit";
    }

    /**
     * 流程节点保存
     */
    @RequestMapping(value = "/saveform", method = RequestMethod.POST)
    public @ResponseBody ResultMsg saveform(StoreExit storeExit, Integer type) {
        // type 用来定义节点
        //type 1区域经理 2省长 3财务部
        storeExitService.saveform(type,storeExit);
        return success().addObjX(storeExit.getId());
    }



    /**
     * 审核
     * @param id shopId
     * @return
     */
    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public @ResponseBody ResultMsg check_wf(Long id,String modelId,Long objTypeId) {
        if (id == null) {
            // 请选择订单
            return error("请选择单据");
        }
        StoreExit storeExit = storeExitService.find(id);
        storeExitService.createWf(id, modelId, objTypeId);
        return success().addObjX(storeExit.getId());
    }
    
    @RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(StoreExit se, Long storeId, String isCoreStroe,
                                        String saleOrg, String areaManager, String firstTimee, String lastTimee,
                                        String firstTime, String lastTime, String createName, String[] header, String[] properties,
                                        Pageable pageable, ModelMap model, Integer page){
    	List<Object> params = new ArrayList<Object>();
    	params.add(storeId);
        params.add(isCoreStroe);
        params.add(saleOrg);
        params.add(areaManager);
        params.add(firstTimee);
        params.add(lastTimee);
        params.add(firstTime);
        params.add(lastTime);
        params.add(createName);
    	Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = storeExitService.findList(se, params, pageable, page, size);
    	return getModelAndViewForList(data,header,properties, model);
    }
    
    /**
	 * 条件导出
	 * 
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(StoreExit se, Long storeId, String isCoreStroe,
                                                                     String saleOrg, String areaManager, String firstTimee, String lastTimee,
                                                                     String firstTime, String lastTime, String createName){
		List<Object> params = new ArrayList<Object>();
		params.add(storeId);
        params.add(isCoreStroe);
        params.add(saleOrg);
        params.add(areaManager);
        params.add(firstTimee);
        params.add(lastTimee);
        params.add(firstTime);
        params.add(lastTime);
        params.add(createName);
		int size = storeExitService.count(se,params);
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,String[] header,String[] properties, ModelMap model) {
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置列宽
		Integer[] widths = {};
		if(header==null){
			header = new String[]{};
		}
		
		if(properties==null){
			properties = new String[]{};
		}else{
			List<Integer> w = new ArrayList<Integer>();
			for(int i=0;i<properties.length;i++){
				w.add(25 * 256);
			}
			widths = w.toArray(new Integer[]{w.size()});
		}
		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}
    
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
}
