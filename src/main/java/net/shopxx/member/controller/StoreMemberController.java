package net.shopxx.member.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.*;
import net.shopxx.member.entity.*;
import net.shopxx.member.service.*;
import net.shopxx.order.entity.Contract;
import net.shopxx.order.service.ContractService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.stock.entity.WarehouseStore;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.MemberRank;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.entity.StoreMember.Gender;
import net.shopxx.stock.entity.Warehouse;

/**
 * 用户
 */
@Controller("memberStoreMemberController")
@RequestMapping("/member/store_member")
public class StoreMemberController extends BaseController {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "memberBaseServiceImpl")
	private MemberBaseService memberBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "partnerCategoryBaseServiceImpl")
	private PartnerCategoryBaseService partnerCategoryBaseService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "pcRoleBaseServiceImpl")
	private PcRoleBaseService pcRoleBaseService;
	@Resource(name = "smWarehouseBaseServiceImpl")
	private SmWarehouseBaseService smWarehouseBaseService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "postBaseServiceImpl")
	private PostBaseService postBaseService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "storeInvoiceInfoServiceImpl")
	private StoreInvoiceInfoService storeInvoiceInfoService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardBaseService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreBaseService;
	@Resource(name = "visualReportServiceImpl")
	private VisualReportService visualReportService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "contractServiceImpl")
	private ContractService contractService;
    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;
	

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {

		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, fis, null));
        model.addAttribute("menuId",menuId);
        //获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/store_member/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_out", method = RequestMethod.GET)
	public String list_out(Pageable pageable, ModelMap model,Long userId,Long menuId) {

		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, fis, null));
        model.addAttribute("menuId",menuId);
        //获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/store_member/list_out";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable,Long menuId, ModelMap model) {
        model.addAttribute("menuId",menuId);
		return "/member/store_member/list_tb";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String username, String mobile, String name,
			Long memberRankId, Boolean isPartner, Long storeId, Long saleOrgId,
			String startTime, String endTime, Pageable pageable, ModelMap model) {

		Object[] args = new Object[] { username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				0 };
		Page<Map<String, Object>> page = storeMemberBaseService.findPage(pageable,
				args);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_out_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_out_data(String username, String mobile, String name,
			Long memberRankId, Boolean isPartner, Long storeId, Long saleOrgId,
			String startTime, String endTime, Pageable pageable, ModelMap model) {

		Object[] args = new Object[] { username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				1 };
		Page<Map<String, Object>> page = storeMemberBaseService.findPage(pageable,
				args);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Integer memberType, ModelMap model) {

		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		model.addAttribute("memberType", memberType);
		List<Filter> filters = new ArrayList<Filter>();
		
		StoreMember storeMember = storeMemberBaseService.getCurrent();
	

	    filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		
		List<PartnerCategory> partnerCategories = partnerCategoryBaseService.findList(null,
				filters,
				null);
		model.addAttribute("pc_lists", partnerCategories);

		filters.clear();
		filters.add(Filter.eq("isTop", true));
		List<SaleOrg> saleOrgs = saleOrgBaseService.findList(1, filters, null);
		if (saleOrgs.size() > 0) {
			model.addAttribute("saleOrg", saleOrgs.get(0));
		}

		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, filters, null));
		model.addAttribute("genders", Gender.values());
		model.addAttribute("companyInfo", companyInfo);
		if(storeMember.getMemberType()==0){
			List<PcRole> roles = pcRoleBaseService.findAll();
			model.addAttribute("roles", roles);
		}else{
			List<PcRole> roles = pcRoleBaseService.findByStoreMemberId(storeMember.getId());
			model.addAttribute("roles", roles);
		}
		
		model.addAttribute("storeTypes", storeBaseService.getTypes());// 客户类型

		model.addAttribute("isAdmin", WebUtils.isAdmin());

		int undefinedProduct2Order = 0;// 未定义产品是否支持下单
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		return "/member/store_member/add";
	}


	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(StoreMember storeMember, Member member, Long memberRankId,
				   Long[] storeId, Integer[] isDefault, Long pc_id, Long parentId,
				   Long[] roleId, Long[] saleOrgId, Long[] warehouseId,Long[] organizationId,Integer[] isDeFaults,
				   String[] postId,String[] postIds, Boolean isAdministrator, Long[] sbuId,
				   Integer[] isDefaults,Long[] shopId, HttpServletRequest request, ModelMap model) {

		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		if (companyInfo == null) {
			// 您没有权限添加用户
			return error("1601003");
		}
		if (ConvertUtil.isEmpty(member.getUsername())
				|| ConvertUtil.isEmpty(member.getPassword())
				|| ConvertUtil.isEmpty(member.getMobile())) {
			// 用户名、密码或手机号未填
			return error("1601001");
		}
		if (member.getUsername().indexOf("admin") != -1) {
			// 非法的用户名
			return error("1601011");
		}
		if (storeMember.getMemberType() != 1
				&& (saleOrgId == null || saleOrgId.length < 1)) {
			return error("请维护所属机构");
		}
		Setting setting = SettingUtils.get();
		if (member.getUsername().length() < setting.getUsernameMinLength()
				|| member.getUsername().length() > setting.getUsernameMaxLength()) {
			// 用户名长度范围有误
			return error("1601004");
		}
		if (member.getPassword().length() < setting.getPasswordMinLength()
				|| member.getPassword().length() > setting.getPasswordMaxLength()) {
			// 密码长度范围有误
			return error("1601005");
		}
		// if (memberBaseService.mobileExists(member.getMobile(), null)) {
		// //手机号已存在
		// return error("1601006");
		// }
		Member m = memberBaseService.findByMobile(member.getMobile());
		if (m != null) {
			if (storeMemberBaseService.storeMemberExists(m.getId(), 0L)) {
				// 手机号已存在
				return error("1601006");
			}
			member = m;
		}
		if(storeMember.getEmailAddress()!=null){
			String regEx ="^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
			Pattern p = Pattern.compile(regEx);
			Matcher mr = p.matcher(storeMember.getEmailAddress());
			if(!mr.matches()){
				return error("邮箱格式有误");
			}

		}
		// MemberRank memberRank = memberRankBaseService.find(memberRankId);
		// if (memberRank == null) {
		// //请选择会员等级
		// return error("1601007");
		// }
		MemberRank memberRank = memberRankBaseService.findDefault(companyInfo.getId());

		PartnerCategory partnerCategory = null;
		if (storeMember.getIsPartner() != null && storeMember.getIsPartner()) {
			if (pc_id == null) {
				// 合伙人必须选择合伙人类型
				return error("1601008");
			}
			partnerCategory = partnerCategoryBaseService.find(pc_id);
		}
		StoreMember recommend = storeMemberBaseService.find(parentId);

		storeMember.setMemberRank(memberRank);
		storeMember.setPartnerCategory(partnerCategory);
		storeMember.setRecommendStoreMember(recommend);
		if(storeMember.getIsActiveAdministrator() == null ){
			storeMember.setIsActiveAdministrator(false);
		}

		// 保存用户
		long storeMemberId = storeMemberBaseService.saveStoreMember(storeMember,
				member,
				storeId,
				roleId,
				saleOrgId,
				postId,
				isDefault,
				warehouseId,
				sbuId,
				isDefaults,
				isAdministrator,
				shopId,
				postIds,
				request,
				organizationId,
				isDeFaults);

		return success().addObjX(storeMemberId);
	}


	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
	public String edit(@PathVariable String code, Long id, Integer isEdit,
					   Integer isStore, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isTop", true));
		List<SaleOrg> saleOrgs = saleOrgBaseService.findList(1, filters, null);
		if (saleOrgs.size() > 0) {
			model.addAttribute("saleOrg", saleOrgs.get(0));
		}
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, filters, null));

		Store store = storeBaseService.find(id);
		model.addAttribute("store", store);

		//门店数量
		Integer ShopInfoCount = shopInfoService.countShopInfo(id);
		model.addAttribute("ShopInfoCount", ShopInfoCount);

		filters.clear();
		filters.add(Filter.eq("store", store));
		BankCard bankCard = bankCardBaseService.find(filters) == null ? new BankCard()
				: bankCardBaseService.find(filters);
		model.addAttribute("bankCard", bankCard);

		filters.clear();
		filters.add(Filter.eq("store", store));
		List<WarehouseStore> warehouseStores = warehouseStoreBaseService.findList(null,
				filters,
				null);
		List<Warehouse> warehouses = new ArrayList<Warehouse>();
		for (WarehouseStore warehouseStore : warehouseStores) {
			warehouses.add(warehouseStore.getWarehouse());
		}

		//平台性质
		filters.clear();
		filters.add(Filter.eq("code", "SaleOrgType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgTypes", saleOrgTypes);

		//客户类型
		filters.clear();
		filters.add(Filter.eq("code", "CustomerType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> customerTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("customerTypes", customerTypes);

		//经销商属性
		filters.clear();
		filters.add(Filter.eq("code", "agentProperty"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> agentProperties = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("agentProperties", agentProperties);

		//等级
		filters.clear();
		filters.add(Filter.eq("code", "Grade"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> grades = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("grades", grades);

		filters.clear();
		filters.add(Filter.eq("store", store));
		//		Pageable pageable = new Pageable();
		//		Page<Map<String, Object>> storeAddressList = storeBaseService
		//				.findStoreAddressPage(null, null, id, null, pageable);
		//		model.addAttribute("storeAddressList",
		//				JsonUtils.toJson(storeAddressList.getContent()));
		//		model.addAttribute("storeAddressSize",
		//				storeAddressList.getContent() == null ? "0"
		//						: storeAddressList.getContent().size());

		List<Map<String, Object>> storeAddressList = storeBaseService.findStoreAddressList(id);
		model.addAttribute("storeAddressList",
				JsonUtils.toJson(storeAddressList));
		model.addAttribute("storeAddressSize", storeAddressList.size());

		List<Map<String, Object>> storeInvoiceInfoList = storeInvoiceInfoService.findListByStore(store.getId(),null);
		model.addAttribute("storeInvoiceInfoList",
				JsonUtils.toJson(storeInvoiceInfoList));

		model.addAttribute("warehouses", warehouses);
		model.addAttribute("isEdit", isEdit);
		model.addAttribute("isStore", isStore);

		List<Integer> types = storeBaseService.getTypes();
		model.addAttribute("types", types);

		if (store != null) {
			model.addAttribute("storeManagerList",
					JsonUtils.toJson(store.getStoreManagers()));
			model.addAttribute("storeManagerSize",
					store.getStoreManagers() == null ? "0"
							: store.getStoreManagers().size());
		}
		model.addAttribute("companyInfoId", WebUtils.getCurrentCompanyInfoId());

		// 获取经营品类
		List<Map<String, Object>> productType = storeBaseService.findBusinessCategory(store);
		model.addAttribute("productType", JsonUtils.toJson(productType));

		// 获取经营记录
		List<Map<String, Object>> businessRecord = storeBaseService.findBusinessRecord(store);
		model.addAttribute("businessRecord", JsonUtils.toJson(businessRecord));

		// 获取保证金
		List<Map<String, Object>> cautionMoney = storeBaseService.findCautionMoney(store);
		model.addAttribute("cautionMoney", JsonUtils.toJson(cautionMoney));

		// 获取保证金
		List<Map<String, Object>> storeMembers = storeMemberBaseService.findStoreMemberByMember(store.getId());
		model.addAttribute("storeMembers", JsonUtils.toJson(storeMembers));

		// 获取门店
		List<Map<String, Object>> shopInfos = shopInfoService.findListByStoreId(id);
		model.addAttribute("shopInfos", JsonUtils.toJson(shopInfos));

		// 获取联系人
		List<Map<String, Object>> storecontracts = storeBaseService.findStoreContract(store);
		model.addAttribute("storecontracts", JsonUtils.toJson(storecontracts));

		List<Map<String, Object>> storeCooperations = storeBaseService.findStoreCooperation(store);
		model.addAttribute("storeCooperations",
				JsonUtils.toJson(storeCooperations));

		// 业务类型
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("businessTypes", businessTypes);

		// 经销商状态
		filters.clear();
		filters.add(Filter.eq("code", "distributorStatus"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> distributorStatus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("distributorStatus", distributorStatus);

		filters.clear();
		filters.add(Filter.eq("code", "sbu"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> sbus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("sbus", sbus);

		//销售体系
		filters.clear();
		filters.add(Filter.eq("code", "SaleStyle"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleStyle = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleStyles", saleStyle);
		model.addAttribute("code", code);
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		model.addAttribute("nowDate", df.format(new Date()));

		//订单附件
		String StoreAttach_json = JsonUtils.toJson(storeBaseService.findListByStoreId(store.getId()));
		model.addAttribute("StoreAttach_json", StoreAttach_json);

		//sbu
		String sbuList = JsonUtils.toJson(storeMemberBaseService.findSbu(id));
		model.addAttribute("sbu_json", sbuList);
		filters.clear();
		filters.add(Filter.eq("store", store));
		filters.add(Filter.eq("companyInfoId",
				WebUtils.getCurrentCompanyInfoId()));
		List<Contract> contractList = contractService.findList(null,
				filters,
				null);
		model.addAttribute("contractList", contractList);
		//客户附件
		List<Map<String, Object>> storeAttachs = storeBaseService.findStoreAttach(id, 0);
		model.addAttribute("store_attach0", JsonUtils.toJson(storeAttachs));
		List<Map<String, Object>> storeAttachs1 = storeBaseService.findStoreAttach(id, 1);
		model.addAttribute("store_attach1", JsonUtils.toJson(storeAttachs1));
		List<Map<String, Object>> storeAttachs2 = storeBaseService.findStoreAttach(id, 2);
		model.addAttribute("store_attach2", JsonUtils.toJson(storeAttachs2));

		model.addAttribute("visualReport",
				visualReportService.findVisualReports(storeMemberBaseService.getCurrent().getId(),net.shopxx.member.entity.VisualReport.Type.store));

		String storeFullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByElseSnAndType(store.getSn(), 103));
		model.addAttribute("storeFullLink_json", storeFullLink_json);

		//机构区域
		filters.clear();
		filters.add(Filter.eq("code", "saleOrgRegion"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgRegions", saleOrgRegions);

		return CommonUtil.getFolderPrefix(code) + "/member/store/edit";
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, Integer flag, Integer memberType, ModelMap model) {

		StoreMember storeMember = storeMemberBaseService.find(id);
		model.addAttribute("store_member", storeMember);
		model.addAttribute("memberType", memberType);
		
		StoreMember loginMember=storeMemberBaseService.getCurrent();
		//sbu
		String sbuList = JsonUtils.toJson(storeMemberBaseService.findSbu(id));
		model.addAttribute("sbu_json", sbuList);
		
		//经营组织
		String organizationList = JsonUtils.toJson(storeMemberBaseService.findOrganization(id));
		model.addAttribute("organization_json", organizationList);
		
		//门店
		List<Map<String, Object>> shopLists=storeMemberBaseService.findShop(id);
		String idPost ="";
		for(int i=0; i<shopLists.size();i++){
			Map<String,Object> shopMap = shopLists.get(i);
			if(i==shopLists.size()-1){
				idPost+=shopMap.get("id");
			}else{
				idPost+=shopMap.get("id")+",";
			}
		}
		List<Map<String, Object>> shopPuts = storeMemberBaseService.findListByShop(id,
				idPost);
		List<Map<String, Object>> postIds = null;
		for (Map<String, Object> shopList : shopLists) {
			postIds = new ArrayList<Map<String, Object>>();
			String procTempId = shopList.get("id").toString();
			for (Map<String, Object> itemMap : shopPuts) {
				String pid = itemMap.get("shopInfo").toString();
				if (procTempId.equals(pid)) {
					postIds.add(itemMap);
				}
			}
			shopList.put("posts", postIds);
		}
		
		String shop_json = JsonUtils.toJson(shopLists);
		model.addAttribute("shop_json", shop_json);
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		List<PartnerCategory> partnerCategories = partnerCategoryBaseService.findList(null,
				filters,
				null);
		model.addAttribute("pc_lists", partnerCategories);

		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, filters, null));
		model.addAttribute("genders", Gender.values());

		model.addAttribute("isAdmin", WebUtils.isAdmin());

		model.addAttribute("stores_json",
				JsonUtils.toJson(storeBaseService.findListByMember(storeMember.getMember()
						.getId())));

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		List<PcRole> uRoles = new ArrayList<PcRole>();
		for (PcUserRole userRole : userRoles) {
			uRoles.add(userRole.getPcRole());
		}
		model.addAttribute("uRoles", uRoles);

		if(loginMember.getMemberType()==0){
			List<PcRole> roles = pcRoleBaseService.findAll();
			model.addAttribute("roles", roles);
		}else{
			List<PcRole> roles = pcRoleBaseService.findByStoreMemberId(loginMember.getId());
			model.addAttribute("roles", roles);
		}

		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		model.addAttribute("companyInfo", companyInfo);

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<SmWarehouse> smWarehouses = smWarehouseBaseService.findList(null,
				filters,
				null);
		// List<Warehouse> warehouses = new ArrayList<Warehouse>();
		List<Map<String, Object>> warehouses = new ArrayList<Map<String, Object>>();
		for (SmWarehouse smWarehouse : smWarehouses) {
			Warehouse warehouse = smWarehouse.getWarehouse();
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("id", warehouse.getId());
			map.put("name", warehouse.getName());
			map.put("erp_warehouse_code", warehouse.getErp_warehouse_code());
			map.put("sn", warehouse.getSn());
			warehouses.add(map);
		}
		model.addAttribute("warehouses_json", JsonUtils.toJson(warehouses));
		List<Map<String, Object>> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findListByStoreMember(id);
		String ids = "";
		for (int i = 0; i < storeMemberSaleOrgs.size(); i++) {
			Map<String, Object> map = storeMemberSaleOrgs.get(i);
			if (i == storeMemberSaleOrgs.size() - 1) {
				ids += map.get("id");
			}
			else {
				ids += map.get("id") + ",";
			}
		}
		List<Map<String, Object>> puts = storeMemberSaleOrgBaseService.findListBySaleOrg(id,
				ids);
		List<Map<String, Object>> posts = null;
		for (Map<String, Object> storeMemberSaleOrg : storeMemberSaleOrgs) {
			posts = new ArrayList<Map<String, Object>>();
			String procTempId = storeMemberSaleOrg.get("id").toString();
			for (Map<String, Object> itemMap : puts) {
				String pid = itemMap.get("saleOrg").toString();
				if (procTempId.equals(pid)) {
					posts.add(itemMap);
				}
			}
			storeMemberSaleOrg.put("posts", posts);
		}
		String storeMemberSaleOrgs_json = JsonUtils.toJson(storeMemberSaleOrgs);
		model.addAttribute("storeMemberSaleOrgs_json", storeMemberSaleOrgs_json);

		model.addAttribute("storeTypes", storeBaseService.getTypes());// 客户类型

		int undefinedProduct2Order = 0;// 未定义产品是否支持下单
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);

		Integer linkStock  = Integer.valueOf(SystemConfig.getConfig("linkStock",WebUtils.getCurrentCompanyInfoId()));
		model.addAttribute("linkStock", linkStock);

		if (flag!=null && flag == 1) {
			return "/member/store_member/edit_info";
		}else if(flag!=null && flag == 2){
			return "/member/store_member/edit_info_card";
		}
		return "/member/store_member/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(StoreMember storeMember, Member member, Long memberRankId,
			Long[] storeId, Integer[] isDefault, Long pc_id, Long parentId,
			Long[] roleId, Long[] saleOrgId, Long[] warehouseId,Long[] organizationId,
			Integer[] isDeFaults,String[] postId, Boolean isAdministrator, Long[] sbuId,
			Integer[] isDefaults,Long[] shopId,String[] postIds, HttpServletRequest request,
			ModelMap model) {

		if (ConvertUtil.isEmpty(member.getMobile())) {
			// 用户名或手机号未填
			return error("1601002");
		}
		if (storeMember.getMemberType() != 1
				&& (saleOrgId == null || saleOrgId.length < 1)) {
			return error("请维护所属机构");
		}
		Setting setting = SettingUtils.get();
		if (StringUtils.isNotEmpty(member.getPassword())) {
			if (member.getPassword().length() < setting.getPasswordMinLength()
					|| member.getPassword().length() > setting.getPasswordMaxLength()) {
				// 密码长度范围有误
				return error("1601005");
			}
		}

		// 更新用户
		storeMemberBaseService.updateStoreMember(storeMember,
				member,
				storeId,
				roleId,
				saleOrgId,
				postId,
				isDefault,
				warehouseId,
				isAdministrator,
				pc_id,
				parentId,
				sbuId,
				isDefaults,
				shopId,
				postIds,
				request,
				organizationId,
				isDeFaults);

		return success();
	}

	/**
	 * 点击用户头像弹出更新
	 */
	@RequestMapping(value = "/update_info", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update_info(StoreMember storeMember, Member member) {

		Setting setting = SettingUtils.get();
		if (StringUtils.isNotEmpty(member.getPassword())) {
			if (member.getPassword().length() < setting.getPasswordMinLength()
					|| member.getPassword().length() > setting.getPasswordMaxLength()) {
				// 密码长度范围有误
				return error("1601005");
			}
		}
		
		
		storeMemberBaseService.updateInfo(storeMember, member);
		return success().addObjX("/login.jhtml");
	}

	/**
	 * 业务员列表
	 */
	@RequestMapping(value = "/select_saleman", method = RequestMethod.GET)
	public String selectSaleman(Long memberRankId, Boolean isPartner,
			Integer multi, Pageable pageable, ModelMap model) {
		

		model.addAttribute("memberRankId", memberRankId);
		model.addAttribute("isPartner", isPartner);
		model.addAttribute("multi", multi);

		return "/member/store_member/select_saleman";
	}

	/**
	 * 业务员列表数据
	 */
	@RequestMapping(value = "/select_saleman_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectSalemanData(String username, String mobile, String name,
			Long memberRankId, Boolean isPartner, Long storeId, Long saleOrgId,
			String startTime, String endTime, Pageable pageable, ModelMap model) {

		Object[] args = new Object[] { username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				null,
				true };
		Page<Map<String, Object>> page = storeMemberBaseService.findPage(pageable,
				args);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_store_member", method = RequestMethod.GET)
	public String selectStoreMember(Long memberRankId, Boolean isPartner,
			Integer multi, Pageable pageable, ModelMap model, Integer role,
			Integer memberType,Boolean isSalesman) {

		model.addAttribute("memberRankId", memberRankId);
		model.addAttribute("isPartner", isPartner);
		model.addAttribute("multi", multi);
		model.addAttribute("role", role);
		model.addAttribute("memberType", memberType);
		model.addAttribute("isSalesman", isSalesman);

		return "/member/store_member/select_store_member";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_store_member_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectStoreMemberData(String username, String mobile,
			String name, Long memberRankId, Boolean isPartner, Long storeId,
			Long saleOrgId, String startTime, String endTime,
			Pageable pageable, ModelMap model, Integer role, Integer memberType,
			Boolean isSalesman) {
		Object[] args = new Object[] { username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				memberType,
				false,
				role };
		Page<Map<String, Object>> page = storeMemberBaseService.findPage(pageable,
				args);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * excel导入用户
	 * 
	 * @param file
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file, Integer memberType,
			HttpServletResponse response) throws Exception {

		Map<String, Object> results = storeMemberBaseService.storeMemberImport(file,
				memberType);
		int result = Integer.parseInt(results.get("result").toString());
		int success = Integer.parseInt(results.get("success").toString());
		String msg = results.get("msg").toString();

		// String resultMsg = "";
		if (ConvertUtil.isEmpty(msg)) {
			// resultMsg = JsonUtils.toJson(success("总数" + result + "行，成功导入" +
			// success + " 行"));
			return success("总数" + result + "行，成功导入" + success + " 行");
		}
		else {
			// resultMsg = JsonUtils.toJson(error("总数" + result + "行，成功导入" +
			// success + " 行<br>" + msg));
			return error("总数" + result + "行，成功导入" + success + " 行<br>" + msg);
		}
		// response.sendRedirect("/common/errComm.jhtml?resultMsg=" +
		// resultMsg);

		// return success(resultMsg);
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String username, String mobile,
			String name, Long memberRankId, Boolean isPartner, Long storeId,
			Long saleOrgId, String startTime, String endTime,
			Integer memberType, Pageable pageable, ModelMap model) {

		Object[] args = new Object[] { username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				memberType };
		Page<Map<String, Object>> findPage = storeMemberBaseService.findPage(pageable,
				args);
		Integer size = (int) findPage.getTotal();
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 导出
	 * 
	 * @param username
	 * @param mobile
	 * @param name
	 * @param memberRankId
	 * @param isPartner
	 * @param storeId
	 * @param saleOrgId
	 * @param startTime
	 * @param endTime
	 * @param model
	 * @param page
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String username, String mobile,
			String name, Long memberRankId, Boolean isPartner, Long storeId,
			Long saleOrgId, String startTime, String endTime,
			Integer memberType, ModelMap model, Integer page) {
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = storeMemberBaseService.findList(username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				memberType,
				null,
				page,
				size);
		return this.getModelAndView(data, model);

	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {
		List<Map<String, Object>> data = storeMemberBaseService.findList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null);
		return this.getModelAndView(data, model);
	}

	public ModelAndView getModelAndView(List<Map<String, Object>> data,
			ModelMap model) {
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		String[] header = { "用户名",
				"手机",
				"姓名",
				"所属客户",
				"机构名称",
				"SBU",
				"用户状态",
				"创建日期", };
		// 设置单 元格取值
		String[] properties = { "username",
				"mobile",
				"name",
				"store_name",
				"sale_org_name",
				"sbu_name",
				"is_enabled",
				"create_date" };
		for (Map<String, Object> map : data) {
			String is_enabled = null;
			if (map.get("is_enabled") != null) {
				is_enabled = map.get("is_enabled").toString();
				if ("false".equals(is_enabled)) {
					is_enabled = "禁用";
				}
				else {
					is_enabled = "正常";
				}
			}
			map.put("is_enabled", is_enabled);
		}

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, null, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	@RequestMapping(value = "/edit_post", method = RequestMethod.GET)
	public String edit_proc_user(Long[] postId, Long saleOrgId, ModelMap model) {

		List<Map<String, Object>> posts = new ArrayList<Map<String, Object>>();
		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		if (postId != null) {
			for (Long id : postId) {
				Map<String, Object> map = new HashMap<String, Object>();
				Post post = postBaseService.find(id);
				map.put("id", post.getId());
				map.put("name", post.getName());
				map.put("sale_org_name", saleOrg.getName());
				posts.add(map);
			}
		}
		posts = new ArrayList<Map<String, Object>>(
				new LinkedHashSet<Map<String, Object>>(posts));
		model.addAttribute("procuserTemps", JsonUtils.toJson(posts));
		model.addAttribute("saleOrgId", saleOrgId);
		return "/member/store_member/edit_post";
	}

	@RequestMapping(value = "/intf", method = RequestMethod.GET)
	public @ResponseBody
	ResultMsg intf(Pageable pageable, ModelMap model) {
		intfOrderMessageToService.IntfStoreMember();
		return success("");
	}
}
