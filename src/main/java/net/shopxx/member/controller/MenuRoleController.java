package net.shopxx.member.controller;

import java.util.*;

import javax.annotation.Resource;

import net.shopxx.mobile.entity.AppMenu;
import net.shopxx.mobile.service.AppMenuBaseService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.entity.PcMenu;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.VisualReport;
import net.shopxx.member.service.PcMenuBaseService;
import net.shopxx.member.service.PcRoleBaseService;
import net.shopxx.member.service.VisualReportService;

/**
 * Controller - 菜单角色
 */
@Controller("memberMenuRoleController")
@RequestMapping("/member/menu_role")
public class MenuRoleController extends BaseController {

	@Resource(name = "pcRoleBaseServiceImpl")
	private PcRoleBaseService pcRoleBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "pcMenuBaseServiceImpl")
	private PcMenuBaseService pcMenuBaseService;
	@Resource(name = "visualReportServiceImpl")
	private VisualReportService visualReportService;

	@Resource(name = "appMenuBaseServiceImpl")
	private AppMenuBaseService appMenuBaseService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/member/menu_role/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/menu_role/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String name, Boolean isSystem, Pageable pageable,
			String firstTime, String lastTime, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		if (StringUtils.isNotEmpty(name)) {
			filters.add(Filter.like("name", "%" + name + "%"));
		}
		if (isSystem != null) {
			filters.add(Filter.eq("isSystem", isSystem));
		}
		if (StringUtils.isNotEmpty(firstTime)) {
			filters.add(Filter.ge("createDate",
					DateUtil.convert(firstTime + " 00:00:00")));
		}
		if (StringUtils.isNotEmpty(lastTime)) {
			filters.add(Filter.lt("createDate",
					DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
							1)));
		}
		Page<PcRole> page = pcRoleBaseService.findPage(filters, null, pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		if (companyInfo != null) {
			model.addAttribute("companyInfo", companyInfo);
		}
		//List<PcMenu> menus = pcMenuBaseService.getCurrentMenuList(1);
		List<PcMenu> menus = pcMenuBaseService.findAllMenuList(1);
		model.addAttribute("menus", menus);
		//按钮
		List<VisualReport> visualReport = new ArrayList<VisualReport>();
		List<Map<String, Object>> visualReportList = visualReportService.findList(true);
		for(Map<String,Object> vr : visualReportList){
			Long visualReportId = Long.parseLong(vr.get("id").toString());
			visualReport.add(visualReportService.find(visualReportId));
		}
		model.addAttribute("visualReport", visualReport);

		/**
		 * app菜单
		 */
		List<AppMenu> appMenus = new ArrayList<AppMenu>();//所有app菜单
		List<Map<String, Object>> appMenuList = appMenuBaseService.findList(true);
		for(Map<String,Object> am : appMenuList){
			Long appMenuId = Long.parseLong(am.get("id").toString());
			appMenus.add(appMenuBaseService.find(appMenuId));
		}
        model.addAttribute("appMenus",appMenus);

        return "/member/menu_role/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(PcRole pcRole, Long[] menuId,Long[] visualReport,Long[] appMenuId) {

		if (pcRole.getName() == null) {
			return error("请填写角色名称");
		}
		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("name", pcRole.getName()));
		List<PcRole> pcRoles = pcRoleBaseService.findList(null, fis, null);
		if (pcRoles != null && pcRoles.size() > 0) {
			return error("该角色名称已存在，请重新维护");
		}
		
		List<PcMenu> menus = new ArrayList<PcMenu>();
		for (Long mId : menuId) {
			PcMenu menu = pcMenuBaseService.find(mId);
			menus.add(menu);
		}



        //
		if(visualReport!=null){
			List<VisualReport> roleVr = new ArrayList<VisualReport>();
			for (Long vr : visualReport) {
				VisualReport vrs = visualReportService.find(vr);
				roleVr.add(vrs);
			}
			
			pcRole.setRoleVrs(new HashSet<VisualReport>(roleVr));			
		}
		pcRole.setPcMenus(new HashSet<PcMenu>(menus));
		pcRole.setIsSystem(false);
		pcRoleBaseService.save(pcRole);
        //保存app菜单
        if(appMenuId != null){
            appMenuBaseService.addRoleAppMenu(pcRole,appMenuId);
        }
		return success().addObjX(pcRole.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {

		model.addAttribute("pcRole", pcRoleBaseService.find(id));
		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		if (companyInfo != null) {
			model.addAttribute("companyInfo", companyInfo);
		}
		//List<PcMenu> menus = pcMenuBaseService.getCurrentMenuList(1);
		List<PcMenu> menus = pcMenuBaseService.findAllMenuList(1);
		model.addAttribute("menus", menus);

		/**
		 * app菜单
		 */

		List<AppMenu> appMenus = new ArrayList<AppMenu>();//所有app菜单
		List<Map<String, Object>> appMenuList = appMenuBaseService.findList(true);
		for(Map<String,Object> am : appMenuList){
			Long appMenuId = Long.parseLong(am.get("id").toString());
			appMenus.add(appMenuBaseService.find(appMenuId));
		}
		model.addAttribute("appMenus",appMenus);

		//角色拥有的app菜单
		//List<Map<String, Object>> roleMenuList = appMenuBaseService.findListByRoleId(id);
		List<AppMenu> appRoleMenus = appMenuBaseService.findListByRoleId(id);
		model.addAttribute("appRoleMenus",appRoleMenus);



		List<VisualReport> visualReport = new ArrayList<VisualReport>();
		List<Map<String, Object>> visualReportList = visualReportService.findList(true);
		for(Map<String,Object> vr : visualReportList){
			Long visualReportId = Long.parseLong(vr.get("id").toString());
			visualReport.add(visualReportService.find(visualReportId));
		}
		model.addAttribute("visualReport", visualReport);
		return "/member/menu_role/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(PcRole pcRole, Long[] menuId,Long[] visualReport,Long[] appMenuId) {

		PcRole pPcRole = pcRoleBaseService.find(pcRole.getId());
		if (pPcRole == null) {
			return error("16350");
		}

		if (pcRole.getName() == null) {
			return error("请填写角色名称");
		}
		List<Filter> fis = new ArrayList<Filter>();
	//	fis.add(Filter.ne("id", pcRole.getId()));
		fis.add(Filter.eq("name", pcRole.getName()));
		List<PcRole> pcRoles = pcRoleBaseService.findList(null, fis, null);
		if (pcRoles != null && pcRoles.size() > 1) {
			return error("该角色名称已存在，请重新维护");
		}

		List<PcMenu> menus = new ArrayList<PcMenu>();
		for (Long mId : menuId) {
			PcMenu menu = pcMenuBaseService.find(mId);
			menus.add(menu);
			if (menu.getSuperId()!=null){   //父级
				menus.add(menu.getSuperId());
				if (menu.getSuperId().getSuperId()!=null){
					menus.add(menu.getSuperId().getSuperId());
				}
			}
		}

		//添加角色和app菜单的关系
		appMenuBaseService.updateRoleAppMenu(pcRole,appMenuId);


		List<VisualReport> roleVr = new ArrayList<VisualReport>();
		if(visualReport!=null){
			for (Long vr : visualReport) {
				VisualReport vrs = visualReportService.find(vr);
				roleVr.add(vrs);
			}
			
			pcRole.getRoleVrs().clear();
			pcRole.setRoleVrs(new HashSet<VisualReport>(roleVr));			
		}
		pcRole.getPcMenus().clear();
		pcRole.setPcMenus(new HashSet<PcMenu>(menus));
		if (pPcRole.getIsSystem()) {
			pcRoleBaseService.update(pcRole,
					"name",
					"isSystem",
					"companyInfoId");
		}
		else {
			pcRoleBaseService.update(pcRole, "isSystem", "companyInfoId");
		}
		return success();
	}

	/**
	 * 删除
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg delete(Long[] ids) {

		if (ids != null && ids.length > 0) {
			for (Long id : ids) {
				PcRole pcRole = pcRoleBaseService.find(id);
				if (pcRole == null) {
					return error("16350");
				}
				if (pcRole.getIsSystem()) {
					return error("16351");
				}
				if (pcRoleBaseService.count(Filter.eq("pcRole", pcRole)) > 0) {
					return error("16352", pcRole.getName());
				}
			}
			pcRoleBaseService.delete(ids);
		}
		else {
			return error("16353");
		}
		return success();

	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_menu_role", method = RequestMethod.GET)
	public String select_menu_role(Integer multi, ModelMap model) {

		model.addAttribute("multi", multi);
		return "/member/menu_role/select_menu_role";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_menu_role_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_menu_role_data(String name, Boolean isSystem,
			Pageable pageable, String firstTime, String lastTime, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		if (StringUtils.isNotEmpty(name)) {
			filters.add(Filter.like("name", "%" + name + "%"));
		}
		if (isSystem != null) {
			filters.add(Filter.eq("isSystem", isSystem));
		}
		if (StringUtils.isNotEmpty(firstTime)) {
			filters.add(Filter.ge("createDate",
					DateUtil.convert(firstTime + " 00:00:00")));
		}
		if (StringUtils.isNotEmpty(lastTime)) {
			filters.add(Filter.lt("createDate",
					DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
							1)));
		}
		Page<PcRole> page = pcRoleBaseService.findPage(filters, null, pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

}