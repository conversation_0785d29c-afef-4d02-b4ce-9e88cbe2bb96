package net.shopxx.member.controller;

import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.member.service.PwdModificationRecordService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 修改密码
 */
@Controller("pwdModRecordController")
@RequestMapping("/pwdModRecord")
public class PwdModRecordController extends BaseController {

    @Resource(name = "pwdModificationRecordServiceImpl")
    private PwdModificationRecordService pwdModificationRecordService;

    /**
     * 列表
     */
    @RequestMapping(value = "/change_pwd", method = RequestMethod.GET)
    public String change_pwd(Long storeMemberId,ModelMap model) {
        model.addAttribute("storeMemberId",storeMemberId);
        return "/change_pwd";
    }


    /**
     * 修改密码提交
     * @throws Exception
     */
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg submit(String oldpwd, String newPwd, String confirmNewPwd,Long storeMemberId,
                     String language, ModelMap model)
            throws Exception {
        language = StringUtils.isEmpty(language) ? "ChinaCN" : language;

        String userName = pwdModificationRecordService.saveUserPasswordRecord(storeMemberId, oldpwd, newPwd, confirmNewPwd, language);
        return  success().addObjX("/member/index.jhtml");
    }
}
