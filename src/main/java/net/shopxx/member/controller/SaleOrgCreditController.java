package net.shopxx.member.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.member.entity.SaleOrgCredit;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.SaleOrgCreditService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

@Controller("shopSaleOrgCreditController")
@RequestMapping("/member/saleorg_credit")
public class SaleOrgCreditController extends BaseController {

	@Resource(name = "saleOrgCreditServiceImpl")
	private SaleOrgCreditService saleOrgCreditService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long menuId, ModelMap model) {

		model.addAttribute("menuId", menuId);
		return "/member/saleorg_credit/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Long userId, Long menuId, ModelMap model) {
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/saleorg_credit/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn, Integer[] status, Long creatorId,
			Long operatorId, Long storeId, Long sbuId, Pageable pageable,
			ModelMap model) {
		Page<Map<String, Object>> page = saleOrgCreditService.findPage(1,
				sn,
				null,
				storeId,
				status,
				creatorId,
				operatorId,
				sbuId,
				pageable);
		List<Map<String, Object>> heads = page.getContent();
		if (!heads.isEmpty()) {
			String ids = "";
			for (int i = 0; i < heads.size(); i++) {
				Map<String, Object> map = heads.get(i);
				if (i == heads.size() - 1) {
					ids += map.get("id");
				}
				else {
					ids += map.get("id") + ",";
				}
			}
			List<Map<String, Object>> lines = saleOrgCreditService.findScItemList(null, ids, true);
			List<Map<String, Object>> items = null;
			for (Map<String, Object> map : heads) {
				items = new ArrayList<Map<String, Object>>();
				String headId = map.get("id").toString();
				for (Map<String, Object> itemMap : lines) {
					String hid = itemMap.get("sale_org_credit").toString();
					if (headId.equals(hid)) {
						items.add(itemMap);
					}
				}
				map.put("sale_org_credit_items", items);
			}
		}
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping(value = "/list_check_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_check_data(String sn, Integer[] status, Long creatorId,
			Long operatorId, Long storeId, Pageable pageable, ModelMap model) {

		boolean isAdmin = WebUtils.isAdmin();
		if (!isAdmin) {
			creatorId = storeMemberService.getCurrent().getId();
		}
		String jsonPage = JsonUtils.toJson(saleOrgCreditService.findPage(1,
				sn,
				null,
				storeId,
				status,
				creatorId,
				operatorId,
				null,
				pageable));

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("startDate", DateUtil.getCurrDateStr("yyyy-MM-dd"));
		Calendar curr = Calendar.getInstance();
		curr.set(Calendar.YEAR, curr.get(Calendar.YEAR) + 1);
		Date y = curr.getTime();
		model.addAttribute("endDate", df.format(y));
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}

		List<Map<String, Object>> sbu = storeMemberService.findSbuTy(storeMember.getId());
		if (sbu.size() > 0) {
			Long sbuIds = Long.parseLong(sbu.get(0).get("id").toString());
			model.addAttribute("sbuIds", sbuIds);
		}
		
		/**
		 * 用户sbu
		 */
		List<Sbu> sbuList = roleJurisdictionUtil.getSbuList();
		model.addAttribute("sbuList", sbuList);
		
		/**
		 * 用户经营组织
		 */
		List<Organization> organizationList = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("organizationList", organizationList);
		
		//平台授信额度分配类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "saleOrgCreditType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgCreditTypeList = systemDictService.findList(null,filters,null);
		model.addAttribute("saleOrgCreditTypeList", saleOrgCreditTypeList);
		
		return "/member/saleorg_credit/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(SaleOrgCredit saleOrgCredit, Long storeId, 
			Long saleOrgId, Long sbuId, Long organizationId, Long saleOrgCreditTypeId) {
		
		if(!ConvertUtil.isEmpty(saleOrgCredit.getId())){
			SaleOrgCredit pSaleOrgCredit = saleOrgCreditService.find(saleOrgCredit.getId());
			//校验平台授信单状态
			saleOrgCreditService.checkSaleOrgCreditStatus(pSaleOrgCredit,0,"已保存","保存");
		}
		if(saleOrgCredit.getSaleOrgCreditItems().isEmpty()){
			return error("会员价明细不能少于一条");
		}
		if (saleOrgCredit.getTotalAmount().compareTo(BigDecimal.ZERO) != 1) {
			return error("授信金额不能小于0");
		}
		if (saleOrgCredit.getUsedAmount() == null) {
			saleOrgCredit.setUsedAmount(BigDecimal.ZERO);
		}
		Date startDate = saleOrgCredit.getStartDate();
		Date endDate = saleOrgCredit.getEndDate();
		if (startDate == null || endDate == null) {
			// 请维护开始时间和结束时间
			return error("18551");
		}
		// 处理sbu
		if (sbuId != null) {
			Sbu sbu = sbuService.find(sbuId);
			saleOrgCredit.setSbu(sbu);
		}
		//经营组织
		if (organizationId != null) {
			Organization organization = organizationService.find(organizationId);
			saleOrgCredit.setOrganization(organization);
		}
		//平台授信额度分配类型
		if (saleOrgCreditTypeId == null) {
			return error("请选择平台授信额度分配类型！");
		}
		SystemDict saleOrgCreditType = systemDictService.find(saleOrgCreditTypeId);
		saleOrgCredit.setSaleOrgCreditType(saleOrgCreditType);
		
		saleOrgCreditService.saveOrUpdateSaleOrgCredit(saleOrgCredit);
		return success().addObjX(saleOrgCredit.getId());
	}

	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, ModelMap model) {

		SaleOrgCredit saleOrgCredit = saleOrgCreditService.find(id);
		model.addAttribute("cr", saleOrgCredit);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		/**全链路*/
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(saleOrgCredit.getSn(),
				8));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(58L);
		model.addAttribute("isCheckWf", isCheckWf);

		/**明细*/
		List<Map<String, Object>> lines = saleOrgCreditService.findScItemList(null,id.toString(),null);
		String jsonStr = JsonUtils.toJson(lines);
		model.addAttribute("items", jsonStr);

		/**
		 * 用户sbu
		 */
		List<Sbu> sbuList = roleJurisdictionUtil.getSbuList();
		model.addAttribute("sbuList", sbuList);
		
		/**
		 * 用户经营组织
		 */
		List<Organization> organizationList = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("organizationList", organizationList);
		
		//平台授信额度分配类型
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "saleOrgCreditType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgCreditTypeList = systemDictService.findList(null,filters,null);
		model.addAttribute("saleOrgCreditTypeList", saleOrgCreditTypeList);
		
		return "/member/saleorg_credit/check";
	}

	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check(Long id, Integer flag, String note, BigDecimal totalAmount,
			ModelMap model) {

		SaleOrgCredit saleOrgCredit = saleOrgCreditService.find(id);
		//校验平台授信单状态
		saleOrgCreditService.checkSaleOrgCreditStatus(saleOrgCredit,0,"已保存","审核");
		if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
			//授信金额必须大于0
			return error("授信金额必须大于0");
		}
		saleOrgCreditService.check(saleOrgCredit, flag, note, totalAmount);
		return success();
	}

	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check_wf(Long id, Integer flag, String note,
			BigDecimal totalAmount, Long objConfId, ModelMap model) {

		SaleOrgCredit saleOrgCredit = saleOrgCreditService.find(id);

		if (saleOrgCredit.getDocStatus() != 0) {
			return error("只有单据状态为已保存的申请单才能审批流程");
		}
		if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
			//授信金额必须大于0
			return error("授信金额必须大于0");
		}
		saleOrgCreditService.checkSaleOrgCreditWf(saleOrgCredit,
				flag,
				note,
				totalAmount,
				objConfId);

		return success();
	}

	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg cancel(Long id, Integer flag, String note, ModelMap model) {

		SaleOrgCredit saleOrgCredit = saleOrgCreditService.find(id);
		//校验平台授信单状态
		saleOrgCreditService.checkSaleOrgCreditStatus(saleOrgCredit,0,"已保存","作废");
		//单据状态  0.已保存(没有流程)  1.处理中(有流程)  2.已处理(流程走完) 3.作废
		saleOrgCredit.setDocStatus(3); 
		saleOrgCreditService.update(saleOrgCredit);
		orderFullLinkService.addFullLink(18,null,saleOrgCredit.getSn(),
				ConvertUtil.convertI18nMsg("18703", new Object[] { "平台授信",
						"作废本次平台授信" }),null);
		return success();
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/updata", method = RequestMethod.POST)
	public @ResponseBody ResultMsg updata(SaleOrgCredit saleOrgCredit,
			Long saleOrgId, Long sbuId, Long organizationId,Long saleOrgCreditTypeId) {
		
		
		if(saleOrgCredit.getSaleOrgCreditItems().isEmpty()){
			return error("会员价明细不能少于一条");
		}
		
		SaleOrgCredit cr = saleOrgCreditService.find(saleOrgCredit.getId());
		if (cr.getDocStatus() != 0) {
			return error("只有单据状态为已保存才允许修改");
		}
		if (saleOrgCredit.getTotalAmount() == null) {
			return error("授信总金额不能为空");
		}
		if (saleOrgCredit.getUsedAmount() == null) {
			saleOrgCredit.setUsedAmount(BigDecimal.ZERO);
		}
		//sbu
		Sbu sbu = sbuService.find(sbuId);
		saleOrgCredit.setSbu(sbu);
		//经营组织
		Organization organization = organizationService.find(organizationId);
		saleOrgCredit.setOrganization(organization);
		//平台授信额度分配类型
		if (saleOrgCreditTypeId == null) {
			return error("请选择平台授信额度分配类型！");
		}
		SystemDict saleOrgCreditType = systemDictService.find(saleOrgCreditTypeId);
		saleOrgCredit.setSaleOrgCreditType(saleOrgCreditType);
		
		saleOrgCreditService.saveOrUpdateSaleOrgCredit(saleOrgCredit);
		return success();
	}

	/**
	 * 获取授信额度
	 */
	@RequestMapping(value = "/get_amount", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg get_amount(Long saleOrgId) {

		Map<String, Object> map = new HashMap<String, Object>();

		List<Map<String, Object>> saleOrgCreditList = saleOrgCreditService.findListBySaleOrg(saleOrgId);
		if (saleOrgCreditList != null && saleOrgCreditList.size() > 0) {
			map.put("total_amount", saleOrgCreditList.get(0).get("totalAmount"));
			map.put("used_amount", saleOrgCreditList.get(0).get("usedAmount"));
		}
		return success().addObjX(map);
	}

}