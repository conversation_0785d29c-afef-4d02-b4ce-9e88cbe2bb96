/*
 * Copyright 2005-2013 shopxx.net. All rights reserved. Support:
 * http://www.shopxx.net License: http://www.shopxx.net/license
 */
package net.shopxx.member.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.para.secure.client.OAuthClient;
import com.para.secure.client.model.UserInfo;
import com.para.secure.exceptions.OAuthApiException;
import com.para.secure.model.Token;
import com.para.secure.oauth.OAuthService;
import com.para.secure.utils.OAuthConfigUtil;

import net.sf.ehcache.CacheManager;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.service.LoginOutBaseService;
import net.shopxx.member.service.MemberBaseService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;

/**
 * Controller - 单点登录
 */
@Controller("authorizeController")
@RequestMapping("/custom")
public class AuthorizeController extends BaseController {

	private final String URLIP = "http://crm.linkcrm.top:8080"; //正式url

	@Resource(name = "memberBaseServiceImpl")
	private MemberBaseService memberBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "loginOutBaseServiceImpl")
	private LoginOutBaseService loginOutBaseService;
	@Resource(name = "ehCacheManager")
	private CacheManager cacheManager;

	/**
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/authorize", method = RequestMethod.GET)
	public @ResponseBody ResultMsg authorize(String username, String password, String companyName, String language,
			HttpServletRequest request, HttpServletResponse response, HttpSession session, ModelMap model)
			throws Exception {

		String targetUri = request.getParameter("target_uri");
		OAuthConfigUtil configUtil = new OAuthConfigUtil();
		String clientId = configUtil.getClientId();
		String clientSecret = configUtil.getClientSecret();
		String redirectUri = configUtil.getRedirectUri();
		String requestContextPath = request.getRequestURL().toString();
		String contextPath = request.getContextPath();
		requestContextPath = requestContextPath.substring(0,
				requestContextPath.indexOf(contextPath) + contextPath.length());

		//打印日志
		LogUtils.info("targetUri:"+targetUri);
		LogUtils.info("clientId:"+clientId);
		LogUtils.info("clientSecret:"+clientSecret);
		LogUtils.info("redirectUri:"+redirectUri);
		LogUtils.info("requestContextPath:"+requestContextPath);
		LogUtils.info("contextPath:"+contextPath);
		LogUtils.info("requestContextPath2:"+requestContextPath);

		OAuthService oAuthService = null;
		/** 参数说明：appId,appSecret, redirectUri,targetUri  用于后续判定页面跳转**/
		oAuthService = new OAuthService(clientId, clientSecret, redirectUri, targetUri);
		String ul = oAuthService.getAuthorizationUrl();
		/**重定向**/
		response.sendRedirect(oAuthService.getAuthorizationUrl());

		//打印日志
		LogUtils.info("oAuthService:"+oAuthService);
		LogUtils.info("ul:"+ul);
		LogUtils.info("ul:"+oAuthService.getAuthorizationUrl());

		return success().addObjX("/member/index.jhtml");
	}

	/**
	 * 单点登录callback
	 *
	 * @param language
	 * @param request
	 * @param response
	 * @param session
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/callback", method = RequestMethod.GET)
	public String callback(String code, String language, HttpServletRequest request, HttpServletResponse response,
			HttpSession session, ModelMap model) throws Exception {

		OAuthService oAuthService = null;
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		if (request.getSession().getAttribute("OAuthService") != null) {
			oAuthService = (OAuthService) request.getSession().getAttribute("OAuthService");
		} else {
			oAuthService = new OAuthService(OAuthConfigUtil.getClientId(), OAuthConfigUtil.getClientSecret(),
					OAuthConfigUtil.getRedirectUri(), "11");
		}
		code = request.getParameter("code");
		/**通过code换去token**/
		Token accessToken = oAuthService.getAccessToken(code);
		LogUtils.info("单点进入：code="+code+",accessToken:"+accessToken);
		try {
			// http://ssotest.ceibs.edu/profile/oauth2/profile?access_token=623239c3760c600d81ed551e6d2adf4fwefwqd5a1ed24d8f959ac44b2b82b
			OAuthClient cl = new OAuthClient(OAuthConfigUtil.getUserInfoUrl());
			UserInfo user_ = new UserInfo(accessToken);
			UserInfo user = user_.getUserInfo();
			LogUtils.info("userName:"+user.getUserName());
			if (user != null) {
				request.setAttribute("userName", user.getUserName());
				request.setAttribute("LinkId", user.getLinkId());
				request.setAttribute("classOrTeam", user.getClassOrTeam());
				request.setAttribute("id", user.getId());
				request.setAttribute("gradeOrDept", user.getGradeOrDept());
				request.setAttribute("userType", user.getUserType());
				request.setAttribute("loginName", user.getId());

				language = StringUtils.isEmpty(language) ? "ChinaCN" : language;
				String msg = loginOutBaseService.oauth(user.getLinkId(), language, httpRequest);
				LogUtils.info("request端口：" + request.getServerPort());
				if (msg.equalsIgnoreCase("success")) {
					/**成功 重定向index**/
					response.sendRedirect(URLIP+"/member/index.jhtml");
				} else if(msg.equalsIgnoreCase("moreThanOne")){
					/**当前ID用户不唯一**/
					response.sendRedirect(URLIP+"/login/submitCheck.jhtml");
				}else{
					/**登录失败**/
					response.sendRedirect(URLIP+"/login.jhtml");
				}
			}
		} catch (OAuthApiException e) {
			LogUtils.error("单点异常:"+e);
		}
		return "/login";
	}

	private CompanyInfo findCompanyInfo(HttpServletRequest request) {
		CompanyInfo companyInfo = null;
		String webSite = "";
		StringBuffer sb = request.getRequestURL();
		Matcher m = Pattern.compile("^http://[^/]+").matcher(sb);
		while (m.find()) {
			webSite = m.group();
		}
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("webSite", webSite.trim()));
		List<CompanyInfo> companyInfos = companyInfoBaseService.findList(2, filters, null);
		if (companyInfos.size() == 1) {
			companyInfo = companyInfos.get(0);
		}
		return companyInfo;
	}
}