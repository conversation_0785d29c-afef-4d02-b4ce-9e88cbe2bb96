package net.shopxx.member.controller;

import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.member.service.CustomerRechargeService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
@Controller("customerRechargeController")
@RequestMapping("/member/customer_recharge")
public class CustomerRechargeController extends BaseController{
	
	@Resource(name = "customerRechargeServiceImpl")
    private CustomerRechargeService customerRechargeService;
	
	
	/**
	 *订单经营组织余额
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Long storeId,Long sbuId,Long organizationId) {

		String mapListJson = JsonUtils.toJson(customerRechargeService.
				findCustomerRechargeList(storeId, organizationId, sbuId));

		return success(mapListJson);
	}

}
