package net.shopxx.member.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.FleeingGoods;
import net.shopxx.member.entity.FleeingGoodsAttach;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.FleeingGoodsAttachService;
import net.shopxx.member.service.FleeingGoodsService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.util.CommonUtil;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 窜货
 */
@Controller("fleeingGoodsController")
@RequestMapping("/member/fleeing_goods")
public class FleeingGoodsController extends BaseController {
    
    @Resource(name = "fleeingGoodsServiceImpl")
    private FleeingGoodsService fleeingGoodsService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "fleeingGoodsAttachServiceImpl")
    private FleeingGoodsAttachService fleeingGoodsAttachService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "shopAddedServiceImpl")
    private ShopAddedService shopAddedService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    
    /**
     * 列表
     */
    @RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
    public String list_tb(@PathVariable String code, Long sid,Long objTypeId, Long objid, ModelMap model) {
        model.addAttribute("code", code);
        model.addAttribute("sid", sid);
        model.addAttribute("objTypeId", objTypeId);
        model.addAttribute("objid", objid);
        return CommonUtil.getFolderPrefix(code) + "/member/fleeing_goods/list_tb";
    }
    
    /**
     * 列表
     */
    @RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
    public String list_code(@PathVariable String code, Integer multi, Long sid, ModelMap model) {
        model.addAttribute("code", code);
        model.addAttribute("multi", multi);
        model.addAttribute("sid", sid);
        return CommonUtil.getFolderPrefix(code) + "/member/fleeing_goods/list";
    }
    
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_Data(FleeingGoods fg, Long sid,
                               String createName, String firstTime, String lastTime,
                               String productSpec, String productName, String productModel,
                               String firstTimef, String lastTimef,
                               Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(sid);
        param.add(createName);
        param.add(firstTime);
        param.add(lastTime);
        param.add(firstTimef);
        param.add(lastTimef);
        param.add(productSpec);
        param.add(productName);
        param.add(productModel);
        Page<Map<String, Object>> page = fleeingGoodsService.findSelectPage(fg,param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }
    
    /**
     * 添加
     */
    @RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
    public String add(@PathVariable String code, Long sid, ModelMap model) {
        model.addAttribute("code", code);
        Store store = storeBaseService.find(sid);
        if (store != null) {
            model.addAttribute("store", store);
        }
        return CommonUtil.getFolderPrefix(code) + "/member/fleeing_goods/add";
    }
    
    /**
     * 选择列表
     */
    @RequestMapping(value = "/select_fleeing_goods/{code}", method = RequestMethod.GET)
    public String selectFleeingGoods(@PathVariable String code, Integer multi, Long sid, ModelMap model) {
        model.addAttribute("code", code);
        model.addAttribute("multi", multi);
        model.addAttribute("sid", sid);
        return CommonUtil.getFolderPrefix(code) + "/member/fleeing_goods/select_fleeing_goods";
    }
    
    /**
     * 列表数据 - 选择列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/select_shop_devise_data", method = RequestMethod.POST)
    public ResultMsg selectShopInfoData(String sn, Long sid, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(sn);
        param.add(sid);
        Page<Map<String, Object>> page = fleeingGoodsService.findSelectPage(null,param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }
    
    /**
     * 编辑
     */
    @RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
    public String edit(@PathVariable String code,Long id, ModelMap model) {
        model.addAttribute("code", code);
        FleeingGoods fleeingGoods = fleeingGoodsService.find(id);
        model.addAttribute("fleeingGoods", fleeingGoods);
        Store store = fleeingGoods.getByFleeingGoodStore();
        model.addAttribute("store",store);
        List<FleeingGoodsAttach> blowBy0Attachs = this.findAttachs(fleeingGoods,0);
        List<FleeingGoodsAttach> blowBy1Attachs = this.findAttachs(fleeingGoods,1);
        List<FleeingGoodsAttach> blowBy2Attachs = this.findAttachs(fleeingGoods,2);
        List<FleeingGoodsAttach> blowBy3Attachs = this.findAttachs(fleeingGoods,3);
        List<FleeingGoodsAttach> blowBy4Attachs = this.findAttachs(fleeingGoods,4);
        List<FleeingGoodsAttach> blowBy5Attachs = this.findAttachs(fleeingGoods,5);
        model.addAttribute("blowByAttachs_0", JsonUtils.toJson(blowBy0Attachs));
        model.addAttribute("blowByAttachs_1", JsonUtils.toJson(blowBy1Attachs));
        model.addAttribute("blowByAttachs_2", JsonUtils.toJson(blowBy2Attachs));
        model.addAttribute("blowByAttachs_3", JsonUtils.toJson(blowBy3Attachs));
        model.addAttribute("blowByAttachs_4", JsonUtils.toJson(blowBy4Attachs));
        model.addAttribute("blowByAttachs_5", JsonUtils.toJson(blowBy5Attachs));
        
        //审核流程
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        if(fleeingGoods.getWfId()!= null){
            ActWf wf = fleeingGoodsService.getWfByWfId(fleeingGoods.getWfId());
            if (wf != null) {
                //省长审核
                Boolean szsh = false;
                //渠道部审核
                Boolean qdbsh = false;
                //销售中心副总审核
                Boolean xszxfzsh = false;
                //事业部总裁审核
                Boolean sybzcsh = false ;
                //串货方确认
                Boolean chfqr = false;
                //省长审核权限
                Boolean szshs = false;
                //渠道部审核权限
                Boolean qdbshs = false;
                //销售中心副总审核权限
                Boolean xszxfzshs = false;
                //事业部总裁审核权限
                Boolean sybzcshs = false ;
                //串货方确认
//                Boolean chfqrs = false;
                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for(Map<String, Object> c : item){
                    if(c.get("suggestion")!=null){
                        //处理结果
                        String approved = c.get("approved")!=null?c.get("approved").toString():"false";
                        //节点名称
                        String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";

                        //对比节点名称是否对应
                        if(rwm.contains("省长")){
                            //赋值处理结果到所定义的节点上
                            szsh = Boolean.valueOf(approved);
                        }
                        if(rwm.contains("渠道")){
                            //赋值处理结果到所定义的节点上
                            qdbsh = Boolean.valueOf(approved);
                            if(!qdbsh){
                                szsh = false;
                            }
                        }
                        if(rwm.contains("销售")){
                            xszxfzsh = Boolean.valueOf(approved);
                            if(!xszxfzsh){
                                qdbsh = false;
                            }
                        }
                        if(rwm.contains("事业部")){
                            sybzcsh = Boolean.valueOf(approved);
                            if(!sybzcsh){
                                xszxfzsh = false;
                            }
                        }
                        if(rwm.contains("串货方")){
                            chfqr = Boolean.valueOf(approved);
                            if(!chfqr){
                                sybzcsh = false;
                            }
                        }
                    }
                }
                //获取当前流程所在的节点
                Task t = shopAddedService.getCurrTaskByWf(wf);
                if(t!=null){
                    //获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
                        szshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道")){
                        szshs = true;qdbshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("销售")){
                        szshs = true;qdbshs = true;xszxfzshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("事业部")){
                        szshs = true;qdbshs = true;xszxfzshs = true;sybzcshs = true;
                    }
                }
                model.addAttribute("wf", wf);
                model.addAttribute("node", t);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdbsh", qdbsh);
                model.addAttribute("xszxfzsh", xszxfzsh);
                model.addAttribute("sybzcsh", sybzcsh);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdbshs", qdbshs);
                model.addAttribute("xszxfzshs", xszxfzshs);
                model.addAttribute("sybzcshs", sybzcshs);
            }
        }
        return CommonUtil.getFolderPrefix(code) + "/member/fleeing_goods/edit";
    }
    
    /**
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMsg save(FleeingGoods fleeingGoods, Long productId) {
    	Product product = productBaseService.find(productId);
    	if(fleeingGoods.getByFleeingGoodStore()==null||fleeingGoods.getByFleeingGoodStore().getId() == null){
    		return error("被侵权经销商不能为空！");
    	}
    	if(fleeingGoods.getFgRegionArea()==null||fleeingGoods.getFgRegionArea().getId()==null){
    		return error("请选择窜货方信息地址！");
    	}
        fleeingGoods.setStatus(0);
        fleeingGoods.setProduct(product);
        fleeingGoodsService.saveFleeingGoods(fleeingGoods);
        return success().addObjX(fleeingGoods.getId());
    }
    
	/**
     * 更新
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultMsg update(FleeingGoods fleeingGoods, Long productId) {
    	Product product = productBaseService.find(productId);
    	if(fleeingGoods.getByFleeingGoodStore()==null||fleeingGoods.getByFleeingGoodStore().getId() == null){
    		return error("被侵权经销商不能为空！");
    	}
    	if(fleeingGoods.getFgRegionArea()==null||fleeingGoods.getFgRegionArea().getId()==null){
    		return error("请选择窜货方信息地址！");
    	}
    	fleeingGoods.setProduct(product);
        fleeingGoodsService.updateFleeingGoods(fleeingGoods);
        return success().addObjX(fleeingGoods.getId());
    }
    
	private List<FleeingGoodsAttach> findAttachs(FleeingGoods fleeingGoods, Integer type) {
		List<Filter> filterss = new ArrayList<Filter>();
	    filterss.add(Filter.eq("fleeingGoods",fleeingGoods));
	    filterss.add(Filter.eq("type",type));
	    List<FleeingGoodsAttach> fleeingGoodsAttachs = fleeingGoodsAttachService.findList(null, filterss, null);
	    return fleeingGoodsAttachs;
	}
	/**
     * 审核
     * @param id shopId
     * @return
     */
    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg check_wf(Long id,String modelId,Long objTypeId) {
        if (id == null) {
            // 请选择订单
            return error("请选择单据");
        }

        fleeingGoodsService.createWf(id,modelId,objTypeId);
        return success();
    }
    /**
     * 流程节点保存
     */
    @RequestMapping(value = "/saveform", method = RequestMethod.POST)
    public @ResponseBody ResultMsg saveform(FleeingGoods fleepingGoods, Integer type) {
        // type 用来定义节点
        //type 1区域经理 2省长
        fleeingGoodsService.saveform(type,fleepingGoods);
        return success().addObjX(fleepingGoods.getId());
    }
    
    @RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(FleeingGoods fg, Long sid,
                                        String createName, String firstTime, String lastTime,
                                        String productSpec, String productName, String productModel,
                                        String firstTimef, String lastTimef, String[] header, String[] properties,
                                        Pageable pageable, ModelMap model, Integer page){
    	List<Object> params = new ArrayList<Object>();
    	params.add(sid);
    	params.add(createName);
    	params.add(firstTime);
    	params.add(lastTime);
    	params.add(firstTimef);
    	params.add(lastTimef);
    	params.add(productSpec);
    	params.add(productName);
    	params.add(productModel);
    	Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = fleeingGoodsService.findList(fg, params, pageable, page, size);
		return getModelAndViewForList(data,header,properties, model);
    }
    
    /**
	 * 条件导出
	 * 
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(FleeingGoods fg, Long sid,
                                                                     String createName, String firstTime, String lastTime,
                                                                     String productSpec, String productName, String productModel,
                                                                     String firstTimef, String lastTimef){
		List<Object> params = new ArrayList<Object>();
    	params.add(sid);
    	params.add(createName);
    	params.add(firstTime);
    	params.add(lastTime);
    	params.add(firstTimef);
    	params.add(lastTimef);
    	params.add(productSpec);
    	params.add(productName);
    	params.add(productModel);
		int size = fleeingGoodsService.count(fg,params);
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,String[] header,String[] properties, ModelMap model) {
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		Integer[] widths = {};
		if(header==null){
			header = new String[]{};
		}
		if(properties==null){
			properties = new String[]{};
		}else{
			List<Integer> w = new ArrayList<Integer>();
			for(int i=0;i<properties.length;i++){
				w.add(25 * 256);
			}
			widths = w.toArray(new Integer[]{w.size()});
		}
		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}
    
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
}
