package net.shopxx.member.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.*;
import net.shopxx.member.entity.*;
import net.shopxx.template.entity.DTemplates;
import net.shopxx.template.service.DTemplatesService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.MobileAndIdCardUtil;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreApply.Type;
import net.shopxx.member.service.StoreApplySbuService;
import net.shopxx.member.service.StoreApplyService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.shop.entity.ShopAlteration;
import net.shopxx.util.CommonUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

/*
 * Controller - 客户
 */
@Controller("storeApplyController")
@RequestMapping("/member/storeApply")
public class StoreApplyController extends BaseController {

	@Resource(name = "storeApplyServiceImpl")
	private StoreApplyService storeApplyService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	@Resource(name = "storeApplySbuServiceImpl")
	private StoreApplySbuService storeApplySbuService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "dTemplatesServiceImpl")
	private DTemplatesService dTemplatesService;
    @Resource(name = "organizationServiceImpl")
    private OrganizationService organizationService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;

	/*
	 * 列表
	 */
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Integer isCheck,
			Long objTypeId, Long objid, ModelMap model,Long menuId) {
		model.addAttribute("isCheck", isCheck);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		model.addAttribute("code", code);
        model.addAttribute("menuId", menuId);
		return CommonUtil.getFolderPrefix(code) + "/member/storeApply/list_tb";
	}

	/*
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Integer isCheck,Long menuId,Long userId,
			Pageable pageable, ModelMap model) {

		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankService.findList(null, fis, null));
		model.addAttribute("isCheck", isCheck);
		List<Integer> types = storeApplyService.getTypes();
		model.addAttribute("types", types);

		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(55L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("code", code);

		//新增客户
		fis.clear();
		fis.add(Filter.eq("code", "CustomerType"));
		fis.add(Filter.eq("isEnabled", true));
		fis.add(Filter.isNotNull("parent"));
		List<SystemDict> customerTypes = systemDictBaseService.findList(null,
				fis,
				null);
		model.addAttribute("customerTypes", customerTypes);
        //获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);

		return CommonUtil.getFolderPrefix(code) + "/member/storeApply/list";
	}

	/*
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String name, String outTradeNo, String alias,
			Integer[] type, Long memberRankId, Long saleOrgId,
			Pageable pageable, ModelMap model) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Page<Map<String, Object>> page = storeApplyService.findPage(name,
				outTradeNo,
				alias,
				type,
				companyInfoId,
				memberRankId,
				saleOrgId,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add(@PathVariable String code, ModelMap model,
					  Pageable pageable) {
		List<Filter> filters = new ArrayList<Filter>();
		List<Integer> types = storeApplyService.getTypes();
		model.addAttribute("types", types);
		model.addAttribute("companyInfoId", WebUtils.getCurrentCompanyInfoId());
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		// 业务类型
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("businessTypes", businessTypes);

		// 经销商状态
		filters.clear();
		filters.add(Filter.eq("code", "distributorStatus"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> distributorStatus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("distributorStatus", distributorStatus);

		//客户类型
		filters.clear();
		filters.add(Filter.eq("code", "CustomerType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> customerTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("customerTypes", customerTypes);

		//等级
		filters.clear();
		filters.add(Filter.eq("code", "Grade"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> grades = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("grades", grades);

		filters.clear();
		filters.add(Filter.eq("code", "sbu"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> sbu = systemDictBaseService.findList(null,
				filters,
				null);
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> s = storeMemberSbuService.findList(null,
				filters,
				null);
		//取当前用户默认的sbu
		String sbuNames = "";
		Long xtch = null;
		List<Map<String, Object>> sbuName = storeMemberService.findSbuTy(storeMember.getId());
		if(sbuName!=null){
			if (sbuName.size() > 0) {
				sbuNames = sbuName.get(0).get("name").toString();
			}
		}
		List<SystemDict> sbus = new ArrayList<SystemDict>();
		for (int i = 0; i < s.size(); i++) {
			for (SystemDict sb : sbu) {
				String a = s.get(i).getSbu().getName();
				String b = sb.getValue();
				if (a.equals(b)) {
					sbus.add(sb);
				}
				if (b.equals(sbuNames)) {
					xtch = sb.getId();
				}
			}

		}
		model.addAttribute("xtch", xtch);
		model.addAttribute("sbus", sbus);
		model.addAttribute("storeMember", storeMember.getMemberType() == 0
				|| storeMember.getIsSalesman() ? storeMember : 0);
		//		System.out.println(storeMember.getMemberType() == 1
		//				|| storeMember.getIsSalesman() ? storeMember : 0);

		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		//filters.add(Filter.eq("isDefault", true));
		Page<MemberRank> page = memberRankService.findPage(filters,
				null,
				pageable);
		if (page != null
				&& page.getContent() != null
				&& page.getContent().size() > 0) {
			model.addAttribute("memberRankList", page.getContent());
		}
		else {
			model.addAttribute("memberRankList", new ArrayList<MemberRank>());
		}
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		model.addAttribute("nowDate", df.format(new Date()));
		model.addAttribute("code", code);

		// 业务类型
		filters.clear();
		filters.add(Filter.eq("code", "SaleStyle"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> saleStyles = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("saleStyles", saleStyles);

		//公司类别
		filters.clear();
		filters.add(Filter.eq("code", "categoryOfCompany"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> categoryOfCompanys = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("categoryOfCompanys", categoryOfCompanys);

		//公司性质
		filters.clear();
		filters.add(Filter.eq("code", "companyNature"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> companyNatures = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("companyNatures", companyNatures);

		//平台性质
		filters.clear();
		filters.add(Filter.eq("code", "SaleOrgType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgTypes", saleOrgTypes);

		//经营组织
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
		List<Organization> organizations = organizationService.findList(null, filters, null);
		List<String> oname = new ArrayList<String>();
		List<Organization> organization = new ArrayList<Organization>();
		for(int i=0;i<organizations.size();i++){
			if(organizations.get(i).getName().contains("大自然家居")||organizations.get(i).getName().contains("广西柏景")){
				organization.add(organizations.get(i));
				oname.add(organizations.get(i).getName());
			}
		}
		model.addAttribute("os", organization);
		model.addAttribute("osName", StringUtils.arrayToDelimitedString(oname.toArray(new String[oname.size()]), ","));

		//客户缴纳保证金
		filters.clear();
		filters.add(Filter.eq("code", "handleAccountCode"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> storeAccount = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("storeAccount", storeAccount);

		//机构区域
		filters.clear();
		filters.add(Filter.eq("code", "saleOrgRegion"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgRegions", saleOrgRegions);

		return CommonUtil.getFolderPrefix(code) + "/member/storeApply/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(StoreApply storeApply, Long storeMemberId, Long saleOrgId,
				   Long memberRankId, Integer store_type, Long salesPlatformId,
				   Long businessTypeId, Long areaId, Long sbuId, Long saleStyleId,
				   Long customerTypeId, Long gradenId, Long categoryOfCompanyId,
				   Long companyNatureId, Long storeApplySalesAreaId,Long distributorStatusId,
				   Long[] sbuIds,Long[] organizationIds) {
		List<Filter> filters = new ArrayList<Filter>();
		if (storeApplyService.exists(Filter.eq("name", storeApply.getName()),
				Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()))
				|| storeService.exists(Filter.eq("name", storeApply.getName()),
				Filter.eq("companyInfoId",
						WebUtils.getCurrentCompanyInfoId()))) {
			// 客户名称已存在
			return error("160001");
		}
		if (storeApply.getHeadNewArea() == null
				|| storeApply.getHeadNewArea().getId() == null) {
			return error("请选择地区!");
		}
		filters.clear();
		filters.add(Filter.eq("parent", storeApply.getHeadNewArea()));
		List<Area> areas = areaBaseService.findList(null, filters, null);
		if(areas.size()>0){
			return error("请选择省市区!");
		}
		if (memberRankId == null){
			return error("请填写价格类型！");
		}
		if(storeApply.getDealerBackground()==null){
			return error("请选择经销商背景!");
		}
		if(storeApply.getPropagandaAwareness()==null){
			return error("请选择宣传意识!");
		}
		if(storeApply.getBrandAwareness()==null){
			return error("请选择品牌意识!");
		}
		if(storeApply.getAfterService()==null){
			return error("请选择售后服务!");
		}
		if(storeApply.getDealerLevel()==null){
			return error("请选择经销商等级!");
		}
		if(storeApply.getPcOrBroadband()==null){
			return error("请选择是否有电脑/宽带!");
		}
		if(storeApply.getDealerBackground() == 3 && ConvertUtil.isEmpty(storeApply.getDealerBackgroundMemo())){
			return error("请填写经销商背景");
		}
		if(ConvertUtil.isEmpty(storeApply.getRegion())){
			return error("区域不能为空！");
		}
		SystemDict sbu = systemDictBaseService.find(sbuId);
		storeApply.setSbu(sbu);
		SystemDict customerType = systemDictBaseService.find(customerTypeId);
		storeApply.setCustomerType(customerType);
		SystemDict graden = systemDictBaseService.find(gradenId);
		String saleType = null;
		if (saleStyleId != null) {
			SystemDict saleStyle = systemDictBaseService.find(saleStyleId);
			storeApply.setSaleStyle(saleStyle);
			saleType = saleStyle.getLowerCode();
		}
		storeApply.setGraden(graden);
		if (storeApply.getOutTradeNo() != null
				&& (storeApplyService.exists(Filter.eq("outTradeNo",
				storeApply.getOutTradeNo()),
				Filter.eq("companyInfoId",
						WebUtils.getCurrentCompanyInfoId())) || storeService.exists(Filter.eq("outTradeNo",
				storeApply.getOutTradeNo()),
				Filter.eq("companyInfoId",
						WebUtils.getCurrentCompanyInfoId())))) {
			// 客户名称已存在
			return error("客户编码已存在");
		}
		if (store_type == null) return error("请选择客户类型");
		StoreApply.Type type = StoreApply.Type.values()[store_type];
		storeApply.setType(type);

		storeApply.setDocStatus(0);
		if (storeApply.getTaxRate() != null) {
			storeApply.setTaxRate(storeApply.getTaxRate()
					.divide(new BigDecimal(100)));
		}
		storeApply.setCategoryOfCompany(systemDictBaseService.find(categoryOfCompanyId));
		storeApply.setCompanyNature(systemDictBaseService.find(companyNatureId));
		if (storeApply.getStoreApplyAddress().size()<1){
			return error("收货地址不能为空！");
		};
		storeApply.setOrganizations(StringUtils.arrayToDelimitedString(organizationIds, ","));
		BigDecimal total = storeApply.getSalesChannelsVal1().add(storeApply.getSalesChannelsVal2()).add(storeApply.getSalesChannelsVal3().add(storeApply.getSalesChannelsVal4())).add(storeApply.getSalesChannelsVal5()).add(storeApply.getSalesChannelsVal6());
		if (total.compareTo(new BigDecimal(100)) != 0) {
			return error("销售渠道百分比总和须为100%");
		}
		if(!MobileAndIdCardUtil.isMobile(storeApply.getHeadPhone())){
			return error("请输入正确的手机号！");
		}
		if(!MobileAndIdCardUtil.isIdCard(storeApply.getIdentity())){
			return error("请输入正确的身份证号！");
		}
		if(storeApply.getStoreApplyAttachs() == null){
			return error("请上传附件！");
		}
		if("地板中心".equals(storeApply.getSbu().getValue())){
			if (organizationIds == null){
				return error("经营组织(多选) 不能为空！");
			}
			Boolean is0 = false;
			Boolean is1 = false;
			Boolean is2 = true;
			is1 = storeApply.getCategory() == 0;
			is0 = storeApply.getRealCautionPaid()!=null&&storeApply.getRealCautionPaid().compareTo(BigDecimal.ZERO)>0;
			List<StoreApplyAttach> saas = storeApply.getStoreApplyAttachs();
			for(StoreApplyAttach sy : saas){
				if(sy.getType() != null&&sy.getType() == 2){
					is1 = false;
				}
				if(sy.getType() != null&&sy.getType() == 0){
					is0 = false;
				}
				if(sy.getType() != null&&sy.getType() == 1){
					is2 = false;
				}
			}
//			if(is1){
//				return error("请上传一城多商附件！");
//			}
//			if(is0){
//				return error("请上传经销商缴纳保证金凭证附件！");
//			}
//			if(is2){
//				return error("请上传经销商加盟申请表附件！");
//			}
		}
		storeApplyService.save(storeApply,
				storeMemberId,
				saleOrgId,
				memberRankId,
				salesPlatformId,
				businessTypeId,
				areaId,
				saleType,
				storeApplySalesAreaId,
				sbuId,
				distributorStatusId);
		return success().addObjX(storeApply.getId());
	}

    /**
     * 客户加盟详情页
     * @param code b
     * @param id 表单id
     * @param isCheck 单点过来默认为null
     */
    @RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
    public String edit(@PathVariable String code, Long id, Integer isCheck,
                       ModelMap model) {

        model.addAttribute("isCheck", isCheck);
        List<Integer> types = storeApplyService.getTypes();
        model.addAttribute("types", types);
        model.addAttribute("companyInfoId", WebUtils.getCurrentCompanyInfoId());

        // new
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("isTop", true));
        List<SaleOrg> saleOrgs = saleOrgService.findList(1, filters, null);
        if (saleOrgs.size() > 0) {
            model.addAttribute("saleOrg", saleOrgs.get(0));
        }
        filters.clear();
        filters.add(Filter.eq("isEnabled", true));
        model.addAttribute("memberRanks",
                memberRankService.findList(null, filters, null));

        StoreApply storeApply = storeApplyService.find(id);
        model.addAttribute("store", storeApply);
        Integer ShopInfoCount = storeApplyService.countShopInfo(id);
        model.addAttribute("ShopInfoCount", ShopInfoCount);

        // 获取联系人
        List<Map<String, Object>> storecontracts = storeApplyService.findStoreContract(storeApply);
        model.addAttribute("storecontracts", JsonUtils.toJson(storecontracts));

        //合作单位
        List<Map<String, Object>> storeApplyCooperations = storeApplyService.findStoreApplyCooperation(storeApply);
        model.addAttribute("storeApplyCooperations",
                JsonUtils.toJson(storeApplyCooperations));
        filters.clear();
        filters.add(Filter.eq("storeApply", storeApply));
        Pageable pageable = new Pageable();
        Page<Map<String, Object>> storeAddressList = storeApplyService.findStoreApplyAddressPage(null,
                null,
                id,
                null,
                pageable);
        model.addAttribute("storeAddressList",
                JsonUtils.toJson(storeAddressList.getContent()));
        model.addAttribute("storeAddressSize",
                storeAddressList.getContent() == null ? "0"
                        : storeAddressList.getContent().size());

        List<Map<String, Object>> storeInvoiceInfoList = storeApplyService.findInvoiceInfoListByStoreApply(storeApply.getId());
        model.addAttribute("storeInvoiceInfoList",
                JsonUtils.toJson(storeInvoiceInfoList));

        // 获取经营品类
        List<Map<String, Object>> productType = storeApplyService.findBusinessCategoryApply(storeApply);
        model.addAttribute("productType", JsonUtils.toJson(productType));

        // 获取经营记录
        List<Map<String, Object>> businessRecord = storeApplyService.findBusinessRecordApply(storeApply);
        model.addAttribute("businessRecord", JsonUtils.toJson(businessRecord));

        // 获取保证金
        List<Map<String, Object>> cautionMoney = storeApplyService.findCautionMoneyApply(storeApply);
        model.addAttribute("cautionMoney", JsonUtils.toJson(cautionMoney));

        boolean isCheckWf = wfObjConfigBaseService.isCheckWf(55L);
        model.addAttribute("isCheckWf", isCheckWf);
        model.addAttribute("wfStates", wfBaseService.getAllWfStates());

        //客户附件
        List<Map<String, Object>> storeAttachs = storeApplyService.findListByOrderId(id, 0);
        model.addAttribute("store_attach0", JsonUtils.toJson(storeAttachs));
        List<Map<String, Object>> storeAttachs1 = storeApplyService.findListByOrderId(id, 1);
        model.addAttribute("store_attach1", JsonUtils.toJson(storeAttachs1));
        List<Map<String, Object>> storeAttachs2 = storeApplyService.findListByOrderId(id, 2);
        model.addAttribute("store_attach2", JsonUtils.toJson(storeAttachs2));

        // 业务类型
        filters.clear();
        filters.add(Filter.eq("code", "businessType"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessTypes = systemDictService.findList(null,
                filters,
                null);
        model.addAttribute("businessTypes", businessTypes);

        // 经销商状态
        filters.clear();
        filters.add(Filter.eq("code", "distributorStatus"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> distributorStatus = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("distributorStatus", distributorStatus);

        //客户类型
        filters.clear();
        filters.add(Filter.eq("code", "CustomerType"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> customerTypes = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("customerTypes", customerTypes);

        //等级
        filters.clear();
        filters.add(Filter.eq("code", "Grade"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> grades = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("grades", grades);

        filters.clear();
        filters.add(Filter.eq("code", "sbu"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> sbu = systemDictBaseService.findList(null,
                filters,
                null);
        StoreMember storeMember = storeMemberService.getCurrent();
        filters.clear();
        filters.add(Filter.eq("storeMember", storeMember));
        List<StoreMemberSbu> s = storeMemberSbuService.findList(null,
                filters,
                null);
        List<SystemDict> sbus = new ArrayList<SystemDict>();
        for (int i = 0; i < s.size(); i++) {
            for (SystemDict sb : sbu) {
                String a = s.get(i).getSbu().getName();
                String b = sb.getValue();
                if (a.equals(b)) {
                    sbus.add(sb);
                }
            }
        }
        model.addAttribute("sbus", sbus);

        model.addAttribute("code", code);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        model.addAttribute("nowDate", df.format(new Date()));

        filters.clear();
        filters.add(Filter.eq("code", "SaleStyle"));
        filters.add(Filter.isNotNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        List<SystemDict> saleStyles = systemDictService.findList(null,
                filters,
                null);
        model.addAttribute("saleStyles", saleStyles);

        //公司类别
        filters.clear();
        filters.add(Filter.eq("code", "categoryOfCompany"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> categoryOfCompanys = systemDictService.findList(null,
                filters,
                null);
        model.addAttribute("categoryOfCompanys", categoryOfCompanys);

        //公司性质
        filters.clear();
        filters.add(Filter.eq("code", "companyNature"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> companyNatures = systemDictService.findList(null,
                filters,
                null);
        model.addAttribute("companyNatures", companyNatures);

        //sbu
        List<Map<String, Object>> sbuList = storeApplySbuService.findStoreApplySbu(id);
        model.addAttribute("sbu_json", JsonUtils.toJson(sbuList));

        //平台性质
        filters.clear();
        filters.add(Filter.eq("code", "SaleOrgType"));
        filters.add(Filter.isNotNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("saleOrgTypes", saleOrgTypes);

        //经营组织多选
        List<Organization> os = new ArrayList<Organization>();
        if(storeApply.getOrganizations()!=null){
            for(String osId : storeApply.getOrganizations().split(",")){
                os.add(organizationService.find(Long.parseLong(osId)));
            }
            model.addAttribute("os", os);
        }

        //客户缴纳保证金
        filters.clear();
        filters.add(Filter.eq("code", "handleAccountCode"));
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> storeAccount = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("storeAccount", storeAccount);
        // 获取流程节点
        if (storeApply.getWfId() != null) {
            //获取到当前流程
            ActWf wf = storeApplyService.getWfByWfId(storeApply.getWfId());

            if (wf != null) {
                //省长审核
                Boolean szsh = false;
                //渠道专员
                Boolean qdzy = false;
                //省长审核权限
                Boolean szshs = false;
                //渠道专员权限
                Boolean qdzys = false;
                //跟单
                Boolean gd = false;
                Boolean gds = false;
                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for(Map<String, Object> c : item){
                    if(c.get("suggestion")!=null){
                        //处理结果
                        String approved = c.get("approved")!=null?c.get("approved").toString():"false";
                        //节点名称
                        String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
                        //对比节点名称是否对应
                        if(rwm.contains("省长")){
                            szsh = Boolean.valueOf(approved);
                        }
                        if(rwm.contains("渠道")){
                            qdzy = Boolean.valueOf(approved);
                            if(!qdzy){
                                szsh = false;
                            }
                        }
                        if(rwm.contains("跟单")){
                            gd = Boolean.valueOf(approved);
                            if(!gd){
                                qdzy = false;
                            }
                        }
                        if(rwm.contains("销售支持部总监")){
                            if(!Boolean.valueOf(approved)){
                                gd = false;
                            }
                        }
                    }
                }
                //获取当前流程所在的节点
                Task t = storeApplyService.getCurrTaskByWf(wf);
                if(t!=null){
                    //获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
                        szshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道")){
                        szshs = true;qdzys = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("跟单")){
                        szshs = true;qdzys = true;gds = true;
                    }
                }
                model.addAttribute("node", t);
                model.addAttribute("szsh", szsh);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzys", qdzys);
                model.addAttribute("gd", gd);
                model.addAttribute("gds", gds);
            }
        }
        //机构区域
        filters.clear();
        filters.add(Filter.eq("code", "saleOrgRegion"));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,
                filters,
                null);
        model.addAttribute("saleOrgRegions", saleOrgRegions);
        return CommonUtil.getFolderPrefix(code) + "/member/storeApply/edit";
    }

	/**
	@RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
	public String edit(@PathVariable String code, Long id, Integer isCheck,
			ModelMap model) {
		model.addAttribute("isCheck", isCheck);
		List<Integer> types = storeApplyService.getTypes();
		model.addAttribute("types", types);
		model.addAttribute("companyInfoId", WebUtils.getCurrentCompanyInfoId());

		// new
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isTop", true));
		List<SaleOrg> saleOrgs = saleOrgService.findList(1, filters, null);
		if (saleOrgs.size() > 0) {
			model.addAttribute("saleOrg", saleOrgs.get(0));
		}
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankService.findList(null, filters, null));

		StoreApply storeApply = storeApplyService.find(id);
		model.addAttribute("store", storeApply);
		Integer ShopInfoCount = storeApplyService.countShopInfo(id);
		model.addAttribute("ShopInfoCount", ShopInfoCount);

		// 获取联系人
		List<Map<String, Object>> storecontracts = storeApplyService.findStoreContract(storeApply);
		model.addAttribute("storecontracts", JsonUtils.toJson(storecontracts));

		//合作单位
		List<Map<String, Object>> storeApplyCooperations = storeApplyService.findStoreApplyCooperation(storeApply);
		model.addAttribute("storeApplyCooperations",
				JsonUtils.toJson(storeApplyCooperations));
		filters.clear();
		filters.add(Filter.eq("storeApply", storeApply));
		Pageable pageable = new Pageable();
		Page<Map<String, Object>> storeAddressList = storeApplyService.findStoreApplyAddressPage(null,
				null,
				id,
				null,
				pageable);
		model.addAttribute("storeAddressList",
				JsonUtils.toJson(storeAddressList.getContent()));
		model.addAttribute("storeAddressSize",
				storeAddressList.getContent() == null ? "0"
						: storeAddressList.getContent().size());

		List<Map<String, Object>> storeInvoiceInfoList = storeApplyService.findInvoiceInfoListByStoreApply(storeApply.getId());
		model.addAttribute("storeInvoiceInfoList",
				JsonUtils.toJson(storeInvoiceInfoList));

		// 获取经营品类
		List<Map<String, Object>> productType = storeApplyService.findBusinessCategoryApply(storeApply);
		model.addAttribute("productType", JsonUtils.toJson(productType));

		// 获取经营记录
		List<Map<String, Object>> businessRecord = storeApplyService.findBusinessRecordApply(storeApply);
		model.addAttribute("businessRecord", JsonUtils.toJson(businessRecord));

		// 获取保证金
		List<Map<String, Object>> cautionMoney = storeApplyService.findCautionMoneyApply(storeApply);
		model.addAttribute("cautionMoney", JsonUtils.toJson(cautionMoney));

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(55L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		//订单附件 
		String StoreApplyAttach_json = JsonUtils.toJson(storeApplyService.findListByOrderId(id));
		model.addAttribute("StoreApplyAttach_json", StoreApplyAttach_json);

		// 业务类型
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.eq("isEnabled", true));               
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("businessTypes", businessTypes);
		
		// 经销商状态
		filters.clear();
		filters.add(Filter.eq("code", "distributorStatus"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> distributorStatus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("distributorStatus", distributorStatus);

		//客户类型
		filters.clear();
		filters.add(Filter.eq("code", "CustomerType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> customerTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("customerTypes", customerTypes);

		//等级
		filters.clear();
		filters.add(Filter.eq("code", "Grade"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> grades = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("grades", grades);

		filters.clear();
		filters.add(Filter.eq("code", "sbu"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> sbu = systemDictBaseService.findList(null,
				filters,
				null);
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> s = storeMemberSbuService.findList(null,
				filters,
				null);
		//取当前用户默认的sbu
		String sbuNames = "";
		Long xtch = null;
		List<Map<String, Object>> sbuName = storeMemberService.findSbuTy(storeMember.getId());
		System.out.println("sbu长度："+sbu.size());
		System.out.println("sbuname长度："+sbuName.size());
		if (sbu.size() > 0&&sbuName.size()>0) {
			sbuNames = sbuName.get(0).get("name").toString();
		}
		List<SystemDict> sbus = new ArrayList<SystemDict>();
		for (int i = 0; i < s.size(); i++) {
			for (SystemDict sb : sbu) {
				String a = s.get(i).getSbu().getName();
				String b = sb.getValue();
				if (a.equals(b)) {
					sbus.add(sb);
				}
				if (b.equals(sbuNames)) {
					xtch = sb.getId();
				}
			}
		}
		model.addAttribute("sbus", sbus);

		model.addAttribute("code", code);
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		model.addAttribute("nowDate", df.format(new Date()));

		filters.clear();
		filters.add(Filter.eq("code", "SaleStyle"));
		filters.add(Filter.isNotNull("parent"));
		filters.add(Filter.eq("isEnabled", true));
		List<SystemDict> saleStyles = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("saleStyles", saleStyles);

		//公司类别
		filters.clear();
		filters.add(Filter.eq("code", "categoryOfCompany"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> categoryOfCompanys = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("categoryOfCompanys", categoryOfCompanys);

		//公司性质
		filters.clear();
		filters.add(Filter.eq("code", "companyNature"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> companyNatures = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("companyNatures", companyNatures);

		//sbu
		List<Map<String, Object>> sbuList = storeApplySbuService.findStoreApplySbu(id);
		model.addAttribute("sbu_json", JsonUtils.toJson(sbuList));

		//平台性质
		filters.clear();
		filters.add(Filter.eq("code", "SaleOrgType"));
		filters.add(Filter.isNotNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgTypes", saleOrgTypes);
		// 获取流程节点
		if (storeApply.getWfId() != null) {
			//获取到当前流程
			ActWf wf = storeApplyService.getWfByWfId(Long.valueOf(storeApply.getWfId()));
			
			if (wf != null) {
				//省长审核
				Boolean szsh = false;
				//渠道专员
				Boolean qdzy = false;
				//省长审核权限
				Boolean szshs = false;
				//渠道专员权限
				Boolean qdzys = false;
				// 查找当前流程明细
				List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
				for(Map<String, Object> c : item){
					if(c.get("suggestion")!=null){
						//处理结果
						String approved = c.get("approved")!=null?c.get("approved").toString():"false";
						//节点名称
						String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
						//对比节点名称是否对应
						if(rwm.contains("省长")){
							szsh = Boolean.valueOf(approved);
						}
						if(rwm.contains("渠道")){
							qdzy = Boolean.valueOf(approved);
							if(!qdzy){
								szsh = false;
							}
						}
					}
				}
				//获取当前流程所在的节点
				Task t = storeApplyService.getCurrTaskByWf(wf);
				if(t!=null){
					//获取当前节点所有用户id
					List<String> userId = actWfService.getTaskUsers(t.getId());
					if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
						szshs = true;
					}
					if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道")){
						szshs = true;qdzys = true;
					}
				}
				model.addAttribute("node", t);
				model.addAttribute("szsh", szsh);
				model.addAttribute("szsh", szsh);
				model.addAttribute("qdzy", qdzy);
				model.addAttribute("szshs", szshs);
				model.addAttribute("qdzys", qdzys);
			}
		}
		return CommonUtil.getFolderPrefix(code) + "/member/storeApply/edit";
	}**/

	/*
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(StoreApply storeApply, Long storeMemberId, Long areaId,
			Long memberRankId, Long saleOrgId, BankCard bankCard, Long bAreaId,
			String bAddress, Boolean bIsEnabled, String bMobile, Long gradenId,
			Long salesPlatformId, Long businessTypeId, Long changeMemberRankId,
			Long sbuId, Long customerTypeId, Long saleStyleId,
			Long categoryOfCompanyId, Long companyNatureId,
			Long storeApplySalesAreaId, Long createById,Long distributorStatusId) {
		if (storeApplyService.exists(Filter.eq("name", storeApply.getName()),
				Filter.ne("id", storeApply.getId()),
				Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()))) {
			// 客户名称已存在
			return error("160001");
		}
		if (storeApply.getHeadNewArea() == null
				|| storeApply.getHeadNewArea().getId() == null) {
			return error("请选择地区!");
		}
		if (memberRankId == null){
			return error("请填写价格类型！");
		}
		if(storeApply.getDealerBackground()==null){
			return error("请选择经销商背景!");
		}
		if(storeApply.getPropagandaAwareness()==null){
			return error("请选择宣传意识!");
		}
		if(storeApply.getBrandAwareness()==null){
			return error("请选择品牌意识!");
		}
		if(storeApply.getAfterService()==null){
			return error("请选择售后服务!");
		}
		if(storeApply.getDealerLevel()==null){
			return error("请选择经销商等级!");
		}
		if(storeApply.getPcOrBroadband()==null){
			return error("请选择是否有电脑/宽带!");
		}
		if (storeApply.getTaxRate() != null) {
			storeApply.setTaxRate(storeApply.getTaxRate()
					.divide(new BigDecimal(100)));
		}
		/*
		 * if (sbuId != null) { SystemDict sbu =
		 * systemDictBaseService.find(sbuId); if(sbu != null){
		 * storeApply.setSbu(sbu); }else{ return error("sbu不存在"); } }else{
		 * return error("请选择sbu"); }
		 */

		SystemDict customerType = systemDictBaseService.find(customerTypeId);
		storeApply.setCustomerType(customerType);

		SystemDict graden = systemDictBaseService.find(gradenId);
		storeApply.setGraden(graden);

		SystemDict saleStyle = systemDictBaseService.find(saleStyleId);
		storeApply.setSaleStyle(saleStyle);
		
        if (storeApply.getStoreApplyAddress().size()<1){
        	return error("收货地址不能为空！");
        };
		storeApply.setType(Type.distributor);
		SystemDict sbu = systemDictBaseService.find(sbuId);
		storeApply.setSbu(sbu);
		storeApply.setCategoryOfCompany(systemDictBaseService.find(categoryOfCompanyId));
		storeApply.setCompanyNature(systemDictBaseService.find(companyNatureId));
		storeApplyService.update(storeApply,
				storeMemberId,
				areaId,
				saleOrgId,
				memberRankId,
				salesPlatformId,
				businessTypeId,
				bankCard,
				bAreaId,
				bAddress,
				bMobile,
				bIsEnabled,
				changeMemberRankId,
				sbuId,
				storeApplySalesAreaId,
				createById,
				distributorStatusId);
		return success();
	}

	/*
	 * Excel导入
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file, Integer type,
			HttpServletResponse response, ModelMap model) {
		try {
			if (type == 0) {
				storeApplyService.storeImport(file);
			}
			else if (type == 1) {
				storeApplyService.storeImport1(file);
			}

			return ResultMsg.success();
		}
		catch (Exception e) {
			LogUtils.error("导入客户", e);
			return ResultMsg.error(e.getMessage());
		}
	}

	/*
	 * 审批
	 */
	@RequestMapping(value = "/approval", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg approval(Long id) {
		storeApplyService.createStore(id);
		return success();
	}

	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check_wf(Long id, String modelId, Long objTypeId) {

		StoreApply storeApply = storeApplyService.find(id);
		// Status status = depositRecharge.getStatus();
		if (storeApply.getDocStatus() != 0) {
			// if (status.equals(Status.success) ||
			// status.equals(Status.failure)) {
			// 申请单已审核通过或驳回
			return error("只有单据状态为已保存的申请单才能审批流程");
		}
		storeApplyService.createStoreWf(id,modelId,objTypeId);
		return success();
	}
	
	
	
	/**
	 * 流程节点保存
	 */
	@RequestMapping(value = "/saveform", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveform(StoreApply storeApply, Integer save_type) {
		// type 用来定义节点
		// type 1区域经理 2渠道部
		storeApplyService.saveform(storeApply, save_type);
		return success().addObjX(storeApply.getId());
	}

}
