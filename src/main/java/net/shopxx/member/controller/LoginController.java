/*
 * Copyright 2005-2013 shopxx.net. All rights reserved. Support:
 * http://www.shopxx.net License: http://www.shopxx.net/license
 */
package net.shopxx.member.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.shopxx.base.core.util.*;
import net.shopxx.basic.service.SystemParameterBaseService;
import net.shopxx.member.service.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.sf.ehcache.CacheManager;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.entity.StoreMember;

/**
 * Controller - 会员登录
 */
@Controller("loginController")
@RequestMapping("/login")
public class LoginController extends BaseController {

	@Resource(name = "memberBaseServiceImpl")
	private MemberBaseService memberBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "loginOutBaseServiceImpl")
	private LoginOutBaseService loginOutBaseService;
	@Resource(name = "ehCacheManager")
	private CacheManager cacheManager;
	@Resource(name = "pwdModificationRecordServiceImpl")
	private PwdModificationRecordService pwdModificationRecordService;
	@Autowired
	private SystemParameterBaseService systemParameterBaseService;

	/**
	 * 登录页面
	 * @throws Exception 
	 */
	@RequestMapping(method = RequestMethod.GET)
	public String index(String language, String redirectUrl, String company,
			String loginguid, HttpServletRequest request,
			HttpServletResponse response, ModelMap model) throws Exception {
		language = StringUtils.isEmpty(language) ? "ChinaCN" : language;
		System.out.println("loginguid:"+loginguid);
		//单点登录
		if (!ConvertUtil.isEmpty(loginguid)) {
			String msg = loginOutBaseService.SSO(company,
					loginguid,
					language,
					request);
			if (!msg.equalsIgnoreCase("success")) {
//				return errorV(model, error(msg));
				LogUtils.error("单点登录：" + msg);
			}
			else {
				response.sendRedirect("/member/index.jhtml");
			}
		}
		String url = "";
		if (language.equalsIgnoreCase("ChinaCN")) {

			CompanyInfo companyInfo = findCompanyInfo(request);
			if (companyInfo != null) {
				company = companyInfo.getSimple_name();
				String company_code = companyInfo.getCompany_code();
				url = "/login/" + company_code;
			}
			else {
				url = "/login";
			}
		}
		else if (language.equalsIgnoreCase("ChinaTW")) {
			url = "/login";
		}
		else if (language.equalsIgnoreCase("English")) {
			url = "/login";
		}
		else if (language.equalsIgnoreCase("Japanese")) {
			url = "/login";
		}
		else if (language.equalsIgnoreCase("Korean")) {
			url = "/login";
		}
		model.addAttribute("redirectUrl", redirectUrl);
		System.out.println("redirectUrl:"+redirectUrl);
		model.addAttribute("captchaId", UUID.randomUUID().toString());
		model.addAttribute("company", company);
		Setting setting = SettingUtils.get();
		String siteUrl = setting.getSiteUrl();
		model.addAttribute("siteUrl", siteUrl);
		return url;
	}

	/**
	 * 登录提交
	 * @throws Exception 
	 */
	@RequestMapping(value = "/submitCheck", method = RequestMethod.GET)
	public 
	String submitCheck(Pageable pageable,HttpServletRequest request,
			HttpServletResponse response, HttpSession session, ModelMap model)
			 {
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		List<StoreMember> sotreMembers =loginOutBaseService.findStoreMember(storeMember.getIdCard());
		model.addAttribute("storeMembers", sotreMembers);
	
		if(sotreMembers.size()>0 && sotreMembers.size()>1){
			return "/member/store_member/member_logins";
		}else{
			String language =  "ChinaCN" ;
			ExceptionUtil.throwServiceException(ConvertUtil.convertI18nMsg("请维护身份证信息",
					language));
			return "login";
		}
		
	}
	
	/**
	 * 登录提交
	 * @throws Exception 
	 */
	@RequestMapping(value = "/submit", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg submit(String username, String password, String companyName,
			String language, HttpServletRequest request,
			HttpServletResponse response, HttpSession session, ModelMap model)
			throws Exception {

//		if (ConvertUtil.isEmpty(companyName)) {
//			CompanyInfo companyInfo = findCompanyInfo(request);
//			if (companyInfo != null) {
//				companyName = companyInfo.getSimple_name(); 
//			}
//		}
		Integer flag=0;
		loginOutBaseService.submitLogin(username,
				password,
				companyName,
				language,
				request,
				response,
				session,
				flag);
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		List<StoreMember> sotreMembers =loginOutBaseService.findStoreMember(storeMember.getIdCard());
		String url = null;
		//判断是否需要修改密码
        //获取系统参数
		Long currentCompanyInfoId = WebUtils.getCurrentCompanyInfoId();
		Integer flagNum = Integer.valueOf(systemParameterBaseService.getValue(currentCompanyInfoId, "isPasswordControlEnabled"));
		Integer count = pwdModificationRecordService.getPwdModificationCount(storeMember.getId());
		if ((storeMember.getIsNeedToChangePwd()!=null && storeMember.getIsNeedToChangePwd())||(flagNum==1 && count==0)){
			//清空cookie及缓存
			//loginOutBaseService.logout(request, response);
            //跳转到修改页面
			url="/pwdModRecord/change_pwd.jhtml?storeMemberId="+storeMember.getId();
		}else {
			if(sotreMembers.size()==1 && sotreMembers.get(0).getIdCard()!=null){
				url="/member/index.jhtml";
			}else if(sotreMembers.size()>0 && sotreMembers.size()!=1){
				url="/login/submitCheck.jhtml";
			}else if(sotreMembers.size()==0){
				url="/member/store_member/edit.jhtml?flag=2&id="+storeMember.getId()+"";
			}
		}
		return success().addObjX(url);
	}




	private CompanyInfo findCompanyInfo(HttpServletRequest request) {
		CompanyInfo companyInfo = null;
		String webSite = "";
		StringBuffer sb = request.getRequestURL();
		Matcher m = Pattern.compile("^http://[^/]+").matcher(sb);
		while (m.find()) {
			webSite = m.group();
		}
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("webSite", webSite.trim()));
		List<CompanyInfo> companyInfos = companyInfoBaseService.findList(2,
				filters,
				null);
		if (companyInfos.size() == 1) {
			companyInfo = companyInfos.get(0);
		}
		return companyInfo;
	}
}