package net.shopxx.member.controller;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.Adjustment;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.AdjustmentService;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.wf.service.WfBaseService;
@Controller("adjustmentController")
@RequestMapping("/member/adjustment")
public class AdjustmentController extends BaseController {
	
	@Resource(name = "adjustmentServiceImpl") 
	private AdjustmentService adjustmentService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;
	
	
	
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model,Integer flag,Long menuId) {
		model.addAttribute("flag", flag);
        model.addAttribute("menuId", menuId);
		return "/member/adjustment/list_tb";
	}
	
	
	
	/**
	 * 系统调账列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(ModelMap model,Long userId,Long menuId) {
        model.addAttribute("menuId",menuId);
        //获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/adjustment/list";
	}
	
	/**
	 * 系统调账单列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn, Long[] states, Long[] wfStates, String sourceSn,
			Long[] sourceStoreId, Long[] adjustmentStoreId, Long[] sourceSbuId, Long[] adjustmentSbuId,
			Long[] sourceOrganizationId, Long[] adjustmentOrganizationId, String adjustmentSn, Long[] sourceSaleOrgId,
			Long[] adjustmentSaleOrgId,Pageable pageable) {

		Page<Map<String, Object>> page = adjustmentService.findPage(sn, states, wfStates, sourceSn, sourceStoreId,
				adjustmentStoreId, sourceSbuId, adjustmentSbuId, sourceOrganizationId, adjustmentOrganizationId,
				adjustmentSn, sourceSaleOrgId, adjustmentSaleOrgId, null, pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}
	
	
	public ModelAndView getModelAndView(List<Map<String, Object>> data, ModelMap model) {
		if(!ConvertUtil.isEmpty(data)){
			for (Map<String, Object> map : data) {
				//单据状态
				if (!ConvertUtil.isEmpty(map.get("status"))) {
					Integer status = (Integer)map.get("status");
					if (status == 0){
						 map.put("status", "已保存");
					}else if (status == 1){
						 map.put("status", "审核中");
					}else if (status == 2){
						 map.put("status", "已审核");
					}else if (status == 3){
						 map.put("status", "作废");
					}
				}
				//流程状态
				if (!ConvertUtil.isEmpty(map.get("wf_state"))) {
					Integer wfState = (Integer)map.get("wf_state");
					if (wfState == 0){
						 map.put("wf_state", "未启动");
					}else if (wfState == 1){
						 map.put("wf_state", "审核中");
					}else if (wfState == 2){
						 map.put("wf_state", "已完成");
					}else if (wfState == 3){
						 map.put("wf_state", "驳回");
					}
				}
			}
		}
		
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "单据编号", "单据状态", "流程状态", "创建人", "创建时间", "来源单据编号", "来源机构",
		 		"来源客户编码", "来源客户","来源客户简称","申请调款金额","来源SBU", "来源账户", "来源收款银行", "来源银行代码",
		 		"来源经营组织", "调款单编号", "调款机构", "调款客户编码", "调款客户","调款客户简称", "实际调款金额","调款SBU",
		 		"调款账户", "调款收款银行", "调款银行代码", "调款经营组织", "调款备注"};
		// 设置单元格宽度
		Integer[] widths = { 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 ,
				25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 ,
				25 * 256 , 25 * 256 ,25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 , 25 * 256 ,
				25 * 256 , 25 * 256 , 25 * 256, 25 * 256, 25 * 256 , 25 * 256 , 25 * 256,
				25 * 256};
		// 设置单元格取值
		String[] properties = { "sn", "status", "wf_state", "b_creater", "create_date", "sourceSn",
		 "sourceSaleOrgName","sourceOutTradeNo", "sourceStoreName", "sourceStoreAlias", "amount" ,
		 "sourceSbuName" , "sourceBankCardNo" , "sourceBankName" , "sourceBankCode" , "sourceOrganizationName" ,
		 "adjustmentSn" , "adjustmentSaleOrgName", "adjustmentOutTradeNo","adjustmentStoreName" , "adjustmentStoreAlias" ,
		 "actual_amount", "adjustmentSbuName" , "adjustmentBankCardNo", "adjustmentBankName", "adjustmentBankCode",
		 "adjustmentOrganizationName", "memo" };
		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}
	
	
	/**
	 * 系统调账选择导出
	 * @param ids
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model,Pageable pageable) {
		
		Page<Map<String, Object>> page = adjustmentService.findPage(null, null, null, null, null,
				null, null, null, null, null,null, null, null, ids, pageable);
		
		List<Map<String, Object>> data = page.getContent();
		
		return getModelAndView(data, model);
	}
	
	
	
	
	/**
	 * 系统调账条件导出统计数量
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(String sn, Long[] states, Long[] wfStates,
			String sourceSn,Long[] sourceStoreId, Long[] adjustmentStoreId, Long[] sourceSbuId, Long[] adjustmentSbuId,
			Long[] sourceOrganizationId, Long[] adjustmentOrganizationId, String adjustmentSn, Long[] sourceSaleOrgId,
			Long[] adjustmentSaleOrgId,Pageable pageable) {
		
		Page<Map<String, Object>> page = adjustmentService.findPage(sn, states, wfStates, sourceSn, sourceStoreId,
				adjustmentStoreId, sourceSbuId, adjustmentSbuId, sourceOrganizationId, adjustmentOrganizationId,
				adjustmentSn, sourceSaleOrgId, adjustmentSaleOrgId, null, pageable);
		
		Integer size = (int) page.getTotal(); 
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	
	/**
	 * 生产单条件导出
	 * @param sn
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, Long[] states, Long[] wfStates,
			String sourceSn,Long[] sourceStoreId, Long[] adjustmentStoreId, Long[] sourceSbuId, Long[] adjustmentSbuId,
			Long[] sourceOrganizationId, Long[] adjustmentOrganizationId, String adjustmentSn, Long[] sourceSaleOrgId,
			Long[] adjustmentSaleOrgId,Pageable pageable, ModelMap model,Integer page){
		
		Map<String, Integer> segments = getSegment();
		//每页显示多少条
		pageable.setPageSize(segments.get("size"));
		//当前页码
		pageable.setPageNumber(page);
		Page<Map<String, Object>> pageMap = adjustmentService.findPage(sn, states, wfStates, sourceSn, sourceStoreId,
				adjustmentStoreId, sourceSbuId, adjustmentSbuId, sourceOrganizationId, adjustmentOrganizationId,
				adjustmentSn, sourceSaleOrgId, adjustmentSaleOrgId, null, pageable);
		
		List<Map<String, Object>> data = pageMap.getContent();
		
		return getModelAndView(data, model);
		
	}
	
	
	/**
	 * 系统调账添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long sbuId, ModelMap model) {
		//设置日期格式
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("nowDate", df.format(new Date()));
		
		return "/member/adjustment/add";
	}
	
	
	/*
	 * 系统调账保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveAdjustment(Adjustment adjustment,
			Long sourceStoreId,BigDecimal adjustmentAmount,Long sourceSbuId,
			Long sourceBankCardId,Long sourceOrganizationId,Long adjustmentStoreId,
			BigDecimal adjustmentActualAmount,Long adjustmentSbuId,Long adjustmentBankCardId,
			Long sourceSaleOrgId,Long adjustmentSaleOrgId,Long adjustmentOrganizationId) {
		
		adjustmentService.saveAdjustment(adjustment, sourceStoreId, adjustmentAmount, sourceSbuId,
				sourceBankCardId, sourceOrganizationId, adjustmentStoreId, adjustmentActualAmount,
				adjustmentSbuId, adjustmentBankCardId, sourceSaleOrgId, adjustmentSaleOrgId, adjustmentOrganizationId);
		
		return success().addObjX(adjustment.getId());
	}
	
	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, Integer flag, Integer type, Long objTypeId,
			ModelMap model) {
		
		Adjustment adjustment = adjustmentService.find(id);
		model.addAttribute("dr", adjustment);
		
		
		/** 附件 */
		String adjustmentAttach_json = JsonUtils.toJson(adjustmentService.findListByAdjustmentId(adjustment.getId()));
		model.addAttribute("adjustmentAttach_json", adjustmentAttach_json);
		/** 全链路 */
		String fullLink_json = JsonUtils.toJson(orderFullLinkService.findListByElseSnAndType(adjustment.getSn(),5));
		model.addAttribute("fullLink_json", fullLink_json);
		
		return "/member/adjustment/edit";
	}
	
	
	/*
	 * 系统调账更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg updateAdjustment(Adjustment adjustment,
			Long sourceStoreId,BigDecimal adjustmentAmount,Long sourceSbuId,
			Long sourceBankCardId,Long sourceOrganizationId,Long adjustmentStoreId,
			BigDecimal adjustmentActualAmount,Long adjustmentSbuId,Long adjustmentBankCardId,
			Long sourceSaleOrgId,Long adjustmentSaleOrgId,Long adjustmentOrganizationId) {
		
		adjustmentService.updateAdjustment(adjustment, sourceStoreId, adjustmentAmount, sourceSbuId,
				sourceBankCardId, sourceOrganizationId, adjustmentStoreId, adjustmentActualAmount,
				adjustmentSbuId, adjustmentBankCardId, sourceSaleOrgId, adjustmentSaleOrgId, adjustmentOrganizationId);
		
		return success().addObjX(adjustment.getId());
	}
	
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
		Adjustment adjustment = adjustmentService.find(id);
		if(ConvertUtil.isEmpty(adjustment)){
			return error("该调账单不存在");
		}
		if(adjustment.getStatus() != 0){
			return error("只有单据状态为已保存的调账单才能审批流程");
		}
		adjustmentService.createWf(adjustment, modelId, objTypeId);
		return success();
	}
	
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg cancel(Long id) {

		Adjustment adjustment = adjustmentService.find(id);
		if(ConvertUtil.isEmpty(adjustment)){
			 return error("该调账单不存在");
		}
		if(adjustment.getStatus() != 0){
			 return error("只有单据状态为已保存的调账单才能作废");
		}
		adjustmentService.cancel(adjustment);
		
		return success();
	}
	
		
}
