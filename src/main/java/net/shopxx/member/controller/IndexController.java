package net.shopxx.member.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Order;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.dao.impl.NativeDao;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.homepage.entity.QuickLink;
import net.shopxx.homepage.entity.Statistics;
import net.shopxx.homepage.entity.StatisticsCategory;
import net.shopxx.homepage.entity.SystemNotice;
import net.shopxx.homepage.service.HotSalesProductService;
import net.shopxx.homepage.service.QuickLinkService;
import net.shopxx.homepage.service.StatisticsCategoryService;
import net.shopxx.homepage.service.StatisticsService;
import net.shopxx.homepage.service.SystemNoticeService;
import net.shopxx.member.dao.RoleVisualReportDao;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.PcMenu;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSaleOrgPost;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.entity.VisualReport;
import net.shopxx.member.service.LoginOutBaseService;
import net.shopxx.member.service.PcMenuBaseService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.VisualReportService;
import net.shopxx.mobile.service.AppImageService;
import net.shopxx.mobile.service.ShowProductService;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.wf.service.WfBaseService;

/*
 * Controller - 共用
 */
@Controller("memberIndexController")
@RequestMapping("/member/index")
public class IndexController extends BaseController {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "pcMenuBaseServiceImpl")
	private PcMenuBaseService pcMenuBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "productCategoryBaseServiceImpl")
	private ProductCategoryBaseService productCategoryBaseService;
	@Resource(name = "hotSalesProductServiceImpl")
	private HotSalesProductService hotSalesProductService;
	@Resource(name = "quickLinkServiceImpl")
	private QuickLinkService quickLinkService;
	@Resource(name = "systemNoticeServiceImpl")
	private SystemNoticeService systemNoticeService;
	@Resource(name = "statisticsCategoryServiceImpl")
	private StatisticsCategoryService statisticsCategoryService;
	@Resource(name = "statisticsServiceImpl")
	private StatisticsService statisticsService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "storeMemberSaleOrgPostServiceImpl")
	private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "appImageServiceImpl")
	private AppImageService appImageService;
	@Resource(name = "showProductServiceImpl")
	private ShowProductService showProductService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleVisualReportDao")
	private RoleVisualReportDao roleVisualReportDao;
	@Resource(name = "visualReportServiceImpl")
	private VisualReportService visualReportService;
	@Resource(name = "loginOutBaseServiceImpl")
	private LoginOutBaseService loginOutBaseService;
	
	
	/*
	 * 主页
	 */
	@RequestMapping(method = RequestMethod.GET)
	public String index(Long storeId, String storeName, Pageable pageable,String username, String password, String companyName,
			String language, HttpServletRequest request,
			HttpServletResponse response, HttpSession session, ModelMap model) {

		if(username!=null){
			int flag=1;
			loginOutBaseService.submitLogin(username,
					password,
					companyName,
					language,
					request,
					response,
					session,
					flag);
		}
		
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("companyInfo", companyInfo);

		List<PcMenu> menus = null;
		if (storeMember.getCompanyInfoId() == null) {
			menus = pcMenuBaseService.getCurrentMenuList(0);
		}
		else {
			menus = pcMenuBaseService.getCurrentMenuList(1);
		}
		model.addAttribute("menu_list", menus); // 菜单列表

		Map<String, Object> saleOrgPost = new HashMap<String, Object>();
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("isDefault", true));
		List<StoreMemberSaleOrg> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findList(null,
				filters,
				null);
		for (StoreMemberSaleOrg storeMemberSaleOrg : storeMemberSaleOrgs) {
			if (storeMemberSaleOrg.getIsDefault()) {
				SaleOrg saleOrg = storeMemberSaleOrg.getSaleOrg();
				filters.clear();
				filters.add(Filter.eq("storeMember", storeMember));
				filters.add(Filter.eq("saleOrg", saleOrg));
				List<StoreMemberSaleOrgPost> storeMemberSaleOrgPosts = storeMemberSaleOrgPostService.findList(null,
						filters,
						null);
				for (StoreMemberSaleOrgPost storeMemberSaleOrgPost : storeMemberSaleOrgPosts) {
					saleOrgPost.put("saleOrgName", saleOrg.getName());
					saleOrgPost.put("postName",
							storeMemberSaleOrgPost.getPost().getName());
					break;
				}
			}
		}
		if (saleOrgPost.get("saleOrgName") == null
				&& storeMemberSaleOrgs.size() > 0) {
			saleOrgPost.put("saleOrgName", storeMemberSaleOrgs.get(0)
					.getSaleOrg()
					.getName());
		}
		model.addAttribute("saleOrgPost", saleOrgPost);
		

		return "/index";
	}

	@RequestMapping(value = "/home", method = RequestMethod.GET)
	public String home(ModelMap model) {

		StoreMember storeMember = storeMemberBaseService.getCurrent();
		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("companyInfo", companyInfo);

		if (companyInfo != null) {

			List<Order> orders = new ArrayList<Order>();
			List<Filter> filters = new ArrayList<Filter>();

			/* 公告 */
			/*
			 * orders.add(Order.asc("order")); filters.add(Filter.eq("isPublic",
			 * true)); filters.add(Filter.eq("type", 0)); List<SystemNotice>
			 * systemNotices =
			 * systemNoticeService.findList(null,filters,orders);
			 */
			//过滤公告
			List<SystemNotice> systemNoticesNew = new ArrayList<SystemNotice>();
			List<SystemNotice> systemNotices = systemNoticeService.getSystemNoticeById(storeMember.getId());
			if (systemNotices.size() > 0) {
				for (SystemNotice systemNotice : systemNotices) {
					if (systemNotice.getIsPublic()) {
						systemNoticesNew.add(systemNotice);
					}
				}
			}
			model.addAttribute("systemNotices", systemNoticesNew);

			/* 快捷通道 */
			orders.clear();
			filters.clear();
			orders.add(Order.asc("order"));
			filters.add(Filter.eq("isEnabled", true));
			List<QuickLink> quickLinks = quickLinkService.findList(6,
					filters,
					orders);
			model.addAttribute("quickLinks", quickLinks);

			/* 产品分类 */
			orders.clear();
			filters.clear();
			orders.add(Order.asc("order"));
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.isNull("parent"));
			List<ProductCategory> productCategorys = productCategoryBaseService.findList(20,
					filters,
					orders);
			model.addAttribute("productCategorys", productCategorys);

			/* 待办分类 */
			orders.clear();
			filters.clear();
			orders.add(Order.asc("order"));
			filters.add(Filter.eq("isEnabled", true));
			List<StatisticsCategory> statisticsCategorys = statisticsCategoryService.findList(2,
					filters,
					orders);

			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			if (storeMember != null) {
				for (StatisticsCategory statisticsCategory : statisticsCategorys) {
					List<Statistics> statisticss = statisticsService.findListForHomePage(statisticsCategory.getId(),
							storeMember.getId(),
							6);
					Map<String, Object> data = new HashMap<String, Object>();
					data.put("statisticsCategory", statisticsCategory);
					data.put("statisticss", statisticss);
					list.add(data);
				}
			}
			model.addAttribute("list", list);

			filters.clear();
			filters.add(Filter.eq("member", storeMember.getMember()));
			List<StoreMember> storeMembers = storeMemberBaseService.findList(null,
					filters,
					null);
			Store store = null;
			for (StoreMember storeMember2 : storeMembers) {
				if (!storeMember2.getStore()
						.getType()
						.equals(Store.Type.enterprise)) {
					store = storeMember2.getStore();
					break;
				}
			}
			model.addAttribute("store", store);

			/* 热销产品 */
			List<Map<String, Object>> hotSalesProducts = hotSalesProductService.findShow(null,
					null,
					null,
					null,
					null,
					store == null ? null : store.getId(),
					null,
					18);
			model.addAttribute("hotSalesProducts", hotSalesProducts);

			/* 当前需要处理的流程 */
			Page<Map<String, Object>> page = wfBaseService.findPage(1,
					WebUtils.getCurrentStoreMemberId(),
					null,
					null,
					null,
					null,
					null,
					new Pageable());
			model.addAttribute("wfs",
					page == null ? new ArrayList<Map<String, Object>>()
							: page.getContent());
			model.addAttribute("wfsize", page == null ? 0 : page.getContent()
					.size());
			filters.clear();
			filters.add(Filter.eq("storeMember",storeMember));
			filters.add(Filter.eq("companyInfoId",companyInfo.getId()));
			
			List<VisualReport> VisualReport = new ArrayList<VisualReport>();
			Set<VisualReport> A = new HashSet<VisualReport>();
			Set<VisualReport> B = new HashSet<VisualReport>();
			List<PcUserRole> pcUserRole = pcUserRoleBaseService.findList(null, filters, null);
			if(pcUserRole!=null){
				List<Map<String, Object>> visualReportList = roleVisualReportDao.findVisualReport(pcUserRole);
				for(Map<String, Object> vr : visualReportList){
					if(vr.get("role_vrs")!=null){
						Long id = Long.parseLong(vr.get("role_vrs").toString());
						VisualReport visualReport = visualReportService.find(id);
						VisualReport.add(visualReport);						
					}
				}
				for(VisualReport vr : VisualReport){
					if("A".equals(vr.getCategory())){
						A.add(vr);
					}
					if("B".equals(vr.getCategory())){
						B.add(vr);
					}
				}
				model.addAttribute("vrA",A);
				model.addAttribute("vrB",B);				
			}
		}
		NativeDao nativeDao = storeMemberBaseService.getDaoCenter()
				.getNativeDao();
		StringBuilder sql = new StringBuilder();
		StringBuilder sqlShipping = new StringBuilder();
		sql.append("select count(a.id) from xx_order a "
				+ " left join xx_sale_org so on so.id = a.sale_org"
				+ " where a.order_status in (5) and a.order_type = 2 and a.company_info_id = ?");
		sqlShipping.append("select count(a.id) from xx_order a "
				+ " left join xx_sale_org so on so.id = a.sale_org"
				+ " where a.shipping_status= 0 and a.order_type = 2 and a.company_info_id = ? ");

		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and a.stores in (" + storeAuth + ")");
				sqlShipping.append(" and a.stores in (" + storeAuth + ")");
			}
		}
		else {
			String saleOrgSql = " and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = "
					+ storeMember.getId()
					+ ") "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = "
					+ storeMember.getId()
					+ ")))";

			sql.append(saleOrgSql);
			sqlShipping.append(saleOrgSql);

		}

		Integer totalOrderNum = nativeDao.findInt(sql.toString(),
				new Object[] { WebUtils.getCurrentCompanyInfoId() });
		Integer totalUnshippingNum = nativeDao.findInt(sqlShipping.toString(),
				new Object[] { WebUtils.getCurrentCompanyInfoId() });
		model.addAttribute("totalOrderNum", totalOrderNum);
		model.addAttribute("totalUnshippingNum", totalUnshippingNum);

		int natureShortcut = 0;
		try {
			natureShortcut = Integer.parseInt(SystemConfig.getConfig("natureShortcut",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("natureShortcut", natureShortcut);

		// 获取8个轮播图
		List<Map<String, Object>> findImage = appImageService.findImage(8);
		model.addAttribute("findImage", findImage);
		//产品分类展示按钮
		List<Map<String, Object>> productCategory = showProductService.findList(5);
		model.addAttribute("productCategory", productCategory);
		Store store = null;
		Sbu sbu = null;
		//获取member
		Member member = storeMemberBaseService.getCurrent().getMember();
		List<StoreMember> storeMembers = storeMemberBaseService.findNotDefaultByMember(member);
		//获取客户  用户 - 默认客户
		if (!storeMembers.isEmpty()) {
			store = storeMembers.get(0).getStore();
			model.addAttribute("store", store);
		}
		//获取sbu 用户 - 默认sbu
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("isDefault", true));
		StoreMemberSbu storeMemberSbu = storeMemberSbuService.find(filters);
		sbu = storeMemberSbu!=null?storeMemberSbu.getSbu():null;
		model.addAttribute("sbu", sbu);
		//获取组织  用户 - 默认的sbu - 预留4
		Organization organization = sbu==null?null:sbu.getUndefined4();
		//获取机构  用户 - 第一个客户 - 机构
		SaleOrg saleOrg = store==null?null:store.getSaleOrg();
		model.addAttribute("organization", organization);
		model.addAttribute("saleOrg", saleOrg);
		if(store!=null&&sbu!=null&&organization!=null&&saleOrg!=null){
			Map<String, Object> map = storeBalanceService.findBalanceSbu(store.getId(),
					sbu.getId(),
					organization.getId(),
					saleOrg.getId());
			if(map!=null){
				//余额
				if (map.get("yue") != null) {
					BigDecimal balance = new BigDecimal(map.get("balance")
							.toString());
					BigDecimal credit_amount = new BigDecimal(
							map.get("credit_amount").toString());
					BigDecimal yue = balance.subtract(credit_amount);
					model.addAttribute("yue", yue);
				}
				//授信
				if (map.get("credit_amount") != null) {
					model.addAttribute("credit", map.get("credit_amount"));
				}
			}
			
		}
		return "/home";
	}

	@RequestMapping(value = "/store_home", method = RequestMethod.GET)
	public String store_home(ModelMap model) {

		StoreMember storeMember = storeMemberBaseService.getCurrent();
		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("companyInfo", companyInfo);

		if (companyInfo != null) {

			List<Order> orders = new ArrayList<Order>();
			List<Filter> filters = new ArrayList<Filter>();

			/* 公告 */
			orders.add(Order.asc("order"));
			filters.add(Filter.eq("isPublic", true));
			List<SystemNotice> systemNotices = systemNoticeService.findList(null,
					filters,
					orders);
			model.addAttribute("systemNotices", systemNotices);

			/* 快捷通道 */
			orders.clear();
			filters.clear();
			orders.add(Order.asc("order"));
			filters.add(Filter.eq("isEnabled", true));
			List<QuickLink> quickLinks = quickLinkService.findList(6,
					filters,
					orders);
			model.addAttribute("quickLinks", quickLinks);

			/* 产品分类 */
			orders.clear();
			filters.clear();
			orders.add(Order.asc("order"));
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.isNull("parent"));
			List<ProductCategory> productCategorys = productCategoryBaseService.findList(20,
					filters,
					orders);
			model.addAttribute("productCategorys", productCategorys);

			/* 待办分类 */
			orders.clear();
			filters.clear();
			orders.add(Order.asc("order"));
			filters.add(Filter.eq("isEnabled", true));
			List<StatisticsCategory> statisticsCategorys = statisticsCategoryService.findList(2,
					filters,
					orders);

			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			if (storeMember != null) {
				for (StatisticsCategory statisticsCategory : statisticsCategorys) {
					List<Statistics> statisticss = statisticsService.findListForHomePage(statisticsCategory.getId(),
							storeMember.getId(),
							6);
					Map<String, Object> data = new HashMap<String, Object>();
					data.put("statisticsCategory", statisticsCategory);
					data.put("statisticss", statisticss);
					list.add(data);
				}
			}
			model.addAttribute("list", list);

			filters.clear();
			filters.add(Filter.eq("member", storeMember.getMember()));
			List<StoreMember> storeMembers = storeMemberBaseService.findList(null,
					filters,
					null);
			Store store = null;
			for (StoreMember storeMember2 : storeMembers) {
				if (!storeMember2.getStore()
						.getType()
						.equals(Store.Type.enterprise)) {
					store = storeMember2.getStore();
					break;
				}
			}
			model.addAttribute("store", store);

			/* 热销产品 */
			List<Map<String, Object>> hotSalesProducts = hotSalesProductService.findShow(null,
					null,
					null,
					null,
					null,
					store == null ? null : store.getId(),
					null,
					18);
			model.addAttribute("hotSalesProducts", hotSalesProducts);
		}

		return "/store_home";
	}

	@RequestMapping(value = "/count_statistics", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg count_statistics(Long[] ids) {
//		NativeDao nativeDao = storeMemberBaseService.getDaoCenter()
//				.getNativeDao();
//		String storeAuth = storeMemberBaseService.storeAuth();

		Map<Long, Object> data = new HashMap<Long, Object>();
//		for (Long id : ids) {
//			BigDecimal total = BigDecimal.ZERO;
//			Statistics statistics = statisticsService.find(id);
//			String sql = statistics.getSql();
//			if (sql != null) {
//				if (storeAuth != null) {
//					sql += " and a.stores in (" + storeAuth + ")";
//				}
//				total = nativeDao.findBigDecimal(sql,
//						new Object[] { WebUtils.getCurrentCompanyInfoId() });
//			}
//			data.put(id, total);
//		}
		return this.success().addObjX(data);
	}

	/*
	 * 地区
	 */
	@RequestMapping(value = "/area", method = RequestMethod.GET)
	public @ResponseBody
	Map<Long, String> area(Long parentId, String locale) {
		List<Area> areas = new ArrayList<Area>();
		Area parent = areaBaseService.find(parentId);
		if (parent != null) {
			areas = new ArrayList<Area>(parent.getChildren());
		}
		else {
			areas = areaBaseService.findRoots(locale);
		}
		Map<Long, String> options = new HashMap<Long, String>();
		for (Area area : areas) {
			options.put(area.getId(), area.getName());
		}
		return options;
	}

}