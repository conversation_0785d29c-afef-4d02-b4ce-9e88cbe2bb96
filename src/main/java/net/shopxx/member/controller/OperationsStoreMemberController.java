package net.shopxx.member.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.entity.Setting;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.SettingUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.dao.OperationsStoreMemberDao;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.PartnerCategory;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.SmWarehouse;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMember.Gender;
import net.shopxx.member.service.MemberBaseService;
import net.shopxx.member.service.PartnerCategoryBaseService;
import net.shopxx.member.service.PcRoleBaseService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.SmWarehouseBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.stock.entity.Warehouse;
/**
 * 平台运营部
 */
@Controller("operationsStoreMemberController")
@RequestMapping("/member/operations_store_member")
public class OperationsStoreMemberController extends BaseController{
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "memberBaseServiceImpl")
	private MemberBaseService memberBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "partnerCategoryBaseServiceImpl")
	private PartnerCategoryBaseService partnerCategoryBaseService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "pcRoleBaseServiceImpl")
	private PcRoleBaseService pcRoleBaseService;
	@Resource(name = "smWarehouseBaseServiceImpl")
	private SmWarehouseBaseService smWarehouseBaseService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "operationsStoreMemberDao")
	private OperationsStoreMemberDao operationsStoreMemberDao;

	
	

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {

		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, fis, null));
		return "/member/operations_store_member/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model) {

		return "/member/operations_store_member/list_tb";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String username, String mobile, String name,
			Long memberRankId, Boolean isPartner, Long storeId, Long saleOrgId,
			String startTime, String endTime, Pageable pageable, ModelMap model) {

		Object[] args = new Object[] { username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				0};
		Page<Map<String, Object>> page = operationsStoreMemberDao.findPage(pageable,
				args);


		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}


	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, Integer flag, Integer memberType, ModelMap model) {

		StoreMember storeMember = storeMemberBaseService.find(id);
		model.addAttribute("store_member", storeMember);
		model.addAttribute("memberType", memberType);
		
		StoreMember loginMember=storeMemberBaseService.getCurrent();
		//sbu
		String sbuList = JsonUtils.toJson(storeMemberBaseService.findSbu(id));
		model.addAttribute("sbu_json", sbuList);
		
		//经营组织
		String organizationList = JsonUtils.toJson(storeMemberBaseService.findOrganization(id));
		model.addAttribute("organization_json", organizationList);
		
		//门店
		List<Map<String, Object>> shopLists=storeMemberBaseService.findShop(id);
		String idPost ="";
		for(int i=0; i<shopLists.size();i++){
			Map<String,Object> shopMap = shopLists.get(i);
			if(i==shopLists.size()-1){
				idPost+=shopMap.get("id");
			}else{
				idPost+=shopMap.get("id")+",";
			}
		}
		List<Map<String, Object>> shopPuts = storeMemberBaseService.findListByShop(id,
				idPost);
		List<Map<String, Object>> postIds = null;
		for (Map<String, Object> shopList : shopLists) {
			postIds = new ArrayList<Map<String, Object>>();
			String procTempId = shopList.get("id").toString();
			for (Map<String, Object> itemMap : shopPuts) {
				String pid = itemMap.get("shopInfo").toString();
				if (procTempId.equals(pid)) {
					postIds.add(itemMap);
				}
			}
			shopList.put("posts", postIds);
		}
		
		String shop_json = JsonUtils.toJson(shopLists);
		model.addAttribute("shop_json", shop_json);
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		List<PartnerCategory> partnerCategories = partnerCategoryBaseService.findList(null,
				filters,
				null);
		model.addAttribute("pc_lists", partnerCategories);
		// SaleOrg saleOrg =saleOrgService.findByStoreMember(id);
		// model.addAttribute("saleOrg", saleOrg);
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, filters, null));
		model.addAttribute("genders", Gender.values());

		// PcRole arole = pcRoleBaseService.findAdministrator();
		// List<Filter> fis = new ArrayList<Filter>();
		// fis.add(Filter.eq("pcRole", arole));
		// fis.add(Filter.eq("companyInfoId",
		// companyInfoBaseService.getCurrentId()));
		// fis.add(Filter.eq("storeMember", storeMember));
		// PcUserRole pcUserRole = pcUserRoleBaseService.find(fis);
		// model.addAttribute("isAdministrator", pcUserRole == null ? false :
		// true);
		model.addAttribute("isAdmin", WebUtils.isAdmin());

		// filters.clear();
		// filters.add(Filter.eq("member", storeMember.getMember()));
		// MemberOfSaleOrg memberOfSaleOrg =
		// memberOfSaleOrgService.find(filters);
		// if (memberOfSaleOrg != null) {
		// model.addAttribute("saleOrg", memberOfSaleOrg.getSaleOrg());
		// }
		// filters.clear();
		// filters.add(Filter.eq("storeMember", storeMember));
		// StoreMemberSaleOrg storeMemberSaleOrg =
		// storeMemberSaleOrgBaseService.find(filters);
		// if (storeMemberSaleOrg != null) {
		// model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
		// }

		// filters.clear();
		// filters.add(Filter.eq("member", storeMember.getMember()));
		// List<StoreMember> storeMembers =
		// storeMemberBaseService.findList(null, filters, null);
		// List<Map<String, Object>> stores= new
		// ArrayList<Map<String,Object>>();
		// for (StoreMember storeMember2 : storeMembers) {
		// if (!storeMember2.getStore().getType().equals(Type.enterprise)) {
		// Map<String, Object> map = new HashMap<String, Object>();
		// Type type=store.getType();
		// map.put("id", store.getId());
		// map.put("name", store.getName());
		// map.put("rank_name", store.getMemberRank().getName());
		// stores.add(map);
		// }
		// }
		model.addAttribute("stores_json",
				JsonUtils.toJson(storeBaseService.findListByMember(storeMember.getMember()
						.getId())));

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
				filters,
				null);
		List<PcRole> uRoles = new ArrayList<PcRole>();
		for (PcUserRole userRole : userRoles) {
			uRoles.add(userRole.getPcRole());
		}
		model.addAttribute("uRoles", uRoles);

		if(loginMember.getMemberType()==0){
			List<PcRole> roles = pcRoleBaseService.findAll();
			model.addAttribute("roles", roles);
		}else{
			List<PcRole> roles = pcRoleBaseService.findByStoreMemberId(loginMember.getId());
			model.addAttribute("roles", roles);
		}

		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		model.addAttribute("companyInfo", companyInfo);

		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<SmWarehouse> smWarehouses = smWarehouseBaseService.findList(null,
				filters,
				null);
		// List<Warehouse> warehouses = new ArrayList<Warehouse>();
		List<Map<String, Object>> warehouses = new ArrayList<Map<String, Object>>();
		for (SmWarehouse smWarehouse : smWarehouses) {
			Warehouse warehouse = smWarehouse.getWarehouse();
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("id", warehouse.getId());
			map.put("name", warehouse.getName());
			map.put("erp_warehouse_code", warehouse.getErp_warehouse_code());
			map.put("sn", warehouse.getSn());
			warehouses.add(map);
		}
		model.addAttribute("warehouses_json", JsonUtils.toJson(warehouses));

		// filters.clear();
		// filters.add(Filter.eq("storeMember", storeMember));
		// List<StoreMemberSaleOrg> storeMemberSaleOrgs =
		// storeMemberSaleOrgBaseService.findList(null, filters, null);
		// model.addAttribute("storeMemberSaleOrgs", storeMemberSaleOrgs);

		List<Map<String, Object>> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findListByStoreMember(id);
		String ids = "";
		for (int i = 0; i < storeMemberSaleOrgs.size(); i++) {
			Map<String, Object> map = storeMemberSaleOrgs.get(i);
			if (i == storeMemberSaleOrgs.size() - 1) {
				ids += map.get("id");
			}
			else {
				ids += map.get("id") + ",";
			}
		}
		List<Map<String, Object>> puts = storeMemberSaleOrgBaseService.findListBySaleOrg(id,
				ids);
		List<Map<String, Object>> posts = null;
		for (Map<String, Object> storeMemberSaleOrg : storeMemberSaleOrgs) {
			posts = new ArrayList<Map<String, Object>>();
			String procTempId = storeMemberSaleOrg.get("id").toString();
			for (Map<String, Object> itemMap : puts) {
				String pid = itemMap.get("saleOrg").toString();
				if (procTempId.equals(pid)) {
					posts.add(itemMap);
				}
			}
			storeMemberSaleOrg.put("posts", posts);
		}
		String storeMemberSaleOrgs_json = JsonUtils.toJson(storeMemberSaleOrgs);
		model.addAttribute("storeMemberSaleOrgs_json", storeMemberSaleOrgs_json);

		model.addAttribute("storeTypes", storeBaseService.getTypes());// 客户类型

		int undefinedProduct2Order = 0;// 未定义产品是否支持下单
		try {
			undefinedProduct2Order = Integer.parseInt(SystemConfig.getConfig("undefinedProduct2Order",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}
		model.addAttribute("undefinedProduct2Order", undefinedProduct2Order);
		
		/*区分系统*/
		Integer linkStock  = Integer.valueOf(SystemConfig.getConfig("linkStock",WebUtils.getCurrentCompanyInfoId()));
		model.addAttribute("linkStock", linkStock);

		if (flag!=null && flag == 1) {
			return "/member/store_member/edit_info";
		}else if(flag!=null && flag == 2){
			return "/member/store_member/edit_info_card";
		}
		return "/member/operations_store_member/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(StoreMember storeMember, Member member, Long memberRankId,
			Long[] storeId, Integer[] isDefault, Long pc_id, Long parentId,
			Long[] roleId, Long[] saleOrgId, Long[] warehouseId,Long[] organizationId,
			Integer[] isDeFaults,String[] postId, Boolean isAdministrator, Long[] sbuId,
			Integer[] isDefaults,Long[] shopId,String[] postIds, HttpServletRequest request,
			ModelMap model) {

		if (ConvertUtil.isEmpty(member.getMobile())) {
			// 用户名或手机号未填
			return error("1601002");
		}
		if (storeMember.getMemberType() != 1
				&& (saleOrgId == null || saleOrgId.length < 1)) {
			return error("请维护所属机构");
		}
		Setting setting = SettingUtils.get();
		if (StringUtils.isNotEmpty(member.getPassword())) {
			if (member.getPassword().length() < setting.getPasswordMinLength()
					|| member.getPassword().length() > setting.getPasswordMaxLength()) {
				// 密码长度范围有误
				return error("1601005");
			}
		}

		// 更新用户
		storeMemberBaseService.updateStoreMember(storeMember,
				member,
				storeId,
				roleId,
				saleOrgId,
				postId,
				isDefault,
				warehouseId,
				isAdministrator,
				pc_id,
				parentId,
				sbuId,
				isDefaults,
				shopId,
				postIds,
				request,
				organizationId,
				isDeFaults);

		return success();
	}

	/**
	 * 点击用户头像弹出更新
	 */
	@RequestMapping(value = "/update_info", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update_info(StoreMember storeMember, Member member) {

		Setting setting = SettingUtils.get();
		if (StringUtils.isNotEmpty(member.getPassword())) {
			if (member.getPassword().length() < setting.getPasswordMinLength()
					|| member.getPassword().length() > setting.getPasswordMaxLength()) {
				// 密码长度范围有误
				return error("1601005");
			}
		}
		
		
		storeMemberBaseService.updateInfo(storeMember, member);
		return success().addObjX("/login.jhtml");
	}


	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_store_member", method = RequestMethod.GET)
	public String selectStoreMember(Long memberRankId, Boolean isPartner,
			Integer multi, Pageable pageable, ModelMap model, Integer role,
			Integer memberType,Boolean isSalesman) {

		model.addAttribute("memberRankId", memberRankId);
		model.addAttribute("isPartner", isPartner);
		model.addAttribute("multi", multi);
		model.addAttribute("role", role);
		model.addAttribute("memberType", memberType);
		model.addAttribute("isSalesman", isSalesman);

		return "/member/operations_store_member/select_store_member";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_store_member_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectStoreMemberData(String username, String mobile,
			String name, Long memberRankId, Boolean isPartner, Long storeId,
			Long saleOrgId, String startTime, String endTime,
			Pageable pageable, ModelMap model, Integer role, Integer memberType,
			Boolean isSalesman) {
		Object[] args = new Object[] { username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				memberType,
				false,
				role };
		Page<Map<String, Object>> page = storeMemberBaseService.findPage(pageable,
				args);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}


	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String username, String mobile,
			String name, Long memberRankId, Boolean isPartner, Long storeId,
			Long saleOrgId, String startTime, String endTime,
			Integer memberType, Pageable pageable, ModelMap model) {

		Object[] args = new Object[] { username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				memberType };
		Page<Map<String, Object>> findPage = storeMemberBaseService.findPage(pageable,
				args);
		Integer size = (int) findPage.getTotal();
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 导出
	 * 
	 * @param username
	 * @param mobile
	 * @param name
	 * @param memberRankId
	 * @param isPartner
	 * @param storeId
	 * @param saleOrgId
	 * @param startTime
	 * @param endTime
	 * @param model
	 * @param page
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String username, String mobile,
			String name, Long memberRankId, Boolean isPartner, Long storeId,
			Long saleOrgId, String startTime, String endTime,
			Integer memberType, ModelMap model, Integer page) {
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = storeMemberBaseService.findList(username,
				mobile,
				name,
				memberRankId,
				isPartner,
				storeId,
				saleOrgId,
				startTime,
				endTime,
				memberType,
				null,
				page,
				size);
		return this.getModelAndView(data, model);

	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {
		List<Map<String, Object>> data = storeMemberBaseService.findList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null);
		return this.getModelAndView(data, model);
	}

	public ModelAndView getModelAndView(List<Map<String, Object>> data,
			ModelMap model) {
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		String[] header = { "用户名",
				"手机",
				"姓名",
				"所属客户",
				"机构名称",
				"SBU",
				"用户状态",
				"创建日期", };
		// 设置单 元格取值
		String[] properties = { "username",
				"mobile",
				"name",
				"store_name",
				"sale_org_name",
				"sbu_name",
				"is_enabled",
				"create_date" };
		for (Map<String, Object> map : data) {
			String is_enabled = null;
			if (map.get("is_enabled") != null) {
				is_enabled = map.get("is_enabled").toString();
				if ("false".equals(is_enabled)) {
					is_enabled = "禁用";
				}
				else {
					is_enabled = "正常";
				}
			}
			map.put("is_enabled", is_enabled);
		}

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, null, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	

}
