package net.shopxx.member.controller;

import net.shopxx.aftersales.entity.CreditRechargeVo;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.*;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.*;
import net.shopxx.member.service.*;
import net.shopxx.order.entity.CustomerContract;
import net.shopxx.order.service.CustomerContractService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;
import net.shopxx.util.RoleJurisdictionUtil;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller("shopCreditRechargeController")
@RequestMapping("/member/credit_recharge")
public class CreditRechargeController extends BaseController {

	@Resource(name = "creditRechargeServiceImpl")
	private CreditRechargeService creditRechargeService;
	@Resource(name = "saleOrgCreditServiceImpl")
	private SaleOrgCreditService saleOrgCreditService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "creditAttachServiceImpl")
	private CreditAttachService creditAttachService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "customerContractServiceImpl")
	private CustomerContractService customerContractService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "aftersaleServiceImpl")
	private AftersaleService aftersaleService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb_code(@PathVariable String code, Integer flag, Integer rechargeType, Integer type,
			Long objTypeId, Long objid, Long sbuId, Long menuId, ModelMap model) {
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("menuId", menuId);
		model.addAttribute("objid", objid);
		model.addAttribute("code", code);
		model.addAttribute("rechargeType", rechargeType);
		return CommonUtil.getFolderPrefix(code) + "/member/credit_recharge/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list_code(@PathVariable String code, Integer rechargeType, Integer flag, Long sbuId, Long userId,
			Long menuId, ModelMap model) {
		model.addAttribute("flag", flag);
		model.addAttribute("code", code);
		model.addAttribute("rechargeType", rechargeType);
		model.addAttribute("sbuId", sbuId);
		// 获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return CommonUtil.getFolderPrefix(code) + "/member/credit_recharge/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn, Integer[] status, Integer rechargeType, Long creatorId,
			Long operatorId, Long storeId, Long[] organizationId, Long sbuId, Long saleOrgId, String storeMemberName,
			String startFirstTime, String startLastTime, String endFirstTime, String endLastTime, Pageable pageable,
			ModelMap model) {

		String jsonPage = JsonUtils.toJson(creditRechargeService.findPage(1, sn, null, storeId, status, creatorId,
				operatorId, rechargeType, organizationId, sbuId, saleOrgId, storeMemberName, startFirstTime,
				startLastTime, endFirstTime, endLastTime, pageable));

		return success(jsonPage);
	}

	@RequestMapping(value = "/list_check_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_check_data(String sn, Integer[] status, Integer rechargeType, Long creatorId,
			Long operatorId, Long storeId, Long[] organizationId, Pageable pageable, ModelMap model) {

		boolean isAdmin = WebUtils.isAdmin();
		if (!isAdmin) {
			creatorId = storeMemberService.getCurrent().getId();
		}
		String jsonPage = JsonUtils.toJson(creditRechargeService.findPage(1, sn, null, storeId, status, creatorId,
				operatorId, rechargeType, organizationId, null, null, null, null, null, null, null, pageable));

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add_code(@PathVariable String code, Integer rechargeType, Long sbuId, Integer flag, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("rechargeType", rechargeType);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		model.addAttribute("startDate", DateUtil.getCurrDateStr("yyyy-MM-dd"));
		model.addAttribute("endDate", df.format(DateUtil.getLastMonthDay(new Date())));
		Store store = null;
		Member member = storeMemberService.getCurrent().getMember();
		List<StoreMember> storeMembers = storeMemberService.findNotDefaultByMember(member);
		if (storeMembers != null) {
			for (StoreMember storeMember1 : storeMembers) {
				store = storeMember1.getStore();
				if (store.getType().equals(Store.Type.distributor)) {
					model.addAttribute("store", store);
					break;
				} else {
					store = null;
				}
			}
		}
		/**
		 * 用户经营组织权限
		 */
		List<Organization> organizationList = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("organizationList", organizationList);

		model.addAttribute("code", code);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService
					.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		}
		Sbu sbu = sbuService.find(sbuId);
		model.addAttribute("sbu", sbu);
		// 合同权限
		Integer contractRoles = roleJurisdictionUtil.getRoleCount("contractRoles");
		model.addAttribute("contractRoles", contractRoles);

		return CommonUtil.getFolderPrefix(code) + "/member/credit_recharge/add";
	}

	/**
	 * 保存
	 */
	/**
	 * @param creditRecharge
	 * @param storeId
	 * @param storeMemberId
	 * @param saleOrgId
	 * @param organizationId
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(CreditRecharge creditRecharge, Long storeId, Long contractId,
			Long storeMemberId, Long saleOrgId, Long organizationId, Long sbuId, String aftersaleSn) {
		if (organizationId != null) {
			Organization organization = organizationService.find(organizationId);
			if (organization != null) {
				creditRecharge.setOrganization(organization);
			}
		}
		Store store = storeService.find(storeId);
		StoreMember storeMember = storeMemberService.find(storeMemberId);
		if (creditRecharge.getRechargeType() == 1) {
			if (storeMember == null) {
				// 操作错误，客户不存在
				return error("操作错误，用户不存在");
			}
		} else {
			if (store == null) {
				// 操作错误，客户不存在
				return error("16400");
			}
		}
		if (creditRecharge.getAmount() != null && creditRecharge.getAmount().compareTo(BigDecimal.ZERO) != 1) {
			return error("计划充值金额不能小于等于0");
		}
		if(StringUtils.isNotEmpty(aftersaleSn) &&
				creditRechargeService.exists(Filter.eq("fourAftersaleSn", aftersaleSn),
						Filter.ne("docStatus", 3))){
			return error("该售后单已经被关联！");
		}
		if (contractId != null) {
			CustomerContract customerContract = customerContractService.find(contractId);
			creditRecharge.setCustomerContract(customerContract);
		}
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		creditRecharge.setSaleOrg(saleOrg);
		creditRecharge.setStatus(0);
		creditRecharge.setDocStatus(0);// 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.作废
		creditRecharge.setType(0);
		creditRecharge.setStore(store);
		creditRecharge.setStoreMember(storeMember);
		creditRecharge.setCreator(storeMemberService.getCurrent());
		creditRecharge.setActualAmount(creditRecharge.getAmount());
		creditRecharge.setSn(SnUtil.getCreditRechargeSn());
		if (creditRecharge.getAftersale() == null || creditRecharge.getAftersale().getId() == null) {
			creditRecharge.setAftersale(null);
		}
		creditRecharge.setFourAftersaleSn(aftersaleSn);

		List<CreditAttach> creditAttachs = creditRecharge.getCreditAttachs();
		for (Iterator<CreditAttach> iterator = creditAttachs.iterator(); iterator.hasNext();) {
			CreditAttach creditAttach = iterator.next();
			if (creditAttach == null || creditAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (creditAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			creditAttach.setFileName(creditAttach.getName() + "." + creditAttach.getSuffix());
			creditAttach.setCreditRecharge(creditRecharge);
		}

		// 处理sbu
		Sbu sbu = sbuService.find(sbuId);
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		if (sbus.size() == 0 || sbus == null) {
			return error("该客户没有维护此类型SBU");
		}
		if (sbuId == null) {
			return error("请选择sbu！");
		} else {

			creditRecharge.setSbu(sbu);
		}
		creditRecharge.setCreditAttachs(creditAttachs);
		creditRechargeService.save(creditRecharge);
		orderFullLinkService.addFullLink(8, null, creditRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "客户授信申请" }), null);

		if(creditRecharge.getFourAftersaleSn() != null){
			CreditRechargeVo vo = new CreditRechargeVo();
			vo.setAftersaleSn(creditRecharge.getFourAftersaleSn());
			vo.setCreditRechargeSn(creditRecharge.getSn());
			vo.setCreditRechargeDocStatus(creditRecharge.getDocStatus());
			vo.setCreditRechargeActualAmount(creditRecharge.getActualAmount());
			aftersaleService.syncCreditRechargeToAftersale(vo);
		}

		return success().addObjX(creditRecharge.getId());
	}

	@RequestMapping(value = "/view/{code}", method = RequestMethod.GET)
	public String view_code(@PathVariable String code, Integer rechargeType, Long id, Integer flag, ModelMap model) {

		CreditRecharge creditRecharge = creditRechargeService.find(id);
		model.addAttribute("cr", creditRecharge);
		model.addAttribute("flag", flag);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		model.addAttribute("rechargeType", rechargeType);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(creditRecharge.getSn(), 8));
		model.addAttribute("payment_json", payment_json);

		/** 全链路 */
		String fullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByElseSnAndType(creditRecharge.getSn(), 8));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(46L);
		model.addAttribute("isCheckWf", isCheckWf);

		String twContractAttach_json = JsonUtils.toJson(creditAttachService.findListByItemId(null, id));
		model.addAttribute("twContractAttach_json", twContractAttach_json);

		/**
		 * 用户经营组织权限
		 */
		List<Organization> organizationList = roleJurisdictionUtil.getOrganizationList();
		model.addAttribute("organizationList", organizationList);

		model.addAttribute("code", code);
		/*
		 * Map<String, Object> bal =
		 * storeBalanceService.findBalanceSbu(creditRecharge.getStore().getId(),
		 * creditRecharge.getSbu().getId(),creditRecharge.getOrganization().getId(),
		 * creditRecharge.getSaleOrg().getId()); if (bal!=null && bal.get("balance") !=
		 * null) { model.addAttribute("balance", bal.get("balance")); }
		 */
		// 合同权限
		Integer contractRoles = roleJurisdictionUtil.getRoleCount("contractRoles");
		model.addAttribute("contractRoles", contractRoles);

		// 授信作废，只有角色-财务才能作废
		boolean jurisdiction = creditRechargeService.findJurisdiction(storeMember);
		model.addAttribute("jurisdiction", jurisdiction);

		return CommonUtil.getFolderPrefix(code) + "/member/credit_recharge/check";
	}

	@RequestMapping(value = "/viewSn", method = RequestMethod.GET)
	public String viewSn(String sn, Integer isAdd, ModelMap model) {

		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("sn", sn));
		CreditRecharge creditRecharge = creditRechargeService.find(fis);
		model.addAttribute("cr", creditRecharge);
		model.addAttribute("isAdd", isAdd);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(sn, 8));
		model.addAttribute("payment_json", payment_json);

		/** 全链路 */
		String fullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByElseSnAndType(creditRecharge.getSn(), 8));
		model.addAttribute("fullLink_json", fullLink_json);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(46L);
		model.addAttribute("isCheckWf", isCheckWf);

		return "/member/credit_recharge/check";
	}

	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check(Long id, Integer flag, String note, BigDecimal actualAmount, ModelMap model) {

		CreditRecharge creditRecharge = creditRechargeService.find(id);
		Integer status = creditRecharge.getStatus();
		if (status == 1 || status == 2) {
			// 申请单已审核通过或驳回
			return error("16402");
		}
//		if (actualAmount == null
//				|| actualAmount.compareTo(BigDecimal.ZERO) <= 0) {
//			//实际充值金额必须大于0
//			return error("16403");
//		}
		if (actualAmount.compareTo(creditRecharge.getAmount()) == 1) {
			// 实际充值金额不能大于计划充值金额
			return error("16404");
		}
		/** 部门总额度，已使用额度 */
		List<Map<String, Object>> saleOrgCreditList = saleOrgCreditService
				.findListBySaleOrg(creditRecharge.getSaleOrg().getId());
		if (saleOrgCreditList != null && saleOrgCreditList.size() > 0) {
			Map<String, Object> map = saleOrgCreditList.get(0);

			BigDecimal totalAmount = new BigDecimal(
					map.get("totalAmount") == null ? "0.00" : map.get("totalAmount").toString());
			BigDecimal usedAmount = new BigDecimal(
					map.get("totalAmount") == null ? "0.00" : map.get("usedAmount").toString());
			BigDecimal balance = totalAmount.subtract(usedAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
			if (actualAmount.compareTo(balance) == 1) {
				return error("实际充值金额不能大于部门余额");
			}
		} else {
			return error("实际充值金额不能大于部门余额");
		}
		creditRechargeService.check(creditRecharge, flag, note, actualAmount);
		return success();
	}

	/**
	 * 新流程启动
	 * 
	 * @param id
	 * @param flag
	 * @param note
	 * @param typeflag
	 * @param actualAmount
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/start_check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg start_check_wf(Long id, Integer flag, String note, Integer typeflag,
			BigDecimal actualAmount, String modelId, Long objTypeId, ModelMap model) {

		CreditRecharge creditRecharge = creditRechargeService.find(id);
		// 校验授信单状态
		creditRechargeService.checkCreditRechargeStatus(creditRecharge, 0, "已保存", "审批流程");
		creditRechargeService.createWf(id, modelId, objTypeId, creditRecharge, flag, note, actualAmount);
		return success();
	}

	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancel(Long id, Integer flag, String note, ModelMap model) {

		CreditRecharge creditRecharge = creditRechargeService.find(id);
		// 校验授信单状态
		creditRechargeService.checkCreditRechargeStatus(creditRecharge, 0, "已保存", "作废");
		creditRecharge.setDocStatus(3); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.作废
		creditRechargeService.update(creditRecharge);
		orderFullLinkService.addFullLink(8, null, creditRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18703", new Object[] { "客户授信充值", "作废本次充值申请" }), null);
		return success();
	}

	/**
	 * 关闭
	 */
	@RequestMapping(value = "/close", method = RequestMethod.POST)
	public @ResponseBody ResultMsg close(Long id, Integer flag, String note, ModelMap model) {

		CreditRecharge creditRecharge = creditRechargeService.find(id);
		// 校验授信单状态
		creditRechargeService.checkCreditRechargeStatus(creditRecharge, 2, "已处理", "关闭");
		creditRecharge.setDocStatus(4); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.作废 4.关闭
		creditRechargeService.update(creditRecharge);
		orderFullLinkService.addFullLink(8, null, creditRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18703", new Object[] { "客户授信充值", "关闭本次充值申请" }), null);
		if(creditRecharge.getFourAftersaleSn() != null){
			CreditRechargeVo vo = new CreditRechargeVo();
			vo.setAftersaleSn(creditRecharge.getFourAftersaleSn());
			vo.setCreditRechargeSn(creditRecharge.getSn());
			vo.setCreditRechargeDocStatus(creditRecharge.getDocStatus());
			vo.setCreditRechargeActualAmount(creditRecharge.getActualAmount());
			aftersaleService.syncCreditRechargeToAftersale(vo);
		}
		return success();
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(CreditRecharge creditRecharge, Long storeId, Long storeMemberId,
			Long saleOrgId, BigDecimal actualAmount, Long organizationId, Long sbuId, Long contractId,String aftersaleSn) {

		CreditRecharge cr = creditRechargeService.find(creditRecharge.getId());
		// 校验授信单状态
		creditRechargeService.checkCreditRechargeStatus(cr, 0, "已保存", "保存");
		String fourAftersaleSn = cr.getFourAftersaleSn();

		Store store = storeService.find(storeId);
		StoreMember storeMember = storeMemberService.find(storeMemberId);
		if (creditRecharge.getRechargeType() == 1) {
			if (storeMember == null) {
				// 操作错误，客户不存在
				return error("操作错误，用户不存在");
			}
		} else {
			if (store == null) {
				// 操作错误，客户不存在
				return error("16400");
			}
		}
		if(StringUtils.isNotEmpty(aftersaleSn) &&
				creditRechargeService.exists(Filter.eq("fourAftersaleSn", aftersaleSn),
						Filter.ne("docStatus", 3),
						Filter.ne("id", creditRecharge.getId()))){
			return error("该售后单已经被关联！");
		}
		if (organizationId != null) {
			Organization organization = organizationService.find(organizationId);
			if (organization != null) {
				cr.setOrganization(organization);
			}
		}
		List<CreditAttach> creditAttachs = creditRecharge.getCreditAttachs();

		for (Iterator<CreditAttach> iterator = creditAttachs.iterator(); iterator.hasNext();) {
			CreditAttach creditAttach = iterator.next();
			if (creditAttach == null || creditAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (creditAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			creditAttach.setFileName(creditAttach.getName() + "." + creditAttach.getSuffix());
			creditAttach.setCreditRecharge(cr);
		}

		if (contractId != null) {
			CustomerContract customerContract = customerContractService.find(contractId);
			creditRecharge.setCustomerContract(customerContract);
		}
		cr.getCreditAttachs().clear();
		cr.getCreditAttachs().addAll(creditAttachs);
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		cr.setSaleOrg(saleOrg);
		cr.setStore(store);
		cr.setStoreMember(storeMember);
		cr.setAmount(creditRecharge.getAmount());
		cr.setActualAmount(actualAmount);
		cr.setImage(creditRecharge.getImage());
		cr.setImage2(creditRecharge.getImage2());
		cr.setImage3(creditRecharge.getImage3());
		cr.setMemo(creditRecharge.getMemo());
		cr.setStartDate(creditRecharge.getStartDate());
		cr.setEndDate(creditRecharge.getEndDate());

		cr.setFourAftersaleSn(aftersaleSn);

		Sbu sbu = sbuService.find(sbuId);
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		if (sbus.size() == 0 || sbus == null) {
			return error("该客户没有维护此类型SBU");
		}
		if (sbuId == null) {
			return error("请选择sbu！");
		} else {
			cr.setSbu(sbu);
		}
        if (creditRecharge.getAftersale() == null || creditRecharge.getAftersale().getId() == null) {
            cr.setAftersale(null);
        }
		creditRechargeService.update(cr);
		if(fourAftersaleSn != null){
			CreditRechargeVo vo = new CreditRechargeVo();
			vo.setAftersaleSn(fourAftersaleSn);
			aftersaleService.syncCreditRechargeToAftersale(vo);
		}
		if(cr.getFourAftersaleSn() != null){
			CreditRechargeVo vo = new CreditRechargeVo();
			vo.setAftersaleSn(cr.getFourAftersaleSn());
			vo.setCreditRechargeSn(cr.getSn());
			vo.setCreditRechargeDocStatus(cr.getDocStatus());
			vo.setCreditRechargeActualAmount(cr.getActualAmount());
			aftersaleService.syncCreditRechargeToAftersale(vo);
		}
		return success();
	}

	/**
	 * 授信查询
	 */
	@RequestMapping(value = "/selectCreditContract", method = RequestMethod.GET)
	public String selectCreditContract(Long storeId, Integer rechargeType, ModelMap model) {

		model.addAttribute("storeId", storeId);
		model.addAttribute("rechargeType", rechargeType);
		return "/member/credit_recharge/select_credit";
	}

	@RequestMapping(value = "/selectCreditContractData", method = RequestMethod.POST)
	public @ResponseBody ResultMsg selectCreditContractData(Long storeId, String sn, Integer rechargeType,
			Pageable pageable) {

		Page<Map<String, Object>> page = creditRechargeService.findShowPage(storeId, sn, rechargeType, pageable);

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);

	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> countDepositRecharge(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId, Long operatorId, Long storeId, String firstTime,
			String lastTime, BigDecimal minPrice, BigDecimal maxPrice, Long sbuId, Pageable pageable, Long[] ids,
			ModelMap model) {
		Integer size = creditRechargeService.countCreditRecharge(new Integer[] { 1 }, sn, null, storeId, null, status,
				docstatus, rechargeTypeId, creatorId, operatorId, minPrice, maxPrice, sbuId, firstTime, lastTime, ids,
				pageable, null, null);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @param sn
	 * @param status
	 * @param docstatus
	 * @param rechargeTypeId
	 * @param creatorId
	 * @param operatorId
	 * @param storeId
	 * @param firstTime
	 * @param lastTime
	 * @param minPrice
	 * @param maxPrice
	 * @param pageable
	 * @param page
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView findCreditRechargeList(String sn, Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, Long storeId, String firstTime, String lastTime, BigDecimal minPrice,
			BigDecimal maxPrice, Long sbuId, Pageable pageable, Integer page, ModelMap model) {
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = creditRechargeService.findCreditRechargeList(new Integer[] { 1 }, sn, null,
				storeId, null, status, docstatus, rechargeTypeId, creatorId, operatorId, minPrice, maxPrice, firstTime,
				lastTime, sbuId, null, pageable, page, size);
		return getModelAndViewForList(data, model);
	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView findCreditRechargeList(Long[] ids, ModelMap model) {
		List<Map<String, Object>> data = creditRechargeService.findCreditRechargeList(new Integer[] { 1 }, null, null,
				null, null, null, null, null, null, null, null, null, null, null, null, ids, null, null, null);
		return getModelAndViewForList(data, model);
	}

	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("amount") != null) {

				str.put("amount", new BigDecimal(str.get("amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("actual_amount") != null) {

				str.put("actual_amount",
						new BigDecimal(str.get("actual_amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("doc_status") != null) {
				if ((Integer) str.get("doc_status") == 0) {
					str.put("doc_status", "已保存");

				} else if ((Integer) str.get("doc_status") == 1) {
					str.put("doc_status", "	已提交");

				} else if ((Integer) str.get("doc_status") == 2) {
					str.put("doc_status", "已处理");

				} else if ((Integer) str.get("doc_status") == 3) {
					str.put("doc_status", "已关闭");

				} else {
					str.put("doc_status", "-");
				}

			}

			if (str.get("wf_state") != null) {
				if ((Integer) str.get("wf_state") == 0) {
					str.put("wf_state", "未启动");
				} else if ((Integer) str.get("wf_state") == 1) {
					str.put("wf_state", "审核中");
				} else if ((Integer) str.get("wf_state") == 2) {
					str.put("wf_state", "已完成");
				} else if ((Integer) str.get("wf_state") == 3) {
					str.put("wf_state", "驳回");
				} else if ((Integer) str.get("wf_state") == 4) {
					str.put("wf_state", "中断");
				} else if ((Integer) str.get("wf_state") == 5) {
					str.put("wf_state", "禁用");
				} else {
					str.put("wf_state", "-");
				}

			}
			if ((Integer) str.get("status") == 0) {
				str.put("status", "未审核");
			} else if ((Integer) str.get("status") == 1) {
				str.put("status", "已审核");
			} else if ((Integer) str.get("status") == 1) {
				str.put("status", "已驳回");
			}

			if ((Integer) str.get("type") == 0) {
				str.put("type", "未生效");
			} else if ((Integer) str.get("type") == 1) {
				str.put("type", "已生效");
			} else if ((Integer) str.get("type") == 2) {
				str.put("type", "已过期");
			}
			if (str.get("create_date") != null) {
				String create_date = str.get("create_date").toString();
				str.put("create_date", create_date.substring(0, 10));
			}
			if (str.get("apply_date") != null) {
				String apply_date = str.get("apply_date").toString();
				str.put("apply_date", apply_date.substring(0, 10));
			}
			if (str.get("start_date") != null) {
				String start_date = str.get("start_date").toString();
				str.put("start_date", start_date.substring(0, 10));
			}
			if (str.get("end_date") != null) {
				String end_date = str.get("end_date").toString();
				str.put("end_date", end_date.substring(0, 10));
			}

		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "客户授信编号", "经营组织", "充值客户", "客户简称", "ERP客户编码", "机构", "sbu", "计划充值金额", "实际充值金额", "充值状态",
				"开始时间", "结束时间",
//				"流程状态",
				"申请日期", "创建人", "申请备注" };

		// 设置单元格取值
		String[] properties = { "sn", "organization_name", "store_name", "store_alias", "out_trade_no", "sale_org_name",
				"sbu_name", "amount", "actual_amount", "type",
//				"doc_status",
				"start_date", "end_date",
//				"wf_state",
				"create_date", "creator_name", "memo" };
		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
}