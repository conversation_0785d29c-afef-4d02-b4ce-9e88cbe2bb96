package net.shopxx.member.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.MemberRankBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.Post;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.Store.Type;
import net.shopxx.member.entity.StoreAddress;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.entity.StoreMemberSaleOrgPost;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.PostBaseService;
import net.shopxx.member.service.StoreAddressService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreInvoiceInfoService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.member.service.VisualReportService;
import net.shopxx.order.entity.Contract;
import net.shopxx.order.service.ContractService;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductStoreBaseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.stock.entity.Warehouse;
import net.shopxx.stock.entity.WarehouseStore;
import net.shopxx.stock.service.WarehouseStoreBaseService;
import net.shopxx.util.CommonUtil;

/**
 * Controller - 客户
 */
@Controller("shopMemberStoreController")
@RequestMapping("/member/store")
public class StoreController extends BaseController {

	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "memberRankBaseServiceImpl")
	private MemberRankBaseService memberRankBaseService;
	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardBaseService;
	@Resource(name = "warehouseStoreBaseServiceImpl")
	private WarehouseStoreBaseService warehouseStoreBaseService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "productStoreBaseServiceImpl")
	private ProductStoreBaseService productStoreBaseService;
	@Resource(name = "storeInvoiceInfoServiceImpl")
	private StoreInvoiceInfoService storeInvoiceInfoService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "storeAddressServiceImpl")
	private StoreAddressService storeAddressService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeSbuServiceImpl")
	private StoreSbuService storeSbuService;
	@Resource(name = "contractServiceImpl")
	private ContractService contractService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
    private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
	@Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
	@Resource(name = "postBaseServiceImpl")
	private PostBaseService postBaseService;
	@Resource(name = "visualReportServiceImpl")
	private VisualReportService visualReportService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Integer isEdit,Long menuId,
			Integer isStore, Integer readOnly, ModelMap model) {
		model.addAttribute("isEdit", isEdit);
		model.addAttribute("code", code);
		model.addAttribute("isStore", isStore);
		model.addAttribute("readOnly", readOnly);
		model.addAttribute("menuId", menuId);
		return CommonUtil.getFolderPrefix(code) + "/member/store/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Integer isEdit,Long userId, Long menuId,
			Integer isStore, Integer readOnly, Pageable pageable, ModelMap model) {

		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, fis, null));
		model.addAttribute("isEdit", isEdit);
		//新增客户
		fis.clear();
		fis.add(Filter.eq("code", "CustomerType"));
		fis.add(Filter.eq("isEnabled", true));
		fis.add(Filter.isNotNull("parent"));
		List<SystemDict> customerTypes = systemDictBaseService.findList(null,
				fis,
				null);
		model.addAttribute("customerTypes", customerTypes);
		List<Integer> types = storeBaseService.getTypes();
		model.addAttribute("types", types);
		model.addAttribute("code", code);
		model.addAttribute("isStore", isStore);
		model.addAttribute("readOnly", readOnly);
		
		// 经销商状态
		fis.clear();
		fis.add(Filter.eq("code", "distributorStatus"));
		fis.add(Filter.eq("isEnabled", true));
		fis.add(Filter.isNotNull("parent"));
		List<SystemDict> distributorStatus = systemDictBaseService.findList(null,
				fis,
				null);
		model.addAttribute("distributorStatus", distributorStatus);
		//销售面价
		boolean role = storeBaseService.findByRole(storeMember, "管理员");
		model.addAttribute("role", role);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return CommonUtil.getFolderPrefix(code) + "/member/store/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String name, String sn, String outTradeNo,
			String mobile, String serviceTelephone, Integer[] type,
			Long saleOrgId, Long memberRankId, String createBy,
			String saleOrgName, String outShopName, Integer onlineShopType,
			Long storeMemberId,String region,
			Long[] distributorStatusId,String headAddress,Pageable pageable, ModelMap model) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Page<Map<String, Object>> page = storeBaseService.findPage(name,
				sn,
				outTradeNo,
				mobile,
				serviceTelephone,
				type,
				outShopName,
				saleOrgId,
				companyInfoId,
				memberRankId,
				createBy,
				saleOrgName,
				null,
				null,
				null,
				null,
				null,
				storeMemberId,
				region,
				distributorStatusId,
				headAddress,
				null,
				pageable,
				null);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add(@PathVariable String code, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isTop", true));
		List<SaleOrg> saleOrgs = saleOrgBaseService.findList(1, filters, null);
		if (saleOrgs.size() > 0) {
			model.addAttribute("saleOrg", saleOrgs.get(0));
		}
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, filters, null));

		List<Integer> types = storeBaseService.getTypes();
		model.addAttribute("types", types);
		model.addAttribute("companyInfoId", WebUtils.getCurrentCompanyInfoId());

		// 业务类型
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("businessTypes", businessTypes);

		// 经销商状态
		filters.clear();
		filters.add(Filter.eq("code", "distributorStatus"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> distributorStatus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("distributorStatus", distributorStatus);

		//客户类型
		filters.clear();
		filters.add(Filter.eq("code", "CustomerType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> customerTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("customerTypes", customerTypes);

		//经销商属性
		filters.clear();
		filters.add(Filter.eq("code", "agentProperty"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> agentProperties = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("agentProperties", agentProperties);

		//等级
		filters.clear();
		filters.add(Filter.eq("code", "Grade"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> grades = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("grades", grades);

		filters.clear();
		filters.add(Filter.eq("code", "sbu"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> sbus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("sbus", sbus);

		//销售体系
		filters.clear();
		filters.add(Filter.eq("code", "SaleStyle"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleStyle = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleStyles", saleStyle);

		model.addAttribute("code", code);
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		model.addAttribute("nowDate", df.format(new Date()));

		return CommonUtil.getFolderPrefix(code) + "/member/store/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(Store store, Long storeMemberId, Long saleStyleId,
				   Long areaId, Long saleOrgId, Long memberRankId, BankCard bankCard,
				   Long bAreaId, Long salesPlatformId, String bAddress,
				   String bMobile, Integer store_type, Long businessTypeId,
				   Long changeMemberRankId, Long distributorStatusId, Long sbuId,
				   Long customerTypeId, Long gradenId, Long salesAreaId,
				   Long storeSalesAreaId,Long agentPropertyId) {

		if (storeBaseService.exists(Filter.eq("name", store.getName()),
				Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()))) {
			// 客户名称已存在
			return error("160001");
		}
		if (store_type == null) return error("请选择客户类型");
		if (memberRankId == null) {
			return error("请选择价格类型");
		}
		Store.Type type = Store.Type.values()[store_type];
		store.setType(type);
		if (store.getTaxRate() != null) {
			store.setTaxRate(store.getTaxRate().divide(new BigDecimal(100)));
		}
		SystemDict sbu = systemDictBaseService.find(sbuId);
		store.setSbu(sbu);
		SystemDict customerType = systemDictBaseService.find(customerTypeId);
		store.setCustomerType(customerType);
		SystemDict agentProperty = systemDictBaseService.find(agentPropertyId);
		store.setAgentProperty(agentProperty);//经销商属性
		SystemDict graden = systemDictBaseService.find(gradenId);
		store.setGraden(graden);
		SystemDict saleStyle = systemDictBaseService.find(saleStyleId);
		store.setSaleStyle(saleStyle);
		/*
		 * if (sbuId != null) { SystemDict sbu =
		 * systemDictBaseService.find(sbuId); if(sbu != null){
		 * store.setSbu(sbu); }else{ return error("请选择sbu"); } }else{ return
		 * error("请选择sbu"); }
		 */
		if (saleOrgId == null) {
			return error("请维护机构");
		}
		if (store.getHeadNewArea() == null
				|| store.getHeadNewArea().getId() == null) {
			store.setHeadNewArea(null);
		}
		storeBaseService.save(store,
				storeMemberId,
				areaId,
				saleOrgId,
				memberRankId,
				salesPlatformId,
				businessTypeId,
				distributorStatusId,
				bankCard,
				bAreaId,
				bAddress,
				bMobile,
				changeMemberRankId,
				salesAreaId,
				storeSalesAreaId);
		//给地址外部编码赋唯一值
		List<StoreAddress> lists = store.getStoreAddress();
		for (StoreAddress sas : lists) {
			sas.setOutTradeNo(sas.getId().toString());
			storeAddressService.update(sas);
		}
		return success().addObjX(store.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
	public String edit(@PathVariable String code, Long id, Integer isEdit,
					   Integer isStore, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isTop", true));
		List<SaleOrg> saleOrgs = saleOrgBaseService.findList(1, filters, null);
		if (saleOrgs.size() > 0) {
			model.addAttribute("saleOrg", saleOrgs.get(0));
		}
		filters.clear();
		filters.add(Filter.eq("isEnabled", true));
		model.addAttribute("memberRanks",
				memberRankBaseService.findList(null, filters, null));

		Store store = storeBaseService.find(id);
		model.addAttribute("store", store);

		//门店数量
		Integer ShopInfoCount = shopInfoService.countShopInfo(id);
		model.addAttribute("ShopInfoCount", ShopInfoCount);

		filters.clear();
		filters.add(Filter.eq("store", store));
		BankCard bankCard = bankCardBaseService.find(filters) == null ? new BankCard()
				: bankCardBaseService.find(filters);
		model.addAttribute("bankCard", bankCard);

		filters.clear();
		filters.add(Filter.eq("store", store));
		List<WarehouseStore> warehouseStores = warehouseStoreBaseService.findList(null,
				filters,
				null);
		List<Warehouse> warehouses = new ArrayList<Warehouse>();
		for (WarehouseStore warehouseStore : warehouseStores) {
			warehouses.add(warehouseStore.getWarehouse());
		}

		//平台性质
		filters.clear();
		filters.add(Filter.eq("code", "SaleOrgType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgTypes", saleOrgTypes);

		//客户类型
		filters.clear();
		filters.add(Filter.eq("code", "CustomerType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> customerTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("customerTypes", customerTypes);

		//经销商属性
		filters.clear();
		filters.add(Filter.eq("code", "agentProperty"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> agentProperties = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("agentProperties", agentProperties);

		//等级
		filters.clear();
		filters.add(Filter.eq("code", "Grade"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> grades = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("grades", grades);

		filters.clear();
		filters.add(Filter.eq("store", store));
		//		Pageable pageable = new Pageable();
		//		Page<Map<String, Object>> storeAddressList = storeBaseService
		//				.findStoreAddressPage(null, null, id, null, pageable);
		//		model.addAttribute("storeAddressList",
		//				JsonUtils.toJson(storeAddressList.getContent()));
		//		model.addAttribute("storeAddressSize",
		//				storeAddressList.getContent() == null ? "0"
		//						: storeAddressList.getContent().size());

		List<Map<String, Object>> storeAddressList = storeBaseService.findStoreAddressList(id);
		model.addAttribute("storeAddressList",
				JsonUtils.toJson(storeAddressList));
		model.addAttribute("storeAddressSize", storeAddressList.size());

		List<Map<String, Object>> storeInvoiceInfoList = storeInvoiceInfoService.findListByStore(store.getId(),null);
		model.addAttribute("storeInvoiceInfoList",
				JsonUtils.toJson(storeInvoiceInfoList));

		model.addAttribute("warehouses", warehouses);
		model.addAttribute("isEdit", isEdit);
		model.addAttribute("isStore", isStore);

		List<Integer> types = storeBaseService.getTypes();
		model.addAttribute("types", types);

		if (store != null) {
			model.addAttribute("storeManagerList",
					JsonUtils.toJson(store.getStoreManagers()));
			model.addAttribute("storeManagerSize",
					store.getStoreManagers() == null ? "0"
							: store.getStoreManagers().size());
		}
		model.addAttribute("companyInfoId", WebUtils.getCurrentCompanyInfoId());

		// 获取经营品类
		List<Map<String, Object>> productType = storeBaseService.findBusinessCategory(store);
		model.addAttribute("productType", JsonUtils.toJson(productType));

		// 获取经营记录
		List<Map<String, Object>> businessRecord = storeBaseService.findBusinessRecord(store);
		model.addAttribute("businessRecord", JsonUtils.toJson(businessRecord));

		// 获取保证金
		List<Map<String, Object>> cautionMoney = storeBaseService.findCautionMoney(store);
		model.addAttribute("cautionMoney", JsonUtils.toJson(cautionMoney));

		// 获取保证金
		List<Map<String, Object>> storeMembers = storeMemberBaseService.findStoreMemberByMember(store.getId());
		model.addAttribute("storeMembers", JsonUtils.toJson(storeMembers));

		// 获取门店
		List<Map<String, Object>> shopInfos = shopInfoService.findListByStoreId(id);
		model.addAttribute("shopInfos", JsonUtils.toJson(shopInfos));

		// 获取联系人
		List<Map<String, Object>> storecontracts = storeBaseService.findStoreContract(store);
		model.addAttribute("storecontracts", JsonUtils.toJson(storecontracts));

		List<Map<String, Object>> storeCooperations = storeBaseService.findStoreCooperation(store);
		model.addAttribute("storeCooperations",
				JsonUtils.toJson(storeCooperations));

		// 业务类型
		filters.clear();
		filters.add(Filter.eq("code", "businessType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessTypes = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("businessTypes", businessTypes);

		// 经销商状态
		filters.clear();
		filters.add(Filter.eq("code", "distributorStatus"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> distributorStatus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("distributorStatus", distributorStatus);

		filters.clear();
		filters.add(Filter.eq("code", "sbu"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> sbus = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("sbus", sbus);

		//销售体系
		filters.clear();
		filters.add(Filter.eq("code", "SaleStyle"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleStyle = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleStyles", saleStyle);
		model.addAttribute("code", code);
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		model.addAttribute("nowDate", df.format(new Date()));

		//订单附件
		String StoreAttach_json = JsonUtils.toJson(storeBaseService.findListByStoreId(store.getId()));
		model.addAttribute("StoreAttach_json", StoreAttach_json);

		//sbu
		List<Map<String, Object>> sbuList = storeSbuService.findStoreSbu(id);
		model.addAttribute("sbu_json", JsonUtils.toJson(sbuList));
		filters.clear();
		filters.add(Filter.eq("store", store));
		filters.add(Filter.eq("companyInfoId",
				WebUtils.getCurrentCompanyInfoId()));
		List<Contract> contractList = contractService.findList(null,
				filters,
				null);
		model.addAttribute("contractList", contractList);
		//客户附件
		List<Map<String, Object>> storeAttachs = storeBaseService.findStoreAttach(id, 0);
		model.addAttribute("store_attach0", JsonUtils.toJson(storeAttachs));
		List<Map<String, Object>> storeAttachs1 = storeBaseService.findStoreAttach(id, 1);
		model.addAttribute("store_attach1", JsonUtils.toJson(storeAttachs1));
		List<Map<String, Object>> storeAttachs2 = storeBaseService.findStoreAttach(id, 2);
		model.addAttribute("store_attach2", JsonUtils.toJson(storeAttachs2));

		model.addAttribute("visualReport",
				visualReportService.findVisualReports(storeMemberBaseService.getCurrent().getId(),net.shopxx.member.entity.VisualReport.Type.store));

		String storeFullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByElseSnAndType(store.getSn(), 103));
		model.addAttribute("storeFullLink_json", storeFullLink_json);

		//机构区域
		filters.clear();
		filters.add(Filter.eq("code", "saleOrgRegion"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> saleOrgRegions = systemDictBaseService.findList(null,
				filters,
				null);
		model.addAttribute("saleOrgRegions", saleOrgRegions);

		return CommonUtil.getFolderPrefix(code) + "/member/store/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(Store store, Long storeMemberId, Long saleStyleId,
					 Long areaId, Long salesPlatformId, Long saleOrgId,
					 Long businessTypeId, Long memberRankId, BankCard bankCard,
					 Long bAreaId, String bAddress, Boolean bIsEnabled, String bMobile,
					 Long distributorStatusId, Long changeMemberRankId, Long sbuId,
					 Long customerTypeId, Long gradenId, Long storeSalesAreaId,
					 Long createById,Long agentPropertyId,Long storeIdLink5) {
		/*
		 * if (storeBaseService.exists(Filter.eq("name", store.getName()),
		 * Filter.ne("id", store.getId()), Filter.eq("companyInfoId",
		 * WebUtils.getCurrentCompanyInfoId()))) { // 客户名称已存在 return
		 * error("160001"); }
		 */
		if (store.getTaxRate() != null) {
			store.setTaxRate(store.getTaxRate().divide(new BigDecimal(100)));
		}
		/*
		 * if (sbuId != null) { SystemDict sbu =
		 * systemDictBaseService.find(sbuId); if(sbu != null){
		 * store.setSbu(sbu); }else{ return error("请选择sbu"); } }else{ return
		 * error("请选择sbu"); }
		 */
		List<StoreAddress> addressList = store.getStoreAddress();
		for (Iterator<StoreAddress> iterator = addressList.iterator(); iterator.hasNext();) {
			StoreAddress storeAddress = (StoreAddress) iterator.next();
			if (storeAddress == null || storeAddress.getAddress() == null) {
				iterator.remove();
			}
			if (storeAddress.getSalesArea().getId() == null) {
				return error("请选择销售区域");
			}
		}
		SystemDict sbu = systemDictBaseService.find(sbuId);
		store.setSbu(sbu);
		SystemDict graden = systemDictBaseService.find(gradenId);
		store.setGraden(graden);
		SystemDict agentProperty = systemDictBaseService.find(agentPropertyId);
		store.setAgentProperty(agentProperty);//经销商属性
		SystemDict customerType = systemDictBaseService.find(customerTypeId);
		store.setCustomerType(customerType);
		SystemDict saleStyle = systemDictBaseService.find(saleStyleId);
		store.setSaleStyle(saleStyle);
		if (store.getHeadNewArea() == null
				|| store.getHeadNewArea().getId() == null) {
			store.setHeadNewArea(null);
		}
		storeBaseService.update(store,
				storeMemberId,
				areaId,
				saleOrgId,
				memberRankId,
				salesPlatformId,
				businessTypeId,
				distributorStatusId,
				bankCard,
				bAreaId,
				bAddress,
				bMobile,
				bIsEnabled,
				changeMemberRankId,
				null,
				storeSalesAreaId,
				createById,
				storeIdLink5);
		//以地址明细id作为外部编号传到ERP,作为KA在ERP的地址外部编码的唯一标识
		Store s = storeBaseService.find(store.getId());
		List<StoreAddress> lists = s.getStoreAddress();
		for (StoreAddress sas : lists) {
			if (sas.getOutTradeNo() == null) {
				sas.setOutTradeNo(sas.getId().toString());
				storeAddressService.update(sas);
			}

		}
		return success().addObjX(store.getId());
	}


	/**
	 * 客户信息变更
	 */
	@RequestMapping(value = "/update_store", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg updateStore(Store store) {
		storeBaseService.updateStore1(store);
		//以地址明细id作为外部编号传到ERP,作为KA在ERP的地址外部编码的唯一标识
		Store s = storeBaseService.find(store.getId());
		List<StoreAddress> lists = s.getStoreAddress();
		for (StoreAddress sas : lists) {
			if (sas.getOutTradeNo() == null) {
				sas.setOutTradeNo(sas.getId().toString());
				storeAddressService.update(sas);
			}

		}
		return success().addObjX(store.getId());
	}

	/**
	 * 客户信息变更-财务
	 */
	@RequestMapping(value = "/update_store_finance", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg updateStoreFinance(Store store) {
		storeBaseService.updateStoreFinance(store);
		//以地址明细id作为外部编号传到ERP,作为KA在ERP的地址外部编码的唯一标识
		Store s = storeBaseService.find(store.getId());
		List<StoreAddress> lists = s.getStoreAddress();
		for (StoreAddress sas : lists) {
			if (sas.getOutTradeNo() == null) {
				sas.setOutTradeNo(sas.getId().toString());
				storeAddressService.update(sas);
			}
		}
		return success().addObjX(store.getId());
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/updateStoreAddress", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg updateStoreAddress(Long storeId, Integer flag, Long areaId,
			Long salesAreaId, StoreAddress storeAddress, Boolean isEnabled) {
		storeBaseService.storeAddressIntf(storeId,
				flag,
				areaId,
				salesAreaId,
				storeAddress);
		return success();
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_store", method = RequestMethod.GET)
	public String selectStore(Pageable pageable, Integer multi, Type type,
			Long saleOrgId, Integer isSelect, Integer isCustomer,String user,
			Long sbuId, ModelMap model) {

		model.addAttribute("multi", multi);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("isSelect", isSelect);
		model.addAttribute("isSelect", isSelect);
		model.addAttribute("isCustomer", isCustomer);
		List<Integer> types = storeBaseService.getTypes();
		model.addAttribute("types", types);
		model.addAttribute("user", user);
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		/** 尚高 */
		if (companyInfoId != null && companyInfoId == 19) {
			model.addAttribute("isSl", 1);
		}
		return "/member/store/select_store";
	}

	@RequestMapping(value = "/select_store2", method = RequestMethod.GET)
	public String selectStore2(Pageable pageable, Integer multi, Type type,
			Integer isSl, ModelMap model) {

		model.addAttribute("multi", multi);
		model.addAttribute("type", type);
		List<Integer> types = storeBaseService.getTypes();
		model.addAttribute("types", types);
		return "/member/store/select_store2";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/select_store_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectStoreData(String name, String sn, Long saleOrgId,
			String saleOrgName, Integer[] type, Pageable pageable,
			Integer isSl, Integer isSelect, Integer isCustomer,
			String outTradeNo, String alias,String grantCode,
			String user, ModelMap model, Long sbuId) {
		
		Page<Map<String, Object>> page;
		// list列表选客户弹框没有机构
		if (saleOrgId == null) {
			isSelect = null;
			// 编辑选客户弹框	
		}else {
			isSelect = 1;
		}
		if (isSl != null && isSl == 1) {
			page = storeBaseService.findStorePageWithBalance(name,outTradeNo,grantCode,pageable);
		}else {
			Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
			page = storeBaseService.findPage(name,sn,outTradeNo,null,null,type,null,saleOrgId,
					companyInfoId,null,null,null,true,isSelect/* 非空，则为弹框查询客户 */,isCustomer,
					alias,grantCode,null,null,null,null,user,pageable,sbuId);
		}
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	
	/**
	 * 移动端业务员计划选择经销商数据
	 */
	@ResponseBody
	@RequestMapping(value = "/findStoreMobileData", method = RequestMethod.POST)
	public ResultMsg findStoreMobileData(String name, String sn, Long saleOrgId,
	        String saleOrgName, Integer[] type, Pageable pageable,
	        Integer isSl, Integer isSelect, Integer isCustomer,
	        String outTradeNo, String alias, ModelMap model) {
	    StoreMember storeMember = storeMemberService.getCurrent();
//	    if (storeMember.getIsSalesman() == null || !storeMember.getIsSalesman()) {
//	        return success(); 
//	    }
	    // 经销商状态
	    List<Filter> fis = new ArrayList<Filter>();
        fis.add(Filter.eq("code", "distributorStatus"));
        fis.add(Filter.eq("isEnabled", true));
        fis.add(Filter.isNotNull("parent"));
        List<SystemDict> distributorStatus = systemDictBaseService.findList(null, fis, null);
        Long distributorStatusId = null;
        for (SystemDict item : distributorStatus) {
            if ("有效".equals(item.getValue().toString())) {
                distributorStatusId = item.getId();
                break;
            }
        }
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        
        // 获取当前区域经理下的经销商
//        Iterator<Map<String, Object>> iterator = data.iterator();
//        List<Map<String, Object>> storeList = new ArrayList<Map<String, Object>>();
        // 省长看整个省的经销商
        // 获取区域管理部下的所有有效的省份机构
        // 父机构
        ArrayList<Filter> filter = new ArrayList<Filter>();
        filter.add(Filter.eq("companyInfoId", companyInfoId));
        filter.add(Filter.eq("name", "区域管理部"));
        SaleOrg saleOrg = saleOrgService.find(filter);
        // 子机构 
        filter.clear();
        filter.add(Filter.eq("companyInfoId", companyInfoId));
        filter.add(Filter.eq("parent", saleOrg.getId()));
        List<SaleOrg> saleOrgList = saleOrgService.findList(null, filter, null);
        Map<Long, Object> soMap = new HashMap<Long, Object>();
        for (SaleOrg item : saleOrgList) {
            soMap.put(item.getId(), "0");
        }
        // 判断当前用户是否是省长
        filter.clear();
        filter.add(Filter.eq("storeMember", storeMember));
        List<StoreMemberSaleOrg> storeMemberSaleOrgs = storeMemberSaleOrgBaseService.findList(null, filter, null);
        Map<Long, Object> soids = new HashMap<Long, Object>();
        List<String> soList = new ArrayList<String>();
        // 获取省长职位、机构
        filter.clear();
        filter.add(Filter.eq("companyInfoId", companyInfoId));
        filter.add(Filter.eq("name", "地板中心区域总监"));
        Post post = postBaseService.find(filter);
        for (StoreMemberSaleOrg storeMemberSaleOrg : storeMemberSaleOrgs) {
            Long sid = storeMemberSaleOrg.getSaleOrg().getId();
            if (soMap.containsKey(sid)) {
                filter.clear();
                filter.add(Filter.eq("companyInfoId", companyInfoId));
                filter.add(Filter.eq("storeMember", storeMember));
                filter.add(Filter.eq("saleOrg", storeMemberSaleOrg.getSaleOrg()));
                filter.add(Filter.eq("post", post));
                StoreMemberSaleOrgPost storeMemberSaleOrgPost = storeMemberSaleOrgPostService.find(filter);
                if (storeMemberSaleOrgPost != null) {
                    soids.put(sid, 0);
                    soList.add(sid.toString());
                }
            }
        }
        
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("distributorStatusId", distributorStatusId);  // 经销商状态
        param.put("name", name);  // 经销商名称
        if (soList.size() > 0) {
            param.put("saleOrgId", StringUtils.join(soList, ","));
        } else {
            param.put("saleOrgId", null);
        }
        
        List<Map<String, Object>> data = storeBaseService.findMobileData(param);
        
	    String jsonPage = JsonUtils.toJson(data);
	    return success(jsonPage);
	}

	/**
	 * 选择地址
	 */
	@RequestMapping(value = "/select_store_address", method = RequestMethod.GET)
	public String selectStoreAddress(Pageable pageable, Long storeId,
			Integer multi, Type type, ModelMap model) {
		model.addAttribute("storeId", storeId);
		return "/member/store/select_store_address";
	}

	/**
	 * 选择地址
	 */
	@RequestMapping(value = "/select_store_address_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectStoreAddressData(String mobile, String consignee,
			String address, Long storeId,String areaName, Pageable pageable, ModelMap model) {
		// System.out.println("storeId1--"+storeId+"mobile"+mobile+consignee);
		Page<Map<String, Object>> page = storeBaseService.findStoreAddressPage(mobile,
				consignee,
				storeId,
				address,
				areaName,
				pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 选择发票抬头
	 */
	@RequestMapping(value = "/select_store_invoice", method = RequestMethod.GET)
	public String selectStoreInvoice(Pageable pageable, Long storeId,
			Integer multi, Type type, ModelMap model) {
		model.addAttribute("storeId", storeId);
		return "/member/store/select_store_invoice";
	}

	/**
	 * 选择发票抬头
	 */
	@RequestMapping(value = "/select_store_invoice_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg selectStoreInvoicesData(String mobile, String consignee,
			String address, Long storeId, Pageable pageable, ModelMap model,
			String invoiceTitle) {
		List<Map<String, Object>> lists = storeInvoiceInfoService.findListByStore(storeId,invoiceTitle);
		String jsonPage = JsonUtils.toJson(lists);
		return success(jsonPage);
	}

	@RequestMapping(value = "/check_name", method = RequestMethod.GET)
	public @ResponseBody
	boolean checkName(String name, Long id) {

		Long companyInfoId = companyInfoBaseService.getCurrentId();
		if (id == null) {
			return !storeBaseService.exists(Filter.eq("name", name),
					Filter.eq("companyInfoId", companyInfoId));
		}
		else {
			return !storeBaseService.exists(Filter.eq("name", name),
					Filter.ne("id", id),
					Filter.eq("companyInfoId", companyInfoId));
		}
	}

	@RequestMapping(value = "/check_sn", method = RequestMethod.GET)
	public @ResponseBody
	boolean checkSn(String sn, Long id) {
		if (id == null) {
			return !storeBaseService.exists(Filter.eq("sn", sn),
					Filter.isNotNull("sn"));
		}
		else {
			return !storeBaseService.exists(Filter.eq("sn", sn),
					Filter.isNotNull("sn"),
					Filter.ne("id", id));
		}
	}

	/**
	 * Excel导入
	 * 
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file, HttpServletResponse response,
			ModelMap model) {

		try {
			storeBaseService.storeImport(file);
			return ResultMsg.success();
		}
		catch (Exception e) {
			LogUtils.error("导入客户", e);
			return ResultMsg.error(e.getMessage());
		}
	}

	/**
	 * 客户地址Excel导入
	 *
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/import_excel_address", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importAddressFromExcel(MultipartFile file,
			HttpServletResponse response, ModelMap model) {

		try {
			storeBaseService.addressImport(file);
			return ResultMsg.success();
		}
		catch (Exception e) {
			LogUtils.error("导入地址", e);
			return ResultMsg.error(e.getMessage());
		}
	}

	/**
	 * Excel导入 kb cw
	 * 
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/import_excel_kb", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcelKb(MultipartFile file,
			HttpServletResponse response, ModelMap model) {

		try {
			storeBaseService.storeImportKb(file);
			return ResultMsg.success();
		}
		catch (Exception e) {
			LogUtils.error("导入客户", e);
			return ResultMsg.error(e.getMessage());
		}
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String name,String sn,String outTradeNo,
			String mobile,String serviceTelephone,Integer[] type,Long saleOrgId,Long memberRankId,
			String createBy,String saleOrgName,String outShopName,Integer onlineShopType,
			Long storeMemberId,String region,Long[] distributorStatusId,String headAddress,
			Pageable pageable) {
		
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Page<Map<String, Object>> page = storeBaseService.findPage(name,sn,outTradeNo,mobile,serviceTelephone,
				type,outShopName,saleOrgId,companyInfoId,memberRankId,createBy,saleOrgName,null,null,null,null,
				null,storeMemberId,region,distributorStatusId,headAddress,null,pageable,null);
		
		Integer size = (int)page.getTotal();
				
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String name, String sn,
			String outTradeNo, String mobile, String serviceTelephone,
			Integer[] type, Long saleOrgId, Long memberRankId,
			String outShopName, Integer onlineShopType, Pageable pageable,
			ModelMap model, Integer page,Long storeMemberId,String region,
			Long[] distributorStatusId,String headAddress) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Map<String, Object>> data = storeBaseService.findItemList(name,
				sn,
				outTradeNo,
				mobile,
				serviceTelephone,
				type,
				outShopName,
				saleOrgId,
				companyInfoId,
				memberRankId,
				null,
				null,
				page,
				size,
				storeMemberId,
				region,
				distributorStatusId,
				headAddress);
		return this.getModelAndView(data, model);

	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {
		List<Map<String, Object>> data = storeBaseService.findItemList(null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				ids,
				null,
				null,
				null,
				null,
				null,
				null);
		return this.getModelAndView(data, model);
	}

	public ModelAndView getModelAndView(List<Map<String, Object>> data,
			ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("platform_property") != null) {//0.运营管理中心 1.控股合资管理中心 2.营销服务中心 3.营销管理中心
				Integer platform_property = Integer.valueOf(str.get("platform_property").toString());
				if (platform_property == 1) {
					str.put("platform_property", "控股合资管理中心");
				}else if (platform_property == 2) {
					str.put("platform_property", "营销服务中心");
				}else if (platform_property == 3) {
					str.put("platform_property", "营销管理中心");
				}else if (platform_property == 0) {
					str.put("platform_property", "运营管理中心");
				}
			}
			//城市等级 0省级 1 地市级 2 区县级 3乡镇级
			if (str.get("account_type_code") != null) {
				Integer account_type_code = Integer.valueOf(str.get("account_type_code").toString());
				if (account_type_code == 1) {
					str.put("account_type_code", "地市级");
				}else if (account_type_code == 2) {
					str.put("account_type_code", "区县级");
				}else if (account_type_code == 3) {
					str.put("account_type_code", "乡镇级");
				}else if (account_type_code == 0) {
					str.put("account_type_code", "省级");
				}
			}
			//0.国内经销商 1.国际经销商 2.国产产品经销商 3.进口产品经销商 
			if (str.get("distributor_type") != null) {
				Integer distributor_type = Integer.valueOf(str.get("distributor_type").toString());
				if (distributor_type == 1) {
					str.put("distributor_type", "国际经销商");
				}else if (distributor_type == 2) {
					str.put("distributor_type", "国产产品经销商");
				}else if (distributor_type == 3) {
					str.put("distributor_type", "进口产品经销商");
				}else if (distributor_type == 0) {
					str.put("distributor_type", "国内经销商");
				}
			}
			//0.总经销商 1.省会城市经销商 2.平台经销商 3.经销商 4.分销商 
			if (str.get("sub_type") != null) {
				Integer sub_type = Integer.valueOf(str.get("sub_type").toString());
				if (sub_type == 1) {
					str.put("sub_type", "省会城市经销商");
				}else if (sub_type == 2) {
					str.put("sub_type", "平台经销商");
				}else if (sub_type == 3) {
					str.put("sub_type", "经销商");
				}else if (sub_type == 4) {
					str.put("sub_type", "分销商 ");
				}else if (sub_type == 0) {
					str.put("sub_type", "总经销商");
				}
			}
			if (str.get("create_date") != null) {
				String create_date = DateUtil.convert((Date) str.get("create_date"));
				if (create_date.length() > 9) {
					create_date = create_date.substring(0, 10);
				}
				str.put("create_date", create_date);
			}
			if (str.get("cancel_date") != null) {
				String cancel_date = DateUtil.convert((Date) str.get("cancel_date"));
				if (cancel_date.length() > 9) {
					cancel_date = cancel_date.substring(0, 10);
				}
				str.put("cancel_date", cancel_date);
			}
			if (str.get("active_date") != null) {

				String active_date = DateUtil.convert((Date) str.get("active_date"));
				if (active_date.length() > 9) {
					active_date = active_date.substring(0, 10);
				}
				str.put("active_date", active_date);
			}
			if (str.get("is_enabled") != null && (Boolean) str.get("is_enabled")) {
				str.put("is_enabled", "是");
			}else {
				str.put("is_enabled", "否");
			}
			if (str.get("is_join_false") != null && (Boolean) str.get("is_join_false")) {
				str.put("is_join_false", "否");
			}else {
				str.put("is_join_false", "是");
			}
			if (str.get("cancel_info_flag") != null && (Boolean) str.get("cancel_info_flag")) {
				str.put("cancel_info_flag", "是");
			}else {
				str.put("cancel_info_flag", "否");
			}
			if (str.get("cash_client_flag") != null && (Boolean) str.get("cash_client_flag")) {
				str.put("cash_client_flag", "是");
			}else {
				str.put("cash_client_flag", "否");
			}
			if (str.get("need_caution_paid") != null) {
				str.put("need_caution_paid",new BigDecimal(str.get("need_caution_paid").toString()).setScale(2,BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("real_caution_paid") != null) {
				str.put("real_caution_paid",new BigDecimal(str.get("real_caution_paid").toString()).setScale(2,BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("unpaid_caution_paid") != null) {
				str.put("unpaid_caution_paid",new BigDecimal(str.get("unpaid_caution_paid").toString()).setScale(2,BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("tax_rate") != null) {
				str.put("tax_rate", new BigDecimal(str.get("tax_rate").toString()).multiply(new BigDecimal("100")).intValue());
			}
			String areaRegionName = "";
			String areaCityName = "";
			String areaProvinceName = "";
			Area area = null;
			if (str.get("harea_Id") != null) {
				Long areaId = Long.valueOf(str.get("harea_Id").toString());
				area = areaBaseService.find(areaId);
			}
			if (str.get("harea_tree_path") != null && area != null) {
				String area_tree_path = str.get("harea_tree_path").toString();
				//数量
				Integer num = (area_tree_path.length() - area_tree_path.replace(",","").length())/ ",".length();
				//省
				if (num == 1) {
					str.put("areaRegionName", areaRegionName);
					str.put("areaCityName", areaCityName);
					str.put("areaProvinceName", area.getName() == null ? "" : area.getName());
				//省市
				}else if (num == 2) {
					str.put("areaRegionName", areaRegionName);
					str.put("areaCityName", area.getName() == null ? "" : area.getName());
					str.put("areaProvinceName", area.getParent().getName() == null ? "" : area.getParent().getName());
				//省市区
				}else if (num == 3) {
					str.put("areaRegionName", area.getName() == null ? "" : area.getName());
					str.put("areaCityName",area.getParent().getName() == null ? "" : area.getParent().getName());
					str.put("areaProvinceName",area.getParent().getParent().getName() == null ? "" : area.getParent().getParent().getName());
				}
			//不填
			}else {
				str.put("areaRegionName", areaRegionName);
				str.put("areaCityName", areaCityName);
				str.put("areaProvinceName", areaProvinceName);
			}
			//是否可发货
			if (str.get("shipping_state") != null) {
				Integer shipping_state = Integer.valueOf(str.get("shipping_state").toString());
				if(shipping_state==1){
					str.put("shipping_state", "是");
				}else{
					str.put("shipping_state", "否");
				}
			}else {
				str.put("shipping_state", "否");
			}
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		
		// 设置标题
		String[] header = {"客户编码",
				"客户名称",
				"客户简称",
				"客户介绍",
				"授权编号",
				"经销商姓名",
				"经销商状态",
				"区域经理",
				"业务类型",
				"价格类型",
				"是否启用",
				"平台性质",
				"销售平台",
				"机构",
				"税率",
				"城市等级",
				"省份",
				"地级城市",
				"区县城市",
				"乡镇城市",
				"销售区域",
				"区域",
				"SBU",
				"经销商类型",
				"经销商子类型",
				"销售品类",
				"法人代表",
				"总经销商",
				"经销商关系说明",
				"身份证信息",
				"固定号码",
				"手机号码",
				"加盟日期",
				"加盟档案编号",
				"加盟是否成功",
				"解约日期",
				"解约档案编号",
				"解约时是否资料齐全",
				"解约原因",
				"应缴品牌保证金",
				"实缴品牌保证金",
				"欠缴品牌保证金",
				"销量保证金",
				"缴纳情况/异常说明",
				"是否现金客户",
				"货币",
				"备注(门店地址)",
				"合同主体",
				"经销商地址",
				"是否可发货"};
		// 设置单元格取值
		String[] properties = { "out_trade_no",
				"name",
				"alias",
				"introduction",
				"grant_code",
				"dealer_name",
				"distributorStatusName",
				"store_member_name",
				"businessTypeName",
				"rank_name",
				"is_enabled",
				"platform_property",//0.运营管理中心 1.控股合资管理中心 2.营销服务中心 3.营销管理中心
				"sales_platform_name",
				"sale_org_name",
				"tax_rate",
				"account_type_code",//城市等级 0省级 1 地市级 2 区县级 3乡镇级
				"areaProvinceName",//省份
				"areaCityName",//地级城市
				"areaRegionName",//区县城市
				"country_name",//新增字段
				"head_salesarea",
				"region",
				"sbuName",
				"distributor_type",//0.国内经销商 1.国际经销商 2.国产产品经销商 3.进口产品经销商 
				"sub_type",//0.总经销商 1.省会城市经销商 2.平台经销商 3.经销商 4.分销商 
				"sales_category",
				"contact",
				"franchisee",
				"dealer_relation_ship",
				"identity",
				"fixed_number",
				"head_phone",//手机号码
				"active_date",//加盟日期
				"join_file_number",
				"is_join_false",
				"cancel_date",
				"unfile_number",
				"cancel_info_flag",
				"cancel_reason",
				"need_caution_paid",
				"real_caution_paid",
				"unpaid_caution_paid",
				"sales_deposit",
				"payment_status",
				"cash_client_flag",
				"currency_code",
				"description",
				"contract_subject",
				"head_address",
				"shipping_state"};

		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256};

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/synchronization", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg synchronization(String startTime, String endTime) {
	    String strStartTime =null;
	    String strEndTime =null;
	     Calendar c = Calendar.getInstance();
         c.setTime(new Date());
         Date d = c.getTime();
	    if (ConvertUtil.isEmpty(startTime)) {
	      SimpleDateFormat startformat = new SimpleDateFormat("yyyy-MM-dd");
          strStartTime = startformat.format(d);  
	    }else{
	      strStartTime = startTime;
	    }
       if (ConvertUtil.isEmpty(endTime)) {
           SimpleDateFormat endformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
           strEndTime = endformat.format(d);  
         }else{
           strEndTime = endTime;
         }
         
	    
//		if (ConvertUtil.isEmpty(startTime) && ConvertUtil.isEmpty(endTime)) {
//			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//			Calendar c = Calendar.getInstance();
//			c.setTime(new Date());
//			Date d = c.getTime();
//			String currentTime = format.format(d);
//			storeBaseService.synchronization(startTime, endTime, currentTime);
//		}
//		else {
//			storeBaseService.synchronization(startTime, endTime, null);
//		}
	    storeBaseService.synchronization(strStartTime, strEndTime, null);
		return success("同步成功");
	}

	/**
	 * 发票抬头Excel导入
	 *
	 * @param file
	 * @param response
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/import_excel_invoice", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importInvoiceFromExcel(MultipartFile file,
			HttpServletResponse response, ModelMap model) {

		try {
			storeBaseService.invoiceImport(file);
			return ResultMsg.success();
		}
		catch (Exception e) {
			LogUtils.error("导入发票", e);
			return ResultMsg.error(e.getMessage());
		}
	}
}