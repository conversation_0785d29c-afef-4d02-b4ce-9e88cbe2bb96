package net.shopxx.member.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.member.entity.BankCard;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.BankCardBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSbuService;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

@Controller("bankCardController")
@RequestMapping("/member/bankCard")
public class BankCardController extends BaseController {

	@Resource(name = "bankCardBaseServiceImpl")
	private BankCardBaseService bankCardBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/bankCard/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list_code(@PathVariable String code, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("code", code);
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/" + code + "/member/bankCard/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model,Long menuId) {
		model.addAttribute("menuId",menuId);
		return "/member/bankCard/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb_code(@PathVariable String code, ModelMap model,Long menuId) {
		model.addAttribute("code", code);
		model.addAttribute("menuId",menuId);
		return "/" + code + "/member/bankCard/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(Pageable pageable, Long saleOrgId, String bankName,
			String bankCardNo, String mobile,Long sbuId) {
		Page<Map<String, Object>> page = bankCardBaseService.findPage(pageable,
				saleOrgId,
				bankName,
				bankCardNo,
				mobile,
				null,
				sbuId,
				null,
				null);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);
		//查询所有sbu
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Map<String, Object>> sbu = storeMemberService.findSbuTy(storeMember.getId());
		if (sbu.size() > 0) {
			Long sbuIds = Long.parseLong(sbu.get(0).get("id").toString());
			model.addAttribute("sbuIds", sbuIds);
		}
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,
				filters,
				null);
		model.addAttribute("sbus", sbus);
		return "/member/bankCard/add";
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add/{code}", method = RequestMethod.GET)
	public String add_code(@PathVariable String code, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);
		model.addAttribute("code", code);
		return "/" + code + "/member/bankCard/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(BankCard bankCard, Long organizationId, Long saleOrgId,Long sbuId) {
		if (bankCard.getBankName() == null) {
			//银行名称不能为空
			return error("18450");
		}
		bankCard.setStore(storeBaseService.getMainStore());
		Organization organization = organizationService.find(organizationId);
		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		bankCard.setOrganization(organization);
		bankCard.setSaleOrg(saleOrg);
		bankCard.setSbu(sbuService.find(sbuId));
		bankCardBaseService.save(bankCard);
		
		return success().addObjX(bankCard.getId());

	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {

		BankCard bankCard = bankCardBaseService.find(id);
		model.addAttribute("bankCard", bankCard);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null,
				filters,
				null);
		model.addAttribute("sbus", sbus);

		return "/member/bankCard/edit";
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit/{code}", method = RequestMethod.GET)
	public String edit_code(@PathVariable String code, Long id, ModelMap model) {

		BankCard bankCard = bankCardBaseService.find(id);
		model.addAttribute("bankCard", bankCard);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("type", 0));
		List<Organization> organizations = organizationService.findList(null,
				filters,
				null);
		model.addAttribute("organizations", organizations);
		model.addAttribute("code", code);
		return "/" + code + "/member/bankCard/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(BankCard bankCard, Long organizationId, Long saleOrgId,Long sbuId) {

		if (bankCard.getBankName() == null) {
			//银行名称不能为空
			return error("18450");
		}

		bankCard.setStore(storeBaseService.getMainStore());
		Organization organization = organizationService.find(organizationId);
		SaleOrg saleOrg = saleOrgBaseService.find(saleOrgId);
		bankCard.setOrganization(organization);
		bankCard.setSaleOrg(saleOrg);
		bankCard.setSbu(sbuService.find(sbuId));
		bankCardBaseService.update(bankCard);
		return success();
	}

	/**
	 * 列表
	 * @param model
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "select_bank_card", method = RequestMethod.GET)
	public String select_bank_card(Integer multi, Long saleOrgId,Long sbuId, 
			Boolean isTotalAccount,ModelMap model) {
		model.addAttribute("multi", multi);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("isTotalAccount", isTotalAccount);
		return "/member/bankCard/select_bank_card";

	}

	@RequestMapping(value = "select_bank_card/{code}", method = RequestMethod.GET)
	public String select_bank_card_code(@PathVariable String code,
			Integer multi, Long saleOrgId, ModelMap model) {
		model.addAttribute("multi", multi);
		model.addAttribute("saleOrgId", saleOrgId);
		model.addAttribute("code", code);
		return "/" + code + "/member/bankCard/select_bank_card";

	}

	@RequestMapping(value = "select_bank_card_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg select_bank_card_data(Pageable pageable, Long saleOrgId,
			String bankName, String bankCardNo, String mobile, Boolean isEnabled,
			Long sbuId,Boolean isTotalAccount,String bankCode) {
		
		isEnabled = true;
		Page<Map<String, Object>> page = bankCardBaseService.findPage(pageable,
				saleOrgId,
				bankName,
				bankCardNo,
				mobile,
				isEnabled,
				sbuId,
				isTotalAccount,
				bankCode);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg importFromExcel(MultipartFile file, HttpServletResponse response,
							  ModelMap model) {

		try {
			bankCardBaseService.importFromExcel(file);
			return ResultMsg.success();
		}
		catch (Exception e) {
			LogUtils.error("导入收款账号", e);
			return ResultMsg.error(e.getMessage());
		}
	}
}