package net.shopxx.member.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.VisualReport;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.member.service.VisualReportService;

@Controller("visualReportController")
@RequestMapping("/member/visual_report")
public class VisualReportController extends BaseController {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "visualReportServiceImpl")
	private VisualReportService visualReportService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model,String menuId) {
		model.addAttribute("menuId",menuId);
		return "/member/visual_report/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model,Long userId,Long menuId) {
		model.addAttribute("menuId",menuId);
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/member/visual_report/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String category, Integer type, Pageable pageable) {
		List<Filter> filters = new ArrayList<Filter>();
		if (category != null && !"".equals(category)) {
			filters.add(Filter.eq("category", category));
		}
		if (type != null) {
			filters.add(Filter.eq("type", type));
		}
		Page<VisualReport> page = visualReportService.findPage(filters, null, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);

	}

	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {

		return "/member/visual_report/add";
	}

	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		VisualReport visualReport = visualReportService.find(id);
		model.addAttribute("vr", visualReport);
		return "/member/visual_report/edit";
	}

	/**
	 * 新增
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveUpdate(VisualReport visualReport, ModelMap model) {
		if (visualReport.getName() == null || "".equals(visualReport.getName())) {
			return error("请输入名称");
		}
		if (visualReport.getCategory() == null || "".equals(visualReport.getCategory())) {
			return error("请输入分类");
		}
		if (visualReport.getSerialNumber() == null || "".equals(visualReport.getSerialNumber())) {
			return error("请输入排序");
		}
		visualReportService.save(visualReport);

		return success().addObjX(visualReport.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(VisualReport visualReport, Long id, ModelMap model) {
		// VisualReport visualReports = visualReportService.find(id);
		// visualReports.setIsDefault(isDefault);
		if (visualReport.getName() == null || "".equals(visualReport.getName())) {
			return error("请输入名称");
		}
		if (visualReport.getCategory() == null || "".equals(visualReport.getCategory())) {
			return error("请输入分类");
		}
		if (visualReport.getSerialNumber() == null || "".equals(visualReport.getSerialNumber())) {
			return error("请输入排序");
		}
		visualReportService.update(visualReport);

		return success().addObjX(visualReport.getId());
	}

	/**
	 * 任务完成情况
	 */
	@RequestMapping(value = "/task_completion", method = RequestMethod.POST)
	public @ResponseBody ResultMsg TaskCompletion(Long[] visualReportId) {
		if (visualReportId == null) {
			visualReportId = visualReportService.findVisualReportId("A");
		}
		// SBU需要基于当前登录人SBU过滤 全国
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		// 获取当前登录用户sbu
		List<Long> sbu = visualReportService.findSbu(storeMember);
		List<Map<String, Object>> map = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String jsonPage = "";
		for (Long id : visualReportId) {
			VisualReport visualReport = visualReportService.find(id);
			String name = visualReport.getName();
			if (name != null) {
				if (name.equals("全国客户销量排名")) {
					Map<String, Object> mp = new HashMap<String, Object>();
					map = visualReportService.findTaskCompletionStore(sbu, null, null);
					mp.put(visualReport.getId().toString(), map);
					list.add(mp);
				}
				if (name.equals("全国销量排名")) {
					Map<String, Object> mp = new HashMap<String, Object>();
					map = visualReportService.findTaskCompletion(sbu, null, null);
					mp.put(visualReport.getId().toString(), map);
					list.add(mp);
				}
				if (name.equals("全国品类排名")) {
					Map<String, Object> mp = new HashMap<String, Object>();
					map = visualReportService.findTaskCompletionCategory(sbu, null, null);
					mp.put(visualReport.getId().toString(), map);
					list.add(mp);
				}
				if (name.equals("全国产品排名")) {
					Map<String, Object> mp = new HashMap<String, Object>();
					map = visualReportService.findTaskCompletionProduct(sbu, null, null);
					mp.put(visualReport.getId().toString(), map);
					list.add(mp);
				}
			}
		}
		jsonPage = JsonUtils.toJson(list);
		return success(jsonPage);
	}

	/**
	 * 产品销量排行
	 */
	@RequestMapping(value = "/product_sales_ranking", method = RequestMethod.POST)
	public @ResponseBody ResultMsg ProductSalesRanking(Long[] visualReportId) {
		if (visualReportId == null) {
			visualReportId = visualReportService.findVisualReportId("B");
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		// SBU
		List<Long> sbu = visualReportService.findSbu(storeMember);
		// 机构
		List<Long> saleOrg = visualReportService.findSaleOrg(storeMember);
		// 客户
		List<Long> store = visualReportService.findStore(storeMember);
		List<Map<String, Object>> map = null;
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String jsonPage = "";
		for (Long id : visualReportId) {
			VisualReport visualReport = visualReportService.find(id);
			String name = visualReport.getName();
			if (name != null) {
				if (name.equals("客户销量排名")) {
					Map<String, Object> mp = new HashMap<String, Object>();
					map = visualReportService.findTaskCompletionStore(sbu, saleOrg, store);
					mp.put(visualReport.getId().toString(), map);
					list.add(mp);
				}
				if (name.equals("销量排名")) {
					Map<String, Object> mp = new HashMap<String, Object>();
					map = visualReportService.findTaskCompletion(sbu, saleOrg, store);
					mp.put(visualReport.getId().toString(), map);
					list.add(mp);
				}
				if (name.equals("品类排名")) {
					Map<String, Object> mp = new HashMap<String, Object>();
					map = visualReportService.findTaskCompletionCategory(sbu, saleOrg, store);
					mp.put(visualReport.getId().toString(), map);
					list.add(mp);
				}
				if (name.equals("产品排名")) {
					Map<String, Object> mp = new HashMap<String, Object>();
					map = visualReportService.findTaskCompletionProduct(sbu, saleOrg, store);
					mp.put(visualReport.getId().toString(), map);
					list.add(mp);
				}
			}
		}
		jsonPage = JsonUtils.toJson(list);
		return success(jsonPage);
	}
	
	/**
	 * 删除
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	public @ResponseBody ResultMsg delete(Long id) {
		if (id == null ) {
			return error("请选择单据");
		}
		visualReportService.delete(id);
		return success();
	}

}
