package net.shopxx.member.entity;

import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

public class PcMenuRole extends BaseEntity {


    private PcMenu pcMenu;    //菜单

    private PcRole pcRole;   //角色


    @ManyToOne(fetch = FetchType.LAZY)
    public PcMenu getPcMenu() {
        return pcMenu;
    }

    public void setPcMenu(PcMenu pcMenu) {
        this.pcMenu = pcMenu;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public PcRole getPcRole() {
        return pcRole;
    }

    public void setPcRole(PcRole pcRole) {
        this.pcRole = pcRole;
    }
}
