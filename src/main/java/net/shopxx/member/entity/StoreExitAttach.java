package net.shopxx.member.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;

@Entity
@Table(name = "xx_store_exit_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_store_exit_attach_sequence")
public class StoreExitAttach extends BaseEntity{
	
	private static final long serialVersionUID = -560273712917656755L;
	
	private StoreExit storeExit;
	
	/** 1.退出申请签字版 2.品牌保证金收据 */
	private Integer type;
	
	/** 附件URL */
	private String url;

	/** 备注 */
	private String memo;

	/** 文件名 */
	private String fileName;

	/**文件名*/
	private String name;

	/**文件后缀*/
	private String suffix;

	/** 序号 */
	private Integer seq;

	/** 上传人 */
	private StoreMember storeMember;
	
	/**
	 * @return the order
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "store_exits", nullable = false, updatable = false)
	public StoreExit getStoreExit() {
		return storeExit;
	}

	public void setStoreExit(StoreExit storeExit) {
		this.storeExit = storeExit;
	}
	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	/**
	 * @return the url
	 */
	public String getUrl() {
		return url;
	}

	/**
	 * @param url the url to set
	 */
	public void setUrl(String url) {
		this.url = url;
	}

	/**
	 * @return the memo
	 */
	public String getMemo() {
		return memo;
	}

	/**
	 * @param memo the memo to set
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/**
	 * 排序
	 * @return the seq
	 */
	public Integer getSeq() {
		return seq;
	}

	/**
	 * 排序
	 * @param seq the seq to set
	 */
	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSuffix() {
		return suffix;
	}

	public void setSuffix(String suffix) {
		this.suffix = suffix;
	}

}
