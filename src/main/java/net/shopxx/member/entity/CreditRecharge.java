package net.shopxx.member.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.order.entity.CustomerContract;
import net.shopxx.util.SnUtil;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 临时额度充值
 * 
 */
@Entity
@Table(name = "xx_credit_recharge")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_credit_recharge_sequence")
public class CreditRecharge extends ActWfBillEntity {

	private static final long serialVersionUID = -8323455373046981882L;

	/** 充值编号 */
	private String sn;

	/** 充值状态 0未审核 1已审核 2已驳回 */
	private Integer status;

	/** 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.作废 4.已关闭 */
	private Integer docStatus;

	/** 充值类型 0未生效 1已生效 2已过期 */
	private Integer type;

	/** 计划充值金额 */
	private BigDecimal amount;

	/** 实际充值金额 */
	private BigDecimal actualAmount;

	/** 创建人 */
	private StoreMember creator;

	/** 审核人 */
	private StoreMember operator;

	/** 申请客户 */
	private Store store;

	/** 审核时间 */
	private Date checkDate;

	/** 申请备注 */
	private String memo;

	/** 审核备注 */
	private String note;

	/** 附件 */
	private String image;

	/** 附件2 */
	private String image2;

	/** 附件3 */
	private String image3;

	/** 使用起始日期 */
	private Date startDate;

	/** 使用结束日期 */
	private Date endDate;

	/** 申请机构 */
	private SaleOrg saleOrg;

	/** 申请用户 */
	private StoreMember storeMember;

	/** 类型：0授信申请 1信贷额度 */
	private int rechargeType;

	/** 经营组织 */
	private Organization organization;

	/** 附件项 */
	private List<CreditAttach> creditAttachs = new ArrayList<CreditAttach>();

	/** SBU */
	private Sbu sbu;

	/** 经销商合同 */
	private CustomerContract customerContract;

    /**
     * 售后单
     */
    private Aftersale aftersale;


	/**
	 * link4售后单
	 */
	private String fourAftersaleSn;

	/**
	 * 获取收入金额
	 * 
	 * @return 收入金额
	 */
	@Column(nullable = false, precision = 21, scale = 6)
	public BigDecimal getAmount() {
		return amount;
	}

	/**
	 * 设置收入金额
	 * 
	 * @param credit 收入金额
	 */
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	/**
	 * 获取 创建人
	 * 
	 * @return creator
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getCreator() {
		return creator;
	}

	/**
	 * 设置 创建人
	 * 
	 * @param creator 创建人
	 */
	public void setCreator(StoreMember creator) {
		this.creator = creator;
	}

	/**
	 * 获取操作员
	 * 
	 * @return 操作员
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getOperator() {
		return operator;
	}

	/**
	 * 设置操作员
	 * 
	 * @param operator 操作员
	 */
	public void setOperator(StoreMember operator) {
		this.operator = operator;
	}

	/**
	 * 获取备注
	 * 
	 * @return 备注
	 */
	@Length(max = 200)
	@Column(updatable = true)
	public String getMemo() {
		return memo;
	}

	/**
	 * 设置备注
	 * 
	 * @param memo 备注
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/**
	 * 获取客户
	 * 
	 * @return
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}

	/**
	 * 设置客户
	 * 
	 * @param store
	 */
	public void setStore(Store store) {
		this.store = store;
	}

	/**
	 * @return the sn
	 */
	@Column(unique = true)
	public String getSn() {
		return sn;
	}

	/**
	 * @param sn the sn to set
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	/**
	 * 获取 附件2
	 * 
	 * @return image2
	 */
	public String getImage2() {
		return image2;
	}

	/**
	 * 设置 附件2
	 * 
	 * @param image2 附件2
	 */
	public void setImage2(String image2) {
		this.image2 = image2;
	}

	/**
	 * 获取 附件3
	 * 
	 * @return image3
	 */
	public String getImage3() {
		return image3;
	}

	/**
	 * 设置 附件3
	 * 
	 * @param image3 附件3
	 */
	public void setImage3(String image3) {
		this.image3 = image3;
	}

	/**
	 * 获取 实际充值金额
	 * 
	 * @return actualAmount
	 */
	@Column(nullable = false, precision = 21, scale = 6)
	public BigDecimal getActualAmount() {
		return actualAmount;
	}

	/**
	 * 设置 实际充值金额
	 * 
	 * @param actualAmount 实际充值金额
	 */
	public void setActualAmount(BigDecimal actualAmount) {
		this.actualAmount = actualAmount;
	}

	/**
	 * 获取 充值状态 0未审核 1已审核 2已驳回
	 * 
	 * @return status
	 */
	public Integer getStatus() {
		return status;
	}

	/**
	 * 设置 充值状态 0未审核 1已审核 2已驳回
	 * 
	 * @param status 充值状态 0未审核 1已审核 2已驳回
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}

	/**
	 * 获取 充值类型 0未生效 1已生效 2已过期
	 * 
	 * @return type
	 */
	public Integer getType() {
		return type;
	}

	/**
	 * 设置 充值类型 0未生效 1已生效 2已过期
	 * 
	 * @param type 充值类型 0未生效 1已生效 2已过期
	 */
	public void setType(Integer type) {
		this.type = type;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getDocStatus() {
		return docStatus;
	}

	public void setDocStatus(Integer docStatus) {
		this.docStatus = docStatus;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	@OneToMany(mappedBy = "creditRecharge", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<CreditAttach> getCreditAttachs() {
		return creditAttachs;
	}

	public void setCreditAttachs(List<CreditAttach> creditAttachs) {
		this.creditAttachs = creditAttachs;
	}

	public int getRechargeType() {
		return rechargeType;
	}

	public void setRechargeType(int rechargeType) {
		this.rechargeType = rechargeType;
	}

	/**
	 * 持久化前
	 */
	@PrePersist
	public void prePersist() {
		if (getStatus() == null) {
			setStatus(0);
		}
		if (getSn() == null) {
			setSn(SnUtil.generateSn());
		}
		if (getWfState() == null) {
			setWfState(0);
		}
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}

	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public CustomerContract getCustomerContract() {
		return customerContract;
	}

	public void setCustomerContract(CustomerContract customerContract) {
		this.customerContract = customerContract;
	}

    @ManyToOne(fetch = FetchType.LAZY)
    public Aftersale getAftersale() {
        return aftersale;
    }

    public void setAftersale(Aftersale aftersale) {
        this.aftersale = aftersale;
    }

	public String getFourAftersaleSn() {
		return fourAftersaleSn;
	}

	public void setFourAftersaleSn(String fourAftersaleSn) {
		this.fourAftersaleSn = fourAftersaleSn;
	}
}