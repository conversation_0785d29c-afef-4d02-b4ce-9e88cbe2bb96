package net.shopxx.member.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;

/**
 * 经销商终止附件
 */
@Entity
@Table(name = "xx_store_stop_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_store_stop_attach_sequence")
public class StoreStopAttach extends BaseEntity {

    private static final long serialVersionUID = -7943430871750441830L;

    /** 经销商终止退出 */
    private StoreStop storeStop;

    /**
     * 0、三年销量数据签字版，
     * 1、函件省长签字版
     * 2、函件Word版
     * 3、其他
     * 4、渠道附件
     */
    private Integer type;

    /** 附件URL */
    private String url;

    /** 备注 */
    private String memo;

    /** 文件名 */
    private String fileName;

    /**文件名*/
    private String name;

    /**文件后缀*/
    private String suffix;

    /** 序号 */
    private Integer seq;

    /** 上传人 */
    private StoreMember storeMember;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "storeStop", nullable = false, updatable = false)
    public StoreStop getStoreStop() {
        return storeStop;
    }

    public void setStoreStop(StoreStop storeStop) {
        this.storeStop = storeStop;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getStoreMember() {
        return storeMember;
    }

    public void setStoreMember(StoreMember storeMember) {
        this.storeMember = storeMember;
    }
}
