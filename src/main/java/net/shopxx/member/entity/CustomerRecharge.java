package net.shopxx.member.entity;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;

import javax.persistence.*;
import java.math.BigDecimal;
/**
 * Entity - 客户充值表
 */
@Entity
@Table(name = "xx_customer_recharge")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_customer_recharge_sequence")
public class CustomerRecharge extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/** 客户**/
	private Store store;
	
	/** 经营组织 */
	private Organization organization;
	
	/** sbu*/
	private Sbu sbu;
	
	/** 订单余额*/
	private BigDecimal orderBalance;
	
	/** 退货金额*/
	private BigDecimal returnBalance;
	
	/** 发货金额*/
	private BigDecimal shipmentBalance;
	
	/** 客户充值金额 */
	private BigDecimal balance;
	
	/** 政策金额*/
	private BigDecimal policyBalance;
	
	/** 授信额度 */
	private BigDecimal creditBalance;
	
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}
	public void setStore(Store store) {
		this.store = store;
	}
	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}
	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}
	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}
	public BigDecimal getBalance() {
		return balance;
	}
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}
	public BigDecimal getOrderBalance() {
		return orderBalance;
	}
	public void setOrderBalance(BigDecimal orderBalance) {
		this.orderBalance = orderBalance;
	}
	public BigDecimal getReturnBalance() {
		return returnBalance;
	}
	public BigDecimal getShipmentBalance() {
		return shipmentBalance;
	}
	public BigDecimal getPolicyBalance() {
		return policyBalance;
	}
	public BigDecimal getCreditBalance() {
		return creditBalance;
	}
	public void setReturnBalance(BigDecimal returnBalance) {
		this.returnBalance = returnBalance;
	}
	public void setShipmentBalance(BigDecimal shipmentBalance) {
		this.shipmentBalance = shipmentBalance;
	}
	public void setPolicyBalance(BigDecimal policyBalance) {
		this.policyBalance = policyBalance;
	}
	public void setCreditBalance(BigDecimal creditBalance) {
		this.creditBalance = creditBalance;
	}
}
