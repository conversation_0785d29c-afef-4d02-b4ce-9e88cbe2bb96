package net.shopxx.member.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.base.core.entity.BaseEntity;

/**
 * 用户角色
 * <AUTHOR>
 * @date 2017年7月11日 下午8:18:53
 */
@Entity
@Table(name = "xx_pc_user_role")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_pc_user_role_sequence")
public class PcUserRole extends BaseEntity {

	private static final long serialVersionUID = -7201295196206143284L;

	/** 用户 */
	private StoreMember storeMember;

	/** 角色 */
	private PcRole pcRole;

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public PcRole getPcRole() {
		return pcRole;
	}

	public void setPcRole(PcRole pcRole) {
		this.pcRole = pcRole;
	}
}
