/**
 * 
 */
package net.shopxx.member.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.base.core.entity.BaseEntity;

/**
 * <AUTHOR>
 * @date 2018年9月16日 下午5:29:00
 */
@Entity
@Table(name = "xx_store_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_store_attach_sequence")
public class StoreAttach extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7908766360661343312L;

	/** 订单 */
	private Store store;

	/** 附件URL */
	private String url;

	/** 备注 */
	private String memo;

	/** 文件名 */
	private String fileName;

	/**文件名*/
	private String name;

	/**文件后缀*/
	private String suffix;

	/** 序号 */
	private Integer seq;

	/** 上传人 */
	private StoreMember storeMember;
	
	private Integer type;

	/**
	 * @return the url
	 */
	public String getUrl() {
		return url;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "stores", nullable = false, updatable = false)
	public Store getStore() {
		return store;
	}

	public void setStore(Store store) {
		this.store = store;
	}

	/**
	 * @param url the url to set
	 */
	public void setUrl(String url) {
		this.url = url;
	}

	/**
	 * @return the memo
	 */
	public String getMemo() {
		return memo;
	}

	/**
	 * @param memo the memo to set
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/**
	 * 排序
	 * @return the seq
	 */
	public Integer getSeq() {
		return seq;
	}

	/**
	 * 排序
	 * @param seq the seq to set
	 */
	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSuffix() {
		return suffix;
	}

	public void setSuffix(String suffix) {
		this.suffix = suffix;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
}
