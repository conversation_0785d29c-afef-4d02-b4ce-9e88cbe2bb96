package net.shopxx.member.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SalesArea;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 客户地址
 */
@Entity
@Table(name = "xx_store_address")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_store_address_sequence")
public class StoreAddress extends BaseEntity {

	private static final long serialVersionUID = -7489510869825629267L;
	/**
	 * 店铺
	 */
	private Store store;
	/**
	 * 地址
	 */
	private String address;
	/**
	 * 手机号码
	 */
	private String mobile;
	/**
	 *  服务电话: 
	 */
	private String serviceTelephone;
	/**
	 * 区域
	 */
	private Area area;
	
	/**
	 * 销售区域
	 */
	private SalesArea salesArea;
	/**
	 * 收货人
	 */
	private String consignee;
	/***
	 * 邮编
	 */
	private String zipCode;
	/**
	 * 是否默认
	 */
	private Boolean isDefault;
	
	/**
	 * 地址类型  0.经销商地址  1.收货地址  2.收单地址  3.收单收货地址
	 */
	private Integer addressType;
	
	/** 外部编号 */
	private String outTradeNo;
	
	/**身份证号码*/
	private String cardId;
	
	/**是否失效  1是*/
	private Integer isInvalid;
	
	/**是否导入  0否 1是*/
	private Integer isImport;
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public Store getStore() {
		return store;
	}
	public void setStore(Store store) {
		this.store = store;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getServiceTelephone() {
		return serviceTelephone;
	}
	public void setServiceTelephone(String serviceTelephone) {
		this.serviceTelephone = serviceTelephone;
	}
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Area getArea() {
		return area;
	}
	public void setArea(Area area) {
		this.area = area;
	}
	public String getConsignee() {
		return consignee;
	}
	public void setConsignee(String consignee) {
		this.consignee = consignee;
	}
	public String getZipCode() {
		return zipCode;
	}
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	public Boolean getIsDefault() {
		return isDefault;
	}
	public void setIsDefault(Boolean isDefault) {
		this.isDefault = isDefault;
	}
	public Integer getAddressType() {
		return addressType;
	}
	public void setAddressType(Integer addressType) {
		this.addressType = addressType;
	}
	public String getOutTradeNo() {
		return outTradeNo;
	}
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}
	public String getCardId() {
		return cardId;
	}
	public void setCardId(String cardId) {
		this.cardId = cardId;
	}
	/** 
	 * 获取  是否失效  1是 
	 * @return isInvalid 是否失效  1是 
	 */
	public Integer getIsInvalid() {
		return isInvalid;
	}
	
	/** 
	 * 设置  是否失效  1是 
	 * @param isInvalid 是否失效  1是 
	 */
	public void setIsInvalid(Integer isInvalid) {
		this.isInvalid = isInvalid;
	}
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SalesArea getSalesArea() {
		return salesArea;
	}
	public void setSalesArea(SalesArea salesArea) {
		this.salesArea = salesArea;
	}
	
	public void setIsImport(Integer isImport) {
		this.isImport = isImport;
	}
	public Integer getIsImport() {
		return isImport;
	}
}