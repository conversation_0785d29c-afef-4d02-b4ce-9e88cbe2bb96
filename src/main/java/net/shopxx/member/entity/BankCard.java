package net.shopxx.member.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreRemove;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;

/**
 * Entity - 银行卡
 */
@Entity
@Table(name = "xx_bank_card")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_bank_card_sequence")
public class BankCard extends BaseEntity {

	private static final long serialVersionUID = 537762839588659427L;

	/** 用户 */
	private StoreMember storeMember;

	/** 客户 */
	private Store store;

	/**银行名称*/
	private String bankName;

	/**银行代号*/
	private String bankCode;

	/**开户地址*/
	private String address;

	/**开户地区*/
	private Area area;

	/**开户人*/
	private String createy;

	/**卡号*/
	private String bankCardNo;

	/** 企业 */
	private CompanyInfo company;

	/**是否启用*/
	private Boolean isEnabled;

	/**备注*/
	private String memo;

	/** 预留手机号码 */
	private String mobile;
	/** 货币id */
	private Long currencyId;

	/**组织*/
	private Organization organization;

	/** 申请机构 */
	private SaleOrg saleOrg;

	/** 大区 String */
	private String areaName;

	/** 是否内部账户*/
	private Boolean  isInternalAccount;
	
	/** 是否总账户*/
	private Boolean  isTotalAccount;
	
	/**sbu*/
	private Sbu sbu;

    /**
     * 是否新账号
     */
    private Boolean isNewBankCode;
	
	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getCreatey() {
		return createy;
	}

	public void setCreatey(String createy) {
		this.createy = createy;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Area getArea() {
		return area;
	}

	public void setArea(Area area) {
		this.area = area;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankCardNo() {
		return bankCardNo;
	}

	public void setBankCardNo(String bankCardNo) {
		this.bankCardNo = bankCardNo;
	}

	public Boolean getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}

	/**
	 * 获取预留手机号码
	 * @return mobile
	 */
	public String getMobile() {
		return mobile;
	}

	/**
	 * 设置预留手机号码
	 * @param mobile
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	

	/**
	 *  设置企业
	 * @return
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public CompanyInfo getCompany() {
		return company;
	}

	public void setCompany(CompanyInfo company) {
		this.company = company;
	}

	@PrePersist
	public void prePersist() {
//		if (StringUtils.isBlank(getMobile())) {
//			setMobile(getMember().getMobile());
//		}
	}

	@PreRemove
	public void preRemove() {
//		if (StringUtils.isBlank(getMobile())) {
//			setMobile(getMember().getMobile());
//		}
	}

	/**
	 * 获取 客户
	 * @date 2017年8月19日
	 * @return store
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}

	/**
	 * 设置 客户
	 * @date 2017年8月19日
	 * @param store 客户
	 */
	public void setStore(Store store) {
		this.store = store;
	}

	/**
	 * 获取 用户
	 * @date 2017年9月15日
	 * @return storeMember
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	/**
	 * 设置 用户
	 * @date 2017年9月15日
	 * @param storeMember 用户
	 */
	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	public Long getCurrencyId() {
		return currencyId;
	}

	public void setCurrencyId(Long currencyId) {
		this.currencyId = currencyId;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}

	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}

	public Boolean getIsInternalAccount() {
		return isInternalAccount;
	}

	public void setIsInternalAccount(Boolean isInternalAccount) {
		this.isInternalAccount = isInternalAccount;
	}

	public Boolean getIsTotalAccount() {
		return isTotalAccount;
	}

	public void setIsTotalAccount(Boolean isTotalAccount) {
		this.isTotalAccount = isTotalAccount;
	}

	public Boolean getIsNewBankCode() {
		return isNewBankCode;
	}

	public void setIsNewBankCode(Boolean newBankCode) {
		isNewBankCode = newBankCode;
	}
}