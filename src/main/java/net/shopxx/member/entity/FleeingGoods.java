package net.shopxx.member.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.product.entity.Product;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 窜货
 */
@Entity
@Table(name = "xx_fleeing_goods")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_fleeing_goods_sequence")
public class FleeingGoods extends ActWfBillEntity {

    private static final long serialVersionUID = 6537939491608801103L;

    /** 处罚单号 */
    private String sn;
    
    /** 客户 */
    private Store store;
    
    /** 状态 0已保存 1已生效 2已终止 3进行中 */
    private Integer status;

    /** 窜货时间 */
    private Date fleeingGoodsTime;

    /** 处罚类型 0：警告，1：处罚 */
    private Integer punishType;
    
    /** 窜货方省市区 */
    private Area fgRegionArea;

    /** 窜货方地区 */
    private String fleeingGoodsRegion;

    /** 窜货方经销商姓名 */
    private String fleeingGoodsDealers;

    /** 窜货方授权编号 */
    private String fleeingGoodsNumber;
    
    /** 被窜货方客户 */
    private Store byFleeingGoodStore;
    
    /** 被窜货省市区 */
    private Area byfgRegionArea;

    /** 被窜货方地区 */
    private String byFleeingGoodsRegion;

    /** 被窜货方经销商姓名 */
    private String byFleeingGoodsDealers;
    
    /** 产品*/
    private Product product;

    /** 窜货面积 */
    private BigDecimal fleeingGoodsArea;

    /** 消费者姓名 */
    private String customerName;

    /** 消费者安装地址 */
    private String customerInstallAddress;

    /** 消费者联系电话 */
    private String customerPhone;

    /** 省长意见 */
    private String governorOpinion;

    /** 渠道部意见 */
    private String channelOpinion;

    // -------- 快递信息 start --------
    /** 文件编号 */
    private String fileNumber;

    /** 处理时间 */
    private Date handleTime;

    /** 窜货类型 */
    private String fleeingGoodsType;

    /** 函件发出时间 */
    private Date lettersSentOutTime;

    /** 快递单号 */
    private String trackingNumber;

    /** 收件人 */
    private String consignee;

    /** 收件人类别 0：经销商，1：区域经理 */
    private Integer consigneeType;

    /** 联系电话 */
    private String consigneePhone;

    /** 快递地址 */
    private String expressAddress;

    /** 处理结果 */
    private String processingResult;

    // -------- 快递信息 end --------

    /** 函件收到时间 */
    private Date lettersReceiveTime;

    /** 窜货方确认意见 */
    private Date fleeingGoodsOpinion;

    /** 6个附件 */
    private List<FleeingGoodsAttach> blowBy0Attachs = new ArrayList<FleeingGoodsAttach>();
    private List<FleeingGoodsAttach> blowBy1Attachs = new ArrayList<FleeingGoodsAttach>();
    private List<FleeingGoodsAttach> blowBy2Attachs = new ArrayList<FleeingGoodsAttach>();
    private List<FleeingGoodsAttach> blowBy3Attachs = new ArrayList<FleeingGoodsAttach>();
    private List<FleeingGoodsAttach> blowBy4Attachs = new ArrayList<FleeingGoodsAttach>();
    private List<FleeingGoodsAttach> blowBy5Attachs = new ArrayList<FleeingGoodsAttach>();

    @Column(nullable = false, updatable = false, unique = true, length = 100)
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    public Store getStore() {
        return store;
    }

    public void setStore(Store store) {
        this.store = store;
    }
    
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getFleeingGoodsTime() {
        return fleeingGoodsTime;
    }

    public void setFleeingGoodsTime(Date fleeingGoodsTime) {
        this.fleeingGoodsTime = fleeingGoodsTime;
    }

    public Integer getPunishType() {
        return punishType;
    }

    public void setPunishType(Integer punishType) {
        this.punishType = punishType;
    }

    public String getFleeingGoodsRegion() {
        return fleeingGoodsRegion;
    }

    public void setFleeingGoodsRegion(String fleeingGoodsRegion) {
        this.fleeingGoodsRegion = fleeingGoodsRegion;
    }

    public String getFleeingGoodsDealers() {
        return fleeingGoodsDealers;
    }

    public void setFleeingGoodsDealers(String fleeingGoodsDealers) {
        this.fleeingGoodsDealers = fleeingGoodsDealers;
    }

    public String getFleeingGoodsNumber() {
        return fleeingGoodsNumber;
    }

    public void setFleeingGoodsNumber(String fleeingGoodsNumber) {
        this.fleeingGoodsNumber = fleeingGoodsNumber;
    }

    public String getByFleeingGoodsRegion() {
        return byFleeingGoodsRegion;
    }

    public void setByFleeingGoodsRegion(String byFleeingGoodsRegion) {
        this.byFleeingGoodsRegion = byFleeingGoodsRegion;
    }

    public String getByFleeingGoodsDealers() {
        return byFleeingGoodsDealers;
    }

    public void setByFleeingGoodsDealers(String byFleeingGoodsDealers) {
        this.byFleeingGoodsDealers = byFleeingGoodsDealers;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

	public BigDecimal getFleeingGoodsArea() {
        return fleeingGoodsArea;
    }

    public void setFleeingGoodsArea(BigDecimal fleeingGoodsArea) {
        this.fleeingGoodsArea = fleeingGoodsArea;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerInstallAddress() {
        return customerInstallAddress;
    }

    public void setCustomerInstallAddress(String customerInstallAddress) {
        this.customerInstallAddress = customerInstallAddress;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getGovernorOpinion() {
        return governorOpinion;
    }

    public void setGovernorOpinion(String governorOpinion) {
        this.governorOpinion = governorOpinion;
    }

    public String getChannelOpinion() {
        return channelOpinion;
    }

    public void setChannelOpinion(String channelOpinion) {
        this.channelOpinion = channelOpinion;
    }

    public String getFileNumber() {
        return fileNumber;
    }

    public void setFileNumber(String fileNumber) {
        this.fileNumber = fileNumber;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getFleeingGoodsType() {
        return fleeingGoodsType;
    }

    public void setFleeingGoodsType(String fleeingGoodsType) {
        this.fleeingGoodsType = fleeingGoodsType;
    }

    public Date getLettersSentOutTime() {
        return lettersSentOutTime;
    }

    public void setLettersSentOutTime(Date lettersSentOutTime) {
        this.lettersSentOutTime = lettersSentOutTime;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public String getConsignee() {
        return consignee;
    }

    public void setConsignee(String consignee) {
        this.consignee = consignee;
    }

    public Integer getConsigneeType() {
        return consigneeType;
    }

    public void setConsigneeType(Integer consigneeType) {
        this.consigneeType = consigneeType;
    }

    public String getConsigneePhone() {
        return consigneePhone;
    }

    public void setConsigneePhone(String consigneePhone) {
        this.consigneePhone = consigneePhone;
    }

    public String getExpressAddress() {
        return expressAddress;
    }

    public void setExpressAddress(String expressAddress) {
        this.expressAddress = expressAddress;
    }

    public String getProcessingResult() {
        return processingResult;
    }

    public void setProcessingResult(String processingResult) {
        this.processingResult = processingResult;
    }

    public Date getLettersReceiveTime() {
        return lettersReceiveTime;
    }

    public void setLettersReceiveTime(Date lettersReceiveTime) {
        this.lettersReceiveTime = lettersReceiveTime;
    }

    public Date getFleeingGoodsOpinion() {
        return fleeingGoodsOpinion;
    }

    public void setFleeingGoodsOpinion(Date fleeingGoodsOpinion) {
        this.fleeingGoodsOpinion = fleeingGoodsOpinion;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<FleeingGoodsAttach> getBlowBy0Attachs() {
        return blowBy0Attachs;
    }

    public void setBlowBy0Attachs(List<FleeingGoodsAttach> blowBy0Attachs) {
        this.blowBy0Attachs = blowBy0Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<FleeingGoodsAttach> getBlowBy1Attachs() {
        return blowBy1Attachs;
    }

    public void setBlowBy1Attachs(List<FleeingGoodsAttach> blowBy1Attachs) {
        this.blowBy1Attachs = blowBy1Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<FleeingGoodsAttach> getBlowBy2Attachs() {
        return blowBy2Attachs;
    }

    public void setBlowBy2Attachs(List<FleeingGoodsAttach> blowBy2Attachs) {
        this.blowBy2Attachs = blowBy2Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<FleeingGoodsAttach> getBlowBy3Attachs() {
        return blowBy3Attachs;
    }

    public void setBlowBy3Attachs(List<FleeingGoodsAttach> blowBy3Attachs) {
        this.blowBy3Attachs = blowBy3Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<FleeingGoodsAttach> getBlowBy4Attachs() {
        return blowBy4Attachs;
    }

    public void setBlowBy4Attachs(List<FleeingGoodsAttach> blowBy4Attachs) {
        this.blowBy4Attachs = blowBy4Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<FleeingGoodsAttach> getBlowBy5Attachs() {
        return blowBy5Attachs;
    }

    public void setBlowBy5Attachs(List<FleeingGoodsAttach> blowBy5Attachs) {
        this.blowBy5Attachs = blowBy5Attachs;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	public Area getFgRegionArea() {
		return fgRegionArea;
	}

	public void setFgRegionArea(Area fgRegionArea) {
		this.fgRegionArea = fgRegionArea;
	}
	
    @ManyToOne(fetch = FetchType.LAZY)
	public Area getByfgRegionArea() {
		return byfgRegionArea;
	}

	public void setByfgRegionArea(Area byfgRegionArea) {
		this.byfgRegionArea = byfgRegionArea;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Store getByFleeingGoodStore() {
		return byFleeingGoodStore;
	}

	public void setByFleeingGoodStore(Store byFleeingGoodStore) {
		this.byFleeingGoodStore = byFleeingGoodStore;
	}

    
}
