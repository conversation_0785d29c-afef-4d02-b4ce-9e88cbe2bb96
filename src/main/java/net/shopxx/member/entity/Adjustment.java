package net.shopxx.member.entity;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import net.shopxx.act.entity.ActWfBillEntity;
/**
 * 系统调账
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "xx_adjustment")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_adjustment_sequence")
public class Adjustment extends ActWfBillEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/** 单据编号 */
	private String sn;
	
	/** 单据状态  0.已保存(没有流程)1.审核中  2.审核 3.作废    */
	private Integer status;
	
	/** 调账备注 */
	private String memo;
	
	/** GL日期 */
	private Date glDate;
	
	/**附件项 */
	private List<AdjustmentAttach> adjustmentAttachs = new ArrayList<AdjustmentAttach>();
	
	/** 来源充值单 */
	private DepositRecharge sourceDepositRecharge;
	
	/** 调款充值单 */
	private DepositRecharge adjustmentDepositRecharge;
	
	

	public String getSn() {
		return sn;
	}

	public Integer getStatus() {
		return status;
	}

	public String getMemo() {
		return memo;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
	
	@OneToMany(mappedBy = "adjustment", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<AdjustmentAttach> getAdjustmentAttachs() {
		return adjustmentAttachs;
	}

	public void setAdjustmentAttachs(List<AdjustmentAttach> adjustmentAttachs) {
		this.adjustmentAttachs = adjustmentAttachs;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public DepositRecharge getSourceDepositRecharge() {
		return sourceDepositRecharge;
	}

	public void setSourceDepositRecharge(DepositRecharge sourceDepositRecharge) {
		this.sourceDepositRecharge = sourceDepositRecharge;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public DepositRecharge getAdjustmentDepositRecharge() {
		return adjustmentDepositRecharge;
	}

	public void setAdjustmentDepositRecharge(DepositRecharge adjustmentDepositRecharge) {
		this.adjustmentDepositRecharge = adjustmentDepositRecharge;
	}

	public Date getGlDate() {
		return glDate;
	}

	public void setGlDate(Date glDate) {
		this.glDate = glDate;
	}
}
