package net.shopxx.member.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.Area;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 经销商终止
 */
@Entity
@Table(name = "xx_store_exit")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_store_exit_sequence")
public class StoreExit extends ActWfBillEntity {

    private static final long serialVersionUID = -5164520192692989887L;

    /** 单号 */
    private String sn;

    /** 单据状态 0：已保存，1：已提交，2：已终止，3：进行中 4：已完成*/
    private Integer status;

    /**
     * 类型
     * 0：退出
     * 1：终止
     */
    private Integer type;

    /** 客户 */
    private Store store;

    /** 经销商 */
    private String dealer;

    /** 联系方式 */
    private String phone;

    /** 经营区域 */
    private String businessAddress;

    /** 经营年限 */
    private Integer businessYear;

    /** 门店数量 */
    private Integer shopNumber;

    /**
     *  退出原因
     * 0：经营不善
     * 1：资金不足
     * 2：经营其他品牌产品
     * 3：其他原因
     */
    private Integer exitCause;

    /** 其他退出原因 */
    private String exitCauseOther;

    /**
     * 门店状况
     * 0：门店关闭
     * 1：门店已交接
     */
    private Integer shopState;

    /** 门店地址 */
    private String shopAddress;

    /** 退出时间 */
    private Date exitTime;

    /**
     * 退出状态
     * 0：自主退出
     * 1：终止经销关系
     */
    private Integer exitStatus;

    /**
     * 品牌保证金
     * 0：保证金缴纳到公司总部
     * 1：保证金缴纳到总经销商
     * 2：保证金缴纳到省运营中心
     **/
    private Integer brandMargin;

    /**
     * 品牌收据
     * 0：有公司开具收据
     * 1：无公司开具收据
     * 2：收据丢失
     * 3：其他
     */
    private Integer brandReceipt;

    /** 其他收据 */
    private String brandReceiptOther;

    /** 缴纳金额 */
    private BigDecimal payAmount;

    /** 缴纳时间 */
    private Date payTime;

    // 保证金退还银行帐户信息
    private Area bankArea;

    /** 开户省 */
    private String openProvince;

    /** 开户市 */
    private String openCity;

    /** 开户银行 */
    private String openBank;

    /** 开户支行 */
    private String openBranch;

    /** 开户姓名 */
    private String openMaster;

    /** 开户账号 */
    private String openAccount;

    /** 经销商是否有欠款 0：无，1：有 */
    private Integer whetherArrears;

    /** 欠款金额 */
    private BigDecimal arrearsMoney;

    /**
     * 经销商无法联系
     * 0：①电话拒接或关机
     * 1：②携款潜逃
     * 2：③门店关门
     * 3：④门店已转让
     * 4：⑤私自与他人交接
     * 5：⑥经销商无法联系，店员营业
     */
    private String dealerContact;

    /** 区域经理意见 */
    private String areaManagerOpinion;

    /** 省长意见 */
    private String governorOpinion;

    /** 渠道部意见 */
    private String channelOpinion;

    /** 是否核心经销商*/
    private Boolean isCoreStore;
    
    /** 附件项 */
	private List<StoreExitAttach> storeExitAttachs = new ArrayList<StoreExitAttach>();
	
	/** 收据*/
	private List<StoreExitAttach> storeReceiptAttachs = new ArrayList<StoreExitAttach>();
	
	/** 解约原因 */
	private String cancelReason;

	/** 解约档案编号 */
	private String unfileNumber;
	
	/** 解约日期 */
	private Date cancelDate;
	
	/** 客户退出门店关联表 */
	private List<StoreExitShopInfo> seList = new ArrayList<StoreExitShopInfo>();

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Store getStore() {
        return store;
    }

    public void setStore(Store store) {
        this.store = store;
    }

    public String getDealer() {
        return dealer;
    }

    public void setDealer(String dealer) {
        this.dealer = dealer;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getBusinessAddress() {
        return businessAddress;
    }

    public void setBusinessAddress(String businessAddress) {
        this.businessAddress = businessAddress;
    }

    public Integer getBusinessYear() {
        return businessYear;
    }

    public void setBusinessYear(Integer businessYear) {
        this.businessYear = businessYear;
    }

    public Integer getShopNumber() {
        return shopNumber;
    }

    public void setShopNumber(Integer shopNumber) {
        this.shopNumber = shopNumber;
    }

    public Integer getExitCause() {
        return exitCause;
    }

    public void setExitCause(Integer exitCause) {
        this.exitCause = exitCause;
    }

    public String getExitCauseOther() {
        return exitCauseOther;
    }

    public void setExitCauseOther(String exitCauseOther) {
        this.exitCauseOther = exitCauseOther;
    }

    public Integer getShopState() {
        return shopState;
    }

    public void setShopState(Integer shopState) {
        this.shopState = shopState;
    }

    public String getShopAddress() {
        return shopAddress;
    }

    public void setShopAddress(String shopAddress) {
        this.shopAddress = shopAddress;
    }

    public Date getExitTime() {
        return exitTime;
    }

    public void setExitTime(Date exitTime) {
        this.exitTime = exitTime;
    }

    public Integer getExitStatus() {
        return exitStatus;
    }

    public void setExitStatus(Integer exitStatus) {
        this.exitStatus = exitStatus;
    }

    public Integer getBrandMargin() {
        return brandMargin;
    }

    public void setBrandMargin(Integer brandMargin) {
        this.brandMargin = brandMargin;
    }

    public Integer getBrandReceipt() {
        return brandReceipt;
    }

    public void setBrandReceipt(Integer brandReceipt) {
        this.brandReceipt = brandReceipt;
    }

    public String getBrandReceiptOther() {
        return brandReceiptOther;
    }

    public void setBrandReceiptOther(String brandReceiptOther) {
        this.brandReceiptOther = brandReceiptOther;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getOpenProvince() {
        return openProvince;
    }

    public void setOpenProvince(String openProvince) {
        this.openProvince = openProvince;
    }

    public String getOpenCity() {
        return openCity;
    }

    public void setOpenCity(String openCity) {
        this.openCity = openCity;
    }

    public String getOpenBank() {
        return openBank;
    }

    public void setOpenBank(String openBank) {
        this.openBank = openBank;
    }

    public String getOpenBranch() {
        return openBranch;
    }

    public void setOpenBranch(String openBranch) {
        this.openBranch = openBranch;
    }

    public String getOpenMaster() {
        return openMaster;
    }

    public void setOpenMaster(String openMaster) {
        this.openMaster = openMaster;
    }

    public String getOpenAccount() {
        return openAccount;
    }

    public void setOpenAccount(String openAccount) {
        this.openAccount = openAccount;
    }

    public Integer getWhetherArrears() {
        return whetherArrears;
    }

    public void setWhetherArrears(Integer whetherArrears) {
        this.whetherArrears = whetherArrears;
    }

    public BigDecimal getArrearsMoney() {
        return arrearsMoney;
    }

    public void setArrearsMoney(BigDecimal arrearsMoney) {
        this.arrearsMoney = arrearsMoney;
    }

    public String getDealerContact() {
        return dealerContact;
    }

    public void setDealerContact(String dealerContact) {
        this.dealerContact = dealerContact;
    }

    public String getAreaManagerOpinion() {
        return areaManagerOpinion;
    }

    public void setAreaManagerOpinion(String areaManagerOpinion) {
        this.areaManagerOpinion = areaManagerOpinion;
    }

    public String getGovernorOpinion() {
        return governorOpinion;
    }

    public void setGovernorOpinion(String governorOpinion) {
        this.governorOpinion = governorOpinion;
    }

    public String getChannelOpinion() {
        return channelOpinion;
    }

    public void setChannelOpinion(String channelOpinion) {
        this.channelOpinion = channelOpinion;
    }

    public Boolean getIsCoreStore() {
        return isCoreStore;
    }

    public void setIsCoreStore(Boolean coreStore) {
        isCoreStore = coreStore;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
	public List<StoreExitAttach> getStoreExitAttachs() {
		return storeExitAttachs;
	}

	public void setStoreExitAttachs(List<StoreExitAttach> storeExitAttachs) {
		this.storeExitAttachs = storeExitAttachs;
	}

	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
	public List<StoreExitAttach> getStoreReceiptAttachs() {
		return storeReceiptAttachs;
	}

	public void setStoreReceiptAttachs(List<StoreExitAttach> storeReceiptAttachs) {
		this.storeReceiptAttachs = storeReceiptAttachs;
	}

    public String getCancelReason() {
		return cancelReason;
	}

	public void setCancelReason(String cancelReason) {
		this.cancelReason = cancelReason;
	}

	public String getUnfileNumber() {
		return unfileNumber;
	}

	public void setUnfileNumber(String unfileNumber) {
		this.unfileNumber = unfileNumber;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}
	
	@OneToMany(mappedBy = "storeExit", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StoreExitShopInfo> getSeList() {
		return seList;
	}

	public void setSeList(List<StoreExitShopInfo> seList) {
		this.seList = seList;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    public Area getBankArea() {
        return bankArea;
    }

    public void setBankArea(Area bankArea) {
        this.bankArea = bankArea;
    }
}

