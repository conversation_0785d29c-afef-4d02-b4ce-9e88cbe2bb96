package net.shopxx.member.entity;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.MemberRank;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * 用户企业关系
 */
@Entity
@Table(name = "xx_store_member", uniqueConstraints = { @UniqueConstraint(columnNames = { "username",
		"store" }) })
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_store_member_sequence")
public class StoreMember extends BaseEntity {

	private static final long serialVersionUID = -7666285831739005551L;
	
	/** 树路径分隔符 */
	public static final String ID_PATH_SEPARATOR = ",";

	/** 企业 */
	private Store store;

	/** 用户信息 */
	private Member member;

	/** 微信用户标识 */
	private String openid;

	/** 会员等级 */
	private MemberRank memberRank;

	/** 是否启用 */
	private Boolean isEnabled;

    /** 是否需要修改密码 */
    private Boolean isNeedToChangePwd;

	/** 是否默认 */
	private Boolean isDefault;

	/** 用户余额 */
	private BigDecimal balance;

//	/** 用户收益 */
//	private BigDecimal profit;

	/** 推荐人*/
	private StoreMember recommendStoreMember;

	/** 是否合伙人*/
	private Boolean isPartner;

	/** 合伙人类型 */
	private String partnerType;

	/** 证件照片1 */
	private String url1;

	/** 证件照片2 */
	private String url2;

	/** 身份证正面 */
	private String idUrl1;

	/** 身份证反面 */
	private String idUrl2;

	/** 身份证号码 */
	private String idCode;

	/** 合伙人类型 */
	private PartnerCategory partnerCategory;

	/** 邀请码 */
	private String inviteCode;

	/** 登录密码 */
	private String password;

	/** 余额支付密码 */
	private String payPassword;

	/** 姓名  */
	private String name;

	/** 用户名  同一个企业唯一 也做为登录用*/
	private String username;

	/** 头像 */
	private String imageName;

	/**
	 * 性别
	 */
	public enum Gender {

		/** 男 */
		male,

		/** 女 */
		female
	}

	/** 性别 */
	private Gender gender;

	/** 出生日期 */
	private Date birth;

	/** 地址 */
	private String address;

	/** 邮编 */
	private String zipCode;

	/** 最后登录IP */
	private String loginIp;

	/** 最后登录日期 */
	private Date loginDate;

	/** 收益 */
	private BigDecimal income;

	/** 备注 */
	private String memo;

	/** 会员类型：0 企业用户，1 外部用户，99小程序帐号  */
	private int memberType;

	/** 唯一标识 UUID */
	private String uniqueIdentify;

	/** 是否admin创建   1 admin创建  */
	private Integer createByAdmin;

	/** 是否业务员 */
	private Boolean isSalesman;
	
	/** 是否单点登录用户   1 是  */
	private Integer ssoUser;

	/** 身份证 */
	private String idCard;
	
	/** 邮箱 */
	private String emailAddress;
	
	/** 层级*/
	private StoreMember parent;
	
	/** 是否活动管理员*/
	private Boolean isActiveAdministrator;
	
	/** 小程序用户类型*/
	private Integer appType;
	
	/**
	 * 获取机构
	 * @return store
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public Store getStore() {
		return store;
	}

	/**
	 * 设置机构
	 * @param store
	 */
	public void setStore(Store store) {
		this.store = store;
	}

	/**
	 * 获取会员
	 * @return member
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public Member getMember() {
		return member;
	}

	/**
	 * 设置会员
	 * @param member
	 */
	public void setMember(Member member) {
		this.member = member;
	}

//	/**
//	 * 获取企业信息
//	 * @return companyInfo
//	 */
//	@ManyToOne(fetch = FetchType.LAZY)
//	@JoinColumn(nullable = false)
//	public CompanyInfo getCompanyInfo() {
//		return companyInfo;
//	}
//
//	/**
//	 * 设置企业信息
//	 * @param companyInfo
//	 */
//	public void setCompanyInfo(CompanyInfo companyInfo) {
//		this.companyInfo = companyInfo;
//	}

	/**
	 * 获取余额
	 * @return balance
	 */
	@Column(nullable = false)
	public BigDecimal getBalance() {
		return balance;
	}

	/**
	 * 设置余额
	 * @param balance
	 */
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}

//	/**
//	 * 获取消费额
//	 * @return amount
//	 */
//	@Column(nullable = false)
//	public BigDecimal getAmount() {
//		return amount;
//	}
//
//	/**
//	 * 设置消费额
//	 * @param amount
//	 */
//	public void setAmount(BigDecimal amount) {
//		this.amount = amount;
//	}
//
//	/**
//	 * 获取积分
//	 * @return point
//	 */
//	@Column(nullable = false)
//	public Long getPoint() {
//		return point;
//	}
//
//	/**
//	 * 设置积分
//	 * @param point
//	 */
//	public void setPoint(Long point) {
//		this.point = point;
//	}

	/** 
	 * 获取  是否单点登录用户   1 是 
	 * @return ssoUser 是否单点登录用户   1 是 
	 */
	public Integer getSsoUser() {
		return ssoUser;
	}
	

	/** 
	 * 设置  是否单点登录用户   1 是 
	 * @param ssoUser 是否单点登录用户   1 是 
	 */
	public void setSsoUser(Integer ssoUser) {
		this.ssoUser = ssoUser;
	}
	

	/**
	 * 获取会员等级
	 * @return memberRank
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public MemberRank getMemberRank() {
		return memberRank;
	}

	/**
	 * 设置会员等级
	 * @param memberRank
	 */
	public void setMemberRank(MemberRank memberRank) {
		this.memberRank = memberRank;
	}

	/**
	 * 获取是否启用
	 * @return isEnabled
	 */
	public Boolean getIsEnabled() {
		return isEnabled;
	}

	/**
	 * 设置是否启用
	 * @param isEnabled
	 */
	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}

	/**
	 * @return the isDefault
	 */
	public Boolean getIsDefault() {
		return isDefault;
	}

	/**
	 * @param isDefault the isDefault to set
	 */
	public void setIsDefault(Boolean isDefault) {
		this.isDefault = isDefault;
	}

//	public BigDecimal getProfit() {
//		return profit;
//	}
//
//	public void setProfit(BigDecimal profit) {
//		this.profit = profit;
//	}

	/**
	 * @return the recommend
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getRecommendStoreMember() {
		return recommendStoreMember;
	}

	/**
	 * @param recommendStoreMember the recommend to set
	 */
	public void setRecommendStoreMember(StoreMember recommendStoreMember) {
		this.recommendStoreMember = recommendStoreMember;
	}

//	/**
//	 * @return the grade
//	 */
//	public Integer getGrade() {
//		return grade;
//	}
//
//	/**
//	 * @param grade the grade to set
//	 */
//	public void setGrade(Integer grade) {
//		this.grade = grade;
//	}
//
//	/**
//	 * @return the treePath
//	 */
//	public String getTreePath() {
//		return treePath;
//	}
//
//	/**
//	 * @param treePath the treePath to set
//	 */
//	public void setTreePath(String treePath) {
//		this.treePath = treePath;
//	}

	/**
	 * @return the isPartner
	 */
	public Boolean getIsPartner() {
		return isPartner;
	}

	/**
	 * @param isPartner the isPartner to set
	 */
	public void setIsPartner(Boolean isPartner) {
		this.isPartner = isPartner;
	}

	/**
	 * @return the partnerType
	 */
	public String getPartnerType() {
		return partnerType;
	}

	/**
	 * @param partnerType the partnerType to set
	 */
	public void setPartnerType(String partnerType) {
		this.partnerType = partnerType;
	}

	/**
	 * @return the url1
	 */
	public String getUrl1() {
		return url1;
	}

	/**
	 * @param url1 the url1 to set
	 */
	public void setUrl1(String url1) {
		this.url1 = url1;
	}

	/**
	 * @return the url2
	 */
	public String getUrl2() {
		return url2;
	}

	/**
	 * @param url2 the url2 to set
	 */
	public void setUrl2(String url2) {
		this.url2 = url2;
	}

	/**
	 * @return the idUrl1
	 */
	public String getIdUrl1() {
		return idUrl1;
	}

	/**
	 * @param idUrl1 the idUrl1 to set
	 */
	public void setIdUrl1(String idUrl1) {
		this.idUrl1 = idUrl1;
	}

	/**
	 * @return the idUrl2
	 */
	public String getIdUrl2() {
		return idUrl2;
	}

	/**
	 * @param idUrl2 the idUrl2 to set
	 */
	public void setIdUrl2(String idUrl2) {
		this.idUrl2 = idUrl2;
	}

	/**
	 * @return the idCode
	 */
	public String getIdCode() {
		return idCode;
	}

	/**
	 * @param idCode the idCode to set
	 */
	public void setIdCode(String idCode) {
		this.idCode = idCode;
	}

	/**
	 * @return the partnerCategory
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public PartnerCategory getPartnerCategory() {
		return partnerCategory;
	}

	/**
	 * @param partnerCategory the partnerCategory to set
	 */
	public void setPartnerCategory(PartnerCategory partnerCategory) {
		this.partnerCategory = partnerCategory;
	}

	/**
	 * 获取邀请码
	 *
	 * @return inviteCode
	 */
	public String getInviteCode() {
		return inviteCode;
	}

	/**
	 * 设置邀请码
	 *
	 * @param inviteCode
	 */
	public void setInviteCode(String inviteCode) {
		this.inviteCode = inviteCode;
	}

	/**
	 * 获取用户标识
	 * @return openid
	 */
	public String getOpenid() {
		return openid;
	}

	/**
	 * 设置 用户标识
	 */
	public void setOpenid(String openid) {
		this.openid = openid;
	}

	/**
	 * 持久化前
	 */
	@PrePersist
	public void prePersist() {
		/*
		 * 初始化 
		 */
		BigDecimal zero = new BigDecimal(0);
//		point = point == null ? 0L : point;
//		amount = amount == null ? zero : amount;
		balance = balance == null ? zero : balance;
		isEnabled = isEnabled == null ? true : isEnabled;
		isDefault = isDefault == null ? false : isDefault;
		isPartner = isPartner == null ? false : isPartner;
		income = income == null ? zero : income;
		appType = appType == null ? 2 : appType;

		if (getPartnerCategory() != null) {
			setPartnerType(getPartnerCategory().getName());
		}
		if (getName() == null) {
			setName(getUsername());
		}
		if (getUniqueIdentify() == null) {
			setUniqueIdentify(UUID.randomUUID().toString());
		}
		if (getIsSalesman() == null) {
			setIsSalesman(false);
		}
		
		setAppType(appType);
	}

	@PreUpdate
	public void preUpdate() {
		appType = appType == null ? 2 : appType;
        setAppType(appType);
		if (getPartnerCategory() != null) {
			setPartnerType(getPartnerCategory().getName());
		}
		if (getName() == null) {
			setName(getUsername());
		}
		if (getIsSalesman() == null) {
			setIsSalesman(false);
		}
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getImageName() {
		return imageName;
	}

	public void setImageName(String imageName) {
		this.imageName = imageName;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public Date getBirth() {
		return birth;
	}

	public void setBirth(Date birth) {
		this.birth = birth;
	}

	public String getLoginIp() {
		return loginIp;
	}

	public void setLoginIp(String loginIp) {
		this.loginIp = loginIp;
	}

	public Date getLoginDate() {
		return loginDate;
	}

	public void setLoginDate(Date loginDate) {
		this.loginDate = loginDate;
	}

//	/**
//	 * 获取 是否属于该企业
//	 * <AUTHOR>
//	 * @date 2016-10-10
//	 * @return isThisCompany
//	 */
//	public Boolean getIsThisCompany() {
//		return isThisCompany;
//	}
//
//	/**
//	 * 设置 是否属于该企业
//	 * <AUTHOR>
//	 * @date 2016-10-10
//	 * @param isThisCompany 是否属于该企业
//	 */
//	public void setIsThisCompany(Boolean isThisCompany) {
//		this.isThisCompany = isThisCompany;
//	}

	/**
	 * 获取收益剩余余额
	 * @return
	 */
	@Column()
	public BigDecimal getIncome() {
		return income;
	}

	/**
	 * 设置收益剩余余额
	 * @param income
	 */
	public void setIncome(BigDecimal income) {
		this.income = income;
	}

//	/**
//	 * 获取 是否启用云商务
//	 * <AUTHOR>
//	 * @date 2016-11-10
//	 * @return isCloudBusiness
//	 */
//	public Boolean getIsCloudBusiness() {
//		return isCloudBusiness;
//	}
//
//	/**
//	 * 设置 是否启用云商务
//	 * <AUTHOR>
//	 * @date 2016-11-10
//	 * @param isCloudBusiness 是否启用云商务
//	 */
//	public void setIsCloudBusiness(Boolean isCloudBusiness) {
//		this.isCloudBusiness = isCloudBusiness;
//	}

	/**
	 * 获取 备注
	 * @return memo
	 */
	public String getMemo() {
		return memo;
	}

	/**
	 * 设置 备注
	 * @param memo 备注
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/**
	 * 获取 用户名  同一个企业唯一 也做为登录用
	 * @return username
	 */
	public String getUsername() {
		return username;
	}

	/**
	 * 设置 用户名  同一个企业唯一 也做为登录用
	 * @param username 用户名  同一个企业唯一 也做为登录用
	 */
	public void setUsername(String username) {
		this.username = username;
	}

	/**
	 * 获取 地址
	 * @return address
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * 设置 地址
	 * @param address 地址
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	/**
	 * 获取 邮编
	 * @return zipCode
	 */
	public String getZipCode() {
		return zipCode;
	}

	/**
	 * 设置 邮编
	 * @param zipCode 邮编
	 */
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	/**
	 * 获取 会员类型
	 * @return memberType
	 */
	public int getMemberType() {
		return memberType;
	}

	/**
	 * 设置 会员类型
	 * @param memberType 会员类型
	 */
	public void setMemberType(int memberType) {
		this.memberType = memberType;
	}

	/**
	 * 获取 唯一标识 UUID
	 * @return uniqueIdentify
	 */
	public String getUniqueIdentify() {
		return uniqueIdentify;
	}

	/**
	 * 设置 唯一标识 UUID
	 * @param uniqueIdentify 唯一标识 UUID
	 */
	public void setUniqueIdentify(String uniqueIdentify) {
		this.uniqueIdentify = uniqueIdentify;
	}

	@Column(updatable = false)
	public Integer getCreateByAdmin() {
		return createByAdmin;
	}

	public void setCreateByAdmin(Integer createByAdmin) {
		this.createByAdmin = createByAdmin;
	}

	public Boolean getIsSalesman() {
		return isSalesman;
	}

	public void setIsSalesman(Boolean isSalesman) {
		this.isSalesman = isSalesman;
	}

	/**
	 * 获取身份证
	 * @return
	 */
	public String getIdCard() {
		return idCard;
	}

	/**
	 * 设置身份证
	 * @return
	 */
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getParent() {
		return parent;
	}

	public void setParent(StoreMember parent) {
		this.parent = parent;
	}

	@Column(name="is_active_administrator", columnDefinition="bit default 0",nullable=false)
	public Boolean getIsActiveAdministrator() {
		return isActiveAdministrator;
	}

	public void setIsActiveAdministrator(Boolean isActiveAdministrator) {
		this.isActiveAdministrator = isActiveAdministrator;
	}

    public Boolean getIsNeedToChangePwd() {
        return isNeedToChangePwd;
    }

    public void setIsNeedToChangePwd(Boolean needToChangePwd) {
        isNeedToChangePwd = needToChangePwd;
    }

    public Integer getAppType() {
		return appType;
	}

	public void setAppType(Integer appType) {
		this.appType = appType;
	}


//	/**
//	 * 获取 政策池
//	 * <AUTHOR>
//	 * @date 2016-11-16
//	 * @return policyAmt
//	 */
//	public BigDecimal getPolicyAmt() {
//		return policyAmt;
//	}
//
//	/**
//	 * 设置 政策池
//	 * <AUTHOR>
//	 * @date 2016-11-16
//	 * @param policyAmt 政策池
//	 */
//	public void setPolicyAmt(BigDecimal policyAmt) {
//		this.policyAmt = policyAmt;
//	}
//
//	/**  
//	 * 获取costPoolAmt  
//	 * @return costPoolAmt
//	 */
//	public BigDecimal getCostPoolAmt() {
//		return costPoolAmt;
//	}
//
//	/**  
//	 * 设置costPoolAmt  
//	 * @param costPoolAmt costPoolAmt  
//	 */
//	public void setCostPoolAmt(BigDecimal costPoolAmt) {
//		this.costPoolAmt = costPoolAmt;
//	}

}