package net.shopxx.member.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.PrePersist;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.aftersales.entity.Aftersale;
import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.order.entity.Contract;
import net.shopxx.order.entity.CustomerContract;
import net.shopxx.order.entity.Order;
import net.shopxx.wf.entity.WfBillEntity;

/**
 * 用户、客户 余额充值
 * 
 */
@Entity
@Table(name = "xx_deposit_recharge")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_deposit_recharge_sequence")
public class DepositRecharge extends ActWfBillEntity {

	private static final long serialVersionUID = -8323455373046981882L;

	/** 余额充值编号 */
	private String sn;

	/**来源编号 售后单号**/
	private String sourceSn;

	public enum Status {

		/**
		 * 待审核
		 */
		wait,
//
//		/**
//		 * 审核中
//		 */
//		progressing,

		/**
		 * 审核未通过
		 */
		failure,
		/**
		 * 审核通过
		 */
		success
	}

	/** 充值状态 */
	private Status status;

	/** 单据状态  0.已保存(没有流程)  1.已提交  2.已处理(流程走完) 3.作废    */
	private Integer docStatus;

	/** 计划充值金额 */
	private BigDecimal amount;

	/** 实际充值金额 */
	private BigDecimal actualAmount;

	/** 申请人 */
	private StoreMember storeMember;

	/** 创建人 */
	private StoreMember creator;

	/** 审核人 */
	private StoreMember operator;

	/** 申请客户  */
	private Store store;

	/** 申请机构  */
	private SaleOrg saleOrg;

//	/** 审核人 */
//	private String checkAdmin;

	/** 审核时间 */
	private Date checkDate;

	/** 申请日期 */
	private Date applyDate;

	/** 申请备注 */
	private String memo;

	/** 审核备注 */
	private String note;

	/** 代充值时，充值对象 */
	private StoreMember createBy;

	/** 类型 0用户余额充值 1客户余额充值2机构费用预算充值3客户费用预算充值 */
	private Integer type;

	/** 附件 */
	private String image;

	/** 附件2 */
	private String image2;

	/** 附件3 */
	private String image3;

	/** 充值类型 取词汇编码为DepositRechargeType的词汇 */
	private SystemDict rechargeType;

	/** 0增加预算 1扣减预算 */
	private Integer budgetType;

	/** 对账月份 */
	private String balanceMonth;

	/**附件项 */
	private List<DepositAttach> depositAttachs = new ArrayList<DepositAttach>();

	/**银行卡号*/
	private BankCard bankCard;

	/**银行水单号*/
	private String bankSlip;

	/**提交时间*/
	private Date submitDate;

	private SystemDict policyType;

	/**组织*/
	private Organization organization;

	/**认领类型(0:回款流水认领到客户，1：回款流水认领到合同,2:承兑汇票领到客户,3:承兑汇票认领到合同)*/
	private Integer claimType;

	/**货款类型（0认领货款 1认领垫资 2垫资还款 3信贷还款 4额度还款）*/
	private Integer amountType;

	/**经销商*/
	private Store distributor;

	/**合同编号*/
	private String contract_no;

	/** 合同金额 */
	private BigDecimal contract_amt;

	/** 垫资回款单 */
	private DepositRecharge paymentRecharge;

	/** 还款金额 */
	private BigDecimal backAmount;

	/** 退款金额 */
	private BigDecimal refundAmount;

	/** 信贷单 */
	private CreditRechargeContract creditBill;

	/** 额度单 */
	private CreditRechargeContract creditLine;

	/**明细 */
	private List<DepositRechargeItem> depositRechargeItems = new ArrayList<DepositRechargeItem>();

	/**合同付款方式 */
	private List<DepositPaymentBatch> depositPaymentBatchs = new ArrayList<DepositPaymentBatch>();

	/** erp状态  0未到账  1已到账*/
	private Integer erpStatus;

	//合同
	private Contract contract;

	//20181018 大自然新加字段

	/** 税率 */
	private BigDecimal taxRate;

	/** 是否有发票  0无发票  1有发票*/
	private Integer hasInvoice;

	/** 发票类型 取词汇编码为InvoiceType的词汇 */
	private SystemDict invoiceType;

	/** erp现金流项目 取词汇编码为CashProject的词汇 */
	private SystemDict cashProject;

	/** 回款确认（0未确认  1已确认） */
	private Integer returnStatus;
	
	/** GL日期 */
	private Date glDate;
	
	/** SBU*/
	private Sbu sbu;
	
	/** 汇款人*/
	private String remitter;
	
	/** 汇款账号*/
	private String remittanceAccount;
	
	/** 放行人*/
	private String permitThroughName;
	
	
	/** 区域经理*/
	private StoreMember regionalManager;
	
	
	/** 到账状态  0.已到账 1.未到账      */
	private Integer payStatus;
	
	/** 关联订单     */
	private Order order;
	
	/** 来源单据号    */
	private DepositRecharge sourceDepositRecharge;

	/**数据来源类型 if =2(中板同步) else (am新增)**/
	private Integer sourceType;
	
	/** 经销商合同*/
	private CustomerContract customerContract;
	
	
	/**经销商充值明细 */
	private List<DistributorRechargeItem> distributorRechargeItems = new ArrayList<DistributorRechargeItem>();

    /** 售后 */
    private Aftersale aftersale;


    /** 充值的类型  0-普通充值（没有明细列表的） 1-经销商充值（有明细列表的）*/
    private Integer rechargeTypes;


    /**
     * 获取 充值的类型
     * @return rechargeTypes
     */
    public Integer getRechargeTypes() {
        return rechargeTypes;
    }

    /**
     * 设置充值的类型
     *
     * @param
     *
     */
    public void setRechargeTypes(Integer rechargeTypes) {
        this.rechargeTypes = rechargeTypes;
    }

    /**
	 * 获取收入金额
	 * 
	 * @return 收入金额
	 */
	@Column(nullable = false, precision = 21, scale = 6)
	public BigDecimal getAmount() {
		return amount;
	}

	/**
	 * 设置收入金额
	 * 
	 * @param
	 *
	 */
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	/**
	 * 获取 创建人
	 * @return creator
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getCreator() {
		return creator;
	}

	/**
	 * 设置 创建人
	 * @param creator 创建人
	 */
	public void setCreator(StoreMember creator) {
		this.creator = creator;
	}

	/**
	 * 获取操作员
	 * 
	 * @return 操作员
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getOperator() {
		return operator;
	}

	/**
	 * 设置操作员
	 * 
	 * @param operator
	 *            操作员
	 */
	public void setOperator(StoreMember operator) {
		this.operator = operator;
	}

	/**
	 * 获取备注
	 * 
	 * @return 备注
	 */
	@Length(max = 200)
	@Column(updatable = true)
	public String getMemo() {
		return memo;
	}

	/**
	 * 设置备注
	 * 
	 * @param memo
	 *            备注
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(updatable = false)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	/**
	 * 获取店铺
	 * @return
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "stores")
	public Store getStore() {
		return store;
	}

	/**
	 * 设置店铺
	 * @param store
	 */
	public void setStore(Store store) {
		this.store = store;
	}

	/**
	 * @return the sn
	 */
	@Column(unique = true)
	public String getSn() {
		return sn;
	}

	/**
	 * @param sn the sn to set
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	/**
	 * 获取预存款充值状态
	 * <AUTHOR>
	 * @date 2016年9月1日 上午10:08:26
	 * @return
	 */
	public Status getStatus() {
		return status;
	}

	/**
	 * 设置预存款充值状态
	 * <AUTHOR>
	 * @date 2016年9月1日 上午10:08:23
	 * @param status
	 */
	public void setStatus(Status status) {
		this.status = status;
	}

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

//	public String getCheckAdmin() {
//		return checkAdmin;
//	}
//
//	public void setCheckAdmin(String checkAdmin) {
//		this.checkAdmin = checkAdmin;
//	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	/**
	 * 获取 附件2
	 * @date 2016-11-15
	 * @return image2
	 */
	public String getImage2() {
		return image2;
	}

	/**
	 * 设置 附件2
	 * @date 2016-11-15
	 * @param image2 附件2
	 */
	public void setImage2(String image2) {
		this.image2 = image2;
	}

	/**
	 * 获取 附件3
	 * @date 2016-11-15
	 * @return image3
	 */
	public String getImage3() {
		return image3;
	}

	/**
	 * 设置 附件3
	 * @date 2016-11-15
	 * @param image3 附件3
	 */
	public void setImage3(String image3) {
		this.image3 = image3;
	}

	/**
	 * 获取 代充值时，充值对象
	 * @date 2016-11-16
	 * @return createBy
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getCreateBy() {
		return createBy;
	}

	/**
	 * 设置 代充值时，充值对象
	 * @date 2016-11-16
	 * @param createBy 代充值时，充值对象
	 */
	public void setCreateBy(StoreMember createBy) {
		this.createBy = createBy;
	}

	/**
	 * 获取 类型 0用户余额充值 1客户余额充值
	 * @date 2017年8月10日
	 * @return type
	 */
	public Integer getType() {
		return type;
	}

	/**
	 * 设置 类型 0用户余额充值 1客户余额充值
	 * @date 2017年8月10日
	 * @param type 类型 0用户余额充值 1客户余额充值
	 */
	public void setType(Integer type) {
		this.type = type;
	}

	/**
	 * 获取 实际充值金额
	 * @date 2017年9月5日
	 * @return actualAmount
	 */
	@Column(nullable = false, precision = 21, scale = 6)
	public BigDecimal getActualAmount() {
		return actualAmount;
	}

	/**
	 * 设置 实际充值金额
	 * @date 2017年9月5日
	 * @param actualAmount 实际充值金额
	 */
	public void setActualAmount(BigDecimal actualAmount) {
		this.actualAmount = actualAmount;
	}

	/**
	 * 获取 充值类型 取词汇编码为DepositRechargeType的词汇
	 * @return rechargeType
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getRechargeType() {
		return rechargeType;
	}

	/**
	 * 设置 充值类型 取词汇编码为DepositRechargeType的词汇
	 * @param rechargeType 充值类型 取词汇编码为DepositRechargeType的词汇
	 */
	public void setRechargeType(SystemDict rechargeType) {
		this.rechargeType = rechargeType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getPolicyType() {
		return policyType;
	}

	public void setPolicyType(SystemDict policyType) {
		this.policyType = policyType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	/**
	 * 获取 0增加预算 1扣减预算
	 * @return budgetType
	 */
	public Integer getBudgetType() {
		return budgetType;
	}

	/**
	 * 设置 0增加预算 1扣减预算
	 * @param budgetType 0增加预算 1扣减预算
	 */
	public void setBudgetType(Integer budgetType) {
		this.budgetType = budgetType;
	}

	public Integer getDocStatus() {
		return docStatus;
	}

	public void setDocStatus(Integer docStatus) {
		this.docStatus = docStatus;
	}

	/**
	 * 获取 对账月份
	 * @return balanceMonth
	 */
	public String getBalanceMonth() {
		return balanceMonth;
	}

	/**
	 * 设置 对账月份
	 * @param balanceMonth 对账月份
	 */
	public void setBalanceMonth(String balanceMonth) {
		this.balanceMonth = balanceMonth;
	}

	@OneToMany(mappedBy = "depositRecharge", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<DepositAttach> getDepositAttachs() {
		return depositAttachs;
	}

	public void setDepositAttachs(List<DepositAttach> depositAttachs) {
		this.depositAttachs = depositAttachs;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public BankCard getBankCard() {
		return bankCard;
	}

	public void setBankCard(BankCard bankCard) {
		this.bankCard = bankCard;
	}

	public Date getSubmitDate() {
		return submitDate;
	}

	public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}

	/**
	 * 持久化前
	 */
	@PrePersist
	public void prePersist() {
		if (getType() == null) {
			setType(0);
		}
		if (getWfState() == null) {
			setWfState(0);
		}
	}

	public String getBankSlip() {
		return bankSlip;
	}

	public void setBankSlip(String bankSlip) {
		this.bankSlip = bankSlip;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	/**  
	 * 获取claimType  
	 * @return claimType claimType  
	 */
	public Integer getClaimType() {
		return claimType;
	}

	/**  
	 * 设置claimType  
	 * @param claimType claimType  
	 */
	public void setClaimType(Integer claimType) {
		this.claimType = claimType;
	}

	/**  
	 * 获取amountType  
	 * @return amountType amountType  
	 */
	public Integer getAmountType() {
		return amountType;
	}

	/**  
	 * 设置amountType  
	 * @param amountType amountType  
	 */
	public void setAmountType(Integer amountType) {
		this.amountType = amountType;
	}

	/**  
	 * 获取distributor  
	 * @return distributor distributor  
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getDistributor() {
		return distributor;
	}

	/**  
	 * 设置distributor  
	 * @param distributor distributor  
	 */
	public void setDistributor(Store distributor) {
		this.distributor = distributor;
	}

	/**  
	 * 获取contract_no  
	 * @return contract_no contract_no  
	 */
	public String getContract_no() {
		return contract_no;
	}

	/**  
	 * 设置contract_no  
	 * @param contract_no contract_no  
	 */
	public void setContract_no(String contract_no) {
		this.contract_no = contract_no;
	}

	/**  
	 * 获取contract_amt  
	 * @return contract_amt contract_amt  
	 */
	public BigDecimal getContract_amt() {
		return contract_amt;
	}

	/**  
	 * 设置contract_amt  
	 * @param contract_amt contract_amt  
	 */
	public void setContract_amt(BigDecimal contract_amt) {
		this.contract_amt = contract_amt;
	}

	/**  
	 * 获取depositRechargeItems  
	 * @return depositRechargeItems depositRechargeItems  
	 */
	@OneToMany(mappedBy = "depositRecharge", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<DepositRechargeItem> getDepositRechargeItems() {
		return depositRechargeItems;
	}

	/**  
	 * 设置depositRechargeItems  
	 * @param depositRechargeItems depositRechargeItems  
	 */
	public void setDepositRechargeItems(
			List<DepositRechargeItem> depositRechargeItems) {
		this.depositRechargeItems = depositRechargeItems;
	}

	/**  
	 * 获取depositPaymentBatchs  
	 * @return depositPaymentBatchs depositPaymentBatchs  
	 */
	@OneToMany(mappedBy = "depositRecharge", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<DepositPaymentBatch> getDepositPaymentBatchs() {
		return depositPaymentBatchs;
	}

	/**  
	 * 设置depositPaymentBatchs  
	 * @param depositPaymentBatchs depositPaymentBatchs  
	 */
	public void setDepositPaymentBatchs(
			List<DepositPaymentBatch> depositPaymentBatchs) {
		this.depositPaymentBatchs = depositPaymentBatchs;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public DepositRecharge getPaymentRecharge() {
		return paymentRecharge;
	}

	public void setPaymentRecharge(DepositRecharge paymentRecharge) {
		this.paymentRecharge = paymentRecharge;
	}

	public BigDecimal getBackAmount() {
		return backAmount;
	}

	public void setBackAmount(BigDecimal backAmount) {
		this.backAmount = backAmount;
	}

	public BigDecimal getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public CreditRechargeContract getCreditBill() {
		return creditBill;
	}

	public void setCreditBill(CreditRechargeContract creditBill) {
		this.creditBill = creditBill;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public CreditRechargeContract getCreditLine() {
		return creditLine;
	}

	public void setCreditLine(CreditRechargeContract creditLine) {
		this.creditLine = creditLine;
	}

	/** 
	 * 获取  erp状态  0未到账  1已到账 
	 * @return erpStatus erp状态  0未到账  1已到账 
	 */
	public Integer getErpStatus() {
		return erpStatus;
	}

	/** 
	 * 设置  erp状态  0未到账  1已到账 
	 * @param erpStatus erp状态  0未到账  1已到账 
	 */
	public void setErpStatus(Integer erpStatus) {
		this.erpStatus = erpStatus;
	}

	/** 
	 * 获取   contract 合同
	 * @return contract 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Contract getContract() {
		return contract;
	}

	/** 
	 * 设置  contract 合同
	 * @return Contract contract 
	 */
	public void setContract(Contract contract) {
		this.contract = contract;
	}

	/** 
	 * 获取  税率 
	 * @return taxRate 税率 
	 */
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	/** 
	 * 设置  税率 
	 * @param taxRate 税率 
	 */
	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	/** 
	 * 获取  是否有发票  0无发票  1有发票 
	 * @return hasInvoice 是否有发票  0无发票  1有发票 
	 */
	public Integer getHasInvoice() {
		return hasInvoice;
	}

	/** 
	 * 设置  是否有发票  0无发票  1有发票 
	 * @param hasInvoice 是否有发票  0无发票  1有发票 
	 */
	public void setHasInvoice(Integer hasInvoice) {
		this.hasInvoice = hasInvoice;
	}

	/** 
	 * 获取  发票类型 取词汇编码为InvoiceType的词汇 
	 * @return invoiceType 发票类型 取词汇编码为InvoiceType的词汇 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getInvoiceType() {
		return invoiceType;
	}

	/** 
	 * 设置  发票类型 取词汇编码为InvoiceType的词汇 
	 * @param invoiceType 发票类型 取词汇编码为InvoiceType的词汇 
	 */
	public void setInvoiceType(SystemDict invoiceType) {
		this.invoiceType = invoiceType;
	}

	/** 
	 * 获取  充值类型 取词汇编码为CashProject的词汇 
	 * @return cashProject 充值类型 取词汇编码为CashProject的词汇 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getCashProject() {
		return cashProject;
	}

	/** 
	 * 设置  充值类型 取词汇编码为CashProject的词汇 
	 * @param cashProject 充值类型 取词汇编码为CashProject的词汇 
	 */
	public void setCashProject(SystemDict cashProject) {
		this.cashProject = cashProject;
	}

	public Integer getReturnStatus() {
		return returnStatus;
	}

	public void setReturnStatus(Integer returnStatus) {
		this.returnStatus = returnStatus;
	}

	public Date getGlDate() {
		return glDate;
	}

	public void setGlDate(Date glDate) {
		this.glDate = glDate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}

	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}
	
	/** 
	 * 获取  收款人
	 * 
	 */
	public String getRemitter() {
		return remitter;
	}
	
	/** 
	 * 设置  收款人
	 * @param remitter 收款人
	 */
	public void setRemitter(String remitter) {
		this.remitter = remitter;
	}
	
	/** 
	 * 获取  收款人账号 
	 * 
	 */
	public String getRemittanceAccount() {
		return remittanceAccount;
	}
	
	/** 
	 * 设置  收款人账号 
	 * @param remittanceAccount 收款人账号
	 */
	public void setRemittanceAccount(String remittanceAccount) {
		this.remittanceAccount = remittanceAccount;
	}
	
	/** 
	 * 获取  放行人
	 * 
	 */
	public String getPermitThroughName() {
		return permitThroughName;
	}
	
	/** 
	 * 设置  放行人 
	 * @param permitThroughName 放行人
	 */
	public void setPermitThroughName(String permitThroughName) {
		this.permitThroughName = permitThroughName;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getRegionalManager() {
		return regionalManager;
	}

	public void setRegionalManager(StoreMember regionalManager) {
		this.regionalManager = regionalManager;
	}



	public Integer getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(Integer payStatus) {
		this.payStatus = payStatus;
	}
	
	/**
	 * 获取订单
	 * 
	 * @return 订单
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "orders", nullable = true, updatable = false)
	public Order getOrder() {
		return order;
	}

	/**
	 * 设置订单
	 * 
	 * @param order
	 *            订单
	 */
	public void setOrder(Order order) {
		this.order = order;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public DepositRecharge getSourceDepositRecharge() {
		return sourceDepositRecharge;
	}

	public void setSourceDepositRecharge(DepositRecharge sourceDepositRecharge) {
		this.sourceDepositRecharge = sourceDepositRecharge;
	}
	
	
	@JsonIgnore
	@OneToMany(mappedBy = "depositRecharge", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<DistributorRechargeItem> getDistributorRechargeItems() {
		return distributorRechargeItems;
	}

	public void setDistributorRechargeItems(List<DistributorRechargeItem> distributorRechargeItems) {
		this.distributorRechargeItems = distributorRechargeItems;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public CustomerContract getCustomerContract() {
		return customerContract;
	}

	public void setCustomerContract(CustomerContract customerContract) {
		this.customerContract = customerContract;
	}

	public String getSourceSn() {
		return sourceSn;
	}

	public void setSourceSn(String sourceSn) {
		this.sourceSn = sourceSn;
	}

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

	@ManyToOne(fetch = FetchType.LAZY)
    public Aftersale getAftersale() {
        return aftersale;
    }

    public void setAftersale(Aftersale aftersale) {
        this.aftersale = aftersale;
    }
}