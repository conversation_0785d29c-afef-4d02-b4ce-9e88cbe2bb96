package net.shopxx.member.entity;

import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;

/**
 * 密码修改记录表
 */
@Entity
@Table(name = "xx_pwd_modification_record")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_pwd_modification_record_sequence")
public class PwdModificationRecord extends BaseEntity{

    private static final long serialVersionUID = 1L;

    /**用户**/
    private StoreMember storeMember;

    /**用户名**/
    private String username;

    /**最新密码**/
    private String pwd;

    /**密码修改来源 1-密码修改页面  2-用户编辑页面 3-中板同步过来 **/
    private Integer revisionSource;

    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getStoreMember() {
        return storeMember;
    }

    public void setStoreMember(StoreMember storeMember) {
        this.storeMember = storeMember;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public Integer getRevisionSource() {
        return revisionSource;
    }

    public void setRevisionSource(Integer revisionSource) {
        this.revisionSource = revisionSource;
    }
}
