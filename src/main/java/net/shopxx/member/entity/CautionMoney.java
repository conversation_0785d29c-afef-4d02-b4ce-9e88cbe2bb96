package net.shopxx.member.entity;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.shopxx.base.core.entity.BaseEntity;

/**店铺保证金*/
@Entity
@Table(name = "xx_caution_money")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_caution_money_sequence")
public class CautionMoney extends BaseEntity {

	private static final long serialVersionUID = 3497001895918992384L;

	/**编号*/
	private String sn;
	
	/**客户*/
	private Store store;

	/**账户类型 1现金账户 2品牌保证金账户 3返利账户*/
	private Integer accountType;
	
	/**应缴*/
	private  BigDecimal payable;
	
	/**实缴*/
	private BigDecimal paidIn;
	
	/**未缴*/
	private BigDecimal unpaid;
	
	/**缴纳说明*/
	private String paidNote;

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public Store getStore() {
		return store;
	}

	public void setStore(Store store) {
		this.store = store;
	}

	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}

	public BigDecimal getPayable() {
		return payable;
	}

	public void setPayable(BigDecimal payable) {
		this.payable = payable;
	}

	public BigDecimal getPaidIn() {
		return paidIn;
	}

	public void setPaidIn(BigDecimal paidIn) {
		this.paidIn = paidIn;
	}

	public BigDecimal getUnpaid() {
		return unpaid;
	}

	public void setUnpaid(BigDecimal unpaid) {
		this.unpaid = unpaid;
	}
  
	public String getPaidNote() {
		return paidNote;
	}

	public void setPaidNote(String paidNote) {
		this.paidNote = paidNote;
	}
	
}
