package net.shopxx.member.entity;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.*;

import net.shopxx.base.core.entity.OrderEntity;
import net.shopxx.template.entity.DTemplates;
import org.hibernate.annotations.Columns;
import org.springframework.web.bind.annotation.Mapping;

/**
 * PC菜单
 */
@Entity
@Table(name = "xx_pc_menu")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_pc_menu_sequence")
public class PcMenu extends OrderEntity {

	private static final long serialVersionUID = 4678171465952497956L;

	/** 父菜单 */
	private PcMenu superId;

	/** 菜单名称 */
	private String menuName;

	/** 菜单标号 */
	private String menuCode;

	/** 菜单对应图标地址 */
	private String menuImg;

	/** 是否是目录(不是目录就是功能。目录、功能) */
	private Boolean isCat;

	/** 只有当菜单isCat=false才能设置 */
	private String url;

	/** 活动状态 */
	private Boolean isEnabled;

	/** 活动角色 */
	private Set<PcRole> pcRoles = new HashSet<PcRole>();

	/** 子菜单 */
	private List<PcMenu> children = new ArrayList<PcMenu>();

	/** 层级 */
	private Integer grade;

	/** 是否叶子端 */
	private Boolean isLeaf;

	/** 类型 0平台管理菜单 1其他菜单 */
	private Integer type;

	/**关联模板**/
	private Long dTemplates;

	/**跳转路径**/
	private String jumpPath;

	/**排列顺序**/
	private Integer number;

	/**
	 * 获取 父菜单
	 * @date 2017年7月14日
	 * @return superId
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public PcMenu getSuperId() {
		return superId;
	}

	/**
	 * 设置 父菜单
	 * @date 2017年7月14日
	 * @param superId 父菜单
	 */
	public void setSuperId(PcMenu superId) {
		this.superId = superId;
	}

	/**
	 * 获取 菜单名称
	 * @date 2017年7月14日
	 * @return menuName
	 */
	public String getMenuName() {
		return menuName;
	}

	/**
	 * 设置 菜单名称
	 * @date 2017年7月14日
	 * @param menuName 菜单名称
	 */
	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}

	/**
	 * 获取 菜单标号
	 * @date 2017年7月14日
	 * @return menuCode
	 */
	public String getMenuCode() {
		return menuCode;
	}

	/**
	 * 设置 菜单标号
	 * @date 2017年7月14日
	 * @param menuCode 菜单标号
	 */
	public void setMenuCode(String menuCode) {
		this.menuCode = menuCode;
	}

	/**
	 * 获取 菜单对应图标地址
	 * @date 2017年7月14日
	 * @return menuImg
	 */
	public String getMenuImg() {
		return menuImg;
	}

	/**
	 * 设置 菜单对应图标地址
	 * @date 2017年7月14日
	 * @param menuImg 菜单对应图标地址
	 */
	public void setMenuImg(String menuImg) {
		this.menuImg = menuImg;
	}

	/**
	 * 获取 是否是目录(不是目录就是功能。目录、功能)
	 * @date 2017年7月14日
	 * @return isCat
	 */
	public Boolean getIsCat() {
		return isCat;
	}

	/**
	 * 设置 是否是目录(不是目录就是功能。目录、功能)
	 * @date 2017年7月14日
	 * @param isCat 是否是目录(不是目录就是功能。目录、功能)
	 */
	public void setIsCat(Boolean isCat) {
		this.isCat = isCat;
	}

	/**
	 * 获取 只有当菜单isCat=false才能设置
	 * @date 2017年7月14日
	 * @return url
	 */
	public String getUrl() {
		return url;
	}

	/**
	 * 设置 只有当菜单isCat=false才能设置
	 * @date 2017年7月14日
	 * @param url 只有当菜单isCat=false才能设置
	 */
	public void setUrl(String url) {
		this.url = url;
	}

	/**
	 * 获取 活动状态
	 * @date 2017年7月14日
	 * @return isEnabled
	 */
	public Boolean getIsEnabled() {
		return isEnabled;
	}

	/**
	 * 设置 活动状态
	 * @date 2017年7月14日
	 * @param isEnabled 活动状态
	 */
	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}

	/**
	 * 获取 活动角色
	 * @date 2017年7月14日
	 * @return pcRoles
	 */
	@ManyToMany(mappedBy = "pcMenus", fetch = FetchType.LAZY)
	public Set<PcRole> getPcRoles() {
		return pcRoles;
	}

	/**
	 * 设置 活动角色
	 * @date 2017年7月14日
	 * @param pcRoles 活动角色
	 */
	public void setPcRoles(Set<PcRole> pcRoles) {
		this.pcRoles = pcRoles;
	}

	/**
	 * 获取 子菜单
	 * @date 2017年7月14日
	 * @return children
	 */
	@OneToMany(mappedBy = "superId", fetch = FetchType.LAZY)
	public List<PcMenu> getChildren() {
		return children;
	}

	/**
	 * 设置 子菜单
	 * @date 2017年7月14日
	 * @param children 子菜单
	 */
	public void setChildren(List<PcMenu> children) {
		this.children = children;
	}

	public Integer getGrade() {
		return grade;
	}

	public void setGrade(Integer grade) {
		this.grade = grade;
	}

	public Boolean getIsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(Boolean isLeaf) {
		this.isLeaf = isLeaf;
	}

	/**
	 * 获取 类型 0平台管理菜单 1其他菜单
	 * @return type
	 */
	public Integer getType() {
		return type;
	}

	/**
	 * 设置 类型 0平台管理菜单 1其他菜单
	 * @param type 类型 0平台管理菜单 1其他菜单
	 */
	public void setType(Integer type) {
		this.type = type;
	}

	public Long getdTemplates() {
		return dTemplates;
	}

	public void setdTemplates(Long dTemplates) {
		this.dTemplates = dTemplates;
	}

    public String getJumpPath() {
        return jumpPath;
    }

    public void setJumpPath(String jumpPath) {
        this.jumpPath = jumpPath;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
}
