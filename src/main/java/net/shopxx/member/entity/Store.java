package net.shopxx.member.entity;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.*;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Entity - 客户信息
 */
@Entity
@Table(name = "xx_store")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_store_sequence")
public class Store extends BaseEntity {

	private static final long serialVersionUID = -4422107659283633951L;

	/** 客户编号 */
	private String sn;

	/** 唯一标识 UUID */
	private String uniqueIdentify;

	/** 客户名称 **/
	private String name;

	/** 外部编号 */
	private String outTradeNo;

	/** 是否启用 */
	private Boolean isEnabled;

	/** 是否对应企业 **/
	private Boolean isMainStore;

	/**sbu*/
	private List<StoreSbu> storeSbu = new ArrayList<StoreSbu>();

	/** 客户预览 */
	private String storeImage;

	/** 客户预览2 */
	private String storeImage2;

	/** 联系电话 */
	private String mobile;

	/** 服务电话 */
	private String serviceTelephone;

	/** 客户地址 */
	private String address;

	/** 地区 */
	private Area area;

	/** 客户简介 */
	private String introduction;

	/** 客户类型 企业(不显示出来)、线上店铺、线下门店、供应商、代理商、直营商、分销商 */
	public enum Type {

		/** 企业 (不显示出来) */
		enterprise, /** 线上店铺 */
		onlineShop, /** 线下门店 */
		offlineStore, /** 供应商 */
		provider, /** 代理商 */
		distributor, /** 直营商 */
		directDealer, /** 分销商 */
		reseller
	}

	/** 客户类型 */
	public Type type;

	/** 线上店铺名 */
	private String outShopName;

	/** 区域经理*/
	private StoreMember storeMember;
	/**
	 * 线上店铺类型 1 天猫、2 天猫供销平台、3 京东、4 苏宁、5 国美、6 一号店、7 唯品会、8 贝贝网、9合伙人、 10当当 11飞牛网
	 */
	private Integer onlineShopType;

	/** 组织 */
	private SaleOrg saleOrg;

	/** 下正式订单是否校验可发货余额 */
	private Boolean isReduceBalance;

	/** 客户余额 */
	private BigDecimal balance;

	/** 授信额度 */
	private BigDecimal credit;

	/** 费用预算 */
	private BigDecimal budget;

	/** 会员等级 */
	private MemberRank memberRank;

	/** 客户其他名称 */
	private String alias;

	/** 收货人 */
	private String consignee;

	/** 改变的会员等级 */
	private MemberRank changeMemberRank;

	/** 启用改变的会员等级的数量 */
	private BigDecimal changeQuantity;

	/** 冻结费用预算 */
	private BigDecimal lockBudget;

	/** 冻结余额 */
	private BigDecimal lockBalance;

	/** 地址明细 */
	private List<StoreAddress> storeAddress = new ArrayList<StoreAddress>();

	/** 客户联系人 */
	private List<StoreContract> storeContract = new ArrayList<StoreContract>();

	/** 客户发票信息 */
	private List<StoreInvoiceInfo> storeInvoiceInfos = new ArrayList<StoreInvoiceInfo>();

	/** 客户经理 */
	private List<StoreManager> storeManagers = new ArrayList<StoreManager>();

	/** 税率 */
	private BigDecimal taxRate;

	// 2018/7/4加的字段 开始
	/**  (弃用) 经销商状态 1有效 */
	private String accountStatus;

	/** 经销商状态 */
	private SystemDict distributorStatus;

	/** 城市等级 0省级 1 地市级 2 区县级 3乡镇级 */
	private Integer accountTypeCode;

	/** 创建人 */
	private StoreMember createBy;

	/** 是否现金客户 */
	private Boolean cashClientFlag;

	/** 平台性质 0.运营管理中心 1.控股合资管理中心 2.营销服务中心 3.营销管理中心 */
	private Integer platformProperty;

	/** 销售平台 */
	private SaleOrg salesPlatform;

	/** 销售区域 */
	private String territoryName;

	/** 是否加盟成功 */
	private Boolean unJoinSucessFlag;

	/** 经销商姓名 */
	private String dealerName;

	/** 授权编号 */
	private String grantCode;

	/** 管理团队 */
	/*
	 * private String salesRep;
	 */

	/** 货币 */
	private String currencyCode;

	/** 经营品类 */
	private List<BusinessCategory> businessCategorys = new ArrayList<BusinessCategory>();

	/** 保证金 */
	private List<CautionMoney> cautionMoneies = new ArrayList<CautionMoney>();

	/** 经营记录 */
	private List<BusinessRecord> businessRecords = new ArrayList<BusinessRecord>();

	private List<StoreAttach> storeAttachs = new ArrayList<StoreAttach>();
	// 2018/7/4加的字段 结束

	// 2018/7/6字段 非必填
	/** 身份证信息 */
	private String identity;

	/** 应缴品牌保证金 */
	private BigDecimal needCautionPaid;

	/** 实缴品牌保证金 */
	private BigDecimal realCautionPaid;

	/** 欠缴品牌保证金 */
	private BigDecimal unpaidCautionPaid;

	/** 解约日期 */
	private Date cancelDate;

	/** 解约时是否资料齐全 */
	private Boolean cancelInfoFlag;

	/** 解约原因 */
	private String cancelReason;

	/** 解约档案编号 */
	private String unfileNumber;

	/** 加盟日期 */
	private Date activeDate;

	/** 法人代表 */
	private String contact;

	/** 经销商关系说明 */
	private String dealerRelationShip;

	/** 加盟档案编号 */
	private String joinFileNumber;

	/** 经销商类型 0.国内经销商 1.国际经销商 2.国产产品经销商 3.进口产品经销商 */
	private Integer distributorType;

	/** 子类型 0.总经销商 1.省会城市经销商 2.平台经销商 3.经销商 4.分销商 */
	private Integer subType;

	/** 门店地址 */
	private String description;

	/** 缴纳说明 */
	private String paidNote;

	/** 价格表 */
	private String priceList;

	/** 销量保证金 */
	private BigDecimal salesDeposit;

	/** 固定号码 */
	private String fixedNumber;
	// 结束

	/** 业务类型 取词汇编码为businessType的词汇 */
	private SystemDict businessType;

	/** sbu 取词汇编码为sbu的词汇 */
	private SystemDict sbu;

	/** 客户类型词汇*/
	private SystemDict customerType;

	/** 是否签约 0.是1.否*/
	private Integer isSigning;

	/** 签约时间*/
	private Date signingTime;

	/** 等级*/
	private String grade;

	/** 等级词汇*/
	private SystemDict graden;

	/** 销售体系 */
	private SystemDict saleStyle;

	/**未加盟成功 */
	private Boolean isJoinFalse;

	/** 对接人姓名*/
	private String receiverName;

	/** 对接人号码*/
	private String receiverPhone;

	/** 对接人岗位*/
	private String receiverPost;

	/** 最大流水号  */
	private String maxSn;

	/** 是否有抵押贷款 0.是 1.否*/
	private Integer haveMortgage;

	/** 是否有过公诉 0.是 1.否*/
	private Integer havaeProsecution;

	/** 是否跟天加员工有关系 0.是 1.否*/
	private Integer haveRelationship;

	/** 是否是异地经销商 0.是 1.否*/
	private Integer isDifferentPlaces;

	/** 大区*/
	private String lageArea;

	/** 区*/
	private String orgArea;

	/** 记录类型 合作单位发布会/门店入驻/门店撤场 */
	private Integer recordType;

	/**商场  */
	private String market;

	/**商场所在地  */
	private String marketlocal;

	/**商场负责人  */
	private String marketLeader;

	/**负责人联系方式  */
	private String leaderPhone;

	/**区域对接人  */
	private String areaPoint;

	/**经销商 */
	private String dealer;

	/**跟进事项 */
	private String followUp;

	/**跟进时间 */
	private String followTime;

	/**门店位置*/
	private String position;

	/**门店面积*/
	private BigDecimal acreage;

	/**沟通结果*/
	private String communicatResult;

	/**关店原因*/
	private String closeShipReason;

	/**加盟店/直营店 */
	private String joinStoreType;

	/** 通知时间*/
	private Date noticeTime;

	/**省份 */
	private String province;

	/**招商发布会地址 */
	private String conferenceAdress;

	/**参会人员 */
	private String conferee;

	/**经销商进驻意愿 */
	private String dominate;

	/**是否进驻 */
	private Boolean dealerIsIn;

	/**租金 */
	private BigDecimal rent;

	/** 区域关店时间 */
	private Date areaCloseTime;

	/** 进驻时间 */
	private Date dealerInTime;

	/** 开业时间 */
	private Date openingTime;

	/**商场支持：（租金、展位) */
	private String marketSupport;

	/** 区域 */
	private String region;

	/** 总经销商 */
	private String franchisee;

	/** 缴纳情况/异常说明*/
	private String paymentStatus;

	/** 经销商头信息的地区 */
	private Area headNewArea;

	/** 合作单位 */
	private List<StoreCooperation> storeCooperation = new ArrayList<StoreCooperation>();

	/** 销售区域 */
	private SalesArea salesArea;

	/** 传真号码*/
	private String faxNumber;

	/** 销售品类 */
	private String salesCategory;

	/** 手机号*/
	private String headPhone;

	/** 乡镇*/
	private String countryName;

	/** 合同主体*/
	private String contractSubject;

	/** 经销商地址*/
	private String headAddress;
	
	/**=========================2019/9/11 Link四期添加==========================*/
	
	/** 经销商性别 0：男  1：女 */
	private Integer dealerSex;
	
	/** 经销商学历 */
	private String dealerGrade;
	
	/** (备注)门店地址 */
	private String shopAddress;
	
	/**
	 * 公司性质
	 * 0：独立公司
	 * 1：合伙公司
	 * 2：个体工商户
	 */
	private Integer companyType;
	
	/**
	 * 经销商背景
	 * 0：只经营地板
	 * 1：经营多类建材品牌
	 * 2：非建材销售商家
	 * 3：其他
     * 4:经营多品类品牌
	 */
	private Integer dealerBackground;


    /** 经销商背景之备注 */
    private String dealerBackgroundMemo;

    /** 可发货状态：（客户存在欠款，不可发货）
     * 0：不可发货
     * 1：可发货
     * */
	private Integer shippingState;
	/**
	 * 经销商等级
	 * 0：资金雄厚，有建材经验
	 * 1：资金一般，有潜力
	 * 2：资金紧凑，可扶持
	 * 3：其他
	 */
	private Integer dealerLevel;
	
	/** 销售渠道值 */
	private BigDecimal salesChannelsVal1;
	private BigDecimal salesChannelsVal2;
	private BigDecimal salesChannelsVal3;
	private BigDecimal salesChannelsVal4;
	private BigDecimal salesChannelsVal5;
	private BigDecimal salesChannelsVal6;
	
	/** 宣传意识 0：优，1：良，2：一般，3：差，4：无 */
	private Integer propagandaAwareness;
	
	/** 品牌意识 0：优，1：良，2：一般，3：差，4：无 */
	private Integer brandAwareness;
	
	/** 售后服务 0：优，1：良，2：一般，3：差，4：无 */
	private Integer afterService;
	
    /* 人员配置 */
    /** 营销人员 */
    private Integer marketersNumber;
    
    /** 售后人员 */
    private Integer afterSaleNumber;
    
    /** 安装工人 */
    private Integer installBodyNumber;
    
    /** 仓库面积 */
    private BigDecimal warehouseArea;
    
    /* 门店设施 */
	/** 货车 */
    private Integer truck;
    
    /** 小车 */
    private Integer smallCar;
    
    /** 电脑/宽带  1：有  0：没有 */
    private Integer pcOrBroadband;
    
    // 加盟手续 
    /** 1:是  0:否 */
    private Integer joiningFormalities1;
    
    /** 1:是  0:否 */
    private Integer joiningFormalities2;
    
    /** 是否核心经销商 */
    private Boolean isCoreStroe;
    
    /** 经销商类别  1.一城一商    0.一城多商 */
    private Integer category;
    
    /** 新建日期 */
    private Date effectiveDate;


    /** 经销商属性   （link四期）   */
    private SystemDict agentProperty;
    
    /**
     * 四期ID
     */
    private Long linkFourId;
    
    /**
     * 5期ID
     */
    private Long linkFiveId;
    
    /**
     * 是否推五期
     */
    private Boolean isToLink5;
    
    /**LINK5归属经销商*/
	private Store storeLink5;
	
	@ManyToOne(fetch = FetchType.LAZY)
    public Store getStoreLink5() {
		return storeLink5;
	}

	public void setStoreLink5(Store storeLink5) {
		this.storeLink5 = storeLink5;
	}

    @ManyToOne(fetch = FetchType.LAZY)
    public SystemDict getAgentProperty() {
        return this.agentProperty;
    }


    public void setAgentProperty(SystemDict agentProperty) {
        this.agentProperty = agentProperty;
    }

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getFaxNumber() {
		return faxNumber;
	}

	public void setFaxNumber(String faxNumber) {
		this.faxNumber = faxNumber;
	}

	public String getSalesCategory() {
		return salesCategory;
	}

	public void setSalesCategory(String salesCategory) {
		this.salesCategory = salesCategory;
	}

	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StoreCooperation> getStoreCooperation() {
		return storeCooperation;
	}

	public void setStoreCooperation(List<StoreCooperation> storeCooperation) {
		this.storeCooperation = storeCooperation;
	}

	/**
	 * 获取经销商头信息的地区
	 * 
	 * @return 经销商头信息的地区
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Area getHeadNewArea() {
		return headNewArea;
	}

	/**
	 * 设置经销商头信息的地区
	 * 
	 * @param headNewArea
	 *            经销商头信息的地区
	 */
	public void setHeadNewArea(Area headNewArea) {
		this.headNewArea = headNewArea;
	}

	public String getFranchisee() {
		return franchisee;
	}

	public void setFranchisee(String franchisee) {
		this.franchisee = franchisee;
	}

	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public Integer getRecordType() {
		return recordType;
	}

	public void setRecordType(Integer recordType) {
		this.recordType = recordType;
	}

	public String getMarket() {
		return market;
	}

	public void setMarket(String market) {
		this.market = market;
	}

	public String getMarketlocal() {
		return marketlocal;
	}

	public void setMarketlocal(String marketlocal) {
		this.marketlocal = marketlocal;
	}

	public String getMarketLeader() {
		return marketLeader;
	}

	public void setMarketLeader(String marketLeader) {
		this.marketLeader = marketLeader;
	}

	public String getLeaderPhone() {
		return leaderPhone;
	}

	public void setLeaderPhone(String leaderPhone) {
		this.leaderPhone = leaderPhone;
	}

	public String getAreaPoint() {
		return areaPoint;
	}

	public void setAreaPoint(String areaPoint) {
		this.areaPoint = areaPoint;
	}

	public String getDealer() {
		return dealer;
	}

	public void setDealer(String dealer) {
		this.dealer = dealer;
	}

	public String getFollowUp() {
		return followUp;
	}

	public void setFollowUp(String followUp) {
		this.followUp = followUp;
	}

	public String getFollowTime() {
		return followTime;
	}

	public void setFollowTime(String followTime) {
		this.followTime = followTime;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public BigDecimal getAcreage() {
		return acreage;
	}

	public void setAcreage(BigDecimal acreage) {
		this.acreage = acreage;
	}

	public String getCommunicatResult() {
		return communicatResult;
	}

	public void setCommunicatResult(String communicatResult) {
		this.communicatResult = communicatResult;
	}

	public String getCloseShipReason() {
		return closeShipReason;
	}

	public void setCloseShipReason(String closeShipReason) {
		this.closeShipReason = closeShipReason;
	}

	public String getJoinStoreType() {
		return joinStoreType;
	}

	public void setJoinStoreType(String joinStoreType) {
		this.joinStoreType = joinStoreType;
	}

	public Date getNoticeTime() {
		return noticeTime;
	}

	public void setNoticeTime(Date noticeTime) {
		this.noticeTime = noticeTime;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getConferenceAdress() {
		return conferenceAdress;
	}

	public void setConferenceAdress(String conferenceAdress) {
		this.conferenceAdress = conferenceAdress;
	}

	public String getConferee() {
		return conferee;
	}

	public void setConferee(String conferee) {
		this.conferee = conferee;
	}

	public String getDominate() {
		return dominate;
	}

	public void setDominate(String dominate) {
		this.dominate = dominate;
	}

	public Boolean getDealerIsIn() {
		return dealerIsIn;
	}

	public void setDealerIsIn(Boolean dealerIsIn) {
		this.dealerIsIn = dealerIsIn;
	}

	public BigDecimal getRent() {
		return rent;
	}

	public void setRent(BigDecimal rent) {
		this.rent = rent;
	}

	public Date getOpeningTime() {
		return openingTime;
	}

	public void setOpeningTime(Date openingTime) {
		this.openingTime = openingTime;
	}

	public String getMarketSupport() {
		return marketSupport;
	}

	public void setMarketSupport(String marketSupport) {
		this.marketSupport = marketSupport;
	}

	@Column(nullable = false)
	public String getName() {
		return name;
	}

	public String getIdentity() {
		return identity;
	}

	public void setIdentity(String identity) {
		this.identity = identity;
	}

	public BigDecimal getNeedCautionPaid() {
		return needCautionPaid;
	}

	public void setNeedCautionPaid(BigDecimal needCautionPaid) {
		this.needCautionPaid = needCautionPaid;
	}

	public BigDecimal getRealCautionPaid() {
		return realCautionPaid;
	}

	public void setRealCautionPaid(BigDecimal realCautionPaid) {
		this.realCautionPaid = realCautionPaid;
	}

	public BigDecimal getUnpaidCautionPaid() {
		return unpaidCautionPaid;
	}

	public void setUnpaidCautionPaid(BigDecimal unpaidCautionPaid) {
		this.unpaidCautionPaid = unpaidCautionPaid;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public Boolean getCancelInfoFlag() {
		return cancelInfoFlag;
	}

	public void setCancelInfoFlag(Boolean cancelInfoFlag) {
		this.cancelInfoFlag = cancelInfoFlag;
	}

	public String getCancelReason() {
		return cancelReason;
	}

	public void setCancelReason(String cancelReason) {
		this.cancelReason = cancelReason;
	}

	public String getUnfileNumber() {
		return unfileNumber;
	}

	public void setUnfileNumber(String unfileNumber) {
		this.unfileNumber = unfileNumber;
	}

	public Date getActiveDate() {
		return activeDate;
	}

	public void setActiveDate(Date activeDate) {
		this.activeDate = activeDate;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getDealerRelationShip() {
		return dealerRelationShip;
	}

	public void setDealerRelationShip(String dealerRelationShip) {
		this.dealerRelationShip = dealerRelationShip;
	}

	public String getJoinFileNumber() {
		return joinFileNumber;
	}

	public void setJoinFileNumber(String joinFileNumber) {
		this.joinFileNumber = joinFileNumber;
	}

	public Integer getSubType() {
		return subType;
	}

	public void setSubType(Integer subType) {
		this.subType = subType;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getPaidNote() {
		return paidNote;
	}

	public void setPaidNote(String paidNote) {
		this.paidNote = paidNote;
	}

	public String getPriceList() {
		return priceList;
	}

	public void setPriceList(String priceList) {
		this.priceList = priceList;
	}

	public BigDecimal getSalesDeposit() {
		return salesDeposit;
	}

	public void setSalesDeposit(BigDecimal salesDeposit) {
		this.salesDeposit = salesDeposit;
	}

	public String getFixedNumber() {
		return fixedNumber;
	}

	public void setFixedNumber(String fixedNumber) {
		this.fixedNumber = fixedNumber;
	}

	public void setName(String name) {
		this.name = name;
	}

	@NotNull
	@Column(nullable = false)
	public Boolean getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}

	public Boolean getIsMainStore() {
		return isMainStore;
	}

	public void setIsMainStore(Boolean isMainStore) {
		this.isMainStore = isMainStore;
	}

	// @Email
	// @NotEmpty
	// @Length(max = 200)
	// @NotNull
	// public String getEmail() {
	// return email;
	// }
	//
	// public void setEmail(String email) {
	// this.email = email;
	// }

	public Integer getDistributorType() {
		return distributorType;
	}

	public void setDistributorType(Integer distributorType) {
		this.distributorType = distributorType;
	}

	public String getStoreImage() {
		return storeImage;
	}

	public void setStoreImage(String storeImage) {
		this.storeImage = storeImage;
	}

	/**
	 * 获取店铺服务电话
	 * 
	 * @return
	 */
	@NotEmpty
	@Length(max = 20)
	@NotNull
	public String getServiceTelephone() {
		return serviceTelephone;
	}

	/**
	 * 设置店铺服务电话
	 * 
	 * @param serviceTelephone
	 */
	public void setServiceTelephone(String serviceTelephone) {
		this.serviceTelephone = serviceTelephone;
	}

	// @Pattern(regexp =
	// "^(([01]?\\d{1}|2[0-3]):[0-5]?\\d{1})-(([01]?\\d{1}|2[0-3]):[0-5]?\\d{1})$")
	// public String getOpenTime() {
	// return openTime;
	// }
	//
	// public void setOpenTime(String openTime) {
	// this.openTime = openTime;
	// }

	/**
	 * 获取详细地址
	 * 
	 * @return 详细地址
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * 设置详细地址
	 * 
	 * @param areaName
	 *            详细地址
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	// /**
	// * 获取地区名称
	// *
	// * @return 地区名称
	// */
	// public String getAreaName() {
	// return areaName;
	// }
	//
	// /**
	// * 设置地区名称
	// *
	// * @param areaName
	// * 地区名称
	// */
	// public void setAreaName(String areaName) {
	// this.areaName = areaName;
	// }

	// /**
	// * 获取坐标x轴
	// *
	// * @return 坐标x轴
	// */
	// @JsonProperty
	// @Field(store = org.hibernate.search.annotations.Store.YES, index =
	// Index.NO)
	// @Min(0)
	// @Digits(integer = 12, fraction = 6)
	// @Column(precision = 21, scale = 6)
	// public BigDecimal getMapx() {
	// return mapx;
	// }
	//
	// /**
	// * 设置坐标x轴
	// *
	// * @param mapx
	// * 坐标x轴
	// */
	// public void setMapx(BigDecimal mapx) {
	// this.mapx = mapx;
	// }
	//
	// /**
	// * 获取坐标y轴
	// *
	// * @return 坐标y轴
	// */
	// @JsonProperty
	// @Field(store = org.hibernate.search.annotations.Store.YES, index =
	// Index.NO)
	// @Min(0)
	// @Digits(integer = 12, fraction = 6)
	// @Column(precision = 21, scale = 6)
	// public BigDecimal getMapy() {
	// return mapy;
	// }
	//
	// /**
	// * 设置坐标x轴
	// *
	// * @param mapx
	// * 坐标x轴
	// */
	// public void setMapy(BigDecimal mapy) {
	// this.mapy = mapy;
	// }

	/**
	 * 获取地区
	 * 
	 * @return 地区
	 */
	@NotNull
	@NotEmpty
	@ManyToOne(fetch = FetchType.LAZY)
	public Area getArea() {
		return area;
	}

	/**
	 * 设置地区
	 * 
	 * @param area
	 *            地区
	 */
	public void setArea(Area area) {
		this.area = area;
	}

	/**
	 * 获取体验店简介
	 * 
	 * @return 体验店简介
	 */
	public String getIntroduction() {
		return introduction;
	}

	/**
	 * 设置体验店简介
	 * 
	 * @param busline
	 *            体验店简介
	 */
	public void setIntroduction(String introduction) {
		this.introduction = introduction;
	}

	// /**
	// * 获取申请人姓名
	// * @return
	// */
	// @NotNull
	// @NotEmpty
	// public String getApplyMan() {
	// return applyMan;
	// }
	//
	// /**
	// * 设置申请人姓名
	// * @param applyMan
	// */
	// public void setApplyMan(String applyMan) {
	// this.applyMan = applyMan;
	// }
	//
	// public String getZipCode() {
	// return zipCode;
	// }
	//
	// public void setZipCode(String zipCode) {
	// this.zipCode = zipCode;
	// }

	public String getStoreImage2() {
		return storeImage2;
	}

	public void setStoreImage2(String storeImage2) {
		this.storeImage2 = storeImage2;
	}

	// public String getStoreImage3() {
	// return storeImage3;
	// }
	//
	// public void setStoreImage3(String storeImage3) {
	// this.storeImage3 = storeImage3;
	// }
	//
	// /**
	// * 获取申请人QQ
	// * @return
	// */
	// public String getQq() {
	// return qq;
	// }
	//
	// /**
	// * 设置申请人QQ
	// * @param qq
	// */
	// public void setQq(String qq) {
	// this.qq = qq;
	// }
	//
	// /**
	// * 获取申请人的联系电话
	// * @return
	// */
	// @NotNull
	// public String getContactTelephone() {
	// return contactTelephone;
	// }
	//
	// /**
	// * 设置申请人的联系电话
	// * @param contactTelephone
	// */
	// public void setContactTelephone(String contactTelephone) {
	// this.contactTelephone = contactTelephone;
	// }
	//
	// /**
	// * 获取公司名
	// * @return
	// */
	// public String getCompanyName() {
	// return companyName;
	// }
	//
	// /**
	// * 设置公司名
	// * @param company
	// */
	// public void setCompanyName(String companyName) {
	// this.companyName = companyName;
	// }
	//
	// public String getProvince() {
	// return province;
	// }
	//
	// public void setProvince(String province) {
	// this.province = province;
	// }
	//
	// public String getCity() {
	// return city;
	// }
	//
	// public void setCity(String city) {
	// this.city = city;
	// }
	//
	// public String getDistrict() {
	// return district;
	// }
	//
	// public void setDistrict(String district) {
	// this.district = district;
	// }
	//
	// /**
	// * 获取企业
	// * @return the companyInfo
	// */
	// @ManyToOne(fetch = FetchType.LAZY)
	// public CompanyInfo getCompanyInfo() {
	// return companyInfo;
	// }
	//
	// /**
	// * 设置企业
	// * @param companyInfo the companyInfo to set
	// */
	// public void setCompanyInfo(CompanyInfo companyInfo) {
	// this.companyInfo = companyInfo;
	// }

	/**
	 * 获取 sn
	 * 
	 * @return sn
	 */
	@Column(updatable = false, unique = true)
	public String getSn() {
		return sn;
	}

	/**
	 * 设置 sn
	 * 
	 * @param sn
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	public Type getType() {
		return type;
	}

	public void setType(Type type) {
		this.type = type;
	}

	/**
	 * @return the saleOrg
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	/**
	 * @param saleOrg
	 *            the saleOrg to set
	 */
	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	// /**
	// * @return the installService
	// */
	// public Boolean getInstallService() {
	// return installService;
	// }
	//
	// /**
	// * @param installService the installService to set
	// */
	// public void setInstallService(Boolean installService) {
	// this.installService = installService;
	// }
	//
	// /**
	// * @return the isAllowReq
	// */
	// public Boolean getIsAllowReq() {
	// return isAllowReq;
	// }
	//
	// /**
	// * @param isAllowReq the isAllowReq to set
	// */
	// public void setIsAllowReq(Boolean isAllowReq) {
	// this.isAllowReq = isAllowReq;
	// }
	//
	// /**
	// * @return the onlyOffline
	// */
	// public Boolean getOnlyOffline() {
	// return onlyOffline;
	// }
	//
	// /**
	// * @param onlyOffline the onlyOffline to set
	// */
	// public void setOnlyOffline(Boolean onlyOffline) {
	// this.onlyOffline = onlyOffline;
	// }
	//
	// /**
	// * @return the negStock
	// */
	// public Integer getNegStock() {
	// return negStock;
	// }
	//
	// /**
	// * @param negStock the negStock to set
	// */
	// public void setNegStock(Integer negStock) {
	// this.negStock = negStock;
	// }
	//
	// /**
	// * @return the isForceAttach
	// */
	// public Boolean getIsForceAttach() {
	// return isForceAttach;
	// }
	//
	// /**
	// * @param isForceAttach the isForceAttach to set
	// */
	// public void setIsForceAttach(Boolean isForceAttach) {
	// this.isForceAttach = isForceAttach;
	// }
	//
	// /**
	// * 是否允许修改价格
	// * @return the allowedChPrice
	// */
	// public Boolean getAllowedChPrice() {
	// return allowedChPrice;
	// }
	//
	// /**
	// * 是否允许修改价格
	// * @param allowedChPrice the allowedChPrice to set
	// */
	// public void setAllowedChPrice(Boolean allowedChPrice) {
	// this.allowedChPrice = allowedChPrice;
	// }
	//
	// /**
	// * 获取 是否属于办事处
	// * @date 2016-10-12
	// * @return isSaleOrg
	// */
	// public Boolean getIsSaleOrg() {
	// return isSaleOrg;
	// }
	//
	// /**
	// * 设置 是否属于办事处
	// * @date 2016-10-12
	// * @param isSaleOrg 是否属于办事处
	// */
	// public void setIsSaleOrg(Boolean isSaleOrg) {
	// this.isSaleOrg = isSaleOrg;
	// }

	/**
	 * 持久化前
	 */
	@PrePersist
	public void prePersist() {
		BigDecimal zero = BigDecimal.ZERO;
		if (getBalance() == null) {
			setBalance(zero);
		}
		if (getCredit() == null) {
			setCredit(zero);
		}
		if (getBudget() == null) {
			setBudget(zero);
		}
		if (getLockBudget() == null) {
			setLockBudget(zero);
		}
		if (getLockBalance() == null) {
			setLockBalance(zero);
		}
		if (getUniqueIdentify() == null) {
			setUniqueIdentify(UUID.randomUUID().toString());
		}

		if (getAccountTypeCode() == null) {
			setAccountTypeCode(2);
		}

		if (getUnJoinSucessFlag() == null) {
			setUnJoinSucessFlag(Boolean.TRUE);
		}

		if (getCurrencyCode() == null) {
			setCurrencyCode("人民币（元）");
		}
	}

	/**
	 * 清除前
	 * 
	 * <AUTHOR>
	 *
	 * @version 1.0
	 *
	 *          创建日期 2015年4月3日
	 *
	 */
	@PreRemove
	public void preRemove() {

	}

	/**
	 * 获取 余额
	 * 
	 * @date 2016-12-2
	 * @return balance
	 */
	public BigDecimal getBalance() {
		return balance;
	}

	/**
	 * 设置 余额
	 * 
	 * @date 2016-12-2
	 * @param balance
	 *            余额
	 */
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}

	/**
	 * 获取 会员等级
	 * 
	 * @date 2017年8月9日
	 * @return memberRank
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public MemberRank getMemberRank() {
		return memberRank;
	}

	/**
	 * 设置 会员等级
	 * 
	 * @date 2017年8月9日
	 * @param memberRank
	 *            会员等级
	 */
	public void setMemberRank(MemberRank memberRank) {
		this.memberRank = memberRank;
	}

	/**
	 * 获取 唯一标识
	 * 
	 * @date 2017年8月18日
	 * @return uniqueIdentify
	 */
	@Column(updatable = false)
	public String getUniqueIdentify() {
		return uniqueIdentify;
	}

	/**
	 * 设置 唯一标识
	 * 
	 * @date 2017年8月18日
	 * @param uniqueIdentify
	 *            唯一标识
	 */
	public void setUniqueIdentify(String uniqueIdentify) {
		this.uniqueIdentify = uniqueIdentify;
	}

	/**
	 * 获取 外部编号
	 * 
	 * @date 2017年8月18日
	 * @return outTradeNo
	 */
	@Column(nullable = false, unique = true)
	public String getOutTradeNo() {
		return outTradeNo;
	}

	/**
	 * 设置 外部编号
	 * 
	 * @date 2017年8月18日
	 * @param outTradeNo
	 *            外部编号
	 */
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	/**
	 * 获取 联系电话
	 * 
	 * @date 2017年8月18日
	 * @return mobile
	 */
	public String getMobile() {
		return mobile;
	}

	/**
	 * 设置 联系电话
	 * 
	 * @date 2017年8月18日
	 * @param mobile
	 *            联系电话
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	/**
	 * 获取 线上店铺名
	 * 
	 * @date 2017年8月18日
	 * @return outShopName
	 */
	public String getOutShopName() {
		return outShopName;
	}

	/**
	 * 设置 线上店铺名
	 * 
	 * @date 2017年8月18日
	 * @param outShopName
	 *            线上店铺名
	 */
	public void setOutShopName(String outShopName) {
		this.outShopName = outShopName;
	}

	/**
	 * 获取 线上店铺类型 1 天猫、2 天猫供销平台、3 京东、4 苏宁、5 国美、6 一号店、7 唯品会、8 贝贝网、9合伙人、 10当当 11飞牛网
	 * 
	 * @date 2017年8月18日
	 * @return onlineShopType
	 */
	public Integer getOnlineShopType() {
		return onlineShopType;
	}

	/**
	 * 设置 线上店铺类型 1 天猫、2 天猫供销平台、3 京东、4 苏宁、5 国美、6 一号店、7 唯品会、8 贝贝网、9合伙人、 10当当 11飞牛网
	 * 
	 * @date 2017年8月18日
	 * @param onlineShopType
	 *            线上店铺类型 1 天猫、2 天猫供销平台、3 京东、4 苏宁、5 国美、6 一号店、7 唯品会、8 贝贝网、9合伙人、
	 *            10当当 11飞牛网
	 */
	public void setOnlineShopType(Integer onlineShopType) {
		this.onlineShopType = onlineShopType;
	}

	/**
	 * 获取 是否扣减客户余额
	 * 
	 * @date 2017年8月18日
	 * @return isReduceBalance
	 */
	public Boolean getIsReduceBalance() {
		return isReduceBalance;
	}

	/**
	 * 设置 是否扣减客户余额
	 * 
	 * @date 2017年8月18日
	 * @param isReduceBalance
	 *            是否扣减客户余额
	 */
	public void setIsReduceBalance(Boolean isReduceBalance) {
		this.isReduceBalance = isReduceBalance;
	}

	/**
	 * 获取 授信额度
	 * 
	 * @return credit
	 */
	public BigDecimal getCredit() {
		return credit;
	}

	/**
	 * 设置 授信额度
	 * 
	 * @param credit
	 *            授信额度
	 */
	public void setCredit(BigDecimal credit) {
		this.credit = credit;
	}

	/**
	 * 获取 客户其他名称
	 * 
	 * @return alias
	 */
	public String getAlias() {
		return alias;
	}

	/**
	 * 设置 客户其他名称
	 * 
	 * @param alias
	 *            客户其他名称
	 */
	public void setAlias(String alias) {
		this.alias = alias;
	}

	/**
	 * 获取 收货人
	 * 
	 * @return consignee
	 */
	public String getConsignee() {
		return consignee;
	}

	/**
	 * 设置 收货人
	 * 
	 * @param consignee
	 *            收货人
	 */
	public void setConsignee(String consignee) {
		this.consignee = consignee;
	}

	/**
	 * 获取 改变的会员等级
	 * 
	 * @return changeMemberRank
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public MemberRank getChangeMemberRank() {
		return changeMemberRank;
	}

	/**
	 * 设置 改变的会员等级
	 * 
	 * @param changeMemberRank
	 *            改变的会员等级
	 */
	public void setChangeMemberRank(MemberRank changeMemberRank) {
		this.changeMemberRank = changeMemberRank;
	}

	/**
	 * 获取 启用改变的会员等级的数量
	 * 
	 * @return changeQuantity
	 */
	@Column(precision = 21, scale = 3)
	public BigDecimal getChangeQuantity() {
		return changeQuantity;
	}

	/**
	 * 设置 启用改变的会员等级的数量
	 * 
	 * @param changeQuantity
	 *            启用改变的会员等级的数量
	 */
	public void setChangeQuantity(BigDecimal changeQuantity) {
		this.changeQuantity = changeQuantity;
	}

	/**
	 * 获取 费用预算
	 * 
	 * @return budget
	 */
	public BigDecimal getBudget() {
		return budget;
	}

	/**
	 * 设置 费用预算
	 * 
	 * @param budget
	 *            费用预算
	 */
	public void setBudget(BigDecimal budget) {
		this.budget = budget;
	}

	/**
	 * 获取 冻结费用预算
	 * 
	 * @return lockBudget
	 */
	public BigDecimal getLockBudget() {
		return lockBudget;
	}

	/**
	 * 设置 冻结费用预算
	 * 
	 * @param lockBudget
	 *            冻结费用预算
	 */
	public void setLockBudget(BigDecimal lockBudget) {
		this.lockBudget = lockBudget;
	}

	/**
	 * 获取 冻结余额
	 * 
	 * @return lockBalance
	 */
	public BigDecimal getLockBalance() {
		return lockBalance;
	}

	/**
	 * 设置 冻结余额
	 * 
	 * @param lockBalance
	 *            冻结余额
	 */
	public void setLockBalance(BigDecimal lockBalance) {
		this.lockBalance = lockBalance;
	}

	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StoreAddress> getStoreAddress() {
		return storeAddress;
	}

	public void setStoreAddress(List<StoreAddress> storeAddress) {
		this.storeAddress = storeAddress;
	}

	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StoreAttach> getStoreAttachs() {
		return storeAttachs;
	}

	public void setStoreAttachs(List<StoreAttach> storeAttachs) {
		this.storeAttachs = storeAttachs;
	}

	/**
	 * 客户类型
	 * 
	 * @return
	 */
	@Transient
	public int getTypeInt() {
		return getType().ordinal();
	}

	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StoreInvoiceInfo> getStoreInvoiceInfos() {
		return storeInvoiceInfos;
	}

	public void setStoreInvoiceInfos(List<StoreInvoiceInfo> storeInvoiceInfos) {
		this.storeInvoiceInfos = storeInvoiceInfos;
	}

	@Column(precision = 21, scale = 6)
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	/**
	 * 获取 客户经理
	 * 
	 * @return storeManagers
	 */
	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StoreManager> getStoreManagers() {
		return storeManagers;
	}

	/**
	 * 设置 客户经理
	 * 
	 * @param storeManagers
	 *            客户经理
	 */
	public void setStoreManagers(List<StoreManager> storeManagers) {
		this.storeManagers = storeManagers;
	}

	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StoreSbu> getStoreSbu() {
		return storeSbu;
	}

	public void setStoreSbu(List<StoreSbu> storeSbu) {
		this.storeSbu = storeSbu;
	}

	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public String getAccountStatus() {
		return accountStatus;
	}

	public void setAccountStatus(String accountStatus) {
		this.accountStatus = accountStatus;
	}

	/** 城市等级 0省级 1 地市级 2 区县级 3乡镇级 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public Integer getAccountTypeCode() {
		return accountTypeCode;
	}

	/** 城市等级 0省级 1 地市级 2 区县级 3乡镇级 */
	public void setAccountTypeCode(Integer accountTypeCode) {
		this.accountTypeCode = accountTypeCode;
	}

	/** 创建人 */
	/*
	 * @NotNull
	 * @NotEmpty
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getCreateBy() {
		return createBy;
	}

	/** 创建人 */
	public void setCreateBy(StoreMember createBy) {
		this.createBy = createBy;
	}

	/** 是否现金客户 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public Boolean getCashClientFlag() {
		return cashClientFlag;
	}

	/** 是否现金客户 */
	public void setCashClientFlag(Boolean cashClientFlag) {
		this.cashClientFlag = cashClientFlag;
	}

	/** 平台性质 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public Integer getPlatformProperty() {
		return platformProperty;
	}

	/** 平台性质 */
	public void setPlatformProperty(Integer platformProperty) {
		this.platformProperty = platformProperty;
	}

	/** 销售平台 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSalesPlatform() {
		return salesPlatform;
	}

	/** 销售平台 */
	public void setSalesPlatform(SaleOrg salesPlatform) {
		this.salesPlatform = salesPlatform;
	}

	/** 销售区域 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public String getTerritoryName() {
		return territoryName;
	}

	/** 销售区域 */
	public void setTerritoryName(String territoryName) {
		this.territoryName = territoryName;
	}

	/** 是否加盟成功 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public Boolean getUnJoinSucessFlag() {
		return unJoinSucessFlag;
	}

	/** 是否加盟成功 */
	public void setUnJoinSucessFlag(Boolean unJoinSucessFlag) {
		this.unJoinSucessFlag = unJoinSucessFlag;
	}

	/** 经销商姓名 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public String getDealerName() {
		return dealerName;
	}

	/** 经销商姓名 */
	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	/** 授权编号 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public String getGrantCode() {
		return grantCode;
	}

	/** 授权编号 */
	public void setGrantCode(String grantCode) {
		this.grantCode = grantCode;
	}

	/** 货币 */
	/*
	 * @NotNull
	 * @Column(nullable = false)
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/** 货币 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/** 经营品类 */
	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<BusinessCategory> getBusinessCategorys() {
		return businessCategorys;
	}

	public void setBusinessCategorys(List<BusinessCategory> businessCategorys) {
		this.businessCategorys = businessCategorys;
	}

	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<CautionMoney> getCautionMoneies() {
		return cautionMoneies;
	}

	public void setCautionMoneies(List<CautionMoney> cautionMoneies) {
		this.cautionMoneies = cautionMoneies;
	}

	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<BusinessRecord> getBusinessRecords() {
		return businessRecords;
	}

	public void setBusinessRecords(List<BusinessRecord> businessRecords) {
		this.businessRecords = businessRecords;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getBusinessType() {
		return businessType;
	}

	public void setBusinessType(SystemDict businessType) {
		this.businessType = businessType;
	}

	/**
	 * 客户类型
	 * 
	 * @return
	 */
	@Transient
	public List<Integer> getAllTypeInt() {
		List<Integer> typeList = new ArrayList<Integer>();
		Type[] types = Type.values();
		for (Type type : types) {
			typeList.add(type.ordinal());
		}
		return typeList;
	}

	@ManyToOne(fetch = FetchType.EAGER)
	public SystemDict getSbu() {
		return sbu;
	}

	public void setSbu(SystemDict sbu) {
		this.sbu = sbu;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getCustomerType() {
		return customerType;
	}

	public void setCustomerType(SystemDict customerType) {
		this.customerType = customerType;
	}

	public Integer getIsSigning() {
		return isSigning;
	}

	public void setIsSigning(Integer isSigning) {
		this.isSigning = isSigning;
	}

	public Date getSigningTime() {
		return signingTime;
	}

	public void setSigningTime(Date signingTime) {
		this.signingTime = signingTime;
	}

	public String getGrade() {
		return grade;
	}

	public void setGrade(String grade) {
		this.grade = grade;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getGraden() {
		return graden;
	}

	public void setGraden(SystemDict graden) {
		this.graden = graden;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getSaleStyle() {
		return saleStyle;
	}

	public void setSaleStyle(SystemDict saleStyle) {
		this.saleStyle = saleStyle;
	}

	/** 
	 * 获取  未加盟成功 
	 * @return isJoinFalse 未加盟成功 
	 */
	public Boolean getIsJoinFalse() {
		return isJoinFalse;
	}

	/** 
	 * 设置  未加盟成功 
	 * @param isJoinFalse 未加盟成功 
	 */
	public void setIsJoinFalse(Boolean isJoinFalse) {
		this.isJoinFalse = isJoinFalse;
	}

	/** 
	 * 获取  经销商状态 
	 * @return distributorStatus 经销商状态 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SystemDict getDistributorStatus() {
		return distributorStatus;
	}

	/** 
	 * 设置  经销商状态 
	 * @param distributorStatus 经销商状态 
	 */
	public void setDistributorStatus(SystemDict distributorStatus) {
		this.distributorStatus = distributorStatus;
	}

	public String getReceiverName() {
		return receiverName;
	}

	public void setReceiverName(String receiverName) {
		this.receiverName = receiverName;
	}

	public String getReceiverPhone() {
		return receiverPhone;
	}

	public void setReceiverPhone(String receiverPhone) {
		this.receiverPhone = receiverPhone;
	}

	public String getReceiverPost() {
		return receiverPost;
	}

	public void setReceiverPost(String receiverPost) {
		this.receiverPost = receiverPost;
	}

	/** 
	 * 获取  最大流水号 
	 * @return maxSn 最大流水号 
	 */
	public String getMaxSn() {
		return maxSn;
	}

	/** 
	 * 设置  最大流水号 
	 * @param maxSn 最大流水号 
	 */
	public void setMaxSn(String maxSn) {
		this.maxSn = maxSn;
	}

	public Integer getHaveMortgage() {
		return haveMortgage;
	}

	public void setHaveMortgage(Integer haveMortgage) {
		this.haveMortgage = haveMortgage;
	}

	public Integer getHavaeProsecution() {
		return havaeProsecution;
	}

	public void setHavaeProsecution(Integer havaeProsecution) {
		this.havaeProsecution = havaeProsecution;
	}

	public Integer getHaveRelationship() {
		return haveRelationship;
	}

	public void setHaveRelationship(Integer haveRelationship) {
		this.haveRelationship = haveRelationship;
	}

	public Integer getIsDifferentPlaces() {
		return isDifferentPlaces;
	}

	public void setIsDifferentPlaces(Integer isDifferentPlaces) {
		this.isDifferentPlaces = isDifferentPlaces;
	}

	public String getLageArea() {
		return lageArea;
	}

	public void setLageArea(String lageArea) {
		this.lageArea = lageArea;
	}

	public String getOrgArea() {
		return orgArea;
	}

	public void setOrgArea(String orgArea) {
		this.orgArea = orgArea;
	}

	@OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	public List<StoreContract> getStoreContract() {
		return storeContract;
	}

	public void setStoreContract(List<StoreContract> storeContract) {
		this.storeContract = storeContract;
	}

	public Date getAreaCloseTime() {
		return areaCloseTime;
	}

	public void setAreaCloseTime(Date areaCloseTime) {
		this.areaCloseTime = areaCloseTime;
	}

	public Date getDealerInTime() {
		return dealerInTime;
	}

	public void setDealerInTime(Date dealerInTime) {
		this.dealerInTime = dealerInTime;
	}

	/**
	 * @return the contractSubject
	 */
	public String getContractSubject() {
		return contractSubject;
	}

	/**
	 * @param contractSubject the contractSubject to set
	 */
	public void setContractSubject(String contractSubject) {
		this.contractSubject = contractSubject;
	}

	/**
	 * @return the headAddress
	 */
	public String getHeadAddress() {
		return headAddress;
	}

	/**
	 * @param headAddress the headAddress to set
	 */
	public void setHeadAddress(String headAddress) {
		this.headAddress = headAddress;
	}

	/**
	 * @return the countryName
	 */
	public String getCountryName() {
		return countryName;
	}

	/**
	 * @param countryName the countryName to set
	 */
	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	/**
	 * @return the headPhone
	 */
	public String getHeadPhone() {
		return headPhone;
	}

	/**
	 * @param headPhone the headPhone to set
	 */
	public void setHeadPhone(String headPhone) {
		this.headPhone = headPhone;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public SalesArea getSalesArea() {
		return salesArea;
	}

	public void setSalesArea(SalesArea salesArea) {
		this.salesArea = salesArea;
	}

	public Integer getDealerSex() {
		return dealerSex;
	}

	public void setDealerSex(Integer dealerSex) {
		this.dealerSex = dealerSex;
	}

	public String getDealerGrade() {
		return dealerGrade;
	}

	public void setDealerGrade(String dealerGrade) {
		this.dealerGrade = dealerGrade;
	}

	public String getShopAddress() {
		return shopAddress;
	}

	public void setShopAddress(String shopAddress) {
		this.shopAddress = shopAddress;
	}

	public Integer getCompanyType() {
		return companyType;
	}

	public void setCompanyType(Integer companyType) {
		this.companyType = companyType;
	}

	public Integer getDealerBackground() {
		return dealerBackground;
	}

	public void setDealerBackground(Integer dealerBackground) {
		this.dealerBackground = dealerBackground;
	}

	public Integer getDealerLevel() {
		return dealerLevel;
	}

	public void setDealerLevel(Integer dealerLevel) {
		this.dealerLevel = dealerLevel;
	}

	public BigDecimal getSalesChannelsVal1() {
		return salesChannelsVal1;
	}

	public void setSalesChannelsVal1(BigDecimal salesChannelsVal1) {
		this.salesChannelsVal1 = salesChannelsVal1;
	}

	public BigDecimal getSalesChannelsVal2() {
		return salesChannelsVal2;
	}

	public void setSalesChannelsVal2(BigDecimal salesChannelsVal2) {
		this.salesChannelsVal2 = salesChannelsVal2;
	}

	public BigDecimal getSalesChannelsVal3() {
		return salesChannelsVal3;
	}

	public void setSalesChannelsVal3(BigDecimal salesChannelsVal3) {
		this.salesChannelsVal3 = salesChannelsVal3;
	}

	public BigDecimal getSalesChannelsVal4() {
		return salesChannelsVal4;
	}

	public void setSalesChannelsVal4(BigDecimal salesChannelsVal4) {
		this.salesChannelsVal4 = salesChannelsVal4;
	}

	public BigDecimal getSalesChannelsVal5() {
		return salesChannelsVal5;
	}

	public void setSalesChannelsVal5(BigDecimal salesChannelsVal5) {
		this.salesChannelsVal5 = salesChannelsVal5;
	}

	public BigDecimal getSalesChannelsVal6() {
		return salesChannelsVal6;
	}

	public void setSalesChannelsVal6(BigDecimal salesChannelsVal6) {
		this.salesChannelsVal6 = salesChannelsVal6;
	}

	public Integer getPropagandaAwareness() {
		return propagandaAwareness;
	}

	public void setPropagandaAwareness(Integer propagandaAwareness) {
		this.propagandaAwareness = propagandaAwareness;
	}

	public Integer getBrandAwareness() {
		return brandAwareness;
	}

	public void setBrandAwareness(Integer brandAwareness) {
		this.brandAwareness = brandAwareness;
	}

	public Integer getAfterService() {
		return afterService;
	}

	public void setAfterService(Integer afterService) {
		this.afterService = afterService;
	}

	public Integer getMarketersNumber() {
		return marketersNumber;
	}

	public void setMarketersNumber(Integer marketersNumber) {
		this.marketersNumber = marketersNumber;
	}

	public Integer getAfterSaleNumber() {
		return afterSaleNumber;
	}

	public void setAfterSaleNumber(Integer afterSaleNumber) {
		this.afterSaleNumber = afterSaleNumber;
	}

	public Integer getInstallBodyNumber() {
		return installBodyNumber;
	}

	public void setInstallBodyNumber(Integer installBodyNumber) {
		this.installBodyNumber = installBodyNumber;
	}

	public BigDecimal getWarehouseArea() {
		return warehouseArea;
	}

	public void setWarehouseArea(BigDecimal warehouseArea) {
		this.warehouseArea = warehouseArea;
	}

	public Integer getTruck() {
		return truck;
	}

	public void setTruck(Integer truck) {
		this.truck = truck;
	}

	public Integer getSmallCar() {
		return smallCar;
	}

	public void setSmallCar(Integer smallCar) {
		this.smallCar = smallCar;
	}

	public Integer getPcOrBroadband() {
		return pcOrBroadband;
	}

	public void setPcOrBroadband(Integer pcOrBroadband) {
		this.pcOrBroadband = pcOrBroadband;
	}

	public Integer getJoiningFormalities1() {
		return joiningFormalities1;
	}

	public void setJoiningFormalities1(Integer joiningFormalities1) {
		this.joiningFormalities1 = joiningFormalities1;
	}

	public Integer getJoiningFormalities2() {
		return joiningFormalities2;
	}

	public void setJoiningFormalities2(Integer joiningFormalities2) {
		this.joiningFormalities2 = joiningFormalities2;
	}
	
	public Boolean getIsCoreStroe() {
		return isCoreStroe;
	}

	public void setIsCoreStroe(Boolean isCoreStroe) {
		this.isCoreStroe = isCoreStroe;
	}

    public String getDealerBackgroundMemo() {
        return dealerBackgroundMemo;
    }

    public void setDealerBackgroundMemo(String dealerBackgroundMemo) {
        this.dealerBackgroundMemo = dealerBackgroundMemo;
    }

    public Integer getShippingState() {
        return shippingState;
    }

    public void setShippingState(Integer shippingState) {
        this.shippingState = shippingState;
    }

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}


	public Long getLinkFourId() {
		return linkFourId;
	}


	public void setLinkFourId(Long linkFourId) {
		this.linkFourId = linkFourId;
	}


	public Boolean getIsToLink5() {
		return isToLink5;
	}


	public void setIsToLink5(Boolean isToLink5) {
		this.isToLink5 = isToLink5;
	}


	public Long getLinkFiveId() {
		return linkFiveId;
	}


	public void setLinkFiveId(Long linkFiveId) {
		this.linkFiveId = linkFiveId;
	}
    
}
