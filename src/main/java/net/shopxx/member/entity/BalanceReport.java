package net.shopxx.member.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.Sbu;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
/**
 * Entity - 余额报表
 */
@Entity
@Table(name = "xx_balance_report")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_balance_report_sequence")
public class BalanceReport extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3955745159477971847L;
	
	/**客户*/
	private Store store;
	/**SBU*/
	private Sbu sbu;
	/**经营组织*/
	private Organization organization; 
	/**单据日期*/
	private Date billDate;
	/**订单金额*/
	private BigDecimal orderBalance;
	/**订单关闭金额*/
	private BigDecimal orderCloseBalance;
	/**发货金额*/
	private BigDecimal shipmentBalance;
	/**退货金额*/
	private BigDecimal returnBalance;
	/**充值金额*/
	private BigDecimal balance;
	/**政策金额*/
	private BigDecimal policyBalance;
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}
		
	public void setStore(Store store) {
		this.store = store;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Sbu getSbu() {
		return sbu;
	}
	public void setSbu(Sbu sbu) {
		this.sbu = sbu;
	}
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public Organization getOrganization() {
		return organization;
	}
	
	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
	
	public Date getBillDate() {
		return billDate;
	}
	
	public void setBillDate(Date billDate) {
		this.billDate = billDate;
	}
	
	public BigDecimal getOrderBalance() {
		return orderBalance;
	}
	
	public void setOrderBalance(BigDecimal orderBalance) {
		this.orderBalance = orderBalance;
	}
	
	public BigDecimal getOrderCloseBalance() {
		return orderCloseBalance;
	}
	
	public void setOrderCloseBalance(BigDecimal orderCloseBalance) {
		this.orderCloseBalance = orderCloseBalance;
	}
	
	public BigDecimal getShipmentBalance() {
		return shipmentBalance;
	}
	
	public void setShipmentBalance(BigDecimal shipmentBalance) {
		this.shipmentBalance = shipmentBalance;
	}
	
	public BigDecimal getReturnBalance() {
		return returnBalance;
	}
	
	public void setReturnBalance(BigDecimal returnBalance) {
		this.returnBalance = returnBalance;
	}
	
	public BigDecimal getBalance() {
		return balance;
	}
	
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}
	
	public BigDecimal getPolicyBalance() {
		return policyBalance;
	}
	
	public void setPolicyBalance(BigDecimal policyBalance) {
		this.policyBalance = policyBalance;
	} 
	
}
