package net.shopxx.member.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.validation.constraints.Pattern;

import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.entity.BaseEntity;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * Entity - 会员
 */
@Entity
@Table(name = "xx_member")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_member_sequence")
public class Member extends BaseEntity {

	private static final long serialVersionUID = 1533130686714725835L;

//	/**
//	 * 会员类型
//	 */
//	public enum MemberType {
//
//		/** 普通会员 */
//		ordinary
//	}

	/** 用户名  UUID */
	private String username;

	/** 密码 */
	private String password;

//	/** E-mail */
//	private String email;
//
//	/** 是否启用 */
//	private Boolean isEnabled;

	/** 是否锁定 */
	private Boolean isLocked;

	/** 连续登录失败次数 */
	private Integer loginFailureCount;

	/** 锁定日期 */
	private Date lockedDate;

//	/** 注册IP */
//	private String registerIp;
//
//	/** 最后登录IP */
//	private String loginIp;
//
//	/** 最后登录日期 */
//	private Date loginDate;
//
//	/** 姓名 */
//	private String name;
//
//	/** 会员类型  */
//	private MemberType memberType;

//	/** 地址 */
//	private String address;
//
//	/** 邮编 */
//	private String zipCode;

	/** 手机 */
	private String mobile;
	
	/** 身份证 */
	private String idCard;

//	/** 余额支付密码 */
//	private String payPassword;

//	/** 会员机构关系 */
//	private List<MemberOfSaleOrg> memberOfSaleOrgs = new ArrayList<MemberOfSaleOrg>();

	/**
	 * 获取用户名
	 * @return 用户名
	 */
	@NotEmpty
	@Pattern(regexp = "^[0-9a-z_A-Z\\u4e00-\\u9fa5]+$")
	@Column(nullable = false, updatable = false, unique = true, length = 100)
	public String getUsername() {
		return username;
	}

	/**
	 * 设置用户名
	 * 
	 * @param username
	 *            用户名
	 */
	public void setUsername(String username) {
		this.username = username;
	}

	/**
	 * 获取密码
	 * 
	 * @return 密码
	 */
	@NotEmpty
	@Pattern(regexp = "^[^\\s&\"<>]+$")
	@Column(nullable = false)
	public String getPassword() {
		return password;
	}

	/**
	 * 设置密码
	 * 
	 * @param password
	 *            密码
	 */
	public void setPassword(String password) {
		this.password = password;
	}

//	/**
//	 * 获取E-mail
//	 * 
//	 * @return E-mail
//	 */
//	@Email
//	@Length(max = 200)
//	public String getEmail() {
//		return email;
//	}
//
//	/**
//	 * 设置E-mail
//	 * 
//	 * @param email
//	 *            E-mail
//	 */
//	public void setEmail(String email) {
//		this.email = email;
//	}
//
//	/**
//	 * 获取当前积分
//	 * 
//	 * @return 积分
//	 */
//	@NotNull
//	@Min(0)
//	@Column(nullable = false)
//	public Long getPoint() {
//		return point;
//	}
//
//	/**
//	 * 设置当前积分
//	 * 
//	 * @param point
//	 *            积分
//	 */
//	public void setPoint(Long point) {
//		this.point = point;
//	}
//
//	public Long getMaxPoint() {
//		return maxPoint;
//	}
//
//	public void setMaxPoint(Long maxPoint) {
//		this.maxPoint = maxPoint;
//	}
//
//	/**
//	 * 获取消费金额
//	 * 
//	 * @return 消费金额
//	 */
//	@Column(nullable = false, precision = 27, scale = 12)
//	public BigDecimal getAmount() {
//		return amount;
//	}
//
//	/**
//	 * 设置消费金额
//	 * 
//	 * @param amount
//	 *            消费金额
//	 */
//	public void setAmount(BigDecimal amount) {
//		this.amount = amount;
//	}
//
//	/**
//	 * 获取余额
//	 * 
//	 * @return 余额
//	 */
//	@NotNull
//	@Min(0)
//	@Digits(integer = 12, fraction = 3)
//	@Column(nullable = false, precision = 27, scale = 12)
//	public BigDecimal getBalance() {
//		return balance;
//	}
//
//	/**
//	 * 设置余额
//	 * 
//	 * @param balance
//	 *            余额
//	 */
//	public void setBalance(BigDecimal balance) {
//		this.balance = balance;
//	}
//
//	/**
//	 * 获取是否启用
//	 * 
//	 * @return 是否启用
//	 */
//	@NotNull
//	@Column(nullable = false)
//	public Boolean getIsEnabled() {
//		return isEnabled;
//	}
//
//	/**
//	 * 设置是否启用
//	 * 
//	 * @param isEnabled
//	 *            是否启用
//	 */
//	public void setIsEnabled(Boolean isEnabled) {
//		this.isEnabled = isEnabled;
//	}

	/**
	 * 获取是否锁定
	 * 
	 * @return 是否锁定
	 */
	@Column(nullable = false)
	public Boolean getIsLocked() {
		return isLocked;
	}

	/**
	 * 设置是否锁定
	 * 
	 * @param isLocked
	 *            是否锁定
	 */
	public void setIsLocked(Boolean isLocked) {
		this.isLocked = isLocked;
	}

	/**
	 * 获取连续登录失败次数
	 * 
	 * @return 连续登录失败次数
	 */
	@Column(nullable = false)
	public Integer getLoginFailureCount() {
		return loginFailureCount;
	}

	/**
	 * 设置连续登录失败次数
	 * 
	 * @param loginFailureCount
	 *            连续登录失败次数
	 */
	public void setLoginFailureCount(Integer loginFailureCount) {
		this.loginFailureCount = loginFailureCount;
	}

	/**
	 * 获取锁定日期
	 * 
	 * @return 锁定日期
	 */
	public Date getLockedDate() {
		return lockedDate;
	}

	/**
	 * 设置锁定日期
	 * 
	 * @param lockedDate
	 *            锁定日期
	 */
	public void setLockedDate(Date lockedDate) {
		this.lockedDate = lockedDate;
	}

//	/**
//	 * 获取注册IP
//	 * 
//	 * @return 注册IP
//	 */
//	@Column(nullable = false, updatable = false)
//	public String getRegisterIp() {
//		return registerIp;
//	}
//
//	/**
//	 * 设置注册IP
//	 * 
//	 * @param registerIp
//	 *            注册IP
//	 */
//	public void setRegisterIp(String registerIp) {
//		this.registerIp = registerIp;
//	}
//
//	/**
//	 * 获取最后登录IP
//	 * 
//	 * @return 最后登录IP
//	 */
//	public String getLoginIp() {
//		return loginIp;
//	}
//
//	/**
//	 * 设置最后登录IP
//	 * 
//	 * @param loginIp
//	 *            最后登录IP
//	 */
//	public void setLoginIp(String loginIp) {
//		this.loginIp = loginIp;
//	}
//
//	/**
//	 * 获取最后登录日期
//	 * 
//	 * @return 最后登录日期
//	 */
//	public Date getLoginDate() {
//		return loginDate;
//	}
//
//	/**
//	 * 获取 isChecked
//	 * @return the isChecked
//	 */
//	public Boolean getIsChecked() {
//		return isChecked;
//	}
//
//	/**
//	 * 设置 isChecked 
//	 * @param isChecked the isChecked to set
//	 */
//	public void setIsChecked(Boolean isChecked) {
//		this.isChecked = isChecked;
//	}
//
//	/**
//	 * 设置最后登录日期
//	 * 
//	 * @param loginDate
//	 *            最后登录日期
//	 */
//	public void setLoginDate(Date loginDate) {
//		this.loginDate = loginDate;
//	}
//
//	/**
//	 * 获取姓名
//	 * 
//	 * @return 姓名
//	 */
//	@Length(max = 200)
//	public String getName() {
//		return name;
//	}
//
//	/**
//	 * 设置姓名
//	 * 
//	 * @param name
//	 *            姓名
//	 */
//	public void setName(String name) {
//		this.name = name;
//	}
//
//	/**
//	 * 获取性别
//	 * 
//	 * @return 性别
//	 */
//	public Gender getGender() {
//		return gender;
//	}
//
//	/**
//	 * 设置性别
//	 * 
//	 * @param gender
//	 *            性别
//	 */
//	public void setGender(Gender gender) {
//		this.gender = gender;
//	}
//
//	/**
//	 * 获取会员类型
//	 * 
//	 * @return 会员类型
//	 */
//	public MemberType getMemberType() {
//		return memberType;
//	}
//
//	/**
//	 * 设置会员类型
//	 * 
//	 * @param memberType
//	 *            会员类型
//	 */
//	public void setMemberType(MemberType memberType) {
//		this.memberType = memberType;
//	}
//
//	/**
//	 * 获取出生日期
//	 * 
//	 * @return 出生日期
//	 */
//	public Date getBirth() {
//		return birth;
//	}
//
//	/**
//	 * 设置出生日期
//	 * 
//	 * @param birth
//	 *            出生日期
//	 */
//	public void setBirth(Date birth) {
//		this.birth = birth;
//	}
//
//	/**
//	 * 获取地址
//	 * 
//	 * @return 地址
//	 */
//	@Length(max = 200)
//	public String getAddress() {
//		return address;
//	}
//
//	/**
//	 * 设置地址
//	 * 
//	 * @param address
//	 *            地址
//	 */
//	public void setAddress(String address) {
//		this.address = address;
//	}
//
//	/**
//	 * 获取邮编
//	 * 
//	 * @return 邮编
//	 */
//	@Length(max = 200)
//	public String getZipCode() {
//		return zipCode;
//	}
//
//	/**
//	 * 设置邮编
//	 * 
//	 * @param zipCode
//	 *            邮编
//	 */
//	public void setZipCode(String zipCode) {
//		this.zipCode = zipCode;
//	}
//
//	/**
//	 * 获取电话
//	 * 
//	 * @return 电话
//	 */
//	@Length(max = 200)
//	public String getPhone() {
//		return phone;
//	}
//
//	/**
//	 * 设置电话
//	 * 
//	 * @param phone
//	 *            电话
//	 */
//	public void setPhone(String phone) {
//		this.phone = phone;
//	}

	/**
	 * 获取手机
	 * 
	 * @return 手机
	 */
	@NotEmpty
	@Length(max = 200)
	@Column(unique = true)
	public String getMobile() {
		return mobile;
	}

	/**
	 * 设置手机
	 * 
	 * @param mobile
	 *            手机
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

//	/**
//	 * 获取支付宝
//	 * 
//	 * @return 支付宝
//	 */
//	@Length(max = 200)
//	public String getZhifubao() {
//		return zhifubao;
//	}
//
//	/**
//	 * 设置支付宝
//	 * 
//	 * @param mobile
//	 *            支付宝
//	 */
//	public void setZhifubao(String zhifubao) {
//		this.zhifubao = zhifubao;
//	}
//
//	/**
//	 * 获取银行账号
//	 * 
//	 * @return 银行账号
//	 */
//	@Length(max = 200)
//	public String getYinghanzhanghao() {
//		return yinghanzhanghao;
//	}
//
//	/**
//	 * 设置银行账号
//	 * 
//	 * @param mobile
//	 *            银行账号
//	 */
//	public void setYinghanzhanghao(String yinghanzhanghao) {
//		this.yinghanzhanghao = yinghanzhanghao;
//	}
//
//	/**
//	 * 获取QQ
//	 * 
//	 * @return QQ
//	 */
//	@Length(max = 200)
//	public String getQq() {
//		return qq;
//	}
//
//	/**
//	 * 设置QQ
//	 * 
//	 * @param mobile
//	 *            QQ
//	 */
//	public void setQq(String qq) {
//		this.qq = qq;
//	}
//
//	/**
//	 * 获取旺旺
//	 * 
//	 * @return 旺旺
//	 */
//	@Length(max = 200)
//	public String getWangwang() {
//		return wangwang;
//	}
//
//	/**
//	 * 设置旺旺
//	 * 
//	 * @param mobile
//	 *            旺旺  
//	 */
//	public void setWangwang(String wangwang) {
//		this.wangwang = wangwang;
//	}
//
//	public String getDangweimingchen() {
//		return dangweimingchen;
//	}
//
//	public void setDangweimingchen(String dangweimingchen) {
//		this.dangweimingchen = dangweimingchen;
//	}
//
//	public String getNashuishibiehao() {
//		return nashuishibiehao;
//	}
//
//	public void setNashuishibiehao(String nashuishibiehao) {
//		this.nashuishibiehao = nashuishibiehao;
//	}
//
//	public String getZhucedizhi() {
//		return zhucedizhi;
//	}
//
//	public void setZhucedizhi(String zhucedizhi) {
//		this.zhucedizhi = zhucedizhi;
//	}
//
//	public String getZhucedianhua() {
//		return zhucedianhua;
//	}
//
//	public void setZhucedianhua(String zhucedianhua) {
//		this.zhucedianhua = zhucedianhua;
//	}
//
//	public String getKaihuyh() {
//		return kaihuyh;
//	}
//
//	public void setKaihuyh(String kaihuyh) {
//		this.kaihuyh = kaihuyh;
//	}
//
//	public String getZengzhishuiyhzh() {
//		return zengzhishuiyhzh;
//	}
//
//	public void setZengzhishuiyhzh(String zengzhishuiyhzh) {
//		this.zengzhishuiyhzh = zengzhishuiyhzh;
//	}

//	/**
//	 * 获取会员注册项值0
//	 * 
//	 * @return 会员注册项值0
//	 */
//	@Length(max = 200)
//	public String getAttributeValue0() {
//		return attributeValue0;
//	}
//
//	/**
//	 * 设置会员注册项值0
//	 * 
//	 * @param attributeValue0
//	 *            会员注册项值0
//	 */
//	public void setAttributeValue0(String attributeValue0) {
//		this.attributeValue0 = attributeValue0;
//	}
//
//	/**
//	 * 获取会员注册项值1
//	 * 
//	 * @return 会员注册项值1
//	 */
//	@Length(max = 200)
//	public String getAttributeValue1() {
//		return attributeValue1;
//	}
//
//	/**
//	 * 设置会员注册项值1
//	 * 
//	 * @param attributeValue1
//	 *            会员注册项值1
//	 */
//	public void setAttributeValue1(String attributeValue1) {
//		this.attributeValue1 = attributeValue1;
//	}
//
//	/**
//	 * 获取会员注册项值2
//	 * 
//	 * @return 会员注册项值2
//	 */
//	@Length(max = 200)
//	public String getAttributeValue2() {
//		return attributeValue2;
//	}
//
//	/**
//	 * 设置会员注册项值2
//	 * 
//	 * @param attributeValue2
//	 *            会员注册项值2
//	 */
//	public void setAttributeValue2(String attributeValue2) {
//		this.attributeValue2 = attributeValue2;
//	}
//
//	/**
//	 * 获取会员注册项值3
//	 * 
//	 * @return 会员注册项值3
//	 */
//	@Length(max = 200)
//	public String getAttributeValue3() {
//		return attributeValue3;
//	}
//
//	/**
//	 * 设置会员注册项值3
//	 * 
//	 * @param attributeValue3
//	 *            会员注册项值3
//	 */
//	public void setAttributeValue3(String attributeValue3) {
//		this.attributeValue3 = attributeValue3;
//	}
//
//	/**
//	 * 获取会员注册项值4
//	 * 
//	 * @return 会员注册项值4
//	 */
//	@Length(max = 200)
//	public String getAttributeValue4() {
//		return attributeValue4;
//	}
//
//	/**
//	 * 设置会员注册项值4
//	 * 
//	 * @param attributeValue4
//	 *            会员注册项值4
//	 */
//	public void setAttributeValue4(String attributeValue4) {
//		this.attributeValue4 = attributeValue4;
//	}
//
//	/**
//	 * 获取会员注册项值5
//	 * 
//	 * @return 会员注册项值5
//	 */
//	@Length(max = 200)
//	public String getAttributeValue5() {
//		return attributeValue5;
//	}
//
//	/**
//	 * 设置会员注册项值5
//	 * 
//	 * @param attributeValue5
//	 *            会员注册项值5
//	 */
//	public void setAttributeValue5(String attributeValue5) {
//		this.attributeValue5 = attributeValue5;
//	}
//
//	/**
//	 * 获取会员注册项值6
//	 * 
//	 * @return 会员注册项值6
//	 */
//	@Length(max = 200)
//	public String getAttributeValue6() {
//		return attributeValue6;
//	}
//
//	/**
//	 * 设置会员注册项值6
//	 * 
//	 * @param attributeValue6
//	 *            会员注册项值6
//	 */
//	public void setAttributeValue6(String attributeValue6) {
//		this.attributeValue6 = attributeValue6;
//	}
//
//	/**
//	 * 获取会员注册项值7
//	 * 
//	 * @return 会员注册项值7
//	 */
//	@Length(max = 200)
//	public String getAttributeValue7() {
//		return attributeValue7;
//	}
//
//	/**
//	 * 设置会员注册项值7
//	 * 
//	 * @param attributeValue7
//	 *            会员注册项值7
//	 */
//	public void setAttributeValue7(String attributeValue7) {
//		this.attributeValue7 = attributeValue7;
//	}
//
//	/**
//	 * 获取会员注册项值8
//	 * 
//	 * @return 会员注册项值8
//	 */
//	@Length(max = 200)
//	public String getAttributeValue8() {
//		return attributeValue8;
//	}
//
//	/**
//	 * 设置会员注册项值8
//	 * 
//	 * @param attributeValue8
//	 *            会员注册项值8
//	 */
//	public void setAttributeValue8(String attributeValue8) {
//		this.attributeValue8 = attributeValue8;
//	}
//
//	/**
//	 * 获取会员注册项值9
//	 * 
//	 * @return 会员注册项值9
//	 */
//	@Length(max = 200)
//	public String getAttributeValue9() {
//		return attributeValue9;
//	}
//
//	/**
//	 * 设置会员注册项值9
//	 * 
//	 * @param attributeValue9
//	 *            会员注册项值9
//	 */
//	public void setAttributeValue9(String attributeValue9) {
//		this.attributeValue9 = attributeValue9;
//	}
//
//	/**
//	 * 获取地区
//	 * 
//	 * @return 地区
//	 */
//	@ManyToOne(fetch = FetchType.LAZY)
//	public Area getArea() {
//		return area;
//	}
//
//	/**
//	 * 设置地区
//	 * 
//	 * @param area
//	 *            地区
//	 */
//	public void setArea(Area area) {
//		this.area = area;
//	}
//
//	/**
//	 * 获取会员等级
//	 * 
//	 * @return 会员等级
//	 */
//	@NotNull
//	@ManyToOne(fetch = FetchType.LAZY)
//	@JoinColumn(nullable = false)
//	public MemberRank getMemberRank() {
//		return memberRank;
//	}
//
//	/**
//	 * 设置会员等级
//	 * 
//	 * @param memberRank
//	 *            会员等级
//	 */
//	public void setMemberRank(MemberRank memberRank) {
//		this.memberRank = memberRank;
//	}
//
//	/**
//	 * 获取会员注册项值
//	 * 
//	 * @param memberAttribute
//	 *            会员注册项
//	 * @return 会员注册项值
//	 */
//	@Transient
//	public Object getAttributeValue(MemberAttribute memberAttribute) {
//		if (memberAttribute != null) {
//			if (memberAttribute.getType() == Type.name) {
//				return getName();
//			}
//			else if (memberAttribute.getType() == Type.gender) {
//				return getGender();
//			}
//			else if (memberAttribute.getType() == Type.birth) {
//				return getBirth();
//			}
//			else if (memberAttribute.getType() == Type.area) {
//				return getArea();
//			}
//			else if (memberAttribute.getType() == Type.address) {
//				return getAddress();
//			}
//			else if (memberAttribute.getType() == Type.zipCode) {
//				return getZipCode();
//			}
//			else if (memberAttribute.getType() == Type.phone) {
//				return getPhone();
//			}
//			else if (memberAttribute.getType() == Type.mobile) {
//				return getMobile();
//			}
//			else if (memberAttribute.getType() == Type.checkbox) {
//				if (memberAttribute.getPropertyIndex() != null) {
//					try {
//						String propertyName = ATTRIBUTE_VALUE_PROPERTY_NAME_PREFIX
//								+ memberAttribute.getPropertyIndex();
//						String propertyValue = (String) PropertyUtils.getProperty(this,
//								propertyName);
//						if (propertyValue != null) {
//							return JsonUtils.toObject(propertyValue, List.class);
//						}
//					}
//					catch (IllegalAccessException e) {
//						e.printStackTrace();
//					}
//					catch (InvocationTargetException e) {
//						e.printStackTrace();
//					}
//					catch (NoSuchMethodException e) {
//						e.printStackTrace();
//					}
//				}
//			}
//			else {
//				if (memberAttribute.getPropertyIndex() != null) {
//					try {
//						String propertyName = ATTRIBUTE_VALUE_PROPERTY_NAME_PREFIX
//								+ memberAttribute.getPropertyIndex();
//						return (String) PropertyUtils.getProperty(this,
//								propertyName);
//					}
//					catch (IllegalAccessException e) {
//						e.printStackTrace();
//					}
//					catch (InvocationTargetException e) {
//						e.printStackTrace();
//					}
//					catch (NoSuchMethodException e) {
//						e.printStackTrace();
//					}
//				}
//			}
//		}
//		return null;
//	}
//
//	/**
//	 * 设置会员注册项值
//	 * 
//	 * @param memberAttribute
//	 *            会员注册项
//	 * @param attributeValue
//	 *            会员注册项值
//	 */
//	@Transient
//	public void setAttributeValue(MemberAttribute memberAttribute,
//			Object attributeValue) {
//		if (memberAttribute != null) {
//			if (attributeValue instanceof String
//					&& StringUtils.isEmpty((String) attributeValue)) {
//				attributeValue = null;
//			}
//			if (memberAttribute.getType() == Type.name
//					&& (attributeValue instanceof String || attributeValue == null)) {
//				setName((String) attributeValue);
//			}
//			else if (memberAttribute.getType() == Type.gender
//					&& (attributeValue instanceof Gender || attributeValue == null)) {
//				setGender((Gender) attributeValue);
//			}
//			else if (memberAttribute.getType() == Type.birth
//					&& (attributeValue instanceof Date || attributeValue == null)) {
//				setBirth((Date) attributeValue);
//			}
//			else if (memberAttribute.getType() == Type.area
//					&& (attributeValue instanceof Area || attributeValue == null)) {
//				setArea((Area) attributeValue);
//			}
//			else if (memberAttribute.getType() == Type.address
//					&& (attributeValue instanceof String || attributeValue == null)) {
//				setAddress((String) attributeValue);
//			}
//			else if (memberAttribute.getType() == Type.zipCode
//					&& (attributeValue instanceof String || attributeValue == null)) {
//				setZipCode((String) attributeValue);
//			}
//			else if (memberAttribute.getType() == Type.phone
//					&& (attributeValue instanceof String || attributeValue == null)) {
//				setPhone((String) attributeValue);
//			}
//			else if (memberAttribute.getType() == Type.mobile
//					&& (attributeValue instanceof String || attributeValue == null)) {
//				setMobile((String) attributeValue);
//			}
//			else if (memberAttribute.getType() == Type.checkbox
//					&& (attributeValue instanceof List || attributeValue == null)) {
//				if (memberAttribute.getPropertyIndex() != null) {
//					if (attributeValue == null
//							|| (memberAttribute.getOptions() != null && memberAttribute.getOptions()
//									.containsAll((List<?>) attributeValue))) {
//						try {
//							String propertyName = ATTRIBUTE_VALUE_PROPERTY_NAME_PREFIX
//									+ memberAttribute.getPropertyIndex();
//							PropertyUtils.setProperty(this,
//									propertyName,
//									JsonUtils.toJson(attributeValue));
//						}
//						catch (IllegalAccessException e) {
//							e.printStackTrace();
//						}
//						catch (InvocationTargetException e) {
//							e.printStackTrace();
//						}
//						catch (NoSuchMethodException e) {
//							e.printStackTrace();
//						}
//					}
//				}
//			}
//			else {
//				if (memberAttribute.getPropertyIndex() != null) {
//					try {
//						String propertyName = ATTRIBUTE_VALUE_PROPERTY_NAME_PREFIX
//								+ memberAttribute.getPropertyIndex();
//						PropertyUtils.setProperty(this,
//								propertyName,
//								attributeValue);
//					}
//					catch (IllegalAccessException e) {
//						e.printStackTrace();
//					}
//					catch (InvocationTargetException e) {
//						e.printStackTrace();
//					}
//					catch (NoSuchMethodException e) {
//						e.printStackTrace();
//					}
//				}
//			}
//		}
//	}
//
//	/**
//	 * 移除所有会员注册项值
//	 */
//	@Transient
//	public void removeAttributeValue() {
//		setName(null);
//		setGender(null);
//		setBirth(null);
//		setArea(null);
//		setAddress(null);
//		setZipCode(null);
//		setPhone(null);
//		setMobile(null);
//		for (int i = 0; i < ATTRIBUTE_VALUE_PROPERTY_COUNT; i++) {
//			String propertyName = ATTRIBUTE_VALUE_PROPERTY_NAME_PREFIX + i;
//			try {
//				PropertyUtils.setProperty(this, propertyName, null);
//			}
//			catch (IllegalAccessException e) {
//				e.printStackTrace();
//			}
//			catch (InvocationTargetException e) {
//				e.printStackTrace();
//			}
//			catch (NoSuchMethodException e) {
//				e.printStackTrace();
//			}
//		}
//	}
//
//	/**
//	 * 获取微信会员id
//	 * @return
//	 */
//	@Column(unique = true)
//	public String getWechatVipId() {
//		return wechatVipId;
//	}
//
//	/**
//	 * 设置微信会员id
//	 * @param wechatVipId
//	 */
//	public void setWechatVipId(String wechatVipId) {
//		this.wechatVipId = wechatVipId;
//	}
//
//	/**
//	 * 获取 recommend
//	 * @return the recommend
//	 */
//	@ManyToOne(fetch = FetchType.LAZY)
//	public Member getRecommend() {
//		return recommend;
//	}
//
//	/**
//	 * 设置 recommend 
//	 * @param recommend the recommend to set
//	 */
//	public void setRecommend(Member recommend) {
//		this.recommend = recommend;
//	}
//
//	/**
//	 * 获取 grade
//	 * @return the grade
//	 */
//	public Integer getGrade() {
//		return grade;
//	}
//
//	/**
//	 * 设置 grade 
//	 * @param grade the grade to set
//	 */
//	public void setGrade(Integer grade) {
//		this.grade = grade;
//	}
//
//	/**
//	 * 获取 treePath
//	 * @return the treePath
//	 */
//	public String getTreePath() {
//		return treePath;
//	}
//
//	/**
//	 * 设置 treePath 
//	 * @param treePath the treePath to set
//	 */
//	public void setTreePath(String treePath) {
//		this.treePath = treePath;
//	}
//
//	/**
//	 * 获取 children
//	 * @return the children
//	 */
//	@OneToMany(mappedBy = "recommend", fetch = FetchType.LAZY)
//	public Set<Member> getChildren() {
//		return children;
//	}
//
//	/**
//	 * 设置 children 
//	 * @param children the children to set
//	 */
//	public void setChildren(Set<Member> children) {
//		this.children = children;
//	}
//
//	/**
//	 * 获取 isPartner
//	 * @return the isPartner
//	 */
//	public Boolean getIsPartner() {
//		return isPartner;
//	}
//
//	/**
//	 * 设置 isPartner 
//	 * @param isPartner the isPartner to set
//	 */
//	public void setIsPartner(Boolean isPartner) {
//		if (isPartner == null) {
//			this.isPartner = false;
//		}
//		else {
//			this.isPartner = isPartner;
//		}
//	}
//
//	/**
//	 * 获取 applyPartnerStatus
//	 * @return the applyPartnerStatus
//	 */
//	public ApplyPartnerStatus getApplyPartnerStatus() {
//		return applyPartnerStatus;
//	}
//
//	/**
//	 * 设置 applyPartnerStatus 
//	 * @param applyPartnerStatus the applyPartnerStatus to set
//	 */
//	public void setApplyPartnerStatus(ApplyPartnerStatus applyPartnerStatus) {
//		this.applyPartnerStatus = applyPartnerStatus;
//	}
//
//	/**
//	 * 获取 idCode
//	 * @return the idCode
//	 */
//	public String getIdCode() {
//		return idCode;
//	}
//
//	/**
//	 * 设置 idCode 
//	 * @param idCode the idCode to set
//	 */
//	public void setIdCode(String idCode) {
//		this.idCode = idCode;
//	}
//
//	/**
//	 * @return the partnerCategory
//	 */
//	@ManyToOne(fetch = FetchType.LAZY)
//	public PartnerCategory getPartnerCategory() {
//		return partnerCategory;
//	}
//
//	/**
//	 * @param partnerCategory the partnerCategory to set
//	 */
//	public void setPartnerCategory(PartnerCategory partnerCategory) {
//		this.partnerCategory = partnerCategory;
//	}
//
//	/**
//	 * 获取 idUrl1
//	 * @return the idUrl1
//	 */
//	public String getIdUrl1() {
//		return idUrl1;
//	}
//
//	/**
//	 * 设置 idUrl1 
//	 * @param idUrl1 the idUrl1 to set
//	 */
//	public void setIdUrl1(String idUrl1) {
//		this.idUrl1 = idUrl1;
//	}
//
//	/**
//	 * 获取 idUrl2
//	 * @return the idUrl2
//	 */
//	public String getIdUrl2() {
//		return idUrl2;
//	}
//
//	/**
//	 * 设置 idUrl2 
//	 * @param idUrl2 the idUrl2 to set
//	 */
//	public void setIdUrl2(String idUrl2) {
//		this.idUrl2 = idUrl2;
//	}
//
//	/**
//	 * 获取支付密码
//	 * 
//	 * @return 密码
//	 */
//	@NotEmpty
//	@Pattern(regexp = "^[^\\s&\"<>]+$")
//	@Column(nullable = false)
//	public String getPayPassword() {
//		return payPassword;
//	}
//
//	public void setPayPassword(String payPassword) {
//		this.payPassword = payPassword;
//	}
//
//	/**
//	 * 获取 memberOfSaleOrgs
//	 * @return memberOfSaleOrgs
//	 */
//	@OneToMany(mappedBy = "member", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
//	public List<MemberOfSaleOrg> getMemberOfSaleOrgs() {
//		return memberOfSaleOrgs;
//	}
//
//	/**
//	 * 设置 memberOfSaleOrgs
//	 * @param memberOfSaleOrgs
//	 */
//	public void setMemberOfSaleOrgs(List<MemberOfSaleOrg> memberOfSaleOrgs) {
//		this.memberOfSaleOrgs = memberOfSaleOrgs;
//	}

	/**
	 * 持久化前处理
	 */
	@PrePersist
	public void prePersist() {
//		Member recommend = getRecommend();
//		if (recommend != null) {
//			setTreePath(recommend.getTreePath()
//					+ recommend.getId()
//					+ ID_PATH_SEPARATOR);
//		}
//		else {
//			setTreePath(ID_PATH_SEPARATOR);
//		}
//		setGrade(getTreePaths().size());
//		setPartnerType(getPartnerCategory() == null ? null
//				: getPartnerCategory().getName());
		if (getUsername() == null) {
			setUsername(Sequence.getInstance().getSequence(null));
		}
	}

	/**
	 * 更新前处理
	 */
	@PreUpdate
	public void preUpdate() {
//		Member recommend = getRecommend();
//		if (recommend != null) {
//			setTreePath(recommend.getTreePath()
//					+ recommend.getId()
//					+ ID_PATH_SEPARATOR);
//		}
//		else {
//			setTreePath(ID_PATH_SEPARATOR);
//		}
//		setGrade(getTreePaths().size());
//		setPartnerType(getPartnerCategory() == null ? null
//				: getPartnerCategory().getName());
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

//	/**
//	 * 获取树路径
//	 * 
//	 * @return 树路径
//	 */
//	@Transient
//	public List<Long> getTreePaths() {
//		List<Long> treePaths = new ArrayList<Long>();
//		String[] ids = StringUtils.split(getTreePath(), ID_PATH_SEPARATOR);
//		if (ids != null) {
//			for (String id : ids) {
//				treePaths.add(Long.valueOf(id));
//			}
//		}
//		return treePaths;
//	}
}