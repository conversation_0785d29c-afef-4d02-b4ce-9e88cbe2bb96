package net.shopxx.member.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.shop.entity.ShopInfo;

import javax.persistence.*;
import java.util.Date;

/**
 * Entity - 客户退出门店关联表
 */
@Entity
@Table(name = "xx_store_exit_shop_info")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_store_exit_shop_info_sequence")
public class StoreExitShopInfo extends BaseEntity{
	
	private static final long serialVersionUID = 2141274891274721L;
	
	/** 客户退出 */
	private StoreExit storeExit;
	
	/** 门店资料 */
	private ShopInfo shopInfo;
	
	/** 是否关闭 */
	private Boolean isClose;
	
	/** 是否退出 */
	private Boolean isExit;
	
	/** 门店减少档案编号*/
	private String decreaseCode;
	
	/** 时间 */
	private Date offTime;
	
	/** 原因 */
	private String cause;

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public StoreExit getStoreExit() {
		return storeExit;
	}

	public void setStoreExit(StoreExit storeExit) {
		this.storeExit = storeExit;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	public ShopInfo getShopInfo() {
		return shopInfo;
	}

	public void setShopInfo(ShopInfo shopInfo) {
		this.shopInfo = shopInfo;
	}

	public Boolean getIsClose() {
		return isClose;
	}

	public void setIsClose(Boolean isClose) {
		this.isClose = isClose;
	}

	public Boolean getIsExit() {
		return isExit;
	}

	public void setIsExit(Boolean isExit) {
		this.isExit = isExit;
	}

	public String getDecreaseCode() {
		return decreaseCode;
	}

	public void setDecreaseCode(String decreaseCode) {
		this.decreaseCode = decreaseCode;
	}

	public Date getOffTime() {
		return offTime;
	}

	public void setOffTime(Date offTime) {
		this.offTime = offTime;
	}

	public String getCause() {
		return cause;
	}

	public void setCause(String cause) {
		this.cause = cause;
	}
	
}
