package net.shopxx.appfrom.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.appfrom.service.ShowSignInService;
import net.shopxx.appfrom.service.ShowStoreMemberService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
@Controller("showStoreMemberController")
@RequestMapping("/show/store_member")
public class ShowStoreMemberController extends BaseController{
	
	@Resource(name = "showStoreMemberServiceImpl")
	private ShowStoreMemberService showStoreMemberService;
	@Resource(name = "showSignInServiceImpl")
	private ShowSignInService showSignInService;
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model) {

		return "/show/store_member/list_tb";
	}
	/**
	 * 业务员管理后台报表列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list_order(Pageable pageable,Integer multi, ModelMap model) {
		model.addAttribute("multi", multi);
		return "/show/store_member/list";
	}
	
	@ResponseBody
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public ResultMsg list_data_order(String name,Long[] saleOrgId, String planFirstTime, String planListTime, String summaryFirstTime, String summaryListTime, Pageable pageable) {
		Page<Map<String, Object>> page = showStoreMemberService.findPage(name,saleOrgId,planFirstTime,planListTime,summaryFirstTime,summaryListTime,pageable);
		List<Map<String, Object>> c = page.getContent();
		for(Map<String, Object> m:c){			
			Long storeMemberId = Long.parseLong(m.get("sm_id").toString());
			String saleOrgId1 = m.get("sale_org").toString();
			List<String> str = new ArrayList<String>();
			List<Map<String, Object>> postList = showStoreMemberService.findpostName(storeMemberId, saleOrgId1);
			for(Map<String, Object> p : postList){
				str.add(p.get("name").toString());
			}
			String[] strsStrings = str.toArray(new String[]{});
			m.put("post", splicer(strsStrings));
		}
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	protected String splicer(String[] str){
		String s = "";
		if(str!=null&&str.length>0){
			for(int i=0;i<str.length;i++){
				if(str.length-1==i){
					s += str[i];
				}else{
					s += str[i]+",";
				}
			}			
		}
		return s;
	}
	/**
	 *条件导出
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String name,Long[] saleOrgId,
			String planFirstTime,String planListTime, String summaryFirstTime, String summaryListTime,
			Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = showStoreMemberService.countReport(name,
				saleOrgId,
				planFirstTime,
				planListTime,
				summaryFirstTime,
				summaryListTime,
				pageable,
				null,
				null);
		
		int size = page.getContent().size();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String name,Long[] saleOrgId,
			String planFirstTime,String planListTime, String summaryFirstTime, String summaryListTime,
			Pageable pageable,ModelMap model, Integer page) {
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = showStoreMemberService.findReportList(name, 
				saleOrgId,
				planFirstTime,
				planListTime,
				summaryFirstTime,
				summaryListTime,
				pageable,
				page,
				size);
		for(Map<String, Object> m:data){
			Long storeMemberId = Long.parseLong(m.get("sm_id").toString());
			String saleOrgId1 = m.get("sale_org").toString();
			List<String> str = new ArrayList<String>();
			List<Map<String, Object>> postList = showStoreMemberService.findpostName(storeMemberId, saleOrgId1);
			for(Map<String, Object> p : postList){
				str.add(p.get("name").toString());
			}
			String[] strsStrings = str.toArray(new String[]{});
			m.put("post", splicer(strsStrings));
		}
		return getModelAndViewForList(data, model);
	}
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {

		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		//设置标题
		String[] header = { "用户名",
				"业务员姓名",
				"区域",
				"岗位",
				"计划时间",
				"总结时间",
				"工作计划",
				"工作总结"};

		//设置单元格取值
		String[] properties = { "username",
				"name",
				"sale_org_name",
				"post",
				"plan_time",
				"summary_time",
				"content",				
				"summary"};
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
//	@RequestMapping(value = "/select_sale_org", method = RequestMethod.GET)
//	public String select_sale_org(Pageable pageable, ModelMap model) {
//		return "/show/sign_in/select_sale_org";
//	}
//	
//	@ResponseBody
//	@RequestMapping(value = "/select_sale_org_data", method = RequestMethod.POST)
//	public ResultMsg select_sale_org_data(String saleOrgName,Pageable pageable) {
//		Page<Map<String, Object>> page = showSignInService.findSaleOrg(saleOrgName,pageable);
//		String jsonPage = JsonUtils.toJson(page);
//		return success(jsonPage);
//	}
}
