package net.shopxx.appfrom.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.appfrom.service.ShowAppService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;

/**
 * Controller - 小程序报表
 */
@Controller("showAppController")
@RequestMapping("/show/app")
public class ShowAppController extends BaseController{
	
	@Resource(name = "showAppServiceImpl")
	private ShowAppService showAppService;
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model) {

		return "/show/app/list_tb";
	}
	/**
	 * 小程序活动报表列表
	 */
	@RequestMapping(value = "/list_show", method = RequestMethod.GET)
	public String list_order(Pageable pageable, ModelMap model) {
		return "/show/app/list_show";
	}
	
	/**
	 * 小程序活动报表列表数据
	 */
	@ResponseBody
	@RequestMapping(value = "/list_data_show", method = RequestMethod.POST)
	public ResultMsg list_data_order(String subject, String areaName, String areaManager,String startDate, String endDate, Pageable pageable) {
		Page<Map<String, Object>> page = showAppService.findPage(subject,
				areaName,
				areaManager,
				startDate,
				endDate,
				pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	/**
	 *条件导出
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String subject, String areaName, String areaManager, String startDate, String endDate,Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = showAppService.countReport(subject,
				areaName,
				areaManager,
				startDate,
				endDate,
				pageable,
				null,
				null);
		
		int size = page.getContent().size();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String subject, String areaName, String areaManager, String startDate, String endDate, Pageable pageable,
			ModelMap model, Integer page) {
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = showAppService.findReportList(subject,
				areaName,
				areaManager,
				startDate, 
				endDate,
				pageable,
				page,
				size);
		return getModelAndViewForList(data, model);
	}
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {

		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		//设置标题
		String[] header = { "活动主题",
				"活动开始时间",
				"活动结束时间",
				"所属主办活动",
				"区域",
				"参与经销商数",
				"参与门店数",
				"参与导购数",
				"阅读量",
				"访客量",
				"售票",
				"成交量",
				"售票增量",
				"成交增量",
				"关闭量",};

		//设置单元格取值
		String[] properties = { "subject",
				"begin_time",
				"end_time",
				"main_subject",
				"area_name",				
				"boss_count",				
				"store_count", 
				"waiter_count",
				"read_count",
				"vistor_count",
				"sale",
				"deal",
				"sale_plus",
				"deal_plus",
				"cancel",};
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
}
