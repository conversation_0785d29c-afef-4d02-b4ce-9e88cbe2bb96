package net.shopxx.appfrom.controller;

import net.shopxx.appfrom.service.ShowShopProductService;
import net.shopxx.appfrom.service.ShowSignInService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopInfoService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 门店上样报表
 */
@Controller("showShopProductController")
@RequestMapping("/show/shopProduct")
public class ShowShopProductController extends BaseController{
	
	@Resource(name = "showSignInServiceImpl")
	private ShowSignInService showSignInService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "showShopProductImpl")
	private ShowShopProductService showShopProductService;




	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model) {

		return "/show/shop_product/list_tb";
	}

	/**
	 * 业务员签到报表列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list_order(Pageable pageable,Integer multi, ModelMap model) {
		model.addAttribute("multi", multi);
		return "/show/shop_product/list";
	}


	/**
	 * 业务员签到报表列表数据
	 */
	@ResponseBody
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public ResultMsg list_data_order(String shopName,//店名
									 String distributorName,//经销商姓名
									 String authorizationCode,
									 Long[] saleOrgId,//机构
									 Long[] storeMemberId,//区域经理
									// String shopStatus,
									// String transitTime,
									 Pageable pageable,
									 ModelMap model) {


        Page<Map<String, Object>> result = showShopProductService.findShopInfoList(shopName, distributorName, authorizationCode,saleOrgId,storeMemberId,pageable);


		String jsonPage = JsonUtils.toJson(result);
		return success(jsonPage);
	}
	
	protected String splicer(String[] str){
		String s = "";
		if(str!=null&&str.length>0){
			for(int i=0;i<str.length;i++){
				if(str.length-1==i){
					s += str[i];
				}else{
					s += str[i]+",";
				}
			}			
		}
		return s;
	}
	
	/**
	 *条件导出
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport (String shopName,//店名
																	  String distributorName,//经销商姓名
																	  String authorizationCode,
																	  Long[] saleOrgId,//机构
																	  Long[] storeMemberId,//区域经理
																	  // String shopStatus,
																	  // String transitTime,
																	  Pageable pageable,
																	  ModelMap model) {

		//int size = page.getContent().size();
		Page<Map<String, Object>> page = showShopProductService.countReport(distributorName,pageable);
		List<Map<String, Object>> content = page.getContent();
		Long size = Long.valueOf(content.get(0).get("size").toString());

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}


	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String shopName,//店名
										String distributorName,//经销商姓名
										String authorizationCode,
										Long[] saleOrgId,//机构
										Long[] storeMemberId,//区域经理
										// String shopStatus,
										// String transitTime,
										Pageable pageable,
										ModelMap model) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");

		Page<Map<String, Object>> page = showShopProductService.findShopInfoList(shopName, distributorName, authorizationCode,saleOrgId,storeMemberId,  pageable);

		return getModelAndViewForList(page.getContent(), model);
	}


	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {

		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		//设置标题//改
		String[] header = {"参与门店",
				"所属经销商",
				"省长",
				"区域经理",
				//"所属省份-城市-区/县",
				"机构",
				"地址",
				//"城市",
				//"区/县",
				"全国必备",
				"常规上样",
				"定制上样",
				};
		//设置单元格取值
		String[] properties = { "shopName",
				"distributorName",
				"provinceName",
				"areaManagerName",
				"salesPlatform",
				"address",
				//"street",
				"shopProductCategory_1",
				"shopProductCategory_2",
				"shopProductCategory_3",
				};
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties,
				header, widths, null, data, null), model);

	}
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	
	@ResponseBody
	@RequestMapping(value = "/select_sale_org_data", method = RequestMethod.POST)
	public ResultMsg select_sale_org_data(String saleOrgName,Pageable pageable) {
		Page<Map<String, Object>> page = showSignInService.findSaleOrg(saleOrgName,pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping("/show")
	public String show(Long shopInfoId, Long shopProductCategoryId, Model model){
		ShopInfo shopInfo = shopInfoService.find(shopInfoId);
		model.addAttribute("shopInfo",shopInfo);
		model.addAttribute("shopProductCategoryId",shopProductCategoryId);
		model.addAttribute("shopInfoId",shopInfoId);

		return "/show/shop_product/show";
	}

	@RequestMapping("/show_data")
	@ResponseBody
	public ResultMsg showData (Long shopInfoId, Long shopProductCategoryId,Pageable pageable){

        Page<Map<String, Object>> page = showShopProductService.findShopProductList(shopInfoId, shopProductCategoryId, pageable);

        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);

    }


}
