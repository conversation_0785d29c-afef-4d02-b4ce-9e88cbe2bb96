package net.shopxx.appfrom.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
@Repository("showSignInDao")
public class ShowSignInDao extends DaoCenter{

	public Page<Map<String, Object>> findPage(String username,String name, Long[] saleOrgIds,String startFirstTime,
			String startLastTime, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
//		sql.append("SELECT si.id,sm.username,sm.name,sm.id as sm_id,t.sale_org,t.name AS sale_org_name,si.create_date AS sign_in_time,si.content,si.address,sia.url AS sign_in_picture"
//				+ " FROM xx_sign_in si"
//				+ " LEFT JOIN xx_store_member sm ON sm.id = si.store_member"
//				+ " LEFT JOIN xx_sign_in_attach sia ON sia.sign_in = si.id"
//				
//				+ " LEFT JOIN ("
//				+ "SELECT sm.username,smso.sale_org,t1.name FROM xx_sign_in si"
//				+ " LEFT JOIN xx_store_member sm ON sm.id = si.store_member"
//				+ " LEFT JOIN xx_store_member_sale_org smso ON smso.store_member = si.store_member"
//				+ " LEFT JOIN ("
//				+ "SELECT so.name,smso1.sale_org"
//				+ " FROM xx_store_member_sale_org smso1"
//				+ " LEFT JOIN xx_sale_org so ON so.id = smso1.sale_org)"
//				+ "t1 ON t1.sale_org = smso.sale_org)t ON t.username = sm.username"
//				+ " WHERE 1 = 1");
		sql.append("SELECT si.id,sm.username,sm.`name`,sm.id sm_id,t.id sale_org,"
				+ "t.`name` sale_org_name,t.id,si.create_date sign_in_time,"
				+ "si.content,si.address,sia.url sign_in_picture FROM xx_sign_in si "
				+ " LEFT JOIN xx_store_member sm ON sm.id = si.store_member "
				+ " LEFT JOIN xx_sign_in_attach sia ON sia.sign_in = si.id "
				+ " LEFT JOIN xx_store_member_sale_org smso ON smso.store_member = sm.id "
				+ " LEFT JOIN xx_sale_org t on t.id = smso.sale_org WHERE 1=1 "
		+ " AND smso.sale_org in(SELECT id FROM xx_sale_org WHERE company_info_id = ? AND parent = 364 )");
		list.add(companyInfoId);
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + name + "%");
		}
		if (saleOrgIds != null && saleOrgIds.length > 0) {
			String p = "";
			for (int i = 0; i < saleOrgIds.length; i++) {
				if (i == saleOrgIds.length - 1)
					p += saleOrgIds[i];
				else
					p += saleOrgIds[i] + ",";
			}
			sql.append(" and t.id in (" + p + ")");
		}
		if (!ConvertUtil.isEmpty(startFirstTime)) {
			sql.append(" and si.create_date >= ? ");
			list.add(DateUtil.convert(startFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(startLastTime)) {
			sql.append(" and si.create_date <= ? ");
			list.add(DateUtil.convert(startLastTime + " 23:59:59"));
		}
		sql.append(" GROUP BY si.id");
		sql.append(" ORDER BY si.create_date DESC");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public Page<Map<String, Object>> countReport(String username,String name, Long[] saleOrgIds,String startFirstTime,
			String startLastTime, Pageable pageable, Integer page,
			Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT si.id,sm.username,sm.`name`,sm.id sm_id,t.id sale_org,"
				+ "t.`name` sale_org_name,si.create_date sign_in_time,"
				+ "si.content,si.address,sia.url sign_in_picture FROM xx_sign_in si "
				+ " LEFT JOIN xx_store_member sm ON sm.id = si.store_member "
				+ " LEFT JOIN xx_sign_in_attach sia ON sia.sign_in = si.id "
				+ " LEFT JOIN xx_store_member_sale_org smso ON smso.store_member = sm.id "
				+ " LEFT JOIN xx_sale_org t on t.id = smso.sale_org WHERE 1=1 "
		+ " AND smso.sale_org in(SELECT id FROM xx_sale_org WHERE company_info_id = ? AND parent = 364 )");
		list.add(companyInfoId);
		
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + name + "%");
		}
		if (saleOrgIds != null && saleOrgIds.length > 0) {
			String p = "";
			for (int i = 0; i < saleOrgIds.length; i++) {
				if (i == saleOrgIds.length - 1)
					p += saleOrgIds[i];
				else
					p += saleOrgIds[i] + ",";
			}
			sql.append(" and t.id in (" + p + ")");
		}
		if (!ConvertUtil.isEmpty(startFirstTime)) {
			sql.append(" and si.create_date >= ? ");
			list.add(DateUtil.convert(startFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(startLastTime)) {
			sql.append(" and si.create_date <= ? ");
			list.add(DateUtil.convert(startLastTime + " 23:59:59"));
		}
		sql.append(" GROUP BY si.id");
		sql.append(" ORDER BY si.create_date");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public List<Map<String, Object>> findReportList(String username,String name, Long[] saleOrgIds,String startFirstTime,
			String startLastTime, Pageable pageable, Integer page,
			Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT si.id,sm.username,sm.`name`,sm.id sm_id,t.id sale_org,"
				+ "t.`name` sale_org_name,si.create_date sign_in_time,"
				+ "si.content,si.address,sia.url sign_in_picture FROM xx_sign_in si "
				+ " LEFT JOIN xx_store_member sm ON sm.id = si.store_member "
				+ " LEFT JOIN xx_sign_in_attach sia ON sia.sign_in = si.id "
				+ " LEFT JOIN xx_store_member_sale_org smso ON smso.store_member = sm.id "
				+ " LEFT JOIN xx_sale_org t on t.id = smso.sale_org WHERE 1=1 "
		+ " AND smso.sale_org in(SELECT id FROM xx_sale_org WHERE company_info_id = ? AND parent = 364 )");
		list.add(companyInfoId);

		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + name + "%");
		}
		if (saleOrgIds != null && saleOrgIds.length > 0) {
			String p = "";
			for (int i = 0; i < saleOrgIds.length; i++) {
				if (i == saleOrgIds.length - 1)
					p += saleOrgIds[i];
				else
					p += saleOrgIds[i] + ",";
			}
			sql.append(" and t.id in (" + p + ")");
		}
		if (!ConvertUtil.isEmpty(startFirstTime)) {
			sql.append(" and si.create_date >= ? ");
			list.add(DateUtil.convert(startFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(startLastTime)) {
			sql.append(" and si.create_date <= ? ");
			list.add(DateUtil.convert(startLastTime + " 23:59:59"));
		}
		sql.append(" GROUP BY si.id");
		sql.append(" ORDER BY si.create_date");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),objs,0);

		return maps;
	}
	
	
	public List<Map<String,Object>> findpostName(Long storeMemberId, String saleOrgId1) {
		StringBuilder sql = new StringBuilder();
		sql.append("select p.name from xx_store_member_sale_org_post s, xx_post p");
		sql.append(" where p.id = s.post and s.store_member=" + storeMemberId);
		if (!ConvertUtil.isEmpty(saleOrgId1)) {
			sql.append(" and s.sale_org in (" + saleOrgId1 + ")");
		}
		return getNativeDao().findListMap(sql.toString(), null, 0);
		
	}

	public Page<Map<String, Object>> findSaleOrg(String saleOrgName,Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECt * from xx_sale_org where parent = 364");
		if (!ConvertUtil.isEmpty(saleOrgName)) {
			sql.append(" and name = ?");
			list.add(saleOrgName);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);	
	}

}
