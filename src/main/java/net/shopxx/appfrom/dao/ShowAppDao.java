package net.shopxx.appfrom.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;

@Repository("showAppDao")
public class ShowAppDao extends DaoCenter {

	/*
	public Page<Map<String, Object>> findPage(String subject, String areaName, String areaManager, String startDate, String endDate,
			Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ta.id AS ticket_action_id, ta.subject, ta.begin_time, ta.end_time,"
				+ " ta2.subject AS main_subject,so.name as area_name, sm2.name as area_manager, count(DISTINCT sm.id) AS boss_count,"
				+ "count(DISTINCT sta.ticket_store) AS store_count,count(DISTINCT sm1.id) AS waiter_count,"
				+ "(SELECT count(v.type = 0 OR NULL) FROM visitor v WHERE v.ticket_action = ta.id AND v.ticket_store = ts.id) AS read_count,"
				+ "(SELECT count(v.type = 0 OR NULL) FROM visitor v WHERE v.ticket_action = ta.id AND v.ticket_store = ts.id) AS vistor_count,"
				+ "count(t.payment_status = 1 OR NULL) AS sale, count(t.ticket_status = 3 OR NULL) AS deal,"
				+ "count((t.payment_status = 1 AND t.share_man != t.waiter) OR NULL) AS sale_plus,"
				+ "count((t.share_man != t.waiter AND t.ticket_status = 3) OR NULL) AS deal_plus,"
				+ "count(t.ticket_status = 99 OR NULL) AS cancel"
				+ " FROM ticket_action ta"
				+ " LEFT JOIN store_ticket_action sta ON sta.ticket_action = ta.id"
				+ " LEFT JOIN ticket_store ts ON ts.id = sta.ticket_store"
				+ " LEFT JOIN ticket_store_member tsm ON tsm.ticket_store = ts.id"
				+ " LEFT JOIN xx_store_member sm ON sm.id = ts.boss"
				+ " LEFT JOIN xx_store_member sm1 ON sm1.id = tsm.store_member"
				+ " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id"
				+ " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.ticket_store = ts.id"
				+ " LEFT JOIN ticket_action_ticket_actions tas ON tas.ticket_actions = ta.id"
				+ " LEFT JOIN ticket_action ta2 ON ta2.id = tas.ticket_action"
				+ " LEFT JOIN xx_sale_org so on so.id = ts.sale_org"
				+ " LEFT JOIN xx_store_member sm2 on sm.id = ts.store_member"
				+ " WHERE 1 = 1");
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.subject = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and so.name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"),
					1));
		}
		sql.append(" GROUP BY ta.id");
		sql.append(" ORDER BY ta.begin_time");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}*/
	public Page<Map<String, Object>> findPage(String subject, String areaName, String areaManager, String startDate, String endDate,
			Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM ticket_action_data ta WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.`subject` = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and ta.area_name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"),
					1));
		}
		sql.append(" GROUP BY ta.id");
		sql.append(" ORDER BY ta.begin_time DESC");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public Page<Map<String, Object>> countReport(String subject, String areaName, String areaManager,String startDate, 
			String endDate, Pageable pageable, Integer page, Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ta.id AS ticket_action_id, ta.subject, ta.begin_time, ta.end_time,"
				+ " ta2.subject AS main_subject,so.name as area_name, sm2.name as area_manager, count(DISTINCT sm.id) AS boss_count,"
				+ "count(DISTINCT sta.ticket_store) AS store_count,count(DISTINCT sm1.id) AS waiter_count,"
				+ "(SELECT count(v.type = 0 OR NULL) FROM visitor v WHERE v.ticket_action = ta.id AND v.ticket_store = ts.id) AS read_count,"
				+ "(SELECT count(v.type = 0 OR NULL) FROM visitor v WHERE v.ticket_action = ta.id AND v.ticket_store = ts.id) AS vistor_count,"
				+ "count(t.payment_status = 1 OR NULL) AS sale, count(t.ticket_status = 3 OR NULL) AS deal,"
				+ "count((t.payment_status = 1 AND t.share_man != t.waiter) OR NULL) AS sale_plus,"
				+ "count((t.share_man != t.waiter AND t.ticket_status = 3) OR NULL) AS deal_plus,"
				+ "count(t.ticket_status = 99 OR NULL) AS cancel"
				+ " FROM ticket_action ta"
				+ " LEFT JOIN store_ticket_action sta ON sta.ticket_action = ta.id"
				+ " LEFT JOIN ticket_store ts ON ts.id = sta.ticket_store"
				+ " LEFT JOIN ticket_store_member tsm ON tsm.ticket_store = ts.id"
				+ " LEFT JOIN xx_store_member sm ON sm.id = ts.boss"
				+ " LEFT JOIN xx_store_member sm1 ON sm1.id = tsm.store_member"
				+ " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id"
				+ " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.ticket_store = ts.id"
				+ " LEFT JOIN ticket_action_ticket_actions tas ON tas.ticket_actions = ta.id"
				+ " LEFT JOIN ticket_action ta2 ON ta2.id = tas.ticket_action"
				+ " LEFT JOIN xx_sale_org so on so.id = ts.sale_org"
				+ " LEFT JOIN xx_store_member sm2 on sm.id = ts.store_member"
				+ " WHERE 1 = 1");
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.subject = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and so.name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"),
					1));
		}
		sql.append(" GROUP BY ta.id");
		sql.append(" ORDER BY ta.begin_time");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public List<Map<String, Object>> findReportList(String subject, String areaName, String areaManager, String startDate,
			String endDate, Pageable pageable, Integer page, Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ta.id AS ticket_action_id, ta.subject, ta.begin_time, ta.end_time,"
				+ " ta2.subject AS main_subject,so.name as area_name, sm2.name as area_manager, count(DISTINCT sm.id) AS boss_count,"
				+ "count(DISTINCT sta.ticket_store) AS store_count,count(DISTINCT sm1.id) AS waiter_count,"
				+ "(SELECT count(v.type = 0 OR NULL) FROM visitor v WHERE v.ticket_action = ta.id AND v.ticket_store = ts.id) AS read_count,"
				+ "(SELECT count(v.type = 0 OR NULL) FROM visitor v WHERE v.ticket_action = ta.id AND v.ticket_store = ts.id) AS vistor_count,"
				+ "count(t.payment_status = 1 OR NULL) AS sale, count(t.ticket_status = 3 OR NULL) AS deal,"
				+ "count((t.payment_status = 1 AND t.share_man != t.waiter) OR NULL) AS sale_plus,"
				+ "count((t.share_man != t.waiter AND t.ticket_status = 3) OR NULL) AS deal_plus,"
				+ "count(t.ticket_status = 99 OR NULL) AS cancel"
				+ " FROM ticket_action ta"
				+ " LEFT JOIN store_ticket_action sta ON sta.ticket_action = ta.id"
				+ " LEFT JOIN ticket_store ts ON ts.id = sta.ticket_store"
				+ " LEFT JOIN ticket_store_member tsm ON tsm.ticket_store = ts.id"
				+ " LEFT JOIN xx_store_member sm ON sm.id = ts.boss"
				+ " LEFT JOIN xx_store_member sm1 ON sm1.id = tsm.store_member"
				+ " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id"
				+ " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.ticket_store = ts.id"
				+ " LEFT JOIN ticket_action_ticket_actions tas ON tas.ticket_actions = ta.id"
				+ " LEFT JOIN ticket_action ta2 ON ta2.id = tas.ticket_action"
				+ " LEFT JOIN xx_sale_org so on so.id = ts.sale_org"
				+ " LEFT JOIN xx_store_member sm2 on sm.id = ts.store_member"
				+ " WHERE 1 = 1");
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.subject = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and so.name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"),
					1));
		}
		sql.append(" GROUP BY ta.id");
		sql.append(" ORDER BY ta.begin_time");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),objs,0);

		return maps;
	}
}
