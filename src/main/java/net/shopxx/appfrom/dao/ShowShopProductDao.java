package net.shopxx.appfrom.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.service.StoreMemberBaseService;
import org.springframework.stereotype.Repository;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Repository("showShopProductDao")
public class ShowShopProductDao extends DaoCenter {
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;


    public Page<Map<String, Object>> countReport(String distributorName,
                                                 Pageable pageable) {

        StringBuilder sql = new StringBuilder();
        LinkedList<Object> params = new LinkedList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

        sql.append(" select count(1) as size from xx_shop_info si ");
        sql.append(" where 1 = 1 ");

        if (!ConvertUtil.isEmpty(distributorName)) {
            sql.append(" and si.distributorName like ?");
            params.add("%" + distributorName + "%");
        }
        if (companyInfoId != null) {
            sql.append(" and si.company_info_id = ?");
            params.add(companyInfoId);
        }

        return getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);
    }

    public Page<Map<String, Object>> findPage(String shopName,//店名
                                              String distributorName,//经销商姓名
                                              String authorizationCode,
                                              Long[] saleOrgIds,//机构
                                              Long[] storeMemberIds,//区域经理
                                              // String address,
                                              //String[] shopStatus,
                                              //Integer reimburse,
                                              //String transitTime,
                                              Pageable pageable) {

        StringBuilder sql = new StringBuilder();
        LinkedList<Object> params = new LinkedList<Object>();
        sql.append("SELECT shopInfo.id,shopInfo.shop_name shopName, shopInfo.distributor_name distributorName,"
                + "storeMember.`name` areaManagerName,IFNULL(shopInfo.sale_org ,- 1) saleOrgId,"
                + "shopInfo.sales_platform salesPlatform,shopInfo.address address ");
        sql.append("FROM xx_shop_info AS shopInfo ");
        sql.append(" LEFT JOIN xx_store AS store ON store.id = shopInfo.store ");
        sql.append(" LEFT JOIN xx_store_member AS storeMember ON storeMember.id = store.store_member ");
        sql.append(" WHERE 1 = 1");

        sql.append(" AND shopInfo.company_info_id = ? ");
        params.add(WebUtils.getCurrentCompanyInfoId());

        if (!ConvertUtil.isEmpty(shopName)) {
            sql.append(" AND shopInfo.shop_name like ? ");
            params.add("%" + shopName + "%");
        }

        if (!ConvertUtil.isEmpty(distributorName)) {
            sql.append(" AND shopInfo.distributor_name like ? ");
            params.add("%" + distributorName + "%");
        }

        if (!ConvertUtil.isEmpty(authorizationCode)) {
            sql.append(" AND shopInfo.authorization_code like ? ");
            params.add("%" + authorizationCode + "%");
        }

        if (saleOrgIds != null && saleOrgIds.length > 0) {
            sql.append(" AND shopInfo.sale_org IN  (");
            for (int i = 0; i < saleOrgIds.length; i++) {
                sql.append(" ? ");
                if (i < saleOrgIds.length - 1) {
                    sql.append(",");
                }
                params.add(saleOrgIds[i]);
            }
            sql.append(")");

        }
        if (storeMemberIds != null && storeMemberIds.length > 0) {
            sql.append(" AND storeMember.id IN ( ");
            for (int i = 0; i < storeMemberIds.length; i++) {
                sql.append(" ? ");
                if (i < storeMemberIds.length - 1) {
                    sql.append(",");
                }
                params.add(storeMemberIds[i]);

            }
            sql.append(")");
        }


        return getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);
    }


    /**
     * 查询门店最新上样时间
     *
     * @param shopName
     * @param distributorName
     * @param authorizationCode
     * @param saleOrgIds
     * @param storeMemberIds
     * @param pageable
     * @return
     */

    public Page<Map<String, Object>> findShopProductLastTime(String shopName,//店名
                                                             String distributorName,//经销商姓名
                                                             String authorizationCode,
                                                             Long[] saleOrgIds,//机构
                                                             Long[] storeMemberIds,//区域经理
                                                             // String address,
                                                             //String[] shopStatus,
                                                             //Integer reimburse,
                                                             //String transitTime,
                                                             Pageable pageable) {

        StringBuilder sql = new StringBuilder();
        LinkedList<Object> params = new LinkedList<Object>();
        sql.append("SELECT MAX(shopProduct.marketable_time) lastTime,shopProduct.shop_info shopInfoId ");
        sql.append("FROM xx_shop_info AS shopInfo ");
        sql.append(" LEFT JOIN xx_store AS store ON store.id = shopInfo.store ");
        sql.append(" LEFT JOIN xx_store_member AS storeMember ON storeMember.id = store.store_member ");
        sql.append(" LEFT JOIN xx_shop_product AS shopProduct ON shopProduct.shop_info = shopInfo.id ");
        sql.append(" WHERE 1 = 1");

        sql.append(" AND shopInfo.company_info_id = ? ");
        params.add(WebUtils.getCurrentCompanyInfoId());

        if (!ConvertUtil.isEmpty(shopName)) {
            sql.append(" AND shopInfo.shop_name like ? ");
            params.add("%" + shopName + "%");
        }

        if (!ConvertUtil.isEmpty(distributorName)) {
            sql.append(" AND shopInfo.distributor_name like ? ");
            params.add("%" + distributorName + "%");
        }

        if (!ConvertUtil.isEmpty(authorizationCode)) {
            sql.append(" AND shopInfo.authorization_code like ? ");
            params.add("%" + authorizationCode + "%");
        }

        if (saleOrgIds != null && saleOrgIds.length > 0) {
            sql.append(" AND shopInfo.sale_org IN  (");
            for (int i = 0; i < saleOrgIds.length; i++) {
                sql.append(" ? ");
                if (i < saleOrgIds.length - 1) {
                    sql.append(",");
                }
                params.add(saleOrgIds[i]);
            }
            sql.append(")");

        }
        if (storeMemberIds != null && storeMemberIds.length > 0) {
            sql.append(" AND storeMember.id IN ( ");
            for (int i = 0; i < storeMemberIds.length; i++) {
                sql.append(" ? ");
                if (i < storeMemberIds.length - 1) {
                    sql.append(",");
                }
                params.add(storeMemberIds[i]);

            }
            sql.append(")");
        }

        sql.append(" GROUP BY shopInfoId ");


        return getNativeDao().findPageMap(sql.toString(), params.toArray(), pageable);
    }


    /**
     * 根据shopInfoId查询门店上样分类情况
     *
     * @param shopIds
     * @return
     */
    public List<Map<String, Object>> findListCount(Long[] shopIds) {
        List<Object> params = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT shopInfo.id shopInfo, IFNULL(count(if(product.shop_product_states = 1,true,NULL)),0) shopProductCategory_1,");
        sql.append("IFNULL(count(if(product.shop_product_states = 2,true,NULL)),0) shopProductCategory_2,");
        sql.append("IFNULL(count(if(product.shop_product_states = 3,true,NULL)),0) shopProductCategory_3 ");
        sql.append(" FROM xx_shop_info shopInfo ");
        sql.append(" LEFT JOIN xx_shop_product sp ON shopInfo.id = sp.shop_info ");
        sql.append(" LEFT JOIN xx_product product ON sp.product = product.id ");
        sql.append(" WHERE 1 = 1 ");

        if (shopIds != null && shopIds.length > 0) {


            sql.append(" AND shopInfo.id in(");
            for (int i = 0; i < shopIds.length; i++) {
                sql.append(" ? ");
                if (i < shopIds.length - 1) {
                    sql.append(",");
                }
                params.add(shopIds[i]);
            }
            sql.append(") ");
        }

        //已上样
        sql.append(" AND sp.is_marketable = TRUE ");

        sql.append(" AND shopInfo.company_info_id = ? ");
        params.add(companyInfoId);
        sql.append(" GROUP BY shopInfo.id");

        LogUtils.debug("======="+sql);
        LogUtils.debug("========="+params.toString());
        return getNativeDao().findListMap(sql.toString(), params.toArray(), 0);
    }


    /**
     * 查询某门店某门店上样分类的已上样商品
     * @param shopId
     * @param shopProductCategoryId
     * @param pageable
     * @return
     */
    public Page<Map<String, Object>> findShopProduct(Long shopId, Long shopProductCategoryId, Pageable pageable) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        String sql = "select p.id,p.`name` product_name,pc.`name` product_category_name,p.vonder_code,p.model,p.description,"
                + " sp.shop_info,sp.is_marketable,sp.is_show,sp.is_referrer,sp.modify_date,p.spec,p.shop_product_states"
                + " from xx_product p "
                + " left join xx_product_category pc on p.product_category = pc.id "
                + " left join xx_shop_product sp on sp.product = p.id "
                + " where 1=1 ";

        if (shopId != null) {
            sql += " and sp.shop_info =" + shopId;

        }
        if(shopProductCategoryId != null){
            sql += " AND p.shop_product_states =" + shopProductCategoryId;
        }else {
            ExceptionUtil.throwDaoException("门店上样分类shopProductCategoryId不能为空");
        }
        if (companyInfoId != null) {
            sql += " and p.company_info_id = " + companyInfoId;
        }

        //已上样
        sql += " AND sp.is_marketable = TRUE ";

        sql += " group by p.id";

        return getNativeDao().findPageMap(sql,null,pageable);
    }

}
