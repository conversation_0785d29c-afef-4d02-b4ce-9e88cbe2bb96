package net.shopxx.appfrom.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;

@Repository("showGuideDao")
public class ShowGuideDao extends DaoCenter {

	/*
	 * public Page<Map<String, Object>> findPage(String name, String storeName,
	 * String boss, String subject, String startDate, String endDate, String
	 * areaName, String areaManager, Pageable pageable) { List<Object> list =
	 * new ArrayList<Object>(); StringBuilder sql = new StringBuilder();
	 * sql.append(
	 * "SELECT sm.NAME AS name, ts.NAME AS store_name, sm1.NAME AS boss, ta.subject, ta.begin_time, ta.end_time,"
	 * + "so.name AS area_name, sm2.name AS area_manager," +
	 * "(SELECT count( v.type = 0 OR NULL ) FROM	visitor v WHERE v.ticket_action = ta.id AND v.waiter = sm.id ) AS read_count,"
	 * +
	 * "(SELECT count( v.type = 0 OR NULL ) FROM visitor v WHERE v.ticket_action = ta.id AND v.waiter = sm.id ) AS vistor_count,"
	 * + "count( t.payment_status = 1 or null) AS sale ," +
	 * "count(t.ticket_status = 3 or null) AS deal," +
	 * "count((t.payment_status = 1 and t.share_man!=t.waiter) or null) as sale_plus,"
	 * +
	 * "count((t.share_man!=t.waiter and t.ticket_status = 3) or null) as deal_plus,"
	 * + "count(t.ticket_status=99 or null) as cancel" +
	 * " FROM ticket_action ta" +
	 * " LEFT JOIN store_ticket_action sta ON sta.ticket_action = ta.id" +
	 * " LEFT JOIN ticket_store ts ON ts.id = sta.ticket_store right JOIN ticket_store_member tsm ON tsm.ticket_store = ts.id"
	 * + " LEFT JOIN xx_store_member sm ON sm.id = tsm.store_member" +
	 * " LEFT JOIN xx_store_member sm1 ON sm1.id = ts.boss" +
	 * " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id" +
	 * " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.waiter = sm.id"
	 * + " LEFT JOIN xx_sale_org so on so.id = ts.sale_org" +
	 * " LEFT JOIN xx_store_member sm2 on sm.id = ts.store_member" +
	 * " WHERE 1 = 1"); if (!ConvertUtil.isEmpty(name)) { sql.append(
	 * " and sm.NAME = ?"); list.add(name); } if
	 * (!ConvertUtil.isEmpty(storeName)) { sql.append(" and ts.NAME = ?");
	 * list.add(storeName); } if (!ConvertUtil.isEmpty(boss)) { sql.append(
	 * " and sm1.NAME = ?"); list.add(boss); } if
	 * (!ConvertUtil.isEmpty(subject)) { sql.append(" and ta.subject = ?");
	 * list.add(subject); } if (!ConvertUtil.isEmpty(areaName)) { sql.append(
	 * " and so.name = ?"); list.add(areaName); } if
	 * (!ConvertUtil.isEmpty(areaManager)) { sql.append(" and sm2.name = ?");
	 * list.add(areaManager); } if (!ConvertUtil.isEmpty(startDate)) {
	 * sql.append(" and ta.begin_time >= ?");
	 * list.add(DateUtil.convert(startDate + " 00:00:00")); } if
	 * (!ConvertUtil.isEmpty(endDate)) { sql.append(" and ta.end_time < ?");
	 * list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"), 1));
	 * } sql.append(" GROUP BY sm.id"); sql.append(" ORDER BY ta.begin_time");
	 * Object[] objs = new Object[list.size()]; for (int i = 0; i < list.size();
	 * i++) { objs[i] = list.get(i); } return
	 * getNativeDao().findPageMap(sql.toString(), objs, pageable); }
	 */
	public Page<Map<String, Object>> findPage(String name, String storeName, String boss, String subject,
			String startDate, String endDate, String areaName, String areaManager, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT wd.*,s.dealer_name,sm.name manager_name FROM waiter_data wd "
				+ " LEFT JOIN ticket_store ts on ts.id = wd.ticket_store_id "
				+ " LEFT JOIN xx_shop_info si on si.id = ts.shop_info "
				+ " LEFT JOIN xx_store s on s.id = si.store "
				+ " LEFT JOIN xx_store_member sm on sm.id = s.store_member "
				+ " WHERE 1=1");
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" AND wd.`name` LIKE ? ");
			list.add("%"+name+"%");
		}
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" AND wd.store_name LIKE ? ");
			list.add("%"+storeName+"%");
		}
		if (!ConvertUtil.isEmpty(boss)) {
			sql.append(" and s.dealer_name LIKE ? ");
			list.add("%"+boss+"%");
		}
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" AND wd.`subject` LIKE ? ");
			list.add("%"+subject+"%");
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" AND wd.area_name LIKE ? ");
			list.add("%"+areaName+"%");
		}
		if (!ConvertUtil.isEmpty(areaManager)) {
			sql.append(" and sm.name LIKE ?");
			list.add("%"+areaManager+"%");
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and wd.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and wd.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"), 1));
		}
		sql.append(" GROUP BY wd.id");
		sql.append(" ORDER BY wd.begin_time DESC");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public Page<Map<String, Object>> countReport(String name, String storeName, String boss, String subject,
			String startDate, String endDate, String areaName, String areaManager, Pageable pageable, Integer page,
			Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(
				"SELECT sm.NAME AS name, ts.NAME AS store_name, sm1.NAME AS boss, ta.`subject`, ta.begin_time, ta.end_time,"
						+ "so.name AS area_name, sm2.name AS area_manager,"
						+ "(SELECT count( v.type = 0 OR NULL ) FROM	visitor v WHERE v.ticket_action = ta.id AND v.waiter = sm.id ) AS read_count,"
						+ "(SELECT count( v.type = 0 OR NULL ) FROM visitor v WHERE v.ticket_action = ta.id AND v.waiter = sm.id ) AS vistor_count,"
						+ "count( t.payment_status = 1 or null) AS sale ,"
						+ "count(t.ticket_status = 3 or null) AS deal,"
						+ "count((t.payment_status = 1 and t.share_man!=t.waiter) or null) as sale_plus,"
						+ "count((t.share_man!=t.waiter and t.ticket_status = 3) or null) as deal_plus,"
						+ "count(t.ticket_status=99 or null) as cancel" + " FROM ticket_action ta"
						+ " LEFT JOIN store_ticket_action sta ON sta.ticket_action = ta.id"
						+ " LEFT JOIN ticket_store ts ON ts.id = sta.ticket_store right JOIN ticket_store_member tsm ON tsm.ticket_store = ts.id"
						+ " LEFT JOIN xx_store_member sm ON sm.id = tsm.store_member"
						+ " LEFT JOIN xx_store_member sm1 ON sm1.id = ts.boss"
						+ " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id"
						+ " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.waiter = sm.id"
						+ " LEFT JOIN xx_sale_org so on so.id = ts.sale_org"
						+ " LEFT JOIN xx_store_member sm2 on sm.id = ts.store_member" + " WHERE 1 = 1");
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.NAME = ?");
			list.add(name);
		}
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" and ts.NAME = ?");
			list.add(storeName);
		}
		if (!ConvertUtil.isEmpty(boss)) {
			sql.append(" and sm1.NAME = ?");
			list.add(boss);
		}
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.subject = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and so.name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(areaManager)) {
			sql.append(" and sm2.name = ?");
			list.add(areaManager);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"), 1));
		}
		sql.append(" GROUP BY sm.id");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public List<Map<String, Object>> findReportList(String name, String storeName, String boss, String subject,
			String startDate, String endDate, String areaName, String areaManager, Pageable pageable, Integer page,
			Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(
				"SELECT sm.NAME AS name, ts.NAME AS store_name, sm1.NAME AS boss, ta.`subject`, ta.begin_time, ta.end_time,"
						+ "so.name AS area_name, sm2.name AS area_manager,"
						+ "(SELECT count( v.type = 0 OR NULL ) FROM	visitor v WHERE v.ticket_action = ta.id AND v.waiter = sm.id ) AS read_count,"
						+ "(SELECT count( v.type = 0 OR NULL ) FROM visitor v WHERE v.ticket_action = ta.id AND v.waiter = sm.id ) AS vistor_count,"
						+ "count( t.payment_status = 1 or null) AS sale ,"
						+ "count(t.ticket_status = 3 or null) AS deal,"
						+ "count((t.payment_status = 1 and t.share_man!=t.waiter) or null) as sale_plus,"
						+ "count((t.share_man!=t.waiter and t.ticket_status = 3) or null) as deal_plus,"
						+ "count(t.ticket_status=99 or null) as cancel" + " FROM ticket_action ta"
						+ " LEFT JOIN store_ticket_action sta ON sta.ticket_action = ta.id"
						+ " LEFT JOIN ticket_store ts ON ts.id = sta.ticket_store right JOIN ticket_store_member tsm ON tsm.ticket_store = ts.id"
						+ " LEFT JOIN xx_store_member sm ON sm.id = tsm.store_member"
						+ " LEFT JOIN xx_store_member sm1 ON sm1.id = ts.boss"
						+ " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id"
						+ " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.waiter = sm.id"
						+ " LEFT JOIN xx_sale_org so on so.id = ts.sale_org"
						+ " LEFT JOIN xx_store_member sm2 on sm.id = ts.store_member" + " WHERE 1 = 1");
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.NAME = ?");
			list.add(name);
		}
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" and ts.NAME = ?");
			list.add(storeName);
		}
		if (!ConvertUtil.isEmpty(boss)) {
			sql.append(" and sm1.NAME = ?");
			list.add(boss);
		}
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.subject = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and so.name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(areaManager)) {
			sql.append(" and sm2.name = ?");
			list.add(areaManager);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"), 1));
		}
		sql.append(" GROUP BY sm.id");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);

		return maps;
	}

}
