package net.shopxx.appfrom.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

@Repository("showStoreDao")
public class ShowStoreDao extends DaoCenter{
	
	/*
	public Page<Map<String, Object>> findPage(String storeName, String boss, String subject, String startDate, String endDate,
			 String areaName, String areaManager, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ts.id AS ticket_store_id,ts.NAME AS store_name,sm.name as boss,ta.subject,"
				+ "ta.begin_time,ta.end_time,so.name as area_name,sm1.name as area_manager,ta2.subject as main_subject,"
				+ "(select count( v.type = 0 OR NULL ) from visitor v where v.ticket_action = ta.id and v.ticket_store = ts.id) AS read_count,"
				+ "(select count( v.type = 0 OR null ) from visitor v where v.ticket_action = ta.id and v.ticket_store = ts.id) AS vistor_count,"
				+ "count( t.payment_status = 1 ) AS sale ,"
				+ "count(t.ticket_status = 3 or null ) AS deal,"
				+ "count((t.payment_status = 1 and t.share_man!=t.waiter) or null) as sale_plus,"
				+ "count((t.share_man!=t.waiter and t.ticket_status = 3) or null) as deal_plus,"
				+ "count(t.ticket_status=99) as cancel"
				+ " FROM ticket_store ts"
				+ " LEFT JOIN store_ticket_action sta ON ts.id = sta.ticket_store"
				+ " INNER JOIN ticket_action ta ON sta.ticket_action = ta.id"
				+ " left join xx_store_member sm on sm.id = ts.boss "
				+ " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id"
				+ " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.ticket_store = ts.id"
				+ " left join ticket_action_ticket_actions tas on tas.ticket_actions = ta.id"
				+ " left join ticket_action ta2 on ta2.id = tas.ticket_action"
				+ " LEFT join xx_sale_org so on so.id = ts.sale_org"
				+ " LEFT join xx_store_member sm1 on sm.id = ts.store_member"
				+ " WHERE 1 = 1 ");	
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" and ts.NAME = ?");
			list.add(storeName);
		}
		if (!ConvertUtil.isEmpty(boss)) {
			sql.append(" and sm.name = ?");
			list.add(boss);
		}
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.subject = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"),
					1));
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and so.name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(areaManager)) {
			sql.append(" and sm1.name = ?");
			list.add(areaManager);
		}
		sql.append(" GROUP BY ts.id");
		sql.append(" ORDER BY ta.begin_time");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}*/
	public Page<Map<String, Object>> findPage(String storeName, String boss, String subject, String startDate, String endDate,
			 String areaName, String areaManager, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT tsd.*,s.dealer_name,sm.name manager_name FROM ticket_store_data tsd " 
				+" LEFT JOIN ticket_store ts on ts.id = tsd.ticket_store_id "
				+" LEFT JOIN xx_shop_info si on si.id = ts.shop_info "
				+" LEFT JOIN xx_store s on s.id = si.store "
				+" LEFT JOIN xx_store_member sm on sm.id = s.store_member"
				+" WHERE 1=1 ");	
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" AND tsd.store_name LIKE ? ");
			list.add("%" + storeName + "%");
		}
		if (!ConvertUtil.isEmpty(boss)) {
			sql.append(" and s.dealer_name LIKE ?");
			list.add("%" + boss + "%");
		}
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and tsd.subject LIKE ?");
			list.add("%" + subject + "%");
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and tsd.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and tsd.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"),
					1));
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and tsd.area_name LIKE ?");
			list.add("%" +areaName+ "%");
		}
		if (!ConvertUtil.isEmpty(areaManager)) {
			sql.append(" and sm.name LIKE ?");
			list.add("%" +areaManager+ "%");
		}
		sql.append(" GROUP BY tsd.id");
		sql.append(" ORDER BY tsd.begin_time DESC");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}
	
	//条件导出
	public Page<Map<String, Object>> countReport(String storeName, String boss, String subject, String startDate,
			String endDate,  String areaName, String areaManager, Pageable pageable, Integer page, Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ts.id AS ticket_store_id,ts.NAME AS store_name,sm.name as boss,ta.subject,"
				+ "ta.begin_time,ta.end_time,so.name as area_name,sm1.name as area_manager,ta2.subject as main_subject,"
				+ "(select count( v.type = 0 OR NULL ) from visitor v where v.ticket_action = ta.id and v.ticket_store = ts.id) AS read_count,"
				+ "(select count( v.type = 0 OR null ) from visitor v where v.ticket_action = ta.id and v.ticket_store = ts.id) AS vistor_count,"
				+ "count( t.payment_status = 1 ) AS sale ,"
				+ "count(t.ticket_status = 3 or null ) AS deal,"
				+ "count((t.payment_status = 1 and t.share_man!=t.waiter) or null) as sale_plus,"
				+ "count((t.share_man!=t.waiter and t.ticket_status = 3) or null) as deal_plus,"
				+ "count(t.ticket_status=99) as cancel"
				+ " FROM ticket_store ts"
				+ " LEFT JOIN store_ticket_action sta ON ts.id = sta.ticket_store"
				+ " INNER JOIN ticket_action ta ON sta.ticket_action = ta.id"
				+ " left join xx_store_member sm on sm.id = ts.boss "
				+ " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id"
				+ " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.ticket_store = ts.id"
				+ " left join ticket_action_ticket_actions tas on tas.ticket_actions = ta.id"
				+ " left join ticket_action ta2 on ta2.id = tas.ticket_action"
				+ " LEFT join xx_sale_org so on so.id = ts.sale_org"
				+ " LEFT join xx_store_member sm1 on sm.id = ts.store_member"
				+ " WHERE 1 = 1 ");	
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" and ts.NAME = ?");
			list.add(storeName);
		}
		if (!ConvertUtil.isEmpty(boss)) {
			sql.append(" and sm.name = ?");
			list.add(boss);
		}
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.subject = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"),
					1));
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and so.name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(areaManager)) {
			sql.append(" and sm1.name = ?");
			list.add(areaManager);
		}
		sql.append(" GROUP BY ts.id");
		sql.append(" ORDER BY ta.begin_time");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public List<Map<String, Object>> findReportList(String storeName, String boss, String subject, String startDate,
			String endDate, String areaName, String areaManager, Pageable pageable, Integer page, Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ts.id AS ticket_store_id,ts.NAME AS store_name,sm.name as boss,ta.subject,"
				+ "ta.begin_time,ta.end_time,so.name as area_name,sm1.name as area_manager,ta2.subject as main_subject,"
				+ "(select count( v.type = 0 OR NULL ) from visitor v where v.ticket_action = ta.id and v.ticket_store = ts.id) AS read_count,"
				+ "(select count( v.type = 0 OR null ) from visitor v where v.ticket_action = ta.id and v.ticket_store = ts.id) AS vistor_count,"
				+ "count( t.payment_status = 1 ) AS sale ,"
				+ "count(t.ticket_status = 3 or null ) AS deal,"
				+ "count((t.payment_status = 1 and t.share_man!=t.waiter) or null) as sale_plus,"
				+ "count((t.share_man!=t.waiter and t.ticket_status = 3) or null) as deal_plus,"
				+ "count(t.ticket_status=99) as cancel"
				+ " FROM ticket_store ts"
				+ " LEFT JOIN store_ticket_action sta ON ts.id = sta.ticket_store"
				+ " INNER JOIN ticket_action ta ON sta.ticket_action = ta.id"
				+ " left join xx_store_member sm on sm.id = ts.boss "
				+ " LEFT JOIN ticket_order_item toi ON toi.ticket_action = ta.id"
				+ " LEFT JOIN ticket_order t ON t.id = toi.ticket_order AND t.ticket_store = ts.id"
				+ " left join ticket_action_ticket_actions tas on tas.ticket_actions = ta.id"
				+ " left join ticket_action ta2 on ta2.id = tas.ticket_action"
				+ " LEFT join xx_sale_org so on so.id = ts.sale_org"
				+ " LEFT join xx_store_member sm1 on sm.id = ts.store_member"
				+ " WHERE 1 = 1 ");	
		if (!ConvertUtil.isEmpty(storeName)) {
			sql.append(" and ts.NAME = ?");
			list.add(storeName);
		}
		if (!ConvertUtil.isEmpty(boss)) {
			sql.append(" and sm.name = ?");
			list.add(boss);
		}
		if (!ConvertUtil.isEmpty(subject)) {
			sql.append(" and ta.subject = ?");
			list.add(subject);
		}
		if (!ConvertUtil.isEmpty(startDate)) {
			sql.append(" and ta.begin_time >= ?");
			list.add(DateUtil.convert(startDate + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(endDate)) {
			sql.append(" and ta.end_time < ?");
			list.add(DateUtils.addDays(DateUtil.convert(endDate + " 00:00:00"),
					1));
		}
		if (!ConvertUtil.isEmpty(areaName)) {
			sql.append(" and so.name = ?");
			list.add(areaName);
		}
		if (!ConvertUtil.isEmpty(areaManager)) {
			sql.append(" and sm1.name = ?");
			list.add(areaManager);
		}
		sql.append(" GROUP BY ts.id");
		sql.append(" ORDER BY ta.begin_time");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),objs,0);
		return maps;
	}

}
