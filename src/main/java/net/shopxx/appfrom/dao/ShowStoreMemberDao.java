package net.shopxx.appfrom.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
@Repository("showStoreMemberDao")
public class ShowStoreMemberDao extends DaoCenter{

	public Page<Map<String, Object>> findPage(String name,Long[] saleOrgIds,
			String planFirstTime,String planListTime, String summaryFirstTime, String summaryListTime,
			Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT p.id AS plan_id,sm.username,sm.name AS name1 ,sm.id AS sm_id,p.create_date AS plan_time,"
				+ "p.summary_time,pi.content,pi.summary,pia.url AS work_picture,t.id AS sale_org,t.name AS sale_org_name"
				+ " FROM xx_plan p"
				+ " LEFT JOIN xx_store_member sm ON sm.id = p.store_member"
				+ " LEFT JOIN xx_plan_item pi ON pi.plan = p.id"
				+ " LEFT JOIN xx_plan_item_attach pia ON pia.plan_item = pi.id"
				+ " LEFT JOIN xx_store_member_sale_org smso ON smso.store_member = sm.id"
				+ " LEFT JOIN xx_sale_org t ON t.id = smso.sale_org"
				+ " WHERE	1 = 1"
				+ " AND smso.sale_org IN (SELECT id FROM xx_sale_org"
				+ " WHERE company_info_id = ? AND parent = 364)");
		list.add(companyInfoId);
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + name + "%");
		}
		if (saleOrgIds != null && saleOrgIds.length > 0) {
			String p = "";
			for (int i = 0; i < saleOrgIds.length; i++) {
				if (i == saleOrgIds.length - 1)
					p += saleOrgIds[i];
				else
					p += saleOrgIds[i] + ",";
			}
			sql.append(" and t.id in (" + p + ")");
		}
		if (!ConvertUtil.isEmpty(planFirstTime)) {
			sql.append(" and p.create_date >= ?");
			list.add(DateUtil.convert(planFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(planListTime)) {
			sql.append(" and p.create_date <= ?");
			list.add(DateUtil.convert(planListTime + " 23:59:59"));
		}
		if (!ConvertUtil.isEmpty(summaryFirstTime)) {
			sql.append(" and p.summary_time >= ?");
			list.add(DateUtil.convert(summaryFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(summaryListTime)) {
			sql.append(" and p.summary_time <= ?");
			list.add(DateUtil.convert(summaryListTime + " 23:59:59"));
		}
		sql.append(" GROUP BY p.id");
		sql.append(" ORDER BY p.create_date DESC");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public Page<Map<String, Object>> countReport(String name,Long[] saleOrgIds,
			String planFirstTime,String planListTime, String summaryFirstTime, String summaryListTime,
			Pageable pageable, Integer page, Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT p.id AS plan_id,sm.username,sm.name ,sm.id AS sm_id,p.create_date AS plan_time,"
				+ "p.summary_time,pi.content,pi.summary,pia.url AS work_picture,t.id AS sale_org,t.name AS sale_org_name"
				+ " FROM xx_plan p"
				+ " LEFT JOIN xx_store_member sm ON sm.id = p.store_member"
				+ " LEFT JOIN xx_plan_item pi ON pi.plan = p.id"
				+ " LEFT JOIN xx_plan_item_attach pia ON pia.plan_item = pi.id"
				+ " LEFT JOIN xx_store_member_sale_org smso ON smso.store_member = sm.id"
				+ " LEFT JOIN xx_sale_org t ON t.id = smso.sale_org"
				+ " WHERE	1 = 1"
				+ " AND smso.sale_org IN (SELECT id FROM xx_sale_org"
				+ " WHERE company_info_id = ? AND parent = 364)");
		list.add(companyInfoId);
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + name + "%");
		}
		if (saleOrgIds != null && saleOrgIds.length > 0) {
			String p = "";
			for (int i = 0; i < saleOrgIds.length; i++) {
				if (i == saleOrgIds.length - 1)
					p += saleOrgIds[i];
				else
					p += saleOrgIds[i] + ",";
			}
			sql.append(" and t.id in (" + p + ")");
		}
		if (!ConvertUtil.isEmpty(planFirstTime)) {
			sql.append(" and p.create_date >= ?");
			list.add(DateUtil.convert(planFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(planListTime)) {
			sql.append(" and p.create_date <= ?");
			list.add(DateUtil.convert(planListTime + " 23:59:59"));
		}
		if (!ConvertUtil.isEmpty(summaryFirstTime)) {
			sql.append(" and p.summary_time >= ?");
			list.add(DateUtil.convert(summaryFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(summaryListTime)) {
			sql.append(" and p.summaryListTime <= ?");
			list.add(DateUtil.convert(summaryListTime + " 23:59:59"));
		}
		sql.append(" GROUP BY p.id");
		sql.append(" ORDER BY p.create_date DESC");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public List<Map<String, Object>> findReportList(String name,Long[] saleOrgIds,
			String planFirstTime,String planListTime, String summaryFirstTime, String summaryListTime,
			Pageable pageable, Integer page, Integer size) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT p.id AS plan_id,sm.username,sm.name ,sm.id AS sm_id,p.create_date AS plan_time,"
				+ "p.summary_time,pi.content,pi.summary,pia.url AS work_picture,t.id AS sale_org,t.name AS sale_org_name"
				+ " FROM xx_plan p"
				+ " LEFT JOIN xx_store_member sm ON sm.id = p.store_member"
				+ " LEFT JOIN xx_plan_item pi ON pi.plan = p.id"
				+ " LEFT JOIN xx_plan_item_attach pia ON pia.plan_item = pi.id"
				+ " LEFT JOIN xx_store_member_sale_org smso ON smso.store_member = sm.id"
				+ " LEFT JOIN xx_sale_org t ON t.id = smso.sale_org"
				+ " WHERE	1 = 1"
				+ " AND smso.sale_org IN (SELECT id FROM xx_sale_org"
				+ " WHERE company_info_id = ? AND parent = 364)");
		list.add(companyInfoId);
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.name like ?");
			list.add("%" + name + "%");
		}
		if (saleOrgIds != null && saleOrgIds.length > 0) {
			String p = "";
			for (int i = 0; i < saleOrgIds.length; i++) {
				if (i == saleOrgIds.length - 1)
					p += saleOrgIds[i];
				else
					p += saleOrgIds[i] + ",";
			}
			sql.append(" and t.id in (" + p + ")");
		}
		if (!ConvertUtil.isEmpty(planFirstTime)) {
			sql.append(" and p.create_date >= ?");
			list.add(DateUtil.convert(planFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(planListTime)) {
			sql.append(" and p.create_date <= ?");
			list.add(DateUtil.convert(planListTime + " 23:59:59"));
		}
		if (!ConvertUtil.isEmpty(summaryFirstTime)) {
			sql.append(" and p.summary_time >= ?");
			list.add(DateUtil.convert(summaryFirstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(summaryListTime)) {
			sql.append(" and p.summaryListTime <= ?");
			list.add(DateUtil.convert(summaryListTime + " 23:59:59"));
		}
		sql.append(" GROUP BY p.id");
		sql.append(" ORDER BY p.create_date DESC");
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),objs,0);

		return maps;
	}

	public List<Map<String,Object>> findpostName(Long storeMemberId, String saleOrgId1) {
		StringBuilder sql = new StringBuilder();
		sql.append("select p.name from xx_store_member_sale_org_post s, xx_post p");
		sql.append(" where p.id = s.post and s.store_member=" + storeMemberId);
		if (!ConvertUtil.isEmpty(saleOrgId1)) {
			sql.append(" and s.sale_org in (" + saleOrgId1 + ")");
		}
		return getNativeDao().findListMap(sql.toString(), null, 0);
		
	}
	
}
