package net.shopxx.appfrom.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.appfrom.dao.ShowAppDao;
import net.shopxx.appfrom.service.ShowAppService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;

@Service("showAppServiceImpl")
public class ShowAppServiceImpl extends BaseServiceImpl<Object> implements ShowAppService{
	
	@Resource(name = "showAppDao")
	private ShowAppDao showAppDao;

	@Override
	public Page<Map<String, Object>> findPage(String subject, String areaName, String areaManager, String startDate, String endDate, Pageable pageable) {
		return showAppDao.findPage(subject, areaName, areaManager, startDate, endDate, pageable);
	}
	
	/**导出*/
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> countReport(String subject, String areaName, String areaManager, String startDate,
			String endDate, Pageable pageable, Integer page, Integer size) {
		return showAppDao.countReport(subject, areaName, areaManager,startDate,endDate,pageable,page,size);
	}
	
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findReportList(String subject, String areaName, String areaManager, String startDate,
			String endDate, Pageable pageable, Integer page, Integer size) {
		return showAppDao.findReportList(subject, areaName, areaManager,startDate,endDate,pageable,page,size);
	}
}
