package net.shopxx.appfrom.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.appfrom.dao.ShowStoreDao;
import net.shopxx.appfrom.service.ShowStoreService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
@Service("showStoreServiceImpl")
public class ShowStoreServiceImpl extends BaseServiceImpl<Object> implements ShowStoreService{
	
	@Resource(name = "showStoreDao")
	private ShowStoreDao showStoreDao;

	@Override
	public Page<Map<String, Object>> findPage(String storeName, String boss, String subject,
			String startDate, String endDate, String areaName, String areaManager, Pageable pageable) {
		return showStoreDao.findPage(storeName,boss,subject,startDate, endDate, areaName, areaManager, pageable);
	}
	/**导出*/
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> countReport(String storeName, String boss, String subject, String startDate,
			String endDate, String areaName, String areaManager, Pageable pageable, Integer page, Integer size) {
		return showStoreDao.countReport(storeName, boss, subject, startDate, endDate, areaName, areaManager, pageable,page,size);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findReportList(String storeName, String boss, String subject, String startDate,
			String endDate, String areaName, String areaManager, Pageable pageable, Integer page, Integer size) {
		return showStoreDao.findReportList(storeName, boss, subject, startDate, endDate, areaName, areaManager, pageable,page,size);
	}

}
