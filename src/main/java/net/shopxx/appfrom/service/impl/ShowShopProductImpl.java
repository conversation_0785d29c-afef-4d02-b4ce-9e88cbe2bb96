package net.shopxx.appfrom.service.impl;

import net.shopxx.appfrom.dao.ShowShopProductDao;
import net.shopxx.appfrom.service.ShowShopProductService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.member.entity.StoreMemberSaleOrgPost;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.entity.ShopProductCategory;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.shop.service.ShopProductCategoryService;
import net.shopxx.shop.service.ShopProductService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service("showShopProductImpl")
public class ShowShopProductImpl extends BaseServiceImpl<ShopInfo> implements ShowShopProductService {

    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
    @Resource(name = "showShopProductDao")
    private ShowShopProductDao showShopProductDao;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "shopProductServiceImpl")
    private ShopProductService shopProductService;
    @Resource(name = "shopProductCategoryServiceImpl")
    private ShopProductCategoryService shopProductCategoryService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgBaseService;


    @Override
    @Transactional
    public Page<Map<String, Object>> findShopInfoList(String shopName,//店名
                                                      String distributorName,//经销商姓名
                                                      String authorizationCode,
                                                      Long[] saleOrgIds,//机构
                                                      Long[] storeMemberIds,//区域经理
                                                      // String address,
                                                      //String[] shopStatus,
                                                      //Integer reimburse,
                                                      //String transitTime,
                                                      Pageable pageable) {

        //flag = false 查询全部必备、定制上样、常规上样非零的门店,否则查询所有
        Boolean flag = false;

        if (shopName != null || distributorName != null || authorizationCode != null
                || (saleOrgIds != null && (saleOrgIds.length > 0)) || (storeMemberIds != null && (storeMemberIds.length > 0))) {
            flag = true;
        }


        Page<Map<String, Object>> page = showShopProductDao.findPage(shopName, distributorName, authorizationCode, saleOrgIds, storeMemberIds, pageable);

        List<Map<String, Object>> dataList = page.getContent();

        if (dataList == null) {
            return page;
        }

        List<Filter> filters = new ArrayList<Filter>();
        //查出所有省长   地板中心区域总监id=19
        Long postId = 19L;
        filters.clear();
        filters.add(Filter.eq("post", postId));
        filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
        List<StoreMemberSaleOrgPost> smsopList = storeMemberSaleOrgPostService.findList(null, filters, null);

        //查询门店上样分类数量
        List<ShopProductCategory> shopProductCategories = shopProductCategoryService.findAll();

        //门店上样数量
        Set<Long> shopInfoIds = new TreeSet<Long>();
        for (Map<String, Object> data : dataList) {
            shopInfoIds.add(Long.parseLong(data.get("id").toString()));
        }
        List<Map<String, Object>> shopShopProductCounts = showShopProductDao.findListCount(shopInfoIds.toArray(new Long[shopInfoIds.size()]));

        //门店最后上样时间
        Page<Map<String, Object>> shopProductLastTimePage = showShopProductDao.findShopProductLastTime(shopName, distributorName, authorizationCode, saleOrgIds, storeMemberIds, pageable);
        List<Map<String, Object>> shopProductLastTimeList = shopProductLastTimePage.getContent();


        for (Iterator<Map<String, Object>> iterator = dataList.iterator(); iterator.hasNext(); ) {
            Map<String, Object> data = iterator.next();
            long id = Long.parseLong(data.get("id").toString());
            if (flag == true) {
                this.handleData(data, smsopList, shopProductLastTimeList, shopProductCategories, shopShopProductCounts);
            } else {
                //剔除全国必备、常规上样、定制上样的上样数是 0,0,0或者null、null、null的

                //剔除没上样过的店铺全国必备、常规上样、定制上样的上样数是null、null、null
                Set<Long> shopInfoIdSet = new TreeSet<Long>();
                for (Map<String, Object> map : shopShopProductCounts) {
                    long shopInfo = Long.parseLong(map.get("shopInfo").toString());
                    shopInfoIdSet.add(shopInfo);
                }
                if(!shopInfoIdSet.contains(id)){
                    iterator.remove();
                }

                //剔除全国必备、常规上样、定制上样的上样数是 0、0、0
                for (Map<String, Object> map : shopShopProductCounts) {
                    long shopInfo = Long.parseLong(map.get("shopInfo").toString());
                    if (id == shopInfo) {
                        if ((Integer.parseInt(map.get("shopProductCategory_1").toString()) +
                                Integer.parseInt(map.get("shopProductCategory_2").toString()) +
                                Integer.parseInt(map.get("shopProductCategory_3").toString())) == 0) {
                            iterator.remove();
                        } else {
                            this.handleData(data, smsopList, shopProductLastTimeList, shopProductCategories, shopShopProductCounts);

                        }
                    }
                }
            }
        }
        return page;

    }

    /**
     * 处理返回数据
     *
     * @param data
     * @param smsopList
     * @param shopProductLastTimeList
     * @param shopShopProductCounts
     */
    private void handleData(Map<String, Object> data, List<StoreMemberSaleOrgPost> smsopList, List<Map<String, Object>> shopProductLastTimeList, List<ShopProductCategory> shopProductCategories, List<Map<String, Object>> shopShopProductCounts) {
        long saleOrgId = Long.parseLong(data.get("saleOrgId").toString());
        long id = Long.parseLong(data.get("id").toString());
        //省长
        if (saleOrgId > -1) {
            if (smsopList != null && smsopList.size() > 0) {
                for (StoreMemberSaleOrgPost storeMemberSaleOrgPost : smsopList) {
                    if (storeMemberSaleOrgPost.getSaleOrg().getId() == saleOrgId) {
                        data.put("provinceName", storeMemberSaleOrgPost.getStoreMember().getName());
                        break;
                    }
                }
            }
        }

        //全国必备，常规上样，定制上样数量
        for (Map<String, Object> map : shopShopProductCounts) {
            long shopInfo = Long.parseLong(map.get("shopInfo").toString());
            if (shopInfo == id) {
                data.put("shopProductCategory_1", map.get("shopProductCategory_1"));
                data.put("shopProductCategory_2", map.get("shopProductCategory_2"));
                data.put("shopProductCategory_3", map.get("shopProductCategory_3"));
            }
        }

        //最后上样时间
        for (Map<String, Object> shopProductLastTime : shopProductLastTimeList) {
            if (shopProductLastTime.get("lastTime") != null && shopProductLastTime.get("shopInfoId") != null) {
                long shopInfoId = Long.parseLong(shopProductLastTime.get("shopInfoId").toString());
                if (shopInfoId == id) {
                    data.put("lastTime", shopProductLastTime.get("lastTime"));
                    break;
                }
            }

        }

    }

    /**
     * 查询数量
     *
     * @param distributorName
     * @param pageable
     * @return
     */
    @Override
    public Page<Map<String, Object>> countReport(String distributorName, Pageable pageable) {
        return showShopProductDao.countReport(distributorName, pageable);
    }

    /**
     * 查询某门店某门店上样分类已上样明细
     * @param shopInfoId
     * @param shopProductCategoryId
     * @param pageable
     * @return
     */
    @Override
    public Page<Map<String, Object>> findShopProductList(Long shopInfoId, Long shopProductCategoryId, Pageable pageable) {
        return showShopProductDao.findShopProduct(shopInfoId,shopProductCategoryId,pageable);
    }


}
