package net.shopxx.appfrom.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.appfrom.dao.ShowGuideDao;
import net.shopxx.appfrom.service.ShowGuideService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
@Service("showGuideServiceImpl")
public class ShowGuideServiceImpl extends BaseServiceImpl<Object> implements ShowGuideService{
	
	@Resource(name = "showGuideDao")
	private ShowGuideDao showGuideDao;
	@Override
	public Page<Map<String, Object>> findPage(String name, String storeName, String boss, String subject, String startDate, String endDate,
			 String areaName, String areaManager, Pageable pageable) {
		return showGuideDao.findPage(name, storeName, boss, subject, startDate, endDate, areaName, areaManager, pageable);
	}
	
	/**导出*/
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> countReport(String name, String storeName, String boss, String subject, String startDate, String endDate,
			 String areaName, String areaManager, Pageable pageable, Integer page, Integer size) {
		return showGuideDao.countReport(name, storeName, boss, subject, startDate, endDate, areaName, areaManager,pageable,page,size);
	}
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findReportList(String name, String storeName, String boss, String subject, String startDate, String endDate,
			 String areaName, String areaManager, Pageable pageable, Integer page, Integer size) {
		return showGuideDao.findReportList(name, storeName, boss, subject, startDate, endDate, areaName, areaManager,pageable,page,size);
	}
}
