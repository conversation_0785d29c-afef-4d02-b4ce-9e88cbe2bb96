package net.shopxx.appfrom.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;

public interface ShowAppService extends BaseService<Object> {
	
	Page<Map<String, Object>> findPage(String subject, String areaName, String areaManager, String startDate, String endDate,Pageable pageable);

	List<Map<String, Object>> findReportList(String subject, String areaName, String areaManager, String startDate, String endDate,
			Pageable pageable, Integer page, Integer size);

	Page<Map<String, Object>> countReport(String subject, String areaName, String areaManager, String startDate, String endDate,
			Pageable pageable, Integer page, Integer size);

}
