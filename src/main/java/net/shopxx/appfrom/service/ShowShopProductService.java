package net.shopxx.appfrom.service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.shop.entity.ShopInfo;

import java.util.List;
import java.util.Map;

public interface ShowShopProductService extends BaseService<ShopInfo>{

	Page<Map<String, Object>> findShopInfoList(String shopName,//店名
											   String distributorName,//经销商姓名
											   String authorizationCode,
											   Long[] saleOrgId,//机构
											   Long[] storeMemberId,//区域经理
											   // String address,
											   //String[] shopStatus,
											   //Integer reimburse,
											   //String transitTime,
											   Pageable pageable);

	Page<Map<String, Object>> countReport(String distributorName, Pageable pageable);

	Page<Map<String, Object>> findShopProductList(Long shopInfoId, Long shopProductCategoryId,Pageable pageable);
}
