package net.shopxx.appfrom.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;

public interface ShowStoreService extends BaseService<Object>{

	Page<Map<String, Object>> findPage(String storeName, String boss, String subject,String startDate, String endDate, String areaName, String areaManager, Pageable pageable);

	Page<Map<String, Object>> countReport(String storeName, String boss, String subject, String startDate, String endDate, String areaName, String areaManager,
			Pageable pageable, Integer page, Integer size);

	List<Map<String, Object>> findReportList(String storeName, String boss, String subject, String startDate, String endDate, String areaName, String areaManager,
			Pageable pageable, Integer page, Integer size);

}
