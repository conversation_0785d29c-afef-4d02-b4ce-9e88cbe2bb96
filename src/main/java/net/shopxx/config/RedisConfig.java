package net.shopxx.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * redis配置类
 * */

@Configuration("redisConfig")
@PropertySource("classpath:redis.properties")
public class RedisConfig {
	
	@Value("${redis.host}")
	private String host;
	
	@Value("${redis.port}")
	private Integer port;
	
	@Value("${redis.password}")
	private String password;
	
	@Value("${redis.timeout}")
	private Integer timeout;
	
	@Value("${redis.maxIdle}")
	private Integer maxIdle;
	
	@Value("${redis.maxActive}")
	private Integer maxActive;
	
	@Value("${redis.maxWait}")
	private Integer maxWait;
	
	@Value("${redis.timeBetweenEvictionRunsMillis}")
	private Integer timeBetweenEvictionRunsMillis;
	
	@Value("${redis.minEvictableIdleTimeMillis}")
	private Integer minEvictableIdleTimeMillis;
	
	@Value("${redis.testOnBorrow}")
	private Boolean testOnBorrow;
	
	@Value("${redis.encode}")
	private String encode;
	
	@Value("${redis.expire}")
	private Integer expire;
	
	@Value("${redis.unlock}")
	private Boolean unlock;
	
	@Value("${redis.database}")
	private Integer database;
	

	@Bean(name = "jedisPoolConfig")
	public JedisPoolConfig jedisPoolConfig() {
		JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
		
		jedisPoolConfig.setMaxIdle(maxIdle);
		jedisPoolConfig.setMaxActive(maxIdle);
		jedisPoolConfig.setMaxWait(maxWait);
		jedisPoolConfig.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
		jedisPoolConfig.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
		jedisPoolConfig.setTestOnBorrow(testOnBorrow);
		
		return jedisPoolConfig;
	}
	
	@Bean(name = "jedisPool")
	public JedisPool jedisPool(JedisPoolConfig poolConfig) {
		
		JedisPool jedisPool = new JedisPool(poolConfig, host, port, timeout, password, database);
		
		return jedisPool;
	}
	
	@Bean(name = "redisConnectionFactory")
	public RedisConnectionFactory redisConnectionFactory(JedisPoolConfig poolConfig) {
		JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
		//设置主机ip
		jedisConnectionFactory.setHostName(host);
		//设置端口
		jedisConnectionFactory.setPort(port);
		//设置密码
		jedisConnectionFactory.setPassword(password);
		//设置超时时间
		jedisConnectionFactory.setTimeout(timeout);
		jedisConnectionFactory.setDatabase(database);
		jedisConnectionFactory.setUsePool(true);
		jedisConnectionFactory.setPoolConfig(poolConfig);
		return jedisConnectionFactory;
	}

	@Bean(name = "redisTemplate")
	public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
		RedisTemplate<String, String> redisTemplate = new RedisTemplate<String,String>();
		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
		JdkSerializationRedisSerializer jdkSerializationRedisSerializer = new JdkSerializationRedisSerializer();
		redisTemplate.setConnectionFactory(redisConnectionFactory);
		redisTemplate.setKeySerializer(stringRedisSerializer);
		redisTemplate.setValueSerializer(stringRedisSerializer);
		redisTemplate.setHashKeySerializer(stringRedisSerializer);
		redisTemplate.setHashValueSerializer(jdkSerializationRedisSerializer);
		redisTemplate.afterPropertiesSet();
		return redisTemplate;
	}
	
	@Bean(name = "stringRedisTemplate")
	public RedisTemplate<String, String> stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
		StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
		JdkSerializationRedisSerializer jdkSerializationRedisSerializer = new JdkSerializationRedisSerializer();
		stringRedisTemplate.setConnectionFactory(redisConnectionFactory);
		stringRedisTemplate.setKeySerializer(stringRedisSerializer);
		stringRedisTemplate.setValueSerializer(stringRedisSerializer);
		stringRedisTemplate.setHashKeySerializer(stringRedisSerializer);
		stringRedisTemplate.setHashValueSerializer(jdkSerializationRedisSerializer);
		stringRedisTemplate.afterPropertiesSet();
		return stringRedisTemplate;
	}

}
