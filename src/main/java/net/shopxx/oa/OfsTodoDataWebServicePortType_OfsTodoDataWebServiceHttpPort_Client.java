
package net.shopxx.oa;

/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 2.7.1
 * 2019-10-22T15:18:03.204+08:00
 * Generated source version: 2.7.1
 * 
 */
public final class OfsTodoDataWebServicePortType_OfsTodoDataWebServiceHttpPort_Client {

    private static final QName SERVICE_NAME = new QName("webservices.ofs.weaver.com.cn", "OfsTodoDataWebService");

    private OfsTodoDataWebServicePortType_OfsTodoDataWebServiceHttpPort_Client() {
    }

    public static void main(String args[]) throws java.lang.Exception {
        URL wsdlURL = OfsTodoDataWebService.WSDL_LOCATION;
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) { 
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }
      
        OfsTodoDataWebService ss = new OfsTodoDataWebService(wsdlURL, SERVICE_NAME);
        OfsTodoDataWebServicePortType port = ss.getOfsTodoDataWebServiceHttpPort();  
        
        {
        System.out.println("Invoking receiveTodoRequestByMap...");
        net.shopxx.oa.AnyType2AnyTypeMap _receiveTodoRequestByMap_in0 = null;
        net.shopxx.oa.AnyType2AnyTypeMap _receiveTodoRequestByMap__return = port.receiveTodoRequestByMap(_receiveTodoRequestByMap_in0);
        System.out.println("receiveTodoRequestByMap.result=" + _receiveTodoRequestByMap__return);


        }
        {
        System.out.println("Invoking processDoneRequestByJson...");
        java.lang.String _processDoneRequestByJson_in0 = "";
        java.lang.String _processDoneRequestByJson__return = port.processDoneRequestByJson(_processDoneRequestByJson_in0);
        System.out.println("processDoneRequestByJson.result=" + _processDoneRequestByJson__return);


        }
        {
        System.out.println("Invoking processDoneRequestByMap...");
        net.shopxx.oa.AnyType2AnyTypeMap _processDoneRequestByMap_in0 = null;
        net.shopxx.oa.AnyType2AnyTypeMap _processDoneRequestByMap__return = port.processDoneRequestByMap(_processDoneRequestByMap_in0);
        System.out.println("processDoneRequestByMap.result=" + _processDoneRequestByMap__return);


        }
        {
        System.out.println("Invoking deleteUserRequestInfoByXML...");
        java.lang.String _deleteUserRequestInfoByXML_in0 = "";
        java.lang.String _deleteUserRequestInfoByXML__return = port.deleteUserRequestInfoByXML(_deleteUserRequestInfoByXML_in0);
        System.out.println("deleteUserRequestInfoByXML.result=" + _deleteUserRequestInfoByXML__return);


        }
        {
        System.out.println("Invoking processOverRequestByMap...");
        net.shopxx.oa.AnyType2AnyTypeMap _processOverRequestByMap_in0 = null;
        net.shopxx.oa.AnyType2AnyTypeMap _processOverRequestByMap__return = port.processOverRequestByMap(_processOverRequestByMap_in0);
        System.out.println("processOverRequestByMap.result=" + _processOverRequestByMap__return);


        }
        {
        System.out.println("Invoking deleteRequestInfoByJson...");
        java.lang.String _deleteRequestInfoByJson_in0 = "";
        java.lang.String _deleteRequestInfoByJson__return = port.deleteRequestInfoByJson(_deleteRequestInfoByJson_in0);
        System.out.println("deleteRequestInfoByJson.result=" + _deleteRequestInfoByJson__return);


        }
        {
        System.out.println("Invoking receiveRequestInfoByJson...");
        java.lang.String _receiveRequestInfoByJson_in0 = "";
        java.lang.String _receiveRequestInfoByJson__return = port.receiveRequestInfoByJson(_receiveRequestInfoByJson_in0);
        System.out.println("receiveRequestInfoByJson.result=" + _receiveRequestInfoByJson__return);


        }
        {
        System.out.println("Invoking receiveRequestInfoByMap...");
        net.shopxx.oa.AnyType2AnyTypeMap _receiveRequestInfoByMap_in0 = null;
        net.shopxx.oa.AnyType2AnyTypeMap _receiveRequestInfoByMap__return = port.receiveRequestInfoByMap(_receiveRequestInfoByMap_in0);
        System.out.println("receiveRequestInfoByMap.result=" + _receiveRequestInfoByMap__return);


        }
        {
        System.out.println("Invoking processOverRequestByXml...");
        java.lang.String _processOverRequestByXml_in0 = "";
        java.lang.String _processOverRequestByXml__return = port.processOverRequestByXml(_processOverRequestByXml_in0);
        System.out.println("processOverRequestByXml.result=" + _processOverRequestByXml__return);


        }
        {
        System.out.println("Invoking deleteUserRequestInfoByJson...");
        java.lang.String _deleteUserRequestInfoByJson_in0 = "";
        java.lang.String _deleteUserRequestInfoByJson__return = port.deleteUserRequestInfoByJson(_deleteUserRequestInfoByJson_in0);
        System.out.println("deleteUserRequestInfoByJson.result=" + _deleteUserRequestInfoByJson__return);


        }
        {
        System.out.println("Invoking receiveTodoRequestByJson...");
        java.lang.String _receiveTodoRequestByJson_in0 = "";
        java.lang.String _receiveTodoRequestByJson__return = port.receiveTodoRequestByJson(_receiveTodoRequestByJson_in0);
        System.out.println("receiveTodoRequestByJson.result=" + _receiveTodoRequestByJson__return);


        }
        {
        System.out.println("Invoking processDoneRequestByXml...");
        java.lang.String _processDoneRequestByXml_in0 = "";
        java.lang.String _processDoneRequestByXml__return = port.processDoneRequestByXml(_processDoneRequestByXml_in0);
        System.out.println("processDoneRequestByXml.result=" + _processDoneRequestByXml__return);


        }
        {
        System.out.println("Invoking deleteUserRequestInfoByMap...");
        net.shopxx.oa.AnyType2AnyTypeMap _deleteUserRequestInfoByMap_in0 = null;
        net.shopxx.oa.AnyType2AnyTypeMap _deleteUserRequestInfoByMap__return = port.deleteUserRequestInfoByMap(_deleteUserRequestInfoByMap_in0);
        System.out.println("deleteUserRequestInfoByMap.result=" + _deleteUserRequestInfoByMap__return);


        }
        {
        System.out.println("Invoking deleteRequestInfoByMap...");
        net.shopxx.oa.AnyType2AnyTypeMap _deleteRequestInfoByMap_in0 = null;
        net.shopxx.oa.AnyType2AnyTypeMap _deleteRequestInfoByMap__return = port.deleteRequestInfoByMap(_deleteRequestInfoByMap_in0);
        System.out.println("deleteRequestInfoByMap.result=" + _deleteRequestInfoByMap__return);


        }
        {
        System.out.println("Invoking processOverRequestByJson...");
        java.lang.String _processOverRequestByJson_in0 = "";
        java.lang.String _processOverRequestByJson__return = port.processOverRequestByJson(_processOverRequestByJson_in0);
        System.out.println("processOverRequestByJson.result=" + _processOverRequestByJson__return);


        }
        {
        System.out.println("Invoking receiveRequestInfoByXml...");
        java.lang.String _receiveRequestInfoByXml_in0 = "";
        java.lang.String _receiveRequestInfoByXml__return = port.receiveRequestInfoByXml(_receiveRequestInfoByXml_in0);
        System.out.println("receiveRequestInfoByXml.result=" + _receiveRequestInfoByXml__return);


        }
        {
        System.out.println("Invoking receiveTodoRequestByXml...");
        java.lang.String _receiveTodoRequestByXml_in0 = "";
        java.lang.String _receiveTodoRequestByXml__return = port.receiveTodoRequestByXml(_receiveTodoRequestByXml_in0);
        System.out.println("receiveTodoRequestByXml.result=" + _receiveTodoRequestByXml__return);


        }
        {
        System.out.println("Invoking deleteRequestInfoByXML...");
        java.lang.String _deleteRequestInfoByXML_in0 = "";
        java.lang.String _deleteRequestInfoByXML__return = port.deleteRequestInfoByXML(_deleteRequestInfoByXML_in0);
        System.out.println("deleteRequestInfoByXML.result=" + _deleteRequestInfoByXML__return);


        }

        System.exit(0);
    }

}
