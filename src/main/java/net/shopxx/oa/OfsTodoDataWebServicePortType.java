package net.shopxx.oa;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 2.7.1
 * 2019-10-22T15:18:03.268+08:00
 * Generated source version: 2.7.1
 * 
 */
@WebService(targetNamespace = "webservices.ofs.weaver.com.cn", name = "OfsTodoDataWebServicePortType")
@XmlSeeAlso({ObjectFactory.class})
public interface OfsTodoDataWebServicePortType {

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "receiveTodoRequestByMap", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveTodoRequestByMap")
    @WebMethod
    @ResponseWrapper(localName = "receiveTodoRequestByMapResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveTodoRequestByMapResponse")
    public net.shopxx.oa.AnyType2AnyTypeMap receiveTodoRequestByMap(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        net.shopxx.oa.AnyType2AnyTypeMap in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "processDoneRequestByJson", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessDoneRequestByJson")
    @WebMethod
    @ResponseWrapper(localName = "processDoneRequestByJsonResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessDoneRequestByJsonResponse")
    public java.lang.String processDoneRequestByJson(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "processDoneRequestByMap", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessDoneRequestByMap")
    @WebMethod
    @ResponseWrapper(localName = "processDoneRequestByMapResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessDoneRequestByMapResponse")
    public net.shopxx.oa.AnyType2AnyTypeMap processDoneRequestByMap(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        net.shopxx.oa.AnyType2AnyTypeMap in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "deleteUserRequestInfoByXML", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteUserRequestInfoByXML")
    @WebMethod
    @ResponseWrapper(localName = "deleteUserRequestInfoByXMLResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteUserRequestInfoByXMLResponse")
    public java.lang.String deleteUserRequestInfoByXML(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "processOverRequestByMap", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessOverRequestByMap")
    @WebMethod
    @ResponseWrapper(localName = "processOverRequestByMapResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessOverRequestByMapResponse")
    public net.shopxx.oa.AnyType2AnyTypeMap processOverRequestByMap(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        net.shopxx.oa.AnyType2AnyTypeMap in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "deleteRequestInfoByJson", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteRequestInfoByJson")
    @WebMethod
    @ResponseWrapper(localName = "deleteRequestInfoByJsonResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteRequestInfoByJsonResponse")
    public java.lang.String deleteRequestInfoByJson(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "receiveRequestInfoByJson", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveRequestInfoByJson")
    @WebMethod
    @ResponseWrapper(localName = "receiveRequestInfoByJsonResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveRequestInfoByJsonResponse")
    public java.lang.String receiveRequestInfoByJson(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "receiveRequestInfoByMap", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveRequestInfoByMap")
    @WebMethod
    @ResponseWrapper(localName = "receiveRequestInfoByMapResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveRequestInfoByMapResponse")
    public net.shopxx.oa.AnyType2AnyTypeMap receiveRequestInfoByMap(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        net.shopxx.oa.AnyType2AnyTypeMap in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "processOverRequestByXml", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessOverRequestByXml")
    @WebMethod
    @ResponseWrapper(localName = "processOverRequestByXmlResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessOverRequestByXmlResponse")
    public java.lang.String processOverRequestByXml(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "deleteUserRequestInfoByJson", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteUserRequestInfoByJson")
    @WebMethod
    @ResponseWrapper(localName = "deleteUserRequestInfoByJsonResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteUserRequestInfoByJsonResponse")
    public java.lang.String deleteUserRequestInfoByJson(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "receiveTodoRequestByJson", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveTodoRequestByJson")
    @WebMethod
    @ResponseWrapper(localName = "receiveTodoRequestByJsonResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveTodoRequestByJsonResponse")
    public java.lang.String receiveTodoRequestByJson(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "processDoneRequestByXml", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessDoneRequestByXml")
    @WebMethod
    @ResponseWrapper(localName = "processDoneRequestByXmlResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessDoneRequestByXmlResponse")
    public java.lang.String processDoneRequestByXml(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "deleteUserRequestInfoByMap", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteUserRequestInfoByMap")
    @WebMethod
    @ResponseWrapper(localName = "deleteUserRequestInfoByMapResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteUserRequestInfoByMapResponse")
    public net.shopxx.oa.AnyType2AnyTypeMap deleteUserRequestInfoByMap(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        net.shopxx.oa.AnyType2AnyTypeMap in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "deleteRequestInfoByMap", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteRequestInfoByMap")
    @WebMethod
    @ResponseWrapper(localName = "deleteRequestInfoByMapResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteRequestInfoByMapResponse")
    public net.shopxx.oa.AnyType2AnyTypeMap deleteRequestInfoByMap(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        net.shopxx.oa.AnyType2AnyTypeMap in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "processOverRequestByJson", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessOverRequestByJson")
    @WebMethod
    @ResponseWrapper(localName = "processOverRequestByJsonResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ProcessOverRequestByJsonResponse")
    public java.lang.String processOverRequestByJson(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "receiveRequestInfoByXml", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveRequestInfoByXml")
    @WebMethod
    @ResponseWrapper(localName = "receiveRequestInfoByXmlResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveRequestInfoByXmlResponse")
    public java.lang.String receiveRequestInfoByXml(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "receiveTodoRequestByXml", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveTodoRequestByXml")
    @WebMethod
    @ResponseWrapper(localName = "receiveTodoRequestByXmlResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.ReceiveTodoRequestByXmlResponse")
    public java.lang.String receiveTodoRequestByXml(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "webservices.ofs.weaver.com.cn")
    @RequestWrapper(localName = "deleteRequestInfoByXML", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteRequestInfoByXML")
    @WebMethod
    @ResponseWrapper(localName = "deleteRequestInfoByXMLResponse", targetNamespace = "webservices.ofs.weaver.com.cn", className = "net.shopxx.oa.DeleteRequestInfoByXMLResponse")
    public java.lang.String deleteRequestInfoByXML(
        @WebParam(name = "in0", targetNamespace = "webservices.ofs.weaver.com.cn")
        java.lang.String in0
    );
}
