package net.shopxx.common;

import net.shopxx.base.core.interceptor.AbstractHandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AccessInterceptor extends AbstractHandlerInterceptor {
  public boolean preHandleInternal(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
    return true;
  }

  public void postHandleInternal(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView mv) throws Exception {}

  public void afterCompletionInternal(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {}
}
