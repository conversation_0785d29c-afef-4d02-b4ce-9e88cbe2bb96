/*     */ package net.shopxx.common.dao;
/*     */ 
/*     */

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.stock.entity.StockTask;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Repository("lockDataDao")
/*     */ public class LockDataDao
/*     */   extends DaoCenter
/*     */ {
/*     */   public void lockOrder(String ids) {
/*  22 */     getNativeDao().update("update xx_order set id=id where id in (" + 
/*  23 */         ids + 
/*  24 */         ")", 
/*  25 */         null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void lockStock(String ids) {
/*  33 */     getNativeDao().update("update xx_stock set id=id where id in (" + 
/*  34 */         ids + 
/*  35 */         ")", 
/*  36 */         null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void lockWf(String ids) {
/*  44 */     getNativeDao().update("update wf set id=id where id in (" + 
/*  45 */         ids + 
/*  46 */         ")", 
/*  47 */         null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void lockStore(String ids) {
/*  55 */     getNativeDao().update("update xx_store set id=id where id in (" + 
/*  56 */         ids + 
/*  57 */         ")", 
/*  58 */         null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void lockSaleOrg(String ids) {
/*  66 */     getNativeDao().update("update xx_sale_org set id=id where id in (" + 
/*  67 */         ids + 
/*  68 */         ")", 
/*  69 */         null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void lockStock(List<StockTask> stockTasks) {
/*  78 */     List<Object> list = new ArrayList();
/*  79 */     StringBuilder sql = new StringBuilder();
/*  80 */     sql.append("update xx_stock set id = id where");
/*  81 */     for (int i = 0; i < stockTasks.size(); i++) {
/*  82 */       StockTask stockTask = stockTasks.get(0);
/*  83 */       if (i == 0) {
/*  84 */         sql.append(" (product = ? and warehouse = ?)");
/*     */       } else {
/*     */         
/*  87 */         sql.append(" or (product = ? and warehouse = ?)");
/*     */       } 
/*  89 */       list.add(stockTask.getProductId());
/*  90 */       list.add(stockTask.getWarehouseId());
/*     */     } 
/*  92 */     Object[] objs = new Object[list.size()];
/*  93 */     for (int j = 0; j < list.size(); j++) {
/*  94 */       objs[j] = list.get(j);
/*     */     }
/*  96 */     getNativeDao().update(sql.toString(), objs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void lockStockByProduct(String productIds) {
/* 105 */     StringBuilder sql = new StringBuilder();
/* 106 */     sql.append("update xx_stock set id = id where product in (" + 
/* 107 */         productIds + 
/* 108 */         ")");
/* 109 */     getNativeDao().update(sql.toString(), null);
/*     */   }
/*     */   
/*     */   public void lockOrderBySn(String sns) {
/* 113 */     getNativeDao().update("update xx_order set id=id where sn in (" + 
/* 114 */         sns + 
/* 115 */         ")", 
/* 116 */         null);
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\common\dao\LockDataDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */