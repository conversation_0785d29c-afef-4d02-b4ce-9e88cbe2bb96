package net.shopxx.common.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date2020/10/6 15:43
 * @Place:Daliang
 * @Version V1.0
 **/
@Repository("fileDownloadDao")
public class FileDownloadDao extends DaoCenter{

    public Map<String,Object> getAttachInfo(String tableName,Long id){
        String sql = "";
        if(tableName!=null && tableName.equals("tw_contract_attach")){  //特价申请菜单的附件没有url字段，故特殊处理
            sql = " select  file_name, file_url as url  from "+ tableName + "  where id = ?";
        }else{
            sql = " select  file_name, url   from "+ tableName + "  where id = ?";
        }

        List<Map<String, Object>> attachInfos = getNativeDao().findListMap(sql,
                new Object[] { id },
                1);
        if (!attachInfos.isEmpty()) {
            return attachInfos.get(0);
        }
        return null;
    }
}
