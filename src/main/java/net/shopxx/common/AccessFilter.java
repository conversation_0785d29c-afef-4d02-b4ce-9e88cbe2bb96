package net.shopxx.common;

import net.shopxx.base.core.filter.AbstractAccessFilter;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class AccessFilter extends AbstractAccessFilter {
	protected boolean doFilterBusiness(HttpServletRequest arg0, HttpServletResponse arg1)
			throws ServletException, IOException {
		return true;
	}
}
