package net.shopxx.common;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.fastjson.JSONObject;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.finance.service.StoreDepositService;
import net.shopxx.template.service.DUserColumnsService;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipOutputStream;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * @Description
 * <AUTHOR>
 * @Date2020/10/6 14:43
 * @Place:DaLiang
 * @Version V1.0
 **/
@Controller("fileDownload")
@RequestMapping("/common")
public class FileDownload {

    /**
     * 导出文件的临时文件url
     */
    @Resource(name = "storeDepositServiceImpl")
    private StoreDepositService storeDepositService;

    @Resource(name = "dUserColumnsServiceImpl")
    private DUserColumnsService dUserColumnsService;

    @RequestMapping(value = "/batchDownloadFile", method = RequestMethod.GET)
    public void download(String infos,Long userId, Long templateId,int excelType,HttpSession session,HttpServletResponse response) throws IOException {
        System.out.println("Down coming!"+infos );
        infos = StringEscapeUtils.unescapeHtml(infos);
        JSONObject jsonObject = JSONObject.parseObject(infos);
        String firstTime = jsonObject.getString("bill_start_dateStartTime");
        String lastTime = jsonObject.getString("bill_start_dateEndTime");
        System.out.println("firstTime："+firstTime);
        System.out.println("lastTime："+lastTime);
        response.reset();
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        //处理当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String time = sdf.format(new Date());
        String zipName = "客户余额报表"+time;
        try {
            zipName = new String(zipName.getBytes(),"ISO8859-1");
        } catch (UnsupportedEncodingException e) {
           e.printStackTrace();
        }
        response.setHeader("Content-Disposition","attachment; filename="+zipName+".zip");
        //创建压缩流
        ZipOutputStream out = null;
        out = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()));

        //获取遍历数据信息
        List<Map<String,Object>> mapList = dUserColumnsService.customerBalanceReportData(infos,userId,templateId,excelType);
        System.out.println("数量："+mapList.size());
        System.out.println("开始时间："+new Date());
        if (mapList!=null&&mapList.size()>0){
            //模板路径
            ServletContext servletContext =session.getServletContext();
            String path= servletContext.getRealPath("/WEB-INF/excelTemplate/accountStatement.xls");
            //遍历
            for (int i = 0; i < mapList.size(); i++) {
                if (mapList.get(i)==null){
                    continue;
                }
                Map<String,Object> info = mapList.get(i);
                Long storeId = Long.valueOf(String.valueOf(info.get("storeId")));
                //遍历生成报表条目
                if (excelType==1){  //大报表
                    this.upload(storeId,null,null,firstTime,lastTime,null,null,1,out,path,info);
                }else {   //小报表
                    Long[] sbuId = {Long.valueOf(String.valueOf(info.get("sbuId")))};   //sbu
                    Long[] organizationId = {Long.valueOf(String.valueOf(info.get("organizationId")))};  //经营组织
                    //机构
                    Long saleOrgId = info.get("saleOrgId")==null?null:Long.valueOf( String.valueOf(info.get("saleOrgId")));
                    this.upload(storeId,sbuId,organizationId,firstTime,lastTime,null,saleOrgId,2,out,path,info);
                }
                out.flush();
            }
            System.out.println("结束时间："+new Date());
            out.close();

        }

    }

    //生成条目
    public void upload(Long store, Long[] sbu,
                              Long[] organization, String firstTime, String lastTime, String[] type, Long saleOrgId,
                              int excelType,ZipOutputStream zipStream,String path,Map<String,Object> mapInfo){
        try{
            TemplateExportParams params = new TemplateExportParams(path);
            Long typeId = Long.valueOf(7);
            //处理数据源
            Map<String,Object> map =  storeDepositService.exportCustomerStatement(store, sbu, organization, firstTime, lastTime, type, saleOrgId, excelType, typeId);
            //客户名
            String storeName = mapInfo.get("store_alias")==null?"":String.valueOf(mapInfo.get("store_alias"));
            storeName =  storeName.replace("/","、");
            String sbuName = mapInfo.get("sbu_name")==null?"":String.valueOf(mapInfo.get("sbu_name"));
            String organizationName = mapInfo.get("organization_name")==null?"":String.valueOf(mapInfo.get("organization_name"));
            String saleOrgName = mapInfo.get("sale_org_name")==null?"":String.valueOf(mapInfo.get("sale_org_name"));
            String fileName = "";
            if (excelType==1){
                fileName = "【"+storeName+"】余额报表"+".xls";
            }else {
                fileName = "【"+storeName+"】"+saleOrgName+"_"+sbuName+"_"+organizationName+".xls";
            }

            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            // 字节缓冲区, 是内存读写流, 不同于指向硬盘的流, 字节数组是成员变量, 当数组不再使用的时候, GC会自动回收, 不用手动关闭流
            ByteArrayOutputStream out = new ByteArrayOutputStream();

            workbook.write(out);  //写入信息

            InputStream in = new ByteArrayInputStream(out.toByteArray());
            ZipEntry zipEntry = new ZipEntry(fileName); //自己命名，这里假设是1,2,3
            zipStream.putNextEntry(zipEntry);
            zipStream.setEncoding("GBK");
            IOUtils.copy(in, zipStream);
            }catch (Exception e) {
                 LogUtils.error("生成压缩条目异常"+e);
            }
    }
}
