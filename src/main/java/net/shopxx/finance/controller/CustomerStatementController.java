package net.shopxx.finance.controller;

import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.StoreDepositService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.util.CommonUtil;

/**
 * Controller - 客户余额报表
 */
@Controller("customerStatementController")
@RequestMapping("/finance/customer_statement")
public class CustomerStatementController extends BaseController {

	private static Logger logger = LoggerFactory.getLogger(CustomerStatementController.class);

	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeDepositServiceImpl")
	private StoreDepositService storeDepositService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
    @Resource(name = "menuJumpUtils")
    private MenuJumpUtils menuJumpUtils;

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Pageable pageable, ModelMap model, Integer flag,Long menuId) {

		model.addAttribute("flag", flag);
		model.addAttribute("code", code);
        model.addAttribute("menuId", menuId);
		return CommonUtil.getFolderPrefix(code) + "/finance/customer_statement/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Pageable pageable, ModelMap model, 
			String firstTime, String lastTime, Long type, Long saleOrgId, Long storeId, 
			Long sbuId,String memo, Long organizationId,Long userId,Long menuId) {
		// sbu
		model.addAttribute("code", code);
		// 开始时间
		model.addAttribute("firstTime", firstTime);
		// 结束时间
		model.addAttribute("lastTime", lastTime);
		// 类型
		model.addAttribute("type", type);
        // 备注
		if(ConvertUtil.isEmpty(memo)){
			memo = " AND A.单据开始日期 >= start_dateStartTime AND A.单据开始日期 <= start_dateEndTime AND A.`单据类型` in ('政策录入','财务录入','客户充值','发货通知单','退货单') ";
		}
        model.addAttribute("memo", memo);
        // 客户
		Store store = null;
		if (!ConvertUtil.isEmpty(storeId)) {
			store = storeService.find(storeId);
		}
		model.addAttribute("store", store);
		// 机构
		SaleOrg saleOrg = null;
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			saleOrg = saleOrgService.find(saleOrgId);
		}
		model.addAttribute("saleOrg", saleOrg);
		// SBU
		Sbu sbu = null;
		if (!ConvertUtil.isEmpty(sbuId)) {
			sbu = sbuService.find(sbuId);
		}
		model.addAttribute("sbu", sbu);
		// 经营组织
		Organization organization = null;
		if (!ConvertUtil.isEmpty(organizationId)) {
			organization = organizationService.find(organizationId);
		}
		model.addAttribute("organization", organization);
        //获取ModelMap
        menuJumpUtils.getModelMap(model, userId, menuId);

		return CommonUtil.getFolderPrefix(code) + "/finance/customer_statement/list";
	}

	/**
	 * 平台对账单列表
	 */
	@RequestMapping(value = "/platformSheet_list/{code}", method = RequestMethod.GET)
	public String platformSheet_list(@PathVariable String code, Pageable pageable, ModelMap model, String firstTime,
			String lastTime, Long type, Long saleOrgId, Long storeId,String memo, Long sbuId, Long organizationId,Long menuId,Long userId,String firstTimeNew) {

		model.addAttribute("code", code);
        // 备注
        System.out.println("MEMO///:"+memo);
        model.addAttribute("memo", memo);

		// 开始时间
        if(firstTimeNew.isEmpty()){
        	model.addAttribute("PfirstTime", firstTime);
        }else{
			model.addAttribute("PfirstTime", firstTimeNew);
		}
		// 结束时间
		model.addAttribute("PlastTime", lastTime);
		// 类型
		model.addAttribute("type", type);
		// 机构
		SaleOrg saleOrg = null;
		if (!ConvertUtil.isEmpty(saleOrgId)) {
			saleOrg = saleOrgService.find(saleOrgId);
		}
		model.addAttribute("PsaleOrg", saleOrg);
		// 客户
		Store store = null;
		if (!ConvertUtil.isEmpty(storeId)) {
			store = storeService.find(storeId);
		}
		model.addAttribute("Pstore", store);
		// SBU
		Sbu sbu = null;
		if (!ConvertUtil.isEmpty(sbuId)) {
			sbu = sbuService.find(sbuId);
		}
		model.addAttribute("Psbu", sbu);
		// 经营组织
		Organization organization = null;
		if (!ConvertUtil.isEmpty(organizationId)) {
			organization = organizationService.find(organizationId);
		}
		model.addAttribute("Porganization", organization);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);

		return CommonUtil.getFolderPrefix(code) + "/finance/customer_statement/platformSheet_list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Long storeId, String[] type, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime, Long saleOrgId, Pageable pageable, ModelMap model, Long typeId) {

//		if(sbuName==null){
//			return error("请输入SBU");
//		}
//		if(organizationName==null){
//			return error("请输入组织");
//		}
//		if(storeId==null){
//			return error("请输入客户");
//		}

		Page<Map<String, Object>> page = storeDepositService.findPageCustomerStatement(storeId, sbuId, organizationId,
				type, firstTime, lastTime, saleOrgId, pageable, typeId);

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 销售报表数据
	 */
	@RequestMapping(value = "/salesReportData", method = RequestMethod.POST)
	public @ResponseBody ResultMsg findSalesReportData(Long store, Long[] sbu, Long[] organizationId, String firstTime,
			String lastTime, String[] type, Long saleOrgId, Pageable pageable, Long typeId, String productCode,
			String productName, String billCode) {

		Page<Map<String, Object>> page = storeDepositService.findSalesReportData(store, sbu, organizationId, firstTime,
				lastTime, type, saleOrgId, pageable, typeId, productCode, productName, billCode);

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping(value = "/to_salesReport_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toSalesReportExport(Long store, Long[] sbu, Long[] organizationId,
			String firstTime, String lastTime, String[] type, Long saleOrgId, Pageable pageable, Long typeId,
			String productCode, String productName, String billCode) {

		Integer size = storeDepositService.findSalesReportCount(store, sbu, organizationId, firstTime, lastTime, type,
				saleOrgId, null, typeId, productCode, productName, billCode);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/salesReport_export", method = RequestMethod.GET)
	public ModelAndView salesReportExport(Long store, Long[] sbu, Long[] organizationId, String firstTime,
			String lastTime, String[] type, Long saleOrgId, Pageable pageable, Integer page, Long typeId,
			String productCode, String productName, String billCode, ModelMap model) {
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		pageable.setPageSize(size);
		pageable.setPageNumber(page);
		Page<Map<String, Object>> pageData = storeDepositService.findSalesReportData(store, sbu, organizationId,
				firstTime, lastTime, type, saleOrgId, pageable, typeId, productCode, productName, billCode);
		List<Map<String, Object>> data = pageData.getContent();
		return getSalesReportForList(data, model);
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(Long storeId, String[] type, Long[] sbuId,
			Long[] organizationId, String firstTime, String lastTime, Long saleOrgId, Long typeId) {
		List<Map<String, Object>> mapList = storeDepositService.findTableCustomerStatement(storeId, sbuId,
				organizationId, firstTime, lastTime, type, saleOrgId, null, typeId);

		Integer size = 0;
		if (!ConvertUtil.isEmpty(mapList) && mapList.size() > 0) {
			size = mapList.size();
		}

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	@RequestMapping(value = "/newcCondition_export", method = RequestMethod.GET)
	public @ResponseBody ResultMsg newcCondition_export(Long storeId, String[] type, Long[] sbuId,
			Long[] organizationId, String firstTime, String lastTime, Long saleOrgId, Long typeId, Integer page,
			Pageable pageable, HttpSession session, HttpServletResponse response) {
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		pageable.setPageSize(size);
		pageable.setPageNumber(page);
		try {
			ServletContext servletContext = session.getServletContext();
			String path = servletContext.getRealPath("/WEB-INF/excelTemplate/statementConditionExport.xls");
			TemplateExportParams params = new TemplateExportParams(path);
			// 处理数据源
			List<Map<String, Object>> listData = storeDepositService.getcustomerStatementListToEaeyPoi(storeId, type,
					sbuId, organizationId, firstTime, lastTime, saleOrgId, typeId, page, pageable);
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("maplist", listData);
			// 处理当前时间
			Date date = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			String time = sdf.format(date);
			String fileName = "客户对账单导出报表" + time;
			Workbook workbook = ExcelExportUtil.exportExcel(params, map);
			this.setResponseHeader(response, fileName);
			OutputStream os = response.getOutputStream();
			workbook.write(os);
			os.flush();
			os.close();
		} catch (IllegalArgumentException ex) {
			logger.error("客户对账单报表下标异常：", ex);
		} catch (Exception e) {
			logger.error("客户对账单报表异常：", e);
			return ResultMsg.success("系统错误");
		}
		return ResultMsg.success("导出报表成功");
	}

	// 发送响应流方法
	public void setResponseHeader(HttpServletResponse response, String fileName) {
		try {
			try {
				fileName = new String(fileName.getBytes(), "ISO8859-1");
			} catch (UnsupportedEncodingException e) {
				logger.error("导出异常命名", e);
			}
			response.setContentType("application/octet-stream;charset=ISO8859-1");
			response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xls");
			response.addHeader("Pargam", "no-cache");
			response.addHeader("Cache-Control", "no-cache");
		} catch (Exception ex) {
			logger.error("导出异常", ex);
		}
	}

	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(Long storeId, String[] type, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime, Long saleOrgId, Long typeId, ModelMap model, Integer page,
			Pageable pageable) {
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		pageable.setPageSize(size);
		pageable.setPageNumber(page);
		Page<Map<String, Object>> pageData = storeDepositService.findPageCustomerStatement(storeId, sbuId,
				organizationId, type, firstTime, lastTime, saleOrgId, pageable, typeId);
		List<Map<String, Object>> data = pageData.getContent();
		return getModelAndViewForList(data, model);
	}

	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "机构", "经营组织", "客户编码", "客户名称", "SBU", "仓库", "日期", "分类", "票据编号", "产品名称", "产品编码", "木种花色",
				"业务类型", "型号", "规格", "数量", "发货支数", "单价", "发货金额", "付款金额", "参考订单", "单据创建人", "备注", "平台结算价", "结算发货金额",
				"总部收款", "平台收款", "渠道", };
		// 设置单元格取值
		String[] properties = { "sale_org_name", "organization", "store_code", "store_name", "sbu_name",
				"warehouse_name", "start_date", "type", "sn", "product_name", "product_code", "wood_type_or_color",
				"businessType", "model", "spec", "shipping_quantity", "shipped_branch", "unit_price", "shipping_money",
				"policy_money", "order_sn", "creator_name", "memo", "sale_org_unit_price", "sale_org_shipped_amount",
				"all_company_amount", "all_sale_org_amount", "sys_business_type" };
		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };
		ModelAndView a = new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null),
				model);
		return a;
	}

	public ModelAndView getSalesReportForList(List<Map<String, Object>> data, ModelMap model) {

		// 设置导出表格名
		String filename = "销售报表" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "机构", "经营组织", "客户编码", "客户名称", "SBU", "产品分类", "仓库", "日期", "分类", "票据编号", "参考订单", "产品名称",
				"产品等级", "产品编码", "木种花色", "型号", "规格", "含水率", "新旧标识", "色号", "批次", "数量", "发货支数", "单价", "发货金额", "业务类型",
				"长度mm", "宽度mm", "厚度mm", "收货地址", "开单人", "产品结构", "是否新品", "是否爆款", "是否定制", "备注" };
		// 设置单元格取值
		String[] properties = { "sale_org_name", "organization", "store_code", "store_name", "sbu_name",
				"product_category", "warehouse_name", "start_date", "type", "sn", "order_sn", "product_name",
				"product_level", "product_code", "wood_type_or_color", "model", "spec", "moistureContent",
				"oldAndNew_logo", "color_number", "batch", "shipping_quantity", "shipped_branch", "unit_price",
				"shipping_money", "businessType", "length", "width", "thickness", "delivery_address", "creator_name",
				"product_results", "new_product", "hot_money", "customized", "memo" };

		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };
		ModelAndView a = new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null),
				model);
		return a;
	}

}
