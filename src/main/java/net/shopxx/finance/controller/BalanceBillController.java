package net.shopxx.finance.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.aftersales.b2b.service.B2bReturnsService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PolicyEntryService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.CreditRechargeService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.service.OrderService;
import net.shopxx.order.service.ShippingService;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;

/**
 * Controller - 可发货余额流水
 */
@Controller("financeBalanceBillController")
@RequestMapping("/finance/balanceBill")
public class BalanceBillController extends BaseController {

	@Resource(name = "creditRechargeServiceImpl")
	private CreditRechargeService creditRechargeService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "policyEntryServiceImpl")
	private PolicyEntryService policyEntryService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	@Resource(name = "b2bReturnsServiceImpl")
	private B2bReturnsService b2bReturnsService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "shippingServiceImpl")
	private ShippingService shippingService;

	/**
	 *  客户授信列表
	 */
	@RequestMapping(value = "/credit_recharge_list", method = RequestMethod.GET)
	public String list_code(Integer rechargeType, Long storeId,
			BigDecimal balance, Long organizationId,Long sbuId, Integer flag,
			Pageable pageable, ModelMap model) {

		model.addAttribute("flag", flag);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(46L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("rechargeType", rechargeType);
		Store store = storeService.find(storeId);
		model.addAttribute("store", store);
		model.addAttribute("balance", balance);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("sbuId", sbuId);

		return "/b/finance/balance/credit_recharge_list";
	}

	/**
	 * 客户授信列表数据
	 */
	@RequestMapping(value = "/credit_recharge_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg credit_recharge_list_data(String sn, Integer[] status,
			Integer rechargeType, Long creatorId, Long operatorId,
			Long storeId, Long[] organizationId,Long sbuId, Pageable pageable, ModelMap model) {
		
		String jsonPage = JsonUtils.toJson(creditRechargeService.findPage(1,
				sn,
				null,
				storeId,
				status,
				creatorId,
				operatorId,
				rechargeType,
				organizationId,
				sbuId,
				null,
				null,
				null,
				null,
				null,
				null,
				pageable));

		return success(jsonPage);
	}
	
	@RequestMapping(value = "/to_condition_export_credit", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExportCredit(String sn, Integer[] status,
			Integer rechargeType, Long creatorId, Long operatorId,
			Long storeId, Long organizationId,Long sbuId, ModelMap model) {

		List<Map<String, Object>> list = creditRechargeService.findTable(status,storeId,organizationId,sbuId);
		
		Integer size = list==null?0:list.size();
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/credit_recharge_export", method = RequestMethod.GET)
	public ModelAndView creditRechargeExport(String sn, Integer[] status,
			Integer rechargeType, Long creatorId, Long operatorId,
			Long storeId, Long organizationId,Long sbuId, ModelMap model) {
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = creditRechargeService.findTable(status,storeId,organizationId,sbuId);
		String store_name = "";
		String store_value = "";
		String types = "";
		String statu = "";
		String wfStates = "";
		for(Map<String, Object> val : data){
			if(val.get("recharge_type")!=null){
				String recharge_type = val.get("recharge_type").toString();
				if(recharge_type.equals("1")){
					store_name = "授信用户";
					store_value = "store_member_name";
				}else{
					store_name = "充值客户";
					store_value = "store_name";
				}
				val.put("store_name", store_name);
				val.put("store_value", store_value);
			}
			if(val.get("type")!=null){
				String type = val.get("type").toString();
				if(type.equals("0")){
					types = "未生效";
				}
				if(type.equals("1")){
					types = "已生效";
				}
				if(type.equals("2")){
					types = "已过期";
				}
				val.put("types", types);
			}
			if(val.get("doc_status")!=null){
				String doc_status = val.get("doc_status").toString();
				if(doc_status.equals("0")){
					statu = "已保存";
				}
				if(doc_status.equals("1")){
					statu = "处理中";
				}
				if(doc_status.equals("2")){
					statu = "已处理";
				}
				if(doc_status.equals("3")){
					statu = "已作废";
				}
				val.put("statu",statu);
			}
			if(val.get("wf_state")!=null){
				String wf_state = val.get("wf_state").toString();
				if(wf_state.equals("0")){
					wfStates = "未启动";
				}
				if(wf_state.equals("1")){
					wfStates = "审核中";
				}
				if(wf_state.equals("2")){
					wfStates = "已完成";
				}
				if(wf_state.equals("3")){
					wfStates = "已驳回";
				}
				val.put("wfStates",wfStates);
			}
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
						+ ".xls";
		// 设置标题
		String[] header = {"客户授信编号",
				store_name,
				"机构",
				"计划充值金额",
				"审批金额",
				"充值状态",
				"单据状态",
				"流程状态",
				"申请日期",
				"创建人"
		};
		// 设置单元格取值
		String[] properties = {"sn",
				store_value,
				"sale_org_name",
				"amount",
				"actual_amount",
				"types",
				"statu",
				"wfStates",
				"create_date",
				"creator_name"
				
		};
		Integer[] widths = {25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
		};
		
		return new ModelAndView(new ExcelView(filename, null, properties,
						header, widths, null, data, null), model);
	}

	
	

	/**
	 * 政策录入列表
	 */
	@RequestMapping(value = "/policy_entry_list", method = RequestMethod.GET)
	public String policy_entry_list(Integer flag, Integer type,
			Pageable pageable, Long storeId, BigDecimal balance,
			Long organizationId,Long sbuId, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("rechargeTypes", rechargeTypes);
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);

		Store store = storeService.find(storeId);
		model.addAttribute("store", store);
		model.addAttribute("balance", balance);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("sbuId", sbuId);

		return "/b/finance/balance/policy_entry_list";
	}

	/**
	 * 政策录入列表数据
	 */
	@RequestMapping(value = "/policy_entry_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg policy_entry_list_data(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice, Integer flag,
			Long organizationId,Long sbuId, Pageable pageable, ModelMap model) {

		// if (!Integer.valueOf(1).equals(flag)) {
		// 非审核页面进入
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.eq("value", "政策录入"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		if (rechargeTypes.size() == 0) {
			return this.error("查询失败，充值类型【政策录入】不存在");
		}
		else if (rechargeTypes.size() > 1) {
			return this.error("查询失败，充值类型【政策录入】存在多个");
		}
		rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		// }

		String jsonPage = JsonUtils.toJson(policyEntryService.newfindPage(new Integer[] { 1 },
				sn,
				null,
				storeId,
				null,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				minPrice,
				maxPrice,
				firstTime,
				lastTime,
				organizationId,
				sbuId,
				pageable));
		return success(jsonPage);
	}
	
	@RequestMapping(value = "/to_condition_export_policy", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExportPolicy(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice, Integer flag,
			Long organizationId,Long sbuId, Pageable pageable, ModelMap model) {
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.eq("value", "政策录入"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		
		Integer size = policyEntryService.countDepositRecharge(new Integer[] { 1 },
				sn,
				null,
				storeId,
				null,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				minPrice,
				maxPrice,
				firstTime,
				lastTime,
				sbuId,
				null,
				organizationId,
				pageable,
				null,
				null);
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/policy_entry_export", method = RequestMethod.GET)
	public ModelAndView policyEntryExport(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice, Integer flag,
			Long organizationId,Long sbuId, Pageable pageable,Integer page, ModelMap model) {
		
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.eq("value", "政策录入"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = policyEntryService.findDepositRechargeList(
				new Integer[] { 1 },
				sn,
				null,
				storeId,
				null,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				minPrice,
				maxPrice,
				firstTime,
				lastTime,
				sbuId,
				null,
				organizationId,
				pageable,
				page,
				size);

		String result = "";
		String flowPath = "";
		for(Map<String, Object> val : data){
			if(val.get("doc_status")!=null){
				String doc_status = val.get("doc_status").toString();
				if(doc_status.equals("0")){
					result = "已保存";
				}
				if(doc_status.equals("1")){
					result = "已提交";
				}
				if(doc_status.equals("2")){
					result = "已处理";
				}
				if(doc_status.equals("3")){
					result = "已关闭";
				}
				val.put("result", result);
			}
			if(val.get("wf_state")!=null){
				String wf_state = val.get("wf_state").toString();
				if(wf_state.equals("0")){
					flowPath = "未启动";
				}
				if(wf_state.equals("1")){
					flowPath = "审核中";
				}
				if(wf_state.equals("2")){
					flowPath = "已完成";
				}
				if(wf_state.equals("3")){
					flowPath = "已驳回";
				}
				val.put("flowPath", flowPath);
			}
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
						+ ".xls";
		// 设置标题
		String[] header = {"政策录入编号",
				"客户",
				"客户编码",
				"机构",
				"录入金额",
				"实际录入金额",
				"类型",
				"单据状态",
				"流程状态",
				"申请日期",
				"银行水单号",
				"创建日期",
				"创建人",
				"申请备注"
		};
		// 设置单元格取值
		String[] properties = {"sn",
				"store_name",
				"sn",
				"sale_org_name",
				"amount",
				"actual_amount",
				"recharge_type_value",
				"result",
				"flowPath",
				"apply_date",
				"bank_slip",
				"create_date",
				"creator_name",
				"memo"
		};
		Integer[] widths = {25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
		};
		
		return new ModelAndView(new ExcelView(filename, null, properties,
						header, widths, null, data, null), model);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/store_recharge_list", method = RequestMethod.GET)
	public String store_recharge_list(Long storeId, BigDecimal balance,
			Long organizationId,Long sbuId, Integer flag, Integer type, Pageable pageable,
			ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("rechargeTypes", rechargeTypes);
		model.addAttribute("flag", flag);
		model.addAttribute("type", type);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);

		Store store = storeService.find(storeId);
		model.addAttribute("store", store);
		model.addAttribute("balance", balance);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("sbuId", sbuId);

		return "/b/finance/balance/store_recharge_list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/store_recharge_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg store_recharge_list_data(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice, Integer flag,
			Long organizationId,Long sbuId, Pageable pageable, ModelMap model) {

		String jsonPage = JsonUtils.toJson(depositRechargeService.newfindPage(new Integer[] { 1 },
				sn,
				null,
				storeId,
				null,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				minPrice,
				maxPrice,
				firstTime,
				lastTime,
				organizationId,
				sbuId,
				null,
				null,
				null,
				null,
				null,
				pageable));
		return success(jsonPage);
	}
	
	@RequestMapping(value = "/to_condition_export_store", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExportStore(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice, Integer flag,
			Long organizationId,Long sbuId, Pageable pageable, ModelMap model) {

		Integer size = depositRechargeService.countDepositRecharge(new Integer[] { 1 },
				sn,
				null,
				storeId,
				null,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				minPrice,
				maxPrice,
				sbuId,
				firstTime,
				lastTime,
				null,
				organizationId,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null);
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	@RequestMapping(value = "/store_recharge_export", method = RequestMethod.GET)
	public ModelAndView storeRechargeExport(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, Long storeId, String firstTime, String lastTime,
			BigDecimal minPrice, BigDecimal maxPrice, Integer flag,
			Long organizationId,Long sbuId, Pageable pageable,Integer page, ModelMap model) {
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = depositRechargeService.findDepositRechargeList(
				new Integer[] { 1 },
				sn,
				null,
				storeId,
				null,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				minPrice,
				maxPrice,
				sbuId,
				firstTime,
				lastTime,
				null,
				organizationId,
				pageable,
				page,
				size,
				null,
				null,
				null,
				null,
				null);
		
		String type = "";
		String result = "";
		for(Map<String, Object> val : data){
			if(val.get("doc_status")!=null){
				String doc_status = val.get("doc_status").toString();
				if(doc_status.equals("0")){
					type = "已保存";
				}
				if(doc_status.equals("1")){
					type = "已提交";
				}
				if(doc_status.equals("2")){
					type = "已处理";
				}
				if(doc_status.equals("3")){
					type = "已作废";
				}
				val.put("types", type);
			}
			if(val.get("wf_state")!=null){
				String wf_state = val.get("wf_state").toString();
				if(wf_state.equals("0")){
					result = "未启动";
				}
				if(wf_state.equals("1")){
					result = "审核中";
				}
				if(wf_state.equals("2")){
					result = "已完成";
				}
				if(wf_state.equals("3")){
					result = "已驳回";
				}
				val.put("result",result);
			}
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
						+ ".xls";
		// 设置标题
		String[] header = {"余额充值编号",
				"充值客户",
				"客户编码",
				"机构",
				"经营组织",
				"充值金额",
				"实际充值金额",
				"充值类型",
				"单据状态",
				"流程状态",
				"申请日期",
				"银行水单号",
				"创建日期",
				"创建人",
				"申请备注"
		};
		// 设置单元格取值
		String[] properties = {"sn",
				"store_name",
				"out_trade_no",
				"sale_org_name",//机构
				"organization_name",//经营组织
				"amount",//充值金额
				"actual_amount",//实际充值金额
				"recharge_type_value",//充值类型
				"types",//单据状态
				"result",//流程状态
				"apply_date",//申请日期
				"bank_slip",//银行水单号
				"create_date",//创建日期
				"creator_name",//创建人
				"memo"//申请备注
		};
		Integer[] widths = {25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
		};
		
		return new ModelAndView(new ExcelView(filename, null, properties,
						header, widths, null, data, null), model);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/return_list", method = RequestMethod.GET)
	public String return_list(Long storeId, BigDecimal balance,
			Long organizationId,Long sbuId, Integer flag, Pageable pageable, ModelMap model) {
		model.addAttribute("flag", flag); // flag=1审核，flag=2入库

		Store store = storeService.find(storeId);
		model.addAttribute("store", store);
		model.addAttribute("balance", balance);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("sbuId", sbuId);
		return "/b/finance/balance/return_list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/return_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg return_list_data(String sn, Long[] storeId, Integer[] status,
			String firstTime, String lastTime, Integer flag, Long[] saleOrgId,
			Long organizationId,Long sbuId, Pageable pageable, ModelMap model) {

		//2019-05-17 冯旗 退货增加搜索条件影响加NULL
		Page<Map<String, Object>> page = b2bReturnsService.findPage(sn,
				null,
				storeId,
				sbuId,
				status,
				null,
				firstTime,
				lastTime,
				flag,
				organizationId,
				saleOrgId,
				null,
				null,
				null,
				pageable,
				null,
				null,
				null,
				null,
				null,
                null,
                null);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	@RequestMapping(value = "/to_condition_export_return", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExportReturn(String sn, Long[] storeId, Integer[] status,
			String firstTime, String lastTime, Integer flag, Long saleOrgId,
			Long organizationId,Long sbuId, Pageable pageable, ModelMap model) {

		List<Map<String,Object>> list = b2bReturnsService.findTable(sn,
				null,
				storeId,
				sbuId,
				status,
				null,
				firstTime,
				lastTime,
				flag,
				organizationId,
				saleOrgId,
				null,
				null,
				pageable);
		
		Integer size = list == null?0:list.size();
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	@RequestMapping(value = "/return_export", method = RequestMethod.GET)
	public ModelAndView returnExport(String sn, Long[] storeId, Integer[] status,
			String firstTime, String lastTime, Integer flag, Long saleOrgId,
			Long organizationId,Long sbuId, Pageable pageable, ModelMap model) {
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = b2bReturnsService.findTable(sn,
				null,
				storeId,
				sbuId,
				status,
				null,
				firstTime,
				lastTime,
				flag,
				organizationId,
				saleOrgId,
				null,
				null,
				pageable);
		
		String statuss = "";
		for(Map<String, Object> val : data){
			if(val.get("status")!=null){
				String st = val.get("status").toString();
				if(st.equals("0")){
					statuss = "未审核";
				}
				if(st.equals("1")){
					statuss = "已审核";
				}
				if(st.equals("2")){
					statuss = "入库中";
				}
				if(st.equals("3")){
					statuss = "已入库";
				}
				val.put("statuss", statuss);
			}
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
						+ ".xls";
		// 设置标题
		String[] header = {"退货单号",
				"客户",
				"机构",
				"退货状态",
				"退款金额",
				"实退款金额",
				"退货人姓名",
				"退货人电话",
				"退货原因",
				"创建日期"
		};
		// 设置单元格取值
		String[] properties = {"sn",
				"store_name",
				"sale_org_name",
				"statuss",
				"amount",
				"return_amount_item",
				"name",
				"mobile",
				"reason",
				"create_date"
		};
		Integer[] widths = {25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256
		};
		
		return new ModelAndView(new ExcelView(filename, null, properties,
						header, widths, null, data, null), model);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/order_list", method = RequestMethod.GET)
	public String list(Long storeId, BigDecimal balance, Long organizationId,Long sbuId,
			Pageable pageable, Integer readOnly, ModelMap model) {
		model.addAttribute("readOnly", readOnly);

		Store store = storeService.find(storeId);
		model.addAttribute("store", store);
		model.addAttribute("balance", balance);
		model.addAttribute("organizationId", organizationId);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(47L);
		model.addAttribute("isCheckWf", isCheckWf);

		// 创建订单保存后，是否直接变成"已下达"状态 0 否，1是
		int saveOrder2Unaudited = 0;
		try {
			saveOrder2Unaudited = Integer.parseInt(SystemConfig.getConfig("saveOrder2Unaudited",
					WebUtils.getCurrentCompanyInfoId()));
		}
		catch (Exception e) {}

		try {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("storeMember",
					storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId())));
			List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,
					filters,
					null);
			String value = SystemConfig.getConfig("hiddenAmountRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] role = value.split(",");
			List<String> list = Arrays.asList(role);
			int hiddenAmount = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					hiddenAmount++;
					break;
				}
			}
			model.addAttribute("hiddenAmount", hiddenAmount); // 订单是否展示金额 0 不展示
																// 非0 展示
		}
		catch (RuntimeException e) {

		}

		model.addAttribute("saveOrder2Unaudited", saveOrder2Unaudited);
		model.addAttribute("sbuId", sbuId);
		return "/b/finance/balance/order_list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/order_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg order_list_data(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			String store_member_name, Long saleOrgId, Long organizationId,Long sbuId,
			Pageable pageable, ModelMap model) {
		
		Page<Map<String, Object>> page = orderService.findPage(orderSn,
				outTradeNo,
				orderStatus,
				shippingStatus,
				sbuId,
				warehouseId,
				storeId,
				consignee,
				phone,
				address,
				deliveryCorpId,
				productId,
				paymentStatus,
				flag,
				2,
				firstTime,
				lastTime,
				confirmStatus,
				null,
				store_member_name,
				saleOrgId,
				organizationId,
				pageable);

//		List<Map<String, Object>> orders = page.getContent();
//
//		if (!orders.isEmpty()) {
//			String ids = "";
//			for (int i = 0; i < orders.size(); i++) {
//				Map<String, Object> map = orders.get(i);
//				if (i == orders.size() - 1) {
//					ids += map.get("id");
//				}
//				else {
//					ids += map.get("id") + ",";
//				}
//			}
//			List<Map<String, Object>> orderItems = orderService.findOrderItemListByOrderId(ids);
//			if (readOnly == null) {
//				orderItems = sort(orderItems, null);
//			}
//			List<Map<String, Object>> items = null;
//			for (Map<String, Object> map : orders) {
//				items = new ArrayList<Map<String, Object>>();
//				String orderId = map.get("id").toString();
//				for (Map<String, Object> itemMap : orderItems) {
//					String oid = itemMap.get("orders").toString();
//					if (readOnly != null) {
//						if (orderId.equals(oid)
//								&& itemMap.get("parent") == null) {
//							items.add(itemMap);
//						}
//					}
//					else {
//						if (orderId.equals(oid)) {
//							items.add(itemMap);
//						}
//					}
//				}
//				map.put("order_items", items);
//			}
//		}

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
	
	@RequestMapping(value = "/to_condition_export_order", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExportOrder(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			String store_member_name, Long saleOrgId, Long organizationId,Long sbuId,
			Pageable pageable, ModelMap model) {
		
		List<Map<String,Object>> maps = orderService.findTable(orderSn, outTradeNo, orderStatus,
				shippingStatus, sbuId, warehouseId, storeId, consignee, phone,
				address, deliveryCorpId, productId, paymentStatus, flag, null,
				firstTime, lastTime, confirmStatus, null, store_member_name,
				saleOrgId, organizationId, null, null, null);

		Integer size = maps==null?0:maps.size();
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	@RequestMapping(value = "/order_list_export", method = RequestMethod.GET)
	public ModelAndView orderListExport(String orderSn, String outTradeNo,
			Integer[] orderStatus, Integer[] exStatus, Integer[] paymentStatus,
			Integer[] shippingStatus, Integer[] flag, Long warehouseId,
			Long[] storeId, String phone, String consignee, String address,
			Long deliveryCorpId, Long productId[], String firstTime,
			String lastTime, Integer readOnly, Integer[] confirmStatus,
			String store_member_name, Long saleOrgId, Long organizationId,Long sbuId,
			Pageable pageable,Integer page, ModelMap model) {
		
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = orderService.findTable(orderSn, outTradeNo,
				orderStatus, shippingStatus, sbuId, warehouseId, storeId, consignee,
				phone, address, deliveryCorpId, productId, paymentStatus, flag,
				null, firstTime, lastTime, confirmStatus, null, store_member_name,
				saleOrgId, organizationId, pageable, page, size);
		
		String order_statuss = "";
		String shipping_statuss = "";
		String payment_statuss = "";
		for(Map<String, Object> val : data){
			if(val.get("order_status")!=null){
				String order_status = val.get("order_status").toString();
				if(order_status.equals("0")){
					order_statuss = "未确认";
				}
				if(order_status.equals("1")){
					order_statuss = "已确认";		
				}
				if(order_status.equals("2")){
					order_statuss = "已完成";
				}
				if(order_status.equals("3")){
					order_statuss = "已作废";
				}
				if(order_status.equals("4")){
					order_statuss = "已删除";
				}
				if(order_status.equals("5")){
					order_statuss = "已下达";
				}
				if(order_status.equals("6")){
					order_statuss = "已审核";
				}
				if(order_status.equals("7")){
					order_statuss = "已保存";
				}
				if(order_status.equals("9")){
					order_statuss = "异常";
				}
				val.put("order_statuss", order_statuss);
			}
			if(val.get("shipping_status")!=null){
				String shipping_status = val.get("shipping_status").toString();
				if(shipping_status.equals("0")){
					shipping_statuss = "未发货";
				}
				if(shipping_status.equals("1")){
					shipping_statuss = "部分发货";		
				}
				if(shipping_status.equals("2")){
					shipping_statuss = "完全发货";
				}
				if(shipping_status.equals("3")){
					shipping_statuss = "已收货";
				}
				val.put("shipping_statuss",shipping_statuss);
			}
			if(val.get("payment_status")!=null){
				String payment_status = val.get("payment_status").toString();
				if(payment_status.equals("0")){
					payment_statuss = "未支付";
				}
				if(payment_status.equals("1")){
					payment_statuss = "部分支付";	
				}
				if(payment_status.equals("2")){
					payment_statuss = "完全支付";
				}
				val.put("play", payment_statuss);
			}
			if(val.get("amount_closed")==null){
				val.put("amount_closed", 0);
			}
			
		}
		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
						+ ".xls";
		// 设置标题
		String[] header = {"订单编号",
				"下单时间",
				"订单状态",
				"客户名称",
				"客户编码",
				"下单人",
				"订单金额",
				"订单关闭金额",
				"仓库",
				"配送状态",
				"区域经理",
				"收货人",
				"收货人电话",
				"收货人地区",
				"收货人地址",
				"支付状态",
				"附言",
				"审核人",
				"审核时间",
				"机构",
				"创建时间",
				"来源单号",
		};
		// 设置单元格取值
		String[] properties = {"sn",
				"order_date",//下单时间
				"order_statuss",//订单状态
				"store_name",//客户名称
				"store_code",//客户编码
				"store_member_name",//下单人
				"amount",//订单金额
				"amount_closed",//订单关闭金额
				"warehouse_name",//仓库
				"shipping_statuss",//配送状态
				"regional_manager_name",//区域经理
				"consignee",//收货人
				"area_name",//收货人电话
				"area_name",//收货人地区
				"address",//收货人地址
				"play",//支付状态
				"memo",//附言
				"check_store_member_name",//审核人
				"check_date",//审核时间
				"sale_org_name",//机构
				"create_date",//创建时间
				"out_trade_no",//来源单号
		};
		Integer[] widths = {25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
		};
		
		return new ModelAndView(new ExcelView(filename, null, properties,
						header, widths, null, data, null), model);
	}
	
	
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}
	
	
	/**
	 * 列表
	 */
	@RequestMapping(value = "/shipping_list", method = RequestMethod.GET)
	public String shipping_list(Long storeId,Long sbuId,Long organizationId,
			BigDecimal balance, ModelMap model) {
		
		Store store = storeService.find(storeId);
		model.addAttribute("store", store);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("organizationId", organizationId);
		model.addAttribute("balance", balance);
		return "/b/finance/balance/shipping_list";
	}
	
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/shipping_list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg shipping_list_data(String sn,Long[] statuss,
			Long[] statusItems,Long storeId,Long sbuId,Long organizationId,
			Pageable pageable) {
		Page<Map<String, Object>> mapPage = shippingService.getShippedData(sn,statuss, 
				statusItems,storeId,sbuId,organizationId,pageable);
		String jsonPage = JsonUtils.toJson(mapPage);
		return success(jsonPage);
	}
	
	
	/**
	 * 客户余额查看---发货统计总记录数
	 * @param sn
	 * @param statuss
	 * @param statusItems
	 * @param storeId
	 * @param sbuId
	 * @param organizationId
	 * @param pageable
	 * @return
	 */
	@RequestMapping(value = "/to_condition_export_shipping", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> to_condition_export_shipping(String sn,
			Long[] statuss,Long[] statusItems,Long storeId,Long sbuId,Long organizationId,
			Pageable pageable) {

		Page<Map<String, Object>> mapPage = shippingService.getShippedData(sn,statuss, 
				statusItems,storeId,sbuId,organizationId,pageable);

		Integer size = (int) mapPage.getTotal();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	
	public ModelAndView getShippingModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			// 单据状态
			if (!ConvertUtil.isEmpty(str.get("status"))) {
				Integer status = (Integer) str.get("status");
				if (status == 0) {
					str.put("status", "未审核");
				} else if (status == 1) {
					str.put("status", "已审核");
				} else if (status == 2) {
					str.put("status", "作废");
				} else if (status == 3) {
					str.put("status", "部分发货");
				} else if (status == 4) {
					str.put("status", "完全发货");
				} else if (status == 5) {
					str.put("status", "审核中");
				}
			}
			// 创建日期
			if (!ConvertUtil.isEmpty(str.get("create_date"))) {
				String create_date = str.get("create_date").toString();
				str.put("create_date", create_date.substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";

		// 设置标题
		String[] header = { "单据编号", "单据状态", "客户名称", "客户编码", "机构", "发货金额", "实发货金额",
				"收货人", "收货人电话", "收货人地址", "备注", "创建人", "创建时间"};

		// 设置单元格取值
		String[] properties = { "sn", "status", "store_name", "out_trade_no", "sale_org_name", "amount",
				"shipped_amount", "consignee", "phone", "address", "memo", "store_member_name", "create_date"};

		// 设置列宽
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}
	
	
	/**
	 * 客户余额查看---发货导出
	 * @param sn
	 * @param statuss
	 * @param statusItems
	 * @param storeId
	 * @param sbuId
	 * @param organizationId
	 * @param pageable
	 * @param modelMap
	 * @param page
	 * @return
	 */
	@RequestMapping(value = "/shipping_export", method = RequestMethod.GET)
	public ModelAndView shipping_export(String sn,Long[] statuss,Long[] statusItems,
			Long storeId,Long sbuId,Long organizationId,Pageable pageable,ModelMap modelMap,
			int page) {

		Map<String, Integer> segments = getSegment();
		int page_size = segments.get("size");
		pageable.setPageNumber(page);
		pageable.setPageSize(page_size);
		
		Page<Map<String, Object>> mapPage = shippingService.getShippedData(sn,statuss, 
				statusItems,storeId,sbuId,organizationId,pageable);

		List<Map<String, Object>> data = mapPage.getContent();

		return getShippingModelAndViewForList(data, modelMap);

	}

}