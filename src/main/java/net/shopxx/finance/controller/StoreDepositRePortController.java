package net.shopxx.finance.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.StoreDepositService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;

/**
 * Controller - 客户资金明细报表
 */
@Controller("storeDepositRePortController")
@RequestMapping("/finance/store_deposit_report")
public class StoreDepositRePortController extends BaseController {

	@Resource(name = "storeDepositServiceImpl")
	private StoreDepositService storeDepositService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model) {

		return "/b2b/order_report/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		/**
		 * 用户经营组织权限
		 */
		List<Filter> filters = new ArrayList<Filter>();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						 Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						 if(organization!=null){
							 organizationList.add(organization);
						 }
					}
				}
			}
		}else{
			//经营组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			organizationList = organizationService.findList(null,filters,null);
		}
		model.addAttribute("organizations", organizationList);
		
		Integer isMember = 0;
		
		
		//根据当前登录用户ID找出所有关联的SBU
		StoreMember storeMember = storeMemberService.getCurrent();
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		List<StoreMemberSbu> sbus=storeMemberSbuService.findList(null, filters, null);
		model.addAttribute("sbus",
				sbus);
		isMember = storeMember.getMemberType();
		model.addAttribute("isMember",isMember);
		return "/finance/store_deposit_report/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_datas", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_datas(String sn, Integer[] type, Long[] storeId,
			String firstTime, String lastTime, String[] organizationName,
			Long[] saleOrgId,Long[] sbuId, Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = storeDepositService.findReport(sn,
				type,
				storeId,
				firstTime,
				lastTime,
				organizationName,
				saleOrgId,
				sbuId,
				pageable);
		
		List<Map<String, Object>> deposits = page.getContent();
	

		//添加余额流水明细
		if (!deposits.isEmpty()) {
			String ids = "";
			int types = 0;
			List<Map<String, Object>> orderItems  = new ArrayList<Map<String,Object>>();
				for (int i = 0; i < deposits.size(); i++) {
					Map<String, Object> map = deposits.get(i);
					if (i == deposits.size() - 1) {
						types=Integer.parseInt(map.get("type").toString());
						ids += map.get("id");
					}
					else {
						ids += map.get("id") + ",";
						types=Integer.parseInt(map.get("type").toString());
					}
					
				}

			    if(types==1){
					orderItems = storeDepositService.findOrderItemListById(ids);
				}else if(types==2){
					orderItems = storeDepositService.findReturnItemListById(ids);
				}
			    
			List<Map<String, Object>> items = null;
			for (Map<String, Object> maps : deposits) {
				items = new ArrayList<Map<String, Object>>();
				String orderId="-1" ;
				if(types==1||types ==2){	
					 orderId = maps.get("id").toString();
				}
				for (Map<String, Object> itemMap : orderItems) {
					String oid = itemMap.get("id").toString();
				
						if (orderId.equals(oid)) {
							items.add(itemMap);
						}
						
				}
				maps.put("order_items", items);
			
			}
			    
		}

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);

		
	}
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String sn, Integer[] type, String storeName,
			String firstTime, String lastTime, String organizationName,
			String sbuName, Pageable pageable, ModelMap model) {
		if(storeName==null){
			return error("请选择客户");
		}
		if(sbuName==null){
			return error("请选择SBU");
		}
		if(organizationName==null){
			return error("请选择组织");
		}
		List<String> list = new ArrayList<String>();
		Object[] Type = null;
		if(type!=null&&type.length>0){
			for(Integer ty : type){
				if(ty==1){
					list.add("订单");
				}
				if(ty==2){
					list.add("退货单");
				}
				if(ty==3){
					list.add("客户充值");
				}
				if(ty==4){
					list.add("政策录入");
				}
				if(ty==5){
					list.add("授信");
				}
			}			
			Type = list.toArray();			
		}
		
		
		

		Page<Map<String, Object>> page = storeDepositService.findPageCurrentAccount(sn,
				storeName,
				organizationName,
				sbuName,
				firstTime,
				lastTime,
				Type,
				pageable);

		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);

		
	}
	
	

	@RequestMapping(value = "/to_s", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExports(String sn, Integer[] type,
			Long[] storeId, String firstTime, String lastTime,
			String[] organizationName, Long[] saleOrgId,Long[] sbuId, Pageable pageable,
			ModelMap model) {
		Integer size = storeDepositService.countReports(sn,
				type,
				storeId,
				firstTime,
				lastTime,
				organizationName,
				saleOrgId,
				sbuId,
				pageable,
				null,
				null);
		

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/condition_exports", method = RequestMethod.GET)
	public ModelAndView conditionExports(String sn, Integer[] type,
			Long[] storeId, String firstTime, String lastTime,
			String[] organizationName, Long[] saleOrgId,Long[] sbuId, Pageable pageable,
			Integer page, ModelMap model) {
		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = storeDepositService.findReportList(sn,
				type,
				storeId,
				firstTime,
				lastTime,
				organizationName,
				saleOrgId,
				sbuId,
				pageable,
				page,
				size);
		return getModelAndViewForList(data, model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}


//=============================================导入导出========================================================
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(String sn, Integer[] type, String storeName,
			String firstTime, String lastTime, String organizationName,
			String sbuName, Pageable pageable, ModelMap model) {
		
		List<String> list = new ArrayList<String>();
		Object[] Type = null;
		if(type!=null&&type.length>0){
			for(Integer ty : type){
				if(ty==1){
					list.add("订单");
				}
				if(ty==2){
					list.add("退货单");
				}
				if(ty==3){
					list.add("客户充值");
				}
				if(ty==4){
					list.add("政策录入");
				}
				if(ty==5){
					list.add("授信");
				}
			}			
			Type = list.toArray();			
		}
		
		List<Map<String, Object>> page = storeDepositService.findExportTable(sn,
				storeName,
				organizationName,
				sbuName,
				firstTime,
				lastTime,
				Type);
		 
		Integer size =  page.size();
		

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, Integer[] type, String storeName,
			String firstTime, String lastTime, String organizationName,
			String sbuName, Pageable pageable, ModelMap model) {
	
		List<String> list = new ArrayList<String>();
		Object[] Type = null;
		if(type!=null&&type.length>0){
			for(Integer ty : type){
				if(ty==1){
					list.add("订单");
				}
				if(ty==2){
					list.add("退货单");
				}
				if(ty==3){
					list.add("客户充值");
				}
				if(ty==4){
					list.add("政策录入");
				}
				if(ty==5){
					list.add("授信");
				}
			}			
			Type = list.toArray();			
		}
		
		List<Map<String, Object>> data = storeDepositService.findExportTable(sn,
				storeName,
				organizationName,
				sbuName,
				firstTime,
				lastTime,
				Type);
		
		return getModelAndViewForList(data, model);
	}
	
	
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {
		
		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		//设置标题
		String[] header = { "单据类型",
				"当前余额",
				"单据编号",
				"单据开始时间",
				"单据结束时间",
				"单据状态",
				"SBU",
				"客户",
				"机构",
				"经营组织",
				"数量",
				"发货数量",
				"关闭数量",
				"销售单价",
				"平台结算价",
				"金额",
				"发货金额",
				"关闭金额",
				"结算金额",
				"结算发货金额",
				"结算关闭金额",
				"产品",
				"品类",
				"备注"};
		
		//设置标题
		String[] headers = { "单据类型",
				"当前余额",
				"单据编号",
				"单据开始时间",
				"单据结束时间",
				"单据状态",
				"SBU",
				"客户",
				"机构",
				"经营组织",
				"数量",
				"发货数量",
				"关闭数量",
				"销售单价",
				"平台结算价",
				"金额",
				"发货金额",	
				"产品",
				"品类",
				"备注"};
		

		//设置单元格取值
		String[] properties = { "type",
				"balances",
				"sn",
				"create_date",
				"modify_date",
				"invoice_status",
				"sbu_name",
				"store_name",
				"sale_org_name",
				"organization",
				"quantity",
				"shipping_quantity",
				"close_quantity",
				"unit_price",
				"sale_org_price",
				"money",
				"shipping_money",
				"close_money",
				"settle_account",
				"shipping_settle_account",
				"close_settle_account",
				"product_name",
				"product_category",
				"memo"};
		
		//设置单元格取值
		String[] propertiess = { "type",
				"balances",
				"sn",
				"create_date",
				"modify_date",
				"invoice_status",
				"sbu_name",
				"store_name",
				"sale_org_name",
				"organization",
				"quantity",
				"shipping_quantity",
				"close_quantity",
				"unit_price",
				"sale_org_price",
				"money",
				"shipping_money",
				"product_name",
				"product_category",
				"memo"};
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256};
		ModelAndView a = null;
		
		Integer isMember = storeMemberService.getCurrent().getMemberType();
		if(isMember==0){
			a = new ModelAndView(new ExcelView(filename, null, properties,
					header, widths, null, data, null), model);			
			
		}else{
			a = new ModelAndView(new ExcelView(filename, null, propertiess,
					headers, widths, null, data, null), model);	
		}
		
		return a;

	}
	
}