package net.shopxx.finance.controller;

import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.finance.service.AccountStatementService;
import net.shopxx.member.service.StoreBaseService;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller("financeAccountStatementController")
@RequestMapping("/finance/accountStatement")
public class AccountStatementController extends BaseController {

	@Resource(name = "accountStatementServiceImpl")
	private AccountStatementService accountStatementService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer isCheck, ModelMap model) {

		return "/finance/accountStatement/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, Integer isCheck, ModelMap model) {

		return "/finance/accountStatement/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(String month, Long storeId, Long saleOrgId,
			Pageable pageable) {

		if (month == null) {
			return this.error("请选择月份");
		}

		Page<Map<String, Object>> page = accountStatementService.findPage(month,
				storeId,
				saleOrgId,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/item_list", method = RequestMethod.GET)
	public String item_list(String month, Long storeId, ModelMap model) {

		model.addAttribute("month", month);
		model.addAttribute("store", storeBaseService.find(storeId));
		return "/finance/accountStatement/item_list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "item_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg item_list_data(String month, Long storeId, String sn,
			Integer type, Pageable pageable) {

		if (month == null) {
			return this.error("请选择月份");
		}

		if (storeId == null) {
			return this.error("请选择客户");
		}

		Page<Map<String, Object>> page = accountStatementService.findLinePage(month,
				storeId,
				sn,
				type,
				pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}
}
