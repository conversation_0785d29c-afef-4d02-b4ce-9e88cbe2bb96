package net.shopxx.finance.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.fastjson.JSONObject;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.StoreDepositService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.*;
import net.shopxx.template.service.DUserColumnsService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Controller - 外部使用-客户对账单
 */
@Controller("externalUseStatementController")
@RequestMapping("/b/finance/account_statement")
public class ExternalUseStatementController extends BaseController{
	
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeDepositServiceImpl")
	private StoreDepositService storeDepositService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "dUserColumnsServiceImpl")
	private DUserColumnsService dUserColumnsService;
	
	
	private static Logger logger = LoggerFactory.getLogger(ExternalUseStatementController.class);
	
		
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Pageable pageable, ModelMap model) {
		return "/b/finance/account_statement/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		
		/**
		 * 用户经营组织权限
		 */
		
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						 Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						 if(organization!=null){
							 organizationList.add(organization);
						 }
					}
				}
			}
		}else{
			//经营组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("type", 0));
			organizationList = organizationService.findList(null,filters,null);
			
		}
		model.addAttribute("managementOrganizations", organizationList);
		
		// 查询用户关联SBU
		List<Map<String, Object>> sbu = storeMemberService.findSbuTy(storeMember.getId());
		if (sbu.size() > 0) {
			Long sbuIds = Long.parseLong(sbu.get(0).get("id").toString());
			model.addAttribute("sbuIds", sbuIds);

		}
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember.getId()));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		model.addAttribute("sbus", sbus);

		return "/b/finance/account_statement/list";
	}
	
	@RequestMapping(value = "/sales_report", method = RequestMethod.GET)
	public String sales_report(Long userId, Long menuId, ModelMap model) {
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/b/finance/account_statement/sales_report";
	}
	
	
	@RequestMapping(value = "/customerStatement_list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(Long storeId, String[] type, Long[] sbuId,
			Long[] organizationId, String firstTime, String lastTime,
			Long saleOrgId,Pageable pageable, ModelMap model,
			Long typeId) {
		
		Page<Map<String, Object>> page =  storeDepositService.findPageCustomerStatement(storeId, sbuId, organizationId, type, firstTime, lastTime, saleOrgId, pageable, typeId);
			
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	
	
	/**
	 * 
	 * @param store  客户id
	 * @param sbu   
	 * @param organization 经营组织id
	 * @param firstTime  开始时间
	 * @param lastTime   结束时间
	 * @param type    
	 * @param saleOrgId   
	 * @param excelType  报表类型 1-大报表 2-小报表
	 * @param session
	 * @param response
	 * @return
	 */
	@SuppressWarnings({ "unused", "rawtypes" })
	@RequestMapping(value = "/exportCustomerStatement", method = RequestMethod.GET)
	public @ResponseBody ResultMsg exportCustomerStatement(Long store, Long[] sbu,
			Long[] organization,String firstTime, String lastTime,String[] type,Long saleOrgId,
			int excelType,HttpSession session,HttpServletResponse response) {
		try{
			ServletContext servletContext =session.getServletContext();
			String path= servletContext.getRealPath("/WEB-INF/excelTemplate/accountStatement.xls");
			TemplateExportParams params = new TemplateExportParams(path);
			Long typeId = Long.valueOf(7);
			//处理数据源
			Map<String,Object> map =  storeDepositService.exportCustomerStatement(store, sbu, organization, firstTime, lastTime, type, saleOrgId, excelType, typeId);
			//处理当前时间
			Date date = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			String time = sdf.format(date);
			//客户名
			String storeName = map.get("userName")==null?"":String.valueOf(map.get("userName"));
			String fileName = "【"+storeName+"】客户对账单报表"+time;
			Workbook workbook = ExcelExportUtil.exportExcel(params, map);
			this.setResponseHeader(response, fileName);
			OutputStream os = response.getOutputStream();
			workbook.write(os);
			os.flush();
			os.close();
		}catch(IllegalArgumentException ex){
			logger.error("客户余额报表下标异常：",ex);
		}catch (Exception e) {
			logger.error("客户余额报表异常：",e);
			return ResultMsg.success("系统错误");
		}
		return ResultMsg.success("导出报表成功");
	}



	public void setResponseHeader(HttpServletResponse response, String fileName) {
		try {
			try {
				fileName = new String(fileName.getBytes(),"ISO8859-1");
			} catch (UnsupportedEncodingException e) {
				logger.error("导出异常命名",e);
			}
			response.setContentType("application/octet-stream;charset=ISO8859-1");
			response.setHeader("Content-Disposition", "attachment;filename="+ fileName+".xls");
			response.addHeader("Pargam", "no-cache");
			response.addHeader("Cache-Control", "no-cache");
		} catch (Exception ex) {
			logger.error("导出异常",ex);
		}
	}


	/**
	 * 导出多个客户的多组织excel的压缩包,批量导出excel生成zip
	 */
	@SuppressWarnings({ "unused", "rawtypes" })
	@RequestMapping(value = "/exportDetail", method = RequestMethod.GET)
	public @ResponseBody void exportDetail(String infos,Long userId, Long templateId,int excelType,HttpServletResponse response, HttpServletRequest request)throws  IOException {
		infos = StringEscapeUtils.unescapeHtml(infos);
		JSONObject jsonObject = JSONObject.parseObject(infos);
		String firstTime = jsonObject.getString("bill_start_dateStartTime");
		String lastTime = jsonObject.getString("bill_start_dateEndTime");

		//确定遍历次数

		List<Map<String,Object>> mapList = dUserColumnsService.customerBalanceReportData(infos,userId,templateId,excelType);
		System.out.println("数量："+mapList.size());
		// 模板在项目存放位置
		String fileRootPath = request.getSession().getServletContext().getRealPath("/WEB-INF/excelTemplate");
		// 模板文件名称
		String fileName = "accountStatement.xls";
		TemplateExportParams params = new TemplateExportParams(fileRootPath+"/"+fileName);
		Long typeId = Long.valueOf(7);

		// 将zip导出的文件位置
		String zipFilePath = request.getSession().getServletContext().getRealPath("/")+"/WEB-INF/Export";

		// 得到此路径下文件
		File fileDir = new File(zipFilePath);
		//	创建文件夹
		if (!fileDir.exists() && !fileDir.isDirectory()) {
			fileDir.mkdirs();
		}
		// 用于存放生成的excel文件名称
		List<String> excelFileNames = new ArrayList<String>();
		// 导出Excel文件路径
		String fullFilePath = "";
		//输入流
		InputStream in = null;
		//输出流
		FileOutputStream os = null;
		//循环导出excel到临时文件夹中
		for (int i = 0; i<mapList.size(); i++) {
			// 往excel填入内容
			Map<String,Object> info = mapList.get(i);
			Long storeId = Long.valueOf(String.valueOf(info.get("storeId")));
			Map<String,Object> map = null;
			if (excelType==1){  //大报表
				map =  storeDepositService.exportCustomerStatement(storeId, null, null, firstTime, lastTime, null, null, 1, typeId);
			}else {   //小报表
				Long[] sbuId = {Long.valueOf(String.valueOf(info.get("sbuId")))};   //sbu
				Long[] organizationId = {Long.valueOf(String.valueOf(info.get("organizationId")))};  //经营组织
				//机构
				Long saleOrgId = info.get("saleOrgId")==null?null:Long.valueOf( String.valueOf(info.get("saleOrgId")));
				map =  storeDepositService.exportCustomerStatement(storeId, sbuId, organizationId, firstTime, lastTime, null, saleOrgId, 1, typeId);
			}
			Iterator<Map.Entry<String, Object>> entries = map.entrySet().iterator();
			while (entries.hasNext())
			{
				Map.Entry<String, Object> entry = entries.next();
				System.out.println("Key = " + entry.getKey() + ", Value = " + entry.getValue());
			}
			//处理当前时间
			Date date = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			String time = sdf.format(date);
			//客户名
			String storeName = map.get("userName")==null?"":String.valueOf(map.get("userName"));
			//每次导出的excel的文件名
			String sbuName = map.get("sbuName")==null?"":String.valueOf(map.get("sbuName"));
			String organizationName = map.get("organizationName")==null?"":String.valueOf(map.get("organizationName"));
			String saleOrgName = map.get("saleOrgName")==null?"":String.valueOf(map.get("saleNOrgName"));
			String excelFileName = "";
			if (excelType==1){
				excelFileName = "【"+storeName+"】客户对账单报表"+time+".xls";
			}else {
				excelFileName = "【"+storeName+"】"+saleOrgName+"_"+sbuName+"_"+organizationName+".xls";
			}

			//生成excel文件
			long abegin=System.currentTimeMillis();
			Workbook workbook = ExcelExportUtil.exportExcel(params, map);
			long aend=System.currentTimeMillis();
			System.out.println("生成Excel所用时间"+(double)(aend-abegin)/1000);
			// 设置sheet页名称
//			String sheetName = "sheet1";
//			workbook.setSheetName(0, sheetName);
			// 生成excel的全路径
			fullFilePath =  request.getSession().getServletContext().getRealPath("/WEB-INF/Export/")+File.separator + excelFileName;
			excelFileNames.add(fullFilePath);

			if (!fileDir.exists() && !fileDir.isDirectory()) {
				fileDir.mkdirs();
			}
			long IObegin=System.currentTimeMillis();
			try {
				os = new FileOutputStream(fullFilePath);
				// 写文件
				workbook.write(os);
			}catch(FileNotFoundException e){

			}
			finally{

				if(os != null){
					try {
						//清空流缓冲区数据
						os.flush();
						//关闭流
						os.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
			//in.close();
			os = null;
			long IOend=System.currentTimeMillis();
			System.out.println("IO流所用时间"+(double)(IOend-IObegin)/1000);
		}

		//导出压缩文件的全路径
		 zipFilePath = zipFilePath+ "/客户对账单"+".zip";
		//导出zip
		File zip = new File(zipFilePath);
		System.out.println("excel文件的数量："+excelFileNames.size());
		//将excel文件生成压缩文件
		File srcfile[] = new File[excelFileNames.size()];
		for (int j = 0, n1 = excelFileNames.size(); j < n1; j++) {
			srcfile[j] = new File(excelFileNames.get(j));
			System.out.println("Excel文件名："+srcfile[j].getName());
		}
		String zipName =zip.getName();
		try {
			zipName = new String(zip.getName().getBytes(),"ISO8859-1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		System.out.println("ZIP文件名："+zip.getName());
		ZipFiles(srcfile, zip);
		System.out.println("生成zip完成");
		response.setContentType("text/html; charset=utf-8");
		response.setContentType("application/zip");
		response.setHeader("Location",zip.getName());
		response.setHeader("Content-Disposition", "attachment; filename=" + zipName);
		OutputStream outputStream = response.getOutputStream();
		InputStream inputStream = new FileInputStream(zipFilePath);
		byte[] buffer = new byte[1024];
		int i = -1;
		while ((i = inputStream.read(buffer)) != -1) {
			outputStream.write(buffer, 0, i);
		}
		outputStream.flush();
		outputStream.close();
		inputStream.close();
		outputStream = null;

	}


	//压缩文件
	public void ZipFiles(File[] srcfile, File zipfile) {
		byte[] buf = new byte[1024];
		ZipOutputStream out = null;
		try {
			out = new ZipOutputStream(new FileOutputStream(
					zipfile));
			out.setEncoding("GBK");
			for (int i = 0; i < srcfile.length; i++) {
				FileInputStream in = new FileInputStream(srcfile[i]);
				out.putNextEntry(new ZipEntry(srcfile[i].getName()));
				int len;
				while ((len = in.read(buf)) > 0) {
					out.write(buf, 0, len);
				}
				if(out != null){
					out.closeEntry();
				}
				in.close();
			}

		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			if(out != null){
				try {
					out.close();
				}
				catch (IOException e){}
			}
		}
	}



}
