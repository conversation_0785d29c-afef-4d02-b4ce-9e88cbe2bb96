package net.shopxx.finance.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.entity.AccountParameter;
import net.shopxx.finance.service.AccountParameterService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/*
 * Controller - 可发货余额
 */
@Controller("financeBalanceController")
@RequestMapping("/finance/balance")
public class BalanceController extends BaseController {

	@Resource(name = "accountParameterServiceImpl")
	private AccountParameterService accountParameterService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Integer flag, 
			Long menuId, ModelMap model) {
		
		model.addAttribute("code", code);
		model.addAttribute("flag", flag);
		model.addAttribute("menuId", menuId);
		return CommonUtil.getFolderPrefix(code) + "/finance/balance/list_tb";
	}

	/*
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Long userId, 
			Long menuId, ModelMap model) {
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		model.addAttribute("code", code);
		return CommonUtil.getFolderPrefix(code) + "/finance/balance/list";
	}

	/*
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(Long storeId, Long saleOrgId, Long[] organizationId,
			Integer[] type, Long[] sbuId, Pageable pageable, ModelMap model) {

		Object[] args = new Object[] { storeId,
				type,
				saleOrgId,
				organizationId,
				sbuId };
		Page<Map<String, Object>> page = storeBalanceService.findPage(pageable,
				args);
		String jsonPage = JsonUtils.toJson(page);
		model.addAttribute("organizationId", organizationId);

		return success(jsonPage);
	}

	/*
	 * 月末结账处理
	 */
	@RequestMapping(value = "/balance_account", method = RequestMethod.GET)
	public String balance_account(ModelMap model) {

		List<AccountParameter> accountParameters = accountParameterService.findList(1,
				null,
				null);
		AccountParameter accountParameter = accountParameters.get(0);
		String lastDate = accountParameter.getBalanceDate();
		String currentDate = DateUtil.convert(DateUtil.addDate("MM",
				1,
				DateUtil.convert(lastDate, "yyyy-MM")), "yyyy-MM");
		model.addAttribute("lastDate", lastDate);
		model.addAttribute("currentDate", currentDate);

		return "/finance/balance/balance_account";
	}

	@RequestMapping(value = "/account", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg account(String lastDate, String currentDate, Integer flag) {

		//flag 0月结 1反月结
		storeBalanceService.account(lastDate, currentDate, flag);

		return success();
	}

	@RequestMapping(value = "/get_balance", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg getBalance(Long storeId, Long sbuId, Long organizationId,
			Long saleOrgId) {

		Map<String, Object> map = storeBalanceService.findBalanceSbu(storeId,
				sbuId,
				organizationId,
				saleOrgId);
		return success().addObjX(map);
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(Long storeId, Long saleOrgId, 
			Long[] organizationId, Integer[] type, Long[] sbuId, Pageable pageable) {
		
		Object[] args = new Object[] { storeId, type, saleOrgId, organizationId,
				sbuId };
		Page<Map<String, Object>> page = storeBalanceService.findPage(pageable,
				args);
		Integer size = (int)page.getTotal();
		/*Integer size = storeBalanceService.countForExcel(args);*/
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/*
	 * 条件导出
	 * @param productCategoryId
	 * @param sn
	 * @param vonderCode
	 * @param mod
	 * @param name
	 * @param startTime
	 * @param endTime
	 * @param isMarketable
	 * @param pageable
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(Long storeId, Integer[] type,
			Long saleOrgId, Long[] organizationId, Integer page, Long[] sbuId,
			Pageable pageable, ModelMap model) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		String balanceDate = "";
		List<AccountParameter> accountParameters = accountParameterService.findList(1,null,null);
		if (!accountParameters.isEmpty()) {
			AccountParameter accountParameter = accountParameters.get(0);
			balanceDate = accountParameter.getBalanceDate();
		}
		Object[] args = new Object[] { storeId, type, saleOrgId, organizationId, sbuId };
		/*List<Map<String, Object>> data = storeBalanceService.findList(null,args,page,size);*/
		pageable.setPageNumber(page);
		pageable.setPageSize(size);
		Page<Map<String, Object>> pageMap = storeBalanceService.findPage(pageable,args);
		List<Map<String, Object>> listMap =  pageMap.getContent();
		return this.getModelAndView(listMap, model, balanceDate);
	}

	/*
	 * 选择导出
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, Long[] organizationId,
			Long[] sbuId, ModelMap model) {

		String balanceDate = "";
		List<AccountParameter> accountParameters = accountParameterService.findList(1,null,null);
		if (!accountParameters.isEmpty()) {
			AccountParameter accountParameter = accountParameters.get(0);
			balanceDate = accountParameter.getBalanceDate();
		}

		Object[] args = new Object[] { null, null, null, organizationId, sbuId };
		List<Map<String, Object>> data = storeBalanceService.findList(ids,args,null,null);
		
		return getModelAndView(data, model, balanceDate);
	}

	public ModelAndView getModelAndView(List<Map<String, Object>> data,
			ModelMap model, String balanceDate) {
		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		//设置标题
		for (Map<String, Object> str : data) {
			if (!ConvertUtil.isEmpty(str.get("credit_amount"))) {
				BigDecimal credit_amount = new BigDecimal(str.get("credit_amount").toString());
				str.put("credit_amount",credit_amount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (!ConvertUtil.isEmpty(str.get("deposit_amount"))) {
				BigDecimal deposit_amount = new BigDecimal(str.get("deposit_amount").toString());
				str.put("deposit_amount",deposit_amount.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (!ConvertUtil.isEmpty( str.get("amount_paid"))) {
				BigDecimal amount_paid = new BigDecimal(str.get("amount_paid").toString());
				str.put("amount_paid",amount_paid.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (!ConvertUtil.isEmpty(str.get("balance"))) {
				BigDecimal balance = new BigDecimal(str.get("balance").toString());
				str.put("balance",balance.setScale(2, BigDecimal.ROUND_HALF_UP));
			}
		}
		
		String[] header = { "客户名称",
				"客户编码",
				"经营组织",
				"SBU",
				"授信",
				"销售回款",
				"销售订单",
				"退货",
				"政策录入",
				"可发货余额",
				"机构" };
		
		//设置单元格取值
		String[] properties = { "store_name",
				"out_trade_no",
				"org_name",
				"sbu_name",
				"credit_amount",
				"deposit_amount",
				"amount_paid",
				"return_amount",
				"policyEntry_amount",
				"balance",
				"sale_org" };

		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256, };

		//附加内容
		String[] contents = new String[3];
		contents[0] = "可发货余额";
		contents[1] = "当前月结月份为" + ": " + balanceDate;
		return new ModelAndView(new ExcelView(filename, null, properties,header, widths, null, data, contents), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}
}