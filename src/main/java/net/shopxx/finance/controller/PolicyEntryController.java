package net.shopxx.finance.controller;

import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.dao.impl.NativeDao;
import net.shopxx.base.core.util.*;
import net.shopxx.basic.entity.*;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.PolicyEntryService;
import net.shopxx.finance.service.StoreBalanceService;
import net.shopxx.intf.service.ClientPushFourHundredService;
import net.shopxx.member.entity.*;
import net.shopxx.member.service.*;
import net.shopxx.order.entity.CustomerContract;
import net.shopxx.order.service.CustomerContractService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigBaseService;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

@Controller("financePolicyEntryController")
@RequestMapping("/finance/policyEntry/")
public class PolicyEntryController extends BaseController {

	@Resource(name = "policyEntryServiceImpl")
	private PolicyEntryService policyEntryService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "depositAttachServiceImpl")
	private DepositAttachService depositAttachService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "storeMemberSaleOrgBaseServiceImpl")
	private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeBalanceServiceImpl")
	private StoreBalanceService storeBalanceService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name="customerContractServiceImpl")
	private CustomerContractService customerContractService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	@Resource(name = "aftersaleServiceImpl")
	private AftersaleService aftersaleService;
	@Resource(name = "clientPushFourHundredServiceImpl")
	private ClientPushFourHundredService clientPushFourHundredService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long objid, Long sbuId, Long menuId,
			ModelMap model) {
		model.addAttribute("objid", objid);
		model.addAttribute("sbuId", sbuId);
		model.addAttribute("menuId", menuId);
		return "/finance/policyEntry/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Long sbuId, Long userId, Long menuId, 
			ModelMap model) {
		model.addAttribute("sbuId", sbuId);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);
		return "/finance/policyEntry/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn, Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, Long storeId, String firstTime, String lastTime, BigDecimal minPrice,
			BigDecimal maxPrice, Integer flag, Long organizationId, Long sbuId,Long saleOrgId, Pageable pageable, ModelMap model) {

		// if (!Integer.valueOf(1).equals(flag)) {
		// 非审核页面进入
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.eq("value", "政策录入"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null, filters, null);
		if (rechargeTypes.size() == 0) {
			return this.error("查询失败，充值类型【政策录入】不存在");
		} else if (rechargeTypes.size() > 1) {
			return this.error("查询失败，充值类型【政策录入】存在多个");
		}
		rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };
		// }

		String jsonPage = JsonUtils.toJson(policyEntryService.newfindPage(new Integer[] { 1 }, sn, null, storeId, saleOrgId,
				status, docstatus, rechargeTypeId, creatorId, operatorId, minPrice, maxPrice, firstTime, lastTime,
				organizationId, sbuId, pageable));
		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long sbuId, ModelMap model) {
		// 设置日期格式
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("rechargeTypes", rechargeTypes);

		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		model.addAttribute("companyId", WebUtils.getCurrentCompanyInfoId());
		model.addAttribute("nowDate", df.format(new Date()));
		
		
		/**
		 * 用户经营组织权限
		 */
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		filters.clear();
		filters.add(Filter.eq("storeMember",storeMemberId));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		String value = SystemConfig.getConfig("storeMemberOrganization",companyInfoId);
		int storeMemberOrganization = 0;
		if(userRoles!=null&&userRoles.size()>0){
			String[] perRole = value.split(",");
			List<String> perRoleList = Arrays.asList(perRole);
			for (PcUserRole userRole : userRoles) {
				if (perRoleList.contains(userRole.getPcRole().getName())) {
					storeMemberOrganization++;
					break;
				}
			}
		}
		
		List<Organization> organizationList = null;
		if(storeMemberOrganization==0){
			filters.clear();
			filters.add(Filter.eq("storeMember", storeMemberId));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			organizationList = new ArrayList<Organization>();
			List<StoreMemberOrganization> storeMemberOrganizationList = storeMemberOrganizationService.findList(null,filters,null);
			if(storeMemberOrganizationList!=null&&storeMemberOrganizationList.size()>0){
				for (StoreMemberOrganization storeMemberOrganiZation : storeMemberOrganizationList) {
					if(storeMemberOrganiZation!=null){
						 Organization organization = organizationService.find(storeMemberOrganiZation.getOrganization().getId());
						 if(organization!=null){
							 organizationList.add(organization);
						 }
					}
				}
			}
		}else{
			//组织
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			organizationList = organizationService.findList(null, filters, null);
			
		}
		model.addAttribute("organizations", organizationList);
		
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);
		if (storeMember.getMemberType() != 1) {// 企业用户
			StoreMemberSaleOrg storeMemberSaleOrg = storeMemberSaleOrgService
					.findDefalutSaleOrg(WebUtils.getCurrentStoreMemberId());
			if (storeMemberSaleOrg != null) {
				model.addAttribute("saleOrg", storeMemberSaleOrg.getSaleOrg());
			}
		} else if (storeMember.getMemberType() == 1) {
			NativeDao nativeDao = storeMemberService.getDaoCenter().getNativeDao();
			StringBuilder sql = new StringBuilder();
			sql.append("select s.* from xx_store_member sm, xx_store s");
			sql.append(" where sm.store = s.id");
			sql.append(" and s.is_enabled = 1 and s.is_main_store = 0");
			sql.append(" and sm.username = ?");
			Object[] objs = null;
			if (WebUtils.getCurrentCompanyInfoId() != null) {
				sql.append(" and sm.company_info_id = ?");
				objs = new Object[] { storeMember.getUsername(), WebUtils.getCurrentCompanyInfoId() };
			} else {
				sql.append(" and sm.company_info_id is null");
				objs = new Object[] { storeMember.getUsername() };
			}

			Store store = nativeDao.findSingleManaged(sql.toString(), objs, Store.class);

			model.addAttribute("store", store);
			model.addAttribute("saleOrg", store.getSaleOrg());

		}

		Sbu sbu = sbuService.find(sbuId);
	
		model.addAttribute("sbu", sbu);
		
		if(sbu!=null){
			List<SbuPolicy>  sbuPolicys= sbu.getSbuPolicys();
			List<SystemDict> policyTypes=new ArrayList<SystemDict>();
			if(sbuPolicys!=null){
				for(SbuPolicy sbuPolicy : sbuPolicys){
					SystemDict policyType = sbuPolicy.getPolicyType();
					policyTypes.add(policyType);
				}
			}
			model.addAttribute("policyTypes", policyTypes);
			
			List<SbuInvoice>  sbuInvoices= sbu.getSbuInvoices();
			List<SystemDict> invoiceTypes=new ArrayList<SystemDict>();
			for(SbuInvoice sbuInvoice : sbuInvoices){
				SystemDict invoiceType = sbuInvoice.getInvoiceType();
				invoiceTypes.add(invoiceType);
			}
			model.addAttribute("invoiceTypes", invoiceTypes);
			
			List<SbuCase>  sbuCases= sbu.getSbuCases();
			List<SystemDict> cashProjects=new ArrayList<SystemDict>();
			for(SbuCase sbuCase : sbuCases){
				SystemDict cashProject = sbuCase.getCaseProject();
				cashProjects.add(cashProject);
			}
			model.addAttribute("cashProjects", cashProjects);
			
		}
		
		
		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles); 
		}
		catch (RuntimeException e) {

		}
		
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMemberId));
		filters.add(Filter.eq("companyInfoId", companyInfoId));
		filters.add(Filter.eq("isDefault", true));
		List<StoreMemberOrganization> storeMemberOrganizations=storeMemberOrganizationService.findList(null,filters,null);
		Organization organization =null;
		if(storeMemberOrganizations.size()>0) {
			organization=storeMemberOrganizations.get(0).getOrganization();
		}
		model.addAttribute("oz", organization);
		
		return "/finance/policyEntry/add";
	}

    public void commonSave(DepositRecharge depositRecharge, Long saleOrgId, Long storeId,
						   Long organizationId, Long rechargeTypeId, Long policyTypeId, Long invoiceTypeId, Long cashProjectId,
						   Long sbuId,Long regionalManagerId,Long contractId){
		if(regionalManagerId!=null){   //区域经理
			StoreMember regionalManager = storeMemberService.find(regionalManagerId);
			depositRecharge.setRegionalManager(regionalManager);
		}
		Store store = storeService.find(storeId);
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		if (store == null) {
			// 操作错误，客户不存在
			ExceptionUtil.throwServiceException("客户不存在");
		}
		if(contractId!=null){
			CustomerContract customerContract=customerContractService.find(contractId);
			depositRecharge.setCustomerContract(customerContract);
		}
		Organization organization = organizationService.find(organizationId);
		depositRecharge.setOrganization(organization);
		depositRecharge.setSaleOrg(saleOrgService.find(saleOrgId));
		depositRecharge.setPolicyType(systemDictService.find(policyTypeId));
		depositRecharge.setCashProject(systemDictService.find(cashProjectId));
		depositRecharge.setSbu(sbuService.find(sbuId));
		if(depositRecharge.getTaxRate()==null){
			ExceptionUtil.throwServiceException("请选择税率");
		}
		if(policyTypeId==null){
			ExceptionUtil.throwServiceException("请选择政策类型");
		}
		if (depositRecharge.getTaxRate().compareTo(new BigDecimal("0")) != 0 && depositRecharge.getHasInvoice() == 1) {
			ExceptionUtil.throwServiceException("有发票税率类型必须为0");
		}
		Sbu sbu = sbuService.find(sbuId);
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		if (sbus.size() == 0 || sbus == null) {
			ExceptionUtil.throwServiceException("当前用户没有维护"+sbu.getName()+"类型SBU");
		}
		policyEntryService.save(store, depositRecharge, 5, rechargeTypeId,0,invoiceTypeId);
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(DepositRecharge depositRecharge, Long saleOrgId, Long storeId,
			Long organizationId, Long rechargeTypeId, Long policyTypeId, Long invoiceTypeId, Long cashProjectId,
			Long sbuId,Long regionalManagerId,Long contractId) {
		commonSave(depositRecharge, saleOrgId, storeId, organizationId, rechargeTypeId, policyTypeId, invoiceTypeId, cashProjectId, sbuId, regionalManagerId, contractId);
		return success().addObjX(depositRecharge.getId());
	}

	@RequestMapping(value = "/view", method = RequestMethod.GET)
	public String view(Long id, ModelMap model) {

		DepositRecharge depositRecharge = policyEntryService.find(id);
		model.addAttribute("dr", depositRecharge);
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("storeMember", storeMember);

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null, filters, null);
		model.addAttribute("rechargeTypes", rechargeTypes);

		Sbu sbu = depositRecharge.getSbu();
		List<SbuPolicy>  sbuPolicys= sbu.getSbuPolicys();
		List<SystemDict> policyTypes=new ArrayList<SystemDict>();
		for(SbuPolicy sbuPolicy : sbuPolicys){
			SystemDict policyType = sbuPolicy.getPolicyType();
			policyTypes.add(policyType);
		}
		
		List<SbuInvoice>  sbuInvoices= sbu.getSbuInvoices();
		List<SystemDict> invoiceTypes=new ArrayList<SystemDict>();
		for(SbuInvoice sbuInvoice : sbuInvoices){
			SystemDict invoiceType = sbuInvoice.getInvoiceType();
			invoiceTypes.add(invoiceType);
		}
		
		List<SbuCase>  sbuCases= sbu.getSbuCases();
		List<SystemDict> cashProjects=new ArrayList<SystemDict>();
		for(SbuCase sbuCase : sbuCases){
			SystemDict cashProject = sbuCase.getCaseProject();
			cashProjects.add(cashProject);
		}
		
		model.addAttribute("policyTypes", policyTypes);
		model.addAttribute("invoiceTypes", invoiceTypes);
		model.addAttribute("cashProjects", cashProjects);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(depositRecharge.getSn(), 3));
		model.addAttribute("payment_json", payment_json);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		/** 附件 */
		String depositAttach_json = JsonUtils.toJson(depositAttachService.findListByDepositRechargeId(id));
		model.addAttribute("depositAttach_json", depositAttach_json);
		/** 全链路 */
		String fullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByElseSnAndType(depositRecharge.getSn(), 5));
		model.addAttribute("fullLink_json", fullLink_json);
		
		Map<String,Object> map = storeBalanceService.findBalanceSbu(depositRecharge.getStore().getId(), depositRecharge.getSbu().getId(), depositRecharge.getOrganization().getId(), depositRecharge.getSaleOrg().getId());

		Integer balance=0;
		if (map !=null && map.get("balance") != null) {
			model.addAttribute("balance", map.get("balance"));
			
		}else{
			model.addAttribute("balance", balance);
		}
		
		filters.clear();
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null,filters,null);
		// 合同权限
		try {
			String values = SystemConfig.getConfig("contractRoles",
					WebUtils.getCurrentCompanyInfoId());
			String[] perRole = values.split(",");
			List<String> list = Arrays.asList(perRole);
			int contractRoles = 0;
			for (PcUserRole userRole : userRoles) {
				if (list.contains(userRole.getPcRole().getName())) {
					contractRoles++;
					break;
				}
			}
			model.addAttribute("contractRoles", contractRoles); 
		}catch (RuntimeException e) {

		}
		

		return "/finance/policyEntry/view";
	}

	@RequestMapping(value = "/viewSn", method = RequestMethod.GET)
	public String viewSn(String sn, Integer isAdd, Integer type, Long objTypeId, ModelMap model) {

		List<Filter> fis = new ArrayList<Filter>();
		fis.add(Filter.eq("sn", sn));
		DepositRecharge depositRecharge = policyEntryService.find(fis);
		model.addAttribute("dr", depositRecharge);
		model.addAttribute("isAdd", isAdd);
		model.addAttribute("type", type);
		/** 付款单 */
		String payment_json = JsonUtils.toJson(paymentService.findListByElseSn(sn, 3));
		model.addAttribute("payment_json", payment_json);

		/** 附件 */
		String depositAttach_json = JsonUtils
				.toJson(depositAttachService.findListByDepositRechargeId(depositRecharge.getId()));
		model.addAttribute("depositAttach_json", depositAttach_json);

		/** 全链路 */
		String fullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByElseSnAndType(depositRecharge.getSn(), 5));
		model.addAttribute("fullLink_json", fullLink_json);

		boolean isCheckWf = wfObjConfigBaseService.isCheckWf(43L);
		model.addAttribute("isCheckWf", isCheckWf);
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());

		return "/finance/policyEntry/view";
	}

	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check(Long id, Integer flag, String note, BigDecimal actualAmount, ModelMap model) {

		DepositRecharge depositRecharge = policyEntryService.find(id);
		if (depositRecharge.getDocStatus() != 1) {
			return error("只有单据状态为已提交的申请单才能审批");
		}
		if (actualAmount == null || actualAmount.compareTo(BigDecimal.ZERO) == 0) {
			// 实际充值金额不能为空
			return error("16405");
		}
		BigDecimal amount = depositRecharge.getAmount();
		if (amount.compareTo(BigDecimal.ZERO) == 1) {

			if (actualAmount.compareTo(depositRecharge.getAmount()) == 1) {
				// 实际充值金额不能大于计划充值金额
				return error("16404");
			}
		} else {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == -1
					|| actualAmount.compareTo(BigDecimal.ZERO) == 1) {
				//
				return error("16406");
			}
		}
		policyEntryService.check(depositRecharge, flag, note, actualAmount);
		return success();
	}

	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String glDate, 
			Integer flag, String note, BigDecimal actualAmount,Long objConfId, 
			ModelMap model) {

		DepositRecharge depositRecharge = policyEntryService.find(id);
		if (depositRecharge.getDocStatus() != 1) {
			return error("只有单据状态为已提交的政策单才能审批流程");
		}
		if (!ConvertUtil.isEmpty(glDate)) {
			depositRecharge.setGlDate(DateUtil.convert(glDate, "yyyy-MM-dd"));
		}
		if (actualAmount == null || actualAmount.compareTo(BigDecimal.ZERO) == 0) {
			// 实际充值金额不能为空
			return error("16405");
		}
		BigDecimal amount = depositRecharge.getAmount();
		if (amount.compareTo(BigDecimal.ZERO) == 1) {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == 1) {
				// 实际充值金额不能大于计划充值金额
				return error("16404");
			}
		} else {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == -1
					|| actualAmount.compareTo(BigDecimal.ZERO) == 1) {
				return error("16406");
			}
		}
		policyEntryService.checkStoreRechargeWf(depositRecharge, note, actualAmount, objConfId);
		return success();
	}

	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancel(Long id, String note, ModelMap model) {

		DepositRecharge depositRecharge = policyEntryService.find(id);
		//校验政策单状态
		policyEntryService.checkDepositRechargeStatus(depositRecharge,1,"已提交","作废");
		policyEntryService.cancel(depositRecharge, 5);
		return success();
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/updata", method = RequestMethod.POST)
	public @ResponseBody ResultMsg updata(DepositRecharge depositRecharge,
			Long storeId, Long saleOrgId, Long organizationId, Long rechargeTypeId,Long invoiceTypeId,
			Integer isSubmit, BigDecimal actualAmount, Long sbuId, Long regionalManagerId,Long policyTypeId,
			Long contractId) {
		
		if(!ConvertUtil.isEmpty(regionalManagerId)){
			StoreMember regionalManager = storeMemberService.find(regionalManagerId);
			depositRecharge.setRegionalManager(regionalManager);
		}
		
		Store store = storeService.find(storeId);
		StoreMember storeMember = storeMemberService.getCurrent();
		List<Filter> filters = new ArrayList<Filter>();
		if (store == null) {
			// 操作错误，客户不存在
			return error("16400");
		}
		SaleOrg saleOrg = saleOrgService.find(saleOrgId);
		depositRecharge.setSaleOrg(saleOrg);
		
		if (!ConvertUtil.isEmpty(actualAmount)) {
			depositRecharge.setActualAmount(actualAmount);
		}
		
		if(!ConvertUtil.isEmpty(contractId)){
			CustomerContract customerContract=customerContractService.find(contractId);
			depositRecharge.setCustomerContract(customerContract);
		}

		Sbu sbu = sbuService.find(sbuId);
		filters.clear();
		filters.add(Filter.eq("storeMember", storeMember));
		filters.add(Filter.eq("sbu", sbu));
		List<StoreMemberSbu> sbus = storeMemberSbuService.findList(null, filters, null);
		if (sbus.size() == 0 || sbus == null) {
			return error("该客户没有维护此类型SBU");
		}
		depositRecharge.setSbu(sbuService.find(sbuId));
		depositRecharge.setOrganization(organizationService.find(organizationId));
		depositRecharge.setInvoiceType(systemDictService.find(invoiceTypeId));
		depositRecharge.setPolicyType(systemDictService.find(policyTypeId));
	
		policyEntryService.update(store, depositRecharge, 5, isSubmit);

		return success().addObjX(depositRecharge.getId());
	}

	/**
	 * 模拟支付
	 */
	@RequestMapping(value = "/submit", method = RequestMethod.POST)
	public String submit(HttpServletRequest request, BigDecimal payamount, String payname, HttpServletResponse response,
			ModelMap model) {

		model.addAttribute("requestUrl", "https://mapi.alipay.com/gateway.do");
		model.addAttribute("requestMethod", "get");
		model.addAttribute("requestCharset", "UTF-8");
		Map<String, Object> data = new HashMap<String, Object>();
		data = getParameterMap(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()),
				request.getParameter("payname"), request);
		model.addAttribute("parameterMap", data);
		return "/finance/policyEntry/submit";

	}

	public Map<String, Object> getParameterMap(String sn, String description, HttpServletRequest request) {

		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("service", "create_direct_pay_by_user");
		parameterMap.put("partner", "2088211535838965");// 支付宝商户号
		parameterMap.put("_input_charset", "utf-8");
		parameterMap.put("sign_type", "MD5");
		parameterMap.put("return_url", "http://mk.5mall.com");// 支付成功同步回调地址
		parameterMap.put("notify_url", "http://mk.5mall.com");// 支付成功异步回调地址
		parameterMap.put("out_trade_no", sn);// 支付单号
		parameterMap.put("subject",
				StringUtils.abbreviate(description.replaceAll("[^0-9a-zA-Z\\u4e00-\\u9fa5 ]", ""), 60));
		parameterMap.put("body",
				StringUtils.abbreviate(description.replaceAll("[^0-9a-zA-Z\\u4e00-\\u9fa5 ]", ""), 600));
		parameterMap.put("payment_type", "1");
		parameterMap.put("seller_id", "2088211535838965");// 支付宝商户号
		parameterMap.put("total_fee", request.getParameter("payamount"));// 支付金额
		parameterMap.put("show_url", "http://mk.5mall.com");// 网站地址
		parameterMap.put("paymethod", "directPay");
		parameterMap.put("exter_invoke_ip", request.getLocalAddr());// 请求ip
		parameterMap.put("extra_common_param", "twkj remark");// 备注

		try {
			parameterMap.put("sign", generateSign(parameterMap));
		} catch (Exception e) {
			System.out.print("---11----" + e.getMessage());
		}

		return parameterMap;
	}

	private String generateSign(Map<String, ?> parameterMap) {

		return DigestUtils.md5Hex(joinKeyValue(new TreeMap<String, Object>(parameterMap), null,
				"********************************", "&", true, "sign_type", "sign"));
	}

	protected String joinKeyValue(Map<String, Object> map, String prefix, String suffix, String separator,
			boolean ignoreEmptyValue, String... ignoreKeys) {
		List<String> list = new ArrayList<String>();
		if (map != null) {
			for (Entry<String, Object> entry : map.entrySet()) {
				String key = entry.getKey();
				String value = ConvertUtils.convert(entry.getValue());
				if (StringUtils.isNotEmpty(key) && !ArrayUtils.contains(ignoreKeys, key)
						&& (!ignoreEmptyValue || StringUtils.isNotEmpty(value))) {
					list.add(key + "=" + (value != null ? value : ""));
				}
			}
		}
		return (prefix != null ? prefix : "") + StringUtils.join(list, separator) + (suffix != null ? suffix : "");
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> countDepositRecharge(String sn, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId, Long operatorId, Long storeId, String firstTime,
			String lastTime, BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable, Long[] ids, Long sbuId,
			ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.eq("value", "政策录入"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null, filters, null);
		rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };

		Integer size = policyEntryService.countDepositRecharge(new Integer[] { 1 }, sn, null, storeId, null, status,
				docstatus, rechargeTypeId, creatorId, operatorId, minPrice, maxPrice, firstTime, lastTime, sbuId, ids,null,
				pageable, null, null);

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView findDepositRechargeList(String sn, Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, Long storeId, String firstTime, String lastTime, BigDecimal minPrice,
			BigDecimal maxPrice, Long sbuId, Pageable pageable, Integer page, ModelMap model) {

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("code", "DepositRechargeType"));
		filters.add(Filter.eq("value", "政策录入"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> rechargeTypes = systemDictService.findList(null, filters, null);
		rechargeTypeId = new Long[] { rechargeTypes.get(0).getId() };

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = policyEntryService.findDepositRechargeList(new Integer[] { 1 }, sn, null,
				storeId, null, status, docstatus, rechargeTypeId, creatorId, operatorId, minPrice, maxPrice, firstTime,
				lastTime, sbuId, null,null, pageable, page, size);
		return getModelAndViewForList(data, model);
	}

	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView findDepositRechargeList(Long[] ids, ModelMap model) {
		List<Map<String, Object>> data = policyEntryService.findDepositRechargeList(new Integer[] { 1 }, null, null,
				null, null, null, null, null, null, null, null, null, null, null, null, ids,null, null, null, null);
		return getModelAndViewForList(data, model);
	}

	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("amount") != null) {
				str.put("amount", new BigDecimal(str.get("amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("actual_amount") != null) {
				str.put("actual_amount", new BigDecimal(str.get("actual_amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP));
			}
			if (str.get("doc_status") != null) {
				if ((Integer) str.get("doc_status") == 0) {
					str.put("doc_status", "已保存");
				} else if ((Integer) str.get("doc_status") == 1) {
					str.put("doc_status", "	已提交");
				} else if ((Integer) str.get("doc_status") == 2) {
					str.put("doc_status", "已处理");
				} else if ((Integer) str.get("doc_status") == 3) {
					str.put("doc_status", "已关闭");
				} else {
					str.put("doc_status", "-");
				}
			}
			if (str.get("wf_state") != null) {
				if ((Integer) str.get("wf_state") == 0) {
					str.put("wf_state", "未启动");
				} else if ((Integer) str.get("wf_state") == 1) {
					str.put("wf_state", "审核中");
				} else if ((Integer) str.get("wf_state") == 2) {
					str.put("wf_state", "已完成");
				} else if ((Integer) str.get("wf_state") == 3) {
					str.put("wf_state", "驳回");
				} else if ((Integer) str.get("wf_state") == 4) {
					str.put("wf_state", "中断");
				} else if ((Integer) str.get("wf_state") == 5) {
					str.put("wf_state", "禁用");
				} else {
					str.put("wf_state", "-");
				}
			}
			if (str.get("create_date") != null) {
				String check_date = str.get("create_date").toString();
				str.put("create_date", check_date.substring(0, 19));
			}
			if (str.get("apply_date") != null) {
				String check_date = str.get("apply_date").toString();
				str.put("apply_date", check_date.substring(0, 19));
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "余额充值编号", "充值客户", "客户简称", "客户编码", "充值金额", "实际充值金额", "充值类型", "经营组织", "单据状态", "流程状态", "申请日期","GL日期", "创建日期",
				"创建人","放行人", "申请备注", "SBU" };

		// 设置单元格取值
		String[] properties = { "sn", "store_name", "store_alias", "store_sn", "amount", "actual_amount", "recharge_type_value",
				"organization_name", "doc_status", "wf_state", "apply_date","gl_date","create_date", "creator_name","permit_through_name", "memo",
				"sbu_name" };
		// 设置列宽
		Integer[] widths = { 25 * 256,25 * 256,25 * 256,25 * 256,25 * 256,25 * 256,25 * 256,25 * 256,25 * 256,
				25 * 256,25 * 256,25 * 256,25 * 256,25 * 256 ,25 * 256,25 * 256,25 * 256  };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);

	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	/**
	 * 模板导入
	 */
	@RequestMapping(value = "/import_template", method = RequestMethod.POST)
	public @ResponseBody ResultMsg importTemplate(MultipartFile file, HttpServletResponse response, ModelMap model)
			throws Exception {
		try {
			policyEntryService.policyAddImport(file, null);
			return ResultMsg.success();
		} catch (Exception e) {
			LogUtils.error("导入政策录入", e);
			return ResultMsg.error(e.getMessage());
		}
	}

	@RequestMapping(value = "/checkStoreRecharge", method = RequestMethod.POST)
	public @ResponseBody ResultMsg checkStoreRecharge(Long id, Integer flag, String note, String glDate,
			BigDecimal actualAmount, ModelMap model) throws Exception {

		DepositRecharge depositRecharge = policyEntryService.find(id);
		//校验政策单状态
		policyEntryService.checkDepositRechargeStatus(depositRecharge,1,"已提交","审核");
		if (!ConvertUtil.isEmpty(glDate)) {
			depositRecharge.setGlDate(DateUtil.convert(glDate, "yyyy-MM-dd"));
		}
		if (actualAmount == null || actualAmount.compareTo(BigDecimal.ZERO) == 0) {
			// 实际充值金额不能为空
			return error("16405");
		}
		BigDecimal amount = depositRecharge.getAmount();
		if (amount.compareTo(BigDecimal.ZERO) == 1) {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == 1) {
				// 实际充值金额不能大于计划充值金额
				return error("16404");
			}
		} else {
			if (actualAmount.compareTo(depositRecharge.getAmount()) == -1 || actualAmount.compareTo(BigDecimal.ZERO) == 1) {
				return error("16406");
			}
		}
		policyEntryService.checkStoreRecharge(depositRecharge, note, actualAmount);
		Aftersale aftersale = depositRecharge.getAftersale();
		if (aftersale !=null ){
			aftersale.setIsReceived(true);
			aftersale.setReceiveAmount(depositRecharge.getAmount());
			aftersale.setReceiveDate(new Date());
			aftersale.setDepositRechargeSn(depositRecharge.getSn());
			aftersaleService.update(aftersale);
			//推送政策信息给CSS
			clientPushFourHundredService.pushAftersale(aftersale);
		}
		return success();
	}
}