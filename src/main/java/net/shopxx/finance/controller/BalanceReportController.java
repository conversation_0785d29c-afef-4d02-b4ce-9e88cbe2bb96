package net.shopxx.finance.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.StoreDepositService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.template.tempUtil.MenuJumpUtils;
import net.shopxx.util.CommonUtil;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/*
 * Controller - 客户余额报表
 */
@Controller("balanceReportController")
@RequestMapping("/finance/balance_report")
public class BalanceReportController extends BaseController {

	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeDepositServiceImpl")
	private StoreDepositService storeDepositService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;
	
	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code,Long menuId,
			ModelMap model, Integer flag) {

		model.addAttribute("flag", flag);
		model.addAttribute("code", code);
		model.addAttribute("menuId", menuId);
		return CommonUtil.getFolderPrefix(code) 
				+ "/finance/balance_report/list_tb";
	}
	
	
	/*
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Long userId, 
			Long menuId, ModelMap model) {
		//获取ModelMap
	    menuJumpUtils.getModelMap(model, userId, menuId);
		model.addAttribute("code", code);

		return CommonUtil.getFolderPrefix(code)
				+ "/finance/balance_report/list";
	}

	/*
	 * 列表
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(Long saleOrgId, Long storeId, String storeCode,
			Long[] sbuId, Long[] organizationId, String firstTime, String lastTime,
			Pageable pageable, ModelMap model) {

		Page<Map<String, Object>> page = storeDepositService.findPage(saleOrgId,
				storeId,
				storeCode,
				sbuId,
				organizationId,
				firstTime,
				lastTime,
				pageable);

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(Long saleOrgId, Long storeId,
			String storeCode, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime) {

		Integer size = storeDepositService.findTableNew(saleOrgId,
				storeId,
				storeCode,
				sbuId,
				organizationId,
				firstTime,
				lastTime).size();

		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(Long saleOrgId, Long storeId,
			String storeCode, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime, ModelMap model) {

		List<Map<String, Object>> data = storeDepositService.findTableNew(saleOrgId,
				storeId,
				storeCode,
				sbuId,
				organizationId,
				firstTime,
				lastTime);
		return getModelAndViewForList(data, model);
	}
	
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selected_export(String[] ids, ModelMap model){
		List<Map<String, Object>> data = storeDepositService.findTable(null, null, null ,
				null, null,null,null ,ids);
		return getModelAndViewForList(data, model);
		
		
	}

	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {

		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		//设置标题
		String[] header = { "机构",
				"客户名称",
				"客户编码",
				"SBU",
				"经营组织",
				"期初应收",
				"期间发货",
				"期间回款",
				"期末应收",
				"占用金额",
				"可发货余额",
				"授信"};
		//设置单元格取值
		String[] properties = {"sale_org",
				"store",
				"store_code",
				"sbu",
				"organization",
				"qcys",
				"qjfh",
				"qjhk",
				"qmys",
				"zyje",
				"balance",
				"sx"};
		//设置列宽
		Integer[] widths = { 25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256,
				25 * 256 };
		ModelAndView a = new ModelAndView(new ExcelView(filename, null,
				properties, header, widths, null, data, null), model);
		return a;
	}
	
}
