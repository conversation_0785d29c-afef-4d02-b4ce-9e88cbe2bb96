package net.shopxx.finance.controller;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.template.tempUtil.MenuJumpUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.service.StoreDepositService;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberOrganization;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberOrganizationService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.util.CommonUtil;

@Controller("differenceControlle")
@RequestMapping("/finance/difference")
public class DifferenceControlle extends BaseController{
	
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "storeDepositServiceImpl")
	private StoreDepositService storeDepositService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberOrganizationServiceImpl")
	private StoreMemberOrganizationService storeMemberOrganizationService;
	@Resource(name = "menuJumpUtils")
	private MenuJumpUtils menuJumpUtils;

	@RequestMapping(value = "/list_tb/{code}", method = RequestMethod.GET)
	public String list_tb(@PathVariable String code, Pageable pageable,Long menuId,
			ModelMap model, Integer flag) {

		model.addAttribute("flag", flag);
		model.addAttribute("code", code);
		model.addAttribute("menuId", menuId);
		return CommonUtil.getFolderPrefix(code)
				+ "/finance/difference/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list/{code}", method = RequestMethod.GET)
	public String list(@PathVariable String code, Pageable pageable,Long userId,Long menuId, String firstTime, String lastTime,
			ModelMap model) {
        // 开始时间
        model.addAttribute("firstTime", firstTime);
        // 结束时间
        model.addAttribute("lastTime", lastTime);
		/**
		 * 用户经营组织权限
		 */
		//sbu
		StoreMember storeMember = storeMemberService.getCurrent();
		model.addAttribute("code", code);
		Integer isMember = 0;
		isMember = storeMember.getMemberType();
		model.addAttribute("isMember",isMember);
		
		// 判断是否拥有角色
		boolean role = storeService.findByRole(storeMember, "对账报表查看");
		Integer seeRole=0;
		if(role){
			seeRole=1;
		}
		model.addAttribute("seeRole",seeRole);
		//获取ModelMap
		menuJumpUtils.getModelMap(model, userId, menuId);

		return CommonUtil.getFolderPrefix(code)
				+ "/finance/difference/list";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg list_data(Long storeId, Long saleOrgId, Long sbuId,
			Long organizationId, String firstTime, String lastTime,
			Pageable pageable, ModelMap model) {
		
//		if(saleOrgId==null&&storeId==null){
//			return error("请选择客户");
//		}
//		if(organizationName==null){
//			return error("请输入组织");
//		}
//		if(sbuName==null){
//			return error("请输入SBU");
//		}

		Page<Map<String, Object>> page = storeDepositService.findPageDifference(storeId,
				sbuId,
				organizationId,
				saleOrgId,
				firstTime,
				lastTime,
				pageable);

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody
	List<Map<String, Object>> toConditionExport(Long storeId,
			Long sbuId, Long organizationId,Long saleOrgId,
			String firstTime, String lastTime) {

		Integer size = 
			storeDepositService.findTableDifference(storeId, sbuId, organizationId, saleOrgId, firstTime, lastTime, null)
			==null?0:
				storeDepositService.findTableDifference(storeId, sbuId, organizationId, saleOrgId, firstTime, lastTime, null).size();
		
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			}
			else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			}
			else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig",
					WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		}
		catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(Long storeId, String[] type,
			Long sbuName, Long organizationName,Long saleOrgId, String firstTime,
			String lastTime, ModelMap model) {

		List<Map<String, Object>> data = storeDepositService.findTableDifference(storeId,
				sbuName,
				organizationName,
				saleOrgId,
				firstTime,
				lastTime,
				null);
		return getModelAndViewForList(data, model);
	}

	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,
			ModelMap model) {

		//设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date())
				+ ".xls";
		
		for (Map<String, Object> str : data) {
			BigDecimal qmsx = BigDecimal.ZERO ;
			BigDecimal qmye = BigDecimal.ZERO;
			BigDecimal zyje = BigDecimal.ZERO;
			if (str.get("qmsx") != null) {
				qmsx = new BigDecimal(str.get("zyje")
						.toString());
			}
			if (str.get("qmye") != null) {
				qmye = new BigDecimal(str.get("zyje")
						.toString());
			}
			if (str.get("zyje") != null) {
				 zyje = new BigDecimal(str.get("zyje")
						.toString());
			}
			BigDecimal zb_fhye=qmsx.add(zyje).add(qmye);
			str.put("zb_fhye",
					NumberFormat.getInstance().format(zb_fhye));
			
		}
		
		// 判断是否拥有角色
		StoreMember storeMember = storeMemberService.getCurrent();
		boolean role = storeService.findByRole(storeMember, "对账报表查看");
		ModelAndView a = null;
		//设置标题
		if(role){
			String[] header = { "机构",
					"客户名称",
					"客户编码",
					"SBU",
					"经营组织",
					"期初余额",
					"订单分销价金额",
					"订单结算价金额",
					"平台毛利",
					"经销商总付款",
					"总部收款",
					"平台收款",
					"期末余额",
					"占用金额",
					"期末授信",
					"期末订单",
					"期末发货",
					"期末退货",
					"期末订单结算",
					"期末发货结算",
					"期末退货结算",
					"期末总部收款",
					"期末平台收款",
					"期初授信",
					"期初订单",
					"期初发货",
					"期初退货",
					"期初订单结算",
					"期初发货结算",
					"期初退货结算",
					"期初经销商总付款",
					"期初总部收款",
					"期初平台收款",
					"总部账户可发货余额"	};
			//设置单元格取值
			String[] properties = { "saleOrg",
					"store",
					"store_code",
					"sbu",
					"organization",
					"qcye",
					"order_fxje",
					"order_jsje",
					"ptml",
					"store_fk",
					"qmye",
					"zyje",
					"qmsx",
					"qm_order",
					"qm_shipping",
					"qm_returns",
					"qm_org_amount",
					"qm_shipping_amount",
					"qm_returns_amount",
					"qm_store_fk",
					"qm_zbsk",
					"qm_ptsk",
					"qc_credit",
					"qc_order",
					"qc_shipping",
					"qc_returns",
					"qc_order_amount",
					"qc_org_amount",
					"qc_returns_amount",
					"qc_store_fk",
					"qc_zbsk",
					"qc_ptsk",
					"zb_fhye"};
			//设置列宽
			Integer[] widths = { 25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256};
			 a = new ModelAndView(new ExcelView(filename, null,
					properties, header, widths, null, data, null), model);
		}else{
			String[] header = { "机构",
					"客户名称",
					"客户编码",
					"SBU",
					"经营组织",
					"期初余额",
					"订单结算价金额",
					"经销商总付款",
					"期末余额",
					"占用金额",
					"期末授信",
					"总部账户可发货余额"};
			//设置单元格取值
			String[] properties = { "saleOrg",
					"store",
					"store_code",
					"sbu",
					"organization",
					"qcye",
					"order_jsje",
					"store_fk",
					"qmye",
					"zyje",
					"qc_credit",
					"zb_fhye"};
			//设置列宽
			Integer[] widths = { 25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256,
					25 * 256};
			 a = new ModelAndView(new ExcelView(filename, null,
					properties, header, widths, null, data, null), model);
		}
		
		
		return a;

	}


}
