package net.shopxx.finance.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

import org.springframework.stereotype.Repository;

@Repository("accountStatementDao")
public class AccountStatementDao extends DaoCenter {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	public Page<Map<String, Object>> findPage(String month, Long storeId,
			Long saleOrgId, Pageable pageable) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();

		sql.append("select s.id store_id,s.out_trade_no store_out_trade_no,s.name store_name,so.name sale_org_name,"
				+ "ifnull(dr.deposit_amount,0) deposit_amount,ifnull(o.amount_paid,0) amount_paid, ? month"
				+ " from xx_store s"
				+ " left join xx_sale_org so on s.sale_org=so.id");
		list.add(month);
		//销售回款
		sql.append(" left join");
		sql.append("  (select a.stores, sum(ifnull(a.actual_amount, 0)) deposit_amount");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b");
		sql.append("  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id");
		sql.append("  and b.is_enabled = 1");
		sql.append("  and a.balance_month = ?");
		sql.append("  group by a.stores");
		sql.append("  ) dr on dr.stores = s.id");
		list.add(month);

		//已下达订单
		sql.append(" left join");
		sql.append("  (select a.stores, sum(b.zhekou_price * b.quantity) amount_paid");
		sql.append("  from xx_order a, xx_order_item b");
		sql.append("  where a.id = b.orders and a.order_status = 6 and a.order_type = 2");
		sql.append("  and date_format(a.create_date,'%Y-%m') = ?");
		sql.append("  group by a.stores");
		sql.append("  ) o on o.stores = s.id");
		list.add(month);

		sql.append(" where 1=1"
				+ " and s.company_info_id = ?"
				+ " and s.is_main_store = 0");
		list.add(companyInfoId);
		
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		StoreMember storeMember = storeMemberBaseService.find(storeMemberId);
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and s.id in (" + storeAuth + ")");
			}
		} else {
			sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(storeMemberId);
			list.add(storeMemberId);
		}

		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}

		if (saleOrgId != null) {
			sql.append(" and s.sale_org = ?");
			list.add(saleOrgId);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return page;

	}

	public Page<Map<String, Object>> findLinePage(String month, Long storeId,
			String sn, Integer type, Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();

		sql.append("select p.*,s.name store_name,? month from ("
				+ " select a.stores,a.sn,a.create_date, a.actual_amount,0 type"
				+ " from xx_deposit_recharge a, xx_system_dict b"
				+ " where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id"
				+ " and b.is_enabled = 1"
				+ " and a.balance_month = ?"
				+ " and a.stores=?"
				+ " union"
				+ " select a.stores,a.sn, a.create_date,sum(b.zhekou_price * b.quantity) actual_amount,1 type"
				+ " from xx_order a, xx_order_item b"
				+ " where a.id = b.orders and a.order_status = 6 and a.order_type = 2"
				+ " and date_format(a.create_date,'%Y-%m') = ?"
				+ " and a.stores=?"
				+ " group by b.orders"
				+ " ) p ,"
				+ " xx_store s"
				+ " where s.id=p.stores");

		list.add(month);
		list.add(month);
		list.add(storeId);
		list.add(month);
		list.add(storeId);
		
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and p.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(type)) {
			sql.append(" and p.type=?");
			list.add(type);
		}

		sql.append(" order by p.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return page;

	}
}
