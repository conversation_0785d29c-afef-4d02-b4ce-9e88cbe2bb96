package net.shopxx.finance.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

@Repository("paymentDao")
public class PaymentDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;

	/**
	 * 支付单列表数据
	 */
	public Page<Map<String, Object>> findPage(String sn, String unifiedSn,
			String tradeNo, Long orderId, String orderSn, Integer[] status,
			Integer[] method, Long storeId, String firstTime, String lastTime,
			boolean isAdmin, Pageable pageable) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();

		StringBuilder sql = new StringBuilder();
		sql.append("select p.*,sm.username,sm1.username operate_username,o.sn as order_sn,o.id as order_id,s.name as store_name");
		sql.append(" from xx_payment p");
		sql.append(" left join xx_order o on p.orders=o.id");
		sql.append(" left join xx_store_member sm on p.store_member=sm.id");
		sql.append(" left join xx_store_member sm1 on p.operate_store_member=sm1.id");
		sql.append(" left join xx_store s on p.stores=s.id");
		sql.append("  where 1=1");

		if (companyInfoId != null) {
			sql.append(" and p.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and p.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(tradeNo)) {
			sql.append(" and p.trade_no like ?");
			list.add("%" + tradeNo + "%");
		}
		if (!ConvertUtil.isEmpty(unifiedSn)) {
			sql.append(" and p.unified_sn like ?");
			list.add("%" + unifiedSn + "%");
		}
		if (orderId != null) {
			sql.append(" and o.id = ?");
			list.add(orderId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql.append(" and o.sn like ?");
			list.add("%" + orderSn + "%");
		}
		if (status != null && status.length > 0) {
			String os = "";
			for (int i = 0; i < status.length; i++) {
				if (i == status.length - 1)
					os += status[i];
				else
					os += status[i] + ",";
			}
			sql.append(" and p.status in (" + os + ")");
		}
		if (method != null && method.length > 0) {
			String os = "";
			for (int i = 0; i < method.length; i++) {
				if (i == method.length - 1)
					os += method[i];
				else
					os += method[i] + ",";
			}
			sql.append(" and p.method in (" + os + ")");
		}
		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (!isAdmin) {
			sql.append(" and p.store_member = ? or p.stores in (select s.id from xx_store_member sm, xx_store s where sm.store = s.id and s.is_main_store = 0 and sm.member = ?)");
			StoreMember storeMember = storeMemberService.getCurrent();
			list.add(storeMember.getId());
			list.add(storeMember.getMember().getId());
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and p.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and p.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		sql.append(" order by p.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> listItems = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
		return listItems;
	}

	/**
	 * 根据明细(订单 或 余额充值 或 临时额度充值)id查找对应的细付款信息
	 */
	public List<Map<String, Object>> findListByDetailedId(Long orderId,
			Long depositRechargeId, Long creditRechargeId) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_payment where");
		if (orderId != null) {
			sql.append(" orders=?");
			list.add(orderId);
		}
		if (depositRechargeId != null) {
			sql.append(" deposit_recharge=?");
			list.add(depositRechargeId);
		}
		if (creditRechargeId != null) {
			sql.append(" credit_recharge=?");
			list.add(creditRechargeId);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	public List<Map<String, Object>> findListByElseSn(String sn, Integer type) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("select * from xx_payment where 1=1");
		if (sn != null) {
			sql.append(" and else_sn=?");
			list.add(sn);
		}
		if (type != null) {
			sql.append(" and type=?");
			list.add(type);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
}