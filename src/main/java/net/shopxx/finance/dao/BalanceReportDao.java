package net.shopxx.finance.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.util.RoleJurisdictionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Repository("balanceReportDao")
public class BalanceReportDao extends DaoCenter {

    private static Logger logger = LoggerFactory.getLogger(BalanceReportDao.class);
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "pcUserRoleBaseServiceImpl")
    private PcUserRoleBaseService pcUserRoleBaseService;
    @Resource(name = "roleJurisdictionUtil")
    private RoleJurisdictionUtil roleJurisdictionUtil;

    public StringBuilder GetBalanceReportSql(Long saleOrg, Long st, String storeCode, Long[] sbuId,
                                             Long[] organizationId, String firstTime, String lastTime) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
//        sql.append(" SELECT s.alias store,s.id storeId,sbu.id SBUID,sbu.NAME sbu,s.out_trade_no store_code,s.sale_org saleOrgId,"
//                + "so.NAME sale_org,org.id organizationId,org.NAME organization,concat( s.id,'@',sbu.id,'@',org.id ) dj_id"
//                + ",IFNULL( b.发货,0 ) + IFNULL( b.退货,0 ) + IFNULL( b.政策回款,0 ) qcys,IFNULL( a.发货,0 ) + IFNULL( a.退货,0 ) - IFNULL( b.发货,0 ) - IFNULL( b.退货,0 ) qjfh,"
//                + "IFNULL( a.政策回款,0 ) - IFNULL( b.政策回款,0 ) qjhk,IFNULL( a.发货,0 ) + IFNULL( a.退货,0 ) + IFNULL( a.政策回款,0 ) qmys,IFNULL( a.订单,0 ) - IFNULL( a.发货,0 ) zyje,"
//                + "IFNULL( a.订单,0 ) + IFNULL( a.退货,0 ) + IFNULL( a.政策回款,0 ) + IFNULL( c.授信,0 ) balance,IFNULL( c.授信,0 ) sx,IFNULL( a.订单,0 ) `订单a`,"
//                + "IFNULL( a.发货,0 ) `发货a`,IFNULL( a.退货,0 ) `退货a`,IFNULL( a.政策,0 ) `政策a`,IFNULL( a.回款,0 ) `回款a`,IFNULL( a.订单,0 ) `订单b`,"
//                + "IFNULL( a.发货,0 ) `发货b`,IFNULL( a.退货,0 ) `退货b`,IFNULL( b.政策,0 ) `政策b`,IFNULL( b.回款,0 ) `回款b`  "
//                + "FROM xx_store s JOIN xx_sale_org so ON so.id = s.sale_org JOIN xx_sbu sbu ON s.company_info_id = sbu.company_info_id "
//                + "JOIN xx_organization org ON org.company_info_id = s.company_info_id LEFT JOIN ( "
//                + "SELECT store,organization,sbu,SUM( IFNULL( actual_amount,0 ) ) 授信  FROM xx_credit_recharge  WHERE doc_status=2 and end_date > "
//                + "'"+lastTime+"'"
//                + "  GROUP BY store,organization,sbu  "
//                + ") c ON s.id = c.store  AND org.id = c.organization  AND sbu.id = c.sbu LEFT JOIN ( "
//                + "SELECT `客户ID`,`经营组织ID`,`单据开始日期`,`SBUID`,SUM( IFNULL( 订单,0 ) ) 订单,SUM( IFNULL( 发货,0 ) ) 发货,SUM( IFNULL( 退货,0 ) ) 退货,SUM( IFNULL( 政策回款,0 ) ) 政策回款,"
//                + "SUM( CASE WHEN `单据类型` = '政策录入' THEN IFNULL( 政策回款,0 ) ELSE 0 END ) 政策,SUM( CASE WHEN `单据类型` <> '政策录入' THEN IFNULL( 政策回款,0 ) ELSE 0 END ) 回款  FROM v_link_balance  "
//                + "WHERE 单据开始日期 <= "
//                + "'"+lastTime+"'"
//                + "  GROUP BY `客户ID`,`经营组织ID`,`SBUID`  ) a ON s.id = a.`客户ID`  AND org.id = a.`经营组织ID`  AND sbu.id = a.`SBUID` LEFT JOIN ("
//                + " SELECT `客户ID`,`经营组织ID`,`SBUID`,SUM( IFNULL( 订单,0 ) ) 订单,SUM( IFNULL( 发货,0 ) ) 发货,SUM( IFNULL( 退货,0 ) ) 退货,SUM( IFNULL( 政策回款,0 ) ) 政策回款,"
//                + "SUM( CASE WHEN `单据类型` = '政策录入' THEN IFNULL( 政策回款,0 ) ELSE 0 END ) 政策,SUM( CASE WHEN `单据类型` <> '政策录入' THEN IFNULL( 政策回款,0 ) ELSE 0 END ) 回款  FROM v_link_balance "
//                + " WHERE 单据开始日期 < "
//                + "'"+firstTime+"'"
//                + "  GROUP BY `客户ID`,`经营组织ID`,`SBUID`  ) b ON s.id = b.`客户ID`  AND org.id = b.`经营组织ID`  AND sbu.id = b.`SBUID` "
//                + " WHERE 1 = 1  ");
        sql.append("SELECT 0 id,s.id storeId, s.NAME store_name, s.alias store, s.out_trade_no store_code, so.id saleOrgId, " +
                " so.NAME sale_org, sbu.id SBUID, sbu.NAME sbu, org.id organizationId, org.NAME organization," +
                " a.`单据开始日期` bill_start_date, CONCAT( s.id,'@',sbu.id,'@',org.id ) dj_id," +
                " IFNULL( b.发货,0 ) + IFNULL( b.退货,0 ) + IFNULL( b.政策回款,0 ) qcys, IFNULL( a.发货,0 ) + IFNULL( a.退货,0 ) - IFNULL( b.发货,0 ) - IFNULL( b.退货,0 ) qjfh,IFNULL( a.政策回款,0 ) - IFNULL( b.政策回款,0 ) qjhk, IFNULL( a.发货,0 ) + IFNULL( a.退货,0 ) + IFNULL( a.政策回款,0 ) qmys,IFNULL( a.订单,0 ) - IFNULL( a.发货,0 ) zyje, IFNULL( a.订单,0 ) + IFNULL( a.退货,0 ) + IFNULL( a.政策回款,0 ) + IFNULL( c.授信,0 ) balance," +
                " IFNULL( c.授信,0 ) sx,IFNULL( a.订单,0 ) `订单a`, IFNULL( a.发货,0 ) `发货a`,IFNULL( a.退货,0 ) `退货a`,IFNULL( a.政策,0 ) `政策a`,IFNULL( a.回款,0 ) `回款a`,IFNULL( a.订单,0 ) `订单b`, IFNULL( a.发货,0 ) `发货b`,IFNULL( a.退货,0 ) `退货b`,IFNULL( b.政策,0 ) `政策b`,IFNULL( b.回款,0 ) `回款b` " +
                " FROM xx_store s JOIN xx_sale_org so ON so.id = s.sale_org JOIN xx_sbu sbu ON s.company_info_id = sbu.company_info_id " +
                " JOIN xx_organization org ON org.company_info_id = s.company_info_id LEFT JOIN ( SELECT store,organization,sbu,SUM( IFNULL( actual_amount,0 ) ) 授信 FROM xx_credit_recharge " +
                " WHERE end_date > '"+lastTime+"' GROUP BY store,organization,sbu ) c ON s.id = c.store AND org.id = c.organization AND sbu.id = c.sbu LEFT JOIN ( SELECT `客户ID`,`经营组织ID`,`单据开始日期`," +
                " `SBUID`,SUM( IFNULL( 订单,0 ) ) 订单,SUM( IFNULL( 发货,0 ) ) 发货,SUM( IFNULL( 退货,0 ) ) 退货, SUM( IFNULL( 政策回款,0 ) ) 政策回款,SUM( CASE WHEN `单据类型` = '政策录入' THEN IFNULL( 政策回款,0 ) ELSE 0 END ) 政策, " +
                " SUM( CASE WHEN `单据类型` <> '政策录入' THEN IFNULL( 政策回款,0 ) ELSE 0 END ) 回款 FROM v_link_balance WHERE 单据开始日期 <= '"+lastTime+"' GROUP BY `客户ID`,`经营组织ID`,`SBUID` ) a ON s.id = a.`客户ID` AND org.id = a.`经营组织ID` AND sbu.id = a.`SBUID` LEFT JOIN ( SELECT `客户ID`,`经营组织ID`,`SBUID`,SUM( IFNULL( 订单,0 ) ) 订单," +
                " SUM( IFNULL( 发货,0 ) ) 发货,SUM( IFNULL( 退货,0 ) ) 退货, SUM( IFNULL( 政策回款,0 ) ) 政策回款,SUM( CASE WHEN `单据类型` = '政策录入' THEN IFNULL( 政策回款,0 ) ELSE 0 END ) 政策, SUM( CASE WHEN `单据类型` <> '政策录入' THEN IFNULL( 政策回款,0 ) ELSE 0 END ) 回款 FROM v_link_balance WHERE 单据开始日期 < '"+firstTime+"' GROUP BY `客户ID`,`经营组织ID`,`SBUID` ) b ON s.id = b.`客户ID` AND org.id = b.`经营组织ID` AND sbu.id = b.`SBUID` " +
                " WHERE 1 = 1  ");

        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (storeMember.getMemberType() == 1) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and s.id in (" + storeAuth + ")");
            }
        }
        if (!ConvertUtil.isEmpty(storeCode)) {

            sql.append(" AND s.out_trade_no = ?");
        }
        if (!ConvertUtil.isEmpty(saleOrg)) {
            sql.append(" AND so.id = " + saleOrg);
        }

        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND s.company_info_id = " + companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sbuId) && sbuId.length > 0) {
            String s = "";
            for (int i = 0; i < sbuId.length; i++) {
                if (i == sbuId.length - 1) {
                    s += "'" + sbuId[i] + "'";
                } else {
                    s += "'" + sbuId[i] + "',";
                }
            }
            sql.append(" AND sbu.id IN(" + s + ")");
        }
        if (!ConvertUtil.isEmpty(st)) {
            sql.append(" AND s.id = " + st);
        }

        if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
            String s = "";
            for (int i = 0; i < organizationId.length; i++) {
                if (i == organizationId.length - 1) {
                    s += "'" + organizationId[i] + "'";
                } else {
                    s += "'" + organizationId[i] + "',";
                }
            }
            sql.append(" AND org.id IN(" + s + ")");
        }

        // 用户机构
        String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
        if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
                sql.append(" and so.id in (" + saleOrgIds + ")");
            } else {
                sql.append(" and so.id is null");
            }
        }

        // 用户Sbu
        String sbuIds = roleJurisdictionUtil.getSbuIds();
        if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
                sql.append(" and sbu.id in (" + sbuIds + ")");
            } else {
                sql.append(" and sbu.id is null");
            }
        }

        // 用户经营组织
        String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
        if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")) {
            if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")) {
                sql.append(" and org.id in (" + organizationIdS + ")");
            } else {
                sql.append(" and org.id is null");
            }
        }
//		//时间过滤
//		if (!ConvertUtil.isEmpty(firstTime)){
//			sql.append("  AND a.单据开始日期 >= '"+firstTime+"'");
//		}
//		if (!ConvertUtil.isEmpty(lastTime)) {
//			sql.append("  AND a.单据开始日期 <= '"+lastTime+"'");
//		} else {
//			sql.append("  AND a.单据开始日期 <= now()");
//		}
        sql.append("  AND ( a.`客户ID` IS NOT NULL OR b.`客户ID` IS NOT NULL OR c.store IS NOT NULL )");
        return sql;
    }


    public Page<Map<String, Object>> findPage(Long saleOrg, Long st, String storeCode, Long[] sbuId,
                                              Long[] organizationId, String firstTime, String lastTime, Pageable pageable) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        if (ConvertUtil.isEmpty(firstTime)) {
            ExceptionUtil.throwServiceException("请输入对账期间开始日期");
        }
        if (ConvertUtil.isEmpty(lastTime)) {
            ExceptionUtil.throwServiceException("请输入对账期间结束日期");

        }
        if (ConvertUtil.isEmpty(companyInfoId)) {
            ExceptionUtil.throwServiceException("公司号不能为空");
        }
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        sql = GetBalanceReportSql(saleOrg, st, storeCode, sbuId, organizationId, firstTime, lastTime);
        Object[] objs = new Object[list.size()];

        Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
        return page;
    }

    // ============================================================================================================
    // 获取指定id的用户信息
    public Map<String, Object> findStoreInfo(Long storeId) {
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        Map<String, Object> storeMap = new HashMap<String, Object>();
        try {
            sql.append(
                    "select a.客户  store,a.客户ID storeId,a.SBUID,a.SBU sbu,a.`客户编码` store_code,a.`经营组织` organization,a.`机构` saleOrg from v_link_balance a left JOIN xx_store  s ON a.`客户ID` = s.id ");
            sql.append(" where a.客户ID = ? ");
            list.add(storeId);
            sql.append(" group by a.客户ID");
            Object[] objs = new Object[list.size()];
            for (int i = 0; i < list.size(); i++) {
                objs[i] = list.get(i);
            }
            storeMap = getNativeDao().findSingleMap(sql.toString(), objs);
        } catch (Exception e) {
            logger.error("客户对账单报表：", e);
        }
        return storeMap;
    }

    // ============================================================================================================

    /**
     * 导出客户余额报表数据
     **/
    public List<Map<String, Object>> findTableNew(Long saleOrg, Long st, String storeCode, Long[] sbuId,
                                                  Long[] organizationId, String firstTime, String lastTime) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        if (ConvertUtil.isEmpty(firstTime)) {
            return new ArrayList<Map<String, Object>>();
        }
        if (ConvertUtil.isEmpty(lastTime)) {
            return new ArrayList<Map<String, Object>>();
        }
        if (ConvertUtil.isEmpty(companyInfoId)) {
            return new ArrayList<Map<String, Object>>();
        }
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        sql = GetBalanceReportSql(saleOrg, st, storeCode, sbuId, organizationId, firstTime, lastTime);

        Object[] objs = new Object[list.size()];
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }

    public List<Map<String, Object>> findTable(Object[] saleOrgId, Object[] storeId, String storeCode, Long[] sbuId,
                                               Long[] organizationId, String firstTime, String lastTime, String[] ids) {

        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        if (ConvertUtil.isEmpty(firstTime)) {
            logger.error("客户对账单报表：", "请输入开始日期");
        }
        if (ConvertUtil.isEmpty(lastTime)) {
            logger.error("客户对账单报表：", "请输入结束日期");
        }
        if (ConvertUtil.isEmpty(companyInfoId)) {
            logger.error("客户对账单报表：", "公司号为空");
        }

        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
//        sql =GetBalanceReportSql( saleOrgId,  storeId,  storeCode,  sbuId,organizationId,  firstTime,  lastTime);
        Object[] objs = new Object[list.size()];
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }

    public Page<Map<String, Object>> findPageCustomerStatement(Long store, Long[] sbu, Long[] organizationId,
                                                               String firstTime, String lastTime, String[] type, Long saleOrgId, Pageable pageable, Long typeId) {

        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Date now = new Date();
        DateFormat dfStart = new SimpleDateFormat("yyyy-MM-dd");
        String nowDate = dfStart.format(now);
        StringBuilder sql = neaten(sbu, null, organizationId, type);

        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND A.company_info_id = ?");
            list.add(companyInfoId);
        }

        if (!ConvertUtil.isEmpty(sbu) && sbu.length > 0) {
            String s = "";
            for (int i = 0; i < sbu.length; i++) {
                if (i == sbu.length - 1) {
                    s += sbu[i];
                } else {
                    s += sbu[i] + ",";
                }
            }
            sql.append(" AND A.SBUID IN(" + s + ")");
        }

        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (store != null) {
            sql.append(" AND A.客户ID =" + store);
        }
        if (saleOrgId != null) {
            sql.append(" AND A.机构ID  =" + saleOrgId);
        }
        if (storeMember.getMemberType() == 1) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and A.客户ID in (" + storeAuth + ")");
            }
        }
        if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
            String s = "";
            for (int i = 0; i < organizationId.length; i++) {
                if (i == organizationId.length - 1) {
                    s += organizationId[i];
                } else {
                    s += organizationId[i] + ",";
                }
            }
            sql.append(" AND A.`经营组织ID` IN(" + s + ")");
        }
        if (!ConvertUtil.isEmpty(typeId)) {
            // 对账日期
            if (ConvertUtil.isEmpty(firstTime)) {
                firstTime = nowDate;
            }
            if (ConvertUtil.isEmpty(lastTime)) {
                lastTime = nowDate;
            }
            if (typeId == 1) {
                sql.append(" AND A.`单据开始日期` < ? ");
                list.add(firstTime);
            } else if (typeId == 2) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据开始日期` <= ? and A.`单据类型` in ('发货通知单','退货单') ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 3) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据开始日期` <= ? and A.`单据类型` in ('政策录入','财务录入','客户充值') ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 4) {
                sql.append(" AND A.`单据开始日期` <= ? ");
                list.add(lastTime);
            } else if (typeId == 5) {
                sql.append(
                        " AND A.`单据开始日期` >= ? and A.`单据结束日期` <= ? and A.`单据类型` in ('订单') AND  A.数量<(A.发货数量+A.关闭数量) ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 6) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据结束日期` <? and A.`单据类型` in ('授信') ");
                list.add(lastTime);
                list.add(lastTime);
            } else if (typeId == 7) {
                if (!ConvertUtil.isEmpty(firstTime)) {
                    sql.append(" AND A.单据开始日期 >= ? ");
                    list.add(firstTime);
                } else {
                    sql.append(" AND A.单据开始日期 >= str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d') ");
                }
                if (!ConvertUtil.isEmpty(lastTime)) {
                    sql.append(" AND A.单据开始日期 <= ? ");
                    list.add(lastTime);
                } else {
                    sql.append(" AND A.单据开始日期 < now() ");
                }
                sql.append(" AND A.`单据类型` in ('政策录入','财务录入','客户充值','发货通知单','退货单') ");

            } else if (typeId == 8) { // 客户对账单
                if (!ConvertUtil.isEmpty(firstTime)) {
                    sql.append(" AND A.单据开始日期 >= ? ");
                    list.add(firstTime);
                } else {
                    sql.append(" AND A.单据开始日期 >= str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d') ");
                }
                if (!ConvertUtil.isEmpty(lastTime)) {
                    sql.append(" AND A.单据开始日期 <= ? ");
                    list.add(lastTime);
                } else {
                    sql.append(" AND A.单据开始日期 < now() ");
                }
                sql.append(" AND A.`单据类型` in ('政策录入','财务录入','客户充值','发货通知单','退货单','订单','授信') ");
            }
        } else {
            if (!ConvertUtil.isEmpty(firstTime)) {
                sql.append(" AND A.单据开始日期 >= ? ");
                list.add(firstTime);
            } else {
                sql.append(" AND A.单据开始日期 >= str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d') ");
            }
            if (!ConvertUtil.isEmpty(lastTime)) {
                sql.append(" AND A.单据开始日期 <= ? ");
                list.add(lastTime);
            } else {
                sql.append(" AND A.单据开始日期 < now() ");
            }
            sql.append(" AND A.`单据类型` <> '订单'");
        }

        // 用户机构
        String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
        if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
                sql.append(" and A.机构ID in (" + saleOrgIds + ")");
            } else {
                sql.append(" and A.机构ID is null");
            }
        }

        // 用户Sbu
        String sbuIds = roleJurisdictionUtil.getSbuIds();
        if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
                sql.append(" and A.SBUID in (" + sbuIds + ")");
            } else {
                sql.append(" and A.SBUID is null");
            }
        }

        // 用户经营组织
        String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
        if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")) {
            if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")) {
                sql.append(" and A.`经营组织ID` in (" + organizationIdS + ")");
            } else {
                sql.append(" and A.`经营组织ID` is null");
            }
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        sql.append(" ORDER BY A.`单据开始日期` DESC	");

        Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);

        return page;
    }

    public List<Map<String, Object>> findTableCustomerStatement(Long store, Long[] sbu, Long[] organizationId,
                                                                String firstTime, String lastTime, String[] type, Long saleOrgId, Long[] ids, Long typeId) {

        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Date now = new Date();
        DateFormat dfStart = new SimpleDateFormat("yyyy-MM-dd");
        String nowDate = dfStart.format(now);
        StringBuilder sql = neaten(sbu, null, organizationId, type);
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND A.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(typeId)) {
            // 对账日期
            if (ConvertUtil.isEmpty(firstTime)) {
                firstTime = nowDate;
            }
            if (ConvertUtil.isEmpty(lastTime)) {
                lastTime = nowDate;
            }
            if (typeId == 1) {
                sql.append(" AND A.`单据开始日期` < ? ");
                list.add(firstTime);
            } else if (typeId == 2) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据开始日期` <= ? and A.`单据类型` in('发货通知单','退货单') ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 3) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据开始日期` <= ? and A.`单据类型` in('政策录入','财务录入','客户充值') ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 4) {
                sql.append(" AND A.`单据开始日期` <= ? ");
                list.add(lastTime);
            } else if (typeId == 5) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据结束日期` <= ? and A.`单据类型` in('订单') AND  A.数量<(A.发货数量+A.关闭数量) ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 6) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据结束日期` <? and A.`单据类型` in('授信') ");
                list.add(lastTime);
                list.add(lastTime);
            } else if (typeId == 7) {
                if (!ConvertUtil.isEmpty(firstTime)) {
                    sql.append(" AND A.单据开始日期 >= ? ");
                    list.add(firstTime);
                } else {
                    sql.append(" AND A.单据开始日期 >= str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d') ");
                }
                if (!ConvertUtil.isEmpty(lastTime)) {
                    sql.append(" AND A.单据开始日期 <= ? ");
                    list.add(lastTime);
                } else {
                    sql.append(" AND A.单据开始日期 < now() ");
                }
                sql.append(" AND A.`单据类型` in ('政策录入','财务录入','客户充值','发货通知单','退货单') ");
            } else if (typeId == 8) { // 客户对账单
                if (!ConvertUtil.isEmpty(firstTime)) {
                    sql.append(" AND A.单据开始日期 >= ? ");
                    list.add(firstTime);
                } else {
                    sql.append(" AND A.单据开始日期 >= str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d') ");
                }
                if (!ConvertUtil.isEmpty(lastTime)) {
                    sql.append(" AND A.单据开始日期 <= ? ");
                    list.add(lastTime);
                } else {
                    sql.append(" AND A.单据开始日期 < now() ");
                }
                sql.append(" AND A.`单据类型` in ('政策录入','财务录入','客户充值','发货通知单','退货单','订单','授信') ");
            }
        } else {
            if (!ConvertUtil.isEmpty(firstTime)) {
                sql.append(" AND A.单据开始日期 >= ? ");
                list.add(firstTime);
            } else {
                sql.append(" AND A.单据开始日期 >= str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d') ");
            }
            if (!ConvertUtil.isEmpty(lastTime)) {
                sql.append(" AND A.单据开始日期 <= ? ");
                list.add(lastTime);
            } else {
                sql.append(" AND A.单据开始日期 < now() ");
            }
            sql.append(" AND A.`单据类型` <> '订单'");
        }

        if (!ConvertUtil.isEmpty(sbu) && sbu.length > 0) {
            String s = "";
            for (int i = 0; i < sbu.length; i++) {
                if (i == sbu.length - 1) {
                    s += sbu[i];
                } else {
                    s += sbu[i] + ",";
                }
            }
            sql.append(" AND A.SBUID IN (" + s + ")");
        }
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());

        if (store != null) {
            sql.append(" AND A.客户ID =" + store);
        }
        if (saleOrgId != null) {
            sql.append(" AND A.机构ID =" + saleOrgId);
        }
        if (storeMember.getMemberType() == 1) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and A.客户ID in (" + storeAuth + ")");
            }
        }

        if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
            String s = "";
            for (int i = 0; i < organizationId.length; i++) {
                if (i == organizationId.length - 1) {
                    s += organizationId[i];
                } else {
                    s += organizationId[i] + ",";
                }
            }
            sql.append(" AND A.`经营组织ID` IN (" + s + ")");
        }

        // 用户机构
        String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
        if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
                sql.append(" and A.机构ID in (" + saleOrgIds + ")");
            } else {
                sql.append(" and A.机构ID is null");
            }
        }

        // 用户Sbu
        String sbuIds = roleJurisdictionUtil.getSbuIds();
        if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
                sql.append(" and A.SBUID in (" + sbuIds + ")");
            } else {
                sql.append(" and A.SBUID is null");
            }
        }

        // 用户经营组织
        String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
        if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")) {
            if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")) {
                sql.append(" and A.`经营组织ID` in (" + organizationIdS + ")");
            } else {
                sql.append(" and A.`经营组织ID` is null");
            }
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        sql.append(" ORDER BY A.`单据开始日期`	");

        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }

    public StringBuilder neaten(Long[] sbu, Long store, Long[] organizationId, String[] type) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT " + "A.SBU sbu_name," + "A.`客户` store_name," + "A.`机构` sale_org_name,"
                + "A.`经营组织` organization,A.`经营组织ID` organization_id," + "A.`客户编码` store_code,"
                + "A.`平台结算价` sale_org_unit_price," + "A.`结算发货金额` sale_org_shipped_amount,"
                + "A.`总部收款` all_company_amount," + "A.`平台收款` all_sale_org_amount,A.`仓库` warehouse_name, ");
        sql.append(" A.`产品编码` product_code,A.`木种花色` wood_type_or_color,A.`型号` model,A.`规格` spec, "
                + " A.`发货支数` shipped_branch,A.`渠道`  sys_business_type, ");
        sql.append("A.`单据开始日期` start_date," + "A.`单据类型` type," + "A.`单据编号` sn," + "A.`产品` product_name,"
                + "A.`发货数量` shipping_quantity," + "A.`销售单价` unit_price," + "A.`发货金额` shipping_money,"
                + "A.`政策回款` policy_money," + "A.`金额` money," + "A.`订单编码` order_sn," + "A.`创建人` creator_name,"
                + "A.`备注` memo,xsd.value businessType, "
                + "A.`调款单号` transfer_no ,A.`单位号` unit_no,A.`平台号` platform_no,A.`政策类型` policy_type,A.`应收余额` batchStatus,A.`来源金额`  order_money from "
                + " v_link_balance A left join xx_product p on A.产品ID = p.id "
                + " left join xx_system_dict xsd on xsd.id = p.business_division where 1=1 ");
        return sql;
    }

    public Integer findSalesReportCount(Long store, Long[] sbu, Long[] organizationId, String firstTime,
                                        String lastTime, String[] type, Long saleOrgId, Pageable pageable, Long typeId, String productCode,
                                        String productName, String billCode) {

        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        Long storeMemberId = WebUtils.getCurrentStoreMemberId();
        List<Object> list = new ArrayList<Object>();
        Date now = new Date();
        DateFormat dfStart = new SimpleDateFormat("yyyy-MM-dd");
        String nowDate = dfStart.format(now);
        StringBuilder sql = salesReportSql(sbu, null, organizationId, type, productCode, productName, billCode);

        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND A.company_info_id = ?");
            list.add(companyInfoId);
        }

        if (!ConvertUtil.isEmpty(sbu) && sbu.length > 0) {
            String s = "";
            for (int i = 0; i < sbu.length; i++) {
                if (i == sbu.length - 1) {
                    s += sbu[i];
                } else {
                    s += sbu[i] + ",";
                }
            }
            sql.append(" AND A.SBUID IN(" + s + ")");
        }

        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (store != null) {
            sql.append(" AND A.客户ID =" + store);
        }
        if (saleOrgId != null) {
            sql.append(" AND A.机构ID  =" + saleOrgId);
        }

        // 票据编号
        if (!ConvertUtil.isEmpty(billCode)) {
            sql.append(" AND A.`单据编号` =  ?");
            list.add(billCode);
        }

        if (storeMember.getMemberType() == 1) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and A.客户ID in (" + storeAuth + ")");
            }
        }

        if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
            String s = "";
            for (int i = 0; i < organizationId.length; i++) {
                if (i == organizationId.length - 1) {
                    s += organizationId[i];
                } else {
                    s += organizationId[i] + ",";
                }
            }
            sql.append(" AND A.`经营组织ID` IN(" + s + ")");
        }
        if (!ConvertUtil.isEmpty(typeId)) {
            // 对账日期
            if (ConvertUtil.isEmpty(firstTime)) {
                firstTime = nowDate;
            }
            if (ConvertUtil.isEmpty(lastTime)) {
                lastTime = nowDate;
            }
            if (typeId == 1) {
                sql.append(" AND A.`单据开始日期` < ? ");
                list.add(firstTime);
            } else if (typeId == 2) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据开始日期` <= ? and A.`单据类型` in('发货通知单','退货单') ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 3) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据开始日期` <= ? and A.`单据类型` in('政策录入','财务录入','客户充值') ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 4) {
                sql.append(" AND A.`单据开始日期` <= ? ");
                list.add(lastTime);
            } else if (typeId == 5) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据结束日期` <= ? and A.`单据类型` in('订单') AND  A.数量<(A.发货数量+A.关闭数量) ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 6) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据结束日期` <? and A.`单据类型` in('授信') ");
                list.add(lastTime);
                list.add(lastTime);
            }
        } else {
            if (!ConvertUtil.isEmpty(firstTime)) {
                sql.append(" AND A.单据开始日期 >= ? ");
                list.add(firstTime);
            } else {
                sql.append(" AND A.单据开始日期 >= str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d') ");
            }
            if (!ConvertUtil.isEmpty(lastTime)) {
                sql.append(" AND A.单据开始日期 <= ? ");
                list.add(lastTime);
            } else {
                sql.append(" AND A.单据开始日期 < now() ");
            }
            sql.append(" AND A.`单据类型` <> '订单'");
        }

        // 用户机构
        String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
        if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
                sql.append(" and A.机构ID in (" + saleOrgIds + ")");
            } else {
                sql.append(" and A.机构ID is null");
            }
        }

        // 用户Sbu
        String sbuIds = roleJurisdictionUtil.getSbuIds();
        if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
                sql.append(" and A.SBUID in (" + sbuIds + ")");
            } else {
                sql.append(" and A.SBUID is null");
            }
        }

        // 用户经营组织
        String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
        if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")) {
            if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")) {
                sql.append(" and A.`经营组织ID` in (" + organizationIdS + ")");
            } else {
                sql.append(" and A.`经营组织ID` is null");
            }
        }

        sql.append(" ORDER BY A.`单据开始日期` DESC	");

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        String totalsql = "select count(1) from ( " + sql + ") t";

        Integer count = getNativeDao().findInt(totalsql, objs);

        return count;
    }

    public Page<Map<String, Object>> findSalesReportData(Long store, Long[] sbu, Long[] organizationId,
                                                         String firstTime, String lastTime, String[] type, Long saleOrgId, Pageable pageable, Long typeId,
                                                         String productCode, String productName, String billCode) {

        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        Date now = new Date();
        DateFormat dfStart = new SimpleDateFormat("yyyy-MM-dd");
        String nowDate = dfStart.format(now);
        StringBuilder sql = salesReportSql(sbu, null, organizationId, type, productCode, productName, billCode);

        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND A.company_info_id = ?");
            list.add(companyInfoId);
        }

        if (!ConvertUtil.isEmpty(sbu) && sbu.length > 0) {
            String s = "";
            for (int i = 0; i < sbu.length; i++) {
                if (i == sbu.length - 1) {
                    s += sbu[i];
                } else {
                    s += sbu[i] + ",";
                }
            }
            sql.append(" AND A.SBUID IN(" + s + ")");
        }

        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (store != null) {
            sql.append(" AND A.客户ID =" + store);
        }
        if (saleOrgId != null) {
            sql.append(" AND A.机构ID  =" + saleOrgId);
        }

        // 票据编号
        if (!ConvertUtil.isEmpty(billCode)) {
            sql.append(" AND A.`单据编号` =  ?");
            list.add(billCode);
        }

        if (storeMember.getMemberType() == 1) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and A.客户ID in (" + storeAuth + ")");
            }
        }

        if (!ConvertUtil.isEmpty(organizationId) && organizationId.length > 0) {
            String s = "";
            for (int i = 0; i < organizationId.length; i++) {
                if (i == organizationId.length - 1) {
                    s += organizationId[i];
                } else {
                    s += organizationId[i] + ",";
                }
            }
            sql.append(" AND A.`经营组织ID` IN(" + s + ")");
        }
        if (!ConvertUtil.isEmpty(typeId)) {
            // 对账日期
            if (ConvertUtil.isEmpty(firstTime)) {
                firstTime = nowDate;
            }
            if (ConvertUtil.isEmpty(lastTime)) {
                lastTime = nowDate;
            }
            if (typeId == 1) {
                sql.append(" AND A.`单据开始日期` < ? ");
                list.add(firstTime);
            } else if (typeId == 2) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据开始日期` <= ? and A.`单据类型` in('发货通知单','退货单') ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 3) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据开始日期` <= ? and A.`单据类型` in('政策录入','财务录入','客户充值') ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 4) {
                sql.append(" AND A.`单据开始日期` <= ? ");
                list.add(lastTime);
            } else if (typeId == 5) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据结束日期` <= ? and A.`单据类型` in('订单') AND  A.数量<(A.发货数量+A.关闭数量) ");
                list.add(firstTime);
                list.add(lastTime);
            } else if (typeId == 6) {
                sql.append(" AND A.`单据开始日期` >= ? and A.`单据结束日期` <? and A.`单据类型` in('授信') ");
                list.add(lastTime);
                list.add(lastTime);
            }
        } else {
            if (!ConvertUtil.isEmpty(firstTime)) {
                sql.append(" AND A.单据开始日期 >= ? ");
                list.add(firstTime);
            } else {
                sql.append(" AND A.单据开始日期 >= str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d') ");
            }
            if (!ConvertUtil.isEmpty(lastTime)) {
                sql.append(" AND A.单据开始日期 <= ? ");
                list.add(lastTime);
            } else {
                sql.append(" AND A.单据开始日期 < now() ");
            }
            sql.append(" AND A.`单据类型` <> '订单'");
        }

        // 用户机构
        String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
        if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
                sql.append(" and A.机构ID in (" + saleOrgIds + ")");
            } else {
                sql.append(" and A.机构ID is null");
            }
        }

        // 用户Sbu
        String sbuIds = roleJurisdictionUtil.getSbuIds();
        if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
                sql.append(" and A.SBUID in (" + sbuIds + ")");
            } else {
                sql.append(" and A.SBUID is null");
            }
        }

        // 用户经营组织
        String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
        if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")) {
            if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")) {
                sql.append(" and  A.`经营组织ID` in (" + organizationIdS + ")");
            } else {
                sql.append(" and  A.`经营组织ID` is null");
            }
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        sql.append(" ORDER BY A.`单据开始日期` DESC	");

        Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);

        return page;
    }

    // 销售报表sql
    public StringBuilder salesReportSql(Long[] sbu, Long store, Long[] organizationId, String[] type,
                                        String productCode, String productName, String billCode) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT " + "A.SBU sbu_name," + "A.`客户` store_name," + "A.`机构` sale_org_name,"
                + "A.`品类` product_category," + "A.`经营组织` organization," + "A.`客户编码` store_code,"
                + "A.`平台结算价` sale_org_unit_price," + "A.`结算发货金额` sale_org_shipped_amount,"
                + "A.`总部收款` all_company_amount," + "A.`平台收款` all_sale_org_amount,A.`仓库` warehouse_name, ");
        sql.append(" A.`产品编码` product_code,A.`木种花色` wood_type_or_color,A.`型号` model,A.`规格` spec, "
                + " A.`发货支数` shipped_branch,A.`渠道`  sys_business_type, ");
        sql.append("A.`单据开始日期` start_date," + "A.`单据类型` type," + "A.`单据编号` sn," + "A.`产品` product_name, "
                + "A.`发货数量` shipping_quantity," + "A.`销售单价` unit_price," + "A.`发货金额` shipping_money, "
                + "A.`政策回款` policy_money," + "A.`金额` money," + "A.`订单编码` order_sn," + "A.`创建人` creator_name,"
                + "A.`备注` memo,A.`业务类型` businessType, "
                + "A.`产品等级` product_level,A.`含水率` moistureContent,A.`新旧标识` oldAndNew_logo,A.`色号` color_number,A.`批次` batch, "
                + "A.`长度mm` length,A.`宽度mm` width,A.`厚度mm` thickness,A.`收货地址` delivery_address,A.`产品结构` product_results, "
                + "A.`是否新品` new_product,A.`是否爆款` hot_money,A.`是否定制` customized from "
                + " v_link_balance A left join xx_product p on A.产品ID = p.id "
                + " left join xx_system_dict xsd on xsd.id = p.business_division where 1=1 ");
        return sql;
    }

    public Page<Map<String, Object>> findPageDifference(Long store, Long sbu, Long organizationId, Long saleOrgId,
                                                        String firstTime, String lastTime, Pageable pageable) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = neatenDifference(firstTime, sbu, saleOrgId, store, organizationId);
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND a.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sbu)) {
            sql.append(" AND a.SBUID = ?");
            list.add(sbu);
        }
        if (!ConvertUtil.isEmpty(store)) {
            sql.append(" AND a.`客户ID` = ?");
            list.add(store);
        }
        if (!ConvertUtil.isEmpty(saleOrgId)) {
            sql.append(" AND a.`机构ID` = ?");
            list.add(saleOrgId);
        }
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (store != null) {
            sql.append(" AND a.客户ID =" + store);
        }
        if (storeMember.getMemberType() == 1) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and a.客户ID in (" + storeAuth + ")");
            }
        }
        if (!ConvertUtil.isEmpty(organizationId)) {
            sql.append(" AND a.`经营组织ID` = ?");
            list.add(organizationId);
        }

        if (!ConvertUtil.isEmpty(lastTime)) {
            sql.append(" AND a.单据开始日期 <= ? ");
            list.add(lastTime);
        } else {
            sql.append(" AND a.单据开始日期 <= now() ");
        }

        // 用户机构
        String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
        if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
                sql.append(" and a.机构ID in (" + saleOrgIds + ")");
            } else {
                sql.append(" and a.机构ID is null");
            }
        }

        // 用户Sbu
        String sbuIds = roleJurisdictionUtil.getSbuIds();
        if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
                sql.append(" and a.SBUID in (" + sbuIds + ")");
            } else {
                sql.append(" and a.SBUID is null");
            }
        }

        // 用户经营组织
        String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
        if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")) {
            if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")) {
                sql.append(" and a.经营组织ID in (" + organizationIdS + ")");
            } else {
                sql.append(" and a.经营组织ID is null");
            }
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        sql.append(" GROUP BY a.SBUID,a.SBU,a.客户ID,	a.客户,a.机构ID,a.机构,	a.经营组织ID,a.经营组织");

        Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
        return page;
    }

    public List<Map<String, Object>> findTableDifference(Long store, Long sbu, Long organizationId, Long saleOrgId,
                                                         String firstTime, String lastTime, Long[] ids) {
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = neatenDifference(firstTime, sbu, saleOrgId, store, organizationId);
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND a.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sbu)) {
            sql.append(" AND a.SBUID = ?");
            list.add(sbu);
        }
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (store != null) {
            sql.append(" AND a.客户ID =" + store);
        }
        if (saleOrgId != null) {
            sql.append(" AND a.机构ID =" + saleOrgId);
        }
        if (storeMember.getMemberType() == 1) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and a.客户ID in (" + storeAuth + ")");
            }
        }
        if (!ConvertUtil.isEmpty(organizationId)) {
            sql.append(" AND a.`经营组织ID` = ?");
            list.add(organizationId);
        }

        if (!ConvertUtil.isEmpty(lastTime)) {
            sql.append(" AND a.单据开始日期 <= ? ");
            list.add(lastTime);
        } else {
            sql.append(" AND a.单据开始日期 < now() ");
        }

        // 用户机构
        String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
        if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")) {
                sql.append(" and a.机构ID in (" + saleOrgIds + ")");
            } else {
                sql.append(" and a.机构ID is null");
            }
        }

        // 用户Sbu
        String sbuIds = roleJurisdictionUtil.getSbuIds();
        if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")) {
            if (!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")) {
                sql.append(" and a.SBUID in (" + sbuIds + ")");
            } else {
                sql.append(" and a.SBUID is null");
            }
        }

        // 用户经营组织
        String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
        if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")) {
            if (!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")) {
                sql.append(" and a.经营组织ID in (" + organizationIdS + ")");
            } else {
                sql.append(" and a.经营组织ID is null");
            }
        }

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        sql.append(" GROUP BY a.SBUID,a.SBU,a.客户ID,	a.客户,a.机构ID,a.机构,	a.经营组织ID,a.经营组织");
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }

    /**
     * 查询主语句
     *
     * @param firstTime
     * @param sbu
     * @param saleOrgId
     * @param store
     * @param organization
     */
    public StringBuilder neatenDifference(String firstTime, Long sbu, Long saleOrgId, Long store, Long organization) {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT a.机构 saleOrg,a.客户 store,a.SBU sbu,a.经营组织 organization,a.`客户编码` store_code,"
                + "a.机构ID saleOrgId,a.客户ID storeId,a.SBUID SBUID,a.经营组织ID organizationId,"
                + "sum(ifnull(b.发货结算,0))+sum(ifnull(b.退货结算,0))+sum(ifnull(b.总部收款,0)) qcye,"// 期初余额
                + "SUM(a.发货)-sum(b.发货)+SUM(a.退货)-sum(b.退货) order_fxje,"// 分销价金额
                + "SUM(a.发货结算)-sum(b.发货结算)+SUM(a.退货结算)-sum(b.退货结算) order_jsje, "// 结算价金额
                + "(SUM(a.发货)-sum(b.发货)+SUM(a.退货)-sum(b.退货))-(SUM(a.发货结算)-sum(b.发货结算)+SUM(a.退货结算)-sum(b.退货结算)) ptml,"// 平台毛利
                + "sum(a.总部收款)-sum(b.总部收款) zbsk,"// 总部收款
                + "sum(a.平台收款)-sum(b.平台收款) ptsk,"// 平台收款
                + "sum(a.经销商总付款)-sum(b.经销商总付款) store_fk,"// 经销商总付款
                + "sum(ifnull(a.发货结算,0))+sum(ifnull(a.退货结算,0))+sum(ifnull(a.总部收款,0)) qmye,"// 期末余额
                + "sum(a.订单结算)-sum(a.发货结算) zyje,"// 占用金额
                + "sum(a.授信) qmsx,"// 期末授信
                + "sum(a.订单) qm_order,"// 期末订单
                + "sum(a.发货) qm_shipping,"// 期末发货
                + "sum(a.退货) qm_returns,"// 期末退货
                + "sum(a.订单结算) qm_org_amount,"// 期末订单结算
                + "sum(a.发货结算) qm_shipping_amount,"// 期末发货结算
                + "sum(a.退货结算) qm_returns_amount,"// 期末退货结算
                + "sum(a.经销商总付款) qm_store_fk,"// 期末经销商总付款
                + "sum(a.总部收款) qm_zbsk,"// 期末总部收款
                + "sum(a.平台收款) qm_ptsk,"// 期末平台收款
                + "sum(b.授信) qc_credit,"// 期初授信
                + "sum(b.订单) qc_order,"// 期初订单
                + "sum(b.发货) qc_shipping,"// 期初发货
                + "sum(b.退货) qc_returns,"// 期初退货
                + "sum(b.订单结算) qc_order_amount,"// 期初订单结算
                + "sum(b.发货结算) qc_org_amount,"// 期初发货结算
                + "sum(b.退货结算) qc_returns_amount,"// 期初退货结算
                + "sum(b.经销商总付款) qc_store_fk,"// 期初经销商总付款
                + "sum(b.总部收款) qc_zbsk,"// 期初总部收款
                + "sum(b.平台收款) qc_ptsk"// 期初平台收款
                + " FROM " + " v_link_balance a LEFT JOIN v_link_balance b ON ( a.`单据ID` =b.`单据ID` ");
        if (!ConvertUtil.isEmpty(firstTime)) {
            sql.append(" AND b.单据开始日期 < '" + firstTime + "'");
        } else {
            sql.append(" AND b.单据开始日期 < str_to_date(date_format(now(),'%Y%m01'),'%Y%m%d')");
        }
        if (!ConvertUtil.isEmpty(sbu)) {
            sql.append(" AND b.SBUID = " + sbu);
        }
        // if(!ConvertUtil.isEmpty(store)){
        // sql.append(" AND b.`客户ID` = "+store);
        // }
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());

        if (store != null) {
            sql.append(" AND b.客户ID =" + store);
        }

        if (storeMember.getMemberType() == 1) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and b.客户ID in (" + storeAuth + ")");
            }
        } else {
            sql.append(" and (b.机构ID in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = " + WebUtils.getCurrentStoreMemberId() + ") "
                    + " or b.机构ID in (select  a.id from xx_sale_org a,xx_sale_org b "
                    + " where a.tree_path like concat('%,', b.id, ',%') "
                    + " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = " + WebUtils.getCurrentStoreMemberId()
                    + ")))");

        }
        if (!ConvertUtil.isEmpty(saleOrgId)) {
            sql.append(" AND b.机构ID = " + saleOrgId);
        }
        if (!ConvertUtil.isEmpty(organization)) {
            sql.append(" AND b.经营组织ID = " + organization);
        }
        sql.append(" ) WHERE 1=1 ");

        return sql;
    }

}
