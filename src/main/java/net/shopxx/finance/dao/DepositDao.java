package net.shopxx.finance.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;

@Repository("depositDao")
public class DepositDao extends DaoCenter {

	/**
	 * 用户资金明细列表数据
	 */
	public Page<Map<String, Object>> findItemPage(String sn, String tradeNo,
			Long orderId, String orderSn, Long memberId, Integer[] type,
			String firstTime, String lastTime, Pageable pageable) {
		String sql = "select d.*,sm.username,o.id as order_id,o.sn as order_sn, p.id as payment_id,p.sn as payment_sn  "
				+ " from xx_deposit d, xx_member m ,xx_store_member sm,xx_order o,xx_payment p "
				+ "	WHERE d.member=m.id and d.orders=o.id and d.payment=p.id and sm.member=m.id and sm.is_default=1";
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Object> list = new ArrayList<Object>();
		if (companyInfoId != null) {
			sql += " and d.company_info_id = ?";
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql += " and d.sn like ?";
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(tradeNo)) {
			sql += " and d.trade_no like ?";
			list.add("%" + tradeNo + "%");
		}
		if (orderId != null) {
			sql += " and o.id = ?";
			list.add(orderId);
		}
		if (!ConvertUtil.isEmpty(orderSn)) {
			sql += " and o.sn like ?";
			list.add("%" + orderSn + "%");
		}
		if (memberId != null) {
			sql += " and m.id = ?";
			list.add(memberId);
		}
		if (type != null && type.length > 0) {
			String os = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					os += type[i];
				else
					os += type[i] + ",";
			}
			sql += " and d.type in (" + os + ")";
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql += " and d.create_date >= ?";
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql += " and d.create_date < ?";
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),
					1));
		}
		sql += " group by d.id order by d.create_date desc";

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> listItems = getNativeDao().findPageMap(sql,
				objs,
				pageable);
		return listItems;
	}
}