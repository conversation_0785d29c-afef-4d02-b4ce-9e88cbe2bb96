package net.shopxx.finance.dao;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSbu;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSbuService;
import net.shopxx.util.RoleJurisdictionUtil;
@Repository("storeBalanceDao")
public class StoreBalanceDao extends DaoCenter {
	
	

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "storeMemberSbuServiceImpl")
	private StoreMemberSbuService storeMemberSbuService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "roleJurisdictionUtil")
	private RoleJurisdictionUtil roleJurisdictionUtil;
	

	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeMemberId = WebUtils.getCurrentStoreMemberId();
		Long storeId = (Long) args[0];
		Integer[] type = (Integer[]) args[1];
		Long saleOrgId = (Long) args[2];
		Long[] organizationId = (Long[]) args[3];
		Long[] sbuId = (Long[]) args[4];

		StringBuilder sql = new StringBuilder();

		sql.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sql.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");

		Date now = new Date();
		sql.setLength(0);
		sql.append("select");
		sql.append("  s.id,");
		sql.append("  s.out_trade_no,");
		sql.append("  s.name store_name,");
		sql.append("  s.type store_type,");
		sql.append("  xso.name sale_org,");
		sql.append("  xs.name sbu_name,");
		sql.append("  xs.id sbu_id,");
		sql.append("  org.id org_id,");
		sql.append("  org.name org_name,");
		sql.append("  ifnull(cr.credit_amount,0) credit_amount,");
		sql.append("  ifnull(dr.deposit_amount,0) deposit_amount,");
		sql.append("  ifnull(policyEntry.deposit_amount,0) policyEntry_amount,");
		sql.append("  ifnull(o.amount_paid,0) amount_paid,");
		sql.append("  ifnull(oc.amount_closed,0) amount_closed,");
		sql.append(" ifnull(r.return_amount,0) return_amount,");
		sql.append("  ifnull(sb.begin_amount,0) begin_amount,");
		sql.append(
				"  (ifnull(sb.begin_amount,0) + ifnull(dr.deposit_amount,0)  - ifnull(o.amount_paid,0) + ifnull(oc.amount_closed,0)) end_amount,");
		sql.append(
				"  (ifnull(sb.begin_amount,0) + ifnull(cr.credit_amount,0) + ifnull(dr.deposit_amount,0) + ifnull(policyEntry.deposit_amount,0) - ifnull(o.amount_paid,0) + ifnull(oc.amount_closed,0) + ifnull(r.return_amount,0)) balance");
		sql.append(" from xx_store s");
		sql.append(" left join ");
		sql.append("  xx_sale_org xso on xso.id = s.sale_org ");
		// 期初 暂不处理
		sql.append(" left join");
		sql.append("  xx_store_balance sb on s.id = sb.store and sb.company_info_id = ? and sb.balance_date = ?");
		sql.append(" LEFT JOIN xx_organization org on  org.company_info_id = ?");
		sql.append(" INNER JOIN xx_store_sbu xss ON xss.store=s.id AND xss.company_info_id= ?");
		sql.append(" LEFT JOIN xx_sbu xs ON xs.id=xss.sbu and xs.status=1");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(companyInfoId);
		list.add(companyInfoId);
		// 客户授信
		sql.append(" left join");
		sql.append("  (select a.store,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) credit_amount,");
		sql.append("  xo.name organization_name,sb.name sbu_name");
		sql.append("  from xx_credit_recharge a, xx_organization xo,xx_sbu sb ");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and xo.id=a.organization and a.sbu=sb.id and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");
		list.add(now);
		list.add(now);
		list.add(companyInfoId);
		
		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.store,a.organization ,a.sbu) cr on cr.store = s.id AND cr.sbu=xs.id and cr.organization=org.id");

		// 销售回款
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) deposit_amount,xo.name organization_name,sb.name sbu_name");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 	and xo.id=a.organization and a.sbu=sb.id and a.recharge_type = b.id and b.value !='政策录入'");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.stores,a.organization,a.sbu) dr on dr.stores = s.id AND xs.id=dr.sbu and dr.organization=org.id");

		// 政策录入
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) deposit_amount,xo.name  organization_name ,sb.name sbu_name");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id 	and xo.id=a.organization and sb.id=a.sbu and b.value ='政策录入'");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.stores,a.organization,a.sbu) policyEntry on policyEntry.stores = s.id and policyEntry.sbu=xs.id and policyEntry.organization=org.id");
		// 已下达订单
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(b.price * b.quantity,2)) amount_paid,xo.name  organization_name ,sb.name sbu_name");
		sql.append("  from xx_order a, xx_order_item b, xx_organization xo, xx_sbu sb");
		sql.append(
				"  where a.id = b.orders and xo.id=a.organization and a.sbu=sb.id and a.order_status in (2, 5, 6) and a.order_type = 2");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.organization,a.stores,a.sbu) o on o.stores = s.id and o.organization=org.id and xs.id=o.sbu");
		// 已关闭订单
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(b.price * ci.quantity,2)) amount_closed,xo.name  organization_name ,sb.name sbu_name");
		sql.append(
				"  from xx_order a, xx_order_item b, xx_order_close_item ci,xx_order_close c,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		sql.append("  and xo.id=a.organization and a.sbu=sb.id");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(" group by a.organization,a.stores,a.sbu ) oc on oc.stores = s.id and oc.organization=org.id and oc.sbu=xs.id ");

		// 退货金额
		sql.append(" left join");
		sql.append(" (select a.store,a.organization,a.sbu, sum(round(b.price * b.returned_quantity,2)) return_amount,xo.name  organization_name ,sb.name sbu_name");
		sql.append(" from xx_b2b_returns_item b , xx_b2b_returns a,xx_organization xo,xx_sbu sb ");
		sql.append(" where b.b2b_returns=a.id");
		sql.append(" and  a.organization=xo.id and a.sbu=sb.id and a.status in (4,5)");
		sql.append(" and a.company_info_id = ?");
		sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(" group by a.store,a.organization,a.sbu) r on r.store = s.id and r.organization=org.id and r.sbu=xs.id");
		sql.append(" where s.company_info_id = ? and s.is_main_store = 0");
		list.add(companyInfoId);

		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (type != null && type.length > 0) {
			String t = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					t += type[i];
				else
					t += type[i] + ",";
			}
			sql.append(" and s.type in (" + t + ")");
		}
		if (saleOrgId != null) {
			sql.append(" and s.sale_org = ?");
			list.add(saleOrgId);
		}

		
		
		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			sql.append(" and xs.id in (" + os + ")");
		}

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and org.id in (" + os + ")");
		}
		
		// 获取用户
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and s.id in (" + storeAuth + ")");
			}
		} 
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and xso.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and xso.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and xs.id in (" + sbuIds + ")");
			}else{
				sql.append(" and xs.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and org.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and org.id is null");
			}
		}
		
		sql.append(" order by s.create_date desc,s.id ");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
			
		}
		System.out.println("客户余额查看："+sql.toString());
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);

		return page;
	}

	public BigDecimal findBalance(Long storeId, Long organizationId, Long sbuId) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sql.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");

		Date now = new Date();
		sql.setLength(0);
		sql.append("select");
		sql.append("  (ifnull(sb.begin_amount,0) + ifnull(cr.credit_amount,0) + ifnull(dr.deposit_amount,0)");
		sql.append("  - ifnull(o.amount_paid,0) + ifnull(oc.amount_closed,0) + ifnull(r.return_amount,0)) balance");
		sql.append(" from xx_store s");
		// 期初 暂不作处理
		sql.append(" left join");
		sql.append("  xx_store_balance sb on s.id = sb.store and sb.company_info_id = ? and sb.balance_date = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		// 客户授信
		sql.append(" left join");
		sql.append("  (select a.store, sum(round(ifnull(a.actual_amount, 0),2)) credit_amount");
		sql.append("  from xx_credit_recharge a,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.type = 1 and xo.id=a.organization and a.sbu=sb.id  and a.doc_status = 2 and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");
		sql.append("  and a.store = ?");
		sql.append("  and a.sbu = ?");
		list.add(now);
		list.add(now);
		list.add(companyInfoId);
		list.add(storeId);
		list.add(sbuId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) cr on cr.store = s.id");
		// 销售回款
		sql.append(" left join");
		sql.append("  (select a.stores, sum(round(ifnull(a.actual_amount, 0),2)) deposit_amount");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b");
		sql.append("  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		sql.append("  and a.stores = ?");
		sql.append("  and a.sbu = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		list.add(sbuId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) dr on dr.stores = s.id");
		// 已下达订单
		sql.append(" left join");
		sql.append("  (select a.stores, sum(round(b.price * b.quantity,2)) amount_paid");
		sql.append("  from xx_order a, xx_order_item b");
		sql.append("  where a.id = b.orders and a.order_status in (2, 5, 6) and a.order_type = 2");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append("  and a.stores = ?");
		sql.append("  and a.sbu = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		list.add(sbuId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) o on o.stores = s.id");
		// 已关闭订单
		sql.append(" left join");
		sql.append("  (select a.stores, sum(round(b.price * ci.quantity,2)) amount_closed");
		sql.append("  from xx_order a, xx_order_item b, xx_order_close_item ci,xx_order_close c");
		sql.append(
				"  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append("  and a.stores = ?");
		sql.append("  and a.sbu = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		list.add(sbuId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) oc on oc.stores = s.id");
		// 退货金额
		// sql.append(" left join");
		// sql.append(" (select a.store, sum(a.amount) return_amount");
		// sql.append(" from xx_b2b_returns a");
		//// sql.append(" where a.status in (1,2,3)");
		// sql.append(" where a.status = 4");
		// sql.append(" and a.company_info_id = ?");
		// sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		// sql.append(" and a.store = ?");
		// list.add(companyInfoId);
		// list.add(balanceDate);
		// list.add(storeId);
		// if (organizationId != null) {
		// sql.append(" and a.organization = ?");
		// list.add(organizationId);
		// }
		// sql.append(" ) r on r.store = s.id");
		// sql.append(" where s.company_info_id = ? and s.is_main_store = 0 and
		// s.id = ?");
		// list.add(companyInfoId);
		// list.add(storeId);

		// 退货金额
		sql.append(" left join");
		sql.append(" (select a.store, sum(round(b.price * b.returned_quantity,2)) return_amount");
		sql.append(" from xx_b2b_returns_item b , xx_b2b_returns a ");
		sql.append(" where b.b2b_returns=a.id  and a.status in (1,2,3,4,5) ");
		sql.append(" and a.company_info_id = ?");
		sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append(" and a.store = ?");
		sql.append("  and a.sbu = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		list.add(sbuId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append(" ) r on r.store = s.id");
		sql.append(" where s.company_info_id = ? and s.is_main_store = 0 and s.id = ?");
		list.add(companyInfoId);
		list.add(storeId);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		BigDecimal balance = getNativeDao().findBigDecimal(sql.toString(), objs);

		return balance;
	}

	public BigDecimal findCheckBalance(Long storeId, Long organizationId) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sql.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");

		Date now = new Date();
		sql.setLength(0);
		sql.append("select");
		sql.append(
				"  (ifnull(sb.begin_amount,0) + ifnull(cr.credit_amount,0) + ifnull(dr.deposit_amount,0) - ifnull(o.amount_paid,0) + ifnull(oc.amount_closed,0) + ifnull(r.return_amount,0)) balance");
		sql.append(" from xx_store s");
		// 期初 暂不作处理
		sql.append(" left join");
		sql.append("  xx_store_balance sb on s.id = sb.store and sb.company_info_id = ? and sb.balance_date = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		// 客户授信
		sql.append(" left join");
		sql.append("  (select a.store, sum(ifnull(a.actual_amount, 0)) credit_amount");
		sql.append("  from xx_credit_recharge a");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");
		sql.append("  and a.store = ?");
		list.add(now);
		list.add(now);
		list.add(companyInfoId);
		list.add(storeId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) cr on cr.store = s.id");
		// 销售回款
		sql.append(" left join");
		sql.append("  (select a.stores, sum(ifnull(a.actual_amount, 0)) deposit_amount");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b");
		sql.append("  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		sql.append("  and a.stores = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) dr on dr.stores = s.id");
		// 已下达订单
		sql.append(" left join");
		sql.append("  (select a.stores, sum(round(ifnull(b.price * b.quantity,0),2)) amount_paid");
		sql.append("  from xx_order a, xx_order_item b");
		sql.append("  where a.id = b.orders and a.order_status = 6 and a.order_type = 2");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append("  and a.stores = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) o on o.stores = s.id");
		// 已关闭订单
		sql.append(" left join");
		sql.append("  (select a.stores, sum(round(ifnull(b.price * ci.quantity,0),2)) amount_closed");
		sql.append("  from xx_order a, xx_order_item b, xx_order_close_item ci,xx_order_close c");
		sql.append(
				"  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append("  and a.stores = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) oc on oc.stores = s.id");
		// 退货金额
		// sql.append(" left join");
		// sql.append(" (select a.store, sum(a.amount) return_amount");
		// sql.append(" from xx_b2b_returns a");
		//// sql.append(" where a.status in (1,2,3)");
		// sql.append(" where a.status = 4");
		// sql.append(" and a.company_info_id = ?");
		// sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		// sql.append(" and a.store = ?");
		// list.add(companyInfoId);
		// list.add(balanceDate);
		// list.add(storeId);
		// if (organizationId != null) {
		// sql.append(" and a.organization = ?");
		// list.add(organizationId);
		// }
		// sql.append(" ) r on r.store = s.id");
		// sql.append(" where s.company_info_id = ? and s.is_main_store = 0 and
		// s.id = ?");
		// list.add(companyInfoId);
		// list.add(storeId);

		// 退货金额
		sql.append(" left join");
		sql.append(" (select a.store, sum(round(b.price * b.returned_quantity,2)) return_amount");
		sql.append(" from xx_b2b_returns_item b , xx_b2b_returns a ");
		sql.append(" where b.b2b_returns=a.id");
		sql.append(" and a.company_info_id = ?");
		sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append(" group by a.store) r on r.store = s.id");
		sql.append(" where s.company_info_id = ? and s.is_main_store = 0");
		list.add(companyInfoId);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		BigDecimal balance = getNativeDao().findBigDecimal(sql.toString(), objs);

		return balance;
	}

	public BigDecimal findEndBalance(Long storeId) {

		Long organizationId = null;
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sql.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");

		Date now = new Date();
		sql.setLength(0);
		sql.append("select");
		sql.append(
				"  (ifnull(sb.begin_amount,0) + ifnull(dr.deposit_amount,0) - ifnull(o.amount_paid,0) + ifnull(oc.amount_closed,0) + ifnull(r.return_amount,0)) balance");
		sql.append(" from xx_store s");
		// 期初 暂不作处理
		sql.append(" left join");
		sql.append("  xx_store_balance sb on s.id = sb.store and sb.company_info_id = ? and sb.balance_date = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		// 客户授信
		sql.append(" left join");
		sql.append("  (select a.store, sum(ifnull(a.actual_amount, 0)) credit_amount");
		sql.append("  from xx_credit_recharge a");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");
		sql.append("  and a.store = ?");
		list.add(now);
		list.add(now);
		list.add(companyInfoId);
		list.add(storeId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) cr on cr.store = s.id");
		// 销售回款
		sql.append(" left join");
		sql.append("  (select a.stores, sum(ifnull(a.actual_amount, 0)) deposit_amount");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b");
		sql.append("  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		sql.append("  and a.stores = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) dr on dr.stores = s.id");
		// 已下达订单
		sql.append(" left join");
		sql.append("  (select a.stores, sum(round(b.price * b.quantity,2)) amount_paid");
		sql.append("  from xx_order a, xx_order_item b");
		sql.append("  where a.id = b.orders and a.order_status in (2,5, 6) and a.order_type = 2");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append("  and a.stores = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) o on o.stores = s.id");
		// 已关闭订单
		sql.append(" left join");
		sql.append("  (select a.stores, sum(round(b.price * ci.quantity,2)) amount_closed");
		sql.append("  from xx_order a, xx_order_item b, xx_order_close_item ci,xx_order_close c");
		sql.append(
				"  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		sql.append("  and a.stores = ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(storeId);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append("  ) oc on oc.stores = s.id");
		// 退货金额
		// sql.append(" left join");
		// sql.append(" (select a.store, sum(a.amount) return_amount");
		// sql.append(" from xx_b2b_returns a");
		// sql.append(" where a.status = 4");
		// sql.append(" and a.company_info_id = ?");
		// sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		// sql.append(" and a.store = ?");
		// list.add(companyInfoId);
		// list.add(balanceDate);
		// list.add(storeId);
		// if (organizationId != null) {
		// sql.append(" and a.organization = ?");
		// list.add(organizationId);
		// }
		// sql.append(" ) r on r.store = s.id");
		// sql.append(" where s.company_info_id = ? and s.is_main_store = 0 and
		// s.id = ?");
		// list.add(companyInfoId);
		// list.add(storeId);

		// 退货金额
		sql.append(" left join");
		sql.append(" (select a.store, sum(round(b.price * b.returned_quantity,2)) return_amount");
		sql.append(" from xx_b2b_returns_item b , xx_b2b_returns a ");
		sql.append(" where b.b2b_returns=a.id");
		sql.append(" and a.company_info_id = ?");
		sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);
		if (organizationId != null) {
			sql.append(" and a.organization = ?");
			list.add(organizationId);
		}
		sql.append(" group by a.store) r on r.store = s.id");
		sql.append(" where s.company_info_id = ? and s.is_main_store = 0");
		list.add(companyInfoId);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		BigDecimal balance = getNativeDao().findBigDecimal(sql.toString(), objs);

		return balance;
	}

	public String getBalanceDate() {// 期货日期（企业创建时间，对账月份必须大于企业创建时间）
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sql.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");
		return balanceDate;

	}

	public List<Map<String, Object>> findList(Long[] ids, Object[] args, Integer page, Integer size) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeId = (Long) args[0];
		Integer[] type = (Integer[]) args[1];
		Long saleOrgId = (Long) args[2];
		Long[] organizationId = (Long[]) args[3];
		Long[] sbuId = (Long[]) args[4];

		String balanceDate = getBalanceDate();

		StringBuilder sql = new StringBuilder();
		Date now = new Date();
		sql.setLength(0);
		sql.append("select");
		sql.append("  s.id,");
		sql.append("  s.out_trade_no,");
		sql.append("  s.name store_name,");
		sql.append("  s.type store_type,");
		sql.append("  xso.name sale_org,");
		sql.append("  xs.name sbu_name,");
		sql.append("  org.name org_name,");
		sql.append("  ifnull(cr.credit_amount,0) credit_amount,");
		sql.append("  ifnull(dr.deposit_amount,0) deposit_amount,");
		sql.append("  ifnull(o.amount_paid,0) amount_paid,");
		sql.append("  ifnull(oc.amount_closed,0) amount_closed,");
		sql.append("  ifnull(r.return_amount,0) return_amount,");
		sql.append("  ifnull(policyEntry.deposit_amount,0) policyEntry_amount,");
		sql.append("  ifnull(sb.begin_amount,0) begin_amount,");
		sql.append(
				"  (ifnull(sb.begin_amount,0) + ifnull(dr.deposit_amount,0)  - ifnull(o.amount_paid,0) + ifnull(oc.amount_closed,0)) end_amount,");
		sql.append(
				"  (ifnull(sb.begin_amount,0) + ifnull(cr.credit_amount,0) + ifnull(dr.deposit_amount,0) + ifnull(policyEntry.deposit_amount,0) - ifnull(o.amount_paid,0) + ifnull(oc.amount_closed,0) + ifnull(r.return_amount,0)) balance");
		sql.append(" from xx_store s");
		sql.append(" left join ");
		sql.append("  xx_sale_org xso on xso.id = s.sale_org ");
		// 期初 暂不处理
		sql.append(" left join");
		sql.append("  xx_store_balance sb on s.id = sb.store and sb.company_info_id = ? and sb.balance_date = ?");
		sql.append(" LEFT JOIN xx_organization org on  org.company_info_id = ?");
		sql.append(" LEFT JOIN xx_sbu  xs ON xs.status=1");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(companyInfoId);
		// 客户授信
		sql.append(" left join");
		sql.append("  (select a.store,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) credit_amount,");
		sql.append("  xo.name organization_name,sb.name sbu_name");
		sql.append("  from xx_credit_recharge a, xx_organization xo,xx_sbu sb ");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and xo.id=a.organization and a.sbu=sb.id and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");
		list.add(now);
		list.add(now);
		list.add(companyInfoId);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.store,a.organization ,a.sbu) cr on cr.store = s.id AND cr.sbu=xs.id and cr.organization=org.id");

		// 销售回款
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) deposit_amount,xo.name organization_name,sb.name sbu_name");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 	and xo.id=a.organization and a.sbu=sb.id and a.recharge_type = b.id and b.value !='政策录入'");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.stores,a.organization,a.sbu) dr on dr.stores = s.id AND xs.id=dr.sbu and dr.organization=org.id");

		// 政策录入
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) deposit_amount,xo.name  organization_name ,sb.name sbu_name");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id 	and xo.id=a.organization and sb.id=a.sbu and b.value ='政策录入'");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.stores,a.organization,a.sbu) policyEntry on policyEntry.stores = s.id and policyEntry.sbu=xs.id and policyEntry.organization=org.id");
		// 已下达订单
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(b.price * b.quantity,2)) amount_paid,xo.name  organization_name ,sb.name sbu_name");
		sql.append("  from xx_order a, xx_order_item b, xx_organization xo, xx_sbu sb");
		sql.append(
				"  where a.id = b.orders and xo.id=a.organization and a.sbu=sb.id and a.order_status in (2, 5, 6) and a.order_type = 2");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append("  group by a.organization,a.stores,a.sbu) o on o.stores = s.id and o.organization=org.id and xs.id=o.sbu");
		// 已关闭订单
		sql.append(" left join");
		sql.append("  (select a.stores,a.organization,a.sbu, sum(round(b.price * ci.quantity,2)) amount_closed,xo.name  organization_name ,sb.name sbu_name");
		sql.append("  from xx_order a, xx_order_item b, xx_order_close_item ci,xx_order_close c,xx_organization xo,xx_sbu sb");
		sql.append("  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		sql.append("  and xo.id=a.organization and a.sbu=sb.id");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(" group by a.organization,a.stores,a.sbu ) oc on oc.stores = s.id and oc.organization=org.id and oc.sbu=xs.id");
	
		// 退货金额
		sql.append(" left join");
		sql.append(" (select a.store,a.organization,a.sbu, sum(round(b.price * b.returned_quantity,2)) return_amount,xo.name  organization_name ,sb.name sbu_name");
		sql.append(" from xx_b2b_returns_item b , xx_b2b_returns a,xx_organization xo,xx_sbu sb ");
		sql.append(" where b.b2b_returns=a.id");
		sql.append(" and  a.organization=xo.id and a.sbu=sb.id");
		sql.append(" and a.company_info_id = ?");
		sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(" group by a.store,a.organization,a.sbu) r on r.store = s.id and r.organization=org.id and r.sbu=xs.id");
		sql.append(" where s.company_info_id = ? and s.is_main_store = 0");
		list.add(companyInfoId);

		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (type != null && type.length > 0) {
			String t = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					t += type[i];
				else
					t += type[i] + ",";
			}
			sql.append(" and s.type in (" + t + ")");
		}
		if (saleOrgId != null) {
			sql.append(" and s.sale_org = ?");
			list.add(saleOrgId);
		}
		
		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			sql.append(" and xs.id in (" + os + ")");
		} 
		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and org.id in (" + os + ")");
		}
		
		// 获取用户
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}

		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and s.id in (" + inIds + ")");
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and xso.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and xso.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and xs.id in (" + sbuIds + ")");
			}else{
				sql.append(" and xs.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and org.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and org.id is null");
			}
		}
		
		
		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);
		
		return maps;
	}

	public Integer countForExcel(Object[] args) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		Long storeId = (Long) args[0];
		Integer[] type = (Integer[]) args[1];
		Long saleOrgId = (Long) args[2];
		Long[] organizationId = (Long[]) args[3];
		Long[] sbuId = (Long[]) args[4];

		StringBuilder sql = new StringBuilder();

		String balanceDate = getBalanceDate();

		Date now = new Date();
		sql.append("select count(1) from (");
		sql.append("select 1");
		sql.append(" from xx_store s");
		sql.append(" left join ");
		sql.append("  xx_sale_org xso on xso.id = s.sale_org ");
		// 期初 暂不处理
		sql.append(" left join");
		sql.append("  xx_store_balance sb on s.id = sb.store and sb.company_info_id = ? and sb.balance_date = ?");
		sql.append(" LEFT JOIN xx_organization org on  org.company_info_id = ?");
		sql.append(" LEFT JOIN xx_sbu  xs ON xs.status=1");
		list.add(companyInfoId);
		list.add(balanceDate);
		list.add(companyInfoId);
		// 客户授信
		sql.append(" left join");
		sql.append("  (select a.store,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) credit_amount,");
		sql.append("  xo.name organization_name,sb.name sbu_name");
		sql.append("  from xx_credit_recharge a, xx_organization xo,xx_sbu sb ");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and xo.id=a.organization and a.sbu=sb.id and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");
		list.add(now);
		list.add(now);
		list.add(companyInfoId);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.store,a.organization ,a.sbu) cr on cr.store = s.id AND cr.sbu=xs.id and cr.organization=org.id");

		// 销售回款
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) deposit_amount,xo.name organization_name,sb.name sbu_name");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 	and xo.id=a.organization and a.sbu=sb.id and a.recharge_type = b.id and b.value !='政策录入'");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.stores,a.organization,a.sbu) dr on dr.stores = s.id AND xs.id=dr.sbu and dr.organization=org.id");

		// 政策录入
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(ifnull(a.actual_amount, 0),2)) deposit_amount,xo.name  organization_name ,sb.name sbu_name");
		sql.append("  from xx_deposit_recharge a, xx_system_dict b,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.type = 1 and a.doc_status = 2 and a.recharge_type = b.id 	and xo.id=a.organization and sb.id=a.sbu and b.value ='政策录入'");
		sql.append("  and b.is_enabled = 1 and a.company_info_id = ?");
		sql.append("  and a.balance_month >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.stores,a.organization,a.sbu) policyEntry on policyEntry.stores = s.id and policyEntry.sbu=xs.id and policyEntry.organization=org.id");
		// 已下达订单
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(b.price * b.quantity,2)) amount_paid,xo.name  organization_name ,sb.name sbu_name");
		sql.append("  from xx_order a, xx_order_item b, xx_organization xo, xx_sbu sb");
		sql.append(
				"  where a.id = b.orders and xo.id=a.organization and a.sbu=sb.id and a.order_status in (2, 5, 6) and a.order_type = 2");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(
				"  group by a.organization,a.stores,a.sbu) o on o.stores = s.id and o.organization=org.id and xs.id=o.sbu");
		// 已关闭订单
		sql.append(" left join");
		sql.append(
				"  (select a.stores,a.organization,a.sbu, sum(round(b.price * ci.quantity,2)) amount_closed,xo.name  organization_name ,sb.name sbu_name");
		sql.append(
				"  from xx_order a, xx_order_item b, xx_order_close_item ci,xx_order_close c,xx_organization xo,xx_sbu sb");
		sql.append(
				"  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		sql.append("  and xo.id=a.organization and a.sbu=sb.id");
		sql.append("  and a.company_info_id = ?");
		sql.append("  and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(" group by a.organization,a.stores,a.sbu ) oc on oc.stores = s.id and oc.organization=org.id and oc.sbu=xs.id");

		// 退货金额
		sql.append(" left join");
		sql.append(" (select a.store,a.organization,a.sbu, sum(round(b.price * b.returned_quantity,2)) return_amount,xo.name  organization_name ,sb.name sbu_name");
		sql.append(" from xx_b2b_returns_item b , xx_b2b_returns a,xx_organization xo,xx_sbu sb ");
		sql.append(" where b.b2b_returns=a.id");
		sql.append(" and  a.organization=xo.id and a.sbu=sb.id");
		sql.append(" and a.company_info_id = ?");
		sql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		list.add(companyInfoId);
		list.add(balanceDate);

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and a.organization in (" + os + ")");
		}

		sql.append(" group by a.store,a.organization,a.sbu) r on r.store = s.id and r.organization=org.id and r.sbu=xs.id");
		sql.append(" where s.company_info_id = ? and s.is_main_store = 0");
		list.add(companyInfoId);

		if (storeId != null) {
			sql.append(" and s.id = ?");
			list.add(storeId);
		}
		if (type != null && type.length > 0) {
			String t = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					t += type[i];
				else
					t += type[i] + ",";
			}
			sql.append(" and s.type in (" + t + ")");
		}
		if (saleOrgId != null) {
			sql.append(" and s.sale_org = ?");
			list.add(saleOrgId);
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			sql.append(" and xs.id in (" + os + ")");
		} 

		if (organizationId != null && organizationId.length > 0) {
			String os = "";
			for (int i = 0; i < organizationId.length; i++) {
				if (i == organizationId.length - 1)
					os += "'" + organizationId[i] + "'";
				else
					os += "'" + organizationId[i] + "'" + ",";
			}
			sql.append(" and org.id in (" + os + ")");
		}
		
		// 获取用户
		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and o.stores in (" + storeAuth + ")");
			}
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and xso.id in (" + saleOrgIds + ")");
			}else{
				sql.append(" and xso.id is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and xs.id in (" + sbuIds + ")");
			}else{
				sql.append(" and xs.id is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and org.id in (" + organizationIdS + ")");
			}else{
				sql.append(" and org.id is null");
			}
		}
		
		
		sql.append(") p");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}

	public Page<Map<String, Object>> findReport(String sn, Integer[] type, Long[] storeId, String firstTime,
			String lastTime, String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		List<Object> contionlist = new ArrayList<Object>();
		List<Object> creditlist = new ArrayList<Object>();
		List<Object> orderlist = new ArrayList<Object>();
		List<Object> returnlist = new ArrayList<Object>();
		List<Object> policyEntrylist = new ArrayList<Object>();
		List<Object> depostRechargelist = new ArrayList<Object>();
		StringBuilder sqlDate = new StringBuilder();
		StringBuilder unionAllsql = new StringBuilder(" union all ");
		StringBuilder selectOrdersql = new StringBuilder();// 查询订单
		StringBuilder selectReturnsql = new StringBuilder();// 查询退货单
		StringBuilder selectDepostRechargesql = new StringBuilder();// 查询回款
		StringBuilder selectPolicyEntrysql = new StringBuilder();// 查询政策录入
		StringBuilder selectCreditRechargesql = new StringBuilder();// 查询授信
		sqlDate.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sqlDate.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");

		Date now = new Date();
		List<Integer> groupBylist = type == null ? null : Arrays.asList(type);// 将string数组转成list,判断分组包含关系

		// 客户授信
		selectCreditRechargesql.append(
				" select 5 'type',a.id, round(ifnull(a.actual_amount, 0),2) amount,a.sn,a.check_date create_date ,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo, ");
		selectCreditRechargesql.append(" sb.name sbu_name ");
		selectCreditRechargesql.append(" from xx_credit_recharge a ");
		selectCreditRechargesql.append("  join xx_organization og on og.id = a.organization ");
		selectCreditRechargesql.append("  join xx_sbu sb on sb.id = a.sbu ");
		selectCreditRechargesql.append("  join xx_store st on st.id = a.store ");
		selectCreditRechargesql.append("  join xx_sale_org so on so.id = a.sale_org ");
		selectCreditRechargesql.append(
				" where a.type = 1 and a.doc_status = 2 and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");

		creditlist.add(now);
		creditlist.add(now);
		creditlist.add(companyInfoId);
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectCreditRechargesql.append("  and a.store in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectCreditRechargesql.append("  and a.sale_org in (" + s + ")");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectCreditRechargesql.append(" and a.sn like ?");
			creditlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectCreditRechargesql.append(" and og.name in (" + os + ")");
		}
		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectCreditRechargesql.append(" and a.sbu in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectCreditRechargesql.append(" and a.check_date >= ?");
			creditlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectCreditRechargesql.append(" and a.check_date < ?");
			creditlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}

		// 销售回款
		selectDepostRechargesql.append(
				" select 3 'type',a.id, round(ifnull(a.actual_amount, 0),2) amount,a.sn,a.check_date create_date,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo,");
		selectDepostRechargesql.append(" sb.name sbu_name ");
		selectDepostRechargesql.append(" from xx_deposit_recharge a");
		selectDepostRechargesql.append("  join xx_organization og on og.id = a.organization ");
		selectDepostRechargesql.append("  join xx_sbu sb on sb.id = a.sbu ");
		selectDepostRechargesql.append("  join xx_store st on st.id = a.stores ");
		selectDepostRechargesql.append("  join xx_sale_org so on so.id = a.sale_org ");
		selectDepostRechargesql.append(
				"  join  xx_system_dict b  on a.recharge_type = b.id and b.is_enabled = 1 and b.value !='政策录入'  ");
		selectDepostRechargesql.append(" where a.type = 1 and a.doc_status = 2 ");
		selectDepostRechargesql.append(" and a.company_info_id = ?");
		selectDepostRechargesql.append(" and a.balance_month >= ?");

		depostRechargelist.add(companyInfoId);
		depostRechargelist.add(balanceDate);
		if (!ConvertUtil.isEmpty(firstTime)) {
			selectDepostRechargesql.append(" and a.check_date >= ?");
			depostRechargelist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectDepostRechargesql.append(" and a.check_date < ?");
			depostRechargelist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectDepostRechargesql.append(" and a.sn like ?");
			depostRechargelist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectDepostRechargesql.append(" and og.name in (" + os + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectDepostRechargesql.append(" and a.sbu in (" + os + ")");
		}

		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectDepostRechargesql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectDepostRechargesql.append("  and a.sale_org in (" + s + ")");
		}

		// 政策录入
		selectPolicyEntrysql.append(
				" select 4 'type',a.id,ifnull(a.actual_amount, 0) amount,a.sn,a.check_date create_date ,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo, ");
		selectPolicyEntrysql.append(" sb.name sbu_name ");
		selectPolicyEntrysql.append(" from xx_deposit_recharge a");
		selectPolicyEntrysql.append("  join xx_organization og on og.id = a.organization ");
		selectPolicyEntrysql.append("  join xx_sbu sb on sb.id = a.sbu ");
		selectPolicyEntrysql.append("  join xx_store st on st.id = a.stores ");
		selectPolicyEntrysql.append("  join xx_sale_org so on so.id = a.sale_org ");
		selectPolicyEntrysql.append(
				"  join  xx_system_dict b  on a.recharge_type = b.id and b.is_enabled = 1 and b.value ='政策录入' ");
		selectPolicyEntrysql.append(" where a.type = 1 and a.doc_status = 2 ");
		selectPolicyEntrysql.append(" and a.company_info_id = ?");
		selectPolicyEntrysql.append(" and a.balance_month >= ?");
		policyEntrylist.add(companyInfoId);
		policyEntrylist.add(balanceDate);
		if (!ConvertUtil.isEmpty(firstTime)) {
			selectPolicyEntrysql.append(" and a.check_date >= ?");
			policyEntrylist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectPolicyEntrysql.append(" and a.check_date < ?");
			policyEntrylist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectPolicyEntrysql.append(" and a.sn like ?");
			policyEntrylist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectPolicyEntrysql.append(" and og.name in (" + os + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectPolicyEntrysql.append(" and a.sbu in (" + os + ")");
		}

		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectPolicyEntrysql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectPolicyEntrysql.append("  and a.sale_org in (" + s + ")");
		}
		// 已下达订单(减去关闭的数量)
		selectOrdersql.append(
				" select 1 'type',a.id,(sum(round(b.price * b.quantity,2))-IFNULL(round(oc.amount_closed,2),0)) amount,a.sn,xs.erp_date create_date,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo, ");
		selectOrdersql.append(" sb.name sbu_name ");
		selectOrdersql.append(" from xx_order a");
		selectOrdersql.append("  join xx_order_item b  on a.id = b.orders ");
		selectOrdersql.append("  join xx_organization og on og.id = a.organization ");
		selectOrdersql.append("  join xx_sbu sb on sb.id = a.sbu ");
		selectOrdersql.append("  join xx_store st on st.id = a.stores ");
		selectOrdersql.append("  join xx_sale_org so on so.id = a.sale_org ");
		// 关联关闭订单行
		selectOrdersql.append(" left join");
		selectOrdersql.append("  (select b.orders, sum(round(b.price * ci.quantity,2)) amount_closed");
		selectOrdersql.append("  from  xx_order a,xx_order_item b, xx_order_close_item ci,xx_order_close c");
		selectOrdersql.append(
				"  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		selectOrdersql.append("  and a.company_info_id = ? ");
		orderlist.add(companyInfoId);
		selectOrdersql.append(" group by  b.orders ) oc on oc.orders = a.id");
		// Erp回传时间
		selectOrdersql.append(" left join");
		selectOrdersql.append("  (select b.id, s.erp_date");
		selectOrdersql.append("  from  xx_shipping_item a,xx_order b,xx_shipping s ");
		selectOrdersql.append("  where a.orders=b.id and a.shipping=s.id ");
		selectOrdersql.append(" group by  a.orders ) xs on xs.id = a.id");

		selectOrdersql.append(" where a.order_status in (2, 5, 6) and a.order_type = 2 ");
		selectOrdersql.append(" and a.company_info_id = ?");
		selectOrdersql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		orderlist.add(companyInfoId);
		orderlist.add(balanceDate);
		if (!ConvertUtil.isEmpty(sn)) {
			selectOrdersql.append(" and a.sn like ?");
			orderlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectOrdersql.append(" and og.name in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectOrdersql.append(" and xs.erp_date >= ?");
			orderlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectOrdersql.append(" and xs.erp_date < ?");
			orderlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectOrdersql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectOrdersql.append("  and a.sale_org in (" + s + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectOrdersql.append(" and a.sbu in (" + os + ")");
		}
		selectOrdersql.append(" group by a.id ");
		// 退货单
		selectReturnsql.append(
				" select 2 'type',a.id, sum(round(b.price * b.returned_quantity,2)) amount,a.sn,a.erp_date create_date,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo, ");
		selectReturnsql.append(" sb.name sbu_name ");
		selectReturnsql.append(" from xx_b2b_returns_item b ");
		selectReturnsql.append("  join xx_b2b_returns a  on b.b2b_returns= a.id ");
		selectReturnsql.append("  join xx_organization og on og.id = a.organization ");
		selectReturnsql.append("  join xx_sbu sb on sb.id = a.sbu ");
		selectReturnsql.append("  join xx_store st on st.id = a.store");
		selectReturnsql.append("  join xx_sale_org so on so.id = a.sale_org ");
		selectReturnsql.append(" where st.is_main_store = 0 and a.company_info_id = ?");
		selectReturnsql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		returnlist.add(companyInfoId);
		returnlist.add(balanceDate);
		if (!ConvertUtil.isEmpty(sn)) {
			selectReturnsql.append(" and a.sn like ?");
			returnlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectReturnsql.append(" and og.name in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectReturnsql.append(" and a.erp_date >= ?");
			returnlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectReturnsql.append(" and a.erp_date < ?");
			returnlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectReturnsql.append("  and a.store in (" + s + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectReturnsql.append(" and a.sbu in (" + os + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectReturnsql.append("  and a.sale_org in (" + s + ")");
		}
		selectReturnsql.append(" group by a.id  ");
		// 1,2,3,4,5的树形图
		if (type != null) {
			if (groupBylist.contains(1)) {// 查询订单
				if (groupBylist.contains(2)) {// 查询退货单
					if (groupBylist.contains(3)) {// 查询销售回款
						if (groupBylist.contains(4)) {// 查询政策录入
							if (groupBylist.contains(5)) {// 客户授信
								contionlist.addAll(creditlist);
								contionlist.addAll(depostRechargelist);
								contionlist.addAll(policyEntrylist);
								contionlist.addAll(orderlist);
								contionlist.addAll(returnlist);
								Object[] objs = new Object[contionlist.size()];
								for (int i = 0; i < contionlist.size(); i++) {
									objs[i] = contionlist.get(i);
								}
								Page<Map<String, Object>> page = getNativeDao().findPageMap(selectCreditRechargesql
										.append(unionAllsql).append(selectDepostRechargesql).append(unionAllsql)
										.append(selectPolicyEntrysql).append(unionAllsql).append(selectOrdersql)
										.append(unionAllsql).append(selectReturnsql).toString(), objs, pageable);

								return page;
							} else {// 1,2,3,4
								contionlist.addAll(orderlist);
								contionlist.addAll(returnlist);
								contionlist.addAll(depostRechargelist);
								contionlist.addAll(policyEntrylist);
								Object[] objs = new Object[contionlist.size()];
								for (int i = 0; i < contionlist.size(); i++) {
									objs[i] = contionlist.get(i);
								}
								Page<Map<String, Object>> page = getNativeDao()
										.findPageMap(
												selectOrdersql.append(unionAllsql).append(selectReturnsql)
														.append(unionAllsql).append(selectDepostRechargesql)
														.append(unionAllsql).append(selectPolicyEntrysql).toString(),
												objs, pageable);

								return page;
							}
						} else if (groupBylist.contains(5)) {// 1,2,3,5
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Page<Map<String, Object>> page = getNativeDao()
									.findPageMap(
											selectOrdersql.append(unionAllsql).append(selectReturnsql)
													.append(unionAllsql).append(selectDepostRechargesql)
													.append(unionAllsql).append(selectCreditRechargesql).toString(),
											objs, pageable);

							return page;
						} else {// 1,2,3
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Page<Map<String, Object>> page = getNativeDao()
									.findPageMap(
											selectOrdersql.append(unionAllsql).append(selectReturnsql)
													.append(unionAllsql).append(selectDepostRechargesql).toString(),
											objs, pageable);

							return page;
						}
					} else if (groupBylist.contains(4)) {// 1,2,4(,5)
						if (groupBylist.contains(5)) {// 1,2,4,5
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Page<Map<String, Object>> page = getNativeDao()
									.findPageMap(
											selectOrdersql.append(unionAllsql).append(selectReturnsql)
													.append(unionAllsql).append(selectPolicyEntrysql)
													.append(unionAllsql).append(selectCreditRechargesql).toString(),
											objs, pageable);
							return page;
						} else {// 1,2,4
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Page<Map<String, Object>> page = getNativeDao()
									.findPageMap(
											selectOrdersql.append(unionAllsql).append(selectReturnsql)
													.append(unionAllsql).append(selectPolicyEntrysql).toString(),
											objs, pageable);
							return page;
						}

					} else if (groupBylist.contains(5)) {// 1,2,5
						contionlist.addAll(orderlist);
						contionlist.addAll(returnlist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao().findPageMap(selectOrdersql.append(unionAllsql)
								.append(selectReturnsql).append(unionAllsql).append(selectCreditRechargesql).toString(),
								objs, pageable);
						return page;
					} else {// 1,2
						contionlist.addAll(orderlist);
						contionlist.addAll(returnlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao().findPageMap(
								selectOrdersql.append(unionAllsql).append(selectReturnsql).toString(), objs, pageable);
						return page;
					}

				} else if (groupBylist.contains(3)) {// 1,3
					if (groupBylist.contains(4)) {// 1,3,4(,5)
						if (groupBylist.contains(5)) {// 1,3,4,5
							contionlist.addAll(orderlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Page<Map<String, Object>> page = getNativeDao()
									.findPageMap(
											selectOrdersql.append(unionAllsql).append(selectDepostRechargesql)
													.append(unionAllsql).append(selectPolicyEntrysql)
													.append(unionAllsql).append(selectCreditRechargesql).toString(),
											objs, pageable);

							return page;
						} else {// 1,3,4
							contionlist.addAll(orderlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Page<Map<String, Object>> page = getNativeDao().findPageMap(
									selectOrdersql.append(unionAllsql).append(selectDepostRechargesql)
											.append(unionAllsql).append(selectPolicyEntrysql).toString(),
									objs, pageable);

							return page;
						}

					} else if (groupBylist.contains(5)) {// 1,3,5
						contionlist.addAll(orderlist);
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao()
								.findPageMap(
										selectOrdersql.append(unionAllsql).append(selectDepostRechargesql)
												.append(unionAllsql).append(selectCreditRechargesql).toString(),
										objs, pageable);

						return page;
					} else {
						contionlist.addAll(orderlist);
						contionlist.addAll(depostRechargelist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao().findPageMap(
								selectOrdersql.append(unionAllsql).append(selectDepostRechargesql).toString(), objs,
								pageable);

						return page;
					}

				} else if (groupBylist.contains(4)) {// 1,4(,5)
					if (groupBylist.contains(5)) {
						contionlist.addAll(orderlist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao()
								.findPageMap(
										selectOrdersql.append(unionAllsql).append(selectPolicyEntrysql)
												.append(unionAllsql).append(selectCreditRechargesql).toString(),
										objs, pageable);
						return page;
					} else {// 1,4
						contionlist.addAll(orderlist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao().findPageMap(
								selectOrdersql.append(unionAllsql).append(selectPolicyEntrysql).toString(), objs,
								pageable);
						return page;
					}
				} else if (groupBylist.contains(5)) {// 1,5
					contionlist.addAll(orderlist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Page<Map<String, Object>> page = getNativeDao().findPageMap(
							selectOrdersql.append(unionAllsql).append(selectCreditRechargesql).toString(), objs,
							pageable);
					return page;
				} else {// 1
					contionlist.addAll(orderlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Page<Map<String, Object>> page = getNativeDao().findPageMap(selectOrdersql.toString(), objs,
							pageable);
					return page;
				}
			} else if (groupBylist.contains(2)) {// 不包含1
				if (groupBylist.contains(3)) {// 查询销售回款
					if (groupBylist.contains(4)) {// 查询政策录入
						if (groupBylist.contains(5)) {// 2,3,4,5
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Page<Map<String, Object>> page = getNativeDao()
									.findPageMap(
											selectReturnsql.append(unionAllsql).append(selectDepostRechargesql)
													.append(unionAllsql).append(selectPolicyEntrysql)
													.append(unionAllsql).append(selectCreditRechargesql).toString(),
											objs, pageable);

							return page;
						} else {// 2,3,4
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Page<Map<String, Object>> page = getNativeDao().findPageMap(
									selectReturnsql.append(unionAllsql).append(selectDepostRechargesql)
											.append(unionAllsql).append(selectPolicyEntrysql).toString(),
									objs, pageable);

							return page;
						}
					} else if (groupBylist.contains(5)) {// 2,3,5
						contionlist.addAll(returnlist);
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao()
								.findPageMap(
										selectReturnsql.append(unionAllsql).append(selectDepostRechargesql)
												.append(unionAllsql).append(selectCreditRechargesql).toString(),
										objs, pageable);

						return page;
					} else {// 2,3
						contionlist.addAll(returnlist);
						contionlist.addAll(depostRechargelist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao().findPageMap(
								selectReturnsql.append(unionAllsql).append(selectDepostRechargesql).toString(), objs,
								pageable);

						return page;
					}
				} else if (groupBylist.contains(4)) {// 2,4(,5)
					if (groupBylist.contains(5)) {// 2,4,5
						contionlist.addAll(returnlist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao()
								.findPageMap(
										selectReturnsql.append(unionAllsql).append(selectPolicyEntrysql)
												.append(unionAllsql).append(selectCreditRechargesql).toString(),
										objs, pageable);
						return page;
					} else {// 2,4
						contionlist.addAll(returnlist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao().findPageMap(
								selectReturnsql.append(unionAllsql).append(selectPolicyEntrysql).toString(), objs,
								pageable);
						return page;
					}

				} else if (groupBylist.contains(5)) {// 2,5
					contionlist.addAll(returnlist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Page<Map<String, Object>> page = getNativeDao().findPageMap(
							selectReturnsql.append(unionAllsql).append(selectCreditRechargesql).toString(), objs,
							pageable);
					return page;
				} else {// 2
					contionlist.addAll(returnlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Page<Map<String, Object>> page = getNativeDao().findPageMap(selectReturnsql.toString(), objs,
							pageable);
					return page;
				}
			} else if (groupBylist.contains(3)) {// 不包含1,2
				if (groupBylist.contains(4)) {
					if (groupBylist.contains(5)) {// 3,4,5
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao().findPageMap(
								selectDepostRechargesql.append(unionAllsql).append(selectPolicyEntrysql)
										.append(unionAllsql).append(selectCreditRechargesql).toString(),
								objs, pageable);

						return page;
					} else {// 3,4
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Page<Map<String, Object>> page = getNativeDao().findPageMap(
								selectDepostRechargesql.append(unionAllsql).append(selectPolicyEntrysql).toString(),
								objs, pageable);

						return page;

					}
				} else if (groupBylist.contains(5)) {// 3,5
					contionlist.addAll(depostRechargelist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Page<Map<String, Object>> page = getNativeDao().findPageMap(
							selectDepostRechargesql.append(unionAllsql).append(selectCreditRechargesql).toString(),
							objs, pageable);

					return page;
				} else {// 3
					contionlist.addAll(depostRechargelist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Page<Map<String, Object>> page = getNativeDao().findPageMap(selectDepostRechargesql.toString(),
							objs, pageable);

					return page;
				}

			} else if (groupBylist.contains(4)) {// 不包含1,2,3
				if (groupBylist.contains(5)) {// 4,5
					contionlist.addAll(policyEntrylist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Page<Map<String, Object>> page = getNativeDao().findPageMap(
							selectPolicyEntrysql.append(unionAllsql).append(selectCreditRechargesql).toString(), objs,
							pageable);

					return page;
				} else {// 4
					contionlist.addAll(policyEntrylist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Page<Map<String, Object>> page = getNativeDao().findPageMap(selectPolicyEntrysql.toString(), objs,
							pageable);

					return page;

				}
			} else if (groupBylist.contains(5)) {// 不包含1,2,3,4
				contionlist.addAll(creditlist);
				Object[] objs = new Object[contionlist.size()];
				for (int i = 0; i < contionlist.size(); i++) {
					objs[i] = contionlist.get(i);
				}
				Page<Map<String, Object>> page = getNativeDao().findPageMap(selectCreditRechargesql.toString(), objs,
						pageable);
				return page;

			} else {
				contionlist.addAll(creditlist);
				contionlist.addAll(depostRechargelist);
				contionlist.addAll(policyEntrylist);
				contionlist.addAll(orderlist);
				contionlist.addAll(returnlist);
				Object[] objs = new Object[contionlist.size()];
				for (int i = 0; i < contionlist.size(); i++) {
					objs[i] = contionlist.get(i);
				}
				Page<Map<String, Object>> page = getNativeDao()
						.findPageMap(
								selectCreditRechargesql.append(unionAllsql).append(selectDepostRechargesql)
										.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
										.append(selectOrdersql).append(unionAllsql).append(selectReturnsql).toString(),
								objs, pageable);

				return page;
			}
		} else {
			contionlist.addAll(creditlist);
			contionlist.addAll(depostRechargelist);
			contionlist.addAll(policyEntrylist);
			contionlist.addAll(orderlist);
			contionlist.addAll(returnlist);
			Object[] objs = new Object[contionlist.size()];
			for (int i = 0; i < contionlist.size(); i++) {
				objs[i] = contionlist.get(i);
			}

			Page<Map<String, Object>> page = getNativeDao()
					.findPageMap(
							selectCreditRechargesql.append(unionAllsql).append(selectDepostRechargesql)
									.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
									.append(selectOrdersql).append(unionAllsql).append(selectReturnsql).toString(),
							objs, pageable);

			return page;
		}

	}

	public Integer countReports(String sn, Integer[] type, Long[] storeId, String firstTime, String lastTime,
			String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable, Integer page, Integer size) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		List<Object> contionlist = new ArrayList<Object>();
		List<Object> creditlist = new ArrayList<Object>();
		List<Object> orderlist = new ArrayList<Object>();
		List<Object> returnlist = new ArrayList<Object>();
		List<Object> policyEntrylist = new ArrayList<Object>();
		List<Object> depostRechargelist = new ArrayList<Object>();
		StringBuilder sqlDate = new StringBuilder();
		StringBuilder unionAllsql = new StringBuilder(" union all ");
		StringBuilder selectOrdersql = new StringBuilder();// 查询订单
		StringBuilder selectReturnsql = new StringBuilder();// 查询退货单
		StringBuilder selectDepostRechargesql = new StringBuilder();// 查询回款
		StringBuilder selectPolicyEntrysql = new StringBuilder();// 查询政策录入
		StringBuilder selectCreditRechargesql = new StringBuilder();// 查询授信
		sqlDate.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sqlDate.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");

		Date now = new Date();
		List<Integer> groupBylist = type == null ? null : Arrays.asList(type);// 将string数组转成list,判断分组包含关系
		// 客户授信
		selectCreditRechargesql.append(" select count(1) num ");
		selectCreditRechargesql.append(" from xx_credit_recharge a ");
		selectCreditRechargesql.append(" left join xx_organization og on og.id = a.organization ");
		selectCreditRechargesql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectCreditRechargesql.append(" left join xx_store st on st.id = a.store ");
		selectCreditRechargesql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		selectCreditRechargesql.append(
				" where a.type = 1 and a.doc_status = 2 and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");

		creditlist.add(now);
		creditlist.add(now);
		creditlist.add(companyInfoId);
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectCreditRechargesql.append("  and a.store in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectCreditRechargesql.append("  and a.sale_org in (" + s + ")");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectCreditRechargesql.append(" and a.sn like ?");
			creditlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectCreditRechargesql.append(" and og.name in (" + os + ")");
		}
		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectCreditRechargesql.append(" and a.sbu in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectCreditRechargesql.append(" and a.check_date >= ?");
			creditlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectCreditRechargesql.append(" and a.check_date < ?");
			creditlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}

		// 销售回款
		selectDepostRechargesql.append(" select count(1) num ");
		selectDepostRechargesql.append(" from xx_deposit_recharge a");
		selectDepostRechargesql.append(" left join xx_organization og on og.id = a.organization ");
		selectDepostRechargesql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectDepostRechargesql.append(" left join xx_store st on st.id = a.stores ");
		selectDepostRechargesql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		selectDepostRechargesql.append(
				" left join  xx_system_dict b  on a.recharge_type = b.id and b.is_enabled = 1 and b.value !='政策录入' ");
		selectDepostRechargesql.append(" where a.type = 1 and a.doc_status = 2 ");
		selectDepostRechargesql.append(" and a.company_info_id = ?");
		selectDepostRechargesql.append(" and a.balance_month >= ?");

		depostRechargelist.add(companyInfoId);
		depostRechargelist.add(balanceDate);
		if (!ConvertUtil.isEmpty(firstTime)) {
			selectDepostRechargesql.append(" and a.check_date >= ?");
			depostRechargelist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectDepostRechargesql.append(" and a.check_date < ?");
			depostRechargelist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectDepostRechargesql.append(" and a.sn like ?");
			depostRechargelist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectDepostRechargesql.append(" and og.name in (" + os + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectDepostRechargesql.append(" and a.sbu in (" + os + ")");
		}

		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectDepostRechargesql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectDepostRechargesql.append("  and a.sale_org in (" + s + ")");
		}

		// 政策录入
		selectPolicyEntrysql.append(" select count(1) num ");
		selectPolicyEntrysql.append(" from xx_deposit_recharge a");
		selectPolicyEntrysql.append(" left join xx_organization og on og.id = a.organization ");
		selectPolicyEntrysql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectPolicyEntrysql.append(" left join xx_store st on st.id = a.stores ");
		selectPolicyEntrysql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		selectPolicyEntrysql.append(
				" left join  xx_system_dict b  on a.recharge_type = b.id and b.is_enabled = 1 and b.value ='政策录入' ");
		selectPolicyEntrysql.append(" where a.type = 1 and a.doc_status = 2 ");
		selectPolicyEntrysql.append(" and a.company_info_id = ?");
		selectPolicyEntrysql.append(" and a.balance_month >= ?");
		policyEntrylist.add(companyInfoId);
		policyEntrylist.add(balanceDate);
		if (!ConvertUtil.isEmpty(firstTime)) {
			selectPolicyEntrysql.append(" and a.check_date >= ?");
			policyEntrylist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectPolicyEntrysql.append(" and a.check_date < ?");
			policyEntrylist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectPolicyEntrysql.append(" and a.sn like ?");
			policyEntrylist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectPolicyEntrysql.append(" and og.name in (" + os + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectPolicyEntrysql.append(" and a.sbu in (" + os + ")");
		}

		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectPolicyEntrysql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectPolicyEntrysql.append("  and a.sale_org in (" + s + ")");
		}
		// 已下达订单(减去关闭的数量)
		selectOrdersql.append(" select count(1) num ");
		selectOrdersql.append(" from xx_order a");
		selectOrdersql.append(" left join xx_order_item b  on a.id = b.orders ");
		selectOrdersql.append(" left join xx_organization og on og.id = a.organization ");
		selectOrdersql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectOrdersql.append(" left join xx_store st on st.id = a.stores ");
		selectOrdersql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		// 关联关闭订单行
		selectOrdersql.append(" left join");
		selectOrdersql.append("  (select b.orders, sum(round(b.price * ci.quantity,2)) amount_closed");
		selectOrdersql.append("  from  xx_order a,xx_order_item b, xx_order_close_item ci,xx_order_close c");
		selectOrdersql.append(
				"  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		selectOrdersql.append("  and a.company_info_id = ? ");
		orderlist.add(companyInfoId);
		selectOrdersql.append(" group by  b.orders ) oc on oc.orders = a.id");
		// Erp回传时间
		selectOrdersql.append(" left join");
		selectOrdersql.append("  (select b.id, s.erp_date");
		selectOrdersql.append("  from  xx_shipping_item a,xx_order b,xx_shipping s ");
		selectOrdersql.append("  where a.orders=b.id and a.shipping=s.id ");
		selectOrdersql.append(" group by  a.orders ) xs on xs.id = a.id");

		selectOrdersql.append(" where a.order_status in (2, 5, 6) and a.order_type = 2 ");
		selectOrdersql.append(" and a.company_info_id = ?");
		selectOrdersql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		orderlist.add(companyInfoId);
		orderlist.add(balanceDate);
		if (!ConvertUtil.isEmpty(sn)) {
			selectOrdersql.append(" and a.sn like ?");
			orderlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectOrdersql.append(" and og.name in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectOrdersql.append(" and xs.erp_date >= ?");
			orderlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectOrdersql.append(" and xs.erp_date < ?");
			orderlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectOrdersql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectOrdersql.append("  and a.sale_org in (" + s + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectOrdersql.append(" and a.sbu in (" + os + ")");
		}
		selectOrdersql.append(" group by a.id ");
		// 退货单
		selectReturnsql.append(" select count(1) num ");
		selectReturnsql.append(" from xx_b2b_returns_item b ");
		selectReturnsql.append(" left join xx_b2b_returns a  on b.b2b_returns= a.id ");
		selectReturnsql.append(" left join xx_organization og on og.id = a.organization ");
		selectReturnsql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectReturnsql.append(" left join xx_store st on st.id = a.store");
		selectReturnsql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		selectReturnsql.append(" where st.is_main_store = 0 and a.company_info_id = ?");
		selectReturnsql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		returnlist.add(companyInfoId);
		returnlist.add(balanceDate);
		if (!ConvertUtil.isEmpty(sn)) {
			selectReturnsql.append(" and a.sn like ?");
			returnlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectReturnsql.append(" and og.name in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectReturnsql.append(" and a.erp_date >= ?");
			returnlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectReturnsql.append(" and a.erp_date < ?");
			returnlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectReturnsql.append("  and a.store in (" + s + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectReturnsql.append(" and a.sbu in (" + os + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectReturnsql.append("  and a.sale_org in (" + s + ")");
		}
		selectReturnsql.append(" group by a.id  ");
		// 1,2,3,4,5的树形图
		if (type != null) {
			if (groupBylist.contains(1)) {// 查询订单
				if (groupBylist.contains(2)) {// 查询退货单
					if (groupBylist.contains(3)) {// 查询销售回款
						if (groupBylist.contains(4)) {// 查询政策录入
							if (groupBylist.contains(5)) {// 客户授信
								contionlist.addAll(creditlist);
								contionlist.addAll(depostRechargelist);
								contionlist.addAll(policyEntrylist);
								contionlist.addAll(orderlist);
								contionlist.addAll(returnlist);
								Object[] objs = new Object[contionlist.size()];
								for (int i = 0; i < contionlist.size(); i++) {
									objs[i] = contionlist.get(i);
								}
								Integer count = getNativeDao()
										.findInt("select sum(num) from ( " + selectCreditRechargesql.append(unionAllsql)
												.append(selectDepostRechargesql).append(unionAllsql)
												.append(selectPolicyEntrysql).append(unionAllsql).append(selectOrdersql)
												.append(unionAllsql).append(selectReturnsql).toString() + ") t ", objs);

								return count;
							} else {// 1,2,3,4
								contionlist.addAll(orderlist);
								contionlist.addAll(returnlist);
								contionlist.addAll(depostRechargelist);
								contionlist.addAll(policyEntrylist);
								Object[] objs = new Object[contionlist.size()];
								for (int i = 0; i < contionlist.size(); i++) {
									objs[i] = contionlist.get(i);
								}
								Integer count = getNativeDao().findInt(
										"select sum(num) from ( "
												+ selectOrdersql.append(unionAllsql).append(selectReturnsql)
														.append(unionAllsql).append(selectDepostRechargesql)
														.append(unionAllsql).append(selectPolicyEntrysql).toString()
												+ " ) t ",
										objs);

								return count;
							}
						} else if (groupBylist.contains(5)) {// 1,2,3,5
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Integer count = getNativeDao().findInt(
									"select sum(num) from ( "
											+ selectOrdersql.append(unionAllsql).append(selectReturnsql)
													.append(unionAllsql).append(selectDepostRechargesql)
													.append(unionAllsql).append(selectCreditRechargesql).toString()
											+ " ) t ",
									objs);

							return count;
						} else {// 1,2,3
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Integer count = getNativeDao()
									.findInt("select sum(num) from ( "
											+ selectOrdersql.append(unionAllsql).append(selectReturnsql)
													.append(unionAllsql).append(selectDepostRechargesql).toString()
											+ " ) t ", objs);

							return count;
						}
					} else if (groupBylist.contains(4)) {// 1,2,4(,5)
						if (groupBylist.contains(5)) {// 1,2,4,5
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Integer count = getNativeDao().findInt(
									"select sum(num) from ( "
											+ selectOrdersql.append(unionAllsql).append(selectReturnsql)
													.append(unionAllsql).append(selectPolicyEntrysql)
													.append(unionAllsql).append(selectCreditRechargesql).toString()
											+ " ) t ",
									objs);
							return count;
						} else {// 1,2,4
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Integer count = getNativeDao()
									.findInt("select sum(num) from ( "
											+ selectOrdersql.append(unionAllsql).append(selectReturnsql)
													.append(unionAllsql).append(selectPolicyEntrysql).toString()
											+ " ) t ", objs);
							return count;
						}

					} else if (groupBylist.contains(5)) {// 1,2,5
						contionlist.addAll(orderlist);
						contionlist.addAll(returnlist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao().findInt(
								"select sum(num) from ( " + selectOrdersql.append(unionAllsql).append(selectReturnsql)
										.append(unionAllsql).append(selectCreditRechargesql).toString() + " ) t ",
								objs);
						return count;
					} else {// 1,2
						contionlist.addAll(orderlist);
						contionlist.addAll(returnlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao().findInt("select sum(num) from ( "
								+ selectOrdersql.append(unionAllsql).append(selectReturnsql).toString() + " ) t ",
								objs);
						return count;
					}

				} else if (groupBylist.contains(3)) {// 1,3
					if (groupBylist.contains(4)) {// 1,3,4(,5)
						if (groupBylist.contains(5)) {// 1,3,4,5
							contionlist.addAll(orderlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Integer count = getNativeDao().findInt(
									"select sum(num) from ( "
											+ selectOrdersql.append(unionAllsql).append(selectDepostRechargesql)
													.append(unionAllsql).append(selectPolicyEntrysql)
													.append(unionAllsql).append(selectCreditRechargesql).toString()
											+ " ) t ",
									objs);

							return count;
						} else {// 1,3,4
							contionlist.addAll(orderlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Integer count = getNativeDao()
									.findInt("select sum(num) from ( "
											+ selectOrdersql.append(unionAllsql).append(selectDepostRechargesql)
													.append(unionAllsql).append(selectPolicyEntrysql).toString()
											+ " ) t ", objs);

							return count;
						}

					} else if (groupBylist.contains(5)) {// 1,3,5
						contionlist.addAll(orderlist);
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao()
								.findInt("select sum(num) from ( "
										+ selectOrdersql.append(unionAllsql).append(selectDepostRechargesql)
												.append(unionAllsql).append(selectCreditRechargesql).toString()
										+ " ) t ", objs);

						return count;
					} else {
						contionlist.addAll(orderlist);
						contionlist.addAll(depostRechargelist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao().findInt("select sum(num) from ( "
								+ selectOrdersql.append(unionAllsql).append(selectDepostRechargesql).toString()
								+ " ) t ", objs);

						return count;
					}

				} else if (groupBylist.contains(4)) {// 1,4(,5)
					if (groupBylist.contains(5)) {
						contionlist.addAll(orderlist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao()
								.findInt("select sum(num) from ( "
										+ selectOrdersql.append(unionAllsql).append(selectPolicyEntrysql)
												.append(unionAllsql).append(selectCreditRechargesql).toString()
										+ " ) t ", objs);
						return count;
					} else {// 1,4
						contionlist.addAll(orderlist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao().findInt("select sum(num) from ( "
								+ selectOrdersql.append(unionAllsql).append(selectPolicyEntrysql).toString() + " ) t ",
								objs);
						return count;
					}
				} else if (groupBylist.contains(5)) {// 1,5
					contionlist.addAll(orderlist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Integer count = getNativeDao().findInt("select sum(num) from ( "
							+ selectOrdersql.append(unionAllsql).append(selectCreditRechargesql).toString() + " ) t ",
							objs);
					return count;
				} else {// 1
					contionlist.addAll(orderlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Integer count = getNativeDao()
							.findInt("select sum(num) from ( " + selectOrdersql.toString() + " ) t ", objs);
					return count;
				}
			} else if (groupBylist.contains(2)) {// 不包含1
				if (groupBylist.contains(3)) {// 查询销售回款
					if (groupBylist.contains(4)) {// 查询政策录入
						if (groupBylist.contains(5)) {// 2,3,4,5
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Integer count = getNativeDao().findInt(
									"select sum(num) from ( "
											+ selectReturnsql.append(unionAllsql).append(selectDepostRechargesql)
													.append(unionAllsql).append(selectPolicyEntrysql)
													.append(unionAllsql).append(selectCreditRechargesql).toString()
											+ " ) t ",
									objs);

							return count;
						} else {// 2,3,4
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							Integer count = getNativeDao()
									.findInt("select sum(num) from ( "
											+ selectReturnsql.append(unionAllsql).append(selectDepostRechargesql)
													.append(unionAllsql).append(selectPolicyEntrysql).toString()
											+ " ) t ", objs);

							return count;
						}
					} else if (groupBylist.contains(5)) {// 2,3,5
						contionlist.addAll(returnlist);
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao()
								.findInt("select sum(num) from ( "
										+ selectReturnsql.append(unionAllsql).append(selectDepostRechargesql)
												.append(unionAllsql).append(selectCreditRechargesql).toString()
										+ " ) t ", objs);

						return count;
					} else {// 2,3
						contionlist.addAll(returnlist);
						contionlist.addAll(depostRechargelist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao().findInt("select sum(num) from ( "
								+ selectReturnsql.append(unionAllsql).append(selectDepostRechargesql).toString()
								+ " ) t ", objs);

						return count;
					}
				} else if (groupBylist.contains(4)) {// 2,4(,5)
					if (groupBylist.contains(5)) {// 2,4,5
						contionlist.addAll(returnlist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao()
								.findInt("select sum(num) from ( "
										+ selectReturnsql.append(unionAllsql).append(selectPolicyEntrysql)
												.append(unionAllsql).append(selectCreditRechargesql).toString()
										+ " ) t ", objs);
						return count;
					} else {// 2,4
						contionlist.addAll(returnlist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao().findInt("select sum(num) from ( "
								+ selectReturnsql.append(unionAllsql).append(selectPolicyEntrysql).toString() + " ) t ",
								objs);
						return count;
					}

				} else if (groupBylist.contains(5)) {// 2,5
					contionlist.addAll(returnlist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Integer count = getNativeDao().findInt("select sum(num) from ( "
							+ selectReturnsql.append(unionAllsql).append(selectCreditRechargesql).toString() + " ) t ",
							objs);
					return count;
				} else {// 2
					contionlist.addAll(returnlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Integer count = getNativeDao()
							.findInt("select sum(num) from ( " + selectReturnsql.toString() + " ) t ", objs);
					return count;
				}
			} else if (groupBylist.contains(3)) {// 不包含1,2
				if (groupBylist.contains(4)) {
					if (groupBylist.contains(5)) {// 3,4,5
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao()
								.findInt("select sum(num) from ( "
										+ selectDepostRechargesql.append(unionAllsql).append(selectPolicyEntrysql)
												.append(unionAllsql).append(selectCreditRechargesql).toString()
										+ " ) t ", objs);

						return count;
					} else {// 3,4
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						Integer count = getNativeDao().findInt("select sum(num) from ( "
								+ selectDepostRechargesql.append(unionAllsql).append(selectPolicyEntrysql).toString()
								+ " ) t ", objs);

						return count;

					}
				} else if (groupBylist.contains(5)) {// 3,5
					contionlist.addAll(depostRechargelist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Integer count = getNativeDao().findInt("select sum(num) from ( "
							+ selectDepostRechargesql.append(unionAllsql).append(selectCreditRechargesql).toString()
							+ " ) t ", objs);

					return count;
				} else {// 3
					contionlist.addAll(depostRechargelist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Integer count = getNativeDao()
							.findInt("select sum(num) from ( " + selectDepostRechargesql.toString() + " ) t ", objs);

					return count;
				}

			} else if (groupBylist.contains(4)) {// 不包含1,2,3
				if (groupBylist.contains(5)) {// 4,5
					contionlist.addAll(policyEntrylist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Integer count = getNativeDao().findInt("select sum(num) from ( "
							+ selectPolicyEntrysql.append(unionAllsql).append(selectCreditRechargesql).toString()
							+ " ) t ", objs);

					return count;
				} else {// 4
					contionlist.addAll(policyEntrylist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					Integer count = getNativeDao()
							.findInt("select sum(num) from ( " + selectPolicyEntrysql.toString() + " ) t ", objs);

					return count;

				}
			} else if (groupBylist.contains(5)) {// 不包含1,2,3,4
				contionlist.addAll(creditlist);
				Object[] objs = new Object[contionlist.size()];
				for (int i = 0; i < contionlist.size(); i++) {
					objs[i] = contionlist.get(i);
				}
				Integer count = getNativeDao()
						.findInt("select sum(num) from ( " + selectCreditRechargesql.toString() + " ) t ", objs);
				return count;

			} else {
				contionlist.addAll(creditlist);
				contionlist.addAll(depostRechargelist);
				contionlist.addAll(policyEntrylist);
				contionlist.addAll(orderlist);
				contionlist.addAll(returnlist);
				Object[] objs = new Object[contionlist.size()];
				for (int i = 0; i < contionlist.size(); i++) {
					objs[i] = contionlist.get(i);
				}
				Integer count = getNativeDao().findInt("select sum(num) from ( "
						+ selectCreditRechargesql.append(unionAllsql).append(selectDepostRechargesql)
								.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
								.append(selectOrdersql).append(unionAllsql).append(selectReturnsql).toString()
						+ " ) t ", objs);

				return count;
			}
		} else {
			contionlist.addAll(creditlist);
			contionlist.addAll(depostRechargelist);
			contionlist.addAll(policyEntrylist);
			contionlist.addAll(orderlist);
			contionlist.addAll(returnlist);
			Object[] objs = new Object[contionlist.size()];
			for (int i = 0; i < contionlist.size(); i++) {
				objs[i] = contionlist.get(i);
			}
			Integer count = getNativeDao().findInt(
					"select sum(num) from ( "
							+ selectCreditRechargesql.append(unionAllsql).append(selectDepostRechargesql)
									.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
									.append(selectOrdersql).append(unionAllsql).append(selectReturnsql).toString()
							+ " ) t ",
					objs);
			return count;
		}

	}

	public List<Map<String, Object>> findReportList(String sn, Integer[] type, Long[] storeId, String firstTime,
			String lastTime, String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable, Integer page,
			Integer size) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		List<Object> contionlist = new ArrayList<Object>();
		List<Object> creditlist = new ArrayList<Object>();
		List<Object> orderlist = new ArrayList<Object>();
		List<Object> returnlist = new ArrayList<Object>();
		List<Object> policyEntrylist = new ArrayList<Object>();
		List<Object> depostRechargelist = new ArrayList<Object>();
		StringBuilder sqlDate = new StringBuilder();
		StringBuilder unionAllsql = new StringBuilder(" union all ");
		StringBuilder selectOrdersql = new StringBuilder();// 查询订单
		StringBuilder selectReturnsql = new StringBuilder();// 查询退货单
		StringBuilder selectDepostRechargesql = new StringBuilder();// 查询回款
		StringBuilder selectPolicyEntrysql = new StringBuilder();// 查询政策录入
		StringBuilder selectCreditRechargesql = new StringBuilder();// 查询授信
		sqlDate.append("select balance_date from xx_account_parameter where company_info_id = ?");
		String preDate = getNativeDao().findString(sqlDate.toString(), new Object[] { companyInfoId });
		String balanceDate = DateUtil.convert(DateUtil.addDate("MM", 1, DateUtil.convert(preDate, "yyyy-MM")),
				"yyyy-MM");

		Date now = new Date();
		List<Integer> groupBylist = type == null ? null : Arrays.asList(type);// 将string数组转成list,判断分组包含关系
		String limitSql = "";
		if (page != null && size != null) {
			limitSql += (" limit " + (size * (page - 1)) + "," + size);
		}
		// 客户授信
		// 客户授信
		selectCreditRechargesql.append(
				" select 5 'type',a.id, round(ifnull(a.actual_amount, 0),2) amount,a.sn,a.check_date create_date ,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo, ");
		selectCreditRechargesql.append(" sb.name sbu_name ");
		selectCreditRechargesql.append(" from xx_credit_recharge a ");
		selectCreditRechargesql.append(" left join xx_organization og on og.id = a.organization ");
		selectCreditRechargesql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectCreditRechargesql.append(" left join xx_store st on st.id = a.store ");
		selectCreditRechargesql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		selectCreditRechargesql.append(
				" where a.type = 1 and a.doc_status = 2 and a.start_date <= ? and date_add(a.end_date,interval 1 day) > ? and a.company_info_id = ?");

		creditlist.add(now);
		creditlist.add(now);
		creditlist.add(companyInfoId);
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectCreditRechargesql.append("  and a.store in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectCreditRechargesql.append("  and a.sale_org in (" + s + ")");
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectCreditRechargesql.append(" and a.sn like ?");
			creditlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectCreditRechargesql.append(" and og.name in (" + os + ")");
		}
		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectCreditRechargesql.append(" and a.sbu in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectCreditRechargesql.append(" and a.check_date >= ?");
			creditlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectCreditRechargesql.append(" and a.check_date < ?");
			creditlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}

		// 销售回款
		selectDepostRechargesql.append(
				" select 3 'type',a.id, round(ifnull(a.actual_amount, 0),2) amount,a.sn,a.check_date create_date,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo,");
		selectDepostRechargesql.append(" sb.name sbu_name ");
		selectDepostRechargesql.append(" from xx_deposit_recharge a");
		selectDepostRechargesql.append(" left join xx_organization og on og.id = a.organization ");
		selectDepostRechargesql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectDepostRechargesql.append(" left join xx_store st on st.id = a.stores ");
		selectDepostRechargesql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		selectDepostRechargesql.append(
				" left join  xx_system_dict b  on a.recharge_type = b.id and b.is_enabled = 1 and b.value !='政策录入' ");
		selectDepostRechargesql.append(" where a.type = 1 and a.doc_status = 2 ");
		selectDepostRechargesql.append(" and a.company_info_id = ?");
		selectDepostRechargesql.append(" and a.balance_month >= ?");

		depostRechargelist.add(companyInfoId);
		depostRechargelist.add(balanceDate);
		if (!ConvertUtil.isEmpty(firstTime)) {
			selectDepostRechargesql.append(" and a.check_date >= ?");
			depostRechargelist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectDepostRechargesql.append(" and a.check_date < ?");
			depostRechargelist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectDepostRechargesql.append(" and a.sn like ?");
			depostRechargelist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectDepostRechargesql.append(" and og.name in (" + os + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectDepostRechargesql.append(" and a.sbu in (" + os + ")");
		}

		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectDepostRechargesql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectDepostRechargesql.append("  and a.sale_org in (" + s + ")");
		}

		// 政策录入
		selectPolicyEntrysql.append(
				" select 4 'type',a.id,ifnull(a.actual_amount, 0) amount,a.sn,a.check_date create_date ,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo, ");
		selectPolicyEntrysql.append(" sb.name sbu_name ");
		selectPolicyEntrysql.append(" from xx_deposit_recharge a");
		selectPolicyEntrysql.append(" left join xx_organization og on og.id = a.organization ");
		selectPolicyEntrysql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectPolicyEntrysql.append(" left join xx_store st on st.id = a.stores ");
		selectPolicyEntrysql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		selectPolicyEntrysql.append(
				" left join  xx_system_dict b  on a.recharge_type = b.id and b.is_enabled = 1 and b.value ='政策录入' ");
		selectPolicyEntrysql.append(" where a.type = 1 and a.doc_status = 2 ");
		selectPolicyEntrysql.append(" and a.company_info_id = ?");
		selectPolicyEntrysql.append(" and a.balance_month >= ?");
		policyEntrylist.add(companyInfoId);
		policyEntrylist.add(balanceDate);
		if (!ConvertUtil.isEmpty(firstTime)) {
			selectPolicyEntrysql.append(" and a.check_date >= ?");
			policyEntrylist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectPolicyEntrysql.append(" and a.check_date < ?");
			policyEntrylist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (!ConvertUtil.isEmpty(sn)) {
			selectPolicyEntrysql.append(" and a.sn like ?");
			policyEntrylist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectPolicyEntrysql.append(" and og.name in (" + os + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectPolicyEntrysql.append(" and a.sbu in (" + os + ")");
		}

		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectPolicyEntrysql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectPolicyEntrysql.append("  and a.sale_org in (" + s + ")");
		}
		// 已下达订单(减去关闭的数量)
		selectOrdersql.append(
				" select 1 'type',a.id,(sum(round(b.price * b.quantity,2))-IFNULL(round(oc.amount_closed,2),0)) amount,a.sn,xs.erp_date create_date,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo, ");
		selectOrdersql.append(" sb.name sbu_name ");
		selectOrdersql.append(" from xx_order a");
		selectOrdersql.append(" left join xx_order_item b  on a.id = b.orders ");
		selectOrdersql.append(" left join xx_organization og on og.id = a.organization ");
		selectOrdersql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectOrdersql.append(" left join xx_store st on st.id = a.stores ");
		selectOrdersql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		// 关联关闭订单行
		selectOrdersql.append(" left join");
		selectOrdersql.append("  (select b.orders, sum(round(b.price * ci.quantity,2)) amount_closed");
		selectOrdersql.append("  from  xx_order a,xx_order_item b, xx_order_close_item ci,xx_order_close c");
		selectOrdersql.append(
				"  where a.id = b.orders and ci.order_item_id=b.id and ci.order_close=c.id and c.status =1 and a.order_type = 2 and a.order_status <>3");
		selectOrdersql.append("  and a.company_info_id = ? ");
		orderlist.add(companyInfoId);
		selectOrdersql.append(" group by  b.orders ) oc on oc.orders = a.id");
		// Erp回传时间
		selectOrdersql.append(" left join");
		selectOrdersql.append("  (select b.id, s.erp_date");
		selectOrdersql.append("  from  xx_shipping_item a,xx_order b,xx_shipping s ");
		selectOrdersql.append("  where a.orders=b.id and a.shipping=s.id ");
		selectOrdersql.append(" group by  a.orders ) xs on xs.id = a.id");

		selectOrdersql.append(" where a.order_status in (2, 5, 6) and a.order_type = 2 ");
		selectOrdersql.append(" and a.company_info_id = ?");
		selectOrdersql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		orderlist.add(companyInfoId);
		orderlist.add(balanceDate);
		if (!ConvertUtil.isEmpty(sn)) {
			selectOrdersql.append(" and a.sn like ?");
			orderlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectOrdersql.append(" and og.name in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectOrdersql.append(" and xs.erp_date >= ?");
			orderlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectOrdersql.append(" and xs.erp_date < ?");
			orderlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectOrdersql.append("  and a.stores in (" + s + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectOrdersql.append("  and a.sale_org in (" + s + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectOrdersql.append(" and a.sbu in (" + os + ")");
		}
		selectOrdersql.append(" group by a.id ");
		// 退货单
		selectReturnsql.append(
				" select 2 'type',a.id, sum(round(b.price * b.returned_quantity,2)) amount,a.sn,a.erp_date create_date,og.name organization,so.name saleOrg,st.name store,st.out_trade_no,a.memo, ");
		selectReturnsql.append(" sb.name sbu_name ");
		selectReturnsql.append(" from xx_b2b_returns_item b ");
		selectReturnsql.append(" left join xx_b2b_returns a  on b.b2b_returns= a.id ");
		selectReturnsql.append(" left join xx_organization og on og.id = a.organization ");
		selectReturnsql.append(" left join xx_sbu sb on sb.id = a.sbu ");
		selectReturnsql.append(" left join xx_store st on st.id = a.store");
		selectReturnsql.append(" left join xx_sale_org so on so.id = a.sale_org ");
		selectReturnsql.append(" where st.is_main_store = 0 and a.company_info_id = ?");
		selectReturnsql.append(" and date_format(a.create_date,'%Y-%m') >= ?");
		returnlist.add(companyInfoId);
		returnlist.add(balanceDate);
		if (!ConvertUtil.isEmpty(sn)) {
			selectReturnsql.append(" and a.sn like ?");
			returnlist.add("%" + sn + "%");
		}

		if (organizationName != null && organizationName.length > 0) {
			String os = "";
			for (int i = 0; i < organizationName.length; i++) {
				if (i == organizationName.length - 1)
					os += "'" + organizationName[i] + "'";
				else
					os += "'" + organizationName[i] + "'" + ",";
			}
			selectReturnsql.append(" and og.name in (" + os + ")");
		}

		if (!ConvertUtil.isEmpty(firstTime)) {
			selectReturnsql.append(" and a.erp_date >= ?");
			returnlist.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			selectReturnsql.append(" and a.erp_date < ?");
			returnlist.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"), 1));
		}
		if (storeId != null && storeId.length > 0) {
			String s = "";
			for (int i = 0; i < storeId.length; i++) {
				if (i == storeId.length - 1)
					s += storeId[i];
				else
					s += storeId[i] + ",";
			}
			selectReturnsql.append("  and a.store in (" + s + ")");
		}

		if (sbuId != null && sbuId.length > 0) {
			String os = "";
			for (int i = 0; i < sbuId.length; i++) {
				if (i == sbuId.length - 1)
					os += "'" + sbuId[i] + "'";
				else
					os += "'" + sbuId[i] + "'" + ",";
			}
			selectReturnsql.append(" and a.sbu in (" + os + ")");
		}
		if (saleOrgId != null && saleOrgId.length > 0) {
			String s = "";
			for (int i = 0; i < saleOrgId.length; i++) {
				if (i == saleOrgId.length - 1)
					s += saleOrgId[i];
				else
					s += saleOrgId[i] + ",";
			}
			selectReturnsql.append("  and a.sale_org in (" + s + ")");
		}
		selectReturnsql.append(" group by a.id  ");
		// 1,2,3,4,5的树形图
		if (type != null) {
			if (groupBylist.contains(1)) {// 查询订单
				if (groupBylist.contains(2)) {// 查询退货单
					if (groupBylist.contains(3)) {// 查询销售回款
						if (groupBylist.contains(4)) {// 查询政策录入
							if (groupBylist.contains(5)) {// 客户授信
								contionlist.addAll(creditlist);
								contionlist.addAll(depostRechargelist);
								contionlist.addAll(policyEntrylist);
								contionlist.addAll(orderlist);
								contionlist.addAll(returnlist);
								Object[] objs = new Object[contionlist.size()];
								for (int i = 0; i < contionlist.size(); i++) {
									objs[i] = contionlist.get(i);
								}
								List<Map<String, Object>> list = getNativeDao().findListMap(selectCreditRechargesql
										.append(unionAllsql).append(selectDepostRechargesql).append(unionAllsql)
										.append(selectPolicyEntrysql).append(unionAllsql).append(selectOrdersql)
										.append(unionAllsql).append(selectReturnsql).toString() + limitSql, objs, 0);

								return list;
							} else {// 1,2,3,4
								contionlist.addAll(orderlist);
								contionlist.addAll(returnlist);
								contionlist.addAll(depostRechargelist);
								contionlist.addAll(policyEntrylist);
								Object[] objs = new Object[contionlist.size()];
								for (int i = 0; i < contionlist.size(); i++) {
									objs[i] = contionlist.get(i);
								}
								List<Map<String, Object>> list = getNativeDao()
										.findListMap(selectOrdersql.append(unionAllsql).append(selectReturnsql)
												.append(unionAllsql).append(selectDepostRechargesql).append(unionAllsql)
												.append(selectPolicyEntrysql).toString() + limitSql, objs, 0);

								return list;
							}
						} else if (groupBylist.contains(5)) {// 1,2,3,5
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							List<Map<String, Object>> list = getNativeDao()
									.findListMap(selectOrdersql.append(unionAllsql).append(selectReturnsql)
											.append(unionAllsql).append(selectDepostRechargesql).append(unionAllsql)
											.append(selectCreditRechargesql).toString() + limitSql, objs, 0);

							return list;
						} else {// 1,2,3
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							List<Map<String, Object>> list = getNativeDao().findListMap(
									selectOrdersql.append(unionAllsql).append(selectReturnsql).append(unionAllsql)
											.append(selectDepostRechargesql).toString() + limitSql,
									objs, 0);

							return list;
						}
					} else if (groupBylist.contains(4)) {// 1,2,4(,5)
						if (groupBylist.contains(5)) {// 1,2,4,5
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							List<Map<String, Object>> list = getNativeDao()
									.findListMap(selectOrdersql.append(unionAllsql).append(selectReturnsql)
											.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
											.append(selectCreditRechargesql).toString() + limitSql, objs, 0);
							return list;
						} else {// 1,2,4
							contionlist.addAll(orderlist);
							contionlist.addAll(returnlist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							List<Map<String, Object>> list = getNativeDao().findListMap(
									selectOrdersql.append(unionAllsql).append(selectReturnsql).append(unionAllsql)
											.append(selectPolicyEntrysql).toString() + limitSql,
									objs, 0);
							return list;
						}

					} else if (groupBylist.contains(5)) {// 1,2,5
						contionlist.addAll(orderlist);
						contionlist.addAll(returnlist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectOrdersql.append(unionAllsql).append(selectReturnsql).append(unionAllsql)
										.append(selectCreditRechargesql).toString() + limitSql,
								objs, 0);
						return list;
					} else {// 1,2
						contionlist.addAll(orderlist);
						contionlist.addAll(returnlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectOrdersql.append(unionAllsql).append(selectReturnsql).toString() + limitSql, objs,
								0);
						return list;
					}

				} else if (groupBylist.contains(3)) {// 1,3
					if (groupBylist.contains(4)) {// 1,3,4(,5)
						if (groupBylist.contains(5)) {// 1,3,4,5
							contionlist.addAll(orderlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							List<Map<String, Object>> list = getNativeDao()
									.findListMap(selectOrdersql.append(unionAllsql).append(selectDepostRechargesql)
											.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
											.append(selectCreditRechargesql).toString() + limitSql, objs, 0);

							return list;
						} else {// 1,3,4
							contionlist.addAll(orderlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							List<Map<String, Object>> list = getNativeDao().findListMap(
									selectOrdersql.append(unionAllsql).append(selectDepostRechargesql)
											.append(unionAllsql).append(selectPolicyEntrysql).toString() + limitSql,
									objs, 0);

							return list;
						}

					} else if (groupBylist.contains(5)) {// 1,3,5
						contionlist.addAll(orderlist);
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectOrdersql.append(unionAllsql).append(selectDepostRechargesql).append(unionAllsql)
										.append(selectCreditRechargesql).toString() + limitSql,
								objs, 0);

						return list;
					} else {
						contionlist.addAll(orderlist);
						contionlist.addAll(depostRechargelist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectOrdersql.append(unionAllsql).append(selectDepostRechargesql).toString()
										+ limitSql,
								objs, 0);

						return list;
					}

				} else if (groupBylist.contains(4)) {// 1,4(,5)
					if (groupBylist.contains(5)) {
						contionlist.addAll(orderlist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectOrdersql.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
										.append(selectCreditRechargesql).toString() + limitSql,
								objs, 0);
						return list;
					} else {// 1,4
						contionlist.addAll(orderlist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectOrdersql.append(unionAllsql).append(selectPolicyEntrysql).toString() + limitSql,
								objs, 0);
						return list;
					}
				} else if (groupBylist.contains(5)) {// 1,5
					contionlist.addAll(orderlist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					List<Map<String, Object>> list = getNativeDao().findListMap(
							selectOrdersql.append(unionAllsql).append(selectCreditRechargesql).toString() + limitSql,
							objs, 0);
					return list;
				} else {// 1
					contionlist.addAll(orderlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					List<Map<String, Object>> list = getNativeDao().findListMap(selectOrdersql.toString() + limitSql,
							objs, 0);
					return list;
				}
			} else if (groupBylist.contains(2)) {// 不包含1
				if (groupBylist.contains(3)) {// 查询销售回款
					if (groupBylist.contains(4)) {// 查询政策录入
						if (groupBylist.contains(5)) {// 2,3,4,5
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							contionlist.addAll(creditlist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							List<Map<String, Object>> list = getNativeDao()
									.findListMap(selectReturnsql.append(unionAllsql).append(selectDepostRechargesql)
											.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
											.append(selectCreditRechargesql).toString() + limitSql, objs, 0);

							return list;
						} else {// 2,3,4
							contionlist.addAll(returnlist);
							contionlist.addAll(depostRechargelist);
							contionlist.addAll(policyEntrylist);
							Object[] objs = new Object[contionlist.size()];
							for (int i = 0; i < contionlist.size(); i++) {
								objs[i] = contionlist.get(i);
							}
							List<Map<String, Object>> list = getNativeDao().findListMap(
									selectReturnsql.append(unionAllsql).append(selectDepostRechargesql)
											.append(unionAllsql).append(selectPolicyEntrysql).toString() + limitSql,
									objs, 0);

							return list;
						}
					} else if (groupBylist.contains(5)) {// 2,3,5
						contionlist.addAll(returnlist);
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectReturnsql.append(unionAllsql).append(selectDepostRechargesql).append(unionAllsql)
										.append(selectCreditRechargesql).toString() + limitSql,
								objs, 0);

						return list;
					} else {// 2,3
						contionlist.addAll(returnlist);
						contionlist.addAll(depostRechargelist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectReturnsql.append(unionAllsql).append(selectDepostRechargesql).toString()
										+ limitSql,
								objs, 0);

						return list;
					}
				} else if (groupBylist.contains(4)) {// 2,4(,5)
					if (groupBylist.contains(5)) {// 2,4,5
						contionlist.addAll(returnlist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectReturnsql.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql)
										.append(selectCreditRechargesql).toString() + limitSql,
								objs, 0);
						return list;
					} else {// 2,4
						contionlist.addAll(returnlist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectReturnsql.append(unionAllsql).append(selectPolicyEntrysql).toString() + limitSql,
								objs, 0);
						return list;
					}

				} else if (groupBylist.contains(5)) {// 2,5
					contionlist.addAll(returnlist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					List<Map<String, Object>> list = getNativeDao().findListMap(
							selectReturnsql.append(unionAllsql).append(selectCreditRechargesql).toString() + limitSql,
							objs, 0);
					return list;
				} else {// 2
					contionlist.addAll(returnlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					List<Map<String, Object>> list = getNativeDao().findListMap(selectReturnsql.toString() + limitSql,
							objs, 0);
					return list;
				}
			} else if (groupBylist.contains(3)) {// 不包含1,2
				if (groupBylist.contains(4)) {
					if (groupBylist.contains(5)) {// 3,4,5
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(policyEntrylist);
						contionlist.addAll(creditlist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectDepostRechargesql.append(unionAllsql).append(selectPolicyEntrysql)
										.append(unionAllsql).append(selectCreditRechargesql).toString() + limitSql,
								objs, 0);

						return list;
					} else {// 3,4
						contionlist.addAll(depostRechargelist);
						contionlist.addAll(policyEntrylist);
						Object[] objs = new Object[contionlist.size()];
						for (int i = 0; i < contionlist.size(); i++) {
							objs[i] = contionlist.get(i);
						}
						List<Map<String, Object>> list = getNativeDao().findListMap(
								selectDepostRechargesql.append(unionAllsql).append(selectPolicyEntrysql).toString()
										+ limitSql,
								objs, 0);

						return list;

					}
				} else if (groupBylist.contains(5)) {// 3,5
					contionlist.addAll(depostRechargelist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					List<Map<String, Object>> list = getNativeDao().findListMap(
							selectDepostRechargesql.append(unionAllsql).append(selectCreditRechargesql).toString()
									+ limitSql,
							objs, 0);

					return list;
				} else {// 3
					contionlist.addAll(depostRechargelist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					List<Map<String, Object>> list = getNativeDao()
							.findListMap(selectDepostRechargesql.toString() + limitSql, objs, 0);

					return list;
				}

			} else if (groupBylist.contains(4)) {// 不包含1,2,3
				if (groupBylist.contains(5)) {// 4,5
					contionlist.addAll(policyEntrylist);
					contionlist.addAll(creditlist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					List<Map<String, Object>> list = getNativeDao().findListMap(
							selectPolicyEntrysql.append(unionAllsql).append(selectCreditRechargesql).toString()
									+ limitSql,
							objs, 0);

					return list;
				} else {// 4
					contionlist.addAll(policyEntrylist);
					Object[] objs = new Object[contionlist.size()];
					for (int i = 0; i < contionlist.size(); i++) {
						objs[i] = contionlist.get(i);
					}
					List<Map<String, Object>> list = getNativeDao()
							.findListMap(selectPolicyEntrysql.toString() + limitSql, objs, 0);

					return list;

				}
			} else if (groupBylist.contains(5)) {// 不包含1,2,3,4
				contionlist.addAll(creditlist);
				Object[] objs = new Object[contionlist.size()];
				for (int i = 0; i < contionlist.size(); i++) {
					objs[i] = contionlist.get(i);
				}
				List<Map<String, Object>> list = getNativeDao()
						.findListMap(selectCreditRechargesql.toString() + limitSql, objs, 0);
				return list;

			} else {
				contionlist.addAll(creditlist);
				contionlist.addAll(depostRechargelist);
				contionlist.addAll(policyEntrylist);
				contionlist.addAll(orderlist);
				contionlist.addAll(returnlist);
				Object[] objs = new Object[contionlist.size()];
				for (int i = 0; i < contionlist.size(); i++) {
					objs[i] = contionlist.get(i);
				}
				List<Map<String, Object>> list = getNativeDao().findListMap(
						selectCreditRechargesql.append(unionAllsql).append(selectDepostRechargesql).append(unionAllsql)
								.append(selectPolicyEntrysql).append(unionAllsql).append(selectOrdersql)
								.append(unionAllsql).append(selectReturnsql).toString() + limitSql,
						objs, 0);

				return list;
			}
		} else {
			contionlist.addAll(creditlist);
			contionlist.addAll(depostRechargelist);
			contionlist.addAll(policyEntrylist);
			contionlist.addAll(orderlist);
			contionlist.addAll(returnlist);
			Object[] objs = new Object[contionlist.size()];
			for (int i = 0; i < contionlist.size(); i++) {
				objs[i] = contionlist.get(i);
			}
			List<Map<String, Object>> list = getNativeDao()
					.findListMap(selectCreditRechargesql.append(unionAllsql).append(selectDepostRechargesql)
							.append(unionAllsql).append(selectPolicyEntrysql).append(unionAllsql).append(selectOrdersql)
							.append(unionAllsql).append(selectReturnsql).toString() + limitSql, objs, 0);

			return list;
		}

	}

	public List<Map<String, Object>> findOrderItemListById(String ids) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(
				" SELECT oi.price,oi.quantity,oi.name,oi.vonder_code,oi.sale_org_price,ifnull(sum(oci.branch_quantity),0) closed_quantity,"
						+ " oi.shipped_quantity,o.id " + " FROM  xx_order_item oi"
						+ " left join xx_order o on o.id=oi.orders "
						+ " left join xx_order_close_item ocs on oi.id=ocs.order_item_id "
						+ " left join xx_order_close oc on ocs.order_close=oc.id and oc.status=1 AND oc.STATUS IS NOT NULL "
						+ " left join xx_order_close_item oci on oi.id=oci.order_item_id where 1=1 "
						+ "   and o.id in (" + ids + ") ");

		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and o.company_info_id = ?");
			list.add(companyInfoId);
		}

		// if (!ConvertUtil.isEmpty(ids)) {
		// sql.append(" and o.id in (?)");
		// list.add(ids);
		// }

		sql.append(" group by oi.id");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql.toString(), objs, 0);

		return lists;
	}

	public List<Map<String, Object>> findReturnItemListById(String ids) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();

		sql.append(" select b.price,b.quantity,b.name,b.vonder_code,b.sale_org_price,0 as close_quantity,"
				+ " b.returned_quantity shipped_quantity,a.id ");
		sql.append(" from xx_b2b_returns_item b ");
		sql.append(" left join xx_b2b_returns a  on b.b2b_returns= a.id ");
		sql.append(" where 1=1 and a.id in (" + ids + ") ");

		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" and a.company_info_id = ?");
			list.add(companyInfoId);
		}

		// if (!ConvertUtil.isEmpty(ids)) {
		// sql.append(" and a.id in (?)");
		// list.add(ids);
		// }

		sql.append(" group by b.id");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		List<Map<String, Object>> lists = getNativeDao().findListMap(sql.toString(), objs, 0);
		return lists;

	}

	public Map<String, Object> findBalanceSbu(Long storeId, Long sbuId, Long organizationId, Long saleOrgId) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		StringBuilder sql = new StringBuilder();

		sql.append("select b.* ,b.amount_paid-b.amount_closed from v_store_balance b  where b.company_info_id ="
				+ companyInfoId + " and 	b.id= " + storeId + " and b.sbuid = " + sbuId + " and orgid =  "
				+ organizationId + " and b.sale_org_id=" + saleOrgId);

		List<Map<String, Object>> items = getNativeDao().findListMap(sql.toString(), null, 0);

		net.shopxx.base.core.util.LogUtils.debug("storeId：" + storeId + "余额:sql=" + sql.toString());
		if (items.isEmpty()) {
			return null;
		}
		return items.get(0);

	}

	public Page<Map<String, Object>> findPageCurrentAccount(String sn, String storeName, 
			String organizationName, String sbuName, String firstTime, String lastTime, Object[] type,
			Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append("SELECT (SELECT SUM( b.`金额` ) FROM	v_link_balance b WHERE	b.`单据开始日期` <= A.`单据开始日期` ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND b.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" AND b.`单据编号` = ?");
			list.add(sn);
		}
		if(!ConvertUtil.isEmpty(sbuName)){
			sql.append(" AND b.SBU = ?");
			list.add(sbuName);
		}
		if(!ConvertUtil.isEmpty(storeName)){
			sql.append(" AND b.`客户` = ?");
			list.add(storeName);
		}
		if(!ConvertUtil.isEmpty(organizationName)){
			sql.append(" AND b.`经营组织` = ?");
			list.add(organizationName);
		}
		if (type != null && type.length > 0) {
			String s = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					s += "'"+type[i].toString()+"'";
				else
					s += "'"+type[i].toString()+"'" + ",";
			}
			sql.append(" AND b.`单据类型` IN(" + s + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" AND b.`单据开始日期` >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" AND b.`单据开始日期` <= ?");
			list.add(DateUtil.convert(lastTime + " 23:59:59"));
		}
		
		sql.append(" GROUP BY b.SBU,b.`客户`,	b.`经营组织`) balances,");
		sql.append(" A.`单据类型` type,A.`单据编号` sn,A.`单据开始日期` create_date,A.`单据结束日期` modify_date,"
				+"A.`单据状态` invoice_status,A.SBU sbu_name,A.SBUID,A.`客户` store_name,"
				+"A.`机构` sale_org_name,A.`机构ID`,A.`经营组织` organization,A.`数量` quantity,"
				+"A.`发货数量` shipping_quantity,A.`关闭数量` close_quantity,A.`销售单价` unit_price,"
				+"A.`平台结算价` sale_org_price,A.`金额` money,A.`发货金额` shipping_money,A.`关闭金额` close_money,"
				+"A.`结算金额` settle_account,A.`结算发货金额` shipping_settle_account,A.`结算关闭金额` close_settle_account,"
				+"A.`产品` product_name,A.`品类` product_category,A.`备注` memo FROM v_link_balance A WHERE 1=1");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND A.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" AND A.`单据编号` = ?");
			list.add(sn);
		}
		if(!ConvertUtil.isEmpty(sbuName)){
			sql.append(" AND A.SBU = ?");
			list.add(sbuName);
		}
		if(!ConvertUtil.isEmpty(storeName)){
			sql.append(" AND A.`客户` = ?");
			list.add(storeName);
		}
		if(!ConvertUtil.isEmpty(organizationName)){
			sql.append(" AND A.`经营组织` = ?");
			list.add(organizationName);
		}
		if (type != null && type.length > 0) {
			String s = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					s += "'"+type[i].toString()+"'";
				else
					s += "'"+type[i].toString()+"'" + ",";
				
			}
			sql.append(" AND A.`单据类型` IN(" + s + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" AND A.`单据开始日期` >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" AND A.`单据开始日期` <= ?");
			list.add(DateUtil.convert(lastTime + " 23:59:59"));
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and A.`机构ID` in (" + saleOrgIds + ")");
			}else{
				sql.append(" and A.`机构ID` is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and A.SBUID in (" + sbuIds + ")");
			}else{
				sql.append(" and A.SBUID is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and A.`经营组织ID` in (" + organizationIdS + ")");
			}else{
				sql.append(" and A.`经营组织ID` is null");
			}
		}
		
		sql.append(" ORDER BY A.`单据开始日期` ASC");
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}
	
	public List<Map<String,Object>> findExportTable(String sn, String storeName, 
			String organizationName, String sbuName, String firstTime, String lastTime, Object[] type){
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append("SELECT (SELECT SUM( b.`金额` ) FROM	v_link_balance b WHERE	b.`单据开始日期` <= A.`单据开始日期` ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND b.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" AND b.`单据编号` = ?");
			list.add(sn);
		}
		if(!ConvertUtil.isEmpty(sbuName)){
			sql.append(" AND b.SBU = ?");
			list.add(sbuName);
		}
		if(!ConvertUtil.isEmpty(storeName)){
			sql.append(" AND b.`客户` = ?");
			list.add(storeName);
		}
		if(!ConvertUtil.isEmpty(organizationName)){
			sql.append(" AND b.`经营组织` = ?");
			list.add(organizationName);
		}
		if (type != null && type.length > 0) {
			String s = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					s += "'"+type[i].toString()+"'";
				else
					s += "'"+type[i].toString()+"'" + ",";
			}
			sql.append(" AND b.`单据类型` IN(" + s + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" AND b.`单据开始日期` >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" AND b.`单据开始日期` <= ?");
			list.add(DateUtil.convert(lastTime + " 23:59:59"));
		}
		sql.append(" GROUP BY b.SBU,b.`客户`,	b.`经营组织`) balances,");
		sql.append(" A.`单据类型` type,A.`单据编号` sn,A.`单据开始日期` create_date,A.`单据结束日期` modify_date,"
				+"A.`单据状态` invoice_status,A.SBU sbu_name,A.SBUID,A.`客户` store_name,"
				+"A.`机构` sale_org_name,A.`机构ID`,A.`经营组织` organization,A.`数量` quantity,"
				+"A.`发货数量` shipping_quantity,A.`关闭数量` close_quantity,A.`销售单价` unit_price,"
				+"A.`平台结算价` sale_org_price,A.`金额` money,A.`发货金额` shipping_money,A.`关闭金额` close_money,"
				+"A.`结算金额` settle_account,A.`结算发货金额` shipping_settle_account,A.`结算关闭金额` close_settle_account,"
				+"A.`产品` product_name,A.`品类` product_category,A.`备注` memo FROM v_link_balance A WHERE 1=1");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND A.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" AND A.`单据编号` = ?");
			list.add(sn);
		}
		if(!ConvertUtil.isEmpty(sbuName)){
			sql.append(" AND A.SBU = ?");
			list.add(sbuName);
		}
		if(!ConvertUtil.isEmpty(storeName)){
			sql.append(" AND A.`客户` = ?");
			list.add(storeName);
		}
		if(!ConvertUtil.isEmpty(organizationName)){
			sql.append(" AND A.`经营组织` = ?");
			list.add(organizationName);
		}
		if (type != null && type.length > 0) {
			String s = "";
			for (int i = 0; i < type.length; i++) {
				if (i == type.length - 1)
					s += "'"+type[i].toString()+"'";
				else
					s += "'"+type[i].toString()+"'" + ",";
				
			}
			sql.append(" AND A.`单据类型` IN(" + s + ")");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" AND A.`单据开始日期` >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" AND A.`单据开始日期` <= ?");
			list.add(DateUtil.convert(lastTime + " 23:59:59"));
		}
		
		//用户机构
		String saleOrgIds = roleJurisdictionUtil.getSaleOrgIds();
		if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("-1")){
			if(!ConvertUtil.isEmpty(saleOrgIds) && !saleOrgIds.equals("0")){
				sql.append(" and A.`机构ID` in (" + saleOrgIds + ")");
			}else{
				sql.append(" and A.`机构ID` is null");
			}
		}
		
		//用户Sbu
		String sbuIds = roleJurisdictionUtil.getSbuIds();
		if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("-1")){
			if(!ConvertUtil.isEmpty(sbuIds) && !sbuIds.equals("0")){
				sql.append(" and A.SBUID in (" + sbuIds + ")");
			}else{
				sql.append(" and A.SBUID is null");
			}
		}
		
		//用户经营组织
		String organizationIdS = roleJurisdictionUtil.getOrganizationIds();
		if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("-1")){
			if(!ConvertUtil.isEmpty(organizationIdS) && !organizationIdS.equals("0")){
				sql.append(" and A.`经营组织ID` in (" + organizationIdS + ")");
			}else{
				sql.append(" and A.`经营组织ID` is null");
			}
		}
		
		sql.append(" ORDER BY A.`单据开始日期` ASC");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

}