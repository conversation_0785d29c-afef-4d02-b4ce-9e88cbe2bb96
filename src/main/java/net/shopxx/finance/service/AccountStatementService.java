package net.shopxx.finance.service;

import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;

public interface AccountStatementService extends BaseService<Object> {
	
	public Page<Map<String, Object>> findPage(String month, Long storeId,
			Long saleOrgId, Pageable pageable);
	
	public Page<Map<String, Object>> findLinePage(String month, Long storeId,
			String sn, Integer type, Pageable pageable);
}
