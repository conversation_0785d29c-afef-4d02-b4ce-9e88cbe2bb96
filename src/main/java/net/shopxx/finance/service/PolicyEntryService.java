package net.shopxx.finance.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.basic.entity.Organization;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.Store;
import net.shopxx.wf.service.WfBillBaseService;

import org.springframework.web.multipart.MultipartFile;

public interface PolicyEntryService extends WfBillBaseService<DepositRecharge> {

	/**
	 * 余额充值审核
	 * 
	 * @param depositRecharge
	 * @param flag
	 * @param note
	 * @return
	 */
	public void check(DepositRecharge depositRecharge, Integer flag,
			String note, BigDecimal actualAmount);

	/**
	 * 流程审批
	 * 
	 * @param depositRecharge
	 * @param note
	 * @param actualAmount
	 * @param objConfId
	 */
	public void checkStoreRechargeWf(DepositRecharge depositRecharge,
			String note, BigDecimal actualAmount, Long objConfId);

	/**
	 * 用户、客户余额充值列表数据
	 */
	public Page<Map<String, Object>> findPage(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			Pageable pageable);

	public void update(Store store, DepositRecharge depositRecharge,
			 Integer fullLinkType, Integer isSubmit);

	public void cancel(DepositRecharge depositRecharge, Integer fullLinkType);

	public void save(Store store, DepositRecharge depositRecharge,
			Integer fullLinkType, Long rechargeTypeId,Integer sourceType,Long invoiceTypeId);

	/**
	 * 销售回款列表数据
	 */
	public Page<Map<String, Object>> newfindPage(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			String firstTime, String lastTime, Long organizationId, Long sbuId,
			Pageable pageable);

	/**
	 * 销售回款列表数据导出数量
	 */
	public Integer countDepositRecharge(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			String firstTime, String lastTime, Long sbuId, Long[] ids,
			Long organizationId,Pageable pageable, Integer page, Integer size);

	/**
	 * 销售回款列表数据导出
	 */
	public List<Map<String, Object>> findDepositRechargeList(Integer[] type,
			String sn, Long storeMemberId, Long storeId, Long saleOrgId,
			Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, BigDecimal minPrice,
			BigDecimal maxPrice, String firstTime, String lastTime, Long sbuId,
			Long[] ids,Long organizationId, Pageable pageable, Integer page, Integer size);

	public Page<Map<String, Object>> findShowPage(Long storeId, String sn,
			Pageable page);

	public List<Map<String, Object>> findList(Long storeId, String sn);

	/**
	 * excel导入
	 * 
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public String policyAddImport(MultipartFile multipartFile, Integer type)
			throws Exception;

	/**
	 * 政策审批
	 * 
	 * @param depositRecharge
	 * @param note
	 * @param actualAmount
	 * @param
	 */
	public void checkStoreRecharge(DepositRecharge depositRecharge,
			String note, BigDecimal actualAmount) throws Exception;


	/**
	 * 同步政策信息到中版
	 * @param depositRecharge
	 */
	public void synPolicyInformation(DepositRecharge depositRecharge);

	
	/**
	 * 校验政策单状态
	 */
	public void checkDepositRechargeStatus(DepositRecharge depositRecharge, Integer status,
			String statusName, String operationName);
	
	
}