package net.shopxx.finance.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.finance.dao.PolicyDao;
import net.shopxx.finance.entity.Policy;
import net.shopxx.finance.entity.PolicyAmount;
import net.shopxx.finance.entity.PolicyAttach;
import net.shopxx.finance.entity.PolicyCustomer;
import net.shopxx.finance.entity.PolicyGift;
import net.shopxx.finance.entity.PolicyGifts;
import net.shopxx.finance.entity.PolicyGoods;
import net.shopxx.finance.entity.PolicyProduct;
import net.shopxx.finance.entity.PolicySaleOrg;
import net.shopxx.finance.entity.ProductByPolicy;
import net.shopxx.finance.entity.RelationByPolicy;
import net.shopxx.finance.entity.RelationByPolicyC;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.PolicyFileService;
import net.shopxx.finance.service.PolicyProductService;
import net.shopxx.finance.service.PolicyService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service("policyServiceImpl")
public class PolicyServiceImpl extends WfBillBaseServiceImpl<Policy> implements
		PolicyService {

	@Resource(name = "policyDao")
	private PolicyDao policyDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "lockDataDao")
	private LockDataDao lockDataDao;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "policyFileServiceImpl")
	private PolicyFileService policyFileService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "policyProductServiceImpl")
	private PolicyProductService policyProductService;

	/**
	 * 政策列表数据
	 */
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn, String name,
			Long storeId, Integer applyType, Integer[] status, Long creatorId,
			Pageable pageable, Integer policyType) {
		return policyDao.findPage(sn,
				name,
				storeId,
				applyType,
				status,
				creatorId,
				pageable,
				policyType);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findAttachListByPolicyId(Long id) {
		return policyDao.findAttachListByPolicyId(id);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyProductListByPolicyrId(
			String policyIds) {
		return policyDao.findPolicyProductListByPolicyrId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyGiftListByPolicyrId(
			String policyIds) {
		return policyDao.findPolicyGiftListByPolicyrId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyProductListByProductId(
			Long productId, Long storeId) {
		return policyDao.findPolicyProductListByProductId(productId, storeId);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findSelect(String sn, String name,
			Long storeId, Integer applyType, Integer[] status, Long creatorId) {
		return policyDao.findSelect(sn,
				name,
				storeId,
				applyType,
				status,
				creatorId);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPolicyList(Pageable pageable,
			Object[] args) {
		return policyDao.findPolicyList(pageable, args);
	}

	@Override
	@Transactional
	public void savePolicy(Policy policy, Store store, SaleOrg saleOrg) {
		policy.setSaleOrg(saleOrg);
		policy.setStore(store);
		// policy.setSn(SnUtil.generateSn(null, 14));
		policy.setDocStatus(0);// 单据状态 0.已保存(没有流程) 1.处理中(有流程)
		// 2.已处理(流程走完) 3.作废
		policy.setCreator(storeMemberService.getCurrent());

		// 产品
		List<PolicyProduct> policyProducts = policy.getPolicyProducts();
		for (Iterator<PolicyProduct> iterator = policyProducts.iterator(); iterator.hasNext();) {
			PolicyProduct policyProduct = iterator.next();
			if (policyProduct == null || policyProduct.getProduct() == null) {
				iterator.remove();
				continue;
			}
			Product product = productService.find(policyProduct.getProduct()
					.getId());
			if (product == null) {
				ExceptionUtil.throwServiceException("请维护正确的产品");
			}
			policyProduct.setProduct(product);
			policyProduct.setProductCategory(product.getProductCategory());
			policyProduct.setPolicy(policy);
		}
		policy.setPolicyProducts(policyProducts);

		// 赠品
		List<PolicyGift> policyGifts = policy.getPolicyGifts();
		for (Iterator<PolicyGift> iterator = policyGifts.iterator(); iterator.hasNext();) {
			PolicyGift policyGift = iterator.next();
			if (policyGift == null || policyGift.getProduct() == null) {
				iterator.remove();
				continue;
			}
			Product product = productService.find(policyGift.getProduct()
					.getId());
			if (product == null) {
				ExceptionUtil.throwServiceException("请维护正确的产品");
			}
			policyGift.setProduct(product);
			policyGift.setPolicy(policy);
		}
		policy.setPolicyGifts(policyGifts);

		// 附件
		List<PolicyAttach> policyAttachs = policy.getPolicyAttachs();
		for (Iterator<PolicyAttach> iterator = policyAttachs.iterator(); iterator.hasNext();) {
			PolicyAttach policyAttach = iterator.next();
			if (policyAttach == null || policyAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (policyAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			policyAttach.setFileName(policyAttach.getName()
					+ "."
					+ policyAttach.getSuffix());
			policyAttach.setPolicy(policy);
			policyAttach.setStoreMember(storeMemberService.getCurrent());
		}
		policy.setPolicyAttachs(policyAttachs);

		if (policy.getId() == null) {
			save(policy);
			orderFullLinkService.addFullLink(18,
					null,
					policy.getSn(),
					ConvertUtil.convertI18nMsg("18700", new Object[] { "政策设置" }),
					null);
		}
		else {
			update(policy, "sn", "creator", "docStatus");
		}

	}

	// @Override
	// @Transactional
	// public void check(CreditRecharge creditRecharge, Integer flag, String
	// note,
	// BigDecimal actualAmount) {
	// creditRecharge.setNote(note);
	// creditRecharge.setCheckDate(new Date());
	// creditRecharge.setOperator(storeMemberService.getCurrent());
	// if (flag == 2) {
	// creditRecharge.setStatus(2); // 审核未通过
	// creditRecharge.setDocStatus(3);
	// }
	// else if (flag == 1) {
	// creditRecharge.setStatus(1); // 审核通过
	// creditRecharge.setDocStatus(2);
	// if (actualAmount == null) {
	// actualAmount = creditRecharge.getAmount();
	// }
	// creditRecharge.setActualAmount(actualAmount);
	// Date startDate = creditRecharge.getStartDate();
	// Date endDate = DateUtils.addDays(creditRecharge.getEndDate(), 1);
	// Date nowDate = new Date();
	// if (startDate != null && endDate != null) {
	// if (nowDate.compareTo(startDate) >= 0
	// && nowDate.compareTo(endDate) < 0) {
	// creditRecharge.setType(1);
	// }
	// }
	// }
	// update(creditRecharge);
	//
	// orderFullLinkService.addFullLink(8,
	// null,
	// creditRecharge.getSn(),
	// ConvertUtil.convertI18nMsg("18702", new Object[] { "临时额度申请" }),
	// null);
	//
	// }
	//
	@Override
	@Transactional
	public void checkPolicyWf(Policy policy, Long objConfId) {

		// 创建流程实例
		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}
		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());
		policy.setByObjConfig(wfObjConfigLine); // 设置流程配置
		wfBaseService.createwf(storeMemberService.getCurrent(),
				policy.getSaleOrg() == null ? null : policy.getSaleOrg()
						.getId(),
				policy.getSn(),
				policy.getWfTempId(),
				policy.getObjTypeId(),
				policy.getId());

		policy.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完)3.作废
		update(policy);
		orderFullLinkService.addFullLink(18,
				null,
				policy.getSn(),
				ConvertUtil.convertI18nMsg("18701",
						new Object[] { wfTemp.getWfTempName() }),
				null);
	}

	// public void creditRecharge(Long companyInfoId, Payment payment,
	// BigDecimal modifyBalance,
	// CreditRecharge creditRecharge) {
	//
	// Store store = creditRecharge.getStore();
	// lockDataDao.lockStore(store.getId().toString());
	//
	// // 生成一张付款单
	// paymentService.initPayment(8, 3, 5, 1, 1, modifyBalance, new Date(),
	// null, null, creditRecharge.getStore(),
	// null, creditRecharge.getSn(), null, null, null, "临时额度生效");
	//
	// // 修改客户余额
	// store.setBalance(store.getBalance().add(modifyBalance));
	// store.setCredit(store.getCredit().add(modifyBalance));
	// storeService.update(store);
	// }

	//
	@Override
	public void agreeBack(Wf wf) {

		if (wf.getStat().intValue() == 2) {
			Policy policy = find(wf.getObjId());
			// Date startDate = creditRecharge.getStartDate();
			// Date endDate = DateUtils.addDays(creditRecharge.getEndDate(), 1);
			// Date nowDate = new Date();
			// if (startDate != null && endDate != null) {
			// if (nowDate.compareTo(startDate) >= 0 &&
			// nowDate.compareTo(endDate) < 0) {
			// creditRecharge.setType(1);
			// }
			// }
			policy.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
									// 2.已处理(流程走完)3.作废
			update(policy);

		}
	}

	//
	@Override
	public void startBack(Wf wf) {

		Policy policy = find(wf.getObjId());
		wf.setStore(policy.getStore());
		wfBaseService.update(wf);
	}

	//
	@Override
	public void interruptBack(Wf wf) {
		Policy policy = find(wf.getObjId());
		policy.setDocStatus(0); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
								// 2.已处理(流程走完)3.作废
		update(policy);
		orderFullLinkService.addFullLink(18,
				null,
				policy.getSn(),
				ConvertUtil.convertI18nMsg("18704"),
				null);
	}

	//
	// @Override
	// @Transactional
	// public void effective(CreditRecharge creditRecharge) {
	//
	// creditRecharge = find(creditRecharge.getId());
	// creditRecharge.setType(1);
	// update(creditRecharge);
	// }
	//
	// @Override
	// @Transactional
	// public void invalid(CreditRecharge creditRecharge) {
	//
	// creditRecharge = find(creditRecharge.getId());
	// creditRecharge.setType(2);
	// update(creditRecharge);
	// }

	/*
	 * (non-Javadoc)
	 * @see net.shopxx.finance.service.PolicyService#savePolicyYear(net.shopxx.
	 * finance.entity.Policy, java.lang.Long, java.lang.Long)
	 */
	@Override
	@Transactional
	public void savePolicyYear(Policy policy, Long saleStyleId,
			Long[] orderStyleIds, Long areaId, Long[] customerTypeIds,
			Long[] unknown, Long policyFileId) {
		Area area = areaService.find(areaId);
		policy.setArea(area);

//		if (policyFileId == null || policyFileId.equals("")) {
//			ExceptionUtil.throwServiceException("政策文件名不能为空！");
//		}
//
//		PolicyFile policyFile = policyFileService.find(policyFileId);
//		policy.setPolicyFile(policyFile);
//
//		/**同一个政策，不可以存在重复的有效期间；根据政策文件名+有效时间区间去校验**/
//		List<Filter> filters = new ArrayList<Filter>();
//		filters.add(Filter.eq("policyFile", policyFile));
//		List<Policy> policys = this.findList(null, filters, null);
//		if (policys != null && policys.size() > 0) {
//			for (Policy p : policys) {
//				if (policy.getStartDate().after(p.getStartDate())
//						&& policy.getStartDate().before(p.getEndDate())) {
//					ExceptionUtil.throwServiceException("有效期内存在相同的政策！");
//				}
//			}
//		}

		StoreMember storeMember = storeMemberService.getCurrent();
		policy.setCreator(storeMember);

		String sn = "ZCN" + SnUtil.generateSn();
		policy.setSn(sn);
		//金额明细
		List<PolicyAmount> policyAmounts = policy.getPolicyAmounts();
		for (Iterator<PolicyAmount> iterator = policy.getPolicyAmounts()
				.iterator(); iterator.hasNext();) {
			PolicyAmount policyAmount = iterator.next();
			if (policyAmount == null || policyAmount.getReward() == null) {
				iterator.remove();
				continue;
			}
			policyAmount.setPolicy(policy);
		}
		policy.setPolicyAmounts(policyAmounts);

		//机构明细
		List<PolicySaleOrg> policySaleorgs = policy.getPolicySaleOrgs();
		for (Iterator<PolicySaleOrg> iterator = policy.getPolicySaleOrgs()
				.iterator(); iterator.hasNext();) {
			PolicySaleOrg policySaleOrg = iterator.next();
			if (policySaleOrg == null
					|| policySaleOrg.getSaleOrg() == null
					|| policySaleOrg.getSaleOrg().getId() == null) {
				iterator.remove();
				continue;
			}
			policySaleOrg.setPolicy(policy);
		}
		policy.setPolicySaleOrgs(policySaleorgs);

		//促销政策-产品价格促销 :明细
		List<ProductByPolicy> productByPolicys = policy.getProducts();
		if (productByPolicys != null && productByPolicys.size() > 0) {
			for (Iterator<ProductByPolicy> iterator = policy.getProducts()
					.iterator(); iterator.hasNext();) {
				ProductByPolicy productByPolicy = iterator.next();
				if (productByPolicy == null
						|| productByPolicy.getProduct() == null
						|| productByPolicy.getProduct().getId() == null) {
					iterator.remove();
					continue;
				}
				Product product = productService.find(productByPolicy.getProduct()
						.getId());
				productByPolicy.setProduct(product);
				productByPolicy.setPolicy(policy);
			}
			policy.setProducts(productByPolicys);
		}

		//年返-囤货政策 :毛利明细
		List<PolicyGoods> policyGoods = policy.getPolicyGoods();
		if (policyGoods != null && policyGoods.size() > 0) {
			for (Iterator<PolicyGoods> iterator = policy.getPolicyGoods()
					.iterator(); iterator.hasNext();) {
				PolicyGoods policyGood = iterator.next();
				if (policyGood == null
						|| policyGood.getProduct() == null
						|| policyGood.getProduct().getId() == null) {
					iterator.remove();
					continue;
				}
				Product product = productService.find(policyGood.getProduct()
						.getId());
				policyGood.setProduct(product);
				policyGood.setPolicy(policy);
			}
			policy.setPolicyGoods(policyGoods);
		}

		//促销政策-满赠
		List<PolicyGifts> policyGitfs = policy.getPolicyByGifts();
		if (policyGitfs != null && policyGitfs.size() > 0) {
			for (Iterator<PolicyGifts> iterator = policy.getPolicyByGifts()
					.iterator(); iterator.hasNext();) {
				PolicyGifts policyGitf = iterator.next();
				if (policyGitf == null
						|| policyGitf.getProduct() == null
						|| policyGitf.getProduct().getId() == null) {
					iterator.remove();
					continue;
				}
				Product product = productService.find(policyGitf.getProduct()
						.getId());
				policyGitf.setProduct(product);
				policyGitf.setPolicy(policy);
			}
			policy.setPolicyByGifts(policyGitfs);
		}

		//客户明细
		List<PolicyCustomer> policyCustomers = policy.getPolicyCustomers();
		for (Iterator<PolicyCustomer> iterator = policy.getPolicyCustomers()
				.iterator(); iterator.hasNext();) {
			PolicyCustomer policyCustomer = iterator.next();
			if (policyCustomer == null
					|| policyCustomer.getStore() == null
					|| policyCustomer.getStore().getId() == null) {
				iterator.remove();
				continue;
			}
			policyCustomer.setPolicy(policy);
		}
		policy.setPolicyCustomers(policyCustomers);

		SystemDict saleStyle = systemDictService.find(saleStyleId);
		policy.setSaleStyle(saleStyle);
		//存入订单类型和客户类型
		if (orderStyleIds != null && orderStyleIds.length > 0) {
			List<RelationByPolicy> relationByPolicys = new ArrayList<RelationByPolicy>();
			for (Long orderStyleId : orderStyleIds) {
				RelationByPolicy rb = new RelationByPolicy();
				SystemDict orderStyle = systemDictService.find(orderStyleId);
				rb.setSystemDict(orderStyle);
				rb.setPolicy(policy);
				relationByPolicys.add(rb);
			}
			policy.setRelationByPolicys(relationByPolicys);
		}
		if (customerTypeIds != null && customerTypeIds.length > 0) {
			List<RelationByPolicyC> relationcByPolicys = new ArrayList<RelationByPolicyC>();
			for (Long customerTypeId : customerTypeIds) {
				RelationByPolicyC rb = new RelationByPolicyC();
				SystemDict customerType = systemDictService.find(customerTypeId);
				rb.setSystemDict(customerType);
				rb.setPolicy(policy);
				relationcByPolicys.add(rb);
			}
			policy.setRelationcByPolicys(relationcByPolicys);
		}

		save(policy);

	}

	@Override
	@Transactional
	public void updatePolicyYear(Policy policy, Long saleStyleId,
			Long[] orderStyleIds, Long areaId, Long[] customerTypeIds,
			Long[] unknown, Long policyFileId) {

		Area area = areaService.find(areaId);
		policy.setArea(area);
		policy.setDocStatus(0);

//		if (policyFileId == null || policyFileId.equals("")) {
//			ExceptionUtil.throwServiceException("政策文件名不能为空！");
//		}
//		PolicyFile policyFile = policyFileService.find(policyFileId);
//		policy.setPolicyFile(policyFile);
//
//		/**同一个政策，不可以存在重复的有效期间；根据政策文件名+有效时间区间去校验**/
//		List<Filter> filters = new ArrayList<Filter>();
//		filters.add(Filter.eq("policyFile", policyFile));
//		List<Policy> policys = this.findList(null, filters, null);
//		if (policys != null && policys.size() > 0) {
//			for (Policy p : policys) {
//				if (policy.getStartDate().after(p.getStartDate())
//						&& policy.getStartDate().before(p.getEndDate())) {
//					ExceptionUtil.throwServiceException("有效期内存在相同的政策！");
//				}
//			}
//		}
		//金额明细
		List<PolicyAmount> policyAmounts = policy.getPolicyAmounts();
		for (Iterator<PolicyAmount> iterator = policy.getPolicyAmounts()
				.iterator(); iterator.hasNext();) {
			PolicyAmount policyAmount = iterator.next();
			if (policyAmount == null || policyAmount.getReward() == null) {
				iterator.remove();
				continue;
			}
			policyAmount.setPolicy(policy);
		}
		policy.setPolicyAmounts(policyAmounts);

		//机构明细
		List<PolicySaleOrg> policySaleorgs = policy.getPolicySaleOrgs();
		for (Iterator<PolicySaleOrg> iterator = policy.getPolicySaleOrgs()
				.iterator(); iterator.hasNext();) {
			PolicySaleOrg policySaleOrg = iterator.next();
			if (policySaleOrg == null
					|| policySaleOrg.getSaleOrg() == null
					|| policySaleOrg.getSaleOrg().getId() == null) {
				iterator.remove();
				continue;
			}
			policySaleOrg.setPolicy(policy);
		}
		policy.setPolicySaleOrgs(policySaleorgs);

		//客户明细
		List<PolicyCustomer> policyCustomers = policy.getPolicyCustomers();
		for (Iterator<PolicyCustomer> iterator = policy.getPolicyCustomers()
				.iterator(); iterator.hasNext();) {
			PolicyCustomer policyCustomer = iterator.next();
			if (policyCustomer == null
					|| policyCustomer.getStore() == null
					|| policyCustomer.getStore().getId() == null) {
				iterator.remove();
				continue;
			}
			policyCustomer.setPolicy(policy);
		}
		policy.setPolicyCustomers(policyCustomers);

		//促销政策-产品价格促销 :明细
		List<ProductByPolicy> productByPolicys = policy.getProducts();
		if (productByPolicys != null && productByPolicys.size() > 0) {
			for (Iterator<ProductByPolicy> iterator = policy.getProducts()
					.iterator(); iterator.hasNext();) {
				ProductByPolicy productByPolicy = iterator.next();
				if (productByPolicy == null
						|| productByPolicy.getProduct() == null
						|| productByPolicy.getProduct().getId() == null) {
					iterator.remove();
					continue;
				}
				Product product = productService.find(productByPolicy.getProduct()
						.getId());
				productByPolicy.setProduct(product);
				productByPolicy.setPolicy(policy);
			}
			policy.setProducts(productByPolicys);
		}
		//年返-囤货政策 :毛利明细
		List<PolicyGoods> policyGoods = policy.getPolicyGoods();
		if (policyGoods != null && policyGoods.size() > 0) {
			for (Iterator<PolicyGoods> iterator = policy.getPolicyGoods()
					.iterator(); iterator.hasNext();) {
				PolicyGoods policyGood = iterator.next();
				if (policyGood == null
						|| policyGood.getProduct() == null
						|| policyGood.getProduct().getId() == null) {
					iterator.remove();
					continue;
				}
				Product product = productService.find(policyGood.getProduct()
						.getId());
				policyGood.setProduct(product);
				policyGood.setPolicy(policy);
			}
			policy.setPolicyGoods(policyGoods);
		}

		//促销政策-满赠
		List<PolicyGifts> policyGitfs = policy.getPolicyByGifts();
		if (policyGitfs != null && policyGitfs.size() > 0) {
			for (Iterator<PolicyGifts> iterator = policy.getPolicyByGifts()
					.iterator(); iterator.hasNext();) {
				PolicyGifts policyGitf = iterator.next();
				if (policyGitf == null
						|| policyGitf.getProduct() == null
						|| policyGitf.getProduct().getId() == null) {
					iterator.remove();
					continue;
				}
				Product product = productService.find(policyGitf.getProduct()
						.getId());
				policyGitf.setProduct(product);
				policyGitf.setPolicy(policy);
			}
			policy.setPolicyByGifts(policyGitfs);
		}
		//存入销售体系，订单类型
		SystemDict saleStyle = systemDictService.find(saleStyleId);
		policy.setSaleStyle(saleStyle);

		if (orderStyleIds != null && orderStyleIds.length > 0) {
			List<RelationByPolicy> relationByPolicys = new ArrayList<RelationByPolicy>();
			for (Long orderStyleId : orderStyleIds) {
				RelationByPolicy rb = new RelationByPolicy();
				SystemDict orderStyle = systemDictService.find(orderStyleId);
				rb.setSystemDict(orderStyle);
				rb.setPolicy(policy);
				relationByPolicys.add(rb);
			}
			policy.setRelationByPolicys(relationByPolicys);
		}
		if (customerTypeIds != null && customerTypeIds.length > 0) {
			List<RelationByPolicyC> relationcByPolicys = new ArrayList<RelationByPolicyC>();
			for (Long customerTypeId : customerTypeIds) {
				RelationByPolicyC rb = new RelationByPolicyC();
				SystemDict customerType = systemDictService.find(customerTypeId);
				rb.setSystemDict(customerType);
				rb.setPolicy(policy);
				relationcByPolicys.add(rb);
			}
			policy.setRelationcByPolicys(relationcByPolicys);
		}
		update(policy, "sn", "creator");

	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyAmountListByPolicyId(
			String policyIds) {
		return policyDao.findPolicyAmountListByPolicyId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicySaleOrgListByPolicyId(
			String policyIds) {
		return policyDao.findPolicySaleOrgListByPolicyId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyCustomerListByPolicyId(
			String policyIds) {
		return policyDao.findPolicyCustomerListByPolicyId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyRelationListByPolicyId(
			String policyIds) {
		return policyDao.findPolicyRelationListByPolicyId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyProductId(String policyIds) {
		return policyDao.findPolicyProductId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyGoodId(String policyIds) {
		return policyDao.findPolicyGoodId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPolicyGiftsListByPolicyId(
			String policyIds) {
		return policyDao.findPolicyGiftsListByPolicyId(policyIds);
	}

	@Override
	@Transactional(readOnly = true)
	public boolean findByRole(StoreMember storeMember, String roleName) {
		return policyDao.findByRole(storeMember, roleName);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> selectPolicyMoney(String sn,
			Pageable pageable) {
		return policyDao.selectPolicyMoney(sn, pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPolicyProductPageForOrder(
			Long productId, Long storeId, Pageable pageable) {
		// TODO 自动生成的方法存根
		return policyDao.findPolicyProductPageForOrder(productId,
				storeId,
				pageable);
	}

	/**
	 * 导入订单
	 */
	@Override
	@Transactional(readOnly = true)
	public String policyImport(MultipartFile multipartFile) throws Exception {

		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
		if (rows > 1001) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入1000条");
			}
			else {
				rows = 1001;
			}
		}

		String name;//政策名称
		String saleOrgName;//机构
		String storeName;//客户
		String productsn;//产品编码
		String min;//最小数量
		String max;//最大数量
		String price;//执行价格
		String stareTime;//开始时间
		String stopTime;//结束时间
		//0.投款政策 1.提货政策 2.促销政策 3.满赠政策 
		Integer applyTypes = 2;
		Integer policyType = 3;

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, Long> dataMap = new HashMap<String, Long>();
		List<Filter> filters = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		for (int i = 1; i < rows; i++) {
			int index = i + 1;

			//政策名称，唯一
			cell = sheet.getCell(0, i);
			name = cell.getContents().trim();
			if (StringUtils.isEmpty(name)) {
				throw new RuntimeException("第" + index + "行,政策名称为空");
			}

			//机构
			cell = sheet.getCell(1, i);
			saleOrgName = cell.getContents().trim();
			if (StringUtils.isBlank(saleOrgName)) {
				msg = "第" + index + "行." + "机构名称为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("name", saleOrgName));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			SaleOrg saleOrg = saleOrgService.find(filters);
			if (saleOrg == null) {
				msg = "第" + index + "行." + "机构【" + saleOrgName + "】不存在";
				ExceptionUtil.throwServiceException(msg);
			}

			// 客户，非必填
			cell = sheet.getCell(2, i);
			storeName = cell.getContents().trim();
			Store store = null;
			if (StringUtils.isNotBlank(storeName)) {
				filters.clear();
				filters.add(Filter.eq("name", storeName));
				List<Store> stores = storeService.findList(null, filters, null);
				if (stores.size() > 0) {
					store = stores.get(0);
				}
				else {
					msg = "第" + index + "行." + "客户【" + storeName + "】不存在";
					ExceptionUtil.throwServiceException(msg);
				}
			}

			cell = sheet.getCell(3, i);
			productsn = cell.getContents().trim();
			if (StringUtils.isEmpty(productsn)) {
				throw new RuntimeException("第" + index + "行,产品编码为空");
			}
			filters.clear();
			filters.add(Filter.eq("vonderCode", productsn));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			Product product = productService.find(filters);
			if (product == null) {
				msg = "第" + index + "行." + "产品【" + productsn + "】不存在";
				ExceptionUtil.throwServiceException(msg);
			}

			//最小数量
			cell = sheet.getCell(4, i);
			min = cell.getContents().trim();
			if (StringUtils.isEmpty(min)) {
				msg = "第" + index + "行." + "最小数量为空";
				ExceptionUtil.throwServiceException(msg);
			}

			BigDecimal minQuantity = null;
			try {
				minQuantity = new BigDecimal(min);
			}
			catch (Exception e) {
				msg = "第" + index + "行." + "最小数量格式错误";
				ExceptionUtil.throwServiceException(msg);
			}

			//最大数量
			cell = sheet.getCell(5, i);
			max = cell.getContents().trim();
			if (StringUtils.isEmpty(max)) {
				msg = "第" + index + "行." + "最大数量为空";
				ExceptionUtil.throwServiceException(msg);
			}

			BigDecimal maxQuantity = null;
			try {
				maxQuantity = new BigDecimal(max);
			}
			catch (Exception e) {
				msg = "第" + index + "行." + "最大数量格式错误";
				ExceptionUtil.throwServiceException(msg);
			}

			//价格
			cell = sheet.getCell(6, i);
			price = cell.getContents().trim();
			if (StringUtils.isEmpty(price)) {
				msg = "第" + index + "行." + "价格为空";
				ExceptionUtil.throwServiceException(msg);
			}

			BigDecimal excutePrice = null;
			try {
				excutePrice = new BigDecimal(price);
			}
			catch (Exception e) {
				msg = "第" + index + "行." + "价格格式错误";
				ExceptionUtil.throwServiceException(msg);
			}

			//开始时间
			cell = sheet.getCell(7, i);
			stareTime = cell.getContents().trim();
			if (StringUtils.isEmpty(stareTime)) {
				msg = "第" + index + "行." + "开始时间为空";
				ExceptionUtil.throwServiceException(msg);
			}

			Date startDate = null;
			try {
				startDate = formatter.parse(stareTime);
			}
			catch (Exception e) {
				msg = "第"
						+ index
						+ "行."
						+ "开始时间【"
						+ stareTime
						+ "】格式错误,格式：2001-01-01";
				ExceptionUtil.throwServiceException(msg);
			}

			//结束时间
			cell = sheet.getCell(8, i);
			stopTime = cell.getContents().trim();
			if (StringUtils.isEmpty(stopTime)) {
				msg = "第" + index + "行." + "结束时间为空";
				ExceptionUtil.throwServiceException(msg);
			}
			Date endDate = null;
			try {
				endDate = formatter.parse(stopTime);
			}
			catch (Exception e) {
				msg = "第"
						+ index
						+ "行."
						+ "结束时间【"
						+ stopTime
						+ "】格式错误,格式：2001-01-01";
				ExceptionUtil.throwServiceException(msg);
			}

			Policy policy = null;
			if (dataMap.get(name) != null) {
				policy = this.find(dataMap.get(name));
			}
			else {
				if (this.exists(Filter.eq("name", name),
						Filter.eq("applyType", 2))) {
					throw new RuntimeException("第"
							+ index
							+ "行,政策名称【"
							+ name
							+ "】的促销政策已经存在！");
				}
				else {
					policy = this.createPolicy(name,
							applyTypes,
							saleOrg,
							store,
							startDate,
							endDate,
							policyType,
							null);
				}
			}
			this.createPolicyProducts(policy,
					minQuantity,
					maxQuantity,
					excutePrice,
					product);

			dataMap.put(name, policy.getId());
			success++;

		}

		int result = rows - 1;
		msg = "msg:" + "总数" + result + "行,成功导入" + success + " 行. ";
		return msg;
	}

	public Policy createPolicy(String name, Integer applyTypes,
			SaleOrg saleOrg, Store store, Date startDate, Date endDate,
			Integer policyType, String memo) {
		Policy policy = new Policy();
		policy.setDocStatus(0);
		policy.setCreator(storeMemberService.getCurrent());
		policy.setPolicyType(policyType);
		policy.setSaleOrg(saleOrg);
		policy.setStartDate(startDate);
		policy.setEndDate(endDate);
		policy.setName(name);
		policy.setApplyType(applyTypes);
		policy.setStore(store);
		policy.setMemo(memo);
		policy.setSn(SnUtil.generateSn());
		save(policy);
		return policy;
	}

	public void createPolicyProducts(Policy policy, BigDecimal minQuantity,
			BigDecimal maxQuantity, BigDecimal excutePrice, Product product) {
		PolicyProduct policyProduct = new PolicyProduct();
		policyProduct.setPolicy(policy);
		policyProduct.setProduct(product);
		policyProduct.setProductCategory(product.getProductCategory());
		policyProduct.setExcutePrice(excutePrice);
		policyProduct.setMaxQuantity(maxQuantity);
		policyProduct.setMinQuantity(minQuantity);
		policyProductService.save(policyProduct);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findlistdate(String sn, String name,
			Long storeId, Integer applyType, Integer[] status, Long[] ids) {

		return policyDao.findlistdate(sn, name, storeId, applyType, status, ids);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPolicyGiftPageByProductId(
			Long productId, Long storeId, Pageable pageable) {
		// TODO 自动生成的方法存根
		return policyDao.findPolicyGiftPageByProductId(productId,
				storeId,
				pageable);
	}

	@Override
	@Transactional
	public void checkPolicy(Policy policy) {
		if (policy.getDocStatus() != 0) {
			ExceptionUtil.throwServiceException("单据非已保存状态，不能审核！");
		}
		policy.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完)3.作废
		update(policy);
		orderFullLinkService.addFullLink(18,
				null,
				policy.getSn(),
				ConvertUtil.convertI18nMsg("单据审核"),
				null);
	}

	@Override
	@Transactional(readOnly = true)
	public int count(String sn, String name, Long storeId, Integer applyType,
			Integer[] status, Long[] ids) {
		return policyDao.count(sn, name, storeId, applyType, status, ids);
	}

}
