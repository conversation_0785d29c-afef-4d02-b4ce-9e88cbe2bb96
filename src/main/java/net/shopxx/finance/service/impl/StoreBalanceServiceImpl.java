package net.shopxx.finance.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.finance.dao.StoreBalanceDao;
import net.shopxx.finance.entity.StoreBalance;
import net.shopxx.finance.service.StoreBalanceService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/*
 * Service - 可发货余额
 */
@Service("storeBalanceServiceImpl")
public class StoreBalanceServiceImpl extends BaseServiceImpl<StoreBalance>
		implements StoreBalanceService {

	@Resource(name = "storeBalanceDao")
	private StoreBalanceDao storeBalanceDao;

	@Override
	@Transactional(readOnly = true)
	public BigDecimal findBalance(Long storeId, Long organizationId,Long sbuId) {
		return storeBalanceDao.findBalance(storeId, organizationId,sbuId);
	}

	@Override
	@Transactional(readOnly = true)
	public BigDecimal findCheckBalance(Long storeId, Long organizationId) {
		return storeBalanceDao.findCheckBalance(storeId, organizationId);
	}

	@Override
	@Transactional
	public void account(String lastDate, String currentDate, Integer flag) {

		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();

		if (flag == 0) {
//			sql.append("select count(1) from xx_store_balance sb");
//			sql.append(" where sb.balance_date = ? and sb.company_info_id = ?");
//			int count = storeBalanceDao.getNativeDao().findInt(sql.toString(),
//					new Object[] { currentDate, companyInfoId });
//			if (count == 0) {
//				//月结失败，请先联系管理员初始化数据
//				ExceptionUtil.throwServiceException("18751");
//			}

			sql.setLength(0);
			sql.append("select id from xx_store s where is_main_store = 0 and company_info_id = ?");
			List<Map<String, Object>> storeList = storeBalanceDao.getNativeDao()
					.findListMap(sql.toString(),
							new Object[] { companyInfoId },
							0);
			String nextDate = DateUtil.convert(DateUtil.addDate("MM",
					1,
					DateUtil.convert(currentDate, "yyyy-MM")), "yyyy-MM");
			for (Map<String, Object> map : storeList) {
				Long storeId = Long.parseLong(map.get("id").toString());
				BigDecimal balance = storeBalanceDao.findEndBalance(storeId);

				sql.setLength(0);
				sql.append("insert into xx_store_balance(company_info_id,create_date,modify_date,balance_date,begin_amount,store)");
				sql.append(" values(?,?,?,?,?,?)");
				Date now = new Date();
				storeBalanceDao.getNativeDao().insert(sql.toString(),
						new Object[] { companyInfoId,
								now,
								now,
								nextDate,
								balance,
								storeId });
			}

			sql.setLength(0);
			sql.append("update xx_account_parameter set balance_date = ? where company_info_id = ?");
			storeBalanceDao.getNativeDao().update(sql.toString(),
					new Object[] { currentDate, companyInfoId });
		}
		else {
			String preDate = DateUtil.convert(DateUtil.addDate("MM",
					-1,
					DateUtil.convert(lastDate, "yyyy-MM")), "yyyy-MM");
			sql.setLength(0);
			sql.append("select count(1) from xx_store_balance sb where sb.balance_date = ? and sb.company_info_id = ?");
			int count = storeBalanceDao.getNativeDao().findInt(sql.toString(),
					new Object[] { lastDate, companyInfoId });
			if (count == 0) {
				//{0}是最早的月结月份，不能进行反月结
				ExceptionUtil.throwServiceException("18750", lastDate);
			}

			//插入历史表
			sql.setLength(0);
			sql.append("insert into xx_store_balance_h(id, company_info_id, create_date, modify_date, balance_date, begin_amount, store, add_date)");
			sql.append(" select id, company_info_id, create_date, modify_date, balance_date, begin_amount, store, ?");
			sql.append(" from xx_store_balance where balance_date = ? and company_info_id = ?");
			storeBalanceDao.getNativeDao().insert(sql.toString(),
					new Object[] { new Date(), currentDate, companyInfoId });

			//删除客户期初
			sql.setLength(0);
			sql.append("delete from xx_store_balance where balance_date = ? and company_info_id = ?");
			storeBalanceDao.getNativeDao().delete(sql.toString(),
					new Object[] { currentDate, companyInfoId });

			//更新最后结账日期
			sql.setLength(0);
			sql.append("update xx_account_parameter set balance_date = ? where company_info_id = ?");
			storeBalanceDao.getNativeDao().update(sql.toString(),
					new Object[] { preDate, companyInfoId });
		}
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args) {
		return storeBalanceDao.findPage(pageable, args);
	}

	@Override
	@Transactional(readOnly = true)
	public Integer countForExcel(Object[] args) {
		return storeBalanceDao.countForExcel(args);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findList(Long[] ids, Object[] args,
			Integer page, Integer size) {
		return storeBalanceDao.findList(ids, args, page, size);
	}

	@Override
	@Transactional(readOnly = true)
	public String getBalanceDate() {
		return storeBalanceDao.getBalanceDate();
	}

	@Override
	@Transactional(readOnly = true)
	public Map<String, Object> findBalanceSbu(Long storeId, Long sbu,
			Long organizationId,Long saleOrgId) {

		return storeBalanceDao.findBalanceSbu(storeId, sbu, organizationId,saleOrgId);
	}

}