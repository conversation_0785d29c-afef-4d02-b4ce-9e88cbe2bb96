package net.shopxx.finance.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.aftersales.entity.Aftersale;
import net.shopxx.aftersales.service.AftersaleService;
import net.shopxx.base.core.*;
import net.shopxx.base.core.exception.DaoException;
import net.shopxx.base.core.exception.IllegalDataException;
import net.shopxx.base.core.util.*;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.Organization;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.OrganizationService;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SbuService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.service.TotalDateService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.finance.entity.Payment;
import net.shopxx.finance.service.PaymentService;
import net.shopxx.finance.service.PolicyEntryService;
import net.shopxx.intf.service.IntfOrderMessageToService;
import net.shopxx.member.dao.DepositRechargeDao;
import net.shopxx.member.entity.CustomerRecharge;
import net.shopxx.member.entity.DepositAttach;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.DepositRecharge.Status;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.CustomerRechargeService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service("policyEntryServiceImpl")
public class PolicyEntryServiceImpl extends
		WfBillBaseServiceImpl<DepositRecharge> implements PolicyEntryService {

	private static String token = Global.getLoader()
			.getProperty("synintf.token");
	private static String host = Global.getLoader()
			.getProperty("synintf.zbUrl");

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoService;
	@Resource(name = "paymentServiceImpl")
	private PaymentService paymentService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "depositRechargeDao")
	private DepositRechargeDao depositRechargeDao;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "intfOrderMessageToServiceImpl")
	private IntfOrderMessageToService intfOrderMessageToService;
	@Resource(name = "organizationServiceImpl")
	private OrganizationService organizationService;
	@Resource(name = "sbuServiceImpl")
	private SbuService sbuService;
	@Resource(name = "totalDateServiceImpl")
	private TotalDateService totalDateService;
	@Resource(name = "customerRechargeServiceImpl")
	private CustomerRechargeService customerRechargeService;
	@Resource(name = "aftersaleServiceImpl")
	private AftersaleService aftersaleService;
	

	@Override
	@Transactional
	public void check(DepositRecharge depositRecharge, Integer flag,
			String note, BigDecimal actualAmount) {

		depositRecharge.setNote(note);
		depositRecharge.setCheckDate(new Date());
		depositRecharge.setOperator(storeMemberService.getCurrent());
		if (flag == 2) {
			depositRecharge.setStatus(DepositRecharge.Status.failure); // 审核未通过
			if (depositRecharge.getType() == 1) {
				// 客户余额充值
				depositRecharge.setDocStatus(3); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
													// 2.已处理(流程走完) 3.关闭
			}
			update(depositRecharge);
		}
		else if (flag == 1) {
			depositRecharge.setStatus(DepositRecharge.Status.success); // 审核通过
			if (actualAmount == null) {
				actualAmount = depositRecharge.getAmount();
			}
			depositRecharge.setActualAmount(actualAmount);

			depositRecharge.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
												// 2.已处理(流程走完) 3.关闭
			update(depositRecharge);

			orderFullLinkService.addFullLink(5,
					null,
					depositRecharge.getSn(),
					ConvertUtil.convertI18nMsg("18702",
							new Object[] { "政策录入申请" }),
					null);

			// 写接口表
			//saveIntfAtCheck(depositRecharge);
		}

	}

//推送接口
	public void saveIntfAtCheck(DepositRecharge depositRecharge)
			throws Exception {
		Long companyInfoId = depositRecharge.getCompanyInfoId();
		CompanyInfo companyInfo = companyInfoService.find(companyInfoId);
		System.out.println("推送接口");
		if ("nature".equals(companyInfo.getCompany_code())) {
			String msg = "";
			if (depositRecharge.getHasInvoice() == 0) {//0 无发票
				//无发票：政策类型+ 发票类型,ERP现金流项目默认空
				System.out.println("进入 政策类型+ 发票类型");
				msg = intfOrderMessageToService.savePolicyIntf(depositRecharge);
				if (!msg.equals("S")) {
					ExceptionUtil.throwServiceException("操作失败!ERP返回信息：" + msg);
				}

			}
			else { //有发票：政策类型+ERP现金流项目,发票类型 默认空
				System.out.println("进入 政策类型+ERP现金流项目");
				msg = intfOrderMessageToService.savePolicyHaveInvoiceIntf(depositRecharge);
				if (!msg.equals("S")) {
					ExceptionUtil.throwServiceException("操作失败!ERP返回信息：" + msg);
				}

			}
		}
	}

	@Override
	@Transactional
	public void checkStoreRechargeWf(DepositRecharge depositRecharge,
			String note, BigDecimal actualAmount, Long objConfId) {
		
		//总账日期校验
		this.isCheckTotalDate(depositRecharge);
		depositRecharge.setNote(note);
		if (actualAmount == null) {
			actualAmount = depositRecharge.getAmount();
		}
		depositRecharge.setActualAmount(actualAmount);
		if (depositRecharge.getActualAmount() != null) {
			//红字金额大于0
			SystemDict invoiceType = depositRecharge.getInvoiceType();
			if (invoiceType != null) {
				//无发票
				if (invoiceType.getValue().toString().contains("红字")) {
					//无发票+红字
					if (depositRecharge.getActualAmount().compareTo(BigDecimal.ZERO) <= 0) {
						ExceptionUtil.throwServiceException("无发票红字发票类型实际录入金额不能小于等于0");
					}
				}else {
					//非红字小于0
					if (depositRecharge.getActualAmount().compareTo(BigDecimal.ZERO) >= 0) {
						//无发票+非红字
						ExceptionUtil.throwServiceException("无发票非红字发票类型实际录入金额不能大于等于0");
					}
				}
			}else {
				//有发票
				if (depositRecharge.getActualAmount().compareTo(BigDecimal.ZERO) <= 0) {
					ExceptionUtil.throwServiceException("有发票实际录入金额不能小于等于0");
				}
			}
		}else {
			ExceptionUtil.throwServiceException("实际录入金额不能为空");
		}
		// 创建流程实例
		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}
		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());
        ExceptionUtil.throwServiceException("创建流程的代码被注释，请联系管理员");
        // 单据状态 0.已保存(没有流程) 1.已提交 2.已处理(流程走完)// 3.关闭
		depositRecharge.setDocStatus(1); 				
		update(depositRecharge);
		orderFullLinkService.addFullLink(5,null,
				depositRecharge.getSn(),ConvertUtil.convertI18nMsg("18701",
				new Object[] { wfTemp.getWfTempName() }),null);

	}

	/**
	 * 用户余额充值
	 */
	public void storeMemberRecharge(Long companyInfoId,
			StoreMember storeMember, Payment payment, BigDecimal modifyBalance,
			DepositRecharge depositRecharge) {

		// StoreMember pStoreMember =
		// storeMemberService.findByMemberAndCompanyInfoId(member,
		// companyInfoId);
		// 生成一张付款单
		initPayment(companyInfoId,
				storeMember,
				payment,
				modifyBalance,
				depositRecharge);
		// 修改用户余额
		storeMember.setBalance(storeMember.getBalance().add(modifyBalance));
		storeMemberService.update(storeMember);
	}

	/**
	 * 客户余额充值
	 */
	public void storeRecharge(Long companyInfoId, StoreMember storeMember,
			Payment payment, BigDecimal modifyBalance,
			DepositRecharge depositRecharge) {

		// 生成一张付款单
		initPayment(companyInfoId,
				storeMember,
				payment,
				modifyBalance,
				depositRecharge);
		// 修改客户余额
		Store store = depositRecharge.getStore();
		store.setBalance(store.getBalance().add(modifyBalance));
		storeService.update(store);

	}

	/**
	 * 机构预算充值
	 */
	public void saleOrgBudget(Long companyInfoId, StoreMember storeMember,
			Payment payment, BigDecimal modifyBalance,
			DepositRecharge depositRecharge) {
		// 生成一张付款单
		initPayment(companyInfoId,
				storeMember,
				payment,
				modifyBalance,
				depositRecharge);
		SaleOrg saleOrg = depositRecharge.getSaleOrg();
		saleOrg.setBudget(modifyBalance);
		saleOrgService.update(saleOrg);
	}

	/**
	 * 客户预算充值
	 */
	public void storeBudget(Long companyInfoId, StoreMember storeMember,
			Payment payment, BigDecimal modifyBalance,
			DepositRecharge depositRecharge) {

		// 生成一张付款单
		initPayment(companyInfoId,
				storeMember,
				payment,
				modifyBalance,
				depositRecharge);
		Store store = depositRecharge.getStore();
		store.setBudget(modifyBalance);
		storeService.update(store);

	}

	public void initPayment(Long companyInfoId, StoreMember storeMember,
			Payment payment, BigDecimal modifyBalance,
			DepositRecharge depositRecharge) {

		CompanyInfo companyInfo = companyInfoService.find(companyInfoId);
		Integer paymentPrefix = companyInfo.getPaymentPrefix();
		String hexString = Integer.toHexString(paymentPrefix).toUpperCase();
		if (hexString.length() == 1) {
			hexString = "0" + hexString;
		}
		payment = new Payment();
		String sn = SnUtil.generateSn();
		payment.setSn(sn);
		payment.setUnifiedSn(sn);
		payment.setStatus(1);// 支付成功
		payment.setMethod(1);
		payment.setFee(new BigDecimal(0));
		payment.setAmount(modifyBalance);
		payment.setPaymentDate(new Date());
		payment.setPaymentPluginId(null);
		payment.setExpire(null);
		if (depositRecharge.getType() == 0) { // 用户余额充值
			payment.setType(1);
			payment.setPayType(0);
			payment.setStoreMember(storeMember);
		}
		else if (depositRecharge.getType() == 1) { // 客户余额充值
			payment.setType(3);
			payment.setPayType(1);
			payment.setStore(depositRecharge.getStore());
		}
		else if (depositRecharge.getType() == 2) { // 机构预算充值
			payment.setType(6);
			payment.setPayType(6);
			payment.setSaleOrg(depositRecharge.getSaleOrg());
		}
		else if (depositRecharge.getType() == 3) { // 客户预算充值
			payment.setType(7);
			payment.setPayType(7);
			payment.setStore(depositRecharge.getStore());
		}
		payment.setTermType(3);
		payment.setElseSn(depositRecharge.getSn());
		payment.setOperator(storeMemberService.getCurrent().getUsername());
		payment.setOperateStoreMember(storeMemberService.getCurrent());
		payment.setCompanyInfoId(companyInfoId);
		paymentService.save(payment);
	}

	/**
	 * 用户、客户余额充值列表数据
	 */
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			Pageable pageable) {
		return depositRechargeDao.findPage(type,
				sn,
				storeMemberId,
				storeId,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				null,
				minPrice,
				maxPrice,
				pageable);
	}

	@Override
	public void agreeBack(Wf wf) {

		if (wf.getStat().intValue() == 2) {
			DepositRecharge depositRecharge = find(wf.getObjId());
			if (depositRecharge.getType().intValue() == 1) {
				depositRecharge.setCheckDate(new Date());
				depositRecharge.setOperator(storeMemberService.getCurrent());
				depositRecharge.setStatus(DepositRecharge.Status.success); // 审核通过
				depositRecharge.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
													// 2.已处理(流程走完) 3.关闭
				update(depositRecharge);

				// 写接口表（eas收款单推送）
				//saveIntfAtCheck(depositRecharge);
			}
		}
	}

	@Override
	public void startBack(Wf wf) {
		DepositRecharge depositRecharge = find(wf.getObjId());
		wf.setStore(depositRecharge.getStore());
		wfBaseService.update(wf);
	}

    @Override
	@Transactional
	public void save(Store store, DepositRecharge depositRecharge,
			Integer fullLinkType, Long rechargeTypeId,Integer sourceType,Long invoiceTypeId) {
		//接收数据不校验
	    if (sourceType==1){
            //总账日期校验
            this.isCheckTotalDate(depositRecharge);
        }
		SystemDict invoiceType = systemDictService.find(invoiceTypeId);
		if (depositRecharge.getHasInvoice() == 0) {
			//0 无发票,有发票类型
			if (invoiceType == null) {
				ExceptionUtil.throwServiceException("无发票发票类型不能为空");
			}
		}else {
			SystemDict cashProject = depositRecharge.getCashProject();
			if (cashProject == null) {
				ExceptionUtil.throwServiceException("有发票ERP现金流项目类型不能为空");
			}
		}
		if (sourceType==1){
            if (depositRecharge.getAmount() != null) {
                if (invoiceType != null) {//无发票（有发票类型：红字：录入金额填正数，传负数；非红字：录入金额填负数，传正数）
                    if (invoiceType.getValue().toString().contains("红字")) {//无发票+红字
                        if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                            ExceptionUtil.throwServiceException("红字发票录入金额不能小于等于0");
                        }
                    }else if (!invoiceType.getValue().toString().contains("红字")) {//非红字小于0
                        if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) >= 0) {//无发票+非红字
                            ExceptionUtil.throwServiceException("非红字发票录入金额不能大于等于0");
                        }
                    }
                }else {
                    if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        ExceptionUtil.throwServiceException("有发票录入金额不能小于0");
                    }
                }
            }else {
                ExceptionUtil.throwServiceException("录入金额不能为空");
            }
        }

		depositRecharge.setType(1);
		depositRecharge.setInvoiceType(invoiceType);  //发票类型
		depositRecharge.setRechargeType(systemDictService.find(rechargeTypeId));
		depositRecharge.setStatus(Status.wait);
		depositRecharge.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)
											// 2.已处理(流程走完) 3.关闭
		depositRecharge.setStore(store);
		if (sourceType==1){
            depositRecharge.setCreator(storeMemberService.getCurrent());
            depositRecharge.setStoreMember(storeMemberService.getCurrent());
        }

		depositRecharge.setActualAmount(depositRecharge.getAmount() == null ? BigDecimal.ZERO : depositRecharge.getAmount());
		if (sourceType==1){
            depositRecharge.setSn(SnUtil.getPolicyEntrySn());
        }else {
            depositRecharge.setSn(SnUtil.getPolicyEntrySnType(Long.valueOf(9)));
        }
		if (depositRecharge.getBankCard() == null
				|| depositRecharge.getBankCard().getId() == null) {
			depositRecharge.setBankCard(null);
		}
		// 附件
		List<DepositAttach> depositAttachs = depositRecharge.getDepositAttachs();
		for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext();) {
			DepositAttach depositAttach = iterator.next();
			if (depositAttach == null || depositAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (depositAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			depositAttach.setFileName(depositAttach.getName()+ "."
					+ depositAttach.getSuffix());
			depositAttach.setDepositRecharge(depositRecharge);
			if (sourceType==1){
                depositAttach.setStoreMember(storeMemberService.getCurrent());
            }
		}
		depositRecharge.setDepositAttachs(depositAttachs);
		depositRecharge.setPermitThroughName(depositRecharge.getPermitThroughName());
		save(depositRecharge);
		Aftersale aftersale = depositRecharge.getAftersale();
		if(aftersale != null){
			aftersale.setDepositRechargeSn(depositRecharge.getSn());
			aftersaleService.update(aftersale);
		}

		if (sourceType==1){
            orderFullLinkService.addFullLink(fullLinkType,null,depositRecharge.getSn(),
                    ConvertUtil.convertI18nMsg("18700", new Object[] { "政策录入申请" }),null);
        }else {
			orderFullLinkService.addFullLinkWithOutMember(fullLinkType,null,depositRecharge.getSn(),
					ConvertUtil.convertI18nMsg("18700", new Object[] { "政策录入申请" }),null);
		}

	}

	@Override
	@Transactional
	public void update(Store store, DepositRecharge depositRecharge,
			 Integer fullLinkType, Integer isSubmit) {
		//总账日期校验
		this.isCheckTotalDate(depositRecharge);
		DepositRecharge dr = find(depositRecharge.getId());
		//校验政策单状态
		this.checkDepositRechargeStatus(dr,1,"已提交","提交");
		if (isSubmit != null && isSubmit.intValue() == 1) {
			// 实际充值金额
			if (dr.getDocStatus() == 1) {//实际录入金额可填写
				if (depositRecharge.getActualAmount() != null) {
					SystemDict invoiceType = depositRecharge.getInvoiceType();
					if (invoiceType != null) {//无发票（有发票类型：红字：录入金额填正数，传负数；非红字：录入金额填负数，传正数）
						if (invoiceType.getValue().toString().contains("红字")) {//无发票+红字
							if (depositRecharge.getActualAmount()
									.compareTo(BigDecimal.ZERO) <= 0) {
								ExceptionUtil.throwServiceException("红字发票实际录入金额不能小于等于0");
							}

						} else {//非红字小于0
							if (depositRecharge.getActualAmount()
									.compareTo(BigDecimal.ZERO) >= 0) {//无发票+非红字
								ExceptionUtil.throwServiceException("非红字发票实际录入金额不能大于等于0");
							}
						}
					} else {//有发票（无发票类型，录入金额填正数，传正数）
						if (depositRecharge.getActualAmount()
								.compareTo(BigDecimal.ZERO) <= 0) {//无发票+非红字
							ExceptionUtil.throwServiceException("有发票实际录入金额不能小于0");
						}
					}

				} else {
					ExceptionUtil.throwServiceException("实际录入金额不能为空");
				}
			}
			dr.setActualAmount(depositRecharge.getActualAmount());
			dr.setDocStatus(1);
			dr.setBalanceMonth(depositRecharge.getBalanceMonth());
			dr.setSubmitDate(new Date());
			orderFullLinkService.addFullLink(fullLinkType,null,
					depositRecharge.getSn(),ConvertUtil.convertI18nMsg("18703",
				    new Object[] { "政策录入申请", "提交政策录入申请" }),null);
		}
		if(depositRecharge.getRegionalManager()!=null){
			dr.setRegionalManager(depositRecharge.getRegionalManager());
		}
		dr.setActualAmount(depositRecharge.getAmount());
		dr.setStore(store);
		//机构
		dr.setSaleOrg(depositRecharge.getSaleOrg());
		//GL日期
		dr.setGlDate(depositRecharge.getGlDate());
		//sbu
		dr.setSbu(depositRecharge.getSbu());
		dr.setInvoiceType(depositRecharge.getInvoiceType());
		//经营组织
		dr.setOrganization(depositRecharge.getOrganization());
		dr.setImage(depositRecharge.getImage());
		dr.setImage2(depositRecharge.getImage2());
		dr.setImage3(depositRecharge.getImage3());
		if (depositRecharge.getAmount() != null) {
			SystemDict invoiceType = dr.getInvoiceType();
			if (invoiceType != null) {//无发票（有发票类型：红字：录入金额填正数，传负数；非红字：录入金额填负数，传正数）
				if (invoiceType.getValue().toString().contains("红字")) {//无发票+红字
					if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
						ExceptionUtil.throwServiceException("无发票红字发票类型录入金额不能小于等于0");
					}
				} else {//非红字小于0
					if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) >= 0) {//无发票+非红字
						ExceptionUtil.throwServiceException("无发票非红字发票录入金额不能大于等于0");
					}
				}
			} else {//有发票（无发票类型，录入金额填正数，传正数）
				if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
					ExceptionUtil.throwServiceException("有发票录入金额不能小于0");
				}
			}
		} else {
			ExceptionUtil.throwServiceException("录入金额不能为空");
		}
		dr.setAmount(depositRecharge.getAmount());
		dr.setMemo(depositRecharge.getMemo());
		if (depositRecharge.getPolicyType()!=null){
			dr.setPolicyType(depositRecharge.getPolicyType());
		}
		dr.setApplyDate(depositRecharge.getApplyDate());
		if (depositRecharge.getBankCard() == null
				|| depositRecharge.getBankCard().getId() == null) {
			dr.setBankCard(null);
		}else {
			dr.setBankCard(depositRecharge.getBankCard());
		}
		// 附件
		List<DepositAttach> depositAttachs = depositRecharge.getDepositAttachs();
		for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext();) {
			DepositAttach depositAttach = iterator.next();
			if (depositAttach == null || depositAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (depositAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			depositAttach.setFileName(depositAttach.getName()
					+ "."
					+ depositAttach.getSuffix());
			depositAttach.setDepositRecharge(dr);
			depositAttach.setStoreMember(storeMemberService.getCurrent());
		}
		dr.getDepositAttachs().clear();
		dr.getDepositAttachs().addAll(depositAttachs);
		dr.setPermitThroughName(depositRecharge.getPermitThroughName());
		update(dr);
	}

	@Override
	@Transactional
	public void cancel(DepositRecharge depositRecharge, Integer fullLinkType) {
		//单据状态  0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.关闭
		depositRecharge.setDocStatus(3); 
		update(depositRecharge);
		List<String> orderSns = new ArrayList<String>();
		orderSns.add(depositRecharge.getSn());
		orderFullLinkService.addFullLink(fullLinkType,orderSns,
				depositRecharge.getSn(),"作废本次政策录入",null);
	}

	@Override
	public Page<Map<String, Object>> newfindPage(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			String firstTime, String lastTime, Long organizationId, Long sbuId,
			Pageable pageable) {
		return depositRechargeDao.newfindPage(type,
				sn,
				storeMemberId,
				storeId,
				saleOrgId,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				null,
				minPrice,
				maxPrice,
				firstTime,
				lastTime,
				organizationId,
				sbuId,
				null, null, null, null, null, pageable);

	}

	@Override
	public Integer countDepositRecharge(Integer[] type, String sn,
			Long storeMemberId, Long storeId, Long saleOrgId, Integer[] status,
			Integer[] docstatus, Long[] rechargeTypeId, Long creatorId,
			Long operatorId, BigDecimal minPrice, BigDecimal maxPrice,
			String firstTime, String lastTime, Long sbuId, Long[] ids,
			Long organizationId,Pageable pageable, Integer page, Integer size) {
		return depositRechargeDao.countDepositRecharge(type,
				sn,
				storeMemberId,
				storeId,
				saleOrgId,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				null,
				minPrice,
				maxPrice,
				sbuId,
				firstTime,
				lastTime,
				ids,
				organizationId,
				pageable,
				page,
				size, null, null, null, null, null);
	}

	@Override
	public List<Map<String, Object>> findDepositRechargeList(Integer[] type,
			String sn, Long storeMemberId, Long storeId, Long saleOrgId,
			Integer[] status, Integer[] docstatus, Long[] rechargeTypeId,
			Long creatorId, Long operatorId, BigDecimal minPrice,
			BigDecimal maxPrice, String firstTime, String lastTime, Long sbuId,
			Long[] ids,Long organizationId, Pageable pageable, Integer page, Integer size) {
		return depositRechargeDao.findDepositRechargeList(type,
				sn,
				storeMemberId,
				storeId,
				saleOrgId,
				status,
				docstatus,
				rechargeTypeId,
				creatorId,
				operatorId,
				null,
				minPrice,
				maxPrice,
				sbuId,
				firstTime,
				ids,
				organizationId,
				lastTime,
				pageable,
				page,
				size, null, null, null, null, null);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findShowPage(Long storeId, String sn,
			Pageable page) {
		return depositRechargeDao.findShowPage(storeId, sn, page);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findList(Long storeId, String sn) {
		return depositRechargeDao.findList(storeId, sn);
	}

	@Override
	@Transactional
	public String policyAddImport(MultipartFile multipartFile, Integer type)
			throws Exception {

		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		int success = 0;
		tempFile = new File(System.getProperty("java.io.tmpdir")
				+ "/upload_"
				+ UUID.randomUUID()
				+ ".tmp");
		if (!tempFile.getParentFile().exists()) {
			tempFile.getParentFile().mkdirs();
		}
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();
        System.out.println("==rows:"+rows);
         rows =rows-1;
		if (rows > 501) {
			if (StringUtils.isNotBlank(sheet.getCell(0, 500).getContents())) {
				ExceptionUtil.throwServiceException("一次最多导入500条");
			}
			else {
				rows = 501;
			}
		}

		String storeMemberName;// 客户名称
		String erpSn; // ERP客户编码
		String rechargeTypeName; // 充值类型
		String priceStr; // 政策金额
		//String actualAmountStr; // 实际录入金额
		String balanceDateStr; // 对账月份
		String taxRateStr; // 税率
		String cashProjectName; // ERP现金流项目
		String invoiceTypeName; // 发票类型
		String policyTypeName; // 政策类型
		String organizationName; // 经营组织类型
		String applyDateStr; // 申请日期
		String glDateStr; // GL日期
		String memo; // 申请备注
		String isInvoice;//是否有发票
		String sbuName;//SBU
		String permitThroughName;//放行人

		List<Filter> filters = new ArrayList<Filter>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

		//----debug----
        for (int a = 1; a < rows; a++) {
            System.out.println("第" + a + "行：");
            for (int j = 0; j < 16; j++) {
                System.out.print(sheet.getCell(j, a).getContents()+",");
            }
			System.out.println();
        }
        //----debug----

		for (int i = 1; i < rows; i++) {

			cell = sheet.getCell(0, i);
			storeMemberName = cell.getContents();
			if (StringUtils.isBlank(storeMemberName)) {
				msg = "第" + i + "行." + "客户名称为空";
				ExceptionUtil.throwServiceException(msg);
			}
			cell = sheet.getCell(1, i);
			erpSn = cell.getContents();
			if (StringUtils.isBlank(erpSn)) {
				msg = "第" + i + "行." + "ERP编码为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			if (storeMemberName != null) {//平台使用多
				filters.add(Filter.eq("name", storeMemberName.trim()));
			}
			else if (erpSn != null) {//总部使用多
				filters.add(Filter.eq("outTradeNo", erpSn.trim()));
			}
			else if (storeMemberName == null && erpSn == null) {
				ExceptionUtil.throwServiceException("第" + i + "行." +"客户名称和ERP编码不能同时为空");
			}
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			List<Store> stores = storeService.findList(null, filters, null);
			if (stores == null || (stores == null && stores.size() == 0)) {
				ExceptionUtil.throwServiceException("第" + i + "行." +"通过ERP编码或名称查不到对应的客户");
			}
			//充值类型
			cell = sheet.getCell(2, i);
			rechargeTypeName = cell.getContents();
			if (StringUtils.isBlank(rechargeTypeName)) {
				msg = "第" + i + "行." + "充值类型为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("code", "DepositRechargeType"));
			filters.add(Filter.eq("value", rechargeTypeName.trim()));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> rechargeTypes = systemDictService.findList(null,
					filters,
					null);
			if (rechargeTypes == null
					|| (rechargeTypes == null && rechargeTypes.size() == 0)) {
				ExceptionUtil.throwServiceException("第" + i + "行." +"通过充值类型名称查不到对应的充值类型");
			}
			//政策金额
			cell = sheet.getCell(3, i);
			priceStr = cell.getContents();
			BigDecimal price = BigDecimal.ZERO;
			if (priceStr != null) {
				try {
					price = new BigDecimal(priceStr.trim());
				}
				catch (Exception e) {
					msg = "第" + i + "行." + "金额有误";
					ExceptionUtil.throwServiceException(msg);
				}
			}
			else {
				msg = "第" + i + "行." + "政策金额为空";
				ExceptionUtil.throwServiceException(msg);
			}
			//对账月份
			cell = sheet.getCell(4, i);
			balanceDateStr = cell.getContents();
			if (StringUtils.isBlank(balanceDateStr)) {
				msg = "第" + i + "行." + "对账月份为空";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!isValidMonth(balanceDateStr)) {
				msg = "第" + i + "行." + "对账月份格式有误，格式应为yyyy-MM";
				ExceptionUtil.throwServiceException(msg);
			}

			//税率
			Integer taxRateInteger = null;
			cell = sheet.getCell(5, i);
			taxRateStr = cell.getContents();
			String taxRate = taxRateStr.replace("OUT_VAT", "");
			if (taxRate.matches("\\d+")) {
				taxRateInteger = Integer.parseInt(taxRate);
			}

			cell = sheet.getCell(6, i);
			isInvoice = cell.getContents();
			if (StringUtils.isBlank(isInvoice)) {
				msg = "第" + i + "行." + "是否有发票为空";
				ExceptionUtil.throwServiceException(msg);
			}

			//ERP现金流项目
			cell = sheet.getCell(7, i);
			cashProjectName = cell.getContents();
			List<SystemDict> cashProjects = null;
			if (!StringUtils.isBlank(cashProjectName)) {
				filters.clear();
				filters.add(Filter.eq("code", "cashProject"));
				filters.add(Filter.eq("value", cashProjectName.trim()));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				filters.add(Filter.isNotNull("parent"));
				cashProjects = systemDictService.findList(null, filters, null);
				if (cashProjects == null
						|| (cashProjects == null && cashProjects.size() == 0)) {
					ExceptionUtil.throwServiceException("第" + i + "行." +"通过ERP现金流项目查不到对应的ERP现金流项目");
				}
			}

			//发票类型
			cell = sheet.getCell(8, i);
			invoiceTypeName = cell.getContents();
			List<SystemDict> invoiceTypes = null;
			if (!StringUtils.isBlank(invoiceTypeName)) {
				filters.clear();
				filters.add(Filter.eq("code", "invoiceType"));
				filters.add(Filter.eq("value", invoiceTypeName.trim()));
				filters.add(Filter.eq("companyInfoId", companyInfoId));
				filters.add(Filter.isNotNull("parent"));
				invoiceTypes = systemDictService.findList(null, filters, null);
				if (invoiceTypes == null
						|| (invoiceTypes == null && invoiceTypes.size() == 0)) {
					ExceptionUtil.throwServiceException("第" + i + "行." +"通过发票类型查不到对应的发票类型");
				}
			}
			//政策类型
			cell = sheet.getCell(9, i);
			policyTypeName = cell.getContents();
			List<SystemDict> policyTypes = null;
			if (StringUtils.isBlank(policyTypeName)) {
				msg = "第" + i + "行." + "政策类型为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("code", "policyType"));
			filters.add(Filter.eq("value", policyTypeName.trim()));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			filters.add(Filter.isNotNull("parent"));
			policyTypes = systemDictService.findList(null,
					filters,
					null);
			if (policyTypes == null
					|| (policyTypes == null && policyTypes.size() == 0)) {
				ExceptionUtil.throwServiceException("第" + i + "行." +"通过政策类型名称查不到对应的政策类型");
			}

			// 经营组织
			cell = sheet.getCell(10, i);
			organizationName = cell.getContents();
			if (StringUtils.isBlank(organizationName)) {
				msg = "第" + i + "行." + "经营组织为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("isEnabled", true));
			filters.add(Filter.eq("name", organizationName.trim()));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			List<Organization> organizations = organizationService.findList(null,
					filters,
					null);
			if (organizations == null
					|| (organizations == null && organizations.size() == 0)) {
				ExceptionUtil.throwServiceException("第" + i + "行." +"通过经营组织名称查不到对应的经营组织");
			}
			cell = sheet.getCell(11, i);
			applyDateStr = cell.getContents();
			if (StringUtils.isBlank(applyDateStr)) {
				msg = "第" + i + "行." + "申请日期为空";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!isValidDate(applyDateStr)) {
				msg = "第" + i + "行." + "申请日期格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(12, i);
			glDateStr = cell.getContents();
			if (StringUtils.isBlank(glDateStr)) {
				msg = "第" + i + "行." + "GL日期为空";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!isValidDate(glDateStr)) {
				msg = "第" + i + "行." + "GL日期格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(13, i);
			memo = cell.getContents();
			if (StringUtils.isBlank(storeMemberName)) {
				msg = "第" + i + "行." + "备注为空";
				ExceptionUtil.throwServiceException(msg);
			}

			cell = sheet.getCell(14, i);
			sbuName = cell.getContents();
			if (StringUtils.isBlank(sbuName)) {
				msg = "第" + i + "行." + "SBU为空";
				ExceptionUtil.throwServiceException(msg);
			}
			filters.clear();
			filters.add(Filter.eq("name", sbuName.trim()));
			filters.add(Filter.eq("status", true));
			List<Sbu> sbuList = sbuService.findList(null, filters, null);
			if (sbuList == null
					|| (sbuList == null && sbuList.size() == 0)) {
				ExceptionUtil.throwServiceException("第" + i + "行." +"通过SBU名称查不到对应的SBU");
			}
			cell = sheet.getCell(15, i);
			permitThroughName = cell.getContents();

			/*
			 * Date balanceDate = DateUtil.convertStrToDate(balanceDateStr,
			 * "yyyy-MM");
			 */
			Date applyDate = DateUtil.convert(applyDateStr + " 00:00:00");
			Date glDate = DateUtil.convert(glDateStr + " 00:00:00");

			createPolicy(stores.get(0),
					rechargeTypes.get(0),
					price,
					balanceDateStr,
					taxRateInteger,
					cashProjects == null ? null : cashProjects.get(0),
					invoiceTypes == null ? null
							: (invoiceTypes.size() == 0 ? null
									: invoiceTypes.get(0)),
					policyTypes.size() == 0 ? null
									: policyTypes.get(0),
					organizations.get(0),
					applyDate,
					glDate,
					memo,
					isInvoice,
					sbuList.size() == 0?null:sbuList.get(0),
					permitThroughName);
			success++;
		}
		int result = rows - 1;
		msg = "msg:" + "总数" + result + "行,成功导入" + success + " 行. ";
		System.out.println("导入："+msg);
		return msg;
	}

	public void createPolicy(Store store, SystemDict rechargeType,
			BigDecimal price, String balanceDate, Integer taxRateInteger,
			SystemDict cashProject, SystemDict invoiceType,
			SystemDict policyType, Organization organization, Date applyDate,
			Date glDate, String memo, String isInvoice, Sbu sbu,String permitThroughName) {
		DepositRecharge depositRecharge = new DepositRecharge();
		depositRecharge.setApplyDate(applyDate);
		depositRecharge.setGlDate(glDate);
		depositRecharge.setBalanceMonth(balanceDate);
		depositRecharge.setStore(store);
		SaleOrg saleOrg = store.getSaleOrg();
		depositRecharge.setSaleOrg(saleOrg);
		depositRecharge.setAmount(price);
		depositRecharge.setActualAmount(price);
		depositRecharge.setMemo(memo);
		depositRecharge.setOrganization(organization);
		depositRecharge.setTaxRate(new BigDecimal(taxRateInteger));
		depositRecharge.setPermitThroughName(permitThroughName);
		if (policyType == null) {
			ExceptionUtil.throwServiceException("政策类型不能为空");
		}
		depositRecharge.setPolicyType(policyType);
		if (rechargeType == null) {
			ExceptionUtil.throwServiceException("充值类型不能为空");
		}
		if (!"是".equals(isInvoice.trim())) {//0 无发票,有发票类型
			if (invoiceType == null) {
				ExceptionUtil.throwServiceException("无发票发票类型不能为空");
			}
			depositRecharge.setInvoiceType(invoiceType);
			depositRecharge.setHasInvoice(0);
		}
		else if ("是".equals(isInvoice.trim())) {
			if (cashProject == null) {
				ExceptionUtil.throwServiceException("有发票ERP现金流项目类型不能为空");
			}
			depositRecharge.setHasInvoice(1);
			depositRecharge.setCashProject(cashProject);
		}
		if (depositRecharge.getAmount() != null) {
			if (invoiceType != null) {//无发票（有发票类型：红字：录入金额填正数，传负数；非红字：录入金额填负数，传正数）
				if (invoiceType.getValue().toString().contains("红字")) {//无发票+红字
					if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
						ExceptionUtil.throwServiceException("红字发票录入金额不能小于等于0");
					}

				}
				else if (!invoiceType.getValue().toString().contains("红字")) {//非红字小于0
					if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) >= 0) {//无发票+非红字
						ExceptionUtil.throwServiceException("非红字发票录入金额不能大于等于0");
					}
				}
			}
			else {
				if (depositRecharge.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
					ExceptionUtil.throwServiceException("有发票录入金额不能小于0");
				}
			}
		}
		else {
			ExceptionUtil.throwServiceException("录入金额不能为空");
		}
		depositRecharge.setType(1);
		depositRecharge.setRechargeType(rechargeType);
		depositRecharge.setStatus(Status.wait);
		depositRecharge.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.处理中(有流程)  2.已处理(流程走完) 3.关闭
		depositRecharge.setCreator(storeMemberService.getCurrent());
		depositRecharge.setStoreMember(storeMemberService.getCurrent());
		//depositRecharge.setActualAmount(BigDecimal.ZERO);
		depositRecharge.setSn(SnUtil.getPolicyEntrySn());
		depositRecharge.setSbu(sbu);
		save(depositRecharge);
		orderFullLinkService.addFullLink(5,
				null,
				depositRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "政策录入申请" }),
				null);

	}

	private boolean isValidDate(String str) {
		boolean convertSuccess = true;
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			format.setLenient(false);
			format.parse(str);
		}
		catch (ParseException e) {
			convertSuccess = false;
		}
		return convertSuccess;
	}

	private boolean isValidMonth(String str) {
		boolean convertSuccess = true;
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
			format.setLenient(false);
			format.parse(str);
		}
		catch (ParseException e) {
			convertSuccess = false;
		}
		return convertSuccess;
	}

	@Override
	@Transactional
	public void checkStoreRecharge(DepositRecharge depositRecharge,
			String note, BigDecimal actualAmount) throws Exception {
		
		//总账日期校验
		this.isCheckTotalDate(depositRecharge);
		depositRecharge.setNote(note);
		depositRecharge.setCheckDate(new Date());
		depositRecharge.setOperator(storeMemberService.getCurrent());
		if (actualAmount == null) {
			actualAmount = depositRecharge.getAmount();
		}
		depositRecharge.setActualAmount(actualAmount);
		if (depositRecharge.getActualAmount() != null) {
			//红字金额大于0
			SystemDict invoiceType = depositRecharge.getInvoiceType();
			if (invoiceType != null) {//无发票
				if (invoiceType.getValue().toString().contains("红字")) {//无发票+红字
					if (depositRecharge.getActualAmount().compareTo(BigDecimal.ZERO) <= 0) {
						ExceptionUtil.throwServiceException("无发票红字发票类型实际录入金额不能小于等于0");
					}
				}else {
					//非红字小于0
					if (depositRecharge.getActualAmount().compareTo(BigDecimal.ZERO) >= 0) {
						//无发票+非红字
						ExceptionUtil.throwServiceException("无发票非红字发票类型实际录入金额不能大于等于0");
					}
				}
			}else {
				//有发票
				if (depositRecharge.getActualAmount()
						.compareTo(BigDecimal.ZERO) <= 0) {
					ExceptionUtil.throwServiceException("有发票实际录入金额不能小于等于0");
				}
			}

		}else {
			ExceptionUtil.throwServiceException("实际录入金额不能为空");
		}
		//客户充值
		CustomerRecharge customerRecharge = new CustomerRecharge();
		//客户
		customerRecharge.setStore(depositRecharge.getStore());
		//经营组织
		customerRecharge.setOrganization(depositRecharge.getOrganization());
		//sbu
		customerRecharge.setSbu(depositRecharge.getSbu());
		customerRecharge = customerRechargeService.saveOrUpdate(customerRecharge);
		if(!ConvertUtil.isEmpty(customerRecharge) && !ConvertUtil.isEmpty(customerRecharge.getId())){
			//政策金额
			customerRecharge.setPolicyBalance(
					customerRecharge.getPolicyBalance().add(depositRecharge.getActualAmount()));
			customerRechargeService.update(customerRecharge);
		}else{
			throw new RuntimeException("审核异常，请管理员进行维护");
		}
		depositRecharge.setOperator(storeMemberService.getCurrent());
		depositRecharge.setStatus(DepositRecharge.Status.success); // 审核通过
		 // 单据状态 0.已保存(没有流程) 1.已提交 2.已处理(流程走完)// 3.关闭
		depositRecharge.setDocStatus(2);				
		update(depositRecharge);
		orderFullLinkService.addFullLink(5,null,depositRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18701", new Object[] { "政策录入申请" }),null);
		String isIntegration  = SystemConfig.getConfig("isIntegration",WebUtils.getCurrentCompanyInfoId());
        if( isIntegration != null && "1".equals(isIntegration) ) {
        	// 写接口表
            saveIntfAtCheck(depositRecharge);
        }
	}
	
	
	private  void isCheckTotalDate(DepositRecharge depositRecharge){
		//机构
		if(ConvertUtil.isEmpty(depositRecharge.getSaleOrg())){
			throw new RuntimeException("机构不能为空");
		}
		//sbu
		if(ConvertUtil.isEmpty(depositRecharge.getSbu())){
			throw new RuntimeException("sbu不能为空");
		}
		//经营组织
		if(ConvertUtil.isEmpty(depositRecharge.getOrganization())){
			throw new RuntimeException("经营组织不能为空");
		}
		//Gl日期
		if(ConvertUtil.isEmpty(depositRecharge.getGlDate())){
			throw new RuntimeException("Gl日期不能为空");
		}
		
		SystemDict totalDateType =systemDictService.findSystemDictList("totalDateType", "应收账期").get(0);
		List<Map<String, Object>> mapList = totalDateService.findTotalDateList(true, depositRecharge.getSaleOrg().getId(),
				depositRecharge.getSbu().getId(), new Long[]{depositRecharge.getOrganization().getId()}, depositRecharge.getGlDate(),totalDateType);
		if(mapList.isEmpty()||mapList.size() ==0){
			throw new RuntimeException("GL日期不包含在总账日期内，请填写合适的单据日期");
		}
	}
	
	/**
	 * 校验政策单状态
	 */
	@Override
	public void checkDepositRechargeStatus(DepositRecharge depositRecharge, Integer status,
			String statusName, String operationName) {
		if(ConvertUtil.isEmpty(depositRecharge)){
			ExceptionUtil.throwServiceException("该政策单不存在");	
		}
		if (!ConvertUtil.isEmpty(depositRecharge.getDocStatus()) && depositRecharge.getDocStatus() != status) {
			ExceptionUtil.throwServiceException("只有单据状态为"+statusName+"的政策单才能"+operationName);	
		}
	}

	@Override
	public void synPolicyInformation(DepositRecharge depositRecharge) {
		String path = "/intf/policyEntry/syncPolicyEntryStatusToAftersale.jhtml";
		String url = host+path;
		Map<String, String> headers = new HashMap<String, String>();
		headers.put("Authorization", token);
		headers.put("Content-Type","application/json");
		Map<String, Object> info = new HashMap<String, Object>();
		info.put("aftersaleSn",depositRecharge.getSourceSn());     //售后单据号
		info.put("docStatus",depositRecharge.getDocStatus()); //单据状态
		info.put("amount",depositRecharge.getAmount()); //实际录入金额
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		info.put("checkDate",df.format(new Date())); //审核时间
		info.put("sn",depositRecharge.getSn()); //政策单号
		LogUtils.info("同步政策单数据到中板："+JsonUtils.toJson(info));
		String msg = HttpClientUtil.post(url,
				JsonUtils.toJson(info),
				headers);
		System.out.println("msg:"+msg);
	}
}
