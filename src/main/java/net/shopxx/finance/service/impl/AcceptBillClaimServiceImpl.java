package net.shopxx.finance.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.finance.dao.AcceptBillClaimDao;
import net.shopxx.finance.entity.AcceptBill;
import net.shopxx.finance.service.AcceptBillClaimService;
import net.shopxx.finance.service.AcceptBillService;
import net.shopxx.member.entity.DepositAttach;
import net.shopxx.member.entity.DepositPaymentBatch;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.DepositRecharge.Status;
import net.shopxx.member.entity.DepositRechargeItem;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.CreditRechargeContractService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("acceptBillClaimServiceImpl")
public class AcceptBillClaimServiceImpl extends
		WfBillBaseServiceImpl<DepositRecharge> implements
		AcceptBillClaimService {

	@Resource(name = "acceptBillClaimDao")
	private AcceptBillClaimDao acceptBillClaimDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "saleOrgBaseServiceImpl")
	private SaleOrgBaseService saleOrgService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "acceptBillServiceImpl")
	private AcceptBillService acceptBillService;
	@Resource(name = "creditRechargeContractServiceImpl")
	private CreditRechargeContractService creditRechargeContractService;

	@Override
	@Transactional
	public void save(Store store, DepositRecharge depositRecharge,
			Long rechargeTypeId, Long saleOrgId, Long paymentRechargeId,
			Long creditBillId, Long creditLineId) {

		BigDecimal totalAmount = BigDecimal.ZERO;
		List<DepositRechargeItem> depositRechargeItems = depositRecharge.getDepositRechargeItems();
		for (Iterator<DepositRechargeItem> iterator = depositRechargeItems.iterator(); iterator.hasNext();) {

			DepositRechargeItem depositRechargeItem = iterator.next();
			if (depositRechargeItem == null
					|| depositRechargeItem.getAcceptBill() == null
					|| depositRechargeItem.getAcceptBill().getId() == null) {
				iterator.remove();
				continue;
			}
			if (depositRechargeItem.getAmount() == null) {
				ExceptionUtil.throwServiceException("认领金额不能为空");
			}
			if (depositRechargeItem.getAmount().doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("认领金额不能小于0");
			}
			depositRechargeItem.setClaimType(depositRecharge.getClaimType());
			depositRechargeItem.setDepositRecharge(depositRecharge);

			totalAmount = totalAmount.add(depositRechargeItem.getAmount());
		}

		BigDecimal paymentBatchAmount = BigDecimal.ZERO;
		List<DepositPaymentBatch> depositPaymentBatchs = depositRecharge.getDepositPaymentBatchs();
		for (Iterator<DepositPaymentBatch> iterator = depositPaymentBatchs.iterator(); iterator.hasNext();) {

			DepositPaymentBatch depositPaymentBatch = iterator.next();
			if (depositPaymentBatch == null
					|| depositPaymentBatch.getContract_no() == null) {
				iterator.remove();
				continue;
			}
			paymentBatchAmount = paymentBatchAmount.add(depositPaymentBatch.getThisAmount());
			depositPaymentBatch.setDepositRecharge(depositRecharge);
		}
		if (totalAmount.compareTo(paymentBatchAmount) != 0) {
			ExceptionUtil.throwServiceException("资金流水本次认领金额必须等于付款方式本次认领金额之和");
		}
		depositRecharge.setDepositPaymentBatchs(depositPaymentBatchs);

		// 附件
		List<DepositAttach> depositAttachs = depositRecharge.getDepositAttachs();
		for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext();) {
			DepositAttach depositAttach = iterator.next();
			if (depositAttach == null || depositAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (depositAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			depositAttach.setFileName(depositAttach.getName()
					+ "."
					+ depositAttach.getSuffix());
			depositAttach.setDepositRecharge(depositRecharge);
			depositAttach.setStoreMember(storeMemberService.getCurrent());
		}
		depositRecharge.setDepositAttachs(depositAttachs);

		depositRecharge.setType(1);

		if (rechargeTypeId != null) {
			depositRecharge.setRechargeType(systemDictService.find(rechargeTypeId));
		}
		else {
			List<Filter> filters = new ArrayList<Filter>();
			filters.add(Filter.eq("code", "DepositRechargeType"));
			filters.add(Filter.eq("value", "客户充值"));
			filters.add(Filter.isNotNull("parent"));
			List<SystemDict> rechargeTypes = systemDictService.findList(1,
					filters,
					null);
			if (!rechargeTypes.isEmpty()) {
				depositRecharge.setRechargeType(rechargeTypes.get(0));
			}
		}
		depositRecharge.setStatus(Status.wait);
		depositRecharge.setDocStatus(0);
		depositRecharge.setStore(store);
		if (saleOrgId == null && store != null) {
			depositRecharge.setSaleOrg(store.getSaleOrg());
		}
		else {
			depositRecharge.setSaleOrg(saleOrgService.find(saleOrgId));
		}
		depositRecharge.setCreator(storeMemberService.getCurrent());
		depositRecharge.setStoreMember(storeMemberService.getCurrent());
		depositRecharge.setSn(SnUtil.generateSn());
		if (depositRecharge.getBankCard() == null
				|| depositRecharge.getBankCard().getId() == null) {
			depositRecharge.setBankCard(null);
		}
		depositRecharge.setAmount(totalAmount);
		depositRecharge.setActualAmount(totalAmount);
		if (depositRecharge.getAmountType().intValue() == 2) {
			depositRecharge.setPaymentRecharge(find(paymentRechargeId));
		}
		if (depositRecharge.getAmountType().intValue() == 3) {
			depositRecharge.setCreditBill(creditRechargeContractService.find(creditBillId));
		}
		if (depositRecharge.getAmountType().intValue() == 4) {
			depositRecharge.setCreditLine(creditRechargeContractService.find(creditLineId));
		}
		save(depositRecharge);

		orderFullLinkService.addFullLink(5,
				null,
				depositRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18700", new Object[] { "承兑汇票认领" }),
				null);
	}

	@Override
	@Transactional
	public void update(Store store, DepositRecharge depositRecharge,
			Long saleOrgId, Long paymentRechargeId, Long creditBillId,
			Long creditLineId) {

		DepositRecharge dr = find(depositRecharge.getId());
		dr.setStore(store);
		if (saleOrgId == null && store != null) {
			dr.setSaleOrg(store.getSaleOrg());
		}
		else {
			dr.setSaleOrg(saleOrgService.find(saleOrgId));
		}
		dr.setSaleOrg(saleOrgService.find(saleOrgId));
		dr.setImage(depositRecharge.getImage());
		if (depositRecharge.getBankCard() == null
				|| depositRecharge.getBankCard().getId() == null) {
			dr.setBankCard(null);
		}
		else {
			dr.setBankCard(depositRecharge.getBankCard());
		}
		dr.setMemo(depositRecharge.getMemo());

		BigDecimal totalAmount = BigDecimal.ZERO;
		List<DepositRechargeItem> depositRechargeItems = depositRecharge.getDepositRechargeItems();
		for (Iterator<DepositRechargeItem> iterator = depositRechargeItems.iterator(); iterator.hasNext();) {

			DepositRechargeItem depositRechargeItem = iterator.next();
			if (depositRechargeItem == null
					|| depositRechargeItem.getAcceptBill() == null
					|| depositRechargeItem.getAcceptBill().getId() == null) {
				iterator.remove();
				continue;
			}
			if (depositRechargeItem.getAmount() == null) {
				ExceptionUtil.throwServiceException("认领金额不能为空");
			}
			if (depositRechargeItem.getAmount().doubleValue() <= 0) {
				ExceptionUtil.throwServiceException("认领金额不能小于0");
			}
			depositRechargeItem.setClaimType(depositRecharge.getClaimType());
			depositRechargeItem.setDepositRecharge(dr);

			totalAmount = totalAmount.add(depositRechargeItem.getAmount());
		}
		dr.getDepositRechargeItems().clear();
		dr.getDepositRechargeItems().addAll(depositRechargeItems);

		dr.setAmount(totalAmount);
		dr.setActualAmount(totalAmount);

		BigDecimal paymentBatchAmount = BigDecimal.ZERO;
		List<DepositPaymentBatch> depositPaymentBatchs = depositRecharge.getDepositPaymentBatchs();
		for (Iterator<DepositPaymentBatch> iterator = depositPaymentBatchs.iterator(); iterator.hasNext();) {

			DepositPaymentBatch depositPaymentBatch = iterator.next();
			if (depositPaymentBatch == null
					|| depositPaymentBatch.getContract_no() == null) {
				iterator.remove();
				continue;
			}
			paymentBatchAmount = paymentBatchAmount.add(depositPaymentBatch.getThisAmount());
			depositPaymentBatch.setDepositRecharge(dr);
		}
		if (totalAmount.compareTo(paymentBatchAmount) != 0) {
			ExceptionUtil.throwServiceException("资金流水本次认领金额必须等于付款方式本次认领金额之和");
		}
		dr.getDepositPaymentBatchs().clear();
		dr.getDepositPaymentBatchs().addAll(depositPaymentBatchs);

		// 附件
		List<DepositAttach> depositAttachs = depositRecharge.getDepositAttachs();
		for (Iterator<DepositAttach> iterator = depositAttachs.iterator(); iterator.hasNext();) {
			DepositAttach depositAttach = iterator.next();
			if (depositAttach == null || depositAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (depositAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			depositAttach.setFileName(depositAttach.getName()
					+ "."
					+ depositAttach.getSuffix());
			depositAttach.setDepositRecharge(dr);
			depositAttach.setStoreMember(storeMemberService.getCurrent());
		}
		dr.getDepositAttachs().clear();
		dr.getDepositAttachs().addAll(depositAttachs);

		dr.setPaymentRecharge(find(paymentRechargeId));
		dr.setCreditBill(creditRechargeContractService.find(creditBillId));
		dr.setCreditLine(creditRechargeContractService.find(creditLineId));

		update(dr);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findItemListById(Long id) {
		return acceptBillClaimDao.findItemListById(id);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findAttachListById(Long id) {
		return acceptBillClaimDao.findAttachListById(id);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args) {
		return acceptBillClaimDao.findPage(pageable, args);
	}

	@Override
	@Transactional
	public void check(Long id) {

		DepositRecharge depositRecharge = find(id);
		depositRecharge.setCheckDate(new Date());
		depositRecharge.setOperator(storeMemberService.getCurrent());
		depositRecharge.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.关闭
		update(depositRecharge);

		for (DepositRechargeItem depositRechargeItem : depositRecharge.getDepositRechargeItems()) {
			AcceptBill acceptBill = depositRechargeItem.getAcceptBill();
			BigDecimal amount = depositRechargeItem.getAmount();

			BigDecimal claimedAmount = acceptBill.getClaimedAmount();
			if (claimedAmount == null) {
				claimedAmount = new BigDecimal(0);
			}
			BigDecimal pAmount = claimedAmount.add(amount);
			if (pAmount.compareTo(acceptBill.getAmount()) == 1) {
				ExceptionUtil.throwServiceException("可认领金额不足");
			}
			else {
				acceptBill.setClaimedAmount(pAmount);
			}
			acceptBillService.update(acceptBill);
		}

		orderFullLinkService.addFullLink(5,
				null,
				depositRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18702", new Object[] { "回款流水认领" }),
				null);

	}

	@Override
	@Transactional
	public void checkWf(Long id, Long objConfId) {

		DepositRecharge depositRecharge = find(id);
		// 创建流程实例
		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}
		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());

        ExceptionUtil.throwServiceException("创建流程的代码被注释，请联系管理员");

//		depositRecharge.setByObjConfig(wfObjConfigLine); // 设置流程配置
//		wfBaseService.createwf(storeMemberService.getCurrent(),
//				depositRecharge.getSaleOrg() == null ? null
//						: depositRecharge.getSaleOrg().getId(),
//				depositRecharge.getSn(),
//				depositRecharge.getWfTempId(),
//				depositRecharge.getObjTypeId(),
//				depositRecharge.getId());

		depositRecharge.setDocStatus(1); // 单据状态 0.已保存(没有流程) 1.已提交 2.已处理(流程走完) 3.关闭
		update(depositRecharge);
		orderFullLinkService.addFullLink(5,
				null,
				depositRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18701",
						new Object[] { wfTemp.getWfTempName() }),
				null);
	}

	@Override
	public void agreeBack(Wf wf) {

		if (wf.getStat().intValue() == 2) {
			DepositRecharge depositRecharge = find(wf.getObjId());
			if (depositRecharge.getType().intValue() == 1) {
				depositRecharge.setCheckDate(new Date());
				depositRecharge.setOperator(storeMemberService.getCurrent());
				depositRecharge.setStatus(DepositRecharge.Status.success); // 审核通过
				depositRecharge.setDocStatus(2); // 单据状态 0.已保存(没有流程) 1.处理中(有流程) 2.已处理(流程走完) 3.关闭
				update(depositRecharge);

				for (DepositRechargeItem depositRechargeItem : depositRecharge.getDepositRechargeItems()) {
					AcceptBill acceptBill = depositRechargeItem.getAcceptBill();
					BigDecimal amount = depositRechargeItem.getAmount();

					BigDecimal claimedAmount = acceptBill.getClaimedAmount();
					if (claimedAmount == null) {
						claimedAmount = new BigDecimal(0);
					}
					BigDecimal pAmount = claimedAmount.add(amount);
					if (pAmount.compareTo(acceptBill.getAmount()) == 1) {
						ExceptionUtil.throwServiceException("可认领金额不足");
					}
					else {
						acceptBill.setClaimedAmount(pAmount);
					}
					acceptBillService.update(acceptBill);
				}

			}
		}
	}

	@Override
	public void interruptBack(Wf wf) {
		DepositRecharge depositRecharge = find(wf.getObjId());
		depositRecharge.setCheckDate(null);
		depositRecharge.setDocStatus(0);
		update(depositRecharge);
		orderFullLinkService.addFullLink(5,
				null,
				depositRecharge.getSn(),
				ConvertUtil.convertI18nMsg("18704"),
				null);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPaymentBatchByDepositRechargeId(
			Long depositRechargeId) {

		StringBuilder sql = new StringBuilder();
		sql.append("select b1.*,ifnull(sum(b2.amount),0) payed_amount from xx_deposit_payment_batch b1");
		sql.append(" left join xx_deposit_payment_batch b2");
		sql.append(" on b1.contract_no=b2.contract_no and b1.id<>b2.id and b1.contract_payment_batch=b2.contract_payment_batch");
		sql.append(" where b1.deposit_recharge=? group by b1.id");
		return this.getDaoCenter()
				.getNativeDao()
				.findListMap(sql.toString(),
						new Object[] { depositRechargeId },
						0);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findAcceptBillPage(String sn,
			String storeName, Pageable pageable) {
		// TODO 自动生成的方法存根
		return acceptBillClaimDao.findAcceptBillPage(sn, storeName, pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Map<String, Object> findAcceptBill(Long id) {
		// TODO 自动生成的方法存根
		return acceptBillClaimDao.findAcceptBill(id);
	}
}
