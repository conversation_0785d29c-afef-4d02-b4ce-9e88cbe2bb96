package net.shopxx.finance.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.finance.dao.AmountAdjustBillDao;
import net.shopxx.finance.entity.AmountAdjustBill;
import net.shopxx.finance.entity.AmountAdjustBillBatch;
import net.shopxx.finance.entity.AmountAdjustBillItem;
import net.shopxx.finance.entity.ReturnBill;
import net.shopxx.finance.service.AmountAdjustBillService;
import net.shopxx.finance.service.ReturnBillService;
import net.shopxx.member.entity.DepositPaymentBatch;
import net.shopxx.member.entity.DepositRecharge;
import net.shopxx.member.entity.DepositRechargeItem;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.DepositPaymentBatchService;
import net.shopxx.member.service.DepositRechargeService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.entity.Contract;
import net.shopxx.order.entity.ContractPaymentBatch;
import net.shopxx.order.service.ContractService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("amountAdjustBillServiceImpl")
public class AmountAdjustBillServiceImpl extends
		WfBillBaseServiceImpl<AmountAdjustBill> implements
		AmountAdjustBillService {

	@Resource(name = "amountAdjustBillDao")
	private AmountAdjustBillDao amountAdjustBillDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "depositRechargeServiceImpl")
	private DepositRechargeService depositRechargeService;
	@Resource(name = "depositPaymentBatchServiceImpl")
	private DepositPaymentBatchService depositPaymentBatchService;
	@Resource(name = "contractServiceImpl")
	private ContractService contractService;
	@Resource(name = "returnBillServiceImpl")
	private ReturnBillService returnBillService;

	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;

	public void saveAmountAdjustBill(AmountAdjustBill amountAdjustBill,
			Long depositRechargeId) {

		Contract contract = contractService.find((amountAdjustBill.getContract() != null) ? amountAdjustBill.getContract()
				.getId()
				: null);
		if (contract == null) {
			ExceptionUtil.throwServiceException("请选择合同");
		}

		DepositRecharge depositRecharge = depositRechargeService.find(depositRechargeId);
		
		//原回款单和调账单的合同号不能相同
		if(contract.getContract_no().equals(depositRecharge.getContract_no())){
			ExceptionUtil.throwServiceException("调账到的合同号不能和原回款单的合同号相同");
		}
		
		
		BigDecimal totalAdjustAmount = BigDecimal.ZERO;
		BigDecimal claimAmountAmount = BigDecimal.ZERO;

		List<AmountAdjustBillItem> amountAdjustBillItems = amountAdjustBill.getAmountAdjustBillItems();
		for (Iterator<AmountAdjustBillItem> iterator = amountAdjustBillItems.iterator(); iterator.hasNext();) {

			AmountAdjustBillItem amountAdjustBillItem = iterator.next();
			BigDecimal adjustAmount = amountAdjustBillItem.getAdjustAmount();
			BigDecimal claimAmount = amountAdjustBillItem.getClaimAmount();
			if (adjustAmount == null) {
				ExceptionUtil.throwServiceException("调整金额不能为空");
			}
			if (adjustAmount.doubleValue() < 0) {
				ExceptionUtil.throwServiceException("调整金额不能小于0");
			}

			DepositPaymentBatch depositPaymentBatch = depositPaymentBatchService.find(amountAdjustBillItem.getDepositPaymentBatch()
					.getId());
			BigDecimal amount = depositPaymentBatch.getAmount();
			if (adjustAmount.compareTo(amount) == 1) {
				ExceptionUtil.throwServiceException("调整金额不允许大于原认领金额");
			}

			amountAdjustBillItem.setAmountAdjustBill(amountAdjustBill);

			totalAdjustAmount = totalAdjustAmount.add(adjustAmount);

		}

		List<AmountAdjustBillBatch> amountAdjustBillBatchs = amountAdjustBill.getAmountAdjustBillBatchs();
		for (Iterator<AmountAdjustBillBatch> iterator = amountAdjustBillBatchs.iterator(); iterator.hasNext();) {

			AmountAdjustBillBatch amountAdjustBillBatch = iterator.next();

			if (amountAdjustBillBatch == null
					|| amountAdjustBillBatch.getContract_no() == null) {
				iterator.remove();
				continue;
			}
			if (amountAdjustBillBatch.getThisAmount() == null) {
				amountAdjustBillBatch.setThisAmount(BigDecimal.ZERO);
			}

			claimAmountAmount = claimAmountAmount.add(amountAdjustBillBatch.getThisAmount());
			amountAdjustBillBatch.setAmountAdjustBill(amountAdjustBill);

		}

		if (totalAdjustAmount.compareTo(claimAmountAmount) == -1) {
			BigDecimal sAmount = totalAdjustAmount.subtract(claimAmountAmount);
			ExceptionUtil.throwServiceException("调整总金额："
					+ totalAdjustAmount
					+ ";认领总金额："
					+ claimAmountAmount
					+ ";差额："
					+ sAmount);
		}

		amountAdjustBill.setContract(contract);
		amountAdjustBill.setStoreMember(storeMemberBaseService.getCurrent());
		amountAdjustBill.setStatus(0);
		amountAdjustBill.setDepositRecharge(depositRecharge);
		amountAdjustBill.setTotalAdjustAmount(totalAdjustAmount);
		amountAdjustBill.setSn(SnUtil.generateSn());
		save(amountAdjustBill);
	}

	public void updateAmountAdjustBill(AmountAdjustBill amountAdjustBill,
			Long depositRechargeId) {

		AmountAdjustBill pAmountAdjustBill = this.find(amountAdjustBill.getId());
		if (pAmountAdjustBill.getStatus() != 0) {
			ExceptionUtil.throwServiceException("非未审核状态，不允许编辑");
		}

		Contract contract = contractService.find((amountAdjustBill.getContract() != null) ? amountAdjustBill.getContract()
				.getId()
				: null);
		if (contract == null) {
			ExceptionUtil.throwServiceException("请选择合同");
		}

		DepositRecharge depositRecharge = depositRechargeService.find(depositRechargeId);

		//原回款单和调账单的合同号不能相同
		if(contract.getContract_no().equals(depositRecharge.getContract_no())){
			ExceptionUtil.throwServiceException("调账到的合同号不能和原回款单的合同号相同");
		}
		
				
		BigDecimal totalAdjustAmount = BigDecimal.ZERO;
		BigDecimal claimAmountAmount = BigDecimal.ZERO;

		List<AmountAdjustBillItem> amountAdjustBillItems = amountAdjustBill.getAmountAdjustBillItems();
		for (Iterator<AmountAdjustBillItem> iterator = amountAdjustBillItems.iterator(); iterator.hasNext();) {

			AmountAdjustBillItem amountAdjustBillItem = iterator.next();
			BigDecimal adjustAmount = amountAdjustBillItem.getAdjustAmount();
			BigDecimal claimAmount = amountAdjustBillItem.getClaimAmount();
			if (adjustAmount == null) {
				ExceptionUtil.throwServiceException("调整金额不能为空");
			}
			if (adjustAmount.doubleValue() < 0) {
				ExceptionUtil.throwServiceException("调整金额不能小于0");
			}

			DepositPaymentBatch depositPaymentBatch = depositPaymentBatchService.find(amountAdjustBillItem.getDepositPaymentBatch()
					.getId());
			BigDecimal amount = depositPaymentBatch.getAmount();
			if (adjustAmount.compareTo(amount) == 1) {
				ExceptionUtil.throwServiceException("调整金额不允许大于原认领金额");
			}

			amountAdjustBillItem.setAmountAdjustBill(amountAdjustBill);

			totalAdjustAmount = totalAdjustAmount.add(adjustAmount);

		}

		List<AmountAdjustBillBatch> amountAdjustBillBatchs = amountAdjustBill.getAmountAdjustBillBatchs();
		for (Iterator<AmountAdjustBillBatch> iterator = amountAdjustBillBatchs.iterator(); iterator.hasNext();) {

			AmountAdjustBillBatch amountAdjustBillBatch = iterator.next();

			if (amountAdjustBillBatch == null
					|| amountAdjustBillBatch.getContract_no() == null) {
				iterator.remove();
				continue;
			}
			if (amountAdjustBillBatch.getThisAmount() == null) {
				amountAdjustBillBatch.setThisAmount(BigDecimal.ZERO);
			}

			claimAmountAmount = claimAmountAmount.add(amountAdjustBillBatch.getThisAmount());
			amountAdjustBillBatch.setAmountAdjustBill(amountAdjustBill);

		}

		if (totalAdjustAmount.compareTo(claimAmountAmount) == -1) {
			BigDecimal sAmount = totalAdjustAmount.subtract(claimAmountAmount);
			ExceptionUtil.throwServiceException("调整总金额："
					+ totalAdjustAmount
					+ ";认领总金额："
					+ claimAmountAmount
					+ ";差额："
					+ sAmount);
		}

		amountAdjustBill.setContract(contract);
		amountAdjustBill.setStatus(0);
		amountAdjustBill.setDepositRecharge(depositRecharge);
		amountAdjustBill.setTotalAdjustAmount(totalAdjustAmount);
		update(amountAdjustBill);
	}

	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn,
			String depositRechargeSn, Integer[] status, Pageable pageable) {
		return amountAdjustBillDao.findPage(sn,
				depositRechargeSn,
				status,
				pageable);
	}

	@Transactional(readOnly = true)
	public List<Map<String, Object>> findItemListById(Long id) {

		StringBuilder sql = new StringBuilder();
		sql.append("select b1.*,ifnull(sum(b2.amount),0) payed_amount,bi.adjust_amount,bi.id item_id from xx_deposit_payment_batch b1");
		sql.append(" left join xx_deposit_payment_batch b2");
		sql.append(" on b1.contract_no=b2.contract_no and b1.id<>b2.id and b1.contract_payment_batch=b2.contract_payment_batch");
		sql.append(" left join xx_amount_adjust_bill_item bi on bi.deposit_payment_batch=b1.id");
		sql.append(" where bi.amount_adjust_bill=? group by b1.id");
		return this.getDaoCenter()
				.getNativeDao()
				.findListMap(sql.toString(), new Object[] { id }, 0);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findPaymentBatchByMainId(Long id) {

		StringBuilder sql = new StringBuilder();
		sql.append("select b1.*,ifnull(sum(b2.amount),0) payed_amount from xx_amount_adjust_bill_batch b1");
		sql.append(" left join xx_amount_adjust_bill_batch b2");
		sql.append(" on b1.contract_no=b2.contract_no and b1.id<>b2.id and b1.contract_payment_batch=b2.contract_payment_batch");
		sql.append(" where b1.amount_adjust_bill=? group by b1.id");
		return this.getDaoCenter()
				.getNativeDao()
				.findListMap(sql.toString(), new Object[] { id }, 0);
	}

	public DepositRecharge createDepositRecharge(
			AmountAdjustBill amountAdjustBill) {

		DepositRecharge pDepositRecharge = amountAdjustBill.getDepositRecharge();
		DepositRecharge depositRecharge = new DepositRecharge();
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		BeanUtils.copyProperties(pDepositRecharge,
				depositRecharge,
				new String[] { "id",
						"createDate",
						"modifyDate",
						"objTypeId",
						"wfState",
						"wfId",
						"wfTempId",
						"sn",
						"depositAttachs",
						"depositRechargeItems",
						"depositPaymentBatchs" });

		//设置付款方式
		List<DepositPaymentBatch> depositPaymentBatchs = new ArrayList<DepositPaymentBatch>();
		for (AmountAdjustBillBatch amountAdjustBillBatch : amountAdjustBill.getAmountAdjustBillBatchs()) {
			ContractPaymentBatch contractPaymentBatch = amountAdjustBillBatch.getContractPaymentBatch();
			DepositPaymentBatch depositPaymentBatch = new DepositPaymentBatch();
			depositPaymentBatch.setName(amountAdjustBillBatch.getName());
			depositPaymentBatch.setRate(amountAdjustBillBatch.getRate());
			depositPaymentBatch.setMemo(amountAdjustBillBatch.getMemo());
			depositPaymentBatch.setContract_no(amountAdjustBillBatch.getContract_no());
			depositPaymentBatch.setSeq(amountAdjustBillBatch.getSeq());
			depositPaymentBatch.setIsDeposit(amountAdjustBillBatch.getIsDeposit());
			depositPaymentBatch.setAmount(amountAdjustBillBatch.getAmount());
			depositPaymentBatch.setThisAmount(amountAdjustBillBatch.getThisAmount());
			depositPaymentBatch.setContractPaymentBatch(contractPaymentBatch);

			depositPaymentBatch.setDepositRecharge(depositRecharge);
			depositPaymentBatchs.add(depositPaymentBatch);
		}
		depositRecharge.setDepositPaymentBatchs(depositPaymentBatchs);

		//设置付款明细
		List<DepositRechargeItem> depositRechargeItems = new ArrayList<DepositRechargeItem>();
		DepositRechargeItem depositRechargeItem = new DepositRechargeItem();
		depositRechargeItem.setDepositRecharge(depositRecharge);
		depositRechargeItem.setReturnBill(amountAdjustBill.getDepositRecharge()
				.getDepositRechargeItems()
				.get(0)
				.getReturnBill());
		depositRechargeItem.setAmount(amountAdjustBill.getTotalAdjustAmount());
		depositRechargeItem.setClaimType(1);
		depositRechargeItem.setPurpose(amountAdjustBill.getItemPurpose());
		depositRechargeItem.setNote(amountAdjustBill.getItemMemo());
		depositRechargeItems.add(depositRechargeItem);
		depositRecharge.setDepositRechargeItems(depositRechargeItems);

		//设置主表
		depositRecharge.setStatus(DepositRecharge.Status.success);
		depositRecharge.setDocStatus(2);
		depositRecharge.setAmount(amountAdjustBill.getTotalAdjustAmount());
		depositRecharge.setActualAmount(amountAdjustBill.getTotalAdjustAmount());
		depositRecharge.setStoreMember(storeMember);
		depositRecharge.setCreator(storeMember);
		depositRecharge.setOperator(storeMember);
		depositRecharge.setCheckDate(new Date());
		depositRecharge.setMemo(amountAdjustBill.getMemo());
		depositRecharge.setCreateBy(storeMember);
		depositRecharge.setSubmitDate(new Date());
		depositRecharge.setAmountType(amountAdjustBill.getAmountType());

		Contract contract = amountAdjustBill.getContract();
		depositRecharge.setContract_no(contract.getContract_no());
		depositRecharge.setContract_amt(contract.getContract_amt());
		depositRecharge.setSn(SnUtil.generateSn());
		depositRechargeService.save(depositRecharge);
		return depositRecharge;

	}

	@Transactional
	public void check(AmountAdjustBill amountAdjustBill, Long depositRechargeId) {
		this.updateAmountAdjustBill(amountAdjustBill, depositRechargeId);

		AmountAdjustBill pAmountAdjustBill = this.find(amountAdjustBill.getId());
		DepositRecharge depositRecharge = createDepositRecharge(pAmountAdjustBill);
		pAmountAdjustBill.setDepositRechargeSn(depositRecharge.getSn());
		pAmountAdjustBill.setStatus(1);
		pAmountAdjustBill.setCheckDate(new Date());
		pAmountAdjustBill.setCheckStoreMember(storeMemberBaseService.getCurrent());
		this.update(pAmountAdjustBill);
		
		//返还金额
		DepositRecharge oDepositRecharge = pAmountAdjustBill.getDepositRecharge();
		BigDecimal backAmount = oDepositRecharge.getBackAmount();
		if(backAmount==null){
			backAmount = BigDecimal.ZERO;
		}
		backAmount = backAmount.add(pAmountAdjustBill.getTotalAdjustAmount());
		oDepositRecharge.setBackAmount(backAmount);
		depositRechargeService.update(oDepositRecharge);

		for (DepositRechargeItem depositRechargeItem : depositRecharge.getDepositRechargeItems()) {
			ReturnBill returnBill = depositRechargeItem.getReturnBill();
			BigDecimal amount = depositRechargeItem.getAmount();
			BigDecimal pAmount = returnBill.getClaimedAmount().add(amount);
			if (pAmount.compareTo(returnBill.getAmount()) == 1) {
				ExceptionUtil.throwServiceException("可认领金额不足");
			}
			else {
				returnBill.setClaimedAmount(pAmount);
			}
			returnBillService.update(returnBill);
		}

//		orderFullLinkService.addFullLink(5,
//				null,
//				depositRecharge.getSn(),
//				ConvertUtil.convertI18nMsg("18702", new Object[] { "回款流水认领" }),
//				null);

	}

	@Transactional
	public void checkWf(AmountAdjustBill amountAdjustBill,
			Long depositRechargeId, Long objConfId) {

		this.updateAmountAdjustBill(amountAdjustBill, depositRechargeId);

		AmountAdjustBill pAmountAdjustBill = find(amountAdjustBill.getId());

		SaleOrg saleOrg = pAmountAdjustBill.getDepositRecharge().getSaleOrg();

		// 创建流程实例
		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}
		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());
		pAmountAdjustBill.setByObjConfig(wfObjConfigLine); // 设置流程配置
		wfBaseService.createwf(storeMemberBaseService.getCurrent(),
				saleOrg == null ? null : saleOrg.getId(),
				pAmountAdjustBill.getSn(),
				pAmountAdjustBill.getWfTempId(),
				pAmountAdjustBill.getObjTypeId(),
				pAmountAdjustBill.getId());

		pAmountAdjustBill.setCheckDate(new Date());
		pAmountAdjustBill.setCheckStoreMember(storeMemberBaseService.getCurrent());
		this.update(pAmountAdjustBill);
//		orderFullLinkService.addFullLink(5,
//				null,
//				pAmountAdjustBill.getSn(),
//				ConvertUtil.convertI18nMsg("18701",
//						new Object[] { wfTemp.getWfTempName() }),
//				null);
	}

	public void agreeBack(Wf wf) {

		if (wf.getStat().intValue() == 2) {
			AmountAdjustBill pAmountAdjustBill = find(wf.getObjId());
			DepositRecharge depositRecharge = createDepositRecharge(pAmountAdjustBill);
			pAmountAdjustBill.setDepositRechargeSn(depositRecharge.getSn());
			pAmountAdjustBill.setStatus(1);
			this.update(pAmountAdjustBill);
			
			DepositRecharge oDepositRecharge = pAmountAdjustBill.getDepositRecharge();
			BigDecimal backAmount = oDepositRecharge.getBackAmount();
			if(backAmount==null){
				backAmount = BigDecimal.ZERO;
			}
			backAmount = backAmount.add(pAmountAdjustBill.getTotalAdjustAmount());
			oDepositRecharge.setBackAmount(backAmount);
			depositRechargeService.update(oDepositRecharge);

			for (DepositRechargeItem depositRechargeItem : depositRecharge.getDepositRechargeItems()) {
				ReturnBill returnBill = depositRechargeItem.getReturnBill();
				BigDecimal amount = depositRechargeItem.getAmount();
				BigDecimal pAmount = returnBill.getClaimedAmount().add(amount);
				if (pAmount.compareTo(returnBill.getAmount()) == 1) {
					ExceptionUtil.throwServiceException("可认领金额不足");
				}
				else {
					returnBill.setClaimedAmount(pAmount);
				}
				returnBillService.update(returnBill);
			}
		}
	}

}
