/*
 * Copyright 2005-2013 shopxx.net. All rights reserved. Support:
 * http://www.shopxx.net License: http://www.shopxx.net/license
 */
package net.shopxx.finance.service.impl;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.finance.dao.DepositDao;
import net.shopxx.finance.entity.Deposit;
import net.shopxx.finance.service.DepositService;

/**
 * Service - 预存款
 * 
 * <AUTHOR> Team
 * @version 3.0
 */
@Service("depositServiceImpl")
public class DepositServiceImpl extends BaseServiceImpl<Deposit>
		implements DepositService {

	@Resource(name = "depositDao")
	private DepositDao depositDao;

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn, String tradeNo,
			Long orderId, String orderSn, Long memberId, Integer[] type,
			String firstTime, String lastTime, Pageable pageable) {
		return depositDao.findItemPage(sn,
				tradeNo,
				orderId,
				orderSn,
				memberId,
				type,
				firstTime,
				lastTime,
				pageable);
	}

}