package net.shopxx.finance.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aspose.cells.WorkbookDesigner;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.finance.controller.ExternalUseStatementController;
import net.shopxx.finance.dao.BalanceReportDao;
import net.shopxx.finance.dao.StoreBalanceDao;
import net.shopxx.finance.dao.StoreDepositDao;
import net.shopxx.finance.entity.StoreDeposit;
import net.shopxx.finance.service.StoreDepositService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.VisualReportService;
import net.shopxx.util.HashMapDataTable;
import net.shopxx.util.ResponseEx;

/**
 * Service - 预存款
 */
@Service("storeDepositServiceImpl")
public class StoreDepositServiceImpl extends BaseServiceImpl<StoreDeposit> implements StoreDepositService {
	
	private static Logger logger = LoggerFactory.getLogger(StoreDepositServiceImpl.class);

	@Resource(name = "storeDepositDao")
	private StoreDepositDao storeDepositDao;
	@Resource(name = "storeBalanceDao")
	private StoreBalanceDao storeBalanceDao;
	@Resource(name = "balanceReportDao")
	private BalanceReportDao balanceReportDao;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "visualReportServiceImpl")
	private VisualReportService visualReportService;
	

	/**
	 * 客户资金明细列表数据
	 */
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn, String tradeNo, Long orderId, String orderSn, Long storeId,
			Integer[] type, String firstTime, String lastTime, Pageable pageable) {
		return storeDepositDao.findItemPage(sn, tradeNo, orderId, orderSn, storeId, type, firstTime, lastTime,
				pageable);

	}

	@Override
	public Page<Map<String, Object>> findReport(String sn, Integer[] type, Long[] storeId, String firstTime,
			String lastTime, String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable) {
		return storeBalanceDao.findReport(sn, type, storeId, firstTime, lastTime, organizationName, saleOrgId, sbuId,
				pageable);
	}

	@Override
	public Integer countReports(String sn, Integer[] type, Long[] storeId, String firstTime, String lastTime,
			String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable, Integer page, Integer size) {
		return storeBalanceDao.countReports(sn, type, storeId, firstTime, lastTime, organizationName, saleOrgId, sbuId,
				pageable, page, size);
	}

	@Override
	public List<Map<String, Object>> findReportList(String sn, Integer[] type, Long[] storeId, String firstTime,
			String lastTime, String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable, Integer page,
			Integer size) {
		return storeBalanceDao.findReportList(sn, type, storeId, firstTime, lastTime, organizationName, saleOrgId,
				sbuId, pageable, page, size);
	}

	@Override
	public List<Map<String, Object>> findOrderItemListById(String ids) {
		return storeBalanceDao.findOrderItemListById(ids);
	}

	@Override
	public List<Map<String, Object>> findReturnItemListById(String ids) {
		return storeBalanceDao.findReturnItemListById(ids);
	}

	@Override
	public Page<Map<String, Object>> findPageCurrentAccount(String sn, String storeName, 
			String organizationName, String sbuName, String firstTime, String lastTime, Object[] type,
			Pageable pageable) {
		return storeBalanceDao.findPageCurrentAccount(sn, storeName,  organizationName, sbuName, firstTime,
				lastTime, type, pageable);
	}
	
	@Override
	public List<Map<String, Object>> findExportTable(String sn, String storeName, 
			String organizationName, String sbuName, String firstTime, String lastTime, Object[] type) {
		return storeBalanceDao.findExportTable(sn, storeName,  organizationName, sbuName, firstTime,
				lastTime, type);
	}

	@Override
	public Page<Map<String, Object>> findPage(Long saleOrgId, Long storeId,
			String storeCode, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime, Pageable pageable) {
//		Object[] st = findStore(storeId,saleOrgId,true);
//		Object[] saleOrg = findSaleOrg(saleOrgId,storeId,true);
		return balanceReportDao.findPage(saleOrgId, storeId, storeCode, sbuId, organizationId, firstTime, lastTime, pageable);
	}

	
	@Override
	public List<Map<String, Object>> findTableNew(Long saleOrgId, Long storeId, String storeCode, Long[] sbuId,
			Long[] organizationId, String firstTime, String lastTime) {
		// TODO Auto-generated method stub
		 return balanceReportDao.findTableNew(saleOrgId, storeId, storeCode, sbuId, organizationId, firstTime, lastTime);
	}

	@Override
	public List<Map<String, Object>> findTable(Long saleOrgId, Long storeId,
			String storeCode, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime,String[] ids) {
		Object[] st = findStore(storeId,saleOrgId,true);
		Object[] saleOrg = findSaleOrg(saleOrgId,storeId,true);
		return balanceReportDao.findTable(saleOrg, st, storeCode, sbuId, organizationId, firstTime, lastTime,ids);
	}

	@Override
	public Page<Map<String, Object>> findPageCustomerStatement(Long store, Long[] sbu,
			Long[] organization,String[] type,String firstTime, String lastTime,Long saleOrgId, 
			Pageable pageable,Long typeId) {
//		Object[] st = findStore(store,null,true);
		return balanceReportDao.findPageCustomerStatement(store,
				sbu,
				organization,
				firstTime,
				lastTime,
				type,
				saleOrgId,
				pageable,
				typeId);
	}

	@Override
	public List<Map<String, Object>> findTableCustomerStatement(Long store, Long[] sbu,
			Long[] organization,String firstTime, String lastTime,String[] type,Long saleOrgId,
			Long[] ids,Long typeId) {
//		Object[] st = findStore(store,null,true);
		return balanceReportDao.findTableCustomerStatement(store,
				sbu,
				organization,
				firstTime,
				lastTime,
				type,
				saleOrgId,
				null,
				typeId);
	}
			

	@Override
	public Page<Map<String, Object>> findSalesReportData(Long store, Long[] sbu, Long[] organizationId,
			String firstTime, String lastTime, String[] type, Long saleOrgId, Pageable pageable, Long typeId,
			String productCode, String productName, String billCode) {
		return balanceReportDao.findSalesReportData(store, sbu, organizationId, firstTime, lastTime, type, saleOrgId, pageable, typeId, productCode, productName, billCode);
				
	}
	
		
	@Override
	public Integer findSalesReportCount(Long store, Long[] sbu, Long[] organizationId, String firstTime,
			String lastTime, String[] type, Long saleOrgId, Pageable pageable, Long typeId, String productCode,
			String productName, String billCode) {		
		return balanceReportDao.findSalesReportCount(store, sbu, organizationId, firstTime, lastTime, type, saleOrgId, pageable, typeId, productCode, productName, billCode);
	}

	@Override
	public Page<Map<String, Object>> findPageDifference(Long store, Long sbu,
			Long organization,Long saleOrgId, String firstTime, String lastTime,
			Pageable pageable) {
//		Object[] st = findStore(store,saleOrgId,true);
//		Object[] saleOrg = findSaleOrg(saleOrgId,store,true);
		return balanceReportDao.findPageDifference(store, sbu, organization,saleOrgId, firstTime, lastTime, pageable);
	}

	@Override
	public List<Map<String, Object>> findTableDifference(Long store,
			Long sbu, Long organization,Long saleOrgId, String firstTime, String lastTime,
			Long[] ids) {
//		Object[] st = findStore(store,saleOrgId,true);
//		Object[] saleOrg = findSaleOrg(saleOrgId,store,true);
		return balanceReportDao.findTableDifference(store, sbu, organization,saleOrgId, firstTime, lastTime, ids);
	}
	
	//权限校验客户
	public Object[] findStore(Long store,Long saleOrg,Boolean isEnable){//isEnable=false不启用过滤,isEnable=true启用过滤
		List<Long> storeList = new ArrayList<Long>();
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		if(isEnable){
				Long member = storeMember.getMember().getId();
				List<Map<String, Object>>  storeLists = storeBaseService.findListByMember(member);
				for(Map<String,Object> s :storeLists){
					if(s!=null){
						Long d = s.get("id")==null?null:Long.parseLong(s.get("id").toString());
						if(d!=null){
							if(store==null){
								storeList.add(d);								
							}
							if(store!=null){
								//如果当前登录用户查询的客户没有在当前用户列表维护则查不出数据
								if(d.equals(store)){
									storeList.add(d);	
								}else if(saleOrg==null){
									return null;
								}
							}
							
						}
					}
				}
			
		}else if(store!=null){
			storeList.add(store);
		}
		return storeList.toArray();
	}
	
	//权限校验机构
	public Object[] findSaleOrg(Long saleOrg,Long store,Boolean isEnable){//isEnable=false不启用过滤,isEnable=true启用过滤
		List<Long> storeList = new ArrayList<Long>();
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		List<Long> saleOrgList = visualReportService.findSaleOrg(storeMember);
//		System.out.println("客户"+saleOrgList.toString());
//		System.out.println("查询客户"+saleOrg);
		if(isEnable){
			if(saleOrg==null){
				return saleOrgList.toArray();
			}
			for(Long s : saleOrgList){
				//如果当前登录用户查询的机构没有在当前用户列表维护则查不出数据
				if(s.equals(saleOrg)){
					storeList.add(saleOrg);
				}else if(store==null){
					return null;
				}
			}			
		}else if(saleOrg!=null){
			storeList.add(saleOrg);
		}
		return storeList.toArray();
	}

	@SuppressWarnings({ "finally", "rawtypes", "deprecation" })     //去掉黄色警告线
	@Override
	public Map<String, Object>  exportCustomerStatement(Long store, Long[] sbu,
			Long[] organization,String firstTime, String lastTime,String[] type,Long saleOrgId,
			int excelType,Long typeId) {
		//获取数据信息
		List<Map<String, Object>>  listDatas= new ArrayList<Map<String,Object>>();
		Map<String, Object> title = new HashMap<String, Object>();
		try {							
			//用户信息
			Map<String,Object> storeInfo = balanceReportDao.findStoreInfo(store);					
			//获取余额报表信息
			List<Map<String, Object>> reportData = balanceReportDao.findTableNew(saleOrgId, store, null, sbu, organization, firstTime, lastTime);
			if (reportData != null && reportData.size() > 0) {								
				for(Map<String, Object> reportDataItem : reportData) {					
					//初期参数
					Object itemOrganization = reportDataItem.get("organization");
					String sbuName = String.valueOf(reportDataItem.get("sbu"));					
					String initialStart_date = firstTime;
					String initialType = "上期余额";					
					String initialBatchStatus = String.valueOf(reportDataItem.get("qcys"));      //期初应收
					dataProcessing(listDatas, itemOrganization, initialStart_date, initialType, initialBatchStatus,sbuName,null,null,null);
					//遍历明细
					//处理参数
					Long itemStore = reportDataItem.get("storeId")==null?null:Long.valueOf(String.valueOf(reportDataItem.get("storeId")));
					Long[] itemSBUID = null;
					if (reportDataItem.get("SBUID") != null) {   //sbu不为空并且导的小报表
						itemSBUID = new Long[1];
						itemSBUID[0] = Long.valueOf(String.valueOf(reportDataItem.get("SBUID")));
					}
					Long[] itemOrganizationId = null;
					if (reportDataItem.get("organizationId") != null) {  //经营组织不为空并且导的小报表
						itemOrganizationId = new Long[1];
						itemOrganizationId[0] = Long.valueOf(String.valueOf(reportDataItem.get("organizationId")));
					}
					List<Map<String, Object>> listDataDetails = balanceReportDao.findTableCustomerStatement(itemStore,
							itemSBUID,
							itemOrganizationId,
							firstTime,
							lastTime,
							type,
							null,
							null,
							typeId);
					//最新应收余额
					BigDecimal newbatchStatus = new BigDecimal(initialBatchStatus);
					//遍历数据处理
					for(Map<String, Object> listDataDetail : listDataDetails) {
						newbatchStatus = dataLoad(listDatas,listDataDetail,newbatchStatus);
					}
					//末期参数					
					String endStart_date =  lastTime;
					String endType = "本月合计";					
					String endStatus = String.valueOf(reportDataItem.get("qmys"));   //期末应收
					dataProcessing(listDatas, itemOrganization, endStart_date, endType, endStatus,sbuName,null,null,null);
				}												
			}else {
				dataLoad(listDatas,null,null);
			}						
			//表头赋值
			title.put("userCode", storeInfo.get("store_code")==null?"":storeInfo.get("store_code"));
			title.put("userName", storeInfo.get("store")==null?"":storeInfo.get("store"));
            title.put("organization", storeInfo.get("organization")==null?"":storeInfo.get("organization"));
			title.put("saleOrg", storeInfo.get("organization")==null?"":storeInfo.get("saleOrg"));
			title.put("sbu", storeInfo.get("sbu")==null?"":storeInfo.get("sbu"));
			title.put("startTime",firstTime);
			title.put("endTime",lastTime);
		}catch (Exception e) {
			logger.error("对账单处理数据异常：",e);
			title.put("userCode", "");
			title.put("userName", "");
			title.put("startTime","");
			title.put("endTime","");
			dataLoad(listDatas,null,null);
			
		}finally {			
//			designer.setDataSource("title", new HashMapDataTable(title));
//			designer.setDataSource("bill", new HashMapDataTable(listDatas));
			title.put("maplist",listDatas);
			return title;
		}
	}
	
	//处理加载数据
	BigDecimal dataLoad(List<Map<String, Object>>  listDatas,Map<String,Object> paramData,BigDecimal newbatchStatus) {
		if(paramData != null) {
			//日期转换
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Map<String,Object> data = new HashMap<String, Object>();
			data.put("organization", paramData.get("organization")==null?"":paramData.get("organization"));        //经营组织			
			data.put("start_date", paramData.get("start_date")==null?"":sdf.format(paramData.get("start_date")));        //日期
			data.put("type", paramData.get("type")==null?"":paramData.get("type"));         //分类
			data.put("sn", paramData.get("sn")==null?"":paramData.get("sn"));           //票据编号
			data.put("sbu_name", paramData.get("sbu_name")==null?"":paramData.get("sbu_name"));         //sbu
			data.put("warehouse_name", paramData.get("warehouse_name")==null?"":paramData.get("warehouse_name"));    //仓库
			data.put("product_code", paramData.get("product_code")==null?"":paramData.get("product_code"));      //产品编码
			data.put("product_name", paramData.get("product_name")==null?"":paramData.get("product_name"));       //产品名称
			data.put("wood_type_or_color", paramData.get("wood_type_or_color")==null?"":paramData.get("wood_type_or_color"));    //木种花色
			data.put("model", paramData.get("model")==null?"":paramData.get("model"));         //型号
			data.put("spec", paramData.get("spec")==null?"":paramData.get("spec"));       //规格
//			Integer  shipped_branch = null;
//			if(paramData.get("shipped_branch")!=null) {				
//				  shipped_branch = Integer.valueOf(String.valueOf(paramData.get("shipped_branch")));
//			}			
			data.put("shipped_branch", paramData.get("shipped_branch")==null?"":paramData.get("shipped_branch"));    //发货支数    
			data.put("shipping_quantity", paramData.get("shipping_quantity")==null?"":paramData.get("shipping_quantity"));   //数量
			data.put("unit_price", paramData.get("unit_price")==null?"":paramData.get("unit_price"));    //单价
			data.put("shipping_money", paramData.get("shipping_money")==null?"":paramData.get("shipping_money"));   //发货金额
			data.put("order_sn", paramData.get("order_sn")==null?"":paramData.get("order_sn"));   //来源单号
			data.put("transfer_no", paramData.get("transfer_no")==null?"":paramData.get("transfer_no"));   //调款单号
			data.put("unit_no", paramData.get("unit_no")==null?"":paramData.get("unit_no"));    //单位号
			data.put("platform_no", paramData.get("platform_no")==null?"":paramData.get("platform_no"));   //平台号
			data.put("policy_type", paramData.get("policy_type")==null?"":paramData.get("policy_type"));    //政策类型
			data.put("policy_money", paramData.get("batchStatus")==null?"":paramData.get("batchStatus"));   //政策回款
			// 应收余额=上一次的应收余额-当前行的发货金额+单位号+平台号+政策回款
			if (newbatchStatus == null) {
				data.put("batchStatus", 0);    //应收余额
			}else {
				newbatchStatus = newbatchStatus.add(new BigDecimal(String.valueOf(paramData.get("shipping_money")))).subtract(new BigDecimal(String.valueOf(paramData.get("unit_no")))).subtract(new BigDecimal(String.valueOf(paramData.get("platform_no")))).subtract(new BigDecimal(String.valueOf(paramData.get("batchStatus"))));
				data.put("batchStatus", newbatchStatus);    //应收余额
			}
			data.put("memo",paramData.get("memo")==null?"":paramData.get("memo"));   //备注

			//列表专用
			data.put("storeName", paramData.get("store_name")==null?"":paramData.get("store_name"));    //客户名称
			data.put("storeCode", paramData.get("store_code")==null?"":paramData.get("store_code"));   //客户编码
			data.put("sale_org_name", paramData.get("sale_org_name")==null?"":paramData.get("sale_org_name"));   //机构
			data.put("storeId", null);    //客户id
			listDatas.add(data);
		}else {
			Map<String,Object> data = new HashMap<String, Object>();
			data.put("organization", "");        //经营组织
			data.put("start_date", "");        //日期
			data.put("type", "");         //分类
			data.put("sn", "");           //票据编号
			data.put("sbu_name", "");         //sbu
			data.put("warehouse_name", "");    //仓库
			data.put("product_code", "");      //产品编码
			data.put("product_name", "");       //产品名称
			data.put("wood_type_or_color", "");    //木种花色
			data.put("model", "");         //型号
			data.put("spec", "");       //规格
			data.put("shipped_branch", "");    //发货字数    
			data.put("shipping_quantity", "");   //数量
			data.put("unit_price", "");    //单价
			data.put("shipping_money", "");   //发货金额
			data.put("order_sn", "");   //来源单号
			data.put("transfer_no", "");   //调款单号
			data.put("unit_no", "");    //单位号
			data.put("platform_no", "");   //平台号
			data.put("policy_type", "");    //政策类型
			data.put("policy_money", "");   //政策回款
			data.put("batchStatus", "");    //应收余额
			data.put("storeName", "");    //客户名称
			data.put("storeCode", "");   //客户编码
			data.put("sale_org_name", "");   //机构
			data.put("memo","");   //备注
			listDatas.add(data);
		}
		return newbatchStatus;
	}
	
	
	//初期 末期数据处理
	void dataProcessing(List<Map<String, Object>>  listDatas,Object organization,String startDate,String type,String batchStatus,String sbuName,String storeName,String storeCode,String storeId) {
		//获取对指定的对账单数据
		Map<String, Object> listData = new HashMap<String, Object>();
		listData.put("organization", organization);        //经营组织
		listData.put("start_date", startDate);        //日期
		listData.put("type", type);         //分类
		listData.put("sn", "");           //票据编号
		listData.put("sbu_name", sbuName);         //sbu
		listData.put("warehouse_name", "");    //仓库
		listData.put("product_code", "");      //产品编码
		listData.put("product_name", "");       //产品名称
		listData.put("wood_type_or_color", "");    //木种花色
		listData.put("model", "");         //型号
		listData.put("spec", "");       //规格
		listData.put("shipped_branch", "");    //发货字数    
		listData.put("shipping_quantity", "");   //数量
		listData.put("unit_price", "");    //单价
		listData.put("shipping_money", "");   //发货金额
		listData.put("order_sn", "");   //来源单号
		listData.put("transfer_no", "");   //调款单号
		listData.put("unit_no", "");    //单位号
		listData.put("platform_no", "");   //平台号
		listData.put("policy_type", "");    //政策类型
		listData.put("policy_money", "");   //政策回款
		listData.put("batchStatus", batchStatus);    //应收余额
		
		listData.put("storeName", storeName);    //客户名称
		listData.put("storeCode", storeCode);   //客户编码
		listData.put("storeId", storeId);    //客户id
		listDatas.add(listData);
	}

	@SuppressWarnings("finally")
	@Override
	public List<Map<String, Object>> customerStatementList(Long store, Long[] sbu,
			Long[] organization,String firstTime, String lastTime,String[] type,Long saleOrgId,
			Long typeId){
			//获取数据信息
			List<Map<String, Object>>  listDatas= new ArrayList<Map<String,Object>>();			
			try {							
				//获取余额报表信息
				List<Map<String, Object>> reportData = balanceReportDao.findTableNew(saleOrgId, store, null, sbu, organization, firstTime, lastTime);
				if (reportData != null && reportData.size() > 0) {								
					for(Map<String, Object> reportDataItem : reportData) {					
						//初期参数
						Object itemOrganization = reportDataItem.get("organization");
						String sbuName = String.valueOf(reportDataItem.get("sbu"));
						String initialStart_date = firstTime;
						String initialType = "上期余额";					
						String storeName = String.valueOf(reportDataItem.get("store"));      //客户名称
						String storeCode = String.valueOf(reportDataItem.get("store_code"));      //客户编码
						String storeId = String.valueOf(reportDataItem.get("storeId"));      //客户编码
						String initialBatchStatus = String.valueOf(reportDataItem.get("qcys"));      //期初应收
						
						dataProcessing(listDatas, itemOrganization, initialStart_date, initialType, initialBatchStatus,sbuName,storeName,storeCode,storeId);
						//遍历明细
						//处理参数
						Long itemStore = reportDataItem.get("storeId")==null?null:Long.valueOf(String.valueOf(reportDataItem.get("storeId")));
						Long[] itemSBUID = null;
						if (reportDataItem.get("SBUID") != null) {   //sbu不为空并且导的小报表
							itemSBUID = new Long[1];
							itemSBUID[0] = Long.valueOf(String.valueOf(reportDataItem.get("SBUID")));
						}
						Long[] itemOrganizationId = null;
						if (reportDataItem.get("organizationId") != null) {  //经营组织不为空并且导的小报表
							itemOrganizationId = new Long[1];
							itemOrganizationId[0] = Long.valueOf(String.valueOf(reportDataItem.get("organizationId")));
						}
						List<Map<String, Object>> listDataDetails = balanceReportDao.findTableCustomerStatement(itemStore,
								itemSBUID,
								itemOrganizationId,
								firstTime,
								lastTime,
								type,
								null,
								null,
								typeId);
//						//最新应收余额
//						BigDecimal newbatchStatus = new BigDecimal(initialBatchStatus);
//						//遍历数据处理
//						for(Map<String, Object> listDataDetail : listDataDetails) {
//							dataLoad(listDatas,listDataDetail,newbatchStatus);
//						}
						//末期参数					
						String endStart_date = lastTime;
						String endType = "本月合计";					
						String endStatus = String.valueOf(reportDataItem.get("qmys"));   //期末应收
						dataProcessing(listDatas, itemOrganization, endStart_date, endType, endStatus,sbuName,storeName,storeCode,storeId);
					}												
				}else {
					dataLoad(listDatas,null,null);
				}																	
			}catch (Exception e) {			
				logger.error("对账单处理数据异常：",e);
				
			}finally {							
				return listDatas;
			}
		
	}

	@Override
	public Map<String, Object> findStoreInfo(Long storeId) {
		
		return balanceReportDao.findStoreInfo(storeId);
	}

	@SuppressWarnings("finally")
	@Override
	public ResponseEx newcCondition_export(Long storeId, String[] type, Long[] sbuId, Long[] organizationId,
			String firstTime, String lastTime, Long saleOrgId, Long typeId, Integer page, Pageable pageable,
			WorkbookDesigner designer) {
		List<Map<String, Object>>  listDatas= new ArrayList<Map<String,Object>>();
		try {
			//获取数据信息
			Page<Map<String, Object>> pageData = balanceReportDao.findPageCustomerStatement(storeId, sbuId, organizationId, firstTime, lastTime, type, saleOrgId, pageable, typeId);
			List<Map<String, Object>> datas = pageData.getContent();
			if (datas != null && datas.size() > 0 ) {
				for(Map<String, Object> listDataDetail : datas) {
					dataLoad(listDatas,listDataDetail,null);
				}
			}else {
				dataLoad(listDatas,null,null);
			}			
		}catch (Exception e) {
			logger.error("按条件导出客户对账单异常：",e);
			dataLoad(listDatas,null,null);
		}finally {	
			designer.setDataSource("bill", new HashMapDataTable(listDatas));
			return ResponseEx.createSuccess("数据处理完成！");
		}
	}


    @Override
    public List<Map<String, Object>> getcustomerStatementListToEaeyPoi(Long storeId, String[] type, Long[] sbuId, Long[] organizationId, String firstTime, String lastTime, Long saleOrgId, Long typeId, Integer page, Pageable pageable) {
        List<Map<String, Object>>  listDatas= new ArrayList<Map<String,Object>>();
        try {
            //获取数据信息
            Page<Map<String, Object>> pageData = balanceReportDao.findPageCustomerStatement(storeId, sbuId, organizationId, firstTime, lastTime, type, saleOrgId, pageable, typeId);
            List<Map<String, Object>> datas = pageData.getContent();
            if (datas != null && datas.size() > 0 ) {
                for(Map<String, Object> listDataDetail : datas) {
                    dataLoad(listDatas,listDataDetail,null);
                }
            }else {
                dataLoad(listDatas,null,null);
            }
        }catch (Exception e) {
            dataLoad(listDatas,null,null);
        }finally {
            return listDatas;
        }
    }
}