package net.shopxx.finance.service.impl;

import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.finance.dao.AccountStatementDao;
import net.shopxx.finance.service.AccountStatementService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("accountStatementServiceImpl")
public class AccountStatementServiceImpl extends BaseServiceImpl<Object>
		implements AccountStatementService {
	
	@Resource(name = "accountStatementDao")
	private AccountStatementDao accountStatementDao;

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String month, Long storeId,
			Long saleOrgId, Pageable pageable) {

		return accountStatementDao.findPage(month, storeId, saleOrgId, pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findLinePage(String month, Long storeId,
			String sn, Integer type, Pageable pageable) {
		// TODO 自动生成的方法存根
		return accountStatementDao.findLinePage(month, storeId, sn, type, pageable);
	}

}
