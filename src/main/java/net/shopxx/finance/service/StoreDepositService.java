package net.shopxx.finance.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.ui.ModelMap;

import com.aspose.cells.WorkbookDesigner;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.finance.entity.StoreDeposit;
import net.shopxx.util.ResponseEx;

/**
 * Service - 预存款
 */
public interface StoreDepositService extends BaseService<StoreDeposit> {

	/**
	 * 客户资金明细列表数据
	 */
	public Page<Map<String, Object>> findPage(String sn, String tradeNo, Long orderId, String orderSn, Long storeId,
			Integer[] type, String firstTime, String lastTime, Pageable pageable);

	/**
	 * 客户资金明细列表数据报表
	 */
	public Page<Map<String, Object>> findReport(String sn, Integer[] type, Long[] storeId, String firstTime,
			String lastTime, String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable);

	public Integer countReports(String sn, Integer[] type, Long[] storeId, String firstTime, String lastTime,
			String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable, Integer page, Integer size);

	public List<Map<String, Object>> findReportList(String sn, Integer[] type, Long[] storeId, String firstTime,
			String lastTime, String[] organizationName, Long[] saleOrgId, Long[] sbuId, Pageable pageable, Integer page,
			Integer size);

	public List<Map<String, Object>> findOrderItemListById(String ids);

	public List<Map<String, Object>> findReturnItemListById(String ids);

	/**
	 * 余额流水视图
	 * */
	public Page<Map<String, Object>> findPageCurrentAccount(String sn, String storeName, 
			String organizationName, String sbuName, String firstTime, String lastTime, Object[] type,
			Pageable pageable);
	
	public List<Map<String, Object>> findExportTable(String sn, String storeName, 
			String organizationName, String sbuName, String firstTime, String lastTime, Object[] type);
	
	public Page<Map<String, Object>> findPage(Long saleOrgId, Long storeId, String storeCode,
			 Long[] sbuId,Long[] organizationId,String firstTime,String lastTime, Pageable pageable);
	
	public List<Map<String, Object>> findTableNew(Long saleOrgId, Long storeId, String storeCode,
			 Long[] sbuId,Long[] organizationId,String firstTime,String lastTime);
	
	public List<Map<String,Object>> findTable(Long saleOrgId, Long storeId, String storeCode,
			 Long[] sbuId,Long[] organizationId,String firstTime,String lastTime,String[] ids);
	
	public Page<Map<String, Object>> findPageCustomerStatement( Long store, Long[] sbu,
			Long[] organization,String[] type,String firstTime, String lastTime,Long saleOrgId,
			Pageable pageable,Long typeId);
	
	public List<Map<String,Object>> findTableCustomerStatement(Long store, Long[] sbu,
			Long[] organization,String firstTime, String lastTime,String[] type,Long saleOrgId,
			Long[] ids,Long typeId);
	
	public Page<Map<String, Object>> findPageDifference( Long store, Long sbu,
			Long organization,Long saleOrgId,String firstTime, String lastTime, Pageable pageable);
	
	public List<Map<String,Object>> findTableDifference(Long store, Long sbu,
			Long organization,Long saleOrgId,String firstTime, String lastTime,Long[] ids);
	
	//获取销售报表数据
	public Page<Map<String, Object>> findSalesReportData(Long store, 
			Long[] sbu, Long[] organizationId, String firstTime, String lastTime, 
			String[] type,Long saleOrgId,  Pageable pageable,Long typeId,String productCode,String productName,String billCode);
	
	//获取销售报表总数
	public Integer findSalesReportCount(Long store, 
			Long[] sbu, Long[] organizationId, String firstTime, String lastTime, 
			String[] type,Long saleOrgId,  Pageable pageable,Long typeId,String productCode,String productName,String billCode);
	
	/**
	 * 导出客户余额报表
	 * author duoai gql 2020/5/19
	 * @return
	 */
	public Map<String, Object>  exportCustomerStatement(Long store, Long[] sbu,
			Long[] organization,String firstTime, String lastTime,String[] type,Long saleOrgId,
			int excelType,Long typeId);
	
	/**
	 * 按条件导出客户余额报表
	 * @param storeId
	 * @param type
	 * @param sbuId
	 * @param organizationId
	 * @param firstTime
	 * @param lastTime
	 * @param saleOrgId
	 * @param typeId
	 * @param model
	 * @param page
	 * @param pageable
	 * @param designer
	 * @return
	 */
	public ResponseEx newcCondition_export(Long storeId,String[] type, Long[] sbuId, 
			Long[] organizationId,String firstTime, String lastTime,Long saleOrgId,
			Long typeId,Integer page, Pageable pageable,WorkbookDesigner designer);
	
	
	/**
	 * 客戶对账单
	 * author duoai gql 2020/5/19
	 * @param
	 * @param
	 * @return
	 */
	public List<Map<String, Object>> customerStatementList(Long store, Long[] sbu,
			Long[] organization,String firstTime, String lastTime,String[] type,Long saleOrgId,
			Long typeId);
	
	/**
	 * 获取指定用户信息
	 * @param storeId
	 * @return
	 */
	public Map<String, Object> findStoreInfo(Long storeId);


	/**
	 * 获取客户对账单数据
	 * @param firstTime
	 * @param lastTime
	 * @param type
	 * @param saleOrgId
	 * @param typeId
	 * @return
	 */
	public  List<Map<String,Object>> getcustomerStatementListToEaeyPoi(Long storeId,String[] type, Long[] sbuId,
																	   Long[] organizationId,String firstTime, String lastTime,Long saleOrgId,
																	   Long typeId,Integer page, Pageable pageable);
}