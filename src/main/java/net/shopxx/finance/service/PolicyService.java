package net.shopxx.finance.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.finance.entity.Policy;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.wf.service.WfBillBaseService;

import org.springframework.web.multipart.MultipartFile;

public interface PolicyService extends WfBillBaseService<Policy> {

	/**
	 * 政策列表数据
	 */
	public Page<Map<String, Object>> findPage(String sn, String name,
			Long storeId, Integer applyType, Integer[] status, Long creatorId,
			Pageable pageable, Integer policyType);

	public void savePolicy(Policy policy, Store store, SaleOrg saleOrg);

	public List<Map<String, Object>> findAttachListByPolicyId(Long id);

	public List<Map<String, Object>> findPolicyProductListByPolicyrId(
			String policyIds);

	public List<Map<String, Object>> findPolicyGiftListByPolicyrId(
			String policyIds);

	public void checkPolicyWf(Policy policy, Long objConfId);

	public List<Map<String, Object>> findPolicyProductListByProductId(
			Long productId, Long storeId);

	public List<Map<String, Object>> findSelect(String sn, String name,
			Long storeId, Integer applyType, Integer[] status, Long creatorId);

	public Page<Map<String, Object>> findPolicyList(Pageable pageable,
			Object[] args);

	// public void check(CreditRecharge creditRecharge, Integer flag, String
	// note,
	// BigDecimal actualAmount);
	//
	// public void checkCreditRechargeWf(CreditRecharge creditRecharge,
	// Integer flag, String note, BigDecimal actualAmount, Long objConfId);
	//
	// public void effective(CreditRecharge creditRecharge);
	//
	// public void invalid(CreditRecharge creditRecharge);

	public void savePolicyYear(Policy policy, Long saleStyleId,
			Long[] orderStyleIds, Long areaId, Long[] customerTypeIds,
			Long[] a, Long policyFileId);

	public void updatePolicyYear(Policy policy, Long saleStyleId,
			Long[] orderStyleId, Long areaId, Long[] customerTypeId, Long[] a,
			Long policyFileId);

	public List<Map<String, Object>> findPolicyAmountListByPolicyId(
			String policyIds);

	public List<Map<String, Object>> findPolicySaleOrgListByPolicyId(
			String policyIds);

	public List<Map<String, Object>> findPolicyCustomerListByPolicyId(
			String policyIds);

	public List<Map<String, Object>> findPolicyRelationListByPolicyId(
			String policyIds);

	public List<Map<String, Object>> findPolicyProductId(String policyIds);

	public List<Map<String, Object>> findPolicyGoodId(String policyIds);

	public List<Map<String, Object>> findPolicyGiftsListByPolicyId(
			String policyIds);

	public boolean findByRole(StoreMember storemember, String roleName);

	/**
	 * 打款查询
	 * 
	 
	 */
	public Page<Map<String, Object>> selectPolicyMoney(String sn,
			Pageable pageable);

	public Page<Map<String, Object>> findPolicyProductPageForOrder(
			Long productId, Long storeId, Pageable pageable);

	/**
	 * 导入模板
	 * */
	public String policyImport(MultipartFile multipartFile) throws Exception;

	public List<Map<String, Object>> findlistdate(String sn, String name,
			Long storeId, Integer applyType, Integer[] status, Long[] ids);

	public Page<Map<String, Object>> findPolicyGiftPageByProductId(
			Long productId, Long storeId, Pageable pageable);
	
	public void checkPolicy(Policy policy);
	
	public int count(String sn, String name, Long storeId,
			Integer applyType, Integer[] status, Long[] ids);

}