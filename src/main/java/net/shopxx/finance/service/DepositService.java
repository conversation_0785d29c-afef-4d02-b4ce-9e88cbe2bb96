/*
 * Copyright 2005-2013 shopxx.net. All rights reserved. Support:
 * http://www.shopxx.net License: http://www.shopxx.net/license
 */
package net.shopxx.finance.service;

import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.finance.entity.Deposit;

/**
 * Service - 预存款
 * 
 * <AUTHOR> Team
 * @version 3.0
 */
public interface DepositService extends BaseService<Deposit> {

	/**
	 * 用户资金明细列表数据
	 * @param sn
	 * @param tradeNo
	 * @param orderId
	 * @param orderSn
	 * @param memberId
	 * @param type
	 * @param firstTime
	 * @param lastTime
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findPage(String sn, String tradeNo,
			Long orderId, String orderSn, Long memberId, Integer[] type,
			String firstTime, String lastTime, Pageable pageable);

}