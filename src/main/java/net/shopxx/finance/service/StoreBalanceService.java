package net.shopxx.finance.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.finance.entity.StoreBalance;

/*
 * Service - 可发货余额
 */
public interface StoreBalanceService extends BaseService<StoreBalance> {

	/*
	 * 查找可发货余额
	 * @param storeId
	 * @return
	 */
	public BigDecimal findBalance(Long storeId, Long organizationId,Long sbuId);

	/*
	 * 查找可发货余额sbu
	 */
	public Map<String, Object> findBalanceSbu(Long storeId, Long sbu,
			Long organizationId,Long saleOrgId);

	public BigDecimal findCheckBalance(Long storeId, Long organizationId);

	public void account(String lastDate, String currentDate, Integer flag);

	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args);

	public List<Map<String, Object>> findList(Long[] ids, Object[] args,
			Integer page, Integer size);

	public Integer countForExcel(Object[] args);

	public String getBalanceDate();

}