package net.shopxx.shop.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.Tort;
import net.shopxx.shop.entity.TortAttach;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.TortService;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Controller - 侵权
 */
@Controller("shopTortController")
@RequestMapping("/shop/tort")
public class TortController extends BaseController {

    @Resource(name = "tortServiceImpl")
    private TortService tortService;
    @Resource(name = "shopAddedServiceImpl")
    private ShopAddedService shopAddedService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;

    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(ModelMap model,Long objTypeId, Long objid, Long sid) {
        model.addAttribute("sid", sid);
        model.addAttribute("objTypeId", objTypeId);
        model.addAttribute("objid", objid);
        return "/shop/tort/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(Pageable pageable, ModelMap model, Long sid) {
        model.addAttribute("sid", sid);
        return "/shop/tort/list";
    }

    /**
     * 列表数据
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_data(Tort t, Long storeId, String createName,
                        String firstTime, String lastTime, Pageable pageable) {
    	List<Object> params = new ArrayList<Object>();
    	params.add(storeId);
		params.add(createName);
		params.add(firstTime);
		params.add(lastTime);
        Page<Map<String, Object>> page = tortService.findPage(t,params, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model, Long sid) {
        Store store = storeBaseService.find(sid);
        model.addAttribute("store", store);
        return "/shop/tort/add";
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        Tort tort = tortService.find(id);
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        shopAddedService.findPost(storeMember);
        List<Map<String, Object>> complaintAttach = tortService.findComplaintAttach(id);
        List<Map<String, Object>> noticeAttach = tortService.findNoticeAttach(id);
        List<Map<String, Object>> tortAttach = tortService.findTortAttach(id, 0);
        model.addAttribute("tort", tort);
        model.addAttribute("complaint_attach", JsonUtils.toJson(complaintAttach));
        model.addAttribute("notice_attach", JsonUtils.toJson(noticeAttach));
        model.addAttribute("tort_attach0", JsonUtils.toJson(tortAttach));

        if (tort.getWfId() != null) {
            //获取到当前流程
            ActWf wf = shopAddedService.getWfByWfId(tort.getWfId());
            if (wf != null) {
                //省长审核
                Boolean szsh = false;
                //渠道专员
                Boolean qdbsh = false;
                //销售中心副总
                Boolean xszxfz = false;
                //事业部总裁
                Boolean sybzc = false;
                //省长审核权限
                Boolean szshs = false;
                //渠道专员权限
                Boolean qdbshs = false;
                //销售中心副总权限
                Boolean xszxfzs = false;
                //事业部总裁权限
                Boolean sybzcs = false;
                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for (Map<String, Object> c : item) {
                    if (c.get("suggestion") != null) {
                        //处理结果
                        String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
                        //节点名称
                        String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
                        //对比节点名称是否对应
                        if (rwm.contains("省长")) {
                            szsh = Boolean.valueOf(approved);
                        }
                        if (rwm.contains("渠道")) {
                            qdbsh = Boolean.valueOf(approved);
                            if (!qdbsh) {
                                szsh = false;
                            }
                        }
                        if (rwm.contains("销售")) {
                            xszxfz = Boolean.valueOf(approved);
                            if (!xszxfz) {
                                qdbsh = false;
                            }
                        }
                        if (rwm.contains("事业部")) {
                            sybzc = Boolean.valueOf(approved);
                            if (!sybzc) {
                                xszxfz = false;
                            }
                        }
                    }
                }

                //获取当前流程所在的节点
                Task t = shopAddedService.getCurrTaskByWf(wf);
                if (t != null) {
                    //获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("省长")) {
                        szshs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道")) {
                        szshs = true;
                        qdbshs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("销售")) {
                        szshs = true;
                        qdbshs = true;
                        xszxfzs = true;
                    }
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("事业部")) {
                        szshs = true;
                        qdbshs = true;
                        xszxfzs = true;
                        sybzcs = true;
                    }
                }
                model.addAttribute("wf", wf);
                model.addAttribute("node", t);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdbsh", qdbsh);
                model.addAttribute("xszxfz", xszxfz);
                model.addAttribute("sybzc", sybzc);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdbshs", qdbshs);
                model.addAttribute("xszxfzs", xszxfzs);
                model.addAttribute("sybzcs", sybzcs);

            }
        }
        return "/shop/tort/edit";
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg save(Tort tort, String[] hp, Long storeId) {
    	if (tort.getTortArea() == null || tort.getTortArea().getId() == null) {
        	return error("请填写侵权地址");
        }
    	Store store = storeBaseService.find(storeId);
    	if (ConvertUtil.isEmpty(store)){
			return error("请选择被侵权人");
		}
        if (ConvertUtil.isEmpty(tort.getArea())) {
        	return error("请选择区域");
        }
        if (ConvertUtil.isEmpty(tort.getTortAddress())) {
        	return error("请填写侵权地址");
        }
        if (ConvertUtil.isEmpty(tort.getTortMatter())) {
        	return error("请填写侵权事项");
        }
        if (ConvertUtil.isEmpty(tort.getJoinTime())) {
        	return error("请填写加盟时间");
        }
        if (ConvertUtil.isEmpty(tort.getPrincipal())) {
        	return error("请填写区域负责人");
        }
        if(tort.getTortAttachs()  == null){
        	return error("请上传侵权附件！");
        }
        Boolean is0 = true;
        List<TortAttach> ta = tort.getTortAttachs();
    	for(TortAttach t : ta){
    		if(t.getType() == 0){
    			is0 = false;
    		}
    	}
        if(is0){
        	return error("请上传侵权依据！");
        }
        tort.setTorthp(shopAddedService.splicer(hp));
        tort.setStore(store);
        tortService.saveTort(tort);
        return success().addObjX(tort.getId());
    }

    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg update(Tort tort, Long storeId) {
    	Store store = storeBaseService.find(storeId);
    	if (tort.getTortArea() == null || tort.getTortArea().getId() == null) {
        	return error("请填写侵权地址");
        }
    	if (ConvertUtil.isEmpty(store)){
			return error("请选择被侵权人");
		}
        if (ConvertUtil.isEmpty(tort.getArea())) {
        	return error("请选择区域");
        }
        if (ConvertUtil.isEmpty(tort.getTortAddress())) {
        	return error("请填写侵权地址");
        }
        if (ConvertUtil.isEmpty(tort.getTortMatter())) {
        	return error("请填写侵权事项");
        }
        if (ConvertUtil.isEmpty(tort.getJoinTime())) {
        	return error("请填写加盟时间");
        }
        if (ConvertUtil.isEmpty(tort.getPrincipal())) {
        	return error("请填写区域负责人");
        }
        if(tort.getTortAttachs()  == null){
        	return error("请上传侵权附件！");
        }
        Boolean is0 = true;
        List<TortAttach> ta = tort.getTortAttachs();
    	for(TortAttach t : ta){
    		if(t.getType() == 0){
    			is0 = false;
    		}
    	}
        if(is0){
        	return error("请上传侵权依据！");
        }
        tort.setStore(store);
        tortService.updateTort(tort);
        return success().addObjX(tort.getId());
    }

    /**
     * @param id        单据id
     * @param modelId
     * @param objTypeId
     * @return
     */
    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
        if (id == null) {
            // 请选择订单
            return error("请选择侵权单据");
        }
//		StoreMember storeMember = storeMemberBaseService.getCurrent();
//		Tort tort = tortService.find(id);
//
//		tortService.createWf(tort.getSn(),
//				String.valueOf(storeMember.getId()),
//				modelId,
//				objTypeId,
//				id,
//				WebUtils.getCurrentCompanyInfoId());

        tortService.createWf(id, modelId, objTypeId);

        return success();
    }

    /**
     * 流程节点保存
     */
    @RequestMapping(value = "/saveform", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg saveform(Tort tort, String[] hp, Integer type) {
        // type 用来定义节点
        //type 1省长 2渠道部 3销售中心 4事业部
    	Tort t = tortService.find(tort.getId());
    	if(type == 99 &&t.getWfId()!=null&&t.getStatus()==1){
    		if (tort.getExpress() == null || tort.getExpress().getId() == null) {
            	return error("请选择快递地址");
            }
        	if (ConvertUtil.isEmpty(tort.getRecipientType())) {
            	return error("请填写收件人类别");
            }
        	if (ConvertUtil.isEmpty(tort.getContactWay())) {
            	return error("请填写联系方式");
            }
        	if (ConvertUtil.isEmpty(tort.getExpressAddress())) {
            	return error("请填写快递地址");
            }
        	if (ConvertUtil.isEmpty(hp)) {
            	return error("请填写侵权处理途径");
            }else {
    			for (int i = 0; i < hp.length; i++) {
    				if("发函致工商局".contains(hp[i])){
    					if(ConvertUtil.isEmpty(tort.getAicName())){
    						return error("请填写当地工商局名称");
    					}
    					if(ConvertUtil.isEmpty(tort.getAuthorizationName())){
    						return error("请填授权人姓名");
    					}
    					if(ConvertUtil.isEmpty(tort.getCard())){
    						return error("请填写身份证信息");
    					}
    				}

    				if("发函致侵权门店所在管理方".contains(hp[i])){
    					if(ConvertUtil.isEmpty(tort.getBusinessLicense())){
    						return error("请填写商场管理方经营执照名称");
    					}
    				}
    			}
    		}
    		tort.setTorthp(shopAddedService.splicer(hp));
    	}
    	tortService.saveform(tort, type);    		
        return success().addObjX(tort.getId());
    }

    @RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(Tort t, Long storeId, String createName,
                                        String firstTime, String lastTime, String[] header, String[] properties,
                                        Pageable pageable, ModelMap model, Integer page){
    	Map<String, Integer> segments = getSegment();
    	List<Object> params = new ArrayList<Object>();
    	params.add(storeId);
		params.add(createName);
		params.add(firstTime);
		params.add(lastTime);
		int size = segments.get("size");
		List<Map<String, Object>> data = tortService.findList(t, params, pageable, page, size);
    	return getModelAndViewForList(data, header, properties, model);
    }
    
    /**
	 * 条件导出
	 * 
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(Tort t, Long storeId, String createName,
                                                                     String firstTime, String lastTime){
		List<Object> params = new ArrayList<Object>();
    	params.add(storeId);
		params.add(createName);
		params.add(firstTime);
		params.add(lastTime);
		int size = tortService.count(t,params);
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,String[] header,String[] properties, ModelMap model) {
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		Integer[] widths = {};
		if(header==null){
			header = new String[]{};
		}
		if(properties==null){
			properties = new String[]{};
		}else{
			List<Integer> w = new ArrayList<Integer>();
			for(int i=0;i<properties.length;i++){
				w.add(25 * 256);
			}
			widths = w.toArray(new Integer[]{w.size()});
		}
		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}
    
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

}
