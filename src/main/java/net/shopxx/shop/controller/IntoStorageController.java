package net.shopxx.shop.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.Sbu;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreSbu;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreSbuService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.shop.dao.ShopOrderDao;
import net.shopxx.shop.entity.IntoOrOutStorage;
import net.shopxx.shop.entity.IntoOrOutStorageItem;
import net.shopxx.shop.service.IntoOrOutStorageService;
import net.shopxx.shop.service.SetStorageService;
import net.shopxx.shop.service.ShopStoreService;
import net.shopxx.shop.service.StorageProductService;

/**
 * 入库申请 
 */
@Controller("intoStorageController")
@RequestMapping("/shop/into_storage")
public class IntoStorageController extends BaseController {
    
    @Resource(name = "intoOrOutStorageServiceImpl")
    private IntoOrOutStorageService intoOrOutStorageService;
    @Resource(name = "setStorageServiceImpl")
    private SetStorageService setStorageService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;
    @Resource(name = "productCategoryBaseServiceImpl")
    private ProductCategoryBaseService productCategoryBaseService;
    @Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeService;
    @Resource(name = "storageProductServiceImpl")
    private StorageProductService storageProductService;
    @Resource(name = "shopOrderDao")
    private ShopOrderDao shopOrderDao;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name = "storeSbuServiceImpl")
    private StoreSbuService storeSbuService;
    @Resource(name="shopStoreServiceImpl")
    private ShopStoreService shopStoreService;

    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Pageable pageable, ModelMap model) {
        return "/shop/into_storage/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(Pageable pageable, ModelMap model) {
        return "/shop/into_storage/list";
    }
    
    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        // 当前经销商的仓库
        List<Map<String, Object>> oStorage = setStorageService.findOwnStorage();
        model.addAttribute("oStorage", oStorage);
        return "/shop/into_storage/add";
    }
    
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        IntoOrOutStorage intoStorage = intoOrOutStorageService.find(id);
        model.addAttribute("is", intoStorage);
        List<Map<String, Object>> oStorage = setStorageService.findOwnStorage();
        model.addAttribute("oStorage", oStorage);
        // 当前经销商
        Store store = shopStoreService.findStore();
        model.addAttribute("store", store);
        // 出入库明细
        List<Map<String, Object>> ioItems = intoOrOutStorageService.findIOItemById(id);
        String ioItem_json = JsonUtils.toJson(ioItems);
        model.addAttribute("ioItem_json", ioItem_json);
        //查订单sn
        List<Map<String, Object>> order = shopOrderDao.findOrder(intoStorage.getOrderId());
        String sn = null;
        if(order!=null&&order.size()>0){
        	for(Map<String, Object> o : order){
        		if(o.get("ticket_sn")!=null){
        			sn = o.get("ticket_sn").toString();
        		}
        	}        	
        }
        model.addAttribute("sn", sn);
        return "/shop/into_storage/edit";
    }
    
    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(String sn,String orderSn, Integer[] status, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(sn);
        param.add(0);  // type类型 入库
        param.add(status);
        param.add(orderSn);
        Page<Map<String, Object>> page = intoOrOutStorageService.findInPage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

    /**
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMsg save(IntoOrOutStorage intoOrOutStorage) {
        intoOrOutStorage.setStatus(0);  // 已保存状态
        intoOrOutStorage.setType(0);  // 标记为入库
        intoOrOutStorage.setSn(Sequence.getInstance().getSequence("R"));
        if (intoOrOutStorage.getStorage() != null && intoOrOutStorage.getStorage().getId() != null) {
            intoOrOutStorage.setStorage(setStorageService.find(intoOrOutStorage.getStorage().getId()));
        }
        // 处理产品项（出入库项）
        List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
        for (Iterator<IntoOrOutStorageItem> iterator = products.iterator(); iterator.hasNext();) {
            IntoOrOutStorageItem item = iterator.next();
            Product product = item.getProduct();
            if (product == null || product.getId() == null) {
                iterator.remove();
                continue;
            }
            if(item.getBranch()!=null){
            	if (BigDecimal.ZERO.compareTo(item.getBranch().divideAndRemainder(BigDecimal.ONE)[1]) != 0) {
					ExceptionUtil.throwServiceException("支数不能为小数！");
				}
            }
            item.setIntoOrOutStorage(intoOrOutStorage);
        }
        
        intoOrOutStorage.setIntoOtOutPro(products);
        intoOrOutStorage.setStatus(1);
        intoOrOutStorageService.save(intoOrOutStorage);
//        Store store = intoOrOutStorage.getStorage().getStore();
//        if(store==null){
//        	return error("该帐号没有绑定经销商!");
//        }
//        //把入库的商品写入到库存中
//        for (int i = 0; i < products.size(); i++) {
//            Product product = products.get(i).getProduct();
//            // 先判断该经销商是否有该商品，有则更新（增加）入库数量，没有则新增一条记录
//            List<Filter> filter = new ArrayList<Filter>();
//            filter.add(Filter.eq("storage", intoOrOutStorage.getStorage().getId()));
//            filter.add(Filter.eq("product", product.getId()));
//            filter.add(Filter.eq("store", store));
//            StorageProduct storageProduct = storageProductService.find(filter);
//            if (storageProduct != null && storageProduct.getId() != null) {
//                storageProduct.setInventory(storageProduct.getInventory().add(products.get(i).getNumber()));
//                storageProductService.update(storageProduct);
//            } else {
//                storageProduct = new StorageProduct();
//                storageProduct.setStore(store);
//                storageProduct.setProduct(product);
//                storageProduct.setStorage(intoOrOutStorage.getStorage());
//                storageProduct.setInventory(products.get(i).getNumber());
//                storageProductService.save(storageProduct);
//            }
//        }
        
        return success().addObjX(intoOrOutStorage.getId());
    }
    
    /**
     * 更新
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultMsg update(IntoOrOutStorage intoOrOutStorage) {
        IntoOrOutStorage inStorage = intoOrOutStorageService.find(intoOrOutStorage.getId());
        intoOrOutStorage.setType(inStorage.getType());
        intoOrOutStorage.setSn(inStorage.getSn());
        if (intoOrOutStorage.getStorage() != null && intoOrOutStorage.getStorage().getId() != null) {
            intoOrOutStorage.setStorage(setStorageService.find(intoOrOutStorage.getStorage().getId()));
        }
        // 处理产品项（出入库项）
        List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
        for (Iterator<IntoOrOutStorageItem> iterator = products.iterator(); iterator.hasNext();) {
            IntoOrOutStorageItem item = iterator.next();
            Product product = item.getProduct();
            if (product == null || product.getId() == null) {
                iterator.remove();
                continue;
            }
            if(item.getBranch()!=null){
            	if (BigDecimal.ZERO.compareTo(item.getBranch().divideAndRemainder(BigDecimal.ONE)[1]) != 0) {
					ExceptionUtil.throwServiceException("支数不能为小数！");
				}
            }
        }
        for (int i = 0; i < products.size(); i++) {
            IntoOrOutStorageItem item = products.get(i);
            item.setIntoOrOutStorage(intoOrOutStorage);
            item.setProduct(productBaseService.find(item.getProduct().getId()));
        }
        intoOrOutStorageService.update(intoOrOutStorage);
        return success().addObjX(intoOrOutStorage.getId());
    }
    
    /*
     * 列表
     */
    @RequestMapping(value = "/select_product", method = RequestMethod.GET)
    public String selectProduct(String code, Integer multi, Integer type,Long storeId, ModelMap model) {
        model.addAttribute("code", code);
        model.addAttribute("type", type);
        model.addAttribute("multi", multi);
        // 产品分类
        List<Object> objs = new ArrayList<Object>();
        List<Filter> fis = new ArrayList<Filter>();
        fis.add(Filter.isNull("parent"));
        fis.add(Filter.eq("isEnabled", true));
        fis.add(Filter.eq("type", 0));
        for (ProductCategory child : productCategoryBaseService.findList(null, fis, null)) {
            if ("强化地板".equals(child.getName()) || "弹性地板".equals(child.getName()) ||
                    "Nature".equals(child.getName()) || "实木地板".equals(child.getName()) || 
                    "实木复合三层地板".equals(child.getName()) || "实木复合多层地板".equals(child.getName()) ||
                    "辅料".equals(child.getName())) {
                Map<String, Object> data = new HashMap<String, Object>();
                data.put("id", child.getId());
                data.put("name", child.getName());
                data.put("isParent", !getIsLeft(child));
                objs.add(data);
            }
        }
        model.addAttribute("pt", objs);
        Store store = storeService.find(storeId);
        fis.clear();
        fis.add(Filter.eq("store", store));
        fis.add(Filter.eq("isDefault", true));
        List<StoreSbu> sb = storeSbuService.findList(null, fis, null);
        List<Sbu> sbu = new ArrayList<Sbu>();
        for(StoreSbu s : sb){
        	sbu.add(s.getSbu());
        }
        String s = "";
        for(int i=0;i<sbu.size();i++){
        	if(i==sbu.size()-1){
        		s += sbu.get(i).getId().toString();
        	}else{
        		s += sbu.get(i).getId().toString()+",";
        	}
        }
        model.addAttribute("sbu", s);
        return "/shop/into_storage/select_into_or_out_product";
    }
    
    public boolean getIsLeft(ProductCategory productCategory) {
        boolean isLeft = productCategory.getIsLeaf();
        if (!isLeft) {
            long count = productCategoryBaseService.count(Filter.eq("parent",
                    productCategory), Filter.eq("isEnabled", true));
            if (count == 0) {
                isLeft = true;
            }
        }
        return isLeft;
    }
    
    /**
     * 入库审核
     */
    @ResponseBody
    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public ResultMsg check_wf(Long id) {
    	IntoOrOutStorage is = intoOrOutStorageService.find(id);
    	intoOrOutStorageService.Icheck_wf(is, 1);
    	is.setStatus(4);
        return success().addObjX(is.getId());
    }
    
    
}
