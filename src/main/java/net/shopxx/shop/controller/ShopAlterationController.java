package net.shopxx.shop.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.shop.entity.ShopAlterationAttach;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.ShopAlteration;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopAlterationService;
import net.shopxx.shop.service.ShopInfoService;

/**
 * Controller - 门店变更
 */
@Controller("shopAlterationController")
@RequestMapping("/shop/alteration")
public class ShopAlterationController extends BaseController {

    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name = "shopAddedServiceImpl")
    private ShopAddedService shopAddedService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;
    @Resource(name = "shopAlterationServiceImpl")
    private ShopAlterationService shopAlterationService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;

    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Long shopInfoId, Long objTypeId, Long objid, Pageable pageable, ModelMap model) {
        model.addAttribute("id", shopInfoId);
        model.addAttribute("objTypeId", objTypeId);
        model.addAttribute("objid", objid);
        return "/shop/alteration/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        return "/shop/alteration/list";
    }

    /**
     * 列表数据
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_data(Pageable pageable) {
        Page<Map<String, Object>> page = shopAlterationService.findPage(null, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);

    }

    /**
     * 门店入口跳转判定
     */
    @RequestMapping(value = "/view", method = RequestMethod.GET)
    public String view(Long shopInfoId, ModelMap model) {
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        List<Filter> filters = new ArrayList<Filter>();
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        String url = "";
        List<ShopAlteration> sa = new ArrayList<ShopAlteration>();
        List<Map<String, Object>> salist = shopAlterationService.findSA(shopInfoId);
        for (Map<String, Object> m : salist) {
            if (m.get("id") != null) {
                sa.add(shopAlterationService.find(Long.parseLong(m.get("id").toString())));
            }
        }
        if (sa.size() > 1) {
            ExceptionUtil.throwServiceException("页面跳转失败");
        }
        if (sa.size() == 0 || sa == null) {
            url = "/shop/alteration/add";
            Store store = shopInfo.getStore();
            filters.clear();
            filters.add(Filter.eq("code", "businessCategory"));
            filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
            filters.add(Filter.isNotNull("parent"));
            List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
            model.addAttribute("BusinessCategory", businessCategoryList);
            model.addAttribute("store", store);
            model.addAttribute("shopInfo", shopInfo);
        } else if (sa.size() == 1) {
            url = "/shop/alteration/edit";
            ShopAlteration shopAlteration = sa.get(0);
//			List<Map<String, Object>> shopAlterationAttachs = shopAlterationService.findShopAlterationAttach(sa.get(0).getId());
            List<Map<String, Object>> shopFarAlterationAttach = shopAlterationService.findShopAttach1(shopAlteration.getId(), 0);
            List<Map<String, Object>> shopAlterationAttach = shopAlterationService.findShopAttach1(shopAlteration.getId(), 1);
            List<Map<String, Object>> payPlatformAlterationAttach = shopAlterationService.findShopAttach1(shopAlteration.getId(), 2);
            List<Map<String, Object>> plateAlterationAttach = shopAlterationService.findShopAttach1(shopAlteration.getId(), 3);
            filters.clear();
            filters.add(Filter.eq("code", "businessCategory"));
            filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
            filters.add(Filter.isNotNull("parent"));
            List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
            model.addAttribute("BusinessCategory", businessCategoryList);
            filters.clear();
            filters.add(Filter.eq("code", "shopSign"));
            filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
            filters.add(Filter.isNotNull("parent"));
            List<SystemDict> shopSignList = systemDictService.findList(null, filters, null);
            model.addAttribute("shopSign", shopSignList);
            model.addAttribute("shopAlteration", shopAlteration);
            model.addAttribute("shopFarAlterationAttach", JsonUtils.toJson(shopFarAlterationAttach));
            model.addAttribute("shopAlterationAttach", JsonUtils.toJson(shopAlterationAttach));
            model.addAttribute("payPlatformAlterationAttach", JsonUtils.toJson(payPlatformAlterationAttach));
            model.addAttribute("plateAlterationAttach", JsonUtils.toJson(plateAlterationAttach));
//			model.addAttribute("shop_attach", JsonUtils.toJson(shopAlterationAttachs == null ? "" : shopAlterationAttachs));
            // 获取流程节点
            if (shopAlteration.getWfId() != null) {
                // 获取到当前流程
                ActWf wf = shopAlterationService.getWfByWfId(shopAlteration.getWfId());
                if (wf != null) {
                    // 区域经理
                    Boolean qyjl = false;
                    // 渠道专员
                    Boolean qdzy = false;
                    // 区域经理权限
                    Boolean qyjls = false;
                    // 渠道专员权限
                    Boolean qdzys = false;
                    // 查找当前流程明细
                    List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                    for (Map<String, Object> c : item) {
                        if (c.get("suggestion") != null) {
                            // 处理结果
                            String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
                            // 节点名称
                            String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
                            // 对比节点名称是否对应
                            if (rwm.contains("区域经理")) {
                                // 赋值处理结果到所定义的节点上
                                qyjl = Boolean.valueOf(approved);
                            }
                            if (rwm.contains("渠道")) {
                                qdzy = Boolean.valueOf(approved);
                            }
                        }
                    }
                    // 获取当前流程所在的节点
                    Task t = shopAddedService.getCurrTaskByWf(wf);
                    if (t != null) {
                        // 获取当前节点所有用户id
                        List<String> userId = actWfService.getTaskUsers(t.getId());
                        if (userId.contains(storeMember.getId().toString()) && t.getName().contains("区域经理")) {
                            qyjls = true;
                        }

                        if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道")) {
                            qyjls = true;
                            qdzys = true;
                        }
                    }
                    model.addAttribute("wf", wf);
                    model.addAttribute("node", t);
                    model.addAttribute("qyjl", qyjl);
                    model.addAttribute("qdzy", qdzy);
                    model.addAttribute("qyjls", qyjls);
                    model.addAttribute("qdzys", qdzys);
                }
            }
        }
        return url;
    }

    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(Long shopInfoId, ModelMap model) {
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        List<Filter> filters = new ArrayList<Filter>();
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        Store store = shopInfo.getStore();
        filters.add(Filter.eq("code", "businessCategory"));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
        model.addAttribute("BusinessCategory", businessCategoryList);
        model.addAttribute("store", store);
        model.addAttribute("shopInfo", shopInfo);
        return "/shop/alteration/add";
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        List<Filter> filters = new ArrayList<Filter>();
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        ShopAlteration shopAlteration = shopAlterationService.find(id);
//		List<Map<String, Object>> shopAlterationAttachs = shopAlterationService.findShopAlterationAttach(id);
        List<Map<String, Object>> shopFarAlterationAttach = shopAlterationService.findShopAttach1(shopAlteration.getId(), 0);
        List<Map<String, Object>> shopAlterationAttach = shopAlterationService.findShopAttach1(shopAlteration.getId(), 1);
        List<Map<String, Object>> payPlatformAlterationAttach = shopAlterationService.findShopAttach1(shopAlteration.getId(), 2);
        List<Map<String, Object>> plateAlterationAttach = shopAlterationService.findShopAttach1(shopAlteration.getId(), 3);
        filters.add(Filter.eq("code", "businessCategory"));
        filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
        filters.add(Filter.isNotNull("parent"));
        List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
        model.addAttribute("BusinessCategory", businessCategoryList);
        model.addAttribute("shopAlteration", shopAlteration);
        model.addAttribute("shopFarAlterationAttach", JsonUtils.toJson(shopFarAlterationAttach));
        model.addAttribute("shopAlterationAttach", JsonUtils.toJson(shopAlterationAttach));
        model.addAttribute("payPlatformAlterationAttach", JsonUtils.toJson(payPlatformAlterationAttach));
        model.addAttribute("plateAlterationAttach", JsonUtils.toJson(plateAlterationAttach));
//		model.addAttribute("shop_attach", JsonUtils.toJson(shopAlterationAttachs == null ? "" : shopAlterationAttachs));
        // 获取流程节点
        if (shopAlteration.getWfId() != null) {
            // 获取到当前流程
            ActWf wf = shopAlterationService.getWfByWfId(shopAlteration.getWfId());
            if (wf != null) {
                // 区域经理
                Boolean qyjl = false;
                // 渠道专员
                Boolean qdzy = false;
                // 区域经理权限
                Boolean qyjls = false;
                // 渠道专员权限
                Boolean qdzys = false;
                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for (Map<String, Object> c : item) {
                    if (c.get("suggestion") != null) {
                        // 处理结果
                        String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
                        // 节点名称
                        String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
                        // 对比节点名称是否对应
                        if (rwm.contains("区域经理")) {
                            // 赋值处理结果到所定义的节点上
                            qyjl = Boolean.valueOf(approved);
                        }
                        if (rwm.contains("渠道")) {
                            qdzy = Boolean.valueOf(approved);
                        }
                    }
                }
                // 获取当前流程所在的节点
                Task t = shopAddedService.getCurrTaskByWf(wf);
                if (t != null) {
                    // 获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());
                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("区域经理")) {
                        qyjls = true;
                    }

                    if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道")) {
                        qyjls = true;
                        qdzys = true;
                    }
                }
                model.addAttribute("wf", wf);
                model.addAttribute("node", t);
                model.addAttribute("qyjl", qyjl);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("qyjls", qyjls);
                model.addAttribute("qdzys", qdzys);
            }
        }

        return "/shop/alteration/edit";
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg save(ShopAlteration shopAlteration, Boolean isDesign,
                   String shopOwnership, BigDecimal monthlyRent, String[] businessCategory, Long storeId,
                   Long shopInfoId) {
        if (shopAlteration.getNewShopArea() == null || shopAlteration.getNewShopArea().getId() == null) {
            return error("请输入新店地址！");
        }
        if (isDesign == false && shopAlteration.getShopAlterationAttachs().size() < 1) {
            return error("请上传门店附件！");
        } else {
            checkAttachs(shopAlteration.getShopAlterationAttachs());
        }

        if (shopOwnership.equals("租赁") && monthlyRent == null) {
            return error("请填写月租金!");
        }
        shopAlterationService.saveAlteration(shopAlteration, businessCategory, storeId, shopInfoId);
        return success().addObjX(shopAlteration.getId());
    }

    /**
     * 校验附件
     *
     * @param shopAlterationAttachs
     */
    private void checkAttachs(List<ShopAlterationAttach> shopAlterationAttachs) {

        //门头远距离照数量
        int type0 = 0;
        //门头近距离照数量
        int type1 = 0;
        //收银台照片数量
        int type2 = 0;
        //样板区照片数量
        int type3 = 0;
        for (ShopAlterationAttach shopAlterationAttach : shopAlterationAttachs) {
            Integer type = shopAlterationAttach.getType();
            if (type == 0) {
                type0 += 1;
            } else if (type == 1) {
                type1 += 1;
            } else if (type == 2) {
                type2 += 1;
            } else if (type == 3) {
                type3 += 1;
            }
        }

        if(type0 < 1){
        	error("请上传门头远距离照");
		}
        if(type1 < 1){
			error("请上传门头近距离照");
		}
        if(type2 < 1){
			error("请上传收银台照片");
		}
        if(type3 < 1){
			error("请上传样板区照片");
		}
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg update(ShopAlteration shopAlteration, Boolean isDesign,
                     String shopOwnership, BigDecimal monthlyRent, String[] businessCategory, Long storeId,
                     Long shopInfoId) {
        if (shopAlteration.getNewShopArea() == null || shopAlteration.getNewShopArea().getId() == null) {
            return error("请输入新店地址！");
        }
        if (isDesign == false && shopAlteration.getShopAlterationAttachs().size() < 1) {
            return error("请上传门店附件！");
        }
        if (shopOwnership.equals("租赁") && monthlyRent == null) {
            return error("请填写月租金!");
        }
        if (shopAlteration.getBusinessCategory() == null) {
            return error("请选择经营品类！");
        }
        shopAlterationService.updateAlteration(shopAlteration, businessCategory, storeId, shopInfoId);
        return success().addObjX(shopAlteration.getId());
    }

    /**
     * 作废
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg cancel(Long id) {
        if (id == null) {
            return error("请选择单据");
        }
        ShopAlteration sa = shopAlterationService.find(id);
        if (sa.getStatuss().equals("cancel")) {
            return error("操作失败!单据已作废!");
        }
        shopAlterationService.cancel(id);
        return success().addObjX(sa.getInfoShop().getId());
    }

    /**
     * 审核
     *
     * @param shopId
     * @return
     */
    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
        if (id == null) {
            // 请选择订单
            return error("请选择门店单据");
        }
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        ShopAlteration shopAlteration = shopAlterationService.find(id);
//		shopAlterationService.createWf(shopAlteration.getSn(), String.valueOf(storeMember.getId()), modelId, objTypeId,
//				id, WebUtils.getCurrentCompanyInfoId());
        shopAlterationService.createWf(id, modelId, objTypeId);

        shopAlterationService.setStatus(shopAlteration);
        return success();
    }

    /**
     * 流程节点保存
     */
    @RequestMapping(value = "/saveform", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg saveform(ShopAlteration shopAlteration, String[] shopSign, Integer Type) {
        // type 用来定义节点
        // type 1区域经理 2渠道部
        shopAlterationService.saveform(shopAlteration, shopSign, Type);
        return success().addObjX(shopAlteration.getId());
    }


}
