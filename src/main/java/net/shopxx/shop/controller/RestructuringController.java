package net.shopxx.shop.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.Restructuring;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.RestructuringService;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopInfoService;

/**
 * Controller - 停业整顿
 */
@Controller("restructuringController")
@RequestMapping("/shop/restructuring")
public class RestructuringController extends BaseController{
	
	@Resource(name = "shopAddedServiceImpl")
	private ShopAddedService shopAddedService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "restructuringServiceImpl")
	private RestructuringService restructuringService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long shopInfoId,Long objTypeId, Long objid,Pageable pageable, ModelMap model) {	
		model.addAttribute("id",shopInfoId);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		return "/shop/restructuring/list_tb";
	}
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(String sn,Pageable pageable, ModelMap model) {
		return "/shop/restructuring/list";
	}
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn,Pageable pageable) {
		Page<Map<String, Object>> page = restructuringService.findPage(sn, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);

	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long shopInfoId,ModelMap model) {
		ShopInfo shopInfo = shopInfoService.find(shopInfoId);
		Store store = shopInfo.getStore();
		model.addAttribute("shopInfo",shopInfo);				
		
		model.addAttribute("store",store);
	
		return "/shop/restructuring/add";
	}
	
	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		Restructuring rt = restructuringService.find(id);
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		shopAddedService.findPost(storeMember);
		
		model.addAttribute("rt",rt);
		// 获取流程节点
		if (rt.getWfId() != null) {
			//获取到当前流程
			ActWf wf = shopAddedService.getWfByWfId(rt.getWfId());
			//获取当前流程所在的节点
			if(wf!= null){
				//省长审核
				Boolean szsh = false;
				//区域经理
				Boolean qyjl = false;
				//省长审核权限
				Boolean szshs = false;
				//区域经理权限
				Boolean qyjls = false;
				// 查找当前流程明细
				List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
				for(Map<String, Object> c : item){
					if(c.get("suggestion")!=null){
						//处理结果
						String approved = c.get("approved")!=null?c.get("approved").toString():"false";
						//节点名称
						String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
						//对比节点名称是否对应
						if(rwm.contains("省长")){
							szsh = Boolean.valueOf(approved);
						}
						if(rwm.contains("区域经理")){
							qyjl = Boolean.valueOf(approved);
							if(!qyjl){
								szsh = false;
							}
						}						
					}
				}
				Task t = shopAddedService.getCurrTaskByWf(wf);
				if(t!=null){
					//获取当前节点所有用户id
					List<String> userId = actWfService.getTaskUsers(t.getId());
					if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
						szshs = true;
					}
					if(userId.contains(storeMember.getId().toString())&&t.getName().contains("区域经理")){
						szshs = true;qyjls = true;
					}
				}
				model.addAttribute("wf", wf);
				model.addAttribute("node", t);
				model.addAttribute("szsh", szsh);
				model.addAttribute("qyjl", qyjl);
				model.addAttribute("szshs", szshs);
				model.addAttribute("qyjls", qyjls);			
			}
		}
		return "/shop/restructuring/edit";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(Restructuring rt,Long storeId,Long shopInfoId) {
		restructuringService.saveRestructuring(rt, storeId, shopInfoId);
		return success().addObjX(rt.getId());
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(Restructuring rt,Long storeId,Long shopInfoId) {
		restructuringService.updateRestructuring(rt, storeId, shopInfoId);
		return success().addObjX(rt.getId());
	}
	
	
	/**
	 * 审核
	 * @param shopId
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check_wf(Long id,String modelId) {
		if (id == null) {
			return error("请选择单据");
		}
		restructuringService.createWf(id, modelId, 10009L);

		return success();
	}
//	/**
//	 * 流程节点保存
//	 */
//	@RequestMapping(value = "/saveform", method = RequestMethod.POST)
//	public @ResponseBody ResultMsg saveform(Restructuring restructuring,  Integer type) {
//		// type 用来定义节点
//		//type 1渠道部
//		restructuringService.saveform(restructuring,type);
//		return success().addObjX(restructuring.getId());
//	}
}
