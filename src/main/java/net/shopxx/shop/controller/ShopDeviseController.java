package net.shopxx.shop.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.AcceptanceReimburse;
import net.shopxx.shop.entity.ShopDevise;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.AcceptanceReimburseService;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopDeviseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.wf.service.WfBaseService;

/**
 * Controller - 门店设计
 * 
 */
@Controller("shopDeviseController")
@RequestMapping("/shop/devise")
public class ShopDeviseController extends BaseController {

	@Resource(name = "shopDeviseServiceImpl")
	private ShopDeviseService shopDeviseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "acceptanceReimburseServiceImpl")
	private AcceptanceReimburseService acceptanceReimburseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name = "shopAddedServiceImpl")
	private ShopAddedService shopAddedService;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long objTypeId, Long objid,Pageable pageable, ModelMap model) {
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		return "/shop/devise/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(String sn, Pageable pageable, ModelMap model) {
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		return "/shop/devise/list";
	}

	/**
	 * 选择列表
	 */
	@RequestMapping(value = "/select_shop_devise", method = RequestMethod.GET)
	public String select_shopInfo(Integer multi, ModelMap model) {
		model.addAttribute("multi", multi);
		return "/shop/devise/select_shop_devise";
	}

	/**
	 * 列表数据 - 选择门店设计的列表数据
	 */
	@ResponseBody
	@RequestMapping(value = "/select_shop_devise_data", method = RequestMethod.POST)
	public ResultMsg selectShopInfoData(String sn, Pageable pageable) {
		List<Object> param = new ArrayList<Object>();
		param.add(sn);
		param.add(null);
		param.add(null);
		Page<Map<String, Object>> page = shopDeviseService.findSelectPage(param, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

//	/**
//	 * 列表数据
//	 */
//	@ResponseBody
//	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
//	public ResultMsg list_data(String sn, String[] attributes, Integer[] status, Integer[] reimburses,
//			Pageable pageable) {
//		List<Object> param = new ArrayList<Object>();
//		param.add(sn);
//		param.add(status);
//		param.add(attributes);
//		param.add(reimburses);
//		Page<Map<String, Object>> page = shopDeviseService.findPage(param, pageable);
//		String jsonPage = JsonUtils.toJson(page);
//		return success(jsonPage);
//	}
	/**
	 * 列表数据
	 */
	@ResponseBody
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public ResultMsg list_data(
			ShopDevise shopDevise, String[] attributes, Integer[] status, Integer[] reimburses,
			Long designerId,Long shopInfoId,Long storeId,Long saleOrgId,Long storeMemberId,
			String dfirstTime,String dlastTime,String pfirstTime,String plastTime,String wfirstTime,
			String wlastTime,Pageable pageable) {
		List<Object> param = new ArrayList<Object>();
		param.add(status);      //单号
		param.add(attributes);  //装修属性
		param.add(reimburses);  //是否报销
		param.add(designerId);  //设计师
		param.add(shopInfoId);  //门店
		param.add(storeId);     //客户
		param.add(saleOrgId);   //机构
		param.add(storeMemberId);//区域经理
		param.add(dfirstTime);  //提交设计师开始时间
		param.add(dlastTime);   //提交设计师结束时间
		param.add(pfirstTime);  //平面图完成开始时间
		param.add(plastTime);   //平面图完成结束时间
		param.add(wfirstTime);  //施工图完成开始时间
		param.add(wlastTime);  //施工图完成结束时间
		Page<Map<String, Object>> page = shopDeviseService.findPage(shopDevise,param, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		return "/shop/devise/add";
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) throws Exception {
		ShopDevise shopDevise = shopDeviseService.find(id);
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		// 上样计划
		List<String> spList = new ArrayList<String>();
		if (!StringUtils.isEmpty(shopDevise.getSamplePlan())) {
			String[] strs = shopDevise.getSamplePlan().split("==");
			spList = Arrays.asList(strs);
		}
		model.addAttribute("sp", spList);
		// 省运营管理中心总经理意见
		List<String> rmoList = new ArrayList<String>();
		if (!StringUtils.isEmpty(shopDevise.getRegionalManagerOpinion())) {
			String[] strs = shopDevise.getRegionalManagerOpinion().split(",");
			rmoList = Arrays.asList(strs);
		}
		model.addAttribute("rmo", rmoList);
		// 地板事业部渠道部
		List<String> fdList = new ArrayList<String>();
		if (!StringUtils.isEmpty(shopDevise.getFloorDivision())) {
			String[] strs = shopDevise.getFloorDivision().split(",");
			fdList = Arrays.asList(strs);
		}
		model.addAttribute("fd", fdList);
		// 经销商提交申请附件
		List<Map<String, Object>> dealersAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 0);
		List<Map<String, Object>> storeContractAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 3);
		List<Map<String, Object>> storePicturesAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 4);
		// 区域经理审核附件
		List<Map<String, Object>> managerDealersAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 1);
		List<Map<String, Object>> managerStoreContractAttachs = shopDeviseService
				.findShopDeviseAttach(shopDevise.getId(), 5);
		List<Map<String, Object>> managerStorePicturesAttachs = shopDeviseService
				.findShopDeviseAttach(shopDevise.getId(), 6);
		// 设计部确认附件
		List<Map<String, Object>> designAttachs = shopDeviseService.findShopDeviseAttach(shopDevise.getId(), 2);
		model.addAttribute("shopDevise", shopDevise);
		model.addAttribute("dealers_attach", JsonUtils.toJson(dealersAttachs));
		model.addAttribute("store_contract_attachs", JsonUtils.toJson(storeContractAttachs));
		model.addAttribute("store_pictures_attachs", JsonUtils.toJson(storePicturesAttachs));
		model.addAttribute("manager_dealers_attach", JsonUtils.toJson(managerDealersAttachs));
		model.addAttribute("manager_store_contract_attachs", JsonUtils.toJson(managerStoreContractAttachs));
		model.addAttribute("manager_store_pictures_attachs", JsonUtils.toJson(managerStorePicturesAttachs));
		model.addAttribute("design_attach", JsonUtils.toJson(designAttachs));

		// 获取流程节点
		if (shopDevise.getWfId() != null) {
			// 获取到当前流程
			ActWf wf = shopAddedService.getWfByWfId(shopDevise.getWfId());
			// 获取当前流程所在的节点
			if (wf != null) {
				// 区域经理
				Boolean qyjl = false;
				// 省长审核
				Boolean szsh = false;
				// 渠道专员
				Boolean qdzy = false;
				// 渠道总监
				Boolean qdzj = false;
				// 门店设计师
				Boolean mdsjs = false;
				// 区域经理权限
				Boolean qyjls = false;
				// 省长审核权限
				Boolean szshs = false;
				// 渠道专员权限
				Boolean qdzys = false;
				// 渠道总监
				Boolean qdzjs = false;
				// 门店设计师权限
				Boolean mdsjss = false;
				// 查找当前流程明细
				List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
				for (Map<String, Object> c : item) {
					if (c.get("suggestion") != null) {
						// 处理结果
						String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
						// 节点名称
						String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
						// 对比节点名称是否对应
						if (rwm.contains("区域经理")) {
							// 赋值处理结果到所定义的节点上
							qyjl = Boolean.valueOf(approved);
						}
						if (rwm.contains("省长")) {
							szsh = Boolean.valueOf(approved);
							// 如果父节点驳回改变上一个节点状态
							if (!szsh) {
								qyjl = false;
							}
						}
						if (rwm.contains("渠道专员")) {
							qdzy = Boolean.valueOf(approved);
							if (!qdzy) {
								szsh = false;
							}
						}
						if (rwm.contains("渠道总监")) {
							qdzj = Boolean.valueOf(approved);
							if (!qdzj) {
								qdzy = false;
							}
						}
						if (rwm.contains("设计部")) {
							mdsjs = Boolean.valueOf(approved);
							if (!mdsjs) {
								qdzj = false;
							}
						}
					}
				}
				Task t = shopAddedService.getCurrTaskByWf(wf);
				if (t != null) {
					// 获取当前节点所有用户id
					List<String> userId = actWfService.getTaskUsers(t.getId());
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("区域经理")) {
						qyjls = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("省长")) {
						szshs = true;
						qyjls = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道专员")) {
						szshs = true;
						qyjls = true;
						qdzys = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道总监")) {
						szshs = true;
						qyjls = true;
						qdzys = true;
						qdzjs = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("设计部")) {
						szshs = true;
						qyjls = true;
						qdzys = true;
						qdzjs = true;
						mdsjss = true;
					}
				}
				model.addAttribute("wf", wf);
				model.addAttribute("node", t);
				model.addAttribute("qyjl", qyjl);
				model.addAttribute("szsh", szsh);
				model.addAttribute("qdzy", qdzy);
				model.addAttribute("qdzj", qdzj);
				model.addAttribute("mdsjs", mdsjs);
				model.addAttribute("qyjls", qyjls);
				model.addAttribute("szshs", szshs);
				model.addAttribute("qdzys", qdzys);
				model.addAttribute("qdzjs", qdzjs);
				model.addAttribute("mdsjss", mdsjss);
			}
		}
		return "/shop/devise/edit";
	}

	/**
	 * 保存
	 */
	@ResponseBody
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public ResultMsg save(ShopDevise shopDevise, Long shopInfoId, String[] samplePlans,
			Integer[] regionalManagerOpinions, Integer reimburse, Integer[] floorDivisions) {
		ShopInfo shopInfo = shopInfoService.find(shopInfoId);
		if (shopInfo == null) {
			return error("门店选择有误，请重新选择！");
		}
		if (samplePlans == null) {
			return error("请勾选上样计划！");
		}
		if (reimburse == null) {
			return error("请选择是否参与报销！");
		}
		if (shopDevise.getDealersAttachs().size() < 1) {
			return error("请上传门店照片！");
		}
		if (shopDevise.getDealersAttachs().size() < 5) {
			return error("上传门店照片不能少于5张！");
		}
		if (shopDevise.getStoreContractAttachs().size() < 1) {
			return error("请上传租赁合同！");
		}
		if (shopDevise.getStorePicturesAttachs().size() < 1) {
			return error("请上传平面图！");
		}
		if (shopDevise.getPredictConstructionTime() != null && shopDevise.getPredictStartsTime() != null) {
			Date a = shopDevise.getPredictConstructionTime();
			Date b = shopDevise.getPredictStartsTime();
			if (a.getTime() > b.getTime()) {
				return error("预计施工时间不能大于预计预计开业时间！");
			}
		}
		// 处理上样占比总和 100%
		BigDecimal solidWoodRatio = shopDevise.getSolidWoodRatio();
		BigDecimal intensifyRatio = shopDevise.getIntensifyRatio();
		BigDecimal multilayerRatio = shopDevise.getMultilayerRatio();
		BigDecimal total = solidWoodRatio.add(intensifyRatio).add(multilayerRatio);
		if (total.compareTo(new BigDecimal(100)) != 0) {
			return error("上样占比总和须为100%");
		}
		shopDevise.setShopInfo(shopInfo);
		shopDevise.setSaleOrg(shopInfo.getSaleOrg() != null ? shopInfo.getSaleOrg() : null);
		if (samplePlans != null && samplePlans.length > 0) {
			// 以双等号(==)来分隔上样计划
			String samplePlan = StringUtils.arrayToDelimitedString(samplePlans, "==");
			shopDevise.setSamplePlan(samplePlan);
		}
		if (regionalManagerOpinions != null && regionalManagerOpinions.length > 0) {
			// 省运营管理中心总经理意见以逗号分隔（接收的是整型数据）
			String regionalManagerOpinion = StringUtils.arrayToDelimitedString(regionalManagerOpinions, ",");
			shopDevise.setRegionalManagerOpinion(regionalManagerOpinion);
		}
		if (floorDivisions != null && floorDivisions.length > 0) {
			// 地板事业部渠道部审核以逗号分隔（接收的是整型数据）
			String floorDivision = StringUtils.arrayToDelimitedString(floorDivisions, ",");
			shopDevise.setFloorDivision(floorDivision);
		}
		// 保存状态
		shopDevise.setBillsStatus(0);
		shopDeviseService.saveShopDevise(shopDevise);

		return success().addObjX(shopDevise.getId());
	}

	/**
	 * 判断门店设计是否已经存在过该门店的记录
	 * 
	 * @return 存在返回 1，不存在返回 0
	 */
	@ResponseBody
	@RequestMapping(value = "/existShopInfo", method = RequestMethod.POST)
	public String existShopDevise(Long shopInfoId) {
		if (shopInfoId == null) {
			ExceptionUtil.throwServiceException("门店信息有误");
		}
		Filter filter = Filter.exists("shopInfo", shopInfoId);
		if (shopDeviseService.exists(filter)) {
			return "1";
		}
		return "0";
	}

	/**
	 * 更新
	 */
	@ResponseBody
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public ResultMsg update(ShopDevise shopDevise, Long shopInfoId, String[] samplePlans,
			Integer[] regionalManagerOpinions, Integer reimburse, Integer[] floorDivisions) {
		ShopInfo shopInfo = shopInfoService.find(shopInfoId);
		if (shopInfo == null) {
			return error("门店选择有误，请重新选择！");
		}
		if (samplePlans == null) {
			return error("请勾选上样计划！");
		}
		if (reimburse == null) {
			return error("请选择是否参与报销！");
		}
		if (shopDevise.getDealersAttachs().size() < 1) {
			return error("请上传门店照片！");
		}
		if (shopDevise.getDealersAttachs().size() < 5) {
			return error("上传门店照片不能少于5张！");
		}
		if (shopDevise.getStoreContractAttachs().size() < 1) {
			return error("请上传租赁合同！");
		}
		if (shopDevise.getStorePicturesAttachs().size() < 1) {
			return error("请上传平面图！");
		}
		shopDevise.setShopInfo(shopInfo);
		shopDevise.setSaleOrg(shopInfo.getSaleOrg() != null ? shopInfo.getSaleOrg() : null);
		// 处理上样占比总和 100%
		BigDecimal solidWoodRatio = shopDevise.getSolidWoodRatio();
		BigDecimal intensifyRatio = shopDevise.getIntensifyRatio();
		BigDecimal multilayerRatio = shopDevise.getMultilayerRatio();
		BigDecimal total = solidWoodRatio.add(intensifyRatio).add(multilayerRatio);
		if (total.compareTo(new BigDecimal(100)) != 0) {
			return error("上样占比总和须为100%");
		}
		if (samplePlans != null && samplePlans.length > 0) {
			// 以双等号(==)来分隔上样计划
			String samplePlan = StringUtils.arrayToDelimitedString(samplePlans, "==");
			shopDevise.setSamplePlan(samplePlan);
		}
		if (regionalManagerOpinions != null && regionalManagerOpinions.length > 0) {
			// 省运营管理中心总经理意见以逗号分隔（接收的是整型数据）
			String regionalManagerOpinion = StringUtils.arrayToDelimitedString(regionalManagerOpinions, ",");
			shopDevise.setRegionalManagerOpinion(regionalManagerOpinion);
		}
		if (floorDivisions != null && floorDivisions.length > 0) {
			// 地板事业部渠道部审核以逗号分隔（接收的是整型数据）
			String floorDivision = StringUtils.arrayToDelimitedString(floorDivisions, ",");
			shopDevise.setFloorDivision(floorDivision);
		}
		if (shopDevise.getPredictConstructionTime() != null && shopDevise.getPredictStartsTime() != null) {
			Date a = shopDevise.getPredictConstructionTime();
			Date b = shopDevise.getPredictStartsTime();
			if (a.getTime() > b.getTime()) {
				return error("预计施工时间不能大于预计预计开业时间！");
			}
		}
		shopDeviseService.updateShopDevise(shopDevise);
		return success().addObjX(shopDevise.getId());
	}

	/**
	 * 生成报销或不报销表单
	 * 
	 * @param id
	 * @param sign
	 *            0为不报销，1为报销
	 * @param model
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/generate", method = RequestMethod.POST)
	public ResultMsg generate(Long id, Integer sign, ModelMap model) {
		ShopDevise shopDevise = shopDeviseService.find(id);
		if (shopDevise == null) {
			ExceptionUtil.throwControllerException("操作有误，请重新生成");
		}
		// model.addAttribute("sd", shopDevise);
		AcceptanceReimburse ar = new AcceptanceReimburse();
		ar.setShopDevise(shopDevise);
		ar.setShopInfo(shopDevise.getShopInfo());
		acceptanceReimburseService.saveGenerate(ar, sign);
		String str = "";
		if (sign == 0) {
			str = "生成的验收单，单号为：" + ar.getSn();
		} else if (sign == 1) {
			str = "成功生成报销表单，单号为：" + ar.getSn();
		}

		// 正式用这个
		ResultMsg success = success(str).addObjX(ar.getSn());
		return success;
	}

	// /**
	// * 审核
	// * @param shopId
	// * @return
	// */
	// @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	// public @ResponseBody
	// ResultMsg check_wf(Long id, ShopDevise shopDevise, Long objConfId) {
	// if (id == null) {
	// // 请选择订单
	// return error("请选择门店单据");
	// }
	//
	// shopDeviseService.checkShopDeviseWf(id, objConfId);
	//
	// return success();
	// }

	/**
	 * 审核
	 * 
	 * @param shopId
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
		if (id == null) {
			// 请选择订单
			return error("请选择门店单据");
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		if (storeMember.getMemberType() == 0 && !WebUtils.isAdmin()) {
			return error("经销商才能发起流程!");
		}
		shopDeviseService.createWf(id, modelId, objTypeId);

		return success();
	}

	/**
	 * 流程节点保存
	 */
	@RequestMapping(value = "/saveform", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveform(ShopDevise shopDevise, Integer[] regionalManagerOpinions,
			Integer[] floorDivisions, Integer type) {
		// type 用来定义节点
		// type 1区域经理 2省长 3渠道部 4门店设计师 5经销商确认
		shopDeviseService.saveform(shopDevise, regionalManagerOpinions, floorDivisions, type);
		return success().addObjX(shopDevise.getId());
	}

	/**
	 * 作废
	 */
	@RequestMapping(value = "/cancel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg cancel(Long id) {
		if (id == null) {
			return error("请选择单据");
		}
		ShopDevise sd = shopDeviseService.find(id);
		if (sd.getWfId()!=null){
			return error("单据已启动流程作废无效!");
		}
		if (sd.getBillsStatus() == 99) {
			return error("操作失败!单据已作废!");
		}
		shopDeviseService.cancel(id);
		return success();
	}
}
