package net.shopxx.shop.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.shop.entity.IntoOrOutStorage;
import net.shopxx.shop.entity.IntoOrOutStorageItem;
import net.shopxx.shop.service.IntoOrOutStorageService;
import net.shopxx.shop.service.SetStorageService;
import net.shopxx.shop.service.ShopStoreService;

/**
 * 采购入库
 */
@Controller("proPutInStorageController")
@RequestMapping("/shop/pro_put_in_storage")
public class ProPutInStorageController extends BaseController {

    @Resource(name = "intoOrOutStorageServiceImpl")
    private IntoOrOutStorageService intoOrOutStorageService;
    @Resource(name = "setStorageServiceImpl")
    private SetStorageService setStorageService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictBaseService;
    @Resource(name = "productCategoryBaseServiceImpl")
    private ProductCategoryBaseService productCategoryBaseService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name="shopStoreServiceImpl")
    private ShopStoreService shopStoreService;
    
    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Pageable pageable, ModelMap model) {
        return "/shop/pro_put_in_storage/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(Pageable pageable, ModelMap model) {
        return "/shop/pro_put_in_storage/list";
    }
    
    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        // 当前经销商的仓库
        List<Map<String, Object>> oStorage = setStorageService.findOwnStorage();
        model.addAttribute("oStorage", oStorage);
        
        Store store = shopStoreService.findStore();

        model.addAttribute("store", store);
        return "/shop/pro_put_in_storage/add";
    }
    
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        IntoOrOutStorage intoStorage = intoOrOutStorageService.find(id);
        model.addAttribute("is", intoStorage);
        List<Map<String, Object>> oStorage = setStorageService.findOwnStorage();
        model.addAttribute("oStorage", oStorage);
        // 当前经销商
        Store store = shopStoreService.findStore();
        model.addAttribute("store", store);
        // 采购入库明细
        List<Map<String, Object>> ioItems = intoOrOutStorageService.findProInById(id, store);
        String ioItem_json = JsonUtils.toJson(ioItems);
        model.addAttribute("ioItem_json", ioItem_json);
        return "/shop/pro_put_in_storage/edit";
    }
    
    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(String sn, Integer[] status, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(sn); // 订单编号
        param.add(2);  // type类型 采购入库
        param.add(status);
        Page<Map<String, Object>> page = intoOrOutStorageService.findProPutInPage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }
    
    /**
     * 选择采购入库的产品
     */
    @RequestMapping(value = "/select_put_product", method = RequestMethod.GET)
    public String select_put_product(String code, Integer multi, Integer type, ModelMap model) {
        model.addAttribute("code", code);
        model.addAttribute("type", type);
        model.addAttribute("multi", multi);
        return "/shop/pro_put_in_storage/select_put_product";
    }
    
    /**
     * 选择采购入库的列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/select_list_data", method = RequestMethod.POST)
    public ResultMsg select_list_data(String sn, String pName, String vonderCode, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(sn); // 发货单号
        param.add(pName); // 发货单号
        param.add(vonderCode); // 发货单号
        Page<Map<String, Object>> page = intoOrOutStorageService.findProPutInSelectPage(param, pageable);
        List<Map<String, Object>> list = page.getContent();
//        for(Map<String, Object> m : list){
//        	BigDecimal q = new BigDecimal(m.get("quantity").toString());
//        	if(q.compareTo(BigDecimal.ZERO)<=0){
//        		list.remove(0);
//        	}
//        }
        Iterator<Map<String, Object>> iterator = list.iterator();
        while (iterator.hasNext()) {
        	Map<String, Object> m = iterator.next();
        	BigDecimal q = new BigDecimal(m.get("quantity").toString());
        	if(q.compareTo(BigDecimal.ZERO)<=0){
        		iterator.remove();
        	}
        }
        
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }
    
    /**
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMsg save(IntoOrOutStorage intoOrOutStorage, Long storeId) {
        intoOrOutStorage.setStatus(0);  // 已保存状态
        intoOrOutStorage.setType(2);  // 标记为采购入库
        intoOrOutStorage.setSn(Sequence.getInstance().getSequence("CR"));
        if (intoOrOutStorage.getStorage() != null && intoOrOutStorage.getStorage().getId() != null) {
            intoOrOutStorage.setStorage(setStorageService.find(intoOrOutStorage.getStorage().getId()));
        }
        List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
        for (Iterator<IntoOrOutStorageItem> iterator = products.iterator(); iterator.hasNext();) {
            IntoOrOutStorageItem item = iterator.next();
            Product product = item.getProduct();
            if (product == null || product.getId() == null) {
                iterator.remove();
                continue;
            }
            if(item.getBranch()!=null){
            	if (BigDecimal.ZERO.compareTo(item.getBranch().divideAndRemainder(BigDecimal.ONE)[1]) != 0) {
					ExceptionUtil.throwServiceException("支数不能为小数！");
				}
            }
            item.setIntoOrOutStorage(intoOrOutStorage);
        }
        intoOrOutStorageService.saveCR(intoOrOutStorage, storeId);
        return success().addObjX(intoOrOutStorage.getId());
    }
    
    /**
     * 更新
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultMsg update(IntoOrOutStorage intoOrOutStorage, Long storeId) {
        IntoOrOutStorage inStorage = intoOrOutStorageService.find(intoOrOutStorage.getId());
        intoOrOutStorage.setType(inStorage.getType());
        intoOrOutStorage.setSn(inStorage.getSn());
        if (intoOrOutStorage.getStorage() != null && intoOrOutStorage.getStorage().getId() != null) {
            intoOrOutStorage.setStorage(setStorageService.find(intoOrOutStorage.getStorage().getId()));
        }
        if (intoOrOutStorage.getStorage() != null && intoOrOutStorage.getStorage().getId() != null) {
            intoOrOutStorage.setStorage(setStorageService.find(intoOrOutStorage.getStorage().getId()));
        }
        List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
        for (Iterator<IntoOrOutStorageItem> iterator = products.iterator(); iterator.hasNext();) {
            IntoOrOutStorageItem item = iterator.next();
            Product product = item.getProduct();
            if (product == null || product.getId() == null) {
                iterator.remove();
                continue;
            }
            if(item.getBranch()!=null){
            	if (BigDecimal.ZERO.compareTo(item.getBranch().divideAndRemainder(BigDecimal.ONE)[1]) != 0) {
					ExceptionUtil.throwServiceException("支数不能为小数！");
				}
            }
            item.setIntoOrOutStorage(intoOrOutStorage);
        }
        intoOrOutStorageService.updateCR(intoOrOutStorage, storeId);
        return success().addObjX(intoOrOutStorage.getId());
    }
    
    /**
     * 采购入库审核
     */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(IntoOrOutStorage intoOrOutStorage,Long storeId) {
		intoOrOutStorageService.check_wf(intoOrOutStorage, storeId);
		return success().addObjX(intoOrOutStorage.getId());
	}
}
