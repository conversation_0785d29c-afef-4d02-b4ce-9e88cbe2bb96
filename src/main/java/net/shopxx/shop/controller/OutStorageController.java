package net.shopxx.shop.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.shop.dao.ShopOrderDao;
import net.shopxx.shop.entity.IntoOrOutStorage;
import net.shopxx.shop.entity.IntoOrOutStorageItem;
import net.shopxx.shop.service.IntoOrOutStorageService;
import net.shopxx.shop.service.SetStorageService;
import net.shopxx.shop.service.StorageProductService;

/**
 * 出库申请 
 */
@Controller("outStorageController")
@RequestMapping("/shop/out_storage")
public class OutStorageController extends BaseController {

    @Resource(name = "intoOrOutStorageServiceImpl")
    private IntoOrOutStorageService intoOrOutStorageService;
    @Resource(name = "setStorageServiceImpl")
    private SetStorageService setStorageService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    @Resource(name = "storageProductServiceImpl")
    private StorageProductService storageProductService;
    @Resource(name = "shopOrderDao")
    private ShopOrderDao shopOrderDao;

    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Pageable pageable, ModelMap model) {
        return "/shop/out_storage/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        return "/shop/out_storage/list";
    }
    
    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        List<Map<String, Object>> oStorage = setStorageService.findOwnStorage();
        model.addAttribute("oStorage", oStorage);
        return "/shop/out_storage/add";
    }
    
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        IntoOrOutStorage outStorage = intoOrOutStorageService.find(id);
        model.addAttribute("os", outStorage);
        List<Map<String, Object>> oStorage = setStorageService.findOwnStorage();
        model.addAttribute("oStorage", oStorage);
        // 出入库明细
        List<Map<String, Object>> ioItems = intoOrOutStorageService.findIOItemById(id);
        String ioItem_json = JsonUtils.toJson(ioItems);
        model.addAttribute("ioItem_json", ioItem_json);
        //查订单sn
        List<Map<String, Object>> order = shopOrderDao.findOrder(outStorage.getOrderId());
        String sn = null;
        if(order!=null&&order.size()>0){
        	for(Map<String, Object> o : order){
        		if(o.get("ticket_sn")!=null){
        			sn = o.get("ticket_sn").toString();
        		}
        	}        	
        }
        model.addAttribute("sn", sn);
        return "/shop/out_storage/edit";
    }
    
    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(String sn,String orderSn, Integer[] status, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(sn);
        param.add(1);  // type类型 出库
        param.add(status);
        param.add(orderSn);
        Page<Map<String, Object>> page = intoOrOutStorageService.findOutPage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

    /**
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMsg save(IntoOrOutStorage intoOrOutStorage) {
        intoOrOutStorage.setStatus(0);  // 已保存状态
        intoOrOutStorage.setType(1);  // 标记为出库
        intoOrOutStorage.setSn(Sequence.getInstance().getSequence("C"));
        if (intoOrOutStorage.getStorage() != null && intoOrOutStorage.getStorage().getId() != null) {
            intoOrOutStorage.setStorage(setStorageService.find(intoOrOutStorage.getStorage().getId()));
        }
        // 处理产品项（出入库项）
        List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
        for (Iterator<IntoOrOutStorageItem> iterator = products.iterator(); iterator.hasNext();) {
            IntoOrOutStorageItem item = iterator.next();
            Product product = item.getProduct();
            if (product == null || product.getId() == null) {
                iterator.remove();
                continue;
            }
            if(item.getBranch()!=null){
            	if (BigDecimal.ZERO.compareTo(item.getBranch().divideAndRemainder(BigDecimal.ONE)[1]) != 0) {
					ExceptionUtil.throwServiceException("支数不能为小数！");
				}
            }
        }
        for (int i = 0; i < products.size(); i++) {
            IntoOrOutStorageItem item = products.get(i);
            item.setIntoOrOutStorage(intoOrOutStorage);
            item.setProduct(productBaseService.find(item.getProduct().getId()));
        }
        intoOrOutStorageService.save(intoOrOutStorage);
        return success().addObjX(intoOrOutStorage.getId());
    }
    
    /**
     * 更新
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultMsg update(IntoOrOutStorage intoOrOutStorage) {
        IntoOrOutStorage oStorage = intoOrOutStorageService.find(intoOrOutStorage.getId());
        intoOrOutStorage.setType(oStorage.getType());
        intoOrOutStorage.setSn(oStorage.getSn());
        if (intoOrOutStorage.getStorage() != null && intoOrOutStorage.getStorage().getId() != null) {
            intoOrOutStorage.setStorage(setStorageService.find(intoOrOutStorage.getStorage().getId()));
        }
        // 处理产品项（出入库项）
        List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
        for (Iterator<IntoOrOutStorageItem> iterator = products.iterator(); iterator.hasNext();) {
            IntoOrOutStorageItem item = iterator.next();
            Product product = item.getProduct();
            if (product == null || product.getId() == null) {
                iterator.remove();
                continue;
            }
            if(item.getBranch()!=null){
            	if (BigDecimal.ZERO.compareTo(item.getBranch().divideAndRemainder(BigDecimal.ONE)[1]) != 0) {
					ExceptionUtil.throwServiceException("支数不能为小数！");
				}
            }
        }
        for (int i = 0; i < products.size(); i++) {
            IntoOrOutStorageItem item = products.get(i);
            item.setIntoOrOutStorage(intoOrOutStorage);
            item.setProduct(productBaseService.find(item.getProduct().getId()));
        }
        intoOrOutStorageService.update(intoOrOutStorage);
        return success().addObjX(intoOrOutStorage.getId());
    }
    
     
    /**
     * 出库审核
     */
    @ResponseBody
    @RequestMapping(value = "/check_wf", method = RequestMethod.POST)
    public ResultMsg check_wf(Long id) {
    	IntoOrOutStorage is = intoOrOutStorageService.find(id);
    	intoOrOutStorageService.Ocheck_wf(is, 1);
    	is.setStatus(4);
        return success().addObjX(is.getId());
    }
    
}
