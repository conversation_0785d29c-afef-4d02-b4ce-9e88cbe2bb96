package net.shopxx.shop.controller;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.shop.service.StoreMemberService;

/**
 * Link四期,用户控制器
 */
@Controller("managerController")
@RequestMapping("/shop/manager")
public class ManagerController extends BaseController {

	@Resource(name = "storeMemberServiceImpl")
	private StoreMemberService storeMemberService;

	/**
	 * @param memberType 0内部用户 1外部用户
	 * @param isSalesman true只显示业务员 false或者null 显示所有用户
	 * 
	 */
	@RequestMapping(value = "/select_manager", method = RequestMethod.GET)
	public String selectManager(Integer memberType,Boolean isSalesman, Integer multi, Pageable pageable, ModelMap model) {
		model.addAttribute("multi", multi);
		model.addAttribute("memberType", memberType);
		model.addAttribute("isSalesman", isSalesman);
		return "/shop/manager/select_manager";
	}

	@RequestMapping(value = "/select_manager_data", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg selectManagerData(String username, String mobile, String name, Integer memberType,
			Boolean isSalesman,Pageable pageable) {
		Object[] args = new Object[] { username, mobile, name, memberType,isSalesman };
		Page<Map<String, Object>>  page = storeMemberService.findStoreMemberPage(pageable, args);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

}
