package net.shopxx.shop.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.AcceptanceReimburse;
import net.shopxx.shop.entity.ShopDevise;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.AcceptanceReimburseService;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopDeviseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.wf.service.WfBaseService;

/**
 * 门店装修验收及报销
 */
@Controller("acceptanceReimburseController")
@RequestMapping("/shop/acceptance_reimburse")
public class AcceptanceReimburseController extends BaseController {

	@Resource(name = "acceptanceReimburseServiceImpl")
	private AcceptanceReimburseService acceptanceReimburseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "shopDeviseServiceImpl")
	private ShopDeviseService shopDeviseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "shopAddedServiceImpl")
	private ShopAddedService shopAddedService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;

	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long objTypeId, Long objid, Pageable pageable, ModelMap model) {
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		return "/shop/acceptance_reimburse/list_tb";
	}


	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(String sn, Pageable pageable, ModelMap model) {
		model.addAttribute("wfStates", wfBaseService.getAllWfStates());
		return "/shop/acceptance_reimburse/list";
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		return "/shop/acceptance_reimburse/add";
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		shopAddedService.findPost(storeMember);
		AcceptanceReimburse acceptanceReimburse = acceptanceReimburseService.find(id);
		ShopDevise shopDevise = acceptanceReimburse.getShopDevise();
		model.addAttribute("acceptanceReimburse", acceptanceReimburse);
		model.addAttribute("shopDevise", shopDevise);
		// 验收申请承诺项
		List<String> acList = new ArrayList<String>();
		if (!StringUtils.isEmpty(acceptanceReimburse.getAcceptanceCommitment())) {
			String[] strs = acceptanceReimburse.getAcceptanceCommitment().split(",");
			acList = Arrays.asList(strs);
		}
		model.addAttribute("ac", acList);
		// 验收人员核实项
		/*
		 * List<String> avList = new ArrayList<String>(); if
		 * (!StringUtils.isEmpty(acceptanceReimburse.getAcceptanceVerify())) {
		 * String[] strs = acceptanceReimburse.getAcceptanceVerify().split(",");
		 * avList = Arrays.asList(strs); } model.addAttribute("av", avList);
		 */
		// 终端设计经理意见项
		List<String> dmList = new ArrayList<String>();
		if (!StringUtils.isEmpty(acceptanceReimburse.getDesignManager())) {
			String[] strs = acceptanceReimburse.getDesignManager().split(",");
			dmList = Arrays.asList(strs);
		}
		model.addAttribute("dm", dmList);
		// 渠道总监意见项
		List<String> doList = new ArrayList<String>();
		if (!StringUtils.isEmpty(acceptanceReimburse.getDirectorOpinion())) {
			String[] strs = acceptanceReimburse.getDirectorOpinion().split(",");
			doList = Arrays.asList(strs);
		}
		model.addAttribute("do", doList);
		// 初始化验收申请承诺附件
		List<Map<String, Object>> acceptanceCommitmentAttachs = acceptanceReimburseService
				.findAttach(acceptanceReimburse.getId(), 2);
		model.addAttribute("acceptanceCommitmentAttachs", JsonUtils.toJson(acceptanceCommitmentAttachs));
		List<Map<String, Object>> storeContractAttachs = acceptanceReimburseService
				.findAttach(acceptanceReimburse.getId(), 1);
		model.addAttribute("storeContractAttachs", JsonUtils.toJson(storeContractAttachs));
		List<Map<String, Object>> storePictureAttachs = acceptanceReimburseService
				.findAttach(acceptanceReimburse.getId(), 0);
		model.addAttribute("storePictureAttachs", JsonUtils.toJson(storePictureAttachs));
		// 门店装修验收照片附件
		List<Map<String, Object>> d1Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 11);
		List<Map<String, Object>> d2Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 12);
		List<Map<String, Object>> d3Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 13);
		List<Map<String, Object>> d4Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 14);
		List<Map<String, Object>> d5Attachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 15);
		model.addAttribute("d1Attachs", JsonUtils.toJson(d1Attachs));
		model.addAttribute("d2Attachs", JsonUtils.toJson(d2Attachs));
		model.addAttribute("d3Attachs", JsonUtils.toJson(d3Attachs));
		model.addAttribute("d4Attachs", JsonUtils.toJson(d4Attachs));
		model.addAttribute("d5Attachs", JsonUtils.toJson(d5Attachs));
		//查历史门店报销单 选第一个 ac
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("shopInfo", acceptanceReimburse.getShopInfo()));
		List<AcceptanceReimburse> ac = acceptanceReimburseService.findList(null, filters, null);
		if(ac!=null){
			if(ac.size()>0){
				model.addAttribute("acr", ac.get(0));
			}			
		}

		// model.addAttribute("post", shopAddedService.findPost(storeMember));
		// 获取流程节点
		if (acceptanceReimburse.getWfId() != null) {
			// 获取到当前流程
			ActWf wf = shopAddedService.getWfByWfId(acceptanceReimburse.getWfId());
			// 获取当前流程所在的节点
			if (wf != null) {
				// 区域经理
				Boolean qyjl = false;
				Boolean qyjls = false;
				// 省长审核
				Boolean szsh = false;
				Boolean szshs = false;
				// 渠道专员审核
				Boolean qdzy = false;
				Boolean qdzys = false;
				// 设计部审核
				Boolean sjb = false;
				Boolean sjbs = false;
				// 渠道总监审核
				Boolean qdzj = false;
				Boolean qdzjs = false;
				// 销售中心副总审核
				Boolean xszx = false;
				Boolean xszxs = false;
				// 地板事业部总裁审核
				Boolean syb = false;
				Boolean sybs = false;
				
				// 查找当前流程明细
				List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
				for (Map<String, Object> c : item) {
					if (c.get("suggestion") != null) {
						// 处理结果
						String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
						// 节点名称
						String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
						// 对比节点名称是否对应
						if (rwm.contains("区域经理")) {
							// 赋值处理结果到所定义的节点上
							qyjl = Boolean.valueOf(approved);
						}
						if (rwm.contains("省长")){
							szsh = Boolean.valueOf(approved);
							// 如果父节点驳回改变上一个节点状态
							if (!szsh) {
								qyjl = false;
							}
						}
						if (rwm.contains("渠道专员")) {
							qdzy = Boolean.valueOf(approved);
							if (!qdzy) {
								szsh = false;
							}
						}
						if (rwm.contains("设计部")) {
							sjb = Boolean.valueOf(approved);
							if (!sjb){
								qdzy = false;
							}
						}
						if (rwm.contains("渠道总监")) {
							qdzj = Boolean.valueOf(approved);
							if(!qdzy){
								sjb = false;
							}
						}
						if (rwm.contains("销售中心")){
							xszx = Boolean.valueOf(approved);
							if(!xszx){
								qdzj = false;
							}
						}
						if (rwm.contains("事业部")) {
							syb = Boolean.valueOf(approved);
							if(!syb){
								xszx = false;
							}
						}
					}
				}
				// 获取当前流程所在的节点
				Task t = shopAddedService.getCurrTaskByWf(wf);
				if (t != null) {
					// 获取当前节点所有用户id
					List<String> userId = actWfService.getTaskUsers(t.getId());
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("区域经理")) {
						qyjls = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("省长")) {
						qyjls = true;
						szshs = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道专员")) {
						qyjls = true;
						szshs = true;
						qdzys = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("设计部")) {
						qyjls = true;
						szshs = true;
						qdzys = true;
						sjbs = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道总监")) {
						qyjls = true;
						szshs = true;
						qdzys = true;
						sjbs = true;
						qdzjs = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("销售中心")) {
						qyjls = true;
						szshs = true;
						qdzys = true;
						sjbs = true;
						qdzjs = true;
						xszxs = true;
					}
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("事业部")) {
						qyjls = true;
						szshs = true;
						qdzys = true;
						sjbs = true;
						qdzjs = true;
						xszxs = true;
						sybs = true;
					}
				}
				model.addAttribute("wf", wf);
				model.addAttribute("node", t);
				model.addAttribute("qyjl", qyjl);
				model.addAttribute("qyjls",qyjls );
				model.addAttribute("szsh", szsh);
				model.addAttribute("szshs", szshs);
				model.addAttribute("qdzy", qdzy);
				model.addAttribute("qdzys", qdzys);
				model.addAttribute("sjb", sjb);
				model.addAttribute("sjbs", sjbs);
				model.addAttribute("qdzj", qdzj);
				model.addAttribute("qdzjs", qdzjs);
				model.addAttribute("xszx", xszx);
				model.addAttribute("xszxs", xszxs);
				model.addAttribute("syb", syb);
				model.addAttribute("sybs", sybs);
			}
		}
		return "/shop/acceptance_reimburse/edit";
	}

	/**
	 * 列表数据
	 */
	@ResponseBody
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public ResultMsg list_data(String sn, String deviseSn, Integer[] status, Pageable pageable) {
		List<Object> param = new ArrayList<Object>();
		param.add(1);
		param.add(sn);
		param.add(status);
		param.add(deviseSn);
		Page<Map<String, Object>> page = acceptanceReimburseService.findPage(param, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 保存
	 */
	@ResponseBody
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public ResultMsg save(AcceptanceReimburse acceptanceReimburse, Long shopInfoId, Long shopDeviseId,
			Integer[] acceptanceCommitments, Integer[] acceptanceVerifys, Integer[] designManagers,
			Integer[] directorOpinions) {
		ShopInfo shopInfo = shopInfoService.find(shopInfoId);
		if (shopInfo == null) {
			return error("门店选择有误，请重新选择！");
		}
		acceptanceReimburse.setShopInfo(shopInfo);
		acceptanceReimburse.setSaleOrg(shopInfo.getSaleOrg() != null ? shopInfo.getSaleOrg() : null);
		ShopDevise shopDevise = shopDeviseService.find(shopDeviseId);
		if (shopDevise == null) {
			return error("门店设计选择有误，请重新选择！");
		}
		acceptanceReimburse.setShopDevise(shopDevise);
		// 验收申请承诺
		if (acceptanceCommitments != null && acceptanceCommitments.length > 0) {
			String acceptanceCommitment = StringUtils.arrayToDelimitedString(acceptanceCommitments, ",");
			acceptanceReimburse.setAcceptanceCommitment(acceptanceCommitment);
		}
		// 验收人员核实
		/*
		 * if (acceptanceVerifys != null && acceptanceVerifys.length > 0) {
		 * String acceptanceVerify =
		 * StringUtils.arrayToDelimitedString(acceptanceVerifys, ",");
		 * acceptanceReimburse.setAcceptanceVerify(acceptanceVerify); }
		 */
		// 终端设计经理意见
		if (designManagers != null && designManagers.length > 0) {
			String designManager = StringUtils.arrayToDelimitedString(designManagers, ",");
			acceptanceReimburse.setDesignManager(designManager);
		}
		// 渠道总监意见
		if (directorOpinions != null && directorOpinions.length > 0) {
			String directorOpinion = StringUtils.arrayToDelimitedString(directorOpinions, ",");
			acceptanceReimburse.setDirectorOpinion(directorOpinion);
		}
		// 保存状态
		acceptanceReimburse.setStatus(0);
		acceptanceReimburseService.saveAcceptanceReimburse(acceptanceReimburse);
		return success().addObjX(acceptanceReimburse.getId());
	}

	/**
	 * 更新
	 */
	@ResponseBody
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public ResultMsg update(AcceptanceReimburse acceptanceReimburse, Long shopInfoId, Long shopDeviseId,
			Integer[] acceptanceCommitments, Integer[] acceptanceVerifys, Integer[] designManagers,
			Integer[] directorOpinions) {
		ShopInfo shopInfo = shopInfoService.find(shopInfoId);
		if (shopInfo == null) {
			return error("门店选择有误，请重新选择！");
		}
		acceptanceReimburse.setShopInfo(shopInfo);
		acceptanceReimburse.setSaleOrg(shopInfo.getSaleOrg() != null ? shopInfo.getSaleOrg() : null);
		ShopDevise shopDevise = shopDeviseService.find(shopDeviseId);
		if (shopDevise == null) {
			return error("门店设计选择有误，请重新选择！");
		}
		acceptanceReimburse.setShopDevise(shopDevise);
		// 验收申请承诺
		if (acceptanceCommitments != null && acceptanceCommitments.length > 0) {
			String acceptanceCommitment = StringUtils.arrayToDelimitedString(acceptanceCommitments, ",");
			acceptanceReimburse.setAcceptanceCommitment(acceptanceCommitment);
		}
		// 验收人员核实
		/*
		 * if (acceptanceVerifys != null && acceptanceVerifys.length > 0) {
		 * String acceptanceVerify =
		 * StringUtils.arrayToDelimitedString(acceptanceVerifys, ",");
		 * acceptanceReimburse.setAcceptanceVerify(acceptanceVerify); }
		 */
		// 终端设计经理意见
		if (designManagers != null && designManagers.length > 0) {
			String designManager = StringUtils.arrayToDelimitedString(designManagers, ",");
			acceptanceReimburse.setDesignManager(designManager);
		}
		// 渠道总监意见
		if (directorOpinions != null && directorOpinions.length > 0) {
			String directorOpinion = StringUtils.arrayToDelimitedString(directorOpinions, ",");
			acceptanceReimburse.setDirectorOpinion(directorOpinion);
		}
		acceptanceReimburseService.updateAcceptanceReimburse(acceptanceReimburse);
		return success().addObjX(acceptanceReimburse.getId());
	}

	/**
	 * 审核
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
		if (id == null) {
			// 请选择订单
			return error("请选择门店单据");
		}

		acceptanceReimburseService.createWf(id, modelId, objTypeId);

		return success();
	}

	/**
	 * 流程节点保存
	 */
	@RequestMapping(value = "/saveform1", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveform1(AcceptanceReimburse acceptanceReimburse, Integer[] designManagers,
			Integer[] directorOpinions, Integer type) {
		// type 1区域经理 2省长 3设计部 4渠道部总监 5事业部总裁
		acceptanceReimburseService.saveform1(acceptanceReimburse, designManagers, directorOpinions, type);
		return success().addObjX(acceptanceReimburse.getId());
	}
	
	@RequestMapping(value = "/find_ac", method = RequestMethod.POST)
	public @ResponseBody ResultMsg find_ac(Long shopInfoId) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("shopInfo", shopInfoService.find(shopInfoId)));
		List<AcceptanceReimburse> ac = acceptanceReimburseService.findList(null, filters, null);
		String s = "";
		if(ac!=null){
			if(ac.size()>0){
				s = JsonUtils.toJson(acceptanceReimburseService.findAcceptanceReimburse(shopInfoId));
			}			
		}
		return success(s);
	}
	
	@RequestMapping(value = "/find_ac_at", method = RequestMethod.POST)
	public @ResponseBody ResultMsg find_ac_at(Long acceptanceReimburseId) {
		List<Map<String, Object>> mp = acceptanceReimburseService.findAttach(acceptanceReimburseId, new Integer[]{0,1,2});
		return success(JsonUtils.toJson(mp));
	}
	
}
