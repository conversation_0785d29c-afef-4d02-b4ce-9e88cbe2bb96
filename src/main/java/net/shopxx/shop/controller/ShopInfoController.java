package net.shopxx.shop.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.shopxx.shop.entity.ShopDecrease;
import net.shopxx.shop.service.ShopDecreaseService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.VisualReport.Type;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.VisualReportService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.wf.service.WfObjConfigBaseService;

/**
 * Controller - 门店
 */
@Controller("shopMemberShopInfoController")
@RequestMapping("/shop/shopInfo")
public class ShopInfoController extends BaseController {

	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "productCategoryBaseServiceImpl")
	private ProductCategoryBaseService productCategoryService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "wfObjConfigBaseServiceImpl")
	private WfObjConfigBaseService wfObjConfigBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	@Resource(name = "visualReportServiceImpl")
	private VisualReportService visualReportService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;

	@Resource(name = "shopDecreaseServiceImpl")
	private ShopDecreaseService shopDecreaseService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model) {
		return "/shop/shopInfo/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		List<Map<String, Object>> content = shopInfoService.findListMap();
		Integer totalQuantity = 0;
		Integer shop_status = 0;
		Integer devise_status = 0;
		Integer decoration_status = 0;
		Integer open_shop = 0;
		Integer business = 0;
		Integer stop = 0;
		Integer close = 0;
		Integer not_design = 0;
		Integer design = 0;
		Integer has_been_designed = 0;
		Integer no_design = 0;
		Integer not_decoration = 0;
		Integer decoration = 0;
		Integer has_been_decoration = 0;
		Integer approved = 0;
		for (Map<String, Object> d : content) {
			totalQuantity++;
			String a = d.get("shop_status") == null ? "" : d.get("shop_status").toString();
			String b = d.get("devise_status") == null ? "" : d.get("devise_status").toString();
			String c = d.get("decoration_status") == null ? "" : d.get("decoration_status").toString();
			if (compare(a, "开店中") || compare(a, "正在营业") || compare(a, "停业整顿") || compare(a, "关闭")) {
				if (compare(a, "开店中"))
					open_shop++;
				if (compare(a, "正在营业"))
					business++;
				if (compare(a, "停业整顿"))
					stop++;
				if (compare(a, "关闭"))
					close++;
				shop_status++;
			}
			if (compare(b, "未设计") || compare(b, "设计中") || compare(b, "已设计") || compare(b, "无需设计")) {
				if (compare(b, "未设计"))
					not_design++;
				if (compare(b, "设计中"))
					design++;
				if (compare(b, "已设计"))
					has_been_designed++;
				if (compare(b, "无需设计"))
					no_design++;
				devise_status++;
			}
			if (compare(c, "未装修") || compare(c, "装修中") || compare(c, "已装修") || compare(c, "已验收")) {
				if (compare(c, "未装修"))
					not_decoration++;
				if (compare(c, "装修中")) {
					decoration++;
				}
				if (compare(c, "已装修"))
					has_been_decoration++;
				if (compare(c, "已验收"))
					approved++;
				decoration_status++;
			}
		}
		model.addAttribute("open_shop", open_shop);
		model.addAttribute("business", business);
		model.addAttribute("stop", stop);
		model.addAttribute("close", close);
		model.addAttribute("not_design", not_design);
		model.addAttribute("design", design);
		model.addAttribute("has_been_designed", has_been_designed);
		model.addAttribute("no_design", no_design);
		model.addAttribute("not_decoration", not_decoration);
		model.addAttribute("decoration_cs", decoration);
		model.addAttribute("has_been_decoration", has_been_decoration);
		model.addAttribute("approved", approved);
		model.addAttribute("total", totalQuantity);
		model.addAttribute("shop_status", shop_status);
		model.addAttribute("devise", devise_status);
		model.addAttribute("decoration", decoration_status);
		return "/shop/shopInfo/list";
	}

	protected Boolean compare(String a, String b) {
		if (a.equals("")) {
			return false;
		}
		Boolean c = false;
		if (b.equals(a)) {
			c = true;
		}
		return c;
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/select_shopInfo", method = RequestMethod.GET)
	public String select_shopInfo(Integer multi, String[] shopStatus, Integer reimburse, Boolean off, ModelMap model) {
		model.addAttribute("multi", multi);
		model.addAttribute("shopStatus", splicer(shopStatus));
		model.addAttribute("reimburse", reimburse);
		model.addAttribute("off", off);
		return "/shop/shopInfo/select_shopInfo";
	}

	public String splicer(String[] str) {
		String s = "";
		if (str != null && str.length > 0) {
			for (int i = 0; i < str.length; i++) {
				if (str.length - 1 == i) {
					s += str[i];
				} else {
					s += str[i] + ",";
				}
			}
		}
		return s;
	}

	/**
	 * 列表数据 - 选择门店的列表数据
	 */
	@RequestMapping(value = "/select_shopInfo_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg selectShopInfoData(String sn, String distributorName, String address,
			String authorizationCode, String shopStatus, Integer reimburse, String transitTime, Pageable pageable,
			Boolean off,String shopName, ModelMap model) {
		String[] a = null;
		Boolean is = false;
		if (off != null) {
			is = off;
		}
		if (shopStatus != null) {
			a = shopStatus.split(",");
		}

		Page<Map<String, Object>> page = shopInfoService.findSelectPage(sn, distributorName, address, authorizationCode,
				a, reimburse, transitTime,shopName, pageable);
		if (is) {
			// 获取查询内容
			List<Map<String, Object>> c = page.getContent();
			// 遍历查询内容
			for (Map<String, Object> m : c) {
				// 判断门店id不为空
				if (m.get("id") != null) {
					// 门店id
					String shopInfoId = m.get("id").toString();
					String reimburses = m.get("reimburse").toString();
					// 通过门店id查询门店设计
					List<Map<String, Object>> devises = shopInfoService.findDevise(shopInfoId, reimburses);
					// 判断查询结果不等于空并且大小大于o
					if (devises != null && devises.size() > 0) {
						// 取集合第一个门店设计单
						Map<String, Object> devise = devises.get(0);
						// 获取门店设计单号
						if (devise.get("sn") != null) {
							String devise_sn = devise.get("sn").toString();
							m.put("devise_sn", devise_sn);
						}
						// 获取门店设计id
						if (devise.get("id") != null) {
							String devise_id = devise.get("id").toString();
							m.put("devise_id", devise_id);
						}
						if (devise.get("structure1") != null) {
							String structure1 = devise.get("structure1").toString();
							m.put("structure1", structure1);
						}
						if (devise.get("structure2") != null) {
							String structure2 = devise.get("structure2").toString();
							m.put("structure2", structure2);
						}
						if (devise.get("shop_renovation_attribute") != null) {
							String shop_renovation_attribute = devise.get("shop_renovation_attribute").toString();
							m.put("shop_renovation_attribute", shop_renovation_attribute);
						}
						if (devise.get("high_limit") != null) {
							String high_limit = devise.get("high_limit").toString();
							m.put("high_limit", high_limit);
						}
						if (devise.get("design_brand") != null) {
							String design_brand = devise.get("design_brand").toString();
							m.put("design_brand", design_brand);
						}
						if (devise.get("area") != null) {
							String area = devise.get("area").toString();
							m.put("area", area);
						}
						if (devise.get("predict_construction_time") != null) {
							String predict_construction_time = devise.get("predict_construction_time").toString();
							m.put("predict_construction_time", predict_construction_time);
						}
						if (devise.get("predict_starts_time") != null) {
							String predict_starts_time = devise.get("predict_starts_time").toString();
							m.put("predict_starts_time", predict_starts_time);
						}
					}
				}
			}
		}

		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String sn, String distributorName, String address,
			String authorizationCode, String shopStatus, String deviseStatus, String decorationStatus,
			Pageable pageable) {

		Page<Map<String, Object>> page = shopInfoService.findPage(sn, distributorName, address, authorizationCode,
				shopStatus, deviseStatus, decorationStatus, pageable);
		String jsonPage = JsonUtils.toJson(page);

		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "activationType"));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> activationType = systemDictService.findList(null, filters, null);
		filters.clear();
		filters.add(Filter.eq("code", "businessCategory"));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
		model.addAttribute("BusinessCategory", businessCategoryList);
		model.addAttribute("activationType", activationType);
		return "/shop/shopInfo/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(ShopInfo shopInfo, Long storeId) {
		if (shopInfo.getInclusiveBrand() == null) {
			return error("请选择所含品牌");
		}
		Store store = storeBaseService.find(storeId);
		shopInfo.setStore(store);
		shopInfoService.saveShopInfo(shopInfo);
		return success().addObjX(shopInfo.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		ShopInfo shopInfo = shopInfoService.find(id);
		// 授权编码
		String authorizationCode1 = null;
		List<Map<String, Object>> alist = shopInfoService.findListBy(id);
		for (Map<String, Object> m : alist) {
			if (m.get("authorization_code") != null) {
				authorizationCode1 = m.get("authorization_code").toString();
			} else if (m.get("shop_authorization_codes") != null) {
				authorizationCode1 = m.get("shop_authorization_codes").toString();
			}
		}
		if (authorizationCode1 != null) {
			shopInfo.setAuthorizationCode(authorizationCode1);
		}
		List<Filter> filters = new ArrayList<Filter>();
		 //获取经营品类
		 List<Map<String, Object>> productType =
		 shopInfoService.findBusinessCategoryApply(shopInfo);
		 model.addAttribute("productType", JsonUtils.toJson(productType));

		 //获取门店稽查
		 List<Map<String, Object>> shopInspections = shopInfoService.findShopInspection(shopInfo);
		 model.addAttribute("shopInspections",
		 JsonUtils.toJson(shopInspections));

		// 获取门店设计
		List<Map<String, Object>> shopDesigns = shopInfoService.findShopDesign(shopInfo);
		model.addAttribute("shop_devises", JsonUtils.toJson(shopDesigns));

		// 获取门店装修验收
		List<Map<String, Object>> acceptanceReimburses = shopInfoService.findShopAcceptance(shopInfo);
		model.addAttribute("acceptance_reimburses", JsonUtils.toJson(acceptanceReimburses));

		// 获取停业整顿
		List<Map<String, Object>> restructurings = shopInfoService.findRestructuring(shopInfo);
		model.addAttribute("restructurings", JsonUtils.toJson(restructurings));

		//获取门店减少
		filters.clear();
		filters.add(Filter.eq("companyInfoId",  companyInfoBaseService.getCurrent().getId()));
		filters.add(Filter.eq("shopInfo",id));
		filters.add(Filter.isNotNull("wfId"));
		List<ShopDecrease> shopDecreaseList = shopDecreaseService.findList(null, filters, null);
		if(!shopDecreaseList.isEmpty()){
//			model.addAttribute("shopDecrease",shopDecreaseList.get(shopDecreaseList.size()-1));
			model.addAttribute("shopDecrease",shopDecreaseList.get(0));
		}

		model.addAttribute("shopInfo", shopInfo);
		filters.clear();
		filters.add(Filter.eq("code", "businessCategory"));
		filters.add(Filter.eq("companyInfoId",  companyInfoBaseService.getCurrent().getId()));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessCategoryList = systemDictService.findList(null, filters, null);
		model.addAttribute("BusinessCategory", businessCategoryList);
		filters.clear();
		filters.add(Filter.eq("code", "shopSign"));
		filters.add(Filter.eq("companyInfoId",  companyInfoBaseService.getCurrent().getId()));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> shopSignList = systemDictService.findList(null, filters, null);
		model.addAttribute("shopSign", shopSignList);
		model.addAttribute("visualReport",
				visualReportService.findVisualReports(storeMemberBaseService.getCurrent().getId(), Type.shop));
		String shopInfoFullLink_json = JsonUtils
				.toJson(orderFullLinkService.findListByElseSnAndType(shopInfo.getSn(),102));
		model.addAttribute("shopInfoFullLink_json", shopInfoFullLink_json);
		return "/shop/shopInfo/edit";
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(ShopInfo shopInfo) {
		shopInfoService.updateShopInfo(shopInfo);
		return success();
	}

	/**
	 * excel导入门店资料
	 * 
	 * @param file
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/import_excel", method = RequestMethod.POST)
	public @ResponseBody ResultMsg importFromExcel(MultipartFile file, HttpServletResponse response) throws Exception {

		try {
			shopInfoService.shopInfoImport(file);
			return ResultMsg.success();
		} catch (Exception e) {
			LogUtils.error("导入门店", e);
			return ResultMsg.error(e.getMessage());
		}

	}

	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(String sn, String distributorName,
			Pageable pageable, ModelMap model) {

		Integer size = shopInfoService.count(sn, distributorName, pageable, null, null);
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}

	/**
	 * 条件导出
	 * 
	 * @param sn
	 * @param distributorName
	 * @param pageable
	 * @param model
	 * @param page
	 * @return
	 */
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(String sn, String distributorName, Pageable pageable, ModelMap model,
			Integer page) {

		Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = shopInfoService.findItemList(sn, distributorName, null, page, size);
		return this.getModelAndView(data, model);

	}

	/**
	 * 选择导出
	 * 
	 * @param ids
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/selected_export", method = RequestMethod.GET)
	public ModelAndView selectedExport(Long[] ids, ModelMap model) {
		List<Map<String, Object>> data = shopInfoService.findItemList(null, null, ids, null, null);
		return this.getModelAndView(data, model);
	}

	public ModelAndView getModelAndView(List<Map<String, Object>> data, ModelMap model) {
		for (Map<String, Object> str : data) {
			if (str.get("wf_state") != null) {// 流程状态
												// 0未启动，1审核中，2已完成，3驳回,4中断,5禁用（删除了单据）
				Integer wf_state = Integer.valueOf(str.get("wf_state").toString());
				if (wf_state == 1) {
					str.put("wf_state", "审核中");
				} else if (wf_state == 2) {
					str.put("wf_state", "已完成");
				} else if (wf_state == 3) {
					str.put("wf_state", "驳回");
				} else if (wf_state == 4) {
					str.put("wf_state", "中断");
				} else if (wf_state == 0) {
					str.put("wf_state", "未启动");
				}
			}
			if (str.get("create_date") != null) {
				String create_date = DateUtil.convert((Date) str.get("create_date"));
				if (create_date.length() > 19) {
					create_date = create_date.substring(0, 19);
				}
				str.put("create_date", create_date);
			}
			if (str.get("open_date") != null) {

				String cancel_date = DateUtil.convert((Date) str.get("open_date"));
				if (cancel_date.length() > 19) {
					cancel_date = cancel_date.substring(0, 19);
				}
				str.put("open_date", cancel_date);
			}
			if (str.get("join_date") != null) {
				String join_date = DateUtil.convert((Date) str.get("join_date"));
				if (join_date.length() > 19) {
					join_date = join_date.substring(0, 19);
				}
				str.put("join_date", join_date);
			}
			if (str.get("last_check_time") != null) {
				String last_check_time = DateUtil.convert((Date) str.get("last_check_time"));
				if (last_check_time.length() > 19) {
					last_check_time = last_check_time.substring(0, 19);
				}
				str.put("last_check_time", last_check_time);
			}
			if (str.get("closing_time") != null) {

				String closing_time = DateUtil.convert((Date) str.get("closing_time"));
				if (closing_time.length() > 19) {
					closing_time = closing_time.substring(0, 19);
				}
				str.put("closing_time", closing_time);
			}
			if (str.get("activation_time") != null) {
				String activation_time = DateUtil.convert((Date) str.get("activation_time"));
				if (activation_time.length() > 19) {
					activation_time = activation_time.substring(0, 19);
				}
				str.put("activation_time", activation_time);
			}
			String areaRegionName = "";
			String areaCityName = "";
			String areaProvinceName = "";
			Area area = null;
			if (str.get("area_Id") != null) {
				Long areaId = Long.valueOf(str.get("area_Id").toString());
				area = areaBaseService.find(areaId);
			}
			if (str.get("area_tree_path") != null && area != null) {
				String area_tree_path = str.get("area_tree_path").toString();
				Integer num = (area_tree_path.length() - area_tree_path.replace(",", "").length()) / ",".length();// ,数量
				if (num == 1) {// 省
					str.put("areaRegionName", areaRegionName);
					str.put("areaCityName", areaCityName);
					str.put("areaProvinceName", area.getName() == null ? "" : area.getName());
				} else if (num == 2) {// 省市
					str.put("areaRegionName", areaRegionName);
					str.put("areaCityName", area.getName() == null ? "" : area.getName());
					str.put("areaProvinceName", area.getParent().getName() == null ? "" : area.getParent().getName());
				} else if (num == 3) {// 省市区
					str.put("areaRegionName", area.getName() == null ? "" : area.getName());
					str.put("areaCityName", area.getParent().getName() == null ? "" : area.getParent().getName());
					str.put("areaProvinceName", area.getParent().getParent().getName() == null ? ""
							: area.getParent().getParent().getName());
				}
			} else {// 不填
				str.put("areaRegionName", areaRegionName);
				str.put("areaCityName", areaCityName);
				str.put("areaProvinceName", areaProvinceName);
			}
		}

		// 设置导出表格名
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		// 设置标题
		String[] header = { "授权编码", "客户编码", "区域", "省份", "地级城市", "区县城市", "乡镇城市", "总经销商", "经销商姓名", "加盟日期", "经销商手机号码",
				"城市行政等级", "建店日期", "面积", "详细地址", "门店状态", "门店状态备注", "位置类型", "门店类别", "最后稽查结果", "新增档案编号", "减少档案编号",
				"门店关闭原因", "门店关闭时间", "VI版本", "业态", "招牌", "所属销售平台", "所属部门", "所含品牌", "所属品牌", "销售渠道", "销售渠道备注", "门店销量",
				"备注", "最后设计日期", "最后设计状态", "激活时间", "创建时间", "流程状态", "审核时间", "审核人" };
		// 设置单元格取值
		String[] properties = { "authorization_code", // 授权编码
				"out_trade_no", // 客户编码
				"region", // 区域
				"areaProvinceName", // 省份
				"areaCityName", // 地级城市
				"areaRegionName", // 区县城市
				"town", // 乡镇城市
				"franchisee", // 总经销
				"distributor_name", // 经销商姓名
				"join_date", // 加盟日期
				"distributor_phone", // 经销商手机号码 10
				"administrative_rank", // 城市行政等级
				"open_date", // 建店日期
				"acreage", // 面积
				"address", // 详细地址
				"status", // 门店状态
				"status_note", // 门店状态备注
				"position_type", // 位置类别
				"type", // 门店类别
				"last_inspection_result", // 最后稽查结果
				"increase_archives_code", // 新增档案编号
				"decrease_archives_code", // 减少档案编号
				"shut_down_menu", // 门店关闭原因
				"closing_time", // 门店关闭时间
				"vi_version", // VI版本
				"business_form", // 业态
				"shop_sign", // 招牌
				"sales_platform", // 所属销售平台
				"department", // 所属部门
				"inclusive_brand", // 所含品牌
				"belong_brand", "sales_channel", // 销售渠道
				"sales_channel_note", // 销售渠道备注
				"sales_volume", "memo", //
				"last_design_date", //
				"last_design_status", //
				"activation_time", //
				"create_date", //
				"wf_state", //
				"check_date", //
				"check_store_member_name" };
		Integer[] widths = { 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256, 25 * 256,
				25 * 256, 25 * 256, 25 * 256 };

		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}

	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 10000);
		}
		return map;
	}

	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, Long objConfId, ModelMap model) {

		ShopInfo shopInfo = shopInfoService.find(id);
		if (shopInfo.getWfId() != null) {
			return error("单据流程已启动");
		}
		shopInfoService.checkWf(id, objConfId);
		return success();
	}

	@RequestMapping(value = "/flush", method = RequestMethod.POST)
	public @ResponseBody ResultMsg flush(Long id, String longitude, String latitude) {
		ShopInfo shopInfo = shopInfoService.find(id);
		if (shopInfo == null) {
			return error("单据不存在！");
		}
		if (shopInfo.getLongitude() != null && shopInfo.getLatitude() != null) {
			if (longitude.equals(shopInfo.getLongitude()) && latitude.equals(shopInfo.getLatitude())) {
				return error("经纬度相同无需同步！");
			}
		}
		shopInfo.setLongitude(longitude);
		shopInfo.setLatitude(latitude);
		shopInfoService.update(shopInfo);
		shopInfoService.updateShop(shopInfo);
		return success();
	}

	@RequestMapping(value = "/flush_images", method = RequestMethod.POST)
	public @ResponseBody ResultMsg flushImages(ShopInfo shopInfo) {
		shopInfoService.updetaShopImages(shopInfo);
		return success();
	}

	// 转换门店状态:开店中
	@RequestMapping(value = "/shop_open", method = RequestMethod.POST)
	public @ResponseBody ResultMsg shop_open(Long[] ids,String status, ModelMap model) {
		Set<Map<String,String>> statusList = new HashSet<Map<String,String>>();
		Map<String,String> mp = new HashMap<String, String>();
		mp.put("A0", "开店中");
		mp.put("A1", "正在营业");
		mp.put("A2", "停业整顿");
		mp.put("A3", "关闭");
		mp.put("B0", "未设计");
		mp.put("B1", "设计中");
		mp.put("B2", "已设计");
		mp.put("B3", "无需设计");
		mp.put("C0", "未装修");
		mp.put("C1", "装修中");
		mp.put("C2", "已装修");
		mp.put("C3", "已验收");
		statusList.add(mp);
		
		String i = null;
		for(Map<String,String> s : statusList){
			i = s.get(status);
		}
		if(i!=null){
			String s = status.substring(0,1);
			for(Long id : ids){
				ShopInfo si = shopInfoService.find(id);
				if("A".equals(s)){
					si.setShopStatus(i);
				}
				if("B".equals(s)){
					si.setDeviseStatus(i);
				}
				if("C".equals(s)){
					si.setDecorationStatus(i);
				}
				shopInfoService.update(si);
			}
		}
		return success();
	}

	
}