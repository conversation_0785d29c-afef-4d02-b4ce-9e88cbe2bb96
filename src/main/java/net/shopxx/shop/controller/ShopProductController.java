package net.shopxx.shop.controller;

import jxl.read.biff.BiffException;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrgPost;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;
import net.shopxx.member.service.StoreMemberShopInfoService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.entity.ShopProductAndProductCategory;
import net.shopxx.shop.entity.ShopProductCategory;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.shop.service.ShopProductAndProductCategoryService;
import net.shopxx.shop.service.ShopProductCategoryService;
import net.shopxx.shop.service.ShopProductService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Controller - PC门店商品
 */
@Controller("shopProductController")
@RequestMapping("/shop/product")
public class ShopProductController extends BaseController {

    @Resource(name = "productCategoryBaseServiceImpl")
    private ProductCategoryBaseService productCategoryBaseService;
    @Resource(name = "shopProductServiceImpl")
    private ShopProductService shopProductService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeMemberShopInfoServiceImpl")
    private StoreMemberShopInfoService storeMemberShopInfoService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    @Resource(name = "shopProductCategoryServiceImpl")
    private ShopProductCategoryService shopProductCategoryService;
    @Resource(name = "shopProductAndProductCategoryServiceImpl")
    private ShopProductAndProductCategoryService shopProductAndProductCategoryService;
    @Resource(name = "storeMemberSaleOrgPostServiceImpl")
    private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
    @Resource(name = "systemDictBaseServiceImpl")
    private SystemDictBaseService systemDictService;

    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(ModelMap model) {
        return "/shop/product/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(ModelMap model, Long shopId) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        List<Filter> filters = new ArrayList<Filter>();
        ShopInfo shopInfo = shopInfoService.find(shopId);
        model.addAttribute("shopInfo", shopInfo);

        if (shopId != null) {

            //Map shop_info = shopInfoService.findById(shopId);
            Map shop_info = shopInfoService.findById(shopId);
            model.addAttribute("shop_info", shop_info);

            //查询省长
            SaleOrg saleOrg = shopInfo.getSaleOrg();
            //地板中心区域总监id=19
            Long postId = 19L;
            filters.clear();
            filters.add(Filter.eq("post", postId));
            filters.add(Filter.eq("saleOrg", saleOrg));
            filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
            List<StoreMemberSaleOrgPost> list = storeMemberSaleOrgPostService.findList(null, filters, null);
            if (list != null && list.size() > 0) {
                StoreMemberSaleOrgPost storeMemberSaleOrgPost = list.get(0);
                StoreMember province = storeMemberSaleOrgPost.getStoreMember();
                model.addAttribute("province", province);
            }


            //大区经理为区域经理上级
            if (shopInfo.getStore() != null && shopInfo.getStore().getStoreMember() != null
                    && shopInfo.getStore().getStoreMember().getParent() != null) {
                StoreMember parent = shopInfo.getStore().getStoreMember().getParent();
                model.addAttribute("parentName", parent.getName());
            }


        }


        //门店上样分类相关
        List<Object> shopProductCategories = new ArrayList<Object>();//门店上样分类列表

        Long currentCompanyInfoId = WebUtils.getCurrentCompanyInfoId();
        filters.clear();
        filters.add(Filter.eq("isEnabled", true));
        filters.add(Filter.eq("companyInfoId", currentCompanyInfoId));
        List<ShopProductCategory> shopProductCategoryList = shopProductCategoryService.findList(null, filters, null);

        //全部产品数量
        Integer allCount = shopProductService.findListCount(null, null, null, null);


        //某门店已上样商品总数
        Integer isShopAllCount = shopProductService.findListCount(null, null, shopId, true);

        for (ShopProductCategory shopProductCategory : shopProductCategoryList) {
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("id", shopProductCategory.getId());
            data.put("shopProductCategoryId", shopProductCategory.getId());
            data.put("isTop", true);
            data.put("name", shopProductCategory.getName());
            data.put("open", "true");//展开
            data.put("isParent", true);
            //某分类的总数
            Integer oneCount = shopProductService.findListCount(shopProductCategory.getId(), null, null, null);
            data.put("oneCount", oneCount);

            //某门店某分类的已上样商品总数
            Integer isShopCount = shopProductService.findListCount(shopProductCategory.getId(), null, shopId, true);
            data.put("isShopCount", isShopCount);

            data.put("allCount", allCount);//上一级的总数
            shopProductCategories.add(data);

        }

        model.addAttribute("shopId", shopId);
        model.addAttribute("allCount", allCount);//全部产品
        model.addAttribute("isShopAllCount", isShopAllCount);
        model.addAttribute("shopProductCategory", JsonUtils.toJson(shopProductCategories));

        return "/shop/product/list";
    }

    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_data(Long shopProductCategoryId,
                        Long productCategoryId,
                        String vonderCode,
                        String mod,
                        String name,
                        Long shopId,
                        Boolean isReferrer,
                        Boolean isMarketable,
                        Boolean isShow,
                        Pageable pageable, ModelMap model) {

        Page<Map<String, Object>> page = shopProductService.findPage(shopProductCategoryId, productCategoryId, vonderCode, mod,
                name,null , isReferrer, isMarketable, isShow, pageable);

        //没选择门店不允许isReferrer、isShop、isShow查询
//        if (shopId == null) {
//            isReferrer = null;
//            isShop = null;
//            isShow = null;
//             page = shopProductService.findPage(shopProductCategoryId, productCategoryId, vonderCode, mod,
//                    name, isMarketable, null,isReferrer,isShop,isShow, pageable);
//        }else {
//             page = shopProductService.findPage(shopProductCategoryId, productCategoryId, vonderCode, mod,
//                    name, isMarketable, shopId,isReferrer,isShop,isShow, pageable);
//        }
//
//        if(page.getContent() == null){
//            String jsonPage = JsonUtils.toJson(page);
//            return success(jsonPage);
//        }

        List<Map<String, Object>> c = page.getContent();


        //首次进入门店上样，没选门店的情况不查询门店商品上样
        if (shopId != null) {
            List<Map<String, Object>> shopProduct = shopProductService.findShopProduct(shopId);
            //Set<Map<String, Object>> maps = new HashSet<Map<String, Object>>(shopProduct);

            for (Map<String, Object> sp : shopProduct) {
                if (sp.get("id") != null) {
                    String ids = sp.get("id").toString();
                    for (int i = 0; i < c.size(); i++) {
                        Map<String, Object> m = c.get(i);
                        String id = m.get("id").toString();
                        if (ids.equals(id)) {
                            c.remove(i);
                            c.add(sp);
                        }
                    }
                }
            }


            //排序
            Collections.sort(c, new Comparator<Map<String, Object>>() {
                SimpleDateFormat sdft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    int mark = 1;
                    try {
                        if (o1.get("modify_date") == null) {
                            o1.put("modify_date", "2000-01-01 00:00:00");
                        }
                        if (o2.get("modify_date") == null) {
                            o2.put("modify_date", "2000-01-01 00:00:00");
                        }

                        Date name1 = sdft.parse(o1.get("modify_date").toString());//name1是从你list里面拿出来的一个
                        long time1 = name1.getTime();
                        Date name2 = sdft.parse(o2.get("modify_date").toString());//name1是从你list里面拿出来的第二name
                        long time2 = name2.getTime();


                        //name1权重
                        int i1 = 1;
                        //name2权重
                        int i2 = 1;

                        //设置权重，上样的权重为10（改）
                        if (o1.get("is_marketable") != null && Boolean.parseBoolean(o1.get("is_marketable").toString())) {
                            i1 = 10;
                        }

                        if (o2.get("is_marketable") != null && Boolean.parseBoolean(o2.get("is_marketable").toString())) {
                            i2 = 10;
                        }

                        //得分=权重*毫秒数
                        long score1 = time1 * i1;
                        long score2 = time2 * i2;


                        if (score1 > score2) {
                            mark = -1;//调整顺序
                        }

                        //mark小的排在前
                        return mark;
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    return mark;
                }
            });
        }


        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }


    /**
     * 获取分类节点(新)
     *
     * @param shopProductCategoryId
     * @return
     */
    @RequestMapping(value = "/getNodes", method = RequestMethod.POST)
    public @ResponseBody
    List<Object> getNodes(Long shopId, Boolean isTop, Long shopProductCategoryId, Long productCategoryId, String keyword) {
        List<Object> objs = new ArrayList<Object>();

        if (isTop) {
            List<Filter> filters = new ArrayList<Filter>();
            filters.add(Filter.eq("shopProductCategory", shopProductCategoryId));
            List<ShopProductAndProductCategory> spAndPcList = shopProductAndProductCategoryService.findList(null, filters, null);
            Set<Long> productCategoryIds = new HashSet<Long>();
            for (ShopProductAndProductCategory shopProductAndProductCategory : spAndPcList) {
                productCategoryIds.add(shopProductAndProductCategory.getProductCategory().getId());
            }

            filters.clear();
            filters.add(Filter.in("id", productCategoryIds));
            List<ProductCategory> productCategoryList = productCategoryBaseService.findList(null, filters, null);

            for (ProductCategory productCategory : productCategoryList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("shopProductCategoryId", shopProductCategoryId);
                map.put("productCategoryId", productCategory.getId());
                map.put("id", productCategory.getId());
                map.put("name", productCategory.getName());
                map.put("isParent", !getIsLeft(productCategory));
                map.put("isTop", false);
                //某分类的商品总数
                Integer oneCount = shopProductService.findListCount(shopProductCategoryId, productCategory.getId(), null, null);
                map.put("oneCount", oneCount);
                //某门店某分类的已上样商品总数
                Integer isShopCount = shopProductService.findListCount(shopProductCategoryId, productCategory.getId(), shopId, true);
                map.put("isShopCount", isShopCount);
                objs.add(map);
            }
        } else {
            ProductCategory productCategory = productCategoryBaseService.find(productCategoryId);
            if (productCategory != null) {
                for (ProductCategory child : productCategory.getChildren()) {
                    if (child.getIsEnabled() != null && child.getIsEnabled()) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        //map.put("shopProductCategoryId",null);
                        //map.put("productCategoryId",productCategory.getId());
                        map.put("productCategoryId", child.getId());
                        map.put("id", child.getId());
                        map.put("name", child.getName());
                        map.put("isParent", !getIsLeft(child));
                        map.put("isTop", false);
                        //某分类的总数
                        Integer oneCount = shopProductService.findListCount(shopProductCategoryId, child.getId(), null, null);
                        map.put("oneCount", oneCount);

                        //某门店某分类的已上样商品总数
                        Integer isShopCount = shopProductService.findListCount(shopProductCategoryId, child.getId(), shopId, true);
                        map.put("isShopCount", isShopCount);

                        //过滤没产品的分类
                        if (oneCount > 0) {
                            objs.add(map);
                        }
                    }
                }
            }
        }

        return objs;
    }


//	/**
//	 * 获取分类节点
//	 *
//	 * @param id
//	 * @return
//	 */
//	@RequestMapping(value = "/getNodes", method = RequestMethod.POST)
//	public @ResponseBody List<Object> getNodes(Long id, String keyword) {
//		List<Object> objs = new ArrayList<Object>();
//		if (keyword != null) {
//
//		} else {
//			ProductCategory productCategory = productCategoryBaseService.find(id);
//			if (productCategory != null) {
//				for (ProductCategory child : productCategory.getChildren()) {
//					if (child.getIsEnabled() != null && child.getIsEnabled()) {
//						Map<String, Object> data = new HashMap<String, Object>();
//						data.put("id", child.getId());
//						data.put("name", child.getName());
//						data.put("isParent", !getIsLeft(child));
//						objs.add(data);
//					}
//				}
//
//			} else {
//				List<Filter> fis = new ArrayList<Filter>();
//				fis.add(Filter.isNull("parent"));
//				fis.add(Filter.eq("isEnabled", true));
//				fis.add(Filter.eq("type", 0));
//				for (ProductCategory child : productCategoryBaseService.findList(null, fis, null)) {
//					Map<String, Object> data = new HashMap<String, Object>();
//					data.put("id", child.getId());
//					data.put("name", child.getName());
//					data.put("isParent", !getIsLeft(child));
//					objs.add(data);
//				}
//			}
//		}
//		return objs;
//	}

    public boolean getIsLeft(ProductCategory productCategory) {
        boolean isLeft = productCategory.getIsLeaf();
        if (!isLeft) {
            long count = productCategoryBaseService.count(Filter.eq("parent", productCategory),
                    Filter.eq("isEnabled", true));
            if (count == 0) {
                isLeft = true;
            }
        }
        return isLeft;
    }

    /**
     * 保存
     *
     * @throws Exception
     * @throws BiffException
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg save(Product product, ModelMap model) {

        return success().addObjX(product.getId());
    }

    @RequestMapping(value = "/operation", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg operation(Long shopId, Long productId, Integer type, ModelMap model) {
        ShopInfo shop = shopInfoService.find(shopId);
        Product product = productBaseService.find(productId);
        shopProductService.operation(shop, product, type);
        return success().addObjX(product.getId());
    }




}
