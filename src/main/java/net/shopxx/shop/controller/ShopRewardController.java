package net.shopxx.shop.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.shop.entity.ShopReward;
import net.shopxx.shop.service.ShopRewardService;
import net.shopxx.shop.service.ShopStoreService;

/**
 * Controller - 奖车
 */
@Controller("shopRewardController")
@RequestMapping("/shop/reward")
public class ShopRewardController extends BaseController{
	
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "shopRewardServiceImpl")
	private ShopRewardService shopRewardService;
	@Resource(name="shopStoreServiceImpl")
	private ShopStoreService shopStoreService;
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model) {
		return "/shop/reward/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		return "/shop/reward/list";
	}
	
	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		Store store = shopStoreService.findStore();
		model.addAttribute("store", store);
		return "/shop/reward/add";
	}
	
	/**
	 * 编辑 
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id,ModelMap model){
		ShopReward shopReward = shopRewardService.find(id);
		List<Map<String, Object>>  advertisAttach = shopRewardService.findAdvertisAttach(id);
		model.addAttribute("sr", shopReward);
		model.addAttribute("advertis_attach",JsonUtils.toJson(advertisAttach));
		return "/shop/reward/edit";
	}
	
	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(ShopReward shopReward,Long storeId) {
		shopRewardService.saveShopReward(storeId, shopReward);
		return success().addObjX(shopReward.getId());
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg update(ShopReward shopReward,Long storeId) {
		shopRewardService.updateShopReward(storeId, shopReward);
		return success().addObjX(shopReward.getId());
	}
	
	
}
