package net.shopxx.shop.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.shop.dao.ShopOrderDao;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.entity.ShopReplenishment;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.shop.service.ShopReplenishmentService;
import net.shopxx.shop.service.ShopStoreService;
import net.shopxx.shop.service.StorageProductService;

/**
 * Controller - 门店安全库存/缺货下达
 */
@Controller("shopReplenishmentController")
@RequestMapping("/shop/replenishment")
public class ShopReplenishmentController extends BaseController {

	@Resource(name = "shopReplenishmentServiceImpl")
	private ShopReplenishmentService shopReplenishmentService;
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name ="shopOrderDao")
	private ShopOrderDao shopOrderDao;
    @Resource(name = "storageProductServiceImpl")
    private StorageProductService storageProductService;
    @Resource(name="shopStoreServiceImpl")
    private ShopStoreService shopStoreService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Integer readOnly, ModelMap model) {
		model.addAttribute("readOnly", readOnly);
		return "/shop/replenishment/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		return "/shop/replenishment/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(String vonderCode,String productName, Pageable pageable) {
		Page<Map<String, Object>>  page = shopReplenishmentService.findPage(vonderCode, productName, pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}
	
	/**
	 * 缺货列表
	 */
	@RequestMapping(value = "/lists", method = RequestMethod.GET)
	public String lists(Pageable pageable, ModelMap model) {
		
		return "/shop/replenishment/lists";
	}

	/**
	 * 缺货列表数据
	 */
	@RequestMapping(value = "/lists_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg lists_data(Pageable pageable) {
		LogUtils.info("==================缺货报表查询开始=================");
		//定义一个空的列用于返回前台参数信息
		List<Map<String, Object>> list_product = new ArrayList<Map<String,Object>>();
		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		//获取当前登录用户的默认客户
		Store store = shopStoreService.findStore();
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("store",store));
		filters.add(Filter.eq("companyInfoId",companyInfo.getId()));
		//获取客户下的所有门店
		List<ShopInfo> shopInfo = shopInfoService.findList(null, filters, null);
		LogUtils.info("门店长度："+shopInfo.size());
		List<String> shopCode = new ArrayList<String>();
		for(ShopInfo si : shopInfo) {
			if(si.getSn()!=null){
				shopCode.add(si.getId().toString());
			}
		}
		//拼接门店id
		String code = "";
		for(int i=0;i<shopCode.size();i++){
			if(i == shopCode.size()-1){
				code += shopCode.get(i);
			}else{
				code += shopCode.get(i)+",";
			}
		}
		LogUtils.info("门店id："+code);
		//使用多个门店编码查询小程序对应门店所有订单明细的商品
		List<Map<String, Object>> products = shopOrderDao.findOrderItemProductId(code);
		//查库存商品
		List<Map<String, Object>> storageProduct = storageProductService.findProduct(store.getId());
		Set<Long> productSet = new HashSet<Long>();
		for(Map<String,Object> p : products){
			if(p.get("product")!=null){
				productSet.add(Long.parseLong(p.get("product").toString()));
			}
		}
		for(Map<String,Object> p : storageProduct){
			if(p.get("id")!=null){
				productSet.add(Long.parseLong(p.get("id").toString()));
			}
		}
		
		//遍历商品
		for(Long p : productSet){
			Map<String, Object> map = new HashMap<String, Object>();
//			System.out.println("商品"+p);
			Product product = productBaseService.find(p);
			//定义发货数量
			BigDecimal shippingQuantity = BigDecimal.ZERO;
			//定义安全数量
			BigDecimal securityQuantity = BigDecimal.ZERO;
			//定义库存数量
			BigDecimal inventoryQuantity = BigDecimal.ZERO;
			//定义缺货数量
			BigDecimal replenishment = BigDecimal.ZERO;
			
			BigDecimal a = null;
			BigDecimal b = null;
			BigDecimal c = null;
			//通过产品id找订单明细的发货数量总和
			shippingQuantity = shopOrderDao.findOrderItemShippingQuantity(p);
			a = shippingQuantity == null?BigDecimal.ZERO:shippingQuantity;
			LogUtils.info("=======================逻辑处理==============================");
			LogUtils.info("发货数量:"+a);
			//通过产品id找对应当前登录用户的库存数量总和
			inventoryQuantity = shopOrderDao.findInventoryQuantity(p,store.getId());
			b = inventoryQuantity==null?BigDecimal.ZERO:inventoryQuantity;
			LogUtils.info("库存数量总和"+b);
			//通过产品id找对应的安全库存数量
			securityQuantity = shopOrderDao.findSecurityQuantity(p);
			c = securityQuantity == null ?BigDecimal.ZERO:securityQuantity;
			LogUtils.info("安全库存数量:"+c);
			replenishment = b.subtract(a.add(c));
			LogUtils.info("缺料数量:"+replenishment);
			LogUtils.info("产品名："+product.getDescription());
			LogUtils.info("产品编码："+product.getVonderCode());
			LogUtils.info("=====================================================");
			if(replenishment.compareTo(BigDecimal.ZERO)<0){
//				System.out.println(product.getId());
				if(product!=null){
					//产品编码
					map.put("voder_code", product.getVonderCode());
					//产品描述
					map.put("description", product.getDescription());
					//待发数量
					map.put("shipping_quantity", a);
					//库存数量
					map.put("inventory_quantity", b);
					//安全库存数
					map.put("security_quantity", c);
					//缺货数量
					map.put("replenishment", replenishment);
					map.put("id", product.getId());
					list_product.add(map);						
				}
			}
		}
		LogUtils.info("==================缺货报表查询结束=================");
		String jsonPage = JsonUtils.toJson(list_product);
		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Integer readOnly, ModelMap model) {
		model.addAttribute("readOnly", readOnly);
		return "/shop/replenishment/add";
	}

	/**
	 * 订单详情页
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, Integer readOnly, ModelMap model) {
		ShopReplenishment sr = shopReplenishmentService.find(id);
		model.addAttribute("sr", sr);
		model.addAttribute("readOnly", readOnly);
		return "/shop/replenishment/edit";
	}

	/**
	 * 安全库存保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg save(ShopReplenishment sr,Long productId) {
		if(productId==null){
			return error("请选择产品！");
		}
		if (sr.getStore().getId() == null){
        	return error("客户不能为空！");
        }
		Product product = productBaseService.find(productId);
		List<Filter> filters  = new ArrayList<Filter>();
		filters.add(Filter.eq("product", product));
		filters.add(Filter.eq("store", sr.getStore()));
		ShopReplenishment s = shopReplenishmentService.find(filters);
		if(s!=null){
			return error("该产品有对应维护单据请选则对应单据维护");
		}
		sr.setProduct(product);
		sr.setType(0);
		shopReplenishmentService.save(sr);
		return success().addObjX(sr.getId());
	}

	/**
	 * 安全库存更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(ShopReplenishment sr, Long productId) {
		if(productId==null){
			return error("请选择产品！");
		}
		if (sr.getStore().getId() == null){
        	return error("客户不能为空！");
        }
		sr.setProduct(productBaseService.find(productId));
		sr.setType(0);
		shopReplenishmentService.update(sr);
		return success();
	}
}
