package net.shopxx.shop.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.shop.service.StorageProductService;

/**
 * 库存查询
 */
@Controller("storageQueryController")
@RequestMapping("/shop/storage_query")
public class StorageQueryController extends BaseController {

    @Resource(name = "storageProductServiceImpl")
    private StorageProductService storageProductService; 
    
    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Pageable pageable, ModelMap model) {
        return "/shop/storage_query/list_tb";
    }

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(Pageable pageable, ModelMap model) {
        return "/shop/storage_query/list";
    }
    
    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(String stoName, String proName, String vonderCode, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(stoName);  // 仓库名称
        param.add(proName);  // 产品名称
        param.add(vonderCode); // 产品编码
        Page<Map<String, Object>> page = storageProductService.findPage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }
    
    /**
     * 列表
     */
    @RequestMapping(value = "/select_storage", method = RequestMethod.GET)
    public String selectStorage(Integer multi, ModelMap model) {
        model.addAttribute("multi", multi);
        return "/shop/storage_query/select_storage";
    }
    
    /**
     * 列表数据 - 选择门店的列表数据
     */
    @RequestMapping(value = "/select_storage_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg selectStorageData(String name,String vonderCode,Pageable pageable, ModelMap model) {
        Page<Map<String, Object>> page = storageProductService.findProduct(name, vonderCode, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }
}
