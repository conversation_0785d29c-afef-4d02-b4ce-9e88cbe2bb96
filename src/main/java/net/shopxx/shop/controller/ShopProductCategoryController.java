package net.shopxx.shop.controller;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.shop.entity.ShopProductAndProductCategory;
import net.shopxx.shop.entity.ShopProductCategory;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.shop.service.ShopProductAndProductCategoryService;
import net.shopxx.shop.service.ShopProductCategoryService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * controller-门店上样分类
 */
@Controller("shopProductCategoryController")
@RequestMapping("/shop/shop_product_category")
public class ShopProductCategoryController extends BaseController {
    @Resource(name = "productCategoryBaseServiceImpl")
    private ProductCategoryBaseService productCategoryBaseService;
    @Resource(name = "productBaseServiceImpl")
    private ProductBaseService productBaseService;
    @Resource(name = "shopProductCategoryServiceImpl")
    private ShopProductCategoryService shopProductCategoryService;
    @Resource(name = "shopProductAndProductCategoryServiceImpl")
    private ShopProductAndProductCategoryService shopProductAndProductCategoryService;

    /**
     * 列表
     */
    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(ModelMap model) {

        return "/shop/shop_product_category/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, String name, Boolean isEnabled, ModelMap model) {
        return "/shop/shop_product_category/list";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg list_data(String sn, String name, Boolean isEnabled,
                        Pageable pageable) {

        List<Map<String, Object>> list = shopProductCategoryService.findList(sn,
                name,
                isEnabled);
        String jsonPage = JsonUtils.toJson(new Page<Map<String, Object>>(
                list, list.size(), pageable));
        return success(jsonPage);
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg delete(Long id) {

        return success();
    }



    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(Long ids, ModelMap model) {
        //查询产品分类的顶级分类
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.isNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        List<ProductCategory> productCategoryList = productCategoryBaseService.findList(null, filters, null);

        model.addAttribute("productCategoryList",productCategoryList);

        return "/shop/shop_product_category/add";
    }


    /**
     * 保存
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg save(ShopProductCategory shopProductCategory,Long [] productCategoryId) {
        if (shopProductCategoryService.count(Filter.eq("name",
                shopProductCategory.getName()),
                Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId())) > 0) {
            // 该分类名称已存在
            return error("120050");
        }

        if (!ConvertUtil.isEmpty(shopProductCategory.getSn())) {
            if (productCategoryBaseService.count(Filter.eq("sn",
                    shopProductCategory.getName())) > 0) {
                // 该外部编号已存在
                return error("120051");
            }
        }
        shopProductCategoryService.saveShopProductCategory(shopProductCategory,productCategoryId);

        return success().addObjX(shopProductCategory.getId());
    }


    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        //查询产品分类的顶级分类
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.isNull("parent"));
        filters.add(Filter.eq("isEnabled", true));
        List<ProductCategory> productCategoryList = productCategoryBaseService.findList(null, filters, null);
        model.addAttribute("productCategoryList",productCategoryList);

        //选中的分类
        filters.clear();
        filters.add(Filter.eq("shopProductCategory",id));
        List<ShopProductAndProductCategory> productAndProductCategorieList = shopProductAndProductCategoryService.findList(null, filters, null);
        model.addAttribute("productAndProductCategoryList",productAndProductCategorieList);

        ShopProductCategory shopProductCategory = shopProductCategoryService.find(id);
        model.addAttribute("shopProductCategory", shopProductCategory);
        return "/shop/shop_product_category/edit";
    }


    /**
     * 更新
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public @ResponseBody
    ResultMsg update(ShopProductCategory shopProductCategory,Long [] productCategoryId) {

        shopProductCategoryService.updateShopProductCategory(shopProductCategory,productCategoryId);

        return success().addObjX(shopProductCategory.getId());

    }
}
