package net.shopxx.shop.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.member.entity.PcRole;
import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcRoleBaseService;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.service.ShopMemberService;

/**
 * Controller - 门店成员
 */
@Controller("shopMemberController")
@RequestMapping("/shop/member")
public class ShopMemberController extends BaseController {

	@Resource(name = "shopMemberServiceImpl")
	private ShopMemberService shopMemberService;
	@Resource(name = "pcRoleBaseServiceImpl")
	private PcRoleBaseService pcRoleBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(ModelMap model) {
		return "/shop/member/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		List<PcRole> role = findRole();
		model.addAttribute("roles", role);
		return "/shop/member/add";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		List<Map<String, Object>> app = shopMemberService.findAppStoreMember(id);
		Map<String, Object> content = app == null ? null : app.get(0);
		model.addAttribute("shop_name", content.get("shop_name"));
		model.addAttribute("type", content.get("type"));
		model.addAttribute("mobile", content.get("mobile"));
		model.addAttribute("gender", content.get("gender"));
		model.addAttribute("id_card", content.get("id_card"));
		model.addAttribute("name", content.get("name"));
		model.addAttribute("tsm_id", content.get("tsm_id"));
		model.addAttribute("shop_info",content.get("shop_info"));
		Long memberId = content.get("login_store_member") == null ? null
				: Long.parseLong(content.get("login_store_member").toString());
		StoreMember storeMember = storeMemberBaseService.find(memberId);
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember", storeMember));
		List<PcUserRole> userRoles = pcUserRoleBaseService.findList(null, filters, null);
		List<PcRole> uRoles = new ArrayList<PcRole>();
		if (storeMember != null) {
			for (PcUserRole userRole : userRoles) {
				uRoles.add(userRole.getPcRole());
			}
		}
		List<PcRole> role = findRole();
		model.addAttribute("uRoles", uRoles);
		model.addAttribute("roles", role);
		model.addAttribute("storeMember", storeMember);
		return "/shop/member/edit";
	}

	protected List<PcRole> findRole() {
		List<PcRole> roles = pcRoleBaseService.findAll();
		List<PcRole> role = new ArrayList<PcRole>();
		for (PcRole r : roles) {
			if ("经销商财务".equals(r.getName()))
				role.add(r);
			if ("经销商跟单".equals(r.getName()))
				role.add(r);
			if ("店长".equals(r.getName()))
				role.add(r);
			if ("副店长".equals(r.getName()))
				role.add(r);
			if ("导购员".equals(r.getName()))
				role.add(r);
		}
		return role;
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		return "/shop/member/list";
	}

	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Pageable pageable) {
		Page<Map<String, Object>> page = shopMemberService.findPages(pageable);
		String jsonPage = JsonUtils.toJson(page);
		return success(jsonPage);
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(String name, Integer gender, String idCard, String phone, String imageName,
			Long[] roleId, Long shopInfoId, Boolean isEnabled,Integer type) {
		if (shopInfoId == null) {
			return error("请选择门店");
		}
		//没有用户新增门店用户
		shopMemberService.saveShopMember(name, gender, idCard, phone, imageName, roleId, shopInfoId,
				isEnabled,type);
		return success();
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(Long id,Long storeMemberId, String name, Integer gender, String idCard, String phone,
			String imageName, Long[] roleId, Long shopInfoId, Boolean isEnabled,Integer type) {
		if (shopInfoId == null) {
			return error("请选择门店");
		}
		//当关联表link用户为空
		if(storeMemberId == null){
			//有用户创建link用户
			shopMemberService.addMember(id, name, gender, idCard, phone, isEnabled, shopInfoId, roleId);
		}else{
			//关联表link不为空 更新link用户
			shopMemberService.updateShopMember(storeMemberId, name, gender, idCard, phone, imageName, roleId, shopInfoId, isEnabled);
		}
		
	
		return success();
	}

}
