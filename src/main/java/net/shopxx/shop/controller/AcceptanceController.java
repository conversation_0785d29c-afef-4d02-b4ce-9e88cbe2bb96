package net.shopxx.shop.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.member.service.StoreMemberSaleOrgPostService;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.AcceptanceReimburse;
import net.shopxx.shop.entity.ShopDevise;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.AcceptanceReimburseService;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopDeviseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.wf.service.WfBaseService;

/**
 * 门店装修验收（不报销）
 */
@Controller("acceptanceController")
@RequestMapping("/shop/acceptance")
public class AcceptanceController extends BaseController {

    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;

    @Resource(name = "shopAddedServiceImpl")
    private ShopAddedService shopAddedService;

    @Resource(name = "acceptanceReimburseServiceImpl")
    private AcceptanceReimburseService acceptanceReimburseService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "shopDeviseServiceImpl")
    private ShopDeviseService shopDeviseService;
    @Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeMemberSaleOrgPostServiceImpl")
	private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;



    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Long objTypeId, Long objid, Pageable pageable, ModelMap model) {
    	model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
        return "/shop/acceptance/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        model.addAttribute("wfStates", wfBaseService.getAllWfStates());
        return "/shop/acceptance/list";
    }

    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
        return "/shop/acceptance/add";
    }
    
    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        AcceptanceReimburse acceptanceReimburse = acceptanceReimburseService.find(id);
        ShopDevise shopDevise = acceptanceReimburse.getShopDevise();
        model.addAttribute("acceptanceReimburse", acceptanceReimburse);
        model.addAttribute("shopDevise", shopDevise);
        // 验收申请承诺项
        List<String> acList = new ArrayList<String>();
        if (!StringUtils.isEmpty(acceptanceReimburse.getAcceptanceCommitment())) {
            String[] strs = acceptanceReimburse.getAcceptanceCommitment().split(",");
            acList = Arrays.asList(strs);
        }
        model.addAttribute("ac", acList);
        // 验收人员核实项
        /*List<String> avList = new ArrayList<String>();
        if (!StringUtils.isEmpty(acceptanceReimburse.getAcceptanceVerify())) {
            String[] strs = acceptanceReimburse.getAcceptanceVerify().split(",");
            avList = Arrays.asList(strs);
        }
        model.addAttribute("av", avList);*/
        // 初始化验收申请承诺附件
        List<Map<String, Object>> acceptanceCommitmentAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 0);
        // 门店装修验收照片附件
        List<Map<String, Object>> shopFarAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 4);
        List<Map<String, Object>> shopDecorateAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 1);
        List<Map<String, Object>> payPlatformAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 2);
        List<Map<String, Object>> plateAttachs = acceptanceReimburseService.findAttach(acceptanceReimburse.getId(), 3);
        model.addAttribute("acceptanceCommitmentAttachs", JsonUtils.toJson(acceptanceCommitmentAttachs));
        model.addAttribute("shopFarAttachs", JsonUtils.toJson(shopFarAttachs));
        model.addAttribute("shopDecorateAttachs", JsonUtils.toJson(shopDecorateAttachs));
        model.addAttribute("payPlatformAttachs", JsonUtils.toJson(payPlatformAttachs));
        model.addAttribute("plateAttachs", JsonUtils.toJson(plateAttachs));


        //================================新流程
        //将当前登录用户岗位返回前台
        StoreMember storeMember = storeMemberBaseService.getCurrent();

        if(acceptanceReimburse.getWfId()!= null){
            ActWf wf = acceptanceReimburseService.getWfByWfId(acceptanceReimburse.getWfId());
           // model.addAttribute("wf", wf);


            if (wf != null) {
                //省长审核
                Boolean szsh = false;
                //渠道专员
                Boolean qdzy = false;
                //省长审核权限
                Boolean szshs = false;
                //渠道专员权限
                Boolean qdzys = false;

                // 查找当前流程明细
                List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
                for(Map<String, Object> c : item){
                    if(c.get("suggestion")!=null){
                        //处理结果
                        String approved = c.get("approved")!=null?c.get("approved").toString():"false";
                        //节点名称
                        String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
                        //对比节点名称是否对应

                        if(rwm.contains("省长")){
                            szsh = Boolean.valueOf(approved);

                        }
                        if(rwm.contains("渠道")){
                            qdzy = Boolean.valueOf(approved);
                            if(!qdzy){
                                szsh = false;
                            }
                        }
                    }
                }

                //获取当前流程所在的节点
                Task t = acceptanceReimburseService.getCurrTaskByWf(wf);
                if(t!=null){
                    //获取当前节点所有用户id
                    List<String> userId = actWfService.getTaskUsers(t.getId());

                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
                        szshs = true;
                    }
                    if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道")){
                        szshs = true;qdzys = true;
                    }
                }
                model.addAttribute("wf", wf);
                model.addAttribute("node", t);
                model.addAttribute("szsh", szsh);
                model.addAttribute("qdzy", qdzy);
                model.addAttribute("szshs", szshs);
                model.addAttribute("qdzys", qdzys);
            }
        }

        return "/shop/acceptance/edit";
    }


    /**
     * 流程节点保存
     */
    @RequestMapping(value = "/saveform", method = RequestMethod.POST)
    public @ResponseBody ResultMsg saveform(AcceptanceReimburse acceptanceReimburse,Integer type) {
        // type 用来定义节点
        //type 1区域经理 2省长 3渠道部
        acceptanceReimburseService.saveform(type,acceptanceReimburse);
        return success().addObjX(acceptanceReimburse.getId());
    }

    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(String sn, String deviseSn, Integer[] status, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(2);  // type
        param.add(sn);
        param.add(status);
        param.add(deviseSn);
        Page<Map<String, Object>> page = acceptanceReimburseService.findAcceptancePage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }


    /**
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMsg save(AcceptanceReimburse acceptanceReimburse, Long shopInfoId, Long shopDeviseId, 
            Integer[] acceptanceCommitments, Integer[] acceptanceVerifys) {
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        if (shopInfo == null) {
            return error("门店选择有误，请重新选择！");
        }
        acceptanceReimburse.setShopInfo(shopInfo);
        acceptanceReimburse.setSaleOrg(shopInfo.getSaleOrg()!=null?shopInfo.getSaleOrg():null);
        ShopDevise shopDevise = shopDeviseService.find(shopDeviseId);
        if (shopDevise == null) {
            return error("门店设计选择有误，请重新选择！");
        }
        acceptanceReimburse.setShopDevise(shopDevise);
        // 验收申请承诺
//        if (acceptanceCommitments != null && acceptanceCommitments.length > 0) {
//            String acceptanceCommitment = StringUtils.arrayToDelimitedString(acceptanceCommitments, ",");
//            acceptanceReimburse.setAcceptanceCommitment(acceptanceCommitment);
//        }else{
//        	return error("验收申请承诺需要勾选！");
//        }
        // 验收人员核实
        /*if (acceptanceVerifys != null && acceptanceVerifys.length > 0) {
            String acceptanceVerify = StringUtils.arrayToDelimitedString(acceptanceVerifys, ",");
            acceptanceReimburse.setAcceptanceVerify(acceptanceVerify);
        }*/
        // 保存状态
        acceptanceReimburse.setStatus(0);
        acceptanceReimburseService.saveAcceptance(acceptanceReimburse);
        return success().addObjX(acceptanceReimburse.getId());
    }

    /**
     * 更新
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultMsg update(AcceptanceReimburse acceptanceReimburse, Long shopInfoId, Long shopDeviseId, 
            Integer[] acceptanceCommitments, Integer[] acceptanceVerifys, Integer[] designManagers,
            Integer[] directorOpinions) {
        ShopInfo shopInfo = shopInfoService.find(shopInfoId);
        if (shopInfo == null) {
            return error("门店选择有误，请重新选择！");
        }
        acceptanceReimburse.setShopInfo(shopInfo);
        acceptanceReimburse.setSaleOrg(shopInfo.getSaleOrg()!=null?shopInfo.getSaleOrg():null);
        ShopDevise shopDevise = shopDeviseService.find(shopDeviseId);
        if (shopDevise == null) {
            return error("门店设计选择有误，请重新选择！");
        }
        acceptanceReimburse.setShopDevise(shopDevise);
        // 验收申请承诺
//        if (acceptanceCommitments != null && acceptanceCommitments.length > 0) {
//            String acceptanceCommitment = StringUtils.arrayToDelimitedString(acceptanceCommitments, ",");
//            acceptanceReimburse.setAcceptanceCommitment(acceptanceCommitment);
//        }else{
//        	return error("验收申请承诺需要勾选！");
//        }
        // 验收人员核实
        /*if (acceptanceVerifys != null && acceptanceVerifys.length > 0) {
            String acceptanceVerify = StringUtils.arrayToDelimitedString(acceptanceVerifys, ",");
            acceptanceReimburse.setAcceptanceVerify(acceptanceVerify);
        }*/
        acceptanceReimburseService.updateAcceptance(acceptanceReimburse);
        return success().addObjX(acceptanceReimburse.getId());
    }
    
    /**
   	 * 审核
   	 * @param id shopId
   	 * @return
   	 */
   	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
   	public @ResponseBody
   	ResultMsg check_wf(Long id,String modelId,Long objTypeId) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        String postName = "地板中心区域经理";
        //只有区域经理可以提交流程
        Boolean flag = storeMemberSaleOrgPostService.checkPostByStoreMember(storeMember, postName);
        if(flag == false){
            ExceptionUtil.throwControllerException("您不是区域经理不能提交审核");
        }

        if (id == null) {
   			// 请选择订单
   			return error("请选择门店单据");
   		}
   		AcceptanceReimburse acceptanceReimburse = acceptanceReimburseService.find(id);

        acceptanceReimburseService.createWf(id, modelId, objTypeId);
   		return success();
   	}
}
