package net.shopxx.shop.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.SetStorage;
import net.shopxx.shop.service.SetStorageService;
/**
 * 仓库设置 
 */
@Controller("setStorageController")
@RequestMapping("/shop/set_storage")
public class SetStorageController extends BaseController {
    
    @Resource(name = "areaBaseServiceImpl")
    private AreaBaseService areaService;
    @Resource(name = "setStorageServiceImpl")
    private SetStorageService setStorageService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;

    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Pageable pageable, ModelMap model) {
        return "/shop/set_storage/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        return "/shop/set_storage/list";
    }
    
    /**
     * 添加
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String add(ModelMap model) {
    	
        return "/shop/set_storage/add";
    }
    
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String edit(Long id, ModelMap model) {
        SetStorage storage = setStorageService.find(id);
        model.addAttribute("storage", storage);
        return "/shop/set_storage/edit";
    }
    
    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(String sn, Integer[] available, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(sn);
        param.add(available);
        Page<Map<String, Object>> page = setStorageService.findPage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

    /**
     * 保存
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMsg save(SetStorage setStorage) {
        if (setStorage.getArea() != null && setStorage.getArea().getId() != null) {
            setStorage.setArea(areaService.find(setStorage.getArea().getId()));
        }
        if (setStorage.getStore().getId() == null){
        	return error("客户不能为空！");
        }
        setStorage.setSn(Sequence.getInstance().getSequence("C"));
        setStorageService.save(setStorage);
        return success().addObjX(setStorage.getId());
    }
    
    /**
     * 更新
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultMsg update(SetStorage setStorage) {
        if (setStorage.getArea() != null && setStorage.getArea().getId() != null) {
            setStorage.setArea(areaService.find(setStorage.getArea().getId()));
        }
        if (setStorage.getStore().getId() == null){
        	return error("客户不能为空！");
        }
        setStorageService.update(setStorage);
        return success().addObjX(setStorage.getId());
    }
    
    @RequestMapping(value = "/storage", method = RequestMethod.POST)
	public @ResponseBody ResultMsg storage(Long id){
    	 List<Map<String, Object>> oStorage = setStorageService.findStorage(id);
    	 String jsonPage = JsonUtils.toJson(oStorage);
		return success(jsonPage);
	}
}
