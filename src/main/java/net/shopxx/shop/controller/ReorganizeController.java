package net.shopxx.shop.controller;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.ExcelView;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.util.SystemConfig;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.Reorganize;
import net.shopxx.shop.entity.ReorganizeAttach;
import net.shopxx.shop.service.ReorganizeAttachService;
import net.shopxx.shop.service.ReorganizeService;
import net.shopxx.shop.service.ShopAddedService;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 客户整改
 */
@Controller("reorganizeController")
@RequestMapping("/shop/reorganize")
public class ReorganizeController extends BaseController {

	@Resource(name = "reorganizeAttachServiceImpl")
	private ReorganizeAttachService reorganizeAttachService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name = "reorganizeServiceImpl")
	private ReorganizeService reorganizeService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "shopAddedServiceImpl")
	private ShopAddedService shopAddedService;

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long sid, Long objTypeId, Long objid, ModelMap model) {
		model.addAttribute("sid", sid);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		return "/shop/reorganize/list_tb";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Pageable pageable, ModelMap model) {
		return "/shop/reorganize/list";
	}

	/**
	 * 列表数据
	 */
	@ResponseBody
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public ResultMsg list_data(Reorganize r, Long storeId, String createName,
                               String firstTime, String lastTime, Pageable pageable) {
		List<Object> params = new ArrayList<Object>();
		params.add(storeId);
		params.add(createName);
		params.add(firstTime);
		params.add(lastTime);
		String jsonPage = JsonUtils.toJson(reorganizeService.findPage(r,params, pageable));
		return success(jsonPage);
	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long sid, ModelMap model) {
		Store store = storeBaseService.find(sid);
		if (store != null) {
			model.addAttribute("store", store);
		}
		return "/shop/reorganize/add";
	}

	/**
	 * 保存
	 */
	@ResponseBody
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public ResultMsg save(Reorganize reorganize, Long sid) {

		checkObj(reorganize, sid);

		reorganize.setStatus(0);
		Store store = storeBaseService.find(sid);
		if (store != null) {
			reorganize.setStore(store);
		}
		reorganizeService.saveReorganize(reorganize);
		return success().addObjX(reorganize.getId());
	}

	/**
	 * 表单必填校验
	 * 
	 * @param reorganize
	 * @param sid
	 */
	private void checkObj(Reorganize reorganize, Long sid) {

		if (reorganize.getWhetherSignedContract() == null) {
			ExceptionUtil.throwControllerException("请填写是否签订今年合同");
		}
		if (reorganize.getWhetherSignature() == null) {
			ExceptionUtil.throwControllerException("请填写省长是否签字");
		}
		if (reorganize.getLetters() == null) {
			ExceptionUtil.throwControllerException("请填写公司发出函件");
		}
		if (reorganize.getWhetherSubmitPlan() == null) {
			ExceptionUtil.throwControllerException("请填写是否提交计划");
		}
		if (reorganize.getRectificationContent() == null) {
			ExceptionUtil.throwControllerException("请填写整改内容");
		}
		if (reorganize.getReorganized0Attachs().isEmpty()) {
			ExceptionUtil.throwControllerException("请添加三年销量数据签字版附件");
		}
		if (reorganize.getReorganized1Attachs().isEmpty()) {
			ExceptionUtil.throwControllerException("请添加函件省长签字版附件");
		}
		if (reorganize.getReorganized2Attachs().isEmpty()) {
			ExceptionUtil.throwControllerException("请添加函件Word版附件");
		}
		Boolean isWord = false;
		for(ReorganizeAttach ra:reorganize.getReorganized2Attachs()){
			if(ra == null||ra.getSuffix()==null){
				continue;
			}
			if(!"docx".equals(ra.getSuffix())&&!"doc".equals(ra.getSuffix())){
				isWord = true;
			}
		}
		if (isWord) {
			ExceptionUtil.throwControllerException("函件Word版附件：上传的文件格式有误请重新上传！");
		}
	}
	

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(Reorganize reorganize, Long storeId) {
		checkObj(reorganize, storeId);
		reorganizeService.updateReorganize(reorganize, storeId);
		return success().addObjX(reorganize.getId());
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		Reorganize reorganize = reorganizeService.find(id);
		model.addAttribute("reorganize", reorganize);
		Store store = storeBaseService.find(reorganize.getStore().getId());
		model.addAttribute("store", store);
		// 查询附件
		List<ReorganizeAttach> reorganizeAttaches0 = this.findAttachs(reorganize, 0);
		List<ReorganizeAttach> reorganizeAttaches1 = this.findAttachs(reorganize, 1);
		List<ReorganizeAttach> reorganizeAttaches2 = this.findAttachs(reorganize, 2);
		List<ReorganizeAttach> reorganizeAttaches3 = this.findAttachs(reorganize, 3);
		List<ReorganizeAttach> reorganizeAttaches4 = this.findAttachs(reorganize, 4);
        List<ReorganizeAttach> receiptAttachs = this.findAttachs(reorganize, 5);
		model.addAttribute("reorganizedAttachs_0", JsonUtils.toJson(reorganizeAttaches0));
		model.addAttribute("reorganizedAttachs_1", JsonUtils.toJson(reorganizeAttaches1));
		model.addAttribute("reorganizedAttachs_2", JsonUtils.toJson(reorganizeAttaches2));
		model.addAttribute("reorganizedAttachs_3", JsonUtils.toJson(reorganizeAttaches3));
		model.addAttribute("reorganizedAttachs_4", JsonUtils.toJson(reorganizeAttaches4));
		model.addAttribute("receiptAttachs", JsonUtils.toJson(receiptAttachs));
		System.out.println("receiptAttachs json==" + JsonUtils.toJson(receiptAttachs));

		// 审核流程
		StoreMember storeMember = storeMemberBaseService.getCurrent();

		if (reorganize.getWfId() != null) {
			ActWf wf = reorganizeService.getWfByWfId(reorganize.getWfId());

			if (wf != null) {

				// 省长审核
				Boolean szsh = false;
				// 渠道部审核
				Boolean qdbsh = false;
				// 销售中心副总审核
				Boolean xszxfzsh = false;
				// 事业部总裁审核
				Boolean sybzcsh = false;

				// 省长审核权限
				Boolean szshs = false;
				// 渠道部审核权限
				Boolean qdbshs = false;
				// 销售中心副总审核权限
				Boolean xszxfzshs = false;
				// 事业部总裁审核权限
				Boolean sybzcshs = false;

				// 查找当前流程明细
				List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
				for (Map<String, Object> c : item) {
					if (c.get("suggestion") != null) {
						// 处理结果
						String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
						// 节点名称
						String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";

						// 对比节点名称是否对应
						if (rwm.contains("省长")) {
							// 赋值处理结果到所定义的节点上
							szsh = Boolean.valueOf(approved);
						}

						if (rwm.contains("渠道")) {
							// 赋值处理结果到所定义的节点上
							qdbsh = Boolean.valueOf(approved);
							if (!qdbsh) {
								szsh = false;
							}
						}

						if (rwm.contains("销售")) {
							xszxfzsh = Boolean.valueOf(approved);
							if (!xszxfzsh) {
								qdbsh = false;
							}
						}

						if (rwm.contains("事业部")) {
							sybzcsh = Boolean.valueOf(approved);
							if (!sybzcsh) {
								xszxfzsh = false;
							}
						}
					}
				}
				// 获取当前流程所在的节点
				Task t = shopAddedService.getCurrTaskByWf(wf);

				if (t != null) {
					// 获取当前节点所有用户id
					List<String> userId = actWfService.getTaskUsers(t.getId());
					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("省长")) {
						szshs = true;
					}

					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("渠道")) {
						szshs = true;
						qdbshs = true;
					}

					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("销售")) {
						szshs = true;
						qdbshs = true;
						xszxfzshs = true;
					}

					if (userId.contains(storeMember.getId().toString()) && t.getName().contains("事业部")) {
						szshs = true;
						qdbshs = true;
						xszxfzshs = true;
						sybzcshs = true;
					}
				}

				model.addAttribute("wf", wf);
				model.addAttribute("node", t);
				model.addAttribute("szsh", szsh);
				model.addAttribute("qdbsh", qdbsh);
				model.addAttribute("xszxfzsh", xszxfzsh);
				model.addAttribute("sybzcsh", sybzcsh);

				model.addAttribute("szshs", szshs);
				model.addAttribute("qdbshs", qdbshs);
				model.addAttribute("xszxfzshs", xszxfzshs);
				model.addAttribute("sybzcshs", sybzcshs);

			}
		}
		return "/shop/reorganize/edit";
	}

	/**
	 * 审核
	 * 
	 * @param id
	 *            shopId
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody ResultMsg check_wf(Long id, String modelId, Long objTypeId) {
		if (id == null) {
			// 请选择订单
			return error("请选择单据");
		}
		// StoreMember storeMember = storeMemberBaseService.getCurrent();
		// Reorganize reorganize = reorganizeService.find(id);
		//
		// reorganizeService.createWf(reorganize.getSn(),
		// String.valueOf(storeMember.getId()),
		// modelId,
		// objTypeId,
		// id,
		// WebUtils.getCurrentCompanyInfoId());

		reorganizeService.createWf(id, modelId, objTypeId);

		return success();
	}

	/**
	 * 流程节点保存
	 */
	@RequestMapping(value = "/saveform", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveform(Reorganize reorganize, Integer type) {
		// type 用来定义节点
		// type 1区域经理 2省长 3财务部
		reorganizeService.saveform(type, reorganize);
		return success().addObjX(reorganize.getId());
	}

	private List<ReorganizeAttach> findAttachs(Reorganize reorganize, Integer type) {
		List<Filter> filterss = new ArrayList<Filter>();
		filterss.add(Filter.eq("reorganize", reorganize));
		filterss.add(Filter.eq("type", type));
		List<ReorganizeAttach> reorganizeAttaches = reorganizeAttachService.findList(null, filterss, null);
		return reorganizeAttaches;
	}
	
	@RequestMapping(value = "/condition_export", method = RequestMethod.GET)
	public ModelAndView conditionExport(Reorganize r, Long storeId, String createName,
                                        String firstTime, String lastTime, String[] header, String[] properties,
                                        Pageable pageable, ModelMap model, Integer page){
		List<Object> params = new ArrayList<Object>();
		params.add(storeId);
		params.add(createName);
		params.add(firstTime);
		params.add(lastTime);
    	Map<String, Integer> segments = getSegment();
		int size = segments.get("size");
		List<Map<String, Object>> data = reorganizeService.findList(r, params, pageable, page, size);
		return getModelAndViewForList(data,header,properties, model);
    }
    
    /**
	 * 条件导出
	 * 
	 */
	@RequestMapping(value = "/to_condition_export", method = RequestMethod.POST)
	public @ResponseBody List<Map<String, Object>> toConditionExport(Reorganize r, Long storeId, String createName,
                                                                     String firstTime, String lastTime){
		List<Object> params = new ArrayList<Object>();
		params.add(storeId);
		params.add(createName);
		params.add(firstTime);
		params.add(lastTime);
		int size = reorganizeService.count(r,params);
		List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
		Map<String, Integer> segments = getSegment();
		int stage = segments.get("segment");
		int page_size = segments.get("size");
		for (int i = 0; i < stage; i++) {
			Map<String, Object> map = new HashMap<String, Object>();
			int total = i * page_size;
			if (size == 0) {
				map.put("data", "0-0");
			} else if ((size - total) <= page_size) {
				map.put("data", (total + 1) + "-" + size);
			} else {
				map.put("data", (total + 1) + "-" + (i + 1) * page_size);
			}
			lists.add(map);
			if ((size - total) <= page_size) {
				break;
			}
		}
		return lists;
	}
	
	public ModelAndView getModelAndViewForList(List<Map<String, Object>> data,String[] header,String[] properties, ModelMap model) {
		String filename = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + ".xls";
		Integer[] widths = {};
		if(header==null){
			header = new String[]{};
		}
		if(properties==null){
			properties = new String[]{};
		}else{
			List<Integer> w = new ArrayList<Integer>();
			for(int i=0;i<properties.length;i++){
				w.add(25 * 256);
			}
			widths = w.toArray(new Integer[]{w.size()});
		}
		return new ModelAndView(new ExcelView(filename, null, properties, header, widths, null, data, null), model);
	}
    
	private Map<String, Integer> getSegment() {
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		try {
			String value = SystemConfig.getConfig("SegmentExportConfig", WebUtils.getCurrentCompanyInfoId());
			String[] values = value.split(",");
			map.put("segment", Integer.parseInt(values[0]));
			map.put("size", Integer.parseInt(values[1]));
		} catch (Exception e) {
			map.put("segment", 10);
			map.put("size", 1000);
		}
		return map;
	}

}
