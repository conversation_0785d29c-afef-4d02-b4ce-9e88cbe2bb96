package net.shopxx.shop.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.shop.service.CustomerInfoService;

/**
 * 顾客列表
 */
@Controller("customerInfoController")
@RequestMapping("/shop/customer_info")
public class CustomerInfoController extends BaseController {
    
    @Resource(name = "customerInfoServiceImpl")
    private CustomerInfoService customerInfoService;
    
    @RequestMapping(value = "/list_tb", method = RequestMethod.GET)
    public String list_tb(Pageable pageable, ModelMap model) {
        return "/shop/customer_info/list_tb";
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(String sn, Pageable pageable, ModelMap model) {
        return "/shop/customer_info/list";
    }
    
    /**
     * 列表数据
     */
    @ResponseBody
    @RequestMapping(value = "/list_data", method = RequestMethod.POST)
    public ResultMsg list_data(String name, String phone, Pageable pageable) {
        List<Object> param = new ArrayList<Object>();
        param.add(name);
        param.add(phone);
        Page<Map<String, Object>> page = customerInfoService.findPage(param, pageable);
        String jsonPage = JsonUtils.toJson(page);
        return success(jsonPage);
    }

}
