package net.shopxx.shop.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.entity.SystemDict;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.ShopDecrease;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopDecreaseService;
import net.shopxx.shop.service.ShopInfoService;

/**
 * Controller - 门店减少
 */
@Controller("shopDecreaseController")
@RequestMapping("/shop/decrease")
public class ShopDecreaseController extends BaseController{
	
	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "shopAddedServiceImpl")
	private ShopAddedService shopAddedService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "shopDecreaseServiceImpl")
	private ShopDecreaseService shopDecreaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	
	@RequestMapping(value = "/list_tb", method = RequestMethod.GET)
	public String list_tb(Long shopInfoId,Long objTypeId, Long objid,Pageable pageable, ModelMap model) {
		model.addAttribute("id",shopInfoId);
		model.addAttribute("objTypeId", objTypeId);
		model.addAttribute("objid", objid);
		return "/shop/decrease/list_tb";
	}
	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(String sn,Pageable pageable, ModelMap model) {
		return "/shop/decrease/list";
	}
	
	/**
	 * 列表数据
	 */
	@RequestMapping(value = "/list_data", method = RequestMethod.POST)
	public @ResponseBody ResultMsg list_data(Pageable pageable) {
		Page<Map<String, Object>> page = shopDecreaseService.findPage(null,pageable);
		String jsonPage = JsonUtils.toJson(page);
 		return success(jsonPage);

	}

	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(Long shopInfoId,ModelMap model) {
		List<Filter> filters = new ArrayList<Filter>();
		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		ShopInfo shopInfo = shopInfoService.find(shopInfoId);
		Store store = shopInfo.getStore();
		filters.add(Filter.eq("code", "businessCategory"));
		filters.add(Filter.eq("companyInfoId", companyInfo.getId()));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> businessCategoryList = systemDictService.findList(null,
				filters,
				null);
		model.addAttribute("BusinessCategory",businessCategoryList);
		model.addAttribute("shopInfo",shopInfo);
		model.addAttribute("store",store);
		return "/shop/decrease/add";
	}
	
	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		ShopDecrease shopDecrease =shopDecreaseService.find(id);
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		model.addAttribute("shopDecrease",shopDecrease);
		ShopInfo shopInfo = null;
		if(shopDecrease!=null){
			shopInfo = shopDecrease.getShopInfo();
			model.addAttribute("shopInfo",shopInfo);
		}
		List<Filter> filters = new ArrayList<Filter>();
		filters.clear();
		filters.add(Filter.eq("code", "shopSign"));
		filters.add(Filter.eq("companyInfoId",  companyInfoBaseService.getCurrent().getId()));
		filters.add(Filter.isNotNull("parent"));
		List<SystemDict> shopSignList = systemDictService.findList(null, filters, null);
		model.addAttribute("shopSign", shopSignList);

		// 获取流程节点
		if (shopDecrease.getWfId() != null) {
			//获取到当前流程
			ActWf wf = shopDecreaseService.getWfByWfId(shopDecrease.getWfId());
			if (wf != null) {
				//区域经理
				Boolean qyjl = false;
				//省长审核
				Boolean szsh = false;
				//渠道专员
				Boolean qdzy = false;
				//区域经理权限
				Boolean qyjls = false;
				//省长审核权限
				Boolean szshs = false;
				//渠道专员权限
				Boolean qdzys = false;
				// 查找当前流程明细
				List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
				for(Map<String, Object> c : item){
					if(c.get("suggestion")!=null){
						//处理结果
						String approved = c.get("approved")!=null?c.get("approved").toString():"false";
						//节点名称
						String rwm = c.get("activityName")!=null?c.get("activityName").toString():"";
						//对比节点名称是否对应
						if(rwm.contains("区域经理")){
							//赋值处理结果到所定义的节点上
							qyjl = Boolean.valueOf(approved);
						}
						if(rwm.contains("省长")){
							szsh = Boolean.valueOf(approved);
							//如果父节点驳回改变上一个节点状态
							if(!szsh){
								qyjl = false;
							}
						}
						if(rwm.contains("渠道")){
							qdzy = Boolean.valueOf(approved);
							if(!qdzy){
								szsh = false;
							}
						}
					}
				}
				//获取当前流程所在的节点
				Task t = shopAddedService.getCurrTaskByWf(wf);
				if(t!=null){
					//获取当前节点所有用户id
					List<String> userId = actWfService.getTaskUsers(t.getId());
					if(userId.contains(storeMember.getId().toString())&&t.getName().contains("区域经理")){
						qyjls = true;
					}
					if(userId.contains(storeMember.getId().toString())&&t.getName().contains("省长")){
						szshs = true;qyjls = true;
					}
					if(userId.contains(storeMember.getId().toString())&&t.getName().contains("渠道")){
						szshs = true;qyjls = true;qdzys = true;
					}
				}
				model.addAttribute("wf", wf);
				model.addAttribute("node", t);
				model.addAttribute("qyjl", qyjl);
				model.addAttribute("szsh", szsh);
				model.addAttribute("qdzy", qdzy);
				model.addAttribute("qyjls", qyjls);
				model.addAttribute("szshs", szshs);
				model.addAttribute("qdzys", qdzys);
			}
		}
		return "/shop/decrease/edit";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public @ResponseBody ResultMsg save(ShopDecrease shopDecrease,Long storeId,Long shopInfoId) {
		if(shopInfoId==null||shopInfoId.equals("")){
			return error("门店不能为空");
		}
		shopDecreaseService.saveDecrease(shopDecrease, storeId, shopInfoId);
		return success().addObjX(shopDecrease.getId());
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public @ResponseBody ResultMsg update(ShopDecrease shopDecrease,Long storeId,Long shopInfoId) {
		if(shopInfoId==null||shopInfoId.equals("")){
			return error("门店不能为空");
		}
		shopDecreaseService.updateDecrease(shopDecrease, storeId, shopInfoId);
		return success().addObjX(shopDecrease.getId());
	}
	
	
	/**
	 * 审核
	 * @param shopId
	 * @return
	 */
	@RequestMapping(value = "/check_wf", method = RequestMethod.POST)
	public @ResponseBody
	ResultMsg check_wf(Long id,String modelId,Long objTypeId) {
		if (id == null) {
			// 请选择订单
			return error("请选择门店单据");
		}
		shopDecreaseService.createWf(id, modelId, objTypeId);
		return success();
	}
	
	/**
	 * 流程节点保存
	 */
	@RequestMapping(value = "/saveform", method = RequestMethod.POST)
	public @ResponseBody ResultMsg saveform(ShopDecrease shopDecrease,Integer type) {
		// type 用来定义节点
		//type 1区域经理 2渠道部
		shopDecreaseService.saveform(shopDecrease,type);
		return success().addObjX(shopDecrease.getId());
	}
}
