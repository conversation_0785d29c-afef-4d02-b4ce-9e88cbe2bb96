package net.shopxx.shop.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.member.entity.Store;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 整改
 */
@Entity
@Table(name = "xx_reorganize")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_reorganize_sequence")
public class Reorganize extends ActWfBillEntity {

    private static final long serialVersionUID = 4237670068410294170L;

    /** 单号 */
    private String sn;

    /** 客户 */
    private Store store;

    /** 状态 0已保存 1已提交 2已终止 3进行中 4已完成 */
    private Integer status;

    /** 经销商授权编号 */
    private String dealerAccredit;

    /** 区域 0：东，1：南，2：西，3：北 */
    private Integer district;

    /** 地区 */
    private String region;

    /** 经销商姓名 */
    private String dealerName;

    /** 公司发出函件 0:整改通知函, 1:不续约通知, 2:整改通知书, 3:终止通知书 */
    private Integer letters;

    /** 是否签订今年合同 1:是，0:否 */
    private Integer whetherSignedContract;

    /** 省长是否签字 1:是，0:否 */
    private Integer whetherSignature;

    /** 是否提交计划 1:是，0:否 */
    private Integer whetherSubmitPlan;

    /** 整改内容 */
    private String rectificationContent;

    /** 省长意见 */
    private String governorOpinion;

    /** 渠道部意见 */
    private String channelOpinion;

    /** 档案文号 */
    private String archivesNo;

    /** 文件编号 */
    private String fileNo;

    /** 整改期限 */
    private Date reorganizeTimeLimit;

    /** 经销商收到时间 */
    private Date dealerReceivedTime;

    /** 原件发出时间 */
    private Date sendOutTime;

    /** 收件人 */
    private String consignee;

    /** 收件人类别 0：经销商，1：区域经理 */
    private Integer consigneeType;

    /** 联系电话 */
    private String consigneePhone;

    /** 快递单号 */
    private String trackingNumber;

    /** 快递签收时间 */
    private Date courierReceivedTime;

    /** 是否有回执单 1:是，0:否 */
    private Integer whetherReceipt;

    /** 快递地址 */
    private String expressAddress;

    /** 是否终止经销商 1:是，0:否 */
    private Integer whetherStopDealer;

    /** 意见 */
    private String opinion;
    
    /**快递省市区*/
    private Area expressArea;

    /**销售中心意见 */
    private String salesCenterOpinion;

    /**事业部意事业 */
    private String businessOpinion;


    /** 4个附件 */
    private List<ReorganizeAttach> reorganized0Attachs = new ArrayList<ReorganizeAttach>();
    private List<ReorganizeAttach> reorganized1Attachs = new ArrayList<ReorganizeAttach>();
    private List<ReorganizeAttach> reorganized2Attachs = new ArrayList<ReorganizeAttach>();
    private List<ReorganizeAttach> reorganized3Attachs = new ArrayList<ReorganizeAttach>();
    private List<ReorganizeAttach> reorganized4Attachs = new ArrayList<ReorganizeAttach>();
    /**回执单附件，在选择 “是否有回执单”为 “有” 的情况下才展示此附件 */
    private List<ReorganizeAttach> receiptAttachs = new ArrayList<ReorganizeAttach>();




    @Column(nullable = false, updatable = false, unique = true, length = 100)
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Store getStore() {
        return store;
    }

    public void setStore(Store store) {
        this.store = store;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDealerAccredit() {
        return dealerAccredit;
    }

    public void setDealerAccredit(String dealerAccredit) {
        this.dealerAccredit = dealerAccredit;
    }

    public Integer getDistrict() {
        return district;
    }

    public void setDistrict(Integer district) {
        this.district = district;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public Integer getLetters() {
        return letters;
    }

    public void setLetters(Integer letters) {
        this.letters = letters;
    }

    public Integer getWhetherSignedContract() {
        return whetherSignedContract;
    }

    public void setWhetherSignedContract(Integer whetherSignedContract) {
        this.whetherSignedContract = whetherSignedContract;
    }

    public Integer getWhetherSignature() {
        return whetherSignature;
    }

    public void setWhetherSignature(Integer whetherSignature) {
        this.whetherSignature = whetherSignature;
    }

    public Integer getWhetherSubmitPlan() {
        return whetherSubmitPlan;
    }

    public void setWhetherSubmitPlan(Integer whetherSubmitPlan) {
        this.whetherSubmitPlan = whetherSubmitPlan;
    }

    public String getRectificationContent() {
        return rectificationContent;
    }

    public void setRectificationContent(String rectificationContent) {
        this.rectificationContent = rectificationContent;
    }

    public String getGovernorOpinion() {
        return governorOpinion;
    }

    public void setGovernorOpinion(String governorOpinion) {
        this.governorOpinion = governorOpinion;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public Area getExpressArea() {
		return expressArea;
	}

	public void setExpressArea(Area expressArea) {
		this.expressArea = expressArea;
	}

	public String getChannelOpinion() {
        return channelOpinion;
    }

    public void setChannelOpinion(String channelOpinion) {
        this.channelOpinion = channelOpinion;
    }

    public String getArchivesNo() {
        return archivesNo;
    }

    public void setArchivesNo(String archivesNo) {
        this.archivesNo = archivesNo;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public Date getReorganizeTimeLimit() {
        return reorganizeTimeLimit;
    }

    public void setReorganizeTimeLimit(Date reorganizeTimeLimit) {
        this.reorganizeTimeLimit = reorganizeTimeLimit;
    }

    public Date getDealerReceivedTime() {
        return dealerReceivedTime;
    }

    public void setDealerReceivedTime(Date dealerReceivedTime) {
        this.dealerReceivedTime = dealerReceivedTime;
    }

    public Date getSendOutTime() {
        return sendOutTime;
    }

    public void setSendOutTime(Date sendOutTime) {
        this.sendOutTime = sendOutTime;
    }

    public String getConsignee() {
        return consignee;
    }

    public void setConsignee(String consignee) {
        this.consignee = consignee;
    }

    public Integer getConsigneeType() {
        return consigneeType;
    }

    public void setConsigneeType(Integer consigneeType) {
        this.consigneeType = consigneeType;
    }

    public String getConsigneePhone() {
        return consigneePhone;
    }

    public void setConsigneePhone(String consigneePhone) {
        this.consigneePhone = consigneePhone;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public Date getCourierReceivedTime() {
        return courierReceivedTime;
    }

    public void setCourierReceivedTime(Date courierReceivedTime) {
        this.courierReceivedTime = courierReceivedTime;
    }

    public Integer getWhetherReceipt() {
        return whetherReceipt;
    }

    public void setWhetherReceipt(Integer whetherReceipt) {
        this.whetherReceipt = whetherReceipt;
    }

    public String getExpressAddress() {
        return expressAddress;
    }

    public void setExpressAddress(String expressAddress) {
        this.expressAddress = expressAddress;
    }

    public Integer getWhetherStopDealer() {
        return whetherStopDealer;
    }

    public void setWhetherStopDealer(Integer whetherStopDealer) {
        this.whetherStopDealer = whetherStopDealer;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ReorganizeAttach> getReorganized0Attachs() {
        return reorganized0Attachs;
    }

    public void setReorganized0Attachs(List<ReorganizeAttach> reorganized0Attachs) {
        this.reorganized0Attachs = reorganized0Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ReorganizeAttach> getReorganized1Attachs() {
        return reorganized1Attachs;
    }

    public void setReorganized1Attachs(List<ReorganizeAttach> reorganized1Attachs) {
        this.reorganized1Attachs = reorganized1Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ReorganizeAttach> getReorganized2Attachs() {
        return reorganized2Attachs;
    }

    public void setReorganized2Attachs(List<ReorganizeAttach> reorganized2Attachs) {
        this.reorganized2Attachs = reorganized2Attachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ReorganizeAttach> getReorganized3Attachs() {
        return reorganized3Attachs;
    }

    public void setReorganized3Attachs(List<ReorganizeAttach> reorganized3Attachs) {
        this.reorganized3Attachs = reorganized3Attachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ReorganizeAttach> getReorganized4Attachs() {
        return reorganized4Attachs;
    }

    public void setReorganized4Attachs(List<ReorganizeAttach> reorganized4Attachs) {
        this.reorganized4Attachs = reorganized4Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ReorganizeAttach> getReceiptAttachs() {
        return this.receiptAttachs;
    }

    public void setReceiptAttachs(List<ReorganizeAttach> receiptAttachs) {
        this.receiptAttachs = receiptAttachs;
    }

    public String getSalesCenterOpinion() {
        return salesCenterOpinion;
    }

    public void setSalesCenterOpinion(String salesCenterOpinion) {
        this.salesCenterOpinion = salesCenterOpinion;
    }

    public String getBusinessOpinion() {
        return businessOpinion;
    }

    public void setBusinessOpinion(String businessOpinion) {
        this.businessOpinion = businessOpinion;
    }
}
