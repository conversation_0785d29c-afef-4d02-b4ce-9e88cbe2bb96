package net.shopxx.shop.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 门店设计
 */
@Entity
@Table(name = "xx_shop_devise")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_devise_sequence")
public class ShopDevise extends ActWfBillEntity {

    private static final long serialVersionUID = -1535633949401656200L;

    /** 单号 */
    private String sn;
    
    /**
     * 单据状态
     * 0：已保存
     * 1：已生效
     * 2：已终止
     * 3：进行中
     * 99: 作废
     */
    private Integer billsStatus;
    
    /** 机构 */
	private SaleOrg saleOrg;
    
    /** 申请城市 */
    private String applyCity;
    
    /** 设计品牌 */
    private String designBrand;
    
    /** 经销商 */
    private String dealer;
    
    /** 联系方式 */
    private String phone;
    
    /** 天花限高 */
    private BigDecimal highLimit;
    
    /** 面积 */
    private BigDecimal area;
    
    /** QQ/邮箱 */
    private String qqOrEmail;
    
    /** 门店 */
    private ShopInfo shopInfo;
    
    /** 门店名称 */
    private String shopInfoName;
    
    /** 门店编码 */
    private String shopInfoSn;
    
    /** 门店区域*/
    private Area deviseArea;
    
    /** 门店地址 */
    private String shopAddress;
    
    /** 预订施工时间 */
    private Date predictConstructionTime;
    
    /** 预订开业时间 */
    private Date predictStartsTime;
    
    /** 门店结构情况 1 */
    private String structure1;
    
    /** 门店结构情况 2 */
    private String structure2;
    
    /** 是否二次设计 */
    private String shopAttribute;
    
    /** 上样计划 */
    private String samplePlan;
    
    /** 上样占比 */
    // 实木
    private BigDecimal solidWoodRatio;
    
    // 多层
    private BigDecimal multilayerRatio;
    
    // 强化
    private BigDecimal intensifyRatio;
    
    /** 2、提供设计、参与装修报销, 1、提供设计、不参与报销 */
    private Integer reimburse;
    
    /** 经销商提交申请附件 */
    private List<ShopDeviseAttach> dealersAttachs = new ArrayList<ShopDeviseAttach>();
    private List<ShopDeviseAttach> storeContractAttachs = new ArrayList<ShopDeviseAttach>();
    private List<ShopDeviseAttach> storePicturesAttachs = new ArrayList<ShopDeviseAttach>();
    
    /** 区域经理审核附件 */
//    private List<ShopDeviseAttach> regionalManagerAttachs = new ArrayList<ShopDeviseAttach>();
    private List<ShopDeviseAttach> managerDealersAttachs = new ArrayList<ShopDeviseAttach>();
    private List<ShopDeviseAttach> managerStoreContractAttachs = new ArrayList<ShopDeviseAttach>();
    private List<ShopDeviseAttach> managerStorePicturesAttachs = new ArrayList<ShopDeviseAttach>();
    /** 设计部确认附件（设计图） */
    private List<ShopDeviseAttach> designAttachs = new ArrayList<ShopDeviseAttach>();
    
    /**
     * 省运营管理中心总经理意见
     * 0: 申请设计面积与事实相符
     * 1: 同意按公司SI标准设计
     */
    private String regionalManagerOpinion;
    
    /** 省运营管理中心总经理备注 */
    private String regionalManagerMemo;
    
    /**
     * 地板事业部渠道部审核
     * 0：2年内未参与装修报销
     * 1：签署2019年经销合同
     * 2：缴纳品牌保证金
     */
    private String floorDivision;
    
    /** 地板事业部渠道部备注 */
    private String floorDivisionMemo;
    
    /** 设计部意见 */
    private String designOpinion;
    
    /**
     * 经销商，设计图是否满意
     * 0：否
     * 1：是
     */
    private Integer dealersSatisfied;
    
    /** 装修开始日期 */
    private Date decorateStartDate;
    
    /** 经销商意见 */
    private String dealersOpinion;
    
    /** 门店装修属性 */
    private String shopRenovationAttribute;
    
    /** 审核通过时间*/
    private Date transitTime;

    /** 设计状态 */
    private String deviseState;

    /** 改变设计状态时间 */
    private Date changeDeviseStateTime;

    /** 设计师提交时间 */
    private Date designerSubmitTime;

    /** 最后执行人 */
    private StoreMember lastOperator;

    /** 设计师 */
    private StoreMember designer;
    
    /** 记录门店装修状态*/
    private String decorationStatus;
    
    /** 记录门店设计状态*/
    private String deviseStatus;

    /** 平面图完成时间*/
    private Date plansFinishedTime;

    /** 施工图完成时间*/
    private Date workingDrawingFinishedTime;

    @Column(nullable = false, updatable = false, unique = true, length = 100)
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getBillsStatus() {
        return billsStatus;
    }

    public void setBillsStatus(Integer billsStatus) {
        this.billsStatus = billsStatus;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	public String getApplyCity() {
        return applyCity;
    }

    public void setApplyCity(String applyCity) {
        this.applyCity = applyCity;
    }

    public String getDesignBrand() {
        return designBrand;
    }

    public void setDesignBrand(String designBrand) {
        this.designBrand = designBrand;
    }

    public String getDealer() {
        return dealer;
    }

    public void setDealer(String dealer) {
        this.dealer = dealer;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public BigDecimal getHighLimit() {
        return highLimit;
    }

    public void setHighLimit(BigDecimal highLimit) {
        this.highLimit = highLimit;
    }

    public BigDecimal getArea() {
        return area;
    }

    public void setArea(BigDecimal area) {
        this.area = area;
    }

    public String getQqOrEmail() {
        return qqOrEmail;
    }

    public void setQqOrEmail(String qqOrEmail) {
        this.qqOrEmail = qqOrEmail;
    }

    public String getShopAddress() {
        return shopAddress;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public ShopInfo getShopInfo() {
        return shopInfo;
    }

    public void setShopInfo(ShopInfo shopInfo) {
        this.shopInfo = shopInfo;
    }

    public String getShopInfoName() {
        return shopInfoName;
    }

    public void setShopInfoName(String shopInfoName) {
        this.shopInfoName = shopInfoName;
    }

    public String getShopInfoSn() {
        return shopInfoSn;
    }

    public void setShopInfoSn(String shopInfoSn) {
        this.shopInfoSn = shopInfoSn;
    }

    public String getShopAttribute() {
        return shopAttribute;
    }

    public void setShopAttribute(String shopAttribute) {
        this.shopAttribute = shopAttribute;
    }

    public void setShopAddress(String shopAddress) {
        this.shopAddress = shopAddress;
    }

    public Date getPredictConstructionTime() {
        return predictConstructionTime;
    }

    public void setPredictConstructionTime(Date predictConstructionTime) {
        this.predictConstructionTime = predictConstructionTime;
    }

    public Date getPredictStartsTime() {
        return predictStartsTime;
    }

    public void setPredictStartsTime(Date predictStartsTime) {
        this.predictStartsTime = predictStartsTime;
    }

    public String getStructure1() {
        return structure1;
    }

    public void setStructure1(String structure1) {
        this.structure1 = structure1;
    }

    public String getStructure2() {
        return structure2;
    }

    public void setStructure2(String structure2) {
        this.structure2 = structure2;
    }

    public String getSamplePlan() {
        return samplePlan;
    }

    public void setSamplePlan(String samplePlan) {
        this.samplePlan = samplePlan;
    }

    public BigDecimal getSolidWoodRatio() {
        return solidWoodRatio;
    }

    public void setSolidWoodRatio(BigDecimal solidWoodRatio) {
        this.solidWoodRatio = solidWoodRatio;
    }

    public BigDecimal getMultilayerRatio() {
        return multilayerRatio;
    }

    public void setMultilayerRatio(BigDecimal multilayerRatio) {
        this.multilayerRatio = multilayerRatio;
    }

    public BigDecimal getIntensifyRatio() {
        return intensifyRatio;
    }

    public void setIntensifyRatio(BigDecimal intensifyRatio) {
        this.intensifyRatio = intensifyRatio;
    }
    
    public Integer getReimburse() {
        return reimburse;
    }

    public void setReimburse(Integer reimburse) {
        this.reimburse = reimburse;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopDeviseAttach> getDealersAttachs() {
        return dealersAttachs;
    }

    public void setDealersAttachs(List<ShopDeviseAttach> dealersAttachs) {
        this.dealersAttachs = dealersAttachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopDeviseAttach> getStoreContractAttachs() {
        return storeContractAttachs;
    }

    public void setStoreContractAttachs(List<ShopDeviseAttach> storeContractAttachs) {
        this.storeContractAttachs = storeContractAttachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopDeviseAttach> getStorePicturesAttachs() {
        return storePicturesAttachs;
    }

    public void setStorePicturesAttachs(List<ShopDeviseAttach> storePicturesAttachs) {
        this.storePicturesAttachs = storePicturesAttachs;
    }

//    @OneToMany(mappedBy = "shopDevise", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopDeviseAttach> getManagerStorePicturesAttachs() {
        return managerStorePicturesAttachs;
    }

    public void setManagerStorePicturesAttachs(List<ShopDeviseAttach> managerStorePicturesAttachs) {
        this.managerStorePicturesAttachs = managerStorePicturesAttachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopDeviseAttach> getManagerStoreContractAttachs() {
        return managerStoreContractAttachs;
    }

    public void setManagerStoreContractAttachs(List<ShopDeviseAttach> managerStoreContractAttachs) {
        this.managerStoreContractAttachs = managerStoreContractAttachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopDeviseAttach> getManagerDealersAttachs() {
        return managerDealersAttachs;
    }

    public void setManagerDealersAttachs(List<ShopDeviseAttach> managerDealersAttachs) {
        this.managerDealersAttachs = managerDealersAttachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopDeviseAttach> getDesignAttachs() {
        return designAttachs;
    }

    public void setDesignAttachs(List<ShopDeviseAttach> designAttachs) {
        this.designAttachs = designAttachs;
    }

    public String getRegionalManagerOpinion() {
        return regionalManagerOpinion;
    }

    public void setRegionalManagerOpinion(String regionalManagerOpinion) {
        this.regionalManagerOpinion = regionalManagerOpinion;
    }

    public String getRegionalManagerMemo() {
        return regionalManagerMemo;
    }

    public void setRegionalManagerMemo(String regionalManagerMemo) {
        this.regionalManagerMemo = regionalManagerMemo;
    }

    public String getFloorDivision() {
        return floorDivision;
    }

    public void setFloorDivision(String floorDivision) {
        this.floorDivision = floorDivision;
    }

    public String getFloorDivisionMemo() {
        return floorDivisionMemo;
    }

    public void setFloorDivisionMemo(String floorDivisionMemo) {
        this.floorDivisionMemo = floorDivisionMemo;
    }

    public String getDesignOpinion() {
        return designOpinion;
    }

    public void setDesignOpinion(String designOpinion) {
        this.designOpinion = designOpinion;
    }

    public Integer getDealersSatisfied() {
        return dealersSatisfied;
    }

    public void setDealersSatisfied(Integer dealersSatisfied) {
        this.dealersSatisfied = dealersSatisfied;
    }

    public Date getDecorateStartDate() {
        return decorateStartDate;
    }

    public void setDecorateStartDate(Date decorateStartDate) {
        this.decorateStartDate = decorateStartDate;
    }

    public String getDealersOpinion() {
        return dealersOpinion;
    }

    public void setDealersOpinion(String dealersOpinion) {
        this.dealersOpinion = dealersOpinion;
    }
    
	@ManyToOne(fetch = FetchType.LAZY)
    public Area getDeviseArea(){
    	return deviseArea;
    }
    
    public void setDeviseArea(Area deviseArea){
    	this.deviseArea = deviseArea;
    }
    public String getShopRenovationAttribute() {
        return shopRenovationAttribute;
    }

    public void setShopRenovationAttribute(String shopRenovationAttribute) {
        this.shopRenovationAttribute = shopRenovationAttribute;
    }
    
    public Date getTransitTime() {
        return transitTime;
    }

    public void setTransitTime(Date transitTime) {
        this.transitTime = transitTime;
    }

    public String getDeviseState() {
        return deviseState;
    }

    public void setDeviseState(String deviseState) {
        this.deviseState = deviseState;
    }

    public Date getChangeDeviseStateTime() {
        return changeDeviseStateTime;
    }

    public void setChangeDeviseStateTime(Date changeDeviseStateTime) {
        this.changeDeviseStateTime = changeDeviseStateTime;
    }

    public Date getDesignerSubmitTime() {
        return designerSubmitTime;
    }

    public void setDesignerSubmitTime(Date designerSubmitTime) {
        this.designerSubmitTime = designerSubmitTime;
    }
    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getLastOperator() {
        return lastOperator;
    }

    public void setLastOperator(StoreMember lastOperator) {
        this.lastOperator = lastOperator;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getDesigner() {
        return designer;
    }

    public void setDesigner(StoreMember designer) {
        this.designer = designer;
    }

	public String getDecorationStatus() {
		return decorationStatus;
	}

	public void setDecorationStatus(String decorationStatus) {
		this.decorationStatus = decorationStatus;
	}

	public String getDeviseStatus() {
		return deviseStatus;
	}

	public void setDeviseStatus(String deviseStatus) {
		this.deviseStatus = deviseStatus;
	}

    public Date getPlansFinishedTime() {
        return plansFinishedTime;
    }

    public void setPlansFinishedTime(Date plansFinishedTime) {
        this.plansFinishedTime = plansFinishedTime;
    }

    public Date getWorkingDrawingFinishedTime() {
        return workingDrawingFinishedTime;
    }

    public void setWorkingDrawingFinishedTime(Date workingDrawingFinishedTime) {
        this.workingDrawingFinishedTime = workingDrawingFinishedTime;
    }
}
