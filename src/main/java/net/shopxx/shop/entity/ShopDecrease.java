package net.shopxx.shop.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.Store;
/**
 * Entity - 门店减少
 */
@Entity
@Table(name = "xx_shop_decrease")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_decrease_sequence")
public class ShopDecrease extends ActWfBillEntity{

	private static final long serialVersionUID = 942182357891L;
	
	/**减少单号*/
	private String sn;
	
	/**状态*/
	public enum Status {

		/** 已保存 */
		saved,

		/** 已提交 */
		submitted,

		/** 已终止 */
		terminated,

		/** 进行中 */
		underway,
	}
	
	/**单据状态*/
	private Status statuss;
	
	/**客户*/
	private Store store;
	
	/** 机构 */
	private SaleOrg saleOrg;
	
	/**门店资料*/
	private ShopInfo shopInfo;
	
	/**操作类型*/
	private String operationType;
	
	/**变更类型*/
	private String changeType;
	
	/**门店关闭原因*/
	private String closeReason;
	
	/**关店日期*/
	private Date closingTime;
	
	//区域经理
	/**门店类型*/ //门店类别
	private String type;
	
	/**所属销售平台*/
	private String salesPlatform;

	/**所属品牌*/
	private String belongBrand;
	
	/**VI版本*/
	private String viVersion;
	
	/**所属部门*/
	private String department;
	
	/**关闭原因*/
	private String shutDownMenu;
		
	/**销售渠道*/
	private String salesChannel;
	
	//渠道部意见
	/**门店授权编号*/
	private String authorizationCode;

	/**新增档案编号*/
	private String increaseArchivesCode;

	/**新增时间*/
	private Date newTime;

	/**减少档案编号*/
	private String decreaseArchivesCode;

	/**减少时间*/
	private Date decreaseTime;

	/**门店情况备注*/
	private String shopCaseNote;

	/** 渠道部填写的门店关闭原因*/
	private String shopClosedReason;
	
	@Column(nullable = false, updatable = false, unique = true, length = 100)
	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getCloseReason() {
		return closeReason;
	}

	public void setCloseReason(String closeReason) {
		this.closeReason = closeReason;
	}
	
	public Date getClosingTime() {
		return closingTime;
	}

	public void setClosingTime(Date closingTime) {
		this.closingTime = closingTime;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}
	
	public void setStore(Store store) {
		this.store = store;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	/**
	 * 获取状态
	 * 
	 * @return 状态
	 */
	@Column(nullable = false)
	public Status getStatuss() {
		return statuss;
	}

	/**
	 * 设置状态
	 * 
	 * @param statuss
	 *            状态
	 */
	public void setStatuss(Status statuss) {
		this.statuss = statuss;
	}


	@ManyToOne(fetch = FetchType.LAZY)
	public ShopInfo getShopInfo() {
		return shopInfo;
	}
	
	public void setShopInfo(ShopInfo shopInfo) {
		this.shopInfo = shopInfo;
	}
	

	public String getChangeType() {
		return changeType;
	}


	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getSalesPlatform() {
		return salesPlatform;
	}

	public void setSalesPlatform(String salesPlatform) {
		this.salesPlatform = salesPlatform;
	}

	public String getBelongBrand() {
		return belongBrand;
	}

	public void setBelongBrand(String belongBrand) {
		this.belongBrand = belongBrand;
	}

	public String getViVersion() {
		return viVersion;
	}

	public void setViVersion(String viVersion) {
		this.viVersion = viVersion;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getShutDownMenu() {
		return shutDownMenu;
	}

	public void setShutDownMenu(String shutDownMenu) {
		this.shutDownMenu = shutDownMenu;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getAuthorizationCode() {
		return authorizationCode;
	}

	public void setAuthorizationCode(String authorizationCode) {
		this.authorizationCode = authorizationCode;
	}

	public String getIncreaseArchivesCode() {
		return increaseArchivesCode;
	}

	public void setIncreaseArchivesCode(String increaseArchivesCode) {
		this.increaseArchivesCode = increaseArchivesCode;
	}

	public Date getNewTime() {
		return newTime;
	}

	public void setNewTime(Date newTime) {
		this.newTime = newTime;
	}

	public String getDecreaseArchivesCode() {
		return decreaseArchivesCode;
	}

	public void setDecreaseArchivesCode(String decreaseArchivesCode) {
		this.decreaseArchivesCode = decreaseArchivesCode;
	}

	public Date getDecreaseTime() {
		return decreaseTime;
	}

	public void setDecreaseTime(Date decreaseTime) {
		this.decreaseTime = decreaseTime;
	}

	public String getShopCaseNote() {
		return shopCaseNote;
	}

	public void setShopCaseNote(String shopCaseNote) {
		this.shopCaseNote = shopCaseNote;
	}

	public String getShopClosedReason() {
		return shopClosedReason;
	}

	public void setShopClosedReason(String shopClosedReason) {
		this.shopClosedReason = shopClosedReason;
	}
}
