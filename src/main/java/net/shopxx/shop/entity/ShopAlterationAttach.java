package net.shopxx.shop.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;

/**
 * Entity - 门店变更附件
 */
@Entity
@Table(name = "xx_shop_alteration_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_alteration_attach_sequence")
public class ShopAlterationAttach extends BaseEntity{

	private static final long serialVersionUID = 458799656452354656L;
	
	private ShopAlteration alteration;
	
	/** 附件URL */
	private String url;

	/** 备注 */
	private String memo;

	/** 文件名 */
	private String fileName;
	
	/**文件名*/
	private String name;
	
	/**文件后缀*/
	private String suffix;

	/** 序号 */
	private Integer seq;

	/** 上传人 */
	private StoreMember storeMember;
	
	private Integer type;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "alterations", nullable = false, updatable = false)
	public ShopAlteration getAlteration() {
		return alteration;
	}


	public void setAlteration(ShopAlteration alteration) {
		this.alteration = alteration;
	}


	public String getUrl() {
		return url;
	}




	public void setUrl(String url) {
		this.url = url;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSuffix() {
		return suffix;
	}

	public void setSuffix(String suffix) {
		this.suffix = suffix;
	}

	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	@JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
