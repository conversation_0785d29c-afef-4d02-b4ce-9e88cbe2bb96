package net.shopxx.shop.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.member.entity.Store;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 侵权
 */
@Entity
@Table(name="xx_tort")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_tort_sequence")
public class Tort extends ActWfBillEntity{

	private static final long serialVersionUID = 102735917L;
	
	/**编号*/
	private String sn;
	
	/**被侵权客户*/
	private Store store;
	
	/**区域*/
	private String area;
	
	/**侵权门店省市区*/
	private Area tortArea;

    /** 状态 0已保存 1已生效 2已终止 3进行中  */
    private Integer status;
	
	/**侵权门店地址*/
	private String tortAddress;
	
	/**侵权人姓名*/
	private String tortName;

	/** 侵权人 */
	private Store tortStore;
	
	/**退出时间*/
	private Date quitTime;
	
	/**侵权事项*/
	private String tortMatter;
	
	/**加盟时间*/
	private String joinTime;
	
	/**区域负责人*/
	private String principal;
	
	/**收件人*/
	private String recipients;
	
	/**收件人类别*/
	private String recipientType;
	
	/**联系方式*/
	private String contactWay;
	
	/**快递省市区*/
	private Area express;
	
	/**快递地址*/
	private String expressAddress;
	
	/**侵权处理途径*/
	private String torthp;
	
	/**当地工商局名称*/
	private String aicName;
	
	/**授权人姓名*/
	private String authorizationName;
	
	/**身份证信息*/
	private String card;
	
	/**商场管理方经营执照名称*/
	private String businessLicense;
	
	/**省长意见*/
	private String szyj;
	
	/**文件编号*/
	private String code;
	
	/**处理明细*/
	private String mx;
	
	/**备注*/
	private String memo;
	
	/**函件发出时间*/
	private Date lettersTime;
	
	/**快递单号*/
	private String trackingNumber;
	
	/**处理结果*/
	private String resultOfHandling;
	
	/**投诉书*/
	private List<TortAttach> tortAttachs = new ArrayList<TortAttach>();
	
	/**投诉书*/
	private List<ComplaintAttach> complaintAttachs = new ArrayList<ComplaintAttach>();
	
	/**律师函*/
	private List<NoticeAttach> noticeAttachs = new ArrayList<NoticeAttach>();
	
	/**被侵权人乡镇*/
	private String countryName;
	
	/**被侵权人详细地址*/
	private String headAddress;

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}

	public void setStore(Store store) {
		this.store = store;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Area getTortArea() {
		return tortArea;
	}

	public void setTortArea(Area tortArea) {
		this.tortArea = tortArea;
	}

	public String getTortAddress() {
		return tortAddress;
	}

	public void setTortAddress(String tortAddress) {
		this.tortAddress = tortAddress;
	}

	public String getTortName() {
		return tortName;
	}

	public void setTortName(String tortName) {
		this.tortName = tortName;
	}

	public Date getQuitTime() {
		return quitTime;
	}

	public void setQuitTime(Date quitTime) {
		this.quitTime = quitTime;
	}

	public String getTortMatter() {
		return tortMatter;
	}

	public void setTortMatter(String tortMatter) {
		this.tortMatter = tortMatter;
	}

	public String getJoinTime() {
		return joinTime;
	}

	public void setJoinTime(String joinTime) {
		this.joinTime = joinTime;
	}

	public String getPrincipal() {
		return principal;
	}

	public void setPrincipal(String principal) {
		this.principal = principal;
	}

	public String getRecipients() {
		return recipients;
	}

	public void setRecipients(String recipients) {
		this.recipients = recipients;
	}

	public String getRecipientType() {
		return recipientType;
	}

	public void setRecipientType(String recipientType) {
		this.recipientType = recipientType;
	}

	public String getContactWay() {
		return contactWay;
	}

	public void setContactWay(String contactWay) {
		this.contactWay = contactWay;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Area getExpress() {
		return express;
	}

	public void setExpress(Area express) {
		this.express = express;
	}

	public String getExpressAddress() {
		return expressAddress;
	}

	public void setExpressAddress(String expressAddress) {
		this.expressAddress = expressAddress;
	}

	public String getTorthp() {
		return torthp;
	}

	public void setTorthp(String torthp) {
		this.torthp = torthp;
	}

	public String getAicName() {
		return aicName;
	}

	public void setAicName(String aicName) {
		this.aicName = aicName;
	}

	public String getAuthorizationName() {
		return authorizationName;
	}

	public void setAuthorizationName(String authorizationName) {
		this.authorizationName = authorizationName;
	}

	public String getCard() {
		return card;
	}

	public void setCard(String card) {
		this.card = card;
	}

	public String getBusinessLicense() {
		return businessLicense;
	}

	public void setBusinessLicense(String businessLicense) {
		this.businessLicense = businessLicense;
	}

	public String getSzyj() {
		return szyj;
	}

	public void setSzyj(String szyj) {
		this.szyj = szyj;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMx() {
		return mx;
	}

	public void setMx(String mx) {
		this.mx = mx;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Date getLettersTime() {
		return lettersTime;
	}

	public void setLettersTime(Date lettersTime) {
		this.lettersTime = lettersTime;
	}

	public String getTrackingNumber() {
		return trackingNumber;
	}

	public void setTrackingNumber(String trackingNumber) {
		this.trackingNumber = trackingNumber;
	}

	public String getResultOfHandling() {
		return resultOfHandling;
	}

	public void setResultOfHandling(String resultOfHandling) {
		this.resultOfHandling = resultOfHandling;
	}
	
	@OneToMany(mappedBy = "tort", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<TortAttach> getTortAttachs() {
		return tortAttachs;
	}

	public void setTortAttachs(List<TortAttach> tortAttachs) {
		this.tortAttachs = tortAttachs;
	}

	@OneToMany(mappedBy = "tort", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<ComplaintAttach> getComplaintAttachs() {
		return complaintAttachs;
	}

	public void setComplaintAttachs(List<ComplaintAttach> complaintAttachs) {
		this.complaintAttachs = complaintAttachs;
	}

	@OneToMany(mappedBy = "tort", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<NoticeAttach> getNoticeAttachs() {
		return noticeAttachs;
	}

	public void setNoticeAttachs(List<NoticeAttach> noticeAttachs) {
		this.noticeAttachs = noticeAttachs;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Store getTortStore() {
        return tortStore;
    }

    public void setTortStore(Store tortStore) {
        this.tortStore = tortStore;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getHeadAddress() {
		return headAddress;
	}

	public void setHeadAddress(String headAddress) {
		this.headAddress = headAddress;
	}
}
