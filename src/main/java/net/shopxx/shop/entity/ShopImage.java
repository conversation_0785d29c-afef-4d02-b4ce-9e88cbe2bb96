package net.shopxx.shop.entity;

import org.apache.commons.lang.builder.CompareToBuilder;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.validation.constraints.Min;
import java.io.Serializable;

@Embeddable
public class ShopImage implements Serializable, Comparable<ShopImage>{

	private static final long serialVersionUID = 23523521L;
	
	/** 原图片 */
	private String source;
	
	/** 排序 */
	private Integer order;
	
	/** 缩略图 */
	private String thumbnail;
	
	/** 点击图片跳转路径*/
	private String url;

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Min(0)
	@Column(name = "orders")
	public Integer getOrder() {
		return order;
	}

	public void setOrder(Integer order) {
		this.order = order;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Override
	public int compareTo(ShopImage o) {
		return new CompareToBuilder().append(getOrder(),
				o.getOrder()).toComparison();
	}
	
	

}
