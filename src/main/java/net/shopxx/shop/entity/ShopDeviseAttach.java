package net.shopxx.shop.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;

/**
 * 门店设计附件
 */
@Entity
@Table(name = "xx_shop_devise_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_devise_attach_sequence")
public class ShopDeviseAttach extends BaseEntity {

    private static final long serialVersionUID = 4532742106331197886L;
    
    /** 门店设计 */
    private ShopDevise shopDevise;
    
    /** 0、经销商提交申请附件，1、区域经理审核附件，2、设计部确认附件（设计图） */
    private Integer type;
    
    /** 附件URL */
    private String url;

    /** 备注 */
    private String memo;

    /** 文件名 */
    private String fileName;

    /**文件名*/
    private String name;

    /**文件后缀*/
    private String suffix;

    /** 序号 */
    private Integer seq;

    /** 上传人 */
    private StoreMember storeMember;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shopDevise", nullable = false, updatable = false)
    public ShopDevise getShopDevise() {
        return shopDevise;
    }

    public void setShopDevise(ShopDevise shopDevise) {
        this.shopDevise = shopDevise;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getStoreMember() {
        return storeMember;
    }

    public void setStoreMember(StoreMember storeMember) {
        this.storeMember = storeMember;
    }

}
