package net.shopxx.shop.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.product.entity.Product;
import net.shopxx.product.entity.ProductCategory;

import java.util.Date;

/**
 * 门店产品表
 */
@Entity
@Table(name = "xx_shop_product")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_product_sequence")
public class ShopProudct extends BaseEntity{

	private static final long serialVersionUID = 8978793242312L;
	
	//门店
	private ShopInfo shopInfo;
	
	//产品
	private Product product;
	
	//产品分类
	private ProductCategory productCategory;
	
	//是否上样
	private Boolean isMarketable;
	
	//小程序是否展示
	private Boolean isShow;
	
	//是否推荐
	private Boolean isReferrer;

	//上样时间
	private Date marketableTime;


	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public ShopInfo getShopInfo() {
		return shopInfo;
	}

	public void setShopInfo(ShopInfo shopInfo) {
		this.shopInfo = shopInfo;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public ProductCategory getProductCategory() {
		return productCategory;
	}

	public void setProductCategory(ProductCategory productCategory) {
		this.productCategory = productCategory;
	}

	public Boolean getIsMarketable() {
		return isMarketable;
	}

	public void setIsMarketable(Boolean isMarketable) {
		this.isMarketable = isMarketable;
	}

	public Boolean getIsShow() {
		return isShow;
	}

	public void setIsShow(Boolean isShow) {
		this.isShow = isShow;
	}

	public Boolean getIsReferrer() {
		return isReferrer;
	}

	public void setIsReferrer(Boolean isReferrer) {
		this.isReferrer = isReferrer;
	}

    public Date getMarketableTime() {
        return marketableTime;
    }

    public void setMarketableTime(Date marketableTime) {
        this.marketableTime = marketableTime;
    }
}
