package net.shopxx.shop.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import net.shopxx.member.entity.Store;
import net.shopxx.wf.entity.WfBillEntity;

/**
 * Entity - 门店奖励
 */
@Entity
@Table(name="xx_shop_reward")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_reward_sequence")
public class ShopReward extends WfBillEntity{

	private static final long serialVersionUID = 24124890098L;
	
	/**编号*/
	private String sn;
	
	/**经销商*/
	private Store store;
	
	/**操作类型*/
	private String operationType;
	
	/**奖励日期*/
	private Date rewardtime;
	
	/**当年目标*/
	private String target;
	
	/**实际完成*/
	private String completion;
	
	/**达成率*/
	private String completionRate;
	
	/**总奖励金额*/
	private BigDecimal totalMoney;
	
	/**保证金欠缴*/
	private BigDecimal arrearage;
	
	/**签名*/
	private String signature;
	
	/**广告公司*/
	private String advertisementCompany;
	
	/**投放地址*/
	private String addes;
	
	/**报销状态*/
	private String baoxiao;
	
	/**更新时间*/
	private Date updatetime;
	
	/**广告合同附件*/
	private List<AdvertisAttach> advertisAttachs = new ArrayList<AdvertisAttach>();

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}

	public void setStore(Store store) {
		this.store = store;
	}
	
	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	public Date getRewardtime() {
		return rewardtime;
	}

	public void setRewardtime(Date rewardtime) {
		this.rewardtime = rewardtime;
	}

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}

	public String getCompletion() {
		return completion;
	}

	public void setCompletion(String completion) {
		this.completion = completion;
	}

	public String getCompletionRate() {
		return completionRate;
	}

	public void setCompletionRate(String completionRate) {
		this.completionRate = completionRate;
	}

	public BigDecimal getTotalMoney() {
		return totalMoney;
	}

	public void setTotalMoney(BigDecimal totalMoney) {
		this.totalMoney = totalMoney;
	}

	public BigDecimal getArrearage() {
		return arrearage;
	}

	public void setArrearage(BigDecimal arrearage) {
		this.arrearage = arrearage;
	}

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getAdvertisementCompany() {
		return advertisementCompany;
	}

	public void setAdvertisementCompany(String advertisementCompany) {
		this.advertisementCompany = advertisementCompany;
	}

	public String getAddes() {
		return addes;
	}

	public void setAddes(String addes) {
		this.addes = addes;
	}

	public String getBaoxiao() {
		return baoxiao;
	}

	public void setBaoxiao(String baoxiao) {
		this.baoxiao = baoxiao;
	}

	public Date getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}

	@OneToMany(mappedBy = "shopReward", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<AdvertisAttach> getAdvertisAttachs() {
		return advertisAttachs;
	}

	public void setAdvertisAttachs(List<AdvertisAttach> advertisAttachs) {
		this.advertisAttachs = advertisAttachs;
	}
}
