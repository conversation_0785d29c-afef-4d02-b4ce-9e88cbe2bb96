package net.shopxx.shop.entity;

import net.shopxx.base.core.entity.OrderEntity;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.persistence.*;

/**
 * Entity - 门店上样分类
 */
@Entity
@Table(name = "xx_shop_product_category")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_product_category_sequence")
public class ShopProductCategory extends OrderEntity {

	private static final long serialVersionUID = 1L;

	/** 名称 */
	private String name;

	/** 是否启用 */
	private Boolean isEnabled;

	/** 分类编码 */
	private String sn;


	/**
	 * 获取名称
	 * 
	 * @return 名称
	 */
	@NotEmpty
	@Length(max = 200)
	@Column(nullable = false)
	public String getName() {
		return name;
	}

	/**
	 * 设置名称
	 * 
	 * @param name
	 *            名称
	 */
	public void setName(String name) {
		this.name = name;
	}

	public Boolean getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Boolean isEnabled) {
		this.isEnabled = isEnabled;
	}


	/**
	 * 获取 分类编码
	 * @return sn
	 */
	public String getSn() {
		return sn;
	}

	/**
	 * 设置 分类编码
	 * @param sn 分类编码
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}




    


	
}