package net.shopxx.shop.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

/**
 * 整改附件
 */
@Entity
@Table(name = "xx_reorganize_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_reorganize_attach_sequence")
public class ReorganizeAttach extends BaseEntity {

    private static final long serialVersionUID = -7943430871750441830L;

    /** 门店设计 */
    private Reorganize reorganize;
    
    /**
     * 0、三年销量数据签字版，
     * 1、函件省长签字版
     * 2、函件Word版
     */
    private Integer type;
    
    /** 附件URL */
    private String url;

    /** 备注 */
    private String memo;

    /** 文件名 */
    private String fileName;

    /**文件名*/
    private String name;

    /**文件后缀*/
    private String suffix;

    /** 序号 */
    private Integer seq;

    /** 上传人 */
    private StoreMember storeMember;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reorganize", nullable = false, updatable = false)
    public Reorganize getReorganize() {
        return reorganize;
    }

    public void setReorganize(Reorganize reorganize) {
        this.reorganize = reorganize;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getStoreMember() {
        return storeMember;
    }

    public void setStoreMember(StoreMember storeMember) {
        this.storeMember = storeMember;
    }
}
