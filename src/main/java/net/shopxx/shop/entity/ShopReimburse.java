package net.shopxx.shop.entity;

import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;
import java.util.Date;

/**
 * Entity - 门店报销单
 */
@Entity
@Table(name = "xx_shop_reimburse")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_reimburse_sequence")
public class ShopReimburse extends BaseEntity{

	private static final long serialVersionUID = 160654389L;
	
	/** 门店*/
	private ShopInfo shopInfo;
	
	/** 装修验收报销单*/
	private AcceptanceReimburse acceptanceReimburse;
	
	/** 报销编号*/
	private String reimburseCode;
	
	/** 审批情况*/
	private String reimburse1;
	
	/** 更新日期*/
	private Date reimburse2;
	
	/** 是否欠租赁合同*/
	private String reimburse3;
	
	/** 是否欠装修合同*/
	private String reimburse4;
	/** 是否申请设计*/
	private String reimburse5;
	/** 是否欠平面图*/
	private String reimburse6;
	/** 是否逾期*/
	private String reimburse7;
	/** 装修属性*/
	private String reimburse8;
	/** 装修施工时间*/
	private Date reimburse9;
	/** 装修完成时间*/
	private Date reimburse10;
	/** 提报面积（㎡）*/
	private String reimburse11;
	/** 审批验收时间*/
	private Date reimburse12;
	/** 施工图完成时间*/
	private Date reimburse13;
	/** 审核有效面积*/
	private String reimburse14;
	/** 纸档审核分数*/
	private String reimburse15;
	/** 2年内未参与返利*/
	private String reimburse16;
	/** 是否有门店*/
	private String reimburse17;
	/** 是否提交装修申请备案*/
	private String reimburse18;
	/** 是否已粘贴签字版设计申请*/
	private String reimburse19;
	/** 是否签署2020合同*/
	private String reimburse20;
	/** 报销标准*/
	private String reimburse21;
	/** 预批金额*/
	private String reimburse22;
	/** 应打折扣*/
	private String reimburse23;
	/** 应返利金额*/
	private String reimburse24;
	/** 实际返利金额*/
	private String reimburse25;
	/** 欠交保证金*/
	private String reimburse26;
	/** 实际扣除保证金*/
	private String reimburse27;
	/** 协议总预算*/
	private String reimburse28;
	/** 协议单价*/
	private String reimburse29;
	/** 报销金额*/
	private String reimburse30;
	/** 协议剩余装修费用*/
	private String reimburse31;
	/** 开票方*/
	private String reimburse32;
	/** 协议签署方（经销商）*/
	private String reimburse33;
	/** 文件发出时间*/
	private Date reimburse34;
	/** 文件收到时间*/
	private Date reimburse35;
	/** 发票是否合格*/
	private String reimburse36;
	/** 协议是否合格*/
	private String reimburse37;
	/** 付款单申请时间*/
	private Date reimburse38;
	/** 提交财务时间*/
	private Date reimburse39;
	/** 财务付款时间*/
	private Date reimburse40;
	/** 返利文件归档编号*/
	private String reimburse41;
	/** 不合格*/
	private String reimburse42;
	/** 跟进时间*/
	private Date reimburse43;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public ShopInfo getShopInfo() {
		return shopInfo;
	}

	public void setShopInfo(ShopInfo shopInfo) {
		this.shopInfo = shopInfo;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false)
	public AcceptanceReimburse getAcceptanceReimburse() {
		return acceptanceReimburse;
	}

	public void setAcceptanceReimburse(AcceptanceReimburse acceptanceReimburse) {
		this.acceptanceReimburse = acceptanceReimburse;
	}

	public String getReimburseCode() {
		return reimburseCode;
	}

	public void setReimburseCode(String reimburseCode) {
		this.reimburseCode = reimburseCode;
	}

	public String getReimburse1() {
		return reimburse1;
	}

	public void setReimburse1(String reimburse1) {
		this.reimburse1 = reimburse1;
	}

	public Date getReimburse2() {
		return reimburse2;
	}

	public void setReimburse2(Date reimburse2) {
		this.reimburse2 = reimburse2;
	}

	public String getReimburse3() {
		return reimburse3;
	}

	public void setReimburse3(String reimburse3) {
		this.reimburse3 = reimburse3;
	}

	public String getReimburse4() {
		return reimburse4;
	}

	public void setReimburse4(String reimburse4) {
		this.reimburse4 = reimburse4;
	}

	public String getReimburse5() {
		return reimburse5;
	}

	public void setReimburse5(String reimburse5) {
		this.reimburse5 = reimburse5;
	}

	public String getReimburse6() {
		return reimburse6;
	}

	public void setReimburse6(String reimburse6) {
		this.reimburse6 = reimburse6;
	}

	public String getReimburse7() {
		return reimburse7;
	}

	public void setReimburse7(String reimburse7) {
		this.reimburse7 = reimburse7;
	}

	public String getReimburse8() {
		return reimburse8;
	}

	public void setReimburse8(String reimburse8) {
		this.reimburse8 = reimburse8;
	}

	public Date getReimburse9() {
		return reimburse9;
	}

	public void setReimburse9(Date reimburse9) {
		this.reimburse9 = reimburse9;
	}

	public Date getReimburse10() {
		return reimburse10;
	}

	public void setReimburse10(Date reimburse10) {
		this.reimburse10 = reimburse10;
	}

	public String getReimburse11() {
		return reimburse11;
	}

	public void setReimburse11(String reimburse11) {
		this.reimburse11 = reimburse11;
	}

	public Date getReimburse12() {
		return reimburse12;
	}

	public void setReimburse12(Date reimburse12) {
		this.reimburse12 = reimburse12;
	}

	public Date getReimburse13() {
		return reimburse13;
	}

	public void setReimburse13(Date reimburse13) {
		this.reimburse13 = reimburse13;
	}

	public String getReimburse14() {
		return reimburse14;
	}

	public void setReimburse14(String reimburse14) {
		this.reimburse14 = reimburse14;
	}

	public String getReimburse15() {
		return reimburse15;
	}

	public void setReimburse15(String reimburse15) {
		this.reimburse15 = reimburse15;
	}

	public String getReimburse16() {
		return reimburse16;
	}

	public void setReimburse16(String reimburse16) {
		this.reimburse16 = reimburse16;
	}

	public String getReimburse17() {
		return reimburse17;
	}

	public void setReimburse17(String reimburse17) {
		this.reimburse17 = reimburse17;
	}

	public String getReimburse18() {
		return reimburse18;
	}

	public void setReimburse18(String reimburse18) {
		this.reimburse18 = reimburse18;
	}

	public String getReimburse19() {
		return reimburse19;
	}

	public void setReimburse19(String reimburse19) {
		this.reimburse19 = reimburse19;
	}

	public String getReimburse20() {
		return reimburse20;
	}

	public void setReimburse20(String reimburse20) {
		this.reimburse20 = reimburse20;
	}

	public String getReimburse21() {
		return reimburse21;
	}

	public void setReimburse21(String reimburse21) {
		this.reimburse21 = reimburse21;
	}

	public String getReimburse22() {
		return reimburse22;
	}

	public void setReimburse22(String reimburse22) {
		this.reimburse22 = reimburse22;
	}

	public String getReimburse23() {
		return reimburse23;
	}

	public void setReimburse23(String reimburse23) {
		this.reimburse23 = reimburse23;
	}

	public String getReimburse24() {
		return reimburse24;
	}

	public void setReimburse24(String reimburse24) {
		this.reimburse24 = reimburse24;
	}

	public String getReimburse25() {
		return reimburse25;
	}

	public void setReimburse25(String reimburse25) {
		this.reimburse25 = reimburse25;
	}

	public String getReimburse26() {
		return reimburse26;
	}

	public void setReimburse26(String reimburse26) {
		this.reimburse26 = reimburse26;
	}

	public String getReimburse27() {
		return reimburse27;
	}

	public void setReimburse27(String reimburse27) {
		this.reimburse27 = reimburse27;
	}

	public String getReimburse28() {
		return reimburse28;
	}

	public void setReimburse28(String reimburse28) {
		this.reimburse28 = reimburse28;
	}

	public String getReimburse29() {
		return reimburse29;
	}

	public void setReimburse29(String reimburse29) {
		this.reimburse29 = reimburse29;
	}

	public String getReimburse30() {
		return reimburse30;
	}

	public void setReimburse30(String reimburse30) {
		this.reimburse30 = reimburse30;
	}

	public String getReimburse31() {
		return reimburse31;
	}

	public void setReimburse31(String reimburse31) {
		this.reimburse31 = reimburse31;
	}

	public String getReimburse32() {
		return reimburse32;
	}

	public void setReimburse32(String reimburse32) {
		this.reimburse32 = reimburse32;
	}

	public String getReimburse33() {
		return reimburse33;
	}

	public void setReimburse33(String reimburse33) {
		this.reimburse33 = reimburse33;
	}

	public Date getReimburse34() {
		return reimburse34;
	}

	public void setReimburse34(Date reimburse34) {
		this.reimburse34 = reimburse34;
	}

	public Date getReimburse35() {
		return reimburse35;
	}

	public void setReimburse35(Date reimburse35) {
		this.reimburse35 = reimburse35;
	}

	public String getReimburse36() {
		return reimburse36;
	}

	public void setReimburse36(String reimburse36) {
		this.reimburse36 = reimburse36;
	}

	public String getReimburse37() {
		return reimburse37;
	}

	public void setReimburse37(String reimburse37) {
		this.reimburse37 = reimburse37;
	}

	public Date getReimburse38() {
		return reimburse38;
	}

	public void setReimburse38(Date reimburse38) {
		this.reimburse38 = reimburse38;
	}

	public Date getReimburse39() {
		return reimburse39;
	}

	public void setReimburse39(Date reimburse39) {
		this.reimburse39 = reimburse39;
	}

	public Date getReimburse40() {
		return reimburse40;
	}

	public void setReimburse40(Date reimburse40) {
		this.reimburse40 = reimburse40;
	}

	public String getReimburse41() {
		return reimburse41;
	}

	public void setReimburse41(String reimburse41) {
		this.reimburse41 = reimburse41;
	}

	public String getReimburse42() {
		return reimburse42;
	}

	public void setReimburse42(String reimburse42) {
		this.reimburse42 = reimburse42;
	}

	public Date getReimburse43() {
		return reimburse43;
	}

	public void setReimburse43(Date reimburse43) {
		this.reimburse43 = reimburse43;
	}

}
