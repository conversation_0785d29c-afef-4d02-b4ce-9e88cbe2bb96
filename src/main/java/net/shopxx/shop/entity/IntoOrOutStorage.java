package net.shopxx.shop.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.validation.Valid;

import org.hibernate.validator.constraints.NotEmpty;

import net.shopxx.base.core.entity.BaseEntity;

/**
 * Entity - 入库申请、出库申请、采购入库
 */
@Entity
@Table(name = "xx_into_or_out_storage")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_into_or_out_storage_sequence")
public class IntoOrOutStorage extends BaseEntity {

    private static final long serialVersionUID = 3662236000959562232L;
    
    /** 单号 */
    private String sn;
    
    /**
     * 单据状态
     * 0：已保存
     * 1：已提交
     * 2：已终止
     * 3：进行中
     */
    private Integer status;
    
    /** 类型 0：入库申请，1：出库申请，2：采购入库 */
    private Integer type;
    
    /** 入库时间 */
    private Date enterDate;
    
    /** 出库时间 */
    private Date outDate;
  
    /** 仓库(仓库名称) */
    private SetStorage storage;
    
    /** 备注 */
    private String memo;
    
    /** 订单id(四期)*/
    private Long orderId;
    
    /** 出入库项 */
    private List<IntoOrOutStorageItem> intoOtOutPro = new ArrayList<IntoOrOutStorageItem>();
    
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getEnterDate() {
        return enterDate;
    }

    public void setEnterDate(Date enterDate) {
        this.enterDate = enterDate;
    }

    public Date getOutDate() {
        return outDate;
    }

    public void setOutDate(Date outDate) {
        this.outDate = outDate;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public SetStorage getStorage() {
        return storage;
    }

    public void setStorage(SetStorage storage) {
        this.storage = storage;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Valid
    @NotEmpty
    @OneToMany(mappedBy = "intoOrOutStorage", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    public List<IntoOrOutStorageItem> getIntoOtOutPro() {
        return intoOtOutPro;
    }

    public void setIntoOtOutPro(List<IntoOrOutStorageItem> intoOtOutPro) {
        this.intoOtOutPro = intoOtOutPro;
    }

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

}
