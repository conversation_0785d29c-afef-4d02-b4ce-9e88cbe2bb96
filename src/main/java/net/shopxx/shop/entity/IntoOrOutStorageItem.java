package net.shopxx.shop.entity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.product.entity.Product;

/**
 * 出入库的产品明细
 */
@Entity
@Table(name = "xx_into_or_out_storage_item")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_into_or_out_storage_item_sequence")
public class IntoOrOutStorageItem extends BaseEntity {
    
    private static final long serialVersionUID = -4933799824766689426L;

    /** 产品 */
    private Product product;
    
    /** 出入库的数量 */
    private BigDecimal number;
    
    /** 箱数 */
    private BigDecimal box;
    
    /** 支数 */
    private BigDecimal branch;
    
    /** 零散支数 */
    private BigDecimal scatteredBranch;
    
    /** 备注 */
    private String memo;
    
    /** 采购入库所需字段（发货项） */
    private ShippingItem shippingItem;
    
    /** 对应出入库 */
    private IntoOrOutStorage intoOrOutStorage;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    @JsonProperty
    @NotNull
    @Min(0)
    @Column(nullable = false, precision = 21, scale = 6)
    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }
    
    public BigDecimal getBox() {
        return box;
    }

    public void setBox(BigDecimal box) {
        this.box = box;
    }
    
    public BigDecimal getBranch() {
        return branch;
    }

    public void setBranch(BigDecimal branch) {
        this.branch = branch;
    }
    
    public BigDecimal getScatteredBranch() {
        return scatteredBranch;
    }

    public void setScatteredBranch(BigDecimal scatteredBranch) {
        this.scatteredBranch = scatteredBranch;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public ShippingItem getShippingItem() {
        return shippingItem;
    }

    public void setShippingItem(ShippingItem shippingItem) {
        this.shippingItem = shippingItem;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public IntoOrOutStorage getIntoOrOutStorage() {
        return intoOrOutStorage;
    }

    public void setIntoOrOutStorage(IntoOrOutStorage intoOrOutStorage) {
        this.intoOrOutStorage = intoOrOutStorage;
    }
}
