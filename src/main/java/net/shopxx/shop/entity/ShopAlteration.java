package net.shopxx.shop.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 门店变更
 */
@Entity
@Table(name = "xx_shop_alteration")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_alteration_sequence")
public class ShopAlteration extends ActWfBillEntity {

	private static final long serialVersionUID = 2141265865871L;

	/** 变更单号 */
	private String sn;

	/** 状态 */
	public enum Status {

		/** 已保存 */
		saved,

		/** 已提交 */
		submitted,

		/** 已终止 */
		terminated,

		/** 进行中 */
		underway,
		
		/** 已作废*/
		cancel
	}

	/** 单据状态 */
	private Status statuss;

	/** 客户 */
	private Store store;
	
	/** 机构 */
	private SaleOrg saleOrg;

	/** 门店资料 */
	private ShopInfo infoShop;

	/** 操作类型 */
	private String operationType;

	/** 变更类型 */
	private String changeType;

	/** 关店日期 */
	private Date closingTime;

	// 新店信息
	/** 建店日期 */
	private Date openDate;

	/** 门店面积 */
	private BigDecimal acreage;

	/** 门店位置 */// 位置类型
	private String positionType;

	/** 公司性质 */
	private String companyNature;

	/** 门店所有 */
	private String shopOwnership;

	/** 月租金 */
	private BigDecimal monthlyRent;

	/** 城市行政等级 */
	private String administrativeRank;

	/** 是否需要设计 */
	private Boolean isDesign;

	/** 经营品类 */
	private String businessCategory;

	/** 新店地址 */
	private String address;

	/** 门店搬迁原因 */
	private String moveReason;

	/** 新店地址省市区 */
	private Area newShopArea;

	/** 店名 */
	private String shopName;

	/** 乡镇 */
	private String town;

	// 区域经理
	/** 门店类型 */ // 门店类别
	private String type;

	/** 所属销售平台 */
	private String salesPlatform;

	/** 所属品牌 */
	private String belongBrand;

	/** 所含品牌 */
	private String shopSign;

	/** VI版本 */
	private String viVersion;

	/** 所属部门 */
	private String department;

	/** 关闭原因 */
	private String shutDownMenu;

	/** 销售渠道 */
	private String salesChannel;

	// 渠道部意见
	/** 门店授权编号 */
	private String authorizationCode;

	/** 新增档案编号 */
	private String increaseArchivesCode;

	/** 新增时间 */
	private Date newTime;

	/** 减少档案编号 */
	private String decreaseArchivesCode;

	/** 减少时间 */
	private Date decreaseTime;

	/** 门店情况备注 */
	private String shopCaseNote;
	
	/** 创建人*/
	private StoreMember storeMember;

    /** 性别 */
    private String dealerSex;

    private List<ShopAlterationAttach> shopAlterationAttachs = new ArrayList<ShopAlterationAttach>();
    /** 门店附件 */
    private List<ShopAlterationAttach> shopFarAttachs = new ArrayList<ShopAlterationAttach>();
    private List<ShopAlterationAttach> shopDecorateAttachs = new ArrayList<ShopAlterationAttach>();
    private List<ShopAlterationAttach> payPlatformAttachs = new ArrayList<ShopAlterationAttach>();
    private List<ShopAlterationAttach> plateAttachs = new ArrayList<ShopAlterationAttach>();

	@Column(nullable = false, updatable = false, unique = true, length = 100)
	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}

	/**
	 * 获取状态
	 * 
	 * @return 状态
	 */
	@Column(nullable = false)
	public Status getStatuss() {
		return statuss;
	}

	/**
	 * 设置状态
	 * 
	 * @param statuss
	 *            状态
	 */
	public void setStatuss(Status statuss) {
		this.statuss = statuss;
	}

	public void setStore(Store store) {
		this.store = store;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public ShopInfo getInfoShop() {
		return infoShop;
	}

	public void setInfoShop(ShopInfo infoShop) {
		this.infoShop = infoShop;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}
	
	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}
	

	public Date getOpenDate() {
		return openDate;
	}

	public void setOpenDate(Date openDate) {
		this.openDate = openDate;
	}

	public BigDecimal getAcreage() {
		return acreage;
	}

	public void setAcreage(BigDecimal acreage) {
		this.acreage = acreage;
	}

	public String getPositionType() {
		return positionType;
	}

	public void setPositionType(String positionType) {
		this.positionType = positionType;
	}

	public String getCompanyNature() {
		return companyNature;
	}

	public void setCompanyNature(String companyNature) {
		this.companyNature = companyNature;
	}

	public String getShopOwnership() {
		return shopOwnership;
	}

	public void setShopOwnership(String shopOwnership) {
		this.shopOwnership = shopOwnership;
	}

	public BigDecimal getMonthlyRent() {
		return monthlyRent;
	}

	public void setMonthlyRent(BigDecimal monthlyRent) {
		this.monthlyRent = monthlyRent;
	}

	public String getAdministrativeRank() {
		return administrativeRank;
	}

	public void setAdministrativeRank(String administrativeRank) {
		this.administrativeRank = administrativeRank;
	}

	public Boolean getIsDesign() {
		return isDesign;
	}

	public void setIsDesign(Boolean isDesign) {
		this.isDesign = isDesign;
	}

	public String getBusinessCategory() {
		return businessCategory;
	}

	public void setBusinessCategory(String businessCategory) {
		this.businessCategory = businessCategory;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getMoveReason() {
		return moveReason;
	}

	public void setMoveReason(String moveReason) {
		this.moveReason = moveReason;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Area getNewShopArea() {
		return newShopArea;
	}

	public void setNewShopArea(Area newShopArea) {
		this.newShopArea = newShopArea;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getSalesPlatform() {
		return salesPlatform;
	}

	public void setSalesPlatform(String salesPlatform) {
		this.salesPlatform = salesPlatform;
	}

	public String getBelongBrand() {
		return belongBrand;
	}

	public void setBelongBrand(String belongBrand) {
		this.belongBrand = belongBrand;
	}

	public String getShopSign() {
		return shopSign;
	}

	public void setShopSign(String shopSign) {
		this.shopSign = shopSign;
	}

	public String getViVersion() {
		return viVersion;
	}

	public void setViVersion(String viVersion) {
		this.viVersion = viVersion;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getShutDownMenu() {
		return shutDownMenu;
	}

	public void setShutDownMenu(String shutDownMenu) {
		this.shutDownMenu = shutDownMenu;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getAuthorizationCode() {
		return authorizationCode;
	}

	public void setAuthorizationCode(String authorizationCode) {
		this.authorizationCode = authorizationCode;
	}

	public String getIncreaseArchivesCode() {
		return increaseArchivesCode;
	}

	public void setIncreaseArchivesCode(String increaseArchivesCode) {
		this.increaseArchivesCode = increaseArchivesCode;
	}

	public Date getNewTime() {
		return newTime;
	}

	public void setNewTime(Date newTime) {
		this.newTime = newTime;
	}

	public String getDecreaseArchivesCode() {
		return decreaseArchivesCode;
	}

	public void setDecreaseArchivesCode(String decreaseArchivesCode) {
		this.decreaseArchivesCode = decreaseArchivesCode;
	}

	public Date getDecreaseTime() {
		return decreaseTime;
	}

	public void setDecreaseTime(Date decreaseTime) {
		this.decreaseTime = decreaseTime;
	}

	public String getShopCaseNote() {
		return shopCaseNote;
	}

	public void setShopCaseNote(String shopCaseNote) {
		this.shopCaseNote = shopCaseNote;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<ShopAlterationAttach> getShopAlterationAttachs() {
		return shopAlterationAttachs;
	}

	public void setShopAlterationAttachs(List<ShopAlterationAttach> shopAlterationAttachs) {
		this.shopAlterationAttachs = shopAlterationAttachs;
	}

    /**
     * 获取门店附件
     *
     * @return 门店附件
     */
    @OneToMany( fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopAlterationAttach> getShopFarAttachs() {
        return shopFarAttachs;
    }

    public void setShopFarAttachs(List<ShopAlterationAttach> shopFarAttachs) {
        this.shopFarAttachs = shopFarAttachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopAlterationAttach> getShopDecorateAttachs() {
        return shopDecorateAttachs;
    }

    public void setShopDecorateAttachs(List<ShopAlterationAttach> shopDecorateAttachs) {
        this.shopDecorateAttachs = shopDecorateAttachs;
    }

    @OneToMany( fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopAlterationAttach> getPayPlatformAttachs() {
        return payPlatformAttachs;
    }

    public void setPayPlatformAttachs(List<ShopAlterationAttach> payPlatformAttachs) {
        this.payPlatformAttachs = payPlatformAttachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<ShopAlterationAttach> getPlateAttachs() {
        return plateAttachs;
    }

    public void setPlateAttachs(List<ShopAlterationAttach> plateAttachs) {
        this.plateAttachs = plateAttachs;
    }

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}



	public String getChangeType() {
		return changeType;
	}

	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}

	public Date getClosingTime() {
		return closingTime;
	}

	public void setClosingTime(Date closingTime) {
		this.closingTime = closingTime;
	}

    public String getDealerSex() {
        return dealerSex;
    }

    public void setDealerSex(String dealerSex) {
        this.dealerSex = dealerSex;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }
}
