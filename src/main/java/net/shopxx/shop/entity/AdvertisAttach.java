package net.shopxx.shop.entity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;


/**
 * 门店装修验收及报销附件
 */
@Entity
@Table(name = "xx_advertis_attach")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_advertis_attach_sequence")
public class AdvertisAttach extends BaseEntity {

	private static final long serialVersionUID = 112412125125L;
	private ShopReward shopReward;
	
	/** 附件URL */
	private String url;

	/** 备注 */
	private String memo;

	/** 文件名 */
	private String fileName;
	
	/**文件名*/
	private String name;
	
	/**文件后缀*/
	private String suffix;

	/** 序号 */
	private Integer seq;

	/** 上传人 */
	private StoreMember storeMember;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "rewards", nullable = false, updatable = false)
	public ShopReward getShopReward() {
		return shopReward;
	}

	public void setShopReward(ShopReward shopReward) {
		this.shopReward = shopReward;
	}

	public String getUrl() {
		return url;
	}


	public void setUrl(String url) {
		this.url = url;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSuffix() {
		return suffix;
	}

	public void setSuffix(String suffix) {
		this.suffix = suffix;
	}

	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	@JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

}
