package net.shopxx.shop.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 门店
 */
@Entity
@Table(name = "xx_shop")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_shop_sequence")
public class Shop extends ActWfBillEntity {

	private static final long serialVersionUID = -41242111L;

	/** 单号 */
	private String sn;

	/** 单据状态 */
	public enum Status {

		/** 已保存 */
		saved,

		/** 进行中 */
		underway,

		/** 已终止 */
		terminated,

		/** 已生效 */
		submitted,
	}

	/** 单据状态 */
	private Status statuss;

	/** 客户 */
	private Store store;

	/** 机构 */
	private SaleOrg saleOrg;

	/** 操作类型 */
	private String operationType;

	/** 变更类型 */
	private String alterationType;

	/** 建店日期 */
	private Date createShopDate;

	/** 关店日期 */
	private Date closeShopDate;

	/** 门店面积 */
	private BigDecimal shopAcreage;

	/** 门店位置 */
	private String shopLocation;

	/** 公司性质 */
	private String companyNature;

	/** 门店所有 */
	private String shopOwnership;

	/** 是否参与门店设计 */
	private Boolean isParticipateDesign;

	/** 旧店地址 */
	private String originalShopAddress;

	/** 新店地址 */
	private String newShopAddress;

	/** 经营品类 */
	private String businessCategory;

    /** 审核人 */
    private StoreMember checkStoreMember;

    /** 审核时间 */
    private Date checkDate;

	/** 门店附件 */
	private List<ShopAttach> shopFarAttachs = new ArrayList<ShopAttach>();
	private List<ShopAttach> shopDecorateAttachs = new ArrayList<ShopAttach>();
	private List<ShopAttach> payPlatformAttachs = new ArrayList<ShopAttach>();
	private List<ShopAttach> plateAttachs = new ArrayList<ShopAttach>();


	/** 招牌 */
	private String shopSign;

	/** 关闭原因 */
	private String shutDownMenu;

	/** 门店授权编号 */
	private String shopAuthorizationCodes;

	/** 新增档案编号 */
	private String archivesCodes;

	/** 新增时间 */
	private Date newTime;

	/** 减少档案编号 */
	private String decreaseArchivesCodes;

	/** 减少时间 */
	private Date decreaseTime;

	/** 门店情况备注 */
	private String shopCaseNote;

	/** 合伙人名称 */
	private String partnerName;

	/** 合伙人电话 */
	private String partnerPhone;

	/** 新店地区 */
	private Area newShopArea;

	/** 月租金 */
	private BigDecimal monthlyRent;

	/** 店名 */
	private String shopName;

	/** 创建人 */
	private StoreMember storeMember;

    /** 最后执行人 */
    private StoreMember lastOperator;

    /** 乡镇 */
    private String town;

	// 区域经理字段======================
	/** 门店类型 */
	private String shopType;

	/** 城市行政等级 */
	private String administrativeRank;

	/** 所属品牌 */
	private String belongBrand;

	/** VI版本 */
	private String viVersion;

	/** 所属部门 */
	private String department;

	/** 销售渠道 */
	private String salesChannel;

    /** 性别 */
    private String dealerSex;

	// 省长字段===========================
	/** 省长意见 */
	private String szyj;

	/**
	 * 获取单号
	 * 
	 * @return 单号
	 */
	@Column(nullable = false, updatable = false, unique = true, length = 100)
	public String getSn() {
		return sn;
	}

	/**
	 * 设置单号
	 * 
	 * @param sn
	 *            单号
	 */
	public void setSn(String sn) {
		this.sn = sn;
	}

	/**
	 * 获取状态
	 * 
	 * @return 状态
	 */
	@Column(nullable = false)
	public Status getStatuss() {
		return statuss;
	}

	/**
	 * 设置状态
	 * 
	 * @param statuss
	 *            状态
	 */
	public void setStatuss(Status statuss) {
		this.statuss = statuss;
	}

	/**
	 * 获取客户
	 * 
	 * @return 客户
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public Store getStore() {
		return store;
	}

	/**
	 * 设置客户
	 * 
	 * @param store
	 *            客户
	 */
	public void setStore(Store store) {
		this.store = store;
	}

	/**
	 * 获取机构
	 * 
	 * @return 机构
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	/**
	 * 设置机构
	 * 
	 * @param saleOrg
	 *            机构
	 */
	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	/**
	 * 获取操作类型
	 * 
	 * @return 操作类型
	 */
	public String getOperationType() {
		return operationType;
	}

	/**
	 * 设置操作类型
	 * 
	 * @param operationType
	 *            操作类型
	 */
	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	/**
	 * 获取变更类型
	 * 
	 * @return 变更类型
	 */
	public String getAlterationType() {
		return alterationType;
	}

	/**
	 * 设置变更类型
	 * 
	 * @param alterationType
	 *            变更类型
	 */
	public void setAlterationType(String alterationType) {
		this.alterationType = alterationType;
	}

	/**
	 * 获取建店日期
	 * 
	 * @return 建店日期
	 */
	public Date getCreateShopDate() {
		return createShopDate;
	}

	/**
	 * 设置建店日期
	 * 
	 * @param createShopDate
	 *            建店日期
	 */
	public void setCreateShopDate(Date createShopDate) {
		this.createShopDate = createShopDate;
	}

	/**
	 * 获取关店日期
	 * 
	 * @return 关店日期
	 */
	public Date getCloseShopDate() {
		return closeShopDate;
	}

	/**
	 * 设置关店日期
	 * 
	 * @param closeShopDate
	 *            关店日期
	 */
	public void setCloseShopDate(Date closeShopDate) {
		this.closeShopDate = closeShopDate;
	}

	/**
	 * 获取门店面积
	 * 
	 * @return 门店面积
	 */
	public BigDecimal getShopAcreage() {
		return shopAcreage;
	}

	/**
	 * 设置门店面积
	 * 
	 * @param shopAcreage
	 *            门店面积
	 */
	public void setShopAcreage(BigDecimal shopAcreage) {
		this.shopAcreage = shopAcreage;
	}

	/**
	 * 获取门店位置
	 * 
	 * @return 门店位置
	 */
	public String getShopLocation() {
		return shopLocation;
	}

	/**
	 * 设置门店位置
	 * 
	 * @param shopLocation
	 *            门店位置
	 */
	public void setShopLocation(String shopLocation) {
		this.shopLocation = shopLocation;
	}

	/**
	 * 获取公司性质
	 * 
	 * @return 公司性质
	 */
	public String getCompanyNature() {
		return companyNature;
	}

	/**
	 * 设置公司性质
	 * 
	 * @param companyNature
	 *            公司性质
	 */
	public void setCompanyNature(String companyNature) {
		this.companyNature = companyNature;
	}

	/**
	 * 获取门店所有
	 * 
	 * @return 门店所有
	 */
	public String getShopOwnership() {
		return shopOwnership;
	}

	/**
	 * 设置门店所有
	 * 
	 * @param shopOwnership
	 *            门店所有
	 */
	public void setShopOwnership(String shopOwnership) {
		this.shopOwnership = shopOwnership;
	}

	/**
	 * 获取是否参与门店设计
	 * 
	 * @return 是否参与门店设计
	 */
	public Boolean getIsParticipateDesign() {
		return isParticipateDesign;
	}

	/**
	 * 设置是否参与门店设计
	 * 
	 * @param isParticipateDesign
	 *            是否参与门店设计
	 */
	public void setIsParticipateDesign(Boolean isParticipateDesign) {
		this.isParticipateDesign = isParticipateDesign;
	}

	/**
	 * 获取旧店地址
	 * 
	 * @return 旧店地址
	 */
	public String getOriginalShopAddress() {
		return originalShopAddress;
	}

	/**
	 * 设置旧店地址
	 * 
	 * @param originalShopAddress
	 *            旧店地址
	 */
	public void setOriginalShopAddress(String originalShopAddress) {
		this.originalShopAddress = originalShopAddress;
	}

	/**
	 * 获取新店地址
	 * 
	 * @return 新店地址
	 */
	public String getNewShopAddress() {
		return newShopAddress;
	}

	/**
	 * 设置新店地址
	 * 
	 * @param newShopAddress
	 *            新店地址
	 */
	public void setNewShopAddress(String newShopAddress) {
		this.newShopAddress = newShopAddress;
	}

	/**
	 * 获取经营品类
	 * 
	 * @return 经营品类
	 */
	public String getBusinessCategory() {
		return businessCategory;
	}

	/**
	 * 设置经营品类
	 * 
	 * @param businessCategory
	 *            经营品类
	 */
	public void setBusinessCategory(String businessCategory) {
		this.businessCategory = businessCategory;
	}

	/**
	 * 获取门店附件
	 * 
	 * @return 门店附件
	 */
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<ShopAttach> getShopFarAttachs() {
		return shopFarAttachs;
	}

	public void setShopFarAttachs(List<ShopAttach> shopFarAttachs) {
		this.shopFarAttachs = shopFarAttachs;
	}
	
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<ShopAttach> getShopDecorateAttachs() {
		return shopDecorateAttachs;
	}

	public void setShopDecorateAttachs(List<ShopAttach> shopDecorateAttachs) {
		this.shopDecorateAttachs = shopDecorateAttachs;
	}
	
	@OneToMany( fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<ShopAttach> getPayPlatformAttachs() {
		return payPlatformAttachs;
	}

	public void setPayPlatformAttachs(List<ShopAttach> payPlatformAttachs) {
		this.payPlatformAttachs = payPlatformAttachs;
	}
	
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
	@OrderBy("seq asc")
	public List<ShopAttach> getPlateAttachs() {
		return plateAttachs;
	}

	public void setPlateAttachs(List<ShopAttach> plateAttachs) {
		this.plateAttachs = plateAttachs;
	}

	/**
	 * 获取招牌
	 * 
	 * @return 招牌
	 */
	public String getShopSign() {
		return shopSign;
	}

	/**
	 * 设置招牌
	 * 
	 * @param shopSign
	 *            招牌
	 */
	public void setShopSign(String shopSign) {
		this.shopSign = shopSign;
	}

	/**
	 * 获取门店类型
	 * 
	 * @return 门店类型
	 */
	public String getShopType() {
		return shopType;
	}

	/**
	 * 设置门店类型
	 * 
	 * @param shopType
	 *            门店类型
	 */
	public void setShopType(String shopType) {
		this.shopType = shopType;
	}

	/**
	 * 获取所属品牌
	 * 
	 * @return 所属品牌
	 */
	public String getBelongBrand() {
		return belongBrand;
	}

	/**
	 * 设置所属品牌
	 * 
	 * @param belongBrand
	 *            所属品牌
	 */
	public void setBelongBrand(String belongBrand) {
		this.belongBrand = belongBrand;
	}

	/**
	 * 获取VI版本
	 * 
	 * @return VI版本
	 */
	public String getViVersion() {
		return viVersion;
	}

	/**
	 * 设置VI版本
	 * 
	 * @param viVersion
	 *            VI版本
	 */
	public void setViVersion(String viVersion) {
		this.viVersion = viVersion;
	}

	/**
	 * 获取所属部门
	 * 
	 * @return 所属部门
	 */
	public String getDepartment() {
		return department;
	}

	/**
	 * 设置所属部门
	 * 
	 * @param department
	 *            所属部门
	 */
	public void setDepartment(String department) {
		this.department = department;
	}

	/**
	 * 获取关闭原因
	 * 
	 * @return 关闭原因
	 */
	public String getShutDownMenu() {
		return shutDownMenu;
	}

	/**
	 * 设置关闭原因
	 * 
	 * @param shutDownMenu
	 *            关闭原因
	 */
	public void setShutDownMenu(String shutDownMenu) {
		this.shutDownMenu = shutDownMenu;
	}

	/**
	 * 获取销售渠道
	 * 
	 * @return 销售渠道
	 */
	public String getSalesChannel() {
		return salesChannel;
	}

	/**
	 * 设置销售渠道
	 * 
	 * @param salesChannel
	 *            销售渠道
	 */
	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getShopAuthorizationCodes() {
		return shopAuthorizationCodes;
	}

	public void setShopAuthorizationCodes(String shopAuthorizationCodes) {
		this.shopAuthorizationCodes = shopAuthorizationCodes;
	}

	public String getArchivesCodes() {
		return archivesCodes;
	}

	public void setArchivesCodes(String archivesCodes) {
		this.archivesCodes = archivesCodes;
	}

	public Date getNewTime() {
		return newTime;
	}

	public void setNewTime(Date newTime) {
		this.newTime = newTime;
	}

	public String getDecreaseArchivesCodes() {
		return decreaseArchivesCodes;
	}

	public void setDecreaseArchivesCodes(String decreaseArchivesCodes) {
		this.decreaseArchivesCodes = decreaseArchivesCodes;
	}

	public Date getDecreaseTime() {
		return decreaseTime;
	}

	public void setDecreaseTime(Date decreaseTime) {
		this.decreaseTime = decreaseTime;
	}

	/**
	 * 获取门店情况备注
	 * 
	 * @return 门店情况备注
	 */
	@Length(max = 200)
	public String getShopCaseNote() {
		return shopCaseNote;
	}

	/**
	 * 设置门店情况备注
	 * 
	 * @param shopCaseNote
	 *            门店情况备注
	 */
	public void setShopCaseNote(String shopCaseNote) {
		this.shopCaseNote = shopCaseNote;
	}

	/**
	 * 获取城市行政等级
	 * 
	 * @return 城市行政等级
	 */
	public String getAdministrativeRank() {
		return administrativeRank;
	}

	/**
	 * 设置城市行政等级
	 * 
	 * @param administrativeRank
	 *            城市行政等级
	 */
	public void setAdministrativeRank(String administrativeRank) {
		this.administrativeRank = administrativeRank;
	}

	/**
	 * 获取省长意见
	 * 
	 * @return 省长意见
	 */
	@Length(max = 200)
	public String getSzyj() {
		return szyj;
	}

	/**
	 * 设置省长意见
	 * 
	 * @param szyj
	 *            省长意见
	 */
	public void setSzyj(String szyj) {
		this.szyj = szyj;
	}

	public String getPartnerName() {
		return partnerName;
	}

	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}

	public String getPartnerPhone() {
		return partnerPhone;
	}

	public void setPartnerPhone(String partnerPhone) {
		this.partnerPhone = partnerPhone;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public Area getNewShopArea() {
		return newShopArea;
	}

	public void setNewShopArea(Area newShopArea) {
		this.newShopArea = newShopArea;
	}

	public BigDecimal getMonthlyRent() {
		return monthlyRent;
	}

	public void setMonthlyRent(BigDecimal monthlyRent) {
		this.monthlyRent = monthlyRent;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	public StoreMember getStoreMember() {
		return storeMember;
	}

	public void setStoreMember(StoreMember storeMember) {
		this.storeMember = storeMember;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

    public String getDealerSex() {
        return dealerSex;
    }

    public void setDealerSex(String dealerSex) {
        this.dealerSex = dealerSex;
    }
    /**
     * 获取   审核人
     * @return checkStoreMember
     */
    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getCheckStoreMember() {
        return checkStoreMember;
    }

    /**
     * 设置  审核人
     * @return checkStoreMember
     */
    public void setCheckStoreMember(StoreMember checkStoreMember) {
        this.checkStoreMember = checkStoreMember;
    }

    /**
     * 获取   审核时间
     * @return checkDate
     */
    public Date getCheckDate() {
        return checkDate;
    }

    /**
     * 设置    审核时间
     * @return checkDate
     */
    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getLastOperator() {
        return lastOperator;
    }

    public void setLastOperator(StoreMember lastOperator) {
        this.lastOperator = lastOperator;
    }

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}
}
