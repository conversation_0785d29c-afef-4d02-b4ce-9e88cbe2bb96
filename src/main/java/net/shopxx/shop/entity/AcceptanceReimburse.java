package net.shopxx.shop.entity;

import net.shopxx.act.entity.ActWfBillEntity;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Entity - 门店装修验收及报销、验收不报销
 */
@Entity
@Table(name = "xx_acceptance_reimburse")
@SequenceGenerator(name = "sequenceGenerator", sequenceName = "xx_acceptance_reimburse_sequence")
public class AcceptanceReimburse extends ActWfBillEntity {

    private static final long serialVersionUID = -6335791743331293084L;
    
    /** 单号 */
    private String sn;
    /**
     * 单据状态
     * 0：已保存
     * 1：进行中
     * 2：已生效
     * 3：已终止
     */
    private Integer status;

    /**
     * 1、验收及报销
     * 2、验收不报销
     */
    private Integer type;
    
    /** 门店 */
    private ShopInfo shopInfo;
    
    /** 机构 */
	private SaleOrg saleOrg;
    
    /** 经销商 */
    private String dealer;
    
    /** 省份 */
    private String provinces;
    
    /** 城市 */
    private String city;
    
    /** 经销商联系电话 */
    private String dealerPhone;
    
    /** 区域经理姓名 */
    private String regionalManager;
    
    /** 区域经理联系电话 */
    private String regionalManagerPhone;
    
    /** 门店详细地址 */
    private String shopAddress;
    
    /** 门店设计 */
    private ShopDevise shopDevise;
    
    /**
     * 验收申请承诺 
     * 0、门店平面图核实面积且签字
     * 1、提交门店装修合同复印件
     * 2、提交门店租赁合同/房产证复印件
     */
    private String acceptanceCommitment;
    
    /** 验收租赁期限 */
    private BigDecimal acceptanceLeaseTerm;
    
    /** 验收合同门店面积 */
    private BigDecimal acceptanceArea;
    
    /**
     * 验收人员核实（区域经理、设计师）
     * 0、经确认，装修施工时间
     * 1、经确认，装修完成时间
     * 2、经确认，门店装修属性：重装店  新店
     * 3、经核实，有效面积
     * 4、经审核，评分表得分
     */
    private String acceptanceVerify;
    
    /** 装修施工时间 */
    private Date decorateConstructionTime;
    
    /** 装修完成时间 */
    private Date decorateCompleteTime;
    
    /** 验收人员核实装修属性 */
    private String acceptanceShop;
    
    /** 验收人员核实面积 */
    private String acceptanceVerifyArea;
    
    /** 验收人员核实评分 */
    private String acceptanceVerifyScore;
    
    /** 省运营管理中心意见 */
    private String provincialOperationMemo;
    
    /** 
     * 终端设计经理意见
     * 0、经确认，施工图完成时间
     * 1、经核实，套内有效营业面积
     * 2、经审核，得分
     * 3、经审核，不合格
     */
    private String designManager;
    
    /** 施工图完成时间 */
    private Date constructionFigureCompleteTime;
    
    /** 有效营业面积 */
    private BigDecimal designManagerArea;
    
    /** 评分 */
    private BigDecimal designManagerScore;
    
    /** 备注 */
    private String designManagerMemo;
    
    /**
     * 渠道总监意见
     * 0、2年内未参与装修报销 
     * 1、签署2019年经销合同
     * 2、经审核，报销标准
     * 3、不同意报销
     * 4、同意报销
     */
    private String directorOpinion;
    
    /** 备注 */
    private String directorOpinionMemo;
    
    /**
     * 缴纳齐全品牌保证金
     * 0、否
     * 1、是  
     */
    private Integer payDeposit;

    /** 折扣 */
    private String discount;

    /** 补贴标准 */
    private String subsidy;


    /** 报销标准 N 元/㎡ */
    private String directorOpinionMoney1;
    
    /** 报销金额 */
    private String directorOpinionMoney2;
    
    /** 扣除品牌保证金 */
    private String directorOpinionMoney3;
    
    /**
     * 地板事业部总经理意见
     * 0、不同意报销
     * 1、同意报销
     */
    private Integer floorManagerOpinion;
    
    /**门店授权编号*/
    private String shopAuthorizationCodes;
    
    /**新增档案编号*/
    private String archivesCodes;
    
    /**新增时间*/
    private Date newTime;
    
    /**省长意见*/
    private String szyj;

    /** 装修状态*/
    private String decorationState;

    /** 装修状态更改时间*/
    private Date changeDecorationStateTime;

    /** 最后执行人 */
    private StoreMember lastOperator;

    /** 审批验收时间 */
    private Date approveAcceptanceTime;
    
    /** 设计经理审核时间*/
    private Date designManagerCheckTime;
    
    /** 验收申请承诺附件 */
    private List<AcceptanceReimburseAttach> acceptanceCommitmentAttachs = new ArrayList<AcceptanceReimburseAttach>();
    private List<AcceptanceReimburseAttach> storeContractAttachs = new ArrayList<AcceptanceReimburseAttach>();
    private List<AcceptanceReimburseAttach> storePictureAttachs = new ArrayList<AcceptanceReimburseAttach>();
    
    /** 门店装修验收照片附件 */
    private List<AcceptanceReimburseAttach> shopFarAttachs = new ArrayList<AcceptanceReimburseAttach>();
    private List<AcceptanceReimburseAttach> shopDecorateAttachs = new ArrayList<AcceptanceReimburseAttach>();
    private List<AcceptanceReimburseAttach> payPlatformAttachs = new ArrayList<AcceptanceReimburseAttach>();
    private List<AcceptanceReimburseAttach> plateAttachs = new ArrayList<AcceptanceReimburseAttach>();
    
    /** 门店装修验收照片附件，门店装修验收及报销 */
    private List<AcceptanceReimburseAttach> decorate1Attachs = new ArrayList<AcceptanceReimburseAttach>();
    
    private List<AcceptanceReimburseAttach> decorate2Attachs = new ArrayList<AcceptanceReimburseAttach>();
    
    private List<AcceptanceReimburseAttach> decorate3Attachs = new ArrayList<AcceptanceReimburseAttach>();
    
    private List<AcceptanceReimburseAttach> decorate4Attachs = new ArrayList<AcceptanceReimburseAttach>();
    
    private List<AcceptanceReimburseAttach> decorate5Attachs = new ArrayList<AcceptanceReimburseAttach>();

    @Column(nullable = false, updatable = false, unique = true, length = 100)
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public ShopInfo getShopInfo() {
        return shopInfo;
    }

    public void setShopInfo(ShopInfo shopInfo) {
        this.shopInfo = shopInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public SaleOrg getSaleOrg() {
		return saleOrg;
	}

	public void setSaleOrg(SaleOrg saleOrg) {
		this.saleOrg = saleOrg;
	}

	public String getDealer() {
        return dealer;
    }

    public void setDealer(String dealer) {
        this.dealer = dealer;
    }

    public String getProvinces() {
        return provinces;
    }

    public void setProvinces(String provinces) {
        this.provinces = provinces;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDealerPhone() {
        return dealerPhone;
    }

    public void setDealerPhone(String dealerPhone) {
        this.dealerPhone = dealerPhone;
    }

    public String getRegionalManager() {
        return regionalManager;
    }

    public void setRegionalManager(String regionalManager) {
        this.regionalManager = regionalManager;
    }

    public String getRegionalManagerPhone() {
        return regionalManagerPhone;
    }

    public void setRegionalManagerPhone(String regionalManagerPhone) {
        this.regionalManagerPhone = regionalManagerPhone;
    }

    public String getShopAddress() {
        return shopAddress;
    }

    public void setShopAddress(String shopAddress) {
        this.shopAddress = shopAddress;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    public ShopDevise getShopDevise() {
        return shopDevise;
    }

    public void setShopDevise(ShopDevise shopDevise) {
        this.shopDevise = shopDevise;
    }

    public String getAcceptanceCommitment() {
        return acceptanceCommitment;
    }

    public void setAcceptanceCommitment(String acceptanceCommitment) {
        this.acceptanceCommitment = acceptanceCommitment;
    }

    public Date getDecorateConstructionTime() {
        return decorateConstructionTime;
    }

    public void setDecorateConstructionTime(Date decorateConstructionTime) {
        this.decorateConstructionTime = decorateConstructionTime;
    }

    public Date getDecorateCompleteTime() {
        return decorateCompleteTime;
    }

    public void setDecorateCompleteTime(Date decorateCompleteTime) {
        this.decorateCompleteTime = decorateCompleteTime;
    }

    public BigDecimal getAcceptanceLeaseTerm() {
        return acceptanceLeaseTerm;
    }

    public void setAcceptanceLeaseTerm(BigDecimal acceptanceLeaseTerm) {
        this.acceptanceLeaseTerm = acceptanceLeaseTerm;
    }

    public BigDecimal getAcceptanceArea() {
        return acceptanceArea;
    }

    public void setAcceptanceArea(BigDecimal acceptanceArea) {
        this.acceptanceArea = acceptanceArea;
    }

    public String getAcceptanceVerify() {
        return acceptanceVerify;
    }

    public void setAcceptanceVerify(String acceptanceVerify) {
        this.acceptanceVerify = acceptanceVerify;
    }

    public String getAcceptanceShop() {
        return acceptanceShop;
    }

    public void setAcceptanceShop(String acceptanceShop) {
        this.acceptanceShop = acceptanceShop;
    }

    public String getAcceptanceVerifyArea() {
        return acceptanceVerifyArea;
    }

    public void setAcceptanceVerifyArea(String acceptanceVerifyArea) {
        this.acceptanceVerifyArea = acceptanceVerifyArea;
    }

    public String getAcceptanceVerifyScore() {
        return acceptanceVerifyScore;
    }

    public void setAcceptanceVerifyScore(String acceptanceVerifyScore) {
        this.acceptanceVerifyScore = acceptanceVerifyScore;
    }

    public String getProvincialOperationMemo() {
        return provincialOperationMemo;
    }

    public void setProvincialOperationMemo(String provincialOperationMemo) {
        this.provincialOperationMemo = provincialOperationMemo;
    }

    public String getDesignManager() {
        return designManager;
    }

    public void setDesignManager(String designManager) {
        this.designManager = designManager;
    }

    public Date getConstructionFigureCompleteTime() {
        return constructionFigureCompleteTime;
    }

    public void setConstructionFigureCompleteTime(Date constructionFigureCompleteTime) {
        this.constructionFigureCompleteTime = constructionFigureCompleteTime;
    }

    public BigDecimal getDesignManagerArea() {
        return designManagerArea;
    }

    public void setDesignManagerArea(BigDecimal designManagerArea) {
        this.designManagerArea = designManagerArea;
    }

    public BigDecimal getDesignManagerScore() {
        return designManagerScore;
    }

    public void setDesignManagerScore(BigDecimal designManagerScore) {
        this.designManagerScore = designManagerScore;
    }

    public String getDesignManagerMemo() {
        return designManagerMemo;
    }

    public void setDesignManagerMemo(String designManagerMemo) {
        this.designManagerMemo = designManagerMemo;
    }

    public String getDirectorOpinion() {
        return directorOpinion;
    }

    public void setDirectorOpinion(String directorOpinion) {
        this.directorOpinion = directorOpinion;
    }

    public Integer getPayDeposit() {
        return payDeposit;
    }

    public void setPayDeposit(Integer payDeposit) {
        this.payDeposit = payDeposit;
    }

    public String getDirectorOpinionMemo() {
        return directorOpinionMemo;
    }

    public void setDirectorOpinionMemo(String directorOpinionMemo) {
        this.directorOpinionMemo = directorOpinionMemo;
    }

    public String getDirectorOpinionMoney1() {
        return directorOpinionMoney1;
    }

    public void setDirectorOpinionMoney1(String directorOpinionMoney1) {
        this.directorOpinionMoney1 = directorOpinionMoney1;
    }

    public String getDirectorOpinionMoney2() {
        return directorOpinionMoney2;
    }

    public void setDirectorOpinionMoney2(String directorOpinionMoney2) {
        this.directorOpinionMoney2 = directorOpinionMoney2;
    }

    public String getDirectorOpinionMoney3() {
        return directorOpinionMoney3;
    }

    public void setDirectorOpinionMoney3(String directorOpinionMoney3) {
        this.directorOpinionMoney3 = directorOpinionMoney3;
    }

    public Integer getFloorManagerOpinion() {
        return floorManagerOpinion;
    }

    public void setFloorManagerOpinion(Integer floorManagerOpinion) {
        this.floorManagerOpinion = floorManagerOpinion;
    }

    public String getShopAuthorizationCodes() {
		return shopAuthorizationCodes;
	}

	public void setShopAuthorizationCodes(String shopAuthorizationCodes) {
		this.shopAuthorizationCodes = shopAuthorizationCodes;
	}

	public String getArchivesCodes() {
		return archivesCodes;
	}

	public void setArchivesCodes(String archivesCodes) {
		this.archivesCodes = archivesCodes;
	}

	public Date getNewTime() {
		return newTime;
	}

	public void setNewTime(Date newTime) {
		this.newTime = newTime;
	}

	public String getSzyj() {
		return szyj;
	}

	public void setSzyj(String szyj) {
		this.szyj = szyj;
	}

	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AcceptanceReimburseAttach> getAcceptanceCommitmentAttachs() {
        return acceptanceCommitmentAttachs;
    }
    public void setAcceptanceCommitmentAttachs(List<AcceptanceReimburseAttach> acceptanceCommitmentAttachs) {
        this.acceptanceCommitmentAttachs = acceptanceCommitmentAttachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AcceptanceReimburseAttach> getStorePictureAttachs() {
        return storePictureAttachs;
    }
    public void setStorePictureAttachs(List<AcceptanceReimburseAttach> storePictureAttachs) {
        this.storePictureAttachs = storePictureAttachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AcceptanceReimburseAttach> getStoreContractAttachs() {
        return storeContractAttachs;
    }
    public void setStoreContractAttachs(List<AcceptanceReimburseAttach> storeContractAttachs) {
        this.storeContractAttachs = storeContractAttachs;
    }

//    @OneToMany(mappedBy = "acceptanceReimburse", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AcceptanceReimburseAttach> getShopFarAttachs() {
        return shopFarAttachs;
    }

    public void setShopFarAttachs(List<AcceptanceReimburseAttach> shopFarAttachs) {
        this.shopFarAttachs = shopFarAttachs;
    }
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AcceptanceReimburseAttach> getShopDecorateAttachs() {
        return shopDecorateAttachs;
    }

    public void setShopDecorateAttachs(List<AcceptanceReimburseAttach> shopDecorateAttachs) {
        this.shopDecorateAttachs = shopDecorateAttachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AcceptanceReimburseAttach> getPayPlatformAttachs() {
        return payPlatformAttachs;
    }

    public void setPayPlatformAttachs(List<AcceptanceReimburseAttach> payPlatformAttachs) {
        this.payPlatformAttachs = payPlatformAttachs;
    }
    
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("seq asc")
    public List<AcceptanceReimburseAttach> getPlateAttachs() {
        return plateAttachs;
    }

    public void setPlateAttachs(List<AcceptanceReimburseAttach> plateAttachs) {
        this.plateAttachs = plateAttachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    public List<AcceptanceReimburseAttach> getDecorate1Attachs() {
        return decorate1Attachs;
    }

    public void setDecorate1Attachs(List<AcceptanceReimburseAttach> decorate1Attachs) {
        this.decorate1Attachs = decorate1Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    public List<AcceptanceReimburseAttach> getDecorate2Attachs() {
        return decorate2Attachs;
    }

    public void setDecorate2Attachs(List<AcceptanceReimburseAttach> decorate2Attachs) {
        this.decorate2Attachs = decorate2Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    public List<AcceptanceReimburseAttach> getDecorate3Attachs() {
        return decorate3Attachs;
    }

    public void setDecorate3Attachs(List<AcceptanceReimburseAttach> decorate3Attachs) {
        this.decorate3Attachs = decorate3Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    public List<AcceptanceReimburseAttach> getDecorate4Attachs() {
        return decorate4Attachs;
    }

    public void setDecorate4Attachs(List<AcceptanceReimburseAttach> decorate4Attachs) {
        this.decorate4Attachs = decorate4Attachs;
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    public List<AcceptanceReimburseAttach> getDecorate5Attachs() {
        return decorate5Attachs;
    }

    public void setDecorate5Attachs(List<AcceptanceReimburseAttach> decorate5Attachs) {
        this.decorate5Attachs = decorate5Attachs;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public String getSubsidy() {
        return subsidy;
    }

    public void setSubsidy(String subsidy) {
        this.subsidy = subsidy;
    }

    public String getDecorationState() {
        return decorationState;
    }

    public void setDecorationState(String decorationState) {
        this.decorationState = decorationState;
    }

    public Date getChangeDecorationStateTime() {
        return changeDecorationStateTime;
    }

    public void setChangeDecorationStateTime(Date changeDecorationStateTime) {
        this.changeDecorationStateTime = changeDecorationStateTime;
    }
    @ManyToOne(fetch = FetchType.LAZY)
    public StoreMember getLastOperator() {
        return lastOperator;
    }

    public void setLastOperator(StoreMember lastOperator) {
        this.lastOperator = lastOperator;
    }

    public Date getApproveAcceptanceTime() {
        return approveAcceptanceTime;
    }

    public void setApproveAcceptanceTime(Date approveAcceptanceTime) {
        this.approveAcceptanceTime = approveAcceptanceTime;
    }

	public Date getDesignManagerCheckTime() {
		return designManagerCheckTime;
	}

	public void setDesignManagerCheckTime(Date designManagerCheckTime) {
		this.designManagerCheckTime = designManagerCheckTime;
	}
}
