package net.shopxx.shop.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.Tort;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("tortDao")
public class TortDao extends DaoCenter{
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	
	public List<Map<String, Object>> findTortAttach(Long id,Integer type) {
		List<Object> list = new ArrayList<Object>();
		String sql = "SELECT * FROM xx_tort_attach WHERE company_info_id = ? and torts = ? and type = ?";
		list.add(WebUtils.getCurrentCompanyInfoId());
		list.add(id);
		list.add(type);
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}
	
	
	public List<Map<String, Object>> findComplaintAttach(Long id) {
		List<Object> list = new ArrayList<Object>();
		String sql = "SELECT * FROM xx_complaint_attach WHERE company_info_id = ? and torts = ?";
		list.add(WebUtils.getCurrentCompanyInfoId());
		list.add(id);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}
	
	public List<Map<String, Object>> findNoticeAttach(Long id) {
		List<Object> list = new ArrayList<Object>();
		String sql = "SELECT * FROM xx_notice_attach WHERE company_info_id = ? and torts = ?";
		list.add(WebUtils.getCurrentCompanyInfoId());
		list.add(id);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}
	
	public Page<Map<String, Object>> findPage(Tort t, List<Object> param, Pageable pageable) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		Object storeId = param.get(0);
	    String createName= (String) param.get(1);
	    String firstTime = (String) param.get(2);
	    String lastTime = (String) param.get(3);
		sql.append(" SELECT t.*, ");
		sql.append(" s.`name` store_name,s.dealer_name dealer_names,s.grant_code,s.alias,s.region, ");
		sql.append(" s.active_date, ");
		sql.append("( CASE s.is_core_stroe WHEN 1 THEN '是' WHEN 0 THEN '否' END ) is_core,");
        sql.append("( CASE s.category WHEN 1 THEN '一城一商' WHEN 0 THEN '一城多商' END ) store_category,");
        sql.append(" sm.`name` create_name,salesman.`name` salesman_name ");
		sql.append(" FROM xx_tort t ");
		sql.append(" LEFT JOIN xx_store s ON s.id = t.store ");
		sql.append(" LEFT JOIN xx_sale_org so ON so.id = s.sale_org ");
		sql.append(" LEFT JOIN xx_store_member sm ON sm.username = t.b_creater ");
		sql.append(" LEFT JOIN xx_store_member salesman ON salesman.id = s.store_member ");
		sql.append(" WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND t.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(t.getSn())){
            sql.append(" AND t.sn = ?");
            list.add(t.getSn());
        }
        if(!ConvertUtil.isEmpty(t.getStatus())){
			sql.append(" and t.status = ? ");
			list.add(t.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and t.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and t.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}

        sql.append(" GROUP BY t.id ORDER BY t.create_date DESC ");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}
	
	public Integer count(Tort t, List<Object> param) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		Object storeId = param.get(0);
	    String createName= (String) param.get(1);
	    String firstTime = (String) param.get(2);
	    String lastTime = (String) param.get(3);
		sql.append(" SELECT count(distinct t.id) FROM xx_tort t ");
		sql.append(" LEFT JOIN xx_store s ON s.id = t.store ");
		sql.append(" LEFT JOIN xx_sale_org so ON so.id = s.sale_org ");
		sql.append(" LEFT JOIN xx_store_member sm ON sm.username = t.b_creater ");
		sql.append(" LEFT JOIN xx_store_member salesman ON salesman.id = s.store_member ");
		sql.append(" WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND t.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(t.getSn())){
            sql.append(" AND t.sn = ?");
            list.add(t.getSn());
        }
        if(!ConvertUtil.isEmpty(t.getStatus())){
			sql.append(" and t.status = ? ");
			list.add(t.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and t.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and t.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findInt(sql.toString(), objs);
	}

	public List<Map<String, Object>> findList(Tort t, List<Object> param, Pageable pageable, Integer page,
                                              Integer size) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		Object storeId = param.get(0);
	    String createName= (String) param.get(1);
	    String firstTime = (String) param.get(2);
	    String lastTime = (String) param.get(3);
		sql.append(" SELECT t.*, ");
		sql.append(" s.`name` store_name,s.dealer_name dealer_names,s.grant_code,s.alias,s.region, ");
		sql.append(" s.active_date, ");
		sql.append("( CASE s.is_core_stroe WHEN 1 THEN '是' WHEN 0 THEN '否' END ) is_core,");
        sql.append("( CASE s.category WHEN 1 THEN '一城一商' WHEN 0 THEN '一城多商' END ) store_category,");
        sql.append(" sm.`name` create_name,salesman.`name` salesman_name ");
		sql.append(" FROM xx_tort t ");
		sql.append(" LEFT JOIN xx_store s ON s.id = t.store ");
		sql.append(" LEFT JOIN xx_sale_org so ON so.id = s.sale_org ");
		sql.append(" LEFT JOIN xx_store_member sm ON sm.username = t.b_creater ");
		sql.append(" LEFT JOIN xx_store_member salesman ON salesman.id = s.store_member ");
		sql.append(" WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" AND t.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(t.getSn())){
            sql.append(" AND t.sn = ?");
            list.add(t.getSn());
        }
        if(!ConvertUtil.isEmpty(t.getStatus())){
			sql.append(" and t.status = ? ");
			list.add(t.getStatus());
		}
        if(!ConvertUtil.isEmpty(storeId)){
			sql.append(" and s.id = ? ");
			list.add(storeId);
		}
        if(!ConvertUtil.isEmpty(createName)){
			sql.append(" and sm.`name` like ? ");
			list.add("%"+createName+"%");
		}
        if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" and t.create_date >= ?");
			list.add(DateUtil.convert(firstTime + " 00:00:00"));
		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" and t.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
		}
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		String storeAuth = storeMemberBaseService.storeAuth();
		Long storeMemberId = storeMember.getId();
		if (storeAuth != null) {
			if ((storeMember.getMemberType() == 1 || (storeMember.getIsSalesman() && !storeMember.getIsActiveAdministrator()))) {
				String salesman = storeMemberBaseService.findSalesman(storeMemberId);
				if (!salesman.equals("")) {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member in(" + salesman + ") ) ");
				} else {
					sql.append(" and (s.id in (" + storeAuth + ") or s.store_member = ?) )");
					list.add(storeMemberId);
				}
			} else {
				sql.append(" and (so.id in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?) "
						+ " or so.id in (select  a.id from xx_sale_org a,xx_sale_org b "
						+ " where a.tree_path like concat('%,', b.id, ',%') "
						+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
						+ " where smo.sale_org = s.id and smo.store_member = ?)))");
				list.add(storeMemberId);
				list.add(storeMemberId);
			}
		}
        sql.append(" GROUP BY t.id ORDER BY t.create_date DESC ");
        if (page != null && size != null) {
        	sql.append(" limit " + (size * (page - 1)) + "," + size);
        }
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
}
