package net.shopxx.shop.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.DateUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

@Repository("shopOrderDao")
public class ShopOrderDao extends DaoCenter {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	
	public Page<Map<String, Object>> findPage(List<Object> param,Long[] productId,Pageable pageable){
		Object sn = param.get(0);//订单号
		Object ticketStatus = param.get(1);
		Object measureStatus = param.get(2);
		Object installStatus = param.get(3);
		Object payStatus = param.get(4);
		Object paymentStatus = param.get(5);
		Object shippingStatus = param.get(6);
		Object evaluateStatus = param.get(7);
		Object ludanren = param.get(8);//录单人
		Object firstTime = param.get(9);//起始时间
		Object lastTime = param.get(10);//结束时间
		Object shopName = param.get(11);//门店名称
		Object consignee = param.get(12);//顾客姓名
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT t.*,ts.name shop_name,sm.name ludanren "
				+ "FROM ticket_order t"
				+ " LEFT JOIN ticket_order_item toi ON toi.ticket_order = t.id"
				+ " LEFT JOIN ticket_store ts ON t.ticket_store = ts.id "
				+ " LEFT JOIN xx_shop_info si ON ts.shop_info = si.id "
				+ " LEFT JOIN xx_store s ON s.id = si.store "
				+ " LEFT JOIN xx_store_member sm ON sm.id = t.auth_store_member "
				+ "WHERE 1=1 ");
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND t.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" AND t.ticket_sn like ?");
			list.add("%" + sn.toString() + "%");
		}
		if (ticketStatus!=null){
			sql.append(" AND t.ticket_status = ? ");
			list.add(ticketStatus.toString());
		}
		if (measureStatus!=null){
			sql.append(" AND t.measure_status = ? ");
			list.add(measureStatus.toString());
		}
		if (installStatus!=null){
			sql.append(" AND t.install_status = ? ");
			list.add(installStatus.toString());
		}
		if (payStatus!=null){
			sql.append(" AND t.pay_status = ? ");
			list.add(payStatus.toString());
		}
		if (paymentStatus!=null){
			sql.append(" AND t.payment_status = ?");
			list.add(paymentStatus.toString());
		}
		if (shippingStatus!=null){
			sql.append(" AND t.shipping_status = ?");
			list.add(shippingStatus.toString());
		}
		if (evaluateStatus!=null){
			sql.append(" AND t.evaluate_status = ?");
			list.add(evaluateStatus.toString());
		}
		if (ludanren!=null){
			sql.append(" AND sm.name like ?");
			list.add("%" + ludanren.toString()+ "%");
		}
		if (!ConvertUtil.isEmpty(firstTime)) {
			sql.append(" AND t.create_date >= ?");
			list.add(DateUtil.convert(firstTime.toString() + " 00:00:00"));

		}
		if (!ConvertUtil.isEmpty(lastTime)) {
			sql.append(" AND t.create_date < ?");
			list.add(DateUtils.addDays(DateUtil.convert(lastTime.toString() + " 00:00:00"),
					1));
		}
		if (shopName!=null){
			sql.append(" AND ts.shop_name = ?");
			list.add(shopName.toString());
		}
		if (consignee!=null){
			sql.append(" AND t.consignee like ?");
			list.add("%"+consignee.toString()+"%");
		}
		if (productId!=null&& productId.length>0){
			String p = "";
			for(int i=0;i<productId.length;i++){
				if(i==productId.length-1){
					p += productId[i].toString(); 
				}else{
					p += productId[i].toString()+",";
				}
			}
			sql.append(" AND toi.product in("+p+")");
		}
		
        if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and si.store in (" + storeAuth + ") ");
			}
		}
		
		sql.append(" GROUP BY t.id ORDER BY t.create_date  DESC ");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		

		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);

		return page;
	}
	
	public List<Map<String,Object>> findOrder(){
		String sql = "select t.ticket_status,t.measure_status,t.install_status,"
				+"t.pay_status,t.payment_status,t.shipping_status,t.evaluate_status,"
				+ "si.shop_name from ticket_order t "
				+ "LEFT JOIN ticket_store ts on ts.id = t.ticket_store "
				+ "LEFT JOIN xx_shop_info si ON si.id = ts.shop_info where t.company_info_id = 9 ";
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql +=" and si.store in (" + storeAuth + ") ";
			}
		}
		
		return getNativeDao().findListMap(sql, null, 0);
	}
	
	public List<Map<String,Object>> findOrder(Long id){
		if(id==null||id.equals("")){
			return null;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT o.id,o.ticket_sn,o.ticket_status,o.create_date,o.type,shop.sn shop_sn,shop.`name` shop_name,"
			 +"o.total_amount,o.trade_amount,IFNULL(o.trade_amount_paid,0) trade_amount_paid,o.amount_paid,o.trade_note,"
			 + "o.phone,o.consignee,o.dealer_address,s.dealer_name,sm.name ludanren,"
			 + "measure_status,install_status,pay_status,"
			 + "designate_measure_name,designate_measure_phone,"
			 + "designate_install_name,designate_install_phone FROM "
			 +" ticket_order o LEFT JOIN ticket_store shop ON shop.id = o.ticket_store"
			+ " LEFT JOIN xx_shop_info si ON si.id = shop.shop_info "
			+ " LEFT JOIN xx_store s ON	s.id = si.store "
			+ " LEFT JOIN xx_store_member sm ON sm.id = o.auth_store_member "
			+ " WHERE 1=1");
		sql.append(" AND o.id = ?");
		return getNativeDao().findListMap(sql.toString(), new Object[]{id}, 0);
	}
	
	//用订单id查明细
	//用明细id查明细
	//用多个订单id查明细
	public List<Map<String,Object>> findOrderItem(Long orderId,Long id,String ids){
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		
		sql.append("SELECT t.*,(IFNULL(t.quantity,0) - IFNULL(t.shipped_quantity,0)) deliverable_quantity,"
				+ "(IFNULL(t.shipped_quantity,0) - IFNULL(t.return_quantity,0)) deliverable_return_quantity,"
				+ "p.vonder_code vc,p.description d,p.id product_id,p.unit u,p.per_box,p.per_branch,p.branch_per_box,"
				+ "IFNULL(pc1.`name`,pc.`name`) pc_name,pc2.`name` super_name,(IFNULL(t.quantity,0)/IFNULL(p.per_branch,0)) branch_number,"
				+ "ts.shop_info,si.store,o.id order_id,(IFNULL(t.quantity,0)/IFNULL(p.per_box,0)) box_number"
				+ " FROM ticket_order o "
				+ " LEFT JOIN ticket_order_item t ON o.id = t.ticket_order "
				+ " LEFT JOIN xx_product p ON p.id = t.product " 
				+ " LEFT JOIN xx_product_category pc on pc.id = p.product_category "
				+ " LEFT JOIN xx_product_category pc1 on pc1.id = pc.parent " 
				+ " LEFT JOIN xx_product_category pc2 on pc2.id = pc1.parent "
				+ " LEFT JOIN ticket_store ts ON ts.id = o.ticket_store " 
				+ " LEFT JOIN xx_shop_info si ON si.id = ts.shop_info WHERE 1=1 AND t.product is not null");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND t.company_info_id = ?");
			list.add(companyInfoId);
		}
		
		if (!ConvertUtil.isEmpty(orderId)) {
			sql.append(" AND t.ticket_order = ?");
			list.add(orderId);
		}
		
		if (!ConvertUtil.isEmpty(id)) {
			sql.append(" AND t.id = ?");
			list.add(id);
		}
		
		if (ids!=null) {
			sql.append(" AND t.ticket_order IN("+ids+")");
		}
		
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
	
	public List<Map<String,Object>> findOrderRecord(Long id,Integer type){
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM xx_shop_order_record WHERE 1=1");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(id)) {
			sql.append(" AND order_id = ?");
			list.add(id);
		}
		if (!ConvertUtil.isEmpty(type)) {
			sql.append(" AND type = ?");
			list.add(type);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
	
	public List<Map<String,Object>> findShippingAndReturn(Long id,Integer type){
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ssi.description,ssi.shipping_quantity,"
				+ "ssi.vonder_code,ssi.category,s.`name`,"
				+ "s.create_date,s.memo FROM xx_shop_order_record s "
				+ "LEFT JOIN xx_shop_shipping_item ssi ON ssi.shop_order_record = s.id"
				+ "  WHERE 1=1");
		
		if (!ConvertUtil.isEmpty(companyInfoId)) {
			sql.append(" AND s.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(id)) {
			sql.append(" AND s.order_id = ?");
			list.add(id);
		}
		if (!ConvertUtil.isEmpty(type)) {
			sql.append(" AND s.type = ?");
			list.add(type);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
	
	@Transactional(readOnly = true)
	public int out(Long id){//结账
		if(id==null){
			return -99;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE ticket_order SET pay_status = ? WHERE id = ? ");
		return getNativeDao().update(sql.toString(),new Object[]{2,id});
	}
	
	@Transactional(readOnly = true)
	public int updateOrderItemShippingQuantity(Long id,BigDecimal q){
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE ticket_order_item SET shipped_quantity = ? WHERE company_info_id = ? AND id = ? ");
		return getNativeDao().update(sql.toString(),new Object[]{q,WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	@Transactional(readOnly = true)
	public int updateOrderItemReturnQuantity(Long id,BigDecimal r){
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE ticket_order_item SET return_quantity = ? WHERE company_info_id = ? AND id = ? ");
		return getNativeDao().update(sql.toString(),new Object[]{r,WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	@Transactional(readOnly = true)
	public int updateOrderAmount(Long id,BigDecimal r){
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE ticket_order SET trade_amount_paid = ? WHERE company_info_id = ? AND id = ? ");
		return getNativeDao().update(sql.toString(),new Object[]{r,WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	@Transactional(readOnly = true)
	public BigDecimal findOrderAmount(Long id){
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT IFNULL(trade_amount_paid,0) trade_amount_paid FROM ticket_order WHERE company_info_id = ? AND id = ?");
		return getNativeDao().findBigDecimal(sql.toString(),new Object[]{WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	
	/**
	 * 通过门店编号查找对应门店-订单-订单明细-产品id
	 */
	public List<Map<String,Object>> findOrderItemProductId(String sn){
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT tor.product FROM	ticket_order_item tor "
				+ "LEFT JOIN ticket_order t ON t.id = tor.ticket_order "
				+ "LEFT JOIN ticket_store ts ON ts.id = t.ticket_store WHERE "
				+ "tor.product is not null AND ts.shop_info IN ("+sn+") AND tor.company_info_id = ? "
				+ "group by tor.product ");
		return getNativeDao().findListMap(sql.toString(),new Object[]{companyInfoId}, 0);
	}
	
	/**
	 * 通过产品Id查找订单明细发货数量 
	 */
	public BigDecimal findOrderItemShippingQuantity(Long productId){
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT SUM(IFNULL(tor.shipped_quantity,0)) FROM ticket_order_item tor WHERE"
				+ " tor.product = ? and tor.company_info_id = ? ");
		return getNativeDao().findBigDecimal(sql.toString(),new Object[]{productId,companyInfoId});
	}
	
	/**
	 * 通过产品Id,当前登录用户id查找产品库存
	 * 
	 */
	public BigDecimal findInventoryQuantity(Long productId,Long storeId){
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT SUM(IFNULL(sp.inventory,0)) FROM xx_storage_product sp "
				+ "LEFT JOIN xx_set_storage ss ON  sp.storage = ss.id "
				+ "LEFT JOIN xx_product p ON p.id = sp.product "
				+ "WHERE sp.company_info_id = ? AND ss.store = ? AND p.id = ?");
		return getNativeDao().findBigDecimal(sql.toString(),new Object[]{companyInfoId,storeId,productId,});
	}
	
	
	public BigDecimal findSecurityQuantity(Long productId){
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT SUM(quantity) FROM xx_shop_replenishment sr WHERE sr.type = 0 AND sr.company_info_id = ?  AND sr.product = ?");
		return getNativeDao().findBigDecimal(sql.toString(),new Object[]{companyInfoId,productId});
	}
	
	public List<Map<String,Object>> findProduct(Long productId){//查找产品
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT * FROM xx_product p WHERE p.company_info_id = ? AND p.is_marketable = 1 AND p.id = ?");
		return getNativeDao().findListMap(sql.toString(),new Object[]{companyInfoId,productId},0);
	}
	
	@Transactional
	public int updeteMeasureDesignate(String name,String phone,String id){//更新测量人员
		String sql = "UPDATE ticket_order SET designate_measure_name = ? ,designate_measure_phone = ?,measure_status=2 WHERE company_info_id = ? AND id = ?";
		return getNativeDao().update(sql, new Object[]{name,phone,WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	@Transactional
	public int updeteInstallDesignate(String name,String phone,String id){//更新安装人员
		String sql = "UPDATE ticket_order SET designate_install_name = ? ,designate_install_phone = ?,install_status=2 WHERE company_info_id = ? AND id = ?";
		return getNativeDao().update(sql, new Object[]{name,phone,WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	@Transactional
	public int updeteMeasureComplete(String id){//更新测量状态
		String sql = "UPDATE ticket_order SET measure_status=3 WHERE company_info_id = ? AND id = ?";
		return getNativeDao().update(sql, new Object[]{WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	@Transactional
	public int updeteInstallComplete(String id){//更新安装状态
		String sql = "UPDATE ticket_order SET install_status=3 WHERE company_info_id = ? AND id = ?";
		return getNativeDao().update(sql, new Object[]{WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	public Integer findOrderStatus(Long id){//查订单状态
		String sql = "SELECT IFNULL(ticket_status,-99) FROM ticket_order WHERE company_info_id = ? AND id = ?";
		return getNativeDao().findInt(sql, new Object[]{WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	@Transactional
	public int cancel(Long id){//作废订单
		String sql = "UPDATE ticket_order SET ticket_status=4 WHERE company_info_id = ? AND id = ?";
		return getNativeDao().update(sql, new Object[]{WebUtils.getCurrentCompanyInfoId(),id});
	}
	
	/**
	 * 状态转换 
	 * 
	 */
	@Transactional
	public int conversionStatus(Long[] ids,Integer status,String statusName){
		if(statusName==null){
			ExceptionUtil.throwServiceException("状态名称为空！");
		}
		String sql = "UPDATE ticket_order SET "+statusName+" =? WHERE company_info_id = ? ";
		if(ids!=null&&ids.length>0){
			String c = "";
			for(int i=0;i<ids.length;i++){
				if(i==ids.length-1){
					c += ids[i].toString(); 
				}else{
					c += ids[i].toString()+",";
				}
			}
			sql += " AND id IN("+c+")";
		}
		return getNativeDao().update(sql, new Object[]{status,WebUtils.getCurrentCompanyInfoId()});
	}
	
	
}
