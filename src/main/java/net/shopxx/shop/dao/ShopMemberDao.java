package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.util.ExceptionUtil;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

@Repository("shopMemberDao")
public class ShopMemberDao extends DaoCenter {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	public List<Map<String, Object>> findListMap() {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();

		sql.append("select s.shop_status,s.devise_status,s.decoration_status from xx_shop_info s where 1=1 ");
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		sql.append(" order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	public Page<Map<String, Object>> findPage(Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(
				" SELECT si.shop_name,tsm.type,sm.name,m.mobile,sm.gender,tsm.id_card,tsm.id tsm_id FROM ticket_store_member tsm "
						+ " LEFT JOIN ticket_store ts ON ts.id = tsm.ticket_store "
						+ " LEFT JOIN xx_store_member sm ON sm.id = tsm.store_member"
						+ " LEFT JOIN xx_member m ON m.id = sm.member " 
						+ " LEFT JOIN xx_shop_info si ON si.sn = ts.sn "
						+ " WHERE 1=1 ");

		if (companyInfoId != null) {
			sql.append(" AND tsm.company_info_id = ?");
			list.add(companyInfoId);
		}
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (storeMember.getMemberType()==1){
			sql.append(" and si.id in (SELECT shop_info FROM xx_store_member_shop_info smsi WHERE smsi.store_member = ?)");
			list.add(WebUtils.getCurrentStoreMemberId());
		}else{
			
		}
		sql.append(" AND ts.shop_info is not null");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), objs, pageable);
		return page;
	}

	public List<Map<String, Object>> findAppStoreMember(Long id) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT si.shop_name,tsm.type,sm.name,m.mobile,sm.gender,"
				+ "tsm.id_card,tsm.id tsm_id,ts.shop_info,tsm.login_store_member FROM ticket_store_member tsm "
				+ " LEFT JOIN ticket_store ts ON ts.id = tsm.ticket_store "
				+ " LEFT JOIN xx_store_member sm ON sm.id = tsm.store_member"
				+ " LEFT JOIN xx_member m ON m.id = sm.member " + " LEFT JOIN xx_shop_info si ON si.sn = ts.sn "
				+ " WHERE 1=1 ");

		if (companyInfoId != null) {
			sql.append(" AND tsm.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (id != null) {
			sql.append(" AND tsm.id = ?");
			list.add(id);
		}
		sql.append(" AND ts.shop_info is not null");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql.toString(), objs, 0);
	}

	public void delete(Long id) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" DELETE FROM xx_pc_user_role WHERE store_member = ? AND company_info_id = ? ");
		getNativeDao().delete(sql.toString(), new Object[] { id, companyInfoId });
	}

	public int saveAppStoreMember(Long appStoreMemberId,String createBy,Integer type,String IdCard,Long loginStoreMember) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO ticket_store_member (create_date,modify_date,company_info_id,ticket_store,create_by,type,id_card,login_store_member) "
				+ " VALUES (NOW(),NOW(),?,?,?,?,?,?)");
		return getNativeDao().insert(sql.toString(), new Object[] { companyInfoId,appStoreMemberId,createBy==null?"":createBy,type,IdCard==null?"":IdCard,loginStoreMember});
	}
	
	public Long findAppStoreMemberId(Long shopInfoId) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT id FROM ticket_store WHERE company_info_id = ? AND shop_info = ?");
		return getNativeDao().findLong(sql.toString(), new Object[] { companyInfoId, shopInfoId });
	}
	
	public void appMember(Long member,Long appId) {
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE ticket_store_member SET login_store_member = ? WHERE company_info_id = ? AND id = ?");
		getNativeDao().update(sql.toString(), new Object[] {member,companyInfoId, appId });
	}

	public List<Map<String,Object>> findShopMember(Long id){
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("SELECT id,store_member FROM ticket_store_member  "
				+ "WHERE company_info_id = ? AND type <> 4 AND ticket_store in "
				+ "(SELECT id FROM ticket_store WHERE shop_info = ?)");
		return getNativeDao().findListMap(sql.toString(), new Object[]{companyInfoId,id},0);
	}

	public void deleteTickStoreMember(Long tsm_id){
		if(tsm_id == null){
			ExceptionUtil.throwServiceException("ID不能为空！");
		}
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("DELETE FROM ticket_store_member WHERE company_info_id = ? AND id = ?");
		getNativeDao().delete(sql.toString(), new Object[]{companyInfoId,tsm_id});
	}

	public void deleteStoreMemberShopInfo(Long id){
		String sql = "DELETE FROM xx_store_member_shop_info WHERE store_member = ?";
		getNativeDao().delete(sql, new Object[]{id});
	}
}
