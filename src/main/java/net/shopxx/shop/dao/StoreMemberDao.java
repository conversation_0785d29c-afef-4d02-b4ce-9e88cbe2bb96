package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;


@Repository("storeMemberDao")
public class StoreMemberDao extends DaoCenter{
	
	/**
	 * 查找用户分页数据
	 * 
	 * @param username
	 * @param mobile
	 * @param name
	 * @param memberType
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findPage(Pageable pageable, Object[] args) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		String username = (String) args[0];
		String mobile = (String) args[1];
		String name = (String) args[2];
		Integer memberType = (Integer) args[3];
		Boolean isSalesman = (<PERSON><PERSON>an) args[4];

		StringBuilder sql = new StringBuilder();
		sql.append("select s.id,s.username,m.mobile,s.`name` from xx_store_member s ");
		sql.append(" left join xx_store ss on s.store=ss.id ");
		sql.append(" left join xx_member m on s.member = m.id ");
		sql.append(" left join xx_store_member_sale_org smso on s.id=smso.store_member ");
		sql.append(" left join xx_sale_org so on so.id=smso.sale_org ");
		sql.append(" where ss.is_main_store = 1 and s.is_enabled = true ");
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ? and ss.company_info_id = ? ");
			list.add(companyInfoId);
			list.add(companyInfoId);
		}
		if (StringUtils.isNotEmpty(username)) {
			sql.append(" and s.username like ?");
			list.add("%" + username + "%");
		}
		if (StringUtils.isNotEmpty(mobile)) {
			sql.append(" and m.mobile like ?");
			list.add("%" + mobile + "%");
		}
		if (StringUtils.isNotEmpty(name)) {
			sql.append(" and s.name like ?");
			list.add("%" + name + "%");
		}
		if (memberType != null) {
			sql.append(" and s.member_type = ?");
			list.add(memberType);
		}
		if (isSalesman !=null ){
			sql.append(" and s.is_salesman = ?");
			list.add(isSalesman);
		}
		sql.append(" group by s.id order by s.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);
	}

}
