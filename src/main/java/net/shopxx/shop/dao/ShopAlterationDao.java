package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.shop.entity.ShopInfo;

@Repository("shopAlterationDao")
public class ShopAlterationDao extends DaoCenter{
	
	
	public List<Map<String, Object>> findSA(Long shopInfoId){
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM xx_shop_alteration WHERE "
				+ " info_shop = ? AND company_info_id = ? AND statuss IN(0,1,2,3)");
		return getNativeDao().findListMap(sql.toString(), new Object[]{shopInfoId,companyInfoId}, 0);
	}
	
	public List<Map<String, Object>> findShopAlterationAttach(Long id) {
		List<Object> list = new ArrayList<Object>();
		String sql = "SELECT * FROM xx_shop_alteration_attach WHERE company_info_id = ? and alterations = ?";
		list.add(WebUtils.getCurrentCompanyInfoId());
		list.add(id);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}
	
	public Page<Map<String, Object>> findPage(String a, Pageable pageable) {
		
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_shop_alteration WHERE 1=1 ");

		if (companyInfoId != null) {
			sql.append(" AND company_info_id = ?");
			list.add(companyInfoId);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}
	
	public void update(ShopInfo shopInfo ){
		if(shopInfo==null){
			ExceptionUtil.throwServiceException("门店资料不能为空！");
		}
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		List<Map<String, Object>>  c = getNativeDao().findListMap("SELECT * FROM ticket_store WHERE company_info_id = ? AND sn = ? ", new Object[] { companyInfoId,shopInfo.getSn()}, 0);
		if(c==null){
			ExceptionUtil.throwServiceException("没有找到小程序对应的门店信息！");
		}
		System.out.println("小程序对应门店："+c.get(0).get("name"));
		sql.append("UPDATE ticket_store SET `name` = ?,area = ?,address = ? ,create_by = ?,modify_date = now() WHERE sn = ?");
		list.add(shopInfo.getShopName());
		if(shopInfo.getArea() != null){
			list.add(shopInfo.getArea().getId());
		}else{
			list.add(0);
		}
		list.add(shopInfo.getAddress());
		if(shopInfo.getStoreMember()!=null){
			list.add(shopInfo.getStoreMember().getName());			
		}else{
			list.add("");
		}
		list.add(shopInfo.getSn());
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		getNativeDao().update(sql.toString(), objs);
	}

	public List<Map<String, Object>> findShopAttach1(Long id, Integer type) {
		StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select * "
                + " from xx_shop_alteration_attach "
                + " where company_info_id = ? and alterations = ? and type = ? ");
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);
        list.add(type);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
	}
}
