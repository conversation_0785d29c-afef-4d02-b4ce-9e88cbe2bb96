package net.shopxx.shop.dao;

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("shopProductCategoryDao")
public class ShopProductCategoryDao extends DaoCenter {


    public List<Map<String, Object>> findList(String sn, String name, Boolean isEnabled) {
        StringBuffer sql = new StringBuffer();
        List<Object> params = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();

        sql.append("select * from xx_shop_product_category where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and company_info_id = ?");
            params.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sn)) {
            sql.append(" and sn like ?");
            params.add("%" + sn + "%");
        }

        if(isEnabled == null){

        }else {
            sql.append(" and is_enabled = ? ");
            params.add(isEnabled);
        }

        if (!ConvertUtil.isEmpty(name)) {
            sql.append(" and name like ?");
            params.add("%" + name + "%");
        }
        sql.append(" order by orders asc");

        List<Map<String, Object>> list = getNativeDao()
                .findListMap(sql.toString(), params.toArray(), 0);
        return list;
    }
}