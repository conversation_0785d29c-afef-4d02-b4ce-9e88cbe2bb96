package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;

@Repository("shopCostDao")
public class ShopCostDao extends DaoCenter{
	
	public Page<Map<String, Object>> findPage(String sn,Pageable pageable) {
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_shop_cost sc WHERE 1=1 ");
		if (companyInfoId != null) {
			sql.append(" AND sc.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (StringUtils.isNotEmpty(sn)){
			sql.append(" AND sc.sn = ?");
			list.add(sn);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}
}
