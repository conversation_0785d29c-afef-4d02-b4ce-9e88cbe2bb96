package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.util.DateUtil;
import net.shopxx.shop.entity.Shop;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

@Repository("shopAddedDao")
public class ShopAddedDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;

    public Page<Map<String, Object>> findPage2(Shop shop, List<Object> param, Pageable pageable) {
        Object sn = shop.getSn(); // 单号
        Object shopAuthorizationCodes = shop.getShopAuthorizationCodes();//门店授权编号
        Object shopName = shop.getShopName();//店名
        Object administrativeRank = shop.getAdministrativeRank();//行政等级
        Integer[] status =  (Integer[]) param.get(0);
        Object storeId = param.get(1);//客户
        Object saleOrgId = param.get(2);;//机构
        Object storeMemberId = param.get(3);;//区域经理
        Object region = param.get(4);//区域
        String firstShopTime = param.get(5) instanceof String ? (String) param.get(5):null;//建店起始日期
        String lastShopTime = param.get(6) instanceof String ?(String) param.get(6):null;//建店结束日期
        Object createName = param.get(7);//创建人
        String firstTime = param.get(8) instanceof String ?(String) param.get(8):null;//创建时间起始日期
        String lastTime = param.get(9) instanceof String ?(String) param.get(9):null;//创建时间结束日期
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        sql.append("select s.*, ss.grant_code s_grant_code,ss.region, ss.dealer_name s_dealer_name,ss.name s_name,ss.head_phone,ss.active_date,ss.fixed_number "
                + " ,sm.name sm_xujl_name, m.mobile m_xujl_mobile, so.name so_name,a.full_name area_name,last_operator.name last_operator_name,beginer.name beginer,so.region region_code "
                + " from xx_shop s "
                + " left join xx_store ss on s.store = ss.id "
                + " left join xx_store_member sm on ss.store_member = sm.id "
                + " left join xx_member m on sm.member = m.id "
                + " left join xx_sale_org so on ss.sale_org = so.id "
                +" left join xx_area a on s.new_shop_area = a.id "
                + " left join xx_store_member beginer on beginer.username = s.b_creater "
                +" left join xx_store_member last_operator on last_operator.id = s.last_operator "
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and s.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sn)) {
            sql.append(" and s.sn like ? ");
            list.add("%" + sn.toString() + "%");
        }
        if (status != null && status.length > 0) {
            String os = "";
            for (int i = 0; i < status.length; i++) {
                if (i == status.length - 1)
                    os += status[i];
                else
                    os += status[i] + ",";
            }
            sql.append(" and s.statuss in (" + os + ") ");
        }
        if (!ConvertUtil.isEmpty(shopAuthorizationCodes)){
            sql.append(" and s.shop_authorization_codes = ? ");
            list.add(shopAuthorizationCodes);
        }
        if (!ConvertUtil.isEmpty(shopName)){
            sql.append(" and s.shop_name like ? ");
            list.add("%"+shopName+"%");
        }
        if (!ConvertUtil.isEmpty(administrativeRank)){
            sql.append(" and s.administrative_rank = ? ");
            list.add(administrativeRank);
        }
        if (!ConvertUtil.isEmpty(storeId)){
            sql.append(" and ss.id = ? ");
            list.add(storeId);
        }
        if (!ConvertUtil.isEmpty(saleOrgId)){
            sql.append(" and so.id = ? ");
            list.add(saleOrgId);
        }
        if (!ConvertUtil.isEmpty(storeMemberId)){
            sql.append(" and sm.id = ? ");
            list.add(storeMemberId);
        }
        if (!ConvertUtil.isEmpty(region)){
            sql.append(" and so.region = ? ");
            list.add(region);
        }
        if (!ConvertUtil.isEmpty(firstShopTime)) {
            sql.append(" and s.create_shop_date >= ?");
            list.add(DateUtil.convert(firstShopTime + " 00:00:00"));
        }
        if (!ConvertUtil.isEmpty(lastShopTime)) {
            sql.append(" and s.create_shop_date < ?");
            list.add(DateUtils.addDays(DateUtil.convert(lastShopTime + " 00:00:00"),1));
        }
        if (!ConvertUtil.isEmpty(createName)){
            sql.append(" and beginer.`name` like ? ");
            list.add("%"+createName+"%");
        }
        if (!ConvertUtil.isEmpty(firstTime)) {
            sql.append(" and s.create_date >= ?");
            list.add(DateUtil.convert(firstTime + " 00:00:00"));
        }
        if (!ConvertUtil.isEmpty(lastTime)) {
            sql.append(" and s.create_date < ?");
            list.add(DateUtils.addDays(DateUtil.convert(lastTime + " 00:00:00"),1));
        }
        StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
        if (storeMember.getMemberType() == 1 || (storeMember.getIsSalesman()&&!storeMember.getIsActiveAdministrator())) {
            String storeAuth = storeMemberService.storeAuth();
            if (storeAuth != null) {
                String salesman = storeMemberService.findSalesman(storeMember.getId());
                if (!salesman.equals("")) {
                    sql.append(" and (ss.id in (" + storeAuth + ") or ss.store_member in("+salesman+") ) ");
                } else {
                    sql.append(" and (ss.id in (" + storeAuth + ") or ss.store_member = ?) ");
                    list.add(WebUtils.getCurrentStoreMemberId());
                }
            }
        }else {
            sql.append(" and (s.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = ?) "
                    + " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
                    + " where a.tree_path like concat('%,', b.id, ',%') "
                    + " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = ?)))");
            list.add(WebUtils.getCurrentStoreMemberId());
            list.add(WebUtils.getCurrentStoreMemberId());
        }

        sql.append(" GROUP BY s.id ");
        sql.append(" order by s.id desc ");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        Page<Map<String, Object>> page= getNativeDao().findPageMap(sql.toString(), objs, pageable);
        StringBuilder totalSql = new StringBuilder("select count(1) from ( ");
        totalSql.append(sql).append(") a");
        long total = getNativeDao().findInt(totalSql.toString(), objs);
        page.setTotal(total);
        return page;
    }


//	public Page<Map<String, Object>> findPage2(List<Object> param, Pageable pageable) {
//		Object sn = param.get(0); // 单号
//		Object dealer = param.get(1); // 经销商
//		Integer[] status = null; //
//		if (param.get(2) != null) {
//			status = (Integer[]) param.get(2);
//		}
//		List<Object> list = new ArrayList<Object>();
//		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
//		StringBuilder sql = new StringBuilder();
//		sql.append("select s.*, ss.grant_code s_grant_code, ss.dealer_name s_dealer_name, "
//				+ "     sm.name sm_xujl_name, m.mobile m_xujl_mobile, so.name so_name " + " from xx_shop s "
//				+ " left join xx_store ss on s.store = ss.id "
//				+ " left join xx_store_member sm on ss.store_member = sm.id "
//				+ " left join xx_member m on sm.member = m.id " + " left join xx_sale_org so on ss.sale_org = so.id "
//				+ " where 1=1 ");
//		if (companyInfoId != null) {
//			sql.append(" and s.company_info_id = ?");
//			list.add(companyInfoId);
//		}
//		if (!ConvertUtil.isEmpty(sn)) {
//			sql.append(" and s.sn like ? ");
//			list.add("%" + sn.toString() + "%");
//		}
//		if (!ConvertUtil.isEmpty(dealer)) {
//			sql.append(" and ss.dealer_name like ? ");
//			list.add("%" + dealer.toString() + "%");
//		}
//		if (status != null && status.length > 0) {
//			String os = "";
//			for (int i = 0; i < status.length; i++) {
//				if (i == status.length - 1)
//					os += status[i];
//				else
//					os += status[i] + ",";
//			}
//			sql.append(" and s.statuss in (" + os + ") ");
//		}
//
//		StoreMember storeMember = storeMemberService.find(WebUtils.getCurrentStoreMemberId());
//		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
//			String storeAuth = storeMemberService.storeAuth();
//			if (storeAuth != null) {
//				sql.append(" and s.store in (" + storeAuth + ") or ss.store_member = ?");
//				list.add(WebUtils.getCurrentStoreMemberId());
//			}
//		}else {
//			sql.append(" and (s.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
//					+ " where smo.sale_org = s.id and smo.store_member = ?) "
//					+ " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
//					+ " where a.tree_path like concat('%,', b.id, ',%') "
//					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
//					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
//			list.add(WebUtils.getCurrentStoreMemberId());
//			list.add(WebUtils.getCurrentStoreMemberId());
//		}
//
//		sql.append(" order by s.create_date desc ");
//		Object[] objs = new Object[list.size()];
//		for (int i = 0; i < list.size(); i++) {
//			objs[i] = list.get(i);
//		}
//		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
//	}

	/**
	 * 根据订单id查找对应的附件信息
	 */
	public List<Map<String, Object>> findShopAttach(Long id) {
		List<Object> list = new ArrayList<Object>();
		String sql = "SELECT * FROM xx_shop_attach WHERE company_info_id = ? and shops = ?";
		list.add(WebUtils.getCurrentCompanyInfoId());
		list.add(id);

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findListMap(sql, objs, 0);
	}

	public int init(String sn, String shopName, Area area, String address, StoreMember create_by, Long shopInfoId,Long saleOrgId) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(
				"insert into ticket_store(company_info_id,sn,`name`,balance,area,address,"
				+ "is_enabled,create_by,shop_info,sale_org,create_date,modify_date) "
				+ "VALUES(?,?,?,?,?,?,?,?,?,?,now(),now())");
		list.add(WebUtils.getCurrentCompanyInfoId());
		list.add(sn);
		list.add(shopName);
		list.add(0);
		if (area.getId() != null) {
			list.add(area.getId());
		} else {
			list.add(0);
		}
		list.add(address);
		list.add(true);
		if (create_by.getId() != null) {
			list.add(create_by.getName());
		} else {
			list.add("");
		}
		list.add(shopInfoId);
		list.add(saleOrgId);
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		int sa = getNativeDao().insert(sql.toString(), objs);
		return sa;
	}

	public List<Map<String, Object>> findShopAttach1(Long id, Integer type) {
		StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select * "
                + " from xx_shop_attach "
                + " where company_info_id = ? and shops = ? and type = ? ");
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);
        list.add(type);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }
}
