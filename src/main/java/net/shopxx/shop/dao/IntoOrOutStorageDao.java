package net.shopxx.shop.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.service.ShopStoreService;

@Repository("intoOrOutStorageDao")
public class IntoOrOutStorageDao extends DaoCenter {
    
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "companyInfoBaseServiceImpl")
    private CompanyInfoBaseService companyInfoBaseService;
    @Resource(name="shopStoreServiceImpl")
    private ShopStoreService shopStoreService;

    public Page<Map<String, Object>> findInPage(List<Object> param, Pageable pageable) {
        Object sn = param.get(0);
        Object type = param.get(1);
        Integer[] status = null;
        if (param.get(2) != null) {
            status = (Integer[]) param.get(2);
        }
        Object orderSn = param.get(3);
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select ioos.*, ss.name s_name,t.ticket_sn order_sn  "
                + " from xx_into_or_out_storage ioos "
                + " left join xx_set_storage ss on ss.id = ioos.storage "
                + " LEFT JOIN ticket_order t ON t.id = ioos.order_id "
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and ioos.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!StringUtils.isEmpty(sn)) {
            sql.append(" and ioos.sn like ? ");
            list.add("%" + sn.toString() + "%");
        }
        if (type != null) {
            sql.append(" and ioos.type = ? ");
            list.add(type.toString());
        }
        if (status != null && status.length > 0) {
            String os = "";
            for (int i = 0; i < status.length; i++) {
                if (i == status.length - 1)
                    os += status[i];
                else
                    os += status[i] + ",";
            }
            sql.append(" and ioos.status in (" + os + ")");
        }
        if( !StringUtils.isEmpty(orderSn)){
        	sql.append(" and t.ticket_sn like ? ");
            list.add("'%"+orderSn.toString()+"%'");
        }
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (storeMember.getMemberType() == 1) {
			sql.append(" and ss.store in (SELECT s.id FROM xx_store s "
					+ "LEFT JOIN xx_store_member sm ON sm.store = s.id "
					+ "WHERE s.company_info_id = ? and s.type != 0 and sm.member = "
					+ "(SELECT member FROM xx_store_member WHERE id = ? ))");
			list.add(WebUtils.getCurrentCompanyInfoId());
			list.add(WebUtils.getCurrentStoreMemberId());
		} else {

		}
        sql.append(" order by ioos.create_date desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }

    public Page<Map<String, Object>> findOutPage(List<Object> param, Pageable pageable) {
        Object sn = param.get(0);
        Object type = param.get(1);
        Integer[] status = null;
        if (param.get(2) != null) {
            status = (Integer[]) param.get(2);
        }
        Object orderSn = param.get(3);
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select ioos.*, ss.name s_name,t.ticket_sn order_sn "
                + " from xx_into_or_out_storage ioos "
                + " left join xx_set_storage ss on ss.id = ioos.storage "
                + " LEFT JOIN ticket_order t ON t.id = ioos.order_id "
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and ioos.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!StringUtils.isEmpty(sn)) {
            sql.append(" and ioos.sn like ? ");
            list.add("%" + sn.toString() + "%");
        }
        if (type != null) {
            sql.append(" and ioos.type = ? ");
            list.add(type.toString());
        }
        if (status != null && status.length > 0) {
            String os = "";
            for (int i = 0; i < status.length; i++) {
                if (i == status.length - 1)
                    os += status[i];
                else
                    os += status[i] + ",";
            }
            sql.append(" and ioos.status in (" + os + ")");
        }
        if( !StringUtils.isEmpty(orderSn)){
        	sql.append(" and t.ticket_sn like ? ");
            list.add("'%"+orderSn.toString()+"%'");
        }
        StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
        if (storeMember.getMemberType() == 1) {
			sql.append(" and ss.store in (SELECT s.id FROM xx_store s "
					+ "LEFT JOIN xx_store_member sm ON sm.store = s.id "
					+ "WHERE s.company_info_id = ? and s.type != 0 and sm.member = "
					+ "(SELECT member FROM xx_store_member WHERE id = ? ))");
			list.add(WebUtils.getCurrentCompanyInfoId());
			list.add(WebUtils.getCurrentStoreMemberId());
		} else {

		}
        sql.append(" order by ioos.create_date desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }

    public List<Map<String, Object>> findIOItemById(Long id) {
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select ioosi.memo, ioosi.number, ioosi.box, ioosi.branch, ioosi.scattered_branch, p.id, p.name, p.vonder_code, p.description, "
                + "     p.unit, pc.name product_category_name, p.per_box, p.per_branch, p.branch_per_box"
                + " from xx_into_or_out_storage_item ioosi "
                + " join xx_into_or_out_storage ioos on ioos.id = ioosi.into_or_out_storage "
                + " left join xx_product p on p.id = ioosi.product "
                + " left join xx_product_category pc on pc.id = p.product_category "
                + " where 1=1 ");
        if (!StringUtils.isEmpty(id)) {
            sql.append(" and ioosi.into_or_out_storage = ? ");
            list.add(id);
        }
        sql.append(" order by ioosi.id asc ");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }

    public Page<Map<String, Object>> findProPutInPage(List<Object> param, Pageable pageable) {
        Object sn = param.get(0);
        Object type = param.get(1);
        Integer[] status = null;
        if (param.get(2) != null) {
            status = (Integer[]) param.get(2);
        }
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select ioos.*, ss.name s_name "
                + " from xx_into_or_out_storage ioos "
                + " left join xx_set_storage ss on ss.id = ioos.storage "
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and ioos.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!StringUtils.isEmpty(sn)) {
            sql.append(" and ioos.sn like ? ");
            list.add("%" + sn.toString() + "%");
        }
        if (type != null) {
            sql.append(" and ioos.type = ? ");
            list.add(type.toString());
        }
        if (status != null && status.length > 0) {
            String os = "";
            for (int i = 0; i < status.length; i++) {
                if (i == status.length - 1)
                    os += status[i];
                else
                    os += status[i] + ",";
            }
            sql.append(" and ioos.status in (" + os + ")");
        }
        //查找当前经销商
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        if (storeMember.getMemberType() == 1) {
			sql.append(" and ss.store in (SELECT s.id FROM xx_store s "
					+ "LEFT JOIN xx_store_member sm ON sm.store = s.id "
					+ "WHERE s.company_info_id = ? and s.type != 0 and sm.member = "
					+ "(SELECT member FROM xx_store_member WHERE id = ? ))");
			list.add(WebUtils.getCurrentCompanyInfoId());
			list.add(WebUtils.getCurrentStoreMemberId());
		} else {

		}
        sql.append(" order by ioos.create_date desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }

    public Page<Map<String, Object>> findProPutInSelectPage(List<Object> param, Pageable pageable) {
        Object sn = param.get(0);   // 发货单号 
        Object pName = param.get(1); // 产品名称
        Object vonderCode = param.get(2); // 产品编码
        CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
        Store store = shopStoreService.findStore();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select "
        		+ "si.id,"
        		+ " si.price,"
        		+ " si.shipped_quantity-IFNULL((SELECT SUM(number) FROM xx_into_or_out_storage_item WHERE shipping_item = si.id),0) quantity,"
        		+ " (si.price*quantity) money,"
        		+ " s.sn,"
        		+ " p.id p_id,"
        		+ " p.name,"
        		+ " p.vonder_code,"
        		+ "	p.per_box,"
        		+ " p.per_branch,"
        		+ " p.branch_per_box,"
        		+ " p.description, "
                + " p.unit,"
                + " pc.name product_category_name,"
                + " st.name store_name,"
                + " st.id store_id "
                + " from xx_shipping_item si "
                + " left join xx_shipping s on s.id = si.shipping "
                + " left join xx_order oo on oo.id = si.orders "
                + " left join xx_store st on st.id = oo.stores "
                + " left join xx_product p on si.product = p.id "
                + " left join xx_product_category pc on pc.id = p.product_category "
                + " where 1=1 and s.`status` in(3,4) and si.shipped_quantity > 0");
        if (companyInfoId != null) {
            sql.append(" and si.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!StringUtils.isEmpty(sn)) {
            sql.append(" and s.sn like ? ");
            list.add("%" + sn.toString() + "%");
        }
        // 筛选经销商自己的发货单
        if (store != null) {
            sql.append(" and st.id = ? ");
            sql.append(" and s.sbu in(SELECT sbu FROM xx_store_sbu WHERE store = ?)");
            list.add(store.getId());
            list.add(store.getId());
        }
        if (!StringUtils.isEmpty(pName)) {
            sql.append(" and p.name like ? ");
            list.add("%" + pName.toString() + "%");
        }
        if (!StringUtils.isEmpty(vonderCode)) {
            sql.append(" and p.vonder_code like ? ");
            list.add("%" + vonderCode.toString() + "%");
        }
        // TODO 过滤已经入库的发货项
        sql.append(" order by s.create_date desc ");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }

    public List<Map<String, Object>> findProInById(Long id, Store store) {
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select si.id, si.price, si.quantity, (si.price*si.quantity) money, ss.sn, ioosi.memo, ioosi.number, ioosi.box, ioosi.branch, p.id p_id,  "
                + "     p.name, p.vonder_code, p.description, p.unit, pc.name product_category_name, p.per_box, p.per_branch, p.branch_per_box "
                + " from xx_into_or_out_storage_item ioosi "
                + " join xx_into_or_out_storage ioos on ioos.id = ioosi.into_or_out_storage "
                + " left join xx_product p on p.id = ioosi.product "
                + " left join xx_product_category pc on pc.id = p.product_category "
                + " left join xx_shipping_item si on si.id = ioosi.shipping_item "
                + " left join xx_shipping ss on ss.id = si.shipping "
                + " where 1=1 ");
        if (!StringUtils.isEmpty(id)) {
            sql.append(" and ioosi.into_or_out_storage = ? ");
            list.add(id);
        }
        sql.append(" order by ioosi.id asc ");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }
    
    public BigDecimal findInventory(Long id){
    	 StringBuilder sql = new StringBuilder();
         List<Object> list = new ArrayList<Object>();
         CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
         sql.append("SELECT si.shipped_quantity - "
         		+ "IFNULL((SELECT SUM(number) FROM xx_into_or_out_storage_item "
         		+ "WHERE shipping_item = si.id ),0) FROM xx_shipping_item si WHERE 1=1");
         
         if (companyInfo != null) {
             sql.append(" and si.company_info_id = ?");
             list.add(companyInfo);
         }
         if(id != null){
        	 sql.append(" and si.id = ?");
        	 list.add(id);
         }
         Object[] objs = new Object[list.size()];
         for (int i = 0; i < list.size(); i++) {
             objs[i] = list.get(i);
         }
         return getNativeDao().findBigDecimal(sql.toString(), objs);
	}

}
