package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

@Repository("storageProductDao")
public class StorageProductDao extends DaoCenter {
    
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;

    public Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable) {
        Object stoName = param.get(0);
        Object proName = param.get(1);
        Object vonderCode = param.get(2);
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select sp.id, sp.inventory, p.name, p.vonder_code, p.description, p.unit, "
                + "     pc.name product_category_name, ss.name sto_name "
                + " from xx_storage_product sp  "
                + " left join xx_set_storage ss on ss.id = sp.storage "
                + " left join xx_product p on p.id = sp.product "
                + " left join xx_product_category pc on pc.id = p.product_category  "
                + " where 1=1 ");
        if (companyInfoId != null) {
            sql.append(" and ss.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(stoName)) {
            sql.append(" and ss.name like ? ");
            list.add("%" + stoName.toString() + "%");
        }
        if (!ConvertUtil.isEmpty(proName)) {
            sql.append(" and p.name like ? ");
            list.add("%" + proName.toString() + "%");
        }
        if (!ConvertUtil.isEmpty(vonderCode)) {
            sql.append(" and p.vonder_code like ? ");
            list.add("%" + vonderCode.toString() + "%");
        }
        // 经销商只能看自己的仓库
        if (storeMember.getMemberType() == 1) {
        	sql.append(" and ss.store in (SELECT s.id FROM xx_store s "
        			+ "LEFT JOIN xx_store_member sm ON sm.store = s.id "
        			+ "WHERE s.company_info_id = ? and s.type != 0 and sm.member = "
        			+ "(SELECT member FROM xx_store_member WHERE id = ? ))");
        	list.add(WebUtils.getCurrentCompanyInfoId());
        	list.add(WebUtils.getCurrentStoreMemberId());
        } else {
        	
        }
        sql.append(" order by sp.create_date desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }
    
    
    public Page<Map<String, Object>> findProduct(String name,String vonderCode,Pageable pageable){
    	 StoreMember storeMember = storeMemberBaseService.getCurrent();
         Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
         List<Object> list = new ArrayList<Object>();
         StringBuilder sql = new StringBuilder();
         sql.append("SELECT p.*,ss.name storage_name FROM xx_storage_product sp"
         		+ " LEFT JOIN xx_set_storage ss ON  sp.storage = ss.id"
         		+ " LEFT JOIN xx_product p ON p.id = sp.product WHERE 1=1");
         if (companyInfoId != null) {
             sql.append(" and ss.company_info_id = ?");
             list.add(companyInfoId);
         }
         if (!ConvertUtil.isEmpty(name)) {
             sql.append(" and p.name like ? ");
             list.add("%" + name + "%");
         }
         if (!ConvertUtil.isEmpty(vonderCode)) {
             sql.append(" and p.vonder_code like ? ");
             list.add("%" + vonderCode + "%");
         }
         // 经销商只能看自己的仓库
         if (storeMember.getMemberType() == 1) {
        	 sql.append(" and ss.store in (SELECT s.id FROM xx_store s "
        			 + "LEFT JOIN xx_store_member sm ON sm.store = s.id "
        			 + "WHERE s.company_info_id = ? and s.type != 0 and sm.member = "
        			 + "(SELECT member FROM xx_store_member WHERE id = ? )) ");
        	 list.add(WebUtils.getCurrentCompanyInfoId());
        	 list.add(WebUtils.getCurrentStoreMemberId());
         } else {
        	 
         }
         sql.append(" group by p.id ");
         Object[] objs = new Object[list.size()];
         for (int i = 0; i < list.size(); i++) {
             objs[i] = list.get(i);
         }
         return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }
    
    public List<Map<String, Object>> findProduct(Long storeId){
    	if(storeId == null){
    		return null;
    	}
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        List<Object> list = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT p.* FROM xx_storage_product sp"
        		+ " LEFT JOIN xx_set_storage ss ON  sp.storage = ss.id"
        		+ " LEFT JOIN xx_product p ON p.id = sp.product WHERE 1=1");
        sql.append("  and ss.store = ?");
        list.add(storeId);
        if (companyInfoId != null) {
            sql.append(" and ss.company_info_id = ?");
            list.add(companyInfoId);
        }
        sql.append(" group by p.id ");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }
}
