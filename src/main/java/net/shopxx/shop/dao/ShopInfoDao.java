package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.ShopImage;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopInfoService;

@Repository("shopInfoDao")
public class ShopInfoDao extends DaoCenter {
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;

	public Page<Map<String, Object>> findPage(String sn,
			String distributorName,String address,String authorizationCode,String shopStatus,
			String deviseStatus,String decorationStatus, Pageable pageable) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		sql.append("select s.*,a.full_name area_name "
				+ " from xx_shop_info s "
				+ " left join xx_area a on s.area = a.id"
				+ " left join xx_store st on st.id = s.store"
				+ " left join xx_sale_org so on so.id = st.sale_org"
				+ " where 1=1 ");
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and s.sn like ?");
			list.add("%" + sn + "%");
		}

		if (!ConvertUtil.isEmpty(distributorName)) {
			sql.append(" and s.distributor_name like ?");
			list.add("%" + distributorName + "%");
		}
		
		if (!ConvertUtil.isEmpty(address)) {
			sql.append(" and s.address = ?");
			list.add(address);
		}
		
		if (!ConvertUtil.isEmpty(authorizationCode)) {
			sql.append(" and s.authorization_code = ?");
			list.add(authorizationCode);
		}
		
		if (!ConvertUtil.isEmpty(shopStatus)) {
			sql.append(" and s.shop_status = ?");
			list.add(shopStatus);
		}
		
		if (!ConvertUtil.isEmpty(deviseStatus)) {
			sql.append(" and s.devise_status = ?");
			list.add(deviseStatus);
		}
		
		if (!ConvertUtil.isEmpty(decorationStatus)) {
			sql.append(" and s.decoration_status = ?");
			list.add(decorationStatus);
		}
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				if(!storeMemberBaseService.findSalesman(storeMember.getId()).equals("")){
					sql.append(" and (s.store in (" + storeAuth + ") or st.store_member in(?) ) ");
					list.add(storeMemberBaseService.findSalesman(storeMember.getId()));
				}else{
					sql.append(" and (s.store in (" + storeAuth + ") or st.store_member = ?) ");
					list.add(WebUtils.getCurrentStoreMemberId());
				}
			}
		}else {
			sql.append(" and (s.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
		}
		sql.append(" order by s.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
				objs,
				pageable);

		return page;
	}
	
	public List<Map<String, Object>> findListMap() {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();

		sql.append("select s.shop_status,s.devise_status,s.decoration_status "
				+ " from xx_shop_info s "
				+ " left join xx_store ss on ss.id = s.store where 1=1 ");
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				if(!storeMemberBaseService.findSalesman(storeMember.getId()).equals("")){
					sql.append(" and (s.store in (" + storeAuth + ") or ss.store_member in(?) ) ");
					list.add(storeMemberBaseService.findSalesman(storeMember.getId()));
				}else{
					sql.append(" and (s.store in (" + storeAuth + ") or ss.store_member = ?) ");
					list.add(WebUtils.getCurrentStoreMemberId());
				}
			}
		}else {
			sql.append(" and (s.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
		}
		
		sql.append(" order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		return getNativeDao().findListMap(sql.toString(),
				objs,
				0);
	}

	public Page<Map<String, Object>> findSelectPage(String sn,
	        String distributorName,String address,String authorizationCode,String[] shopStatus,Integer reimburse,String transitTime,String shopname, Pageable pageable) {
	    
	    List<Object> list = new ArrayList<Object>();
	    Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
	    StringBuilder sql = new StringBuilder();
	    
	    sql.append("select si.*,sd.reimburse as reimburse,sd.transit_time, a.full_name area_name,a.id area_id,a.tree_path area_tree_path,"
	    		+ " sm.name xujl_name, m.mobile xujl_mobile,so.name sale_org_name,s.dealer_name "
	            + " from xx_shop_info si "
	            + " left join xx_area a on si.area = a.id "
	            + " left join xx_store s on si.store = s.id "
	            + " left join xx_store_member sm on s.store_member = sm.id "
	            + " left join xx_member m on sm.member = m.id "
	            + " left join xx_sale_org so on so.id = si.sale_org"
	            + "	LEFT JOIN xx_shop_devise sd on sd.shop_info = si.id"
	            + " where 1=1 ");
	    if (companyInfoId != null) {
	        sql.append(" and si.company_info_id = ?");
	        list.add(companyInfoId);
	    }
	    if (!ConvertUtil.isEmpty(sn)) {
	        sql.append(" and si.sn like ?");
	        list.add("%" + sn + "%");
	    }
//	    if (!ConvertUtil.isEmpty(distributorName)) {
//	        sql.append(" and s.dealer_name like ? ");
//	        list.add("%" + distributorName + "%");
//	    }
        if (!ConvertUtil.isEmpty(distributorName)) {
            sql.append(" and si.store = ? ");
            list.add(distributorName);
        }


	    if (!ConvertUtil.isEmpty(address)) {
	        sql.append(" and si.address = ?");
	        list.add(address);
	    }
	    if (!ConvertUtil.isEmpty(authorizationCode)) {
	        sql.append(" and si.authorization_code = ?");
	        list.add(authorizationCode);
	    }

        if (!ConvertUtil.isEmpty(shopname)) {
            sql.append(" and si.shop_name like ? ");
            list.add("%" + shopname + "%");
        }


	    if (!ConvertUtil.isEmpty(shopStatus)&&shopStatus.length>0) {
	    	String a = "";
			for (int i = 0; i < shopStatus.length; i++) {
				if (i == shopStatus.length - 1)
					a += "'"+shopStatus[i].toString()+"'";
				else
					a += "'"+shopStatus[i].toString() + "',";
			}
	        sql.append(" and si.shop_status in ("+a+") ");
	    }
	    if (!ConvertUtil.isEmpty(reimburse)) {
	        sql.append(" and sd.reimburse = ?");
	        list.add(reimburse);
	    }
	    StoreMember storeMember = storeMemberBaseService.getCurrent();
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				if(!storeMemberBaseService.findSalesman(storeMember.getId()).equals("")){
					sql.append(" and (si.store in (" + storeAuth + ") or s.store_member in(?) ) ");
					list.add(storeMemberBaseService.findSalesman(storeMember.getId()));
				}else{
					sql.append(" and (si.store in (" + storeAuth + ") or s.store_member = ?) ");
					list.add(WebUtils.getCurrentStoreMemberId());
				}
			}
		}else {
			sql.append(" and (si.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or si.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
		}
		sql.append(" GROUP BY si.id");
	    sql.append(" order by si.create_date desc");
	    
	    Object[] objs = new Object[list.size()];
	    for (int i = 0; i < list.size(); i++) {
	        objs[i] = list.get(i);
	    }
	    Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(),
	            objs,
	            pageable);
	    return page;
	}

	public List<Map<String, Object>> findBusinessCategoryApply(ShopInfo shopInfo) {

		StringBuilder sql = new StringBuilder();
		sql.append(" select p1.id id,p1.name name,p2.id cid,p2.name cname from xx_business_category_shop_info c ");
		sql.append(" left join xx_product_category p1  on p1.id =c.product_big_type");
		sql.append(" left join xx_product_category p2 on p2.id = c.product_center_type ");
		sql.append(" where c.shop_inof = " + shopInfo.getId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findShopDesign(ShopInfo shopInfo) {

		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_shop_devise sd "
				+ "LEFT JOIN xx_shop_info si ON sd.shop_info = si.id WHERE"
				+ " sd.company_info_id = "+shopInfo.getCompanyInfoId());
		if(shopInfo!=null){
			sql.append(" AND si.id = "+shopInfo.getId());
		}
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}
	
	public List<Map<String, Object>> findShopAcceptance(ShopInfo shopInfo) {

		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_acceptance_reimburse ar "
				+ "LEFT JOIN xx_shop_info si ON ar.shop_info = si.id WHERE"
				+ " ar.company_info_id = "+shopInfo.getCompanyInfoId());
		if(shopInfo!=null){
			sql.append(" AND si.id = "+shopInfo.getId());
		}
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}
	
	
	public List<Map<String, Object>> findRestructuring(ShopInfo shopInfo) {

		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM xx_restructuring r "
				+ "LEFT JOIN xx_shop_info si ON r.shop_info = si.id WHERE"
				+ " r.company_info_id = "+shopInfo.getCompanyInfoId());
		if(shopInfo!=null){
			sql.append(" AND si.id = "+shopInfo.getId());
		}
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findShopInspection(ShopInfo shopInfo) {

		StringBuilder sql = new StringBuilder();
		sql.append(" select * from xx_shop_inspection sd ");
		sql.append(" where sd.shop_info = "
				+ shopInfo.getId()
				+ " and sd.company_info_id = "
				+ shopInfo.getCompanyInfoId());
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public List<Map<String, Object>> findListByStoreId(Long storeId) {

		StringBuilder sql = new StringBuilder();
		sql.append(" select s.*,a.full_name area_name from xx_shop_info s left join xx_area a on s.area=a.id where store="
				+ storeId);
		return this.getNativeDao().findListMap(sql.toString(), null, 0);
	}

	public Integer countShopInfo(Long id) {
		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		sql.append(" select count(*) ");
		sql.append(" from xx_store s ");
		sql.append(" left join xx_shop_info si on s.id = si.store");
		sql.append(" where 1=1 and si.store = " + id + " ");

		sql.append(" order by s.create_date desc");

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}

		Integer count = getNativeDao().findInt(sql.toString(), objs);

		return count;
	}

	public List<Map<String, Object>> findItemList(String sn,
			String distributorName, Long[] ids, Integer page, Integer size) {

		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();

		sql.append("select s.*,a.full_name area_name,csm.name check_store_member_name,st.out_trade_no,a.id area_Id,a.tree_path  area_tree_path "
				+ " from xx_shop_info s "
				+ " left join xx_area a on s.area=a.id "
				+ " left join xx_store st on st.id=s.store "
				+ " left join xx_store_member csm on csm.id = s.check_store_member"
				+ " where 1=1 ");
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}

		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and s.sn like ?");
			list.add("%" + sn + "%");
		}

		if (!ConvertUtil.isEmpty(distributorName)) {
			sql.append(" and s.distributor_name like ?");
			list.add("%" + distributorName + "%");
		}
		if (ids != null && ids.length > 0) {
			StringBuilder inIds = new StringBuilder();
			for (int i = 0; i < ids.length; i++) {
				inIds.append("?,");
				list.add(ids[i]);
			}
			inIds.deleteCharAt(inIds.length() - 1);
			sql.append(" and s.id in (" + inIds + ")");
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				if(!storeMemberBaseService.findSalesman(storeMember.getId()).equals("")){
					sql.append(" and (s.store in (" + storeAuth + ") or st.store_member in(?) ) ");
					list.add(storeMemberBaseService.findSalesman(storeMember.getId()));
				}else{
					sql.append(" and (s.store in (" + storeAuth + ") or st.store_member = ?) ");
					list.add(WebUtils.getCurrentStoreMemberId());
				}
			}
		}else {
			sql.append(" and (s.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
		}

		sql.append(" order by s.create_date desc");

		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}

		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		//LogUtils.info("门店条件导出查询:"+sql.toString());
		List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(),
				objs,
				0);

		return maps;
	}

	public Integer count(String sn, String distributorName, Pageable pageable,
			Integer page, Integer size) {

		List<Object> list = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		sql.append("select count(s.id) "
				+ " from xx_shop_info s "
				+ " left join xx_store st on s.store = st.id "
				+ " left join xx_store_member csm on csm.id = s.check_store_member"
				+ " left join xx_area a on s.area=a.id ");
		sql.append(" where 1=1");
		if (!ConvertUtil.isEmpty(sn)) {
			sql.append(" and s.sn like ?");
			list.add("%" + sn + "%");
		}
		if (!ConvertUtil.isEmpty(distributorName)) {
			sql.append(" and s.distributor_name like ?");
			list.add("%" + distributorName + "%");
		}
		if (companyInfoId != null) {
			sql.append(" and s.company_info_id = ?");
			list.add(companyInfoId);
		}
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				if(!storeMemberBaseService.findSalesman(storeMember.getId()).equals("")){
					sql.append(" and (s.store in (" + storeAuth + ") or st.store_member in(?) ) ");
					list.add(storeMemberBaseService.findSalesman(storeMember.getId()));
				}else{
					sql.append(" and (s.store in (" + storeAuth + ") or st.store_member = ?) ");
					list.add(WebUtils.getCurrentStoreMemberId());
				}
			}
		}else {
			sql.append(" and (s.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or s.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
		}

		sql.append(" order by s.create_date desc");

		if (page != null && size != null) {
			sql.append(" limit " + (size * (page - 1)) + "," + size);
		}
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		Integer count = getNativeDao().findInt(sql.toString(), objs);
		return count;
	}
	
	public void updetaShop(ShopInfo shopInfo){
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		List<Map<String, Object>>  c = getNativeDao().findListMap("SELECT * FROM ticket_store WHERE company_info_id = ? AND sn = ? ", new Object[] { companyInfoId,shopInfo.getSn()}, 0);
		if(c==null){
			ExceptionUtil.throwServiceException("没有找到小程序对应的门店信息！");
		}
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE ticket_store SET latitude = ?,longitude = ? WHERE sn = ? AND company_info_id = ? ");
		getNativeDao().update(sql.toString(), new Object[]{shopInfo.getLatitude(),shopInfo.getLongitude(),shopInfo.getSn(),companyInfoId});
	}
	
	public void updetaShopImages(ShopInfo shopInfo){
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		ShopInfo si = shopInfoService.find(shopInfo.getId());
//		si.getShopImages().clear();
//		si.getShopImages().addAll(shopInfo.getShopImages());
		getNativeDao().delete("DELETE FROM shop_image WHERE shop_info = ?", new Object[] {si.getId()});
		for(ShopImage s : shopInfo.getShopImages()){
			getNativeDao().insert("INSERT INTO shop_image(shop_info,orders,source,thumbnail) VALUES(?,?,?,?)", new Object[] {si.getId(),s.getOrder(),s.getSource(),s.getThumbnail()});
		}
		String shopId = null;
		List<Map<String, Object>>  c = getNativeDao().findListMap("SELECT * FROM ticket_store WHERE company_info_id = ? AND sn = ? ", new Object[] { companyInfoId,si.getSn()}, 0);
		if(c==null||c.size()<0){
			ExceptionUtil.throwServiceException("没有找到小程序对应的门店信息！");
		}else if(c.size()>1){
			ExceptionUtil.throwServiceException("检测到多个门店信息请联系管理员！");
		}else{
			for(Map<String,Object> shop : c){
				if(shop.get("id")!=null){
					shopId = shop.get("id").toString();
				}
			}
			if(shopId!=null){
				getNativeDao().delete("DELETE FROM ticket_store_image WHERE ticket_store = ?", new Object[] {shopId});
			}else{
				ExceptionUtil.throwServiceException("没有找到图片对应门店信息！");
			}
			if(shopInfo.getShopImages().size()>0&&shopId!=null){
				for(ShopImage image : shopInfo.getShopImages()){
					getNativeDao().insert("INSERT INTO ticket_store_image(ticket_store,source_image,orders) VALUES(?,?,?)",new Object[] {shopId,image.getSource(),image.getOrder()});
				}
			}
		}
	}
	
	public List<Map<String, Object>> findDevise(String id,String reimburses){
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM xx_shop_devise "
				+ " WHERE company_info_id = ? "
				+ " AND shop_info = ? "
				+ " AND bills_status = 1 "
				+ " AND reimburse = ? "
				+ " ORDER BY transit_time DESC");
		
		return getNativeDao().findListMap(sql.toString(), new Object[]{companyInfoId,id,reimburses}, 0);
	}

	public List<Map<String, Object>> findShopList(String sn, String distributorName, Long[] ids, Integer page, Integer size)
	  {
	    List<Object> list = new ArrayList<Object>();
	    Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
	    StringBuilder sql = new StringBuilder();

	    sql.append("select s.*,a.full_name area_name from xx_shop_info s  left join xx_area a on s.area = a.id left join xx_store st on st.id = s.store left join xx_sale_org so on so.id = st.sale_org where 1=1 ");

	    if (companyInfoId != null) {
	      sql.append(" and s.company_info_id = ?");
	      list.add(companyInfoId);
	    }

	    if (!ConvertUtil.isEmpty(sn)) {
	      sql.append(" and s.sn like ?");
	      list.add("%" + sn + "%");
	    }

	    if (!ConvertUtil.isEmpty(distributorName)) {
	      sql.append(" and s.distributor_name like ?");
	      list.add("%" + distributorName + "%");
	    }
	    if ((ids != null) && (ids.length > 0)) {
	      StringBuilder inIds = new StringBuilder();
	      for (int i = 0; i < ids.length; i++) {
	        inIds.append("?,");
	        list.add(ids[i]);
	      }
	      inIds.deleteCharAt(inIds.length() - 1);
	      sql.append(" and s.id in (" + inIds + ")");
	    }
	    sql.append(" order by s.create_date desc");

	    Object[] objs = new Object[list.size()];
	    for (int i = 0; i < list.size(); i++) {
	      objs[i] = list.get(i);
	    }
	    return getNativeDao().findListMap(sql.toString(), objs, 0);
	  }

	  public List<Map<String, Object>> findListBy(Long id)
	  {
	    List<Object> list = new ArrayList<Object>();
	    Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
	    StringBuilder sql = new StringBuilder();

	    sql.append("SELECT si.*,ar.shop_authorization_codes from xx_shop_info si LEFT JOIN xx_acceptance_reimburse ar ON ar.shop_info = si.id where 1=1");

	    if (companyInfoId != null) {
	      sql.append(" and si.company_info_id = ?");
	      list.add(companyInfoId);
	    }
	    if (id != null) {
	      sql.append(" and si.id = ?");
	      list.add(id);
	    }
	    sql.append(" GROUP BY si.id");
	    Object[] objs = new Object[list.size()];
	    for (int i = 0; i < list.size(); i++) {
	      objs[i] = list.get(i);
	    }
	    return getNativeDao().findListMap(sql.toString(), objs, 0);
	  }

	  public Map<String, Object> findById (Long shopInfoId){
		  List<Object> list = new ArrayList<Object>();
		  StringBuilder sql = new StringBuilder();

		  sql.append("select si.*,sd.reimburse as reimburse,sd.transit_time, a.full_name area_name,a.id area_id,a.tree_path area_tree_path,"
				  + " sm.name xujl_name, m.mobile xujl_mobile,so.name sale_org_name,sm.name areaManagerName,s.name dealerName "
				  + " from xx_shop_info si "
				  + " left join xx_area a on si.area = a.id "
				  + " left join xx_store s on si.store = s.id "
				  + " left join xx_store_member sm on s.store_member = sm.id "
				  + " left join xx_member m on sm.member = m.id "
				  + " left join xx_sale_org so on so.id = si.sale_org"
				  + "	LEFT JOIN xx_shop_devise sd on sd.shop_info = si.id"
				  + " where 1=1 ");

		  if (shopInfoId == null){
		  	ExceptionUtil.throwDaoException("shopInfoId不能为空");
		  }
		  sql.append(" and si.id = ? ");
		  list.add(shopInfoId);

		  sql.append(" GROUP BY si.id ");
		  return getNativeDao().findSingleMap(sql.toString(),list.toArray());
	  }

    public List<Map<String, Object>> findStoreByShopInfo(Long storeId) {
        if(ConvertUtil.isEmpty(storeId)){
            return new ArrayList<Map<String, Object>>();
        }
        List<Object> list = new ArrayList<Object>();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();

        sql.append("select s.*,a.full_name from xx_shop_info s "
                + " left join xx_store ss on ss.id = s.store"
                + " left join xx_area a on a.id = s.area where 1=1 ");
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" and s.company_info_id = ?");
            list.add(companyInfoId);
        }
        sql.append(" AND ss.id = ?");
        list.add(storeId);
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        if (storeMember.getMemberType() == 1 || (storeMember.getIsSalesman()&&!storeMember.getIsActiveAdministrator())) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                String salesman = storeMemberBaseService.findSalesman(storeMember.getId());
                if (!salesman.equals("")) {
                    sql.append(" and (s.store in (" + storeAuth + ") or ss.store_member in("+salesman+") ) ");
                } else {
                    sql.append(" and (s.store in (" + storeAuth + ") or ss.store_member = ?) ");
                    list.add(WebUtils.getCurrentStoreMemberId());
                }
            }
        }

        sql.append(" GROUP BY s.id order by s.create_date desc");
        System.out.println(sql.toString());
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }

        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }
}
