package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.util.DateUtil;
import net.shopxx.shop.entity.ShopDevise;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;

@Repository("shopDeviseDao")
public class ShopDeviseDao extends DaoCenter {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
    public Page<Map<String, Object>> findPage(ShopDevise shopDevise, List<Object> param, Pageable pageable) {
        Object sn = shopDevise.getSn();                                                             //单号
        Object shopAttribute = shopDevise.getShopAttribute();                                       //是否二次设计
        Integer[] status = param.get(0) instanceof Integer[]?(Integer[]) param.get(0) : null;       //状态
        String[] attributes = param.get(1) instanceof String[]?(String[]) param.get(1) : null;      // 装修属性
        Integer[] reimburses = param.get(2) instanceof Integer[]? (Integer[]) param.get(2) : null;  // 是否报销
        Long designerId = param.get(3) instanceof Long ? (Long) param.get(3):null;                  //设计师
        Object shopInfoId = param.get(4);                                                           //门店
        Object storeId = param.get(5);                                                              //客户
        Object saleOrgId = param.get(6);                                                            //机构
        Object storeMemberId = param.get(7);                                                        //区域经理
        String dfirstTime = param.get(8) instanceof String? (String) param.get(8) :null;            //提交设计师开始时间
        String dlastTime = param.get(9) instanceof String? (String) param.get(9) :null;             //提交设计师结束时间
        String pfirstTime = param.get(10) instanceof String? (String) param.get(10) :null;          //平面图完成开始时间
        String plastTime = param.get(11) instanceof String? (String) param.get(11) :null;           //平面图完成结束时间
        String wfirstTime = param.get(12) instanceof String? (String) param.get(12) :null;          //施工图完成开始时间
        String wlastTime = param.get(13) instanceof String? (String) param.get(13) :null;           //施工图完成结束时间

        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select sd.*, si.authorization_code si_authorization_code, si.address si_address, si.distributor_name si_distributor_name,ar.decoration_state,last_operator.name last_operator_name,si.administrative_rank,si.open_date,si.join_date, "
                + " s.grant_code s_grant_code,s.fixed_number,s.name s_name,s.head_phone,s.region,s.active_date,"
                + " sm.name sm_xujl_name, m.mobile m_xujl_mobile, so.name so_name,si.shop_name si_shop_name,"
                + " s.dealer_name as s_dealer_name,a.full_name area_name,beginer.name beginer,so.region region_code,"
                + " stylist.`name` stylist_name "
                + " from xx_shop_devise sd "
                + " left join xx_shop_info si on sd.shop_info = si.id "
                + " left join xx_store s on si.store = s.id "
                + " left join xx_store_member sm on s.store_member = sm.id "
                + " left join xx_member m on sm.member = m.id "
                + " left join xx_sale_org so on s.sale_org = so.id "
                + " left join xx_area a on a.id = sd.devise_area "
                + " left join xx_acceptance_reimburse ar on ar.shop_devise = sd.id "
                + " left join xx_store_member beginer on beginer.username = s.b_creater "
                + " left join xx_store_member last_operator on last_operator.id = sd.last_operator "
                + " left join xx_store_member stylist on stylist.id = sd.designer "
                + " where 1=1 ");
        if (!ConvertUtil.isEmpty(companyInfoId)) {
            sql.append(" and sd.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(sn)) {
            sql.append(" and sd.sn like ? ");
            list.add("%" + sn.toString() + "%");
        }
        if (!ConvertUtil.isEmpty(shopAttribute)){
            sql.append(" and sd.shop_attribute = ? ");
            list.add(shopAttribute);
        }
        if (status != null && status.length > 0) {
            String os = "";
            for (int i = 0; i < status.length; i++) {
                if (i == status.length - 1)
                    os += status[i];
                else
                    os += status[i] + ",";
            }
            sql.append(" and sd.bills_status in (" + os + ")");
        }
        if (attributes != null && attributes.length > 0) {
            StringBuilder str = new StringBuilder();
            for (int i = 0; i < attributes.length; i++) {
                str.append("?,");
                list.add(attributes[i]);
            }
            str.deleteCharAt(str.length() - 1);
            sql.append(" and sd.shop_renovation_attribute in (" + str + ")");
        }
        if (reimburses != null && reimburses.length > 0) {
            String os = "";
            for (int i = 0; i < reimburses.length; i++) {
                if (i == reimburses.length - 1)
                    os += reimburses[i];
                else
                    os += reimburses[i] + ",";
            }
            sql.append(" and sd.reimburse in (" + os + ")");
        }
        if (!ConvertUtil.isEmpty(designerId)){
            sql.append(" and sd.designer = ? ");
            list.add(designerId);
        }
        if (!ConvertUtil.isEmpty(shopInfoId)){
            sql.append(" and si.id = ? ");
            list.add(shopInfoId);
        }
        if (!ConvertUtil.isEmpty(storeId)){
            sql.append(" and s.id = ? ");
            list.add(storeId);
        }
        if (!ConvertUtil.isEmpty(saleOrgId)){
            sql.append(" and so.id = ? ");
            list.add(saleOrgId);
        }
        if (!ConvertUtil.isEmpty(storeMemberId)){
            sql.append(" and sm.id = ? ");
            list.add(storeMemberId);
        }
        if (!ConvertUtil.isEmpty(dfirstTime)) {
            sql.append(" and sd.designer_submit_time >= ?");
            list.add(DateUtil.convert(dfirstTime + " 00:00:00"));
        }
        if (!ConvertUtil.isEmpty(dlastTime)) {
            sql.append(" and sd.designer_submit_time < ?");
            list.add(DateUtils.addDays(DateUtil.convert(dlastTime + " 00:00:00"),1));
        }
        if (!ConvertUtil.isEmpty(pfirstTime)) {
            sql.append(" and sd.plans_finished_time >= ?");
            list.add(DateUtil.convert(pfirstTime + " 00:00:00"));
        }
        if (!ConvertUtil.isEmpty(plastTime)) {
            sql.append(" and sd.plans_finished_time < ?");
            list.add(DateUtils.addDays(DateUtil.convert(plastTime + " 00:00:00"),1));
        }
        if (!ConvertUtil.isEmpty(wfirstTime)) {
            sql.append(" and sd.working_drawing_finished_time >= ?");
            list.add(DateUtil.convert(wfirstTime + " 00:00:00"));
        }
        if (!ConvertUtil.isEmpty(wlastTime)) {
            sql.append(" and sd.working_drawing_finished_time < ?");
            list.add(DateUtils.addDays(DateUtil.convert(wlastTime + " 00:00:00"),1));
        }

        StoreMember storeMember = storeMemberBaseService.getCurrent();
        if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
            String storeAuth = storeMemberBaseService.storeAuth();
            if (storeAuth != null) {
                sql.append(" and (si.store in (" + storeAuth + ") or s.store_member = ?) ");
                list.add(WebUtils.getCurrentStoreMemberId());
            }
        }else {
            sql.append(" and (sd.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = ?) "
                    + " or sd.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
                    + " where a.tree_path like concat('%,', b.id, ',%') "
                    + " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
                    + " where smo.sale_org = s.id and smo.store_member = ?)))");
            list.add(WebUtils.getCurrentStoreMemberId());
            list.add(WebUtils.getCurrentStoreMemberId());
        }

        sql.append(" GROUP BY sd.id ");
        sql.append(" order by sd.id desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        Page<Map<String, Object>> page= getNativeDao().findPageMap(sql.toString(), objs, pageable);
        StringBuilder totalSql = new StringBuilder("select count(1) from ( ");
        totalSql.append(sql).append(") a");
        long total = getNativeDao().findInt(totalSql.toString(), objs);
        page.setTotal(total);
        return page;
    }

//    public Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable) {
//        Object sn = param.get(0);
//        Integer[] status = null;
//        if (param.get(1) != null) {
//            status = (Integer[]) param.get(1);
//        }
//        String[] attributes = null;  // 装修属性
//        if (param.get(2) != null) {
//            attributes = (String[]) param.get(2);
//        }
//        Integer[] reimburses = null;  // 是否报销
//        if (param.get(3) != null) {
//            reimburses = (Integer[]) param.get(3);
//        }
//        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
//        StringBuilder sql = new StringBuilder();
//        List<Object> list = new ArrayList<Object>();
//        sql.append("select sd.*, si.authorization_code si_authorization_code, si.address si_address, si.distributor_name si_distributor_name, "
//                + " s.grant_code s_grant_code, sm.name sm_xujl_name, m.mobile m_xujl_mobile, so.name so_name,si.shop_name si_shop_name,s.dealer_name as s_dealer_name"
//                + " from xx_shop_devise sd "
//                + " left join xx_shop_info si on sd.shop_info = si.id "
//                + " left join xx_store s on si.store = s.id "
//                + " left join xx_store_member sm on s.store_member = sm.id "
//                + " left join xx_member m on sm.member = m.id "
//                + " left join xx_sale_org so on s.sale_org = so.id "
//                + " where 1=1 ");
//        if (companyInfoId != null) {
//            sql.append(" and sd.company_info_id = ?");
//            list.add(companyInfoId);
//        }
//        if (!ConvertUtil.isEmpty(sn)) {
//            sql.append(" and sd.sn like ? ");
//            list.add("%" + sn.toString() + "%");
//        }
//        if (status != null && status.length > 0) {
//            String os = "";
//            for (int i = 0; i < status.length; i++) {
//                if (i == status.length - 1)
//                    os += status[i];
//                else
//                    os += status[i] + ",";
//            }
//            sql.append(" and sd.bills_status in (" + os + ")");
//        }
//        if (attributes != null && attributes.length > 0) {
//            StringBuilder str = new StringBuilder();
//            for (int i = 0; i < attributes.length; i++) {
//                str.append("?,");
//                list.add(attributes[i]);
//            }
//            str.deleteCharAt(str.length() - 1);
//            sql.append(" and sd.shop_renovation_attribute in (" + str + ")");
//        }
//        if (reimburses != null && reimburses.length > 0) {
//            String os = "";
//            for (int i = 0; i < reimburses.length; i++) {
//                if (i == reimburses.length - 1)
//                    os += reimburses[i];
//                else
//                    os += reimburses[i] + ",";
//            }
//            sql.append(" and sd.reimburse in (" + os + ")");
//        }
//        StoreMember storeMember = storeMemberBaseService.getCurrent();
//        if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
//			String storeAuth = storeMemberBaseService.storeAuth();
//			if (storeAuth != null) {
//				sql.append(" and (si.store in (" + storeAuth + ") or s.store_member = ?) ");
//				list.add(WebUtils.getCurrentStoreMemberId());
//			}
//		}else {
//			sql.append(" and (sd.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
//					+ " where smo.sale_org = s.id and smo.store_member = ?) "
//					+ " or sd.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
//					+ " where a.tree_path like concat('%,', b.id, ',%') "
//					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
//					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
//			list.add(WebUtils.getCurrentStoreMemberId());
//			list.add(WebUtils.getCurrentStoreMemberId());
//		}
//
//        sql.append(" order by sd.create_date desc");
//        Object[] objs = new Object[list.size()];
//        for (int i = 0; i < list.size(); i++) {
//          objs[i] = list.get(i);
//        }
//        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
//    }
    
    public Page<Map<String, Object>> findSelectPage(List<Object> param, Pageable pageable) {
        Object sn = param.get(0);
        Object dealer = param.get(1);
        Integer[] status = null;
        if (param.get(2) != null) {
            status = (Integer[]) param.get(2);
        }
        
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select sd.*,si.shop_name"
                + " from xx_shop_devise sd "
                + " left join xx_shop_info si on sd.shop_info = si.id "
                + " left join xx_store s on	s.id = si.store "
                + " where 1=1 AND sd.bills_status = 2 ");
        if (!ConvertUtil.isEmpty(sn)) {
            sql.append(" and sd.sn like ? ");
            list.add("%" + sn.toString() + "%");
        }
        if (!ConvertUtil.isEmpty(dealer)) {
            sql.append(" and sd.dealer like ? ");
            list.add("%" + dealer.toString() + "%");
        }
        if (status != null && status.length > 0) {
            String os = "";
            for (int i = 0; i < status.length; i++) {
                if (i == status.length - 1)
                    os += status[i];
                else
                    os += status[i] + ",";
            }
            sql.append(" and sd.bills_status in (" + os + ")");
        }
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql.append(" and (si.store in (" + storeAuth + ") or s.store_member = ?) ");
				list.add(WebUtils.getCurrentStoreMemberId());
			}
		}else {
			sql.append(" and (sd.sale_org in (select s.id from xx_store_member_sale_org smo, xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?) "
					+ " or sd.sale_org in (select  a.id from xx_sale_org a,xx_sale_org b "
					+ " where a.tree_path like concat('%,', b.id, ',%') "
					+ " and b.id in (select s.id from xx_store_member_sale_org smo,xx_sale_org s "
					+ " where smo.sale_org = s.id and smo.store_member = ?)))");
			list.add(WebUtils.getCurrentStoreMemberId());
			list.add(WebUtils.getCurrentStoreMemberId());
		}
        
        sql.append(" order by sd.create_date desc");
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }

    public List<Map<String, Object>> findShopDeviseAttach(Long id, Integer type) {
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append("select * "
                + " from xx_shop_devise_attach "
                + " where company_info_id = ? and shop_devise = ? and type = ? ");
        list.add(WebUtils.getCurrentCompanyInfoId());
        list.add(id);
        list.add(type);

        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(), objs, 0);
    }

}
