package net.shopxx.shop.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Repository("shopReplenishmentDao")
public class ShopReplenishmentDao extends DaoCenter{
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	
	public Page<Map<String, Object>> findPage(String vonderCode,String productName, Pageable pageable) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
        StringBuilder sql = new StringBuilder();
        List<Object> list = new ArrayList<Object>();
        sql.append(" SELECT sr.*,s.name store_name,p.* FROM xx_shop_replenishment sr "
        		+ "LEFT JOIN xx_product p ON p.id = sr.product "
        		+ "LEFT JOIN xx_store s ON sr.store = s.id WHERE 1=1 ");
        //安全库存
        sql.append(" AND sr.type = 0");
        if (companyInfoId != null) {
            sql.append(" AND sr.company_info_id = ?");
            list.add(companyInfoId);
        }
        if (!ConvertUtil.isEmpty(productName)) {
            sql.append(" AND p.name like ? ");
            list.add("%" + productName + "%");
        }
        if (!ConvertUtil.isEmpty(vonderCode)) {
            sql.append(" AND p.vonder_code like ? ");
            list.add("%" + vonderCode + "%");
        }
        if (storeMember.getMemberType() == 1) {
        	sql.append(" and sr.store in (SELECT s.id FROM xx_store s "
        			+ "LEFT JOIN xx_store_member sm ON sm.store = s.id "
        			+ "WHERE s.company_info_id = ? and s.type != 0 and sm.member = "
        			+ "(SELECT member FROM xx_store_member WHERE id = ? ))");
        	list.add(WebUtils.getCurrentCompanyInfoId());
        	list.add(WebUtils.getCurrentStoreMemberId());
        } else {
        	
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        sql.append(" ORDER BY sr.create_date desc");
        return getNativeDao().findPageMap(sql.toString(), objs, pageable);
    }
}
