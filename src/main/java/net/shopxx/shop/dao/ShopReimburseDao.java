package net.shopxx.shop.dao;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.service.StoreMemberBaseService;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("shopReimburseDao")
public class ShopReimburseDao extends DaoCenter{
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	
	public Page<Map<String, Object>> findPage(List<Object> param,Long shopInfoId, Pageable pageable) {
		String authorizationCode = (String) param.get(0);
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		List<Object> list = new ArrayList<Object>();
		sql.append(
				"SELECT *, concat(a.full_name,si.address) shop_area_name,s.region region1 FROM xx_shop_reimburse sre "
				+ "LEFT JOIN xx_acceptance_reimburse ar ON ar.id = sre.acceptance_reimburse "
				+ "LEFT JOIN xx_shop_info si ON si.id = sre.shop_info "
				+ "LEFT JOIN xx_area a ON a.id = si.area "
				+ "LEFT JOIN xx_store s ON s.id = si.store WHERE 1=1 ");
		if (companyInfoId != null) {
			sql.append(" AND sre.company_info_id = ?");
			list.add(companyInfoId);
		}
		if(shopInfoId != null){
			sql.append(" AND ar.shop_info = ? ");
			list.add(shopInfoId);
		}
		if(authorizationCode != null){
			sql.append(" AND si.authorization_code = ? ");
			list.add(authorizationCode);
		}
		sql.append(" order by sre.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}

	public Integer count(List<Object> param) {
		String authorizationCode = (String) param.get(0);
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		List<Object> list = new ArrayList<Object>();
		sql.append(
				"SELECT count(sre.id) FROM xx_shop_reimburse sre "
				+ "LEFT JOIN xx_acceptance_reimburse ar ON ar.id = sre.acceptance_reimburse "
				+ "LEFT JOIN xx_shop_info si ON si.id = sre.shop_info "
				+ "LEFT JOIN xx_area a ON a.id = si.area "
				+ "LEFT JOIN xx_store s ON s.id = si.store WHERE 1=1 ");
		if (companyInfoId != null) {
			sql.append(" AND sre.company_info_id = ?");
			list.add(companyInfoId);
		}
		if(authorizationCode != null){
			sql.append(" AND si.authorization_code = ? ");
			list.add(authorizationCode);
		}
		sql.append(" order by sre.create_date desc");
//		if (page != null && size != null) {
//            sql.append(" limit " + (size * (page - 1)) + "," + size);
//        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        Integer count = getNativeDao().findInt(sql.toString(), objs);
        return count;
	}

	
	public List<Map<String, Object>> findItemList(List<Object> param, Integer page, Integer size) {
		String authorizationCode = (String) param.get(0);
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		List<Object> list = new ArrayList<Object>();
		sql.append(
				"SELECT *, concat(a.full_name,si.address) shop_area_name FROM xx_shop_reimburse sre "
				+ "LEFT JOIN xx_acceptance_reimburse ar ON ar.id = sre.acceptance_reimburse "
				+ "LEFT JOIN xx_shop_info si ON si.id = sre.shop_info "
				+ "LEFT JOIN xx_area a ON a.id = si.area "
				+ "LEFT JOIN xx_store s ON s.id = si.store WHERE 1=1 ");
		if (companyInfoId != null) {
			sql.append(" AND sre.company_info_id = ?");
			list.add(companyInfoId);
		}
		if(authorizationCode != null){
			sql.append(" AND si.authorization_code = ? ");
			list.add(authorizationCode);
		}
		sql.append(" order by sre.create_date desc");
		
		if (page != null && size != null) {
            sql.append(" limit " + (size * (page - 1)) + "," + size);
        }
        Object[] objs = new Object[list.size()];
        for (int i = 0; i < list.size(); i++) {
            objs[i] = list.get(i);
        }
        return getNativeDao().findListMap(sql.toString(),objs,0);
	}
	
}
