package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PcUserRoleBaseService;
import net.shopxx.member.service.StoreMemberBaseService;

@Repository("customerInfoDao")
public class CustomerInfoDao extends DaoCenter {

	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;

	public Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable) {
		Object name = param.get(0);
		Object phone = param.get(1);
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		StringBuilder sqL = new StringBuilder();
		List<Object> list = new ArrayList<Object>();
		sql.append(
				"select oo.id, ts.name shop_name, sm.username, sm.name, sm.member_type, sm.address, sm.create_date, m.mobile "
						+ " from ticket_order oo " + " left join ticket_store ts on ts.id = oo.ticket_store "
						+ " join xx_store_member sm on sm.id = oo.store_member "
						+ " left join xx_member m on m.id = sm.member "
						+ " left join xx_shop_info si on si.id = ts.shop_info " + " where 1=1 ");
		sqL.append("SELECT sm.`name` store_member_name,tc.`status`,tc.`name`,tc.mobile,a.full_name,tc.address,"
				+ "tc.create_date,si.shop_name FROM ticket_customer tc" + " LEFT JOIN xx_area a ON a.id = tc.area"
				+ " LEFT JOIN xx_store_member sm ON sm.id = tc.store_member"
				+ " LEFT JOIN ticket_store_member tsm ON tsm.store_member = sm.id"
				+ " LEFT JOIN ticket_store ts ON ts.id = tsm.ticket_store"
				+ " LEFT JOIN xx_shop_info si ON si.id = ts.shop_info where 1=1 and si.id is not null ");
		if (companyInfoId != null) {
			sql.append(" and oo.company_info_id = ?");
			sqL.append(" and tc.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (!ConvertUtil.isEmpty(name)) {
			sql.append(" and sm.name like ? ");
			sqL.append(" and tc.name like ? ");
			list.add("%" + name.toString() + "%");
		}
		if (!ConvertUtil.isEmpty(phone)) {
			sql.append(" and m.mobile like ? ");
			sqL.append(" and tc.mobile like ? ");
			list.add("%" + phone.toString() + "%");
		}
		StoreMember storeMember = storeMemberBaseService.find(WebUtils.getCurrentStoreMemberId());
		if (storeMember.getMemberType() == 1) {
			sqL.append(
					" and si.id in (SELECT shop_info FROM xx_store_member_shop_info smsi WHERE smsi.store_member = ?)");
			list.add(WebUtils.getCurrentStoreMemberId());
			String role = pcUserRoleBaseService.findRole(WebUtils.getCurrentStoreMemberId());
			if(role.indexOf("导购员")>=0){
				sqL.append(" and tc.store_member in(select store_member from ticket_store_member where login_store_member = ?)");
				list.add(WebUtils.getCurrentStoreMemberId());
			}
		} else {

		}
		System.out.println(sqL.toString());

		// sql.append(" group by sm.name, ts.name ");
		// sql.append(" order by oo.create_date desc");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sqL.toString(), objs, pageable);
	}

}
