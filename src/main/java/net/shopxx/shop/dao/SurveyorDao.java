package net.shopxx.shop.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.entity.Surveyor;

@Repository("surveyorDao")
public class SurveyorDao extends DaoCenter {
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	
	public Page<Map<String,Object>> findPage(Surveyor surveyor,Pageable pageable){
		List<Object> list = new ArrayList<Object>();
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT sr.*,s.`name` store_name,ct.team_name FROM xx_surveyor sr "
				+ " LEFT JOIN xx_store s ON s.id = sr.store "
				+ " LEFT JOIN xx_construction_team ct ON ct.id = sr.teams WHERE 1=1 ");
		if (companyInfoId != null) {
			sql.append(" AND sr.company_info_id = ?");
			list.add(companyInfoId);
		}
		if (surveyor.getName()!=null){
			sql.append(" AND sr.`name` LIKE ? ");
			list.add("%" + surveyor.getName() + "%");
		}
		if (surveyor.getPhone()!=null){
			sql.append(" AND sr.phone = ? ");
			list.add(surveyor.getPhone());
		}
		if (surveyor.getIsMeasure()!=null){
			sql.append(" AND sr.is_measure = ? ");
			list.add(surveyor.getIsMeasure());
		}
		if (surveyor.getIsInstall()!=null){
			sql.append(" AND sr.is_install = ? ");
			list.add(surveyor.getIsInstall());
		}
		if(surveyor.getTeam()!=null){
			if (surveyor.getTeam().getId()!=null){
				sql.append(" AND sr.teams = ? ");
				list.add(surveyor.getTeam().getId());
			}			
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		if (storeMember.getMemberType() == 1 || storeMember.getIsSalesman()) {
			String storeAuth = storeMemberBaseService.storeAuth();
			if (storeAuth != null) {
				sql.append(" AND (sr.store in (" + storeAuth + ") OR s.store_member = ?) ");
				list.add(WebUtils.getCurrentStoreMemberId());
			}
		}
		sql.append(" ORDER BY sr.create_date DESC ");
		Object[] objs = new Object[list.size()];
		for (int i = 0; i < list.size(); i++) {
			objs[i] = list.get(i);
		}
		return getNativeDao().findPageMap(sql.toString(), objs, pageable);
	}
	
	public List<Map<String, Object>> findSurveyor(Long id) {
		return getNativeDao().findListMap("SELECT * FROM xx_surveyor WHERE teams = ? AND company_info_id = ?", new Object[]{id,WebUtils.getCurrentCompanyInfoId()}, 0);
	}

}
