package net.shopxx.shop.service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.shop.entity.ShopReimburse;

import java.util.List;
import java.util.Map;

public interface ShopReimburseService extends BaseService<ShopReimburse>{
	
	public Page<Map<String, Object>> findPage(List<Object> param, Long shopInfoId, Pageable pageable);

	void saveShopReimburse(ShopReimburse shopReimburse, Long acceptanceReimburseId, Long shopInfoId);
	
	void updateShopReimburse(ShopReimburse shopReimburse);
	
	Integer count(List<Object> param);
	
	List<Map<String, Object>> findItemList(List<Object> param, Integer page, Integer size);

}
