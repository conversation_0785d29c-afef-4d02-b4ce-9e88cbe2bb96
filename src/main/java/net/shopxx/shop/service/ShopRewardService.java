package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.shop.entity.ShopReward;
import net.shopxx.wf.service.WfBillBaseService;

public interface ShopRewardService extends WfBillBaseService<ShopReward> {
	
	/**
	 * 根据订单id查找对应的附件信息
	 * */
	List<Map<String, Object>> findAdvertisAttach(Long id);
	
	void saveShopReward(Long storeId,ShopReward shopReward);
	
	void updateShopReward(Long storeId,ShopReward shopReward);

}
