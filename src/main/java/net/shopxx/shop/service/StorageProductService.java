package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.shop.entity.StorageProduct;

public interface StorageProductService extends BaseService<StorageProduct> {

    Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable);
    
    Page<Map<String, Object>> findProduct(String name,String vonderCode,Pageable pageable);
    
    List<Map<String, Object>> findProduct(Long storeId);

}
