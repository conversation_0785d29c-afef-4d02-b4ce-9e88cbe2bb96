package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.shop.entity.ShopDevise;

public interface ShopDeviseService extends ActWfBillService<ShopDevise> {

    public void saveShopDevise(ShopDevise shopDevise);

    public Page<Map<String, Object>> findPage(ShopDevise shopDevise,List<Object> param, Pageable pageable);
    
    public Page<Map<String, Object>> findSelectPage(List<Object> param, Pageable pageable);

    public List<Map<String, Object>> findShopDeviseAttach(Long id, Integer type);

    public void updateShopDevise(ShopDevise shopDevise);

	public void saveform(ShopDevise shopDevise, Integer[] regionalManagerOpinions, Integer[] floorDivisions, Integer type);
    
	/** 创建流程实例 */
	void createWf(Long id, String modelId, Long objTypeId);
	
	/** 作废*/
	void cancel(Long id);

}
