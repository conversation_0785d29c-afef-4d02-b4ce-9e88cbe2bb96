package net.shopxx.shop.service;

import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.shop.entity.ConstructionTeam;

public interface ConstructionTeamService extends BaseService<ConstructionTeam>{
	
	Page<Map<String,Object>> findPage(ConstructionTeam t,Pageable pageable);
	
	void saveConstructionTeam(ConstructionTeam t);
	
	void updateConstructionTeam(ConstructionTeam t);


}
