package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.product.entity.Product;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.entity.ShopProudct;

public interface ShopProductService extends BaseService<ShopProudct> {


    void operation(ShopInfo shop, Product product, Integer type);

    List<Map<String, Object>> findShopProduct(Long shopId);




    /**
     *条件查询门店上样各级数量
     * @param shopProductCategoryId 门店上样分类
     * @param productCategoryId	商品分类
     * @param shopId	shopInfoId
     * @param isMarketable 否是上样
     * @return
     */
    public Integer findListCount(Long shopProductCategoryId,Long productCategoryId, Long shopId,Boolean isMarketable);

    /**
     * PC端查询门店上样商品列表
     * @param shopProductCategoryId
     * @param productCategoryId
     * @param vonderCode
     * @param mod
     * @param name
     * @param shopId
     * @param isReferrer
     * @param isMarketable
     * @param isShow
     * @param pageable
     * @return
     */
    Page<Map<String, Object>> findPage(Long shopProductCategoryId, Long productCategoryId, String vonderCode, String mod, String name, Long shopId, Boolean isReferrer, Boolean isMarketable, Boolean isShow, Pageable pageable);


    /**
     * 移动端查询门店上样商品列表
     * @param shopProductCategoryId
     * @param productCategoryId
     * @param vonderCode
     * @param mod
     * @param name
     * @param shopId
     * @param isReferrer
     * @param isMarketable
     * @param isShow
     * @param pageable
     * @return
     */
    Page<Map<String, Object>> findAppShopProductPage(Long shopProductCategoryId, Long productCategoryId, String vonderCode, String mod, String name, Long shopId, Boolean isReferrer, Boolean isMarketable, Boolean isShow, Pageable pageable);
}
