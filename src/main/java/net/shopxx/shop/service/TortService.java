package net.shopxx.shop.service;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.shop.entity.Tort;

import java.util.List;
import java.util.Map;

public interface TortService extends ActWfBillService<Tort>{
	
	void saveTort(Tort tort);
	
	void updateTort(Tort tort);
	
	List<Map<String, Object>> findTortAttach(Long id, Integer type);

	List<Map<String, Object>> findComplaintAttach(Long id);

	List<Map<String,Object>> findNoticeAttach(Long id);

	Page<Map<String, Object>> findPage(Tort t, List<Object> param, Pageable pageable);

	void saveform(Tort tort, Integer type);

    /** 创建流程实例 */
    void createWf(Long id, String modelId, Long objTypeId);

    /**
     * 查询导出数量
     */
    Integer count(Tort t, List<Object> param);

    /**
     * 导出查询
     */
	List<Map<String, Object>> findList(Tort t, List<Object> param, Pageable pageable, Integer page, Integer size);
}
