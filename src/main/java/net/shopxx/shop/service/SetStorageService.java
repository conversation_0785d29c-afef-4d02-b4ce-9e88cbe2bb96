package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.shop.entity.SetStorage;

public interface SetStorageService extends BaseService<SetStorage> {

    Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable);

    /**
     * 获取当前经销商的所有仓库
     */
    List<Map<String, Object>> findOwnStorage();
    
    /**
     * 获取当前经销商的所有仓库
     */
    List<Map<String, Object>> findStorage(Long id);

}
