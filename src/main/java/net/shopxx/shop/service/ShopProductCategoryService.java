package net.shopxx.shop.service;

import net.shopxx.base.core.service.BaseService;
import net.shopxx.shop.entity.ShopProductCategory;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Service - 门店上样分类
 */
public interface ShopProductCategoryService extends
		BaseService<ShopProductCategory> {

	public List<Map<String, Object>> findList(String sn, String name,
                                              Boolean isEnabled);

	public List<Map<String, Object>> findChildren(Long parentId, String name);

	/**
	 * 查找下级商品分类
	 *
	 * @param shopProductCategory
	 * @return
	 */
	public List<ShopProductCategory> findChildren(ShopProductCategory shopProductCategory);

	/**
	 * 查找下级商品分类
	 *
	 * @param sn
	 * @param name
	 * @param isEnabled
	 * @param shopProductCategory
	 * @param count
	 * @return
	 */
	public List<ShopProductCategory> findChildren(String sn, String name,
                                              Boolean isEnabled, ShopProductCategory shopProductCategory, int count);

	/**
	 * 删除商品分类
	 *
	 * @param shopProductCategory
	 */
	public void deleteProductCategory(ShopProductCategory shopProductCategory);

	/**
	 * excel导入
	 *
	 * @param file
	 * @return
	 * @throws Exception
	 */
	public String importFromExcel(MultipartFile file, Integer type)
			throws Exception;



	void saveShopProductCategory(ShopProductCategory shopProductCategory, Long[] productCategoryId);

	void updateShopProductCategory(ShopProductCategory shopProductCategory, Long[] productCategoryId);
}