package net.shopxx.shop.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.shop.dao.SetStorageDao;
import net.shopxx.shop.entity.SetStorage;
import net.shopxx.shop.service.SetStorageService;

@Service("setStorageServiceImpl")
public class SetStorageServiceImpl extends BaseServiceImpl<SetStorage> implements SetStorageService {
    
    @Resource(name = "setStorageDao")
    private SetStorageDao setStorageDao;

    @Override
    public Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable) {
        return setStorageDao.findPage(param, pageable);
    }

    @Override
    public List<Map<String, Object>> findOwnStorage() {
        return setStorageDao.findOwnStorage();
    }

	@Override
	public List<Map<String, Object>> findStorage(Long id) {
		return setStorageDao.findStorage(id);
	}

}
