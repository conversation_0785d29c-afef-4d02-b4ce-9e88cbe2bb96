package net.shopxx.shop.service.impl;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.dao.ShopRewardDao;
import net.shopxx.shop.entity.AdvertisAttach;
import net.shopxx.shop.entity.ShopReward;
import net.shopxx.shop.service.ShopRewardService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

@Service("shopRewardServiceImpl")
public class ShopRewardServiceImpl extends WfBillBaseServiceImpl<ShopReward>
implements ShopRewardService{

	@Resource(name = "shopRewardDao")
	private ShopRewardDao shopRewardDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;

	@Override
	public List<Map<String, Object>> findAdvertisAttach(Long id) {
		return shopRewardDao.findShopAttach(id);
	}

	@Override
	public void saveShopReward(Long storeId, ShopReward shopReward) {
		String sn = SnUtil.generateSn();
		shopReward.setSn(sn);
		Store store = storeBaseService.find(storeId);
		shopReward.setStore(store);
		//广告合同附件
		List<AdvertisAttach> advertisAttachs = shopReward.getAdvertisAttachs();
		for (Iterator<AdvertisAttach> iterator = advertisAttachs.iterator(); iterator.hasNext();){
			AdvertisAttach advertisAttach = iterator.next();
			if(advertisAttach == null || advertisAttach.getUrl() ==null){
				iterator.remove();
				continue;
			}
			if(advertisAttach.getName() == null){
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			advertisAttach.setFileName(advertisAttach.getName()
							+ "."
							+ advertisAttach.getSuffix());
			advertisAttach.setShopReward(shopReward);
//			advertisAttach.setStoreMember(storeMemberBaseService.getCurrent());
		}
		shopReward.setAdvertisAttachs(advertisAttachs);
		save(shopReward);
	}

	@Override
	public void updateShopReward(Long storeId, ShopReward shopReward) {
		Store store = storeBaseService.find(storeId);
		shopReward.setStore(store);
		//广告合同附件
		List<AdvertisAttach> advertisAttachs = shopReward.getAdvertisAttachs();
		for (Iterator<AdvertisAttach> iterator = advertisAttachs.iterator(); iterator.hasNext();){
			AdvertisAttach advertisAttach = iterator.next();
			if(advertisAttach == null || advertisAttach.getUrl() ==null){
				iterator.remove();
				continue;
			}
			if(advertisAttach.getName() == null){
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			advertisAttach.setFileName(advertisAttach.getName()
							+ "."
							+ advertisAttach.getSuffix());
			advertisAttach.setShopReward(shopReward);
//			advertisAttach.setStoreMember(storeMemberBaseService.getCurrent());
		}
		shopReward.setAdvertisAttachs(advertisAttachs);
		update(shopReward);
	}

}
