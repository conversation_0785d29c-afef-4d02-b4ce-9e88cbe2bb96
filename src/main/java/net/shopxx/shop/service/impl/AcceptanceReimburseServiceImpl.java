package net.shopxx.shop.service.impl;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.dao.AcceptanceReimburseDao;
import net.shopxx.shop.entity.AcceptanceReimburse;
import net.shopxx.shop.entity.AcceptanceReimburseAttach;
import net.shopxx.shop.service.AcceptanceReimburseService;
import net.shopxx.shop.service.ShopInfoService;

@Service("acceptanceReimburseServiceImpl")
public class AcceptanceReimburseServiceImpl extends ActWfBillServiceImpl<AcceptanceReimburse>
		implements AcceptanceReimburseService {

	@Resource(name = "acceptanceReimburseDao")
	private AcceptanceReimburseDao acceptanceReimburseDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "actWfServiceImpl")
	private ActWfService actWfService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	@Override
	@Transactional
	public void saveAcceptanceReimburse(AcceptanceReimburse acceptanceReimburse) {
		// 单号
		acceptanceReimburse.setSn(Sequence.getInstance().getSequence("BX"));
		// 判断设计单号是否已经生成过报销单
		if (this.exists(Filter.eq("type", "1"), Filter.eq("shopDevise", acceptanceReimburse.getShopDevise().getId()))) {
			ExceptionUtil.throwServiceException(
					"单号为【" + acceptanceReimburse.getShopDevise().getSn() + "】的设计单已经生成过装修验收及报销单，不能再重复生成！");
		}
		// 验收申请承诺附件
		List<AcceptanceReimburseAttach> c1Attachs = handle1Attach(acceptanceReimburse,
				acceptanceReimburse.getAcceptanceCommitmentAttachs(), 1);
		if (c1Attachs == null || c1Attachs.size() == 0) {
			ExceptionUtil.throwServiceException("门店装修合同复印件不能为空！");
		}
		List<AcceptanceReimburseAttach> c2Attachs = handle1Attach(acceptanceReimburse,
				acceptanceReimburse.getStoreContractAttachs(), 1);
		if (c2Attachs == null || c2Attachs.size() == 0) {
			ExceptionUtil.throwServiceException("门店租赁合同/房产复印件不能为空！");
		}
		List<AcceptanceReimburseAttach> c3Attachs = handle1Attach(acceptanceReimburse,
				acceptanceReimburse.getStorePictureAttachs(), 1);
		if (c3Attachs == null || c3Attachs.size() == 0) {
			ExceptionUtil.throwServiceException("门店平面图不能为空！");
		}
		acceptanceReimburse.setAcceptanceCommitmentAttachs(c1Attachs);
		acceptanceReimburse.setStoreContractAttachs(c2Attachs);
		acceptanceReimburse.setStorePictureAttachs(c3Attachs);

		// 门店装修验收照片附件
		List<AcceptanceReimburseAttach> d1Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate1Attachs(), 2);
		acceptanceReimburse.setDecorate1Attachs(d1Attach);
		List<AcceptanceReimburseAttach> d2Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate2Attachs(), 2);
		acceptanceReimburse.setDecorate2Attachs(d2Attach);
		List<AcceptanceReimburseAttach> d3Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate3Attachs(), 2);
		acceptanceReimburse.setDecorate3Attachs(d3Attach);
		List<AcceptanceReimburseAttach> d4Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate4Attachs(), 2);
		acceptanceReimburse.setDecorate4Attachs(d4Attach);
		List<AcceptanceReimburseAttach> d5Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate5Attachs(), 2);
		acceptanceReimburse.setDecorate5Attachs(d5Attach);

		this.save(acceptanceReimburse);
	}

	@Override
	@Transactional
	public void updateAcceptanceReimburse(AcceptanceReimburse acceptanceReimburse) {
		// 验收申请承诺附件
		List<AcceptanceReimburseAttach> c1Attachs = handle1Attach(acceptanceReimburse,
				acceptanceReimburse.getAcceptanceCommitmentAttachs(), 1);
		if (c1Attachs == null || c1Attachs.size() == 0) {
			ExceptionUtil.throwServiceException("门店装修合同复印件不能为空！");
		}
		List<AcceptanceReimburseAttach> c2Attachs = handle1Attach(acceptanceReimburse,
				acceptanceReimburse.getStoreContractAttachs(), 1);
		if (c2Attachs == null || c2Attachs.size() == 0) {
			ExceptionUtil.throwServiceException("门店租赁合同/房产复印件不能为空！");
		}
		List<AcceptanceReimburseAttach> c3Attachs = handle1Attach(acceptanceReimburse,
				acceptanceReimburse.getStorePictureAttachs(), 1);
		if (c3Attachs == null || c3Attachs.size() == 0) {
			ExceptionUtil.throwServiceException("门店平面图不能为空！");
		}
		acceptanceReimburse.setAcceptanceCommitmentAttachs(c1Attachs);
		acceptanceReimburse.setStoreContractAttachs(c2Attachs);
		acceptanceReimburse.setStorePictureAttachs(c3Attachs);
		// 门店装修验收照片附件
		List<AcceptanceReimburseAttach> d1Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate1Attachs(), 2);
		acceptanceReimburse.getDecorate1Attachs().clear();
		acceptanceReimburse.getDecorate1Attachs().addAll(d1Attach);
		List<AcceptanceReimburseAttach> d2Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate2Attachs(), 2);
		acceptanceReimburse.getDecorate2Attachs().clear();
		acceptanceReimburse.getDecorate2Attachs().addAll(d2Attach);
		List<AcceptanceReimburseAttach> d3Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate3Attachs(), 2);
		acceptanceReimburse.getDecorate3Attachs().clear();
		acceptanceReimburse.getDecorate3Attachs().addAll(d3Attach);
		List<AcceptanceReimburseAttach> d4Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate4Attachs(), 2);
		//acceptanceReimburse.setDecorate4Attachs(d4Attach);
		acceptanceReimburse.getDecorate4Attachs().clear();
		acceptanceReimburse.getDecorate4Attachs().addAll(d4Attach);
		List<AcceptanceReimburseAttach> d5Attach = handleAttach(acceptanceReimburse,
				acceptanceReimburse.getDecorate5Attachs(), 2);
		//acceptanceReimburse.setDecorate5Attachs(d5Attach);
		acceptanceReimburse.getDecorate5Attachs().clear();
		acceptanceReimburse.getDecorate5Attachs().addAll(d5Attach);

		this.update(acceptanceReimburse);
	}

	/**
	 * 处理附件
	 * 
	 * @param ar
	 * @param attachs
	 * @param type
	 *            1、不限制附件数量，2、限制附件数量
	 * @return
	 */
	public List<AcceptanceReimburseAttach> handleAttach(AcceptanceReimburse ar, List<AcceptanceReimburseAttach> attachs,
			Integer type) {
		if (type == null && attachs != null && attachs.size() < 4) {
			ExceptionUtil.throwServiceException("附件照片不能少于 4 张");
		}
		Iterator<AcceptanceReimburseAttach> iterator = attachs.iterator();
		while (iterator.hasNext()) {
			AcceptanceReimburseAttach attach = iterator.next();
			if (attach == null || attach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (attach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			attach.setFileName(attach.getName() + "." + attach.getSuffix());
			attach.setAcceptanceReimburse(ar);
			attach.setStoreMember(storeMemberService.getCurrent());
		}
		return attachs;
	}

	public List<AcceptanceReimburseAttach> handle1Attach(AcceptanceReimburse ar,
			List<AcceptanceReimburseAttach> attachs, Integer type) {
		if (type == null && attachs != null && attachs.size() < 1) {
			ExceptionUtil.throwServiceException("附件照片不能少于 1 张");
		}
		Iterator<AcceptanceReimburseAttach> iterator = attachs.iterator();
		while (iterator.hasNext()) {
			AcceptanceReimburseAttach attach = iterator.next();
			if (attach == null || attach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (attach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			attach.setFileName(attach.getName() + "." + attach.getSuffix());
			attach.setAcceptanceReimburse(ar);
			attach.setStoreMember(storeMemberService.getCurrent());
		}
		return attachs;
	}

	@Override
	public Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable) {
		return acceptanceReimburseDao.findPage(param, pageable);
	}

	@Override
	public List<Map<String, Object>> findAttach(Long id, Integer type) {
		return acceptanceReimburseDao.findAttach(id, type);
	}
	
	@Override
	public List<Map<String, Object>> findAttach(Long id, Integer[] type) {
		
		return acceptanceReimburseDao.findAttach(id, type);
	}

	// ------------------- 验收不报销 -------------------

	@Override
	public Page<Map<String, Object>> findAcceptancePage(List<Object> param, Pageable pageable) {
		return acceptanceReimburseDao.findAcceptancePage(param, pageable);
	}

	@Override
	@Transactional
	public void saveAcceptance(AcceptanceReimburse acceptanceReimburse) {
		// 单号
		acceptanceReimburse.setSn(Sequence.getInstance().getSequence("YS"));
		// 判断设计单号是否已经生成过不报销单
		if (this.exists(Filter.eq("type", "2"), Filter.eq("shopDevise", acceptanceReimburse.getShopDevise().getId()))) {
			ExceptionUtil.throwServiceException(
					"单号为【" + acceptanceReimburse.getShopDevise().getSn() + "】的设计单已经生成过装修验收单，不能再重复生成！");
		}
		// 验收申请承诺附件
		// List<AcceptanceReimburseAttach> commitmentAttachs =
		// acceptanceReimburse.getAcceptanceCommitmentAttachs();
		// if (commitmentAttachs == null || commitmentAttachs.size() == 0) {
		// ExceptionUtil.throwServiceException("门店平面图、门店租赁合同/房产复印件、门店装修合同复印件等相关附件不能为空");
		// }
		// Iterator<AcceptanceReimburseAttach> commitmentIterator =
		// commitmentAttachs.iterator();
		// while (commitmentIterator.hasNext()) {
		// AcceptanceReimburseAttach reimburseAttach =
		// commitmentIterator.next();
		// if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
		// commitmentIterator.remove();
		// continue;
		// }
		// if (reimburseAttach.getName() == null) {
		// ExceptionUtil.throwServiceException("附件名不能为空");
		// }
		// reimburseAttach.setFileName(reimburseAttach.getName() + "." +
		// reimburseAttach.getSuffix());
		// reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
		// reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		// }
		// acceptanceReimburse.setAcceptanceCommitmentAttachs(commitmentAttachs);

		// 门店装修验收照片附件
		List<AcceptanceReimburseAttach> shopFarAttachs = acceptanceReimburse.getShopFarAttachs();
		if (shopFarAttachs == null || shopFarAttachs.size() < 1) {
			ExceptionUtil.throwServiceException("门头远近距离照附件不能为空");
		}
		Iterator<AcceptanceReimburseAttach> shopFarIterator = shopFarAttachs.iterator();
		while (shopFarIterator.hasNext()) {
			AcceptanceReimburseAttach reimburseAttach = shopFarIterator.next();
			if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
				shopFarIterator.remove();
				continue;
			}
			if (reimburseAttach.getName() == null) {
				ExceptionUtil.throwServiceException("门头远近距离照附件名不能为空");
			}
			reimburseAttach.setFileName(reimburseAttach.getName() + "." + reimburseAttach.getSuffix());
			reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
			reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		}
		acceptanceReimburse.setShopFarAttachs(shopFarAttachs);

		List<AcceptanceReimburseAttach> shopDecorateAttachs = acceptanceReimburse.getShopDecorateAttachs();
		if (shopDecorateAttachs == null || shopDecorateAttachs.size() < 1) {
			ExceptionUtil.throwServiceException("门头远近距离照附件不能为空");
		}
		Iterator<AcceptanceReimburseAttach> shopDecorateIterator = shopDecorateAttachs.iterator();
		while (shopDecorateIterator.hasNext()) {
			AcceptanceReimburseAttach reimburseAttach = shopDecorateIterator.next();
			if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
				shopDecorateIterator.remove();
				continue;
			}
			if (reimburseAttach.getName() == null) {
				ExceptionUtil.throwServiceException("门头远近距离照附件名不能为空");
			}
			reimburseAttach.setFileName(reimburseAttach.getName() + "." + reimburseAttach.getSuffix());
			reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
			reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		}
		acceptanceReimburse.setShopDecorateAttachs(shopDecorateAttachs);

		List<AcceptanceReimburseAttach> payPlatformAttachs = acceptanceReimburse.getPayPlatformAttachs();
		if (payPlatformAttachs == null || payPlatformAttachs.size() < 1) {
			ExceptionUtil.throwServiceException("收银台附件不能为空");
		}
		Iterator<AcceptanceReimburseAttach> payPlatformIterator = payPlatformAttachs.iterator();
		while (payPlatformIterator.hasNext()) {
			AcceptanceReimburseAttach reimburseAttach = payPlatformIterator.next();
			if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
				payPlatformIterator.remove();
				continue;
			}
			if (reimburseAttach.getName() == null) {
				ExceptionUtil.throwServiceException("收银台附件名不能为空");
			}
			reimburseAttach.setFileName(reimburseAttach.getName() + "." + reimburseAttach.getSuffix());
			reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
			reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		}
		acceptanceReimburse.setPayPlatformAttachs(payPlatformAttachs);

		List<AcceptanceReimburseAttach> plateAttachs = acceptanceReimburse.getPlateAttachs();
		if (plateAttachs == null || plateAttachs.size() < 5) {
			ExceptionUtil.throwServiceException("请上传各样板区附件且数量不少于5");
		}
		Iterator<AcceptanceReimburseAttach> plateIterator = plateAttachs.iterator();
		while (plateIterator.hasNext()) {
			AcceptanceReimburseAttach reimburseAttach = plateIterator.next();
			if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
				plateIterator.remove();
				continue;
			}
			if (reimburseAttach.getName() == null) {
				ExceptionUtil.throwServiceException("各样板区附件名不能为空");
			}
			reimburseAttach.setFileName(reimburseAttach.getName() + "." + reimburseAttach.getSuffix());
			reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
			reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		}
		acceptanceReimburse.setPlateAttachs(plateAttachs);

		this.save(acceptanceReimburse);
	}

	@Override
	@Transactional
	public void updateAcceptance(AcceptanceReimburse acceptanceReimburse) {
		// 验收申请承诺附件
		// List<AcceptanceReimburseAttach> commitmentAttachs =
		// acceptanceReimburse.getAcceptanceCommitmentAttachs();
		// if (commitmentAttachs == null || commitmentAttachs.size() == 0) {
		// ExceptionUtil.throwServiceException("门店平面图、门店租赁合同/房产复印件、门店装修合同复印件等相关附件不能为空");
		// }
		// Iterator<AcceptanceReimburseAttach> commitmentIterator =
		// commitmentAttachs.iterator();
		// while (commitmentIterator.hasNext()) {
		// AcceptanceReimburseAttach reimburseAttach =
		// commitmentIterator.next();
		// if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
		// commitmentIterator.remove();
		// continue;
		// }
		// if (reimburseAttach.getName() == null) {
		// ExceptionUtil.throwServiceException("附件名不能为空");
		// }
		// reimburseAttach.setFileName(reimburseAttach.getName() + "." +
		// reimburseAttach.getSuffix());
		// reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
		// reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		// }
		// acceptanceReimburse.setAcceptanceCommitmentAttachs(commitmentAttachs);
		// 门店装修验收照片附件
		List<AcceptanceReimburseAttach> shopFarAttachs = acceptanceReimburse.getShopFarAttachs();
		if (shopFarAttachs == null || shopFarAttachs.size() < 1) {
			ExceptionUtil.throwServiceException("门头远近距离照附件不能为空");
		}
		Iterator<AcceptanceReimburseAttach> shopFarIterator = shopFarAttachs.iterator();
		while (shopFarIterator.hasNext()) {
			AcceptanceReimburseAttach reimburseAttach = shopFarIterator.next();
			if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
				shopFarIterator.remove();
				continue;
			}
			if (reimburseAttach.getName() == null) {
				ExceptionUtil.throwServiceException("门头远近距离照附件名不能为空");
			}
			reimburseAttach.setFileName(reimburseAttach.getName() + "." + reimburseAttach.getSuffix());
			reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
			reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		}
		acceptanceReimburse.setShopFarAttachs(shopFarAttachs);

		List<AcceptanceReimburseAttach> shopDecorateAttachs = acceptanceReimburse.getShopDecorateAttachs();
		if (shopDecorateAttachs == null || shopDecorateAttachs.size() < 1) {
			ExceptionUtil.throwServiceException("门头远近距离照附件不能为空");
		}
		Iterator<AcceptanceReimburseAttach> shopDecorateIterator = shopDecorateAttachs.iterator();
		while (shopDecorateIterator.hasNext()) {
			AcceptanceReimburseAttach reimburseAttach = shopDecorateIterator.next();
			if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
				shopDecorateIterator.remove();
				continue;
			}
			if (reimburseAttach.getName() == null) {
				ExceptionUtil.throwServiceException("门头远近距离照附件名不能为空");
			}
			reimburseAttach.setFileName(reimburseAttach.getName() + "." + reimburseAttach.getSuffix());
			reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
			reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		}
		acceptanceReimburse.setShopDecorateAttachs(shopDecorateAttachs);

		List<AcceptanceReimburseAttach> payPlatformAttachs = acceptanceReimburse.getPayPlatformAttachs();
		if (payPlatformAttachs == null || payPlatformAttachs.size() < 1) {
			ExceptionUtil.throwServiceException("收银台附件不能为空");
		}
		Iterator<AcceptanceReimburseAttach> payPlatformIterator = payPlatformAttachs.iterator();
		while (payPlatformIterator.hasNext()) {
			AcceptanceReimburseAttach reimburseAttach = payPlatformIterator.next();
			if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
				payPlatformIterator.remove();
				continue;
			}
			if (reimburseAttach.getName() == null) {
				ExceptionUtil.throwServiceException("收银台附件名不能为空");
			}
			reimburseAttach.setFileName(reimburseAttach.getName() + "." + reimburseAttach.getSuffix());
			reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
			reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		}
		acceptanceReimburse.setPayPlatformAttachs(payPlatformAttachs);

		List<AcceptanceReimburseAttach> plateAttachs = acceptanceReimburse.getPlateAttachs();
		if (plateAttachs == null || plateAttachs.size() < 5) {
			ExceptionUtil.throwServiceException("请上传各样板区附件且数量不少于5");
		}
		Iterator<AcceptanceReimburseAttach> plateIterator = plateAttachs.iterator();
		while (plateIterator.hasNext()) {
			AcceptanceReimburseAttach reimburseAttach = plateIterator.next();
			if (reimburseAttach == null || reimburseAttach.getUrl() == null) {
				plateIterator.remove();
				continue;
			}
			if (reimburseAttach.getName() == null) {
				ExceptionUtil.throwServiceException("各样板区附件名不能为空");
			}
			reimburseAttach.setFileName(reimburseAttach.getName() + "." + reimburseAttach.getSuffix());
			reimburseAttach.setAcceptanceReimburse(acceptanceReimburse);
			reimburseAttach.setStoreMember(storeMemberService.getCurrent());
		}
		acceptanceReimburse.setPlateAttachs(plateAttachs);

		this.update(acceptanceReimburse);
	}

	@Override
	@Transactional
	public void saveGenerate(AcceptanceReimburse acceptanceReimburse, Integer sign) {
		if (sign == 0) {
			// 判断设计单号是否已经生成过不报销单
			if (this.exists(Filter.eq("type", "2"),
					Filter.eq("shopDevise", acceptanceReimburse.getShopDevise().getId()))) {
				ExceptionUtil.throwServiceException(
						"单号为【" + acceptanceReimburse.getShopDevise().getSn() + "】的设计单已经生成过装修验收单，不能再重复生成！");
			}
			// 生成不报销单
			acceptanceReimburse.setSn(Sequence.getInstance().getSequence("YS"));
			acceptanceReimburse.setType(2);
		} else if (sign == 1) {
			// 判断设计单号是否已经生成过报销单
			if (this.exists(Filter.eq("type", "1"),
					Filter.eq("shopDevise", acceptanceReimburse.getShopDevise().getId()))) {
				ExceptionUtil.throwServiceException(
						"单号为【" + acceptanceReimburse.getShopDevise().getSn() + "】的设计单已经生成过装修验收及报销单，不能再重复生成！");
			}
			// 生成报销单
			acceptanceReimburse.setSn(Sequence.getInstance().getSequence("BX"));
			acceptanceReimburse.setType(1);
		}
		acceptanceReimburse.setStatus(0);
		this.save(acceptanceReimburse);
	}

	/**
	 * 流程节点保存
	 */
	@Override
	public void saveform(Integer type, AcceptanceReimburse ar) {
		AcceptanceReimburse acceptanceReimburse = find(ar.getId());
		// type 1区域经理 2省长 3渠道部

		if (type == 2) {
			// 设置省长意见
			if (ar.getProvincialOperationMemo() != null) {
				acceptanceReimburse.setProvincialOperationMemo(ar.getProvincialOperationMemo());
			}
		}

		if (type == 3) {
			// 设置渠道专员意见
			if (ar.getDirectorOpinionMemo() != null) {
				acceptanceReimburse.setDirectorOpinionMemo(ar.getDirectorOpinionMemo());
			}

			if (ar.getShopAuthorizationCodes() != null) {
				acceptanceReimburse.setShopAuthorizationCodes(ar.getShopAuthorizationCodes());
			}

			if (ar.getArchivesCodes() != null) {
				acceptanceReimburse.setArchivesCodes(ar.getArchivesCodes());
			}

			if (ar.getNewTime() != null) {
				acceptanceReimburse.setNewTime(ar.getNewTime());
			}
		}
		this.update(acceptanceReimburse);

	}

	// 装修验收及报销节点保存
	@Override
	public void saveform1(AcceptanceReimburse acceptanceReimburse, Integer[] designManagers, Integer[] directorOpinions,
			Integer type) {
		// type 1区域经理 2省长 3设计部 4渠道部 5事业部总裁
		AcceptanceReimburse ar = find(acceptanceReimburse.getId());
		if (type == 1) {
			if (acceptanceReimburse.getDecorateConstructionTime() == null) {
				ExceptionUtil.throwServiceException("请填写装修施工时间");
			}
			if (acceptanceReimburse.getDecorateCompleteTime() == null) {
				ExceptionUtil.throwServiceException("请填写装修完成时间");
			}
			if (acceptanceReimburse.getAcceptanceShop() == null) {
				ExceptionUtil.throwServiceException("请填写门店装修属性");
			}
			if (acceptanceReimburse.getAcceptanceVerifyArea() == null) {
				ExceptionUtil.throwServiceException("请填写有效面积");
			}
			if (acceptanceReimburse.getAcceptanceVerifyScore() == null) {
				ExceptionUtil.throwServiceException("请填写评分");
			}
			ar.setDecorateConstructionTime(acceptanceReimburse.getDecorateConstructionTime());
			ar.setDecorateCompleteTime(acceptanceReimburse.getDecorateCompleteTime());
			ar.setAcceptanceShop(acceptanceReimburse.getAcceptanceShop());
			ar.setAcceptanceVerifyArea(acceptanceReimburse.getAcceptanceVerifyArea());
			ar.setAcceptanceVerifyScore(acceptanceReimburse.getAcceptanceVerifyScore());

			List<AcceptanceReimburseAttach> d1Attach = handleAttach(acceptanceReimburse,
					acceptanceReimburse.getDecorate1Attachs(), 2);
			ar.getDecorate1Attachs().clear();
			ar.getDecorate1Attachs().addAll(d1Attach);

			List<AcceptanceReimburseAttach> d2Attach = handleAttach(acceptanceReimburse,
					acceptanceReimburse.getDecorate2Attachs(), 2);
			ar.getDecorate2Attachs().clear();
			ar.getDecorate2Attachs().addAll(d2Attach);

			List<AcceptanceReimburseAttach> d3Attach = handleAttach(acceptanceReimburse,
					acceptanceReimburse.getDecorate3Attachs(), 2);
			ar.getDecorate3Attachs().clear();
			ar.getDecorate3Attachs().addAll(d3Attach);

			List<AcceptanceReimburseAttach> d4Attach = handleAttach(acceptanceReimburse,
					acceptanceReimburse.getDecorate4Attachs(), 2);
			ar.getDecorate4Attachs().clear();
			ar.getDecorate4Attachs().addAll(d4Attach);

			List<AcceptanceReimburseAttach> d5Attach = handleAttach(acceptanceReimburse,
					acceptanceReimburse.getDecorate5Attachs(), 2);
			ar.getDecorate5Attachs().clear();
			ar.getDecorate5Attachs().addAll(d5Attach);
		}
		if (type == 2) {
			if (acceptanceReimburse.getSzyj() == null) {
				ExceptionUtil.throwServiceException("请填写意见！");
			}
			ar.setSzyj(acceptanceReimburse.getSzyj());
		}
		if (type == 3) {
			if (acceptanceReimburse.getShopAuthorizationCodes() == null) {
				ExceptionUtil.throwServiceException("请填写门店授权编码");
			}
			if (acceptanceReimburse.getArchivesCodes() == null) {
				ExceptionUtil.throwServiceException("请填写新增档案编号");
			}
			if (acceptanceReimburse.getNewTime() == null) {
				ExceptionUtil.throwServiceException("请填写新增时间");
			}
			if (acceptanceReimburse.getProvincialOperationMemo() == null) {
				ExceptionUtil.throwServiceException("请填写备注");
			}
			ar.setShopAuthorizationCodes(acceptanceReimburse.getShopAuthorizationCodes());
			ar.setArchivesCodes(acceptanceReimburse.getArchivesCodes());
			ar.setNewTime(acceptanceReimburse.getNewTime());
			ar.setProvincialOperationMemo(acceptanceReimburse.getProvincialOperationMemo());
		}
		if (type == 4) {
			if (acceptanceReimburse.getDesignManagerMemo() == null) {
				ExceptionUtil.throwServiceException("请填写备注");
			}
			ar.setDesignManager(splicer(designManagers));
			ar.setConstructionFigureCompleteTime(acceptanceReimburse.getConstructionFigureCompleteTime());
			ar.setDesignManagerArea(acceptanceReimburse.getDesignManagerArea());
			ar.setDesignManagerScore(acceptanceReimburse.getDesignManagerScore());
			ar.setDesignManagerMemo(acceptanceReimburse.getDesignManagerMemo());
		}
		if (type == 5) {
			if (acceptanceReimburse.getDirectorOpinionMemo() == null) {
				ExceptionUtil.throwServiceException("请填写备注");
			}
			ar.setDirectorOpinion(splicer(directorOpinions));
			ar.setPayDeposit(acceptanceReimburse.getPayDeposit());
			ar.setDirectorOpinionMoney1(acceptanceReimburse.getDirectorOpinionMoney1());
			ar.setDirectorOpinionMoney2(acceptanceReimburse.getDirectorOpinionMoney2());
			ar.setDirectorOpinionMoney3(acceptanceReimburse.getDirectorOpinionMoney3());
			ar.setDirectorOpinionMemo(acceptanceReimburse.getDirectorOpinionMemo());
		}
		if (type == 6) {
			if (acceptanceReimburse.getFloorManagerOpinion() == null) {
				ExceptionUtil.throwServiceException("请选择意见");
			}
			ar.setFloorManagerOpinion(acceptanceReimburse.getFloorManagerOpinion());
		}
		this.update(ar);
	}

	public String splicer(Integer[] str) {
		String s = "";
		if (str != null && str.length > 0) {
			for (int i = 0; i < str.length; i++) {
				if (str.length - 1 == i) {
					s += str[i];
				} else {
					s += str[i] + ",";
				}
			}
		}
		return s;
	}

	@Override
	public void endBack(ActWf wf) {
		super.endBack(wf);
		AcceptanceReimburse acceptanceReimburse = find(wf.getObjId());
		acceptanceReimburse.setStatus(2);// 结束后改为已完成状态
		// //更新门店资料装修状态
		// String decorationStatus = "已装修";
		// shopInfoService.updateDecorationStatus(acceptanceReimburse.getShopInfo(),decorationStatus);
		// //更新门店状态
		// String shopStatus = "正在营业";
		// shopInfoService.updateShopStatus(acceptanceReimburse.getShopInfo(),shopStatus);
		update(acceptanceReimburse);
	}

	// @Override
	// public void completeBack(ActWf wf) {
	// Task currTaskByWf = actWfService.getCurrTaskByWf(wf);
	// //String name = currTaskByWf.getName();
	// List<Map<String, Object>> item =
	// actWfService.getWfProcList(wf.getProcInstId());
	// String assignee = currTaskByWf.getAssignee();
	//
	// LogUtils.info("completeBack.currTaskByWf.getAssignee"+assignee);
	// LogUtils.info(item.toString());
	// //LogUtils.info("completeBack.currTaskByWf.getName():"+name);
	// }

	@Override
	public void startBack(ActWf wf) {
		super.startBack(wf);
		AcceptanceReimburse acceptanceReimburse = find(wf.getObjId());
		acceptanceReimburse.setStatus(1);// 进行中状态
		update(acceptanceReimburse);
	}

	@Override
	public void interruptBack(ActWf wf) {
		super.interruptBack(wf);
		AcceptanceReimburse acceptanceReimburse = find(wf.getObjId());
		// acceptanceReimburse.setStatus(3);//终止状态
		acceptanceReimburse.setStatus(0);// 保存
		update(acceptanceReimburse);
	}

	@Override
	@Transactional
	public void createWf(Long id, String modelId, Long objTypeId) {
		AcceptanceReimburse acceptanceReimburse = find(id);
		if (acceptanceReimburse.getWfId() != null) {
			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		createWf(acceptanceReimburse.getSn(), String.valueOf(storeMember.getId()),
				new Long[] { acceptanceReimburse.getSaleOrg().getId() },
				acceptanceReimburse.getShopInfo().getStore().getId(), modelId, objTypeId, id,
				WebUtils.getCurrentCompanyInfoId(), true);

		acceptanceReimburse.setStatus(1);
		update(acceptanceReimburse);
	}

	public void completePre(ActWf wf) {
		AcceptanceReimburse acceptanceReimburse = find(wf.getObjId());
		Task t = this.getCurrTaskByWf(wf);
		String nodeName = t.getName();
		if (acceptanceReimburse.getType() == 1) {// 验收及报销
			if (nodeName.contains("区域经理")) {
				if (acceptanceReimburse.getDecorateConstructionTime() == null) {
					ExceptionUtil.throwServiceException("请填写装修施工时间");
				}
				if (acceptanceReimburse.getDecorateCompleteTime() == null) {
					ExceptionUtil.throwServiceException("请填写装修完成时间");
				}
				if (acceptanceReimburse.getAcceptanceShop() == null) {
					ExceptionUtil.throwServiceException("请填写门店装修属性");
				}
				if (acceptanceReimburse.getAcceptanceVerifyArea() == null) {
					ExceptionUtil.throwServiceException("请填写有效面积");
				}
				if (acceptanceReimburse.getAcceptanceVerifyScore() == null) {
					ExceptionUtil.throwServiceException("请填写评分");
				}
			}
			if (nodeName.contains("省长")) {
				if (acceptanceReimburse.getSzyj() == null) {
					ExceptionUtil.throwServiceException("请填写意见！");
				}
			}
			if (nodeName.contains("渠道专员")) {
				if (acceptanceReimburse.getShopAuthorizationCodes() == null) {
					ExceptionUtil.throwServiceException("请填写门店授权编码");
				}
				if (acceptanceReimburse.getArchivesCodes() == null) {
					ExceptionUtil.throwServiceException("请填写新增档案编号");
				}
				if (acceptanceReimburse.getNewTime() == null) {
					ExceptionUtil.throwServiceException("请填写新增时间");
				}
				if (acceptanceReimburse.getProvincialOperationMemo() == null) {
					ExceptionUtil.throwServiceException("请填写备注");
				}
			}
			if (nodeName.contains("设计部")) {
				if (acceptanceReimburse.getDesignManagerMemo() == null) {
					ExceptionUtil.throwServiceException("请填写备注");
				}
			}
			if (nodeName.contains("渠道总监")) {
				if (acceptanceReimburse.getDirectorOpinionMemo() == null) {
					ExceptionUtil.throwServiceException("请填写备注");
				}
			}
			if (nodeName.contains("事业部")) {
				if (acceptanceReimburse.getFloorManagerOpinion() == null) {
					ExceptionUtil.throwServiceException("请选择意见");
				}
			}
		} else if (acceptanceReimburse.getType() == 2) {// 验收不报销

		}

	}

	@Override
	public List<Map<String, Object>> findAcceptanceReimburse(Long id) {
		// TODO Auto-generated method stub
		return acceptanceReimburseDao.findAcceptanceReimburse(id);
	}

	
}
