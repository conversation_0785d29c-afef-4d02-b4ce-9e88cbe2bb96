package net.shopxx.shop.service.impl;

import java.util.*;

import javax.annotation.Resource;

import net.shopxx.member.entity.PcUserRole;
import net.shopxx.member.service.PcUserRoleBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.shop.dao.StoreMemberDao;
import net.shopxx.shop.service.StoreMemberService;
@Service("storeMemberServiceImpl")
public class StoreMemberServiceImpl extends BaseServiceImpl<StoreMember> implements 
	StoreMemberService {

	@Resource(name = "storeMemberDao")
	private StoreMemberDao storeMemberDao;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "pcUserRoleBaseServiceImpl")
	private PcUserRoleBaseService pcUserRoleBaseService;
	
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findStoreMemberPage(Pageable pageable, Object[] args) {
		return storeMemberDao.findPage(pageable, args);
	}
	
	public String findSalesman(Long id){
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("parent", id));
		filters.add(Filter.eq("isEnabled", true));
		filters.add(Filter.eq("isSalesman", true));
		filters.add(Filter.eq("companyInfoId", WebUtils.getCurrentCompanyInfoId()));
		List<StoreMember> storeMembers = findList(null, filters, null);
		String ids = "";
		if(storeMembers.size()>0){
			for(int i=0;i<storeMembers.size();i++){
				if (storeMembers.size() - 1 == i) {
					ids += storeMembers.get(i).toString();
				} else {
					ids += storeMembers.get(i).toString() + ",";
				}
			}		
		}else{
			ids += id.toString();
		}
		return ids;
	}

	@Override
	public Boolean hasRoles(StoreMember storeMember,String... roleName) {
		if(storeMember==null||roleName==null) {
			return false;
		}
		Set<String> roleNameSet = new HashSet<String>(Arrays.asList(roleName));
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("storeMember",storeMember));
		filters.add(Filter.eq("companyInfoId",storeMember.getCompanyInfoId()));
		List<PcUserRole> pcUserRole = pcUserRoleBaseService.findList(null, filters, null);
		for (PcUserRole pcRole : pcUserRole) {
			String pcRoleName = pcRole.getPcRole().getName();
			if(roleNameSet.contains(pcRoleName)) {
				return true;
			}
		}
		return false;
	}

	
}
