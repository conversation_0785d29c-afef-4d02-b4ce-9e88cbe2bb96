package net.shopxx.shop.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.product.entity.Product;
import net.shopxx.shop.dao.ShopProductDao;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.entity.ShopProudct;
import net.shopxx.shop.service.ShopProductService;

@Service("shopProductServiceImpl")
public class ShopProductServiceImpl extends BaseServiceImpl<ShopProudct> implements ShopProductService {

    @Resource(name = "shopProductDao")
    private ShopProductDao shopProductDao;



    @Override
    @Transactional
    public void operation(ShopInfo shop, Product product, Integer type) {
        Boolean isProduct = true;
        ShopProudct sp = null;
        Boolean IsReferrer = null;
        Boolean IsShow = null;
        Boolean IsMarketable = null;
        List<Filter> filters = new ArrayList<Filter>();
        filters.add(Filter.eq("shopInfo", shop));
        filters.add(Filter.eq("product", product));
        List<ShopProudct> sps = this.findList(null, filters, null);
        if(sps==null||sps.size()<=0){
            isProduct = false;
        }else{
            sp = sps.get(0);
            IsReferrer = sp.getIsReferrer();
            IsShow = sp.getIsShow();
            IsMarketable = sp.getIsMarketable();
        }
        if(type == 3){
            IsReferrer = true;
        }
        if(type == 4){
            IsReferrer = false;
        }
        if(type == 5){
            IsShow = true;
        }
        if(type == 6){
            IsShow = false;
        }
        if(type == 1){
            IsMarketable = true;
        }
        if(type == 2){
            IsMarketable = false;
        }
        System.out.println("商品存在吗："+isProduct);
        if(isProduct){
            System.out.println("更新");
            sp.setIsShow(IsShow);
            sp.setIsMarketable(IsMarketable);
            sp.setIsReferrer(IsReferrer);
            if(type == 1){
                sp.setMarketableTime(new Date());
            } else if(type == 2){
                sp.setMarketableTime(null);
            }



            update(sp);
        }else{
            System.out.println("保存");
            ShopProudct newSp = new ShopProudct();
            newSp.setShopInfo(shop);
            newSp.setProduct(product);
            newSp.setIsMarketable(IsMarketable==null?false:IsMarketable);
            newSp.setIsShow(IsShow==null?false:IsShow);
            newSp.setIsReferrer(IsReferrer==null?false:IsReferrer);
            if(type == 1){
                newSp.setMarketableTime(new Date());
            } else if(type == 2){
                newSp.setMarketableTime(null);
            }
            save(newSp);
        }
    }

    public List<Map<String, Object>> findShopProduct(Long shopId){
        return shopProductDao.findShopProduct(shopId);
    }


    /**
     *条件查询门店上样各级数量
     * @param shopProductCategoryId 门店上样分类
     * @param productCategoryId	商品分类
     * @param shopId	shopInfoId
     * @param isMarketable 否是上样
     * @return
     */
    @Override
    public Integer findListCount(Long shopProductCategoryId, Long productCategoryId, Long shopId,Boolean isMarketable) {
        return shopProductDao.findListCount(shopProductCategoryId,productCategoryId,shopId,isMarketable);
    }

    @Override
    public Page<Map<String, Object>> findPage(Long shopProductCategoryId, Long productCategoryId, String vonderCode, String mod, String name, Long shopId, Boolean isReferrer, Boolean isMarketable, Boolean isShow, Pageable pageable) {
        return shopProductDao.findShopProductPage(shopProductCategoryId, productCategoryId, vonderCode, mod,
                name, shopId, isReferrer, isMarketable, isShow, pageable);
    }

    @Override
    public Page<Map<String, Object>> findAppShopProductPage(Long shopProductCategoryId, Long productCategoryId, String vonderCode, String mod, String name, Long shopId, Boolean isReferrer, Boolean isMarketable, Boolean isShow, Pageable pageable) {
        return shopProductDao.findAppShopProductPage(shopProductCategoryId, productCategoryId, vonderCode, mod,
                name, shopId, isReferrer, isMarketable, isShow, pageable);
    }

}
