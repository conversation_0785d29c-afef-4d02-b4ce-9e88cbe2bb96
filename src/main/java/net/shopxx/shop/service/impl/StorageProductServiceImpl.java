package net.shopxx.shop.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.shop.dao.StorageProductDao;
import net.shopxx.shop.entity.StorageProduct;
import net.shopxx.shop.service.StorageProductService;

@Service("storageProductServiceImpl")
public class StorageProductServiceImpl extends BaseServiceImpl<StorageProduct> 
        implements StorageProductService {
    
    @Resource(name = "storageProductDao")
    private StorageProductDao storageProductDao;

    @Override
    public Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable) {
        return storageProductDao.findPage(param, pageable);
    }

	@Override
	public Page<Map<String, Object>> findProduct(String name, String vonderCode, Pageable pageable) {
		return storageProductDao.findProduct(name, vonderCode, pageable);
	}

	@Override
	public List<Map<String, Object>> findProduct(Long storeId) {
		return storageProductDao.findProduct(storeId);
	}

}
