package net.shopxx.shop.service.impl;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.dao.RestructuringDao;
import net.shopxx.shop.entity.Restructuring;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.RestructuringService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.util.SnUtil;

@Service("restructuringServiceImpl")
public class RestructuringServiceImpl extends
ActWfBillServiceImpl<Restructuring> implements RestructuringService{
	
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "restructuringDao")
	private RestructuringDao restructuringDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;

	@Override
	public void saveRestructuring(Restructuring rt, Long storeId, Long shopInfoId) {
		rt.setSn(SnUtil.generateSn());
		rt.setStatus(0);
		Store store = storeBaseService.find(storeId);
		ShopInfo si = shopInfoService.find(shopInfoId);
		rt.setStore(store);
		rt.setShopInfo(si);
		rt.setSaleOrg(si.getSaleOrg());
		save(rt);
	}

	@Override
	public void updateRestructuring(Restructuring rt, Long storeId, Long shopInfoId) {
		Store store = storeBaseService.find(storeId);
		ShopInfo si = shopInfoService.find(shopInfoId);
		rt.setStore(store);
		rt.setShopInfo(si);
		rt.setSaleOrg(si.getSaleOrg());
		update(rt,"status");
	}

	@Override
	public Page<Map<String, Object>> findPage(String a, Pageable pageable) {
		return restructuringDao.findPage(a, pageable);
	}

//	@Override
//	public void saveform(Restructuring restructuring, Integer type) {
//		Restructuring rt = find(restructuring.getId());
//		if(type == 1){
//			rt.setMemo(restructuring.getMemo());
//			rt.setInventoryResult(restructuring.getInventoryResult());
//		}
//		this.update(rt);
//	}
	
	/**
	 * 创建流程实例
	 */
	@Override
	@Transactional
	public void createWf(Long id, String modelId, Long objTypeId) {
		Restructuring rt = find(id);
		if (rt.getWfId() != null) {
			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		// 创建流程实例
//		createWf(rt.getSn(), String.valueOf(storeMember.getId()), modelId, objTypeId, id,
//				WebUtils.getCurrentCompanyInfoId());
		createWf(rt.getSn(), String.valueOf(storeMember.getId()),
				new Long[]{rt.getSaleOrg().getId()},
				rt.getStore().getId(),
				modelId,
				objTypeId,
				id,
				WebUtils.getCurrentCompanyInfoId(),
                true);
		rt.setStatus(3);
		update(rt);
	}
	
	/**
	 * 中断流程回调
	 */
	public void interruptBack(ActWf wf) {
		Restructuring rt = find(wf.getObjId());
		rt.setStatus(0);
		update(rt);
	}

	@Override
	public void endBack(ActWf arg0) {
		super.endBack(arg0);
		Restructuring rt = find(arg0.getObjId());
		rt.setStatus(1);
		update(rt);
		shopInfoService.restructuringStatus(rt);
	}

}
