package net.shopxx.shop.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import net.shopxx.act.entity.ActWf;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ServletContextAware;
import org.springframework.web.multipart.MultipartFile;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.Area;
import net.shopxx.basic.entity.SaleOrg;
import net.shopxx.basic.service.AreaBaseService;
import net.shopxx.basic.service.SystemDictBaseService;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.order.service.OrderFullLinkService;
import net.shopxx.product.service.ProductCategoryBaseService;
import net.shopxx.shop.dao.ShopAddedDao;
import net.shopxx.shop.dao.ShopAlterationDao;
import net.shopxx.shop.dao.ShopInfoDao;
import net.shopxx.shop.entity.AcceptanceReimburse;
import net.shopxx.shop.entity.Restructuring;
import net.shopxx.shop.entity.Shop;
import net.shopxx.shop.entity.ShopAlteration;
import net.shopxx.shop.entity.ShopDecrease;
import net.shopxx.shop.entity.ShopDevise;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

@Service("shopInfoServiceImpl")
public class ShopInfoServiceImpl extends WfBillBaseServiceImpl<ShopInfo>
		implements ShopInfoService, ServletContextAware {

	@Resource(name = "shopInfoDao")
	private ShopInfoDao shopInfoDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "productCategoryBaseServiceImpl")
	private ProductCategoryBaseService productCategoryService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "wfTempBaseServiceImpl")
	private WfTempBaseService wfTempBaseService;
	@Resource(name = "orderFullLinkServiceImpl")
	private OrderFullLinkService orderFullLinkService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "systemDictBaseServiceImpl")
	private SystemDictBaseService systemDictBaseService;
	@Resource(name = "areaBaseServiceImpl")
	private AreaBaseService areaBaseService;
	/** servletContext */
	@SuppressWarnings("unused")
	private ServletContext servletContext;
	@Resource(name = "shopAddedDao")
	private ShopAddedDao shopAddedDao;
	@Resource(name = "shopAlterationDao")
	private ShopAlterationDao shopAlterationDao;

	@Override
	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findPage(String sn, String distributorName, String address,
			String authorizationCode, String shopStatus, String deviseStatus, String decorationStatus,
			Pageable pageable) {
		return shopInfoDao.findPage(sn, distributorName, address, authorizationCode, shopStatus, deviseStatus,
				decorationStatus, pageable);
	}

	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findSelectPage(String sn, String distributorName, String address,
			String authorizationCode,String[] shopStatus,Integer reimburse,String transitTime,String shopName, Pageable pageable) {
		return shopInfoDao.findSelectPage(sn, distributorName, address, authorizationCode, shopStatus, reimburse, transitTime,shopName, pageable);
	}

	@Override
	public List<Map<String, Object>> findBusinessCategoryApply(ShopInfo shopInfo) {

		return shopInfoDao.findBusinessCategoryApply(shopInfo);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> findListByStoreId(Long storeId) {
		return shopInfoDao.findListByStoreId(storeId);
	}

	@Override
	public void saveShopInfo(ShopInfo shopInfo) {
		if (shopInfo.getArea() == null || shopInfo.getArea().getId() == null) {
			shopInfo.setArea(null);
		}
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		shopInfo.setStoreMember(storeMember);
		shopInfo.setSn(Sequence.getInstance().getSequence(null));

		// List<BusinessCategoryShopInfo> bc =
		// shopInfo.getBusinessCategoryApplys();
		// for (Iterator<BusinessCategoryShopInfo> iterator = bc.iterator();
		// iterator.hasNext();) {
		// BusinessCategoryShopInfo businessCategory =
		// (BusinessCategoryShopInfo) iterator.next();
		// if (businessCategory == null
		// || businessCategory.getProductBigType() == null) {
		// iterator.remove();
		// }
		// if (businessCategory.getProductBigType() != null) {
		// businessCategory.setProductBigType(productCategoryService.find(businessCategory.getProductBigType()
		// .getId()));
		// }
		// if (businessCategory.getProductCenterType() != null) {
		// businessCategory.setProductCenterType(productCategoryService.find(businessCategory.getProductCenterType()
		// .getId()));
		// }
		// businessCategory.setShopInof(shopInfo);
		// }
		// shopInfo.setBusinessCategoryApplys(bc);

		// List<ShopInspection> shopInspections = shopInfo.getShopInspections();
		// for (Iterator<ShopInspection> iterator = shopInspections.iterator();
		// iterator.hasNext();) {
		// ShopInspection shopInspection = (ShopInspection) iterator.next();
		// if (shopInspection == null || shopInspection.getResult() == null) {
		// iterator.remove();
		// }
		// String result = null;
		// if (shopInspection.getResult().intValue() == 1) {
		// result = "正常营业";
		// } else if (shopInspection.getResult().intValue() == 2) {
		// result = "门店关闭";
		// } else if (shopInspection.getResult().intValue() == 3) {
		// result = "门店交接";
		// } else if (shopInspection.getResult().intValue() == 0) {
		// result = "其他(备注)";
		// }
		// shopInfo.setLastInspectionResult(result);
		// shopInfo.setLastCheckTime(shopInspection.getAuditDate());
		// shopInspection.setShopInfo(shopInfo);
		// }
		// shopInfo.setShopInspections(shopInspections);

		/*
		 * List<ShopDesign> shopDesigns = shopInfo.getShopDesigns(); for
		 * (Iterator<ShopDesign> iterator = shopDesigns.iterator();
		 * iterator.hasNext();) { ShopDesign shopDesign = (ShopDesign)
		 * iterator.next(); if (shopDesign == null ||
		 * shopDesign.getDesignStatu() == null) { iterator.remove(); } if
		 * (shopDesign.getArea() == null || shopDesign.getArea().getId() ==
		 * null) { shopDesign.setArea(null); }
		 * shopInfo.setLastDesignStatus(shopDesign.getDesignStatu());
		 * shopInfo.setLastDesignDate(shopDesign.getApplicationTime());
		 * shopDesign.setShopInfo(shopInfo); }
		 * shopInfo.setShopDesigns(shopDesigns);
		 */

		this.save(shopInfo);

	}

	@Override
	public void updateShopInfo(ShopInfo shopInfo) {
		this.update(shopInfo);
		shopInfoDao.updetaShopImages(shopInfo);
	}

	@Override
	@Transactional
	public String shopInfoImport(MultipartFile multipartFile) throws Exception {

		String msg = "";
		Workbook wb = null;
		File tempFile = null;
		Cell cell = null;
		tempFile = new File((new StringBuilder(String.valueOf(System.getProperty("java.io.tmpdir")))).append("/upload_")
				.append(UUID.randomUUID()).append(".tmp").toString());
		if (!tempFile.getParentFile().exists())
			tempFile.getParentFile().mkdirs();
		multipartFile.transferTo(tempFile);
		wb = Workbook.getWorkbook(tempFile);
		Sheet sheet = wb.getSheets()[0];
		int rows = sheet.getRows();

		// System.out.println("rows" + rows);
		Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
		if (rows > 1001)
			if (StringUtils.isNotBlank(sheet.getCell(0, 1000).getContents()))
				ExceptionUtil.throwServiceException("\u4E00\u6B21\u6700\u591A\u5BFC\u51651000\u6761", new Object[0]);
			else
				rows = 1001;
		List<Filter> filters = new ArrayList<Filter>();
		String authorizationCode;// 授权编码
		String out_trade_no;// 客户编码
		String region;// 区域
		String areaProvinceName;// 省份
		String areaCityName;// 地级城市
		String areaRegionName;// 区县城市
		String town;// 乡镇城市
		String franchisee;// 总经销商
		String distributorName;// 经销商姓名 5
		String joinDateStr;// 加盟日期
		String distributorPhone;// 经销商手机号码
		String administrativeRank;// 城市行政等级
		String openDateStr;// 建店日期
		String acreage;// 面积 10
		String address;// 详细地址
		String status;// 门店状态
		String statusNote;// 门店状态备注
		String positionType;// 位置类型
		String type;// 门店类别 15
		String lastInspectionResult;// 最后稽查结果
		String increaseArchivesCode;// 新增档案编号
		String decreaseArchivesCode;// 减少档案编号
		String shutDownMenu;// 门店关闭原因
		String closingTimeStr;// 门店关闭时间 20
		String viVersion;// VI版本
		String businessForm;// 业态
		String shopSign;// 招牌
		String salesPlatform;// 所属销售平台
		String department;// 所属部门 25
		String inclusiveBrand;// 所含品牌
		String belongBrand;// 所属品牌
		String salesChannel;// 销售渠道
		String salesChannelNote;// 销售渠道备注
		String salesVolume;// 门店销量 30
		String memo;// 备注
		String lastDesignDateStr;// 最后设计日期
		String lastDesignStatus;// 最后设计状态
		String activationTimeStr;// 激活时间 34
		String areaFullName;
		for (int i = 1; i < rows; i++) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			cell = sheet.getCell(0, i);
			authorizationCode = cell.getContents() == null ? "" : cell.getContents().trim();
			filters.clear();
			filters.add(Filter.eq("authorizationCode", authorizationCode));
			List<ShopInfo> shopInfoo = findList(null, filters, null);
			if (shopInfoo.size() > 0) {
				msg = "第" + i + "行." + "门店授权编码已存在";
				ExceptionUtil.throwServiceException(msg);
			}
			// 客户编码
			cell = sheet.getCell(1, i);
			out_trade_no = cell.getContents() == null ? "" : cell.getContents().trim();
			if (StringUtils.isEmpty(out_trade_no)) {
				throw new RuntimeException("第" + (i + 1) + "行,客户编码为空");
			}
			filters.clear();
			filters.add(Filter.eq("outTradeNo", out_trade_no));
			filters.add(Filter.eq("companyInfoId", companyInfoId));
			List<Store> stores = storeService.findList(null, filters, null);
			if (stores == null || stores.size() < 1) {
				throw new RuntimeException("第" + (i + 1) + "行,客户编码[" + out_trade_no + "]不存在");
			}
			Store store = stores.get(0);

			cell = sheet.getCell(2, i);
			region = cell.getContents() == null ? "" : cell.getContents().trim();
			// 省
			cell = sheet.getCell(3, i);
			areaProvinceName = cell.getContents() == null ? "" : cell.getContents().trim();
			// 市
			cell = sheet.getCell(4, i);
			areaCityName = cell.getContents() == null ? "" : cell.getContents().trim();
			// 区
			cell = sheet.getCell(5, i);
			areaRegionName = cell.getContents() == null ? "" : cell.getContents().trim();
			areaFullName = areaProvinceName + areaCityName + areaRegionName;
			// 乡镇
			cell = sheet.getCell(6, i);
			town = cell.getContents() == null ? "" : cell.getContents().trim();
			// 总经销商
			cell = sheet.getCell(7, i);
			franchisee = cell.getContents() == null ? "" : cell.getContents().trim();
			// 经销商姓名
			cell = sheet.getCell(8, i);
			distributorName = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(9, i);
			joinDateStr = cell.getContents();
			Date joinDate = null;
			if (joinDateStr != "" && !isValidDate(joinDateStr)) {
				msg = "第" + i + "行." + "加盟时间格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!StringUtils.isEmpty(joinDateStr)) {
				joinDate = sdf.parse(joinDateStr);
			}

			cell = sheet.getCell(10, i);
			distributorPhone = cell.getContents() == null ? "" : cell.getContents().trim();
			// 城市行政等级
			cell = sheet.getCell(11, i);
			administrativeRank = cell.getContents() == null ? "" : cell.getContents().trim();

			// 建店日期
			cell = sheet.getCell(12, i);
			openDateStr = cell.getContents();
			Date openDate = null;
			if (openDateStr != "" && !isValidDate(openDateStr)) {
				msg = "第" + i + "行." + "建店时间格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!StringUtils.isEmpty(openDateStr)) {
				openDate = sdf.parse(openDateStr);
			}

			cell = sheet.getCell(13, i);
			acreage = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(14, i);
			address = cell.getContents() == null ? "" : cell.getContents().trim();

			// 门店状态
			cell = sheet.getCell(15, i);
			status = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(16, i);
			statusNote = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(17, i);
			positionType = cell.getContents() == null ? "" : cell.getContents().trim();
			// 门店类别
			cell = sheet.getCell(18, i);
			type = cell.getContents() == null ? "" : cell.getContents().trim();

			// 最后稽查结果
			cell = sheet.getCell(19, i);
			lastInspectionResult = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(20, i);
			increaseArchivesCode = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(21, i);
			decreaseArchivesCode = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(22, i);
			shutDownMenu = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(23, i);
			closingTimeStr = cell.getContents();
			Date closingTime = null;
			if (closingTimeStr != "" && !isValidDate(closingTimeStr)) {
				msg = "第" + i + "行." + "门店关闭时间格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!StringUtils.isEmpty(closingTimeStr)) {
				closingTime = sdf.parse(closingTimeStr);
			}

			cell = sheet.getCell(24, i);
			viVersion = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(25, i);
			businessForm = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(26, i);
			shopSign = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(27, i);
			salesPlatform = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(28, i);
			department = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(29, i);
			inclusiveBrand = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(30, i);
			belongBrand = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(31, i);
			salesChannel = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(32, i);
			salesChannelNote = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(33, i);
			salesVolume = cell.getContents() == null ? "" : cell.getContents().trim();
			BigDecimal salesVolumeNum = BigDecimal.ZERO;
			if (!StringUtils.isEmpty(salesVolume)) {
				try {
					salesVolumeNum = new BigDecimal(salesVolume);
				} catch (Exception e) {
					msg = "第" + i + "行." + "行,门店数量格式有误;";
					ExceptionUtil.throwServiceException(msg);
				}
			}

			// 备注
			cell = sheet.getCell(34, i);
			memo = cell.getContents() == null ? "" : cell.getContents().trim();

			cell = sheet.getCell(35, i);
			lastDesignDateStr = cell.getContents();
			Date lastDesignDate = null;
			if (lastDesignDateStr != "" && !isValidDate(lastDesignDateStr)) {
				msg = "第" + i + "行." + "最后设计日期格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!StringUtils.isEmpty(lastDesignDateStr)) {
				lastDesignDate = sdf.parse(lastDesignDateStr);
			}

			cell = sheet.getCell(36, i);
			lastDesignStatus = cell.getContents() == null ? "" : cell.getContents().trim();
			// 激活时间
			cell = sheet.getCell(37, i);
			activationTimeStr = cell.getContents();
			Date activationTime = null;
			if (activationTimeStr != "" && !isValidDate(activationTimeStr)) {
				msg = "第" + i + "行." + "激活时间格式有误，格式应为yyyy-MM-dd";
				ExceptionUtil.throwServiceException(msg);
			}
			if (!StringUtils.isEmpty(activationTimeStr)) {
				activationTime = sdf.parse(activationTimeStr);
			}
			this.createShopInfo(authorizationCode, store, region, areaFullName, franchisee, distributorName, joinDate,
					distributorPhone, administrativeRank, acreage, address, status, statusNote, positionType, type,
					lastInspectionResult, increaseArchivesCode, openDate, decreaseArchivesCode, shutDownMenu,
					closingTime, viVersion, businessForm, shopSign, salesPlatform, department, inclusiveBrand,
					belongBrand, salesChannel, salesChannelNote, salesVolumeNum, memo, lastDesignDate, lastDesignStatus,
					activationTime, town);

		}

		return msg;
	}

	private void createShopInfo(String authorizationCode, Store store, String region, String areaFullName,
			String franchisee, String distributorName, Date joinDate, String distributorPhone,
			String administrativeRank, String acreage, String address, String shopStatus, String statusNote,
			String positionType, String type, String lastInspectionResult, String increaseArchivesCode, Date openDate,
			String decreaseArchivesCode, String shutDownMenu, Date closingTime, String viVersion, String businessForm,
			String shopSign, String salesPlatform, String department, String inclusiveBrand, String belongBrand,
			String salesChannel, String salesChannelNote, BigDecimal salesVolume, String memo, Date lastDesignDate,
			String lastDesignStatus, Date activationTime, String town) {

		ShopInfo shopInfo = new ShopInfo();
		BigDecimal ac = new BigDecimal((acreage == null || acreage == "") ? "0" : acreage);
		String sn = Sequence.getInstance().getSequence(null);
		shopInfo.setSn(sn);
		shopInfo.setStoreMember(storeMemberService.getCurrent());
		shopInfo.setAuthorizationCode(authorizationCode);
		shopInfo.setShopStatus(shopStatus);
		// shopInfo.setTel(tel);
		shopInfo.setDistributorName(distributorName);
		shopInfo.setDistributorPhone(distributorPhone);
		shopInfo.setOpenDate(openDate);
		shopInfo.setJoinDate(joinDate);
		List<Filter> filters = new ArrayList<Filter>();
		if (areaFullName != null) {
			filters.add(Filter.eq("fullName", areaFullName));
			shopInfo.setArea(areaBaseService.find(filters));
		}
		shopInfo.setTown(town);
		shopInfo.setAddress(address);
		shopInfo.setSalesPlatform(salesPlatform);
		shopInfo.setStatusNote(statusNote);
		shopInfo.setIncreaseArchivesCode(increaseArchivesCode);
		shopInfo.setType(type);
		shopInfo.setInclusiveBrand(inclusiveBrand);
		shopInfo.setAcreage(ac);
		// shopInfo.setContactPhone(contactPhone);
		shopInfo.setDepartment(department);
		shopInfo.setBelongBrand(belongBrand);
		shopInfo.setAdministrativeRank(administrativeRank);
		shopInfo.setPositionType(positionType);
		// shopInfo.setActivationType(activationType);
		shopInfo.setActivationTime(activationTime);
		shopInfo.setLastDesignDate(lastDesignDate);
		shopInfo.setLastDesignStatus(lastDesignStatus);
		shopInfo.setLastInspectionResult(lastInspectionResult);
		shopInfo.setRegion(region);
		shopInfo.setFranchisee(franchisee);
		// shopInfo.setLastCheckTime(lastCheckTime);
		shopInfo.setDecreaseArchivesCode(decreaseArchivesCode);
		shopInfo.setShutDownMenu(shutDownMenu);
		shopInfo.setClosingTime(closingTime);
		shopInfo.setViVersion(viVersion);
		shopInfo.setBusinessForm(businessForm);
		shopInfo.setShopSign(shopSign);
		shopInfo.setSalesChannel(salesChannel);
		shopInfo.setSalesChannelNote(salesChannelNote);
		shopInfo.setSalesVolume(salesVolume);
		shopInfo.setMemo(memo);
		shopInfo.setStore(store);
		shopInfo.setCompanyInfoId(WebUtils.getCurrentCompanyInfoId());
		this.save(shopInfo);
	}

	private boolean isValidDate(String str) {
		boolean convertSuccess = true;
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			format.setLenient(false);
			format.parse(str);
		} catch (ParseException e) {
			convertSuccess = false;
		}
		return convertSuccess;
	}

	@Override
	public void checkWf(Long shopInfoId, Long objConfId) {
		ShopInfo shopInfo = find(shopInfoId);
		// 创建流程实例
		WfObjConfigLine wfObjConfigLine = wfObjConfigLineBaseService.find(objConfId);
		if (wfObjConfigLine == null) {
			ExceptionUtil.throwServiceException("请选择审核流程");
		}
		StoreMember storeMember = storeMemberService.getCurrent();
		shopInfo.setCheckStoreMember(storeMember);
		shopInfo.setByObjConfig(wfObjConfigLine); // 设置流程配置
		wfBaseService.createwf(storeMemberService.getCurrent(), null, shopInfo.getSn(), shopInfo.getWfTempId(),
				shopInfo.getObjTypeId(), shopInfo.getId());
		WfTemp wfTemp = wfTempBaseService.find(wfObjConfigLine.getWfTempId());
		List<String> sns = new ArrayList<String>();
		sns.add(shopInfo.getSn());
		orderFullLinkService.addFullLink(1, sns, shopInfo.getSn(),
				ConvertUtil.convertI18nMsg("18701", new Object[] { wfTemp.getWfTempName() }), null);

	}

	@Override
	public void agreeBack(Wf wf) {

		if (wf.getStat().intValue() == 2) {
			ShopInfo shopInfo = find(wf.getObjId());
			shopInfo.setCheckDate(new Date());
			update(shopInfo);

		}
	}

	@Override
	public List<Map<String, Object>> findShopInspection(ShopInfo shopInfo) {

		return shopInfoDao.findShopInspection(shopInfo);
	}

	@Override
	public List<Map<String, Object>> findShopDesign(ShopInfo shopInfo) {
		return shopInfoDao.findShopDesign(shopInfo);
	}

	// 统计门店数量
	@Override
	public Integer countShopInfo(Long id) {
		return shopInfoDao.countShopInfo(id);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see net.shopxx.member.service.ShopInfoService#count(java.lang.String,
	 * java.lang.String, net.shopxx.base.core.Pageable, java.lang.Integer,
	 * java.lang.Integer)
	 */
	@Override
	public Integer count(String sn, String distributorName, Pageable pageable, Integer page, Integer size) {
		return shopInfoDao.count(sn, distributorName, pageable, page, size);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * net.shopxx.member.service.ShopInfoService#findItemList(java.lang.String,
	 * java.lang.String, java.lang.Long[], java.lang.Integer, java.lang.Integer)
	 */
	@Override
	public List<Map<String, Object>> findItemList(String sn, String distributorName, Long[] ids, Integer page,
			Integer size) {
		return shopInfoDao.findItemList(sn, distributorName, ids, page, size);
	}

	@Override
	public List<Map<String, Object>> findListMap() {
		return shopInfoDao.findListMap();
	}

	@Override
	public List<Map<String, Object>> findShopAcceptance(ShopInfo shopInfo) {
		return shopInfoDao.findShopAcceptance(shopInfo);
	}

	@Override
	public List<Map<String, Object>> findRestructuring(ShopInfo shopInfo) {
		return shopInfoDao.findRestructuring(shopInfo);
	}
	
	/**
	 *	创建门店
	 *	@param store 客户
	 *  @param saleOrg 机构
	 *  @param area 地区
	 *  @param storeMember 创建人
	 *  @param positionType 门店位置类型
	 *  @param companyNature 公司性质
	 *  @param shopOwnership 门店所有
	 *  @param businessCategory 经营品类
	 *  @param address 门店详细地址
	 *  @param type 门店类型
	 *  @param administrativeRank 城市行政等级
	 *  @param salesPlatform 门店所属销售平台
	 *  @param belongBrand 门店所属品牌
	 *  @param viVersion VI版本
	 *  @param department 所属部门
	 *  @param inclusiveBrand 门店所含品牌
	 *  @param salesChannel 门店销售渠道
	 *  @param authorizationCode 门店授权编号
	 *  @param increaseArchivesCode 新增档案编号
	 *  @param shopName 门店名称
	 *  @param partnerName 合伙人名称
	 *  @param partnerPhone 合伙人电话
	 *  @param shopStatus 门店状态
	 *  @param decorationStatus 装修状态
	 *  @param deviseStatus 设计状态
	 *  @param openDate 建店日期
	 *  @param acreage 门店面积
	 *  @param monthlyRent 月租金
	 *  @param is 是否参与门店设计
	 */
	@Transactional(readOnly = true)
	public ShopInfo createShopInfo(Store store,SaleOrg saleOrg,Area area,StoreMember storeMember,
			String positionType,String companyNature,String shopOwnership,
			String businessCategory,String address,String type,
			String administrativeRank,String salesPlatform,String belongBrand,
			String viVersion,String department,String inclusiveBrand,
			String salesChannel,String authorizationCode,String increaseArchivesCode,
			String shopName,String partnerName,String partnerPhone,
			String shopStatus,String decorationStatus,String deviseStatus,
			Date openDate,BigDecimal acreage,BigDecimal monthlyRent,
			Boolean is){
		if (store == null) {
			ExceptionUtil.throwServiceException("门店没有绑定客户！");
		}
		ShopInfo si = new ShopInfo();
		si.setSn(Sequence.getInstance().getSequence(null));
		si.setSaleOrg(saleOrg);
		si.setStore(store);
		si.setDistributorName(store.getName());
		si.setDistributorPhone(store.getHeadPhone());
		si.setTel(store.getFixedNumber());
		si.setFranchisee(store.getFranchisee());
		si.setJoinDate(store.getActiveDate());
		si.setOpenDate(openDate);
		si.setAcreage(acreage);
		si.setPositionType(positionType);
		si.setCompanyNature(companyNature);
		si.setShopOwnership(shopOwnership);
		si.setBusinessCategory(businessCategory);
		si.setArea(area);
		si.setAddress(address);
		si.setType(type);
		si.setAdministrativeRank(administrativeRank);
		si.setSalesPlatform(salesPlatform);
		si.setBelongBrand(belongBrand);
		si.setViVersion(viVersion);
		si.setDepartment(department);
		si.setInclusiveBrand(inclusiveBrand);
		si.setSalesChannel(salesChannel);
		si.setAuthorizationCode(authorizationCode);
		si.setIncreaseArchivesCode(increaseArchivesCode);
		si.setMonthlyRent(monthlyRent);
		si.setShopName(shopName);
		si.setPartnerName(partnerName);
		si.setPartnerPhone(partnerPhone);
		si.setStoreMember(storeMember);
		si.setDeviseStatus(deviseStatus);
		si.setShopStatus(shopStatus);
		si.setDecorationStatus(decorationStatus);
		save(si);
		//创建门店全链路
		orderFullLinkService.addFullLink(102, null, si.getSn(), "创建门店档案信息", null);
		return si;
	}

	@Override
	public void callbackShop(Shop shop) {
		if (shop == null) {
			ExceptionUtil.throwServiceException("门店不能为空！");
		}
		if (shop.getStore() == null) {
			ExceptionUtil.throwServiceException("门店没有绑定客户！");
		}
		//是否需要设计
		String[] status = Judgment(shop.getIsParticipateDesign());
		
		Store s = shop.getStore();
		ShopInfo shopInfo = createShopInfo(s,shop.getSaleOrg(),shop.getNewShopArea(),shop.getStoreMember(),
				shop.getShopLocation(),shop.getCompanyNature(),shop.getShopOwnership(),
				shop.getBusinessCategory(),shop.getNewShopAddress(),shop.getShopType(),
				shop.getAdministrativeRank(),s.getSalesPlatform().getName(),shop.getBelongBrand(),
				shop.getViVersion(),shop.getDepartment(),shop.getShopSign(),
				shop.getSalesChannel(),shop.getShopAuthorizationCodes(),shop.getArchivesCodes(),
				shop.getShopName(),shop.getPartnerName(),shop.getPartnerPhone(),
				status[0],status[1],status[2],shop.getCreateShopDate(),shop.getShopAcreage(),shop.getMonthlyRent(),
				null);
		shopAddedDao.init(shopInfo.getSn(), shopInfo.getShopName(), shopInfo.getArea(), shopInfo.getAddress(),
				shopInfo.getStoreMember(), shopInfo.getId(), shopInfo.getSaleOrg().getId());
	}

	@Override
	@Transactional(readOnly = true)
	public void updateShopInfo(ShopAlteration sa) {
		//关闭门店
		ShopInfo shopInfo = sa.getInfoShop();
		shopInfo.setShopStatus("关闭");
		update(shopInfo);
		orderFullLinkService.addFullLink(102, null, shopInfo.getSn(), "门店被关闭", null);
		//是否需要设计
		String[] status = Judgment(sa.getIsDesign());
		//新建门店
		createShopInfo(sa.getStore(), sa.getSaleOrg(), sa.getNewShopArea(),sa.getStoreMember(),
				sa.getPositionType(), sa.getCompanyNature(), sa.getShopOwnership(),
				sa.getBusinessCategory(),sa.getAddress(), sa.getType(),
				sa.getAdministrativeRank(), sa.getStore().getSalesPlatform().getName(), sa.getBelongBrand(),
				sa.getViVersion(), sa.getDepartment(), sa.getShopSign(),
				sa.getSalesChannel(), sa.getAuthorizationCode(), sa.getIncreaseArchivesCode(),
				sa.getShopName(), null, null,
				status[0], status[1],status[2],
				sa.getOpenDate(), sa.getAcreage(), sa.getMonthlyRent(),null);
		shopAlterationDao.update(sa.getInfoShop());
	}
	
	public String[] Judgment(Boolean is){
		String deviseStatus = null;//设计状态
		String shopStatus = null;//门店状态
		String decorationStatus = null;//装修状态
		if(is){
			shopStatus = "开店中";
			deviseStatus = "未设计";
			decorationStatus = "未装修";
		}else{
			shopStatus = "正在营业";
			deviseStatus = "无需设计";
			decorationStatus = "已装修";
		}
		return new String[]{shopStatus,decorationStatus,deviseStatus};
	}

	@Override
	@Transactional(readOnly = true)
	public void updateShop(ShopInfo shopInfo) {
		shopInfoDao.updetaShop(shopInfo);
	}

	@Override
	@Transactional(readOnly = true)
	public void updetaShopImages(ShopInfo shopInfo) {
		shopInfoDao.updetaShopImages(shopInfo);
	}

	@Override
	@Transactional
	public void decreaseShop(ShopDecrease sd) {
		ShopInfo si = sd.getShopInfo();
		si.setShutDownMenu(sd.getCloseReason());
		si.setClosingTime(sd.getDecreaseTime());
		si.setDecreaseArchivesCode(sd.getDecreaseArchivesCode());
		si.setShopStatus("关闭");
		update(si);
		orderFullLinkService.addFullLink(102, null, si.getSn(), "门店被关闭", null);	
	}

	@Override
	@Transactional
	public void restructuringStatus(Restructuring rt) {
		ShopInfo si = rt.getShopInfo();
		if(rt.getStatus()==1){
			si.setShopStatus("停业整顿");
		}
		update(si);
		orderFullLinkService.addFullLink(102, null, si.getSn(), "门店被停业整顿", null);	
	}


	@Override
	@Transactional
	public void updateDeviseStatus(ShopDevise shopDevise) {
		ShopInfo si = shopDevise.getShopInfo();
		if(shopDevise.getBillsStatus()==1){
			si.setDeviseStatus("已设计");
		}else if(shopDevise.getBillsStatus()==0){
			si.setDeviseStatus("未设计");
		}else if(shopDevise.getBillsStatus()==3){
			si.setDeviseStatus("设计中");				
		}
		update(si);
		if(shopDevise.getBillsStatus()==1){
			orderFullLinkService.addFullLink(102, null, si.getSn(), "门店设计完成", null);			
		}
	}

	@Override
	public void updateDecorationStatus(AcceptanceReimburse acceptanceReimburse) {
//		4.装修验收及报销申请渠道人员审核通过，门店装修变更为正在营业，装修状态变更为已装修，
//		流程审核通过后装修状态变更为已验收
//		5.门店装修验收流程审核通过后，装修状态变更为已装修。
		ShopInfo si = acceptanceReimburse.getShopInfo();
		if(acceptanceReimburse.getType()==1){//验收及报销
			if(acceptanceReimburse.getStatus()==1){//已提交
				si.setShopStatus("正在营业");
				si.setDecorationStatus("已验收");
			}			
		}else if(acceptanceReimburse.getType()==2){//验收不报销
			if(acceptanceReimburse.getStatus()==1){//已提交
				si.setDecorationStatus("已装修");
			}
		}
		update(si);
		if(acceptanceReimburse.getType()==1&&acceptanceReimburse.getStatus()==1){
			orderFullLinkService.addFullLink(102, null, si.getSn(), "门店已验收", null);					
		}else if(acceptanceReimburse.getType()==2&&acceptanceReimburse.getStatus()==1){
			orderFullLinkService.addFullLink(102, null, si.getSn(), "门店已装修", null);	
		}
	}

	@Override
	public List<Map<String, Object>> findDevise(String id,String reimburses) {
		return shopInfoDao.findDevise(id,reimburses);
	}

	@Override
	public List<Map<String, Object>> findShopList(String sn, String distributorName, Long[] ids, Integer page,
			Integer size) {
		return shopInfoDao.findShopList(sn,distributorName,ids,page,size);
	}

	@Override
	public List<Map<String, Object>> findListBy(Long id) {
		return shopInfoDao.findListBy(id);
	}

	@Override
	public Map<String, Object> findById(Long shopInfoId) {
		return shopInfoDao.findById(shopInfoId);
	}

    @Override
    public List<Map<String, Object>> findStoreByShopInfo(Long storeId) {
        return shopInfoDao.findStoreByShopInfo(storeId);
    }

    @Override
    public void restructuring(List<ShopInfo> shopInfo, String str, Date reorganizeDate, ActWf wf) {
        for (ShopInfo si : shopInfo) {
            // 门店资料更新为停业整顿
           // restructuringStatus(null, si, str, wf,reorganizeDate);
        }
    }

    @Override
    public void decrease(List<ShopInfo> shopInfo, String str,ActWf wf) {
        for (ShopInfo si : shopInfo) {
            // 门店资料更新为停业整顿
           // decreaseShop(null, si, str, wf);
        }
    }

}
