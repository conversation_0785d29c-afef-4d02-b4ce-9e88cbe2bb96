package net.shopxx.shop.service.impl;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.intf.dao.Oa_WfModelDao;
import net.shopxx.intf.service.OaToWfService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.dao.TortDao;
import net.shopxx.shop.entity.ComplaintAttach;
import net.shopxx.shop.entity.NoticeAttach;
import net.shopxx.shop.entity.Tort;
import net.shopxx.shop.entity.TortAttach;
import net.shopxx.shop.service.TortService;
import net.shopxx.util.SnUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


@Service("tortServiceImpl")
public class TortServiceImpl extends ActWfBillServiceImpl<Tort> implements TortService {

    @Resource(name = "tortDao")
    private TortDao tortDao;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "oaToWfServiceImpl")
    private OaToWfService oaToWfService;
    @Resource(name = "oa_WfModelDao")
    private Oa_WfModelDao oa_WfModelDao;

    @Override
    public void saveTort(Tort tort) {
        String sn = SnUtil.generateSn();
        tort.setSn(sn);
        List<TortAttach> tortAttachs = tort.getTortAttachs();
        for (Iterator<TortAttach> iterator = tortAttachs.iterator(); iterator.hasNext(); ) {
        	TortAttach tortAttach = iterator.next();
            if (tortAttach == null || tortAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (tortAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            tortAttach.setFileName(tortAttach.getName()
                    + "."
                    + tortAttach.getSuffix());
            tortAttach.setTort(tort);
        }
        tort.setTortAttachs(tortAttachs);
        //投诉书
        List<ComplaintAttach> complaintAttachs = tort.getComplaintAttachs();
        for (Iterator<ComplaintAttach> iterator = complaintAttachs.iterator(); iterator.hasNext(); ) {
            ComplaintAttach complaintAttach = iterator.next();
            if (complaintAttach == null || complaintAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (complaintAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            complaintAttach.setFileName(complaintAttach.getName()
                    + "."
                    + complaintAttach.getSuffix());
            complaintAttach.setTort(tort);
        }
        tort.setComplaintAttachs(complaintAttachs);
        //告知函
        List<NoticeAttach> noticeAttachs = tort.getNoticeAttachs();
        for (Iterator<NoticeAttach> iterator = noticeAttachs.iterator(); iterator.hasNext(); ) {
            NoticeAttach noticeAttach = iterator.next();
            if (noticeAttach == null || noticeAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (noticeAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            noticeAttach.setFileName(noticeAttach.getName()
                    + "."
                    + noticeAttach.getSuffix());
            noticeAttach.setTort(tort);
        }
        tort.setNoticeAttachs(noticeAttachs);
        this.save(tort);
    }

    @Override
    public void updateTort(Tort tort) {
        List<TortAttach> tortAttachs = tort.getTortAttachs();
        for (Iterator<TortAttach> iterator = tortAttachs.iterator(); iterator.hasNext(); ) {
        	TortAttach tortAttach = iterator.next();
            if (tortAttach == null || tortAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (tortAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            tortAttach.setFileName(tortAttach.getName()
                    + "."
                    + tortAttach.getSuffix());
            tortAttach.setTort(tort);
        }
        tort.setTortAttachs(tortAttachs);
        //投诉书
        List<ComplaintAttach> complaintAttachs = tort.getComplaintAttachs();
        for (Iterator<ComplaintAttach> iterator = complaintAttachs.iterator(); iterator.hasNext(); ) {
            ComplaintAttach complaintAttach = iterator.next();
            if (complaintAttach == null || complaintAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (complaintAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            complaintAttach.setFileName(complaintAttach.getName()
                    + "."
                    + complaintAttach.getSuffix());
            complaintAttach.setTort(tort);
        }
        tort.setComplaintAttachs(complaintAttachs);
        //告知函
        List<NoticeAttach> noticeAttachs = tort.getNoticeAttachs();
        for (Iterator<NoticeAttach> iterator = noticeAttachs.iterator(); iterator.hasNext(); ) {
            NoticeAttach noticeAttach = iterator.next();
            if (noticeAttach == null || noticeAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (noticeAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            noticeAttach.setFileName(noticeAttach.getName()
                    + "."
                    + noticeAttach.getSuffix());
            noticeAttach.setTort(tort);
        }
        tort.setNoticeAttachs(noticeAttachs);
        this.update(tort);
    }

    @Override
    public List<Map<String, Object>> findComplaintAttach(Long id) {
        return tortDao.findComplaintAttach(id);
    }

    @Override
    public List<Map<String, Object>> findNoticeAttach(Long id) {
        return tortDao.findNoticeAttach(id);
    }

    @Override
    public Page<Map<String, Object>> findPage(Tort t, List<Object> param, Pageable pageable) {
        return tortDao.findPage(t,param, pageable);
    }

    @Override
    public void saveform(Tort tort, Integer type) {
        Tort t = find(tort.getId());
        if(getCurrTaskByWf(getWfByWfId(t.getWfId())) != null){
        	String nodeName = getCurrTaskByWf(getWfByWfId(t.getWfId())).getName();
        	wfPass(tort, nodeName);        	
        }
        //tpye 1省长 2渠道部 3销售中心 4事业部
        if (type == 1) {
            t.setSzyj(tort.getSzyj());
        }
        if (type == 2) {
            //投诉书
            List<ComplaintAttach> complaintAttachs = tort.getComplaintAttachs();
            Iterator<ComplaintAttach> complaintIterator = complaintAttachs.iterator();
            while (complaintIterator.hasNext()) {
                ComplaintAttach complaintAttach = complaintIterator.next();
                if (complaintAttach == null || complaintAttach.getUrl() == null) {
                    complaintIterator.remove();
                    continue;
                }
                if (complaintAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                complaintAttach.setFileName(complaintAttach.getName() + "." + complaintAttach.getSuffix());
                complaintAttach.setTort(t);
                complaintAttach.setStoreMember(storeMemberService.getCurrent());
            }
            t.getComplaintAttachs().clear();
            t.getComplaintAttachs().addAll(complaintAttachs);
            //告知函
            List<NoticeAttach> noticeAttachs = tort.getNoticeAttachs();
            Iterator<NoticeAttach> noticeIterator = noticeAttachs.iterator();
            while (noticeIterator.hasNext()) {
                NoticeAttach noticeAttach = noticeIterator.next();
                if (noticeAttach == null || noticeAttach.getUrl() == null) {
                    noticeIterator.remove();
                    continue;
                }
                if (noticeAttach.getName() == null) {
                    ExceptionUtil.throwServiceException("附件名不能为空");
                }
                noticeAttach.setFileName(noticeAttach.getName() + "." + noticeAttach.getSuffix());
                noticeAttach.setTort(t);
                noticeAttach.setStoreMember(storeMemberService.getCurrent());
            }
            t.getNoticeAttachs().clear();
            t.getNoticeAttachs().addAll(noticeAttachs);
        }
        if(type == 99){
        	if(ConvertUtil.isEmpty(tort.getCode())){
                ExceptionUtil.throwServiceException("请填写文件编号");
            }
            if(ConvertUtil.isEmpty(tort.getMx())){
                ExceptionUtil.throwServiceException("请填写处理明细");
            }
            if(ConvertUtil.isEmpty(tort.getMemo())){
                ExceptionUtil.throwServiceException("请填写备注");
            }
            if(ConvertUtil.isEmpty(tort.getLettersTime())){
                ExceptionUtil.throwServiceException("请填写函件发出时间");
            }
            if(ConvertUtil.isEmpty(tort.getTrackingNumber())){
                ExceptionUtil.throwServiceException("请填写快递单号");
            }
            if(ConvertUtil.isEmpty(tort.getResultOfHandling())){
                ExceptionUtil.throwServiceException("请填写处理结果");
            }
        	t.setRecipients(tort.getRecipients());
        	t.setRecipientType(tort.getRecipientType());
        	t.setContactWay(tort.getContactWay());
        	t.setExpress(tort.getExpress());
        	t.setExpressAddress(tort.getExpressAddress());
        	t.setTorthp(tort.getTorthp());
        	t.setAicName(tort.getAicName());
        	t.setAuthorizationName(tort.getAuthorizationName());
        	t.setCard(tort.getCard());
        	t.setBusinessLicense(tort.getBusinessLicense());
        	t.setCode(tort.getCode());
            t.setMx(tort.getMx());
            t.setMemo(tort.getMemo());
            t.setLettersTime(tort.getLettersTime());
            t.setTrackingNumber(tort.getTrackingNumber());
            t.setResultOfHandling(tort.getResultOfHandling());
        }
        this.update(t);
    }

    /**
     * 创建流程实例
     *
     * @param id        单据id
     * @param modelId
     * @param objTypeId
     */
    @Override
    @Transactional
    public void createWf(Long id, String modelId, Long objTypeId) {

        Tort tort = find(id);
        
        StoreMember storeMember = storeMemberService.getCurrent();


		createWf(tort.getSn(),
				String.valueOf(storeMember.getId()),
				new Long[]{tort.getStore().getSaleOrg().getId()},
				tort.getStore().getId(),
				modelId,
				objTypeId,
				id,
				WebUtils.getCurrentCompanyInfoId(),
                true);
		tort.setStatus(3);
		update(tort);
    }

    /** 流程开始回调 */
    @Transactional
    public void startBack(ActWf wf) {
        List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
        for (String receiver : user) {
            oaToWfService.receiveRequestInfoByJson(wf,
                    new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
        }
    };

    /** 通过节点任务回调 发送待办 */
    @Transactional
    public void agreeBack(ActWf wf) {
        if (getCurrTaskByWf(wf) != null) {
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务回调 发送待办 */
    @Transactional
    public void rejectBack(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            // oa触发OA接口推送待办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.receiveTodoRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 通过节点任务前回调 发送待办转以办 */
    @Transactional
    public void agreePre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            // 通过节点校验
            wfPass(find(wf.getObjId()),getCurrTaskByWf(wf).getName());
            // oa触发OA接口推送待办转以办
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 驳回节点任务前回调 发送待办转以办 */
    @Transactional
    public void rejectPre(ActWf wf) {
        if(getCurrTaskByWf(wf)!=null){
            List<String> user = getTaskUsers(getCurrTaskByWf(wf).getId());
            for (String receiver : user) {
                oaToWfService.processDoneRequestByJson(wf,
                        new String[] { getCurrTaskByWf(wf).getName(), findUserName(receiver) });
            }
        }
    };

    /** 流程中断前回调 1 */
    @Transactional
    public void interruptPre(ActWf wf) {
        oaToWfService.deleteRequestInfoByJson(wf);
    };

    @Override
    public void interruptBack(ActWf wf) {
        super.interruptBack(wf);
        Tort tort = find(wf.getObjId());
        tort.setStatus(0);//保存状态
        update(tort);
    }
    
    @Override
    public void endBack(ActWf wf) {
    	super.endBack(wf);
        oaToWfService.processOverRequestByJson(wf,
                new String[] { "", oa_WfModelDao.findWfStartUser(wf.getProcInstId()) });
    	Tort tort = find(wf.getObjId());
    	tort.setStatus(1);
        update(tort);
    }

    public String findUserName(String id) {
        StoreMember storeMember = storeMemberService.find(Long.parseLong(id));
        if (storeMember == null) {
            return "";
        } else {
            return storeMember.getUsername();
        }
    }


    /**
     * 节点必填校验
     * @param tort
     * @param nodeName
     */
    public void wfPass(Tort tort, String nodeName){
    	
        if(nodeName.contains("省长审核")){
            if(ConvertUtil.isEmpty(tort.getSzyj())){
                ExceptionUtil.throwServiceException("请填写省长意见");
            }
        }

        if(nodeName.contains("渠道专员审核")){
        	 

        }

    }

	@Override
	public List<Map<String, Object>> findTortAttach(Long id, Integer type){
		return tortDao.findTortAttach(id, type);
	}

	@Override
	public Integer count(Tort t, List<Object> param) {
		return tortDao.count(t, param);
	}

	@Override
	public List<Map<String, Object>> findList(Tort t, List<Object> param, Pageable pageable, Integer page,
                                              Integer size) {
		return tortDao.findList(t, param, pageable, page, size);
	}

}
