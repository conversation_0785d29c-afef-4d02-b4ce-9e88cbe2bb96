package net.shopxx.shop.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.dao.StoreBaseDao;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.service.ShopStoreService;

@Service("shopStoreServiceImpl")
public class ShopStoreServiceImpl extends BaseServiceImpl<Store> implements ShopStoreService{
	
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storeBaseDao")
	private StoreBaseDao storeBaseDao;
	
	
	@Override
	public Store findStore() {
		StoreMember storeMember = storeMemberBaseService.getCurrent();
		Long member = storeMember.getMember().getId();
		Store store = null;
		String sql = "select s.* from xx_store s left join xx_store_member sm on sm.store=s.id where s.type!=0"
				+ " and s.company_info_id = " + WebUtils.getCurrentCompanyInfoId() + " and sm.member = " + member
				+ " order by s.create_date desc";
		List<Map<String, Object>> map = storeBaseDao.getNativeDao().findListMap(sql, null, 0);
		if (map != null && map.size() > 0) {
			Long id = Long.parseLong(map.get(0).get("id").toString());
			store = find(id);
		}
		return store;
	}

}
