package net.shopxx.shop.service.impl;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.shop.dao.ShopProductCategoryDao;
import net.shopxx.product.entity.ProductCategory;
import net.shopxx.shop.entity.ShopProductAndProductCategory;
import net.shopxx.shop.entity.ShopProductCategory;
import net.shopxx.shop.service.ShopProductAndProductCategoryService;
import net.shopxx.shop.service.ShopProductCategoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service - 门店上样分类
 */
@Service("shopProductCategoryServiceImpl")
public class ShopProductCategoryServiceImpl extends
		BaseServiceImpl<ShopProductCategory> implements ShopProductCategoryService {

	@Resource(name = "shopProductCategoryDao")
	private ShopProductCategoryDao shopProductCategoryDao;
	@Resource(name = "shopProductAndProductCategoryServiceImpl")
	private ShopProductAndProductCategoryService shopProductAndProductCategoryService;



	@Override
	public List<Map<String, Object>> findList(String sn, String name, Boolean isEnabled) {
		return shopProductCategoryDao.findList(sn,name,isEnabled);
	}

	@Override
	public List<Map<String, Object>> findChildren(Long parentId, String name) {
		return null;
	}

	@Override
	public List<ShopProductCategory> findChildren(ShopProductCategory shopProductCategory) {
		return null;
	}

	@Override
	public List<ShopProductCategory> findChildren(String sn, String name, Boolean isEnabled, ShopProductCategory shopProductCategory, int count) {
		return null;
	}

	@Override
	public void deleteProductCategory(ShopProductCategory shopProductCategory) {

	}

	@Override
	public String importFromExcel(MultipartFile file, Integer type) throws Exception {
		return null;
	}


	/**
	 * 保存
	 * @param shopProductCategory
	 * @param productCategoryIds
	 */
	@Override
	@Transactional
	public void saveShopProductCategory(ShopProductCategory shopProductCategory, Long[] productCategoryIds) {
		save(shopProductCategory);
	if(productCategoryIds != null){
			for(Long productCategoryId :productCategoryIds){
				ShopProductAndProductCategory shopProductAndProductCategory = new ShopProductAndProductCategory();
				ProductCategory productCategory = new ProductCategory();
				productCategory.setId(productCategoryId);
				shopProductAndProductCategory.setProductCategory(productCategory);
				shopProductAndProductCategory.setShopProductCategory(shopProductCategory);
				shopProductAndProductCategoryService.save(shopProductAndProductCategory);
			}
		}
	}

	@Override
	@Transactional
	public void updateShopProductCategory(ShopProductCategory newShopProductCategory, Long[] productCategoryIds) {
		ShopProductCategory shopProductCategory = find(newShopProductCategory.getId());
		shopProductCategory.setIsEnabled(newShopProductCategory.getIsEnabled());
		shopProductCategory.setName(newShopProductCategory.getName());
		shopProductCategory.setOrder(newShopProductCategory.getOrder());
		shopProductCategory.setSn(newShopProductCategory.getSn());
		update(shopProductCategory);

		//删除关联的分类
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("shopProductCategory",shopProductCategory));
		List<ShopProductAndProductCategory> list = shopProductAndProductCategoryService.findList(null, filters, null);
		for(ShopProductAndProductCategory shopProductAndProductCategory : list){
			shopProductAndProductCategoryService.delete(shopProductAndProductCategory.getId());
		}

		//重新添加分类
		if(productCategoryIds != null){
			for(Long productCategoryId :productCategoryIds){
				ShopProductAndProductCategory shopProductAndProductCategory = new ShopProductAndProductCategory();
				ProductCategory productCategory = new ProductCategory();
				productCategory.setId(productCategoryId);
				shopProductAndProductCategory.setProductCategory(productCategory);
				shopProductAndProductCategory.setShopProductCategory(shopProductCategory);
				shopProductAndProductCategoryService.save(shopProductAndProductCategory);
			}
		}


	}
}