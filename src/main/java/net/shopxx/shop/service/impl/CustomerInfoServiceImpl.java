package net.shopxx.shop.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.shop.dao.CustomerInfoDao;
import net.shopxx.shop.service.CustomerInfoService;

@Service("customerInfoServiceImpl")
public class CustomerInfoServiceImpl implements CustomerInfoService {
    
    @Resource(name = "customerInfoDao")
    private CustomerInfoDao customerInfoDao;

    @Override
    public Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable) {
        return customerInfoDao.findPage(param, pageable);
    }

}
