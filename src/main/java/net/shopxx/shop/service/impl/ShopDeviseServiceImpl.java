package net.shopxx.shop.service.impl;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.dao.ShopDeviseDao;
import net.shopxx.shop.entity.ShopDevise;
import net.shopxx.shop.entity.ShopDeviseAttach;
import net.shopxx.shop.service.ShopDeviseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;

@Service("shopDeviseServiceImpl")
public class ShopDeviseServiceImpl extends ActWfBillServiceImpl<ShopDevise> implements ShopDeviseService {

	@Resource(name = "shopDeviseDao")
	private ShopDeviseDao shopDeviseDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;

	@Override
	@Transactional
	public void saveShopDevise(ShopDevise shopDevise) {
		// 单号
		shopDevise.setSn(SnUtil.geShopDeviseSn());

		// 经销商提交申请附件
		List<ShopDeviseAttach> dealersAttachs = shopDevise.getDealersAttachs();
		Iterator<ShopDeviseAttach> dealersIterator = dealersAttachs.iterator();
		while (dealersIterator.hasNext()) {
			ShopDeviseAttach dealersAttach = dealersIterator.next();
			if (dealersAttach == null || dealersAttach.getUrl() == null) {
				dealersIterator.remove();
				continue;
			}
			if (dealersAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			dealersAttach.setFileName(dealersAttach.getName() + "." + dealersAttach.getSuffix());
			dealersAttach.setShopDevise(shopDevise);
			dealersAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setDealersAttachs(dealersAttachs);

		List<ShopDeviseAttach> storeContractAttachs = shopDevise.getStoreContractAttachs();
		Iterator<ShopDeviseAttach> storeContractIterator = storeContractAttachs.iterator();
		while (storeContractIterator.hasNext()) {
			ShopDeviseAttach storeContractAttach = storeContractIterator.next();
			if (storeContractAttach == null || storeContractAttach.getUrl() == null) {
				storeContractIterator.remove();
				continue;
			}
			if (storeContractAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			storeContractAttach.setFileName(storeContractAttach.getName() + "." + storeContractAttach.getSuffix());
			storeContractAttach.setShopDevise(shopDevise);
			storeContractAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setStoreContractAttachs(storeContractAttachs);

		List<ShopDeviseAttach> storePicturesAttachs = shopDevise.getStorePicturesAttachs();
		Iterator<ShopDeviseAttach> storePicturesIterator = storePicturesAttachs.iterator();
		while (storePicturesIterator.hasNext()) {
			ShopDeviseAttach storePicturesAttach = storePicturesIterator.next();
			if (storePicturesAttach == null || storePicturesAttach.getUrl() == null) {
				storePicturesIterator.remove();
				continue;
			}
			if (storePicturesAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			storePicturesAttach.setFileName(storePicturesAttach.getName() + "." + storePicturesAttach.getSuffix());
			storePicturesAttach.setShopDevise(shopDevise);
			storePicturesAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setStorePicturesAttachs(storePicturesAttachs);

		// 区域经理审核附件
		List<ShopDeviseAttach> managerDealersAttachs = shopDevise.getManagerDealersAttachs();
		Iterator<ShopDeviseAttach> managerDealersIterator = managerDealersAttachs.iterator();
		while (managerDealersIterator.hasNext()) {
			ShopDeviseAttach managerDealersAttach = managerDealersIterator.next();
			if (managerDealersAttach == null || managerDealersAttach.getUrl() == null) {
				managerDealersIterator.remove();
				continue;
			}
			if (managerDealersAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			managerDealersAttach.setFileName(managerDealersAttach.getName() + "." + managerDealersAttach.getSuffix());
			managerDealersAttach.setShopDevise(shopDevise);
			managerDealersAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setManagerDealersAttachs(managerDealersAttachs);

		List<ShopDeviseAttach> managerStoreContractAttachs = shopDevise.getManagerStoreContractAttachs();
		Iterator<ShopDeviseAttach> managerStoreContractIterator = managerStoreContractAttachs.iterator();
		while (managerStoreContractIterator.hasNext()) {
			ShopDeviseAttach managerStoreContractAttach = managerStoreContractIterator.next();
			if (managerStoreContractAttach == null || managerStoreContractAttach.getUrl() == null) {
				managerStoreContractIterator.remove();
				continue;
			}
			if (managerStoreContractAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			managerStoreContractAttach
					.setFileName(managerStoreContractAttach.getName() + "." + managerStoreContractAttach.getSuffix());
			managerStoreContractAttach.setShopDevise(shopDevise);
			managerStoreContractAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setManagerStoreContractAttachs(managerStoreContractAttachs);

		List<ShopDeviseAttach> managerStorePicturesAttachs = shopDevise.getManagerStorePicturesAttachs();
		Iterator<ShopDeviseAttach> managerStorePicturesIterator = managerStorePicturesAttachs.iterator();
		while (managerStorePicturesIterator.hasNext()) {
			ShopDeviseAttach managerStorePicturesAttach = managerStorePicturesIterator.next();
			if (managerStorePicturesAttach == null || managerStorePicturesAttach.getUrl() == null) {
				managerStorePicturesIterator.remove();
				continue;
			}
			if (managerStorePicturesAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			managerStorePicturesAttach
					.setFileName(managerStorePicturesAttach.getName() + "." + managerStorePicturesAttach.getSuffix());
			managerStorePicturesAttach.setShopDevise(shopDevise);
			managerStorePicturesAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setManagerStorePicturesAttachs(managerStorePicturesAttachs);

		// 设计部确认附件
		List<ShopDeviseAttach> designAttachs = shopDevise.getDesignAttachs();
		Iterator<ShopDeviseAttach> designIterator = designAttachs.iterator();
		while (designIterator.hasNext()) {
			ShopDeviseAttach designAttach = designIterator.next();
			if (designAttach == null || designAttach.getUrl() == null) {
				designIterator.remove();
				continue;
			}
			if (designAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			designAttach.setFileName(designAttach.getName() + "." + designAttach.getSuffix());
			designAttach.setShopDevise(shopDevise);
			designAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setDesignAttachs(designAttachs);
		this.save(shopDevise);
	}

	@Override
	public Page<Map<String, Object>> findPage(ShopDevise shopDevise, List<Object> param, Pageable pageable) {
		return shopDeviseDao.findPage(shopDevise,param, pageable);
	}

	@Override
	public Page<Map<String, Object>> findSelectPage(List<Object> param, Pageable pageable) {
		return shopDeviseDao.findSelectPage(param, pageable);
	}

	@Override
	public List<Map<String, Object>> findShopDeviseAttach(Long id, Integer type) {
		return shopDeviseDao.findShopDeviseAttach(id, type);
	}

	@Override
	@Transactional
	public void updateShopDevise(ShopDevise shopDevise) {

		// 经销商提交申请附件
		List<ShopDeviseAttach> dealersAttachs = shopDevise.getDealersAttachs();
		Iterator<ShopDeviseAttach> dealersIterator = dealersAttachs.iterator();
		while (dealersIterator.hasNext()) {
			ShopDeviseAttach dealersAttach = dealersIterator.next();
			if (dealersAttach == null || dealersAttach.getUrl() == null) {
				dealersIterator.remove();
				continue;
			}
			if (dealersAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			dealersAttach.setFileName(dealersAttach.getName() + "." + dealersAttach.getSuffix());
			dealersAttach.setShopDevise(shopDevise);
			dealersAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setDealersAttachs(dealersAttachs);

		List<ShopDeviseAttach> storeContractAttachs = shopDevise.getStoreContractAttachs();
		Iterator<ShopDeviseAttach> storeContractIterator = storeContractAttachs.iterator();
		while (storeContractIterator.hasNext()) {
			ShopDeviseAttach storeContractAttach = storeContractIterator.next();
			if (storeContractAttach == null || storeContractAttach.getUrl() == null) {
				storeContractIterator.remove();
				continue;
			}
			if (storeContractAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			storeContractAttach.setFileName(storeContractAttach.getName() + "." + storeContractAttach.getSuffix());
			storeContractAttach.setShopDevise(shopDevise);
			storeContractAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setStoreContractAttachs(storeContractAttachs);

		List<ShopDeviseAttach> storePicturesAttachs = shopDevise.getStorePicturesAttachs();
		Iterator<ShopDeviseAttach> storePicturesIterator = storePicturesAttachs.iterator();
		while (storePicturesIterator.hasNext()) {
			ShopDeviseAttach storePicturesAttach = storePicturesIterator.next();
			if (storePicturesAttach == null || storePicturesAttach.getUrl() == null) {
				storePicturesIterator.remove();
				continue;
			}
			if (storePicturesAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			storePicturesAttach.setFileName(storePicturesAttach.getName() + "." + storePicturesAttach.getSuffix());
			storePicturesAttach.setShopDevise(shopDevise);
			storePicturesAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setStorePicturesAttachs(storePicturesAttachs);

		// 区域经理审核附件
		List<ShopDeviseAttach> managerDealersAttachs = shopDevise.getManagerDealersAttachs();
		Iterator<ShopDeviseAttach> managerDealersIterator = managerDealersAttachs.iterator();
		while (managerDealersIterator.hasNext()) {
			ShopDeviseAttach managerDealersAttach = managerDealersIterator.next();
			if (managerDealersAttach == null || managerDealersAttach.getUrl() == null) {
				managerDealersIterator.remove();
				continue;
			}
			if (managerDealersAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			managerDealersAttach.setFileName(managerDealersAttach.getName() + "." + managerDealersAttach.getSuffix());
			managerDealersAttach.setShopDevise(shopDevise);
			managerDealersAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setManagerDealersAttachs(managerDealersAttachs);

		List<ShopDeviseAttach> managerStoreContractAttachs = shopDevise.getManagerStoreContractAttachs();
		Iterator<ShopDeviseAttach> managerStoreContractIterator = managerStoreContractAttachs.iterator();
		while (managerStoreContractIterator.hasNext()) {
			ShopDeviseAttach managerStoreContractAttach = managerStoreContractIterator.next();
			if (managerStoreContractAttach == null || managerStoreContractAttach.getUrl() == null) {
				managerStoreContractIterator.remove();
				continue;
			}
			if (managerStoreContractAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			managerStoreContractAttach
					.setFileName(managerStoreContractAttach.getName() + "." + managerStoreContractAttach.getSuffix());
			managerStoreContractAttach.setShopDevise(shopDevise);
			managerStoreContractAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setManagerStoreContractAttachs(managerStoreContractAttachs);

		List<ShopDeviseAttach> managerStorePicturesAttachs = shopDevise.getManagerStorePicturesAttachs();
		Iterator<ShopDeviseAttach> managerStorePicturesIterator = managerStorePicturesAttachs.iterator();
		while (managerStorePicturesIterator.hasNext()) {
			ShopDeviseAttach managerStorePicturesAttach = managerStorePicturesIterator.next();
			if (managerStorePicturesAttach == null || managerStorePicturesAttach.getUrl() == null) {
				managerStorePicturesIterator.remove();
				continue;
			}
			if (managerStorePicturesAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			managerStorePicturesAttach
					.setFileName(managerStorePicturesAttach.getName() + "." + managerStorePicturesAttach.getSuffix());
			managerStorePicturesAttach.setShopDevise(shopDevise);
			managerStorePicturesAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setManagerStorePicturesAttachs(managerStorePicturesAttachs);
		// 设计部确认附件
		List<ShopDeviseAttach> designAttachs = shopDevise.getDesignAttachs();
		Iterator<ShopDeviseAttach> designIterator = designAttachs.iterator();
		while (designIterator.hasNext()) {
			ShopDeviseAttach designAttach = designIterator.next();
			if (designAttach == null || designAttach.getUrl() == null) {
				designIterator.remove();
				continue;
			}
			if (designAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			designAttach.setFileName(designAttach.getName() + "." + designAttach.getSuffix());
			designAttach.setShopDevise(shopDevise);
			designAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopDevise.setDesignAttachs(designAttachs);
		this.update(shopDevise);
	}
	//
	// @Override
	// public void checkShopDeviseWf(Long id, Long objConfId) {
	// // 创建流程实例
	// ShopDevise sd = find(id);
	// if(sd.getWfId() != null){
	// ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
	// }
	// System.out.println("id:"+id+" objConfId:"+objConfId);
	// WfObjConfigLine wfObjConfigLine =
	// wfObjConfigLineBaseService.find(objConfId);
	// System.out.println(wfObjConfigLine);
	//
	//
	// if(wfObjConfigLine == null){
	// ExceptionUtil.throwServiceException("请选择审核流程");
	// }
	// sd.setByObjConfig(wfObjConfigLine);
	//
	// wfBaseService.createwf(
	// storeMemberService.getCurrent(),
	// null,
	// sd.getSn(),
	// sd.getWfTempId(),
	// sd.getObjTypeId(),
	// sd.getId());
	// update(sd);
	//
	// }

	@Override
	public void saveform(ShopDevise shopDevise, Integer[] regionalManagerOpinions, Integer[] floorDivisions,
			Integer type) {
		ShopDevise s = find(shopDevise.getId());
		// type 1区域经理 2省长 3渠道部 4门店设计师
		if (type == 1) {
			// 区域经理审核附件
			List<ShopDeviseAttach> managerDealersAttachs = shopDevise.getManagerDealersAttachs();
			Iterator<ShopDeviseAttach> managerDealersIterator = managerDealersAttachs.iterator();
			while (managerDealersIterator.hasNext()) {
				ShopDeviseAttach managerDealersAttach = managerDealersIterator.next();
				if (managerDealersAttach == null || managerDealersAttach.getUrl() == null) {
					managerDealersIterator.remove();
					continue;
				}
				if (managerDealersAttach.getName() == null) {
					ExceptionUtil.throwServiceException("附件名不能为空");
				}
				managerDealersAttach
						.setFileName(managerDealersAttach.getName() + "." + managerDealersAttach.getSuffix());
				managerDealersAttach.setShopDevise(s);
				managerDealersAttach.setStoreMember(storeMemberService.getCurrent());
			}
			s.getManagerDealersAttachs().clear();
			s.getManagerDealersAttachs().addAll(managerDealersAttachs);

			List<ShopDeviseAttach> managerStoreContractAttachs = shopDevise.getManagerStoreContractAttachs();
			Iterator<ShopDeviseAttach> managerStoreContractIterator = managerStoreContractAttachs.iterator();
			while (managerStoreContractIterator.hasNext()) {
				ShopDeviseAttach managerStoreContractAttach = managerStoreContractIterator.next();
				if (managerStoreContractAttach == null || managerStoreContractAttach.getUrl() == null) {
					managerStoreContractIterator.remove();
					continue;
				}
				if (managerStoreContractAttach.getName() == null) {
					ExceptionUtil.throwServiceException("附件名不能为空");
				}
				managerStoreContractAttach.setFileName(
						managerStoreContractAttach.getName() + "." + managerStoreContractAttach.getSuffix());
				managerStoreContractAttach.setShopDevise(s);
				managerStoreContractAttach.setStoreMember(storeMemberService.getCurrent());
			}
			s.getManagerStoreContractAttachs().clear();
			s.getManagerStoreContractAttachs().addAll(managerStoreContractAttachs);

			List<ShopDeviseAttach> managerStorePicturesAttachs = shopDevise.getManagerStorePicturesAttachs();
			Iterator<ShopDeviseAttach> managerStorePicturesIterator = managerStorePicturesAttachs.iterator();
			while (managerStorePicturesIterator.hasNext()) {
				ShopDeviseAttach managerStorePicturesrAttach = managerStorePicturesIterator.next();
				if (managerStorePicturesrAttach == null || managerStorePicturesrAttach.getUrl() == null) {
					managerStorePicturesIterator.remove();
					continue;
				}
				if (managerStorePicturesrAttach.getName() == null) {
					ExceptionUtil.throwServiceException("附件名不能为空");
				}
				managerStorePicturesrAttach.setFileName(
						managerStorePicturesrAttach.getName() + "." + managerStorePicturesrAttach.getSuffix());
				managerStorePicturesrAttach.setShopDevise(s);
				managerStorePicturesrAttach.setStoreMember(storeMemberService.getCurrent());
			}
			s.getManagerStorePicturesAttachs().clear();
			s.getManagerStorePicturesAttachs().addAll(managerStorePicturesAttachs);
		}
		if (type == 2) {
			if(shopDevise.getRegionalManagerMemo()==null){
				ExceptionUtil.throwServiceException("请填写省长意见的备注");
			}
			s.setRegionalManagerOpinion(splicer(regionalManagerOpinions));
			s.setRegionalManagerMemo(shopDevise.getRegionalManagerMemo());
		}
		if (type == 3) {
			if(shopDevise.getFloorDivisionMemo()==null){
				ExceptionUtil.throwServiceException("请填写地板事业部渠道部的备注");
			}
			s.setFloorDivision(splicer(floorDivisions));
			s.setFloorDivisionMemo(shopDevise.getFloorDivisionMemo());
		}
		if (type == 4) {
			if(shopDevise.getDesignOpinion()==null){
				ExceptionUtil.throwServiceException("请填写设计师意见");
			}
			if(shopDevise.getDesignAttachs()==null){
				ExceptionUtil.throwServiceException("请上传附件");
			}
			s.setDesignOpinion(shopDevise.getDesignOpinion());
			// 设计部审核附件
			List<ShopDeviseAttach> designAttachs = shopDevise.getDesignAttachs();
			Iterator<ShopDeviseAttach> designIterator = designAttachs.iterator();
			while (designIterator.hasNext()) {
				ShopDeviseAttach designAttach = designIterator.next();
				if (designAttach == null || designAttach.getUrl() == null) {
					designIterator.remove();
					continue;
				}
				if (designAttach.getName() == null) {
					ExceptionUtil.throwServiceException("附件名不能为空");
				}
				designAttach.setFileName(designAttach.getName() + "." + designAttach.getSuffix());
				designAttach.setShopDevise(s);
				designAttach.setStoreMember(storeMemberService.getCurrent());
			}
			s.getDesignAttachs().clear();
			s.getDesignAttachs().addAll(designAttachs);
			// s.setDesignAttachs(shopDevise.getDesignAttachs());
		}
		this.update(s);

	}

	public String splicer(Integer[] str) {
		String s = "";
		if (str != null && str.length > 0) {
			for (int i = 0; i < str.length; i++) {
				if (str.length - 1 == i) {
					s += str[i];
				} else {
					s += str[i] + ",";
				}
			}
		}
		return s;
	}

	/**
	 * 创建流程实例
	 */
	@Override
	@Transactional
	public void createWf(Long id, String modelId, Long objTypeId) {
		ShopDevise sd = find(id);
		if (sd.getWfId() != null) {
			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
		}
		StoreMember storeMember = storeMemberService.getCurrent();
		// 创建流程实例
		createWf(sd.getSn(), String.valueOf(storeMember.getId()),
				new Long[]{sd.getShopInfo().getSaleOrg().getId()},
				sd.getShopInfo().getStore().getId(),
				modelId, objTypeId, id,
				WebUtils.getCurrentCompanyInfoId());
		sd.setBillsStatus(3);
		update(sd);
		shopInfoService.updateDeviseStatus(sd);
	}

	/**
	 * 中断流程回调
	 */
	public void interruptBack(ActWf wf) {
		ShopDevise shopDevise = find(wf.getObjId());
		shopDevise.setBillsStatus(0);
		update(shopDevise);
		shopInfoService.updateDeviseStatus(shopDevise);
	}

	/**
	 * 流程结束回调
	 */
	@Override
	public void endBack(ActWf arg0) {
		super.endBack(arg0);
		ShopDevise shopDevise = find(arg0.getObjId());
		shopDevise.setBillsStatus(1);
		// 流程结束时间
		Date day = new Date();
		shopDevise.setTransitTime(day);
		update(shopDevise);
		shopInfoService.updateDeviseStatus(shopDevise);
	}

	@Override
	public void cancel(Long id) {
		ShopDevise sd = this.find(id);
		sd.setBillsStatus(99);
		update(sd);
	}

	/**
	 * 流程节点通过前回调 
	 */
	public void completePre(ActWf wf) {
		ShopDevise shopDevise = find(wf.getObjId());
		Task t = this.getCurrTaskByWf(wf);
		String nodeName = t.getName();
		if (nodeName.contains("区域经理")) {
			
		}
		if (nodeName.contains("省长")) {
			if(shopDevise.getRegionalManagerOpinion()==null){
				ExceptionUtil.throwServiceException("请勾选一个选项！");
			}
			if(!shopDevise.getRegionalManagerOpinion().contains("0")){
				ExceptionUtil.throwServiceException("请勾选： 申请设计面积与事实相符！");
			}
			if(!shopDevise.getRegionalManagerOpinion().contains("1")){
				ExceptionUtil.throwServiceException("请勾选： 同意按公司SI标准设计！");
			}
			if(shopDevise.getRegionalManagerMemo()==null){
				ExceptionUtil.throwServiceException("请填写省长意见的备注！");
			}
		}
		if (nodeName.contains("渠道专员")) {
			if(shopDevise.getFloorDivision()==null){
				ExceptionUtil.throwServiceException("请勾选一个选项！");
			}
			if(!shopDevise.getFloorDivision().contains("0")){
				ExceptionUtil.throwServiceException("请勾选： 2年内未参与装修报销！");
			}
			if(!shopDevise.getFloorDivision().contains("1")){
				ExceptionUtil.throwServiceException("请勾选： 签署2020年经销合同！");
			}
			if(!shopDevise.getFloorDivision().contains("2")){
				ExceptionUtil.throwServiceException("请勾选： 缴纳品牌保证金！");
			}
			if(shopDevise.getFloorDivisionMemo()==null){
				ExceptionUtil.throwServiceException("请填写地板事业部渠道部的备注！");
			}
		}
		if (nodeName.contains("设计部")) {
			
		}
	}

}
