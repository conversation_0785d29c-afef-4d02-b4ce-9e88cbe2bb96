package net.shopxx.shop.service.impl;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.dao.ShopAlterationDao;
import net.shopxx.shop.entity.ShopAlteration;
import net.shopxx.shop.entity.ShopAlteration.Status;
import net.shopxx.shop.entity.ShopAlterationAttach;
import net.shopxx.shop.entity.ShopInfo;
import net.shopxx.shop.service.ShopAlterationService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;

@Service("shopAlterationServiceImpl")
public class ShopAlterationServiceImpl extends ActWfBillServiceImpl<ShopAlteration> implements ShopAlterationService {

	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;
	@Resource(name = "shopAlterationDao")
	private ShopAlterationDao shopAlterationDao;
	@Resource(name = "wfObjConfigLineBaseServiceImpl")
	private WfObjConfigLineBaseService wfObjConfigLineBaseService;
	@Resource(name = "wfBaseServiceImpl")
	private WfBaseService wfBaseService;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberService;

	@Override
	public void saveAlteration(ShopAlteration shopAlteration, String[] businessCategory, Long storeId,
			Long shopInfoId) {
		if(shopAlteration.getNewShopArea()==null||shopAlteration.getNewShopArea().getId()==null){
			shopAlteration.setNewShopArea(null);
		}
		shopAlteration.setSn(Sequence.getInstance().getSequence(null));
		shopAlteration.setStatuss(Status.saved);
		Store store = storeBaseService.find(storeId);
		ShopInfo si = shopInfoService.find(shopInfoId);
		shopAlteration.setStoreMember(storeMemberService.getCurrent());
		shopAlteration.setStore(store);
		shopAlteration.setInfoShop(si);
		shopAlteration.setBusinessCategory(splicer(businessCategory));
		shopAlteration.setSaleOrg(si.getSaleOrg());
		// 附件处理
		List<ShopAlterationAttach> shopAlterationAttachs = shopAlteration.getShopAlterationAttachs();
		for (Iterator<ShopAlterationAttach> iterator = shopAlterationAttachs.iterator(); iterator.hasNext();) {
			ShopAlterationAttach shopAlterationAttach = iterator.next();
			if (shopAlterationAttach == null || shopAlterationAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (shopAlterationAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			shopAlterationAttach.setFileName(shopAlterationAttach.getName() + "." + shopAlterationAttach.getSuffix());
			shopAlterationAttach.setAlteration(shopAlteration);
			shopAlterationAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopAlteration.setShopAlterationAttachs(shopAlterationAttachs);
		save(shopAlteration);
	}

	@Override
	public void updateAlteration(ShopAlteration shopAlteration, String[] businessCategory, Long storeId,
			Long shopInfoId) {
		if(shopAlteration.getNewShopArea()==null||shopAlteration.getNewShopArea().getId()==null){
			shopAlteration.setNewShopArea(null);
		}
		Store store = storeBaseService.find(storeId);
		ShopInfo si = shopInfoService.find(shopInfoId);
		shopAlteration.setStore(store);
		shopAlteration.setInfoShop(si);
		shopAlteration.setBusinessCategory(splicer(businessCategory));
		shopAlteration.setSaleOrg(si.getSaleOrg());
		// 附件处理
		List<ShopAlterationAttach> shopAlterationAttachs = shopAlteration.getShopAlterationAttachs();
		for (Iterator<ShopAlterationAttach> iterator = shopAlterationAttachs.iterator(); iterator.hasNext();) {
			ShopAlterationAttach shopAlterationAttach = iterator.next();
			if (shopAlterationAttach == null || shopAlterationAttach.getUrl() == null) {
				iterator.remove();
				continue;
			}
			if (shopAlterationAttach.getName() == null) {
				ExceptionUtil.throwServiceException("附件名不能为空");
			}
			shopAlterationAttach.setFileName(shopAlterationAttach.getName() + "." + shopAlterationAttach.getSuffix());
			shopAlterationAttach.setAlteration(shopAlteration);
			shopAlterationAttach.setStoreMember(storeMemberService.getCurrent());
		}
		shopAlteration.setShopAlterationAttachs(shopAlterationAttachs);
		update(shopAlteration, "statuss");
	}

	@Override
	public Page<Map<String, Object>> findPage(String a, Pageable pageable) {
		return shopAlterationDao.findPage(a, pageable);
	}

	@Override
	public List<Map<String, Object>> findShopAlterationAttach(Long id) {
		return shopAlterationDao.findShopAlterationAttach(id);
	}

	@Override
	public void saveform(ShopAlteration shopAlteration, String[] shopSign, Integer Type) {
		ShopAlteration sa = find(shopAlteration.getId());
		// 区域经理
		if (Type == 1) {
			sa.setType(shopAlteration.getType());
			sa.setBelongBrand(shopAlteration.getBelongBrand());
			sa.setDepartment(shopAlteration.getDepartment());
			sa.setViVersion(shopAlteration.getViVersion());
			sa.setShopSign(splicer(shopSign));
			sa.setSalesChannel(shopAlteration.getSalesChannel());
		}
		// 渠道部
		if (Type == 2) {
			sa.setAuthorizationCode(shopAlteration.getAuthorizationCode());
			sa.setIncreaseArchivesCode(shopAlteration.getIncreaseArchivesCode());
			sa.setNewTime(shopAlteration.getNewTime());
			sa.setDecreaseArchivesCode(shopAlteration.getDecreaseArchivesCode());
			sa.setDecreaseTime(shopAlteration.getDecreaseTime());
			sa.setShopCaseNote(shopAlteration.getShopCaseNote());
		}
		update(sa);
	}

	public String splicer(String[] str) {
		String s = "";
		if (str != null && str.length > 0) {
			for (int i = 0; i < str.length; i++) {
				if (str.length - 1 == i) {
					s += str[i];
				} else {
					s += str[i] + ",";
				}
			}
		}
		return s;
	}

	@Override
	public void endBack(ActWf wf) {
		super.endBack(wf);
		ShopAlteration shopAlteration = find(wf.getObjId());
		shopAlteration.setStatuss(Status.submitted);
		update(shopAlteration);
		shopInfoService.updateShopInfo(shopAlteration);
	}
	

	@Override
	public void setStatus(ShopAlteration sa) {
		sa.setStatuss(Status.underway);
		update(sa);
	}

	@Override
	public void cancel(Long id) {
		ShopAlteration sa = find(id);
		sa.setStatuss(Status.cancel);
		update(sa);
	}

	@Override
	public List<Map<String, Object>> findSA(Long shopInfoId) {
		return shopAlterationDao.findSA(shopInfoId);
	}

	@Override
	public void createWf(Long id, String modelId, Long objTypeId) {
		ShopAlteration sa = find(id);
		if (sa.getWfId() != null) {
			ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
		}
		StoreMember storeMember = storeMemberService.getCurrent();
		createWf(sa.getSn(), String.valueOf(storeMember.getId()),
				new Long[]{sa.getSaleOrg().getId()},
				sa.getStore().getId(),
				modelId,
				objTypeId,
				id,
				WebUtils.getCurrentCompanyInfoId(),
                true);
		sa.setStatuss(Status.underway);
		update(sa);

	}

	@Override
	public List<Map<String, Object>> findShopAttach1(Long id, Integer type) {
		return shopAlterationDao.findShopAttach1(id, type);

	}
    @Override
    public void interruptBack(ActWf wf) {
        ShopAlteration shopAlteration = find(wf.getObjId());
        shopAlteration.setStatuss(Status.saved);
        update(shopAlteration);
    }

}
