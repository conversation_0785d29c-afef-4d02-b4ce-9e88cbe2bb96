package net.shopxx.shop.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.shopxx.base.core.util.ExceptionUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.member.dao.StoreMemberBaseDao;
import net.shopxx.member.entity.Member;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMember.Gender;
import net.shopxx.member.service.MemberBaseService;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.shop.dao.ShopMemberDao;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.shop.service.ShopMemberService;
import net.shopxx.util.PinYinUtils;

@Service("shopMemberServiceImpl")
public class ShopMemberServiceImpl extends BaseServiceImpl<Object> implements ShopMemberService {

	@Resource(name = "companyInfoBaseServiceImpl")
	private CompanyInfoBaseService companyInfoBaseService;
	@Resource(name = "memberBaseServiceImpl")
	private MemberBaseService memberBaseService;
	@Resource(name = "storeMemberBaseDao")
	private StoreMemberBaseDao storeMemberBaseDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	@Resource(name = "shopMemberDao")
	private ShopMemberDao shopMemberDao;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeBaseService;

	@Override
	public void saveShopMember(String name, Integer gender, String IdCard, String Phone, String imageName,
			Long[] roleId, Long shopInfoId, Boolean isEnabled,Integer type) {
		Long[] lo = new Long[]{shopInfoId};
		StoreMember s = createShopMember(name, gender, IdCard, Phone, imageName, roleId, isEnabled,lo);
		Long memberId = s.getId();
		Long appId = shopMemberDao.findAppStoreMemberId(shopInfoId);
		shopMemberDao.saveAppStoreMember(appId, "createBy", type, IdCard, memberId);
	}

	@Transactional
	public StoreMember createShopMember(String name, Integer gender, String IdCard, String Phone, String imageName,
			Long[] roleId, Boolean isEnabled,Long[] shopId) {
		CompanyInfo companyInfo = companyInfoBaseService.getCurrent();
		// 保存用户
		Member member = new Member();
		Member m = memberBaseService.findByMobile(Phone);
		if(m!=null){
			member = m;
		}else{
			member.setIsLocked(false);
			member.setLoginFailureCount(0);
			member.setPassword(DigestUtils.md5Hex("123456"));
			member.setUsername(Sequence.getInstance().getSequence(null));
			member.setMobile(Phone);
			memberBaseService.save(member);			
		}
		
		StoreMember storeMember = new StoreMember();
		storeMember.setCompanyInfoId(companyInfo.getId());
		// 保存用户
		storeMember.setMember(member);
		storeMember.setBalance(new BigDecimal(0));
		storeMember.setIsEnabled(isEnabled);
		storeMember.setUsername(name);
		storeMember.setPassword(DigestUtils.md5Hex("123456"));
		storeMember.setPayPassword(DigestUtils.md5Hex("888888"));
		storeMember.setIdCard(IdCard);
		storeMember.setImageName(imageName);
		storeMember.setStore(storeBaseService.find(132L));
		if (gender == 0) {
			storeMember.setGender(Gender.male);
		} else if (gender == 1) {
			storeMember.setGender(Gender.female);
		}
		storeMember.setName(name);
		if (storeMemberBaseDao.existsIsDefaultStoreMember(member.getId())) {
			storeMember.setIsDefault(false);
		} else {
			storeMember.setIsDefault(true);
		}
		storeMember.setMemberType(1);
		storeMemberBaseService.save(storeMember);
		// 保存用户角色
		storeMemberBaseService.saveUserRole(roleId, member, storeMember, true);
		// 保存用户门店
		storeMemberBaseService.saveSmShop(shopId, storeMember);
		return storeMember;

	}

	@Override
	public void updateShopMember(Long storeMemberId, String name, Integer gender, String IdCard, String Phone,
			String imageName, Long[] roleId, Long shopInfoId, Boolean isEnabled) {
		StoreMember s = storeMemberBaseService.find(storeMemberId);
		updateStoreMember(s, name, gender, IdCard, Phone, imageName, roleId, isEnabled);
		
	}
	
	@Override
	@Transactional
	public Long addMember(Long appId,String name, Integer gender, String IdCard, String Phone, Boolean isEnabled,
			Long shopInfoId, Long[] roleId){
		StoreMember s = createLinkMember(name, gender, IdCard, Phone, isEnabled, shopInfoId, roleId);
		shopMemberDao.appMember(s.getId(),appId);
		return appId;
	}

	@Transactional
	public StoreMember updateStoreMember(StoreMember storeMember, String name, Integer gender, String IdCard,
			String Phone, String imageName, Long[] roleId, Boolean isEnabled) {
		Member m = memberBaseService.findByMobile(Phone);
		if (m != null) {
//			if (storeMemberBaseService.storeMemberExists(m.getId(), storeMember.getId())) {
//				// 手机号已存在
//				ExceptionUtil.throwServiceException("1601006");
//			}
		}else{
			m.setMobile(Phone);		
		}
		memberBaseService.update(m,"id");
		storeMember.setBalance(new BigDecimal(0));
		storeMember.setIsEnabled(isEnabled);
		storeMember.setName(name);
		storeMember.setIdCard(IdCard);
		storeMember.setImageName(imageName);
		if (gender == 0) {
			storeMember.setGender(Gender.male);
		} else if (gender == 1) {
			storeMember.setGender(Gender.female);
		}
		// 清空用户所有角色
		shopMemberDao.delete(storeMember.getId());
		// 保存用户角色
		storeMemberBaseService.saveUserRole(roleId, storeMember.getMember(), storeMember, true);
		storeMemberBaseService.update(storeMember);
		return storeMember;
	}

	@Override
	public Page<Map<String, Object>> findPages(Pageable pageable) {
		return shopMemberDao.findPage(pageable);
	}

	@Override
	public List<Map<String, Object>> findAppStoreMember(Long id) {
		return shopMemberDao.findAppStoreMember(id);
	}

	public StoreMember createLinkMember(String name, Integer gender, String IdCard, String Phone, Boolean isEnabled,
			Long shopInfoId, Long[] roleId) {
		//username生成策略   取姓名头字母+手机号
		String username = PinYinUtils.getPinYinHeadChar(name)+Phone;
		Long[] lo = new Long[]{shopInfoId};
		StoreMember s = createShopMember(username, gender, IdCard, Phone, "", roleId, isEnabled,lo);
		return s;
		
	}

	@Override
	public void createAppMember(String name, Integer gender, String IdCard, String Phone, Long shopInfoId) {
		
		
		
	}

	@Override
	@Transactional
	public void deleteShopMember(Long tsm_id, Long id) {
		if(tsm_id==null||id==null){
			ExceptionUtil.throwServiceException("删除失败！");
		}
		StoreMember storeMember = storeMemberBaseService.find(id);
		storeMember.setIsEnabled(false);
		storeMember.setAppType(2);
		storeMember.setMemberType(99);
		storeMemberBaseService.update(storeMember);
		shopMemberDao.deleteTickStoreMember(tsm_id);
		shopMemberDao.deleteStoreMemberShopInfo(id);
	}

	@Override
	public List<Map<String,Object>> findShopMember(Long id) {
		return shopMemberDao.findShopMember(id);
	}

}
