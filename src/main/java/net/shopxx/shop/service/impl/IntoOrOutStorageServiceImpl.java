package net.shopxx.shop.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.act.entity.ActWf;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.Sequence;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.member.entity.Store;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.order.entity.ShippingItem;
import net.shopxx.order.service.ShippingItemService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.shop.dao.IntoOrOutStorageDao;
import net.shopxx.shop.entity.AcceptanceReimburse;
import net.shopxx.shop.entity.IntoOrOutStorage;
import net.shopxx.shop.entity.IntoOrOutStorageItem;
import net.shopxx.shop.entity.SetStorage;
import net.shopxx.shop.entity.StorageProduct;
import net.shopxx.shop.service.IntoOrOutStorageService;
import net.shopxx.shop.service.StorageProductService;
import net.shopxx.wf.service.impl.WfBillBaseServiceImpl;

@Service("intoOrOutStorageServiceImpl")
public class IntoOrOutStorageServiceImpl extends WfBillBaseServiceImpl<IntoOrOutStorage>
		implements IntoOrOutStorageService {

	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;
	@Resource(name = "storeBaseServiceImpl")
	private StoreBaseService storeService;
	@Resource(name = "storageProductServiceImpl")
	private StorageProductService storageProductService;
	@Resource(name = "shippingItemServiceImpl")
	private ShippingItemService shippingItemService;
	@Resource(name = "intoOrOutStorageDao")
	private IntoOrOutStorageDao intoOrOutStorageDao;

	@Override
	public Page<Map<String, Object>> findInPage(List<Object> param, Pageable pageable) {
		return intoOrOutStorageDao.findInPage(param, pageable);
	}

	@Override
	public Page<Map<String, Object>> findOutPage(List<Object> param, Pageable pageable) {
		return intoOrOutStorageDao.findOutPage(param, pageable);
	}

	@Override
	public List<Map<String, Object>> findIOItemById(Long id) {
		return intoOrOutStorageDao.findIOItemById(id);
	}

	@Override
	public Page<Map<String, Object>> findProPutInPage(List<Object> param, Pageable pageable) {
		return intoOrOutStorageDao.findProPutInPage(param, pageable);
	}

	@Override
	public Page<Map<String, Object>> findProPutInSelectPage(List<Object> param, Pageable pageable) {
		return intoOrOutStorageDao.findProPutInSelectPage(param, pageable);
	}

	@Override
	@Transactional
	public void saveCR(IntoOrOutStorage intoOrOutStorage, Long storeId) {
		// 处理产品项（出入库项）
		List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
		List<Map<String,BigDecimal>> listMap = new ArrayList<Map<String,BigDecimal>>();
		Set<Long> set = new HashSet<Long>();
		List<Long> list = new ArrayList<Long>();
		for (Iterator<IntoOrOutStorageItem> iterator = products.iterator(); iterator.hasNext();) {
			IntoOrOutStorageItem item = iterator.next();
			Product product = item.getProduct();
			if (product == null || product.getId() == null) {
				iterator.remove();
				continue;
			}
			ShippingItem shippingItem = item.getShippingItem();
			if (shippingItem == null || shippingItem.getId() == null) {
				iterator.remove();
				continue;
			}
			Map<String,BigDecimal> map = new HashMap<String, BigDecimal>();
			
			set.add(shippingItem.getId());
			System.out.println("添加："+shippingItem.getId());
			map.put(shippingItem.getId().toString(), item.getNumber());
			listMap.add(map);
		}
		list.addAll(set);
		for(int i=0;i<list.size();i++){
			String key = list.get(i).toString();
			BigDecimal kcsl = findInventory(list.get(i));
			BigDecimal rksl = BigDecimal.ZERO;
			Product p = shippingItemService.find(list.get(i)).getProduct();
			for(Map<String,BigDecimal> m : listMap){
				if(m.get(key)!=null){
					rksl = rksl.add(m.get(key));					
				}
			}
			if(kcsl.compareTo(rksl)<0){
				ExceptionUtil.throwServiceException("商品"+p.getVonderCode()+"入库数量大于采购数量！");
			}
		}
		for (int i = 0; i < products.size(); i++) {
			IntoOrOutStorageItem item = products.get(i);
			item.setIntoOrOutStorage(intoOrOutStorage);
			item.setProduct(productBaseService.find(item.getProduct().getId()));
			// 发货项
			item.setShippingItem(shippingItemService.find(item.getShippingItem().getId()));
		}
		intoOrOutStorage.setStatus(0);
		this.save(intoOrOutStorage);
	}

	@Override
	@Transactional
	public void updateCR(IntoOrOutStorage intoOrOutStorage, Long storeId) {
		// 处理产品项（出入库项）
		List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
		List<Map<String,BigDecimal>> listMap = new ArrayList<Map<String,BigDecimal>>();
		Set<Long> set = new HashSet<Long>();
		List<Long> list = new ArrayList<Long>();
		for (Iterator<IntoOrOutStorageItem> iterator = products.iterator(); iterator.hasNext();) {
			IntoOrOutStorageItem item = iterator.next();
			Product product = item.getProduct();
			if (product == null || product.getId() == null) {
				iterator.remove();
				continue;
			}
			ShippingItem shippingItem = item.getShippingItem();
			if (shippingItem == null || shippingItem.getId() == null) {
				iterator.remove();
				continue;
			}
			Map<String,BigDecimal> map = new HashMap<String, BigDecimal>();
			set.add(shippingItem.getId());
			map.put(shippingItem.getId().toString(), item.getNumber());
			listMap.add(map);
		}
		list.addAll(set);
		for(int i=0;i<list.size();i++){
			String key = list.get(i).toString();
			BigDecimal kcsl = findInventory(list.get(i));
			BigDecimal rksl = BigDecimal.ZERO;
			Product p = shippingItemService.find(list.get(i)).getProduct();
			for(Map<String,BigDecimal> m : listMap){
				if(m.get(key)!=null){
					rksl = rksl.add(m.get(key));					
				}
			}
			
			if(kcsl.compareTo(rksl)<0){
				ExceptionUtil.throwServiceException("商品"+p.getVonderCode()+"入库数量大于采购数量！");
			}
		}
		for (int i = 0; i < products.size(); i++) {
			IntoOrOutStorageItem item = products.get(i);
			item.setIntoOrOutStorage(intoOrOutStorage);
			item.setProduct(productBaseService.find(item.getProduct().getId()));
			// 发货项
			item.setShippingItem(shippingItemService.find(item.getShippingItem().getId()));
		}
		this.update(intoOrOutStorage);
	}

	@Override
	public List<Map<String, Object>> findProInById(Long id, Store store) {
		return intoOrOutStorageDao.findProInById(id, store);
	}

	@Override
	public void createrOutStorage(Integer status, Integer type,Long orderId, SetStorage storage, List<Map<String, Object>> list) {
		IntoOrOutStorage intoOrOutStorage = new IntoOrOutStorage();
		intoOrOutStorage.setStatus(status); // 已保存状态
		intoOrOutStorage.setType(type); // 标记为出库
		intoOrOutStorage.setSn(Sequence.getInstance().getSequence("C"));
		intoOrOutStorage.setStorage(storage);
		intoOrOutStorage.setOrderId(orderId);
		if(type == 1){
			intoOrOutStorage.setOutDate(new Date());			
		}else if(type ==0){
			intoOrOutStorage.setEnterDate(new Date());
		}
		// 处理产品项（出入库项）
		List<IntoOrOutStorageItem> products = new ArrayList<IntoOrOutStorageItem>();
		// List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
		for (Map<String, Object> c : list) {
			IntoOrOutStorageItem sso = new IntoOrOutStorageItem();
			Product product = productBaseService.find(Long.parseLong(c.get("product").toString()));
			BigDecimal number = new BigDecimal(c.get("number").toString());
			sso.setProduct(product);
			sso.setNumber(number);
			sso.setIntoOrOutStorage(intoOrOutStorage);
			products.add(sso);
		}
		intoOrOutStorage.setIntoOtOutPro(products);
		this.save(intoOrOutStorage);
	}

	@Override
	public void Icheck_wf(IntoOrOutStorage intoOrOutStorage,Integer status) {
		List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
        Store store = intoOrOutStorage.getStorage().getStore();
        //把入库的商品写入到库存中
        for (int i = 0; i < products.size(); i++) {
            Product product = products.get(i).getProduct();
            // 先判断该经销商是否有该商品，有则更新（增加）入库数量，没有则新增一条记录
            List<Filter> filter = new ArrayList<Filter>();
            filter.add(Filter.eq("storage", intoOrOutStorage.getStorage().getId()));
            filter.add(Filter.eq("product", product.getId()));
            filter.add(Filter.eq("store", store));
            StorageProduct storageProduct = storageProductService.find(filter);
            if (storageProduct != null && storageProduct.getId() != null) {
                storageProduct.setInventory(storageProduct.getInventory().add(products.get(i).getNumber()));
                storageProductService.update(storageProduct);
            } else {
                storageProduct = new StorageProduct();
                storageProduct.setStore(store);
                storageProduct.setProduct(product);
                storageProduct.setStorage(intoOrOutStorage.getStorage());
                storageProduct.setInventory(products.get(i).getNumber());
                storageProductService.save(storageProduct);
            }
        }
		intoOrOutStorage.setStatus(status);
		update(intoOrOutStorage);
	}
	
	public void check_wf(IntoOrOutStorage intoOrOutStorage, Long storeId){
		Store store = storeService.find(storeId);
		// 处理产品项（出入库项）
		List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
		// 审核后保存商品的入库数量到对应的经销商（xx_storage_product 表）
		for (int i = 0; i < products.size(); i++) {
			Product product = products.get(i).getProduct();
			// 先判断该经销商是否有该商品，有则更新（增加）入库数量，没有则新增一条记录
			List<Filter> filter = new ArrayList<Filter>();
			filter.add(Filter.eq("storage", intoOrOutStorage.getStorage().getId()));
			filter.add(Filter.eq("product", product.getId()));
			filter.add(Filter.eq("store", store.getId()));
			StorageProduct storageProduct = storageProductService.find(filter);
			if (storageProduct != null && storageProduct.getId() != null) {
				storageProduct.setInventory(storageProduct.getInventory().add(products.get(i).getNumber()));
				storageProductService.update(storageProduct);
			} else {
				storageProduct = new StorageProduct();
				storageProduct.setStore(store);
				storageProduct.setProduct(product);
				storageProduct.setStorage(intoOrOutStorage.getStorage());
				storageProduct.setInventory(products.get(i).getNumber());
				storageProductService.save(storageProduct);
			}
		}
		IntoOrOutStorage i = find(intoOrOutStorage.getId());
		i.setStatus(1);
		this.update(i);
	}
	
	public BigDecimal findInventory(Long id){
		return intoOrOutStorageDao.findInventory(id);
	}

	@Override
	public void Ocheck_wf(IntoOrOutStorage intoOrOutStorage, Integer status) {
		List<IntoOrOutStorageItem> products = intoOrOutStorage.getIntoOtOutPro();
		intoOrOutStorage.setIntoOtOutPro(products);
        Store store = intoOrOutStorage.getStorage().getStore();
        //减去库存
        for (int i = 0; i < products.size(); i++) {
            Product product = products.get(i).getProduct();
            // 先判断该经销商是否有该商品，有则更新（减去）出库数量，没有则抛出异常
            List<Filter> filter = new ArrayList<Filter>();
            filter.add(Filter.eq("storage", intoOrOutStorage.getStorage().getId()));
            filter.add(Filter.eq("product", product.getId()));
            filter.add(Filter.eq("store", store));
            StorageProduct storageProduct = storageProductService.find(filter);
            if (storageProduct != null && storageProduct.getId() != null) {
            	if(storageProduct.getInventory().compareTo(products.get(i).getNumber())>=0){
            		storageProduct.setInventory(storageProduct.getInventory().subtract(products.get(i).getNumber()));
            		storageProductService.update(storageProduct);            		
            	}else{
            		ExceptionUtil.throwServiceException("产品："+product.getVonderCode()+"库存不足！");
            	}
            } else {
            	ExceptionUtil.throwServiceException("产品："+product.getVonderCode()+"在仓库中不存在");
            }
        }
        intoOrOutStorage.setStatus(status);
        update(intoOrOutStorage);
	}
	
}
