package net.shopxx.shop.service.impl;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.shop.dao.ShopReimburseDao;
import net.shopxx.shop.entity.ShopReimburse;
import net.shopxx.shop.service.AcceptanceReimburseService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.shop.service.ShopReimburseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service("shopReimburseServiceImpl")
public class ShopReimburseServiceImpl extends BaseServiceImpl<ShopReimburse> implements ShopReimburseService {
	
	@Resource(name = "shopReimburseDao")
	private ShopReimburseDao shopReimburseDao;
	@Resource(name = "acceptanceReimburseServiceImpl")
	private AcceptanceReimburseService acceptanceReimburseService;
	@Resource(name = "shopInfoServiceImpl")
	private ShopInfoService shopInfoService;
	
	public Page<Map<String, Object>> findPage(List<Object> param,Long shopInfoId, Pageable pageable){
		return shopReimburseDao.findPage(param, shopInfoId, pageable);
	}

	@Override
	public void saveShopReimburse(ShopReimburse shopReimburse, Long acceptanceReimburseId, Long shopInfoId) {
		shopReimburse.setShopInfo(shopInfoService.find(shopInfoId));
		shopReimburse.setAcceptanceReimburse(acceptanceReimburseService.find(acceptanceReimburseId));
		save(shopReimburse);
	}

	@Override
	public void updateShopReimburse(ShopReimburse shopReimburse) {
		ShopReimburse sr = find(shopReimburse.getId());
		shopReimburse.setShopInfo(sr.getShopInfo());
		shopReimburse.setAcceptanceReimburse(sr.getAcceptanceReimburse());
		update(shopReimburse);
	}

	@Override
	public Integer count(List<Object> param) {
		return shopReimburseDao.count(param);
	}

	@Override
	public List<Map<String, Object>> findItemList(List<Object> param, Integer page, Integer size) {
		return shopReimburseDao.findItemList(param, page, size);
	}
	
}
