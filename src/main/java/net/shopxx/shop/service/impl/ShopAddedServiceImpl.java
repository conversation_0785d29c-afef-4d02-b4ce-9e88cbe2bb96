package net.shopxx.shop.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfService;
import net.shopxx.act.service.impl.ActWfBillServiceImpl;
import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.basic.service.SaleOrgBaseService;
import net.shopxx.member.dao.StoreBaseDao;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.entity.StoreMemberSaleOrg;
import net.shopxx.member.service.StoreBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.shop.dao.ShopAddedDao;
import net.shopxx.shop.entity.Shop;
import net.shopxx.shop.entity.Shop.Status;
import net.shopxx.shop.entity.ShopAttach;
import net.shopxx.shop.service.ShopAddedService;
import net.shopxx.shop.service.ShopInfoService;
import net.shopxx.util.SnUtil;
import net.shopxx.wf.service.WfBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;

@Service("shopAddedServiceImpl")
public class ShopAddedServiceImpl extends ActWfBillServiceImpl<Shop> implements ShopAddedService {

    @Resource(name = "shopAddedDao")
    private ShopAddedDao shopAddedDao;
    @Resource(name = "storeBaseServiceImpl")
    private StoreBaseService storeBaseService;
    @Resource(name = "saleOrgBaseServiceImpl")
    private SaleOrgBaseService saleOrgService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberBaseService;
    @Resource(name = "storeBaseDao")
    private StoreBaseDao storeBaseDao;
    @Resource(name = "wfObjConfigLineBaseServiceImpl")
    private WfObjConfigLineBaseService wfObjConfigLineBaseService;
    @Resource(name = "wfBaseServiceImpl")
    private WfBaseService wfBaseService;
    @Resource(name = "storeMemberBaseServiceImpl")
    private StoreMemberBaseService storeMemberService;
    @Resource(name = "shopInfoServiceImpl")
    private ShopInfoService shopInfoService;
    @Resource(name = "storeMemberSaleOrgBaseServiceImpl")
    private StoreMemberSaleOrgBaseService storeMemberSaleOrgBaseService;
    @Resource(name = "actWfServiceImpl")
    private ActWfService actWfService;

    @Override
    public void shopAddedSave(Shop shop, Long storeId, Long saleOrgId, String[] businessCategory, String[] shopSign) {
        String sn = SnUtil.generateSn();
        shop.setSn(sn);
        shop.setStatuss(Status.saved);
        shop.setStore(storeBaseService.find(storeId));
        shop.setSaleOrg(saleOrgService.find(saleOrgId));
        shop.setBusinessCategory(splicer(businessCategory));
        shop.setShopSign(splicer(shopSign));
        shop.setStoreMember(storeMemberService.getCurrent());
        // 门店附件
        List<ShopAttach> shopFarAttachs = shop.getShopFarAttachs();
        for (Iterator<ShopAttach> iterator = shopFarAttachs.iterator(); iterator.hasNext(); ) {
            ShopAttach shopFarAttach = iterator.next();
            if (shopFarAttach == null || shopFarAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (shopFarAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            shopFarAttach.setFileName(shopFarAttach.getName() + "." + shopFarAttach.getSuffix());
            shopFarAttach.setShop(shop);
            shopFarAttach.setStoreMember(storeMemberService.getCurrent());
        }
        shop.setShopFarAttachs(shopFarAttachs);

        List<ShopAttach> shopDecorateAttachs = shop.getShopDecorateAttachs();
        for (Iterator<ShopAttach> iterator = shopDecorateAttachs.iterator(); iterator.hasNext(); ) {
            ShopAttach shopDecorateAttach = iterator.next();
            if (shopDecorateAttach == null || shopDecorateAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (shopDecorateAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            shopDecorateAttach.setFileName(shopDecorateAttach.getName() + "." + shopDecorateAttach.getSuffix());
            shopDecorateAttach.setShop(shop);
            shopDecorateAttach.setStoreMember(storeMemberService.getCurrent());
        }
        shop.setShopDecorateAttachs(shopDecorateAttachs);

        List<ShopAttach> payPlatformeAttachs = shop.getPayPlatformAttachs();
        for (Iterator<ShopAttach> iterator = payPlatformeAttachs.iterator(); iterator.hasNext(); ) {
            ShopAttach payPlatformeAttach = iterator.next();
            if (payPlatformeAttach == null || payPlatformeAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (payPlatformeAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            payPlatformeAttach.setFileName(payPlatformeAttach.getName() + "." + payPlatformeAttach.getSuffix());
            payPlatformeAttach.setShop(shop);
            payPlatformeAttach.setStoreMember(storeMemberService.getCurrent());
        }
        shop.setPayPlatformAttachs(payPlatformeAttachs);

        List<ShopAttach> plateAttachs = shop.getPlateAttachs();
        for (Iterator<ShopAttach> iterator = plateAttachs.iterator(); iterator.hasNext(); ) {
            ShopAttach plateAttach = iterator.next();
            if (plateAttach == null || plateAttach.getUrl() == null) {
                iterator.remove();
                continue;
            }
            if (plateAttach.getName() == null) {
                ExceptionUtil.throwServiceException("附件名不能为空");
            }
            plateAttach.setFileName(plateAttach.getName() + "." + plateAttach.getSuffix());
            plateAttach.setShop(shop);
            plateAttach.setStoreMember(storeMemberService.getCurrent());
        }
        shop.setPlateAttachs(plateAttachs);
        save(shop);
    }

    @Override
    public void shopAddedUpdate(Shop shop, Long storeId, Long saleOrgId, String[] businessCategory) {
        Shop s = find(shop.getId());
        s.setStore(storeBaseService.find(storeId));
        s.setSaleOrg(saleOrgService.find(saleOrgId));
        s.setBusinessCategory(splicer(businessCategory));
        s.setStatuss(Shop.Status.saved);
        s.setNewShopArea(shop.getNewShopArea());
        s.setNewShopAddress(shop.getNewShopAddress());
        s.setPartnerName(shop.getPartnerName());
        s.setPartnerPhone(shop.getPartnerPhone());
        s.setCreateShopDate(shop.getCreateShopDate());
        s.setShopAcreage(shop.getShopAcreage());
        s.setShopLocation(shop.getShopLocation());
        s.setCompanyNature(shop.getCompanyNature());
        s.setShopOwnership(shop.getShopOwnership());
        s.setMonthlyRent(shop.getMonthlyRent());
        s.setIsParticipateDesign(shop.getIsParticipateDesign());
        s.setShopName(shop.getShopName());

//		List<ShopAttach> shopAttachs = shop.getShopAttachs();
//		Iterator<ShopAttach> shopIterator = shopAttachs.iterator();
//		while (shopIterator.hasNext()) {
//			ShopAttach shopAttach = shopIterator.next();
//			if (shopAttach == null || shopAttach.getUrl() == null) {
//				shopIterator.remove();
//				continue;
//			}
//			if (shopAttach.getName() == null) {
//				ExceptionUtil.throwServiceException("附件名不能为空");
//			}
//			shopAttach.setFileName(shopAttach.getName() + "." + shopAttach.getSuffix());
//			shopAttach.setShop(s);
//			shopAttach.setStoreMember(storeMemberService.getCurrent());
//		}
//		s.getShopAttachs().clear();
//		s.getShopAttachs().addAll(shopAttachs);
        List<ShopAttach> shopFarAttachs = shop.getShopFarAttachs();
        Iterator<ShopAttach> shopFarIterator = shopFarAttachs.iterator();
        while (shopFarIterator.hasNext()) {
            ShopAttach shopFarAttach = shopFarIterator.next();
            if (shopFarAttach == null || shopFarAttach.getUrl() == null) {
                shopFarIterator.remove();
                continue;
            }
            if (shopFarAttach.getName() == null) {
                ExceptionUtil.throwServiceException("门头远近距离照附件名不能为空");
            }
            shopFarAttach.setFileName(shopFarAttach.getName() + "." + shopFarAttach.getSuffix());
            shopFarAttach.setShop(s);
            shopFarAttach.setStoreMember(storeMemberService.getCurrent());
        }
        s.getShopFarAttachs().clear();
        s.getShopFarAttachs().addAll(shopFarAttachs);

        List<ShopAttach> shopDecorateAttachs = shop.getShopDecorateAttachs();
        Iterator<ShopAttach> shopDecorateIterator = shopDecorateAttachs.iterator();
        while (shopDecorateIterator.hasNext()) {
            ShopAttach shopDecorateAttach = shopDecorateIterator.next();
            if (shopDecorateAttach == null || shopDecorateAttach.getUrl() == null) {
                shopDecorateIterator.remove();
                continue;
            }
            if (shopDecorateAttach.getName() == null) {
                ExceptionUtil.throwServiceException("门头远近距离照附件名不能为空");
            }
            shopDecorateAttach.setFileName(shopDecorateAttach.getName() + "." + shopDecorateAttach.getSuffix());
            shopDecorateAttach.setShop(s);
            shopDecorateAttach.setStoreMember(storeMemberService.getCurrent());
        }
        s.getShopDecorateAttachs().clear();
        s.getShopDecorateAttachs().addAll(shopDecorateAttachs);

        List<ShopAttach> payPlatformAttachs = shop.getPayPlatformAttachs();
        Iterator<ShopAttach> payPlatformIterator = payPlatformAttachs.iterator();
        while (payPlatformIterator.hasNext()) {
            ShopAttach payPlatformAttach = payPlatformIterator.next();
            if (payPlatformAttach == null || payPlatformAttach.getUrl() == null) {
                payPlatformIterator.remove();
                continue;
            }
            if (payPlatformAttach.getName() == null) {
                ExceptionUtil.throwServiceException("收银台附件名不能为空");
            }
            payPlatformAttach.setFileName(payPlatformAttach.getName() + "." + payPlatformAttach.getSuffix());
            payPlatformAttach.setShop(s);
            payPlatformAttach.setStoreMember(storeMemberService.getCurrent());
        }
        s.getPayPlatformAttachs().clear();
        s.getPayPlatformAttachs().addAll(payPlatformAttachs);

        List<ShopAttach> plateAttachs = shop.getPlateAttachs();
        Iterator<ShopAttach> plateIterator = plateAttachs.iterator();
        while (plateIterator.hasNext()) {
            ShopAttach plateAttach = plateIterator.next();
            if (plateAttach == null || plateAttach.getUrl() == null) {
                plateIterator.remove();
                continue;
            }
            if (plateAttach.getName() == null) {
                ExceptionUtil.throwServiceException("各样板区附件名不能为空");
            }
            plateAttach.setFileName(plateAttach.getName() + "." + plateAttach.getSuffix());
            plateAttach.setShop(s);
            plateAttach.setStoreMember(storeMemberService.getCurrent());
        }
        s.getPlateAttachs().clear();
        s.getPlateAttachs().addAll(plateAttachs);
        this.update(s);
    }


    public String splicer(String[] str) {
        String s = "";
        if (str != null && str.length > 0) {
            for (int i = 0; i < str.length; i++) {
                if (str.length - 1 == i) {
                    s += str[i];
                } else {
                    s += str[i] + ",";
                }
            }
        }
        return s;
    }

    @Override
    public List<Map<String, Object>> findShopAttach(Long id) {
        return shopAddedDao.findShopAttach(id);
    }

    @Override
    public Store findStore(CompanyInfo companyInfo) {
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        Long member = storeMember.getMember().getId();
        Store store = null;
        String sql = "select s.* from xx_store s left join xx_store_member sm on sm.store=s.id where s.type!=0"
                + " and s.company_info_id = " + companyInfo.getId() + " and sm.member = " + member
                + " order by s.create_date desc";
        List<Map<String, Object>> map = storeBaseDao.getNativeDao().findListMap(sql, null, 0);
        if (map != null && map.size() > 0) {
            Long id = Long.parseLong(map.get(0).get("id").toString());
            store = storeBaseService.find(id);
        }
        return store;
    }

    @Override
    public Page<Map<String, Object>> findPage2(Shop shop,List<Object> param, Pageable pageable) {
        return shopAddedDao.findPage2(shop,param, pageable);
    }

    /**
     * 创建流程实例
     */
    @Override
    @Transactional
    public void createWf(Long id, String modelId, Long objTypeId) {
        Shop shop = find(id);
        if (shop.getWfId() != null) {
            ExceptionUtil.throwServiceException("该单据已审核，请勿重复操作！");
        }
        StoreMember storeMember = storeMemberBaseService.getCurrent();
        // 创建流程实例
//		createWf(shop.getSn(), String.valueOf(storeMember.getId()), modelId, objTypeId, id,
//				WebUtils.getCurrentCompanyInfoId());
//		createWf(shop.getSn(), String.valueOf(storeMember.getId()),
//				new Long[]{shop.getSaleOrg().getId()},
//				modelId,
//				objTypeId,
//				id,
//				WebUtils.getCurrentCompanyInfoId());
        createWf(shop.getSn(), String.valueOf(storeMember.getId()),
                new Long[]{shop.getSaleOrg().getId()},
                shop.getStore().getId(),
                modelId,
                objTypeId,
                id,
                WebUtils.getCurrentCompanyInfoId(),true);

        shop.setStatuss(Status.underway);
        update(shop);
    }

    /**
     * 中断流程回调
     */
    public void interruptBack(ActWf wf) {
        Shop s = find(wf.getObjId());
        s.setStatuss(Status.saved);
        update(s);
    }

    /**
     * 流程结束回调
     */
    @Override
    public void endBack(ActWf arg0) {
        super.endBack(arg0);
        Shop shop = find(arg0.getObjId());
        shop.setStatuss(Status.submitted);
        update(shop);
        shopInfoService.callbackShop(shop);
    }

    @Override
    public void saveform(Shop shop, String[] shopSign, String shopType, String administrativeRank, String viVersion,
                         String salesChannel, Integer type) {
        Shop s = find(shop.getId());
        // type 1区域经理 2省长 3渠道部
        if (type == 1) {
            if (shop.getShopType() == null) {
                ExceptionUtil.throwServiceException("请选择门店类型");
            }
            if (shop.getAdministrativeRank() == null) {
                ExceptionUtil.throwServiceException("请选择城市行政等级");
            }
            if (shop.getViVersion() == null) {
                ExceptionUtil.throwServiceException("请选择VI版本");
            }
            if (shop.getShopSign() == null) {
                ExceptionUtil.throwServiceException("请选择所含品牌");
            }
            if (shop.getSalesChannel() == null) {
                ExceptionUtil.throwServiceException("请选择销售渠道");
            }
            if(shop.getDealerSex() == null){
				ExceptionUtil.throwServiceException("请选择性别");
			}
            s.setShopType(shop.getShopType());
            s.setShopSign(splicer(shopSign));
            s.setAdministrativeRank(shop.getAdministrativeRank());
            s.setViVersion(shop.getViVersion());
            s.setDepartment(shop.getDepartment());
            s.setBelongBrand(shop.getBelongBrand());
            s.setSalesChannel(shop.getSalesChannel());
            s.setDealerSex(shop.getDealerSex());
            // 表单验证
        }
        if (type == 2) {
            if (shop.getSzyj() == null) {
                ExceptionUtil.throwServiceException("请填写省长意见");
            }
            s.setSzyj(shop.getSzyj());
        }
        if (type == 3) {
            if (s.getIsParticipateDesign()) {
                s.setArchivesCodes(null);
                s.setNewTime(null);
                s.setShopAuthorizationCodes(null);
            } else {
                s.setArchivesCodes(shop.getArchivesCodes());
                s.setNewTime(shop.getNewTime());
                s.setShopAuthorizationCodes(shop.getShopAuthorizationCodes());
            }
            if (shop.getShopCaseNote() == null) {
                ExceptionUtil.throwServiceException("请填写门店情况备注");
            }
            s.setShopCaseNote(shop.getShopCaseNote());
        }
        this.update(s);
    }

    @Override
    public List<String> findPost(StoreMember storeMember) {
        List<Filter> filters = new ArrayList<Filter>();
        // 查机构
        filters.add(Filter.eq("storeMember", storeMember));
        List<StoreMemberSaleOrg> ssom = storeMemberSaleOrgBaseService.findList(null, filters, null);
        List<String> SaleOrgList = new ArrayList<String>();
        List<String> postName = new ArrayList<String>();
        String saleOrgs = "";
        if (SaleOrgList != null) {
            for (StoreMemberSaleOrg s : ssom) {
                SaleOrgList.add(s.getSaleOrg().getId().toString());
            }
            saleOrgs = splicer(SaleOrgList.toArray(new String[SaleOrgList.size()]));
        }
        // 查岗位
        List<Map<String, Object>> posts = storeMemberSaleOrgBaseService.findListBySaleOrg(storeMember.getId(),
                saleOrgs);
        for (Map<String, Object> p : posts) {
            if (p.get("name") != null) {
                postName.add(p.get("name").toString());
            }
        }
        return postName;
    }

    public List<Map<String, Boolean>> taskItem(ActWf wf, String... str) {
        List<Map<String, Boolean>> list = new ArrayList<Map<String, Boolean>>();
        List<Map<String, Object>> item = actWfService.getWfProcList(wf.getProcInstId());
        // 查找当前流程明细
        for (Map<String, Object> c : item) {
            Map<String, Boolean> map = new HashMap<String, Boolean>();
            if (c.get("suggestion") != null) {
                // 处理结果
                String approved = c.get("approved") != null ? c.get("approved").toString() : "false";
                // 节点名称
                String rwm = c.get("activityName") != null ? c.get("activityName").toString() : "";
                for (int i = 0; i < str.length; i++) {
                    // 对比节点名称是否对应
                    // System.out.println(str[i]+":"+rwm.contains(str[i]));
                    // System.out.println(rwm+"======="+str[i]);
                    if (rwm.contains(str[i].toString())) {
                        Boolean is = Boolean.valueOf(approved);
                        // 赋值处理结果到所定义的节点上
                        map.put(i + "", is);
                        if (!is) {
                            map.put(i - 1 + "", false);
                        }
                        list.add(map);
                    }
                }
            }
        }

        return list;
    }

    @Override
    public List<Map<String, Object>> findShopAttach1(Long id, Integer type) {
        return shopAddedDao.findShopAttach1(id, type);
    }

    public void completePre(ActWf wf) {
        Shop shop = find(wf.getObjId());
        Task t = this.getCurrTaskByWf(wf);
        String nodeName = t.getName();

        //区域经理必填判定
        if (nodeName.equals("区域经理审核")) {
            if (shop.getShopType() == null
                    || shop.getAdministrativeRank() == null
                    || shop.getBelongBrand() == null
                    || shop.getViVersion() == null
                    || shop.getDepartment() == null
                    || shop.getSalesChannel() == null
                    || shop.getDealerSex() == null
            ) {
                ExceptionUtil.throwServiceException("请填完整区域经理意见");
            }

        }

        if (nodeName.equals("省长审核")) {
            if (shop.getSzyj() == null) {
                ExceptionUtil.throwServiceException("请填写省长意见");
            }
        }

    }


}
