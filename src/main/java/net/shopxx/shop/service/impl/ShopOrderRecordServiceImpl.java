package net.shopxx.shop.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.product.entity.Product;
import net.shopxx.product.service.ProductBaseService;
import net.shopxx.shop.dao.ShopOrderDao;
import net.shopxx.shop.entity.SetStorage;
import net.shopxx.shop.entity.ShopOrderRecord;
import net.shopxx.shop.entity.ShopShippingItem;
import net.shopxx.shop.entity.StorageProduct;
import net.shopxx.shop.service.IntoOrOutStorageService;
import net.shopxx.shop.service.SetStorageService;
import net.shopxx.shop.service.ShopOrderRecordService;
import net.shopxx.shop.service.StorageProductService;

@Service("shopOrderRecordServiceImpl")
public class ShopOrderRecordServiceImpl extends BaseServiceImpl<ShopOrderRecord> implements ShopOrderRecordService {

	@Resource(name = "shopOrderDao")
	private ShopOrderDao shopOrderDao;
	@Resource(name = "storeMemberBaseServiceImpl")
	private StoreMemberBaseService storeMemberBaseService;
	@Resource(name = "storageProductServiceImpl")
	private StorageProductService storageProductService;
	@Resource(name = "intoOrOutStorageServiceImpl")
	private IntoOrOutStorageService intoOrOutStorageService;
	@Resource(name = "setStorageServiceImpl")
	private SetStorageService setStorageService;
	@Resource(name = "productBaseServiceImpl")
	private ProductBaseService productBaseService;

	@Override
	@Transactional(readOnly = true)
	public void shopOrderRecordSave(ShopOrderRecord shopOrderRecord, Long orderId, Integer type, BigDecimal price,
			String description, BigDecimal quantity, String memo, String name,Long[] ids) {
		String StoreMemberName = storeMemberBaseService.getCurrent().getName();
		if (type == 2) {
			// 发货
			List<ShopShippingItem> ssi = shopOrderRecord.getShippingItems();
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			Map<String, Long> maps = new HashMap<String, Long>();
			SetStorage storage1 = null;
			// 库存操作
			StorageProduct sp = null;
			for (Iterator<ShopShippingItem> iterator = ssi.iterator(); iterator.hasNext();) {
				ShopShippingItem si = iterator.next();
				if(!Arrays.asList(ids).contains(si.getOrderItemId())){
					continue;
				}
				Map<String, Object> map = new HashMap<String, Object>();
				// 定义订单发货数量
				BigDecimal s = BigDecimal.ZERO;
				// 定义下单数量
				BigDecimal q = BigDecimal.ZERO;
				// 定义库存数量
				BigDecimal i = BigDecimal.ZERO;
				if (si.getShippingBranchQuantity() != null) {
					if (BigDecimal.ZERO.compareTo(si.getShippingBranchQuantity().divideAndRemainder(BigDecimal.ONE)[1]) != 0) {
						ExceptionUtil.throwServiceException("支数不能为小数！");
					}
				}
				if (si.getShippingBoxQuantity() != null) {
					if (BigDecimal.ZERO.compareTo(si.getShippingBoxQuantity() .divideAndRemainder(BigDecimal.ONE)[1]) != 0) {
						ExceptionUtil.throwServiceException("箱数不能为小数！");
					}
				}
				// 获取仓库
				SetStorage storage = setStorageService.find(si.getStorageId());
				// 获取商品
				Product product = productBaseService.find(si.getProductId());
				if (storage == null) {
					ExceptionUtil.throwServiceException("仓库不能为空！");
				}
				storage1 = storage;
				maps.put(storage.getId().toString(), storage.getId());
				if (maps.size() > 1) {
					ExceptionUtil.throwServiceException("仓库必须相同！");
				}
				// 产品分类
				String category = "";
				if (si.getOrderItemId() == null) {
					iterator.remove();
					continue;
				} else {
					// 查找订单明细
					List<Map<String, Object>> orderItems = shopOrderDao.findOrderItem(null, si.getOrderItemId(), null);
					for (Map<String, Object> oi : orderItems) {
						if (oi.get("quantity") != null) {
							q = new BigDecimal(oi.get("quantity").toString());
						}
						if (oi.get("shipped_quantity") != null) {
							s = new BigDecimal(oi.get("shipped_quantity").toString());
						}
						if (oi.get("pc_name") != null) {
							category = oi.get("pc_name").toString();
						} else if (oi.get("super_name") != null) {
							category = oi.get("super_name").toString();
						}
					}
				}
				if (q.compareTo(si.getShippingQuantity()) < 0) {
					ExceptionUtil.throwServiceException("发货数量不能大于下单数量！");
				}
				if (s.compareTo(BigDecimal.ZERO) == 0) {
					s = si.getShippingQuantity();
				} else {
					s = s.add(si.getShippingQuantity());
					if (s.compareTo(q) > 0) {
						ExceptionUtil.throwServiceException("发货数量不能大于下单数量！");
					}
				}
				List<Filter> filters = new ArrayList<Filter>();
				filters.add(Filter.eq("storage", storage));
				filters.add(Filter.eq("product", product));
				List<StorageProduct> spli = storageProductService.findList(null, filters, null);
				if (spli != null && spli.size() > 0) {
					sp = spli.get(0);
					i = sp.getInventory();
				} else {
					ExceptionUtil.throwServiceException("商品编码:" + product.getVonderCode() + "没有库存！");
				}
				if (s.compareTo(i) > 0) {
					ExceptionUtil.throwServiceException("商品编码:" + product.getVonderCode() + "库存不足！");
				}
				si.setOrderItemId(si.getOrderItemId());
				si.setShippingQuantity(si.getShippingQuantity());
				si.setVonderCode(si.getVonderCode());
				si.setDescription(si.getDescription());
				si.setShopOrderRecord(shopOrderRecord);
				si.setCategory(category);
				si.setType(type);
				shopOrderDao.updateOrderItemShippingQuantity(si.getOrderItemId(), s);
				// 保存完发货单进行减去库存操作:生成一张出库单
				map.put("product", product.getId());
				map.put("number", si.getShippingQuantity());
				list.add(map);
			}
			shopOrderRecord.setShippingItems(ssi);
			shopOrderRecord.setName(StoreMemberName);
			save(shopOrderRecord);
			intoOrOutStorageService.createrOutStorage(0, 1, orderId, storage1, list);
		} else if (type == 3) {
			// 退货
			List<ShopShippingItem> ssi = shopOrderRecord.getShippingItems();
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			Map<String, Long> maps = new HashMap<String, Long>();
			SetStorage storage1 = null;
			for (Iterator<ShopShippingItem> iterator = ssi.iterator(); iterator.hasNext();) {
				Map<String, Object> map = new HashMap<String, Object>();
				// 定义订单发货数量
				BigDecimal s = BigDecimal.ZERO;
				// 定义订单退货数量
				BigDecimal r = BigDecimal.ZERO;
				BigDecimal i = BigDecimal.ZERO;
				ShopShippingItem si = iterator.next();
				// 获取仓库
				SetStorage storage = setStorageService.find(si.getStorageId());
				// 获取商品
				Product product = productBaseService.find(si.getProductId());
				if (storage == null) {
					ExceptionUtil.throwServiceException("仓库不能为空！");
				}
				storage1 = storage;
				maps.put(storage.getId().toString(), storage.getId());
				if (maps.size() > 1) {
					ExceptionUtil.throwServiceException("仓库必须相同！");
				}
				// 产品分类
				String category = "";
				if (si.getOrderItemId() == null) {
					iterator.remove();
					continue;
				} else {
					// 查找订单明细
					List<Map<String, Object>> orderItems = shopOrderDao.findOrderItem(null, si.getOrderItemId(), null);
					for (Map<String, Object> oi : orderItems) {
						if (oi.get("shipped_quantity") != null) {
							s = new BigDecimal(oi.get("shipped_quantity").toString());
						}
						if (oi.get("return_quantity") != null) {
							r = new BigDecimal(oi.get("return_quantity").toString()).add(si.getShippingQuantity());
							if (r.compareTo(s) > 0) {
								ExceptionUtil.throwServiceException("退货数量不能大于发货数量！");
							}
						}
						if (oi.get("pc_name") != null) {
							category = oi.get("pc_name").toString();
						} else if (oi.get("super_name") != null) {
							category = oi.get("super_name").toString();
						}
					}
				}
				// 库存操作
				StorageProduct sp = null;
				List<Filter> filters = new ArrayList<Filter>();
				filters.add(Filter.eq("storage", storage));
				filters.add(Filter.eq("product", product));
				List<StorageProduct> spli = storageProductService.findList(null, filters, null);
				if (spli != null && spli.size() > 0) {
					sp = spli.get(0);
					i = sp.getInventory();
				} else {
					ExceptionUtil.throwServiceException("商品编码:" + product.getVonderCode() + "没有库存！");
				}
				si.setShippingQuantity(si.getShippingQuantity());
				si.setVonderCode(si.getVonderCode());
				si.setDescription(si.getDescription());
				si.setShopOrderRecord(shopOrderRecord);
				si.setCategory(category);
				si.setType(type);
				shopOrderDao.updateOrderItemReturnQuantity(si.getOrderItemId(), r);
				// 保存完退货单进行增加库存操作:生成一张入库单
				map.put("product", product.getId());
				map.put("number", si.getShippingQuantity());
				list.add(map);
			}
			shopOrderRecord.setShippingItems(ssi);
			save(shopOrderRecord);
			intoOrOutStorageService.createrOutStorage(0, 0, orderId, storage1, list);
		} else if (type == 0) {
			BigDecimal amount = shopOrderDao.findOrderAmount(orderId);
			amount = amount.add(price);
			shopOrderDao.updateOrderAmount(orderId, amount);
			// 收款
			shopOrderRecord.setOrderId(orderId);
			shopOrderRecord.setType(type);
			shopOrderRecord.setPrice(price);
			shopOrderRecord.setDescription(description);
			shopOrderRecord.setQuantity(quantity);
			shopOrderRecord.setMemo(memo);
			shopOrderRecord.setName(StoreMemberName);
			save(shopOrderRecord);
		} else if (type == 1) {
			BigDecimal amount = shopOrderDao.findOrderAmount(orderId);
			if (amount.compareTo(price) < 0) {
				ExceptionUtil.throwServiceException("退款金额不能大于收款金额！");
			} else {
				amount = amount.subtract(price);
				shopOrderDao.updateOrderAmount(orderId, amount);
			}
			// 退款
			shopOrderRecord.setOrderId(orderId);
			shopOrderRecord.setType(type);
			shopOrderRecord.setPrice(price);
			shopOrderRecord.setDescription(description);
			shopOrderRecord.setQuantity(quantity);
			shopOrderRecord.setMemo(memo);
			shopOrderRecord.setName(StoreMemberName);
			save(shopOrderRecord);
		}

	}

}
