package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.act.entity.ActWf;
import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.basic.entity.CompanyInfo;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.shop.entity.Shop;

public interface ShopAddedService extends ActWfBillService<Shop> {

	void shopAddedSave(Shop shop, Long storeId, Long saleOrgId, String[] businessCategory, String[] shopSign);

	void shopAddedUpdate(Shop shop, Long storeId, Long saleOrgId, String[] businessCategory);

	List<Map<String, Object>> findShopAttach(Long id);

	Store findStore(CompanyInfo companyInfo);

	String splicer(String[] str);

	Page<Map<String, Object>> findPage2(Shop shop,List<Object> param, Pageable pageable);

	/** 创建流程实例 */
	void createWf(Long id, String modelId, Long objTypeId);
	
	/** 保存流程节点 */
	void saveform(Shop shop, String[] shopSign, String shopType, String administrativeRank, String viVersion,
			String salesChannel, Integer type);

	/** 查找岗位 */
	List<String> findPost(StoreMember storeMember);

	List<Map<String, Boolean>> taskItem(ActWf wf, String... str);

	List<Map<String, Object>> findShopAttach1(Long id, Integer type);
	
}
