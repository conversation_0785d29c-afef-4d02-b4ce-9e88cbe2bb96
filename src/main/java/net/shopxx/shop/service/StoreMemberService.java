package net.shopxx.shop.service;

import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.StoreMember;

public interface StoreMemberService extends BaseService<StoreMember>{
	
	/**
	 * 查找用户分页数据
	 * @param username
	 * @param mobile
	 * @param name
	 * @param memberType
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findStoreMemberPage(Pageable pageable, Object[] args);

	/**
	 * 判断用户是否具有某些角色中的一个
	 * @param storeMember 用户
	 * @param roleName 角色名
	 * */
	Boolean hasRoles(StoreMember storeMember, String... roleName);

}
