package net.shopxx.shop.service;

import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.shop.entity.Restructuring;

public interface RestructuringService extends ActWfBillService<Restructuring>{
	
	void saveRestructuring(Restructuring rt,Long storeId,Long shopInfoId);
	
	void updateRestructuring(Restructuring rt,Long storeId,Long shopInfoId);
	
	Page<Map<String, Object>> findPage(String a, Pageable pageable);

//	void saveform(Restructuring restructuring, Integer type);
	
	void createWf(Long id, String modelId, Long objTypeId);

}
