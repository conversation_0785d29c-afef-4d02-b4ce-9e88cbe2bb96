package net.shopxx.shop.service;

import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.shop.entity.ShopDecrease;

public interface ShopDecreaseService  extends ActWfBillService<ShopDecrease>{
	
	void saveDecrease(ShopDecrease shopDecrease,Long storeId, Long shopInfoId);
	
	void updateDecrease(ShopDecrease shopDecrease,Long storeId, Long shopInfoId);
	
	Page<Map<String, Object>> findPage(String a, Pageable pageable);
	
	void saveform(ShopDecrease shopDecrease,Integer type);
	
	void createWf(Long id, String modelId, Long objTypeId);

}
