package net.shopxx.shop.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import net.shopxx.act.entity.ActWf;
import net.shopxx.shop.entity.*;

import org.springframework.web.multipart.MultipartFile;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.wf.service.WfBillBaseService;

public interface ShopInfoService extends WfBillBaseService<ShopInfo> {

	public Page<Map<String, Object>> findPage(String sn,String distributorName,
			String address,String authorizationCode,String shopStatus,
			String deviseStatus,String decorationStatus, Pageable pageable);
	
	public Page<Map<String, Object>> findSelectPage(String sn,
	        String distributorName,String address,String authorizationCode,String[] shopStatus,Integer reimburse,String transitTime,String shopName, Pageable pageable);

	public void saveShopInfo(ShopInfo shopInfo);

	public void updateShopInfo(ShopInfo shopInfo);

	public List<Map<String, Object>> findBusinessCategoryApply(ShopInfo shopInfo);

	public List<Map<String, Object>> findShopInspection(ShopInfo shopInfo);

	public List<Map<String, Object>> findShopDesign(ShopInfo shopInfo);
	
	public List<Map<String, Object>> findShopAcceptance(ShopInfo shopInfo);

	public List<Map<String, Object>> findListByStoreId(Long storeId);
	
	public List<Map<String, Object>> findRestructuring(ShopInfo shopInfo);

	/**
	 * excel导入门店资料
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public String shopInfoImport(MultipartFile multipartFile) throws Exception;

	public void checkWf(Long shopInfoId, Long objConfId);

	//统计门店数量
	public Integer countShopInfo(Long id);

	/**
	 * 导出查询数量
	 * 
	 * @param sn
	 * @param distributorName
	 * @param ids
	 * @param pageable
	 * @param page
	 * @param size
	 * @return
	 */
	public Integer count(String sn, String distributorName, Pageable pageable,
			Integer page, Integer size);

	/**
	 * 导出查询
	 * 
	 * @param sn
	 * @param distributorName
	 * @param ids
	 * @param pageable
	 * @param page
	 * @param size
	 * @return
	 */
	public List<Map<String, Object>> findItemList(String sn,
			String distributorName, Long[] ids, Integer page, Integer size);
	
	/**
	 *查询门店资料 
	 */
	public List<Map<String, Object>> findListMap();
	
	/**
	 * 更新门店经纬度
	 */
	public void updateShop(ShopInfo shopInfo);
	
	/**
	 * 更新门店轮播图
	 */
	public void updetaShopImages(ShopInfo shopInfo);
	
	/** 流程：门店新增*/
	public void callbackShop(Shop shop);
	
	/** 流程：门店减少*/
	public void decreaseShop(ShopDecrease sd);
	
	/** 流程：门店更新*/
	public void updateShopInfo(ShopAlteration shopAlteration);
	
	/** 流程：门店停业整顿*/
	public void restructuringStatus(Restructuring rt);

	/** 更新门店设计状态*/
	public void updateDeviseStatus(ShopDevise shopDevise);

	/** 更新门店装修*/
	public void updateDecorationStatus(AcceptanceReimburse acceptanceReimburse);
	
	List<Map<String,Object>> findDevise(String id,String reimburses);

	public List<Map<String, Object>> findShopList(String sn,
			String distributorName, Long[] ids, Integer page, Integer size);

	public List<Map<String, Object>> findListBy(Long id);

	public Map<String, Object> findById (Long shopInfoId);

    /**
     * 查询客户名下所有门店
     * @param storeId
     * @return
     */
    public List<Map<String, Object>> findStoreByShopInfo(Long storeId);

    /**
     * 门店批量停业整顿操作
     *
     * @param shopInfo
     *            门店
     * @param str
     *            停业整顿原因
     * @param reorganizeDate
     *            经销商退出时间=停业整顿时间
     * @return
     */
    public void restructuring(List<ShopInfo> shopInfo, String str, Date reorganizeDate, ActWf wf);

    /**
     * 门店批量减少操作
     *
     * @param shopInfo
     *            门店
     * @param str
     * 			  全链路描述
     * @param wf
     * 			 流程实例
     * @return
     */
    public void decrease(List<ShopInfo> shopInfo, String str, ActWf wf);

}
