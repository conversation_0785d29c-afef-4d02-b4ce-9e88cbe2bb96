package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.member.entity.Store;
import net.shopxx.shop.entity.IntoOrOutStorage;
import net.shopxx.shop.entity.SetStorage;
import net.shopxx.wf.service.WfBillBaseService;

public interface IntoOrOutStorageService extends WfBillBaseService<IntoOrOutStorage> {

    /**
     * 入库列表数据
     * @param param
     * @param pageable
     * @return
     */
    Page<Map<String, Object>> findInPage(List<Object> param, Pageable pageable);

    /**
     * 出库列表数据
     * @param param
     * @param pageable
     * @return
     */
    Page<Map<String, Object>> findOutPage(List<Object> param, Pageable pageable);

    /**
     * 根据出入库的id来获取对应的产品项
     * @param id
     * @return
     */
    List<Map<String, Object>> findIOItemById(Long id);

    /**
     * 采购入库的数据
     * @param param
     * @param pageable
     * @return
     */
    Page<Map<String, Object>> findProPutInPage(List<Object> param, Pageable pageable);

    /**
     * 采购入库的选择列表数据
     * @param param
     * @param pageable
     * @return
     */
    Page<Map<String, Object>> findProPutInSelectPage(List<Object> param, Pageable pageable);
    

    /***
     * 保存采购入库
     * @param intoOrOutStorage
     */
    void saveCR(IntoOrOutStorage intoOrOutStorage, Long storeId);

    /***
     * 保存采购入库
     * @param intoOrOutStorage
     */
    void updateCR(IntoOrOutStorage intoOrOutStorage, Long storeId);

    List<Map<String, Object>> findProInById(Long id, Store store);

    /** 创建出库单*/
	void createrOutStorage(Integer status,Integer type,Long orderId,SetStorage storage,List<Map<String,Object>> list);
	
	/** 入库审核*/
	void Icheck_wf(IntoOrOutStorage intoOrOutStorage,Integer status);
	
	/** 采购入库审核后入库*/
	void check_wf(IntoOrOutStorage intoOrOutStorage, Long storeId);
	
	/** 出库审核*/
	void Ocheck_wf(IntoOrOutStorage intoOrOutStorage,Integer status);
}
