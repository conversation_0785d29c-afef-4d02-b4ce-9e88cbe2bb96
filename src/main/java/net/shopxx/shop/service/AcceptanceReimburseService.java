package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.shop.entity.AcceptanceReimburse;

public interface AcceptanceReimburseService extends ActWfBillService<AcceptanceReimburse> {
    /**
     * 获取验收及报销
     */
    Page<Map<String, Object>> findPage(List<Object> param, Pageable pageable);
    
    /**
     * 查找验收及报销
     */
    List<Map<String,Object>> findAcceptanceReimburse(Long id);
    
    /**
     * 新增验收及报销 
     */
    void saveAcceptanceReimburse(AcceptanceReimburse acceptanceReimburse);
    
    /**
     * 更新验收及报销 
     */
    void updateAcceptanceReimburse(AcceptanceReimburse acceptanceReimburse);

    /**
     * 附件
     */
    List<Map<String, Object>> findAttach(Long id, Integer type);

    /**
     * 附件
     */
    List<Map<String, Object>> findAttach(Long id, Integer[] type);

    /**
     * 获取验收不报销
     */
    Page<Map<String, Object>> findAcceptancePage(List<Object> param, Pageable pageable);

    /**
     * 新增验收不报销
     */
    void saveAcceptance(AcceptanceReimburse acceptanceReimburse);

    /**
     * 更新验收不报销 
     */
    void updateAcceptance(AcceptanceReimburse acceptanceReimburse);

    /**
     * 根据门店设计来生成报销单或不报销单
     * @param shopDevise
     * @param sign
     */
    void saveGenerate(AcceptanceReimburse acceptanceReimburse, Integer sign);

    /**
     * 装修及验收（不含报销）流程节点保存
     * @param type
     * @param acceptanceReimburse
     */
    void saveform(Integer type, AcceptanceReimburse acceptanceReimburse);
    
    /**
     * 装修及验收及报销流程节点保存
     * @param type
     * @param acceptanceReimburse
     */
	void saveform1(AcceptanceReimburse acceptanceReimburse, Integer[] designManagers, Integer[] directorOpinions, Integer type);

	/**
	 * 创建流程实例 
	 */
	void createWf(Long id, String modelId, Long objTypeId);

//    /**
//	 * 流程审批
//	 * @param objConfId
//	 */
//	public void checkAcceptanceWf(Long id, Long objConfId);
	
//	/**
//	 * 流程审批 报销
//	 * @param objConfId
//	 */
//	public void checkAcceptanceReimburseWf(Long id, Long objConfId);


}
