package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;

public interface ShopMemberService extends BaseService<Object> {

	void saveShopMember(String name, Integer gender, String IdCard, String Phone, String imageName, Long[] roleId,
			Long shopInfoId, Boolean isEnabled,Integer type);

	void updateShopMember(Long storeMemberId, String name, Integer gender, String IdCard, String Phone,
			String imageName, Long[] roleId, Long shopInfoId, Boolean isEnabled);

	Page<Map<String, Object>> findPages(Pageable pageable);

	List<Map<String, Object>> findAppStoreMember(Long id);
	
	public Long addMember(Long appId,String name, Integer gender, String IdCard, String Phone, Boolean isEnabled,
			Long shopInfoId, Long[] roleId);
	//姓名，性别，身份证，电话，关联门店
	void createAppMember(String name, Integer gender, String IdCard, String Phone,Long shopInfoId);


	void deleteShopMember(Long tsm_id,Long id);

	/**
	 * 查询门店成员关联表
	 * @param id 门店资料id
	 * @return
	 */
	List<Map<String,Object>> findShopMember(Long id);
}
