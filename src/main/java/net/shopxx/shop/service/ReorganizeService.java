package net.shopxx.shop.service;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.shop.entity.Reorganize;

import java.util.List;
import java.util.Map;

public interface ReorganizeService extends ActWfBillService<Reorganize> {

    void saveReorganize(Reorganize reorganize);

    void saveform(Integer type, Reorganize reorganize);

    Page<Map<String, Object>> findPage(Reorganize r, List<Object> param, Pageable pageable);

    void updateReorganize(Reorganize reorganize, Long storeId);

    /** 创建流程实例 */
    void createWf(Long id, String modelId, Long objTypeId);

    Integer count(Reorganize r, List<Object> param);

	List<Map<String, Object>> findList(Reorganize r, List<Object> param, Pageable pageable, Integer page, Integer size);
}
