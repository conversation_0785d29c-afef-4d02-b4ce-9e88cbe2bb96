package net.shopxx.shop.service;

import java.util.List;
import java.util.Map;

import net.shopxx.act.service.ActWfBillService;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.shop.entity.ShopAlteration;

public interface ShopAlterationService extends ActWfBillService<ShopAlteration> {

	void saveAlteration(ShopAlteration shopAlteration, String[] businessCategory, Long storeId, Long shopInfoId);

	void updateAlteration(ShopAlteration shopAlteration, String[] businessCategory, Long storeId, Long shopInfoId);

	Page<Map<String, Object>> findPage(String a, Pageable pageable);

	List<Map<String, Object>> findShopAlterationAttach(Long id);

	void saveform(ShopAlteration shopAlteration, String[] shopSign, Integer Type);

	// void checkShopAlterationWf(Long id, Long objConfId);
	void setStatus(ShopAlteration sa);
	
	/** 作废*/
	void cancel(Long id);
	
	/** 页面跳转查询*/
	public List<Map<String, Object>> findSA(Long shopInfoId);
	
	/** 创建流程实例 */
	void createWf(Long id, String modelId, Long objTypeId);

	List<Map<String, Object>> findShopAttach1(Long id, Integer type);
}
