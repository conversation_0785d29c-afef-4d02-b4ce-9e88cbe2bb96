package net.shopxx.product.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.product.entity.Product;

import org.springframework.web.multipart.MultipartFile;

/**
 * Service - 商品
 */
public interface ProductBaseService extends BaseService<Product> {
 
	/**
	 * 检查产品编码是否已存在
	 * 
	 * @param sn
	 * @return
	 */
	public boolean snExists(String sn);

	/**
	 * 查询产品列表
	 * 
	 * @param sn
	 * @param name
	 * @param vonderCode
	 * @param memberId
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findShowPage(Long productCategoryId,
			String sn, String name, String vonderCode, String mod,
			Long storeId, Map<Long, List<Long>> dataMap,
			Map<Long, List<BigDecimal[]>> rangeMap, Boolean isParts,
			Integer isGift, Long memberRankId, String keyWords, String ids,
			String orderParam, String orderDirect, Long typeSystemDictId,
			Boolean isToOrder, Integer productGrade, String woodTypeOrColor,
			Long sbuId,String warehousePrice,Pageable pageable);

	/**
	 * 查询产品列表
	 * 
	 * @param sn
	 * @param name
	 * @param vonderCode
	 * @param memberId
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> newfindShowPage(String sn, String name,
			String vonderCode, Integer productGrade, Pageable pageable);

	/**
	 * 查找产品
	 * 
	 * @param productCategoryId
	 * @param sn
	 * @param vonderCode
	 * @param mod
	 * @param name
	 * @param startTime
	 * @param endTime
	 * @param isMarketable
	 * @param ids
	 * @return
	 */
	public List<Map<String, Object>> findList(Long productCategoryId,
			String sn, String vonderCode, String mod, String name,
			String startTime, String endTime, Long isMarketable, Long brandId,
			Long[] ids, Integer page, Integer size);

	/**
	 * 查询产品数量
	 * 
	 * @param productCategoryId
	 * @param sn
	 * @param vonderCode
	 * @param mod
	 * @param name
	 * @param startTime
	 * @param endTime
	 * @param isMarketable
	 * @param ids
	 * @param page
	 * @param size
	 * @return
	 */
	public Integer count(Long productCategoryId, String sn, String vonderCode,
			String mod, String name, String startTime, String endTime,
			Long isMarketable, Long brandId, Long[] ids, Integer page,
			Integer size);

	/**
	 * 产品excel导入
	 * 
	 * @param multipartFile
	 * @return
	 * @throws Exception
	 */
	public String productAddImport(MultipartFile multipartFile, Integer type)
			throws Exception;

	/**
	 * 产品excel导入康宝
	 * */
	public String productAddImportTwo(MultipartFile multipartFile, Integer type)
			throws Exception;
	
	//2019-10-14 xgc修改 添加 String isShop,String isShow 查询条件
	public Page<Map<String, Object>> findPage(Long productCategoryId,
			String sn, String vonderCode, String model, String name,
			String startTime, String endTime, Long isMarketable,
			Long warehouseId, Integer isSuit, Integer type, Long brandId,
			Long isShop,Long isShow,String sbuId,Long solidWoodOrgId, Pageable pageable);

	/**
	 * 删除
	 * 
	 * @param ids
	 */
	public void deleteProduct(Long[] ids);

	/**
	 * 根据产品id查找对应的附件信息
	 */
	public List<Map<String, Object>> findAttachListByProductId(Long id);

	public BigDecimal getCartItemTotal();
	
	/**
	 * 查询sbu
	 * */
	public List<Map<String,Object>> findProductSbuList(Long productId);
	
	
	/**
	 * 查询saleOrg
	 * */
	public List<Map<String,Object>> findProductSaleOrgList(Long productId);
	
	/**
	 * 查询经营组织
	 * @param productId
	 * @return
	 */
	public List<Map<String, Object>> findProductOrganizationList(Long productId);
	
	
	
	/**
	 * 查询经营组织列表
	 * @param productId
	 * @return
	 */
	public List<Map<String, Object>> findProductOrganizationItemList(Long productId);
	
	/**
	 * 查询产品列表
	 * 
	 * @param sn
	 * @param name
	 * @param vonderCode
	 * @param memberId
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> selectProductOptimization(Long productCategoryId,
			String sn, String name, String vonderCode, String mod,
			Long storeId, Map<Long, List<Long>> dataMap,
			Map<Long, List<BigDecimal[]>> rangeMap, Boolean isParts,
			Integer isGift, Long memberRankId, String keyWords, String ids,
			String orderParam, String orderDirect, Long typeSystemDictId,
			Boolean isToOrder, Integer productGrade, String woodTypeOrColor,
			Long sbuId,String productIds,Integer isStr,Pageable pageable,
			String productSpec,String productDescription,Long organizationId);
	
	/**
	 * 查询产品列表，移动端调用
	 * 
	 * @param sn
	 * @param name
	 * @param vonderCode
	 * @param memberId
	 * @param pageable
	 * @return
	 */
	public Page<Map<String, Object>> findShowPageForMobile(Long productCategoryId,
			String sn, String name, String vonderCode, String mod,
			Long storeId, Map<Long, List<Long>> dataMap,
			Map<Long, List<BigDecimal[]>> rangeMap, Boolean isParts,
			Integer isGift, Long memberRankId, String keyWords, String ids,
			String orderParam, String orderDirect, Long typeSystemDictId,
			Boolean isToOrder, Integer productGrade, String woodTypeOrColor,
			Long sbuId,String warehousePrice,Pageable pageable);
	
	/**
	 * 首页产品系列查询方法 
	 * 
	 */
	public Page<Map<String, Object>> findIndexProduct(Long productCategoryId,
			Long storeId, String warehousePrice);
	
	public String findCategory(Long parent);
	
	
	public Page<Map<String, Object>> selectProductList(String name,String vonderCode,
			String mod,Pageable pageable,Long sbuId,String productSpec,String productDescription,
			Long productOrganizationId,String woodTypeOrColor,Integer addType);

	public List<Map<String, Object>> findProductChannelClassification(Long id);
}