/*     */ package net.shopxx.wf.controller;
/*     */ 
/*     */

import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.LogUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.basic.service.CompanyInfoBaseService;
import net.shopxx.wf.entity.WfObjConfig;
import net.shopxx.wf.service.WfObjConfigBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Controller("wfObjConfigController")
/*     */ @RequestMapping({"/wf/wf_obj_config"})
/*     */ public class WfObjConfigController
/*     */   extends BaseController
/*     */ {
/*     */   @Resource(name = "companyInfoBaseServiceImpl")
/*     */   private CompanyInfoBaseService companyInfoBaseService;
/*     */   @Resource(name = "wfTempBaseServiceImpl")
/*     */   private WfTempBaseService wfTempBaseService;
/*     */   @Resource(name = "wfObjConfigLineBaseServiceImpl")
/*     */   private WfObjConfigLineBaseService wfObjConfigLineBaseService;
/*     */   @Resource(name = "wfObjConfigBaseServiceImpl")
/*     */   private WfObjConfigBaseService wfObjConfigBaseService;
/*     */   
/*     */   @RequestMapping(value = {"/list_tb"}, method = {RequestMethod.GET})
/*     */   public String list_tb(ModelMap model) {
/*  50 */     return "/wf/wf_obj_config/list_tb";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/list"}, method = {RequestMethod.GET})
/*     */   public String list(String objTypeName, String wfTempName, Pageable pageable, ModelMap model) {
/*  60 */     Long companyInfoId = WebUtils.getPrincipal().getCompanyinfoid();
/*  61 */     boolean isAdmin = false;
/*  62 */     if (companyInfoId == null) {
/*  63 */       isAdmin = true;
/*     */     }
/*  65 */     model.addAttribute("isAdmin", Boolean.valueOf(isAdmin));
/*     */     
/*  67 */     return "/wf/wf_obj_config/list";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"list_data"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg list_data(String objTypeName, String wfTempName, Boolean isFlow, Boolean isBill, Pageable pageable) {
/*  78 */     List<Map<String, Object>> configs = this.wfObjConfigBaseService.findPage(objTypeName, 
/*  79 */         wfTempName, 
/*  80 */         isFlow, 
/*  81 */         isBill);
/*  82 */     String jsonPage = JsonUtils.toJson(configs);
/*     */     
/*  84 */     return success(jsonPage, new Object[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/add"}, method = {RequestMethod.GET})
/*     */   public String add(ModelMap model) {
/*  93 */     model.addAttribute("wfTemps", this.wfTempBaseService.findAll());
/*  94 */     model.addAttribute("companyInfos", this.companyInfoBaseService.findAll());
/*     */     
/*  96 */     return "/wf/wf_obj_config/add";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/edit"}, method = {RequestMethod.GET})
/*     */   public String edit(Long id, ModelMap model) {
/* 105 */     WfObjConfig wfObjConfig = (WfObjConfig)this.wfObjConfigBaseService.find(id);
/* 106 */     List<Map<String, Object>> wfConfigWfTemps = this.wfObjConfigLineBaseService.getConfig(wfObjConfig.getObjTypeId(), 
/* 107 */         null);
/* 108 */     model.addAttribute("wfObjConfig", wfObjConfig);
/* 109 */     model.addAttribute("wfConfigWfTemps", JsonUtils.toJson(wfConfigWfTemps));
/*     */     
/* 111 */     return "/wf/wf_obj_config/edit";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/update"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg update(WfObjConfig wfObjConfig, Long[] wfTempId, String[] conditions) {
/* 122 */     if (ConvertUtil.isEmpty(wfObjConfig.getObjTypeName()))
/*     */     {
/* 124 */       return error("17500", new Object[0]);
/*     */     }
/* 126 */     if (wfObjConfig.getIsFlow().booleanValue() && (
/* 127 */       wfTempId == null || wfTempId.length == 0))
/*     */     {
/* 129 */       return error("17504", new Object[0]);
/*     */     }
/* 131 */     this.wfObjConfigBaseService.saveorUpdate(wfObjConfig, wfTempId, conditions);
/*     */     
/* 133 */     return success();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/get_config"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg getConfig(Long obj_type_id, Long objid) {
/* 156 */     List<Map<String, Object>> configs = new ArrayList<Map<String, Object>>();
/* 157 */     List<Map<String, Object>> list = this.wfObjConfigLineBaseService.getConfig(obj_type_id, 
/* 158 */         Boolean.valueOf(true));
/*     */     
/* 160 */     for (Map<String, Object> map : list) {
/* 161 */       String conditions = ConvertUtil.isEmpty(map.get("conditions")) ? null : 
/* 162 */         map.get("conditions").toString();
/* 163 */       if (conditions == null) {
/* 164 */         configs.add(map);
/*     */         continue;
/*     */       } 
/* 167 */       if (objid != null) {
/* 168 */         conditions = conditions.replace("[objid]", objid.toString());
/*     */       }
/* 170 */       int count = 0;
/*     */       try {
/* 172 */         count = this.wfObjConfigBaseService.getDaoCenter()
/* 173 */           .getNativeDao()
/* 174 */           .findInt(conditions, null).intValue();
/*     */       }
/* 176 */       catch (Exception e) {
/* 177 */         LogUtils.error("获取启动流程：" + conditions, e);
/*     */       } 
/* 179 */       if (count > 0) {
/* 180 */         configs.add(map);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 185 */     return success().addObjX(configs);
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\controller\WfObjConfigController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */