/*     */ package net.shopxx.wf.controller;
/*     */ 
/*     */

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Order;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.wf.entity.Wf;
import net.shopxx.wf.entity.WfProc;
import net.shopxx.wf.entity.WfProcTemp;
import net.shopxx.wf.entity.WfProcuser;
import net.shopxx.wf.service.*;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Controller("wfController")
/*     */ @RequestMapping({"/wf"})
/*     */ public class WfController
/*     */   extends BaseController
/*     */ {
/*     */   @Resource(name = "storeMemberBaseServiceImpl")
/*     */   private StoreMemberBaseService storeMemberBaseService;
/*     */   @Resource(name = "wfBaseServiceImpl")
/*     */   private WfBaseService wfBaseService;
/*     */   @Resource(name = "wfProcBaseServiceImpl")
/*     */   private WfProcBaseService wfProcBaseService;
/*     */   @Resource(name = "wfProcuserBaseServiceImpl")
/*     */   private WfProcuserBaseService wfProcuserBaseService;
/*     */   @Resource(name = "wfUserOpinionBaseServiceImpl")
/*     */   private WfUserOpinionBaseService wfUserOpinionBaseService;
/*     */   @Resource(name = "wfProcTempBaseServiceImpl")
/*     */   private WfProcTempBaseService wfProcTempBaseService;
/*     */   
/*     */   @RequestMapping(value = {"/wf"}, method = {RequestMethod.GET})
/*     */   public String wf(Long wfid, ModelMap model) {
/*  62 */     StoreMember storeMember = this.storeMemberBaseService.getCurrent();
/*  63 */     Wf wf = (Wf)this.wfBaseService.find(wfid);
/*  64 */     model.addAttribute("wf", wf);
/*  65 */     model.addAttribute("storeMember", storeMember);
/*     */     
/*  67 */     if (wf != null) {
/*  68 */       StoreMember starter = wf.getStarter();
/*  69 */       boolean isStarter = false;
/*  70 */       if (starter.equals(storeMember)) {
/*  71 */         isStarter = true;
/*     */       }
/*  73 */       model.addAttribute("isStarter", Boolean.valueOf(isStarter));
/*     */     } 
/*     */     
/*  76 */     return "/wf/wf";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/wf_view"}, method = {RequestMethod.GET})
/*     */   public String wf_view(Long wfid, ModelMap model) {
/*  85 */     StoreMember storeMember = this.storeMemberBaseService.getCurrent();
/*  86 */     Wf wf = (Wf)this.wfBaseService.find(wfid);
/*  87 */     model.addAttribute("wf", wf);
/*     */     
/*  89 */     List<Filter> filters = new ArrayList<Filter>();
/*  90 */     filters.add(Filter.eq("wf", wf));
/*  91 */     List<Order> orders = new ArrayList<Order>();
/*  92 */     orders.add(Order.asc("procSeq"));
/*  93 */     List<WfProc> wfProcs = this.wfProcBaseService.findList(null, filters, orders);
/*  94 */     model.addAttribute("wfProcs", wfProcs);
/*     */     
/*  96 */     if (wf != null) {
/*  97 */       StoreMember starter = wf.getStarter();
/*  98 */       boolean isStarter = false;
/*  99 */       if (starter.equals(storeMember)) {
/* 100 */         isStarter = true;
/*     */       }
/* 102 */       model.addAttribute("isStarter", Boolean.valueOf(isStarter));
/*     */       
/* 104 */       boolean isProcuser = false;
/* 105 */       Long currProcId = wf.getCurrProcId();
/* 106 */       if (currProcId != null) {
/* 107 */         isProcuser = this.wfProcuserBaseService.isProcuser(currProcId, 
/* 108 */             storeMember.getId());
/*     */       }
/* 110 */       model.addAttribute("isProcuser", Boolean.valueOf(isProcuser));
/*     */     } 
/*     */     
/* 113 */     return "/wf/wf_view";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/interrupt"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg interrupt(Long wfid) {
/* 123 */     Wf wf = (Wf)this.wfBaseService.find(wfid);
/* 124 */     this.wfBaseService.interrupt(wf);
/*     */     
/* 126 */     return success();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/start"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg start(Long wfid, String subject) {
/* 137 */     Wf wf = (Wf)this.wfBaseService.find(wfid);
/* 138 */     if (!ConvertUtil.isEmpty(subject)) {
/* 139 */       wf.setSubject(subject);
/*     */     }
/* 141 */     this.wfBaseService.start(wf);
/*     */     
/* 143 */     return success();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/checkwf"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg checkwf(Long wfid, Long currprocid, String opinion, Integer check_type) {
/* 157 */     StoreMember storeMember = this.storeMemberBaseService.getCurrent();
/*     */     
/* 159 */     Wf wf = (Wf)this.wfBaseService.find(wfid);
/* 160 */     if (currprocid.longValue() == 0L) {
/* 161 */       currprocid = wf.getCurrProcId();
/*     */     }
/* 163 */     WfProc currWfProc = (WfProc)this.wfProcBaseService.find(currprocid);
/*     */ 
/*     */     
/* 166 */     if (check_type.intValue() == 1) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 173 */       this.wfProcBaseService.agree(currWfProc, wf, storeMember, opinion);
/*     */     }
/* 175 */     else if (check_type.intValue() == 2) {
/* 176 */       this.wfProcBaseService.reject(currWfProc, wf, storeMember, opinion);
/*     */     } 
/* 178 */     return success();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/select_proc_user"}, method = {RequestMethod.GET})
/*     */   public String select_proc_user(Long procid, ModelMap model) {
/* 187 */     WfProc wfProc = (WfProc)this.wfProcBaseService.find(procid);
/* 188 */     model.addAttribute("wfProc", wfProc);
/* 189 */     WfProcTemp wfprocTemp = (WfProcTemp)this.wfProcTempBaseService.find(wfProc.getProcTempId());
/* 190 */     if (wfprocTemp != null && 
/* 191 */       wfprocTemp.getWfStoreTemps() != null && 
/* 192 */       wfprocTemp.getWfStoreTemps().size() > 0)
/*     */     {
/* 194 */       model.addAttribute("wfprocTempId", wfProc.getProcTempId());
/*     */     }
/*     */     
/* 197 */     List<Map<String, Object>> procusers = new ArrayList<Map<String, Object>>();
/* 198 */     List<WfProcuser> wfProcusers = wfProc.getWfProcusers();
/* 199 */     for (WfProcuser wfProcuser : wfProcusers) {
/* 200 */       Map<String, Object> map = new HashMap<String, Object>();
/* 201 */       StoreMember storeMember = wfProcuser.getStoreMember();
/* 202 */       map.put("userid", storeMember.getId());
/* 203 */       map.put("username", storeMember.getUsername());
/* 204 */       map.put("name", storeMember.getName());
/* 205 */       map.put("mobile", storeMember.getMember().getMobile());
/* 206 */       procusers.add(map);
/*     */     } 
/* 208 */     model.addAttribute("procusers", JsonUtils.toJson(procusers));
/*     */     
/* 210 */     return "/wf/select_proc_user";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/update_proc_user"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg updateProcUser(Long wfid, Long procid, Long[] userid, Integer minPerson, Boolean allPerson) {
/* 221 */     this.wfProcuserBaseService.updateProcUser(wfid, 
/* 222 */         procid, 
/* 223 */         userid, 
/* 224 */         minPerson, 
/* 225 */         allPerson);
/*     */     
/* 227 */     return success();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/get_user_opinion"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg getUserOpinion(Long wfid, Long procid) {
/* 237 */     List<Map<String, Object>> opinions = this.wfUserOpinionBaseService.findListByWf(wfid, 
/* 238 */         procid);
/*     */     
/* 240 */     String jsonPage = JsonUtils.toJson(opinions);
/*     */     
/* 242 */     return success(jsonPage, new Object[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/save_attach"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg saveAttach(Wf wf) {
/* 252 */     this.wfBaseService.saveAttach(wf);
/*     */     
/* 254 */     return success();
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\controller\WfController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */