/*    */ package net.shopxx.wf.controller;
/*    */ 
/*    */

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.wf.service.WfBaseService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Controller("wfManageController")
/*    */ @RequestMapping({"/wf/wf_manage"})
/*    */ public class WfManageController
/*    */   extends BaseController
/*    */ {
/*    */   @Resource(name = "wfBaseServiceImpl")
/*    */   private WfBaseService wfBaseService;
/*    */   
/*    */   @RequestMapping(value = {"/list"}, method = {RequestMethod.GET})
/*    */   public String list(Pageable pageable, ModelMap model) {
/* 38 */     Long userid = WebUtils.getCurrentStoreMemberId();
/* 39 */     List<Map<String, Object>> configs0 = this.wfBaseService.countList(Integer.valueOf(0), 
/* 40 */         userid, 
/* 41 */         null);
/* 42 */     List<Map<String, Object>> configs1 = this.wfBaseService.countList(Integer.valueOf(1), 
/* 43 */         userid, 
/* 44 */         null);
/* 45 */     List<Map<String, Object>> configs2 = this.wfBaseService.countList(Integer.valueOf(2), 
/* 46 */         userid, 
/* 47 */         null);
/* 48 */     List<Map<String, Object>> configs3 = this.wfBaseService.countList(Integer.valueOf(3), 
/* 49 */         userid, 
/* 50 */         null);
/* 51 */     model.addAttribute("configs0", configs0);
/* 52 */     model.addAttribute("configs1", configs1);
/* 53 */     model.addAttribute("configs2", configs2);
/* 54 */     model.addAttribute("configs3", configs3);
/*    */     
/* 56 */     return "/wf/wf_manage/list";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @RequestMapping(value = {"list_data"}, method = {RequestMethod.POST})
/*    */   @ResponseBody
/*    */   public ResultMsg list_data(Integer flag, Long objType, String wfName, Integer stat, String subject, Long storeId, Pageable pageable, ModelMap model) {
/* 69 */     Page<Map<String, Object>> page = this.wfBaseService.findPage(flag, 
/* 70 */         WebUtils.getCurrentStoreMemberId(), 
/* 71 */         objType, 
/* 72 */         wfName, 
/* 73 */         stat, 
/* 74 */         subject, 
/* 75 */         storeId, 
/* 76 */         pageable);
/* 77 */     String jsonPage = JsonUtils.toJson(page);
/*    */     
/* 79 */     return success(jsonPage, new Object[0]);
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\controller\WfManageController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */