/*     */ package net.shopxx.wf.controller;
/*     */ 
/*     */

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.controller.BaseController;
import net.shopxx.base.core.controller.ResultMsg;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.JsonUtils;
import net.shopxx.member.entity.Post;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.PostBaseService;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.member.service.StoreMemberSaleOrgBaseService;
import net.shopxx.wf.entity.WfTemp;
import net.shopxx.wf.service.*;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Controller("wfTempController")
/*     */ @RequestMapping({"/wf/wf_temp"})
/*     */ public class WfTempController
/*     */   extends BaseController
/*     */ {
/*     */   @Resource(name = "wfTempBaseServiceImpl")
/*     */   private WfTempBaseService wfTempBaseService;
/*     */   @Resource(name = "wfProcTempBaseServiceImpl")
/*     */   private WfProcTempBaseService wfProcTempBaseService;
/*     */   @Resource(name = "storeMemberBaseServiceImpl")
/*     */   private StoreMemberBaseService storeMemberBaseService;
/*     */   @Resource(name = "wfProcuserTempBaseServiceImpl")
/*     */   private WfProcuserTempBaseService wfProcuserTempBaseService;
/*     */   @Resource(name = "wfProcusersTempBaseServiceImpl")
/*     */   private WfProcusersTempBaseService wfProcusersTempBaseService;
/*     */   @Resource(name = "postBaseServiceImpl")
/*     */   private PostBaseService postBaseService;
/*     */   @Resource(name = "wfPostTempServiceImpl")
/*     */   private WfPostTempService wfPostTempService;
/*     */   @Resource(name = "storeMemberSaleOrgBaseServiceImpl")
/*     */   private StoreMemberSaleOrgBaseService storeMemberSaleOrgService;
/*     */   
/*     */   @RequestMapping(value = {"/list_tb"}, method = {RequestMethod.GET})
/*     */   public String list_tb(ModelMap model) {
/*  66 */     return "/wf/wf_temp/list_tb";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/list"}, method = {RequestMethod.GET})
/*     */   public String list(Pageable pageable, ModelMap model) {
/*  75 */     return "/wf/wf_temp/list";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"list_data"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg list_data(String wfTempName, Pageable pageable, ModelMap model) {
/*  85 */     List<Filter> filters = new ArrayList<Filter>();
/*  86 */     if (!ConvertUtil.isEmpty(wfTempName)) {
/*  87 */       filters.add(Filter.like("wfTempName", "%" + wfTempName + "%"));
/*     */     }
/*     */     
/*  90 */     Page<WfTemp> page = this.wfTempBaseService.findPage(filters, null, pageable);
/*  91 */     String jsonPage = JsonUtils.toJson(page);
/*     */     
/*  93 */     return success(jsonPage, new Object[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/add"}, method = {RequestMethod.GET})
/*     */   public String add(ModelMap model) {
/* 102 */     return "/wf/wf_temp/add";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/save"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg save(WfTemp wfTemp, ModelMap model) {
/* 112 */     if (ConvertUtil.isEmpty(wfTemp.getWfTempName()))
/*     */     {
/* 114 */       return error("17503", new Object[0]);
/*     */     }
/* 116 */     this.wfTempBaseService.saveOrUpdate(wfTemp, null);
/*     */     
/* 118 */     return success().addObjX(wfTemp.getId());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/edit"}, method = {RequestMethod.GET})
/*     */   public String edit(Long id, ModelMap model) {
/* 127 */     model.addAttribute("wfTemp", this.wfTempBaseService.find(id));
/*     */     
/* 129 */     List<Map<String, Object>> procTemps = this.wfProcTempBaseService.findListByWfTemp(id);
/* 130 */     String ids = "";
/* 131 */     for (int i = 0; i < procTemps.size(); i++) {
/* 132 */       Map<String, Object> map = procTemps.get(i);
/* 133 */       if (i == procTemps.size() - 1) {
/* 134 */         ids = String.valueOf(ids) + map.get("id");
/*     */       } else {
/*     */         
/* 137 */         ids = String.valueOf(ids) + map.get("id") + ",";
/*     */       } 
/*     */     } 
/* 140 */     List<Map<String, Object>> puts = this.wfProcuserTempBaseService.findListByProcTemp(ids);
/* 141 */     List<Map<String, Object>> procuserTemps = null;
/* 142 */     for (Map<String, Object> procTemp : procTemps) {
/* 143 */       procuserTemps = new ArrayList<Map<String, Object>>();
/* 144 */       String procTempId = procTemp.get("id").toString();
/* 145 */       for (Map<String, Object> itemMap : puts) {
/* 146 */         String pid = itemMap.get("wf_proc_temp").toString();
/* 147 */         if (procTempId.equals(pid)) {
/* 148 */           procuserTemps.add(itemMap);
/*     */         }
/*     */       } 
/* 151 */       procTemp.put("procuserTemps", procuserTemps);
/*     */     } 
/*     */     
/* 154 */     List<Map<String, Object>> wfStoreTempss = this.wfProcusersTempBaseService.findListByProcTemp(ids);
/* 155 */     List<Map<String, Object>> wfStoreTemps = null;
/* 156 */     for (Map<String, Object> procTemp : procTemps) {
/* 157 */       wfStoreTemps = new ArrayList<Map<String, Object>>();
/* 158 */       String procTempId = procTemp.get("id").toString();
/* 159 */       for (Map<String, Object> itemMap : wfStoreTempss) {
/* 160 */         String pid = itemMap.get("wf_proc_temp").toString();
/* 161 */         if (procTempId.equals(pid)) {
/* 162 */           wfStoreTemps.add(itemMap);
/*     */         }
/*     */       } 
/* 165 */       procTemp.put("wfStoreTemps", wfStoreTemps);
/*     */     } 
/*     */     
/* 168 */     List<Map<String, Object>> wfPostTempss = this.wfPostTempService.findPostsByProcTemp(ids);
/* 169 */     List<Map<String, Object>> wfPostTemps = null;
/* 170 */     for (Map<String, Object> procTemp : procTemps) {
/* 171 */       wfPostTemps = new ArrayList<Map<String, Object>>();
/* 172 */       String procTempId = procTemp.get("id").toString();
/* 173 */       for (Map<String, Object> itemMap : wfPostTempss) {
/* 174 */         String pid = itemMap.get("wf_proc_temp").toString();
/* 175 */         if (procTempId.equals(pid)) {
/* 176 */           wfPostTemps.add(itemMap);
/*     */         }
/*     */       } 
/* 179 */       procTemp.put("wfPostTemps", wfPostTemps);
/*     */     } 
/*     */     
/* 182 */     String procTemps_json = JsonUtils.toJson(procTemps);
/* 183 */     model.addAttribute("procTemps_json", procTemps_json);
/*     */     
/* 185 */     return "/wf/wf_temp/edit";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/update"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg update(WfTemp wfTemp, Long[] delProcTempId) {
/* 195 */     if (ConvertUtil.isEmpty(wfTemp.getWfTempName()))
/*     */     {
/* 197 */       return error("17503", new Object[0]);
/*     */     }
/* 199 */     this.wfTempBaseService.saveOrUpdate(wfTemp, delProcTempId);
/*     */     
/* 201 */     return success();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/edit_proc_user"}, method = {RequestMethod.GET})
/*     */   public String edit_proc_user(Long[] userid, ModelMap model) {
/* 210 */     List<Map<String, Object>> procuserTemps = new ArrayList<Map<String, Object>>();
/* 211 */     if (userid != null) {
/* 212 */       byte b; int i; Long[] arrayOfLong; for (i = (arrayOfLong = userid).length, b = 0; b < i; ) { Long id = arrayOfLong[b];
/* 213 */         Map<String, Object> map = new HashMap<String, Object>();
/* 214 */         StoreMember storeMember = (StoreMember)this.storeMemberBaseService.find(id);
/* 215 */         map.put("userid", storeMember.getId());
/* 216 */         map.put("username", storeMember.getUsername());
/* 217 */         map.put("name", storeMember.getName());
/* 218 */         map.put("mobile", storeMember.getMember().getMobile());
/* 219 */         procuserTemps.add(map); b++; }
/*     */     
/*     */     } 
/* 222 */     procuserTemps = new ArrayList<Map<String, Object>>(
/* 223 */         new LinkedHashSet<Map<String, Object>>(procuserTemps));
/* 224 */     model.addAttribute("procuserTemps", JsonUtils.toJson(procuserTemps));
/* 225 */     return "/wf/wf_temp/edit_proc_user";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/select_list"}, method = {RequestMethod.GET})
/*     */   public String select_list(Integer multi, Pageable pageable, ModelMap model) {
/* 234 */     model.addAttribute("multi", multi);
/* 235 */     return "/wf/wf_temp/select_list";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"select_list_data"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg select_list_data(String wfTempName, Pageable pageable, ModelMap model) {
/* 246 */     List<Filter> filters = new ArrayList<Filter>();
/* 247 */     if (!ConvertUtil.isEmpty(wfTempName)) {
/* 248 */       filters.add(Filter.like("wfTempName", "%" + wfTempName + "%"));
/*     */     }
/* 250 */     filters.add(Filter.eq("actived", Boolean.valueOf(true)));
/*     */     
/* 252 */     Page<WfTemp> page = this.wfTempBaseService.findPage(filters, null, pageable);
/* 253 */     String jsonPage = JsonUtils.toJson(page);
/*     */     
/* 255 */     return success(jsonPage, new Object[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/select_store_member"}, method = {RequestMethod.GET})
/*     */   public String selectStoreMember(Long memberRankId, Boolean isPartner, Long wfprocTempId, Integer multi, Pageable pageable, ModelMap model) {
/* 265 */     model.addAttribute("memberRankId", memberRankId);
/* 266 */     model.addAttribute("isPartner", isPartner);
/* 267 */     model.addAttribute("multi", multi);
/* 268 */     model.addAttribute("wfprocTempId", wfprocTempId);
/*     */     
/* 270 */     return "/wf/wf_temp/select_store_member";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/select_store_member_data"}, method = {RequestMethod.POST})
/*     */   @ResponseBody
/*     */   public ResultMsg selectStoreMemberData(String username, String mobile, String name, Long wfprocTempId, Pageable pageable, ModelMap model) {
/* 289 */     List<Map<String, Object>> list = this.wfProcTempBaseService.findListByWfStoreTemp(username, 
/* 290 */         mobile, 
/* 291 */         name, 
/* 292 */         wfprocTempId, 
/* 293 */         null);
/* 294 */     String jsonPage = JsonUtils.toJson(list);
/*     */     
/* 296 */     return success(jsonPage, new Object[0]);
/*     */   }
/*     */ 
/*     */   
/*     */   @RequestMapping(value = {"/edit_post"}, method = {RequestMethod.GET})
/*     */   public String edit_post(Long[] postId, ModelMap model) {
/* 302 */     List<Map<String, Object>> posts = new ArrayList<Map<String, Object>>();
/* 303 */     if (postId != null) {
/* 304 */       byte b; int i; Long[] arrayOfLong; for (i = (arrayOfLong = postId).length, b = 0; b < i; ) { Long id = arrayOfLong[b];
/* 305 */         Map<String, Object> map = new HashMap<String, Object>();
/* 306 */         Post post = (Post)this.postBaseService.find(id);
/* 307 */         map.put("id", post.getId());
/* 308 */         map.put("name", post.getName());
/* 309 */         posts.add(map); b++; }
/*     */     
/*     */     } 
/* 312 */     posts = new ArrayList<Map<String, Object>>(
/* 313 */         new LinkedHashSet<Map<String, Object>>(posts));
/* 314 */     model.addAttribute("postJson", JsonUtils.toJson(posts));
/* 315 */     return "/wf/wf_temp/edit_post";
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\controller\WfTempController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */