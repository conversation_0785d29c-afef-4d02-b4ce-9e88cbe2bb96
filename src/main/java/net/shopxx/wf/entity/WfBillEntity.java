/*    */ package net.shopxx.wf.entity;
/*    */ 
/*    */

import com.fasterxml.jackson.annotation.JsonProperty;
import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @MappedSuperclass
/*    */ public abstract class WfBillEntity
/*    */   extends BaseEntity
/*    */ {
/*    */   private static final long serialVersionUID = 3957406653503989106L;
/*    */   private Long objTypeId;
/* 23 */   private Integer wfState = Integer.valueOf(0);
/*    */ 
/*    */ 
/*    */   
/*    */   private Long wfId;
/*    */ 
/*    */   
/*    */   private Long wfTempId;
/*    */ 
/*    */ 
/*    */   
/*    */   @PrePersist
/*    */   public void prePersist() {
/* 36 */     if (this.wfState == null) this.wfState = Integer.valueOf(0);
/*    */   
/*    */   }
/*    */ 
/*    */   
/*    */   @PreUpdate
/*    */   public void preUpdate() {}
/*    */   
/*    */   @JsonProperty
/*    */   public Long getObjTypeId() {
/* 46 */     return this.objTypeId;
/*    */   }
/*    */   
/*    */   public void setObjTypeId(Long objTypeId) {
/* 50 */     this.objTypeId = objTypeId;
/*    */   }
/*    */   
/*    */   @JsonProperty
/*    */   public Integer getWfState() {
/* 55 */     return this.wfState;
/*    */   }
/*    */   
/*    */   public void setWfState(Integer wfState) {
/* 59 */     this.wfState = wfState;
/*    */   }
/*    */   
/*    */   public Long getWfId() {
/* 63 */     return this.wfId;
/*    */   }
/*    */   
/*    */   public void setWfId(Long wfId) {
/* 67 */     this.wfId = wfId;
/*    */   }
/*    */   
/*    */   public Long getWfTempId() {
/* 71 */     return this.wfTempId;
/*    */   }
/*    */   
/*    */   public void setWfTempId(Long wfTempId) {
/* 75 */     this.wfTempId = wfTempId;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setByObjConfig(WfObjConfigLine twObjConfig) {
/* 83 */     this.objTypeId = twObjConfig.getObjTypeId();
/* 84 */     setCompanyInfoId(twObjConfig.getCompanyInfoId());
/* 85 */     this.wfTempId = twObjConfig.getWfTempId();
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfBillEntity.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */