/*    */ package net.shopxx.wf.entity;
/*    */ 
/*    */

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Entity
/*    */ @Table(name = "wf_st")
/*    */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_st_sequence")
/*    */ public class WfStoreTemp
/*    */   extends BaseEntity
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private Long wfTempId;
/*    */   private WfProcTemp wfProcTemp;
/*    */   private StoreMember storeMember;
/*    */   private String note;
/*    */   
/*    */   public Long getWfTempId() {
/* 36 */     return this.wfTempId;
/*    */   }
/*    */   
/*    */   public void setWfTempId(Long wfTempId) {
/* 40 */     this.wfTempId = wfTempId;
/*    */   }
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public WfProcTemp getWfProcTemp() {
/* 45 */     return this.wfProcTemp;
/*    */   }
/*    */   
/*    */   public void setWfProcTemp(WfProcTemp wfProcTemp) {
/* 49 */     this.wfProcTemp = wfProcTemp;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public StoreMember getStoreMember() {
/* 58 */     return this.storeMember;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setStoreMember(StoreMember storeMember) {
/* 66 */     this.storeMember = storeMember;
/*    */   }
/*    */   
/*    */   public String getNote() {
/* 70 */     return this.note;
/*    */   }
/*    */   
/*    */   public void setNote(String note) {
/* 74 */     this.note = note;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfStoreTemp.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */