/*     */ package net.shopxx.wf.entity;
/*     */ 
/*     */

import com.fasterxml.jackson.annotation.JsonIgnore;
import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Entity
/*     */ @Table(name = "wf_temp")
/*     */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_temp_sequence")
/*     */ public class WfTemp
/*     */   extends BaseEntity
/*     */ {
/*     */   private static final long serialVersionUID = 851692940859169812L;
/*     */   private String wfTempName;
/*     */   private String code;
/*     */   private Boolean actived;
/*     */   private Long breakProcId;
/*     */   private Boolean keepHistory;
/*     */   private String note;
/*  46 */   List<WfProcTemp> wfProcTemps = new ArrayList<WfProcTemp>();
/*     */   
/*     */   public String getWfTempName() {
/*  49 */     return this.wfTempName;
/*     */   }
/*     */   
/*     */   public void setWfTempName(String wfTempName) {
/*  53 */     this.wfTempName = wfTempName;
/*     */   }
/*     */   
/*     */   public String getCode() {
/*  57 */     return this.code;
/*     */   }
/*     */   
/*     */   public void setCode(String code) {
/*  61 */     this.code = code;
/*     */   }
/*     */   
/*     */   public Long getBreakProcId() {
/*  65 */     return this.breakProcId;
/*     */   }
/*     */   
/*     */   public void setBreakProcId(Long breakProcId) {
/*  69 */     this.breakProcId = breakProcId;
/*     */   }
/*     */   
/*     */   public String getNote() {
/*  73 */     return this.note;
/*     */   }
/*     */   
/*     */   public void setNote(String note) {
/*  77 */     this.note = note;
/*     */   }
/*     */   
/*     */   @JsonIgnore
/*     */   @OneToMany(mappedBy = "wfTemp", fetch = FetchType.LAZY)
/*     */   @OrderBy("procTempSeq asc")
/*     */   public List<WfProcTemp> getWfProcTemps() {
/*  84 */     return this.wfProcTemps;
/*     */   }
/*     */   
/*     */   public void setWfProcTemps(List<WfProcTemp> wfProcTemps) {
/*  88 */     this.wfProcTemps = wfProcTemps;
/*     */   }
/*     */   
/*     */   public Boolean getKeepHistory() {
/*  92 */     return this.keepHistory;
/*     */   }
/*     */   
/*     */   public void setKeepHistory(Boolean keepHistory) {
/*  96 */     this.keepHistory = keepHistory;
/*     */   }
/*     */   
/*     */   public Boolean getActived() {
/* 100 */     return this.actived;
/*     */   }
/*     */   
/*     */   public void setActived(Boolean actived) {
/* 104 */     this.actived = actived;
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfTemp.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */