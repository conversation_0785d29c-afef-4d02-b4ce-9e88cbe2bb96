/*    */ package net.shopxx.wf.entity;
/*    */ 
/*    */

import net.shopxx.base.core.entity.OrderEntity;

import javax.persistence.Entity;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Entity
/*    */ @Table(name = "wf_obj_config_line")
/*    */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_obj_config_line_sequence")
/*    */ public class WfObjConfigLine
/*    */   extends OrderEntity
/*    */ {
/*    */   private static final long serialVersionUID = -5244245428418193476L;
/*    */   private Long objConfigId;
/*    */   private Long objTypeId;
/*    */   private Long wfTempId;
/*    */   private String conditions;
/*    */   
/*    */   public Long getObjConfigId() {
/* 36 */     return this.objConfigId;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setObjConfigId(Long objConfigId) {
/* 44 */     this.objConfigId = objConfigId;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Long getObjTypeId() {
/* 52 */     return this.objTypeId;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setObjTypeId(Long objTypeId) {
/* 60 */     this.objTypeId = objTypeId;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Long getWfTempId() {
/* 68 */     return this.wfTempId;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setWfTempId(Long wfTempId) {
/* 76 */     this.wfTempId = wfTempId;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getConditions() {
/* 84 */     return this.conditions;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setConditions(String conditions) {
/* 92 */     this.conditions = conditions;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfObjConfigLine.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */