/*    */ package net.shopxx.wf.entity;
/*    */ 
/*    */

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.Post;

import javax.persistence.*;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Entity
/*    */ @Table(name = "wf_post_temp")
/*    */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_post_temp_sequence")
/*    */ public class WfPostTemp
/*    */   extends BaseEntity
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private Long wfTempId;
/*    */   private WfProcTemp wfProcTemp;
/*    */   private Post post;
/*    */   
/*    */   public Long getWfTempId() {
/* 33 */     return this.wfTempId;
/*    */   }
/*    */   
/*    */   public void setWfTempId(Long wfTempId) {
/* 37 */     this.wfTempId = wfTempId;
/*    */   }
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public WfProcTemp getWfProcTemp() {
/* 42 */     return this.wfProcTemp;
/*    */   }
/*    */   
/*    */   public void setWfProcTemp(WfProcTemp wfProcTemp) {
/* 46 */     this.wfProcTemp = wfProcTemp;
/*    */   }
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public Post getPost() {
/* 51 */     return this.post;
/*    */   }
/*    */   
/*    */   public void setPost(Post post) {
/* 55 */     this.post = post;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfPostTemp.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */