/*     */ package net.shopxx.wf.entity;
/*     */ 
/*     */

import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Entity
/*     */ @Table(name = "wf_proc")
/*     */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_proc_sequence")
/*     */ public class WfProc
/*     */   extends BaseEntity
/*     */ {
/*     */   private static final long serialVersionUID = -319638138092189588L;
/*     */   private Wf wf;
/*     */   private Integer procSeq;
/*     */   private String procName;
/*     */   private Integer procType;
/*     */   private Boolean isSkip;
/*     */   private Integer minPerson;
/*     */   private Boolean allPerson;
/*     */   private WfProc preproc;
/*     */   private WfProc afterproc;
/*     */   private Integer stat;
/*     */   private String defOpinion;
/*     */   private String note;
/*     */   private Boolean isSendSms;
/*     */   private String specialHandleCode;
/*     */   private Long procTempId;
/*  72 */   List<WfProcuser> wfProcusers = new ArrayList<WfProcuser>();
/*     */ 
/*     */   
/*  75 */   List<WfUserOpinion> wfUserOpinions = new ArrayList<WfUserOpinion>();
/*     */   
/*     */   @ManyToOne(fetch = FetchType.LAZY)
/*     */   public Wf getWf() {
/*  79 */     return this.wf;
/*     */   }
/*     */   
/*     */   public void setWf(Wf wf) {
/*  83 */     this.wf = wf;
/*     */   }
/*     */   
/*     */   public String getProcName() {
/*  87 */     return this.procName;
/*     */   }
/*     */   
/*     */   public void setProcName(String procName) {
/*  91 */     this.procName = procName;
/*     */   }
/*     */   
/*     */   public Integer getProcType() {
/*  95 */     return this.procType;
/*     */   }
/*     */   
/*     */   public void setProcType(Integer procType) {
/*  99 */     this.procType = procType;
/*     */   }
/*     */   
/*     */   public Boolean getIsSkip() {
/* 103 */     return this.isSkip;
/*     */   }
/*     */   
/*     */   public void setIsSkip(Boolean isSkip) {
/* 107 */     this.isSkip = isSkip;
/*     */   }
/*     */   
/*     */   public Integer getMinPerson() {
/* 111 */     return this.minPerson;
/*     */   }
/*     */   
/*     */   public void setMinPerson(Integer minPerson) {
/* 115 */     this.minPerson = minPerson;
/*     */   }
/*     */   
/*     */   public Boolean getAllPerson() {
/* 119 */     return this.allPerson;
/*     */   }
/*     */   
/*     */   public void setAllPerson(Boolean allPerson) {
/* 123 */     this.allPerson = allPerson;
/*     */   }
/*     */   
/*     */   @OneToOne(fetch = FetchType.LAZY)
/*     */   public WfProc getPreproc() {
/* 128 */     return this.preproc;
/*     */   }
/*     */   
/*     */   public void setPreproc(WfProc preproc) {
/* 132 */     this.preproc = preproc;
/*     */   }
/*     */   
/*     */   public Integer getStat() {
/* 136 */     return this.stat;
/*     */   }
/*     */   
/*     */   public void setStat(Integer stat) {
/* 140 */     this.stat = stat;
/*     */   }
/*     */   
/*     */   public String getDefOpinion() {
/* 144 */     return this.defOpinion;
/*     */   }
/*     */   
/*     */   public void setDefOpinion(String defOpinion) {
/* 148 */     this.defOpinion = defOpinion;
/*     */   }
/*     */   
/*     */   public String getNote() {
/* 152 */     return this.note;
/*     */   }
/*     */   
/*     */   public void setNote(String note) {
/* 156 */     this.note = note;
/*     */   }
/*     */   
/*     */   @OneToMany(mappedBy = "wfProc", fetch = FetchType.LAZY)
/*     */   public List<WfProcuser> getWfProcusers() {
/* 161 */     return this.wfProcusers;
/*     */   }
/*     */   
/*     */   public void setWfProcusers(List<WfProcuser> wfProcusers) {
/* 165 */     this.wfProcusers = wfProcusers;
/*     */   }
/*     */   
/*     */   @OneToMany(mappedBy = "wfProc", fetch = FetchType.LAZY)
/*     */   public List<WfUserOpinion> getWfUserOpinions() {
/* 170 */     return this.wfUserOpinions;
/*     */   }
/*     */   
/*     */   public void setWfUserOpinions(List<WfUserOpinion> wfUserOpinions) {
/* 174 */     this.wfUserOpinions = wfUserOpinions;
/*     */   }
/*     */   
/*     */   @OneToOne(fetch = FetchType.LAZY)
/*     */   public WfProc getAfterproc() {
/* 179 */     return this.afterproc;
/*     */   }
/*     */   
/*     */   public void setAfterproc(WfProc afterproc) {
/* 183 */     this.afterproc = afterproc;
/*     */   }
/*     */   
/*     */   public Integer getProcSeq() {
/* 187 */     return this.procSeq;
/*     */   }
/*     */   
/*     */   public void setProcSeq(Integer procSeq) {
/* 191 */     this.procSeq = procSeq;
/*     */   }
/*     */   
/*     */   public Boolean getIsSendSms() {
/* 195 */     return this.isSendSms;
/*     */   }
/*     */   
/*     */   public void setIsSendSms(Boolean isSendSms) {
/* 199 */     this.isSendSms = isSendSms;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSpecialHandleCode() {
/* 207 */     return this.specialHandleCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSpecialHandleCode(String specialHandleCode) {
/* 215 */     this.specialHandleCode = specialHandleCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Long getProcTempId() {
/* 223 */     return this.procTempId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setProcTempId(Long procTempId) {
/* 231 */     this.procTempId = procTempId;
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfProc.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */