/*    */ package net.shopxx.wf.entity;
/*    */ 
/*    */

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;
import java.util.Date;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Entity
/*    */ @Table(name = "wf_user_opinion")
/*    */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_user_o_sequence")
/*    */ public class WfUserOpinion
/*    */   extends BaseEntity
/*    */ {
/*    */   private static final long serialVersionUID = 2995339354715281576L;
/*    */   private Long wfId;
/*    */   private WfProc wfProc;
/*    */   private StoreMember storeMember;
/*    */   private Integer stat;
/*    */   private Date signTime;
/*    */   private String opinion;
/*    */   
/*    */   public Long getWfId() {
/* 43 */     return this.wfId;
/*    */   }
/*    */   
/*    */   public void setWfId(Long wfId) {
/* 47 */     this.wfId = wfId;
/*    */   }
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public WfProc getWfProc() {
/* 52 */     return this.wfProc;
/*    */   }
/*    */   
/*    */   public void setWfProc(WfProc wfProc) {
/* 56 */     this.wfProc = wfProc;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public StoreMember getStoreMember() {
/* 65 */     return this.storeMember;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setStoreMember(StoreMember storeMember) {
/* 73 */     this.storeMember = storeMember;
/*    */   }
/*    */   
/*    */   public Integer getStat() {
/* 77 */     return this.stat;
/*    */   }
/*    */   
/*    */   public void setStat(Integer stat) {
/* 81 */     this.stat = stat;
/*    */   }
/*    */   
/*    */   public Date getSignTime() {
/* 85 */     return this.signTime;
/*    */   }
/*    */   
/*    */   public void setSignTime(Date signTime) {
/* 89 */     this.signTime = signTime;
/*    */   }
/*    */   
/*    */   public String getOpinion() {
/* 93 */     return this.opinion;
/*    */   }
/*    */   
/*    */   public void setOpinion(String opinion) {
/* 97 */     this.opinion = opinion;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfUserOpinion.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */