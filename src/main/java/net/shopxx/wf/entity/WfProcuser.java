/*     */ package net.shopxx.wf.entity;
/*     */ 
/*     */

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Entity
/*     */ @Table(name = "wf_procuser")
/*     */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_procuser_sequence")
/*     */ public class WfProcuser
/*     */   extends BaseEntity
/*     */ {
/*     */   private static final long serialVersionUID = 1192443406840900885L;
/*     */   private Long wfId;
/*     */   private WfProc wfProc;
/*     */   private StoreMember storeMember;
/*     */   private Integer isSigned;
/*     */   private Boolean tempUser;
/*     */   private Integer freq;
/*     */   private Boolean isRead;
/*     */   private String note;
/*     */   
/*     */   public Long getWfId() {
/*  48 */     return this.wfId;
/*     */   }
/*     */   
/*     */   public void setWfId(Long wfId) {
/*  52 */     this.wfId = wfId;
/*     */   }
/*     */   
/*     */   @ManyToOne(fetch = FetchType.LAZY)
/*     */   public WfProc getWfProc() {
/*  57 */     return this.wfProc;
/*     */   }
/*     */   
/*     */   public void setWfProc(WfProc wfProc) {
/*  61 */     this.wfProc = wfProc;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ManyToOne(fetch = FetchType.LAZY)
/*     */   public StoreMember getStoreMember() {
/*  70 */     return this.storeMember;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setStoreMember(StoreMember storeMember) {
/*  78 */     this.storeMember = storeMember;
/*     */   }
/*     */   
/*     */   public Boolean getTempUser() {
/*  82 */     return this.tempUser;
/*     */   }
/*     */   
/*     */   public void setTempUser(Boolean tempUser) {
/*  86 */     this.tempUser = tempUser;
/*     */   }
/*     */   
/*     */   public Integer getFreq() {
/*  90 */     return this.freq;
/*     */   }
/*     */   
/*     */   public void setFreq(Integer freq) {
/*  94 */     this.freq = freq;
/*     */   }
/*     */   
/*     */   public Boolean getIsRead() {
/*  98 */     return this.isRead;
/*     */   }
/*     */   
/*     */   public void setIsRead(Boolean isRead) {
/* 102 */     this.isRead = isRead;
/*     */   }
/*     */   
/*     */   public String getNote() {
/* 106 */     return this.note;
/*     */   }
/*     */   
/*     */   public void setNote(String note) {
/* 110 */     this.note = note;
/*     */   }
/*     */   
/*     */   public Integer getIsSigned() {
/* 114 */     return this.isSigned;
/*     */   }
/*     */   
/*     */   public void setIsSigned(Integer isSigned) {
/* 118 */     this.isSigned = isSigned;
/*     */   }
/*     */   
/*     */   @PrePersist
/*     */   public void prePersist() {
/* 123 */     this.isSigned = Integer.valueOf(1);
/* 124 */     this.freq = Integer.valueOf(0);
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfProcuser.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */