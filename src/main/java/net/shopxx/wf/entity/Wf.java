/*     */ package net.shopxx.wf.entity;
/*     */ 
/*     */

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.Store;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Entity
/*     */ @Table(name = "wf")
/*     */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_sequence")
/*     */ public class Wf
/*     */   extends BaseEntity
/*     */ {
/*     */   private static final long serialVersionUID = 2637403899300869308L;
/*     */   private String wfName;
/*     */   private Long wfTempId;
/*     */   private Integer stat;
/*     */   private Date statTime;
/*     */   private Date endTime;
/*     */   private Long currProcId;
/*     */   private Boolean keepHistory;
/*     */   private Long breakProcId;
/*     */   private String wfOpinion;
/*     */   private String subject;
/*     */   private String subjectType;
/*     */   private StoreMember lastUser;
/*     */   private Date lastTime;
/*     */   private Long objType;
/*     */   private Long objId;
/*     */   private Integer priority;
/*     */   private String breakReason;
/*     */   private String managers;
/*     */   private String note;
/*     */   private StoreMember starter;
/*     */   private Store store;
/*  93 */   private List<WfProc> wfProcs = new ArrayList<WfProc>();
/*     */ 
/*     */   
/*  96 */   private List<WfAttach> wfAttachs = new ArrayList<WfAttach>();
/*     */   
/*     */   public String getWfName() {
/*  99 */     return this.wfName;
/*     */   }
/*     */   
/*     */   public void setWfName(String wfName) {
/* 103 */     this.wfName = wfName;
/*     */   }
/*     */   
/*     */   public Long getWfTempId() {
/* 107 */     return this.wfTempId;
/*     */   }
/*     */   
/*     */   public void setWfTempId(Long wfTempId) {
/* 111 */     this.wfTempId = wfTempId;
/*     */   }
/*     */   
/*     */   public Integer getStat() {
/* 115 */     return this.stat;
/*     */   }
/*     */   
/*     */   public void setStat(Integer stat) {
/* 119 */     this.stat = stat;
/*     */   }
/*     */   
/*     */   public Date getStatTime() {
/* 123 */     return this.statTime;
/*     */   }
/*     */   
/*     */   public void setStatTime(Date statTime) {
/* 127 */     this.statTime = statTime;
/*     */   }
/*     */   
/*     */   public Date getEndTime() {
/* 131 */     return this.endTime;
/*     */   }
/*     */   
/*     */   public void setEndTime(Date endTime) {
/* 135 */     this.endTime = endTime;
/*     */   }
/*     */   
/*     */   public Long getCurrProcId() {
/* 139 */     return this.currProcId;
/*     */   }
/*     */   
/*     */   public void setCurrProcId(Long currProcId) {
/* 143 */     this.currProcId = currProcId;
/*     */   }
/*     */   
/*     */   public Boolean getKeepHistory() {
/* 147 */     return this.keepHistory;
/*     */   }
/*     */   
/*     */   public void setKeepHistory(Boolean keepHistory) {
/* 151 */     this.keepHistory = keepHistory;
/*     */   }
/*     */   
/*     */   public Long getBreakProcId() {
/* 155 */     return this.breakProcId;
/*     */   }
/*     */   
/*     */   public void setBreakProcId(Long breakProcId) {
/* 159 */     this.breakProcId = breakProcId;
/*     */   }
/*     */   
/*     */   public String getWfOpinion() {
/* 163 */     return this.wfOpinion;
/*     */   }
/*     */   
/*     */   public void setWfOpinion(String wfOpinion) {
/* 167 */     this.wfOpinion = wfOpinion;
/*     */   }
/*     */   
/*     */   public String getSubject() {
/* 171 */     return this.subject;
/*     */   }
/*     */   
/*     */   public void setSubject(String subject) {
/* 175 */     this.subject = subject;
/*     */   }
/*     */   
/*     */   public String getSubjectType() {
/* 179 */     return this.subjectType;
/*     */   }
/*     */   
/*     */   public void setSubjectType(String subjectType) {
/* 183 */     this.subjectType = subjectType;
/*     */   }
/*     */   
/*     */   @ManyToOne(fetch = FetchType.LAZY)
/*     */   public StoreMember getLastUser() {
/* 188 */     return this.lastUser;
/*     */   }
/*     */   
/*     */   public void setLastUser(StoreMember lastUser) {
/* 192 */     this.lastUser = lastUser;
/*     */   }
/*     */   
/*     */   public Date getLastTime() {
/* 196 */     return this.lastTime;
/*     */   }
/*     */   
/*     */   public void setLastTime(Date lastTime) {
/* 200 */     this.lastTime = lastTime;
/*     */   }
/*     */   
/*     */   public Long getObjType() {
/* 204 */     return this.objType;
/*     */   }
/*     */   
/*     */   public void setObjType(Long objType) {
/* 208 */     this.objType = objType;
/*     */   }
/*     */   
/*     */   public Long getObjId() {
/* 212 */     return this.objId;
/*     */   }
/*     */   
/*     */   public void setObjId(Long objId) {
/* 216 */     this.objId = objId;
/*     */   }
/*     */   
/*     */   public Integer getPriority() {
/* 220 */     return this.priority;
/*     */   }
/*     */   
/*     */   public void setPriority(Integer priority) {
/* 224 */     this.priority = priority;
/*     */   }
/*     */   
/*     */   public String getBreakReason() {
/* 228 */     return this.breakReason;
/*     */   }
/*     */   
/*     */   public void setBreakReason(String breakReason) {
/* 232 */     this.breakReason = breakReason;
/*     */   }
/*     */   
/*     */   public String getManagers() {
/* 236 */     return this.managers;
/*     */   }
/*     */   
/*     */   public void setManagers(String managers) {
/* 240 */     this.managers = managers;
/*     */   }
/*     */   
/*     */   public String getNote() {
/* 244 */     return this.note;
/*     */   }
/*     */   
/*     */   public void setNote(String note) {
/* 248 */     this.note = note;
/*     */   }
/*     */   
/*     */   @ManyToOne(fetch = FetchType.LAZY)
/*     */   public StoreMember getStarter() {
/* 253 */     return this.starter;
/*     */   }
/*     */   
/*     */   public void setStarter(StoreMember starter) {
/* 257 */     this.starter = starter;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ManyToOne(fetch = FetchType.LAZY)
/*     */   public Store getStore() {
/* 266 */     return this.store;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setStore(Store store) {
/* 274 */     this.store = store;
/*     */   }
/*     */   
/*     */   @OneToMany(mappedBy = "wf", fetch = FetchType.LAZY)
/*     */   public List<WfProc> getWfProcs() {
/* 279 */     return this.wfProcs;
/*     */   }
/*     */   
/*     */   public void setWfProcs(List<WfProc> wfProcs) {
/* 283 */     this.wfProcs = wfProcs;
/*     */   }
/*     */   
/*     */   @OneToMany(mappedBy = "wf", fetch = FetchType.LAZY)
/*     */   public List<WfAttach> getWfAttachs() {
/* 288 */     return this.wfAttachs;
/*     */   }
/*     */   
/*     */   public void setWfAttachs(List<WfAttach> wfAttachs) {
/* 292 */     this.wfAttachs = wfAttachs;
/*     */   }
/*     */   
/*     */   @Transient
/*     */   public WfProc getStartProc() {
/* 297 */     for (WfProc twWfProc : getWfProcs()) {
/* 298 */       if (twWfProc.getProcType().intValue() == 1) {
/* 299 */         return twWfProc;
/*     */       }
/*     */     } 
/* 302 */     return null;
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\Wf.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */