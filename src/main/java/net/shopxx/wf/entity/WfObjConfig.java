/*     */ package net.shopxx.wf.entity;
/*     */ 
/*     */

import com.fasterxml.jackson.annotation.JsonProperty;
import net.shopxx.base.core.entity.OrderEntity;

import javax.persistence.Entity;
import javax.persistence.PrePersist;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Entity
/*     */ @Table(name = "wf_obj_config")
/*     */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_obj_config_sequence")
/*     */ public class WfObjConfig
/*     */   extends OrderEntity
/*     */ {
/*     */   private static final long serialVersionUID = -5244245428418193476L;
/*     */   private Long objTypeId;
/*     */   private String objTypeName;
/*     */   private String objClass;
/*     */   private Boolean isFlow;
/*     */   private String tableName;
/*     */   private Boolean isBill;
/*     */   private String note;
/*     */   private String image;
/*     */   private String url;
/*     */   
/*     */   @JsonProperty
/*     */   public String getObjTypeName() {
/*  51 */     return this.objTypeName;
/*     */   }
/*     */   
/*     */   public void setObjTypeName(String objTypeName) {
/*  55 */     this.objTypeName = objTypeName;
/*     */   }
/*     */   
/*     */   public String getObjClass() {
/*  59 */     return this.objClass;
/*     */   }
/*     */   
/*     */   public void setObjClass(String objClass) {
/*  63 */     this.objClass = objClass;
/*     */   }
/*     */   
/*     */   public Boolean getIsFlow() {
/*  67 */     return this.isFlow;
/*     */   }
/*     */   
/*     */   public void setIsFlow(Boolean isFlow) {
/*  71 */     this.isFlow = isFlow;
/*     */   }
/*     */   
/*     */   public String getTableName() {
/*  75 */     return this.tableName;
/*     */   }
/*     */   
/*     */   public void setTableName(String tableName) {
/*  79 */     this.tableName = tableName;
/*     */   }
/*     */   
/*     */   public Boolean getIsBill() {
/*  83 */     return this.isBill;
/*     */   }
/*     */   
/*     */   public void setIsBill(Boolean isBill) {
/*  87 */     this.isBill = isBill;
/*     */   }
/*     */   
/*     */   public String getNote() {
/*  91 */     return this.note;
/*     */   }
/*     */   
/*     */   public void setNote(String note) {
/*  95 */     this.note = note;
/*     */   }
/*     */   
/*     */   public String getImage() {
/*  99 */     return this.image;
/*     */   }
/*     */   
/*     */   public void setImage(String image) {
/* 103 */     this.image = image;
/*     */   }
/*     */   
/*     */   public Long getObjTypeId() {
/* 107 */     return this.objTypeId;
/*     */   }
/*     */   
/*     */   public void setObjTypeId(Long objTypeId) {
/* 111 */     this.objTypeId = objTypeId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUrl() {
/* 119 */     return this.url;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUrl(String url) {
/* 127 */     this.url = url;
/*     */   }
/*     */   
/*     */   @PrePersist
/*     */   public void prePersist() {
/* 132 */     if (this.objClass != null)
/* 133 */       if (this.objClass.equals("itemPromotionServiceImpl")) {
/*     */         
/* 135 */         this.objTypeId = Long.valueOf(41L);
/*     */       }
/* 137 */       else if (this.objClass.equals("priceApplyServiceImpl")) {
/*     */         
/* 139 */         this.objTypeId = Long.valueOf(42L);
/*     */       }
/* 141 */       else if (this.objClass.equals("depositRechargeServiceImpl")) {
/*     */         
/* 143 */         this.objTypeId = Long.valueOf(43L);
/*     */       }
/* 145 */       else if (this.objClass.equals("expenseBudgetServiceImpl")) {
/*     */         
/* 147 */         this.objTypeId = Long.valueOf(44L);
/*     */       }
/* 149 */       else if (this.objClass.equals("reimbursementServiceImpl")) {
/*     */         
/* 151 */         this.objTypeId = Long.valueOf(45L);
/*     */       }
/* 153 */       else if (this.objClass.equals("creditRechargeServiceImpl")) {
/*     */         
/* 155 */         this.objTypeId = Long.valueOf(46L);
/*     */       }
/* 157 */       else if (this.objClass.equals("orderServiceImpl")) {
/*     */         
/* 159 */         this.objTypeId = Long.valueOf(47L);
/*     */       }
/* 161 */       else if (this.objClass.equals("stockInServiceImpl")) {
/*     */         
/* 163 */         this.objTypeId = Long.valueOf(48L);
/*     */       }
/* 165 */       else if (this.objClass.equals("productPriceHeadServiceImpl")) {
/*     */         
/* 167 */         this.objTypeId = Long.valueOf(49L);
/*     */       }
/* 169 */       else if (this.objClass.equals("activityPriceHeadServiceImpl")) {
/*     */         
/* 171 */         this.objTypeId = Long.valueOf(50L);
/*     */       }
/* 173 */       else if (this.objClass.equals("contractPriceServiceImpl")) {
/*     */         
/* 175 */         this.objTypeId = Long.valueOf(51L);
/*     */       }
/* 177 */       else if (this.objClass.equals("stockOutServiceImpl")) {
/*     */         
/* 179 */         this.objTypeId = Long.valueOf(52L);
/*     */       }
/* 181 */       else if (this.objClass.equals("reconciliationServiceImpl")) {
/*     */         
/* 183 */         this.objTypeId = Long.valueOf(53L);
/*     */       }
/* 185 */       else if (this.objClass.equals("backlogServiceImpl")) {
/*     */         
/* 187 */         this.objTypeId = Long.valueOf(54L);
/*     */       }
/* 189 */       else if (this.objClass.equals("storeApplyServiceImpl")) {
/*     */         
/* 191 */         this.objTypeId = Long.valueOf(55L);
/*     */       }
/* 193 */       else if (this.objClass.equals("policyServiceImpl")) {
/*     */         
/* 195 */         this.objTypeId = Long.valueOf(56L);
/*     */       }
/* 197 */       else if (this.objClass.equals("policyEntryServiceImpl")) {
/*     */         
/* 199 */         this.objTypeId = Long.valueOf(60L);
/*     */       }  
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfObjConfig.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */