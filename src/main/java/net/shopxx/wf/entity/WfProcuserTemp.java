/*    */ package net.shopxx.wf.entity;
/*    */ 
/*    */

import net.shopxx.base.core.entity.BaseEntity;
import net.shopxx.member.entity.StoreMember;

import javax.persistence.*;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Entity
/*    */ @Table(name = "wf_procuser_temp")
/*    */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_procuser_t_sequence")
/*    */ public class WfProcuserTemp
/*    */   extends BaseEntity
/*    */ {
/*    */   private static final long serialVersionUID = -3825058522641159826L;
/*    */   private Long wfTempId;
/*    */   private WfProcTemp wfProcTemp;
/*    */   private StoreMember storeMember;
/*    */   private String note;
/*    */   
/*    */   public Long getWfTempId() {
/* 35 */     return this.wfTempId;
/*    */   }
/*    */   
/*    */   public void setWfTempId(Long wfTempId) {
/* 39 */     this.wfTempId = wfTempId;
/*    */   }
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public WfProcTemp getWfProcTemp() {
/* 44 */     return this.wfProcTemp;
/*    */   }
/*    */   
/*    */   public void setWfProcTemp(WfProcTemp wfProcTemp) {
/* 48 */     this.wfProcTemp = wfProcTemp;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public StoreMember getStoreMember() {
/* 57 */     return this.storeMember;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setStoreMember(StoreMember storeMember) {
/* 65 */     this.storeMember = storeMember;
/*    */   }
/*    */   
/*    */   public String getNote() {
/* 69 */     return this.note;
/*    */   }
/*    */   
/*    */   public void setNote(String note) {
/* 73 */     this.note = note;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfProcuserTemp.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */