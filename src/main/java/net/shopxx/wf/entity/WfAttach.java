/*    */ package net.shopxx.wf.entity;
/*    */ 
/*    */

import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Entity
/*    */ @Table(name = "wf_attach")
/*    */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_attach_sequence")
/*    */ public class WfAttach
/*    */   extends BaseEntity
/*    */ {
/*    */   private static final long serialVersionUID = 5166497377180708787L;
/*    */   private Wf wf;
/*    */   private String fileUrl;
/*    */   private String fileName;
/*    */   private String note;
/*    */   
/*    */   @ManyToOne(fetch = FetchType.LAZY)
/*    */   public Wf getWf() {
/* 35 */     return this.wf;
/*    */   }
/*    */   
/*    */   public void setWf(Wf wf) {
/* 39 */     this.wf = wf;
/*    */   }
/*    */   
/*    */   public String getFileUrl() {
/* 43 */     return this.fileUrl;
/*    */   }
/*    */   
/*    */   public void setFileUrl(String fileUrl) {
/* 47 */     this.fileUrl = fileUrl;
/*    */   }
/*    */   
/*    */   public String getFileName() {
/* 51 */     return this.fileName;
/*    */   }
/*    */   
/*    */   public void setFileName(String fileName) {
/* 55 */     this.fileName = fileName;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 63 */     return this.note;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setNote(String note) {
/* 71 */     this.note = note;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfAttach.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */