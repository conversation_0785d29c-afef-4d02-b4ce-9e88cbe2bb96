/*     */ package net.shopxx.wf.entity;
/*     */ 
/*     */

import net.shopxx.base.core.entity.BaseEntity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Entity
/*     */ @Table(name = "wf_proc_temp")
/*     */ @SequenceGenerator(name = "sequenceGenerator", sequenceName = "wf_proc_temp_sequence")
/*     */ public class WfProcTemp
/*     */   extends BaseEntity
/*     */ {
/*     */   private static final long serialVersionUID = -1475055989662995287L;
/*     */   private WfTemp wfTemp;
/*     */   private Integer procTempSeq;
/*     */   private String procTempName;
/*     */   private Integer procType;
/*     */   private Boolean isSkip;
/*     */   private Integer stat;
/*     */   private Integer minPerson;
/*     */   private Boolean allPerson;
/*     */   private WfProcTemp preProc;
/*     */   private WfProcTemp afterProc;
/*     */   private String defOpinion;
/*     */   private Boolean isSendSms;
/*     */   private String specialHandleCode;
/*  69 */   List<WfProcuserTemp> wfProcuserTemps = new ArrayList<WfProcuserTemp>();
/*     */ 
/*     */   
/*  72 */   List<WfStoreTemp> wfStoreTemps = new ArrayList<WfStoreTemp>();
/*     */ 
/*     */   
/*  75 */   List<WfPostTemp> wfPostTemps = new ArrayList<WfPostTemp>();
/*     */   
/*     */   @OneToMany(mappedBy = "wfProcTemp", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
/*     */   public List<WfStoreTemp> getWfStoreTemps() {
/*  79 */     return this.wfStoreTemps;
/*     */   }
/*     */   
/*     */   public void setWfStoreTemps(List<WfStoreTemp> wfStoreTemps) {
/*  83 */     this.wfStoreTemps = wfStoreTemps;
/*     */   }
/*     */   
/*     */   @ManyToOne(fetch = FetchType.LAZY)
/*     */   public WfTemp getWfTemp() {
/*  88 */     return this.wfTemp;
/*     */   }
/*     */   
/*     */   public void setWfTemp(WfTemp wfTemp) {
/*  92 */     this.wfTemp = wfTemp;
/*     */   }
/*     */   
/*     */   public String getProcTempName() {
/*  96 */     return this.procTempName;
/*     */   }
/*     */   
/*     */   public void setProcTempName(String procTempName) {
/* 100 */     this.procTempName = procTempName;
/*     */   }
/*     */   
/*     */   public Integer getProcType() {
/* 104 */     return this.procType;
/*     */   }
/*     */   
/*     */   public void setProcType(Integer procType) {
/* 108 */     this.procType = procType;
/*     */   }
/*     */   
/*     */   public Boolean getIsSkip() {
/* 112 */     return this.isSkip;
/*     */   }
/*     */   
/*     */   public void setIsSkip(Boolean isSkip) {
/* 116 */     this.isSkip = isSkip;
/*     */   }
/*     */   
/*     */   public Integer getStat() {
/* 120 */     return this.stat;
/*     */   }
/*     */   
/*     */   public void setStat(Integer stat) {
/* 124 */     this.stat = stat;
/*     */   }
/*     */   
/*     */   public Integer getMinPerson() {
/* 128 */     return this.minPerson;
/*     */   }
/*     */   
/*     */   public void setMinPerson(Integer minPerson) {
/* 132 */     this.minPerson = minPerson;
/*     */   }
/*     */   
/*     */   public Boolean getAllPerson() {
/* 136 */     return this.allPerson;
/*     */   }
/*     */   
/*     */   public void setAllPerson(Boolean allPerson) {
/* 140 */     this.allPerson = allPerson;
/*     */   }
/*     */   
/*     */   @OneToOne(fetch = FetchType.LAZY)
/*     */   public WfProcTemp getPreProc() {
/* 145 */     return this.preProc;
/*     */   }
/*     */   
/*     */   public void setPreProc(WfProcTemp preProc) {
/* 149 */     this.preProc = preProc;
/*     */   }
/*     */   
/*     */   public String getDefOpinion() {
/* 153 */     return this.defOpinion;
/*     */   }
/*     */   
/*     */   public void setDefOpinion(String defOpinion) {
/* 157 */     this.defOpinion = defOpinion;
/*     */   }
/*     */   
/*     */   @OneToMany(mappedBy = "wfProcTemp", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
/*     */   public List<WfProcuserTemp> getWfProcuserTemps() {
/* 162 */     return this.wfProcuserTemps;
/*     */   }
/*     */   
/*     */   public void setWfProcuserTemps(List<WfProcuserTemp> wfProcuserTemps) {
/* 166 */     this.wfProcuserTemps = wfProcuserTemps;
/*     */   }
/*     */   
/*     */   @OneToOne(fetch = FetchType.LAZY)
/*     */   public WfProcTemp getAfterProc() {
/* 171 */     return this.afterProc;
/*     */   }
/*     */   
/*     */   public void setAfterProc(WfProcTemp afterProc) {
/* 175 */     this.afterProc = afterProc;
/*     */   }
/*     */   
/*     */   public Integer getProcTempSeq() {
/* 179 */     return this.procTempSeq;
/*     */   }
/*     */   
/*     */   public void setProcTempSeq(Integer procTempSeq) {
/* 183 */     this.procTempSeq = procTempSeq;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSpecialHandleCode() {
/* 191 */     return this.specialHandleCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSpecialHandleCode(String specialHandleCode) {
/* 199 */     this.specialHandleCode = specialHandleCode;
/*     */   }
/*     */   
/*     */   @PrePersist
/*     */   public void prePersist() {
/* 204 */     if (this.isSkip == null) this.isSkip = Boolean.valueOf(false); 
/* 205 */     if (this.allPerson == null) this.allPerson = Boolean.valueOf(false); 
/*     */   }
/*     */   
/*     */   public Boolean getIsSendSms() {
/* 209 */     return this.isSendSms;
/*     */   }
/*     */   
/*     */   public void setIsSendSms(Boolean isSendSms) {
/* 213 */     this.isSendSms = isSendSms;
/*     */   }
/*     */   
/*     */   @OneToMany(mappedBy = "wfProcTemp", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
/*     */   public List<WfPostTemp> getWfPostTemps() {
/* 218 */     return this.wfPostTemps;
/*     */   }
/*     */   
/*     */   public void setWfPostTemps(List<WfPostTemp> wfPostTemps) {
/* 222 */     this.wfPostTemps = wfPostTemps;
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\entity\WfProcTemp.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */