/*     */ package net.shopxx.wf.dao;
/*     */ 
/*     */

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.member.entity.StoreMember;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Repository("wfProcTempBaseDao")
/*     */ public class WfProcTempBaseDao
/*     */   extends DaoCenter
/*     */ {
/*     */   public List<Map<String, Object>> findListByWfTemp(Long wfTempId) {
/*  27 */     StringBuilder sql = new StringBuilder();
/*  28 */     sql.append("select * from wf_proc_temp where wf_temp = ? order by proc_temp_seq");
/*  29 */     List<Map<String, Object>> procTemps = getNativeDao().findListMap(sql.toString(), new Object[] { wfTempId }, 0);
/*     */     
/*  31 */     return procTemps;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> findListByWfStoreTemp(String username, String mobile, String name, Long wfprocTempId, Long saleOrgId) {
/* 105 */     StringBuilder sql = new StringBuilder();
/* 106 */     List<Object> list = new ArrayList();
/* 107 */     sql.append("SELECT s.* from wf_st w LEFT JOIN xx_store_member s on w.store_member=s.id WHERE w.wf_proc_temp=?");
/* 108 */     list.add(wfprocTempId);
/* 109 */     if (StringUtils.isNotEmpty(username)) {
/* 110 */       sql.append(" and s.username like ?");
/* 111 */       list.add("%" + username + "%");
/*     */     } 
/* 113 */     if (StringUtils.isNotEmpty(mobile)) {
/* 114 */       sql.append(" and m.mobile like ?");
/* 115 */       list.add("%" + mobile + "%");
/*     */     } 
/* 117 */     if (StringUtils.isNotEmpty(name)) {
/* 118 */       sql.append(" and s.name like ?");
/* 119 */       list.add("%" + name + "%");
/*     */     } 
/* 121 */     Object[] objs = new Object[list.size()];
/* 122 */     for (int i = 0; i < list.size(); i++) {
/* 123 */       objs[i] = list.get(i);
/*     */     }
/*     */     
/* 126 */     List<Map<String, Object>> maps = getNativeDao().findListMap(sql.toString(), objs, 0);
/*     */     
/* 128 */     return maps;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<StoreMember> findStoreMemberByPost(Long storeMemberId, Long saleOrgId, String posts) {
/* 133 */     StringBuilder sql = new StringBuilder();
/* 134 */     sql.append("select group_concat(org.tree_path,org.id separator '')");
/* 135 */     sql.append(" from xx_sale_org org where org.id=?");
/* 136 */     String orgIds = getNativeDao().findString(sql.toString(), new Object[] { saleOrgId });
/*     */     
/* 138 */     List<StoreMember> storeMembers = new ArrayList<StoreMember>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 158 */     sql.setLength(0);
/* 159 */     sql.append("select distinct sm.*");
/* 160 */     sql.append(" from wf_post_temp wpt,xx_store_member_sale_org_post smsop, xx_store_member sm");
/* 161 */     sql.append(" where wpt.post = smsop.post and smsop.store_member = sm.id and sm.is_enabled is true");
/* 162 */     if (orgIds != null) {
/* 163 */       orgIds = orgIds.substring(1);
/* 164 */       sql.append(" and smsop.sale_org in (" + orgIds + ")");
/*     */     } 
/* 166 */     sql.append(" and smsop.post in (" + posts + ")");
/* 167 */     storeMembers = getNativeDao().findListManaged(sql.toString(), null, 0, StoreMember.class);
/* 168 */     return storeMembers;
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\dao\WfProcTempBaseDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */