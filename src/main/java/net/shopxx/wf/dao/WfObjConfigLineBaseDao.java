/*    */ package net.shopxx.wf.dao;
/*    */ 
/*    */

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Repository("wfObjConfigLineBaseDao")
/*    */ public class WfObjConfigLineBaseDao
/*    */   extends DaoCenter
/*    */ {
/*    */   public List<Map<String, Object>> getConfig(Long obj_type_id, Boolean isFlow) {
/* 19 */     Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
/*    */     
/* 21 */     StringBuilder sql = new StringBuilder();
/* 22 */     sql.append("select c.*, w.wf_temp_name from wf_obj_config_line c, wf_temp w, wf_obj_config t");
/* 23 */     sql.append(" where c.wf_temp_id = w.id and t.id = c.obj_config_id");
/* 24 */     sql.append(" and c.obj_type_id = ? and c.company_info_id = ?");
/* 25 */     if (isFlow != null) {
/* 26 */       if (isFlow.booleanValue()) {
/* 27 */         sql.append(" and t.is_flow = 1");
/*    */       } else {
/*    */         
/* 30 */         sql.append(" and t.is_flow = 0");
/*    */       } 
/*    */     }
/*    */     
/* 34 */     List<Map<String, Object>> configs = getNativeDao().findListMap(sql.toString(), 
/* 35 */         new Object[] { obj_type_id, companyInfoId
/* 36 */         }, 0);
/*    */     
/* 38 */     return configs;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\dao\WfObjConfigLineBaseDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */