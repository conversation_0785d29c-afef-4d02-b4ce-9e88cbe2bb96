/*    */ package net.shopxx.wf.dao;
/*    */ 
/*    */

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Repository("wfUserOpinionBaseDao")
/*    */ public class WfUserOpinionBaseDao
/*    */   extends DaoCenter
/*    */ {
/*    */   public List<Map<String, Object>> findListByWf(Long wfid, Long procid) {
/* 24 */     if (ConvertUtil.isEmpty(wfid) && ConvertUtil.isEmpty(procid)) {
/* 25 */       ExceptionUtil.throwDaoException("value is empty!", new Object[0]);
/*    */     }
/*    */     
/* 28 */     List<Object> list = new ArrayList();
/*    */     
/* 30 */     StringBuilder sql = new StringBuilder();
/* 31 */     sql.append("select t.*, p.id proc_id, p.proc_name, s.id userid, s.name member_name");
/* 32 */     sql.append(" from wf_user_opinion t, wf_proc p, xx_store_member s");
/* 33 */     sql.append(" where t.store_member = s.id and t.wf_proc = p.id");
/* 34 */     if (wfid != null) {
/* 35 */       sql.append(" and t.wf_id = ?");
/* 36 */       list.add(wfid);
/*    */     } 
/* 38 */     if (procid != null) {
/* 39 */       sql.append(" and t.wf_proc = ?");
/* 40 */       list.add(procid);
/*    */     } 
/* 42 */     sql.append(" order by t.sign_time");
/*    */     
/* 44 */     Object[] objs = new Object[list.size()];
/* 45 */     for (int i = 0; i < list.size(); i++) {
/* 46 */       objs[i] = list.get(i);
/*    */     }
/*    */     
/* 49 */     List<Map<String, Object>> procs = getNativeDao().findListMap(sql.toString(), 
/* 50 */         objs, 
/* 51 */         0);
/*    */     
/* 53 */     return procs;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\dao\WfUserOpinionBaseDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */