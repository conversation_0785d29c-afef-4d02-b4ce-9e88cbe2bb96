/*     */ package net.shopxx.wf.dao;
/*     */ 
/*     */

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Repository("wfBaseDao")
/*     */ public class WfBaseDao
/*     */   extends DaoCenter
/*     */ {
/*     */   public Page<Map<String, Object>> findPage(Integer flag, Long userid, Long objType, String wfName, Integer stat, String subject, Long storeId, Pageable pageable) {
/*  28 */     List<Object> list = new ArrayList();
/*  29 */     Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
/*     */     
/*  31 */     StringBuilder sql = new StringBuilder();
/*  32 */     sql.append("select distinct w.*, o.url,s.name starter_name,l.name last_user_name, p.proc_name curr_proc_name,st.name store_name from wf w");
/*  33 */     sql.append(" left join wf_obj_config o on w.obj_type = o.obj_type_id");
/*  34 */     sql.append(" left join xx_store_member s on w.starter = s.id");
/*  35 */     sql.append(" left join xx_store_member l on w.last_user = l.id");
/*  36 */     sql.append(" left join wf_proc p on w.curr_proc_id = p.id");
/*  37 */     sql.append(" left join xx_store st on w.store = st.id");
/*  38 */     sql.append(" where 1 = 1");
/*     */     
/*  40 */     if (companyInfoId != null) {
/*  41 */       sql.append(" and w.company_info_id = ?");
/*  42 */       sql.append(" and o.company_info_id = ?");
/*  43 */       list.add(companyInfoId);
/*  44 */       list.add(companyInfoId);
/*     */     } 
/*  46 */     if (objType != null) {
/*  47 */       sql.append(" and w.obj_type = ?");
/*  48 */       list.add(objType);
/*     */     } 
/*  50 */     if (!ConvertUtil.isEmpty(wfName)) {
/*  51 */       sql.append(" and w.wf_name like ?");
/*  52 */       list.add("%" + wfName + "%");
/*     */     } 
/*  54 */     if (stat != null) {
/*  55 */       sql.append(" and w.stat = ?");
/*  56 */       list.add(stat);
/*     */     } 
/*  58 */     if (!ConvertUtil.isEmpty(subject)) {
/*  59 */       sql.append(" and w.subject like ?");
/*  60 */       list.add("%" + subject + "%");
/*     */     } 
/*  62 */     if (storeId != null) {
/*  63 */       sql.append(" and w.store = ?");
/*  64 */       list.add(storeId);
/*     */     } 
/*  66 */     if (flag != null) {
/*  67 */       if (flag.intValue() == 0) {
/*  68 */         sql.append(" and w.stat in (1, 2, 3)");
/*  69 */         sql.append(" and w.starter = ?");
/*  70 */         list.add(userid);
/*     */       }
/*  72 */       else if (flag.intValue() == 1) {
/*  73 */         sql.append(" and w.stat in (1, 3)");
/*  74 */         sql.append(" and exists (select 1 from wf_procuser where wf_id = w.id and wf_proc = w.curr_proc_id and store_member = ?)");
/*  75 */         list.add(userid);
/*     */       }
/*  77 */       else if (flag.intValue() == 2) {
/*  78 */         sql.append(" and w.stat = 1");
/*  79 */         sql.append(" and exists (select 1 from wf_procuser where wf_id = w.id and is_signed = 2 and store_member = ?)");
/*  80 */         list.add(userid);
/*     */       }
/*  82 */       else if (flag.intValue() == 3) {
/*  83 */         sql.append(" and w.stat = 2");
/*  84 */         sql.append(" and exists (select 1 from wf_procuser where wf_id = w.id and store_member = ?)");
/*  85 */         list.add(userid);
/*     */       } 
/*     */     }
/*  88 */     sql.append(" order by w.last_time desc");
/*     */     
/*  90 */     Object[] objs = new Object[list.size()];
/*  91 */     for (int i = 0; i < list.size(); i++) {
/*  92 */       objs[i] = list.get(i);
/*     */     }
/*     */     
/*  95 */     Page<Map<String, Object>> page = getNativeDao().findPageMap(sql.toString(), 
/*  96 */         objs, 
/*  97 */         pageable);
/*     */     
/*  99 */     return page;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> countList(Integer flag, Long userid, Long objType) {
/* 112 */     List<Object> list = new ArrayList();
/* 113 */     Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
/*     */     
/* 115 */     StringBuilder sql = new StringBuilder();
/* 116 */     sql.append("select w.obj_type, o.obj_type_name, count(1) num from wf w, wf_obj_config o");
/* 117 */     sql.append(" where w.obj_type = o.obj_type_id");
/*     */     
/* 119 */     if (companyInfoId != null) {
/* 120 */       sql.append(" and w.company_info_id = ?");
/* 121 */       sql.append(" and o.company_info_id = ?");
/* 122 */       list.add(companyInfoId);
/* 123 */       list.add(companyInfoId);
/*     */     } 
/* 125 */     if (objType != null) {
/* 126 */       sql.append(" and w.obj_type = ?");
/* 127 */       list.add(companyInfoId);
/*     */     } 
/*     */     
/* 130 */     if (flag != null) {
/* 131 */       if (flag.intValue() == 0) {
/* 132 */         sql.append(" and w.stat in (1, 2, 3)");
/* 133 */         sql.append(" and w.starter = ?");
/* 134 */         list.add(userid);
/*     */       }
/* 136 */       else if (flag.intValue() == 1) {
/* 137 */         sql.append(" and w.stat in (1, 3)");
/* 138 */         sql.append(" and exists (select 1 from wf_procuser where wf_id = w.id and wf_proc = w.curr_proc_id and store_member = ?)");
/* 139 */         list.add(userid);
/*     */       }
/* 141 */       else if (flag.intValue() == 2) {
/* 142 */         sql.append(" and w.stat = 1");
/* 143 */         sql.append(" and exists (select 1 from wf_procuser where wf_id = w.id and is_signed = 2 and store_member = ?)");
/* 144 */         list.add(userid);
/*     */       }
/* 146 */       else if (flag.intValue() == 3) {
/* 147 */         sql.append(" and w.stat = 2");
/* 148 */         sql.append(" and exists (select 1 from wf_procuser where wf_id = w.id and store_member = ?)");
/* 149 */         list.add(userid);
/*     */       } 
/*     */     }
/* 152 */     sql.append(" group by w.obj_type");
/*     */     
/* 154 */     Object[] objs = new Object[list.size()];
/* 155 */     for (int i = 0; i < list.size(); i++) {
/* 156 */       objs[i] = list.get(i);
/*     */     }
/*     */     
/* 159 */     List<Map<String, Object>> counts = getNativeDao().findListMap(sql.toString(), 
/* 160 */         objs, 
/* 161 */         0);
/*     */     
/* 163 */     return counts;
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\dao\WfBaseDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */