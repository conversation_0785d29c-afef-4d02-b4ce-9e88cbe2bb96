/*    */ package net.shopxx.wf.dao;
/*    */ 
/*    */

import net.shopxx.base.core.dao.impl.DaoCenter;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.WebUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Repository("wfObjConfigBaseDao")
/*    */ public class WfObjConfigBaseDao
/*    */   extends DaoCenter
/*    */ {
/*    */   public List<Map<String, Object>> findPage(String objTypeName, String wfTempName, Boolean isFlow, Boolean isBill) {
/* 28 */     List<Object> list = new ArrayList();
/* 29 */     Long companyInfoId = WebUtils.getCurrentCompanyInfoId();
/*    */     
/* 31 */     StringBuilder sql = new StringBuilder();
/* 32 */     sql.append("select c.*, group_concat(wt.wf_temp_name separator ';') wf_temp_name from wf_obj_config c");
/* 33 */     sql.append(" left join wf_obj_config_line cl on c.id = cl.obj_config_id");
/* 34 */     sql.append(" left join wf_temp wt on cl.wf_temp_id = wt.id");
/* 35 */     sql.append(" where 1 = 1");
/*    */     
/* 37 */     if (companyInfoId != null) {
/* 38 */       sql.append(" and c.company_info_id = ?");
/* 39 */       list.add(companyInfoId);
/*    */     } 
/* 41 */     if (!ConvertUtil.isEmpty(objTypeName)) {
/* 42 */       sql.append(" and c.obj_type_name like ?");
/* 43 */       list.add("%" + objTypeName + "%");
/*    */     } 
/*    */     
/* 46 */     if (!ConvertUtil.isEmpty(wfTempName)) {
/* 47 */       sql.append(" and wt.wf_temp_name like ?");
/* 48 */       list.add("%" + wfTempName + "%");
/*    */     } 
/* 50 */     if (isFlow != null) {
/* 51 */       if (isFlow.booleanValue()) {
/* 52 */         sql.append(" and c.is_flow = 1");
/*    */       } else {
/*    */         
/* 55 */         sql.append(" and c.is_flow = 0");
/*    */       } 
/*    */     }
/* 58 */     if (isBill != null) {
/* 59 */       if (isBill.booleanValue()) {
/* 60 */         sql.append(" and c.is_bill = 1");
/*    */       } else {
/*    */         
/* 63 */         sql.append(" and c.is_bill = 0");
/*    */       } 
/*    */     }
/* 66 */     sql.append(" group by c.id");
/*    */     
/* 68 */     Object[] objs = new Object[list.size()];
/* 69 */     for (int i = 0; i < list.size(); i++) {
/* 70 */       objs[i] = list.get(i);
/*    */     }
/*    */     
/* 73 */     List<Map<String, Object>> configs = getNativeDao().findListMap(sql.toString(), 
/* 74 */         objs, 
/* 75 */         0);
/*    */     
/* 77 */     return configs;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\dao\WfObjConfigBaseDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */