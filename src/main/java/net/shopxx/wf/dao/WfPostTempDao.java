/*    */ package net.shopxx.wf.dao;
/*    */ 
/*    */

import net.shopxx.base.core.dao.impl.DaoCenter;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ @Repository("wfPostTempDao")
/*    */ public class WfPostTempDao
/*    */   extends DaoCenter
/*    */ {
/*    */   public List<Map<String, Object>> findPostsByProcTemp(String procTempIds) {
/* 15 */     StringBuilder sql = new StringBuilder();
/* 16 */     sql.append("select wp.*, p.id postid, p.name post_name from wf_post_temp wp, xx_post p");
/* 17 */     sql.append(" where wp.post = p.id and wp.wf_proc_temp in (" + 
/* 18 */         procTempIds + 
/* 19 */         ")");
/* 20 */     List<Map<String, Object>> procTemps = getNativeDao().findListMap(sql.toString(), 
/* 21 */         null, 
/* 22 */         0);
/*    */     
/* 24 */     return procTemps;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\dao\WfPostTempDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */