/*    */ package net.shopxx.wf.dao;
/*    */ 
/*    */

import net.shopxx.base.core.dao.impl.DaoCenter;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Repository("wfProcusersTempBaseDao")
/*    */ public class WfProcusersTempBaseDao
/*    */   extends DaoCenter
/*    */ {
/*    */   public List<Map<String, Object>> findListByProcTemp(String procTempIds) {
/* 23 */     StringBuilder sql = new StringBuilder();
/* 24 */     sql.append("select ws.*, s.id userid, s.name member_name from wf_st ws, xx_store_member s");
/* 25 */     sql.append(" where ws.store_member = s.id and ws.wf_proc_temp in (" + 
/* 26 */         procTempIds + 
/* 27 */         ")");
/* 28 */     List<Map<String, Object>> procTemps = getNativeDao().findListMap(sql.toString(), 
/* 29 */         null, 
/* 30 */         0);
/*    */     
/* 32 */     return procTemps;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\dao\WfProcusersTempBaseDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */