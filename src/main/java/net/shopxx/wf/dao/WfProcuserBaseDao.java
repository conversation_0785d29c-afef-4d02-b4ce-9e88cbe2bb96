/*    */ package net.shopxx.wf.dao;
/*    */ 
/*    */

import net.shopxx.base.core.dao.impl.DaoCenter;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Repository("wfProcuserBaseDao")
/*    */ public class WfProcuserBaseDao
/*    */   extends DaoCenter
/*    */ {
/*    */   public void updateSign(Long wfId, Integer state) {
/* 17 */     if (wfId.longValue() != 0L)
/* 18 */       return;  String sql = " update wf_procuser set is_signed = ? where  wf_id = ?";
/* 19 */     getNativeDao().update(sql, new Object[] { state, wfId });
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public List<Map<String, Object>> findListByWf(String wfId) {
/* 29 */     StringBuilder sql = new StringBuilder();
/* 30 */     sql.append("select t.*, s.id userid, s.name member_name from wf_user_opinion t, xx_store_member s");
/* 31 */     sql.append(" where t.store_member = s.id and t.wf_proc = ? order by t.sign_time");
/* 32 */     List<Map<String, Object>> procs = getNativeDao().findListMap(sql.toString(), 
/* 33 */         null, 
/* 34 */         0);
/*    */     
/* 36 */     return procs;
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\dao\WfProcuserBaseDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */