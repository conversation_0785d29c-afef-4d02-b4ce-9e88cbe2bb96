package net.shopxx.wf.service;

import net.shopxx.base.core.service.BaseService;
import net.shopxx.wf.entity.WfObjConfig;

import java.util.List;
import java.util.Map;

public interface WfObjConfigBaseService extends BaseService<WfObjConfig> {
  List<Map<String, Object>> findPage(String paramString1, String paramString2, Boolean paramBoolean1, Boolean paramBoolean2);
  
  void saveorUpdate(WfObjConfig paramWfObjConfig, Long[] paramArrayOfLong, String[] paramArrayOfString);
  
  boolean isCheckWf(Long paramLong);
}


/* Location:              D:\wf\!\net\shopxx\wf\service\WfObjConfigBaseService.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */