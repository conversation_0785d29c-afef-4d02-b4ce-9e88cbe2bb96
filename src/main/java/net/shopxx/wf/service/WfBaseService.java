package net.shopxx.wf.service;

import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.wf.entity.Wf;

import java.util.List;
import java.util.Map;

public interface WfBaseService extends BaseService<Wf> {
  Page<Map<String, Object>> findPage(Integer paramInteger1, Long paramLong1, Long paramLong2, String paramString1, Integer paramInteger2, String paramString2, Long paramLong3, Pageable paramPageable);
  
  List<Map<String, Object>> countList(Integer paramInteger, Long paramLong1, Long paramLong2);
  
  Wf createwf(StoreMember paramStoreMember, Long paramLong1, String paramString, Long paramLong2, Long paramLong3, Long paramLong4);
  
  void start(Wf paramWf);
  
  void interrupt(Wf paramWf);
  
  void updateWfToBill(Wf paramWf, Integer paramInteger);
  
  void saveAttach(Wf paramWf);
  
  List<Integer> getAllWfStates();
}


/* Location:              D:\wf\!\net\shopxx\wf\service\WfBaseService.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */