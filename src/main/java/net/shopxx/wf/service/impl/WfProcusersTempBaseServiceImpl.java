/*    */ package net.shopxx.wf.service.impl;
/*    */ 
/*    */

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.wf.dao.WfProcusersTempBaseDao;
import net.shopxx.wf.entity.WfStoreTemp;
import net.shopxx.wf.service.WfProcusersTempBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("wfProcusersTempBaseServiceImpl")
/*    */ public class WfProcusersTempBaseServiceImpl
/*    */   extends BaseServiceImpl<WfStoreTemp>
/*    */   implements WfProcusersTempBaseService
/*    */ {
/*    */   @Resource(name = "wfProcusersTempBaseDao")
/*    */   private WfProcusersTempBaseDao wfProcusersTempBaseDao;
/*    */   
/*    */   @Transactional(readOnly = true)
/*    */   public List<Map<String, Object>> findListByProcTemp(String procTempIds) {
/* 29 */     return this.wfProcusersTempBaseDao.findListByProcTemp(procTempIds);
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfProcusersTempBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */