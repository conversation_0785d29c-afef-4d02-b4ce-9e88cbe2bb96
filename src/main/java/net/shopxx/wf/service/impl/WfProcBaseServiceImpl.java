/*     */ package net.shopxx.wf.service.impl;
/*     */ 
/*     */

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ConvertUtil;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.wf.entity.*;
import net.shopxx.wf.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Service("wfProcBaseServiceImpl")
/*     */ public class WfProcBaseServiceImpl
/*     */   extends BaseServiceImpl<WfProc>
/*     */   implements WfProcBaseService
/*     */ {
/*     */   @Resource(name = "wfBaseServiceImpl")
/*     */   private WfBaseService wfBaseService;
/*     */   @Resource(name = "wfProcuserBaseServiceImpl")
/*     */   private WfProcuserBaseService wfProcuserBaseService;
/*     */   @Resource(name = "wfUserOpinionBaseServiceImpl")
/*     */   private WfUserOpinionBaseService wfUserOpinionBaseService;
/*     */   @Resource(name = "storeMemberBaseServiceImpl")
/*     */   private StoreMemberBaseService storeMemberBaseService;
/*     */   @Resource(name = "wfObjConfigBaseServiceImpl")
/*     */   private WfObjConfigBaseService wfObjConfigBaseService;
/*     */   @Resource(name = "lockDataDao")
/*     */   private LockDataDao lockDataDao;
/*     */   
/*     */   @Transactional
/*     */   public void start(WfProc wfProc, Wf wf) {
/*  58 */     if (wfProc.getProcType().intValue() == 2) {
/*  59 */       wfProc.setStat(Integer.valueOf(7));
/*  60 */       update(wfProc);
/*  61 */       wf.setCurrProcId(wfProc.getId());
/*  62 */       wf.setStat(Integer.valueOf(2));
/*  63 */       wf.setEndTime(new Date());
/*  64 */       this.wfBaseService.update(wf);
/*     */     } else {
/*     */       
/*  67 */       wfProc.setStat(Integer.valueOf(4));
/*  68 */       update(wfProc);
/*  69 */       wf.setCurrProcId(wfProc.getId());
/*  70 */       this.wfBaseService.update(wf);
/*     */ 
/*     */       
/*  73 */       List<Filter> filters = new ArrayList<Filter>();
/*  74 */       filters.add(Filter.eq("wfId", wf.getId()));
/*  75 */       filters.add(Filter.eq("wfProc", wfProc));
/*  76 */       List<WfProcuser> wfProcusers = this.wfProcuserBaseService.findList(null, 
/*  77 */           filters, 
/*  78 */           null);
/*  79 */       for (WfProcuser wfProcuser : wfProcusers) {
/*  80 */         wfProcuser.setIsSigned(Integer.valueOf(1));
/*  81 */         this.wfProcuserBaseService.update(wfProcuser);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  86 */     if (wfProc.getIsSkip().booleanValue() && wfProc.getProcType().intValue() != 2) {
/*  87 */       submit(wfProc, wf);
/*     */     }
/*     */     else {
/*     */       
/*  91 */       this.wfBaseService.updateWfToBill(wf, Integer.valueOf(0));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void submit(WfProc currWfProc, Wf wf) {
/*  99 */     boolean isNext = false;
/*     */ 
/*     */     
/* 102 */     Integer procType = currWfProc.getProcType();
/* 103 */     if (procType.intValue() == 2)
/*     */     {
/* 105 */       ExceptionUtil.throwServiceException("结束节点不能提交", new Object[0]);
/*     */     }
/*     */     
/* 108 */     if (currWfProc.getAfterproc() == null)
/*     */     {
/* 110 */       ExceptionUtil.throwServiceException("当前节点没有后续节点，不能提交", new Object[0]);
/*     */     }
/*     */     
/* 113 */     if (checkSign(currWfProc) || currWfProc.getIsSkip().booleanValue()) {
/* 114 */       isNext = true;
/*     */     } else {
/*     */       
/* 117 */       isNext = false;
/*     */     } 
/*     */ 
/*     */     
/* 121 */     if (wf.getStat().intValue() == 3) {
/* 122 */       wf.setStat(Integer.valueOf(1));
/* 123 */       this.wfBaseService.update(wf);
/*     */     } 
/*     */     
/* 126 */     if (isNext) {
/* 127 */       currWfProc.setStat(Integer.valueOf(7));
/* 128 */       update(currWfProc);
/*     */       
/* 130 */       start(currWfProc.getAfterproc(), wf);
/*     */     }
/*     */     else {
/*     */       
/* 134 */       this.wfBaseService.updateWfToBill(wf, Integer.valueOf(0));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void agree(WfProc currWfProc, Wf wf, StoreMember storeMember, String opinion) {
/* 145 */     this.lockDataDao.lockWf(wf.getId().toString());
/*     */     
/* 147 */     if (wf.getStat() != null && wf.getStat().intValue() == 4) {
/* 148 */       ExceptionUtil.throwServiceException("该流程已中断", new Object[0]);
/*     */     }
/*     */ 
/*     */     
/* 152 */     writeOpinion(currWfProc, wf, storeMember, opinion, 1);
/*     */ 
/*     */     
/* 155 */     submit(currWfProc, wf);
/*     */ 
/*     */     
/* 158 */     List<Filter> filters = new ArrayList<Filter>();
/* 159 */     filters.add(Filter.eq("companyInfoId", wf.getCompanyInfoId()));
/* 160 */     filters.add(Filter.eq("objTypeId", wf.getObjType()));
/* 161 */     WfObjConfig wfObjConfig = (WfObjConfig)this.wfObjConfigBaseService.find(filters);
/* 162 */     String objClassName = wfObjConfig.getObjClass();
/* 163 */     WfBillBaseService<WfBillEntity> billService = (WfBillBaseService<WfBillEntity>)SpringUtils.getBean(objClassName);
/* 164 */     billService.agreeBack(wf);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void reject(WfProc currWfProc, Wf wf, StoreMember storeMember, String opinion) {
/* 174 */     this.lockDataDao.lockWf(wf.getId().toString());
/*     */     
/* 176 */     if (wf.getStat() != null && wf.getStat().intValue() == 4) {
/* 177 */       ExceptionUtil.throwServiceException("该流程已中断", new Object[0]);
/*     */     }
/*     */     
/* 180 */     if (currWfProc.getProcType().intValue() == 1) {
/* 181 */       ExceptionUtil.throwServiceException("开始节点不允许驳回操作", new Object[0]);
/*     */     }
/*     */ 
/*     */     
/* 185 */     writeOpinion(currWfProc, wf, storeMember, opinion, 2);
/*     */ 
/*     */     
/* 188 */     rejectHandler(currWfProc, wf);
/*     */ 
/*     */     
/* 191 */     List<Filter> filters = new ArrayList<Filter>();
/* 192 */     filters.add(Filter.eq("companyInfoId", wf.getCompanyInfoId()));
/* 193 */     filters.add(Filter.eq("objTypeId", wf.getObjType()));
/* 194 */     WfObjConfig wfObjConfig = (WfObjConfig)this.wfObjConfigBaseService.find(filters);
/* 195 */     String objClassName = wfObjConfig.getObjClass();
/* 196 */     WfBillBaseService<WfBillEntity> billService = (WfBillBaseService<WfBillEntity>)SpringUtils.getBean(objClassName);
/* 197 */     billService.rejectBack(wf);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeOpinion(WfProc currWfProc, Wf wf, StoreMember storeMember, String opinion, int stat) {
/* 203 */     if (currWfProc.getStat().intValue() == 7 || currWfProc.getStat().intValue() == 5) {
/* 204 */       ExceptionUtil.throwServiceException("当前步骤已处理，请刷新重试", new Object[0]);
/*     */     }
/*     */ 
/*     */     
/* 208 */     List<Filter> filters = new ArrayList<Filter>();
/* 209 */     filters.add(Filter.eq("wfId", wf.getId()));
/* 210 */     filters.add(Filter.eq("wfProc", currWfProc));
/* 211 */     filters.add(Filter.eq("storeMember", storeMember));
/* 212 */     List<WfProcuser> wfProcusers = this.wfProcuserBaseService.findList(Integer.valueOf(1), 
/* 213 */         filters, 
/* 214 */         null);
/* 215 */     if (wfProcusers != null && wfProcusers.size() > 0) {
/* 216 */       WfProcuser wfProcuser = wfProcusers.get(0);
/* 217 */       if (stat == 1) {
/* 218 */         wfProcuser.setIsSigned(Integer.valueOf(2));
/*     */       } else {
/*     */         
/* 221 */         wfProcuser.setIsSigned(Integer.valueOf(4));
/*     */       } 
/* 223 */       Integer freq = Integer.valueOf((wfProcuser.getFreq() == null) ? 0 : 
/* 224 */           wfProcuser.getFreq().intValue());
/* 225 */       wfProcuser.setFreq(Integer.valueOf(freq.intValue() + 1));
/* 226 */       this.wfProcuserBaseService.update(wfProcuser);
/*     */       
/* 228 */       WfUserOpinion twUserOpinion = new WfUserOpinion();
/* 229 */       twUserOpinion.setCompanyInfoId(wf.getCompanyInfoId());
/* 230 */       twUserOpinion.setWfId(wf.getId());
/* 231 */       twUserOpinion.setWfProc(currWfProc);
/* 232 */       twUserOpinion.setStoreMember(storeMember);
/* 233 */       if (stat == 1) {
/* 234 */         twUserOpinion.setStat(Integer.valueOf(7));
/* 235 */         if (ConvertUtil.isEmpty(opinion)) {
/* 236 */           opinion = "同意";
/*     */         }
/*     */       } else {
/*     */         
/* 240 */         twUserOpinion.setStat(Integer.valueOf(5));
/* 241 */         if (ConvertUtil.isEmpty(opinion)) {
/* 242 */           opinion = "不同意";
/*     */         }
/*     */       } 
/*     */       
/* 246 */       twUserOpinion.setSignTime(new Date());
/* 247 */       twUserOpinion.setOpinion(opinion);
/* 248 */       this.wfUserOpinionBaseService.save(twUserOpinion);
/*     */     } else {
/*     */       
/* 251 */       ExceptionUtil.throwServiceException("您不是当前节点用户，不可操作", new Object[0]);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void rejectHandler(WfProc currWfProc, Wf wf) {
/* 260 */     for (WfProc wfProc : wf.getWfProcs()) {
/* 261 */       if (wfProc.getProcType().intValue() == 1) {
/* 262 */         wfProc.setStat(Integer.valueOf(4));
/*     */         
/* 264 */         List<Filter> filters = new ArrayList<Filter>();
/* 265 */         filters.add(Filter.eq("wfId", wf.getId()));
/* 266 */         filters.add(Filter.eq("wfProc", wfProc));
/* 267 */         List<WfProcuser> wfProcusers = this.wfProcuserBaseService.findList(null, 
/* 268 */             filters, 
/* 269 */             null);
/* 270 */         for (WfProcuser wfProcuser : wfProcusers) {
/* 271 */           wfProcuser.setIsSigned(Integer.valueOf(1));
/* 272 */           this.wfProcuserBaseService.update(wfProcuser);
/*     */         } 
/*     */       } else {
/*     */         
/* 276 */         wfProc.setStat(Integer.valueOf(1));
/*     */       } 
/* 278 */       update(wfProc);
/*     */     } 
/* 280 */     currWfProc.setStat(Integer.valueOf(5));
/* 281 */     update(currWfProc);
/*     */     
/* 283 */     wf.setStat(Integer.valueOf(3));
/* 284 */     wf.setCurrProcId((wf.getStartProc() != null) ? wf.getStartProc().getId() : 
/* 285 */         null);
/* 286 */     wf.setLastUser(wf.getStarter());
/* 287 */     wf.setLastTime(new Date());
/* 288 */     this.wfBaseService.update(wf);
/*     */ 
/*     */     
/* 291 */     this.wfBaseService.updateWfToBill(wf, Integer.valueOf(0));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkSign(WfProc wfProc) {
/* 301 */     boolean flag = false;
/* 302 */     List<WfProcuser> wfProcusers = wfProc.getWfProcusers();
/*     */     
/* 304 */     int person_num = wfProcusers.size();
/*     */     
/* 306 */     int signed_num = getSignedNum(wfProc);
/*     */     
/* 308 */     if (wfProc.getAllPerson().booleanValue()) {
/* 309 */       if (signed_num == person_num) {
/* 310 */         flag = true;
/*     */       }
/*     */     } else {
/*     */       
/* 314 */       int minPerson = wfProc.getMinPerson().intValue();
/* 315 */       if (signed_num >= minPerson) {
/* 316 */         flag = true;
/*     */       }
/*     */     } 
/*     */     
/* 320 */     return flag;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getSignedNum(WfProc wfProc) {
/* 330 */     List<WfProcuser> wfProcusers = wfProc.getWfProcusers();
/* 331 */     int num = 0;
/* 332 */     for (WfProcuser wfProcuser : wfProcusers) {
/* 333 */       if (wfProcuser.getIsSigned().intValue() == 2) {
/* 334 */         num++;
/*     */       }
/*     */     } 
/* 337 */     return num;
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfProcBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */