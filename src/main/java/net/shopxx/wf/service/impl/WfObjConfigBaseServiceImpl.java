/*     */ package net.shopxx.wf.service.impl;
/*     */ 
/*     */

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.WebUtils;
import net.shopxx.wf.dao.WfObjConfigBaseDao;
import net.shopxx.wf.entity.WfObjConfig;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.service.WfObjConfigBaseService;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Service("wfObjConfigBaseServiceImpl")
/*     */ public class WfObjConfigBaseServiceImpl
/*     */   extends BaseServiceImpl<WfObjConfig>
/*     */   implements WfObjConfigBaseService
/*     */ {
/*     */   @Resource(name = "wfObjConfigBaseDao")
/*     */   private WfObjConfigBaseDao wfObjConfigBaseDao;
/*     */   @Resource(name = "wfObjConfigLineBaseServiceImpl")
/*     */   private WfObjConfigLineBaseService wfObjConfigLineBaseService;
/*     */   
/*     */   @Transactional(readOnly = true)
/*     */   public List<Map<String, Object>> findPage(String objTypeName, String wfTempName, Boolean isFlow, Boolean isBill) {
/*  37 */     return this.wfObjConfigBaseDao.findPage(objTypeName, 
/*  38 */         wfTempName, 
/*  39 */         isFlow, 
/*  40 */         isBill);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void saveorUpdate(WfObjConfig wfObjConfig, Long[] wfTempId, String[] conditions) {
/*  48 */     if (wfObjConfig.getId() == null) {
/*  49 */       save(wfObjConfig);
/*     */     } else {
/*     */       
/*  52 */       update(wfObjConfig, new String[] { "objTypeId", "objClass", "tableName", "url" });
/*     */       
/*  54 */       getDaoCenter().getNativeDao()
/*  55 */         .delete("delete from wf_obj_config_line where obj_config_id = ?", 
/*  56 */           new Object[] { wfObjConfig.getId() });
/*     */     } 
/*     */     
/*  59 */     WfObjConfig pWfObjConfig = (WfObjConfig)find(wfObjConfig.getId());
/*  60 */     Long objConfigId = pWfObjConfig.getId();
/*  61 */     Long objTypeId = pWfObjConfig.getObjTypeId();
/*  62 */     if (wfTempId != null) {
/*  63 */       for (int i = 0; i < wfTempId.length; i++) {
/*     */         
/*  65 */         WfObjConfigLine wfObjConfigLine = new WfObjConfigLine();
/*  66 */         wfObjConfigLine.setObjConfigId(objConfigId);
/*  67 */         wfObjConfigLine.setWfTempId(wfTempId[i]);
/*  68 */         wfObjConfigLine.setConditions((conditions == null || conditions.length == 0) ? null : 
/*  69 */             conditions[i]);
/*  70 */         wfObjConfigLine.setObjTypeId(objTypeId);
/*  71 */         this.wfObjConfigLineBaseService.save(wfObjConfigLine);
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCheckWf(Long objTypeId) {
/*  90 */     boolean isCheckWf = false;
/*  91 */     if (objTypeId != null) {
/*  92 */       List<Filter> filters = new ArrayList<Filter>();
/*  93 */       filters.add(Filter.eq("companyInfoId", 
/*  94 */             WebUtils.getCurrentCompanyInfoId()));
/*  95 */       filters.add(Filter.eq("objTypeId", objTypeId));
/*  96 */       WfObjConfig wfObjConfig = (WfObjConfig)find(filters);
/*  97 */       if (wfObjConfig != null) {
/*  98 */         isCheckWf = (wfObjConfig.getIsFlow() == null) ? false : 
/*  99 */           wfObjConfig.getIsFlow().booleanValue();
/*     */       } else {
/* 101 */         isCheckWf = false;
/*     */       } 
/*     */     } 
/* 104 */     return isCheckWf;
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfObjConfigBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */