/*    */ package net.shopxx.wf.service.impl;
/*    */ 
/*    */

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.wf.dao.WfProcuserBaseDao;
import net.shopxx.wf.entity.WfProc;
import net.shopxx.wf.entity.WfProcuser;
import net.shopxx.wf.service.WfProcBaseService;
import net.shopxx.wf.service.WfProcuserBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("wfProcuserBaseServiceImpl")
/*    */ public class WfProcuserBaseServiceImpl
/*    */   extends BaseServiceImpl<WfProcuser>
/*    */   implements WfProcuserBaseService
/*    */ {
/*    */   @Resource(name = "wfProcuserBaseDao")
/*    */   private WfProcuserBaseDao wfProcuserBaseDao;
/*    */   @Resource(name = "wfProcBaseServiceImpl")
/*    */   private WfProcBaseService wfProcBaseService;
/*    */   @Resource(name = "storeMemberBaseServiceImpl")
/*    */   private StoreMemberBaseService storeMemberBaseService;
/*    */   
/*    */   @Transactional
/*    */   public void updateSign(Long wfId, Integer state) {
/* 36 */     this.wfProcuserBaseDao.updateSign(wfId, state);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Transactional
/*    */   public void updateProcUser(Long wfid, Long procid, Long[] userid, Integer minPerson, Boolean allPerson) {
/* 44 */     WfProc wfProc = (WfProc)this.wfProcBaseService.find(procid);
/*    */     
/* 46 */     List<WfProcuser> wfProcusers = wfProc.getWfProcusers();
/* 47 */     for (WfProcuser wfProcuser : wfProcusers) {
/* 48 */       delete(wfProcuser);
/*    */     }
/*    */     
/* 51 */     Long wfId = wfProc.getWf().getId();
/* 52 */     if (userid != null) {
/* 53 */       byte b; int i; Long[] arrayOfLong; for (i = (arrayOfLong = userid).length, b = 0; b < i; ) { Long memberid = arrayOfLong[b];
/* 54 */         WfProcuser wfProcuser = new WfProcuser();
/* 55 */         StoreMember storeMember = (StoreMember)this.storeMemberBaseService.find(memberid);
/* 56 */         wfProcuser.setStoreMember(storeMember);
/* 57 */         wfProcuser.setTempUser(Boolean.valueOf(false));
/* 58 */         wfProcuser.setWfId(wfId);
/* 59 */         wfProcuser.setWfProc(wfProc);
/* 60 */         save(wfProcuser);
/*    */         b++; }
/*    */     
/*    */     } 
/* 64 */     wfProc.setMinPerson(Integer.valueOf((minPerson == null) ? 0 : minPerson.intValue()));
/* 65 */     wfProc.setAllPerson(Boolean.valueOf((allPerson == null) ? false : allPerson.booleanValue()));
/* 66 */     this.wfProcBaseService.update(wfProc);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   @Transactional(readOnly = true)
/*    */   public boolean isProcuser(Long procid, Long userid) {
/* 73 */     StringBuilder sql = new StringBuilder();
/* 74 */     sql.append("select count(1) from wf_procuser where is_signed in (1, 4, 5) and wf_proc = ? and store_member = ?");
/* 75 */     int count = getDaoCenter().getNativeDao().findInt(sql.toString(), 
/* 76 */         new Object[] { procid, userid }).intValue();
/*    */     
/* 78 */     return (count > 0);
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfProcuserBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */