/*    */ package net.shopxx.wf.service.impl;
/*    */ 
/*    */

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.wf.dao.WfPostTempDao;
import net.shopxx.wf.entity.WfPostTemp;
import net.shopxx.wf.service.WfPostTempService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("wfPostTempServiceImpl")
/*    */ public class WfPostTempServiceImpl
/*    */   extends BaseServiceImpl<WfPostTemp>
/*    */   implements WfPostTempService
/*    */ {
/*    */   @Resource(name = "wfPostTempDao")
/*    */   private WfPostTempDao wfPostTempDao;
/*    */   
/*    */   @Transactional(readOnly = true)
/*    */   public List<Map<String, Object>> findPostsByProcTemp(String procTempIds) {
/* 26 */     return this.wfPostTempDao.findPostsByProcTemp(procTempIds);
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfPostTempServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */