/*     */ package net.shopxx.wf.service.impl;
/*     */ 
/*     */

import net.shopxx.base.core.Filter;
import net.shopxx.base.core.Page;
import net.shopxx.base.core.Pageable;
import net.shopxx.base.core.service.BaseService;
import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.base.core.util.ExceptionUtil;
import net.shopxx.base.core.util.SpringUtils;
import net.shopxx.common.dao.LockDataDao;
import net.shopxx.member.entity.StoreMember;
import net.shopxx.member.service.StoreMemberBaseService;
import net.shopxx.wf.dao.WfBaseDao;
import net.shopxx.wf.dao.WfProcTempBaseDao;
import net.shopxx.wf.entity.*;
import net.shopxx.wf.service.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Service("wfBaseServiceImpl")
/*     */ public class WfBaseServiceImpl
/*     */   extends BaseServiceImpl<Wf>
/*     */   implements WfBaseService
/*     */ {
/*     */   @Resource(name = "lockDataDao")
/*     */   private LockDataDao lockDataDao;
/*     */   @Resource(name = "wfBaseDao")
/*     */   private WfBaseDao wfBaseDao;
/*     */   @Resource(name = "wfProcTempBaseDao")
/*     */   private WfProcTempBaseDao wfProcTempBaseDao;
/*     */   @Resource(name = "wfProcBaseServiceImpl")
/*     */   private WfProcBaseService wfProcBaseService;
/*     */   @Resource(name = "wfProcuserBaseServiceImpl")
/*     */   private WfProcuserBaseService wfProcuserBaseService;
/*     */   @Resource(name = "storeMemberBaseServiceImpl")
/*     */   private StoreMemberBaseService storeMemberService;
/*     */   @Resource(name = "wfObjConfigBaseServiceImpl")
/*     */   private WfObjConfigBaseService wfObjConfigBaseService;
/*     */   @Resource(name = "wfTempBaseServiceImpl")
/*     */   private WfTempBaseService wfTempBaseService;
/*     */   @Resource(name = "wfAttachBaseServiceImpl")
/*     */   private WfAttachBaseService wfAttachBaseService;
/*     */   @Resource(name = "wfStoreMemberSaleOrgPostServiceImpl")
/*     */   private StoreMemberSaleOrgPostService storeMemberSaleOrgPostService;
/*     */   
/*     */   @Transactional(readOnly = true)
/*     */   public Page<Map<String, Object>> findPage(Integer flag, Long userid, Long objType, String wfName, Integer stat, String subject, Long storeId, Pageable pageable) {
/*  77 */     return this.wfBaseDao.findPage(flag, userid, objType, wfName, stat, subject, storeId, pageable);
/*     */   }
/*     */ 
/*     */   
/*     */   @Transactional(readOnly = true)
/*     */   public List<Map<String, Object>> countList(Integer flag, Long userid, Long objType) {
/*  83 */     return this.wfBaseDao.countList(flag, userid, objType);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public Wf createwf(StoreMember storeMember, Long saleOrgId, String obj_sn, Long wf_temp_id, Long obj_type_id, Long obj_id) {
/*  91 */     Long companyInfoId = storeMember.getCompanyInfoId();
/*     */     
/*  93 */     if (wf_temp_id == null) {
/*  94 */       ExceptionUtil.throwServiceException("流程模版id不能空", new Object[0]);
/*     */     }
/*  96 */     if (obj_type_id == null) {
/*  97 */       ExceptionUtil.throwServiceException("单据类型id不能空", new Object[0]);
/*     */     }
/*  99 */     if (obj_id == null) {
/* 100 */       ExceptionUtil.throwServiceException("单据id不能空", new Object[0]);
/*     */     }
/*     */ 
/*     */     
/* 104 */     WfTemp wfTemp = (WfTemp)this.wfTempBaseService.find(wf_temp_id);
/*     */     
/* 106 */     Wf wf = new Wf();
/*     */ 
/*     */     
/* 109 */     List<WfProc> wfProcs = new ArrayList<WfProc>();
/*     */     
/* 111 */     List<WfAttach> wfAttachs = new ArrayList<WfAttach>();
/*     */     
/* 113 */     wf.setWfName(wfTemp.getWfTempName());
/* 114 */     wf.setWfTempId(wfTemp.getId());
/* 115 */     wf.setObjType(obj_type_id);
/* 116 */     wf.setObjId(obj_id);
/* 117 */     wf.setNote(wfTemp.getNote());
/*     */     
/* 119 */     wf.setStat(Integer.valueOf(0));
/* 120 */     wf.setStatTime(new Date());
/* 121 */     if (obj_sn != null) {
/* 122 */       wf.setSubject(String.valueOf(obj_sn) + "-" + ((storeMember.getName() == null) ? storeMember.getUsername() : storeMember.getName()) + "-" + 
/* 123 */           wfTemp.getWfTempName());
/*     */     } else {
/* 125 */       wf.setSubject(String.valueOf((storeMember.getName() == null) ? storeMember.getUsername() : storeMember.getName()) + "-" + 
/* 126 */           wfTemp.getWfTempName());
/*     */     } 
/* 128 */     wf.setLastUser(storeMember);
/* 129 */     wf.setLastTime(new Date());
/* 130 */     wf.setStarter(storeMember);
/* 131 */     wf.setCompanyInfoId(companyInfoId);
/* 132 */     save(wf);
/*     */     
/* 134 */     List<WfProcTemp> wfProcTemps = wfTemp.getWfProcTemps();
/* 135 */     List<Filter> filters = new ArrayList<Filter>();
/* 136 */     WfProc preProc = null;
/* 137 */     for (int i = 0; i < wfProcTemps.size(); i++) {
/* 138 */       WfProcTemp wfProcTemp = wfProcTemps.get(i);
/* 139 */       WfProc wfProc = new WfProc();
/* 140 */       wfProc.setProcSeq(wfProcTemp.getProcTempSeq());
/* 141 */       wfProc.setProcName(wfProcTemp.getProcTempName());
/* 142 */       wfProc.setProcType(wfProcTemp.getProcType());
/* 143 */       wfProc.setIsSkip(Boolean.valueOf((wfProcTemp.getIsSkip() == null) ? false : wfProcTemp.getIsSkip().booleanValue()));
/* 144 */       wfProc.setMinPerson(Integer.valueOf((wfProcTemp.getMinPerson() == null) ? 0 : wfProcTemp.getMinPerson().intValue()));
/* 145 */       wfProc.setAllPerson(Boolean.valueOf((wfProcTemp.getAllPerson() == null) ? false : wfProcTemp.getAllPerson().booleanValue()));
/* 146 */       wfProc.setIsSendSms(Boolean.valueOf((wfProcTemp.getIsSendSms() == null) ? false : wfProcTemp.getIsSendSms().booleanValue()));
/* 147 */       wfProc.setSpecialHandleCode(StringUtils.defaultIfBlank(wfProcTemp.getSpecialHandleCode(), null));
/* 148 */       wfProc.setProcTempId(wfProcTemp.getId());
/*     */       
/* 150 */       if (preProc != null) {
/* 151 */         wfProc.setPreproc(preProc);
/*     */       }
/*     */       
/* 154 */       wfProc.setDefOpinion(wfProcTemp.getDefOpinion());
/* 155 */       wfProc.setNote(null);
/*     */       
/* 157 */       wfProc.setStat(Integer.valueOf(1));
/* 158 */       wfProc.setWf(wf);
/* 159 */       wfProc.setCompanyInfoId(wf.getCompanyInfoId());
/* 160 */       this.wfProcBaseService.save(wfProc);
/*     */       
/* 162 */       if (wfProc.getProcType().intValue() == 1) {
/* 163 */         wf.setCurrProcId(wfProc.getId());
/*     */       }
/*     */ 
/*     */       
/* 167 */       List<WfProcuser> wfProcusers = new ArrayList<WfProcuser>();
/* 168 */       if (wfProcTemp.getProcType().intValue() == 1) {
/*     */         
/* 170 */         WfProcuser wfProcuser = new WfProcuser();
/*     */         
/* 172 */         wfProcuser.setStoreMember(storeMember);
/* 173 */         wfProcuser.setNote(null);
/* 174 */         wfProcuser.setTempUser(Boolean.valueOf(false));
/* 175 */         wfProcuser.setWfId(wf.getId());
/* 176 */         wfProcuser.setWfProc(wfProc);
/* 177 */         wfProcuser.setCompanyInfoId(wf.getCompanyInfoId());
/* 178 */         this.wfProcuserBaseService.save(wfProcuser);
/* 179 */         wfProcusers.add(wfProcuser);
/*     */       } else {
/* 181 */         List<StoreMember> storeMembers = new ArrayList<StoreMember>();
/*     */         
/* 183 */         List<WfProcuserTemp> wfProcuserTemps = wfProcTemp.getWfProcuserTemps();
/* 184 */         if (!wfProcuserTemps.isEmpty()) {
/* 185 */           for (int j = 0; j < wfProcuserTemps.size(); j++) {
/* 186 */             WfProcuserTemp twProcuserTemp = wfProcuserTemps.get(j);
/* 187 */             StoreMember sm = twProcuserTemp.getStoreMember();
/* 188 */             if (!storeMembers.contains(sm)) {
/* 189 */               storeMembers.add(sm);
/*     */             }
/*     */           } 
/*     */         }
/*     */         
/* 194 */         List<WfPostTemp> wfPostTemps = wfProcTemp.getWfPostTemps();
/* 195 */         if (!wfPostTemps.isEmpty()) {
/* 196 */           String postIds = "";
/* 197 */           for (int j = 0; j < wfPostTemps.size(); j++) {
/* 198 */             WfPostTemp wfPostTemp = wfPostTemps.get(j);
/* 199 */             Long postId = wfPostTemp.getPost().getId();
/* 200 */             postIds = String.valueOf(postIds) + "," + postId;
/*     */           } 
/* 202 */           postIds = postIds.substring(1);
/* 203 */           List<StoreMember> sMembers = this.wfProcTempBaseDao.findStoreMemberByPost(storeMember.getId(), saleOrgId, 
/* 204 */               postIds);
/* 205 */           for (StoreMember sm : sMembers) {
/* 206 */             if (!storeMembers.contains(sm)) {
/* 207 */               storeMembers.add(sm);
/*     */             }
/*     */           } 
/*     */         } 
/* 211 */         if (!storeMembers.isEmpty()) {
/* 212 */           for (StoreMember sMember : storeMembers) {
/* 213 */             WfProcuser twProcuser = new WfProcuser();
/* 214 */             twProcuser.setStoreMember(sMember);
/* 215 */             twProcuser.setNote(null);
/* 216 */             twProcuser.setTempUser(Boolean.valueOf(true));
/* 217 */             twProcuser.setWfId(wf.getId());
/* 218 */             twProcuser.setWfProc(wfProc);
/* 219 */             twProcuser.setCompanyInfoId(wf.getCompanyInfoId());
/* 220 */             this.wfProcuserBaseService.save(twProcuser);
/* 221 */             wfProcusers.add(twProcuser);
/*     */           } 
/*     */         }
/*     */       } 
/* 225 */       wfProc.setWfProcusers(wfProcusers);
/* 226 */       this.wfProcBaseService.update(wfProc);
/*     */       
/* 228 */       wfProcs.add(wfProc);
/* 229 */       if (preProc != null) {
/* 230 */         preProc.setAfterproc(wfProc);
/* 231 */         this.wfProcBaseService.update(preProc);
/*     */       } 
/* 233 */       preProc = wfProc;
/*     */     } 
/*     */     
/* 236 */     if (wfProcs != null && wfProcs.size() > 0)
/* 237 */       wf.setWfProcs(wfProcs); 
/* 238 */     if (wfAttachs != null && wfAttachs.size() > 0) {
/* 239 */       wf.setWfAttachs(wfAttachs);
/*     */     }
/* 241 */     update(wf);
/*     */     
/* 243 */     updateWfToBill(wf, Integer.valueOf(0));
/*     */ 
/*     */     
/* 246 */     autoStart(wf, storeMember);
/*     */     
/* 248 */     return wf;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void autoStart(Wf wf, StoreMember storeMember) {
/* 254 */     List<WfProc> wfProcs = wf.getWfProcs();
/* 255 */     for (WfProc wfProc : wfProcs) {
/* 256 */       if (wfProc.getProcType().intValue() == 3 && 
/* 257 */         wfProc.getWfProcusers().isEmpty()) {
/*     */         return;
/*     */       }
/*     */     } 
/* 261 */     for (WfProc wfProc : wfProcs) {
/* 262 */       if (wfProc.getProcType().intValue() == 1) {
/* 263 */         WfProc wfProc2 = (WfProc)this.wfProcBaseService.find(wfProc.getId());
/* 264 */         wfProc2.setStat(Integer.valueOf(4));
/* 265 */         this.wfProcBaseService.update(wfProc2);
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/* 270 */     wf.setStat(Integer.valueOf(1));
/* 271 */     update(wf);
/* 272 */     updateWfToBill(wf, Integer.valueOf(0));
/*     */     
/* 274 */     List<Filter> filters = new ArrayList<Filter>();
/* 275 */     filters.add(Filter.eq("companyInfoId", wf.getCompanyInfoId()));
/* 276 */     filters.add(Filter.eq("objTypeId", wf.getObjType()));
/* 277 */     WfObjConfig wfObjConfig = (WfObjConfig)this.wfObjConfigBaseService.find(filters);
/* 278 */     String objClassName = wfObjConfig.getObjClass();
/* 279 */     WfBillBaseService<WfBillEntity> billService = 
/* 280 */       (WfBillBaseService<WfBillEntity>)SpringUtils.getBean(objClassName);
/* 281 */     billService.startBack(wf);
/*     */     
/* 283 */     WfProc currWfProc = (WfProc)this.wfProcBaseService.find(wf.getCurrProcId());
/* 284 */     this.wfProcBaseService.agree(currWfProc, wf, storeMember, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void start(Wf wf) {
/* 292 */     this.lockDataDao.lockWf(wf.getId().toString());
/*     */     
/* 294 */     StoreMember storeMember = this.storeMemberService.getCurrent();
/* 295 */     StoreMember starter = wf.getStarter();
/* 296 */     if (!starter.equals(storeMember)) {
/* 297 */       ExceptionUtil.throwServiceException("不是您创建的流程，无法启动", new Object[0]);
/*     */     }
/*     */ 
/*     */     
/* 301 */     List<WfProc> wfProcs = wf.getWfProcs();
/* 302 */     for (WfProc wfProc : wfProcs) {
/* 303 */       if (wfProc.getProcType().intValue() == 1) {
/* 304 */         WfProc wfProc2 = (WfProc)this.wfProcBaseService.find(wfProc.getId());
/* 305 */         wfProc2.setStat(Integer.valueOf(4));
/* 306 */         this.wfProcBaseService.update(wfProc2);
/*     */       } 
/* 308 */       if (wfProc.getProcType().intValue() == 3 && 
/* 309 */         wfProc.getWfProcusers().isEmpty()) {
/* 310 */         ExceptionUtil.throwServiceException("流程节点用户不能为空", new Object[0]);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 315 */     wf.setStat(Integer.valueOf(1));
/* 316 */     update(wf);
/* 317 */     updateWfToBill(wf, Integer.valueOf(0));
/*     */ 
/*     */     
/* 320 */     List<Filter> filters = new ArrayList<Filter>();
/* 321 */     filters.add(Filter.eq("companyInfoId", wf.getCompanyInfoId()));
/* 322 */     filters.add(Filter.eq("objTypeId", wf.getObjType()));
/* 323 */     WfObjConfig wfObjConfig = (WfObjConfig)this.wfObjConfigBaseService.find(filters);
/* 324 */     String objClassName = wfObjConfig.getObjClass();
/* 325 */     WfBillBaseService<WfBillEntity> billService = 
/* 326 */       (WfBillBaseService<WfBillEntity>)SpringUtils.getBean(objClassName);
/* 327 */     billService.startBack(wf);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void interrupt(Wf wf) {
/* 335 */     this.lockDataDao.lockWf(wf.getId().toString());
/*     */     
/* 337 */     StoreMember storeMember = this.storeMemberService.getCurrent();
/* 338 */     StoreMember starter = wf.getStarter();
/* 339 */     if (!starter.equals(storeMember)) {
/* 340 */       ExceptionUtil.throwServiceException("不是您创建的流程，无法中断", new Object[0]);
/*     */     }
/*     */ 
/*     */     
/* 344 */     wf.setStat(Integer.valueOf(4));
/* 345 */     update(wf);
/* 346 */     updateWfToBill(wf, Integer.valueOf(1));
/*     */ 
/*     */     
/* 349 */     List<Filter> filters = new ArrayList<Filter>();
/* 350 */     filters.add(Filter.eq("companyInfoId", wf.getCompanyInfoId()));
/* 351 */     filters.add(Filter.eq("objTypeId", wf.getObjType()));
/* 352 */     WfObjConfig wfObjConfig = (WfObjConfig)this.wfObjConfigBaseService.find(filters);
/* 353 */     String objClassName = wfObjConfig.getObjClass();
/* 354 */     WfBillBaseService<WfBillEntity> billService = 
/* 355 */       (WfBillBaseService<WfBillEntity>)SpringUtils.getBean(objClassName);
/* 356 */     billService.interruptBack(wf);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void updateWfToBill(Wf wf, Integer flag) {
/* 364 */     Long objtypeid = wf.getObjType();
/* 365 */     Long objid = wf.getObjId();
/* 366 */     Long companyInfoId = wf.getCompanyInfoId();
/*     */     
/* 368 */     List<Filter> filters = new ArrayList<Filter>();
/* 369 */     filters.add(Filter.eq("companyInfoId", companyInfoId));
/* 370 */     filters.add(Filter.eq("objTypeId", objtypeid));
/* 371 */     WfObjConfig wfObjConfig = (WfObjConfig)this.wfObjConfigBaseService.find(filters);
/* 372 */     String objClassName = wfObjConfig.getObjClass();
/* 373 */     BaseService<WfBillEntity> billService = (BaseService<WfBillEntity>)SpringUtils.getBean(objClassName);
/* 374 */     WfBillEntity billEntity = (WfBillEntity)billService.find(objid);
/* 375 */     if (flag.intValue() == 0) {
/* 376 */       billEntity.setWfId(wf.getId());
/* 377 */       billEntity.setWfState(wf.getStat());
/*     */     } else {
/* 379 */       billEntity.setWfId(null);
/* 380 */       billEntity.setWfTempId(null);
/* 381 */       billEntity.setWfState(Integer.valueOf(0));
/*     */     } 
/* 383 */     billService.update(billEntity);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void saveAttach(Wf wf) {
/* 390 */     List<WfAttach> wfAttachs = wf.getWfAttachs();
/* 391 */     for (Iterator<WfAttach> iterator = wfAttachs.iterator(); iterator.hasNext(); ) {
/* 392 */       WfAttach wfAttach = iterator.next();
/* 393 */       if (wfAttach == null || wfAttach.getFileUrl() == null) {
/* 394 */         iterator.remove();
/*     */         continue;
/*     */       } 
/* 397 */       wfAttach.setWf(wf);
/* 398 */       if (wfAttach.getId() == null) {
/* 399 */         this.wfAttachBaseService.save(wfAttach); continue;
/*     */       } 
/* 401 */       this.wfAttachBaseService.update(wfAttach);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional(readOnly = true)
/*     */   public List<Integer> getAllWfStates() {
/* 409 */     return new ArrayList<Integer>() {
/*     */       
/*     */       };
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */