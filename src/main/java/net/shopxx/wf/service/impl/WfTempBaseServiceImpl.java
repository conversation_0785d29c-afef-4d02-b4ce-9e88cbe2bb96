/*     */ package net.shopxx.wf.service.impl;
/*     */ 
/*     */

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.wf.entity.*;
import net.shopxx.wf.service.WfProcTempBaseService;
import net.shopxx.wf.service.WfProcuserTempBaseService;
import net.shopxx.wf.service.WfTempBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Service("wfTempBaseServiceImpl")
/*     */ public class WfTempBaseServiceImpl
/*     */   extends BaseServiceImpl<WfTemp>
/*     */   implements WfTempBaseService
/*     */ {
/*     */   @Resource(name = "wfProcTempBaseServiceImpl")
/*     */   private WfProcTempBaseService wfProcTempBaseService;
/*     */   @Resource(name = "wfProcuserTempBaseServiceImpl")
/*     */   private WfProcuserTempBaseService wfProcuserTempBaseService;
/*     */   
/*     */   @Transactional
/*     */   public void saveOrUpdate(WfTemp wfTemp, Long[] delProcTempId) {
/*  37 */     saveOrUpdateWfAndProc(wfTemp);
/*  38 */     if (delProcTempId != null && delProcTempId.length > 0) {
/*  39 */       this.wfProcTempBaseService.delete(delProcTempId);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Transactional
/*     */   public void saveOrUpdateWfAndProc(WfTemp wfTemp) {
/*  48 */     for (Iterator<WfProcTemp> iterator = wfTemp.getWfProcTemps().iterator(); iterator.hasNext(); ) {
/*  49 */       WfProcTemp wfProcTemp = iterator.next();
/*  50 */       if (wfProcTemp == null || wfProcTemp.getProcTempName() == null) {
/*  51 */         iterator.remove();
/*     */       }
/*     */     } 
/*  54 */     if (wfTemp.getId() != null) {
/*  55 */       update(wfTemp, new String[] { "createDate", "companyInfoId" });
/*     */     } else {
/*     */       
/*  58 */       wfTemp.setActived(wfTemp.getActived());
/*  59 */       save(wfTemp);
/*     */     } 
/*     */     
/*  62 */     List<WfProcTemp> wfProcTemps = wfTemp.getWfProcTemps();
/*     */ 
/*     */     
/*  65 */     WfProcTemp preProc = null;
/*  66 */     for (WfProcTemp wfProcTemp : wfProcTemps) {
/*  67 */       boolean isUpdateProc = false;
/*     */       
/*  69 */       if (wfProcTemp.getId() != null) {
/*  70 */         isUpdateProc = true;
/*     */       }
/*     */       
/*  73 */       wfProcTemp.setPreProc(preProc);
/*     */       
/*  75 */       wfProcTemp.setWfTemp(wfTemp);
/*     */       
/*  77 */       List<WfProcuserTemp> wfProcuserTemps = wfProcTemp.getWfProcuserTemps();
/*  78 */       for (Iterator<WfProcuserTemp> iterator1 = wfProcuserTemps.iterator(); iterator1.hasNext(); ) {
/*  79 */         WfProcuserTemp wfProcuserTemp = iterator1.next();
/*  80 */         if (wfProcuserTemp == null || 
/*  81 */           wfProcuserTemp.getStoreMember() == null) {
/*  82 */           iterator1.remove();
/*     */           continue;
/*     */         } 
/*  85 */         wfProcuserTemp.setWfTempId(wfTemp.getId());
/*  86 */         wfProcuserTemp.setWfProcTemp(wfProcTemp);
/*     */       } 
/*     */       
/*  89 */       if (isUpdateProc) {
/*  90 */         setWfprocDefVal(wfProcTemp);
/*  91 */         this.wfProcTempBaseService.update(wfProcTemp, new String[] {
/*  92 */               "companyInfoId", 
/*  93 */               "specialHandleCode"
/*     */             });
/*     */       } else {
/*  96 */         setWfprocDefVal(wfProcTemp);
/*  97 */         this.wfProcTempBaseService.save(wfProcTemp);
/*     */       } 
/*     */ 
/*     */       
/* 101 */       if (preProc != null) {
/* 102 */         getDaoCenter().getObjDao().detach(preProc);
/* 103 */         preProc.setAfterProc(wfProcTemp);
/*     */         
/* 105 */         setWfprocDefVal(preProc);
/* 106 */         this.wfProcTempBaseService.update(preProc, new String[] { "wfProcuserTemps" });
/*     */       } 
/* 108 */       preProc = wfProcTemp;
/*     */ 
/*     */       
/* 111 */       List<WfStoreTemp> fWfStoreTemps = wfProcTemp.getWfStoreTemps();
/* 112 */       for (Iterator<WfStoreTemp> iterator2 = fWfStoreTemps.iterator(); iterator2.hasNext(); ) {
/* 113 */         WfStoreTemp fWfStoreTemp = iterator2.next();
/* 114 */         if (fWfStoreTemp == null || 
/* 115 */           fWfStoreTemp.getStoreMember() == null) {
/* 116 */           iterator2.remove();
/*     */           continue;
/*     */         } 
/* 119 */         fWfStoreTemp.setWfTempId(wfTemp.getId());
/* 120 */         fWfStoreTemp.setWfProcTemp(wfProcTemp);
/*     */       } 
/*     */ 
/*     */       
/* 124 */       List<WfPostTemp> wfPostTemps = wfProcTemp.getWfPostTemps();
/* 125 */       for (Iterator<WfPostTemp> iterator3 = wfPostTemps.iterator(); iterator3.hasNext(); ) {
/* 126 */         WfPostTemp wfPostTemp = iterator3.next();
/* 127 */         if (wfPostTemp == null || wfPostTemp.getPost() == null) {
/* 128 */           iterator3.remove();
/*     */           continue;
/*     */         } 
/* 131 */         wfPostTemp.setWfTempId(wfTemp.getId());
/* 132 */         wfPostTemp.setWfProcTemp(wfProcTemp);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void setWfprocDefVal(WfProcTemp wfProcTemp) {
/* 139 */     if (wfProcTemp.getIsSkip() == null) wfProcTemp.setIsSkip(Boolean.valueOf(false)); 
/* 140 */     if (wfProcTemp.getAllPerson() == null) wfProcTemp.setAllPerson(Boolean.valueOf(false)); 
/* 141 */     if (wfProcTemp.getIsSendSms() == null) wfProcTemp.setIsSendSms(Boolean.valueOf(false)); 
/* 142 */     if (wfProcTemp.getMinPerson() == null) wfProcTemp.setMinPerson(Integer.valueOf(0)); 
/*     */   }
/*     */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfTempBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */