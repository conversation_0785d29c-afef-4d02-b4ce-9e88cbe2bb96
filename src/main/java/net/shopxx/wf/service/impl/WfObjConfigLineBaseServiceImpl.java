/*    */ package net.shopxx.wf.service.impl;
/*    */ 
/*    */

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.wf.dao.WfObjConfigLineBaseDao;
import net.shopxx.wf.entity.WfObjConfigLine;
import net.shopxx.wf.service.WfObjConfigLineBaseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("wfObjConfigLineBaseServiceImpl")
/*    */ public class WfObjConfigLineBaseServiceImpl
/*    */   extends BaseServiceImpl<WfObjConfigLine>
/*    */   implements WfObjConfigLineBaseService
/*    */ {
/*    */   @Resource(name = "wfObjConfigLineBaseDao")
/*    */   private WfObjConfigLineBaseDao wfObjConfigLineBaseDao;
/*    */   
/*    */   public List<Map<String, Object>> getConfig(Long obj_type_id, Boolean isFlow) {
/* 26 */     return this.wfObjConfigLineBaseDao.getConfig(obj_type_id, isFlow);
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfObjConfigLineBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */