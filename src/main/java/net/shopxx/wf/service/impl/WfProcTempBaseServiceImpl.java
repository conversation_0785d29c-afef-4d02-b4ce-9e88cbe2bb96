/*    */ package net.shopxx.wf.service.impl;
/*    */ 
/*    */

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.wf.dao.WfProcTempBaseDao;
import net.shopxx.wf.entity.WfProcTemp;
import net.shopxx.wf.service.WfProcTempBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("wfProcTempBaseServiceImpl")
/*    */ public class WfProcTempBaseServiceImpl
/*    */   extends BaseServiceImpl<WfProcTemp>
/*    */   implements WfProcTempBaseService
/*    */ {
/*    */   @Resource(name = "wfProcTempBaseDao")
/*    */   private WfProcTempBaseDao wfProcTempBaseDao;
/*    */   
/*    */   @Transactional(readOnly = true)
/*    */   public List<Map<String, Object>> findListByWfTemp(Long wfTempId) {
/* 29 */     return this.wfProcTempBaseDao.findListByWfTemp(wfTempId);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   @Transactional(readOnly = true)
/*    */   public List<Map<String, Object>> findListByWfStoreTemp(String username, String mobile, String name, Long wfprocTempId, Long saleOrgId) {
/* 36 */     return this.wfProcTempBaseDao.findListByWfStoreTemp(username, 
/* 37 */         mobile, 
/* 38 */         name, 
/* 39 */         wfprocTempId, 
/* 40 */         saleOrgId);
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfProcTempBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */