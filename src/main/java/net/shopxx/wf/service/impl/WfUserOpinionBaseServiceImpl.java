/*    */ package net.shopxx.wf.service.impl;
/*    */ 
/*    */

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.wf.dao.WfUserOpinionBaseDao;
import net.shopxx.wf.entity.WfUserOpinion;
import net.shopxx.wf.service.WfUserOpinionBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("wfUserOpinionBaseServiceImpl")
/*    */ public class WfUserOpinionBaseServiceImpl
/*    */   extends BaseServiceImpl<WfUserOpinion>
/*    */   implements WfUserOpinionBaseService
/*    */ {
/*    */   @Resource(name = "wfUserOpinionBaseDao")
/*    */   private WfUserOpinionBaseDao wfUserOpinionBaseDao;
/*    */   
/*    */   @Transactional
/*    */   public List<Map<String, Object>> findListByWf(Long wfid, Long procid) {
/* 29 */     return this.wfUserOpinionBaseDao.findListByWf(wfid, procid);
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfUserOpinionBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */