/*    */ package net.shopxx.wf.service.impl;
/*    */ 
/*    */

import net.shopxx.base.core.service.impl.BaseServiceImpl;
import net.shopxx.wf.dao.WfProcuserTempBaseDao;
import net.shopxx.wf.entity.WfProcuserTemp;
import net.shopxx.wf.service.WfProcuserTempBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("wfProcuserTempBaseServiceImpl")
/*    */ public class WfProcuserTempBaseServiceImpl
/*    */   extends BaseServiceImpl<WfProcuserTemp>
/*    */   implements WfProcuserTempBaseService
/*    */ {
/*    */   @Resource(name = "wfProcuserTempBaseDao")
/*    */   private WfProcuserTempBaseDao wfProcuserTempBaseDao;
/*    */   
/*    */   @Transactional(readOnly = true)
/*    */   public List<Map<String, Object>> findListByProcTemp(String procTempIds) {
/* 29 */     return this.wfProcuserTempBaseDao.findListByProcTemp(procTempIds);
/*    */   }
/*    */ }


/* Location:              D:\wf\!\net\shopxx\wf\service\impl\WfProcuserTempBaseServiceImpl.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */