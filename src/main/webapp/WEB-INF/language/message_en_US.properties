﻿##通用
999=\u8425\u9500\u5E73\u53F0
1000=success
1001=add
1002=delete
1003=refresh
1004=search
1005=edit
1006=view
1007=operation
1008=display per page
1009=excel import
1010=export
1011=creation date
1012=sort
1013=save
1014=back
1015=cancle
1016=start date
1017=end date
1018=total {0} pages
1019=,to {0} pages
1020=please choose
1021=yes
1022=no
1023=chose
1024=check

2001=E.C
2002=Management System

##登录提示
3001=\u8BF7\u8F93\u5165\u7528\u6237\u540D
3002=\u8BF7\u8F93\u5165\u5BC6\u7801
3003=\u8BE5\u7528\u6237\u4E0D\u5B58\u5728
3004=\u8BF7\u8F93\u5165\u4F01\u4E1A\u7B80\u79F0\u6216\u5168\u79F0
3005=\u6839\u636E\u8F93\u5165\u7684\u4F01\u4E1A\u7B80\u79F0\u6216\u5168\u79F0\u627E\u4E0D\u5230\u4F01\u4E1A
3006=\u8BE5\u8D26\u53F7\u5DF2\u88AB\u7981\u7528
3007=\u5BC6\u7801\u9519\u8BEF
3008=\u503C\u4E0D\u80FD\u4E3A\u7A7A

##---------------------营销管理17--------------------------##
17000000=marketing management

##促销管理
17010001=promotion management
17010002=name
17010003=title
17010004=start date
17010005=end time
17010006=regional activities
17010007=headquarters activities

##优惠券管理
17020001=coupon management
17020002=name
17020003=prefix
17020004=start date of use
17020005=end of use date
17020006=discount amount
17020007=enable
17020008=type
17020009=issue method

##团购管理
17030001=group buying management
17030002=group name
17030003=group type
17030004=planned start time
17030005=planned end time
17030006=activity start time
17030007=activity end time

##活动管理
17040001=activity management
17040002=name
17040003=type
17040004=description
17040005=start time
17040006=end time
17040007=enable

17050001=promotional type setting
##-----------统计管理18-----------##
18000000=statistics management

##访问统计
18010001=access statistics

##ͳ统计设置
18020001=statistics setting

##销售统计
18030001=sales statistics 

##销售排行
18040001=sales ranking
18040002=ranking
18040003=commodity code
18040004=commodity name
18040005=sales volume
18040006=sales amount

##消费排行
18050001=comsuption ranking
18050002=ranking
18050003=user
18050004=E-mail
18050005=consuption amount

##Ԥ预存款
18060001=pre deposit
18060002=type
18060003=income amount
18060004=paid ammount
18060005=current balance
18060006=operator
18060007=membership
18060008=order
18060009=receipt
18060010=remarks
18060011=show profit only

##-----------系统管理19-----------##
19000000=system management

##系统设置
19010001=system setting
19010002=site name
19010003=wechat site name
19010004=website
19010005=logo
19010006=QR code
19010007=default logo generated by QR code
19010008=online QQ customer service
19010009=hot search
19010010=address
19010011=contact telephone
19010012=post code
19010013=E-mail
19010014=record number
19010015=site open
19010016=site close massage
19010017=basics configure
19010018=display configure
19010019=registration and security
19010020=email configure
19010021=other configure

##地区管理
19020001=region management
19020002=top region
19020003=parent region
19020004=not child region

##支付方式
19030001=payment method
19030002=name
19030003=method
19030004=introduction
19030005=Update failed. Please login again
19030006=Failed to save. Please log in again
19030007=Deleting all is not allowed
19030008=time-out
19030009=support delivery mode
19030010=content
19030011=\u5355\u4F4D\uFF1A\u5206\u949F\uFF0C\u7559\u7A7A\u8868\u793A\u6C38\u4E45\u6709\u6548

##配送方式
19040001=delivery method
19040002=name
19040003=Basic weight price
19040004=continued weight price
19040005=default logistics company
19040006=\u9996\u91CD\u91CF
19040007=\u7EED\u91CD\u91CF
19040008=\u56FE\u6807
19040009=\u6392\u5E8F
19040010=\u4ECB\u7ECD
19040011=\u8D77\u6B65\u8DDD\u79BB
19040012=\u8D85\u8FC7\u8D77\u6B65\u8DDD\u79BB
19040013=\u8D77\u6B65\u8D39\u7528
19040014=\u8D85\u8FC7\u8D77\u6B65\u8DDD\u79BB\u8D39\u7528
19040015=\u662F\u5426\u514D\u8FD0\u8D39

##物流公司
19050001=logistics
19050002=name
19050003=site
19050004=official telephone

##物流地区费用
19060001=logistics regional cost
19060002=warehouse
19060003=region
19060004=basic price

##物流折扣
19070001=logistics discount
19070002=warehouse
19070003=basic cube
19070004=continued cube
19070005=discount

##支付插件
19080001=payment plug-in
19080002=name
19080003=enable
19080004=install
19080005=uninstall
19080006=configure

##存储插件
19090001=save plug-in
19090002=name
19090003=version
19090004=author
19090005=enable
19090006=install
19090007=uninstall
19090008=configure

##日志管理
19100001=log management
19100002=operator
19100003=ip
19100004=content
19100005=empty

##系统参数
19110001=system parameter
19110002=parameter code
19110003=parameter name
19110004=parameter value
19110005=remarks

##-----------库存管理-----------##
##
12001=inventory management
12002=inventory checking
12003=warehouse code
12004=comodity code
12005=comodity name
12006=Physical inventory
12007=available inventory
12008=Application for warehouse receipt
12009=warehouse receipt Number
12010=warehouse code
12011=warehouse received total
12012=status
12013=remarks
12014=creation date
12015=Audit of warehouse receipt
12016=Outbound order
12017=odo number
12018=Outbound total
12019=allocation note
12020=allocation note number
12021=out warehouse code
12022=turn in warehouse code
12023=allocation total
12024=inventory log
12025=commodity
12026=warehouse  
12027=Physical inventory
12028=available inventory
12029=content

##app首页
12030=app home page
12031=promotion area
12032=promotion name
12033=click type
12034=sort
12035=enterprise
12036=operation
12037=commodity area
12038=title
12039=name
12040=description
12041=cover map
12042=NO.1 commodity code
12043=name of No.1 commodity
12044=NO.2 commodity code
12045=name of No.2 commodity
12046=NO.3 commodity code
12047=name of No.3 commodity
12048=ad area
12049=ad position 
12050=click type
12051=type
12052=start date
12053=end date
12054=icon edit area
12055=display on home page or not
12056=icon of home page display
12057=enable


##产品管理
12058=comodity management
12059=commodity
12060=code
12061=classification of commodity
12062=selling price
12063=on shelves 
12064=organized commodity
12065=oganization
12066=store commodity
12067=membership level
12068=membership price
12069=store
12070=enterprise
12071=classification of commodity
12072=display on home page or not
12073=icon of display on home page
12074=Commodity parameter
12075=bound sorting
12076=parameter
12077=Commodity specification
12078=Specification value
12079=Commodity specification
12080=icon
12081=basic information
12082=commodity classification
12083=subtitle
12084=manufacturer number
12085=international barcode
12086=place of origin
12087=delivery method
12088=cost
12089=market price
12090=display picture
12091=display big picture
12092=unit
12093=weight
12094=cubic
12095=gift points
12096=setting
12097=exchange price
12098=exchange points
12099=seach key word
12100=page key word
12101=page description
12102=enable membership price
12103=commodity introduction
12104=commodity pictures
12105=add pictures
12106=document
12107=scan
12108=sdocument not selected
12109=check
12110=commodity description
12111=chose the desciption
12112=color
12113=model
12114=commodity parameter
12115=category parent
12116=category pictures
12117=app home page icon
12118=brand screen
12119=page title
12120=page key word
12121=page description
12122=click type
12123=display on app home page
12124=additional specification value
12125=sepecification value name
12126=specification value picture
12127=description value sort
12128=warehouse name
120001=classification does not exist
120002=classified existence subcategory,deleting is not allowed
120003=goods under classification,deleting is not allowed
120004=parameter[{0}]existing associated commodity coding[{1}]
120005=the picture type 'logo' cannot be empty

##产品
12201=\u4EA7\u54C1\u89C4\u683C\u8BBE\u7F6E\u6709\u8BEF
12202=\u4EA7\u54C1\u4ECB\u7ECD\u5185\u5BB9\u8FC7\u591A

##产品会员价
12301=member rank
12302=product name
12303=product sn
12304=member rank price
12305=vonder code
12306=model
12307=product member rank price
12308=add product member rank price
12309=edit product member rank price

##产品选配项
120010=please select product category
120011=please select the configuration item
120012=please select options
120013=options are not optional under selected configuration items


12129=out warehouse
12130=turn in warehouse
12131=out warehouse name
12132=turn in warehouse name
12133=basic information
12134=commodity introduction
12135=commodity pictures
12136=commodity parameter
12137=commodity attribute
12138=commodity sepecification

12140=theme color
12141=partners apply for pictures
12142=partner channel pictures
12143=newspaper installation picture
12144=top categories

##使用说明
12319=instruction manual
##选择文件
12320=select file
##维护配送方式
12321=maintenance delivery mode
##商品价格导入
12322=mommodity price introduction
12401=\u6B64\u8BA2\u5355\u5DF2\u505A\u5165\u5E93,\u4E0D\u53EF\u91CD\u590D\u5165\u5E93\!
12402=\u4EA7\u54C1\u7F16\u53F7\u4E3A[{0}]\u7684\u4EA7\u54C1\u4E0D\u5B58\u5728\!
12403=\u64CD\u4F5C\u5931\u8D25\uFF0C\u5165\u5E93\u5355\u4E0D\u5B58\u5728\uFF01
12404=\u8BE5\u5165\u5E93\u5355[{0}]\u5DF2\u5BA1\u6838\uFF0C\u8BF7\u52FF\u91CD\u590D\u64CD\u4F5C\uFF01
12405=\u4EA7\u54C1\u4E0D\u5B58\u5728\uFF01
12406=\u5C55\u793A\u56FE
##开始查询
12323=start query
##展开\收起
12324=show/close
##初始化
12325=initialization
##编辑活动
12326=editing activities
##编辑专场
12327=editing special
##批量操作
12328=batch operation
##上架
12329=shelves
##下架
12330=ban
##更改商品分类
12331=change goods classification
##二维码
12332=QR code

12333=count the inventory

##品牌设置
12350=name
12351=website
12352=sort
12353=operation

##入库出库
12400=\u4ED3\u5E93\u4E0D\u5B58\u5728\!

##调拨单
12450=\u8F6C\u51FA\u4ED3\u5E93\u4E0D\u5B58\u5728\!
12451=\u8F6C\u5165\u4ED3\u5E93\u4E0D\u5B58\u5728\!
12452=\u8F6C\u51FA\u4ED3\u5E93\u4E2D\u4E0D\u5B58\u5728\u4EA7\u54C1\u7F16\u7801\u4E3A[{0}]\u7684\u4EA7\u54C1\!
12453=\u5546\u54C1\u7F16\u7801\u4E3A[{0}]\u7684\u8C03\u62E8\u6570\u91CF({1})\u4E0D\u80FD\u5927\u4E8E\u8F6C\u51FA\u4ED3\u5E93\u4E2D\u5B58\u5728\u7684\u6570\u91CF({2})
12454=\u64CD\u4F5C\u5931\u8D25\uFF0C\u8C03\u62E8\u5355\u4E0D\u5B58\u5728\uFF01
12455=\u8BE5\u8C03\u62E8\u5355\u5DF2\u5BA1\u6838\uFF0C\u8BF7\u52FF\u91CD\u590D\u64CD\u4F5C\uFF01

##-----------订单管理-----------##
15001=order management
15002=order-edit
15003=order information
15004=confirmation
15005=cancel order
15006=order number
15007=order status
15008=delivery status
15009=order amount
15010=commodity weight
15011=promotion
15012=promotion discount
15013=adjusted amount
15014=freight
15015=payment method
15016=consignee
15017=address
15018=telephone
15019=store owned
15020=partner income
15021=creation date
15022=payment status
15023=user name
15024=paid amount
15025=commodity quantity
15026=use coupon
15027=coupon discount
15028=gift points
15029=payment commission
15030=delivery method
15031=region
15032=post code
15033=postscript
15034=refund status
15035=payer
15036=delivery
15037=commodity information
15038=commodity code
15039=commodity name
15040=commodity price
15041=quantity
15042=delivered quantity
15043=returned quantity
15044=subtotal
15045=payment information
15046=code
15047=mode
15048=payment method
15049=payment amount
15050=status
15051=date of payment
15052=batch
15053=payment bill information
15054=delivery information
15055=delivery method
15056=logistics
15057=delivery bill number 
15058=refund information
15059=refund amount
15060=return information
15061=consignor
15062=order log
15063=type
15064=operator
15065=content
15066=payment record-edit
15067=receiving bank
15068=menbership
15069=collection account
15070=order
15071=remarks
15072=application for refund and return-edit
15073=order information
15074=invoice header
15075=tax
15076=order details
15077=name
15078=price
15079=quantity
15080=reason of reufnd
15081=refund amount applied
15082=reason for rejection
15083=print
15084=shipping status
15085=order time
15086=payment situation
15087=expired state
15088=operate
15089=put away
15090=open up
15091=export to Excel
15092=order
15093=retail order
15094=purchase order
15095=payment record
15096=cash withdrawal application
15097=refund and return application
15098=delivery center management
15099=delivery bill module
15100=shop
15101=bank card
15102=name
15103=openning bank
15104=applicant
15105=application amount
15106=overrule
15107=money
15108=owned stores
15109=owned clerk
15110=refund returns reason
15111=contact person
15112=area name
15113=print management
15114=financial refund
15115=supplier order

##购物车
15120=\u5546\u54C1\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0
15121=\u8BE5\u5546\u54C1\u4E0D\u5B58\u5728
15122=\u8BE5\u5546\u54C1\u5DF2\u4E0B\u67B6
15123=\u8BE5\u5546\u54C1\u4E3A\u975E\u5356\u54C1
15124=\u5546\u54C1\u6570\u91CF\u4E0D\u5141\u8BB8\u8D85\u8FC7{0}
15125=\u8BE5\u8D2D\u7269\u8F66\u9879\u5DF2\u4E0D\u5B58\u5728
15126=\u8D2D\u7269\u8F66\u5DF2\u6E05\u7A7A

##-----------用户管理-----------##
16001=user management
16002=membership management-check
16003=basic info
16004=personal data
16005=user name
16006=name
16007=level
16008=sex
16009=status
16010=date of birth
16011=points
16012=region
16013=balance
16014=address
16015=creation date
16016=mobile phone
16017=last login date
16018=store info
16019=oganization
16020=store
16021=fund detail
16022=type
16023=income ammount
16024=ammount paid
16025=current balance
16026=operator
16027=membership
16028=order
16029=receipt
16030=remarks
16031=membership level-edit
16032=name
16033=discount proportion
16034=level
16035=setting
16036=default
16037=adminstrator-edit
16038=basic info
16039=password
16040=confirm password
16041=E-mail
16042=role
16043=oganization administrator or not
16044=enterprise
16045=enable
16046=department
16047=partner type-edit
16048=active status
16049=affiliated company
16050=enabled
16051=adminstrator roles-edit
16052=description
16053=pre recharge application-check
16054=application info
16055=attachment
16056=user
16057=recharge amount
16058=operator
16059=application time
16060=auditor
16061=time of audit
16062=remarks of audit
16063=store owner-check
16064=partner mode
16065=senior partner
16066=sales-check
16067=policy pool
16068=cost pool
16069=membership registration-edit
16070=sort
16071=required fill in or not
16072=Member management
16073=Store owner
16074=Sales
16075=Membership level
16076=Membership registration
16077=Partner application
16078=Partner type
16079=Menu roles
16080=Administrator
16081=Administrator roles
16082=Balance application
16083=Balance recharge audit
16084=Customer partner relationship
16085=Article management
16086=Article classification management
16087=whether it is activated 
16088=whether it is a partner
16089=type of membership
16090=current points
16091=recommended phone number
16092=the IP of last login
16093=whether built-in
16094=approval Status
16095=client
16096=partner
16097=whether the customer is a partner
16098=article classification
16099=whether it is released
16100=store manager
16101=revenue management
16102=policy pool adjustment application
16103=policy pool adjustment audit
16104=policy pool details
16111=cannot set yourself up as a higher partner

##合伙人类型
16200=partner type does not exist
16201=categories associated with a partner cannot be deleted

##客户合伙人关系
16250=operation error, the user [{0}] does not exist 
16251=operation error, the user [{0}] is not a partner 

##会员等级
16300=you do not have permission to operate
16301=name cannot be empty
16302=this member class already exists
16303=this member class does not exis

##菜单角色
16350=the characters don't exist
16351=the built-in role is not allowed to be deleted
16352=deletion failed. User [{0}] exists under the role
16353=please select the characters you want to delete first

##余额充值申请
16400=operation error, under the enterprise does not exist the user, please check and then add
16401=peration error,customer does not exist
16402=the application has been examined, approved or rejected

##页面设置
10001=page setup
10002=Carousel figure area
10003=advertise area
10004=Commodity area
10005=Navigation management
10006=wechat shopping mall home page
10007=title
10008=Column name
10009=name
10010=Add content
10011=url
10012=Column description
10013=position
10014=Carousel figure
10015=Enable
10016=No. 1 commodity
10017=new window to open
10018=add a picture
10019=sort
10020=No. 2 commodity
10021=sort
10022=save
10023=enterprise
10024=No. 3 commodity
10025=operation
10026=picture ads
10027=operation
10028=No. 4 commodity
10029=commodity
10030=No. 5 commodity
10031=check home page
10032=No. 6 commodity
10033=click to edit picture ads, Recommended width: 580px
10034=status
10035=picture
10036=operation
10037=display mode:Multi split irregular display
10038=commodity pictures: display commodity name here
10039=Cannot be restored after the module is deleted! Confirm delete?
10040=display mode: big picture, small picture, detailed list
10041=module 1/2/3/4
10042=input name of selected commodity

##企业管理
11001=enterprise management
11002=Enterprise information
11003=warehouse information
11004=Organizational relationship
11005=store information
11006=warehous store
11007=Corporate name
11008=warehouse code
11009=name
11010=code
11011=warehouse code
11012=Organization type
11013=Activation code
11014=organization
11015=region
11016=organization
11017=operation
11018=role code
11019=status
11020=address
11021=region
11022=enable status
11023=address
11024=creation time
11025=enable status
11026=modification date
11027=creater
11028=Modifier
11029=remarks
11030=enterprise share configuration information

##合伙人申请
11031=recommender mobile

##企业信息
11050=\u4F01\u4E1A\u540D\u79F0
11051=\u4F01\u4E1A\u7B80\u79F0
11052=\u4F01\u4E1A\u7F16\u7801
11053=\u89C4\u5219\u7801
11054=\u670D\u52A1\u5668URL
11055=\u63A5\u53E3\u5BC6\u5319
11056=\u4E3B\u4F01\u4E1A
11057=\u652F\u4ED8\u8D85\u65F6\u65F6\u95F4
11058=\u5408\u4F19\u4EBA\u6A21\u5F0F
11059=\u5408\u4F19\u4EBA\u5BA1\u6838\u6A21\u5F0F
11060=\u542F\u52A8\u9875
11061=\u9009\u62E9\u56FE\u7247
11062=\u5907\u6CE8
11063=\u6D3B\u52A8\u72B6\u6001
11064=\u662F\u5426\u542F\u7528\u5355\u4E00\u4ED3
11065=\u5145\u503C\u662F\u5426\u77ED\u4FE1\u901A\u77E5
11066=\u8D2D\u4E70\u662F\u5426\u77ED\u4FE1\u901A\u77E5
11067=\u662F\u5426\u5141\u8BB8\u4F59\u989D\u652F\u4ED8
11068=\u662F\u5426\u5141\u8BB8\u4F7F\u7528\u6536\u76CA\u8FDB\u884C\u6D88
11069=\u662F\u5426\u5141\u8BB8\u4F7F\u7528\u6536\u76CA\u8FDB\u884C\u63D0\u73B0
11070=\u6FC0\u6D3B\u7801
11071=\u63D0\u73B0\u6700\u9AD8\u91D1\u989D
11072=\u4ED8\u6B3E\u5355\u524D\u7F00

##组织关系
11090=\u4E0A\u7EA7\u7EC4\u7EC7
11091=\u63A5\u53E3\u5BC6\u94A5
11092=\u8BBE\u7F6E
16105=\u7981\u7528
16106=\u9501\u5B9A
16107=\u6B63\u5E38
12407=\u5206\u7C7B
12408=\u914D\u7F6E\u9879
12409=\u53EF\u9009\u9879
12410=\u6587\u672C
12411=\u56FE\u7247

##---------------------业务代表17--------------------------##
17001=\u4E1A\u52A1\u4EE3\u8868
17002=\u9879\u76EE\u767B\u8BB0
17003=\u9700\u6C42\u5355
##价格申请
17004=\u4EF7\u683C\u7533\u8BF7
17005=\u72B6\u6001
17006=\u9700\u6C42\u5355\u5355\u53F7
17007=\u7533\u8BF7\u4EBA
17008=\u5907\u6CE8
17009=\u5BA1\u6838\u610F\u89C1
17010=\u64CD\u4F5C\u8005
17011=\u5BA1\u6838\u65F6\u95F4
17012=\u5DF2\u4FDD\u5B58
17013=\u5DF2\u63D0\u4EA4
17014=\u5DF2\u5BA1\u6838
17015=\u5DF2\u9A73\u56DE
17016=\u5DF2\u5173\u95ED
17017=\u4EF7\u683C\u7533\u8BF7\u7F16\u7801
17018=\u5DF2\u751F\u6210\u6B63\u5F0F\u8BA2\u5355
17019=\u5DF2\u53D1\u5E03
17020=\u5DF2\u5B8C\u6210
17021=\u5DF2\u53D6\u6D88
17022=\u7279\u4EF7\u7533\u8BF7\u4E0D\u5B58\u5728
17023=\u8BE5\u72B6\u6001\u4E0D\u80FD\u64CD\u4F5C
17024=\u7248\u672C\u53F7
17025=\u5546\u54C1\u7F16\u53F7
17026=\u5546\u54C1\u4E0B\u5355\u4EF7\u683C
17027=\u4EF7\u683C\u7533\u8BF7\u4FE1\u606F
17028=\u4EF7\u683C\u7533\u8BF7\u660E\u7EC6

##项目登记
17050=\u9879\u76EE\u5355\u53F7
17051=\u8054\u7CFB\u4EBA
17052=\u8054\u7CFB\u65B9\u5F0F
17053=\u5730\u5740
17054=\u5185\u5BB9
17055=\u4E1A\u52A1\u5458

##需求单
17100=\u62A5\u4EF7\u7533\u8BF7\u6B21\u6570
17101=\u603B\u91D1\u989D
17102=\u9700\u6C42
17103=\u4E1A\u52A1\u5458
17104=\u63D0\u4EA4\u65F6\u95F4