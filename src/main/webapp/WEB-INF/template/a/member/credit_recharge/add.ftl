<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>${message("授信申请 ")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
    <script src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <script type="text/javascript" src="/resources/js/utils.js"></script>
    <script type="text/javascript">
        function editPrice(t,e){
            extractNumber(t,2,false,e)
        }
        var  fileExistFlag = false ;
        $().ready(function() {
            var $inputForm = $("#inputForm");
            var $selectMember = $("#selectMember");
            var $deleteProduct = $("a.deleteProduct");
            var $addProduct = $("#addProduct");
            var itemIndex = 1;

            var $inputForm = $("#inputForm");
            var $submitButton = $("#submitButton");
            $("input[name='image']").single_upload({
                uploadSize:"source"
            });
            $("input[name='image2']").single_upload({
                uploadSize:"source"
            });
            $("input[name='image3']").single_upload({
                uploadSize:"source"
            });
            // 表单验证
            $inputForm.validate({
                rules: {
                    amount: {
                        required: true,
                        min: 0,
                        decimal: {
                            integer: 12,
                            fraction: ${setting.priceScale}
                        }
                    },
                    startDate: {
                        required: true
                    },
                    endDate: {
                        required: true
                    },
                    storeName: {
                        required: true
                    }
                } ,
                submitHandler:function(form){
                    return false;
                }
            });

            var cols = [
                { title:'${message("内容")}', name:'content' ,width:300,align:'center'},
                { title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
                { title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
            ];
            $('#table-full').mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });

            //查询售后单
            $("#openAftersale").click(function () {
                var storeId = $(".storeId").val();
                if (storeId == "") {
                    $.message_alert('请选择客户');
                    return;
                }
                $("#openAftersale").bindQueryBtn({
                    type: 'aftersale',
                    bindClick: false,
                    title: '${message("查询售后单")}',
                    url: '/aftersales/aftersale/select_aftersale.jhtml?multi=1&storeId=' + storeId,
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var row = rows[0];
                            $('.aftersaleId').val(row.id);
                            $('.aftersaleSn').val(row.sn);
                        }
                    }
                });
            });
           /* $(".aftersaleSn").click(function(){
                var aftersaleId = $(".aftersaleId").val();
                if(aftersaleId==""){
                    return;
                }
                var a = $("<a/>");
                a.attr("data-src", "/aftersales/aftersale/list_tb.jhtml?objid="+aftersaleId);
                a.attr("data-name", "售后申请");
                top.open_new_tab(a);
            });*/

            //查询客户
            $("#selectStore").click(function(){
                var sbuId = $(".sbuId").val();
                if(isNull(sbuId) == null){
                    $.message_alert('sbu不能为空');
                    return false;
                }
                //经营组织
                var organizationId = $("#organizationId option:selected").val();
                $("#selectStore").bindQueryBtn({
                    type:'store',
                    title:'${message("查询客户")}',
                    bindClick:false,
                    url:'/member/store/select_store.jhtml?type=distributor&isMember=1'+'&sbuId='+sbuId,
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $(".storeName").val(row.name);
                            $(".storeId").val(row.id);
                            $("#storeAlias").text(row.alias);
                            $("#outTradeNo").html(row.out_trade_no);
                            $(".saleOrgName").val(row.sale_org_name);
                            $(".saleOrgId").val(row.sale_org_id);
                            //可用余额
                            customerBalance(row.id,sbuId,organizationId);
                        }
                    }
                });
            });

            //经营组织
            $("#organizationId").live("change", function() {
                //经营组织
                var organizationId = $(this).val();
                //sbu
                var sbuId = $(".sbuId").val();
                //客户
                var storeId = $(".storeId").val();
                //可用余额
                customerBalance(storeId,sbuId,organizationId);
            })

            //查询机构
            $("#selectSaleOrg").bindQueryBtn({
                type:'saleOrg',
                title:'${message("查询机构")}',
                url:'/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1',
                callback:function(rows){
                    if(rows.length>0){
                        var row = rows[0];
                        $("input[name='saleOrgName']").attr("value",row.name);
                        $("input[name='saleOrgId']").attr("value",row.id);
                        $(".storeName").val('');
                        $(".storeId").val('');
                        $("#storeAlias").text('');
                        $("#outTradeNo").html('');
                        ajaxSubmit('',{
                            method:'post',
                            url:'/member/saleorg_credit/get_amount.jhtml?saleOrgId='+row.id,
                            async: false,
                            callback:function(resultMsg) {
                                var data = resultMsg.objx;
                                var totalAmount = data.total_amount;
                                var usedAmount = data.used_amount;
                                if(isNaN(totalAmount)){
                                    totalAmount = 0.00;
                                }
                                if(isNaN(usedAmount)){
                                    usedAmount = 0.00;
                                }
                                $("#totalAmount").text(currency(totalAmount,true));
                                $("#usedAmount").text(currency(usedAmount,true));
                            }
                        })
                    }
                }
            });


            //查询合同
            $("#selectContract").click(function(){
                $("#openStore").bindQueryBtn({
                    type:'store',
                    bindClick:false,
                    title:'${message("查询合同")}',
                    url:'/b2b/customerContract/select_contract.jhtml',
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $("input[name='contractId']").attr("value",row.id);
                            $("input[name='contractName']").attr("value",row.contract_name);
                            $("input[name='storeId']").attr("value",row.store_id);
                            $("input[name='storeName']").attr("value",row.store_name);
                            $("input[name='saleOrgId']").attr("value",row.sale_org_id);
                            $("input[name='saleOrgName']").attr("value",row.sale_org_name);
                            $("input[name='storeMemberId']").attr("value",row.store_member_id);
                            $("input[name='storeMemberName']").attr("value",row.store_member_name);
                            $("input[name='contacts']").attr("value",row.contacts);
                            $("input[name='phone']").attr("value",row.phone);
                            $("input[name='address']").attr("value",row.address);
                        }
                    }
                });
            })



            /**初始化附件*/
            var attachIdnex = 0;

            var cols = [
                { title:'${message("附件")}', name:'content',width:260,align:'center',renderer:function(val,item,rowIndex,obj){


                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['creditAttachs['+attachIdnex+'].url']=url;
                        hideValues['creditAttachs['+attachIdnex+'].suffix']=fileObj.suffix;
                        return createFileStr({
                            url : url,
                            fileName : fileObj.file_name,
                            name : fileObj.name,
                            suffix : fileObj.suffix,
                            time : '',
                            textName:'creditAttachs['+attachIdnex+'].name',
                            hideValues:hideValues
                        });
                    }},
                { title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
                        return '<div><textarea class="text file_memo" name="creditAttachs['+attachIdnex+'].memo" >'+val+'</textarea></div>';
                    }},
                { title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
                        attachIdnex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }}
            ];
            var $amGrid=$('#table-attach').mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addAttach = $("#addAttach");
            var attachIdnex = 0;
            var option1 = {
                dataType: "json",
                uploadToFileServer:true,
                uploadSize: "fileurl",
                callback : function(data){
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth()+1;
                    var day = date.getDate();
                    var time = year+'-'+month+'-'+day;
                    for(var i=0;i<data.length;i++){
                        var row = data[i].file_info;
                        $amGrid.addRow(row,null,1);
                    }

                }
            }

            $addAttach.file_upload(option1);
            var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
            });

        });

        function save(e){
            var url = '/member/credit_recharge/save.jhtml';
            var $form = $("#inputForm");
            if($form.valid()){
                var data = $form.serialize();
                var content = '您确定要保存吗？';
                $.message_confirm(content,function() {
                    Mask();
                    ajaxSubmit(e,{
                        url: url,
                        data:data,
                        method: "post",
                        failCallback:function(resultMsg){				// 访问地址失败，或发生异常没有正常返回
                            $.message_alert(resultMsg.rmid+"<br/>"+resultMsg.content);
                            UnMask();
                        },
                        callback:function(resultMsg){
                            location.href= '/member/credit_recharge/view/${code}.jhtml?flag=1&id='+resultMsg.objx;
                        }
                    })
                });
            }
        };
    </script>
</head>
<body>
<div class="pathh">
    &nbsp;${message("新增授信申请")}
</div>
<form id="inputForm" action="/member/credit_recharge/save.jhtml" method="post" type="ajax" validate-type="validate">
    <input type="hidden" name="rechargeType" value="[#if rechargeType==1]1[#else]0[/#if]" />
    <div class="tabContent">
        <table class="input input-edit">
            <tr>
                <th>
                    ${message("申请单号")}:
                </th>
                <td>
                </td>
                [#if rechargeType==1]
                    <th>
                        <span class="requiredField">*</span>${message("用户")}:
                    </th>
                    <td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeMemberId" class="text storeMemberId" btn-fun="clear"/>
					<input type="text" name="storeMemberName" class="text storeMemberName" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStoreMember">
					</span>
                    </td>
                [#else]
                    <th>
                        <span class="requiredField">*</span>${message("客户")}:
                    </th>
                    <td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeId" class="text storeId" btn-fun="clear" value="${store.id }"/>
					<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" value="${store.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore">
					</span>
                    </td>
                [/#if]
                <th>
                    <span class="requiredField">*</span>${message("申请额度")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
                        <input type="text"  class="t"  name="amount" value="" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
                    </div>
                </td>
                <th>
                    ${message("实际充值金额")}:
                </th>
                <td>
                    <span class="red">${currency(0, true)}</span>
                </td>
            </tr>
            <tr>
                <th>${message("机构")}:</th>
                <td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="[#if store??]${store.saleOrg.id}[#else]${saleOrg.id}[/#if]" />
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)"value="[#if store??]${store.saleOrg.name}[#else]${saleOrg.name}[/#if]" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
                </td>
                <th>${message("客户编码")}:</th>
                <td>
                    <span id="outTradeNo"></span>
                </td>
                <th>
                    ${message("流程状态")}:
                </th>
                <td>
                    <!--<b class="blue">${message("未启动")}</b>-->
                </td>
                <th>
                    <span class="requiredField">*</span>${message("开始日期")}:
                </th>
                <td>
                    <input id="startTime" name="startDate" class="text" value="${startDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("结束日期")}:
                </th>
                <td>
                    <input id="endTime" name="endDate" class="text" value="${endDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
                </td>
                <th>${message("审核状态")}:</th>
                <td>
                </td>
                <th>
                    ${message("创建人")}:
                </th>
                <td>
                </td>
                <th>
                    ${message("申请日期")}:
                </th>
                <td>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("经营组织")}:
                </th>
                <td>
                    <select name="organizationId" id="organizationId" class="text">
                        [#list organizationList as organization]
                            <option value="${organization.id}">${organization.name}</option>
                        [/#list]
                    </select>
                </td>
                <th>${message("充值状态")}:</th>
                <td></td>
                <th>${message("Sbu")}:</th>
                <td>
                    <input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbu.id}"/>
                    <span id="sbuName">${sbu.name}</span>
                </td>
                <th>${message("客户余额")}</th>
                <td>
                    <span class="red" id="balance"></span>
                </td>
            </tr>
            <tr>
                <th>${message("客户简称")}:</th>
                <td>
                    <span id="storeAlias">${store.alias}</span>
                </td>
                <th><span class="requiredField">*</span>${message("合同名称")}:</th>
                <td>
            		 <span class="search" style="position: relative">
	            		  <input  type="hidden" name="contractId" class="text contractId" value="" btn-fun="clear" />
	            		  <input type="text" name="contractName" class="text contractName" value=""maxlength="200" onkeyup="clearSelect(this)" readOnly /> 
	            		  <input type="button" class="iconSearch" value="" id="selectContract">
            		 </span>
                </td>
                <th class="aftersaleDS">${message("售后单据")}:</th>
                <td class="aftersaleDS">
                    <span class="search" style="position: relative">
                        [#--<input type="hidden" name="aftersale.id" class="text aftersaleId" value="${cr.aftersale.id}"
                               btn-fun="clear"/>--]
                        <input type="text" name="aftersaleSn" class="text aftersaleSn" value="${cr.aftersale.sn}"
                               maxlength="200" readOnly/>
                        <input type="button" class="iconSearch" value="" id="openAftersale">
                    </span>
                </td>
            </tr>
            <tr>
                <th>
                    ${message("备注")}:
                </th>
                <td colspan="7">
                    <textarea name="memo" class="text" id="memo"></textarea>
                </td>
            </tr>
            <tr class="s-tr"><td colspan="8"></td></tr>
            <tr class="border-L1">
                <th>${message("附件信息")}:</th>
                <td colspan="7"><a href="javascript:;" id="addAttach" class="button">添加附件</a></td>
            </tr>
            <tr class="border-L1">
                <td colspan="8">
                    <div  class="w_1135">
                        <table id="table-attach"></table>
                    </div>
                </td>
            </tr>
        </table>
        <table class="input input-edit" style="width:100%;margin-top:5px;">
            <tr class="border-L2">
                <th>${message("全链路信息")}:</th>
                <td colspan="7"></td>
            </tr>
            <tr class="border-L2">
                <td colspan="8">
                    <table id="table-full"></table>
                </td>
            </tr>
        </table>
    </div>
    <div class="fixed-top">
        <input type="button" id="submit_button" onclick="save(this)" class="button sureButton" value="${message("1013")}" />
        <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
    </div>
</form>
</body>
</html>