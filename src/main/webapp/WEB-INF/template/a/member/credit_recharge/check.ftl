<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<title>${message("客户授信申请查看")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css"/>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<style type="text/css">
     #qButton {
         width: 20px;
         height: 20px;
         position: absolute;
         top: 4px;
         left: 12px;
         background: url(/resources/images/button/ico-aa.png) no-repeat center;
     }

     #addquantity {
         padding: 0 21px;
         height: 28px;
         line-height: 28px;
         margin-right: 0;
     }

     #n-input {
         width: 74%;
         float: left;
         margin-right: 5px;
     }
</style>
    <script type="text/javascript">
        function editPrice(t, e) {
            extractNumber(t, 2, false, e)
        }

        $().ready(function () {
        	
            var $inputForm = $("#inputForm");
            var $submitButton = $("#submitButton");
			[#if flag==1]
				$("#wf_area").load("/act/wf/wf.jhtml?wfid=${cr.wfId}");
			[/#if]
            // 表单验证
            $inputForm.validate({
                rules: {
                    amount: {
                        required: true,
                        min: 0,
                        decimal: {
                            integer: 12,
                            fraction: ${setting.priceScale}
                        }
                    },
                    startDate: {
                        required: true
                    },
                    endDate: {
                        required: true
                    },
                    storeName: {
        				required: true
        			}
                }
            });

            //查询售后单
            $("#openAftersale").click(function () {
                var storeId = $(".storeId").val();
                if (storeId == "") {
                    $.message_alert('请选择客户');
                    return;
                }
                $("#openAftersale").bindQueryBtn({
                    type: 'aftersale',
                    bindClick: false,
                    title: '${message("查询售后单")}',
                    url: '/aftersales/aftersale/select_aftersale.jhtml?multi=1&storeId=' + storeId,
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var row = rows[0];
                            $('.aftersaleId').val(row.id);
                            $('.aftersaleSn').val(row.sn);
                        }
                    }
                });
            });
           /* $(".aftersaleSn").click(function(){
                var aftersaleId = $(".aftersaleId").val();
                if(aftersaleId==""){
                    return;
                }
                var a = $("<a/>");
                a.attr("data-src", "/aftersales/aftersale/list_tb.jhtml?objid="+aftersaleId);
                a.attr("data-name", "售后申请");
                top.open_new_tab(a);
            });*/

            //查询客户
            $("#selectStore").click(function () {
                var sbuId = $(".sbuId").val();
                if(isNull(sbuId) == null){
                	$.message_alert('sbu不能为空');
                    return false;
                }
                //经营组织
                var organizationId = $("#organizationId option:selected").val();
                $("#selectStore").bindQueryBtn({
                    type: 'store',
                    title: '${message("查询客户")}',
                    bindClick: false,
                    url: '/member/store/select_store.jhtml?type=distributor&isMember=1'+'&sbuId='+sbuId,
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var row = rows[0];
                            $(".storeName").val(row.name);
                            $(".storeId").val(row.id);
                            $("#storeAlias").text(row.alias);
                            $("#outTradeNo").html(row.out_trade_no);
                            $(".saleOrgName").val(row.sale_org_name);
                            $(".saleOrgId").val(row.sale_org_id);
                          	//可用余额
        					customerBalance(row.id,sbuId,organizationId);
                        }
                    }
                });
            });
			
          	//经营组织
        	$("#organizationId").live("change", function() {
        		//经营组织
        		var organizationId = $(this).val();
        		//sbu 
        	    var sbuId = $(".sbuId").val();
        		//客户
        	    var storeId = $(".storeId").val(); 
        	  	//可用余额
        	    customerBalance(storeId,sbuId,organizationId);
        	}) 
        	
        	
        	//可用余额
  			customerBalance('${cr.store.id}','${cr.sbu.id}','${cr.organization.id}');
            
            
            //查询用户
            $("#selectStoreMember").bindQueryBtn({
                type: 'storeMember',
                title: '${message("查询用户")}',
                url: '/member/store_member/select_store_member.jhtml',
                callback: function (rows) {
                    if (rows.length > 0) {
                        var row = rows[0];
                        $(".storeMemberName").val(row.username);
                        $(".storeMemberId").val(row.id);
                    }
                }
            });

            //查询机构
            $("#selectSaleOrg").bindQueryBtn({
                type: 'saleOrg',
                title: '${message("查询机构")}',
                url: '/basic/saleOrg/select_saleOrg.jhtml??isSellSaleOrg=1',
                callback:function(rows){
        			if(rows.length>0){
        				var row = rows[0];
        			    $("input[name='saleOrgName']").attr("value",row.name);
        			    $("input[name='saleOrgId']").attr("value",row.id);
        			    $(".storeName").val('');
                        $(".storeId").val('');
                        $("#storeAlias").text('');
                        $("#outTradeNo").html('');	
        			}
        		}		
            });
            
            //查询合同
			$("#selectContract").click(function(){
		    	$("#openStore").bindQueryBtn({
			        type:'store',
			        bindClick:false,
			        title:'${message("查询合同")}',
			        url:'/b2b/customerContract/select_contract.jhtml',
			        callback:function(rows){
			            if(rows.length>0){
			                var row = rows[0];
			                $("input[name='contracttId']").attr("value",row.id);
			                $("input[name='contractName']").attr("value",row.contract_name);
			                $("input[name='storeId']").attr("value",row.store_id);
			                $("input[name='storeName']").attr("value",row.store_name);
			                $("input[name='saleOrgId']").attr("value",row.sale_org_id);
			                $("input[name='saleOrgName']").attr("value",row.sale_org_name);
			                $("input[name='storeMemberId']").attr("value",row.store_member_id);
			                $("input[name='storeMemberName']").attr("value",row.store_member_name);
			                $("input[name='contacts']").attr("value",row.contacts);
			                $("input[name='phone']").attr("value",row.phone);
			                $("input[name='address']").attr("value",row.address);
			            }
			        }
			    });
		    })

            var orderFullLink_items = ${fullLink_json};
            var cols = [
                {title: '${message("内容")}', name: 'content', width: 300, align: 'center'},
                {title: '${message("操作人")}', name: 'operator_name', width: 100, align: 'center'},
                {title: '${message("创建日期")}', name: 'create_date', width: 150, align: 'center'}
            ];
            $('#table-full').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                items: orderFullLink_items,
                checkCol: false,
                autoLoad: true
            });

            $("form").bindAttribute({
                isConfirm: true,
                callback: function (resultMsg) {
                    $.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
                        location.reload(true);
                    })
                }
            });

            /**初始化附件*/
            var twContractAttach_items = ${twContractAttach_json};
            var attachIdnex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    width: 260,
                    align: 'center',
                    renderer: function (val, item, rowIndex, obj) {
                        if (obj == undefined) {
                            var url = item.url;
                            var fileObj = getfileObj(item.file_name, item.name, item.suffix);

                            /**设置隐藏值*/
                            var hideValues = {};
                            hideValues['creditAttachs[' + attachIdnex + '].id'] = item.id;
                            hideValues['creditAttachs[' + attachIdnex + '].url'] = url;
                            hideValues['creditAttachs[' + attachIdnex + '].suffix'] = fileObj.suffix;

                            return createFileStr({
                                url: url,
                                fileName: fileObj.file_name,
                                name: fileObj.name,
                                suffix: fileObj.suffix,
                                time: item.create_date,
                                textName: 'creditAttachs[' + attachIdnex + '].name',
                                hideValues: hideValues
                            });
                        } else {
                            var url = item.url;
                            var fileObj = getfileObj(item.name);
                            /**设置隐藏值*/
                            var hideValues = {};
                            hideValues['creditAttachs[' + attachIdnex + '].url'] = url;
                            hideValues['creditAttachs[' + attachIdnex + '].suffix'] = fileObj.suffix;

                            return createFileStr({
                                url: url,
                                fileName: fileObj.file_name,
                                name: fileObj.name,
                                suffix: fileObj.suffix,
                                time: '',
                                textName: 'creditAttachs[' + attachIdnex + '].name',
                                hideValues: hideValues
                            });

                        }


                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    width: 590,
                    align: 'center',
                    renderer: function (val, item, rowIndex) {
                        return '<div><textarea class="text file_memo" name="creditAttachs[' + attachIdnex + '].memo" >' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', width: 5, align: 'center', renderer: function (val, item, rowIndex) {
                        attachIdnex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $amGrid = $('#table-attach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                items: twContractAttach_items,
                checkCol: false,
                autoLoad: true
            });

            var $addAttach = $("#addAttach");
            var attachIdnex = 0;
            var option1 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $amGrid.addRow(row, null, 1);
                    }

                }
            }
            $addAttach.file_upload(option1);
            var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function () {
                var $this = $(this);
                $this.closest("tr").remove();
            });

            var amount = ${cr.actualAmount};
            if (amount.toString().indexOf("-") != -1) {
                var str = fmoney(amount.toString().substring(1, amount.length));
                amount = "-" + str;
            } else {
                amount = fmoney(amount);
            }
            $('.number').text("￥" + amount);

        });

        function check_wf(e) {
            var $this = $(e);
            var $form = $("#inputForm");
            var sbuId = $(".sbuId").val();
          	//var modelId = model($("#sbuId").val(),"测试");
            var modelId = model($("#sbuId").val(),"正式");
            if($form.valid()){
                $.message_confirm("您确定要审批流程吗？",function(){
                    Mask();
                    //var objTypeId = 100020;//大自然开发
                    //var modelId = 777501;//大自然开发
                    //var objTypeId = 100026;//大自然测试
                    var objTypeId = 100026;//大自然正式
                    var url="/member/credit_recharge/start_check_wf.jhtml?id=${cr.id}&modelId="+modelId+"&objTypeId="+objTypeId;
                    var flag = 1;
                    var data = $form.serialize()+"&flag=" + flag;
                    ajaxSubmit(e,{
                        method: 'post',
                        url: url,
                        async: true,
                        data:data,
                        failCallback:function(resultMsg){				
                        	// 访问地址失败，或发生异常没有正常返回
                            messageAlert(resultMsg);
                            UnMask();
                        },
                        callback: function(resultMsg) {
                            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                location.reload(true);
                            })
                        }
                    });
                });
            }
        }

        //保存
        function save(e){
            var $form = $("#inputForm");
            if($form.valid()){
                var url = '/member/credit_recharge/update.jhtml';
                var data = $form.serialize();
                var content = '您确定要保存吗？';
                $.message_confirm(content,function() {
                    Mask();
                    ajaxSubmit(e,{
                        url: url,
                        data:data,
                        method: "post",
                        failCallback:function(resultMsg){				
                        	// 访问地址失败，或发生异常没有正常返回
                            messageAlert(resultMsg);
                            UnMask();
                        },
                        callback:function(resultMsg){
                            location.reload(true);
                        }
                    });
                });
            }
        }

        //查流程模板
        function model(sbuId,versions){
            var json = '{"正式":{"1":"60133","2":"60137","3":"60143","4":"60135","5":"60139","6":"57501","7":"57501","8":"57501"},';
            json +='"测试":{"1":"55038","2":"122557","3":"122561","4":"122555","5":"122559","6":"","7":"","8":"175142"}}';
            var model = JSON.parse(json);
            return model[versions][sbuId];
        }
        
        // 审核
        function check(e) {
            var $this = $(e);
            var $form = $("#inputForm");
            if ($form.valid()) {
                var url = "/member/credit_recharge/check.jhtml";
                var data = $form.serialize() + "&flag=1";
                var str = "您确定要审核吗？";
                $.message_confirm(str,function() {
                    Mask();
                    ajaxSubmit($this, {
                        url: url,
                        method: "post",
                        data: data,
                        failCallback:function(resultMsg){				// 访问地址失败，或发生异常没有正常返回
                            $.message_alert(resultMsg.rmid+"<br/>"+resultMsg.content);
                            UnMask();
                        },
                        callback: function (resultMsg) {
                           // $.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
                                location.reload(true);
                           // })
                        }
                    })
                })
            }
        }

        function close_t(e) {
            var $this = $(e);
            var $form = $("#inputForm");
            var content = '您确定要作废吗？';
            if ($form.valid()) {
                $.message_confirm(content,function() {
                    Mask();
                    ajaxSubmit(e, {
                        url: "/member/credit_recharge/cancel.jhtml",
                        method: "post",
                        data: $form.serialize() + "&flag=2",
                        failCallback:function(resultMsg){				
                        	// 访问地址失败，或发生异常没有正常返回
                        	messageAlert(resultMsg);
                            UnMask();
                        },
                        callback: function (resultMsg) {
                            location.reload(true);
                        }
                    })
                })
            }
        }
        
        function close_it(e) {
            var $this = $(e);
            var $form = $("#inputForm");
            var content = '您确定要关闭吗？';
            if ($form.valid()) {
                $.message_confirm(content,function() {
                    Mask();
                    ajaxSubmit(e, {
                        url: "/member/credit_recharge/close.jhtml",
                        method: "post",
                        data: $form.serialize()+ "&flag=2",
                        failCallback:function(resultMsg){				
                        	// 访问地址失败，或发生异常没有正常返回
                        	messageAlert(resultMsg);
                            UnMask();
                        },
                        callback: function (resultMsg) {
                            location.reload(true);
                        }
                    })
                })
            }
        }
        

        //计划赋值实际
        function addquantity() {
            var $input = $("input.pPrice");
            var $tr = $input.closest("tr");
            $tr.find("input.actualAmount").val($input.val());
        }

        function fmoney(s, n) {
            n = n > 0 && n <= 20 ? n : 2;
            s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
            var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
            t = "";
            for (i = 0; i < l.length; i++) {
                t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
            }
            return t.split("").reverse().join("") + "." + r;
        }
    </script>
    
</head>
<body>
<div class="pathh">
    &nbsp;${message("查看授信")}
</div>
<form id="inputForm" action="/member/credit_recharge/update.jhtml" method="post" enctype="multipart/form-data" type="ajax" validate-type="validate">
    <input type="hidden" name="id" value="${cr.id}"/>
    <input type="hidden" name="rechargeType" value="${cr.rechargeType}"/>
    <div class="tabContent">
        <table class="input input-edit">
            <tr>
                <th>
					${message("申请单号")}:
                </th>
                <td>${cr.sn}</td>
				[#if rechargeType == 1]
					<th>
						${message("用户")}:
	                </th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="storeMemberId" class="text storeMemberId" btn-fun="clear" value="${cr.storeMember.id}"/>
							<input type="text" name="storeMemberName" class="text storeMemberName" maxlength="200" onkeyup="clearSelect(this)" value="${cr.storeMember.username}" readOnly/>   
							[#if cr.docStatus==0 ]
								<input type="button" class="iconSearch" value="" id="selectStoreMember"/>
							[/#if]
						</span>
	                </td>
				[#else]
					<th>
						${message("客户")}:
	                </th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" btn-fun="clear" value="${cr.store.id}"/>
							<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" value="${cr.store.name}" readOnly/>
							[#if cr.docStatus==0 ]
								<input type="button" class="iconSearch" value="" id="selectStore"/>
							[/#if]
						</span>
	                </td>
				[/#if]
                <th>
					${message("申请额度")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-" onMouseDown="decrease(this,event)" onClick="addquantity()"/>
                        <input type="text" class="t pPrice" name="amount" value="${cr.amount}" minData="0" oninput="addquantity()" onpropertychange="editPrice(this,event)"/> 
                        <input type="button" value="+" class="b increase" onMouseDown="increase(this,event)" onClick="addquantity()"/>     
                    </div>
                </td>
                <th>
					${message("实际充值金额")}:
                </th>
                <td>
					[#if cr.docStatus==0  && (!isCheckWf || cr.wfId==null)]
                        <div class="nums-input ov" id="n-input">
                            <input type="button" class="b decrease" value="-" onMouseDown="decrease(this,event)"/>
                            <input type="text" class="t actualAmount" name="actualAmount" value="${cr.actualAmount}" minData="0" maxData="${cr.amount}" oninput="addquantity()" onpropertychange="editPrice(this,event)"/>
                            <input type="button" value="+" class="b increase" onMouseDown="increase(this,event)"/>
                        </div>
                        <a href="javascript:;" id="addquantity" class="iconButton" onClick="addquantity()" title="">
                        	<span id="qButton"></span>
                        </a>
					[#elseif cr.type ==1]
						<span class="red number">${currency(dr.actualAmount, true)}</span>
					[#else]
						<span class="red number"></span>
					[/#if]
                </td>
            </tr>
            <tr>
                <th>${message("机构")}:</th>
                <td>
					<span class="search" style="position:relative">
						<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${cr.saleOrg.id}"/>
						<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${cr.saleOrg.name}" readOnly/>    
						<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
                </td>
                <th>${message("客户编码")}:</th>
                <td>
                    <span id="outTradeNo">${cr.store.outTradeNo}</span>
                </td>
                <th>
					${message("流程状态")}:
                </th>
                <td>
					[#if cr.wfState??]
						${message("22222222"+cr.wfState)}
					[/#if]
                </td>

                <th>
					${message("开始日期")}:
                </th>
                <td>
					[#if cr.docStatus==0  ]
                        <input id="startTime" name="startDate" class="text"
                               value="${(cr.startDate?string("yyyy-MM-dd"))!""}"
                               onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});"
                               type="text" btn-fun="clear"/>
					[#else]
						<span>${(cr.startDate?string("yyyy-MM-dd"))!"-"}</span>
					[/#if]
                </td>
            </tr>
            <tr>
                <th>
					${message("结束日期")}:
                </th>
                <td>
					[#if cr.docStatus==0  ]
                        <input id="endTime" name="endDate" class="text" value="${(cr.endDate?string("yyyy-MM-dd"))!""}"
                               onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});"
                               type="text" btn-fun="clear"/>
					[#else]
					<span>${(cr.endDate?string("yyyy-MM-dd"))!"-"}</span>
					[/#if]
                </td>
                <th>${message("单据状态")}:</th>
                <td>
					[#if cr.docStatus == 0]<b class="blue">${message("已保存")}</b>[/#if]
					[#if cr.docStatus == 1]<b class="blue">${message("处理中")}</b>[/#if]
					[#if cr.docStatus == 2]<b class="green">${message("已处理")}</b>[/#if]
					[#if cr.docStatus == 3]<b class="red">${message("已作废")}</b>[/#if]
					[#if cr.docStatus == 4]<b class="red">${message("已关闭")}</b>[/#if]
                </td>
                <th>
					${message("创建人")}:
                </th>
                <td>
					${cr.creator.name}
                </td>
                <th>
					${message("申请日期")}:
                </th>
                <td>
                    <span>${cr.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("经营组织")}:
                </th>
                <td>
                    <select name="organizationId" class="text" id="organizationId">
						[#list organizationList as organization]
                            <option value="${organization.id}" [#if cr.organization.id == organization.id]selected="selected"[/#if]>${organization.name}</option>
						[/#list]
                    </select>
                </td>
                <th>${message("状态")}:</th>
                <td>
					[#if cr.type == "0"]<b class="red">${message("未生效")}</b>[/#if]
        			[#if cr.type == "1"]<b class="blue">${message("已生效")}</b>[/#if]
        			[#if cr.type == "2"]<b class="">${message("已过期")}</b>[/#if]
                </td>
                <th>${message("Sbu")}:</th>
                <td>
                    <input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${cr.sbu.id}"/>
                    <span id="sbuName">${cr.sbu.name}</span>
                </td>
             <th>${message("客户余额")}</th>
             <td>
             	<span class="red" id="balance"></span>
             </td>
            </tr>
             <tr>
                <th>${message("客户简称")}:</th>
                <td>
                	<span id="storeAlias">${cr.store.alias}</span>
                </td>
				<th>
					<span class="requiredField">*</span>${message("合同名称")}:
				</th>
            	<td>
	           		 <span class="search" style="position: relative">
	            		  <input  type="hidden" name="contractId" class="text contractId" value="" btn-fun="clear" />
	            		  <input type="text" name="contractName" class="text contractName" value=""maxlength="200" onkeyup="clearSelect(this)" readOnly /> 
	            		  <input type="button" class="iconSearch" value="" id="selectContract">
	           		 </span>
            	</td>
                 <th class="aftersaleDS">${message("售后单据")}:
                 </th>
                 <td class="aftersaleDS">
                    <span class="search" style="position: relative">
                       [#-- <input type="hidden" name="aftersale.id" class="text aftersaleId" value="${cr.aftersale.id}"
                               btn-fun="clear"/>--]
                        <input type="text" name="aftersaleSn" class="text aftersaleSn" value="${cr.fourAftersaleSn}"
                               maxlength="200" readOnly/>
                        <input type="button" class="iconSearch" value="" id="openAftersale">
                    </span>
                 </td>
                <th></th>
                <td></td>
            </tr>
            <tr>
                <th>
				${message("备注")}:
                </th>
                <td colspan="7">
                    <textarea class="text" name="memo">${cr.memo}</textarea>
                </td>
            </tr>
            <tr class="s-tr">
                <td colspan="8"></td>
            </tr>
            <tr class="border-L1">
                <th>${message("附件信息")}:</th>
                <td colspan="7"><a href="javascript:;" id="addAttach" class="button">添加附件</a></td>
            </tr>
            <tr class="border-L1">
                <td colspan="8">
                    <div class="w_1135">
                        <table id="table-attach"></table>
                    </div>
                </td>
            </tr>
            <tr class="border-L1">
                <td colspan="8">
                    <div>
                        <table id="table-m1"></table>
                    </div>
                </td>
            </tr>
        </table>
        <table class="input input-edit" style="width:100%;margin-top:5px;">
            <tr class="border-L2">
                <th>${message("全链路信息")}:</th>
                <td colspan="7"></td>
            </tr>
            <tr class="border-L2">
                <td colspan="8">
                    <table id="table-full"></table>
                </td>
            </tr>
        </table>
    </div>
    <div class="fixed-top">
			<a href="javascript:void(0);" class="iconButton" id="addButton"
               onClick="create_iframe('/member/credit_recharge/add/${code}.jhtml?flag=${flag}&rechargeType=${cr.rechargeType}&sbuId=${cr.sbu.id }')">
                <span class="addIcon">&nbsp;</span>${message("1001")}
            </a>
           [#if cr.docStatus == 2 && cr.type !="2"]
           		<input type="button" class="button cancleButton" value="${message("关闭")}" onclick="close_it(this,2)">
           [/#if]
			[#if  cr.docStatus==0]
				<input type="button" id="submit_button" onclick="save(this)" class="button sureButton" value="${message("保存")}">
				<input type="button" class="button sureButton" value="${message("流程审批")}" onclick="check_wf(this,1)"/>
				<input type="button" class="button cancleButton" value="${message("作废")}" onclick="close_t(this,2)"/>
			[/#if]
        <input type="button" onclick="reloding()" class="button refreshButton ml15" value="${message("刷新")}">
    </div>
</form>
<div id="wf_area" style="width:100%"></div>
</body>
</html>