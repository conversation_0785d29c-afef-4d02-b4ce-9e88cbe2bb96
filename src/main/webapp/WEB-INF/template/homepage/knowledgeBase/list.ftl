<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("知识库列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/resources/js/dynamicForm/mmGridConfiguration.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/formList.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>
<script type="text/javascript">
	$().ready(function(e) {
		//产品分类
		productType(e,"全部知识库",'/homepage/knowledgeBase/getNodes.jhtml','knowledge_category_name');
	    //获取筛选html
		parameterList('${userId}','${dTemplates.id}');
		//遍历列表表头
		traverseList('${userId}','${dTemplates.id}','${defaultQuery}','${dTemplates.pdfPath}','${dTemplates.excelPath}','500',1);
	    //获取按钮控件
		getButtonHtml(0,'${dTemplates.id}');

		var notice = ${notice!"0"};
		if (notice==1){
			$("#addButton").css("display","none");
		}
	});
	//查看
	function edit(id){
		var notice = $(".notice").val();
		if (notice==1){
				parent.change_tab(0,'notice.jhtml?id='+id);
		}else {
			parent.change_tab(0,'edit.jhtml?id='+id);
		}

	}
	//新增
	function add(e){
	    parent.change_tab(0,'add.jhtml');
	}
</script>
</head>
<body class="tree-contain">
<div class="subnav">
  	<div class="tit"><s></s>知识库分类</div>
  	<div class="subn-list ztree" id="js-menu">
  	</div>
</div>
<label id="labBtn"></label>
<div class="main-right">
	<form id="listForm" action="list.jhtml" method="get">
		<input type="hidden" name="userId" class="userId"  value="${userId}"/>
		<input type="hidden" name="templateId" class="templatesId"  value="${dTemplates.id}"/>
		<input type="hidden" name="pagesize" class="pagesize" value="${pagesize}"/>
		<input type="hidden" name="notice" class="notice" value="${notice!"0"}"/>
		<div class="bar">
			<div class="buttonWrap"></div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
				<div id="search-content" >
					<table id="search-table">
					</table>	
				 </div>		
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
	</div>
</body>
</html>