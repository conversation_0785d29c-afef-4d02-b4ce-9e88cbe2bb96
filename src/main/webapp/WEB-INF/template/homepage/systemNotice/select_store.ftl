<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查询用户")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	function initTypes(){
		types = {};
		[#list types as value]
			types['${value}'] = "${message('11111111'+value)}";
		[/#list]
	}
	$().ready(function() {
		
		/**初始化多选下拉框*/
		initMultipleSelect();
		
		/**初始化客户类型*/
		initTypes();
		
		var cols = [
		{ title:'${message("用户名")}', name:'username' ,align:'center'},
		{ title:'${message("姓名")}', name:'name' ,align:'center'},
		{ title:'${message("所属角色")}',name:'pc_role_name',align:'center' }
	];
	var multiSelect = false;
	[#if multi==2]
		multiSelect = true;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		[#if multi!=2]checkByDblClickTd:true,[/#if]
		rowCursorPointer:true,
		formQuery:true,
        cols: cols,
        [#if memberType==0]	//企业用户
			url: '/member/store_member/list_data.jhtml',
		[/#if]
		[#if memberType==1]	//外部用户
			url: '/member/store_member/list_out_data.jhtml',
		[/#if]
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	});
	function childMethod(){
	   return $mmGrid.selectedRows();
	};
</script>
</head>
<body  style="min-width: 0px;">
<form id="listForm" action="select_store_data.jhtml" method="post">
	<input type="hidden" name="search" value="1">
	<input type="hidden" name="isMember" value="${isMember}">
	<input type="hidden" name="multi" value="${multi}">
	<input type="hidden" name="type" value="[#if type!=null]${type.ordinal()}[/#if]">
	<input type="hidden" name="isSl" value="${isSl}">
	<input type="hidden" name="saleOrgId" value="${saleOrgId}">
	<input type="hidden" name="isSelect" value="${isSelect}">
	<input type="hidden" name="isCustomer" value="${isCustomer}">
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
		    	<dl>
    				<dt ><p>${message("16005")}:</p></dt>
	    			<dd >
	    				<input type="text" class="text" id="username" name="username"  btn-fun="clear"/>
	    			</dd>
	    		</dl>
	    		<dl>
	    			<dt><p>${message("16006")}:</p></dt>
	    			<dd>
	    				<input type="text" class="text" id="name" name="name" btn-fun="clear"/>
	    			</dd>
	    		</dl>
	        </div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
	</form>
</body>
</html>