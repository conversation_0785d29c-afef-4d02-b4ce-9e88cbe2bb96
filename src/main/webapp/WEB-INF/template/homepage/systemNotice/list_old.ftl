<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript">
function edit(id){
	parent.change_tab(0,'edit.jhtml?id='+id);
}
$().ready(function() {
	var cols = [
		{ title:'${message("标题")}', name:'title' , align:'center', renderer: function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';		
		}},
		{ title:'${message("副标题")}', name:'second_title' ,align:'center' },
		{ title:'${message("是否发布")}', name:'is_public' , align:'center', renderer: function(val){
			if(val=='1'){
				return '<span class="trueIcon">&nbsp;</span>';
			}else if(val=='0'){
				return '<span class="falseIcon">&nbsp;</span>';
			}
		}},
		{ title:'${message("发布时间")}', name:'public_date' , align:'center', renderer: function(val,item,rowIndex){
			var public_date = '';
			if(val!='' && val.length>10){
				public_date = val.substring(0,10);
			}
			return public_date;		
		}},
		{ title:'${message("序号")}', name:'orders' ,align:'center' },
		{ title:'${message("创建人")}', name:'store_member_name' ,align:'center' },
		{ title:'${message("创建时间")}', name:'create_date' , align:'center' },
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });

});

function check_view(url){
	var w = $(window).width();
	var h = $(window).height();
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
		title:'${message("查看客户")}',
		width:w,
		height:h,
		content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
		onOk: function() {
			var rows = $("#"+iframeId)[0].contentWindow.childMethod(btn);
			alert(rows);
		}
	});
	$dialog.find(".dialogContent").css("height",h+"px");
	$dialog.css("top",0+"px").css("max-height",h+"px");
	

}

</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
	<input  type="hidden" name="type" value="0"/>
		<div class="bar">
		<div class="buttonWrap">
			<a href="javascript:;" onclick="parent.change_tab(0,'add.jhtml',1)" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
		</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
			    	<dl>
						<dt><p>${message("标题")}:</p></dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="title" value=""  btn-fun="clear">
						</dd>
					</dl>
					[#--
					<dl>
						<dt><p>${message("发布人")}:</p></dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="store_member_name" value=""  btn-fun="clear">
						</dd>
					</dl>
					--]
					<dl>
						<dt><p>${message("是否发布")}:</p></dt>
						<dd>
						<select name="isPublic" class="text">
							<option value>请选择</option>
							<option value=0>否</option>
							<option value=1>是</option>
						</select>
						</dd>
					</dl>
		        </div>
			</div>
			
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	    	<div id="body-paginator">
	        	<div id="paginator"></div>
	    	</div>
		</div>
	</form>
</body>
</html>