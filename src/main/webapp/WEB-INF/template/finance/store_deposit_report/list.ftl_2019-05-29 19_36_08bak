<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("客户余额流水报表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$().ready(function() {
	/**初始化多选的下拉框*/
	initMultipleSelect();
	var cols = [
		{ title:'${message("单据类型")}', 		name:'type',		  align:'center'},
		{ title:'${message("当前余额")}', 		name:'balances',	  align:'center'},
		{ title:'${message("单据编号")}', 		name:'sn',			  align:'center'},
		{ title:'${message("单据开始日期")}',  	name:'create_date' ,  align:'center'},
		{ title:'${message("单据结束日期")}', 	name:'modify_date',  align:'center'},
		{ title:'${message("单据状态")}', 		name:'invoice_status',align:'center'},
		{ title:'${message("SBU")}', 		name:'sbu_name' ,	  align:'center'},
		{ title:'${message("客户")}', 		name:'store_name' ,	  align:'center'},
		{ title:'${message("机构")}', 		name:'sale_org_name', align:'center'},
		{ title:'${message("经营组织")}', 		name:'organization',  align:'center'},
		{ title:'${message("数量")}', 		name:'quantity',	  align:'center'},
		{ title:'${message("发货数量")}', 		name:'shipping_quantity',align:'center'},
		{ title:'${message("关闭数量")}', 		name:'close_quantity',align:'center'},
		{ title:'${message("销售单价")}', 		name:'unit_price',	  align:'center'},
		[#if isMember == 0]
		{ title:'${message("平台结算价")}',	name:'sale_org_price',align:'center'},
		[/#if]
		{ title:'${message("金额")}', 		name:'money',		  align:'center'},
		{ title:'${message("发货金额")}', 		name:'shipping_money',align:'center'},
		{ title:'${message("关闭金额")}', 		name:'close_money',	  align:'center'},
		[#if isMember == 0]
		{ title:'${message("结算金额")}', 		name:'settle_account',align:'center'},
		{ title:'${message("结算发货金额")}',	name:'shipping_settle_account',align:'center'},
		{ title:'${message("结算关闭金额")}', 	name:'close_settle_account',   align:'center'},
		{ title:'${message("产品")}', 		name:'product_name' , align:'center'},
		[/#if]
		{ title:'${message("品类")}', 		name:'product_category',align:'center'},
		{ title:'${message("备注")}', 		name:'memo',		  align:'center'}
	];
	$mmGrid = $('#table-m1').mmGrid({
		 cols: cols,
	     autoLoad: false,
        url: 'list_data.jhtml',
//        lineRoot:"order_items",
        callback:function(data){
	    	var $tr = $("table.mmg-body tr");
	    	var size = $tr.length;
	    	if(size==1 && $tr.hasClass("emptyRow")){
	    		size=0;
	    	}
	    	$('.currentPageLine').text(size);
	    	$(".recordsCheckedLine").text(0);
	    	$(".CheckedOrderLine").text(0);
	    	$(".CheckedAccountLine").text(0);
	    },
        method: 'post',
        params:function(){
        	return $("#listForm").serializeObject();
        },
        root: 'content',
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	 $("input.checkAll").click(function(){
			var $this = $(this);
			var $itemInputs = $("input[name='itemIds']");
			if($this.prop("checked")){
				 $itemInputs.prop("checked",true);
			}else{
				$itemInputs.prop("checked",false);
			}
			var lineCheckedNum = $("input[name='itemIds']:checked").length;
			var $orderCheckedinput = $("input[name='itemIds']:checked");
			var $checkInputs = $("input.mmg-check:checked");
			var atotal = 0;
			var stotal = 0;
			$("tr.selected").each(function(){
				var $this = $(this);
				var amount = Number($this.find("input[name='suliang']").val());
				atotal = accAdd(atotal,amount);
			})
			$checkInputs.each(function(){
				var $this = $(this);
				var $tr = $this.closest("tr");
				var sprice = Number($tr.find("input[name='sprice']").val());
				stotal = stotal+sprice;
			})
			$(".CheckedOrderLine").text(atotal);
			$(".CheckedAccountLine").text(currency(stotal,true));
			$(".recordsCheckedLine").text(lineCheckedNum);
		});
		
		$(".mmg-check").live("click", function(){
			var $this = $(this);
			var rowIndex = $this.closest("tr").attr("rowIndex");
			var row = $mmGrid.row(rowIndex);
			var $tr = $("tr[rowIndex='"+rowIndex+"']");
			var $itemInputs = $tr.find("input[name='itemIds']:enabled");
			if($this.prop("checked")){
				 $itemInputs.prop("checked",true);
			}else{
				$itemInputs.prop("checked",false);
			}
			var lineCheckedNum = $("input[name='itemIds']:checked").length;
			var $orderCheckedinput = $("input[name='itemIds']:checked");
			var $checkInputs = $("input.mmg-check:checked");
			var atotal = 0;
			var stotal = 0;
			$("tr.selected").each(function(){
				var $this = $(this);
				var amount = Number($this.find("input[name='suliang']").val());
				atotal = accAdd(atotal,amount);
			})
			$checkInputs.each(function(){
				var $this=$(this);
				var $tr = $this.closest("tr");
				var sprice = Number($tr.find("input[name='sprice']").val());
				stotal = stotal+sprice;
			})
			$(".CheckedOrderLine").text(atotal);
			$(".CheckedAccountLine").text(currency(stotal,true));
			$(".recordsCheckedLine").text(lineCheckedNum);
		});
		
		$("input[name='itemIds']").live("click",function(){
			var lineCheckedNum = $("input[name='itemIds']:checked").length;
			var $orderCheckedinput = $("input[name='itemIds']:checked");
			var total = 0;
			$orderCheckedinput.each(function(){
				var $this = $(this);
				var $tr = $this.closest("tr");
				var amount = Number($tr.find("input[name='suliang']").val());
				var sprice = Number($tr.find("input[name='sprice']").val());
				total = accAdd(total,amount);
			})
			$(".CheckedOrderLine").text(total);
			$(".recordsCheckedLine").text(lineCheckedNum);
		});
	
	//查询下单人	
	$("#selectStroeMember").bindQueryBtn({
		type:'storeMember',
		title:'${message("查询下单人")}',
		url:'/member/store_member/select_store_member.jhtml'
	});	
	
	/*多选客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?isSelect=0',
		callback:function(rows){
			if(rows.length>0){
				var mhtml="";
				if($("input[name='storeName']").val() == null){
					var all= "";
				}else{
					var all= $("input[name='storeName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".storeId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('客户【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					all =all +','+ rows[i].name;
					mhtml = '<div><input name="storeId" class="text storeId storeId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
					$(".store").append(mhtml);
				}
				$("input[name='storeName']").attr("value",all);
				
			}
		}
	});*/
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
	
	
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var mhtml="";
				if($("input[name='saleOrgName']").val() == null){
					var all= "";
				}else{
					var all= $("input[name='saleOrgName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".saleOrg_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('机构【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					all =all +','+ rows[i].name;
					mhtml = '<div><input name="saleOrgId" class="text saleOrg saleOrg_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="saleOrgclosePro(this)"></i></div>';
					$(".saleOrg").append(mhtml);
				}
				$("input[name='saleOrgName']").attr("value",all);
			}
		}
	});
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('产品【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					allName =allName +','+ rows[i].name  
					vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="closePro(this)"></i></div>'
					$(".product").append(vhtml);
				}
				$("input[name='productName']").attr("value",allName)
			}
		}
	});
});
	function closePro(e) {
		$(e).closest("div").remove();
		var allName2 = '';
		$(".product > div").each(function() {
			allName2 = allName2 + ',' + $(this).find("p").html();
		})
		$("input[name='productName']").attr("value", allName2);
	}
	function newclosePro(e) {
		$(e).closest("div").remove();
		var allName2 = '';
		$(".store > div").each(function() {
			allName2 = allName2 + ',' + $(this).find("p").html();
		})
		$("input[name='storeName']").attr("value", allName2);
	}
	function saleOrgclosePro(e) {
		$(e).closest("div").remove();
		var allName3 = '';
		$(".saleOrg > div").each(function() {
			allName3 = allName3 + ',' + $(this).find("p").html();
		})
		$("input[name='saleOrgName']").attr("value", allName3);
	}
	//条件导出		    
	function segmentedExport(e) {
		var needConditions = false;//
		var page_url = 'to_condition_export.jhtml';//分页导出统计页面
		var url = 'condition_export.jhtml';//导出的方法
		conditions_export(e, {
			needConditions : needConditions,
			page_url : page_url,
			url : url
		});
	}

	
	function fmoney(s, n) { 
		n = n > 0 && n <= 20 ? n : 2; 
		s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + ""; 
		var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1]; 
		t = ""; 
		for (i = 0; i < l.length; i++) { 
		t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : ""); 
		} 
		return t.split("").reverse().join("") + "." + r; 
	}
</script>
</head>
<body>
<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
						<a href="javascript:void(0);" class="iconButton" id="export1Button">
							<span class="impIcon">&nbsp;</span>导出
						</a>
						<ul class="flag-list">
							<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
						</ul>
					</div>
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("搜索")}</a>
			</div>
			<div id="searchDiv">
				<div id="search-content">
			 <dl>
			    	<dt><p>${message("编号")}：</p></dt>
			    	<dd>
						<input type="text" class="text"  name="sn" btn-fun="clear"/>
			    	</dd>
			    </dl>
        		 <dl>
						<dt><p>${message("客户名称")}:</p></dt>
	    				<dd>
	    				<!--	<span style="position:relative">
								<input class="text storeName" maxlength="200" type="text" name="storeName" value="" onkeyup="clearSelect(this)">
								<input type="button" class="iconSearch" value="" id="selectStore">
								<div class="pupTitleName  store"></div>
							</span> -->
							<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" id="storeId" btn-fun="clear" />
							<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" id="storeName" />
							<input type="button" class="iconSearch" value="" id="selectStore">
						</span>
	    				</dd></dl>
        			<dl>
        			
        		<!--	<dt><p>${message("机构名称")}：</p></dt>
        		 	<dd >
        				<span style="position:relative">
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" id="saleOrgName" />
							<input type="button" class="iconSearch" value="" id="selectSaleOrg">
							<div class="pupTitleName  saleOrg"></div>
						</span>
        			</dd>  		
        			</dl> -->
					<dl>
	        			<dt><p>${message("经营组织")}：</p></dt>
	        			<dd>
						    <select  name="organizationName" class="text">
								[#list organizations as organization]
								<option value="${organization.name}">${organization.name}</option>
								[/#list]
							</select>
	        			</dd>
	        		</dl>
	        		<dl>
	        			<dt><p>${message("SBU")}：</p></dt>
	        			<dd>
						      <select  name="sbuName" class="text">
								[#list sbus as sbu]
								<option  value="${sbu.sbu.name}">${sbu.sbu.name}</option>
								[/#list]
							</select>
	        			</dd>
	        		</dl>
					<dl>
						<dt><p>${message("单据时间")}:</p></dt>
						<dd class="date-wrap">
								<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
								<div class="fl">--</div>
								<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
							</dd>
						</dl>
					<dl>
					<dt><p>${message("单据类型")}:</p></dt>
					<dd>
					<label><input type="checkbox" name="type"  value="1"/>订单</label>
					<label><input type="checkbox" name="type"  value="2"/>退货单</label>
					<label><input type="checkbox" name="type"  value="3"/>销售回款</label>
					<label><input type="checkbox" name="type"  value="4"/>政策录入</label>
					<label><input type="checkbox" name="type"  value="5"/>客户授信</label>
					</dd>
					</dl>
				</div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
</form>
</body>
</html>
		