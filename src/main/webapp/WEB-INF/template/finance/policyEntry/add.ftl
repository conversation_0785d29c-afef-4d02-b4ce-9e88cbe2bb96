<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("政策录入")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/resources/js/utils.js"></script>
<style>
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
</style>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,2,true,e)
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $submitButton = $("#submitButton");
	$("input[name='image']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
	
	var d = new Date();
	var month=d.getMonth()+1;
	$("input.balanceMonth").val(d.getFullYear()+"-"+(month<10?'0'+month:month));
	
	// 表单验证
	$inputForm.validate({
		rules: {
			storeName: {
				required: true
			},
			taxRate: {
				required: true
			},
			amount: {
				required: true,
				decimal: {
					integer: 12,
					fraction: ${setting.priceScale}
				}
			},
			balanceMonth: {
				required: true
			},
			organizationId: {
				required: true
			}
		} 
	});
	
	
	/**初始化订单附件*/
    var depositAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues['depositAttachs['+depositAttachIndex+'].url']=url;
			hideValues['depositAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName:'depositAttachs['+depositAttachIndex+'].name',
				hideValues:hideValues
			});               
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="depositAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			depositAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
    
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
     var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
    
    	var cols = [				
    	{ title:'${message("支付单编号")}', name:'sn' ,width:120, align:'center'},
		{ title:'${message("支付单类型")}', name:'type' ,align:'center'},
		{ title:'${message("支付方式")}', name:'method' ,align:'center'},
		{ title:'${message("终端类型")}', name:'term_type' ,align:'center'},
		{ title:'${message("付款类型")}', name:'pay_type' ,align:'center'},
		{ title:'${message("付款金额")}', name:'amount' ,align:'center' ,renderer:function(val,item,rowIndex){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		{ title:'${message("交易流水号")}', name:'trade_no',width:120 ,align:'center'},
		{ title:'${message("支付状态")}', name:'status' ,align:'center', renderer: function(val,item,rowIndex){
			return statuss[val];
		}},
		{ title:'${message("付款日期")}', name:'payment_date',width:120 ,align:'center'}
	];
	$('#table-payment').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true,
    });
	
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
	
	$("form").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'view.jhtml?id='+resultMsg.objx;
			});
		}
	 });
	
	//查询客户
	 $("#selectStore").click(function(){
		//sbu 
        var sbuId = $(".sbuId").val();
        if(isNull(sbuId) == null){
         	$.message_alert('sbu不能为空');
             return false;
        }
        //经营组织
        var organizationId = $("#organizationId option:selected").val();
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			bindClick:false,
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1'+'&sbuId='+sbuId,
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$(".storeName").val(row.name);
					$(".storeId").val(row.id);
					$("#storeAlias").text(row.alias);
					$("#outTradeNo").html(row.out_trade_no);
					$("input[name='regionalManagerId']").attr("value",row.store_member_id);
	                $("#regionalManagerName").text(row.store_member_name);
	                $(".saleOrgName").val(row.sale_org_name);
					$(".saleOrgId").val(row.sale_org_id);
					//可用余额
					customerBalance(row.id,sbuId,organizationId);
				}
			}
		});
	});
	
	
	//经营组织
	$("#organizationId").live("change", function() {
		//经营组织
		var organizationId = $(this).val();
		//sbu 
	    var sbuId = $(".sbuId").val();
		//客户
	    var storeId = $(".storeId").val(); 
	  	//可用余额
	    customerBalance(storeId,sbuId,organizationId);
	}) 
	
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$(".saleOrgId").val(row.id);
				$(".saleOrgName").val(row.name);
				$(".storeName").val('');
				$(".storeId").val('');
				$("#storeAlias").text('');
				$("#outTradeNo").html('');
				$("input[name='regionalManagerId']").attr("value",'');
                $("#regionalManagerName").text('');
			}
		}
	});
	
	$("#rechargeTypeId").change(function(){
		if($("#rechargeTypeId").val()==340){
			$("#paybutton").show();
		}else{
			$("#paybutton").hide();
		}
	})
	
	var str='';
	if($("#policyTypeId option:selected").attr("memo")=="1"){
		str='${message("有发票")}<input name="hasInvoice" type="hidden" value="1"/>';
	}else{
		str='${message("无发票")}<input name="hasInvoice" type="hidden" value="0"/>';
	}
		$("#hasInvoice").html(str);
		//无发票：政策类型+ 发票类型,ERP现金流项目默认空
		//有发票：政策类型+erp现金流项目,,发票类型 默认空
		var num =$("input[name='hasInvoice']").val();
		if (num == 1) {//有发票
			$("#cashProjectId").show();
			$("#invoiceTypeId").hide();
			$("#invoiceTypeId option:selected").attr("selected", false);
		}else if (num == 0) {//无发票
			$("#invoiceTypeId").show();
			$("#cashProjectId").hide();
			$("#cashProjectId option:selected").attr("selected", false);
		}
	
	$("#policyTypeId").change(function(){
		var html='';
		if($("#policyTypeId option:selected").attr("memo")=="1"){
			html='${message("有发票")}<input name="hasInvoice" type="hidden" value="1"/>';
		}else{
			html='${message("无发票")}<input name="hasInvoice" type="hidden" value="0"/>';
		}
			$("#hasInvoice").html(html);
			//无发票：政策类型+ 发票类型,ERP现金流项目默认空
			//有发票：政策类型+erp现金流项目,,发票类型 默认空
			var num =$("input[name='hasInvoice']").val();
			if (num == 1) {//有发票
				$("#cashProjectId").show();
				$("#invoiceTypeId").hide();
				$("#invoiceTypeId option:selected").attr("selected",false);
				$("#taxRate option:selected").attr("selected", false);
			}else if (num == 0) {
				$("#invoiceTypeId").show();
				$("#cashProjectId").hide();
				$("#cashProjectId option:selected").attr("selected", false);
			}
		//AAA
		var option = $("#policyTypeId option:selected").text().substring(0,3);
		var organization = $("#organizationId");
		if(option == 208){
			$("#organizationId option[value=7]").attr("selected", true);
		}
		if(option == 219){
			$("#organizationId option[value=8]").attr("selected", true);
		}
	})
	
	$("#organizationId").change(function(){
		var organization = $("#organizationId option:selected").val();
		var option = $("#policyTypeId").find('option');
		
		for(var i=0;i<=option.length;i++){
			var a = option.eq(i).text().substring(0,3);
			option.eq(i).show();
			if(organization == 7){//大自然家居（中国）有限公司
				if(a != 208){//208
					option.eq(i).hide();
				}
				if(a == 208){
					option.eq(i).show();
				}
			}
			if(organization == 8){//广西柏景地板有限公司
				if(a != 219){//219
					option.eq(i).hide();
				}
				if(a == 219){
					option.eq(i).show();
				}
			}
			if(organization != 7||organization == 8){
				
			}
		}
	});
	
});
function pay(){
		var num=$("input[name='amount']").val();
		if(num==null ||num=='' ||num==undefined){
		$.message_alert("请填写计划充值金额");
		return false;
		}
		$("#payamount").val(num);
		$("#payForm").submit();
	}

//保存
function save(e){
	var url = 'save.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		var data = $form.serialize();
		var content = '您确定要保存吗？';
		$.message_confirm(content,function() {
			Mask();
			ajaxSubmit(e,{
				url: url,
				data:data,
				method: "post",
				failCallback:function(resultMsg){				// 访问地址失败，或发生异常没有正常返回
					$.message_alert(resultMsg.rmid+"<br/>"+resultMsg.content);
					UnMask();
				},
				callback:function(resultMsg){
					location.href= 'view.jhtml?id='+resultMsg.objx;
				}
			})
		});
	}
}

function clearSelect(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("div.search");
		$this.prev("input").val("");
		$(".bankName").text('');
	}
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("政策录入申请")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("政策录入编号")}:
					</th>
					<td></td>
					<th>
						<span class="requiredField">*</span>${message("客户")}:
					</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" value="[#if storeMember.memberType == 1]${store.id}[/#if]" btn-fun="clear"/>
							<input type="text" name="storeName" class="text storeName" value="[#if storeMember.memberType == 1]${store.name}[/#if]" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
							<input type="button" class="iconSearch" value="" id="selectStore">
						</span>
					</td>
					<th>
						<span class="requiredField">*</span>${message("政策金额")}:
					</th>
					<td>
						<div class="nums-input ov">
							<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
							<input type="text"  class="t"  name="amount" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
							<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
						</div>				
					</td>
					<th>
						${message("实际录入金额")}: 
					</th>
					<td></td>
				</tr>
				<tr>
					<th>${message("机构")}:</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${saleOrg.id}"/>
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${saleOrg.name}" readOnly/>
							<input type="button" class="iconSearch" value="" id="selectSaleOrg">
						</span>
					</td>
					<th>${message("客户简称")}:</th>
					<td>
						<span id="storeAlias"></span>
					</td>
					<th>${message("客户编码")}:</th>
					<td>
					   <span id="outTradeNo"></span>
					</td>
					<th>${message("客户余额")}:</th>
					<td>
						<span class="red" id="balance"></span>
					</td>
				</tr>
				<tr>
					<th>${message("Sbu")}:</th>
		            <td>
		            	<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbu.id}"/>
		            	<span id="sbuName">${sbu.name}</span>
		            </td>
		            <th>
						<span class="requiredField">*</span>
						${message("组织")}:
					</th>
					<td>
						<select class="text" name="organizationId" id="organizationId">
							[#list organizations as organization]
							<option  value="${organization.id}" [#if organization.id==oz.id]selected[/#if]>${organization.name}</option>
							[/#list]
						</select>
					</td>
					<th>
						${message("充值类型")}:
					</th>
					<td>
						<select id="rechargeTypeId" name="rechargeTypeId" class="text">
							[#list rechargeTypes as rechargeType]
								[#if rechargeType.value=="政策录入"]
									<option value="${rechargeType.id}">${rechargeType.value}</option>
									[#break]
								[/#if]
							[/#list]
						</select>
					</td>
					<th>
						${message("对账月份")}:
					</th>
					<td>
						<input type="text " value="" class="text balanceMonth" name="balanceMonth" onfocus="WdatePicker({dateFmt: 'yyyy-MM'});" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("到款状态")}:</th>
					<td>
						<b class="blue">${message("未到账")}</b>
					</td>
					<th>
						<span class="requiredField">*</span>${message("税率")}:
					</th>
					<td>
					   <select id="taxRate" name="taxRate" class="text">
						    <option value="" >请选择</option>
							<option value="0" >0</option>
							<option value="16">OUT_VAT16</option>
							<option value="5">OUT_VAT5</option>
							<option value="4">OUT_VAT4</option>
							<option value="13">OUT_VAT13</option>
							<option value="10">OUT_VAT10</option>
							<option value="17">OUT_VAT17</option>
							<option value="11">OUT_VAT11</option>
						</select>
					</td>
					<th>
						${message("erp现金流项目")}:
					</th>
					<td id="cashProjectIdText">
						<select id="cashProjectId" name="cashProjectId" class="text">
							<option value="">请选择</option>
							[#list cashProjects as cashProject]
								<option value="${cashProject.id}">${cashProject.value}</option>
							[/#list]
						</select>
					</td>
					<th>
						${message("是否有发票")}:
					</th>
					<td id="hasInvoice"></td>
				</tr>
				<tr>
					<th>
						<span class="requiredField">*</span>
						${message("发票类型")}:
					</th>
					<td id="invoiceTypeIdText">
						<select id="invoiceTypeId" name="invoiceTypeId" class="text">
						    <option value="">请选择</option>
							[#list invoiceTypes as invoiceType]
								<option value="${invoiceType.id}" >${invoiceType.value}</option>
							[/#list]
						</select>
					</td>
					<th>
						<span class="requiredField">*</span>${message("政策类型")}:
					</th>
					<td>
						<select id="policyTypeId" name="policyTypeId" class="text">
							<option value="" >请选择</option>
							[#list policyTypes as policyType]
								<option value="${policyType.id}" memo="${policyType.lowerCode}">${policyType.value}</option>
							[/#list]
						</select>
					</td>
				    <th>
						${message("申请日期")}:
					</th>
					<td>
						<input type="text" class="text" name="applyDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${nowDate}"  />
					</td>
					<th>${message("单据状态")}:</th>
					<td>
						<b class="blue">${message("未处理")}</b>
					</td>
				</tr>
				<tr>
					<th>
						${message("GL日期")}:
					</th>
					<td>
						<input type="text" class="text" name="glDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${nowDate}"  />
					</td>
		             <th>${message("区域经理")}:</th>
					 <td>
						 <input type="hidden" name="regionalManagerId" class="text storeMemberId" btn-fun="clear" value="" /> 
						 <span id="regionalManagerName"></span>
					 </td>
				     <th>${message("创建人")}:</th>
					 <td></td>
					 <th>${message("创建日期")}:</th>
					 <td></td>
				</tr>
				<tr>
					<th>
						${message("申请备注")}:
					</th>
					<td colspan="7">
						<textarea name="memo" class="text  policyMemo" id="memo" required></textarea>
					</td>
				</tr>
				<tr>
					<th></th>
					<td colspan="7">
						<span class="requiredField">
	                                                                       步骤说明:<br/>
	                          208->大自然家居（中国）有限公司<br/>
	                          219->广西柏景地板有限公司<br/>
			                                                  选择政策类型-》是否有发票-》<br/>
			                                                  无发票：政策类型+ 发票类型<br/>
			                                                  有发票（就收应付中转、总账中转）：政策类型+erp现金流项目<br/>
			                                                  如果报错,请截图告知管理员协助分析原因(一般来说需要作废重新选择).<br/>
	              		</span>
	              	</td>
				</tr>
			</table>
			<div class="title-style">
				${message("全链路信息")}
			</div>
			<table id="table-full"></table>
			<div class="title-style">
				${message("附件信息")}
				<div class="btns">
					<a href="javascript:;" id="addAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			<input type="button" id="submit_button" onclick="save(this)" class="button sureButton" value="${message("提交")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>