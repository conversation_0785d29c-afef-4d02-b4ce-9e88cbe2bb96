<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("政策录入查看")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<style type="text/css">
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
	#qButton{
		width:20px;
		height:20px;
		position:absolute;
		top:4px;
		left:12px;
		background:url(/resources/images/button/ico-aa.png) no-repeat center;
	}
	#addquantity {
		padding:0 21px;
		height:28px;
		line-height:28px;
		margin-right:0;
	}
	#n-input{
		width:74%;
		float:left;
		margin-right:5px;
	}
</style>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,2,true,e)
}
function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var flag =1;
	var content = '您确定要审核吗？';
	var data = $form.serialize()+"&flag="+flag;
	if($form.valid()){
		$.message_confirm(content,function(){
			//begin
			ajaxSubmit(e,{
				 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=59&objid=${dr.id}',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 	if(rows.length==1){
				 		data = data+'&objConfId='+rows[0].id;
				 		ajaxSubmit('',{
							 url: "check_wf.jhtml",
							 method: "post",
							 data: data,
							 callback:function(resultMsg){
								$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
									reflush_wf();
								});
							 }
						})
				 	}else{
				 		var str = '';
					 	for(var i=0;i<rows.length;i++){
					 		var row = rows[i];
					 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					 	}
					 	var content = '<table class="input input-edit" style="width:100%">'
								+'<tbody><tr><th>流程模版</th>'
								+'<td>'
									+'<select class="text" id="objConfId">'
										+str
									+'</select>'
								+'</td>'
							+'</tr></tbody></table>';
						var $dialog_check = $.dialog({
							title:"政策录入审核",
							height:'135',
							content: content,
							onOk:function(){
								var objConfId = $("#objConfId").val();
								if(objConfId=='' || objConfId == null){
									$.message_alert("请选择政策录入模版");
									return false;
								}
								var url="check_wf.jhtml";
								data = data+'&objConfId='+objConfId;
								
								ajaxSubmit($this,{
									 url: url,
									 method: "post",
									 data: data,
									 callback:function(resultMsg){
										//$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
											reflush_wf();
										//});
									 }
								})
								
							}
						});
				 	}
					var h = 150;
					$dialog_check.css("top",h+"px");
				 }
			});
		});
	}
}

// 审核
function check(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		var url="checkStoreRecharge.jhtml";
		var data = $form.serialize()+"&flag=1";
		var str = "您确定要审核吗？";
		$.message_confirm(str,function() {
			Mask();
			ajaxSubmit($this,{
				url: url,
				method: "post",
				data: data,
				failCallback:function(resultMsg){				
					// 访问地址失败，或发生异常没有正常返回
					messageAlert(resultMsg);
					UnMask();
				},
				callback:function(resultMsg){
					location.reload(true);
				}
			})
		})

	}
}

function save(e,isSubmit){
	var url = 'updata.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		var data = $form.serialize();
		var content = '';
		if(isSubmit==1){
			content = '您确定要提交吗？';
			data+= '&isSubmit='+1
		}else{
			content = '您确定要保存吗？';
		}
		$.message_confirm(content,function() {
			Mask();
			ajaxSubmit(e,{
				url: url,
				data:data,
				method: "post",
				failCallback:function(resultMsg){				
					// 访问地址失败，或发生异常没有正常返回
					messageAlert(resultMsg);
					UnMask();
				},
				callback:function(resultMsg){
					location.reload(true);
				}
			});
		});
	}
};

function close_e(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var flag = 2;
	var	content = '您确定要作废吗？';
	$.message_confirm(content,function(){
		var url="cancel.jhtml";
		var data = $form.serialize()+"&flag="+flag;
		Mask();
		ajaxSubmit($this,{
			 url: url,
			 method: "post",
			 data: data,
			 failCallback:function(resultMsg){				
             	// 访问地址失败，或发生异常没有正常返回
             	messageAlert(resultMsg);
                UnMask();
             },
			 callback:function(resultMsg){
				location.reload(true);
			 }
		})
	});
}

function pay(){
		$("#payForm").submit();
	}

function initGrid(){
	/**初始化订单附件*/
    var depositAttach_items = ${depositAttach_json};
    var depositAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
			if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name , item.name, item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['depositAttachs['+depositAttachIndex+'].id']=item.id;
				hideValues['depositAttachs['+depositAttachIndex+'].url']=url;
				hideValues['depositAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'depositAttachs['+depositAttachIndex+'].name',
					hideValues: hideValues
				});
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['depositAttachs['+depositAttachIndex+'].url']=url;
				hideValues['depositAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'depositAttachs['+depositAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="depositAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			depositAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:depositAttach_items,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row=data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	// 表单验证
	$inputForm.validate({
		rules: {
			storeName: {
				required: true
			},
			taxRate: {
				required: true
			},
			amount: {
				required: true,
// 				min:0.001,
				decimal: {
					integer: 12,
					fraction: ${setting.priceScale}
				}
			},
			balanceMonth: {
				required: true
			}
		} 
	});
	
	$.validator.addClassRules({
		policyMemo: {
			required: true
		}
	});
	
	[#if dr.wfId!=null]
	 $("#wf_area").load("/wf/wf.jhtml?wfid=${dr.wfId}");
	[/#if]

	var payment_types = {'0':'${message("第三方平台订单支付")}','1':'${message("用户余额充值")}','2':'${message("用户余额订单支付")}','3':'${message("客户余额充值")}','4':'${message("客户余额订单支付")}','5':'${message("供应商订单支付")}'};
    var methods = {'0':'${message("在线支付")}','1':'${message("线下支付")}'};
    var termTypes = {'0':'${message("APP")}','1':'${message("微商城")}','2':'${message("PC商城")}','3':'${message("PC后台")}'};
    var payTypes = {'0':'${message("用户余额")}','1':'${message("客户余额")}','2':'${message("微信")}','3':'${message("支付宝")}','4':'${message("银联")}','5':'${message("临时额度")}'};
    var statuss = {'0':'${message("等待支付")}','1':'${message("支付成功")}','2':'${message("支付失败")}'}
    
    var payment_items = ${payment_json};
    	var cols = [				
    	{ title:'${message("支付单编号")}', name:'sn' ,width:120, align:'center'},
		{ title:'${message("支付单类型")}', name:'type' ,align:'center', renderer: function(val,item,rowIndex){
			return payment_types[val];
		}},
		{ title:'${message("支付方式")}', name:'method' ,align:'center', renderer: function(val,item,rowIndex){
			return methods[val];
		}},
		{ title:'${message("终端类型")}', name:'term_type' ,align:'center', renderer: function(val,item,rowIndex){
			return termTypes[val];
		}},
		{ title:'${message("付款类型")}', name:'pay_type' ,align:'center', renderer: function(val,item,rowIndex){
			return payTypes[val];
		}},
		{ title:'${message("付款金额")}', name:'amount' ,align:'center' ,renderer:function(val,item,rowIndex){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		{ title:'${message("交易流水号")}', name:'trade_no',width:120 ,align:'center'},
		{ title:'${message("支付状态")}', name:'status' ,align:'center', renderer: function(val,item,rowIndex){
			return statuss[val];
		}},
		{ title:'${message("付款日期")}', name:'payment_date',width:120 ,align:'center'}
	];
	$('#table-payment').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:payment_items,
        checkCol: false,
        autoLoad: true,
    });
    
    var orderFullLink_items = ${fullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
    
    //查询客户
    $("#selectStore").click(function(){
        var sbuId = $(".sbuId").val();
        if(isNull(sbuId) == null){
         	$.message_alert('sbu不能为空');
             return false;
        }
        //经营组织
        var organizationId = $("input.organization").val();
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			bindClick:false,
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1'+'&sbuId='+sbuId,
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$(".storeName").val(row.name);
					$(".storeId").val(row.id);
					$("#storeAlias").text(row.alias);
					$("#outTradeNo").html(row.out_trade_no);
					$("input[name='regionalManagerId']").attr("value",row.store_member_id);
					$("#regionalManagerName").text(row.store_member_name);
					$(".saleOrgName").val(row.sale_org_name);
					$(".saleOrgId").val(row.sale_org_id);
					//可用余额
					customerBalance(row.id,sbuId,organizationId);
				}
			}
		});
	});
    
    
  	//可用余额
  	customerBalance('${dr.store.id}','${dr.sbu.id}','${dr.organization.id}');

	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$(".saleOrgId").val(row.id);
				$(".saleOrgName").val(row.name);
				$(".storeName").val('');
				$(".storeId").val('');
				$("#storeAlias").text('');
				$("#outTradeNo").html('');
				$("input[name='regionalManagerId']").attr("value",'');
				$("#regionalManagerName").text('');
			}
		}
	});
	
	$("input[name='image']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
	
	//附件初始化
	initGrid();
	
	var $delProductImage1 = $(".deleteAttachment");
	$delProductImage1.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	$("#inputForm").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	 });

    var amount = ${dr.actualAmount};
    if (amount.toString().indexOf("-") != -1){
        var str = fmoney(amount.toString().substring(1,amount.length));
        amount = "-" + str;
    } else {
        amount = fmoney(amount);
    }
    $('.number').text("￥" + amount);

});

//计划赋值实际
function addquantity() {
	var $input=$("input.pPrice");
	var $tr = $input.closest("tr");
	$tr.find("input.actualAmount").val($input.val());
}

function clearSelect(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("div.search");
		$this.prev("input").val("");
		$(".bankName").text('');
	}
}

function fmoney(s, n) {
    n = n > 0 && n <= 20 ? n : 2;
    s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
    var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
    t = "";
    for (i = 0; i < l.length; i++) {
        t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
    }
    return t.split("").reverse().join("") + "." + r;
}
/* function selectBal(){
	var ssId = $(".storeId").val();
	var sbuId = $("#sbuId").val();
	var saleOrgId = $(".saleOrgId").val();
	var orgId = $("#organizationId option:selected").val();
	ajaxSubmit("",{
        method:'post',
        url:'/finance/balance/get_balance.jhtml',
        data:{storeId:ssId,sbuId:sbuId,saleOrgId:saleOrgId,organizationId:orgId},
        callback:function(resultMsg) {
            var data = resultMsg.objx;
            if(data!=null){
	            //可用余额
	            $("#balance").text(currency(data.balance,true));            	
            }
        }
    });
} */
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看政策录入")}
	</div>
	<form id="inputForm" action=""  method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${dr.id}" />
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("政策录入编号")}:
					</th>
					<td>
						${dr.sn}
					</td>
					<th>
						${message("客户")}:
					</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" btn-fun="clear" value="${dr.store.id}"/>
							<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" value="${dr.store.name}"  />
							<input type="button" class="iconSearch" value=""  id="selectStore" >
						</span>
					</td>
					<th>
						${message("录入金额")}:
					</th>
					<td>
						<div class="nums-input ov">
							<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
							<input type="text"  class="t amount"  name="amount" value="${dr.amount}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
							<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
						</div>	
					</td>
					<th>
						${message("实际录入金额")}:
					</th>
					<td>
						[#if dr.docStatus == 1 && (!isCheckWf || dr.wfId==null)]
							<div class="nums-input ov" id="n-input">
								<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
								<input type="text"  class="t actualAmount"  name="actualAmount" value="${dr.actualAmount}" [#if dr.amount>0] minData="0" maxData="${dr.amount}"[#else] minData="${dr.amount}" maxData="0"[/#if] oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
								<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
							</div>
							<a href="javascript:;" id="addquantity" class="iconButton" onClick="addquantity()" title=""><span id="qButton"></span></a>					
						[#else]
							<span class="red number">${currency(dr.actualAmount, true)}</span>
							<input type="hidden"  name="actualAmount" value="${dr.actualAmount}" />
						[/#if]
					</td>
				</tr>
				<tr>
					<th>${message("机构")}:</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${dr.saleOrg.id}"/>
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${dr.saleOrg.name}" />
							<input type="button" class="iconSearch" value="" id="selectSaleOrg">
						</span>
					</td>
					<th>${message("客户简称")}:</th>
					<td>
						<span id="storeAlias">${dr.store.alias}</span>
					</td>
					<th>${message("客户编码")}:</th>
					<td>
					   <span id="outTradeNo">${dr.store.outTradeNo}</span>
					</td>
					<th>${message("客户余额")}</th>
		             <td>
		             	<span class="red" id="balance"></span>
		             </td>
				</tr>
				<tr>
					<th>${message("Sbu")}:</th>
		            <td>
			           	<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${dr.sbu.id}"/>
			           	<span id="sbuName">${dr.sbu.name}</span>
		            </td>
					<th>${message("组织")}:</th>
					<td>
						<span id="organization">${dr.organization.name}</span>
						<input type="hidden" class="text organization" name="organizationId" value="${dr.organization.id}"/>
					</td>
					<th>
						${message("充值类型")}:
					</th>
					<td>
						${dr.rechargeType.value}
					</td>
					<th>
						${message("对账月份")}:
					</th>
					<td>
						<input type="text " value="${dr.balanceMonth}" class="text balanceMonth" name="balanceMonth" onfocus="WdatePicker({dateFmt: 'yyyy-MM'});" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("到款状态")}:</th>
					<td>
						<b class="blue">${message("未到账")}</b>
					</td>
					<th>
						${message("放行人")}:
					</th>
					<td>
						<input type="text" class="text" value="" name="permitThroughName" disabled="disabled">
					</td>
					<th>
						${message("税率")}:
					</th>
					<td>
						<select id="taxRate" name="taxRate" class="text taxRate">
							<option value="0"  [#if dr.taxRate == '0']selected="selected" [/#if] >0</option>
							<option value="16" [#if dr.taxRate == '16']selected="selected" [/#if]>OUT_VAT16</option>
							<option value="5"  [#if dr.taxRate == '5']selected="selected"  [/#if]>OUT_VAT5</option>
							<option value="4"  [#if dr.taxRate == '4']selected="selected"  [/#if]>OUT_VAT4</option>
							<option value="13" [#if dr.taxRate == '13']selected="selected" [/#if]>OUT_VAT13</option>
							<option value="10" [#if dr.taxRate == '10']selected="selected" [/#if]>OUT_VAT10</option>
							<option value="17" [#if dr.taxRate == '17']selected="selected" [/#if]>OUT_VAT17</option>
							<option value="11" [#if dr.taxRate == '11']selected="selected" [/#if]>OUT_VAT11</option>
						</select>
					</td>
					<th>
						${message("erp现金流项目")}:
					</th>
					<td>
						${dr.cashProject.value}	
					</td>
				</tr>
				<tr>
					<th>
						${message("是否有发票")}:
					</th>
					<td>
						[#if dr.hasInvoice==1]
							${message("有发票")}
						[#else]
							${message("无发票")}
						[/#if]
					</td>
                    <th>
                        <span class="requiredField">*</span>
                        ${message("发票类型")}:
                    </th>
                    <td id="invoiceTypeIdText">
                        <select id="invoiceTypeId" name="invoiceTypeId" class="text">
                            <option value="">请选择</option>
                            [#list invoiceTypes as invoiceType]
                                <option value="${invoiceType.id}" [#if invoiceType.id == dr.invoiceType.id]selected="selected" [/#if]>${invoiceType.value}</option>
                            [/#list]
                        </select>
                    </td>
[#--					<th>--]
[#--						<span class="requiredField">*</span>--]
[#--						${message("政策类型")}:--]
[#--					</th>--]
[#--					<td>--]
[#--						${dr.policyType.value}--]
[#--					</td>--]
                    <th>
                        <span class="requiredField">*</span>${message("政策类型")}:
                    </th>
                    <td>
                        <select id="policyTypeId" name="policyTypeId" class="text">
                            <option value="" >请选择</option>
                            [#list policyTypes as policyType]
                                <option value="${policyType.id}" memo="${policyType.lowerCode}" [#if policyType.id == dr.policyType.id]selected="selected" [/#if]>${policyType.value}</option>
                            [/#list]
                        </select>
                    </td>
					<th>
						${message("申请日期")}:
					</th>
					<td>
						[#if dr.applyDate??]
							<span>${dr.applyDate?string("yyyy-MM-dd")}</span>
							<input type="hidden" name="applyDate" value="${dr.applyDate}">
						[/#if]
					</td>
				</tr>
				<tr>
					<th>${message("单据状态")}:</th>
					<td>
						[#if dr.docStatus == 0]<b class="blue">${message("已保存")}</b>[/#if]
						[#if dr.docStatus == 1]<b class="blue">${message("已提交")}</b>[/#if]
						[#if dr.docStatus == 2]<b class="green">${message("已处理")}</b>[/#if]
						[#if dr.docStatus == 3]<b class="red">${message("已作废")}</b>[/#if]
					</td>
					<th>${message("区域经理")}:</th>
					<td>
						<input type="hidden" name="regionalManagerId" class="text storeMemberId" btn-fun="clear" value="${dr.regionalManager.id}" /> 
						<span id="regionalManagerName">${dr.regionalManager.name}</span>
					</td>
					<th>
						${message("GL日期")}:
					</th>
					<td>
						<input type="text" class="text" name="glDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${dr.glDate}"  />
					</td>
					<th>
						${message("创建人")}:
					</th>
					<td>
						${dr.creator.name}
					</td>
				</tr>
				<tr>
					<th>
						${message("创建日期")}:
					</th>
					<td>
						<span>${dr.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
					</td>
                    <th>${message("售后单号")}:</th>
                    <td>
                        <input type="text" class="text" name="sourceSn" class="text sourceSn" btn-fun="clear" value="${dr.sourceSn}"/>
                    </td>
				</tr>
				<tr>
					<th>
						${message("申请备注")}:
					</th>
					<td colspan="7">
						<textarea name="memo" class="text policyMemo" id="memo" required>${dr.memo}</textarea>
					</td>
				</tr>
			    <tr>
				    <th></th>
					<td colspan="7">
						<span class="requiredField">
	                                                                       步骤说明:<br/>
	                          208->大自然家居（中国）有限公司<br/>
	                          219->广西柏景地板有限公司<br/>
			                                                  选择政策类型-》是否有发票-》<br/>
			                                                  无发票：政策类型+ 发票类型<br/>
			                                                  有发票（就收应付中转、总账中转）：政策类型+erp现金流项目<br/>
			                                                  如果报错,请截图告知管理员协助分析原因(一般来说需要作废重新选择).<br/>
	              		</span>
	              	</td>
				</tr>
			</table>
			<div class="title-style">
				${message("全链路信息")}
			</div>
			<table id="table-full"></table>
			<div class="title-style">
				${message("附件信息")}
				<div class="btns">
					<a href="javascript:;" id="addAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			[#if dr.docStatus != 1]
				<a href="add.jhtml?sbuId=${dr.sbu.id}" class="iconButton" id="addButton">
					<span class="addIcon">&nbsp;</span>${message("1001")}
				</a>
			[/#if]
			[#if dr.docStatus == 1]
				<input type="button" class="button sureButton" value="${message("提交")}" onclick="save(this,1)">
				<a id="shengheButton" class="iconButton" onclick="check(this)"  >
					<span class="ico-shengheIcon">&nbsp;</span>${message("审核")}
				</a>
                <input type="button" class="button cancleButton" value="${message("作废")}" onclick="close_e(this,1)">
			[/#if]
		    <input type="button" onclick="reloding()" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<!-- 支付跳页面  -->
	<form id="payForm"  target="_blank" action="submit.jhtml"  method="post" >
	</form>
	<!--  end   -->
	<div id="wf_area" style="width:100%"></div>
</body>
</html>