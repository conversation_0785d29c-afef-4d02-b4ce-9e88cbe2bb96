<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("编辑流程模板")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/icon.css">
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/plugin/jquery-easyui-*******/jquery.easyui.min.js"></script>
<script type="text/javascript" src="/resources/js/base/datagrid-dnd.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
		
	$inputForm = $("#inputForm");
		
		// 表单验证
	$inputForm.validate({
		rules: {
    		name: "required"
		} ,
		submitHandler:function(form){
			return false;
		}
	});

	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	 });
	 
	[#if bill.wfId!=null]
		$("#wf_area").load("/act/wf/wf.jhtml?wfid=${bill.wfId}");
	[/#if]
	
	/**
    var frozenColumns = [[]];
    var wfProcs = ${wfProcs!'[]'};
	var cols = [[	
		{ title:'${message("任务名称")}', field:'activityName' ,width:150,align:'center'},
		{ title:'${message("任务状态")}', field:'activityState' ,width:150,align:'center',formatter:function(val,item){
			var str = "";
			if (val==0) str = "已执行";
			if (val==1) str = "待执行";
			if (val==2) str = "未执行";
			return str;
		}},
		{ title:'${message("处理人")}', field:'assigneeName',width:150 ,align:'center'},
		{ title:'${message("处理时间")}', field:'endTime' ,width:150 ,align:'center'},
		{ title:'${message("处理结果")}', field:'approved' ,width:150 ,align:'center',formatter:function(val,item){
			var str = "";
			if (val=="true") str = "提交";
			if (val=="false") str = "驳回";
			return str;
		}},
		{ title:'${message("处理意见")}', field:'suggestion' ,width:150 ,align:'center'}
	]];
	var options ={
		tableName:"actBillEditProc",//表名
		data:wfProcs,//初始化数据
		columns: cols,//列
		frozenColumns:frozenColumns,//冻结列，
		pagination:false,//是否使用分页
		onHeaderContextMenu: function (e, field) {
			e.preventDefault();
	}};
	//初始化列表	 
	initEasyUiTable('#table-full',options);*/
});

function createWf() {
	ajaxSubmit('',{
		method:'post',
		url:'createWf.jhtml?id=${bill.id}&modelId=1',
		async: true,
		callback: function(resultMsg) {
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	})
}

/**
function submit1() {
	ajaxSubmit('',{
		method:'post',
		url:'submit.jhtml?id=${bill.id}&modelId=1',
		async: true,
		callback: function(resultMsg) {
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	})
}

function reject() {
	ajaxSubmit('',{
		method:'post',
		url:'reject.jhtml?id=${bill.id}',
		async: true,
		callback: function(resultMsg) {
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	})
}

function interrupt() {
	ajaxSubmit('',{
		method:'post',
		url:'interrupt.jhtml?id=${bill.id}',
		async: true,
		callback: function(resultMsg) {
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	})
}
*/
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看测试单据")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
        <input type="hidden" name="id" value="${bill.id}" />
	<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("名称")}:
				</th>
				<td>
					<input type="text" name="name" value="${bill.name}" class="text" btn-fun="clear" />
				</td>
				<th>
					${message("流程状态")}:
				</th>
				<td>
					[#if bill.wfState == 0]未启动
					[#elseif bill.wfState == 1]审核中
					[#elseif bill.wfState == 2]已完成
					[#elseif bill.wfState == 4]中断
					[/#if]
				</td>
				<th>
					${message("流程图")}:
				</th>
				<td>
					[#if wf??]
						<a target="_blank" class="red" href="/actbill/process/monitor_show_image/${wf.procInstId}.jhtml">查看</a>
					[/#if]
				</td>
				<th>
					${message("创建日期")}:
				</th>
				<td>
					${bill.createDate?string("yyyy-MM-dd HH:mm:ss")}
				</td>
			</tr>
		</table>
		[#--
		<div class="title-style">
			${message("流程明细")}
		</div>
		<table id="table-full"></table>
		--]
		</div>
		<div class="fixed-top">
				<a href="add.jhtml" class="iconButton" id="addButton">
					<span class="addIcon">&nbsp;</span>${message("1001")}
				</a>
				[#if wf??]
				[#else]
				<input type="button" onclick="createWf()" class="button sureButton" value="流程审批" />
				<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
				[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>