<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <title></title>
    <link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css"/>
    <link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css"/>
    <script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
    <script src="/resources/js/swiper.min.3.4.2.js" type="text/javascript"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
</head>
<body>
<input type="hidden" name="storeId" id="storeId" value="${storeId}"/>
<input type="hidden" name="storeName" id="storeName" value="${storeName}"/>
<div id="containt">

    <div class="swiper-container i-banner">
        <div class="swiper-wrapper">
            [#list findImage as fi]
                <div class="swiper-slide"><a href="${fi.url}"><img src="${fi.image}"></a></div>
            [/#list]
        </div>
        <div class="swiper-pagination i-pages"></div>
    </div>

    <div class="i-fastItem">
        <div><a href="/mobile/order/balance.jhtml"><i class="ico-f01"></i>余额</a></div>
        [#if storeMember.memberType == 0]
            <!-- 企业用户 -->
            <div><a onclick="countTodo(this)"><i class="ico-f02"></i>待办事项<b>${countTodo}</b></a></div>
        [/#if]
    </div>
    <div class="swiper-container i-navScroll mt6">
        <div class="swiper-wrapper">
            [#--<div class="swiper-slide">
                <a href="/mobile/order/list_sbu.jhtml?type=1" class="item"><i class="ico-n01"></i>商品下单</a>
                <a href="/mobile/order/order_query.jhtml" class="item"><i class="ico-n02"></i>订单查询</a>
                <a href="/mobile/order/list_sbu.jhtml?type=2" class="item"><i class="ico-n03"></i>充值申请</a>
                <a href="/mobile/order/store_recharge_query.jhtml" class="item"><i class="ico-n04"></i>充值查询</a>
                <a href="/mobile/product/product_display.jhtml" class="item"><i class="ico-n05"></i>产品展示</a>
                <a href="/mobile/knowledge/repository.jhtml" class="item"><i class="ico-n06"></i>知识库</a>
                [#if isHave==0]
                <a href="/mobile/theMe/list_theme.jhtml" class="item"><i class="ico-n001"></i>数据采集</a>
                [/#if]
                <a href="/mobile/shop/shop_manage.jhtml" class="item"><i class="ico-n21"></i>门店管理</a>

                <div class="swiper-slide">
                    <a href="/mobile/member/store_apply.jhtml" class="item"><i class="ico-n22"></i>客户加盟</a>
                    <a href="/mobile/order/set_data.jhtml" class="item"><i class="ico-n08"></i>资料设置</a>
                    <a href="/mobile/salesman/kanban.jhtml" class="item"><i class="ico-n22"></i>看板</a>
                    <a href="#" onclick="logout()" class="item"><i class="ico-n09"></i>退出</a>

                </div>

            </div>--]

            <!-- 菜单控制 -->
            [#assign menuSize = 0][#--父级菜单个数--]
            [#list Session.menu_list as menu]
                [#if (menu.superId.id == null)]
                    [#assign menuSize = menuSize+1]
                [/#if]
            [/#list]

            [#assign page = 1][#--总页数--]
            [#list 0..menuSize as t]
                [#if ((menuSize - 6)/8) > t]
                    [#assign page = page+1]
                [/#if]
            [/#list]


            [#--首页菜单--]
            [#assign start = 1]   [#--每页开始值,首页为1--]
            [#assign end = 6] [#--每页结束值，首页为6--]
            [#assign index = 0][#--第n个图标--]

            <div class="swiper-slide">
                [#list Session.menu_list as menu]
                    [#if index < end]
                        [#if (menu.superId.id == null)]
                            <a href="${menu.url}[#if menu.url?contains('?')]${'&'}[#else]${'?'}[/#if]superId=${menu.id}" class="item"><i class="${menu.menuImg}"   ></i>${menu.menuName}</a>
                        [#--							[#if menu.url?contains('?')]&[#else]?[/#if]superId=${menu.id}" class="item"><i class="${menu.menuImg}"></i>${menu.menuName}</a>--]
                            [#assign index = index +1 ]
                        [/#if]
                    [/#if]
                [/#list]
                <a href="/mobile/order/set_data.jhtml" class="item"><i class="ico-n08"></i>资料设置</a>

                <a href="#" onclick="logout()" class="item"><i class="ico-n09"></i>退出</a>
            </div>

            [#--第二页之后菜单--]
            [#if page > 1]
                [#list 2..page as currentPage]
                    [#assign index = 1][#--第n个图标--]
                    [#assign start =((currentPage-2)*8+6)]
                    [#assign end = ((currentPage-1)*8+6)]
                    [#assign size = size + 8]
                    <div class="swiper-slide">
                        [#list Session.menu_list as menu]
                            [#if (menu.superId.id == null)]
                                [#if (index > start && index < (end + 1))]
                                    <a href="${menu.url}[#if menu.url?contains('?')]${'&'}[#else]${'?'}[/#if]superId=${menu.id}" class="item"><i class="${menu.menuImg}"></i>${menu.menuName}</a>
                                [#--                                    [#if menu.url?contains('?')]&[#else]?[/#if]superId=${menu.id}" class="item"><i class="${menu.menuImg}"></i>${menu.menuName}</a>--]
                                [/#if]
                                [#assign index = index+1]
                            [/#if]
                        [/#list]
                    </div>
                [/#list]
            [/#if]

            <!-- 菜单控制 -->
            [#--[#assign size = 5] 每页动态显示的图标数
            [#assign index = 0]
            <div class="swiper-slide">
                [#if isHave==0]
                    <a href="/mobile/theMe/list_theme.jhtml" class="item"><i class="ico-n001"></i>数据采集</a>
                [#else ]
                    [#assign index = index -1 ]
                    [#assign size = 6]
                [/#if]
                [#list Session.menu_list as menu]
                    [#if index <5]
                        [#if (menu.superId.id == null)]
                            <a href="${menu.url}
							[#if menu.url?contains('?')]&[#else]?[/#if]superId=${menu.id}" class="item"><i
                                        class="${menu.menuImg}"></i>${menu.menuName}</a>
                            [#assign index = index +1 ]
                        [/#if]
                    [/#if]
                [/#list]
                <a href="/mobile/order/set_data.jhtml" class="item"><i class="ico-n08"></i>资料设置</a>

                <a href="#" onclick="logout()" class="item"><i class="ico-n09"></i>退出</a>
            </div>

            [#if (index>=5)]
                <div class="swiper-slide">
                    [#assign index = 0]
                    [#list Session.menu_list as menu]
                        [#if (menu.superId.id == null)]
                            [#if (index >=size)]
                                <a href="${menu.url}
							[#if menu.url?contains('?')]&[#else]?[/#if]superId=${menu.id}" class="item"><i
                                            class="${menu.menuImg}"></i>${menu.menuName}</a>
                            [/#if]
                            [#assign index = index+1]
                        [/#if]
                    [/#list]
                </div>
            [/#if]--]




        </div>
        <div class="swiper-pagination"></div>
    </div>
    <div class="notice-box">
        <div class="t"><i>通</i><i>知</i>公告</div>
        <div class="swiper-container notice-scroll">
            <div class="swiper-wrapper">
                [#list systemNotices as systemNotice]
                    <div class="swiper-slide"><a href="/mobile/knowledge/notice.jhtml">${systemNotice.title}</a></div>
                [/#list]
            </div>
        </div>
    </div>

    <div style="display:none">
        <form id="loadData">
            <input type="hidden" name="storeId" id="storeId" value="${storeId}"/>
            <input type="hidden" name="storeName" id="storeName" value="${storeName}"/>
            [#list productCategorys as pc]
                <input type="hidden" name="pcIds" value="${pc.category}"></input>
            [/#list]
        </form>
    </div>

    <div id="content-data">
    </div>
    <div class="copyright">Copyright @ 大自然家居</div>
</div>
<script type="text/javascript">
    function logout() {
        $.ajax({
            url: '/logout.jhtml',
            type: 'POST',
            success: function (data) {
                if (data.type == "success") {
                    window.location.href = '/mobile/login.jhtml';
                }
            }
        });
    }

    $().ready(function () {
        //加载产品数据
        loadGoods();

        var count = 0;
        [#list Session.menu_list as menu]
        [#if (menu.superId.id == null)]
        count += 1;
        console.log('${menu.menuName}')
        [/#if]
        [/#list]

    })

    

    // 获取待办事项的个数
    function countTodo(e) {
        ajaxSubmit('', {
            method: 'get',
            url: "/mobile/order/count_todo.jhtml",
            callback: function (resultMsg) {
                var content = JSON.parse(resultMsg.content);
                $(e).find('b').text(content.countTodo);
                // 跳转到流程管理
                //window.location.href = '/mobile/process/process_manage.jhtml';
                window.location.href = '/mobile/act/wf/list.jhtml';

            }
        })
    }

    function loadGoods() {
        $.ajax({
            type: 'POST',
            url: '/mobile/load_goods.jhtml',
            data: $('#loadData').serialize(),
            success: function (data) {
                if (data.type == 'success') {
                    var rows = JSON.parse(data.content);
                    var count = 1;
                    var html = "";

                    for (var item in rows) {
                        if (rows[item].length > 0) {
                            row = rows[item];
                            html = '<div class="floor-box mt6">'
                                + '<div class="tit">'
                                + '<b>' + item + '</b>'
                                + '<a href="/mobile/product/product_display.jhtml" class="more">MORE</a>'
                                + '</div>'
                                + '<div class="swiper-container pro-scroll pro-00" id="pro-01">'
                                + '<div class="swiper-wrapper i-proList">';

                            for (var i = 0; i < row.length; i++) {
                                html += '<div class="swiper-slide">'
                                    // 								+     '<a href="/mobile/product/product_detail.jhtml?flag='+1+'&id='+row[i].id+'" class="item">'
                                    + '<a href="/mobile/product/product_detail.jhtml?flag=' + 1 + '&id=' + row[i].id + '&product_grade=' + row[i].level_Id + '" class="item">'
                                    + '<div class="pic"><img src="' + row[i].image + '"></div>'
                                    + '<div class="name">' + row[i].name + '(图片仅供参考)</div>'
                                    + '<p>型号: ' + row[i].model + '</p>';
                                if (typeof row[i].spec === 'undefined') {
                                    html += "<p>规格: </p>"
                                } else if (row[i].spec == "") {
                                    html += "<p>规格: </p>"
                                } else {
                                    html += "<p>规格: " + row[i].spec + "</p>"
                                }
                                html += '</a>'
                                    + '</div>'
                            }

                            html += '</div>'
                                + '<div>'
                                + '</div>';

                            count = count + 1;
                        }
                        $('#content-data').append(html);
                        html = "";
                        if (count >= 5) {
                            break;
                        }
                    }
                    var swiper = new Swiper('.pro-00', {
                        slidesPerView: 3.4,
                        paginationClickable: true,
                        spaceBetween: 8
                    });
                }
            }
        })
    }

</script>
<script type="text/javascript">
    var swiper = new Swiper('.i-banner', {
        pagination: '.i-banner .swiper-pagination',
        paginationType: 'fraction',
        autoplay: 5000,
        autoplayDisableOnInteraction: false
    });
    var swiper = new Swiper('.i-navScroll', {
        pagination: '.i-navScroll .swiper-pagination',
        paginationClickable: true
    });
    var swiper = new Swiper('.notice-scroll', {
        direction: 'vertical',
        spaceBetween: 30,
        centeredSlides: true,
        autoplay: 5000,
        autoplayDisableOnInteraction: false
    });
    var swiper = new Swiper('#pro-01', {
        slidesPerView: 3.4,
        paginationClickable: true,
        spaceBetween: 8
    });
    var swiper = new Swiper('#pro-02', {
        slidesPerView: 3.4,
        paginationClickable: true,
        spaceBetween: 8
    });
    var swiper = new Swiper('#pro-03', {
        slidesPerView: 3.4,
        paginationClickable: true,
        spaceBetween: 8
    });
    var swiper = new Swiper('#pro-04', {
        slidesPerView: 3.4,
        paginationClickable: true,
        spaceBetween: 8
    });

</script>
</body>
</html>
