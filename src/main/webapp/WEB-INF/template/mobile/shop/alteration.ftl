<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>门店变更申请表</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    }else{
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

//文件上传 ----- start -----
//添加附件
function addAttach(e, attachFile) {
	$('#fileForm').find('input').removeAttr("name");
	$('#' + attachFile).attr("name", "file");
	$('#' + attachFile).trigger('click'); 
}

/** 
* 附件上传
* @param attachId 所在input标签的id名
* @param paramAttachs 后台实体类接收附件的参数名
* @param type 后台用来区分不同类型附件，若后台的附件类只存一个类型的附件，则可以不用传该值
*/
function fileUpload(e, attachId, paramAttachs, type) {
	var formData = new FormData($("#fileForm")[0]);
	var len = $('#'+attachId).find('div[class="item"]').size()
	$.ajax({
	    type:'GET',
	    url:'/common/fileurl.jhtml',
	    success:function(data) {
	        if(data.type == "success"){
	            $.ajax({
	                type:'POST',
	                url: data.objx,
	                data:formData,
	                cache: false,  
	                contentType: false,  
	                processData: false, 
	                success:function(data_) {
	                    data_ = JSON.parse(data_)
	                    if(data_.message.type == "success"){
	                        var html = "<div class='item'>"
	                                + "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
	                                + "<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
	                                + "<textarea class='txt' placeholder='请输入备注' id='attachMemo' name='"+paramAttachs+"["+len+"].memo'></textarea>"
	                                + "<input type='hidden' id='attachName' name='"+paramAttachs+"["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
	                                + "<input type='hidden' id='attachUrl' name='"+paramAttachs+"["+len+"].url' value='"+data_.url+"'/>"
	                                + "<input type='hidden' id='attachSuffix' name='"+paramAttachs+"["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
	                                + "</div>";
	                        $('#'+attachId).append(html);
	                    }
	                }
	            })              
	        }
	    }
	})
}
//删除附件
function del(e) {
    $(e).parent('div').remove();
}
//文件上传 ----- end -----

// 保存操作
function save(e){
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    $.ajax({
        type:'POST',
        url:'/shop/alteration/save.jhtml',
        data:$("#inputForm").serialize(),
        success:function(data) {
            if(data.type == 'success'){
                alert("保存成功");
            } else {
                alert("保存失败, " + data.content);
            }
        }
    })
}
</script>
</head>
<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
	    <div class="info-box">
	        <div class="title"><b>经销商信息</b><a href="#">查看历史记录</a></div>
	        <div class="dl-style01">
	            <dl class="readonly">
	                <dt>经销商授权编码</dt>
	                <dd><input type="text" readonly value="${store.grantCode}" class="txt" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商名称</dt>
	                <dd>
	                    <input type="text" value="${store.dealerName}" class="txt" readonly="readonly" />
    	                <input type="hidden" value="${store.id}" name ="storeId" class="txt" />
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>手机号码</dt>
	                <dd><input type="text" readonly value="${store.headPhone}" class="txt" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>固定号码</dt>
	                <dd><input type="text" readonly value="${store.fixedNumber}" class="txt" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>区域</dt>
	                <dd><input type="text" readonly value="${store.region}" class="txt" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>地区</dt>
	                <dd><input type="text" value="${shopInfo.area.fullName + ' ' + shopInfo.address}" class="txt" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>合伙人名称</dt>
	                <dd><input type="text" readonly value="${shopInfo.partnerName}" class="txt" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>合伙人电话</dt>
	                <dd><input type="text" readonly value="${shopInfo.partnerPhone}" class="txt" /></dd>
	            </dl>
	        </div>
	    </div>
	    
	    <div class="info-box mt8">
	        <div class="title"><b>旧店信息</b></div>
	        <div class="dl-style01">
	            <dl class="readonly">
	                <dt>操作类型</dt>
	                <dd>
	                    <input type="text" name="operationType" value="变更" class="txt" readonly="readonly"/>
	                    <input type="hidden" name="shopInfoId" value="${shopInfo.id}" />
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>变更类型</dt>
	                <dd><input type="text" name="changeType" value="搬迁" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>门店面积</dt>
	                <dd><input type="text" class="txt" value="${shopInfo.acreage}" readonly="readonly" /> ㎡</dd>
	            </dl>
	            <dl class="readonly">
	                <dt>旧店地址</dt>
	                <dd><input type="text" class="txt" value="${shopInfo.address}" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>门店位置</dt>
	                <dd><input type="text" value="${shopInfo.positionType}" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>公司性质</dt>
	                <dd><input type="text" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>门店所有</dt>
	                <dd><input type="text" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>城市行政等级</dt>
	                <dd><input type="text" value="${shopInfo.administrativeRank}" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl>
                    <dt>关店日期</dt>
                    <dd><input type="date" name="closingTime" class="txt time" value="" /></dd>
                </dl>
                <dl class="checkbox">
                    <dt>经营品类</dt>
                    <dd>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="businessCategory" value="全品类" /><div class="check_box"></div>全品类</label>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="businessCategory" value="实木" /><div class="check_box"></div>实木</label>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="businessCategory" value="三层复合" /><div class="check_box"></div>三层复合</label>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="businessCategory" value="多层复合" /><div class="check_box"></div>多层复合</label>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="businessCategory" value="强化" /><div class="check_box"></div>强化</label>
                    </dd>
                </dl>
	        </div>
	    </div>
	    
	    <div class="info-box mt8">
	        <div class="title"><b>新店信息</b></div>
	        <div class="dl-style01">
	            <dl>
	                <dt>建店日期</dt>
	                <dd><input type="date" name="openDate" class="txt time" value="" /></dd>
	            </dl>
	            <dl>
	                <dt>门店面积</dt>
	                <dd><input type="number" name="acreage" class="txt" value="请输入" /> ㎡</dd>
	            </dl>
	            <dl>
	                <dt>新店地址</dt>
	                <dd><input type="text" name="address" value="" class="txt" /></dd>
	            </dl>
	            <dl class="checkbox">
	                <dt>经营品类</dt>
	                <dd>
                        [#list BusinessCategory as bc]
                        <label class="checkhid" data-id="c"><input type="checkbox" name="businessCategory" vaule="${bc.value}"/><div class="check_box"></div>${bc.value}</label>
                        [/#list]
	                </dd>
	            </dl>
	            <dl>
	                <dt>门店位置</dt>
	                <dd>
	                    <select name="positionType" class="txt positionType" dir="rtl">
                            <option></option>
                            <option value="建材市场">${message("建材市场")}</option>
                            <option value="临街商铺">${message("临街商铺")}</option>
                            <option value="家具商城">${message("家具商城")}</option>
                            <option value="装饰公司">${message("装饰公司")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>公司性质</dt>
	                <dd>
	                    <select name="companyNature" class="txt companyNature" dir="rtl">
                            <option></option>
                            <option value="独立公司">${message("独立公司")}</option>
                            <option value="合伙公司">${message("合伙公司")}</option>
                            <option value="个体工商户">${message("个体工商户")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>门店所有</dt>
	                <dd>
	                    <select name="shopOwnership" class="txt shopOwnership" dir="rtl">
                            <option></option>
                            <option value="租赁">${message("租赁")}</option>
                            <option value="个人物业">${message("个体工商户")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>月租金（元/㎡）</dt>
	                <dd><input type="number" name="monthlyRent" class="txt" placeholder="请输入" /></dd>
	            </dl>
	            <dl>
	                <dt>城市行政等级</dt>
	                <dd>
	                    <select name="administrativeRank" class="txt administrativeRank" dir="rtl">
                            <option></option>
                            <option value="省级" >${message("省级")}</option>
                            <option value="市级" >${message("市级")}</option>
                            <option value="区县级">${message("区县级")}</option>
                            <option value="乡镇级">${message("乡镇级")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>是否参与门店设计</dt>
	                <dd>
	                    <select name="isDesign" class="txt isDesign">
                            <option></option>
                            <option value="true"  >${message("是")}</option>
                            <option value="false" >${message("否")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>门店搬迁原因</dt>
	                <dd><textarea class="txt" name="moveReason" placeholder="请输入"></textarea></dd>
	            </dl>
	            <div class="tips-box">温馨提醒：若需总部设计，新增流程审批后的7个工作日内需提交门店设计申请，逾期无法提交</div>
	            <dl class="upload-bBox">
	                <dt>上传附件/门店照片<br/>门头远近距离照、<br/>收银台、各样板区</dt>
	                <dd>
	                    <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'shopFile')">上传</a>
	                    <p class="f-red">只支持.jpg 格式</p>
	                </dd>
	            </dl>
	        </div>
	        <div class="atta-list" id="addShopAttach"></div>
	    </div>
	    
	    <div class="info-box mt8">
	        <div class="title"><b>区域经理填写</b></div>
	        <div class="dl-style01">
	            <dl class="readonly">   
	                <dt>客户</dt>
	                <dd><input type="text" value="${store.name}" class="txt" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商加盟时间</dt>
	                <dd><input type="text" class="txt" value="${store.activeDate}" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商学历</dt>
	                <dd><input type="text" class="txt" value="${store.dealerGrade}" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商性别</dt>
	                <dd>
	                    [#if store.dealerSex == 0]
	                    <input type="text" class="txt" value="男" readonly="readonly" />
	                    [#elseif store.dealerSex == 1]
	                    <input type="text" class="txt" value="女" readonly="readonly" />
	                    [/#if]
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>总经销商</dt>
	                <dd><input type="text" class="txt" value="${store.franchisee}" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
                    <dt>所属销售平台</dt>
                    <dd><input type="text" class="txt" value="${store.salesPlatform.name}" readonly="readonly" /></dd>
                </dl>
                <dl class="readonly">
                    <dt>所属部门</dt>
                    <dd><input type="text" class="txt" name="department" value="渠道" readonly="readonly" /></dd>
                </dl>
	            <dl>
	                <dt>门店类型</dt>
	                <dd>
	                    <select name="type" class="txt type" dir="rtl">
                            <option></option>
                            <option value="专卖店">${message("专卖店")}</option>
                            <option value="大家居">${message("大家居")}</option>
                            <option value="一址多名">${message("一址多名")}</option>
                            <option value="多品类">${message("多品类")}</option>
                            <option value="产品专区">${message("产品专区")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>城市行政等级</dt>
	                <dd>
	                    <select class="txt" dir="rtl">
                            <option></option>
                            <option value="省级">${message("省级")}</option>
                            <option value="市级">${message("市级")}</option>
                            <option value="区县级">${message("区县级")}</option>
                            <option value="乡镇级">${message("乡镇级")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl class="checkbox">
	                <dt>所含品牌</dt>
	                [#--
	                <dd>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="国际出口" disabled="disabled" [#if ib?seq_contains("国际出口") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('国际出口') == true]checked[/#if]"></div>国际出口</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="木香居地板" disabled="disabled" [#if ib?seq_contains("木香居地板") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('木香居地板') == true]checked[/#if]"></div>木香居地板</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="大自然工程" disabled="disabled" [#if ib?seq_contains("大自然工程") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('大自然工程') == true]checked[/#if]"></div>大自然工程</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="nature地板" disabled="disabled" [#if ib?seq_contains("nature地板") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('nature地板') == true]checked[/#if]"></div>nature地板</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="大自然地板" disabled="disabled" [#if ib?seq_contains("大自然地板") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('大自然地板') == true]checked[/#if]"></div>大自然地板</label>
	                </dd>
	                --]
	                <dd> 
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="国际出口" /><div class="check_box"></div>国际出口</label>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="木香居地板" /><div class="check_box"></div>木香居地板</label>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="大自然工程" /><div class="check_box"></div>大自然工程</label>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="nature地板" /><div class="check_box"></div>nature地板</label>
                        <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="大自然地板" /><div class="check_box"></div>大自然地板</label>
                    </dd>
	            </dl>
	            <dl>
	                <dt>所属品牌</dt>
	                <dd>
	                    <select name="belongBrand" class="txt" dir="rtl">
                            <option></option>
                            <option value="国际出口">${message("国际出口")}</option>
                            <option value="木香居地板">${message("木香居地板")}</option>
                            <option value="大自然工程">${message("大自然工程")}</option>
                            <option value="nature地板">${message("nature地板")}</option>
                            <option value="大自然地板">${message("大自然地板")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>销售渠道</dt>
	                <dd>
	                    <select name="salesChannel" class="txt salesChannel">
                            <option></option>
                            <option value="零售">${message("零售")}</option>
                            <option value="工程">${message("工程")}</option>
                            <option value="家装">${message("家装")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>VI版本</dt>
	                <dd>
	                    <select name="viVersion" class="txt viVersion" dir="rtl">
                            <option></option>
                            <option value="2013年以前版本">${message("2013年以前版本")}</option>
                            <option value="2013版本">${message("2013版本")}</option>
                            <option value="2017版本">${message("2017版本")}</option>
                        </select>
	                </dd>
	            </dl>
	        </div>
	    </div>
	    
	    <div class="info-box mt8">
	        <div class="title"><b>渠道部</b></div>
	        <div class="dl-style01">
	            <dl>
	                <dt>门店授权编号</dt>
	                <dd><input type="text" name="authorizationCode" class="txt" placeholder="请输入" /></dd>
	            </dl>
	            <dl>
	                <dt>新增档案编号</dt>
	                <dd><input type="text" name="increaseArchivesCode" class="txt" placeholder="请输入" /></dd>
	            </dl>
	            <dl>
	                <dt>新增时间</dt>
	                <dd><input type="date" name="newTime" class="txt time" value="" /></dd>
	            </dl>
	            <dl>
	                <dt>减少档案编号</dt>
	                <dd><input type="text" name="decreaseArchivesCode" class="txt" placeholder="请输入" /></dd>
	            </dl>
	            <dl>
	                <dt>减少时间</dt>
	                <dd><input type="date" name="decreaseTime" class="txt time" value="" /></dd>
	            </dl>
	            <dl>
	                <dt>门店情况备注</dt>
	                <dd><textarea class="txt" name="shopCaseNote" placeholder="请输入"></textarea></dd>
	            </dl>
	        </div>
	    </div>
    </form>
    
    <!-- 文件上传 -->
    <form id="fileForm">
        <input type="file" name="file" id="shopFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addShopAttach', 'shopAttachs')" style="display: none"/>
    </form>
    
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" class="btn-blue btn" onclick="save(this)"/></div>
</div>
</body>
</html>