<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>大自然售后勘察报告</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
 
input[type="number"] {
    -moz-appearance: textfield;
}

label.error {
    color: red;
    font-size: 12px;
    display: block;
    text-align: right;
    margin: -5px 3px 0 0;
}
</style>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    } else {
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

//当前页数
var pageNumber = 1;
// 每页数据量
var pageSize = 25;

$().ready(function(){
    // 初始化弹出框：如选择XXX  start  ---------
    $(".js-cancle").click(function(){
        $(".pup-obox").hide()
        $("body").attr("style","overflow:auto")
        $(".info-btns").show();  // 显示提交按钮
    });
    
    // 表单验证
    // 自定义validate验证输入的数字小数点位数不能大于两位
    jQuery.validator.addMethod("minNumber",function(value, element){
        var returnVal = true;
        inputZ = value;
        var ArrMen = inputZ.split(".");    //截取字符串
        if(ArrMen.length == 2){
            if(ArrMen[1].length>2){    //判断小数点后面的字符串长度
                returnVal = false;
                return false;
            }
        }
        return returnVal;
    }, "小数点后最多保留两位");         //验证错误信息
    
    $("#inputForm").validate({
        rules: {
        	name: "required",
        	contactNumber: {
                required: true,
                digits: true,
                minlength: 11,
                maxlength: 11
            },
            checkInTime: "required",
            downTime: "required",
            layingTime: "required",
            layingArea: {
            	required: true,
            	number: true,
            	min: 0.01,
            	minNumber: $('#layingArea').val()
            },
            layingAddress: "required",
            layingName: "required",
            layingMethod: "required",
            layingMoistureproof: "required",
            heatingSystem: "required",
            emergencyDegree: "required",
            
            batchNumber: "required",
            price: {
                required: true,
                number: true,
                min: 0.01,
                minNumber: $('#price').val()
            },
            totalSales: {
                required: true,
                number: true,
                min: 0.01,
                minNumber: $('#totalSales').val()
            },
            otherInformation: "required",
        },
        messages: {
            name: "请输入顾客姓名",
        	contactNumber: {
                required: "请输入联系电话",
                digits: "联系电话只能是数字",
                minlength: "长度不能小于11位",
                maxlength: "长度不能大于11位"
            },
            checkInTime: "请选择入住时间",
            downTime: "请选择故障时间",
            layingTime: "请选择铺设时间",
            layingArea: {
            	required: "请输入铺设面积",
            	number: "铺设面积只能是数值",
            	min: "铺设面积最小为0.01"
            },
            layingAddress: "请输入地址",
            layingName: "请选择铺设方",
            layingMethod: "请选择铺设方法",
            layingMoistureproof: "请选择铺设防潮",
            heatingSystem: "请选择采暖方式",
            emergencyDegree: "请选择紧急程度",
            
            batchNumber: "请输入‘批次号/板底喷码’",
            price: {
                required: "请输入销售单价",
                number: "销售单价只能是数值",
                min: "销售单价最小为0.01"
            },
            totalSales: {
                required: "请输入销售总额",
                number: "销售总额只能是数值",
                min: "销售总额最小为0.01"
            },
            otherInformation: "请输入新/旧房",
        },
        errorPlacement: function (error, element) { //指定错误信息位置
        	if (element.attr('name') == 'layingAddress') {
        	    error.insertAfter(element.parent().parent().parent()); //将错误信息添加当前元素的父结点后面
        	} else {
        	    error.insertAfter(element.parent().parent()); //将错误信息添加当前元素的父结点后面
        	}
        }
    })
    
    // 初始化下拉加载
    initScroll("ProductModel");
    
    // 初始化地址
    initAddress('xProvince');
})

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

/**
 * 初始化下拉加载
 * @param id 弹出框所在盒子的标识id
 */
function initScroll(id) {
	// 获取当前浏览器中的滚动事件 
    $("#" + id).scroll(function() {
        //获取当前目标div的滚动条高度
        var scrollHeight = $("#" + id).prop('scrollHeight');
        //判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
        if (scrollHeight <= ($("#" + id).scrollTop() + $("#" + id).height())) {
        	// 查询产品
        	if(id == "ProductModel"){
	            setTimeout(function () {
	                $.ajax({
	                    type:'POST',
	                    url:'/product/product/selectProductData.jhtml',
	                    data:{name:$('#searchByProductName').val(),pageNumber:++pageNumber,pageSize:pageSize},
	                    success:function(data) {
	                        fillProductData(data, 'ProductModel');
	                    }
	                })
	            }, 500);
        	}
        }
    });
}

// 弹出框相关 ------ start ------
// 搜索产品
function searchProduct() {
    var e = $('#containt').find('dd[data-id="ProductModel"]')[0]
    showPup(e);
}

function showPup(e){
    var id = $(e).attr("data-id");
    pageNumber = 1;
    $("body").attr("style","overflow:hidden");
    $(".pup-obox").hide();
    $(".info-btns").hide();  // 隐藏提交按钮
    $("#"+id).show();
    
    // 选择产品
    if(id == "ProductModel"){
        $('#'+id).find('ul').empty();
        $.ajax({
            type:'POST',
            url:'/product/product/selectProductData.jhtml',
            data:{name:$('#searchByProductName').val(),pageNumber:pageNumber,pageSize:pageSize},
            success:function(data) {
            	fillProductData(data, id);
            }
        })
    }
}

//填充产品数据
function fillProductData(data, id) {
    if(data.type == 'success'){
        var rows = JSON.parse(data.content).content;
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            var html = "<li data-id='ProductModel' onclick='selectItem(this)'><a href='#'>"
                    + "<div class='name'>"+ isNull(row.name) +"</div>"
                    + "<div class='fl'><span>产品型号：</span>"+ isNull(row.model) +"</div>"
                    + "<div class='fr'><span>产品等级：</span>"+ isNull(row.levelName) +"</div><br>"
                    + "<div class='fl'><span>规格：</span>"+ isNull(row.spec) +"</div><br>"
                    + "<div class='fl'><span>产品描述：</span>"+ isNull(row.description) +"</div>"
                    + "</a>"
                    + "<input type='hidden' class='xsProductId' value='"+ isNull(row.id) +"'/>"
                    + "<input type='hidden' class='xsProductName' value='"+ isNull(row.name) +"'/>"
                    + "<input type='hidden' class='xsProductModel' value='"+ isNull(row.model) +"'/>"
                    + "<input type='hidden' class='xsProductSpec' value='"+ isNull(row.spec) +"'/>" 
                    + "<input type='hidden' class='xsPcParent' value='"+ isNull(row.pc_parent) +"'/>" 
                    + "</li>";
            $('#'+id).find('ul').append(html);
        }
    }
}

// 将弹出框选择的数据回显到页面
function selectItem(e){
    var id = $(e).attr("data-id")
    $("body").attr("style", "overflow:auto");
    $(".pup-obox").hide();
    $(".info-btns").show();
    if(id == "ProductModel"){ // 选择产品
        $('#containt').find('input[name="product_model"]').val($(e).find('input[class="xsProductModel"]').val());
        $('#containt').find('input[name="productId"]').val($(e).find('input[class="xsProductId"]').val());
        $('#containt').find('input[id="productName"]').val($(e).find('input[class="xsProductName"]').val());
        $('#containt').find('input[id="productSpec"]').val($(e).find('input[class="xsProductSpec"]').val());
        if ($(e).find('input[class="xsPcParent"]').val() != "") {
            $.ajax({
                url : '/product/product/find_category.jhtml?parent='+$(e).find('input[class="xsPcParent"]').val(),
                type : "post",
                success : function(data) {
                    if(data != null && data.type == "success"){
                    	$('#containt').find('input[name="category"]').val(data.content);
                    }
                }
            });
        }
    }
}
// 弹出框相关 ------ end ------

// ------ 地址 start ------

// 初始化地址（初始化省）
function initAddress(idVal) {
    var data = {locale:"ChinaCN"};
    fillAddr(data, idVal);
}

/**
 * @param nextId 要初始化的下一级select所在的id值
 */
function initAddr(e, nextId) {
    var pid = $(e).find('option:selected').attr('id-val');
    var data = {locale:"ChinaCN", parentId:pid};
    $(e).parent().prev('input').val(pid);
    // 先清除旧的数据
    $(e).nextAll().html('<option>请选择</option>');
    if (pid == null || pid == '' || pid == 'undefined') {
        // 如果选中的是‘请选择’则修改地址的id为上一级选中的地区id
        if ($(e).index('select') == 1) {
            var idv = $(e).prev().find('option:selected').attr('id-val');
            $(e).parent().prev('input').val(idv);
        }
        return ;
    }
    // 填充新的数据
    fillAddr(data, nextId);
}

// 填充数据
function fillAddr(data, idVal) {
    $.ajax({
        type:'GET',
        url: "/member/index/area.jhtml",
        data: data,
        success:function(dataMap) {
            var optionHtml = "<option>请选择</option>";
            for (var key in dataMap) {
                optionHtml += "<option id-val='"+ key +"'>"+ dataMap[key] +"</option>";
            }
            $('#' + idVal).html(optionHtml);
        }
    })
}

function tailSelect(e) {
    var pid = $(e).find('option:selected').attr('id-val');
    $(e).parent().prev('input').val(pid);
    if (pid == null || pid == '' || pid == 'undefined') {
        // 如果选中的是‘请选择’则修改地址的id为上一级选中的地区id
        var idv = $(e).prev().find('option:selected').attr('id-val');
        $(e).parent().prev('input').val(idv);
    }
}

//------ 地址 end ------

// 文件上传 ----- start -----
function addAttach(e, attachFile) {
    $('#fileForm').find('input').removeAttr("name");
    $('#' + attachFile).attr("name", "file");
    $('#' + attachFile).trigger('click'); 
}

/** 
 * 附件上传
 * @param attachId 所在input标签的id名
 * @param paramAttachs 后台实体类接收附件的参数名
 */
function fileUpload(e, attachId, paramAttachs) {
    var formData = new FormData($("#fileForm")[0]);
    var len = $('#'+attachId).find('div[class="item"]').size()
    $.ajax({
        type:'GET',
        url:'/common/fileurl.jhtml',
        success:function(data) {
            if(data.type == "success"){
                $.ajax({
                    type:'POST',
                    url: data.objx,
                    data:formData,
                    cache: false,  
                    contentType: false,  
                    processData: false, 
                    success:function(data_) {
                        data_ = JSON.parse(data_)
                        if(data_.message.type == "success"){
                            var html = "<div class='item'>"
                                    + "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                                    + "<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
                                    + "<textarea class='txt' placeholder='请输入备注' id='attachMemo' name='"+paramAttachs+"["+len+"].memo'></textarea>"
                                    + "<input type='hidden' id='attachName' name='"+paramAttachs+"["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
                                    + "<input type='hidden' id='attachUrl' name='"+paramAttachs+"["+len+"].url' value='"+data_.url+"'/>"
                                    + "<input type='hidden' id='attachSuffix' name='"+paramAttachs+"["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
                                    + "</div>";
                            $('#'+attachId).append(html);
                        }
                    }
                })              
            }
        }
    })
}
// 删除附件
function del(e){
    $(e).parent('div').remove()
}
// 文件上传 ----- end -----

function saveAftersale(e) {
	var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    if ($("#inputForm").find('input[name="productId"]').val() == null || $("#inputForm").find('input[name="productId"]').val() == ""){
        alert("请重新选择产品");
        return false;
    }
    if ($("#inputForm").valid()) {
	    $.ajax({
	        type:'POST',
	        url:'/aftersales/aftersale/save.jhtml',
	        data:$("#inputForm").serialize(),
	        success:function(data) {
	            if(data.type == 'success'){
	                alert("保存成功");
	            } else {
	                alert("保存失败, " + data.content);
	            }
	        }
	    })
    } else {
        alert("请先输入必填的输入框，再进行提交！");
    }
}

</script>
</head>
<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
	    <!-- <div class="info-box">
	        <div class="title"><b>经销商信息</b><a href="#">查看历史记录</a></div>
	        <div class="cont">
	            <p>省份城市：广东省佛山市顺德区大良街道</p>
	            <p>经销商：张三</p>
	            <p>经销商电话：15626165439</p>
	        </div>
	    </div> -->
	    <div class="info-box">
	        <div class="title"><b>基本信息</b><a href="#">查看历史记录</a></div>
	        <div class="dl-style01">
	            <dl class="readonly">
	                <dt>经销商姓名</dt>
	                <dd>
	                    <input type="text" class="txt" value="${store.dealerName}" readonly="readonly" />
	                    <input type="hidden" name="storeId" value="${store.id}" />
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>省份城市</dt>
	                <dd><input type="text" value="${Provinces}" class="txt" readonly="readonly" /></dd>
	            </dl>
	            <dl>
	                <dt>顾客姓名</dt>
	                <dd><input type="text" name="name" class="txt" /></dd>
	            </dl>
	            <dl>
	                <dt>联系电话</dt>
	                <dd><input type="number" name="contactNumber" class="txt" /></dd>
	            </dl>
	            <dl>
	                <dt>铺设面积</dt>
	                <dd><input type="number" name="layingArea" id="layingArea" class="txt" placeholder="请输入" /> ㎡</dd>
	            </dl>
	            <dl class="address">
	                <dt>铺装地址</dt>
	                <dd>
                        <input type="hidden" name="headNewArea.id" />
                        <div>
                            <select class="txt" id="xProvince" dir="rtl" onchange="initAddr(this, 'xCity')" style="max-width:70px;">
                                <option>请选择</option>
                            </select>
                            <select class="txt" id="xCity" dir="rtl" onchange="initAddr(this, 'xRegion')" style="max-width:70px;">
                                <option>请选择</option>
                            </select>
                            <select class="txt" id="xRegion" dir="rtl" onchange="tailSelect(this)" style="max-width:70px;">
                                <option>请选择</option>
                            </select>
                        </div>
                        <div>
                            <input type="text" name="layingAddress" value=""  class="txt" placeholder="请输入"/>
                        </div>
                    </dd>
	            </dl>
	            <dl>
	                <dt>入住时间</dt>
	                <dd>
	                    <input type="date" name="checkInTime" value="" class="txt time"/>
	                </dd>
	            </dl>
	            <dl>
	                <dt>故障时间</dt>
	                <dd><input type="date" name="downTime" value="" class="txt time"/></dd>
	            </dl>
	            <dl>
	                <dt>铺设时间</dt>
	                <dd><input type="date" name="layingTime" value="" class="txt time"/></dd>
	            </dl>
	            <dl>
	                <dt>铺设方</dt>
	                <dd>
	                    <select name="layingName" class="txt" dir="rtl">
	                        <option></option>
                            <option value="经销商" >${message("经销商")}</option>
                            <option value="客户安装" >${message("客户安装")}</option>
                            <option value="装饰公司" >${message("装饰公司")}</option>
                            <option value="家装公司" >${message("家装公司")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>紧急程度</dt>
	                <dd>
	                    <select name="emergencyDegree" class="txt emergencyDegree" dir="rtl">
	                        <option></option>
                            <option value="非常紧急" >${message("非常紧急")}</option>
                            <option value="紧急" >${message("紧急")}</option>
                            <option value="一般" >${message("一般")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>铺设方式</dt>
	                <dd>
	                    <select name="layingMethod" class="txt layingMethod" dir="rtl">
	                        <option></option>
                            <option value="龙骨法" >${message("龙骨法")}</option>
                            <option value="悬浮法" >${message("悬浮法")}</option>
                            <option value="夹板法" >${message("夹板法")}</option>
                            <option value="其他" >${message("其他")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>铺设防潮</dt>
	                <dd>
	                    <select name="layingMoistureproof" class="txt layingMoistureproof">
	                        <option></option>
                            <option value="双重防潮">${message("双重防潮")}</option>
                            <option value="单重防潮">${message("单重防潮")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>采暖方式</dt>
	                <dd>
	                    <select name="heatingSystem" class="txt heatingSystem">
	                        <option></option>
                            <option value="地暖">地暖</option>
                            <option value="电暖">电暖</option>
                            <option value="壁暖（暖气片）">壁暖(暖气片)</option>
                            <option value="空调（冬季）">空调(冬季)</option>
                        </select>
	                </dd>
	            </dl>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>产品资料</b></div>
	        <div class="dl-style01">
	            <dl>
	                <dt>产品型号</dt>
	                <dd data-id="ProductModel" onclick="showPup(this)">
                        <input type="text" class="txt arrow" placeholder="请选择" name="product_model" id="product_model" value="" disabled />
                        <input type="hidden" name="productId" class="text productId" value="" btn-fun="clear"/>
                    </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>产品品名</dt>
	                <dd><input type="text" readonly name="" class="txt productName" id="productName" value=""/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>产品规格</dt>
	                <dd><input type="text" readonly name="" class="txt productSpec" id="productSpec" value=""/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>地板品类</dt>
	                <dd><input type="text" readonly name="category" class="txt"/></dd>
	            </dl>
	            <dl>
	                <dt>销售单价</dt>
	                <dd><input type="number" name="price" id="price" class="txt" placeholder="请输入" style="width:80%"/> 元/㎡</dd>
	            </dl>
	            <dl>
	                <dt>销售总额</dt>
	                <dd><input type="number" name="totalSales" id="totalSales" class="txt" placeholder="请输入"/> 元</dd>
	            </dl>
	            <dl>
	                <dt>批次号/板底喷码</dt>
	                <dd><input type="text" name="batchNumber" class="txt" placeholder="请输入"/></dd>
	            </dl>
	            <p class="f-red">备注：无批次号报告不做处理。</p>
	        </div>
	    </div>
	    <div style="border-top:solid 1px #eee;" class="info-box">
	        <div class="dl-style01">
	            <p class="f-black">安装环境</p>
	            <!-- <div id="b5" class="mt6">
	                <div><label class="checkhid" data-id="b5" onclick="showChoose(this)"><input type="radio" name="1"><div class="check_box checked"></div>旧房</label>（装修完毕时间<input type="text" value="" class="txt1" style="width: 52px;height: 26px;">年<input type="text" value="" class="txt1" style="width: 40px;height: 26px;">月）</div>
	                <div class="mt6"><label class="checkhid" data-id="b5" onclick="showChoose(this)"><input type="radio" name="1"><div class="check_box "></div>新房</label>（装修完毕时间<input type="text" value="" class="txt1" style="width: 52px;height: 26px;">年<input type="text" value="" class="txt1" style="width: 40px;height: 26px;">月）</div>
	            </div> -->
	            <dl>
	                <dt>新/旧房</dt>
	                <dd>
			            <select name="otherInformation" class="txt otherInformation">
                            <option></option>
                            <option value="新房">${message("新房")}</option>
                            <option value="旧房">${message("旧房")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
                    <dt>装修完毕时间</dt>
                    <dd><input type="date" name="decorationTime" value="" class="txt time"></dd>
                </dl>
	            <dl>
	                <dt>地板含水率</dt>
	                <dd><input type="number" name="floorMoistureContent" class="txt" placeholder="请输入" style="padding-right: 5px;" >%</dd>
	            </dl>
	            <dl>
	                <dt>地面平整度</dt>
	                <dd><input type="text" name="floorFlatness" class="txt" placeholder="请输入"></dd>
	            </dl>
	            <p class="f-black mt8">附件</p>
	            <dl class="upload-bBox">
                    <dt>上传附件<br>现场图片、故障样板</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'aftersaleFile')">上传</a>
                    </dd>
                </dl>
                <div class="atta-list" id="addAftersaleAttach"></div>
	            <p class="f-black mt8">经销商描述故障信息</p>
	            <textarea class="txt tl border mt6" name="storeFaultMessage" placeholder="输入故障描述信息"></textarea>
	            <p class="f-black">客户诉求</p>
	            <div class="chooseBox1">
	                <label class="checkhid" data-id="c"><input type="checkbox" name="storeAppeal" value="全部更换" /><div class="check_box"></div>全部更换</label>
	                <label class="checkhid" data-id="c"><input type="checkbox" name="storeAppeal" value="局部更换" /><div class="check_box"></div>局部更换</label>
	                <label class="checkhid" data-id="c"><input type="checkbox" name="storeAppeal" value="退货" /><div class="check_box"></div>退货</label>
	                <label class="checkhid" data-id="c"><input type="checkbox" name="storeAppeal" value="赔偿" /><div class="check_box"></div>赔偿<input type="number" name="compensationPrice" class="txt1" style="width: 52px;height: 26px;">元</label>
	            </div>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>售后部调查信息</b></div>
	        <div class="dl-style01">
	            <dl>
	                <dt>勘察人</dt>
	                <dd><input type="text" name="surveyorName" class="txt" placeholder="请输入"></dd>
	            </dl>
	            <dl>
	                <dt>勘察人电话</dt>
	                <dd><input type="text" name="surveyorPhone" class="txt" placeholder="请输入"></dd>
	            </dl>
	            <dl>
	                <dt>工厂</dt>
	                <dd>
	                    <!-- 选择框 -->
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>厂长</dt>
	                <dd><input type="text"  class="txt" readonly></dd>
	            </dl>
	            <dl>
	                <dt>售后等级</dt>
	                <dd>
	                    <select name="grade" class="txt">
                            <option></option>
                            <option value="A">${message("A")}</option>
                            <option value="B">${message("B")}</option>
                            <option value="C">${message("C")}</option>
                            <option value="D">${message("D")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>处理状态</dt>
	                <dd>
	                    <select name="processingStatus" class="txt">
                            <option></option>
                            <option value="待勘察">${message("待勘察")}</option>
                            <option value="待观察">${message("待观察")}</option>
                            <option value="待协商">${message("待协商")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>申请是否有效</dt>
	                <dd>
	                    <select name="isEnabled" class="txt">
	                        <option></option>
                            <option value="true">${message("是")}</option>
                            <option value="false">${message("否")}</option>
                        </select>
	                </dd>
	            </dl>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>勘察说明</b></div>
	        <div class="dl-style01">
	            <textarea class="txt tl border" name="surveyorExplain" placeholder="请输入"></textarea>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>勘察现场照片附件</b></div>
	        <div class="dl-style01">
                <dl class="upload-bBox">
                    <dt>上传附件<br></dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'surveyorFile')">上传</a>
                    </dd>
                </dl>
            </div>
            <div class="atta-list" id="addSurveyorAttach"></div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>总部售后调查信息</b></div>
	        <div class="dl-style01">
	            <textarea class="txt tl border" name="surveyInformation" placeholder="请输入"></textarea>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>公司处理方案</b></div>
	        <div class="dl-style01">
	            <textarea class="txt tl border" name="disposeScheme" placeholder="请输入"></textarea>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>处理结果表单附件</b></div>
	        <div class="dl-style01">
                <dl class="upload-bBox">
                    <dt>上传附件<br>售后处理确认单</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'coupleBackFile')">上传</a>
                    </dd>
                </dl>
            </div>
            <div class="atta-list" id="addCoupleBackAttach"></div>
            <hr style="border: 0.5px solid #bbb; margin: 0 10px;" />
	        <div class="dl-style01">
                <dl class="upload-bBox">
                    <dt>上传附件<br>售后协议书</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'agreementFile')">上传</a>
                    </dd>
                </dl>
            </div>
            <div class="atta-list" id="addAgreementAttach"></div>
            <hr style="border: 0.5px solid #bbb; margin: 0 10px;" />
	        <div class="dl-style01">
                <dl class="upload-bBox">
                    <dt>上传附件<br>收据</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'quittanceFile')">上传</a>
                    </dd>
                </dl>
            </div>
            <div class="atta-list" id="addQuittanceAttach"></div>
            <hr style="border: 0.5px solid #bbb; margin: 0 10px;" />
	        <div class="dl-style01">
                <dl class="upload-bBox">
                    <dt>上传附件<br>退货申请单</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'returnsFile')">上传</a>
                    </dd>
                </dl>
            </div>
            <div class="atta-list" id="addReturnsAttach"></div>
	    </div>
    </form>
    
    <!-- 文件上传 -->
    <form id="fileForm">
        <input type="file" name="file" id="aftersaleFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addAftersaleAttach', 'aftersaleAttachs')" style="display: none"/>
        <input type="file" name="file" id="surveyorFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addSurveyorAttach', 'surveyorAttachs')" style="display: none"/>
        <input type="file" name="file" id="coupleBackFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addCoupleBackAttach', 'coupleBackAttachs')" style="display: none"/>
        <input type="file" name="file" id="agreementFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addAgreementAttach', 'agreementAttachs')" style="display: none"/>
        <input type="file" name="file" id="quittanceFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addQuittanceAttach', 'quittanceAttachs')" style="display: none"/>
        <input type="file" name="file" id="returnsFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addReturnsAttach', 'returnsAttachs')" style="display: none"/>
    </form>
    
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" onclick="saveAftersale(this)" class="btn-blue btn"></div>
</div>

<!-- 选择产品 -->
<div class="pup-obox" id="ProductModel">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            <#--<a href="javascript :history.back(-1);" class="go-back js-cancle"></a>-->
            <div class="h-txt">请选择产品</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="产品名称" id="searchByProductName"/>
            <input type="button" class="btn" value="搜索" onclick="searchProduct()" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;">
        
    </ul>
</div>
</body>
</html>