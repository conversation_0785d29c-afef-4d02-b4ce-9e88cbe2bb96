<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>门店减少申请表</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    } else {
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

//保存操作
function save(e){
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    $.ajax({
        type:'POST',
        url:'/shop/decrease/save.jhtml',
        data:$("#inputForm").serialize(),
        success:function(data) {
            if(data.type == 'success'){
                alert("保存成功");
            } else {
                alert("保存失败, " + data.content);
            }
        }
    })
}
</script>
</head>
<body>
<div id="containt">
    <div class="info-box">
        <div class="title"><b>经销商信息</b><a href="#">查看历史记录</a></div>
        <div class="dl-style01">
            <dl class="readonly">
                <dt>经销商授权编码</dt>
                <dd><input type="text" readonly value="${store.grantCode}" class="txt" /></dd>
            </dl>
            <dl class="readonly">
                <dt>经销商名称</dt>
                <dd>
                    <input type="text" readonly value="${store.dealerName}" class="txt" />
                    <input type="hidden" value="${store.id}" name ="storeId" class="text" />
                </dd>
            </dl>
            <dl class="readonly">
                <dt>手机号码</dt>
                <dd><input type="text" readonly value="${store.headPhone}" class="txt" /></dd>
            </dl>
            <dl class="readonly">
                <dt>固定号码</dt>
                <dd><input type="text" readonly value="${store.fixedNumber}" class="txt" /></dd>
            </dl>
            <dl class="readonly">
                <dt>区域</dt>
                <dd><input type="text" readonly value="${store.region}" class="txt" /></dd>
            </dl>
            <dl class="readonly">
                <dt>地区</dt>
                <dd><input type="text" value="${shopInfo.area.fullName + ' ' + shopInfo.address}" class="txt" readonly="readonly" /></dd>
            </dl>
            <dl class="readonly">
                <dt>合伙人名称</dt>
                <dd><input type="text" readonly value="${shopInfo.partnerName}" class="txt" /></dd>
            </dl>
            <dl class="readonly">
                <dt>合伙人电话</dt>
                <dd><input type="text" readonly value="${shopInfo.partnerPhone}" class="txt" /></dd>
            </dl>
        </div>
    </div>
    <div class="info-box mt8">
        <div class="title"><b>门店信息</b></div>
        <div class="dl-style01">
            <dl class="readonly">
                <dt>操作类型</dt>
                <dd>
                    <input type="text" name="operationType" value="减少" class="txt" readonly="readonly"/>
                    <input type="hidden" name="shopInfoId" value="${shopInfo.id}" />
                </dd>
            </dl>
            <dl class="readonly">
                <dt>变更类型</dt>
                <dd><input type="text" name="changeType" value="" class="txt" readonly="readonly"/></dd>
            </dl>
            <dl class="readonly">
                <dt>门店面积</dt>
                <dd><input type="text" class="txt" value="${shopInfo.acreage}" readonly="readonly"/> ㎡</dd>
            </dl>
            <dl class="readonly">
                <dt>门店位置</dt>
                <dd><input type="text" class="txt" value="${shopInfo.positionType}" readonly="readonly" /></dd>
            </dl>
            <dl class="readonly">
                <dt>公司性质</dt>
                <dd></dd>
            </dl>
            <dl class="readonly">
                <dt>门店所有</dt>
                <dd></dd>
            </dl>
            <dl class="readonly">
                <dt>城市行政等级</dt>
                <dd><input type="text" class="txt" value="${shopInfo.administrativeRank}" readonly/></dd>
            </dl>
            <dl>
                <dt>关店日期</dt>
                <dd><input type="date" name="closingTime" class="txt time" value=""/></dd>
            </dl>
            <dl>
                <dt>门店关闭原因</dt>
                <dd><textarea class="txt" name="closeReason" placeholder="请输入"></textarea></dd>
            </dl>
        </div>
    </div>
    
    <div class="info-box mt8">
        <div class="title"><b>区域经理填写</b></div>
        <div class="dl-style01">
            <dl class="readonly">   
                <dt>客户</dt>
                <dd><input type="text" value="${store.name}" class="txt" readonly /></dd>
            </dl>
            <dl class="readonly">
                <dt>经销商加盟时间</dt>
                <dd><input type="text" class="txt" value="${store.activeDate}" readonly="readonly" /></dd>
            </dl>
            <dl class="readonly">
                <dt>经销商学历</dt>
                <dd><input type="text" class="txt" value="${store.dealerGrade}" readonly="readonly"/></dd>
            </dl>
            <dl class="readonly">
                <dt>经销商性别</dt>
                <dd>
                    [#if store.dealerSex == 0]
                    <input type="text" class="txt" value="男" readonly="readonly" />
                    [#elseif store.dealerSex == 1]
                    <input type="text" class="txt" value="女" readonly="readonly" />
                    [/#if]
                </dd>
            </dl>
            <dl class="readonly">
                <dt>总经销商</dt>
                <dd><input type="text" class="txt" value="${store.franchisee}" readonly="readonly"/></dd>
            </dl>
            <dl class="readonly">
                <dt>门店类型</dt>
                <dd>
                    <select name="type" class="txt type" dir="rtl" disabled="disabled">
                        <option></option>
                        <option value="专卖店"[#if shopInfo.type="专卖店"] selected[/#if]>${message("专卖店")}</option>
                        <option value="大家居"[#if shopInfo.type="大家居"] selected[/#if]>${message("大家居")}</option>
                        <option value="一址多名"[#if shopInfo.type="一址多名"] selected[/#if]>${message("一址多名")}</option>
                        <option value="多品类"[#if shopInfo.type="多品类"] selected[/#if]>${message("多品类")}</option>
                        <option value="产品专区"[#if shopInfo.type="产品专区"] selected[/#if]>${message("产品专区")}</option>
                    </select>
                </dd>
            </dl>
            <dl class="readonly">
                <dt>城市行政等级</dt>
                <dd>
                    [#if store.accountTypeCode == 0]<input type="text" value="省级" class="txt" readonly />[/#if]
                    [#if store.accountTypeCode == 1]<input type="text" value="地市级" class="txt" readonly />[/#if]
                    [#if store.accountTypeCode == 2]<input type="text" value="区县级" class="txt" readonly />[/#if]
                    [#if store.accountTypeCode == 3]<input type="text" value="乡镇级" class="txt" readonly />[/#if]
                </dd>
            </dl>
            <dl class="readonly">
                <dt>所属销售平台</dt>
                <dd><input type="text" class="txt" value="${store.salesPlatform.name}" readonly /></dd>
            </dl>
            <dl class="checkbox readonly">
                <dt>所含品牌</dt>
                <dd>
                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="国际出口" disabled="disabled" [#if ib?seq_contains("国际出口") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('国际出口') == true]checked[/#if]"></div>国际出口</label>
                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="木香居地板" disabled="disabled" [#if ib?seq_contains("木香居地板") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('木香居地板') == true]checked[/#if]"></div>木香居地板</label>
                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="大自然工程" disabled="disabled" [#if ib?seq_contains("大自然工程") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('大自然工程') == true]checked[/#if]"></div>大自然工程</label>
                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="nature地板" disabled="disabled" [#if ib?seq_contains("nature地板") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('nature地板') == true]checked[/#if]"></div>nature地板</label>
                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="大自然地板" disabled="disabled" [#if ib?seq_contains("大自然地板") == true]checked[/#if] /><div class="check_box [#if ib?seq_contains('大自然地板') == true]checked[/#if]"></div>大自然地板</label>
                </dd>                                                                                                                                                                              
            </dl>
            <dl class="readonly">
                <dt>所属品牌</dt>
                <dd>
                    <select name="belongBrand" class="txt" disabled="disabled" dir="rtl">
                        <option></option>
                        <option value="国际出口"  [#if shopInfo.belongBrand="国际出口"] selected[/#if]>${message("国际出口")}</option>
                        <option value="木香居地板" [#if shopInfo.belongBrand="木香居地板"] selected[/#if]>${message("木香居地板")}</option>
                        <option value="大自然工程" [#if shopInfo.belongBrand="大自然工程"] selected[/#if]>${message("大自然工程")}</option>
                        <option value="nature地板" [#if shopInfo.belongBrand="nature地板"] selected[/#if]>${message("nature地板")}</option>
                        <option value="大自然地板" [#if shopInfo.belongBrand="大自然地板"] selected[/#if]>${message("大自然地板")}</option>
                    </select>
                </dd>
            </dl>
            <dl class="readonly">
                <dt>销售渠道</dt>
                <dd>
                    <select name="salesChannel" class="txt salesChannel" disabled="disabled">
                        <option></option>
                        <option value="零售"[#if shopInfo.salesChannel="零售"] selected[/#if]>${message("零售")}</option>
                        <option value="工程"[#if shopInfo.salesChannel="工程"] selected[/#if]>${message("工程")}</option>
                        <option value="家装"[#if shopInfo.salesChannel="家装"] selected[/#if]>${message("家装")}</option>
                    </select>
                </dd>
            </dl>
            <dl class="readonly">
                <dt>所属部门</dt>
                <dd><input type="text" class="txt" name="department" value="渠道" readonly="readonly" /></dd>
            </dl>
            <dl class="readonly">
                <dt>VI版本</dt>
                <dd>
                    <select name="viVersion" class="txt viVersion" dir="rtl" disabled="disabled">
                        <option></option>
                        <option value="2013年以前版本"[#if shopInfo.viVersion="2013年以前版本"] selected[/#if]>${message("2013年以前版本")}</option>
                        <option value="2013版本"[#if shopInfo.viVersion="2013版本"] selected[/#if]>${message("2013版本")}</option>
                        <option value="2017版本"[#if shopInfo.viVersion="2017版本"] selected[/#if]>${message("2017版本")}</option>
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>门店关闭原因</dt>
                <dd><textarea class="txt" name="shutDownMenu" placeholder="请输入"></textarea></dd>
            </dl>
        </div>
    </div>
    <div class="info-box mt8">
        <div class="title"><b>渠道部</b></div>
        <div class="dl-style01">
            <dl class="readonly">
                <dt>门店授权编号</dt>
                <dd><input type="text" name="authorizationCode" value="${shopInfo.authorizationCode}" class="txt" readonly /></dd>
            </dl>
            <dl class="readonly">
                <dt>新增档案编号</dt>
                <dd><input type="text" name="increaseArchivesCode" value="${shopInfo.increaseArchivesCode}" class="txt" readonly /></dd>
            </dl>
            <dl class="readonly">
                <dt>新增时间</dt>
                <dd><input type="date" name="newTime" value="${shopInfo.addTime}" class="txt time" readonly /></dd>
            </dl>
            <dl>
                <dt>减少档案编号</dt>
                <dd><input type="text" name="decreaseArchivesCode" class="txt" placeholder="请输入" /></dd>
            </dl>
            <dl>
                <dt>减少时间</dt>
                <dd><input type="date" name="decreaseTime" class="txt time" /></dd>
            </dl>
            <dl>
                <dt>门店情况备注</dt>
                <dd><textarea class="txt" name="shopCaseNote" placeholder="请输入"></textarea></dd>
            </dl>
        </div>
    </div>
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" class="btn-blue btn" onclick="save(this)" /></div>
</div>

</body>
</html>