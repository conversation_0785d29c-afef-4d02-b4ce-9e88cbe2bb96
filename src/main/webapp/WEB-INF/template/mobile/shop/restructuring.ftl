<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>门店停业整顿申请表</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    } else {
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

//保存操作
function save(e){
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    $.ajax({
        type:'POST',
        url:'/shop/restructuring/save.jhtml',
        data:$("#inputForm").serialize(),
        success:function(data) {
            if(data.type == 'success'){
                alert("保存成功");
            } else {
                alert("保存失败, " + data.content);
            }
        }
    })
}

</script>
</head>
<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
	    <div class="info-box">
	        <div class="title"><b>经销商信息</b><a href="#">查看历史记录</a></div>
	        <div class="dl-style01">
	            <dl class="readonly">
	                <dt>经销商授权编码</dt>
	                <dd><input type="text" readonly value="${store.grantCode}" class="txt" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商名称</dt>
	                <dd>
	                    <input type="text" readonly value="${store.dealerName}" class="txt">
	                    <input type="hidden" name ="storeId" value="${store.id}" class="txt" />
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>手机号码</dt>
	                <dd><input type="text" readonly value="${store.headPhone}" class="txt"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>固定号码</dt>
	                <dd><input type="text" readonly value="${store.fixedNumber}" class="txt"/></dd>
	            </dl>
	            <dl class="readonly">
                    <dt>区域</dt>
                    <dd><input type="text" readonly value="${store.region}" class="txt" /></dd>
                </dl>
	            <dl class="readonly">
                    <dt>地区</dt>
	                <dd><input type="text" value="${shopInfo.area.fullName + ' ' + shopInfo.address}" class="txt" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>合伙人名称</dt>
	                <dd><input type="text" readonly value="${shopInfo.partnerName}" class="txt" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>合伙人电话</dt>
	                <dd><input type="text" readonly value="${shopInfo.partnerPhone}" class="txt" /></dd>
	            </dl>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>门店信息</b></div>
	        <div class="dl-style01">
	            <dl class="readonly">
	                <dt>门店授权编号</dt>
	                <dd>
	                    <input type="text" class="txt" value="${shopInfo.authorizationCode}" readonly/>
	                    <input type="hidden" value="${shopInfo.id}" name="shopInfoId"/>
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>建店日期</dt>
	                <dd><input type="text" class="txt" value="${shopInfo.addTime}" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>门店面积</dt>
	                <dd><input type="text" class="txt" value="${shopInfo.acreage}" readonly="readonly"/> ㎡</dd>
	            </dl>
	            <dl class="readonly">
	                <dt>门店地址</dt>
	                <dd><input type="text" class="txt" value="${shopInfo.address}" readonly="readonly" /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>城市行政等级</dt>
	                <dd><input type="text" class="txt" value="${shopInfo.administrativeRank}" readonly/></dd>
	            </dl>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>整改信息</b></div>
	        <div class="dl-style01">
	            <dl>
	                <dt>整顿时间</dt>
	                <dd><input type="date" class="txt time" name="restructuringTime" value="" /></dd>
	            </dl>
	            <dl>
	                <dt>操作类型</dt>
	                <dd>
	                    <select name="operationType" class="txt operationType">
	                        <option value="停业整顿">${message("停业整顿")}</option>
	                    </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>盘查时间</dt>
	                <dd><input type="date" class="txt time" name="inventoryTime" value="" /></dd>
	            </dl>
	            <dl>
	                <dt>渠道部最新动态备注</dt>
	                <dd><textarea class="txt" name="memo" placeholder="请输入"></textarea></dd>
	            </dl>
	            <dl>
	                <dt>盘查结果</dt>
	                <dd><textarea class="txt" name="inventoryResult" placeholder="请输入"></textarea></dd>
	            </dl>
	        </div>
	    </div>
    </form>
    
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" class="btn-blue btn" onclick="save(this)" /></div>
</div>

</body>
</html>