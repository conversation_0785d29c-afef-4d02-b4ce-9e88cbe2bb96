<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>门店列表</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
function changeShow(e){
    $(e).closest("li").siblings().find(".ico").attr("data-status","0")
    if($(e).attr("data-status") == 0){
        $(e).closest("li").find(".cont").hide()
        $(e).parent().find(".cont").show()
        $(e).closest("li").attr("style","z-index:9")
        $(e).closest("li").siblings().attr("style","z-index:0")
        $(e).closest("li").siblings().find(".cont").hide()
        $(e).attr("data-status",1)
    }else{
        $(e).closest("li").attr("style","z-index:0")
        $(e).parent().find(".cont").hide()
        $(e).attr("data-status",0)
    }
}
</script>
</head>
<body>
<div id="containt">
    <div class="search-box">
        <input type="text" class="txt" id="title-name" placeholder="请输入门店名称" />
        <input type="button" class="btn" onclick="search()" value="搜索" />
    </div>
    <div class="store-list">
        <div class="item">
            <div class="tit">${siList[0].store.name}</div>
            <ul>
                [#list siList as item]
                <li>
                    <div class="pic"><a href="#"><img src="/resources/images/mobile/banner.jpg" /></a></div>
                    <p class="name"><a href="#">${item.shopName}</a></p>
                    <p>主营：${item.businessCategory ? replace(',', '、')}</p>
                    <p>${item.address}</p>
                    <a href="/mobile/shop/shop_detail.jhtml?shopInfoId=${item.id}" class="go"></a>
                    <div class="more-opea">
                        <i class="ico" data-status="0" onclick="changeShow(this)"></i>
                        <ul class="cont">
                            <li><a href="/mobile/shop/decrease.jhtml?shopInfoId=${item.id}">门店减少</a></li>
                            <li><a href="/mobile/shop/alteration.jhtml?shopInfoId=${item.id}">门店变更</a></li>
                            <li><a href="/mobile/shop/restructuring.jhtml?shopInfoId=${item.id}">停业整顿</a></li>
                        </ul>
                    </div>
                </li>
                [/#list]
            </ul>
        </div>
    </div>
</div>

</body>
</html>

