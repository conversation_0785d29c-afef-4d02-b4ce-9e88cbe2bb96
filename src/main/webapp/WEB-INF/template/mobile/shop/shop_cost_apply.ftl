<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>门店费用申请</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script src="/resources/js/swiper.min.3.4.2.js" type="text/javascript"></script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
 
input[type="number"] {
    -moz-appearance: textfield;
}
</style>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    }else{
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

$().ready(function() {
	// 表单验证
    /* $("#inputForm").validate({
        rules: {
            costType: "required",
        },
        messages: {
        	costType: "请选择报销事项",
        },
        errorPlacement: function (error, element) { //指定错误信息位置
            error.insertAfter(element.parent().parent()); //将错误信息添加当前元素的父结点后面
        }
    }) */
});

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

function onbank(e) {
	//获取当前光标的位置
         var caret = this.selectionStart;
         //获取当前的value
         var value = this.value;
         //从左边沿到坐标之间的空格数
         var sp =  (value.slice(0, caret).match(/\s/g) || []).length;
         //去掉所有空格
         var nospace = value.replace(/\s/g, '');
         //重新插入空格
         var curVal = this.value = nospace.replace(/\D+/g,"").replace(/(\d{4})/g, "$1 ").trim();
         //从左边沿到原坐标之间的空格数
         var curSp = (curVal.slice(0, caret).match(/\s/g) || []).length;
         //修正光标位置
         this.selectionEnd = this.selectionStart = caret + curSp - sp;
}

//保存操作
function save(e){
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    $.ajax({
        type:'POST',
        url:'/shop/cost/save.jhtml',
        data:$("#inputForm").serialize(),
        success:function(data) {
            if(data.type == 'success'){
                alert("保存成功");
            } else {
                alert("保存失败, " + data.content);
            }
        }
    })
}
</script>
</head>

<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
        <div class="info-box">
            <div class="title"><b>经销商信息</b><a href="#">查看历史记录</a></div>
            <div class="dl-style01">
                <dl class="readonly">
                    <dt>经销商名称</dt>
                    <dd>
                        <input type="text" value="${store.name}" class="txt" readonly="readonly" />
                        <input type="hidden" name="storeId" value="${store.id}" class="txt" />
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>经销商授权编码</dt>
                    <dd><input type="text" value="${store.grantCode}" class="txt" readonly="readonly" /></dd>
                </dl>
                <dl class="readonly">
                    <dt>手机号码</dt>
                    <dd><input type="text" value="${store.headPhone}" class="txt" readonly="readonly" /></dd>
                </dl>
                <dl class="readonly">
                    <dt>地区</dt>
                    <dd>
                        <input type="text" value="${store.headNewArea.fullName}" class="txt" readonly="readonly"/>
                        <input type="hidden" name="headNewArea.id" value="${(store.headNewArea.id)!}" />
                    </dd>
                </dl>
            </div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>报销信息</b></div>
            <div class="dl-style01">
                <dl>
                    <dt>报销事项</dt>
                    <dd>
                        <select name="costType" class="txt">
                            <option value=""></option>
                            <option value="1">107.促销返利HS</option>
                            <option value="2">381.利息收入</option>
                            <option value="3">107.地板利息收入</option>
                            <option value="4">107.地板差价</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt>项目时间</dt>
                    <dd><input type="date" name="newDate" class="txt time" value=""/></dd>
                </dl>
                <dl>
                    <dt>项目金额</dt>
                    <dd><input type="number" name="amount" class="txt" /></dd>
                </dl>
                <dl>
                    <dt>货物所属部门</dt>
                    <dd><input type="text" name="department" class="txt" /></dd>
                </dl>
                <dl>
                    <dt>经销商开户名</dt>
                    <dd><input type="text" name="accountName" class="txt" /></dd>
                </dl>
                <dl>
                    <dt>银行帐号</dt>
                    <dd><input type="text" name="bankNumber" class="txt" maxlength="23" onkeyup="onbank(this)" /></dd>
                </dl>
                <dl>
                    <dt>开户银行</dt>
                    <dd><input type="text" name="openingBank" class="txt" /></dd>
                </dl>
                <dl>
                    <dt>付款方式</dt>
                    <dd class="chooseBox" id="b3">
                        <label class="checkhid" data-id="b3" style="display: inline-block;"><input type="radio" name="payType" value="1"/><div class="check_box"></div>银行</label>&nbsp;&nbsp;
                        <label class="checkhid" data-id="b3" style="display: inline-block;"><input type="radio" name="payType" value="0" checked="checked"/><div class="check_box checked"></div>现金</label>
                    </dd>
                </dl>
            </div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>项目细节</b></div>
            <div class="dl-style01">
                <dl>
                    <dd><textarea class="txt" name="ac" placeholder="请输入"></textarea></dd>
                </dl>
            </div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>业务部门审核</b></div>
            <div class="dl-style01">
                <dl>
                    <dd><textarea class="txt" name="bc" placeholder="请输入"></textarea></dd>
                </dl>
            </div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>财务部门审核</b></div>
            <div class="dl-style01">
                <dl>
                    <dd><textarea class="txt" name="cc" placeholder="请输入"></textarea></dd>
                </dl>
            </div>
        </div>
    </form>
    
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" onclick="save(this)" class="btn-blue btn"/></div>
</div>
</body>
</html>