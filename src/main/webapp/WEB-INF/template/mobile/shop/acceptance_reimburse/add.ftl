<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>装修验收及报销申请表</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script src="/resources/js/swiper.min.3.4.2.js" type="text/javascript"></script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
 
input[type="number"] {
    -moz-appearance: textfield;
}
label.fieldError {
    color: red;
    font-size: 12px;
    display: block;
    text-align: right;
    margin: -5px 3px 0 0;
}
input.er.error {
    border-color: red;
}
input.er.fieldError {
    border-color: red;
}
.red {
    color: red;
}
</style>
<script>
$("label.checkhid").live("click", function () {
	var id = $(this).attr("data-id");
	$(this).siblings().find("input[type=radio]").removeAttr("checked")
	if($(this).find("input").attr("type")=="checkbox"){
		if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
			$(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
			//$(this).find("input[type=checkbox]").val(false)
		} else {
			$(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
			//$(this).find("input[type=checkbox]").val(true)
		}
	}else{
		$(this).siblings().find("input[type=radio]").removeAttr("checked")
		$(this).find("input[type=radio]").attr("checked", true)
		$(this).siblings().find(".check_box").removeClass("checked")
		$(this).find(".check_box").addClass("checked")

		/*if ($(this).find("input[type=radio]:checked").val() == undefined) {
			$(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked");
		} else {
			$($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
			$(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
		}*/
	}
});
var swiper = new Swiper('.upload-slide', {
    nextButton: '.swiper-button-next',
    prevButton: '.swiper-button-prev'
});
function showChoose(e){
    $(e).parent().parent().siblings().find(".dl-style01").hide()
    $(e).parent().parent().find(".dl-style01").show()
    $(e).parent().parent().siblings().find(".dl-style01 input[type=date]").val("");
    $(e).parent().parent().siblings().find(".dl-style01 input[type=number]").val("");
    $(e).parent().parent().siblings().find(".dl-style01 input[type=number]").val("");
    $(e).parent().parent().siblings().find(".dl-style01 select option").prop("selected",'');
}

$().ready(function(){
    // 初始化弹出框：如选择XXX  start  ---------
    $(".js-cancle").click(function() {
        $(".pup-obox").hide();
        $("body").attr("style","overflow:auto");
        $(".info-btns").show();  // 显示提交按钮
    });
})

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

// 弹出框相关 ------ start ------
// 搜索门店
function searchShop() {
    var e = $('#containt').find('dd[data-id="ShopCode"]')[0]
    showPup(e);
}

//搜索门店设计 
function searchDevise() {
    var e = $('#containt').find('dd[data-id="DeviseCode"]')[0]
    showPup(e);
}

function showPup(e){
    var id = $(e).attr("data-id");
    $("body").attr("style","overflow:hidden");
    $(".pup-obox").hide();
    $(".info-btns").hide();  // 隐藏提交按钮
    $("#"+id).show();
    // 选择门店
    if(id == "ShopCode"){
        $('#'+id).find('ul').empty();
        $.ajax({
            type:'POST',
            url:'/shop/shopInfo/select_shopInfo_data.jhtml',
            data:{
            	sn:$('#searchByShopCode').val(),
				multi:1,
				reimburse:2,
				shopStatus:'开店中,正在营业',
				off:true,
			},
            success:function(data) {
                if(data.type == 'success'){
                    var rows = JSON.parse(data.content).content;
					var brands = {"0": "大自然综合店","1": "大自然·三层专卖店","2":"大自然·实木专卖店", "3":"大自然·强化专卖店" }
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        var html = "<li data-id='ShopCode' onclick='selectItem(this)'><a href='#'>"
                                + "<div class='name'>"+ isNull(row.sn) +"</div>"
                                + "<div class='fl'><span>经销商：</span>"+ isNull(row.distributor_name) +"</div>"
                                + "<div class='fr'><span>授权编号：</span>"+ isNull(row.authorization_code) +"</div>"
                                + "</a>"
                                + "<input type='hidden' class='xsShopInfoName' value='"+ isNull(row.shop_name) +"'/>"
                                + "<input type='hidden' class='xsShopInfoId' value='"+ isNull(row.id) +"'/>"
                                + "<input type='hidden' class='xsApplyCity' value='"+ isNull(row.area_name) +"'/>"
                                + "<input type='hidden' class='xsDealer' value='"+ isNull(row.dealer_name) +"'/>" // 经销商
                                + "<input type='hidden' class='xsDistributorPhone' value='"+ isNull(row.distributor_phone) +"'/>"  // 经销商电话
                                + "<input type='hidden' class='xsAcreage' value='"+ isNull(row.acreage) +"'/>"  // 面积
                                + "<input type='hidden' class='xsAddress' value='"+ isNull(row.address) +"'/>"  // 地址
                                + "<input type='hidden' class='xsXujlName' value='"+ isNull(row.xujl_name) +"'/>"  // 区域经理
                                + "<input type='hidden' class='xsXujlMobile' value='"+ isNull(row.xujl_mobile) +"'/>"  // 区域经理电话
                                + "<input type='hidden' class='xsSaleOrgName' value='"+ isNull(row.sale_org_name) +"'/>"  // 机构
                                + "<input type='hidden' class='xsShopName' value='"+ isNull(row.shop_name) +"'/>"  // 店名
								+ "<input type='hidden' class='sjShopDeviseSn' value='"+ isNull(row.devise_sn) +"'/>"//设计单sn
								+ "<input type='hidden' class='sjShopDeviseId' value='"+ isNull(row.devise_id) +"'/>"//设计单id
								+ "<input type='hidden' class='sjDesignBrand' value='"+ isNull(brands[row.design_brand]) +"'/>"  // 设计品牌
								+ "<input type='hidden' class='sjArea' value='"+ isNull(row.area) +"'/>" // 面积
								+ "<input type='hidden' class='sjHighLimit' value='"+ isNull(row.high_limit) +"'/>"  // 限高
								+ "<input type='hidden' class='sjPredictConstructionTime' value='"+ isNull(row.predict_construction_time==null?"":row.predict_construction_time.substring(0,10)) +"'/>"  // 预订施工时间
								+ "<input type='hidden' class='sjPredictStartsTime' value='"+ isNull(row.predict_starts_time==null?"":row.predict_starts_time.substring(0,10)) +"'/>"  // 开业时间
								+ "<input type='hidden' class='sjStructure1' value='"+ isNull(row.structure1) +"'/>"  // 门店结构情况
								+ "<input type='hidden' class='sjStructure2' value='"+ isNull(row.structure2) +"'/>"
								+ "<input type='hidden' class='sjShopAttribute' value='"+ isNull(row.shop_renovation_attribute) +"'/>"  // 装修属性
								+ "</li>";
                        $('#'+id).find('ul').append(html)
                    }
                }
            }
        })
    }
    // 选择门店设计
    if(id == "DeviseCode"){
        $('#'+id).find('ul').empty();
        $.ajax({
            type:'POST',
            url:'/shop/devise/select_shop_devise_data.jhtml',
            data:{sn:$('#searchByDeviseSn').val()},
            success:function(data) {
                if(data.type == 'success'){
                    var rows = JSON.parse(data.content).content;
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        var brand = new Array("大自然综合店","大自然·三层专卖店","大自然·实木专卖店","大自然·强化专卖店");
                        var html = "<li data-id='DeviseCode' onclick='selectItem(this)'><a href='#'>"
                                + "<div class='name'>"+ isNull(row.sn) +"</div>"
                                + "<div class='fl'><span>经销商：</span>"+ isNull(row.dealer) +"</div>"
                                + "<div class='fr'><span>装修属性：</span>"+ isNull(row.shop_attribute) +"</div>"
                                + "</a>"
                                + "<input type='hidden' class='sjShopDeviseSn' value='"+ isNull(row.sn) +"'/>"
                                + "<input type='hidden' class='sjShopDeviseId' value='"+ isNull(row.id) +"'/>"
                                + "<input type='hidden' class='sjDesignBrand' value='"+ isNull(brand[row.design_brand]) +"'/>"  // 设计品牌
                                + "<input type='hidden' class='sjArea' value='"+ isNull(row.area) +"'/>" // 面积
                                + "<input type='hidden' class='sjHighLimit' value='"+ isNull(row.high_limit) +"'/>"  // 限高
                                + "<input type='hidden' class='sjPredictConstructionTime' value='"+ isNull(row.predict_construction_time.substring(0,10)) +"'/>"  // 预订施工时间
                                + "<input type='hidden' class='sjPredictStartsTime' value='"+ isNull(row.predict_starts_time.substring(0,10)) +"'/>"  // 开业时间
                                + "<input type='hidden' class='sjStructure1' value='"+ isNull(row.structure1) +"'/>"  // 门店结构情况
                                + "<input type='hidden' class='sjStructure2' value='"+ isNull(row.structure2) +"'/>" 
                                + "<input type='hidden' class='sjShopAttribute' value='"+ isNull(row.shop_attribute) +"'/>"  // 装修属性
                                + "</li>";
                        $('#'+id).find('ul').append(html)
                    }
                }
            }
        })
    }
}

// 将弹出框选择的数据回显到页面
function selectItem(e){
    var id = $(e).attr("data-id")
    $("body").attr("style", "overflow:auto");
    $(".pup-obox").hide();
    $(".info-btns").show();  // 显示提交按钮
    if(id == "ShopCode"){ // 选择门店
        $('#containt').find('input[name="shopInfoName"]').val($(e).find('input[class="xsShopInfoName"]').val());
        $('#containt').find('input[name="shopInfoId"]').val($(e).find('input[class="xsShopInfoId"]').val());
        $('#containt').find('input[name="city"]').val($(e).find('input[class="xsApplyCity"]').val());
        $('#containt').find('input[name="dealer"]').val($(e).find('input[class="xsDealer"]').val());
        $('#containt').find('input[name="dealerPhone"]').val($(e).find('input[class="xsDistributorPhone"]').val());
        $('#containt').find('input[name="shopAddress"]').val($(e).find('input[class="xsAddress"]').val());
        $('#containt').find('input[name="regionalManager"]').val($(e).find('input[class="xsXujlName"]').val());
        $('#containt').find('input[name="regionalManagerPhone"]').val($(e).find('input[class="xsXujlMobile"]').val());
        $('#containt').find('input[name="saleOrgName"]').val($(e).find('input[class="xsSaleOrgName"]').val());
        $('#containt').find('input[name="shopInfoShopName"]').val($(e).find('input[class="xsShopName"]').val());

		$('#containt').find('input[name="shopDeviseSn"]').val($(e).find('input[class="sjShopDeviseSn"]').val());
		$('#containt').find('input[name="shopDeviseId"]').val($(e).find('input[class="sjShopDeviseId"]').val());
		$('#containt').find('input[id="designBrand"]').val($(e).find('input[class="sjDesignBrand"]').val());
		$('#containt').find('input[id="area"]').val($(e).find('input[class="sjArea"]').val());
		$('#containt').find('input[id="highLimit"]').val($(e).find('input[class="sjHighLimit"]').val());
		$('#containt').find('input[id="predictConstructionTime"]').val($(e).find('input[class="sjPredictConstructionTime"]').val());
		$('#containt').find('input[id="predictStartsTime"]').val($(e).find('input[class="sjPredictStartsTime"]').val());
		$('#containt').find('input[id="structure1"]').val($(e).find('input[class="sjStructure1"]').val());
		$('#containt').find('input[id="structure2"]').val($(e).find('input[class="sjStructure2"]').val());
		$('#containt').find('input[id="shopAttribute"]').val($(e).find('input[class="sjShopAttribute"]').val());

	}
    if(id == "DeviseCode"){ // 选择门店设计
        $('#containt').find('input[name="shopDeviseSn"]').val($(e).find('input[class="sjShopDeviseSn"]').val());
        $('#containt').find('input[name="shopDeviseId"]').val($(e).find('input[class="sjShopDeviseId"]').val());
        $('#containt').find('input[id="designBrand"]').val($(e).find('input[class="sjDesignBrand"]').val());
        $('#containt').find('input[id="area"]').val($(e).find('input[class="sjArea"]').val());
        $('#containt').find('input[id="highLimit"]').val($(e).find('input[class="sjHighLimit"]').val());
        $('#containt').find('input[id="predictConstructionTime"]').val($(e).find('input[class="sjPredictConstructionTime"]').val());
        $('#containt').find('input[id="predictStartsTime"]').val($(e).find('input[class="sjPredictStartsTime"]').val());
        $('#containt').find('input[id="structure1"]').val($(e).find('input[class="sjStructure1"]').val());
        $('#containt').find('input[id="structure2"]').val($(e).find('input[class="sjStructure2"]').val());
        $('#containt').find('input[id="shopAttribute"]').val($(e).find('input[class="sjShopAttribute"]').val());
    }
}
// 弹出框相关 ------ end ------

// 文件上传 ----- start -----
// 添加附件
function addAttach(e, attachFile) {
    $('#fileForm').find('input').removeAttr("name");
    $('#' + attachFile).attr("name", "file");
    $('#' + attachFile).trigger('click'); 
}

/** 
 * 附件上传
 * @param attachId 所在input标签的id名
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param type 后台用来区分不同类型附件，若后台的附件类只存一个类型的附件，则可以不用传该值
 */
function fileUpload(e, attachId, paramAttachs, name,type) {
    var formData = new FormData($("#fileForm")[0]);
    var len = $('#'+attachId).find('div[class="item"]').size()
    $.ajax({
        type:'GET',
        url:'/common/fileurl.jhtml',
        success:function(data) {
            if(data.type == "success"){
                $.ajax({
                    type:'POST',
                    url: data.objx,
                    data:formData,
                    cache: false,  
                    contentType: false,  
                    processData: false, 
                    success:function(data_) {
                        data_ = JSON.parse(data_)
                        if(data_.message.type == "success"){
                            var html = "<div class='item'>"
                                    + "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                                    + "<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
                                    + "<textarea class='txt' placeholder='请输入备注' id='attachMemo' name='"+paramAttachs+"["+len+"].memo'></textarea>"
                                    + "<input type='hidden' id='attachName' name='"+paramAttachs+"["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
                                    + "<input type='hidden' id='attachUrl' name='"+paramAttachs+"["+len+"].url' value='"+data_.url+"'/>"
                                    + "<input type='hidden' id='attachType' name='"+paramAttachs+"["+len+"].type' value='"+parseInt(type)+"'/>"
                                    + "<input type='hidden' id='attachSuffix' name='"+paramAttachs+"["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
                                    + "</div>";
                            $('#'+attachId).append(html);
                        }
                    }
                })              
            }
        }
    })
}
// 删除附件
function del(e) {
    $(e).parent('div').remove();
}
// 文件上传 ----- end -----

// 保存操作
function saveAcceptanceReimburse(e){
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    if($("#inputForm").find('input[name="shopInfoId"]').val() == null || $("#inputForm").find('input[name="shopInfoId"]').val() == ""){
        alert("请重新选择门店");
        return false;
    }
    if($("#inputForm").find('input[name="shopDeviseId"]').val() == null || $("#inputForm").find('input[name="shopDeviseId"]').val() == ""){
        alert("请重新选择门店设计");
        return false;
    }
    $.ajax({
        type:'POST',
        url:'/shop/acceptance_reimburse/save.jhtml',
        data:$("#inputForm").serialize(),
        success:function(data) {
            if(data.type == 'success'){
                alert("保存成功");
				location.href="/mobile/shop/acceptance_reimburse/edit.jhtml?id="+data.objx;
            } else {
                alert("保存失败, " + data.content);
            }
        }
    })
}
</script>
</head>
<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
        <input type="hidden" name="type" value="1" />  <!-- 标记为验收报销 -->
	    <div class="info-box">
	        <div class="title"><b>门店资料</b><a href="/mobile/shop/acceptance_reimburse/list.jhtml">查看历史记录</a></div>
	        <div class="dl-style01">
	            <dl>
	                <dt><span class="red">* </span>门店名称</dt>
	                <dd data-id="ShopCode" onclick="showPup(this)">
                        <input type="text" class="txt arrow" placeholder="请选择" id="shopInfoName" name="shopInfoName" value="" disabled />
                        <input type="hidden" name="shopInfoId" class="text shopInfoId" value="" btn-fun="clear"/>
                    </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>省份城市</dt>
	                <dd><input type="text" name="city" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商（姓名）</dt>
	                <dd><input type="text" name="dealer" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商联系电话</dt>
	                <dd><input type="text" name="dealerPhone" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>区域经理（姓名）</dt>
	                <dd><input type="text" name="regionalManager" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>区域经理联系电话</dt>
	                <dd><input type="text" name="regionalManagerPhone" class="txt" readonly="readonly"/></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>门店详细地址</dt>
	                <dd><input type="text" name="shopAddress" class="txt" readonly="readonly"/></dd>
	            </dl>

                <dl class="readonly">
                    <dt>机构</dt>
                    <dd><input type="text" name="saleOrgName" class="txt" readonly="readonly"/></dd>
                </dl>

<#--                <dl class="readonly">-->
<#--                    <dt>门店名称</dt>-->
<#--                    <dd><input type="text" name="shopInfoShopName" class="txt" readonly="readonly"/></dd>-->
<#--                </dl>-->
	        </div>
	    </div>
	    
	    <div class="info-box mt8">
	        <div class="title"><b>设计资料</b></div>
	        <div class="dl-style01">
	            <dl class="readonly">
	                <dt>设计单号</dt>
	                <dd data-id="DeviseCode" <#--onclick="showPup(this)"-->>
                        <input type="text" class="txt" <#--placeholder="请选择"--> id="shopDeviseSn" name="shopDeviseSn" value="" disabled />
                        <input type="hidden" name="shopDeviseId" class="text shopDeviseId" value="" btn-fun="clear"/>
                    </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>设计品牌</dt>
	                <dd><input type="text" id="designBrand" class="txt" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>面积（㎡）</dt>
	                <dd><input type="text" id="area" class="txt" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>天花限高</dt>
	                <dd><input type="text" id="highLimit" class="txt" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>预计施工时间</dt>
	                <dd><input type="text" id="predictConstructionTime" class="txt" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>预计开业时间</dt>
	                <dd><input type="text" id="predictStartsTime" class="txt" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>门店结构情况</dt>
	                <dd>
	                    <input type="text" id="structure1" class="txt" readonly style="width:60px" />&nbsp;&nbsp;&nbsp;
	                    <input type="text" id="structure2" class="txt" readonly style="width:60px" />
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>门店装修属性</dt>
	                <dd><input type="text" id="shopAttribute" class="txt" readonly /></dd>
	            </dl>
	        </div>
	    </div>
	    

	    
	    <div class="info-box mt8">
	        <div class="title"><b>验收申请承诺</b></div>
	        <div class="article-wrap mt8">
	            <p>1、确定按公司设计图纸严格按图施工；如有与图纸不符，将接受公司的相关裁定。</p>
	            <p>2、确定按照软装、灯具清单购置相关标准配饰物料；如购置不规范，将视为接受直接折扣处理，不做整改。</p>
	            <p>3、确定按设计规范设置辅料产品展示区，如无辅料区，将扣除报销总金额的10%处理，不做整改。</p>
	            <p>4、由于经销商个人原因，要求重复设计的门店，将扣除报销总金额的10%处理。</p>
	            <p>5、确认以下资料提交齐全</p>
	        </div>
	        <div class="line" style="background:#eee;margin: 13px 12px 0;height: 1px;"></div>
	        <div class="chooseBox" id="b1">
	            <label class="checkhid" data-id="b1"><input type="checkbox" name="acceptanceCommitments" value="0" /><div class="check_box"></div>门店平面图核实面积且签字</label>
	            <label class="checkhid" data-id="b1"><input type="checkbox" name="acceptanceCommitments" value="1" /><div class="check_box"></div>提交门店装修合同复印件</label>
	            <label class="checkhid" data-id="b1"><input type="checkbox" name="acceptanceCommitments" value="2" /><div class="check_box"></div>提交门店租赁合同/房产证复印件</label>
	        </div>
<#--	        <div class="dl-style01">-->
<#--	            <dl>-->
<#--	                <dt>租赁期限</dt>-->
<#--	                <dd><input type="number" name="acceptanceLeaseTerm" class="txt" placeholder="请输入"/> 年</dd>-->
<#--	            </dl>-->
<#--	            <dl>-->
<#--	                <dt>合同门店面积</dt>-->
<#--	                <dd><input type="number" name="acceptanceArea" class="txt" placeholder="请输入"/> ㎡</dd>-->
<#--	            </dl>-->
<#--	            <dl class="upload-bBox">-->
<#--                    <dt>上传附件<br>平面图、门店装修合同复印件、<br>门店租赁合同/房产证复印件</dt>-->
<#--                    <dd>-->
<#--                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'acceptanceCommitmentFile')">上传</a>-->
<#--                        <!-- <p class="f-red">只支持.jpg 格式</p> &ndash;&gt;-->
<#--                    </dd>-->
<#--                </dl>-->
<#--	        </div>-->
<#--	        <div class="atta-list" id="addAcceptanceCommitmentAttach"></div>-->

			<div class="dl-style01">
				<dl class="upload-bBox">
					<dt><span class="red">* </span>门店照片</dt>
					<dd>
						<a href="javascript:void(0);" class="a-upload"
						   onclick="addAttach(this, 'acceptanceCommitmentFile')">上传</a>
						<!-- <p class="f-red">只支持.jpg 格式</p> -->
					</dd>
				</dl>
			</div>
			<div class="atta-list" id="addAcceptanceCommitmentAttach"></div>

			<div class="dl-style01">
				<dl class="upload-bBox">
					<dt><span class="red">* </span>租赁合同</dt>
					<dd>
						<a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'storeContractAttach')">上传照片</a>
						<!-- <p class="f-red">只支持.jpg 格式</p> -->
					</dd>
				</dl>
			</div>
			<div class="atta-list" id="addStoreContractAttach" style="padding: 0 10px 0 0px;"></div>

			<div class="dl-style01">
				<dl class="upload-bBox">
					<dt><span class="red">* </span>平面图</dt>
					<dd>
						<a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'storePictureAttach')">上传照片</a>
						<!-- <p class="f-red">只支持.jpg 格式</p> -->
					</dd>
				</dl>
			</div>
			<div class="atta-list" id="addStorePictureAttachs" style="padding: 0 10px 0 0px;"></div>
		</div>

<#--		<div class="info-box mt8">-->
<#--			<div class="title"><b>渠道部意见</b></div>-->
<#--			<div class="dl-style01">-->
<#--				<dl>-->
<#--					<dt>门店授权编号</dt>-->
<#--					<dd><input type="text" name="shopAuthorizationCodes" class="txt" /></dd>-->
<#--				</dl>-->
<#--				<dl>-->
<#--					<dt>新增档案编号</dt>-->
<#--					<dd><input type="text" name="archivesCodes" class="txt" /></dd>-->
<#--				</dl>-->
<#--				<dl>-->
<#--					<dt>新增时间</dt>-->
<#--					<dd><input type="date" name="newTime" class="txt time" value=""/></dd>-->
<#--				</dl>-->
<#--			</div>-->
<#--		</div>-->


<#--	    <div class="info-box mt8">-->
<#--	        <div class="title"><b>验收人员意见（区域经理、设计师）</b></div>-->
<#--	        <div class="dl-style01">-->
<#--	            <dl>-->
<#--	                <dt>经确认，装修施工时间</dt>-->
<#--	                <dd><input type="date" name="decorateConstructionTime" class="txt time" value=""/></dd>-->
<#--	            </dl>-->
<#--	            <dl>-->
<#--	                <dt>经确认，装修完成时间</dt>-->
<#--	                <dd><input type="date" name="decorateCompleteTime" class="txt time" value=""/></dd>-->
<#--	            </dl>-->
<#--	            <dl>-->
<#--	                <dt>经确认，门店装修属性</dt>-->
<#--	                <dd>-->
<#--	                    <select name="acceptanceShop" class="txt" dir="rtl">-->
<#--	                        <option></option>-->
<#--	                        <option value="重装店">重装店</option>-->
<#--	                        <option value="新店">新店</option>-->
<#--	                    </select>-->
<#--	                </dd>-->
<#--	            </dl>-->
<#--	            <dl>-->
<#--	                <dt>经确认，套内有效面积</dt>-->
<#--	                <dd><input type="number" name="acceptanceVerifyArea" class="txt" placeholder="请输入" style="width: 85%"/> ㎡</dd>-->
<#--	            </dl>-->
<#--	            <dl>-->
<#--	                <dt>经审核，评分表得分为</dt>-->
<#--	                <dd><input type="number" name="acceptanceVerifyScore" class="txt" placeholder="请输入" style="width: 85%"/> 分</dd>-->
<#--	            </dl>-->
<#--	        </div>-->
<#--	        <p class="f-black" style="margin-left:12px;">上传附件<span class="f-gray" style="font-size: 0.75rem;">（每个最少上传4张）</span></p>-->
<#--	        <div class="dl-style01">-->
<#--	            <dl class="upload-bBox">-->
<#--                    <dt>门头+外墙、形象墙/植物墙、<br/>外墙材料高清照/收银区</dt>-->
<#--                    <dd>-->
<#--                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'decorate1File')">上传</a>-->
<#--                    </dd>-->
<#--                </dl>-->
<#--            </div>-->
<#--            <div class="atta-list" id="addDecorate1Attach"></div>-->
<#--            <hr style="border: 0.5px solid #bbb; margin: 0 10px;" />-->
<#--            <div class="dl-style01">-->
<#--                <dl class="upload-bBox">-->
<#--                    <dt>野生实木区两张、<br/>实木复合展示区两张</dt>-->
<#--                    <dd>-->
<#--                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'decorate2File')">上传</a>-->
<#--                    </dd>-->
<#--                </dl>-->
<#--            </div>-->
<#--            <div class="atta-list" id="addDecorate2Attach"></div>-->
<#--            <hr style="border: 0.5px solid #bbb; margin: 0 10px;" />-->
<#--            <div class="dl-style01">-->
<#--                <dl class="upload-bBox">-->
<#--                    <dt>康德展示区、4+2展示区、<br/>1530专区、进口三层区</dt>-->
<#--                    <dd>-->
<#--                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'decorate3File')">上传</a>-->
<#--                    </dd>-->
<#--                </dl>-->
<#--            </div>-->
<#--            <div class="atta-list" id="addDecorate3Attach"></div>-->
<#--            <hr style="border: 0.5px solid #bbb; margin: 0 10px;" />-->
<#--            <div class="dl-style01">-->
<#--                <dl class="upload-bBox">-->
<#--                    <dt>强化地板欧式展示区两张、<br/>强化地板中式简约区两张</dt>-->
<#--                    <dd>-->
<#--                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'decorate4File')">上传</a>-->
<#--                    </dd>-->
<#--                </dl>-->
<#--            </div>-->
<#--            <div class="atta-list" id="addDecorate4Attach"></div>-->
<#--            <hr style="border: 0.5px solid #bbb; margin: 0 10px;" />-->
<#--            <div class="dl-style01">-->
<#--                <dl class="upload-bBox">-->
<#--                    <dt>橡木区/柚木区/其他、五红水吧文化区、<br/>辅料区、验收人合影照</dt>-->
<#--                    <dd>-->
<#--                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'decorate5File')">上传</a>-->
<#--                    </dd>-->
<#--                </dl>-->
<#--            </div>-->
<#--            <div class="atta-list" id="addDecorate5Attach"></div>-->
<#--	    </div>-->
<#--	    <div class="info-box mt8">-->
<#--	        <div class="title"><b>省运营管理中心总经理意见</b></div>-->
<#--	        <div class="dl-style01">-->
<#--	            <textarea class="txt tl border mt6" name="provincialOperationMemo" placeholder="请输入"></textarea>-->
<#--	        </div>-->
<#--	    </div>-->
<#--	    <div class="info-box mt8" style="padding-bottom: 10px;" id="b4">-->
<#--	        <div class="title"><b>终端设计经理意见</b></div>-->
<#--	        <div>-->
<#--	            <div class="chooseBox">-->
<#--	                <label class="checkhid" data-id="b4" onclick="showChoose(this)"><input type="radio" name="designManagers" value="4"><div class="check_box"></div>经审核，合格</label>-->
<#--	            </div>-->
<#--	            <div class="dl-style01" style="padding:0 12px; display:none;">-->
<#--	                <dl>-->
<#--	                    <dt>经确认，施工图完成时间</dt>-->
<#--	                    <dd><input type="date" name="constructionFigureCompleteTime" class="txt time" value=""/></dd>-->
<#--	                </dl>-->
<#--	                <dl>-->
<#--	                    <dt>经核实，套内有效营业面积</dt>-->
<#--	                    <dd><input type="number" name="designManagerArea" class="txt" placeholder="请输入" style="width: 27%"/> ㎡,验收合格</dd>-->
<#--	                </dl>-->
<#--	                <dl>-->
<#--	                    <dt>经审核，得分为</dt>-->
<#--	                    <dd><input type="number" name="designManagerScore" class="txt" placeholder="请输入" style="width: 45%"/> 分</dd>-->
<#--	                </dl>-->
<#--	            </div>-->
<#--	        </div>-->
<#--	        <div>-->
<#--	            <div class="chooseBox mt0">-->
<#--	                <label class="checkhid" data-id="b4" onclick="showChoose(this)"><input type="radio" name="designManagers" value="3"/><div class="check_box"></div>经审核，不合格，备注</label>-->
<#--	            </div>-->
<#--	            <div class="dl-style01" style="display: none;padding:0 12px">-->
<#--	                <textarea class="txt tl border mt6" name="designManagerMemo" placeholder="请输入"></textarea>-->
<#--	            </div>-->
<#--	        </div>-->
<#--	    </div>-->
<#--	    <div class="info-box mt8" style="padding-bottom: 10px;" id="b5">-->
<#--	        <div class="title"><b>渠道总监意见</b></div>-->
<#--	        <div>-->
<#--	            <div class="chooseBox">-->
<#--	                <label class="checkhid" data-id="b5" onclick="showChoose(this)"><input type="radio" name="directorOpinions" value="4"/><div class="check_box"></div>同意报销</label>-->
<#--	            </div>-->
<#--	            <div class="dl-style01" style="padding:0 12px; display:none;">-->
<#--	                <dl>-->
<#--	                    <dt>2年内未参与装修报销</dt>-->
<#--	                    <dd>-->
<#--	                        <select name="directorOpinions" class="txt">-->
<#--	                            <option></option>-->
<#--	                            <option value="0">是</option>-->
<#--	                            <option value="">否</option>-->
<#--	                        </select>-->
<#--	                    </dd>-->
<#--	                </dl>-->
<#--	                <dl>-->
<#--	                    <dt>签署经销合同</dt>-->
<#--	                    <dd>-->
<#--                            <select name="directorOpinions" class="txt">-->
<#--                                <option></option>-->
<#--                                <option value="1">是</option>-->
<#--                                <option value="">否</option>-->
<#--                            </select>-->
<#--                        </dd>-->
<#--	                </dl>-->
<#--	                <dl>-->
<#--	                    <dt>缴纳齐全品牌保证金</dt>-->
<#--	                    <dd>-->
<#--                            <select name="payDeposit" class="txt">-->
<#--                                <option></option>-->
<#--                                <option value="1">是</option>-->
<#--                                <option value="0">否</option>-->
<#--                            </select>-->
<#--                        </dd>-->
<#--	                </dl>-->
<#--	                <dl>-->
<#--	                    <dt>经审核，报销标准</dt>-->
<#--	                    <dd><input type="number" class="txt" name="directorOpinionMoney1" placeholder="请输入" style="width:35%"/> 元/㎡</dd>-->
<#--	                </dl>-->
<#--	                <dl>-->
<#--	                    <dt>报销金额</dt>-->
<#--	                    <dd><input type="number" class="txt" name="directorOpinionMoney2" placeholder="请输入" style="width:80%"/> 元</dd>-->
<#--	                </dl>-->
<#--	                <dl>-->
<#--	                    <dt>扣除品牌保证金</dt>-->
<#--	                    <dd><input type="number" class="txt" name="directorOpinionMoney3" placeholder="请输入" style="width:80%"/> 元</dd>-->
<#--	                </dl>-->
<#--	            </div>-->
<#--	        </div>-->
<#--	        <div>-->
<#--	            <div class="chooseBox mt0">-->
<#--	                <label class="checkhid" data-id="b5"onclick="showChoose(this)"><input type="radio" name="directorOpinions" value="3" /><div class="check_box"></div>不同意报销，备注</label>-->
<#--	            </div>-->
<#--	            <div class="dl-style01" style="display: none;padding:0 12px">-->
<#--	                <textarea class="txt tl border mt6" name="directorOpinionMemo" placeholder="请输入"></textarea>-->
<#--	            </div>-->
<#--	        </div>-->
<#--	    </div>-->
<#--	    <div class="info-box mt8 clear">-->
<#--	        <div class="title"><b>地板事业部总经理意见</b></div>-->
<#--	        <div class="chooseBox" id="b12" style="padding-bottom:14px">-->
<#--	            <label class="checkhid" data-id="b12"><input type="radio" name="floorManagerOpinion" value="1" /><div class="check_box"></div>同意报销</label>-->
<#--	            <label class="checkhid" data-id="b12"><input type="radio" name="floorManagerOpinion" value="0" /><div class="check_box"></div>不同意报销</label>-->
<#--	        </div>-->
<#--	    </div>-->
    </form>
    
    <!-- 文件上传 -->
    <form id="fileForm">
        <input type="file" name="file" id="acceptanceCommitmentFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addAcceptanceCommitmentAttach', 'acceptanceCommitmentAttachs', '门店照片',0)" style="display: none"/>
        <input type="file" name="file" id="storeContractAttach" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addStoreContractAttach', 'storeContractAttachs', '租赁合同',1)" style="display: none"/>
        <input type="file" name="file" id="storePictureAttach" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addStorePictureAttachs', 'storePictureAttachs', '平面图',2)" style="display: none"/>

        <input type="file" name="file" id="decorate1File" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addDecorate1Attach', 'decorate1Attachs', '11')" style="display: none"/>
        <input type="file" name="file" id="decorate2File" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addDecorate2Attach', 'decorate2Attachs', '12')" style="display: none"/>
        <input type="file" name="file" id="decorate3File" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addDecorate3Attach', 'decorate3Attachs', '13')" style="display: none"/>
        <input type="file" name="file" id="decorate4File" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addDecorate4Attach', 'decorate4Attachs', '14')" style="display: none"/>
        <input type="file" name="file" id="decorate5File" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addDecorate5Attach', 'decorate5Attachs', '15')" style="display: none"/>
    </form>
    
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" onclick="saveAcceptanceReimburse(this)" class="btn-blue btn"/></div>
</div>

<!-- 选择门店（编码） -->
<div class="pup-obox" id="ShopCode">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            <#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>-->
            <div class="h-txt">请选择门店</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="门店编码" id="searchByShopCode"/>
            <input type="button" class="btn" value="搜索" onclick="searchShop()" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>

<!-- 选择门店设计 -->
<div class="pup-obox" id="DeviseCode">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            <#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>-->
            <div class="h-txt">请选择门店</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="单号" id="searchByDeviseSn"/>
            <input type="button" class="btn" value="搜索" onclick="searchDevise()" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
</body>
</html>