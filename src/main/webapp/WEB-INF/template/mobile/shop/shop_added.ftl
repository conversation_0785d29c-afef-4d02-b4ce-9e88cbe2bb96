<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>门店新增申请表</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<style type="">
label.error {
    color: red;
    font-size: 12px;
    display: block;
    text-align: right;
    margin: -5px 3px 0 0;
}
</style>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    }else{
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

$().ready(function(){
    $(".js-cancle").click(function(){
        $(".pup-obox").hide()
        $("body").attr("style","overflow:auto")
        $(".info-btns").show();  // 显示提交按钮
    })
    
    // 表单验证
    $("#inputForm").validate({
    	rules: {
    		partnerPhone: {
    			minlength: 11,
    			maxlength: 11,
    			digits: true
    		},
    		createShopDate: "required",
    		shopAcreage: "required",
    		businessCategory: "required",
    		shopLocation: "required",
    		companyNature: "required",
    		shopOwnership: "required",
    		isParticipateDesign: "required",
    	},
    	messages: {
    		partnerPhone: {
                digits: "合伙人手机号码只能是数字",
                minlength: "合伙人手机号码长度不能小于11位",
                maxlength: "合伙人手机号码长度不能大于11位"
            },
            createShopDate: "请选择建店日期",
            shopAcreage: "请输入门店面积",
            businessCategory: "请选择经营品类",
            shopLocation: "请选择门店位置",
            companyNature: "请选择公司性质",
            shopOwnership: "请选择门店所有",
            isParticipateDesign: "请选择是否参与门店设计",
    	},
    	errorPlacement: function (error, element) { //指定错误信息位置
    		error.insertAfter(element.parent().parent()); //将错误信息添加当前元素的父结点后面
        }
    })
    
    // 初始化地址
    initAddress('xProvince');
})

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

// 根据客户姓名搜索客户
function searchCustomer() {
    var e = $('#containt').find('dd[data-id="CustomerBox"]')[0]
    showPup(e);
}

function showPup(e){
    var id = $(e).attr("data-id")
    $("body").attr("style","overflow:hidden")
    $(".pup-obox").hide()
    $(".info-btns").hide();  // 隐藏提交按钮
    $("#"+id).show()
    //选择客户
    if(id == "CustomerBox"){
        $('#'+id).find('ul').empty()
        $.ajax({
            type:'POST',
            url:'/member/store/select_store_data.jhtml',
            data:{name:$('#searchByCustomerName').val()},
            success:function(data) {
                if(data.type == 'success'){
                    var rows = JSON.parse(data.content).content;
                    var sex = new Array("男", "女");
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        var html = "<li data-id='CustomerBox' onclick='selectItem(this)'><a href='#'>"
		                        + "<div class='name'>"+ isNull(row.name) +"</div>"
		                        + "<div class='fl'><span>机构：</span>"+ isNull(row.sale_org_name) +"</div>"
		                        + "<div class='fr'><span>编码：</span>"+ isNull(row.out_trade_no) +"</div>"
		                        + "</a>"
		                        + "<input type='hidden' class='xsName' value='"+ isNull(row.name) +"'/>"
		                        + "<input type='hidden' class='xsDealerName' value='"+ isNull(row.dealer_name) +"'/>"
		                        + "<input type='hidden' class='xsStoreId' value='"+ isNull(row.id) +"'/>"
		                        + "<input type='hidden' class='xsSaleOrgId' value='"+ isNull(row.sale_org_id) +"'/>"
		                        + "<input type='hidden' class='xsHeadAddress' value='"+ isNull(row.head_address) +"'/>"
		                        + "<input type='hidden' class='xsSaleOrgName' value='"+ isNull(row.sale_org_name) +"'/>"
		                        + "<input type='hidden' class='xsGrantCode' value='"+ isNull(row.grant_code) +"'/>"  // 经销商授权编码
		                        + "<input type='hidden' class='xsHeadPhone' value='"+ isNull(row.head_phone) +"'/>"  // 手机号
		                        + "<input type='hidden' class='xsFixedNumber' value='"+ isNull(row.fixed_number) +"'/>"  // 固定号码
		                        + "<input type='hidden' class='xsFranchisee' value='"+ isNull(row.franchisee) +"'/>"  // 总经销商
		                        + "<input type='hidden' class='xsSaleOrgName' value='"+ isNull(row.sale_org_name) +"'/>"  // 所属销售平台
		                        + "<input type='hidden' class='xsDealerGrade' value='"+ isNull(row.dealer_grade) +"'/>"  // 经销商学历
		                        + "<input type='hidden' class='xsDealerSex' value='"+ isNull(sex[row.dealer_sex]) +"'/>"  // 经销商性别
		                        + "<input type='hidden' class='xsActiveDate' value='"+ isNull(row.active_date) +"'/>"  // 加盟时间
		                        + "</li>";
                        $('#'+id).find('ul').append(html)
                    }
                }
            }
        })
    }
}

// 将弹出框选择的数据回显到页面
function selectItem(e){
    var id = $(e).attr("data-id")
    $("body").attr("style", "overflow:auto");
    $(".pup-obox").hide();
    $(".info-btns").show();  // 显示提交按钮
    if(id == "CustomerBox"){ //选择客户
        $('#containt').find('input[id="grantCode"]').val($(e).find('input[class="xsGrantCode"]').val());
        $('#containt').find('input[name="storeId"]').val($(e).find('input[class="xsStoreId"]').val());
        $('#containt').find('input[id="storeName"]').val($(e).find('input[class="xsDealerName"]').val());
        $('#containt').find('input[id="headAddress"]').val($(e).find('input[class="xsHeadAddress"]').val());
        $('#containt').find('input[id="headPhone"]').val($(e).find('input[class="xsHeadPhone"]').val());
        $('#containt').find('input[id="fixedNumber"]').val($(e).find('input[class="xsFixedNumber"]').val());
        $('#containt').find('input[id="storeNames"]').val($(e).find('input[class="xsName"]').val());
        $('#containt').find('input[id="franchisee"]').val($(e).find('input[class="xsFranchisee"]').val());
        $('#containt').find('input[id="salesPlatform"]').val($(e).find('input[class="xsSaleOrgName"]').val());
        $('#containt').find('input[id="dealerSex"]').val($(e).find('input[class="xsDealerSex"]').val());
        $('#containt').find('input[id="dealerGrade"]').val($(e).find('input[class="xsDealerGrade"]').val());
        $('#containt').find('input[id="activeDate"]').val($(e).find('input[class="xsActiveDate"]').val());
    }
}

// 文件上传 ----- start -----
// 添加附件
function addAttach(e, attachFile) {
	$('#fileForm').find('input').removeAttr("name");
	$('#' + attachFile).attr("name", "file");
	$('#' + attachFile).trigger('click'); 
}

/** 
 * 附件上传
 * @param attachId 所在input标签的id名
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param type 后台用来区分不同类型附件
 */
function fileUpload(e, attachId, paramAttachs, type) {
	var formData = new FormData($("#fileForm")[0]);
	var len = $('#'+attachId).find('div[class="item"]').size()
	$.ajax({
	    type:'GET',
	    url:'/common/fileurl.jhtml',
	    success:function(data) {
	        if(data.type == "success"){
	            $.ajax({
	                type:'POST',
	                url: data.objx,
	                data:formData,
	                cache: false,  
	                contentType: false,  
	                processData: false, 
	                success:function(data_) {
	                    data_ = JSON.parse(data_)
	                    if(data_.message.type == "success"){
	                        var html = "<div class='item'>"
	                                + "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
	                                + "<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
	                                + "<textarea class='txt' placeholder='请输入备注' id='attachMemo' name='"+paramAttachs+"["+len+"].memo'></textarea>"
	                                + "<input type='hidden' id='attachName' name='"+paramAttachs+"["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
	                                + "<input type='hidden' id='attachUrl' name='"+paramAttachs+"["+len+"].url' value='"+data_.url+"'/>"
	                                /* + "<input type='hidden' id='attachType' name='"+paramAttachs+"["+len+"].type' value='"+parseInt(type)+"'/>" */
	                                + "<input type='hidden' id='attachSuffix' name='"+paramAttachs+"["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
	                                + "</div>";
	                        $('#'+attachId).append(html);
	                    }
	                }
	            })              
	        }
	    }
	})
}

// 删除附件
function del(e) {
    $(e).parent('div').remove()
}
// 文件上传 ----- end -----

// 选中设计时，渠道的字段不能填，无需设计时必须填写 placeholder="请输入"
function handleShowCode(e) {
	var a = $(".isParticipateDesign option:selected").val();
    var sa = $("input[name='shopAuthorizationCodes']");
    var ac = $("input[name='archivesCodes']");
    var nt = $("input[name='newTime']");
    if (a == "true") {
    	sa.val("");
    	ac.val("");
    	nt.val("");
        sa.attr("readonly","readonly");
        sa.removeAttr("placeholder");
        ac.attr("readonly","readonly");
        ac.removeAttr("placeholder");
        nt.attr("disabled","disabled");
        sa.parent().parent().addClass("readonly");
        ac.parent().parent().addClass("readonly");
        nt.parent().parent().addClass("readonly");
    } else {
        sa.removeAttr("readonly");
        sa.attr("placeholder","请输入");
        ac.removeAttr("readonly");
        ac.attr("placeholder","请输入");
        nt.removeAttr("disabled");
        sa.parent().parent().removeClass("readonly");
        ac.parent().parent().removeClass("readonly");
        nt.parent().parent().removeClass("readonly");
    }
}

// ------ 地址 start ------

// 初始化地址（初始化省）
function initAddress(idVal) {
	var data = {locale:"ChinaCN"};
	fillAddr(data, idVal);
}

/**
 * @param nextId 要初始化的下一级select所在的id值
 */
function initAddr(e, nextId) {
    var pid = $(e).find('option:selected').attr('id-val');
    var data = {locale:"ChinaCN", parentId:pid};
    $(e).parent().prev('input').val(pid);
    // 先清除旧的数据
    $(e).nextAll().html('<option>请选择</option>');
    if (pid == null || pid == '' || pid == 'undefined') {
    	// 如果选中的是‘请选择’则修改地址的id为上一级选中的地区id
    	if ($(e).index('select') == 1) {
    		var idv = $(e).prev().find('option:selected').attr('id-val');
    		$(e).parent().prev('input').val(idv);
    	}
    	return ;
    }
    // 填充新的数据
    fillAddr(data, nextId);
}

// 填充数据
function fillAddr(data, idVal) {
	$.ajax({
        type:'GET',
        url: "/member/index/area.jhtml",
        data: data,
        success:function(dataMap) {
            var optionHtml = "<option>请选择</option>";
            for (var key in dataMap) {
                optionHtml += "<option id-val='"+ key +"'>"+ dataMap[key] +"</option>";
            }
            $('#' + idVal).html(optionHtml);
        }
    })
}

function tailSelect(e) {
	var pid = $(e).find('option:selected').attr('id-val');
	$(e).parent().prev('input').val(pid);
	if (pid == null || pid == '' || pid == 'undefined') {
        // 如果选中的是‘请选择’则修改地址的id为上一级选中的地区id
        var idv = $(e).prev().find('option:selected').attr('id-val');
        $(e).parent().prev('input').val(idv);
    }
}

//------ 地址 end ------

//保存操作
function saveShopAdd(){
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    if ($("#inputForm").find('input[name="storeId"]').val() == null || $("#inputForm").find('input[name="storeId"]').val() == ""){
        alert("请重新选择经销商授权编码");
        return false;
    }
    if ($("#inputForm").valid()) {
        $.ajax({
            type:'POST',
            url:'/shop/added/save.jhtml',
            data:$("#inputForm").serialize(),
            success:function(data) {
                if(data.type == 'success'){
                    alert("保存成功");
                } else {
                    alert("保存失败, " + data.content);
                }
            }
        })
    } else {
        alert("请先输入必填的输入框，再进行提交！");
    }
}
</script>
</head>
<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
	    <div class="info-box">
	        <div class="title"><b>经销商信息</b><a href="#">查看历史记录</a></div>
	        <div class="dl-style01">
	            <dl>
	                <dt>经销商授权编码</dt>
	                [#if isMember == 1]
	                <dd class="readonly">
                        <input type="text" class="txt arrow" id="grantCode" name="" value="${store.grantCode}" disabled />
                        <input type="hidden" name="storeId" class="text storeId" value="${store.id}" />
                    </dd>
                    [#else]
                    <dd data-id="CustomerBox" onclick="showPup(this)">
                        <input type="text" class="txt arrow" placeholder="请选择" id="grantCode" name="" value="${store.grantCode}" disabled />
                        <input type="hidden" name="storeId" class="text storeId" value="${store.id}" />
                    </dd>
                    [/#if]
	                
	            </dl>
	            <dl class="readonly">
	                <dt>经销商姓名</dt>
	                <dd>
	                    <input type="text" id="storeName" value="${store.dealerName}" class="txt" readonly />
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>手机号码</dt>
	                <dd><input type="text" value="${store.headPhone}" id="headPhone" class="txt" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>固定号码</dt>
	                <dd><input type="text" value="${store.fixedNumber}" id="fixedNumber" class="txt" readonly /></dd>
	            </dl>
	            <dl class="readonly">
                    <dt>经销商地址</dt>
                    <dd><input type="text" value="${store.headAddress}" id="headAddress" class="txt" readonly /></dd>
                </dl>
	            <dl>
	                <dt>合伙人名称</dt>
	                <dd><input type="text" name="partnerName" value="" class="txt" /></dd>
	            </dl>
	            <dl>
	                <dt>合伙人手机号码</dt>
	                <dd><input type="number" name="partnerPhone" value="" class="txt" /></dd>
	            </dl>
	        </div>
	    </div>
	    <div class="info-box mt8">
	        <div class="title"><b>门店信息</b></div>
	        <div class="dl-style01">
	            <!-- <dl>
	                <dt>操作类型</dt>
	                <dd><select class="txt"><option>新增</option></select></dd>
	            </dl>
	            <dl>
	                <dt>变更类型</dt>
	                <dd><select class="txt"><option>搬迁</option></select></dd>
	            </dl> -->
	            <dl>
	                <dt>建店日期</dt>
	                <dd><input type="date" name="createShopDate" class="txt time" value="" /></dd>
	            </dl>
	            <dl>
	                <dt>门店面积</dt>
	                <dd><input type="number" name="shopAcreage" class="txt" value="" /> ㎡</dd>
	            </dl>
	            <dl class="address">
	                <dt>新店地址</dt>
	                <dd>
	                    <input type="hidden" name="newShopArea.id" />
	                    <div>
	                        <select class="txt" id="xProvince" dir="rtl" onchange="initAddr(this, 'xCity')" style="max-width:70px;">
	                            <option>请选择</option>
	                        </select>
	                        <select class="txt" id="xCity" dir="rtl" onchange="initAddr(this, 'xRegion')" style="max-width:70px;">
	                            <option>请选择</option>
	                        </select>
	                        <select class="txt" id="xRegion" dir="rtl" onchange="tailSelect(this)" style="max-width:70px;">
	                            <option>请选择</option>
	                        </select>
	                    </div>
	                    <div>
	                        <input type="text" name="newShopAddress" value=""  class="txt" placeholder="请输入"/>
	                    </div>
	                </dd>
	            </dl>
	            <dl class="checkbox">
	                <dt>经营品类</dt>
	                <dd>
	                    [#list BusinessCategory as bc]
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="businessCategory" value="${bc.value}" /><div class="check_box "></div>${message("${bc.value}")}</label>
                        [/#list]
	                </dd>
	            </dl>
	            <dl>
	                <dt>门店位置</dt>
	                <dd>
	                    <select name="shopLocation" class="txt shopLocation">
	                        <option></option>
                            <option value="建材市场">建材市场</option>
                            <option value="临街商铺">临街商铺</option>
                            <option value="家具商城">家具商城</option>
                            <option value="装饰公司">装饰公司</option>
	                    </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>公司性质</dt>
	                <dd>
	                    <select name="companyNature" class="txt companyNature" dir="rtl">
                            <option></option>
                            <option value="独立公司">独立公司</option>
                            <option value="合伙公司">合伙公司</option>
                            <option value="个体工商户">个体工商户</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>门店所有</dt>
	                <dd>
	                    <select name="shopOwnership" class="txt shopOwnership" dir="rtl">
                            <option></option>
                            <option value="租赁">租赁</option>
                            <option value="个人物业">个体工商户</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>是否参与门店设计</dt>
	                <dd>
	                    <select name="isParticipateDesign" class="txt isParticipateDesign" onchange="handleShowCode(this)">
                            <option></option>
                            <option value="true">是</option>
                            <option value="false">否</option>
                        </select>
	                </dd>
	            </dl>
	            <div class="tips-box">温馨提醒：若需总部设计，新增流程审批后的7个工作日内需提交门店设计申请，逾期无法提交</div>
	            <dl class="upload-bBox">
	                <dt>上传附件/门店照片<br/>门头远近距离照<br/>收银台、各样板区</dt>
	                <dd>
	                    <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'shopFile')">上传照片</a>
	                    <!-- <p class="f-red">只支持.jpg 格式</p> -->
	                </dd>
	            </dl>
	            <div class="atta-list" id="addShopAttach"></div>
	        </div>
	    </div>
	    
	    <div class="info-box mt8">
	        <div class="title"><b>区域经理填写</b></div>
    	        <div class="dl-style01">
	            <dl class="readonly">
	                <dt>客户</dt>
	                <dd><input type="text" class="txt" id="storeNames" value="${store.name}" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商加盟时间</dt>
	                <dd>
	                    <!-- <input type="date" class="txt time" value="${store.activeDate}" readonly /> -->
	                    <input type="text" class="txt activeDate" id="activeDate" value="${store.activeDate}" readonly />
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商学历</dt>
	                <dd><input type="text" class="txt" id="dealerGrade" placeholder="" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>经销商性别</dt>
	                <dd><input type="text" class="txt" id="dealerSex" placeholder="" readonly /></dd>
	            </dl>
	            <dl class="readonly">
	                <dt>总经销商</dt>
	                <dd><input type="text" class="txt franchisee" id="franchisee" value="${store.franchisee}" placeholder="" readonly /></dd>
	            </dl>
	            <dl>
	                <dt>门店类型</dt>
	                <dd>
	                    <select name="shopType" class="txt shopType" dir="rtl">
                            <option></option>
                            <option value="专卖店">${message("专卖店")}</option>
                            <option value="大家居">${message("大家居")}</option>
                            <option value="一址多名">${message("一址多名")}</option>
                            <option value="多品类">${message("多品类")}</option>
                            <option value="产品专区">${message("产品专区")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>城市行政等级</dt>
	                <dd>
                        <select name="accountTypeCode" class="txt accountTypeCode" dir="rtl">
                            <option></option>
                            <option value="省级">${message("省级")}</option>
                            <option value="市级">${message("市级")}</option>
                            <option value="区县级">${message("区县级")}</option>
                            <option value="乡镇级">${message("乡镇级")}</option>
                        </select>
                    </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>所属销售平台</dt>
	                <dd>
	                    <input type="text" class="txt" id="salesPlatform" value="${store.salesPlatform.name}" readonly />
	                </dd>
	            </dl>
	            <dl class="checkbox">
	                <dt>所含品牌</dt>
	                <dd>
	                    <!-- <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="国际出口" /><div class="check_box checked"></div>国际出口</label> -->
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="国际出口" /><div class="check_box "></div>国际出口</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="木香居地板" /><div class="check_box "></div>木香居地板</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="大自然工程" /><div class="check_box "></div>大自然工程</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="nature地板" /><div class="check_box "></div>nature地板</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="shopSign" value="大自然地板" /><div class="check_box "></div>大自然地板</label>
	                </dd>
	            </dl>
	            <dl>
	                <dt>所属品牌</dt>
	                <dd>
	                    <select class="txt belongBrand" name="belongBrand">
	                        <option>大自然地板</option>
	                    </select>
	                </dd>
	            </dl>
	            <dl>
	                <dt>销售渠道</dt>
	                <dd>
	                    <select class="txt salesChannel" name="salesChannel">
                            <option></option>
                            <option value="零售">${message("零售")}</option>
                            <option value="工程">${message("工程")}</option>
                            <option value="家装">${message("家装")}</option>
                        </select>
	                </dd>
	            </dl>
	            <dl class="readonly">
	                <dt>所属部门</dt>
	                <dd>
	                    <input type="text" class="txt" name="department" value="渠道" readonly="readonly" />
	                </dd>
	            </dl>
	            <dl>
	                <dt>VI版本</dt>
	                <dd>
	                    <select name="viVersion" class="txt viVersion" dir="rtl">
                            <option></option>
                            <option value="2013年以前版本">${message("2013年以前版本")}</option>
                            <option value="2013版本">${message("2013版本")}</option>
                            <option value="2017版本">${message("2017版本")}</option>
                        </select>
	                </dd>
	            </dl>
	        </div>
	    </div>
	    
	    <div class="info-box mt8">
            <div class="title"><b>省长意见</b></div>
            <div class="dl-style01">
                <dl>
                    <dd><textarea class="txt" name="szyj" placeholder="请输入"></textarea></dd>
                </dl>
            </div>
        </div>
        
	    <div class="info-box mt8">
	        <div class="title"><b>渠道</b></div>
	        <div class="dl-style01">
	            <dl>
	                <dt>门店授权编号</dt>
	                <dd><input type="text" name="shopAuthorizationCodes" class="txt" placeholder="请输入" /></dd>
	            </dl>
	            <dl>
	                <dt>新增档案编号</dt>
	                <dd><input type="text" name="archivesCodes" class="txt" placeholder="请输入" /></dd>
	            </dl>
	            <dl>
	                <dt>新增时间</dt>
	                <dd><input type="date" class="txt time" name="newTime" btn-fun="clear" value="" /></dd>
	            </dl>
	            <dl>
	                <dt>门店情况备注</dt>
	                <dd><textarea class="txt" name="shopCaseNote" placeholder="请输入"></textarea></dd>
	            </dl>
	        </div>
	    </div>
    </form>
    
    <!-- 文件上传 -->
    <form id="fileForm">
        <input type="file" name="file" id="shopFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addShopAttach', 'shopAttachs')" style="display: none"/>
    </form>
    
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" class="btn-blue btn" onclick="saveShopAdd()" /></div>
</div>

<!-- 选择客户 -->
<div class="pup-obox" id="CustomerBox">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
           [#-- <a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择客户</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="客户名称" id="searchByCustomerName"/>
            <input type="button" class="btn" value="搜索" onclick="searchCustomer()" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
</body>
</html>
