<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title></title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script src="/resources/js/swiper.min.3.4.2.js" type="text/javascript"></script>
</head>
<body>
<div id="containt">
    <div class="storeInfo-head">
        <img src="/resources/images/mobile/shop-logo.png" class="logo"/>
        <div class="fr">门店编码：${si.sn}</div>
    </div>
    <div class="swiper-container storeInfo-pic">
        <div class="swiper-wrapper">
            <div class="swiper-slide"><a href="#"><img src="/resources/images/mobile/banner.jpg"></a></div>
            <div class="swiper-slide"><a href="#"><img src="/resources/images/mobile/banner.jpg"></a></div>
            <div class="swiper-slide"><a href="#"><img src="/resources/images/mobile/banner.jpg"></a></div>
            <div class="swiper-slide"><a href="#"><img src="/resources/images/mobile/banner.jpg"></a></div>
        </div>
        <div class="swiper-pagination"></div>
    </div>
    <div class="storeInfo-box">
        <div class="title">
            <b>${si.shopName}</b>
            <p>主营：${si.businessCategory ? replace(',', '、')}</p>
        </div>
        <div class="adress"><a href="#">     
            <b>${si.address}</b>
            <p>门店位置：${si.positionType}</p>
        </a></div>
    </div>
    <div class="storeInfo-data mt8">
        <div class="title">门店信息</div>
        <div class="item">
            <div class="tit">基础信息</div>
            <ul class="sInfo-ul">
                <li>门店所有：${si.shopOwnership}</li>
                [#assign company = ['独立公司', '合伙公司', '个体工商户']]
                <li>公司性质：${company[si.store.companyType]}</li>
                <li>门店面积：${si.acreage}㎡</li>
                <li>建店日期：${si.openDate}</li>
                <li>经销商名称：${si.store.dealerName}</li>
                <li>经销商授权编码：${si.store.grantCode}</li>
                <li>手机号码：${si.store.headPhone}</li>
                <li>固定号码：${si.store.fixedNumber}</li>
            </ul>
        </div>
        <div class="item">
            <div class="tit">门店成员</div>
            <ul class="sInfo-member">
                <li>
                    <div class="pic"><img src="images/mobile/test/001.png"></div>
                    <div class="name">李大大<em class="btn online">在线</em></div>
                    <p>营销主任</p>
                    <a href="#" class="ico-tel"></a>
                </li>
                <li>
                    <div class="pic"><img src="images/mobile/test/001.png"></div>
                    <div class="name">李大大<em class="btn">离线</em></div>
                    <p>营销主任</p>
                    <a href="#" class="ico-tel"></a>
                </li>
                <li>
                    <div class="pic"><img src="images/mobile/test/001.png"></div>
                    <div class="name">李大大<em class="btn">离线</em></div>
                    <p>营销主任</p>
                    <a href="#" class="ico-tel"></a>
                </li>
            </ul>
            <a href="javascript:" onclick="operSotreMember(this)" class="showMember"><span>展开门店成员</span><i class="anima"></i></a>
        </div>
    </div>
    <div class="swiper-container storeInfo-nav mt8">
        <div class="swiper-wrapper">
            <div class="swiper-slide">
                <a class="item">活动列表</a>
                <a class="item">大使列表 </a>
                <a class="item">活动订单</a>
                <a class="item">上样信息</a>
            </div>
            <div class="swiper-slide">
                <a class="item">设计记录</a>
                <a class="item">装修记录</a>
                <a class="item">不良经营记录</a>
                <a class="item">勘察记录</a>
            </div>
        </div>
        <div class="swiper-pagination"></div>
    </div>
    <div class="pup-mask"></div>
    <div class="storeOper" onclick="showMoreOper(this)">
        <a href="javascript:" class="ico-oper anima"></a>
        <div class="list anima">
            <a href="/mobile/shop/decrease.jhtml?shopInfoId=${si.id}">门店减少<i class="ico-so02"></i></a>
            <a href="/mobile/shop/alteration.jhtml?shopInfoId=${si.id}">门店变更<i class="ico-so01"></i></a>
            <a href="/mobile/shop/restructuring.jhtml?shopInfoId=${si.id}">停业整顿<i class="ico-so03"></i></a>
        </div>
    </div>
</div>
<script type="text/javascript">
var swiper = new Swiper('.storeInfo-pic', {
    pagination: '.storeInfo-pic .swiper-pagination',
    paginationClickable: true
});
var swiper = new Swiper('.storeInfo-nav', {
    pagination: '.storeInfo-nav .swiper-pagination',
    paginationClickable: true
});
function operSotreMember(e){
    if($(e).hasClass("cur")){
        $(e).removeClass("cur").find("span").html("展开门店成员")
        $(".sInfo-member").css("height","0")
    }else{
        $(e).addClass("cur").find("span").html("收起门店成员")
        $(".sInfo-member").css("height","auto")
    }
}
function showMoreOper(e){
    if($(e).hasClass("cur")){
        $(e).removeClass("cur")
        $(".pup-mask").hide()
    }else{
        $(e).addClass("cur")
        $(".pup-mask").show()
    }
}
</script>
</body>
</html>