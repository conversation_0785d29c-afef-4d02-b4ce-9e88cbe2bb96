<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>门店设计</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
 
input[type="number"] {
    -moz-appearance: textfield;
}

label.error {
	color: red;
	font-size: 12px;
	display: block;
	text-align: right;
	margin: -5px 3px 0 0;
}
label.fieldError {
	color: red;
	font-size: 12px;
	display: block;
	text-align: right;
	margin: -5px 3px 0 0;
}
input.er.error {
	border-color: red;
}
input.er.fieldError {
	border-color: red;
}
.red {
	color: red;
}
</style>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
	$(this).siblings().find("input[type=radio]").removeAttr("checked")
	if ($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
			$(this).find(".check_box").addClass("checked")
			$(this).find("input[type=checkbox]").attr("checked", true);
		} else {
			$(this).find(".check_box").removeClass("checked")
			$(this).find("input[type=checkbox]").attr("checked", false);
        }
    } else {
		if ($(this).find("input[type=radio]:checked").val() == undefined) {
			$(this).find("input[type=radio]").removeAttr("checked")
			$(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked");
		} else {
			$(this).find("input[type=radio]").attr("checked", true)
			$($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
			$(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
		}
	}
});

$().ready(function(){
	// 初始化弹出框：如选择XXX  start  ---------
    $(".js-cancle").click(function(){
        $(".pup-obox").hide()
        $("body").attr("style","overflow:auto")
        $(".info-btns").show();  // 显示提交按钮
    });
	
 // 表单验证
    // 自定义validate验证输入的数字小数点位数不能大于两位
    jQuery.validator.addMethod("minNumber",function(value, element){
        var returnVal = true;
        inputZ = value;
        var ArrMen = inputZ.split(".");    //截取字符串
        if(ArrMen.length == 2){
            if(ArrMen[1].length>2){    //判断小数点后面的字符串长度
                returnVal = false;
                return false;
            }
        }
        return returnVal;
    }, "小数点后最多保留两位");         //验证错误信息
    
    $("#inputForm").validate({
        rules: {
        	designBrand: "required",
        	highLimit: "required",
        	predictConstructionTime: "required",
        	predictStartsTime: "required",
        	structure1: "required",
        	structure2: "required",
        	solidWoodRatio: {
                required: true,
                number: true,
                min: 0.01,
                minNumber: $('#solidWoodRatio').val()
            },
            multilayerRatio: {
                required: true,
                number: true,
                min: 0.01,
                minNumber: $('#multilayerRatio').val()
            },
            intensifyRatio: {
                required: true,
                number: true,
                min: 0.01,
                minNumber: $('#intensifyRatio').val()
            },
        },
        messages: {
        	designBrand: "请选择设计品牌",
        	highLimit: "请输入天花限高",
        	predictConstructionTime: "请选择预计施工时间",
        	predictStartsTime: "请选择预计开业时间",
        	structure1: "请选择门店结构情况",
        	structure2: "请选择门店结构情况",
        	solidWoodRatio: {
                required: "请输入完整的上样占比",
                number: "只能输入是数值",
                min: "上样占比单个值最小为0.01"
            },
            multilayerRatio: {
                required: "请输入完整的上样占比",
                number: "只能输入是数值",
                min: "上样占比单个值最小为0.01"
            },
            intensifyRatio: {
                required: "请输入完整的上样占比",
                number: "只能输入是数值",
                min: "上样占比单个值最小为0.01"
            },
        },
        errorPlacement: function (error, element) { //指定错误信息位置
            error.insertAfter(element.parent().parent()); //将错误信息添加当前元素的父结点后面
        }
    });


	$('.designBrand').change(function(){
		var c = $(this).val();
		var arr = [];
		var b = $('.proportion')
		if(c==0){
			arr = ["木香居","实木","设计师专区","地暖专区","多层","三层","进口三层","1530","康德1号","戴昆","强化"];
		}else if(c==1){
			arr = ["多层","三层","进口三层","1530"];
		}else if(c==2){
			arr = ["木香居","实木","设计师专区","地暖专区"];
		}else if(c==3){
			arr = ["强化"];
		}
		var ar = $('.statusList label');
		//console.log(ar)
		for(var j=0;j<ar.length;j++){
			$('.statusList label').eq(j).find('input').prop("checked", false);
		}
		for(var j=0;j<ar.length;j++){
			var cs = ar.eq(j).find('input');
			for(var i=0;i<arr.length;i++){
				if(cs.val()==arr[i]){
					cs.prop("checked", true);
				}
			}
		}
	});
})

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

// 弹出框相关 ------ start ------
// 搜索门店
function searchShop() {
    var e = $('#containt').find('dd[data-id="ShopCode"]')[0]
    showPup(e);
}

function showPup(e){
    var id = $(e).attr("data-id");
    $("body").attr("style","overflow:hidden");
    $(".pup-obox").hide();
    $(".info-btns").hide();  // 隐藏提交按钮
    $("#"+id).show();
    // 选择门店
    if(id == "ShopCode"){
        $('#'+id).find('ul').empty();
        $.ajax({
            type:'POST',
            url:'/shop/shopInfo/select_shopInfo_data.jhtml',
            data:{sn:$('#searchByShopCode').val()},
            success:function(data) {
                if(data.type == 'success'){
                    var rows = JSON.parse(data.content).content;
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        var shopRenovationAttribute; //门店装修属性特殊处理
						if(row.authorization_code == null || row.authorization_code == ""){
							shopRenovationAttribute = "新店";
						}else{
							shopRenovationAttribute = "重装店";
						}

                        var html = "<li data-id='ShopCode' onclick='selectItem(this)'><a href='#'>"
                                + "<div class='name'><span>门店单号：</span>"+ isNull(row.sn) +"</div>"
                                + "<div class='name'><span>门店名称：</span>"+ isNull(row.shop_name) +"</div>"
                                + "<div class='name'><span>经销商：</span>"+ isNull(row.dealer_name) +"</div>"
                                + "<div class='fr'><span>授权编号：</span>"+ isNull(row.authorization_code) +"</div>"
                                + "</a>"
                                + "<input type='hidden' class='xsShopName' value='"+ isNull(row.shop_name) +"'/>"
                                + "<input type='hidden' class='xsShopInfoSn' value='"+ isNull(row.sn) +"'/>"
                                + "<input type='hidden' class='xsShopInfoId' value='"+ isNull(row.id) +"'/>"
                                + "<input type='hidden' class='xsApplyCity' value='"+ isNull(row.town) +"'/>"
                                + "<input type='hidden' class='xsDealer' value='"+ isNull(row.distributor_name) +"'/>" // 经销商
                                + "<input type='hidden' class='xsDistributorPhone' value='"+ isNull(row.distributor_phone) +"'/>"  // 经销商电话
                                + "<input type='hidden' class='xsAcreage' value='"+ isNull(row.acreage) +"'/>"  // 面积
                                + "<input type='hidden' class='xsAddress' value='"+ isNull(row.address) +"'/>"  // 地址
                                + "<input type='hidden' class='xsSaleOrgName' value='"+ isNull(row.sale_org_name) +"'/>"  // 地址
                                + "<input type='hidden' class='xsShopRenovationAttribute' value='"+ shopRenovationAttribute +"'/>"  // 门店装修属性
                                + "</li>";
                        $('#'+id).find('ul').append(html)
                    }


                }
            }
        })

    }
}

// 将弹出框选择的数据回显到页面
function selectItem(e){
    var id = $(e).attr("data-id")
    $("body").attr("style", "overflow:auto");
    $(".pup-obox").hide();
    $(".info-btns").show();
    if(id == "ShopCode"){ // 选择门店
        $('#containt').find('input[name="shopName"]').val($(e).find('input[class="xsShopName"]').val());
    	$('#containt').find('input[name="shopInfoSn"]').val($(e).find('input[class="xsShopInfoSn"]').val());
        $('#containt').find('input[name="shopInfoId"]').val($(e).find('input[class="xsShopInfoId"]').val());
        $('#containt').find('input[name="applyCity"]').val($(e).find('input[class="xsApplyCity"]').val());
        $('#containt').find('input[name="dealer"]').val($(e).find('input[class="xsDealer"]').val());
        $('#containt').find('input[name="phone"]').val($(e).find('input[class="xsDistributorPhone"]').val());
        $('#containt').find('input[name="area"]').val($(e).find('input[class="xsAcreage"]').val());
        $('#containt').find('input[name="shopAddress"]').val($(e).find('input[class="xsAddress"]').val());
        $('#containt').find('input[name="saleOrgName"]').val($(e).find('input[class="xsSaleOrgName"]').val());
        $('#containt').find('input[name="shopRenovationAttribute"]').val($(e).find('input[class="xsShopRenovationAttribute"]').val());// 门店装修属性（重装店、新店）
        $.ajax({
            type:'POST',
            url:'/shop/devise/existShopInfo.jhtml',
            data:{shopInfoId:$(e).find('input[class="xsShopInfoId"]').val()},
            success:function(data) {
                // if (data == 1) {
                //     $('#containt').find('input[name="shopAttribute"]').val("重装店");
                // } else if (data == 0) {
                //     $('#containt').find('input[name="shopAttribute"]').val("新店");
                // }
                if (data == 1) {
                    $("#shopAttribute").val("是");
                    $(".shopAttributeText").val("是");
                } else if (data == 0) {
                    $("#shopAttribute").val("否");
                    $(".shopAttributeText").val("否");
                }
            }
        })
    }
}
// 弹出框相关 ------ end ------

// 文件上传 ----- start -----
// 添加附件
function addAttach(e, attachFile) {
	$('#fileForm').find('input').removeAttr("name");
	$('#' + attachFile).attr("name", "file");
    $('#' + attachFile).trigger('click'); 
}

/** 
 * 附件上传
 * @param attachId 所在input标签的id名
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param type 后台用来区分不同类型附件，若后台的附件类只存一个类型的附件，则可以不用传该值
 */
function fileUpload(e, attachId, paramAttachs, name, type) {
    var formData = new FormData($("#fileForm")[0]);
    var len = $('#'+attachId).find('div[class="item"]').size()
    $.ajax({
        type:'GET',
        url:'/common/fileurl.jhtml',
        success:function(data) {
            if(data.type == "success"){
                $.ajax({
                    type:'POST',
                    url: data.objx,
                    data:formData,
                    cache: false,  
                    contentType: false,  
                    processData: false, 
                    success:function(data_) {
                        data_ = JSON.parse(data_)
                        if(data_.message.type == "success"){
                            var html = "<div class='item'>"
	                                + "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
	                                + "<div class='tit'>"+name+" <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
	                                + "<textarea class='txt' placeholder='请输入备注' id='attachMemo' name='"+paramAttachs+"["+len+"].memo'></textarea>"
	                                + "<input type='hidden' id='attachName' name='"+paramAttachs+"["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
	                                + "<input type='hidden' id='attachUrl' name='"+paramAttachs+"["+len+"].url' value='"+data_.url+"'/>"
	                                + "<input type='hidden' id='attachType' name='"+paramAttachs+"["+len+"].type' value='"+parseInt(type)+"'/>"
	                                + "<input type='hidden' id='attachSuffix' name='"+paramAttachs+"["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
	                                + "</div>";
                            $('#'+attachId).append(html);
                        }
                    }
                })              
            }
        }
    })
}
// 删除附件
function del(e) {
    $(e).parent('div').remove();
}
// 文件上传 ----- end -----


// 保存操作
function saveDevise(){
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    if($("#inputForm").find('input[name="shopInfoId"]').val() == null || $("#inputForm").find('input[name="shopInfoId"]').val() == ""){
        alert("请重新选择门店");
        return false;
    }
    if ($("#inputForm").valid()) {
	    $.ajax({
	        type:'POST',
	        url:'/shop/devise/save.jhtml',
	        data:$("#inputForm").serialize(),
	        success:function(data) {
	            if(data.type == 'success'){
	               alert("保存成功");
                    location.href='/mobile/shop/devise/edit.jhtml?id='+data.objx
	            } else {
	            	alert("保存失败, " + data.content);
	            }
	        }
	    })
    } else {
        alert("请先输入必填的输入框，再进行提交！");
    }
}

</script>
</head>
<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
	    <div class="info-box">
	        <div class="title"><b>基本信息</b><a href="/mobile/shop/devise/list.jhtml">查看历史记录</a></div>
	        <div class="dl-style01">
	            <dl>
	                <dt><span class="red">* </span>门店名称</dt>
	                <dd data-id="ShopCode" onclick="showPup(this)">
                        <input type="text" class="txt arrow" name="shopName" value="">
                        <input type="hidden" class="txt arrow" placeholder="请选择" id="shopInfoSn" name="shopInfoSn" value="" disabled />
                        <input type="hidden" name="shopInfoId" class="text shopInfoId" value="" btn-fun="clear"/>
                    </dd>
	            </dl>
                <dl class="readonly">
                    <dt>申请城市</dt>
                    <dd><input type="text" name="applyCity" class="txt applyCity" readonly /></dd>
                </dl>
	            <dl>
                    <dt><span class="red">* </span>设计品牌</dt>
                    <dd>
                        <select name="designBrand" class="txt designBrand" dir="rtl">
                            <option value="">请选择</option>
                            <option value="0">${message("大自然综合店")}</option>
                            <option value="1">${message("大自然·三层专卖店")}</option>
                            <option value="2">${message("大自然·实木专卖店")}</option>
                            <option value="3">${message("大自然·强化专卖店")}</option>
                        </select>
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>经销商</dt>
                    <dd>
                        <input type="text" name="dealer" class="txt dealer" readonly />
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>联系方式</dt>
                    <dd><input type="text" name="phone" class="txt phone" readonly /></dd>
                </dl>
	            <dl>
	                <dt><span class="red">* </span>天花限高</dt>
	                <dd><input type="number" name="highLimit" class="txt" placeholder="请输入"></dd>
	            </dl>
                <dl class="readonly">
                    <dt>面积（㎡）</dt>
                    <dd><input type="text" name="area" class="txt area" readonly /></dd>
                </dl>
                <dl class="readonly">
                    <dt><span class="red">* </span>门店地址</dt>
                    <dd><input type="text" name="shopAddress" class="txt shopAddress" readonly /></dd>
                </dl>
                <dl class="readonly">
                    <dt>乡镇</dt>
                    <dd><input type="text" name="" class="txt" readonly /></dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>预计施工时间</dt>
                    <dd><input type="date" name="predictConstructionTime" value="" class="txt time"></dd>
                </dl>
                <dl>
                   <dt><span class="red">* </span>预计开业时间</dt>
	                <dd><input type="date" name="predictStartsTime" value="" class="txt time"></dd>
	            </dl>
	            <dl>
	                <dt><span class="red">* </span>门店结构情况</dt>
	                <dd>
	                    <select name="structure1" class="txt" style="padding:0 15px;" dir="rtl">
                            <option value="">请选择</option>
                            <option value="市场内">${message("市场内")}</option>
                            <option value="沿街">${message("沿街")}</option>
                        </select>
                        <select name="structure2" class="txt" style="padding:0 15px;" dir="rtl">
                            <option value="">请选择</option>
                            <option value="单层" >${message("单层")}</option>
                            <option value="复式" >${message("复式")}</option>
                        </select>
	                </dd>
	            </dl>
                <dl class="readonly">
                    <dt>是否二次设计</dt>
                    <dd>
                        <input type="text" class="txt shopAttributeText" readonly="readonly" />
						<input type="hidden" name="shopAttribute" id="shopAttribute" class="text" btn-fun="clear" readonly="readonly" />
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>机构</dt>
                    <dd><input type="text" name="saleOrgName" class="txt saleOrgName" readonly /></dd>
                </dl>
                <dl class="readonly">
                    <dt>门店装修属性</dt>
                    <dd><input type="text" name="shopRenovationAttribute" class="txt" readonly /></dd>
                </dl>
                <dl>
                    <dt>QQ/邮箱</dt>
                    <dd><input type="text" name="qqOrEmail" class="txt" placeholder="请输入"></dd>
                </dl>
	            <dl class="checkbox">
	                <dt><span class="red">* </span>上样计划</dt>
	                <dd class="statusList">
						<label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="木香居" /><div class="check_box "></div>木香居</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="实木" /><div class="check_box "></div>实木</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="多层" /><div class="check_box "></div>多层</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="三层" /><div class="check_box "></div>三层</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="进口三层" /><div class="check_box "></div>进口三层</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="1530" /><div class="check_box "></div>1530</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="康德1号" /><div class="check_box "></div>康德1号</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="强化" /><div class="check_box "></div>强化</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="戴昆" /><div class="check_box "></div>戴昆</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="设计师专区" /><div class="check_box "></div>设计师专区</label>
	                    <label class="checkhid" data-id="c"><input type="checkbox" name="samplePlans" value="地暖专区" /><div class="check_box "></div>地暖专区</label>
	                </dd>
	            </dl>
	            <dl>
	                <dt><span class="red">* </span>上样占比</dt>
	                <dd>
	                    <span>实木<input type="number" name="solidWoodRatio" id="solidWoodRatio" class="txt1" style="width:70px; margin: 5px 5px;"/>%</span>&emsp;<br/>
	                    <span>多层<input type="number" name="multilayerRatio" id="multilayerRatio" class="txt1" style="width:70px; margin: 0 5px 5px;"/>%</span>&emsp;<br/>
	                    <span>强化<input type="number" name="intensifyRatio" id="intensifyRatio" class="txt1" style="width:70px; margin: 0 5px 5px;"/>%</span>&emsp;
	                </dd>
	            </dl>
	        </div>
	    </div>
	    <div class="article-wrap" style="border-top:solid 1px #eee;padding:12px;">
	        <div class="tips-box">提示</div>
	        <div class="cont mt6" style="line-height: 1.8">
	            <p>1、经销商在收到公司设计图纸后2个月内必须完成所有装修，竣工后1个月内将门店照片以PDF格式提交到地板事业部终端设计部初审，符合最新形象标准的即提供门店验收表单/相片等完整资料到渠道部报销报销；不符合最新形象标准的，由经销商根据终地板事业部终端设计部下达整改意见进行整改，再做提报返利报销。</p>
	            <p>2、需提供资料：</p>
	            <p style="color:#999">&emsp;（1）原始结构图、门店平面尺寸详图、天花（喷淋及空调管道）标高图（电子版）。</p>
	            <p style="color:#999">&emsp;（2）全方位、多角度门店现场相片5张以上（电子版）。</p>
	            <p style="color:#999">&emsp;（3）建材市场/物业装修管理相关规定（电子版）。</p>
	            <p style="color:#999">&emsp;（4）门店租赁合同/房产证（电子扫描版）</p>
	            <p>3、低于80平方的乡镇门店总部不提供设计。</p>
	            <p>4、由于经销商个人原因，要求重复设计的门店，将扣除报销总金额的10%处理。</p>
	            <p>★特别注明：经销商提交至终端设计部的门店面积需真实有效，若有随意拉大尺寸提高实际面积的行为。经现场审计核算若与事实不符，所有后果由经销商本人承担，并追究相关责任人责任。</p>
	            <br/>
	            <p style="color:#101010;font-weight: 700;font-size: 0.875rem">本人已认真阅读以上内容并严格执行！如未签名确认将不提供设计！</p>
	        </div>
	    </div>
	    <div class="info-box1 mt8">
	        <div class="chooseBox" id="b3">
	            <label class="checkhid" data-id="b3"><input type="radio" name="reimburse" value="2" /><div class="check_box"></div>提供设计、参与装修报销</label>
	            <label class="checkhid" data-id="b3"><input type="radio" name="reimburse" value="1" /><div class="check_box"></div>提供设计、不参与报销</label>
	        </div>
	        <div class="f-red">★提示：参与装修报销经销商在收到设计图之后半年内必须装修完成并且提交装修验收表单，否则不予报销</div>

            <div class="dl-style01">
                <dl class="upload-bBox">
                    <dt>门店照片</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'dealersAttach')">上传照片</a>
                        <!-- <p class="f-red">只支持.jpg 格式</p> -->
                    </dd>
                </dl>
            </div>
            <div class="atta-list" id="addDealersAttach" style="padding: 0 10px 0 0px;"></div>

            <div class="dl-style01">
                <dl class="upload-bBox">
                    <dt>租赁合同</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'storeContractAttach')">上传照片</a>
                        <!-- <p class="f-red">只支持.jpg 格式</p> -->
                    </dd>
                </dl>
            </div>
            <div class="atta-list" id="addStoreContractAttach" style="padding: 0 10px 0 0px;"></div>

            <div class="dl-style01">
                <dl class="upload-bBox">
                    <dt>平面图</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'storePicturesAttach')">上传照片</a>
                        <!-- <p class="f-red">只支持.jpg 格式</p> -->
                    </dd>
                </dl>
            </div>
            <div class="atta-list" id="addStorePicturesAttach" style="padding: 0 10px 0 0px;"></div>
        </div>

    </form>
    
    <!-- 文件上传 -->
    <form id="fileForm">
<!-- 	    <input type="file" name="file" id="designFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addDesignAttach', 'designAttachs', '设计图')" style="display: none"/> -->

		<input type="file" name="file" id="dealersAttach" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addDealersAttach', 'dealersAttachs', '门店照片', 0)" style="display: none"/>
	    <input type="file" name="file" id="storeContractAttach" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addStoreContractAttach', 'storeContractAttachs', '租赁合同', 3)" style="display: none"/>
	    <input type="file" name="file" id="storePicturesAttach" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addStorePicturesAttach', 'storePicturesAttachs', '平面图', 4)" style="display: none"/>

<!-- 		<input type="file" name="file" id="managerDealersAttach" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addManagerDealersAttach', 'managerDealersAttachs', '门店照片')" style="display: none"/>
		<input type="file" name="file" id="managerStoreContractAttach" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addManagerStoreContractAttach', 'managerStoreContractAttachs', '租赁合同')" style="display: none"/>
		<input type="file" name="file" id="managerStorePicturesAttach" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addManagerStorePicturesAttach', 'managerStorePicturesAttachs', '平面图')" style="display: none"/> -->

	</form>
	
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" onclick="saveDevise(this)" class="btn-blue btn" /></div>
</div>

<!-- 选择门店（编码） -->
<div class="pup-obox" id="ShopCode">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择门店</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="门店单号" id="searchByShopCode"/>
            <input type="button" class="btn" value="搜索" onclick="searchShop()" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
</body>
</html>