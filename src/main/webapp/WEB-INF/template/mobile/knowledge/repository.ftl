<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
//当前页数
var pageNumber = 1;
var knowledgeCategoryId = "";
var knowledgeCategoryName = "";
$().ready(function(){
	//加载产品数据
	ajaxListData()
})

function search() {
	pageNumber = 1;	//重置页数为第一页
	ajaxListData()
}

function targetCategory(e) {
	knowledgeCategoryName = $(e).next('input').val();
	pageNumber = 1;	//重置页数为第一页
	$('a').removeClass("on");
	$(e).addClass("on");
 	ajaxListData()
}

//点击全部
function allData(e) {
	knowledgeCategoryName = "";
	pageNumber = 1;	//重置页数为第一页
	$('a').removeClass("on");
	$(e).addClass("on");
 	ajaxListData()
}

function goDetail(e) {
	window.location.href = "/mobile/knowledge/notice_detail.jhtml?id="+$(e).next('input').val()
}

function ajaxListData(){
	$('#kn-list').empty();
	$.ajax({
		type:'POST',
		url:'/mobile/knowledge/list_photo_data.jhtml',
		data:{title:$('#title-name').val(), type:1, pageNumber:pageNumber, knowledgeCategoryName:knowledgeCategoryName},
		success:function(data) {
			if(data.type == 'success'){
				var rows = JSON.parse(data.content).content;
				// console.log(rows);
				for (var i = 0; i < rows.length; i++) {
					var row = rows[i];
					var html = '<li>' 
// 						+ '<a href="/mobile/knowledge/article_detail.jhtml?id='+row.id+'">' + row.title
// 						+ '<a href="/mobile/knowledge/article_detail.jhtml">' + row.title
						+ '<a onclick="goDetail(this)">' + row.title
						+ '   <span class="time">'+ row.create_date.substring(0,10) +'</span>'
						+ '</a>'
						+ '   <input type="hidden" value="'+row.id+'">'
						+ '</li>';
					
					$('#kn-list').append(html);
				}
			}
		}
	})
}

//获取当前浏览器中的滚动事件
$(window).off("scroll").on("scroll", function () {
	//获取当前浏览器的滚动条高度
     var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight; 
   	//判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
     if (scrollHeight <= ($(window).scrollTop() + $(window).height())) { 
         setTimeout(function () {
       		$.ajax({
       			type:'POST',
       			url:'/mobile/knowledge/list_photo_data.jhtml',
       			data:{title:$('#title-name').val(), type:1, pageNumber:pageNumber, knowledgeCategoryName:knowledgeCategoryName},
       			success:function(data) {
       				if(data.type == 'success'){
       					var rows = JSON.parse(data.content).content;
       					// console.log(rows);
       					for (var i = 0; i < rows.length; i++) {
       						var row = rows[i];
       						var html = '<li>' 
       							+ '<a onclick="goDetail(this)">' + row.title
       							+ '   <span class="time">'+ row.create_date.substring(0,10) +'</span>'
       							+ '</a>'
       							+ '   <input type="hidden" value="'+row.id+'">'
       							+ '</li>';
       						
       						$('#kn-list').append(html);
       					}
       				}
       			}
       		})
         },500)
         //设置当前页数
         pageNumber += 1;
     }
 });


function changeFilter(e){
	$(e).closest(".filter-box").find(".c").slideToggle()
	$(e).closest(".filter-box").toggleClass("cur")
	$("body").toggleClass("hidden")
}
</script>
</head>
<body>
<div id="containt">
	<div class="search-box">
		<input type="text" class="txt" id="title-name" placeholder="请输入文章标题"/>
		<input type="button" class="btn" onclick="search()" value="搜索">
	</div>
	<div class="filter-box mt6">
		<div class="t">
			<a onclick="allData(this)" class="on">全部</a>
			<a onclick="targetCategory(this)">${knowledgeCategorys[0].name}</a>
			<input type="hidden" value="${knowledgeCategorys[0].name}"></input>
			<a onclick="targetCategory(this)">${knowledgeCategorys[1].name}</a>
			<input type="hidden" value="${knowledgeCategorys[1].name}"></input>
			<i class="ico-arrow" onclick="changeFilter(this)"></i>
		</div>
		<div class="c">
			<!-- [#list knowledgeCategorys as kc]
			[#if pc.parent??]
		   	[#else]
		   		[#if pc_index != 0 && pc_index != 1]
		   			<a onclick="targetCategory(this, ${kc.id})">${kc.name}</a>
		   			<a href="#">${kc.name}</a>
		   		[/#if]
		    [/#if]
			[/#list] -->
			
			[#list knowledgeCategorys as kc]
		   		<a onclick='targetCategory(this)' data-name="${kc.name}">${kc.name}</a>
		   		<input type="hidden" value="${kc.name}"></input>
			[/#list]
		
			<!-- <a href="#">实物类</a>
			<a href="#">虚拟类</a>
			<a href="#">实物类</a>
			<a href="#">虚拟类</a>
			<a href="#">实物类</a>
			<a href="#">虚拟类</a> -->
			<!-- <a href="#">实物类</a>
			<a href="#">虚拟类</a>
			<a href="#">实物类</a>
			<a href="#">虚拟类</a>
			<a href="#">实物类</a>
			<a href="#">虚拟类</a> -->
		</div>
	</div>
	<ul class="list-style03" id="kn-list">
<!-- 		<li><a href="/mobile/knowledge/article_list.jhtml">大自然地板2018新品发布会<span class="time">2019-09-09</span></a></li> -->
<!-- 		<li><a href="/mobile/knowledge/article_list.jhtml">大自然地板2018新品发布会<span class="time">2019-09-09</span></a></li> -->
<!-- 		<li><a href="/mobile/knowledge/article_list.jhtml">大自然地板2018新品发布会<span class="time">2019-09-09</span></a></li> -->
<!-- 		<li><a href="/mobile/knowledge/article_list.jhtml">大自然地板2018新品发布会<span class="time">2019-09-09</span></a></li> -->
<!-- 		<li><a href="/mobile/knowledge/article_list.jhtml">大自然地板2018新品发布会<span class="time">2019-09-09</span></a></li> -->
	</ul>
</div>
</body>
</html>
