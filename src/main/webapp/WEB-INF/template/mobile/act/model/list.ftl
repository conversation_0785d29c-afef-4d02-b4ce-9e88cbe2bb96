<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("流程模板")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/icon.css">
<script type="text/javascript" src="/resources/plugin/jquery-easyui-*******/jquery.easyui.min.js"></script>
<script type="text/javascript" src="/resources/js/base/datagrid-dnd.js"></script>
<script type="text/javascript">
function add(){
	parent.change_tab(0,'add.jhtml');
}
$().ready(function() {
	//冻结列
	var frozenColumns = [[
		{field:'id',title:'ids',width:25,checkbox:true,},//复选框
		{ title:'${message("模板ID")}', field:'ID_' ,align:'center',width:200,isIcon:true,formatter:function(val,item){
			//return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'edit.jhtml?id='+item.ID_+'\')" class="red">'+val+'</a>';
			return val;
		}},
	]];
	var columns = [[
		{ title:'${message("模板名称")}', field:'NAME_' ,align:'center',width:150 },
		{ title:'${message("模板关键字")}', field:'KEY_' ,align:'center',width:150 },
		{ title:'${message("模板类别")}', field:'CATEGORY_' ,align:'center',width:150 },
		{ title:'${message("部署ID")}', field:'DEPLOYMENT_ID_' ,align:'center',width:150 },
		{ title:'${message("创建日期")}', field:'CREATE_TIME_' ,align:'center',width:150 },
		{ title:'${message("操作")}', field:'cz' ,align:'center',width:150,formatter:function(val,item){
			var html = '<a target="_blank" href="/activiti/showMode.jhtml?modelId='+item.ID_+'" class="btn-edit">建模</a>';
			return html;
		}},
	]];
    //列表参数
	var options = {    
		tableName:"",//列表名（唯一），
		path: 'list_data.jhtml',//数据请求地址
		params:function(){//额外请求参数
			return $("#listForm").serializeObject();
		},
		//mergeMainId:"id",//合并行的主键
		frozenColumns:frozenColumns,//冻结列
		columns: columns,//普通列
		callback:function(data){//回调
		
		}
	}
	//初始化列表	 
	initEasyUiTable('#table-m1',options);
	
});
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
        			<dt><p>${message("名称")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="name" btn-fun="clear" />
        			</dd>
        		</dl>
        		
			</div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton" onclick="doSearch('#table-m1')">${message("1004")}</a></div>
		</div>
		</div>
		<div class="table-responsive" id="table-m1" data-options="resizeEdge:10,edge:10"></div>
	</form>
</body>
</html>