<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
//当前页数
var pageNumber = 1;
// 每页数据量
var pageSize = 8;

$().ready(function(){
	ajaxListData()
	//根据充值编号搜索充值
	/* $("#sn").blur(function(){
		ajaxListData()
	}) */
	/* $("#sn").keypress(function (e) {
        if (e.which == 13) {
        	ajaxListData()
        }
	}) */
});
function ajaxListData(){
	pageNumber = 1;
	$('#p-ul').empty()
	var flag = '${flag}';
	$.ajax({
		type:'POST',
		url:'/act/wf/list_data.jhtml',
		data:{wfName:$('#wfName').val(),flag:flag,pageNumber:pageNumber,pageSize:pageSize},
		success:function(data) {
			fillData(data);
		}
	})
}

//获取当前浏览器中的滚动事件
$(window).off("scroll").on("scroll", function () {
	//获取当前浏览器的滚动条高度
     var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight; 
   	//判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
     if (scrollHeight <= ($(window).scrollTop() + $(window).height())) {
    	 var flag = '${flag}';
         setTimeout(function () {
       		$.ajax({
       			type:'POST',
       			url:'/act/wf/list_data.jhtml',
       			data:{wfName:$('#wfName').val(),flag:flag,pageNumber:pageNumber,pageSize:pageSize},
       			success:function(data) {
       				fillData(data);
       			}
       		})
         },500);
         //设置当前页数
         pageNumber += 1;
     }
});

// 填充数据
function fillData(data) {
	if(data.type == 'success'){
		var rows = JSON.parse(data.content).content;
		for (var i = 0; i < rows.length; i++) {
        	var row = rows[i];
        	
        	var stat = row.stat;
        	var statStr = '';
        	var startClass = '';
        	if(stat==1){
        		statStr = '审核中';
        		startClass = 'btn-lorgn';
        	}else if(stat==2){
        		statStr = '已完成';
        		startClass = 'btn-blue';
        	}else if(stat==3){
        		statStr = '驳回';
        		startClass = 'btn-lgray';
        	}
        	
        	var html = '<li><a href="javascript:void();" onclick="wf_view('+row.wf_id+')">'+
    			'<div class="t">'+
    				'<div class="fl">'+row.wf_name+'</div>'+
    				'<div class="status '+startClass+'">'+statStr+'</div>'+
    			'</div>'+
    			'<div class="c">'+
    				'<p>启动人：'+row.start_username+'</p>'+
    				'<p>流程模版：'+row.model_name+'</p>'+
    				'<p>创建时间：'+row.create_date+'</p>'+
    			'</div>'+
    		'</a></li>';
        	
			$('#p-ul').append(html)
		}
	}
}

//充值详情
function wf_view(wfid){
	window.location.href = "wf.jhtml?wfid="+wfid;
}
function search(e) {
	ajaxListData();
}
</script>

</head>
<body>
<div id="containt">
	<div class="search-box">
		<input type="text" class="txt" id="wfName" placeholder="请输入流程名称"/>
		<input type="submit" class="btn" value="搜索" onclick="search(this)" />
	</div>
	<div class="tab-style mt1">
		<div [#if flag==null || flag==1]class="cur"[/#if]><a href="/mobile/act/wf/list.jhtml?flag=1">我的待办</a></div>
		<div [#if flag==2]class="cur"[/#if]><a href="/mobile/act/wf/list.jhtml?flag=2">我已处理</a></div>
		<div [#if flag==0]class="cur"[/#if]><a href="/mobile/act/wf/list.jhtml?flag=0">我的申请</a></div>
		<div [#if flag==3]class="cur"[/#if]><a href="/mobile/act/wf/list.jhtml?flag=3">已完成的</a></div>
	</div>
	[#--
    <div class="tab-style02" style="margin-top:-1px">
    	<div onclick="showStore(this)">排序<i class="ico-arrow anima"></i></div>
        <div onclick="showGenre(this)">类别<i class="ico-arrow anima"></i></div>
        <div onclick="showScreen(this)">筛选<i class="ico-arrow anima"></i></div>
    </div>
    --]
	<ul class="list-style05" id="p-ul">
		[#--
		<li><a href="#">
			<div class="t">
				<div class="fl">张三的售后申请</div>
				<div class="status btn-blue">完成</div>
			</div>
			<div class="c">
				<p>申请单号：39032984032</p>
				<p>当前处理人：公司</p>
				<p>当前审批结果：待处理</p>
				<p>创建时间：2019-09-09 10:00:00</p>
			</div>
		</a></li>
		<li><a href="#">
			<div class="t">
				<div class="fl">张三的售后申请</div>
				<div class="status btn-lgray">中止</div>
			</div>
			<div class="c">
				<p>申请单号：39032984032</p>
				<p>当前处理人：公司</p>
				<p>当前审批结果：待处理</p>
				<p>创建时间：2019-09-09 10:00:00</p>
			</div>
		</a></li>
        <li><a href="#">
			<div class="t">
				<div class="fl">张三的售后申请</div>
				<div class="status btn-lorgn">进行中</div>
			</div>
			<div class="c">
				<p>申请单号：39032984032</p>
				<p>当前处理人：公司</p>
				<p>当前审批结果：待处理</p>
				<p>创建时间：2019-09-09 10:00:00</p>
			</div>
		</a></li>
		--]
	</ul>
</div>
<a href="#containt" class="go-top"></a>
<!-- pup -->
<div class="pup-mask" style="top:141px;"></div>
<div class="screen-box anima" id="pupStore">
	<dl>
    	<dt>默认排序</dt>
        <dd><button class="">默认排序</button></dd>
    </dl>
    <dl>
    	<dt>状态</dt>
        <dd><button class="cur">升序</button><button class="">降序</button></dd>
    </dl>
    <dl>
    	<dt>创建时间</dt>
        <dd><button class="">升序</button><button class="cur">降序</button></dd>
    </dl>
</div>
<div class="screen-box01 anima" id="pupGenre">
	<div class="sb-head">
		<a href="javascript:" class="s-l f-blue" onclick="cancleGenre(this)">取消</a>
		<div class="t">类别</div>
		<a href="#" class="s-r f-blue">确定</a>
	</div>
	<div class="search-box">
		<input type="search" class="txt" placeholder="搜索" style="width: 100%" />
	</div>
	<ul class="genre-list" id="c">
		<li><label>--清空--</label></li>
		<li>
			<label class="checkhid" data-id="c">
				<input type="radio" name="1">
				<div class="fl">放假</div>
				<div class="check_box checked fr"></div>
			</label>
		</li>
		<li>
			<label class="checkhid" data-id="c">
				<input type="radio" name="1">
				<div class="fl">离职</div>
				<div class="check_box fr"></div>
			</label>
		</li>
		<li>
			<label class="checkhid" data-id="c">
				<input type="radio" name="1">
				<div class="fl">离职</div>
				<div class="check_box fr"></div>
			</label>
		</li>
		<li>
			<label class="checkhid" data-id="c">
				<input type="radio" name="1">
				<div class="fl">入职</div>
				<div class="check_box fr"></div>
			</label>
		</li>
		<li>
			<label class="checkhid" data-id="c">
				<input type="radio" name="1">
				<div class="fl">离职</div>
				<div class="check_box fr"></div>
			</label>
		</li>
	</ul>
</div>
<div class="screen-box02 anima" id="pupScreen">
	<div class="cont checkbox-style">
		<dl>
			<dt>类型</dt>
			<dd  id="a1">
				<label class="checkhid" data-id="a1">
					<input type="radio" name="1">
					<div class="check_box checked">门店新增</div>
				</label>
				<label class="checkhid" data-id="a1">
					<input type="radio" name="1">
					<div class="check_box">门店设计</div>
				</label>
				<label class="checkhid" data-id="a1">
					<input type="radio" name="1">
					<div class="check_box">门店装修验收</div>
				</label>
				<label class="checkhid" data-id="a1">
					<input type="radio" name="1">
					<div class="check_box">门店费用申请</div>
				</label>
			</dd>
		</dl>
		<dl>
			<dt>状态</dt>
			<dd id="a2">
				<label class="checkhid" data-id="a2">
					<input type="radio" name="1">
					<div class="check_box checked">进行中</div>
				</label>
				<label class="checkhid" data-id="a2">
					<input type="radio" name="1">
					<div class="check_box">已终止</div>
				</label>
				<label class="checkhid" data-id="a2">
					<input type="radio" name="1">
					<div class="check_box">已完成</div>
				</label>
			</dd>
		</dl>
		<dl>
			<dt>创建起止时间</dt>
			<dd><input type="date" class="txt" style="width: 45%"> - <input type="date" class="txt" style="width: 45%"></dd>
		</dl>
		<dl>
			<dt>当前审批人</dt>
			<dd>
				<div class="txt"><input type="text"><button class="ico-search"></button></div>
			</dd>
		</dl>
	</div>
	<div class="btns">
		<button class="btn">重置</button>
		<button class="btn btn-blue">完成</button>
	</div>
</div>

<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    }else{
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
         $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
         $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
         $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});
function showStore(e){
	if($(e).hasClass("cur")){
		$(".pup-mask").hide()
		$("#pupStore").css("top","100%")
		$(e).removeClass("cur")
		$("body").attr("style","overflow:initial")

	}else{
		$(".pup-mask").show()
		$("#pupStore").css("top","141px")
		$("#pupScreen").attr("style","top:100%;bottom:-100%")
		$(e).addClass("cur").siblings().removeClass("cur")
		$("body").attr("style","overflow:hidden")
	}
}
function showGenre(e){
	$(e).addClass("cur").siblings().removeClass("cur")
	$("#pupGenre").attr("style","right:0;left:0")
	$("#pupScreen").attr("style","top:100%;bottom:-100%")
	$("#pupStore").css("top","100%")
	$(".pup-mask").hide()
	$("body").attr("style","overflow:hidden")
}
function cancleGenre(e){
	$(".tab-style02 > div").removeClass("cur")
	$("#pupGenre").attr("style","right:100%;left:-100%")
	$("#pupStore").css("top","100%")
	$(".pup-mask").hide()
	$("body").attr("style","overflow:initial")
}
function showScreen(e){
	if($(e).hasClass("cur")){
		$(".pup-mask").hide()
		$("#pupScreen").attr("style","top:100%;bottom:-100%")
		$(e).removeClass("cur")
		$("body").attr("style","overflow:initial")
	}else{
		$(e).addClass("cur").siblings().removeClass("cur")
		$("#pupScreen").attr("style","top:141px;bottom:0")
		$("#pupStore").css("top","100%")
		$(".pup-mask").hide()
		$("body").attr("style","overflow:hidden")
	}
}

</script>
</body>
</html>
