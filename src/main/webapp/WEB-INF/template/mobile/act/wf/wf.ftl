<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title></title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        function changeTab(e){
            var id = $(e).attr("data-id");
            $(e).addClass("cur").siblings().removeClass("cur")
            $("#"+id).show().siblings().hide()
        }
        $().ready(function(){
            $(".appro-oper .ao-more").click(function(){
                if($(this).attr("data-status") == 0){
                    $(".appro-oper").css("bottom",104)
                    $(".appro-operMoreBox").css("bottom",0)
                    $(this).attr("data-status","1")
                }else{
                    $(".appro-oper").css("bottom",0)
                    $(".appro-operMoreBox").css("bottom",-104)
                    $(this).attr("data-status","0")
                }

            })
            $(".appro-oper .ao-at").click(function(){
                $("#mailList").show()
            })
            $(".go-back").click(function(){
                $("#mailList").hide()
                $(".appro-oper").css("bottom",0)
                $(".appro-operMoreBox").css("bottom",-104)
                $(this).attr("data-status","0")
            })
        })

        function reinitIframe(){
            var iframe = document.getElementById("billIframe");
            try{
                var bHeight = iframe.contentWindow.document.body.scrollHeight;
                var dHeight = iframe.contentWindow.document.documentElement.scrollHeight;
                var height = Math.min(bHeight, dHeight);
                iframe.height = height+50;
                // console.log(iframe.height);
            }catch (ex){}
        }

        function checkwf(b, c, a) {
            var d = $(".currproc");
            if(0 >= a) return;
            var e = d.attr("procid"),
                f = "";
            var url;
            var content;
            if(c==1){
                url = "/act/wf/submit.jhtml";
                content = '您确定要同意吗？';
            }else if(c==2){
                url = "/act/wf/reject.jhtml";
                content = '您确定要驳回吗？';
            }else{
                return;
            }

            if(confirm(content)){
                $.ajax({
                    type:'POST',
                    url:url,
                    data: {
                        taskId: a,
                        suggestion: f
                    },
                    success:function(resultMsg) {
                        if(resultMsg.type == "success"){
                            location.reload(true);
                        }else{
                            alert(resultMsg.content);
                        }

                    }
                })
            }

        }
    </script>
</head>
<body>
<div id="containt">
	<div class="apply-head clear">
		<div class="a-info clear">
			<div class="name">${wfName}</div>
		</div>
		<div class="fl no"></div>
		<div class="fr status"><i></i>
			[#if wf.stat==1]审核中
			[#elseif wf.stat==2]已完成
			[#elseif wf.stat==3]驳回
			[/#if]
		</div>
	</div>
	<div class="tab-style" style="border-bottom:solid 1px #eee">
		<div class="cur" data-id="billInfo" onclick="changeTab(this)"><b>单据详情</b></div>
		<div data-id="approvalStep" onclick="changeTab(this)"><b>审批流程</b></div>
	</div>
	<div class="tabContant" id="applyContant">
		<div class="info-box" id="billInfo">
			<iframe id="billIframe" src="[#if murl!=null]${murl}${wf.objId}[/#if]" onload="reinitIframe()"
  			 scrolling="no" height="100%" width="100%"></iframe>
			[#--<div class="h50"></div>--]
		</div>
		<div class="approval-box" id="approvalStep" style="display:none">
			<div class="step-box">
				[#list wfProcs as wfProc]
					<div class="item 
						[#if wfProc.activityState==0]
							[#if wfProc.approved==true]ok
							[#elseif wfProc.approved==false]error
							[/#if]
						[/#if]">
						<i></i>
						<div class="t">
							[#--
							<div class="pic fl"><img src="images/mobile/test/001.png" /></div>
							--]
							<div class="fl">
								<b class="name">${wfProc.activityName}</b>
								<p>${wfProc.assigneeName}</p>
								<p class="time">${(wfProc.endTime?string("yyyy-MM-dd HH:mm:ss"))!}</p>
							</div>
							<div class="status f-blue fr">
								[#if wfProc.activityState==0]
									[#if wfProc.approved==true]同意
									[#elseif wfProc.approved==false]驳回
									[/#if]
								[/#if]
							</div>
						</div>
					</div>
				[/#list]
			</div>
			<div class="appr-data">
				<div class="tab-style01">
					<div onclick="changeTab(this)" data-id="operRecord" class="cur"><b>操作记录</b></div>
					<div onclick="changeTab(this)" data-id="wfImage"><b>流程图</b></div>
				</div>
				<div class="tabContant">
					<div class="oper-record" id="operRecord">
						[#list wfProcs as wfProc]
							[#if wfProc.activityState==0]
							<div class="item">
								<span class="f-gray">${(wfProc.endTime?string("yyyy-MM-dd HH:mm:ss"))!}</span>
								<span>${wfProc.assigneeName}：
									[#if wfProc.approved==true]同意
									[#elseif wfProc.approved==false]驳回
									[/#if]
								</span>
							</div>
							[/#if]
						[/#list]
					</div>
					<div id="wfImage" style="display:none">
						<div class="flow-step clearfix" style="padding:0px 20px 20px 20px;">
							<img src="${image}" style="max-width: 100%;width:auto;" />
						</div>
					</div>
				</div>
			</div>
			
		</div>
	</div>
	[#if (wf?? && wf.stat==1
		&& (canComplete || canReject)) == true]
	<div class="info-box">
		<div class="order-btns">
			<input type="button" class="order-submit" onclick="checkwf(this, 2, ${taskId!0})" value="不同意"
			[#if (wf?? && wf.stat==1
			&& canReject) == false]disabled style="background-color: #eee !important;"[/#if] />
			
			<input type="button" class="order-submit btn-blue" onclick="checkwf(this, 1, ${taskId!0})" value="同意"
			[#if (wf?? && wf.stat==1
			&& canComplete) == false]disabled style="float:right;color: #929292 !important;;background-color: #eee !important;"[#else]style="float:right;"[/#if]  />
		</div>
	</div>
	[/#if]
</div>
<!-- pup -->

</body>
</html>
