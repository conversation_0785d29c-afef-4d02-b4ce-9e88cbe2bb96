<script type="text/javascript">
	$().ready(function() {
		var scoll_flag = parent.scoll_flag;
		if(scoll_flag!=undefined && scoll_flag==1){
			parent.scoll_flag = 0;
			$(window).scrollTop($(".wf-area-title").offset().top);//滚动到锚点位置
		}
	});
</script>			
<div class="flow-title">
       	流程名称：<input type="text" class="text disabled" value="${wfName}" readonly="">

        <div class="fr flow-btn">
        [#if wf?? && wf.stat==1 && canInterrupt]
        <input type="submit" class="button cancleButton" value="中断" onclick="interrupt(this,${wf.id})">
        [/#if]
        </div>
    </div>
    <div style="position:relative">
	<div class="flow-step clearfix">
		<img src="${image}" style="max-width:100%">
         
    </div>
    <div class="flow-replay">
    	<textarea class="text" id="opinion" placeholder="请填写您的意见~                   注：通过默认意见为”同意“、  驳回默认意见为”不同意“" style="height:200px;"  [#if (wf.stat!=1) || !(canComplete || canReject)]disabled style="background-color: #eee !important;"[/#if]></textarea>
      	<div class="flow-btn">
      		<input type="submit" class="iconButton" id="shengheButton" value="通过" onclick="checkwf(this,1,${taskId})" [#if (wf?? && wf.stat==1 && canComplete) == false]disabled
      		 style="background-color: #a2a2a2 !important;cursor: default;"[/#if]>
            <input type="submit" class="button cancleButton" value="驳回" onclick="checkwf(this,2,${taskId})" [#if (wf?? && wf.stat==1 && canReject) == false]disabled
      		 style="background-color: #a2a2a2 !important;cursor: default;"[/#if]>
      		<input type="submit" class="button cancleButton" value="驳回至申请人" onclick="checkwf(this,3,${taskId})" [#if (wf?? && wf.stat==1 && canReject) == false]disabled
      		 style="background-color: #a2a2a2 !important;cursor: default;"[/#if]>
      	</div>
    </div>
    </div>