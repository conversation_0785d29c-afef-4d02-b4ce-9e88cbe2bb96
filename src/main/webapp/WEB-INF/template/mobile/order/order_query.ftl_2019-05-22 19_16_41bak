<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<style>
.order-list li .btns {
    margin-top: 30px;
}

</style>
</head>
<body>
<div class="header">
	<a href="javascript:history.go(-1);" class="go-back"></a>
	<div class="h-txt">订单查询</div>
</div>
<div class="search-box"><input type="search" class="txt" id="orderSn" placeholder="请输入订单编号"></div>
<div class="containt">
	<div class="order-list">
    	<ul>
        	
        </ul>
    </div>
</div>
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
$().ready(function(){
	ajaxListData()
	//根据订单编号搜索订单
	$("#orderSn").blur(function(){
		ajaxListData()
	})
	$("#orderSn").keypress(function (e) {
        if (e.which == 13) {
        	ajaxListData()
        }
	})
})
function ajaxListData(){
	$('.order-list').find('ul').empty()
	$.ajax({
		type:'POST',
		url:'/b2b/order/list_data.jhtml',
		data:{orderSn:$('#orderSn').val()},
		success:function(data) {
			if(data.type == 'success'){
				var rows = JSON.parse(data.content).content;
				for (var i = 0; i < rows.length; i++) {
	            	var row = rows[i];
	            	//订单状态
	            	if(row.order_status == 5){
	            		var order_status = "已下达"
	            	}
	            	if(row.order_status == 6){
	            		var order_status = "已审核"
	            	}
	            	if(row.order_status == 7){
	            		var order_status = "已保存"
	            	}
	            	if(row.order_status == 2){
	            		var order_status = "已完成"
	            	}
	            	if(row.order_status == 3){
	            		var order_status = "已作废"
	            	}
	            	//支付状态
	            	if(row.payment_status == 0){
	            		var payment_status = "未支付"
	            	}
	            	if(row.payment_status == 1){
	            		var payment_status = "部分支付"
	            	}
	            	if(row.payment_status == 2){
	            		var payment_status = "完全支付"
	            	}
	            	//配送状态
	            	if(row.shipping_status == 0){
	            		var shipping_status = "未发货"
	            	}
	            	if(row.shipping_status == 1){
	            		var shipping_status = "部分发货"
	            	}
	            	if(row.shipping_status == 2){
	            		var shipping_status = "完全发货"
	            	}
	            	if(row.shipping_status == 3){
	            		var shipping_status = "已收货"
	            	}
	            	//计算订单金额
	            	//订单明细
				   	var order_items = row.order_items
				   	var orderTotalPrice = 0;//订单总金额
				   	var perNum = 0;
				   	var productPrice = 0;
				   	for (var j = 0; j < order_items.length; j++) {
				   		var order_item = order_items[j]
				   		//如果产品没有箱支转换率
				   		if(order_item.per_branch == "0" || order_item.branch_per_box == "0" || order_item.per_branch == null || order_item.branch_per_box == null){
				   			//按平方数算价钱
				   			perNum = parseFloat(order_item.quantity).toFixed(6)
					   		productPrice = (parseFloat(perNum)*parseFloat(order_item.apply_pricex)).toFixed(2)
				   		}else{
				   			perNum = (parseFloat(order_item.per_branch)*parseFloat(order_item.branch_quantity)).toFixed(6)
					   		productPrice = (parseFloat(perNum)*parseFloat(order_item.apply_pricex)).toFixed(2)
				   		}
						orderTotalPrice = (parseFloat(orderTotalPrice) + parseFloat(productPrice)).toFixed(2)
					}
	    			var html = "<li>"
		            	+"<div class='txt'>"
		            	+"	<p>订单编号："+row.sn+"</p>"
		            	+"	<p>仓库："+row.warehouse_name+"</p>"
		            	+"</div>"
		                +"<div class='status'>"
		                +"	<b class='price f-red'>￥"+orderTotalPrice+"</b>"
		                +"    <span style='display:block'>(订单状态："+order_status+")</span>"
		                +"    <span style=''>(配送状态："+shipping_status+")</span>"
		                +"</div>"
		                +"<div class='btns'>"
		                +" 	<button class='btn-blue btn' onclick='orderDetail(\""+row.sn+"\",\""+row.id+"\")'>查看详情</button>"
		                +"</div>"
		            	+"</li>";
	    			$('.order-list').find('ul').append(html)
				}
			}
		}
	})
}
//订单详情
function orderDetail(orderSn,id){
	window.location.href = "order_detail.jhtml?orderSn="+orderSn+"&id="+id
}
</script>
</body>
</html>
