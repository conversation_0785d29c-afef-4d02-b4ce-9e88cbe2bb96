<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<style>
	.oPro-list .item{padding:20px 10px; border-bottom: solid 1px #f7f7f7;}
	.oPro-list .item .data{overflow:hidden;background:#f7f7f7;padding: 5px 8px;margin-top: 23px;font-size: 0.75rem;line-height: 1.8;border-radius: 2px; display:none}
    .oPro-list .item .data span{ width:100%; float:left;color:#888}
	.oPro-list .item.on .data{display:block}
	.oPro-list .item div {margin-top: -10px;}
	.order-tab .title .btn {margin-left: 5px;}
</style>
</head>
<body>
<!-- 
<div class="header">
	<a href="javascript:history.go(-1);" class="go-back"></a>
	<div class="h-txt">订单详情</div>
</div>
 -->
<div class="containt">
	<div class="order-tab">
		<div class="title" id="baseInfo">基本信息
			<a href='javascript:void(0);' class='lbtn-orgn btn' onclick='copyOrder()'><i class='icon ico-add'></i>复制</a>
		</div>
		<div class="dl-style">
			<input type="hidden" id="wfId"/>
			<dl>
				<dt class="f-black">订单编号</dt>
				<dd><input type="text" class="txt" disabled id="orderSn" value="${orderSn}"></dd>
			</dl>
			<dl>
				<dt class="f-black">客户</dt>
				<dd><input type="text" class="txt" disabled id="storeName" value="${order.store_name}" style="width:100%">
			
				</dd>
			</dl>
			<dl>
				<dt class="f-black">业务类型</dt>
				<dd><input type="text" class="txt" disabled id="business_type_value">
					<input type="hidden" id="business_type_id" />
				</dd>
			</dl>
			<dl>
				<dt>SBU</dt>
				<dd><input type="text" class="txt" disabled value="${order.sbu.name}"/>
					<input type="hidden" id="sbuId" value="${order.sbu.id}"/>
				</dd>
			</dl>
			<dl>
				<dt class="f-black">机构</dt>
				<dd><input type="text" class="txt" disabled id="sale_org_name">
					<input type="hidden" id="sale_org_id" />
				</dd>
			</dl>
			<dl>
				<dt class="f-black">付款方式</dt>
				<dd><input type="text" class="txt" disabled id="paymentMethodName"></dd>
			</dl>
			<dl>
				<dt class="f-black">配送方式</dt>
				<dd>
					<input type="text" class="txt" disabled id="shippingMethodName">
					<input type="hidden" id="shippingMethodId">
				</dd>
			</dl>
			<dl>
				<dt class="f-black">区域经理</dt>
				<dd><input type="text" class="txt" disabled id="store_member_name"></dd>
			</dl>
			<dl>
				<dt class="f-black">仓库</dt>
				<dd><input type="text" class="txt" disabled id="warehouseName">
					<input type="hidden" id="warehouseId">
				</dd>
			</dl>
			<dl>
				<dt class="f-black">仓库类型</dt>
				<dd><input type="text" class="txt" disabled id="warehouseType"></dd>
			</dl>
			<dl>
				<dt class="f-black">经营组织</dt>
				<dd><input type="text" class="txt"  id="organizationName" style="width:100%" disabled>
					<input type="hidden" id="organizationId">
				</dd>
			</dl>
			<dl>
				<dt class="f-black">下单时间</dt>
				<dd><input type="text" class="txt" disabled id="orderDate"></dd>
			</dl>
			<dl>
				<dt class="f-black">备注</dt>
				<dd><textarea class="txt" id="baseInfoMemo"></textarea></dd>
			</dl>
		</div>
	</div>
	<div class="order-tab mt8">
		<div class="title">订单明细</div>
		<div class="oPro-list dl-style" style="position:relative">
			
		</div>
	</div>
	<div class="order-tab mt8">
		<div class="title">订单汇总</div>
		<div class="dl-style">
		 <!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积 -->
		[#if order.sbu.id!=3]
			<dl>
				<dt>订单箱数</dt>
				<dd><span id="orderBoxNum">0</span></dd>
			</dl>
			<dl>
				<dt>订单支数</dt>
				<dd><span id="orderBranchNum">0</span></dd>
			</dl>
		[/#if]	
			<dl>
				<dt>订单平方数</dt>
				<dd><span id="orderPerNum">0</span></dd>
			</dl>
		[#if order.sbu.id!=3]
			<dl>
				<dt>订单重量</dt>
				<dd><span id="orderWeightNum">0</span></dd>
			</dl>
			<dl>
				<dt>订单体积</dt>
				<dd><span id="orderVolumeNum">0</span></dd>
			</dl>
		[/#if]
			<dl [#if hiddenAmount==0]hidden[/#if]>
				<dt>订单金额</dt>
				<dd><span id="orderTotalPrice">0</span></dd>
			</dl>
			<dl [#if hiddenAmount==0]hidden[/#if]>
				<dt>客户余额</dt>
				<dd><span id="orderBalancePrice">0</span></dd>
			</dl>
		
		</div>
	</div>
	<div class="order-tab mt8">
		<div class="title" onclick='onshow(this)'>收货地址</div>
		<!-- <input type="text" id="receiveInfooutTradeNo"/> -->
		<input type="hidden" id="receiveInfoAreaId"/>
		
		<!-- <div class="dl-style">
			<dl>
				<dt>收货人</dt>
				<dd><span id="receiveInfoName"></span></dd>
			</dl>
			<dl>
				<dt>收货电话</dt>
				<dd><span id="receiveInfoPhone"></span></dd>
			</dl>
			<dl>
				<dt>收货地区</dt>
				<dd><span id="receiveInfoArea"></span></dd>
			</dl>
			<dl>
				<dt>收货地址</dt>
				<dd><span id="receiveInfoAddress"></span></dd>
			</dl>
			<dl>
				<dt>收货地址外部编码</dt>
				<dd><span id="receiveInfooutTradeNo"></span></dd>
			</dl>
		</div> -->
		
		<div class="adr-box">
<!-- 			<input type="hidden" id="out_trade_no" value="${storeAddress.out_trade_no}"/> -->
<!-- 			<input type="hidden" id="areaId" value="${storeAddress.area}"/> -->
			<div class="t"><span id="receiveInfoName" class="name"></span><span id="receiveInfoPhone"></span></div>
			<div class="c"><span id="receiveInfoArea"></span> | <span id="receiveInfoAddress"></span></div>
			<div style="display:none;"><span id="receiveInfooutTradeNo"></span></div>
		</div>
	</div>
	<div class="order-tab mt8" style="margin-bottom:55px;">
		<div class="title">附件信息</div>
		<div class="atta-list">
			
		</div>
	</div>
	
	<div class="order-btns">
	
	</div>
	
</div>

<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
$().ready(function(){
	
   	[#if order.orderStatus == "unaudited"]
   		var order_status = "已下达"
   	[/#if]
   	[#if order.orderStatus == "audited"]
   		var order_status = "已审核"
   	[/#if]
   	[#if order.orderStatus == "saved"]
   		var order_status = "已保存"
   	[/#if]
   	[#if order.paymentStatus == "unpaid"]
   		var payment_status = "未支付"

   	[/#if]
   	[#if order.paymentStatus == "partialPayment"]
   		var payment_status = "部分支付"
   		
   	[/#if]
   	[#if order.paymentStatus == "paid"]
   		var payment_status = "完全支付"
   	[/#if]
   	
   
  	//基本信息
   	$("#storeName").val("${order.store.name}")
   	$("#business_type_value").val("${order.businessType.value}")
   	$("#business_type_id").val("${order.businessType.id}")
   	$("#store_member_name").val("${order.regionalManager.name}")
   	$("#shippingMethodId").val("${order.shippingMethod.id}")
   	$("#shippingMethodName").val("${order.shippingMethodName}")
   	$("#paymentMethodName").val("${order.paymentMethodName}")
   	$("#sale_org_name").val("${order.saleOrg.name}")
   	$("#sale_org_id").val("${order.saleOrg.id}")
   	$("#warehouseId").val("${order.warehouse.id}")
   	$("#warehouseName").val("${order.warehouse.name}")
   	$("#warehouseType").val("${order.warehouse.typeSystemDict.value}")
   	$("#organizationName").val("${order.organization.name}")
   	$("#organizationId").val("${order.organization.id}")
   	$("#baseInfoMemo").val("${order.memo}")
   	$("#wfId").val("${order.wfId}") 
   	$("#orderDate").val("${createDate}")
   	
   	/**订单状态:unconfirmed未确认，confirmed已确认，completed已完成，cancelled已作废，deleted已删除，unaudited未审核，audited已审核，saved已保存，accepted已接受，refused已拒绝**/
	[#if (order.orderStatus == "saved") && order.shippingStatus == "unshipped" && order.storeMember.name == storeMember.name]
		$(".containt .order-btns").append("<input type='button' class='order-submit' onclick='cancel_order()' value='作废' style='background:#ffbc65;' />")		
	[/#if]
    [#if (order.wfId == null || order.wfId == "") && order.orderStatus != "cancelled" && order.storeMember.name == storeMember.name]
   		$(".containt .order-btns").append("<input type='button' class='order-submit btn-blue' onclick='saveOrder()' value='支付' style='float:right;' />")
    [/#if]
    [#if order.wfId != null && order.wfId != "" && order.wfState != 2 && order.storeMember.name == storeMember.name]
		$("#baseInfo").append("<a href='javascript:void(0);' class='lbtn-orgn btn' onclick='interrupt()'><i class='icon ico-add'></i>中断</a>")
    [/#if]
   	
   	//订单明细
   	var order_items = JSON.parse('${orderItem_json}')
   	var orderBoxNum = 0;
   	var orderBranchNum = 0;
   	var orderPerNum = 0;
   	var orderWeightNum = 0;
   	var orderVolumeNum = 0;
   	var orderTotalPrice = 0;
   	var perNum = 0;
   	var productPrice = 0;
   	var hiddenAmount=${hiddenAmount};
   	for (var i = 0; i < order_items.length; i++) {
   		var order_item = order_items[i]
   		//如果产品没有箱支转换率
   		if(order_item.per_branch == "0" || order_item.branch_per_box == "0" || order_item.per_branch == null || order_item.branch_per_box == null){
   			//按平方数算价钱
   			perNum = parseFloat(order_item.quantity).toFixed(6)
   			if(hiddenAmount==0){
   				   productPrice="***";
   			}else{
   				productPrice = (parseFloat(perNum)*parseFloat(order_item.apply_pricex)).toFixed(2)
   			 
   			}
	   		
   		}else{
   			perNum = (parseFloat(order_item.per_branch)*parseFloat(order_item.branch_quantity)).toFixed(6)
   	   		if(hiddenAmount==0){
   	   		  productPrice="***";
   				
   			}else{
   			  productPrice = (parseFloat(perNum)*parseFloat(order_item.apply_pricex)).toFixed(2)
   			}
   		}
   		
   		var html = "<div class='item'  onclick='onShow(this)' id='itemLists'>"
			+"<div class='fl' style='position:absolute'>"+order_item.p_name+"</div>"
			+"<div class='fr f-red'>￥"+productPrice+"</div>"
			+"	<div class='data'>"
			+"		<span><div style='float:left'>产品等级：</div><div style='float:right'>"+order_item.levelName+"</div></span>"
			+"		<span><div style='float:left'>产品型号：</div><div style='float:right'>"+order_item.model+"</div></span>";
			//如果产品没有箱支转换率
	   		if(order_item.per_branch == "0" || order_item.branch_per_box == "0" || order_item.per_branch == null || order_item.branch_per_box == null){
	   			html += 
	   			"		<span><div style='display:none'>下单箱数：</div><div style='display:none'>-</div></span>"
				+"		<span><div style='display:none'>支数：</div><div style='display:none'>-</div></span>"
				+"		<span><div style='display:none'>零散支数：</div><div style='display:none'>-</div></span>"
	   		}else{
	   			html += 
	   			"		<span><div style='float:left'>下单箱数：</div><div style='float:right'>"+order_item.box_quantity+"</div></span>"
				+"		<span><div style='float:left'>支数：</div><div style='float:right'>"+order_item.branch_quantity+"</div></span>"
				+"		<span><div style='float:left'>零散支数：</div><div style='float:right'>"+order_item.scattered_quantity+"</div></span>"
	   		}
	   		if($('#sbuId').val()==3 && order_item.pUnit=="m2"){
	   		   html += 
	   			"		<span><div style='float:left'>宽：</div><div style='float:right'>"+order_item.length+"</div></span>"
				+"		<span><div style='float:left'>高：</div><div style='float:right'>"+order_item.width+"</div></span>"
				
	   		
	   		}
	   		if(hiddenAmount != 0){
			html +=
			"		<span><div style='float:left'>数量：</div><div style='float:right'>"+order_item.quantity+"</div></span>"
			+"		<span><div style='float:left'>单价：</div><div style='float:right' class='f-red'>￥"+parseFloat(order_item.apply_pricex).toFixed(2)+"</div></span>"
			}
			//如果产品没有箱支转换率
	   		if(order_item.per_branch == "0" || order_item.branch_per_box == "0" || order_item.per_branch == null || order_item.branch_per_box == null){
	   			html += 
	   			"		<span><div style='display:none'>体积：</div><div style='display:none'>-</div></span>"
	   		}else{
	   			html += 
	   			"		<span><div style='float:left'>体积：</div><div style='float:right'>"+(parseFloat(order_item.volume)*parseInt(order_item.box_quantity)).toFixed(6)+"</div></span>"
	   		}
	   		
			//如果产品没有箱支转换率
			if(order_item.per_branch == "0" || order_item.branch_per_box == "0" || order_item.per_branch == null || order_item.branch_per_box == null){
	   			html += 
	   			"		<span><div style='display:none'>重量：</div><div style='display:none'>-</div></span>"
	   		}else{
	   			html +=
	   				"		<span><div style='float:left'>重量：</div><div style='float:right'>"+(parseFloat(order_item.weight)*parseFloat(order_item.quantity)).toFixed(6)+"</div></span>"
	   		}
			html+="	</div>"
			+"<input type='hidden' id='productGrade' value='"+order_item.level_Id+"'/>"/* 产品等级 */
			+"<input type='hidden' id='model' value='"+order_item.model+"'/>"/* 产品型号 */
			+"<input type='hidden' value='"+order_item.apply_pricex+"'/>"/* 每平方数单价 */
			+"<input type='hidden' id='weight' value='"+order_item.weight+"'/>"/* 每平方数重量 */
			+"<input type='hidden' id='volume' value='"+order_item.volume+"'/>"/* 每箱体积 */
			+"<input type='hidden' value='"+order_item.per_box+"'/>"/* 每箱平方数 */
			+"<input type='hidden' id='perBranch' value='"+order_item.per_branch+"'/>"/* 每支平方数 */
			+"<input type='hidden' id='quantity' value='"+order_item.quantity+"'/>"/* 总平方数 */
			+"<input type='hidden' id='branchQuantity' value='"+order_item.branch_quantity+"'/>"/* 支数 */
			+"<input type='hidden' id='boxQuantity' value='"+order_item.box_quantity+"'/>"/* 箱数 */
			+"<input type='hidden' id='scatteredQuantity' value='"+order_item.scattered_quantity+"'/>"/* 零散支数 */
			+"<input type='hidden' id='branchPerBox' value='"+order_item.branch_per_box+"'/>"/* 每箱支数 */
			
			+"<input type='hidden' id='vonderCode' value='"+order_item.vonder_code+"'/>"
			+"<input type='hidden' id='orderItemId' value='"+order_item.id+"'/>"
			+"<input type='hidden' id='productId' value='"+order_item.product+"'/>"
			+"<input type='hidden' id='productName' value='"+order_item.name+"'/>"
			+"<input type='hidden' id='description' value='"+order_item.description+"'/>"
			+"<input type='hidden' id='woodTypeOrColor' value='"+order_item.wood_type_or_color+"'/>"
			+"<input type='hidden' id='model' value='"+order_item.model+"'/>"
			+"<input type='hidden' id='proPriceHeadPrice' value='"+order_item.pro_price_head_price+"'/>"
			+"<input type='hidden' id='price' value='"+order_item.price+"'/>"
			+"<input type='hidden' id='saleOrgPrice' value='"+order_item.sale_org_price+"'/>";
			if(order_item.delivery_time != null){
				html = html + "<input type='hidden' id='deliveryTime' value='"+order_item.delivery_time+"'/>";
			}
			if(order_item.deliver_date != null){
				html = html + "<input type='hidden' id='deliveryDate' value='"+order_item.deliver_date+"'/>";
			}
			if(order_item.seller_memo != null){
				html = html +"<input type='hidden' id='sellerMemo' value='"+order_item.seller_memo+"'/>";
			}
			if(order_item.price_apply_item != null){
				html = html +"<input type='hidden' id='priceApplyItemId' value='"+order_item.price_apply_item+"'/>";
			}
			html = html + "</div>";
		$(".oPro-list").append(html)
		orderBoxNum = orderBoxNum + parseInt(order_item.box_quantity== null? 0:order_item.box_quantity)
		orderBranchNum = orderBranchNum + parseInt(order_item.branch_quantity == null? 0:order_item.branch_quantity)
		orderPerNum = (parseFloat(orderPerNum) + parseFloat(perNum)).toFixed(6)
		orderWeightNum = (parseFloat(orderWeightNum) + parseFloat(perNum)*parseFloat(order_item.weight == null? 0:order_item.weight)).toFixed(6)
		orderVolumeNum = (parseFloat(orderVolumeNum) + parseFloat(order_item.box_quantity == null?0:order_item.box_quantity)*parseFloat(order_item.volume == null? 0:order_item.volume)).toFixed(6)
		orderTotalPrice = (parseFloat(orderTotalPrice) + parseFloat(productPrice)).toFixed(2)
	}
     	
   	//订单汇总
   	$("#orderBoxNum").html(orderBoxNum)
   	$("#orderBranchNum").html(orderBranchNum)
   	$("#orderPerNum").html(orderPerNum)
   	$("#orderWeightNum").html(orderWeightNum)
   	$("#orderVolumeNum").html(orderVolumeNum)
   	$("#orderTotalPrice").html("￥"+orderTotalPrice)
   	[#if order.storeBalance != null]
 	  	$("#orderBalancePrice").html("￥"+${order.storeBalance})
	[/#if]
   	//收货信息
   	$("#receiveInfoName").html("${order.consignee}")
   	$("#receiveInfoPhone").html("${order.phone}")
   	$("#receiveInfoArea").html("${order.area.fullName}")
   	$("#receiveInfoAddress").html("${order.address}")
   	$("#receiveInfooutTradeNo").html("${order.addressOutTradeNo}")
   	$("#receiveInfoAreaId").val("${order.area.id}")
   	//附件信息
   	var order_attachs = JSON.parse('${orderAttach_json}')
   	for (var i = 0; i < order_attachs.length; i++) {
   		var order_attach = order_attachs[i]
   		var memo = ""
   		if (order_attach.memo != null) {
   			memo = order_attach.memo
   		}
   		var html = "<div class='item'>"
			+"<div class='tit'>附件 <a href='"+order_attach.url+"' target='_blank'><span class='name fr'>"+order_attach.file_name+"</span></a></div>"
			+"<textarea class='txt' placeholder='备注'>"+memo+"</textarea>"
			+"<input type='hidden' id='attachName' value='"+order_attach.file_name+"'/>"
			+"<input type='hidden' id='attachUrl' value='"+order_attach.url+"'/>"
			+"<input type='hidden' id='attachSuffix' value='"+order_attach.suffix+"'/>"
			+"<input type='hidden' id='attachMemo' value='"+memo+"'/>"
			+"</div>"; 
		$(".atta-list").append(html)

   	}
})
function onShow(e){
	$(e).toggleClass("on")	
} 
function interrupt(){
	var flag = confirm("你确定要中断吗？")
	if(flag == true){
		$.ajax({
			type:'POST',
			url:'/act/wf/interrupt.jhtml',
			data:{wfId:$("#wfId").val()},
			success:function(data) {
				if(data.type == "success"){
					alert(data.content)
					location.reload() 
				}
			}
		})
	}
}

//确定支付
function saveOrder(){
	var flag = confirm("您确定要支付吗？");
	if (flag != true) {
		return ;
	}
	var jsonData = {
		'orderId':${order.id},
		'id':${order.id},
		'isCheckWf':true,
		/* 'storeId':$('#storeId').val(), */
		/* 'storeName':$('#name').val(), */
		'businessTypeId':$('#business_type_id').val(),
		/* 'businessTypeName':$('#business_type_value').val(), */
		'saleOrgId':$('#sale_org_id').val(),
		'organizationId':$('#organizationId').val(),
		'warehouseId':$('#warehouseId').val(),
		'warehouseName':$('#warehouseName').val(),
		/* 'typeSystemDictId':$('#type_system_dict_id').val(), */
		/* 'paymentMethodId':$('#paymentMethodId').val(), */
		'shippingMethodId':$('#shippingMethodId').val(),
		'consignee':$('#receiveInfoName').html(),
		'phone':$('#receiveInfoPhone').html(),
		'addressOutTradeNo':$('#receiveInfooutTradeNo').html(),
		'areaId':$('#receiveInfoAreaId').val(),
		'areaName':$('#receiveInfoArea').html(),
		'salesAreaName':$('#receiveInfoArea').html(),
		'address':$('#receiveInfoAddress').html(),
		'memo':$('#baseInfoMemo').val(),
		'storeBalance':$('#orderBalancePrice').html().substring(1),
		'sbuId':$('#sbuId').val()
	}
	if($('#storeMemberId').val() != "null"){
		jsonData['regionalManagerId'] = $('#storeMemberId').val()
	}
	var products = $('.oPro-list').find('div[id="itemLists"]')
	for (var i = 0; i < products.length; i++) {
		jsonData['orderItems['+i+'].vonderCode'] = $(products[i]).find('input[id="vonderCode"]').val()
		jsonData['orderItems['+i+'].id'] = $(products[i]).find('input[id="orderItemId"]').val()
		jsonData['orderItems['+i+'].product.id'] = $(products[i]).find('input[id="productId"]').val()
		jsonData['orderItems['+i+'].name'] = $(products[i]).find('input[id="productName"]').val()
		jsonData['orderItems['+i+'].description'] = $(products[i]).find('input[id="description"]').val()
		jsonData['orderItems['+i+'].woodTypeOrColor'] = $(products[i]).find('input[id="woodTypeOrColor"]').val()
		jsonData['orderItems['+i+'].model'] = $(products[i]).find('input[id="model"]').val()
		jsonData['orderItems['+i+'].productLevel.id'] = $(products[i]).find('input[id="productGrade"]').val()
		jsonData['orderItems['+i+'].boxQuantity'] = $(products[i]).find('input[id="boxQuantity"]').val()
		jsonData['orderItems['+i+'].branchQuantity'] = $(products[i]).find('input[id="branchQuantity"]').val()
		jsonData['orderItems['+i+'].branchPerBox'] = $(products[i]).find('input[id="branchPerBox"]').val()
		jsonData['orderItems['+i+'].perBranch'] = $(products[i]).find('input[id="perBranch"]').val()
		jsonData['orderItems['+i+'].scatteredQuantity'] = $(products[i]).find('input[id="scatteredQuantity"]').val()
		jsonData['orderItems['+i+'].proPriceHeadPrice'] = $(products[i]).find('input[id="proPriceHeadPrice"]').val()//价格表原价
		jsonData['orderItems['+i+'].quantity'] = $(products[i]).find('input[id="quantity"]').val()		
		jsonData['orderItems['+i+'].price'] = $(products[i]).find('input[id="price"]').val()
		jsonData['orderItems['+i+'].saleOrgPrice'] = $(products[i]).find('input[id="saleOrgPrice"]').val()
		jsonData['orderItems['+i+'].volume'] = $(products[i]).find('input[id="volume"]').val()
		jsonData['orderItems['+i+'].weight'] = $(products[i]).find('input[id="weight"]').val()
		jsonData['orderItems['+i+'].deliveryTime'] = $(products[i]).find('input[id="deliveryTime"]').val()
		jsonData['orderItems['+i+'].deliveryDate'] = $(products[i]).find('input[id="deliveryDate"]').val()
		jsonData['orderItems['+i+'].sellerMemo'] = $(products[i]).find('input[id="sellerMemo"]').val()
		if($(products[i]).find('input[id="priceApplyItemId"]').val() != null && $(products[i]).find('input[id="priceApplyItemId"]').val() != ""){
			jsonData['orderItems['+i+'].priceApplyItem.id'] = $(products[i]).find('input[id="priceApplyItemId"]').val()
		}
	}
	var attachs = $('.atta-list').find('div[class="item"]')
	for (var i = 0; i < attachs.length; i++) {
		jsonData['orderAttachs['+i+'].name'] = $(attachs[i]).find('input[id="attachName"]').val()
		jsonData['orderAttachs['+i+'].url'] = $(attachs[i]).find('input[id="attachUrl"]').val()
		jsonData['orderAttachs['+i+'].suffix'] = $(attachs[i]).find('input[id="attachSuffix"]').val()
		jsonData['orderAttachs['+i+'].memo'] = $(attachs[i]).find('textarea[id="attachMemo"]').val()		
	}
	
	$.ajax({
		type:'POST',
		url:'/b2b/order/update.jhtml?stat=1',
		data:jsonData,
		success:function(data) {
			alert(data.content);
			if(data.type == "success"){
				check_wf();//执行新流程审核方法
				/* 切换新流程 支付审核方法注释 20200229 肖光春
				$.ajax({
					type:'POST',
					url:'/wf/wf_obj_config/get_config.jhtml',
					data:{obj_type_id:47,objid:${order.id}},
					success:function(data_) {
						if(data_.type == "success"){
							jsonData['objConfId'] = data_.objx[0].id
							$.ajax({
								type:'POST',
								url:'/b2b/order/check_wf.jhtml',
								data:jsonData,
								success:function(data) {
									if(data.type == "success"){
										location.reload() 
									}
								}
							})
						}
					}
				})
				*/
			}
		}
	})
}
//流程审核方法     创建人：肖光春 时间：2020/02/29 
function check_wf(){
	var objTypeId = 100025;
	var modelId = model($("#sbuId").val(),"正式");
//	var modelId = model($("#sbuId").val(),"测试");
	var url="/b2b/order/check_wf.jhtml?orderId=${order.id}&modelId="+modelId+"&objTypeId="+objTypeId;
	$.ajax({
		type:'POST',
		url:url,
		success:function(data) {
			if(data.type == "success"){
				window.location.href = "/mobile/index.jhtml"
			}
		}
	});
}
//查找对应流程模板 创建人：肖光春 时间：2020/02/29 
function model(sbuId,versions){
	var json = '{"正式":{"1":"47997","2":"48464","3":"55016","4":"48102","5":"55001","6":"","7":"","8":"55027"},';
	json +='"测试":{"1":"47997","2":"48464","3":"55016","4":"48102","5":"55001","6":"","7":"","8":"167501"}}';
	var model = JSON.parse(json);
	return model[versions][sbuId];
}
//订单作废
function cancel_order(){
	var flag = confirm("您确定要作废该订单吗？")
	if(flag == true){
		$.ajax({
			type:'POST',
			url:'/b2b/order/cancel.jhtml?ids=${order.id}',
			success:function(resultMsg) {
				if(resultMsg.type == "success"){
					alert(resultMsg.content)
					location.reload() 
				}
			}
		})
	}
}
function copyOrder() {
	window.location.href = "/mobile/order/copy_order.jhtml?id=${order.id}&sbuId=${order.sbu.id}";
}
</script>
</body>
</html>
