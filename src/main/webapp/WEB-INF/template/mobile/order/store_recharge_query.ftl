<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
//当前页数
var pageNumber = 1;
// 每页数据量
var pageSize = 25;

$().ready(function(){
	ajaxListData()
	//根据充值编号搜索充值
	/* $("#sn").blur(function(){
		ajaxListData()
	}) */
	/* $("#sn").keypress(function (e) {
        if (e.which == 13) {
        	ajaxListData()
        }
	}) */
});
function ajaxListData(){
	pageNumber = 1;
	$('.order-list').find('ul').empty()
	$.ajax({
		type:'POST',
		url:'/member/store_recharge/list_data.jhtml',
		data:{sn:$('#sn').val(),pageNumber:pageNumber,pageSize:pageSize},
		success:function(data) {
			fillData(data);
		}
	})
}

//获取当前浏览器中的滚动事件
$(window).off("scroll").on("scroll", function () {
	//获取当前浏览器的滚动条高度
     var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight; 
   	//判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
     if (scrollHeight <= ($(window).scrollTop() + $(window).height())) {
         setTimeout(function () {
       		$.ajax({
       			type:'POST',
       			url:'/member/store_recharge/list_data.jhtml',
       			data:{sn:$('#sn').val(),pageNumber:pageNumber,pageSize:pageSize},
       			success:function(data) {
       				fillData(data);
       			}
       		})
         },500);
         //设置当前页数
         pageNumber += 1;
     }
});

// 填充数据
function fillData(data) {
	if(data.type == 'success'){
		var rows = JSON.parse(data.content).content;
		for (var i = 0; i < rows.length; i++) {
        	var row = rows[i];
        	if(row.doc_status == 0){
        		var status = "已保存"
        	}
        	if(row.doc_status == 1){
        		var status = "已提交"
        	}
        	if(row.doc_status == 2){
        		var status = "已处理"
        	}
        	if(row.doc_status == 3){
        		var status = "已作废"
        	}
        	
        	var html = "<li onclick='storeRechargeDetail(\""+row.id+"\",\""+row.create_date+"\")'>"
        		 + "<a>"  
        		+ "   <div class='t'>" 
        		+ "   	  <div class='no fl'>充值编号："+row.sn+"</div>";
        		
        	if (row.doc_status == 0 || row.doc_status == 1) {
        		html += "   	  <div class='status btn-lred'>"+ status +"</div>";
        	} else if (row.doc_status == 2) {
        		html += "   	  <div class='status btn-lblue'>"+ status +"</div>";
        	} else {
        		html += "   	  <div class='status btn-lgray'>"+ status +"</div>";
        	}
        		
        	html += "   </div>"
        		+ "   <div class='c'>" 
        		+ "   	  <p><span>充值客户：</span>"+row.store_name+"</p>" 
        		+ "   	  <p><span>充值金额：</span>￥"+row.amount.toFixed(2)+"</p>" 
        		/* + "   	  <p><span>充值时间：</span>"+row.create_date+"</p>"  */
        		+ "   </div>"
        		+ "</a>"
        		+ "</li>";
        	
			$('.order-list').find('ul').append(html)
		}
	}
}

//充值详情
function storeRechargeDetail(id,createDate){
	window.location.href = "store_recharge_detail.jhtml?id="+id+"&createDate="+createDate
}
function search(e) {
	ajaxListData();
}
</script>
</head>
<body>

<div id="containt">
	<!-- <div class="header">
		<a href="javascript:history.go(-1);" class="go-back"></a>
		<div class="h-txt">充值查询</div>
	</div> -->

	<div class="search-box" style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
		<input type="text" class="txt" placeholder="请输入充值编号" id="sn"/>
		<input type="submit" class="btn" onclick="search(this)" value="搜索">
	</div>
	
	<div class="order-list" style="position:relative;padding-top: 54px;">
    	<ul class="list-style02">
    	
    	</ul>
    </div>
    
</div>
</body>
</html>
