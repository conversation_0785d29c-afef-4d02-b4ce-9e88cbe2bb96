<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title>25周年</title>
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<style>
	div
	{
		font-size:20px;
	}
	input
	{
		font-size:18px;
	}
 .test{
    background-color: #008CBA; 
	 border-radius: 10%;
    border: 1px;
    color: white;
    padding: 10px 30px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
}


table
{
    border-collapse:collapse;
}
	
table,td,th
{
	font-size:20px;
    border: 2px solid black;
	text-align:center;
	height:30px;
	padding:10px;
	margin-left:auto;
	margin-right:auto;
}

</style>
</head>
<body class="bgFFF">
<div class="header">
	<a href="javascript:history.go(-1);" class="go-back"></a>
	<div class="h-txt">
	25周年超级盛典数据查看
	</div>
	
</div>
<div class="dl-style">

			<dl>
				<dt>战区</dt>
				<dd>
					<select class="txt arrow" id="zone">
						<option value="" >请选择</option>
						<option value="一战区" >一战区</option>
						<option value="二战区" >二战区</option>
						<option value="三战区" >三战区</option>
						<option value="四战区" >四战区</option>
					</select>
					
					
				</dd>
			</dl>
			<dl>
				<dt>平台</dt>
				<dd>
					<select class="txt arrow" id="saleOrgId">
					<option value="" >请选择</option>
					[#list themeDisplays as themeList]
						<option value="${themeList.id}" >${themeList.name}</option>
					[/#list]
					</select>
					
					
				</dd>
			</dl>
<input type="hidden" class="test" id="themeId" placeholder=${themeId}>
 <center><button type="button" class="test" onclick="searchPro()">提交</button></center>
 </div>

<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">


function searchPro(){
  var zone=$('#zone').val();
  var themeId=$('#themeId').val();
  var saleOrgId=$('#saleOrgId').val();

  window.location.href="list_theme_data.jhtml?themeId="+themeId+"&zone="+zone+"&saleOrgId="+saleOrgId;
}
</script>
<table border="2" cellspacing="0">
<tr>
	<th>平台 </th>
	<th>类别</th>
	<th>目标</th>
	<th>累计</th>
	<th>达成率%</th>
	<th>昨天</th>
	<th>今天</th>
	    
</tr>   
 [#list themeLists as themeList]
<tr>
  	<td>${themeList.name}</td>
	<td>${themeList.category}</td>
	<td>${themeList.target}</td>
	<td>${themeList.cumulative}</td>
	<td>${themeList.rate}</td>
	<td>${themeList.toCumulative}</td>
	<td>${themeList.tmCumulative}</td>
   
</tr>   
[/#list]

</table>
</body>
</html>
