<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
</head>
<body>
<!-- 
<div class="header">
	<a href="/mobile/index.jhtml" class="go-back"></a>
	<div class="h-txt">充值申请</div>
</div>
 -->
<div class="containt">
<form id="storeRechargeInfoForm">
	<div class="order-tab">
		<div class="title">基本信息</div>
		<input type="hidden" id="storeId" name="storeId" value="${store.id}"/>
		<input type="hidden" id="saleOrgId" name="saleOrgId" value="${saleOrg.id}"/>
		<input type="hidden" id="saleOrgName" name="saleOrgName" value="${saleOrg.name}"/>
		<input type="hidden" id="rechargeTypeId" name="rechargeTypeId" value="168"/>
		<input type="hidden" id="bankCardId" name="bankCard.id"/>
		<input type="hidden" id="organizationId" name="organizationId"/>
		<div class="dl-style">
			<dl>
				<dt class="f-black">充值客户<em class="f-red">*</em></dt>
				<dd data-id="CustomerBox"  onclick="showPup(this)"><input type="text" class="txt arrow" disabled placeholder="请选择" id="storeName" name="storeName" value="${store.name}"/></dd>
			</dl>
			<dl>
				<dt>SBU</dt>
				<dd><input type="text" class="txt" disabled value="${sbu.name}"/>
					<input type="hidden" id="sbuId" name="sbuId" value="${sbu.id}"/>
				</dd>
			</dl>
			<dl>
				<dt class="f-black">充值金额</dt>
				<dd><input type="number" class="txt" placeholder="请输入具体金额" id="amount" name="amount"></dd>
			</dl>
			<dl>
				<dt>余额充值编号</dt>
				<dd><input type="text" class="txt" disabled></dd>
			</dl>
			<dl>
				<dt class="f-black">收款账号<em class="f-red">*</em></dt>
				<dd data-id="AccountBox" onclick="showPup(this)"><input type="text" class="txt arrow" disabled placeholder="请选择" id="bankCardNo" name="bankCardNo"/></dd>
			</dl>
            <dl>
				<dt>收款银行</dt>
				<dd><input type="text" class="txt" disabled id="bankName"/></dd>
			</dl>
			<dl>
				<dt class="f-black">银行水单号</dt>
				<dd><input type="text" class="txt" placeholder="请输入"></dd>
			</dl>
			<dl>
				<dt class="f-black">经营组织</dt>
				<dd><input type="text" class="txt" disabled id="organizationName"></dd>
			</dl>
			<dl>
				<dt class="f-black">申请日期</dt>
				<dd><input type="date" class="txt date" id="applyDate" name="applyDate" btn-fun="clear" value="${.now}"/></dd>
			</dl>
			<dl>
				<dt class="f-black">申请备注</dt>
				<dd><input type="text" class="txt" placeholder="请输入" name="memo" id="memo"></dd>
			</dl>
			<dl>
				<dt class="f-black">GL日期</dt>
				<dd><input type="date" class="txt date" id="glDate" name="glDate" btn-fun="clear" value="${.now}"/></dd>
			</dl>
		</div>
	</div>
	<div class="order-tab mt8" >
		<div class="title">附件信息<a href="javascript:void(0);" class="lbtn-orgn btn" data-id="AttaBox"  onclick="addAttach(this)"><i class="icon ico-add"></i>添加附件</a></div>
		<div class="atta-list" style="margin-bottom: 80px;">
			
		</div>
	</div>
	<div class="order-btns" style="text-align: center;">
		<input type="button" class="order-submit" value="保存" onclick="saveStoreRecharge()" />
		<input type='button' class='order-submit btn-blue' value='提交' onclick='submitRecharge(this)' style='float:right;' />
	</div>
	
</form>
<form id="fileForm">
	<input type="file" name="file" id="file" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this)"  style="display: none"/>
</form>
</div>
<div class="pup-obox" id="CustomerBox">
	<div style="position:fixed; width:100%;z-index:10;">
		<div class="pup-header">
			<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
			<div class="h-txt">请选择客户</div>
		</div>
		<div class="search-box" style="border-bottom:solid 1px #eee;">
			<input type="search" class="txt" placeholder="客户名称" id="searchByCustomerName"/>
			<input type="button" class="btn" value="搜索" onclick="searchCustomer()">
		</div>
	</div>
	<ul class="list-txt" style="position:relative;padding-top: 100px;">
		
	</ul>
</div>
<div class="pup-obox" id="AccountBox">
	<div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
		<div class="pup-header">
			<#--<a href="javascript:void(0);" class="go-back js-cancle"></a>-->
			<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
			<div class="h-txt">请选择账号</div>
		</div>
		<div class="search-box">
			<input type="search" class="txt" placeholder="账号" id="searchBybankCardNo"/>
			<input type="button" class="btn" value="搜索" onclick="searchBybankCardNo()">
		</div>
	</div>
	<ul class="list-txt" style="position:relative;padding-top: 100px;">
		
	</ul>
</div>
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script src="/resources/js/swiper.min.js" type="text/javascript"></script>
<script type="text/javascript">
	$().ready(function(){
		$(".js-cancle").click(function(){
			$(".pup-obox").hide()
			$("body").attr("style","overflow:auto")
		})
		//根据客户姓名搜索客户
		/* $("#searchByCustomerName").blur(function(){
			var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
			showPup(e)
		})
		$("#searchByCustomerName").keypress(function (e) {
	        if (e.which == 13) {
	        	var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
				showPup(e)
	        }
		}) */
		//根据账号搜索客户收款账号
		/* $("#searchBybankCardNo").blur(function(){
			var e = $('.containt').find('dd[data-id="AccountBox"]')[0]
			showPup(e)
		})
		$("#searchBybankCardNo").keypress(function (e) {
	        if (e.which == 13) {
	        	var e = $('.containt').find('dd[data-id="AccountBox"]')[0]
				showPup(e)
	        }
		}) */
	})
	
	// 根据客户姓名搜索客户
	function searchCustomer() {
		var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
		showPup(e);
	}
	// 根据账号搜索客户收款账号
	function searchBybankCardNo() {
		var e = $('.containt').find('dd[data-id="AccountBox"]')[0]
		showPup(e);
	}
 	function showPup(e){
		var id = $(e).attr("data-id")
		$("body").attr("style","overflow:hidden")
		$(".pup-obox").hide()
		$("#"+id).show()
		if(id == "CustomerBox"){//选择客户
			$('#'+id).find('ul').empty()
			$.ajax({
				type:'POST',
				url:'/member/store/select_store_data.jhtml',
				data:{name:$('#searchByCustomerName').val()},
				success:function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content).content;
						for (var i = 0; i < rows.length; i++) {
			            	var row = rows[i];
			    			var html = "<li data-id='CustomerBox' onclick='selectItem(this)'><a href='#'>"
			    			+"<div class='name'>"+row.name+"</div>"
			    			+"<div class='fl'><span>机构：</span>"+row.sale_org_name+"</div>"
			    			+"<div class='fr'><span>编码：</span>"+row.out_trade_no+"</div>"
			    			+"</a>"
			    			+"<input type='hidden' id='storeName' value='"+row.name+"'/>"
			    			+"<input type='hidden' id='storeId' value='"+row.id+"'/>"
			    			+"<input type='hidden' id='saleOrgId' value='"+row.sale_org_id+"'/>"
			    			+"<input type='hidden' id='saleOrgName' value='"+row.sale_org_name+"'/>"
			    			+"</li>";
			    			$('#'+id).find('ul').append(html)
						}
					}
				}
			})
		}
		if(id == "AccountBox"){//查询收款账号
			$('#'+id).find('ul').empty()
			$.ajax({
				type:'POST',
				url:'/member/bankCard/select_bank_card_data.jhtml',
				data:{saleOrgId:$('#saleOrgId').val(),bankCardNo:$('#searchBybankCardNo').val(),sbuId:$('#sbuId').val()},
				success:function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content).content;
						for (var i = 0; i < rows.length; i++) {
			            	var row = rows[i];
			    			var html = "<li data-id='AccountBox' onclick='selectItem(this)'><a href='#'>"
			    			+"<div class='name'>"+row.bank_card_no+"</div>"
			    			+"<div class='fl'><span>银行名称：</span>"+row.bank_name+"</div>"
			    			+"</a>"
			    			+"<input type='hidden' id='bankCardId' value='"+row.id+"'/>"
			    			+"<input type='hidden' id='bankCardNo' value='"+row.bank_card_no+"'/>"
			    			+"<input type='hidden' id='bankName' value='"+row.bank_name+"'/>"
			    			+"<input type='hidden' id='organizationId' value='"+row.organization+"'/>"
			    			+"<input type='hidden' id='organizationName' value='"+row.organization_name+"'/>"
			    			+"</li>";
			    			$('#'+id).find('ul').append(html)
						}
					}
				}
			})
		}
	}
	function selectItem(e){
		var id = $(e).attr("data-id")
		$("body").attr("style","overflow:hidden")
		$(".pup-obox").hide()
		$("#"+id).hide()
		if(id == "CustomerBox"){//选择客户
			$('.containt').find('input[id="storeName"]').val($(e).find('input[id="storeName"]').val())
			$('.containt').find('input[id="storeId"]').val($(e).find('input[id="storeId"]').val())
			$('.containt').find('input[id="saleOrgId"]').val($(e).find('input[id="saleOrgId"]').val())
			$('.containt').find('input[id="saleOrgName"]').val($(e).find('input[id="saleOrgName"]').val())
		}
		if(id == "AccountBox"){//选择账号
			$('.containt').find('input[id="bankCardId"]').val($(e).find('input[id="bankCardId"]').val())
			$('.containt').find('input[id="bankCardNo"]').val($(e).find('input[id="bankCardNo"]').val())
			$('.containt').find('input[id="bankName"]').val($(e).find('input[id="bankName"]').val())
			$('.containt').find('input[id="organizationId"]').val($(e).find('input[id="organizationId"]').val())
			$('.containt').find('input[id="organizationName"]').val($(e).find('input[id="organizationName"]').val())
		}
	}
	//充值保存
	function saveStoreRecharge(){
		var flag = confirm("您确定要保存吗？");
		if (flag != true) {
			return ;
		}
		if($("#storeRechargeInfoForm").find('input[id="storeId"]').val() == null || $("#storeRechargeInfoForm").find('input[id="storeId"]').val() == ""){
			alert("请选择客户")
			return false
		}
		if($("#storeRechargeInfoForm").find('input[id="bankCardId"]').val() == null || $("#storeRechargeInfoForm").find('input[id="bankCardId"]').val() == ""){
			alert("请选择账号")
			return false
		}
		var jsonData = {
			'storeId':$("#storeId").val(),
			'storeName':$("#storeName").val(),
			'amount':$("#amount").val(),
			'saleOrgId':$("#saleOrgId").val(),
			'saleOrgName':$("#saleOrgName").val(),
			'rechargeTypeId':$("#rechargeTypeId").val(),
			'balanceMonth':$("#applyDate").val().substr(0,7),
			'bankCard.id':$("#bankCardId").val(),
			'bankCardNo':$("#bankCardNo").val(),
			'applyDate':$("#applyDate").val(),
			'glDate':$("#glDate").val(),
			'organizationId':$("#organizationId").val(),
			'memo':$("#memo").val(),
			'sbuId':$('#sbuId').val()
		}
		var attachs = $('.atta-list').find('div[class="item"]')
		for (var i = 0; i < attachs.length; i++) {
			jsonData['depositAttachs['+i+'].name'] = $(attachs[i]).find('input[id="attachName"]').val()
			jsonData['depositAttachs['+i+'].url'] = $(attachs[i]).find('input[id="attachUrl"]').val()
			jsonData['depositAttachs['+i+'].suffix'] = $(attachs[i]).find('input[id="attachSuffix"]').val()
			jsonData['depositAttachs['+i+'].memo'] = $(attachs[i]).find('textarea[id="attachMemo"]').val()		
		}
		$.ajax({
			type:'POST',
			url:'/member/store_recharge/save.jhtml',
			data:jsonData,
			success:function(data) {
				if(data.type == 'success'){
					/* alert(data.content)
					window.location.href = "/mobile/index.jhtml" */
					
					// window.open('store_recharge_detail.jhtml?id='+data.objx,'_self')
					// parent.location.href = "/mobile/order/store_recharge_query.jhtml"
					location.replace("/mobile/order/store_recharge_query.jhtml");
				}
			}
		})
	}
	// 提交充值
	function submitRecharge(e) {
		var flag = confirm("您确定要提交吗？");
		var dr_id = "";
		var actual_amount = $("#amount").val();
		if (flag != true) {
			return ;
		}
		if($("#storeRechargeInfoForm").find('input[id="storeId"]').val() == null || $("#storeRechargeInfoForm").find('input[id="storeId"]').val() == ""){
			alert("请选择客户")
			return false
		}
		if($("#storeRechargeInfoForm").find('input[id="bankCardId"]').val() == null || $("#storeRechargeInfoForm").find('input[id="bankCardId"]').val() == ""){
			alert("请选择账号")
			return false
		}
		var jsonData = {
			'storeId':$("#storeId").val(),
			'storeName':$("#storeName").val(),
			'amount':$("#amount").val(),
			'saleOrgId':$("#saleOrgId").val(),
			'saleOrgName':$("#saleOrgName").val(),
			'rechargeTypeId':$("#rechargeTypeId").val(),
			'balanceMonth':$("#applyDate").val().substr(0,7),
			'bankCard.id':$("#bankCardId").val(),
			'bankCardNo':$("#bankCardNo").val(),
			'applyDate':$("#applyDate").val(),
			'glDate':$("#glDate").val(),
			'organizationId':$("#organizationId").val(),
			'memo':$("#memo").val(),
			'sbuId':$('#sbuId').val(),
			'actualAmount':$("#amount").val(),//实际充值金额
		}
		var attachs = $('.atta-list').find('div[class="item"]')
		for (var i = 0; i < attachs.length; i++) {
			jsonData['depositAttachs['+i+'].name'] = $(attachs[i]).find('input[id="attachName"]').val()
			jsonData['depositAttachs['+i+'].url'] = $(attachs[i]).find('input[id="attachUrl"]').val()
			jsonData['depositAttachs['+i+'].suffix'] = $(attachs[i]).find('input[id="attachSuffix"]').val()
			jsonData['depositAttachs['+i+'].memo'] = $(attachs[i]).find('textarea[id="attachMemo"]').val()		
		}
		$.ajax({
			type:'POST',
			url:'/member/store_recharge/save.jhtml',
			data:jsonData,
			success:function(data) {
				if(data.type == 'success'){
					/* alert(data.content)
					window.location.href = "/mobile/index.jhtml" */
					
					// window.open('store_recharge_detail.jhtml?id='+data.objx,'_self')
					// parent.location.href = "/mobile/order/store_recharge_query.jhtml"
					// alert("id: " + data.objx);
					dr_id = data.objx
					// location.replace("/mobile/order/store_recharge_query.jhtml");
					// ---------------
					if (dr_id == null || dr_id == "") {
						return ;
					}

					check_wf(dr_id,jsonData)


					// $.ajax({
					// 	type:'POST',
					// 	url:'/wf/wf_obj_config/get_config.jhtml',
					// 	data:{obj_type_id:43,objid:data.objx},
					// 	success:function(resultMsg) {
					// 		if(resultMsg.type == "success"){
					// 			var rows = resultMsg.objx;
					// 		 	if(rows.length==1){
					// 		 		data = data+'&objConfId='+rows[0].id;
					// 		 		$.ajax({
					// 					 url: "/member/store_recharge/check_wf.jhtml",
					// 					 type: 'POST',
					// 					 data: {
					// 						id: dr_id,
					// 						actualAmount: actual_amount,
					// 						flag: 1,
					// 						objConfId: rows[0].id
					// 					 },
					// 					 success:function(resultMsg){
					// 						alert(resultMsg.content)
					// 						if(resultMsg.type == "success"){
					// 							 /* location.reload()  */
					// 							// window.location.href = "/mobile/index.jhtml"
					// 							window.location.href = "/mobile/order/store_recharge_query.jhtml"
					// 						 }
					// 					 }
					// 				})
					// 		 	}else{
					// 		 		var str = '';
					// 			 	for(var i=0;i<rows.length;i++){
					// 			 		var row = rows[i];
					// 			 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					// 			 	}
					// 			 	var content = '<table class="input input-edit" style="width:100%">'
					// 						+'<tbody><tr><th>流程模版</th>'
					// 						+'<td>'
					// 							+'<select class="text" id="objConfId">'
					// 								+str
					// 							+'</select>'
					// 						+'</td>'
					// 					+'</tr></tbody></table>';
					// 				document.getElementById('black').style.display='block';
					// 				$.dialog({
					// 					title:"客户充值审核",
					// 					height:'135',
					// 					content: content,
					// 					onOk:function(){
					// 						var objConfId = $("#objConfId").val();
					// 						if(objConfId=='' || objConfId == null){
					// 							$.message_alert("请选择客户充值模版");
					// 							return false;
					// 						}
					// 						document.getElementById('black').style.display='none';
					// 						data = data+'&objConfId='+objConfId;
					//
					// 						$.ajax({
					// 							 url: "/member/store_recharge/check_wf.jhtml",
					// 							 type: 'POST',
					// 							 data: data,
					// 							 success:function(resultMsg){
					// 								 alert(resultMsg.content)
					// 								 if(resultMsg.type == "success"){
					// 									 /* location.reload() */
					// 									 window.location.href = "/mobile/index.jhtml"
					// 								 }
					// 							 }
					// 						})
					//
					// 					},
					// 					onCancel:function(){
					// 						document.getElementById('black').style.display='none';
					// 					}
					// 				});
					//
					// 		 	}
					// 		 }
					// 	}
					// })
				}
			}
		})
	}

    //流程审核
    function check_wf(id,jsonData){
        var objTypeId = 100022;
//		var modelId = model(sbuId,"正式");
        var modelId = model($("#sbuId").val(),"正式");
        var url="/member/store_recharge/check_wf.jhtml?id="+id+"&modelId="+modelId+"&objTypeId="+objTypeId;
        $.ajax({
            type:'POST',
            url:url,
			data:jsonData,
            success:function(data) {
                if(data.type == "success"){
                    window.location.href = "/mobile/index.jhtml"
                }
            }
        });
    }

    //查流程模板
    function model(sbuId,versions){
        var json = '{"正式":{"1":"55038","2":"55060","3":"55079","4":"55049","5":"","6":"","7":"","8":"55090"},';
        json +='"测试":{"1":"55038","2":"55060","3":"55079","4":"55049","5":"","6":"","7":"","8":"55090"}}';
        var model = JSON.parse(json);
        return model[versions][sbuId];
    }


	//添加附件
	function addAttach(e){
		$('#file').trigger('click'); 
	}
	//附件上传
	function fileUpload(e){
	    var formData = new FormData($( "#fileForm" )[0]);
	    var len = $('.atta-list').find('div[class="item"]').size()
    	$.ajax({
			type:'GET',
			url:'/common/fileurl.jhtml',
			success:function(data) {
				if(data.type == "success"){
					$.ajax({
						type:'POST',
						url: data.objx,
		                data:formData,
		                cache: false,  
		                contentType: false,  
		                processData: false, 
						success:function(data_) {
							data_ = JSON.parse(data_)
							if(data_.message.type == "success"){
								var html = "<div class='item'>"
									+"<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
									+"<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
									+"<textarea class='txt' placeholder='请输入备注' id='attachMemo' name='depositAttachs["+len+"].memo'></textarea>"
									+"<input type='hidden' id='attachName' name='depositAttachs["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
									+"<input type='hidden' id='attachUrl' name='depositAttachs["+len+"].url' value='"+data_.url+"'/>"
									+"<input type='hidden' id='attachSuffix' name='depositAttachs["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
									+"</div>"; 
								$('.atta-list').append(html)
							}
						}
					})				
				}
			}
		})
	}
	//删除
	function del(e){
		$(e).parent('div').remove()
	}
</script>
</body>
</html>
