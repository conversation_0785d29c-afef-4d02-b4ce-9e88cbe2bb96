<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="${base}/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<style>
.nav02{ margin:12px; background:#FFF; border-radius:8px; overflow:hidden}
.nav02 li{ float:left; width:50%;border-bottom:solid 1px #F5F5F5}
.nav02 li a{border-right:solid 1px #F5F5F5; display:block;padding:40px 0 40px 22px;color:#3C3C3C;}
.nav02 li:nth-child(2n) a{ border-right:none}
.nav02 li img{ margin-right:8px;width:32px; height:32px; vertical-align:middle}
</style>
</head>
<body class="[#if type == 1]bgFFF[/#if]">

<div id="containt">
	[#if type == 1]
		<div class="placeOrder-box">
			[#list Session.menu_list as menu]
				[#if (menu.superId.id == superId)]
					<div class="item"><a href="${menu.url}"><i class="${menu.menuImg}"></i>${menu.menuName}</a></div>
				[/#if]
	
	        [/#list]
		</div>
	[#elseif type == 2]
		<div class="payApply-box">
			[#list Session.menu_list as menu]
				[#if (menu.superId.id == superId)]
					<div class="item">
	                    <a href="${menu.url}">
	                        <i class="${menu.menuImg}"></i>
	                        <b>${menu.menuName}</b>
	                        <p>点击进入充值入口</p>
	                    </a>
	                </div>
				[/#if]
			[/#list]
	    </div>
	[/#if]
</div>

<script src="${base}/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
$().ready(function(){
	//加载产品数据
	var iconList = '${iconList}';
	var iconList=eval("("+iconList+")"); 
	for (var i = 0; i < iconList.length; i++) {
		var img = 'url(' + iconList[i].image + ')';
		var str = '.placeOrder-box .item i.po-0'+(iconList[i].iconType)+', .payApply-box .item i.po-0'+(iconList[i].iconType);
		$(str).css("background-image", img);
	}
})
</script>
</body>
</html>
