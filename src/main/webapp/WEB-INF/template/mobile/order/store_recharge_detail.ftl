<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<style>
 .black{
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 2;
    background: rgba(0,0,0,0.1);
    top:0;
    left:0;
}
.xxDialog{
    position: absolute;
    z-index: 3;
    width: 500px;
    top:50%;
    left:50%;
    background: #fff;
    padding: 15px;
    border-radius: 5px;
}
div.xxDialog .dialogTitle {
    cursor: move;
    border: 0;
    margin: 0;
    padding: 0 16px 0 10px;
    height: 44px;
    line-height: 40px;
    text-align: left;
    font-size: 18px;
    color: #666;
    
}
div.xxDialog .dialogBottom {
    box-sizing: content-box;
    padding-top: 7px;
    height: 38px;
    border-top: solid 1px #ededed;
    background: #fbfbfb;
    text-align: center;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}
div.xxDialog .dialogBottom .button {
    float: none;
    margin: 0 5px;
    padding: 0 8px;
}
.button.sureButton:hover {
    background: url(../images/button/ico-sure.png) no-repeat 12px center #4278af;
}
.button.sureButton {
    background: url(../images/button/ico-sure.png) no-repeat 12px center #5a96d2;
    color: #FFF;
}
select{
     width: 200px;
     font-size: 15px;
     text-align: center;
     border: 1px #1a1a1a solid;
     border-radius: 5px;
     padding-left: 10px;
 }
 option{
     line-height: 10px;
 }
 select:focus{
     border: 2px #ddd solid;
     box-shadow: 0 0 15px 1px #DDDDDD;
 }
 option:hover{
     background: #EBCCD1;
 }
 .dl-style dd .txt {
 	width:100%;
 }
</style>
</head>
<body>
<!-- 
<div class="header">
	<a href="/mobile/index.jhtml" class="go-back"></a>
	<a href="javascript:history.go(-1);" class="go-back"></a>
	<div class="h-txt">充值详情</div>
</div>
 -->
<div class="containt">
<form id="storeRechargeInfoForm">
	<div class="order-tab" style="margin-top: 0px;">
		<div class="title" id="baseInfo" style="position:fixed; width:100%;z-index:10;">基本信息</div>
		<div class="dl-style" style="position:relative;padding-top: 41px;">
			<input type="hidden" name="id" id="drId" value="${dr.id}"/>
			<input type="hidden" id="storeId" value="${dr.store.id}"/>
			<input type="hidden" id="saleOrgId" value="${dr.saleOrg.id}"/>
			<input type="hidden" id="saleOrgName" value="${dr.saleOrg.name}"/>
			<input type="hidden" id="bankCardId" value="${dr.bankCard.id}"/>
			<input type="hidden" id="wfId"/>
			<input type="hidden" id="sbuId" value="${dr.sbu.id}"/>
			<input type="hidden" id="organizationId" value="${dr.bankCard.organization.id}"/>
			
			<dl>
				<dt class="f-black">充值客户</dt>
				<dd ><input type="text" class="txt" disabled id="storeName" name="storeName" ></dd>
			</dl>
			<dl>
				<dt>SBU</dt>
				<dd><input type="text" class="txt" disabled value="${dr.sbu.name}"/>
				</dd>
			</dl>
			<dl>
				<dt class="f-black">充值金额</dt>
				<dd><input type="number" class="txt" disabled id="amount" name="amount" ></dd>
			</dl>
			<dl style="display:none;">
				<dt class="f-black">实际充值金额</dt>
				<dd><input type="number" class="txt" id="actualAmount" name="actualAmount" ></dd>
			</dl> 
			<dl>
				<dt>余额充值编号</dt>
				<dd><input type="text" class="txt" disabled value="${dr.sn}"></dd>
			</dl>
			<dl>
				<dt class="f-black">收款账号</dt>
				<dd><input type="text" class="txt" disabled id="bankCardNo" name="bankCardNo"/></dd>
			</dl>
            <dl>
				<dt>收款银行</dt>
				<dd><input type="text" class="txt" disabled id="bankName"></dd>
			</dl>
			<dl>
				<dt class="f-black">银行水单号</dt>
				<dd><input type="text" class="txt" disabled></dd>
			</dl>
			<dl>
				<dt class="f-black">经营组织</dt>
				<dd><input type="text" class="txt" disabled id="organizationName"></dd>
			</dl>
			<dl>
				<dt class="f-black">申请日期</dt>
				<dd><input type="text" class="txt" id="applyDate" disabled/></dd>
			</dl>
			<dl>
				<dt class="f-black">创建时间</dt>
				<dd><input type="text" class="txt" id="createDate" disabled/></dd>
			</dl>
			<dl>
				<dt class="f-black">申请备注</dt>
				<dd><input type="text" class="txt" id="memo" disabled name="memo"></dd>
			</dl>
			<dl>
				<dt class="f-black">GL日期</dt>
				<dd><input type="text" class="txt" id="glDate" name="glDate" disabled /></dd>
			</dl>
		</div>
	</div>
	<div class="order-tab mt8" style="margin-bottom:55px;">
		<div class="title" id="attachTitle">附件信息</div>
		<div class="atta-list">
			
		</div>
	</div>
	<div class="order-btns">
	
	</div>
</form>
<form id="fileForm">
	<input type="file" name="file" id="file" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this)"  style="display: none"/>
</form>
</div>

<div class="black" id="black"></div>
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script src="/resources/js/swiper.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript">
$().ready(function(){
   	//基本信息
   	$("#storeName").val("${dr.store.name}")
   	$("#amount").val(${dr.amount})
   	$("#actualAmount").val(${dr.amount})
	$("#bankCardNo").val("${dr.bankCard.bankCardNo}")
	$("#bankName").val("${dr.bankCard.bankName}")
	$("#organizationName").val("${dr.bankCard.organization.name}")
   	
   	$("#applyDate").val("${dr.applyDate}")
   	$("#memo").val("${dr.memo}")
   	$("#wfId").val("${dr.wfId}") 
   	$("#createDate").val("${createDate}")
   	$("#glDate").val("${dr.glDate}")
   	
   	/** docStatus单据状态 	  0.已保存(没有流程) 1.已提交  2.已处理(流程走完) 3.作废    */
   	/* 
   	[#if (dr.docStatus ==0 && dr.wfId == null) && dr.storeMember.name == storeMember.name]
   		$("#storeRechargeInfoForm .order-btns").append("<input type='button' class='order-submit' value='作废' onclick='close_order()' style='background:#ffbc65;' />")
	[/#if]
	[#if (dr.docStatus==0 || (dr.docStatus==1 && dr.wfId==null)) && dr.storeMember.name == storeMember.name]
		$("#storeRechargeInfoForm .order-btns").append("<input type='button' class='order-submit btn-blue' value='保存' onclick='saveStoreRecharge()' style='float:right;' />")
	[/#if]
   	[#if (dr.docStatus==1 || dr.docStatus==0) && dr.storeMember.name == storeMember.name]
   		$('#attachTitle').append("<a href='javascript:void(0);' class='lbtn-orgn btn' data-id='AttaBox'  onclick='addAttach(this)'><i class='icon ico-add'></i>添加附件</a>")
		$("#baseInfo").append("<a href='javascript:void(0);' class='lbtn-orgn btn' onclick='audit()'><i class='icon ico-add'></i>提交</a>")
    [/#if]
   	[#if dr.wfId != null && dr.wfId != "" && dr.wfState != 2 && dr.storeMember.name == storeMember.name]
		$("#baseInfo").append("<a href='javascript:void(0);' class='lbtn-orgn btn' onclick='interrupt()'><i class='icon ico-add'></i>中断</a>")
    [/#if] 
   	*/
    
   	[#if (dr.docStatus ==0 && dr.wfId == null) && dr.storeMember.name == storeMember.name]
		$("#storeRechargeInfoForm .order-btns").append("<input type='button' class='order-submit' value='作废' onclick='close_order()' style='background:#ffbc65;' />")
		$("#storeRechargeInfoForm .order-btns").append("<input type='button' class='order-submit btn-blue' value='提交' onclick='audit()' style='float:right;' />")
// 		$("#baseInfo").append("<a href='javascript:void(0);' class='lbtn-orgn btn' onclick=''><i class='icon ico-add'></i>提交</a>")
	[/#if]
// 	[#if (dr.docStatus==0 || (dr.docStatus==1 && dr.wfId==null))]
// 		$("#storeRechargeInfoForm .order-btns").append("<input type='button' class='order-submit btn-blue' value='保存' onclick='saveStoreRecharge()' style='float:right;' />")
// 	[/#if]
	[#if (dr.docStatus==1 || dr.docStatus==0) && dr.storeMember.name == storeMember.name]
		$('#attachTitle').append("<a href='javascript:void(0);' class='lbtn-orgn btn' data-id='AttaBox'  onclick='addAttach(this)'><i class='icon ico-add'></i>添加附件</a>")
// 		$("#baseInfo").append("<a href='javascript:void(0);' class='lbtn-orgn btn' onclick='audit()'><i class='icon ico-add'></i>提交</a>")
	[/#if]
	[#if dr.wfId != null && dr.wfId != "" && dr.wfState != 2 && dr.storeMember.name == storeMember.name]
		$("#baseInfo").append("<a href='javascript:void(0);' class='lbtn-orgn btn' onclick='interrupt()'><i class='icon ico-add'></i>中断</a>")
	[/#if]
    
    
   	//附件信息
   	var deposit_attachs = JSON.parse('${depositAttach_json}')
   	for (var i = 0; i < deposit_attachs.length; i++) {
   		var deposit_attach = deposit_attachs[i]
   		var memo = ""
		if (deposit_attach.memo != null) {
   			memo = deposit_attach.memo;
   		}
   		var html = "<div class='item'>"
			+"<div class='tit'>附件 <a href='"+deposit_attach.url+"' target='_blank'><span class='name fr'>"+deposit_attach.file_name+"</span></a></div>"
			+"<textarea class='txt' placeholder='备注' id='attachMemo' name='depositAttachs["+i+"].memo'>"+memo+"</textarea>"
			+"<input type='hidden' id='attachName' name='depositAttachs["+i+"].name' value='"+deposit_attach.file_name.split('.')[0]+"'/>"
			+"<input type='hidden' id='attachUrl' name='depositAttachs["+i+"].url' value='"+deposit_attach.url+"'/>"
			+"<input type='hidden' id='attachSuffix' name='depositAttachs["+i+"].suffix' value='"+deposit_attach.file_name.split('.')[1]+"'/>"
			+"</div>"; 
		$(".atta-list").append(html)

   	}
   	
   	
})




//流程审批
function audit(){
	var flag = confirm("你确定要提交吗？")
	if(flag == true){
		var flag =1;
		var data = $("#storeRechargeInfoForm").serialize()+"&flag="+flag;
		dr_id = ${dr.id}

		if (dr_id == null || dr_id == "") {
			alert("id不能为空")
			return ;
		}
		check_wf(dr_id,data)

		[#--$.ajax({--]
		[#--	type:'POST',--]
		[#--	url:'/wf/wf_obj_config/get_config.jhtml',--]
		[#--	data:{obj_type_id:43,objid:${dr.id}},--]
		[#--	success:function(resultMsg) {--]
		[#--		if(resultMsg.type == "success"){--]
		[#--			var rows = resultMsg.objx;--]
		[#--		 	if(rows.length==1){--]
		[#--		 		data = data+'&objConfId='+rows[0].id;--]
		[#--		 		$.ajax({--]
		[#--					 url: "/member/store_recharge/check_wf.jhtml",--]
		[#--					 type: 'POST',--]
		[#--					 data: data,--]
		[#--					 success:function(resultMsg){--]
		[#--						alert(resultMsg.content)--]
		[#--						if(resultMsg.type == "success"){--]
		[#--							 /* location.reload()  */--]
		[#--							// window.location.href = "/mobile/index.jhtml"--]
		[#--							window.location.href = "/mobile/order/store_recharge_query.jhtml"--]
		[#--						 }--]
		[#--					 }--]
		[#--				})--]
		[#--		 	}else{--]
		[#--		 		var str = '';--]
		[#--			 	for(var i=0;i<rows.length;i++){--]
		[#--			 		var row = rows[i];--]
		[#--			 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';--]
		[#--			 	}--]
		[#--			 	var content = '<table class="input input-edit" style="width:100%">'--]
		[#--						+'<tbody><tr><th>流程模版</th>'--]
		[#--						+'<td>'--]
		[#--							+'<select class="text" id="objConfId">'--]
		[#--								+str--]
		[#--							+'</select>'--]
		[#--						+'</td>'--]
		[#--					+'</tr></tbody></table>';--]
		[#--				document.getElementById('black').style.display='block';--]
		[#--				$.dialog({--]
		[#--					title:"客户充值审核",--]
		[#--					height:'135',--]
		[#--					content: content,--]
		[#--					onOk:function(){--]
		[#--						var objConfId = $("#objConfId").val();--]
		[#--						if(objConfId=='' || objConfId == null){--]
		[#--							$.message_alert("请选择客户充值模版");--]
		[#--							return false;--]
		[#--						}--]
		[#--						document.getElementById('black').style.display='none';--]
		[#--						data = data+'&objConfId='+objConfId;--]
		[#--						--]
		[#--						$.ajax({--]
		[#--							 url: "/member/store_recharge/check_wf.jhtml",--]
		[#--							 type: 'POST',--]
		[#--							 data: data,--]
		[#--							 success:function(resultMsg){--]
		[#--								 alert(resultMsg.content)--]
		[#--								 if(resultMsg.type == "success"){--]
		[#--									 /* location.reload() */ --]
		[#--									 window.location.href = "/mobile/index.jhtml"--]
		[#--								 }--]
		[#--							 }--]
		[#--						})--]
		[#--						--]
		[#--					},--]
		[#--					onCancel:function(){--]
		[#--						document.getElementById('black').style.display='none';--]
		[#--					}--]
		[#--				});--]
		[#--				--]
		[#--		 	}--]
		[#--		 }--]
		[#--	}--]
		[#--})--]
	}
}

//流程审核
function check_wf(id,jsonData){
	var objTypeId = 100022;
//		var modelId = model(sbuId,"正式");
	var modelId = model($("#sbuId").val(),"正式");
	var url="/member/store_recharge/check_wf.jhtml?id="+id+"&modelId="+modelId+"&objTypeId="+objTypeId;
	$.ajax({
		type:'POST',
		url:url,
		data:jsonData,
		success:function(data) {
			if(data.type == "success"){
				window.location.href = "/mobile/index.jhtml"
			}
		}
	});
}

//查流程模板
function model(sbuId,versions){
	var json = '{"正式":{"1":"55038","2":"55060","3":"55079","4":"55049","5":"","6":"","7":"","8":"55090"},';
	json +='"测试":{"1":"55038","2":"55060","3":"55079","4":"55049","5":"","6":"","7":"","8":"55090"}}';
	var model = JSON.parse(json);
	return model[versions][sbuId];
}


//作废
function close_order(){
	var flag = confirm("你确定要作废吗？")
	if(flag == true){
		var jsonData = {
			'id':$('#drId').val(),
			'storeId':$("#storeId").val(),
			'storeName':$("#storeName").val(),
			'amount':$("#amount").val(),
			'actualAmount':$("#actualAmount").val(),
			'saleOrgId':$("#saleOrgId").val(),
			'saleOrgName':$("#saleOrgName").val(),
			'rechargeTypeId':$("#rechargeTypeId").val(),
			'balanceMonth':$("#applyDate").val().substr(0,7),
			'bankCard.id':$("#bankCardId").val(),
			'bankCardNo':$("#bankCardNo").val(),
			'applyDate':$("#applyDate").val(),
			'organizationId':$("#organizationId").val(),
			'memo':$("#memo").val(),
			'sbuId':$('#sbuId').val(),
			'glDate':$('#glDate').val(),
			'flag':2
		}
		var $form = $("#storeRechargeInfoForm");
		var data = jsonData;
		$.ajax({
			type:'POST',
			url:'/member/store_recharge/cancel.jhtml',
			data:data,
			success:function(resultMsg) {
				if(resultMsg.type == "success"){
					alert(resultMsg.content)
					// location.reload() 
					window.location.href="/mobile/order/store_recharge_query.jhtml";
				}
			}
		})
	}
}
//中断
function interrupt(){
	var flag = confirm("你确定要中断吗？")
	if(flag == true){
		$.ajax({
			type:'POST',
			//url:'/wf/interrupt.jhtml',
			url:'/act/wf/interrupt.jhtml',
			data:{wfId:$("#wfId").val()},
			success:function(data) {
				if(data.type == "success"){
					alert(data.content)
					location.reload() 
				}
			}
		})
	}
}
//充值保存
function saveStoreRecharge(){
	var flag = confirm("您确定要保存吗？");
	if (flag != true) {
		return ;
	}
	var jsonData = {
		'id':$('#drId').val(),
		'storeId':$("#storeId").val(),
		'storeName':$("#storeName").val(),
		'amount':$("#amount").val(),
		'actualAmount':$("#actualAmount").val(),
		'saleOrgId':$("#saleOrgId").val(),
		'saleOrgName':$("#saleOrgName").val(),
		'rechargeTypeId':$("#rechargeTypeId").val(),
		'balanceMonth':$("#applyDate").val().substr(0,7),
		'bankCard.id':$("#bankCardId").val(),
		'bankCardNo':$("#bankCardNo").val(),
		'applyDate':$("#applyDate").val(),
		'organizationId':$("#organizationId").val(),
		'memo':$("#memo").val(),
		'sbuId':$('#sbuId').val(),
		'glDate':$('#glDate').val()
	}
	var attachs = $('.atta-list').find('div[class="item"]')
	for (var i = 0; i < attachs.length; i++) {
		jsonData['depositAttachs['+i+'].name'] = $(attachs[i]).find('input[id="attachName"]').val()
		jsonData['depositAttachs['+i+'].url'] = $(attachs[i]).find('input[id="attachUrl"]').val()
		jsonData['depositAttachs['+i+'].suffix'] = $(attachs[i]).find('input[id="attachSuffix"]').val()
		jsonData['depositAttachs['+i+'].memo'] = $(attachs[i]).find('textarea[id="attachMemo"]').val()		
	}
	$.ajax({
		type:'POST',
		url:'/member/store_recharge/updata.jhtml',
		data:jsonData,
		success:function(data) {
			if(data.type == 'success'){
				/* alert(data.content)
				window.location.href = "/mobile/index.jhtml" */
				
				window.open('store_recharge_detail.jhtml?id='+data.objx,'_self')
				
			}
		}
	})
}
//添加附件
function addAttach(e){
	$('#file').trigger('click'); 
}
//附件上传
function fileUpload(e){
    var formData = new FormData($( "#fileForm" )[0]);
    var len = $('.atta-list').find('div[class="item"]').size()
	$.ajax({
		type:'GET',
		url:'/common/fileurl.jhtml',
		success:function(data) {
			if(data.type == "success"){
				$.ajax({
					type:'POST',
					url: data.objx,
	                data:formData,
	                cache: false,  
	                contentType: false,  
	                processData: false, 
					success:function(data_) {
						data_ = JSON.parse(data_)
						if(data_.message.type == "success"){
							var html = "<div class='item'>"
								+"<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
								+"<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
								+"<textarea class='txt' placeholder='请输入备注' id='attachMemo' name='depositAttachs["+len+"].memo'></textarea>"
								+"<input type='hidden' id='attachName' name='depositAttachs["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
								+"<input type='hidden' id='attachUrl' name='depositAttachs["+len+"].url' value='"+data_.url+"'/>"
								+"<input type='hidden' id='attachSuffix' name='depositAttachs["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
								+"</div>"; 
							$('.atta-list').append(html)
						}
					}
				})				
			}
		}
	})
}
//删除
function del(e){
	$(e).parent('div').remove()
}
</script>
</body>
</html>
