<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<style>
	.oPro-list .item{padding:8px 10px;}
	.oPro-list .item .data{overflow:hidden;background:#f7f7f7;padding: 5px 8px;margin-top: 23px;font-size: 0.7rem;line-height: 1.8;border-radius: 2px; display:none}
    .oPro-list .item .data span{ width:100%; float:left;color:#888}
	.oPro-list .item.on .data{display:block}
	.pro-list li p.name {font-size: 0.75rem;}
	.pro-list li p {font-size: 0.65rem;}
</style>
</head>
<body>
<div class="header">
	<a href="javascript:history.go(-1);" class="go-back"></a>
	<div class="h-txt">商品下单</div>
</div>
<div class="containt">
	<div class="order-tab">
		<div class="title">基本信息</div>
		<div class="dl-style">
			[#if "${flas}" == "0"]
				<input type="hidden" id="storeBalance" value="${balance2!0}"/>
			[#else]
				<input type="hidden" id="storeBalance" value="${balance!0}"/>
			[/#if]
			<dl>
				<dt class="f-black">客户<em class="f-red">*</em></dt>
				<dd data-id="CustomerBox"  onclick="showPup(this)"><input type="hidden" class="txt arrow" disabled id="storeId" value="${store.id}"/><input type="text" class="txt arrow" disabled placeholder="请选择" id="name" value="${store.name}"></dd>
			</dl>
			<dl>
				<dt>业务类型</dt>
				<dd>
					<select class="txt arrow" id="business_type_id">
					[#list businessTypes as businessType]
						<option value="${businessType.id}" [#if store.businessType.id==businessType.id] selected[/#if]>${businessType.value}</option>
					[/#list]
					</select>
					<!-- <input type="text" class="txt" disabled id="business_type_value" value="${store.businessType.value}"/> -->
				</dd>
			</dl>
			<dl>
				<dt>SBU</dt>
				<dd><input type="text" class="txt" disabled value="${sbu.name}"/>
					<input type="hidden" id="sbuId" value="${sbu.id}"/>
				</dd>
			</dl>
			<dl>
				<dt>机构<em class="f-red">*</em></dt>
				<dd><input type="text" class="txt" disabled id="sale_org_name" value="${store.saleOrg.name}"/><input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" id="sale_org_id" value="${store.saleOrg.id}"/></dd>
			</dl>
			<dl>
				<dt class="f-black">付款方式<em class="f-red">*</em></dt>
				<dd><select class="txt arrow" id="paymentMethodId">
					<!-- [#list paymentMethods as paymentMethod]
						<option value="${paymentMethod.id}">${paymentMethod.name}</option>
					[/#list] -->	
					<option value="17">线上支付</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt class="f-black">配送方式<em class="f-red">*</em></dt>
				<dd><select class="txt arrow" id="shippingMethodId">
					[#list shippingMethods as shippingMethod]
						<option value="${shippingMethod.id}">${shippingMethod.name}</option>
						
					[/#list]
				</select></dd>
			</dl>
			<dl>
				<dt>区域经理</dt>
				<dd><input type="text" class="txt" disabled id="store_member_name" value="${store.storeMember.name}"/><input type="hidden" id="storeMemberId" value="${store.storeMember.id}"/></dd>
			</dl>
			<dl>
				<dt class="f-black">仓库<em class="f-red">*</em></dt>
				[#if warehouse??]
				<dd data-id="WarehouseBox"  onclick="showPup(this)">
					<input type="hidden" class="txt" disabled id="warehouseId" value="${warehouse.id}">
					<input type="text" class="txt arrow" disabled value="${warehouse.name}" id="warehouseName">
				</dd>
				[#else]
				<dd data-id="WarehouseBox"  onclick="showPup(this)">
					<input type="hidden" class="txt" disabled id="warehouseId">
					<input type="text" class="txt arrow" disabled placeholder="请选择" id="warehouseName">
				</dd>
				[/#if]
			</dl>
			<dl>
				<dt>仓库类型</dt>
				[#if warehouse??]
				<dd>
					<input type="text" class="txt" disabled id="type_system_dict_value" value="${warehouse.getTypeSystemDict().getValue()}">
					<input type="hidden" class="text" id="type_system_dict_id" value="${warehouse.getTypeSystemDict().getId()}">
				</dd>
				[#else]
				<dd>
					<input type="text" class="txt" disabled id="type_system_dict_value" value="">
					<input type="hidden" class="text" id="type_system_dict_id" value="">
				</dd>
				[/#if]
			</dl>
			<dl>
				<dt>经营组织<em class="f-red">*</em></dt>
				[#if warehouse??]
				<dd>
					<input type="text" class="txt" disabled id="management_organization_name" value="${warehouse.getManagementOrganization().getName()}"  style="width:100%"/>
					<input type="hidden" id="organizationId" value="${warehouse.getManagementOrganization().getId()}"/>
				</dd>
				[#else]
				<dd>
					<input type="text" class="txt" disabled id="type_system_dict_value" value="">
					<input type="hidden" class="text" id="type_system_dict_id" value="">
				</dd>
				[/#if]
			</dl>
			<dl>
				<dt>备注</dt>
				<dd><textarea class="txt" placeholder="请输入备注" id="baseInfoMemo"></textarea></dd>
				<input type="hidden" class="text" id="memberRankId" value="${memberRankId}">
			</dl>
		</div>
	</div>
	<div class="order-tab mt8">
		<div class="title">购买商品<a href="javascript:void(0);" class="lbtn-orgn btn" data-id="ProBox"  onclick="showPup(this)"><i class="icon ico-add"></i>新增产品</a></div>
		<div class="oPro-list">
			
		</div>
	</div>
	<div class="order-tab mt8">
		<div class="title">订单汇总</div>
		<div class="dl-style">
			<dl>
				<dt>订单箱数</dt>
				<dd><span id="orderBoxNum">0</span></dd>
			</dl>
			<dl>
				<dt>订单支数</dt>
				<dd><span id="orderBranchNum">0</span></dd>
			</dl>
			<dl>
				<dt>订单平方数</dt>
				<dd><span id="orderPerNum">0</span></dd>
			</dl>
			<dl>
				<dt>订单重量</dt>
				<dd><span id="orderWeightNum">0</span></dd>
			</dl>
			<dl>
				<dt>订单体积</dt>
				<dd><span id="orderVolumeNum">0</span></dd>
			</dl>
			<dl>
				<dt>订单金额</dt>
				<dd><span id="orderTotalPrice">0</span></dd>
			</dl>
			<dl>
				<dt>客户余额</dt>
				<dd class="f-red"><span id="orderBalancePrice">${balance!0}</span></dd>
			</dl>
		</div>
	</div>
	<div class="order-tab mt8">
		<div class="title">收货地址<a href="javascript:void(0);" class="lbtn-orgn btn" data-id="AddressBox"  onclick="showPup(this)"><i class="icon ico-adr"></i>更换地址</a></div>
		<div class="dl-style">
			<input type="hidden" id="out_trade_no" value="${storeAddress.out_trade_no}"/>
			<dl>
				<dt>收货人</dt>
				<dd><span id="receiveInfoName">${storeAddress.consignee}</span></dd>
			</dl>
			<dl>
				<dt>收货电话</dt>
				<dd><span id="receiveInfoPhone">${storeAddress.mobile}</span></dd>
			</dl>
			<dl>
				<dt>收货地区</dt>
				<dd><span id="receiveInfoArea">${storeAddress.area_full_name}</span><input type="hidden" id="areaId" value="${storeAddress.area}"/></dd>
			</dl>
			<dl>
				<dt>收货地址</dt>
				<dd><span id="receiveInfoAddress">${storeAddress.address}</span></dd>
			</dl>
			<dl>
				<dt>收货地址外部编码</dt>
				<dd><span id="out_trade_no_display">${storeAddress.out_trade_no}</span></dd>
			</dl>
		</div>
	</div>
	<div class="order-tab mt8">
		<div class="title">附件信息<a href="javascript:void(0);" class="lbtn-orgn btn" data-id="AttaBox"  onclick="addAttach(this)"><i class="icon ico-add"></i>添加附件</a></div>
		<form id="fileForm"><input type="file" name="file" id="file" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this)"  style="display: none"/></form>
		<div class="atta-list">
			
		</div>
	</div>
	<div><input type="button" class="order-submit" onclick="saveOrder(1)" value="保存" style="float:left;padding-right: 45px;"/>
	<input type="button" class="order-submit" onclick="saveOrder(2)" value="支付" style="float:right"/></div>
</div>
<div class="pup-obox" id="CustomerBox">
	<div class="header">
		<a href="javascript:void(0);" class="go-back js-cancle"></a>
		<div class="h-txt">请选择客户</div>
	</div>
	<div class="search-box"><input type="search" class="txt" placeholder="客户名称" id="searchByCustomerName"/></div>
	<ul class="list-txt">
		
	</ul>
</div>
<div class="pup-obox" id="WarehouseBox">
	<div class="header">
		<a href="javascript:void(0);" class="go-back js-cancle"></a>
		<div class="h-txt">请选择仓库</div>
	</div>
	<div class="search-box"><input type="search" class="txt" placeholder="仓库名称" id="searchByWarehouseName"/></div>
	<ul class="list-txt">
		
	</ul>
</div>
<div class="pup-obox" id="AddressBox">
	<div class="header">
		<a href="javascript:void(0);" class="go-back js-cancle"></a>
		<div class="h-txt">更换地址</div>
	</div>
	<div class="search-box"><input type="search" class="txt" placeholder="电话" id="searchByMobileName"/></div>
	<ul class="list-txt">
		
	</ul>
</div>

<div class="pup-obox" id="ProBox" >
	<div class="header">
		<a href="javascript:void(0);" class="go-back js-cancle"></a>
		<div class="h-txt">请选择商品</div>
	</div>
	<div class="pro-2">
		<div class="search-box"><input type="search" class="txt" placeholder="名称" id="searchByProductName"/></div>
		<ul class="pro-list">
			
		</ul>
	</div>
</div>
<div class="pup-obox" id="ProBox1">
	<div class="header">
		<a href="javascript:void(0);" class="go-back"  data-id="ProBox"  onclick="showPup(this)"></a>
		<div class="h-txt">购买商品</div>
	</div>
	<div class="dl-style">
			<input type="hidden" id="productName" value=""/>
			<input type="hidden" id="productDesc" value=""/>
			<input type="hidden" id="productCategoryId" value=""/>
			<input type="hidden" id="productId" value=""/>
			<input type="hidden" id="vonderCode" value=""/>
			<input type="hidden" id="productGrade" value=""/><!-- 产品等级 -->
			<input type="hidden" id="boxQuantity" value=""/><!-- 箱数 -->
			<input type="hidden" id="branchQuantity" value=""/><!-- 支数 -->
			<input type="hidden" id="scatteredQuantity" value=""/><!-- 零散支数 -->
			<input type="hidden" id="branchPerBox" value=""/><!-- 每箱支数 -->
			<input type="hidden" id="perBox" value=""/><!-- 每箱平方数 -->
			<input type="hidden" id="perBranch" value=""/><!-- 每支平方数 -->
			<input type="hidden" id="totalPer" value=""/><!-- 总平方数 -->
			<input type="hidden" id="price" value=""/>
			<input type="hidden" id="saleOrgPrice" value=""/>
			<input type="hidden" id="memberPrice" value=""/><!-- 会员价，每平方单价 -->
			<input type="hidden" id="volume" value=""/><!-- 每箱体积 -->
			<input type="hidden" id="weight" value=""/><!-- 每平方数重量 -->
			<input type="hidden" id="totalVolume" value=""/><!-- 总体积 -->
			<input type="hidden" id="totalWeight" value=""/><!-- 总重量 -->
			<input type="hidden" id="totalPrice" value=""/><!-- 金额-->
			<input type="hidden" id="priceApplyItemId" value=""/><!-- 特价单ID-->
			<input type="hidden" id="priceApplyItemType" value=""/><!-- 特价单类型-->
			<dl>
				<dt>商品名称</dt>
				<dd><span id="productName"></span></dd>
			</dl>
			<dl>
				<dt>商品描述</dt>
				<dd><span id="productDesc" style="overflow: auto;    white-space: nowrap;    display: block;"></span></dd>
			</dl>
			<dl>
				<dt>商品编码</dt>
				<dd><span id="vonderCode"></span></dd>
			</dl>
			<dl>
				<dt>产品分类</dt>
				<dd><span id="productCategoryName"></span></dd>
			</dl>
			<dl>
				<dt>产品等级</dt>
				<dd>
					<select class="txt arrow" id="productGrade">
						<option value="1">优等品</option>
						<option value="2">二等品</option>
						<option value="3">一等品</option>
						<option value="4">无等级</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt>库存（支）</dt>
				<dd><span id="productInventory"></span></dd>
			</dl>
			<dl>
				<dt>下单箱数</dt>
				<dd>
					<div class="priceDiv nums-input ov">
						<input type="button" value="-" class="b decrease" onmousedown="decrease('box',event)"/>
						<input id="productBoxNum" value="1" type="text" oninput="input('box',this,event)" onpropertychange="input('box',this,event)" mindata="0"/>
						<!-- <span id="productBoxNum">1</span> -->
						<input type="button" value="+" class="b increase" onmousedown="increase('box',event)"/>
						<span>-</span>
					</div>
				</dd>
			</dl>
			<dl>
				<dt>支数</dt>
				<dd>
					<div class="priceDiv nums-input ov">
						<input type="button" value="-" class="b decrease" onmousedown="decrease('branch',event)"/>
						<input id="productBranchNum" value="0" type="text" oninput="input('branch',this,event)" onpropertychange="input('branch',this,event)" mindata="0"/>
						<!-- <span id="productBranchNum">1</span> -->
						<input type="button" value="+" class="b increase" onmousedown="increase('branch',event)"/>
						<span>-</span>
					</div>
				</dd>
			</dl>
			<dl>
				<dt>零散支数</dt>
				<dd>
					<span id="productScatteredNum">0</span>
				</dd>
			</dl>
			<dl>
				<dt>平方数</dt>
				<dd>
					<div class="priceDiv nums-input ov">
						<input type="button" value="-" class="b decrease" onmousedown="decrease('per',event)"/>
						<input id="productPerNum" value="0" type="text" oninput="input('per',this,event)" onpropertychange="input('per',this,event)" mindata="0"/>
						<input type="button" value="+" class="b increase" onmousedown="increase('per',event)"/>
					</div>
				</dd>
			</dl>
			<dl>
				<dt>单价/平方数</dt>
				<dd><span id="memberPrice">0</span></dd>
			</dl>
			<dl>
				<dt class="f-gray">金额</dt>
				<dd class="f-red">￥<span id="totalPrice">0</span></dd>
			</dl>
			<dl>
				<dt>体积</dt>
				<dd><span id="volume">0</span></dd>
			</dl>
			<dl>
				<dt>重量</dt>
				<dd><span id="weight">0</span></dd>
			</dl>
			<dl>
				<dt>特价单号</dt>
				<dd data-id="SpecialOfferBox"  onclick="showPup(this)"><input type="text" class="txt arrow" disabled placeholder="请选择" id="priceApplyItemSn"></dd>
			</dl>
			<dl>
				<dt>交货期</dt>
				<dd><input type="date" class="txt date" id="deliveryTime"></dd>
			</dl>
			<dl class="b">
				<dt>买家备注</dt>
				<dd><textarea class="txt" placeholder="请输入" id="sellerMemo"></textarea></dd>
			</dl>
	</div>
	<input type="button" class="order-submit" data-id="ProBox1" onclick='selectItem(this)' value="保存">
</div>
<div class="pup-obox" id="SpecialOfferBox">
	<div class="header">
		<a href="javascript:void(0);" class="go-back" data-id="ProBox1"  onclick="showPup(this)"></a>
		<div class="h-txt">请选择单号</div>
	</div>
	<div class="search-box"><input type="search" class="txt" placeholder="订单号"></div>
	<ul class="list-txt">
		
	</ul>
</div>
<div class="pup-obox" id="AttaBox">
	<div class="header">
		<a href="javascript:void(0);" class="go-back js-cancle"></a>
		<div class="h-txt">请选择附件</div>
	</div>
	<div class="choose-attr" id="b">
		<label class="checkhid item" data-id="b">
			<input type="checkbox" name="1">
			<div class="pic"><img src="/resources/images/ico-doc.png"></div>
			<div class="name">附件1</div>
			<div class="time">2018-09-09 12:00</div>
			<div class="check_box"></div>
		</label>
		<label class="checkhid item" data-id="b">
			<input type="checkbox" name="1">
			<div class="pic"><img src="/resources/images/ico-doc.png"></div>
			<div class="name">附件1</div>
			<div class="time">2018-09-09 12:00</div>
			<div class="check_box"></div>
		</label>
	</div>
	<div class="attr-bottom">
		<div class="fl">已选择：1</div>
		<input type="button" value="确定" class="btn btn-blue">
	</div>
</div>
<div class="pup-obox" id="ProDetailBox">
	<a href="javascript:void(0);" class="go-back"  data-id="ProBox"  onclick="showPup(this)"></a>
	<ul class="pd-tab">
		<li class="cur"><b>商品</b></li>
		<li><b>参数</b></li>
		<li><b>资料</b></li>
	</ul>
	<div class="tabContant">
		
	</div>
</div>
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script src="/resources/js/swiper.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>

<script type="text/javascript">
	var swiper = new Swiper('.pro-banner', {
        pagination: '.swiper-pagination',
        paginationType: 'fraction'
    });
	$("label.checkhid").live("click", function () {
		var id = $(this).attr("data-id");
		if($(this).find("input").attr("type")=="checkbox"){
	    	if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
	       		$(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
	    	} else {
	        	$(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
	    	}
	    }else{
	    	if ($(this).find("input[type=radio]:checked").val() == undefined) {
	         $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
	     	} else {
	         $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
	         $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
	   		}
		}
	});
	$().ready(function(){
		$(".js-cancle").click(function(){
			$(".pup-obox").hide()
			$("body").attr("style","overflow:auto")
		})
		//根据客户姓名搜索客户
		$("#searchByCustomerName").blur(function(){
			var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
			showPup(e)
		})
		$("#searchByCustomerName").keypress(function (e) {
	        if (e.which == 13) {
	        	var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
				showPup(e)
	        }
		})
		//根据仓库名称搜索仓库
		$("#searchByWarehouseName").blur(function(){
			var e = $('.containt').find('dd[data-id="WarehouseBox"]')[0]
			showPup(e)
		})
		$("#searchByWarehouseName").keypress(function (e) {
	        if (e.which == 13) {
	        	var e = $('.containt').find('dd[data-id="WarehouseBox"]')[0]
				showPup(e)
	        }
		})
		//根据商品名称搜索商品
		$("#searchByProductName").blur(function(){
			var e = $('.containt').find('a[data-id="ProBox"]')[0]
			showPup(e)
		})
		$("#searchByProductName").keypress(function (e) {
	        if (e.which == 13) {
	        	var e = $('.containt').find('a[data-id="ProBox"]')[0]
				showPup(e)
	        }
		})
		//根据电话搜索收货地址
		$("#searchByMobileName").blur(function(){
			var e = $('.containt').find('a[data-id="AddressBox"]')[0]
			showPup(e)
		})
		$("#searchByMobileName").keypress(function (e) {
	        if (e.which == 13) {
	        	var e = $('.containt').find('a[data-id="AddressBox"]')[0]
				showPup(e)
	        }
		})
		//产品等级选择
		$("select#productGrade").change(function(){
			$("#ProBox1").find("input[id='productGrade']").val($(this).val())
			var id = "ProBox1"

			var organizationId = $("#organizationId").val()
		    if(organizationId==7){
		     organizationId=125;
		    }else if(organizationId==8){
		     organizationId=381;
		    }else if(organizationId==10){
		     organizationId=742;
		    }
			
			//查询库存
			$.ajax({
				type:'POST',
				url:'/stock/stock/stockInfolist_data.jhtml',
				data:{itemGrade:$($.find('select[id="productGrade"]')).find("option:selected").text(),warehouseId:$("#warehouseId").val(),itemCode:$('#'+id).find('input[id="vonderCode"]').val(),organizationId:organizationId},
				success:function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content);
						if(rows != null && rows.length > 0){
			            	var row = rows[0];
			            	$('#'+id).find('span[id="productInventory"]').html(row.attQuantity2)
						}else{
							$('#'+id).find('span[id="productInventory"]').html(0)
						}
					
					}
				}
			})
		})
	})
	function showPup(e){
		var id = $(e).attr("data-id")
		$(".pup-obox").hide()
		$("#"+id).show()
		if(id == "CustomerBox"){//选择客户
			$('#'+id).find('ul').empty()
			$.ajax({
				type:'POST',
				url:'/member/store/select_store_data.jhtml',
				data:{name:$('#searchByCustomerName').val()},
				success:function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content).content;
						for (var i = 0; i < rows.length; i++) {
			            	var row = rows[i];
			    			var html = "<li data-id='CustomerBox' onclick='selectItem(this)'><a href='#'>"
			    			+"<div class='name'>"+row.name+"</div>"
			    			+"<div class='fl'><span>机构：</span>"+row.sale_org_name+"</div>"
			    			+"<div class='fr'><span>编码：</span>"+row.out_trade_no+"</div>"
			    			+"</a>"
			    			+"<input type='hidden' id='name' value='"+row.name+"'/>"
			    			+"<input type='hidden' id='storeId' value='"+row.id+"'/>"
			    			+"<input type='hidden' id='sale_org_name' value='"+row.sale_org_name+"'/>"
			    			+"<input type='hidden' id='sale_org_id' value='"+row.sale_org_id+"'/>"
			    			+"<input type='hidden' id='business_type_value' value='"+row.business_type_value+"'/>"
			    			+"<input type='hidden' id='business_type_id' value='"+row.business_type_id+"'/>"
			    			+"<input type='hidden' id='store_member_name' value='"+row.store_member_name+"'/>"//区域经理
			    			+"<input type='hidden' id='storeMemberId' value='"+row.store_member+"'/></li>";//区域经理Id
			    			$('#'+id).find('ul').append(html)
						}
					}
				}
			})
		}
		if(id == "WarehouseBox"){//选择仓库
			$('#'+id).find('ul').empty()
			$.ajax({
				type:'POST',
				url:'/stock/warehouse/select_warehouse_data.jhtml',
				data:{saleOrgId:$('#sale_org_id').val(),name:$('#searchByWarehouseName').val(),sbuId:$('#sbuId').val()},
				success:function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content).content;
						for (var i = 0; i < rows.length; i++) {
			            	var row = rows[i];
			    			var html = "<li data-id='WarehouseBox' onclick='selectItem(this)'><a href='#'>"
			    			+"<div class='fl'>"+row.name+"</div>"
			    			+"<div class='fr'>"+row.erp_warehouse_code+"</div>"
			    			+"</a>"
			    			+"<input type='hidden' id='warehouseName' value='"+row.name+"'/>"
			    			+"<input type='hidden' id='type_system_dict_value' value='"+row.type_system_dict_value+"'/>"
			    			+"<input type='hidden' id='type_system_dict_id' value='"+row.type_system_dict_id+"'/>"
			    			+"<input type='hidden' id='management_organization_name' value='"+row.management_organization_name+"'/>"
			    			+"<input type='hidden' id='organizationId' value='"+row.management_organization_id+"'/>"
			    			+"<input type='hidden' id='warehouseId' value='"+row.id+"'/></li>";
			    			$('#'+id).find('ul').append(html)
						}
					}
				}
			})
		}
		if(id == "ProBox"){//获取商品信息
			if($('#storeId').val() == null || $('#storeId').val() == ""){
				alert("请选择客户")
				$(".pup-obox").hide()
				$("#"+id).hide()
				return false
			}
			if($('#warehouseId').val() == null || $('#warehouseId').val() == ""){
				alert("请选择仓库")
				$(".pup-obox").hide()
				$("#"+id).hide()
				return false
			}
			$('#'+id).find('ul').empty()
			$.ajax({
				type:'POST',
				url:'/product/product/selectProductData.jhtml',
				data:{typeSystemDictId:$('#type_system_dict_id').val(),isToOrder:true,isPart:0,storeId:$('#storeId').val(),name:$('#searchByProductName').val(),sbuId:$('#sbuId').val(),memberRankId:$('#memberRankId').val()},
				success:function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content).content;
						for (var i = 0; i < rows.length; i++) {
			            	var row = rows[i];
			    			var html = "<li><div class='pic productdetail'><img src='"+row.image+"'></div>"
			    				+"<div class='txt'>"
			    					+"<p class='name'>"+row.name+"</p>"
			    					/* +"<p>商品编码："+row.vonder_code+"</p>" */
			    					+"<p>型号："+row.model+"</p>"
			    					if(row.spec != null && row.spec != ""){
			    						html += "<p>规格："+row.spec+"</p>"
			    					}else{
			    						html += "<p>规格：</p>"
			    					}
			    					if(row.product_grade == 1){
				            			html+="	<p>等级：优等品</p>"
				            		}else if(row.product_grade == 2){
				            			html+="	<p>等级：二等品</p>"
				            		}else if(row.product_grade == 3){
				            			html+="	<p>等级：一等品</p>"
				            		}else if(row.product_grade == 4){
				            			html+="	<p>等级：无等级</p>"
				            		}else{
				            			html+="	<p>等级：</p>"
				            		}
			    					/* html+="<p>等级："+row.product_grade+"</p>" */
			    					html+="<p>分类："+row.product_category_name+"</p>"
			    					+"<p class='f-gray'><span class='f-red'>￥"+row.member_price+"</span>（销售价）</p>"
			    					+"</div>"
			    					+"<div class='btns'>"
			    					+"<a href='javascript:void(0)' class='lbtn-orgn btn'  data-id='ProDetailBox'  onclick='showPup(this)'><i class='icon ico-info'></i>商品详情</a>"
			    					+"<a href='javascript:void(0)' class='btn-orgn btn'  data-id='ProBox1'  onclick='showPup(this)'><i class='icon ico-ok'></i>选择商品</a>"
			    					+"</div>"
			    					+"<input type='hidden' id='productName' value='"+row.name+"'/>"
			    					+"<input type='hidden' id='productId' value='"+row.id+"'/>"
			    					+"<input type='hidden' id='productCategoryId' value='"+row.product_category_id+"'/>"
			    					+"<input type='hidden' id='productImage' value='"+row.image+"'/>"
			    					+"<input type='hidden' id='productGrade' value='"+row.product_grade+"'/>"
			    					+"<input type='hidden' id='vonderCode' value='"+row.vonder_code+"'/>"
			    					+"<input type='hidden' id='productCategoryName' value='"+row.product_category_name+"'/>"
			    					+"<input type='hidden' id='productModel' value='"+row.model+"'/>"
			    					+"<input type='hidden' id='perBox' value='"+row.per_box+"'/>";//每箱平方数
			    					
			    					//如果产品没有箱支转换率
			    					if(row.branch_per_box == null || row.branch_per_box == "0" || row.per_branch == null || row.per_branch == "0"){
			    						html += "<input type='hidden' id='branchPerBox' value='"+0+"'/>"//每箱支数
			    						+"<input type='hidden' id='perBranch' value='"+0+"'/>"//每支平方数
			    					}else{
			    						html += "<input type='hidden' id='branchPerBox' value='"+row.branch_per_box+"'/>"//每箱支数
			    						+"<input type='hidden' id='perBranch' value='"+row.per_branch+"'/>"//每支平方数
			    					}
			    					html += "<input type='hidden' id='price' value='"+row.price+"'/>"
			    					+"<input type='hidden' id='memberPrice' value='"+row.member_price+"'/>"//会员价，每平方单价
			    					+"<input type='hidden' id='saleOrgPrice' value='"+row.sale_org_price+"'/>"
			    					+"<input type='hidden' id='volume' value='"+row.volume+"'/>"//体积
			    					+"<input type='hidden' id='weight' value='"+row.weight+"'/>"//重量
			    					+"<input type='hidden' id='productDesc' value='"+row.description+"'/>"//产品描述
			    					+"</li>";
			    			$('#'+id).find('ul').append(html)
						}
					}
				}
			})
		}
		if(id == "ProDetailBox"){//商品详情
			$('#'+id).find('div[class="tabContant"]').empty()
			li = $(e).parent('div').parent('li')
			html = "<div>"
				+"<div class='swiper-container pro-banner'>"
				+"<div class='swiper-wrapper'>"
				+"        <div class='swiper-slide'><img src='"+li.find('input[id="productImage"]').val()+"'></div>"
				+"    </div>"
				+"    <div class='swiper-pagination'></div>"
				+" </div>"
				+"<div class='pro-info'>"
				+"	<div class='name'>"+li.find('input[id="productName"]').val()+"</div>"
				+"	<p>产品型号："+li.find('input[id="productModel"]').val()+"</p>"
				+"	<p>产品分类："+li.find('input[id="productCategoryName"]').val()+"</p>"
				+"	<p>销售价：<em class='f-red'>￥"+li.find('input[id="memberPrice"]').val()+"</em></p>"
				+"</div>"
				/* +"<div class='pro-detail'>"
				+"	<img src='/resources/images/test002.jpg'>"
				+"</div>" */
				+"</div>";
			$('#'+id).find('div[class="tabContant"]').append(html)
		}
		if(id == "ProBox1"){//选择商品 
			li = $(e).parent('div').parent('li')
			if($(e).html() != ""){
				$('#'+id).find('span[id="productName"]').html(li.find('input[id="productName"]').val())
				$('#'+id).find('span[id="productDesc"]').html(li.find('input[id="productDesc"]').val())
				$('#'+id).find('span[id="vonderCode"]').html(li.find('input[id="vonderCode"]').val())
				$('#'+id).find('span[id="productCategoryName"]').html(li.find('input[id="productCategoryName"]').val())
				$('#'+id).find('span[id="memberPrice"]').html(li.find('input[id="memberPrice"]').val())

				/* $('#'+id).find('input[id="productBoxNum"]').val(1)
				$('#'+id).find('input[id="productBranchNum"]').val(li.find('input[id="branchPerBox"]').val())
				$('#'+id).find('span[id="productScatteredNum"]').html(0)
				$('#'+id).find('span[id="volume"]').html(parseFloat(li.find('input[id="volume"]').val()).toFixed(6)) */
				
				//如果产品没有箱支转换率
				if(li.find('input[id="branchPerBox"]').val() == null || li.find('input[id="branchPerBox"]').val() == "0" 
					|| li.find('input[id="perBranch"]').val() == null || li.find('input[id="perBranch"]').val() == "0")
				{
					$('#'+id).find('input[id="productBoxNum"]').prev().hide()					//隐藏数量－按钮
					$('#'+id).find('input[id="productBoxNum"]').next().hide()					//隐藏数量+按钮
					$('#'+id).find('input[id="productBoxNum"]').parent().find("span").show()	//显示内容为'-'的span
					$('#'+id).find('input[id="productBoxNum"]').hide()							//隐藏输入框
					
					$('#'+id).find('input[id="productBranchNum"]').prev().hide()				//隐藏数量－按钮
					$('#'+id).find('input[id="productBranchNum"]').next().hide()				//隐藏数量+按钮
					$('#'+id).find('input[id="productBranchNum"]').parent().find("span").show()	//显示内容为'-'的span
					$('#'+id).find('input[id="productBranchNum"]').hide()						//隐藏输入框
					
					$('#'+id).find('span[id="productScatteredNum"]').parent().parent().hide()	//隐藏零散支数
					$('#'+id).find('span[id="volume"]').parent().parent().hide()				//隐藏体积
					$('#'+id).find('input[id="productPerNum"]').val(0)							//下单平方数默认为0
					$('#'+id).find('span[id="weight"]').html(0)									//下单总量默认为0
					$('#'+id).find('span[id="totalPrice"]').html(0)								//金额默认为0
				}else{
					$('#'+id).find('input[id="productBoxNum"]').show()							//显示输入框
					$('#'+id).find('input[id="productBoxNum"]').val(1)							//恢复箱输入框的默认值为1
					$('#'+id).find('input[id="productBoxNum"]').prev().show()					//显示数量－按钮
					$('#'+id).find('input[id="productBoxNum"]').next().show()					//显示数量+按钮
					$('#'+id).find('input[id="productBoxNum"]').parent().find("span").hide()	//隐藏内容为'-'的span
					
					$('#'+id).find('input[id="productBranchNum"]').show()						//显示输入框
					$('#'+id).find('input[id="productBranchNum"]').val(li.find('input[id="branchPerBox"]').val())
					$('#'+id).find('input[id="productBranchNum"]').prev().show()				//显示数量－按钮
					$('#'+id).find('input[id="productBranchNum"]').next().show()				//显示数量+按钮
					$('#'+id).find('input[id="productBranchNum"]').parent().find("span").hide()	//隐藏内容为'-'的span
					
					$('#'+id).find('span[id="productScatteredNum"]').parent().parent().show()	//显示零散支数
					$('#'+id).find('span[id="volume"]').parent().parent().show()				//显示体积
					$('#'+id).find('span[id="productScatteredNum"]').html(0)					//恢复零散支数的默认值为0
					$('#'+id).find('span[id="volume"]').html(parseFloat(li.find('input[id="volume"]').val()).toFixed(6))
					$('#'+id).find('input[id="productPerNum"]').val(parseFloat(li.find('input[id="perBox"]').val()).toFixed(6))
					$('#'+id).find('span[id="weight"]').html((parseFloat(li.find('input[id="weight"]').val()==null?0:li.find('input[id="weight"]').val())*parseFloat(li.find('input[id="perBox"]').val())).toFixed(6))
					$('#'+id).find('span[id="totalPrice"]').html((li.find('input[id="perBox"]').val()*li.find('input[id="memberPrice"]').val()).toFixed(2))
				}
				$('#'+id).find('input[id="priceApplyItemSn"]').val("")
				$('#'+id).find('input[id="priceApplyItemId"]').val("")
				$('#'+id).find('input[id="deliveryTime"]').val("")
				var productGrade = li.find('input[id="productGrade"]').val()/* 产品等级 */
				if(productGrade == 1){
					$($('#'+id).find('select[id="productGrade"]')[0]).find('option[value="1"]').attr("selected","selected")
				}
				if(productGrade == 2){
					$($('#'+id).find('select[id="productGrade"]')[0]).find('option[value="2"]').attr("selected","selected")
				}
				if(productGrade == 3){
					$($('#'+id).find('select[id="productGrade"]')[0]).find('option[value="3"]').attr("selected","selected")
				}
				if(productGrade == 4){
					$($('#'+id).find('select[id="productGrade"]')[0]).find('option[value="4"]').attr("selected","selected")
				}
				//添加隐藏信息
				$('#'+id).find('input[id="productName"]').val(li.find('input[id="productName"]').val())
				$('#'+id).find('input[id="productDesc"]').val(li.find('input[id="productDesc"]').val())
				$('#'+id).find('input[id="productId"]').val(li.find('input[id="productId"]').val())
				$('#'+id).find('input[id="productCategoryId"]').val(li.find('input[id="productCategoryId"]').val())
				$('#'+id).find('input[id="vonderCode"]').val(li.find('input[id="vonderCode"]').val())
				$('#'+id).find('input[id="productGrade"]').val(li.find('input[id="productGrade"]').val())
				//如果产品没有箱支转换率，下单箱数默认为0
				if(li.find('input[id="branchPerBox"]').val() == null || li.find('input[id="branchPerBox"]').val() == "0" || li.find('input[id="perBranch"]').val() == null || li.find('input[id="perBranch"]').val() == "0"){
					$('#'+id).find('input[id="boxQuantity"]').val(0)//箱数
				}else{
					$('#'+id).find('input[id="boxQuantity"]').val(1)//箱数
				}
				$('#'+id).find('input[id="branchQuantity"]').val(li.find('input[id="branchPerBox"]').val())//支数
				$('#'+id).find('input[id="scatteredQuantity"]').val(0)//零散支数
				$('#'+id).find('input[id="branchPerBox"]').val(li.find('input[id="branchPerBox"]').val())//每箱支数
				$('#'+id).find('input[id="perBox"]').val(li.find('input[id="perBox"]').val())//每箱平方数
				$('#'+id).find('input[id="perBranch"]').val(li.find('input[id="perBranch"]').val())//每支平方数
				$('#'+id).find('input[id="totalPer"]').val(parseFloat(li.find('input[id="perBox"]').val()).toFixed(6))//总平方数
				$('#'+id).find('input[id="price"]').val(li.find('input[id="memberPrice"]').val())//保存原价
				$('#'+id).find('input[id="saleOrgPrice"]').val(li.find('input[id="saleOrgPrice"]').val())
				$('#'+id).find('input[id="memberPrice"]').val(li.find('input[id="memberPrice"]').val())//会员价，每平方单价
				$('#'+id).find('input[id="volume"]').val(parseFloat(li.find('input[id="volume"]').val()).toFixed(6))
				$('#'+id).find('input[id="weight"]').val(parseFloat(li.find('input[id="weight"]').val()).toFixed(6))
				$('#'+id).find('input[id="totalVolume"]').val(parseFloat(li.find('input[id="volume"]').val()).toFixed(6))
				$('#'+id).find('input[id="totalWeight"]').val((parseFloat(li.find('input[id="weight"]').val())*parseFloat(li.find('input[id="perBox"]').val())).toFixed(6))
				$('#'+id).find('input[id="totalPrice"]').val((li.find('input[id="perBox"]').val()*li.find('input[id="memberPrice"]').val()).toFixed(2))
				
				var organizationId = $("#organizationId").val()
			    if(organizationId==7){
			     organizationId=125;
			    }else if(organizationId==8){
			     organizationId=381;
			    }else if(organizationId==10){
			     organizationId=742;
			    }
				
				//查询库存
				$.ajax({
					type:'POST',
					url:'/stock/stock/stockInfolist_data.jhtml',
					data:{itemGrade:$($.find('select[id="productGrade"]')).find("option:selected").text(),warehouseId:$("#warehouseId").val(),itemCode:$('#'+id).find('input[id="vonderCode"]').val(),organizationId:organizationId},
					success:function(data) {
						if(data.type == 'success'){
							var rows = JSON.parse(data.content);
							if(rows != null && rows.length > 0){
				            	var row = rows[0];
				            	$('#'+id).find('span[id="productInventory"]').html(row.attQuantity2)
							}else{
								$('#'+id).find('span[id="productInventory"]').html(0)
							}
						
						}
					}
				})
			}
			
		}
		if(id == "AddressBox"){//更换收货地址
			if($('#storeId').val() == null || $('#storeId').val() == ""){
				alert("请选择客户")
				$(".pup-obox").hide()
				$("#"+id).hide()
				return false
			}
			$('#'+id).find('ul').empty()
			$.ajax({
				type:'POST',
				url:'/member/store/select_store_address_data.jhtml',
				data:{storeId:$('#storeId').val(),mobile:$("#searchByMobileName").val()},
				success:function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content).content;
						for (var i = 0; i < rows.length; i++) {
			            	var row = rows[i];
			    			var html = "<li data-id='AddressBox' onclick='selectItem(this)'><a href='#'>"
			    			+"<div class='fl'>"+row.consignee+"</div>"
			    			+"<div class='fr'>"+row.mobile+"</div>"
			    			+"<div class='clear'>"
			    			+"<span>"+row.address+"</span>"
			    			+"</div>"
			    			+"</a>"
			    			+"<input type='hidden' id='receiveInfoName' value='"+row.consignee+"'/>"
			    			+"<input type='hidden' id='receiveInfoPhone' value='"+row.mobile+"'/>"
			    			+"<input type='hidden' id='receiveInfoAddress' value='"+row.address+"'/>"
			    			+"<input type='hidden' id='receiveInfoArea' value='"+row.area_full_name+"'/>"
			    			+"<input type='hidden' id='areaId' value='"+row.area+"'/>"
			    			+"<input type='hidden' id='out_trade_no' value='"+row.out_trade_no+"'/>"
			    			+"</li>";
			    			$('#'+id).find('ul').append(html)
						}
					}
				}
			})
		}
		if(id == "SpecialOfferBox"){//特价单号
			$('#'+id).find('ul').empty()
			var storeId = $('#storeId').val()
			var productId = $('#ProBox1').find("input[id='productId']").val()
			var productCategoryId = $('#ProBox1').find("input[id='productCategoryId']").val()
			var typeSystemDictId = $('#type_system_dict_id').val()
			var productGrade = $('#ProBox1').find("input[id='productGrade']").val()
			var saleOrgId = $('#sale_org_id').val()
			//var businessTypeName = $('#business_type_value').val()
			$.ajax({
				type:'POST',
				url:'/b2b/priceApply/select_price_apply_item_data.jhtml',
				data:{storeId:storeId,productId:productId,productCategoryId:productCategoryId,typeSystemDictId:typeSystemDictId,productGrade:productGrade,saleOrgId:saleOrgId},
				success:function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content);
						for (var i = 0; i < rows.length; i++) {
			            	var row = rows[i];
			    			var html = "<li data-id='SpecialOfferBox' onclick='selectItem(this)'><a href='#'>"
				    			+"<div class='name'>特价单号:"+row.sn+"<em class='fr f12 f-gray'>"+row.start_date+" - "+row.end_date+"</em></div>"
				    			+"<div class='fl'>特价价格：<em class='f-red'>￥"+row.price+"</em></div>"
				    			+"<div class='fr'><span>最小/最大数量：</span><em class='f-red'>"+row.min_quantity+"</em>/"+row.max_quantity+"</div>"
				    			+"</a>"
				    			+"<input type='hidden' id='priceApplyItemId' value='"+row.id+"'/>"
				    			+"<input type='hidden' id='priceApplyItemType' value='"+row.type+"'/>"
				    			+"<input type='hidden' id='priceApplyItemSn' value='"+row.sn+"'/>"
				    			+"<input type='hidden' id='priceApplyItemPrice' value='"+row.price+"'/>"
				    			+"<input type='hidden' id='saleOrgSalesPrice' value='"+row.sale_org_sales_price+"'/>"/* 平台结算特价 */
				    			+"</li>";
			    			$('#'+id).find('ul').append(html)
						}
					}
				}
			})
		}
	}
	function selectItem(e){
		var id = $(e).attr("data-id")
		$(".pup-obox").hide()
		$("#"+id).hide()
		if(id == "CustomerBox"){//选择客户
			if($(e).find('input[id="storeId"]').val() != $("#storeId").val()){//与之前选择客户不相同
				//清除产品
				$(".oPro-list").empty()
				//订单汇总置空
				$("#orderBoxNum").html(0)
				$("#orderBranchNum").html(0)
				$("#orderPerNum").html(0)
				$("#orderWeightNum").html(0)
				$("#orderVolumeNum").html(0)
				$("#orderTotalPrice").html(0)
				$("#orderBalancePrice").html(0)
				//收货地址置空
				$("#out_trade_no").val("")
				$("#areaId").val("")
				$("#receiveInfoName").html("")
				$("#receiveInfoPhone").html("")
				$("#receiveInfoArea").html("")
				$("#receiveInfoAddress").html("")
				$("#out_trade_no_display").html("")
				
				$('#name').val($(e).find('input[id="name"]').val())
				$('#storeId').val($(e).find('input[id="storeId"]').val())
				$('#sale_org_name').val($(e).find('input[id="sale_org_name"]').val())
				$('#sale_org_id').val($(e).find('input[id="sale_org_id"]').val())
				$('#business_type_value').val($(e).find('input[id="business_type_value"]').val())
				$('#business_type_id').val($(e).find('input[id="business_type_id"]').val())
				$('#store_member_name').val($(e).find('input[id="store_member_name"]').val())
				$('#storeMemberId').val($(e).find('input[id="storeMemberId"]').val())
				
				//带出默认收货地址
				$.ajax({
					type:'POST',
					url:'/member/store/select_store_address_data.jhtml',
					data:{storeId:$('#storeId').val()},
					success:function(data) {
						if(data.type == 'success'){
							var rows = JSON.parse(data.content).content;
				            if(rows.length > 0){
				            	var row = rows[0];
					            $('#receiveInfoName').html(row.consignee)
								$('#receiveInfoPhone').html(row.mobile)
								$('#receiveInfoAddress').html(row.address)
								$("#out_trade_no_display").html(row.out_trade_no)
								$('#receiveInfoArea').html(row.area_full_name)
								$('#areaId').val(row.area)
								$('#out_trade_no').val(row.out_trade_no)
				            }
						}
					}
				})
				//查询客户余额
				$.ajax({
					type:'POST',
					url:'/finance/balance/get_balance.jhtml',
					data:{storeId:$('#storeId').val(),sbuId:$('#sbuId').val(),saleOrgId:$('#sale_org_id').val()},
					success:function(data) {
						if(data.type == 'success'){
							$('#orderBalancePrice').html('￥'+data.objx.balance)
							$('#storeBalance').val(data.objx.balance)
						}
					}
				})
			}
		}
		if(id == "WarehouseBox"){//选择仓库
			$('#warehouseName').val($(e).find('input[id="warehouseName"]').val())
			$('#type_system_dict_value').val($(e).find('input[id="type_system_dict_value"]').val())
			$('#type_system_dict_id').val($(e).find('input[id="type_system_dict_id"]').val())
			$('#management_organization_name').val($(e).find('input[id="management_organization_name"]').val())
			$('#warehouseId').val($(e).find('input[id="warehouseId"]').val())
			$('#organizationId').val($(e).find('input[id="organizationId"]').val())
			//查询客户余额
			$.ajax({
				type:'POST',
				url:'/finance/balance/get_balance.jhtml',
				data:{storeId:$('#storeId').val(),organizationId:$('#organizationId').val(),sbuId:$('#sbuId').val(),saleOrgId:$('#sale_org_id').val()},
				success:function(data) {
					if(data.type == 'success'){
						$('#orderBalancePrice').html('￥'+data.objx.balance)
						$('#storeBalance').val(data.objx.balance)
					}
				}
			})
		}
		if(id == "ProBox1"){//保存选择的产品
			var productName = $('#'+id).find('input[id="productName"]').val();
			var productId = $('#'+id).find('input[id="productId"]').val();
			var vonderCode = $('#'+id).find('input[id="vonderCode"]').val();
			var productGrade = $('#'+id).find('input[id="productGrade"]').val();
			var boxQuantity = $('#'+id).find('input[id="boxQuantity"]').val();//箱数
			var branchQuantity = $('#'+id).find('input[id="branchQuantity"]').val();//支数
			var scatteredQuantity = $('#'+id).find('input[id="scatteredQuantity"]').val();//零散支数
			var branchPerBox = $('#'+id).find('input[id="branchPerBox"]').val();//每箱支数
			var perBox = $('#'+id).find('input[id="perBox"]').val();// 每箱平方数
			var perBranch = $('#'+id).find('input[id="perBranch"]').val();//每支平方数
			var totalPer = $('#'+id).find('input[id="totalPer"]').val();//总平方数
			var price = $('#'+id).find('input[id="price"]').val();
			var saleOrgPrice = $('#'+id).find('input[id="saleOrgPrice"]').val();
			var memberPrice = $('#'+id).find('input[id="memberPrice"]').val();//每平方单价
			var volume = $('#'+id).find('input[id="volume"]').val();//每箱体积
			var totalVolume = $('#'+id).find('input[id="totalVolume"]').val();//总体积
			var weight = $('#'+id).find('input[id="weight"]').val();//每平方数重量
			var totalWeight = $('#'+id).find('input[id="totalWeight"]').val();//总重量
			var totalPrice = $('#'+id).find('input[id="totalPrice"]').val();//该产品总金额
			var priceApplyItemId = $('#'+id).find('input[id="priceApplyItemId"]').val();//特价单ID
			var deliveryTime = $('#'+id).find('input[id="deliveryTime"]').val()//交货期
			var sellerMemo = $('#'+id).find('textarea[id="sellerMemo"]').val()//备注
			//如果产品没有箱转换率
			if(li.find('input[id="branchPerBox"]').val() == null || li.find('input[id="branchPerBox"]').val() == "0" || li.find('input[id="perBranch"]').val() == null || li.find('input[id="perBranch"]').val() == "0"){
				if(totalPer == 0 || isNaN(totalPer)){
					alert("平方数不允许为0")
					$(".pup-obox").hide()
					$("#"+id).show()
					return false
				}
			}else{
				if(Math.round(branchQuantity) != branchQuantity || branchQuantity == 0){
					alert("支数不允许是小数，必须为整数")
					$(".pup-obox").hide()
					$("#"+id).show()
					return false
				}
			}
			if($('#'+id).find('input[id="priceApplyItemType"]').val() == 3){
				//特价类型为工程：业务类型改为商业地板
				/* $('#business_type_id').val(182)
				$('#business_type_value').val("商业地板") */
			}
	   		var productGradeDesc = ""
	   		if(productGrade == 1){
	   			productGradeDesc = "优等品"
	   		}
	   		if(productGrade == 2){
	   			productGradeDesc = "二等品"
	   		}
	   		if(productGrade == 3){
	   			productGradeDesc = "一等品"
	   		}
	   		if(productGrade == 4){
	   			productGradeDesc = "无等级"
	   		}
			html = "<div class='item' onclick='onShow(this)' id='itemLists'>"
				+"<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
				+"<div class='fl'>"+productName+"</div>"
				+"<div class='fr f-red'>￥"+totalPrice+"</div>"
				+"	<div class='data'>"
				+"		<span><div style='float:left'>产品等级：</div><div style='float:right'>"+productGradeDesc+"</div></span>";
				//如果产品没有箱支转换率
				if(boxQuantity == '0' || branchQuantity == '0')
				{
					html += 
						"<span><div style='float:left'>下单箱数：</div><div style='float:right'>-</div></span>"+
						"<span><div style='float:left'>支数：</div><div style='float:right'>-</div></span>"+
						"<span><div style='float:left'>零散支数：</div><div style='float:right'>-</div></span>"
				}else{
					html += 
						"<span><div style='float:left'>下单箱数：</div><div style='float:right'>"+boxQuantity+"</div></span>"+
						"<span><div style='float:left'>支数：</div><div style='float:right'>"+parseFloat(branchQuantity).toFixed(0)+"</div></span>"+
						"<span><div style='float:left'>零散支数：</div><div style='float:right'>"+parseFloat(scatteredQuantity).toFixed(0)+"</div></span>"
				}
				html +=
				"		<span><div style='float:left'>平方数：</div><div style='float:right'>"+totalPer+"</div></span>"
				+"		<span><div style='float:left'>单价/平方米：</div><div style='float:right' class='f-red'>￥"+parseFloat(memberPrice).toFixed(2)+"</div></span>"
				//如果产品没有箱支转换率
				if(boxQuantity == '0' || branchQuantity == '0')
				{
					html += 
					"		<span><div style='float:left'>体积：</div><div style='float:right'>-</div></span>"
				}else{
					html += 
					"		<span><div style='float:left'>体积：</div><div style='float:right'>"+totalVolume+"</div></span>"
				}
				html +=
				"		<span><div style='float:left'>重量：</div><div style='float:right'>"+totalWeight+"</div></span>"
				+"	</div>"
				+"<input type='hidden' id='productName' value='"+productName+"'/>"
				+"<input type='hidden' id='productId' value='"+productId+"'/>"
				+"<input type='hidden' id='vonderCode' value='"+vonderCode+"'/>"
				+"<input type='hidden' id='productGrade' value='"+productGrade+"'/>"
				+"<input type='hidden' id='boxQuantity' value='"+boxQuantity+"'/>"
				+"<input type='hidden' id='branchQuantity' value='"+branchQuantity+"'/>"
				+"<input type='hidden' id='scatteredQuantity' value='"+scatteredQuantity+"'/>"
				+"<input type='hidden' id='branchPerBox' value='"+branchPerBox+"'/>"
				+"<input type='hidden' id='perBox' value='"+perBox+"'/>"
				+"<input type='hidden' id='perBranch' value='"+perBranch+"'/>"
				+"<input type='hidden' id='totalPer' value='"+totalPer+"'/>"
				+"<input type='hidden' id='price' value='"+price+"'/>"
				+"<input type='hidden' id='saleOrgPrice' value='"+saleOrgPrice+"'/>"
				+"<input type='hidden' id='memberPrice' value='"+memberPrice+"'/>"
				+"<input type='hidden' id='volume' value='"+volume+"'/>"
				+"<input type='hidden' id='totalVolume' value='"+totalVolume+"'/>"
				+"<input type='hidden' id='weight' value='"+weight+"'/>"
				+"<input type='hidden' id='totalWeight' value='"+totalWeight+"'/>"
				+"<input type='hidden' id='totalPrice' value='"+totalPrice+"'/>"
				+"<input type='hidden' id='priceApplyItemId' value='"+priceApplyItemId+"'/>"
				+"<input type='hidden' id='deliveryTime' value='"+deliveryTime+"'/>"
				+"<input type='hidden' id='sellerMemo' value='"+sellerMemo+"'/>"
				+"</div>";
			$('.oPro-list').append(html);
			//订单箱数
			$('#orderBoxNum').html(parseInt($('#orderBoxNum').html()) + parseInt(boxQuantity))
			//订单支数
			$('#orderBranchNum').html(parseInt($('#orderBranchNum').html()) + parseInt(branchQuantity))
			//订单平方数
			$('#orderPerNum').html((parseFloat($('#orderPerNum').html()) + parseFloat(totalPer)).toFixed(6))
			//订单重量
			$('#orderWeightNum').html((parseFloat($('#orderWeightNum').html()) + parseFloat(totalWeight)).toFixed(6))
			//订单体积
			$('#orderVolumeNum').html((parseFloat($('#orderVolumeNum').html()) + parseFloat(totalVolume)).toFixed(6))
			//订单金额
			$('#orderTotalPrice').html((parseFloat($('#orderTotalPrice').html()) + parseFloat(totalPrice)).toFixed(2))
		}
		if(id == "AddressBox"){//选择收货地址
			$('#receiveInfoName').html($(e).find('input[id="receiveInfoName"]').val())
			$('#receiveInfoPhone').html($(e).find('input[id="receiveInfoPhone"]').val())
			$('#receiveInfoAddress').html($(e).find('input[id="receiveInfoAddress"]').val())
			$('#receiveInfoArea').html($(e).find('input[id="receiveInfoArea"]').val())
			$('#areaId').val($(e).find('input[id="areaId"]').val())
			$('#out_trade_no').val($(e).find('input[id="out_trade_no"]').val())
			$('#out_trade_no_display').html($(e).find('input[id="out_trade_no"]').val())
		}
		if(id == "SpecialOfferBox"){//选择特价单号
			var totalPrice = (parseFloat($(e).find('input[id="priceApplyItemPrice"]').val())*parseFloat($('#ProBox1').find('input[id="totalPer"]').val())).toFixed(2)
			$('#ProBox1').show()
			$('#ProBox1').find('input[id="priceApplyItemSn"]').val($(e).find('input[id="priceApplyItemSn"]').val())
			$('#ProBox1').find('input[id="priceApplyItemType"]').val($(e).find('input[id="priceApplyItemType"]').val())
			$('#ProBox1').find('input[id="priceApplyItemId"]').val($(e).find('input[id="priceApplyItemId"]').val())
			$('#ProBox1').find('input[id="saleOrgPrice"]').val($(e).find('input[id="saleOrgSalesPrice"]').val())
			$('#ProBox1').find('span[id="memberPrice"]').html($(e).find('input[id="priceApplyItemPrice"]').val())
			$('#ProBox1').find('input[id="memberPrice"]').val($(e).find('input[id="priceApplyItemPrice"]').val())
			$('#ProBox1').find('span[id="totalPrice"]').html(totalPrice)
			$('#ProBox1').find('input[id="totalPrice"]').val(totalPrice)
		}
	}
	//确定下单
	function saveOrder(num){
		var jsonData = {
			'storeId':$('#storeId').val(),
			'storeName':$('#name').val(),
			'businessTypeId':$('#business_type_id').val(),
			'businessTypeName':$('#business_type_id').find('option:selected').text(),
			'saleOrgId':$('#sale_org_id').val(),
			'organizationId':$('#organizationId').val(),
			'warehouseId':$('#warehouseId').val(),
			'warehouseName':$('#warehouseName').val(),
			'typeSystemDictId':$('#type_system_dict_id').val(),
			'paymentMethodId':$('#paymentMethodId').val(),
			'shippingMethodId':$('#shippingMethodId').val(),
			'consignee':$('#receiveInfoName').html(),
			'phone':$('#receiveInfoPhone').html(),
			'addressOutTradeNo':$('#out_trade_no').val(),
			'areaId':$('#areaId').val(),
			'areaName':$('#receiveInfoArea').html(),
			'address':$('#receiveInfoAddress').html(),
			'memo':$('#baseInfoMemo').val(),
			'storeBalance':$('#storeBalance').val(),
			'sbuId':$('#sbuId').val()
		}
		if($('#storeMemberId').val() != "null"){
			jsonData['regionalManagerId'] = $('#storeMemberId').val()
		}
		var products = $('.oPro-list').find('div[id="itemLists"]')
		for (var i = 0; i < products.length; i++) {
			jsonData['orderItems['+i+'].woodTypeOrColor'] = $(products[i]).find('input[id="vonderCode"]').val()
			jsonData['orderItems['+i+'].product.id'] = $(products[i]).find('input[id="productId"]').val()
			jsonData['orderItems['+i+'].productGrade'] = $(products[i]).find('input[id="productGrade"]').val()
			jsonData['orderItems['+i+'].boxQuantity'] = $(products[i]).find('input[id="boxQuantity"]').val()
			jsonData['orderItems['+i+'].branchQuantity'] = $(products[i]).find('input[id="branchQuantity"]').val()
			jsonData['orderItems['+i+'].scatteredQuantity'] = $(products[i]).find('input[id="scatteredQuantity"]').val()
			jsonData['orderItems['+i+'].branchPerBox'] = $(products[i]).find('input[id="branchPerBox"]').val()
			jsonData['orderItems['+i+'].quantity'] = $(products[i]).find('input[id="totalPer"]').val()
			jsonData['orderItems['+i+'].perBranch'] = $(products[i]).find('input[id="perBranch"]').val()
			jsonData['orderItems['+i+'].saleOrgPrice'] = $(products[i]).find('input[id="saleOrgPrice"]').val()
			jsonData['orderItems['+i+'].proPriceHeadPrice'] = $(products[i]).find('input[id="price"]').val()//价格表原价
			jsonData['orderItems['+i+'].price'] = $(products[i]).find('input[id="memberPrice"]').val()
			jsonData['orderItems['+i+'].deliveryTime'] = $(products[i]).find('input[id="deliveryTime"]').val()
			jsonData['orderItems['+i+'].sellerMemo'] = $(products[i]).find('input[id="sellerMemo"]').val()
			if($(products[i]).find('input[id="priceApplyItemId"]').val() != null && $(products[i]).find('input[id="priceApplyItemId"]').val() != ""){
				jsonData['orderItems['+i+'].priceApplyItem.id'] = $(products[i]).find('input[id="priceApplyItemId"]').val()
			}
		}
		var attachs = $('.atta-list').find('div[class="item"]')
		for (var i = 0; i < attachs.length; i++) {
			jsonData['orderAttachs['+i+'].name'] = $(attachs[i]).find('input[id="attachName"]').val()
			jsonData['orderAttachs['+i+'].url'] = $(attachs[i]).find('input[id="attachUrl"]').val()
			jsonData['orderAttachs['+i+'].suffix'] = $(attachs[i]).find('input[id="attachSuffix"]').val()
			jsonData['orderAttachs['+i+'].memo'] = $(attachs[i]).find('textarea[id="attachMemo"]').val()		
		}
		if(num == 1){//保存
			var url = "/b2b/order/save.jhtml?stat=0"
		}
		if(num == 2){//支付
			var url = "/b2b/order/save.jhtml?stat=1"
		}
		$.ajax({
			type:'POST',
			url:url,
			data:jsonData,
			success:function(data) {
				alert(data.content);
				if(data.type == "success"){
					if(num == 2){
						$.ajax({
							type:'POST',
							url:'/wf/wf_obj_config/get_config.jhtml',
							data:{obj_type_id:47,objid:data.objx},
							success:function(data_) {
								if(data_.type == "success"){
									jsonData['id'] = data.objx
									jsonData['orderId'] = data.objx
									jsonData['objConfId'] = data_.objx[0].id
									$.ajax({
										type:'POST',
										url:'/b2b/order/check_wf.jhtml',
										data:jsonData,
										success:function(data) {
											if(data.type == "success"){
												window.location.href = "/mobile/index.jhtml"
											}
										}
									})
								}
							}
						})
					}else{
						
						window.location.href = "/mobile/index.jhtml"
					}
				}
			}
		})
	}
	//删除
	function del(e){
		if($(e).parent('div').parent('div')[0] == $('.oPro-list')[0]){
			//如果移除商品，重新计算订单汇总信息
			var orderBoxNum = parseInt($('#orderBoxNum').html())-parseInt($(e).parent('div').find('input[id="boxQuantity"]').val())
			var orderBranchNum = parseInt($('#orderBranchNum').html())-parseInt($(e).parent('div').find('input[id="branchQuantity"]').val())
			var orderPerNum = (parseFloat($('#orderPerNum').html())-parseFloat($(e).parent('div').find('input[id="totalPer"]').val())).toFixed(6)
			var orderWeightNum = (parseFloat($('#orderWeightNum').html())-parseFloat($(e).parent('div').find('input[id="totalWeight"]').val())).toFixed(6)
			var orderVolumeNum = (parseFloat($('#orderVolumeNum').html())-parseFloat($(e).parent('div').find('input[id="totalVolume"]').val())).toFixed(6)
			var orderTotalPrice = (parseFloat($('#orderTotalPrice').html())-parseFloat($(e).parent('div').find('input[id="totalPrice"]').val())).toFixed(2)
			//订单箱数
			$('#orderBoxNum').html(orderBoxNum)
			//订单支数
			$('#orderBranchNum').html(orderBranchNum)
			//订单平方数
			if(orderPerNum == 0){
				$('#orderPerNum').html(0)
			}else{
				$('#orderPerNum').html(orderPerNum)
			}
			//订单重量
			if(orderWeightNum == 0){
				$('#orderWeightNum').html(0)
			}else{
				$('#orderWeightNum').html(orderWeightNum)
			}
			//订单体积
			if(orderVolumeNum == 0){
				$('#orderVolumeNum').html(0)
			}else{
				$('#orderVolumeNum').html(orderVolumeNum)
			}
			//订单金额
			if(orderTotalPrice == 0){
				$('#orderTotalPrice').html(0)
			}else{
				$('#orderTotalPrice').html(orderTotalPrice)
			}
		
		}
		$(e).parent('div').remove()
	}
	//添加附件
	function addAttach(e){
		$('#file').trigger('click'); 
	}
	//附件上传
	function fileUpload(e){
	    var formData = new FormData($( "#fileForm" )[0]);
    	$.ajax({
			type:'GET',
			url:'/common/fileurl.jhtml',
			success:function(data) {
				if(data.type == "success"){
					$.ajax({
						type:'POST',
						url: data.objx,
		                data:formData,
		                cache: false,  
		                contentType: false,  
		                processData: false, 
						success:function(data_) {
							data_ = JSON.parse(data_)
							if(data_.message.type == "success"){
								var html = "<div class='item'>"
									+"<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
									+"<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
									+"<textarea class='txt' placeholder='请输入备注' id='attachMemo'></textarea>"
									+"<input type='hidden' id='attachName' value='"+data_.file_info.name.split('.')[0]+"'/>"
									+"<input type='hidden' id='attachUrl' value='"+data_.url+"'/>"
									+"<input type='hidden' id='attachSuffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
									+"</div>"; 
								$('.atta-list').append(html)
							}
						}
					})				
				}
			}
		})
	}
	//增加
	function increase(type,event){
		var per = 0
		if(type == "per"){
			//增加平方数
			per = (parseFloat($('#ProBox1').find('input[id="productPerNum"]').val())+parseInt(1)).toFixed(6)
		}
		if(type == "box"){
			//增加箱数
			var box = parseInt($('#ProBox1').find('input[id="productBoxNum"]').val())+parseInt(1)
			per = (parseFloat(box*$('#ProBox1').find('input[id="perBox"]').val())).toFixed(6)
		}
		if(type == "branch"){
			//增加支数
			var branch = parseInt($('#ProBox1').find('input[id="productBranchNum"]').val())+parseInt(1)
			per = (parseFloat(branch*$('#ProBox1').find('input[id="perBranch"]').val())).toFixed(6)
		}
		calculateData(per,null,null)
	}
	//减少
	function decrease(type,event){
		var per = 0
		if(type == "per"){
			//减少平方数
			if(parseFloat($('#ProBox1').find('input[id="productPerNum"]').val()) < 1 || parseFloat($('#ProBox1').find('input[id="productPerNum"]').val()) == 1){
				per = 0
			}else{
				per = (parseFloat($('#ProBox1').find('input[id="productPerNum"]').val())-parseInt(1)).toFixed(6)
			}
		}
		if(type == "box"){
			//减少箱数
			if(parseInt($('#ProBox1').find('input[id="productBoxNum"]').val()) < 1 || parseInt($('#ProBox1').find('input[id="productBoxNum"]').val()) == 1){
				var box = 0
			}else{
				var box = parseInt($('#ProBox1').find('input[id="productBoxNum"]').val())-parseInt(1)
			}
			per = (parseFloat(box*$('#ProBox1').find('input[id="perBox"]').val())).toFixed(6)
		}
		if(type == "branch"){
			//减少支数
			if(parseInt($('#ProBox1').find('input[id="productBranchNum"]').val()) < 1 || parseInt($('#ProBox1').find('input[id="productBranchNum"]').val()) == 1){
				var branch = 0
			}else{
				var branch = parseInt($('#ProBox1').find('input[id="productBranchNum"]').val())-parseInt(1)
			}
			per = (parseFloat(branch*$('#ProBox1').find('input[id="perBranch"]').val())).toFixed(6)
		}
		calculateData(per,null,null)
	}
	//实时修改平方数，箱数，支数
	function input(type,e,event){
		var per = 0;
		if(type == "per"){
			if(extractNumber(e,6,false,event)){
				per = $(e).val()
			}
			calculateData(per,null,null)
		}
		if(type == "box"){
			if(extractNumber(e,3,false,event)){
				var box = $(e).val()
				per = (parseFloat(box*$('#ProBox1').find('input[id="perBox"]').val())).toFixed(6)
			}
			calculateData(per,null,null)
		}
		if(type == "branch"){
			if(extractNumber(e,3,false,event)){
				var branch = $(e).val()
				per = (parseFloat(branch*$('#ProBox1').find('input[id="perBranch"]').val())).toFixed(6)
			}
			calculateData(per,null,branch)
		}
		
	}
	//根据平方数计算
	function calculateData(per,box,branch){
		$('#ProBox1').find('input[id="productPerNum"]').val(per)
		$('#ProBox1').find('input[id="totalPer"]').val(per)
		//如果产品没有箱支转换率
		if(li.find('input[id="branchPerBox"]').val() == null || li.find('input[id="branchPerBox"]').val() == "0" || li.find('input[id="perBranch"]').val() == null || li.find('input[id="perBranch"]').val() == "0"){
			//支数
			$('#ProBox1').find('input[id="productBranchNum"]').val(0)
			$('#ProBox1').find('input[id="branchQuantity"]').val(0)
			//箱数
			$('#ProBox1').find('input[id="productBoxNum"]').val(0)
			$('#ProBox1').find('input[id="boxQuantity"]').val(0)
			//零散支数
			$('#ProBox1').find('span[id="productScatteredNum"]').html(0)
			$('#ProBox1').find('input[id="scatteredQuantity"]').val(0)
			//体积
			$('#ProBox1').find('span[id="volume"]').html(0)
			$('#ProBox1').find('input[id="totalVolume"]').val(0)
			//重量
			if(isNaN($('#ProBox1').find('input[id="weight"]').val())){
				var weight = 0;
			}else{
				var weight = (per*$('#ProBox1').find('input[id="weight"]').val()).toFixed(6)
			}
		}else{
			//总支数
			if(branch == null){
				var branch = (per/$('#ProBox1').find('input[id="perBranch"]').val()).toFixed(6)
			}
			$('#ProBox1').find('input[id="productBranchNum"]').val(branch)
			$('#ProBox1').find('input[id="branchQuantity"]').val(parseFloat(branch).toFixed(6))
			//箱数
			var box = Math.floor(branch/$('#ProBox1').find('input[id="branchPerBox"]').val())
			$('#ProBox1').find('input[id="productBoxNum"]').val(box)
			$('#ProBox1').find('input[id="boxQuantity"]').val(box)
			//零散支数
			var scattered = (branch-box*$('#ProBox1').find('input[id="branchPerBox"]').val()).toFixed(6)
			$('#ProBox1').find('span[id="productScatteredNum"]').html(scattered)
			$('#ProBox1').find('input[id="scatteredQuantity"]').val(scattered)
			//体积
			var volume = (box*$('#ProBox1').find('input[id="volume"]').val()).toFixed(6)
			$('#ProBox1').find('span[id="volume"]').html(volume)
			$('#ProBox1').find('input[id="totalVolume"]').val(volume)
			//重量
			var weight = (per*$('#ProBox1').find('input[id="weight"]').val()).toFixed(6)
		}
		//金额
		var price = (per*$('#ProBox1').find('input[id="memberPrice"]').val()).toFixed(2)
		$('#ProBox1').find('span[id="totalPrice"]').html(price)
		$('#ProBox1').find('input[id="totalPrice"]').val(price)
		//重量
		$('#ProBox1').find('span[id="weight"]').html(weight)
		$('#ProBox1').find('input[id="totalWeight"]').val(weight)
	}
	
	function onShow(e){
		$(e).toggleClass("on")	
	}
</script>
</body>
</html>
