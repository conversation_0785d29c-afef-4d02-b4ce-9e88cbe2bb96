<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <title></title>
    <link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
    <link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>

    <style>
        .oPro-list .item{padding:20px 10px; border-bottom: solid 1px #f7f7f7;}
        .oPro-list .item .data{overflow:hidden;background:#f7f7f7;padding: 5px 8px;margin-top: 23px;font-size: 0.75rem;line-height: 1.8;border-radius: 2px; display:none}
        .oPro-list .item .data span{ width:100%; float:left;color:#888;display:block;line-height: 300%}
        .oPro-list .item.on .data{display:block}
        .oPro-list .item div {margin-top: -10px;}
        .order-tab .title .btn {margin-left: 5px;}
    </style>

</head>
<body>
<!--
<div class="header">
	<a href="/mobile/index.jhtml" class="go-back"></a>
	<div class="h-txt">充值申请</div>
</div>
 -->
<div class="containt">
    <form id="planApplyForm">
        <div class="order-tab">
            <div class="title">基本信息</div>
            <input type="hidden" id="storeId" name="storeId" value="${store.id}"/>
            <input type="hidden" id="saleOrgId" name="saleOrgId" value="${saleOrg.id}"/>
            <input type="hidden" id="planApplyType" name="planApplyType" value="${planApplyType}"/>
            <input type="hidden" id="businessTypeName" name="businessTypeName" value="${businessTypeName}"/>
            <div class="dl-style">
                <dl>
                    <dt class="f-black">客户<em class="f-red">*</em></dt>
                    <dd data-id="CustomerBox"  onclick="showPup(this)"><input type="text" class="txt arrow" disabled placeholder="请选择" id="storeName" name="storeName" value="${store.name}"/></dd>
                </dl>
                <dl>
                    <dt class="f-black">要货月份</dt>
                    <dd><input id="month" name="needDate" class="txt" value="${needDate}" [#--onfocus="WdatePicker({dateFmt: 'yyyy-MM',startDate:'%y-%M'});"--]  btn-fun="clear"/></dd>
                </dl>
                <dl>
                    <dt class="f-black">要货总数量</dt>
                    <dd>
                        <span class="red" id="totalQ">0</span>
                        <input type="hidden" id="totalQuantity" name="totalQuantity" />
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">机构<em class="f-red">*</em></dt>
                    <dd>
                    	<input type="text" class="txt" disabled value="${saleOrg.name}" id="saleOrgName" name="saleOrgName"/>
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">创建人</dt>
                    <dd></dd>
                </dl>

                <dl>
                    <dt class="f-black">创建日期</dt>
                    <dd><input type="date" class="txt date" id="glDate" name="glDate" btn-fun="clear" value="${.now}" readonly/></dd>
                </dl>
                <dl>
                    <dt class="f-black">备注</dt>
                    <dd>
                        <textarea class="txt" id="memo" name="memo" placeholder="请输入" ></textarea>
                    </dd>
                </dl>
            </div>
        </div>

        <div class="order-tab mt8">
            <div class="title">要货明细<a href="javascript:void(0);" class="lbtn-orgn btn" data-id="ProBox"  onclick="showPup(this)"><i class="icon ico-add"></i>选择产品</a></div>
            <div class="oPro-list">

            </div>
        </div>

        <div class="h50 mt6"></div>

        <div class="order-btns" style="text-align: center;">
            <!-- <input type="button" class="order-submit" value="保存" onclick="saveForm()" /> -->
            <input type="button" class="order-submit btn-blue" style="width:88%" value="保存" onclick="saveForm()" >
        </div>
    </form>
</div>
<div class="pup-obox" id="CustomerBox">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
            <div class="h-txt">请选择客户</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="客户名称" id="searchByCustomerName"/>
            <input type="button" class="btn" value="搜索" onclick="searchCustomer()">
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;">

    </ul>
</div>
<div class="pup-obox" id="SaleOrgBox">
    <div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
        <div class="pup-header">
            <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
            <div class="h-txt">请选择</div>
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>


<div class="pup-obox" id="ProBox" >
    <div class="pro-2">
        <div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
            <div class="pup-header">
                <a href="javascript:$('.pup-obox').hide();pageNumber = 2;" class="go-back js-cancle"></a>
                <div class="h-txt">请选择商品</div>
            </div>
            <div class="search-box">
                <input type="search" class="txt" placeholder="名称" id="searchByProductName"/>
                <input type="submit" class="btn" value="搜索" onclick="searchPro()">
            </div>
        </div>
        <ul class="pro-list" style="position:relative;padding-top: 100px;">
        </ul>
        <div class="search-box" align="center">
            <input type="submit" class="btn" value="加载更多产品" onclick="loadGoods()">
        </div>

    </div>
</div>

<div class="pup-obox" id="ProBox1">
    <div class="header">
        <a href="javascript:void(0);" class="go-back"  data-id="ProBox"  onclick="showPup(this)"></a>
        <div class="h-txt">查询商品</div>
    </div>
    <div class="dl-style">
        <input type="hidden" id="productName" value=""/>
        <input type="hidden" id="productDesc" value=""/>
        <input type="hidden" id="productId" value=""/>
        <input type="hidden" id="vonderCode" value=""/>
        <input type="hidden" id="spec" value=""/>
        <input type="hidden" id="model" value=""/>

        <input type="hidden" id="quantity" value=""/>
        <input type="hidden" id="paper" value=""/>
        <input type="hidden" id="paperNum" value=""/>
        <input type="hidden" id="needDate" value=""/>

        <input type='hidden' id='sbuId' value=""/>
       
        <input type="hidden" id="productCategoryId" value=""/>
        <input type="hidden" id="unit" value=""/><!-- 产品单位 -->
        <input type="hidden" id="productGrade" class="pg2" value=""/><!-- 产品等级 -->
        <input type="hidden" id="boxQuantity" value=""/><!-- 箱数 -->
        <input type="hidden" id="branchQuantity" value=""/><!-- 支数 -->
        <input type="hidden" id="scatteredQuantity" value=""/><!-- 零散支数 -->
        <input type="hidden" id="branchPerBox" value=""/><!-- 每箱支数 -->
        <input type="hidden" id="perBox" value=""/><!-- 每箱平方数 -->
        <input type="hidden" id="perBranch" value=""/><!-- 每支平方数 -->
        <input type="hidden" id="length" value=""/><!-- 宽 -->
        <input type="hidden" id="width" value=""/><!--  高 -->
        <input type="hidden" id="totalPer" value=""/><!-- 总平方数 -->
        <input type="hidden" id="price" value=""/>
        <input type="hidden" id="saleOrgPrice" value=""/>
        <input type="hidden" id="memberPrice" value=""/><!-- 会员价，每平方单价 -->
        <input type="hidden" id="volume" value=""/><!-- 每箱体积 -->
        <input type="hidden" id="weight" value=""/><!-- 每平方数重量 -->
        <input type="hidden" id="totalVolume" value=""/><!-- 总体积 -->
        <input type="hidden" id="totalWeight" value=""/><!-- 总重量 -->
        <input type="hidden" id="totalPrice" value=""/><!-- 金额-->
        <input type="hidden" id="priceApplyItemId" value=""/><!-- 特价单ID-->
        <input type="hidden" id="priceApplyItemType" value=""/><!-- 特价单类型-->
        <dl>
            <dt>商品名称</dt>
            <dd><span id="productName"></span></dd>
        </dl>
        <dl>
            <dt>商品描述</dt>
            <dd><span id="productDesc" style="overflow: auto;    white-space: nowrap;    display: block;"></span></dd>
        </dl>
        <dl>
            <dt>商品编码</dt>
            <dd><span id="vonderCode"></span></dd>
        </dl>
		<dl>
            <dt>含水率</dt>
            <dd>
                <select class="txt arrow" id="moistureContent">
                    [#list moistureContentList as moistureContent]
                        <option value="${moistureContent.id}">${moistureContent.value}</option>
                    [/#list]
                </select>
            </dd>
        </dl>
        <dl>
            <dt>业务类型</dt>
            <dd>
                <select class="txt arrow" id="businessType">
                    [#list businessTypeList as businessType]
                        <option value="${businessType.id}">${businessType.value}</option>
                    [/#list]
                </select>
            </dd>
        </dl>
        <dl>
            <dt>sbu</dt>
            <dd>
                <select class="txt arrow" id="Sbu">
                    <option value=""> </option>
                </select>
            </dd>
        </dl>
        <dl>
            <dt>要货平方数</dt>[#--quantity--]
            <dd>
                <div class="priceDiv nums-input ov">
                    <input type="button" value="-" class="b decrease" onmousedown="decrease('quantity',event)"/>
                    <input id="itemsQuantity"  value="0" type="text" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" mindata="0"/>
                    <input type="button" value="+" class="b increase" onmousedown="increase('quantity',event)"/>
                </div>
            </dd>
        </dl>
        <dl>
            <dt>要货日期</dt>[#--needDate--]
            <dd>
                <input id="itemsNeedDate" name="" type="date" class="txt date" value="${lineDefaultdate}" [#--onfocus="WdatePicker({dateFmt: 'yyyy-MM',startDate:'%y-%M'});"--]  btn-fun="clear"/>
            </dd>
        </dl>
    </div>
     [#--下面div不能删--]
    <div class="h50 mt6"></div>
    <div class="h50 mt6"></div>
    <div class="order-btns">
        <input type="button" class="order-submit btn-blue" style="width:88%" data-id="ProBox1" onclick='selectItem(this)' value="保存">
    </div>
</div>
<script src="/resources/js/swiper.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>

<script type="text/javascript">
    $(document).ready(function(){

        $(".js-cancle").click(function(){
            $(".pup-obox").hide()
            $("body").attr("style","overflow:auto")
        })
        
    })

    // 根据客户姓名搜索客户
    function searchCustomer() {
        var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
        showPup(e);
    }
    // 根据账号搜索客户收款账号
    function searchBybankCardNo() {
        var e = $('.containt').find('dd[data-id="SaleOrgBox"]')[0]
        showPup(e);
    }
    function showPup(e){
        var id = $(e).attr("data-id")
        $(".pup-obox").hide()
        pageNumber = 2;
        $("#"+id).show()
        if(id == "CustomerBox"){//选择客户
            $('#'+id).find('ul').empty()
            $.ajax({
                type:'POST',
                url:'/member/store/select_store_data.jhtml',
                data:{name:$('#searchByCustomerName').val()},
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = JSON.parse(data.content).content;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var html = "<li data-id='CustomerBox' onclick='selectItem(this)'><a href='#'>"
                                +"<div class='name'>"+row.name+"</div>"
                                +"<div class='fl'><span>机构：</span>"+row.sale_org_name+"</div>"
                                +"<div class='fr'><span>编码：</span>"+row.out_trade_no+"</div>"
                                +"</a>"
                                +"<input type='hidden' id='storeName' value='"+row.name+"'/>"
                                +"<input type='hidden' id='storeId' value='"+row.id+"'/>"
                                +"<input type='hidden' id='saleOrgId' value='"+row.sale_org_id+"'/>"
                                +"<input type='hidden' id='saleOrgName' value='"+row.sale_org_name+"'/>"
                                +"<input type='hidden' id='businessTypeName' value='"+row.business_type_value+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html);
                            $(".oPro-list").html("");
                            editTotal();
                        }
                    }
                }
            })
        }
        if(id == "SaleOrgBox"){//查询机构
            $('#'+id).find('ul').empty()
            $.ajax({
                type:'POST',
                url:'/basic/saleOrg/getChildren.jhtml',
                data:{id:364},
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = data.objx;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var html = "<li data-id='SaleOrgBox' onclick='selectItem(this)'><a href='#'>"
                                +"<div class='fl'><span>机构名称：</span>"+row.name+"</div>"
                                +"</a>"
                                +"<input type='hidden' id='saleOrgId' value='"+row.id+"'/>"
                                +"<input type='hidden' id='saleOrgName' value='"+row.name+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html)
                        }
                    }
                }
            })
        }

        //展示商品列表信息
        if(id == "ProBox"){
            if($('#storeId').val() == null || $('#storeId').val() == ""){
                alert("请选择客户")
                $(".pup-obox").hide()
                $("#"+id).hide()
                return false
            }

            $('#'+id).find('ul').empty()
            pageNumber = 2;
            var name = $('#searchByProductName').val();
            var isStr = 1;
            if(/.*[\u4e00-\u9fa5]+.*$/.test(name)){
                isStr = 0;
            }
            $.ajax({
                type:'POST',
                url:'/product/product/selectProductList.jhtml',
                data:{
                    name:$('#searchByProductName').val(),
                    pageSize:pageSize,
                    pageNumber:1,
                    isStr: isStr,//查询方式
                },
                success:function(data) {
                    if(data.type == 'success'){
                        fillProBoxData(data)
                    }
                }
            })
        }

        //选择商品
        if(id == "ProBox1"){
            li = $(e).parent('div').parent('li')
            if($(e).html() != ""){
                $('#'+id).find('span[id="productName"]').html(li.find('input[id="productName"]').val())
                $('#'+id).find('span[id="productDesc"]').html(li.find('input[id="productDesc"]').val())
                $('#'+id).find('span[id="model"]').html(li.find('input[id="model"]').val())
                $('#'+id).find('span[id="vonderCode"]').html(li.find('input[id="vonderCode"]').val())
                $('#'+id).find('span[id="spec"]').html(li.find('input[id="spec"]').val())
                $('#'+id).find('span[id="model"]').html(li.find('input[id="model"]').val())

                $('#'+id).find('span[id="quantity"]').html(li.find('input[id="quantity"]').val() == null)
                $('#'+id).find('span[id="paper"]').html(li.find('input[id="paper"]').val())

                $('#'+id).find('span[id="paperNum"]').html(li.find('input[id="paperNum"]').val())
                $('#'+id).find('span[id="needDate"]').html(li.find('input[id="needDate"]').val())
                //sbu
                var sbuId = li.find('input[id="sbuId"]').val();
                if(!isEmpty(sbuId)){
                	var sbus = JSON.parse(sbuId)
                	if(!isEmpty(sbus) && sbus.length>0){
    	 				var html = '';
    	 				for(var i = 0;i < sbus.length;i++){
    						var sbu = sbus[i];
    						html+='<option value="'+sbu['id']+'">'+sbu['name']+'</option> ';
    					};
    					$('#'+id).find('select[id="Sbu"]').empty().append(html);
    	 			}
                }
                
                //业务类型
                var businessTypeName = $("#planApplyForm").find('input[id="businessTypeName"]').val();
                var html = '';
                [#list businessTypeList as businessType]
	   				if('${businessType.value}' == businessTypeName){
	   					html+='<option value="${businessType.id}" selected="selected" >${businessType.value}</option> ';
	   				}else{
	   					html+='<option value="${businessType.id}">${businessType.value}</option> ';
	   				}
				[/#list]
				 $('#'+id).find('select[id="businessType"]').empty().append(html);
               
                $('#'+id).find('span[id="unit"]').html(li.find('input[id="unit"]').val())
                $('#'+id).find('span[id="productCategoryName"]').html(li.find('input[id="productCategoryName"]').val())
                $('#'+id).find('span[id="memberPrice"]').html(li.find('input[id="memberPrice"]').val())



                if(li.find('input[id="unit"]').val()=="m2" && $('#sbuId').val()==3){
                    $('#'+id).find('input[id="length"]').show()							//显示输入框
                    $('#'+id).find('input[id="length"]').prev().show()					//显示数量－按钮
                    $('#'+id).find('input[id="length"]').next().show()					//显示数量+按钮


                    $('#'+id).find('input[id="width"]').show()							//显示输入框
                    $('#'+id).find('input[id="width"]').prev().show()					//显示数量－按钮
                    $('#'+id).find('input[id="width"]').next().show()					//显示数量+按钮
                    //隐藏内容为'-'的span

                    $('#'+id).find('input[id="productPerNum"]').attr("readOnly","true")
                    $('#'+id).find('input[id="productPerNum"]').next().hide()
                    $('#'+id).find('input[id="productPerNum"]').prev().hide()

                    $('#'+id).find('input[id="length"]').val(parseFloat(li.find('input[id="length"]').val()))
                    $('#'+id).find('input[id="width"]').val(parseFloat(li.find('input[id="width"]').val()))
                }else{
                    $('#'+id).find('input[id="length"]').prev().hide()					//隐藏数量－按钮
                    $('#'+id).find('input[id="length"]').next().hide()					//隐藏数量+按钮
                    $('#'+id).find('input[id="length"]').parent().find("span").show()	//显示内容为'-'的span
                    $('#'+id).find('input[id="length"]').hide()							//隐藏输入框


                    $('#'+id).find('input[id="width"]').prev().hide()					//隐藏数量－按钮
                    $('#'+id).find('input[id="width"]').next().hide()					//隐藏数量+按钮
                    $('#'+id).find('input[id="width"]').parent().find("span").show()	//显示内容为'-'的span
                    $('#'+id).find('input[id="width"]').hide()							//隐藏输入框

                }
                $('#'+id).find('input[id="priceApplyItemSn"]').val("")
                $('#'+id).find('input[id="priceApplyItemId"]').val("")
                $('#'+id).find('input[id="deliveryTime"]').val("")

                var productGrade = li.find('input[id="productGrade"]').val()/* 产品等级 */


                //添加隐藏信息---
                $('#'+id).find('input[id="productName"]').val(li.find('input[id="productName"]').val())
                $('#'+id).find('input[id="productDesc"]').val(li.find('input[id="productDesc"]').val())
                $('#'+id).find('input[id="model"]').val(li.find('input[id="model"]').val())
                $('#'+id).find('input[id="productId"]').val(li.find('input[id="productId"]').val())
                $('#'+id).find('input[id="productCategoryId"]').val(li.find('input[id="productCategoryId"]').val())
                $('#'+id).find('input[id="vonderCode"]').val(li.find('input[id="vonderCode"]').val())
                $('#'+id).find('input[id="spec"]').val(li.find('input[id="spec"]').val())
                $('#'+id).find('input[id="model"]').val(li.find('input[id="model"]').val())
                $('#'+id).find('input[id="paperNum"]').val(li.find('input[id="paperNum"]').val())//纸张转换率
                $('#'+id).find('input[id="unit"]').val(li.find('input[id="unit"]').val())
                $('#'+id).find('input[id="productGrade"]').val(li.find('input[id="productGrade"]').val())
                $('#'+id).find('input[class="pg1"]').val(li.find('input[id="productGrade"]').val())
                //------------
                $('#'+id).find('input[id="sbuId"]').val(li.find('input[id="sbuId"]').val())
                $('#'+id).find('input[id="sbuName"]').val(li.find('input[id="sbuName"]').val())


                //如果产品没有箱支转换率，下单箱数默认为0
                if(li.find('input[id="branchPerBox"]').val() == null || li.find('input[id="branchPerBox"]').val() == "0" || li.find('input[id="perBranch"]').val() == null || li.find('input[id="perBranch"]').val() == "0"){
                    $('#'+id).find('input[id="boxQuantity"]').val(0)//箱数
                }else{
                    $('#'+id).find('input[id="boxQuantity"]').val(1)//箱数
                }
                $('#'+id).find('input[id="branchQuantity"]').val(li.find('input[id="branchPerBox"]').val())//支数
                $('#'+id).find('input[id="scatteredQuantity"]').val(0)//零散支数
                $('#'+id).find('input[id="branchPerBox"]').val(li.find('input[id="branchPerBox"]').val())//每箱支数
                $('#'+id).find('input[id="perBox"]').val(li.find('input[id="perBox"]').val())//每箱平方数
                $('#'+id).find('input[id="perBranch"]').val(li.find('input[id="perBranch"]').val())//每支平方数
                $('#'+id).find('input[id="totalPer"]').val(parseFloat(li.find('input[id="perBox"]').val()).toFixed(6))//总平方数
                $('#'+id).find('input[id="price"]').val(li.find('input[id="memberPrice"]').val())//保存原价
                $('#'+id).find('input[id="saleOrgPrice"]').val(li.find('input[id="saleOrgPrice"]').val())
                $('#'+id).find('input[id="memberPrice"]').val(li.find('input[id="memberPrice"]').val())//会员价，每平方单价
                $('#'+id).find('input[id="volume"]').val(parseFloat(li.find('input[id="volume"]').val()).toFixed(6))
                $('#'+id).find('input[id="weight"]').val(parseFloat(li.find('input[id="weight"]').val()).toFixed(6))
                $('#'+id).find('input[id="totalVolume"]').val(parseFloat(li.find('input[id="volume"]').val()).toFixed(6))
                $('#'+id).find('input[id="totalWeight"]').val((parseFloat(li.find('input[id="weight"]').val())*parseFloat(li.find('input[id="perBox"]').val())).toFixed(6))
                $('#'+id).find('input[id="totalPrice"]').val((li.find('input[id="perBox"]').val()*li.find('input[id="memberPrice"]').val()).toFixed(2))


            }

        }
        if(id == "AddressBox"){//更换收货地址
            if($('#storeId').val() == null || $('#storeId').val() == ""){
                alert("请选择客户")
                $(".pup-obox").hide()
                $("#"+id).hide()
                return false
            }
            $('#'+id).find('ul').empty()
            $.ajax({
                type:'POST',
                url:'/member/store/select_store_address_data.jhtml',
                data:{storeId:$('#storeId').val(),mobile:$("#searchByMobileName").val()},
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = JSON.parse(data.content).content;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var html = "<li data-id='AddressBox' onclick='selectItem(this)'><a href='#'>"
                                +"<div class='fl'>"+row.consignee+"</div>"
                                +"<div class='fr'>"+row.mobile+"</div>"
                                +"<div class='clear'>"
                                +"<span>"+row.address+"</span>"
                                +"</div>"
                                +"</a>"
                                +"<input type='hidden' id='receiveInfoName' value='"+row.consignee+"'/>"
                                +"<input type='hidden' id='receiveInfoPhone' value='"+row.mobile+"'/>"
                                +"<input type='hidden' id='receiveInfoAddress' value='"+row.address+"'/>"
                                +"<input type='hidden' id='receiveInfoArea' value='"+row.area_full_name+"'/>"
                                +"<input type='hidden' id='areaId' value='"+row.area+"'/>"
                                +"<input type='hidden' id='out_trade_no' value='"+row.out_trade_no+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html)
                        }
                    }
                }
            })
        }
    }

    function dealEmpty(a){
        if(isEmpty(a)){
            return "";
        }else{
            return a;
        }
    }

    function selectItem(e){
        var id = $(e).attr("data-id");
        if(id == "CustomerBox"){//选择客户
            $('.containt').find('input[id="storeName"]').val($(e).find('input[id="storeName"]').val());
            $('.containt').find('input[id="storeId"]').val($(e).find('input[id="storeId"]').val());
            $('.containt').find('input[id="saleOrgId"]').val($(e).find('input[id="saleOrgId"]').val());
            $('.containt').find('input[id="saleOrgName"]').val($(e).find('input[id="saleOrgName"]').val());
            $('.containt').find('input[id="businessTypeName"]').val($(e).find('input[id="businessTypeName"]').val());
        }
        if(id == "AccountBox"){//选择账号
            $('.containt').find('input[id="bankCardId"]').val($(e).find('input[id="bankCardId"]').val());
            $('.containt').find('input[id="bankCardNo"]').val($(e).find('input[id="bankCardNo"]').val());
            $('.containt').find('input[id="bankName"]').val($(e).find('input[id="bankName"]').val());
            $('.containt').find('input[id="organizationId"]').val($(e).find('input[id="organizationId"]').val());
            $('.containt').find('input[id="organizationName"]').val($(e).find('input[id="organizationName"]').val());
        }
        if(id == "SaleOrgBox"){
            $('.containt').find('input[id="saleOrgName"]').val($(e).find('input[id="saleOrgName"]').val());
            $('.containt').find('input[id="saleOrgId"]').val($(e).find('input[id="saleOrgId"]').val());

        }
        if(id == "ProBox1"){//保存选择的产品
          
            var productName = $('#'+id).find('input[id="productName"]').val();
            var productId = $('#'+id).find('input[id="productId"]').val();
            var vonderCode = $('#'+id).find('input[id="vonderCode"]').val();
            var spec = $('#'+id).find('input[id="spec"]').val();
            var model = $('#'+id).find('input[id="model"]').val();
            var productDesc = $('#'+id).find('input[id="productDesc"]').val();
            //--------------
            
            var moistureContentId = $('#'+id).find('select[id="moistureContent"]').find("option:selected").val()
            var moistureContentName = $('#'+id).find('select[id="moistureContent"]').find("option:selected").text();
            var sbuId = $('#'+id).find('select[id="Sbu"]').find("option:selected").val()
            var sbuName = $('#'+id).find('select[id="Sbu"]').find("option:selected").text();
            var businessTypeId = $('#'+id).find('select[id="businessType"]').find("option:selected").val();
            var businessTypeValue = $('#'+id).find('select[id="businessType"]').find("option:selected").text();

            var quantity = $("#itemsQuantity").val();
            if(isEmpty(quantity) || quantity == 0){
            	alert("要货平方数必须大于0");
            	return false;
            }
            
            var paper = $("#itemsPaper").val();
           
            var needDate = $("#itemsNeedDate").val();

            var unit = $('#'+id).find('input[id="unit"]').val();
            var productGrade = $('#'+id).find('input[id="productGrade"]').val();
            var boxQuantity = $('#'+id).find('input[id="boxQuantity"]').val();//箱数
            var branchQuantity = $('#'+id).find('input[id="branchQuantity"]').val();//支数
            var scatteredQuantity = $('#'+id).find('input[id="scatteredQuantity"]').val();//零散支数
            var branchPerBox = $('#'+id).find('input[id="branchPerBox"]').val();//每箱支数
            var perBox = $('#'+id).find('input[id="perBox"]').val();// 每箱平方数
            var perBranch = $('#'+id).find('input[id="perBranch"]').val();//每支平方数
            var length = $('#'+id).find('input[id="length"]').val();//宽
            var width = $('#'+id).find('input[id="width"]').val();//高
            var totalPer = $('#'+id).find('input[id="totalPer"]').val();//总平方数
            var price = $('#'+id).find('input[id="price"]').val();

            var saleOrgPrice = $('#'+id).find('input[id="saleOrgPrice"]').val();

            if(saleOrgPrice == 'undefined'){
                saleOrgPrice = 0;
            }
            var memberPrice = $('#'+id).find('input[id="memberPrice"]').val();//每平方单价
            var volume = $('#'+id).find('input[id="volume"]').val();//每箱体积
            var totalVolume = $('#'+id).find('input[id="totalVolume"]').val();//总体积
            var weight = $('#'+id).find('input[id="weight"]').val();//每平方数重量
            var totalWeight = $('#'+id).find('input[id="totalWeight"]').val();//总重量
            var totalPrice = $('#'+id).find('input[id="totalPrice"]').val();//该产品总金额
            var priceApplyItemId = $('#'+id).find('input[id="priceApplyItemId"]').val();//特价单ID
            var deliveryTime = $('#'+id).find('input[id="deliveryTime"]').val()//交货期
            var sellerMemo = $('#'+id).find('textarea[id="sellerMemo"]').val()//备注
            var hiddenAmount=1;
            if(hiddenAmount==0){
                totalPrice="***";
            }
     


            //显示明细
            var html = "<div class='item' onclick='onShow(this)' id='itemLists'>"
                +"<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                +"<div class=''>"+productName+"</div>"
                +"<div class='data'>"
                +"		<span><div style='float:left'>产品编码：</div><div style='float:right'>"+vonderCode+"</div></span>"
                +"		<span><div style='float:left'>产品描述：</div><div style='float:right'>"+productDesc+"</div></span>"
                +"		<span><div style='float:left'>含水率：</div><div style='float:right'>"+moistureContentName+"</div></span>"
                +"		<span><div style='float:left'>sbu：</div><div style='float:right'>"+sbuName+"</div></span>"
                +"		<span><div style='float:left'>业务类型：</div><div style='float:right'>"+businessTypeValue+"</div></span>"
                +"		<span><div style='float:left'>要货平方数：</div><div style='float:right'>"+quantity+"</div></span>"
                +"		<span><div style='float:left'>要货日期：</div><div style='float:right'>"+needDate+"</div></span>"
                +"</div>"





            html += ""
                +"<input type='hidden' id='productName' value='"+productName+"'/>"
                +"<input type='hidden' id='productId' value='"+productId+"'/>"
                +"<input type='hidden' id='vonderCode' value='"+vonderCode+"'/>"
                +"<input type='hidden' id='spec' value='"+spec+"'/>"
                +"<input type='hidden' id='model' value='"+model+"'/>"
                +"<input type='hidden' class='quantity' id='quantity' value='"+quantity+"'/>"
                +"<input type='hidden' id='paper' value='"+paper+"'/>"
                +"<input type='hidden' id='needDate' value='"+needDate+"'/>"

                //----------------------------------------
                //----------------------------------------
                +"<input type='hidden' id='moistureContentId' value='"+moistureContentId+"'/>"
                +"<input type='hidden' id='moistureContentName' value='"+moistureContentName+"'/>"
                +"<input type='hidden' id='sbuId' value='"+sbuId+"'/>"
                +"<input type='hidden' id='sbuName' value='"+sbuName+"'/>"
                +"<input type='hidden' id='businessTypeId' value='"+businessTypeId+"'/>"
                +"<input type='hidden' id='businessTypeValue' value='"+businessTypeValue+"'/>"


                +"<input type='hidden' id='productGrade' value='"+productGrade+"'/>"
                +"<input type='hidden' id='boxQuantity' value='"+boxQuantity+"'/>"
                +"<input type='hidden' id='branchQuantity' value='"+branchQuantity+"'/>"
                +"<input type='hidden' id='scatteredQuantity' value='"+scatteredQuantity+"'/>"
                +"<input type='hidden' id='branchPerBox' value='"+branchPerBox+"'/>"
                +"<input type='hidden' id='perBox' value='"+perBox+"'/>"
                +"<input type='hidden' id='perBranch' value='"+perBranch+"'/>"
                +"<input type='hidden' id='length' value='"+length+"'/>"
                +"<input type='hidden' id='width' value='"+width+"'/>"
                +"<input type='hidden' id='totalPer' value='"+totalPer+"'/>"
                +"<input type='hidden' id='price' value='"+price+"'/>"
                +"<input type='hidden' id='saleOrgPrice' value='"+saleOrgPrice+"'/>"
                +"<input type='hidden' id='memberPrice' value='"+memberPrice+"'/>"
                +"<input type='hidden' id='volume' value='"+volume+"'/>"
                +"<input type='hidden' id='totalVolume' value='"+totalVolume+"'/>"
                +"<input type='hidden' id='weight' value='"+weight+"'/>"
                +"<input type='hidden' id='totalWeight' value='"+totalWeight+"'/>"
                +"<input type='hidden' id='totalPrice' value='"+totalPrice+"'/>"
                +"<input type='hidden' id='priceApplyItemId' value='"+priceApplyItemId+"'/>"
                +"<input type='hidden' id='deliveryTime' value='"+deliveryTime+"'/>"
                +"<input type='hidden' id='sellerMemo' value='"+sellerMemo+"'/>"
                +"</div>";

            $('.oPro-list').append(html);
            //初始化
            $("#itemsQuantity").val(0);
            $("#itemsPaper").val(0);
            //修改要货总数量
            editTotal();
        }
        $(".pup-obox").hide();
        $("#"+id).hide();
    }
    //保存
    function saveForm(){
        var flag = confirm("您确定要保存吗？");
        if (flag != true) {
            return ;
        }
        var storeId = $("#planApplyForm").find('input[id="storeId"]').val();
        if(isEmpty(storeId)){
            alert("请选择客户")
            return false
        }
        var jsonData = {
        	'planApplyType':$("#planApplyType").val(),	
            'storeId':storeId,
            'saleOrgId':$("#saleOrgId").val(),
            'needDate':$("#month").val(),
            'totalQuantity':$('#totalQuantity').val(),
            'memo':$("#memo").val(),
        }
      
        var planApplyItems = $('.oPro-list').find('div[class="item"]');
        
        for (var i = 0; i < planApplyItems.length; i++) {
        	jsonData['planApplyItems['+i+'].type'] = $("#planApplyForm").find('input[id="planApplyType"]').val(), /* 计划提报类型：0、经销商 7、经销商提报 */
        	jsonData['planApplyItems['+i+'].product.id'] = $(planApplyItems[i]).find('input[id="productId"]').val(),
        	jsonData['planApplyItems['+i+'].moistureContent.id'] = $(planApplyItems[i]).find('input[id="moistureContentId"]').val(),
        	jsonData['planApplyItems['+i+'].sbu.id'] = $(planApplyItems[i]).find('input[id="sbuId"]').val(),
        	jsonData['planApplyItems['+i+'].businessType.id'] = $(planApplyItems[i]).find('input[id="businessTypeId"]').val(),
            jsonData['planApplyItems['+i+'].quantity'] = $(planApplyItems[i]).find('input[id="quantity"]').val(),
            jsonData['planApplyItems['+i+'].needDate'] = $(planApplyItems[i]).find('input[id="needDate"]').val()
        }
        
        $.ajax({
            type:'POST',
            url:'/b2b/planApply/saveOrUpdate.jhtml',
            data:jsonData,
            success:function(data) {
            	if(data.type == 'success'){
            		alert(data.content);
            		window.location.href = "/mobile/b2b/planApplyNotToErp/form_detail.jhtml?id="+ data.objx;
            	}else{
            		alert(data.content);
            	}
            }
        })
    }


    // 搜索商品
    function searchPro() {
        var e = $('.containt').find('a[data-id="ProBox"]')[0]
        pageNumber = 2;
        showPup(e);
    }

    //删除
    function del(e){
        $(e).parent('div').remove();
        editTotal();
    }

    function calculateDataNew(per,length,width){

        $('#ProBox1').find('input[id="productPerNum"]').val(per)
        $('#ProBox1').find('input[id="totalPer"]').val(per)

        if(isNaN($('#ProBox1').find('input[id="weight"]').val())){
            var weight = 0;
        }else{
            var weight = (per*$('#ProBox1').find('input[id="weight"]').val()).toFixed(6)
        }
        var price = (per*$('#ProBox1').find('input[id="memberPrice"]').val()).toFixed(2)
        $('#ProBox1').find('span[id="totalPrice"]').html(price)
        $('#ProBox1').find('input[id="totalPrice"]').val(price)
        //重量
        $('#ProBox1').find('span[id="weight"]').html(weight)
        $('#ProBox1').find('input[id="totalWeight"]').val(weight)
        if(length!=null){
            $('#ProBox1').find('input[id="length"]').val(length)

        }else if(width!=null){
            $('#ProBox1').find('input[id="width"]').val(width)

        }




    }


    //增加
    function increase(type,event){
        var per = 0
        if(type == "per"){
            //增加平方数
            per = (parseFloat($('#ProBox1').find('input[id="productPerNum"]').val())+parseInt(1)).toFixed(6)
        }
        if(type == "box"){
            //增加箱数
            var box = parseInt($('#ProBox1').find('input[id="productBoxNum"]').val())+parseInt(1)
            per = (parseFloat(box*$('#ProBox1').find('input[id="perBox"]').val())).toFixed(6)
        }
        if(type == "branch"){
            //增加支数
            var branch = parseInt($('#ProBox1').find('input[id="productBranchNum"]').val())+parseInt(1)
            per = (parseFloat(branch*$('#ProBox1').find('input[id="perBranch"]').val())).toFixed(6)
        }

        if(type == "quantity"){
            //增加要货平方数
            per = (parseFloat($("#itemsQuantity").val())+parseInt(1)).toFixed(6)
            $("#itemsQuantity").val(per);

            editPageNum();
        }

        if(type == "paper"){
            //增加要货纸张数
            per =(parseFloat($("#itemsPaper").val())+parseInt(1)).toFixed(2);
            $("#itemsPaper").val(per)
            editLinePaper()
        }


        calculateData(per,null,null)
    }


    //减少
    function decrease(type,event){
        var per = 0

        if(type == "paper"){
            if(parseFloat($('#itemsPaper').val()) < 1 || parseFloat($('#itemsPaper').val()) == 1){
                per = 0;
            }else {
                per = (parseFloat($('#itemsPaper').val())-parseInt(1)).toFixed(2)
            }

            $("#itemsPaper").val(per)
            editLinePaper()
        }

        if(type == "quantity"){
            //减少要货平方数
            if(parseFloat($('#itemsQuantity').val()) < 1 || parseFloat($('#itemsQuantity').val()) == 1){
                per = 0

            }else{
                per = (parseFloat($('#itemsQuantity').val())-parseInt(1)).toFixed(6)
            }

            editPageNum();

            $("#itemsQuantity").val(per);
        }

        if(type == "per"){
            //减少平方数
            if(parseFloat($('#ProBox1').find('input[id="productPerNum"]').val()) < 1 || parseFloat($('#ProBox1').find('input[id="productPerNum"]').val()) == 1){
                per = 0
            }else{
                per = (parseFloat($('#ProBox1').find('input[id="productPerNum"]').val())-parseInt(1)).toFixed(6)
            }
        }
        if(type == "box"){
            //减少箱数
            if(parseInt($('#ProBox1').find('input[id="productBoxNum"]').val()) < 1 || parseInt($('#ProBox1').find('input[id="productBoxNum"]').val()) == 1){
                var box = 0
            }else{
                var box = parseInt($('#ProBox1').find('input[id="productBoxNum"]').val())-parseInt(1)
            }
            per = (parseFloat(box*$('#ProBox1').find('input[id="perBox"]').val())).toFixed(6)
        }
        if(type == "branch"){
            //减少支数
            if(parseInt($('#ProBox1').find('input[id="productBranchNum"]').val()) < 1 || parseInt($('#ProBox1').find('input[id="productBranchNum"]').val()) == 1){
                var branch = 0
            }else{
                var branch = parseInt($('#ProBox1').find('input[id="productBranchNum"]').val())-parseInt(1)
            }
            per = (parseFloat(branch*$('#ProBox1').find('input[id="perBranch"]').val())).toFixed(6)
        }

        calculateData(per,null,null)
    }



    //根据平方数计算
    function calculateData(per,box,branch){
        //$("#itemsQuantity").val(per);

        $('#ProBox1').find('input[id="productPerNum"]').val(per)
        $('#ProBox1').find('input[id="totalPer"]').val(per)




        //如果产品没有箱支转换率
        if(li.find('input[id="branchPerBox"]').val() == null || li.find('input[id="branchPerBox"]').val() == "0" || li.find('input[id="perBranch"]').val() == null || li.find('input[id="perBranch"]').val() == "0"){
            //支数
            $('#ProBox1').find('input[id="productBranchNum"]').val(0)
            $('#ProBox1').find('input[id="branchQuantity"]').val(0)
            //箱数
            $('#ProBox1').find('input[id="productBoxNum"]').val(0)
            $('#ProBox1').find('input[id="boxQuantity"]').val(0)
            //零散支数
            $('#ProBox1').find('span[id="productScatteredNum"]').html(0)
            $('#ProBox1').find('input[id="scatteredQuantity"]').val(0)
            //体积
            $('#ProBox1').find('span[id="volume"]').html(0)
            $('#ProBox1').find('input[id="totalVolume"]').val(0)
            //重量
            if(isNaN($('#ProBox1').find('input[id="weight"]').val())){
                var weight = 0;
            }else{
                var weight = (per*$('#ProBox1').find('input[id="weight"]').val()).toFixed(6)
            }
        }else{
            //总支数
            if(branch == null){
                var branch = (per/$('#ProBox1').find('input[id="perBranch"]').val()).toFixed(6)
            }
            $('#ProBox1').find('input[id="productBranchNum"]').val(branch)
            $('#ProBox1').find('input[id="branchQuantity"]').val(parseFloat(branch).toFixed(6))
            //箱数
            var box = Math.floor(branch/$('#ProBox1').find('input[id="branchPerBox"]').val())
            $('#ProBox1').find('input[id="productBoxNum"]').val(box)
            $('#ProBox1').find('input[id="boxQuantity"]').val(box)
            //零散支数
            var scattered = (branch-box*$('#ProBox1').find('input[id="branchPerBox"]').val()).toFixed(6)
            $('#ProBox1').find('span[id="productScatteredNum"]').html(scattered)
            $('#ProBox1').find('input[id="scatteredQuantity"]').val(scattered)
            //体积
            var volume = (box*$('#ProBox1').find('input[id="volume"]').val()).toFixed(6)
            $('#ProBox1').find('span[id="volume"]').html(volume)
            $('#ProBox1').find('input[id="totalVolume"]').val(volume)
            //重量
            var weight = (per*$('#ProBox1').find('input[id="weight"]').val()).toFixed(6)
        }
        //金额
        var price = (per*$('#ProBox1').find('input[id="memberPrice"]').val()).toFixed(2)
        $('#ProBox1').find('span[id="totalPrice"]').html(price)
        $('#ProBox1').find('input[id="totalPrice"]').val(price)
        //重量
        $('#ProBox1').find('span[id="weight"]').html(weight)
        $('#ProBox1').find('input[id="totalWeight"]').val(weight)
    }


    //实时修改平方数，箱数，支数
    function input(type,e,event){
        var per = 0;

        if(type == "quantity"){
            if(extractNumber(e,6,false,event)){
                per = $(e).val()
            }
            calculateData(per,null,null)
        }


        if(type == "per"){
            if(extractNumber(e,6,false,event)){
                per = $(e).val()
            }
            calculateData(per,null,null)
        }
        if(type == "box"){
            if(extractNumber(e,3,false,event)){
                var box = $(e).val()
                per = (parseFloat(box*$('#ProBox1').find('input[id="perBox"]').val())).toFixed(6)
            }
            calculateData(per,null,null)
        }
        if(type == "branch"){
            if(extractNumber(e,3,false,event)){
                var branch = $(e).val()
                per = (parseFloat(branch*$('#ProBox1').find('input[id="perBranch"]').val())).toFixed(6)
            }
            calculateData(per,null,branch)
        }
        if(type == "length"){
            if(extractNumber(e,6,false,event)){
                var length = $(e).val()
                per = (parseFloat(length*$('#ProBox1').find('input[id="width"]').val())).toFixed(6)
                calculateDataNew(per,length,null)
            }

        }
        if(type == "width"){
            if(extractNumber(e,6,false,event)){
                var width = $(e).val()
                per = (parseFloat(width*$('#ProBox1').find('input[id="length"]').val())).toFixed(6)
                calculateDataNew(per,null,width)
            }

        }

    }

    //根据要货平方数修改要货纸张数
    function editQty(t,e,n) {
        var rate = 6;
        if(n!=undefined)rate = n;
        if(extractNumber(t,rate,true,e)){
            if($(t).hasClass("adjustQuantity")){
                editLineQuantity(t);
            }
            editPageNum();
        }
    }
    

    //修改要货总数量
    function editTotal(){
        var total = 0;
        $("input.quantity").each(function(){
            var $this = $(this);
            var quantity = $this.val();
            total = accAdd(total,quantity);
        });
        $("#totalQ").text(total);
        $("#totalQuantity").val(total);
    }

    function editPageNum(){
        var pageNum = $("#ProBox1").find('input[id="paperNum"]').val();
        var quantity = $("#itemsQuantity").val();
        if(isNaN(pageNum)){
            pageNum = 0;
        }
        if(isNaN(quantity)){
            quantity = 0;
        }
        var paper = accDiv(quantity,pageNum).toFixed(2);
        if(isNaN(paper)){
            paper = 0;
        }
        $("#itemsPaper").val(paper);
    }

    //根据要货纸张数修改要货平方数
    function editPaper(t,e) {
        if(extractNumber(t,2,false,e)){
            editLinePaper(t);
        }
    }

    function editLinePaper(){
        var pageNum = $("#ProBox1").find('input[id="paperNum"]').val();
        var paper = $("#itemsPaper").val();
        if(isNaN(pageNum)){
            pageNum = 0;
        }
        if(isNaN(paper)){
            paper = 0;
        }
        var lineQuantity = accMul(pageNum,paper).toFixed(6);
        if(isNaN(lineQuantity)){
            lineQuantity = 0;
        }
        $("#itemsQuantity").val(lineQuantity);
    }


    function onShow(e){
        $(e).toggleClass("on")
    }


    //填充商品列表
    function fillProBoxData(data) {

        var rows = JSON.parse(data.content).content;

        if(rows.length < 1){
            alert("没有更多商品~")
        }

        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            var member_price = row.member_price;
            var html = "<li><div class='pic productdetail'><img src='"+row.image+"'></div>"
                +"<div class='txt'>"
                +"<p class='name'>"+row.name+"</p>"
                /* +"<p>商品编码："+row.vonder_code+"</p>" */
                +"<p>型号："+row.model+"</p>"
            if(row.spec != null && row.spec != ""){
                html += "<p>规格："+row.spec+"</p>"
            }else{
                html += "<p>规格：</p>"
            }
            if(row.levelName != null && row.levelName != ""){
                html+="	<p>等级："+row.levelName+"</p>"
            }else{
                html+="	<p>等级：</p>"
            }

            html+="<p>分类："+row.product_category_name+"</p>"
                +"</div>"
                +"<div class='btns'>"
                +"<a href='javascript:void(0)' class='lbtn-orgn btn'  data-id='ProDetailBox2'  onclick='showPup(this)'><i class='icon ico-info'></i>商品详情</a>"
                +"<input type='hidden' value="+row.level_Id+">"
                +"<a href='javascript:void(0)' class='btn-orgn btn'  data-id='ProBox1'  onclick='showPup(this)'><i class='icon ico-ok'></i>选择商品</a>"
                +"</div>"
                +"<input type='hidden' id='productName' value='"+row.name+"'/>"
                +"<input type='hidden' id='productId' value='"+row.id+"'/>"
                +"<input type='hidden' id='productCategoryId' value='"+row.product_category_id+"'/>"
                +"<input type='hidden' id='productImage' value='"+row.image+"'/>"
                +"<input type='hidden' id='productGrade' value='"+row.level_Id+"'/>"
                +"<input type='hidden' id='vonderCode' value='"+row.vonder_code+"'/>"
                +"<input type='hidden' id='spec' value='"+row.spec+"'/>"//规格
                +"<input type='hidden' id='model' value='"+row.model+"'/>"//型号
                +"<input type='hidden' id='unit' value='"+row.unit+"'/>"
                +"<input type='hidden' id='productCategoryName' value='"+row.product_category_name+"'/>"
                +"<input type='hidden' id='productModel' value='"+row.model+"'/>"
                +"<input type='hidden' id='perBox' value='"+row.per_box+"'/>"//每箱平方数
             //sbu
             if(!isEmpty(row.sbus)){
            	 html += "<input type='hidden' id='sbuId' value='"+row.sbus+"'/>"
             }else{
            	 html += "<input type='hidden' id='sbuId' value=''/>"
             }   
            //如果产品没有纸张转换率
            if(row.paper_num == null || row.paper_num == "0"){
                html += "<input type='hidden' id='paperNum' value='"+1+"'/>"//纸张转换率(㎡/张)
            }else{
                html += "<input type='hidden' id='paperNum' value='"+row.paper_num+"'/>"//纸张转换率(㎡/张)
            }
            html += "<input type='hidden' id='price' value='"+row.price+"'/>"
                +"<input type='hidden' id='memberPrice' value='"+row.member_price+"'/>"//会员价，每平方单价
                +"<input type='hidden' id='saleOrgPrice' value='"+row.sale_org_price+"'/>"
                +"<input type='hidden' id='volume' value='"+row.volume+"'/>"//体积
                +"<input type='hidden' id='weight' value='"+row.weight+"'/>"//重量
                +"<input type='hidden' id='productDesc' value='"+ dealEmpty(row.description)+"'/>"//产品描述
                +"<input type='hidden' id='model' value='"+row.model+"'/>"//产品模型
                +"</li>";
            $('#ProBox').find('ul').append(html)
        }
    }

    //分页查询商品===
    var pageSize = 20;
    var pageNumber = 2;
    function loadGoods() {
        var name = $('#searchByProductName').val();
        var isStr = 1;
        if(/.*[\u4e00-\u9fa5]+.*$/.test(name)){
            isStr = 0;
        }
        $.ajax({
            type:'POST',
            url:'/product/product/selectProductList.jhtml',
            data:{
                name:name,
                pageSize:pageSize,
                pageNumber:pageNumber,
                sbuId:$('#sbuId').val(),
                isStr: isStr,//查询方式
            },
            success:function(data) {
                if(data.type == 'success'){
                    fillProBoxData(data)
                    pageNumber += 1;
                }
            }
        })
    }
   
</script>

</body>
</html>
