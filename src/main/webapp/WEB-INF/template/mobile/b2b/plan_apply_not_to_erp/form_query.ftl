<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <title></title>
    <link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div class="containt">
    <div class="search-box" style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
        <input type="text" class="txt" id="orderSn" placeholder="请输入单据编号"/>
        <input type="submit" class="btn" onclick="ajaxListData()" value="搜索">
    </div>
    <div class="order-list" style="position:relative;padding-top: 54px;">
        <ul class="list-style02"> </ul>
    </div>
</div>


<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
    //当前页数
    var pageNumber = 1;

    $().ready(function(){
        ajaxListData()
    })
    function ajaxListData(){
        pageNumber = 1;
        $('.order-list').find('ul').empty()
        $.ajax({
            type:'POST',
            url:'/mobile/b2b/planApplyNotToErp/list_data.jhtml',
            data:{sn:$('#orderSn').val(),pageNumber:pageNumber},
            success:function(data) {
                fillData(data);
            }
        })
    }

    //获取当前浏览器中的滚动事件
    $(window).off("scroll").on("scroll", function () {
        //获取当前浏览器的滚动条高度
        var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
        //判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
        if (scrollHeight <= ($(window).scrollTop() + $(window).height())) {
            //设置当前页数
            // pageNumber += 1;
            setTimeout(function () {
                $.ajax({
                    type:'POST',
                    url:'/mobile/b2b/planApplyNotToErp/list_data.jhtml',
                    data:{orderSn:$('#orderSn').val(),pageNumber:pageNumber},
                    success:function(data) {
                        fillData(data);
                    }
                })
            },500);
            //设置当前页数
            pageNumber += 1;
        }
    });

    // 填充数据
    function fillData(data) {
        if(data.type == 'success'){
            var rows = JSON.parse(data.content).content;
            var html = '';
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                html = "<li >"
                    + "<a>"
                html += "  <div class='t' onclick='formDetail(\"" + row.sn + "\",\"" + row.id + "\",\"" + row.create_date + "\")'>"
                    + "       <div class='no fl'>单据编号：" + row.sn +"</div>"
                    + "       <div class='status btn-lred'>" + row.status + "</div>"
                    + "    </div>"
                html += "  <div class='c' style='background:inherit;' onclick='formDetail(\""+row.sn+"\",\""+row.id+"\",\""+row.create_date+"\")'>"
                    + "       <p><span>启动人：</span>" + row.store_member_name + "</p>"
                    + "       <p><span>创建时间：</span>" + row.create_date + "</p>"
                    + "    </div>";
                html += "</a>"
                    + "</li>";
                $('.order-list').find('ul').append(html)
            }
        }
    }

    //表单详情
    function formDetail(orderSn,id,createDate){
        window.location.href = "form_detail.jhtml?id="+id;
    }

</script>
</body>
</html>
