<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <title></title>
    <link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
    <link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>

    <style>
        .oPro-list .item{padding:20px 10px; border-bottom: solid 1px #f7f7f7;}
        .oPro-list .item .data{overflow:hidden;background:#f7f7f7;padding: 5px 8px;margin-top: 23px;font-size: 0.75rem;line-height: 1.8;border-radius: 2px; display:none}
        .oPro-list .item .data span{ width:100%; float:left;color:#888;display:block;line-height: 300%}
        .oPro-list .item.on .data{display:block}
        .oPro-list .item div {margin-top: -10px;}
        .order-tab .title .btn {margin-left: 5px;}
    </style>

</head>
<body>
<!--
<div class="header">
	<a href="/mobile/index.jhtml" class="go-back"></a>
	<div class="h-txt">充值申请</div>
</div>
 -->
<div class="containt">
    <form id="inputForm">
        <div class="order-tab">
            <div class="title">基本信息</div>
            <input type="hidden" id="id" name="id" value="${cr.id}"/>
            <input type="hidden" id="storeId" name="storeId" value="${cr.store.id}"/>
            <input type="hidden" id="saleOrgId" name="saleOrgId" value="${cr.saleOrg.id}"/>
            [#--            <input type="hidden" id="saleOrgName" name="saleOrgName" value="${saleOrg.name}"/>--]
            <input type="hidden" name="contractId" class="text contractId" id="contractId" btn-fun="clear" value="${cr.customerContract.id}"/>
            <input type="hidden" id="rechargeType" name="rechargeType" value="${cr.rechargeType}"/>
            <div class="dl-style">
                <dl>
                    <dt class="f-black">申请单号</dt>
                    <dd>
                        ${cr.sn}
                    </dd>
                </dl>

                [#if rechargeType == 1]
                <dl>
                    <dt class="f-black">用户<em class="f-red">*</em></dt>
                    <dd data-id="StoreMemberBox"  [#if cr.docStatus==0 ]onclick="showPup(this)"[/#if]><input type="text" class="txt arrow" disabled placeholder="请选择" id="storeMemberName" name="storeMemberName" value="${cr.storeMember.username}"/></dd>
                    <input type="hidden" id="storeMemberId" name="storeMemberId" value="${cr.storeMember.id}"/>
                </dl>
                [#else]


                <dl>
                    <dt class="f-black">客户<em class="f-red">*</em></dt>
                    <dd data-id="CustomerBox" [#if cr.docStatus==0 ] onclick="showPup(this)"[/#if]><input type="text" class="txt arrow" disabled placeholder="请选择" id="storeName" name="storeName" value="${cr.store.name}"/></dd>
                </dl>
                [/#if]
                <dl>
                    <dt class="f-black">客户编码</dt>
                    <dd>

                        <input type="text" class="txt" disabled name="outTradeNo" id="outTradeNo" value="${cr.store.outTradeNo}"/>
                    </dd>
                </dl>

                <dl>
                    <dt>SBU</dt>
                    <dd>
                        <input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${cr.sbu.id}"/>
                        <span id="sbuName">
            	            ${cr.sbu.name}
            	        </span>
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">申请额度</dt>
                    <dd>
                        <div class="priceDiv nums-input ov">
                            <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onClick="addquantity()">
                            <input class="pPrice" id="amount" name="amount" value="${cr.amount}" type="text" oninput="extractNumber(this, 2, false)"  mindata="0" onblur="addquantity()"/>
                            <!-- <span id="productBoxNum">1</span> -->
                            <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onClick="addquantity()">
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">实际充值金额</dt>
                    <dd>
                        [#if cr.docStatus==0  && (!isCheckWf || cr.wfId==null)]
                        <div class="priceDiv nums-input ov">
                            <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" >
                            <input class="actualAmount" id="actualAmount" name="actualAmount" value="${cr.actualAmount}" type="text"  oninput="extractNumber(this, 2, false)"  mindata="0" maxData="${cr.amount}"/>
                            <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" >
                        </div>

                        [#elseif cr.type ==1]
                            <span class="red number">${currency(dr.actualAmount, true)}</span>
                        [#else]
                            <span class="red number"></span>
                        [/#if]

                        [#--                        <span class="red">${currency(0, true)}</span>--]
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">客户余额</dt>
                    <dd>
                        <span class="red" id="balance">${currency(balance, true)}</span>
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">机构<em class="f-red">*</em></dt>
                    <dd data-id="SaleOrgBox" onclick="showPup(this)"><input type="text" class="txt arrow" disabled value="${cr.saleOrg.name}" id="saleOrgName" name="saleOrgName"/></dd>

                </dl>
                <dl>
                    <dt class="f-black">开始日期<em class="f-red">*</em></dt>
                    <dd>
                        [#if cr.docStatus==0  ]
                        <input type="date" class="txt date" id="startDate" name="startDate" btn-fun="clear" value="${(cr.startDate?string("yyyy-MM-dd"))!""}"/>
                        [#else]
                            <span>${(cr.startDate?string("yyyy-MM-dd"))!"-"}</span>
                        [/#if]

                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">结束日期<em class="f-red">*</em></dt>
                    <dd>
                        [#if cr.docStatus==0  ]
                        <input type="date" class="txt date" id="endDate" name="endDate" btn-fun="clear" value="${(cr.endDate?string("yyyy-MM-dd"))!""}"/>
                        [#else]
                            <span>${(cr.endDate?string("yyyy-MM-dd"))!"-"}</span>

                        [/#if]
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">经营组织</dt>
                    <dd>
                        <select class="txt arrow" id="organizationId" name="organizationId" onchange="selectBal()"  >
                            [#list managementOrganizations as managementOrganization]
                                <option value="${managementOrganization.id}"[#if cr.organization.id == managementOrganization.id]selected="selected"[/#if]>${managementOrganization.name}</option>
                            [/#list]
                        </select>
                        <!-- <input type="text" class="txt" disabled id="business_type_value" value="${store.businessType.value}"/> -->
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">创建人</dt>
                    <dd>
                        ${cr.creator.name}
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">合同名称[#--<em class="f-red">*</em>--]</dt>
                    <dd data-id="contractBox"  onclick="showPup(this)">
                        <input type="text" class="txt arrow" disabled placeholder="" id="contractName" name="contractName" value="${cr.customerContract.contractName}"/>
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">近半年进货额</dt>
                    <dd>
                        <input type="text" class="txt" id="purchaseAmount" name="purchaseAmount"  placeholder="请输入" value="${cr.purchaseAmount}"/>
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">平均进货额</dt>
                    <dd>
                        <input type="text" class="txt" id="pjPurchaseAmount" name="pjPurchaseAmount" placeholder="请输入" value="${cr.pjPurchaseAmount}"/>
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">授信类型</dt>
                    <dd>
                        <select class="txt arrow" id="creditTypeId" name="creditTypeId">
                            <option></option>
                            [#list creditTypes as ct]
                                <option value="${ct.id}"[#if cr.creditType.id == ct.id]selected="selected"[/#if]>${ct.value}</option>
                            [/#list]
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">备注</dt>
                    <dd>
                        <textarea class="txt" id="memo" name="memo" placeholder="请输入" >${cr.memo}</textarea>
                    </dd>
                </dl>

[#--                <dl>--]
[#--                    <dt class="f-black">审核状态</dt>--]
[#--                    <dd>--]
[#--                        [#if cr.status == "0"]<b class="green">${message("待审核")}</b>[/#if]--]
[#--                        [#if cr.status == "2"]<b class="red">${message("未通过")}</b>[/#if]--]
[#--                        [#if cr.status == "1"]<b class="blue">${message("通过")}</b>[/#if]--]
[#--                    </dd>--]
[#--                </dl>--]

                <dl>
                    <dt class="f-black">流程状态</dt>
                    <dd>
                        [#if cr.wfState??]
                            ${message("22222222"+cr.wfState)}
                        [/#if]
                    </dd>
                </dl>

                <dl>
                    <dt class="f-black">单据状态</dt>
                    <dd>
                        [#if cr.docStatus == 0]<b class="blue">${message("已保存")}</b>[/#if]
                        [#if cr.docStatus == 1]<b class="blue">${message("处理中")}</b>[/#if]
                        [#if cr.docStatus == 2]<b class="green">${message("已处理")}</b>[/#if]
                        [#if cr.docStatus == 3]<b class="red">${message("已作废")}</b>[/#if]
                    </dd>
                </dl>
                <dl>
                    <dt class="f-black">状态</dt>
                    <dd>
                        [#if cr.type == "0"]<b class="red">${message("未生效")}</b>[/#if]
                        [#if cr.type == "1"]<b class="blue">${message("已生效")}</b>[/#if]
                        [#if cr.type == "2"]<b class="">${message("已过期")}</b>[/#if]
                    </dd>
                </dl>
            </div>
        </div>


        <div class="order-tab mt8" >
            <div class="title">附件信息<a href="javascript:void(0);" class="lbtn-orgn btn" data-id="AttaBox"  onclick="addAttach(this)"><i class="icon ico-add"></i>添加附件</a></div>
            <div class="atta-list" style="margin-bottom: 80px;">

            </div>
        </div>

        <div class="h50 mt6"></div>
        <div class="h50 mt6"></div>

        <div class="order-btns" style="text-align: center;">
[#--            <input type="button" class="order-submit" value="保存" onclick="saveForm()" />--]
[#--            <input type='button' class='order-submit btn-blue' value='提交' onclick='submitForm(this)' style='float:right;' />--]

            [#if cr.docStatus==0 ]
                [#if isCheckWf]
                    [#if cr.wfId==null]
                        <input type='button' class='order-submit btn-blue' value='审核' onclick='submitForm(this)' style='float:right;' />
                    [/#if]

                [/#if]
            [/#if]
            [#if  cr.docStatus==0 && (!isCheckWf || cr.wfId==null)]
                <input type="button" class="order-submit btn-blue" value="保存" onclick="saveForm()" />
            [/#if]
            [#if (cr.docStatus ==0 && cr.wfId==null)||cr.docStatus ==2]
                <input type="button" class="order-submit btn-lgray" style="background-color: #c79083" value="${message("作废")}" onclick="cancle()"/>
            [/#if]
        </div>

        [#--        <div class="info-btns" >--]
        [#--            <input type="button" class="btn-blue btn" value="保存" onclick="saveForm()" />--]
        [#--        </div>--]

    </form>
    <form id="fileForm">
        <input type="file" name="file" id="file" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this)"  style="display: none"/>
    </form>
</div>
<div class="pup-obox" id="CustomerBox">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            [#--            <a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
            <div class="h-txt">请选择客户</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="客户名称" id="searchByCustomerName"/>
            <input type="button" class="btn" value="搜索" onclick="searchCustomer()">
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;">

    </ul>
</div>
<div class="pup-obox" id="StoreMemberBox">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            [#--            <a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
            <div class="h-txt">请选择用户</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="用户名称" id="searchByStoreMemberName"/>
            <input type="button" class="btn" value="搜索" onclick="searchStoreMember()">
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;">

    </ul>
</div>
<div class="pup-obox" id="SaleOrgBox">
    <div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
        <div class="pup-header">
            <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
            [#--            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择</div>
        </div>
        [#--        <div class="search-box">--]
        [#--            <input type="search" class="txt" placeholder="账号" id="searchBybankCardNo"/>--]
        [#--            <input type="button" class="btn" value="搜索" onclick="searchBybankCardNo()">--]
        [#--        </div>--]
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;">

    </ul>
</div>
<div class="pup-obox" id="contractBox">
    <div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
        <div class="pup-header">
            <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
            [#--            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择</div>
        </div>
        [#--        <div class="search-box">--]
        [#--            <input type="search" class="txt" placeholder="账号" id="searchBybankCardNo"/>--]
        [#--            <input type="button" class="btn" value="搜索" onclick="searchBybankCardNo()">--]
        [#--        </div>--]
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;">

    </ul>
</div>
<div class="pup-obox" id="AttaBox">
    <div class="header">
        <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
        <div class="h-txt">请选择附件</div>
    </div>
    <div class="choose-attr" id="b">
        <label class="checkhid item" data-id="b">
            <input type="checkbox" name="1">
            <div class="pic"><img src="/resources/images/ico-doc.png"></div>
            <div class="name">附件1</div>
            <div class="time">2018-09-09 12:00</div>
            <div class="check_box"></div>
        </label>
        <label class="checkhid item" data-id="b">
            <input type="checkbox" name="1">
            <div class="pic"><img src="/resources/images/ico-doc.png"></div>
            <div class="name">附件1</div>
            <div class="time">2018-09-09 12:00</div>
            <div class="check_box"></div>
        </label>
    </div>
    <div class="attr-bottom">
        <div class="fl">已选择：1</div>
        <input type="button" value="确定" class="btn btn-blue">
    </div>
</div>
<script src="/resources/js/swiper.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>

<script type="text/javascript">
    $().ready(function(){
        $(".js-cancle").click(function(){
            $(".pup-obox").hide()
            $("body").attr("style","overflow:auto")
        })
        //根据客户姓名搜索客户
        /* $("#searchByCustomerName").blur(function(){
            var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
            showPup(e)
        })
        $("#searchByCustomerName").keypress(function (e) {
            if (e.which == 13) {
                var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
                showPup(e)
            }
        }) */
        //根据账号搜索客户收款账号
        /* $("#searchBybankCardNo").blur(function(){
            var e = $('.containt').find('dd[data-id="AccountBox"]')[0]
            showPup(e)
        })
        $("#searchBybankCardNo").keypress(function (e) {
            if (e.which == 13) {
                var e = $('.containt').find('dd[data-id="AccountBox"]')[0]
                showPup(e)
            }
        }) */

        //附件信息
        var attachs = JSON.parse('${twContractAttach_json}')
        console.log(attachs)
        for (var i = 0; i < attachs.length; i++) {
            var attach = attachs[i]
            var memo = ""
            if (attachs.memo != null) {
                memo = attachs.memo;
            }
            var html = "<div class='item'>"
                +"<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                +"<div class='tit'>附件 <a href='"+attach.url+"' target='_blank'><span class='name fr'>"+attach.file_name+"</span></a></div>"
                +"<textarea class='txt' placeholder='备注' id='attachMemo' name='creditAttachs["+i+"].memo'>"+memo+"</textarea>"
                +"<input type='hidden' id='attachName' name='creditAttachs["+i+"].name' value='"+attach.file_name.split('.')[0]+"'/>"
                +"<input type='hidden' id='attachUrl' name='creditAttachs["+i+"].url' value='"+attach.url+"'/>"
                +"<input type='hidden' id='attachSuffix' name='creditAttachs["+i+"].suffix' value='"+attach.file_name.split('.')[1]+"'/>"
                +"</div>";
            $(".atta-list").append(html)
        }



        var amount = ${cr.actualAmount};
        if (amount.toString().indexOf("-") != -1) {
            var str = fmoney(amount.toString().substring(1, amount.length));
            amount = "-" + str;
        } else {
            amount = fmoney(amount);
        }
        $('.number').text("￥" + amount);


    })

    // 根据客户姓名搜索客户
    function searchCustomer() {
        var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
        showPup(e);
    }

    function searchStoreMember() {
        var e = $('.containt').find('dd[data-id="StoreMemberBox"]')[0]
        showPup(e);
    }
    // 根据账号搜索客户收款账号
    function searchBybankCardNo() {
        var e = $('.containt').find('dd[data-id="SaleOrgBox"]')[0]
        showPup(e);
    }
    function showPup(e){
        var id = $(e).attr("data-id")
        //$("body").attr("style","overflow:hidden")
        $(".pup-obox").hide()
        pageNumber = 2;
        $("#"+id).show()
        if(id == "CustomerBox"){//选择客户
            $('#'+id).find('ul').empty()
            $.ajax({
                type:'POST',
                url:'/member/store/select_store_data.jhtml',
                data:{name:$('#searchByCustomerName').val()},
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = JSON.parse(data.content).content;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var html = "<li data-id='CustomerBox' onclick='selectItem(this)'><a href='#'>"
                                +"<div class='name'>"+row.name+"</div>"
                                +"<div class='fl'><span>机构：</span>"+row.sale_org_name+"</div>"
                                +"<div class='fr'><span>编码：</span>"+row.out_trade_no+"</div>"
                                +"</a>"
                                +"<input type='hidden' id='storeName' value='"+row.name+"'/>"
                                +"<input type='hidden' id='storeId' value='"+row.id+"'/>"
                                +"<input type='hidden' id='saleOrgId' value='"+row.sale_org_id+"'/>"
                                +"<input type='hidden' id='saleOrgName' value='"+row.sale_org_name+"'/>"
                                +"<input type='hidden' id='outTradeNo' value='"+row.out_trade_no+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html)
                        }
                    }
                }
            })
        }
        if(id == "SaleOrgBox"){//查询机构
            $('#'+id).find('ul').empty()
            $.ajax({
                type:'POST',
                url:'/basic/saleOrg/getChildren.jhtml',
                // data:{saleOrgId:$('#saleOrgId').val(),bankCardNo:$('#searchBybankCardNo').val(),sbuId:$('#sbuId').val()},
                data:{id:364},
                success:function(data) {
                    if(data.type == 'success'){
                        // var rows = JSON.parse(data.content).content;
                        var rows = data.objx;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            console.log(row)
                            var html = "<li data-id='SaleOrgBox' onclick='selectItem(this)'><a href='#'>"
                                // +"<div class='name'>"+row.bank_card_no+"</div>"
                                +"<div class='fl'><span>机构名称：</span>"+row.name+"</div>"
                                +"</a>"
                                +"<input type='hidden' id='saleOrgId' value='"+row.id+"'/>"
                                +"<input type='hidden' id='saleOrgName' value='"+row.name+"'/>"

                                // +"<input type='hidden' id='bankCardNo' value='"+row.bank_card_no+"'/>"
                                // +"<input type='hidden' id='bankName' value='"+row.bank_name+"'/>"
                                // +"<input type='hidden' id='organizationId' value='"+row.organization+"'/>"
                                // +"<input type='hidden' id='organizationName' value='"+row.organization_name+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html)
                        }
                    }
                }
            })
        }

        if(id == "contractBox"){//查询合同
            $('#'+id).find('ul').empty()
            var sbuId = $("#sbuId").val();
            var storeId = $("#storeId").val();
            $.ajax({
                type:'POST',
                data:{
                    sbuId:sbuId,
                    storeId:storeId,
                },
                url:'/b2b/customerContract/select_contract_data.jhtml',
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = JSON.parse(data.content).content;
                        // var rows = data.objx;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            // console.log(row)
                            var contract_no = row.contract_no==null?'':row.contract_no;
                            var contract_name = row.contract_name==null?'':row.contract_name;
                            var store_name = row.contract_no==null?'':row.store_name;
                            var distributor_name = row.distributor_name==null?'':row.distributor_name;
                            var contract_no = row.contract_no==null?'':row.contract_no;
                            var sale_org_name = row.sale_org_name==null?'':row.sale_org_name;


                            var html = "<li data-id='contractBox' onclick='selectItem(this)'><a href='#'>"
                                // +"<div class='name'>"+row.bank_card_no+"</div>"
                                +"<div class=''><span>合同名称:</span>"+contract_no+"</div>"
                                +"<div class=''><span>合同编码:</span>"+contract_name+"</div>"
                                +"<div class=''><span>甲方/经销商:</span>"+store_name+"</div>"
                                +"<div class=''><span>客户名称:</span>"+distributor_name+"</div>"
                                +"<div class=''><span>机构:</span>"+sale_org_name+"</div>"
                                +"</a>"
                                +"<input type='hidden' id='contractId' value='"+row.id+"'/>"
                                +"<input type='hidden' id='contractName' value='"+contract_no+"'/>"

                                +"</li>";
                            $('#'+id).find('ul').append(html)
                        }
                    }
                }
            })
        }


        if(id == "StoreMemberBox"){//选择用户
            $('#'+id).find('ul').empty()
            $.ajax({
                type:'POST',
                url: '/member/store_member/select_store_member.jhtml',
                data:{name:$('#searchByStoreMemberName').val()},
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = JSON.parse(data.content).content;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var html = "<li data-id='StoreMemberBox' onclick='selectItem(this)'><a href='#'>"
                                +"<div class='name'>"+row.name+"</div>"
                                // +"<div class='fl'><span>机构：</span>"+row.sale_org_name+"</div>"
                                // +"<div class='fr'><span>编码：</span>"+row.out_trade_no+"</div>"
                                +"</a>"
                                +"<input type='hidden' id='storeMemberName' value='"+row.name+"'/>"
                                +"<input type='hidden' id='storeMemberId' value='"+row.id+"'/>"
                                // +"<input type='hidden' id='saleOrgId' value='"+row.sale_org_id+"'/>"
                                // +"<input type='hidden' id='saleOrgName' value='"+row.sale_org_name+"'/>"
                                // +"<input type='hidden' id='outTradeNo' value='"+row.out_trade_no+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html)
                        }
                    }
                }
            })
        }



    }
    function selectItem(e){
        var id = $(e).attr("data-id")
        //$("body").attr("style","overflow:hidden")
        $(".pup-obox").hide()
        $("#"+id).hide()
        if(id == "CustomerBox"){//选择客户
            $('.containt').find('input[id="storeName"]').val($(e).find('input[id="storeName"]').val())
            $('.containt').find('input[id="storeId"]').val($(e).find('input[id="storeId"]').val())
            $('.containt').find('input[id="saleOrgId"]').val($(e).find('input[id="saleOrgId"]').val())
            $('.containt').find('input[id="saleOrgName"]').val($(e).find('input[id="saleOrgName"]').val())
            $('.containt').find('input[id="outTradeNo"]').val($(e).find('input[id="outTradeNo"]').val())
        }


        if(id == "SaleOrgBox"){
            $('.containt').find('input[id="saleOrgName"]').val($(e).find('input[id="saleOrgName"]').val())
            $('.containt').find('input[id="saleOrgId"]').val($(e).find('input[id="saleOrgId"]').val())

        }

        if(id == "contractBox"){
            $('.containt').find('input[id="contractName"]').val($(e).find('input[id="contractName"]').val())
            $('.containt').find('input[id="contractId"]').val($(e).find('input[id="contractId"]').val())

        }


    }
    //保存
    function saveForm(){
        var flag = confirm("您确定要保存吗？");
        if (flag != true) {
            return ;
        }
        if($("#inputForm").find('input[id="storeId"]').val() == null || $("#inputForm").find('input[id="storeId"]').val() == ""){
            alert("请选择客户")
            return false
        }

        if($("#endDate").val() == null || $("#endDate").val() == ""){
            alert("请填结束时间")
            return false
        }
        var jsonData = getJsonData();


        $.ajax({
            type:'POST',
            url:'/member/credit_recharge/updata.jhtml',
            data:jsonData,
            success:function(data) {
                alert(data.content)
                if(data.type == 'success'){
                    // window.location.href = "/mobile/index.jhtml"
                    location.reload(true);
                    // window.open('store_recharge_detail.jhtml?id='+data.objx,'_self')
                    // parent.location.href = "/mobile/order/store_recharge_query.jhtml"


                    //location.replace("/mobile/order/store_recharge_query.jhtml");
                }
            }
        })
    }

    function getJsonData() {
        var jsonData = {
            'id':$('#id').val(),
            'rechargeType':$('#rechargeType').val(),
            'storeId':$("#storeId").val(),
            'storeName':$("#storeName").val(),
            'saleOrgId':$("#saleOrgId").val(),
            'memo':$("#memo").val(),
            'sbuId':$('#sbuId').val(),
            'startDate':$('#startDate').val(),
            'endDate':$('#endDate').val(),
            'organizationId':$('#organizationId').val(),
            'contractId':$('#contractId').val(),
            'businessTypeId':$('#business_type_id').val(),
            'totalQuantity':$('#totalQuantity').val(),
            'amount':$("#amount").val(),
            'actualAmount':$("#actualAmount").val(),
            'creditTypeId':$("#creditTypeId").val(),
            'pjPurchaseAmount':$("#pjPurchaseAmount").val(),
            'purchaseAmount':$("#purchaseAmount").val(),

            // 'needDate':$("#month").val(),
            //'bankCard.id':$("#bankCardId").val(),
            //'rechargeTypeId':$("#rechargeTypeId").val(),
            //'balanceMonth':$("#applyDate").val().substr(0,7),
            //'bankCardNo':$("#bankCardNo").val(),
            //'glDate':$("#glDate").val(),
            //'organizationId':$("#organizationId").val(),
            // 'saleOrgName':$("#saleOrgName").val(),
            // 'applyDate':$("#applyDate").val(),
        }
        var attachs = $('.atta-list').find('div[class="item"]')
        for (var i = 0; i < attachs.length; i++) {
            jsonData['creditAttachs['+i+'].name'] = $(attachs[i]).find('input[id="attachName"]').val()
            jsonData['creditAttachs['+i+'].url'] = $(attachs[i]).find('input[id="attachUrl"]').val()
            jsonData['creditAttachs['+i+'].suffix'] = $(attachs[i]).find('input[id="attachSuffix"]').val()
            jsonData['creditAttachs['+i+'].memo'] = $(attachs[i]).find('textarea[id="attachMemo"]').val()
        }

        //var jsonData = $('#inputForm').serialize()
        // var attachs = $('.atta-list').find('div[class="item"]')
        // for (var i = 0; i < attachs.length; i++) {
        //     jsonData['creditAttachs['+i+'].name'] = $(attachs[i]).find('input[id="attachName"]').val()
        //     jsonData['creditAttachs['+i+'].url'] = $(attachs[i]).find('input[id="attachUrl"]').val()
        //     jsonData['creditAttachs['+i+'].suffix'] = $(attachs[i]).find('input[id="attachSuffix"]').val()
        //     jsonData['creditAttachs['+i+'].memo'] = $(attachs[i]).find('textarea[id="attachMemo"]').val()
        // }
        // console.log(jsonData)
        return jsonData;
    }

    // 提交表单
    function submitForm(e) {

        var flag = confirm("您确定要提交吗？");
        var dr_id = "";
        var actual_amount = $("#amount").val();
        if (flag != true) {
            return ;
        }

        if($("#inputForm").find('input[id="storeId"]').val() == null || $("#inputForm").find('input[id="storeId"]').val() == ""){
            alert("请选择客户")
            return false
        }

        if($("#endDate").val() == null || $("#endDate").val() == ""){
            alert("请填结束时间")
            return false
        }
        // if($("#storeRechargeInfoForm").find('input[id="bankCardId"]').val() == null || $("#storeRechargeInfoForm").find('input[id="bankCardId"]').val() == ""){
        //     alert("请选择账号")
        //     return false
        // }

        var jsonData = getJsonData();
        $.ajax({
            type:'POST',
            url:'/member/credit_recharge/updata.jhtml',
            data:jsonData,
            success:function(data) {
                alert(data.content)
                if(data.type == 'success'){

                    check_wf('${cr.id}',jsonData);

                    /* alert(data.content)
                    window.location.href = "/mobile/index.jhtml" */

                    // window.open('store_recharge_detail.jhtml?id='+data.objx,'_self')
                    // parent.location.href = "/mobile/order/store_recharge_query.jhtml"
                    // alert("id: " + data.objx);
                    //dr_id = data.objx
                    // location.replace("/mobile/order/store_recharge_query.jhtml");
                    // ---------------
                    // if (dr_id == null || dr_id == "") {
                    //     return ;
                    // }

                }
            }
        })
    }



    //添加附件
    function addAttach(e){
        $('#file').trigger('click');
    }
    // 附件上传
    function fileUpload(e){
        var formData = new FormData($( "#fileForm" )[0]);
        var len = $('.atta-list').find('div[class="item"]').size()
        $.ajax({
            type:'GET',
            url:'/common/fileurl.jhtml',
            success:function(data) {
                if(data.type == "success"){
                    $.ajax({
                        type:'POST',
                        url: data.objx,
                        data:formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success:function(data_) {
                            data_ = JSON.parse(data_)
                            if(data_.message.type == "success"){
                                var html = "<div class='item'>"
                                    +"<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                                    +"<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
                                    +"<textarea class='txt' placeholder='请输入备注' id='attachMemo' name='creditAttachs["+len+"].memo'></textarea>"
                                    +"<input type='hidden' id='attachName' name='creditAttachs["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
                                    +"<input type='hidden' id='attachUrl' name='creditAttachs["+len+"].url' value='"+data_.url+"'/>"
                                    +"<input type='hidden' id='attachSuffix' name='creditAttachs["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
                                    +"</div>";
                                $('.atta-list').append(html)
                            }
                        }
                    })
                }
            }
        })
    }



    //删除
    function del(e){
        $(e).parent('div').remove()
        editTotal()
    }

    function onShow(e){
        $(e).toggleClass("on")
    }

    //流程审核
    function check_wf(id,jsonData){
        var objTypeId = 100026;//大自然正式和测试
        var modelId = model($("#sbuId").val(),"正式");
        var sbuId = $("#sbuId").val();
        var type = $('#creditTypeId').val();
        if(sbuId==1 && type==572){
           modelId=3121625;
        }
        var url="/member/credit_recharge/start_check_wf.jhtml?id="+id+"&modelId="+modelId+"&objTypeId="+objTypeId;
        $.ajax({
            type:'POST',
            url:url,
            data:jsonData,
            success:function(data) {
                alert(data.content)
                if(data.type == "success"){
                    //window.location.href = "/mobile/index.jhtml"
                    location.reload(true);
                }
            }
        });
    }

    //查流程模板
    function model(sbuId,versions){
        var json = '{"正式":{"1":"2398551","2":"60137","3":"60143","4":"60135","5":"60139","6":"57501","7":"994939","8":"57501"},';
        json +='"测试":{"1":"1350470","2":"122557","3":"122561","4":"122555","5":"122559","6":"","7":"","8":"175142"}}';
        var model = JSON.parse(json);
        return model[versions][sbuId];
    }
    
    function selectBal(){
        var ssId = $(".storeId").val();
        var sbuId = $("#sbuId").val();
        var saleOrgId = $(".saleOrgId").val();
        var orgId = $("#organizationId option:selected").val();
        ajaxSubmit("",{
            method:'post',
            url:'/finance/balance/get_balance.jhtml',
            data:{storeId:ssId,sbuId:sbuId,saleOrgId:saleOrgId,organizationId:orgId},
            callback:function(resultMsg) {
                var data = resultMsg.objx;
                if(data!=null){
                    //可用余额
                    $("#balance").text(currency(data.balance,true));
                }
            }
        });
    }

    //计划赋值实际
    function addquantity() {
        var $input = $("input.pPrice");
        $('#actualAmount').val($input.val())
        $('#actualAmount').attr('maxdata',$input.val());
    }


    //表单作废
    function cancle(){
        var flag = confirm("您确定要作废该订单吗？")
        if(flag == true){
            $.ajax({
                type:'POST',
                url:'/member/credit_recharge/cancel.jhtml?id=${cr.id}',
                success:function(resultMsg) {
                    if(resultMsg.type == "success"){
                        alert(resultMsg.content)
                        location.reload()
                    }
                }
            })
        }
    }


    function fmoney(s, n) {
        n = n > 0 && n <= 20 ? n : 2;
        s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
        var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
        t = "";
        for (i = 0; i < l.length; i++) {
            t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
        }
        return t.split("").reverse().join("") + "." + r;
    }
</script>

</body>
</html>
