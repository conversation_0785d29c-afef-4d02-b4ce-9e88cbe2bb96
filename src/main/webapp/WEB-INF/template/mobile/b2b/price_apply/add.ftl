<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <title></title>
    <link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
    <link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
    <script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <style>
        .oPro-list .item{padding:8px 10px;}
        .oPro-list .item .data{overflow:hidden;background:#f7f7f7;padding: 5px 8px;margin-top: 23px;font-size: 0.7rem;line-height: 1.8;border-radius: 2px; display:none}
        .oPro-list .item .data span{ width:100%; float:left;color:#888}
        .oPro-list .item.on .data{display:block}
    </style>
</head>
<body>
<div class="containt">
   	<form id="inputForm" action="/b2b/priceApply/save.jhtml" method="post" type="ajax" >
    <div class="order-tab">
        <div class="title">基本信息</div>
	        <div class="dl-style">
	            <dl>
	                <dt class="f-black">客户</dt>
	                <dd data-id="CustomerBox"  onclick="showPup(this)">
	                	<input type="hidden" class="txt arrow" id="storeId" name="store.id" value="${store.id}"/>
	                	<input type="text" class="txt arrow" placeholder="请选择" id="name" value="${store.name}">
	                	<input type="hidden" id="memberRankId" value="" />
	                </dd>
	            </dl>
	            <dl>
	                <dt>特价类型</dt>
	                <dd>
	                    <select id="type" class="txt arrow" name="typeID" onchange="projectChange(this)">
                        [#list specialTypes as type]
                            <option value="${type.id}">${type.value}</option>
                        [/#list]
                    	</select>
	                </dd>
	            </dl>
	            <dl id="gongcheng" style="display:none;">
	                <dt>工程<em class="f-red">*</em></dt>
	                <dd data-id="EngineeringBox"  onclick="showPup(this)">
	                	<input type="hidden" class="txt arrow" name="engineering.id" id="engineering_id" value="${store.saleOrg.id}"/>
	                	<input type="text" class="txt arrow" placeholder="请选择" id="engineering_name" value="${store.saleOrg.name}">
	                </dd>
	            </dl>
	            <dl>
	                <dt>特价开始时间<em class="f-red">*</em></dt>
	                <dd>
	                	<input type="date" class="txt date" name="startDate" btn-fun="clear" value=""/>
	                </dd>
	            </dl>
	            <dl>
	                <dt>特价结束时间<em class="f-red">*</em></dt>
	                <dd>
	                    <input type="date" class="txt date" name="endDate" btn-fun="clear" value=""/>
	                </dd>
	            </dl>
	            <dl>
	                <dt>机构</dt>
	                <dd data-id="SaleOrgBox"  onclick="showPup(this)">
	                	<input type="hidden" class="txt arrow" name="saleOrg.id" id="input_sale_org_id" value="${store.saleOrg.id}"/>
	                	<input type="text" class="txt arrow" placeholder="请选择" id="sale_org_name" value="${store.saleOrg.name}">
	                </dd>
	            </dl>
	            <dl>
	                <dt>SBU</dt>
	                <dd><input type="text" class="txt" disabled value="${sbu.name}"/>
	                    <input type="hidden" id="sbuId" name="sbuId" value="${sbu.id}"/>
	                </dd>
	            </dl>
	            <dl>
	                <dt>经营组织<em class="f-red">*</em></dt>
	               	<dd>
						<select class="txt arrow" name="organization.id" id="organizationId">
	                        [#list organizations as organization]
							<option  value="${organization.id}" >${organization.name}</option>
							[/#list]
	                    </select>
	               	</dd>
	            </dl>
	            <dl>
	                <dt>申请说明<em class="f-red">*</em></dt>
	                <dd><textarea class="txt" placeholder="请输入" name="memo" id="baseInfoMemo"></textarea></dd>
	            </dl>
	        </div>
    </div>
    <div class="order-tab mt8">
        <div class="title">
        	选择产品
        	<a href="javascript:void(0);" class="lbtn-orgn btn" data-id="ProBox"  onclick="showPup(this)">
        	<i class="icon ico-add"></i>新增产品</a>
        </div>
        <div class="oPro-list">

        </div>
    </div>
    <div class="order-tab mt8">
        <div class="title">订单汇总</div>
        <div class="dl-style">
           <dl>
                <dt>总优惠数量</dt>
                <dd><span id="priceApplyMax">0</span></dd>
            </dl>
            <dl>
                <dt>总优惠金额</dt>
                <dd><span id="priceApplyAmount">0</span></dd>
            </dl>
        </div>
    </div>
    <div class="order-tab mt8">
        <div class="title">附件信息<a href="javascript:void(0);" class="lbtn-orgn btn" data-id="AttaBox"  onclick="addAttach(this)"><i class="icon ico-add"></i>添加附件</a></div>
        <input type="file" id="priceApplyFile" name="file" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this)"  style="display: none"/>
        <div class="atta-list"></div>
    </div>
    
    <!--产品选择详情页-->
	<div class="pup-obox" id="ProBox1">
	    <div class="header">
	        <a href="javascript:void(0);" class="go-back"  data-id="ProBox"  onclick="showPup(this)"></a>
	        <div class="h-txt">特价明细</div>
	    </div>
	    <div class="dl-style">
	        <input type="hidden" id="productName" value=""/>
	        <input type="hidden" id="productDesc" value=""/>
	        <input type="hidden" id="productCategoryId" value=""/>
	        <input type="hidden" id="productId" value=""/>
	        <input type="hidden" id="vonderCode" value=""/>
	        <input type="hidden" id="unit" value=""/><!-- 产品单位 -->
	        <input type="hidden" id="productGrade" class="pg2" value=""/><!-- 产品等级 -->
	        <input type="hidden" id="price" value=""/>
	        <dl>
	            <dt>发货仓</dt>
	            <dd>
	            	<select class="txt arrow" id="shippingWarehouse" class="text shippingWarehouseId" >
					[#list shippingWarehouses as shippingWarehouse]
						<option value="${shippingWarehouse.id}" >${shippingWarehouse.value}</option>
	                [/#list]
	                </select>
	            </dd>
	        </dl>
	        <dl>
	            <dt>产品系列</dt>
	            <dd>
	            	<span id="productCategoryName"></span>
	            	<span id="productCategoryId" style="display:none;"></span>
	            </dd>
	        </dl>
	        <dl>
	            <dt>产品名称</dt>
	            <dd>
	            	<span id="productName"></span>
	            	<span id="productId" style="display:none;"></span>
	            </dd>
	        </dl>
	        <dl>
	            <dt>产品型号</dt>
	            <dd><span id="model"></span></dd>
	        </dl>
	        <dl>
	            <dt>产品编码</dt>
	            <dd><span id="vonderCode"></span></dd>
	        </dl>
	        <dl>
	            <dt>产品描述</dt>
	            <dd><span id="description"></span></dd>
	        </dl>
	        <dl>
	            <dt>产品等级</dt>
	            <dd>
	                <select class="txt arrow" id="productGrade" class="productGrade2">
	                    [#list productLevelList as products]
	                        <option value="${products.id}">${products.value}</option>
	                    [/#list]
	                </select>
	            </dd>
	        </dl>
	        <dl>
	            <dt>原价</dt>
	            <dd>
	            	<span id="member_price"></span>
	            	<input type="hidden" id="memberPrice" />
	            </dd>
	        </dl>
	        <dl>
	            <dt>审批价</dt>
	            <dd>
	                <div class="priceDiv nums-input ov">
	                    <input type="button" class="b decrease " value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextElementSibling,event)">
	                    <input type="text" id="item_price" value="0" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >
	                    <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousElementSibling,event)">
	                </div>
	            </dd>
	        </dl>
	        <dl>
	            <dt>折扣</dt>
	            <dd>
	            	<span id="discountt">100</span>
	            	<input type="hidden" id="discount" minData="0" >
	            </dd>
	        </dl>
	        <dl>
	            <dt>平台结算价原价</dt>
	            <dd><span id="sale_org_price"></span></dd>
	        </dl>
	        <dl>
	            <dt>平台结算价特价</dt>
	            <dd>
	            	<div class="priceDiv nums-input ov">
	                    <input type="button" class="b decrease " value="-"  onMouseDown ="decrease(this,event)">
	                    <input type="text" id="saleOrgSalesPrice" value="0" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >
	                    <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
	                </div>
	            </dd>
	        </dl>
	        <dl>
	            <dt>最小开单数量/m2</dt>
	            <dd>
	            	<div class="priceDiv nums-input ov">
	                    <input type="button" class="b decrease " value="-"  onMouseDown ="decrease(this,event)">
	                    <input type="text" id="minQuantity" value="0" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >
	                    <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
	                </div>
	            </dd>
	        </dl>
	        <dl>
	            <dt>最大累计数量/m2</dt>
	            <dd>
	            	<div class="priceDiv nums-input ov">
	                    <input type="button" class="b decrease " value="-"  onMouseDown ="decrease(this,event)">
	                    <input type="text" id="maxQuantity" value="0" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >
	                    <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
	                </div>
	            </dd>
	        </dl>
	    </div>
	    <div class="h50 mt6"></div>
	    <div class="order-btns">
	        <input type="button" class="order-submit btn-blue item" style="width:88%" data-id="ProBox1" onclick='selectItem(this)' value="保存">
	    </div>
	</div>
	<!--产品选择详情页end-->
   
    <div class="h50 mt6"></div>
    <div class="order-btns">
        <input type="button" class="order-submit btn-blue invoices" onclick="saveOrder(this,1)" value="保存" />
    </div>
    </from>
</div>
<!--选择客户搜索页-->
<div class="pup-obox" id="CustomerBox">
    <div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
        <div class="pup-header">
            <!-- <a href="javascript:history.back(-1);" class="go-back js-cancle"></a> -->
            <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
            <div class="h-txt">请选择客户</div>
        </div>
        <div class="search-box">
            <input type="text" class="txt" placeholder="客户名称" id="searchByCustomerName"/>
            <input type="button" class="btn" value="搜索" onclick="searchCustomer()">
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;">

    </ul>
</div>
<!--选择客户搜索页end-->
<!--选择机构搜索页-->
<div class="pup-obox" id="SaleOrgBox">
    <div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
        <div class="pup-header">
            <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
[#--            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择</div>
        </div>
[#--        <div class="search-box">--]
[#--            <input type="search" class="txt" placeholder="账号" id="searchBybankCardNo"/>--]
[#--            <input type="button" class="btn" value="搜索" onclick="searchBybankCardNo()">--]
[#--        </div>--]
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;">

    </ul>
</div>
<!--选择机构搜索页end-->
<!--选择产品搜索页-->
<div class="pup-obox" id="ProBox" >
    <div class="pro-2">
        <div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
            <div class="pup-header">
                <!-- <a href="javascript:history.back(-1);" class="go-back js-cancle"></a> -->
                <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
                <div class="h-txt">请选择商品</div>
            </div>
            <div class="search-box">
                <input type="search" class="txt" placeholder="名称" id="searchByProductName"/>
                <input type="submit" class="btn" value="搜索" onclick="searchPro()">
            </div>
        </div>
        <ul class="pro-list" style="position:relative;padding-top: 100px;">
        </ul>
    </div>
</div>
<!--选择产品搜索页end-->
<!--选择工程搜索页-->
<div class="pup-obox" id="EngineeringBox" >
    <div class="pro-2">
        <div style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee;">
            <div class="pup-header">
                <!-- <a href="javascript:history.back(-1);" class="go-back js-cancle"></a> -->
                <a href="javascript:$('.pup-obox').hide()" class="go-back js-cancle"></a>
                <div class="h-txt">请选择工程</div>
            </div>
            <div class="search-box">
                <input type="search" class="txt" placeholder="名称" id="searchByEngineeringSn"/>
                <input type="submit" class="btn" value="搜索" onclick="searchEngineering()">
            </div>
        </div>
        <ul class="list-txt" style="position:relative;padding-top: 100px;">
        </ul>
    </div>
</div>
<!--选择工程搜索页end-->


<!---------------------------------------页面加载end--------------------------------------->
<script src="/resources/js/swiper.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>

<script type="text/javascript">
	$().ready(function(){
		$(".js-cancle").click(function(){
			$(".pup-obox").hide()
			$("body").attr("style","overflow:auto")
		});
		
		var storeId = $('#storeId').val();
		var sbuId = $('#sbuId').val();
		if(storeId != ""&&sbuId!=null){
			findMemberRank(storeId,sbuId);
		}
		
		count();
		openEngineering($('#type').find("option:selected").text());
		
	});
	
	// 特价类型选择调用
	function projectChange(e){
		openEngineering($(e).find("option:selected").text());
	}
	// 工程搜索显示控制
	function openEngineering(str){
		if(str == "工程"){
			$('#gongcheng').css('display','block');
		}else{
			$('#gongcheng').css('display','none');
			$('#engineering_id').val("");
			$('#engineering_name').val("");
		}
	}
    
    // 搜索客户
    function searchCustomer() {
        var e = $('.containt').find('dd[data-id="CustomerBox"]')[0]
        showPup(e);
    }
    
    // 搜索商品
    function searchPro() {
        var e = $('.containt').find('a[data-id="ProBox"]')[0]
        showPup(e);
    }
    // 搜索工程
    function searchEngineering(){
    	var e = $('.containt').find('a[data-id="EngineeringBox"]')[0]
        showPup(e);
    }
    
    function showPup(e){
        var id = $(e).attr("data-id");
        $(".pup-obox").hide();
        $("#"+id).show();
        if(id == "CustomerBox"){//选择客户
            $('#'+id).find('ul').empty();
            $.ajax({
                type:'POST',
                url:'/member/store/select_store_data.jhtml',
                data:{name:$('#searchByCustomerName').val()},
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = JSON.parse(data.content).content;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var html = "<li data-id='CustomerBox' onclick='selectItem(this)'><a href='#'>"
                                +"<div class='name'>"+row.name+"</div>"
                                +"<div class='fl'><span>机构：</span>"+row.sale_org_name+"</div>"
                                +"<div class='fr'><span>编码：</span>"+row.out_trade_no+"</div>"
                                +"</a>"
                                +"<input type='hidden' id='name' value='"+row.name+"'/>"
                                +"<input type='hidden' id='storeId' value='"+row.id+"'/>"
                                +"<input type='hidden' id='sale_org_name' value='"+row.sale_org_name+"'/>"
                                +"<input type='hidden' id='sale_org_id' value='"+row.sale_org_id+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html);
                            findMemberRank(row.id,$('#sbuId').val());
                        }
                    }
                }
            });
        }
        if(id == "SaleOrgBox"){
        	$('#'+id).find('ul').empty()
            $.ajax({
                type:'POST',
                url:'/basic/saleOrg/getChildren.jhtml',
                data:{id:364},
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = data.objx;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var html = "<li data-id='SaleOrgBox' onclick='selectItem(this)'><a href='#'>"
                                +"<div class='fl'><span>机构名称：</span>"+row.name+"</div>"
                                +"</a>"
                                +"<input type='hidden' id='saleOrgId' value='"+row.id+"'/>"
                                +"<input type='hidden' id='saleOrgName' value='"+row.name+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html);
                        }
                    }
                }
            });
        }
        if(id == "EngineeringBox"){
        	$('#'+id).find('ul').empty()
        	var $storeId = $('#storeId').val();
        	if(isEmpty($storeId)){
        		alert("请选择客户")
        		$(".pup-obox").hide()
                $("#"+id).hide()
                return false
        	}
        	$.ajax({
                type:'POST',
                url:'/basic/engineering/select_list_data.jhtml',
                data:{storeId:$storeId,sn:$('#searchByEngineeringSn').val()},
                success:function(data) {
                    if(data.type == 'success'){
                        var rows = JSON.parse(data.content).content;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var html = "<li data-id='EngineeringBox' onclick='selectItem(this)'><a href='#'>"
                                +"<div class='name'>"+row.name+"</div>"
                                +"<div class='fl'><span>工程编号：</span>"+row.sn+"</div>"
                                +"<div class='fr'><span>工程地址：</span>"+row.address+"</div>"
                                +"</a>"
                                +"<input type='hidden' class='engineeringName' value='"+row.name+"'/>"
                                +"<input type='hidden' class='engineeringId' value='"+row.id+"'/>"
                                +"</li>";
                            $('#'+id).find('ul').append(html);
                        }
                    }
                }
            });
        }
        if(id == "ProBox"){
            $('#'+id).find('ul').empty()
        	var sbuId = $('#sbuId').val();
        	var $storeId = $('#storeId').val();
        	if(sbuId == null || sbuId == ""){
                alert("请选择sbu")
                $(".pup-obox").hide()
                $("#"+id).hide()
                return false
            }
            var name = $('#searchByProductName').val();
            var memberRankId = $('#memberRankId').val();
            var isStr = 1;
            if(/.*[\u4e00-\u9fa5]+.*$/.test(name)){
                isStr = 0;
            }
            $.ajax({
                type:'POST',
            	url:'/product/product/selectProductData.jhtml',
            	data:{name:name,storeId:$storeId,sbuId:sbuId,isStr:isStr,
            	isToOrder:true,isPart:0,memberRankId:memberRankId},
            	async : true,
                success:function(data) {
                	if(data.type == 'success'){
                        var rows = JSON.parse(data.content).content;
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var grade = row.level_Id;
                            var name = isEmpty(row.name)?"-":row.name;
                            var vonderCode = isEmpty(row.vonder_code)?"-":row.vonder_code;
                            var model = isEmpty(row.model)?"-":row.model;
                            var levelName = isEmpty(row.levelName)?"-":row.levelName;
                            var html = "<li><div class='pic productdetail'><img src='"+row.image+"'></div>"
                                +"<div class='txt'>"
                                +"<p class='name'>"+name+"</p>"
                                +"<p>商品编码："+vonderCode+"</p>"
                                +"<p>型号："+model+"</p>"
                                +"<p>等级："+levelName+"</p>";
                            html+="<p>分类："+row.product_category_name+"</p>"
                                +"</div>"
                                +"<div class='btns'>"
                                +"<a href='javascript:void(0)' class='btn-orgn btn' data-id='ProBox1' onclick='showPup(this)'>"
                                +"<i class='icon ico-ok'></i>选择产品</a>"
                                +"</div>"
                                +"<input type='hidden' id='productName' value='"+row.name+"'/>"
                                +"<input type='hidden' id='productId' value='"+row.id+"'/>"
                                +"<input type='hidden' id='productCategoryId' value='"+row.product_category_id+"'/>"
                                +"<input type='hidden' id='productGrade' value='"+row.level_Id+"'/>"
                                +"<input type='hidden' id='vonderCode' value='"+row.vonder_code+"'/>"
                                +"<input type='hidden' id='unit' value='"+row.unit+"'/>"
                                +"<input type='hidden' id='productCategoryName' value='"+row.product_category_name+"'/>"
                                +"<input type='hidden' id='productModel' value='"+row.model+"'/>"
                                +"<input type='hidden' id='description' value='"+row.description+"'/>"
                                +"<input type='hidden' id='member_price' value='"+row.member_price+"' />"
                                +"<input type='hidden' id='sale_org_price' value='"+row.sale_org_price+"' />"
                   			$('#'+id).find('ul').append(html);
                        }
                    }
                }
            });
        }
        if(id == "ProBox1"){
        	$('.invoices').hide();
        	li = $(e).parent('div').parent('li')
            if($(e).html() != ""){
                $('#'+id).find('span[id="productName"]').html(isNull(li.find('input[id="productName"]').val()))
                $('#'+id).find('span[id="productDesc"]').html(isNull(li.find('input[id="productDesc"]').val()))
                $('#'+id).find('span[id="model"]').html(isNull(li.find('input[id="productModel"]').val()))
                $('#'+id).find('span[id="vonderCode"]').html(isNull(li.find('input[id="vonderCode"]').val()))
                $('#'+id).find('span[id="unit"]').html(isNull(li.find('input[id="unit"]').val()))
                $('#'+id).find('span[id="productCategoryName"]').html(isNull(li.find('input[id="productCategoryName"]').val()))
                $('#'+id).find('span[id="memberPrice"]').html(isNull(li.find('input[id="memberPrice"]').val()))
                $('#'+id).find('span[id="description"]').html(isNull(li.find('input[id="description"]').val()))
                $('#'+id).find('span[id="sale_org_price"]').html(isNull(li.find('input[id="sale_org_price"]').val()))
            	$('#'+id).find('span[id="productCategoryId"]').html(isNull(li.find('input[id="productCategoryId"]').val()))
            	$('#'+id).find('span[id="productId"]').html(isNull(li.find('input[id="productId"]').val()))
            	var gradeId = li.find('input[id="productGrade"]').val();
            	if(gradeId != null){
            		$('#'+id).find('#productGrade option').each(function(){ 
            			if($(this).val() == gradeId){
            				$(this).attr("selected", true);
            			}
            		});
            	}
                $('#'+id).find('span[id="member_price"]').html(isNull(li.find('input[id="member_price"]').val()))
            }
        }
    }
	function selectItem(e){
		var id = $(e).attr("data-id");
		$(".pup-obox").hide();
		$("#"+id).hide();
		if(id == "CustomerBox"){//选择客户
			if($(e).find('input[id="storeId"]').val() != $("#storeId").val()){//与之前选择客户不相同
				$('#name').val($(e).find('input[id="name"]').val());
				$('#storeId').val($(e).find('input[id="storeId"]').val());
				$('#sale_org_name').val($(e).find('input[id="sale_org_name"]').val());
				$('#input_sale_org_id').val($(e).find('input[id="sale_org_id"]').val());            	
			}
		}
		if(id == "SaleOrgBox"){//选择机构
			if($(e).find('input[id="saleOrgId"]').val() != $("#saleOrgId").val()){
				$('#sale_org_name').val($(e).find('input[id="saleOrgName"]').val());
				$('#input_sale_org_id').val($(e).find('input[id="saleOrgId"]').val());           	
			}
		}
		if(id == "EngineeringBox"){//选择工程
			if($(e).find('.engineeringId').val() != $("#engineering_id").val()){
				$('#engineering_id').val($(e).find('.engineeringId').val());
				$('input[id="engineering_name"]').val($(e).find('.engineeringName').val());           	
			}
		}
		if(id == "ProBox1"){//选择完明细后保存操作同步
			var obj = $('#'+id);
			var jsonString = JSON.stringify(newItem(obj)) 
			itm.add(JSON.parse(jsonString));
			$('.invoices').show();
			count();
		}
	}
	
	var itm = {
		dom : $('.oPro-list'), 
		itemIndex : 0,
		className : "priceApplyItems",
		row : function(title,span){
			return "<div class='item' onclick='onShow(this)' id='itemLists'>"
                +"<a href='javascript:void(0);' class='ico-del' onclick='itm.del(this)'></a>"
                +"<div class='fl'>"+title+"</div>"
                +"<div class='data'>"+span+"</div>"
                +"</div>";
        },
		span : function(name,value,classValue,className){
			var html = "<span>"
        		+"<div style='float:left' >"+name+"</div>"
        		+"<div style='float:right'>"+value+"</div>";
        		if(classValue != null){
	        		html += "<input type='hidden' class='"+className+"' name='priceApplyItems["+this.itemIndex+"]."+className+"' value='"+classValue+"' >"
        		}
        		html += "</span>";
        	return html;
		},
		del : function(e){//删除
			$(e).parent('div').remove();
			count();
		},
		add : function(content){//插入一行
		 	var str = "";
		 	var shipping_warehouse = "";
		 	[#list shippingWarehouses as shippingWarehouse]
			if(${shippingWarehouse.id}==content.shipping_warehouse){
               shipping_warehouse = "${shippingWarehouse.value}";
            }
            [/#list]
                             
		 	str += this.span("发货仓",shipping_warehouse,content.shipping_warehouse,"shippingWarehouse.id");
		 	str += this.span("产品系列",content.product_category_name,content.product_category_id,"productCategory.id");
		 	str += this.span("产品名称",content.name,content.id,"product.id");
		 	str += this.span("产品型号",content.model,null,null);
		 	str += this.span("${message("12211")}",content.vonder_code,null,null);
		 	str += this.span("产品描述",content.description,null,null);
		 	str += this.span("产品等级",content.product_grade,content.level_Id,"productLevel.id");
		 	if(content.member_price!=undefined&&content.member_price != ""){
			 	str += this.span("原价",content.member_price,content.member_price,"memberPrice");
		 	}else{
		 		str += this.span("原价","-",null,null);
		 	}
		 	str += this.span("审批价",content.item_price==undefined?"":content.item_price,content.item_price==undefined?"":content.item_price,"price");
		 	str += this.span("折扣",content.discount,content.discount,"discount");
		 	str += this.span("平台结算价原价",content.sale_org_price,content.sale_org_price,"saleOrgPrice");
		 	str += this.span("平台结算价特价",content.sale_org_sales_price==''?0:content.sale_org_sales_price,content.sale_org_sales_price==''?0:content.sale_org_sales_price,"saleOrgSalesPrice");
		 	str += this.span("最小开单数量/m2",content.min_quantity,content.min_quantity,"minQuantity");
		 	str += this.span("最大累计数量/m2",content.max_quantity,content.max_quantity,"maxQuantity");
		 	str += this.span("使用数量",content.usage_quantity==undefined?"":content.usage_quantity,null,null);
		 	
			var html = this.row(content.name,str);
			this.itemIndex++;
			this.dom.append(html);
		},
		ech : function(item){
			for (var i = 0; i < item.length; i++) {
				this.add(item[i]);
			}
		},
	};
	
	function findMemberRank(storeId,sbuId){
		$.ajax({
			url: "/b2b/mobile/price_apply/findStoreMemberRank.jhtml",
			type: "POST",
			data: {storeId: storeId,sbuId:sbuId},
			dataType: "json",
			async : true,
			success: function(message) {
				$('#memberRankId').val(message.objx);
			}
		});
	}
	
	//计算折扣
	function getdiscount(e){
        var priceApply=$(e).val(); //审批价
        var productPrice=$('#member_price').text()*1; //原价
        var num=0.00;
        if(productPrice=="0" || !(productPrice!=undefined && productPrice!=null && productPrice!='')){
            num=0.00;
        }else if(productPrice!=undefined && productPrice!=null && productPrice!=''&&priceApply>0){
            num =(priceApply/productPrice)*100;
        }
        $('#discountt').html(currency(num.toFixed(0)));
        $('#discount').val(currency(num.toFixed(0)));
    }
	
	function isNull(value){
		if(isEmpty(value)){
			return "";
		}else{
			return value;
		}
	}
	
	function onShow(e){
        $(e).toggleClass("on")
    }
	
	function count(){
		var priceApplyMaxQuantity = 0;
		var priceApplyAmountQuantity = 0;
		$('.oPro-list').find('.item .data').each(function(index){
			var $this = $(this);
			var memberPrice = $this.find('.memberPrice').val(); //原价
			var price = $this.find('.price').val(); //审批价
			var maxQuantity  = $this.find('.maxQuantity').val(); //最大数量		
			priceApplyAmountQuantity = accAdd(priceApplyAmountQuantity,accMul(accSub(memberPrice,price),maxQuantity));
			priceApplyMaxQuantity = accAdd(priceApplyMaxQuantity,maxQuantity);
		});
		$('#priceApplyMax').html(priceApplyMaxQuantity.toFixed(2));
		$('#priceApplyAmount').html(priceApplyAmountQuantity.toFixed(6));
	}

    //确定支付
    function saveOrder(){
        var flag = confirm("您确定要保存吗？");       
        $.ajax({
            type:'POST',
            url:'/b2b/priceApply/save.jhtml',
            data:$("#inputForm").serialize(),
            success:function(data) {
                alert(data.content);
                if(data.type == "success"){
                    //check_wf();//执行新流程审核方法
                    window.location.href = "/b2b/mobile/price_apply/edit.jhtml?id="+data.objx;
                }
            }
        });   
    }
    
	//表单校验
	function fromValidation(value,flag,msg){
		if(isEmpty(value)){
        	flag = false;
        	alert(msg);
        }
	}
    
    function editPrice(t,e){
        if(extractNumber(t,2,false,e)){
        	getdiscount(t);
            //countTotal();
           // maxquantity();
        }
    }
    
    /* 装载明细对象*/
	function newItem(obj){
		var item = new Object();
		item.shipping_warehouse =  obj.find('#shippingWarehouse').val();		//发货仓
        item.product_category_name = obj.find('#productCategoryName').text();	//产品系列
        item.product_category_id = obj.find('#productCategoryId').text();		//产品系列id
        item.name = obj.find('#productName').text();							//产品名称
        item.id = obj.find('#productId').text();								//产品id
        item.model = obj.find('#model').text(); 								//产品型号
        item.vonder_code = obj.find('#vonderCode').text();						//产品编码         
        item.description = obj.find('#description').text();						//产品描述
        item.product_grade = $('#productGrade').val();							//产品等级
        item.member_price = obj.find('#member_price').text();					//原价
        item.item_price = obj.find('#item_price').val(); 						//审批价
        item.discount = obj.find('#discount').val(); 							//折扣
        item.sale_org_price = obj.find('#sale_org_price').text();				//平台结算价原价
        item.sale_org_sales_price = obj.find('#saleOrgSalesPrice').val();		//平台结算价特价
        item.min_quantity = obj.find('#minQuantity').val();						//最小开单数量/m2
        item.max_quantity = obj.find('#maxQuantity').val();						//最大累计数量/m2
        return item;
	}
    
    //添加附件
    function addAttach(e){
        $('#priceApplyFile').trigger('click');
    }

    //附件上传
    var indexf = 0; 
    function fileUpload(e){
        var formData = new FormData($("#inputForm")[0]);
        $.ajax({
            type:'GET',
            url:'/common/fileurl.jhtml',
            success:function(data) {
                if(data.type == "success"){
                    $.ajax({
                        type:'POST',
                        url: data.objx,
                        data:formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success:function(data_) {
                            data_ = JSON.parse(data_)
                            if(data_.message.type == "success"){
                                var html = "<div class='item'>"
                                    +"<a href='javascript:void(0);' class='ico-del' onclick='itm.del(this)'></a>"
                                    +"<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>"
                                    +"<textarea class='txt' name='twContractAttachs["+indexf+"].memo' placeholder='请输入备注' id='attachMemo'></textarea>"
                                    +"<input type='hidden' id='attachName' name='twContractAttachs["+indexf+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
                                    +"<input type='hidden' id='attachUrl' name='twContractAttachs["+indexf+"].fileUrl' value='"+data_.url+"'/>"
                                    +"<input type='hidden' id='attachSuffix' name='twContractAttachs["+indexf+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
                                    +"</div>";
                                $('.atta-list').append(html);
                                indexf++;
                            }
                        }
                    })
                }
            }
        });
    }

    
</script>
</body>
</html>
