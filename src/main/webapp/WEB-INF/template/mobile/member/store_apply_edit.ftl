<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>客户加盟单查看</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
input[type="number"] {
    -moz-appearance: textfield;
}
.red {
    color: red;
}
label.error {
    color: red;
    font-size: 12px;
    display: block;
    text-align: right;
    margin: -5px 3px 0 0;
}
label.fieldError {
    color: red;
    font-size: 12px;
    display: block;
    text-align: right;
    margin: -5px 3px 0 0;
}
input.er.error {
    border-color: red;
}
input.er.fieldError {
    border-color: red;
}
.info-box .chooseBox label .check_box {
    margin:0 3px 0 5px;
}
.info-box .chooseBox {
    margin-top: 4px;
}
.dl-style01 .txt1 {
    margin: 2px 4px;
    width: 70px;
}
.add-addr {
    right: 13px;
    height: 26px;
    padding: 0 14px;
    border: none;
    color: #fff;
    font-size: 0.875rem;
    display: inline-block;
    float: right;
    line-height: 26px;
    border-radius: 50px;
    margin-top: 7px;
    background: #48A2FE;
}
.card-addr {
    border: 1px solid #e6e6e6;
    margin: 13px;
    border-radius: 10px;
    position: relative;
}
.card-sbu {
    border: 1px solid #e6e6e6;
    margin: 13px;
    border-radius: 10px;
    position: relative;
}
.del-addr {
    position: absolute;
    right: -10px;
    top: -10px;
}
.address-info-head {
    display: flex !important;
    align-items: center;
    padding-left: 10px;
    margin: 0 !important;
}
.hide-address-info-body {
    background-color: #f7f7f7;
}
.hide-address-info-body dl {
    margin: 0;
    min-height: 25px;
    border: none;
}
.hide-address-info-body dt {
    line-height: 25px;
}
.hide-address-info-body input {
    line-height: 25px !important;
    height: 25px !important;
}
</style>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");

    $(this).siblings().find("input[type=radio]").removeAttr("checked")
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
            $(this).find("input[type=checkbox]").val(false)
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true).val(true);
            $(this).find("input[type=checkbox]").val(true)
        }
    }else{

        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find("input[type=radio]").removeAttr("checked")
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked");
        } else {

            $(this).find("input[type=radio]").attr("checked", true)
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

//当前页数
var pageNumber = 1;
// 每页数据量
var pageSize = 25;

//附件index
var attachIndex=0;

var sbuIndex=0;//sbu列表的下标

var store_attach0_length=0;
var store_attach1_length=0;
var store_attach2_length=0;

$().ready(function(){
    var storeAddressList = null
    [#if storeAddressList !=null]
        storeAddressList = ${storeAddressList};

    [/#if]
    if (storeAddressList != null) {
        $.each(storeAddressList, function(index, item){
            $('#hide-address-info').find('.addressText').text(item.address); // 收货人
            $('#hide-address-info').find('input[d-name="consignee"]').attr("name",'storeApplyAddress['+index+'].consignee').attr('value', item.consignee); // 收货人
            $('#hide-address-info').find('input[d-name="mobile"]').attr("name",'storeApplyAddress['+index+'].mobile').attr('value', item.mobile); // 收货人电话
            $('#hide-address-info').find('input[d-name="areaId"]').attr("name",'storeApplyAddress['+index+'].area.id').attr('value', item.area); // 收货地区
            $('#hide-address-info').find('input[d-name="areaFullName"]').attr('value', item.area_full_name); // 收货地区fullname
            $('#hide-address-info').find('input[d-name="salesAreaId"]').attr("name",'storeApplyAddress['+index+'].salesArea.id').attr('value', item.salesAreaId); // 销售区域Id
            $('#hide-address-info').find('input[d-name="salesAreaFullName"]').attr('value', item.sales_area_full_name); // 销售区域fullname
            $('#hide-address-info').find('input[d-name="address"]').attr("name",'storeApplyAddress['+index+'].address').attr('value', item.address); // 收货地址
            $('#hide-address-info').find('input[d-name="zipCode"]').attr("name",'storeApplyAddress['+index+'].zipCode').attr('value', item.zip_code); // 收货地区邮编
            $('#hide-address-info').find('input[d-name="addressType"]').attr("name",'storeApplyAddress['+index+'].addressType').attr('value', item.address_type); // 地址类型
            let aa = {0:"经销商地址" ,1:"收货地址",2:"收单地址",3:"收单收货地址" }
            $('#hide-address-info').find('input[d-name="addressType"]').next().text(aa[item.address_type] ); // 地址类型
            $('#hide-address-info').find('input[d-name="isDefault"]').attr("name",'storeApplyAddress['+index+'].isDefault').attr('value', item.is_default);
            $('#hide-address-info').find('input[d-name="isDefault"]').next().text(item.is_default ? '是':'否'); // 设置
            $("#showAddress").append($('#hide-address-info').html())
            addrIndex++;  //地址表下标的全局变量
        });
    }


    var sbu_json = null
    [#if sbu_json !=null]
        sbu_json = ${sbu_json};
    [/#if]
    if (sbu_json != null) {
        
        $.each(sbu_json, function(index, item){
            [#if store.docStatus ==0 && store.wfId==null]
                $('#hide-sbu').find('input[d-name="memberRankName"]').attr('readOnly',true)
                $('#hide-sbu').find('input[d-name="memberRankName"]').parent().removeAttr('onclick');
            [/#if]

            $('#hide-sbu').find('input[d-name="storeSbuId"]').attr('name', 'storeApplySbu['+index+'].sbu.id').attr('value',item.sbuId); // sbuId
            $('#hide-sbu').find('input[d-name="storeSbuName"]').attr('value',item.sbu_name); // sbuName
            $('#hide-sbu').find('input[d-name="memberRankId"]').attr('name', 'storeApplySbu['+index+'].memberRank.id').attr('value',item.memberRankId); // 价格类型id
            $('#hide-sbu').find('input[d-name="memberRankName"]').addClass('memberRankNameClass').attr('id', 'memberRankNameClass' + index).attr('value',item.memberRank);
            $('#hide-sbu').find('input[dd-name="isDefault"]').attr('name', 'storeApplySbu['+index+'].isDefault').attr('value',item.is_default); // 是否默认
            $('#hide-sbu').find('input[dd-name="isDefault"]').next().text(item.is_default ? '是':'否');
            $('#hide-sbu').find('.sub_check_box').hide()
            $("#sbu_list").append($('#hide-sbu').html())
            sbuIndex++;
        })
    }




    $(".js-cancle").click(function(){
        $(".pup-obox").hide()
        $("body").attr("style","overflow:auto")
        $(".info-btns").show();  // 显示提交按钮
    })

    $(".address-info-head").toggle(function(){
        $(this).next().slideDown(300);
    },function(){
        $(this).next().slideUp(300);
    });
    
    // 表单验证
    $("#inputForm").validate({
        rules: {
        	// 基本信息
        	region: "required",
        	//countryName: "required",
        	dealerName: "required",
        	dealerSex: "required",
        	//dealerGrade: "required",
        	accountTypeCode: "required",
        	name: "required",
        	alias: "required",
            memberRankId: "required",
        	headPhone: {
        		required: true,
                minlength: 11,
                maxlength: 11,
                digits: true
            },
        	// fixedNumber: {
            //     required: true,
            //     digits: true
            // },
        	businessTypeId: "required",
            companyType: "required",
        	//franchisee: "required",
        	distributorStatusId: "required",
        	identity: "required",
        	sbuId: "required",
        	contact: "required",
        	headAddress: "required",
            platformProperty: "required",
            category: "required",
        	// 经销商资质
        	dealerBackground: "required",
        	dealerLevel: "required",
        	salesChannelsVal1: "required",
        	salesChannelsVal2: "required",
        	salesChannelsVal3: "required",
        	salesChannelsVal4: "required",
        	salesChannelsVal5: "required",
        	salesChannelsVal6: "required",
        	marketersNumber: "required",
        	afterSaleNumber: "required",
        	afterSaleNumber: "required",
        	installBodyNumber: "required",
        	warehouseArea: "required",
        	truck: "required",
        	smallCar: "required",
        	// 其他信息
        	//realCautionPaid: "required",
        	//salesDeposit: "required",
        	distributorType: "required",
        	subType: "required",
        	taxRate: "required",
        	governorOpinion: "required",
        	dealerCoding: "required",
        	addArchivesCoding: "required",
        	addTime: "required",
        	dealerCaseNote: "required",
        },
        messages: {
        	// 基本信息
        	region: "请选择区域",
        	//countryName: "请输入乡镇",
        	dealerName: "请输入经销商姓名",
        	dealerSex: "请选择经销商性别",
        	//dealerGrade: "请选择经销商学历",
        	accountTypeCode: "请选择城市等级",
        	name: "请输入客户名称",
        	alias: "请输入客户简称",
            memberRankId: "请选择价格类型",
        	headPhone: {
        		required: "请输入手机号",
                digits: "手机号只能是数字",
                minlength: "手机号长度不能小于11位",
                maxlength: "手机号长度不能大于11位"
            },
        	// fixedNumber: {
            //     required: "请输入固定号码",
            //     digits: "号码只能是数字",
            // },
        	businessTypeId: "请选择业务类型",
        	companyType: "请选择公司性质",
        	//franchisee: "请输入总经销商",
        	distributorStatusId: "请选择经销商状态",
        	identity: "请输入身份证信息",
        	sbuId: "请选择SBU",
        	contact: "请输入法人代表",
        	headAddress: "请输入经销商地址",
            platformProperty: "请选择平台性质",
            category: "请选择经销商类别",
        	// 经销商资质
        	dealerBackground: "请选择经销商背景",
        	dealerLevel: "请选择经销商等级",
        	// 其他信息
        	//realCautionPaid: "请输入实缴品牌保证金",
        	//salesDeposit: "请输入销量保证金",
        	distributorType: "请选择经销商类型",
        	subType: "请选择经销商子类型",
        	taxRate: "请输入税率",
        	governorOpinion: "请输入省长意见",
        	dealerCoding: "请输入经销商授权编号",
        	addArchivesCoding: "请输入新增档案编号",
        	addTime: "请选择新增时间",
        	dealerCaseNote: "请输入经销商情况",
        },
        errorPlacement: function (error, element) { //指定错误信息位置
            if (element.is(':radio') || element.is(':checkbox')) { //如果是radio或checkbox
                var eid = element.attr('name'); //获取元素的name属性
                error.appendTo(element.parent().parent()); //将错误信息添加当前元素的父结点后面
            } else {
            	error.insertAfter(element.parent().parent()); //将错误信息添加当前元素的父结点后面
            }
        }
    })


    
    // 初始化地址
    initAddress('xProvince');
    
    // 初始化应缴品牌保证金
    initNeedCautionPaid();
    
    // 初始化下拉加载
//     initScroll('OrganizationPup');  机构的后台没有使用分页来处理数据，不适用于下拉加载，无需初始化
    initScroll('StoreMemberPup');  // 区域经理
    initScroll('SalesAreaPup');  // 销售区域
//     initScroll('SalesCategoryPup');  // 销售品类，后台问题分页无效
    initScroll('ReceivingAreaPup');  // 收货地址 - 收货地区
    initScroll('AddrSalesAreaPup');  // 收货地址 - 销售区域

    //初始化附件
    <!--  缴纳保证金凭证附件  -->
    [#if  store_attach0!=null]
        var store_attach0 = ${store_attach0}
        store_attach0_length = store_attach0.length;
        for(let i=0; i<store_attach0_length; i++){
            var html = "<div class='item'>"
                +          "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                +           "<div class='tit'>附件 "
                +               "<a href='" + store_attach0[i].url+ "' target='_blank'> <span class='name fr'>"+ store_attach0[i].name+"</span></a>"
                +               "<input type='hidden' name='storeApplyAttachs[" + i + "].url' value='" + store_attach0[i].url + "' >"
                +               "<input type='hidden' name='storeApplyAttachs[" + i + "].name' value='" + store_attach0[i].name + "' >"
                +               "<input type='hidden' name='storeApplyAttachs[" + i + "].suffix' value='" + store_attach0[i].suffix + "' >"
                +               "<input type='hidden' name='storeApplyAttachs[" + i + "].type' value='0'>"
                +            "</div>"
                + "</div>";

            $("#attach0").append(html)
        }
        attachIndex = attachIndex + store_attach0_length ; //所有列表的下表一直往上递增
    [/#if]
    <!--  经销商加盟申请表  -->
    [#if  store_attach1!=null]

    var store_attach1 = ${store_attach1}
    store_attach1_length = store_attach1.length;
    for(let i=0; i<store_attach1_length; i++){
        var html = "<div class='item'>"
            +          "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
            +           "<div class='tit'>附件 "
            +               "<a href='" + store_attach1[i].url+ "' target='_blank'> <span class='name fr'>"+ store_attach1[i].name+"</span></a>"
            +               "<input type='hidden' name='storeApplyAttachs[" + (store_attach0_length+i) + "].url' value=" + store_attach1[i].url + " >"
            +               "<input type='hidden' name='storeApplyAttachs[" + (store_attach0_length+i)  + "].name' value=" + store_attach1[i].name + ">"
            +               "<input type='hidden' name='storeApplyAttachs[" + (store_attach0_length+i)  + "].suffix' value=" + store_attach1[i].suffix + " >"
            +               "<input type='hidden' name='storeApplyAttachs[" + (store_attach0_length+i)  + "].type' value='1'>"
            +               "</div>"
            + "</div>";

        $("#attach1").append(html)
    }
    attachIndex = attachIndex + store_attach1_length ; //所有列表的下标一直往上递增
    [/#if]

    <!--  一城多商  -->
    [#if  store_attach2!=null]

    var store_attach2 = null;
    store_attach2 = ${store_attach2}
    store_attach2_length =  store_attach2.length;
    var myindex = store_attach0_length + store_attach1_length
    for(let i=0; i<store_attach2_length; i++){
        var html = "<div class='item'>"
            +          "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
            +           "<div class='tit'>附件 "
            +               "<a href='" + store_attach2[i].url+ "' target='_blank'> <span class='name fr'>"+ store_attach2[i].name+"</span></a>"
            +               "<input type='hidden' name='storeApplyAttachs[" + (myindex+i)  + "].url' value=" + store_attach2[i].url + " >"

            +               "<input type='hidden' name='storeApplyAttachs[" + (myindex+i)  + "].name' value=" + store_attach2[i].name + ">"
            +               "<input type='hidden' name='storeApplyAttachs[" + (myindex+i)  + "].suffix' value=" + store_attach2[i].suffix + " >"
            +               "<input type='hidden' name='storeApplyAttachs[" + (myindex+i)  + "].type' value='2'>"
            +               "</div>"
            + "</div>";

        $("#attach2").append(html)
    }
    attachIndex = attachIndex + store_attach2_length ; //所有列表的下标一直往上递增
    [/#if]


})

function isNull(str) {
    var a = (str == null || str == "undefined" ) ? "" : str;
    return a;
}

// 初始化应缴品牌保证金
function initNeedCautionPaid() {
	var typeVal = $('#accountTypeCode option:selected').val();
    if (typeVal == "0") {
        $('#needCautionPaid').val("45000");
    } else if (typeVal == "1") {
        $('#needCautionPaid').val("30000");
    } else if (typeVal == "2") {
        $('#needCautionPaid').val("15000");
    } else if (typeVal == "3") {
        $('#needCautionPaid').val("15000");
    }
    $('#needCautionPaid').trigger('input'); 
}

function countCautionPaid(){
    var needCautionPaid = $("#needCautionPaid").val();
    var realCautionPaid = $("#realCautionPaid").val();
    if(isNaN(needCautionPaid)){
        needCautionPaid = 0;
    }
    if(isNaN(realCautionPaid)){
        realCautionPaid = 0;
    }
    $("#unpaidCautionPaid").val(accSub(needCautionPaid,realCautionPaid));
}

function editAccSub(t,e){
    if(extractNumber(t,0,true,e)){
        countCautionPaid();
    }
}

var flag = true;
var sbu_memberRankNameID = ''
var sbu_storeSbuNameID = ''
function showPup(e){
    let memberRankNameClass = $(e).children().find(".memberRankNameClass")
    let memberRankNameId = memberRankNameClass.prevObject[0].id
    if (memberRankNameId != '') {
        sbu_memberRankNameID = memberRankNameId
    }

    let storeSbuNameClass = $(e).children().find(".storeSbuNameClass")
    let storeSbuNameID = storeSbuNameClass.prevObject[1].id
    if (storeSbuNameID != '') {
        sbu_storeSbuNameID = storeSbuNameID
    }

    var id = $(e).attr("data-id");
    if(id=="MemberRankPup"){  //对于选择价格类型要先检验机构必填
        var  saleOrgId = $(".saleOrgId").val();
        if(saleOrgId!=undefined && saleOrgId=="") {
            alert("请先选择机构！")
            return
        }
    }
    pageNumber = 1;
    $("body").attr("style","overflow:hidden");
    $(".pup-obox").hide();
    $(".info-btns").hide();  // 隐藏提交按钮
    $("#"+id).show();
    $('#'+id).find('ul').empty();
    ajaxRequest(id, e);
}

// 记录收货地址中的目标事件
var tergetE;

function ajaxRequest(id, e) {
	// 机构
	if(id == "OrganizationPup") {
        $.ajax({
            type:'POST',
            url:'/basic/saleOrg/select_saleOrg_data.jhtml',
            data:{name:$('#oKeyword').val(), isSellSaleOrg:1, search:1,factoryType:""},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    } 
	// 区域经理
	else if (id == "StoreMemberPup") {
    	$.ajax({
            type:'POST',
            url:'/member/store_member/select_store_member_data.jhtml',
            data:{name:$('#smKeyword').val(), memberType:0, pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    }
	// 销售区域
	else if (id == "SalesAreaPup") {
    	$.ajax({
            type:'POST',
            url:'/basic/sales_area/select_salesArea_data.jhtml',
            data:{name:$('#saKeyword').val(), locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    }
	// 销售品类
	else if (id == "SalesCategoryPup") {
    	$.ajax({
            type:'POST',
            url:'/product/product_category/findTopCategory_data.jhtml',
            data:{name:$('#scKeyword').val(), pageNumber:pageNumber++, pageSize:200},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    }
	// 收货地址 - 收货地区
	else if (id == "ReceivingAreaPup") {
    	$.ajax({
            type:'POST',
            url:'/basic/area/select_area_data.jhtml',
            data:{name:$('#raKeyword').val(), locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
            	tergetE = e;
                fillPupData(data, id);
            }
        })
    }
	// 收货地址 - 销售区域
	else if (id == "AddrSalesAreaPup") {
    	$.ajax({
            type:'POST',
            url:'/basic/sales_area/select_salesArea_data.jhtml',
            data:{name:$('#asaKeyword').val(), locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
            	tergetE = e;
                fillPupData(data, id);
            }
        })
    }
    else if(id == "MemberRankPup"){ //价格类型
        var  saleOrgId = $(".saleOrgId").val();
        $.ajax({
            type:'POST',
            url:'/basic/member_rank/select_memberRank_data.jhtml',
            data:{name:$('#mrKeyword').val(), saleOrgId: saleOrgId,locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {

                tergetE = e;
                fillPupData(data, id);
            }
        })
    }
    else if(id == "ManagementOrganizationPup"){  //经营组织
        $.ajax({
            type:'POST',
            url:'/member/management/list_data.jhtml',
            data:{name:$('#moKeyword').val(), locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
                tergetE = e;
                fillPupData(data, id);
            }
        })
    }
    // 区域经理
    else if (id == "SbuPup") {
        $.ajax({
            type:'POST',
            url:'/basic/sbu/select_sbu_data.jhtml',
            data:{name:$('#sbuKeyword').val(), locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    }
}

/**
 * 初始化下拉加载
 * @param id 弹出框所在盒子的标识id
 */
function initScroll(id) {
    // 获取当前浏览器中的滚动事件 
    $("#" + id).scroll(function() {
        //获取当前目标div的滚动条高度
        var scrollHeight = $("#" + id).prop('scrollHeight');
        //判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
        if (scrollHeight <= ($("#" + id).scrollTop() + $("#" + id).height())) {
           	setTimeout(function () {
	            ajaxRequest(id);
            }, 500);
        }
    });
}

//填充弹出框的数据
function fillPupData(data, id) {
    if(data.type == 'success') {
    	var rows ;
        if (id == "MemberRankPup" ){
            rows = JSON.parse(data.content);
        }else{
            rows = JSON.parse(data.content).content;
        }
    	/*if (id == "OrganizationPup" || id == "ManagementOrganizationPup") {
    		rows = JSON.parse(data.content).content;
    	} else {
    		rows = JSON.parse(data.content);
    	}*/
    	if (rows == null || rows.length == 0) {
            pageNumber--;
            return ;
        }
        // 机构
        if (id == "OrganizationPup") {
	        for (var i = 0; i < rows.length; i++) {
	            var row = rows[i];
	            let item = {
	            		"id": isNull(row.id),
	            		"name": isNull(row.name),
	            		"value": isNull(row.value),
                        "region":isNull(row.region)
	            };
	            let html = "<li data-id='OrganizationPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
	                    + "<div class='fl'><span>机构名称：</span>"+ isNull(row.name) +"</div><br>"
	                    + "<div class='fl'><span>上级机构：</span>"+ isNull(row.tree_path_name) +"</div>"
	                    + "</a></li>";
	            $('#'+id).find('ul').append(html);
	        }
        }
        else if (id == "MemberRankPup") {
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                    "id": isNull(row.id),
                    "name": isNull(row.name), //价格类型名称
                    "grade": isNull(row.grade),//价格类型等级
                };
                let html = "<li data-id='MemberRankPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                    + "<div class='fl'><span>价格类型：</span>"+ isNull(row.name) +"</div><br>"
                    + "<div class='fl'><span>等级：</span>"+ isNull(row.grade) +"</div>"
                    + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        }
        else if (id == "SbuPup") {  //sbu

            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                    "id": isNull(row.id),
                    "name": isNull(row.name), //sbu名称
                    "status": isNull(row.status),//状态
                };

                let html = "<li data-id='SbuPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                    + "<div class='fl'><span>sbu名称：</span>"+ isNull(row.name) +"</div><br>"
                    + "<div class='fl'><span>状态：</span>"+ (isNull(row.status) ?'正常':'弃用')+"</div>"
                    + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        }
        else if (id == "ManagementOrganizationPup") {  //经营组织
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                    "id": isNull(row.id),
                    "name": isNull(row.name), //经营组织名称
                    "code": isNull(row.code),//编码
                };
                let html = "<li data-id='ManagementOrganizationPup' onclick='selectItem(this, " + JSON.stringify(item) + ")'><a href='#'>"
                    + "<div class='fl'><span>经营组织：</span>" + isNull(row.name) + "</div><br>"
                    + "<div class='fl'><span>组织编码：</span>" + isNull(row.code) + "</div>"
                    + "</a></li>";
                $('#' + id).find('ul').append(html);
            }
        }
        else if (id == "StoreMemberPup") {
        	// 区域经理
        	for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "name": isNull(row.name),
                        "mobile": isNull(row.mobile)
                };
                let html = "<li data-id='StoreMemberPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>用户名：</span>"+ isNull(row.username) +"</div><br>"
                        + "<div class='fl'><span>姓名：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>手机：</span>"+ isNull(row.mobile) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        } else if (id == "SalesAreaPup") {
            // 销售区域
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "name": isNull(row.name)
                };
                let html = "<li data-id='SalesAreaPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>地区名称：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>地区全称：</span>"+ isNull(row.full_name) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        } else if (id == "SalesCategoryPup") {
        	// 销售品类
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "name": isNull(row.name)
                };
                let html = "<li data-id='SalesCategoryPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>分类名称：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>外部编号：</span>"+ isNull(row.sn) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        } else if (id == "ReceivingAreaPup") {
            // 收货地址 - 收货地区
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "full_name": isNull(row.full_name)
                };
                let html = "<li data-id='ReceivingAreaPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>地区名称：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>地区全称：</span>"+ isNull(row.full_name) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        } else if (id == "AddrSalesAreaPup") {
            // 收货地址 - 销售区域
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "name": isNull(row.name)
                };
                let html = "<li data-id='AddrSalesAreaPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>地区名称：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>地区全称：</span>"+ isNull(row.full_name) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        }
    } else {
    	pageNumber--;
    }
}

// 搜索机构
function searchOrganization() {
    var e = $('#containt').find('dd[data-id="OrganizationPup"]')[0]
    showPup(e);
}
// 搜索价格类型
function searchMemberRank() {
    var e = $('#containt').find('dd[data-id="MemberRankPup"]')[0]
    showPup(e);
}

// 搜索sbu
function searchSbuData() {
    var e = $('#containt').find('dd[data-id="SbuPup"]')[0]
    showPup(e);
}

// 搜索经营组织
function searchManagementOrganization() {
    var e = $('#containt').find('dd[data-id="ManagementOrganizationPup"]')[0]
    showPup(e);
}

// 搜索区域经理
function searchStoreMember() {
    var e = $('#containt').find('dd[data-id="StoreMemberPup"]')[0]
    showPup(e);
}
// 搜索销售区域
function searchSalesArea() {
    var e = $('#containt').find('dd[data-id="SalesAreaPup"]')[0]
    showPup(e);
}
// 搜索销售品类
function searchSalesCategory() {
    var e = $('#containt').find('dd[data-id="SalesCategoryPup"]')[0]
    showPup(e);
}
// 收货地址 - 收货地区
function searchReceivingArea() {
	pageNumber = 1;
    $('#ReceivingAreaPup').find('ul').empty();
    ajaxRequest("ReceivingAreaPup", tergetE);
}
// 收货地址 - 销售区域
function searchAddrSalesArea() {
	pageNumber = 1;
    $('#AddrSalesAreaPup').find('ul').empty();
    ajaxRequest("AddrSalesAreaPup", tergetE);
}

// 将弹出框选择的数据回显到页面
function selectItem(e, item) {
    var id = $(e).attr("data-id")
    $("body").attr("style", "overflow:auto");
    $(".pup-obox").hide();
    $(".info-btns").show();  // 显示提交按钮
    
    if (id == 'OrganizationPup') { // 机构 
        $('#inputForm').find('input[name="saleOrgName"]').val(item.name);
        $('#inputForm').find('input[name="saleOrgId"]').val(item.id);
        $('#inputForm').find('input[name="salesPlatformId"]').val(item.id);
        $('#inputForm').find('input[name="salesPlatformName"]').val(item.name);
        $(".platformProperty option").each(function() {
            var a = $(this);
            if(a.text() == item.value) {
                a.attr("selected",true);
                $("#inputForm input[name='platformProperty']").val(a.val());
            }
        });
        //处理选择完机构自动带出区域
        var saleOrgRegion = {
        [#list saleOrgRegions as saleOrgRegion]
        ${saleOrgRegion.id}:"${saleOrgRegion.value}",
        [/#list]
    }
        var region = saleOrgRegion[item.region]==null?"":saleOrgRegion[item.region];
        $('#inputForm').find(".region").val(region);
    } else if (id == "ManagementOrganizationPup") { // 经营组织 --------------------------------------

        $(".organizationName").val( $(".organizationName").val()+","+ item.name ) ;//将所选的经营组织名称拼接到已有的值后面
        $("#managementOrganizationBox").append("<input type='hidden' name='organizationIds' value='" +  item.id +"' >" );

    } else if (id == "MemberRankPup") { // 价格类型
        if (sbu_memberRankNameID == '') {
            $('#inputForm').find('input[name="memberRankId"]').val(item.id);
            $('#inputForm').find('input[name="memberRankName"]').val(item.name);
        } else {
            $('#inputForm').find('input[id=' + sbu_memberRankNameID + ']').val(item.name);
            $('#inputForm').find('input[id=' + sbu_memberRankNameID + ']').next().val(item.id);
            sbu_memberRankNameID = '';
        }
    }  else if (id == "StoreMemberPup") { // 区域经理
    	$('#inputForm').find('input[name="storeMemberName"]').val(item.name);
    	$('#inputForm').find('input[name="storeMemberId"]').val(item.id);
    	$('#inputForm').find('input[name="storeMemberPhone"]').val(item.mobile);
    } else if (id == "SalesAreaPup") { // 销售区域
    	$('#inputForm').find('input[name="storeApplySalesAreaName"]').val(item.name);
        $('#inputForm').find('input[name="storeApplySalesAreaId"]').val(item.id);
    } else if (id == "SalesCategoryPup") { // 销售品类
    	$('#inputForm').find('input[name="salesCategory"]').val(item.name);
    } else if (id == "ReceivingAreaPup") { // 收货地址 - 收货地区
        $(tergetE).find('input[d-name="areaId"]').val(item.id);
        $(tergetE).find('input[d-name="receivingAreaVal"]').val(item.full_name);
    } else if (id == "AddrSalesAreaPup") { // 收货地址 - 销售区域
        $(tergetE).find('input[d-name="salesAreaId"]').val(item.id);
        $(tergetE).find('input[d-name="salesAreaVal"]').val(item.name);
    }  else if (id == "SbuPup") { // sbu
        $('#inputForm').find('input[id=' + sbu_storeSbuNameID + ']').val(item.name);
        $('#inputForm').find('input[id=' + sbu_storeSbuNameID + ']').prev().val(item.id);
        sbu_storeSbuNameID = '';
    }
}

// 设置默认的收货地址
function setDefault(e) {
	var $defaultE = $(e).parent().parent().parent().parent().siblings().find('input[d-name="isDefault"]');
	$defaultE.removeAttr("checked");
	$defaultE.next().removeClass("checked");
}


// 文件上传 ----- start -----
// 添加附件
function addAttach(e, attachFile) {
   // $('#fileForm').find('input').removeAttr("name");
    $('#' + attachFile).attr("name", "file");
    $('#' + attachFile).trigger('click'); 
}

/** 
 * 附件上传
 * @param attachId 所在input标签的id名
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param type 后台用来区分不同类型附件
 */
function fileUpload(e, addAttachId, type) {
    var element = $('#fileForm_'+type);
    var formData = new FormData(element[0]);

   /* var formData = new FormData($("#fileForm")[0]);
    var len = $('#'+attachId).find('div[class="item"]').size()
    if (attachId == "addShopAttach" && len >= 2) {
    	alert("附件不能大于两个");
    	return ;
    }*/
    $.ajax({
        type:'GET',
        url:'/common/fileurl.jhtml',
        success:function(data) {
            if(data.type == "success"){
                $.ajax({
                    type:'POST',
                    url: data.objx,
                    data:formData,
                    cache: false,  
                    contentType: false,  
                    processData: false, 
                    success:function(data_) {
                        data_ = JSON.parse(data_)
                        var file_info = data_.file_info
                        if(data_.message.type == "success") {
                            let last_index = file_info.url.lastIndexOf('.');
                            if (last_index >= 0) {
                                file_info.suffix = file_info.url.substring(last_index + 1, file_info.url.length);
                            }
                            var html = "<div class='item'>"
                                + "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                                + "<div class='tit'>附件 "
                                + "<a href='" + file_info.url + "' target='_blank'> <span class='name fr'>" + file_info.name + "</span></a>"
                                + "<input type='hidden' name='storeApplyAttachs[" + attachIndex + "].url' value=" + file_info.url + " >"
                                + "<input type='hidden' name='storeApplyAttachs[" + attachIndex + "].name' value=" + file_info.name + ">"
                                + "<input type='hidden' name='storeApplyAttachs[" + attachIndex + "].suffix' value=" + file_info.suffix + " >"
                                + "<input type='hidden' name='storeApplyAttachs[" + attachIndex + "].type' value=" + type + " >"
                                + "</div>"
                                + "</div>"
                            $('#' + addAttachId).append(html);
                            attachIndex++;
                        }
                    }
                })              
            }
        }
    })
}

// 删除附件
function del(e) {
    if (confirm("您确定要删除吗？")){
        $(e).parent('div').remove()
    }
}
// 文件上传 ----- end -----

// ------ 地址 start ------
// 初始化地址（初始化省）
function initAddress(idVal) {
    var data = {locale:"ChinaCN"};
    fillAddr(data, idVal);
}

/**
 * @param nextId 要初始化的下一级select所在的id值
 */
function initAddr(e, nextId) {
        var pid = $(e).find('option:selected').attr('id-val');
    var data = {locale:"ChinaCN", parentId:pid};
    $(e).parent().prev('input').val(pid);
    // 先清除旧的数据
    $(e).nextAll().html('<option>请选择</option>');
    if (pid == null || pid == '' || pid == 'undefined') {
        // 如果选中的是‘请选择’则修改地址的id为上一级选中的地区id
        if ($(e).index('select') == 1) {
            var idv = $(e).prev().find('option:selected').attr('id-val');
            $(e).parent().prev('input').val(idv);
        }
        return ;
    }
    // 填充新的数据
    fillAddr(data, nextId);
}

// 填充数据
function fillAddr(data, idVal) {
    $.ajax({
        type:'GET',
        url: "/member/index/area.jhtml",
        data: data,
        success:function(dataMap) {
            var optionHtml = "<option>请选择</option>";
            for (var key in dataMap) {
                optionHtml += "<option id-val='"+ key +"'>"+ dataMap[key] +"</option>";
            }
            $('#' + idVal).html(optionHtml);
        }
    })
}

function tailSelect(e) {
    var pid = $(e).find('option:selected').attr('id-val');
    $(e).parent().prev('input').val(pid);
    if (pid == null || pid == '' || pid == 'undefined') {
        // 如果选中的是‘请选择’则修改地址的id为上一级选中的地区id
        var idv = $(e).prev().find('option:selected').attr('id-val');
        $(e).parent().prev('input').val(idv);
    }
}
//------ 地址 end ------

// 添加收货地址
var addrIndex = 0;
function addAddrees(e) {
	$('#hide-address').find('input[d-name="consignee"]').attr('name', 'storeApplyAddress['+addrIndex+'].consignee'); // 收货人
	$('#hide-address').find('input[d-name="mobile"]').attr('name', 'storeApplyAddress['+addrIndex+'].mobile'); // 收货人电话
	$('#hide-address').find('input[d-name="areaId"]').attr('name', 'storeApplyAddress['+addrIndex+'].area.id'); // 收货地区
	$('#hide-address').find('input[d-name="salesAreaId"]').attr('name', 'storeApplyAddress['+addrIndex+'].salesArea.id'); // 销售区域
	$('#hide-address').find('input[d-name="address"]').attr('name', 'storeApplyAddress['+addrIndex+'].address'); // 收货地址
	$('#hide-address').find('input[d-name="zipCode"]').attr('name', 'storeApplyAddress['+addrIndex+'].zipCode'); // 收货地区邮编
	$('#hide-address').find('select[d-name="addressType"]').attr('name', 'storeApplyAddress['+addrIndex+'].addressType'); // 地址类型
	$('#hide-address').find('input[d-name="isDefault"]').attr('name', 'storeApplyAddress['+addrIndex+'].isDefault'); // 设置
	addrIndex++;
	// $(e).parent('div').parent('div').append($('#hide-address').html());
	$("#newAddress").append($('#hide-address').html())
}



//由上面头表的sbu下拉框选择完同步带出下面sbu列表的内容
function showSbuTable() {
    sbuIndex = 0;
    $("#sbu_list").empty()  //清空第一行（虽然也仅有一行）
    var sbuId = $("#sbu option:selected").val();
    var sbuName = $("#sbu option:selected").text();
    var sbuIdMap = ${sbuIdMapStr}
    var id;
    if(sbuIdMap[sbuId]!=null){
        id = sbuIdMap[sbuId];
    }

    $('#hide-sbu').find('input[d-name="storeSbuId"]').attr('name', 'storeApplySbu['+sbuIndex+'].sbu.id').attr('value',id); // sbuId
    $('#hide-sbu').find('input[d-name="storeSbuName"]').attr('value',sbuName); // sbuName
    $('#hide-sbu').find('input[d-name="memberRankId"]').attr('name', 'storeApplySbu['+sbuIndex+'].memberRank.id').attr('value',null);
    $('#hide-sbu').find('input[dd-name="isDefault"]').attr('name', 'storeApplySbu['+sbuIndex+'].isDefault').attr('value',false); // 是否默认
    $('#hide-sbu').find('.sub_span').hide()
    $('#hide-sbu').find('.sub_check_box').show()
    sbuIndex++;
    $("#sbu_list").append($('#hide-sbu').html())

    [#if jinkousancengSbuId!=null]
    if(sbuName=='地板中心'  ){
        id = sbuIdMap[${jinkousancengSbuId}]; //获取进口三层的sbuId（非系统词汇，而是sbu表的sbuId）
        $('#hide-sbu').find('input[d-name="storeSbuId"]').attr('name', 'storeApplySbu['+sbuIndex+'].sbu.id').attr('value',id); // sbuId
        $('#hide-sbu').find('input[d-name="storeSbuName"]').attr('value','进口三层'); // sbuName
        $('#hide-sbu').find('input[d-name="memberRankId"]').attr('name', 'storeApplySbu['+sbuIndex+'].memberRank.id').attr('value',null);
        $('#hide-sbu').find('input[dd-name="isDefault"]').attr('name', 'storeApplySbu['+sbuIndex+'].isDefault').attr('value',false); // 是否默认
        sbuIndex++;
        $("#sbu_list").append($('#hide-sbu').html())
    }
    [/#if]
}

function addSbuItem(e) {
    $('#hide-sbu').find('input[d-name="storeSbuId"]').parent().attr('data-id','SbuPup').attr('onclick','showPup(this)');
    $('#hide-sbu').find('input[d-name="storeSbuId"]').attr('name', 'storeApplySbu['+sbuIndex+'].sbu.id').attr('value',''); // sbuId
    $('#hide-sbu').find('input[d-name="storeSbuName"]').addClass('storeSbuNameClass').addClass('arrow').attr('value','').attr('id', 'storeSbuNameClass' + sbuIndex); // sbuName
    $('#hide-sbu').find('input[dd-name="isDefault"]').attr('name', 'storeApplySbu['+sbuIndex+'].isDefault').attr('value',false); // 是否默认
    $('#hide-sbu').find('input[d-name="memberRankId"]').attr('name', 'storeApplySbu['+sbuIndex+'].memberRank.id').attr('value',null); // 价格类型id
    $('#hide-sbu').find('input[d-name="memberRankName"]').addClass('memberRankNameClass').attr('id', 'memberRankNameClass' + sbuIndex).attr('value',"");
    $('#hide-sbu').find('.sub_span').hide()
    $('#hide-sbu').find('.sub_check_box').show()
    sbuIndex++;
    $("#sbu_list").append($('#hide-sbu').html())
}

//保存操作
function save() {
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    //--------------暂时注释------------------
    /*if (isNull($("#inputForm").find('input[id="headNewArea"]').val()) == ""){
        alert("请重新选择地区");
        return false;
    } */
    else if (isNull($("#inputForm").find('input[name="saleOrgId"]').val()) == "") {
    	alert("请重新选择机构");
    	return false;
    } else if (isNull($("#inputForm").find('input[name="storeMemberId"]').val()) == "") {
        alert("请重新选择区域经理");
        return false;
    } else if (isNull($("#inputForm").find('input[name="storeApplySalesAreaId"]').val()) == "") {
        alert("请重新选择销售区域");
        return false;
    }
    /*else if (isNull($("#inputForm").find('input[name="salesCategory"]').val()) == "") {
        alert("请重新选择销售品类");
        return false;
    } */
    
    var paVal = $('input:radio[name="propagandaAwareness"]:checked').val(); // 宣传意识
    var baVal = $('input:radio[name="brandAwareness"]:checked').val(); // 品牌意识
    var asVal = $('input:radio[name="afterService"]:checked').val(); // 售后服务
    var ccVal = $('input:radio[name="cashClientFlag"]:checked').val(); // 现金客户
    if (isNull(paVal) == "") {
    	alert("请选择宣传意识");
    	return false;
    } else if (isNull(baVal) == "") {
    	alert("请选择品牌意识");
    	return false;
    } else if (isNull(asVal) == "") {
        alert("请选择售后服务");
        return false;
    } else if (isNull(ccVal) == "") {
        alert("请选择是否现金客户");
        return false;
    }
    if ($("#inputForm").valid()) {
        $.ajax({
            type:'POST',
            url:'/member/storeApply/update.jhtml',
            data:$("#inputForm").serialize(),
            success:function(data) {
                if(data.type == 'success'){
                    alert("保存成功");
                    location.reload(true);
                } else {
                    alert("保存失败, " + data.content);
                }
            }
        })
    } else {
        alert("请先输入必填的输入框，再进行提交！");
    }
}

function check_wf() {
    var flag = confirm("，审批发起后无法更改，您确定修改内容已经提交吗？");
    if (flag != true) {
        return ;
    }
    if (isNull($("#inputForm").find('input[name="saleOrgId"]').val()) == "") {
        alert("请重新选择机构");
        return false;
    } else if (isNull($("#inputForm").find('input[name="storeMemberId"]').val()) == "") {
        alert("请重新选择区域经理");
        return false;
    } else if (isNull($("#inputForm").find('input[name="storeApplySalesAreaId"]').val()) == "") {
        alert("请重新选择销售区域");
        return false;
    }
    var paVal = $('input:radio[name="propagandaAwareness"]:checked').val(); // 宣传意识
    var baVal = $('input:radio[name="brandAwareness"]:checked').val(); // 品牌意识
    var asVal = $('input:radio[name="afterService"]:checked').val(); // 售后服务
    var ccVal = $('input:radio[name="cashClientFlag"]:checked').val(); // 现金客户
    if (isNull(paVal) == "") {
        alert("请选择宣传意识");
        return false;
    } else if (isNull(baVal) == "") {
        alert("请选择品牌意识");
        return false;
    } else if (isNull(asVal) == "") {
        alert("请选择售后服务");
        return false;
    } else if (isNull(ccVal) == "") {
        alert("请选择是否现金客户");
        return false;
    }

    if ($("#inputForm").valid()) {
        var objTypeId = 100018;//大自然测试正式
        var modelId = 60141;//大自然测试正式

        var url="/member/storeApply/check_wf.jhtml?id=${store.id}&modelId="+modelId+"&objTypeId="+objTypeId;
        var data = $("#inputForm").serialize();
        $.ajax({
            type:'POST',
            url:url,
            data:data,
            async: true,
            success:function(resultMsg) {
                if (resultMsg.type == "success") {
                    alert("流程已发起！")
                    location.reload(true);
                }
            },
            error:function(){
                alert("流程发起失败")
            }
        });
    } else {
        alert("请先输入必填的输入框，再进行审批！");
    }
}


function saveform(e){
    alert("您确定要提交吗？");
    if (!$("#inputForm").valid()) return;
    //获取表单所有数据
    var params = $("#inputForm").serialize();
    //定义url
    var url = '/member/storeApply/saveform.jhtml?save_type='+e;
    $.ajax({
        type:'POST',
        url:url,
        data:params,
        async: true,
        success:function(data) {
            if(data.type == 'success'){
                alert("保存成功");
                location.reload(true);
            } else {
                alert("保存失败, " + data.content);
            }
        }
    });
}


</script>
</head>
<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
        <input type="hidden" name="id" id="sid" value="${store.id}" />
        <input type="hidden" name="isEnabled" value="true"/>
        <input type="hidden" name = "outTradeNo" value="${store.outTradeNo}"/>
        <div class="info-box">
            <div class="title"><b>基本信息</b><a href="/mobile/member/store_apply_list.jhtml">查看历史记录</a></div>
            <div class="dl-style01">
                <dl class="readonly">
                    <dt>单号</dt>
                    <dd class="txt">${store.sn}</dd>
                </dl>
                <dl class="readonly">
                    <dt>单据状态</dt>
                    <dd class="txt">
                        [#if store.docStatus == "0"]
                        <span>已保存</span>
                        [#elseif store.docStatus == "1"]
                        <span>处理中</span>
                        [#elseif store.docStatus == "2"]
                        <span>已生效</span>
                        [#elseif store.docStatus == "3"]
                        <span>已失效</span>
                        [/#if]
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>区域</dt>
                    <dd>
                        <input type="text" name="region"  class="txt region" value="${store.region}" readonly/>
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>地区</dt>
                    <dd>

                        <input type="hidden" id="headNewArea" name="headNewArea.id" value="${store.headNewArea.id}"/>
                        <div>
                            <select class="txt" id="xProvince" dir="rtl" onchange="initAddr(this, 'xCity')" style="max-width:70px;">
                                <option>${store.headNewArea.parent.parent.name}</option>
                            </select>
                            <select class="txt" id="xCity" dir="rtl" onchange="initAddr(this, 'xRegion')" style="max-width:70px;">
                                <option>${store.headNewArea.parent.name}</option>
                            </select>
                            <select class="txt" id="xRegion" dir="rtl" onchange="tailSelect(this)" style="max-width:70px;">
                                <option>${store.headNewArea.name}</option>
                            </select>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt>乡镇</dt>
                    <dd><input type="text" name="countryName" value="${(store.countryName)!}" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>经销商姓名</dt>
                    <dd><input type="text" name="dealerName" value="${store.dealerName}" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>经销商性别</dt>
                    <dd>
                        <select name="dealerSex" class="txt">
                            <option value=""></option>
	                        <option value="0" [#if store.dealerSex == "0"]selected[/#if]>男</option>
	                        <option value="1" [#if store.dealerSex == "1"]selected[/#if]>女</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>城市等级</dt>
                    <dd>
                        <select name="accountTypeCode" id="accountTypeCode" class="txt" dir="rtl" onchange="initNeedCautionPaid()">
                            <option value="0" [#if store.accountTypeCode == 0] selected="selected"[/#if]>${message("省级")}</option>
                            <option value="1" [#if store.accountTypeCode == 1] selected="selected"[/#if]>${message("地市级")}</option>
                            <option value="2" [#if store.accountTypeCode == 2] selected="selected"[/#if] >${message("区县级")}</option>
                            <option value="3" [#if store.accountTypeCode == 3] selected="selected"[/#if]>${message("乡镇级")}</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>机构</dt>
                    <dd data-id="OrganizationPup" onclick="showPup(this)">
                        <input type="text" name="saleOrgName" class="txt arrow" placeholder="请选择" [#if store.saleOrg??]value="${store.saleOrg.name}"[/#if] readOnly/>
                        <input type="hidden" name="saleOrgId" class="text saleOrgId" value="[#if store.saleOrg??]${store.saleOrg.id}[/#if]" />
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>客户名称</dt>
                    <dd><input type="text" name="name" value="${store.name}" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>手机号</dt>
                    <dd><input type="number" name="headPhone" value="${store.headPhone}" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>经销商学历</dt>
                    <dd>
                        <select name="dealerGrade" class="txt" dir="rtl">
                            <option value="" ></option>
                            <option value="小学" [#if store.dealerGrade == "小学"]selected="selected"[/#if]>${message("小学")}</option>
                            <option value="中学" [#if store.dealerGrade == "中学"]selected="selected"[/#if]>${message("中学")}</option>
                            <option value="大专" [#if store.dealerGrade == "大专"]selected="selected"[/#if]>${message("大专")}</option>
                            <option value="本科" [#if store.dealerGrade == "本科"]selected="selected"[/#if]>${message("本科")}</option>
                            <option value="研究生" [#if store.dealerGrade == "研究生"]selected="selected"[/#if]>${message("研究生")}</option>
                            <option value="硕士" [#if store.dealerGrade == "硕士"]selected="selected"[/#if]>${message("硕士")}</option>
                            <option value="博士" [#if store.dealerGrade == "博士"]selected="selected"[/#if]>${message("博士")}</option>
                            <option value="博士后" [#if store.dealerGrade == "博士后"]selected="selected"[/#if]>${message("博士后")}</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>业务类型</dt>
                    <dd>
                        <select name="businessTypeId" class="txt" dir="rtl">
                            [#list businessTypes as businessType]
                            <option value="${businessType.id}" [#if store.businessType.id==businessType.id] selected[/#if]>${businessType.value}</option>
                            [/#list]
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>客户简称</dt>
                    <dd><input type="text" name="alias" value="${store.alias}" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>固定号码</dt>
                    <dd><input type="number" name="fixedNumber" value="${store.fixedNumber}" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>公司性质</dt>
                    <dd>
                        <select name="companyType" class="txt" dir="rtl">
	                        <option value="0" [#if store.companyType==0]selected[/#if]>独立公司</option>
	                        <option value="1" [#if store.companyType==1]selected[/#if]>合伙公司</option>
	                        <option value="2" [#if store.companyType==2]selected[/#if]>个体工商户</option>
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>总经销商</dt>
                    <dd><input type="text" name="franchisee" value="${store.franchisee}" class="txt" placeholder="请输入" /></dd>
                </dl>
[#--                <dl>--]
[#--                    <dt>经销商状态</dt>--]
[#--                    <dd>--]
[#--                        <select name="distributorStatusId" class="txt" dir="rtl">--]
[#--	                        <option value=""></option>--]
[#--	                        [#list distributorStatus as ds]--]
[#--	                        <option value="${ds.id}">${ds.value}</option>--]
[#--	                        [/#list]--]
[#--	                    </select>--]
[#--                    </dd>--]
[#--                </dl>--]
                <dl>
                    <dt><span class="red">* </span>身份证信息</dt>
                    <dd><input type="number" name="identity" value="${store.identity}" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>平台性质</dt>
                    <dd>
                        <select class="txt platformProperty" name="platformProperty" dir="rtl">
	                        <option value = ""></option>
	                        [#list saleOrgTypes as saleOrgType]
	                        <option value="${saleOrgType.id}" [#if store.platformProperty==saleOrgType.id]selected[/#if]>${saleOrgType.value}</option>
	                        [/#list]
	                    </select>
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>销售平台</dt>
                    <dd>
                        <input type="text" name="salesPlatformName" class="txt"  value="[#if store.salesPlatform??]${store.salesPlatform.name}[/#if]"  readonly="readonly"/>
                        <input type="hidden" name="salesPlatformId" class="txt" btn-fun="clear"  [#if store.salesPlatform??]value="${store.salesPlatform.id}"[/#if]/>
                    </dd>
                </dl>
[#--                <dl>--]
[#--                    <dt>是否可发货</dt>--]
[#--                    <dd class="chooseBox" id="b3">--]
[#--                        <label class="checkhid" data-id="b3" style="display: inline-block;"><input type="radio" name="whetherShip" value="true" checked="checked"/><div class="check_box checked"></div>是</label>&nbsp;&nbsp;--]
[#--                        <label class="checkhid" data-id="b3" style="display: inline-block;"><input type="radio" name="whetherShip" value="false"/><div class="check_box"></div>否</label>--]
[#--                    </dd>--]
[#--                </dl>--]
                <dl>
                    <dt><span class="red">* </span>区域经理</dt>
                    <dd data-id="StoreMemberPup" onclick="showPup(this)">
                        <input type="text" name="storeMemberName" class="txt arrow" [#if "${storeMember}" != 0]value="${storeMember.name}" [#else]value=""[/#if] disabled />
                        <input type="hidden" name="storeMemberId" class="text" [#if "${storeMember}" != 0]value="${storeMember.id}" [#else]value=""[/#if]/>
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>区域经理电话</dt>
                    <dd>
                        <input type="text" class="txt" name="storeMemberPhone" value="${storeMember.member.mobile}" readonly="readonly" />
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>SBU</dt>
                    <dd>
                        <select id="sbu" name="sbuId" class="txt" dir="rtl" onchange="showSbuTable()">
	                        <option value=""></option>
	                        [#list sbus as sbu]
	                        <option value="${sbu.id}" [#if store.sbu.id == sbu.id]selected="selected"[/#if]>${sbu.value}</option>
	                        [/#list]
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>法人代表</dt>
                    <dd><input type="text" class="txt" name="contact" value="${store.contact}" /></dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>销售区域</dt>
                    <dd data-id="SalesAreaPup" onclick="showPup(this)">
                        <input type="text" name="storeApplySalesAreaName" class="txt arrow" [#if store.salesArea??]value="${store.salesArea.name}"[/#if]  />
                        <input type="hidden" name="storeApplySalesAreaId" class="txt" [#if store.salesArea??]value="${store.salesArea.id}"[/#if]/>
                    </dd>
                </dl>
[#--                <dl class="readonly">--]
[#--                    <dt>ERP客户编码</dt>--]
[#--                    <dd>--]
[#--                        <input type="text" class="txt" name="outTradeNo" value="" readonly="readonly" />--]
[#--                    </dd>--]
[#--                </dl>--]
                <dl>
                    <dt>合同主体</dt>
                    <dd><input type="text" name="contractSubject" value="${store.contractSubject}" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>经销商关系说明</dt>
                    <dd>
                        <input type="text" class="txt" name="dealerRelationShip" value="${store.dealerRelationShip}" />
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>价格类型</dt>
                    <dd data-id="MemberRankPup" onclick="showPup(this)">
                        <input type="text" name="memberRankName" class="txt arrow" placeholder="请选择" value="${store.memberRank.name}"  />
                        <input type="hidden" name="memberRankId" class="text memberRankId" value="${store.memberRank.id}"/>
                    </dd>
                </dl>
                <dl>
                    <dt>经销商地址</dt>
                    <dd>
                        <input type="text" class="txt" name="headAddress" value="${store.headAddress}" />
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>经营组织（多选）</dt>
                    <dd data-id="ManagementOrganizationPup" onclick="showPup(this)">
                        <div id = "managementOrganizationBox">
                            <input type="text" name="organizationName" class="txt arrow organizationName"  value="${organizationNames}" disabled />
                            [#list os as o]
                                <input name="organizationIds"  type="hidden" value="${o.id}">
                            [/#list]
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>经销商类别</dt>
                    <dd>
                        <select id="category" name="category" class="txt" dir="rtl">
                            <option></option>
                            <option value="0" [#if store.category == 0] selected="selected"[/#if]>${message("一城多商")}</option>
                            <option value="1" [#if store.category == 1] selected="selected"[/#if]>${message("一城一商")}</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt>经销商属性</dt>
                    <dd>
                        <select id="agentPropertyId" name="agentPropertyId" class="txt" dir="rtl">
                            <option></option>
                            [#list agentProperties as agentProperty]
                                <option value="${agentProperty.id}" [#if store.agentProperty.id==agentProperty.id] selected[/#if]>${agentProperty.value}</option>
                            [/#list]
                        </select>
                    </dd>
                </dl>
                <!-- <dl>
                    <dt>(备注)门店地址</dt>
                    <dd>
                        <input type="text" class="txt" name="shopAddress" value="" />
                    </dd>
                </dl> -->
            </div>
        </div>
        <div class="info-box mt8">
            <div class="title"><b>经销商资质</b></div>
            <div class="dl-style01">
                <dl>
                    <dt>经销商背景</dt>
                    <dd>
                        <select name="dealerBackground" class="txt" dir="rtl">
                            <option value="">---</option>
                            <option value="0" [#if store.dealerBackground==0]selected[/#if]>只经营地板</option>
                            <option value="2" [#if store.dealerBackground==2]selected[/#if]>非建材销售商家</option>
                            <option value="1" [#if store.dealerBackground==1]selected[/#if]>经营多品类品牌</option>

                            <option value="3" [#if store.dealerBackground==3]selected[/#if]>其他</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt>经销商等级</dt>
                    <dd>
                        <select name="dealerLevel" class="txt" dir="rtl">
                            <option value=""></option>
                            <option value="0" [#if store.dealerLevel==0]selected[/#if]>资金雄厚，有建材经验</option>
                            <option value="1" [#if store.dealerLevel==1]selected[/#if]>资金一般，有潜力</option>
                            <option value="2" [#if store.dealerLevel==2]selected[/#if]>资金紧凑，可扶持</option>
                            <option value="3" [#if store.dealerLevel==3]selected[/#if]>其他</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt>宣传意识</dt>
                    <dd class="chooseBox" id="b4">
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="0" [#if store.propagandaAwareness==0]checked[/#if]/><div class="check_box [#if store.propagandaAwareness==0]checked[/#if] "></div>优</label>
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="1" [#if store.propagandaAwareness==1]checked[/#if]/><div class="check_box [#if store.propagandaAwareness==1]checked[/#if]"></div>良</label>
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="2" [#if store.propagandaAwareness==2]checked[/#if]/><div class="check_box [#if store.propagandaAwareness==2]checked[/#if]"></div>一般</label>
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="3" [#if store.propagandaAwareness==3]checked[/#if]/><div class="check_box [#if store.propagandaAwareness==3]checked[/#if]"></div>差</label>
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="4" [#if store.propagandaAwareness==4]checked[/#if]/><div class="check_box [#if store.propagandaAwareness==4]checked[/#if]"></div>无</label>
                    </dd>
                </dl>
                <dl>
                    <dt>品牌意识</dt>
                    <dd class="chooseBox" id="b5">
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="0" [#if store.brandAwareness==0]checked[/#if]/><div class="check_box [#if store.brandAwareness==0]checked[/#if]"></div>优</label>
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="1" [#if store.brandAwareness==1]checked[/#if]/><div class="check_box [#if store.brandAwareness==1]checked[/#if]"></div>良</label>
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="2" [#if store.brandAwareness==2]checked[/#if]/><div class="check_box [#if store.brandAwareness==2]checked[/#if]"></div>一般</label>
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="3" [#if store.brandAwareness==3]checked[/#if]/><div class="check_box [#if store.brandAwareness==3]checked[/#if]"></div>差</label>
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="4" [#if store.brandAwareness==4]checked[/#if]/><div class="check_box [#if store.brandAwareness==4]checked[/#if]"></div>无</label>
                    </dd>
                </dl>
                <dl>
                    <dt>售后服务</dt>
                    <dd class="chooseBox" id="b6">
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="0" [#if store.afterService==0]checked[/#if]/><div class="check_box [#if store.afterService==0]checked[/#if]"></div>优</label>
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="1" [#if store.afterService==1]checked[/#if]/><div class="check_box [#if store.afterService==1]checked[/#if]"></div>良</label>
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="2" [#if store.afterService==2]checked[/#if]/><div class="check_box [#if store.afterService==2]checked[/#if]"></div>一般</label>
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="3" [#if store.afterService==3]checked[/#if]/><div class="check_box [#if store.afterService==3]checked[/#if]"></div>差</label>
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="4" [#if store.afterService==4]checked[/#if]/><div class="check_box [#if store.afterService==4]checked[/#if]"></div>无</label>
                    </dd>
                </dl>
                <dl>
                    <dt>销售渠道</dt>
                    <dd>
                        <label>
	                        <span>零售</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal1" value="${store.salesChannelsVal1}"/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>装饰公司</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal2" value="${store.salesChannelsVal2}"/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>电商</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal3" value="${store.salesChannelsVal3}"/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>工程</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal4" value="${store.salesChannelsVal4}"/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>促销活动</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal5" value="${store.salesChannelsVal5}"/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>其他</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal6" value="${store.salesChannelsVal6}"/>
	                        <span>%</span>&nbsp;
	                    </label>
                    </dd>
                </dl>
                <dl>
                    <dt>人员配置</dt>
                    <dd>
                        <label>
		                    <span>营销人员</span>
		                    <input class="txt1 er" name="marketersNumber" value="${store.marketersNumber}" type="number" />
		                    <span>名</span>
	                    </label><br/>
	                    <label>
		                    <span>售后人员</span>
		                    <input class="txt1 er" name="afterSaleNumber" value="${store.afterSaleNumber}" type="number" />
		                    <span>名</span>
	                    </label><br/>
	                    <label>
		                    <span>安装工人</span>
		                    <input class="txt1 er" name="installBodyNumber" value="${store.installBodyNumber}" type="number" />
		                    <span>名</span>
	                    </label><br/>
	                    <label style="padding-right: 4px;">
		                    <span>仓库面积</span>
		                    <input class="txt1 er" name="warehouseArea" value="${store.warehouseArea}" type="number" />
		                    <span>㎡</span>
	                    </label>
                    </dd>
                </dl>
                <dl>
                    <dt>门店设施</dt>
                    <dd>
                        <label>
	                        <span>车辆配置：货车</span>
		                    <input class="txt1 er" name="truck" value="${store.truck}" type="number"/>
		                    <span>辆</span>
	                    </label><br/>
	                    <label>
		                    <span>小车</span>
		                    <input class="txt1 er" name="smallCar" value="${store.smallCar}" type="number"/>
		                    <span>辆</span>
	                    </label><br/>
	                    <div class="chooseBox" id="b7" style="margin:0; padding:0;">
	                        <span>电脑/宽带：</span>
	                        <label class="checkhid" data-id="b7" style="display: inline-block; margin: 3px 0 4px;">
	                            <input type="radio" name="pcOrBroadband" value="1" checked="[#if store.pcOrBroadband==1]checked[/#if]"/>
	                            <div class="check_box [#if store.pcOrBroadband==1]checked[/#if]"></div>有
	                        </label>&nbsp;
	                        <label class="checkhid" data-id="b7" style="display: inline-block; margin: 3px 0 4px;">
	                            <input type="radio" name="pcOrBroadband" value="0" [#if store.pcOrBroadband==0]checked[/#if]/>
	                            <div class="check_box [#if store.pcOrBroadband==0]checked[/#if]"></div>没有
	                        </label>&nbsp;
	                    </div>
                    </dd>
                </dl>
[#--                <dl>--]
[#--                    <dt>加盟手续</dt>--]
[#--                    <dd style="margin: 0 0 3px;">--]
[#--	                    <div class="chooseBox" id="b8" style="margin: 5px 0 0;">--]
[#--	                        <label class="checkhid" data-id="b8" style="display: inline-block; margin: 3px 0 4px;">--]
[#--                                <input type="radio" name="joiningFormalities1" value="1" />--]
[#--                                <div class="check_box"></div>是--]
[#--                            </label>&nbsp;--]
[#--                            <label class="checkhid" data-id="b8" style="display: inline-block; margin: 3px 0 4px;">--]
[#--                                <input type="radio" name="joiningFormalities1" value="0" checked="checked" />--]
[#--                                <div class="check_box checked"></div>否--]
[#--                            </label>--]
[#--	                    </div>--]
[#--	                    <span>区域经理已建立CRM帐户（详见《关于linkCRM系统经销商信息命名规则》）</span>--]
[#--	                    <div class="chooseBox" id="b9" style="margin: 5px 0 0px;">--]
[#--	                        <label class="checkhid" data-id="b9" style="display: inline-block; margin: 3px 0 4px;">--]
[#--                                <input type="radio" name="joiningFormalities2" value="1" />--]
[#--                                <div class="check_box"></div>是--]
[#--                            </label>&nbsp;--]
[#--                            <label class="checkhid" data-id="b9" style="display: inline-block; margin: 3px 0 4px;">--]
[#--                                <input type="radio" name="joiningFormalities2" value="0" checked="checked" />--]
[#--                                <div class="check_box checked"></div>否--]
[#--                            </label>--]
[#--	                    </div>--]
[#--	                    <span>已缴纳品牌保证金（汇款凭证提供渠道部，TEL:0757-22916305）</span>--]
[#--                    </dd>--]
[#--                </dl>--]
                <dl class="dl-style-textarea">
                    <dt>客户说明</dt>
                    <dd><textarea class="txt" name="introduction" placeholder="请输入">${store.introduction}</textarea></dd>
                </dl>

                [#--                ------------------第一个附件表（start）---------------------------------------]
                <dl class="upload-bBox">
                    <dt>&emsp;提供经销商缴纳保证金凭证 </dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'addAttach0')">上传</a>
                    </dd>
                </dl>
                <div class="atta-list" id="attach0"></div>
                [#--                ------------------第一个附件表（end）---------------------------------------]

                [#--                ------------------第二个附件表（start）---------------------------------------]
                <dl class="upload-bBox">
                    <dt>&emsp;经销商加盟申请表 </dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'addAttach1')">上传</a>
                    </dd>
                </dl>
                <div class="atta-list" id="attach1"></div>
                [#--                ------------------第二个附件表（end）---------------------------------------]

                [#--                ------------------第三个附件表（start）---------------------------------------]
                <dl class="upload-bBox">
                    <dt>&emsp;一城多商 </dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'addAttach2')">上传</a>
                    </dd>
                </dl>
                <div class="atta-list" id="attach2"></div>
                [#--                ------------------第三个附件表（end）---------------------------------------]




                [#--                <dl class="upload-bBox">--]
[#--                    <dt>&emsp;上传附件<br/>（最多两个）</dt>--]
[#--                    <dd>--]
[#--                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'shopFile')">上传</a>--]
[#--                    </dd>--]
[#--                </dl>--]
[#--                <div class="atta-list" id="addShopAttach"></div>--]
            </div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>其他信息</b></div>
            <div class="dl-style01">
                <dl class="readonly">
                    <dt>应缴品牌保证金</dt>
                    <dd><input type="number" class="txt" name="needCautionPaid" id="needCautionPaid" value="${store.needCautionPaid}" readonly oninput="editAccSub(this,event)" onpropertychange="editAccSub(this,event)" /></dd>
                </dl>
                <dl>
                    <dt>实缴品牌保证金</dt>
                    <dd><input type="number" class="txt" name="realCautionPaid" id="realCautionPaid" value="${store.realCautionPaid}" oninput="editAccSub(this,event)" onpropertychange="editAccSub(this,event)" /></dd>
                </dl>
                <dl class="readonly">
                    <dt>欠缴品牌保证金</dt>
                    <dd><input type="number" class="txt" name="unpaidCautionPaid" id="unpaidCautionPaid" value="" readonly="readonly" /></dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>现金客户</dt>
                    <dd class="chooseBox" id="b10">
                        <label class="checkhid" data-id="b10" style="display: inline-block;"><input type="radio" name="cashClientFlag" value="true" [#if store.cashClientFlag == true]checked[/#if]/><div class="check_box [#if store.cashClientFlag == true]checked[/#if]"></div>是</label>&nbsp;
                        <label class="checkhid" data-id="b10" style="display: inline-block;"><input type="radio" name="cashClientFlag" value="false" [#if store.cashClientFlag == false]checked[/#if]/><div class="check_box [#if store.cashClientFlag == false]checked[/#if]"></div>否</label>
                    </dd>
                </dl>
                <dl>
                    <dt>货币</dt>
                    <dd>
                        <select name="currencyCode" class="txt">
	                        <option value="人民币" >${message("人民币")}</option>
	                    </select>
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>门店数量</dt>
                    <dd class="txt">
                        <span id="numberStores" name="ShopInfoCount">${ShopInfoCount}</span>
                    </dd>
                </dl>
                <dl>
                    <dt>销量保证金</dt>
                    <dd><input type="number" class="txt" name="salesDeposit" value="${store.salesDeposit}" /></dd>
                </dl>
[#--                <dl>--]
[#--                    <dt>销售品类</dt>--]
[#--                    <dd data-id="SalesCategoryPup" onclick="showPup(this)">--]
[#--                        <input type="text" name="salesCategory" class="txt arrow" value="" disabled />--]
[#--                    </dd>--]
[#--                </dl>--]
                <dl>
                    <dt>经销商类型</dt>
                    <dd>
                        <select name="distributorType" class="txt" dir="rtl">
                            <option value="0" [#if store.distributorType == 0]selected="selected"[/#if] >${message("国内经销商")}</option>
                            <option value="1" [#if store.distributorType == 1]selected="selected"[/#if] >${message("国际经销商")}</option>
                            <option value="2" [#if store.distributorType == 2]selected="selected"[/#if]>${message("国产产品经销商")}</option>
                            <option value="3" [#if store.distributorType == 3]selected="selected"[/#if]>${message("进口产品经销商")}</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt><span class="red">* </span>经销商子类型</dt>
                    <dd>
                        <select name="subType" class="txt" dir="rtl">
                            <option value="" >${message("请选择")}</option>
                            <option value="0" [#if store.subType == 0]selected="selected"[/#if]>${message("总经销商")}</option>
                            <option value="1" [#if store.subType == 1]selected="selected"[/#if]>${message("省会城市经销商")}</option>
                            <option value="3" [#if store.subType == 3]selected="selected"[/#if]>${message("经销商")}</option>
                            <option value="4" [#if store.subType == 4]selected="selected"[/#if]>${message("分销商")}</option>
                            <option value="5" [#if store.subType == 5]selected="selected"[/#if]>${message("乡镇运营商")}</option>
                            <option value="6" [#if store.subType == 6]selected="selected"[/#if]>${message("工程代理商")}</option>
                            <option value="7" [#if store.subType == 7]selected="selected"[/#if]>${message("一城多商")}</option>
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>税率</dt>
                    <dd><input type="number" class="txt" name="taxRate" value="[#if store.taxRate!=null]${store.taxRate*100}[/#if]" />&nbsp;%</dd>
                </dl>
                <dl class="dl-style-textarea">
                    <dt>缴纳情况/异常说明</dt>
                    <dd><textarea class="txt" name="paymentStatus" placeholder="请输入">${store.paymentStatus}</textarea></dd>
                </dl>
                <dl style="display:none;">
                    <select name="store_type">
                        [#list types as value]
                        <option value="${value}" [#if value==4]selected[/#if]>${message('11111111'+value)}</option>
                        [/#list]
                    </select>
                </dl>
            </div>
        </div>
        
        <!-- 添加收货地址 -->
        <div class="info-box mt8" style="padding-bottom: 1px;">
            <div class="title">
                <b>收货地址</b>
                [#if store.docStatus ==0 && store.wfId==null]
                <input type="button" class="add-addr" value="添加" onclick="addAddrees(this)"/>
                [/#if]
            </div>
            <div id="showAddress"></div>
            <div id="newAddress"></div>
        </div>

        <!-- sbu列表-->
        <div class="info-box mt8" style="padding-bottom: 1px;">
            <div class="title"><b>sbu列表</b> [#if node.name?contains("跟单")]<span class="add-addr" onclick="saveform(3)">提交</span>  <span class="add-addr" style="margin-right: 20px;" onclick="addSbuItem(this)">添加sbu</span>[/#if]</div>
            <div id = "sbu_list">

            </div>
        </div>

        [#if store.wfId!=null ]
            [#if szsh||szshs]
            <div class="info-box mt8">
                <div class="title"><b>省长意见</b>   [#if node.name?contains("省长")]<span class="add-addr" onclick="saveform(1)">提交</span>[/#if]  </div>
                <div class="dl-style01">
                    <dl>
                        <dd><textarea class="txt" name="governorOpinion" placeholder="请输入">${store.governorOpinion}</textarea></dd>
                    </dl>
                </div>
            </div>
            [/#if]
            [#if qdzy||qdzys]
                <div class="info-box mt8">
                    <div class="title"><b>渠道部意见</b> [#if node.name?contains("渠道")]<span class="add-addr" onclick="saveform(2)">提交</span>[/#if]</div>
                    <div class="dl-style01">
                        <dl>
                            <dt>是否核心经销商</dt>
                            <dd class="chooseBox" id="c1">
                                <label class="checkhid" data-id="c1" style="display: inline-block;"><input type="radio" name="isCoreStroe" value="true" [#if store.isCoreStroe]checked[/#if]/><div class="check_box [#if store.isCoreStroe]checked[/#if] "></div>是</label>
                                <label class="checkhid" data-id="c1"  style="display: inline-block;"><input type="radio" name="isCoreStroe" value="false" [#if !store.isCoreStroe]checked[/#if]/><div class="check_box [#if !store.isCoreStroe]checked[/#if] "></div>否</label>
                            </dd>
                        </dl>
                        <dl>
                            <dt>经销商类别</dt>
                            <dd>
                                <select id="category" name="category1" class="txt" dir="rtl">
                                    <option></option>
                                    <option value="0" [#if store.category == 0] selected="selected"[/#if]>${message("一城多商")}</option>
                                    <option value="1" [#if store.category == 1] selected="selected"[/#if]>${message("一城一商")}</option>
                                </select>
                            </dd>
                        </dl>
                        <dl>
                            <dt>是否可发货</dt>
                            <dd class="chooseBox" id="c2">
                                <label class="checkhid" data-id="c2" style="display: inline-block;"><input type="radio" name="shippingState" value="1" [#if store.shippingState]checked[/#if]/><div class="check_box [#if store.shippingState]checked[/#if] "></div>是</label>
                                <label class="checkhid" data-id="c2"  style="display: inline-block;"><input type="radio" name="shippingState" value="0" [#if !store.shippingState]checked[/#if]/><div class="check_box [#if !store.shippingState]checked[/#if] "></div>否</label>
                            </dd>
                        </dl>

                        <dl>
                            <dt>经销商授权编号</dt>
                            <dd><input type="text" name="dealerCoding" class="txt" placeholder="请输入" value="${store.dealerCoding}"/></dd>
                        </dl>
                        <dl>
                            <dt>新增档案编号</dt>
                            <dd><input type="text" name="addArchivesCoding" class="txt" placeholder="请输入" value="${store.addArchivesCoding}"/></dd>
                        </dl>
                        <dl>
                            <dt>加盟时间</dt>
                            <dd><input type="date" class="txt time" name="activeDate" btn-fun="clear" value="${store.activeDate}"/></dd>
                        </dl>
                        <dl>
                            <dt>是否加盟成功</dt>
                            <dd class="chooseBox" id="c3">
                                <label class="checkhid" data-id="c3" style="display: inline-block;"><input type="radio" name="applyResult" value="1" [#if store.applyResult]checked[/#if]/><div class="check_box [#if store.applyResult]checked[/#if] "></div>是</label>
                                <label class="checkhid" data-id="c3"  style="display: inline-block;"><input type="radio" name="applyResult" value="0" [#if !store.applyResult]checked[/#if]/><div class="check_box [#if !store.applyResult]checked[/#if] "></div>否</label>
                            </dd>
                        </dl>
                    </div>
                </div>

                <div class="info-box mt8">
                    <div class="title"><b>经销商情况</b></div>
                    <div class="dl-style01">
                        <dl class="dl-style-textarea">
                            <dt>备注</dt>
                            <dd><textarea class="txt" name="dealerCaseNote" placeholder="请输入">${store.dealerCaseNote}</textarea></dd>
                        </dl>
                    </div>
                </div>
            [/#if]
        [/#if]
    </form>

    <!-- 文件上传 -->
    <form id="fileForm_0">   [#-- 表1--]
        <input type="file" name="file" id="addAttach0" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'attach0', 0)" style="display: none"/>
    </form>
    <form id="fileForm_1">   [#-- 表2--]
        <input type="file" name="file" id="addAttach1" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'attach1', 1)" style="display: none"/>
    </form>
    <form id="fileForm_2">   [#-- 表3--]
        <input type="file" name="file" id="addAttach2" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'attach2', 2)" style="display: none"/>
    </form>
    
    <div class="h50"></div>
    [#if store.docStatus ==0 && store.wfId==null]  //保存状态下才显示此按钮
    <div class="info-btns">
        <input type="button" value="提交" class="btn-blue btn" onclick="save()" style="width: 40%"/>
        <input type="button" value="流程审批" class="btn-blue btn" onclick="check_wf()" style="width: 40%"/>
    </div>
    [/#if]
</div>

<!-- ***************** 隐藏数据 ***************** -->
<div id="hide-address" style="display:none;">
	<div class="dl-style01 card-addr">
	    <a href='javascript:void(0);' class='ico-del del-addr' onclick='del(this)'></a>
	    <dl>
	        <dt>收货人</dt>
	        <dd><input type="text" name="" d-name="consignee" value="" class="txt" /></dd>
	    </dl>
	    <dl>
	        <dt>收货人电话</dt>
	        <dd><input type="text" name="" d-name="mobile" value="" class="txt" /></dd>
	    </dl>
	    <dl>
	        <dt>收货地区</dt>
	        <dd data-id="ReceivingAreaPup" onclick="showPup(this)">
	            <input type="hidden" name="" d-name="areaId" value="" class="txt" />
	            <input type="text" name="" d-name="receivingAreaVal" value="" class="txt arrow" disabled="disabled"/>
	        </dd>
	    </dl>
	    <dl>
	        <dt>销售区域</dt>
	        <dd data-id="AddrSalesAreaPup" onclick="showPup(this)">
	            <input type="hidden" name="" d-name="salesAreaId" value="" class="txt" />
	            <input type="text" name="" d-name="salesAreaVal" value="" class="txt arrow" disabled="disabled"/>
	        </dd>
	    </dl>
	    <dl>
	        <dt><span class="red">* </span>收货地址</dt>
	        <dd><input type="text" name="" d-name="address" value="" class="txt" /></dd>
	    </dl>
	    <dl>
	        <dt>收货地区邮编</dt>
	        <dd><input type="text" name="" d-name="zipCode" value="" class="txt" /></dd>
	    </dl>
	    <dl>
	        <dt>地址类型</dt>
	        <dd>
	            <select name="" d-name="addressType" dir="rtl" class="txt">
	                <option value="0" >经销商地址</option>
	                <option value="1">收货地址</option>
	                <option value="2">收单地址</option>
	                <option value="3" selected="selected">收单收货地址</option>
	            </select>
	        </dd>
	    </dl>
	    <dl>
	        <dt>设置</dt>
	        <dd class="chooseBox" id="b20">
                <label class="checkhid" data-id="b20" style="display: inline-block;">
	                <input type="checkbox" name="" d-name="isDefault" value="true" />
	                <div class="check_box"></div>是否默认
                </label>
            </dd>
	    </dl>
	</div>
</div>

<div id="hide-address-info" style="display:none;">
    <div class="dl-style01 card-addr">
        <a href='javascript:void(0);'  class='ico-del del-addr'    onclick='del(this)' ></a>
        <dl class="address-info-head">
            <dd class="addressText"></dd>
        </dl>
        <div class="hide-address-info-body" style="display: none">
            <dl>
                <dt>收货人</dt>
                <dd><input type="text" d-name="consignee" value="" class="txt" /></dd>
            </dl>
            <dl>
                <dt>收货人电话</dt>
                <dd><input type="text" d-name="mobile" value="" class="txt" /></dd>
            </dl>
            <dl>
                <dt>收货地区</dt>
                <dd data-id="ReceivingAreaPup">
                    <input type="hidden" d-name="areaId" value="" class="txt" />
                    <input type="text" d-name="areaFullName" value="" class="txt"/>
                </dd>
            </dl>
            <dl>
                <dt>销售区域</dt>
                <dd data-id="AddrSalesAreaPup">
                    <input type="hidden" d-name="salesAreaId" value="" class="txt" />
                    <input type="text" d-name="salesAreaFullName" value="" class="txt"/>
                </dd>
            </dl>
            <dl>
                <dt>收货地区邮编</dt>
                <dd><input type="text" d-name="zipCode" value="" class="txt" /></dd>
            </dl>
            <dl>
                <dt>收货地址</dt>
                <dd><input type="text" d-name="address" value="" class="txt" /></dd>
            </dl>
            <dl>
                <dt>地址类型</dt>
                <dd>
                    <input type="hidden" d-name="addressType" value="" class="txt" />
                    <span style="line-height: 25px"></span>
                </dd>
            </dl>
            <dl>
                <dt>是否默认</dt>
                <dd>
                    <input type="hidden" d-name="isDefault" value="" class="txt" />
                    <span style="line-height: 25px;"></span>
                </dd>
            </dl>
        </div>
</div>


<!-- ***************** sbu列表隐藏表 ***************** -->
<div id="hide-sbu" style="display:none;">
    <div class="dl-style01 card-sbu">
        <a href='javascript:void(0);' ></a>
        <dl>
            <dt>sbu</dt>
            <dd>
                <input type="hidden" name="" d-name="storeSbuId" value="" class="txt" />
                <input type="text" name="" d-name="storeSbuName" value="" class="txt " disabled="disabled"/>
            </dd>
        </dl>
        <dl>
            <dt>价格类型</dt>
            <dd data-id="MemberRankPup" onclick="showPup(this)">
                <input type="text" d-name="memberRankName" class="txt   [#if store.wfId!=null] arrow[/#if]   "  value=""  />
                <input type="hidden" d-name="memberRankId" class="text memberRankId" value=""/>
            </dd>
        </dl>
        <dl>
            <dt>是否默认</dt>
            <dd class="chooseBox" id="b20">
                <label class="checkhid" data-id="b20" style="display: inline-block;">
                    <input type="checkbox"  dd-name="isDefault" value=""/>
                    <span class="sub_span" style="line-height: 25px;"></span>
                    <block class="sub_check_box"><div class="check_box"></div>是否默认</block>
                </label>
            </dd>

        </dl>
    </div>
</div>

<!-- ***************** 选择弹出框 ***************** -->
<!-- 选择机构 -->
<div class="pup-obox" id="OrganizationPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            [#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择机构</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="机构名称" id="oKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchOrganization(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>

<!--选择价格类型-->
<div class="pup-obox" id="MemberRankPup" style="z-index: 2;">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            [#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择价格类型</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="价格类型名称" id="mrKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchMemberRank(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>

<!--选择经营组织-->
<div class="pup-obox" id="ManagementOrganizationPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            [#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择经营组织</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="经营组织名称名称" id="moKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchManagementOrganization(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>

<!-- 选择区域经理 -->
<div class="pup-obox" id="StoreMemberPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
           [#-- <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择区域经理</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="姓名" id="smKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchStoreMember(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 选择sbu -->
<div class="pup-obox" id="SbuPup" style="z-index: 2;">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            [#-- <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择sbu</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="sbu名称" id="sbuKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchSbuData(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>

<!-- 选择销售区域 -->
<div class="pup-obox" id="SalesAreaPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            [#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择销售区域</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="地区名称" id="saKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchSalesArea(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 选择销售品类 -->
<div class="pup-obox" id="SalesCategoryPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            [#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择销售品类</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="分类名称" id="scKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchSalesCategory(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 收货地址 - 收货地区 -->
<div class="pup-obox" id="ReceivingAreaPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            [#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择收货地区</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="地区名称" id="raKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchReceivingArea(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 收货地址 - 销售区域 -->
<div class="pup-obox" id="AddrSalesAreaPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            [#--<a href="javascript:history.back(-1);" class="go-back js-cancle"></a>--]
            <div class="h-txt">请选择销售区域</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="地区名称" id="asaKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchAddrSalesArea(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
</body>
</html>
