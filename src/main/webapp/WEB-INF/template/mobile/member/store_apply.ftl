<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>客户加盟申请表</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
input[type="number"] {
    -moz-appearance: textfield;
}
label.error {
    color: red;
    font-size: 12px;
    display: block;
    text-align: right;
    margin: -5px 3px 0 0;
}
label.fieldError {
    color: red;
    font-size: 12px;
    display: block;
    text-align: right;
    margin: -5px 3px 0 0;
}
input.er.error {
    border-color: red;
}
input.er.fieldError {
    border-color: red;
}
.info-box .chooseBox label .check_box {
    margin:0 3px 0 5px;
}
.info-box .chooseBox {
    margin-top: 4px;
}
.dl-style01 .txt1 {
    margin: 2px 4px;
    width: 70px;
}
.add-addr {
    right: 13px;
    height: 26px;
    padding: 0 14px;
    border: none;
    color: #fff;
    font-size: 0.875rem;
    display: inline-block;
    float: right;
    line-height: 26px;
    border-radius: 50px;
    margin-top: 7px;
    background: #48A2FE;
}
.card-addr {
    border: 1px solid #e6e6e6;
    margin: 13px;
    border-radius: 10px;
    position: relative;
}
.del-addr {
    position: absolute;
    right: -10px;
    top: -10px;
}
</style>
<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    }else{
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

//当前页数
var pageNumber = 1;
// 每页数据量
var pageSize = 25;

$().ready(function(){
    $(".js-cancle").click(function(){
        $(".pup-obox").hide()
        $("body").attr("style","overflow:auto")
        $(".info-btns").show();  // 显示提交按钮
    })
    
    // 表单验证
    $("#inputForm").validate({
        rules: {
        	// 基本信息
        	region: "required",
        	countryName: "required",
        	dealerName: "required",
        	dealerSex: "required",
        	dealerGrade: "required",
        	accountTypeCode: "required",
        	name: "required",
        	alias: "required",
        	headPhone: {
        		required: true,
                minlength: 11,
                maxlength: 11,
                digits: true
            },
        	fixedNumber: {
                required: true,
                digits: true
            },
        	businessTypeId: "required",
        	companyType: "required",
        	franchisee: "required",
        	distributorStatusId: "required",
        	identity: "required",
        	sbuId: "required",
        	contact: "required",
        	headAddress: "required",
        	// 经销商资质
        	dealerBackground: "required",
        	dealerLevel: "required",
        	salesChannelsVal1: "required",
        	salesChannelsVal2: "required",
        	salesChannelsVal3: "required",
        	salesChannelsVal4: "required",
        	salesChannelsVal5: "required",
        	salesChannelsVal6: "required",
        	marketersNumber: "required",
        	afterSaleNumber: "required",
        	afterSaleNumber: "required",
        	installBodyNumber: "required",
        	warehouseArea: "required",
        	truck: "required",
        	smallCar: "required",
        	// 其他信息
        	realCautionPaid: "required",
        	salesDeposit: "required",
        	distributorType: "required",
        	subType: "required",
        	taxRate: "required",
        	governorOpinion: "required",
        	dealerCoding: "required",
        	addArchivesCoding: "required",
        	addTime: "required",
        	dealerCaseNote: "required",
        },
        messages: {
        	// 基本信息
        	region: "请选择区域",
        	countryName: "请输入乡镇",
        	dealerName: "请输入经销商姓名",
        	dealerSex: "请选择经销商性别",
        	dealerGrade: "请选择经销商学历",
        	accountTypeCode: "请选择城市等级",
        	name: "请输入客户名称",
        	alias: "请输入客户简称",
        	headPhone: {
        		required: "请输入手机号",
                digits: "手机号只能是数字",
                minlength: "手机号长度不能小于11位",
                maxlength: "手机号长度不能大于11位"
            },
        	fixedNumber: {
                required: "请输入固定号码",
                digits: "号码只能是数字",
            },
        	businessTypeId: "请选择业务类型",
        	companyType: "请选择公司性质",
        	franchisee: "请输入总经销商",
        	distributorStatusId: "请选择经销商状态",
        	identity: "请输入身份证信息",
        	sbuId: "请选择SBU",
        	contact: "请输入法人代表",
        	headAddress: "请输入经销商地址",
        	// 经销商资质
        	dealerBackground: "请选择经销商背景",
        	dealerLevel: "请选择经销商等级",
        	// 其他信息
        	realCautionPaid: "请输入实缴品牌保证金",
        	salesDeposit: "请输入销量保证金",
        	distributorType: "请选择经销商类型",
        	subType: "请选择经销商子类型",
        	taxRate: "请输入税率",
        	governorOpinion: "请输入省长意见",
        	dealerCoding: "请输入经销商授权编号",
        	addArchivesCoding: "请输入新增档案编号",
        	addTime: "请选择新增时间",
        	dealerCaseNote: "请输入经销商情况",
        },
        errorPlacement: function (error, element) { //指定错误信息位置
            if (element.is(':radio') || element.is(':checkbox')) { //如果是radio或checkbox
                var eid = element.attr('name'); //获取元素的name属性
                error.appendTo(element.parent().parent()); //将错误信息添加当前元素的父结点后面
            } else {
            	error.insertAfter(element.parent().parent()); //将错误信息添加当前元素的父结点后面
            }
        }
    })
    
    // 初始化地址
    initAddress('xProvince');
    
    // 初始化应缴品牌保证金
    initNeedCautionPaid();
    
    // 初始化下拉加载
//     initScroll('OrganizationPup');  机构的后台没有使用分页来处理数据，不适用于下拉加载，无需初始化
    initScroll('StoreMemberPup');  // 区域经理
    initScroll('SalesAreaPup');  // 销售区域
//     initScroll('SalesCategoryPup');  // 销售品类，后台问题分页无效
    initScroll('ReceivingAreaPup');  // 收货地址 - 收货地区
    initScroll('AddrSalesAreaPup');  // 收货地址 - 销售区域
})

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

// 初始化应缴品牌保证金
function initNeedCautionPaid() {
	var typeVal = $('#accountTypeCode option:selected').val();
    if (typeVal == "0") {
        $('#needCautionPaid').val("45000");
    } else if (typeVal == "1") {
        $('#needCautionPaid').val("30000");
    } else if (typeVal == "2") {
        $('#needCautionPaid').val("15000");
    } else if (typeVal == "3") {
        $('#needCautionPaid').val("15000");
    }
    $('#needCautionPaid').trigger('input'); 
}

function countCautionPaid(){
    var needCautionPaid = $("#needCautionPaid").val();
    var realCautionPaid = $("#realCautionPaid").val();
    if(isNaN(needCautionPaid)){
        needCautionPaid = 0;
    }
    if(isNaN(realCautionPaid)){
        realCautionPaid = 0;
    }
    $("#unpaidCautionPaid").val(accSub(needCautionPaid,realCautionPaid));
}

function editAccSub(t,e){
    if(extractNumber(t,0,true,e)){
        countCautionPaid();
    }
}

function showPup(e){
    var id = $(e).attr("data-id");
    pageNumber = 1;
    $("body").attr("style","overflow:hidden");
    $(".pup-obox").hide();
    $(".info-btns").hide();  // 隐藏提交按钮
    $("#"+id).show();
    $('#'+id).find('ul').empty();
    ajaxRequest(id, e);
}

// 记录收货地址中的目标事件
var tergetE;

function ajaxRequest(id, e) {
	// 机构
	if(id == "OrganizationPup") {
        $.ajax({
            type:'POST',
            url:'/basic/saleOrg/select_saleOrg_data.jhtml',
            data:{name:$('#oKeyword').val(), isSellSaleOrg:1, search:1,factoryType:""},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    } 
	// 区域经理
	else if (id == "StoreMemberPup") {
    	$.ajax({
            type:'POST',
            url:'/member/store_member/select_store_member_data.jhtml',
            data:{name:$('#smKeyword').val(), memberType:0, pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    }
	// 销售区域
	else if (id == "SalesAreaPup") {
    	$.ajax({
            type:'POST',
            url:'/basic/sales_area/select_salesArea_data.jhtml',
            data:{name:$('#saKeyword').val(), locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    }
	// 销售品类
	else if (id == "SalesCategoryPup") {
    	$.ajax({
            type:'POST',
            url:'/product/product_category/findTopCategory_data.jhtml',
            data:{name:$('#scKeyword').val(), pageNumber:pageNumber++, pageSize:200},
            success:function(data) {
                fillPupData(data, id);
            }
        })
    }
	// 收货地址 - 收货地区
	else if (id == "ReceivingAreaPup") {
    	$.ajax({
            type:'POST',
            url:'/basic/area/select_area_data.jhtml',
            data:{name:$('#raKeyword').val(), locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
            	tergetE = e;
                fillPupData(data, id);
            }
        })
    }
	// 收货地址 - 销售区域
	else if (id == "AddrSalesAreaPup") {
    	$.ajax({
            type:'POST',
            url:'/basic/sales_area/select_salesArea_data.jhtml',
            data:{name:$('#asaKeyword').val(), locale:"zh_CN", pageNumber:pageNumber++, pageSize:pageSize},
            success:function(data) {
            	tergetE = e;
                fillPupData(data, id);
            }
        })
    }
}

/**
 * 初始化下拉加载
 * @param id 弹出框所在盒子的标识id
 */
function initScroll(id) {
    // 获取当前浏览器中的滚动事件 
    $("#" + id).scroll(function() {
        //获取当前目标div的滚动条高度
        var scrollHeight = $("#" + id).prop('scrollHeight');
        //判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
        if (scrollHeight <= ($("#" + id).scrollTop() + $("#" + id).height())) {
           	setTimeout(function () {
	            ajaxRequest(id);
            }, 500);
        }
    });
}

//填充弹出框的数据
function fillPupData(data, id) {
    if(data.type == 'success') {
    	var rows ;
    	if (id == "OrganizationPup") {
    		rows = JSON.parse(data.content);
    	} else {
    		rows = JSON.parse(data.content).content;
    	}
    	if (rows == null || rows.length == 0) {
            pageNumber--;
            return ;
        }
        // 机构
        if (id == "OrganizationPup") {
	        for (var i = 0; i < rows.length; i++) {
	            var row = rows[i];
	            let item = {
	            		"id": isNull(row.id),
	            		"name": isNull(row.name),
	            		"value": isNull(row.value)
	            };
	            let html = "<li data-id='OrganizationPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
	                    + "<div class='fl'><span>机构名称：</span>"+ isNull(row.name) +"</div><br>"
	                    + "<div class='fl'><span>上级机构：</span>"+ isNull(row.tree_path_name) +"</div>"
	                    + "</a></li>";
	            $('#'+id).find('ul').append(html);
	        }
        } else if (id == "StoreMemberPup") {
        	// 区域经理
        	for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "name": isNull(row.name),
                        "mobile": isNull(row.mobile)
                };
                let html = "<li data-id='StoreMemberPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>用户名：</span>"+ isNull(row.username) +"</div><br>"
                        + "<div class='fl'><span>姓名：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>手机：</span>"+ isNull(row.mobile) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        } else if (id == "SalesAreaPup") {
            // 销售区域
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "name": isNull(row.name)
                };
                let html = "<li data-id='SalesAreaPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>地区名称：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>地区全称：</span>"+ isNull(row.full_name) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        } else if (id == "SalesCategoryPup") {
        	// 销售品类
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "name": isNull(row.name)
                };
                let html = "<li data-id='SalesCategoryPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>分类名称：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>外部编号：</span>"+ isNull(row.sn) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        } else if (id == "ReceivingAreaPup") {
            // 收货地址 - 收货地区
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "full_name": isNull(row.full_name)
                };
                let html = "<li data-id='ReceivingAreaPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>地区名称：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>地区全称：</span>"+ isNull(row.full_name) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        } else if (id == "AddrSalesAreaPup") {
            // 收货地址 - 销售区域
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                let item = {
                        "id": isNull(row.id),
                        "name": isNull(row.name)
                };
                let html = "<li data-id='AddrSalesAreaPup' onclick='selectItem(this, "+ JSON.stringify(item) +")'><a href='#'>"
                        + "<div class='fl'><span>地区名称：</span>"+ isNull(row.name) +"</div><br>"
                        + "<div class='fl'><span>地区全称：</span>"+ isNull(row.full_name) +"</div>"
                        + "</a></li>";
                $('#'+id).find('ul').append(html);
            }
        }
    } else {
    	pageNumber--;
    }
}

// 搜索机构
function searchOrganization() {
    var e = $('#containt').find('dd[data-id="OrganizationPup"]')[0]
    showPup(e);
}
// 搜索区域经理
function searchStoreMember() {
    var e = $('#containt').find('dd[data-id="StoreMemberPup"]')[0]
    showPup(e);
}
// 搜索销售区域
function searchSalesArea() {
    var e = $('#containt').find('dd[data-id="SalesAreaPup"]')[0]
    showPup(e);
}
// 搜索销售品类
function searchSalesCategory() {
    var e = $('#containt').find('dd[data-id="SalesCategoryPup"]')[0]
    showPup(e);
}
// 收货地址 - 收货地区
function searchReceivingArea() {
	pageNumber = 1;
    $('#ReceivingAreaPup').find('ul').empty();
    ajaxRequest("ReceivingAreaPup", tergetE);
}
// 收货地址 - 销售区域
function searchAddrSalesArea() {
	pageNumber = 1;
    $('#AddrSalesAreaPup').find('ul').empty();
    ajaxRequest("AddrSalesAreaPup", tergetE);
}

// 将弹出框选择的数据回显到页面
function selectItem(e, item) {
    var id = $(e).attr("data-id")
    $("body").attr("style", "overflow:auto");
    $(".pup-obox").hide();
    $(".info-btns").show();  // 显示提交按钮
    
    if (id == 'OrganizationPup') { // 机构 
        $('#inputForm').find('input[name="saleOrgName"]').val(item.name);
        $('#inputForm').find('input[name="saleOrgId"]').val(item.id);
        $('#inputForm').find('input[name="salesPlatformId"]').val(item.id);
        $('#inputForm').find('input[name="salesPlatformName"]').val(item.name);
        $(".platformProperty option").each(function() {
            var a = $(this);
            if(a.text() == item.value) {
                a.attr("selected",true);
                $("#inputForm input[name='platformProperty']").val(a.val());
            }
        });
    } else if (id == "StoreMemberPup") { // 区域经理
    	$('#inputForm').find('input[name="storeMemberName"]').val(item.name);
    	$('#inputForm').find('input[name="storeMemberId"]').val(item.id);
    	$('#inputForm').find('input[name="storeMemberPhone"]').val(item.mobile);
    } else if (id == "SalesAreaPup") { // 销售区域
    	$('#inputForm').find('input[name="storeApplySalesAreaName"]').val(item.name);
        $('#inputForm').find('input[name="storeApplySalesAreaId"]').val(item.id);
    } else if (id == "SalesCategoryPup") { // 销售品类
    	$('#inputForm').find('input[name="salesCategory"]').val(item.name);
    } else if (id == "ReceivingAreaPup") { // 收货地址 - 收货地区
        $(tergetE).find('input[d-name="areaId"]').val(item.id);
        $(tergetE).find('input[d-name="receivingAreaVal"]').val(item.full_name);
    } else if (id == "AddrSalesAreaPup") { // 收货地址 - 销售区域
        $(tergetE).find('input[d-name="salesAreaId"]').val(item.id);
        $(tergetE).find('input[d-name="salesAreaVal"]').val(item.name);
    }
}

// 设置默认的收货地址
function setDefault(e) {
	var $defaultE = $(e).parent().parent().parent().parent().siblings().find('input[d-name="isDefault"]');
	$defaultE.removeAttr("checked");
	$defaultE.next().removeClass("checked");
}

// 文件上传 ----- start -----
// 添加附件
function addAttach(e, attachFile) {
    $('#fileForm').find('input').removeAttr("name");
    $('#' + attachFile).attr("name", "file");
    $('#' + attachFile).trigger('click'); 
}

/** 
 * 附件上传
 * @param attachId 所在input标签的id名
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param type 后台用来区分不同类型附件
 */
function fileUpload(e, attachId, paramAttachs, type) {
    var formData = new FormData($("#fileForm")[0]);
    var len = $('#'+attachId).find('div[class="item"]').size()
    if (attachId == "addShopAttach" && len >= 2) {
    	alert("附件不能大于两个");
    	return ;
    }
    $.ajax({
        type:'GET',
        url:'/common/fileurl.jhtml',
        success:function(data) {
            if(data.type == "success"){
                $.ajax({
                    type:'POST',
                    url: data.objx,
                    data:formData,
                    cache: false,  
                    contentType: false,  
                    processData: false, 
                    success:function(data_) {
                        data_ = JSON.parse(data_)
                        if(data_.message.type == "success"){
                            var html = "<div class='item'>"
                                    + "<a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                                    + "<div class='tit'>附件 <a href='"+data_.url+"' target='_blank'><span class='name fr'>"+data_.file_info.name+"</span></a></div>";
                            var imageVal =  $('#inputForm').find('input[name="storeImage"]').val();
                            var imageVal2 =  $('#inputForm').find('input[name="storeImage2"]').val();
                            if (isNull(imageVal) == "") {
                            	html += "<input type='hidden' id='attachUrl' name='storeImage' value='"+data_.url+"'/>";
                            } else if (isNull(imageVal2) == "") {
                            	html += "<input type='hidden' id='attachUrl' name='storeImage2' value='"+data_.url+"'/>";
                            }
                                html += "</div>";
                            $('#'+attachId).append(html);
                        }
                    }
                })              
            }
        }
    })
}

// 删除附件
function del(e) {
    $(e).parent('div').remove()
}
// 文件上传 ----- end -----

// ------ 地址 start ------
// 初始化地址（初始化省）
function initAddress(idVal) {
    var data = {locale:"ChinaCN"};
    fillAddr(data, idVal);
}

/**
 * @param nextId 要初始化的下一级select所在的id值
 */
function initAddr(e, nextId) {
    var pid = $(e).find('option:selected').attr('id-val');
    var data = {locale:"ChinaCN", parentId:pid};
    $(e).parent().prev('input').val(pid);
    // 先清除旧的数据
    $(e).nextAll().html('<option>请选择</option>');
    if (pid == null || pid == '' || pid == 'undefined') {
        // 如果选中的是‘请选择’则修改地址的id为上一级选中的地区id
        if ($(e).index('select') == 1) {
            var idv = $(e).prev().find('option:selected').attr('id-val');
            $(e).parent().prev('input').val(idv);
        }
        return ;
    }
    // 填充新的数据
    fillAddr(data, nextId);
}

// 填充数据
function fillAddr(data, idVal) {
    $.ajax({
        type:'GET',
        url: "/member/index/area.jhtml",
        data: data,
        success:function(dataMap) {
            var optionHtml = "<option>请选择</option>";
            for (var key in dataMap) {
                optionHtml += "<option id-val='"+ key +"'>"+ dataMap[key] +"</option>";
            }
            $('#' + idVal).html(optionHtml);
        }
    })
}

function tailSelect(e) {
    var pid = $(e).find('option:selected').attr('id-val');
    $(e).parent().prev('input').val(pid);
    if (pid == null || pid == '' || pid == 'undefined') {
        // 如果选中的是‘请选择’则修改地址的id为上一级选中的地区id
        var idv = $(e).prev().find('option:selected').attr('id-val');
        $(e).parent().prev('input').val(idv);
    }
}
//------ 地址 end ------

// 添加收货地址
var addrIndex = 0;
function addAddrees(e) {
	$('#hide-address').find('input[d-name="consignee"]').attr('name', 'storeApplyAddress['+addrIndex+'].consignee'); // 收货人
	$('#hide-address').find('input[d-name="mobile"]').attr('name', 'storeApplyAddress['+addrIndex+'].mobile'); // 收货人电话
	$('#hide-address').find('input[d-name="areaId"]').attr('name', 'storeApplyAddress['+addrIndex+'].area.id'); // 收货地区
	$('#hide-address').find('input[d-name="salesAreaId"]').attr('name', 'storeApplyAddress['+addrIndex+'].salesArea.id'); // 销售区域
	$('#hide-address').find('input[d-name="address"]').attr('name', 'storeApplyAddress['+addrIndex+'].address'); // 收货地址
	$('#hide-address').find('input[d-name="zipCode"]').attr('name', 'storeApplyAddress['+addrIndex+'].zipCode'); // 收货地区邮编
	$('#hide-address').find('select[d-name="addressType"]').attr('name', 'storeApplyAddress['+addrIndex+'].addressType'); // 地址类型
	$('#hide-address').find('input[d-name="isDefault"]').attr('name', 'storeApplyAddress['+addrIndex+'].isDefault'); // 设置
	addrIndex++;
	$(e).parent('div').parent('div').append($('#hide-address').html());
}

//保存操作
function save() {
    var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    if (isNull($("#inputForm").find('input[id="headNewArea"]').val()) == ""){
        alert("请重新选择地区");
        return false;
    } else if (isNull($("#inputForm").find('input[name="saleOrgId"]').val()) == "") {
    	alert("请重新选择机构");
    	return false;
    } else if (isNull($("#inputForm").find('input[name="storeMemberId"]').val()) == "") {
        alert("请重新选择区域经理");
        return false;
    } else if (isNull($("#inputForm").find('input[name="storeApplySalesAreaId"]').val()) == "") {
        alert("请重新选择销售区域");
        return false;
    } else if (isNull($("#inputForm").find('input[name="salesCategory"]').val()) == "") {
        alert("请重新选择销售品类");
        return false;
    } 
    
    var paVal = $('input:radio[name="propagandaAwareness"]:checked').val(); // 宣传意识
    var baVal = $('input:radio[name="brandAwareness"]:checked').val(); // 品牌意识
    var asVal = $('input:radio[name="afterService"]:checked').val(); // 售后服务
    var ccVal = $('input:radio[name="cashClientFlag"]:checked').val(); // 现金客户
    if (isNull(paVal) == "") {
    	alert("请选择宣传意识");
    	return false;
    } else if (isNull(baVal) == "") {
    	alert("请选择品牌意识");
    	return false;
    } else if (isNull(asVal) == "") {
        alert("请选择售后服务");
        return false;
    } else if (isNull(ccVal) == "") {
        alert("请选择是否现金客户");
        return false;
    }
    if ($("#inputForm").valid()) {
        $.ajax({
            type:'POST',
            url:'/member/storeApply/save.jhtml',
            data:$("#inputForm").serialize(),
            success:function(data) {
                if(data.type == 'success'){
                    alert("保存成功");
                } else {
                    alert("保存失败, " + data.content);
                }
            }
        })
    } else {
        alert("请先输入必填的输入框，再进行提交！");
    }
}
</script>
</head>
<body>
<div id="containt">
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
        <div class="info-box">
            <div class="title"><b>基本信息</b><a href="#">查看历史记录</a></div>
            <div class="dl-style01">
                <dl>
                    <dt>区域</dt>
                    <dd>
                        <select class="txt region" name="region">
	                        <option value="东区">${message("东区")}</option>
	                        <option value="西区">${message("西区")}</option>
	                        <option value="南区">${message("南区")}</option>
	                        <option value="北区">${message("北区")}</option>
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>地区</dt>
                    <dd>
                        <input type="hidden" id="headNewArea" name="headNewArea.id" />
                        <div>
                            <select class="txt" id="xProvince" dir="rtl" onchange="initAddr(this, 'xCity')" style="max-width:70px;">
                                <option>请选择</option>
                            </select>
                            <select class="txt" id="xCity" dir="rtl" onchange="initAddr(this, 'xRegion')" style="max-width:70px;">
                                <option>请选择</option>
                            </select>
                            <select class="txt" id="xRegion" dir="rtl" onchange="tailSelect(this)" style="max-width:70px;">
                                <option>请选择</option>
                            </select>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt>乡镇</dt>
                    <dd><input type="text" name="countryName" value="" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>经销商姓名</dt>
                    <dd><input type="text" name="dealerName" value="" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>经销商性别</dt>
                    <dd>
                        <select name="dealerSex" class="txt">
                            <option value=""></option>
	                        <option value="0">男</option>
	                        <option value="1">女</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt>经销商学历</dt>
                    <dd>
                        <select name="dealerGrade" class="txt" dir="rtl">
                        <option value="" ></option>
                        <option value="小学">${message("小学")}</option>
                        <option value="中学">${message("中学")}</option>
                        <option value="大专">${message("大专")}</option>
                        <option value="本科">${message("本科")}</option>
                        <option value="研究生">${message("研究生")}</option>
                        <option value="硕士">${message("硕士")}</option>
                        <option value="博士">${message("博士")}</option>
                        <option value="博士后">${message("博士后")}</option>
                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>城市等级</dt>
                    <dd>
                        <select name="accountTypeCode" id="accountTypeCode" class="txt" dir="rtl" onchange="initNeedCautionPaid()">
	                        <option value="0">${message("省级")}</option>
	                        <option value="1">${message("地市级")}</option>
	                        <option value="2" selected="selected" >${message("区县级")}</option>
	                        <option value="3">${message("乡镇级")}</option>
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>客户名称</dt>
                    <dd><input type="text" name="name" value="" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>客户简称</dt>
                    <dd><input type="text" name="alias" value="" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>手机号</dt>
                    <dd><input type="number" name="headPhone" value="" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>固定号码</dt>
                    <dd><input type="number" name="fixedNumber" value="" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>业务类型</dt>
                    <dd>
                        <select name="businessTypeId" class="txt" dir="rtl">
	                        [#list businessTypes as businessType]
	                        <option value="${businessType.id}">${businessType.value}</option>
	                        [/#list]
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>公司性质</dt>
                    <dd>
                        <select name="companyType" class="txt" dir="rtl">
	                        <option value="0">独立公司</option>
	                        <option value="1">合伙公司</option>
	                        <option value="2">个体工商户</option>
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>总经销商</dt>
                    <dd><input type="text" name="franchisee" value="" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>经销商状态</dt>
                    <dd>
                        <select name="distributorStatusId" class="txt" dir="rtl">
	                        <option value=""></option>
	                        [#list distributorStatus as ds]
	                        <option value="${ds.id}">${ds.value}</option>
	                        [/#list]
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>身份证信息</dt>
                    <dd><input type="number" name="identity" value="" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>机构</dt>
                    <dd data-id="OrganizationPup" onclick="showPup(this)">
                        <input type="text" name="saleOrgName" class="txt arrow" placeholder="请选择" value="" disabled />
                        <input type="hidden" name="saleOrgId" class="text saleOrgId" value=""/>
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>平台性质</dt>
                    <dd>
                        <select class="txt platformProperty" disabled="disabled" dir="rtl">
	                        <option value = ""></option>
	                        [#list saleOrgTypes as saleOrgType]
	                        <option value="${saleOrgType.id}">${saleOrgType.value}</option>
	                        [/#list]
	                    </select>
	                    <input type="hidden" name="platformProperty" value="" class="platformProperty" />
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>销售平台</dt>
                    <dd>
                        <input type="text" name="salesPlatformName" class="txt" value="" readonly="readonly"/>
                        <input type="hidden" name="salesPlatformId" class="txt" btn-fun="clear" value=""/>
                    </dd>
                </dl>
                <dl>
                    <dt>是否可发货</dt>
                    <dd class="chooseBox" id="b3">
                        <label class="checkhid" data-id="b3" style="display: inline-block;"><input type="radio" name="whetherShip" value="true" checked="checked"/><div class="check_box checked"></div>是</label>&nbsp;&nbsp;
                        <label class="checkhid" data-id="b3" style="display: inline-block;"><input type="radio" name="whetherShip" value="false"/><div class="check_box"></div>否</label>
                    </dd>
                </dl>
                <dl>
                    <dt>区域经理</dt>
                    <dd data-id="StoreMemberPup" onclick="showPup(this)">
                        <input type="text" name="storeMemberName" class="txt arrow" [#if "${storeMember}" != 0]value="${storeMember.name}" [#else]value=""[/#if] disabled />
                        <input type="hidden" name="storeMemberId" class="text" [#if "${storeMember}" != 0]value="${storeMember.id}" [#else]value=""[/#if]/>
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>区域经理电话</dt>
                    <dd>
                        <input type="text" class="txt" name="storeMemberPhone" value="${storeMember.member.mobile}" readonly="readonly" />
                    </dd>
                </dl>
                <dl>
                    <dt>SBU</dt>
                    <dd>
                        <select id="sbu" name="sbuId" class="txt" dir="rtl">
	                        <option value=""></option>
	                        [#list sbus as sbu]
	                        <option value="${sbu.id}">${sbu.value}</option>
	                        [/#list]
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>法人代表</dt>
                    <dd><input type="text" class="txt" name="contact" value="" /></dd>
                </dl>
                <dl>
                    <dt>销售区域</dt>
                    <dd data-id="SalesAreaPup" onclick="showPup(this)">
                        <input type="text" name="storeApplySalesAreaName" class="txt arrow" value="" disabled />
                        <input type="hidden" name="storeApplySalesAreaId" class="txt" value=""/>
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>ERP客户编码</dt>
                    <dd>
                        <input type="text" class="txt" name="outTradeNo" value="" readonly="readonly" />
                    </dd>
                </dl>
                <dl>
                    <dt>经销商关系说明</dt>
                    <dd>
                        <input type="text" class="txt" name="dealerRelationShip" value="" />
                    </dd>
                </dl>
                <dl>
                    <dt>经销商地址</dt>
                    <dd>
                        <input type="text" class="txt" name="headAddress" value="" />
                    </dd>
                </dl>
                <dl>
                    <dt>(备注)门店地址</dt>
                    <dd>
                        <input type="text" class="txt" name="shopAddress" value="" />
                    </dd>
                </dl>
            </div>
        </div>
        <div class="info-box mt8">
            <div class="title"><b>经销商资质</b></div>
            <div class="dl-style01">
                <dl>
                    <dt>经销商背景</dt>
                    <dd>
                        <select name="dealerBackground" class="txt" dir="rtl">
                            <option value=""></option>
                            <option value="0">只经营地板</option>
                            <option value="1">经营多类建材品牌</option>
                            <option value="2">非建材销售商家</option>
                            <option value="3">其他</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt>经销商等级</dt>
                    <dd>
                        <select name="dealerLevel" class="txt" dir="rtl">
                            <option value=""></option>
                            <option value="0">资金雄厚，有建材经验</option>
                            <option value="1">资金一般，有潜力</option>
                            <option value="2">资金紧凑，可扶持</option>
                            <option value="3">其他</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt>宣传意识</dt>
                    <dd class="chooseBox" id="b4">
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="0" /><div class="check_box"></div>优</label>
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="1" /><div class="check_box"></div>良</label>
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="2" /><div class="check_box"></div>一般</label>
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="3" /><div class="check_box"></div>差</label>
	                    <label class="checkhid" data-id="b4" style="display: inline-block;"><input type="radio" name="propagandaAwareness" value="4" /><div class="check_box"></div>无</label>
                    </dd>
                </dl>
                <dl>
                    <dt>品牌意识</dt>
                    <dd class="chooseBox" id="b5">
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="0" /><div class="check_box"></div>优</label>
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="1" /><div class="check_box"></div>良</label>
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="2" /><div class="check_box"></div>一般</label>
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="3" /><div class="check_box"></div>差</label>
                        <label class="checkhid" data-id="b5" style="display: inline-block;"><input type="radio" name="brandAwareness" value="4" /><div class="check_box"></div>无</label>
                    </dd>
                </dl>
                <dl>
                    <dt>售后服务</dt>
                    <dd class="chooseBox" id="b6">
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="0" /><div class="check_box"></div>优</label>
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="1" /><div class="check_box"></div>良</label>
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="2" /><div class="check_box"></div>一般</label>
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="3" /><div class="check_box"></div>差</label>
                        <label class="checkhid" data-id="b6" style="display: inline-block;"><input type="radio" name="afterService" value="4" /><div class="check_box"></div>无</label>
                    </dd>
                </dl>
                <dl>
                    <dt>销售渠道</dt>
                    <dd>
                        <label>
	                        <span>零售</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal1" value=""/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>装饰公司</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal2" value=""/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>电商</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal3" value=""/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>工程</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal4" value=""/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>促销活动</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal5" value=""/>
	                        <span>%</span>
	                    </label><br/>
	                    <label>
	                        <span>其他</span>
	                        <input type="number" class="txt1 er" name="salesChannelsVal6" value=""/>
	                        <span>%</span>&nbsp;
	                    </label>
                    </dd>
                </dl>
                <dl>
                    <dt>人员配置</dt>
                    <dd>
                        <label>
		                    <span>营销人员</span>
		                    <input class="txt1 er" name="marketersNumber" value="" type="number" />
		                    <span>名</span>
	                    </label><br/>
	                    <label>
		                    <span>售后人员</span>
		                    <input class="txt1 er" name="afterSaleNumber" value="" type="number" />
		                    <span>名</span>
	                    </label><br/>
	                    <label>
		                    <span>安装工人</span>
		                    <input class="txt1 er" name="installBodyNumber" value="" type="number" />
		                    <span>名</span>
	                    </label><br/>
	                    <label style="padding-right: 4px;">
		                    <span>仓库面积</span>
		                    <input class="txt1 er" name="warehouseArea" value="" type="number" />
		                    <span>㎡</span>
	                    </label>
                    </dd>
                </dl>
                <dl>
                    <dt>门店设施</dt>
                    <dd>
                        <label>
	                        <span>车辆配置：货车</span>
		                    <input class="txt1 er" name="truck" value="" type="number"/>
		                    <span>辆</span>
	                    </label><br/>
	                    <label>
		                    <span>小车</span>
		                    <input class="txt1 er" name="smallCar" value="" type="number"/>
		                    <span>辆</span>
	                    </label><br/>
	                    <div class="chooseBox" id="b7" style="margin:0; padding:0;">
	                        <span>电脑/宽带：</span>
	                        <label class="checkhid" data-id="b7" style="display: inline-block; margin: 3px 0 4px;">
	                            <input type="radio" name="pcOrBroadband" value="1" />
	                            <div class="check_box"></div>有
	                        </label>&nbsp;
	                        <label class="checkhid" data-id="b7" style="display: inline-block; margin: 3px 0 4px;">
	                            <input type="radio" name="pcOrBroadband" value="0" />
	                            <div class="check_box"></div>没有
	                        </label>&nbsp;
	                    </div>
                    </dd>
                </dl>
                <dl>
                    <dt>加盟手续</dt>
                    <dd style="margin: 0 0 3px;">
	                    <div class="chooseBox" id="b8" style="margin: 5px 0 0;">
	                        <label class="checkhid" data-id="b8" style="display: inline-block; margin: 3px 0 4px;">
                                <input type="radio" name="joiningFormalities1" value="1" />
                                <div class="check_box"></div>是
                            </label>&nbsp;
                            <label class="checkhid" data-id="b8" style="display: inline-block; margin: 3px 0 4px;">
                                <input type="radio" name="joiningFormalities1" value="0" checked="checked" />
                                <div class="check_box checked"></div>否
                            </label>
	                    </div>
	                    <span>区域经理已建立CRM帐户（详见《关于linkCRM系统经销商信息命名规则》）</span>
	                    <div class="chooseBox" id="b9" style="margin: 5px 0 0px;">
	                        <label class="checkhid" data-id="b9" style="display: inline-block; margin: 3px 0 4px;">
                                <input type="radio" name="joiningFormalities2" value="1" />
                                <div class="check_box"></div>是
                            </label>&nbsp;
                            <label class="checkhid" data-id="b9" style="display: inline-block; margin: 3px 0 4px;">
                                <input type="radio" name="joiningFormalities2" value="0" checked="checked" />
                                <div class="check_box checked"></div>否
                            </label>
	                    </div>
	                    <span>已缴纳品牌保证金（汇款凭证提供渠道部，TEL:0757-22916305）</span>
                    </dd>
                </dl>
                <dl>
                    <dt>客户说明</dt>
                    <dd><textarea class="txt" name="shopCaseNote" placeholder="请输入"></textarea></dd>
                </dl>
                <dl class="upload-bBox">
                    <dt>&emsp;上传附件<br/>（最多两个）</dt>
                    <dd>
                        <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'shopFile')">上传</a>
                    </dd>
                </dl>
                <div class="atta-list" id="addShopAttach"></div>
            </div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>其他信息</b></div>
            <div class="dl-style01">
                <dl class="readonly">
                    <dt>应缴品牌保证金</dt>
                    <dd><input type="number" class="txt" name="needCautionPaid" id="needCautionPaid" value="" readonly oninput="editAccSub(this,event)" onpropertychange="editAccSub(this,event)" /></dd>
                </dl>
                <dl>
                    <dt>实缴品牌保证金</dt>
                    <dd><input type="number" class="txt" name="realCautionPaid" id="realCautionPaid" value="" oninput="editAccSub(this,event)" onpropertychange="editAccSub(this,event)" /></dd>
                </dl>
                <dl class="readonly">
                    <dt>欠缴品牌保证金</dt>
                    <dd><input type="number" class="txt" name="unpaidCautionPaid" id="unpaidCautionPaid" value="" readonly="readonly" /></dd>
                </dl>
                <dl>
                    <dt>现金客户</dt>
                    <dd class="chooseBox" id="b10">
                        <label class="checkhid" data-id="b10" style="display: inline-block;"><input type="radio" name="cashClientFlag" value="true" /><div class="check_box"></div>是</label>&nbsp;
                        <label class="checkhid" data-id="b10" style="display: inline-block;"><input type="radio" name="cashClientFlag" value="false" /><div class="check_box"></div>否</label>
                    </dd>
                </dl>
                <dl>
                    <dt>货币</dt>
                    <dd>
                        <select name="currencyCode" class="txt">
	                        <option value="人民币" >${message("人民币")}</option>
	                    </select>
                    </dd>
                </dl>
                <dl class="readonly">
                    <dt>门店数量</dt>
                    <dd></dd>
                </dl>
                <dl>
                    <dt>销量保证金</dt>
                    <dd><input type="number" class="txt" name="salesDeposit" value="" /></dd>
                </dl>
                <dl>
                    <dt>销售品类</dt>
                    <dd data-id="SalesCategoryPup" onclick="showPup(this)">
                        <input type="text" name="salesCategory" class="txt arrow" value="" disabled />
                    </dd>
                </dl>
                <dl>
                    <dt>经销商类型</dt>
                    <dd>
                        <select name="distributorType" class="txt" dir="rtl">
                            <option value="0" >${message("国内经销商")}</option>
                            <option value="1" >${message("国际经销商")}</option>
                            <option value="2" >${message("国产产品经销商")}</option>
                            <option value="3" >${message("进口产品经销商")}</option>
                        </select>
                    </dd>
                </dl>
                <dl>
                    <dt>经销商子类型</dt>
                    <dd>
                        <select name="subType" class="txt" dir="rtl">
	                        <option value="" >${message("请选择")}</option>
	                        <option value="0" >${message("总经销商")}</option>
	                        <option value="1" >${message("省会城市经销商")}</option>
	                        <option value="2" >${message("平台经销商")}</option>
	                        <option value="3" >${message("经销商")}</option>
	                        <option value="4" >${message("分销商")}</option>
	                    </select>
                    </dd>
                </dl>
                <dl>
                    <dt>税率</dt>
                    <dd><input type="number" class="txt" name="taxRate" value="13" />&nbsp;%</dd>
                </dl>
                <dl>
                    <dt>缴纳情况/异常说明</dt>
                    <dd><textarea class="txt" name="paymentStatus" placeholder="请输入"></textarea></dd>
                </dl>
                <dl style="display:none;">
                    <select name="store_type">
                        [#list types as value]
                        <option value="${value}" [#if value==4]selected[/#if]>${message('11111111'+value)}</option>
                        [/#list]
                    </select>
                </dl>
            </div>
        </div>
        
        <!-- 添加收货地址 -->
        <div class="info-box mt8" style="padding-bottom: 1px;">
            <div class="title"><b>收货地址</b><input type="button" class="add-addr" value="添加" onclick="addAddrees(this)"/></div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>省长意见</b></div>
            <div class="dl-style01">
                <dl>
                    <dd><textarea class="txt" name="governorOpinion" placeholder="请输入"></textarea></dd>
                </dl>
            </div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>渠道部意见</b></div>
            <div class="dl-style01">
                <dl>
                    <dt>经销商授权编号</dt>
                    <dd><input type="text" name="dealerCoding" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>新增档案编号</dt>
                    <dd><input type="text" name="addArchivesCoding" class="txt" placeholder="请输入" /></dd>
                </dl>
                <dl>
                    <dt>新增时间</dt>
                    <dd><input type="date" class="txt time" name="addTime" btn-fun="clear" value="" /></dd>
                </dl>
            </div>
        </div>
        
        <div class="info-box mt8">
            <div class="title"><b>经销商情况</b></div>
            <div class="dl-style01">
                <dl>
                    <dt>备注</dt>
                    <dd><textarea class="txt" name="dealerCaseNote" placeholder="请输入"></textarea></dd>
                </dl>
            </div>
        </div>
    </form>
    
    <!-- 文件上传 -->
    <form id="fileForm">
        <input type="file" name="file" id="shopFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addShopAttach', 'shopAttachs')" style="display: none"/>
    </form>
    
    <div class="h50"></div>
    <div class="info-btns"><input type="button" value="提交" class="btn-blue btn" onclick="save()" /></div>
</div>

<!-- ***************** 隐藏数据 ***************** -->
<div id="hide-address" style="display:none;">
	<div class="dl-style01 card-addr">
	    <a href='javascript:void(0);' class='ico-del del-addr' onclick='del(this)'></a>
	    <dl>
	        <dt>收货人</dt>
	        <dd><input type="text" name="" d-name="consignee" value="" class="txt" /></dd>
	    </dl>
	    <dl>
	        <dt>收货人电话</dt>
	        <dd><input type="text" name="" d-name="mobile" value="" class="txt" /></dd>
	    </dl>
	    <dl>
	        <dt>收货地区</dt>
	        <dd data-id="ReceivingAreaPup" onclick="showPup(this)">
	            <input type="hidden" name="" d-name="areaId" value="" class="txt" />
	            <input type="text" name="" d-name="receivingAreaVal" value="" class="txt arrow" disabled="disabled"/>
	        </dd>
	    </dl>
	    <dl>
	        <dt>销售区域</dt>
	        <dd data-id="AddrSalesAreaPup" onclick="showPup(this)">
	            <input type="hidden" name="" d-name="salesAreaId" value="" class="txt" />
	            <input type="text" name="" d-name="salesAreaVal" value="" class="txt arrow" disabled="disabled"/>
	        </dd>
	    </dl>
	    <dl>
	        <dt>收货地址</dt>
	        <dd><input type="text" name="" d-name="address" value="" class="txt" /></dd>
	    </dl>
	    <dl>
	        <dt>收货地区邮编</dt>
	        <dd><input type="text" name="" d-name="zipCode" value="" class="txt" /></dd>
	    </dl>
	    <dl>
	        <dt>地址类型</dt>
	        <dd>
	            <select name="" d-name="addressType" dir="rtl" class="txt">
	                <option value="0" >经销商地址</option>
	                <option value="1">收货地址</option>
	                <option value="2">收单地址</option>
	                <option value="3" selected="selected">收单收货地址</option>
	            </select>
	        </dd>
	    </dl>
	    <dl>
	        <dt>设置</dt>
	        <dd class="chooseBox" id="b20">
                <label class="checkhid" data-id="b20" style="display: inline-block;">
	                <input type="radio" name="" d-name="isDefault" value="true" onchange="setDefault(this)" />
	                <div class="check_box""></div>是否默认
                </label>
            </dd>
	    </dl>
	</div>
</div>

<!-- ***************** 选择弹出框 ***************** -->
<!-- 选择机构 -->
<div class="pup-obox" id="OrganizationPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            [#--<a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择机构</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="机构名称" id="oKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchOrganization(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 选择区域经理 -->
<div class="pup-obox" id="StoreMemberPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            [#--<a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择区域经理</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="姓名" id="smKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchStoreMember(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 选择销售区域 -->
<div class="pup-obox" id="SalesAreaPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
           [#-- <a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择销售区域</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="地区名称" id="saKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchSalesArea(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 选择销售品类 -->
<div class="pup-obox" id="SalesCategoryPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
           [#-- <a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择销售品类</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="分类名称" id="scKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchSalesCategory(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 收货地址 - 收货地区 -->
<div class="pup-obox" id="ReceivingAreaPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            [#--<a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择收货地区</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="地区名称" id="raKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchReceivingArea(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
<!-- 收货地址 - 销售区域 -->
<div class="pup-obox" id="AddrSalesAreaPup">
    <div style="position:fixed; width:100%;z-index:10;">
        <div class="pup-header">
            [#--<a href="javascript:void(0);" class="go-back js-cancle"></a>--]
            <a href="javascript:history.back(-1);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择销售区域</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="地区名称" id="asaKeyword"/>
            <input type="button" class="btn" value="搜索" onclick="searchAddrSalesArea(this)" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>
</body>
</html>
