<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
	<meta content="yes" name="apple-mobile-web-app-capable" />
	<meta content="black" name="apple-mobile-web-app-status-bar-style" />
	<meta content="telephone=no" name="format-detection" />
	<meta http-equiv="Cache-Control" content="no-siteapp" />
	<title>市场人员管理</title>
	<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
	<link href="/resources/css/mobile/new.css" rel="stylesheet" type="text/css" />
	<style>
		.kb_con_left {width: 50px; overflow: hidden;}
		#pupScreen, #pupScreenRead {top: 100%; bottom: -100%; left: 0; right: 0;}
		input[type="date"]:before{
			content: attr(placeholder);
		}
		body {
			font-size: 12px;
		}
		.salesmanBtn {
			background: #48A2FE;
			color: #FFF;
			border: none;
			float: right;
			width: 14%;
			border-radius: 30px;
			height: 24px;
			font-size: 12px;
			margin: 4px 5px 0 0;
			line-height: 24px;
			text-align: center;
		}
		.screen-box01 .sb-head, .screen-box01 .sb-head a {font-size:13px;}
		.tab-style h1, #title-tab h1 { height: 42px; line-height: 42px; }
	</style>
</head>
<body>
<div id="top" style="margin-bottom: 30px;">
	<!--头部背景-->
	<ul class="kanban_bg" style="height:150px;">
		<li class="kb_text">
			<h1 onclick="toSignIn(this)">签到</h1>
			<h2 onclick="toWritePlan(this)">发布看板</h2>
		</li>
		[#if sm.imageName??]
			<li class="kb_img"><img src="${sm.imageName}" style="border-radius: 50px; width: 60px; height: 60px;"/></li>
		[#else]
			<li class="kb_img"><img src="/resources/images/mobile/default_tx.jpg" style="border-radius: 50px; width: 60px; height: 60px;"/></li>
		[/#if]
		<li class="kb_name" style="margin-top:30px;padding-left: 15px;">${sm.name}</li>
	</ul>
	<!--tab栏-->
	<ul class="tab-style" id="title-tab" style="border-bottom: solid 1px #eee;height:44px;">
		<li class="[#if !tab??]cur[/#if]" data-id="billInfo" onclick="changeTab(this)">
			[#--		    <h1 onclick="unReadClick()">未读<span class="number">${unread?size}</span></h1><p></p>--]
			<h1  onclick="unReadClick(this)">未读<span class="number">${unReadCount}</span></h1><p></p>
		</li>
		<li data-id="approvalStep" onclick="changeTab(this)">
			<h1 onclick="readClick(this)">已读</h1><p></p>
		</li>
		<li class="[#if tab == 3]cur[/#if]" data-id="Step2" onclick="changeTab(this)">
			<h1 onclick="myClick()">我的</h1><p></p>
		</li>
	</ul>
	<div class="kb_tab_box">
		<!--未读-->
		<div class="info-box" id="billInfo" style="[#if tab??]display: none;[/#if]">
			<div class="tab-style02" style="height:40px;">
				<h2 onclick="showGenre(this, 'unread')"><span>机构</span><i class="ico-arrow anima"></i></h2>
				<h2 onclick="showStore(this, 'unread')"><span>业务员</span><i class="ico-arrow anima"></i></h2>
				<!-- <h2 class="space_box" style="padding:1px;"><input type="date" name="unreadDate" value="" onchange="selectDate(this, 'unread')" class="txt time" style="height:3rem;"/></h2> -->
				<h2 onclick="showDate(this, 'unread')"><span>时间</span><i class="ico-arrow anima"></i></h2>
			</div>

			<div id="unreadPlan">
				[#list unread as item]
					<div class="kb_con_box">
						<div class="kb_con_left">
							[#if item.plan.storeMember.imageName??]
								<img src="${item.plan.storeMember.imageName}"/><h1>${item.plan.storeMember.name}</h1>
							[#else]
								<img src="/resources/images/mobile/default_tx.jpg"/><h1>${item.plan.storeMember.name}</h1>
							[/#if]
						</div>
						<div class="kb_con_right">
							<ul class="kb_con_task_box" onclick="toPlanDetails(this, ${item.plan.id})">
								<li class="kb_con_right_title"><p class="kb_heng"></p><span>计划</span></li>
								[#list item.plan.plans as plan]
									<li>
										<h2><span>${plan_index + 1}. </span>${plan.content}</h2>
										[#if plan.status??]
											[#if plan.status == 1]
												<p class="completed">已完成</p>
											[#elseif plan.status == 2]
												<p class="undown">未完成</p>
											[#elseif plan.status == 3]
												<p class="undown">调整</p>
											[/#if]
										[/#if]
									</li>
								[/#list]
							</ul>
							<ul onclick="toPlanDetails(this, ${item.plan.id})">
								<li class="kb_con_right_title"><p class="kb_heng"></p><span>总结</span></li>
								[#assign zj=0 /]
								[#list item.plan.plans as plan2]
									[#if plan2.summary??]
										[#assign zj=zj+1 /]
										<li><h3>${zj}. ${plan2.summary}</h3></li>
									[/#if]
								[/#list]
								[#if zj == 0]
									<img src="/resources/images/mobile/zwzj_2.jpg" style="width: 80px; margin: 5px 35%;"/>
								[/#if]
							</ul>
							<ul class="kb_con_right_img">
								[#assign imgSize=0 ]
								[#list item.plan.plans as plan3]
									[#if plan3.planItemAttachs?? && (plan3.planItemAttachs?size > 0)]
										[#list plan3.planItemAttachs as attach]
											[#if imgSize > 3]
												[#break]
											[/#if]
											<li><img src="${attach.url}" onclick="imgDisplay(this)" /></li>
											[#assign imgSize=imgSize+1 ]
										[/#list]
									[/#if]
								[/#list]
							</ul>
							<!-- <div class="kb_con_right_loc">
                                <img src="/resources/images/mobile/ico-adr02.png"/><h3>广东省佛广州市番禺石壁街道小路</h3>
                            </div> -->
							<div class="kb_con_right_hour">
								<h4 style="display: inline;padding-top:0px;">${(item.plan.createDate?string("yyyy-MM-dd HH:mm:ss"))!}</h4>
								<!-- <h4>2小时前</h4> -->
								<div class="oper">
									<span class="ico-like [#if item.like == 1]on[/#if]" onclick="onLike(this, ${item.plan.id})">${item.plan.likeCount}</span>
									<span class="ico-see">${item.plan.viewCount}</span>
									<span class="ico-comment" onclick="togComment(this, ${item.plan.id})">${item.comments?size}</span>
								</div>
							</div>
						</div>
						<div class="clearfix"></div>
						<ul class="kb_con_btoom_message">
							[#list item.comments as comm]
								<li>
									[#if comm.storeMember.imageName??]
										<img src="${comm.storeMember.imageName}"/>
									[#else]
										<img src="/resources/images/mobile/default_tx.jpg"/>
									[/#if]
									<div class="kb_message_right">
										<h1>${comm.storeMember.name}</h1>
										<h2>${(comm.createDate?string("MM月dd日 HH:mm"))!}</h2>
										<h3 style="white-space: normal;">${comm.content}</h3>
									</div>
								</li>
							[/#list]
						</ul>
					</div>
				[/#list]
			</div>

			[#if unread?size > 0]
			<div class="kb_con_box">
				<p style="text-align: center;height: 30px" onclick="unReadLoadNext(this)" >加载</p>
			</div>
				[#else]
					<div class="kb_con_box">
						<p style="text-align: center;height: 30px" onclick="unReadLoadNext(this)" >没有更多的数据~</p>
					</div>
			[/#if]
			<!--筛选页面-->
			<!-- pup -->
			<div class="pup-mask" style="top: 240px;"></div>
			<div class="screen-box anima" id="pupStore" style="border-top: 1px solid #eee;">
				<dl onclick="selectDate(this, 'unread')">
					<dt>无</dt>
				</dl>
				[#--				<dl onclick="selectDate(this, 'unread')">--]
				[#--					<dt>最近一周</dt>--]
				[#--				</dl>--]
				[#--				<dl onclick="selectDate(this, 'unread')">--]
				[#--					<dt>一个月</dt>--]
				[#--				</dl>--]
				[#--				<dl onclick="selectDate(this, 'unread')">--]
				[#--					<dt>三个月</dt>--]
				[#--				</dl>--]
				<dl>
					<dt>开始时间：<input type="date" id="unreadStartTime" name="unreadStartTime" value="" class="txt time"></dt>
					<dt>结束时间：<input type="date" id="unreadEndTime" name="unreadEndTime" value="" class="txt time"></dt>

					[#--                    <dt>开始时间：<input type="date" id="unreadStartTime"/></dt>--]
					[#--					<dt>结束时间：<input type="date" id="unreadEndTime"/></dt>--]
					[#--						<dt onclick="selectTime(this)">确定</dt>--]
					<dt onclick="selectDate(this, 'unread')">确定</dt>
				</dl>
			</div>
			<div class="screen-box01 anima" id="pupGenre">
				<div class="sb-head">
					<!-- <a href="javascript:;" class="s-l f-blue" onclick="cancleGenre(this)">取消</a> -->
					<div class="t">机构</div>
					<a href="javascript:;" class="s-r f-blue" onclick="selectOrg(this, 'unread')" style="font-size:13px;">确定</a>
				</div>
				<div></div>
				<!-- <div class="search-box">
					<input type="search" class="txt" placeholder="搜索" style="width: 100%"/>
				</div> -->
				<ul class="genre-list" id="c" style="top: 44px; border-top: 1px solid #48A2FE;">
					<li>
						<label class="checkhid" data-id="c">
							<input type="radio" name="unreadOrg" value="" org-val="机构" checked />
							<div class="fl">无</div>
							<div class="check_box fr checked"></div>
						</label>
					</li>
					[#list saleOrgs as org]
						<li>
							<label class="checkhid" data-id="c">
								<input type="radio" name="unreadOrg" value="${org.id}" org-val="${org.name}" />
								<div class="fl">${org.name}</div>
								<div class="check_box fr"></div>
							</label>
						</li>
					[/#list]
				</ul>
			</div>
			<div class="screen-box01 anima unreadSalesman" id="pupScreen">
				<div class="sb-head">
					<!-- <a href="javascript:;" class="s-l f-blue" onclick="cancleGenre(this)">取消</a> -->
					<div class="t">业务员</div>
					<a href="javascript:;" class="s-r f-blue" onclick="selectSalesman(this, 'unread')" style="font-size:13px;">确定</a>
				</div>
				<div class="search-box" style="padding:5px;">
					<div style="background: #fff; border-radius: 50px;">
						<input type="search" class="txt" placeholder="名称" style="width:80%;"/>
						<input type="button" value="搜索" onclick="searchSalesman(this, 'unread')" class="salesmanBtn" />
					</div>
				</div>
				<ul class="genre-list" id="d">
					<li>
						<label class="checkhid" data-id="d">
							<input type="radio" name="salesman" value="" org-val="业务员" checked />
							<div class="fl">无</div>
							<div class="check_box fr checked"></div>
						</label>
					</li>
				</ul>
			</div>
		</div>
		<!--已读-->
		<div class="approval-box" id="approvalStep" style="display: none">
			<div class="info-box" id="billInfo1">
				<div class="tab-style02" style="height:40px;">
					<h2 onclick="showGenre(this, 'read')"><span>机构</span><i class="ico-arrow anima"></i></h2>
					<h2 onclick="showStore(this, 'read')"><span>业务员</span><i class="ico-arrow anima"></i></h2>
					<!-- <h2 class="space_box" style="padding:1px;"><input type="date" name="unreadDate" value="" onchange="selectDate(this, 'read')" class="txt time" style="height:3rem;"/></h2> -->
					<h2 onclick="showDate(this, 'read')"><span>时间</span><i class="ico-arrow anima"></i></h2>
				</div>

				<div id="readPlan">
					[#list read as item]
						<div class="kb_con_box">
							<div class="kb_con_left">
								[#if item.plan.storeMember.imageName??]
									<img src="${item.plan.storeMember.imageName}"/><h1>${item.plan.storeMember.name}</h1>
								[#else]
									<img src="/resources/images/mobile/default_tx.jpg"/><h1>${item.plan.storeMember.name}</h1>
								[/#if]
							</div>
							<div class="kb_con_right">
								<ul class="kb_con_task_box" onclick="toPlanDetails(this, ${item.plan.id})">
									<li class="kb_con_right_title"><p class="kb_heng"></p><span>计划</span></li>
									[#list item.plan.plans as plan]
										<li>
											<h2><span>${plan_index + 1}. </span>${plan.content}</h2>
											[#if plan.status??]
												[#if plan.status == 1]
													<p class="completed">已完成</p>
												[#elseif plan.status == 2]
													<p class="undown">未完成</p>
												[#elseif plan.status == 3]
													<p class="undown">调整</p>
												[/#if]
											[/#if]
										</li>
									[/#list]
								</ul>
								<ul onclick="toPlanDetails(this, ${item.plan.id})">
									<li class="kb_con_right_title"><p class="kb_heng"></p><span>总结</span></li>
									[#assign zj=0 /]
									[#list item.plan.plans as plan2]
										[#if plan2.summary??]
											[#assign zj=zj+1 /]
											<li><h3>${zj}. ${plan2.summary}</h3></li>
										[/#if]
									[/#list]
									[#if zj == 0]
										<img src="/resources/images/mobile/zwzj_2.jpg" style="width: 80px; margin: 5px 35%;"/>
									[/#if]
								</ul>
								<ul class="kb_con_right_img">
									[#assign imgSize=0 ]
									[#list item.plan.plans as plan3]
										[#if plan3.planItemAttachs?? && (plan3.planItemAttachs?size > 0)]
											[#list plan3.planItemAttachs as attach]
												[#if imgSize > 3]
													[#break]
												[/#if]
												<li><img src="${attach.url}" onclick="imgDisplay(this)" /></li>
												[#assign imgSize=imgSize+1 ]
											[/#list]
										[/#if]
									[/#list]
								</ul>
								<!-- <div class="kb_con_right_loc">
                                    <img src="/resources/images/mobile/ico-adr02.png"/><h3>广东省佛广州市番禺石壁街道小路</h3>
                                </div> -->
								<div class="kb_con_right_hour">
									<h4 style="display: inline;padding-top:0px;">${(item.plan.createDate?string("yyyy-MM-dd HH:mm:ss"))!}</h4>
									<!-- <h4>2小时前</h4> -->
									<div class="oper">
										<span class="ico-like [#if item.like == 1]on[/#if]" onclick="onLike(this, ${item.plan.id})">${item.plan.likeCount}</span>
										<span class="ico-see">${item.plan.viewCount}</span>
										<span class="ico-comment" onclick="togComment(this, ${item.plan.id})">${item.comments?size}</span>
									</div>
								</div>
							</div>
							<div class="clearfix"></div>
							<ul class="kb_con_btoom_message">
								[#list item.comments as comm]
									<li>
										[#if comm.storeMember.imageName??]
											<img src="${comm.storeMember.imageName}"/>
										[#else]
											<img src="/resources/images/mobile/default_tx.jpg"/>
										[/#if]
										<div class="kb_message_right">
											<h1>${comm.storeMember.name}</h1>
											<h2>${(comm.createDate?string("MM月dd日 HH:mm"))!}</h2>
											<h3 style="white-space: normal;">${comm.content}</h3>
										</div>
									</li>
								[/#list]
							</ul>
						</div>
					[/#list]
				</div>
				[#if read?size > 0]
				<div class="kb_con_box">
					<h1 style="text-align: center;height: 30px" onclick="readLoadNext(this)" >加载</h1>
				</div>
				[/#if]
				<!--筛选页面-->
				<!-- pup -->
				<div class="pup-mask" style="top:240px;"></div>
				<div class="screen-box anima"  style="border-top: 1px solid #eee;">
					<dl onclick="selectDate(this, 'read')">
						<dt>无</dt>
					</dl>
					[#--	                <dl onclick="selectDate(this, 'read')">--]
					[#--	                    <dt>最近一周</dt>--]
					[#--	                </dl>--]
					[#--	                <dl onclick="selectDate(this, 'read')">--]
					[#--	                    <dt>一个月</dt>--]
					[#--	                </dl>--]
					[#--	                <dl onclick="selectDate(this, 'read')">--]
					[#--	                    <dt>三个月</dt>--]
					[#--	                </dl>--]

					<dl>
						<dt>开始时间：<input type="date" id="readStartTime" name="readStartTime" value="" class="txt time"></dt>
						<dt>结束时间：<input type="date" id="readEndTime" name="readEndTime" value="" class="txt time"></dt>
						[#--						<dt>开始时间：<input type="date" id="readStartTime"/></dt>--]
						[#--						<dt>结束时间：<input type="date" id="readEndTime"/></dt>--]
						[#--						<dt onclick="selectTime(this)">确定</dt>--]
						<dt onclick="selectDate(this, 'read')">确定</dt>
					</dl>
				</div>
				<div class="screen-box01 anima" id="pupGenreRead">
					<div class="sb-head">
						<!-- <a href="javascript:;" class="s-l f-blue" onclick="cancleGenre(this)">取消</a> -->
						<div class="t">机构</div>
						<a href="javascript:;" class="s-r f-blue" onclick="selectOrg(this, 'read')" style="font-size:13px;">确定</a>
					</div>
					<div></div>
					<!-- <div class="search-box">
                        <input type="search" class="txt" placeholder="搜索" style="width: 100%"/>
                    </div> -->
					<ul class="genre-list" id="e" style="top: 44px; border-top: 1px solid #48A2FE;">
						<li>
							<label class="checkhid" data-id="e">
								<input type="radio" name="unreadOrg" value="" org-val="机构" checked />
								<div class="fl">无</div>
								<div class="check_box fr checked"></div>
							</label>
						</li>
						[#list saleOrgs as org]
							<li>
								<label class="checkhid" data-id="e">
									<input type="radio" name="unreadOrg" value="${org.id}" org-val="${org.name}" />
									<div class="fl">${org.name}</div>
									<div class="check_box fr"></div>
								</label>
							</li>
						[/#list]
					</ul>
				</div>
				<div class="screen-box01 anima readSalesman" id="pupScreenRead">
					<div class="sb-head">
						<!-- <a href="javascript:;" class="s-l f-blue" onclick="cancleGenre(this)">取消</a> -->
						<div class="t">业务员</div>
						<a href="javascript:;" class="s-r f-blue" onclick="selectSalesman(this, 'read')" style="font-size:13px;">确定</a>
					</div>
					<div class="search-box" style="padding:5px;">
						<div style="background: #fff; border-radius: 50px;">
							<input type="search" class="txt" placeholder="名称" style="width:80%;"/>
							<input type="button" value="搜索" onclick="searchSalesman(this, 'read')" class="salesmanBtn" />
						</div>
					</div>
					<ul class="genre-list" id="f">
						<li>
							<label class="checkhid" data-id="f">
								<input type="radio" name="salesman" value="" org-val="业务员" checked />
								<div class="fl">无</div>
								<div class="check_box fr checked"></div>
							</label>
						</li>
					</ul>
				</div>
			</div>
		</div>
		<!--我的-->
		<div class="box2" id="Step2" style="[#if tab != 3]display: none;[/#if]">
			<!-- <div class="kb_zs">
				<h1>招商任务</h1>
				<h2>
					<span>10</span><img src="/resources/images/mobile/ico-arrowR.png" />
				</h2>
			</div> -->
			[#list my as item]
				<div class="kb_con_box">
					<div class="kb_con_left">
						[#if item.plan.storeMember.imageName??]
							<img src="${item.plan.storeMember.imageName}"/><h1>${item.plan.storeMember.name}</h1>
						[#else]
							<img src="/resources/images/mobile/default_tx.jpg"/><h1>${item.plan.storeMember.name}</h1>
						[/#if]
					</div>
					<div class="kb_con_right">
						<ul class="kb_con_task_box" onclick="toPlanDetails(this, ${item.plan.id})">
							<li class="kb_con_right_title">
								<p class="kb_heng"></p><span>计划</span>
							</li>
							[#list item.plan.plans as plan]
								<li>
									<h2><span>${plan_index + 1}. </span>${plan.content}</h2>
									[#if plan.status??]
										[#if plan.status == 1]
											<p class="completed">已完成</p>
										[#elseif plan.status == 2]
											<p class="undown">未完成</p>
										[#elseif plan.status == 3]
											<p class="undown">调整</p>
										[/#if]
									[/#if]
								</li>
							[/#list]
						</ul>
						<ul onclick="toPlanDetails(this, ${item.plan.id})">
							<li class="kb_con_right_title"><p class="kb_heng"></p><span>总结</span></li>
							[#assign zj=0 /]
							[#list item.plan.plans as plan2]
								[#if plan2.summary??]
									[#assign zj=zj+1 /]
									<li><h3>${zj}. ${plan2.summary}</h3></li>
								[/#if]
							[/#list]
							[#if zj == 0]
								<img src="/resources/images/mobile/zwzj_2.jpg" style="width: 80px; margin: 5px 35%;"/>
							[/#if]
						</ul>
						<ul class="kb_con_right_img">
							[#assign imgSize=0 ]
							[#list item.plan.plans as plan3]
								[#if plan3.planItemAttachs?? && (plan3.planItemAttachs?size > 0)]
									[#list plan3.planItemAttachs as attach]
										[#if imgSize > 3]
											[#break]
										[/#if]
										<li><img src="${attach.url}" onclick="imgDisplay(this)" /></li>
										[#assign imgSize=imgSize+1 ]
									[/#list]
								[/#if]
							[/#list]
						</ul>
						<!-- <div class="kb_con_right_loc">
                            <img src="/resources/images/mobile/ico-adr02.png"/><h3>广东省佛广州市番禺石壁街道小路</h3>
                        </div> -->
						<div class="kb_con_right_hour">
							<h4 style="display: inline;padding-top:6px;">${(item.plan.createDate?string("yyyy-MM-dd HH:mm:ss"))!}</h4>
							<!-- <h4>2小时前</h4> -->
							<div class="oper">
								<div class="oper">
									<span class="ico-like [#if item.like == 1]on[/#if]" onclick="onLike(this, ${item.plan.id})">${item.plan.likeCount}</span>
									<span class="ico-see">${item.plan.viewCount}</span>
									<span class="ico-comment" onclick="togComment(this, ${item.plan.id})">${item.comments?size}</span>
								</div>
							</div>
						</div>
					</div>
					<div class="clearfix"></div>
					<ul class="kb_con_btoom_message">
						[#list item.comments as comm]
							<li>
								[#if comm.storeMember.imageName??]
									<img src="${comm.storeMember.imageName}"/>
								[#else]
									<img src="/resources/images/mobile/default_tx.jpg"/>
								[/#if]
								<div class="kb_message_right">
									<h1>${comm.storeMember.name}</h1>
									<h2>${(comm.createDate?string("MM月dd日 HH:mm"))!}</h2>
									<h3 style="white-space: normal;">${comm.content}</h3>
								</div>
							</li>
						[/#list]
					</ul>
				</div>
			[/#list]
		</div>
	</div>
	<a href="#top" class="go-top" style="display:none;"></a>
</div>
<!-- 评论区 -->
<div class="comment-bottom" style="display:none; padding: 6px 12px 6px 12px; position: fixed; width: 100%; bottom: 0;">
	<div class="form">
		<input type="text" class="txt" id="commContent" placeholder="评论" onclick="clearTime(this)" onblur="hideCommBox(this)"/>
		<input type="hidden" id="commPid" />
		<input type="button" value="提交" onclick="toComments(this)" class="btn" />
	</div>
</div>
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script>
	var size = 20;
	var readPageNumber = 1;
	var unReadPageNumber = 1;
	window.pageNumber = 1;
	var readState = 'unread';
	var _readOrgId = null;
	var _readName = null;
	var _readDate = null;

	var _unReadOrgId = null;
	var _unReadName = null;
	var _unReadDate = null;


	$().ready(function() {

		$(window).scroll(function() {
			let height = $(window).scrollTop();
			if (height > 1000) {
				$('.go-top').show();
			} else {
				$('.go-top').hide();
			}
		});
	})

	//顶部导航未读消息切换
	function changeTab(e) {
		var id = $(e).attr("data-id");
		$(e).addClass("cur").siblings().removeClass("cur")
		$("#" + id).show().siblings().hide()
	}

	$("label.checkhid").live("click", function() {
		var id = $(this).attr("data-id");
		if ($(this).find("input").attr("type") == "checkbox") {
			if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
				$(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
			} else {
				$(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
			}
		} else {
			if ($(this).find("input[type=radio]:checked").val() == undefined) {
				$(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked");
			} else {
				$($("#" + id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
				$(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
			}
		}
	});
	function showTime(e) {
		if ($(e).hasClass("cur")) {
			$(".pup-mask").hide()
			$(".screen-box").css("top", "100%")
			$(e).removeClass("cur")
			$("body").attr("style", "overflow:initial")

		} else {
			$(".pup-mask").show()
			$(".screen-box").css("top", "240px")
			$(".screen-box02").attr("style", "top:100%;bottom:-100%")
			$(e).addClass("cur").siblings().removeClass("cur")
			$("body").attr("style", "overflow:hidden")
		}
	}
	function showGenre(e, isRead) {
		$(e).addClass("cur").siblings().removeClass("cur")
		if (isRead == 'unread') {
			$("#pupGenre").attr("style", "right:0;left:0")
			$("#pupScreen").attr("style", "top:100%;bottom:-100%")
		} else if (isRead == 'read') {
			$("#pupGenreRead").attr("style", "right:0;left:0")
			$("#pupScreenRead").attr("style", "top:100%;bottom:-100%")
		}
		$(".screen-box").css("top", "100%")
		$(".pup-mask").hide()
		$("body").attr("style", "overflow:hidden")
	}

	function showStore(e, isRead) {
		var orgId = isNull($(e).prev().find('span').attr("val-id"));
		if (orgId == '') {
			alert("请先选择机构！");
			return false;
		}
		ajaxSalesman(e, isRead, orgId);
		$(e).addClass("cur").siblings().removeClass("cur")
		if (isRead == 'unread') {
			$("#pupGenre").attr("style", "top:100%;bottom:-100%;")
			$("#pupScreen").attr("style", "top:0;bottom:0;")
		} else if (isRead == 'read') {
			$("#pupGenreRead").attr("style", "top:100%;bottom:-100%;")
			$("#pupScreenRead").attr("style", "top:0;bottom:0;")
		}
		$(".screen-box").css("top", "100%")
		$(".pup-mask").hide()
		$("body").attr("style", "overflow:hidden")
	}

	function showDate(e, isRead) {
		var orgId = isNull($(e).prev().prev().find('span').attr("val-id"));
		if (orgId == '') {
			alert("请先选择机构！");
			return false;
		}
		if($(e).hasClass("cur")){
			$(".pup-mask").hide()
			$(".screen-box").css("top","100%")
			$(e).removeClass("cur")
			$("body").attr("style","overflow:initial")
		} else {
			$(".pup-mask").show()
			$(".screen-box").css("top","240px")
			$(e).addClass("cur").siblings().removeClass("cur")
			$("body").attr("style","overflow:hidden")
		}
	}

	function cancleGenre(e, isRead) {
		$(".tab-style02 > div").removeClass("cur")
		if (isRead == 'unread') {
			$("#pupGenre").attr("style", "right:100%;left:-100%")
			$("#pupScreen").attr("style", "top:100%;bottom:-100%")
		} else if (isRead == 'read') {
			$("#pupGenreRead").attr("style", "right:100%;left:-100%")
			$("#pupScreenRead").attr("style", "top:100%;bottom:-100%")
		}
		$(".screen-box").css("top", "100%")
		$(".pup-mask").hide()
		$("body").attr("style", "overflow:initial")
	}

	// -----------------------
	function isNull(str) {
		var a = (str == null || str == "undefined") ? "" : str;
		return a;
	}

	/**
	 * 搜索业务员
	 */
	function searchSalesman(e, isRead) {
		let name = isNull($(e).prev().val());
		let orgId = '';
		if (isRead == 'unread') {
			orgId = $('#unreadPlan').prev().find('h2:first span').attr("val-id");
		} else if (isRead == 'read') {
			orgId = $('#readPlan').prev().find('h2:first span').attr("val-id");
		}
		ajaxSalesman(e, isRead, orgId, name);
	}

	/**
	 * @param isRead "unread":未读  "read":已读
	 * @param orgId 机构id
	 * @param name 业务员名称
	 */
	function ajaxSalesman(e, isRead, orgId, name) {
		let radioStoreClass = (isRead == 'unread') ? 'd' : 'f';
		$.ajax({
			type:'POST',
			url:'/mobile/salesman/getSalesman.jhtml',
			data:{name:isNull(name), orgId:isNull(orgId)},
			success:function(data) {
				let h5 = '<li>'
						+ '  <label class="checkhid" data-id="'+ radioStoreClass +'">'
						+ '     <input type="radio" name="salesman" value="" org-val="业务员" checked />'
						+ '     <div class="fl">无</div>'
						+ '     <div class="check_box fr checked"></div>'
						+ '  </label>'
						+ '</li>';
				for (let i = 0; i < data.length; i++) {
					h5 += '<li><label class="checkhid" data-id="'+ radioStoreClass +'">'
							+ '   <input type="radio" name="salesman" value="'+data[i].id+'" org-val="'+isNull(data[i].name)+'" />'
							+ '   <div class="fl">'+ isNull(data[i].name) +'</div>'
							+ '   <div class="check_box fr"></div>'
							+ '</label></li>'
				}
				if (isRead == 'unread') {
					$('.unreadSalesman ul').html(h5);
				} else if (isRead == 'read') {
					$('.readSalesman ul').html(h5);
				}
			}
		})
	}

	// 获取计划
	function ajaxPlan(e, isRead, orgId, name, date) {
		readState = isRead;
		window.pageNumber = 1;
		var d = {
			isRead:isRead,
			name:isNull(name),
			orgId:isNull(orgId),
			pageNumber:window.pageNumber,
			pageSize:size,
		};

		if(date != undefined){
			if(date.startTime!=undefined){
				d.startTime = date.startTime;
			}
			if(date.endTime != undefined){
				d.endTime = date.endTime;
			}
		}


		$.ajax({
			type:'POST',
			url:'/mobile/salesman/searchPlan.jhtml',
			//data:{isRead:isRead, name:isNull(name), orgId:isNull(orgId), date:date},
			data:d,
			success:function(data) {
				if (data.type == 'error') {
					alert("系统繁忙，请刷新页面再试试！");
				}
				let h5 = '';
				for (let i = 0; i < data.length; i++) {
					let row = data[i];
					let img = '/resources/images/mobile/default_tx.jpg';
					let useImg = isNull(row.plan.imageName) == '' ? img : isNull(row.plan.imageName)
					h5 += '<div class="kb_con_box">'
							+ '  <div class="kb_con_left">'
							+ '    <img src="'+ useImg +'"/><h1>'+ isNull(row.plan.name) +'</h1>'
							+ '  </div>'
							+ '  <div class="kb_con_right">' /* 计划项 */
							+ '    <ul class="kb_con_task_box" onclick="toPlanDetails(this, '+ row.plan.planId +')">'
							+ '      <li class="kb_con_right_title"><p class="kb_heng"></p><span>计划</span></li>';
					for (let j = 0; j < row.plan.planItemList.length; j++) {
						let planItem = row.plan.planItemList[j];
						let sta = {"1": "已完成", "2": "未完成", "3": "调整"};
						let staClass = {"1": "completed", "2": "undown", "3": "undown"};
						h5 += '  <li>'
								+ '    <h2><span>'+ (j+1) +'. </span>'+ planItem.content +'</h2>';
						if (isNull(planItem.status) != '') {
							h5 += '<p class="'+ staClass[isNull(planItem.status)] +'">'+ sta[isNull(planItem.status)] +'</p>';
						}
						h5 += '  </li>';
					}
					h5 += '    </ul>'
							+ '    <ul onclick="toPlanDetails(this, '+ row.plan.planId +')">'  /* 总结 */
							+ '      <li class="kb_con_right_title"><p class="kb_heng"></p><span>总结</span></li>';
					let summarySize = 0;
					for (let j = 0; j < row.plan.planItemList.length; j++) {
						let planItem = row.plan.planItemList[j];
						if (isNull(planItem.summary) != '') {
							h5 += '  <li><h3>'+ (j+1) +'. '+ isNull(planItem.summary) +'</h3></li>';
							summarySize++;
						}
					}
					if (summarySize == 0) {
						h5 += '  <img src="/resources/images/mobile/zwzj_2.jpg" style="width: 80px; margin: 5px 35%;"/>';
					}
					h5 += '    </ul>'
							+ '    <ul class="kb_con_right_img">';  /* 总结图片 */
					for (let j = 0; j < row.plan.planItemList.length; j++) {
						let planItem = row.plan.planItemList[j];
						for (let k = 0; k < planItem.attachList; k++) {
							let attach = planItem.attachList[k];
							if (k > 3) break;
							h5 += '<li><img src="'+ planItem.url +'" onclick="imgDisplay(this)"/></li>';
						}
					}
					let like = (row.like == 1) ? 'on' : '';  /* 时间、点赞、查看、评论、 */
					h5 += '    </ul>'
							+ '    <div class="kb_con_right_hour">'
							+ '      <h4 style="display: inline;padding-top:0px;">'+ row.plan.createDate +'</h4>'
							+ '      <div class="oper">'
							+ '        <span class="ico-like '+ like +'" onclick="onLike(this, '+ row.plan.planId +')">'+ row.plan.likeCount + '</span>'
							+ '        <span class="ico-see">'+ row.plan.viewCount +'</span>'
							+ '        <span class="ico-comment" onclick="togComment(this, '+ row.plan.planId +')">'+ row.comments.length +'</span> '
							+ '      </div>'
							+ '    </div>'
							+ '  </div>'
							+ '  <div class="clearfix"></div>'
							+ '  <ul class="kb_con_btoom_message">';
					for (let j = 0; j < row.comments.length; j++) {  /* 评论 */
						let commItem = row.comments[j];
						let commImg = '/resources/images/mobile/default_tx.jpg';
						let useCommImg = isNull(commItem.imageName) == '' ? commImg : isNull(commItem.imageName)
						h5 += ' <li>'
								+ '    <img src="'+ useCommImg +'"/>'
								+ '    <div class="kb_message_right">'
								+ '      <h1>'+ commItem.name +'</h1>'
								+ '      <h2>'+ commItem.createDate +'</h2>'
								+ '      <h3 style="white-space: normal;">'+ commItem.content +'</h3>'
								+ '    </div>'
								+ ' </li>';
					}
					h5 += '  </ul>'
							+ '</div>'


				}
				if (isRead == 'unread') {
					$('#unreadPlan').html(h5);
				} else if (isRead == 'read') {
					$('#readPlan').html(h5);
				}
			}
		})
	}

	// 选择机构
	function selectOrg(e, isRead) {
		var $ul = $(e).parent('div').next().next();
		var unreadOrgId = $ul.find('input:radio[name="unreadOrg"]:checked').val();
		var orgVal = $ul.find('input:radio[name="unreadOrg"]:checked').attr('org-val');
		$(e).parent().parent().parent().find('div:first h2:first span').attr("val-id", unreadOrgId).html(orgVal);
		$(e).parent().parent().parent().find('div:first h2:eq(1) span').attr("val-id", '').html("业务员");
		if (isNull(unreadOrgId) != '') {
			// readState = isRead;
			// _orgId = unreadOrgId;
			if(isRead == 'read'){
				readPageNumber = 1;
				_readOrgId = unreadOrgId;
			}else if(isRead == 'unread'){
				unReadPageNumber = 1;
				_unReadOrgId = unreadOrgId;
			}
			ajaxPlan(e, isRead, unreadOrgId);
		} else {
			window.location.reload();
		}
		cancleGenre(e, isRead);
	}

	// 选择业务员
	function selectSalesman(e, isRead) {
		var orgId = $(e).parent().parent().parent().find('div:first h2:first span').attr("val-id");
		var $ul = $(e).parent('div').next().next();
		var salesmanId = $ul.find('input:radio[name="salesman"]:checked').val();
		var storeVal = $ul.find('input:radio[name="salesman"]:checked').attr('org-val');
		var dateVal = $(e).parent().parent().parent().find('div:first h2:eq(2) span').html();
		$(e).parent().parent().parent().find('div:first h2:eq(1) span').attr("val-id", salesmanId).html(storeVal);
		if (isNull(salesmanId) == '') {
			storeVal = '';
		}
		// readState = isRead;
		// _orgId = orgId;
		// _name = storeVal;
		// _date = dateVal;

		if(isRead == 'read'){
			readPageNumber = 1;
			_readName = storeVal;
			_readDate = dateVal;
		}else if(isRead == 'unread'){
			unReadPageNumber = 1;
			_unReadName = storeVal;
			_unReadDate = dateVal;
		}
		ajaxPlan(e, isRead, orgId, storeVal, dateVal);
		cancleGenre(e, isRead);
	}

	// 选择日期
	/* function selectDate(e, isRead) {
        let date = $(e).val();
        let $store = $(e).parent().prev().find('span');
        let storeName = (isNull($store.attr('val-id')) == '') ? '' : $store.html();
        let orgId = isNull($(e).parent().prev().prev().find('span').attr('val-id'));
        if (orgId == '') {
            alert("请先选择机构");
            $(e).val('');
            return false;
        }
        ajaxPlan(e, isRead, orgId, storeName, date);
    } */

	// 选择日期
	function selectDate(e, isRead) {

		let dateVal = {}
		let text = $(e).children().text();
		if('read' == isRead){
			if('无' != text){
				dateVal = {
					startTime:$('#readStartTime').val(),
					endTime:$('#readEndTime').val(),
				}
			}


		}else if('unread' == isRead){
			if('无' != text){
				dateVal = {
					startTime:$('#unreadStartTime').val(),
					endTime:$('#unreadEndTime').val(),

				}
			}
		}


		//let dateVal = $(e).find('dt:first').html();
		//let $store = $(e).parent().parent().find('div:first h2:eq(1) span');
		let $store = $(e).parent().parent().parent().find('div:first h2:eq(1) span');
		let storeName = (isNull($store.attr('val-id')) == '') ? '' : $store.html();
		//let orgId = isNull($(e).parent().parent().find('div:first h2:first span').attr('val-id'));
		let orgId = isNull($(e).parent().parent().parent().find('div:first h2:first span').attr('val-id'));
		//$store.parent().next().find('span').html("已选时间");
		if (orgId == '') {
			alert("请先选择机构");
			$(e).val('');
			return false;
		}
		// readState = isRead;
		// _orgId = orgId;
		// _name = storeName;
		// _date = dateVal;

		if(isRead == 'read'){
			readPageNumber = 1;
			_readName = storeName;
			_readOrgId = orgId;
			_readDate = dateVal;
		}else if(isRead == 'unread'){
			unReadPageNumber = 1;
			_unReadName = storeName;
			_unReadOrgId = orgId;
			_unReadDate = dateVal;
		}
		ajaxPlan(e, isRead, orgId, storeName, dateVal);
		$store.parent().next().trigger('click');
	}

	function selectOnclickDate(e) {
		let orgId = isNull($(e).parent().prev().prev().find('span').attr('val-id'));
		if (orgId == '') {
			alert("请先选择机构");
			return false;
		}
	}

	//展示大图
	function imgDisplay(obj) {
		var src = $(obj).attr("src");
		var imgHtml = '<div style="width: 100%;height: 100vh;overflow: auto;background: rgba(0,0,0,0.5);text-align: center;position: fixed;top: 0;left: 0;z-index: 1000;display: flex;justify-content: center; align-items: center;"><img src=' + src + ' style="margin-top: 100px;width: 96%;margin-bottom: 100px;"><p style="font-size: 50px;position: fixed;top: 30px;right: 30px;color: white;cursor: pointer;" onclick="closePicture(this)">×</p></div>'
		$('body').append(imgHtml);
	}

	// 关闭大图
	function closePicture(obj) {
		$(obj).parent("div").remove();
	}

	// 跳转到发布看板
	function toWritePlan(e) {
		window.location.href = "/mobile/salesman/writePlan.jhtml";
	}

	// 签到
	function toSignIn(e) {
		window.location.href = "/mobile/salesman/signIn.jhtml";
	}

	// 跳转到计划详情页
	function toPlanDetails(e, pid) {
		window.location.href = "/mobile/salesman/planDetails.jhtml?planId="+pid;
	}

	var commTimeout;
	// 切换评论
	function togComment(e, pid) {
		clearTimeout(commTimeout);
		$('#commPid').val(pid);
		$('.comment-bottom').show();
		$('#commContent').focus();
	}
	//切换评论
	function clearTime(e) {
		clearTimeout(commTimeout);
	}
	// 隐藏评论框
	function hideCommBox(e) {
		commTimeout = setTimeout(function(){
			$('.comment-bottom').hide();
			$('#commPid').val("");
			$('#commContent').val("");
		}, 2000);
	}
	// 评论
	function toComments(e) {
		clearTimeout(commTimeout);
		var commContent = $('#commContent').val();
		if (isNull(commContent) == "") {
			alert("评论的内容不能为空！");
			return ;
		}
		$.ajax({
			type:'POST',
			url:'/mobile/salesman/comment.jhtml',
			data:{pid:$('#commPid').val(), content:commContent},
			success:function(data) {
				if (data.type == 'success') {
					$('#commContent').val("");
					window.location.reload();
				} else {
					alert("系统繁忙，请稍后再试！");
				}
			}
		})
	}
	// 点赞
	function onLike(e, pid) {
		$.ajax({
			type:'POST',
			url:'/mobile/salesman/like.jhtml',
			data:{planId:pid},
			success:function(data) {
				if (data.type == 'success') {
					if ($(e).hasClass('on')) {
						$(e).removeClass('on');
					} else {
						$(e).addClass('on');
					}
					$(e).html(data.content);
				} else {
					alert("系统繁忙，请稍后再试！");
				}
			}
		})
	}

	// 点击加载下一页数据
	function loadPlan(e,isRead, orgId, name, date,pageNumber) {
		var d = {
			isRead:isRead,
			name:isNull(name),
			orgId:isNull(orgId),
			pageNumber:(pageNumber+1),
			pageSize:size,
		};

		if(date != undefined){
			if(date.startTime!=undefined){
				d.startTime = date.startTime;
			}
			if(date.endTime != undefined){
				d.endTime = date.endTime;
			}
		}


		$.ajax({
			type:'POST',
			url:'/mobile/salesman/searchPlan.jhtml',
			//data:{isRead:isRead, name:isNull(name), orgId:isNull(orgId), date:date},
			data:d,
			success:function(data) {
				if (data.type == 'error') {
					alert("系统繁忙，请刷新页面再试试！");
				}



				let h5 = '';
				for (let i = 0; i < data.length; i++) {
					let row = data[i];
					let img = '/resources/images/mobile/default_tx.jpg';
					let useImg = isNull(row.plan.imageName) == '' ? img : isNull(row.plan.imageName)
					h5 += '<div class="kb_con_box">'
							+ '  <div class="kb_con_left">'
							+ '    <img src="'+ useImg +'"/><h1>'+ isNull(row.plan.name) +'</h1>'
							+ '  </div>'
							+ '  <div class="kb_con_right">' /* 计划项 */
							+ '    <ul class="kb_con_task_box" onclick="toPlanDetails(this, '+ row.plan.planId +')">'
							+ '      <li class="kb_con_right_title"><p class="kb_heng"></p><span>计划</span></li>';
					for (let j = 0; j < row.plan.planItemList.length; j++) {
						let planItem = row.plan.planItemList[j];
						let sta = {"1": "已完成", "2": "未完成", "3": "调整"};
						let staClass = {"1": "completed", "2": "undown", "3": "undown"};
						h5 += '  <li>'
								+ '    <h2><span>'+ (j+1) +'. </span>'+ planItem.content +'</h2>';
						if (isNull(planItem.status) != '') {
							h5 += '<p class="'+ staClass[isNull(planItem.status)] +'">'+ sta[isNull(planItem.status)] +'</p>';
						}
						h5 += '  </li>';
					}
					h5 += '    </ul>'
							+ '    <ul onclick="toPlanDetails(this, '+ row.plan.planId +')">'  /* 总结 */
							+ '      <li class="kb_con_right_title"><p class="kb_heng"></p><span>总结</span></li>';
					let summarySize = 0;
					for (let j = 0; j < row.plan.planItemList.length; j++) {
						let planItem = row.plan.planItemList[j];
						if (isNull(planItem.summary) != '') {
							h5 += '  <li><h3>'+ (j+1) +'. '+ isNull(planItem.summary) +'</h3></li>';
							summarySize++;
						}
					}
					if (summarySize == 0) {
						h5 += '  <img src="/resources/images/mobile/zwzj_2.jpg" style="width: 80px; margin: 5px 35%;"/>';
					}
					h5 += '    </ul>'
							+ '    <ul class="kb_con_right_img">';  /* 总结图片 */
					for (let j = 0; j < row.plan.planItemList.length; j++) {
						let planItem = row.plan.planItemList[j];
						for (let k = 0; k < planItem.attachList; k++) {
							let attach = planItem.attachList[k];
							if (k > 3) break;
							h5 += '<li><img src="'+ planItem.url +'" onclick="imgDisplay(this)"/></li>';
						}
					}
					let like = (row.like == 1) ? 'on' : '';  /* 时间、点赞、查看、评论、 */
					h5 += '    </ul>'
							+ '    <div class="kb_con_right_hour">'
							+ '      <h4 style="display: inline;padding-top:0px;">'+ row.plan.createDate +'</h4>'
							+ '      <div class="oper">'
							+ '        <span class="ico-like '+ like +'" onclick="onLike(this, '+ row.plan.planId +')">'+ row.plan.likeCount + '</span>'
							+ '        <span class="ico-see">'+ row.plan.viewCount +'</span>'
							+ '        <span class="ico-comment" onclick="togComment(this, '+ row.plan.planId +')">'+ row.comments.length +'</span> '
							+ '      </div>'
							+ '    </div>'
							+ '  </div>'
							+ '  <div class="clearfix"></div>'
							+ '  <ul class="kb_con_btoom_message">';
					for (let j = 0; j < row.comments.length; j++) {  /* 评论 */
						let commItem = row.comments[j];
						let commImg = '/resources/images/mobile/default_tx.jpg';
						let useCommImg = isNull(commItem.imageName) == '' ? commImg : isNull(commItem.imageName)
						h5 += ' <li>'
								+ '    <img src="'+ useCommImg +'"/>'
								+ '    <div class="kb_message_right">'
								+ '      <h1>'+ commItem.name +'</h1>'
								+ '      <h2>'+ commItem.createDate +'</h2>'
								+ '      <h3 style="white-space: normal;">'+ commItem.content +'</h3>'
								+ '    </div>'
								+ ' </li>';
					}
					h5 += '  </ul>'
							+ '</div>'


				}
				if (isRead == 'unread') {

					$('#unreadPlan').append(h5);
					unReadPageNumber += 1;
				} else if (isRead == 'read') {

					$('#readPlan').append(h5);
					readPageNumber += 1;
				}

				//加载下一页
				if(data.length < 1){
					alert('没有更多的数据~')
				}



			}
		})
	}


	//点击未读
	function unReadClick(e) {
		readState = 'unread';
		//unReadPagenumber = window.pageNumber;
		window.pageNumber = 1;
		unReadPageNumber = 1;
		ajaxPlan(e,readState,_unReadOrgId,_unReadName,_unReadDate)


	}

	//点击已读
	function readClick(e) {
		readState = 'read';
		//readPageNumber = window.pageNumber;
		window.pageNumber = 1;
		readPageNumber = 1;
		ajaxPlan(e,readState,_readOrgId,_readName,_readDate)

	}

	//点击我的
	function myClick() {
		readState = 'me';
	}

	//获取当前浏览器中的滚动事件
	// $(window).off("scroll").on("scroll", function () {
	//     //获取当前浏览器的滚动条高度
	//     var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
	//     //判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
	//     if (scrollHeight <= ($(window).scrollTop() + $(window).height())) {
	//         setTimeout(function () {
	//             if(readState == 'read'){
	//					loadPlan(e,readState,_readOrgId,_readName,_readDate,readPageNumber)
	//             }else if('unread'){
	//					loadPlan(e,readState,_unReadOrgId,_unReadName,_unReadDate,unReadPageNumber)
	//
	// 			}
	//
	//         },500);
	//     }
	// });


	function unReadLoadNext(e) {
		$(e).parent().hide();
		//$(e).text('加载中...')
		if(readState == 'read'){
			loadPlan(e,readState,_readOrgId,_readName,_readDate,readPageNumber)
		}else if(readState == 'unread'){
			loadPlan(e,readState,_unReadOrgId,_unReadName,_unReadDate,unReadPageNumber)
		}

		//避免重复提交
		setTimeout(function () {
			$(e).parent().show();
		},1500)


		//$(e).text('加载下一页')

	}

	function readLoadNext(e) {
		$(e).parent().hide();
		if(readState == 'read'){
			loadPlan(e,readState,_readOrgId,_readName,_readDate,readPageNumber)
		}else if(readState == 'unread'){
			loadPlan(e,readState,_unReadOrgId,_unReadName,_unReadDate,unReadPageNumber)
		}

		//避免重复提交
		setTimeout(function () {
			$(e).parent().show();
		},1500)
	}
</script>


</body>
</html>
