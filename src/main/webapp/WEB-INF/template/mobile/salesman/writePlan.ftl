<!DOCTYPE HTML>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>填写工作计划</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/new.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/raido1.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script src="/resources/js/rolldate.min.js" type="text/javascript"></script>
<style>
.txt.time, .txt.cho_icon { padding-right: 25px; }
.chaosongren{font-size: 14px;background: #fff;padding: 10px 20px;color: #000;overflow: hidden;}
#checkBox{display: none;position: absolute;top: 0;left: 0;width: 100%;min-height: 100%;z-index: 100;background: #eee;}
.checkBox{width: 100%;float: left;padding: 10px 20px;}
.checkBox label{display: block;width: 50%;font-size: 16px; margin: 0 0 6px 0;float: left;}
.checkBox label input{margin: 5px 5px 0;}
.shadeBtn{width: 50%;float: left;text-align: center;}
.shadeBtn input{background: #48A2FE;color: #fff;width: 40%;font-size: 16px;border: 1px solid #48A2FE;padding: 10px 0;}
.searchBox{border: 1px solid #ccc;width: 100%;padding: 10px 5px;font-size: 14px;}
.searcBtn{text-align: center;margin-top: 10px;padding: 10px 15px;font-size: 16px;border: 1px solid #48A2FE;background: #48A2FE;color: #fff;width: 100%;}
.ico-del-cc {
    background: url(/resources/images/button/ico-close01.png) no-repeat center;
    background-size: 59%;
    width: 22px;
    height: 22px;
    position: absolute;
    top: -5px;
    right: -6px;
    display: block;
    border-radius: 50%;
}
</style>
</head>
<body id="containt">
<div class="ser_tit_box">
    <h1>
        <img src="/resources/images/mobile/left.png" style="margin-left: 10px;" onclick="javascript :history.back(-1);">
        填写工作计划
        <span onclick="toSave(this)" style="right: 15px; position: inherit;">提交</span>
    </h1>
</div>
<form id="inputForm" action="#" method="post" type="ajax" validate-type="validate" style="margin-bottom: 70px;">
	<div class="space_box">
		<dl>
			<dt>计划时间</dt>
			<dd>
			    <input type="date" name="planTime" value="" class="txt time">
			    <!-- <input type="text" id="date-group-1" class="txt time" name="planTime" readonly placeholder="YYYY-MM-DD hh:mm:ss"> -->
			</dd>
		</dl>
	</div>
	<!-- <div class="space_box">
		<dl>
			<dt>抄送人</dt>
			<dd data-id="CcPeopleBox" onclick="showPup(this)">
                <input type="text" class="txt cho_icon" id="ccPeople" placeholder="请选择" name="" value="" readonly="readonly" />
                <input type="hidden" name="ccPeople.id" value="" />
            </dd>
        </dl>
	</div> -->
	
	<div class="space_box" style="position: relative;">
        <dl>
            <dt>抄送人</dt>
            <dd data-id="CcPeopleBox" id="CcPeople-dd" onclick="showShade(this)" style="margin-right:23px;">
                <input type="text" class="txt" id="personnel" placeholder="请选择" name="" value="" readonly="readonly" />
            </dd>
        </dl>
        <a href="javascript:void(0);" class="ico-del-cc" onclick="delCcPeople(this)" style="top:10px; right:10px;"></a>
    </div>
	
</form>

<!-- 隐藏数据 -->
<div id="hide-html" style="display:none;">
    <div class="plan_content" style="position:relative; overflow:visible; padding-top: 1px;">
        <a href="javascript:void(0);" class="ico-del" onclick="del(this)"></a>
        <div class="plan_content_title"><span></span><h1>计划<!-- <b class="serial">1</b> --></h1></div> 
        <dl>
            <dt>工作类型</dt>
            <dd>
                <select id="workType" class="cho_icon" onchange="handleWork(this)" dir="rtl">
                    <option value="1">促销执行</option>
                    <option value="2">门店培训</option>
                    <option value="3">客户拜访</option>
                    <option value="4">售后处理</option>
                    <option value="5">其他</option>
                </select>
            </dd>
        </dl>
        <dl id="customerType">
            <dt>客户类型</dt>
            <dd class="chooseBox" id="b">
                <label class="checkhid" data-id="b" style="display: inline-block;">
                    <input type="radio" name="" value="1" onclick="changeStore(this)"/>
                    <div class="check_box"></div>潜在客户
                </label>&nbsp;&nbsp;
                <label class="checkhid" data-id="b" style="display: inline-block;">
                    <input type="radio" name="" value="2" onclick="changeStore(this)" checked="checked"/>
                    <div class="check_box checked"></div>签约客户
                </label>
            </dd>
        </dl>
        <div class="plan_cu_box" id="store">
            <dl id="billInfo">
                <dt class="qy_cs" >客户名称</dt>
                <dd data-id="CustomerBox" onclick="showPup(this)">
                    <input type="text" class="cho_icon" id="customerName" placeholder="请选择" name="" value="" disabled />
                    <input type="hidden" name="" id="storeId" value="" />
                </dd>
            </dl>
        </div>      
        <dl id="shop">
            <dt>门店名称</dt>
	        <dd data-id="ShopCode" onclick="showPup(this)">
	            <input type="text" class="cho_icon" placeholder="请选择" id="shopName" name="" value="" disabled />
	            <input type="hidden" name="" id="shopId" class="text shopInfoId" value="" />
	        </dd>
        </dl>
        <div class="order-evaluation-textarea" id="content">
            <textarea name="reviewMemo" id="TextArea1" placeholder="请填写计划内容"></textarea>
        </div>
    </div>
</div>
<input type="button" class="plan_odd_btn" value="新增工作计划" onclick="addWorkPlan(this)" />   

<!-- 选择客户 -->
<div class="pup-obox" id="CustomerBox" style="font-size:12px;">
    <div style="position:fixed; width:100%;z-index:10; margin-left: -9px;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择客户</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="客户名称" id="searchByCustomerName"/>
            <input type="button" class="btn" value="搜索" onclick="searchCustomer()" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>

<!-- 选择门店（编码） -->
<div class="pup-obox" id="ShopCode">
    <div style="position:fixed; width:100%;z-index:10; margin-left: -9px;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择门店</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="门店名称搜索" id="searchByShopName"/>
            <input type="button" class="btn" value="搜索" onclick="searchShop()" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div>

<!-- 选择抄送人 -->
<!-- <div class="pup-obox" id="CcPeopleBox">
    <div style="position:fixed; width:100%;z-index:10; margin-left: -9px;">
        <div class="pup-header">
            <a href="javascript:void(0);" class="go-back js-cancle"></a>
            <div class="h-txt">请选择抄送人</div>
        </div>
        <div class="search-box" style="border-bottom:solid 1px #eee;">
            <input type="search" class="txt" placeholder="姓名" id="searchByName"/>
            <input type="button" class="btn" value="搜索" onclick="searchSalesman()" />
        </div>
    </div>
    <ul class="list-txt" style="position:relative;padding-top: 100px;"></ul>
</div> -->

<!-- 选择抄送人 - V2 -->
<div id="checkBox">
    <div style="background: #fff;position: absolute;top: 0;width: 100%;">
        <h3 style="font-size: 24px;text-align: center;padding-top: 15px;padding-bottom: 15px;">抄送人员选择</h3>
        <div style="padding:0 15px 20px;">
            <input type="text" id="peopleName" class="searchBox" placeholder="姓名" />
            <input type="submit" id="" class="searcBtn" onclick="ajaxSalesman(this)" value="确定搜索" />
        </div>
    </div>
    <div style="position:static;margin-top:185px;margin-bottom: 70px;background: #fff;overflow-y: auto;overflow-x:hidden;height: 100%;">
        <div class="checkBox" id="ccPeopleAll">
            <!-- <label><input type="checkbox" value="" /><span>无</span></label> -->
        </div>
    </div>
    <div style="overflow: hidden;position: fixed;bottom: 0;width: 100%;padding: 10px 0;background: #fff;">
        <div class="shadeBtn"><input type="submit" id="isOK" value="确认" onclick="isOKBtn();" /></div>
        <div class="shadeBtn"><input type="submit" id="isNO" value="取消" onclick="isNoBtn();" /></div>
    </div>
</div>


<script>
$("label.checkhid").live("click", function () {
    var id = $(this).attr("data-id");
    if($(this).find("input").attr("type")=="checkbox"){
        if ($(this).find("input[type=checkbox]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":checkbox").attr("checked", false);
        } else {
            $(this).find(".check_box").addClass("checked").find(":checkbox").attr("checked", true);
        }
    }else{
        if ($(this).find("input[type=radio]:checked").val() == undefined) {
            $(this).find(".check_box").removeClass("checked").find(":radio").removeAttr("checked"); 
        } else {
            $($("#"+id)).find(".check_box").removeClass("checked").find(":radio").attr("checked", false);
            $(this).find(".check_box").addClass("checked").find(":radio").attr("checked", true);
        }
    }
});

$().ready(function(){
	
	// 移动端日期
	new Rolldate({
        el: '#date-group-1',
        format: 'YYYY-MM-DD hh:mm:ss',
        beginYear: 2000,
        endYear: 2100
    })
	
	$(".js-cancle").click(function(){
        $(".pup-obox").hide()
        $("body").attr("style","overflow:auto")
        $(".info-btns").show();  // 显示提交按钮
    })
	
	addWorkPlan();
	
})

// 抄送人
function showShade(){
	ajaxSalesman();
    $('#checkBox').show();
}

// var chaosongren = [];  // id
var chaosongrenVal = []; // name

function isOKBtn(){
    $.each($('input:checkbox:checked'),function(){
//         chaosongren.push($(this).val());
        chaosongrenVal.push($(this).next().html());
        let h5 = '<input type="hidden" class="ccPeopleIds" name="ccPeopleIds" value="'+ $(this).val() +'" />';
        $('#CcPeople-dd').append(h5);
    });
//     chaosongren = chaosongren.join(',');
//     chaosongrenVal = chaosongrenVal.join(', ');
//     $('.ccPeopleIds').val(chaosongren);
    $('#personnel').val(chaosongrenVal.join(', '));
    $('#checkBox').hide();
}

function isNoBtn() {
    $('#checkBox').hide();
}

// 删除业务员
function delCcPeople(e) {
	$('.ccPeopleIds').remove();
	$('#personnel').val("");
	chaosongrenVal = [];
}

function ajaxSalesman() {
    $.ajax({
        type:'POST',
        url:'/mobile/salesman/getSalesman.jhtml',
        data:{name: $('#peopleName').val()},
        success:function(data) {
            var rows = data;
            var h5 = '';
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                h5 += '<label>'
                    + '<input type="checkbox" name="ccId" value="'+ isNull(row.id)+ '" />'
                    + '<span>'+ isNull(row.name) +'</span>'
                    + '</label>'
            }
            $('#ccPeopleAll').html(h5);
        }
    })
}

// 增加工作计划
function addWorkPlan(e) {
	let index = $('#inputForm').find('.plan_content').length;
	$('#hide-html').find('.serial').html(parseInt(index) + 1);
	$('#hide-html').find('#workType').attr("name", "plans[" + index + "].workType");  // 工作类型
	$('#hide-html').find('#customerType input').attr("name", "plans[" + index + "].customerType");
	$('#hide-html').find('#customerType dd').attr("id", "b" + index);
	$('#hide-html').find('#customerType label').attr("data-id", "b" + index);
	$('#hide-html').find('#store').find('#storeId').attr("name", "plans[" + index + "].store.id");
	$('#hide-html').find('#store').find('#customerName').attr("name", "plans[" + index + "].customerName");
	$('#hide-html').find('#shop').find('#shopId').attr("name", "plans[" + index + "].shop.id");
	$('#hide-html').find('#shop').find('#shopName').attr("name", "plans[" + index + "].shopName");
	$('#hide-html').find('#content textarea').attr("name", "plans[" + index + "].content"); // 计划内容
	$('#inputForm').append($('#hide-html').html());
}

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

//根据客户姓名搜索客户
function searchCustomer() {
    var e = $('#containt').find('dd[data-id="CustomerBox"]')[0]
    showPup(e);
}

//搜索门店
function searchShop() {
    var e = $('#containt').find('dd[data-id="ShopCode"]')[0]
    showPup(e);
}

//搜索业务员
/* function searchSalesman() {
    var e = $('#containt').find('dd[data-id="CcPeopleBox"]')[0]
    showPup(e);
} */

function showPup(e){
    var id = $(e).attr("data-id")
    $("body").attr("style","overflow:hidden")
    $(".pup-obox").hide()
    $(".info-btns").hide();  // 隐藏提交按钮
    $("#"+id).show()
    //选择客户
    if(id == "CustomerBox"){
    	var storeNameVal = $(e).find('input[id="customerName"]').attr("name");
        var storeIdVal = $(e).find('input[id="storeId"]').attr("name");
        var dataMap = {"storeNameVal": storeNameVal, "storeIdVal": storeIdVal};
        $('#'+id).find('ul').empty()
        $.ajax({
            type:'POST',
            url:'/member/store/findStoreMobileData.jhtml',
            data:{name:$('#searchByCustomerName').val()},
            success:function(data) {
                if(data.type == 'success'){
                    var rows = JSON.parse(data.content);
                    var sex = new Array("男", "女");
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        var html = "<li data-id='CustomerBox' onclick='selectItem(this, "+ JSON.stringify(dataMap) +")'><a href='#'>"
                                + "<div class='name'>"+ isNull(row.name) +"</div>"
                                + "<div class='fl'><span>机构：</span>"+ isNull(row.sale_org_name) +"</div>"
                                + "<div class='fr'><span>编码：</span>"+ isNull(row.out_trade_no) +"</div>"
                                + "</a>"
                                + "<input type='hidden' class='xsName' value='"+ isNull(row.name) +"'/>"
                                + "<input type='hidden' class='xsDealerName' value='"+ isNull(row.name) +"'/>"
                                + "<input type='hidden' class='xsStoreId' value='"+ isNull(row.id) +"'/>"
                                + "</li>";
                        $('#'+id).find('ul').append(html)
                    }
                }
            }
        })
    } else if(id == "ShopCode") {
    	var dearName = isNull($(e).parent().prev().find('input[id="storeId"]').val());
    	if (dearName == "") {
    		alert("请先选择客户");
    		$("body").attr("style", "overflow:auto");
    	    $(".pup-obox").hide();
    	    $(".info-btns").show();  // 显示提交按钮
    		return ;
    	}
    	var shopNameVal = $(e).find('input[id="shopName"]').attr("name");
        var shopIdVal = $(e).find('input[id="shopId"]').attr("name");
        var dataMap = {"shopNameVal": shopNameVal, "shopIdVal": shopIdVal};
    	// 选择门店
        $('#'+id).find('ul').empty();
        $.ajax({
            type:'POST',
            url:'/shop/shopInfo/select_shopInfo_data.jhtml',
            data:{distributorName: dearName,
                // sn:$('#searchByShopCode').val(),
                // authorizationCode:$('#searchByShopCode').val(),
                shopName:$('#searchByShopName').val(),
            },
            success:function(data) {
                if(data.type == 'success'){
                    var rows = JSON.parse(data.content).content;
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        var html = "<li data-id='ShopCode' onclick='selectItem(this, "+ JSON.stringify(dataMap) +")'><a href='#'>"
                                + "<div class='name'>"+ isNull(row.sn) +"</div>"
                                + "<div class=''><span>经销商：</span>"+ isNull(row.distributor_name) +"</div>"
                                + "<div class=''><span>门店名称：</span>"+ isNull(row.shop_name) +"</div>"
                                + "<div class=''><span>授权编号：</span>"+ isNull(row.authorization_code) +"</div>"
                                + "</a>"
                                + "<input type='hidden' class='xsShopName' value='"+ isNull(row.shop_name) +"'/>"
                                + "<input type='hidden' class='xsShopInfoId' value='"+ isNull(row.id) +"'/>"
                                + "<input type='hidden' class='xsDealer' value='"+ isNull(row.distributor_name) +"'/>" // 经销商
                                + "</li>";
                        $('#'+id).find('ul').append(html)
                    }
                }
            }
        })
    } else if(id == "CcPeopleBox") {
        $('#'+id).find('ul').empty();
        $.ajax({
            type:'POST',
            url:'/mobile/salesman/getSalesman.jhtml',
            data:{name: $('#searchByName').val()},
            success:function(data) {
                var rows = data;
                for (var i = 0; i < rows.length; i++) {
                    var row = rows[i];
                    var html = "<li data-id='CcPeopleBox' onclick='selectItem(this, "+ JSON.stringify(row) +")'><a href='#'>"
                            + "<div class='name'>"+ isNull(row.sn) +"</div>"
                            + "<div class=''><span>姓名：</span>"+ isNull(row.name) +"</div>"
                            + "<div class=''><span>手机：</span>"+ isNull(row.mobile) +"</div>"
                            /* + "<div class=''><span>状态：</span>"+ (isNull(row.is_enabled) == true ? '正常' : '禁用')  +"</div>" */
                            + "</a>"
                            + "</li>";
                    $('#'+id).find('ul').append(html)
                }
            }
        })
    }
}

// 将弹出框选择的数据回显到页面
function selectItem(e, dataMap){
    var id = $(e).attr("data-id")
    $("body").attr("style", "overflow:auto");
    $(".pup-obox").hide();
    $(".info-btns").show();  // 显示提交按钮
    if(id == "CustomerBox"){ //选择客户
        $('#inputForm').find('input[name="'+dataMap.storeIdVal+'"]').val($(e).find('input[class="xsStoreId"]').val());
        $('#inputForm').find('input[name="'+dataMap.storeNameVal+'"]').val($(e).find('input[class="xsDealerName"]').val());
    } 
    if(id == "ShopCode"){ // 选择门店
        $('#inputForm').find('input[name="'+dataMap.shopNameVal+'"]').val($(e).find('input[class="xsShopName"]').val());
        $('#inputForm').find('input[name="'+dataMap.shopIdVal+'"]').val($(e).find('input[class="xsShopInfoId"]').val());
    }
    if(id == "CcPeopleBox"){ // 选择抄送人
        //$('#inputForm').find('#ccPeople').val(dataMap.username).next().val(dataMap.id);
        $('#inputForm').find('#ccPeople').val(dataMap.name).next().val(dataMap.id);

    }
}

// 选中签约、潜在客户改变对应的值
function changeStore(e) {
	if ($(e).val() == "1") {
		$(e).parent().parent().parent().next().find('dd').removeAttr('onclick')
		      .find('#customerName').removeAttr('disabled').val("").removeClass('cho_icon').attr('placeholder', '请输入');
		$(e).parent().parent().parent().next().find('dd').find('#storeId').val("");
		
		$(e).parent().parent().parent().next().next().find('dd').removeAttr('onclick')
		      .find('#shopName').removeAttr('disabled').val("").removeClass('cho_icon').attr('placeholder', '请输入');
		$(e).parent().parent().parent().next().next().find('dd').find('#shopId').val("");
	} else {
		$(e).parent().parent().parent().next().find('dd').attr('onclick', 'showPup(this)').find('#customerName').val("").attr({disabled:'disabled', placeholder:'请选择'}).addClass('cho_icon');
		$(e).parent().parent().parent().next().next().find('dd').attr('onclick', 'showPup(this)').find('#shopName').val("").attr({disabled:'disabled', placeholder:'请选择'}).addClass('cho_icon');
	}
}

function handleWork(e) {
	if ($(e).val() == 5) {
		$(e).parent().parent().next().find('input[type=radio]').removeAttr("checked");
		$(e).parent().parent().next().find('input[type=radio]').next('div').removeClass("checked");
		$(e).parent().parent().next().find('input[type=radio]').attr("disabled", "disabled");
		$(e).parent().parent().next().next().find('input[id=customerName]').attr("disabled", "disabled").val("");
		$(e).parent().parent().next().next().find('dd').removeAttr("onclick");
		$(e).parent().parent().next().next().find('input[id=storeId]').val("");
		$(e).parent().parent().next().next().next().find('input[id=shopName]').attr("disabled", "disabled").val("");
		$(e).parent().parent().next().next().next().find('dd').removeAttr("onclick");
		$(e).parent().parent().next().next().next().find('input[id=shopId]').val("");
	} else {
		$(e).parent().parent().next().find('input[type=radio]').removeAttr("checked");
		$(e).parent().parent().next().find('input[type=radio]').removeAttr("disabled");
        $(e).parent().parent().next().find('input[type=radio]').next('div').removeClass("checked");
        $(e).parent().parent().next().next().find('dd').attr("onclick","showPup(this)");
        $(e).parent().parent().next().next().next().find('dd').attr("onclick", "showPup(this)");
	}
}

//删除附件
function del(e) {
    $(e).parent('div').remove()
}

// 保存计划
function toSave(e) {
	var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return ;
    }
    if (isNull($('#inputForm').find('input[name="planTime"]').val()) == '') {
    	alert("请先选择计划时间！");
    	return ;
    }
    $.ajax({
        type:'POST',
        url:'/mobile/salesman/savePlan.jhtml',
        data:$("#inputForm").serialize(),
        success:function(data) {
            if(data.type == 'success'){
                alert("保存成功");
                window.location.reload();
            } else {
                alert("保存失败, " + data.content);
            }
        }
    })
}

</script>
</body>
</html>
