<!DOCTYPE HTML>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>填写总结</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/new.css" rel="stylesheet" type="text/css" />
<style>
label.error {
    color: red;
    font-size: 12px;
    display: block;
    text-align: right;
    margin: -21px 20px 0 0;;
}
</style>
</head>

<body>
<div id="containt">
	<div class="ser_tit_box">
<!-- 		<img src="images/mobile/left.png"> -->
<!-- 		<h1>填写总结<span onclick="toSave(this)" style="right: 15px; position: inherit;">保存</span></h1> -->
		<h1>
	        <img src="/resources/images/mobile/left.png" style="margin-left: 10px;" onclick="javascript:history.back(-1);">
	        填写总结
	        <span onclick="toSave(this)" style="right: 15px; position: inherit;">保存</span>
	    </h1>
	</div>

	<div class="plan_content">
		<div class="plan_content_title">
			<span></span>
			<h1>计划</h1>
		</div>
		<dl>
		    [#assign workType=["促销执行", "门店培训", "客户拜访", "售后处理", "其他" ] /]
			<dt>工作类型</dt>
			<dd>${workType[pi.workType - 1] }</dd>
		</dl>
		<dl>
			<dt>计划时间</dt>
			<dd>${pi.plan.planTime}</dd>
		</dl>
		<dl>
			<dt>客户名称</dt>
			<dd>${pi.customerName}</dd>
		</dl>
		<dl>
			<dt>门店名称</dt>
			<dd>${pi.shopName}</dd>
		</dl>
		<div class="order-evaluation-textarea">
			<textarea name="content" id="TextArea1" onKeyUp="words_deal();" disabled="disabled">${pi.content}</textarea>
		</div>
	</div>
	<form id="inputForm" action="#" method="post" type="ajax" validate-type="validate" style="margin-bottom: 30px;">
		<div class="plan_content" style="padding-bottom: 12px;">
			<div class="plan_content_title">
				<span></span>
				<h1>总结</h1>
			</div>
			<dl>
			    <input type="hidden" name="id" value="${pi.id}" />
				<dt>完成情况</dt>
				<dd>
					<select name="status" class="cho_icon" dir="rtl">
					    <option value=""></option>
					    <option value="1" [#if pi.status=='1']selected[/#if]>已完成</option>
					    <option value="2" [#if pi.status=='2']selected[/#if]>未完成</option>
					    <option value="3" [#if pi.status=='3']selected[/#if]>调整</option>
					</select>
				</dd>
			</dl>
			<div class="order-evaluation-textarea">
				<textarea name="summary" id="summary" placeholder="请输入">${pi.summary}</textarea>
			</div>
	        
	        <!-- 图片上传 -->
	        <dl class="upload-bBox" style="border: solid 1px #eee; border-radius: 4px;overflow: hidden; padding: 10px;height:70px; width:105px;">
                <!-- <dt style="line-height: 24px; color: #999;">只能拍照<br/>照片带水印，时间地点</dt> -->
                <dd>
                    <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'planItemFile')">上传</a>
                </dd>
            </dl>
            <div class="atta-list" id="addPlanItemAttach">
                [#list pi.planItemAttachs as item]
                <div class="item" style="width:70px;height:70px;float: left; margin: 11px 14px 5px 0;">   
                    <a href="javascript:void(0);" class="ico-del" onclick="del(this)"></a>   
                    <img src="${item.url}" title="" style="" onclick='imgDisplay(this)'>
                    <input type="hidden" id="attachName" name="planItemAttachs[${item_index}].name" value="${item.name}">   
                    <input type="hidden" id="attachUrl" name="planItemAttachs[${item_index}].url" value="${item.url}">   
                    <input type="hidden" id="attachSuffix" name="planItemAttachs[${item_index}].suffix" value="${item.suffix}">
                </div>
                [/#list]
            </div>
		</div>
	</form>
	
	<!-- 文件上传 -->
    <form id="fileForm">
        <input type="file" name="file" id="planItemFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addPlanItemAttach', 'planItemAttachs')" style="display: none"/>
    </form>
</div>
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script>

// 图片数量 
var len = 0;

$().ready(function(){
	len = $('#addPlanItemAttach').find('div[class="item"]').size();
	
	// 表单验证
    $("#inputForm").validate({
        rules: {
        	status: "required",
        },
        messages: {
        	status: "请选择完成情况",
        },
        errorPlacement: function (error, element) { //指定错误信息位置
            error.insertAfter(element.parent().parent()); //将错误信息添加当前元素的父结点后面
        }
    })
})

// 展示大图
function imgDisplay(obj) {
    var src = $(obj).attr("src");
    var imgHtml = '<div style="width: 100%;height: 100vh;overflow: auto;background: rgba(0,0,0,0.5);text-align: center;position: fixed;top: 0;left: 0;z-index: 1000;display: flex;justify-content: center; align-items: center;"><img src=' + src + ' style="margin-top: 100px;width: 96%;margin-bottom: 100px;"><p style="font-size: 50px;position: fixed;top: 30px;right: 30px;color: white;cursor: pointer;" onclick="closePicture(this)">×</p></div>'
    $('body').append(imgHtml);
}

// 关闭大图
function closePicture(obj) {
    $(obj).parent("div").remove();
}

//添加附件
function addAttach(e, attachFile) {
    $('#fileForm').find('input').removeAttr("name");
    $('#' + attachFile).attr("name", "file");
    $('#' + attachFile).trigger('click'); 
}

/** 
 * 附件上传
 * @param attachId 所在input标签的id名
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param type 后台用来区分不同类型附件
 */
function fileUpload(e, attachId, paramAttachs, type) {
    var formData = new FormData($("#fileForm")[0]);
    $.ajax({
        type:'GET',
        url:'/common/fileurl.jhtml',
        success:function(data) {
            if(data.type == "success"){
                $.ajax({
                    type:'POST',
                    url: data.objx,
                    data:formData,
                    cache: false,  
                    contentType: false,  
                    processData: false, 
                    success:function(data_) {
                        data_ = JSON.parse(data_)
                        if(data_.message.type == "success"){
                        	var imgHtml = "<div class='item' style='width:70px;height:70px;float: left; margin: 11px 14px 5px 0;'>"
                        		        + "   <a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                        		        + "   <img src='" + data_.url + "' onclick='imgDisplay(this)'>"
                        		        + "   <input type='hidden' id='attachName' name='"+paramAttachs+"["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
                                        + "   <input type='hidden' id='attachUrl' name='"+paramAttachs+"["+len+"].url' value='"+data_.url+"'/>"
                                        + "   <input type='hidden' id='attachSuffix' name='"+paramAttachs+"["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
                        		        + "</div>";
                            $('#'+attachId).append(imgHtml);
                            len++;
                        }
                    }
                })              
            }
        }
    })
}

// 删除附件
function del(e) {
    $(e).parent('div').remove()
}

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

function toSave(e) {
	var flag = confirm("您确定要保存吗？");
    if (flag != true) {
        return false;
    }
    if (isNull($('#inputForm').find('#summary').val()) == '') {
        alert("请先填写总结！");
        return false;
    }
    let len = $('#addPlanItemAttach').find('div[class="item"]').size();
    if (len == 0) {
        alert("请先上传照片！");	
        return false;
    }
    if ($("#inputForm").valid()) {
    	$.ajax({
            type:'POST',
            url:'/mobile/salesman/updatePlanItem.jhtml',
            data:$("#inputForm").serialize(),
            success:function(data) {
                if(data.type == 'success'){
                    alert("保存成功");
                    window.location.reload();
                } else {
                    alert("保存失败, " + data.content);
                }
            }
        })
    } else {
        alert("请先输入必填的输入框，再进行提交！");
    }
}
</script>
</body>
</html>
