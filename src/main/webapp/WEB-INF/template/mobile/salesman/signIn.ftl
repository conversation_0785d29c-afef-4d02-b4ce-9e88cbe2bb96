<!DOCTYPE HTML>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<meta http-equiv="Cache-Control" content="no-siteapp" />
<title>签到</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/new.css" rel="stylesheet" type="text/css" />
		<style type="text/css">
			#imgInp{
				display: none;
			}
			#paizhao_Btn{
				display: inline-block;
				width: 100%;
				height: 50px;
				line-height: 50px;
				text-align: center;
				background: #008EFE;
				color: #fff;
				font-size: 16px;
			}
		</style>
</head>
<body>
<div id="containt">
	<div class="ser_tit_box">
<!-- 		<img src="/resources/images/mobile/left.png"> -->
<!-- 		<h1>我的签到<span onclick="toSignInRecoed(this)" style="right: 15px; position: inherit;">查看记录</span></h1> -->
		<h1>
            <img src="/resources/images/mobile/left.png" style="margin-left: 10px;" onclick="javascript :history.back(-1);">
	        我的签到
	        <span onclick="toSignInRecoed(this)" style="right: 15px; position: inherit;">查看记录</span>
	    </h1>
	</div>
<!-- 	<div class="check_in_time">当前时间：2019-10-15 17:00:00</div> -->
    <form id="inputForm" action="#" method="post" type="ajax" validate-type="validate" style="margin-bottom: 30px;">
		<div class="check_in_position">
			<h1 id="h-address"></h1>
			<input type="hidden" name="address" value=""/>
			<div id="allmap" style="width: 0;height: 0; "></div>
		</div>
		<div class="Punch">
			<h1 onclick="toSave(this)">签到</h1>
			<!-- <a href="#" onclick="toSave(this)"><img src="/resources/images/mobile/punch.png"/></a> -->
			<img src="/resources/images/mobile/punch.png"/>
		</div>
		<div class="plan_content" style="padding-bottom:10px;">
			<div class="order-evaluation-textarea">
				<textarea name="content" onKeyUp="words_deal();" placeholder="签到说明"></textarea>
			</div>
			
			<div><span id="paizhao_Btn">拍照</span></div>
			<!-- 图片上传 -->
            <!-- <dl class="upload-bBox" style="border: solid 1px #eee; border-radius: 4px;overflow: hidden; padding: 10px;height:70px; width:105px;">
                <!-- <dt style="line-height: 24px; color: #999;">只能拍照<br/>照片带水印，时间地点</dt> 
                <dd style="float:left;">
                    <a href="javascript:void(0);" class="a-upload" onclick="addAttach(this, 'signInFile')">上传</a>
					 <div>
						<img id="blah" src="" alt="签到" style="max-width: 100%;" />
					</div> 
                </dd> -->
            </dl> 
            <div class="atta-list" id="addSignInAttach"></div>
		</div>
	</form>
	
	<!-- 文件上传 -->
    <form id="fileForm">
    	<input type='file' id="signInFile" name="file" accept="image/*" capture="camera" onchange="fileUpload(this, 'addSignInAttach', 'signInAttach')" style="display: none"/>
        <!-- <input type="file" name="file" id="signInFile" class="webuploader-element-invisible" multiple="multiple" onchange="fileUpload(this, 'addSignInAttach', 'signInAttach')" style="display: none"/> -->
    </form>
</div>

<script src="/resources/js/reference/jquery.js" type="text/javascript"></script>
<!-- <script>var $ = $.noConflict();</script> -->
<!-- <script src="/resources/js/gps/zepto.js" type="text/javascript"></script>
<script src="/resources/js/gps/base.js" type="text/javascript"></script>
<script src="/resources/js/gps/gpsPostion.js" type="text/javascript"></script> -->
<script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak=PlhFWpA02aoURjAOpnWcRGqw7AI8EEyO"></script>
<!-- <script src="//cdn.jsdelivr.net/npm/eruda"></script> -->
<script>
// eruda.init();

/*$(".Punch").click(function() {
	$(".pup-mask").show();
	$(".screen-box").css("top", "18rem")
});
$(".screen-box").click(function() {
	$(".pup-mask").hide();
	$(".screen-box").css("top", "100%")
}); */

//展示大图
function imgDisplay(obj) {
    var src = $(obj).attr("src");
    var imgHtml = '<div style="width: 100%;height: 100vh;overflow: auto;background: rgba(0,0,0,0.5);text-align: center;position: fixed;top: 0;left: 0;z-index: 1000;display: flex;justify-content: center; align-items: center;"><img src=' + src + ' style="margin-top: 100px;width: 96%;margin-bottom: 100px;"><p style="font-size: 50px;position: fixed;top: 30px;right: 30px;color: white;cursor: pointer;" onclick="closePicture(this)">×</p></div>'
    $('body').append(imgHtml);
}

// 关闭大图
function closePicture(obj) {
    $(obj).parent("div").remove();
}

// 百度地图API功能
var map = new BMap.Map("allmap");
var point = new BMap.Point(116.331398,39.897445);
map.centerAndZoom(point,12);

var geolocation = new BMap.Geolocation();
//开启SDK辅助定位
// geolocation.enableSDKLocation();
geolocation.getCurrentPosition(function(r){
    if(this.getStatus() == BMAP_STATUS_SUCCESS){
    	
        var mk = new BMap.Marker(r.point);
        map.addOverlay(mk);//标出所在地
        map.panTo(r.point);//地图中心移动
//         alert('您的位置：'+r.point.lng+','+r.point.lat);
        point = new BMap.Point(r.point.lng,r.point.lat);//用所定位的经纬度查找所在地省市街道等信息
        var gc = new BMap.Geocoder();
        gc.getLocation(point, function(rs){
        	console.log("---前---");
        	console.dir(point);
            var addComp = rs.addressComponents; 
            $('#h-address').html(rs.address);
            $('#inputForm').find('input[name="address"]').val(rs.address);
//             alert(rs.address);
//             translate(point)
        });
        exchange(); 
    }
    else {
//         alert('failed'+this.getStatus());
    }        
},{enableHighAccuracy: true})

//将GPS坐标转换成百度地图的坐标
function exchange(){
	var convertor = new BMap.Convertor();//实例转换器
    var pointArr = [];
    pointArr.push(point);
    convertor.translate(pointArr,1,5,translateCallback);
}
function translateCallback(data){
    if(data.status==0){
        var marker = new BMap.Marker(data.points[0]);
        map.addOverlay(marker);
        map.setCenter(data.points[0]);
    }
    console.log("ppppppppp");
    console.dir(data);
}

/* geolocation.getCurrentPosition(function(r){
    if(this.getStatus() == BMAP_STATUS_SUCCESS){
        var mk = new BMap.Marker(r.point);
        map.addOverlay(mk);
        map.panTo(r.point);
        console.log("H5定位：" +r.point.lng+','+r.point.lat);
//         alert('您的位置：'+r.point.lng+','+r.point.lat);
    }
    else {
        alert('failed'+this.getStatus());
    }        
});
 */
/* function translate(point) {
    var convertor = new BMap.Convertor();//实例转换器
    var pointArr = [];
    pointArr.push(point);//待转换的坐标 可批量
    convertor.translate(pointArr, 1, 5, function(data){
    	console.log("---转换---");
    	console.dir(data);
    	var gc = new BMap.Geocoder();
    	point2 = new BMap.Point(data.points[0].lng, data.points[0].lat);
    	console.log(point2);
        gc.getLocation(point2, function(rs){
            var addComp = rs.addressComponents; 
//             $('#h-address').html(rs.address);
//             $('#inputForm').find('input[name="address"]').val(rs.address);
            console.dir(rs); 
//             alert(rs.address);
        });
    })
} */
//关于状态码
//BMAP_STATUS_SUCCESS   检索成功。对应数值“0”。
//BMAP_STATUS_CITY_LIST 城市列表。对应数值“1”。
//BMAP_STATUS_UNKNOWN_LOCATION  位置结果未知。对应数值“2”。
//BMAP_STATUS_UNKNOWN_ROUTE 导航结果未知。对应数值“3”。
//BMAP_STATUS_INVALID_KEY   非法密钥。对应数值“4”。
//BMAP_STATUS_INVALID_REQUEST   非法请求。对应数值“5”。
//BMAP_STATUS_PERMISSION_DENIED 没有权限。对应数值“6”。(自 1.1 新增)
//BMAP_STATUS_SERVICE_UNAVAILABLE   服务不可用。对应数值“7”。(自 1.1 新增)
//BMAP_STATUS_TIMEOUT   超时。对应数值“8”。(自 1.1 新增)

// GPS坐标(WGS-84)–>百度坐标
/* function translate(point){
    var convertor = new BMap.Convertor();//实例转换器
    var pointArr = [];
    pointArr.push(point);//待转换的坐标 可批量
    convertor.translate(pointArr, 1, 5, function (data){
      if(data.status === 0) {
        var marker = new BMap.Marker(data.points[0]);//转换成功后的坐标
        map.addOverlay(marker);
        map.setCenter(data.points[0]);
      }
    })
} */

// 图片数量 
var len = 0;

$().ready(function(){
    len = $('#addSignInAttach').find('div[class="item"]').size();
});

var image = new Object();
image.status = 0;

//添加附件
function addAttach(e, attachFile) {
	$('#fileForm').find('input').removeAttr("name");
	$('#' + attachFile).attr("name", "file");
	$('#' + attachFile).trigger('click');
}

/** 
 * 附件上传
 * @param attachId 所在input标签的id名
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param type 后台用来区分不同类型附件
 */
function fileUpload(e, attachId, paramAttachs, type) {
	image.status = 2;
    var formData = new FormData($("#fileForm")[0]);
    $.ajax({
        type:'GET',
        url:'/common/fileurl.jhtml',
        success:function(data) {
            if(data.type == "success"){
            	$.ajax({
                    type:'POST',
                    url: data.objx,
                    data:formData,
                    cache: false,  
                    contentType: false,  
                    processData: false, 
                    success:function(data_) {
                        data_ = JSON.parse(data_)
                        if(data_.message.type == "success"){
                        	image.status = 1;
                            var imgHtml = "<div class='item' style='width:70px;height:70px;float: left; margin: 11px 14px 5px 0;'>"
                                        + "   <a href='javascript:void(0);' class='ico-del' onclick='del(this)'></a>"
                                        + "   <img src='" + data_.url + "' onclick='imgDisplay(this)'>"
                                        + "   <input type='hidden' id='attachName' name='"+paramAttachs+"["+len+"].name' value='"+data_.file_info.name.split('.')[0]+"'/>"
                                        + "   <input type='hidden' id='attachUrl' name='"+paramAttachs+"["+len+"].url' value='"+data_.url+"'/>"
                                        + "   <input type='hidden' id='attachSuffix' name='"+paramAttachs+"["+len+"].suffix' value='"+data_.file_info.name.split('.')[1]+"'/>"
                                        + "</div>";
                            $('#'+attachId).append(imgHtml);
                            len++;
                        }
                    }
                })              
            }
        }
    })
}

// 删除附件
function del(e) {
	$(e).parent('div').remove()
}

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

function toSignInRecoed(e) {
	window.location.href = "/mobile/salesman/signInRecord.jhtml";
}

var flag = 1;
function toSave(e) {
	if (flag != 1) {
		alert("您的签到太频繁了，请稍后再试！");
		return false;
	} else {
		flag = 0;
		$.ajax({
	        type:'POST',
	        url:'/mobile/salesman/saveSignIn.jhtml',
	        data:$("#inputForm").serialize(),
	        success:function(data) {
	            if(data.type == 'success'){
	                alert("签到成功");
	                toSignInRecoed();
	            } else {
		            if(image.status == 2){
		            	flag = 1;
		        		alert("拍照图片正在上传请稍等");
		        	}else if(image.status == 0){
		        		flag = 1;
		                alert("签到失败, " + data.content);		        	
		        	}
	            }
	        }
	    });
	}
	var t1 = window.setTimeout(function() {
		flag = 1;
	},10000);
}
if($('#blah').attr('src') == ''){
	$('#blah').hide();
}
function readURL(input) {
   if (input.files && input.files[0]) {
		var reader = new FileReader();
		//var file = input.files[0];
		
		var imgurl = "";
		reader.onload = function (input) {
			imgurl = input.target.result;
			console.log(imgurl);
        	//$('#blah').attr('src', e.target.result);
			//$('#blah').show();
       	}
       	reader.readAsDataURL(input.files[0]);   
	}
}
$("#imgInp").change(function(){
   readURL(this);
   fileUpload();
});
$('#paizhao_Btn').click(function(){
	addAttach(this,'signInFile');
});
</script>
</body>
</html>
