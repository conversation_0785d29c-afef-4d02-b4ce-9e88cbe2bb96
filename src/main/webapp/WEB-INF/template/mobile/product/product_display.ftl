<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title>商品列表</title>
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">

var productCategoryId = "";
//当前页数
var pageNumber = 1;

$().ready(function(){
	//加载产品数据
	ajaxListData()
	//根据商品名称搜索商品
	/* $("#product_name").blur(function(){
		pageNumber = 1;	//重置页数为第一页
		ajaxListData()
	})
	$("#product_name").keypress(function (e) {
        if (e.which == 13) {
        	pageNumber = 1;	//重置页数为第一页
        	ajaxListData()
        }
	}) */
})

function getOneNodes(){
	$.ajax({
		type:'POST',
		url:'/product/product/getNodes.jhtml',
		data:{},
		success:function(data) {
			$('.one-clas').empty();//清空第一层所有数据
			var rows = data;
			var htmlTotal = '';
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				var html='<dl class="item">'
				+'<dt onclick="getTwoNodes(this,'+row.id+')" data="'+row.id+'">'+row.name+'</dt>'
				+'<dd class="two-clas"></dd>'
			    +'</dl>';
			    htmlTotal += html;
			}	
			$('.one-clas').html(htmlTotal); 
		}
	});
}
function getTwoNodes(e,parentId){
	$('.one-clas').find('dl').removeClass('cur');
	$('.two-clas').find('dl').removeClass('cur');
	$('.three-clas a').removeClass('cur');
	$(e).parent('dl').addClass('cur');
	$.ajax({
		type:'POST',
		url:'/product/product/getNodes.jhtml',
		data:{id:parentId},
		success:function(data) {
			$(e).parent().find('.two-clas').empty();//清空内所有
			var rows = data;
			var htmlTotal = '';
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				var html = '';
				html = '<dl class="item">'
				    +'<dt onclick="getThreeNodes(this,'+row.id+')" data="'+row.id+'">'+row.name+'</dt>'
						+'<dd class="three-clas">'
						+'</dd>'
					+'</dl>';
				htmlTotal += html;
			}
			$(e).parent().find('.two-clas').append(htmlTotal); 
		}
	});
}
function getThreeNodes(e,parentId){
	$('.two-clas').find('dl').removeClass('cur');
	$('.three-clas a').removeClass('cur');
	$(e).parent('dl').addClass('cur');
	$.ajax({
		type:'POST',
		url:'/product/product/getNodes.jhtml',
		data:{id:parentId},
		success:function(data) {
			$(e).parent().find(".three-clas").empty();
			var rows = data;
			var htmlTotal = '';
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				var html = '';
				html = '<a href="javascript:" onclick="toCurThree(this)" data="'+row.id+'">'+row.name+'</a>';
				htmlTotal += html;
			}
			$(e).parent().find(".three-clas").append(htmlTotal); 
		}
	});
}

// 重置选择
function resetSelect() {
	getOneNodes();
}
// 完成选择
function completeSelect() {
	// 第三层
	var three = $('.three-clas').children('.cur').attr('data');
	var two = $('.two-clas').children('.cur').find('dt').attr('data');
	var one = $('.one-clas').children('.cur').find('dt').attr('data');
	if (three != null && three != "undefined" && three != "") {
		productCategoryId = parseInt(three);
	} else if (two != null && two != "undefined" && two != "") {
		productCategoryId = parseInt(two);
	} else if (one != null && one != "undefined" && one != "") {
		productCategoryId = parseInt(one);
	} else {
		productCategoryId = "";
	}
	pageNumber = 1;	//重置页数为第一页
 	ajaxListData();
	$('body').removeClass('hidden');
	$('.filter-box').removeClass('cur');
	$('.filter-box').children('.c').css({"display":"none"});
}

function toCurThree(e) {
	$('.three-clas a').removeClass('cur');
	$(e).addClass('cur');
}

function search() {
	pageNumber = 1;	//重置页数为第一页
	productCategoryId = "";
 	ajaxListData()
}
 
// 获取指定类目下的商品
function targetCategory(id) {
	productCategoryId = parseInt(id);
	pageNumber = 1;	//重置页数为第一页
 	ajaxListData()
}

// 点击全部
function allData(e) {
	productCategoryId = "";
	pageNumber = 1;	//重置页数为第一页
	$('a').removeClass("on");
	$(e).addClass("on");
 	ajaxListData()
}
function targetCategory(e, id) {
	productCategoryId = parseInt(id);
	pageNumber = 1;	//重置页数为第一页
	$('a').removeClass("on");
	$(e).addClass("on");
 	ajaxListData()
}

function ajaxListData(){
	$('.pro-Slist').find('ul').empty()
	$.ajax({
		type:'POST',
		url:'/mobile/product/list_photo_data.jhtml',
		//data:{flag:0,index:1,keyWords:$('#product_name').val()},
		data:{productCategoryId:productCategoryId,keyWords:$('#product_name').val(),storeId:$('#storeId').val(),storeName:$('#storeName').val(),pageNumber:pageNumber},
		success:function(data) {
			fillData(data);
		}
	})
}

/* 20190423 上拉加载数据 */
//获取当前浏览器中的滚动事件
$(window).off("scroll").on("scroll", function () {
	//获取当前浏览器的滚动条高度
	var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight; 
   	//判断当前浏览器滚动条高度是否已到达浏览器底部，如果到达底部加载下一页数据信息
	if (scrollHeight <= ($(window).scrollTop() + $(window).height())) { 
		setTimeout(function () {
			$.ajax({
       			type:'POST',
       			url:'/mobile/product/list_photo_data.jhtml',
       			//data:{flag:0,index:1,keyWords:$('#product_name').val()},
       			data:{productCategoryId:productCategoryId,keyWords:$('#product_name').val(),storeId:$('#storeId').val(),storeName:$('#storeName').val(),pageNumber:pageNumber},
       			success:function(data) {
       				fillData(data);
       			}
       		})
		},500)
		//设置当前页数
		pageNumber += 1;
	}
});

function fillData(data) {
	if(data.type == 'success'){
		var rows = JSON.parse(data.content).content;
		var totalPages = JSON.parse(data.content).totalPages;//总页数
		for (var i = 0; i < rows.length; i++) {
        	var row = rows[i];
       		var html = "<li onclick=productDetail(this)>" 
       			+ "<input type='hidden' value='"+row.level_Id+"' class='product_grade'>"
       			+ "<a>"
				+ "<div class='pic'>";
			if(row.image == null){
       			html += "<img src='/resources/images/default-img190.png'>"
       		}else{
       			html += "<img src='"+row.image+"'>"
       		}
			html += "    <div class='ico-see'>点击量: " + row.hits + "</div>"
				+ "</div>"
				+ "<b class='name'>" + row.name + "</b>"
				+ "<p>型号: " + row.model + "</p>";
				if (typeof row.spec === 'undefined') {
					html += "<p>规格: </p>"
				} else if (row.spec == "") {
					html += "<p>规格: </p>"
				} else {
					html += "<p>规格: " + row.spec + "</p>"
				}
				html += "<p>等级: "+row.levelName;
			html += "</p>"
				+ "<p>分类: "+ row.product_category_name +"</p>"
				+ "<input type='hidden' name='productId' id='productId' value='"+row.id+"'/>"
				+ "</a>"
				+ "</li>";
            if(pageNumber <= totalPages){
            	$('.pro-Slist').find('ul').append(html)
            }
		}
	}
}

//商品详情
/* function productDetail(id){
	window.location.href = "/mobile/product/product_detail.jhtml?flag="+1+"&id="+id
} */

//商品详情
function productDetail(e){
	var productId = $(e).find("#productId").val() 
	var product_grade = $(e).find(".product_grade").val();
	window.location.href = "/mobile/product/product_detail.jhtml?flag="+1+"&id="+productId+"&product_grade="+product_grade
}

// 导航显示更多
function changeFilter(e){
	$('.one-clas').html("");
	getOneNodes();
	$(e).closest(".filter-box").find(".c").slideToggle()
	$(e).closest(".filter-box").toggleClass("cur")
	$("body").toggleClass("hidden")
}
</script>
</head>
<body>
[#--
<!-- 
<div class="header">
	<a href="javascript:history.go(-1);" class="go-back"></a>
	<div class="h-txt">产品展示</div>
	<input type="hidden" name="productCategoryId" value="" id="${productCategoryId}">
	<input type="hidden" name="storeId" id="storeId" value="${storeId}"/>
	<input type="hidden" name="storeName" id="storeName" value="${storeName}"/>
</div>
-->
<!-- <input type="hidden" name="productCategoryId" value="" id="${productCategoryId}"> -->
--]
<input type="hidden" name="storeId" id="storeId" value="${storeId}"/>
<input type="hidden" name="storeName" id="storeName" value="${storeName}"/>
<div class="containt">
	<!-- 搜索 -->
	<div class="search-box" style="position:fixed; width:100%;z-index:10;border-bottom:solid 1px #eee; margin-top:-6px;">
		<input type="text" class="txt" id="product_name" placeholder="请输入产品名称"/>
		<input type="submit" class="btn" value="搜索" onclick="search()">
	</div>
	<!-- 导航列 -->
	<div class="filter-box mt6" style="padding-top: 46px;">
		<div class="t">
			<a onclick="allData(this)" class="on">全部</a>
			<!-- <a onclick="targetCategory(this, ${productCategorys[0].id})">${productCategorys[0].name}</a>
			<a onclick="targetCategory(this, ${productCategorys[1].id})">${productCategorys[1].name}</a> -->
			<i class="ico-arrow" onclick="changeFilter(this)"></i>
		</div>
		<div class="c">
			[#--
			<!-- [#list productCategorys as pc]
			[#if pc.parent??]
		   	[#else]
		   		<a onclick="targetCategory(this, ${pc.id})">${pc.name}</a>
		    [/#if]
			[/#list] -->
			--]
			
			<div class="classify-box">
				<div class="one-clas">

				</div>
			</div>
			<div class="btns">
				<input type="button" class="btn" value="重置" onclick="resetSelect()" />
				<input type="button" class="btn btn-blue" value="完成" onclick="completeSelect()" />
			</div>
		</div>
	</div>
	<!-- 产品列表 -->
	<div class="pro-Slist">
    	<ul class="pro-list02">
        	
        </ul>
    </div>
</div>

</body>
</html>