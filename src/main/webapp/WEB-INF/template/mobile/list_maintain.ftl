<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title>25周年</title>
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<style>
	div
	{
		font-size:20px;
	}
	input
	{
		font-size:18px;
	}
 .test{
    background-color: #008CBA; 
	 border-radius: 10%;
    border: 1px;
    color: white;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
}

table
{
    border-collapse:collapse;
}
	
table,td,th
{
	font-size:20px;
    border: 2px solid black;
	text-align:center;
	height:30px;
	padding:10px;
	margin-left:auto;
	margin-right:auto;
}

</style>
</head>
<body class="bgFFF">
<div class="header">
	<a href="javascript:go(-1);" class="go-back"></a>
	<div class="h-txt">25周年超级盛典数据收集</div>
</div>
<div >
	<div >
		<div><center>签单量：<input type="text" class="phone" onkeyup="value=value.replace(/[^\d]/g,'') "  value ="${OrderQtys}"  id="OrderQty"/></center></div>
       	<div><center>访客量：<input type="text" class="phone" onkeyup="value=value.replace(/[^\d]/g,'') "  value ="${customerss}"  id="customers"/></center></div>
		<div><center>报名数：<input type="text" class="phone" onkeyup="value=value.replace(/[^\d]/g,'') "  value ="${SignQtys}"   id="SignQty"/></center></div>
		<div><center><input type="hidden" class="phone" onkeyup="value=value.replace(/[^\d]/g,'') "  value ="${themeId}" id="themeId"/></center></div>
        <center><button type="button" class="test" onclick="submit()">提交</button></center>
        <div id="errorText"></div>
    </div>
</div>
<script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script>
<script type="text/javascript">



function submit(){
	$.ajax({
		type:'POST',
		url:'/mobile/theMe/save.jhtml',
		data:{language:'ChinaCN',OrderQty:$('#OrderQty').val(),customers:$('#customers').val(),SignQty:$('#SignQty').val(),themeId:$('#themeId').val()},
		success:function(data) {
			if(data.type == 'success'){
				window.location.href = '/mobile/theMe/list_maintain.jhtml?themeId='+$('#themeId').val()
			}else{
				$('#errorText').empty()
				$('#errorText').append(data.content)
			}
		}
	})
}
</script>
<table border="2" cellspacing="0">
	<caption>统计信息：</caption>
<tr>
	<th>类别</th>
	<th>目标</th>
	<th>累计</th>
	<th>达成率%</th>
</tr>   
<tr>
	<td>签单量</td>
	<td>${orderNum}</td>
	<td>${OrderQty}</td>
	<td>${orderRate}</td>
</tr>   
<tr>
	<td>访客量</td>
	<td>${customersNum}</td>
	<td>${customers}</td>
	<td>${customersRate}</td>
</tr>  
<tr>
	<td>报名量</td>
	<td>${signNum}</td>
	<td>${SignQty}</td>
	<td>${signRate}</td>
</tr>  

</table>
</body>
</html>
