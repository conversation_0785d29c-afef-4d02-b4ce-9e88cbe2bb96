<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="telephone=no" name="format-detection">
<meta http-equiv="Cache-Control" content="no-siteapp">
<title></title>
<link href="/resources/css/mobile/swiper.min.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mobile/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
function submit(){
	if ($('#isRememberUsername').prop("checked")) {
     	addCookie("memberusername", $('#username').val(), {expires: 7 * 24 * 60 * 60});
     	addCookie("password", $('#password').val(), {expires: 7 * 24 * 60 * 60});
	} else {
     	removeCookie("memberusername");
     	removeCookie("password");
	}
	
	$.ajax({
		type:'POST',
		url:'/login/submit.jhtml',
		data:{language:'ChinaCN',username:$('#username').val(),password:$('#password').val(),companyName:$('#companyName').val()},
		success:function(data) {
			if(data.type == 'success'){
				window.location.href = '/mobile/index.jhtml'
			}else{
				$('#errorText').empty()
				$('#errorText').append(data.content)
			}
		}
	})
}

$().ready( function() {

    var $username = $("#username");
    var $password = $("#password");
    var $isRememberUsername = $("#isRememberUsername");
    
    // 记住用户名
    if(getCookie("memberusername") != null) {
	    $isRememberUsername.prop("checked", true);
	    $username.val(getCookie("memberusername"));
	    $password.val(getCookie("password"));
	    $password.focus();
	} else {
	    $isRememberUsername.prop("checked", false);
	    $username.focus();
    }

});
</script>
</head>
<body>

<div id="containt">
	<div class="login-banner" style="background-image: url(/resources/images/mobile/test002.jpg)"></div>

	<div class="login-form">
		<div class="login-pic"><img src="/resources/images/mobile/login-pic.png"></div>
		<input type="text" class="txt" placeholder="用户名" id="username">
		<input type="password" class="txt" placeholder="登录密码" id="password">
		<div><input type="hidden" class="code" placeholder="企业简称或全称" value="大自然" id="companyName"/></div>
		<div class="clear">
			<label class="fl"><input type="checkbox" id="isRememberUsername" style="width: 14px;height:14px;vertical-align: middle;"> 记住密码</label>
		</div>
		<input type="button" class="btn-blue btn" value="登录" onclick="submit()">
		<div id="errorText"></div>
	</div>
</div>

<!-- <script src="/resources/js/jquery-1.7.2.min.js" type="text/javascript"></script> -->

</body>
</html>
