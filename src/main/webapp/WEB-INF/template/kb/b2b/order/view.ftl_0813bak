<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("订单详情")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
	#atc th,#atc td{border: solid 1px #F7F7F7;}
</style>
<script type="text/javascript">
//TCL获取可发货余额（ERP）
function getTclBalance(){
	var outTradeNo = $("#outTradeNo").val();
	ajaxSubmit('',{
		method:'post',
		url:'/intf/intfRequest/getTclBalance.jhtml?storeNo='+outTradeNo,
		async: false,
		callback: function(resultMsg) {
			var balance = resultMsg.objx;
			if(balance==null)balance=0;
			$("#balance").text(currency(balance,true));
		}
	})
}

 //更换地址（ERP）
function selectTclAddree(){
		 var storeNo = $("#outTradeNo").val();
		 $("#addReceiveAddress").bindQueryBtn({
    	        type:'store',
    	        bindClick:false,
    	        title:'${message("更换地址(ERP)")}',
    	        url:'/intf/intfRequest/selectTclAddree.jhtml?storeNo='+storeNo,
    	        callback:function(rows){
    	            if(rows.length>0){
    	            	var row = rows[0];
    	                $("input[name='consignee']").attr("value",row.consignee);
    	                $("input[name='phone']").attr("value",row.phone);
    	                $("input[name='address']").attr("value",row.consigaddress);
    	                
    	                /**
    	                $(".select_area").find(".fieldSet").empty();
    	                var areaId = row.area;
    	                if(areaId==null)areaId='';
    	                var areaName = row.area_full_name;
    	                if(areaName==null)areaName='';
    	                var treePath = row.tree_path;
    	                if(treePath==null)treePath='';
    	               // $(".select_area").find(".fieldSet").append('<input type="hidden" id="areaId" name="areaId"  value="'+areaId+'" treePath="'+treePath+'" />');
    	                //地区选择
    	               // $("#areaId").lSelect();
    	                $("input[name='areaId']").val(areaId);
    	                $("input[name='areaName']").val(areaName);
    	                */
    	                
    	            }
    	        }   
    	    });
}

</script>
<script type="text/javascript">
function countTotal(){
	var $input = $("input.quantity");
	var total = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var price = Number($this.closest("tr").find("input.price").val());
		var amount = accMul(Number($this.val()),price).toFixed(4);
		if(isNaN(amount)){
			amount = 0;
		}
		total = accAdd(total,amount).toFixed(4);
		$tr.find(".trprice").html(currency(amount,true));
	});
	$("#amount").text(currency(total,true));
	var usePolicyPrice=$("input.usePolicyPrice").val();
	$("#afterTotal").text(currency(total-usePolicyPrice,true));
}
function countVolume(){
	var $input = $("input.quantity");
	var totalVolume = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var volume = Number($tr.find("input.volume").val());
		var volumes = accMul(Number($this.val()),volume).toFixed(4);
		if(isNaN(volumes)){
			volumes = 0.00;
		}
		totalVolume = accAdd(totalVolume,volumes).toFixed(4);
	});
	$("span[name='volumeall']").text(totalVolume);
	$("input[name='volume']").val(totalVolume);
}
function editQty(t,e){
	if(extractNumber(t,3,false,e)){
		countTotal();
		countVolume();
		var id = $(t).attr("itemId");
		var itemOrg = Number($(t).attr("org"));
		var qty = Number($(t).val());
		if(isNaN(qty)){
			qty = 0;
		}
		
		var $input = $("input.cq[parentId='"+id+"']");
		$input.each(function(){
			var $this = $(this);
			var org = Number($this.attr("org"));
			var value = qty*org/itemOrg;
			$this.val(value);
			$this.next(".qty-text").text(value);
		})
	}
}
function editPrice(t,e){
	if(extractNumber(t,2,false,e)){
		countTotal();
	}
}

$().ready(function() {

	var $inputForm = $("#inputForm");
	// 表单验证
	$.validator.addClassRules({
		pprice: {
			required: true
		},
		quantity: {
			required: true
		},
		pVonderCode: {
			required: true
		},
		pModel: {
			required: true
		},
		pName: {
			required: true
		}
	});

/**设置留言*/
[#if isEdit!=0]
try{
	parent.set_list_tb(1,'message_board.jhtml?id=${order.id}',null,true)
}catch(e){}
[/#if]
[#if readOnly!=1]
 $("#wf_area").load("/wf/wf.jhtml?wfid=${order.wfId}");
[/#if]

var $areaId = $("#areaId");
//地区选择
$areaId.lSelect();
	//上传产品图片
var $addProductImage = $("#addProductImage");
var productImgIdnex = ${order.orderAttachs?size};
var option1 = {
		dataType: "json",
    	uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	for(var i=0;i<data.length;i++){
	        	var file_info = data[i].file_info;
	        	[@compress single_line = true]
	    		var tHtml='<tr><td><div class="ul-box">
                        <div class="pic" style="display:inline-block"><img src="'+image+'" style="height:92px"><\/div>
                        <div style="display:inline-block">
                        <b title="'+file_info.name+'" style="width:188px;display: block;">'+abbreviate(file_info.name,26)+'</b>
                        <p>'+time+'</p>
                        </div>
                         <input type="hidden" name="orderAttachs['+attachIdnex+'].url" value="'+file_info.url+'">
			            <input type="hidden" name="orderAttachs['+attachIdnex+'].fileName" value="'+file_info.name+'">
                    <\/div></td><td><textarea  class="text" name="orderAttachs['+attachIdnex+'].memo"></textarea></td>
                    <td><a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a></td></tr>';
	    		[/@compress]                                 
	            $("#addProductImageTr").before(html);
	            productImgIdnex++;
	        }
        }
    }
    $addProductImage.file_upload(option1);
	
    var $delProductImage1 = $(".deleteAttachment");
	$delProductImage1.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	/**初始化订单的列表信息*/
	initGrid();
	
	//打开选择产品界面
	var $addProduct = $("#addProduct");
	var storeId = $(".storeId").val();
	$addProduct.click(function(){
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'${message("查询产品")}',
			url:'/product/product/selectProduct.jhtml?multi=2&isPart=0&storeId='+storeId,
			callback:function(rows){
				if(rows.length>0){
					var error = '';
					[#if useRepeatProduct2Order == 1][#else]
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".productId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					[/#if]
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						
						if(row.apply_price!=undefined && row.apply_price!=null && row.apply_price!=""){
								row.price=row.apply_price;
							}
							else if(row.member_price!=undefined && row.member_price!=null && row.member_price!=""){
								row.price=row.member_price;
							}
						
						$orderItemGrid.addRow(row,null,1);
					}
					countTotal();
					countVolume();
				}
			}
		});	
	})
	// 删除产品
	var $deleteProduct = $("a.deleteProduct");
	$deleteProduct.live("click", function() {
		var itemId = $(this).attr("itemId");
		var index = $(this).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$orderItemGrid.removeRow(index);
			var $child_trs = $("input.pId[parentId='"+itemId+"']").closest("tr");
			$child_trs.each(function(){
				$orderItemGrid.removeRow($(this).index());
			});
			countTotal();
			countVolume();
		})
	});
	
	$("form").bindAttribute({
    	isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	 });
	 
	 $("#openArea").bindQueryBtn({
		type:'area',
		title:'${message("查询地区")}',
		url:'/basic/area/select_area.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var $tr =$this.closest("tr");
				$(".areaId").val(rows[0].id);
				$(".areaName").val(rows[0].full_name);
				//$("input[name='address']").val('');
				$("input[name='zipCode']").val('');
			}
		}
	});	
	
	 //打开选择地址界面
    $("#addReceiveAddress").click(function(){
        var storeId = $(".storeId").val();
        if(storeId==""){
            $.message_alert('请选择客户');
        }else{
        		select_store_address(storeId);
        }
    });
    
    //更换地址
    function select_store_address(storeId){
     $("#addReceiveAddress").bindQueryBtn({
        	        type:'store',
        	        bindClick:false,
        	        title:'${message("更换地址")}',
        	        url:'/member/store/select_store_address.jhtml?storeId='+storeId,
        	        callback:function(rows){
        	            if(rows.length>0){
        	            	var row = rows[0];
        	                $("input[name='consignee']").attr("value",row.consignee);
        	                $("input[name='phone']").attr("value",row.mobile);
        	                $("input[name='address']").attr("value",row.address);
        	                $("input[name='zipCode']").attr("value",row.zip_code);
        	                $(".select_area").find(".fieldSet").empty();
        	                var areaId = row.area;
        	                if(areaId==null)areaId='';
        	                var areaName = row.area_full_name;
        	                if(areaName==null)areaName='';
        	                var treePath = row.tree_path;
        	                if(treePath==null)treePath='';
        	               // $(".select_area").find(".fieldSet").append('<input type="hidden" id="areaId" name="areaId"  value="'+areaId+'" treePath="'+treePath+'" />');
        	                //地区选择
        	               // $("#areaId").lSelect();
        	                $("input[name='areaId']").val(areaId);
        	                $("input[name='areaName']").val(areaName);
        	                
        	            }
        	        }   
        	    });
     }
	
	
})


/**初始化全链路的列表*/
function initFullGrid(e){
	var $li = $(e);
	if($li.hasClass("inited"))return false;
	var orderFullLink_items = ${orderFullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
	$li.addClass("inited");
}



/**初始化订单的列表信息*/
function initGrid(){
	var orderitem_items = ${orderItem_json};
	var orderStatus = '${order.orderStatus}';
	var appProductChangePrice = '${appProductChangePrice}';
	var stockInItemIndex = 0;
    	var cols = [				
		{ title:'${message("产品名称")}', name:'p_name' ,width:150 ,align:'left', renderer: function(val,item,rowIndex, obj){
			if(obj==undefined){
				var str = '';
				if(item.parent!=null){
					str = 'style="margin-left:25px;color: #666 !important;border-bottom: solid 1px #666 !important;"'
				}
				
				var html= '<input type="hidden" name="orderItems['+stockInItemIndex+'].id"  value="'+item.id+'" />'+
				'<input type="hidden" name="orderItems['+stockInItemIndex+'].product.id" class="productId productId_'+item.product+'" value="'+item.product+'" />'+
				'<input type="hidden" name="orderItems['+stockInItemIndex+'].name" value="'+item.name+'" />'+
			    '<a href="javascript:void(0);"  onclick="top.open_new_tab(this)" data-id="product_content_'+item.id+'" data-src="/product/product/content.jhtml?flag=1&id='+item.product+'" data-name="<marquee scrollamount=\'2\'>'+item.name+'</marquee>" reflush="true" class="red"'+str+'>'+item.name+'</a>';
				
				if(item.product==null){
					html = '<input type="hidden" name="orderItems['+stockInItemIndex+'].id"  value="'+item.id+'" />'+
					'<input type="text" name="orderItems['+stockInItemIndex+'].name" class="pName productId_'+item.product+' text" value="'+item.name+'" />';
				}
				return html;
			}else{
				var html= '<input type="hidden" name="orderItems['+stockInItemIndex+'].product.id" class="productId_'+item.id+'" value="'+item.id+'" />'+
						'<input type="hidden" name="orderItems['+stockInItemIndex+'].name" class="pName text" value="'+item.name+'" />'+
						'<a href="javascript:void(0);"  onclick="top.open_new_tab(this)" data-id="product_content_'+item.id+'" data-src="/product/product/content.jhtml?flag=1&id='+item.id+'" data-name="<marquee scrollamount=\'2\'>'+item.name+'</marquee>" reflush="true" class="red"'+str+'>'+item.name+'</a>';
				if(item.else==1){
					html = '<input type="text" name="orderItems['+stockInItemIndex+'].name" class="pName productId_'+item.product+' text" value="" />';
				}
				return html;
			}
		}},
		{ title:'${message("产品型号")}', name:'model' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var html;
			if(obj==undefined){
				if(item.product==null){
					html='<input type="text" name="orderItems['+stockInItemIndex+'].model" class="pModel text" value="'+val+'" />';
				}else{
					html=val+'<input type="hidden" name="orderItems['+stockInItemIndex+'].model" value="'+val+'" />';
				}
			}
			else{
				if(item.else==1){
					html='<input type="text" name="orderItems['+stockInItemIndex+'].model" class="pModel text" value="" />';
				}else{
					html='<input type="hidden" name="orderItems['+stockInItemIndex+'].model"  value="'+val+'" />'+val;
				}
			}
			return html;
		}},
		{ title:'${message("体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var html;
			var v = 0;
			if(obj==undefined){
				if(item.product==null){
					html=v;
				}else{
					if (val == '') {
						html=v;
					}else {
						html=val+'<input type="hidden" name="orderItems['+stockInItemIndex+'].volume" class="volume text" value="'+val+'" />';
					}
				}
			}
			else{
				if(item.else==1){
					html = v;
				}else{
					if (val == '') {
						html = v;
					}else {
						html='<input type="hidden" name="orderItems['+stockInItemIndex+'].volume" class="volume text"  value="'+val+'" />'+val;	
					}
				}
			}
			return html;
		}},
		{ title:'${message("12211")}', name:'vonder_code' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var html;
			if(obj==undefined){
				if(item.product==null){
					html='<input type="text" name="orderItems['+stockInItemIndex+'].vonderCode" class="pVonderCode productId_'+item.vonderCode+' text" value="'+val+'" />';
				}else{
					html=val+'<input type="hidden" name="orderItems['+stockInItemIndex+'].vonderCode" value="'+val+'" />';
				}
			}
			else{
				if(item.else==1){
					html='<input type="text" name="orderItems['+stockInItemIndex+'].vonderCode" class="pVonderCode productId_'+item.vonderCode+' text" value="" />';
				}else{
					html='<input type="hidden" name="orderItems['+stockInItemIndex+'].vonderCode"  value="'+val+'" />'+val;
				}
			}
			return html;
		}},
		{ title:'${message("政策类型")}', name:'apply_type' ,align:'center',renderer:function(val,item,rowIndex){
			return '<span class="policyType">'+val+'</span>';
		}},
		{ title:'${message("政策单号")}', name:'' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var html = '';
			if(item.policy_product==undefined){
				item.policy_product=0;
			}
			if(item.policy_sn==undefined){
				item.policy_sn='';
			}
			html = '<span class="search ">'+
				   	  '<input class="text linePolicyProductId policyProductId" type="hidden" name="orderItems['+stockInItemIndex+'].policyProduct.id" value="'+item.policy_product+'">'+
					  '<input class="text linePolicyProductSn policyProductSn" maxlength="200" type="text"  value="'+item.policy_sn+'" onkeyup="clearSelect(this)">'+
					  '<input type="button" class="iconSearch lineSelectPolicy" value="">'+
				   '</span>';
		   return html;
		}},
		//{ title:'${message("条形码编号")}', name:'bar_code' ,align:'center'},
		//{ title:'${message("发货仓库")}', name:'warehouse_name' ,align:'center'},
		[#--{ title:'${message("特价单号")}', name:'price_apply_sn' ,align:'center',renderer:function(val,item,rowIndex,obj){
			var price_apply_sn = '';
			if(obj==undefined){
				price_apply_sn = val;
			}else{
				price_apply_sn = item.apply_sn;
			}
			if(price_apply_sn!=''){
				return price_apply_sn;
			}else{
				return '-';				
			}
		}},--]
		{ title:'${message("产品价格")}', name:'price' ,align:'center', width:100,renderer:function(val,item,rowIndex,obj){
			if(appProductChangePrice=='1' && orderStatus=='unaudited' 
			&& (obj!=undefined || item.parent==null)){
				if((obj!=undefined && item.else==1)||(obj==undefined && item.product==null)){
					return '<div class="nums-input ov">'+
				            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
				            	'<input type="text"  class="t price pprice"  name="orderItems['+stockInItemIndex+'].price" value="'+val+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
				            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
				        	'</div>';
				}
				[#if !(readOnly??)]
				return '<div class="nums-input ov">'+
				            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
				            	'<input type="text"  class="t price pprice"  name="orderItems['+stockInItemIndex+'].price" value="'+val+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
				            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
				        	'</div>';
				//return '<input type="number" step="0.01" min=0 name="orderItems['+stockInItemIndex+'].price" class="price text" value="'+val+'"  oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)"/>';
				[#else]
					return '<input type="hidden" step="0.01" min=0 name="orderItems['+stockInItemIndex+'].price" class="price text pprice" value="'+val+'"  oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)"/><span class="red priceText">'+currency(val,true)+'</span>';
				[/#if]
			}else if(obj==undefined && item.parent!=null){
				if(item.product==null){
					return '<div class="nums-input ov">'+
				            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
				            	'<input type="text"  class="t price pprice"  name="orderItems['+stockInItemIndex+'].price" value="'+val+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
				            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
				        	'</div>';
				}
				return '<input type="hidden" name="orderItems['+stockInItemIndex+'].price" class="price" value="'+val+'" />-';
			}else{
				if(item.product==null){
					return '<div class="nums-input ov">'+
				            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
				            	'<input type="text"  class="t price pprice"  name="orderItems['+stockInItemIndex+'].price" value="'+val+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
				            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
				        	'</div>';
				}
				return '<input type="hidden" name="orderItems['+stockInItemIndex+'].price" class="price pprice" value="'+val+'" /><span class="red priceText">'+currency(val,true)+'</span>';
			}
		}},
		{ title:'${message("下单数")}', name:'quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
			var quantity = 1;
			if(obj==undefined){
				quantity = val;
			}
			if( (orderStatus=='unaudited' || orderStatus=='saved') && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				return '<div class="nums-input ov">'+
			            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
			            	'<input type="text"  class="t quantity" itemId="'+item.id+'" name="orderItems['+stockInItemIndex+'].quantity" value="'+quantity+'" org="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
			        	'</div>';
				//return '<input type="number" step="0.01" name="orderItems['+stockInItemIndex+'].quantity" value="'+quantity+'" org="'+quantity+'" class="text quantity" min="0" onchange="changPrice(this,'+item.id+',${order.store.id},'+item.price+')" '+str+' oninput="editQty (this,event)" onpropertychange="editQty(this,event)">';
			}else{
				var str = 'class="text quantity"';
				if(obj == undefined || item.parent!=null){
					str = 'class="text cq" parentId="'+item.parent+'"';
				}
				return '<input type="hidden" name="orderItems['+stockInItemIndex+'].quantity" value="'+quantity+'" org="'+quantity+'" '+str+' min="0">'
				+'<span class="qty-text">'+quantity+'</span>';
			}
			
		}},
		{ title:'金额', align:'center',name:'',renderer: function(val,item,rowIndex){
			return '<sapn class="text red trprice">'+currency(item.quantity*item.price,true)+'</span>'
		}},
		{ title:'${message("计划发货数")}', name:'ship_plan_quantity' ,align:'center'},
		{ title:'${message("实际发货数")}', name:'shipped_quantity' ,align:'center'},
		//{ title:'${message("退货数")}', name:'return_quantity' ,align:'center'},
		{ title:'${message("需求日期")}', name:'delivery_time' ,align:'center',renderer:function(val){
			var str1 = ''; 
			if(val!=''){
				str1 = val.substring(0,10);
			}
			return '<input type="text" class="text" name="orderItems['+stockInItemIndex+'].deliveryTime" value="'+str1+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})"/>'
		}},
		{ title:'${message("预计交货时间")}', name:'deliver_date' ,align:'center',renderer:function(val){
			var str = val;
			var str1 = str.substring(0,10)
			return '<input type="text" class="text" name="orderItems['+stockInItemIndex+'].deliverDate" value="'+str1+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})"/>'
		}},
		//{ title:'${message("选配内容")}', name:'parts_name' ,align:'center'},
		//{ title:'${message("特殊要求")}', name:'demand' ,align:'center'},
		{ title:'${message("买家备注")}', align:'center',name:'buyer_memo',renderer: function(val,item,rowIndex){
			[#if readOnly==1]
			return '<input type="text" class="text" name="orderItems['+stockInItemIndex+'].buyerMemo" value="'+val+'">';
			[#else]
			return '<input type="hidden" class="text" name="orderItems['+stockInItemIndex+'].buyerMemo" value="'+val+'">'+val;
			[/#if]
		}},
		{ title:'${message("卖家备注")}', align:'center',name:'seller_memo',renderer: function(val,item,rowIndex){
			[#if readOnly!=1]
			return '<input type="text" class="text" name="orderItems['+stockInItemIndex+'].sellerMemo" value="'+val+'">';
			[#else]
			return '<input type="hidden" name="orderItems['+stockInItemIndex+'].sellerMemo" value="'+val+'">'+val;
			[/#if]
		}},
		{ title:'${message("操作")}', align:'center', renderer:function(val,item,rowIndex,obj){
			stockInItemIndex++;
			if(obj != undefined){
				return '<a href="javascript:;" class="deleteProduct btn-delete">删除</a>';
			}else if((item.parent==null && (orderStatus=='saved' || orderStatus=='unaudited'))){
				return '<a href="javascript:;" class="deleteProduct btn-delete" itemId="'+item.id+'">删除</a>';
			}else{
				if(item.parent!=null){
					return '<input type="hidden" class="pId" parentId="'+item.parent+'">-';
				}else{
					return '-';
				}
				
			}
			
		}}
		
	];
	$orderItemGrid = $('#table-orderItem').mmGrid({
		height:'auto',
        cols: cols,
        items:orderitem_items,
        checkCol: false,
        autoLoad: true
    });
    
    var $addElseProduct = $("#addElseProduct");
    $addElseProduct.click(function(){
		var $storeId = $(".storeId").val();
		if($storeId==""){
			$.message_alert('${message("请选择客户")}');
		}else{
			var row=[{"else":"1"}];
			$orderItemGrid.addRow(row,null,1);
			countTotal();
			countVolume();
			
		}
	});
    
    
  //选择政策
	$(".lineSelectPolicy").live("click",function(){
		var $this = $(this);
		var $tr =$this.closest("tr");
		var productId=$tr.find(".productId").val();
		var storeId=$(".storeId").val();
		$this.bindQueryBtn({
			bindClick:false,
			type:'policyProduct',
			title:'${message("查询政策")}',
			url:'/finance/policy/select_policy_product.jhtml?storeId='+storeId+'&productId='+productId,
			callback:function(rows){
				if(rows.length>0){
					
					var $price_box = $tr.find(".price-box");
					var price;
					if($price_box.length==0 || $price_box.prop("checked")==false){
						$tr.find("input.price").val(rows[0].excute_price);
					}else{
						$tr.find("input.origMemberPrice ").val(rows[0].excute_price);
					}
					
					$tr.find(".price_span").html(currency(rows[0].excute_price,true));
					$tr.find(".price_span").attr("val",rows[0].excute_price);
					$tr.find(".priceText").html(currency(rows[0].excute_price,true));
					$tr.find(".priceText").attr("val",rows[0].excute_price);
					
					//设置数量
					$tr.find(".quantity").attr("minData",rows[0].min_quantity);
					$tr.find(".quantity").attr("maxData",rows[0].max_quantity);
					var quantity=$tr.find(".quantity").val();
					if(quantity<rows[0].min_quantity){
						$tr.find(".quantity").val(rows[0].min_quantity);
					}else if(quantity>rows[0].max_quantity){
						$tr.find(".quantity").val(rows[0].max_quantity);
					}
					
					$tr.find(".linePolicyProductId").val(rows[0].id);
					$tr.find(".linePolicyProductSn").val(rows[0].sn);
					var types={"0":"投款政策"," 1":"提货政策","2":"促销政策","3":"满赠政策", "4":"返利"};
					$tr.find(".policyType").html(types[rows[0].apply_type]);
					countTotal();
					
				}
			}
		});
	})
    
    var shipping_items = ${shipping_json};
    var shipping_stauts={'0':'${message("未审核")}','1':'${message("已审核")}','2':'${message("作废")}','3':'${message("部分发货")}','4':'${message("完全发货")}'};
    	var cols = [				
    	{ title:'${message("发货单号")}', name:'shipping_sn' ,width:120,align:'center',renderer:function(val,item,rowIndex){
			return '<a href="javascript:;" onClick="check_view(\'/b2b/shipping/view.jhtml?id='+item.shipping_id+'\')" class="red">'+val+'</a>';
		}},
    	{ title:'${message("运单号")}', name:'tracking_no' ,width:120,align:'center',renderer:function(val,item,rowIndex){
			return '<a href="javascript:;" onClick="check_view(\'/b2b/shipping/queryDelivery.jhtml?id='+item.shipping_id+'\')" class="red">'+val+'</a>';
		}},
    	{ title:'${message("发货状态")}', name:'status' ,align:'center', renderer: function(val,item,rowIndex){
			return shipping_stauts[val];
		}},
		{ title:'${message("发货仓库")}', name:'warehouse_name' ,align:'center'},
		{ title:'${message("产品名称")}', name:'name' ,align:'center'},
		{ title:'${message("产品型号")}', name:'model' ,align:'center'},
		{ title:'${message("12211")}', name:'vonder_code' ,align:'center'},
		{ title:'${message("计划发货数量")}', name:'quantity' ,align:'center'},
		{ title:'${message("实际发货数量")}', name:'shipped_quantity' ,align:'center'},
		{ title:'${message("已签收数量")}', name:'receipt_quantity' ,align:'center'},
		{ title:'${message("体积")}', name:'volume' ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center'}
	];
	$('#table-shipping').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:shipping_items,
        checkCol: false,
        autoLoad: true
    });
    
    [#--
    /**特价单*/
    var priceApply_items = ${priceApply_json};
      var priceApply_statuss = {'0':'${message("已保存")}','1':'${message("已提交")}','2':'${message("已审核")}','3':'${message("已驳回")}','4':'${message("已关闭")}'};
      
    	var cols = [				
    	{ title:'${message("特价单单号")}', name:'sn' ,width:120,align:'center',renderer:function(val,item,rowIndex){
    		return '<a href="javascript:void(0);" onClick="create_iframe(\'/b2b/priceApply/view.jhtml?id='+item.id+'\')" class="red" style="border-bottom: solid 1px #f60;">'+item.sn+'</a>';
    	}},
		{ title:'${message("申请人")}', name:'sm_username' ,align:'center'},
		{ title:'${message("备注")}', name:'memo' ,align:'center'},
		{ title:'${message("特价单状态")}', name:'status' ,align:'center',renderer:function(val,item,rowIndex){
			return priceApply_statuss[val];
		}},
		{ title:'${message("创建时间")}', name:'create_date' ,align:'center'},
		{ title:'${message("审核时间")}', name:'check_date' ,align:'center'}
	];
	$('#table-priceApply').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:priceApply_items,
        checkCol: false,
        autoLoad: true
    });
    --]
    
    /**初始化订单附件*/
    var orderAttach_items = ${orderAttach_json};
    var orderAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
			if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name , item.name, item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['orderAttachs['+orderAttachIndex+'].id']=item.id;
				hideValues['orderAttachs['+orderAttachIndex+'].url']=url;
				hideValues['orderAttachs['+orderAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'orderAttachs['+orderAttachIndex+'].name',
					hideValues: hideValues
				});
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['orderAttachs['+orderAttachIndex+'].url']=url;
				hideValues['orderAttachs['+orderAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'orderAttachs['+orderAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><input type="text" class="text file_memo" name="orderAttachs['+orderAttachIndex+'].memo" value="'+val+'" style="width:50%"></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			orderAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderAttach_items,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row=data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
    /**初始化全链路*/
    initFullGrid();
    
    [#if order.orderStatus!='unaudited' && order.orderStatus!='saved']
		$(".text").prop("disabled",true);
		$("select").prop("disabled",true);
	[/#if]
}
// 审核
function check(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		var url="/b2b/order/check.jhtml";
		var data = $form.serialize();
		ajaxSubmit($this,{
			 url: url,
			 method: "post",
			 data: data,
			 isConfirm:true,
			 confirmText:'您确定要审核吗？',
			 callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			 }
		})
	}
}

/**确认订单*/
function confirm(e){
	ajaxSubmit(e,{
		method:'post',
		url:'/b2b/order/confirm.jhtml?ids=${order.id}',
		isConfirm:true,
		confirmText:'您确定要确认该订单吗？',
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	})
}

//取消订单
function cancel(e){
	ajaxSubmit(e,{
		method:'post',
		url:'/b2b/order/cancel.jhtml?ids=${order.id}',
		isConfirm:true,
		confirmText:'您确定要取消该订单吗？',
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	})
}
function changPrice(e,pid,sid,price){
	var $thisPrice=$(e).closest("tr").find("input.price");
	var $quantity=$(e).closest("tr").find("input.quantity");
	var $thisPricetext=$(e).closest("tr").find("span.priceText");
	var str='storeId='+sid+'&productId='+pid+'&quantity='+e.value+'&price='+price; 
	ajaxSubmit(e,{
					method:'post',
					url:'/member/store/get_other_price.jhtml',
					data:str,
					callback:function(message) {
						if(message.objx.price!=null){
							$thisPrice.val(message.objx.price);
							$thisPricetext.html(currency(message.objx.price,true));
							countTotal();
						}
					}
				})
	
}


function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		$.message_confirm("您确定要审批流程吗？",function(){
			var url="/b2b/order/check_wf.jhtml";
			var data = $form.serialize();
			
			ajaxSubmit(e,{
				 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=47',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 	var str = '';
				 	for(var i=0;i<rows.length;i++){
				 		var row = rows[i];
				 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
				 	}
				 	var content = '<table class="input input-edit" style="width:100%">'
							+'<tbody><tr><th>流程模版</th>'
							+'<td>'
								+'<select class="text" id="objConfId">'
									+str
								+'</select>'
							+'</td>'
						+'</tr></tbody></table>';
					var $dialog_check = $.dialog({
						title:"${message("正式订单审核")}",
						height:'135',
						content: content,
						onOk:function(){
							var objConfId = $("#objConfId").val();
							if(objConfId=='' || objConfId == null){
								$.message_alert("请选择流程模版");
								return false;
							}
							data = data+'&objConfId='+objConfId;
							
							ajaxSubmit($this,{
								 url: url,
								 method: "post",
								 data: data,
								 callback:function(resultMsg){
									$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
										reflush_wf();
									});
								 }
							})
							
						}
					});
				 }
			})
		});
	}
}

function save(e, flag){
	var $input = $("input.pprice");
	var str;
	var count = 0;
	$input.each(function(){
		var p = $(this).val();
		if(p == '0'){
			count++;
			
		}
	})
	if(count == 0){
			str = '您确定要保存吗？'
		}else{
			str = '您当前的订单包含价格为&nbsp;&nbsp;<b class="red">0</b>&nbsp;&nbsp;的产品，您确定要继续保存吗？'
		}
		var url = '';
		if (flag == 0) {
			url = '/b2b/order/update.jhtml?stat=0';
		}
		else {
			url = '/b2b/order/update.jhtml?stat=1';
		}
		if($("#inputForm").valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
			 	$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
				})
			 }
			})
		}
}
function check_view(url){
	var w = $(window).width();
	var h = $(window).height();
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
		title:'${message("查看物流信息")}',
		width:w,
		height:h,
		content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
		ok: null,
		cancel: null,
		onOk: function() {
		
		}
	});
	$dialog.find(".dialogContent").css("height",h+"px");
	$dialog.css("top",0+"px").css("max-height",h+"px");
	

}

function reject(e){
	var str='<div>您确定要&nbsp;&nbsp;<b>驳回</b>&nbsp;&nbsp;当前订单吗？<br/>确定:订单将变回未审核状态，所扣减的客户余额将退还。<br/>取消:不做任何处理。</div>';
ajaxSubmit(e,{
			url: '/b2b/order/rejectOrder.jhtml?orderId='+${order.id},
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
			 	$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
				})
			 }
			})
}
function check_pdf(e,id){
	ajaxSubmit(e,{
		url:"/b2b/order/checkPdf.jhtml",
		method:"post",
		data:{id:id},
		async: false,
		callback:function(resultMsg){
			window.open(resultMsg.content);
		}
	});
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看订单")}
	</div>
	<form id="inputForm" action="/b2b/order/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="orderId" value="${order.id}" />
		<input type="hidden" name="id" value="${order.id}" />
		<input type="hidden" name="isCheckWf" value="${isCheckWf}" />
		<input type="hidden" id="outTradeNo" value="${order.store.outTradeNo}" />
		<div class="tabContent">
		<div class="title-style mt0">
			${message("基本信息")}
		</div>
		<table class="input input-edit">
			<tr>
				<th>
					${message("订单编号")}:
				</th>
				<td>
					${order.sn}
				</td>
				<th>
					${message("来源单号")}:
				</th>
				<td>
					${order.outTradeNo}
				</td>
				<th>
					${message("创建日期")}:
				</th>
				<td>
					<div>${(order.createDate?string("yyyy-MM-dd HH:mm:ss"))!}</div>
				</td>
				<th>
					${message("下单时间")}:
				</th>
				<td>
					<div>${(order.orderDate?string("yyyy-MM-dd HH:mm:ss"))!}</div>
				</td>
			</tr>
			<tr>
				<th>
					${message("订单状态")}:
				</th>
				<td>
					[#if order.orderStatus == "unconfirmed"]${message("未确认")}
					[#elseif order.orderStatus == "confirmed"]${message("已确认")}
					[#elseif order.orderStatus == "completed"]${message("已完成")}
					[#elseif order.orderStatus == "cancelled"]${message("已取消")}
					[#elseif order.orderStatus == "deleted"]${message("已删除")}
					[#elseif order.orderStatus == "unaudited"]${message("已下达")}
					[#elseif order.orderStatus == "audited"]${message("已审核")}
					[#elseif order.orderStatus == "accepted"]${message("已接受")}
					[#elseif order.orderStatus == "saved"]${message("已保存")}
					[/#if]
				</td>
				<th>
					${message("支付状态")}:
				</th>
				<td>
					[#if order.paymentStatus=="unpaid"]${message("未支付")}
					[#elseif order.paymentStatus=="partialPayment"]${message("部分支付")}
					[#elseif order.paymentStatus=="paid"]${message("已支付")}
					[/#if]
				</td>
				<th>
				    ${message("配送状态")}:
				</th>
				<td>
					[#if order.shippingStatus == "unshipped"]${message("未发货")}
					[#elseif order.shippingStatus == "partialShipment"]${message("部分发货")}
					[#elseif order.shippingStatus == "shipped"]${message("已发货")}
					[#elseif order.shippingStatus == "confirmReceipt"]${message("确认收货")}
					[/#if]
				</td>
				<th>
					${message("配送方式")}:
				</th>
				<td>
					[#if order.orderStatus == "unaudited" || order.orderStatus == "saved"]
					<select class="text" name="shippingMethodId">
						[#list shippingMethods as sm]
						<option value="${sm.id}" [#if order.shippingMethodName == sm.name ] selected="selected"[/#if]>${sm.name}</option>
						[/#list]
					</select>
					[#elseif order.orderStatus != "unaudited"]
						${order.shippingMethodName}
					[/#if]
				</td>
			</tr>
			<tr>
				<th>
					${message("付款方式")}:
				</th>
				<td>
					${order.paymentMethodName}
				</td>
				<!--<th>${message("物流快递")}:</th>
				<td>${order.delivery.name}</td>-->
				<th>
					${message("运费")}:
				</th>
				<td>
					<span class="red">${currency(order.freight, true)}</span>
				</td>
				<th>${message("税率%")}:</th>
            	<td>
            		<input type="text" name="taxRate" class="text" value="16" />
            	</td>
				<th >${message("订单类型")}:</th>
				<td>
					<select class="text" name="orderCategory">
						<option value="0" [#if order.orderCategory==0] selected[/#if]>${message("常规订单")}</option>
						<option value="1" [#if order.orderCategory==1] selected[/#if]>${message("特价订单")}</option>
						<option value="2" [#if order.orderCategory==2] selected[/#if]>${message("合同订单")}</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>${message("机构")}:</th>
				<td>${order.saleOrg.name}</td>
				<th>${message("客户")}:</th>
				<td>
					${order.store.name}
					<input type="hidden" class="storeId" value="${order.store.id}">
				</td>
				<th>${message("制单人")}:</th>
				<td>${order.storeMember.name}</td>
				[#--<th>
					${message("运费承担类型")}:
				    </th>
				    <td>
					<select id="freightChargeTypeId" name="freightChargeTypeId" class="text">
								<option value="">${message("请选择")}</option>
								[#list freightChargeTypes as freightChargeType]
								<option value="${freightChargeType.id}"[#if order.freightChargeType.id==freightChargeType.id] selected[/#if]>${freightChargeType.value}</option>
								[/#list]
							</select>
				    </td>--]
				    <th>${message("审核人")}:</th>
				<td>${order.checkStoreMember.name}</td>
			</tr>
			<tr>
            	<th>${message("业务员")}:</th>
            	<td>${order.salesman.name}</td>
				<th class="noBg"></th><td></td>
				<th class="noBg"></th><td></td>
				<th class="noBg"></th><td></td>
            </tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
					<textarea class="text" name="memo" value="${order.memo}">${order.memo}</textarea>
				</td>
			</tr>
			<tr >
				[#--<th >${message("客户经理")}:</th>
		     <td>
		     [#if order.orderStatus == "unaudited" && readOnly!=1 && isCheckWf && order.wfId==null]
							<select class="text" id="manager" name="manager">
							[#list storeManagerList as storeManager]
							<option value="${storeManager.manager}" [#if order.manager == storeManager.manager]selected="selected" [/#if]>
							<span class="text manager">${storeManager.manager}</span>
							</option>
							[/#list]
						</select>
						[#else]
							${order.manager}
					[/#if] 
			
						
				</td>--]
				<th>${message("审核时间")}:</th>
				<td class="noBg">[#if order.checkDate??]${order.checkDate?string("yyyy-MM-dd HH:mm:ss")}[/#if]</td>
				[#--<th>${message("总体积")}:</th>
				<td><span name="volumeall">${order.volume}</span>
				  <input type="hidden"  name="volume" value="${order.volume}"/>
				</td>--]
				 <th class="noBg"></th>
					<td class="noBg"></td>
				<th class="noBg"></th>
					<td class="noBg"></td>
				<th class="noBg"></th>
					<td></td>
			</tr>
		</table>
		<div class="title-style">${message("客户可发货余额")}
		</div>
		<table class="input input-edit">
            <tr>
            	<th>
					${message("订单金额")}:
				</th>
				<td>
					<span class="red" id="amount">${currency(order.amount, true)}</span>
				</td>
            	<th>${message("本次使用政策")}:</th>
            	<td>
            		<div class="nums-input ov">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="countTotal();">
						<input type="text"  class="t usePolicyPrice" minData="0" maxData="${order.amount}" name="usePolicyPrice" value="${order.usePolicyPrice}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="countTotal();">
					</div>	
<!--             		<input type="text" name="taxRate" class="text" value="0" /> -->
            	</td>
            	<th>${message("本单折扣")}:</th>
            	<td>
            		<span class="red" >￥0.00</span>
<!--             		<input type="text" name="taxRate" class="text" value="0" /> -->
            	</td>
            	<th>${message("折后订单金额")}:</th>
            	<td><span class="red" id="afterTotal">[#if order.usePolicyPrice??]${currency(order.amount-order.usePolicyPrice, true)}[#else]${currency(order.amount, true)}[/#if]</span></td>
            </tr>
			<tr>
            	<th>${message("政策总额")}:</th>
            	<td>
            		<span class="red" >￥0.00</span>
<!--             		<input type="text" name="taxRate" class="text" value="0" /> -->
            	</td>
            	<th>
					${message("已付金额")}:
				</th>
				<td>
				    [#if order.orderStatus == "unaudited" || order.orderStatus == "completed" || order.orderStatus == "audited"]
					<span class="red">${currency(order.amount, true)}</span>
					[#else]
					<span class="red">${currency(0.00, true)}</span> 
					[/#if]
				</td>
            	<th class="noBg"></th><td></td>
				<th class="noBg"></th><td></td>
            </tr>
		</table>
		<div class="title-style">${message("收货信息")}
			<div class="btns">
				[#if order.orderStatus=='saved' || order.orderStatus=='unaudited']
					<a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
				[/#if]
			</div>
		</div>
		<table class="input input-edit">
		<tr class="border-L2">
			<th>
				${message("收货人")}:
			</th>
			<td>
				<input type="text" class="text" name="consignee" value="${order.consignee}" btn-fun="clear">
			</td>
			<th>
				${message("收货人电话")}:
			</th>
			<td>
				<input type="text" class="text" name="phone" value="${order.phone}" btn-fun="clear">
			</td>
			<th>
				${message("收货地区邮编")}:
			</th>
			<td>
				<input type="text" class="text" name="zipCode" value="${order.zipCode}" btn-fun="clear">
			</td>
			<th class="noBg"></th><td class="noBg"></td>
		</tr>
		<tr class="border-L2">
			<th>
				${message("收货地区")}:
			</th>
			<td>
				<span class="search" style="position:relative">
		    <input type="hidden" name="areaId" class="text areaId" value="${(order.area.id)!}" btn-fun="clear"/>
		    <input type="text" name="areaName" class="text areaName"  value="${(order.area.fullName)!}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
		    <input type="button" class="iconSearch" value="" id="openArea">
		 </span>
			</td>
			<th>
				${message("收货地址")}:
			</th>
			<td colspan="3">
				<input type="text" class="text" name="address" value="${order.address}" btn-fun="clear">
			</td>
			<th class="noBg"></th><td class="noBg"></td>
		</tr>
		</table>
		[#--xk begin--]
		<div class="title-style">
			${message("订单明细")}
			<div class="btns">
				[#if order.orderStatus=='saved' || order.orderStatus=='unaudited']
					<a href="javascript:;" id="addProduct" class="button">${message("选择产品")}</a>
					[#if undefinedProduct2Order==1 && false]
						<a href="javascript:;" id="addElseProduct" class="button">${message("增加其他产品")}</a>
					[/#if]
				[/#if]
			</div>
		</div>
		<table id="table-orderItem"></table>
		
		<table id="table-payment"></table>
		<div class="title-style">
			${message("发货信息")}
		</div>
		<table id="table-shipping"></table>
		<div class="title-style">
			${message("附件信息")}
			<div class="btns">
				<a href="javascript:;" id="addAttach" class="button">添加附件</a>
			</div>
		</div>
		<table id="table-attach"></table>
		
		<div class="title-style">
			${message("全链路信息")}
		</div>
		<table id="table-full"></table>
	</div>
		
		<div class="fixed-top">
			[#if isEdit!=0]
				[#if readOnly == 1]
					<a href="/b2b/order/add/${code}.jhtml" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
				[/#if]
				[#if order.orderStatus == "unaudited" && (!isCheckWf || order.wfId==null)]
					[#--<a href="javascript:void(0);" onclick="confirm(this)" class="button" id="cancelButton">${message("确认订单")}</a>--]
					<a href="javascript:void(0);" onclick="cancel(this)" class="button cancleButton" id="cancelButton">${message("取消订单")}</a>
				[/#if]
				[#if order.orderStatus == "unaudited" && readOnly!=1]
					[#if isCheckWf]
						[#if order.wfId==null]
							<input type="button" class="button sureButton" value="${message("12501")}" onclick="check_wf(this)" />
						[/#if]
					[#else]
						<a id="shengheButton" class="iconButton" onclick="check(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
					[/#if]
				[/#if] 
				[#if !isCheckWf || order.wfId==null]
					[#if order.orderStatus == "unaudited" && readOnly!=1]
						<a href="javascript:void(0);" class="button sureButton" onclick="save(this, 1)">${message("保存并下达")}</a>
					[#elseif order.orderStatus == "saved"]
						[#if saveOrder2Unaudited == 0]
				        <a href="javascript:void(0);" class="button sureButton" onclick="save(this, 0)">${message("保存")}</a>
				        [/#if]
						<a href="javascript:void(0);" class="button sureButton" onclick="save(this, 1)">${message("保存并下达")}</a>
					[/#if]
				[/#if]
				[#if order.orderStatus == "audited" && isReject && readOnly!=1 && order.companyInfoId!=19]
				<a href="javascript:void(0);" onclick="reject(this)" class="button cancleButton" >${message("订单驳回")}</a>
				[/#if]
				[#if order.orderStatus == "audited" && readOnly!=1 && order.companyInfoId!=18 && isReject]
				[#--<a href="javascript:void(0);" onclick="top.open_new_tab(this)"  data-id="order_change" data-src="http://mk.etwowin.com.cn/b2b/order_change/list_tb.jhtml?id=${order.id}" data-name="订单变更" class="iconButton">订单变更</a>--]
				[/#if]
			[/#if]
			[#if order.orderStatus == "audited"]<input type="button" class="button pdfButton" value="${message("查看PDF")}" onclick="check_pdf(this,'${order.id}')" />[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>