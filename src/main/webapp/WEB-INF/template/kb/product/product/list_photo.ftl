<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.fly.min.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
<script type="text/javascript">
var defaultImage = '${companyInfo.logo}';

function showPage(resultMsg){
	var pageNumber=1;
	var totaNum=0;
	var pageSize=200;
	if(resultMsg!=undefined){
		var content_data = $.parseJSON(resultMsg.content);
		totaNum = content_data.total;
		pageSize = content_data.pageSize;
		pageNumber = content_data.pageNumber;
	}
	
	
	var page = parseInt(totaNum/pageSize);
	if(totaNum%pageSize!=0){
		page = page+1;
	}
	if(page==0){
		page = 1;
	}
	$("#s-page").remove();
	var $str = $('<p id="s-page" style="float:left;line-height:38px;font-size:14px;margin-left:5px">当前第<span style="color:blue">'+pageNumber+'</span>页，共<span style="color:blue">'+page+'</span>页，总数<span style="color:blue">'+totaNum+'</span></p>');
	$str.insertAfter(".pageList .next");

}
function initTable(num){
	if(num==0){
		var cols = [
			{ title:'', name:'sn',  align:'center', renderer: function(val,row,rowIndex){
				return '';
			}},
		];
	    
	    $mmGrid = $('#table-m1').mmGrid({
			cols: cols,
// 			fullWidthRows:true,
			checkCol:false,
			hideHead:false,
			useCountText:false,
			useLoading:false,
			autoLoad: true,
			nowrap: false,
	        url: '/product/product/list_photo_data.jhtml',
	        renderer:function(rows){
	        	var html = '<ul>';
	        	for(var i=0;i<rows.length;i++){
	        		var row = rows[i];
	        		var isPartStr = '';
	        		var addCartBtnStr = '';
					if(row.is_parts){
						isPartStr = '<div class="tag">可定制</div>';
					}else{
						addCartBtnStr = '<a href="javascript:void(0)" class="btn-cart addcar" onclick="addCart('+row.id+')"></a>';
					}
					var image_str = row.image;
					if(image_str==undefined || image_str.length==0){
						image_str = defaultImage;
					}
					
					var vonder_code = row.vonder_code;
					if(vonder_code==undefined || vonder_code.length==0 ){
						vonder_code = '';
					}
					var model = '';
					if(row.model!=null){
						model = '【'+row.model+'】';
					}
					var del_str = '';
					var price = row.price;
					if(row.apply_price!=null){
						price = row.apply_price;
						del_str = '<del>￥'+row.price+'</del>';
					}else if(row.member_price!=null){
						price = row.member_price;
						del_str = '<del>￥'+row.price+'</del>';
					} 
					if(price==undefined){
						price = 0;
					}
					
					var title = model+row.name+'&#10;'+vonder_code;
					
					[@compress single_line = true]
						html+='<li title="'+title+'">
					      	'+isPartStr+'
					      	<div class="pic">
						      	<a href="javascript:void(0)" onclick="top.open_new_tab(this)" data-id="product_content_'+row.id+'" data-src="/product/product/content/${code}.jhtml?flag=1&id='+row.id+'" data-name="<marquee scrollamount=\'2\'>'+row.name+'</marquee>" reflush="false" data-title="'+row.name+'" title="'+row.name+'">
						      		<img src="'+image_str+'">
						      	</a>
					      	</div>
					          <div class="name"><a href="javascript:void(0)" onclick="top.open_new_tab(this)" data-id="product_content_'+row.id+'" data-src="/product/product/content/${code}.jhtml?flag=1&id='+row.id+'" data-name="<marquee scrollamount=\'2\'>'+row.name+'</marquee>" data-title="'+row.name+'" title="'+row.name+'" reflush="false">'+model+row.full_name+'</a></div>
					          <p class="no">'+vonder_code+'</p>
					          <div class="price">
					          	￥<b>'+currency(price,false)+'</b>
					          </div>
					          <div class="fr" style="margin-top:7px;margin-right:5px;">点击量:'+row.hits+'</div>
					          '+addCartBtnStr+'
					      	</li>';
					[/@compress]
	        	}
	    		html+="</ul>";
				return html;
	        },
	        callback:function(data,resultMsg){
	        	$(".mmGrid").css("margin-left","40px").css("margin-right","40px");
	        	var $headWrapper = $(".mmg-headWrapper");
		     	var $bodyWrapper = $(".mmg-bodyWrapper");
	        	var w = $bodyWrapper.width();
// 	        	$bodyWrapper.find("ul").width($(".mmGrid").width());
	        	$("#table-m1").css("width","100%");
	           	$bodyWrapper.css({"height":"100%","overflow-x": "hidden"});
	           	$headWrapper.hide();
				$(".currentPageRecords").text(data['content'].length);
				showPage(resultMsg);
	        },
		    params:function(){
	        	return $("#listForm").serializeObject();
	        },
		    plugins : [
	            $('#paginator').mmPaginator({limit: 60, limitList: [60]})
	        ]
	    });
	}else{
    	 var cols = [
    	 	{ title:'', name:'id' ,width:30,lockWidth:true, align:'center',hidden:true, renderer: function(val,item,rowIndex){
					if(!item.is_parts){
						var image_str = item.image;
						if(image_str==undefined || image_str.length==0){
							image_str = defaultImage;
						}
						return '<img src="'+image_str+'" style="display:none;"><a href="javascript:void(0)" class="btn-cart addcar" onclick="addCart('+val+')"></a>';
					}
			}},
			{ title:'${message("产品名称")}', name:'name' ,width:200, align:'center',renderer:function(val,item,rowIndex){
				return '<a href="javascript:void(0);" onclick="top.open_new_tab(this)" data-id="product_content_'+item.id+'" data-src="/product/product/content/${code}.jhtml?flag=1&id='+item.id+'" data-name="'+item.name+'" reflush="true" class="red">'+val+'</a>';
			}},
			{ title:'${message("产品型号")}', name:'model' ,width:80, align:'center'},
// 			{ title:'${message("12211")}', name:'vonder_code' ,width:100, align:'center' },
			{ title:'${message("单位")}', name:'unit' ,width:80, align:'center'},
			{ title:'${message("产品分类")}', name:'product_category_name' ,width:80, align:'center' },
// 			{ title:'${message("税控编号")}', name:'fiscal_coding' ,width:80, align:'center' },
			{ title:'${message("产品价格")}', name:'price' ,width:80, align:'center', renderer: function(val){
				return currency(val,true);	
			}},
// 			{ title:'${message("原价")}', name:'origprice' ,width:80, align:'center', renderer: function(val){
// 				return currency(val,true);	
// 			}},
			{ title:'${message("重量")}', name:'weight' ,width:80, align:'center', renderer: function(val){
			return currency(val);	
		}},
		{ title:'${message("体积")}', name:'volume' ,width:80, align:'center', renderer: function(val){
			return currency(val);	
		}},
// 		{ title:'${message("创建日期")}', name:'create_date' ,width:120, align:'center'}
		];
	    $mmGrid = $('#table-m1').mmGrid({
			cols: cols,
			checkCol:false,
			useCountText:false,
			fullWidthRows:true,
			height:auto,
			autoLoad: true,
	        url: '/product/product/list_photo_data.jhtml',
	        callback:function(data,resultMsg){
				$(".currentPageRecords").text(data['content'].length);
				showPage(resultMsg);
	        },
		    params:function(){
	        	return $("#listForm").serializeObject();
	        },
		    plugins : [
	            $('#paginator').mmPaginator({limit: 60, limitList: [60]})
	        ]
	    });
	}
}

$().ready(function() {

	var $searchBtn = $("#searchBtn");
	var $listForm = $("#listForm");
	
	var $dls = $(".ctl-filter dl");
	if($dls.length==1){
		var $ctl_filter = $(".ctl-filter");
		var $responsive = $(".table-responsive");
		var h = $ctl_filter.outerHeight(true);
		$(".ctl-filter").hide();
		//$responsive.height($responsive.height()+h);
	}
		
    [#if flag!=1]
		initTable(0);
    [#else]
    	initTable(1);
    [/#if]
    
		
	var countshow = $("dl.js-count");
	//var ph = window.innerHeight - $(".bar").height() - $(".ctl-filter").height()-74;
    //$(".pro-c-list").attr("style","height:"+ph+"px");
    
    //var h = $(window).height()-$(".bar").height()-$(".ctl-filter").height()-10;
	//$(".table-responsive").attr("style","height:"+h+"px");
    
	var offset = $("#fly-img-div").offset();
	$(".addcar").live("click",function(event){
		var addcar = $(this);
		var img = addcar.parent().find('img').attr('src');
		var flyer = $('<img class="u-flyer" src="'+img+'">');
		flyer.fly({
			start: {
				left: event.pageX,
				top: event.pageY
			},
			end: {
				left: offset.left+10,
				top: offset.top+10,
				width: 0,
				height: 0
			},
			onEnd: function(){
				//$("#msg").show().animate({width: '250px'}, 200).fadeOut(1000);
				//addcar.css("cursor","default").unbind('click');
				addcar.css("display","none");
				this.destory();
			}
		});
	});
	$(".js-cancle").click(function(){
		$(this).parent().parent().hide();	
	})
	
	var $selectStore = $("#selectStore");
	$selectStore.bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'${base}/member/store/select_store.jhtml?isMember=1&type=distributor',
		callback: function(rows) {
			if(rows.length>0){
				var row = rows[0];
				$("#storeId").val(row.id);
				$("#storeName").val(row.name);
				$("#showStoreName").html(row.name);
				$("#listForm").submit();
				//$mmGrid.load({pageNumber:1});
			}
		}
	});
	$("#searchButton").click(function(){
		
		var keyWords=$("#searchKeyword").val();
		var storeId = $("#storeId").val();
		var storeName = $("storeName").val();
		var productCategoryId = $("#productCategoryId").val();
		window.location.href='/product/product/list_photo/${code}.jhtml?flag=0&index=1&keyWords='+keyWords+'&storeId='+storeId+'&storeName='+storeName+'&productCategoryId='+productCategoryId;
	});
	
	
	getCartItemTotal();
	
	
});

function getCartItemTotal(){
	$.ajax({
		url: "/product/product/getItemTotal.jhtml",
		type: "POST",
		dataType: "json",
		success: function(total) {
			$("#cartItemTotal").html(total);
		}
	});
}

function ctlMore(e){

	var h_spec = 0;
	var h0 = $(".ctl-filter").height();

	if($(e).hasClass("on")){
		$(e).removeClass("on").find(".ctl-txt").html("更多");
		$(e).parent().parent().find("dd").removeAttr("style")
	}else{
		$(e).addClass("on").find(".ctl-txt").html("隐藏");
		$(e).parent().parent().find("dd").attr("style","height:auto;");
	}
	var ph = $(".ctl-filter").height();
	h_spec = h0-ph;
	changeGridHeight(h_spec);
}
function ctlCheck(e){
	var h_spec = 0;
	var h0 = $(".ctl-filter").height();
	if($(e).hasClass("on")){
		$(e).removeClass("on").find(".ctl-txt").html("多选");
		$(e).parent().find(".ctl-ok").hide();
		$(e).parent().find(".ctl-more").show();
		$(e).parent().parent().find("dd").removeAttr("style")
		$(e).parent().parent().find("input[type='checkbox']").hide();
		
	}else{
		$(e).addClass("on").find(".ctl-txt").html("取消");
		$(e).parent().find(".ctl-ok").show();
		$(e).parent().find(".ctl-more").hide();	
		$(e).parent().parent().find("dd").attr("style","height:auto;");
		$(e).parent().parent().find("input[type='checkbox']").show();
		$(e).parent().find(".ctl-more").removeClass("on").find(".ctl-txt").html("更多");
	}
	var ph = $(".ctl-filter").height();
	h_spec = h0-ph;
	changeGridHeight(h_spec);
}

function showProDetail(e, id){
  $("#mainiframe").attr("src", "/product/product/content/${code}.jhtml?id="+id);
	$(".pup-proDetail").show();
}

//添加购物车
function addCart(id){
	var storeId = $("#storeId").val();
	$.ajax({
			url: "/b2b/cart/add.jhtml",
			type: "POST",
			data: {productId: id,quantity:1,storeId:storeId},
			dataType: "json",
			success: function(message) {
					 getCartItemTotal();
					 if(message.type != 'success'){
						 $.message_timer(message.type,message.content,3000,function(){
							}) 
					 }
				 
				
			}
		});
}

//更新分类名称
  var $parent_category = $("#categoryName");
  var categoryName = $("#categoryName").val();
  if(categoryName!=""){
  	$parent_category.text(categoryName);
  }else{
  	$parent_category.text('无');
  }

function showCtlmore(e){
	var h_spec = 0;
	var h0 = $(".ctl-filter").height();;
	if($(e).hasClass("on")){
  		$(".ctl-filter-dl dl").eq(2).nextAll().hide();
		$(e).removeClass("on").find(".ctl-txt").html("更多");
		$("input[name='showItem']").val(false);
	}else{
		$(".ctl-filter-dl dl").eq(2).nextAll().show();
		$(e).addClass("on").find(".ctl-txt").html("隐藏");
		$("input[name='showItem']").val(true);
	}
	var countshow = $("dl.js-count");
	var ph = $(".ctl-filter").height();
	h_spec = h0-ph;
	changeGridHeight(h_spec);
}

function changeGridHeight(h_spec){
	var $bodyWrapper = $(".mmg-bodyWrapper");
	var $responsive =  $(".table-responsive");
	var $mmGrid = $(".mmGrid");
	
// 	$bodyWrapper.height($bodyWrapper.height()+h_spec);
	//$responsive.height($responsive.height()+h_spec);
	$responsive.css("height","auto");
	$mmGrid.css("height","auto");
}

function subForm(e){
	$("input[name='singleSlect']").val("");
	$mmGrid.load({pageNumber:1});
}

function oneParamSub(e){
	var $input = $(e);
	var $dd = $input.closest("dd");
	var $divs = $dd.children("div");
	var $inputValueIds = $dd.find("input");
	var $checkeds;
	if(false && $input.is(":hidden")){
		$divs.removeClass("singleSelect");
		$input.closest("div").addClass("singleSelect");
	    $inputValueIds.each(function() {
	       $(this).prop("checked",false);
	    });
	    $input.prop("checked",true);
	    $("input[name='singleSlect']").val("");
	    $mmGrid.load({pageNumber:1});
	    $checkeds = $dd.find("input.search_strs:checked");
	}else{
		var $div = $input.closest("div").toggleClass("singleSelect");
		$checkeds = $dd.find("input.search_strs:checked");
		if($checkeds.length==0){
			$divs.eq(0).addClass("singleSelect");
			$divs.eq(0).find("input").prop("checked",true);
		}else{
			$divs.eq(0).removeClass("singleSelect");
			$divs.eq(0).find("input").prop("checked",false);
		}
	}
	
	var parameter_id = $input.closest("dl").attr("parameter_id");
	var $pli = $("li.pli_"+parameter_id);
	
	if($checkeds.length>0){
		var text = '';
		$checkeds.each(function(index){
			var value = $(this).attr("val");
			if(index>0){
				text+=" "
			}
			text+=value;
		});
		var $pa = $pli.find("a").attr("title",text);
		$pa.find("span").text(text);
		$pli.show();
	}else{
		$pli.hide();
	}
	$mmGrid.load({pageNumber:1});
	
}

function deleteParam(e) {
  var $li = $(e).closest("li")
  var paramId = $li.attr("class").split("_")[1];
  var $dl = $("dl[parameter_id=\'"+paramId+"\']");
  $dl.find("div.singleSelect").removeClass("singleSelect");
  $dl.find("input.search_strs:checked").prop("checked",false);
  
  var $first_div = $dl.find("div:first");
  $first_div.addClass("singleSelect");
  $first_div.find("input").prop("checked",true);
  $li.hide();
  $mmGrid.load({pageNumber:1});
}

function deleteAllParam(e) {
    var $parameter_strss = $("input.search_strs:checked");
    $parameter_strss.each(function() {
      $(this).prop("checked",false);
      $(this).closest("div").removeClass("singleSelect");
    });
    var $wx_div = $(".wx").addClass("singleSelect");
    $wx_div.find("input").prop("checked",true);
    $(".selected-parameter li").hide();
    $mmGrid.load({pageNumber:1});
}

function remoceChecked(e) {
	var $input = $(e);
	var $dd = $input.closest("dd");
	$dd.children("div").removeClass("singleSelect");
	$input.closest("div").addClass("singleSelect");
    var $inputValueIds = $dd.find("input");
    $inputValueIds.prop("checked",false);
    $input.prop("checked",true);
    if(false && $input.is(":hidden")){
    	$mmGrid.load({pageNumber:1});
	}
	
	var parameter_id = $input.closest("dl").attr("parameter_id");
	var $pli = $("li.pli_"+parameter_id);
	$pli.hide();
	$mmGrid.load({pageNumber:1});
	
}

function addColor(e) {
    $(e).find(".close").css("color","#21a786");
}
function delColor(e) {
    $(e).find(".close").removeAttr("style");
}

//点击当前分类时间
function catrgoryClick(id) {

    $("#productCategoryId").val(id);
    $("input.search_strs").prop("checked", false);
    $("input[name='range_strs']：hidden").val();
    $mmGrid.load({pageNumber:1});
}

function checkView(e,id){
	var storeId = $("#storeId").val();
	var url  = '/product/product/content/${code}.jhtml?flag=1&storeId='+storeId+'&id='+id;
	parent.create_iframe(url,e);
}

//图形化展示
// function useImage(e){
// 	$(".table-responsive").empty().html(
// 	  '<table id="table-m1"></table>'+
//       '<div id="body-paginator" style="text-align:left;">'+
//         '<div id="paginator"></div>'+
//       '</div>'
//     );
//     var flag = 0;
//     if(!$(e).prop("checked")){
//      	flag = 1;
// 	}
// 	initTable(flag);
// 	$("#flag", window.parent.document).val(flag);
	 
// }

function editParam(t,parameterId){
	var $this = $(t);
	var value = $this.val();
	var txt = '';
	var $div = $this.closest("div.rangeDiv");
	if($this.hasClass("minValue")){
		var maxValue = $div.find("input.maxValue").val();
		if(value!='' || maxValue!='')txt= parameterId+'_'+value+"_"+maxValue;
	}else{
		var minValue = $div.find("input.minValue").val();
		if(value!='' || minValue!='')txt= parameterId+'_'+minValue+"_"+value;
	}
	$div.find("input[name='range_strs']").val(txt);
}
function editQty(t,e,parameterId){
	if(extractNumber(t,3,false,e)){
		editParam(t,parameterId);
	}
}

function sortClick(e){
	var orderParam = $(e).attr("param");
	var orderDirect = '';
	if($(e).hasClass("on")){
		orderDirect = 'asc';
		$(e).removeClass("on");
		$(e).siblings().removeClass("on");
	}else{
		orderDirect = 'desc';
		$(e).addClass("on");
		$(e).siblings().removeClass("on");
	}
	var html = "/product/product/list_photo/${code}.jhtml?flag=0";
	var id = $("productCategoryId").val();
	html += "&productCategoryId"+id;
	$("#orderParam").val(orderParam);
	$("#orderDirect").val(orderDirect);
	$mmGrid.load({pageNumber:1});
}

/* function open_new_tab(e){
	var tid = $(e).attr("data-id");
	var reflush = $(e).attr("reflush"); 
	var tname = $(e).attr("data-name");
	var title = $(e).attr("data-title");
	if(tname!=undefined && (tid=="" || tid==undefined)){
		var $i = parent.$(".link-btn[data-name='"+tname+"']");
		if($i!=undefined && $i.length>0){
			tid = $i.attr("data-id");
			$(e).attr("data-id",tid);
		}else{
			var second_id = $(e).attr("second-id");
			if(second_id!=undefined && second_id!=""){
				tid = second_id;
				$(e).attr("data-id",tid);
			}else{
				return false;
			}
			
		}
	}
	var tsrc=$(e).attr("data-src");
	var status = true;
	var $this = parent.$(".h-Topbar li");
	var size = parent.$('[superId="'+$(e).attr("val")+'"].nav-third > a').length;
	if(size>0){
		return false;
	}
	$this.each(function(){
		var txt = $(this).attr("data-id");
		if(tid == txt){
			status = false;
            return false;	
		}
	});
	if(status){
		if($this.length>=20){
			$.message_alert('最多只能打开20个窗口，请先关闭一些窗口！');
			return false;
		}
		$this.removeClass("cur");
		var str = '';
		if(reflush=='true'){
			str = ',true';
		}
		parent.$(".h-Topbar ul").append('<li data-id="'+tid+'" onclick="changeTab(this'+str+')" class="topbar-'+tid+' cur" title="'+title+'"><a href="#">'+tname+'</a><i class="js-close" onclick="closeTab(this)"></i></li>')
		parent.$(".mainContent > div").hide();
		
		var $content = $('<div class="tab-lable-'+tid+'" style="display:block"><iframe src="'+ tsrc +'"  style="width: 100%;height: 100%;"></iframe></div>');
		parent.$(".mainContent").append($content);
	
	}else{
		var $obj = parent.$(".mainContent").find(".tab-lable-"+ tid +"");
		var $iframe = $obj.find("iframe");
		var url = $iframe.attr("src")
		if(tsrc!=undefined && tsrc!=''){
			url = tsrc;
		}
		$iframe.attr("src",url);
		$obj.show().siblings().hide();
		parent.$(".topbar-"+tid).addClass("cur").siblings().removeClass("cur")
	}
}
 */
</script>
<style>
.mmGrid{height:100% !important;}
.pro-c-list{overflow-y:initial; height:auto !important}
.pro-c-list li{width: 165px;height: 250px;margin: 6px;}
.ctl-filter dd {    min-height: 34px;    margin: 0px;}
.ctl-filter dd div{text-align: center;margin-top:5px;padding-left: 5px;margin-right: 0;min-width: 50px;}
.ctl-filter dd div .text {    margin-top: -3px;}
.ctl-filter dd label span{padding: 0 5px;display:block;}
.ctl-filter .ctl-filter-dl .selected-parameter a {position: relative;float: left;margin-left: 5px;padding: 0 19px 0 8px;line-height: 22px;overflow: hidden;border: 1px solid #4489ce;color: #333;font-weight:bold;margin-top: 5px;border-radius: 2px;}
.ctl-filter .ctl-filter-dl .selected-parameter a i{position:absolute;right:-3px;top:20%;font-size:18px}
.ctl-filter .ctl-filter-dl .selected-parameter a .close{display:inline-block;cursor:pointer;line-height:24px;font-family:"微软雅黑"!important}
.ctl-filter .ctl-filter-dl .selected-parameter .reset{position:absolute;right: 17px;top:8px;border:0 none;padding: 1px 0 1px 16px;background-position: -119px -74px;color:#666;cursor:pointer;background-image:url(http://icon.zol-img.com.cn/products/v3/list/list20150210.png);background-repeat:no-repeat;line-height:21px;}
.ctl-filter .ctl-btn .ctl-ok,.ctl-filter .ctl-btn a:hover{border:solid 1px #1fb28e;color:#1fb28e}
div.singleSelect span{background:#4489ce;color:#fff;border-radius: 2px;}
a.btn-cart{background: url(/resources/images/button/ico-add-p.png) no-repeat center;width: 27px;height: 27px;cursor: pointer;display: inline-block;}
.pro-c-list li:hover {    background: #4489ce;    border: solid 1px #4489ce;    color: #FFF;}
.pro-c-list li:hover a,.pro-c-list li:hover .price,.pro-c-list li:hover .no{color:#FFF}

/* 05-15 头部  */

.nav > dl > a >.selected_dt{background: #FFF;color: #2A6494 !important;}

.t-logo {float: left;margin: 0 32px 12px 11%;}
.t-logo img{width: 70px;height:70px;}


.pro-c-list{overflow-y:initial; height:auto !important}
.pro-c-list li{width: 172px;height: 260px;margin: 6px;}
.pro-c-list li:hover {    background: #4489ce;    border: solid 1px #4489ce;    color: #FFF;}
.pro-c-list li:hover a,.pro-c-list li:hover .price{color:#FFF}
.search{margin: 15px auto;overflow:hidden;width: 534px;float:left;}
.search .search-box{border: solid 2px #2A6494;width: 530px;height: 34px;line-height: 34px;}
.search .search-box input[type="text"]{border:none;height: 34px;width: 424px;padding: 0 5px;float: left;}
.search .search-box .btn-search{width: 82px;height: 34px;background: #2A6494;font-size: 15px;color:#FFF;border:none;cursor:pointer;float:right;letter-spacing: 4px;padding-left: 4px;}
.search .search-keys{overflow: hidden;height: 28px;line-height: 28px;}
.search .search-keys a{margin-right: 20px; display:inline-block;}
.nav{ height:42px; line-height:42px; border-top:solid 1px #eee; border-bottom:solid 1px #eee;position:relative; box-shadow:0 0 5px #eee; background:#2A6494;margin: 0 40px 10px 40px;padding: 0 5px 0 5px;}
.nav > dl{ float:left; text-align:center; min-width:128px}
.nav>dl>a>dt.title-dt{position:relative;top:0;z-index:22;font-size:15px;width:100%;color:#FFF}
.nav>dl>a>dt.title-dt:after{content:"";width:1px;height:16px;background:#c5c5c5;position:absolute;right:-1px;top:50%;margin-top:-8px}
.nav>dl>dd{position:absolute;top:43px;left:0;z-index:11;box-shadow:0 0 5px #eee;width:100%;display:none}
.nav>dl:hover>a>dt{background:#FFF;color:#2A6494;cursor:pointer}
.nav>dl:hover>a>dt:after{display:none}
.nav>dl:hover dd{display:block}
.nav a{display:block}
.nav>dl>a>dt.title-dt img {width: 20px;height: 20px;float: right;margin: 12px 17px 0 0;}
.nav > dl:first-child > a >.selected_dt{background: #5aa1ed; color:#fff !important;}

.crumbs-nav{ margin:5px 40px;}
.crumbs-nav:after{content:""; display:block; clear:both}
.crumbs-nav .crumbs-nav-item{padding:0 5px; float:left;margin-bottom: 8px;}
.crumbs-nav .crumbs-nav-item.change-level{ line-height:26px;float:}
.crumbs-nav .crumbs-nav-item.change-level a{ display:inline-block; margin:0 10px 0 5px;}
.crumbs-nav .crumbs-nav-item.one-level .crumbs-link{font-size: 18px;font-weight: 700;float: left; margin-right:10px}
.crumbs-nav .crumbs-nav-item .crumbs-arrow{ font-family: serif;font-style: initial; font-size:14px; font-weight:400;margin-top: 5px;float: left;}
.menu-drop{ display:inline-block; position:relative;float: left;margin-right: 10px;}
.menu-drop .trigger{display:inline-block;height:24px;padding:0 4px 0 8px;border:1px solid #ddd;line-height:24px;float:left;position: relative;background: #FFF; }
.menu-drop .menu-drop-arrow{font-family: serif;font-style: initial; font-size:14px;transform: rotate(90deg);-ms-transform: rotate(90deg);-moz-transform: rotate(90deg);-webkit-transform: rotate(90deg);-o-transform: rotate(90deg);display: inline-block;margin-top: 10px;float: right;}
.menu-drop .menu-drop-main{width:350px;padding:10px 0 10px 8px;position:absolute;left:0;top:25px;border:1px solid #ddd;background-color:#fff; z-index:99; display:none}
.menu-drop .menu-drop-list li{float:left;he2ight:24px;overflow:hidden;line-height:24px;margin-right:5px;white-space:nowrap}
.menu-drop .menu-drop-arrow{padding-right:20px;}
.crumbs-nav .crumbs-nav-item:hover .menu-drop .trigger{border:solid 1px #2A6494;border-bottom: solid 1px #FFF;color:#2A6494;z-index: 999; font-weight:700}
.crumbs-nav .crumbs-nav-item:hover .menu-drop-main{ display:block;border:solid 1px #2A6494}
.user-info { float: left; margin-top: 26px; margin-left: 40px;font-size: 15px; width: 160px; color: #555;font-weight: 700;}
 .user-info span { font-size: 16px;}
 .cart-box:hover { background: #2a6494; color: #FFF;}
 .cart-box .ico-cart { display: inline-block; width: 20px; height: 20px; vertical-align: sub; background: url(../../../resources/images/button/ico-cart0.png) no-repeat center; margin-right: 6px;}
 .cart-box { border: 2px solid #2a6494;  height: 36px;  width: 128px; margin-top: 16px; margin-left: 15px; line-height: 36px; text-align: center; font-size: 15px; background: #fefefe;margin-right: 40px;color: #2a6494;}
 .cart-box b { color: #ff6600;}
 .user-info .ico-edit { width: 20px; height: 20px; display: inline-block; vertical-align: bottom; background: url(../../../resources/images/button/ico-pen.png) no-repeat center; margin-left: 4px;}
 .sort-wrap { line-height: 25px; overflow: hidden; padding: 2px 3px; font-size: 13px; background: #dfdfdf;}
.sort-wrap .sort-item:hover {  color: #F3616E;}
.sort-wrap .sort-item {float: left;background: none;padding: 0 8px; color: #333;}
.sort-wrap .sort-item.on em { background-position: right center;}
.sort-wrap .sort-item em { background: url(../../../resources/images/button/ico-arrows.png) left center no-repeat;background-position-x: left; background-position-y: center; display: inline-block;width: 7px;height: 8px; margin-left: 7px;}
.sort-wrap .sort-item.on { color: #F3616E; background: #FFF;}
#fly-img-div{position:fixed;top: 5px;right: 7px;}
</style>
</head>
<body>
<form id="listForm" action="/product/product/list_photo/${code}.jhtml" method="get">
<input type="hidden" name="productCategoryId" value="${productCategoryId}" id="productCategoryId">
<input type="hidden" name="orderParam" id="orderParam" ></input>
<input type="hidden" name="orderDirect" id="orderDirect" ></input>
<div class="tab-top">
	<a href="#" >
		<i class="tab-ico"></i>
		<em class="tab-text">顶部</em>
	</a>
</div>

  <div class="bar">	
   <div class="t-logo"><img src="/resources/images/cp-logo.png"></div>
    <!-- <div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">搜索</a></div>
    <div id="searchDiv">
             <div id="search-content">
                <input type="hidden" name="memberId" value="">
                    <dl>
                    	<dt><p>${message("12211")}：</p></dt>
                        <dd>
                            <span class="ax-span"><input type="text" class="text" name="vonderCode" value="" btn-fun="clear"><a class="close" data-event="callClose" name="e" href="javascript:void(0);" style=" display: none;"></a></span>
                        </dd>
                    </dl>
                     <dl>
                     	<dt><p>产品名称：</p></dt>
                        <dd>
                            <span class="ax-span"><input type="text" class="text" name="name" value="${keyword}" btn-fun="clear"><a class="close" data-event="callClose" name="e" href="javascript:void(0);" style="display: none;"></a></span>
                        </dd>
                    </dl>
                    <dl style="float: right;right: 5px;">
                        <dd>
                          	<label>
								<input type="checkbox" onclick="useImage(this)"  [#if flag!=1]checked[/#if]> 图形化展示
							</label>
                        </dd>
                    </dl>
          </div>
   </div> -->
   
   <div class="search"> 
    <div class="search-box">
      <input type="text" class="fl" name="keyWords" id="searchKeyword" value="${keyWords}">
      <input type="button" value="搜索" class="btn-search fr" id="searchButton">
    </div>
	</div>
	    <div class="user-info">客户：<label><span id="showStoreName">${storeName}</span><em class="ico-edit" id="selectStore"></em></label>
	    <input type="hidden" name="storeId" id="storeId" value="${storeId}"/>
	    <input type="hidden" name="storeName" id="storeName" value="${storeName}"/>
	    </div>
    <a href="javascript:void(0)" class="fr cart-box" onclick="parent.open_new_tab(this)" data-id="161" data-src="/b2b/cart/list/${code}.jhtml" data-name="购物车" data-title="购物车" val="161"><i class="ico-cart"></i>购物车<b id="cartItemTotal"> 0 </b></a>
   <div class="clearfix"></div>
   <div class="nav">
    <dl>
        <a href="/product/product/list_photo/${code}.jhtml?flag=0&productCategoryId=&keyword=${keyword}&storeId=${storeId}&storeName=${storeName}"><dt class="[#if productCategoryId??][#else]selected_dt [/#if]title-dt">全部分类<!-- <img src="/resources/images/all-category.png"/> --></dt></a>
    </dl>
   	[#list productCategorys as pc]
   	[#if pc.parent??]
   	[#else]
	<dl>
        <a href="/product/product/list_photo/${code}.jhtml?flag=0&productCategoryId=${pc.id}&keyword=${keyword}&storeId=${storeId}&storeName=${storeName}"><dt class="[#if pc.id==selectedCategory.id||selectedCategory.treePath?contains(pc.id?string)]selected_dt [/#if]title-dt">${pc.name}</dt></a>
    </dl>
    [/#if]
    [/#list]
	</div>
	
<div class="crumbs-nav">
	
	[#if selectedCategory??]
	[#if selectedCategory.parent??]
	
	[#assign grade=selectedCategory.grade]
	[#assign topCategory=selectedCategory ]
	[#list 0..(grade-1) as g]
		[#assign topCategory=topCategory.parent ]
	[/#list]

	<div class="crumbs-nav-item one-level">
    	<a href="/product/product/list_photo/${code}.jhtml?flag=0&productCategoryId=${topCategory.id}&keyword=${keyword}&storeId=${storeId}&storeName=${storeName}" class="crumbs-link">${topCategory.name}</a>
        <i class="crumbs-arrow">&gt;</i>
	<div class="crumbs-nav-item change-level"> </div>
      <!--    <div class="sort-wrap fr">
            <a href="#" class="sort-item on">销量<em></em></a>
            <a href="#" class="sort-item">价格<em></em></a>
            <a href="#" class="sort-item">评价<em></em></a>
            <a href="#" class="sort-item">上架时间<em></em></a>
        </div>  -->
    </div>
      
	<div class="cty"> </div>
	
   
	<script type="text/javascript">
	$().ready(function() {
	$("div.cty").empty(str);
	[#assign sCategory=selectedCategory ]
	[#list 0..(grade-1) as g]
	var str='';
    str='<div class="crumbs-nav-item" >'+
    	'<div class="menu-drop">'+
       		'<div class="trigger"><span class="cur" [#if sCategory.id==productCategoryId]style="color:#f60;"[/#if]>${sCategory.name}</span><i class="menu-drop-arrow">&gt;</i></div>'+
            '<div class="menu-drop-main">'+
            	'<ul class="menu-drop-list">'+
            		[#list sCategory.parent.children as spChildren]
                	'<li><a href="/product/product/list_photo/${code}.jhtml?flag=0&productCategoryId=${spChildren.id}&keyword=${keyword}&storeId=${storeId}&storeName=${storeName}" title="${spChildren.name}" >${spChildren.name}</a></li>'+
                	[/#list]
                '</ul>'+
            '</div>'+
    	'</div>';
    	[#if sCategory.children?size>0]  
        str+='<i class="crumbs-arrow" >&gt;</i>';
        [/#if]
    str+='</div>';
    $("div.cty").prepend(str);
    [#assign sCategory=sCategory.parent ]
    [/#list]
	})
     </script>
    <div class="crumbs-nav-item change-level">
    	[#list selectedCategory.children as sChildren]
    	<a href="/product/product/list_photo/${code}.jhtml?flag=0&productCategoryId=${sChildren.id}&keyword=${keyword}&storeId=${storeId}&storeName=${storeName}">${sChildren.name}</a>
    	[/#list]
    </div>
      <!--  <div class="sort-wrap fr">
            <a href="#" class="sort-item on">销量<em></em></a>
            <a href="#" class="sort-item">价格<em></em></a>
            <a href="#" class="sort-item">评价<em></em></a>
            <a href="#" class="sort-item">上架时间<em></em></a>
        </div> -->
    [#else]
    <div class="crumbs-nav-item one-level">
    	<a href="/product/product/list_photo/${code}.jhtml?flag=0&productCategoryId=${selectedCategory.id}&keyword=${keyword}&storeId=${storeId}&storeName=${storeName}" class="crumbs-link" style="color:#f60;">${selectedCategory.name}</a>
        <i class="crumbs-arrow">&gt;</i>
      
    </div>
    <div class="crumbs-nav-item change-level">
    	[#list selectedCategory.children as sChildren]
    	  <a href="/product/product/list_photo/${code}.jhtml?flag=0&productCategoryId=${sChildren.id}&keyword=${keyword}&storeId=${storeId}&storeName=${storeName}">${sChildren.name}</a>
    	[/#list]
    </div>
     <!--   <div class="sort-wrap fr">
            <a href="javascript:void(0)" onclick="sortClick(this)" class="sort-item"  param="sale">销量<em></em></a>
            <a href="javascript:void(0)" onclick="sortClick(this)"  class="sort-item" param="price">价格<em></em></a>
            <a href="javascript:void(0)" onclick="sortClick(this)"  class="sort-item" param="comment">评价<em></em></a>
            <a href="javascript:void(0)" onclick="sortClick(this)" class="sort-item" param="marketTime">上架时间<em></em></a>
        </div> -->
    [/#if]
    [/#if]
</div>
   
  </div>
     <div class="ctl-filter">
    	<div class="ctl-filter-dl">
    	 <dl class="selected-parameter">
	      <dt>已选条件：</dt>
	      <dd>	
	      		<ul>
	      			[#if selectedCategory!=null && selectedCategory.isLeaf]
			    	  [#list parameterGroups as parameterGroup]
			            [#list parameterGroup.parameters as parameter]
			            	[#if parameter.searchType==1 || parameter.searchType==2]
				      		   <li class="pli_${parameter.id}" style="display:none;">
					              <a href="javascript:void(0);"  title="" onclick="deleteParam(this)" onmouseover="addColor(this)" onmouseout="delColor(this)">
					                 <span></span>
					                <i class="close"></i>
					              </a>
				               </li>
				        	[/#if]
				    	[/#list]
				   	[/#list]
	               [/#if]
	           </ul>
	        <span class="reset" onclick="deleteAllParam(this)">清空</span>
	      </dd>
	    </dl>
    	[#assign dlNum=0]
    	[#if selectedCategory!=null && selectedCategory.isLeaf]
    	  [#list parameterGroups as parameterGroup]
            [#list parameterGroup.parameters as parameter]
            [#if parameter.searchType==1 || parameter.searchType==2]
            	[#assign dlNum=dlNum+1]
            	<dl class="js-count" parameter_id=${parameter.id} [#if dlNum>2]style="display:none;"[/#if]>
		          <dt>${parameter.name}：</dt>
		          <dd>
		        <div class="wx singleSelect">
		        	<label>
		            <input type="checkbox" value="" onclick="remoceChecked(this)" checked>
                  	<span>不限</span>
                  	</label>
                </div>
                	[#if parameter.searchType==1]
	            	 [#list parameter.parameterValues as parameterValue]
	            	 	[#assign param_str=parameter.id+"_"+parameterValue.id]
	            		<div>
	            			<label>
	            			<input type="checkbox" name="parameter_strs" class="search_strs" value="${param_str}" onclick="oneParamSub(this)" val="${parameterValue.value}">
	            			<span >${parameterValue.value}</span>
	            			</label>
	            		</div>
	            	 [/#list]
	            	[#elseif parameter.searchType==2]
	            		[#list parameter.parameterValues as parameterValue]
		            	 	[#assign param_str=parameter.id+"_"+parameterValue.minValue+"_"+parameterValue.maxValue]
		            		<div>
		            			<label>
		            			<input type="checkbox" name="range_strs" class="search_strs" value="${param_str}" onclick="oneParamSub(this)" val="${parameterValue.minValue+"-"+parameterValue.maxValue}">
		            			<span >${parameterValue.minValue+"-"+parameterValue.maxValue}</span>
		            			</label>
		            		</div>
		            	 [/#list]
		            	 [#--
		            	 <div>
		            	 	<input value="" style="width: 80px;" type="number" class="text minValue" oninput="editQty (this,event,${parameter.id})" onpropertychange="editQty(this,event,${parameter.id})" btn-fun="clear">
		            	 	<span>--</span>
		            	 	<input value="" style="width: 80px;" type="number" class="text maxValue" oninput="editQty (this,event,${parameter.id})" onpropertychange="editQty(this,event,${parameter.id})" btn-fun="clear">
		            	 	<input type="hidden" name="range_strs" value="">
		            	 </div>
		            	 --]
		            	 <div class="rangeDiv">
							<div class="nums-input ov" style="width: 120px;margin-top:-2px;float:left;">
								<input class="b decrease" value="-" onmousedown="decrease(this,event)" onmouseup="editQty (this.nextElementSibling,event,${parameter.id})" type="button">
								<input class="t minValue" mindata="0" oninput="editQty (this,event,${parameter.id})" onpropertychange="editQty (this,event,${parameter.id})" value="" type="text">
								<input value="+" class="b increase" onmousedown="increase(this,event)" onmouseup="editQty (this.previousElementSibling,event,${parameter.id})" type="button">
							</div>
							<div style="float:left;line-height: 15px;min-width: 20px;">--</div>
							<div class="nums-input ov" style="width: 120px;margin-top:-2px;float:left;">
								<input class="b decrease" value="-" onmousedown="decrease(this,event)" onmouseup="editQty (this.nextElementSibling,event,${parameter.id})" type="button">
								<input class="t maxValue" mindata="0" oninput="editQty (this,event,${parameter.id})" onpropertychange="editQty (this,event,${parameter.id})" value="" type="text">
								<input value="+" class="b increase" onmousedown="increase(this,event)" onmouseup="editQty (this.previousElementSibling,event,${parameter.id})" type="button">
							</div>
							<input type="hidden" name="range_strs" value="">
						</div>
	            	[/#if]
            	 </dd>
		          <div class="ctl-btn">
		            <a href="javascript:void(0)" onClick="ctlMore(this)" class="ctl-more"><span class="ctl-txt">更多</span><i class="arrow-b"><i></i><em></em></i></a>
		            <a href="javascript:void(0)" class="ctl-ok" onclick="subForm()">确定</a>
		            [#--<a href="javascript:void(0)" class="ctl-mutil" onClick="ctlCheck(this)"><span class="ctl-txt">多选</span><i class="dpl-plus"><i></i><em></em></i></a>--]
		          </div>
		        </dl>
            [/#if]
            [/#list]
        	[/#list]
      	[/#if]
        </div>
    	 <div class="showMore" onClick="showCtlmore(this)" style="position:initial;display:block">
    		<b>
    			<span class="ctl-txt">更多</span>
    			<i class="arrow-b"><i></i><em></em></i>
    		</b>
    	</div> 
    </div>
  
  <div class="pro-c-list table-responsive" >
    <table id="table-m1"></table>
    <div id="body-paginator" style="text-align:left;">
        <div id="paginator"></div>
    </div>
  </div>
  <!-- <div class="floatDiv" style="position:fixed;top:0;left:10px;height:38px;text-align:right;">
  	<p style="float:left;line-height:38px;font-size:14px;">当前分类：${selectedCategory.name}</p>
  </div> -->
</form>
<div id="fly-img-div" ></div><!-- 这是飞动效果定点  不要删 -->
</body>
</html>