<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("价格表列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function edit(id){
	parent.change_tab(0,'/product/product_price_head/edit/${code}.jhtml?id='+id+'&isCheck= ${isCheck}');
}
function add(){
	parent.change_tab(0,'/product/product_price_head/add/${code}.jhtml');
}
$().ready(function() {
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	var order_sttuss = {'0':'已保存', '1':'处理中', '2':'已生效', '3':'已失效'};
	var wf_state ={'0':'未启动','1':'审核中','2':'已完成','3':'驳回',}
	var cols = [
		{ title:'${message("价格单号")}', name:'sn' , align:'center', renderer: function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
		}},
		{ title:'${message("价格类型")}', name:'member_rank_name', align:'center' },
		{ title:'${message("机构")}', name:'saleOrgName', align:'center' },
		[#--{ title:'${message("产品名称")}', name:'name' , align:'center',isLines:true},
		{ title:'${message("产品型号")}', name:'model' , align:'center',isLines:true},
		{ title:'${message("12211")}', name:'vonder_code' , align:'center',isLines:true },
		{title:'${message("产品分类")}',name:'product_category_name',align:'center',isLines:true},
		{ title:'${message("产品等级")}', align:'center',isLines:true,renderer: function(val,item,rowIndex){
			if($("#type").val()==2){
			return '<span class="gradeName">二等品</span>';
			}else{
			return '<span class="gradeName">优等品</span>';
			}
		}},
		{ title:'${message("产品描述")}', name:'description', align:'center',width:160,isLines:true},
		{ title:'${message("价格")}', name:'store_member_price' , align:'center',isLines:true , renderer: function(val,item){
			if(item.store_member_price!=null){
				return '<span class="red">'+currency(item.store_member_price,true)+'</span>';
			}
		}},
		{ title:'${message("平台结算价格")}', name:'sale_org_price' ,hidden:true, align:'center',isLines:true , renderer: function(val,item){
			if(item.sale_org_price != null){
				return '<span class="red">'+currency(item.sale_org_price,true)+'</span>';
			}
		}},
		{ title:'${message("开始时间")}', name:'start_date' , align:'center',isLines:true,renderer:function(val){
			if(val !=null && val.length>10){
				var str = val;
				return str.substring(0,10);
			}
		}},
		{ title:'${message("结束时间")}', name:'end_date' , align:'center',isLines:true,renderer:function(val){
			if(val !=null && val.length>10){
				var str = val;
				return str.substring(0,10);
			}
		}},--]
		{ title:'${message("单据状态")}', name:'status' , align:'center', renderer: function(val){
			var result = order_sttuss[val];
			if(result!=undefined)return result;			
		}},
		{ title:'${message("流程状态")}', name:'wf_state' , align:'center', renderer: function(val){
			var result = wf_state[val];
			if(result!=undefined)return result;			
		}},
		
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },

	];
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
        cols: cols,
        fullWidthRows:true,
        url: '/product/product_price_head/list_data.jhtml',
        method: 'post',
        params:function(){
        	return $("#listForm").serializeObject();
        },
        root: 'content',
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml'
	});
	  //查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1'
	});
	
	//查询价格等级
	$("#selectMemberRank").bindQueryBtn({
		type:'memberRank',
		title:'${message("查询价格等级")}',
		url:'/basic/member_rank/select_memberRank.jhtml'
	});

});

function productPriceImport(e){	
	excel_import(e,{
		title:"${message("产品会员价")}",
		url:"/product/product_price_head/import_excel_code.jhtml",
		template:"/resources/template/product/productPrice-100.xls",
		callback:function(resultMsg){
			$("#searchBtn").click();
		}
	})
}	

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = '/product/product_price_head/to_condition_export.jhtml';//分页导出统计页面
	var url = '/product/product_price_head/condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的会员价单！")}';//提示
	var url = '/product/product_price_head/selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

</script>
</head>
<body>
	<form id="listForm" action="/product/product_price_head/list/${code}.jhtml" method="get">
	<input type="hidden" name="isCheck" id="isCheck" value="${isCheck}" >
	<input type="hidden" name="objTypeId" value="${objTypeId}" >
	<input type="hidden" name="objid" value="${objid}" >
		<div class="bar">
			<div class="buttonWrap">
				[#--
				<a href="javascript:void(0);" id="importButton" class="iconButton" onclick="productPriceImport(this)">
					<span class="importIcon">&nbsp;</span>${message("导入")}
				</a>
				<a href="javascript:;" id="export1Button" onclick="segmentedExport(this)" class="iconButton">
					<span class="exportIcon">&nbsp;</span>${message("条件导出")}
				</a>
				<a href="javascript:;" id="export1Button" onclick="exportExcel();" class="iconButton">
					<span class="exportIcon">&nbsp;</span>${message("选中导出")}
				</a>
				--]
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="productPriceImport(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
					</ul>
				</div>
				[#if isCheck==1 && objid==null]
				<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
				[/#if]
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
        			<dt><p>${message("单据状态")}：</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="[#if isCheck??]${message("已保存")}[/#if]" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">	
				       			<label><input  class="check js-iname" name="status" value="0" type="checkbox" [#if isCheck??]checked[/#if]/>${message("已保存")}</label>
				       			<label><input  class="check js-iname" name="status" value="1" type="checkbox"/>${message("处理中")}</label>
				       			<label><input  class="check js-iname" name="status" value="2" type="checkbox"/>${message("已生效")}</label>
				       			<label><input  class="check js-iname" name="status" value="3" type="checkbox"/>${message("已失效")}</label>
				      		</div>
						</div>
        			</dd>
        		</dl>
        		<dl>
			    	<dt><p>${message("产品名称")}：</p></dt> 
		    		<dd>
						<span style="position:relative">
							<input type="hidden" name="productId" class="text productId" btn-fun="clear" value=""/>
							<input type="text" name="productName" class="text productName" maxlength="200" value="" onkeyup="clearSelect(this)"/>
							<input type="button" class="iconSearch" value="" id="selectProduct">
						</span>
		    		</dd>
			    </dl>
        		<dl>
			    	<dt><p>${message("机构名称")}：</p></dt> 
		    		<dd>
						<span style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value=""/>
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" value="" onkeyup="clearSelect(this)"/>
							<input type="button" class="iconSearch" value="" id="selectSaleOrg">
						</span>
		    		</dd>
			    </dl>
			    <dl>
			    	<dt><p>${message("价格类型")}：</p></dt> <!-- 价格等级 -->
			    	[#--<dd>
			    		<span class="search" style="position:relative">
						<input type="hidden" name="memberRankId" class="text memberRankId" id="memberRankId" btn-fun="clear" />
						<input type="text" name="memberRankName" class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" id="memberRankName" />
						<input type="button" class="iconSearch" value="" id="selectMemberRank">
						</span>
			    	</dd>--]
			    		<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			[#list memberRankList as memberRank]
				       			<label><input  class="check js-iname" name="memberRankId" value="${memberRank.id}" type="checkbox"/>${memberRank.name}</label>					
						        [/#list]	
				      		</div>
						</div>
        			</dd>
			    </dl>
			    <dl>
					<dt><p>${message("开始时间")}:</p></dt>
						<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
					    </dd>
                    <dt><p>--</p></dt>
					    <dt><p>${message("截止时间")}:</p></dt>
					    <dd class="date-wrap">
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
					    </dd>
				</dl>
			</div>
		<div class="search-btn" style="height:32px"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
			
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>