<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("会员价")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
function view_iframe(url){
	var $view_iframe = $("#view_iframe");
	var src = $view_iframe.attr("src");
	if(src==""){
		$view_iframe.attr("src",url);
	}
}

$().ready(function() {
	var h = $(window).height();
	$("iframe").height(h-$("#tab").outerHeight());

	
	var objid = '${objid}';
	var isCheck = '${isCheck}';
	if(objid!=''){
		var index = 0;
		var url ='/product/product_price_head/edit/${code}.jhtml?id=${objid}&isCheck=1';
		change_tab(index,url);
	}else{
		var index = 1;
		var url ='/product/product_price_head/list/${code}.jhtml?isCheck=${isCheck}';
		change_tab(index,url);
	}

});

function change_tab(index,url,flag){
	$("#tab input").removeClass("current").eq(index).addClass("current");
	var $tabContent = $(".tabContent").hide();
	var $iframe = $tabContent.eq(index).show().find("iframe");
	if(flag==undefined){
		$iframe.attr("src",url);
	}else{
		 if($iframe.attr("src")=="" && url!=""){
			$iframe.attr("src",url);
		}
	}
}
</script>
</head>
<body style="overflow:hidden;">
	<div> 
		 <ul id="tab" class="tab tab-first">
		 	<li>
	            <input type="button" value="${message("常规")}" [#if isCheck ==1 && objid==null]onclick="change_tab(0,'/product/product_price_head/add/${code}.jhtml',1)"[#elseif objid!=null]onclick="change_tab(0,'/product/product_price_head/edit/${code}.jhtml?id=${objid}&isCheck=1',1)"[#else]onclick="change_tab(0,'',1)"[/#if]>
	        </li>
  			<li [#if objid!=null]style="display:none;"[/#if]>
	            <input type="button" value="${message("列表")}" onclick="change_tab(1,'/product/product_price_head/list/${code}.jhtml?isCheck=${isCheck}',1)">
	        </li>
	    </ul>
	    <div class="tabContent" style="display:none;">
			<iframe src="" style="width:100%;"></iframe>
		</div>
		<div class="tabContent" style="display:none;" >
			<iframe src="" style="width:100%;"></iframe>
		</div>
	</div>
</body>
</html>