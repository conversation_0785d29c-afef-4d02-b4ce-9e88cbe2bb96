<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增价格表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e)
}
function editPrice(t,e){
	extractNumber(t,2,false,e)
}
function setDate(){
	var start=$("input[name='startDate']").val();
	var end=$("input[name='endDate']").val();
	var $startDate = $("input.startDate");
	$startDate.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		$tr.find(".startDate").val(start);
		$tr.find(".endDate").val(end);
	});
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $selectMember = $("#selectMember");
	var $deleteProduct = $("a.deleteProduct");
	var $addProduct = $("#addProduct");
	var itemIndex = 0;
	
	// 表单验证
	$.validator.addClassRules({
		price: {
			required: true
		},
		startDate: {
			required: true
		},
		endDate: {
			required: true
		},
		memberRankName: {
			required: true
		},
		saleOrgName: {
			required: true
		},
		start:{
			required: true
		},
		end:{
			required: true
		}
	});
	
	//查询价格等级
	$("#selectMemberRank").bindQueryBtn({
		type:'memberRank',
		title:'${message("查询价格类型")}',
		url:'/basic/member_rank/select_memberRank.jhtml'
	});
	
	//打开选择产品界面
	$addProduct.click(function(){
		var saleOrgId = $("input.saleOrgId").val();
		if (saleOrgId==undefined || saleOrgId.length == 0) {
			$.message_alert("请选择机构");
			return false;
		}
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品',
			url:'/product/product/selectProduct.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid.addRow(row);
						setDate();
					}
				}
			}
		});	
	})
	
	//打开选择产品系列界面
	$("#addProductCategory").click(function(){	
		var count = $("input.saleOrgId").length;
		if(count<1){
			$.message_alert("请选择机构");
		}
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品系列',
			url:'/product/product_category/select_productCategory.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						row.product_category_id=row.id;
						row.product_category_name=row.name;
						row.id='';
						row.name='';
						$mmGrid.addRow(row);
						setDate();
					}
				}
			}
		});
	});
	var itemIndex = 0;
	var cols = [	
//        {title:'${message("发货仓")}', align:'center',name:'account_type',width:130  ,renderer: function(val,item,rowIndex){			
// 	    var html = '<select name="productPrices['+itemIndex+'].shippingWarehouse.id" class="text">'
// 		[#list shippingWarehouses as shippingWarehouse]
// 			+'<option value="${shippingWarehouse.id}" >${shippingWarehouse.value}</option> '
// 		[/#list]
// 		+'</select>';
// 	        return html;
//         }},
       {title:'${message("产品分类")}', align:'center',width:160 , renderer: function(val,item,rowIndex){
	     return '<input type="hidden" name="productPrices['+itemIndex+'].productCategory.id" class="productId_'+item.product_category_id+' productId" value="'+item.product_category_id+'">'+item.product_category_name;
        }},
		{ title:'${message("产品名称")}', align:'left',width:160 , renderer: function(val,item,rowIndex){
			return '<input type="hidden" name="productPrices['+itemIndex+'].productStore.product.id" class="productId_'+item.id+' productId" value="'+item.id+'">'+item.name;
		}},
		{ title:'${message("产品型号")}', name:'model', align:'left',width:160},
		{ title:'${message("12211")}', name:'vonder_code', align:'left',width:160 },
		{ title:'${message("产品等级")}', hidden:true, name:'grade', align:'center', renderer: function(val,item,rowIndex){
			return '<span class="gradeName">优等品</span><input type="hidden" name="productPrices['+itemIndex+'].grade" class="grade" value="1">';
		}},
		{ title:'${message("产品描述")}', name:'description', align:'center',width:160 },
		{ title:'${message("价格 ")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price productPrice"  name="productPrices['+itemIndex+'].storeMemberPrice" value="" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
        	return html;
		}},
		{ title:'${message("平台结算价格")}', align:'center',hidden:true,renderer: function(val,item,rowIndex){
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price saleOrgPrice"  name="productPrices['+itemIndex+'].saleOrgPrice" value="0" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
        	return html;
		}},
		{ title:'${message("开始时间")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<input  id="productPrices['+itemIndex+'].startDate" name="productPrices['+itemIndex+'].startDate" class="text startDate" value="${startDate}" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\', maxDate: \'#F{$dp.$D(\\\''+'productPrices['+itemIndex+'].endDate\\\')}\'});" type="text" btn-fun="clear"/>'
		}},
		{ title:'${message("结束时间")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<input  id="productPrices['+itemIndex+'].endDate" name="productPrices['+itemIndex+'].endDate" class="text endDate" value="${endDate}" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\', minDate: \'#F{$dp.$D(\\\''+'productPrices['+itemIndex+'].startDate\\\')}\'});" type="text" btn-fun="clear"/>'
		}},		
		{ title:'${message("状态")}', name:'status' , align:'center', width:60, renderer: function(val){
			var result = '已保存';
			return result;			
		}},
		{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
			itemIndex++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteProduct(this)">删除</a>';
		}},
	];
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols,
        checkCol:false,
        fullWidthRows: true
    });
    
    $("#submit_button").click(function(){
		var count = $("input.productId").length;
		if(count<1){
			$.message_alert("会员价明细不能少于一条");
		}else{
			$("form").submit();
		}
	});
    
    $("form").bindAttribute({
    	isConfirm:true,
	    callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/product/product_price_head/edit/${code}.jhtml?isCheck= 1&id='+resultMsg.objx;
			});
	    }
	 });
    
  //查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1'
	});
});
function deleteProduct(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
	})
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增价格表")}
	</div>
	<form id="inputForm" action="/product/product_price_head/save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("价格单号")}:
				</th>
				<td>
					${productPriceHead.sn}
				</td>
				<th>
					${message("单据状态")}:
				</th>
				<td>
				</td>
				<th>
					${message("流程状态")}:
				</th>
				<td>
				</td>
				<th>
					${message("创建日期")}:
				</th>
				<td>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("价格类型")}:
				</th>
				   <td>
                	<select class="text" name="memberRankId">
						[#list memberRankList as memberRank]
						<option value="${memberRank.id}" [#if memberRank.isDefault]selected="selected"[/#if]>${memberRank.name}</option>
						[/#list]
					</select>
                </td>
				<th>${message("机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${saleOrg.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td>
			    <th>
					${message("开始时间")}:
				</th>
				<td id="start">
					<input type="text" class="text start" id="startdate" name="startDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',maxDate: '#F{$dp.$D(\'enddate\')}'});"   onchange="setDate()" btn-fun="clear"/>
				</td>
				
				<th>
					${message("结束时间")}:
				</th>
				<td id="end">
					<input type="text" class="text end" id="enddate" name="endDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',minDate: '#F{$dp.$D(\'startdate\')}'});"  onchange="setDate()"  btn-fun="clear" />
				</td>
			</tr>
			
		</table>
		
		<div class="title-style">
		${message("价格明细")}
			<div class="btns">
				<!-- 				  <a href="javascript:;" id="addProductCategory" class="button">选择产品系列</a> -->
				<a href="javascript:;" id="addProduct" class="button">选择产品</a>
	    	</div>
	    </div>
	    <table id="table-m1"></table>

	</div>
	<div class="fixed-top">
		<input type="button" id="submit_button" class="button sureButton" value='${message("保存")}'>
		<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
	</div>
	</form>
</body>
</html>