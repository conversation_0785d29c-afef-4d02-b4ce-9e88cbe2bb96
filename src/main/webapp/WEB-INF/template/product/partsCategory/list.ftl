<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript">
$().ready(function(e) {
 	var zTree = $("#js-menu").initTree({
		url:'/product/product/getNodes.jhtml',
		onClick:function(e,treeId, treeNode) {
			$("#productCategoryId").val(treeNode.id);
	        if(treeNode.id==""){
	      		$("#productCategoryName").val("");
	        }else{
	      		$("#productCategoryName").val(treeNode.name);
	        }
	        $("#searchBtn").click();
		}
	})
    zTree.addNodes(null,{'id':'','pId':'','name':'全部选配项','isParent':false});
    treeScoll();
}); 
</script>
<script type="text/javascript">
function edit(productCategoryId,partsGroupId){
	parent.change_tab(0,'edit.jhtml?productCategoryId='+productCategoryId+'&partsGroupId='+partsGroupId);
}
$().ready(function() {
	var cols = [
		{ title:'${message("产品分类")}', name:'product_category_name' , align:'center', renderer: function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.product_category_id+','+item.parts_group_id+')" class="red" >'+val+'</a>';			
		}},
		{ title:'${message("配置项")}', name:'parts_group_name' ,align:'center' },
		{ title:'${message("可选项")}', name:'parts_name' , align:'center' },
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
        cols: cols,
        fullWidthRows:true,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });

});

</script>
</head>
<body class="tree-contain">
<div class="subnav">
	<input type="hidden" id="flag" value="0">
	<input type="hidden" id="storeId" value="">
  <div class="tit"><s></s>产品分类</div>
  <div class="subn-list ztree" id="js-menu">
  	
  </div>
</div>
<label id="labBtn"></label>
<div class="main-right">
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
		<div class="buttonWrap">
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
			    	<dl>
			    		<dt><p>${message("产品分类")}:</p></dt>
			    		<dd>
								<input type="hidden" id="productCategoryId" name="productCategoryId"  class="text" value="" />
								<input type="text"  class="text" id="productCategoryName" value="" readonly />
			    		</dd>
			    	</dl>
		        </div>
			</div>
			
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	    	<div id="body-paginator">
	        	<div id="paginator"></div>
	    	</div>
		</div>
	</form>
</div>
</body>
</html>