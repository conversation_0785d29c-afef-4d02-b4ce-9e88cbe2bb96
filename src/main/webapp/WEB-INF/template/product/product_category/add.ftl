<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("添加产品分类")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript">
$().ready(function() {

	var $inputForm = $("#inputForm");
	var $browserButton = $("#browserButton");
	var $browserButton2 = $("#browserButton2");
	var $productImageTable = $("#productImageTable");
	var $clickType = $("#clickType");
	var productImageIndex = 0;
	
	$("input[name='image']").single_upload({
		uploadSize:"medium"
	});
	
	$("input[name='topIcon']").single_upload({
		uploadSize:"thumbnail"
	});
    
	$.validator.addClassRules({
		productImageFile: {
			required: true,
			extension: "${setting.uploadImageExtension}"
		},
		productImageTitle: {
			required: true
		},
		productImageOrder: {
			required: true,
			digits: true
		}
	});

	// 表单验证
	$inputForm.validate({
		rules: {
			name: "required",
			order: "digits",
			clickType: "required",
			url: "required"
		} ,
	});
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'edit.jhtml?id='+resultMsg.objx;
			})
	    }
	 });
	 
	 //查询分类
	$("#selectProductCategory").bindQueryBtn({
		type:'productCategory',
		title:'${message("查询分类")}',
		url:'/product/product_category/select_productCategory.jhtml'
	});
});

function clickTypeChange(t){
	var $this = $(t);
	if($this.val()==0){
		$("input[name='url']").prop("disabled",true);
	}else{
		$("input[name='url']").prop("disabled",false);
	}
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增产品分类")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" enctype="multipart/form-data" type="ajax" validate-type="validate">
		<input type="hidden"  name="isUsePartnerSingle" id="isUsePartnerSingle" value="true" />
		<input type="hidden" name="type" value="0"/>
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("12115")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="parentId" class="text productCategoryId" btn-fun="clear"/>
					<input type="text" name="productCategoryName" class="text productCategoryName" maxlength="200" onkeyup="clearSelect(this)" value="顶级分类" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectProductCategory">
					</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("产品分类名称")}:
				</th>
				<td>
					<input type="text" id="name" name="name" class="text " maxlength="200" btn-fun="clear" />
				</td>
				<th>
					${message("12034")}:
				</th>
				<td>
					<input type="text" name="order" class="text " maxlength="9" btn-fun="clear" />
				</td>
				<th>
					${message("外部编号")}:
				</th>
				<td>
					<input type="text" id="sn" name="sn" class="text " maxlength="200" btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>
					${message("12096")}:
				</th>
				<td colspan="7">
					<input type="hidden" name="isShowProduct" value="false"  />[#--是否显示产品--]
					<input type="hidden" name="isRepair" value="false"  />[#--维修预约--]
					<input type="hidden" name="isInstall" value="false"  />[#--安装预约--]
					<input type="hidden" name="isInstruction" value="false"  />[#--产品说明书--]
					<input type="hidden" name="isRecommended" value="false"  />[#--精品推荐--]
					<input type="hidden" name="_isTop" value="false" />
					<label><input type="checkbox" name="isEnabled" value="true" checked="checked"/>${message("12057")}</label>&nbsp;&nbsp;
					<input type="hidden" name="_isEnabled" value="false" />
				</td>
			</tr>
			<tr>
				<th>
					${message("12116")}:
				</th>
				<td colspan="7">
					<input type="hidden" name="image" value=""/>
				</td>
			</tr>
		</table>
		</div>
		
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton"  value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
		
	</form>
</body>
</html>