<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("产品词汇列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript">
	
	function getChildren(e){
		var status =$(e).find("a.blue").attr("data-status");
		var id = $(e).find("a.blue").attr("data-id");
		var rowIndex = $(e).index(); 
		if(isEmpty(status) && !isEmpty(id)){
			rowIndex = parseInt(rowIndex) + 1;
			ajaxSubmit($(id),{
				method:'post',
				url:'list_data.jhtml?parent='+id,
				callback: function(data) {
					if(data.type == 'success'){
						var rows = JSON.parse(data.content).content;
   						if(rows.length>0){
   						   for (var i = 0; i < rows.length;i++) {
   							   $mmGrid.addRow(rows[i],rowIndex);
   						   }
    					}
    					$("tr").removeClass("even");
    					$("tr:nth-child(2n)").addClass("even");
					}
				}
			})
			$(e).find("a.blue").attr("data-status","1");
		}else if(status==1){
			$(".mmGrid").find("a[data-parent="+id+"]").parent().parent().parent().hide();
			$(e).find("a.blue").attr("data-status","0");
			$("tr").removeClass("even");
			$("body").find("tr:visible:even").addClass("even");
		}else{
			$(".mmGrid").find("a[data-parent="+id+"]").parent().parent().parent().show();
			$(e).find("a.blue").attr("data-status","1");
			$("tr").removeClass("even");
			$("body").find("tr:visible:even").addClass("even");
		}
	}
	
	$().ready(function() {
		var cols = [
    		{ title:'${message("词汇名称")}', name:'value' ,align:'left',renderer:function(val,item,rowIndex){
    			if(!isEmpty(item.parent)){
    				return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/product/product_dict/view.jhtml?id='+item.id+'\')" class="red" style="margin-left:20px" data-parent="'+item.parent+'">'+val+'</a>';	
    			}else{
    				return '<a href="javascript:void(0)" class="blue" style="border-bottom: solid 1px #4489ce;" onClick="parent.change_tab(0,\'/product/product_dict/view.jhtml?id='+item.id+'\')" data-id="'+item.id+'" data-rowIndex ="'+rowIndex+'" data-status="">'+val+'</a>';
    			}
    		}},
    		{ title:'词汇编码', name:'code' ,align:'center',renderer:function(val,item,rowIndex){
    			if(isEmpty(item.parent)){
    				return val;	
    			}
    		}},
    		{ title:'${message("是否启用")}', name:'is_enabled' ,align:'center',renderer:function(val){
    			if(!isEmpty(val) && val) {
    				return '<span class="trueIcon">&nbsp;</span>';
    			}else{
    				return '<span class="falseIcon">&nbsp;</span>';
    			}
    		}},	
    		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },
    		{ title:'${message("备注")}', name:'memo' ,align:'center' }
    	];

    	$mmGrid = $('#table-m1').mmGrid({
    		autoLoad:true,
    		multiSelect:false,
            cols: cols,
            fullWidthRows:true,
			checkByClickTd:true,
            url: 'list_data.jhtml?isEmpty=0',
            params:function(){
            	return $("#listForm").serializeObject();
            },
    		plugins : [
                $('#paginator').mmPaginator()
            ],
            checkDisabled:function(item,rowIndex){
            	if(item.parent!=undefined){
            		return true;
            	}
            }
        });
    	
    	$(".real-row").live("click", function(){
    		var $this = $(this);
    		var $tr = $this.closest("tr");
    		getChildren($tr);
    	});
    	
	});
	
	
	
	function add(){
		parent.change_tab(0,'/product/product_dict/add.jhtml');
	}
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap"></div>
			<a href="javascript:add();" class="iconButton">${message("新增")}</a>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">
					${message("1004")}
				</a>
			</div>
			<div id="searchDiv">
				 <div id="search-content" >
				 	<dl>
	    				<dt ><p>${message("词汇名称")}:</p></dt>
		    			<dd >
		    				<input type="text" class="text" name="name" btn-fun="clear"/>
		    			</dd>
		    		</dl>
	        		<dl>
	    				<dt ><p>${message("词汇编码")}:</p></dt>
		    			<dd >
		    				<input type="text" class="text" name="code" btn-fun="clear"/>
		    			</dd>
		    		</dl>
		    		<dl>
						<dt><p>${message("是否启用")}:</p></dt>
						<dd>
						<select name="isEnabled" class="text">
							<option value>请选择</option>
							<option value=true>是</option>
							<option value=false>否</option>
						</select>
						</dd>
					</dl>
	        	</div>
			</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>