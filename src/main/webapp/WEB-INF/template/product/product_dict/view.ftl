<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑产品词汇")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
	$().ready(function() {
		var $inputForm = $("#inputForm");
		// 表单验证
		$inputForm.validate({
			rules: {
				code: "required",
				value: "required"
				},
			submitHandler:function(form){
				return false;
			}
		});
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看词汇")}
	</div>
	<form id="inputForm" action="saveOrUpdate.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${productDict.id}" />
		<input type="hidden" name="parentId" value="${productDict.parent.id}" />
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>${message("词汇类型")}:
					</th>
					<td>
						<input type="hidden" name="parentValue" value="${productDict.parent.value}" />
						<span>${productDict.parent.value}</span>
					</td>
					<th>
						<span class="requiredField">*</span>${message("词汇名")}:
					</th>
					<td>
						<input type="text" name="value" class="text" value="${productDict.value}" />
					</td>
					<th>${message("词汇编码")}:</th>
					<td>
						<input type="text" name="code" class="text" value="${productDict.code}" />
					</td>
					<th>${message("设置")}:</th>
					<td>
						<label>
							<input type="checkbox" name="isEnabled" value="true" [#if productDict.isEnabled]checked="checked"[/#if] />${message("是否启用")}
							<input type="hidden" name="isEnabled" value="false" />
						</label>
					</td>
				</tr>
				<tr>
					<th>${message("备注")}:</th>
					<td colspan="7">
						<textarea rows="3" cols="56" name="remark" class="text">${productDict.remark}</textarea>
					</td>
				</tr>
			</table>
		</div>
		<div class="fixed-top">
			<a href="add.jhtml" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>