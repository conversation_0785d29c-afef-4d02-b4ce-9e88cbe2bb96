<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("添加产品词汇")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
	$().ready(function() {
		var $inputForm = $("#inputForm");
		// 表单验证
		$inputForm.validate({
			rules: {
				code: "required",
				value: "required"
				},
			submitHandler:function(form){
				return false;
			}
		});
		//查询词汇
		$("#selectProductDict").bindQueryBtn({
			type:'systemDict',
			title:'${message("查询产品词汇")}',
			url:'/product/product_dict/select_product_dict.jhtml?isEmpty=0',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.parentId").val(row.id);
					$("input.parentValue").val(row.value);
					$("input.code").val(row.code);
				}
			}
		});
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= 'view.jhtml?id='+resultMsg.objx;
				});
			}
		 });
	});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增词汇")}
	</div>
	<form id="inputForm" action="saveOrUpdate.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("词汇类型(父)")}:
				</th>
				<td>
					<span class="search" style="position:relative">
						<input type="hidden" name="parentId" class="text parentId" btn-fun="clear" value=""/>
						<input type="text" name="parentValue" class="text parentValue" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectProductDict">
					</span>
				</td>
				
				<th>
					<span class="requiredField">*</span>${message("词汇名称")}:
				</th>
				<td>
					<input type="text" name="value" class="text" />
				</td>
				<th>
					<span class="requiredField">*</span>${message("词汇编码")}:
				</th>
				<td>
					<input type="text" name="code" class="text code" />
				</td>
				<th>${message("设置")}:</th>
				<td>
					<label>
						<input type="checkbox" name="isEnabled" value="true" checked="checked" />${message("是否启用")}
						<input type="hidden" name="isEnabled" value="false" />
					</label>
				</td>
			</tr>
			<tr>
				<th>${message("备注")}:</th>
				<td colspan="7">
					<textarea rows="3" cols="56" name="remark" class="text"></textarea>
				</td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>