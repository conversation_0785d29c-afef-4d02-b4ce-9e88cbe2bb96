<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("产品维护")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$().ready(function(e) {
 	var zTree = $("#js-menu").initTree({
		url:'/product/product/getNodes.jhtml',
		onClick:function(e,treeId, treeNode) {
			$("#productCategoryId").val(treeNode.id);
	        if(treeNode.id==""){
	      		$("#productCategoryName").val("");
	        }else{
	      		$("#productCategoryName").val(treeNode.name);
	        }
	        $("#searchBtn").click();
		}
	})
    zTree.addNodes(null,{'id':'','pId':'','name':'全部产品','isParent':false});
    treeScoll();
}); 
</script>
<script type="text/javascript">
function edit(id){
	parent.change_tab(0,'/product/product/edit/${code}.jhtml?id='+id+'&type=${type}');
}
$().ready(function() {
	//查询分类
	$("#selectProductCategory").bindQueryBtn({
		type:'productCategory',
		title:'${message("查询分类")}',
		url:'/product/product_category/select_productCategory.jhtml'
	});
			
	var cols = [
		[#--{ title:'${message("产品系统编号")}', name:'sn' ,width:150, align:'center', renderer: function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';		
		}},--]
	   { title:'${message("产品名称")}', name:'name' ,width:200, align:'center', renderer: function(val,item,rowIndex){
		return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';		
	    }},
		{ title:'${message("产品编码")}', name:'vonder_code' ,width:200, align:'center'},
// 		{ title:'${message("描述")}', name:'detail_description' ,width:200, align:'center'},
		{ title:'${message("产品型号")}', name:'model' ,width:100, align:'center'},
		{ title:'${message("产品品牌")}', name:'brand_name' ,width:100, align:'center'},
		{ title:'${message("产品分类")}', name:'product_category_name' ,width:100, align:'center' },
		{ title:'${message("二级分类")}', name:'product_two' ,width:100, align:'center' },
		{ title:'${message("三级分类")}', name:'product_three' ,width:100, align:'center' },
		{ title:'${message("是否上架")}', name:'is_marketable' ,width:80, align:'left', renderer: function(val){
			if(val=='1'){
				return '<span class="trueIcon">&nbsp;</span>';
			}else if(val=='0'){
				return '<span class="falseIcon">&nbsp;</span>';
			}
		}},
		{ title:'${message("创建日期")}', name:'create_date' ,width:120, align:'center'}
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/product/product/list_data.jhtml?type=${type}',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
			
			
	//导出  lu_anpei
	$("#export").click(function() {
		var sn = $("#sn").val();
		var startTime = $("#startTime").val();
		var endTime = $("#endTime").val();
		var name =  $("#name").val();
		var productCategoryId = $("#productCategoryId option:selected").val();
		/**
		if(createDate == "" && sn == "" &&  name == ""){
				$.message("warn","请完善搜索条件，避免给系统造成不必要的压力!");
				return false;
		}
		*/
		location.href = "/admin/product/export.jhtml?sn="+sn+"&startTime="+startTime+"&name="+name+"&productCategoryId="+productCategoryId+"&endTime="+endTime;
	});
					
});

function productImport(e){	
	excel_import(e,{
		title:"${message("产品导入")}",
		url:"/product/product/import_excel.jhtml",
		template:"/resources/template/product/product.xls",
		callback:function(){
			$("#searchBtn").click();
		}
	})
} 		

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = '/product/product/to_condition_export.jhtml';//分页导出统计页面
	var url = '/product/product/condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的产品！")}';//提示
	var url = '/product/product/selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}
		
function clear_text(e){
	var $this = $(e);
	var productCategoryId = $("#productCategoryId").val();
	if(productCategoryId!=''){
		$this.val('');
		$("#productCategoryId").val('');
	}
}
	
</script>
</head>
<body class="tree-contain">
	<div class="subnav">
	<input type="hidden" id="flag" value="0">
	<input type="hidden" id="storeId" value="">
  <div class="tit"><s></s>产品分类</div>
  <div class="subn-list ztree" id="js-menu">
  	
  </div>
</div>
<label id="labBtn"></label>
<div class="main-right">
	<form action="" id="exportForm" method="get"></form>
	<form id="listForm" action="/product/product/list/${code}.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="productImport(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
					</ul>
				</div>
				<a href="javascript:;" onclick="parent.change_tab(0,'/product/product/add/${code}.jhtml?type=${type}',1)" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
				</a>
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			[#--搜索begin--]
			<div id="searchDiv">
			        <div id="search-content" >
				    	<dl>
				    		<dt><p>${message("产品分类")}:</p></dt>
				    		<dd>
				    			<input type="hidden" id="productCategoryId" name="productCategoryId"  class="text" value="" />
								<input type="text"  class="text disabled" id="productCategoryName" value="" readonly />
							</dd>
				    	</dl>
				    	<dl>
							<dt><p>${message("产品系统编号")}:</p></dt>
							<dd>
								<input class="text" maxlength="200" type="text" name="sn" value=""  btn-fun="clear">
							</dd>
						</dl>
						<dl>
							<dt><p>${message("产品编码")}:</p></dt>
							<dd>
								<input class="text" maxlength="200" type="text" name="vonderCode" value=""  btn-fun="clear">
							</dd>
						</dl>
						<dl>
							<dt><p>${message("产品型号")}:</p></dt>
							<dd>
								<input class="text" maxlength="200" type="text" name="mod" value=""  btn-fun="clear">
							</dd>
						</dl>
						<dl>
							<dt><p>${message("产品名称")}:</p></dt>
							<dd>
								<input class="text" maxlength="200" type="text" name="name" value=""  btn-fun="clear">
							</dd>
						</dl>
						<dl>
							<dt><p>${message("是否上架")}:</p></dt>
							<dd>
							<select name="isMarketable" class="text">
								<option value>请选择</option>
								<option value=0>否</option>
								<option value=1 selected="selected">是</option>
							</select>
							</dd>
						</dl>
						<dl>
							<dt><p>${message("创建日期")}:</p></dt>
							<dd class="date-wrap">
								<input id="startTime" name="startTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
								<div class="fl">--</div>
								<input id="endTime" name="endTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							</dd>
						</dl>						
			        </div>
				</div>
				
			[#--搜索end--]
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</div>
</body>
</html>