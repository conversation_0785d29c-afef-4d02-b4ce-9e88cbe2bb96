<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("产品分类列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	$().ready(function() {
		var cols = [		
		{ title:'${message("12211")}', name:'vonder_code' ,align:'center',width:120},
		{ title:'${message("产品描述")}', name:'description', align:'center',width:200 },
		{ title:'${message("产品等级")}',name:'levelName',width:80,align:'center',renderer: function(val,item,rowIndex){
			 return val;
		}},
		{ title:'${message("产品名称")}', name:'name' ,align:'center'},		
		{ title:'${message("产品型号")}', name:'model' ,align:'center'},
		{ title:'${message("产品规格")}', name:'spec' ,align:'center'},		
		{ title:'${message("销售价")}',[#if hiddenAmount==0]hidden:true,[/#if] name:'price' ,width:90,align:'center', renderer: function(val){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		{ title:'${message("会员价")}',[#if hiddenAmount==0]hidden:true,[/#if] name:'member_price' ,align:'center', width:90,renderer: function(val){
			if(val!=undefined && val!=null && val!=''){
				return '<span class="red">'+currency(val,true)+'</span>';
			}
			else{
				return '-';
			}
		}},
		
	];
	
	[#if multi==2]
		var multiSelect = true;
	[#else]
		var multiSelect = false;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		rowCursorPointer:true,
		formQuery:true,
        cols: cols,
        url: 'selectProductData.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });


		//li点击事件 赋值
		$(".productList li").live("click",function(){
			//获取当前值
			var projectName = $(this).html();
			$("input[name='name']").val(projectName);
			$(".textDis").css("display","none");
		})
});
function childMethod(){
   return $mmGrid.selectedRows();
};

function updateProductList(t) {
	var name = $(t).val();
	$.ajax({
		url : '/product/product/selectProductData.jhtml',
		type : "post",
		data : {name:name},
		success : function(data) {
			var content = JSON.parse(data.content);
			if(content.total!=0){
				var num = 0;
				if (content.total>content.pageSize) {
					num = content.pageSize;
				}else {
					num = content.total;
				}
				var html = "";
				for(var i=0; i < num; i++){
					html += '<li>'+content.content[i].name+'</li>';
				}
				$(".productList").html("");
				$(".productList").html(html);
				//显示
				$(".textDis").css("display","block");
			}
		}
	})
}

</script>
	<style type="text/css">
		.textDis{
			position:absolute;
			z-index:6;
			top:100%;
			min-width: 180px;
			background: #fff;
			border:solid 1px #ddd;
			border-radius:5px;
			padding:5px 0px;
			box-shadow:3px 3px 4px #ddd;
			overflow-y:scroll;
			height: 300px;
			display: none;
			filter:progid:DXImageTransform.Microsoft.Shadow(Strength=4,Direction=135,Color='#000000');
		}
		.textDis li{
			padding:0px 10px;
			font-size: 13px;
			font-weight: initial;
		}
		.textDis li:hover{background-color: #ddd;}
	</style>
</head>
<body style="min-width:0px;">
	<form id="listForm" action="selectProductData.jhtml" method="get">
	<input type="hidden" name="warehouseId" value="${warehouseId}">
	<input type="hidden" name="storeId" value="${storeId}">
	<input type="hidden" name="isSuit" value="${isSuit}">
	<input type="hidden" name="isPart" value="${isPart}">
	<input type="hidden" name="sbuId" value="${sbuId}">
	<input name="isGift" value="${isGift}" type="hidden" />
	<input type="hidden" name="memberRankId" value="${memberRankId}">
	<input type="hidden" name="isToOrder" value="${isToOrder}">
	<input type="hidden" name="typeSystemDictId" value="${typeSystemDictId}">
	<input type="hidden" name="productGrade" value="${productGrade}">
	<input type="hidden" name="organizationId" value="${organizationId}">
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
		    	<dl>
		    		<dt><p>${message("产品名称")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="name" name="name" value ="" btn-fun="clear" oninput="updateProductList(this)"/>
						<div class="textDis">
							<ul class="productList">
							</ul>
						</div>
		    		</dd>
		    	</dl>
		        <dl>
			    	<dt><p>${message("产品型号")}：</p></dt>
	    			<dd>
	    				<input type="text" class="text" id="mod" name="mod" value ="" btn-fun="clear" />
			    	</dd>
			    </dl>
	        	<dl>
		    		<dt><p>${message("12211")}：</p></dt>
		    		<dd>
		    			<input type="text" class="text" id="vonderCode" name="vonderCode" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("产品规格")}：</p></dt>
		    		<dd>
		    			<input type="text" class="text" name="productSpec" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("产品描述")}：</p></dt>
		    		<dd>
		    			<input type="text" class="text"  name="productDescription" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
	        </div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
	</form>
</body>
</html>