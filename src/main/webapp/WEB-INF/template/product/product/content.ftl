<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title></title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.SuperSlide.2.1.1.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.imagezoom.min.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
[#assign defaultImage = '/resources/images/default-img.jpg']
[#if cartItem!=null]
	[#assign attachs = cartItem.cartItemAttachs]
[#elseif demandOrderItem!=null]
	[#assign attachs = demandOrderItem.demandOrderItemAttachs]
[#elseif orderItem!=null]
	[#assign attachs = orderItem.orderItemAttachs]
[/#if]
<script type="text/javascript">
$().ready(function(e) {
 	var zTree = $("#js-menu").initTree({
		url:'/product/product_attach_category/getNodes.jhtml?pcId=${product.productCategory.id}',
		onClick:function(e,treeId, treeNode) {
			$("#productCategoryId").val(treeNode.id);
	        if(treeNode.id==""){
	      		$("#productCategoryName").val("");
	        }else{
	      		$("#productCategoryName").val(treeNode.name);
	        }
	        
	        //----------------------------------
	        if(${attach_json}.length>0){
	        	var attachs=${attach_json};
	        	var html='';
	        	var src='';
	        	[#assign src = "/resources/images/"]
	        	for(var i=0;i<attachs.length;i++){
	        		var attach=attachs[i];
	        		var memo=(attach.memo==null?'':attach.memo);
	        		var suf=attach.suffix.toLowerCase();

	        		if(suf=='jpg'||suf=='png'||suf=='gif'||suf=='jpeg'||suf=='bmp'){
	        			src=attach.thumbnail;
	        		}else if(suf=='mp3'){
	        			src='${src}audio.png';
	        		}else if(suf=='xls'||suf=='xlsx'){
	        			src='${src}excel.png';
	        		}else if(suf=='exe'){
	        			src='${src}exe.png';
	        		}else if(suf=='fla'){
	        			src='${src}flash.png';
	        		}else if(suf=='html'){
	        			src='${src}html.png';
	        		}else if(suf=='mp4'){
	        			src='${src}mp4.png';
	        		}else if(suf=='pdf'){
	        			src='${src}pdf.png';
	        		}else if(suf=='ppt'){
	        			src='${src}ppt.png';
	        		}else if(suf=='txt'){
	        			src='${src}txt.png';
	        		}else if(suf=='doc'||suf=='docx'){
	        			src='${src}word.png';
	        		}else if(suf=='xml'){
	        			src='${src}xml.png';
	        		}else if(suf=='zip'){
	        			src='${src}zip.png';
	        		}else if(suf=='avi'||suf=='wmv'||suf=='rmvb'||suf=='3gp'){
	        			src='${src}video.png';
	        		}else{
	        			src='${src}currency.png';
	        		}
	        		if(treeNode.id==''){
	        			if(attach.product_attach_category==null){
	        				html+='<li><div class="pic" onclick="addAttachDownloadTimes('+attach.id+',&quot;'+attach.url+'&quot;,&quot;'+attach.suffix+'&quot;,&quot;'+attach.file_name+'&quot;)"><img src="'+src+'" ></div>'+
	        				'<div class="ico-down" onclick="allDownload('+attach.id+',&quot;'+attach.url+'&quot;,&quot;'+attach.file_name+'&quot;)"><span></span>'+attach.download_times+'</div><div class="t1" title="'+attach.file_name+'">'+attach.file_name+'</div><div class="t2" title="'+memo+'">'+memo+'</div></li>';
	        			}
	        		}else if(treeNode.id==0||attach.product_attach_category==treeNode.id){
	        				html+='<li><div class="pic" onclick="addAttachDownloadTimes('+attach.id+',&quot;'+attach.url+'&quot;,&quot;'+attach.suffix+'&quot;,&quot;'+attach.file_name+'&quot;)"><img src="'+src+'" ></div>'+
	        				'<div class="ico-down" onclick="allDownload('+attach.id+',&quot;'+attach.url+'&quot;,&quot;'+attach.file_name+'&quot;)"><span></span>'+attach.download_times+'</div><div class="t1" title="'+attach.file_name+'">'+attach.file_name+'</div><div class="t2" title="'+memo+'">'+memo+'</div></li>';
	        		}
	        	}
	        	$("#attach_ul").html(html);
	        }
// 	        ajaxSubmit($(e),{
// 				method:'get',
// 				url:"get_product_attachs.jhtml",
// 				data: {categoryId: treeNode.id, productId: ${product.id}},
// 				callback: function(data) {
// 					var data = $.parseJSON(resultMsg.content);
// 					var item=data.objx;
// 					if (item.longitude!=null && item.latitude!=null){
// 						setMarker(item, treeNode);
// 						showRangeFences(item.latitude, item.longitude);
// 					}
// 				}
// 			});
	        //---------------------------------
	        
	        $("#searchBtn").click();
		}
	})
	zTree.addNodes(null,{'id':'0','pId':'','name':'全部分类','isParent':false});
    zTree.addNodes(null,{'id':'','pId':'','name':'其他分类','isParent':false});
    treeScoll({minClickX:620,maxClickX:1000});
	$("#js-menu_1_a").click();
}); 
</script>
<script>
var defaultImage = '${defaultImage}';
$().ready(function() {
	//alert($(".pro-containt").offset().top - $("body").scrollTop());
	$(".pro-containt").css("min-height",$(window).height()-45);
	
	$("#toTop").click(function() {
	      $("html,body").animate({scrollTop:0}, 500);
	  }); 

	var $parameter_li = $(".parameter_li");
	$parameter_li.parent().removeClass("hidden");
	
	$(".productAttach_li").parent().prev().removeClass("hidden");
	
var $addAttach = $("#addAttach");
var attachIdnex = ${(attachs?size)!0};
var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
	        	var file_info = data[i].file_info;
	        	var image = setting.file_image;
	        	[@compress single_line = true]
	    		var tHtml='<div class="ul-box">
                        <div class="pic"><img src="'+image+'"><\/div>
                        <b title="'+file_info.name+'">'+abbreviate(file_info.name,26)+'</b>
                        <p>'+time+'</p>
                        <a class="del deleteAttachment" href="javascript:void(0);"><\/a>
                         <input type="hidden" name="cartItemAttachs['+attachIdnex+'].url" value="'+file_info.url+'">
			            <input type="hidden" name="cartItemAttachs['+attachIdnex+'].memo" value="'+file_info.name+'">
                    <\/div>';
	    		[/@compress]                   
	            $addAttach.before(tHtml);
	            attachIdnex++;
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
     var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("div").remove();
	});
	
	/**带出标配的下级选配项*/
	initStand();
	
 var sHeight = $(".pro-pic ul li").outerHeight(true);
 var len = $(".pro-pic ul li").length;
 var index = 0;
 /**
 $(".prev").click(function(){
 	if(index == 0){index = len;}
 	index --;
 	showPics(index);
 })
 $(".next").click(function(){
 	if(index == len-1){index = -1;}
 	index ++;
 	showPics(index);
 })*/
 
 function showPics(index){
 	var nowIndex = index-len+3;
	var nowTop = -nowIndex*sHeight;
	$(".pro-pic ul li").animate({"marginTop":nowTop},300);
}

// 商品缩略图滚动
	$("#scrollable").scrollable({vertical:true});
	
	
});

function checkParts(e){
	var $this = $(e);
	var value = $this.val();
	var $dd = $this.closest("dd");
	var standardNum = $dd.find("label.standard").length;
	var $input = $this.parent().find("input:checkbox");
	if(standardNum>0){
		$this.closest("dd").find("input").prop("checked",false);
		$dd.find(".check_box").removeClass("checked");
		$this.next().addClass("checked");
		$this.prop("checked",true);
		
		var $div = $("div[super_parts='"+value+"']");
		//$div.find("input").prop("disabled",false);
		$div.show();
		
		var $siblingsDiv = $this.closest("div.t-check").siblings();
		$siblingsDiv.each(function(){
			var sValue = $(this).find("input").val();
			var $sDiv = $("div[super_parts='"+sValue+"']");
			$sDiv.find("input").prop("checked",false);
			$sDiv.hide();
			$sDiv.find("div.d-check").removeClass("checked");
		})
		
	}else{
		if(!$this.prop("checked")){
			$this.closest("dd").find("input").prop("checked",false);
			$dd.find(".check_box").removeClass("checked");
			$this.prop("checked",false);
			
			var $div = $("div[super_parts='"+value+"']");
			$div.find("input").prop("checked",false);
			//$div.removeClass("checked").hide();
			$div.hide();
			$div.find("div.d-check").removeClass("checked");
		}else{
			$this.closest("dd").find("input").prop("checked",false);
			$dd.find(".check_box").removeClass("checked");
			$this.next().addClass("checked");
			$this.prop("checked",true);
			
			var $div = $("div[super_parts='"+value+"']");
			$div.show();
			
			var $siblingsDiv = $this.closest("div.t-check").siblings();
			$siblingsDiv.each(function(){
				var sValue = $(this).find("input").val();
				var $sDiv = $("div[super_parts='"+sValue+"']");
				$sDiv.find("input").prop("checked",false);
				$sDiv.hide();
				$sDiv.find("div.d-check").removeClass("checked");
			})
		}
	}
	
}

/**带出标配的下级选配项*/
function initStand(){
	$("div.d-check.checked").each(function(){
		var $tDiv = $(this).closest(".t-check");
		var value = $tDiv.find("input").val();
		
		var $div = $("div[super_parts='"+value+"']");
		$div.show();
	
	});
}

function sub(e){
	var $form = $("#dataform");
	ajaxSubmit(e,{
		url:$form.attr("action"),
		method:$form.attr("method"),
		data:$form.serialize(),
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,"保存选配产品、并加入购物车成功！",1000,function(){
				top.open_new_tab(e);
			});
		}
	});
}

function addCart(e){
	var storeId = "${storeId}";
	var id = "${product.id}";
	ajaxSubmit(e,{
		url:"${base}/b2b/cart/add.jhtml",
		method:"post",
		data: {productId: id,quantity:1,storeId:storeId},
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,"添加成功",1000,function(){
				//top.open_new_tab(e);
			});
		}
	});
}


function show_ico(e){
	var $i = $(e).find("i.show-ico");
	if($i.hasClass("down")){
		$i.attr("class", "show-ico up");
	}else{
		$i.attr("class", "show-ico down");
	}
}
var line_index = 1;
var types = {1:'${message("虚拟件")}',2:'${message("替代件")}'};
function getChildren(e,id){
	var line_num = $(e).closest("tr").attr("line_index");
	
	var each_index = 0;
	if($("tr."+line_num).length >0){
		var hi_sh = $("tr."+line_num).eq(0).is(":hidden");
		$("tr."+line_num).each(
			function(){
				if(hi_sh){
					$(this).show();
				}else{
					$(this).hide();
				}
				each_index++;
			}
		);
		show_ico(e);
	}
	
	if($("tr."+line_num).length != 0 && $("tr."+line_num).length == each_index){
		return;
	}
	var cla = "";
	var this_class = $(e).closest("tr").attr("class");
	if(this_class != undefined){
		cla = this_class + " "+line_num;
	}else{
		cla = line_num;
	}
	
	var this_class = $(e).parent().attr("class");
	var num = Number($(e).parent().attr("num"))+1;
	var grade = Number($(e).parent().attr("grade"))+1;
	$.ajax({
		url: "../bom/getChildren.jhtml",
		type: "get",
		data: {parentId: id},
		dataType: "json",
		cache: false,
		success: function(data) {
			var proc = data.objx;
			for(var i=0;i<proc.length;i++){
				var p = proc[i];
				var str1 = '';
				var str2 ='';
				var isBom = ''
				var isDown = '';
				if(!p.is_leaf){
					str2 = "是";
					isDown = 'down';
				}else{
					str2 = "否";
				}
				var type = types[p.type];
				if(type==undefined)type='';
				[@compress single_line = true]
					var trHtml = 
						'<tr class="'+cla+'" id="'+p.id+'" num="'+num+'" line_index="'+line_index+'" _bom="'+p.bom_id+'" >
							<td  align="left" onclick="getChildren(this,'+p.id+')" style="cursor:pointer;text-align: left;">
								<div style="padding-left: '+num * 20 +'px;">
								<i class="show-ico '+isDown+'">&nbsp;</i>
									'+p.name+'
								</div>
							</td>
							<td>
								'+p.model+'
							</td>
							<td>
								'+p.vonder_code+'
							</td>
							<td>
								'+p.product_category_name+'
							</td>
							<td>
								'+type+'
							</td>
							<td>
								'+p.quantity+'
							</td>
							<td>
								'+str2+'
							</td>
						</tr>';
				[/@compress]
				$(e).parent().after(trHtml);
				line_index++;
			}
			if(proc.length>0)show_ico(e);
		}
	});
}

var attachFlag = 0;
/**初始化附件*/
function initAttach(){
	if(attachFlag==1)return false;
    var attach_items = ${attach_json};
    var attachIdnex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,align:'center',renderer:function(val,item,rowIndex,obj){
			if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name , item.name, item.suffix);
				
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['productAttachs['+attachIdnex+'].id']=item.id;
				hideValues['productAttachs['+attachIdnex+'].url']=url;
				hideValues['productAttachs['+attachIdnex+'].suffix']=fileObj.suffix;
				if(item.store_member!=null){
					hideValues['productAttachs['+attachIdnex+'].storeMember.id']=item.store_member;
				}
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'productAttachs['+attachIdnex+'].name',
					hideValues: hideValues
				});
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['productAttachs['+attachIdnex+'].url']=url;
				hideValues['productAttachs['+attachIdnex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'productAttachs['+attachIdnex+'].name',
					hideValues:hideValues
				});
				
			}
			
        
    	}},
		{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex){
			attachIdnex++;
			return val;
		}}
	];
	$amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:attach_items,
        checkCol: false,
        autoLoad: true,
        callback:function(){
        	$("textarea.t-edit").prop("disabled",true);
        }
    });
    attachFlag = 1;
    
}
function slectProductAttach(e){
	cTab(e);
	initAttach();
}

function addAttachDownloadTimes(id,url,suffix,name){
	ajaxSubmit('',{
		url: "addAttachDownloadTimes.jhtml",
		method: "POST",
		data: {id: id},
		callback:function(){
			var suffix_str=suffix.toLowerCase();
			if(suffix_str!="png" && suffix_str!="jpg" && suffix_str!="jpeg"
					&& suffix_str!="gif" && suffix_str!="bmp"){
				downloadFile(url,name);
			}else{
				//----------------------------------------------------------弹框预览图片-----------------------------------------------------------------
// 				var h =$(window).height();
// 				var w =$(window).width();
// 				var $dialog = $.dialog({
// 					width:w,
// 					height:h-30,
// 					content: "<div style='width:"+w+"px;height:"+(h-80)+"px;text-align:center;display: table-cell;vertical-align: middle '><img id='photo_view' src='"+url+"' style='max-width:100%;max-height:"+(h-80)+"px;overflow:hidden;margin:auto;' ></div>",
// 					ok:'下载('+name+')',
// 					onShow:function(){
// 						$(".xxDialog").css("max-height",h-30).css("top","10px").css("background-color","rgba(255,255,255,0.3)");
// 						$(document).click(function(){
// 						    $(".xxDialog").remove();
// 						    $(".dialogOverlay").remove();

// 						});
// 						$("#photo_view").click(function(event){
// 						    event.stopPropagation();
// 						});
// 						$("input.button").click(function(event){
// 						    event.stopPropagation();
// 						});
// 					},
// 					onOk:function(){
// 						downloadFile(url,name);
// 					}
// 				});
				//------------------------------------------------------------end---------------------------------------------------------------------
				window.open(url);
			}
		}
	})
}

function allDownload(id,url,name){
	ajaxSubmit('',{
		url: "addAttachDownloadTimes.jhtml",
		method: "POST",
		data: {id: id},
		callback:function(){
			downloadFile(url,name);
		}
	})
}
</script>

<!--                         左右拖拉   需特殊方法                                                           -->
<script type="text/javascript">
function treeScoll(options){
		var clickX, leftOffset, inx, nextW2, nextW;
		var dragging  = false;
		var doc 	  = document;
		var labBtn 	  = $("#labBtn");
		var wrapWidth = $("body").width();
		
		var begin_height = 0;
		var end_height = 0;
		var height__spec = 0;
	
		labBtn.bind('mousedown',function(){
				dragging   = true;
				leftOffset = 0;
				inx 	   = $(this).index('label');
				begin_height = $("#search-content").height();
				$("body").addClass("resize-ing");
			}
		);
		
		var settings = {
			minClickX:130,
			maxClickX:700,
		}
		
		$.extend(settings, options);
	
		doc.onmousemove = function(e){
			if (dragging) {
				clickX = e.pageX;
				//第一个拖动按钮左边不出界
				if(clickX > leftOffset) {
					if(clickX<settings.minClickX || clickX>settings.maxClickX){
						return false;
					}
					
					var prev = labBtn.prev();
					var left = prev.offset().left;
					var next = labBtn.next();
					
					var w = prev.width();
					var prev_w = clickX-left;
					var spec = prev_w-w;
					
					labBtn.prev().width( prev_w + 'px');
					labBtn.css('left', prev.width()+1 + 'px');//按钮移动
					next.css('margin-left',  prev.width()+11 + 'px');
					
// 					labBtn.prev().width( prev_w + 'px');
// 					labBtn.css('left', next.offset().left-20 + 'px');//按钮移动
// 					next.width(next.width()-spec+'px');
					
					end_height = $("#search-content").height();
				    height__spec = begin_height-end_height;
				    begin_height = end_height;
				    if(height__spec!=0){
				    	changeGridHeight(height__spec);
				    }
				}
			}
		};
	
		$(doc).mouseup(function(e) {
		    dragging = false;
		    e.cancelBubble = true;
		    $("body").removeClass("resize-ing");
		})
		
		function changeGridHeight(h_spec){
			var $bodyWrapper = $(".mmg-bodyWrapper");
			var $responsive =  $(".table-responsive");
			var $mmGrid = $(".mmGrid");
			
			$bodyWrapper.height($bodyWrapper.height()+h_spec);
			$responsive.height($responsive.height()+h_spec);
			$mmGrid.height($mmGrid.height()+h_spec);
			
		
		}
	
	}
</script>
</head>
<body class="tree-contain">
[#assign productCategory = product.productCategory /]

<!-- <div style="width:40px;height:40px;background:url(/resources/images/toTop.png) no-repeat center; filter:alpha(opacity=20);opacity:0.2;position: absolute;left: 5px;bottom: 60px;cursor:pointer" id="toTop" title="回到顶部"></div> -->

<div class="tab-top">
	<a href="#" >
		<i class="tab-ico"></i>
		<em class="tab-text">顶部</em>
	</a>
</div>


<!-- contant S-->
  <div class="pro-mleft">
  <div class="pathh">产品详情</div>
  <!-- product pic s-->
  <div class="pro-pic">
    <ul class="tb-thumb" id="thumblist">
      <div class="tb-booth tb-pic tb-s310">
      	[#if product.productImages?size>0]
         [#assign medium = product.productImages[0].medium]
         [#assign large = product.productImages[0].large]
        [/#if]
       <a href="javascript:void(0);"> <img class="jqzoom" src="${large}" rel="[#if large!=null]${large}[#else]${defaultImage}[/#if]" onerror="this.src='${defaultImage}'"/> </a> </div>
      <div style="width: 93px;height: 381.8px;left: 394px;position: absolute;"> <a href="javascript:;" class="prev"></a>
        <div id="scrollable" class="scrollable" style="overflow: hidden;top: 21px;bottom: 50px;height: 235px;position: relative;">
          <div class="item" style="position: absolute;">
          	[#list product.productImages as productImage]
				<li [#if productImage_index==0]class="tb-selected"[/#if]>
                	<div class="tb-pic tb-s40">
						<a href="javascript:void(0);"  style="display: block;">
							<img src="${productImage.thumbnail}" mid="${productImage.medium}" big="${productImage.large}" onerror="this.src='${defaultImage}'">
						</a>
					</div>
            	</li>
			[/#list]
           </div>
        </div>
        <a href="javascript:;" class="next"></a> </div>
    </ul>
  </div>
  <!-- product pic E--> 
  <div class="pro-title">${product.fullName}</div>
  <div class="pro-t2">${product.seoTitle}</div>
  <div class="pro-para">
  	<dl>
      <dt>${message("12211")}：</dt>
      <dd>${product.vonderCode}</dd>
    </dl>
    <dl>
      <dt>产品型号：</dt>
      <dd>${product.model}</dd>
    </dl>
    <dl>
      <dt>产品分类：</dt>
      <dd>${product.productCategory.name}</dd>
    </dl>
    <dl>
      <dt>销售价：</dt>
      <dd><b class="red">[#if hiddenAmount==0]***[#else]${currency(product.price,true)}[/#if]</b></dd>
    </dl>
    [#if demandOrderItem??]
	    <dl>
	      <dt>新产品编码：</dt>
	      <dd>${demandOrderItem.newSn}</dd>
	    </dl>
	    <dl>
	      <dt>新产品名称：</dt>
	      <dd>${demandOrderItem.newName}</dd>
	    </dl>
    [/#if]
    [#if orderItem??]
	    <dl>
	      <dt>新产品编码：</dt>
	      <dd>${orderItem.sn}</dd>
	    </dl>
	    <dl>
	      <dt>新产品名称：</dt>
	      <dd>${orderItem.vonderCode}</dd>
	    </dl>
    [/#if]
  </div>
    <dvi class="buttonWrap" style="clear: both;width: 100%;padding-top: 10px;">
  	[#if flag!=0 && (flag==1 || cartItem!=null)]
		  <a href="javascript:void(0);" onclick="addCart(this)" class="fr cart-box" data-id="cart_list" data-src="/b2b/cart/list.jhtml" data-name="购物车" reflush='true'><i class="ico-cart"></i>加入购物车</a>
	[/#if]
  </dvi>
</div>

</div>
	 <div class="pro-mright">
	<div class="pro-nav">
        <p onclick="cTab(this)" data-id="detail" [#if !product.isParts] class="cur"[/#if]>产品详情</p>
        <p onclick="cTab(this)" data-id="parameter">产品参数</p>
        <p onclick="cTab(this)" data-id="productAttach">产品资料</p>
	</div>
    <div class="pro-containt" style="height: 100%;min-height: 700px;">
    	<div id="c-detail" class="c-detail" [#if !product.isParts] style="display:block;"[/#if]>
        <div class="productDetailDivImg"> 
            ${product.introduction}
            <!-- <img src="http://mk.5mall.com/upload/image/twkj/2017/08/21/771bee39-8db4-4e25-a32b-729d1f76fc44-medium.jpg" /> -->
        </div>
        <div class="no_content" style="height:300px;"></div>
      </div>
      <div class="c-parameter" id="c-parameter">
       <div class="specboxT">参数规格</div>
       [#list productCategory.parameterGroups as parameterGroup]
       [#if parameterGroup.parameters?size>0]
        <ul class="specbox hidden">
          <div class="tit">${parameterGroup.name}</div>
          [#list parameterGroup.parameters as parameter]
			[#list product.productParamValues as productParamValue]
				[#if productParamValue.value??]
				[#if productParamValue.parameter == parameter]
          		<li class="parameter_li"><span title="${productParamValue.value}">${parameter.name}</span>${productParamValue.value}</li>
          		[/#if]
				[/#if]
          	[/#list]
          [/#list]
        </ul>
       [/#if]
       [/#list]
       <div class="no_content" style="height:300px;"></div>
      </div>
      
      <div class="c-parameter" id="c-productAttach" >
	<input type="hidden" id="flag" value="0">
	<div class="subnav">
	<div class="tit"><s class="attach_img"></s>附件分类</div>
  <div class="pro-tree ztree" id="js-menu">
  	<li id="js-menu_1" class="level0" tabindex="0" hidefocus="true" treenode=""><span id="js-menu_1_switch" title="" class="button level0 switch noline_docu" treenode_switch="" disabled="disabled"></span><a id="js-menu_1_a" class="level0" treenode_a="" onclick="" target="_blank" style="" title="全部产品"><span id="js-menu_1_ico" title="" treenode_ico="" class="button ico_docu" style="width:0px;height:0px;"></span><span id="js-menu_1_span">全部产品</span></a></li>
  </div>
  
  </div>
<label id="labBtn" style="left:201px"></label>

<div class="pro-cont">
	<div class="clearfix"></div>

                <ul class="pro-c-list" id="attach_ul">
                [#list product.productAttachs as productAttach]
                	[#assign suf = productAttach.suffix?lower_case]
          			[#assign src = "/resources/images/"]
                    <li>
                        <div class="pic" onclick="addAttachDownloadTimes(${productAttach.id},&quot;${productAttach.url}&quot;,&quot;${productAttach.suffix}&quot;,&quot;${productAttach.fileName}&quot;)"><img  src="[#if suf=='jpg'||suf=='png'||suf=='gif'||suf=='jpeg'||suf=='bmp']${productAttach.thumbnail}[#elseif suf=='mp3']${src}audio.png[#elseif suf=='xls'||suf=='xlsx']${src}excel.png[#elseif suf=='exe']${src}exe.png[#elseif suf=='fla']${src}flash.png[#elseif suf=='html']${src}html.png[#elseif suf=='mp4']${src}mp4.png[#elseif suf=='pdf']${src}pdf.png[#elseif suf=='ppt']${src}ppt.png[#elseif suf=='txt']${src}txt.png[#elseif suf=='doc'||suf=='docx']${src}word.png[#elseif suf=='xml']${src}xml.png[#elseif suf=='zip']${src}zip.png[#elseif suf=='avi'||suf=='wmv'||suf=='rmvb'||suf=='3gp']${src}video.png[#else]${src}currency.png[/#if]" /></div>
          				<div class="ico-down" onclick="allDownload(${productAttach.id},&quot;${productAttach.url}&quot;,&quot;${productAttach.fileName}&quot;)"><span>download</span>${productAttach.downloadTimes}</div>
          				<div class="t1" title="${productAttach.fileName}">${productAttach.fileName}</div>
          				<div class="t2" title="${productAttach.memo}">${productAttach.memo}</div>
                    </li>
               	[/#list]
                </ul>
        </div>

	 <div class="no_content" style="height:300px;clear:both"></div>



     <!-- [#--  [#list ProductAttachCategorys as ProductAttachCategory]
      [#if ProductAttachCategory.id??]
      	<div class="specboxT">${ProductAttachCategory.name}</div>
        <ul class="pro-c-list">
        [#list product.productAttachs as productAttach]
        [#if productAttach.productAttachCategory??&& ProductAttachCategory.id==productAttach.productAttachCategory.id]
          [#assign suf = productAttach.suffix?lower_case]
          [#assign src = "/resources/images/"]
          <li style="background:#fafafa;">
          	<div class="pic"><a href="${productAttach.url}" target="_blank"><img src="/resources/images/icon_xls.png"></a><img src="[#if suf=='jpg'||suf=='png'||suf=='gif'||suf=='jpeg'||suf=='bmp']${productAttach.url}[#elseif suf=='mp3']${src}audio.png[#elseif suf=='xls']${src}excel.png[#elseif suf=='exe']${src}exe.png[#elseif suf=='fla']${src}flash.png[#elseif suf=='html']${src}html.png[#elseif suf=='mp4']${src}mp4.png[#elseif suf=='pdf']${src}pdf.png[#elseif suf=='ppt']${src}ppt.png[#elseif suf=='txt']${src}txt.png[#elseif suf=='doc']${src}word.png[#elseif suf=='xml']${src}xml.png[#elseif suf=='zip']${src}zip.png[#elseif suf=='avi'||suf=='wmv'||suf=='rmvb'||suf=='3gp']${src}video.png[#else]${src}currency.png[/#if]" /></a></div>
          	<div class="t1" title="${productAttach.fileName}">${productAttach.fileName}</div>
          	<div class="t2" title="memo">${productAttach.memo}</div>
          </li>
         [/#if]
         [/#list]
        </ul>
        [/#if]
      [/#list]
      
      <div class="specboxT hidden">&nbsp;</div>
        <ul class="pro-c-list">
        [#list product.productAttachs as productAttach]
        [#if productAttach.productAttachCategory??]
        
        	
        	
        	
        [#else]
          [#assign suf = productAttach.suffix?lower_case]
          [#assign src = "/resources/images/"]
          <li class="productAttach_li">
          	<div class="pic"><a href="${productAttach.url}" target="_blank" ><img  src="[#if suf=='jpg'||suf=='png'||suf=='gif'||suf=='jpeg'||suf=='bmp']${productAttach.url}[#elseif suf=='mp3']${src}audio.png[#elseif suf=='xls']${src}excel.png[#elseif suf=='exe']${src}exe.png[#elseif suf=='fla']${src}flash.png[#elseif suf=='html']${src}html.png[#elseif suf=='mp4']${src}mp4.png[#elseif suf=='pdf']${src}pdf.png[#elseif suf=='ppt']${src}ppt.png[#elseif suf=='txt']${src}txt.png[#elseif suf=='doc']${src}word.png[#elseif suf=='xml']${src}xml.png[#elseif suf=='zip']${src}zip.png[#elseif suf=='avi'||suf=='wmv'||suf=='rmvb'||suf=='3gp']${src}video.png[#else]${src}currency.png[/#if]" /></a></div>
          	<div class="t1" title="${productAttach.fileName}">${productAttach.fileName}</div>
          	<div class="t2" title="memo">${productAttach.memo}</div>
          </li>
         [/#if]
         [/#list]
        </ul> --] -->
      </div>
    </div>
    
</div>
</body>
<style>
.pro-cont{
position: relative;
overflow-y: initial;
padding: 0;
margin:10px 0 0 0;
margin-left:210px;
}

/* 绝对定位 当前div出滚动条 */
/* .pro-cont{ */
/* position: absolute; */
/* right: 0; */
/* top: 0; */
/* bottom: 0; */
/* overflow-y: hidden; */
/* left: 214px; */
/* padding: 0; */
/* margin:10px 0 0 0; */
/* } */
 .cart-box:hover { background: #2a6494; color: #FFF;}
 .cart-box .ico-cart { display: inline-block; width: 20px; height: 20px; vertical-align: sub; background: url(../../../resources/images/button/ico-cart0.png) no-repeat center; margin-right: 6px;}
 .cart-box { border: 2px solid #2a6494;  height: 36px;  width: 128px; margin-top: 0px; margin-left: 15px; line-height: 36px; text-align: center; font-size: 15px; background: #fefefe;margin-right: 325px;color: #2a6494;}
 .cart-box b { color: #ff6600;}
</style>
<script>
function cTab(e){
	$(e).addClass("cur").siblings().removeClass("cur");
	var vid=$(e).attr("data-id");
	if(vid =="detail"){	
		$("#c-detail").show();
		$("#c-parameter").hide();
		$("#c-setting").hide();
		$("#c-setting").hide();
		$("#c-productSuite").hide();
		$("#c-productAttach").hide();
	}else if(vid =="parameter"){
		$("#c-detail").hide();
		$("#c-parameter").show();
		$("#c-setting").hide();
		$("#c-productSuite").hide();
		$("#c-productAttach").hide();
	}else if(vid =="productSuite"){
		$("#c-detail").hide();
		$("#c-parameter").hide();
		$("#c-setting").hide();
		$("#c-productSuite").show();
		$("#c-productAttach").hide();
	}else if(vid =="productAttach"){
		$("#c-detail").hide();
		$("#c-parameter").hide();
		$("#c-setting").hide();
		$("#c-productSuite").hide();
		$("#c-productAttach").show();
	}else{
		$("#c-detail").hide();
		$("#c-parameter").hide();
		$("#c-setting").show();
		$("#c-productSuite").hide();
		$("#c-productAttach").hide();
	}
}
$().ready(function(e) {
	$(".jqzoom").imagezoom();
	$("#thumblist li a").click(function(){
		$(this).parents("li").addClass("tb-selected").siblings().removeClass("tb-selected");
		$(".jqzoom").attr('src',$(this).find("img").attr("mid"));
		$(".jqzoom").attr('rel',$(this).find("img").attr("big"));
	});
	$(".option-item .txt input").click(function(){
		$(this).parent().parent().find(".checkbox").attr("disabled","true");
	})
	$(".option-item .txt input").bind("input",function(){
		//alert($(this).val());
	})
});

</script>
</html>