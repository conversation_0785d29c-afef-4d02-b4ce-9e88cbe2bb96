<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("产品列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	$().ready(function() {
		var cols = [
			[#if addType == 0]
				{ title:'${message("产品名称")}', name:'name' ,align:'center'},
				{ title:'${message("产品描述")}', name:'description', align:'center',width:160 },
				{ title:'${message("12211")}', name:'vonder_code' ,align:'center'},
				{ title:'${message("产品规格")}', name:'spec' ,align:'center'},
			[/#if]   
			{ title:'${message("产品型号")}', name:'model' ,align:'center'},
			[#if addType == 0]
				{ title:'${message("产品等级")}', name:'levelName' ,align:'center'},
	    	[#elseif addType == 1] 
				{ title:'${message("木种/花色")}', name:'wood_type_or_color', align:'center'},
	    	[/#if]
		];
		[#if multi==2]
			var multiSelect = true;
		[#else]
			var multiSelect = false;
		[/#if]
		$mmGrid = $('#table-m1').mmGrid({
			multiSelect:multiSelect,
			autoLoad: true,
			fullWidthRows:true,
			checkByClickTd:true,
			rowCursorPointer:true,
			cols: cols,
			url: 'selectProductList.jhtml',
			params:function(){
				return $("#listForm").serializeObject();
			},
			plugins : [
				$('#paginator').mmPaginator()
			]
		});

        //回车事件
        $("input").keydown(function(e){
            if((e.keyCode || e.which)==13) {
                // 触发需要调用的方法
                $("#searchBtn").click();
            }
        });
        $("select").keydown(function(e){
            if((e.keyCode || e.which)==13) {
                // 触发需要调用的方法
                $("#searchBtn").click();
            }
        });

	});
	function childMethod(){
	   return $mmGrid.selectedRows();
	}; 
</script>
</head>
<body style="min-width:0px;">
	<form id="listForm" action="selectProductList.jhtml" method="get">
		<input type="hidden" name="sbuId" value="${sbuId}">
		<input type="hidden" name="productOrganizationId" value="${productOrganizationId}">
		<input type="hidden" name="addType" value="${addType}">
		<div class="bar">
			<div class="buttonWrap">
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
		        	 [#if addType == 0]
		        	 	<dl>
				    		<dt><p>${message("产品名称")}：</p></dt>
				    		<dd>
								<input type="text" class="text" id="name" name="name" value ="" btn-fun="clear" />
				    		</dd>
				    	</dl>
		        	 [/#if] 
			    	 <dl>
				    	<dt><p>${message("产品型号")}：</p></dt>
		    			<dd>
		    				<input type="text" class="text" id="mod" name="mod" value ="" btn-fun="clear" />
				    	</dd>
				    </dl>
			    	[#if addType == 0]
		        		<dl>
				    		<dt><p>${message("12211")}：</p></dt>
				    		<dd>
				    			<input type="text" class="text" id="vonderCode" name="vonderCode" value ="" btn-fun="clear" />
				    		</dd>
				    	</dl>
				    	<dl>
				    		<dt><p>${message("产品规格")}：</p></dt>
				    		<dd>
				    			<input type="text" class="text" name="productSpec" value ="" btn-fun="clear" />
				    		</dd>
				    	</dl>
				    	<dl>
				    		<dt><p>${message("产品描述")}：</p></dt>
				    		<dd>
				    			<input type="text" class="text"  name="productDescription" value ="" btn-fun="clear" />
				    		</dd>
				    	</dl>
		        	[#elseif addType == 1] 
		        	 	<dl>
				    		<dt><p>${message("木种/花色")}：</p></dt>
				    		<dd>
				    			<input type="text" class="text"  name="woodTypeOrColor" value ="" btn-fun="clear" />
				    		</dd>
				    	</dl>
		        	[/#if]
		        </div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>