<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("sbu")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
function add(){
	parent.change_tab(0,'/basic/sbu/add.jhtml');
}
$().ready(function() {

	var cols = [
		{ title:'${message("名称")}', name:'name' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/basic/sbu/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		}},
//		{ title:'${message("默认")}', name:'is_default' ,align:'center',renderer:function(val,item){
//			if(val=='1'){
//				return '<span>是</span>';
//			}else if(val=='0'){
//				return '<span>否</span>';
//			}
//
//		}},
		{ title:'${message("状态")}', name:'status' ,align:'center',renderer:function(val,item){
			if(val=='1'){
				return '<span class="green">生效</span>';
			}else if(val=='0'){
				return '<span class="red">失效</span>';
			}
		} },
		{ title:'${message("编码前缀")}', name:'code_pre',align:'center'},
		{ title:'${message("URL标识")}', name:'url_id',align:'center'},
		{ title:'${message("预留1")}', name:'undefined1',align:'center'},
		{ title:'${message("预留2")}', name:'undefined2',align:'center'},
		{ title:'${message("预留3")}', name:'undefined3',align:'center'}
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/basic/sbu/list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	//查询客户
	$("#selectStoreMember").bindQueryBtn({
		type:'storeMember',
		title:'${message("查询用户")}',
		url:'/member/store_member/select_store_member.jhtml?isMember=1'
	});
	
});

////查询客户
//$("#selectStore").click(function(){
//$("#selectStore").bindQueryBtn({
//	type:'store',
//	title:'${message("查询客户")}',
//	bindClick:false,
//	url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
//	callback:function(rows){
//		if(rows.length>0){
//			var row = rows[0];
//			$(".storeName").val(row.name);
//			$(".storeId").val(row.id);
//			[#if storeMember.memberType==1]
//			$(".saleOrgName").val(row.sale_org_name);
//			$(".saleOrgId").val(row.sale_org_id);
//			[/#if]
//		}
//	}
//});
//});

</script>
</head>
<body>
	<form id="listForm" action="/basic/sbu/list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        <!--   <dl>
        			<dt><p>${message("工程编号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="xx" btn-fun="clear" />
        			</dd>
        		</dl> -->
        		
			</div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>