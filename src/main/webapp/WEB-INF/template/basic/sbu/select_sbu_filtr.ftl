<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查询sbu")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	function initTypes(){
		types = {};
		[#list types as value]
			types['${value}'] = "${message('11111111'+value)}";
		[/#list]
	}
	$().ready(function() {
		
		/**初始化多选下拉框*/
		initMultipleSelect();
		
		/**初始化客户类型*/
		initTypes();
		
		var cols = [
		    		{ title:'${message("名称")}', name:'name' ,align:'center',renderer:function(val,item){
		    			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/basic/sbu/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		    		}},
//		    		{ title:'${message("默认")}', name:'is_default' ,align:'center',renderer:function(val,item){
//		    			if(val=='1'){
//		    				return '<span>是</span>';
//		    			}else if(val=='0'){
//		    				return '<span>否</span>';
//		    			}
//
//		    		}},
		    		{ title:'${message("状态")}', name:'status' ,align:'center',renderer:function(val,item){
		    			if(val=='1'){
		    				return '<span class="green">生效</span>';
		    			}else if(val=='0'){
		    				return '<span class="red">失效</span>';
		    			}
		    		} },
		    	];
		var multiSelect = false;
		[#if multi==2]
			multiSelect = true;
		[/#if]
		    	$mmGrid = $('#table-m1').mmGrid({
		    		multiSelect:multiSelect,
		    		autoLoad: true,
		    		fullWidthRows:true,
		    		checkByClickTd:true,
		    		[#if multi!=2]checkByDblClickTd:true,[/#if]
		    		rowCursorPointer:true,
		    		formQuery:true,
		            cols: cols,
		            fullWidthRows:true,
		            url: '/basic/sbu/select_sbu_data.jhtml',
		            params:function(){
		            	return $("#listForm").serializeObject();
		            },
		    		plugins : [
		                $('#paginator').mmPaginator()
		            ]
		        });
		
	
	});
	
	function childMethod(){
	   return $mmGrid.selectedRows();
	};
</script>
</head>
<body  style="min-width: 0px;">
<form id="listForm" action="b2b/sbu/select_sbu_data_filtr.jhtml" method="post">
	<input type="hidden" name="search" value="1">
	<input type="hidden" name="sbuId" value="${sbuId}">
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
	        </div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
	</form>
</body>
</html>