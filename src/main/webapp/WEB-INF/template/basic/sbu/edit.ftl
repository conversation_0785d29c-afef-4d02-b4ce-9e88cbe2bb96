<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增sbu")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">

function chooseOne(domEl){
	var $this = $(domEl).find("input[type='checkbox']");
	var $tr = $this.closest("table");
		$tr.find("input.isDefaultCheckbox").prop("checked",false);
		$this.prop("checked",true);
		$tr.find("input.isDefault").val("0");
		$(domEl).find("input.isDefault").val("1");
}

	
	
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $addShippingMethod = $("#addShippingMethod");
	var $addRoles = $("#addRoles");
	var $addPolicyType=$("#addPolicyType");
	var $addCaseType=$("#addCaseType");
	var $addInvoiceType=$("#addInvoiceType");
	// 表单验证
	$inputForm.validate({
		rules: {
			bankName: "required",
			bankCardNo: "required"
		} ,
		submitHandler:function(form){
			return false;
		}
		
	});
	
	// 表单验证
	$.validator.addClassRules({
		oaSn: {
			required: true
		}
	});
	/**发运方式*/
	var items=${shippingMethod_json};
	var shippingIndex=0;
	$addShippingMethod.click(function(){
		var row={};
		if($(".shippingMethod").length > 1){
			 $.message_alert('只能添加两种发运方式');
            return false;
		}
		if($(".isDefaultCheckbox").length == 0){
			row.is_default = true;
		}
		$shipping_mmGrid.addRow(row);
		shippingIndex=	parseInt(shippingIndex)+1
	})
	var shipping_cols = [
	        {title:'${message("发运方式")}', align:'center',name:'shipping_method',width:130  ,renderer: function(val,item,rowIndex){			
				var str='selected="selected"';
				var html = '<select name="shippingMethodSbuList['+shippingIndex+'].shippingMethod.id" class="text shippingMethod ">';
					[#list shippingWays as shippingWay]		
					if(${shippingWay.id}==val){
						html+='<option value="${shippingWay.id}" '+str+' >${shippingWay.value}</option> ';
					}else{
						html+='<option value="${shippingWay.id}" >${shippingWay.value}</option> ';
					}
					[/#list]
					 html+='</select>';
				return html;
		      }},
			{ title:'${message("设置")}', name:'is_default' ,align:'center',renderer:function(val,item,rowIndex, obj){
   				if(obj==undefined){
						if(item.is_default){
							return '<label onClick="chooseOne(this)">'+
							'<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" checked="true"/>是否默认'+
							'<input type="hidden" name="shippingMethodSbuList['+shippingIndex+'].isDefault" class="isDefault" value="1" /></label>';
				}else{
					return '<label onClick="chooseOne(this)">'+
					'<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" />是否默认'+
					'<input type="hidden" name="shippingMethodSbuList['+shippingIndex+'].isDefault" class="isDefault" value="0" /></label>';
				}
				}else{
				if($("input[value=1].isDefault").length>0){
					return '<label onClick="chooseOne(this)">'+
					'<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" />是否默认'+
					'<input type="hidden" name="shippingMethodSbuList['+shippingIndex+'].isDefault" class="isDefault" value="0" /></label>';
				}else{
					return '<label onClick="chooseOne(this)">'+
					'<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" checked/>是否默认'+
					'<input type="hidden" name="shippingMethodSbuList['+shippingIndex+'].isDefault" class="isDefault" value="1" /></label>';
				}
			}
    		}},
			{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
				shippingIndex++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteShipping(this)" >删除</a>';
			}}
	];
	$shipping_mmGrid = $('#table-shippingMethod').mmGrid({
			height:'auto',
		    cols: shipping_cols,
		    checkCol:false,
		    items:items,
		    autoLoad:true,
		    fullWidthRows: true,
		 });
	
		var roleIndex=0;
		var items=${pcRole_json};
		var role_cols = [
				{title:'${message("角色名称")}', align:'center',name:'shippingWay',width:130  ,renderer: function(val,item,rowIndex){			
					return '<input type="hidden" name="menuRoleSbus['+roleIndex+'].pcRole.id" class="roleId roleId'+item.id+'"  value="'+item.id+'"  />'+item.name;
				}},
				{title:'${message("角色描述")}', align:'center',name:'description',width:130},
				{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
					roleIndex++;
					return '<a href="javascript:;" class="deleteRole btn-delete"" >删除</a>';
				}}
		];
		$role_mmGrid = $('#table-role').mmGrid({
				height:'auto',
		    cols: role_cols,
		    checkCol:false,
		    items:items,
		    autoLoad:true,
		    fullWidthRows: true,
		});
		
		
				/**政策类型*/
		var items=${policy_json};		
		var policyIndex=0;
		$addPolicyType.click(function(){
			var row={};

			$policy_mmGrid.addRow(row);
			policyIndex=	parseInt(policyIndex)+1;
		})
		var policy_cols = [
				{title:'${message("政策类型")}', align:'center',name:'policy_type',width:130  ,renderer: function(val,item,rowIndex){			
					var str='selected="selected"';
				var html = '<select name="sbuPolicys['+policyIndex+'].policyType.id" class="text  policyType ">';
					[#list policyTypes as policyType]	
					if(${policyType.id}==val){
						html+='<option value="${policyType.id}" '+str+' >${policyType.value}</option> ';
					}else{
						html+='<option value="${policyType.id}" >${policyType.value}</option> ';
					}
					[/#list]
					 html+='</select>';
				return html;
				}},
				{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
					policyIndex++;
					return '<a href="javascript:;" class="btn-delete" onclick="deletePolicy(this)" >删除</a>';
				}}
		];
		$policy_mmGrid = $('#table-policyType').mmGrid({
				height:'auto',
		    cols: policy_cols,
		    checkCol:false,
		    items:items,
		    autoLoad:true,
		    fullWidthRows: true,
		});
		
		
		var items=${invoice_json};
		var invoiceIndex=0;
		$addInvoiceType.click(function(){
			var row={};
			

			$invoice_mmGrid.addRow(row);
			invoiceIndex=	parseInt(invoiceIndex)+1;
		})
		var invoice_cols = [
				{title:'${message("发票类型")}', align:'center',name:'invoice_type',width:130  ,renderer: function(val,item,rowIndex){			
				var str='selected="selected"';
				var html = '<select name="sbuInvoices['+invoiceIndex+'].invoiceType.id" class="text  invoiceType ">';
					[#list invoiceTypes as invoiceType]
					if(${invoiceType.id}==val){
						html+='<option value="${invoiceType.id}" '+str+' >${invoiceType.value}</option> ';
					}else{
						html+='<option value="${invoiceType.id}" >${invoiceType.value}</option> ';
					}
					[/#list]
					 html+='</select>';
				return html;
				}},
				{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
					invoiceIndex++;
					return '<a href="javascript:;" class="btn-delete" onclick="deleteInvoice(this)" >删除</a>';
				}}
		];
		$invoice_mmGrid = $('#table-invoiceType').mmGrid({
				height:'auto',
		    cols: invoice_cols,
		    checkCol:false,
		    items:items,
		    autoLoad:true,
		    fullWidthRows: true,
		});
		
		
		var items=${cash_json};
		var caseIndex=0;
		$addCaseType.click(function(){
			var row={};

			$case_mmGrid.addRow(row);
			caseIndex=	parseInt(caseIndex)+1;
		})
		var case_cols = [
				{title:'${message("ERP现金流")}', align:'center',name:'case_project',width:130  ,renderer: function(val,item,rowIndex){			
				var str='selected="selected"';
				var html = '<select name="sbuCases['+caseIndex+'].caseProject.id" class="text  caseProject ">';
					[#list cashProjects as cashProject]
					if(${cashProject.id}==val){
						html+='<option value="${cashProject.id}" '+str+' >${cashProject.value}</option> ';
					}else{
						html+='<option value="${cashProject.id}" >${cashProject.value}</option> ';
					}
					[/#list]
					 html+='</select>';
				return html;
				}},
				{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
					caseIndex++;
					return '<a href="javascript:;" class="btn-delete" onclick="deleteCase(this)" >删除</a>';
				}}
		];
		$case_mmGrid = $('#table-cashProject').mmGrid({
				height:'auto',
		    cols: case_cols,
		    checkCol:false,
		    items:items,
		    autoLoad:true,
		    fullWidthRows: true,
		});
	
		//打开选择角色
	$addRoles.click(function(){
            $addRoles.bindQueryBtn({
            	type:'role',
        		title:'${message("查询角色")}',
        		url:'/member/menu_role/select_menu_role.jhtml?multi=2',
            	bindClick:false,
                callback:function(rows){
                    if(rows.length>0){
                        for (var i = 0; i < rows.length;i++) {
                            var idH = $(".roleId"+rows[i].id).length;
                            if(idH > 0){
                                $.message_alert('角色【'+rows[i].name+'】已添加');
                                return false;
                            }
                        }
                        for (var i = 0; i < rows.length;i++) {
							var row = rows[i];
							
							$role_mmGrid.addRow(row,null,1);
						}
                    }
                }
            }); 
    })
	
		$(".deleteRole").live("click", function() {
			var index = $(this).closest("tr").index();
			$.message_confirm('您确定要删除吗？',function(){
				$role_mmGrid.removeRow(index);
		})
		});
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	});
	
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
});
function deleteShipping(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$shipping_mmGrid.removeRow(index);
	})
}

function deletePolicy(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$policy_mmGrid.removeRow(index);
		})
	}
	
	function deleteInvoice(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$invoice_mmGrid.removeRow(index);
		})
	}
	
	function deleteCase(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$case_mmGrid.removeRow(index);
		})
	}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑sbu")}
	</div>
	<form id="inputForm" action="/basic/sbu/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${id}" />
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("名称")}:
				</th>
				<td>
					<input type="text" name="name" class="text" maxlength="200" value="${sbus.name}" btn-fun="clear" />
				</td>
				<th>${message("状态")}:</th>
				<td>
					<select class="text" name="status">
					<option value="true" [#if sbus.status==true]selected[/#if]>生效</option>
					<option value="false" [#if sbus.status==false]selected[/#if]>失效</option>
					</select>
				</td>
				<th>
				${message("编码前缀")}:
				</th>
				<td>
					<input type="text" name="codePre" class="text" maxlength="200" value="${sbus.codePre}"  btn-fun="clear" />
				</td>
				<th>
					${message("URL标识")}:
				</th>
				<td>
					<input type="text" name="urlId" class="text" maxlength="200"  value="${sbus.urlId}" btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>
					${message("预留1")}:
				</th>
				<td>
					<input type="text" name="undefined1" class="text" maxlength="200"  value="${sbus.undefined1}"  btn-fun="clear" />
				</td>
				<th>
					${message("预留2")}:
				</th>
				<td>
					<input type="text" name="undefined2" class="text" maxlength="200"  value="${sbus.undefined2}" btn-fun="clear" />
				</td>
				<th>
					${message("预留3")}:
				</th>
				<td>
					<input type="text" name="undefined3" class="text" maxlength="200"  value="${sbus.undefined3}" btn-fun="clear" />
				</td>
				<th>
					${message("预留4")}:
				</th>
				<td>
					<select id="type" class="text" name="organizationsId">
						[#list organizations as or]
                            <option value="${or.id}" [#if sbus.undefined4.id == or.id]selected[/#if]>${or.name}</option>
                        [/#list]
                    </select>
				</td>
			</tr>
			<tr>
				<th>
					${message("预留5")}:
				</th>
				<td>
					<input type="text" name="undefined5" class="text" maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("预留6")}:
				</th>
				<td>
					<input type="text" name="undefined6" class="text" maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("预留7")}:
				</th>
				<td>
					<input type="text" name="undefined7" class="text" maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("预留8")}:
				</th>
				<td>
					<input type="text" name="undefined8" class="text" maxlength="200"  btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>
					${message("预留9")}:
				</th>
				<td>
					<input type="text" name="undefined9" class="text" maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("预留10")}:
				</th>
				<td>
					<input type="text" name="undefined10" class="text" maxlength="200"  value="${sbus.undefined10}"  btn-fun="clear" />
				</td>
				<!-- <th>
					${message("预留11")}:
				</th>
				<td>
					<input type="text" name="undefined11" class="text" maxlength="200"  btn-fun="clear" />
				</td> -->
			</tr>
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			<div class="title-style">
				${message("发运方式")}:
				<div class="btns">
					<a href="javascript:void(0);" id="addShippingMethod" class="button">${message("添加")}</a>
				</div>
			</div>
			<table id="table-shippingMethod"></table>
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
					${message("政策类型")}:
					<div class="btns">
						<a href="javascript:void(0);" id="addPolicyType" class="button">${message("添加")}</a>
					</div>
				</div>
				
				<table id="table-policyType"></table>
			</table>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
					${message("发票类型")}:
					<div class="btns">
						<a href="javascript:void(0);" id="addInvoiceType" class="button">${message("添加")}</a>
					</div>
				</div>
				
				<table id="table-invoiceType"></table>
			</table>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
					${message("ERP现金流")}:
					<div class="btns">
						<a href="javascript:void(0);" id="addCaseType" class="button">${message("添加")}</a>
					</div>
				</div>
				
				<table id="table-cashProject"></table>
			</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
					${message("角色")}:
					<div class="btns">
						<a href="javascript:void(0);" id="addRoles" class="button">${message("添加")}</a>
					</div>
				</div>
				
				<table id="table-role"></table>
		</table>
		</div>
		<div class="fixed-top">
	<!--注释
	<a href="/basic/sbu/delete.jhtml" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("删除")}
			</a>-->

			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>