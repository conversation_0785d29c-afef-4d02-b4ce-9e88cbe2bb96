<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style type="text/css">
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
#qButton{
width:20px;
height:20px;
position:absolute;
top:4px;
left:12px;
background:url(/resources/images/button/ico-aa.png) no-repeat center;
}
#addquantity {
padding:0 21px;
height:28px;
line-height:28px;
margin-right:0;
}
#n-input{
width:74%;
float:left;
margin-right:5px;
}
</style>
<script type="text/javascript">
$().ready(function() {
	$.validator.addClassRules({
		name: {
			required: true
		},
		fullName: {
			required: true
		}
	});
    $("#submit_button").click(function(){
			$("form").submit();
	});
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'edit.jhtml?id='+resultMsg.objx;
			});
	    }
	 });
	 
	 //查询销售区域	
	$("#selectsalesArea").bindQueryBtn({
		type:'salesArea',
		title:'${message("销售区域")}',
		url:'/basic/sales_area/select_salesarea.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$(".parentName").val(row.name);
				$(".parentId").val(row.id);
			}
		}
	});
});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增区域")}
	</div>
	<form id="inputForm" action="/basic/sales_area/update.jhtml" method="post"  type="ajax" validate-type="validate">
		<input type="hidden" name="locale" value="ChinaCN"/>
		<input type="hidden" name="id" value="${salesArea.id}"/>
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("上级区域")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="parentId" value="${salesArea.parent.id}" class="text parentId" btn-fun="clear"/>
					<input type="text" name="parentName" class="text parentName"  maxlength="200" onkeyup="clearSelect(this)" value="${salesArea.parent.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectsalesArea">
					</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("名称")}:
				</th>
				<td>
					<input type="text" id="name" name="name" value="${salesArea.name}" class="text " maxlength="200" btn-fun="clear" />
				</td>
				    <th>${message("外部编码")}:</th>
					<td><input type="text" name="erpSn" class="text "  value="${salesArea.erpSn}" btn-fun="clear" /></td>
					<th>${message("逻辑区域类型")}:</th>
					<td><select name="logicType" class="text">
							<option value=""></option>
							<option value="1" [#if salesArea.logicType ==1]selected="selected"[/#if]>国家级</option>
							<option value="2" [#if salesArea.logicType ==2]selected="selected"[/#if]>省级</option>
							<option value="3" [#if salesArea.logicType  == 3]selected="selected"[/#if]>区域级</option>
							<option value="4" [#if salesArea.logicType  == 4]selected="selected"[/#if]>大区级</option>
					</select></td>
					</tr>
					<tr>
					<th>${message("地理区域类型")}:</th>
					<td><select name="geographyType" class="text">
							<option value=""></option>
							<option value="1" [#if salesArea.geographyType =='1']selected="selected"[/#if]>国家</option>
							<option value="2" [#if salesArea.geographyType =='2']selected="selected"[/#if]>(省/直辖市)</option>
							<option value="3" [#if salesArea.geographyType  == '3']selected="selected"[/#if]>城市</option>
							<option value="4" [#if salesArea.geographyType =='4']selected="selected"[/#if]>(区/县)</option>
					</select></td>
					<th>${message("主要结构")}:</th>
					<td><select name="mainStructure" class="text">
							<option value=""></option>
							<option value="1" [#if salesArea.mainStructure =='1']selected="selected"[/#if]>大自然逻辑销售区域结构</option>
					</select></td>
				<th>
					${message("是否逻辑区域")}:
				</th>
				<td >
					<label><input type="checkbox" name="isLogic" value="true" [#if salesArea.isLogic == 'true']checked="checked"[/#if]/>${message("12057")}</label>&nbsp;&nbsp;
					<input type="hidden" name="_isLogic" value="false" />
				</td>
               	<th>
					${message("是否地理区域")}:
				</th>
				<td >
					<label><input type="checkbox" name="isGeography" value="true" [#if salesArea.isGeography =='true']checked="checked"[/#if]/>${message("12057")}</label>&nbsp;&nbsp;
					<input type="hidden" name="_isGeography" value="false" />
				</td>
			</tr>
			<tr>
				<th>
					${message("是否启用")}:
				</th>
				<td >
					<label><input type="checkbox" name="isEnabled" value="true" [#if salesArea.isEnabled =='true']checked="checked"[/#if]/></label>&nbsp;&nbsp;
					<input type="hidden" name="_isEnabled" value="false" />
				</td>
			</tr>
			<tr>
			<th>
					${message("说明")}:
				</th>
				<td  colspan="7">
					<input type="text"  name="instruction" class="text instruction" value="${salesArea.instruction}" maxlength="200" btn-fun="clear" />
				</td>
			</tr>
		</table>
		</div>
		
		<div class="fixed-top">
			<input type="button" id="submit_button" class="button sureButton" value='${message("保存")}'>
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
		
	</form>
</body>
</html>