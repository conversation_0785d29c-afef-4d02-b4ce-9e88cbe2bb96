<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("12067")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,0,false,e)
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	// 表单验证
	$inputForm.validate({
		rules: {
			name: "required",
			//grade: "required"
			},
		submitHandler:function(form){
			return false;
		}
	});
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".saleOrg_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('saleOrg['+rows[i].name+']已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					var row = rows[i];
					$mmGrid.addRow(row,null,1);
				}
			}
		}
	});
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	 });
	
	var index = 0;
	var cols = [
		{ title:'${message("机构")}', align:'center',name:'name',renderer: function(val,item,rowIndex){
			if(item.so_name == undefined){
				return '<input type="hidden" name="memberRankSaleOrg['+index+'].saleOrg.id" class="text saleOrg saleOrg_'+item.id+'" value="'+item.id+'"  />'+item.name;
			}else{
			   return '<input type="hidden" name="memberRankSaleOrg['+index+'].saleOrg.id" class="text saleOrg saleOrg_'+item.sale_org+'" value="'+item.sale_org+'"  />'+item.so_name;
			}
		}},
		{ title:'${message("操作")}', align:'center', renderer:function(val,item){
			index++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteAddress(this)">删除</a>';
		}}
	];
	var items  = ${json!0};
	if(items == 0){
		items = new Array();
	}
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
	    cols: cols,
	    items:items,
        checkCol: false,
        autoLoad: true,
	    fullWidthRows: true
	 });



	//查询sbu
	$("#selectSbu").live("click",function(){
		var $this = $(this);
		$this.bindQueryBtn({
			type:'sbu',
			title:'${message("查询sbu")}',
			url:'/basic/sbu/select_sbu.jhtml',
			callback:function(rows){
				if(rows.length>0){
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".sbu_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('sbu['+rows[i].name+']已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid2.addRow(row,null,1);
					}
				}
			}
		});
	})

	var index2 = 0;
	var cols2 = [
		{ title:'${message("SBU")}', align:'center',name:'name',renderer: function(val,item,rowIndex){
				if(item.sbu_name == undefined){
					return '<input type="hidden" name="memberRankSbu['+index2+'].sbu.id" class="text sbu sbu_'+item.id+'" value="'+item.id+'"  />'+item.name;
				}else{
					return '<input type="hidden" name="memberRankSbu['+index2+'].sbu.id" class="text sbu sbu_'+item.sbu+'" value="'+item.sbu+'"  />'+item.sbu_name;
				}
			}},
		{ title:'${message("操作")}', align:'center', renderer:function(val,item){
				index2++;
				return '<a href="javascript:;" class="btn-delete" onclick="deleteAddress2(this)">删除</a>';
			}}
	];
	var items2  = ${jsonSbu!0};
	if(items2 == 0){
		items2 = new Array();
	}
	$mmGrid2 = $('#table-m2').mmGrid({
		height:'auto',
		cols: cols2,
		items: items2,
		checkCol: false,
		autoLoad: true,
		fullWidthRows: true
	});


});

function deleteAddress(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
	})
}

function deleteAddress2(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid2.removeRow(index);
	})
}

function del(t) {
	var str = '您确定要删除吗';

	ajaxSubmit(t, {
		url: 'delete.jhtml?ids=${memberRank.id}',
		method: "post",
		isConfirm: true,
		confirmText: str,
		callback: function (resultMsg) {
			$.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
				parent.change_tab(1, 'list.jhtml');
			});
		}
	})
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看价格类型")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${memberRank.id}" />
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("价格类型名称")}:
				</th>
				<td><input type="text" name="name" class="text" value="${memberRank.name}" maxlength="100"  /></td>
				<th>
					<span class="text"></span>${message("16034")}:
				</th>
				<td>
					<div class="nums-input ov">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
						<input type="text"  class="t"  name="grade" value="${memberRank.grade}" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>						
				</td>
				<th>${message("类型区分")}:</th>
					<td>
					<select id="rankTypeName" name="rankTypeId" class="text"> 
					    <option value="">请选择</option>
						[#list rankTypes as rankType]
							<option value="${rankType.id}" [#if memberRank.rankType.id == rankType.id]selected[/#if]>${rankType.value}</option>
						[/#list]
					</select>
				</td>
	<!--			<th>${message("机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${memberRank.saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${memberRank.saleOrg.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td> 
				<th></th><td></td>   -->
			</tr>
			<tr>
				<th>
					${message("16035")}:
				</th>
				<td colspan="5">
					<label>
						<input type="checkbox" name="isEnabled" value="true"[#if memberRank.isEnabled] checked="checked"[/#if] />${message("是否启用")}
						<input type="hidden" name="_isEnabled" value="false" />
					</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<label>
						<input type="checkbox" name="isDefault" value="true"[#if memberRank.isDefault] checked="checked"[/#if] />${message("16036")}
						<input type="hidden" name="_isDefault" value="false" />
					</label>
					<input type="hidden" name="isSpecial" value="false" />
				</td>
			</tr>
		</table>
		<div class="title-style">
			<div class="btns">
			 <a href="javascript:void(0);" id="selectSaleOrg" class="button">选择机构</a>
			</div>
		</div>

		<table id="table-m1" ></table>
		<div class="title-style">
			<div class="btns">
				<a href="javascript:void(0);" id="selectSbu" class="button">选择SBU</a>
			</div>
		</div>
		<table id="table-m2" ></table>

		</div>
		<div class="fixed-top">
			<a href="add.jhtml" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<a href="javascript:void(0);" class="button cancleButton" onclick="del(this,0)">${message("删除")}</a>
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>