<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("编辑机构")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
		
	$inputForm = $("#inputForm");
		
		// 表单验证
	$inputForm.validate({
		rules: {
    		name: "required",
    		factoryType: "required",	
		} ,
		submitHandler:function(form){
			return false;
		}
	});
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});	
	
	//查询机构价格类型
	$("#selectMemberRank").bindQueryBtn({
		type:'memberRank',
		title:'${message("查询机构价格类型")}',
		url:'/basic/member_rank/select_memberRank.jhtml'
	});
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	 });
});
function del(t){
	var str='您确定要删除吗';
	
	ajaxSubmit(t,{
		 url: '/basic/saleOrg/delete.jhtml?ids=${saleOrg.id}',
		 method: "post",
		 isConfirm:true,
		 confirmText : str,
		 callback:function(resultMsg){
		 	$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				parent.change_tab(1,'/basic/saleOrg/list.jhtml');
			});
		 }
	})
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看机构")}
	</div>
	<form id="inputForm" [#if saleOrg.isTop][#else]action="/basic/saleOrg/update.jhtml"[/#if] method="post" type="ajax" validate-type="validate">
        <input type="hidden" name="id" value="${saleOrg.id}" />
        <input  type="hidden" name="isTop" value="false"/>
        <input  type="hidden" name="isLeaf" value="[#if saleOrg.isLeaf]true[#else]false[/#if]"/>
	<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField"></span>${message("机构类型")}:
				</th>
				<td>
					<select name="type" class="text">
						[#list saleOrgTypes as saleOrgType]
						<option value="${saleOrgType.id}" [#if saleOrg.type == saleOrgType.id]selected="selected"[/#if]>${saleOrgType.value}</option>
						[/#list]
						<!-- <option value="1" [#if saleOrg.type == 1]selected="selected"[/#if]>办事处</option>
						<option value="2" [#if saleOrg.type == 2]selected="selected"[/#if]>分公司</option>
						<option value="3" [#if saleOrg.type == 3]selected="selected"[/#if]>销售公司</option>
						<option value="4" [#if saleOrg.type == 4]selected="selected"[/#if]>代理商</option>-->
					</select>
				</td>
			[#if saleOrg.parent??]
				<th>${message("上级机构")}:</th>
				<td>
					${saleOrg.parent.name}
					<input type="hidden" name="saleOrgId" value="${saleOrg.parent.id}"/>
				</td>
			[#else]
				<th>${message("上级机构")}:</th>
				<td>${message("顶级机构")}</td>
			[/#if]
				<th>
					<span class="requiredField">*</span>${message("机构名称")}:
				</th>
				<td>
					<input type="text" name="name" class="text" maxlength="200" btn-fun="clear" value="${saleOrg.name}"/>
				</td>
				<th>
					${message("机构编码")}:
				</th>
				<td>
					<input type="text" name="sn" class="text" maxlength="200" btn-fun="clear" value="${saleOrg.sn}"/>
				</td>
			</tr>
			<tr style="display: none;">
				<th>${message("11091")}</th>
				<td>
					${saleOrg.intf_key}
				</td>
				<th class="noBg"></th><td colspan="3"></td>
			</tr>
			<tr>
				[#--<th>
            		${message("sbu")}:
            	</th>
           		<td>
            		<select id="sbu" name="sbuId" class="text">
						[#list sbus as sbu]
						<option value="${sbu.id}"[#if saleOrg.sbu.id==sbu.id] selected[/#if]>${sbu.value}</option>
						[/#list]
					</select>
            	</td>
            	<th>${message("机构价格类型")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="memberRankId" class="text memberRankId" btn-fun="clear" value="${saleOrg.memberRank.id}"/>
					<input type="text" name="memberRankName" class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" value="${saleOrg.memberRank.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectMemberRank">
					</span>
				</td>--]
				<th>	
					${message("11092")}:
				</th>
				<td  colspan="3">
					<label>
						<input type="checkbox" name="isEnabled" value="true" [#if saleOrg.isEnabled == true]checked="checked"[/#if] />是否启用
							<input type="hidden" name="_isEnabled" value="false" />
					</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<label>
						<input type="checkbox" name="isSellSaleOrg" value="true" [#if saleOrg.isSellSaleOrg == true]checked="checked"[/#if] />是否销售机构
						<input type="hidden" name="_isSellSaleOrg" value="false" />
					</label>
				</td>
<!-- 				<th class="noBg"></th><td colspan="5"></td> -->
				<th>	
					${message("负责人")}:
				</th>
				<td>
					<input type="text" name="functionary" class="text" value="${saleOrg.functionary}" maxlength="200" btn-fun="clear" value=""/>
				</td>
				<th>	
					<span class="requiredField">*</span>${message("类型")}:
				</th>
				<td>
					<select name="factoryType" class="text">
						<option value="true"[#if saleOrg.factoryType == true] selected="selected"[/#if]>${message("工厂")}</option>
						<option value="false"[#if saleOrg.factoryType == false] selected="selected"[/#if]>${message("机构")}</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>	
					${message("区域")}:
				</th>
				<td>
					<select name="region" class="text area">
						<option></option>
						<option value="1" [#if saleOrg.region == 1] selected="selected"[/#if]>${message("南区")}</option>
						<option value="0" [#if saleOrg.region == 0] selected="selected"[/#if]>${message("北区")}</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
					<textarea name="memo" class="text">${saleOrg.memo}</textarea>
				</td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
				<a href="/basic/saleOrg/add/${code}.jhtml?type=${type}" class="iconButton" id="addButton">
					<span class="addIcon">&nbsp;</span>${message("1001")}
				</a>
			[#if saleOrg.isTop]
			[#else]
				<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			[/#if]
			<a href="javascript:void(0);" class="button cancleButton" onclick="del(this,0)">${message("删除")}</a>
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>