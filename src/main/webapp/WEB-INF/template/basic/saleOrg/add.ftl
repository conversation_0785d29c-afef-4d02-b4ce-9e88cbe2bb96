<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("添加机构")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
		
	$inputForm = $("#inputForm");
		
		// 表单验证
	$inputForm.validate({
		rules: {
    		name: "required",
    		factoryType: "required",	
		} ,
		submitHandler:function(form){
			return false;
		}
	});
		
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
	
	//查询机构价格类型
	$("#selectMemberRank").bindQueryBtn({
		type:'memberRank',
		title:'${message("查询机构价格类型")}',
		url:'/basic/member_rank/select_memberRank.jhtml'
	});
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/basic/saleOrg/edit/${code}.jhtml?id='+resultMsg.objx;
			})
	    }
	 });
});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增机构")}
	</div>
	<form id="inputForm" action="/basic/saleOrg/save.jhtml" method="post" type="ajax" validate-type="validate">
		<input  type="hidden" name="isTop" value="false"/>
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField"></span>${message("机构类型")}:
				</th>
				<td>
					<select name="type" class="text">
					[#list saleOrgTypes as saleOrgType]
						<option value="${saleOrgType.id}">${saleOrgType.value}</option>
						[/#list]
					</select>
				</td>
			[#if parent!=null]
				<th>${message("上级机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${parent.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${parent.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td>
			[#else]	
				<th>${message("上级机构")}:</th>
				<td>${message("顶级机构")}</td>
			[/#if]
				<th>
					<span class="requiredField">*</span>${message("机构名称")}:
				</th>
				<td>
					<input type="text" name="name" class="text" maxlength="200"   btn-fun="clear" />
				</td>
				<th>
					${message("机构编码")}:
				</th>
				<td>
					<input type="text" name="sn" class="text" maxlength="200"   btn-fun="clear" />
				</td>
			</tr>
			<tr>
            	[#--<th>${message("机构价格类型")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="memberRankId" class="text memberRankId" btn-fun="clear" value=""/>
					<input type="text" name="memberRankName" class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectMemberRank">
					</span>
				</td>--]
				<th>
					${message("11092")}:
				</th>
				<td colspan="3">
					<label>
						<input type="checkbox" name="isEnabled" value="true" checked="checked" />是否启用
						<input type="hidden" name="_isEnabled" value="false" />
					</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<label>
						<input type="checkbox" name="isSellSaleOrg" value="true" checked="checked" />是否销售机构
						<input type="hidden" name="_isSellSaleOrg" value="false" />
					</label>
				</td>
<!-- 				<th class="noBg"></th><td colspan="3"></td> -->
				<th>	
					${message("负责人")}:
				</th>
				<td>
					<input type="text" name="functionary" class="text" value="${saleOrg.functionary}" maxlength="200" btn-fun="clear" value=""/>
				</td>
				<th>	
					<span class="requiredField">*</span>${message("类型")}:
				</th>
				<td>
					<select name="factoryType" class="text">
						<option value="false">${message("机构")}</option>
						<option value="true">${message("工厂")}</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>	
					${message("区域")}:
				</th>
				<td>
					<select name="region" class="text area">
						<option></option>
						<option value="1">${message("南区")}</option>
						<option value="0">${message("北区")}</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
					<textarea name="memo" class="text"></textarea>
				</td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>