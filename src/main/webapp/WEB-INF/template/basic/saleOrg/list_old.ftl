<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("机构列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
function addFun(){
    var rows = $mmGrid.selectedRows();
    var ids_str='';
    if(rows.length>0){
    	ids_str = 'ids='+rows[0].id;
    }
	parent.change_tab(0,'/basic/saleOrg/add/${code}.jhtml?type=${type}&'+ids_str);
  }
function getChildren(e){
	var status = $(e).attr("data-status");
	var id = $(e).attr("data-id");
	var rowIndex = $(e).closest("tr").index();
	var oId;
	$idThis= $(".mmGrid").find(".id"+id)
	if(status==""){
		rowIndex = parseInt(rowIndex) + 1;
		//console.log(rowIndex)
		$(e).attr("data-status","1");	
		ajaxSubmit($(id),{
			method:'post',
			url:'/basic/saleOrg/getChildren.jhtml?id='+id,
			callback: function(data) {
				var uid 
				var item=data.objx;
				if($(e).hasClass("topNav")){
					uid ='id'+$(e).attr("data-id");
				}else{
					uid= $(e).attr("data-class");
				}	
				for(var i=0;i<item.length;i++){
					item[i].parent=id;
				}
				$mmGrid.addRow(item,rowIndex,uid);
				$("tr").removeClass("even");
				$("tr:nth-child(2n)").addClass("even");
			}
		})
		$(e).addClass("treeClose").removeClass("treeOpen")		
	}else if(status==1){
		$idThis.closest("tr").hide();	
		$(e).removeClass("treeClose").addClass("treeOpen")		
		$(e).attr("data-status","0");
		$("tr").removeClass("even");
		$("body").find("tr:visible:even").addClass("even");
	}else{
		$idThis.closest("tr").show();
		oId = $(".mmGrid").find(".id"+id+".treeOpen").attr("data-id")
		$(".mmGrid").find(".id"+oId).closest("tr").hide();		
		$(e).addClass("treeClose").removeClass("treeOpen")
		$(e).attr("data-status","1");
		$("tr").removeClass("even");
		$("body").find("tr:visible:even").addClass("even");
	}
}
function add(){
	parent.change_tab(0,'/basic/saleOrg/add/${code}.jhtml?type=${type}',1);
}
$().ready(function() {
	var $listForm = $("#listForm");
	var $delete = $("#listTable a.delete");
	var $searchBtn = $("#searchBtn");
	var $hideShow = $("#hideShow");
	var $searchDiv = $("#searchDiv");
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	var cols = [
		{ title:'${message("机构名称")}', name:'name' , align:'left', renderer: function(val,item,rowIndex,obj){
			var grade = item.grade;
			var style_str = 'margin-left: '+(grade*30)+'px;';
			if(item.is_search == 1){
				return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/basic/saleOrg/edit/${code}.jhtml?id='+item.id+'&flag=${flag}\')" class="red">'+val+'</a>';
			}else{
				if(obj == undefined){
					if(item.is_leaf == 1){
						return '<span  style="'+style_str+' padding-left:30px"><a href="javascript:void(0);" onClick="parent.change_tab(0,\'/basic/saleOrg/edit/${code}.jhtml?id='+item.id+'\')" class="red">'+val+'</a></span>';
					}else{
						return '<span style="'+style_str+'" class="treeOpen topNav open" onclick="getChildren(this)" data-id="'+item.id+'" data-rowIndex ="'+rowIndex+'" data-status=""></span><a href="javascript:void(0);" onClick="parent.change_tab(0,\'/basic/saleOrg/edit/${code}.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
					}	
				}else{
					if(item.is_leaf == 1){
						return '<span  style="'+style_str+' padding-left:30px" class="'+obj+'" ><a href="javascript:void(0);" onClick="parent.change_tab(0,\'/basic/saleOrg/edit/${code}.jhtml?id='+item.id+'\')" class="red">'+val+'</a></span>';
					}else{
						return '<span style="'+style_str+'" onclick="getChildren(this)" class="treeOpen '+obj+'" data-class="'+obj+' id'+item.id+'"  data-id="'+item.id+'" data-rowIndex ="'+rowIndex+'" data-status=""></span><a href="javascript:void(0);" onClick="parent.change_tab(0,\'/basic/saleOrg/edit/${code}.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
					}	
				}
			}	
		}},
		{ title:'${message("机构编码")}', name:'sn' , align:'center'},
		{ title:'${message("机构类型")}', name:'value' , align:'center' },
		/*
		{ title:'${message("费用预算")}', name:'budget' ,align:'center',renderer:function(val){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		{ title:'${message("冻结费用预算")}', name:'lock_budget' ,align:'center',renderer:function(val){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		*/
		{ title:'${message("12057")}', name:'is_enabled' , align:'center',renderer:function(val){
		if(val == true){
			return '<span class="trueIcon">&nbsp;</span>';
		}else{
			return '<span class="falseIcon">&nbsp;</span>';
		}
		}},
		{ title:'${message("上级机构")}', name:'tree_path_name' , align:'center'},
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
		multiSelect:false,
		fullWidthRows:true,
        cols: cols,
        url: '/basic/saleOrg/list_data.jhtml',
        callback:function(){
        	var e=$(".topNav");
        	getChildren(e);
        },
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });

});
function productCategoryImport(e){	
	excel_import(e,{
		title:'${message("机构导入")}',
		url:'/basic/saleOrg/import_excel.jhtml',
		template:'/resources/template/product/productCategory.xls',
		callback:function(){
			$("#searchBtn").click();
		}
	})
} 	
</script>
</head>
<body>
	<form id="listForm" action="/basic/saleOrg/list.jhtml" method="get">
	<div class="bar">
		<div class="buttonWrap">
			<!--<a href="javascript:void(0);" id="importButton" class="iconButton" onclick="productCategoryImport(this)">
				<span class="importIcon">&nbsp;</span>${message("导入")}
			</a>-->
			[#if flag != 1]
			<a href="javascript:addFun();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			[/#if]
		</div>
		[#--搜索begin--]
		<div class="search-btn">
			<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
		</div>
		<div id="searchDiv">
			<div id="search-content" >
				<dl>
					<dt><p>${message("机构名称")}：</p></dt>
					<dd>
						<input class="text" maxlength="200" type="text" name="saleOrgName"  btn-fun="clear">
					</dd>
				</dl>
				<dl>
					<dt><p>${message("机构编码")}：</p></dt>
					<dd>
						<input class="text" maxlength="200" type="text" name="saleOrgSn"  btn-fun="clear">
					</dd>
				</dl>
		    	<dl>
		    		<dt><p>${message("12057")}：</p></dt>
		    		<dd>
		    			<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			<label><input name="isEnabled" value="true" type="checkbox" class="check js-iname" />是</label>
				       			<label><input name="isEnabled" value="false"  class="check js-iname" type="checkbox" />否</label>
				       		</div>
				       	</div>
		    		</dd>
		    	</dl>
			 </div>		
		</div>
	</div>
	[#--搜索end--]
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>