<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查询价格等级")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
	var cols = [
		{ title:'${message("地区名称")}', name:'name', align:'center'},
		{ title:'${message("地区全称")}', name:'full_name', align:'center'},
	];
	var multiSelect = false;
	[#if multi==2]
		multiSelect = true;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		rowCursorPointer:true,
		formQuery:true,
        cols: cols,
        url: 'select_area_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
});
function childMethod(){
   return $mmGrid.selectedRows();
};
</script>
</head>
<body  style="min-width: 0px;">
<form id="listForm" action="select_area.jhtml" method="get">
	<input type="hidden" name="locale" value="${locale}">
	<div class="bar">	
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
		    	<dl>
		    		<dt ><p>${message("地区名称")}：</p></dt> 
	    			<dd>
	    				<input type="text" class="text"  name="name" value =""  btn-fun="clear"/>
	    			</dd>
		    	</dl>
        		<dl>
		    		<dt ><p>${message("地区全称")}：</p></dt> 
	    			<dd>
	    				<input type="text" class="text" name="fullName" value =""  btn-fun="clear"/>
	    			</dd>
		    	</dl>
	        </div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">	
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
</form>
</body>
</html>