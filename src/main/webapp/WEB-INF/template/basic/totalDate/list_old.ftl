<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("总账日期")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
 
function edit(id){
	parent.change_tab(0,'edit.jhtml?id='+id);
}
function add(){
	parent.change_tab(0,'add.jhtml');
}
$().ready(function() {
	
	/**初始化多选的下拉框*/
	initMultipleSelect();

	var cols = [
		{ title:'${message("单据编号")}', name:'sn' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
		}},        
		{ title:'${message("机构")}', name:'sale_org_name' ,align:'center' }, 
		{ title:'${message("sbu")}', name:'sbu_name' ,align:'center' }, 
		{ title:'${message("经营组织")}', name:'organization_name' ,align:'center' }, 
		{ title:'${message("是否启用")}', name:'is_enabled'  ,align:'center',renderer:function(val){
			if(val == true){
				return '<span class="trueIcon">&nbsp;</span>';
			}else{
				return '<span class="falseIcon">&nbsp;</span>';
			}
		}},
		{ title:'${message("开始日期")}', name:'start_date' ,align:'center' }, 
		{ title:'${message("结束日期")}', name:'end_date' ,align:'center' }, 
		{ title:'${message("有效开始日期")}', name:'effective_start_date' ,align:'center' }, 
		{ title:'${message("有效结束日期")}', name:'effective_end_date' ,align:'center' }, 
		{ title:'${message("创建人")}', name:'b_creater' ,align:'center' }, 
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' }, 
		{ title:'${message("最后修改人")}', name:'b_modifier' ,align:'center' },
		{ title:'${message("最后修改日期")}', name:'modify_date' ,align:'center' },
		{ title:'${message("备注")}', name:'remark' ,align:'center' }
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
        cols: cols,
        fullWidthRows:true,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	//机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='saleOrgName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='saleOrgName']").val();
				}
	
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".saleOrgId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="saleOrgId" class="text saleOrgId saleOrgId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newsaleOrgClosePro(this)"></i></div>';
						$(".saleOrg").append(vhtml);
					}
				}
				$("input[name='saleOrgName']").attr("value",allName);
			}
		}
	});		
	
}); 

function newsaleOrgClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".saleOrg > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='saleOrgName']").attr("value",allName2)
};


</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
	        		<dl>
	    				<dt ><p>${message("单据编号")}:</p></dt>
		    			<dd >
		    				<input type="text" class="text"  name="sn"  btn-fun="clear"/>
		    			</dd>
		    		</dl>
		    		<dl>
						<dt><p>${message("是否启用")}:</p></dt>
						<dd>
						<select name="isEnabled" class="text">
							<option value>请选择</option>
							<option value=0>否</option>
							<option value=1 selected="selected">是</option>
						</select>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("机构名称")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear"/>
							<input type="text" name="saleOrgName" class="text saleOrgId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
							<input type="button" class="iconSearch" id="selectSaleOrg">
							<div class="pupTitleName  saleOrg"></div>
							</span>
						</dd>
					</dl>
	        		<dl>
	        			<dt><p>${message("SBU")}：</p></dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list storeMemberSbuList as storeMemberSbu]
										<label><input class="check js-iname" name="sbuId" value="${storeMemberSbu.sbu.id}" type="checkbox"/>${storeMemberSbu.sbu.name}</label>
									[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
	        		<dl>
	        			<dt><p>${message("经营组织")}：</p></dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list storeMemberOrganizationList as storeMemberOrganization]
										<label><input class="check js-iname" name="organizationId" value="${storeMemberOrganization.organization.id}" type="checkbox"/>${storeMemberOrganization.organization.name}</label>
									[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
	        		<dl>
						<dt><p>${message("创建时间")}:</p></dt>
						<dd class="date-wrap">
							<input id="startTime" name="startTime" class="text Wdate" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startTime:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="endTime" class="text Wdate" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',endTime:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
					</dl>
	        	</div>        
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	    	<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
	</form>
</body>
</html>