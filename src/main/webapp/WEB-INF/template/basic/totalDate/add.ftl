<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("添加经营组织")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
		
	$inputForm = $("#inputForm");
		
		// 表单验证
	$inputForm.validate({
		rules: {
			startDate: "required",	
			endDate: "required",	
		} ,
		submitHandler:function(form){
			return false;
		}
	});
		
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'edit.jhtml?id='+resultMsg.objx;
			})
	    }
	 });
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?multi=1',
		callback:function(rows){
            if(rows.length>0){
                var row = rows[0];
                $("input.saleOrgId").val(row.id);
                $("input.saleOrgName").val(row.name);
            }
	    }
	});
});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增组织")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<input  type="hidden" name="isTop" value="false"/>
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("单据编号")}:
				</th>
				<td></td>
				<th>
					${message("机构")}:
				</th>
				<td>
					<span class="search" style="position:relative">
						<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value=""/>
						<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectSaleOrg" />
					</span>
				</td>
				<th>
					${message("SBU")}:
				</th>
				<td>
					<select name="sbuId" class="text" id="sbuId">
						<option value>请选择</option>
						[#list storeMemberSbuList as storeMemberSbu]
							<option value="${storeMemberSbu.sbu.id}" [#if storeMemberSbu.isDefault] selected="selected" [/#if]>${storeMemberSbu.sbu.name}</option>
						[/#list]	
					</select>
				</td>
				<th>
					${message("经营组织")}:
				</th>
				<td>
					<select name="organizationId" class="text">
						<option value>请选择</option>
						[#list storeMemberOrganizationList as storeMemberOrganization]
							<option value="${storeMemberOrganization.organization.id}" [#if storeMemberOrganization.isDefault] selected="selected" [/#if]>${storeMemberOrganization.organization.name}</option>
						[/#list]	
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("是否启用")}:
				</th>
				<td>
					<select name="isEnabled" class="text">
							<option value=0>否</option>
							<option value=1 selected="selected">是</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("开始日期")}:
				</th>
	   			<td>
	   				<input id="startDate" name="startDate" class="text Wdate" value="${startDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endDate\')}'});" type="text" btn-fun="clear"/>
	   			</td>
	   			<th>
					<span class="requiredField">*</span>${message("结束日期")}:
				</th>
	   			<td>
	   				<input id="endDate" name="endDate" class="text Wdate" value="${endDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',endDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startDate\')}'});" type="text" btn-fun="clear"/>
	   			</td>
	   			<th>
					<span class="requiredField">*</span>${message("有效开始日期")}:
				</th>
	   			<td>
	   				<input id="effectiveStartDate" name="effectiveStartDate" class="text Wdate" value="${startDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endDate\')}'});" type="text" btn-fun="clear"/>
	   			</td>
			</tr>
			<tr>
			    <th>
					<span class="requiredField">*</span>${message("有效结束日期")}:
				</th>
	   			<td>
	   				<input id="effectiveEndDate" name="effectiveEndDate" class="text Wdate" value="${endDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',endDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startDate\')}'});" type="text" btn-fun="clear"/>
	   			</td>
				<th>
					${message("类型")}:
				</th>
				<td>
					<select id="totalDateTypeId" name="totalDateTypeId" class="text totalDateTypeId">
						[#list totalDateTypeList as totalDateTypes]
								<option value="${totalDateTypes.id}" selected="selected">${totalDateTypes.value}</option>
						[/#list]
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
		            <textarea class="text" name="remark"></textarea>
		        </td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>