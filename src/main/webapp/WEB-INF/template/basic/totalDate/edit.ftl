<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑岗位")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript">
	$().ready(function() {
			
		$inputForm = $("#inputForm");
			
		// 表单验证
		$inputForm.validate({
			rules: {
				startDate: "required",	
				endDate: "required"
			} ,
			submitHandler:function(form){
				return false;
			}
		});
			
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= '/basic/totalDate/edit.jhtml?id='+resultMsg.objx;
				});
			}
		 });	
		
		//查询机构
		$("#selectSaleOrg").bindQueryBtn({
			type:'saleOrg',
			title:'${message("查询机构")}',
			url:'/basic/saleOrg/select_saleOrg.jhtml?multi=1',
			callback:function(rows){
	            if(rows.length>0){
	                var row = rows[0];
	                $("input.saleOrgId").val(row.id);
	                $("input.saleOrgName").val(row.name);
	            }
		    }
		});
	});
	
	//失效
	function invalid(e){
		var	content = '您确定要失效吗？';
		$.message_confirm(content,function(){
			Mask();
			ajaxSubmit(e,{
				 url: 'invalid.jhtml',
				 method: 'post',
				 data: {id:${totalDate.id}},
				 failCallback:function(resultMsg){				
					// 访问地址失败，或发生异常没有正常返回					
					messageAlert(resultMsg);
					UnMask();
				 },
				 callback:function(resultMsg){
					location.reload(true);
				 }
			})
		});
	}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看经营组织")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
        <input type="hidden" name="id" value="${totalDate.id}" />
	<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("单据编号")}:
				</th>
				<td>
					${totalDate.sn}
				</td>
				<th>
					${message("机构")}:
				</th>
				<td>
					<span class="search" style="position:relative">
						<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${totalDate.saleOrg.id}"/>
						<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${totalDate.saleOrg.name}" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectSaleOrg" />
					</span>
				</td>
				<th>
					${message("SBU")}:
				</th>
				<td>
					<select name="sbuId" class="text" id="sbuId">
						<option value>请选择</option>
						[#list storeMemberSbuList as storeMemberSbu]
							<option value="${storeMemberSbu.sbu.id}" [#if totalDate.sbu.id==storeMemberSbu.sbu.id] selected [/#if]>${storeMemberSbu.sbu.name}</option>
						[/#list]	
					</select>
				</td>
				<th>
					${message("经营组织")}:
				</th>
				<td>
					<select name="organizationId" class="text" id="organizationId">
						<option value>请选择</option>
						[#list storeMemberOrganizationList as storeMemberOrganization]
							<option value="${storeMemberOrganization.organization.id}" [#if totalDate.organization.id==storeMemberOrganization.organization.id] selected [/#if]>${storeMemberOrganization.organization.name}</option>
						[/#list]	
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("是否启用")}:
				</th>
				<td>
					<select name="isEnabled" class="text">
						<option value=0 [#if totalDate.isEnabled == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if totalDate.isEnabled == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("开始日期")}:
				</th>
	   			<td>
	   				<input id="startDate" name="startDate" class="text Wdate" value="${totalDate.startDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endDate\')}'});" type="text" btn-fun="clear"/>
	   			</td>
	   			<th>
					<span class="requiredField">*</span>${message("结束日期")}:
				</th>
	   			<td>
	   				<input id="endDate" name="endDate" class="text Wdate" value="${totalDate.endDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',endDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startDate\')}'});" type="text" btn-fun="clear"/>
	   			</td>
	   			<th>
					<span class="requiredField">*</span>${message("有效开始日期")}:
				</th>
	   			<td>
	   				<input id="effectiveStartDate" name="effectiveStartDate" class="text Wdate" value="${totalDate.effectiveStartDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endDate\')}'});" type="text" btn-fun="clear"/>
	   			</td>
				
			</tr>
			<tr>
			     <th>
					<span class="requiredField">*</span>${message("有效结束日期")}:
				</th>
	   			<td>
	   				<input id="effectiveEndDate" name="effectiveEndDate" class="text Wdate" value="${totalDate.effectiveEndDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',endDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startDate\')}'});" type="text" btn-fun="clear"/>
	   			</td>
				<th>
					${message("创建人")}:
				</th>
				<td>
					<span>
						${totalDate.bCreater}
					</span>
				</td>
				<th>
					${message("创建时间")}:
				</th>
				<td>
					<samp>
						${totalDate.createDate?string("yyyy-MM-dd")}
					</samp>
				</td>
				<th>
					${message("最后修改人")}:
				</th>
				<td>
					<span>
						${totalDate.bModifier}
					</span>
				</td>
				
			</tr>
			<tr>
				<th>
					${message("最后修改时间")}:
				</th>
				<td>
					<span>
						${totalDate.modifyDate?string("yyyy-MM-dd")}
					</span>
				</td>
				<th>
					${message("类型")}:
				</th>
				<td>
					<select id="totalDateTypeId" name="totalDateTypeId" class="text totalDateTypeId">
						<option value=""></option>
						[#list totalDateTypeList as totalDateTypes]
							<option value="${totalDateTypes.id}" [#if totalDate.totalDateType.id=totalDateTypes.id]selected[/#if]>${totalDateTypes.value}</option>
						[/#list]
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
		            <textarea class="text"  name="remark" >${totalDate.remark}</textarea>
		        </td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			<a href="add.jhtml" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			[#if (totalDate.isEnabled)?? && totalDate.isEnabled]
			 	<a href="javascript:void(0);" class="button cancleButton" onclick="invalid(this)">失效</a>
			[/#if]
			<!--<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" /> -->
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>