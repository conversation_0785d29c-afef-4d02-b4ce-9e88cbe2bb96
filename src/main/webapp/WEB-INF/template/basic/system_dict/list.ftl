<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("系统词汇")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript">
function add(){
	parent.change_tab(0,'add.jhtml');
}
$().ready(function() {
	var cols = [
		{ title:'${message("词汇名")}', name:'value' ,align:'left',renderer:function(val,item,rowIndex){
			if(item.parent != null){
				return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'view.jhtml?id='+item.id+'\')" class="red" style="margin-left:20px" data-parent="'+item.parent+'">'+val+'</a>';	
			}else{
				return '<a href="javascript:void(0)" class="blue" style="border-bottom: solid 1px #4489ce;" onclick="getChildren(this)" data-id="'+item.id+'" data-rowIndex ="'+rowIndex+'" data-status="">'+val+'</a>';
			}
		}},
		{ title:'${message("备注")}', name:'remark' ,align:'center',renderer:function(val,item,rowIndex){
			if(item.flag == 1 && item.parent!= null){
				return val+'(系统词汇)';	
			}else{
				return val;
			}
		}},
		{ title:'${message("词汇编码")}', name:'code' ,align:'center',renderer:function(val,item,rowIndex){
			if(item.parent == null){
				return val;	
			}else{
				return '';
			}
		}},
		{ title:'${message("词汇下级编码")}', name:'lowerCode' ,align:'center',renderer:function(val,item,rowIndex){
				if(item.parent == null){
					return '';
				}else{
					return val;
				}
			}},
		{ title:'${message("是否启用")}', name:'isEnabled' ,align:'center',renderer:function(val){
			if(val == true){
				return '<span class="trueIcon">&nbsp;</span>';
			}else{
				return '<span class="falseIcon">&nbsp;</span>';
			}
		}},	
		{ title:'${message("创建日期")}', name:'createDate' ,align:'center' },
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
		multiSelect:false,
        cols: cols,
        fullWidthRows:true,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ],
        checkDisabled:function(item,rowIndex){
        	if(item.parent!=undefined){
        		return true;
        	}
        }
    });
});
function getChildren(e){
	var status = $(e).attr("data-status");
	var id = $(e).attr("data-id");
	var rowIndex = $(e).closest("tr").index();
	if(status==""){
		rowIndex = parseInt(rowIndex) + 1;
		//console.log(rowIndex)
		ajaxSubmit($(id),{
			method:'post',
			url:'getChildren.jhtml?id='+id,
			callback: function(data) {
				$mmGrid.addRow(data.objx,rowIndex);
				$("tr").removeClass("even");
				$("tr:nth-child(2n)").addClass("even");
			}
		})
		$(e).attr("data-status","1");		
	}else if(status==1){
		$(".mmGrid").find("a[data-parent="+id+"]").parent().parent().parent().hide();
		$(e).attr("data-status","0");
		$("tr").removeClass("even");
		$("body").find("tr:visible:even").addClass("even");
	}else{
		$(".mmGrid").find("a[data-parent="+id+"]").parent().parent().parent().show();
		$(e).attr("data-status","1");
		$("tr").removeClass("even");
		$("body").find("tr:visible:even").addClass("even");
	}
}
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
			    	<dl>
			    		<dt><p>${message("词汇编码")}：</p></dt>
			    		<dd>
							<input class="text" maxlength="200" type="text" name="code"  btn-fun="clear">
			    		</dd>
			    	</dl>
			    	<dl>
			    		<dt><p>${message("词汇名")}：</p></dt>
			    		<dd>
							<input class="text" maxlength="200" type="text" name="value"  btn-fun="clear">
			    		</dd>
			    	</dl>
		        </div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>