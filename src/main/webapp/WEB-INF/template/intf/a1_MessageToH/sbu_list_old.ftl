<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("库存日志")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />	
<script type="text/javascript">
$().ready(function() {
//查询仓库
	$("#selectWarehouse").bindQueryBtn({
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml'
	});
//查询产品
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml'
	});
	
	var companyInfoId = "${companyInfoId}";
	 //0:eas收款单推送接口,1:eas出库单接收接口,2:tcl库存获取接口,3：tcl发货单状态获取接口
	var cols = [
		{ title:'${message("单据编号")}', name:'field1' ,width:120,align:'center',renderer:function(val,item,rowIndex){
			return '<a href="javascript:;" onClick="check_view(\'/b2b/shipping/view.jhtml?id='+item.shipping_id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("单据类型")}', name:'type' ,align:'center', renderer:function(val,item,rowIndex){
			if(item.type == 0){
				return '发货单';
			}else if (item.type == 5) {
				return '客户';
			}else if (item.type == 9) {
				return '退货单';
			}else if (item.type == 20) {
				return '政策无发票';
			}else if (item.type == 21) {
				return '政策有发票';
			}else if (item.type == 22) {
				return '计划提报';
			}else if (item.type == 0) {
				
			}
		}},
		{ title:'${message("状态")}', name:'h_result', width:50 ,align:'center', renderer:function(val,item,rowIndex){
			if(item.h_result==1){
				return '失败';
			}else{
				return '成功';
			}
		}},
		{ title:'${message("异常信息")}', name:'h_result_msg', width:200 ,align:'center' },
		{ title:'${message("推送信息")}', name:'data', width:680 ,align:'center' },
		{ title:'${message("创建日期")}', width:120, name:'create_date' ,align:'center' },
		{ title:'${message("操作")}', align:'center', renderer:function(val,item,rowIndex,obj){
			if(item.h_result==1 && item.type == 0){
				return '<a href="javascript:; " class="btn-delete"  onclick="reexcIntf('+item.id+')">重推</a>';	
			}else{
				return '-';
			}
		}}
	];

	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad: true,
        url: 'sbu_list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
});	  

function reexcIntf(ids){
/* 	var data =$mmGrid.serializeSelectedIds();
	if(data.length==0){
		$.message_alert("${message("请选择需要重推的单据")}");
		return false;
	} */
	ajaxSubmit("",{
		url:"reIntf.jhtml?ids="+ids,
		method:"post",
		isConfirm:true,
		confirmText:"您确定要再次执行此接口吗？",
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				$mmGrid.load();
			})
		}
	
	});
}
function check_view(url){
	var w = $(window).width();
	var h = $(window).height();
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
		title:'${message("查看发货信息")}',
		width:w,
		height:h,
		content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
		ok: null,
		cancel: null,
		onOk: function() {
		
		}
	});
	$dialog.find(".dialogContent").css("height",h+"px");
	$dialog.css("top",0+"px").css("max-height",h+"px");
	

}
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
		        	<dl>
						<dt><p>${message("单据编号")}:</p></dt>
				    	<dd>
				        	<input type="text" class="text"  name="sn" value ="" btn-fun="clear"/>
				     	</dd>
					</dl>	
					<dl>
						<dt><p>${message("单据类型")}:</p></dt>
				    	<dd>
				        	<select class="text" name="type">
				        		<option value="0">发货单</option>
				        	</select>
				     	</dd>
					</dl>	
					<dl>
						<dt><p>${message("状态")}:</p></dt>
				    	<dd>
				        	<select class="text" name="hResult">
				        		<option value="1">失败</option>
				        		<option value="2">成功</option>
				        	</select>
				     	</dd>
					</dl>	
					<dl>
	        			<dt><p>${message("16015")}:</p></dt>
	        			<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
        			</dl>
		        </div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	   		<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
	</form>
</body>
</html>