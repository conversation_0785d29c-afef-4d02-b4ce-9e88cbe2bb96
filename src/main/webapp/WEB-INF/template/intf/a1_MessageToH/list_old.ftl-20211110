<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("库存日志")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />	
<script type="text/javascript">
$().ready(function() {
//查询仓库
	$("#selectWarehouse").bindQueryBtn({
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml'
	});
//查询产品
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml'
	});
	
	var companyInfoId = "${companyInfoId}";
	 //0:eas收款单推送接口,1:eas出库单接收接口,2:tcl库存获取接口,3：tcl发货单状态获取接口
	var cols = [
		{ title:'${message("单据编号")}', name:'field1', width:120 ,align:'center' },
		{ title:'${message("单据类型")}', name:'type' ,align:'center', renderer:function(val,item,rowIndex){
			if(item.type == 0){
				return '发货单';
			}else if (item.type == 1) {
                return '移库';
            }else if (item.type == 5) {
				return '客户';
			}else if (item.type == 9) {
				return '退货单';
			}else if (item.type == 20) {
				return '政策无发票';
			}else if (item.type == 21) {
				return '政策有发票';
			}else if (item.type == 22) {
				return '计划提报';
			}else if (item.type == 0) {
				
			}else if (item.type == 95){
			    return 'TGS';
            }else if (item.type == 12){
                return 'UFU(优工)';
            }
		}},
		{ title:'${message("状态")}', name:'h_result', width:50 ,align:'center', renderer:function(val,item,rowIndex){
			if(item.h_result==1){
				return '失败';
			}else if(item.h_result==2){
				return '成功';
			}else if(item.h_result==3){
				return '处理中';
			}
		}},
		{ title:'${message("异常信息")}', name:'h_result_msg', width:200 ,align:'center' ,renderer:function(val,item,rowIndex,obj){
			if(item.h_result==1 && item.type == 1004){
				return matchUtil.ticketOrderIntfResultHandle(val);
				//return val;
			} else {
				return val;
			}
		}},
		{ title:'${message("推送信息")}', name:'data', width:680 ,align:'center' , renderer:function(val,item,rowIndex,obj){
			return htmlUtil.htmlEncode(val);
		}},
		{ title:'${message("创建日期")}', width:120, name:'create_date' ,align:'center' },
		{ title:'${message("操作")}', align:'center', renderer:function(val,item,rowIndex,obj){
				if(item.h_result==1 && item.type == 0){//发货单
					return '<a href="javascript:; " class="btn-delete"  onclick="reexcIntf('+item.id+')">重推</a>';
				}else if(item.h_result==1 && item.type == 95){//TGS
					return '<a href="javascript:; " class="btn-delete"  onclick="reexcTgs('+item.id+')">重推</a>';
				}else if(item.h_result==1 && item.type == 9){//退货单
					return '<a href="javascript:; " class="btn-delete"  onclick="reexcReturns('+item.id+')">重推</a>';
				}else if(item.h_result==1 && item.type == 12){//同步优工的发货单
					return '<a href="javascript:; " class="btn-delete"  onclick="reexcUfuIntf('+item.id+')">重推</a>';
				}else if(item.h_result==1 && item.type == 13){//同步优工的退货单
					return '<a href="javascript:; " class="btn-delete"  onclick="reexcUfuReturns('+item.id+')">重推</a>';
				}else if(item.h_result==1 && item.type == 1){//同步移库单
                    return '<a href="javascript:; " class="btn-delete"  onclick="reexcMoveLibrary('+item.id+')">重推</a>';
                }else if(item.h_result==1 && item.type==35){
               		return '<a href="javascript:; " class="btn-delete"  onclick="reexcOrderToLink5('+item.id+')">重推</a>';
                }else if(item.h_result==1 && item.type==36){
              		return '<a href="javascript:; " class="btn-delete"  onclick="reexcShippingToLink5('+item.id+')">重推</a>';
                }else{
				return '-';
			}
		}}
	];

	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad: true,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
});	  

function reexcIntf(ids){
    /* 	var data =$mmGrid.serializeSelectedIds();
        if(data.length==0){
            $.message_alert("${message("请选择需要重推的单据")}");
		return false;
	} */
    ajaxSubmit("",{
        url:"reIntf.jhtml?ids="+ids,
        method:"post",
        isConfirm:true,
        confirmText:"您确定要再次执行此接口吗？",
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                $mmGrid.load();
            })
        }

    });
}

function reexcReturns(ids){
    /* 	var data =$mmGrid.serializeSelectedIds();
        if(data.length==0){
            $.message_alert("${message("请选择需要重推的单据")}");
		return false;
	} */
    ajaxSubmit("",{
        url:"reReturns.jhtml?ids="+ids,
        method:"post",
        isConfirm:true,
        confirmText:"您确定要再次执行此接口吗？",
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                $mmGrid.load();
            })
        }

    });
}

function reexcTgs(ids){
    /* 	var data =$mmGrid.serializeSelectedIds();
        if(data.length==0){
            $.message_alert("${message("请选择需要重推的单据")}");
		return false;
	} */
    ajaxSubmit("",{
        url:"reTgs.jhtml?ids="+ids,
        method:"post",
        isConfirm:true,
        confirmText:"您确定要再次执行此接口吗？",
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                $mmGrid.load();
            })
        }

    });
}

function reexcUfuIntf(ids){
    /* 	var data =$mmGrid.serializeSelectedIds();
        if(data.length==0){
            $.message_alert("${message("请选择需要重推的单据")}");
		return false;
	} */
    ajaxSubmit("",{
        url:"reUfuIntf.jhtml?ids="+ids,
        method:"post",
        isConfirm:true,
        confirmText:"您确定要再次执行此接口吗？",
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                $mmGrid.load();
            })
        }

    });
}

function reexcUfuReturns(ids){
    /* 	var data =$mmGrid.serializeSelectedIds();
        if(data.length==0){
            $.message_alert("${message("请选择需要重推的单据")}");
		return false;
	} */
    ajaxSubmit("",{
        url:"reReturnsUfu.jhtml?ids="+ids,
        method:"post",
        isConfirm:true,
        confirmText:"您确定要再次执行此接口吗？",
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                $mmGrid.load();
            })
        }

    });
}

function reexcMoveLibrary(ids){
    ajaxSubmit("",{
        url:"reMoveLibrary.jhtml?ids="+ids,
        method:"post",
        isConfirm:true,
        confirmText:"您确定要再次执行此接口吗？",
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                $mmGrid.load();
            })
        }

    });
}

function reexcShippingToLink5(ids){
    ajaxSubmit("",{
        url:"reexcShippingToLink5.jhtml?ids="+ids,
        method:"post",
        isConfirm:true,
        confirmText:"您确定要再次执行此接口吗？",
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                $mmGrid.load();
            })
        }

    });
}



function reexcOrderToLink5(ids){
    ajaxSubmit("",{
        url:"reexcOrderToLink5.jhtml?ids="+ids,
        method:"post",
        isConfirm:true,
        confirmText:"您确定要再次执行此接口吗？",
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                $mmGrid.load();
            })
        }

    });
}


var matchUtil = {
	"ticketOrderIntfResultHandle" : resultMsg=>{
		let regex = /(?<=编码为【)(.+?)(?=】的产品查询失败)/g;
		let code = resultMsg.match(regex);
		let a = "<a href='javascript:;' onclick='gotoProductView(\""+code+"\")'>"+code+"</a>"
		return resultMsg.replace(regex,a)
	}
}
var htmlUtil = {
	htmlEncode:function (html){
		//1.动态创建一个容器标签元素，如DIV
		let temp = document.createElement ("div");
		//2.然后将要转换的字符串设置为这个元素的innerText或者textContent
		(temp.textContent != undefined ) ? (temp.textContent = html) : (temp.innerText = html);
		//3.最后返回这个元素的innerHTML，即得到经过HTML编码转换的字符串了
		let output = temp.innerHTML;
		temp = null;
		return output;
	},
}
var gotoProductView = vonderCode=>{
	$.ajax({
		url:"/product/product/list_data.jhtml",
		data:{
			"vonderCode":vonderCode
		},
		type:"POST",
		success:result=>{
			let content = JSON.parse(result.content);
			let productList = content.content;
			for(let i = 0;i<productList.length;i++){
				let product = productList[i];
				let code = product.vonder_code;
				if(vonderCode==code){
					let id = product.id;
					let a = $("<a/>");
					a.attr("data-src", "/product/product/edit/b.jhtml?type=0&id="+id);
					a.attr("data-name", "产品资料");
					top.open_new_tab(a);
					break;
				}
			}
		}
	})
}

</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
		        	<dl>
						<dt><p>${message("单据编号")}:</p></dt>
				    	<dd>
				        	<input type="text" class="text"  name="sn" value ="" btn-fun="clear"/>
				     	</dd>
					</dl>	
					<dl>
						<dt><p>${message("单据类型")}:</p></dt>
				    	<dd>
				        	<select class="text" name="type">
				        		<option value="0">发货单</option>
                                <option value="1">移库单</option>
				        		<option value="5">客户</option>
				        		<option value="9">退货单</option>
				        		<option value="20">政策无发票</option>
				        		<option value="21">政策有发票</option>
				        		<option value="22">计划提报</option>
                                <option value="95">TGS</option>
								<option value="12">UFU(优工)发货单</option>
								<option value="13">UFU(优工)退货单</option>
								<option value="1000">Link5产品同步</option>
								<option value="1001">Link5产品分类同步</option>
								<option value="1002">Link5客户同步</option>
								<option value="1003">Link5门店同步</option>
								<option value="1004">Link5订货单同步</option>
								<option value="35">Link5发货单同步</option>
								<option value="36">Link5实际发货同步</option>
				        	</select>
				     	</dd>
					</dl>	
					<dl>
						<dt><p>${message("状态")}:</p></dt>
				    	<dd>
				        	<select class="text" name="hResult">
				        		<option value="1">失败</option>
				        		<option value="2">成功</option>
				        		<option value="3">处理中</option>
				        	</select>
				     	</dd>
					</dl>	
					<dl>
	        			<dt><p>${message("16015")}:</p></dt>
	        			<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
        			</dl>
		        </div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	   		<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
	</form>
</body>
</html>