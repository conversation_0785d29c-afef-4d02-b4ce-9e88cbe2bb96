<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>
</head>
<style>
.erro{
	width: 50%;
	position: absolute;
	top: 100px;
	left: 25%;
	text-align: center;
}
.erro p{margin-top:15px;font-size:24px;margin-bottom:15px; }
</style>
<body>
<script type="text/javascript">
function open_win(){
		var error_content = document.getElementById("error_content").innerHTML;
	    var newWim=window.open("","_blank");
        newWim.document.body.innerHTML=error_content
                              +newWim.document.body.innerHTML;
}

</script>
<div class="erro">
	<img src="/resources/images/ico-errow.png" />
    <p>对不起，出错了！（${errorMsgInfo.rmid}）&nbsp;</p>
    <p>[#if errorMsgInfo.time??]${errorMsgInfo.time}[/#if]</p>
	<div style="margin-top:20px;text-align: center; font-size:15px">${errorMsgInfo.content}</div>
	<p style="font-size: 15px; width:100%">
		[#if errorMsgInfo.objx??]<a href="javascript:;" onclick="open_win(); return false;">查看详情</a>&emsp;&emsp;[/#if]
	</p>
	<div style="font-size: 15px; width:100%">
		<input type="button" class="backButton button" value="返回上一页" onclick="window.history.back(); return false;" style="float:none"/>
	</div>
</div>
<div id="error_content" style="display:none">
${errorMsgInfo.rmid}<br/>
[#if errorMsgInfo.time??]${errorMsgInfo.time}<br/>[/#if]
${errorMsgInfo.objx?replace("at ","<br/>&emsp;&emsp;at ")}
</div>
</body>
</html>
