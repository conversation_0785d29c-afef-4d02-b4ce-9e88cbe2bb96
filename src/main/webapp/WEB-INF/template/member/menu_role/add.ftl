<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("添加角色")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $selectAll = $("#inputForm .selectAll");
	$(".authorities").each(function(){
		if($(this).next("tr").find("label").length>0){
		
		}else{
			$(this).css("display","none");
		}
	});
	// 表单验证
	$inputForm.validate({
		rules: {
			name: "required",
			menuId: "required"
		}
	});
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'edit.jhtml?id='+resultMsg.objx;
			})
	    }
	 });
	
});

function selectAll(e){
	var $this = $(e);
		var $thisCheckbox = $this.closest("tr").find(":checkbox");
		if ($thisCheckbox.filter(":checked").size() > 0) {
			$thisCheckbox.prop("checked", false);
		} else {
			$thisCheckbox.prop("checked", true);
		}
		return false;
}

function cancelSelect(e,sClass){
	if($(e).prop("checked")){
		$("."+sClass).prop("checked",true);
	}	
	if($(".label_"+sClass).find(":checkbox").filter(":checked").size() == 0){
		$("."+sClass).prop("checked",false);
	}
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增用户角色")}
	</div>
    <form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
        <table class="input input-edit">
			<tr class="border-L1">
				<th>
					<span class="requiredField">*</span>${message("用户角色")}:
				</th>
				<td>
					<input type="text" name="name" class="text " maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("角色描述")}:
				</th>
				<td>
					<input type="text" name="description" class="text " maxlength="200" btn-fun="clear" />
				</td>
			</tr>
		</table>
		<table class="input input-classify" style="width:864px;margin-top:20px">
			 [#list menus as menu]
						[#if menu.superId == null]
						[#assign count=0]
				        [#list menus as menu_sub]
				            [#if menu_sub.superId==menu]
				                	[#assign count=count+1]
				            [/#if]
				    	[/#list]
				    	[#if count > 0]
						<tr class="authorities">
	                        <th>
	                        	<input type="hidden" name="menuId" value="${menu.id}"/><b>${menu.menuName}</b>：
	                        </th>
	                    </tr>
	                    <tr>
	                        <td>
	                        	<table class="input secondTable input-edit" style="width:100% !important;" >
	                        	[#assign count_1=0]
	                        	[#list menus as cMenu]
									[#if menu.id = cMenu.superId.id]
										<tr>
										[#assign count_1=count_1+1]
											<th>
												<label class="second label_${menu.id}">
													<input type="checkbox" name="menuId" value="${cMenu.id}" style="opacity:0;width:0;height:0;"  class="${cMenu.id}"/>
													<a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">${cMenu.menuName}</a>：
												</label>
											</th>
											<td>
												[#list menus as ccMenu]
													[#if cMenu == ccMenu.superId]
														<label class="third label_${cMenu.id}">
															<input type="checkbox" onchange="cancelSelect(this,${cMenu.id})"  name="menuId" value="${ccMenu.id}" class="${ccMenu.id}"/>${ccMenu.menuCode}
														</label>
													[/#if]
												[/#list]
											</td>
										</tr>
										
									[/#if]
								[/#list]
								
								</table>
	                        </td>
	                    </tr>
						[/#if]
						[/#if]
					[/#list]
			</table>
			<table class="input input-classify" style="width:864px;margin-top:20px">
				<tr><th><b>首页权限控制:</b></th></tr>
				<tr>
					<td>
						<table class="input secondTable input-edit" style="width:100% !important;" >
							<tr>
								<th>
									<label class="second">
										<a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">按钮权限控制左：</a>
									</label>
								</th>
								<td>
									[#list visualReport as vr]
										[#if vr.category == "A"]
										<label class="third">
											<input type="checkbox"  name="visualReport" value="${vr.id}" class=""/>
											${vr.name}
										</label>
										[/#if]
									[/#list]
								</td>
								
							</tr>
							<tr>
								<th>
									<label class="second">
										<a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">按钮权限控制右：</a>
									</label>
								</th>
								<td>
									[#list visualReport as vr]
										[#if vr.category == "B"]
										<label class="third">
											<input type="checkbox"  name="visualReport" value="${vr.id}" class=""/>
											${vr.name}
										</label>
										[/#if]
									[/#list]
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
            <table class="input input-classify" style="width:864px;margin-top:20px">
                <tr><th>移动端权限控制</th></tr>
                <tr>
                    <td>
                        <table class="input secondTable input-edit" style="width:100% !important;" >
                            [#list appMenus as appMenu0]
                                [#if (appMenu0.superId.id == null)]
                                <tr>
                                    <th>
                                        <label class="second">
                                            <a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">${appMenu0.menuName}：</a>
                                        </label>
                                    </th>

                                    <td>
								[#list appMenus as appMenu1]
								[#if (appMenu1.superId.id == appMenu0.id)]
								<label class="first">
                                    <input type="checkbox"  name="appMenuId" value="${appMenu1.id}" />
									${appMenu1.menuName}
                                </label>
								[/#if]
								[/#list]
                                    </td>
                                </tr>
								[/#if]
							[/#list]

                        </table>
                    </td>
                </tr>
            </table>
		</div>
      	 <div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
    	</form>
    </div>
</body>
</html>