<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("角色列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">

function edit(id){
	parent.change_tab(0,'edit.jhtml?id='+id);
}
function add(){
	parent.change_tab(0,'add.jhtml');
}
$().ready(function() {

	var cols = [
		{ title:'${message("用户角色")}', name:'name' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
		}},
		{ title:'${message("是否内置")}', name:'isSystem' ,align:'center',renderer:function(val){
		if(val == true){
			return '<span class="trueIcon">&nbsp;</span>';
		}else{
			return '<span class="falseIcon">&nbsp;</span>';
		}
		}},
		{ title:'${message("角色描述")}', name:'description' ,align:'center' },
		{ title:'${message("创建日期")}', name:'createDate' ,align:'center' },
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
        cols: cols,
        fullWidthRows:true,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
});
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
			    	<dl>
			    		<dt><p>${message("用户角色")}：</p></dt>
			    		<dd>
							<input class="text" maxlength="200" type="text" name="name"  btn-fun="clear">
			    		</dd>
			    	</dl>
			    	<dl>
						<dt><p>${message("12014")}：</p></dt>
						<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
					</dl>
		        </div>		        
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	    	<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
	</form>
</body>
</html>