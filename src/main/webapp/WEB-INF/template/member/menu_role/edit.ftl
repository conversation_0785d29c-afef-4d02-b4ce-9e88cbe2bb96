<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑角色")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $selectAll = $("#inputForm .selectAll");
	$selectAll.click(function() {
		var $this = $(this);
		var $thisCheckbox = $this.closest("tr").find(":checkbox");
		if ($thisCheckbox.filter(":checked").size() > 0) {
			$thisCheckbox.prop("checked", false);
		} else {
			$thisCheckbox.prop("checked", true);
		}
		return false;
	});
	$(".authorities").each(function(){
		if($(this).next("tr").find("label").length>0){
			
		}else{
			$(this).css("display","none");
		}
	});
	// 表单验证
	$inputForm.validate({
		rules: {
			name: "required",
			authorities: "required"
		}
	});
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			});
	    }
	 });
});
function selectAll(e){
	var $this = $(e);
		var $thisCheckbox = $this.closest("tr").find(":checkbox");
		if ($thisCheckbox.filter(":checked").size() > 0) {
			$thisCheckbox.prop("checked", false);
		} else {
			$thisCheckbox.prop("checked", true);
		}
		return false;
}
function cancelSelect(e,sClass){
	if($(e).prop("checked")){
		$("."+sClass).prop("checked",true);
	}	
	if($(".label_"+sClass).find(":checkbox").filter(":checked").size() == 0){
		$("."+sClass).prop("checked",false);
	}
}
</script>

</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看用户角色")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${pcRole.id}" />
		<div class="tabContent">
		<table class="input input-edit">
			<tr class="border-L1">
				<th>
					<span class="requiredField">*</span>${message("用户角色")}:
				</th>
				<td>
					<input type="text" name="name" class="text " value="${pcRole.name}" maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("角色描述")}:
				</th>
				<td>
					<input type="text" name="description" class="text " value="${pcRole.description}" maxlength="200" btn-fun="clear" />
				</td>
			</tr>
		</table>
		<table class="input input-classify" style="width:864px;margin-top:20px">
			 [#list menus as menu]
						[#if menu.superId == null]
						[#assign count=0]
				        [#list menus as menu_sub]
				            [#if menu_sub.superId==menu]
				                	[#assign count=count+1]
				            [/#if]
				    	[/#list]
				    	[#if count > 0]
						<tr class="authorities">
	                        <th>
	                        	<input type="hidden"  value="${menu.id}"/><b>${menu.menuName}</b>：
	                        </th>
	                    </tr>	
	                    <tr>
	                    	<td>
	                        	<table class="input input-edit secondTable" style="width:100% !important" >
	                        	[#assign count_1=0]
	                        	[#list menus as cMenu]
									[#if menu.id = cMenu.superId.id]
										<tr>
											<th class="cs-w">
												<label class="second label_${menu.id}">
													<input type="checkbox"  value="${cMenu.id}" style="opacity:0;width:0;height:0;"
													[#if pcRole.pcMenus?seq_contains(cMenu)] checked="checked"[/#if] class="${cMenu.id}"/>
													<a href="javascript:;" onclick="selectAll(this)" title="${message("全选此组权限")}">${cMenu.menuName}</a>：
												</label>
											</th>
											<td>
												[#list menus as ccMenu]
													[#if cMenu == ccMenu.superId]
														<label class="third label_${cMenu.id}">
															<input type="checkbox"   name="menuId"
															[#if pcRole.pcMenus?seq_contains(ccMenu)] checked="checked"[/#if]
															value="${ccMenu.id}" class="${ccMenu.id}"/>${ccMenu.menuCode}
														</label>
													[/#if]
												[/#list]
											</td>
										</tr>
									[/#if]
								[/#list]
								</table>
	                        </td>
	                    </tr>
						[/#if]
						[/#if]
					[/#list]
		
			[#if pcRole.isSystem]
				<tr>
					<td>
						<span class="tips">${message("系统内置角色不允许修改")}</span>
					</td>
				</tr>
			[/#if]
		</table>
		<table class="input input-classify" style="width:864px;margin-top:20px">
			<tr><th>首页权限控制</th></tr>
			<tr>
				<td>
					<table class="input secondTable input-edit" style="width:100% !important;" >
						<tr>
							<th>
								<label class="second">
									<a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">按钮权限控制左：</a>
								</label>
							</th>
							<td>
								[#list visualReport as vr]
								[#if vr.category == "A" && vr.type == "index"]
								<label class="third">
									<input type="checkbox"  name="visualReport" value="${vr.id}" [#if pcRole.roleVrs?seq_contains(vr)] checked="checked" [/#if]/>
									${vr.name}
								</label>
								[/#if]
								[/#list]
							</td>
						</tr>
						<tr>
							<th>
								<label class="second">
									<a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">按钮权限控制右：</a>
								</label>
							</th>
							<td>
								[#list visualReport as vr]
								[#if vr.category == "B" && vr.type == "index"]
								<label class="third">
									<input type="checkbox"  name="visualReport" value="${vr.id}" [#if pcRole.roleVrs?seq_contains(vr)] checked="checked" [/#if]/>
									${vr.name}
								</label>
								[/#if]
								[/#list]
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr><th><b>客户按钮权限控制:</b></th></tr>
				<tr>
					<td>
						<table class="input secondTable input-edit" style="width:100% !important;" >
							<tr>
								<th>
									<label class="second">
										<a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">客户按钮权限控制左：</a>
									</label>
								</th>
								<td>
									[#list visualReport as vr]
										[#if vr.category == "A" && vr.type == "store"]
										<label class="third">
											<input type="checkbox"  name="visualReport" value="${vr.id}" [#if pcRole.roleVrs?seq_contains(vr)] checked="checked" [/#if] />
											${vr.name}
										</label>
										[/#if]
									[/#list]
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr><th><b>门店按钮权限控制:</b></th></tr>
				<tr>
					<td>
						<table class="input secondTable input-edit" style="width:100% !important;" >
							<tr>
								<th>
									<label class="second">
										<a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">门店按钮权限控制左：</a>
									</label>
								</th>
								<td>
									[#list visualReport as vr]
										[#if vr.category == "A" && vr.type == "shop"]
										<label class="third">
											<input type="checkbox"  name="visualReport" value="${vr.id}" [#if pcRole.roleVrs?seq_contains(vr)] checked="checked" [/#if] />
											${vr.name}
										</label>
										[/#if]
									[/#list]
								</td>
							</tr>
						</table>
					</td>
				</tr>
		</table>

            <table class="input input-classify" style="width:864px;margin-top:20px">
                <tr><th>移动端权限控制</th></tr>
                <tr>
                    <td>
                        <table class="input secondTable input-edit" style="width:100% !important;" >
                            [#list appMenus as appMenu0]
                                [#if (appMenu0.superId.id == null)]
                                <tr>
                                <th>
                                    <label class="second">
                                        <a href="javascript:;" onclick="selectAll(this)" title="${message("选择全部")}">${appMenu0.menuName}：</a>
                                    </label>
                                </th>

                                <td>
								[#list appMenus as appMenu1]
								[#if (appMenu1.superId.id == appMenu0.id)]
								<label class="first">
                                    <input type="checkbox"  name="appMenuId" value="${appMenu1.id}" [#if appRoleMenus?seq_contains(appMenu1)] checked="checked" [/#if]/>
                                    ${appMenu1.menuName}
                                </label>
								[/#if]
								[/#list]
                                </td>
                            </tr>
                                [/#if]
                            [/#list]

                        </table>
                    </td>
                </tr>
            </table>
		</div>
		<div class="fixed-top">
			<a href="add.jhtml" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			<input type="submit" class="button sureButton" id="submit_button" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>