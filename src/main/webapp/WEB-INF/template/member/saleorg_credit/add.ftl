<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("平台授信")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<style>
.upload-list .ul-box{
margin-right:0px;
}
.upload-list {
	width:80px;
    display: inline-block;
    margin-right: 20px;
}
</style>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,2,false,e);
	countTotal();
}
function countTotal(){
	var $input = $("input.price");
	var total = 0;
	$input.each(function(){
		var $this = $(this);
		var amount = Number($this.val());
		if(isNaN(amount)){
			amount = 0;
		}
		total = accAdd(total,amount).toFixed(4);
	});
	$("#totalAmount").val(total);
	$("#totalamount").text(currency(total,true));
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $submitButton = $("#submitButton");
	var $addSaleOrgCridit = $("#addSaleOrgCridit");
	$("input[name='image']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
	// 表单验证
	$inputForm.validate({
		rules: {
			startDate: {
				required: true
			},
			endDate: {
				required: true
			}
		} ,
		submitHandler:function(form){
			return false;
		}
	});
    
	// 表单验证
	$.validator.addClassRules({
		amount: {
			required: true,
			min: 0,
			decimal: {
				integer: 12,
				fraction: ${setting.priceScale}
			}
		}
	});
	//选择机构
	$addSaleOrgCridit.click(function(){
		$addSaleOrgCridit.bindQueryBtn({
			type:'saleOrg',
			bindClick:false,
			title:'${message("查询机构")}',
			url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2&isSellSaleOrg=1&isSelect=1',
			callback:function(rows){
				if(rows.length>0){
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".saleOrgId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid.addRow(row);
						countTotal();
					}
				}
			}
		});	
	})
	var itemIndex = 0;
	var cols = [	
       {title:'${message("平台")}', align:'center',width:160 , renderer: function(val,item,rowIndex){
	     return '<input type="hidden" name="saleOrgCreditItems['+itemIndex+'].saleOrg.id" class="saleOrgId_'+item.id+' saleOrgId" value="'+item.id+'">'+item.name;
        }},
		{ title:'${message("授信额度")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price amount"  name="saleOrgCreditItems['+itemIndex+'].amount" value="" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
        	return html;
		}},
		{ title:'${message("备注")}', align:'center',renderer: function(val,item,rowIndex){
			return '<input type="text" class="text" name="saleOrgCreditItems['+itemIndex+'].memo" value="'+val+'">';
		}},
		{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
			itemIndex++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteSaleOrg(this)">删除</a>';
		}},
	];
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols,
        checkCol:false,
        fullWidthRows: true,
        callback:function(){
         	countTotal();
         }
    });
	
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
	
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
});

//保存
function save(e){
    var count = $("input.saleOrgId").length;
    if(count<1){
        $.message_alert("会员价明细不能少于一条");
        return false;
    }else{
        var url = 'save.jhtml';
        var $form = $("#inputForm");
        if($form.valid()){
            var data = $form.serialize();
            var content = '您确定要保存吗？';
            $.message_confirm(content,function() {
                Mask();
                ajaxSubmit(e,{
                    url: url,
                    data:data,
                    method: "post",
                    failCallback:function(resultMsg){				// 访问地址失败，或发生异常没有正常返回
                        $.message_alert(resultMsg.rmid+"<br/>"+resultMsg.content);
                        UnMask();
                    },
                    callback:function(resultMsg){
                        location.href= 'view.jhtml?flag=1&id='+resultMsg.objx;
                    }
                })
            });
        }
    }

}

function deleteSaleOrg(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
		countTotal();
	})
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增授信申请")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
			<tr>
				<th>
					${message("单号")}:
				</th>
				<td>
				</td>
				<th>
					<span class="requiredField">*</span>${message("总授信额度")}:
				</th>
				<td>
				   <span id="totalamount" class="red">${currency(0.00, true)}</span>
				   <input type="hidden"  name="totalAmount" id="totalAmount">
				</td>
				<th>
					${message("制单人")}:
				</th>
				<td>
				</td>
				<th>
					${message("已授信额度")}:
				</th>
				<td></td>
			</tr>
			<tr>
				<th>${message("充值状态")}:</th>
				<td>
					<b class="red">${message("未生效")}</b>
				</td>
				
				<th>
					${message("流程状态")}:
				</th>
				<td>
					<b class="blue">${message("未启动")}</b>
				</td>
				<th>
					<span class="requiredField">*</span>${message("开始日期")}:
				</th>
				<td class="date-wrap">
					<input id="startTime" name="startDate" class="text" value="${startDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("截止日期")}:
				</th>
				<td class="date-wrap">
					<input id="endTime" name="endDate" class="text" value="${endDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
				</td>
			</tr>
			<tr>
				<th>
		              <span class="requiredField">*</span>${message("sbu")}:
            	  </th>
           		<td>
            		<select id="sbu" name="sbuId" class="text">
            		    <option value="">${message("请选择")}</option>
						[#list sbuList as sbu]
						<option value="${sbu.id}"[#if sbuIds==sbu.id]selected[/#if]>${sbu.name}</option>
						[/#list]
					</select>
            	</td>
				<th>${message("单据状态")}:</th>
				<td></td>
				<th>${message("制单时间")}:</th>
				<td></td>
				<th>
					<span class="requiredField">*</span>${message("经营组织")}:
				</th>
				<td>
					<select name="organizationId" id="organizationId" class="text organizationId">
					    <option value="">${message("请选择")}</option>
						[#list organizationList as organization]
							<option value="${organization.id}">${organization.name}</option>
						[/#list]
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("平台授信类型")}:
				</th>
				<td>
					<select id="saleOrgCreditTypeId" name="saleOrgCreditTypeId" class="text saleOrgCreditTypeId">
						[#list saleOrgCreditTypeList as saleOrgCreditType]
							<option value="${saleOrgCreditType.id}">${saleOrgCreditType.value}</option>
						[/#list]	
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
					<textarea name="memo" class="text" id="memo"></textarea>
				</td>
			</tr>
			[#--<tr>
				<th>
					${message("附件")}:
				</th>
				<td colspan="7">
					<input type="hidden" name="image" value=""/>
					<input type="hidden" name="image2" value=""/>
					<input type="hidden" name="image3" value=""/>
				</td>
			</tr>--]
		</table>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
			 <tr class="border-L1">
				<th> ${message("平台授信明细")}:
				</th>
				<td colspan="7">
				  <a href="javascript:;" id="addSaleOrgCridit" class="button">选择机构</a>
				</td>
			</tr>
			</tr>
			    <tr class="border-L2">
            	<td colspan="8">
            		<div>
						<table id="table-m1"></table>
					</div>
            	</td>
            </tr>
			<tr class="border-L2">
				<th>${message("全链路信息")}:</th>
				<td colspan="7"></td>
		</table>
		</div>
		<div class="fixed-top">
			<input type="button" id="submit_button" class="button sureButton" onclick="save(this)" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>