<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查询银行")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
	var cols = [
		{ title:'${message("银行名称")}', name:'bank_name' ,align:'center',width:300,renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("帐号")}', name:'bank_card_no' ,width:200,align:'center' },
		{ title:'${message("经营组织")}', name:'organization_name' ,width:200,align:'center' },
		{ title:'${message("sbu")}', name:'sbu_name' ,align:'center' },
		{ title:'${message("银行代号")}', name:'bank_code' ,align:'center' },
		{ title:'${message("预留手机号码")}', name:'mobile' ,align:'center' },
		{ title:'${message("备注")}', name:'memo' ,align:'center' }
	];
	var multiSelect = false;
	[#if multi==2]
		multiSelect = true;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		rowCursorPointer:true,
		formQuery:true,
        cols: cols,
        url: 'select_bank_card_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
});
function childMethod(){
   return $mmGrid.selectedRows();
};
</script>
</head>
<body  style="min-width: 0px;">
<form id="listForm" action="" method="get">
	<input type="hidden" name="multi" value="${multi}">
	<input type="hidden" name="saleOrgId" value="${saleOrgId}">
	<input type="hidden" name="sbuId" value="${sbuId}">
	 [#if isTotalAccount?? ]
	 	<input type="hidden" name="isTotalAccount" value="${isTotalAccount?string ("true","false")}"/>
	 [/#if]
	<div class="bar">	
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
		    	<dl>
        			<dt><p>${message("帐号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="bankCardNo" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("银行名称")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="bankName" btn-fun="clear" />
        			</dd>
        		</dl>
        		
        		<dl>
        			<dt><p>${message("银行代码")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="bankCode" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("预留手机号码")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="mobile" btn-fun="clear" />
        			</dd>
        		</dl>
	        </div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">	
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
</form>
</body>
</html>