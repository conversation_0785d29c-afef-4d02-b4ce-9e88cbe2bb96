<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑银行")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
$().ready(function() {
	var $inputForm = $("#inputForm");	
	// 表单验证
	$inputForm.validate({
		rules: {
			bankName: "required",
			bankCardNo: "required",
			isInternalAccount: "required",
			isTotalAccount: "required",
		} ,
		submitHandler:function(form){
			return false;
		}
	});
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	});
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑银行")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${bankCard.id}" />
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("银行名称")}:
				</th>
				<td>
					<input type="text" name="bankName" class="text" maxlength="200" value="${bankCard.bankName}" btn-fun="clear" />
				</td>
				<th>
					${message("银行代号")}:
				</th>
				<td>
					<input type="text" name="bankCode" class="text" maxlength="200" value="${bankCard.bankCode}" btn-fun="clear" />
				</td>
				<th>
					${message("开户人")}:
				</th>
				<td>
					<input type="text" name="createy" class="text" maxlength="200" value="${bankCard.createy}" btn-fun="clear" />
				</td>
				<th>
					${message("预留手机号码")}:
				</th>
				<td>
					<input type="text" name="mobile" class="text" maxlength="200" value="${bankCard.mobile}" btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("帐号")}:
				</th>
				<td>
					<input type="text" name="bankCardNo" class="text" maxlength="200" value="${bankCard.bankCardNo}" btn-fun="clear" />
				</td>
				 <th>
					${message("银行地址")}:
				</th>
				<td>
					<input type="text" name="address" class="text" maxlength="200" value="${bankCard.address}" btn-fun="clear" />
				</td>
				<th>
					${message("经营组织")}:
				</th>
				<td>
					<select class="text" name="organizationId">
						<option value="">请选择</option>
						[#list organizations as organization]
						<option value="${organization.id}" [#if bankCard.organization.id==organization.id]selected[/#if]>${organization.name}</option>
						[/#list]
					</select>
				</td>
					<th>${message("机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${bankCard.saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${bankCard.saleOrg.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td>
			</tr>
			<tr>
				<th>
					${message("大区")}:
				</th>
				<td>
					<input type="text" name="areaName" value="${bankCard.areaName}" class="text" maxlength="200"  btn-fun="clear" />
				</td>
			    <th>
					${message("是否启用")}:
				</th>
				<td>
					<label>
					<input type="checkbox" name="isEnabled" value="true" [#if bankCard.isEnabled]checked='checked'[/#if]/>
					<input type="hidden" name="_isEnabled" value="false" />
					</label>
				</td>
                <th>
                    ${message("sbu")}:
                </th>
                <td>
                    <select id="sbuId" name="sbuId" class="text">
                    	<option value="">请选择</option>
						[#list sbus as sbu]
	                    	<option value="${sbu.sbu.id}"[#if bankCard.sbu.id==sbu.sbu.id] selected[/#if]>${sbu.sbu.name}</option>
						[/#list]
                    </select>
                </td>
                <th>
				<span class="requiredField">*</span>${message("是否内部账号")}:
	            </th>
				<td>
	            	<select name="isInternalAccount" class="text">
						<option value="true" [#if bankCard.isInternalAccount==true] selected[/#if]>是</option>
						<option value="false"[#if bankCard.isInternalAccount==false] selected[/#if]>否</option>
					</select>
					<input type="hidden"value="${bankCard.isInternalAccount}" class="text" maxlength="200"  btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("是否总账号")}:
	            </th>
				<td>
	            	<select name="isTotalAccount" class="text">
						<option value="true" [#if bankCard.isTotalAccount==true] selected[/#if]>是</option>
						<option value="false"[#if bankCard.isTotalAccount==false] selected[/#if]>否</option>
					</select>
					<input type="hidden"value="${bankCard.isTotalAccount}" class="text" maxlength="200"  btn-fun="clear" />
				</td>
                <th>
                    <span class="requiredField">*</span>${message("新账号")}:
                </th>
                <td>
                    <label>
                        <input type="checkbox" name="isNewBankCode" value="true" [#if bankCard.isNewBankCode]checked='checked'[/#if]/>
                        <input type="hidden" name="_isNewBankCode" value="false" />
                    </label>
                </td>
			</tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
					<textarea  name="memo" class="text">${bankCard.memo}</textarea>
				</td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			<a href="add.jhtml" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>