<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("银行")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
function add(){
	parent.change_tab(0,'add.jhtml');
}
$().ready(function() {

	var cols = [
		{ title:'${message("银行名称")}', name:'bank_name' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("sbu")}', name:'sbu_name' ,align:'center' },
		{ title:'${message("银行代号")}', name:'bank_code' ,align:'center' },
		{ title:'${message("帐号")}', name:'bank_card_no' ,align:'center' },
		{ title:'${message("机构")}', name:'saleOrgName' ,align:'center' },
		{ title:'${message("经营组织")}', name:'organization_name' ,align:'center' },
		{ title:'${message("预留手机号码")}', name:'mobile' ,align:'center' },
		{ title:'${message("是否启用")}', name:'is_enabled' ,align:'center', renderer:function(val){
			if(val==true){
				return '<span class="trueIcon">&nbsp;</span>';
			}else{
				return '<span class="falseIcon">&nbsp;</span>';
			}
		}},
		{ title:'${message("备注")}', name:'memo' ,align:'center' },
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });

    //查询SBU
    $("#selectSbu").bindQueryBtn({
        type:'sbu',
        title:'查询Sbu',
        url:'/basic/sbu/select_sbu.jhtml'
    });
	
});
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
        			<dt><p>${message("帐号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="bankCardNo" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("银行名称")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="bankName" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("预留手机号码")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="mobile" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
					<dt><p>${message("SBU")}:</p></dt>
					<dd>
						<span class="search" style="position:relative">
						<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" />
						<input type="text" name="sbuName" class="text sbuName" maxlength="200" onkeyup="clearSelect(this)" id="sbuName" />
						<input type="button" class="iconSearch" value="" id="selectSbu">
						</span>
					</dd>
				</dl>
			</div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>