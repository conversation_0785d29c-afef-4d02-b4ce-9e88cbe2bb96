<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("客户维护")}</title>
	<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
	<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
	<script type="text/javascript" src="/resources/js/base/request.js"></script>
	<script type="text/javascript" src="/resources/js/dynamicForm/mmGridConfiguration.js"></script>
	<script type="text/javascript" src="/resources/js/base/global.js"></script>
	<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
	<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>
	<script type="text/javascript" src="/resources/js/dynamicForm/formList.js"></script>
	<script type="text/javascript">
		function add(){
			parent.change_tab(0,'/member/store/add/${code}.jhtml');
		}
		function edit(id){
			parent.change_tab(0,'/member/store/edit/${code}.jhtml?isEdit=${isEdit}&isStore=${isStore}&id='+id);
		}
		$().ready(function() {
			//获取筛选html
			parameterList(${userId},${templateId});
			//遍历列表表头
			traverseList(${userId},${templateId},${defaultQuery},'${dTemplates.pdfPath}','${dTemplates.excelPath}');
			//获取按钮控件
			getButtonHtml(0,${templateId});
		});

	</script>
</head>
<body>
<form id="listForm" action="list.jhtml" method="get">
	<input type="hidden" name="userId" class="userId"  value="${userId}"/>
	<input type="hidden" name="templateId" class="templatesId"  value="${templateId}"/>
	<input type="hidden" name="pagesize" class="pagesize" value="${pagesize}"/>
	<div class="bar">
		<div class="buttonWrap">
			[#if role]
				<div  sytle="position:relative;">
					<a href="javascript:;" id="synchronization" onclick="synchronization()" class="iconButton">
						<span>一键同步</span>
					</a>
					<dl>
						<dd class="date-wrap">
							<input id="startTime" class="text" value=""  onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" class="text" value=""  onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
						<dl>
				</div>
			[/#if]
		</div>
		<div class="search-btn">
			<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
		</div>
		<div id="searchDiv">
			<div id="search-content" >
				<table id="search-table">

				</table>
			</div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
		<div id="body-paginator" style="text-align:left;">
			<div id="paginator"></div>
		</div>
	</div>
</form>
</body>
</html>