<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("更换地址")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	function initTypes(){
		types = {};
		[#list types as value]
			types['${value}'] = "${message('11111111'+value)}";
		[/#list]
	}
	$().ready(function() {
		
		/**初始化多选下拉框*/
		initMultipleSelect();
		
		/**初始化客户类型*/
		initTypes();
		
		var cols = [
		{ title:'${message("地区")}', name:'area_full_name' ,width:100, align:'center' },
		{ title:'${message("地址")}', name:'address' ,width:200, align:'center' },
		{ title:'${message("收货人")}', name:'consignee' ,width:100, align:'center' },
		{ title:'${message("联系电话")}', name:'mobile' ,width:100, align:'center' },
		{ title:'${message("邮编")}', name:'zip_code' ,width:100, align:'center' },
		{ title:'${message("身份证号码")}', name:'card_id' ,width:100, align:'center' },
		{ title:'${message("外部编码")}', name:'out_trade_no' ,width:100, align:'center' }
	];
	var multiSelect = false;
	[#if multi==2]
		multiSelect = true;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		rowCursorPointer:true,
		formQuery:true,
        cols: cols,
        url: 'select_store_address_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	});
	function childMethod(){
	   return $mmGrid.selectedRows();
	};
</script>
</head>
<body  style="min-width: 0px;">
<form id="listForm" action="select_store_address_data.jhtml" method="post">
	<input type="hidden" name="search" value="1">
	<input type="hidden" name="storeId" value="${storeId}">
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
	        	<dl>
		    		<dt><p>${message("地区")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="areaName" name="areaName" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("电话")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="mobile" name="mobile" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("收货人")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="consignee" name="consignee" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
	        </div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1">
		
		</table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
	</form>
</body>
</html>