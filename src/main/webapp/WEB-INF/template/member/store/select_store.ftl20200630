<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查询客户")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	function initTypes(){
		types = {};
		[#list types as value]
			types['${value}'] = "${message('11111111'+value)}";
		[/#list]
	}
	$().ready(function() {
		
		/**初始化多选下拉框*/
		initMultipleSelect();
		
		/**初始化客户类型*/
		initTypes();
		
		var cols = [
		{ title:'${message("客户名称")}', name:'name' ,width:100, align:'center'},
		{ title:'${message("客户简称")}', name:'alias' ,width:100, align:'center'},
		{ title:'${message("经销商姓名")}', name:'dealer_name' ,width:100, align:'center'},
		{ title:'${message("客户编码")}', name:'out_trade_no' ,width:100, align:'center'},
		{ title:'${message("授权编号")}', name:'grant_code' ,width:100, align:'center'},
		{ title:'${message("机构")}', name:'sale_org_name' ,width:100, align:'center'}
		[#if isSl==1]
	    ,{ title:'${message("可用余额")}', name:'store_balance' ,width:80, align:'center', renderer: function(val){
	    	return '<span class="red">'+currency(val,true)+'</span>';
	    }},
	    { title:'授信额度', name:'credit_amount' , align:'center',isLines:true,renderer:function(val){
			return '<span class="red">'+currency(val,true)+'</span>';
		}}
		[/#if]
	];
	var multiSelect = false;
	[#if multi==2]
		multiSelect = true;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		[#if multi!=2]checkByDblClickTd:true,[/#if]
		rowCursorPointer:true,
		formQuery:true,
        cols: cols,
        url: 'select_store_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	});
	function childMethod(){
	   return $mmGrid.selectedRows();
	};
</script>
</head>
<body  style="min-width: 0px;">
<form id="listForm" action="select_store_data.jhtml" method="post">
	<input type="hidden" name="search" value="1">
	<input type="hidden" name="isMember" value="${isMember}">
	<input type="hidden" name="multi" value="${multi}">
	<input type="hidden" name="type" value="[#if type!=null]${type.ordinal()}[/#if]">
	<input type="hidden" name="isSl" value="${isSl}">
	<input type="hidden" name="saleOrgId" value="${saleOrgId}">
	<input type="hidden" name="sbuId" value="${sbuId}">
	<input type="hidden" name="isSelect" value="${isSelect}">
	<input type="hidden" name="isCustomer" value="${isCustomer}">
	<input type="hidden" name="user" value="${user}"/>
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
		    	<dl>
		    		<dt><p>${message("客户名称")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="name" name="name" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("客户简称")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="alias" name="alias" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
	    			<dt><p>${message("客户编码")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="outTradeNo" name="outTradeNo" value ="" btn-fun="clear" />
		    		</dd>
	    		</dl>
	    		<dl>
	    			<dt><p>${message("授权编号")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="grantCode" name="grantCode" value ="" btn-fun="clear" />
		    		</dd>
	    		</dl>
		    	
	        </div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
	</form>
</body>
</html>