<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("岗位列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function edit(id){
	parent.change_tab(0,'edit.jhtml?id='+id+'&isCheck=${isCheck}');
}
function add(){
	parent.change_tab(0,'add.jhtml');
}
$().ready(function() {
	var $buildForm = $("#buildForm");
	var $member_id = $("#member_id");
	// 表单验证
	$buildForm.validate({
		rules: {
			memberIds: "required"
		},
		submitHandler: function(form) {
			form.submit();
		}
	});
	
	
	var cols = [
		{ title:'${message("岗位名称")}', name:'name' ,align:'center',renderer:function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
		}},
		{ title:'${message("岗位编号")}', name:'sn' ,align:'center' },
		{ title:'${message("是否启用")}', name:'is_enabled' ,align:'center',renderer:function(val){
			if(val==true){
				return '<span class="trueIcon">&nbsp;</span>';
			}else{
				return '<span class="falseIcon">&nbsp;</span>';
			}
		}},
		{ title:'${message("备注")}', name:'memo' ,align:'center' },
		{ title:'${message("创建日期")}', name:'create_date', width:120, align:'center' },
	];
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
		fullWidthRows:true,
        cols: cols,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
});

function member_import(e){
	excel_import(e,{
		title:'${message("用户导入")}',
		url:'import_excel.jhtml?memberType=0',
		template:'/resources/template/member/member.xls',
		callback:function(){
			$("#searchBtn").click();
		
		}
	});
}

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = 'to_condition_export.jhtml?memberType=0';//分页导出统计页面
	var url = 'condition_export.jhtml?memberType=0';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的用户！")}';//提示
	var url = 'selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

</script>
</head>
<body >
<div>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<!-- <div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="member_import(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
					</ul>
				</div> -->
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
		</div>
		<div class="search-btn">
			<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
		</div>
		<div id="searchDiv">
			 <div id="search-content" >
        		<dl>
    				<dt ><p>${message("岗位名称")}:</p></dt>
	    			<dd >
	    				<input type="text" class="text" id="name" name="name"  btn-fun="clear"/>
	    			</dd>
	    		</dl>
	    		<dl>
	    			<dt><p>${message("岗位编号")}:</p></dt>
	    			<dd>
	    				<input type="text" class="text" id="sn" name="sn"  btn-fun="clear"/>
	    			</dd>
	    		</dl>
	    		<dl>
							<dt><p>${message("是否启用")}:</p></dt>
							<dd>
							<select name="isEnabled" class="text">
								<option value>请选择</option>
								<option value=0>否</option>
								<option value=1 selected="selected">是</option>
							</select>
							</dd>
						</dl>
	    		<dl>
        			<dt><p>${message("16015")}:</p></dt>
        			<dd class="date-wrap">
						<input id="startTime" name="startTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
						<div class="fl">--</div>
						<input id="endTime" name="endTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
					</dd>
        		</dl>
        	</div>
		</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	    	<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
	</form>
</body>
</html>