<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("临时额度申请列表")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />	
<script type="text/javascript">
function initWfStates(){
		wfStates = {};
		[#list wfStates as wfState]
			wfStates['${wfState}'] = '${message('22222222'+wfState)}';
		[/#list]
	}
function add(){
	parent.change_tab(0,'add.jhtml?type=${type}');
}
$().ready(function() {
	
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
	
	initWfStates();
	var sr_status = {'0':'<span class="green">${message("待审核")}</span>','2':'<span class="red">${message("未通过")}</span>','1':'<span class="blue">${message("通过")}</span>'};
	var sr_type = {'0':'<span class="red">${message("未生效")}</span>','1':'<span class="blue">${message("已生效")}</span>','2':'<span class="">${message("已过期")}</span>'};
	var cols = [
		{ title:'${message("单号")}', name:'sn' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'view.jhtml?id='+item.id+'&flag=${flag}\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("平台")}', name:'sale_org_name' ,align:'center',isLines:true},
		{ title:'${message("经营组织")}', name:'organization_name' ,align:'center' },
		{ title:'${message("平台授信类型")}', name:'sale_org_credit_type_name' ,align:'center' },
		{ title:'${message("授信金额")}', name:'amount' ,align:'center',renderer:function(val){
                var num;
                if(val!=0){
                    if (val.toString().indexOf("-") != -1) {
                        var str = fmoney(val.toString().substring(1, val.length));
                        num = "-" + str;
                    } else {
                        num = fmoney(val);
                    }
                }else{
                    num = "0.00";
                }
			return '<span class="red">'+"￥"+num+'</span>';
		}},
		{ title:'${message("平台备注")}', name:'itemMemo' ,align:'center',isLines:true},
		{ title:'${message("授信总额度")}', name:'total_amount' ,align:'center',renderer:function(val){
                var num;
                if(val!=0){
                    if (val.toString().indexOf("-") != -1) {
                        var str = fmoney(val.toString().substring(1, val.length));
                        num = "-" + str;
                    } else {
                        num = fmoney(val);
                    }
                }else{
                    num = "0.00";
                }
			return '<span class="red">'+"￥"+num+'</span>';
		}},
		{ title:'${message("已授信额度")}', name:'used_amount' ,align:'center',renderer:function(val){
                var num;
                if(val!=0){
                    if (val.toString().indexOf("-") != -1) {
                        var str = fmoney(val.toString().substring(1, val.length));
                        num = "-" + str;
                    } else {
                        num = fmoney(val);
                    }
                }else{
                    num = "0.00";
                }
			return '<span class="red">'+"￥"+num+'</span>';
		}},
		{ title:'${message("充值状态")}', name:'type' ,align:'center',renderer:function(val){
			var result=sr_type[val];
			if(result!=undefined)return result;
		}},
		{ title:'${message("单据状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result="";
			if (item.doc_status == 0) result = '<span class="blue">已保存</span>';
			else if (item.doc_status==1) result = '<span class="blue">处理中</span>';
			else if (item.doc_status == 2) result = '<span class="green">已处理</span>';
			else if (item.doc_status == 3) result = '<span class="red">已关闭</span>';
			return result;
		}},
		{ title:'${message("流程状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result = wfStates[item.wf_state];
			if(result!=undefined)return result;
		}},
		{ title:'${message("授信备注")}', name:'memo' ,align:'center',isLines:true},
		{ title:'${message("申请日期")}', name:'create_date' ,align:'center' },
		{ title:'${message("创建人")}', name:'creator_name' ,align:'center' },
	];

	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad:true,
        url: 'list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
});
function fmoney(s, n) { 
		n = n > 0 && n <= 20 ? n : 2; 
		s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + ""; 
		var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1]; 
		t = ""; 
		for (i = 0; i < l.length; i++) { 
		t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : ""); 
		} 
		return t.split("").reverse().join("") + "." + r; 
}
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
        			<dt><p>${message("单号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="sn" btn-fun="clear" />
        			</dd>
        		</dl>
        		[#--<dl>
        			<dt><p>${message("充值客户")}：</p></dt>
        			<dd >
        				<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" id="storeId" btn-fun="clear" />
							<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" id="storeName" />
							<input type="button" class="iconSearch" value="" id="selectStore">
						</span>
        			</dd>
        		</dl>--]
        		<dl>
        			<dt ><p>${message("单据状态")}：</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="${message("已保存;处理中;已处理")}" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			<label><input  class="check js-iname" name="status" value="0" type="checkbox" checked/>${message("已保存")}</label>
				       			<label><input  class="check js-iname" name="status" value="1" type="checkbox" checked/>${message("处理中")}</label>
				       			<label><input  class="check js-iname" name="status" value="2" type="checkbox" checked/>${message("已处理")}</label>
				       			<label><input  class="check js-iname" name="status" value="3" type="checkbox"/>${message("已关闭")}</label>
				      		</div>
						</div>
        			</dd>
        		</dl>
			</div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
			</div>	
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	   		<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
		</form>
	</body>
</html>