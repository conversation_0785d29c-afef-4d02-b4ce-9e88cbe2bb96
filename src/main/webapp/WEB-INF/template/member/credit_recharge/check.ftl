<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<title>${message("授信查看")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<style type="text/css">
.upload-list .ul-box{
margin-right:0px;
}
.upload-list {
	width:80px;
    display: inline-block;
    margin-right: 20px;
}
#qButton{
width:20px;
height:20px;
position:absolute;
top:4px;
left:12px;
background:url(/resources/images/button/ico-aa.png) no-repeat center;
}
#addquantity {
padding:0 21px;
height:28px;
line-height:28px;
margin-right:0;
}
#n-input{
width:74%;
float:left;
margin-right:5px;
}
</style>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,2,false,e);
	countTotal();
}
function countTotal(){
	var $input = $("input.price");
	var total = 0;
	$input.each(function(){
		var $this = $(this);
		var amount = Number($this.val());
		if(isNaN(amount)){
			amount = 0;
		}
		total = accAdd(total,amount).toFixed(4);
	});
	$("#totalAmount").val(total);
	$("#totalamount").text(currency(total,true));
    creadit();
}

function creadit(){
    var amount = ${cr.totalAmount};
    if (amount.toString().indexOf("-") != -1){
        var str = fmoney(amount.toString().substring(1,amount.length));
        amount = "-" + str;
    } else {
        amount = fmoney(amount);
    }
    $('.number').text("￥" + amount);

    var amount1 = ${cr.usedAmount};
    if (amount1.toString().indexOf("-") != -1){
        var str = fmoney(amount1.toString().substring(1,amount1.length));
        amount1 = "-" + str;
    } else {
        amount1 = fmoney(amount1);
    }
    $('.numbers').text("￥" + amount1);
}

$().ready(function() {

	var $inputForm = $("#inputForm");
	var $submitButton = $("#submitButton");
	var $addSaleOrgCridit = $("#addSaleOrgCridit");
	
	[#if flag==1]
		$("#wf_area").load("/wf/wf.jhtml?wfid=${cr.wfId}");
	[/#if]
	
	$("input[name='image']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
	// 表单验证
	$inputForm.validate({
		rules: {
			price: {
				required: true,
				min: 0,
				decimal: {
					integer: 12,
					fraction: ${setting.priceScale}
				}
			},
			startDate: {
				required: true
			},
			endDate: {
				required: true
			}
		}
	});
	// 表单验证
	$.validator.addClassRules({
		amount: {
			required: true,
			min: 0,
			decimal: {
				integer: 12,
				fraction: ${setting.priceScale}
			}
		}
	});
	 //查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
	
	//选择机构
	$addSaleOrgCridit.click(function(){
		$addSaleOrgCridit.bindQueryBtn({
			type:'saleOrg',
			bindClick:false,
			title:'${message("查询机构")}',
			url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2&isSellSaleOrg=1',
			callback:function(rows){
				if(rows.length>0){
					if(rows.length>0){
						for (var i = 0; i < rows.length;i++) {
							var row = rows[i];
							row.sale_org_name = row.name;
							row.sale_org_id = row.id;
							row.amount = '';
							$mmGrid.addRow(row);
							countTotal();
						}
					}		
				}
			}
		});	
	})
	var itemIndex = 0;
	var items =${items};
	var cols = [	
       {title:'${message("平台")}', align:'center',width:160 , renderer: function(val,item,rowIndex){
	     return '<input type="hidden" name="saleOrgCreditItems['+itemIndex+'].saleOrg.id" class="saleOrgId_'+item.sale_org_id+' saleOrgId" value="'+item.sale_org_id+'">'+item.sale_org_name;
        }},
		{ title:'${message("授信额度")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price amount"  name="saleOrgCreditItems['+itemIndex+'].amount" value="'+item.amount+'" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
        	return html;
		}},
		{ title:'${message("备注")}', align:'center',name:'memo',renderer: function(val,item,rowIndex){
			return '<input type="text" class="text" name="saleOrgCreditItems['+itemIndex+'].memo" value="'+val+'">';
		}},
		{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
			itemIndex++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteSaleOrg(this)">删除</a>';
		}},
	];
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
		autoLoad: true,
        cols: cols,
        checkCol:false,
        fullWidthRows: true,
        items:items,
        callback:function(){
         	countTotal();
         }
    });
	
	
	var orderFullLink_items = ${fullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
	
	
	$("form").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	 });
	
	$("#submit_button").click(function(){
		var count = $("input.saleOrgId").length;
		if(count<1){
			$.message_alert("会员价明细不能少于一条");
		}else{
			$("form").submit();
		}
	});

});

function deleteSaleOrg(e){
var index = $(e).closest("tr").index();
$.message_confirm('您确定要删除吗？',function(){
	$mmGrid.removeRow(index);
	countTotal();
})
}

function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var flag =1;
	var content = '您确定要审核吗？';
	var data = $form.serialize()+"&flag="+flag;
	if($form.valid()){
		$.message_confirm(content,function(){
			//begin
			ajaxSubmit(e,{
				 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=58&objid=${cr.id}',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 	if(rows.length==1){
				 		data = data+'&objConfId='+rows[0].id;
				 		ajaxSubmit('',{
							 url: "check_wf.jhtml",
							 method: "post",
							 data: data,
							 callback:function(resultMsg){
								$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
									reflush_wf();
								});
							 }
						})
				 	}else{
				 		var str = '';
					 	for(var i=0;i<rows.length;i++){
					 		var row = rows[i];
					 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					 	}
					 	var content = '<table class="input input-edit" style="width:100%">'
								+'<tbody><tr><th>流程模版</th>'
								+'<td>'
									+'<select class="text" id="objConfId">'
										+str
									+'</select>'
								+'</td>'
							+'</tr></tbody></table>';
						var $dialog_check = $.dialog({
							title:"平台授信审核",
							height:'135',
							content: content,
							onOk:function(){
								var objConfId = $("#objConfId").val();
								if(objConfId=='' || objConfId == null){
									$.message_alert("请选择平台授信审核模版");
									return false;
								}
								var url="check_wf.jhtml";
								
								data = data+'&objConfId='+objConfId;
								
								ajaxSubmit($this,{
									 url: url,
									 method: "post",
									 data: data,
									 callback:function(resultMsg){
										$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
											reflush_wf();
										});
									 }
								})
								
							}
						});
				 	}
				 	
					var h = 150;
					$dialog_check.css("top",h+"px");
				 }
			
			});
				
			//end
			
		});
	}
}

// 审核
function check(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		var url="check.jhtml";
		var data = $form.serialize()+"&flag=1";
		ajaxSubmit($this,{
			 url: url,
			 method: "post",
			 data: data,
			 isConfirm:true,
			 confirmText:'您确定要审核吗？',
			 callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			 }
		})
	}
}

function close_t(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var	content = '您确定要关闭吗？';
	if($form.valid()){
		ajaxSubmit(e,{
		 url: "cancel.jhtml",
		 method: "post",
		 isConfirm:true,
		 data:$form.serialize()+"&flag=2",
		 confirmText : content,
		 callback:function(resultMsg){
		 	$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			});
		 }
	})
	}
}

//计划赋值实际
function addquantity() {
	var $input=$("input.pPrice");
	var $tr = $input.closest("tr");
	$tr.find("input.actualAmount").val($input.val());
}

function fmoney(s, n) {
    n = n > 0 && n <= 20 ? n : 2;
    s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
    var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
    t = "";
    for (i = 0; i < l.length; i++) {
        t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
    }
    return t.split("").reverse().join("") + "." + r;
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看平台授信")}
	</div>
	<form id="inputForm" action="updata.jhtml" method="post" enctype="multipart/form-data" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${cr.id}" />
		<div class="tabContent">
			<table class="input input-edit">
			<tr>
				<th>
					${message("单号")}:
				</th>
				<td>
					${cr.sn}
				</td>
				<th>
					${message("总授信额度")}:
				</th>
				<td>
					 <input type="hidden"  name="totalAmount" id="totalAmount" value="">
					 <span class="red number" id="totalamount"></span>
				</td>
				<th>
					${message("制单人")}:
				</th>
				<td>
					${cr.creator.username}
				</td>
				<th>
					${message("已授信额度")}:
				</th>
				<td>
					<span class="red numbers"></span>
					<input type="hidden"  name="usedAmount"  value="">
				</td>
			</tr>
			<tr>
				<th>${message("充值状态")}:</th>
				<td>
					[#if cr.type == "0"]<b class="red">${message("未生效")}</b>[/#if]
        			[#if cr.type == "1"]<b class="blue">${message("已生效")}</b>[/#if]
        			[#if cr.type == "2"]<b class="">${message("已过期")}</b>[/#if]
				</td>
				
				<th>
					${message("流程状态")}:
				</th>
				<td>
					[#if cr.wfState??]
					${message("22222222"+cr.wfState)}
					[/#if]
				</td>
				
				<th>
					${message("开始日期")}:
				</th>
				<td class="date-wrap">
					<input id="startTime" name="startDate" class="text" value="${(cr.startDate?string("yyyy-MM-dd"))!""}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
				</td>
				<th>
					${message("截止日期")}:
				</th>
				<td class="date-wrap">
					<input id="endTime" name="endDate" class="text" value="${(cr.endDate?string("yyyy-MM-dd"))!""}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
				</td>
			</tr>
			<tr>
				<th>
		              <span class="requiredField">*</span>${message("sbu")}:
            	  </th>
           		<td>
            		<select id="sbu" name="sbuId" class="text">
            		    <option value="">${message("请选择")}</option>
						[#list sbus as sbu]
						<option value="${sbu.sbu.id}" [#if cr.sbu.id==sbu.sbu.id] selected[/#if]>${sbu.sbu.name}</option>
						[/#list]
					</select>
            	</td>
				<th>${message("审核状态")}:</th>
				<td>
					[#if cr.docStatus == 0]<b class="blue">${message("已保存")}</b>[/#if]
					[#if cr.docStatus == 1]<b class="blue">${message("处理中")}</b>[/#if]
					[#if cr.docStatus == 2]<b class="green">${message("已处理")}</b>[/#if]
					[#if cr.docStatus == 3]<b class="red">${message("已关闭")}</b>[/#if]
				</td>
					<th>
					${message("制单日期")}:
				</th>
				<td>
					<span>${cr.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("经营组织")}:
				</th>
				<td>
					<select name="organizationId" id="organizationId" class="text organizationId">
					     <option value="">${message("请选择")}</option>
						[#list organizationList as organization]
							<option value="${organization.id}" [#if cr.organization.id==organization.id] selected[/#if]>${organization.name}</option>
						[/#list]
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("平台授信类型")}:
				</th>
				<td>
					<select id="saleOrgCreditTypeId" name="saleOrgCreditTypeId" class="text saleOrgCreditTypeId">
						[#list saleOrgCreditTypeList as saleOrgCreditType]
							<option value="${saleOrgCreditType.id}" [#if cr.saleOrgCreditType.id==saleOrgCreditType.id] selected[/#if]>${saleOrgCreditType.value}</option>
						[/#list]	
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
					<textarea class="text" name="memo">${cr.memo}</textarea>
				</td>
			</tr>
			[#--<tr style="height:88px;">
				<th>
					${message("附件")}:
				</th>
				<td colspan="7">
					[#if flag!=1 && cr.docStatus==0]
					<input type="hidden" name="image" value="${cr.image}"/>
					<input type="hidden" name="image2" value="${cr.image2}"/>
					<input type="hidden" name="image3" value="${cr.image3}"/>
					[#else]
						[#if cr.image??]
							<span class="ico-options" style="margin-right:15px;display: inline-block;">
								<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">
								<div class="pic"><a href="${cr.image?replace('thumbnail','source')}" target="_blank" style="width: 80px;">
	                        		<img src="${cr.image}" style="width: 80px; height: 80px;">
	                        	</a></div>
	                        	</div>
	                        	<input type="hidden" name="topIcon" value="${cr.image}"/>
							</span>
						[/#if]
						[#if cr.image2??]
							<span class="ico-options" style="margin-right:15px;display: inline-block;">
								<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">
								<div class="pic"><a href="${cr.image2}" target="_blank" style="width: 80px;">
	                        		<img src="${cr.image2}" style="width: 80px; height: 80px;">
	                        	</a></div>
	                        	</div>
	                        	<input type="hidden" name="topIcon" value="${cr.image2}"/>
							</span>
						[/#if]
						[#if cr.image3??]
							<span class="ico-options" style="margin-right:15px;display: inline-block;">
								<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">
								<div class="pic"><a href="${cr.image3}" target="_blank" style="width: 80px;">
	                        		<img src="${cr.image3}" style="width: 80px; height: 80px;">
	                        	</a></div>
	                        	</div>
	                        	<input type="hidden" name="topIcon" value="${cr.image3}"/>
							</span>
						[/#if]
					[/#if]
				</td>
			</tr>--]
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			 <tr class="border-L1">
				<th> ${message("平台授信明细")}:
				</th>
				<td colspan="7">
				  <a href="javascript:;" id="addSaleOrgCridit" class="button">选择机构</a>
				</td>
			</tr>
			</tr>
			    <tr class="border-L2">
            	<td colspan="8">
            		<div>
						<table id="table-m1"></table>
					</div>
            	</td>
            </tr>
			<tr class="border-L2">
				<th>${message("全链路信息")}:</th>
				<td colspan="7"></td>
		</table>
		</div>
		<div class="fixed-top">
			<a href="javascript:void(0);" class="iconButton" id="addButton" onClick="create_iframe('add.jhtml?flag=${flag}')">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			[#if cr.docStatus== 0]
			[#if isCheckWf]
				[#if cr.wfId==null]
					<input type="button" class="button sureButton" value="${message("流程审批")}" onclick="check_wf(this,1)" />
				[/#if]
			[#else]
				<a id="shengheButton" class="iconButton" onclick="check(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			[/#if]
			[/#if]
			[#if cr.docStatus==0 && (!isCheckWf || cr.wfId==null)]
			<input type="submit" id="submit_button" class="button sureButton" value="${message("保存")}" />
			[/#if]
			[#if (cr.docStatus ==0 && cr.wfId==null)||cr.docStatus ==2]
				<input type="button" class="button cancleButton" value="${message("关闭")}" onclick="close_t(this,2)" />
			[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}" />
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>