<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("经销商充值查看")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style type="text/css">
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
	#qButton{
		width:20px;
		height:20px;
		position:absolute;
		top:4px;
		left:12px;
		background:url(/resources/images/button/ico-aa.png) no-repeat center;
		}
		#addquantity {
		padding:0 21px;
		height:28px;
		line-height:28px;
		margin-right:0;
		}
		#n-input{
		width:74%;
		float:left;
		margin-right:5px;
	}
</style>
<script type="text/javascript">

	function editPrice(t,e){
		extractNumber(t,2,true,e)
	}

	function check_wf(e) {
		var $this = $(e);
	    var $form = $("#inputForm");
	    var sbuId = $(".sbuId").val();
	    var modelId = model($("#sbuId").val(),"正式");
	    if($form.valid()){
	        $.message_confirm("您确定要审批流程吗？",function(){
	            //var objTypeId = 100020;//大自然开发
	            //var modelId = 777501;//大自然开发
	            var objTypeId = 100022;//大自然测试&正式
	            var url="/member/distributor_recharge/check_wf.jhtml?id=${dr.id}&modelId="+modelId+"&objTypeId="+objTypeId;
	            var data = $form.serialize();
	            ajaxSubmit(e,{
	                method: 'post',
	                url: url,
	                async: true,
	                data:data,
	                callback: function(resultMsg) {
	                    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
	                        location.reload(true);
	                    })
	                }
	            });
	        });
	    }
	}


	//查流程模板
	function model(sbuId,versions){
	    var json = '{"正式":{"1":"55038","2":"55060","3":"55079","4":"55049","5":"117776","6":"10011","7":"10011","8":"10011"},';
	    json +='"测试":{"1":"55038","2":"55060","3":"55079","4":"55049","5":"175120","6":"","7":"","8":"55090"}}';
	    var model = JSON.parse(json);
	    return model[versions][sbuId];
	}



	// 审核
	function check(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			var url="/member/distributor_recharge/check.jhtml";
			var data = $form.serialize()+"&flag=1";
			ajaxSubmit($this,{
				 url: url,
				 method: "post",
				 data: data,
				 isConfirm:true,
				 confirmText:'您确定要审核吗？',
				 callback:function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				 }
			})
		}	
	}
	
	function save(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			var url="updata.jhtml";
			var data = $form.serialize();
			ajaxSubmit($this,{
				 url: url,
				 method: "post",
				 data: data,
				 isConfirm:true,
				 confirmText:"您确定要保存吗？",
				 callback:function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				 }
			})
		}
	}
	
	function checkWfUpdate(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			var url="checkWf_update.jhtml";
			var data = $form.serialize();
			ajaxSubmit($this,{
				 url: url,
				 method: "post",
				 data: data,
				 isConfirm:true,
				 confirmText:"您确定要保存吗？",
				 callback:function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				 }
			})
		}	
	}
	
	
	function close_e(e){
		var $this = $(e);
		var $form = $("#inputForm");
		var flag = 2;
		var	content = '您确定要作废吗？';
		$.message_confirm(content,function(){
			var url="/member/store_recharge/cancel.jhtml";
			var data = $form.serialize();
			ajaxSubmit($this,{
				 url: url,
				 method: "post",
				 data: data,
				 callback:function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					});
				 }
			})
		});
	}

	function initGrid(){
		/**初始化订单附件*/
	    var depositAttach_items = ${depositAttach_json};
	    var depositAttachIndex=0;
		var cols = [				
	    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
				if(obj==undefined){
					var url = item.url;
					var fileObj = getfileObj(item.file_name , item.name, item.suffix);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['depositAttachs['+depositAttachIndex+'].id']=item.id;
					hideValues['depositAttachs['+depositAttachIndex+'].url']=url;
					hideValues['depositAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
					
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : item.create_date,
						textName:'depositAttachs['+depositAttachIndex+'].name',
						hideValues: hideValues
					});
				}else{
					var url = item.url;
					var fileObj = getfileObj(item.name);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['depositAttachs['+depositAttachIndex+'].url']=url;
					hideValues['depositAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
					
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : '',
						textName:'depositAttachs['+depositAttachIndex+'].name',
						hideValues:hideValues
					});
				}
	    	}},
			{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
				return '<div><textarea class="text" name="depositAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
			}},
	    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
				depositAttachIndex++;
				return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
			}}
		];
		var $amGrid=$('#table-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
	        cols: cols,
	        items:depositAttach_items,
	        checkCol: false,
	        autoLoad: true
	    });
	    
	    var $addAttach = $("#addAttach");
		var attachIdnex = 0;
		var option1 = {
			dataType: "json",
		    uploadToFileServer:true,
		    uploadSize: "fileurl",
	        callback : function(data){
	        	var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
		        for(var i=0;i<data.length;i++){
					var row=data[i].file_info;
					$amGrid.addRow(row,null,1);
		        }
				
	        }
	    }
	    $addAttach.file_upload(option1);
	}
	
	$().ready(function() {
		
		var $addBankCard = $("#addBankCard");
		var $deleteBankCard = $("a.deleteBankCard");
		
		
		[#if dr.wfId!=null]
			$("#wf_area").load("/act/wf/wf.jhtml?wfid=${dr.wfId}");
		[/#if]
		
		addquantity();
		
	    var amount = ${dr.amount};
	    if (amount.toString().indexOf("-") != -1){
	        var str = fmoney(amount.toString().substring(1,amount.length));
	        amount = "-" + str;
		} else {
	        amount = fmoney(amount);
	    }
	    $('.number').text("￥" + amount);
	    var actualAmount = ${dr.actualAmount};
	    if (actualAmount.toString().indexOf("-") != -1){
	        var str = fmoney(actualAmount.toString().substring(1,actualAmount.length));
	        actualAmount = "-" + str;
	    } else {
	        actualAmount = fmoney(actualAmount);
	    }
	    $('.actualNumber').text("￥" + actualAmount);
	
	    var orderFullLink_items = ${fullLink_json};
		var cols = [				
	    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
			{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
			{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
		];
		$('#table-full').mmGrid({
			fullWidthRows:true,
			height:'auto',
	        cols: cols,
	        items:orderFullLink_items,
	        checkCol: false,
	        autoLoad: true
	    });
	
	    //查询客户
	    $("#selectStore").click(function(){
			$("#selectStore").bindQueryBtn({
				type:'store',
				title:'${message("查询客户")}',
				bindClick:false,
				url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
				callback:function(rows){
					if(rows.length>0){
						var row = rows[0];
						//客户
						$(".storeId").val(row.id);
						$(".storeName").val(row.name);
						$("#storeAlias").text(row.alias);
						$("#outTradeNo").text(row.out_trade_no);
						//机构
						$(".saleOrgId").val(row.sale_org_id);
						$("#saleOrgName").text(row.sale_org_name);
						//区域经理
						$("input.storeMemberId").val(row.store_member_id);
	                    $("#regionalManagerName").text(row.store_member_name);
						//收款账户
						$(".bankCardNo").val('');
						$(".bankCardId").val('');
						//sbu
						$("#sbuName").text('');
						$(".sbuId").val('');
						//经营组织
						$(".organization").val('');
						$("#organization").text('');
						$bankCard_mmGrid.removeRow();
					}
				}
			});
		});
	    
	    
	    
		$("#selectBankCard").click(function(){
			var storeId = $(".storeId").val();
	    	if(storeId==''){
	    		$.message_alert('请选择充值客户');
	    		return false;
	    	}
			$("#selectBankCard").bindQueryBtn({
				type:'bankCard',
				bindClick:false,
				title:'${message("查询收款账户")}',
				url:'/member/bankCard/select_bank_card.jhtml?isTotalAccount=true&saleOrgId='+$(".saleOrgId").val(),		
				callback:function(rows){
					if(rows.length>0){
						var row = rows[0];
						$(".bankCardNo").val(row.bank_card_no);
						$(".bankCardId").val(row.id);
						$(".bankName").text(row.bank_name);
						$(".bankCode").text(row.bank_code);
						if (row.organization_name !=null && row.orgId !=null) {
							$("#organization").text(row.organization_name);
							$(".organization").val(row.orgId);
						}
						if (row.sbu_name !=null && row.sbu !=null) {
							$("#sbuName").text(row.sbu_name);
							$(".sbuId").val(row.sbu);
						}
						selectBal();
					}
				}
			});
		});
		
		//附件初始化
		initGrid();
		var $delProductImage1 = $(".deleteAttachment");
		$delProductImage1.live("click", function() {
			var $this = $(this);
			$this.closest("tr").remove();
		});
		
		$("#inputForm").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		 });
		
		$("#inputForm").validate({
			storeName: {
				required: true
			},
			balanceMonth: {
				required: true
			},
			bankCardNo:{required: true}
		});
		
		
		//打开查询收款账户界面
	    $addBankCard.click(function(){
	    	var storeId = $(".storeId").val();
	    	if(storeId==''){
	    		$.message_alert('请选择充值客户');
	    		return false;
	    	}
	    	var bankCardId = $(".bankCardId").val();
	    	if(bankCardId==''){
	    		$.message_alert('请选择收款账户');
	    		return false;
	    	}
	    	var $this = $(this);
			var $tr =$this.closest("tr");
			 $addBankCard.bindQueryBtn({
	            type:'bankCard',
	            bindClick:false,
	            title:'${message("查询收款账户")}',
	            url:'/member/bankCard/select_bank_card.jhtml?isTotalAccount=false&multi=2&saleOrgId='+$(".saleOrgId").val(),		
	            callback:function(rows){
	                if(rows.length>0){
	                	for (var i = 0; i < rows.length;i++) {
	                        var idH = $(".bankCard_"+rows[i].id).length;
	                        if(idH > 0){
	                            $.message_alert('收款账号【'+rows[i].bank_card_no+'】已添加');
	                            return false;
	                        }
	                    }
	                    for (var i = 0; i < rows.length;i++) {
							var row = rows[i];
							$bankCard_mmGrid.addRow(row,null,1);
						}	
	                }
	            }
	        }); 
	    })
		
		 var line_no = 1;	
		//删除收款账号
	    $deleteBankCard.live("click", function() {
	          var index = $(this).closest("tr").index();
	          $.message_confirm('您确定要删除吗？',function(){
	        	  $bankCard_mmGrid.removeRow(index);
	              var line_number = 1;
	              $("span.line_no").each(function(){
	                  $(this).html(line_number++);
	              });
	              line_no--;
	          })
	     });
		    
	    var bankCardIndex=0;
	    var items=${distributorRechargeItemList};
		var bankCard_cols = [
			{ title:'${message("行号")}', width:30, align:'center',renderer: function(val,item,rowIndex){
			    return '<span class="line_no">'+ line_no +'</span>';
			}},
			{ title:'${message("单据编号明细")}' , name:'sn', align:'center', width:100 , renderer: function(val,item,rowIndex, obj){
				var dealerRechargeItemId = '';
         		if(obj == undefined){
         			dealerRechargeItemId = item.id;			
         		}
         		if(dealerRechargeItemId !=''){
         			var html=val+'<input type="hidden" class="text dealerRechargeItemId" name="distributorRechargeItems['+bankCardIndex+'].dealerRechargeItem.id" value="'+dealerRechargeItemId+'">';
    				return html;
         		}
			}},
			{ title:'${message("收款账号")}', name:'bank_card_no' ,align:'center', width:120, renderer: function(val,item,rowIndex, obj){
				var bankCardId = '';
         		if(obj == undefined){
         			bankCardId = item.bank_card_id;			
         		}else {
         			bankCardId = item.id;	
         		}
				var html=val+'<input type="hidden" class="text bankCard bankCard_'+bankCardId+'" name="distributorRechargeItems['+bankCardIndex+'].bankCard.id" value="'+bankCardId+'">';
				return html;
			}},
			{ title:'${message("经营组织")}' , name:'organization_name', align:'center', width:100},
			{ title:'${message("sbu")}' , name:'sbu_name', align:'center', width:80},
		    { title:'${message("金额")}',name:'amount', width:100, align:'center', renderer:function(val,item,rowIndex,obj){
				var html = '<div class="nums-input ov">'+
		            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editAmount (this.nextSibling,event)">'+
		            	'<input type="text"  class="t amountCollected"  name="distributorRechargeItems['+bankCardIndex+'].amountCollected" value="'+val+'" oninput="editAmount (this,event)" onpropertychange="editAmount(this,event)" >'+
		            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editAmount (this.previousSibling,event)">'+
		        	'</div>';
		        return html;
			}},
			{ title:'${message("行备注")}',name:'memo', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
				var html='<input type="text" class="text" name="distributorRechargeItems['+bankCardIndex+'].memo" value="'+val+'"/>';
				return html;
			}},
			{ title:'${message("操作")}', align:'center', width:30, renderer:function(val,item){
				bankCardIndex++;
				line_no++;
				return '<a href="javascript:;"  class="deleteBankCard btn-delete">删除</a>';
			}}
		];
		
		$bankCard_mmGrid = $('#table-bankCard').mmGrid({
			height:'auto',
			cols: bankCard_cols,
			items:items,
			fullWidthRows:true,
			checkCol: false,
			autoLoad: true,
		 });
		
	});

	//计划赋值实际
	function addquantity() {
		var $input=$("input.pPrice");
		var $tr = $input.closest("tr");
		$tr.find("input.actualAmount").val($input.val());
	}

	function clearSelect(e){
		var $this = $(e);
		var value = $this.val();
		if(value==undefined || value.length==0){
			var $tr = $(e).closest("div.search");
			$this.prev("input").val("");
			$(".bankName").text('');
		}
	}

	// 千分号
	function fmoney(s, n) { 
		n = n > 0 && n <= 20 ? n : 2; 
		s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + ""; 
		var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1]; 
		t = ""; 
		for (i = 0; i < l.length; i++) { 
		t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : ""); 
		} 
		return t.split("").reverse().join("") + "." + r; 
		 
	}

	function selectBal(){
		var ssId = $(".storeId").val();
		var sbuId = $("#sbuId").val();
		var saleOrgId = $(".saleOrgId").val();
		var orgId = $(".organization").val();
		ajaxSubmit("",{
	        method:'post',
	        url:'/finance/balance/get_balance.jhtml',
	        data:{storeId:ssId,sbuId:sbuId,saleOrgId:saleOrgId,organizationId:orgId},
	        callback:function(resultMsg) {
	            var data = resultMsg.objx;
	            if(data!=null){
		            //可用余额
		            $("#balance").text(currency(data.balance,true));            	
	            }
	        }
	    });
	}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看客户充值")}
	</div>
	<form id="inputForm" action="update.jhtml"  method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${dr.id}" />
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>${message("余额充值编号")}:</th>
				<td><span>${dr.sn}</span></td>
				<th>${message("机构")}:</th>
				<td>
					<input type="hidden" name="saleOrgId" class="text saleOrgId" id="saleOrgId" btn-fun="clear" value="${dr.saleOrg.id}"/>
	            	<span id="saleOrgName">${dr.saleOrg.name}</span>
				</td>
				<th>${message("充值金额")}:</th>
				<td>
					[#if dr.docStatus == 0]
						<div class="nums-input ov">
							<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onClick="addquantity()">
							<input type="text"  class="t pPrice"  name="amount" value="${dr.amount}"   onpropertychange="editPrice(this,event)" oninput="addquantity()">
							<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onClick="addquantity()">
						</div>						
					[#else]
						<span class="red number">${dr.amount}</span>
						<input type="hidden" name="amount" class="pPrice" value="${dr.amount}">
					[/#if]	
				</td>
				<th>${message("实际充值金额")}:</th>
				<td>
					<span class="red actualNumber">${dr.actualAmount}</span>
					<input type="hidden"  name="actualAmount" value="${dr.actualAmount}" />
				</td>
			</tr>
			<tr>
				<th>${message("充值客户")}:</th>
				<td>
					[#if dr.docStatus == 0]
						<span class="search" style="position:relative">
						<input type="hidden" name="storeId" class="text storeId" btn-fun="clear" value="${dr.store.id}"/>
						<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" value="${dr.store.name}"  readOnly/>
						<input type="button" class="iconSearch" value=""  id="selectStore" >
						</span>
					[#else]
						<input type="hidden" name="storeId" class="text storeId" btn-fun="clear" value="${dr.store.id}"/>
						<span>${dr.store.name}</span>
					[/#if]
				</td>
				<th>${message("客户简称")}:</th>
				<td>
				   <span>${dr.store.alias}</span>
				</td>
				<th>${message("客户编码")}:</th>
				<td>
				   <span id="outTradeNo">${dr.store.outTradeNo}</span>
				</td>
				<th>${message("Sbu")}:</th>
	            <td>
	            	<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${dr.sbu.id}"/>
	            	<span id="sbuName">${dr.sbu.name}</span>
	            </td>
			</tr>
			<tr>
				<th>${message("经营组织")}:</th>
				<td>
					<span id="organization">${dr.organization.name}</span>
					<input type="hidden" class="text organization" name="organizationId" value="${dr.organization.id}"  btn-fun="clear"  />
				</td>	
				<th>
					${message("收款账户")}:
				</th>
				<td>
					[#if dr.docStatus == 0]
						<span class="search" style="position:relative">
							<input type="hidden" name="bankCardId" class="text bankCardId" btn-fun="clear" value="${dr.bankCard.id}"/>
							<input type="text" class="text bankCardNo" maxlength="200" name="bankCardNo" onkeyup="clearSelect(this)" value="${dr.bankCard.bankCardNo}"  readOnly/>
							<input type="button" class="iconSearch" value="" id="selectBankCard">
						</span>	
					[#else]
						<input type="hidden" name="bankCardId" class="text bankCardId" btn-fun="clear" value="${dr.bankCard.id}"/>
						<span>${dr.bankCard.bankCardNo}</span>
					[/#if]
				</td>
				<th>${message("收款银行")}:</th>
				<td>
					<span class="bankName">${dr.bankCard.bankName}</span>
				</td>
				<th>${message("银行代码")}:</th>
				<td>
					<span class="bankCode">${dr.bankCard.bankCode}</span>
				</td>
			</tr>
			<tr>
				<th>${message("汇款人")}:</th>
				<td>
					[#if dr.docStatus == 0]
						<input type="text" class="text remitter" name="remitter" value="${dr.remitter}" btn-fun="clear" />
					[#else]
						<span>${dr.remitter}</span>
					[/#if]
				</td>
				<th>${message("汇款账号")}:</th>
				<td>
					[#if dr.docStatus == 0]
						<input type="text" class="text remittanceAccount" name="remittanceAccount" value="${dr.remittanceAccount}" btn-fun="clear" />
					[#else]
						<span>${dr.remittanceAccount}</span>
					[/#if]
				</td>
				<th>${message("银行水单号")}:</th>
				<td>
					[#if dr.docStatus == 0]
						<input type="text" class="text bankSlip" name="bankSlip" value="${dr.bankSlip}" btn-fun="clear" />
					[#else]
						<span>${dr.bankSlip}</span>
					[/#if]
				</td>
				<th>${message("到款状态")}:</th>
				<td>
					<!-- [#if dr.erpStatus==1]
						<b class="green">${message("已到账")}</b>
					[#else]
						<b class="blue">${message("未到账")}</b>
					[/#if] -->
				</td>
			</tr>
			<tr>
				<th>${message("充值类型")}:</th>
				<td>
					<span>${dr.rechargeType.value}</span>
					<input type="hidden" name="rechargeTypeId" class="text rechargeTypeId" id="rechargeTypeId" btn-fun="clear" value="${dr.rechargeType.id}"/>
				</td>
				<th>${message("区域经理")}:</th>
				<td>
					<input type="hidden" name="regionalManagerId" class="text storeMemberId" btn-fun="clear" value="${dr.regionalManager.id}" /> 
					<span id="regionalManagerName">${dr.regionalManager.name}</span>
				</td>
				<th>${message("对账月份")}:</th>
				<td>
					<input type="hidden" value="${dr.balanceMonth}" class="text balanceMonth" name="balanceMonth" onfocus="WdatePicker({dateFmt: 'yyyy-MM'});" btn-fun="clear" />
				</td>
				<th>${message("申请日期")}:</th>
				<td>
					[#if dr.applyDate??]
						<span>${dr.applyDate?string("yyyy-MM-dd")}</span>
						<input type="hidden" name="applyDate" value="${dr.applyDate}">
					[/#if]
				</td>
			</tr>
			<tr>
				<th>
					<span class="red">*</span>
					${message("GL日期")}:
				</th>
    			<td>
    				<input id="startTime" name="glDate" class="text" value="${dr.glDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
					<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
    			</td>
    			<th>${message("来源单号")}:</th>
				<td>
					<span>${dr.sourceDepositRecharge.sn}</span>
				</td>
				<th>${message("来源账户")}:</th>
				<td>
					<span>${dr.sourceDepositRecharge.bankCard.bankCardNo}</span>
				</td>
    			<th>${message("可用余额")}:</th>
                <td>
                	<span class="red" id="balance">${currency(balance, true)}</span>
                </td>
			</tr>
			<tr>
				<th>${message("放行人")}:</th>
				<td>
					<span>${dr.permitThroughName}</span>
				</td>
				<th>${message("单据状态")}:</th>
				<td>
					[#if dr.docStatus == 0]<b class="blue">${message("已保存")}</b>[/#if]
					[#if dr.docStatus == 1]<b class="blue">${message("已提交")}</b>[/#if]
					[#if dr.docStatus == 2]<b class="green">${message("已处理")}</b>[/#if]
					[#if dr.docStatus == 3]<b class="red">${message("已作废")}</b>[/#if]
				</td> 
				<th>${message("流程状态")}:</th>
				<td>
					[#if dr.wfState??]
						<span>${message("********"+dr.wfState)}</span>
					[/#if]
				</td>
				<th>${message("创建人")}:</th>
				<td>
					<span>${dr.creator.name}</span>
				</td>
			</tr>
			<tr>
				<th>${message("创建日期")}:</th>
				<td>
					<span>${dr.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
				</td>
			</tr>
			<tr>
				<th>
					${message("申请备注")}:
				</th>
				<td colspan="7">
					<textarea name="memo" class="text" id="memo">${dr.memo}</textarea>
				</td>
			</tr>
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			<div class="title-style">
				${message("收款账号列表")}:
				<div class="btns">
					<a href="javascript:void(0);" id="addBankCard" class="button">${message("选择收款账号")}</a>
				</div>
			</div>
			<table id="table-bankCard"></table>
		</table>	
		<div class="title-style">
			${message("全链路信息")}
		</div>
		<table id="table-full"></table>
		<div class="title-style">
			${message("附件信息")}
			<div class="btns">
				<a href="javascript:;" id="addAttach" class="button">添加附件</a>
			</div>
		</div>
		<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			[#if adjustmentCount == 0]
				[#if dr.docStatus==0 ]
					<input type="button" id="submit_button" class="button sureButton" value="${message("保存")}" onclick="save(this)">
					<input type="button" class="button sureButton" value="${message("流程审批")}" onclick="check_wf(this,1)" />
					<input type="button" class="button cancleButton" value="${message("作废")}" onclick="close_e(this,2)" />
				[/#if]
			    [#if distributorSaveUserRoles >0 && dr.docStatus==1]
						<input type="button" class="button sureButton" value="${message("保存")}" onclick="checkWfUpdate(this)">
				[/#if]
			[/#if]	
		    <input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>