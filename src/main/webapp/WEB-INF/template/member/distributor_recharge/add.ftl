<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("经销商充值添加 ")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
</style>
<script type="text/javascript">

function editPrice(t,e){
	extractNumber(t,2,true,e)
}

function editAmount(t,e,n){
	var rate = 6;
	if(n!=undefined)rate = n;
	if(extractNumber(t,rate,true,e)){
		editTotalAmount();
	}
}

function editTotalAmount(){
	var totalAmount = 0;
	$("input.amountCollected").each(function(){
		var $this = $(this);
		var amountCollected = $this.val();
		totalAmount = accAdd(totalAmount,amountCollected);
	});
	$("input.totalAmountCollected").val(totalAmount);
}

$().ready(function() {
	var $inputForm = $("#inputForm");
	var $submitButton = $("#submitButton");
	var $addBankCard = $("#addBankCard");
	var $deleteBankCard = $("a.deleteBankCard");
	
	// 表单验证
	$inputForm.validate({
		rules: {
			storeName: {
				required: true
			},
			balanceMonth: {
				required: true
			},
			bankCardNo:{required: true}
		} 
	});
	
	/**初始化订单附件*/
    var depositAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues['depositAttachs['+depositAttachIndex+'].url']=url;
			hideValues['depositAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName:'depositAttachs['+depositAttachIndex+'].name',
				hideValues:hideValues
			});               
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="depositAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			depositAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
    var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
	
	$("form").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'view.jhtml?id='+resultMsg.objx;
			});
		}
	 });
	
	//查询客户
	$("#selectStore").click(function(){
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			bindClick:false,
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					//客户
					$(".storeId").val(row.id);
					$(".storeName").val(row.name);
					$("#storeAlias").text(row.alias);
					$("#outTradeNo").text(row.out_trade_no);
					//机构
					$(".saleOrgId").val(row.sale_org_id);
					$("#saleOrgName").text(row.sale_org_name);
					//区域经理
					$("input.storeMemberId").val(row.store_member_id);
                    $("#regionalManagerName").text(row.store_member_name);
					//收款账户
					$(".bankCardNo").val('');
					$(".bankCardId").val('');
					//sbu
					$("#sbuName").text('');
					$(".sbuId").val('');
					//经营组织
					$(".organization").val('');
					$("#organization").text('');
					$bankCard_mmGrid.removeRow();
				}
			}
		});
	});
	
	$("#selectBankCard").click(function(){
    	var storeId = $(".storeId").val();
    	if(storeId==''){
    		$.message_alert('请选择充值客户');
    		return false;
    	}
		$("#selectBankCard").bindQueryBtn({
			type:'bankCard',
			bindClick:false,
			title:'${message("查询收款账户")}',
			url:'/member/bankCard/select_bank_card.jhtml?isTotalAccount=true&saleOrgId='+$(".saleOrgId").val(),		
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$(".bankCardNo").val(row.bank_card_no);
					$(".bankCardId").val(row.id);
					$(".bankName").text(row.bank_name);
					$(".bankCode").text(row.bank_code);
					if (row.organization_name !=null && row.orgId !=null) {
						$("#organization").text(row.organization_name);
						$(".organization").val(row.orgId);
					}
					if (row.sbu_name !=null && row.sbu !=null) {
						$("#sbuName").text(row.sbu_name);
						$(".sbuId").val(row.sbu);
					}
					selectBal();
				}
			}
		});
	});
	
	//打开查询收款账户界面
    $addBankCard.click(function(){
    	var storeId = $(".storeId").val();
    	if(storeId==''){
    		$.message_alert('请选择充值客户');
    		return false;
    	}
    	var bankCardId = $(".bankCardId").val();
    	if(bankCardId==''){
    		$.message_alert('请选择收款账户');
    		return false;
    	}
    	var $this = $(this);
		var $tr =$this.closest("tr");
		 $addBankCard.bindQueryBtn({
            type:'bankCard',
            bindClick:false,
            title:'${message("查询收款账户")}',
            url:'/member/bankCard/select_bank_card.jhtml?isTotalAccount=false&multi=2&saleOrgId='+$(".saleOrgId").val(),			
            callback:function(rows){
                if(rows.length>0){
                	for (var i = 0; i < rows.length;i++) {
                        var idH = $(".bankCard_"+rows[i].id).length;
                        if(idH > 0){
                            $.message_alert('收款账号【'+rows[i].bank_card_no+'】已添加');
                            return false;
                        }
                    }
                    for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$bankCard_mmGrid.addRow(row,null,1);
					}	
                }
            }
        }); 
    })
    
    
    var line_no = 1;	
	//删除收款账号
    $deleteBankCard.live("click", function() {
          var index = $(this).closest("tr").index();
          $.message_confirm('您确定要删除吗？',function(){
        	  $bankCard_mmGrid.removeRow(index);
              var line_number = 1;
              $("span.line_no").each(function(){
                  $(this).html(line_number++);
              });
              line_no--;
          })
     });
    
    var bankCardIndex=0;
	var bankCard_cols = [
		{ title:'${message("行号")}', width:30, align:'center',renderer: function(val,item,rowIndex){
		    return '<span class="line_no">'+ line_no +'</span>';
		}},
		{ title:'${message("收款账号")}', name:'bank_card_no' ,align:'center', width:120, renderer: function(val,item,rowIndex, obj){
			var html=val+'<input type="hidden" class="text bankCard bankCard_'+item.id+'" name="distributorRechargeItems['+bankCardIndex+'].bankCard.id" value="'+item.id+'">';
			return html;
		}},
		{ title:'${message("经营组织")}' , name:'organization_name', align:'center', width:100},
		{ title:'${message("sbu")}' , name:'sbu_name', align:'center', width:80},
	    { title:'${message("金额")}', width:100, align:'center', renderer:function(val,item,rowIndex,obj){
			var html = '<div class="nums-input ov">'+
	            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editAmount (this.nextSibling,event)">'+
	            	'<input type="text"  class="t amountCollected"  name="distributorRechargeItems['+bankCardIndex+'].amountCollected" value="0" oninput="editAmount (this,event)" onpropertychange="editAmount(this,event)" >'+
	            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editAmount (this.previousSibling,event)">'+
	        	'</div>';
	        return html;
		}},
		{ title:'${message("行备注")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			var html='<input type="text" class="text" name="distributorRechargeItems['+bankCardIndex+'].memo" value="">';
			return html;
		}},
		{ title:'${message("操作")}', align:'center', width:30, renderer:function(val,item){
			bankCardIndex++;
			line_no++;
			return '<a href="javascript:;"  class="deleteBankCard btn-delete">删除</a>';
		}}
	];

	$bankCard_mmGrid = $('#table-bankCard').mmGrid({
		height:'auto',
		cols: bankCard_cols,
		fullWidthRows:true,
		checkCol: false,
		autoLoad: true,
	 });
	
	/* $("#submit_button").click(function(){
		var totalAmount =  $("input[name='amount']").val();
		if(totalAmount==undefined || totalAmount=='' || parseFloat(totalAmount) <= 0){
			$.message_alert("充值金额不能少于0");
			return false;
		}
		$("form").submit();
	}); */
	
});


function clearSelect(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("div.search");
		$this.prev("input").val("");
		$(".bankName").text('');
	}
}


function selectBal(){
	var ssId = $(".storeId").val();
	var sbuId = $("#sbuId").val();
	var saleOrgId = $(".saleOrgId").val();
	var orgId = $(".organization").val();
	ajaxSubmit("",{
        method:'post',
        url:'/finance/balance/get_balance.jhtml',
        data:{storeId:ssId,sbuId:sbuId,saleOrgId:saleOrgId,organizationId:orgId},
        callback:function(resultMsg) {
            var data = resultMsg.objx;
            if(data!=null){
	            //可用余额
	            $("#balance").text(currency(data.balance,true));            	
            }
        }
    });
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增客户余额充值申请")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
				<table class="input input-edit">
					<tr>
						<th>${message("余额充值编号")}:</th>
						<td></td>
						<th>${message("机构")}:</th>
						<td>
							<input type="hidden" name="saleOrgId" class="text saleOrgId" id="saleOrgId" btn-fun="clear" value=""/>
			            	<span id="saleOrgName"></span>
						</td>
						<th>
							<span class="requiredField">*</span>
							${message("充值金额")}:
						</th>
						<td>
						    <div class="nums-input ov">
								<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
								<input type="text"  class="t"  name="amount" value="0"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
								<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
								<input type="hidden" class="text totalAmountCollected" name="totalAmountCollected" value=""/>
							</div>		
						</td>
						<th>${message("实际充值金额")}:</th>
						<td></td>
					</tr>
					<tr>
						<th>
							<span class="requiredField">*</span>${message("充值客户")}:
						</th>
						<td>
							<span class="search" style="position:relative">
								<input type="hidden" name="storeId" class="text storeId" value="" btn-fun="clear"/>
								<input type="text" name="storeName" class="text storeName" value="" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
								<input type="button" class="iconSearch" value="" id="selectStore">
							</span>
						</td>
						<th>${message("客户简称")}:</th>
						<td>
							<span id="storeAlias"></span>
						</td>
						<th>${message("客户编码")}:</th>
						<td>
						   <span id="outTradeNo"></span>
						</td>
						<th>${message("Sbu")}:</th>
		             	<td>
			            	<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value=""/>
			            	<span id="sbuName"></span>
		             	</td>
					</tr>
					<tr>
						<th>${message("经营组织")}:</th>
						<td>
							<span id="organization"></span>
							<input type="hidden" class="text organization" name="organizationId"  btn-fun="clear"  />
						</td>
						<th>
							<span class="requiredField">*</span>
							${message("收款账户")}:
						</th>
						<td>
							<span class="search" style="position:relative">
								<input type="hidden" name="bankCardId" class="text bankCardId" btn-fun="clear" value=""/>
								<input type="text" class="text bankCardNo" name="bankCardNo" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
								<input type="button" class="iconSearch" value="" id="selectBankCard">
							</span>
						</td>
						<th>${message("收款银行")}:</th>
						<td>
							<span class="bankName"></span>
						</td>
						<th>${message("银行代码")}:</th>
						<td>
							<span class="bankCode"></span>
						</td>
					</tr>
					<tr>
						<th>${message("汇款人")}:</th>
						<td>
							<input type="text" class="text remitter" name="remitter" value="" btn-fun="clear" />
						</td>
						<th>${message("汇款帐号")}:</th>
						<td>
							<input type="text" class="text remittanceAccount" name="remittanceAccount" value="" btn-fun="clear" />
						</td>
						<th>${message("银行水单号")}:</th>
						<td>
							<input type="text" class="text bankSlip" name="bankSlip" value="" btn-fun="clear" />
						</td>
						<th>${message("到款状态")}:</th>
						<td>
							<!-- <b class="blue">${message("未到账")}</b> -->
						</td>
					</tr>
					<tr>
						<th>
							<span class="requiredField">*</span>
							${message("充值类型")}:
						</th>
						<td>
							<span>${rechargeType.value}</span>
							<input type="hidden" name="rechargeTypeId" class="text rechargeTypeId" id="rechargeTypeId" btn-fun="clear" value="${rechargeType.id}"/>
						</td>
						<th>${message("区域经理")}:</th>
						<td>
							<input type="hidden" name="regionalManagerId" class="text storeMemberId" btn-fun="clear" value="" /> 
							<span id="regionalManagerName"></span>
						</td>
						<th>${message("对账月份")}:</th>
						<td>
							<input type="hidden" value="${nowDate.substring(0,7)}" class="text balanceMonth" name="balanceMonth" onfocus="WdatePicker({dateFmt: 'yyyy-MM'});"  btn-fun="clear" />
						</td>
						<th>${message("申请日期")}:</th>
						<td>
							<input type="text" class="text" name="applyDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${nowDate}"  />
						</td>
					</tr>
					<tr>
						<th>
							<span class="red">*</span>
							${message("GL日期")}:
						</th>
		    			<td>
		    				<input id="startTime" name="glDate" class="text" value="${nowDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
		    			</td>
		    			<th>${message("来源单号")}:</th>
						<td><span></span></td>
						<th>${message("来源账户")}:</th>
						<td><span></span></td>
						<th>${message("可用余额")}:</th>
		                <td><span class="red" id="balance"></td>
				    </tr>
				    <tr>
				   		<th>${message("单据状态")}:</th>
						<td>
							<b class="blue">${message("未处理")}</b>
						</td>
				   	    <th>${message("流程状态")}:</th>
						<td>
							<b class="blue">${message("未启动")}</b>
						</td>
						<th>${message("创建人")}:</th>
						<td></td>
						<th>${message("创建日期")}:</th>
						<td></td>
					</tr>
					<tr>
						<th>${message("申请备注")}:</th>
						<td colspan="7">
							<textarea name="memo" class="text" id="memo"></textarea>
						</td>
					</tr>
			</table>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
					${message("收款账号列表")}:
					<div class="btns">
						<a href="javascript:void(0);" id="addBankCard" class="button">${message("选择收款账号")}</a>
					</div>
				</div>
				<table id="table-bankCard"></table>
			</table>
			<div class="title-style">
				${message("全链路信息")}
			</div>
			<table id="table-full"></table>
			<div class="title-style">
				${message("附件信息")}
				<div class="btns">
					<a href="javascript:;" id="addAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>