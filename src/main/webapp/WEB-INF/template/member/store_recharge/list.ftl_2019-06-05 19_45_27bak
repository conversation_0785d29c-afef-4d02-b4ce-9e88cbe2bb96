<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("余额充值申请列表")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />	
<script type="text/javascript">
function initWfStates(){
		wfStates = {};
		[#list wfStates as wfState]
			wfStates['${wfState}'] = '${message('22222222'+wfState)}';
		[/#list]
	}
function add(){
	parent.change_tab(0,'add.jhtml?flag=${flag}&sbuId=${sbuId}');
}

function accualAmount(t){
var accualAmount=0;
var amount=0;
var $input = $("input.samount");
 $input.each(function(){
 var $this = $(this);
		var $tr = $this.closest("tr");
    accualAmount=accualAmount+Number($this.closest("tr").find("input[name='samount']").val());
    amount=amount+Number($this.closest("tr").find("input[name='sprice']").val());
 		
 });
  
 $(".accoualAmount").text(currency(accualAmount,true));
 $(".amount").text(currency(amount,true));
}

$().ready(function() {

	/**初始化多选的下拉框*/
	initMultipleSelect();

	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
	
	//查询用户
	$("#selectStoreMember").bindQueryBtn({
		type:'creator',
		title:'${message("查询创建人")}',
		url:'/member/store_member/select_store_member.jhtml'
	});
	
	
	initWfStates();
	var sr_status = {'0':'<span class="green">${message("待审核")}</span>','1':'<span class="red">${message("未通过")}</span>','2':'<span class="blue">${message("通过")}</span>'};
	var cols = [
		{ title:'${message("余额充值编号")}', name:'sn' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'view.jhtml?id='+item.id+'&flag=${flag}\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("充值客户")}', name:'store_name' ,align:'center' },
		// { title:'${message("客户编码")}', name:'sn' ,align:'center' },
		{ title:'${message("客户编码")}', name:'outTradeNo' ,align:'center' },
		{ title:'${message("机构")}', name:'sale_org_name' ,align:'center' },
		{ title:'${message("sbu")}', name:'sbu_name' ,align:'center' },
		{ title:'${message("经营组织")}', name:'organization_name' ,align:'center' },
		{ title:'${message("充值金额")}', name:'amount' ,align:'center',renderer:function(val){
			var num;
			if(val!=0){
                if (val.toString().indexOf("-") != -1){
                    var jian = val.toString().substring(1,val.length);
                    var str = fmoney(jian);
                    num = "-" + str;
                } else {
                    num = fmoney(val);
                }
            }else{
				num = "0.00";
			}
			
			return '<input type="hidden" name="sprice" value="'+val+'"><span class="red">'+"￥"+num+'</span>';
		}},
		{ title:'${message("实际充值金额")}', name:'actual_amount' ,align:'center',renderer:function(val,item){
			var num;
			if(val!=0){
                if (val.toString().indexOf("-") != -1){
                    var jian = val.toString().substring(1,val.length);
                    var str = fmoney(jian);
                    num = "-" + str;
                } else {
                    num = fmoney(val);
                }
			}else{
				num = "0.00";
			}
			
			return '<input type="hidden" class="t samount" name="samount" value="'+val+'"><span class="red">'+"￥"+num+'</span>';
		}},
		{ title:'${message("充值类型")}', name:'recharge_type_value' ,align:'center' },
		{ title:'${message("单据状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result="";
			if (item.doc_status == 0) result = '<span class="blue">已保存</span>';
			else if (item.doc_status==1) result = '<span class="blue">已提交</span>';
			else if (item.doc_status == 2) result = '<span class="green">已处理</span>';
			else if (item.doc_status == 3) result = '<span class="red">已作废</span>';
			return result;
		}},
		{ title:'${message("流程状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result = wfStates[item.wf_state];
			if(result!=undefined)return result;
			
		}},
		{ title:'${message("申请日期")}', name:'apply_date' ,align:'center',renderer:function(val){
			if(val != null){
				 var str = val;
    			 return str.substring(0,10);	
			}
		}},
		{ title:'${message("银行水单号")}', name:'bank_slip' ,align:'center' },
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },
		{ title:'${message("创建人")}', name:'creator_name' ,align:'center' },
		{ title:'${message("申请备注")}', name:'memo' ,align:'center' }
	];

	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad: true,
        url: 'list_data.jhtml',
         callback:function(){
        	accualAmount();
        },
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
   <!-- 2019-05-20 冯旗 显示勾选金额与页面金额-->
     $("input.checkAll").click(function(){
		var $this = $(this);
		var rowIndex = $this.closest("tr").attr("rowIndex");
		var row = $mmGrid.row(rowIndex);
		var $tr = $("tr[rowIndex='"+rowIndex+"']");
		var $itemInputs = $tr.find("input[name='itemIds']:enabled");
		if($this.prop("checked")){
			 $itemInputs.prop("checked",true);
		}else{
			$itemInputs.prop("checked",false);
		}
		var lineCheckedNum = $("input[name='itemIds']:checked").length;
		var $orderCheckedinput = $("input[name='itemIds']:checked");
		var $checkInputs = $("input.mmg-check:checked");
		var atotal = 0;
		var stotal = 0;
	
		$checkInputs.each(function(){
			var $this=$(this);
			var $tr = $this.closest("tr");
			var sprice = Number($tr.find("input[name='sprice']").val());
			stotal = stotal+sprice;
			
			var samount = Number($tr.find("input[name='samount']").val());
			atotal = atotal+samount;
			
		})
		
		$(".CheckedAccountLine").text(currency(stotal,true));
		$(".CheckedSamount").text(currency(atotal,true));
		
	});
    $(".mmg-check").live("click", function(){
		var $this = $(this);
		var rowIndex = $this.closest("tr").attr("rowIndex");
		var row = $mmGrid.row(rowIndex);
		var $tr = $("tr[rowIndex='"+rowIndex+"']");
		var $itemInputs = $tr.find("input[name='itemIds']:enabled");
		if($this.prop("checked")){
			 $itemInputs.prop("checked",true);
		}else{
			$itemInputs.prop("checked",false);
		}
		var lineCheckedNum = $("input[name='itemIds']:checked").length;
		var $orderCheckedinput = $("input[name='itemIds']:checked");
		var $checkInputs = $("input.mmg-check:checked");
		var atotal = 0;
		var stotal = 0;
	
		$checkInputs.each(function(){
			var $this=$(this);
			var $tr = $this.closest("tr");
			var sprice = Number($tr.find("input[name='sprice']").val());
			stotal = stotal+sprice;
			
			var samount = Number($tr.find("input[name='samount']").val());
			atotal = atotal+samount;
			
		})
		
		$(".CheckedAccountLine").text(currency(stotal,true));
		$(".CheckedSamount").text(currency(atotal,true));
		
	});
	
	
	$("p.checked-p").css("line-height","19px");
	$("p.checked-p").append('<br> 当前页计划充值金额 <span class="amount" style="color:red">0</span>;');
	$("p.checked-p").append(' 当前页实际充值金额 <span class="accoualAmount" style="color:red">0</span>;');
	$("p.checked-p").prepend('当前勾选计划充值金额合计：  <span class="CheckedAccountLine red">0</span>；');
	$("p.checked-p").prepend('当前勾选实际充值金额合计：  <span class="CheckedSamount red">0</span>；');
	
});

//条件导出		    
function segmentedExport(e){
	var needConditions = false;//
	var page_url = 'to_condition_export.jhtml';//分页导出统计页面
	var url = 'condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}
function fmoney(s, n) { 
	n = n > 0 && n <= 20 ? n : 2; 
	s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + ""; 
	var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1]; 
	t = ""; 
	for (i = 0; i < l.length; i++) { 
	t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : ""); 
	} 
	return t.split("").reverse().join("") + "." + r; 
	 
	}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的发货单！")}';//提示
	var url = 'selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<input type="hidden" name="flag" value="${flag}">
		
		<div class="bar">
				<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
						<a href="javascript:void(0);" class="iconButton" id="export1Button">
							<span class="impIcon">&nbsp;</span>导入导出
						</a>
						<ul class="flag-list">
							<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
							<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>

						</ul>
					</div>
				<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>	
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl><dd><input type="hidden" name="sbuId" value="${sbuId}"></dd></dl>
        		<dl>
        			<dt><p>${message("余额充值编号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="sn" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("充值客户")}：</p></dt>
        			<dd >
        				<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" id="storeId" btn-fun="clear" />
							<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" id="storeName" />
							<input type="button" class="iconSearch" value="" id="selectStore">
						</span>
        			</dd>
        		</dl>
        		<dl>
        			<dt ><p>${message("单据状态")}：</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="${message("已保存")};${message("已提交")};${message("已处理")}" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			<label><input  class="check js-iname" name="docstatus" value="0" type="checkbox" checked/>${message("已保存")}</label>
				       			<label><input  class="check js-iname" name="docstatus" value="1" type="checkbox" checked/>${message("已提交")}</label>
				       			<label><input  class="check js-iname" name="docstatus" value="2" type="checkbox" checked/>${message("已处理")}</label>
				       			<label><input  class="check js-iname" name="docstatus" value="3" type="checkbox"/>${message("已作废")}</label>
				      		</div>
						</div>
        			</dd>
        		</dl>
        		[#-- [#if flag == 1]
        		<dl>
        			<dt ><p>${message("充值类型")}：</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						    <input type="text" class="text pointer doStatus"  value="" autocomplete="off" />
						    <div class="statusList cs-box" data-value="off">
						    	[#list rechargeTypes as rechargeType]
						    	[#if rechargeType.value!="财务录入" ]
						       	<label><input class="check js-iname" name="rechargeTypeId" value="${rechargeType.id}" type="checkbox"/>${rechargeType.value}</label>
						       	[/#if]
						       	[/#list]
						     </div>
						</div>
        			</dd>
        		</dl>
        		[/#if] --]
        		[#list rechargeTypes as rechargeType]
					[#if rechargeType.value="客户充值" ||rechargeType.value="财务录入" ]
						<input class="check js-iname" name="rechargeTypeId" value="${rechargeType.id}" type="hidden"/>
					[/#if]
				[/#list]
        		<dl>
        			<dt><p>${message("充值金额")}：</p></dt>
        			<dd class="date-wrap">
        				<input type="text" class="text"  name="minPrice" btn-fun="clear" />
        				<div class="fl">--</div>
        				<input type="text" class="text"  name="maxPrice" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("创建人")}：</p></dt>
        			<dd>
        				<span class="search" style="position:relative">
							<input type="hidden" name="creatorId" class="text creatorId" id="creatorId" btn-fun="clear" />
							<input type="text" name="creatorName" class="text creatorName" maxlength="200" onkeyup="clearSelect(this)" id="creatorName" />
							<input type="button" class="iconSearch" value="" id="selectStoreMember">
						</span>
        			</dd>
        		</dl>
        		<dl>
					<dt><p>${message("创建时间")}:</p></dt>
					<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
					</dd>
				</dl>
			</div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
			</div>	
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	   		<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
		</form>
	</body>
</html>