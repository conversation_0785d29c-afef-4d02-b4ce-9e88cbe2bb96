<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("客户余额充值申请 ")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
</style>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,2,true,e)
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $submitButton = $("#submitButton");
	$("input[name='image']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
/* 	
	var d = new Date();
	var month=d.getMonth()+1;
	$("input.balanceMonth").val(d.getFullYear()+"-"+(month<10?'0'+month:month)); */
	
	// 表单验证
	$inputForm.validate({
		rules: {
			storeName: {
				required: true
			},
			amount: {
				required: true,
				//min:0.001,
				decimal: {
					integer: 12,
					fraction: ${setting.priceScale}
				}
			},
			balanceMonth: {
				required: true
			},
	/* 		bankSlip:{
				required: true
			}, */
			bankCardNo:{required: true}
		} 
	});
	
	/**初始化订单附件*/
    var depositAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues['depositAttachs['+depositAttachIndex+'].url']=url;
			hideValues['depositAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName:'depositAttachs['+depositAttachIndex+'].name',
				hideValues:hideValues
			});               
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="depositAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			depositAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
    
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
     var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
    
    	var cols = [				
    	{ title:'${message("支付单编号")}', name:'sn' ,width:120, align:'center'},
		{ title:'${message("支付单类型")}', name:'type' ,align:'center'},
		{ title:'${message("支付方式")}', name:'method' ,align:'center'},
		{ title:'${message("终端类型")}', name:'term_type' ,align:'center'},
		{ title:'${message("付款类型")}', name:'pay_type' ,align:'center'},
		{ title:'${message("付款金额")}', name:'amount' ,align:'center' ,renderer:function(val,item,rowIndex){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		{ title:'${message("交易流水号")}', name:'trade_no',width:120 ,align:'center'},
		{ title:'${message("支付状态")}', name:'status' ,align:'center', renderer: function(val,item,rowIndex){
			return statuss[val];
		}},
		{ title:'${message("付款日期")}', name:'payment_date',width:120 ,align:'center'}
	];
	$('#table-payment').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true,
    });
	
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
	
	$("form").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'view.jhtml?id='+resultMsg.objx;
			});
		}
	 });
	
	//查询客户
	$("#selectStore").click(function(){
    	var saleOrgId = $(".saleOrgId").val();
    	if(saleOrgId==''){
    		$.message_alert('请选择机构');
    		return false;
    	}
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			bindClick:false,
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$(".storeName").val(row.name);
					$(".storeId").val(row.id);
				
					$(".saleOrgName").val(row.sale_org_name);
					$(".saleOrgId").val(row.sale_org_id);
					$("#outTradeNo").html(row.out_trade_no);
					selectBal();
				}
			}
		});
	});
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$(".saleOrgId").val(row.id);
				$(".saleOrgName").val(row.name);
				$(".bankCardNo").val('');
				$(".bankCardId").val('');
				$(".bankName").text('');
			/* 	$("#organization").text('');
				$(".organization").val(''); */
				ajaxSubmit($(row.id),{
					method:'post',
					url:'/finance/returnBill/select_bankcard.jhtml',
					data:{id:row.id},
					callback:function(resultMsg) {
						var rowss = resultMsg.objx;
						if(rowss!=null){
						//	$("input[name='bankCardNo']").val(rowss.bankCardNo);
						//	$("input[name='bankCard.id']").val(rowss.id);
					 	  //$("#manager").prepend("<option value='"+ro.manager+"'>"+ro.manager+"</option>"); 
							selectBal();
						}
					}
				})
			}
		}
	});
	
	
	$("#selectBankCard").click(function(){
    	var saleOrgId = $(".saleOrgId").val();
    	if(saleOrgId==''){
    		$.message_alert('请选择机构');
    		return false;
    	}
    	var storeId = $(".storeId").val();
    	if(storeId==''){
    		$.message_alert('请选择充值客户');
    		return false;
    	}
    	var sbuId = $(".sbuId").val();
		$("#selectBankCard").bindQueryBtn({
			type:'bankCard',
			bindClick:false,
			title:'${message("查询收款账户")}',
			url:'/member/bankCard/select_bank_card.jhtml?saleOrgId='+$(".saleOrgId").val()+"&sbuId="+sbuId,
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$(".bankCardNo").val(row.bank_card_no);
					$(".bankCardId").val(row.id);
					$(".bankName").text(row.bank_name);
					if (row.organization_name !=null && row.orgId !=null) {
						$("#organization").text(row.organization_name);
						$(".organization").val(row.orgId);
					}
					selectBal();
				}
			}
		});
	});
	
	$("#rechargeTypeId").change(function(){
		if($("#rechargeTypeId").val()==340){
			$("#paybutton").show();
		}else{
			$("#paybutton").hide();
		}
	})
});
function pay(){
		var num=$("input[name='amount']").val();
		if(num==null ||num=='' ||num==undefined){
		$.message_alert("请填写计划充值金额");
		return false;
		}
		$("#payamount").val(num);
		$("#payForm").submit();
	}

function clearSelect(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("div.search");
		$this.prev("input").val("");
		$(".bankName").text('');
	}
}
function selectBal(){
	var ssId = $(".storeId").val();
	var sbuId = $("#sbuId").val();
	var saleOrgId = $(".saleOrgId").val();
	var orgId = $(".organization").val();
	ajaxSubmit("",{
        method:'post',
        url:'/finance/balance/get_balance.jhtml',
        data:{storeId:ssId,sbuId:sbuId,saleOrgId:saleOrgId,organizationId:orgId},
        callback:function(resultMsg) {
            var data = resultMsg.objx;
            if(data!=null){
	            //可用余额
	            $("#balance").text(currency(data.balance,true));            	
            }
        }
    });
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增客户余额充值申请")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">

		<div class="tabContent">
			<table class="input input-edit">
			<tr>
				<th>
					${message("余额充值编号")}:
				</th>
				<td>
				</td>
				<th>
					<span class="requiredField">*</span>${message("充值客户")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeId" class="text storeId" value="[#if storeMember.memberType == 1]${store.id}[/#if]" btn-fun="clear"/>
					<input type="text" name="storeName" class="text storeName" value="[#if storeMember.memberType == 1]${store.name}[/#if]" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore">
					</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("充值金额")}:
				</th>
				<td>
					<div class="nums-input ov">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
						<input type="text"  class="t"  name="amount" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>				
				</td>
				<th>
					${message("实际充值金额")}:
				</th>
				<td>
					
				</td>
			</tr>
			<tr>
				<th>${message("机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${saleOrg.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td>
				<th>${message("客户编码")}:</th>
				<td>
				   <span id="outTradeNo"></span>
				</td>
				<th>${message("流程状态")}:</th>
				<td>
					<b class="blue">${message("未启动")}</b>
				</td>
				<th>
					<span class="requiredField">*</span>${message("充值类型")}:
				</th>
				<td>
					<select id="rechargeTypeId" name="rechargeTypeId" class="text">
						[#list rechargeTypes as rechargeType]
							[#if flag!=1 && rechargeType.value=="客户充值"]
								<option value="${rechargeType.id}">${rechargeType.value}</option>
								[#break]
							[#else]
								<option value="${rechargeType.id}">${rechargeType.value}</option>
							[/#if]
						[/#list]
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("对账月份")}:
				</th>
				<td>
					<input type="hidden" value="${nowDate.substring(0,7)}" class="text balanceMonth" name="balanceMonth" onfocus="WdatePicker({dateFmt: 'yyyy-MM'});"  btn-fun="clear" />
				</td>
				<th>
					<span class="requiredField">*</span>
					${message("收款账户")}:
				</th>
				<td>
					<span class="search" style="position:relative">
						<input type="hidden" name="bankCard.id" class="text bankCardId" btn-fun="clear" value="${bankCard.id}"/>
						<input type="text" class="text bankCardNo" name="bankCardNo" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectBankCard">
					</span>
				</td>
				<th>
					${message("收款银行")}:
				</th>
				<td>
					<span class="bankName"></span>
				</td>
				<th>
					${message("申请日期")}:
				</th>
				<td>
					<input type="text" class="text" name="applyDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${nowDate}"  />
				</td>
			</tr>
			<tr>
			 <th>${message("Sbu")}:</th>
             <td>
            	<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbu.id}"/>
            	<span   id="sbuName">
            	 ${sbu.name}
            	</span>
             </td>
				<!-- <th>
		              <span class="requiredField">*</span>${message("sbu")}:
            	  </th>
           		<td>
            		<select id="sbu" name="sbuId" class="text">
						[#list sbus as sbu]
						<option value="${sbu.sbu.id}"[#if sbuIds==sbu.sbu.id]selected[/#if]>${sbu.sbu.name}</option>
						[/#list]
					</select>
            	</td> -->
				<th>
					${message("创建人")}:
				</th>
				<td></td>
				<th>
					${message("创建日期")}:
				</th>
				<td></td>
				<th>
				
				${message("银行水单号")}:</th>
				<td>
				<input type="text" class="text bankSlip" name="bankSlip" value="" btn-fun="clear" />
				</td>
				
			</tr>
			<tr>
				<th>${message("汇款人")}:</th>
				<td>
				<input type="text" class="text remitter" name="remitter" value="" btn-fun="clear" />
				</td>
				<th>${message("汇款帐号")}:</th>
				<td>
				<input type="text" class="text remittanceAccount" name="remittanceAccount" value="" btn-fun="clear" />
				</td>
				<th>${message("经营组织")}:</th>
				<td>
					<span id="organization"></span>
					<input type="hidden" class="text organization" name="organizationId"  btn-fun="clear"  />
				</td>
				<th>${message("到款状态")}:</th>
				<td>
					<b class="blue">${message("未到账")}</b>
				</td>
			</tr>
			<tr>
				  <th>${message("可用余额")}:</th>
                  <td><span class="red" id="balance"></td>
			</tr>
<!--				<th class="noBg"></th><td></td>		--!>
			</tr>
			<tr>
				<th>${message("单据状态")}:</th>
				<td>
					<b class="blue">${message("未处理")}</b>
				</td>
			</tr>
			<tr>
				<th>
					${message("申请备注")}:
				</th>
				<td colspan="7">
					<textarea name="memo" class="text" id="memo"></textarea>
				</td>
			</tr>
		</table>
		<div class="title-style">
			${message("全链路信息")}
		</div>
		<table id="table-full"></table>
		<div class="title-style">
			${message("附件信息")}
			<div class="btns">
				<a href="javascript:;" id="addAttach" class="button">添加附件</a>
			</div>
		</div>
		<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			[#--
			<input type="button" id="paybutton" style="display:none" class="button sureButton" value="${message("付款")}" onclick="pay(this)" />
			--]
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
	<!-- 支付跳页面  -->
	<form id="payForm"  target="_blank" action="submit.jhtml"  method="post" >
		<input id="payamount" name="payamount" type="hidden" value="">
		<input id="payamount" name="payname" type="hidden" value="客户余额充值">
	</form>
	<!--  end   -->
</body>
</html>