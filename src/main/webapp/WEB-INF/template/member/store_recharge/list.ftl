<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("余额充值申请列表")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/mmGridConfiguration.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/formList.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>	
<script type="text/javascript">
	$().ready(function() {
		$("p.checked-p").css("line-height","38px");
		$("p.checked-p").prepend('当前勾选实际充值金额合计为：  <span class="CheckedSamount red">0</span>；');
		$("p.checked-p").append(' 当前页总实际充值金额为： <span class="accoualAmount" style="color:red">0</span>;');

		//获取按钮控件
		getButtonHtml(0,'${dTemplates.id}');
		//获取筛选html
		parameterList('${userId}','${dTemplates.id}');
		//遍历列表表头
		//traverseList('${userId}','${dTemplates.id}','${defaultQuery}','${dTemplates.pdfPath}','${dTemplates.excelPath}',null,null,'actual_amount');
		traverseList('${userId}','${dTemplates.id}','${defaultQuery}','${dTemplates.pdfPath}','${dTemplates.excelPath}',null,null);

	});
	//查看
	function edit(id){
		parent.change_tab(0,'view.jhtml?id='+id);
	}
	//新增
	function add(e){
		parent.change_tab(0,'add.jhtml?flag=${flag}&sbuId=${sbuId}');
	}
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<input type="hidden" name="userId" class="userId"  value="${userId}"/>
		<input type="hidden" name="templateId" class="templatesId"  value="${dTemplates.id}"/>
		<input type="hidden" name="pagesize" class="pagesize" value="${pagesize}"/>
		<input type="hidden" name="sb_sbu_id" class="sb_sbu_id" value="${sbuId}"/>
		<input type="hidden" name="rechargeTypeIds" class="rechargeTypeIds" value="${rechargeTypeIds}"/>
		<div class="bar">
			<div class="buttonWrap"></div>
			<div id="searchDiv">
	        	<div id="search-content" >
	        		<table id="search-table"></table>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>	
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	   		<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>

        <div class="floatDiv" style="position: fixed; top: 0; right: 420px; height: 38px; text-align: right;">
            <p class="checked-p" style="line-height: 38px; font-size: 14px;"></p>
        </div>
		</form>
	</body>
</html>