<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("用户余额充值申请查看")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script>
$().ready(function() {
	var payment_types = {'0':'${message("第三方平台订单支付")}','1':'${message("用户余额充值")}','2':'${message("用户余额订单支付")}','3':'${message("客户余额充值")}','4':'${message("客户余额订单支付")}','5':'${message("供应商订单支付")}'};
    var methods = {'0':'${message("在线支付")}','1':'${message("线下支付")}'};
    var termTypes = {'0':'${message("APP")}','1':'${message("微商城")}','2':'${message("PC商城")}','3':'${message("PC后台")}'};
    var payTypes = {'0':'${message("用户余额")}','1':'${message("客户余额")}','2':'${message("微信")}','3':'${message("支付宝")}','4':'${message("银联")}','5':'${message("临时额度")}'};
    var statuss = {'0':'${message("等待支付")}','1':'${message("支付成功")}','2':'${message("支付失败")}'}
    
    var payment_items = ${payment_json};
    	var cols = [				
    	{ title:'${message("支付单编号")}', name:'sn' ,width:120, align:'center'},
		{ title:'${message("支付单类型")}', name:'type' ,align:'center', renderer: function(val,item,rowIndex){
			return payment_types[val];
		}},
		{ title:'${message("支付方式")}', name:'method' ,align:'center', renderer: function(val,item,rowIndex){
			return methods[val];
		}},
		{ title:'${message("终端类型")}', name:'term_type' ,align:'center', renderer: function(val,item,rowIndex){
			return termTypes[val];
		}},
		{ title:'${message("付款类型")}', name:'pay_type' ,align:'center', renderer: function(val,item,rowIndex){
			return payTypes[val];
		}},
		{ title:'${message("付款金额")}', name:'amount' ,align:'center' ,renderer:function(val,item,rowIndex){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		{ title:'${message("交易流水号")}', name:'trade_no',width:120 ,align:'center'},
		{ title:'${message("支付状态")}', name:'status' ,align:'center', renderer: function(val,item,rowIndex){
			return statuss[val];
		}},
		{ title:'${message("付款日期")}', name:'payment_date',width:120 ,align:'center'}
	];
	$('#table-payment').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:payment_items,
        checkCol: false,
        autoLoad: true,
    });
    
    
});
function check(e,flag){
	var $this = $(e);
	var $form = $("#inputForm");
	var content = '';
	if(flag==1){
		content = '您确定要审核通过吗？';
	}else{
		content = '您确定要审核驳回吗？';
	}
	if($form.valid()){
		$.message_confirm(content,function(){
			var url="check.jhtml";
			var data = $form.serialize()+"&flag="+flag;
			ajaxSubmit($this,{
				 url: url,
				 method: "post",
				 data: data,
				 callback:function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					});
				 }
			})
		});
	}
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看用户余额充值申请")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${dr.id}">
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("创建人")}:
				</th>
				<td>
					${dr.creator.username}
				</td>
				<th>
					${message("充值用户")}:
				</th>
				<td>
					${dr.storeMember.username}
				</td>
				<th>
					${message("申请日期")}:
				</th>
				<td>
					<span>${dr.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
				</td>
				<th>${message("审核状态")}:</th>
				<td><b class="green">
					[#if dr.status == "wait"]${message("待审核")}[/#if]
        			[#if dr.status == "failure"]${message("未通过")}[/#if]
        			[#if dr.status == "success"]${message("通过")}[/#if]
				</b></td>
			</tr>
			<tr>
				<th>
					${message("计划充值金额")}:
				</th>
				<td>
					<span class="red">${currency(dr.amount, true)}</span>
				</td>
				<th>
					${message("实际充值金额")}:
				</th>
				<td>
					[#if dr.status=="failure" || dr.status=="success"]
						<span class="red">${currency(dr.actualAmount, true)}</span>
					[#else]
						-						
					[/#if]
				</td>
				<th class="noBg"></th><td></td>
				<th class="noBg"></th><td></td>
			</tr>
			<tr>
				<th>
					${message("12013")}:
				</th>
				<td colspan="7">
					<textarea class="text" disabled>${dr.memo}</textarea>
				</td>
			</tr>
			<tr style="height:88px">
				<th>
					${message("附件")}:
				</th>
				<td colspan="7">
					[#if dr.image??]
						<span class="ico-options" style="margin-right:15px;display: inline-block;">
							<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">
							<div class="pic"><a href="${dr.image}" target="_blank" style="width: 80px;">
                        		<img src="${dr.image}" style="width: 80px; height: 80px;">
                        	</a></div>
                        	</div>
                        	<input type="hidden" name="topIcon" value="${dr.image}"/>
						</span>
					[/#if]
					[#if dr.image2??]
						<span class="ico-options" style="margin-right:15px;display: inline-block;">
							<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">
							<div class="pic"><a href="${dr.image2}" target="_blank" style="width: 80px;">
                        		<img src="${dr.image2}" style="width: 80px; height: 80px;">
                        	</a></div>
                        	</div>
                        	<input type="hidden" name="topIcon" value="${dr.image2}"/>
						</span>
					[/#if]
					[#if dr.image3??]
						<span class="ico-options" style="margin-right:15px;display: inline-block;">
							<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">
							<div class="pic"><a href="${dr.image3}" target="_blank" style="width: 80px;">
                        		<img src="${dr.image3}" style="width: 80px; height: 80px;">
                        	</a></div>
                        	</div>
                        	<input type="hidden" name="topIcon" value="${dr.image3}"/>
						</span>
					[/#if]
				</td>
			</tr>
			[#if dr.status?? && dr.status!="wait"]
				<tr class="border-L2">
					<th>
					${message("审核人")}:
				</th>
				<td>
					${dr.operator.username}
				</td>
				<th>
					${message("审核日期")}:
				</th>
				<td colspan="5">
					${dr.checkDate?string("yyyy-MM-dd HH:mm:ss")}
				</td>
				</tr>
				<tr  class="border-L2">
				<th>
					${message("16062")}:
				</th>
				<td colspan="7">
					${dr.note}
				</td>
			</tr>
			[/#if]
			<tr class="border-L3">
				<th>支付明细</th>
				<td colspan="7"></td>
			</tr>
			<tr class="border-L3">
				<td colspan="8">
					<div>
						<table id="table-payment"></table>
					</div>
				</td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			[#if isCheck==1 && dr.status=='wait']
				<input type="button" class="button sureButton" value="${message("审核")}" onclick="check(this,1)" />
				<input type="button" class="button cancleButton" value="${message("驳回")}" onclick="check(this,2)" />
			[/#if]
			[#if isCheck!=1]
				[#if isAdd!=1]
				<a href="add.jhtml" class="iconButton" id="addButton">
	                <span class="addIcon">&nbsp;</span>${message("1001")}
				</a>
				[/#if]
			[/#if]
		    
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
		
	</form>
</body>
</html>