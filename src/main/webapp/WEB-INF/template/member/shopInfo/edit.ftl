<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("客户信息")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e);
}
$().ready(function() {
	
	$.validator.addClassRules({
		//areaId_select: {required: true},
		distributorName: {required: true},
		distributorPhone: {required: true},
		administrativeRank: {required: true},
		acreage: {required: true},
		businessForm: {required: true},
		//contactPhone: {required: true},
		shopSign: {required: true},
		department: {required: true},
		//salesVolume: {required: true},
		department: {required: true},
		//addTime: {required: true},
		//lastCheckTime: {required: true},
		//shutDownMenu: {required: true},
		//activationTypeId: {required: true},
		//activationTime: {required: true},
		storeId: {required: true},
		storeName: {required: true},
		//authorizationCode: {required: true},
		distributorName: {required: true},
		//openDate: {required: true},
		//joinDate: {required: true},
		distributorPhone: {required: true},
		//tel: {required: true},
		//fax: {required: true},
		positionType: {required: true},
		address: {required: true},
		//salesPlatform: {required: true},
		//decorationDate: {required: true},
		salesChannel: {required: true},
		//salesChannelNote: {required: true},
		status: {required: true},
		//statusNote: {required: true},
		//increaseArchivesCode: {required: true},
		//decreaseArchivesCode: {required: true},
		viVersion: {required: true}
		//increaseArchivesCode: {required: true}
	});
	 $("#wf_area").load("/wf/wf.jhtml?wfid=${shopInfo.wfId}");

	// 地区选择
	$("#areaId").lSelect();

	initProductTypeMmgrid();
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			});
	    }
	 });
	//查询产品分类
	$("#addStoreProductType").bindQueryBtn({
		type:'productType',
		title:'${message("查询产品分类 ")}',
		url:'/product/product_category/findTopCategory.jhtml',
		callback:function(row){
			$mmGrid2.addRow(row);
		}
	});
	//查询客户
	$("#selectStore").click(function(){
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			bindClick:false,
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
			callback:function(rows){
				if(rows.length>0){
					//更新主表数据信息
					var row = rows[0];
					$(".storeName").val(row.name);
					$(".storeId").val(row.id);
					$(".salesPlatform").val(row.sale_org_name);
					$("input.franchisee").val(row.franchisee);
					var joinValue = row.active_date;
					if(joinValue){
						$("input.joinDate").val(row.active_date.substring(0,10));
					}else{
						$("input.joinDate").val(joinValue);
					}
					$("input.distributorName").val(row.dealer_name);
					$("input.distributorPhone").val(row.fixed_number);
					$("input.fax").val(row.fax_number);
					$("input.tel").val(row.fixed_number);
					showShopDesign();//更新门店设计
				}
			}
		});
	});
	
	
	 function deleteProductType(e){
			var index = $(e).closest("tr").index();
			$.message_confirm('您确定要删除吗？',function(){
				$mmGrid2.removeRow(index);
			})
		}
	 
	 /**门店设计*/
	 var countaddShopDesign = 0;
	 $("#addShopDesign").click(function(){
			var row={};
			$mmGrid1.addRow(row);
			showShopDesign();
		})
	
	function showShopDesign(){
		areas=	parseInt(countaddShopDesign) - 1;
		$("#shopDesignsAreaId"+ areas).lSelect();
		var $phoneVal = $("input.distributorPhone").val();
		$("input.shopDesignsPhone").attr("value",$phoneVal);
		var $joinDateVal = $("input.joinDate").val();
		$("input.shopDesignsJoinYear").attr("value",$joinDateVal);
		var $openDateVal = $("input.openDate").val();
		$("input.shopDesignsBuiltShopYear").attr("value",$openDateVal);
		var $distributorNameVal = $("input.distributorName").val();
		$("input.shopDesignsDealer").attr("value",$distributorNameVal);
		var $franchiseeVal = $("input.franchisee").val();
		$("input.exclusiveDistribution").attr("value",$franchiseeVal);
	}
	 
		[#if shopDesigns != null]
	 var shopDesign_items = ${shopDesigns};
	     [#else]
	    var  shopDesign_items = [];
	    [/#if]
	 var shopDesign_cols = [	
    		{ title:'${message("设计状态")}', align:'center',name:'design_statu',width:130 ,renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].designStatu" class="text">'
    		    +'<option value="设计中"';
    			if (val != '' && val == '设计中') {
			html += ' selected="selected"';
		}
    			html +='>设计中</option>';
    			html +='<option value="装修中" ';
    			if (val != '' && val == '装修中') {
			html += ' selected="selected"';
		}
    			html +='>装修中</option>';
    			html +='<option value="正常营业"';
    			if (val != '' && val == '正常营业') {
			html += ' selected="selected"';
		}
    			html +='>正常营业</option>';
    			html +='<option value="暂停设计"';
    			if (val == '暂停设计') {
			html += ' selected="selected"';
		}
    			html +='>暂停设计</option>';
    				html +='<option value="暂停装修"';
    			if (val != '' && val == '暂停装修') {
			html += ' selected="selected"';
		}
    			html +='>暂停装修</option>';
    			html +='</select>';
    		    return html;
    		}},
    		{ title:'${message("设计-业态 ")}',name:'design_version', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].designVersion" value="'+val+'" class="text" />';
    		}},
    		{ title:'${message("门头标示")}',name:'door_head_mark',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].doorHeadMark" class="text">'
    		    +'<option value="大自然"';
    			if (val != '' && val == '大自然') {
			html += ' selected="selected"';
		}
    			html +='>大自然</option><option value="竹地板"';
    		  if (val != '' && val == '竹地板') {
			html += ' selected="selected"';
		}
    		html +='>竹地板</option></select>';
    		    return html;
    		}},
    		{ title:'${message("市场性质")}',name:'market_nature',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].marketNature" value="'+val+'" class="text" />'
    		}},
    		{ title:'${message("门店面积 ")}',name:'store_area',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			var html = '<div class="nums-input ov fl" style="width: 163px;"> '
    				+' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">'
    				+' <input type="text"  class="t"  name="shopDesigns['+countaddShopDesign+'].storeArea" value="'+val+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
    				+' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">'
    				+' </div>';
    			return html;
    		}},
    		{ title:'${message("总经销 ")}',name:'exclusive_distribution',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].exclusiveDistribution" value="'+val+'" class="text exclusiveDistribution" />';
    		}},
    		{ title:'${message("经销商 ")}',name:'dealer', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].dealer" class="text shopDesignsDealer" value="'+val+'"/>';
    		}},
    		{ title:'${message("加盟年份")}',name:'join_year',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input name="shopDesigns['+countaddShopDesign+'].joinYear" class="text shopDesignsJoinYear" value="'+val.substring(0,10)+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
    		}},
    		{ title:'${message("手机号码 ")}',name:'phone',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].phone" class="text shopDesignsPhone" value="'+val+'" />';
    		}},
    		{ title:'${message("建店年份 ")}',name:'built_shop_year', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input name="shopDesigns['+countaddShopDesign+'].builtShopYear" class="text shopDesignsBuiltShopYear" value="'+val.substring(0,10)+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
    		}},
    		{ title:'${message("门店所属品牌")}',name:'belong_brand',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].belongBrand" class="text">'
    		    +'<option value="大自然"';
     		  if (val != '' && val == '大自然') {
				html += ' selected="selected"';
			}
     		html +='>大自然</option><option value="木香居"';
    		     if (val != '' && val == '木香居') {
			html += ' selected="selected"';
		}
    		   html +='>木香居</option><option value="nature"';
    		   if (val != '' && val == 'nature') {
			html += ' selected="selected"';
		}
    		 html +='>nature</option><option value="国际出口"';
     	   if (val != '' && val == '国际出口') {
		    html += ' selected="selected"';
		}
     	 html +='>国际出口</option></select>';
    		    return html;
    		}},
    		{ title:'${message("设计申请时间")}',name:'application_time',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].applicationTime" value="'+val.substring(0,10)+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" class="text" />'
    		}},
    		{ title:'${message("门店装修属性")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].decProperty" class="text">'
    		    +'<option value="新装"';
     		   if (val != '' &&val == '新装') {
				html += ' selected="selected"';
			}
     		 html +='>新装</option><option value="重装"';
    		      if (val != '' && val == '重装') {
				html += ' selected="selected"';
			}
    		    html +='>重装</option></select>';
    		    return html;
    		}},
    		{ title:'${message("设计门店地址")}',name:'design_address', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].designAddress" class="text" value="'+val+'"/>'
    		}},
    		{ title:'${message("门店位置类型")}',name:'location_type', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].locationType" class="text">'
    		    +'<option value="1"';
    		      if (val != '' && val == '1') {
				html += 'selected="selected"';
			}
    		    html +='>建材市场</option><option value="2"';
		      if (val != '' && val == '2') {
			html += ' selected="selected"';
		}
    		  html +='>商场</option><option value="3"';
		      if (val != '' && val == '3') {
			html += ' selected="selected"';
		}
		      html +='>临街市场</option><option value="4"';
 		      if (val != '' && val == '4') {
				html += ' selected="selected"';
			}
 		  html +='>装饰公司</option></select>';
    		    return html;
    		}},
    		{ title:'${message("是否返利")}',name:'is_rebate', align:'center',renderer: function(val,item,rowIndex){
	  			var html = '<select name="shopDesigns['+countaddShopDesign+'].isRebate" class="text">'
			    +'<option value="1"';
			      if (val != '' && val == '1') {
				html += 'selected="selected"';
				}
			    html +='>是</option><option value="2"';
		        if (val != '' && val == '2') {
				html += ' selected="selected"';
				}
			    html +='>否</option></select>';
			    return html;
    		}},
    		{ title:'${message("预计开业时间")}',name:'opening_time', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].openingTime"  onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" class="text" value="'+val.substring(0,10)+'" />';
    		}},
    		{ title:'${message("是否签合同")}',name:'is_sign_contract', align:'center',renderer: function(val,item,rowIndex){
	  			var html = '<select name="shopDesigns['+countaddShopDesign+'].isSignContract" class="text">'
			    +'<option value="1"';
			      if (val != '' && val == '1') {
				html += 'selected="selected"';
				}
			    html +='>是</option><option value="2"';
		        if (val != '' && val == '2') {
				html += ' selected="selected"';
				}
			    html +='>否</option></select>';
			    return html;
    		}},
    		{ title:'${message("是否保证金")}',name:'is_pay_deposit', align:'center',renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].isPayDeposit" class="text">'
    		    +'<option value="1"';
    		      if (val != '' && val == '1') {
				html += 'selected="selected"';
				}
    		    html +='>是</option><option value="2"';
		        if (val != '' && val == '2') {
				html += ' selected="selected"';
				}
    		    html +='>否</option></select>';
    		    return html;
    		}},
    		{ title:'${message("保证金备注")}',name:'deposit_note', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].depositNote" class="text" value="'+val+'"/>'
    		}},
    		{ title:'${message("2年内是否参与返利")}',name:'is_join_rebate_tow_year', align:'center',renderer: function(val,item,rowIndex){
	  			var html = '<select name="shopDesigns['+countaddShopDesign+'].isJoinRebateTowYear" class="text">'
			    +'<option value="1"';
			      if (val != '' && val == '1') {
				html += 'selected="selected"';
				}
			    html +='>是</option><option value="2"';
		        if (val != '' && val == '2') {
				html += ' selected="selected"';
				}
			    html +='>否</option></select>';
			    return html;
    		}},
    		{ title:'${message("是否参与返利备注")}', name:'is_join_rebate_memo',align:'center',renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].isJoinRebateMemo" class="text">'
			    +'<option value="1"';
			      if (val != '' && val == '1') {
				html += 'selected="selected"';
				}
			    html +='>是</option><option value="2"';
		        if (val != '' && val == '2') {
				html += ' selected="selected"';
				}
			    html +='>否</option></select>';
			    return html;
    		}},
    		{ title:'${message("渠道审核")}',name:'channel_audit',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].channelAudit" class="text" value="'+val+'"/>';
    		}},
    		{ title:'${message("渠道审核意见")}',name:'channel_audit_opinions', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].channelAuditOpinions" class="text" value="'+val+'"/>';
    		}},
    		{ title:'${message("设计进度")}',name:'design_schedule',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].designSchedule" class="text">'
    		    +'<option value="设计中"';
    		  if (val != '' && val == '设计中') {
			html += ' selected="selected"';
		}
    		    html +='>设计中</option>';
    			html +='<option value="已设计"';
    		  if (val != '' && val == '已设计') {
			html += ' selected="selected"';
		}
    		    html +='>已设计</option>';
    			html +='<option value="暂停设计"';
    		  if (val != '' && val == '暂停设计') {
			html += ' selected="selected"';
		}
    		    html +='>暂停设计</option>';
    			html +='<option value="取消设计"';
    		  if (val != '' && val == '取消设计') {
			html += ' selected="selected"';
		}
    		    html +='>取消设计</option>';
    			html +='</select>';
    		    return html;
    		}},
    		{ title:'${message("区域负责人跟进")}',name:'reg_leader_follow_up', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    			var html = '<select name="shopDesigns['+countaddShopDesign+'].regLeaderFollowUp" class="text">'
    		    +'<option value="装修中"';
    		  if (val != '' && val == '装修中') {
			html += ' selected="selected"';
		}
    		html +='>装修中</option>';
    			html +='<option value="暂停装修"';
    		  if (val != '' && val == '暂停装修') {
			html += ' selected="selected"';
		}
    		html +='>暂停装修</option>';
    			html +='<option value="取消装修"';
    		  if (val != '' && val == '取消装修') {
			html += ' selected="selected"';
		}
    		html +='>取消装修</option>';
    			html +='<option value="完成装修"';
    			 if (val != '' && val == '完成装修') {
				html += ' selected="selected"';
			}
    		html +='>完成装修</option>';
    			html +='</select>';
    		    return html;
    		}},
  		{ title:'${message("区域")}',name:'region', align:'center',width:130 ,renderer: function(val,item,rowIndex){
  			var html = '<select name="shopDesigns['+countaddShopDesign+'].region" class="text">'
  			+'<option value="东区"';
     		  if (val != '' && val == '东区') {
				html += ' selected="selected"';
			}
     		html +='>东区</option>';
     			html +='<option value="西区"';
     		  if (val != '' && val == '西区') {
				html += ' selected="selected"';
			}
     		html +='>西区</option>';
     			html +='<option value="南区"';
     		  if (val != '' && val == '南区') {
				html += ' selected="selected"';
			}
     		html +='>南区</option>';
     			html +='<option value="北区"';
     			 if (val != '' && val == '北区') {
					html += ' selected="selected"';
				}
     		html +='>北区</option>';
     			html +='</select>';
  		    return html;
  		}},
  		{ title:'${message("地区")}',align:'center',width:320 ,renderer: function(val,item,rowIndex){
  			if(item.area == undefined){
  				return '<input type="hidden" id="shopDesignsAreaId'+ countaddShopDesign +'" name="shopDesigns['+countaddShopDesign+'].area.id"/>';
  			}else{
  				return '<input type="hidden" id="shopDesignsAreaId'+ countaddShopDesign +'" value="'+ item.area +'" treePath="'+ item.tree_path +'" name="shopDesigns['+countaddShopDesign+'].area.id"/>';
   			//return '<input type="text" class="text" value="' + item.area + '"/>';
  			}
  		}},
  		{ title:'${message("VI版本")}',name:'vi_version',align:'center',width:130 ,renderer: function(val,item,rowIndex){
  		    return '<input type="text" class="text" name="shopDesigns['+countaddShopDesign+'].viVersion" value="'+val+'"/>';
  		}},
    		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
    			return '<a href="javascript:;" class="btn-delete" onclick="deleteShopDesign(this)">删除</a>'
    		}},
  		{name:'id', hidden:true ,renderer: function(val,item,rowIndex){
  			var html = '<input type="text" name="shopDesigns['+countaddShopDesign+'].id" class="text" value="'+val+'" />'
  			countaddShopDesign++;
  			return html;
  		}}
    		
    	];
  	  var shopDesignSize = ${shopDesignSize};
    	  $mmGrid1 = $('#table-p3').mmGrid({
  		height:'auto',
  	    cols: shopDesign_cols,
  	    items:shopDesign_items,
  	    checkCol:false,
  	    autoLoad:true,
  	    fullWidthRows: true,
  	    callback:function(){
       	for (var i=0;i<shopDesignSize;i++){
       		$("#shopDesignsAreaId"+i).lSelect();
      		}
	}
    	 });
    	  
   	 /**门店稽查*/
   	 var countaddShopInspection = 0;
   	 $("#addShopInspection").click(function(){
   			var row={};
   			 $mmGrid3.addRow(row);
   		})
   		[#if shopInspections != null]
   	     var shopInspection_items = ${shopInspections};
   	     [#else]
   	    var  shopInspection_items = [];
   	    [/#if]
    	var shopInspection_cols = [	
    	   		{ title:'${message("稽查结果")}',name:'result', align:'center',name:'result',width:130  ,renderer: function(val,item,rowIndex){			
    	   			var html = '<select name="shopInspections['+countaddShopInspection+'].result" class="text result">'
    	   				+'<option value="1"';
    	   			if (val != '' && val == '1') {
				html += ' selected="selected"';
			}
    	   		html +='>正常营业</option><option value="2"';
    	   			if (val != '' && val == '2') {
				html += ' selected="selected"';
			}
    	   		html +='>门店关闭</option><option value="3"';
			if (val != '' && val == '3') {
				html += ' selected="selected"';
			}
    	   	    html +='>门店交接</option><option value="0" ';
			if (val != '' && val == '0') {
				html += ' selected="selected"';
			}
    	      	html +='>其他(备注)</option></select>';
    	   			return html;
    	   		}},
    	   		{ title:'${message("稽查说明")}',name:'audit_description', align:'center',name:'audit_description',width:130  ,renderer: function(val,item,rowIndex){			
    	   			var html = '<select name="shopInspections['+countaddShopInspection+'].auditDescription" class="text">'
    	   			    +'<option value="1"';
    	   			if (val != '' && val == '1') {
				html += ' selected="selected"';
			}
    	   		html +='>门店搬迁 </option>';
    	   		html +='<option value="2" ';
    	   			if (val != '' && val == '2') {
				html += ' selected="selected"';
			}
    	   		html +='>经销商退出</option>';
    	   		html +='<option value="3"';
    	   			if (val == '3') {
				html += ' selected="selected"';
			}
    	   	html +='>经营不善</option>';
    	   	html +='<option value="4"';
    	   			if (val != '' && val == '4') {
				html += ' selected="selected"';
			}
    	    html +='>经销商变更</option>';
    		html +='<option value="0"';
    	   			if (val != '' && val == '0') {
				html += ' selected="selected"';
			}
    	   		html +='>其他(备注)</option> ';
    	   		html +='</select>';
    	   			return html;
    	   		}},
    	   		{ title:'${message("备注")}',name:'memo', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    	   			return '<input type="text" name="shopInspections['+countaddShopInspection+'].memo" class="text " value="'+val+'"/>';
    	   		}},
    	   		{ title:'${message("区域责任人")}',name:'regional_res_person', align:'center',width:130  ,renderer: function(val,item,rowIndex){			
    	   			var html = '<select name="shopInspections['+countaddShopInspection+'].regionalResPerson" class="text">'
    	   			    +'<option value="区域经理"';
    	   			if (val != '' && val == '区域经理') {
				html += ' selected="selected"';
			}
    	   		html +='>区域经理</option>';
    	   		html +='<option value="省长"';
    	   			if (val != '' && val == '省长') {
				html += ' selected="selected"';
			}
    	   		html +='>省长</option>';
    	   		html +='</select>';
    	   			return html;
    	   		}},
    	   		{ title:'${message("稽查日期")}',name:'audit_date', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    	   			return '<input name="shopInspections['+countaddShopInspection+'].auditDate" class="text" value="'+val.substring(0,10)+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
    	   		}},
    	   		{ title:'${message("处理情况")}',name:'handle_rusult', align:'center',width:130 ,renderer: function(val,item,rowIndex){	
    	   			var html = '<select name="shopInspections['+countaddShopInspection+'].handleRusult" class="text">'
    	   		    +'<option value="处理"';
    	   		if (val != '' && val == '处理') {
			html += ' selected="selected"';
		}
    	   	html +='>处理</option>';
    	   	html +='<option value="未处理"';
    	   		if (val != '' && val == '未处理') {
			html += ' selected="selected"';
		}
    	    html +='>未处理</option>';
    		html +='</select>';
    	   		   return html;
    	   		}},
    	   		{ title:'${message("处理日期")}',name:'handle_date',align:'center',width:130 ,renderer: function(val,item,rowIndex){
    	   			return '<input name="shopInspections['+countaddShopInspection+'].handleDate" class="text" value="'+val.substring(0,10)+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
    	   		}},
    	   		{ title:'${message("要求处理日期")}',name:'request_handle_date', align:'center',width:130 ,renderer: function(val,item,rowIndex){
    	   			return '<input name="shopInspections['+countaddShopInspection+'].requestHandleDate" class="text" value="'+val.substring(0,10)+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
    	   		}},
    	   		{ title:'${message("逾期未处理工作日")}',name:'over_work_day', align:'center',width:150,renderer: function(val,item,rowIndex){
    	   			var html = '<div class="nums-input ov fl" style="width: 163px;"> '
    	   				+' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">'
    	   				+' <input type="text"  class="t"  name="shopInspections['+countaddShopInspection+'].overWorkDay" value="'+val+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
    	   				+' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">'
    	   				+' </div>';
    	   			return html;
    	   		}},
    	   		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
    	   			return '<a href="javascript:;" class="btn-delete" onclick="deleteshopInspection(this)">删除</a>'
    	   		}},
    			{name:'id', hidden:true ,renderer: function(val,item,rowIndex){
    				var html = '<input type="text" name="shopInspections['+countaddShopInspection+'].id" class="text" value="'+val+'" />'
    				countaddShopInspection++;
    				return html;
    			}},
    	   		
    	   	];
    	   	  $mmGrid3 = $('#table-p2').mmGrid({
    			height:'auto',
    		    cols: shopInspection_cols,
    		    items:shopInspection_items,
    		    checkCol:false,
    		    autoLoad:true,
    		    fullWidthRows: true
    	   	 });
});	

function  initProductTypeMmgrid(){
	var itemsp = ${productType}
	  //经营品类
	  	var colsp = [	
		{ title:'${message("大类")}', align:'center',name:"name",width:130 ,renderer: function(val,item,rowIndex){
			var html = '<span class="productType'+item.bid+'"> </span>';
				return html+'<input type="hidden" name="businessCategoryApplys['+rowIndex+'].productBigType.id"  value="'+item.id+'" class="text countAddress" btn-fun="clear" />'+val;

		}},
		{ title:'${message("中类")}', align:'center',width:130 ,name:"cname" ,renderer: function(val,item,rowIndex){
			
			var cid = item.cid;
			if(cid == "undefined" || typeof(cid)=="undefined" || cid==null){
				cid = "";
			}
			var html = '<span class="search" style="position:relative">'
				+'<input type="hidden" class="productCenterTypeId" name="businessCategoryApplys['+rowIndex+'].productCenterType.id"  value="'+cid+'" >'
				+'<input class="text productCenterTypeName" maxlength="200" type="text"  value="'+item.cname+'"  readonly>'
				+'<input type="button" class="iconSearch" value=""  onclick="findChildrenCategory(this,'+item.id+')" line_index="'+rowIndex+'">'
			+'</span>';
			
			return html;

	}},
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteProductType(this)">删除</a>'
		}}
		
	];
	  $mmGrid2 = $('#table-p1').mmGrid({
		  height:'auto',
	        cols: colsp,
	        items:itemsp,
	        fullWidthRows:true,
	        checkCol: false,
	        autoLoad: true
	 }); 
}
function initMmGrid(){
	if($("#productClick").val()==1){
		initProductTypeMmgrid();
		initBusinessRecordMmgrid();
		initCautionMoneyMmgrid()
		$("#productClick").val(0);
	}
}

function findChildrenCategory(e,id){
	//查询产品分类
	var url = 'http://cloud.etwowin.com.cn/product/product_category/findChildren.jhtml?id='+id;
	var $search = $(e).closest(".search");
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
			title:'${message("查询子级分类")}',
			width:1200,
			height:508,
			content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
			onOk: function() {
				var rows = $("#"+iframeId)[0].contentWindow.childMethod();
				var elem = $(".productType"+rows[0].id);
				if(elem.length==0){
				$search.find(".productCenterTypeId").val(rows[0].id);
				$search.find(".productCenterTypeName").val(rows[0].name);
				$search.addClass("productType"+rows[0].id);
			}else{
				$.message_alert('${message("此分类已添加")}');
				return false;
			}
			}	
		});
}

function deleteProductType(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid2.removeRow(index);
	})
}
function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var url="/member/shopInfo/check_wf.jhtml?shopInfoId=${shopInfo.id}";
	var data = $form.serialize();
	
	ajaxSubmit(e,{
		 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=98&objid=${shopInfo.id}',
		 method: "post",
		 callback:function(resultMsg){
		 	var rows = resultMsg.objx;
		 	var str = '';
		 	for(var i=0;i<rows.length;i++){
		 		var row = rows[i];
		 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
		 	}
		 	var content = '<table class="input input-edit" style="width:100%">'
					+'<tbody><tr><th>流程模版</th>'
					+'<td>'
						+'<select class="text" id="objConfId">'
							+str
						+'</select>'
					+'</td>'
				+'</tr></tbody></table>';
			var $dialog_check = $.dialog({
				title:"门店审核",
				height:'135',
				content: content,
				onOk:function(){
					var objConfId = $("#objConfId").val();
					if(objConfId=='' || objConfId == null){
						$.message_alert("请选择门店审核模版");
						return false;
					}
					data = data+'&objConfId='+objConfId;
					
					ajaxSubmit($this,{
						 url: url,
						 method: "post",
						 data: data,
						 callback:function(resultMsg){
							$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
								reflush_wf();
							});
						 }
					})
					
				}
			});
			var h = 150;
			$dialog_check.css("top",h+"px");
		 }
	})
}
function deleteShopDesign(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid1.removeRow(index);
	})
}
function deleteshopInspection(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid3.removeRow(index);
	})
}
function chooseOne(domEl){
/* 	var $this = $(domEl).find("input[type='checkbox']");
	var $tr = $this.closest("table");
		$tr.find("input.isDefaultCheckbox").prop("checked",false);
		$this.prop("checked",true);
		$tr.find("input.isDefault").val("false");
		$(domEl).find("input.isDefault").val("true"); */
}

function changeCheck(){
	var $phoneVal = $("input.distributorPhone").val();
	$("input.shopDesignsPhone").attr("value",$phoneVal);
	var $joinDateVal = $("input.joinDate").val();
	$("input.shopDesignsJoinYear").attr("value",$joinDateVal);
	var $openDateVal = $("input.openDate").val();
	$("input.shopDesignsBuiltShopYear").attr("value",$openDateVal);
	var $distributorNameVal = $("input.distributorName").val();
	$("input.shopDesignsDealer").attr("value",$distributorNameVal);
	var $franchiseeVal = $("input.franchisee").val();
	$("input.exclusiveDistribution").attr("value",$franchiseeVal);
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看门店资料")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
	    <input type="hidden" name="id" value="${shopInfo.id}" />
	    <div class="tabContent">
			<table class="input input-edit">
					<tr>
					<th>
						${message("门店编码")}:
					</th>
					<td>
						${shopInfo.sn}
					</td>
	            <th>
					<span class="requiredField">*</span>${message("客户")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeId" class="text storeId" value="${shopInfo.store.id}" btn-fun="clear"/>
					<input type="text" name="storeName" class="text storeName" value="${shopInfo.store.name}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore">
					</span>
				</td>
                <th>${message("区域")}:</th>
				<td>
					<select class="text region" id="region" name="region">
						<option value="东区" [#if shopInfo.region=="东区"]selected[/#if]>${message("东区")}</option>
						<option value="西区" [#if shopInfo.region=="西区"]selected[/#if]>${message("西区")}</option>
						<option value="北区" [#if shopInfo.region=="北区"]selected[/#if]>${message("北区")}</option>
						<option value="南区" [#if shopInfo.region=="南区"]selected[/#if]>${message("南区")}</option>
					</select>
				</td>
				    <th>
						${message("所属销售平台")}:
					</th>
					<td>
						<input type="text" name="salesPlatform" class="text salesPlatform" maxlength="200" btn-fun="clear" value="${shopInfo.salesPlatform}" readonly="readonly" />
					</td>
				</tr>
				<tr>
					<th>
						${message("业态")}:
					</th>
					<td>
						<select name="businessForm" class="text businessForm">
							<option value="正常运营店" [#if shopInfo.businessForm=="正常运营店"]selected[/#if]>${message("正常运营店")}</option>
							<option value="低产能店"  [#if shopInfo.businessForm=="低产能店"]selected[/#if] >${message("低产能店")}</option>
						</select>
					</td>
					<th>
						${message("门店状态")}:
					</th>
					<td>
						<select class="text status" id="status" name="status" >
							<option value="新增" [#if shopInfo.status == "新增" ] selected [/#if]>${message("新增")}</option>
							<option value="减少" [#if shopInfo.status == "减少" ] selected [/#if]>${message("减少")}</option>
							<option value="停业整顿" [#if shopInfo.status == "停业整顿" ] selected [/#if]>${message("停业整顿")}</option>
							<option value="正常营业" [#if shopInfo.status == "正常营业" ] selected [/#if]>${message("正常营业")}</option>
						</select>
					</td>
					<th>
						${message("门店状态备注")}:
					</th>
					<td>
						<input type="text" name="statusNote" class="text statusNote" maxlength="200" btn-fun="clear" value="${shopInfo.statusNote}" />
					</td>
					<th>
						${message("VI版本")}:
					</th>
					<td>
						<select name="viVersion" class="text viVersion">
							<option value="2013年以前版本" [#if shopInfo.viVersion=="2013年以前版本"]selected[/#if]>${message("2013年以前版本")}</option>
							<option value="2013版本"  [#if shopInfo.viVersion=="2013版本"]selected[/#if] >${message("2013版本")}</option>
							<option value="2017版本"  [#if shopInfo.viVersion=="2017版本"]selected[/#if] >${message("2017版本")}</option>
						</select>
					</td>
				</tr>
				<tr>
					<th>
						${message("授权编号")}:
					</th>
					<td>
						<input type="text" name="authorizationCode" class="text authorizationCode" maxlength="200" btn-fun="clear" value="${shopInfo.authorizationCode}" />
					</td>
					<th>
						${message("城市行政等级")}:
					</th>
					<td>
					<select name="administrativeRank" class="text administrativeRank">
							<option value="省级" [#if shopInfo.administrativeRank="省级"]selected[/#if]>${message("省级")}</option>
							<option value="市级"   [#if shopInfo.administrativeRank="市级"]selected[/#if]>${message("市级")}</option>
							<option value="区县级"  [#if shopInfo.administrativeRank="区县级"]selected[/#if] >${message("区县级")}</option>
							<option value="乡镇级"   [#if shopInfo.administrativeRank="乡镇级"]selected[/#if]>${message("乡镇级")}</option>
					</select>
					</td>
					<th>
						${message("经销商手机号码")}:
					</th>
					<td>
						<input type="text" name="distributorPhone" class="text distributorPhone" maxlength="200" btn-fun="clear" value="${shopInfo.distributorPhone}" onblur="changeCheck()"/>
					</td>
					<th>
						${message("招牌")}:
					</th>
					<td>
						<select class="text shopSign" id="shopSign" name="shopSign">
							<option value="大自然地板" [#if shopInfo.shopSign=="大自然地板"]selected[/#if]>${message("大自然地板")}</option>
							<option value="大自然竹地板" [#if shopInfo.shopSign=="大自然竹地板"]selected[/#if]>${message("大自然竹地板")}</option>
							<option value="nature" [#if shopInfo.shopSign=="nature"]selected[/#if]>${message("nature")}</option>
							<option value="大自然木香居" [#if shopInfo.shopSign=="大自然木香居"]selected[/#if]>${message("大自然木香居")}</option>
						</select>
					</td>
				</tr>
				<tr>
					<th>
						${message("经销商姓名")}:
					</th>
					<td>
						<input type="text" name="distributorName" class="text distributorName" maxlength="200" btn-fun="clear" value="${shopInfo.distributorName}" onblur="changeCheck()"/>
					</td>
					<th>
						${message("面积")}:
					</th>
					<td>
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button">
							<input class="t acreage" name="acreage" value="${shopInfo.acreage}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text">
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button">
						</div>
					</td>
					<th>
						${message("对接人号码")}:
					</th>
					<td>
						<input type="text" name="contactPhone" class="text contactPhone" maxlength="200" btn-fun="clear" value="${shopInfo.contactPhone}" />
					</td>
					<th>
						${message("位置类型")}:
					</th>
					<td> 
						<select class="text positionType" id="positionType" name="positionType">
						<option value="专卖店" [#if shopInfo.positionType=="家居市场"]selected[/#if]>${message("家居市场")}</option>
						<option value="一址多名" [#if shopInfo.positionType=="建材市场"]selected[/#if]>${message("建材市场")}</option>
						<option value="大家具" [#if shopInfo.positionType=="临街商铺"]selected[/#if]>${message("临街商铺")}</option>
						<option value="多品类综合店" [#if shopInfo.positionType=="装饰公司"]selected[/#if]>${message("装饰公司")}</option>
						</select>
					</td>
			     </tr>
				 <tr>
					<th>${message("总经销商")}:</th>
					<td>
						<input  class="text franchisee" name="franchisee" value="${shopInfo.franchisee}" btn-fun="clear" onblur="changeCheck()"/>
					</td>
					<th>
						${message("详细地址")}:
					</th>
					<td>
						<input type="text" name="address" class="text address" maxlength="200" btn-fun="clear" value="${shopInfo.address}" />
					</td>
					<th>
						${message("固定号码")}:
					</th>
					<td>
						<input type="text" name="tel" class="text tel" maxlength="200" btn-fun="clear" value="${shopInfo.tel}" />
					</td>
					<th>${message("门店类别")}:</th>
             		<td>
                		<select class="text type" id="type" name="type" >
						<option value="专卖店" [#if shopInfo.type == "专卖店" ] selected [/#if]>${message("专卖店")}</option>
							<option value="专一址多名" [#if shopInfo.type == "一址多名" ] selected [/#if]>${message("一址多名")}</option>
						<option value="大家具" [#if shopInfo.type == "大家具" ] selected [/#if]>${message("大家具")}</option>
						<option value="多品类综合店" [#if shopInfo.type == "多品类综合店" ] selected [/#if]>${message("多品类综合店")}</option>
						<option value="产品专区" [#if shopInfo.type == "产品专区"] selected [/#if]>${message("产品专区")}</option>
						</select>
					</td>
					</tr>
					<tr>
					<th>
						${message("地区")}:
					</th>
					<td colspan="3">
						<input type="hidden" id="areaId" name="area.id" value="${(shopInfo.area.id)!}" treePath="${(shopInfo.area.treePath)!}"/>
					</td>
					<th>${message("乡镇城市")}:</th>
					<td>
					<input type="text" name="town" class="text town" value="${(shopInfo.town)!}"  btn-fun="clear" />
					</td>
					<th></th><td></td>
					</tr>
					<tr>
					</tr>
				    <tr>
					<th>
						${message("所属部门")}:
					</th>
					<td>
						<select name="department" class="text department">
							<option value="渠道" [#if shopInfo.department=="渠道"]selected[/#if]>${message("渠道")}</option>
							<option value="工程"  [#if shopInfo.department=="工程"]selected[/#if] >${message("工程")}</option>
							<option value="nature"  [#if shopInfo.department=="nature"]selected[/#if] >${message("nature")}</option>
							<option value="财务账户" [#if shopInfo.department=="财务账户"]selected[/#if]  >${message("财务账户")}</option>
							<option value="电商"  [#if shopInfo.department=="电商"]selected[/#if] >${message("电商")}</option>
							<option value="其它"  [#if shopInfo.department=="其他"]selected[/#if] >${message("其他")}</option>
						</select>
					</td>
			        <th>
						${message("新增档案编号")}:
					</th>
					<td>
						<input type="text" name="increaseArchivesCode" class="text increaseArchivesCode" maxlength="200" btn-fun="clear" value="${shopInfo.increaseArchivesCode}" />
					</td>
				    <th>${message("最后设计日期")}:</th>
					<td>
						<input type="text" name="lastDesignDate" class="text lastDesignDate" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});"  value="${shopInfo.lastDesignDate}" disabled/>
					</td>
					<th>
						${message("创建时间")}:
					</th>
					<td>
						${shopInfo.createDate?string("yyyy-MM-dd HH:mm:ss")}
					</td>
					</tr>
					<tr>
					<th>
						${message("所属品牌")}:
					</th>
					<td>
					<select name="belongBrand" class="text belongBrand">
							<option value="国际出口" [#if shopInfo.belongBrand=="国际出口"]selected[/#if] >${message("国际出口")}</option>
							<option value="木香居地板"  [#if shopInfo.belongBrand=="木香居地板"]selected[/#if] >${message("木香居地板")}</option>
							<option value="大自然工程"  [#if shopInfo.belongBrand=="大自然工程"]selected[/#if] >${message("大自然工程")}</option>
							<option value="nature地板"  [#if shopInfo.belongBrand=="nature地板"]selected[/#if] >${message("nature地板")}</option>
							<option value="大自然地板"  [#if shopInfo.belongBrand=="大自然地板"]selected[/#if] >${message("大自然地板")}</option>
					</select>
					<th>
						${message("加盟日期")}:
					</th>
					<td>
						<input type="text" name="joinDate" class="text joinDate" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" value="${shopInfo.joinDate}" onblur="changeCheck()"/>
					</td>
					</td>
					<th>
					   ${message("最后设计状态")}:
					</th>
					<td>
						<input  class="text lastDesignStatus" name="lastDesignStatus" value="${shopInfo.lastDesignStatus}"  btn-fun="clear" disabled/>
					</td>
					<th>${message("流程状态")}:</th>
					<td>[#if shopInfo.wfState??]
						${message("22222222"+shopInfo.wfState)} [/#if]
					</td>
				</tr>
                <tr>
					<th>
						${message("所含品牌")}:
					</th>
					<td >
					<div class="statusList cs-box" data-value="off">
						<label><input type="checkbox" name="inclusiveBrand" class="text check" maxlength="200" btn-fun="clear" value="国际出口" [#if inclusiveBrandA=="国际出口"]checked[/#if] />国际出口</label>
						 &nbsp;&nbsp;&nbsp;&nbsp;
						<label><input type="checkbox" name="inclusiveBrand" class="text check" maxlength="200" btn-fun="clear"value="木香居地板" [#if inclusiveBrandB=="木香居地板"]checked[/#if]/>木香居地板</label>
						 &nbsp;&nbsp;&nbsp;&nbsp;
						<label><input type="checkbox" name="inclusiveBrand" class="text check" maxlength="200" btn-fun="clear"value="大自然工程" [#if inclusiveBrandC=="大自然工程"]checked[/#if]/>大自然工程</label>
						 &nbsp;
						<label><input type="checkbox" name="inclusiveBrand" class="text check" maxlength="200" btn-fun="clear" value="大自然地板"[#if inclusiveBrandE=="大自然地板"]checked[/#if]/>大自然地板</label>	
						 &nbsp;&nbsp;
						<label><input type="checkbox" name="inclusiveBrand" class="text check" maxlength="200" btn-fun="clear" value="nature地板"[#if inclusiveBrandD=="nature地板"]checked[/#if]/>nature地板</label>			
						</div>
					</td>
					<th>
						${message("建店日期")}:
					</th>
					<td>
						<input type="text" name="openDate" class="text openDate" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" value="${shopInfo.openDate}" onblur="changeCheck()"/>
					</td>
					  <th>${message("最后稽查结果")}:</th>
					<td>
						<input  class="text lastInspectionResult" name="lastInspectionResult" value="${shopInfo.lastInspectionResult}" btn-fun="clear" disabled/>
					</td>
				<th>${message("审核时间")}:</th>
    		    <td>${shopInfo.checkDate}</td>
				</tr>
				<tr>					
					<th>
						${message("销售渠道")}:
					</th>
					<td>
						<select class="text salesChannel" id="salesChannel" name="salesChannel" >
							<option value="零售" [#if shopInfo.salesChannel == "零售" ] selected [/#if]>${message("零售")}</option>
							<option value="工程" [#if shopInfo.salesChannel == "工程" ] selected [/#if]>${message("工程")}</option>
							<option value="家装" [#if shopInfo.salesChannel == "家装" ] selected [/#if]>${message("家装")}</option>
						</select>
					</td>
					<th>
						${message("减少档案编号")}:
					</th>
					<td>
						<input type="text" name="decreaseArchivesCode" class="text decreaseArchivesCode" maxlength="200" btn-fun="clear" value="${shopInfo.decreaseArchivesCode}" />
					</td>
				<th>${message("最后稽查时间")}</th>
				<td>
				<input type="text" name="lastCheckTime" class="text lastCheckTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" value="${shopInfo.lastCheckTime}" disabled/>
				</td>
				<th>${message("审核人")}:</th>
                <td>${shopInfo.checkStoreMember.name}</td>
				</tr>
				<tr>
					<th>
						${message("销售渠道备注")}:
					</th>
					<td>
						<input type="text" name="salesChannelNote" class="text salesChannelNote" maxlength="200" btn-fun="clear" value="${shopInfo.salesChannelNote}" />
					</td>
					<th>${message("门店关闭时间") }</th>
					<td>
					<input type="text" name="closingTime" class="text closingTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" value="${shopInfo.closingTime}" />
					</td>
				<th>${message("激活时间")}</th>
				<td>
				<input type="text" name="activationTime" class="text activationTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});"  value="${shopInfo.activationTime}" />
				</td>
					<th>
						${message("最近一次装修日期")}:
					</th>
					<td>
						<input type="text" name="decorationDate" class="text decorationDate" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" value="${shopInfo.decorationDate}" />
					</td>
				</tr>
				<tr>
					<th>
						${message("门店销量")}:
					</th>
					<td>
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button"/>
							<input class="t salesVolume" name="salesVolume" value="${shopInfo.salesVolume}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"/>
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button"/>
						</div>
					</td>
					<th>${message("门店关闭原因")}</th>
					<td>
					<input type="text" name="shutDownMenu" class="text shutDownMenu" maxlength="200" btn-fun="clear" value="${shopInfo.shutDownMenu}" />
				    </td>
					<th>${message("门店新增时间")}</th>
					<td>
					<input type="text" name="addTime" class="text addTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" value ="${(shopInfo.addTime)!}" />
					</td>
					<th></th><td></td>
				</tr>
				<tr>
					<th>
						${message("备注")}:
					</th>
					<td colspan="7">
						<textarea name="memo" class="text">${shopInfo.memo}</textarea>
					</td>
				</tr>		
			</table>
		<div class="title-style">
			${message("经营品类")}
				<div class="btns">
					<a href="javascript:;" id="addStoreProductType" class="button">${message("添加")}</a>
		    	</div>
		    </div>
		    <table id="table-p1"></table>	
			<div class="title-style">
			${message("门店稽查")}
				<div class="btns">
					<a href="javascript:;" id="addShopInspection" class="button">${message("添加")}</a>
		    	</div>
		    </div>
		    <table id="table-p2"></table>	
			<div class="title-style">
			${message("门店设计")}
				<div class="btns">
					<a href="javascript:;" id="addShopDesign" class="button">${message("添加")}</a>
		    	</div>
		    </div>
		    <table id="table-p3"></table>
		</div>
		<div class="fixed-top">
		
			<a href="add.jhtml" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			[#if isCheckWf]
			[#if shopInfo.wfId==null]
			<input type="button" class="button sureButton" value="${message("12501")}" onclick="check_wf(this)" />
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			[/#if]
			[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
	</body>
</html>