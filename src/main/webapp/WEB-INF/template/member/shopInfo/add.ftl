<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("门店资料")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e);
}
$().ready(function() {
	$.validator.addClassRules({
		//areaId_select: {required: true},
		distributorName: {required: true},
		distributorPhone: {required: true},
		administrativeRank: {required: true},
		acreage: {required: true},
		businessForm: {required: true},
		//contactPhone: {required: true},
		shopSign: {required: true},
		department: {required: true},
		//salesVolume: {required: true},
		department: {required: true},
		addTime: {required: true},
		//lastCheckTime: {required: true},
		//shutDownMenu: {required: true},
		//activationTypeId: {required: true},
		//activationTime: {required: true},
		storeId: {required: true},
		storeName:{required: true},
		//authorizationCode: {required: true},
		distributorName: {required: true},
		//openDate: {required: true},
		//joinDate: {required: true},
		distributorPhone: {required: true},
		//tel: {required: true},
		//fax: {required: true},
		positionType: {required: true},
		address: {required: true},
		//salesPlatform: {required: true},
		//decorationDate: {required: true},
		salesChannel: {required: true},
		//salesChannelNote: {required: true},
		status: {required: true},
		//statusNote: {required: true},
		//increaseArchivesCode: {required: true},
		//decreaseArchivesCode: {required: true},
		viVersion: {required: true}
	});
	var $addShopDesign = $("#addShopDesign");
	var $addShopInspection = $("#addShopInspection");
	var countaddShopDesign = 0;
	var countaddShopInspection = 0;
	
	//门店设计
	$addShopDesign.click(function(){
		var row={};
		$mmGrid1.addRow(row);
		showShopDesign();
	})
	
	//选择经销商后把经销商数据刷新门店设计对应数据
	function showShopDesign(){
		$("#shopDesignsAreaId"+ countaddShopDesign).lSelect();
		countaddShopDesign=	parseInt(countaddShopDesign) + 1;
		var $phoneVal = $("input.distributorPhone").val();
		$("input.shopDesignsPhone").attr("value",$phoneVal);
		var $joinDateVal = $("input.joinDate").val();
		$("input.shopDesignsJoinYear").attr("value",$joinDateVal);
		var $openDateVal = $("input.openDate").val();
		$("input.shopDesignsBuiltShopYear").attr("value",$openDateVal);
		var $distributorNameVal = $("input.distributorName").val();
		$("input.shopDesignsDealer").attr("value",$distributorNameVal);
		var $franchiseeVal = $("input.franchisee").val();
		$("input.exclusiveDistribution").attr("value",$franchiseeVal);
	}

	var colss = [	
		{ title:'${message("设计状态")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].designStatu" class="text">'
		    +'<option value="设计中">设计中</option>'
			+'<option value="装修中">装修中</option>'
			+'<option value="正常营业">正常营业</option>'
			+'<option value="暂停设计">暂停设计</option>'
			+'<option value="暂停装修">暂停装修</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("设计-业态 ")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].designVersion" class="text" />';
		}},
		{ title:'${message("门头标示")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].doorHeadMark" class="text">'
		    +'<option value="大自然">大自然</option>'
			+'<option value="竹地板">竹地板</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("市场性质")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].marketNature" class="text" />'
		}},
		{ title:'${message("门店面积 ")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<div class="nums-input ov fl" style="width: 163px;"> '
  				+' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">'
  				+' <input type="text"  class="t"  name="shopDesigns['+countaddShopDesign+'].storeArea" value="" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
  				+' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">'
  				+' </div>';
  			return html;
		}},
		{ title:'${message("总经销 ")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].exclusiveDistribution" class="text exclusiveDistribution" />';
		}},
		{ title:'${message("经销商 ")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].dealer" class="text shopDesignsDealer" />';
		}},
		{ title:'${message("加盟年份")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input name="shopDesigns['+countaddShopDesign+'].joinYear" class="text shopDesignsJoinYear" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("手机号码 ")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].phone" class="text shopDesignsPhone" />';
		}},
		{ title:'${message("建店年份 ")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input name="shopDesigns['+countaddShopDesign+'].builtShopYear" class="text shopDesignsBuiltShopYear" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("门店所属品牌")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].belongBrand" class="text">'
		    +'<option value="大自然">大自然</option>'
			+'<option value="木香居">木香居</option>'
			+'<option value="nature">nature</option>'
			+'<option value="国际出口">国际出口</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("设计申请时间")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].applicationTime"  onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" class="text" />'
		}},
		{ title:'${message("门店装修属性")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].decProperty" class="text">'
		    +'<option value="新装">新装</option>'
			+'<option value="重装">重装</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("设计门店地址")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].designAddress" class="text" />'
		}},
		{ title:'${message("门店位置类型")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].locationType" class="text">'
		    +'<option value="1">建材市场</option>'
		    +'<option value="2">商场</option>'
		    +'<option value="3">临街市场</option>'
			+'<option value="4">装饰公司</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("是否返利")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].isRebate" class="text">'
		    +'<option value="1">是</option>'
		    +'<option value="2">否</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("预计开业时间")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].openingTime"  onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" class="text" />'
		}},
		{ title:'${message("是否签合同")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].isSignContract" class="text">'
		    +'<option value="1">是</option>'
		    +'<option value="2">否</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("是否保证金")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].isPayDeposit" class="text">'
		    +'<option value="1">是</option>'
		    +'<option value="2">否</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("保证金备注")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].depositNote" class="text" />'
		}},
		{ title:'${message("2年内是否参与返利")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].isJoinRebateTowYear" class="text">'
		    +'<option value="1">是</option>'
		    +'<option value="2">否</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("是否参与返利备注")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].isJoinRebateMemo" class="text">'
		    +'<option value="1">是</option>'
		    +'<option value="2">否</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("渠道审核")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].channelAudit" class="text" />'
		}},
		{ title:'${message("渠道审核意见")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopDesigns['+countaddShopDesign+'].channelAuditOpinions" class="text" />'
		}},
		{ title:'${message("设计进度")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].designSchedule" class="text">'
		    +'<option value="设计中">设计中</option>'
		    +'<option value="已设计">已设计</option>'
		    +'<option value="暂停设计">暂停设计</option>'
			+'<option value="取消设计">取消设计</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("区域负责人跟进")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].regLeaderFollowUp" class="text">'
		    +'<option value="装修中">装修中</option>'
		    +'<option value="暂停装修">暂停装修</option>'
		    +'<option value="取消装修">取消装修</option>'
			+'<option value="完成装修">完成装修</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("区域")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			var html = '<select name="shopDesigns['+countaddShopDesign+'].region" class="text">'
		    +'<option value="东区">东区</option>'
		    +'<option value="西区">西区</option>'
		    +'<option value="南区">南区</option>'
			+'<option value="北区">北区</option>'
			+'</select>';
		    return html;
		}},
		{ title:'${message("地区")}', align:'center',width:320 ,renderer: function(val,item,rowIndex){
		    return '<input type="hidden" id="shopDesignsAreaId'+ countaddShopDesign +'" name="shopDesigns['+countaddShopDesign+'].area.id" />';
		}},
		{ title:'${message("VI版本")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
		    return '<input type="text" class="text" name="shopDesigns['+countaddShopDesign+'].viVersion" />';
		}},
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteShopDesign(this)">删除</a>'
		}}
		
	];
	  $mmGrid1 = $('#table-p3').mmGrid({
		height:'auto',
	    cols: colss,
	    checkCol:false,
	    fullWidthRows: true
	 });
	  
	//门店稽查
	$addShopInspection.click(function(){
		var row={};
		$mmGrid3.addRow(row);
		countaddShopInspection=	parseInt(countaddShopInspection)+1;
	})
	var colss = [	
		{ title:'${message("稽查结果")}', align:'center',name:'result',width:130  ,renderer: function(val,item,rowIndex){			
			var html = '<select name="shopInspections['+countaddShopInspection+'].result" class="text result">'
				+'<option value="1">正常营业</option>'
				+'<option value="2">门店关闭</option>'
				+'<option value="3">门店交接</option>'
				+'<option value="0" >其他(备注)</option> '
				+'</select>';
			return html;
		}},
		{ title:'${message("稽查说明")}', align:'center',name:'auditDescription',width:130  ,renderer: function(val,item,rowIndex){			
			var html = '<select name="shopInspections['+countaddShopInspection+'].auditDescription" class="text">'
			    +'<option value="1">门店搬迁 </option>'
				+'<option value="2">经销商退出</option>'
				+'<option value="3">经营不善</option>'
				+'<option value="4">经销商变更</option>'
				+'<option value="0" >其他(备注)</option> '
				+'</select>';
			return html;
		}},
		{ title:'${message("备注")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="shopInspections['+countaddShopInspection+'].memo" class="text " />'
		}},
		{ title:'${message("区域责任人")}', align:'center',name:'regionalResPerson',width:130  ,renderer: function(val,item,rowIndex){			
			var html = '<select name="shopInspections['+countaddShopInspection+'].regionalResPerson" class="text">'
			    +'<option value="区域经理">区域经理</option>'
				+'<option value="省长">省长</option>'
				+'</select>';
			return html;
		}},
		{ title:'${message("稽查日期")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input name="shopInspections['+countaddShopInspection+'].auditDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("处理情况")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){	
			var html = '<select name="shopInspections['+countaddShopInspection+'].handleRusult" class="text">'
		    +'<option value="处理">处理</option>'
			+'<option value="未处理">未处理</option>'
			+'</select>';
		   return html;
		}},
		{ title:'${message("处理日期")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input name="shopInspections['+countaddShopInspection+'].handleDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("要求处理日期")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input name="shopInspections['+countaddShopInspection+'].requestHandleDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("逾期未处理工作日")}', align:'center',width:150,renderer: function(val,item,rowIndex){
			var html = '<div class="nums-input ov fl" style="width: 163px;"> '
				+' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">'
				+' <input type="text"  class="t"  name="shopInspections['+countaddShopInspection+'].overWorkDay" value="" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
				+' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">'
				+' </div>';
			return html;
		}},
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteshopInspection(this)">删除</a>'
		}}
		
	];
	  $mmGrid3 = $('#table-p2').mmGrid({
		height:'auto',
	    cols: colss,
	    checkCol:false,
	    fullWidthRows: true
	 });
	initProductTypeMmgrid();
	// 地区选择
	$("#areaId").lSelect();
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'edit.jhtml?id='+resultMsg.objx;
			})
	    }
	 });
	//查询产品分类
	$("#addStoreProductType").bindQueryBtn({
		type:'productType',
		title:'${message("查询产品分类 ")}',
		url:'/product/product_category/findTopCategory.jhtml',
		callback:function(row){
			$mmGrid2.addRow(row);
		}
	});
	
	//查询客户
	$("#selectStore").click(function(){
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			bindClick:false,
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$(".storeName").val(row.name);
					$(".storeId").val(row.id);
					//带出经销商数据信息
					$(".salesPlatform").val(row.sale_org_name);
					$("input.franchisee").val(row.franchisee);
					var joinValue = row.active_date;
					if(joinValue){
						$("input.joinDate").val(row.active_date.substring(0,10));
					}else{
						$("input.joinDate").val(joinValue);
					}
					$("input.distributorName").val(row.dealer_name);
					$("input.distributorPhone").val(row.fixed_number);
					$("input.tel").val(row.fixed_number);
					$("input.fax").val(row.fax_number);
					showShopDesign(); //更新门店数据信息
				}
			}
		});
	});
	
})


function  initProductTypeMmgrid(){
	  var itemsp = [];
	  //经营品类
	  	var colsp = [	
		{ title:'${message("大类")}', align:'center',name:"name",width:130 ,renderer: function(val,item,rowIndex){
			var html = '<span class="productType'+item.bid+'"> </span>';
				return html+'<input type="hidden" name="businessCategoryApplys['+rowIndex+'].productBigType.id"  value="'+item.id+'" class="text countAddress" btn-fun="clear" />'+val;

		}},
		{ title:'${message("中类")}', align:'center',width:130 ,name:"cname" ,renderer: function(val,item,rowIndex){
			
			var cid = item.cid;
			if(cid == "undefined" || typeof(cid)=="undefined" || cid==null){
				cid = "";
			}
			var html = '<span class="search" style="position:relative">'
				+'<input type="hidden" class="productCenterTypeId" name="businessCategoryApplys['+rowIndex+'].productCenterType.id"  value="'+cid+'" >'
				+'<input class="text productCenterTypeName" maxlength="200" type="text"  value="'+item.cname+'"  readonly>'
				+'<input type="button" class="iconSearch" value=""  onclick="findChildrenCategory(this,'+item.id+')" line_index="'+rowIndex+'">'
			+'</span>';
			
			return html;

	}},
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteProductType(this)">删除</a>'
		}}
		
	];
	  $mmGrid2 = $('#table-p1').mmGrid({
		  height:'auto',
	        cols: colsp,
	        items:itemsp,
	        fullWidthRows:true,
	        checkCol: false,
	        autoLoad: true
	 }); 
}
function initMmGrid(){
	if($("#productClick").val()==1){
		initProductTypeMmgrid();
		initBusinessRecordMmgrid();
		initCautionMoneyMmgrid()
		$("#productClick").val(0);
	}
}
function findChildrenCategory(e,id){
	//查询产品分类
	var url = 'http://cloud.etwowin.com.cn/product/product_category/findChildren.jhtml?id='+id;
	var $search = $(e).closest(".search");
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
			title:'${message("查询子级分类")}',
			width:1200,
			height:508,
			content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
			onOk: function() {
				var rows = $("#"+iframeId)[0].contentWindow.childMethod();
				var elem = $(".productType"+rows[0].id);
				if(elem.length==0){
				$search.find(".productCenterTypeId").val(rows[0].id);
				$search.find(".productCenterTypeName").val(rows[0].name);
				$search.addClass("productType"+rows[0].id);
			}else{
				$.message_alert('${message("此分类已添加")}');
				return false;
			}
			}	
		});
	
	
	 function deleteProductType(e){
			var index = $(e).closest("tr").index();
			$.message_confirm('您确定要删除吗？',function(){
				$mmGrid2.removeRow(index);
			})
		}

}
function deleteShopDesign(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid1.removeRow(index);
	})
}
function deleteshopInspection(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid3.removeRow(index);
	})
}
function chooseOne(domEl){
/* 	var $this = $(domEl).find("input[type='checkbox']");
	var $tr = $this.closest("table");
		$tr.find("input.isDefaultCheckbox").prop("checked",false);
		$this.prop("checked",true);
		$tr.find("input.isDefault").val("false");
		$(domEl).find("input.isDefault").val("true"); */
}

function changeCheck(){
	var $phoneVal = $("input.distributorPhone").val();
	$("input.shopDesignsPhone").attr("value",$phoneVal);
	var $joinDateVal = $("input.joinDate").val();
	$("input.shopDesignsJoinYear").attr("value",$joinDateVal);
	var $openDateVal = $("input.openDate").val();
	$("input.shopDesignsBuiltShopYear").attr("value",$openDateVal);
	var $distributorNameVal = $("input.distributorName").val();
	$("input.shopDesignsDealer").attr("value",$distributorNameVal);
	var $franchiseeVal = $("input.franchisee").val();
	$("input.exclusiveDistribution").attr("value",$franchiseeVal);
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增门店资料")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("门店编码")}:
					</th>
					<td></td>
				<th>
					<span class="requiredField">*</span>${message("客户")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeId" class="text storeId" value="" btn-fun="clear"/>
					<input type="text" name="storeName" class="text storeName" value="" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore"/>
					</span>
				</td>
				<th>${message("区域")}:</th>
				<td>
					<select class="text region" id="region" name="region">
						<option value="东区">${message("东区")}</option>
						<option value="西区">${message("西区")}</option>
						<option value="北区">${message("北区")}</option>
						<option value="南区">${message("南区")}</option>
					</select>
				</td>
				    <th>
						${message("所属销售平台")}:
					</th>
					<td>
						<input type="text" name="salesPlatform" class="text salesPlatform" maxlength="200" btn-fun="clear" readonly="readonly"/>
					</td>
				</tr>
				<tr>
				    <th>
						${message("业态")}:
					</th>
					<td>
						<select class="text businessForm" id="businessForm" name="businessForm">
						<option value="正常运营店">${message("正常运营店")}</option>
						<option value="低产能店">${message("低产能店")}</option>
						</select>
					</td>
					<th>
						${message("门店状态")}:
					</th>
					<td>
						<select class="text status" id="status" name="status">
						<option value="新增">${message("新增")}</option>
						<option value="减少">${message("减少")}</option>
						<option value="停业整顿">${message("停业整顿")}</option>
						<option value="正常营业">${message("正常营业")}</option>
						</select>
					</td>
					<th>
						${message("门店状态备注")}:
					</th>
					<td>
						<input type="text" name="statusNote" class="text statusNote" maxlength="200" btn-fun="clear" />
					</td>
					<th>
						${message("VI版本")}:
					</th>
					<td>
						<select class="text viVersion" id="viVersion" name="viVersion">
						<option value="2013年以前版本">${message("2013年以前版本")}</option>
						<option value="2013版本">${message("2013版本")}</option>
						<option value="2017版本">${message("2017版本")}</option>
						</select>
					</td>
				</tr>
				<tr>
					<th>
						${message("授权编号")}:
					</th>
					<td>
						<input type="text" name="authorizationCode" class="text authorizationCode" maxlength="200" btn-fun="clear" />
					</td>
					<th>
						${message("城市行政等级")}:
					</th>
					<td>
					<select name="administrativeRank" class="text administrativeRank">
							<option value="省级" >${message("省级")}</option>
							<option value="市级"   >${message("市级")}</option>
							<option value="区县级"   >${message("区县级")}</option>
							<option value="乡镇级"   >${message("乡镇级")}</option>
					</select>
					</td>
					<th>
						${message("经销商手机号码")}:
					</th>
					<td>
						<input type="text" name="distributorPhone" class="text distributorPhone"  btn-fun="clear" onblur="changeCheck()"/>
					</td>
					<th>
						${message("招牌")}:
					</th>
					<td>
						<select class="text shopSign" id="shopSign" name="shopSign">
							<option value="大自然地板">${message("大自然地板")}</option>
							<option value="大自然竹地板">${message("大自然竹地板")}</option>
							<option value="nature">${message("nature")}</option>
							<option value="大自然木香居">${message("大自然木香居")}</option>
						</select>
					</td>
				</tr>
				<tr>
					<th>
						${message("经销商姓名")}:
					</th>
					<td>
						<input type="text" name="distributorName" class="text distributorName" maxlength="200" btn-fun="clear" onblur="changeCheck()"/>
					</td>
				    <th>
						${message("面积")}:
					</th>
					<td>
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t acreage" name="acreage" value="" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
					</td>
					 <th>
						${message("对接人号码")}:
					</th>
					<td>
						<input type="text" name="contactPhone" class="text contactPhone" maxlength="200" btn-fun="clear" />
					</td>
					<th>
						${message("位置类型")}:
					</th>
					<td>
                		<select class="text positionType" id="positionType" name="positionType">
						<option value="专卖店">${message("家居市场")}</option>
						<option value="一址多名">${message("建材市场")}</option>
						<option value="大家具">${message("临街商铺")}</option>
						<option value="多品类综合店">${message("装饰公司")}</option>
						</select>
					</td>
			     </tr>
					<tr>
					<th>${message("总经销商")}:</th>
					<td>
						<input  class="text franchisee" name="franchisee" value="" btn-fun="clear" onblur="changeCheck()"/>
					</td>
					<th>
						${message("详细地址")}:
					</th>
					<td>
						<input type="text" name="address" class="text address" btn-fun="clear" />
					</td>
				    <th>
						${message("固定号码")}:
					</th>
					<td>
						<input type="text" name="tel" class="text tel" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("门店类别")}:</th>
             		<td>
                		<select class="text type" id="type" name="type">
						<option value="专卖店">${message("专卖店")}</option>
						<option value="一址多名">${message("一址多名")}</option>
						<option value="大家具">${message("大家具")}</option>
						<option value="多品类综合店">${message("多品类综合店")}</option>
						<option value="产品专区">${message("产品专区")}</option>
						</select>
					</td>
					</tr>
					<tr>
					<th>
						${message("地区")}:
					</th>
					<td colspan="3">
						<input type="hidden" id="areaId" name="area.id" />
					</td>
					<th>${message("乡镇城市")}:</th>
					<td>
					<input type="text" name="town" class="text town" btn-fun="clear" />
					</td>
					<th></th><td></td>
					</tr>
					<tr>
					</tr>
				    <tr>
					<th>
						${message("所属部门")}:
					</th>
					<td>
						<select name="department" class="text department">
							<option value="渠道" >${message("渠道")}</option>
							<option value="工程"   >${message("工程")}</option>
							<option value="nature"   >${message("nature")}</option>
							<option value="财务账户"   >${message("财务账户")}</option>
							<option value="电商"   >${message("电商")}</option>
							<option value="其它"   >${message("其他")}</option>
						</select>
					</td>
					<th>
						${message("新增档案编号")}:
					</th>
					<td>
						<input type="text" name="increaseArchivesCode" class="text increaseArchivesCode" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("最后设计日期")}:</th>
					<td>
						<input type="text" name="lastDesignDate" class="text lastDesignDate"  onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" disabled/>
					</td>
					<th>
						${message("创建时间")}:
					</th>
					<td>
					</td>
					</tr>
					<tr>
					<th>
						${message("所属品牌")}:
					</th>
					<td>
					<select name="belongBrand" class="text">
							<option value="国际出口" >${message("国际出口")}</option>
							<option value="木香居地板"   >${message("木香居地板")}</option>
							<option value="大自然工程"   >${message("大自然工程")}</option>
							<option value="nature地板"   >${message("nature地板")}</option>
							<option value="大自然地板"   >${message("大自然地板")}</option>
					</select>
					</td>
					<th>
						${message("加盟日期")}:
					</th>
					<td>
						<input type="text" name="joinDate" class="text joinDate" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" onblur="changeCheck()"/>
					</td>
					<th>${message("最后设计状态")}:</th>
					<td>
						<input  class="text lastDesignStatus" name="lastDesignStatus" value="" btn-fun="clear" disabled/>
					</td>
					<th>
						${message("流程状态")}:
					</th>
					<td></td>
					</tr>
					<tr>
					<th>
						<span class="requiredField">*</span>${message("所含品牌")}:
					</th>
					<td>
						<div class="statusList"> 
						 <label><input  class="check js-iname text" name="inclusiveBrand" type="checkbox" vaule="国际出口"/>${message("国际出口")}</label>
						  &nbsp;&nbsp;&nbsp;&nbsp;
						 <label><input  class="check js-iname text" name="inclusiveBrand" type="checkbox" value="木香居地板" />${message("木香居地板")}</label>
						  &nbsp;&nbsp;&nbsp;&nbsp;
						 <label><input  class="check js-iname text" name="inclusiveBrand"  type="checkbox" value="大自然工程" />${message("大自然工程")}</label>
				          &nbsp;
				         <label><input  class="check js-iname text" name="inclusiveBrand"  type="checkbox" value="nature地板" />${message("nature地板")}</label>
				          &nbsp;&nbsp;
				         <label><input  class="check js-iname text" name="inclusiveBrand"  type="checkbox" value="大自然地板" />${message("大自然地板")}</label>
						</div>
					</td>
					<th>
						${message("建店日期")}:
					</th>
					<td>
						<input type="text" name="openDate" class="text openDate" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" onblur="changeCheck()"/>
					</td>
					<th>${message("最后稽查结果")}:</th>
					<td>
						<input  class="text lastInspectionResult" name="lastInspectionResult" value="" btn-fun="clear" disabled/>
					</td>
				<th>${message("审核时间")}:</th>
				<td></td>
				</tr>
				<tr>					
				    <th>
						${message("销售渠道")}:
					</th>
					<td>
						<select class="text salesChannel" id="salesChannel" name="salesChannel">
						<option value="零售">${message("零售")}</option>
						<option value="工程">${message("工程")}</option>
						<option value="家装">${message("家装")}</option>
						</select>
					</td>
					<th>
						${message("减少档案编号")}:
					</th>
					<td>
						<input type="text" name="decreaseArchivesCode" class="text decreaseArchivesCode" maxlength="200" btn-fun="clear" />
					</td>
				<th>${message("最后稽查时间：")}</th>
				<td>
				<input type="text" name="lastCheckTime" class="text lastCheckTime" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" disabled/>
				</td>
				<th>${message("审核人")}:</th>
				<td></td>
				</tr>
				<tr>
				     <th>
						${message("销售渠道备注")}:
					</th>
					<td>
						<input type="text" name="salesChannelNote" class="text salesChannelNote" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("门店关闭时间") }</th>
					<td>
					<input type="text" name="closingTime" class="text closingTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
					</td>
				<th>${message("激活时间")}</th>
				<td>
				<input type="text" name="activationTime" class="text activationTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
				</td>
				     <th>
						${message("最近一次装修日期")}:
					</th>
					<td>
						<input type="text" name="decorationDate" class="text decorationDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
					</td>
				</tr>
				<tr>
				    <th>
						${message("门店销量")}:
					</th>
					<td>
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t salesVolume" name="salesVolume" value="" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"/>
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button"/>
						</div>
					</td>
				   <th>${message("门店关闭原因")}</th>
					<td>
					<input type="text" name="shutDownMenu" class="text shutDownMenu" maxlength="200" btn-fun="clear" />
				    </td>
					<th>${message("门店新增时间")}</th>
					<td>
					<input type="text" name="addTime" class="text addTime" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
					</td>
					<th></th><td></td>
				</tr>
				<tr>
					<th>
						${message("备注")}:
					</th>
					<td colspan="7">
						<textarea name="memo" class="text"></textarea>
					</td>
				</tr>
	       </table>
			<div class="title-style">
			${message("经营品类")}
				<div class="btns">
					<a href="javascript:;" id="addStoreProductType" class="button">${message("添加")}</a>
		    	</div>
		    </div>
		    <table id="table-p1"></table>	
			<div class="title-style">
			${message("门店稽查")}
				<div class="btns">
					<a href="javascript:;" id="addShopInspection" class="button">${message("添加")}</a>
		    	</div>
		    </div>
		    <table id="table-p2"></table>	
			<div class="title-style">
			${message("门店设计")}
				<div class="btns">
					<a href="javascript:;" id="addShopDesign" class="button">${message("添加")}</a>
		    	</div>
		    </div>
		    <table id="table-p3"></table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}" />
		</div>
	</form>
</body>
</html>