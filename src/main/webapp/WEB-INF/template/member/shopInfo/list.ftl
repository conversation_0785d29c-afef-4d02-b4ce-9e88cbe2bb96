<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("客户信息")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	function add(){
		parent.change_tab(0,'add.jhtml');
	}
	
	function edit(id){
		parent.change_tab(0,'edit.jhtml?isEdit=${isEdit}&id='+id);
	}
	function customer_import(e){
		excel_import(e,{
			title:'${message("门店导入")}',
			url:'/member/store/import_excel.jhtml',
			template:'/resources/template/member/store.xls',
			callback:function(){
				$("#searchBtn").click();
			
			}
		});
	}

	//条件导出		    
	function segmentedExport(e){
		var needConditions = false;
		var page_url = '/member/shopInfo/to_condition_export.jhtml';//分页导出统计页面
		var url = '/member/shopInfo/condition_export.jhtml';//导出的方法
		conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
	}

	//选择导出
	function exportExcel(t){
		var param = $mmGrid.serializeSelectedIds();//参数
		var tip = '${message("请选择导出的门店信息")}';//提示
		var url = '/member/shopInfo/selected_export.jhtml';//导出的方法
	  	select_export(t,{tip:tip, param:param, url:url});
	}
	$().ready(function() {
		
		var cols = [
		{ title:'${message("门店编码")}', name:'sn' ,width:100, align:'center', renderer:function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+item.sn+'</a>';
		}},
		{ title:'${message("授权编号")}', name:'authorization_code', align:'center'},
		{ title:'${message("经销商姓名")}', name:'distributor_name', align:'center'},
		{ title:'${message("建店日期")}', name:'open_date', align:'center' ,renderer:function(val){
			if(val!=null && val.length > 10){
				var str = val;
				return str.substring(0,10);	 
			}
		}},
		{ title:'${message("加盟日期")}', name:'join_date', align:'center' ,renderer:function(val){
			if(val!=null && val.length > 10){
				var str = val;
				return str.substring(0,10);	 
			}
		}},
		{ title:'${message("经销商手机号码")}', name:'distributor_phone', align:'center'},
		{ title:'${message("固定号码")}', name:'tel', align:'center'},
		{ title:'${message("传真")}', name:'fax', align:'center'},
		{ title:'${message("位置类型")}', name:'position_type', align:'center'},
		{ title:'${message("面积")}', name:'acreage', align:'center'},
		{ title:'${message("地区")}', name:'area_name', align:'center'},
		{ title:'${message("详细地址")}', name:'address', align:'center'},
		{ title:'${message("所属销售平台")}', name:'sales_platform', align:'center'},
		{ title:'${message("最近一次装修日期")}', name:'decoration_date', align:'center' ,renderer:function(val){
			if(val!=null && val.length > 10){
				var str = val;
				return str.substring(0,10);	 
			}
		}},
		{ title:'${message("销售渠道")}', name:'sales_channel', align:'center'},
		{ title:'${message("销售渠道备注")}', name:'sales_channel_note', align:'center'},
		{ title:'${message("门店状态")}', name:'status', align:'center'},
		{ title:'${message("新增档案编号")}', name:'increase_archives_code', align:'center'},
		{ title:'${message("减少档案编号")}', name:'decrease_archives_code', align:'center'},
		{ title:'${message("业态")}', name:'business_form', align:'center'},
		{ title:'${message("门店类别")}', name:'type', align:'center'},
		{ title:'${message("所含品牌")}', name:'inclusive_brand', align:'center'},
		{ title:'${message("经营品类")}', name:'business_category', align:'center'},
		{ title:'${message("VI版本")}', name:'vi_version', align:'center'},
		{ title:'${message("招牌")}', name:'shop_sign', align:'center'},
		{ title:'${message("对接人号码")}', name:'contact_phone', align:'center'},
		{ title:'${message("门店销量")}', name:'sales_volume', align:'center'},
		{ title:'${message("所属部门")}', name:'department', align:'center'},
		{ title:'${message("城市行政等级")}', name:'administrative_rank', align:'center'},
		{ title:'${message("备注")}', name:'memo', align:'center' ,width:150},
		{ title:'${message("创建日期")}', name:'create_date' ,width:120, align:'center'},
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
		fullWidthRows:true,
        cols: cols,
        url: 'list_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });

});

	function shopInfo_import(e){
		excel_import(e,{
			title:'${message("门店资料导入")}',
			url:'import_excel.jhtml',
			template:'/resources/template/member/shopInfo.xls',
			callback:function(){
				$("#searchBtn").click();
			
			}
		});

	}
</script>

</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<input type="hidden" name="isMember" value="${isMember}"/>
		<div class="bar">
		<div class="buttonWrap">
	        <div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="shopInfo_import(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
					</ul>
				</div>
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
					<dl>
						<dt><p>${message("门店编码")}:</p></dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="sn" value=""  btn-fun="clear">
						</dd>
					</dl>
		        	<dl>
						<dt><p>${message("经销商姓名")}:</p></dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="distributorName" value=""  btn-fun="clear">
						</dd>
					</dl>
				
		        </div>
			</div>
			
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>