<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("工程")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
function add(){
	parent.change_tab(0,'/member/visual_report/add.jhtml');
}
$().ready(function() {
	
	var types = {'index':'${message("首页")}', 'store':'${message("客户")}', 'shop':'${message("门店")}'};
	var cols = [
		{ title:'${message("名称")}', name:'name' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/member/visual_report/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("按钮类型")}', name:'type',align:'center',renderer:function(val,item){
			var result = types[val];
			if(result!=undefined)return result;	
		}},
		{ title:'${message("状态")}', name:'isDefault' ,align:'center',renderer:function(val,item){
			if(val=='1'){
				return '<span class="green">生效</span>';
			}else if(val=='0'){
				return '<span class="red">失效</span>';
			}
		} },
		{ title:'${message("class")}', name:'className',align:'center'},
		{ title:'${message("排序")}', name:'serialNumber',align:'center'},
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/member/visual_report/list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
});


</script>
</head>
<body>
	<form id="listForm" action="/basic/sbu/list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<a href="javascript:add();" class="iconButton" id="addButton"><span
					class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div id="searchDiv">
				<div id="search-content">
					<dl>
						<dt>
							<p>${message("分类")}：</p>
						</dt>
						<dd>
							<select name="category" class="text">
								<option></option>
								<option value="A">A</option>
								<option value="B">B</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("按钮类型")}：</p>
						</dt>
						<dd>
							<select name="type" class="text">
								<option></option>
								<option value="0">首页</option>
								<option value="1">客户</option>
								<option value="2">门店</option>
							</select>
						</dd>
					</dl>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator" style="text-align: left;">
				<div id="paginator"></div>
			</div>
		</div>
	</form>
</body>
</html>