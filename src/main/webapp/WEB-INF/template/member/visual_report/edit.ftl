<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑首页权限按钮")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">

$().ready(function() {
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	});
});

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑按钮")}
	</div>
	<form id="inputForm" action="/member/visual_report/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${vr.id}" />
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>${message("名称")}:
					</th>
					<td>
						<input type="text" class="text" name="name" value="${vr.name}" btn-fun="clear"/>
					</td>
					<th>
						<span class="requiredField">*</span>${message("是否启用")}:
					</th>
					<td>
						<select class="text" name="isDefault">
						  <option value ="true" [#if vr.isDefault==true]selected[/#if]>是</option>
						  <option value ="false" [#if vr.isDefault==false]selected[/#if]>否</option>
						</select>
					</td>
					<th>
						<span class="requiredField">*</span>${message("排序")}:
					</th>
					<td>
						<input type="text" class="text" name="serialNumber" value="${vr.serialNumber}" btn-fun="clear"/>
					</td>
				</tr>
				<tr>
					<th>
						<span class="requiredField">*</span>${message("分类")}:
					</th>
					<td>
						<input type="text" name="category" class="text" maxlength="200" value="${vr.category}" btn-fun="clear" />
					</td>
				</tr>
			</table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>