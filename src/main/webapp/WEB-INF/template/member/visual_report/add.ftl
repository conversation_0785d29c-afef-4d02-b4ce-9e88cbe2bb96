<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增首页权限按钮")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">

	$().ready(function() {
		var $inputForm = $("#inputForm");
		
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= '/member/visual_report/edit.jhtml?id='+resultMsg.objx;
				});
			}
		 });
	
	});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增按钮")}
	</div>
	<form id="inputForm" action="/member/visual_report/save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>${message("名称")}:
					</th>
					<td>
						<input type="text" name="name" class="text" maxlength="200"  btn-fun="clear" />
					</td>

					<th><span class="requiredField">*</span>${message("是否启用")}:</th>
					<td>
						<select class="text" name="isDefault">
						  <option value ="true">是</option>
						  <option value ="false">否</option>
						</select>
					</td>
					<th>
						<span class="requiredField">*</span>${message("序列")}:
					</th>
					<td>
						<input type="text" name="serialNumber" class="text" maxlength="200"  btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("分类")}:</th>
					<td>
						<input type="text" name="category" class="text" maxlength="200"  btn-fun="clear" />
					</td>
				</tr>
			</table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>