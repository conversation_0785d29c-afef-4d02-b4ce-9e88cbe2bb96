<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("编辑经营组织")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	$().ready(function() {
			
		$inputForm = $("#inputForm");
			
			// 表单验证
		$inputForm.validate({
			rules: {
	    		name: "required"	
			} ,
			submitHandler:function(form){
				return false;
			}
		});
			
		$("form").bindAttribute({
			isConfirm:true,
		    callback: function(resultMsg){
		        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
		        	parent.change_tab(1,'list.jhtml');
				})
		    }
		 });
	});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看经营组织")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
        <input type="hidden" name="id" value="${organization.id}" />
	<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("组织名称")}:
				</th>
				<td>
					<input type="text" name="name" class="text" maxlength="200"   btn-fun="clear" value="${organization.name}"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("组织编码")}:
				</th>
				<td>
					<input type="text" name="code" class="text" maxlength="200"   btn-fun="clear" value="${organization.code}"/>
				</td>
				<th>
					${message("组织类型")}:
				</th>
				<td>
					<select name="type" class="text">
						<option value >请选择</option>
						<option value=0 [#if organization.type == 0]selected="selected"[/#if]>经营组织</option>
						<option value=1 [#if organization.type == 1]selected="selected"[/#if]>仓库组织</option>
					</select>
				</td>
				<th>
					${message("是否启用")}:
				</th>
				<td>
					<select name="isEnabled" class="text">
						<option value>请选择</option>
						<option value=0 [#if organization.isEnabled == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if organization.isEnabled == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("创建人")}:
				</th>
				<td>
					<input type="text" name="bCreater" class="text" maxlength="200"   btn-fun="clear" value="${organization.bCreater}" readonly="true"/>
				</td>
				<th>
					${message("创建时间")}:
				</th>
				<td>
					<input type="text" name="createDate" class="text" maxlength="200"   btn-fun="clear" value="${organization.createDate?string("yyyy-MM-dd HH:mm:ss")}" readonly="true"/>
				</td>
				<th>
					${message("最后修改人")}:
				</th>
				<td>
					<input type="text" name="bModifier" class="text" maxlength="200"   btn-fun="clear" value="${organization.bModifier}" readonly="true"/>
				</td>
				<th>
					${message("最后修改时间")}:
				</th>
				<td>
					<input type="text" name="modifyDate" class="text" maxlength="200"   btn-fun="clear" value="${organization.modifyDate?string("yyyy-MM-dd HH:mm:ss")}" readonly="true"/>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("是否平台")}:
				</th>
				<td>
					<label>
						<input type="checkbox" name="platformOrNot" value="true" [#if organization.platformOrNot]checked="checked"[/#if]/>${message("是否启用")}
						<input type="hidden" name="platformOrNot" value="false" />
					</label>
				</td>			
			</tr>
		</table>
		</div>
		<div class="fixed-top">
				<a href="add.jhtml?type=${type}" class="iconButton" id="addButton">
					<span class="addIcon">&nbsp;</span>${message("1001")}
				</a>
				<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>