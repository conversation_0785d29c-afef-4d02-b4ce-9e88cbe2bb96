<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css">
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css">
    <link href="/resources/css/wf.css" rel="stylesheet" type="text/css">
    <link href="/resources/css/layout.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js?version=0.03"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
    <script>
        $().ready(function(e) {

            /**初始化多选的下拉框*/
            initMultipleSelect();

            loadLeftData(0);
            loadLeftData(2);
            loadLeftData(3);

            $("#js-menu dt").click(function(){
                var $dl = $(this).closest("dl");
                if(!$dl.hasClass("noline_docu")){
                    $(this).closest("dl").toggleClass("noline_close").toggleClass("noline_open");
                }
            });
            $("#js-menu dd a").click(function(){
                $(".client-list dd a").removeClass("on")
                $(this).addClass("on");
            });

            var stats = {'1':'${message("审核中")}','2':'${message("已完成")}','3':'${message("驳回")}'};

            var cols = [
                { title:'${message("流程名称")}', name:'wf_name' ,width:200, align:'center', renderer: function(val,item,rowIndex){
                        var url = item.url;
                        if(url.indexOf("?")>=0){
                            url+='&objid='+item.obj_id+'&objTypeId='+item.obj_type;
                        }else{
                            url+='?objid='+item.obj_id+'&objTypeId='+item.obj_type;
                        }
                        return '<a href="javascript:void(0);" onClick="check_view(\''+url+'\')" class="red">'+item.model_name+' SN:'+item.wf_name+'</a>';

                    }},
                //{ title:'${message("对象类型")}', name:'obj_type_name',width:150, align:'center'},
                //{ title:'${message("流程模板名称")}', name:'model_name',width:200, align:'center'},
                { title:'${message("启动人")}', name:'start_name', width:100, align:'center' },
                { title:'${message("启动时间")}', name:'create_date', width:100, align:'center' },
                { title:'${message("节点审核人")}', name:'current_name', width:180, align:'center' },
                { title:'${message("接收时间")}', name:'last_time', width:100, align:'center' },
                { title:'${message("流程状态")}', name:'stat', align:'center', width:100,  renderer: function(val,item,rowIndex){
                        return stats[val];
                    }},
                { title:'${message("创建时间")}', name:'create_date', width:150, align:'center'}
            ];

            $mmGrid = $('#table-m1').mmGrid({
                cols: cols,
                fullWidthRows:true,
                autoLoad:true,
                url: 'list_data.jhtml',
                params:function(){
                    return $("#listForm").serializeObject();
                },
                plugins : [
                    $('#paginator').mmPaginator()
                ]
            });

            $(".total_num").each(function(){
                var num = $(this).val();
                $(this).closest("dl").find(".total").text(num);
            })

            var $a = $(".wf-a:first").addClass("on");
            $a.closest("dl").removeClass("noline_close").addClass("noline_open");

            $("#selectStore").bindQueryBtn({
                type:'store',
                title:'${message("查询客户")}',
                url:'/member/store/select_store.jhtml'
            });

            treeScoll();
        });

        function link_to(flag,objType,objTypeName){
            $("input[name='flag']").val(flag);
            $("input[name='objType']").val(objType);
            $("#objTypeName").val(objTypeName);
            $mmGrid.load();
        }

        function check_view(url){
            var w = $(window).width();
            var h = $(window).height();
            var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
            var $dialog = $.dialog({
                title:'${message("查看流程")}',
                width:w,
                height:h,
                content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
                ok: null,
                cancel: null,
                onOk: function() {

                }
            });
            $dialog.find(".dialogContent").css("height",h+"px");
            $dialog.css("top",0+"px").css("max-height",h+"px");

        }

        function loadLeftData(flag){
            var url = "/new/act/wf/count.jhtml";
            $.ajax({
                url: url,
                data:{flag:flag},
                success: function(result){
                    var dl = $("#countData"+flag);
                    var dd = dl.find("dd");
                    var totalSpan = dl.find(".total");
                    var total = 0;
                    console.log(result.objx);
                    $.each(result.objx,function(index,value){
                        var num = value.num;
                        var a = '<a href="javascript:void(0);" onclick="link_to('+flag+',\''+value.obj_type+'\',\''+value.obj_type_name+'\')" class="wf-a">'+value.obj_type_name+'<b class="red">('+num+')</b></a>';
                        dd.append(a);
                        total += num;
                    })
                    totalSpan.text(total);
                    if(total > 0){
                        dl.removeClass("noline_docu");
                        dl.addClass("noline_close");
                    }

                }
            });
        }

    </script>
</head>
<body class="tree-contain">
<div class="flow-boxL">
    <div class="title">流程管理</div>
    <div class="client-list" id="js-menu">
        <dl [#if configs1?size>0]class="noline_close"[#else]class="noline_docu"[/#if]>
            <dt>
                <a href="javascript:void(0);" target="">当前需要处理的<b class="red">(<span class="total">0</span>)</b></a>
            </dt>
            <dd>
                [#assign total2=0]
                [#list configs1 as config]
                    [#assign total2=total2+config.num]
                    [#if config_first==null]
                        [#assign config_first=config]
                        [#assign flag=1]
                    [/#if]
                    <a href="javascript:void(0);" onclick="link_to(1,'${config.obj_type}','${config.obj_type_name}')" class="wf-a">
                        ${config.obj_type_name}<b class="red">(${config.num})</b>
                    </a>
                [/#list]
                <input type="hidden" class="total_num" value="${total2}">
            </dd>
        </dl>
        <dl class="noline_docu" id="countData0">
            <dt>
                <a href="javascript:void(0);" target="">我启动的<b class="red">(<span class="total" >loading</span>)</b></a>
            </dt>
            <dd>
                [#--<a href="javascript:void(0);" onclick="link_to(0,'${config.obj_type}','${config.obj_type_name}')" class="wf-a">
                    ${config.obj_type_name}
                    <b class="red">(${config.num})</b>
                </a>--]
                <!-- <input type="hidden" class="total_num" value="${total1}"> -->
            </dd>
        </dl>
        <dl class="noline_docu" id="countData2">
            <dt>
                <a href="javascript:void(0);" target="">已处理未结束的<b class="red">(<span class="total" >loading</span>)
                    </b></a>
            </dt>
            <dd>
                [#--<a href="javascript:void(0);" onclick="link_to(2,'${config.obj_type}','${config.obj_type_name}')" class="wf-a">
                    ${config.obj_type_name}
                    <b class="red">(${config.num})</b>
                </a>--]
                <!-- <input type="hidden" class="total_num" value="${total3}"> -->
            </dd>
        </dl>
        <dl class="noline_docu" id="countData3">
            <dt>
                <a href="javascript:void(0);" target="">已结束的<b class="red">(<span class="total" >loading</span>)</b></a>
            </dt>
            <dd>
                [#-- <a href="javascript:void(0);" onclick="link_to(3,'${config.obj_type}','${config.obj_type_name}')" class="wf-a">
                    ${config.obj_type_name}
                    <b class="red">(${config.num})</b>
                </a> --]
                <!-- <input type="hidden" class="total_num" value="${total4}"> -->
            </dd>
        </dl>
    </div>
</div>
<label id="labBtn" style="left: 272px;"></label>
<div class="flow-boxR">
    <form id="listForm" action="" method="get">
        <input type="hidden" name="flag" value="${flag!1}">
        <input type="hidden" name="objType" value="${config_first.obj_type}">
        <div class="bar">
            <div class="buttonWrap"></div>
            <div class="search-btn">
                <a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
            </div>
            [#--搜索begin--]
            <div id="searchDiv">
                <div id="search-content">
                    <dl>
                        <dt>
                            <p>${message("对象类型")}:</p>
                        </dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" id="objTypeName" value="${config_first.obj_type_name}" readonly>
                        </dd>
                    </dl>
                    <dl>
                        <dt>
                            <p>${message("流程名称")}:</p>
                        </dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="wfName" value="" btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt>
                            <p>${message("流程状态")}:</p>
                        </dt>
                        <dd>
                            <div class="checkbox-style">
                                <a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
                                <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                                <div class="statusList cs-box" data-value="off">
                                    <label><input class="check js-iname" name="stat" value="1" type="checkbox" />${message("审核中")}</label>
                                    <label><input class="check js-iname" name="stat" value="2" type="checkbox" />${message("已完成")}</label>
                                    <!-- <label><input  class="check js-iname" name="stat" value="3" type="checkbox"/>${message("驳回")}</label> -->
                                </div>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>

            [#--搜索end--]
        </div>
        <div class="table-responsive">
            <table id="table-m1"></table>
            <div id="body-paginator" style="text-align:left;left: 216px;position: static;">
                <div id="paginator"></div>
            </div>
        </div>
    </form>
</div>
</body>
</html>