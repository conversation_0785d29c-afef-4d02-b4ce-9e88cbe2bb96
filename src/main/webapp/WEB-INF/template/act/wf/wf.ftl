<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
		<script type="text/javascript" src="/resources/js/reference/jquery.SuperSlide.2.1.1.js"></script>
		<link href="/resources/css/wf.css" rel="stylesheet" type="text/css" />
		<script type="text/javascript" src="/resources/js/base/act_wf.js"></script>
		<script type="text/javascript">
			$().ready(function() {
				wf_id = $("#wf_id").val();
				if(wf_id!=''){
					$("#tas").load("/act/wf/wf_view.jhtml?wfid="+wf_id);
					reflush_option_list();
				}
			});
			
			
			function reflush_option_list(){
			    var wfProcs = ${wfProcs!'[]'};
				var cols = [	
					{ title:'${message("任务名称")}', name:'activityName' ,width:150,align:'center'},
					{ title:'${message("任务状态")}', name:'activityState' ,width:150,align:'center',renderer:function(val,item){
						var str = "";
						if (val==0) str = "已执行";
						if (val==1) str = "待执行";
						if (val==2) str = "未执行";
						return str;
					}},
					{ title:'${message("处理人")}', name:'assigneeName',width:150 ,align:'center'},
					{ title:'${message("处理时间")}', name:'endTime' ,width:150 ,align:'center'},
					{ title:'${message("处理结果")}', name:'approved' ,width:150 ,align:'center',renderer:function(val,item){
						var html = '';
						if (val=="true") html += '<span class="green">提交</span>';
						if (val=="false") html += '<span class="red">驳回</span>';
						return html;
					}},
					{ title:'${message("处理意见")}', name:'suggestion' ,width:150 ,align:'center'}
				];
				
				$mmGrid = $('#table-option_list').mmGrid({
					width:'auto',
		            height:'auto',
		            cols: cols,
		            fullWidthRows:true,
		            checkCol: false,
		            items:wfProcs,
		            autoLoad: true,
		            callback:function(){
		            	
		            }
		        });
			
		}
    </script>
</head>
    <body>
    	<input type="hidden" id="wf_id" value="${wf.id}">
    	<input type="hidden" id="procid" value="">
    	[#if wf!=null]
	    	<div class="wf-area-title"><b>流程审批</b></div>
	        <div class="flow-wrap" id="wf_html">
				<div id="tas"></div>
			    <div class="clearfix"></div>
			    <div class="flow-step-tab" style="margin-bottom:100px;">
			    	<div class="hd">
			        	<ul class="fl">
			            	<li class="on">流程明细</li>
			            </ul>
			        </div>
			        <div class="bd">
			             <ul style="height: 380px; overflow: auto;">
			             	<table id="table-option_list"></table>
			             </ul>
			        </div>
			    </div>
			    <script>jQuery(".flow-step-tab").slide({trigger:"click"});</script>
			</div>
		[/#if]
    </body>
</html>