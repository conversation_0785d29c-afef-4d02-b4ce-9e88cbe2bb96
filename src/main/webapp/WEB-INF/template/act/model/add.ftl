<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("添加流程模板")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
		
	$inputForm = $("#inputForm");
		
		// 表单验证
	$inputForm.validate({
		rules: {
    		name: "required",	
    		key: "required"
		} ,
		submitHandler:function(form){
			return false;
		}
	});

	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'edit.jhtml?id='+resultMsg.objx;
			})
	    }
	 });
});
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增流程模板")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("名称")}:
				</th>
				<td>
					<input type="text" name="name" class="text" btn-fun="clear" />
				</td>
				<th>
					<span class="requiredField">*</span>${message("关键字")}:
				</th>
				<td>
					<input type="text" name="key" class="text" btn-fun="clear" />
				</td>
				<th>
					${message("类别")}:
				</th>
				<td>	
					<input type="text" name="category" class="text" btn-fun="clear" />
				</td>
				<th>
					${message("创建日期")}:
				</th>
				<td></td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>