<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/icon.css">
<script type="text/javascript" src="/resources/plugin/jquery-easyui-*******/jquery.easyui.min.js"></script>
<script type="text/javascript" src="/resources/js/base/datagrid-dnd.js"></script>
<script type="text/javascript">
$().ready(function() {

	//列表展示图(342*342)
    $("input[name='image']").single_upload({
		uploadSize:"medium"
	});

	 // 表单验证
	$inputForm.validate({
		rules: {
			objTypeName: "required",
			objClass: "required",
			tableName: "required",
			wfTempId: "required"
		}
	});

	$("form").bindAttribute({
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'edit.jhtml?id='+resultMsg.objx;
			});
		}
	 });
	 
});
</script>
</head>
<body>
<div class="pathh">&nbsp;${message("新增对象类型")}</div>
<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
    <div class="tabContent">
    	<table class="input input-edit">
            <tr>
            	<th>
                    <span class="requiredField">*</span>${message("对象类型名称")}:
                </th>
                <td>
                   <input type="text" class="text" name="objTypeName" btn-fun="clear" value="">
                </td>
                <th>
                    <span class="requiredField">*</span>${message("对象类名")}:
                </th>
                <td>
                	<input type="text" class="text" name="objClass" btn-fun="clear" value="">
                </td>
            	<th>
            		 <span class="requiredField">*</span>${message("对象表名")}:
            	</th>
            	<td>
            		<input type="text" class="text" name="tableName" btn-fun="clear" value="">
            	</td>
            	<th>${message("创建时间")}:</th>
            	<td></td>
            </tr>
               </table>
            <table class="input input-edit" style="width:100%;margin-top:5px;">
            <tr>
            	<th>
            		 <span class="requiredField">*</span>${message("流程模版")}:
            	</th>
            	<td>
            		<select name="wfTempId" class="text">
            			[#list wfTemps as wfTemp]
            				<option value="${wfTemp.id}">${wfTemp.wfTempName}</option>
            			[/#list]
            		</select>
            	</td>
            	<th>
                   ${message("备注")}:
                </th>
                <td colspan="3">
                	<textarea name="note" class="text"></textarea>
                </td>
                <th>
					${message("16035")}:
				</th>
				<td>
					<label>
						<input type="checkbox" name="isFlow" value="true" checked="checked" />${message("是否需要审批流程")}
					</label>&nbsp;&nbsp;
					<label>
						<input type="hidden" name="isBill" value="true" checked="checked" />
					</label>
				</td>
            </tr>
            <tr>
            	 <th>
					<p style="line-height:normal">${message("图片")}</p>(342×342):
				</th>
				<td colspan="7">
					<input type="hidden" name="image" value=""/>
				</td>
            </tr>
        </table>
    </div>
    <div class="fixed-top">
        <input type="submit" id="submit_button" class="button sureButton" value="${message("保存")}">
        <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
    </div>
</form>
</body>
</html>