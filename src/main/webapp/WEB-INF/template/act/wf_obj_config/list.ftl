<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("整机列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/icon.css">
<script type="text/javascript" src="/resources/plugin/jquery-easyui-*******/jquery.easyui.min.js"></script>
<script type="text/javascript" src="/resources/js/base/datagrid-dnd.js"></script>
<script type="text/javascript">
$().ready(function() {
	
	//冻结列
	var frozenColumns = [[
		{field:'id',title:'ids',width:25,checkbox:true,},//复选框
		{ title:'${message("对象类型名称")}', field:'obj_type_name',width:180 ,align:'center',isIcon:true, formatter: function(val,item,rowIndex){
			var	html = '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
			return html;
		}},
	]];
	var cols = [[
		{ title:'${message("流程模版名称")}', field:'wf_temp_name',width:180 ,align:'center'},
		{ title:'${message("备注")}', field:'note',width:180 ,align:'center'},
		{ title:'${message("是否需要审批流程")}', field:'is_flow',width:180 ,align:'center', formatter: function(val){
			if(val=='1'){
				return '<span class="trueIcon">&nbsp;</span>';
			}else if(val=='0'){
				return '<span class="falseIcon">&nbsp;</span>';
			}
		}},
		// { title:'${message("是否为单据")}', field:'is_bill',width:150 ,align:'center',formatter: function(val){
		// 	if(val=='1'){
		// 		return '<span class="trueIcon">&nbsp;</span>';
		// 	}else if(val=='0'){
		// 		return '<span class="falseIcon">&nbsp;</span>';
		// 	}
		// }},
		{ title:'${message("创建日期")}', field:'create_date', width:180 ,align:'center' }

	]];
    //列表参数
	var options = {    
		tableName:"",//列表名（唯一），
		path: 'list_data.jhtml',//数据请求地址
		params:function(){//额外请求参数
			return $("#listForm").serializeObject();
		},
		//mergeMainId:"id",//合并行的主键
		frozenColumns:frozenColumns,//冻结列
		columns: cols,//普通列
		callback:function(data){//回调
		
		}
	}
	//初始化列表	 
	initEasyUiTable('#table-m1',options);
});

</script>
</head>
<body>
	<form id="listForm">
		<input type="hidden" name="stat" id="stat" value="${stat}" />
		<div class="bar">
			<div class="buttonWrap">
			</div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton" onclick="doSearch('#table-m1')">${message("1004")}</a></div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
					<dt><p>${message("对象类型名称")}:</p></dt>
					<dd>
						<input class="text" maxlength="200" type="text" name="objTypeName" value=""  btn-fun="clear">
					</dd>
				</dl>
				<dl>
					<dt><p>${message("流程模版名称")}:</p></dt>
					<dd>
						<input class="text" maxlength="200" type="text" name="wfTempName" value=""  btn-fun="clear">
					</dd>
				</dl>
				<dl>
					<dt><p>${message("是否审批流程")}:</p></dt>
					<dd>
						<select name="isFlow" class="text">
							<option value>请选择</option>
							<option value="0">否</option>
							<option value="1">是</option>
						</select>						
					</dd>
				</dl>
				[#--<dl>
					<dt><p>${message("是否为单据")}:</p></dt>
					<dd>
						<select name="isBill" class="text">
							<option value>请选择</option>
							<option value="0">否</option>
							<option value="1">是</option>
						</select>
					</dd>
				</dl>--]
			</div>
			</div>
		</div>
		<div class="table-responsive" id="table-m1" data-options="resizeEdge:10,edge:10"></div>
	</form>
</body>
</html>