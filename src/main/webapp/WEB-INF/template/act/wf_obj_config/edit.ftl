<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/default/easyui.css">
<link rel="stylesheet" type="text/css" href="/resources/plugin/jquery-easyui-*******/themes/icon.css">
<script type="text/javascript" src="/resources/plugin/jquery-easyui-*******/jquery.easyui.min.js"></script>
<script type="text/javascript" src="/resources/js/base/datagrid-dnd.js"></script>
<style>
	label.condition{
		width: 100px;
		display: inline-block;
	}
	label.name{
		width: 100%;
		display: block;
		margin-left:3px;
	}
	div.right-top-button input{
		float:none;
	}
</style>
<script type="text/javascript">
$().ready(function() {

	var itemIndex=0;
	var items = ${wfConfigWfTemps};
	
	var cols = [[				
		{ title:'${message("模版名称")}', field:'wf_temp_name' ,align:'center', width:300, formatter: function(val,item,rowIndex){
			var html;
			if(obj==undefined){
				html = '<input type="hidden" name="modelId" class="wfTempId_'+item.model_id+'" value="'+item.model_id+'">'+val;
			}else{
				html = '<input type="hidden" name="modelId" class="wfTempId_'+item.ID_+'" value="'+item.ID_+'">'+item.NAME_;
			}
			return html;
		}},
		{ title:'${message("操作")}', field:'cz' ,align:'center', width:150, formatter: function(val,item,rowIndex){
			itemIndex++;
			return '<a href="javascript:;" class="deleteProduct btn-delete">删除</a>';
		}}
	]];
	
    //列表参数
	var options = {    
		tableName:"actWfObjConfigEdit",//列表名（唯一），
		data:items,//初始化数据
		pagination:false,//是否使用分页
		//mergeMainId:"id",//合并行的主键
		columns: cols,//普通列
		callback:function(data){//回调

		}
	}
	//初始化列表	 
	initEasyUiTable('#table-m1',options);
    
    //打开选择产品界面
    var $addwfTemp = $("#addwfTemp");
	$addwfTemp.click(function(){
		$addwfTemp.bindQueryBtn({
			type:'wfTem',
			bindClick:false,
			title:'${message("查询流程模版")}',
			url:'/act/model/select_list.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".wfTempId_"+rows[i].ID_).length;
						if(idH > 0){
							$.message_alert('流程模版【'+rows[i].NAME_+'】已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$('#table-m1').datagrid('addRow',{row: row,obj:1});
					}
				}
			}
		});	
	})
	// 删除产品
	var $deleteProduct = $(".deleteProduct");
	$deleteProduct.live("click", function() {
		var index = $(this).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$('#table-m1').datagrid('removeRow',index);
		})
	});

	 // 表单验证
	 var $inputForm = $("#inputForm");
	$inputForm.validate({
		rules: {
			objTypeName: "required",
			wfTempId: "required"
		}
	});
	
	$("form").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			});
		}
	 });

});
</script>
</head>
<body>
<div class="pathh">&nbsp;${message("查看单据对象")}</div>
<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
	<input type="hidden" name="id" value="${wfObjConfig.id}">
    <div class="tabContent">
    	<table class="input input-edit">
            <tr>
            	<th>
                    <span class="requiredField">*</span>${message("对象类型名称")}:
                </th>
                <td>
                   <input type="text" class="text" name="objTypeName" btn-fun="clear" value="${wfObjConfig.objTypeName}">
                </td>
            	 <th>
					${message("16035")}:
				</th>
				<td>
					<label>
						<input type="checkbox" [#if wfObjConfig.isFlow]checked[/#if] disabled/>
						${message("是否需要审批流程")}
						<input type="hidden" name="isFlow" value="[#if wfObjConfig.isFlow]true[#else]false[/#if]">
					</label>&nbsp;&nbsp;
					<label>
						<input type="hidden" name="isBill" value="true" [#if wfObjConfig.isBill]checked[/#if] />
						[#--${message("是否为单据")}
						<input type="hidden" name="_isBill" value="false">--]
					</label>
				</td>
            	<th>${message("创建时间")}:</th>
            	<td>${wfObjConfig.createDate?string("yyyy-MM-dd HH:mm:ss")}</td>
            	<th class="noBg"></th><td></td>
            </tr>
            <tr>
            	<th>
                   ${message("备注")}:
                </th>
                <td colspan="7">
                	<textarea name="note" class="text">${wfObjConfig.note}</textarea>
                </td>
            </tr>
           </table>
	        <div class="title-style">
				${message("流程模版")}:
	        	<div class="btns">
					<a href="javascript:void(0);" id="addwfTemp" class="button">${message("添加流程模版")}</a>
	        	</div>
	    	</div>
	    	<table id="table-m1"></table>
    </div>
    <div class="fixed-top">
        <input type="submit" id="submit_button" class="button sureButton" value="${message("保存")}">
        <input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
    </div>
</form>
</body>
</html>