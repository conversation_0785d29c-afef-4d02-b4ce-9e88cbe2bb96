<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8"/>
	<title>${message("编辑售后申请单据")}</title>
	<link href="/resources/css/common.css" rel="stylesheet" type="text/css"/>
	<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
	<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
	<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
	<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
	<script type="text/javascript" src="/resources/js/base/request.js"></script>
	<script type="text/javascript" src="/resources/js/base/global.js?version=0.03"></script>
	<script type="text/javascript" src="/resources/js/base/file.js?version=0.03"></script>
	<script type="text/javascript" src="/resources/js/base/textarea.js"></script>
	<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
	<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css"/>
	<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
	<link href="/resources/css/preview-swiper.min.css" rel="stylesheet" type="text/css"/>
	[#--<script type="text/javascript" src="/resources/js/reference/preview-swiper.min.js"></script>
	<script type="text/javascript" src="/resources/js/base/component.js"></script>--]

	<style>
		.underline {
			width: 80px;
			border-bottom: 1px solid #dcdcdc;
			text-align: center;
		}

		tr.s-tr, tr.s-tr td {
			height: 10px !important;
		}

		div.w_1135 {
			width: 1135px;
		}

		#atc th, #atc td {
			border: solid 1px #F7F7F7;
		}

	</style>
	<script type="text/javascript">
		$(function () {
			$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off');
		});

		function editPrice(t, e) {
			extractNumber(t, 2, false, e);
			countTotal();
		}


		function countTotal() {
			var $a = $("input[name='layingArea']").val();
			var $b = $("input[name='price']").val();
			var d = accMul($a, $b);
			$(".totalSales").html(currency(d, true));
		}

		function editjisuan(t, e) {
			extractNumber(t, 2, false, e);
			var name = "";
			$('.fd').find("input[name='fd']").each(function () {
				//console.log($(this).attr("checked"));
				if ($(this).attr("checked") == "checked") {
					name = name +  $(this).val();
				}
			});
			console.log("name  " + name)
			var name1 = "";
			$('.cd').find("input[name='cd']").each(function () {
				//console.log($(this).attr("checked"));
				if ($(this).attr("checked") == "checked") {
					name1 = name1 +  $(this).val();
				}
			});
			var factoryPrice = $("input[name='factoryPrice']").val() == "" ? 0 : $("input[name='factoryPrice']").val();//分销价
			var compensation = $("input[name='compensation']").val() == "" ? 0 : $("input[name='compensation']").val();//赔偿用户金额
			var extraCharges = $("input[name='extraCharges']").val() == "" ? 0 : $("input[name='extraCharges']").val();//其他费用
			var total = 0;
			if (name == "全部更换" || name1 == "全部更换" ) {
				var layingArea = $("input[name='layingArea']").val() == "" ? 0 : $("input[name='layingArea']").val();//铺设面积
				total = accMul(layingArea, factoryPrice)
			} else if (name == "局部更换" || name1 == "局部更换" ) {
				var fdPartialReplacement = $("input[name='fdPartialReplacement']").val() == "" ? 0 : $("input[name='fdPartialReplacement']").val();//退换面积
				total = accMul(fdPartialReplacement, factoryPrice)
			}
			if(name.includes("退货") || name1.includes("退货")){
				var fdReturnGoods = $("input[name='fdReturnGoods']").val() == "" ? 0 : $("input[name='fdReturnGoods']").val();//退货面积
				total = accAdd(total, accMul(fdReturnGoods, factoryPrice))
			}
			if(name.includes("换货") || name1.includes("换货")){
				var fdReplaceGoods = $("input[name='fdReplaceGoods']").val() == "" ? 0 : $("input[name='fdReplaceGoods']").val();//退货面积
				total = accAdd(total, accMul(fdReplaceGoods, factoryPrice))
			}
			$("input[name='amount']").val(accAdd(total, accAdd(compensation, extraCharges)));
		}


		function cjadd(t) {
			var name = "";
			$('.fd').find("input[name='fd']").each(function () {
				//console.log($(this).attr("checked"));
				if ($(this).attr("checked") == "checked") {
					name = $(this).val();
					return false;
				}
			});
			var name1 = "";
			$('.cd').find("input[name='cd']").each(function () {
				//console.log($(this).attr("checked"));
				if ($(this).attr("checked") == "checked") {
					name = $(this).val();
					return false;
				}
			});
			//var factoryPrice = $("input[name='factoryPrice']").val() == "" ? 0 : $("input[name='factoryPrice']").val();//分销价
			var fdCompensation = $("input[name='compensation']").val() == "" ? 0 : $("input[name='compensation']").val();//赔付金额
			var extraCharges = $("input[name='extraCharges']").val() == "" ? 0 : $("input[name='extraCharges']").val();//其他费用
			var factoryPrice = $(t).parent().find("input[name='factoryPrice']").val() * 1;
			var total = 0;
			if (name == "全部更换" || name1 == "全部更换" ) {
				var layingArea = $("input[name='layingArea']").val() == "" ? 0 : $("input[name='layingArea']").val();//铺设面积
				total = accMul(layingArea, factoryPrice)
			} else if (name == "局部更换" || name1 == "局部更换" ) {
				var fdPartialReplacement = $("input[name='fdPartialReplacement']").val() == "" ? 0 : $("input[name='fdPartialReplacement']").val();//退换面积
				total = accMul(fdPartialReplacement, factoryPrice)
			} else if (name == "退货" || name1 == "退货" ) {
				var fdReturnGoods = $("input[name='fdReturnGoods']").val() == "" ? 0 : $("input[name='fdReturnGoods']").val();//退货面积
				total = accMul(fdReturnGoods, factoryPrice)
			}
			$("input[name='amount']").val(accAdd(total, accAdd(fdCompensation, extraCharges)));
		}

		//修改平台价合计费用
		function editPlatformAmount(t, e) {
			extractNumber(t, 2, false, e);
			var acr = $('.acreage').val() == "" ? 0 : $('.acreage').val();//平方
			var fp = $("input[name='fdCompensation']").val() == "" ? 0 : $("input[name='fdCompensation']").val();//赔付金额
			var dj = $(t).val() * 1;
			var total = accMul(acr, dj)
			$("input[name='platformAmount']").val(accAdd(total, fp));


		}

		function editPlatformPrice(t) {
			var acr = $('.acreage').val() == "" ? 0 : $('.acreage').val();//平方
			var fp = $("input[name='fdCompensation']").val() == "" ? 0 : $("input[name='fdCompensation']").val();//赔付金额
			var dj = $(t).parent().find("input[name='platformPrice']").val() * 1;
			var total = accMul(acr, dj)
			$("input[name='platformAmount']").val(accAdd(total, fp));
		}

		function assa() {
			var isDeal = $('input[name="isDeal"]:checked').val();
			$('input[name="factoryCompensation"]').val($('input[name="layingArea"]').val());
			if (isDeal == "0") {
				/*for(var i=0;i<$('.fd input:checkbox ').length;i++){
			$('.fd input:checkbox ').eq(i).prop("checked", true)
			hack($('.fd input:checkbox ').eq(i));
		}*/
			}
		}

		function action() {
			var businessType = $("#businessTypeId option:selected").attr("lowerCode");
			if (businessType == "businessSubconditional") {
				$("#business").show();
			} else {
				$("#business").hide();
			}
		}


		$().ready(function () {
			var $inputForm = $("#inputForm");
			var $addSaleOrgs = $("#openSaleOrg");
			var $addOrganization = $("#openOrganization");
			var $addFinancialStaff = $("#openFinancialStaff");
			var $addProducts = $("#openProduct");
			var $areaId = $("#areaId");
			var $newAreaId = $("#newAreaId");
			var $bAreaId = $("#bAreaId");
			var $layingArea = $("input[name='layingArea']").val();
			var $price = $("input[name='price']").val();
			$(".totalSales").html(currency(accMul($layingArea, $price), true));
			// assa();
			$('input[name="isDeal"]').on('click', function () {
				assa();
			});


			if ($(".isSendToQualityCenter").val() == 1) {
				$(".sampleTestApply").show();
			}

			if ($(".whetherDispose select").val() == "1") {
				$(".noWhetherDispose").show();
			} else {
				$(".noWhetherDispose").hide();
			}

			if ($(".marketingPolicy").val() == "1") {
				$(".noMarketingPolicy").show();
			} else {
				$(".noMarketingPolicy").hide();
			}

			$(".marketingPolicy").change(function () {
				var marketingPolicy = $(".marketingPolicy option:selected").val();
				if (marketingPolicy == "2") {
					$(".noMarketingPolicy").hide();
				} else if (marketingPolicy == "1") {
					$(".noMarketingPolicy").show();
				}
			});

			$(".whetherDispose").change(function () {
				var whetherDispose = $(".whetherDispose option:selected").val();
				if (whetherDispose == "2") {
					$(".noWhetherDispose").hide();
				} else if (whetherDispose == "1") {
					$(".noWhetherDispose").show();
				}
			});

			/*属地品质售后判责一：开始*/
			/*责任判定一*/
			if ($(".afterJudgment").val() == "1") {
				$(".qualityIssue").show();
				if ($(".qualityIssue select").val() == "2") {
					$(".productLiability").show();
				}
			} else if ($(".afterJudgment").val() == "0") {
				$(".noQualityIssue").show();
				if ($(".noQualityIssue select").val() == "1") {
					$(".storeDuty").show();
				} else {
					$(".userDuty").show();
				}
			}

			/*责任判定二*/
			if ($(".afterJudgment2").val() === "1") {
				$(".qualityIssue2").show();
				if ($(".qualityIssue2 select").val() == "2") {
					$(".productLiability2").show();
				}
			} else if ($(".afterJudgment2").val() === "0") {
				$(".noQualityIssue2").show();
				if ($(".noQualityIssue2 select").val() === "1") {
					$(".storeDuty2").show();
				} else {
					$(".userDuty2").show();
				}
			}

			/*责任判定三*/
			if ($(".afterJudgment3").val() === "1") {
				$(".qualityIssue3").show();
				if ($(".qualityIssue3 select").val() == "2") {
					$(".productLiability3").show();
				}
			} else if ($(".afterJudgment3").val() === "0") {
				$(".noQualityIssue3").show();
				if ($(".noQualityIssue3 select").val() === "1") {
					$(".storeDuty3").show();
				} else {
					$(".userDuty3").show();
				}
			}
			/*属地品质售后判责一：结束*/


			/*属地品质售后判责一：开始*/
			/*质量或非质量问题*/
			$(".afterJudgment").change(function () {
				var afterJudgment = $(".afterJudgment option:selected").val();
				if (afterJudgment == "1") {
					$(".qualityIssue").show();
					$(".noQualityIssue").hide();
					$(".productLiability").hide();
					$(".storeDuty").hide();
					$(".userDuty").hide();
				} else if (afterJudgment == "0") {
					$(".qualityIssue").hide();
					$(".noQualityIssue").show();
					$(".productLiability").hide();
					$(".storeDuty").hide();
					$(".userDuty").hide();
				} else {
					$(".qualityIssue").hide();
					$(".noQualityIssue").hide();

				}
			});
			/*质量问题二级*/
			$(".qualityIssue").change(function () {
				var qualityIssue = $(".qualityIssue option:selected").val();
				if (qualityIssue == "2") {
					$(".productLiability").show();
					$(".noQualityIssue").hide();
				} else {
					$(".productLiability").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualityIssue").change(function () {
				var noQualityIssue = $(".noQualityIssue option:selected").val();
				if (noQualityIssue == "1") {
					$(".storeDuty").show();
					$(".userDuty").hide();
				} else if (noQualityIssue == "2") {
					$(".userDuty").show();
					$(".storeDuty").hide();
				} else {
					$(".storeDuty").hide();
					$(".userDuty").hide();
				}
			});
			/*属地品质售后判责一：结束*/

			/*属地品质售后判责二：开始*/
			$(".afterJudgment2").change(function () {
				var afterJudgment2 = $(".afterJudgment2 option:selected").val();
				if (afterJudgment2 == "1") {
					$(".qualityIssue2").show();
					$(".noQualityIssue2").hide();
					$(".productLiability2").hide();
					$(".storeDuty2").hide();
					$(".userDuty2").hide();
				} else if (afterJudgment2 == "0") {
					$(".qualityIssue2").hide();
					$(".noQualityIssue2").show();
					$(".productLiability2").hide();
					$(".storeDuty2").hide();
					$(".userDuty2").hide();
				} else {
					$(".qualityIssue2").hide();
					$(".noQualityIssue2").hide();
				}
			});
			/*质量问题二级*/
			$(".qualityIssue2").change(function () {
				var qualityIssue2 = $(".qualityIssue2 option:selected").val();
				if (qualityIssue2 == "2") {
					$(".productLiability2").show();
					$(".noQualityIssue2").hide();
				} else {
					$(".productLiability2").hide();
					$(".productLiability2").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualityIssue2").change(function () {
				var noQualityIssue2 = $(".noQualityIssue2 option:selected").val();
				if (noQualityIssue2 == "1") {
					$(".storeDuty2").show();
					$(".userDuty2").hide();
				} else if (noQualityIssue2 == "2") {
					$(".userDuty2").show();
					$(".storeDuty2").hide();
				} else {
					$(".storeDuty2").hide();
					$(".userDuty2").hide();
				}
			});
			/*属地品质售后判责二：结束*/

			/*属地品质售后判责三：开始*/
			$(".afterJudgment3").change(function () {
				var afterJudgment3 = $(".afterJudgment3 option:selected").val();
				if (afterJudgment3 == "1") {
					$(".qualityIssue3").show();
					$(".noQualityIssue3").hide();
					$(".productLiability3").hide();
					$(".storeDuty3").hide();
					$(".userDuty3").hide();
				} else if (afterJudgment3 == "0") {
					$(".qualityIssue3").hide();
					$(".noQualityIssue3").show();
					$(".productLiability3").hide();
					$(".storeDuty3").hide();
					$(".userDuty3").hide();
				} else {
					$(".qualityIssue3").hide();
					$(".noQualityIssue3").hide();
				}
			});
			/*质量问题二级*/
			$(".qualityIssue3").change(function () {
				var qualityIssue3 = $(".qualityIssue3 option:selected").val();
				if (qualityIssue3 == "2") {
					$(".productLiability3").show();
					$(".noQualityIssue3").hide();
				} else {
					$(".productLiability3").hide();
					$(".productLiability3").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualityIssue3").change(function () {
				var noQualityIssue3 = $(".noQualityIssue3 option:selected").val();
				if (noQualityIssue3 == "1") {
					$(".storeDuty3").show();
					$(".userDuty3").hide();
				} else if (noQualityIssue3 == "2") {
					$(".userDuty3").show();
					$(".storeDuty3").hide();
				} else {
					$(".storeDuty3").hide();
					$(".userDuty3").hide();
				}
			});
			/*属地品质售后判责三：结束*/


			/*质量总监售后判责：开始*/
			/*责任判定一*/
			if ($(".qualitySumJudgment").val() == "1") {
				$(".qualitySumIssue").show();
				if ($(".qualitySumIssue select").val() == "2") {
					$(".qualitySumProductLiability").show();
				}
			} else if ($(".qualitySumJudgment").val() == "0") {
				$(".noQualitySumIssue").show();
				if ($(".noQualitySumIssue select").val() == "1") {
					$(".qualitySumStoreDuty").show();
				} else {
					$(".qualitySumUserDuty").show();
				}
			}

			/*责任判定二*/
			if ($(".qualitySumJudgment2").val() == "1") {
				$(".qualitySumIssue2").show();
				if ($(".qualitySumIssue2 select").val() == "2") {
					$(".qualitySumProductLiability2").show();
				}
			} else if ($(".qualitySumJudgment2").val() == "0") {
				$(".noQualitySumIssue2").show();
				if ($(".noQualitySumIssue2 select").val() == "1") {
					$(".qualitySumStoreDuty2").show();
				} else {
					$(".qualitySumUserDuty2").show();
				}
			}

			/*责任判定三*/
			if ($(".qualitySumJudgment3").val() == "1") {
				$(".qualitySumIssue3").show();
				if ($(".qualitySumIssue3 select").val() == "2") {
					$(".qualitySumProductLiability3").show();
				}
			} else if ($(".qualitySumJudgment3").val() == "0") {
				$(".noQualitySumIssue3").show();
				if ($(".noQualitySumIssue3 select").val() == "1") {
					$(".qualitySumStoreDuty3").show();
				} else {
					$(".qualitySumUserDuty3").show();
				}
			}
			/*质量总监售后判责：结束*/


			/*质量总监售后判责一：开始*/
			/*质量或非质量问题*/
			$(".qualitySumJudgment").change(function () {
				var qualitySumJudgment = $(".qualitySumJudgment option:selected").val();
				if (qualitySumJudgment == "1") {
					$(".qualitySumIssue").show();
					$(".noQualitySumIssue").hide();
					$(".qualitySumProductLiability").hide();
					$(".qualitySumStoreDuty").hide();
					$(".qualitySumUserDuty").hide();
				} else if (qualitySumJudgment == "0") {
					$(".qualitySumIssue").hide();
					$(".noQualitySumIssue").show();
					$(".qualitySumProductLiability").hide();
					$(".qualitySumStoreDuty").hide();
					$(".qualitySumUserDuty").hide();
				} else {
					$(".qualitySumIssue").hide();
					$(".noQualitySumIssue").hide();

				}
			});
			/*质量问题二级*/
			$(".qualitySumIssue").change(function () {
				var qualitySumIssue = $(".qualitySumIssue option:selected").val();
				if (qualitySumIssue == "2") {
					$(".qualitySumProductLiability").show();
					$(".noQualitySumIssue").hide();
				} else {
					$(".qualitySumProductLiability").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualitySumIssue").change(function () {
				var noQualitySumIssue = $(".noQualitySumIssue option:selected").val();
				if (noQualitySumIssue == "1") {
					$(".qualitySumStoreDuty").show();
					$(".qualitySumUserDuty").hide();
				} else if (noQualitySumIssue == "2") {
					$(".qualitySumUserDuty").show();
					$(".qualitySumStoreDuty").hide();
				} else {
					$(".qualitySumStoreDuty").hide();
					$(".qualitySumUserDuty").hide();
				}
			});
			/*质量总监售后判责一：结束*/

			/*质量总监售后判责二：开始*/
			/*质量或非质量问题*/
			$(".qualitySumJudgment2").change(function () {
				var qualitySumJudgment2 = $(".qualitySumJudgment2 option:selected").val();
				if (qualitySumJudgment2 == "1") {
					$(".qualitySumIssue2").show();
					$(".noQualitySumIssue2").hide();
					$(".qualitySumProductLiability2").hide();
					$(".qualitySumStoreDuty2").hide();
					$(".qualitySumUserDuty2").hide();
				} else if (qualitySumJudgment2 == "0") {
					$(".qualitySumIssue2").hide();
					$(".noQualitySumIssue2").show();
					$(".qualitySumProductLiability2").hide();
					$(".qualitySumStoreDuty2").hide();
					$(".qualitySumUserDuty2").hide();
				} else {
					$(".qualitySumIssue2").hide();
					$(".noQualitySumIssue2").hide();

				}
			});
			/*质量问题二级*/
			$(".qualitySumIssue2").change(function () {
				var qualitySumIssue2 = $(".qualitySumIssue2 option:selected").val();
				if (qualitySumIssue2 == "2") {
					$(".qualitySumProductLiability2").show();
					$(".noQualitySumIssue2").hide();
				} else {
					$(".qualitySumProductLiability2").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualitySumIssue2").change(function () {
				var noQualitySumIssue2 = $(".noQualitySumIssue2 option:selected").val();
				if (noQualitySumIssue2 == "1") {
					$(".qualitySumStoreDuty2").show();
					$(".qualitySumUserDuty2").hide();
				} else if (noQualitySumIssue2 == "2") {
					$(".qualitySumUserDuty2").show();
					$(".qualitySumStoreDuty2").hide();
				} else {
					$(".qualitySumStoreDuty2").hide();
					$(".qualitySumUserDuty2").hide();
				}
			});
			/*质量总监售后判责二：结束*/

			/*质量总监售后判责三：开始*/
			/*质量或非质量问题*/
			$(".qualitySumJudgment3").change(function () {
				var qualitySumJudgment3 = $(".qualitySumJudgment3 option:selected").val();
				if (qualitySumJudgment3 == "1") {
					$(".qualitySumIssue3").show();
					$(".noQualitySumIssue3").hide();
					$(".qualitySumProductLiability3").hide();
					$(".qualitySumStoreDuty3").hide();
					$(".qualitySumUserDuty3").hide();
				} else if (qualitySumJudgment3 == "0") {
					$(".qualitySumIssue3").hide();
					$(".noQualitySumIssue3").show();
					$(".qualitySumProductLiability3").hide();
					$(".qualitySumStoreDuty3").hide();
					$(".qualitySumUserDuty3").hide();
				} else {
					$(".qualitySumIssue3").hide();
					$(".noQualitySumIssue3").hide();

				}
			});
			/*质量问题二级*/
			$(".qualitySumIssue3").change(function () {
				var qualitySumIssue3 = $(".qualitySumIssue3 option:selected").val();
				if (qualitySumIssue23 == "2") {
					$(".qualitySumProductLiability3").show();
					$(".noQualitySumIssue3").hide();
				} else {
					$(".qualitySumProductLiability3").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualitySumIssue3").change(function () {
				var noQualitySumIssue3 = $(".noQualitySumIssue3 option:selected").val();
				if (noQualitySumIssue3 == "1") {
					$(".qualitySumStoreDuty3").show();
					$(".qualitySumUserDuty3").hide();
				} else if (noQualitySumIssue3 == "2") {
					$(".qualitySumUserDuty3").show();
					$(".qualitySumStoreDuty3").hide();
				} else {
					$(".qualitySumStoreDuty3").hide();
					$(".qualitySumUserDuty3").hide();
				}
			});
			/*质量总监售后判责三：结束*/

			// 表单验证
			$inputForm.validate({
				rules: {
					grade: "required",
					area: "required",
					name: "required",
					contactNumber: "required",
					isCheckIn: "required",
//			checkInTime: "required",
					downTime: "required",
					layingTime: "required",
					layingArea: "required",
					layingName: "required",
//			layingMethod: "required",
					layingMoistureproof: "required",
					heatingSystem: "required",
//			otherInformation: "required",
					decorationTime: "required",
//			emergencyDegree: "required",
//			floorFlatness: "required",
//			floorMoistureContent: "required",
//			groundMoistureContent: "required",
					batchNumber: "required",
					price: "required",
//			totalSales: "required",
//			airHumidity: "required",
//			processingStatus: "required",
					faultName: "required",
					zbsurveyorName: "required",
					zbsurveyorPhone: "required",
					storeFaultMessage: "required",
					layingWay: "required",
					layingAddress: "required",
					[#if aftersale.wfId != null]
					formType: "required",
					[/#if]
					businessTypeId: "required"

				},
				submitHandler: function (form) {
					return false;
				}
			});

			//地区选择
			$areaId.lSelect();
			$bAreaId.lSelect();
			$newAreaId.lSelect();
			action();

//	//现场照片附件
//	var aftersale_attachs = ${aftersale_attach};
//	var aftersaleAttachIndex=0;
//	var cols = [
//		{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex, obj){
//				if(obj==undefined){
//					var url = item.url;
//					var fileObj = getfileObj(item.file_name,item.name,item.suffix);
//					/**设置隐藏值*/
//					var hideValues = {};
//					hideValues['aftersaleAttachs['+aftersaleAttachIndex+'].id']=item.id;
//					hideValues['aftersaleAttachs['+aftersaleAttachIndex+'].url']=url;
//					hideValues['aftersaleAttachs['+aftersaleAttachIndex+'].suffix']=fileObj.suffix;
//
//					return createFileStr({
//						url : url,
//						fileName : fileObj.file_name,
//						name : fileObj.name,
//						suffix : fileObj.suffix,
//						time : item.create_date,
//						textName:'aftersaleAttachs['+aftersaleAttachIndex+'].name',
//						hideValues:hideValues
//					});
//				}else{
//					var url = item.url;
//					var fileObj = getfileObj(item.name);
//					/**设置隐藏值*/
//					var hideValues = {};
//					hideValues['aftersaleAttachs['+aftersaleAttachIndex+'].url']=url;
//					hideValues['aftersaleAttachs['+aftersaleAttachIndex+'].suffix']=fileObj.suffix;
//
//					return createFileStr({
//						url : url,
//						fileName : fileObj.file_name,
//						name : fileObj.name,
//						suffix : fileObj.suffix,
//						time : '',
//						textName:'aftersaleAttachs['+aftersaleAttachIndex+'].name',
//						hideValues:hideValues
//					});
//				}
//			}},
//		{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex, obj){
//				return '<div><input type="text" class="text file_memo" name="aftersaleAttachs['+aftersaleAttachIndex+'].memo" value="'+val+'" style="width:50%"></div>';
//			}},
//		{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex, obj){
//				aftersaleAttachIndex++;
//				return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
//			}}
//	];
//	var $aftersaleAttachmGrid=$('#table-attach').mmGrid({
//		fullWidthRows:true,
//		height:'auto',
//		cols: cols,
//		items:aftersale_attachs,
//		checkCol: false,
//		autoLoad: true
//	});
//	var $addAttach = $("#addAttach");
//	var attachIdnex = 0;
//	var option1 = {
//		dataType: "json",
//		uploadToFileServer:true,
//		uploadSize: "fileurl",
//		callback : function(data){
//			var date = new Date();
//			var year = date.getFullYear();
//			var month = date.getMonth()+1;
//			var day = date.getDate();
//			var time = year+'-'+month+'-'+day;
//			for(var i=0;i<data.length;i++){
//				var row = data[i].file_info;
//				$aftersaleAttachmGrid.addRow(row,null,1);
//			}
//		}
//	}
//	$addAttach.file_upload(option1);
			//销售合同
			var sales_contract_attachs = ${sales_contract_attach};
			var salesContractAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}

							/**设置隐藏值*/
							var hideValues = {};
							hideValues['salesContractAttachs[' + salesContractAttachIndex + '].id'] = item.id;
							hideValues['salesContractAttachs[' + salesContractAttachIndex + '].url'] = url;
							hideValues['salesContractAttachs[' + salesContractAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'salesContractAttachs[' + salesContractAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['salesContractAttachs[' + salesContractAttachIndex + '].url'] = url;
							hideValues['salesContractAttachs[' + salesContractAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'salesContractAttachs[' + salesContractAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="salesContractAttachs[' + salesContractAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						salesContractAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $salesContractAttachmGrid = $('#table-salesContractAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: sales_contract_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addSalesContractAttach = $("#addSalesContractAttach");
			var attachIdnex = 0;
			var option1 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$salesContractAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addSalesContractAttach.file_upload(option1);

			//验收单
			var acceptance_sheet_attachs = ${acceptance_sheet_attach};
			var acceptanceSheetAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}

							/**设置隐藏值*/
							var hideValues = {};
							hideValues['acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].id'] = item.id;
							hideValues['acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].url'] = url;
							hideValues['acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].url'] = url;
							hideValues['acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						acceptanceSheetAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $acceptanceSheetAttachmGrid = $('#table-acceptanceSheetAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: acceptance_sheet_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addAcceptanceSheetAttach = $("#addAcceptanceSheetAttach");
			var attachIdnex = 0;
			var option7 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$acceptanceSheetAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addAcceptanceSheetAttach.file_upload(option7);

			//现场照片
			var scene_pictures_attachs = ${scene_pictures_attach};
			var scenePicturesAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}

							/**设置隐藏值*/
							var hideValues = {};
							hideValues['scenePicturesAttachs[' + scenePicturesAttachIndex + '].id'] = item.id;
							hideValues['scenePicturesAttachs[' + scenePicturesAttachIndex + '].url'] = url;
							hideValues['scenePicturesAttachs[' + scenePicturesAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'scenePicturesAttachs[' + scenePicturesAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['scenePicturesAttachs[' + scenePicturesAttachIndex + '].url'] = url;
							hideValues['scenePicturesAttachs[' + scenePicturesAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'scenePicturesAttachs[' + scenePicturesAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="scenePicturesAttachs[' + scenePicturesAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						scenePicturesAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $scenePicturesAttachmGrid = $('#table-scenePicturesAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: scene_pictures_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addScenePicturesAttach = $("#addScenePicturesAttach");
			var attachIdnex = 0;
			var option8 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$scenePicturesAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addScenePicturesAttach.file_upload(option8);

			//勘察图片附件
			var surveyor_attachs = ${surveyor_attach};
			var surveyorAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					name: 'content',
					width: 400,
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['surveyorAttachs[' + surveyorAttachIndex + '].id'] = item.id;
							hideValues['surveyorAttachs[' + surveyorAttachIndex + '].url'] = url;
							hideValues['surveyorAttachs[' + surveyorAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'surveyorAttachs[' + surveyorAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['surveyorAttachs[' + surveyorAttachIndex + '].url'] = url;
							hideValues['surveyorAttachs[' + surveyorAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'surveyorAttachs[' + surveyorAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="surveyorAttachs[' + surveyorAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						surveyorAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $surveyorAttachmGrid = $('#table-surveyorAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: surveyor_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addSurveyorAttach = $("#addSurveyorAttach");
			var attachIdnex = 0;
			var option2 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$surveyorAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addSurveyorAttach.file_upload(option2);

			//售后处理反馈附件
			var coupleBack_attachs = ${coupleBack_attach};
			var coupleBackAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['coupleBackAttachs[' + coupleBackAttachIndex + '].id'] = item.id;
							hideValues['coupleBackAttachs[' + coupleBackAttachIndex + '].url'] = url;
							hideValues['coupleBackAttachs[' + coupleBackAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'coupleBackAttachs[' + coupleBackAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['coupleBackAttachs[' + coupleBackAttachIndex + '].url'] = url;
							hideValues['coupleBackAttachs[' + coupleBackAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'coupleBackAttachs[' + coupleBackAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="coupleBackAttachs[' + coupleBackAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						coupleBackAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $coupleBackAttachmGrid = $('#table-coupleBackAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: coupleBack_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addCoupleBackAttach = $("#addCoupleBackAttach");
			var attachIdnex = 0;
			var option3 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$coupleBackAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addCoupleBackAttach.file_upload(option3);

			//售后协议书附件
			var agreement_attachs = ${agreement_attach};
			var agreementAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['agreementAttachs[' + agreementAttachIndex + '].id'] = item.id;
							hideValues['agreementAttachs[' + agreementAttachIndex + '].url'] = url;
							hideValues['agreementAttachs[' + agreementAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'agreementAttachs[' + agreementAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['agreementAttachs[' + agreementAttachIndex + '].url'] = url;
							hideValues['agreementAttachs[' + agreementAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'agreementAttachs[' + agreementAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="agreementAttachs[' + agreementAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						agreementAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $agreementAttachmGrid = $('#table-agreementAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: agreement_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addAgreementAttach = $("#addAgreementAttach");
			var attachIdnex = 0;
			var option4 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$agreementAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addAgreementAttach.file_upload(option4);

			//收据附件
			var quittance_attachs =
					${quittance_attach}
			var quittanceAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['quittanceAttachs[' + quittanceAttachIndex + '].id'] = item.id;
							hideValues['quittanceAttachs[' + quittanceAttachIndex + '].url'] = url;
							hideValues['quittanceAttachs[' + quittanceAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'quittanceAttachs[' + quittanceAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['quittanceAttachs[' + quittanceAttachIndex + '].url'] = url;
							hideValues['quittanceAttachs[' + quittanceAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'quittanceAttachs[' + quittanceAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="quittanceAttachs[' + quittanceAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						quittanceAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $quittanceAttachmGrid = $('#table-quittanceAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: quittance_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addQuittanceAttach = $("#addQuittanceAttach");
			var attachIdnex = 0;
			var option5 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$quittanceAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addQuittanceAttach.file_upload(option5);

			//退货申请单附件
			var returns_attachs = ${returns_attach};
			var returnsAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['returnsAttachs[' + returnsAttachIndex + '].id'] = item.id;
							hideValues['returnsAttachs[' + returnsAttachIndex + '].url'] = url;
							hideValues['returnsAttachs[' + returnsAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'returnsAttachs[' + returnsAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['returnsAttachs[' + returnsAttachIndex + '].url'] = url;
							hideValues['returnsAttachs[' + returnsAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'returnsAttachs[' + returnsAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="returnsAttachs[' + returnsAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						returnsAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $returnsAttachmGrid = $('#table-returnsAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: returns_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addReturnsAttach = $("#addReturnsAttach");
			var attachIdnex = 0;
			var option6 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$returnsAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addReturnsAttach.file_upload(option6);

			//退货确认 物流单附件
			var returns_logistics_attach = ${returns_logistics_attach};
			var returnsLogisticsAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['returnsLogisticsAttachs[' + returnsLogisticsAttachIndex + '].id'] = item.id;
							hideValues['returnsLogisticsAttachs[' + returnsLogisticsAttachIndex + '].url'] = url;
							hideValues['returnsLogisticsAttachs[' + returnsLogisticsAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'returnsLogisticsAttachs[' + returnsLogisticsAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['returnsLogisticsAttachs[' + returnsLogisticsAttachIndex + '].url'] = url;
							hideValues['returnsLogisticsAttachs[' + returnsLogisticsAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'returnsLogisticsAttachs[' + returnsLogisticsAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="returnsLogisticsAttachs[' + returnsLogisticsAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						returnsAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $returnsLogisticsAttachmGrid = $('#table-returnsLogisticsAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: returns_logistics_attach,
				checkCol: false,
				autoLoad: true
			});
			var $addReturnsLogisticsAttach = $("#addReturnsLogisticsAttach");
			var attachIdnex = 0;
			var option9 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$returnsLogisticsAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addReturnsLogisticsAttach.file_upload(option9);

			//营销政策附件
			var collection_attachs = ${collection_attach};
			var collectionAttachIndex = 0;
			var cols = [
				{
					title: '${message("附件")}',
					width: 400,
					name: 'content',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						if (obj == undefined) {
							var url = item.url;
							var fileObj = getfileObj(item.name, item.name, item.suffix);
							if (fileObj.suffix == 'null' || fileObj.suffix == '') {
								let url_index = url.lastIndexOf('.');
								if (url_index >= 0) {
									fileObj.suffix = url.substring(url_index + 1, url.length);
								}
							}
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['collectionAttachs[' + collectionAttachIndex + '].id'] = item.id;
							hideValues['collectionAttachs[' + collectionAttachIndex + '].url'] = url;
							hideValues['collectionAttachs[' + collectionAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'collectionAttachs[' + collectionAttachIndex + '].name',
								hideValues: hideValues
							});
						} else {
							var url = item.url;
							var fileObj = getfileObj(item.name);
							/**设置隐藏值*/
							var hideValues = {};
							hideValues['collectionAttachs[' + collectionAttachIndex + '].url'] = url;
							hideValues['collectionAttachs[' + collectionAttachIndex + '].suffix'] = fileObj.suffix;

							return createFileStr({
								url: url,
								fileName: fileObj.file_name,
								name: fileObj.name,
								suffix: fileObj.suffix,
								time: '',
								textName: 'collectionAttachs[' + collectionAttachIndex + '].name',
								hideValues: hideValues
							});
						}
					}
				},
				{
					title: '${message("备注")}',
					width: 500,
					name: 'memo',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<div><textarea class="text file_memo" name="collectionAttachs[' + collectionAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
					}
				},
				{
					title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
						collectionAttachIndex++;
						return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
					}
				}
			];
			var $collectionAttachmGrid = $('#table-collectionAttach').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: collection_attachs,
				checkCol: false,
				autoLoad: true
			});
			var $addCollectionAttach = $("#addCollectionAttach");
			var option10 = {
				dataType: "json",
				uploadToFileServer: true,
				uploadSize: "fileurl",
				callback: function (data) {
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day;
					for (var i = 0; i < data.length; i++) {
						var row = data[i].file_info;
						$collectionAttachmGrid.addRow(row, null, 1);
					}
				}
			}
			$addCollectionAttach.file_upload(option10);


			[#if storeMember.memberType !=1 ]
			//历史售后单
			var history = ${history};
			var cols = [
				{
					title: '${message("售后申请单号")}',
					name: 'sn',
					align: 'center',
					renderer: function (val, item, rowIndex, obj) {
						return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/aftersales/aftersale/edit.jhtml?id=' + item.id + '\')" class="red">' + val + '</a>';
					}
				},
				{title: '${message("顾客姓名")}', name: 'name', align: 'center'},
				{title: '${message("联系电话")}', name: 'contact_number', align: 'center'},
				{title: '${message("铺装地址")}', name: 'address', align: 'center'},
				{title: '${message("创建日期")}', align: 'center', name: 'create_date'}
			];
			var $historyAftersalemmGrid = $('#table-historyAftersale').mmGrid({
				fullWidthRows: true,
				width: '1147',
				height: 'auto',
				cols: cols,
				items: history,
				checkCol: false,
				autoLoad: true
			});
			[/#if]

			var $deleteAttachment = $(".deleteAttachment");
			$deleteAttachment.live("click", function () {
				var $this = $(this);
				$this.closest("tr").remove();
			});


			$addSaleOrgs.click(function () {
				$addSaleOrgs.bindQueryBtn({
					type: 'saleOrg',
					bindClick: false,
					title: '${message("查询工厂")}',
					url: '/aftersales/aftersale/select_factory.jhtml',
					callback: function (rows) {
						if (rows.length > 0) {
							var id = rows[0].id;
							var name = rows[0].factory_name;
							$(".factoryId").val(id);
							$(".factoryName").val(name);
						}
					}
				});
			});

			$addOrganization.click(function () {
				$addOrganization.bindQueryBtn({
					type: 'saleOrg',
					bindClick: false,
					title: '${message("查询经营组织")}',
					url: '/aftersales/aftersale/select_organization.jhtml',
					callback: function (rows) {
						if (rows.length > 0) {
							var id = rows[0].id;
							var name = rows[0].name;
							$(".organizationId").val(id);
							$(".organizationName").val(name);
						}
					}
				});
			});

			$addFinancialStaff.click(function () {
				$addFinancialStaff.bindQueryBtn({
					type: 'saleOrg',
					bindClick: false,
					title: '${message("查询工厂财务")}',
					url: '/aftersales/aftersale/select_financial_staff.jhtml?id=${aftersale.id}',
					callback: function (rows) {
						if (rows.length > 0) {
							var id = rows[0].id;
							var name = rows[0].name;
							$(".financialStaffId").val(id);
							$(".financialStaffName").val(name);
						}
					}
				});
			});

			$addProducts.click(function () {
				$addProducts.bindQueryBtn({
					type: 'product',
					bindClick: false,
					title: '${message("查询产品")}',
					url: '/product/product/selectProduct.jhtml?srcPage=1&isMarketable=true',
					callback: function (rows) {
						if (rows.length > 0) {
							var id = rows[0].id;
							var name = rows[0].name;
							var model = rows[0].model;
							var spec = rows[0].spec;
							var productCategory = "";
							var productDescription = rows[0].description;
							var productVonderCode = rows[0].vonder_code;
							if (rows[0].pc_parent != null) {
								$.ajax({
									url: '/product/product/find_category.jhtml?parent=' + rows[0].pc_parent,
									type: "post",
									success: function (data) {
										data.type == "success";
										if (data != null) {
											$(".category").val(data.content);
										}
									}
								});

								$.ajax({
									url: '/aftersales/aftersale/select_sbu_by_product_id.jhtml?productId=' + rows[0].id,
									type: "post",
									success: function (data) {
										if (data.type == "success") {
											if (data != null) {
												var sbu = data.objx;
												if (sbu.length > 0) {
													var value = sbu[0]
													var sbuName = value["name"];
													var sbuId = value["id"];
													$(".sbuName").val(sbuName);
													$(".sbuId").val(sbuId);
												} else {
													alert("该产品没有sbu，请重新选择产品");
												}
											}
										}
									}
								});
							}
							$(".productId").val(id);
							$(".productName").text(name);
							$(".productModel").val(model);
							$(".productSpec").text(spec);
							$(".productDescription").text(productDescription);
							$(".productVonderCode").text(productVonderCode);
							//$(".").val();
						}
					}
				});
			});

			$("form").bindAttribute({
				isConfirm: true,
				callback: function (resultMsg) {
					$.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
						location.reload(true);
					})
				}
			});

			$("#compensation").blur(function () {
				var $this = $(this);
				var value = $this.val();
				var a = $this.parent().find(".check").val(value);
			});

			var inputList = $(".storeAppeal").find("input");
			var storeAppeal = "${aftersale.storeAppeal!0}";
			check(inputList, storeAppeal);

			var fdList = $(".fd").find("input");
			var fd = "${aftersale.fd!0}";
			check(fdList, fd);

			var ssList = $(".storeTreatmentScheme").find("input");
			var ss = "${aftersale.storeTreatmentScheme!0}";
			check(ssList, ss);

			var cdList = $(".cd").find("input");
			var cd = "${aftersale.cd!0}";
			check(cdList, cd);

			function check(input, value) {
				var newArrList = new Array();
				newArrList = value.split(",");
				for (var i = 0; i < input.length; i++) {
					var name = input.eq(i).val();
					if (explore(name, newArrList)) {
						input.eq(i).prop("checked", true);
					}
				}
			}

			function explore(str, newArrList) {
				if (newArrList.length > 0) {
					for (var i = 0; i < newArrList.length; i++) {
						if (newArrList[i] == str) {
							return true;
						}
					}
				}
				return false;
			}

			function initTable(content, attributeName, table, addButton) {//后台的json;主表的变量名;div表格id;添加按钮
				var cn = content;
				var Index = 0;
				var cols = [
					{
						title: '${message("附件")}',
						name: 'content',
						align: 'center',
						renderer: function (val, item, rowIndex, obj) {
							if (obj == undefined) {
								var url = item.url;
								var fileObj = getfileObj(item.file_name, item.name, item.suffix);
								/**设置隐藏值*/
								var hideValues = {};
								hideValues['attributeName[' + Index + '].id'] = item.id;
								hideValues['attributeName[' + Index + '].url'] = url;
								hideValues['attributeName[' + Index + '].suffix'] = fileObj.suffix;

								return createFileStr({
									url: url,
									fileName: fileObj.file_name,
									name: fileObj.name,
									suffix: fileObj.suffix,
									time: item.create_date,
									textName: 'attributeName[' + Index + '].name',
									hideValues: hideValues
								});
							} else {
								var url = item.url;
								var fileObj = getfileObj(item.name);
								/**设置隐藏值*/
								var hideValues = {};
								hideValues['attributeName[' + Index + '].url'] = url;
								hideValues['attributeName[' + Index + '].suffix'] = fileObj.suffix;

								return createFileStr({
									url: url,
									fileName: fileObj.file_name,
									name: fileObj.name,
									suffix: fileObj.suffix,
									time: '',
									textName: 'attributeName[' + Index + '].name',
									hideValues: hideValues
								});
							}
						}
					},
					{
						title: '${message("备注")}',
						name: 'memo',
						align: 'center',
						renderer: function (val, item, rowIndex, obj) {
							return '<div><input type="text" class="text file_memo" name="attributeName[' + Index + '].memo" value="' + val + '" style="width:50%"></div>';
						}
					},
					{
						title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
							Index++;
							return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
						}
					}
				];
				var $mGrid = table.mmGrid({
					fullWidthRows: true,
					height: 'auto',
					cols: cols,
					items: cn,
					checkCol: false,
					autoLoad: true
				});
				var attachIdnex = 0;
				var option = {
					dataType: "json",
					uploadToFileServer: true,
					uploadSize: "fileurl",
					callback: function (data) {
						var date = new Date();
						var year = date.getFullYear();
						var month = date.getMonth() + 1;
						var day = date.getDate();
						var time = year + '-' + month + '-' + day;
						for (var i = 0; i < data.length; i++) {
							var row = data[i].file_info;
							$mGrid.addRow(row, null, 1);
						}
					}
				}
				addButton.file_upload(option);
			}

			var c = $('.totalSales');
			$('.increase').click(function () {
				var a = $("input[name='layingArea']").val();
				var b = $("input[name='price']").val();
				var d = accMul(a, b);
				c.html(currency(d, true));
			});
			$('#price').bind('input propertychange', function () {
				var a = $("input[name='layingArea']").val();
				var b = $("input[name='price']").val();
				var d = accMul(a, b);
				c.html(currency(d, true));
			});
			$('#layingArea').bind('input propertychange', function () {
				var a = $("input[name='layingArea']").val();
				var b = $("input[name='price']").val();
				var d = accMul(a, b);
				c.html(currency(d, true));
			});
			$('.decrease').click(function () {
				var a = $("input[name='layingArea']").val();
				var b = $("input[name='price']").val();
				var d = accMul(a, b);
				c.html(currency(d, true));
			});

			$('#businessTypeId').change(function () {
				var businessType = $("#businessTypeId option:selected").attr("lowerCode");
				console.log(businessType)
				if (businessType == "businessSubconditional") {
					$("#business").show();
				} else {
					$("#business").hide();
				}
			})

			var isReturnLabel;
			$("input[name='isReturn']").click(function () {
				$(this).each(function () {
					var isReturn = $(this).val();

					if (isReturn == '0') {//是
						CheckBoxDisabled('全部更换', '局部更换', '');
						// $('input[name="factoryCompensation"]').val($('input[name="layingArea"]').val());
						//if (isReturn != isReturnLabel)
						//$('input[name="factoryCompensation"]').val(0);
					} else if (isReturn == '1') {//否
						CheckBoxDisabled('', '', '退货');
						// $('input[name="factoryCompensation"]').val($('input[name="layingArea"]').val());
						//if (isReturn != isReturnLabel)
						//$('input[name="factoryCompensation"]').val(0);
					}
					isReturnLabel = isReturn;
				});
			});

			function CheckBoxDisabled(q, j, t) {
				$('.fd').find("input[name='fd']").each(function () {
					$(this).removeAttr("disabled");
				});
				$('.fd').find("input[name='fd']").each(function () {
					var name = $(this).val();
					if (q == name) {
						$(this).attr("disabled", "disabled");
						$(this).attr("checked", false);
					}
					if (j == name) {
						$(this).attr("disabled", "disabled");
						$(this).attr("checked", false);
						$(this).parent().find("input[name='fdPartialReplacement']").val("");
						$(this).parent().find("input[name='fdPartialReplacement']").attr("readonly", true)
					}
					if (t == name) {
						$(this).attr("disabled", "disabled");
						$(this).attr("checked", false);
						$(this).parent().find("input[name='fdReturnGoods']").val("");
						$(this).parent().find("input[name='fdReturnGoods']").attr("readonly", true)
					}
				});
			}

			$('.fd').find("input[name='fd']").click(function () {
				var name = $(this).val();
				if (name == '局部更换') {
					$('.fd').find("input[name='fd']").each(function () {
						if ($(this).val() == '全部更换') {
							$(this).attr("checked", false);
						}
					});
					var acr = 0;
					if ($(this).prop("checked") == true) {
						acr = $("input[name='fdPartialReplacement']").val();
					}
					$('.acreage').val(acr);
				} else if (name == '全部更换') {
					var isChecked = $(this).attr("checked");
					$('.fd').find("input[name='fd']").each(function () {
						if ($(this).val() == '局部更换') {
							$(this).attr("checked", false);
						}
					});
					var acr = 0;
					if ($(this).attr("checked") == 'checked') {
						acr = $("input[name='layingArea']").val();
					}
					$('.acreage').val(acr);
				}
			});

			$('.cd').find("input[name='cd']").click(function () {
				var name = $(this).val();
				$('.cd').find("input[name='cd']").each(function () {
					if ($(this).val() != name) {
						$(this).attr("checked", false);
						var $pdiv = $(this).next();
						if ($pdiv.hasClass("acr")) {
							$pdiv.attr("readonly", "readonly");
							$pdiv.attr("required", false)
							$pdiv.val("");
						}
					}
				});
				if (name == '全部更换') {
					var acr = $("input[name='layingArea']").val();
					$('.acreage').val(acr);
				} else {
					$('.acreage').val(0);
				}
			});


			// returnOrNot();
			$("#return").change(returnOrNot);


			$('.cd-cost').change(function () {
				var name = "";
				$('.cd').find("input[name='cd']").each(function () {
					if ($(this).attr("checked") == true) {
						name = $(this).val();
						return false;
					}
				});

				var factoryPrice = $("input[name='factoryPrice']").val() * 1;
				if (factoryPrice == undefined || factoryPrice == null || factoryPrice == '') {
					factoryPrice = 0;
				}
				var subsidiaryMaterialCost = $('#subsidiaryMaterialCost').val() * 1;
				if (subsidiaryMaterialCost == undefined || subsidiaryMaterialCost == null || subsidiaryMaterialCost == '') {
					subsidiaryMaterialCost = 0;
				}
				var labourCost = $('#labourCost').val() * 1;
				if (labourCost == undefined || labourCost == null || labourCost == '') {
					labourCost = 0;
				}
				var freight = $('#freight').val() * 1;
				if (freight == undefined || freight == null || freight == '') {
					freight = 0;
				}
				var acr = $(".acreage").val() * 1;
				if (acr == undefined || acr == null || acr == '') {
					acr = 0;
				}
				var comCompensation = $("#comCompensation").val() * 1;
				if (comCompensation == undefined || comCompensation == null || comCompensation == '') {
					comCompensation = 0;
				}
				var otherCost = $("#otherCost").val() * 1;
				if (otherCost == undefined || otherCost == null || otherCost == '') {
					otherCost = 0;
				}
				var amount = subsidiaryMaterialCost + labourCost + freight;
				if (name == "全部更换" || name == "局部更换") {
					amount = acr * factoryPrice + amount + comCompensation + otherCost;
				} else if (name == "退货") {
					amount = acr * factoryPrice + comCompensation + amount;
				} else if (name == "赔偿") {
					amount = comCompensation;
				} else {
					amount = 0;
				}
				$("#amount").val(amount);

			});

			// 工厂是否处理控制显隐
			[#if aftersale.isDeal == 0]
			$('.factoryDeal').hide()
			[/#if]
			[#if aftersale.isDeal == 0]
			$('.factoryDeal2').show()
			[/#if]
			$("input[name='isDeal']").click(function () {
				$(this).each(function () {
					var isDeal = $(this).val();
					if (isDeal == '0') {//否
						$('.factoryDeal').hide();
						$('.factoryDeal2').show();
					} else if (isDeal == '1') {//是
						$('.factoryDeal').show();
						$('.factoryDeal2').hide();
					}
				});
			});

			$('.acr').blur(function () {
				$('.acreage').val($(this).val());
			});
			//查5期安装单号
			$("#openAzdh").bindQueryBtn({
				type: 'azdh',
				title: '${message("查询安装单")}',
				url: '/aftersales/aftersale/select_azd.jhtml?storeId=' +  $('.storeId').val(),
				callback: function (rows) {
					if (rows.length > 0) {
						var row = rows[0];
						console.log(row)
						$('.fiveGdId').val(row.id);
						$('.fiveGdSn').val(row.sn);
					}
				}
			});
			$('#checkAzd').click(function () {
				var fiveGdId = $('.fiveGdId').val()
				if(fiveGdId != null){
					var url = "/aftersales/aftersale/azd.jhtml?id=" + fiveGdId
					parent.change_tab(2, url);
				} else{
					$.message_alert('请选择安装单！');
				}
			});
			//查客户
			$("#openStore").bindQueryBtn({
				type: 'store',
				title: '${message("查询客户")}',
				url: '/member/store/select_store.jhtml',
				callback: function (rows) {
					if (rows.length > 0) {
						var row = rows[0];
						$("input[name='storeId']").val(row.id);
						$('.dealerName').val(row.dealer_name);
						$('.sotreName').text(row.name);
						$('.saleOrgId').val(row.sale_org);
						$('.grantCode').text(row.grant_code);
						$('.outTradeNo').text(row.out_trade_no);
					}
				}
			});

			//查询合同
			$("#selectContract").click(function () {
				//var sbuId = $(".sbuId").val();
				var storeId = $(".storeId").val();
				console.log(storeId)
				if (storeId == "") {
					$.message_alert('请选择客户');
					return false;
				}
				$("#openStore").bindQueryBtn({
					type: 'store',
					bindClick: false,
					title: '${message("查询合同")}',
					url: '/b2b/customerContract/select_contract.jhtml?storeId=' + storeId + '&aft=1',
					callback: function (rows) {
						if (rows.length > 0) {
							var row = rows[0];
							$("input[name='customerContractId']").attr("value", row.id);
							$("input[name='contractSn']").attr("value", row.sn);
							$("input[name='contractName']").attr("value", row.contract_name);
							$("input[name='storeMemberId']").attr("value", row.store_member_id);
							$("input[name='storeMemberName']").attr("value", row.store_member_name);
							$("input[name='contacts']").attr("value", row.contacts);
						}
					}
				});
			})

			//查询订单部经理
			$("#openOrderManager").bindQueryBtn({
				type: 'orderManager',
				title: '${message("查询订单部经理")}',
				url: '/aftersales/aftersale/select_order_manager.jhtml?postcode=1022&postcode=1009&postcode=1023&postcode=3002&postcode=2006&postcode=1024'
			});

			//查询产品总
			$("#openProductionSumUp").bindQueryBtn({
				type: 'productionSumUp',
				title: '${message("查询产品总")}',
				url: '/aftersales/aftersale/select_order_manager.jhtml?postcode=1206'
			});


			//查询勘察人
			$("#openSurveyor").bindQueryBtn({
				type: 'surveyor',
				title: '${message("查询勘察人")}',
				url: '/aftersales/aftersale/select_order_manager.jhtml?postcode=2088&postcode=1006&postcode=2093&postcode=1025&postcode=4001',
			});

			//查询检测单
			$("#openSampleTestApply").bindQueryBtn({
				type: 'sampleTestApply',
				title: '${message("查询样板检测单")}',
				url: '/aftersales/aftersale/select_sample_test_apply.jhtml?storeId=${aftersale.store.id}',
				callback: function (rows) {
					if (rows.length > 0) {
						var row = rows[0];
						$('.sampleTestApplySn').val(row.sn);
						$('.sampleTestApplyId').val(row.id);
					}
				}
			});


			[#if aftersale.wfId!=null]
			$("#wf_area").load("/act/wf/wf.jhtml?wfid=${aftersale.wfId}");
			[/#if]

			// 全屏看图功能 start
			$(".toPreviewBtn").click(function () {
				let a_href = $(this).next().next().children().find(".real-row .pic a")
				let listData = []
				for (var i = 0; i < a_href.length; i++) {
					let href_url = a_href[i].href
					let last_index = href_url.lastIndexOf('.');
					let getPath = href_url.substring(last_index + 1, href_url.length);
					if (getPath == "jpg" || getPath == "jpeg" || getPath == "png" || getPath == "bmp" || getPath == "gif" || getPath == "mp3" || getPath == "mp4" || getPath == "avi") {
						let item = a_href[i].href
						listData.push(item)
					}
				}
				browseWholePicture(listData);
			});

			var rotateParam = 0
			var scaleParam = 1
			// 关闭全屏看图
			$("#picture-colse").click(function () {
				$(".preview_swiperBox").fadeOut(300);
				$(".swiper-container").remove();
				rotateParam = 0
				scaleParam = 1
				$(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
				$(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
			});
			$("#picture-swiperLeft").click(function () {
				rotateParam = 0
				scaleParam = 1
				$(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
				$(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
			});
			$("#picture-swiperRight").click(function () {
				rotateParam = 0
				scaleParam = 1
				$(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
				$(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
			});


			$("#previewReset").click(function () {
				rotateParam = 0
				scaleParam = 1
				$(".swiper-wrapper .swiper-slide-active img").css({
					"transform": "rotate(0deg) scale(1)",
					"transition": "transform .3s"
				})
			});
			$("#rotateLeft").click(function () {
				rotateParam -= 90
				let rotateStyle = "rotate(" + rotateParam + "deg)"
				let scaleStyle = "scale(" + scaleParam + ")"
				$(".swiper-wrapper .swiper-slide-active img").css({
					"transform": (rotateStyle + scaleStyle),
					"transition": "transform .3s"
				})
			});
			$("#rotateRight").click(function () {
				rotateParam += 90
				let rotateStyle = "rotate(" + rotateParam + "deg)"
				let scaleStyle = "scale(" + scaleParam + ")"
				$(".swiper-wrapper .swiper-slide-active img").css({
					"transform": (rotateStyle + scaleStyle),
					"transition": "transform .3s"
				})
			});
			$("#scaleBig").click(function () {
				scaleParam += 0.1
				let rotateStyle = "rotate(" + rotateParam + "deg)"
				let scaleStyle = "scale(" + scaleParam + ")"
				$(".swiper-wrapper .swiper-slide-active img").css({
					"transform": (rotateStyle + scaleStyle),
					"transition": "transform .3s"
				})
			});
			$("#scaleSmall").click(function () {
				scaleParam -= 0.1
				let rotateStyle = "rotate(" + rotateParam + "deg)"
				let scaleStyle = "scale(" + scaleParam + ")"
				$(".swiper-wrapper .swiper-slide-active img").css({
					"transform": (rotateStyle + scaleStyle),
					"transition": "transform .3s"
				})
			});
			// 全屏看图功能 end

			// 批量下载功能
			$(".bothDownload").click(function () {
				let input_dom = $(this).next().children().find(".real-row .txt-box").next()
				let listData = []
				let keyNameParam = ''
				for (var i = 0; i < input_dom.length; i++) {
					let item = parseInt(input_dom[i].defaultValue)
					if (!isNaN(item)) {
						listData.push(item)
					}
				}
				if (input_dom.length > 0) {
					let keyNameData = input_dom[0].name
					let name_index = keyNameData.indexOf('[');
					let name_param = keyNameData.substring(0, name_index);
					keyNameParam = name_param
				}
				batchDownloadFile(listData, keyNameParam);
			});
			// 批量下载功能 end
			//

			/*属地品质售后判责一：开始*/
			/*质量或非质量问题*/
			$(".afterJudgment").change(function () {
				var afterJudgment = $(".afterJudgment option:selected").val();
				if (afterJudgment == "1") {
					$(".qualityIssue").show();
					$(".noQualityIssue").hide();
				} else if (afterJudgment == "0") {
					$(".qualityIssue").hide();
					$(".noQualityIssue").show();
				} else {
					$(".qualityIssue").hide();
					$(".noQualityIssue").hide();
				}
			});
			/*质量问题二级*/
			$(".qualityIssue").change(function () {
				var qualityIssue = $(".qualityIssue option:selected").val();
				if (qualityIssue == "2") {
					$(".productLiability").show();
					$(".noQualityIssue").hide();
				} else {
					$(".productLiability").hide();
					$(".productLiability").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualityIssue").change(function () {
				var noQualityIssue = $(".noQualityIssue option:selected").val();
				if (noQualityIssue == "1") {
					$(".storeDuty").show();
					$(".userDuty").hide();
				} else if (noQualityIssue == "2") {
					$(".userDuty").show();
					$(".storeDuty").hide();
				} else {
					$(".storeDuty").hide();
					$(".userDuty").hide();
				}
			});
			/*属地品质售后判责一：结束*/

			/*属地品质售后判责二：开始*/
			$(".afterJudgment2").change(function () {
				var afterJudgment2 = $(".afterJudgment2 option:selected").val();
				if (afterJudgment2 == "1") {
					$(".qualityIssue2").show();
					$(".noQualityIssue2").hide();
				} else if (afterJudgment2 == "0") {
					$(".qualityIssue2").hide();
					$(".noQualityIssue2").show();
				} else {
					$(".qualityIssue2").hide();
					$(".noQualityIssue2").hide();
				}
			});
			/*质量问题二级*/
			$(".qualityIssue2").change(function () {
				var qualityIssue2 = $(".qualityIssue2 option:selected").val();
				if (qualityIssue2 == "2") {
					$(".productLiability2").show();
					$(".noQualityIssue2").hide();
				} else {
					$(".productLiability2").hide();
					$(".productLiability2").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualityIssue2").change(function () {
				var noQualityIssue2 = $(".noQualityIssue2 option:selected").val();
				if (noQualityIssue2 == "1") {
					$(".storeDuty2").show();
					$(".userDuty2").hide();
				} else if (noQualityIssue2 == "2") {
					$(".userDuty2").show();
					$(".storeDuty2").hide();
				} else {
					$(".storeDuty2").hide();
					$(".userDuty2").hide();
				}
			});
			/*属地品质售后判责二：结束*/

			/*属地品质售后判责三：开始*/
			$(".afterJudgment3").change(function () {
				var afterJudgment3 = $(".afterJudgment3 option:selected").val();
				if (afterJudgment3 == "1") {
					$(".qualityIssue3").show();
					$(".noQualityIssue3").hide();
				} else if (afterJudgment3 == "0") {
					$(".qualityIssue3").hide();
					$(".noQualityIssue3").show();
				} else {
					$(".qualityIssue3").hide();
					$(".noQualityIssue3").hide();
				}
			});
			/*质量问题二级*/
			$(".qualityIssue3").change(function () {
				var qualityIssue3 = $(".qualityIssue3 option:selected").val();
				if (qualityIssue3 == "2") {
					$(".productLiability3").show();
					$(".noQualityIssue3").hide();
				} else {
					$(".productLiability3").hide();
					$(".productLiability3").hide();
				}
			});
			/*非质量问题二级*/
			$(".noQualityIssue3").change(function () {
				var noQualityIssue3 = $(".noQualityIssue3 option:selected").val();
				if (noQualityIssue3 == "1") {
					$(".storeDuty3").show();
					$(".userDuty3").hide();
				} else if (noQualityIssue3 == "2") {
					$(".userDuty3").show();
					$(".storeDuty3").hide();
				} else {
					$(".storeDuty3").hide();
					$(".userDuty3").hide();
				}
			});
			/*属地品质售后判责三：结束*/

			compareAmount();
			[#if (aftersale.isReturn ==null)]
			CheckBoxDisabled('全部更换', '局部更换', '退货');
			[/#if]

			var processingStatusChanged = () => {
				let status = $(".processingStatus option:selected").val();
				if (status === "不处理") {
					$(".reasonForNotProcessing").show();
					$("select[name='reasonForNotProcessing']").attr("disabled", false);
				} else {
					$(".reasonForNotProcessing").hide();
					$("select[name='reasonForNotProcessing']").attr("disabled", true);
				}
			}
			$(".processingStatus").change(processingStatusChanged);
			processingStatusChanged();
			jfspChange();

		});

		var number = {
			extractNumber: function (a, len) {
				var $this = $(a).val();
				if (len != null) {
					if ($this.length > len) {
						var tval = $this.substring(0, len);
						$(a).val(tval);
					}
				}
				if (!number.isRealNum($this)) {
					$(a).val("");
				}
			}, isRealNum: function (val) {
				if (val === "" || val == null) {
					return false;
				}
				if (!isNaN(val)) {
					return true;
				} else {
					return false;
				}
			}
		}

		var string = {
			extractString: function (a, b, len) {
				var $this = $(a).val();
				if (len != null) {
					if ($this.length > len) {
						var tval = $this.substring(0, len);
						$(a).val(tval);
					}
				}
				var re;
				if (b == 0) {
					re = /[^A-Za-z0-9 ]/g;
				} else {
					re = /[^A-Za-z0-9 \*-]/g;
				}
				if (!string.check($this, re)) {
					$(a).val("");
				}
			}, check: function (val, re) {
				if (val === "" || val == null) {
					return false;
				}
				if (re.test(val)) {
					return false;
				} else {
					return true;
				}
			}
		}

		function editQty(t, e) {
			extractNumber(t, 2, false, e);
		}

		/*function check_wf(e) {
			var $this = $(e);
			var $form = $("#inputForm");

			//大自然正式
			if ($form.valid()) {
				$.message_confirm("您确定要审批流程吗？", function () {
					var objTypeId = 10002;
					/!*var modelId = 1;//大自然正式-测试
			var sbu = "

                    ${aftersale.sbu.name}";
			if(sbu == "实木"){
				modelId = 6975138;//发版前需改成正式环境实木地板售后流程ID(6975138)
			}*!/
					var modelId = ${modelId};
					if (modelId == null) {
						alert("该sbu没有对应售后流程");
						return;
					}
					var url = "check_wf.jhtml?id=${aftersale.id}&modelId=" + modelId + "&objTypeId=" + objTypeId;
					var data = $form.serialize();

					ajaxSubmit(e, {
						method: 'post',
						url: url,
						async: true,
						callback: function (resultMsg) {
							$.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
								location.reload(true);
							})
						}
					});
				});
			}
		}*/

		function saveform(e) {
			$.message_confirm("您确定要提交吗？", function () {
				textarea.setValue(["surveyDescription", "disposeScheme"]);
				//获取表单所有数据
				var params = $("#inputForm").serializeArray();
				//定义url
				var url = '/aftersales/aftersale/saveform.jhtml?Type=' + e;
				ajaxSubmit(e, {
					method: 'post',
					url: url,
					data: params,
					async: true,
					callback: function (resultMsg) {
						$.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
							reflush_wf();
						})
					}
				});
			});
		}

		function handle(e) {
			if ($(e).prop('checked') == true) {
				var $pdiv = $(e).parent().parent();
				$pdiv.find('input[type=text]').attr("readonly", false)
			} else {
				var $pdiv = $(e).parent().parent();
				$pdiv.find('input[type=text]').attr("readonly", "readonly");
				$pdiv.find('input[type=text]').val("");
			}
		}

		function hack(e) {
			if ($(e).prop('checked') == true) {
				var $pdiv = $(e).nextAll();
				$pdiv.attr("readonly", false)
				$pdiv.attr("required", true)
			} else {
				var $pdiv = $(e).nextAll();
				$pdiv.attr("readonly", "readonly");
				$pdiv.attr("required", false)
				$pdiv.val("");
				if ($(e).attr('name') == "fd" && ($(e).val() == "局部更换" || $(e).val() == "退货")) {
					// $('input[name="factoryCompensation"]').val($('input[name="layingArea"]').val());
					//$('input[name="factoryCompensation"]').val(0);
				}
			}
		}

		function hack2(e) {
			if ($(e).prop('checked') == true) {
				var $pdiv = $(e).next();
				$pdiv.attr("readonly", false)
				$pdiv.attr("required", true)
				if ($(e).val() == "退货") {
					$('input[name="isReturn"]').val(0);
				} else {
					$('input[name="isReturn"]').val(1);
				}
			} else {
				var $pdiv = $(e).next();
				$pdiv.attr("readonly", "readonly");
				$pdiv.attr("required", false)
				$pdiv.val("");
				if ($(e).attr('name') == "cd" && ($(e).val() == "局部更换" || $(e).val() == "退货")) {
					$('input[name="companyCompensation"]').val(0);
				}
				if ($(e).val() == "退货") {
					$('input[name="isReturn"]').val(1);
				}
			}
		}

		function checkTel(e) {
			var tel = $(e).val();
			var mobile = /^1[3|5|7|8|9]\d{9}$/, phone = /^0\d{2,3}-?\d{7,8}$/;
			if (mobile.test(tel) || phone.test(tel)) {
				return true
			} else {
				$(e).val("");
				return false;
			}
		}

		function showLevel(e) {
			$(".level").toggle()
		}

		//保存时触发
		function check(e) {
			textarea.setValue(["storeFaultMessage"]);
		}


		//作废
		function close_e(e) {
			var $this = $(e);
			var id = ${aftersale.id};
			var str = '您确定要作废吗？';
			$.message_confirm(str, function () {
				//定义url
				var url = 'cancel.jhtml';
				ajaxSubmit(e, {
					method: 'post',
					url: url,
					data: {id: id},
					async: true,
					callback: function (resultMsg) {
						$.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
							location.reload(true);
						})
					}
				});
			});
		}

		function update_time(e) {
			var $this = $(e);
			var $form = $("#inputForm");

			var data = $form.serialize();

			ajaxSubmit("", {
				method: 'post',
				url: 'update_time.jhtml',
				async: true,
				data: data,
				callback: function (resultMsg) {
					$.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
						location.reload(true);
					})
				}
			});
		}

		function calStandardAmount() {
			var factoryPrice = $("input[name='factoryPrice']").val() * 1;
			if (factoryPrice == undefined || factoryPrice == null || factoryPrice == '') {
				factoryPrice = 0;
			}
			var subsidiaryMaterialCost = $('#subsidiaryMaterialCost').val() * 1;
			if (subsidiaryMaterialCost == undefined || subsidiaryMaterialCost == null || subsidiaryMaterialCost == '') {
				subsidiaryMaterialCost = 0;
			}
			var labourCost = $('#labourCost').val() * 1;
			if (labourCost == undefined || labourCost == null || labourCost == '') {
				labourCost = 0;
			}
			var freight = $('#freight').val() * 1;
			if (freight == undefined || freight == null || freight == '') {
				freight = 0;
			}
			var layingArea = $("input[name='layingArea']").val() * 1;
			if (layingArea == undefined || layingArea == null || layingArea == '') {
				layingArea = 0;
			}
			var amount = layingArea * factoryPrice + [#if aftersale.formType == 1]subsidiaryMaterialCost + labourCost + [/#if] freight;
			$('#standardAmount').val(amount)
			//console.log(factoryPrice+","+subsidiaryMaterialCost+","+labourCost+","+freight+","+layingArea+","+amount);
			compareAmount();
		}

		function editAcr(t) {
			$('.acreage').val($(t).val());
			console.log("change:" + $('.acreage').val());
		}

		function calAmount() {
			//console.log("calAmount");
			var name = "";
			$('.cd').find("input[name='cd']").each(function () {
				//console.log($(this).attr("checked"));
				if ($(this).attr("checked") == "checked") {
					name = $(this).val();
					return false;
				}
			});

			console.log($(".acreage").val());
			var factoryPrice = $("input[name='factoryPrice']").val() * 1;
			if (factoryPrice == undefined || factoryPrice == null || factoryPrice == '') {
				factoryPrice = 0;
			}
			var subsidiaryMaterialCost = $('#subsidiaryMaterialCost').val() * 1;
			if (subsidiaryMaterialCost == undefined || subsidiaryMaterialCost == null || subsidiaryMaterialCost == '') {
				subsidiaryMaterialCost = 0;
			}
			var labourCost = $('#labourCost').val() * 1;
			if (labourCost == undefined || labourCost == null || labourCost == '') {
				labourCost = 0;
			}
			var freight = $('#freight').val() * 1;
			if (freight == undefined || freight == null || freight == '') {
				freight = 0;
			}
			var acr = $(".acreage").val() * 1;
			if (acr == undefined || acr == null || acr == '') {
				acr = 0;
			}
			var comCompensation = $("#comCompensation").val() * 1;
			if (comCompensation == undefined || comCompensation == null || comCompensation == '') {
				comCompensation = 0;
			}
			var otherCost = $("#otherCost").val() * 1;
			if (otherCost == undefined || otherCost == null || otherCost == '') {
				otherCost = 0;
			}
			var amount = subsidiaryMaterialCost + labourCost + freight;
			if (name == "全部更换" || name == "局部更换") {
				amount = acr * factoryPrice + amount + comCompensation + otherCost;
			} else if (name == "退货") {
				amount = acr * factoryPrice + comCompensation + amount + otherCost;
			} else if (name == "赔偿") {
				amount = comCompensation;
			} else {
				amount = 0;
			}
			//console.log("acr:"+acr);
			$("#amount").val(amount);
			compareAmount();

		}

		function compareAmount() {
			var amount = $("#amount").val();
			var standardAmount = $('#standardAmount').val();
			var tips = "";
			var name = "";
			$('.cd').find("input[name='cd']").each(function () {
				if ($(this).attr("checked") == "checked") {
					name = $(this).val();
					return false;
				}
			});
			// if(name=="赔偿"){
			// 	amount = (amount*1.0)+(standardAmount*1.0);
			// }
			// if(amount*1.0<=standardAmount*1.0){
			// 	tips="标准内";
			// }
			// if(amount*1.0>standardAmount*1.0 && amount*1.0<=standardAmount*1.3){
			// 	tips="标准外130%以下";
			// }
			// if(amount*1.0>standardAmount*1.3){
			// 	tips="标准外130%以上";
			// }
			if (amount * 1.0 > standardAmount * 1.3) {
				tips = "标准外130%以上";
			} else {
				if (amount * 1.0 > standardAmount * 1.0 && amount * 1.0 <= standardAmount * 1.3) {
					tips = "标准外130%以下";
				} else {
					if (name == "赔偿") {
						tips = "标准外130%以下";
					} else {
						tips = "标准内";
					}
				}
			}
			$("#tips").html('<b class="red">' + tips + '</b>')
		}

		function returnOrNot() {
			var val = $("#return option:selected").val();
			console.log("return changed:" + val)

			if (val == 1) {
				$(".return").each(function () {
					$(this).removeAttr("disabled");
				});
				$(".notReturn").each(function () {
					$(this).attr("disabled", "disabled");
					$(this).attr("checked", false);

					debugger;
					console.log($(".acreage").val());
					hack2($(this));
					console.log($(".acreage").val());
				});
			} else if (val == 2) {
				$(".notReturn").each(function () {
					$(this).removeAttr("disabled");
				});
				$(".return").each(function () {
					$(this).attr("disabled", "disabled");
					$(this).attr("checked", false);
					hack2($(this));
				});
			} else {

				$(".notReturn").each(function () {
					$(this).attr("disabled", "disabled");
					$(this).attr("checked", false);
					hack2($(this));
				});
				$(".return").each(function () {
					$(this).attr("disabled", "disabled");
					$(this).attr("checked", false);
					hack2($(this));
				});

			}

			calAmount();
		}

		function jfspChange() {
			let chose = $("#pzzj").val();
			if (chose == 0) {
				$("#isDeal").val("1");
				$("#isQuality").val("1");
				$(".jfspSolution").show();
			}
			if (chose == 1) {
				$("#isDeal").val("1");
				$("#isQuality").val("0");
				$(".jfspSolution").show();
			}
			if (chose == 2) {
				$("#isDeal").val("0");
				$("#isQuality").val("0");
				$(".jfspSolution").hide();
			}
		}

	</script>
</head>
<body>
<div class="pathh">
	&nbsp;${message("编辑售后申请单据")}
	<!-- 【勘察人${surveyor}||${surveyors}】【公司处理信息|客服审核${service}】 【工厂处理${factory}】【质量中心处理意见${quality}】【订单部核价${order}】【经销商处理信息及附件${store}】【售后回访${review}】 -->
</div>
<!-- 【公司处理信息|客服审核${services}】 【工厂处理${factorys}】【质量中心处理意见${qualitys}】【订单部核价${orders}】【经销商处理信息及附件${stores}】【售后回访${reviews}】-->
<form id="inputForm" onsubmit="return check(this)" action="/aftersales/aftersale/update.jhtml" method="post" type="ajax"
	  validate-type="validate">
	<input type="hidden" name="id" value="${aftersale.id}"/>
	<input type="hidden" name="createrName" value="${aftersale.createrName}"/>
	<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("单号")}:
				</th>
				<td>
					${aftersale.sn}
				</td>
				<th>
					${message("单据状态")}:
				</th>
				<td>
					[#if aftersale.invoiceStatus == '0']<span class="blue">已保存</span>[/#if]
					[#if aftersale.invoiceStatus == '1']<span style="color:orange;">进行中</span>[/#if]
					[#if aftersale.invoiceStatus == '2']<span class="green">已生效</span>[/#if]
					[#if aftersale.invoiceStatus == '3']<span class="red">已终止</span>[/#if]
					[#if aftersale.invoiceStatus == '99']<span class="red">已作废</span>[/#if]
				</td>
				<th>
					<span class="requiredField">*</span>${message("处理状态")}:
				</th>
				<td>
					${aftersale.processingStatus}
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("顾客姓名")}:
				</th>
				<td>
					<input type="text" name="name" class="text" maxlength="200" value="${aftersale.name}"
						   btn-fun="clear"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("联系电话")}:
				</th>
				<td>
					<input type="text" name="contactNumber" class="text" maxlength="200"
						   value="${aftersale.contactNumber}" oninput="number.extractNumber(this,11)" btn-fun="clear"/>
				</td>
				<th>
					${message("联系电话2")}:
				</th>
				<td>
					<input type="text" name="contactNumberTow" class="text" maxlength="200"
						   value="${aftersale.contactNumberTow}" oninput="number.extractNumber(this,11)"
						   btn-fun="clear"/>
				</td>
				[#-- <th>
	        			<span class="requiredField">*</span>${message("售后等级")}:
	        		</th>
	        		<td>
	        			<select name="grade" class="text grade">
	        					<option></option>
								<option value="A"[#if aftersale.grade == "A"] selected[/#if]>${message("A")}</option>
								<option value="B"[#if aftersale.grade == "B"] selected[/#if]>${message("B")}</option>
								<option value="C"[#if aftersale.grade == "C"] selected[/#if]>${message("C")}</option>
								<option value="D"[#if aftersale.grade == "D"] selected[/#if]>${message("D")}</option>
						</select>
	        		</td> --]
				<th>
					${message("生产时间")}:
				</th>
				<td>
					[#if aftersale.productionTime??]
						${aftersale.productionTime?string('yyyy-MM-dd HH:mm:ss')}
					[/#if]
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("故障名称")}:
				</th>
				<td>
					<input type="text" name="faultName" class="text" maxlength="200" value="${aftersale.faultName}"
						   btn-fun="clear"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("购买时间")}:
				</th>
				<td>
					<input type="text" name="decorationTime" value="${aftersale.decorationTime}" class="text"
						   maxlength="200"
						   onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("采暖方式")}:
				</th>
				<td>
					<select name="heatingSystem" class="text heatingSystem">
						<option></option>
						<option value="无"[#if aftersale.heatingSystem == "无"] selected[/#if]>${message("无")}</option>
						<option value="地暖"[#if aftersale.heatingSystem == "地暖"] selected[/#if]>${message("地暖")}</option>
						<option value="电暖"[#if aftersale.heatingSystem == "电暖"] selected[/#if]>${message("电暖")}</option>
						<option value="壁暖（暖气片）"[#if aftersale.heatingSystem == "壁暖（暖气片）"] selected[/#if]>${message("壁暖（暖气片）")}</option>
						<option value="空调（冬季）"[#if aftersale.heatingSystem == "空调（冬季）"] selected[/#if]>${message("空调（冬季）")}</option>
					</select>
				</td>

				<th>
					<span class="requiredField">*</span>${message("是否已入住")}:
				</th>
				<td>
					<select name="isCheckIn" class="text isCheckIn">
						<option></option>
						<option value="true"[#if aftersale.isCheckIn == true] selected[/#if]>${message("是")}</option>
						<option value="false"[#if aftersale.isCheckIn == false] selected[/#if]>${message("否")}</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("铺设时间")}:
				</th>
				<td>
					<input type="text" name="layingTime" value="${aftersale.layingTime}" class="text" maxlength="200"
						   onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});"/>
				</td>
				<th>

					${message("入住时间")}:
				</th>
				<td>
					<input type="text" name="checkInTime" value="${aftersale.checkInTime}" class="text" maxlength="200"
						   onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("故障时间")}:
				</th>
				<td>
					<input type="text" name="downTime" value="${aftersale.downTime}" class="text" maxlength="200"
						   onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("铺设方")}:
				</th>
				<td>
					<select name="layingName" class="text layingName">
						<option></option>
						<option value="经销商"[#if aftersale.layingName == "经销商"] selected[/#if]>${message("经销商")}</option>
						<option value="客户安装"[#if aftersale.layingName == "客户安装"] selected[/#if]>${message("客户安装")}</option>
						<option value="装饰公司"[#if aftersale.layingName == "装饰公司"] selected[/#if]>${message("装饰公司")}</option>
						<option value="家装公司"[#if aftersale.layingName == "家装公司"] selected[/#if]>${message("家装公司")}</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("铺设防潮")}:
				</th>
				<td>
					<select name="layingMoistureproof" class="text layingMoistureproof">
						<option></option>
						<option value="双重防潮"[#if aftersale.layingMoistureproof == "双重防潮"] selected[/#if]>${message("双重防潮")}</option>
						<option value="单重防潮"[#if aftersale.layingMoistureproof == "单重防潮"] selected[/#if]>${message("单重防潮")}</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("铺设面积(㎡)")}:
				</th>
				<td>
					[#if aftersale.wfId == null || appointRejectFlag]
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button"/>
							<input id="layingArea" class="t layingArea" name="layingArea"
								   value="${aftersale.layingArea}" mindata="0" oninput="editPrice(this,event)"
								   onpropertychange="editQty(this,event)" type="text"/>
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button"/>
						</div>
					[#else]
						<span>${aftersale.layingArea}</span>
						<input type="hidden" name="layingArea" value="${aftersale.layingArea}"/>
					[/#if]
				</td>
				<th>
					<span class="requiredField">*</span>${message("铺装地址")}:
				</th>
				<td colspan="3">
					<input type="hidden" id="newAreaId" name="headNewArea.id" value="${(aftersale.headNewArea.id)!}"
						   treePath="${(aftersale.headNewArea.treePath)!}"/>
					<input type="text" name="layingAddress" value="${aftersale.layingAddress}" placeholder="请输入详细地址！"
						   class="detailed-address" maxlength="200"/>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("铺设方式")}:
				</th>
				<td>
					<select name="layingWay" class="text layingWay">
						<option></option>
						<option value="龙骨法"[#if aftersale.layingWay == "龙骨法"] selected[/#if]>${message("龙骨法")}</option>
						<option value="悬浮法"[#if aftersale.layingWay == "悬浮法"] selected[/#if]>${message("悬浮法")}</option>
						<option value="夹板法"[#if aftersale.layingWay == "夹板法"] selected[/#if]>${message("夹板法")}</option>
						<option value="胶贴法"[#if aftersale.layingWay == "胶贴法"] selected[/#if]>${message("胶贴法")}</option>
						<option value="钢扣法"[#if aftersale.layingWay == "钢扣法"] selected[/#if]>${message("钢扣法")}</option>
					</select>
				</td>
				<th>
					${message("经销商姓名")}:
				</th>
				<td>
					[#--  --if storeMember.memberType == 1]
                        <span class="dealerName">${aftersale.store.dealerName}</span>
                        <input type="hidden" name="storeId" class="text storeId" value="${aftersale.store.id}"/>
                        <input type="hidden" name="saleOrgId" class="text saleOrgId" value="${aftersale.saleOrg.id}"/>
                    [#else--]
					<span class="search" style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId"
								   value="${aftersale.saleOrg.id}"/>
		                    <input type="hidden" name="storeId" class="text storeId" value="${aftersale.store.id}"
								   btn-fun="clear"/>
		                    <input type="text" class="text dealerName" maxlength="200"
								   value="${aftersale.store.dealerName}" onkeyup="clearSelect(this)" readOnly/>
		                    <input type="button" class="iconSearch" value="" id="openStore"/>
	                    </span>
					[#-- [/#if] --]
				</td>
				<th>
					${message("经销商名称")}:
				</th>
				<td>
					<span class="sotreName">${aftersale.store.name}</span>
				</td>
				<th>
					${message("经销商电话")}:
				</th>
				<td>
					${aftersale.store.headPhone}
				</td>

			</tr>
			<tr>
				<th>
					${message("经销商授权编码")}:
				</th>
				<td>
					<span class="grantCode">${aftersale.store.grantCode}</span>
				</td>
				<th>
					${message("ERP编码")}:
				</th>
				<td>
					<span class="outTradeNo">${aftersale.store.outTradeNo}</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("勘察人姓名")}:
				</th>
				<td>
					<input type="text" name="zbsurveyorName" value="${aftersale.zbsurveyorName}" class="text"
						   maxlength="200" btn-fun="clear"/>
				</td>

				<th>
					<span class="requiredField">*</span>${message("联系方式")}:
				</th>
				<td>
					<input type="text" name="zbsurveyorPhone" value="${aftersale.zbsurveyorPhone}" class="text"
						   maxlength="200" onblur="checkTel(this)" btn-fun="clear"/>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("工厂发货时间")}:
				</th>
				<td>
					<input type="text" name="dateForDealersToAskForGoods"
						   value="${aftersale.dateForDealersToAskForGoods}" class="text" maxlength="200"
						   onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});"/>
				</td>
				<th>
					${message("业务类型")}:
				</th>
				<td>
					<select name="businessTypeId" id="businessTypeId" class="text">
						<option value=""></option>
						[#list businessTypes as businessType]
							<option lowerCode="${businessType.lowerCode}"
									value="${businessType.id}" [#if aftersale.businessType.id==businessType.id] selected[/#if]>${businessType.value}</option>
						[/#list]
					</select>
				</td>
				<th>
					${message("安装单号")}:
				</th>
				<td>
                    <span class="search" style="position:relative">
		                    <input type="hidden" name="fiveGdId" class="text fiveGdId" value="${aftersale.fiveGdId}"
								   btn-fun="clear"/>
		                    <input type="text" class="text fiveGdSn" name="fiveGdSn" maxlength="200" value="${aftersale.fiveGdSn}"
								   onkeyup="clearSelect(this)" readOnly/>
		                    <input type="button" class="iconSearch" value="" id="openAzdh"/>
	                    </span>
				</td>
				<th style="text-align: left; ">
					<a href="javascript:void(0);" id="checkAzd" class="blue">查看</a>
				</th>
			</tr>

			<tr name="business" id="business">
				<th>${message("合同编码")}:</th>
				<td>
            		 <span class="search" style="position: relative">
            		  <input type="hidden" name="customerContractId" class="text customerContractId"
							 value="${aftersale.customerContract.id}"
							 btn-fun="clear"/>
            		  <input type="text" name="contractSn" class="text contractSn"
							 value="${aftersale.customerContract.sn}"
							 maxlength="200" id="checkContractName" onkeyup="clearSelect(this)" readOnly/>
            		  <input type="button" class="iconSearch" value="" id="selectContract"></span>
				</td>

				<th>
					${message("合同名称")}:
				</th>
				<td>
					<input type="text" class="text contractName" name="contractName"
						   value="${aftersale.customerContract.contractName}" btn-fun="clear">
				</td>
			</tr>

			[#if aftersale.invoiceStatus==2]
				<tr>
					<th>${message("政策单号")}</th>
					<td>${aftersale.depositRechargeSn}</td>
					<th>${message("政策到账状态")}</th>
					<td>
						<span>
						[#if aftersale.isReceived==true]<b class="green">已到账</b>
						[#elseif aftersale.isReceived==false]<b class="red">未到账</b>
						[/#if]
						</span>
					</td>
					[#if aftersale.isReceived==true]
						<th>${message("到账时间")}</th>
						<td>${aftersale.receiveDate?string('yyyy-MM-dd HH:mm:ss')}</td>


						[#if isBanned==false]
							<th>${message("到账金额")}</th>
							<td>${aftersale.receiveAmount}</td>
						[/#if]
					[/#if]
				</tr>
			[/#if]

			[#if aftersale.b2bReturnSn != null]
				<tr>
					<th>${message("退货单号")}</th>
					<td>${aftersale.b2bReturnSn}</td>
					<th>${message("退货状态")}</th>
					<td>
						<span>
						[#if aftersale.returnStatus=="0"]<b class="red">未退货</b>
						[#elseif aftersale.returnStatus=="1"]<b class="green">退货中</b>
						[#elseif aftersale.returnStatus=="2"]<b class="green">已退货</b>
						[/#if]
						</span>
					</td>
					[#if aftersale.b2bReturnStatus>0]
						<th>${message("退货时间")}</th>
						<td>${aftersale.returnDate?string('yyyy-MM-dd HH:mm:ss')}</td>
						[#if isBanned==false]
							<th>${message("退款金额")}</th>
							<td>${aftersale.acturalReturnAmount}</td>
						[/#if]
					[/#if]
				</tr>
				<tr>
					<th>${message("实退数量")}</th>
					<td>${total}</td>
					<th>${message("退款金额")}</th>
					<td>${b2bReturns.returnAmount}</td>
					[#if b2bReturns.erpDate != null]
						<th>${message("退货到账时间")}</th>
						<td>${b2bReturns.erpDate?string('yyyy-MM-dd HH:mm:ss')}</td>
					[/#if]
				</tr>
			[/#if]
		</table>
		[#if storeMember.memberType !=1 ]
			<div>
				<span class="title-style fujian">${message("历史售后单")}</span>
				<table id="table-historyAftersale" style="width:850px"></table>
			</div>
		[/#if]
		[#--        <br/>--]
		[#--			<div style="display: inline-block">--]
		[#--				<a href="javascript:void(0);" onclick="showLevel(this)" class="button">售后等级分级说明</a>--]
		[#--			</div>--]
		[#-- <div class="title-style" style="border:1px solid #dcdcdc; padding:10px 15px; font:13px 'Microsoft YaHei';">
			<div style="color:rgb(227, 18, 25); margin:10px 0 0 0;">★售后等级分级说明</div>
				<pre style="font:13px 'Microsoft YaHei';" >
一、重大投诉：
A级：用户财产遭到重大损失或引起人身伤亡、产品被权威部门评定不合格、权威媒体已进行曝光或权威机构已介入调查。
B级：用户投诉到媒体、消协、工商局等公众部门，或起诉到法院。产品质量可能涉及安全隐患，并对用户造成一定财产损失。
C级：非产品质量问题，用户的行为过激对公司及品牌可能造成或已造成负面影响。
二、一般性投诉
D级：除重大投诉外的其他投诉，均视为一般性投诉。
				</pre>
		    </div> --]
		<div class="title-style">${message("产品资料")}:</div>
		<table class="input input-edit">
			<tr>
				<th>
					${message("地板品类")}:
				</th>
				<td>
					<input type="text" name="category" class="text category" maxlength="200"
						   value="${aftersale.category}" btn-fun="clear" readOnly/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("产品型号")}:
				</th>
				<td>
		                <span class="search" style="position:relative">
		                    <input type="hidden" name="productId" class="text productId" value="${aftersale.product.id}"
								   btn-fun="clear"/>
		                    <input type="text" class="text productModel" value="${aftersale.product.model}"
								   maxlength="200" onkeyup="clearSelect(this)" readOnly/>
		                    [#if aftersale.wfId==null]<input type="button" class="iconSearch" value=""
															 id="openProduct">[/#if]
		                </span>
				</td>
				<th>
					${message("产品名称")}:
				</th>
				<td>
					<span class="productName">${aftersale.product.name}</span>
				</td>
				<th>
					${message("产品规格")}:
				</th>
				<td>
					<span class="productSpec">${aftersale.product.spec}</span>
				</td>
			</tr>
			<tr>
				<th>
					${message("产品描述")}:
				</th>
				<td>
					<span class="productDescription">${aftersale.product.description}</span>
				</td>
				<th>
					${message("产品编码")}:
				</th>
				<td>
					<span class="productVonderCode">${aftersale.product.vonderCode}</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("销售单价(元)")}:
				</th>
				<td>
					<div class="nums-input ov">
						<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button"/>
						<input id="price" class="t price" name="price" value="${aftersale.price}" mindata="0"
							   oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" type="text"/>
						<input class="b increase" value="+" onmousedown="increase(this,event)" type="button"/>
					</div>
				</td>
				<th>
					${message("销售总额(元)")}:
				</th>
				<td>
					<span class="red totalSales">${currency(aftersale.totalSales, true)}</span>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("批次号")}:
				</th>
				<td>
					<input type="text" name="batchNumber" class="text" maxlength="200" value="${aftersale.batchNumber}"
						   btn-fun="clear" oninput="string.extractString(this,0,50)"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("板底喷码")}:
				</th>
				<td>
					<input type="text" name="spurtCode" class="text" maxlength="200" value="${aftersale.spurtCode}"
						   btn-fun="clear" oninput="string.extractString(this,1,50)"/>
				</td>
				<th>
					${message("sbu")}:
				</th>
				<td>
					<input type="text" class="sbuName" value="${aftersale.sbu.name}">
					<input type="hidden" name="sbu.id" class="text sbuId" maxlength="200" value="${aftersale.sbu.id}"
						   btn-fun="clear" readonly/>
				</td>
				<th class="woodType">
					${message("木种")}:
				</th>
				<td class="woodType">
					<select name="woodTypeId">
						<option value=""></option>
						[#list woodTypeProductOfficer as woodType]
							<option id="${woodType.value}" value="${woodType.id}"
									[#if aftersale.woodType.id == woodType.id]selected[/#if]>${woodType.value}</option>
						[/#list]
					</select>
				</td>
			</tr>
		</table>
		<div class="title-style">${message("现场勘查信息及附件")}:</div>
		<table class="input input-edit">
			<tr>
				<th>
					${message("地面平整度")}:
				</th>
				<td>
					<input type="text" name="floorFlatness" class="text" maxlength="200"
						   value="${aftersale.floorFlatness}" btn-fun="clear"/>
				</td>
				<th>
					${message("地面含水率")}:
				</th>
				<td>
					<input type="text" name="groundMoistureContent" maxlength="200" oninput="editQty(this)"
						   value="${aftersale.groundMoistureContent}"
						   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
					<span>%</span>
				</td>
				<th>
					${message("地板含水率")}:
				</th>
				<td>
					<input type="text" name="floorMoistureContent" maxlength="200"
						   value="${aftersale.floorMoistureContent}" oninput="editQty(this)"
						   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
					<span>%</span>
				</td>
				<th>
					${message("空气湿度")}:
				</th>
				<td>
					<input type="text" name="airHumidity" maxlength="200" value="${aftersale.airHumidity}"
						   oninput="editQty(this)"
						   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
					<span>%</span>
				</td>
			</tr>
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			<div class="title-style">
				${message("上传附件：销售合同、验收单、现场照片或视频 ")}:
			</div>
			<tr>
				<th><span class="requiredField">*</span>${message("销售合同")}:</th>
				<td>
					[#if aftersale.wfId == null || appointRejectFlag]
						<a href="javascript:;" id="addSalesContractAttach" class="button">添加附件</a>
					[/#if]
				</td>
				<th><span class="requiredField">*</span>${message("验收单")}:</th>
				<td>
					[#if aftersale.wfId == null || appointRejectFlag]
						<a href="javascript:;" id="addAcceptanceSheetAttach" class="button">添加附件</a>
					[/#if]
				</td>
				<th><span class="requiredField">*</span>${message("现场照片或视频")}:</th>
				<td>
					[#if aftersale.wfId == null || appointRejectFlag]
						<a href="javascript:;" id="addScenePicturesAttach" class="button">添加附件</a>
					[/#if]
				</td>
			</tr>
		</table>
		<div>
			<span class="title-style fujian">${message("销售合同")}:</span>
			<span class="toPreviewBtn button">浏览全图</span>
			<span class="bothDownload button">批量下载</span>
			<table id="table-salesContractAttach" style="width:850px"></table>

			<span class="title-style fujian">${message("验收单")}:</span>
			<span class="toPreviewBtn button">浏览全图</span>
			<span class="bothDownload button">批量下载</span>
			<table id="table-acceptanceSheetAttach" style="width:850px"></table>

			<span class="title-style fujian">${message("现场照片或视频")}:</span>
			<span class="toPreviewBtn button">浏览全图</span>
			<span class="bothDownload button">批量下载</span>
			<table id="table-scenePicturesAttach" style="width:850px"></table>
		</div>
		<table class="input input-edit" style="width:60%;margin-top:5px;">
			<br/>
			<div style="width:600px;height:30px;line-height: 30px">
				<span class="requiredField">*</span>
				<span style="font-size:16px;"><b>经销商描述故障信息:(</b></span>
				<span>详细描述故障现象、比例、分布情况及维修记录(不能低于50个文字)</span>
				<span style="font-size:16px;"><b>)</b></span>
			</div>
			<tr>
				<td colspan="7">
                    <textarea class="text" name="storeFaultMessage" maxlength="4000"
							  style="width:800px;height:160px;">${aftersale.storeFaultMessage}</textarea>
				</td>
			</tr>
		</table>
		<table class="input input-edit" style="width:60%;margin-top:5px;">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("经销商处理方案")}:
				</th>
				<td colspan="7">
					<div class="storeTreatmentScheme">
						<label><input class="check js-iname text" name="storeTreatmentScheme" type="checkbox"
									  value="全部更换"/>${message("全部更换")}</label>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<label>
							<input class="check js-iname text" name="storeTreatmentScheme" type="checkbox" value="局部更换"
								   onchange="hack(this)"/>${message("局部更换")}
							<input type="text" class="underline" name="storePartialReplacement"
								   value="${aftersale.storePartialReplacement}" oninput="editQty(this)" maxlength="200"
								   readOnly/><span>m2</span></label>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<label>
							<input class="check js-iname text" name="storeTreatmentScheme" type="checkbox" value="退货"
								   onchange="hack(this)"/>${message("退货")}
							<input type="text" class="underline" name="storeReturnGoods"
								   value="${aftersale.storeReturnGoods}" oninput="editQty(this)" maxlength="200"
								   readOnly/><span>m2</span>
						</label>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<label>
							<input class="check js-iname text" name="storeTreatmentScheme" type="checkbox" value="赔偿"
								   onchange="hack(this)"/>${message("赔偿")}
							<input type="text" class="underline" name="storeCompensation"
								   value="${aftersale.storeCompensation}" oninput="editQty(this)" maxlength="200"
								   readOnly/>
							<span>元</span>
						</label>
					</div>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("赔款明细")}:
				</th>
				<td>
					<span>${message("赔款用户")}</span>
					<input type="text" name="reparationUser" maxlength="200" value="${aftersale.reparationUser}"
						   oninput="editQty(this)"
						   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
					<span>元;</span>
					<span>${message("其他费用")}</span>
					<input type="text" name="otherExpenses" maxlength="200" value="${aftersale.otherExpenses}"
						   oninput="editQty(this)"
						   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
					<span>元</span>
				</td>
			</tr>
		</table>

		<div class="title-style">${message("公司处理信息")}:[#if node.name?contains("售后工程师")]<input type="button"
																										 class="bottonss tj"
																										 onclick="saveform(1)"
																										 value="提交"/>[/#if]</div>
		<table class="input input-edit">
			<tr>
				<th>${message("售后部调查信息")}:</th>
				<td colspan="5">
                    <textarea class="text" name="disposeScheme" maxlength="4000"
							  style="width:875px;height:160px;"
							  [#if aftersale.wfId == null]readonly[/#if]>${aftersale.disposeScheme}</textarea>
				</td>
			</tr>
			<tr>
				<th>
					${message("处理状态")}:
				</th>
				<td>
					<select name="processingStatus"
							class="text processingStatus" [#if aftersale.wfId == null] style="pointer-events: none;" [/#if] >
						<option></option>
						<option value="待勘察"[#if aftersale.processingStatus == "待勘察"] selected[/#if]>${message("待勘察")}</option>
						<option value="待观察"[#if aftersale.processingStatus == "待观察"] selected[/#if]>${message("待观察")}</option>
						<option value="待协商"[#if aftersale.processingStatus == "待协商"] selected[/#if]>${message("待协商")}</option>
						<option value="缺资料"[#if aftersale.processingStatus == "缺资料"] selected[/#if]>${message("缺资料")}</option>
						<option value="已处理"[#if aftersale.processingStatus == "已处理"] selected[/#if]>${message("已处理")}</option>
						<option value="不处理"[#if aftersale.processingStatus == "不处理"] selected[/#if]>${message("不处理")}</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("责任工厂")}:
				</th>
				<td>
	                    <span class="search" style="position:relative">
		                    <input type="hidden" name="factoryId" class="text factoryId" value="${aftersale.factoryId}"
								   btn-fun="clear"/>
		                    <input type="text" name="factoryName" class="text factoryName"
								   value="${aftersale.factoryName}" maxlength="200" onkeyup="clearSelect(this)"
								   readOnly/>
                            [#if node.name?contains("售后工程师")]
								<input type="button" class="iconSearch" value="" id="openSaleOrg">
							[/#if]
	                    </span>
				</td>
				<th>
					<span class="requiredField">*</span>选择订单经理:
				</th>
				<td>
	                    <span class="search" style="position:relative">
		                    <input type="hidden" name="orderManager.id" class="text orderManagerId"
								   value="${aftersale.orderManager.id}" btn-fun="clear"/>
		                    <input type="text" name="" class="text orderManagerName"
								   value="${aftersale.orderManager.name}" maxlength="200" onkeyup="clearSelect(this)"
								   readOnly/>
                            [#if node.name?contains("售后工程师")]
								<input type="button" class="iconSearch" value="" id="openOrderManager">
							[/#if]
                        </span>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("单据类别")}:
				</th>
				<td>
					<select name="formType"
							class="text formType" [#if aftersale.wfId == null] style="pointer-events: none;" [/#if]>
						<option></option>
						<option value="0" [#if aftersale.formType == 0] selected[/#if]>${message("售前单")}</option>
						<option value="1" [#if aftersale.formType == 1] selected[/#if]>${message("售后单")}</option>
					</select>
				</td>
				<th>
					${message("生产时间")}:
				</th>
				<td>
					<input type="text" name="productionTime"
						   [#if aftersale.productionTime??]value="${aftersale.productionTime?string('yyyy-MM-dd HH:mm:ss')}"[/#if]
						   class="text" maxlength="200"
						   onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d',readOnly:'true'});"
							[#if aftersale.wfId == null] disabled [/#if]>
					<input type="hidden" name="isSendToQualityCenter" value="0"/>
				</td>

				[#if aftersale.businessType.remark != "直营零售(售后)" && aftersale.businessType.remark != "经销商零售(售后)"]
					<th>
						<span class="requiredField">*</span>${message("选择产品专业总监")}:
					</th>
					<td>
	                    <span class="search" style="position:relative">
		                    <input type="hidden" name="productionSumUpId" class="text productionSumUpId"
								   value="${aftersale.productionSumUp.id}" btn-fun="clear"/>
		                    <input type="text" name=""
								   [#if aftersale.wfId == null]readonly [/#if] class="text productionSumUpName"
								   value="${aftersale.productionSumUp.name}" maxlength="200" onkeyup="clearSelect(this)"
								   readOnly/>
		                    <input type="button" class="iconSearch" value="" id="openProductionSumUp">
	                    </span>
					</td>
				[#else]
					<th>
						${message("故障类型")}:
					</th>
					<td>
						<input type="text" name="faultType" value="${aftersale.faultType}" class="text" maxlength="200"
							   btn-fun="clear" [#if aftersale.wfId == null] readonly [/#if]>
					</td>
				[/#if]
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("责任判定")}:
				</th>
				<td>
					<input type="text" class="text" name="liabilityJudgments" value="${aftersale.liabilityJudgments}"
						   [#if aftersale.wfId == null]readonly [/#if]>
				</td>

				[#if modelId == "45227731"]
					<th>
						<span class="requiredField">*</span>${message("选择产品专业总监")}:
					</th>
					<td>
	                    <span class="search" style="position:relative">
		                    <input type="hidden" name="productionSumUpId" class="text productionSumUpId"
								   value="${aftersale.productionSumUp.id}" btn-fun="clear"/>
		                    <input type="text" name=""
								   [#if aftersale.wfId == null]readonly [/#if] class="text productionSumUpName"
								   value="${aftersale.productionSumUp.name}" maxlength="200" onkeyup="clearSelect(this)"
								   readOnly/>
		                    <input type="button" class="iconSearch" value="" id="openProductionSumUp">
	                    </span>
					</td>
				[/#if]

				[#if aftersale.businessType.remark != "直营零售(售后)" && aftersale.businessType.remark != "经销商零售(售后)"]
					<th>
						${message("故障类型")}:
					</th>
					<td>
						<input type="text" name="faultType" value="${aftersale.faultType}" class="text" maxlength="200"
							   btn-fun="clear" [#if aftersale.wfId == null] readonly [/#if]>
					</td>
				[/#if]
				[#if aftersale.wfId != null]
					<th class="text reasonForNotProcessing" style="display:none">
						${message("不处理原因")}:
					</th>
					<td>
						<select name="reasonForNotProcessing" class="text reasonForNotProcessing" style="display:none">
							<option></option>
							<option value="安装原因"[#if aftersale.reasonForNotProcessing == "安装原因"] selected[/#if]>${message("安装原因")}</option>
							<option value="使用维护原因"[#if aftersale.reasonForNotProcessing == "使用维护原因"] selected[/#if]>${message("使用维护原因")}</option>
							<option value="其他原因"[#if aftersale.reasonForNotProcessing == "其他原因"] selected[/#if]>${message("其他原因")}</option>
						</select>
					</td>
				[/#if]
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("故障原因")}:
				</th>
				<td colspan="5">
                    <textarea class="text" name="faultReason" maxlength="4000"
							  style="width:875pxpx;height:60px;" [#if aftersale.wfId == null] readonly [/#if]>${aftersale.faultReason}</textarea>
				</td>
			</tr>
		</table>
			<div class="title-style">${message("勘察")}[#if node.name == "勘察"]<input type="button" class="bottonss tj"
																					   onclick="saveform(0)" value="提交"/>[/#if]
			</div>
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>${message("是否现场勘察")}:
					</th>
					<td>
						<label><input type="radio" name="isLocaleSurveyor" value="0"
									  [#if aftersale.isLocaleSurveyor==0]checked[/#if]/>是</label>&nbsp;&nbsp;
						<label><input type="radio" name="isLocaleSurveyor" value="1"
									  [#if aftersale.isLocaleSurveyor==1]checked[/#if]/>否</label>&nbsp;&nbsp;
					</td>
					<th>
						${message("勘察人")}:
					</th>
					<td>
	                    <span class="search" style="position:relative">
		                    <input type="hidden" name="surveyor.id" class="text surveyorId"
								   value="${aftersale.surveyor.id}" btn-fun="clear"/>
		                    <input type="text" class="text surveyorName" value="${aftersale.surveyor.name}"
								   maxlength="200" onkeyup="clearSelect(this)"/>
		                    <input type="button" class="iconSearch" value="" id="openSurveyor">
	                    </span>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("勘察人填写")}[#if node.name?contains("勘察人")]<input type="button" class="bottonss tj"
																									  onclick="saveform(7)"
																									  value="提交"/>[/#if]</div>
			<table class="input input-edit">
				<tr>
					<th>
						${message("勘察时间")}:
					</th>
					<td>
						<input type="text" name="zbsurveyorDate" value="${aftersale.zbsurveyorDate}"
							   class="text predictConstructionTime" maxlength="200"
							   onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:true});"/>
					</td>
					<th>${message("勘察人")}:</th>
					<td>
						<input type="text" name="surveyorName"
							   value="[#if aftersale.surveyorName == null]${aftersale.surveyor.name}[#else]${aftersale.surveyorName}[/#if]"
							   class="text" maxlength="200" btn-fun="clear"/>
					</td>
					<th></th>
					<td></td>
					<th></th>
					<td></td>
				</tr>
				<tr>
					<th>${message("勘察说明")}:</th>
					<td colspan="7">
                <textarea class="text" name="surveyDescription" maxlength="4000"
						  style="width:800px;height:160px;">${aftersale.surveyDescription}</textarea>
					</td>
				</tr>
				<tr>
					<th>
						${message("勘察图片附件")}:
					</th>
					[#if node.name?contains("勘察人")]
						<td>
							<a href="javascript:;" id="addSurveyorAttach" class="button">添加附件</a>
						</td>
					[/#if]
				</tr>
			</table>
			<div>
				<span class="title-style fujian">${message("勘察图片附件")}:</span>
				<span class="toPreviewBtn button">浏览全图</span>
				<span class="bothDownload button">批量下载</span>
				<table id="table-surveyorAttach" style="width:850px"></table>
			</div>
			<div class="title-style">${message("属地品质")}[#if node.name?contains("属地品质")]<input type="button" class="bottonss tj"
																									  onclick="saveform(8)"
																									  value="提交"/>[/#if]
			</div>
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>售后判责一:
					</th>
					[#--一级分类--]
					<td>
						<select name="afterJudgment" class="text afterJudgment">
							<option></option>
							<option value="1" [#if aftersaleJudgment.afterJudgment == "1"] selected[/#if]>${message("质量问题")}</option>
							<option value="0" [#if aftersaleJudgment.afterJudgment == "0"] selected[/#if]>${message("非质量问题")}</option>
						</select>
					</td>
					[#--二级分类--]
					<td class="qualityIssue" style="display:none">
						<select name="qualityIssue" class="text qualityIssue" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualityIssue == "1"] selected[/#if]>${message("制造责任")}</option>
							<option value="2" [#if aftersaleJudgment.qualityIssue == "2"] selected[/#if]>${message("产品责任")}</option>
							<option value="3" [#if aftersaleJudgment.qualityIssue == "3"] selected[/#if]>${message("故障原因不明")}</option>
						</select>
					</td>
					<td class="noQualityIssue" style="display:none">
						<select name="noQualityIssue" class="text noQualityIssue" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.noQualityIssue == "1"] selected[/#if]>${message("经销商责任")}</option>
							<option value="2" [#if aftersaleJudgment.noQualityIssue == "2"] selected[/#if]>${message("用户责任")}</option>
						</select>
					</td>
					[#--三级分类--]
					<td class="productLiability" style="display:none">
						<select name="productLiability" class="text productLiability" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.productLiability == "1"] selected[/#if]>${message("原材料")}</option>
							<option value="2" [#if aftersaleJudgment.productLiability == "2"] selected[/#if]>${message("设计")}</option>
							<option value="3" [#if aftersaleJudgment.productLiability == "3"] selected[/#if]>${message("上样")}</option>
							<option value="4" [#if aftersaleJudgment.productLiability == "4"] selected[/#if]>${message("发货")}</option>
							<option value="5" [#if aftersaleJudgment.productLiability == "5"] selected[/#if]>${message("仓储")}</option>
							<option value="6" [#if aftersaleJudgment.productLiability == "6"] selected[/#if]>${message("运输")}</option>
						</select>
					</td>
					<td class="storeDuty" style="display:none">
						<select name="storeDuty" class="text storeDuty" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.storeDuty == "1"] selected[/#if]>${message("安装")}</option>
							<option value="2" [#if aftersaleJudgment.storeDuty == "2"] selected[/#if]>${message("仓储")}</option>
							<option value="3" [#if aftersaleJudgment.storeDuty == "3"] selected[/#if]>${message("运输")}</option>
							<option value="4" [#if aftersaleJudgment.storeDuty == "4"] selected[/#if]>${message("上样")}</option>

						</select>
					</td>
					<td class="userDuty" style="display:none">
						<select name="userDuty" class="text userDuty" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.userDuty == "1"] selected[/#if]>${message("使用")}</option>
							<option value="2" [#if aftersaleJudgment.userDuty == "2"] selected[/#if]>${message("环境")}</option>
						</select>
					</td>

					<td>
						<span>占比</span>
						<input type="text" name="afterDutyOne" min="0" max="100" oninput="editQty(this)"
							   value="${aftersaleJudgment.afterDutyOne}"
							   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
						<span class="requiredField">%（必填）</span>
					</td>
				</tr>

				<tr>
					<th>
						售后判责二:
					</th>
					[#--一级分类--]
					<td>
						<select name="afterJudgment2" class="text afterJudgment2">
							<option></option>
							<option value="1" [#if aftersaleJudgment.afterJudgment2 == "1"] selected[/#if]>${message("质量问题")}</option>
							<option value="0" [#if aftersaleJudgment.afterJudgment2 == "0"] selected[/#if]>${message("非质量问题")}</option>
						</select>
					</td>
					[#--二级分类--]
					<td class="qualityIssue2" style="display:none">
						<select name="qualityIssue2" class="text qualityIssue2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualityIssue2 == "1"] selected[/#if]>${message("制造责任")}</option>
							<option value="2" [#if aftersaleJudgment.qualityIssue2 == "2"] selected[/#if]>${message("产品责任")}</option>
							<option value="3" [#if aftersaleJudgment.qualityIssue2 == "3"] selected[/#if]>${message("故障原因不明")}</option>
						</select>
					</td>
					<td class="noQualityIssue2" style="display:none">
						<select name="noQualityIssue2" class="text noQualityIssue2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.noQualityIssue2 == "1"] selected[/#if]>${message("经销商责任")}</option>
							<option value="2" [#if aftersaleJudgment.noQualityIssue2 == "2"] selected[/#if]>${message("用户责任")}</option>
						</select>
					</td>
					[#--三级分类--]
					<td class="productLiability2" style="display:none">
						<select name="productLiability2" class="text productLiability2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.productLiability2 == "1"] selected[/#if]>${message("原材料")}</option>
							<option value="2" [#if aftersaleJudgment.productLiability2 == "2"] selected[/#if]>${message("设计")}</option>
							<option value="3" [#if aftersaleJudgment.productLiability2 == "3"] selected[/#if]>${message("上样")}</option>
							<option value="4" [#if aftersaleJudgment.productLiability2 == "4"] selected[/#if]>${message("发货")}</option>
							<option value="5" [#if aftersaleJudgment.productLiability2 == "5"] selected[/#if]>${message("仓储")}</option>
							<option value="6" [#if aftersaleJudgment.productLiability2 == "6"] selected[/#if]>${message("运输")}</option>
						</select>
					</td>
					<td class="storeDuty2" style="display:none">
						<select name="storeDuty2" class="text storeDuty2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.storeDuty2 == "1"] selected[/#if]>${message("安装")}</option>
							<option value="2" [#if aftersaleJudgment.storeDuty2 == "2"] selected[/#if]>${message("仓储")}</option>
							<option value="3" [#if aftersaleJudgment.storeDuty2 == "3"] selected[/#if]>${message("运输")}</option>
							<option value="4" [#if aftersaleJudgment.storeDuty2 == "4"] selected[/#if]>${message("上样")}</option>
						</select>
					</td>
					<td class="userDuty2" style="display:none">
						<select name="userDuty2" class="text userDuty2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.userDuty2 == "1"] selected[/#if]>${message("使用")}</option>
							<option value="2" [#if aftersaleJudgment.userDuty2 == "2"] selected[/#if]>${message("环境")}</option>
						</select>
					</td>
					<td>
						<span>占比</span>
						<input type="text" name="afterDutyOne2" min="0" max="100" oninput="editQty(this)"
							   value="${aftersaleJudgment.afterDutyOne2}"
							   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
						<span>%</span>
					</td>
				</tr>

				<tr>
					<th>
						售后判责三:
					</th>
					[#--一级分类--]
					<td>
						<select name="afterJudgment3" class="text afterJudgment3">
							<option></option>
							<option value="1" [#if aftersaleJudgment.afterJudgment3 == "1"] selected[/#if]>${message("质量问题")}</option>
							<option value="0" [#if aftersaleJudgment.afterJudgment3 == "0"] selected[/#if]>${message("非质量问题")}</option>
						</select>
					</td>
					[#--二级分类--]
					<td class="qualityIssue3" style="display:none">
						<select name="qualityIssue3" class="text qualityIssue3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualityIssue3 == "1"] selected[/#if]>${message("制造责任")}</option>
							<option value="2" [#if aftersaleJudgment.qualityIssue3 == "2"] selected[/#if]>${message("产品责任")}</option>
							<option value="3" [#if aftersaleJudgment.qualityIssue3 == "3"] selected[/#if]>${message("故障原因不明")}</option>
						</select>
					</td>
					<td class="noQualityIssue3" style="display:none">
						<select name="noQualityIssue3" class="text noQualityIssue3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.noQualityIssue3 == "1"] selected[/#if]>${message("经销商责任")}</option>
							<option value="2" [#if aftersaleJudgment.noQualityIssue3 == "2"] selected[/#if]>${message("用户责任")}</option>
						</select>
					</td>
					[#--三级分类--]
					<td class="productLiability3" style="display:none">
						<select name="productLiability3" class="text productLiability3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.productLiability3 == "1"] selected[/#if]>${message("原材料")}</option>
							<option value="2" [#if aftersaleJudgment.productLiability3 == "2"] selected[/#if]>${message("设计")}</option>
							<option value="3" [#if aftersaleJudgment.productLiability3 == "3"] selected[/#if]>${message("上样")}</option>
							<option value="4" [#if aftersaleJudgment.productLiability3 == "4"] selected[/#if]>${message("发货")}</option>
							<option value="5" [#if aftersaleJudgment.productLiability3 == "5"] selected[/#if]>${message("仓储")}</option>
							<option value="6" [#if aftersaleJudgment.productLiability3 == "6"] selected[/#if]>${message("运输")}</option>
						</select>
					</td>
					<td class="storeDuty3" style="display:none">
						<select name="storeDuty3" class="text storeDuty3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.storeDuty3 == "1"] selected[/#if]>${message("安装")}</option>
							<option value="2" [#if aftersaleJudgment.storeDuty3 == "2"] selected[/#if]>${message("仓储")}</option>
							<option value="3" [#if aftersaleJudgment.storeDuty3 == "3"] selected[/#if]>${message("运输")}</option>
							<option value="4" [#if aftersaleJudgment.storeDuty3 == "4"] selected[/#if]>${message("上样")}</option>
						</select>
					</td>
					<td class="userDuty3" style="display:none">
						<select name="userDuty3" class="text userDuty3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.userDuty3 == "1"] selected[/#if]>${message("使用")}</option>
							<option value="2" [#if aftersaleJudgment.userDuty3 == "2"] selected[/#if]>${message("环境")}</option>
						</select>
					</td>
					<td>
						<span>占比</span>
						<input type="text" name="afterDutyOne3" min="0" max="100" oninput="editQty(this)"
							   value="${aftersaleJudgment.afterDutyOne3}"
							   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
						<span>%</span>
					</td>
				</tr>
			</table>

			<table class="input input-edit">
				<tr>
					<th>
						${message("故障原因补充")}:
					</th>
					<td colspan="6">
                <textarea class="text" name="sdpzOpinion" maxlength="4000"
						  style="width:875px;height:95px;">${aftersale.sdpzOpinion}</textarea>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("售后确认处理方案")}
			</div>
			<table class="input input-edit" style="margin-right: 29%;">
				<tr>
					<th>
						${message("跟进明细")}:
					</th>
					<td colspan="6">
                <textarea class="text" name="jfspOpinion" maxlength="4000"
						  style="width:845px;height:95px;">${aftersale.jfspOpinion}</textarea>
					</td>
				</tr>
				<tr name="whetherDispose" class="text whetherDispose">
					<th>
						<span class="requiredField">*</span>${message("是否处理")}:
					</th>
					<td class="whetherDispose">
						<select name="whetherDispose" class="text whetherDispose">
							<option></option>
							<option value="1" [#if aftersale.whetherDispose == "1"] selected[/#if]>${message("是")}</option>
							<option value="2" [#if aftersale.whetherDispose == "2"] selected[/#if]>${message("否")}</option>
						</select>
					</td>
					[#--  <th style="width: 20%;">
                          ${message("是否使用营销政策")}:
                      </th>
                      <td>
                          <select name="marketingPolicy" class="text marketingPolicy" style="width: 40%;">
                              <option></option>
                              <option value="1"[#if aftersale.marketingPolicy == 1] selected[/#if]>${message("是")}</option>
                              <option value="2"[#if aftersale.marketingPolicy == 2] selected[/#if]>${message("否")}</option>
                          </select>
                      </td>--]
					<th style="width: 20%;">
						${message("是否实木快理快赔")}:
					</th>
					<td>
						<select name="isKlkp" class="text isKlkp" style="width: 40%;">
							<option></option>
							[#if aftersale.fiveGdId??]
								<option value="1"[#if aftersale.isKlkp == 1] selected[/#if]>${message("是")}</option>
							[/#if]
							<option value="2"[#if aftersale.isKlkp == 2] selected[/#if]>${message("否")}</option>
						</select>
					</td>
				</tr>
				<tr class="noWhetherDispose">
					<th>
						<span class="requiredField">*</span>${message("是否退货")}:
					</th>
					<td>
						<label><input type="radio" name="isReturn" value="0"
									  [#if aftersale.isReturn==0]checked[/#if] [#if !node.name?contains("售后确认处理方案")]disabled="disabled"[/#if]/>是</label>&nbsp;&nbsp;
						<label><input type="radio" name="isReturn" value="1"
									  [#if aftersale.isReturn==1]checked[/#if] [#if !node.name?contains("售后确认处理方案")]disabled="disabled"[/#if]/>否</label>&nbsp;&nbsp;
					</td>
					<th>
						<span class="requiredField">*</span>${message("运费承担方")}:
					</th>
					<td>
						<select name="freightForwarder" class="text freightForwarder">
							<option></option>
							<option value="工厂"[#if aftersale.freightForwarder == "工厂"] selected[/#if]>${message("工厂")}</option>
							<option value="经销商"[#if aftersale.freightForwarder == "经销商"] selected[/#if]>${message("经销商")}</option>
						</select>
					</td>

				</tr>
				<tr class="noWhetherDispose">
					<th>
						<span class="requiredField">*</span>${message("处理方案")}:
					</th>
					<td colspan="7">
						<div class="fd">
							<label><input class="check js-iname text" name="fd" type="checkbox" value="全部更换"
										  [#if !node.name?contains("工厂")||aftersale.isReturn==0]disabled="disabled"[/#if]/>${message("全部更换")}
							</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label>
								<input class="check js-iname text" name="fd" type="checkbox" value="局部更换" onchange="hack(this)"
									   [#if !node.name?contains("售后确认处理方案")||aftersale.isReturn==0]disabled="disabled"[/#if]/>${message("局部更换")}
								<input type="text" class="underline acr" name="fdPartialReplacement"
									   value="${aftersale.fdPartialReplacement}" oninput="editQty(this)"
									   [#if !node.name?contains("售后确认处理方案")]disabled="disabled"[/#if] maxlength="200" readOnly/>
								<span>m2</span>
							</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label>
								<input class="check js-iname text" name="fd" type="checkbox" value="换货" onchange="hack(this)"
								/>${message("换货")}
								<input type="text" class="underline acr" name="fdReplaceGoods" value="${aftersale.fdReplaceGoods}"
									   oninput="editQty(this)"
									   maxlength="200" readOnly/>
								<span>m2</span>
							</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label>
								<input class="check js-iname text" name="fd" type="checkbox" value="退货" onchange="hack(this)"
									   [#if !node.name?contains("售后确认处理方案")||aftersale.isReturn==1]disabled="disabled"[/#if]/>${message("退货")}
								<input type="text" class="underline acr" name="fdReturnGoods" value="${aftersale.fdReturnGoods}"
									   oninput="editQty(this)" [#if !node.name?contains("售后确认处理方案")]disabled="disabled"[/#if]
									   maxlength="200" readOnly/>
								<span>m2</span>
							</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label>
								<input class="check js-iname text" name="fd" type="checkbox" value="赔偿" onchange="hack(this)"
									   [#if !node.name?contains("售后确认处理方案")]disabled="disabled"[/#if]/>${message("赔偿")}
								<input type="hidden" class="underline" name="fdCompensation" value="${aftersale.fdCompensation}"
									   oninput="editQty(this)" [#if !node.name?contains("售后确认处理方案")]disabled="disabled"[/#if]
									   maxlength="200" readOnly/>
								<span>：赔偿用户</span>
								<input type="text" class="underline" name="compensation" value="${aftersale.compensation}"
									   oninput="editQty(this)" [#if !node.name?contains("售后确认处理方案")]disabled="disabled"[/#if]
									   maxlength="200" readOnly/>
								<span>元，其他费用</span>
								<input type="text" class="underline" name="extraCharges" value="${aftersale.extraCharges}"
									   oninput="editQty(this)" [#if !node.name?contains("售后确认处理方案")]disabled="disabled"[/#if]
									   maxlength="200" readOnly/>
								<span>元</span>
							</label>
							<input type="hidden" class="acreage" name="factoryCompensation"
								   value="${aftersale.factoryCompensation}"/>
						</div>
					</td>
				</tr>

				<table class="input input-edit noMarketingPolicy" style="width:100%;margin-top:5px;display:none; ">
					<tr>
						<th style="width: 20px;"><span class="requiredField">*</span>${message("审批附件")}:</th>
						<td>
							[#if node.name?contains("售后确认处理方案")]
								<a href="javascript:;" id="addCollectionAttach" class="button">添加附件</a>
							[/#if]
						</td>
					</tr>
				</table>

				<div class="noMarketingPolicy" style="display:none;">
					<span class="title-style fujian">${message("营销政策附件")}:</span>
					<span class="toPreviewBtn button">浏览全图</span>
					<span class="bothDownload button">批量下载</span>
					<table id="table-collectionAttach" style="width:850px"></table>
				</div>
			</table>
			<div class="title-style">${message("客服经理审批")}:
			</div>
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>${message("责任判定")}:
					</th>
					<td>
						<select id="pzzj" onchange="jfspChange();" class="text freightForwarder" style="width: 50%;">
							<option value="0" [#if aftersale.isQuality==1]selected[/#if]>质量问题</option>
							<option value="1" [#if aftersale.isQuality==0 && aftersale.isDeal==1]selected[/#if]>
								非质量问题,公司处理
							</option>
							<option value="2" [#if aftersale.isQuality==0 && aftersale.isDeal==0]selected[/#if]>
								非质量问题,公司不处理
							</option>
						</select>
						<input id="isDeal" name="isDeal" type="hidden" value="${aftersale.isDeal}"/>
						<input id="isQuality" name="isQuality" type="hidden" value="${aftersale.isQuality}"/>
					</td>
				</tr>
				<tr>
					<th>
						${message("客服经理意见")}:
					</th>
					<td colspan="7">
                <textarea class="text" name="kfjlOpinion" maxlength="4000"
						  style="width:800px;height:95px;">${aftersale.kfjlOpinion}</textarea>
					</td>
				</tr>
			</table>
				<div class="title-style">${message("订单部核价")}:</div>
				<table class="input input-edit">
					<tr>
						<th>
							<span class="requiredField">*</span>${message("分销价(元)")}:
						</th>
						<td>
							<div class="nums-input ov">
								<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button"
									   onclick="cjadd(this)"/>
								<input id="factoryPrice" kid="CJ" value="${aftersale.factoryPrice}" name="factoryPrice"
									   class="t" mindata="0" oninput="editjisuan(this,event)"
									   onpropertychange="editjisuan(this,event)" type="text"/>
								<input class="b increase" value="+" onmousedown="increase(this,event)" type="button"
									   onclick="cjadd(this)"/>
							</div>
							<!--<input type="text" name="factoryPrice" value="${aftersale.factoryPrice}" class="text" oninput="editQty(this)"  maxlength="200"  btn-fun="clear" />-->
						</td>
						<th>
							<span class="requiredField">*</span>${message("合计费用(元)")}:
						</th>
						<td>
							<input type="text" name="amount" value="${aftersale.amount}" class="text" oninput="editQty(this)"
								   maxlength="200" btn-fun="clear" readonly/>
						</td>
					</tr>
				</table>
			<div class="title-style">${message("质量总监")}

			</div>

			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>售后判责一:
					</th>
					[#--一级分类--]
					<td>
						<select name="qualitySumJudgment" class="text qualitySumJudgment">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumJudgment == "1"] selected[/#if]>${message("质量问题")}</option>
							<option value="0" [#if aftersaleJudgment.qualitySumJudgment == "0"] selected[/#if]>${message("非质量问题")}</option>
						</select>
					</td>
					[#--二级分类--]
					<td class="qualitySumIssue" style="display:none">
						<select name="qualitySumIssue" class="text qualitySumIssue" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumIssue == "1"] selected[/#if]>${message("制造责任")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumIssue == "2"] selected[/#if]>${message("产品责任")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumIssue == "3"] selected[/#if]>${message("故障原因不明")}</option>
						</select>
					</td>
					<td class="noQualitySumIssue" style="display:none">
						<select name="noQualitySumIssue" class="text noQualitySumIssue" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.noQualitySumIssue == "1"] selected[/#if]>${message("经销商责任")}</option>
							<option value="2" [#if aftersaleJudgment.noQualitySumIssue == "2"] selected[/#if]>${message("用户责任")}</option>
						</select>
					</td>
					[#--三级分类--]
					<td class="qualitySumProductLiability" style="display:none">
						<select name="qualitySumProductLiability" class="text qualitySumProductLiability" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumProductLiability == "1"] selected[/#if]>${message("原材料")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumProductLiability == "2"] selected[/#if]>${message("设计")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumProductLiability == "3"] selected[/#if]>${message("上样")}</option>
							<option value="4" [#if aftersaleJudgment.qualitySumProductLiability == "4"] selected[/#if]>${message("发货")}</option>
							<option value="5" [#if aftersaleJudgment.qualitySumProductLiability == "5"] selected[/#if]>${message("仓储")}</option>
							<option value="6" [#if aftersaleJudgment.qualitySumProductLiability == "6"] selected[/#if]>${message("运输")}</option>
						</select>
					</td>
					<td class="qualitySumStoreDuty" style="display:none">
						<select name="qualitySumStoreDuty" class="text qualitySumStoreDuty" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumStoreDuty == "1"] selected[/#if]>${message("安装")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumStoreDuty == "2"] selected[/#if]>${message("仓储")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumStoreDuty == "3"] selected[/#if]>${message("运输")}</option>
							<option value="4" [#if aftersaleJudgment.qualitySumStoreDuty == "4"] selected[/#if]>${message("上样")}</option>
						</select>
					</td>
					<td class="qualitySumUserDuty" style="display:none">
						<select name="qualitySumUserDuty" class="text qualitySumUserDuty" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumUserDuty == "1"] selected[/#if]>${message("使用")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumUserDuty == "2"] selected[/#if]>${message("环境")}</option>
						</select>
					</td>

					<td>
						<span>占比</span>
						<input type="text" name="qualitySumDutyOne" min="0" max="100" oninput="editQty(this)"
							   value="${aftersaleJudgment.qualitySumDutyOne}"
							   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
						<span class="requiredField">%（必填）</span>
					</td>
				</tr>

				<tr>
					<th>
						售后判责二:
					</th>
					[#--一级分类--]
					<td>
						<select name="qualitySumJudgment2" class="text qualitySumJudgment2">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumJudgment2 == "1"] selected[/#if]>${message("质量问题")}</option>
							<option value="0" [#if aftersaleJudgment.qualitySumJudgment2 == "0"] selected[/#if]>${message("非质量问题")}</option>
						</select>
					</td>
					[#--二级分类--]
					<td class="qualitySumIssue2" style="display:none">
						<select name="qualitySumIssue2" class="text qualitySumIssue2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumIssue2 == "1"] selected[/#if]>${message("制造责任")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumIssue2 == "2"] selected[/#if]>${message("产品责任")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumIssue2 == "3"] selected[/#if]>${message("故障原因不明")}</option>
						</select>
					</td>
					<td class="noQualitySumIssue2" style="display:none">
						<select name="noQualitySumIssue2" class="text noQualitySumIssue2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.noQualitySumIssue2 == "1"] selected[/#if]>${message("经销商责任")}</option>
							<option value="2" [#if aftersaleJudgment.noQualitySumIssue2 == "2"] selected[/#if]>${message("用户责任")}</option>
						</select>
					</td>
					[#--三级分类--]
					<td class="qualitySumProductLiability2" style="display:none">
						<select name="qualitySumProductLiability2" class="text qualitySumProductLiability2"
								style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumProductLiability2 == "1"] selected[/#if]>${message("原材料")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumProductLiability2 == "2"] selected[/#if]>${message("设计")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumProductLiability2 == "3"] selected[/#if]>${message("上样")}</option>
							<option value="4" [#if aftersaleJudgment.qualitySumProductLiability2 == "4"] selected[/#if]>${message("发货")}</option>
							<option value="5" [#if aftersaleJudgment.qualitySumProductLiability2 == "5"] selected[/#if]>${message("仓储")}</option>
							<option value="6" [#if aftersaleJudgment.qualitySumProductLiability2 == "6"] selected[/#if]>${message("运输")}</option>
						</select>
					</td>
					<td class="qualitySumStoreDuty2" style="display:none">
						<select name="qualitySumStoreDuty2" class="text qualitySumStoreDuty2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumStoreDuty2 == "1"] selected[/#if]>${message("安装")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumStoreDuty2 == "2"] selected[/#if]>${message("仓储")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumStoreDuty2 == "3"] selected[/#if]>${message("运输")}</option>
							<option value="4" [#if aftersaleJudgment.qualitySumStoreDuty2 == "4"] selected[/#if]>${message("上样")}</option>
						</select>
					</td>
					<td class="qualitySumUserDuty2" style="display:none">
						<select name="qualitySumUserDuty2" class="text qualitySumUserDuty2" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumUserDuty2 == "1"] selected[/#if]>${message("使用")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumUserDuty2 == "2"] selected[/#if]>${message("环境")}</option>
						</select>
					</td>

					<td>
						<span>占比</span>
						<input type="text" name="qualitySumDutyOne2" min="0" max="100" oninput="editQty(this)"
							   value="${aftersaleJudgment.qualitySumDutyOne2}"
							   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
						<span>%</span>
					</td>
				</tr>

				<tr>
					<th>
						售后判责三:
					</th>
					[#--一级分类--]
					<td>
						<select name="qualitySumJudgment3" class="text qualitySumJudgment3">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumJudgment3 == "1"] selected[/#if]>${message("质量问题")}</option>
							<option value="0" [#if aftersaleJudgment.qualitySumJudgment3 == "0"] selected[/#if]>${message("非质量问题")}</option>
						</select>
					</td>
					[#--二级分类--]
					<td class="qualitySumIssue3" style="display:none">
						<select name="qualitySumIssue3" class="text qualitySumIssue3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumIssue3 == "1"] selected[/#if]>${message("制造责任")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumIssue3 == "2"] selected[/#if]>${message("产品责任")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumIssue3 == "3"] selected[/#if]>${message("故障原因不明")}</option>
						</select>
					</td>
					<td class="noQualitySumIssue3" style="display:none">
						<select name="noQualitySumIssue3" class="text noQualitySumIssue3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.noQualitySumIssue3 == "1"] selected[/#if]>${message("经销商责任")}</option>
							<option value="2" [#if aftersaleJudgment.noQualitySumIssue3 == "2"] selected[/#if]>${message("用户责任")}</option>
						</select>
					</td>
					[#--三级分类--]
					<td class="qualitySumProductLiability3" style="display:none">
						<select name="qualitySumProductLiability3" class="text qualitySumProductLiability3"
								style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumProductLiability3 == "1"] selected[/#if]>${message("原材料")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumProductLiability3 == "2"] selected[/#if]>${message("设计")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumProductLiability3 == "3"] selected[/#if]>${message("上样")}</option>
							<option value="4" [#if aftersaleJudgment.qualitySumProductLiability3 == "4"] selected[/#if]>${message("发货")}</option>
							<option value="5" [#if aftersaleJudgment.qualitySumProductLiability3 == "5"] selected[/#if]>${message("仓储")}</option>
							<option value="6" [#if aftersaleJudgment.qualitySumProductLiability3 == "6"] selected[/#if]>${message("运输")}</option>
						</select>
					</td>
					<td class="qualitySumStoreDuty3" style="display:none">
						<select name="qualitySumStoreDuty3" class="text qualitySumStoreDuty3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumStoreDuty3 == "1"] selected[/#if]>${message("安装")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumStoreDuty3 == "2"] selected[/#if]>${message("仓储")}</option>
							<option value="3" [#if aftersaleJudgment.qualitySumStoreDuty3 == "3"] selected[/#if]>${message("运输")}</option>
							<option value="4" [#if aftersaleJudgment.qualitySumStoreDuty3 == "4"] selected[/#if]>${message("上样")}</option>
						</select>
					</td>
					<td class="qualitySumUserDuty3" style="display:none">
						<select name="qualitySumUserDuty3" class="text qualitySumUserDuty3" style="display:none">
							<option></option>
							<option value="1" [#if aftersaleJudgment.qualitySumUserDuty3 == "1"] selected[/#if]>${message("使用")}</option>
							<option value="2" [#if aftersaleJudgment.qualitySumUserDuty3 == "2"] selected[/#if]>${message("环境")}</option>
						</select>
					</td>

					<td>
						<span>占比</span>
						<input type="text" name="qualitySumDutyOne3" min="0" max="100" oninput="editQty(this)"
							   value="${aftersaleJudgment.qualitySumDutyOne3}"
							   style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
						<span>%</span>
					</td>
				</tr>
			</table>

			<table class="input input-edit">
				<tr>
					<th>
						${message("故障原因补充")}:
					</th>
					<td colspan="7">
                <textarea class="text" name="pzzjOpinion" maxlength="4000"
						  style="width:800px;height:95px;">${aftersale.pzzjOpinion}</textarea>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("经销商处理信息及附件")}:</div>
			<table class="input input-edit">

				<tr>
					<th>
						<a href="/resources/template/aftersale/clqrd.doc">${message("售后处理确认单(模版下载)")}:</a>
					</th>
					<td>
						[#if node.name?contains("经销商处理")]
							<a href="javascript:;" id="addCoupleBackAttach" class="button">添加附件</a>
						[/#if]
					</td>
					<th>
						<a href="/resources/template/aftersale/xys.doc">${message("售后协议书(模版下载)")}:</a>
					</th>
					<td>
						[#if node.name?contains("经销商处理")]
							<a href="javascript:;" id="addAgreementAttach" class="button">添加附件</a>
						[/#if]
					</td>
					<th>${message("收据和转证凭证")}:</th>
					<td>
						[#if node.name?contains("经销商处理")]
							<a href="javascript:;" id="addQuittanceAttach" class="button">添加附件</a>
						[/#if]
					</td>
					<th>
						<a href="/resources/template/aftersale/thsqb.doc">${message("退货申请单(模版下载)")}:</a>
					</th>
					<td>
						[#if node.name?contains("经销商处理")]
							<a href="javascript:;" id="addReturnsAttach" class="button">添加附件</a>
						[/#if]
					</td>
				</tr>
			</table>
			<div>
				<span class="title-style fujian">${message("售后处理反馈附件")}:</span>
				<span class="toPreviewBtn button">浏览全图</span>
				<span class="bothDownload button">批量下载</span>
				<table id="table-coupleBackAttach" style="width:850px"></table>

				<span class="title-style fujian">${message("售后协议书")}:</span>
				<span class="toPreviewBtn button">浏览全图</span>
				<span class="bothDownload button">批量下载</span>
				<table id="table-agreementAttach" style="width:850px"></table>

				<span class="title-style fujian">${message("收据")}:</span>
				<span class="toPreviewBtn button">浏览全图</span>
				<span class="bothDownload button">批量下载</span>
				<table id="table-quittanceAttach" style="width:850px"></table>

				<span class="title-style fujian">${message("退货申请单")}:</span>
				<span class="toPreviewBtn button">浏览全图</span>
				<span class="bothDownload button">批量下载</span>
				<table id="table-returnsAttach" style="width:850px"></table>
			</div>
			<div class="title-style">${message("售后回访")}:</div>
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>${message("回访状态")}:
					</th>
					<td>
						<select name="reviewStatus" class="text reviewStatus">
							<option></option>
							<option value="通过"[#if aftersale.reviewStatus == "通过"] selected[/#if]>${message("通过")}</option>
							<option value="不通过"[#if aftersale.reviewStatus == "不通过"] selected[/#if]>${message("不通过")}</option>
						</select>
					</td>
					<th>
						<span class="requiredField">*</span>${message("回访结果")}:
					</th>
					<td>
						<input type="text" name="reviewResult" value="${aftersale.reviewResult}" class="text" maxlength="200"
							   btn-fun="clear"/>
					</td>
					<th>
						${message("回访时间")}:
					</th>
					<td>
						<input type="text" name="reviewDate" value="${aftersale.reviewDate}"
							   class="text predictConstructionTime" maxlength="200"
							   onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:true});"/>
					</td>
					<th>
						<span class="requiredField">*</span>${message("处理满意度")}:
					</th>
					<td>
						<select name="satisfaction" class="text satisfaction">
							<option></option>
							<option value="1"[#if aftersale.satisfaction == "1"] selected[/#if]>${message("非常满意")}</option>
							<option value="2"[#if aftersale.satisfaction == "2"] selected[/#if]>${message("满意")}</option>
							<option value="3"[#if aftersale.satisfaction == "3"] selected[/#if]>${message("一般")}</option>
							<option value="4"[#if aftersale.satisfaction == "4"] selected[/#if]>${message("不满意")}</option>
						</select>
					</td>
				</tr>
				<tr>
					<th>
						${message("冲账金额")}:
					</th>
					<td>
						<input type="text" class="text " name="depositRechargeAmount" value="${aftersale.depositRechargeAmount}"
							   oninput="editQty(this)" maxlength="200"/>
					</td>
					<th>
						<span class="requiredField">*</span>${message("经营组织")}:
					</th>
					<td>
	                  <span class="search" style="position:relative">
	                   <input type="hidden" name="organization.id" class="text organizationId"
							  value="${aftersale.organization.id}" btn-fun="clear"/>
	                   <input type="text" class="text organizationName" value="${aftersale.organization.name}"
							  maxlength="200" onkeyup="clearSelect(this)" readOnly/>
	                   <input type="button" class="iconSearch" value="" id="openOrganization">
	                  </span>
					</td>

				</tr>
				<tr>
					<th>
						${message("冲账备注")}:
					</th>
					<td colspan="5">
                <textarea class="text" name="depositRechargeMemo"
						  style="width:100%;height:80px;">${aftersale.depositRechargeMemo}</textarea>
					</td>
				</tr>

			</table>
			<div class="title-style">${message("经销商提交物流单")}:</div>
			<table class="input input-edit">
				<tr>
					<th>${message("退货物流单")}:</th>
					<td>
						[#if node.name?contains("提交物流单")]
							<a href="javascript:;" id="addReturnsLogisticsAttach" class="button">添加附件</a>
						[/#if]
					</td>
				</tr>
			</table>
			<div>
				<span class="title-style fujian">${message("退货物流单")}:</span>
				<span class="toPreviewBtn button">浏览全图</span>
				<span class="bothDownload button">批量下载</span>
				<table id="table-returnsLogisticsAttach" style="width:850px"></table>
			</div>
	</div>
	<div class="fixed-top">
		[#--[#if aftersale.wfId == null ]
			[#if aftersale.invoiceStatus != 99]
				<a id="shengheButton" class="iconButton" onclick="check_wf(this)"><span
							class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
				<input type="button" onclick="close_e(this)" class="button cancleButton" value="${message("作废")}"/>
			[/#if]
		[#else]
			<input type="button" onclick="update_time(this)" class="button cancleButton" value="${message("更新时间")}"/>
		[/#if]
		[#if (aftersale.wfId == null && aftersale.invoiceStatus != 99) || appointRejectFlag ]
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}"/>
		[/#if]--]
		<input type="button" onclick="location.reload(true);" class="button refreshButton ml15"
			   value="${message("刷新")}">
	</div>
</form>

<!-- 全屏看图结构 -->
<div class="preview_swiperBox">
	<div id="picture-swiperLeft" class="swiperButton swiper-button-prev"></div>
	<div id="picture-swiperRight" class="swiperButton swiper-button-next"></div>
	<div id="picture-colse" class="swiper-button-close">
		<span></span>
		<span></span>
	</div>
	<div class="preview_control">
		<div id="scaleBig" class="scaleBig"><span></span></div>
		<div id="scaleSmall" class="scaleSmall"><span></span></div>
		<div id="previewReset" class="previewReset"><span></span></div>
		<div id="rotateLeft" class="rotateLeft"><span></span></div>
		<div id="rotateRight" class="rotateRight"><span></span></div>
	</div>
</div>
<!-- 全屏看图结构 end-->

<div id="wf_area" style="width:100%"></div>
</body>
</html>