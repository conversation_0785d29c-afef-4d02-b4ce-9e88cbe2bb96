<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <title>${message("新增sbu")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css"/>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js?version=0.03"></script>
    <script type="text/javascript" src="/resources/js/base/file.js?version=0.03"></script>
    <script type="text/javascript" src="/resources/js/base/textarea.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <link href="/resources/css/preview-swiper.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/resources/js/reference/preview-swiper.min.js"></script>
    <script type="text/javascript" src="/resources/js/base/component.js"></script>

    <style>
        .underline {
            width: 80px;
            border-bottom: 1px solid #dcdcdc;
            text-align: center;
        }
    </style>
    <script type="text/javascript">
        $(function () {
            $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off');
        });

        function editQty(t, e) {
            extractNumber(t, 6, false, e);
        }

        $().ready(function () {
            var $inputForm = $("#inputForm");
            var $openAzdh = $("#openAzdh");
            var $addProducts = $("#openProduct");
            var $areaId = $("#areaId");
            var $newAreaId = $("#newAreaId");
            var $bAreaId = $("#bAreaId");
            var $layingArea = $("input[name='layingArea']").val();
            var $price = $("input[name='price']").val();
            $(".totalSales").html(currency(accMul($layingArea, $price), true));
            // 表单验证
            $inputForm.validate({
                rules: {
                    grade: "required",
                    area: "required",
                    name: "required",
                    contactNumber: "required",
                    isCheckIn: "required",
//				checkInTime: "required",
                    downTime: "required",
                    layingTime: "required",
                    layingArea: "required",
                    layingName: "required",
//				layingMethod: "required",
                    layingMoistureproof: "required",
                    heatingSystem: "required",
//				otherInformation: "required",
                    decorationTime: "required",
//				emergencyDegree: "required",
//				floorFlatness: "required",
//				floorMoistureContent: "required",
//				groundMoistureContent: "required",
                    batchNumber: "required",
                    price: {
                        min: 1,
                        required: true,
                    },
                    totalSales: "required",
//				airHumidity: "required",
//				processingStatus: "required",
                    faultName: "required",
                    zbsurveyorName: "required",
                    zbsurveyorPhone: "required",
                    storeFaultMessage: "required",
                    layingWay: "required",
                    layingAddress: "required",
                    businessTypeId: "required",
                    formType:"required"
                },
                submitHandler: function (form) {
                    return false;
                }
            });
            // 表单验证
            $.validator.addClassRules({
                oaSn: {
                    required: true
                }
            });

            //地区选择
            $areaId.lSelect();
            $bAreaId.lSelect();
            $newAreaId.lSelect();

            //销售合同
            var salesContractAttachIndex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    align: 'center',
                    width: 400,
                    renderer: function (val, item, rowIndex, obj) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['salesContractAttachs[' + salesContractAttachIndex + '].url'] = url;
                        hideValues['salesContractAttachs[' + salesContractAttachIndex + '].suffix'] = fileObj.suffix;

                        return createFileStr({
                            url: url,
                            fileName: fileObj.file_name,
                            name: fileObj.name,
                            suffix: fileObj.suffix,
                            time: '',
                            textName: 'salesContractAttachs[' + salesContractAttachIndex + '].name',
                            hideValues: hideValues
                        });
                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    align: 'center',
                    width: 500,
                    renderer: function (val, item, rowIndex, obj) {
                        return '<div><textarea class="text file_memo" name="salesContractAttachs[' + salesContractAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
                        salesContractAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $salesContractAttachmGrid = $('#table-salesContractAttach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addSalesContractAttach = $("#addSalesContractAttach");
            var attachIdnex = 0;
            var option1 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $salesContractAttachmGrid.addRow(row, null, 1);
                    }
                }
            }
            $addSalesContractAttach.file_upload(option1);

            //验收单
            var acceptanceSheetAttachIndex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    align: 'center',
                    width: 400,
                    renderer: function (val, item, rowIndex, obj) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].url'] = url;
                        hideValues['acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].suffix'] = fileObj.suffix;

                        return createFileStr({
                            url: url,
                            fileName: fileObj.file_name,
                            name: fileObj.name,
                            suffix: fileObj.suffix,
                            time: item.create_date,
                            textName: 'acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].name',
                            hideValues: hideValues
                        });
                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    align: 'center',
                    width: 500,
                    renderer: function (val, item, rowIndex, obj) {
                        return '<div><textarea class="text file_memo" name="acceptanceSheetAttachs[' + acceptanceSheetAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
                        acceptanceSheetAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $acceptanceSheetAttachmGrid = $('#table-acceptanceSheetAttach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addAcceptanceSheetAttach = $("#addAcceptanceSheetAttach");
            var attachIdnex = 0;
            var option7 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $acceptanceSheetAttachmGrid.addRow(row, null, 1);
                    }
                }
            }
            $addAcceptanceSheetAttach.file_upload(option7);

            //现场照片
            var scenePicturesAttachIndex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    align: 'center',
                    width: 400,
                    renderer: function (val, item, rowIndex, obj) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['scenePicturesAttachs[' + scenePicturesAttachIndex + '].url'] = url;
                        hideValues['scenePicturesAttachs[' + scenePicturesAttachIndex + '].suffix'] = fileObj.suffix;

                        return createFileStr({
                            url: url,
                            fileName: fileObj.file_name,
                            name: fileObj.name,
                            suffix: fileObj.suffix,
                            time: item.create_date,
                            textName: 'scenePicturesAttachs[' + scenePicturesAttachIndex + '].name',
                            hideValues: hideValues
                        });
                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    align: 'center',
                    width: 500,
                    renderer: function (val, item, rowIndex, obj) {
                        return '<div><textarea class="text file_memo" name="scenePicturesAttachs[' + scenePicturesAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
                        scenePicturesAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $scenePicturesAttachmGrid = $('#table-scenePicturesAttach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addScenePicturesAttach = $("#addScenePicturesAttach");
            var attachIdnex = 0;
            var option8 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $scenePicturesAttachmGrid.addRow(row, null, 1);
                    }
                }
            }
            $addScenePicturesAttach.file_upload(option8);

            //勘察图片附件
            var surveyorAttachIndex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    align: 'center',
                    width: 400,
                    renderer: function (val, item, rowIndex, obj) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['surveyorAttachs[' + surveyorAttachIndex + '].url'] = url;
                        hideValues['surveyorAttachs[' + surveyorAttachIndex + '].suffix'] = fileObj.suffix;

                        return createFileStr({
                            url: url,
                            fileName: fileObj.file_name,
                            name: fileObj.name,
                            suffix: fileObj.suffix,
                            time: '',
                            textName: 'surveyorAttachs[' + surveyorAttachIndex + '].name',
                            hideValues: hideValues
                        });
                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    align: 'center',
                    width: 500,
                    renderer: function (val, item, rowIndex, obj) {
                        return '<div><textarea class="text file_memo" name="surveyorAttachs[' + surveyorAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
                        surveyorAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $surveyorAttachmGrid = $('#table-surveyorAttach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addSurveyorAttach = $("#addSurveyorAttach");
            var attachIdnex = 0;
            var option2 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $surveyorAttachmGrid.addRow(row, null, 1);
                    }
                }
            }
            $addSurveyorAttach.file_upload(option2);

            //售后处理反馈附件
            var coupleBackAttachIndex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    align: 'center',
                    width: 400,
                    renderer: function (val, item, rowIndex, obj) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['coupleBackAttachs[' + coupleBackAttachIndex + '].url'] = url;
                        hideValues['coupleBackAttachs[' + coupleBackAttachIndex + '].suffix'] = fileObj.suffix;

                        return createFileStr({
                            url: url,
                            fileName: fileObj.file_name,
                            name: fileObj.name,
                            suffix: fileObj.suffix,
                            time: '',
                            textName: 'coupleBackAttachs[' + coupleBackAttachIndex + '].name',
                            hideValues: hideValues
                        });
                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    align: 'center',
                    width: 500,
                    renderer: function (val, item, rowIndex, obj) {
                        return '<div><textarea class="text file_memo" name="coupleBackAttachs[' + coupleBackAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
                        coupleBackAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $coupleBackAttachmGrid = $('#table-coupleBackAttach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addCoupleBackAttach = $("#addCoupleBackAttach");
            var attachIdnex = 0;
            var option3 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $coupleBackAttachmGrid.addRow(row, null, 1);
                    }
                }
            }
            $addCoupleBackAttach.file_upload(option3);

            //售后协议书附件
            var agreementAttachIndex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    align: 'center',
                    width: 400,
                    renderer: function (val, item, rowIndex, obj) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['agreementAttachs[' + agreementAttachIndex + '].url'] = url;
                        hideValues['agreementAttachs[' + agreementAttachIndex + '].suffix'] = fileObj.suffix;

                        return createFileStr({
                            url: url,
                            fileName: fileObj.file_name,
                            name: fileObj.name,
                            suffix: fileObj.suffix,
                            time: '',
                            textName: 'agreementAttachs[' + agreementAttachIndex + '].name',
                            hideValues: hideValues
                        });
                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    align: 'center',
                    width: 500,
                    renderer: function (val, item, rowIndex, obj) {
                        return '<div><textarea class="text file_memo" name="agreementAttachs[' + agreementAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
                        agreementAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $agreementAttachmGrid = $('#table-agreementAttach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addAgreementAttach = $("#addAgreementAttach");
            var attachIdnex = 0;
            var option4 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $agreementAttachmGrid.addRow(row, null, 1);
                    }
                }
            }
            $addAgreementAttach.file_upload(option4);

            //收据附件
            var quittanceAttachIndex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    align: 'center',
                    width: 400,
                    renderer: function (val, item, rowIndex, obj) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['quittanceAttachs[' + quittanceAttachIndex + '].url'] = url;
                        hideValues['quittanceAttachs[' + quittanceAttachIndex + '].suffix'] = fileObj.suffix;

                        return createFileStr({
                            url: url,
                            fileName: fileObj.file_name,
                            name: fileObj.name,
                            suffix: fileObj.suffix,
                            time: '',
                            textName: 'quittanceAttachs[' + quittanceAttachIndex + '].name',
                            hideValues: hideValues
                        });
                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    align: 'center',
                    width: 500,
                    renderer: function (val, item, rowIndex, obj) {
                        return '<div><textarea class="text file_memo" name="quittanceAttachs[' + quittanceAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
                        quittanceAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $quittanceAttachmGrid = $('#table-quittanceAttach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addQuittanceAttach = $("#addQuittanceAttach");
            var attachIdnex = 0;
            var option5 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $quittanceAttachmGrid.addRow(row, null, 1);
                    }
                }
            }
            $addQuittanceAttach.file_upload(option5);

            //退货申请单附件
            var returnsAttachIndex = 0;
            var cols = [
                {
                    title: '${message("附件")}',
                    name: 'content',
                    align: 'center',
                    width: 400,
                    renderer: function (val, item, rowIndex, obj) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['returnsAttachs[' + returnsAttachIndex + '].url'] = url;
                        hideValues['returnsAttachs[' + returnsAttachIndex + '].suffix'] = fileObj.suffix;

                        return createFileStr({
                            url: url,
                            fileName: fileObj.file_name,
                            name: fileObj.name,
                            suffix: fileObj.suffix,
                            time: '',
                            textName: 'returnsAttachs[' + returnsAttachIndex + '].name',
                            hideValues: hideValues
                        });
                    }
                },
                {
                    title: '${message("备注")}',
                    name: 'memo',
                    align: 'center',
                    width: 500,
                    renderer: function (val, item, rowIndex, obj) {
                        return '<div><textarea class="text file_memo" name="returnsAttachs[' + returnsAttachIndex + '].memo" style="width:94%;height: 40px">' + val + '</textarea></div>';
                    }
                },
                {
                    title: '${message("操作")}', align: 'center', renderer: function (val, item, rowIndex, obj) {
                        returnsAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }
                }
            ];
            var $returnsAttachmGrid = $('#table-returnsAttach').mmGrid({
                fullWidthRows: true,
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });
            var $addReturnsAttach = $("#addReturnsAttach");
            var attachIdnex = 0;
            var option6 = {
                dataType: "json",
                uploadToFileServer: true,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i].file_info;
                        $returnsAttachmGrid.addRow(row, null, 1);
                    }
                }
            }
            $addReturnsAttach.file_upload(option6);

            $("form").bindAttribute({
                isConfirm: true,
                callback: function (resultMsg) {
                    $.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
                        location.href = '/aftersales/aftersale/edit.jhtml?id=' + resultMsg.objx;
                    });
                }
            });

            var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function () {
                var $this = $(this);
                $this.closest("tr").remove();
            });

            //查询合同
            $("#selectContract").click(function(){
                //var sbuId = $(".sbuId").val();
                var storeId = $(".storeId").val();
                if(storeId==""){
                    $.message_alert('请选择客户');
                    return false;
                }
                $("#openStore").bindQueryBtn({
                    type:'store',
                    bindClick:false,
                    title:'${message("查询合同")}',
                    url: '/b2b/customerContract/select_contract.jhtml?storeId=' + storeId + '&aft=1',
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $("input[name='customerContractId']").attr("value",row.id);
                            $("input[name='contractSn']").attr("value",row.sn);
                            $("input[name='contractName']").attr("value",row.contract_name);
                            $("input[name='storeMemberId']").attr("value",row.store_member_id);
                            $("input[name='storeMemberName']").attr("value",row.store_member_name);
                            $("input[name='contacts']").attr("value",row.contacts);
                        }
                    }
                });
            })

            //查询客户
            $("#selectStore").bindQueryBtn({
                type: 'store',
                title: '${message("查询客户")}',
                url: '/member/store/select_store.jhtml?type=distributor&isMember=1'
            });


            $addProducts.click(function () {
                $addProducts.bindQueryBtn({
                    type: 'product',
                    bindClick: false,
                    title: '${message("查询产品")}',
                    url: '/product/product/selectProduct.jhtml?srcPage=1',
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var id = rows[0].id;
                            var name = rows[0].name;
                            var model = rows[0].model;
                            var spec = rows[0].spec;
                            var productCategory = "";
                            var productDescription = rows[0].description;
                            var productVonderCode = rows[0].vonder_code;
                            if (rows[0].pc_parent != null) {
                                $.ajax({
                                    url: '/product/product/find_category.jhtml?parent=' + rows[0].pc_parent,
                                    type: "post",
                                    success: function (data) {
                                        if(data.type == "success"){
                                            if (data != null) {
                                                $(".category").val(data.content);
                                                $('input[name="categoryId"]').val(data.objx);
                                            }
                                        }
                                    }
                                });
                                $.ajax({
                                    url: '/aftersales/aftersale/select_sbu_by_product_id.jhtml?productId=' + rows[0].id,
                                    type: "post",
                                    success: function (data) {
                                        if(data.type == "success"){
                                            if (data != null) {
                                                var sbu = data.objx;
                                                if(sbu.length>0){
                                                    var value = sbu[0]
                                                    var sbuName = value["name"];
                                                    var sbuId = value["id"];
                                                    $(".sbuName").val(sbuName);
                                                    $(".sbuId").val(sbuId);
                                                }else{
                                                    alert("该产品没有sbu，请重新选择产品");
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                            $(".productId").val(id);
                            $(".productName").text(name);
                            $(".productModel").val(model);
                            $(".productSpec").text(spec);
                            $(".productDescription").text(productDescription);
                            $(".productVonderCode").text(productVonderCode);
                        }
                    }
                });
            });
            $openAzdh.click(function () {
                var storeId = $(".storeId").val()
                if(storeId == null){
                    $.message_alert('请选择经销商！');
                }
                //查5期安装单号
                $openAzdh.bindQueryBtn({
                    type: 'store',
                    bindClick: false,
                    title: '${message("查询安装单")}',
                    url: '/aftersales/aftersale/select_azd.jhtml?storeId=' +  storeId,
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var row = rows[0];
                            $('.fiveGdId').val(row.id);
                            $('.fiveGdSn').val(row.sn);
                        }
                    }
                });
            });
            $('#checkAzd').click(function () {
                var fiveGdId = $('.fiveGdId').val()
                if(fiveGdId != null){
                    var url = "/aftersales/aftersale/azd.jhtml?id=" + fiveGdId
                    parent.change_tab(2, url);
                } else{
                    $.message_alert('请选择安装单！');
                }
            });
            //查客户
            $("#openStore").bindQueryBtn({
                type: 'store',
                title: '${message("查询客户")}',
                url: '/member/store/select_store.jhtml',
                callback: function (rows) {
                    if (rows.length > 0) {
                        var row = rows[0];
                        $("input[name='storeId']").val(row.id);
                        $('.dealerName').val(row.dealer_name);
                        $('.sotreName').text(row.name);
                        $('.saleOrgId').val(row.sale_org);
                        $('#dealerPhone').val(row.head_phone);
                        $('.grantCode').text(row.grant_code);
                        $('.outTradeNo').text(row.out_trade_no);
                    }
                }
            });
            $("#business").hide();
            var c = $('.totalSales');
            $('.increase').click(function () {
                var a = $("input[name='layingArea']").val();
                var b = $("input[name='price']").val();
                var d = (accMul(a, b));
                c.html(currency(d, true));

            });
            $('.decrease').click(function () {
                var a = $("input[name='layingArea']").val();
                var b = $("input[name='price']").val();
                var d = (accMul(a, b));
                c.html(currency(d, true));

            });
            $('#price').bind('input propertychange', function () {
                var a = $("input[name='layingArea']").val();
                var b = $("input[name='price']").val();
                var d = accMul(a, b);
                c.html(currency(d, true));
            });
            $('#layingArea').bind('input propertychange', function () {
                var a = $("input[name='layingArea']").val();
                var b = $("input[name='price']").val();
                var d = accMul(a, b);
                c.html(currency(d, true));
            });
            $('#businessTypeId').change(function(){
                var businessType = $("#businessTypeId option:selected").attr("lowerCode");
                if (businessType == "businessSubconditional" || businessType == "businessSubconditional") {
                    $("#business").show();
                }else{
                    $("#business").hide();
                }
            });


            // 全屏看图功能 start
            $(".toPreviewBtn").click(function () {
                let a_href = $(this).next().children().find(".real-row .pic a")
                let listData = []
                for(var i = 0; i < a_href.length; i++) {
                    let href_url = a_href[i].href
                    let last_index = href_url.lastIndexOf('.');
                    let getPath = href_url.substring(last_index + 1, href_url.length);
                    if (getPath == "jpg" || getPath == "jpeg" || getPath == "png" || getPath == "bmp" || getPath == "gif" || getPath == "mp3" || getPath == "mp4" || getPath == "avi") {
                        let item = a_href[i].href
                        listData.push(item)
                    }
                }
                browseWholePicture(listData);
            });

            var rotateParam = 0
            var scaleParam = 1

            // 关闭全屏看图
            $("#picture-colse").click(function () {
                $(".preview_swiperBox").fadeOut(300);
                $(".swiper-container").remove();
                rotateParam = 0
                scaleParam = 1
                $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
                $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
            });
            $("#picture-swiperLeft").click(function () {
                rotateParam = 0
                scaleParam = 1
                $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
                $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
            });
            $("#picture-swiperRight").click(function () {
                rotateParam = 0
                scaleParam = 1
                $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
                $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
            });


            $("#previewReset").click(function () {
                rotateParam = 0
                scaleParam = 1
                $(".swiper-wrapper .swiper-slide-active img").css({"transform": "rotate(0deg) scale(1)", "transition": "transform .3s"})
            });
            $("#rotateLeft").click(function () {
                rotateParam-= 90
                let rotateStyle = "rotate(" + rotateParam + "deg)"
                let scaleStyle = "scale(" + scaleParam + ")"
                $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
            });
            $("#rotateRight").click(function () {
                rotateParam+= 90
                let rotateStyle = "rotate(" + rotateParam + "deg)"
                let scaleStyle = "scale(" + scaleParam + ")"
                $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
            });
            $("#scaleBig").click(function () {
                scaleParam+= 0.1
                let rotateStyle = "rotate(" + rotateParam + "deg)"
                let scaleStyle = "scale(" + scaleParam + ")"
                $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
            });
            $("#scaleSmall").click(function () {
                scaleParam-= 0.1
                let rotateStyle = "rotate(" + rotateParam + "deg)"
                let scaleStyle = "scale(" + scaleParam + ")"
                $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
            });
            // 全屏看图功能 end

        });


        var number = {
            extractNumber: function (a, len) {
                var $this = $(a).val();
                if (len != null) {
                    if ($this.length > len) {
                        var tval = $this.substring(0, len);
                        $(a).val(tval);
                    }
                }
                if (!number.isRealNum($this)) {
                    $(a).val("");
                }
            }, isRealNum: function (val) {
                if (val === "" || val == null) {
                    return false;
                }
                if (!isNaN(val)) {
                    return true;
                } else {
                    return false;
                }
            }
        }

        var string = {
            extractString:function(a,b,len){
                var $this = $(a).val();
                if(len!=null){
                    if($this.length>len){
                        var tval = $this.substring(0,len);
                        $(a).val(tval);
                    }
                }
                var re;
                if(b==0){
                    re = /[^A-Za-z0-9 ]/g;
                }else{
                    re = /[^A-Za-z0-9 \*-]/g;
                }

                if(!string.check($this,re)){
                    $(a).val("");
                }
            },check:function(val,re){
                if(val === "" || val ==null){
                    return false;
                }
                if(re.test(val)){
                    return false;
                }else{
                    return true;
                }
            }
        }

        function hack(e) {
            if ($(e).prop('checked') == true) {
                var $pdiv = $(e).next();
                $pdiv.attr("readonly", false)
                $pdiv.attr("required", true)
            } else {
                var $pdiv = $(e).next();
                $pdiv.attr("readonly", "readonly");
                $pdiv.attr("required", false)
                $pdiv.val("");
            }
        }

        function checkTel(e) {
            var tel = $(e).val();
            var mobile = /^1[3|5|7|8|9]\d{9}$/, phone = /^0\d{2,3}-?\d{7,8}$/;
            if (mobile.test(tel) || phone.test(tel)) {
                return true
            } else {
                $(e).val("");
                return false;
            }

        }


        function showLevel(e) {
            $(".level").toggle()
        }

        function check(e){
            textarea.setValue(["storeFaultMessage"]);
        }

    </script>
</head>
<body>
<div class="pathh">
    &nbsp;${message("新增售后申请")}
</div>
<form id="inputForm" onsubmit="return check(this)" action="/aftersales/aftersale/save.jhtml" method="post" type="ajax" validate-type="validate">
    <div class="tabContent">
        <table class="input input-edit">
            <input type="hidden" name="isUse" value="false"/>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("顾客姓名")}:
                </th>
                <td>
                    <input type="text" name="name" class="text" maxlength="200" btn-fun="clear"/>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("联系电话")}:
                </th>
                <td>
                    <input type="text" name="contactNumber" class="text" maxlength="200"
                           oninput="number.extractNumber(this,11)" btn-fun="clear"/>
                </td>
                <th>
                    ${message("联系电话2")}:
                </th>
                <td>
                    <input type="text" name="contactNumberTow" class="text" maxlength="200"
                           oninput="number.extractNumber(this,11)" btn-fun="clear"/>
                </td>
                <th>
                    ${message("处理状态")}:
                </th>
                <td>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("故障名称")}:
                </th>
                <td>
                    <input type="text" name="faultName" class="text" maxlength="200" value="" btn-fun="clear"/>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("购买时间")}:
                </th>
                <td>
                    <input type="text" name="decorationTime" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});" />
                </td>
                <th>
                    <span class="requiredField">*</span>${message("采暖方式")}:
                </th>
                <td>
                    <select name="heatingSystem" class="text heatingSystem">
                        <option></option>
                        <option value="无">${message("无")}</option>
                        <option value="地暖">${message("地暖")}</option>
                        <option value="电暖">${message("电暖")}</option>
                        <option value="壁暖（暖气片）">${message("壁暖（暖气片）")}</option>
                        <option value="空调（冬季）">${message("空调（冬季）")}</option>
                    </select>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("是否已入住")}:
                </th>
                <td>
                    <select name="isCheckIn" class="text isCheckIn">
                        <option></option>
                        <option value="true">${message("是")}</option>
                        <option value="false">${message("否")}</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("铺设时间")}:
                </th>
                <td>
                    <input type="text" name="layingTime" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});" />
                </td>
                <th>
                    ${message("入住时间")}:
                </th>
                <td>
                    <input type="text" name="checkInTime"  class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});" />
                </td>
                <th>
                    <span class="requiredField">*</span>${message("故障时间")}:
                </th>
                <td>
                    <input type="text" name="downTime"  class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});" />
                </td>
                <th>
                    <span class="requiredField">*</span>${message("铺设方")}:
                </th>
                <td>
                    <select name="layingName" class="text layingName">
                        <option></option>
                        <option value="经销商">${message("经销商")}</option>
                        <option value="客户安装">${message("客户安装")}</option>
                        <option value="装饰公司">${message("装饰公司")}</option>
                        <option value="家装公司">${message("家装公司")}</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("铺设防潮")}:
                </th>
                <td>
                    <select name="layingMoistureproof" class="text layingMoistureproof">
                        <option></option>
                        <option value="双重防潮">${message("双重防潮")}</option>
                        <option value="单重防潮">${message("单重防潮")}</option>
                    </select>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("铺设面积(㎡)")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button"/>
                        <input id="layingArea" class="t layingArea" name="layingArea" value="" mindata="0"
                               oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"/>
                        <input value="+" class="b increase" onmousedown="increase(this,event)" type="button"/>
                    </div>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("铺装地址")}:
                </th>
                <td colspan="3">
                    <input type="hidden" id="newAreaId" name="headNewArea.id"/>
                    <input type="text" name="layingAddress" placeholder="请输入详细地址！" class="detailed-address"
                           maxlength="200"/>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("铺设方式")}:
                </th>
                <td>
                    <select name="layingWay" class="text layingWay">
                        <option></option>
                        <option value="龙骨法">${message("龙骨法")}</option>
                        <option value="悬浮法">${message("悬浮法")}</option>
                        <option value="夹板法">${message("夹板法")}</option>
                        <option value="胶贴法">${message("胶贴法")}</option>
                        <option value="钢扣法">${message("钢扣法")}</option>
                    </select>
                </td>
                <th>
                    ${message("经销商姓名")}:
                </th>
                <td>
                    [#--  --if storeMember.memberType == 1]
                        <span class="dealerName">${store.dealerName}</span>
                        <input type="hidden" name="storeId" class="text storeId" value="${store.id}"/>
                        <input type="hidden" name="saleOrgId" class="text saleOrgId" value="${store.saleOrg.id}"/>
                    [#else--]
                    <span class="search" style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" value="${store.saleOrg.id}"/>
		                    <input type="hidden" name="storeId" class="text storeId" value="${store.id}"
                                   btn-fun="clear"/>
		                    <input type="text" class="text dealerName" maxlength="200" value="${store.dealerName}"
                                   onkeyup="clearSelect(this)" readOnly/>
		                    <input type="button" class="iconSearch" value="" id="openStore"/>
	                    </span>
                    [#-- [/#if] --]
                </td>
                <th>
                    ${message("经销商名称")}:
                </th>
                <td>
                    <span class="sotreName">${store.name}</span>
                </td>
                <th>
                    ${message("经销商电话")}:
                </th>
                <td>
                    <input type="text" name="" id="dealerPhone" class="text" maxlength="200" btn-fun="clear" readonly/>
                </td>
            </tr>
            <tr>
                <th>
                    ${message("经销商授权编码")}:
                </th>
                <td>
                    <span class="grantCode">${store.grantCode}</span>
                </td>
                <th>
                    ${message("ERP编码")}:
                </th>
                <td>
                    <span class="outTradeNo">${store.outTradeNo}</span>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("勘察人姓名")}:
                </th>
                <td>
                    <input type="text" name="zbsurveyorName" class="text" maxlength="200" btn-fun="clear"/>
                </td>

                <th>
                    <span class="requiredField">*</span>${message("联系方式")}:
                </th>
                <td>
                    <input type="text" name="zbsurveyorPhone" class="text" maxlength="200" onblur="checkTel(this)"
                           btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("工厂发货时间")}:
                </th>
                <td>
                    <input type="text" name="dateForDealersToAskForGoods" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:'true'});" />
                </td>

                <th>
                    ${message("业务类型")}:
                </th>
                <td>
                    <select id="businessTypeId" name="businessTypeId" id="businessTypeId" class="text">
                        <option value=""></option>
                        [#list businessTypes as businessType]
                            <option lowerCode="${businessType.lowerCode}"
                                    value="${businessType.id}" [#if aftersale.businessType.id==businessType.id] selected[/#if]>${businessType.value}</option>
                        [/#list]
                    </select>
                </td>
                <th>
                    ${message("安装单号")}:
                </th>
                <td>
                    <span class="search" style="position:relative">
		                    <input type="hidden" name="fiveGdId" class="text fiveGdId" value=""
                                   btn-fun="clear"/>
		                    <input type="text" class="text fiveGdSn" name="fiveGdSn" maxlength="200" value=""
                                   onkeyup="clearSelect(this)" readOnly/>
		                    <input type="button" class="iconSearch" value="" id="openAzdh"/>
	                    </span>
                </td>
                <th style="text-align: left; ">
                    <a href="javascript:void(0);" id="checkAzd" class="blue">查看</a>
                </th>
            </tr>
            <tr name="business" id="business">
                <th>${message("合同编码")}:</th>
                <td>
            		 <span class="search" style="position: relative">
            		  <input  type="hidden" name="customerContractId" class="text customerContractId" value="" btn-fun="clear" />
            		  <input type="text" name="contractSn" class="text contractSn" value=""maxlength="200" id="contractSn" onkeyup="clearSelect(this)" readOnly />
            		  <input type="button" class="iconSearch" value="" id="selectContract"></span>
                </td>

                <th>
                    ${message("合同名称")}:
                </th>
                <td>
                    <input type="text" name="contractName" id="contractName" class="text" maxlength="200" btn-fun="clear" readonly/>
                </td>
            </tr>
        </table>
        [#--        <br/>--]
        [#--		<div style="display: inline-block">--]
        [#--			<a href="javascript:void(0);" onclick="showLevel(this)" class="button">售后等级分级说明</a>--]
        [#--		</div>--]
        [#--         <div class="title-style level"
                     style="border:1px solid #dcdcdc; padding:10px 15px; font:13px 'Microsoft YaHei';">
                    <div style="color:rgb(227, 18, 25); margin:10px 0 0 0;">★售后等级分级说明</div>
                    <pre style="font:13px 'Microsoft YaHei';">
        A级：用户财产遭到重大损失或引起人身伤亡、产品被权威部门评定不合格、权威媒体已进行曝光或权威机构已介入调查。
        B级：用户投诉到媒体、消协、工商局等公众部门，或起诉到法院。产品质量可能涉及安全隐患，并对用户造成一定财产损失。
        C级：非产品质量问题，用户的行为过激对公司及品牌可能造成或已造成负面影响。
        D级：除重大投诉外的其他投诉，均视为一般性投诉。
                        </pre>
                </div> --]
        <div class="title-style">${message("产品信息")}:</div>
        <table class="input input-edit">
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("产品型号")}:
                </th>
                <td>
		                <span class="search" style="position:relative">
		                    <input type="hidden" name="productId" class="text productId" value="" btn-fun="clear"/>
		                    <input type="text" class="text productModel" value="" maxlength="200"
                                   onkeyup="clearSelect(this)" readOnly/>
		                    <input type="button" class="iconSearch" value="" id="openProduct">
		                </span>
                </td>
                <th>
                    ${message("地板品类")}:
                </th>
                <td>
                    <input type="text" name="category" class="text category" maxlength="200" btn-fun="clear" readOnly/>
                    <input type="hidden" name="categoryId" class="text" maxlength="200" btn-fun="clear" />
                </td>
                <th>
                    ${message("产品名称")}:
                </th>
                <td>
                    <span class="productName"></span>
                </td>
                <th>
                    ${message("产品规格")}:
                </th>
                <td>
                    <span class="productSpec"></span>
                </td>
            </tr>
            <tr>
                <th>
                    ${message("产品描述")}:
                </th>
                <td>
                    <span class="productDescription"></span>
                </td>
                <th>
                    ${message("产品编码")}:
                </th>
                <td>
                    <span class="productVonderCode"></span>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("销售单价(元)")}:
                </th>
                <td>
                    <!--<input type="text" name="price" class="text" maxlength="200" oninput="editQty(this)"  btn-fun="clear" />-->
                    <div class="nums-input ov">
                        <input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button"/>
                        <input id="price" class="t price" name="price" value="" mindata="0"
                               oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"/>
                        <input value="+" class="b increase" onmousedown="increase(this,event)" type="button"/>
                    </div>
                </td>
                <th>
                    ${message("销售总额(元)")}:
                </th>
                <td>
                    <span class="red totalSales">${currency(aftersale.totalSales, true)}</span>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("批次号")}:
                </th>
                <td>
                    <input type="text" name="batchNumber" class="text" maxlength="200" btn-fun="clear"   oninput="string.extractString(this,0,50)" />
                </td>
                <th>
                    <span class="requiredField">*</span>${message("板底喷码")}:
                </th>
                <td>
                    <input type="text" name="spurtCode" class="text" maxlength="200" btn-fun="clear" oninput="string.extractString(this,1,50)"/>
                </td>
                <th>
                    ${message("sbu")}:
                </th>
                <td>
                    <input type="text" class="sbuName" value="">
                    <input type="hidden" name="sbu.id" class="text sbuId" maxlength="200" value="" btn-fun="clear" readonly/>
                </td>
                <th class="woodType">
                    ${message("木种")}:
                </th>
                <td class="woodType">
                    <select name="woodTypeId">
                        <option value=""></option>
                        [#list woodTypeProductOfficer as woodType]
                            <option id="${woodType.value}" value="${woodType.id}">${woodType.value}</option>
                        [/#list]
                    </select>
                </td>
            </tr>
        </table>
        <div class="title-style">${message("现场勘查信息及附件")}:</div>
        <table class="input input-edit">
            <tr>
                <th>
                    ${message("地面平整度")}:
                </th>
                <td>
                    <input type="text" name="floorFlatness" class="text" maxlength="200" btn-fun="clear"/>
                </td>
                <th>
                    ${message("地面含水率")}:
                </th>
                <td>
                    <input type="text" name="groundMoistureContent" maxlength="200" oninput="editQty(this)"
                           style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>%</span>
                </td>
                <th>
                    ${message("地板含水率")}:
                </th>
                <td>
                    <input type="text" name="floorMoistureContent" maxlength="200" oninput="editQty(this)"
                           style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>%</span>
                </td>
                <th>
                    ${message("空气湿度")}:
                </th>
                <td>
                    <input type="text" name="airHumidity" maxlength="200" oninput="editQty(this)"
                           style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>%</span>
                </td>
            </tr>
        </table>
        <table class="input input-edit" style="width:100%;margin-top:5px;">
            <div class="title-style">
                ${message("上传附件：销售合同、验收单、现场照片或视频 ")}:
            </div>
            <tr>
                <th><span class="requiredField">*</span>${message("销售合同")}:</th>
                <td>
                    <a href="javascript:;" id="addSalesContractAttach" class="button">添加附件</a>
                </td>
                <th><span class="requiredField">*</span>${message("验收单")}:</th>
                <td>
                    <a href="javascript:;" id="addAcceptanceSheetAttach" class="button">添加附件</a>
                </td>
                <th><span class="requiredField">*</span>${message("现场照片或视频(数量大于5张)")}:</th>
                <td>
                    <a href="javascript:;" id="addScenePicturesAttach" class="button">添加附件</a>
                </td>
            </tr>
        </table>
        <div>
            <span class="title-style fujian">${message("销售合同")}:</span>
            <span class="toPreviewBtn button">浏览全图</span>
            <table id="table-salesContractAttach" style="width:850px"></table>

            <span class="title-style fujian">${message("验收单")}:</span>
            <span class="toPreviewBtn button">浏览全图</span>
            <table id="table-acceptanceSheetAttach" style="width:850px"></table>

            <span class="title-style fujian">${message("现场照片或视频")}:</span>
            <span class="toPreviewBtn button">浏览全图</span>
            <table id="table-scenePicturesAttach" style="width:850px"></table>
        </div>
        <table class="input input-edit" style="width:100%;margin-top:5px;">
            <br/>
            <div style="width:600px;height:30px;line-height:30px">
                <span class="requiredField">*</span>
                <span style="font-size:16px;"><b>经销商描述故障信息:(</b></span>
                <span>故障比例、分布情况及维修记录，简单明了，上述表格信息无须重复填写</span>
                <span style="font-size:16px;"><b>)</b></span>
            </div>
            <tr>
                <td colspan="7">
                    <textarea class="text" name="storeFaultMessage" maxlength="4000"
                              style="width:1200px;height:120px;"></textarea>
                </td>
            </tr>
        </table>
        <table class="input input-edit" style="width:100%;margin-top:5px;">
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("经销商处理方案")}:
                </th>
                <td colspan="7">
                    <div class="storeTreatmentScheme">
                        <label><input class="check js-iname text" name="storeTreatmentScheme" type="checkbox"
                                      value="全部更换"/>${message("全部更换")}</label>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <label>
                            <input class="check js-iname text" name="storeTreatmentScheme" type="checkbox" value="局部更换"
                                   onchange="hack(this)"/>${message("局部更换")}
                            <input type="text" class="underline" name="storePartialReplacement" value=""
                                   oninput="editQty(this)" maxlength="200" readOnly/><span>㎡</span>
                        </label>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <label>
                            <input class="check js-iname text" name="storeTreatmentScheme" type="checkbox" value="退货"
                                   onchange="hack(this)"/>${message("退货")}
                            <input type="text" class="underline" name="storeReturnGoods" value=""
                                   oninput="editQty(this)" maxlength="200" readOnly/><span>m2</span>
                        </label>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <label>
                            <input class="check js-iname text" name="storeTreatmentScheme" type="checkbox" value="赔偿"
                                   onchange="hack(this)"/>${message("赔偿")}
                            <input type="text" class="underline" name="storeCompensation" value=""
                                   oninput="editQty(this)" maxlength="200" readOnly/><span>元</span>
                        </label>
                    </div>
                </td>
            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("赔款明细")}:
                </th>
                <td>
                    <span>${message("赔款用户")}</span>
                    <input type="text" name="reparationUser" maxlength="200" oninput="editQty(this)"
                           style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>元;</span>
                    <span>${message("其他费用")}</span>
                    <input type="text" name="otherExpenses" maxlength="200" oninput="editQty(this)"
                           style="width:80px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>元</span>
                </td>
            </tr>
        </table>
    </div>
    <div class="fixed-top">
        <input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}"/>
        <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
    </div>
</form>

<!-- 全屏看图结构 -->
<div class="preview_swiperBox">
    <div id="picture-swiperLeft" class="swiperButton swiper-button-prev"></div>
    <div id="picture-swiperRight" class="swiperButton swiper-button-next"></div>
    <div id="picture-colse" class="swiper-button-close">
        <span></span>
        <span></span>
    </div>
    <div class="preview_control">
        <div id="scaleBig" class="scaleBig"><span></span></div>
        <div id="scaleSmall" class="scaleSmall"><span></span></div>
        <div id="previewReset" class="previewReset"><span></span></div>
        <div id="rotateLeft" class="rotateLeft"><span></span></div>
        <div id="rotateRight" class="rotateRight"><span></span></div>
    </div>
</div>
<!-- 全屏看图结构 end-->

</body>
</html>