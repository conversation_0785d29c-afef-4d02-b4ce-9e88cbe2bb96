<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>${message("售后列表")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript">
        $(function(){
            $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off');
        });
        function add(){
            parent.change_tab(0,'/aftersales/aftersale/add.jhtml');
        }
        function initWfStates(){
            wfStates = {};
            [#list wfStates as wfState]
            wfStates['${wfState}'] = '${message('22222222'+wfState)}';
            [/#list]
        }
        function showPage(resultMsg){
            var pageNumber=1;
            var totaNum=0;
            var pageSize=200;
            if(resultMsg!=undefined){
                var content_data = $.parseJSON(resultMsg.content);
                totaNum = content_data.total;
                pageSize = content_data.pageSize;
                pageNumber = content_data.pageNumber;
            }
            
            
            var page = parseInt(totaNum/pageSize);
            if(totaNum%pageSize!=0){
                page = page+1;
            }
            if(page==0){
                page = 1;
            }
            $("#s-page").remove();
            var $str = $('<p id="s-page" style="float:left;line-height:28px;font-size:14px;margin-left:5px">当前第<span style="color:blue">'+pageNumber+'</span>页，共<span style="color:blue">'+page+'</span>页，总数<span style="color:blue">'+totaNum+'</span></p>');
            $str.insertAfter(".pageList .next");

        }
        $().ready(function() {
            initMultipleSelect();
            initWfStates();
            var cols = [
                { title:'${message("申请单号")}', name:'sn' ,align:'center',renderer:function(val,item){
                        return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/aftersales/aftersale/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
                    }},
                { title:'${message("创建时间")}',width:140, name:'create_date' ,align:'center'},
                { title:'${message("单据状态")}',width:60, name:'invoice_status' ,align:'center', renderer:function(val,item){
                        var result="";
                        if (item.invoice_status == '0') result = '<span class="blue">已保存</span>';
                        else if (item.invoice_status == '1') result = '<span style="color:orange;">进行中</span>';
                        else if (item.invoice_status == '3') result = '<span class="red">已终止</span>';
                        else if (item.invoice_status == '2') result = '<span class="green">已生效</span>';
                        else if (item.invoice_status == '99') result = '<span class="grey">已作废</span>';

                        return result;
                    }},
                { title:'${message("流程状态")}',width:60, name:'', align:'center', renderer:function(val,item) {
                        var result = wfStates[item.wf_state];
                        if(result!=undefined) return result;
                    }},
                { title:'${message("sbu")}',width:70, name:'sbu_name',align:'center'},
                { title:'${message("创建人")}',width:60, name:'creater_name' ,align:'center'},
                { title:'${message("经销商姓名")}',width:60, name:'dealer_name' ,align:'center'},
                { title:'${message("经销商名称")}', name:'store_name' ,align:'center'},
                //{ title:'${message("经销商授权编码")}', name:'grant_code' ,align:'center'},
                { title:'${message("顾客姓名")}',width:60, name:'name',align:'center'},
                { title:'${message("顾客电话")}', name:'contact_number',align:'center'},
                { title:'${message("工厂")}', name:'factory_name',align:'center'},
                { title:'${message("处理状态")}',width:60, name:'processing_status' ,align:'center', renderer:function(val,item){
                        var result="";
                        if (item.processing_status == '待勘察') result = '<span class="blue">待勘察</span>';
                        else if (item.processing_status == '待观察') result = '<span style="color:orange;">待观察</span>';
                        else if (item.processing_status == '待协商') result = '<span class="red">待协商</span>';
                        else if (item.processing_status == '缺资料') result = '<span style="blue">缺资料</span>';
                        else if (item.processing_status == '已处理') result = '<span class="blue">已处理</span>';
                        else if (item.processing_status == '不处理') result = '<span style="blue">不处理</span>';
                        return result;
                    }},
                { title:'${message("工厂批复时间")}',width:140, name:'end_time' ,align:'center'},
                { title:'${message("是否到账")}',width:60, name:'is_received' ,align:'center', renderer:function(val,item){
                        var result="";
                        if (item.is_received == 'true') 
                        	result = '<span class="blue">已到账</span>';
                        if (item.is_received == 'false') 
                        	result = '<span style="color:orange;">未到账</span>';
                        return result;
                    }},
                { title:'${message("机构")}',width:60, name:'sale_org_name',align:'center'},
                { title:'${message("区域")}',width:60, name:'region_name',align:'center'},
                { title:'${message("区域经理")}',width:60, name:'region_manager',align:'center'},
                { title:'${message("产品编码")}', name:'vonder_code',align:'center'},
                { title:'${message("售后等级")}',width:60, name:'grade',align:'center'},
            ];

            $mmGrid = $('#table-m1').mmGrid({
                autoLoad: true,
                cols: cols,
                fullWidthRows:true,
                url: '/aftersales/aftersale/list_data.jhtml',
                params:function(){
                    return $("#listForm").serializeObject();
                },
                callback:function(data,resultMsg){
                    $(".currentPageRecords").text(data['content'].length);
                    showPage(resultMsg); 
                },
                plugins : [
                    $('#paginator').mmPaginator()
                ]
            });

            $("#selectStore").bindQueryBtn({
                type:'store',
                title:'查询客户',
                url:'/member/store/select_store.jhtml'
            });
            
            //查询区域经理
			$("#selectStoreMember").bindQueryBtn({
				type:'storeMember',
				title:'${message("查询区域经理")}',
				url:'/shop/manager/select_manager.jhtml?memberType=0',//&isSalesman=true',
			});
            
			$("#selectSaleOrg").bindQueryBtn({
                type:'saleOrg',
                title:'查询机构',
                url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2',
                callback: function (rows) {
	                    if(rows.length>0){
							var mhtml="";
							if($("input[name='saleOrgIds']").val() == null){
								var all= "";
							}else{
								var all= $("input[name='saleOrgName']").val();
							}
							
							for (var i = 0; i < rows.length;i++) {
								var idH = $(".saleOrgIds_"+rows[i].id).length;
								if(idH > 0){
									$.message_alert('机构:【'+rows[i].name+'】已添加');
									return false;
								}
							}
							for (var i = 0; i < rows.length;i++) {
								all =all +','+ rows[i].name;
								mhtml = '<div><input name="saleOrgIds" class="text saleOrgIds saleOrgIds_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this,\'saleOrgs\',\'saleOrgName\')"></i></div>';
								$(".saleOrgs").append(mhtml);
							}
							$("input[name='saleOrgName']").val(all);
						}
	                }
            });
        });
        
		//条件导出		    
		function segmentedExport(e) {
			var needConditions = false;//
			var page_url = 'to_condition_export.jhtml';//分页导出统计页面
			var url = 'condition_export.jhtml';//导出的方法
			conditions_export(e, {
				needConditions : needConditions,
				page_url : page_url,
				url : url
			});
		}
		
		function Invoice_print(){
			 var url = '';
			 var title = "售后单";
			 var method = "aftersale";//指向具体的打印方法
			 url = '/cai_niao/caiNiaoPrint/print_cai_niao.jhtml?';
		     var data = $mmGrid.serializeSelectedIds();//参数
	         if(data.length==0){
	         	$.message_alert("请选择售后单！");
	            return false;
	         }
	         window.open(url + data +"&method="+ method+"&title="+title, "_blank");
		}
		
		//删除弹框选中的值
        function newclosePro(e,classname,inputname){
			$(e).closest("div").remove();
			var allName2 = '';
			$("."+classname+" > div").each(function(){
				allName2 = allName2 +','+  $(this).find("p").html();
			})
			$("input[name='"+inputname+"']").attr("value",allName2)
		}


    </script>
</head>
<body>
<form id="listForm" action="/aftersales/aftersale/list.jhtml" method="get">
    <div class="bar">
        <div class="buttonWrap">
            <a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			<div class="flag-wrap flagImp-wrap">
				<a href="javascript:void(0);" class="iconButton" id="export1Button">
					<span class="impIcon">&nbsp;</span>导出
				</a>
				<ul class="flag-list">							 
					<li>
						<a href="javascript:void(0)" onclick="segmentedExport(this)">
							<i class="flag-imp02"></i>
							${message("条件导出")}
						</a>
					</li>		
				</ul>
			</div>
			<input type="button" class="button sureButton" value="售后单打印" onclick="Invoice_print()">
		</div>
	</div>
        <div id="searchDiv">
            <div id="search-content" >
            
                <dl>
                    <dt><p>${message("申请单号")}：</p></dt>
                    <dd>
                        <input type="text" class="text"  name="sn" btn-fun="clear" />
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("单据状态")}：</p></dt>
                    <dd>
                        <div class="checkbox-style">
                            <a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                            <div class="statusList cs-box" data-value="off">
                                <label><input class="check js-iname" name="invoiceStatus" value="0" type="checkbox" checked/>已保存</label>
                                <label><input class="check js-iname" name="invoiceStatus" value="1" type="checkbox" checked/>进行中</label>
                                <label><input class="check js-iname" name="invoiceStatus" value="2" type="checkbox" checked/>已生效</label>
                                <label><input class="check js-iname" name="invoiceStatus" value="3" type="checkbox" checked/>已终止</label>
                                <label><input class="check js-iname" name="invoiceStatus" value="99" type="checkbox" />已作废</label>
                            </div>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("经销商")}：</p></dt>
                    <dd>
        				<span style="position:relative">
							<input name="storeId" class="text storeId" type="hidden" value="">
							<input class="text storeName" maxlength="200" type="text" value="" onkeyup="clearSelect(this)">
							<input type="button" class="iconSearch" value="" id="selectStore">
						</span>
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("经销商名称")}：</p></dt>
                    <dd>
                        <input type="text" class="text"  name="storeName" btn-fun="clear" />
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("经销商姓名")}：</p></dt>
                    <dd>
                        <input type="text" class="text"  name="dealerName" btn-fun="clear" />
                    </dd>
                </dl>
				[#-- <dl>
					<dt><p>${message("省")}：</p></dt>
					<dd>
						<input type="text" class="text" name="province" btn-fun="clear" />
                  	</dd>
				</dl> --]
				<dl>
                    <dt><p>${message("机构")}：</p></dt>
                    <dd>
        				<span style="position:relative">
							[#-- <input name="saleOrgId" class="text storeId" type="hidden" value="">
							<input class="text saleOrgName" maxlength="200" type="text" value="" onkeyup="clearSelect(this)">
							<input type="button" class="iconSearch" value="" id="selectSaleOrg"> --]
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200"
                               onkeyup="clearSelect(this)" value=",${soName}" readOnly/>
	                        <input type="button" class="iconSearch" value="" id="selectSaleOrg"/>
	                        <div class="pupTitleName  saleOrgs">
	                        </div>
						</span>
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("sbu")}：</p></dt>
                    <dd>
                        <div class="checkbox-style">
                            <a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                            <div class="statusList cs-box" data-value="off">
                            [#list sbus as sbu]
                                <label><input class="check js-iname" name="sbuIds" value="${sbu.id}" type="checkbox"/>${sbu.name}</label>
                            [/#list]
                            </div>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("区域")}：</p></dt>
                    <dd>
                        <div class="checkbox-style">
                            <a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                            <div class="statusList cs-box" data-value="off">
                            [#list saleOrgRegions as region]
                                <label><input class="check js-iname" name="regionIds" value="${region.id}" type="checkbox"/>${region.value}</label>
                            [/#list]
                            </div>
                        </div>
                    </dd>
                </dl>
                <dl>
	        		<dt><p>${message("区域经理")}：</p></dt>
	        		<dd >
	        			<span class="search" style="position:relative">
							<input type="hidden" name="storeMemberId" class="text storeMemberId" id="storeMemberId" btn-fun="clear" />
							<input type="text" name="storeMemberName" class="text storeMemberName" maxlength="200" onkeyup="clearSelect(this)" id="storeMemberName" readonly/>
							<input type="button" class="iconSearch" value="" id="selectStoreMember">
						</span>
	        		</dd>
        		</dl>
                <dl>
                    <dt><p>${message("客户电话")}：</p></dt>
                    <dd>
                        <input type="text" class="text" style="width: 100px" name="contactNumber" btn-fun="clear" />
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("地板品类")}：</p></dt>
                    <dd>
                        <input type="text" class="text" name="category" btn-fun="clear" />
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("产品编码")}：</p></dt>
                    <dd>
                        <input type="text" class="text" name="vonderCode" btn-fun="clear" />
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("责任工厂")}：</p></dt>
                    <dd>
                        <input type="text" class="text"  name="factoryName" btn-fun="clear" />
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("处理状态")}：</p></dt>
                    <dd>
                        <div class="checkbox-style">
                            <a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                            <div class="statusList cs-box" data-value="off">
                                <label><input class="check js-iname" name="processingStatus" value="待勘察" type="checkbox"/>${message("待勘察")}</label>
                                <label><input class="check js-iname" name="processingStatus" value="待观察" type="checkbox"/>${message("待观察")}</label>
                                <label><input class="check js-iname" name="processingStatus" value="待协商" type="checkbox"/>${message("待协商")}</label>
                                <label><input class="check js-iname" name="processingStatus" value="缺资料" type="checkbox"/>${message("缺资料")}</label>
                                <label><input class="check js-iname" name="processingStatus" value="已处理" type="checkbox"/>${message("已处理")}</label>
                                <label><input class="check js-iname" name="processingStatus" value="不处理" type="checkbox"/>${message("不处理")}</label>
                            </div>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("是否到账")}：</p></dt>
                    <dd>
                        <div class="checkbox-style">
                            <select name="isReceived"  class="text">
                            	<option value=""></option>
                            	<option value="1">已到账</option>
                            	<option value="0">未到账</option>
                            </select>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("售后等级")}：</p></dt>
                    <dd>
                        <div class="checkbox-style">
                            <a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                            <div class="statusList cs-box" data-value="off">
                                <label><input class="check js-iname" name="grade" value="A" type="checkbox"/>A</label>
                                <label><input class="check js-iname" name="grade" value="B" type="checkbox"/>B</label>
                                <label><input class="check js-iname" name="grade" value="C" type="checkbox"/>C</label>
                                <label><input class="check js-iname" name="grade" value="D" type="checkbox"/>D</label>
                            </div>
                        </div>
                    </dd>
                </dl>
                <dl> 
                    <dt><p>${message("流程状态")}：</p></dt>
                    <dd>
                        <div class="checkbox-style">
                            <a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                            <div class="statusList cs-box" data-value="off">
                                <label><input class="check js-iname" name="wfStatus" value="0" type="checkbox"/>未启动</label>
                                <label><input class="check js-iname" name="wfStatus" value="1" type="checkbox"/>审核中</label>
                                <label><input class="check js-iname" name="wfStatus" value="2" type="checkbox"/>已完成</label>
                                <label><input class="check js-iname" name="wfStatus" value="3" type="checkbox"/>驳回</label>
                            </div>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("创建时间")}:</p></dt>
                    <dd class="date-wrap">
                        <input id="" name="beginTime" class="text" style="width: 140px" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',isShowOK:true,isShowClear:true,});" type="text" btn-fun="clear" />
                        <div class="fl">--</div>
                        <input id="" name="endTime" class="text" style="width: 140px" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',isShowOK:true,isShowClear:true,});" type="text" btn-fun="clear" />
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("工厂批复时间")}:</p></dt>
                    <dd class="date-wrap">
                        <input id="" name="factoryfirstTime" class="text" style="width: 140px" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',isShowOK:true,isShowClear:true,});" type="text" btn-fun="clear" />
                        <div class="fl">--</div>
                        <input id="" name="factoryEndTime" class="text" style="width: 140px" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',isShowOK:true,isShowClear:true,});" type="text" btn-fun="clear" />
                    </dd>
                </dl>

[#--                <dl>--]
[#--                    <dt><p>${message("产品名称")}：</p></dt>--]
[#--                    <dd>--]
[#--                        <input type="text" class="text"  name="productName" btn-fun="clear" />--]
[#--                    </dd>--]
[#--                </dl>--]
            </div>
            <div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
        </div>
    </div>
    <div class="table-responsive">
        <table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
    </div>
</form>
</body>
</html>