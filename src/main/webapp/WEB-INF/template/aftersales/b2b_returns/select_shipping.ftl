<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("产品分类列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript"
	src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	$().ready(function() {
		var cols = [
		{ title:'${message("发货单号")}', name:'shippingSn' ,align:'center'},
		{ title:'${message("仓库")}', name:'warehouse_name' ,align:'center'},
		{ title:'${message("产品名称")}', name:'name' ,align:'center'},
		{ title:'${message("12211")}', name:'vonder_code' ,align:'center'},
		{ title:'${message("产品型号")}', name:'model' ,align:'center'},
		{ title:'${message("产品等级")}',name:'levelName',align:'center',renderer: function(val,item,rowIndex){
			return val;
		}},
		{ title:'${message("单价")}',[#if hiddenAmount==0]hidden:true,[/#if] name:'member_price' ,align:'center'},
		{ title:'${message("结算价格")}',[#if hiddenAmount==0]hidden:true,[/#if] name:'sale_org_price' ,align:'center'},
		{ title:'${message("可退货数量")}', name:'quantity' ,align:'center'},
	];
	
	[#if multi==2]
		var multiSelect = true;
	[#else]
		var multiSelect = false;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		rowCursorPointer:true,
		formQuery:true,
        cols: cols,
        url: 'selectShippingData.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
});
function childMethod(){
   return $mmGrid.selectedRows();
};
</script>
</head>
<body style="min-width: 0px;">
	<form id="listForm" action="selectOrder.jhtml" method="get">
		<input type="hidden" name="storeId" value="${storeId}"> 
		<input type="hidden" name="sbuId" value="${sbuId}"> 
		<input type="hidden" name="productGrade" value="${productGrade}">
		<input type="hidden" name="productOrganizationId" value="${productOrganizationId}">
					<div class="bar">
						<div class="buttonWrap"></div>
						<div id="searchDiv">
							<div id="search-content">
								<dl>
									<dt>
										<p>${message("发货单号")}：</p>
									</dt>
									<dd>
										<input type="text" class="text" id="sn" name="shippingSn"
											value="" btn-fun="clear" />
									</dd>
								</dl>
								<dl>
									<dt>
										<p>${message("产品名称")}：</p>
									</dt>
									<dd>
										<input type="text" class="text" id="name" name="name" value=""
											btn-fun="clear" />
									</dd>
								</dl>
								<dl>
									<dt>
										<p>${message("产品型号")}：</p>
									</dt>
									<dd>
										<input type="text" class="text" id="mod" name="mod" value=""
											btn-fun="clear" />
									</dd>
								</dl>
								<dl>
									<dt>
										<p>${message("12211")}：</p>
									</dt>
									<dd>
										<input type="text" class="text" id="vonderCode"
											name="vonderCode" value="" btn-fun="clear" />
									</dd>
								</dl>
							</div>
							<div class="search-btn">
								<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
							</div>
						</div>
					</div>
					<div class="table-responsive">
						<table id="table-m1"></table>
						<div id="body-paginator" style="text-align: left;">
							<div id="paginator"></div>
						</div>
					</div>
	</form>
</body>
</html>