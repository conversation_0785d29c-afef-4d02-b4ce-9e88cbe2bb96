<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("选择采购入库产品列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript"
	src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript">
$().ready(function() {

	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	var cols = [
		{ title:'${message("发货单号")}', name:'sn', align:'center'},
		{ title:'${message("产品编码")}', name:'vonder_code', width:110, align:'center' },
		{ title:'${message("产品名称")}', name:'name', width:110, align:'center' },
		{ title:'${message("产品描述")}', name:'description', width:120, align:'center'},
		{ title:'${message("产品分类")}', name:'product_category_name', align:'center'},
		{ title:'${message("单位")}', name:'unit', align:'center'},
		{ title:'${message("单价")}', name:'price', align:'center'},
		{ title:'${message("数量")}', name:'quantity', align:'center'},
		{ title:'${message("金额")}', name:'money', align:'center'},
	];
	
	[#if multi==2]
	    var multiSelect = true;
	[#else]
	    var multiSelect = false;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
	    multiSelect:multiSelect,
	    autoLoad: true,
	    fullWidthRows:true,
	    checkByClickTd:true,
	    rowCursorPointer:true,
	    cols: cols,
	    url: '/shop/pro_put_in_storage/select_list_data.jhtml',
	    params:function(){
	        return $("#listForm").serializeObject();
	    },
	    plugins : [
	        $('#paginator').mmPaginator()
	    ]
	});

});

function childMethod(){
    return $mmGrid.selectedRows();
};

</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<input type="hidden" name="code" value="${code}" /> <input
			type="hidden" name="type" value="${type}" /> <input type="hidden"
			name="multi" value="${multi}" />
		<div class="bar">
			<div class="buttonWrap"></div>
			[#--搜索begin--]
			<div id="searchDiv">
				<div id="search-content">
					<dl>
						<dt>
							<p>${message("发货单号")}：</p>
						</dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="sn"
								value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("产品编码")}：</p>
						</dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="vonderCode"
								value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("产品名称")}：</p>
						</dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="pName"
								value="" btn-fun="clear" />
						</dd>
					</dl>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		[#--搜索end--]
		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator" style="text-align: left;">
				<div id="paginator"></div>
			</div>
		</div>
	</form>
</body>
</html>