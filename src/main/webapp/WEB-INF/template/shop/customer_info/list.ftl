<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("顾客信息列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript"
	src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript">
$().ready(function() {

	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	var cols = [
		{ title:'${message("用户名")}', name:'username', align:'center' },
		{ title:'${message("用户类型")}', name:'', align:'center', renderer:function(val,item){
            var result="";
            if (parseInt(item.member_type) == 1) result = '<span class="blue">外部用户</span>';
            else if (parseInt(item.member_type) == 0) result = '<span class="green">企业用户</span>';
            return result;
        }},
		{ title:'${message("顾客姓名")}', name:'name', align:'center'},
		{ title:'${message("手机号码")}', name:'mobile', align:'center'},
		{ title:'${message("地址")}', name:'address', align:'center'},
		{ title:'${message("创建时间")}', name:'create_date', align:'center'},
		{ title:'${message("积分")}', name:'', align:'center'},
		{ title:'${message("门店名称")}', name:'shop_name', align:'center'},
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
// 		multiSelect:false,	// 多选
		fullWidthRows:true,
        cols: cols,
        url: 'list_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });

});

</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<!-- <a href="javascript:;" onclick="parent.change_tab(0,'add.jhtml')" class="iconButton" id="addButton">
					<span class="addIcon">&nbsp;</span>${message("1001")}
				</a> -->
			</div>
			[#--搜索begin--]
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
				<div id="search-content">
					<dl>
						<dt>
							<p>${message("顾客姓名")}：</p>
						</dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="name"
								value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("手机号码")}：</p>
						</dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="phone"
								value="" btn-fun="clear" />
						</dd>
					</dl>
				</div>
			</div>
		</div>
		[#--搜索end--]
		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator" style="text-align: left;">
				<div id="paginator"></div>
			</div>
		</div>
	</form>
</body>
</html>