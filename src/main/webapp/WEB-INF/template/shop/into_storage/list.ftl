<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("入库申请列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript">
$().ready(function() {

	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	var cols = [
		{ title:'${message("入库单号")}', name:'sn' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var	html = '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
			return html;
		}},
		{ title:'${message("订单号")}', name:'order_sn', align:'center' },
		{ title:'${message("单据状态")}', name:'' ,align:'center',renderer:function(val,item){
            var result="";
            if (item.status == 0) result = '<span class="blue">已保存</span>';
            else if (item.status==1) result = '<span class="blue">已提交</span>';
            else if (item.status == 2) result = '<span class="red">已终止</span>';
            else if (item.status == 3) result = '<span class="green">进行中</span>';
            return result;
        }}, 
		{ title:'${message("入库时间")}', name:'enter_date', align:'center' },
		{ title:'${message("仓库名称")}', name:'s_name', align:'center'},
		{ title:'${message("备注")}', name:'memo', align:'center'},
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
// 		multiSelect:false,	// 多选
		fullWidthRows:true,
        cols: cols,
        url: 'list_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });

});

</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<a href="javascript:;" onclick="parent.change_tab(0,'add.jhtml')" class="iconButton" id="addButton">
					<span class="addIcon">&nbsp;</span>${message("1001")}
				</a>
			</div>
			[#--搜索begin--]
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
				<div id="search-content" >
					<dl>
						<dt><p>${message("订单编号")}：</p></dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="sn" value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
	                    <dt><p>${message("单据状态")}：</p></dt>
	                    <dd>
	                        <div class="checkbox-style">
	                            <a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
	                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
	                            <div class="statusList cs-box" data-value="off">
	                                <label><input class="check js-iname" name="status" value="0" type="checkbox"/>${message("已保存")}</label>
	                                <label><input class="check js-iname" name="status" value="1" type="checkbox"/>${message("已提交")}</label>
	                                <label><input class="check js-iname" name="status" value="2" type="checkbox"/>${message("已终止")}</label>
	                                <label><input class="check js-iname" name="status" value="3" type="checkbox"/>${message("进行中")}</label>
	                            </div>
	                        </div>
	                    </dd>
	                </dl>
				 </div>		
		    </div>
		</div>
		[#--搜索end--]
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>