<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑门店装修验收")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
/* 调整附件 */
.nowrap > div >.t-edit {
    width: 80%;
    margin: 5px 0 -6px 0;
}
</style>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e);
}
function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	// 校验
	$inputForm.validate({

	});
	
	// 选择门店
	$("#selectShopInfo").click(function(){
		$("#selectShopInfo").bindQueryBtn({
			type:'shopInfo',
			title:'${message("查询门店")}',
			bindClick:false,
			url:'/shop/shopInfo/select_shopInfo.jhtml',
			callback:function(rows){
				if(rows.length > 0){
					var row = rows[0];
					$(".shopInfoSn").val(row.sn);
                    $(".shopInfoId").val(row.id);
                    $(".dealerText").html(row.distributor_name);
                    $(".dealer").val(row.distributor_name);
//                  $(".provincesText").html(row.);  //省份
//                  $(".provinces").val(row.);
                    $(".cityText").html(row.area_name);  // 省份城市
                    $(".city").val(row.area_name);
                    $(".dealerPhoneText").html(row.distributor_phone);
                    $(".dealerPhone").val(row.distributor_phone);
                    $(".regionalManagerText").html(row.xujl_name); // 区域经理
                    $(".regionalManager").val(row.xujl_name);
                    $(".regionalManagerPhoneText").html(row.xujl_mobile); // 区域经理电话
                    $(".regionalManagerPhone").val(row.xujl_mobile);
                    $(".shopAddressText").html(row.address); // 地址
                    $("#shopAddress").val(row.address);
                    //切换机构
                    $(".saleOrgName").text(row.sale_org_name==null?"":row.sale_org_name);
				}
			}
		});
	});
	
	// 选择设计
	$("#selectShopDevise").click(function(){
		$("#selectShopDevise").bindQueryBtn({
			type:'shopInfo',
			title:'${message("查询门店")}',
			bindClick:false,
			url:'/shop/devise/select_shop_devise.jhtml',
			callback:function(rows){
				if(rows.length > 0){
					var row = rows[0];
					var brand = new Array("大自然综合店","大自然·三层专卖店","大自然·实木专卖店","大自然·强化专卖店");
					$(".shopDeviseSn").val(row.sn);
                    $(".shopDeviseId").val(row.id);
                    $("#structure1").html(row.structure1);
                    $("#structure2").html(row.structure2);
                    $("#shopRenovationAttributeText").html(row.shop_renovation_attribute);
                    $("#shopRenovationAttribute").val(row.shop_renovation_attribute);
                    $("#highLimitText").html(row.high_limit);
                    $("#highLimit").val(row.high_limit);
                    $("#designBrandText").html(isNull(brand[row.design_brand]));
                    $("#designBrand").val(row.design_brand);
                    $("#areaText").html(row.area);
                    $("#area").val(row.area);

                    // $("#predictConstructionTimeText").html(row.predict_construction_time.substring(0,10));
                    // $("#predictConstructionTime").val(row.predict_construction_time.substring(0,10));
                    // $("#predictStartsTimeText").html(row.predict_starts_time.substring(0,10));
                    // $("#predictStartsTime").val(row.predict_starts_time.substring(0,10));

                    if(row.predict_construction_time != null){
                        $("#predictConstructionTimeText").html(row.predict_construction_time.substring(0,10));
                    }
                    if(row.predict_construction_time != null){
                        $("#predictConstructionTime").val(row.predict_construction_time.substring(0,10));

                    }
                    if(row.predict_starts_time!= null){
                        $("#predictStartsTimeText").html(row.predict_starts_time.substring(0,10));

                    }
                    if(row.predict_starts_time!= null){
                        $("#predictStartsTime").val(row.predict_starts_time.substring(0,10));

                    }
				}
			}
		});
	});

	/** 初始化验收申请承诺附件 ---------- start --------- */
	var acceptance_commitment_attachs = ${acceptanceCommitmentAttachs};
    var acceptanceCommitmentAttachIndex=0;
	var acceptanceCommitmentCols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
    		if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name,item.name,item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].id']=item.id;
				hideValues['acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].url']=url;
				hideValues['acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].type']=item.type;  // 验收申请承诺附件
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].name',
					hideValues:hideValues
				});
				
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].url']=url;
				hideValues['acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].type']=0; // 验收申请承诺附件
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="acceptanceCommitmentAttachs['+acceptanceCommitmentAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
    		acceptanceCommitmentAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid_1=$('#table-acceptance-commitment-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: acceptanceCommitmentCols,
        items: acceptance_commitment_attachs,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAcceptanceCommitmentAttach = $("#addAcceptanceCommitmentAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid_1.addRow(row,null,1);
	        }
        }
    }
    $addAcceptanceCommitmentAttach.file_upload(option1);
	/** 初始化验收申请承诺附件 ---------- end --------- */
	
	/** 门店装修验收照片附件 ---------- start --------- */
	var shop_decorate_attachs = ${shopDecorateAttachs};
    var shopDecorateAttachIndex=0;
	var shopDecorateAttachCols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
    		if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name,item.name,item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].id']=item.id;
				hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].url']=url;
				hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].type']=item.type;  // 门店装修验收照片附件
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'shopDecorateAttachs['+shopDecorateAttachIndex+'].name',
					hideValues:hideValues
				});
				
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].url']=url;
				hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].type']=1;  // 门店装修验收照片附件
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'shopDecorateAttachs['+shopDecorateAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="shopDecorateAttachs['+shopDecorateAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
    		shopDecorateAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid_2=$('#table-shop-decorate-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: shopDecorateAttachCols,
        items: shop_decorate_attachs,
        checkCol: false,
        autoLoad: true
    });
    
    var $addShopDecorateAttach = $("#addShopDecorateAttach");
	var attachIdnex = 0;
	var option2 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid_2.addRow(row,null,1);
	        }
        }
    }
    $addShopDecorateAttach.file_upload(option2);
	
	var pay_platform_attachs = ${payPlatformAttachs};
    var payPlatformAttachIndex=0;
	var payPlatformAttachCols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
    		if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name,item.name,item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].id']=item.id;
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].url']=url;
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].type']=item.type;  // 门店装修验收照片附件
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'payPlatformAttachs['+payPlatformAttachIndex+'].name',
					hideValues:hideValues
				});
				
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].url']=url;
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].type']=2;  // 门店装修验收照片附件
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'payPlatformAttachs['+payPlatformAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="payPlatformAttachs['+payPlatformAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			payPlatformAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid_3=$('#table-pay-platform-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: payPlatformAttachCols,
        items: pay_platform_attachs,
        checkCol: false,
        autoLoad: true
    });
    
    var $addPayPlatformAttach = $("#addPayPlatformAttach");
	var attachIdnex = 0;
	var option3 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid_3.addRow(row,null,1);
	        }
        }
    }
    $addPayPlatformAttach.file_upload(option3);
	
	var plate_attachs = ${plateAttachs};
    var plateAttachIndex=0;
	var plateAttachCols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
    		if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name,item.name,item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['plateAttachs['+plateAttachIndex+'].id']=item.id;
				hideValues['plateAttachs['+plateAttachIndex+'].url']=url;
				hideValues['plateAttachs['+plateAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['plateAttachs['+plateAttachIndex+'].type']=item.type;  // 门店装修验收照片附件
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'plateAttachs['+plateAttachIndex+'].name',
					hideValues:hideValues
				});
				
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['plateAttachs['+plateAttachIndex+'].url']=url;
				hideValues['plateAttachs['+plateAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['plateAttachs['+plateAttachIndex+'].type']=3;  // 门店装修验收照片附件
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'plateAttachs['+plateAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="plateAttachs['+plateAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			plateAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid_4=$('#table-plate-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: plateAttachCols,
        items: plate_attachs,
        checkCol: false,
        autoLoad: true
    });
    
    var $addPlateAttach = $("#addPlateAttach");
	var attachIdnex = 0;
	var option4 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid_4.addRow(row,null,1);
	        }
        }
    }
    $addPlateAttach.file_upload(option4);
	/** 门店装修验收照片附件 ---------- end --------- */
	
	/* 删除附件 */
    var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	[#if acceptanceReimburse.wfId!=null]
	 	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${acceptanceReimburse.wfId}");
	[/#if]
});

// 保存更新
function save(e){
	var str = '您确定要保存吗？';
	var url = 'update.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= 'edit.jhtml?id='+resultMsg.objx;
				})
			}
		});
	}
}

//只有选中复选框后才能编辑后面的文本框
function handle(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').removeAttr("readonly");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').attr("readonly", "readonly");
		$pdiv.find('input[type=text]').val("");
	}
}
// 只有算是选中复选框后才能编辑后面的日期
function handleDate(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').removeAttr("disabled");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').attr("disabled", "disabled");
		$pdiv.find('input[type=text]').val("");
	}
}
// 只有选中复选框后才能编辑后面的单选框
function handleRadio(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=radio]').removeAttr("disabled");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=radio]').attr("disabled", "disabled");
		$pdiv.find('input[type=radio]').removeAttr("checked");
	}
}
	function check_wf(e){

		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			$.message_confirm("您确定要审批流程吗？",function(){
                var objTypeId = 10005;
			    //var modelId = 185030; //开发环境
                var modelId = 7;//测试环境

				var url="check_wf.jhtml?id=${acceptanceReimburse.id}&modelId="+modelId+"&objTypeId="+objTypeId;
				//var url="check_wf.jhtml?id=${acceptanceReimburse.id}&modelId="+modelId+"&objTypeId="+objTypeId;
				var data = $form.serialize();
				ajaxSubmit(e,{
					method:'post',
					url:url,
					async: true,
					callback: function(resultMsg) {
						$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
							location.reload(true);
						})
					}
				});
			});
		}
	}


function saveform(e){
    $.message_confirm("您确定要提交吗？",function(){
        //获取表单所有数据
        var params = $("#inputForm").serializeArray();
        //定义url
        var url = 'saveform.jhtml?type='+e;
        ajaxSubmit(e,{
            method:'post',
            url:url,
            data:params,
            async: true,
            callback: function(resultMsg) {
                $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                    location.reload(true);
                })
            }
        });
    });
}
</script>
</head>
<body>
	<div class="pathh">
        &nbsp;${message("门店装修验收")}
	</div>
	<form id="inputForm" action="/shop/acceptance/update.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<div style="margin:0 0 10px 0; font-size:15px;">
				${message("门店资料")}
			</div>
			<table class="input input-edit">
                <tr>
					<th>${message("单号")}:</th>
					<td>
						<span>${acceptanceReimburse.sn}</span>
						<input type="hidden" class="text" name="sn" value="${acceptanceReimburse.sn}" maxlength="200" btn-fun="clear" />
						<input type="hidden" class="text" name="id" value="${acceptanceReimburse.id}" maxlength="200" btn-fun="clear" />
						<input type="hidden" class="text" name="type" value="${acceptanceReimburse.type}" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("单据状态")}:</th>
					<td>
						[#if acceptanceReimburse.status == 0]
						<span>已保存</span>
						[#elseif acceptanceReimburse.status == 1]
						<span>进行中</span>
						[#elseif acceptanceReimburse.status == 2]
						<span>已完成</span>
						[#elseif acceptanceReimburse.status == 3]
						<span>已终止</span>
						[/#if]
						<input type="hidden" class="text" name="status" value="${acceptanceReimburse.status}" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("门店编码")}:</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="shopInfoId" class="text shopInfoId" value="${acceptanceReimburse.shopInfo.id}" btn-fun="clear"/>
							<input type="text" name="shopInfoSn" class="text shopInfoSn" value="${acceptanceReimburse.shopInfo.sn}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
							<input type="button" class="iconSearch" value="" id="selectShopInfo"/>
						</span>
					</td>
					<th>${message("经销商姓名")}:</th>
					<td>
					    <span class="dealerText">${acceptanceReimburse.shopInfo.distributorName}</span>
						<input type="hidden" class="text dealer" name="dealer" value="${acceptanceReimburse.shopInfo.distributorName}" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("省份城市")}:</th>
                    <td>
                        <span class="cityText">${acceptanceReimburse.shopInfo.area.fullName}</span>
                        <input type="hidden" class="text city" name="city" value="${acceptanceReimburse.shopInfo.area.fullName}" maxlength="200" btn-fun="clear" />
                    </td>
					<th>${message("经销商联系电话")}:</th>
					<td>
					    <span class="dealerPhoneText">${acceptanceReimburse.shopInfo.distributorPhone}</span>
						<input type="hidden" class="text dealerPhone" name="dealerPhone" value="${acceptanceReimburse.shopInfo.distributorPhone}" btn-fun="clear" />
					</td>
					<th>${message("区域经理姓名")}:</th>
					<td>
					    <span class="regionalManagerText">${acceptanceReimburse.shopInfo.store.storeMember.name}</span>
						<input type="hidden" class="text regionalManager" name="regionalManager" value="${acceptanceReimburse.shopInfo.store.storeMember.name}" btn-fun="clear" />
					</td>
					<th>${message("区域经理联系电话")}:</th>
					<td>
					    <span class="regionalManagerPhoneText">${acceptanceReimburse.shopInfo.store.storeMember.member.mobile}</span>
						<input type="hidden" class="text regionalManagerPhone" name="regionalManagerPhone" value="${acceptanceReimburse.shopInfo.store.storeMember.member.mobile}" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("门店详细地址")}:</th>
					<td colspan="3">
					    <span class="shopAddressText">${acceptanceReimburse.shopInfo.address}</span>
						<input type="hidden" class="text shopAddress" name="shopAddress" id="shopAddress" value="${acceptanceReimburse.shopInfo.address}" maxlength="200" btn-fun="clear" readonly="readonly" />
					</td>
					<th>${message("门店名称")}:</th>
					<td>
						<input type="text" class="test shopInfoShopName" name="shopInfoShopName" value="${shopDevise.shopInfo.shopName}" btn-fun="clear" />
					</td>
					<th>${message("机构")}:</th>
					<td>
					    <span class="saleOrgName">${acceptanceReimburse.saleOrg.name}</span>
					</td>
				</tr>
			</table>
			
			<div style="margin:10px 0 10px 0; font-size:15px;">
				${message("设计资料")}
			</div>
			<table class="input input-edit">
				<tr>
					<th>${message("设计单号")}:</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="shopDeviseId" class="text shopDeviseId" value="${shopDevise.id}" btn-fun="clear"/>
							<input type="text" name="shopDeviseSn" class="text shopDeviseSn" value="${shopDevise.sn}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
							<input type="button" class="iconSearch" value="" id="selectShopDevise"/>
						</span>
					</td>
					<th>${message("门店结构情况")}:</th>
					<td>
					    <span id="structure1">${shopDevise.structure1}</span>&nbsp;&nbsp;&nbsp;&nbsp;
                        <span id="structure2">${shopDevise.structure2}</span>
						<!-- <select name="" class="text" style="width:49%" disabled="disabled">
							<option value="${shopDevise.structure1}" id="structure1">${shopDevise.structure1}</option>
						</select>
						<select name="" class="text" style="width:49%" disabled="disabled">
							<option value="${shopDevise.structure2}" id="structure2">${shopDevise.structure2}</option>
						</select> -->
					</td>
					<th>${message("门店装修属性")}:</th>
					<td>
					    <span id="shopRenovationAttributeText">${shopDevise.shopRenovationAttribute}</span>
						<input type="hidden" name="" value="${shopDevise.shopRenovationAttribute}" id="shopRenovationAttribute" class="text" btn-fun="clear" readonly="readonly"></input>
					</td>
					<th>${message("天花限高")}:</th>
					<td>
					    <span id="highLimitText" style="text-align: center;display:block;">${shopDevise.highLimit}</span>
						<input type="hidden" name="" value="${shopDevise.highLimit}" id="highLimit" class="text" btn-fun="clear" readonly="readonly"></input>
					</td>
				</tr>
				<tr>
					<th>${message("设计品牌")}:</th>
					<td>
                        [#if shopDevise.designBrand == 0]
                        <span id="designBrandText">大自然综合店</span>
                        [#elseif shopDevise.designBrand == 1]
                        <span id="designBrandText">大自然·三层专卖店</span>
                        [#elseif shopDevise.designBrand == 2]
                        <span id="designBrandText">大自然·实木专卖店</span>
                        [#elseif shopDevise.designBrand == 3]
                        <span id="designBrandText">大自然·强化专卖店</span>
                        [/#if]
                        <input type="hidden" class="text" name="" value="${shopDevise.designBrand}" id="designBrand" maxlength="200" btn-fun="clear" readonly="readonly" />
                    </td>
					<th>${message("面积")}:</th>
					<td>
					    <span id="areaText" style="text-align: center;display:block;">${shopDevise.area}</span>
						<input type="hidden" name="" value="${shopDevise.area}" id="area" class="text" btn-fun="clear" readonly="readonly"></input>
					</td>
					<th>${message("预订施工时间")}</th>
					<td>
					    <span id="predictConstructionTimeText">${shopDevise.predictConstructionTime!''}</span>
						<input type="hidden" name="" value="${shopDevise.predictConstructionTime}" id="predictConstructionTime" class="text" maxlength="200" readonly="readonly" />
					</td>
					<th>${message("预订开业时间")}</th>
					<td>
					    <span id="predictStartsTimeText">${shopDevise.predictStartsTime!''}</span>
						<input type="hidden" name="" value="${shopDevise.predictStartsTime}" id="predictStartsTime" class="text" maxlength="200" readonly="readonly" />
					</td>
				</tr>
			</table>


			[#-- =====================================================
			<div class="title-style">
				${message("验收申请承诺")}
				<div class="" style="border:1px solid #dcdcdc; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<pre style="font:13px 'Microsoft YaHei';">
1、	确定按公司设计图纸严格按图施工；如有与图纸不符，将接受公司的相关裁定。
2、	确定按照软装、灯具清单购置相关标准配饰物料；如购置不规范，将视为接受直接折扣处理，不做整改。
3、	确定按设计规范设置辅料产品展示区，如无辅料区，将扣除报销总金额的10%处理，不做整改。
4、	由于经销商个人原因，要求重复设计的门店，将扣除报销总金额的10%处理。
5、	确认以下资料提交齐全（□处打√）
					</pre>
					<div>
						<div style="margin:0px 0 10px 0;">
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment1" type="checkbox" value="0" [#if ac?seq_contains("0") == true]checked[/#if] /><label for="acceptanceCommitment1"> 门店平面图核实面积且签字</label>&nbsp;&nbsp;&nbsp;&nbsp;
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment2" type="checkbox" value="1" [#if ac?seq_contains("1") == true]checked[/#if] /><label for="acceptanceCommitment2"> 提交门店装修合同复印件</label>
						</div>
						<div>
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment3" type="checkbox" value="2" onchange="handle(this)" [#if ac?seq_contains("2") == true]checked[/#if] />
							<label for="acceptanceCommitment3"> 提交门店租赁合同/房产证复印件：租赁期限</label>
							<input class="t acreage" name="acceptanceLeaseTerm" value="${acceptanceReimburse.acceptanceLeaseTerm}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" [#if ac?seq_contains("2") == false]readonly="readonly"[/#if] style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
							<span>年，合同门店面积</span>
							<input class="t acreage" name="acceptanceArea" value="${acceptanceReimburse.acceptanceArea}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" [#if ac?seq_contains("2") == false]readonly="readonly"[/#if] style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
							<span>㎡</span>
						</div>
					</div>
			    </div>
			</div>
		    
		    <div class="title-style">
				${message("上传以上确定附件：门店平面图、门店租赁合同/房产复印件、门店装修合同复印件")}
				[#if acceptanceReimburse.wfId == null]
				<div class="btns">
					<a href="javascript:;" id="addAcceptanceCommitmentAttach" class="button">添加附件</a>
				</div>
				[/#if]
			</div>
			<table id="table-acceptance-commitment-attach"></table>
			
			<div class="title-style" >
				${message("验收人员核实（区域经理、设计师）")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 8px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify1" type="checkbox" value="0" onchange="handleDate(this)" /> -->
						<label for="acceptanceVerify1">经确认，装修施工时间：</label>
						<input type="text" style="width:190px;" name="decorateConstructionTime" value="${acceptanceReimburse.decorateConstructionTime}" class="text" id="d4311" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',maxDate:'#F{$dp.$D(\'d4312\')}'});" />
					</div>
					<div style="margin:0 0 8px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify2" type="checkbox" value="1" onchange="handleDate(this)" /> -->
						<label for="acceptanceVerify2">经确认，装修完成时间：</label>
						<input type="text" style="width:190px;" name="decorateCompleteTime" value="${acceptanceReimburse.decorateCompleteTime}" class="text" id="d4312" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',minDate:'#F{$dp.$D(\'d4311\')}'});" />
					</div>
					<div style="margin:0 0 3px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify3" type="checkbox" value="2" onchange="handleRadio(this)" /> -->
						<label for="acceptanceVerify3">经确认，门店装修属性：</label>
						<input class="check js-iname" name="acceptanceShop" id="acceptanceShop1" type="radio" value="重装店" [#if acceptanceReimburse.acceptanceShop == "重装店"]checked[/#if] />
						<label for="acceptanceShop1">重装店</label>&nbsp;&nbsp;&nbsp;&nbsp;
						<input class="check js-iname" name="acceptanceShop" id="acceptanceShop2" type="radio" value="新店" [#if acceptanceReimburse.acceptanceShop == "新店"]checked[/#if] />
						<label for="acceptanceShop2">新店</label>
					</div>
				</div>
			</div>
		 ======================================== --]

				<table class="input input-edit" style="width:100%;margin-top:5px;">
				    <div class="title-style">
				    	${message("门店装修验收照片附件：门头远近距离照、收银台、各样板区")}
				    </div>
					<tr>
						<th>${message("门头远近距离照")}:</th>
						<td>
						[#if acceptanceReimburse.wfId == null]
							<a href="javascript:;" id="addShopDecorateAttach" class="button">添加附件</a>
						[/#if]
						</td>
						<th>${message("收银台")}:</th>
						<td>
						[#if acceptanceReimburse.wfId == null]
							<a href="javascript:;" id="addPayPlatformAttach" class="button">添加附件</a>
						[/#if]
						</td>
						<th>${message("各样板区")}:</th>
						<td>
						[#if acceptanceReimburse.wfId == null]
							<a href="javascript:;" id="addPlateAttach" class="button">添加附件</a>
						[/#if]
						</td>
					</tr>
				</table>
				<div>
		        	<span>${message("门店照片")}</span>
			        <table id="table-shop-decorate-attach" style="width:850px"></table>
			        <span>${message("租赁合同")}</span>
			        <table id="table-pay-platform-attach" style="width:850px"></table>
			        <span>${message("平面图")}</span>
					<table id="table-plate-attach" style="width:850px"></table>
				</div>

			[#if acceptanceReimburse.wfId != null]
			[#if szsh||szshs]
				<div class="title-style" >
					${message("省长意见：")}[#if node.name?contains("省长")]<input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/>[/#if]
						<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
							<div>
								<textarea rows="3" name="provincialOperationMemo" style="width:85%; border:1px solid #dcdcdc; padding:5px;">${acceptanceReimburse.provincialOperationMemo}</textarea>
							</div>
						</div>
				</div>
			[/#if]
			[#if qdzy||qdzys]
				<div class="title-style" >
					${message("渠道部意见：")}[#if node.name?contains("渠道")]<input type="button" class="bottonss tj" onclick="saveform(3)" value="提交"/>[/#if]
					<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
						<div>
							<textarea rows="3" name="directorOpinionMemo" style="width:85%; border:1px solid #dcdcdc; padding:5px;">${acceptanceReimburse.directorOpinionMemo}</textarea>
						</div>
					</div>

                    <div style="margin:10px 0 10px 0; font-size:15px;">
						${message("渠道部填写")}
                    </div>
                    <table class="input input-edit">
                        <tr>
                            <th>${message("门店授权编号")}:</th>
                            <td>
                                <input type="text" name="shopAuthorizationCodes" value="${acceptanceReimburse.shopAuthorizationCodes}" class="text" maxlength="200"  btn-fun="clear" />
                            </td>
                            <th>${message("新增档案编号")}:</th>
                            <td>
                                <input type="text" name="archivesCodes" value="${acceptanceReimburse.archivesCodes}" class="text" maxlength="200"  btn-fun="clear" />
                            </td>
                            <th>${message("新增时间")}:</th>
                            <td>
                                <input id="startTime" name="newTime" class="text" value="${acceptanceReimburse.newTime}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
                                <input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
                            </td>
                        </tr>
                    </table>
				</div>
			[/#if]
			[/#if]
        </div>
        <div class="fixed-top">
			[#if wf == null]
			<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon"></span>${message("审核")}</a>
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			[/#if]
                <input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
            </div>

	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>