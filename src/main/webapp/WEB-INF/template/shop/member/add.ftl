<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增门店成员")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript"
	src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
	$().ready(function() {
		var $inputForm = $("#inputForm");
		
		// 表单验证
		$inputForm.validate({
			rules: {
				name: "required",
				type: "required",
				phone: "required",
				idCard: {
					pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
					//required: true
				},
			} ,
			messages: {
				idCard: {
					pattern: "${message("身份证格式有误")}"
				}
			},
		});
		
		$("input[name='imageName']").single_upload({
			uploadSize:"source"
		});
	
	
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= '/shop/member/edit.jhtml?id='+resultMsg.objx;
				});
			}
		 });
		 
		 // 选择门店
		$("#selectShopInfo").click(function(){
			$("#selectShopInfo").bindQueryBtn({
				type:'shopInfo',
				title:'${message("查询门店")}',
				bindClick:false,
				url:'/shop/shopInfo/select_shopInfo.jhtml',
				callback:function(rows){
					if(rows.length > 0){
						var row = rows[0];
						$(".shopName").val(row.shop_name);
						$(".shopInfoId").val(row.id);
					}
				}
			});
		});
	
	});
</script>
</head>
<body>
	<div class="pathh">&nbsp;${message("新增门店成员")}</div>
	<form id="inputForm" action="/shop/member/save.jhtml" method="post"
		type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>${message("门店")}:</th>
					<td><span class="search" style="position: relative"> <input
							type="hidden" name="shopInfoId" class="text shopInfoId" value=""
							btn-fun="clear" /> <input type="text" class="text shopName"
							value="" maxlength="200" onkeyup="clearSelect(this)" readOnly />
							<input type="button" class="iconSearch" value=""
							id="selectShopInfo" />
					</span></td>
					<th><span class="requiredField">*</span>${message("姓名")}:</th>
					<td><input type="text" name="name" class="text"
						maxlength="200" btn-fun="clear" /></td>
					<th>${message("性别")}:</th>
					<td><label><input type="radio" name="gender" value="0"
							checked="checked" />男</label> <label><input type="radio"
							name="gender" value="1" />女</label></td>
					<th><span class="requiredField">*</span>${message("手机")}:</th>
					<td><input type="text" name="phone" class="text"
						maxlength="200" btn-fun="clear" /></td>
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("身份证")}:</th>
					<td><span class="error-msg"> <input type="text"
							id="idCard" name="idCard" class="text " btn-fun="clear" />
					</span></td>
					<th>${message("职位")}:</th>
					<td><select name="type" class="text type">
							<option></option>
							<option value="0">${message("导购员")}</option>
							<option value="1">${message("跟单")}</option>
							<option value="2">${message("财务")}</option>
							<option value="3">${message("店长")}</option>
							<option value="4">${message("副店长")}</option>
					</select></td>
					<th></th>
					<td></td>
					<th></th>
					<td></td>
				</tr>
				<tr>
					<th>${message("是否启用")}:</th>
					<td><label> <input type="checkbox" name="isEnabled"
							value="true" checked="checked" />${message("16045")} <input
							type="hidden" name="_isEnabled" value="false" />
					</label></td>
					<th></th>
					<td></td>
					<th></th>
					<td></td>
					<th></th>
					<td></td>
				</tr>
				<tr>
					<th>${message("角色")}:</th>
					<td colspan="7"><span class="fieldSet"> [#list roles as
							role] <label> <input id="roleId" type="checkbox"
								name="roleId" value="${role.id}" />${role.name}
						</label>&nbsp;&nbsp;&nbsp; [/#list]
					</span></td>
				</tr>
				<tr>
					<th>${message("头像")}</th>
					<td colspan="7"><input type="hidden" name="imageName"></td>
				</tr>
			</table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton"
				value="${message(" 1013")}" /> <input type="button"
				onclick="location.reload(true);" class="button resetButton ml15"
				value="${message("重置")}">
		</div>
	</form>
</body>
</html>