<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("工程")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
function add(){
	parent.change_tab(0,'/shop/member/add.jhtml');
}
$().ready(function() {
	var types = {'1':'${message("店长")}', '2':'${message("导购员")}', '3':'${message("副店长")}', '4':'${message("副店长")}'};
	var cols = [
		{ title:'${message("门店名称")}', name:'shop_name' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/shop/member/edit.jhtml?id='+item.tsm_id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("姓名")}', name:'name',align:'center'},
		{ title:'${message("电话")}', name:'mobile',align:'center'},
		{ title:'${message("身份证")}', name:'id_card',align:'center'},
		{ title:'${message("职位")}', name:'type',align:'center',renderer:function(val,item){
			var result = types[val];
			if(result!=undefined)return result;
		}},
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/shop/member/list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	
	
});


</script>
</head>
<body>
	<form id="listForm" action="/shop/member/list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<a href="javascript:add();" class="iconButton" id="addButton"><span
					class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div id="searchDiv">
				<div id="search-content">
					<!--   <dl>
        			<dt><p>${message("工程编号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="xx" btn-fun="clear" />
        			</dd>
        		</dl> -->

				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator" style="text-align: left;">
				<div id="paginator"></div>
			</div>
		</div>
	</form>
</body>
</html>