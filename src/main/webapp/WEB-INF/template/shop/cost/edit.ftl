<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript"
	src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
function editQty(t,e){
	extractNumber(t,3,false,e);
}

$().ready(function() {
	var $inputForm = $("#inputForm");
	
	// 表单验证
	$inputForm.validate({
		rules: {
    		
		},
		messages: {
			name: {
				pattern: "${message("非法字符")}",
				remote: "${message("已存在")}"
			}
		}
	});
	
	//地区选择
	$("#newAreaId").lSelect();
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	});

	$("#openStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				var name = isnull(row.name);
				var grant_code = isnull(row.grant_code);
				var active_date = isnull(row.active_date);
				var franchisee = isnull(row.franchisee);
				var sale_org_name = isnull(row.sale_org_name);
				var head_phone = isnull(row.head_phone);
				var fixed_number = isnull(row.fixed_number);
				//切换机构
                $(".saleOrgName").text(row.sale_org_name==null?"":row.sale_org_name);
			}
		}
	 });
	 
	 $("#bankCard").on("keyup",function(){
        //获取当前光标的位置
        var caret = this.selectionStart;
        //获取当前的value
        var value = this.value;
        //从左边沿到坐标之间的空格数
        var sp =  (value.slice(0, caret).match(/\s/g) || []).length;
        //去掉所有空格
        var nospace = value.replace(/\s/g, '');
        //重新插入空格
        var curVal = this.value = nospace.replace(/\D+/g,"").replace(/(\d{4})/g, "$1 ").trim();
        //从左边沿到原坐标之间的空格数
        var curSp = (curVal.slice(0, caret).match(/\s/g) || []).length;
        //修正光标位置
        this.selectionEnd = this.selectionStart = caret + curSp - sp;
    });


	[#if sc.wfId!=null]
	 	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${sc.wfId}");
	 [/#if]

});


function check_wf(e){
    var $this = $(e);
    var $form = $("#inputForm");
    if($form.valid()){
        $.message_confirm("您确定要审批流程吗？",function(){
            //测试环境！！！！
            //var objTypeId = ?;
            // var modelId = ?;
            //开发环境！！！！！
            var objTypeId = 100011;
            var modelId = 332519;
            var url="check_wf.jhtml?id=${sc.id}&modelId="+modelId+"&objTypeId="+objTypeId;
            var data = $form.serialize();
            ajaxSubmit(e,{
                method:'post',
                url:url,
                async: true,
                callback: function(resultMsg) {
                    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                        location.reload(true);
                    })
                }
            });
        });
    }
}

function saveform(e){
    $.message_confirm("您确定要提交吗？",function(){
        //获取表单所有数据
        var params = $("#inputForm").serializeArray();
        //定义url
        var url = 'saveform.jhtml?type='+e;
        ajaxSubmit(e,{
            method:'post',
            url:url,
            data:params,
            async: true,
            callback: function(resultMsg) {
                $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                    location.reload(true);
                })
            }
        });
    });
}
</script>
</head>
<body>
	<div class="pathh">&nbsp;${message("编辑门店费用申请单")}</div>
	<form id="inputForm" action="/shop/cost/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${sc.id}" />
		<div class="tabContent">
			<div class="title-style" style="margin-top: -6px;">${message("经销商信息")}:</div>
			<table class="input input-edit">
				<tr>
					<th>${message("单号")}:</th>
					<td>${sc.sn}</td>
					<th>${message("状态")}:</th>
					<td>
						[#if sc.status == "0"]${message("已保存")}[/#if]
						[#if sc.status == "1"]${message("已提交")}[/#if]
						[#if sc.status == "2"]${message("已终止")}[/#if]
						[#if sc.status == "3"]${message("进行中")}[/#if]
					</td>
					<th>${message("经销商名称")}:</th>
					<td>${sc.store.name} <input type="hidden" name="storeId" value="${sc.store.id}" />
					</td>
					<th>${message("经销商授权编码")}:</th>
					<td>${sc.store.grantCode}</td>
				</tr>
				<tr>
					<th>${message("手机号码")}:</th>
					<td>${sc.store.headPhone}</td>
					<th>${message("地区")}:</th>
					<td colspan="3">
						<input type="hidden" id="newAreaId" name="headNewArea.id" value="${(sc.store.headNewArea.id)!}" treePath="${(sc.store.headNewArea.treePath)!}" />
					</td>
					<th>${message("机构")}:</th>
					<td>
					    <span class="saleOrgName">${sc.saleOrg.name}</span>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("报销信息")}:</div>
			<table class="input input-edit">
				<tr>
					<th>${message("报销事项")}:</th>
					<td><select name="costType" class="text costType">
							<option></option>
							<option value="1" [#if sc.costType==1] selected[/#if]>107.促销返利HS</option>
							<option value="2" [#if sc.costType==2] selected[/#if]>381.利息收入</option>
							<option value="3" [#if sc.costType==3] selected[/#if]>107.地板利息收入</option>
							<option value="4" [#if sc.costType==4] selected[/#if]>107.地板差价</option>
					</select></td>
					<th>${message("项目时间")}:</th>
					<td>
						<input type="text" name="newDate" value="${sc.newDate}" class="text predictConstructionTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" readOnly />
					</td>
					<th>${message("项目金额")}:</th>
					<td>
						<input type="text" name="amount" value="${sc.amount}"
						oninput="editQty (this,event)" class="text" maxlength="50"
						btn-fun="clear" />
					</td>
					<th>${message("货物所属部门")}:</th>
					<td>
						<input type="text" name="department" value="${sc.department}" class="text" maxlength="50" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("经销商开户名")}:</th>
					<td>
						<input type="text" name="accountName" value="${sc.accountName}" class="text" maxlength="50" btn-fun="clear" />
					</td>
					<th>${message("银行帐号")}:</th>
					<td>
						<input type="tel" name="bankNumber" value="${sc.bankNumber}" id="bankCard" placeholder="" maxlength="23" class="text">
					</td>
					<th>${message("开户银行")}:</th>
					<td>
						<input type="text" name="openingBank" value="${sc.openingBank}" class="text" maxlength="200" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("付款方式")}:</th>
					<td>
						<label><input type="radio" name="payType" value="1" [#if sc.payType== "1"] checked="checked" [/#if] />银行</label>&nbsp;&nbsp;
						<label><input type="radio" name="payType" value="0" [#if sc.payType== "0"] checked="checked" [/#if] />现金</label>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("项目细节")}:</div>
			<table class="input input-edit" style="width: 500px; margin-top: 5px;">
				<tr>
					<td colspan="7">
						<textarea class="text" name="ac" style="width: 800px; height: 160px;">${sc.ac}</textarea>
					</td>
				</tr>
			</table>

			[#if sc.wfId != null]
			[#if qyjl||qyjls]
				<div class="title-style">${message("区域经理审核")}:[#if node.name?contains("区域经理")]<input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/>[/#if]</div>
				<table class="input input-edit" style="width: 500px; margin-top: 5px;">
					<tr>
						<td colspan="7">
							<textarea class="text" name="regionalManagerText" style="width: 800px; height: 160px;">${sc.regionalManagerText}</textarea>
						</td>
					</tr>
				</table>
			[/#if]
				[#if szsh||szshs]
				<div class="title-style">${message("省长审核")}:[#if node.name?contains("省长")]<input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/>[/#if]</div>
				<table class="input input-edit" style="width: 500px; margin-top: 5px;">
					<tr>
						<td colspan="7">
							<textarea class="text" name="provincialOperationText" style="width: 800px; height: 160px;">${sc.provincialOperationText}</textarea>
						</td>
					</tr>
				</table>
				[/#if]

				[#if cwb||cwbs]
				<div class="title-style">${message("财务部门审核")}:[#if node.name?contains("财务")]<input type="button" class="bottonss tj" onclick="saveform(3)" value="提交"/>[/#if]</div>
				<table class="input input-edit" style="width: 500px; margin-top: 5px;">
					<tr>
						<td colspan="7"><textarea class="text" name="cc" style="width: 800px; height: 160px;">${sc.cc}</textarea></td>
					</tr>
				</table>
				[/#if]
			[/#if]
			</div>
		<div class="fixed-top">
			[#if sc.wfId == null]
			<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>

	</form>
	<div id="wf_area" style="width: 100%"></div>
</body>
</html>