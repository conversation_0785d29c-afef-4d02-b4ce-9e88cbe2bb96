<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript"
	src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
function editQty(t,e){
	extractNumber(t,3,false,e);
}
$().ready(function() {

		var $inputForm = $("#inputForm");
		
		// 表单验证
		$inputForm.validate({
			rules: {
	    		
			},
			messages: {
				name: {
					pattern: "${message("非法字符")}",
					remote: "${message("已存在")}"
				}
			}
		});
		
		//地区选择
		$("#newAreaId").lSelect();
		
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= '/shop/cost/edit.jhtml?id='+resultMsg.objx;
				});
			}
		 });
         
         $("#openStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			url:'/member/store/select_store.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					var name = isnull(row.name);
					var grant_code = isnull(row.grant_code);
					var active_date = isnull(row.active_date);
					var franchisee = isnull(row.franchisee);
					var sale_org_name = isnull(row.sale_org_name);
					var head_phone = isnull(row.head_phone);
					var fixed_number = isnull(row.fixed_number);
					//切换机构
                    $(".saleOrgName").text(sale_org_name==null?"":row.sale_org_name);
				}
			}
		 });
		 
		  $("#bankCard").on("keyup",function(){
	        //获取当前光标的位置
	        var caret = this.selectionStart;
	        //获取当前的value
	        var value = this.value;
	        //从左边沿到坐标之间的空格数
	        var sp =  (value.slice(0, caret).match(/\s/g) || []).length;
	        //去掉所有空格
	        var nospace = value.replace(/\s/g, '');
	        //重新插入空格
	        var curVal = this.value = nospace.replace(/\D+/g,"").replace(/(\d{4})/g, "$1 ").trim();
	        //从左边沿到原坐标之间的空格数
	        var curSp = (curVal.slice(0, caret).match(/\s/g) || []).length;
	        //修正光标位置
	        this.selectionEnd = this.selectionStart = caret + curSp - sp;
	    }); 
});

</script>
</head>
<body>
	<div class="pathh">&nbsp;${message("新增门店费用申请单")}</div>
	<form id="inputForm" action="/shop/cost/save.jhtml" method="post"
		type="ajax" validate-type="validate">
		<div class="tabContent">
			<div class="title-style" style="margin-top: -6px;">${message("经销商信息")}:</div>
			<table class="input input-edit">
				<tr>
					<th>${message("经销商名称")}:</th>
					<td>${store.name} <input type="hidden" name="storeId"
						value="${store.id}" />
					</td>
					<th>${message("经销商授权编码")}:</th>
					<td>${store.grantCode}</td>
					<th>${message("手机号码")}:</th>
					<td>${store.headPhone}</td>
				</tr>
				<tr>
					<th>${message("机构")}:</th>
					<td>
					    <span class="saleOrgName"></span>
					</td>
					<th>${message("地区")}:</th>
					<td colspan="3"><input type="hidden" id="newAreaId"
						name="headNewArea.id" value="${(store.headNewArea.id)!}"
						treePath="${(store.headNewArea.treePath)!}" /></td>
				</tr>
			</table>
			<div class="title-style">${message("报销信息")}:</div>
			<table class="input input-edit">
				<tr>
					<th>${message("报销事项")}:</th>
					<td><select name="costType" class="text costType">
							<option></option>
							<option value="1">107.促销返利HS</option>
							<option value="2">381.利息收入</option>
							<option value="3">107.地板利息收入</option>
							<option value="4">107.地板差价</option>
					</select></td>
					<th>${message("项目时间")}:</th>
					<td><input type="text" name="newDate"
						class="text predictConstructionTime" maxlength="200"
						onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});"
						readOnly /></td>
					<th>${message("项目金额")}:</th>
					<td><input type="text" name="amount"
						oninput="editQty (this,event)" class="text" maxlength="50"
						btn-fun="clear" /></td>
					<th>${message("货物所属部门")}:</th>
					<td><input type="text" name="department" class="text"
						maxlength="50" btn-fun="clear" /></td>
				</tr>
				<tr>
					<th>${message("经销商开户名")}:</th>
					<td><input type="text" name="accountName" class="text"
						maxlength="50" btn-fun="clear" /></td>
					<th>${message("银行帐号")}:</th>
					<td><input type="tel" name="bankNumber" id="bankCard"
						placeholder="" maxlength="23" class="text"></td>
					<th>${message("开户银行")}:</th>
					<td><input type="text" name="openingBank" class="text"
						maxlength="50" btn-fun="clear" /></td>
				</tr>
				<tr>
					<th>${message("付款方式")}:</th>
					<td><label><input type="radio" name="payType"
							value="1" />银行</label>&nbsp;&nbsp; <label><input type="radio"
							name="payType" value="0" checked="checked" />现金</label></td>
				</tr>
			</table>
			<div class="title-style">${message("项目细节")}:</div>
			<table class="input input-edit"
				style="width: 500px; margin-top: 5px;">
				<tr>
					<td colspan="7"><textarea class="text" name="ac"
							style="width: 800px; height: 160px;"></textarea></td>
				</tr>
			</table>
			<#--
			<div class="title-style">${message("业务部门审核")}:</div>
			<table class="input input-edit"
				style="width: 500px; margin-top: 5px;">
				<tr>
					<td colspan="7"><textarea class="text" name="bc"
							style="width: 800px; height: 160px;"></textarea></td>
				</tr>
			</table>
			<div class="title-style">${message("财务部门审核")}:</div>
			<table class="input input-edit"
				style="width: 500px; margin-top: 5px;">
				<tr>
					<td colspan="7"><textarea class="text" name="cc"
							style="width: 800px; height: 160px;"></textarea></td>
				</tr>
			</table>
			-->
		</div>

		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("保存")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>