<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增门店装修验收及报销")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
/* 调整附件 */
.nowrap > div >.t-edit {
    width: 80%;
    margin: 5px 0 -6px 0;
}
</style>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e);
}
function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	// 校验
	$inputForm.validate({
		rules: {
			shopInfoSn: "required",
			acceptanceLeaseTerm: "required",
            acceptanceArea: "required",
            decorateConstructionTime: "required",
            decorateCompleteTime: "required",
            acceptanceVerifyArea: "required",
            acceptanceVerifyScore: "required",
		} ,
	});
	
	// 选择门店
	$("#selectShopInfo").click(function(){
		$("#selectShopInfo").bindQueryBtn({
			type:'shopInfo',
			title:'${message("查询门店")}',
			bindClick:false,
			url:'/shop/shopInfo/select_shopInfo.jhtml?multi=1&reimburse=2&shopStatus='+'开店中'+'&shopStatus='+'正在营业'+'&off=true',
			callback:function(rows){
				if(rows.length > 0){
					var row = rows[0];
					var brand = new Array("大自然综合店","大自然·三层专卖店","大自然·实木专卖店","大自然·强化专卖店");
					$(".shopInfoSn").val(row.sn);
                    $(".shopInfoId").val(row.id);
                    $(".dealerText").html(row.distributor_name);
                    $(".dealer").val(row.distributor_name);
                    $(".cityText").html(row.area_name);  // 省份城市
                    $(".city").val(row.area_name);
                    $(".dealerPhoneText").html(row.distributor_phone);
                    $(".dealerPhone").val(row.distributor_phone);
                    $(".regionalManagerText").html(row.xujl_name); // 区域经理
                    $(".regionalManager").val(row.xujl_name);
                    $(".regionalManagerPhoneText").html(row.xujl_mobile); // 区域经理电话
                    $(".regionalManagerPhone").val(row.xujl_mobile);
                    $(".shopAddressText").html(row.address); // 地址
                    $("#shopAddress").val(row.address);
                    $(".shopInfoShopNameText").html(row.shop_name);
                    $(".shopInfoShopName").val(row.shop_name);
                    $(".transitTime").val(row.transit_time);
					$(".shopDeviseId").val(row.devise_id);
					$(".shopDeviseSn").text(row.devise_sn);
					$(".structure1").text(row.structure1);
					$(".structure2").text(row.structure2);
					$(".shopRenovationAttribute").text(row.shop_renovation_attribute);
					$(".highLimit").text(row.high_limit);
					$(".designBrandText").html(isNull(brand[row.design_brand]));
					$(".designBrand").text(row.design_brand);
					$(".area").text(row.area);
                    $("#predictConstructionTimeText").html(row.predict_construction_time==null?"":row.predict_construction_time.substring(0,10));
                    $("#predictConstructionTime").val(row.predict_construction_time==null?"":row.predict_construction_time.substring(0,10));
                    $("#predictStartsTimeText").html(row.predict_starts_time==null?"":row.predict_starts_time.substring(0,10));
                    $("#predictStartsTime").val(row.predict_starts_time==null?"":row.predict_starts_time.substring(0,10));


                    //切换机构
                    $(".saleOrgName").text(row.sale_org_name==null?"":row.sale_org_name);
				}
			}
		});
	});
	
	// 选择设计
	$("#selectShopDevise").click(function(){
		$("#selectShopDevise").bindQueryBtn({
			type:'shopInfo',
			title:'${message("查询门店")}',
			bindClick:false,
			url:'/shop/devise/select_shop_devise.jhtml',
			callback:function(rows){
				if(rows.length > 0){
					var row = rows[0];
					var brand = new Array("大自然综合店","大自然·三层专卖店","大自然·实木专卖店","大自然·强化专卖店");
					$(".shopDeviseSn").val(row.sn);
                    $(".shopDeviseId").val(row.id);
                    $("#structure1").html(row.structure1);
                    $("#structure2").html(row.structure2);
                    $("#shopAttributeText").html(row.shop_attribute);
                    $("#shopAttribute").val(row.shop_attribute);
                    $("#highLimitText").html(row.high_limit);
                    $("#highLimit").val(row.high_limit);
                    $("#designBrandText").html(isNull(brand[row.design_brand]));
                    $("#designBrand").val(row.design_brand);
                    $("#areaText").html(row.area);
                    $("#area").val(row.area);
                    $("#predictConstructionTimeText").html(row.predict_construction_time==null?"":row.predict_construction_time.substring(0,10));
                    $("#predictConstructionTime").val(row.predict_construction_time==null?"":row.predict_construction_time.substring(0,10));
                    $("#predictStartsTimeText").html(row.predict_starts_time==null?"":row.predict_starts_time.substring(0,10));
                    $("#predictStartsTime").val(row.predict_starts_time==null?"":row.predict_starts_time.substring(0,10));
				}
			}
		});
	});

	// 初始化附件
	initAttach("storePictureAttachs", "addStorePictureAttach", "table-store-picture-attach", 2);
	initAttach("storeContractAttachs", "addStoreContractAttach", "table-store-contract-attach", 1);
	initAttach("acceptanceCommitmentAttachs", "addAcceptanceCommitmentAttach", "table-acceptance-commitment-attach", 0);
	initAttach("decorate1Attachs", "addDecorate1Attach", "table-decorate1-attach", 11);
	initAttach("decorate2Attachs", "addDecorate2Attach", "table-decorate2-attach", 12);
	initAttach("decorate3Attachs", "addDecorate3Attach", "table-decorate3-attach", 13);
	initAttach("decorate4Attachs", "addDecorate4Attach", "table-decorate4-attach", 14);
	initAttach("decorate5Attachs", "addDecorate5Attach", "table-decorate5-attach", 15);
	
});

/**
 * 初始化附件，每个参数都是必填的
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param addAttachIdName 前端页面添加附件按钮的id值
 * @param tableIdName 前端页面table中的id值 
 * @param type 后台用来区分不同类型附件
 */
function initAttach(paramAttachs, addAttachIdName, tableIdName, type) {
    var index = 0;
	var attachCols = [				
    	{ title:'${message("附件")}',name:'content',width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues[paramAttachs+'['+index+'].url']=url;
			hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
			hideValues[paramAttachs+'['+index+'].type']=type;  // 装修验收照片附件1
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName: paramAttachs+'['+index+'].name',
				hideValues:hideValues
			});
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
    		index++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $grid=$('#'+tableIdName).mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: attachCols,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#"+addAttachIdName);
	var attachIdnex = 0;
	var option = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$grid.addRow(row,null,1);
	        }
        }
    }
    $addAttach.file_upload(option);
	
	/* 删除附件 */
    var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
}

// 保存
function save(e){
	var str = '您确定要保存吗？';
	var url = 'save.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= 'edit.jhtml?id='+resultMsg.objx;
				})
			}
		});
	}
}

// 只有选中复选框后才能编辑后面的文本框
function handle(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').removeAttr("readonly");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').attr("readonly", "readonly");
		$pdiv.find('input[type=text]').val("");
	}
}
// 只有选中复选框后才能编辑后面的日期
function handleDate(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').removeAttr("disabled");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').attr("disabled", "disabled");
		$pdiv.find('input[type=text]').val("");
	}
}
// 只有选中复选框后才能编辑后面的单选框
function handleRadio(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=radio]').removeAttr("disabled");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=radio]').attr("disabled", "disabled");
		$pdiv.find('input[type=radio]').removeAttr("checked");
	}
}

// 选中同意复选框时，显示隐藏的内容
function agreeCheck(e) {
	if ($(e).prop('checked') == true) {
        var $pdiv = $(e).parent("label").parent("div");
        $pdiv.siblings('.last-check').find('input[type=checkbox]').attr("checked", false);
        $pdiv.siblings('.disp').css("display", "inline");
    } else {
    	var $pdiv = $(e).parent("label").parent("div");
        $pdiv.siblings('.disp').css("display", "none");
        $pdiv.siblings('.disp').find('input[type=text]').val("");
        $pdiv.siblings('.disp').find('input[type=checkbox]').attr("checked", false);
        $pdiv.siblings('.disp').find('input[type=radio]').attr("checked", false);
    }
}
//选中不同意复选框时，显示隐藏的内容
function disagreeCheck(e) {
	if ($(e).prop('checked') == true) {
        var $pdiv = $(e).parent("div");
        $pdiv.siblings('.first-check').find('input[type=checkbox]').attr("checked", false);
        $pdiv.siblings('.disp').css("display", "none");
        $pdiv.siblings('.disp').find('input[type=text]').val("");
        $pdiv.siblings('.disp').find('input[type=checkbox]').attr("checked", false);
        $pdiv.siblings('.disp').find('input[type=radio]').attr("checked", false);
    }
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增门店装修验收及报销")}
	</div>
	<form id="inputForm" action="/shop/acceptance_reimburse/save.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="type" value="1" class="text" maxlength="200" btn-fun="clear" />
		<input type="hidden" name="transitTime" value="" class="text transitTime" maxlength="200" btn-fun="clear" />
		<div class="tabContent">
			<div style="margin:0 0 10px 0; font-size:15px;">
				${message("门店资料")}
			</div>
			<table class="input input-edit">
				<tr>
					<th>${message("单号")}:</th>
					<td>
					</td>
					<th>${message("单据状态")}:</th>
					<td>
						<!-- <input type="text" class="text" name="status" btn-fun="clear" readonly="readonly" /> -->
					</td>
					<th>${message("门店编码")}:</th>
                    <td>
                        <span class="search" style="position:relative">
                            <input type="hidden" name="shopInfoId" class="text shopInfoId" value="" btn-fun="clear"/>
                            <input type="text" name="shopInfoSn" class="text shopInfoSn" value="" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
                            <input type="button" class="iconSearch" value="" id="selectShopInfo"/>
                        </span>
                    </td>
                    <th>${message("经销商姓名")}:</th>
                    <td>
                        <span class="dealerText"></span>
                        <input type="hidden" class="text dealer" name="dealer" btn-fun="clear" />
                    </td>
				</tr>
				<tr>
                    <th>${message("省份城市")}:</th>
                    <td>
                        <span class="cityText"></span>
                        <input type="hidden" class="text city" name="city" maxlength="200" btn-fun="clear" />
                    </td>
                    <th>${message("经销商联系电话")}:</th>
                    <td>
                        <span class="dealerPhoneText"></span>
                        <input type="hidden" class="text dealerPhone" name="dealerPhone" btn-fun="clear" />
                    </td>
                    <th>${message("区域经理姓名")}:</th>
                    <td>
                        <span class="regionalManagerText"></span>
                        <input type="hidden" class="text regionalManager" name="regionalManager" btn-fun="clear" />
                    </td>
					<th>${message("区域经理联系电话")}:</th>
                    <td>
                        <span class="regionalManagerPhoneText"></span>
                        <input type="hidden" class="text regionalManagerPhone" name="regionalManagerPhone" btn-fun="clear" />
                    </td>
				</tr>
				<tr>
                    <th>${message("门店详细地址")}:</th>
                    <td colspan="3">
                        <span class="shopAddressText"></span>
                        <input type="hidden" class="text shopAddress" name="shopAddress" id="shopAddress" value="" maxlength="200" btn-fun="clear" readonly="readonly" />
                    </td>
					<th>${message("门店名称")}:</th>
					<td>
						<span class="shopInfoShopNameText"></span>
						<input type="hidden" class="test shopInfoShopName" name="shopInfoShopName" btn-fun="clear" />
					</td>
                    <th>${message("机构")}:</th>
					<td>
					    <span class="saleOrgName"></span>
					</td>
				</tr>
			</table>
			
			<div style="margin:10px 0 10px 0; font-size:15px;">
                ${message("设计资料")}
            </div>
            <table class="input input-edit">
                <tr>
                    <th>${message("设计单号")}:</th>
                    <td>
                        <!--
                        <span class="search" style="position:relative">
                            <input type="hidden" name="shopDeviseId" class="text shopDeviseId" value="${sd.id}" btn-fun="clear"/>
                            <input type="text" name="shopDeviseSn" class="text shopDeviseSn" value="${sd.sn}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
                            <input type="button" class="iconSearch" value="" id="selectShopDevise"/>
                        </span>
                        -->
                        <span class="shopDeviseSn"></span>
						<input type="hidden" name="shopDeviseId" class="text shopDeviseId" value="${sd.id}">
                    </td>
                    <th>${message("门店结构情况")}:</th>
                    <td>
                        <span class="structure1"></span>&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="structure2"></span>
                        <!-- <select name="" class="text" style="width:49%" disabled="disabled">
                            <option value="" id="structure1">${sd.structure1}</option>
                        </select>
                        <select name="" class="text" style="width:49%" disabled="disabled">
                            <option value="" id="structure2">${sd.structure2}</option>
                        </select> -->
                    </td>
                    <th>${message("门店装修属性")}:</th>
                    <td>
                        <span class="shopRenovationAttribute"></span>
                        <!--<input type="hidden" name="shopAttribute" id="shopAttribute" value="${sd.shopAttribute}" class="text" btn-fun="clear" readonly="readonly"></input>-->
                    </td>
                    <th>${message("天花限高(米)")}:</th>
                    <td>
                        <span class="highLimit" ></span>
                        <!--<input type="hidden" name="" id="highLimit" value="${sd.highLimit}" class="text" btn-fun="clear" readonly="readonly"></input>-->
                    </td>
                </tr>
                <tr>
                    <th>${message("设计品牌")}:</th>
                    <td>
                    	<span class="designBrandText"></span>
                        <!--<input type="hidden" class="text" name="" id="designBrand" value="${sd.designBrand}" maxlength="200" btn-fun="clear" readonly="readonly" />-->
                    </td>
                    <th>${message("面积(㎡)")}:</th>
                    <td>
                        <span class="area" ></span>
                        <!--<input type="hidden" name="area" id="area" class="text" value="${sd.area}" btn-fun="clear" readonly="readonly"></input>-->
                    </td>
                    <th>${message("预订施工时间")}</th>
                    <td>
                        <span id="predictConstructionTimeText"></span>
                        <input type="hidden" name="predictConstructionTime" id="predictConstructionTime" class="text" value="${sd.predictConstructionTime}" maxlength="200" readonly="readonly" />
                    </td>
                    <th>${message("预订开业时间")}</th>
                    <td>
                        <span id="predictStartsTimeText"></span>
                        <input type="hidden" name="predictStartsTime" id="predictStartsTime" class="text" value="${sd.predictStartsTime}" maxlength="200" readonly="readonly" />
                    </td>
                </tr>
            </table>
            [#--
             <div style="margin:10px 0 10px 0; font-size:15px;">
				${message("渠道部填写")}
			</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			<input type="text" name="shopAuthorizationCodes" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增档案编号")}:</th>
            		<td>
            			<input type="text" name="archivesCodes" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增时间")}:</th>
            		<td>
            			<input id="startTime" name="newTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            	</tr>
            </table>
            --]
			
			<div class="title-style">
				${message("验收申请承诺")}
				<div class="" style="border:1px solid #dcdcdc; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<pre style="font:13px 'Microsoft YaHei';">
1、	确定按公司设计图纸严格按图施工；如有与图纸不符，将接受公司的相关裁定。
2、	确定按照软装、灯具清单购置相关标准配饰物料；如购置不规范，将视为接受直接折扣处理，不做整改。
3、	确定按设计规范设置辅料产品展示区，如无辅料区，将扣除报销总金额的10%处理，不做整改。
4、	由于经销商个人原因，要求重复设计的门店，将扣除报销总金额的10%处理。
5、	确认以下资料提交齐全（□处打√）
					</pre>
					<div>
						<div style="margin:0px 0 10px 0;">
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment1" type="checkbox" value="0" /><label for="acceptanceCommitment1"> 门店平面图核实面积且签字</label>&nbsp;&nbsp;&nbsp;&nbsp;
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment2" type="checkbox" value="1" /><label for="acceptanceCommitment2"> 提交门店装修合同复印件</label>
						</div>
						<div>
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment3" type="checkbox" value="2" onchange="handle(this)" />
							<label for="acceptanceCommitment3"> 提交门店租赁合同/房产证复印件：租赁期限</label>
							<input class="t acreage" name="acceptanceLeaseTerm" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" readonly="readonly" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
							<span>年，合同门店面积</span>
							<input class="t acreage" name="acceptanceArea" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" readonly="readonly" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
							<span>㎡</span>
						</div>
					</div>
			    </div>
			</div>
		    
		<table class="input input-edit" style="width:100%;margin-top:5px;">
		    <div class="title-style">
				${message("上传以上确定附件：门店平面图、门店租赁合同/房产复印件、门店装修合同复印件")}
			</div>
			<tr>
				<th>${message("门店平面图")}:</th>
				<td>
					<a href="javascript:;" id="addStorePictureAttach" class="button">添加附件</a>
				</td>
				<th>${message("门店租赁合同/房产复印件")}:</th>
				<td>
					<a href="javascript:;" id="addStoreContractAttach" class="button">添加附件</a>
				</td>
				<th>${message("门店装修合同复印件")}:</th>
				<td>
					<a href="javascript:;" id="addAcceptanceCommitmentAttach" class="button">添加附件</a>
				</td>
			</tr>
		</table>
		<div>
        	<span>${message("门店平面图")}</span>
	        <table id="table-store-picture-attach" style="width:850px"></table>
	        <span>${message("门店租赁合同/房产复印件")}</span>
	        <table id="table-store-contract-attach" style="width:850px"></table>
	        <span>${message("门店装修合同复印件")}</span>
			<table id="table-acceptance-commitment-attach" style="width:850px"></table>
        </div>
			[#--
			<div class="title-style" >
				${message("验收人员核实（区域经理、设计师）")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 8px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify1" type="checkbox" value="0" onchange="handleDate(this)" /> -->
						<label for="acceptanceVerify1">经确认，装修施工时间：</label>
						<input type="text" style="width:190px;" name="decorateConstructionTime" id="d4311" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',maxDate:'#F{$dp.$D(\'d4312\')}'});" />
					</div>
					<div style="margin:0 0 8px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify2" type="checkbox" value="1" onchange="handleDate(this)" /> -->
						<label for="acceptanceVerify2">经确认，装修完成时间：</label>
						<input type="text" style="width:190px;" name="decorateCompleteTime" id="d4312" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',minDate:'#F{$dp.$D(\'d4311\')}'});" />
					</div>
					<div style="margin:0 0 12px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify3" type="checkbox" value="2" onchange="handleRadio(this)" /> -->
						<label for="acceptanceVerify3">经确认，门店装修属性：</label>
						<input class="check js-iname" name="acceptanceShop" id="acceptanceShop1" type="radio" value="重装店" />
						<label for="acceptanceShop1">重装店</label>&nbsp;&nbsp;&nbsp;&nbsp;
						<input class="check js-iname" name="acceptanceShop" id="acceptanceShop2" type="radio" value="新店" />
						<label for="acceptanceShop2">新店</label>
					</div>
					<div style="margin:0 0 12px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify4" type="checkbox" value="3" onchange="handle(this)" /> -->
						<label for="acceptanceVerify4" >经核实，有效面积：</label>
						<input class="t acreage" name="acceptanceVerifyArea" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>㎡</span>
					</div>
					<div style="margin:0 0 8px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify5" type="checkbox" value="4" onchange="handle(this)" /> -->
						<label for="acceptanceVerify5">经审核，评分表得分为：</label>
						<input class="t acreage" name="acceptanceVerifyScore" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>分</span>
					</div>
				</div>
			</div>
			
			<div class="title-style" >
				${message("门店装修验收照片附件")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:0px 15px 10px;">
					<div class="title-style">门头＋外墙、形象墙/植物墙、外墙材料高清照、收银区
						<div class="btns">
							<a href="javascript:;" id="addDecorate1Attach" class="button">添加附件</a>
						</div >
					</div>
					<table id="table-decorate1-attach"></table>
					<div class="title-style">野生实木区两张、实木复合展示区两张
						<div class="btns">
							<a href="javascript:;" id="addDecorate2Attach" class="button">添加附件</a>
						</div>
					</div>
					<table id="table-decorate2-attach"></table>
					<div class="title-style">康德展示区、4+2展示区、1530专区、进口三层区
						<div class="btns">
							<a href="javascript:;" id="addDecorate3Attach" class="button">添加附件</a>
						</div>
					</div>
					<table id="table-decorate3-attach"></table>
					<div class="title-style">强化地板欧式展示区两张、强化地板中式简约区两张
						<div class="btns">
							<a href="javascript:;" id="addDecorate4Attach" class="button">添加附件</a>
						</div>
					</div>
					<table id="table-decorate4-attach"></table>
					<div class="title-style">橡木区/柚木区/其他、五红水吧文化区、辅料区、验收人合影照
						<div class="btns">
							<a href="javascript:;" id="addDecorate5Attach" class="button">添加附件</a>
						</div>
					</div>
					<table id="table-decorate5-attach"></table>
				</div>
			</div>
			
			<div class="title-style" >
				${message("省运营管理中心意见")}:
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div>
						<textarea rows="3" name="provincialOperationMemo" style="width:85%; border:1px solid #dcdcdc; padding:5px;"></textarea>
					</div>
				</div>
			</div>
			
			<div class="title-style" >
				${message("终端设计经理意见")}:
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
				    <div style="margin:0 0 10px 0;" class="first-check">
                        <label><input class="check js-iname" name="designManagers" type="checkbox" value="4" onchange="agreeCheck(this)" /> 经审核，合格。</label>
                    </div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
					    &nbsp;&nbsp;&nbsp;&nbsp;
						<!-- <input class="check js-iname" name="designManagers" id="designManager1" type="checkbox" value="0" onchange="handleDate(this)" /> -->
						<label for="designManager1">经确认，施工图完成时间：</label>
						<input type="text" style="width:190px;" name="constructionFigureCompleteTime" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
						&nbsp;&nbsp;&nbsp;&nbsp;
					</div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
						<!-- <input class="check js-iname" name="designManagers" id="designManager2" type="checkbox" value="1" onchange="handle(this)" /> -->
						<label for="designManager2">经核实，套内有效营业面积：</label>
						<input class="t acreage" name="designManagerArea" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>㎡，验收合格。</span>
						&nbsp;&nbsp;&nbsp;&nbsp;
					</div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
						<!-- <input class="check js-iname" name="designManagers" id="designManager3" type="checkbox" value="2" onchange="handle(this)" /> -->
						<label for="designManager3">经审核，得分为：</label>
						<input class="t acreage" name="designManagerScore" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>分。</span>
					</div>
					<div style="margin:6px 0 10px 0;" class="last-check">
						<input class="check js-iname" name="designManagers" id="designManager4" type="checkbox" value="3" onchange="disagreeCheck(this)" />
						<label for="designManager4">经审核，不合格。</label>
					</div>
					<div>
						<div style="margin:0 0 5px 0;">备注:</div>
						<textarea rows="3" name="designManagerMemo" style="width:85%; border:1px solid #dcdcdc;padding:5px;"></textarea>
					</div>
				</div>
			</div>
			
			<div class="title-style" >
				${message("渠道总监意见：")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
				    <div style="margin:0 0 10px 0;" class="first-check">
                        <label><input class="check js-iname agree" name="directorOpinions" type="checkbox" value="4" onchange="agreeCheck(this)" /> 同意报销。</label>
                    </div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
						<input class="check js-iname" name="directorOpinions" id="directorOpinion1" type="checkbox" value="0" />
						<label for="directorOpinion1">2年内未参与装修报销</label>&nbsp;&nbsp;&nbsp;&nbsp;
						<input class="check js-iname" name="directorOpinions" id="directorOpinion2" type="checkbox" value="1" />
						<label for="directorOpinion2">签署2019年经销合同</label>&nbsp;&nbsp;&nbsp;&nbsp;
						<label for="">缴纳齐全品牌保证金：</label>
						<input class="check js-iname" name="payDeposit" id="payDeposit1" type="radio" value="1" /><label for="payDeposit1"> 是 </label>&nbsp;&nbsp;
						<input class="check js-iname" name="payDeposit" id="payDeposit2" type="radio" value="0" /><label for="payDeposit2"> 否 </label>
					</div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
						<!-- <input class="check js-iname" name="directorOpinions" id="directorOpinion3" type="checkbox" value="2" onchange="handle(this)" /> -->
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<label for="directorOpinion3">经审核，报销标准</label>
						<input class="t acreage" name="directorOpinionMoney1" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>元/㎡，报销金额</span>
						<input class="t acreage" name="directorOpinionMoney2" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>元，扣除品牌保证金</span>
						<input class="t acreage" name="directorOpinionMoney3" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>元</span>
					</div>
					<div style="margin:6px 0 10px 0;" class="last-check">
						<input class="check js-iname disagree" name="directorOpinions" id="directorOpinion4" type="checkbox" value="3" onchange="disagreeCheck(this)" />
						<label for="directorOpinion4">不同意报销。</label>
					</div>
					<div>
                        <div style="margin:0 0 5px 0;">备注:</div>
                        <textarea rows="3" name="directorOpinionMemo" style="width:85%; border:1px solid #dcdcdc;padding:5px;"></textarea>
                    </div>
				</div>
			</div>
			
			<div class="title-style" >
				${message("地板事业部总经理意见：")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 10px 0;">
						<input class="check js-iname" name="floorManagerOpinion" id="floorManagerOpinion1" type="radio" value="1" />
						<label for="floorManagerOpinion1"> 同意报销。</label>
					</div>
					<div>
						<input class="check js-iname" name="floorManagerOpinion" id="floorManagerOpinion2" type="radio" value="0" />
						<label for="floorManagerOpinion2"> 不同意报销。</label>
					</div>
				</div>
			</div>
			--]
		</div>
		<div class="fixed-top">
			<!-- <input type="submit" id="submit_button" class="button sureButton" value="${message("保存")}" /> -->
			<input type="button" id="submit_button" onclick="save(this)" class="button sureButton" value="${message("保存")}" />
			<input type="button" id="submit_button" class="button sureButton" value="${message("提交")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>