<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑门店装修验收及报销")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
.ge-btn {
	background-color: #4CAF50;
	border: none;
	color: white;
	padding: 8px 15px;
	text-align: center;
	text-decoration: none;
	display: inline-block;
	font-size: 13px;
	margin: 5px 5px;
	cursor: pointer;
	border-radius: 5px;
}
/* 调整附件 */
.nowrap > div >.t-edit {
    width: 80%;
    margin: 5px 0 -6px 0;
}
.bottonss{
    border: 1px solid #0000;
    height: 32px;
    width: 66px;
    background-color: #2190e8;
    border-radius: 5px;
    color: #fff;
    font-size: 14px;
    font-family: Microsoft YaHei;
    cursor:pointer;
    text-align:center;
}
.bottonss:hover{
	background-color:#5594c5;
}
.tj{
    position: absolute;
	right: 15px;
}
</style>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e);
}
function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	// 校验
	$inputForm.validate({
		rules: {
			shopInfoSn: "required",
            acceptanceLeaseTerm: "required",
            acceptanceArea: "required",
            decorateConstructionTime: "required",
            decorateCompleteTime: "required",
            acceptanceVerifyArea: "required",
            acceptanceVerifyScore: "required",
        }
	});
	
	// 选择门店
	$("#selectShopInfo").click(function(){
		$("#selectShopInfo").bindQueryBtn({
			type:'shopInfo',
			title:'${message("查询门店")}',
			bindClick:false,
			url:'/shop/shopInfo/select_shopInfo.jhtml',
			callback:function(rows){
				if(rows.length > 0){
					var row = rows[0];
					$(".shopInfoSn").val(row.sn);
                    $(".shopInfoId").val(row.id);
                    $(".dealerText").html(row.distributor_name);
                    $(".dealer").val(row.distributor_name);
                    $(".cityText").html(row.area_name);  // 省份城市
                    $(".city").val(row.area_name);
                    $(".dealerPhoneText").html(row.distributor_phone);
                    $(".dealerPhone").val(row.distributor_phone);
                    $(".regionalManagerText").html(row.xujl_name); // 区域经理
                    $(".regionalManager").val(row.xujl_name);
                    $(".regionalManagerPhoneText").html(row.xujl_mobile); // 区域经理电话
                    $(".regionalManagerPhone").val(row.xujl_mobile);
                    $(".shopAddressText").html(row.address); // 地址
                    $("#shopAddress").val(row.address);
                    //切换机构
                    $(".saleOrgName").text(row.sale_org_name==null?"":row.sale_org_name);
				}
			}
		});
	});
	
	// 选择设计
	$("#selectShopDevise").click(function(){
		$("#selectShopDevise").bindQueryBtn({
			type:'shopInfo',
			title:'${message("查询门店")}',
			bindClick:false,
			url:'/shop/devise/select_shop_devise.jhtml',
			callback:function(rows){
				if(rows.length > 0){
					var row = rows[0];
					var brand = new Array("大自然综合店","大自然·三层专卖店","大自然·实木专卖店","大自然·强化专卖店");
					$(".shopDeviseSn").val(row.sn);
                    $(".shopDeviseId").val(row.id);
                    $("#structure1").html(row.structure1);
                    $("#structure2").html(row.structure2);
                    $("#shopAttributeText").html(row.shop_attribute);
                    $("#shopAttribute").val(row.shop_attribute);
                    $("#highLimitText").html(row.high_limit);
                    $("#highLimit").val(row.high_limit);
                    $("#designBrandText").html(isNull(brand[row.design_brand]));
                    $("#designBrand").val(row.design_brand);
                    $("#areaText").html(row.area);
                    $("#area").val(row.area);
                    $("#predictConstructionTimeText").html(row.predict_construction_time == null ? "" : row.predict_construction_time.substring(0,10));
                    $("#predictConstructionTime").val(row.predict_construction_time == null ? "" :row.predict_construction_time.substring(0,10));
                    $("#predictStartsTimeText").html(row.predict_starts_time == null ? "": row.predict_starts_time.substring(0,10));
                    $("#predictStartsTime").val(row.predict_starts_time == null ? "" : row.predict_starts_time.substring(0,10));
				}
			}
		});
	});
	
	// 初始化终端设计经理意见
	if ($('#agreeDesignManagers').prop('checked') == true) {
		$('#agreeDesignManagers').parent("label").parent("div").siblings('.disp').css("display", "inline");
	}
	
	// 初始化渠道总监意见
	if ($('#agreeDirectorOpinions').prop('checked') == true) {
		$('#agreeDirectorOpinions').parent("label").parent("div").siblings('.disp').css("display", "inline");
	}

	// 初始化附件
	initAttach("storePictureAttachs", "addStorePictureAttach", "table-store-picture-attach", 2, ${storePictureAttachs});
	initAttach("storeContractAttachs", "addStoreContractAttach", "table-store-contract-attach", 1, ${storeContractAttachs});
	initAttach("acceptanceCommitmentAttachs", "addAcceptanceCommitmentAttach", "table-acceptance-commitment-attach", 0, ${acceptanceCommitmentAttachs});
	initAttach("decorate1Attachs", "addDecorate1Attach", "table-decorate1-attach", 11, ${d1Attachs});
	initAttach("decorate2Attachs", "addDecorate2Attach", "table-decorate2-attach", 12, ${d2Attachs});
	initAttach("decorate3Attachs", "addDecorate3Attach", "table-decorate3-attach", 13, ${d3Attachs});
	initAttach("decorate4Attachs", "addDecorate4Attach", "table-decorate4-attach", 14, ${d4Attachs});
	initAttach("decorate5Attachs", "addDecorate5Attach", "table-decorate5-attach", 15, ${d5Attachs});
	
	
	[#if acceptanceReimburse.wfId!=null]
	 	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${acceptanceReimburse.wfId}");
	[/#if]
	
});


/**
 * 初始化附件，每个参数都是必填的
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param addAttachIdName 前端页面添加附件按钮的id值
 * @param tableIdName 前端页面table中的id值 
 * @param type 后台用来区分不同类型附件
 * @param attachsData 附件数据
 */
function initAttach(paramAttachs, addAttachIdName, tableIdName, type, attachsData) {
	var attachs_data = attachsData;
    var index = 0;
	var attachCols = [				
    	{ title:'${message("附件")}',name:'content',width:260,align:'center',renderer:function(val,item,rowIndex,obj){
    		if (obj==undefined) {
				var url = item.url;
				var fileObj = getfileObj(item.file_name,item.name,item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues[paramAttachs+'['+index+'].id']=item.id;
				hideValues[paramAttachs+'['+index+'].url']=url;
				hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
				hideValues[paramAttachs+'['+index+'].type']=item.type;
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName: paramAttachs+'['+index+'].name',
					hideValues:hideValues
				});
				
			} else {
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues[paramAttachs+'['+index+'].url']=url;
				hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
				hideValues[paramAttachs+'['+index+'].type']=type; 
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName: paramAttachs+'['+index+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
    		index++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $grid=$('#'+tableIdName).mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: attachCols,
        items: attachs_data,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#"+addAttachIdName);
	var attachIdnex = 0;
	var option = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$grid.addRow(row,null,1);
	        }
        }
    }
    $addAttach.file_upload(option);
	
	/* 删除附件 */
    var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	
}


// 保存更新
function save(e){
	var str = '您确定要保存吗？';
	var url = 'update.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= 'edit.jhtml?id='+resultMsg.objx;
				})
			}
		});
	}
}

//只有选中复选框后才能编辑后面的文本框
function handle(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').removeAttr("readonly");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').attr("readonly", "readonly");
		$pdiv.find('input[type=text]').val("");
	}
}
// 只有选中复选框后才能编辑后面的日期
function handleDate(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').removeAttr("disabled");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=text]').attr("disabled", "disabled");
		$pdiv.find('input[type=text]').val("");
	}
}
// 只有选中复选框后才能编辑后面的单选框
function handleRadio(e) {
	if ($(e).prop('checked') == true) {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=radio]').removeAttr("disabled");
	} else {
		var $pdiv = $(e).parent("div");
		$pdiv.find('input[type=radio]').attr("disabled", "disabled");
		$pdiv.find('input[type=radio]').removeAttr("checked");
	}
}

//选中同意复选框时，显示隐藏的内容
function agreeCheck(e) {
    if ($(e).prop('checked') == true) {
        var $pdiv = $(e).parent("label").parent("div");
        $pdiv.siblings('.last-check').find('input[type=checkbox]').attr("checked", false);
        $pdiv.siblings('.disp').css("display", "inline");
    } else {
        var $pdiv = $(e).parent("label").parent("div");
        $pdiv.siblings('.disp').css("display", "none");
        $pdiv.siblings('.disp').find('input[type=text]').val("");
        $pdiv.siblings('.disp').find('input[type=checkbox]').attr("checked", false);
        $pdiv.siblings('.disp').find('input[type=radio]').attr("checked", false);
    }
}
//选中不同意复选框时，显示隐藏的内容
function disagreeCheck(e) {
    if ($(e).prop('checked') == true) {
        var $pdiv = $(e).parent("div");
        $pdiv.siblings('.first-check').find('input[type=checkbox]').attr("checked", false);
        $pdiv.siblings('.disp').css("display", "none");
        $pdiv.siblings('.disp').find('input[type=text]').val("");
        $pdiv.siblings('.disp').find('input[type=checkbox]').attr("checked", false);
        $pdiv.siblings('.disp').find('input[type=radio]').attr("checked", false);
    }
}

function check_wf(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			$.message_confirm("您确定要审批流程吗？",function(){
				var objTypeId = 10003;
				var modelId = 9; //测试环境
				//var modelId = 202501;//开发环境
				//var objTypeId = 10006;//开发环境
				var url="check_wf.jhtml?id=${acceptanceReimburse.id}&modelId="+modelId+"&objTypeId="+objTypeId;
				var data = $form.serialize();
				ajaxSubmit(e,{
					method:'post',
					url:url,
					async: true,
					callback: function(resultMsg) {
						$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
							location.reload(true);
						})
					}
				});
			});
		}
	}
function saveform1(e){
	$.message_confirm("您确定要提交吗？",function(){
		//获取表单所有数据
		var params = $("#inputForm").serializeArray();
		//定义url
		var url = 'saveform1.jhtml?type='+e;
		ajaxSubmit(e,{
			method:'post',
			url:url,
			data:params,
			async: true,
			callback: function(resultMsg) {
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("门店装修验收及报销")}
	</div>
	<form id="inputForm" action="/shop/acceptance_reimburse/update.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<div style="margin:0 0 10px 0; font-size:15px;">
				${message("门店资料")}
			</div>
			<table class="input input-edit">
				<tr>
					<th>${message("单号")}:</th>
					<td>
						<span>${acceptanceReimburse.sn}</span>
						<input type="hidden" class="text" name="sn" value="${acceptanceReimburse.sn}" maxlength="200" btn-fun="clear" />
						<input type="hidden" class="text" name="id" value="${acceptanceReimburse.id}" maxlength="200" btn-fun="clear" />
						<input type="hidden" class="text" name="type" value="${acceptanceReimburse.type}" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("单据状态")}:</th>
					<td>
						[#if acceptanceReimburse.status == 0]
						<span>已保存</span>
						[#elseif acceptanceReimburse.status == 1]
						<span>进行中</span>
						[#elseif acceptanceReimburse.status == 2]
						<span>已完成</span>
						[#elseif acceptanceReimburse.status == 3]
						<span>已终止</span>
						[/#if]
						<input type="hidden" class="text" name="status" value="${acceptanceReimburse.status}" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("门店编码")}:</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="shopInfoId" class="text shopInfoId" value="${acceptanceReimburse.shopInfo.id}" btn-fun="clear"/>
							<input type="text" name="shopInfoSn" class="text shopInfoSn" value="${acceptanceReimburse.shopInfo.sn}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
							<input type="button" class="iconSearch" value="" id="selectShopInfo"/>
						</span>
					</td>
					<th>${message("经销商姓名")}:</th>
                    <td>
                        <span class="dealerText">${acceptanceReimburse.shopInfo.distributorName}</span>
                        <input type="hidden" class="text dealer" name="dealer" value="${acceptanceReimburse.shopInfo.distributorName}" btn-fun="clear" />
                    </td>
				</tr>
				<tr>
                    <th>${message("省份城市")}:</th>
                    <td>
                        <span class="cityText">${acceptanceReimburse.shopInfo.area.fullName}</span>
                        <input type="hidden" class="text city" name="city" value="${acceptanceReimburse.shopInfo.area.fullName}" maxlength="200" btn-fun="clear" />
                    </td>
                    <th>${message("经销商联系电话")}:</th>
                    <td>
                        <span class="dealerPhoneText">${acceptanceReimburse.shopInfo.distributorPhone}</span>
                        <input type="hidden" class="text dealerPhone" name="dealerPhone" value="${acceptanceReimburse.shopInfo.distributorPhone}" btn-fun="clear" />
                    </td>
                    <th>${message("区域经理姓名")}:</th>
                    <td>
                        <span class="regionalManagerText">${acceptanceReimburse.shopInfo.store.storeMember.name}</span>
                        <input type="hidden" class="text regionalManager" name="regionalManager" value="${acceptanceReimburse.shopInfo.store.storeMember.name}" btn-fun="clear" />
                    </td>
                    <th>${message("区域经理联系电话")}:</th>
                    <td>
                        <span class="regionalManagerPhoneText">${acceptanceReimburse.shopInfo.store.storeMember.member.mobile}</span>
                        <input type="hidden" class="text regionalManagerPhone" name="regionalManagerPhone" value="${acceptanceReimburse.shopInfo.store.storeMember.member.mobile}" btn-fun="clear" />
                    </td>
                </tr>
				<tr>
                    <th>${message("门店详细地址")}:</th>
                    <td colspan="3">
                        <span class="shopAddressText">${acceptanceReimburse.shopInfo.address}</span>
                        <input type="hidden" class="text shopAddress" name="shopAddress" id="shopAddress" value="${acceptanceReimburse.shopInfo.address}" maxlength="200" btn-fun="clear" readonly="readonly" />
                    </td>
                    <th>${message("门店名称")}:</th>
					<td>
						<input type="text" class="test shopInfoShopName" name="shopInfoShopName" value="${shopDevise.shopInfo.shopName}" btn-fun="clear" />
					</td>
                    <th>${message("机构")}:</th>
					<td>
					    <span class="saleOrgName">${acceptanceReimburse.saleOrg.name}</span>
					</td>
                </tr>
			</table>
			
			<div style="margin:10px 0 10px 0; font-size:15px;">
				${message("设计资料")}
			</div>
			<table class="input input-edit">
				<tr>
					<th>${message("设计单号")}:</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="shopDeviseId" class="text shopDeviseId" value="${shopDevise.id}" btn-fun="clear"/>
							<input type="text" name="shopDeviseSn" class="text shopDeviseSn" value="${shopDevise.sn}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
							<input type="button" class="iconSearch" value="" id="selectShopDevise"/>
						</span>
					</td>
					<th>${message("门店结构情况")}:</th>
					<td>
					    <span id="structure1">${shopDevise.structure1}</span>&nbsp;&nbsp;&nbsp;&nbsp;
                        <span id="structure2">${shopDevise.structure2}</span>
						<!-- <select name="" class="text" style="width:49%" disabled="disabled">
							<option value="${shopDevise.structure1}" id="structure1">${shopDevise.structure1}</option>
						</select>
						<select name="" class="text" style="width:49%" disabled="disabled">
							<option value="${shopDevise.structure2}" id="structure2">${shopDevise.structure2}</option>
						</select> -->
					</td>
					<th>${message("门店装修属性")}:</th>
                    <td>
                        <span id="shopAttributeText">${shopDevise.shopRenovationAttribute}</span>
                        <input type="hidden" name="" value="${shopDevise.shopRenovationAttribute}" id="shopAttribute" class="text" btn-fun="clear" readonly="readonly"></input>
                    </td>
                    <th>${message("天花限高")}:</th>
                    <td>
                        <span id="highLimitText" style="text-align: center;display:block;">${shopDevise.highLimit}</span>
                        <input type="hidden" name="" value="${shopDevise.highLimit}" id="highLimit" class="text" btn-fun="clear" readonly="readonly"></input>
                    </td>
				</tr>
				<tr>
					<th>${message("设计品牌")}:</th>
                    <td>
                        [#if shopDevise.designBrand == 0]
                        <span id="designBrandText">大自然综合店</span>
                        [#elseif shopDevise.designBrand == 1]
                        <span id="designBrandText">大自然·三层专卖店</span>
                        [#elseif shopDevise.designBrand == 2]
                        <span id="designBrandText">大自然·实木专卖店</span>
                        [#elseif shopDevise.designBrand == 3]
                        <span id="designBrandText">大自然·强化专卖店</span>
                        [/#if]
                        <input type="hidden" class="text" name="" value="${shopDevise.designBrand}" id="designBrand" maxlength="200" btn-fun="clear" readonly="readonly" />
                    </td>
                    <th>${message("面积")}:</th>
                    <td>
                        <span id="areaText" style="text-align: center;display:block;">${shopDevise.area}</span>
                        <input type="hidden" name="" value="${shopDevise.area}" id="area" class="text" btn-fun="clear" readonly="readonly"></input>
                    </td>
                    <th>${message("预订施工时间")}</th>
                    <td>
                        <span id="predictConstructionTimeText">${shopDevise.predictConstructionTime}</span>
                        <input type="hidden" name="" value="${shopDevise.predictConstructionTime}" id="predictConstructionTime" class="text" maxlength="200" readonly="readonly" />
                    </td>
                    <th>${message("预订开业时间")}</th>
                    <td>
                        <span id="predictStartsTimeText">${shopDevise.predictStartsTime}</span>
                        <input type="hidden" name="" value="${shopDevise.predictStartsTime}" id="predictStartsTime" class="text" maxlength="200" readonly="readonly" />
                    </td>
				</tr>
			</table>
			[#--
			<div style="margin:10px 0 10px 0; font-size:15px;">
				${message("渠道部填写")}
			</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			<input type="text" name="shopAuthorizationCodes" value="${acceptanceReimburse.shopAuthorizationCodes}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增档案编号")}:</th>
            		<td>
            			<input type="text" name="archivesCodes" value="${acceptanceReimburse.archivesCodes}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增时间")}:</th>
            		<td>
            			<input id="startTime" name="newTime" class="text" value="${acceptanceReimburse.newTime}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            	</tr>
            </table>
			--]
			<div class="title-style">
				${message("验收申请承诺")}
				<div class="" style="border:1px solid #dcdcdc; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<pre style="font:13px 'Microsoft YaHei';">
1、	确定按公司设计图纸严格按图施工；如有与图纸不符，将接受公司的相关裁定。
2、	确定按照软装、灯具清单购置相关标准配饰物料；如购置不规范，将视为接受直接折扣处理，不做整改。
3、	确定按设计规范设置辅料产品展示区，如无辅料区，将扣除报销总金额的10%处理，不做整改。
4、	由于经销商个人原因，要求重复设计的门店，将扣除报销总金额的10%处理。
5、	确认以下资料提交齐全（□处打√）
					</pre>
					<div>
						<div style="margin:0px 0 10px 0;">
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment1" type="checkbox" value="0" [#if ac?seq_contains("0") == true]checked[/#if] /><label for="acceptanceCommitment1"> 门店平面图核实面积且签字</label>&nbsp;&nbsp;&nbsp;&nbsp;
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment2" type="checkbox" value="1" [#if ac?seq_contains("1") == true]checked[/#if] /><label for="acceptanceCommitment2"> 提交门店装修合同复印件</label>
						</div>
						<div>
							<input class="check js-iname" name="acceptanceCommitments" id="acceptanceCommitment3" type="checkbox" value="2" onchange="handle(this)" [#if ac?seq_contains("2") == true]checked[/#if] />
							<label for="acceptanceCommitment3"> 提交门店租赁合同/房产证复印件：租赁期限</label>
							<input class="t acreage" name="acceptanceLeaseTerm" value="${acceptanceReimburse.acceptanceLeaseTerm}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" [#if ac?seq_contains("2") == false]readonly="readonly"[/#if] style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
							<span>年，合同门店面积</span>
							<input class="t acreage" name="acceptanceArea" value="${acceptanceReimburse.acceptanceArea}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" [#if ac?seq_contains("2") == false]readonly="readonly"[/#if] style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
							<span>㎡</span>
						</div>
					</div>
			    </div>
			</div>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
			    <div class="title-style">
					${message("上传以上确定附件：门店平面图、门店租赁合同/房产复印件、门店装修合同复印件")}
				</div>
				<tr>
					<th>${message("门店平面图")}:</th>
					<td>
					[#if acceptanceReimburse.wfId ==null]
						<a href="javascript:;" id="addStorePictureAttach" class="button">添加附件</a>
					[/#if]
					</td>
					<th>${message("门店租赁合同/房产复印件")}:</th>
					<td>
					[#if acceptanceReimburse.wfId ==null]
						<a href="javascript:;" id="addStoreContractAttach" class="button">添加附件</a>
					[/#if]
					</td>
					<th>${message("门店装修合同复印件")}:</th>
					<td>
					[#if acceptanceReimburse.wfId ==null]
						<a href="javascript:;" id="addAcceptanceCommitmentAttach" class="button">添加附件</a>
					[/#if]
					</td>
				</tr>
			</table>
			<div>
	        	<span>${message("门店平面图")}</span>
		        <table id="table-store-picture-attach" style="width:850px"></table>
		        <span>${message("门店租赁合同/房产复印件")}</span>
		        <table id="table-store-contract-attach" style="width:850px"></table>
		        <span>${message("门店装修合同复印件")}</span>
				<table id="table-acceptance-commitment-attach" style="width:850px"></table>
	        </div>
			
			[#if acceptanceReimburse.wfId !=null]
			[#if qyjl||qyjls]
			<div class="title-style" >
				${message("验收人员核实（区域经理、设计师）")}:[#if node.name?contains("区域经理")]<input type="button" class="bottonss tj" onclick="saveform1(1)" value="提交"/>[/#if]
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 8px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify1" type="checkbox" value="0" onchange="handleDate(this)"  /> -->
						<label for="acceptanceVerify1">经确认，装修施工时间：</label>
						<input type="text" style="width:190px;" name="decorateConstructionTime" value="${acceptanceReimburse.decorateConstructionTime}" class="text" id="d4311" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',maxDate:'#F{$dp.$D(\'d4312\')}'});" />
					</div>
					<div style="margin:0 0 8px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify2" type="checkbox" value="1" onchange="handleDate(this)" /> -->
						<label for="acceptanceVerify2">经确认，装修完成时间：</label>
						<input type="text" style="width:190px;" name="decorateCompleteTime" value="${acceptanceReimburse.decorateCompleteTime}" class="text" id="d4312" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',minDate:'#F{$dp.$D(\'d4311\')}'});" />
					</div>
					<div style="margin:0 0 12px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify3" type="checkbox" value="2" onchange="handleRadio(this)"  /> -->
						<label for="acceptanceVerify3">经确认，门店装修属性：</label>
						<input class="check js-iname" name="acceptanceShop" id="acceptanceShop1" type="radio" value="重装店" [#if acceptanceReimburse.acceptanceShop == "重装店"]checked[/#if] />
						<label for="acceptanceShop1">重装店</label>&nbsp;&nbsp;&nbsp;&nbsp;
						<input class="check js-iname" name="acceptanceShop" id="acceptanceShop2" type="radio" value="新店" [#if acceptanceReimburse.acceptanceShop == "新店"]checked[/#if] />
						<label for="acceptanceShop2">新店</label>
					</div>
					<div style="margin:0 0 12px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify4" type="checkbox" value="3" onchange="handle(this)"  /> -->
						<label for="acceptanceVerify4">经核实，有效面积：</label>
						<input class="t acreage" name="acceptanceVerifyArea" value="${acceptanceReimburse.acceptanceVerifyArea}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>㎡</span>
					</div>
					<div style="margin:0 0 8px 0;">
						<!-- <input class="check js-iname" name="acceptanceVerifys" id="acceptanceVerify5" type="checkbox" value="4" onchange="handle(this)"  /> -->
						<label for="acceptanceVerify5">经审核，评分表得分为：</label>
						<input class="t acreage" name="acceptanceVerifyScore" value="${acceptanceReimburse.acceptanceVerifyScore}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>分</span>
					</div>
				</div>
			</div>
			[#--
			<div class="title-style">
				${message("门店装修验收照片附件：门头+外墙、形象墙+植物墙、收银区、外墙材料高清照、野生实木区两张、实木复合展示区两张")}
				<div class="btns">
					<a href="javascript:;" id="addShopDecorateAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-shop-decorate-attach"></table>
			--]
			<div class="title-style" >
				${message("门店装修验收照片附件")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:0px 15px 10px;">
					<div class="title-style">门头＋外墙、形象墙/植物墙、外墙材料高清照、收银区
						<div class="btns">
							[#if node.name?contains("区域经理")]<a href="javascript:;" id="addDecorate1Attach" class="button">添加附件</a>[/#if]
						</div >
					</div>
					<table id="table-decorate1-attach"></table>
					<div class="title-style">野生实木区两张、实木复合展示区两张
						<div class="btns">
							[#if node.name?contains("区域经理")]<a href="javascript:;" id="addDecorate2Attach" class="button">添加附件</a>[/#if]
						</div>
					</div>
					<table id="table-decorate2-attach"></table>
					<div class="title-style">康德展示区、4+2展示区、1530专区、进口三层区
						<div class="btns">
							[#if node.name?contains("区域经理")]<a href="javascript:;" id="addDecorate3Attach" class="button">添加附件</a>[/#if]
						</div>
					</div>
					<table id="table-decorate3-attach"></table>
					<div class="title-style">强化地板欧式展示区两张、强化地板中式简约区两张
						<div class="btns">
							[#if node.name?contains("区域经理")]<a href="javascript:;" id="addDecorate4Attach" class="button">添加附件</a>[/#if]
						</div>
					</div>
					<table id="table-decorate4-attach"></table>
					<div class="title-style">橡木区/柚木区/其他、五红水吧文化区、辅料区、验收人合影照
						<div class="btns">
							[#if node.name?contains("区域经理")]<a href="javascript:;" id="addDecorate5Attach" class="button">添加附件</a>[/#if]
						</div>
					</div>
					<table id="table-decorate5-attach"></table>
				</div>
			</div>
			[/#if]
			[#if szsh||szshs]
			<div class="title-style" >
				${message("渠道专员意见：")}[#if node.name?contains("省长")]<input type="button" class="bottonss tj" onclick="saveform1(2)" value="提交"/>[/#if]
				<table class="input input-edit">
					<tr>
		        		<th>${message("门店授权编号")}:</th>
		        		<td>
		        			<input type="text" name="shopAuthorizationCodes" value="${acceptanceReimburse.shopAuthorizationCodes}" class="text" maxlength="200"  btn-fun="clear" />
		        		</td>
		        		<th>${message("新增档案编号")}:</th>
		        		<td>
		        			<input type="text" name="archivesCodes" value="${acceptanceReimburse.archivesCodes}" class="text" maxlength="200"  btn-fun="clear" />
		        		</td>
		        		<th>${message("新增时间")}:</th>
		        		<td>
		        			<input id="startTime" name="newTime" class="text" value="${acceptanceReimburse.newTime}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
							<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
		        		</td>
		        	</tr>
	            </table>
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div>
						<div style="margin:0 0 5px 0;">备注:</div>
						<textarea rows="3" name="provincialOperationMemo" style="width:85%; border:1px solid #dcdcdc; padding:5px;">${acceptanceReimburse.provincialOperationMemo}</textarea>
					</div>
				</div>
			</div>
			[/#if]
			[#if sjbsh||sjbshs]
			<div class="title-style" >
				${message("终端设计经理意见：")}:[#if node.name?contains("设计部")]<input type="button" class="bottonss tj" onclick="saveform1(3)" value="提交"/>[/#if]
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
				    <div style="margin:0 0 10px 0;" class="first-check">
                        <label><input class="check js-iname" name="designManagers" type="checkbox" value="4" id="agreeDesignManagers" onchange="agreeCheck(this)" [#if dm?seq_contains("4") == true]checked[/#if] /> 经审核，合格。</label>
                    </div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
					    &nbsp;&nbsp;&nbsp;&nbsp;
						<!-- <input class="check js-iname" name="designManagers" id="designManager1" type="checkbox" value="0" onchange="handleDate(this)" [#if dm?seq_contains("0") == true]checked[/#if] /> -->
						<label for="designManager1">经确认，施工图完成时间：</label>
						<input type="text" style="width:190px;" name="constructionFigureCompleteTime" value="${acceptanceReimburse.constructionFigureCompleteTime}" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
						&nbsp;&nbsp;&nbsp;&nbsp;
					</div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
						<!-- <input class="check js-iname" name="designManagers" id="designManager2" type="checkbox" value="1" onchange="handle(this)" [#if dm?seq_contains("1") == true]checked[/#if] /> -->
						<label for="designManager2">经核实，套内有效营业面积：</label>
						<input class="t acreage" name="designManagerArea" value="${acceptanceReimburse.designManagerArea}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>㎡，验收合格。</span>
						&nbsp;&nbsp;&nbsp;&nbsp;
					</div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
						<!-- <input class="check js-iname" name="designManagers" id="designManager3" type="checkbox" value="2" onchange="handle(this)" [#if dm?seq_contains("2") == true]checked[/#if] /> -->
						<label for="designManager3">经审核，得分为：</label>
						<input class="t acreage" name="designManagerScore" value="${acceptanceReimburse.designManagerScore}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>分。</span>
					</div>
					<div style="margin:6px 0 10px 0;" class="last-check">
						<input class="check js-iname" name="designManagers" id="designManager4" type="checkbox" value="3" onchange="disagreeCheck(this)" [#if dm?seq_contains("3") == true]checked[/#if] />
						<label for="designManager4">经审核，不合格。</label>
					</div>
					<div>
						<div style="margin:0 0 5px 0;">备注:</div>
						<textarea rows="3" name="designManagerMemo" style="width:85%; border:1px solid #dcdcdc;padding:5px;">${acceptanceReimburse.designManagerMemo}</textarea>
					</div>
				</div>
			</div>
			[/#if]
			[#if qdbsh||qdbshs]
			<div class="title-style" >
				${message("渠道总监意见：")}:[#if node.name?contains("渠道部")]<input type="button" class="bottonss tj" onclick="saveform1(4)" value="提交"/>[/#if]
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
				    <div style="margin:0 0 10px 0;" class="first-check">
                        <label><input class="check js-iname" name="directorOpinions" type="checkbox" value="4" id="agreeDirectorOpinions" onchange="agreeCheck(this)" [#if do?seq_contains("4") == true]checked[/#if] /> 同意报销。</label>
                    </div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
						<input class="check js-iname" name="directorOpinions" id="directorOpinion1" type="checkbox" value="0" [#if do?seq_contains("0") == true]checked[/#if] />
						<label for="directorOpinion1">2年内未参与装修报销</label>&nbsp;&nbsp;&nbsp;&nbsp;
						<input class="check js-iname" name="directorOpinions" id="directorOpinion2" type="checkbox" value="1" [#if do?seq_contains("1") == true]checked[/#if] />
						<label for="directorOpinion2">签署2019年经销合同</label>&nbsp;&nbsp;&nbsp;&nbsp;
						<label for="">缴纳齐全品牌保证金：</label>
						<input class="check js-iname" name="payDeposit" id="payDeposit1" type="radio" value="1" [#if acceptanceReimburse.payDeposit == 1]checked[/#if] /><label for="payDeposit1"> 是 </label>&nbsp;&nbsp;
						<input class="check js-iname" name="payDeposit" id="payDeposit2" type="radio" value="0" [#if acceptanceReimburse.payDeposit == 0]checked[/#if] /><label for="payDeposit2"> 否 </label>
					</div>
					<div class="disp" style="margin:0 0 10px 0; display:none;">
						<!-- <input class="check js-iname" name="directorOpinions" id="directorOpinion3" type="checkbox" value="2" onchange="handle(this)" [#if do?seq_contains("2") == true]checked[/#if] /> -->
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<label for="directorOpinion3">经审核，报销标准</label>
						<input class="t acreage" name="directorOpinionMoney1" value="${acceptanceReimburse.directorOpinionMoney1}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>元/㎡，报销金额</span>
						<input class="t acreage" name="directorOpinionMoney2" value="${acceptanceReimburse.directorOpinionMoney2}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>元，扣除品牌保证金</span>
						<input class="t acreage" name="directorOpinionMoney3" value="${acceptanceReimburse.directorOpinionMoney3}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>元</span>
					</div>
					<div style="margin:6px 0 10px 0;" class="last-check">
						<input class="check js-iname" name="directorOpinions" id="directorOpinion4" type="checkbox" value="3" onchange="disagreeCheck(this)" [#if do?seq_contains("3") == true]checked[/#if] />
						<label for="directorOpinion4">不同意报销。</label>
					</div>
					<div>
                        <div style="margin:0 0 5px 0;">备注:</div>
                        <textarea rows="3" name="directorOpinionMemo" style="width:85%; border:1px solid #dcdcdc;padding:5px;">${acceptanceReimburse.directorOpinionMemo}</textarea>
                    </div>
				</div>
			</div>
			[/#if]
			[#if sybzc||sybzcs]  
			<div class="title-style" > 
				${message("地板事业部总经理意见：")}[#if node.name?contains("事业部")]<input type="button" class="bottonss tj" onclick="saveform1(5)" value="提交"/>[/#if]
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 10px 0;">
						<input class="check js-iname" name="floorManagerOpinion" id="floorManagerOpinion1" type="radio" value="1" [#if acceptanceReimburse.floorManagerOpinion == 1]checked[/#if] />
						<label for="floorManagerOpinion1"> 同意报销。</label>
					</div>
					<div>
						<input class="check js-iname" name="floorManagerOpinion" id="floorManagerOpinion2" type="radio" value="0" [#if acceptanceReimburse.floorManagerOpinion == 0]checked[/#if] />
						<label for="floorManagerOpinion2"> 不同意报销。</label>
					</div>
				</div>
			</div>
			[/#if]
			[/#if]			
		</div>
		<div class="fixed-top">
		[#if acceptanceReimburse.wfId == null]
			<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			<!-- <input type="submit" id="submit_button" class="button sureButton" value="${message("保存")}" /> -->
			<input type="button" id="submit_button" onclick="save(this)" class="button sureButton" value="${message("保存")}" />
		[/#if]	
			<input type="button" id="submit_button" class="button sureButton" value="${message("提交")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>