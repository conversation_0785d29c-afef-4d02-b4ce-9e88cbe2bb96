<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑门店新增")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
function editQty(t,e){
	extractNumber(t,3,false,e);
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $areaId = $("#areaId");
	var $newAreaId = $("#newAreaId");
	var $bAreaId = $("#bAreaId");
	var $newShop = $("#newShopAreaId");
	
	// 表单验证
	$inputForm.validate({
		rules: {
    		operationType: "required",
    		createShopDate: "required",
    		shopAcreage: "required",
    		shopLocation: "required",
    		companyNature: "required",
    		shopOwnership: "required",
    		isParticipateDesign: "required",
    		newShopAddress: "required",
//    		monthlyRent: "required",
		},
		messages: {
			name: {
				pattern: "${message("非法字符")}",
				remote: "${message("已存在")}"
			}
		}
	});
	
	//地区选择
	$areaId.lSelect();
	$bAreaId.lSelect();
	$newAreaId.lSelect();
	$newShop.lSelect();

		//现场照片附件
//		var shop_attachs = ${shop_attach};
//		var shopAttachIndex=0;
//		var cols = [
//			{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex, obj){
//				if(obj==undefined){
//					var url = item.url;
//					var fileObj = getfileObj(item.file_name,item.name,item.suffix);
//					/**设置隐藏值*/
//					var hideValues = {};
//					hideValues['shopAttachs['+shopAttachIndex+'].id']=item.id;
//					hideValues['shopAttachs['+shopAttachIndex+'].url']=url;
//					hideValues['shopAttachs['+shopAttachIndex+'].suffix']=fileObj.suffix;
//					return createFileStr({
//						url : url,
//						fileName : fileObj.file_name,
//						name : fileObj.name,
//						suffix : fileObj.suffix,
//						time : item.create_date,
//						textName:'shopAttachs['+shopAttachIndex+'].name',
//						hideValues:hideValues
//					});
//				}else{
//					var url = item.url;
//					var fileObj = getfileObj(item.name);
//					/**设置隐藏值*/
//					var hideValues = {};
//					hideValues['shopAttachs['+shopAttachIndex+'].url']=url;
//					hideValues['shopAttachs['+shopAttachIndex+'].suffix']=fileObj.suffix;
//					
//					return createFileStr({
//						url : url,
//						fileName : fileObj.file_name,
//						name : fileObj.name,
//						suffix : fileObj.suffix,
//						time : '',
//						textName:'shopAttachs['+shopAttachIndex+'].name',
//						hideValues:hideValues
//					});
//				}
//			}},
//			{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex, obj){
//					return '<div><textarea class="text file_memo" name="shopAttachs['+shopAttachIndex+'].memo" >'+val+'</textarea></div>';
//				}},
//			{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex, obj){
//					shopAttachIndex++;
//					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
//				}}
//		];
//		var $shopAttachmGrid=$('#table-attach').mmGrid({
//			fullWidthRows:true,
//			height:'auto',
//			cols: cols,
//			items:shop_attachs,
//			checkCol: false,
//			autoLoad: true
//		});
//		var $addAttach = $("#addAttach");
//		var attachIdnex = 0;
//		var option1 = {
//			dataType: "json",
//			uploadToFileServer:true,
//			uploadSize: "fileurl",
//			callback : function(data){
//				var date = new Date();
//				var year = date.getFullYear();
//				var month = date.getMonth()+1;
//				var day = date.getDate();
//				var time = year+'-'+month+'-'+day;
//				for(var i=0;i<data.length;i++){
//					var row = data[i].file_info;
//					$shopAttachmGrid.addRow(row,null,1);
//				}
//			}
//		}
//		$addAttach.file_upload(option1);
		
		var shop_decorate_attach = ${shop_decorate_attach};
		var shopDecorateAttachIndex=0;
		var shopDecorateCols = [
			{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex, obj){
				if(obj==undefined){
					var url = item.url;
					var fileObj = getfileObj(item.file_name,item.name,item.suffix);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].id']=item.id;
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].url']=url;
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].suffix']=fileObj.suffix;
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].type']=item.type;
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : item.create_date,
						textName:'shopDecorateAttachs['+shopDecorateAttachIndex+'].name',
						hideValues:hideValues
					});
				}else{
					var url = item.url;
					var fileObj = getfileObj(item.name);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].url']=url;
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].suffix']=fileObj.suffix;
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].type']=1;
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : '',
						textName:'shopDecoratepAttachs['+shopDecorateAttachIndex+'].name',
						hideValues:hideValues
					});
				}
			}},
			{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex, obj){
					return '<div><textarea class="text file_memo" name="shopDecorateAttachs['+shopDecorateAttachIndex+'].memo" >'+val+'</textarea></div>';
				}},
			{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex, obj){
					shopDecorateAttachIndex++;
					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
				}}
		];
		var $mGrid_1=$('#table-shop-decorate-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
			cols: shopDecorateCols,
			items:shop_decorate_attach,
			checkCol: false,
			autoLoad: true
		});
		var $addShopDecorateAttach = $("#addShopDecorateAttach");
		var attachIdnex = 0;
		var option1 = {
			dataType: "json",
			uploadToFileServer:true,
			uploadSize: "fileurl",
			callback : function(data){
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
				for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					$mGrid_1.addRow(row,null,1);
				}
			}
		}
		$addShopDecorateAttach.file_upload(option1);
		
		var pay_platform_attach = ${pay_platform_attach};
		var payPlatformAttachIndex=0;
		var payPlatformCols = [
			{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex, obj){
				if(obj==undefined){
					var url = item.url;
					var fileObj = getfileObj(item.file_name,item.name,item.suffix);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].id']=item.id;
					hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].url']=url;
					hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].suffix']=fileObj.suffix;
					hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].type']=item.type;
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : item.create_date,
						textName:'payPlatformAttachs['+payPlatformAttachIndex+'].name',
						hideValues:hideValues
					});
				}else{
					var url = item.url;
					var fileObj = getfileObj(item.name);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].url']=url;
					hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].suffix']=fileObj.suffix;
					hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].type']=1;
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : '',
						textName:'payPlatformAttachs['+payPlatformAttachIndex+'].name',
						hideValues:hideValues
					});
				}
			}},
			{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex, obj){
					return '<div><textarea class="text file_memo" name="payPlatformAttachs['+payPlatformAttachIndex+'].memo" >'+val+'</textarea></div>';
				}},
			{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex, obj){
					payPlatformAttachIndex++;
					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
				}}
		];
		var $mGrid_2=$('#table-pay-platform-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
			cols: payPlatformCols,
			items:pay_platform_attach,
			checkCol: false,
			autoLoad: true
		});
		var $addPayPlatformAttach = $("#addPayPlatformAttach");
		var attachIdnex = 0;
		var option2 = {
			dataType: "json",
			uploadToFileServer:true,
			uploadSize: "fileurl",
			callback : function(data){
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
				for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					$mGrid_2.addRow(row,null,1);
				}
			}
		}
		$addPayPlatformAttach.file_upload(option2);
		
		var plate_attach = ${plate_attach};
		var plateAttachIndex=0;
		var plateCols = [
			{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex, obj){
				if(obj==undefined){
					var url = item.url;
					var fileObj = getfileObj(item.file_name,item.name,item.suffix);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['plateAttachs['+plateAttachIndex+'].id']=item.id;
					hideValues['plateAttachs['+plateAttachIndex+'].url']=url;
					hideValues['plateAttachs['+plateAttachIndex+'].suffix']=fileObj.suffix;
					hideValues['plateAttachs['+plateAttachIndex+'].type']=item.type;
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : item.create_date,
						textName:'plateAttachs['+plateAttachIndex+'].name',
						hideValues:hideValues
					});
				}else{
					var url = item.url;
					var fileObj = getfileObj(item.name);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['plateAttachs['+plateAttachIndex+'].url']=url;
					hideValues['plateAttachs['+plateAttachIndex+'].suffix']=fileObj.suffix;
					hideValues['plateAttachs['+plateAttachIndex+'].type']=2;
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : '',
						textName:'plateAttachs['+plateAttachIndex+'].name',
						hideValues:hideValues
					});
				}
			}},
			{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex, obj){
					return '<div><textarea class="text file_memo" name="plateAttachs['+plateAttachIndex+'].memo" >'+val+'</textarea></div>';
				}},
			{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex, obj){
					plateAttachIndex++;
					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
				}}
		];
		var $mGrid_3=$('#table-plate-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
			cols: plateCols,
			items:plate_attach,
			checkCol: false,
			autoLoad: true
		});
		var $addPlateAttach = $("#addPlateAttach");
		var attachIdnex = 0;
		var option3 = {
			dataType: "json",
			uploadToFileServer:true,
			uploadSize: "fileurl",
			callback : function(data){
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
				for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					$mGrid_3.addRow(row,null,1);
				}
			}
		}
		$addPlateAttach.file_upload(option3);
	
		 var $deleteAttachment = $(".deleteAttachment");
         $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
         });

		$("form").bindAttribute({
			isConfirm:true,
		    callback: function(resultMsg){
		        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
		    }
		});
		
		var inputList = $(".businessCategory").find("label");
		var businessCategory = "${shop.businessCategory!0}";
		check(inputList,businessCategory);
		
		var inputLists = $(".shopSign").find("label");
		var shopSign = "${shop.shopSign!0}";
		check(inputLists,shopSign);
		
		function check(input,value){
			var newArrList = new Array();
			newArrList = value.split(",");
			input.each(function(){
				var name = $(this).text();
				if(explore(name,newArrList)){
					$(this).find('input').prop("checked",true);
				}
			});
		}
		function explore(str,newArrList){
			if(newArrList.length>0){
				for(var i=0;i<newArrList.length;i++){
					if(newArrList[i]==str){
						return true;
					}
				}
			}
			return false;
		}
		
		$("#openStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			url:'/member/store/select_store.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					var name = isnull(row.name);
					var grant_code = isnull(row.grant_code);
					var active_date = isnull(row.active_date);
					var franchisee = isnull(row.franchisee);
					var sale_org_name = isnull(row.sale_org_name);
					var head_phone = isnull(row.head_phone);
					var fixed_number = isnull(row.fixed_number);
					$("input[name='storeId']").val(row.id);
					$(".grantCode").val(grant_code);
					$("#storeName").text(name);
					$(".storeNames").text(name);
					$("#activeDate").text(active_date);
					$("#franchisee").text(franchisee);
					$("#salesPlatform").text(sale_org_name);
					$("#headPhone").text(head_phone)
					$("#fixedNumber").text(fixed_number);
					$(".dealerGrade").text(row.dealer_grade);
					//切换机构
					$("input[name='saleOrgId']").attr("value",row.sale_org_id);
                    $("#saleOrgName").text(row.sale_org_name);
					//切换经销商地址并刷新
					$("#newAreaId").val(row.ha_id);
					$("#newAreaId").attr("treepath",row.harea_tree_path);
					$("#newAreaId").lSelect();
					//切换经销商性别
					$(".dealerSex").find("option").each(function(){
						if(row.dealer_sex==0&&$(this).val()==0){
							$(this).attr("selected",true);
						}
						if(row.dealer_sex==1&&$(this).val()==1){
							$(this).attr("selected",true);
						}
					});
				}
			}
		 });
		 
		 function isnull(str){
		 	var a = str==null?"":str;
		 	return a;
		 }
		 
	 	var input = $("input[name='shopAuthorizationCodes']");
	 	var archivesCodes = $("input[name='archivesCodes']");
	 	var newTime = $("input[name='newTime']");
	/*	$(".isParticipateDesign").on('change',function(){
			var value = $(this).val();
			if(value == "true"){
				input.attr("disabled","disabled");
			}
			if(value == "false"){
				input.removeAttr("disabled");
			}
		 }); */
		 var inputs = $(".isParticipateDesign option:selected").val();
		 if(inputs == "true"){
			input.attr("disabled","disabled");
			archivesCodes.attr("disabled","disabled");
			newTime.attr("disabled","disabled");
		 }
		 
		 [#if wf!=null]
		 	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${shop.wfId}");
		 [/#if]
		
});

	var number = {
		extractNumber:function(a,len){
			var $this = $(a).val();
			if(len!=null){
				if($this.length>len){
					var tval = $this.substring(0,len);
					$(a).val(tval);
				}
			}
			if(!number.isRealNum($this)){
				$(a).val("");
			}
		},isRealNum:function(val){
			if(val === "" || val ==null){
	        return false;
		    }
		    if(!isNaN(val)){
		        return true;
		    }else{
		        return false;
		    }
		}
	}
	
	function check_wf(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			$.message_confirm("您确定要审批流程吗？",function(){
				var objTypeId = 10001;
				var modelId = 3;//大自然测试
				//var modelId = 35013;//大自然开发 
				var url="check_wf.jhtml?id=${shop.id}&modelId="+modelId+"&objTypeId="+objTypeId;
				var data = $form.serialize();
				ajaxSubmit(e,{
					method:'post',
					url:url,
					async: true,
					callback: function(resultMsg) {
						$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
							location.reload(true);
						})
					}
				});
			});
		}
	}
	
	function saveform(e){
		$.message_confirm("您确定要提交吗？",function(){
			//获取表单所有数据
			var params = $("#inputForm").serializeArray();
			//定义url
			var url = 'saveform.jhtml?type='+e;
			ajaxSubmit(e,{
				method:'post',
				url:url,
				data:params,
				async: true,
				callback: function(resultMsg) {
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				}
			});
		});
	}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑门店新增")}
	</div>
	<form id="inputForm" action="/shop/added/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${shop.id}" />
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("单号")}:
					</th>
					<td>
						${shop.sn}
					</td>
					<th>
						${message("单据状态")}:
					</th>
					<td>
						[#if shop.statuss == "saved" ]<span class="blue">${message("已保存")}</span>[/#if]
						[#if shop.statuss == "submitted" ]<span class="green">${message("已生效")}</span>[/#if]
						[#if shop.statuss == "terminated" ]<span class="red">${message("已终止")}</span>[/#if]
						[#if shop.statuss == "underway" ]<span style="color:orange;">${message("进行中")}</span>[/#if]
					</td>
					<th>
						${message("经销商授权编码")}:
					</th>
					[#if isMember == 1]
            		<td>
						<span id="grantCode">${shop.store.grantCode}</span>
            			<input type="hidden" value="${shop.store.id}" name="storeId" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		[#else]
            		<td>
	                    <span class="search" style="position:relative">
	                    <input type="hidden" name="storeId" class="text storeId" value="${shop.store.id}" btn-fun="clear"/>
	                    <input type="text" class="text grantCode" maxlength="200" value="${shop.store.grantCode}" onkeyup="clearSelect(this)"  readOnly/>
	                    <input type="button" class="iconSearch" value="" id="openStore">
	                    </span>
               	 	</td>	
            		[/#if]
					<th>
						${message("经销商姓名")}:
					</th>
					<td>
						<span id="storeName">${shop.store.dealerName}</span>
					</td>
				</tr>
				<tr>
					<th>
						${message("手机号码")}:
					</th>
					<td>
						<span id="headPhone">${shop.store.headPhone}</span>
					</td>
					<th>
						${message("固定号码")}:
					</th>
					<td>
						<span id="fixedNumber">${shop.store.fixedNumber}</span>
					</td>
					<th>
						${message("合伙人名称")}:
					</th>
					<td>
						<input type="text" name="partnerName" value="${shop.partnerName}" class="text" maxlength="200"  btn-fun="clear" />
					</td>
					<th>
						${message("合伙人电话")}:
					</th>
					<td>
						<input type="text" name="partnerPhone" value="${shop.partnerPhone}" class="text" oninput="editQty (this,event)" maxlength="200"  btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>
						${message("地区")}:
					</th>
					<td colspan="3" class="area">
						<input type="hidden" id="newAreaId" value="${(shop.store.headNewArea.id)!}" treePath="${(shop.store.headNewArea.treePath)!}"/>
					</td>
					<th>${message("机构")}:</th>
					<td>
						<span id="saleOrgName">${shop.saleOrg.name}</span>
						<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear"
						 value="${shop.saleOrg.id}" />
					</td>
				</tr>
			</table>
			<div class="title-style">${message("门店信息")}:</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("操作类型")}:</th>
            		<td>
            			${shop.operationType}
            		</td>
            		<th>${message("变更类型")}:</th>
            		<td>
            			<!-- <select name="alterationType" class="text alterationType">
        					<option></option>
							<option value="搬迁" [#if shop.alterationType == "搬迁"] selected[/#if]>${message("搬迁")}</option>
							<option value="交接" [#if shop.alterationType == "交接"] selected[/#if]>${message("交接")}</option>
						</select> -->
            		</td>
            		<th>${message("建店日期")}:</th>
            		<td>
            			<input id="startTime" name="createShopDate" class="text" value="${shop.createShopDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th>${message("门店面积(m²)")}:</th>
            		<td>
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t acreage" name="shopAcreage" value="${shop.shopAcreage}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店位置")}:</th>
            		<td>
	        			<select name="shopLocation" class="text shopLocation">
	    					<option></option>
							<option value="建材市场"[#if shop.shopLocation == "建材市场"] selected[/#if]>${message("建材市场")}</option>
							<option value="临街商铺"[#if shop.shopLocation == "临街商铺"] selected[/#if]>${message("临街商铺")}</option>
							<option value="家具商城"[#if shop.shopLocation == "家具商城"] selected[/#if]>${message("家具商城")}</option>
							<option value="装饰公司"[#if shop.shopLocation == "装饰公司"] selected[/#if]>${message("装饰公司")}</option>
						</select>
            		</td>
            		<th>${message("公司性质")}:</th>
            		<td>
            			<select name="companyNature" class="text companyNature">
	    					<option></option>
							<option value="独立公司"[#if shop.companyNature == "独立公司"] selected[/#if]>${message("独立公司")}</option>
							<option value="合伙公司"[#if shop.companyNature == "合伙公司"] selected[/#if]>${message("合伙公司")}</option>
							<option value="个体工商户"[#if shop.companyNature == "个体工商户"] selected[/#if]>${message("个体工商户")}</option>
						</select>
            		</td>
            		<th>${message("门店所有")}:</th>
            		<td>
            			<select name="shopOwnership" class="text shopOwnership">
	    					<option></option>
							<option value="租赁"[#if shop.shopOwnership == "租赁"] selected[/#if]>${message("租赁")}</option>
							<option value="个人物业"[#if shop.shopOwnership == "个人物业"] selected[/#if]>${message("个体工商户")}</option>
						</select>
            		</td>
            		<th>${message("月租金（元/㎡）")}:</th>
					<td>
						<div class="nums-input ov">
								<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
								<input class="t acreage" name="monthlyRent" value="${shop.monthlyRent}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
								<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
					</td>
            	</tr>
            	<tr>
            		<th>${message("经营品类")}:</th>
            		<td colspan="3">
            			<div class="businessCategory"> 
            				[#list BusinessCategory as bc ]
							<label><input  class="check js-iname text" name="businessCategory" type="checkbox" value="${bc.value}"/>${message("${bc.value}")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							[/#list]
						</div>
            		</td>
            		<th>${message("新店地址")}:</th>
            		<td colspan="3">
            			<input type="hidden" id="newShopAreaId" name="newShopArea.id" value="${shop.newShopArea.id}" treePath="${(shop.newShopArea.treePath)!}"/>
            			<input type="text" name="newShopAddress" value="${shop.newShopAddress}" class="detailed-address" maxlength="200"/>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("是否参与门店设计")}:</th>
            		<td>
            			<select name="isParticipateDesign" class="text isParticipateDesign">
            				<option></option>
							<option value="true"[#if shop.isParticipateDesign == true] selected[/#if]>${message("是")}</option>
							<option value="false"[#if shop.isParticipateDesign == false] selected[/#if]>${message("否")}</option>
						</select>
            		</td>
            		<th>
						${message("店名")}:
					</th>
					<td>
						<input type="text" name="shopName" value="${shop.shopName}" class="text" maxlength="200"  btn-fun="clear" />
					</td>
            	</tr>
			</table>
			<h1 class="red">温馨提醒：若需总部设计，新增流程审批后的7个工作日内需提交门店设计申请，逾期无法提交</h1>
			[#--
			<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
		        ${message("上传附件/门店照片：门店相片（门头远近距离照，收银台，各样板区）")}:
		            <div class="btns">
		                <a href="javascript:;" id="addAttach" class="button">添加附件</a>
		            </div>
		        </div>
		        <table id="table-attach" style="width:850px"></table>
		   </table>
		   --]
			<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
			        ${message("上传附件/门店照片：门店相片（门头远近距离照，收银台，各样板区）")}:
			    </div>
			    <tr>
					<th>${message("门头远近距离照")}:</th>
					<td>
					[#if shop.wfId == null]
						<a href="javascript:;" id="addShopDecorateAttach" class="button">添加附件</a>
					[/#if]
					</td>
					<th>${message("收银台")}:</th>
					<td>
					[#if shop.wfId == null]
						<a href="javascript:;" id="addPayPlatformAttach" class="button">添加附件</a>
					[/#if]
					</td>
					<th>${message("各样板区")}:</th>
					<td>
					[#if shop.wfId == null]
						<a href="javascript:;" id="addPlateAttach" class="button">添加附件</a>
					[/#if]
					</td>
				</tr>
		   </table>
		   <div>
		     	<span>${message("门头远近距离照")}</span>
				<table id="table-shop-decorate-attach" style="width:850px"></table>
		        <span>${message("收银台")}</span>
		        <table id="table-pay-platform-attach" style="width:850px"></table>
			    <span>${message("各样板区")}</span>
				<table id="table-plate-attach" style="width:850px"></table>
		   </div>
		   [#if shop.wfId != null]
		   [#if qyjl||qyjls]
		   <div class="title-style">${message("区域经理意见")}:[#if node.name?contains("区域经理")]<input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/>[/#if]</div>
		   	<table class="input input-edit">
            	<tr>
            		<th>${message("客户")}:</th>
            		<td>
            			<span class="storeNames">${shop.store.name}</span>
            		</td>
            		<th>${message("经销商学历")}:</th>
            		<td>
            			<span class="dealerGrade">${shop.store.dealerGrade}</span>
            		</td>
            		<th>${message("经销商加盟时间")}:</th>
            		<td>
            			<span id="activeDate">${shop.store.activeDate}</span>
            		</td>
            		<th>${message("经销商性别")}:</th>
            		<td>
            			[#if shop.store.dealerSex==0]
            			<span id="dealerSexText">男</span>
            			[#elseif shop.store.dealerSex==1]
                        <span id="dealerSexText">女</span>
                        [/#if]
            			<input type="hidden" class="text" name="dealerSex" value="${shop.store.dealerSex}" id="dealerSex"readOnly/>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("总经销商")}:</th>
            		<td>
            			<span id="franchisee">${shop.store.franchisee}</span>
            		</td>
            		<th>${message("门店类型")}:</th>
            		<td>
            			<select name="shopType" class="text shopType">
	    					<option></option>
							<option value="多品种专卖店"[#if shop.shopType == "多品种专卖店"] selected[/#if]>${message("多品种专卖店")}</option>
							<option value="单品种专卖店"[#if shop.shopType == "单品种专卖店"] selected[/#if]>${message("单品种专卖店")}</option>
							<option value="多品类综合店"[#if shop.shopType == "多品类综合店"] selected[/#if]>${message("多品类综合店")}</option>
							<option value="家装公司专区"[#if shop.shopType == "家装公司专区"] selected[/#if]>${message("家装公司专区")}</option>
							<option value="门店专区"[#if shop.shopType == "门店专区"] selected[/#if]>${message("门店专区")}</option>
						</select>
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			<select name="administrativeRank" class="text accountTypeCode">
            				<option></option>
            				<option value="省级" [#if shop.administrativeRank == "省级"] selected[/#if]>${message("省级")}</option>
							<option value="市级" [#if shop.administrativeRank == "市级"] selected[/#if]  >${message("市级")}</option>
							<option value="区县级" [#if shop.administrativeRank == "区县级"] selected[/#if]  >${message("区县级")}</option>
							<option value="乡镇级" [#if shop.administrativeRank == "乡镇级"] selected[/#if]  >${message("乡镇级")}</option>
            			</select>
            		</td>
            		<th>${message("所属销售平台")}:</th>
            		<td>
            			<span id="salesPlatform">${shop.store.salesPlatform.name}</span>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所属品牌")}:</th>
            		<td>
            			<input type="text" class="text" name="belongBrand" value="大自然地板" readOnly/>
            			[#--
            			<select name="belongBrand" class="text belongBrand">
            			 	<option></option>
            			 	<option value="国际出口"[#if shop.belongBrand == "国际出口"] selected[/#if]>${message("国际出口")}</option>
							<option value="木香居地板"[#if shop.belongBrand == "木香居地板"] selected[/#if]>${message("木香居地板")}</option>
	    					<option value="大自然工程"[#if shop.belongBrand == "大自然工程"] selected[/#if]>${message("大自然工程")}</option>
	    					<option value="nature地板"[#if shop.belongBrand == "nature地板"] selected[/#if]>${message("nature地板")}</option>
	    					<option value="大自然地板"[#if shop.belongBrand == "大自然地板"] selected[/#if]>${message("大自然地板")}</option>
						</select>
						--]
            		</td>
            		<th>${message("VI版本")}:</th>
            		<td>
            			<select name="viVersion" class="text viVersion">
            				<option></option>
							<option value="2013年以前版本"[#if shop.viVersion == "2013年以前版本"] selected[/#if]>${message("2013年以前版本")}</option>
							<option value="2013版本"[#if shop.viVersion == "2013版本"] selected[/#if]>${message("2013版本")}</option>
	    					<option value="2017版本"[#if shop.viVersion == "2017版本"] selected[/#if]>${message("2017版本")}</option>
						</select>
            		</td>
            		<th>${message("所属部门")}:</th>
            		<td>
            			<input type="text" class="text" name="department" value="渠道" readOnly/>
            			[#--
            		 	<select name="department" class="text department">
	    					 <option></option>
	    					<option value="渠道"[#if shop.department == "渠道"] selected[/#if]>${message("渠道")}</option>
	    					<option value="工程"[#if shop.department == "工程"] selected[/#if]>${message("工程")}</option>
							<option value="nature"[#if shop.department == "nature"] selected[/#if]>${message("nature")}</option>
							<option value="财务帐户"[#if shop.department == "财务帐户"] selected[/#if]>${message("财务帐户")}</option>
							<option value="电商"[#if shop.department == "电商"] selected[/#if]>${message("电商")}</option>
							<option value="其他"[#if shop.department == "其他"] selected[/#if]>${message("其他")}</option>
						</select>
						--]
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所含品牌")}:</th>
            		<td colspan="3">
            			<div class="shopSign"> 
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="木门"/>${message("木门")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="壁纸" />${message("壁纸")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="橱衣柜" />${message("橱衣柜")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="nature" />${message("nature")}</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="其他" />${message("其他")}</label>
						</div>
            		</td>
            		<th>${message("销售渠道")}:</th>
            		<td>
            			<select name="salesChannel" class="text salesChannel">
	    					[#--<option></option>--]
							<option value="零售"[#if shop.salesChannel == "零售"] selected[/#if]>${message("零售")}</option>
							[#--
							<option value="工程"[#if shop.salesChannel == "工程"] selected[/#if]>${message("工程")}</option>
							<option value="家装"[#if shop.salesChannel == "家装"] selected[/#if]>${message("家装")}</option>
							--]
						</select>
            		</td>
            	</tr>
			</table>
			[/#if]
			[#if szsh||szshs]
			<div class="title-style">${message("省长意见")}:[#if node.name?contains("省长")]<input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/>[/#if]</div>
			<table class="input input-edit">
		        <tr>
	                <td colspan="7">
	                    <textarea class="text" name="szyj" style="width:800px;height:160px;">${shop.szyj}</textarea>
	                </td>
            	</tr>
			</table>
			[/#if]
			[#if qdzy||qdzys]
			<div class="title-style">${message("渠道部意见")}:[#if node.name?contains("渠道")]<input type="button" class="bottonss tj" onclick="saveform(3)" value="提交"/>[/#if]</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			<input type="text" name="shopAuthorizationCodes" value="${shop.shopAuthorizationCodes}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增档案编号")}:</th>
            		<td>
            			<input type="text" name="archivesCodes" value="${shop.archivesCodes}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增时间")}:</th>
            		<td>
            			<input id="startTime" name="newTime" class="text" value="${shop.newTime}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		
            	</tr>
            	<tr>
            		<th>${message("门店情况备注")}:</th>
		            		<td colspan="7">
	                    <textarea class="text" name="shopCaseNote" style="width:100%;height:160px;">${shop.shopCaseNote}</textarea>
	                </td>
            	</tr>
			</table>
			[/#if]
			[/#if]
		</div>
		<div class="fixed-top">
		[#if wf == null]
			<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
		[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>