<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
function editQty(t,e){
	extractNumber(t,3,false,e);
}
$().ready(function() {

		var $inputForm = $("#inputForm");
		var $newShop = $("#newShopAreaId");
		var $newAreaId = $("#newAreaId");
		// 表单验证
		$inputForm.validate({
			rules: {
	    		operationType: "required",
	    		createShopDate: "required",
	    		shopAcreage: "required",
	    		shopLocation: "required",
	    		companyNature: "required",
	    		shopOwnership: "required",
	    		isParticipateDesign: "required",
	    		newShopAddress: "required",
//	    		monthlyRent: "required",
	    		shopName: "required"
			},
			messages: {
				name: {
					pattern: "${message("非法字符")}",
					remote: "${message("已存在")}"
				}
			}
		});
		
		//地区选择
		$newAreaId.lSelect();
		$newShop.lSelect();
		
		
		$(".area").find("select").each(function(){
			$(this).attr("disabled",true);
		});
		
		//现场照片附件
//		var shopAttachIndex=0;
//		var cols = [
//			{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex, obj){
//					var url = item.url;
//					var fileObj = getfileObj(item.name);
//					/**设置隐藏值*/
//					var hideValues = {};
//					hideValues['shopAttachs['+shopAttachIndex+'].url']=url;
//					hideValues['shopAttachs['+shopAttachIndex+'].suffix']=fileObj.suffix;
//
//					return createFileStr({
//						url : url,
//						fileName : fileObj.file_name,
//						name : fileObj.name,
//						suffix : fileObj.suffix,
//						time : '',
//						textName:'shopAttachs['+shopAttachIndex+'].name',
//						hideValues:hideValues
//					});
//				}},
//			{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex, obj){
//					return '<div><textarea class="text file_memo" name="shopAttachs['+shopAttachIndex+'].memo" >'+val+'</textarea></div>';
//				}},
//			{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex, obj){
//					shopAttachIndex++;
//					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
//				}}
//		];
//		var $shopAttachmGrid=$('#table-attach').mmGrid({
//			fullWidthRows:true,
//			height:'auto',
//			cols: cols,
//			checkCol: false,
//			autoLoad: true
//		});
//		var $addAttach = $("#addAttach");
//		var attachIdnex = 0;
//		var option1 = {
//			dataType: "json",
//			uploadToFileServer:true,
//			uploadSize: "fileurl",
//			callback : function(data){
//				var date = new Date();
//				var year = date.getFullYear();
//				var month = date.getMonth()+1;
//				var day = date.getDate();
//				var time = year+'-'+month+'-'+day;
//				for(var i=0;i<data.length;i++){
//					var row = data[i].file_info;
//					$shopAttachmGrid.addRow(row,null,1);
//				}
//			}
//		}
//		$addAttach.file_upload(option1);
		
		var shopDecorateAttachIndex=0;
		var shopDecorateAttachCols = [
			{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex){
					var url = item.url;
					var fileObj = getfileObj(item.name);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].url']=url;
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].suffix']=fileObj.suffix;
					hideValues['shopDecorateAttachs['+shopDecorateAttachIndex+'].type']=0;

					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : '',
						textName:'shopDecorateAttachs['+shopDecorateAttachIndex+'].name',
						hideValues:hideValues
					});
				}},
			{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex){
					return '<div><textarea class="text file_memo" name="shopDecorateAttachs['+shopDecorateAttachIndex+'].memo" >'+val+'</textarea></div>';
				}},
			{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex){
					shopDecorateAttachIndex++;
					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
				}}
		];
		var $mGrid_1=$('#table-shop-decorate-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
			cols: shopDecorateAttachCols,
			checkCol: false,
			autoLoad: true
		});
		var $addShopDecorateAttach = $("#addShopDecorateAttach");
		var attachIdnex = 0;
		var option1 = {
			dataType: "json",
			uploadToFileServer:true,
			uploadSize: "fileurl",
			callback : function(data){
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
				for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					$mGrid_1.addRow(row,null,1);
				}
			}
		}
		$addShopDecorateAttach.file_upload(option1);
		
		var payPlatformAttachIndex=0;
		var payPlatformAttachCols = [				
	    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
	    		var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].url']=url;
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['payPlatformAttachs['+payPlatformAttachIndex+'].type']=2;  // 门店装修验收照片附件
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'payPlatformAttachs['+payPlatformAttachIndex+'].name',
					hideValues:hideValues
				});
	    	}},
			{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
				return '<div><textarea class="text" name="payPlatformAttachs['+payPlatformAttachIndex+'].memo" >'+val+'</textarea></div>';
			}},
	    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
				payPlatformAttachIndex++;
				return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
			}}
		];
		var $amGrid_2=$('#table-pay-platform-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
	        cols: payPlatformAttachCols,
	        checkCol: false,
	        autoLoad: true
	    });
	    
	    var $addPayPlatformAttach = $("#addPayPlatformAttach");
		var attachIdnex = 0;
		var option2 = {
			dataType: "json",
		    uploadToFileServer:true,
		    uploadSize: "fileurl",
	        callback : function(data){
	        	var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
		        for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					$amGrid_2.addRow(row,null,1);
		        }
	        }
	    }
	    $addPayPlatformAttach.file_upload(option2);
		
		var plateAttachIndex=0;
		var plateAttachCols = [				
	    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
	    		var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['plateAttachs['+plateAttachIndex+'].url']=url;
				hideValues['plateAttachs['+plateAttachIndex+'].suffix']=fileObj.suffix;
				hideValues['plateAttachs['+plateAttachIndex+'].type']=3;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'plateAttachs['+plateAttachIndex+'].name',
					hideValues:hideValues
				});
	    	}},
			{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
				return '<div><textarea class="text" name="plateAttachs['+plateAttachIndex+'].memo" >'+val+'</textarea></div>';
			}},
	    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
				plateAttachIndex++;
				return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
			}}
		];
		var $amGrid_3=$('#table-plate-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
	        cols: plateAttachCols,
	        checkCol: false,
	        autoLoad: true
	    });
	    
	    var $addPlateAttach = $("#addPlateAttach");
		var attachIdnex = 0;
		var option3 = {
			dataType: "json",
		    uploadToFileServer:true,
		    uploadSize: "fileurl",
	        callback : function(data){
	        	var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
		        for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					$amGrid_3.addRow(row,null,1);
		        }
	        }
	    }
	    $addPlateAttach.file_upload(option3);
		
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= '/shop/added/edit.jhtml?id='+resultMsg.objx;
				});
			}
		 });
		 
		 var $deleteAttachment = $(".deleteAttachment");
         $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
         });
         
         $("#openStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			url:'/member/store/select_store.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					var name = isnull(row.name);
					var grant_code = isnull(row.grant_code);
					var active_date = isnull(row.active_date);
					var franchisee = isnull(row.franchisee);
					var sale_org_name = isnull(row.sale_org_name);
					var head_phone = isnull(row.head_phone);
					var fixed_number = isnull(row.fixed_number);
					$("input[name='storeId']").val(row.id);
					$(".grantCode").val(grant_code);
					$("#storeName").text(name);
					$(".storeNames").text(name);
					$(".dealerGrade").text(row.dealer_grade);
					$("#activeDate").text(active_date);
					$("#franchisee").text(franchisee);
					$("#salesPlatform").text(sale_org_name);
					$("#headPhone").text(head_phone)
					$("#fixedNumber").text(fixed_number);
					//切换机构
					$("input[name='saleOrgId']").attr("value",row.sale_org_id);
                    $("#saleOrgName").text(row.sale_org_name);
					//切换经销商地址并刷新
					$("#newAreaId").val(row.ha_id);
					$("#newAreaId").attr("treepath",row.harea_tree_path);
					$("#newAreaId").lSelect();
					//切换经销商性别
					$(".dealerSex").find("option").each(function(){
						if(row.dealer_sex==0&&$(this).val()==0){
							$(this).attr("selected",true);
						}
						if(row.dealer_sex==1&&$(this).val()==1){
							$(this).attr("selected",true);
						}
					});
				}
			}
		 });
       	 
       	 function isnull(str){
		 	var a = str==null?"":str;
		 	return a;
		 }
		 
		var input = $("input[name='shopAuthorizationCodes']");
	 	var archivesCodes = $("input[name='archivesCodes']");
	 	var newTime = $("input[name='newTime']");
		$(".isParticipateDesign").on('change',function(){
			var value = $(this).val();
			if(value == "true"){
				input.attr("disabled","disabled");
			}
			if(value == "false"){
				input.removeAttr("disabled");
			}
		 });
		
});

var number = {
	extractNumber:function(a,len){
		var $this = $(a).val();
		if(len!=null){
			if($this.length>len){
				var tval = $this.substring(0,len);
				$(a).val(tval);
			}
		}
		if(!number.isRealNum($this)){
			$(a).val("");
		}
	},isRealNum:function(val){
		if(val === "" || val ==null){
        	return false;
	    }
	    if(!isNaN(val)){
	        return true;
	    }else{
	        return false;
	    }
	}
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增门店新增")}
	</div>
	<form id="inputForm" action="/shop/added/save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("单号")}:
					</th>
					<td></td>
					<th>
						${message("单据状态")}:
					</th>
					<td></td>
					<th>
						${message("经销商授权编码")}:
					</th>
					[#if isMember == 1]
            		<td>
						<span class="grantCode">${store.grantCode}</span>
            			<input type="hidden" value="${store.id}" name="storeId" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		[#else]
            		<td>
	                    <span class="search" style="position:relative">
	                    <input type="hidden" name="storeId" class="text storeId" value="${store.id}" btn-fun="clear"/>
	                    <input type="text" class="text grantCode" maxlength="200" value="${store.grantCode}" onkeyup="clearSelect(this)"  readOnly/>
	                    <input type="button" class="iconSearch" value="" id="openStore" />
	                    </span>
               	 	</td>
            		[/#if]
					<th>
						${message("经销商姓名")}:
					</th>
					<td>
						<span id="storeName">${store.dealerName}</span>
					</td>
				</tr>
				<tr>
					<th>
						${message("手机号码")}:
					</th>
					<td>
						<span id="headPhone">${store.headPhone}</span>
					</td>
					<th>
						${message("固定号码")}:
					</th>
					<td>
						<span id="fixedNumber">${store.fixedNumber}</span>
					</td>
					<th>
						${message("合伙人名称")}:
					</th>
					<td>
						<input type="text" name="partnerName" class="text" maxlength="200"  btn-fun="clear" />
					</td>
					<th>
						${message("合伙人电话")}:
					</th>
					<td>
						<input type="text" name="partnerPhone" class="text" oninput="editQty (this,event)" maxlength="200"  btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>
						${message("地区")}:
					</th>
					<td colspan="3" class="area">
						<input type="hidden" id="newAreaId" value="${(store.headNewArea.id)!}" treePath="${(store.headNewArea.treePath)!}"/>
					</td>
					<th><span class="requiredField">*</span>${message("机构")}:</th>
					<td><input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear"
						value="[#if store??]${store.saleOrg.id}[#else]${saleOrg.id}[/#if]" />
						<span id="saleOrgName"> [#if store??] ${store.saleOrg.name} [#else] ${saleOrg.name} [/#if] </span>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("门店信息")}:</div>
			<table class="input input-edit">
            	<tr>
            		<!-- <th>${message("操作类型")}:</th>
            		<td>
            			<select name="operationType" class="text operationType" onfocus="this.defOpt=this.selectedIndex" onchange="this.selectedIndex=this.defOpt;">
							<option value="新增">${message("新增")}</option>
							<option value="变更">${message("变更")}</option>
							<option value="减少">${message("减少")}</option>
						</select>
            		</td>
            		<th>${message("变更类型")}:</th>
            		<td> -->
            			<!-- <select name="alterationType" class="text alterationType">
        					<option></option>
							<option value="搬迁">${message("搬迁")}</option>
							<option value="交接">${message("交接")}</option>
						</select> -->
            		<!-- </td>  -->
            		<th>${message("建店日期")}:</th>
            		<td>
            			<input id="startTime" name="createShopDate" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th>${message("门店面积(m²)")}:</th>
            		<td>
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t acreage" name="shopAcreage" value="" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
            		</td>
            		<th>${message("门店所有")}:</th>
            		<td>
            			<select name="shopOwnership" class="text shopOwnership">
	    					<option></option>
							<option value="租赁">${message("租赁")}</option>
							<option value="个人物业">${message("个体工商户")}</option>
						</select>
            		</td>
            		<th>
						${message("月租金（元/㎡）")}:
					</th>
					<td>
						<div class="nums-input ov">
								<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
								<input class="t acreage" name="monthlyRent" value="" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
								<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
					</td>
            	</tr>
            	<tr>
            		<th>${message("门店位置")}:</th>
            		<td>
	        			<select name="shopLocation" class="text shopLocation">
	    					<option></option>
							<option value="建材市场">${message("建材市场")}</option>
							<option value="临街商铺">${message("临街商铺")}</option>
							<option value="家具商城">${message("家具商城")}</option>
							<option value="装饰公司">${message("装饰公司")}</option>
						</select>
            		</td>
            		<th>${message("公司性质")}:</th>
            		<td>
            			<select name="companyNature" class="text companyNature">
	    					<option></option>
							<option value="独立公司">${message("独立公司")}</option>
							<option value="合伙公司">${message("合伙公司")}</option>
							<option value="个体工商户">${message("个体工商户")}</option>
						</select>
            		</td>
            		<th>${message("是否参与门店设计")}:</th>
            		<td>
            			<select name="isParticipateDesign" class="text isParticipateDesign">
            				<option></option>
							<option value="true">${message("是")}</option>
							<option value="false">${message("否")}</option>
						</select>
            		</td>
            		<th>
						${message("店名")}:
					</th>
					<td>
						<input type="text" name="shopName" class="text" maxlength="200"  btn-fun="clear" />
					</td>
            	</tr>
            	<tr>
            		<th>${message("经营品类")}:</th>
            		<td colspan="3">
            			<div class="businessCategory"> 
            				[#list BusinessCategory as bc ]
							<label><input  class="check js-iname text" name="businessCategory" type="checkbox" value="${bc.value}"/>${message("${bc.value}")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							[/#list]
						</div>
            		</td>
            		<th>${message("新店地址")}:</th>
            		<td colspan="3">
            			<input type="hidden" id="newShopAreaId" name="newShopArea.id" />
            			<input type="text" name="newShopAddress" placeholder="请输入详细地址！" class="detailed-address" maxlength="200"/>
            		</td>
            	</tr>
			</table>
			<h1 class="red">温馨提醒：若需总部设计，新增流程审批后的7个工作日内需提交门店设计申请，逾期无法提交</h1>
			[#--
			<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
		        ${message("上传附件/门店照片：门店相片（门头远近距离照，收银台，各样板区）")}:
		            <div class="btns">
		                <a href="javascript:;" id="addAttach" class="button">添加附件</a>
		            </div>
		        </div>
		        <table id="table-attach" style="width:850px"></table>
		   </table>
		   --]
		   <table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
		        	${message("上传附件/门店照片：门店相片（门头远近距离照，收银台，各样板区）")}:
		        </div>
		        <tr>
					<th>${message("门头远近距离照")}:</th>
					<td>
						<a href="javascript:;" id="addShopDecorateAttach" class="button">添加附件</a>
					</td>
					<th>${message("收银台")}:</th>
					<td>
						<a href="javascript:;" id="addPayPlatformAttach" class="button">添加附件</a>
					</td>
					<th>${message("各样板区")}:</th>
					<td>
						<a href="javascript:;" id="addPlateAttach" class="button">添加附件</a>
					</td>
				</tr>
		   </table>
		   <div>
	       		<span>${message("门头远近距离照")}</span>
				<table id="table-shop-decorate-attach" style="width:850px"></table>
		        <span>${message("收银台")}</span>
		        <table id="table-pay-platform-attach" style="width:850px"></table>
		        <span>${message("各样板区")}</span>
				<table id="table-plate-attach" style="width:850px"></table>
	       </div>
		   [#--
		   <div class="title-style">${message("区域经理意见")}:</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("客户")}:</th>
            		<td>
            			<span class="storeNames">${store.name}</span>
            		</td>
            		<th>${message("经销商学历")}:</th>
            		<td>
            			<span class="dealerGrade">${store.dealerGrade}</span>
            		</td>
            		<th>${message("经销商加盟时间")}:</th>
            		<td>
            			<span id="activeDate">${store.activeDate}</span>
            		</td>
            		<th>${message("经销商性别")}:</th>
            		<td>
            			<select class="text dealerSex">
	                       <option value=""></option>
	                       <option value="0" [#if store.dealerSex == 0]selected[/#if]>男</option>
	                       <option value="1" [#if store.dealerSex == 1]selected[/#if]>女</option>
	                    </select>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("总经销商")}:</th>
            		<td>
            			<span id="franchisee">${store.franchisee}</span>
            		</td>
            		<th>${message("门店类型")}:</th>
            		<td>
            			<select name="shopType" class="text shopType">
	    					<option></option>
							<option value="专卖店">${message("专卖店")}</option>
							<option value="大家居">${message("大家居")}</option>
							<option value="一址多名">${message("一址多名")}</option>
							<option value="多品类">${message("多品类")}</option>
							<option value="产品专区">${message("产品专区")}</option>
						</select>
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			<select name="administrativeRank" class="text accountTypeCode">
            				<option></option>
            				<option value="省级">${message("省级")}</option>
							<option value="市级">${message("市级")}</option>
							<option value="区县级">${message("区县级")}</option>
							<option value="乡镇级">${message("乡镇级")}</option>
            			</select>
            		</td>
            		<th>${message("所属销售平台")}:</th>
            		<td>
            			<span id="salesPlatform">${store.salesPlatform.name}</span>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所属品牌")}:</th>
            		<td>
            			<input type="text" class="text" name="belongBrand" value="大自然地板" readOnly/>
            			<!-- <select name="belongBrand" class="text belongBrand">
            				<option></option>
							<option value="国际出口">${message("国际出口")}</option>
							<option value="木香居地板">${message("木香居地板")}</option>
	    					<option value="大自然工程">${message("大自然工程")}</option>
	    					<option value="nature地板">${message("nature地板")}</option>
	    					<option value="大自然地板">${message("大自然地板")}</option>
						</select>-->
            		</td>
            		<th>${message("VI版本")}:</th>
            		<td>
            			<select name="viVersion" class="text viVersion">
            				<option></option>
							<option value="2013年以前版本">${message("2013年以前版本")}</option>
							<option value="2013版本">${message("2013版本")}</option>
	    					<option value="2017版本">${message("2017版本")}</option>
						</select>
            		</td>
            		<th>${message("所属部门")}:</th>
            		<td>
            			<input type="text" class="text" name="department" value="渠道" readOnly/>
            			<!--<select name="department" class="text department">
	    					<option></option>
							<option value="渠道">${message("渠道")}</option>
							<option value="工程">${message("工程")}</option>
							<option value="nature">${message("nature")}</option>
							<option value="财务帐户">${message("财务帐户")}</option>
							<option value="电商">${message("电商")}</option>
							<option value="其他">${message("其他")}</option>
						</select>-->
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所含品牌")}:</th>
            		<td colspan="3">
            			<div class="shopSign"> 
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="国际出口"/>${message("国际出口")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="木香居地板" />${message("木香居地板")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="大自然工程" />${message("大自然工程")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="nature地板" />${message("nature地板")}</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="大自然地板" />${message("大自然地板")}</label>
						</div>
            		</td>
            		<th>${message("销售渠道")}:</th>
            		<td>
            			<select name="salesChannel" class="text salesChannel">
	    					<option></option>
							<option value="零售">${message("零售")}</option>
							<option value="工程">${message("工程")}</option>
							<option value="家装">${message("家装")}</option>
						</select>
            		</td>
            	</tr>
			</table>
			<table class="input input-edit" style="width:500px;margin-top:5px;">
				<div style="width:500px;height:30px;line-height:30px;">
		       		<span style="font-size:16px;"><b>省长意见：</b></span>
		        </div>
		        <tr>
	                <td colspan="7">
	                    <textarea class="text" name="szyj" style="width:800px;height:160px;"></textarea>
	                </td>
            	</tr>
			</table>
			<div class="title-style">${message("渠道部意见")}:</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			<input type="text" name="shopAuthorizationCodes" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增档案编号")}:</th>
            		<td>
            			<input type="text" name="archivesCodes" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增时间")}:</th>
            		<td>
            			<input id="startTime" name="newTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店情况备注")}:</th>
            		<td colspan="7">
	                    <textarea class="text" name="shopCaseNote" style="width:100%;height:160px;"></textarea>
	                </td>
            	</tr>
			</table>
			--]
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>