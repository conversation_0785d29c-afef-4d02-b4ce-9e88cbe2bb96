<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
function add(){
	parent.change_tab(0,'/shop/added/add.jhtml');
}
function initWfStates(){
    wfStates = {};
    [#list wfStates as wfState]
        wfStates['${wfState}'] = '${message('22222222'+wfState)}';
    [/#list]
}
$().ready(function() {
	
	/**初始化多选的下拉框*/
    initMultipleSelect();
    
    initWfStates();
    
	var cols = [
		{ title:'${message("单号")}', name:'sn', align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/shop/added/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("状态")}', name:'statuss' ,align:'center', renderer:function(val,item){
            var result="";
            if (item.statuss == 0) result = '<span class="blue">已保存</span>';
            else if (item.statuss==1) result = '<span style="color:orange;">进行中</span>';
            else if (item.statuss == 2) result = '<span class="red">已终止</span>';
            else if (item.statuss == 3) result = '<span class="green">已生效</span>';
            
            return result;
        }},		
		{ title:'${message("创建人")}', name:'b_creater',align:'center'},
		{ title:'${message("门店授权号")}', name:'shop_authorization_codes',align:'center'},
        { title:'${message("门店地址")}', name:'new_shop_address', width:120, align:'center'},
        { title:'${message("经销商授权编号")}', name:'s_grant_code',align:'center'},
        { title:'${message("经销商")}', name:'s_dealer_name',align:'center'},
        { title:'${message("区域经理")}', name:'sm_xujl_name',align:'center'},
        { title:'${message("机构")}', name:'so_name',align:'center'},
        { title:'${message("流程状态")}', name:'', align:'center', renderer:function(val,item){
            var result = wfStates[item.wf_state];
            if(result!=undefined) return result;
        }},
        { title:'${message("启动人")}', name:'',align:'center'},
        { title:'${message("当前处理人")}', name:'',align:'center'},
        { title:'${message("最后执行人")}', name:'',align:'center'},
        { title:'${message("创建时间")}', name:'create_date', width:130, align:'center'},
        { title:'${message("更新时间")}', name:'modify_date', width:130, align:'center'}
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/shop/added/list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
});


</script>
</head>
<body>
	<form id="listForm" action="/shop/added/list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
          	 	<dl>
        			<dt><p>${message("单号")}：</p></dt>
        			<dd>
        				<input type="text" class="text" name="sn" btn-fun="clear" />
        			</dd>
        		</dl> 
        		<dl>
        			<dt><p>${message("经销商")}：</p></dt>
        			<dd>
        				<input type="text" class="text" name="dealer" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("单据状态")}：</p></dt>
        			<dd>
                        <div class="checkbox-style">
                            <a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                            <div class="statusList cs-box" data-value="off">
                                <label><input class="check js-iname" name="status" value="0" type="checkbox"/>${message("已保存")}</label>
                                <label><input class="check js-iname" name="status" value="1" type="checkbox"/>${message("已提交")}</label>
                                <label><input class="check js-iname" name="status" value="2" type="checkbox"/>${message("已终止")}</label>
                                <label><input class="check js-iname" name="status" value="3" type="checkbox"/>${message("进行中")}</label>
                            </div>
                        </div>
                    </dd>
        		</dl>
			</div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>