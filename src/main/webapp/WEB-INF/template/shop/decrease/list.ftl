<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
$().ready(function() {

	var cols = [
		{ title:'${message("申请单号")}', name:'sn' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/shop/decrease/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("单据状态")}', name:'invoiceStatus' ,align:'center'},		
		{ title:'${message("客户姓名")}', name:'name',align:'center'},
		{ title:'${message("处理状态")}', name:'processingStatus',align:'center'},
		{ title:'${message("售后等级")}', name:'grade',align:'center'}
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: false,
        cols: cols,
        fullWidthRows:true,
        url: '/shop/decrease/list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
});



</script>
</head>
<body>
	<form id="listForm" action="/shop/decrease/list.jhtml" method="get">
		<div class="bar">
			<div id="searchDiv">
        	<div id="search-content" >
          	 	<dl>
        			<dt><p>${message("申请单号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="sn" btn-fun="clear" />
        			</dd>
        		</dl> 
			</div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>