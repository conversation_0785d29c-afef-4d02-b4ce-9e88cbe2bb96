<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
$().ready(function() {

	var $inputForm = $("#inputForm");
	$("#storeAreaId").lSelect();
	$(".area").find("select").each(function(){
		$(this).attr("disabled",true);
	});
	
	// 表单验证
	$inputForm.validate({
		rules: {
    		closingTime: "required",
		},
		messages: {
			name: {
				pattern: "${message("非法字符")}",
				remote: "${message("已存在")}"
			}
		}
	});

	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	});
		

	var inputLists = $(".shopSign").find("label");
	var shopSign = "${shopDecrease.shopInfo.shopSign!0}";
	check(inputLists,shopSign);
	
	function check(input,value){
		var newArrList = new Array();
		newArrList = value.split(",");
		input.each(function(){
			var name = $(this).text();
			console.log(name);
			if(explore(name,newArrList)){
				$(this).find('input').prop("checked",true);
			}
		});
	}
	function explore(str,newArrList){
		if(newArrList.length>0){
			for(var i=0;i<newArrList.length;i++){
				if(newArrList[i]==str){
					return true;
				}
			}
		}
		return false;
	}
	
	[#if shopDecrease.wfId!=null]
	 	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${shopDecrease.wfId}");
	 [/#if] 
		
});
function editQty(t,e){
	extractNumber(t,3,false,e);
}
var number = {
	extractNumber:function(a,len){
		var $this = $(a).val();
		if(len!=null){
			if($this.length>len){
				var tval = $this.substring(0,len);
				$(a).val(tval);
			}
		}
		if(!number.isRealNum($this)){
			$(a).val("");
		}
	},isRealNum:function(val){
		if(val === "" || val ==null){
        return false;
	    }
	    if(!isNaN(val)){
	        return true;
	    }else{
	        return false;
	    }
	}
}

function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		$.message_confirm("您确定要审批流程吗？",function(){
			var objTypeId = 10007;
			var modelId = 7541;//大自然测试
			//var modelId = 295062;//大自然开发 
			var url="check_wf.jhtml?id=${shopDecrease.id}&modelId="+modelId+"&objTypeId="+objTypeId;
			var data = $form.serialize();
			ajaxSubmit(e,{
				method:'post',
				url:url,
				async: true,
				callback: function(resultMsg) {
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				}
			});
		});
	}
}

function saveform(e){
	$.message_confirm("您确定要提交吗？",function(){
		//获取表单所有数据
		var params = $("#inputForm").serializeArray();
		//定义url
		var url = 'saveform.jhtml?type='+e;
		ajaxSubmit(e,{
			method:'post',
			url:url,
			data:params,
			async: true,
			callback: function(resultMsg) {
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑门店减少")}
	</div>
	<form id="inputForm" action="/shop/decrease/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${shopDecrease.id}" />
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("单号")}:
					</th>
					<td>
						${shopDecrease.sn}
					</td>
					<th>
						${message("单据状态")}:
					</th>
					<td>
					
					</td>
					<th>
						${message("经销商授权编码")}:
					</th>
					<td>${shopDecrease.store.grantCode}</td>
				</tr>
				<tr>
					<th>
						${message("经销商名称")}:
					</th>
					<td>
						${shopDecrease.store.name}
						<input type="hidden" value="${shopDecrease.store.id}" name ="storeId" class="text" maxlength="200"  btn-fun="clear" />
					</td>
					<th>
						${message("合伙人名称")}:
					</th>
					<td>
						${shopDecrease.shopInfo.partnerName}
					</td>
					<th>
						${message("合伙人电话")}:
					</th>
					<td>
						${shopDecrease.shopInfo.partnerPhone}
					</td>
				</tr>
				<tr>
					<th>
						${message("手机号码")}:
					</th>
					<td>${shopDecrease.store.headPhone}</td>
					<th>
						${message("固体号码")}:
					</th>
					<td>${shopDecrease.store.fixedNumber}</td>
					<th>
						${message("区域")}:
					</th>
					<td>${shopDecrease.store.region}</td>
				</tr>
				<tr>
					<th>
						${message("地区")}:
					</th>
					<td colspan="3" class="area">
						<input type="hidden" id="storeAreaId" value="${(shopDecrease.store.headNewArea.id)!}" treePath="${(shopDecrease.store.headNewArea.treePath)!}"/>
					</td>
					<th>
						${message("机构")}:
					</th>
					<td>
						<span class="saleOrgName">${shopDecrease.saleOrg.name}</span>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("门店信息")}:</div>
			<table class="input input-edit" style="width:865px">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			${shopDecrease.shopInfo.authorizationCode}
            		</td>
            		<th>${message("操作类型")}:</th>
            		<td>
            			<input type="text" name="operationType" value="减少" class="text"  btn-fun="clear" readOnly/>
            			<input type="hidden" name="shopInfoId" value="${shopDecrease.shopInfo.id}" />
            		</td>
            		<th>${message("门店店名")}:</th>
            		<td>
            			${shopDecrease.shopInfo.shopName}
            		</td>
            	</tr>
            	<tr>
            		<th><span class="requiredField">*</span>${message("关店日期")}:</th>
            		<td>
            			<input id="startTime" name="closingTime" class="text" value="${shopDecrease.closingTime}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th>${message("门店位置")}:</th>
            		<td>
            			${shopDecrease.shopInfo.positionType}
            		</td>
            		<th>${message("公司性质")}:</th>
            		<td>
            			${shopDecrease.shopInfo.companyNature}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店面积(m²)")}:</th>
            		<td>
            			<div class="nums-input ov">
							<input class="b decrease" value="-" type="button" />
							<input class="t acreage" value="${shopDecrease.shopInfo.acreage}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  readOnly/>
							<input value="+" class="b increase"type="button" />
						</div>
            		</td>
            		<th>${message("门店所有")}:</th>
            		<td>
            			${shopDecrease.shopInfo.shopOwnership}
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			${shopDecrease.shopInfo.administrativeRank}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店关闭原因")}:</th>
            		<td colspan="3">
	                    <textarea class="text" name="closeReason" style="width:100%;height:100px;">${shopDecrease.closeReason}</textarea>
	                </td>
            	</tr>
			</table>
		   [#if shopDecrease.wfId != null]
		   [#if qyjl||qyjls]
		   <div class="title-style">${message("区域经理意见")}:[#if node.name?contains("区域经理")]<input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/>[/#if]</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("客户")}:</th>
            		<td>
            			${shopDecrease.store.name}
            		</td>
            		<th>${message("经销商学历")}:</th>
            		<td>
            			${shopDecrease.store.dealerGrade}
            		</td>
            		<th>${message("经销商加盟时间")}:</th>
            		<td>
            			${shopDecrease.store.activeDate}
            		</td>
            		<th>${message("经销商性别")}:</th>
            		<td>
            			[#if shopDecrease.store.dealerSex ==0]男[/#if]
            			[#if shopDecrease.store.dealerSex ==1]女[/#if]
            		</td>
            	</tr>
            	<tr>
            		<th>${message("总经销商")}:</th>
            		<td>
            			${shopDecrease.store.franchisee}
            		</td>
            		<th>${message("门店类型")}:</th>
            		<td>
            			${shopDecrease.shopInfo.type}
            			<!--<select name="type" class="text type" disabled="disabled">
	    					<option></option>
							<option value="多品种专卖店"[#if shopDecrease.shopInfo.type="多品种专卖店"] selected[/#if]>${message("多品种专卖店")}</option>
							<option value="单品种专卖店"[#if shopDecrease.shopInfo.type="单品种专卖店"] selected[/#if]>${message("单品种专卖店")}</option>
							<option value="多品种综合店"[#if shopDecrease.shopInfo.type="多品种综合店"] selected[/#if]>${message("多品种综合店")}</option>
							<option value="家具公司专卖"[#if shopDecrease.shopInfo.type="家具公司专卖"] selected[/#if]>${message("家具公司专卖")}</option>
							<option value="门店专区"[#if shopDecrease.shopInfo.type="门店专区"] selected[/#if]>${message("门店专区")}</option>
						</select>-->
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			[#if shopDecrease.store.accountTypeCode == 0]省级[/#if]
            			[#if shopDecrease.store.accountTypeCode == 1]地市级[/#if]
            			[#if shopDecrease.store.accountTypeCode == 2]区县级[/#if]
            			[#if shopDecrease.store.accountTypeCode == 3]乡镇级[/#if]
            		</td>
            		<th>${message("所属销售平台")}:</th>
            		<td>
            			${shopDecrease.store.salesPlatform.name}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所属品牌")}:</th>
            		<td>${shopInfo.belongBrand}
            			[#--<select name="belongBrand" class="text" disabled="disabled">--]
            				[#--<option></option>--]
							[#--<option value="国际出口" [#if shopDecrease.shopInfo.belongBrand="国际出口"] selected[/#if]>${message("国际出口")}</option>--]
							[#--<option value="木香居地板"[#if shopDecrease.shopInfo.belongBrand="木香居地板"] selected[/#if]   >${message("木香居地板")}</option>--]
							[#--<option value="大自然工程"[#if shopDecrease.shopInfo.belongBrand="大自然工程"] selected[/#if]   >${message("大自然工程")}</option>--]
							[#--<option value="nature地板"[#if shopDecrease.shopInfo.belongBrand="nature地板"] selected[/#if]   >${message("nature地板")}</option>--]
							[#--<option value="大自然地板"[#if shopDecrease.shopInfo.belongBrand="大自然地板"] selected[/#if]   >${message("大自然地板")}</option>--]
						[#--</select>--]
            		</td>
            		<th>${message("VI版本")}:</th>
            		<td>
            			<select name="viVersion" class="text viVersion" disabled="disabled">
            				<option></option>
							<option value="2013年以前版本"[#if shopDecrease.shopInfo.viVersion="2013年以前版本"] selected[/#if]>${message("2013年以前版本")}</option>
							<option value="2013版本"[#if shopDecrease.shopInfo.viVersion="2013版本"] selected[/#if]>${message("2013版本")}</option>
	    					<option value="2017版本"[#if shopDecrease.shopInfo.viVersion="2017版本"] selected[/#if]>${message("2017版本")}</option>
						</select>
            		</td>
            		<th>${message("所属部门")}:</th>
            		<td>
            			<input type="text" class="text" name="department" value="${shopInfo.department}" readOnly/>
            			<!-- <select name="department" class="text department" onfocus="this.defOpt=this.selectedIndex" onchange="this.selectedIndex=this.defOpt;">
							<option></option>
							<option value="渠道"[#if shopInfo.department="渠道"] selected[/#if]>${message("渠道")}</option>
							<option value="工程"[#if shopInfo.department="工程"] selected[/#if]>${message("工程")}</option>
							<option value="nature"[#if shopInfo.department="nature"] selected[/#if]>${message("nature")}</option>
							<option value="财务帐户"[#if shopInfo.department="财务帐户"] selected[/#if]>${message("财务帐户")}</option>
							<option value="电商"[#if shopInfo.department="电商"] selected[/#if]>${message("电商")}</option>
							<option value="其他"[#if shopInfo.department="其他"] selected[/#if]>${message("其他")}</option>
						</select> -->
            		</td>
					<th></th>
					<td></td>
            		[#--<th>${message("关闭原因")}:</th>
            		<td>
            			<input type="text" name="shutDownMenu" value="${shopDecrease.shutDownMenu}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>--]
            	</tr>
            	<tr>
            		<th>${message("所含品牌")}:</th>

                    <td colspan="3">
                        <div class="inclusiveBrand">
                            <label><input  class="check js-iname text" name="inclusiveBrand"  type="checkbox" vaule="木门" [#if shopInfo.inclusiveBrand?contains("木门")] checked[/#if]/>${message("木门")}</label>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <label><input  class="check js-iname text" name="inclusiveBrand" type="checkbox" value="壁纸" [#if shopInfo.inclusiveBrand?contains("壁纸")] checked[/#if] />${message("壁纸")}</label>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <label><input  class="check js-iname text" name="inclusiveBrand" type="checkbox" value="橱衣柜" [#if shopInfo.inclusiveBrand?contains("橱衣柜")] checked[/#if] />${message("橱衣柜")}</label>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <label><input  class="check js-iname text" name="inclusiveBrand" type="checkbox" value="nature" [#if shopInfo.inclusiveBrand?contains("nature")] checked[/#if] />${message("nature")}</label>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <label><input  class="check js-iname text" name="inclusiveBrand" type="checkbox" value="其他" [#if shopInfo.inclusiveBrand?contains("其他")] checked[/#if] />${message("其他")}</label>
                        </div>
                    </td>
            		[#--<td colspan="3">
            			<div class="shopSign"> 
							<label><input  class="check js-iname text" type="checkbox" vaule="国际出口" disabled="disabled"/>${message("国际出口")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" type="checkbox" value="木香居地板" disabled="disabled"/>${message("木香居地板")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" type="checkbox" value="大自然工程" disabled="disabled"/>${message("大自然工程")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" type="checkbox" value="nature地板" disabled="disabled"/>${message("nature地板")}</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" type="checkbox" value="大自然地板" disabled="disabled"/>${message("大自然地板")}</label>
						</div>
            		</td>--]
            		<th>${message("销售渠道")}:</th>
            		<td>
            			<select name="salesChannel" class="text salesChannel" disabled="disabled">
	    					<option></option>
							<option value="零售"[#if shopDecrease.shopInfo.salesChannel="零售"] selected[/#if]>${message("零售")}</option>
							<option value="工程"[#if shopDecrease.shopInfo.salesChannel="工程"] selected[/#if]>${message("工程")}</option>
							<option value="家装"[#if shopDecrease.shopInfo.salesChannel="家装"] selected[/#if]>${message("家装")}</option>
						</select>
            		</td>
            	</tr>
				<tr>
                    <th>${message("关闭原因")}:</th>
                    <td colspan="3">
                        <textarea class="text" name="shutDownMenu" style="width:100%;height:100px;">${shopDecrease.shutDownMenu}</textarea>
                    </td>
				</tr>
			</table>
			[/#if]
			[#if qdzy||qdzys]
			<div class="title-style">${message("渠道部意见")}:[#if node.name?contains("渠道")]<input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/>[/#if]</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			${shopDecrease.shopInfo.authorizationCode}
            		</td>
            		<th>${message("新增档案编号")}:</th>
            		<td>
            			${shopDecrease.shopInfo.increaseArchivesCode}
            		</td>
            		<th>${message("新增时间")}:</th>
            		<td>
            			${shopDecrease.shopInfo.addTime}
            		</td>
            		<th>${message("减少档案编号")}:</th>
            		<td>
            			<input type="text" name="decreaseArchivesCode" value="${shopDecrease.decreaseArchivesCode}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            	</tr>
            	<tr>
            		<th>${message("减少时间")}:</th>
            		<td>
            			<input id="startTime" name="decreaseTime" value="${shopDecrease.decreaseTime}" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th></th>
            		<td></td>
            		<th></th>
            		<td></td>
            		<th></th>
            		<td></td>
            	</tr>
            	<tr>
            		<th>${message("门店情况备注")}:</th>
            		<td colspan="7">
	                    <textarea class="text" name="shopCaseNote" style="width:100%;height:160px;">${shopDecrease.shopCaseNote}</textarea>
	                </td>
            	</tr>
			</table>
			[/#if]
			[/#if]
		</div>
		<div class="fixed-top">
		[#if wf == null]
			<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
		[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>