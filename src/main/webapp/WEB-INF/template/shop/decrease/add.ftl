<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
$().ready(function() {

		var $inputForm = $("#inputForm");
		$("#storeAreaId").lSelect();
		$(".area").find("select").each(function(){
			$(this).attr("disabled",true);
		});
		// 表单验证
		$inputForm.validate({
			rules: {
	    		closingTime: "required",
			},
			messages: {
				name: {
					pattern: "${message("非法字符")}",
					remote: "${message("已存在")}"
				}
			}
		});
		
	
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= '/shop/decrease/edit.jhtml?id='+resultMsg.objx;
				});
			}
		 });
		 
		 var $deleteAttachment = $(".deleteAttachment");
         $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
         });
         
         var inputLists = $(".shopSign").find("label");
		var shopSign = "${shopInfo.shopSign!0}";
		check(inputLists,shopSign);
		
		function check(input,value){
			var newArrList = new Array();
			newArrList = value.split(",");
			input.each(function(){
				var name = $(this).text();
				console.log(name);
				if(explore(name,newArrList)){
					$(this).find('input').prop("checked",true);
				}
			});
		}
		function explore(str,newArrList){
			if(newArrList.length>0){
				for(var i=0;i<newArrList.length;i++){
					if(newArrList[i]==str){
						return true;
					}
				}
			}
			return false;
		}
});

var number = {
	extractNumber:function(a,len){
		var $this = $(a).val();
		if(len!=null){
			if($this.length>len){
				var tval = $this.substring(0,len);
				$(a).val(tval);
			}
		}
		if(!number.isRealNum($this)){
			$(a).val("");
		}
	},isRealNum:function(val){
		if(val === "" || val ==null){
        return false;
	    }
	    if(!isNaN(val)){
	        return true;
	    }else{
	        return false;
	    }
	}
}
function editQty(t,e){
	extractNumber(t,3,false,e);
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增门店减少")}
	</div>
	<form id="inputForm" action="/shop/decrease/save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("单号")}:
					</th>
					<td></td>
					<th>
						${message("单据状态")}:
					</th>
					<td></td>
					<th>
						${message("经销商授权编码")}:
					</th>
					<td>${store.grantCode}</td>
				</tr>
				<tr>
					<th>
						${message("经销商名称")}:
					</th>
					<td>
						${store.name}
						<input type="hidden" value="${store.id}" name ="storeId" class="text" maxlength="200"  btn-fun="clear" />
					</td>
					<th>
						${message("合伙人名称")}:
					</th>
					<td>
						${shopInfo.partnerName}
					</td>
					<th>
						${message("合伙人电话")}:
					</th>
					<td>
						${shopInfo.partnerPhone}
					</td>
				</tr>
				<tr>
					<th>
						${message("手机号码")}:
					</th>
					<td>${store.headPhone}</td>
					<th>
						${message("固体号码")}:
					</th>
					<td>${store.fixedNumber}</td>
					<th>
						${message("区域")}:
					</th>
					<td>${store.region}</td>
				</tr>
				<tr>
					<th>
						${message("地区")}:
					</th>
					<td colspan="3" class="area">
						<input type="hidden" id="storeAreaId" value="${(store.headNewArea.id)!}" treePath="${(store.headNewArea.treePath)!}"/>
					</td>
					<th>
						${message("机构")}:
					</th>
					<td>
						<span class="saleOrgName">${shopInfo.saleOrg.name}</span>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("门店信息")}:</div>
			<table class="input input-edit" style="width:865px">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			${shopInfo.authorizationCode}
            		</td>
            		<th>${message("操作类型")}:</th>
            		<td>
            			<input type="text" name="operationType" value="减少" class="text"  btn-fun="clear" readOnly/>
            			<input type="hidden" name="shopInfoId" value="${shopInfo.id}" />
            		</td>
            		<th>${message("门店店名")}:</th>
            		<td>
            			${shopInfo.shopName}
            		</td>
            	</tr>
            	<tr>
            		<th><span class="requiredField">*</span>${message("关店日期")}:</th>
            		<td>
            			<input id="startTime" name="closingTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th>${message("门店位置")}:</th>
            		<td>
            			${shopInfo.positionType}
            		</td>
            		<th>${message("公司性质")}:</th>
            		<td>
            			${shopInfo.companyNature}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店面积(m²)")}:</th>
            		<td>
            			<div class="nums-input ov">
							<input class="b decrease" value="-" type="button" />
							<input class="t acreage" value="${shopInfo.acreage}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  readOnly/>
							<input value="+" class="b increase"type="button" />
						</div>
            		</td>
            		<th>${message("门店所有")}:</th>
            		<td>
            			${shopInfo.shopOwnership}
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			${shopInfo.administrativeRank}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店关闭原因")}:</th>
            		<td colspan="3">
	                    <textarea class="text" name="closeReason" style="width:100%;height:100px;"></textarea>
	                </td>
            	</tr>
			</table>
			[#--
		   <div class="title-style">${message("区域经理意见")}:</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("客户")}:</th>
            		<td>
            			${store.name}
            		</td>
            		<th>${message("经销商学历")}:</th>
            		<td>
            			${store.dealerGrade}
            		</td>
            		<th>${message("经销商加盟时间")}:</th>
            		<td>
            			${store.activeDate}
            		</td>
            		<th>${message("经销商性别")}:</th>
            		<td>
            			[#if store.dealerSex ==0]男[/#if]
            			[#if store.dealerSex ==1]女[/#if]
            		</td>
            	</tr>
            	<tr>
            		<th>${message("总经销商")}:</th>
            		<td>
            			${store.franchisee}
            		</td>
            		<th>${message("门店类型")}:</th>
            		<td>
            			<select name="type" class="text type">
	    					<option></option>
							<option value="专卖店"[#if shopInfo.type="专卖店"] selected[/#if]>${message("专卖店")}</option>
							<option value="大家居"[#if shopInfo.type="大家居"] selected[/#if]>${message("大家居")}</option>
							<option value="一址多名"[#if shopInfo.type="一址多名"] selected[/#if]>${message("一址多名")}</option>
							<option value="多品类"[#if shopInfo.type="多品类"] selected[/#if]>${message("多品类")}</option>
							<option value="产品专区"[#if shopInfo.type="产品专区"] selected[/#if]>${message("产品专区")}</option>
						</select>
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			[#if store.accountTypeCode == 0]省级[/#if]
            			[#if store.accountTypeCode == 1]地市级[/#if]
            			[#if store.accountTypeCode == 2]区县级[/#if]
            			[#if store.accountTypeCode == 3]乡镇级[/#if]
            		</td>
            		<th>${message("所属销售平台")}:</th>
            		<td>
            			${store.salesPlatform.name}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所属品牌")}:</th>
            		<td>
            			<select name="belongBrand" class="text" disabled="disabled">
            				<option></option>
							<option value="国际出口" [#if shopInfo.belongBrand="国际出口"] selected[/#if]>${message("国际出口")}</option>
							<option value="木香居地板"[#if shopInfo.belongBrand="木香居地板"] selected[/#if]   >${message("木香居地板")}</option>
							<option value="大自然工程"[#if shopInfo.belongBrand="大自然工程"] selected[/#if]   >${message("大自然工程")}</option>
							<option value="nature地板"[#if shopInfo.belongBrand="nature地板"] selected[/#if]   >${message("nature地板")}</option>
							<option value="大自然地板"[#if shopInfo.belongBrand="大自然地板"] selected[/#if]   >${message("大自然地板")}</option>
						</select>
            		</td>
            		<th>${message("VI版本")}:</th>
            		<td>
            			<select name="viVersion" class="text viVersion" disabled="disabled">
            				<option></option>
							<option value="2013年以前版本"[#if shopInfo.viVersion="2013年以前版本"] selected[/#if]>${message("2013年以前版本")}</option>
							<option value="2013版本"[#if shopInfo.viVersion="2013版本"] selected[/#if]>${message("2013版本")}</option>
	    					<option value="2017版本"[#if shopInfo.viVersion="2017版本"] selected[/#if]>${message("2017版本")}</option>
						</select>
            		</td>
            		<th>${message("所属部门")}:</th>
            		<td>
            			<input type="text" class="text" name="department" value="渠道" readOnly/>
            			<!-- <select name="department" class="text department" onfocus="this.defOpt=this.selectedIndex" onchange="this.selectedIndex=this.defOpt;">
							<option></option>
							<option value="渠道"[#if shopInfo.department="渠道"] selected[/#if]>${message("渠道")}</option>
							<option value="工程"[#if shopInfo.department="工程"] selected[/#if]>${message("工程")}</option>
							<option value="nature"[#if shopInfo.department="nature"] selected[/#if]>${message("nature")}</option>
							<option value="财务帐户"[#if shopInfo.department="财务帐户"] selected[/#if]>${message("财务帐户")}</option>
							<option value="电商"[#if shopInfo.department="电商"] selected[/#if]>${message("电商")}</option>
							<option value="其他"[#if shopInfo.department="其他"] selected[/#if]>${message("其他")}</option>
						</select> -->
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所含品牌")}:</th>
            		<td colspan="3">
            			<div class="shopSign"> 
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" vaule="国际出口" disabled="disabled"/>${message("国际出口")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="木香居地板" disabled="disabled"/>${message("木香居地板")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="大自然工程" disabled="disabled"/>${message("大自然工程")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="nature地板" disabled="disabled"/>${message("nature地板")}</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="大自然地板" disabled="disabled"/>${message("大自然地板")}</label>
						</div>
            		</td>
            		<th>${message("销售渠道")}:</th>
            		<td>
            			<select name="salesChannel" class="text salesChannel" disabled="disabled">
	    					<option></option>
							<option value="零售"[#if shopInfo.salesChannel="零售"] selected[/#if]>${message("零售")}</option>
							<option value="工程"[#if shopInfo.salesChannel="工程"] selected[/#if]>${message("工程")}</option>
							<option value="家装"[#if shopInfo.salesChannel="家装"] selected[/#if]>${message("家装")}</option>
						</select>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("关闭原因")}:</th>
            		<td colspan="3">
	                    <textarea class="text" name="shutDownMenu" style="width:100%;height:100px;"></textarea>
            		</td>
            	</tr>
			</table>
			<div class="title-style">${message("渠道部意见")}:</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			${shopInfo.authorizationCode}
            		</td>
            		<th>${message("新增档案编号")}:</th>
            		<td>
            			${shopInfo.increaseArchivesCode}
            		</td>
            		<th>${message("新增时间")}:</th>
            		<td>
            			${shopInfo.addTime}
            		</td>
            		<th>${message("减少档案编号")}:</th>
            		<td>
            			<input type="text" name="decreaseArchivesCode" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            	</tr>
            	<tr>
            		<th>${message("减少时间")}:</th>
            		<td>
            			<input id="startTime" name="decreaseTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th></th>
            		<td></td>
            		<th></th>
            		<td></td>
            		<th></th>
            		<td></td>
            	</tr>
            	<tr>
            		<th>${message("门店情况备注")}:</th>
            		<td colspan="7">
	                    <textarea class="text" name="shopCaseNote" style="width:100%;height:160px;"></textarea>
	                </td>
            	</tr>
			</table> --]
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>