<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看门店订单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<script type="text/javascript" src="/resources/js/productComputeHandler.js"></script>
<style>
.upload-list {width:82px;display: inline-block;padding-top:0px;margin-right:10px;}
.tg-btn {
}
.tg-menu {
    position: absolute;
    top: 100%;
    /* left: 0; */
    z-index: 1000;
    display: none;
    float: left;
    min-width: 85px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}
.tg-menu li {
    padding: 5px 0 5px 10px;
    font-size: 14px;
    cursor: pointer;
}
.tg-menu>li:focus, .tg-menu>li:hover {
    color: #262626;
    background-color: #f5f5f5;
}
.tg-menu>li>a {
    display: block;
    text-decoration: none;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
}
.tg-btn-icon {
    background: url(../../../resources/images/down-circle.png) no-repeat 48px center #5a96d2;
    color: #FFF;
    padding: 0 35px 0px 14px;
}
.tg-btn:hover {
    
}
</style>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
function editQty(t,e){
	extractNumber(t,3,false,e);
}
function countTotal(){
	$('#table-m1 tr').each(function(){
		var tr = $(this);
		var product = newProduct(this);
		var type = productDisc(product.branchPerBox,product.perBranch,product.perBox);
		var QuantityNumber = tr.find(".quantity").text()*1;//数量
		var BoxNumber = 0;//箱数
		var BranchNumber = 0;//支数
		var ScatteredNumber = 0;//零散数
		if(type == 0){
			BoxNumber = parseInt(accDiv(QuantityNumber,product.perBox));
			BranchNumber = isInteger(accDiv(QuantityNumber,product.perBranch))?accDiv(QuantityNumber,product.perBranch):accDiv(QuantityNumber,product.perBranch).toFixed(6);
			ScatteredNumber = isInteger(BranchNumber%product.branchPerBox)?BranchNumber%product.branchPerBox:(BranchNumber%product.branchPerBox).toFixed(6);
			tr.find(".quantity").text(QuantityNumber);
			tr.find(".boxQuantity").text(BoxNumber);
			tr.find(".branchQuantity").text(BranchNumber);
			tr.find(".scattered_quantity").text(ScatteredNumber);
		}else if(type == 2){
			
		}
	});
}


$().ready(function() {
		$(".tg-btn-jl").hover(
		    function() {
		        $(".tg-menu-jl").show();
		    },
		    function() {
		    	setTimeout(function() {
		    		if ($('.tg-menu-jl').is(':hover')) {
		                $(".tg-menu-jl").hover(
		                     function() {
		                         $(".tg-menu-jl").show();
		                     }, function() {
		                         $(".tg-menu-jl").hide();
		                     }
		                 );
		            } else {
		                $(".tg-menu-jl").hide();
		            }
		    	}, 200);
		    }
		);
		$(".tg-btn-cf").hover(
		    function() {
		        $(".tg-menu-cf").show();
		    },
		    function() {
		    	setTimeout(function() {
		    		if ($('.tg-menu-cf').is(':hover')) {
		                $(".tg-menu-cf").hover(
		                     function() {
		                         $(".tg-menu-cf").show();
		                     }, function() {
		                         $(".tg-menu-cf").hide();
		                     }
		                 );
		            } else {
		                $(".tg-menu-cf").hide();
		            }
		    	}, 200);
		    }
		);
		
		var $inputForm = $("#inputForm");
		
		
		var line_no = 1;
		var stockInItemIndex = 0;
    	var orderitem_items = ${orderItems![]};
    	
		var cols = [
				{ title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
						var text = '<span class="line_no">'+ line_no +'</span> ';
                        return text;
                    }},
                { title:'${message("12211")}', name:'vc' ,align:'center', width:150},
                { title:'${message("产品描述")}', name:'d' ,align:'center', width:250},
                { title:'${message("产品分类")}',name:'pc_name', align:'center',renderer: function(val,item,rowIndex){
                		return val==null?item.super_name:val;
                }},
                { title:'${message("单位")}', name:'u', align:'center',renderer: function(val,item,rowIndex){
                	var html ='<input type="hidden" class="perBranch" value="'+item.per_branch+'" />';
                	html +='<input type="hidden" class="perBox" value="'+item.per_box+'" />';
                	html +='<input type="hidden" class="branchPerBox" value="'+item.branch_per_box+'" />';
                	html +='<span>'+val+'</span>';
                	return html;
                }},
                { title:'${message("箱数")}',name:'', align:'center',renderer: function(item){
                		return '<span class="boxQuantity"></span>';
                }},
                { title:'${message("支数")}',name:'', align:'center',renderer: function(item){
                		return '<span class="branchQuantity"></span>';
                }},
                { title:'${message("零散支数")}',name:'', align:'center',renderer: function(item){
                		return '<span class="scattered_quantity"></span>';
                }},
                { title:'${message("数量")}', name:'quantity', align:'center', width:110,renderer: function(val,item,rowIndex){
                	return '<span class="quantity">'+val+'</span>';
                }},
                { title:'${message("金额")}', name:'',align:'center',renderer: function(val,item,rowIndex){
                		var amount = accMul(item.price,item.quantity);
                		return '<span class="red">'+ currency(amount,true) +'</span>';
                	}},
                { title:'${message("已发货量")}', name:'shipped_quantity',align:'center'},
                { title:'${message("待发货量")}', name:'deliverable_quantity',align:'center'},
                { title:'${message("可发货数量")}', name:'deliverable_quantity',align:'center'},
                { title:'${message("退货数量")}', name:'return_quantity',align:'center'},
                {  title:'${message("")}',hidden:'true', align:'center', renderer:function(val,item,rowIndex,obj){
                        stockInItemIndex++;
                        line_no++;
                   }},

        ];
        $mmGrid = $('#table-m1').mmGrid({
            height:'auto',
            cols: cols,
            fullWidthRows:true,
            checkCol: false,
            items:orderitem_items,
            autoLoad: true,
            callback:function(){
            	countTotal();
            }
        });
        
        var table2 = $("#table-m2")
        initTable(table2,${A![]},"收款金额");
        
        var table3 = $("#table-m3")
        initTable(table3,${B![]},"退款金额");
        
        var table4 = $("#table-m4")
        initTable2(table4,${C![]});
        
        var table5 = $("#table-m5")
        initTable2(table5,${D![]});
		
		initFullGrid();
	
		$("form").bindAttribute({
			isConfirm:true,
		    callback: function(resultMsg){
		        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
		    }
		});
		
		$('#measure').bindQueryBtn({
			type:'measure',
			title:'指派测量',
			url:'/shop/surveyor/select_surveyor.jhtml?multi=2&isMeasure=true',
			callback:function(rows){
				if(rows.length>0){
					var name = "";
					var phone = "";
					var row = null;
					if(rows.length>1){
						for(var i=0;i<rows.length;i++){
							row = rows[i];
							if(i == rows.length-1){
								name += row.team_name +'|'+row.name;
								phone += row.phone;
							}else{
								name += row.team_name +'|'+row.name+',';
								phone += row.phone+',';
							}
						}
					}else{
						row = rows[0];
						name = row.team_name +'|'+row.name;
						phone = row.phone;					
					}
					$('.designateMeasureName').text(name);
					$('.designateMeasurePhone').text(phone);
					designate(name,phone,'measure');
				}
			}
	 	});
	 	
	 	$('#install').bindQueryBtn({
			type:'install',
			title:'指派安装',
			url:'/shop/surveyor/select_surveyor.jhtml?multi=2&isInstall=true',
			callback:function(rows){
				if(rows.length>0){
					var name = "";
					var phone = "";
					var row = null;
					if(rows.length>1){
						for(var i=0;i<rows.length;i++){
							row = rows[i];
							if(i == rows.length-1){
								name += row.team_name +'|'+row.name;
								phone += row.phone;
							}else{
								name += row.team_name +'|'+row.name+',';
								phone += row.phone+',';
							}
						}
					}else{
						row = rows[0];
						name = row.team_name +'|'+row.name;
						phone = row.phone;
					}
					$('.designateInstallName').text(name);
					$('.designateInstallPhone').text(phone);
					designate(name,phone,'install');					
				}
			}
	 	});
	 	
	 	//指派操作记录全链路
	 	function designate(name,phone,type){
	 		var sn = $('#sn').text();
	 		var orderId = $('#orderId').val();
	 		$.ajax({
				url: "/shop/order/designate.jhtml",
				type: "POST",
				data: {name: name,phone:phone,orderSn:sn,type:type,id:orderId},
				dataType: "json",
				success: function(resultMsg) {
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				}
			});
	 	}
});
function initTable(obj,items,titleName){
    var cols = [
            { title:'${message("操作时间")}', name:'create_date' ,align:'center', width:150},
            { title:'${message("操作人")}', name:'name' ,align:'center', width:250},
            { title:titleName,name:'price', align:'center',renderer: function(val,item,rowIndex){
            		return '<span class="red">'+ currency(val,true) +'</span>';
            }},
            { title:'${message("内容")}', name:'memo', align:'center'},
    ];
    obj.mmGrid({
        height:'auto',
        cols: cols,
        fullWidthRows:true,
        checkCol: false,
        items:items,
        autoLoad: true,
        callback:function(){
        	
        }
    });
}

function initTable2(obj,items){
    var cols = [
            { title:'${message("操作时间")}', name:'create_date' ,align:'center', width:150},
            { title:'${message("操作人")}', name:'name' ,align:'center', width:80},
            { title:'${message("产品描述")}',name:'description', align:'center',width:150},
            { title:'${message("产品编码")}',name:'vonder_code', align:'center',width:150},
            { title:'${message("产品分类")}',name:'category', align:'center', width:80},
            { title:'${message("数量")}',name:'shipping_quantity', align:'center',width:80},
            { title:'${message("内容")}', name:'memo', align:'center'},
    ];
    obj.mmGrid({
        height:'auto',
        cols: cols,
        fullWidthRows:true,
        checkCol: false,
        items:items,
        autoLoad: true,
        callback:function(){
        	
        }
    });
}

/**初始化全链路的列表*/
function initFullGrid(e){
	var $li = $(e);
	if($li.hasClass("inited"))return false;
	var orderFullLink_items = ${orderFullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
	$li.addClass("inited");
}

//收款弹框
function payWin(e){
	var id = $("#orderId").val();
	var title = $(e).val();
	var type = null;
	if(title == "收款"){
		type = 0;
	}else if(title == "退款"){
		type = 1;
	}
	if(type !=null && id != null){
		$(e).bindQueryBtn({
			bindClick:false,
			type:'lock',
			title:title,
			url:'/shop/order/select_pay.jhtml?id='+id+'&type='+type,
			callback:function(rows){
				
			}
		});	
	}
}

//发货弹框
function shippingWin(e){
	var id = $("#orderId").val();
	var title = $(e).val();
	var type = null;
	if(title == "发货"){
		type = 2;
	}else if(title == "退货"){
		type = 3;
	}
	if(type !=null && id != null){
		$(e).bindQueryBtn({
			bindClick:false,
			type:'lock',
			title:title,
			url:'/shop/order/select_shipping.jhtml?id='+id+'&type='+type,
			callback:function(rows){
				
			}
		});	
	}
}
//结账
function out(){
	var id = $("#orderId").val();
	$.message_confirm("确定要结账吗？",function(){
		//定义url
		var url = '/shop/order/out.jhtml?';
		ajaxSubmit("",{
			method:'post',
			url:url,
			data:{id:id},
			async: true,
			callback: function(resultMsg) {
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
}

function complete(e){
	var sn = $('#sn').text();
	var orderId = $('#orderId').val();
	var str = "";
	if(e=="measure"){
		str = "是否确认测量完成？"
	}else if(e=="install"){
		str = "是否确认安装完成？"
	}
	$.message_confirm(str,function(){
		//定义url
		var url = 'complete.jhtml';
		ajaxSubmit(e,{
			method:'post',
			url:url,
			data:{type:e,id:orderId,orderSn:sn},
			async: true,
			callback: function(resultMsg) {
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
}
//作废
function close_e(e){
	var $this = $(e);
	var id = $('#orderId').val();
	var	str = '您确定要作废吗？';
	$.message_confirm(str,function(){
		//定义url
		var url = 'cancel.jhtml';
		ajaxSubmit(e,{
			method:'post',
			url:url,
			data:{id:id},
			async: true,
			callback: function(resultMsg) {
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看门店订单")}
	</div>
	<form id="inputForm" action="/shop/order/save.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" value="${id}" id="orderId" name="id"/>
		<div class="tabContent">
			<div class="title-style" style="margin-top:-6px;">${message("基本信息")}:</div>
			<table class="input input-edit">
				<tr>
					<th>
						${message("订单编号")}:
					</th>
					<td>
						<span id="sn">${sn}</span>
					</td>
					<th>
						${message("门店授权编号")}:
					</th>
					<td>
						${code}
					</td>
					<th>
						${message("单据日期")}:
					</th> 
					<td>
						${djrq}
					</td>
					<th>
						${message("订单来源")}:
					</th>
					<td>
						[#if type==0]活动订单[/#if]
						[#if type==1]拼团订单[/#if]
						[#if type==2]线下订单[/#if]
						[#if type==3]优惠卷[/#if]
					</td>
				</tr>
				<tr>
					<th>
						${message("门店名称")}:
					</th>
					<td>
						${shop_name}
					</td>
					<th>
						${message("经销商姓名")}:
					</th>
					<td>
						${store_name}
					</td>
					<th>
						${message("录单人")}:
					</th>
					<td>
						${ludanren}
					</td>
					<th></th>
					<td></td>						
				</tr>
				<tr>
					<th>
						${message("订单状态")}:
					</th>
					<td>
						[#if status==0]未确认[/#if]
						[#if status==1]已确认[/#if]
						[#if status==2]已签到[/#if]
						[#if status==3]已验票[/#if]
						[#if status==99]取消订单[/#if]
						[#if status==4]已完成[/#if]
					</td>
					<th>
						${message("测量状态")}:
					</th>
					<td>
						[#if measure_status==1]未指派[/#if]
						[#if measure_status==2]已指派[/#if]
						[#if measure_status==3]已测量[/#if]
					</td>
					<th>
						${message("安装状态")}:
					</th>
					<td>
						[#if install_status==1]未指派[/#if]
						[#if install_status==2]已指派[/#if]
						[#if install_status==3]已安装[/#if]
					</td>
					<th>
						${message("结账状态")}:
					</th>
					<td>
						[#if pay_status==1]未结账[/#if]
						[#if pay_status==2]已结账[/#if]
						
					</td>
				</tr>
				<tr>
					<th>
						${message("测量人员姓名")}
					</th>
					<td colspan="3">
						<span class="designateMeasureName">${designate_measure_name}</span>
					</td>
					<th>
						${message("测量人员电话")}
					</th>
					<td colspan="3">
						<span class="designateMeasurePhone">${designate_measure_phone}</span>
					</td>
				</tr>
				<tr>
					<th>
						${message("安装人员姓名")}
					</th>
					<td colspan="3">
						<span class="designateInstallName">${designate_install_name}</span>
					</td>
					<th>
						${message("安装人员电话")}
					</th>
					<td colspan="3">
						<span class="designateInstallPhone">${designate_install_phone}</span>
					</td>
				</tr>
				<tr>
					<th>
						${message("订单金额")}:
					</th>
					<td>
						<span class="red">${currency(trade_amount, true)}</span>
					</td>
					<th>
						${message("收款金额合计")}:
					</th>
					<td>
						<span class="red">${currency(amount, true)}</span>
					</td>
				</tr>
				<tr>
					<th>
						${message("备注")}:
					</th>
					<td colspan="3">
						${memo}
					</td>
				</tr>
			</table>
			<div class="title-style">${message("顾客信息")}:</div>
			<table class="input input-edit">
            	<tr>
					<th>
						${message("顾客姓名")}:
					</th>
					<td>
					 	${consignee}
					</td>
					<th>
						${message("手机号码")}:
					</th>
					<td>
					 	${phone}
					</td>
            	</tr>
            	<tr>
            		<th>
						${message("地址")}:
					</th>
					<td colspan="3">
						${address}
					</td>
            	</tr>
			</table>
			<div class="title-style">
	        	${message("订单明细")}:
	        </div>
	        <table id="table-m1"></table>
	        <div class="title-style">
	        	${message("收款记录")}:
	        </div>
	        <table id="table-m2"></table>
	        
	        <div class="title-style">
	        	${message("退款记录")}:
	        </div>
	        <table id="table-m3"></table>
	        
	        <div class="title-style">
	        	${message("发货单记录")}:
	        </div>
	        <table id="table-m4"></table>
	        
	        <div class="title-style">
	        	${message("退货单记录")}:
	        </div>
	        <table id="table-m5"></table>
	        <div class="title-style">
				${message("全链路信息")}
			</div>
			<table id="table-full"></table>
		</div>
		<div class="fixed-top">
		[#if pay_status != 2||status!=-99||status!=4]
			<div class="btn-group" style="float:left;">
	            <button type="button" class="button tg-btn tg-btn-jl tg-btn-icon">测量<span class="caret"></span></button>
	            <ul class="tg-menu tg-menu-jl">
	                <li class="" id="measure">指派测量</li>
	                <li class="" onclick="complete('measure')">测量完成</li>
	            </ul>
	        </div>
	        <div class="btn-group" style="float:left;">
	            <button type="button" class="button tg-btn tg-btn-cf tg-btn-icon">安装<span class="caret"></span></button>
	            <ul class="tg-menu tg-menu-cf">
	                <li class="" second-id="fg" id="install">指派安装</li>
	                <li class="" second-id="fg" onclick="complete('install')">安装完成</li>
	            </ul>
	        </div>
			<input type="button" onclick="shippingWin(this)" class="button"  value="${message("发货")}" />
			<input type="button" onclick="shippingWin(this)" class="button"  value="${message("退货")}" />
			<input type="button" onclick="payWin(this)" class="button"  value="${message("收款")}" />
			<input type="button" onclick="payWin(this)" class="button"  value="${message("退款")}" />
			<input type="button" onclick="out()" class="button"  value="${message("结账")}" />
			<input type="button" onclick="close_e(this)" class="button cancleButton"  value="${message("作废")}" />
		[/#if]   
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>