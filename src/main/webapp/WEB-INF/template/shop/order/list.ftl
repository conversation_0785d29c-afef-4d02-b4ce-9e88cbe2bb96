<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("门店订单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/wf.css" rel="stylesheet" type="text/css">
<link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
function isInteger(num){
	if (num==""&&num!=0){
		return false;
	}else if(num == 0){
		return true;
	}
	if (!(/(^[0-9]*[1-9][0-9]*$)/.test(num))){
		return false;
	}else{
		return true;
	}
}

	$().ready(function() {
		
		/**初始化多选的下拉框*/
		initMultipleSelect();
	
	    $("#js-menu dt").click(function(){
	    	var $dl = $(this).closest("dl");
	    	if(!$dl.hasClass("noline_docu")){
	    		$(this).closest("dl").toggleClass("noline_close").toggleClass("noline_open");
	    	}
	    });
	    $("#js-menu dd a").click(function(){
	        $(".client-list dd a").removeClass("on")
	        $(this).addClass("on");
	    });
	    
	    var order_sttuss = {'1':'${message("未到店")}', '2':'${message("未成交")}', '3':'${message("已成交")}', '99':'${message("已取消")}'};
	    var shipping_statuss = {'0':'${message("未发货")}', '1':'${message("部分发货")}', '2':'${message("完全发货")}'};
	    var measure_statuss = {'1':'${message("未指派")}', '2':'${message("已指派")}', '3':'${message("已测量")}', '4':'${message("无需测量")}'};
	    var install_statuss = {'1':'${message("未指派")}', '2':'${message("已指派")}', '3':'${message("已安装")}', '4':'${message("无需安装")}'};
	    var pay_statuss = {'1':'${message("未结账")}', '2':'${message("已结账")}'};
	    var evaluate_statuss = {'0':'${message("未评价")}', '1':'${message("已评价")}'};
	    var payment_statuss = {'0':'${message("未支付")}', '1':'${message("已支付")}'};
		var cols = [
			{ title:'${message("单号")}', name:'ticket_sn', width:100 ,align:'center',renderer:function(val,item){
				return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/shop/order/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
			}},
			{ title:'${message("订单金额")}', name:'trade_amount', width:80 ,align:'center',renderer:function(val,item){
				var html = '<span class="red">'+currency(val,true)+'</span>'
				return html;
			}},
			{ title:'${message("收款金额")}', name:'trade_amount_paid', width:80 ,align:'center',renderer:function(val,item){
				var html = '<span class="red">'+currency(val,true)+'</span>'
				return html;
			}},
			{ title:'${message("门店名称")}', name:'shop_name', width:100 ,align:'center'},
			{ title:'${message("顾客姓名")}', name:'consignee', width:80 ,align:'center'},
			{ title:'${message("手机号码")}', name:'phone', width:80 ,align:'center'},
			{ title:'${message("地址")}', name:'dealer_address', width:150 ,align:'center'},
			{ title:'订单明细' ,align: 'center', cols: [
				{ title:'${message("12211")}', name:'vc' ,width:150, isLines:true, align:'center'},
				{ title:'${message("产品描述")}', name:'d' ,width:150, isLines:true, align:'center'},
				{ title:'${message("产品分类")}', name:'pc_name' ,width:100, isLines:true, align:'center',renderer: function(val,item,rowIndex){
	                		return val==null?item.super_name:val;
	            }},
				{ title:'${message("单位")}', name:'u' ,width:30, isLines:true,align:'center',renderer: function(val,item,rowIndex){ 
					var html ='<input type="hidden" class="perBranch" value="'+item.per_branch+'" />';
	            	html +='<input type="hidden" class="perBox" value="'+item.per_box+'" />';
	            	html +='<input type="hidden" class="branchPerBox" value="'+item.branch_per_box+'" />';
	            	html +='<span>'+val+'</span>';
	            	return html;
				}},
				{ title:'${message("箱数")}', name:'box_number',width:30, isLines:true, align:'center',renderer: function(val,item,rowIndex){ 
					var v = parseInt(val*1);
					return v;
				}},
				{ title:'${message("支数")}', name:'branch_number',width:100, isLines:true, align:'center',renderer: function(val,item,rowIndex){ 
					var v = isInteger(val)?val:val.toFixed(6);
					return v;
				}},
				{ title:'${message("数量")}', name:'quantity' ,width:100, isLines:true, align:'center'},
			]},
			{ title:'${message("录单人")}', name:'ludanren', width:150 ,align:'center'},
			{ title:'${message("录单时间")}', name:'create_date', width:150 ,align:'center'},
			{ title:'${message("订单状态")}', name:'ticket_status', width:80 ,align:'center', renderer: function(val){
				var result = order_sttuss[val];
				if(result!=undefined)return result;			
			}},
			{ title:'${message("发货状态")}', name:'shipping_status', width:80 ,align:'center', renderer: function(val){
				var result = shipping_statuss[val];
				if(result!=undefined)return result;			
			}},
			{ title:'${message("测量状态")}', name:'measure_status', width:80 ,align:'center', renderer: function(val){
				var result = measure_statuss[val];
				if(result!=undefined)return result;			
			}},
			{ title:'${message("安装状态")}', name:'install_status', width:80 ,align:'center', renderer: function(val){
				var result = install_statuss[val];
				if(result!=undefined)return result;			
			}},
			{ title:'${message("结账状态")}', name:'pay_status', width:80 ,align:'center', renderer: function(val){
				var result = pay_statuss[val];
				if(result!=undefined)return result;			
			}},
			{ title:'${message("评价状态")}', name:'evaluate_status', width:80 ,align:'center', renderer: function(val){
				var result = evaluate_statuss[val];
				if(result!=undefined)return result;			
			}}
		];
	
		$mmGrid = $('#table-m1').mmGrid({
			autoLoad: true,
	        cols: cols,
	        url: '/shop/order/list_data.jhtml',
	        lineRoot:"order_items",
	        fullWidthRows:true,
	        params:function(){
	        	return $("#listForm").serializeObject();
	        },
			plugins : [
	            $('#paginator').mmPaginator()
	        ]
	    });
		
		
		$('.conversion').click(function(){
			var s = $(this).attr("kid");
			var param = $mmGrid.serializeSelectedIds();
			if(param==null||param==""){
				return $.message_alert("请选择订单!");
			}
			$.ajax({
				url: "/shop/order/conversion.jhtml?"+param,
				type: "POST",
				data: {status:s},
				dataType: "json",
				success: function(message) {
					$.message_timer(message.type,message.content,3000,function(){});
					window.location.href='/shop/order/list.jhtml';
				}
			});
		});
	
		$("#selectProduct").bindQueryBtn({
			type:'product',
			title:'${message("查询产品")}',
			url:'/product/product/selectProduct.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var vhtml="";
					if($("input[name='productName']").val() == null){
						var allName= "";
					}else{
						var allName= $("input[name='productName']").val();
					}
					
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".productId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						allName =allName +','+ rows[i].name  
						vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="closePro(this)"></i></div>'
						$(".product").append(vhtml);
					}
					$("input[name='productName']").attr("value",allName)
				}
			}
		});
});
	
	function link_to(flag,objType){
		if(flag==0){
			var aa = $(".paymentStatus option");
			select(aa,objType);
		}
		if(flag==1){
			var cc = $(".ticketStatus option");
			select(cc,objType);
		}
		if(flag==2){
			var dd = $(".measureStatus option");
			select(dd,objType);
		}
		if(flag==3){
			var ee = $(".installStatus option");
			select(ee,objType);
		}
		if(flag==4){
			var ff = $(".payStatus option");
			select(ff,objType);
		}
		if(flag==5){
			var gg = $(".shippingStatus option");
			select(gg,objType);
		}
		if(flag==6){
			var hh = $(".evaluateStatus option");
			select(hh,objType);
		}
		$mmGrid.load();
	}
	
	function select(a,objType){
		$('select').each(function(){ console.log($(this).find('option').prop("selected",false));  });
		a.each(function(){
			var value = $(this).text();
			if(objType == value){
				$(this).attr("selected", true);
			}else{
				$(this).attr("selected", false);
			}
		});
	}
	
</script>

</head>
<body class="tree-contain">
	<div class="flow-boxL">
	<div class="title" style="cursor:pointer" >全部订单（${total}）</div>
	<div class="client-list" id="js-menu">
  	<dl class="noline_open">
        <dt><a href="javascript:void(0);">成交状态<b class="red">(<span class="total">${A}</span>)</b></a></dt>
        <dd>
        	<a href="javascript:void(0);" onclick="link_to(0,'未支付')" class="wf-a">
        		未支付<b class="blue">(<span class="total">${X1}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(1,'未到店')" class="wf-a">
        		未到店<b class="blue">(<span class="total">${A2}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(1,'未成交')" class="wf-a">
        		未成交<b class="blue">(<span class="total">${A3}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(1,'已成交')" class="wf-a">
        		已成交<b class="blue">(<span class="total">${A4}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(1,'已取消')" class="wf-a">
        		已取消<b class="blue">(<span class="total">${A5}</span>)</b>
        	</a>
        </dd>
    </dl>
    <dl class="noline_open">
        <dt><a href="javascript:void(0);">发货状态<b class="red">(<span class="total">${E}</span>)</b></a></dt>
        <dd>
        	<a href="javascript:void(0);" onclick="link_to(5,'未发货')" class="wf-a">
        		未发货<b class="blue">(<span class="total">${E1}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(5,'部分发货')" class="wf-a">
        		部分发货<b class="blue">(<span class="total">${E2}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(5,'完全发货')" class="wf-a">
        		完全发货<b class="blue">(<span class="total">${E3}</span>)</b>
        	</a>
        </dd>
    </dl>
    <dl class="noline_open">
        <dt><a href="javascript:void(0);">测量状态<b class="red">(<span class="total">${B}</span>)</b></a></dt>
        <dd>
        	<a href="javascript:void(0);" onclick="link_to(2,'未指派')" class="wf-a">
        		未指派<b class="blue">(<span class="total">${B1}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(2,'已指派')" class="wf-a">
        		已指派<b class="blue">(<span class="total">${B2}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(2,'已测量')" class="wf-a">
        		已测量<b class="blue">(<span class="total">${B3}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(2,'无需测量')" class="wf-a">
        		无需测量<b class="blue">(<span class="total">${B4}</span>)</b>
        	</a>
        </dd>
    </dl>
    <dl class="noline_open">
	    <dt><a href="javascript:void(0);">安装状态<b class="red">(<span class="total">${C}</span>)</b></a></dt>
	    <dd>
	    	<a href="javascript:void(0);" onclick="link_to(3,'未指派')" class="wf-a">
	    		未指派<b class="blue">(<span class="total">${C1}</span>)</b>
	    	</a>
	    	<a href="javascript:void(0);" onclick="link_to(3,'已指派')" class="wf-a">
	    		已指派<b class="blue">(<span class="total">${C2}</span>)</b>
	    	</a>
	    	<a href="javascript:void(0);" onclick="link_to(3,'已安装')" class="wf-a">
	    		已安装<b class="blue">(<span class="total">${C3}</span>)</b>
	    	</a>
	    	<a href="javascript:void(0);" onclick="link_to(3,'无需安装')" class="wf-a">
	    		无需安装<b class="blue">(<span class="total">${C4}</span>)</b>
	    	</a>
	    </dd>
	</dl>
	<dl class="noline_open">
	    <dt><a href="javascript:void(0);">结账状态<b class="red">(<span class="total">${D}</span>)</b></a></dt>
	    <dd>
	    	<a href="javascript:void(0);" onclick="link_to(4,'未结账')" class="wf-a">
	    		未结账<b class="blue">(<span class="total">${D1}</span>)</b>
	    	</a>
	    	<a href="javascript:void(0);" onclick="link_to(4,'已结账')" class="wf-a">
	    		已结账<b class="blue">(<span class="total">${D2}</span>)</b>
	    	</a>
	    </dd>
	</dl>
	<dl class="noline_open">
        <dt><a href="javascript:void(0);">评价状态<b class="red">(<span class="total">${F}</span>)</b></a></dt>
        <dd>
        	<a href="javascript:void(0);" onclick="link_to(6,'未评价')" class="wf-a">
        		未评价<b class="blue">(<span class="total">${F1}</span>)</b>
        	</a>
        	<a href="javascript:void(0);" onclick="link_to(6,'已评价')" class="wf-a">
        		已评价<b class="blue">(<span class="total">${F2}</span>)</b>
        	</a>
        </dd>
    </dl>
  </div>
</div>
<label id="labBtn"></label>
<div class="main-right">
	<form action="" id="exportForm" method="get"></form>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
			<!-- <div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="shopInfo_import(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
					</ul>
				 </div> -->
				 <div class="flag-wrap flagImp-wrap">
					 <a href="javascript:void(0);" class="iconButton" id="transitionStoreStatusButton">
					 	<span>&nbsp;</span>转移成交状态
					 </a>
					 <ul class="flag-list">
					 	<li><a href="javascript:void(0)" class="conversion" kid="F1">${message("未支付")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="A2">${message("未到店")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="A3">${message("未成交")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="A4">${message("已成交")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="A5">${message("已取消")}</a></li>
					</ul>
				</div>
				<div class="flag-wrap flagImp-wrap">
					 <a href="javascript:void(0);" class="iconButton" id="transitionStoreStatusButton">
					 	<span>&nbsp;</span>转移测量状态
					 </a>
					 <ul class="flag-list">
					 	<li><a href="javascript:void(0)" class="conversion" kid="B1">${message("未指派")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="B2">${message("已指派")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="B3">${message("已测量")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="B4">${message("无需测量")}</a></li>
					</ul>
				</div>
				<div class="flag-wrap flagImp-wrap">
					 <a href="javascript:void(0);" class="iconButton" id="transitionStoreStatusButton">
					 	<span>&nbsp;</span>转移安装状态
					 </a>
					 <ul class="flag-list">
					 	<li><a href="javascript:void(0)" class="conversion" kid="C1">${message("未指派")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="C2">${message("已指派")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="C3">${message("已安装")}</a></li>
					 	<li><a href="javascript:void(0)" class="conversion" kid="C4">${message("无需安装")}</a></li>
					</ul>
				</div>
				<div class="flag-wrap flagImp-wrap">
					 <a href="javascript:void(0);" class="iconButton" id="transitionStoreStatusButton">
					 	<span>&nbsp;</span>转移结账状态
					 </a>
					 <ul class="flag-list">
						<li><a href="javascript:void(0)" class="conversion" kid="D1">${message("未结账")}</a></li>
						<li><a href="javascript:void(0)" class="conversion" kid="D2">${message("已结账")}</a></li>
					</ul>
				</div>
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >
					<dl>
						<dt>
							<p>${message("单号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="sn" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt><p>${message("支付状态")}:</p></dt>
						<dd>
							<select name="paymentStatus" class="text paymentStatus">
								<option></option>
								<option value="0">${message("未支付")}</option>
								<option value="1">${message("已支付")}</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("成交状态")}:</p></dt>
						<dd>
							<select  name="ticketStatus" class="text ticketStatus">
								<option></option>
								<option value="1">${message("未到店")}</option>
								<option value="2">${message("未成交")}</option>
								<option value="3">${message("已成交")}</option>
								<option value="99">${message("已取消")}</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("发货状态")}:</p></dt>
						<dd>
							<select  name="shippingStatus" class="text shippingStatus">
								<option></option>
								<option value="0">${message("未发货")}</option>
								<option value="1">${message("部分发货")}</option>
								<option value="2">${message("完全发货")}</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("测量状态")}:</p></dt>
						<dd>
							<select name="measureStatus" class="text measureStatus">
								<option></option>
								<option value="1">${message("未指派")}</option>
								<option value="2">${message("已指派")}</option>
								<option value="3">${message("已测量")}</option>
								<option value="4">${message("无需测量")}</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("安装状态")}:</p></dt>
						<dd>
							<select name="installStatus" class="text installStatus">
								<option></option>
								<option value="1">${message("未指派")}</option>
								<option value="2">${message("已指派")}</option>
								<option value="3">${message("已安装")}</option>
								<option value="4">${message("无需安装")}</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("结账状态")}:</p></dt>
						<dd>
							<select name="payStatus" class="text payStatus">
								<option></option>
								<option value="1">${message("未结账")}</option>
								<option value="2">${message("已结账")}</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("评价状态")}:</p></dt>
						<dd>
							<select name="evaluateStatus" class="text evaluateStatus">
								<option></option>
								<option value="0">${message("未评价")}</option>
								<option value="1">${message("已评价")}</option>
							</select>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("录单人")}:</p></dt>
						<dd>
							<input type="text" class="text" name="ludanren" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt><p>${message("录单时间")}:</p></dt>
						<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" />
							<div class="fl">--</div>
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt><p>${message("门店名称")}:</p></dt>
						<dd>
							<input type="text" class="text" name="shopName" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt><p>${message("顾客姓名")}:</p></dt>
						<dd>
							<input type="text" class="text" name="consignee" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("产品名称")}:</p>
						</dt>
						<dd>
							<span style="position: relative"> 
								<input class="text productName" maxlength="200" type="text" name="productName" value="" onkeyup="clearSelect(this)" readonly> 
								<input type="button" class="iconSearch" value="" id="selectProduct" />
								<div class="pupTitleName product"></div>
							</span>
						</dd>
					</dl>
		        </div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>	
</div>
</body>
</html>