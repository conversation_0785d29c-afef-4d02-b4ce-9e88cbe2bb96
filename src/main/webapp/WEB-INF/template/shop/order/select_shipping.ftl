<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("发货/退货")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<script type="text/javascript" src="/resources/js/productComputeHandler.js"></script>
<style>
.select-fixed-top {
	position: absolute;
	top: 5px;
	right: 0;
}
</style>
<script type="text/javascript">

function editQty(t,e){
	extractNumber(t,6,false,e);
	computeProduct(t,'boxQuantity','branchQuantity','Quantity','scattered_quantity');
}

function gradeChange(e){
	var tr = $(e).parent().parent().parent();
	var a = tr.find(".line_no").text()*1-1;
	var objS = document.getElementsByClassName('storage')[a];
    var grade = objS.options[objS.selectedIndex].value;
    tr.find(".storageId").val(grade);
}

$().ready(function() {
	var line_no = 1;
		var stockInItemIndex = 0;
    	var orderitem_items = ${orderItems![]};
    	
		var cols = [
				{ title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
						var text = '<span class="line_no">'+ line_no +'</span> <input type="hidden" class="ids" value ="'+item.id+'"/>';
                        return text;
                    }},
                { title:'${message("12211")}', name:'vonder_code' ,align:'center', width:120,renderer: function(val,item,rowIndex){
                		var text = '<span>'+ val +'</span>'+
                			'<input type="hidden" name="shippingItems['+stockInItemIndex+'].vonderCode" value="'+val+'" />'+
                			'<input type="hidden" name="shippingItems['+stockInItemIndex+'].productId" value="'+item.product_id+'" />';
                		return text;
                	}},
                { title:'${message("产品描述")}', name:'description' ,align:'center', width:250,renderer: function(val,item,rowIndex){
                		var text = '<span>'+ val +'</span>'+
                			'<input type="hidden" name="shippingItems['+stockInItemIndex+'].description" value="'+val+'" />'+
                			'<input type="hidden" name="shippingItems['+stockInItemIndex+'].orderItemId" value="'+item.id+'" />';
                		return text;
                	}},
                { title:'${message("单位")}', name:'u',width:40, align:'center',renderer: function(val,item,rowIndex){
                	var html ='<input type="hidden" class="perBranch" value="'+item.per_branch+'" />';
                	html +='<input type="hidden" class="perBox" value="'+item.per_box+'" />';
                	html +='<input type="hidden" class="branchPerBox" value="'+item.branch_per_box+'" />';
                	html +='<span>'+val+'</span>';
                	return html;
                }},
                { title:'${message("产品分类")}',name:'pc_name', align:'center'},
                { title:'${message("箱数")}',name:'', align:'center',renderer: function(item){
                 		var quantity = 100;
                		var text = '<div class="nums-input ov">'+
                             '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                             '<input type="text" kid="boxQuantity" class="t boxQuantity" name="shippingItems['+stockInItemIndex+'].shippingBoxQuantity" max="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                             '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                             '</div>';
                		return text;
                }},
                { title:'${message("支数")}',name:'', align:'center',renderer: function(item){
                		var quantity = 100;
                		var text = '<div class="nums-input ov">'+
                             '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                             '<input type="text" kid="branchQuantity" class="t branchQuantity" name="shippingItems['+stockInItemIndex+'].shippingBranchQuantity"  max="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                             '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                             '</div>';
                		return text;
                }},
                { title:'${message("零散支数")}',name:'', align:'center',renderer: function(val,item,rowIndex){
                		return '<span class="scattered_quantity"></span>';
                }},
                [#if type == 2]
                { title:'${message("发货数量")}', name:'quantity', align:'center', width:110,renderer: function(val,item,rowIndex){
                		var text = '<div class="nums-input ov">'+
                             '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                             '<input type="text" kid="quantity" class="t Quantity"  name="shippingItems['+stockInItemIndex+'].shippingQuantity" max="'+val+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                             '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                             '</div>';
                		return text;
                }},
                { title:'${message("待发货数量")}',name:'deliverable_quantity', align:'center'},
                [/#if]
                [#if type == 3]
                { title:'${message("已发货数量")}',name:'shipped_quantity', align:'center'},
                { title:'${message("退货数量")}', name:'', align:'center', width:110,renderer: function(val,item,rowIndex){
                		var text = '<div class="nums-input ov">'+
                             '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                             '<input type="text" kid="quantity" class="t Quantity"  name="shippingItems['+stockInItemIndex+'].shippingQuantity" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                             '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                             '</div>';
                       return text;
                	}},
                [/#if]
                { title:'${message("仓库")}', name:'',align:'center',renderer: function(val,item,rowIndex){
                		var html = '<select class="text storage" onchange="gradeChange(this)"><option value=""></option></select>';
                		html += '<input type="hidden" class="storageId" name="shippingItems['+stockInItemIndex+'].storageId" value="" />';
                		return html;
                }},
                {  title:'${message("")}',hidden:'true', align:'center', renderer:function(val,item,rowIndex,obj){
                        stockInItemIndex++;
                        line_no++;
                   }},

        ];
        $mmGrid = $('#table-m1').mmGrid({
            height:'auto',
            width:'980px',
            cols: cols,
            fullWidthRows:true,
            //checkCol: false,
            items:orderitem_items,
            autoLoad: true,
            callback:function(page){
            	var rows = page;
            	if(rows.length>0){
            	    var trs = $("#table-m1").find("tr");
            		for(var j=0;j<rows.length;j++){
	            		var store = new Object();
	        			var row = rows[j];
	        			store.id = row.store;
	        			store.rowId = row.id;
	        			
	        			$.ajax({
							url: '/shop/set_storage/storage.jhtml',
				            data:{id:store.id},
							type : "post",
							async : false,
							success : function(date) {
								var c = JSON.parse(date.content);
				                if(c.length>0){
				                	var storagearr = [];
									for(var i=0;i<c.length;i++){
					            		var storage = new Object();
										var con = c[i];
										storage.id = con.id;
										storage.name = con.name;
										storagearr.push(storage);
									}
									var tr = trs.eq(j);
						        	var trid = tr.find(".ids").val();
						        	var select = tr.find(".storage");
						        	if(trid == row.id){
						        		for(var i=0;i<storagearr.length;i++){
						        			var stage = storagearr[i];
							        		select.append('<option value="'+stage.id+'" onchange="aa()">'+stage.name+'</option>'); 							        			
						        		}
						        	}
								}
							}
						});
	        		}
            	}
            }
        });
        
        $('#save').click(function(){
		    var param = $mmGrid.serializeSelectedIds();//参数
		    if(param==""||param==null){
		    	return $.message_alert("请选择发货单!");
		    }
        	var url = '/shop/order/operation.jhtml?'+param;
		    var $form = $("#listForm");
		    var c = $form.serialize();
			ajaxSubmit("",{
				url: url,
				data:$form.serialize(),
				method: "post",
				isConfirm:true,
				callback:function(resultMsg){
					var orderId=resultMsg.objx;
					if(resultMsg.type!="success"){
						$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
								location.reload(true);
						});
						return false;
					}
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.href= '/shop/order/edit.jhtml?id='+resultMsg.objx;
					});
		
				}
			});
        });
        
	
});


function save(e){
    var url = '/shop/order/operation.jhtml';
    var $form = $("#listForm");
    var param = $mmGrid.serializeSelectedIds();//参数
    var c = $form.serialize();
	ajaxSubmit(e,{
		url: url,
		data:$form.serialize(),
		method: "post",
		isConfirm:true,
		callback:function(resultMsg){
			var orderId=resultMsg.objx;
			if(resultMsg.type!="success"){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
				});
				return false;
			}
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/shop/order/edit.jhtml?id='+resultMsg.objx;
			});

		}
	});
}
</script>
</head>
<body style="min-width: 0px;">
	<form id="listForm" action="select_lock_list.jhtml" method="get">
		<input type="hidden" name="orderId" value="${id}" />
		<input type="hidden" name="type" value="${type}" />
		<div style="position: absolute; top: 38px;">
			<table class="input input-edit">
				<tr>
					<th>${message("送货人")}:</th>
					<td>
						<input type="text" name="" value="" class="text" maxlength="200" />
					</td>
					<th>${message("电话")}:</th>
					<td>
						<input type="text" name="" value="" class="text" maxlength="200" />
					</td>
				</tr>
			</table>
			<div class="title-style"></div>
			<table id="table-m1" style="width: 800px;"></table>
		</div>
		<div class="select-fixed-top">
			<input type="button" id="save" class="button" value="${message("提交")}">
		</div>
	</form>
</body>
</html>