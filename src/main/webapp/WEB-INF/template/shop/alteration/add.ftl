<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
$().ready(function() {

		var $inputForm = $("#inputForm");
		
		$("#storeAreaId").lSelect();
		$("#shopInfoAreaId").lSelect();
		$("#newShopAreaId").lSelect();
		
		$(".area").find("select").each(function(){
			$(this).attr("disabled",true);
		});
		
		// 表单验证
		$inputForm.validate({
			rules: {
	    		operationType: "required",
	    		closingTime: "required",
	    		openDate: "required",
	    		acreage: "required",
	    		positionType: "required",
	    		companyNature: "required",
	    		shopOwnership: "required",
	    		administrativeRank: "required",
	    		isDesign: "required",
	    		shopName: "required",
	    		moveReason: "required",
			},
			messages: {
				name: {
					pattern: "${message("非法字符")}",
					remote: "${message("已存在")}"
				}
			}
		});
		//现场照片附件
		var shopAttachIndex=0;
		var cols = [
			{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex, obj){
					var url = item.url;
					var fileObj = getfileObj(item.name);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['shopAlterationAttachs['+shopAttachIndex+'].url']=url;
					hideValues['shopAlterationAttachs['+shopAttachIndex+'].suffix']=fileObj.suffix;

					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : '',
						textName:'shopAlterationAttachs['+shopAttachIndex+'].name',
						hideValues:hideValues
					});
				}},
			{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex, obj){
					return '<div><textarea class="text file_memo" name="shopAlterationAttachs['+shopAttachIndex+'].memo" >'+val+'</textarea></div>';
				}},
			{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex, obj){
					shopAttachIndex++;
					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
				}}
		];
		var $shopAttachmGrid=$('#table-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
			cols: cols,
			checkCol: false,
			autoLoad: true
		});
		var $addAttach = $("#addAttach");
		var attachIdnex = 0;
		var option1 = {
			dataType: "json",
			uploadToFileServer:true,
			uploadSize: "fileurl",
			callback : function(data){
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
				for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					$shopAttachmGrid.addRow(row,null,1);
				}
			}
		}
		$addAttach.file_upload(option1);
		
	
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= '/shop/alteration/edit.jhtml?id='+resultMsg.objx;
				});
			}
		 });
		 
		 var $deleteAttachment = $(".deleteAttachment");
         $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
         });
         
        var inputList = $(".shopInfoBC").find("label");
		var businessCategory = "${shopInfo.businessCategory!0}";
		check(inputList,businessCategory);

		
		function check(input,value){
			var newArrList = new Array();
			newArrList = value.split(",");
			input.each(function(){
				var name = $(this).text();
				if(explore(name,newArrList)){
					$(this).find('input').prop("checked",true);
				}
			});
		}
		function explore(str,newArrList){
			if(newArrList.length>0){
				for(var i=0;i<newArrList.length;i++){
					if(newArrList[i]==str){
						return true;
					}
				}
			}
			return false;
		}
});

var number = {
	extractNumber:function(a,len){
		var $this = $(a).val();
		if(len!=null){
			if($this.length>len){
				var tval = $this.substring(0,len);
				$(a).val(tval);
			}
		}
		if(!number.isRealNum($this)){
			$(a).val("");
		}
	},isRealNum:function(val){
		if(val === "" || val ==null){
        return false;
	    }
	    if(!isNaN(val)){
	        return true;
	    }else{
	        return false;
	    }
	}
}
function editQty(t,e){
	extractNumber(t,3,false,e);
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("门店变更")}
	</div>
	<form id="inputForm" action="/shop/alteration/save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("单号")}:
					</th>
					<td>
						<input type="hidden" class="text" maxlength="200"  btn-fun="clear" />
					</td>
					<th>
						${message("单据状态")}:
					</th>
					<td>
						<input type="hidden" class="text" maxlength="200"  btn-fun="clear" />
					</td>
					<th>
						${message("经销商授权编码")}:
					</th>
					<td>
						${store.grantCode}
					</td>
				</tr>
				<tr>
					<th>
						${message("经销商名称")}:
					</th>
					<td>
						${store.name}
						<input type="hidden" value="${store.id}" name ="storeId" class="text" maxlength="200"  btn-fun="clear" />
					</td>
					<th>
						${message("合伙人名称")}:
					</th>
					<td>
						${shopInfo.partnerName}
					</td>
					<th>
						${message("合伙人电话")}:
					</th>
					<td>
						${shopInfo.partnerPhone}
					</td>
				</tr>
				<tr>
					<th>
						${message("手机号码")}:
					</th>
					<td>
						${store.headPhone}
					</td>
					<th>
						${message("固体号码")}:
					</th>
					<td>
						${store.fixedNumber}
					</td>
					<th>
						${message("区域")}:
					</th>
					<td>
						${store.region}
					</td>
				</tr>
				<tr>
					<th>
						${message("地区")}:
					</th>
					<td colspan="3" class="area">
						<input type="hidden" id="storeAreaId" value="${(store.headNewArea.id)!}" treePath="${(store.headNewArea.treePath)!}"/>
					</td>
					<th>
						${message("机构")}:
					</th>
					<td>
						<span class="saleOrgName">${shopInfo.saleOrg.name}</span>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("旧店信息")}:</div>
			<table class="input input-edit" style="width:865px">
            	<tr>
            		<th>${message("操作类型")}:</th>
            		<td>
            			<input type="text" name="operationType" value="变更" class="text"  btn-fun="clear" readOnly/>
            			<input type="hidden" name="shopInfoId" value="${shopInfo.id}" />
            		</td>
            		<th>${message("变更类型")}:</th>
            		<td>
            			<input type="text" name="changeType" value="搬迁" class="text"  btn-fun="clear" readOnly/>
            		</td>
            		<th><span class="requiredField">*</span>${message("关店日期")}:</th>
            		<td>
            			<input id="startTime" name="closingTime" class="text" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店面积")}:</th>
            		<td>
            			<div class="nums-input ov">
							<input class="b decrease" value="-" type="button" />
							<input class="t acreage" value="${shopInfo.acreage}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  readOnly/>
							<input value="+" class="b increase"type="button" />
						</div>
            		</td>
            		<th>${message("门店位置")}:</th>
            		<td>
            			<input type="text" value="${shopInfo.positionType}" class="text" maxlength="200" oninput="number.extractNumber(this)" btn-fun="clear" readOnly/>
            		</td>
            		<th>${message("公司性质")}:</th>
            		<td>
            			${shopInfo.companyNature}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店所有")}:</th>
            		<td>
            			${shopInfo.shopOwnership}
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			<input type="text" value="${shopInfo.administrativeRank}" class="text" maxlength="200" btn-fun="clear" readOnly/>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("经营品类")}:</th>
            		<td colspan="3">
            			<div class="shopInfoBC">
            				[#list BusinessCategory as bc ]
							<label><input  class="check js-iname text" type="checkbox" value="${bc.value}"/>${message("${bc.value}")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							[/#list]
						</div>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("旧店地址")}:</th>
            		<td colspan="3" class="area">
            			<input type="hidden" id="shopInfoAreaId" value="${(shopInfo.area.id)!}" treePath="${(shopInfo.area.treePath)!}"/>
						<input type="text" value="${shopInfo.address}" class="detailed-address"  readOnly/>
            		</td>
            	</tr>
			</table>
			<div class="title-style">${message("新店信息")}:</div>
			<table class="input input-edit" style="width:865px">
            	<tr>
            		<th>${message("建店日期")}:</th>
            		<td>
            			<input id="startTime" name="openDate" class="text" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th>${message("门店面积")}:</th>
            		<td>
            			<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t acreage" name="acreage" value="" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
            		</td>
            		<th>${message("门店位置")}:</th>
            		<td>
            			<select name="positionType" class="text positionType">
	    					<option></option>
							<option value="建材市场">${message("建材市场")}</option>
							<option value="临街商铺">${message("临街商铺")}</option>
							<option value="家具商城">${message("家具商城")}</option>
							<option value="装饰公司">${message("装饰公司")}</option>
						</select>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("公司性质")}:</th>
            		<td>
            			<select name="companyNature" class="text companyNature">
	    					<option></option>
							<option value="独立公司">${message("独立公司")}</option>
							<option value="合伙公司">${message("合伙公司")}</option>
							<option value="个体工商户">${message("个体工商户")}</option>
						</select>
            		</td>
            		<th>${message("门店所有")}:</th>
            		<td>
            			<select name="shopOwnership" class="text shopOwnership">
	    					<option></option>
							<option value="租赁">${message("租赁")}</option>
							<option value="个人物业">${message("个体工商户")}</option>
						</select>
            		</td>
            		<th>${message("月租金（元/m²）")}:</th>
            		<td>
            			<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t acreage" name="monthlyRent" value="" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			<select name="administrativeRank" class="text administrativeRank">
            				<option></option>
							<option value="省级" >${message("省级")}</option>
							<option value="市级" >${message("市级")}</option>
							<option value="区县级">${message("区县级")}</option>
							<option value="乡镇级">${message("乡镇级")}</option>
						</select>
            		</td>
            		<th>${message("是否需要设计")}:</th>
            		<td>
            			<select name="isDesign" class="text isDesign">
            				<option></option>
							<option value="false" >${message("否")}</option>
							<option value="true"  >${message("是")}</option>
						</select>
            		</td>
            		<th>
						${message("店名")}:
					</th>
					<td>
						<input type="text" name="shopName" class="text" maxlength="200"  btn-fun="clear" />
					</td>
            	</tr>
            	<tr>
            		<th>${message("经营品类")}:</th>
            		<td colspan="3">
            			<div class="businessCategory"> 
							[#list BusinessCategory as bc ]
							<label><input  class="check js-iname text" name="businessCategory" type="checkbox" value="${bc.value}"/>${message("${bc.value}")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							[/#list]
						</div>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("新店地址")}:</th>
            		<td colspan="3">
            			<input type="hidden" id="newShopAreaId" name="newShopArea.id"/>
            			<input type="text" name="address" class="detailed-address" placeholder="请输入详细地址！"  maxlength="200"/>
            		</td>
            	</tr>
            	<tr>
					<th>
						${message("门店搬迁原因")}:
					</th>
					<td colspan="4">
						<textarea name="moveReason" class="text"></textarea>
					</td>
				</tr>
			</table>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
		        ${message("上传附件/门店照片：门店相片（门头远近距离照，收银台，各样板区）")}:
		            <div class="btns">
		                <a href="javascript:;" id="addAttach" class="button">添加附件</a>
		            </div>
		        </div>
		        <table id="table-attach" style="width:850px"></table>
		   </table>
		   [#-- 
		   <div class="title-style">${message("区域经理意见")}:</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("客户")}:</th>
            		<td>
            			${store.name}
            		</td>
            		<th>${message("经销商学历")}:</th>
            		<td>
            			${store.dealerGrade}
            		</td>
            		<th>${message("经销商加盟时间")}:</th>
            		<td>
            			${store.activeDate}
            		</td>
            		<th>${message("经销商性别")}:</th>
            		<td>
            			[#if store.dealerSex ==0]男[/#if]
            			[#if store.dealerSex ==1]女[/#if]
            		</td>
            	</tr>
            	<tr>
            		<th>${message("总经销商")}:</th>
            		<td>
            			${store.franchisee}
            		</td>
            		<th>${message("门店类型")}:</th>
            		<td>
            			<select name="type" class="text type">
	    					<option></option>
							<option value="专卖店">${message("专卖店")}</option>
							<option value="大家居">${message("大家居")}</option>
							<option value="一址多名">${message("一址多名")}</option>
							<option value="多品类">${message("多品类")}</option>
							<option value="产品专区">${message("产品专区")}</option>
						</select>
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			<select class="text accountTypeCode">
            				<option></option>
            				<option value="省级">${message("省级")}</option>
							<option value="市级">${message("市级")}</option>
							<option value="区县级">${message("区县级")}</option>
							<option value="乡镇级">${message("乡镇级")}</option>
            			</select>
            		</td>
            		<th>${message("所属销售平台")}:</th>
            		<td>
            			${store.salesPlatform.name}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所属品牌")}:</th>
            		<td>
            			<select name="belongBrand" class="text">
            				<option></option>
							<option value="国际出口" >${message("国际出口")}</option>
							<option value="木香居地板"   >${message("木香居地板")}</option>
							<option value="大自然工程"   >${message("大自然工程")}</option>
							<option value="nature地板"   >${message("nature地板")}</option>
							<option value="大自然地板"   >${message("大自然地板")}</option>
						</select>
            		</td>
            		<th>${message("VI版本")}:</th>
            		<td>
            			<select name="viVersion" class="text viVersion">
            				<option></option>
							<option value="2013年以前版本">${message("2013年以前版本")}</option>
							<option value="2013版本">${message("2013版本")}</option>
	    					<option value="2017版本">${message("2017版本")}</option>
						</select>
            		</td>
            		<th>${message("所属部门")}:</th>
            		<td>
            			<input type="text" name="department" value="渠道" class="text" maxlength="200"  btn-fun="clear" readOnly/>
            			<!-- <select name="department" class="text department" onfocus="this.defOpt=this.selectedIndex" onchange="this.selectedIndex=this.defOpt;">
							<option></option>
							<option value="渠道">${message("渠道")}</option>
							<option value="工程">${message("工程")}</option>
							<option value="nature">${message("nature")}</option>
							<option value="财务帐户">${message("财务帐户")}</option>
							<option value="电商">${message("电商")}</option>
							<option value="其他">${message("其他")}</option>
						</select> -->
            		</td>
            		<!-- <th>${message("关闭原因")}:</th>
            		<td>
            			<input type="text" name="shutDownMenu" class="text" maxlength="200"  btn-fun="clear" />
            		</td> -->
            	</tr>
            	<tr>
            		<th>${message("所含品牌")}:</th>
            		<td colspan="3">
            			<div class="shopSign"> 
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" vaule="国际出口"/>${message("国际出口")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="木香居地板" />${message("木香居地板")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="大自然工程" />${message("大自然工程")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="nature地板" />${message("nature地板")}</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="大自然地板" />${message("大自然地板")}</label>
						</div>
            		</td>
            		<th>${message("销售渠道")}:</th>
            		<td>
            			<select name="salesChannel" class="text salesChannel">
	    					<option></option>
							<option value="零售">${message("零售")}</option>
							<option value="工程">${message("工程")}</option>
							<option value="家装">${message("家装")}</option>
						</select>
            		</td>
            	</tr>
				<!-- <tr>
					<th>${message("省长意见")}:</th>
					<td colspan="7">
	                    <textarea class="text" name="" style="width:800px;height:160px;"></textarea>
	                </td>
				</tr> -->
			</table>
			<div class="title-style">${message("渠道部意见")}:</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			<input type="text" name="authorizationCode" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增档案编号")}:</th>
            		<td>
            			<input type="text" name="increaseArchivesCode" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增时间")}:</th>
            		<td>
            			<input id="startTime" name="newTime" class="text" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th>${message("减少档案编号")}:</th>
            		<td>
            			<input type="text" name="decreaseArchivesCode" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            	</tr>
            	<tr>
            		<th>${message("减少时间")}:</th>
            		<td>
            			<input id="startTime" name="decreaseTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th></th>
            		<td></td>
            		<th></th>
            		<td></td>
            		<th></th>
            		<td></td>
            	</tr>
            	<tr>
            		<th>${message("门店情况备注")}:</th>
            		<td colspan="7">
	                    <textarea class="text" name="shopCaseNote" style="width:800px;height:160px;"></textarea>
	                </td>
            	</tr>
			</table> --]
			
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>