<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑门店变更")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
function editQty(t,e){
	extractNumber(t,3,false,e);
}
function Cancel(e){
	ajaxSubmit(e,{
		url:'cancel.jhtml?id='+${shopAlteration.id},
		method:"post",
		//data:data,
		isConfirm:true,
		confirmText : '您确定要作废吗？',
		callback:function(resultMsg){
		 	$.message_timer(resultMsg.type,resultMsg.content,5000,function(){
		 		location.href= '/shop/alteration/view.jhtml?shopInfoId='+resultMsg.objx;
			})
		 }
	
	});
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	$("#storeAreaId").lSelect();
	$("#shopInfoAreaId").lSelect();
	$("#newShopAreaId").lSelect();
	
	$(".area").find("select").each(function(){
		$(this).attr("disabled",true);
	});
	
	// 表单验证
	$inputForm.validate({
		rules: {
    		closingTime: "required",
    		operationType: "required",
    		closingTime: "required",
    		openDate: "required",
    		acreage: "required",
    		positionType: "required",
    		companyNature: "required",
    		shopOwnership: "required",
    		administrativeRank: "required",
    		isDesign: "required",
    		shopName: "required",
    		moveReason: "required",
		},
		messages: {
			name: {
				pattern: "${message("非法字符")}",
				remote: "${message("已存在")}"
			}
		}
	});

		//现场照片附件
		var shop_attachs = ${shop_attach};
		var shopAttachIndex=0;
		var cols = [
			{ title:'${message("附件")}', name:'content',align:'center',renderer:function(val,item,rowIndex, obj){
				if(obj==undefined){
					var url = item.url;
					var fileObj = getfileObj(item.file_name,item.name,item.suffix);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['shopAlterationAttachs['+shopAttachIndex+'].id']=item.id;
					hideValues['shopAlterationAttachs['+shopAttachIndex+'].url']=url;
					hideValues['shopAlterationAttachs['+shopAttachIndex+'].suffix']=fileObj.suffix;
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : item.create_date,
						textName:'shopAlterationAttachs['+shopAttachIndex+'].name',
						hideValues:hideValues
					});
				}else{
					var url = item.url;
					var fileObj = getfileObj(item.name);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues['shopAlterationAttachs['+shopAttachIndex+'].url']=url;
					hideValues['shopAlterationAttachs['+shopAttachIndex+'].suffix']=fileObj.suffix;
					
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : '',
						textName:'shopAlterationAttachs['+shopAttachIndex+'].name',
						hideValues:hideValues
					});
				}
			}},
			{ title:'${message("备注")}', name:'memo' ,align:'center', renderer: function(val,item,rowIndex, obj){
					return '<div><textarea class="text file_memo" name="shopAlterationAttachs['+shopAttachIndex+'].memo" >'+val+'</textarea></div>';
				}},
			{ title:'${message("操作")}',align:'center', renderer: function(val,item,rowIndex, obj){
					shopAttachIndex++;
					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
				}}
		];
		var $shopAttachmGrid=$('#table-attach').mmGrid({
			fullWidthRows:true,
			height:'auto',
			cols: cols,
			items:shop_attachs,
			checkCol: false,
			autoLoad: true
		});
		var $addAttach = $("#addAttach");
		var attachIdnex = 0;
		var option1 = {
			dataType: "json",
			uploadToFileServer:true,
			uploadSize: "fileurl",
			callback : function(data){
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
				for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					$shopAttachmGrid.addRow(row,null,1);
				}
			}
		}
		$addAttach.file_upload(option1);
		
		 var $deleteAttachment = $(".deleteAttachment");
         $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
         });

		$("form").bindAttribute({
			isConfirm:true,
		    callback: function(resultMsg){
		        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
		    }
		});
		
		var inputList = $(".shopInfoBC").find("label");
		var shopInfoBC = "${shopAlteration.infoShop.businessCategory!0}";
		check(inputList,shopInfoBC);
		
		var inputList = $(".businessCategory").find("label");
		var businessCategory = "${shopAlteration.businessCategory!0}";
		check(inputList,businessCategory);
		
		var inputList = $(".shopSign").find("label");
		var shopSign = "${shopAlteration.shopSign!0}";
		check(inputList,shopSign);
		
		function check(input,value){
			var newArrList = new Array();
			newArrList = value.split(",");
			input.each(function(){
				var name = $(this).text();
				if(explore(name,newArrList)){
					$(this).find('input').prop("checked",true);
				}
			});
		}
		function explore(str,newArrList){
			if(newArrList.length>0){
				for(var i=0;i<newArrList.length;i++){
					if(newArrList[i]==str){
						return true;
					}
				}
			}
			return false;
		}
		
		
		
		[#if shopAlteration.wfId!=null]
		 	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${shopAlteration.wfId}");
		 [/#if] 
		
});

var number = {
	extractNumber:function(a,len){
		var $this = $(a).val();
		if(len!=null){
			if($this.length>len){
				var tval = $this.substring(0,len);
				$(a).val(tval);
			}
		}
		if(!number.isRealNum($this)){
			$(a).val("");
		}
	},isRealNum:function(val){
		if(val === "" || val ==null){
        return false;
	    }
	    if(!isNaN(val)){
	        return true;
	    }else{
	        return false;
	    }
	}
}

function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		$.message_confirm("您确定要审批流程吗？",function(){
			var objTypeId = 10008;
			//var modelId = 12507;//大自然测试
			var modelId = 305022;//大自然开发 
			var url="check_wf.jhtml?id=${shopAlteration.id}&modelId="+modelId+"&objTypeId="+objTypeId;
			var data = $form.serialize();
			ajaxSubmit(e,{
				method:'post',
				url:url,
				async: true,
				callback: function(resultMsg) {
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				}
			});
		});
	}
}
function saveform(e){
	$.message_confirm("您确定要提交吗？",function(){
		//获取表单所有数据
		var params = $("#inputForm").serializeArray();
		//定义url
		var url = 'saveform.jhtml?Type='+e;
		ajaxSubmit(e,{
			method:'post',
			url:url,
			data:params,
			async: true,
			callback: function(resultMsg) {
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑门店变更")}
	</div>
	<form id="inputForm" action="/shop/alteration/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${shopAlteration.id}" />
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("单号")}:
					</th>
					<td>
						${shopAlteration.sn}
					</td>
					<th>
						${message("单据状态")}:
					</th>
					<td>
						[#if shopAlteration.statuss == "saved" ]<span class="blue">${message("已保存")}</span>[/#if]
						[#if shopAlteration.statuss == "submitted" ]<span class="green">${message("已生效")}</span>[/#if]
						[#if shopAlteration.statuss == "terminated" ]<span class="red">${message("已终止")}</span>[/#if]
						[#if shopAlteration.statuss == "underway" ]<span style="color:orange;">${message("进行中")}</span>[/#if]
					</td>
					<th>
						${message("经销商授权编码")}:
					</th>
					<td>
						${shopAlteration.store.grantCode}
					</td>
				</tr>
				<tr>
					<th>
						${message("经销商名称")}:
					</th>
					<td>
						${shopAlteration.store.name}
						<input type="hidden" value="${shopAlteration.store.id}" name ="storeId" class="text" maxlength="200"  btn-fun="clear" />
					</td>
					<th>
						${message("合伙人名称")}:
					</th>
					<td>
						${shopAlteration.infoShop.partnerName}
					</td>
					<th>
						${message("合伙人电话")}:
					</th>
					<td>
						${shopAlteration.infoShop.partnerPhone}
					</td>
				</tr>
				<tr>
					<th>
						${message("手机号码")}:
					</th>
					<td>
						${shopAlteration.store.headPhone}
					</td>
					<th>
						${message("固体号码")}:
					</th>
					<td>
						${shopAlteration.store.fixedNumber}
					</td>
					<th>
						${message("区域")}:
					</th>
					<td>
						${shopAlteration.store.region}
					</td>
				</tr>
				<tr>
					<th>
						${message("地区")}:
					</th>
					<td colspan="3" class="area">
						<input type="hidden" id="storeAreaId" value="${(shopAlteration.store.headNewArea.id)!}" treePath="${(shopAlteration.store.headNewArea.treePath)!}"/>
					</td>
					<th>
						${message("机构")}:
					</th>
					<td>
						<span class="saleOrgName">${shopAlteration.saleOrg.name}</span>
					</td>
				</tr>
			</table>
			<div class="title-style">${message("旧店信息")}:</div>
			<table class="input input-edit" style="width:865px">
            	<tr>
            		<th>${message("操作类型")}:</th>
            		<td>
            			<input type="text" value="变更" class="text"  btn-fun="clear" readOnly/>
            			<input type="hidden" name="shopInfoId" value="${shopAlteration.infoShop.id}" />
            		</td>
            		<th>${message("变更类型")}:</th>
            		<td>
            			<input type="text" name="changeType" value="${shopAlteration.changeType}" class="text"  btn-fun="clear" readOnly/>
            		</td>
            		<th><span class="requiredField">*</span>${message("关店日期")}:</th>
            		<td>
            			<input id="startTime" name="closingTime" value="${shopAlteration.closingTime}" class="text" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店面积")}:</th>
            		<td>
            			<div class="nums-input ov">
							<input class="b decrease" value="-" type="button" />
							<input class="t acreage" value="${shopAlteration.infoShop.acreage}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  readOnly/>
							<input value="+" class="b increase"type="button" />
						</div>
            		</td>
            		<th>${message("门店位置")}:</th>
            		<td>
            			${shopAlteration.infoShop.positionType}
            		</td>
            		<th>${message("公司性质")}:</th>
            		<td>
            			${shopAlteration.infoShop.companyNature}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("门店所有")}:</th>
            		<td>
            			${shopAlteration.infoShop.shopOwnership}
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			${shopAlteration.infoShop.administrativeRank}
            		</td>
            		<th>${message("门店名称")}:</th>
            		<td>${shopAlteration.infoShop.shopName}</td>
            	</tr>
            	<tr>
            		<th>${message("经营品类")}:</th>
            		<td colspan="3">
            			<div class="shopInfoBC">
							[#list BusinessCategory as bc ]
							<label><input  class="check js-iname text" type="checkbox" value="${bc.value}"/>${message("${bc.value}")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							[/#list]
						</div>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("旧店地址")}:</th>
            		<td colspan="3" class="area">
            			<input type="hidden" id="shopInfoAreaId" value="${(shopAlteration.infoShop.area.id)!}" treePath="${(shopAlteration.infoShop.area.treePath)!}"/>
						<input type="text" value="${shopAlteration.infoShop.address}" class="detailed-address" readOnly/>
            		</td>
            	</tr>
			</table>
			<div class="title-style">${message("新店信息")}:</div>
			<table class="input input-edit" style="width:865px">
            	<tr>
            		<th>${message("建店日期")}:</th>
            		<td>
            			<input id="startTime" name="openDate" value="${shopAlteration.openDate}" class="text" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th>${message("门店面积")}:</th>
            		<td>
            			<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t acreage" name="acreage" value="${shopAlteration.acreage}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
            		</td>
            		<th>${message("门店位置")}:</th>
            		<td>
            			<select name="positionType" class="text positionType">
	    					<option></option>
							<option value="建材市场"[#if shopAlteration.positionType="建材市场"] selected[/#if]>${message("建材市场")}</option>
							<option value="临街商铺"[#if shopAlteration.positionType="临街商铺"] selected[/#if]>${message("临街商铺")}</option>
							<option value="家具商城"[#if shopAlteration.positionType="家具商城"] selected[/#if]>${message("家具商城")}</option>
							<option value="装饰公司"[#if shopAlteration.positionType="装饰公司"] selected[/#if]>${message("装饰公司")}</option>
						</select>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("公司性质")}:</th>
            		<td>
            			<select name="companyNature" class="text companyNature">
	    					<option></option>
							<option value="独立公司"[#if shopAlteration.companyNature="独立公司"] selected[/#if]>${message("独立公司")}</option>
							<option value="合伙公司"[#if shopAlteration.companyNature="合伙公司"] selected[/#if]>${message("合伙公司")}</option>
							<option value="个体工商户"[#if shopAlteration.companyNature="个体工商户"] selected[/#if]>${message("个体工商户")}</option>
						</select>
            		</td>
            		<th>${message("门店所有")}:</th>
            		<td>
            			<select name="shopOwnership" class="text shopOwnership">
	    					<option></option>
							<option value="租赁"[#if shopAlteration.shopOwnership="租赁"] selected[/#if]>${message("租赁")}</option>
							<option value="个人物业"[#if shopAlteration.shopOwnership="个人物业"] selected[/#if]>${message("个体工商户")}</option>
						</select>
            		</td>
            		<th>${message("月租金（元/m²）")}:</th>
            		<td>
            			<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t acreage" name="monthlyRent" value="${shopAlteration.monthlyRent}" mindata="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			<select name="administrativeRank" class="text administrativeRank">
							<option></option>
							<option value="省级"[#if shopAlteration.administrativeRank="省级"] selected[/#if]>${message("省级")}</option>
							<option value="市级"[#if shopAlteration.administrativeRank="市级"] selected[/#if]>${message("市级")}</option>
							<option value="区县级"[#if shopAlteration.administrativeRank="区县级"] selected[/#if]>${message("区县级")}</option>
							<option value="乡镇级"[#if shopAlteration.administrativeRank="乡镇级"] selected[/#if]>${message("乡镇级")}</option>
						</select>
            		</td>
            		<th>${message("是否需要设计")}:</th>
            		<td>
            			<select name="isDesign" class="text isDesign">
            				<option></option>
							<option value="false"[#if shopAlteration.isDesign == false] selected[/#if]>${message("否")}</option>
							<option value="true"[#if shopAlteration.isDesign == true] selected[/#if]>${message("是")}</option>
						</select>
            		</td>
            		<th>
						${message("店名")}:
					</th>
					<td>
						<input type="text" name="shopName" value="${shopAlteration.shopName}" class="text" maxlength="200"  btn-fun="clear" />
					</td>
            	</tr>
            	<tr>
            		<th>${message("经营品类")}:</th>
            		<td colspan="3">
            			<div class="businessCategory"> 
							[#list BusinessCategory as bc ]
							<label><input  class="check js-iname text" name="businessCategory" type="checkbox" value="${bc.value}"/>${message("${bc.value}")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							[/#list]
						</div>
            		</td>
            	</tr>
            	<tr>
            		<th>${message("新店地址")}:</th>
            		<td colspan="3">
            			<input type="hidden" id="newShopAreaId" name="newShopArea.id" value="${shopAlteration.newShopArea.id!0}" treePath="${(shopAlteration.newShopArea.treePath)!}"/>
            			<input type="text" name="address" value="${shopAlteration.address}" class="detailed-address" maxlength="200"/>
            		</td>
            	</tr>
            	<tr>
					<th>
						${message("门店搬迁原因")}:
					</th>
					<td colspan="4">
						<textarea name="moveReason" class="text">${shopAlteration.moveReason}</textarea>
					</td>
				</tr>
			</table>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
				<div class="title-style">
		        ${message("上传附件/门店照片：门店相片（门头远近距离照，收银台，各样板区）")}:
		            <div class="btns">
		                <a href="javascript:;" id="addAttach" class="button">添加附件</a>
		            </div>
		        </div>
		        <table id="table-attach" style="width:850px"></table>
		   </table>
		   [#if shopAlteration.wfId != null]
		   [#if qyjl||qyjls]
		   <div class="title-style">${message("区域经理意见")}:[#if node.name?contains("区域经理")]<input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/>[/#if]</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("客户")}:</th>
            		<td>
            			${shopAlteration.store.name}
            		</td>
            		<th>${message("经销商学历")}:</th>
            		<td>
            			${shopAlteration.store.dealerGrade}
            		</td>
            		<th>${message("经销商加盟时间")}:</th>
            		<td>
            			${shopAlteration.store.activeDate}
            		</td>
            		<th>${message("经销商性别")}:</th>
            		<td>
            			[#if shopAlteration.store.dealerSex ==0]男[/#if]
            			[#if shopAlteration.store.dealerSex ==1]女[/#if]
            		</td>
            	</tr>
            	<tr>
            		<th>${message("总经销商")}:</th>
            		<td>
            			${shopAlteration.store.franchisee}
            		</td>
            		<th>${message("门店类型")}:</th>
            		<td>
            			<select name="type" class="text type">
	    					<option></option>
							<option value="多品种专卖店"[#if shopAlteration.infoShop.type="多品种专卖店"] selected[/#if]>${message("多品种专卖店")}</option>
							<option value="单品种专卖店"[#if shopAlteration.infoShop.type="单品种专卖店"] selected[/#if]>${message("单品种专卖店")}</option>
							<option value="多品种综合店"[#if shopAlteration.infoShop.type="多品种综合店"] selected[/#if]>${message("多品种综合店")}</option>
							<option value="家具公司专卖"[#if shopAlteration.infoShop.type="家具公司专卖"] selected[/#if]>${message("家具公司专卖")}</option>
							<option value="门店专区"[#if shopAlteration.infoShop.type="门店专区"] selected[/#if]>${message("门店专区")}</option>
						</select>
            		</td>
            		<th>${message("城市行政等级")}:</th>
            		<td>
            			<select name="administrativeRank" class="text administrativeRank">
            				<option></option>
							<option value="省级"[#if shopAlteration.administrativeRank="省级"] selected[/#if]>${message("省级")}</option>
							<option value="市级"[#if shopAlteration.administrativeRank="市级"] selected[/#if]>${message("市级")}</option>
							<option value="区县级"[#if shopAlteration.administrativeRank="区县级"] selected[/#if]>${message("区县级")}</option>
							<option value="乡镇级"[#if shopAlteration.administrativeRank="乡镇级"] selected[/#if]>${message("乡镇级")}</option>
						</select>
            		</td>
            		<th>${message("所属销售平台")}:</th>
            		<td>
            			${shopAlteration.store.salesPlatform.name}
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所属品牌")}:</th>
            		<td>
            			${message("大自然地板")}
            			<input type="hidden" name="belongBrand" value="大自然地板"/>
            			<!-- <select name="belongBrand" class="text">
            				<option></option>
							<option value="国际出口"[#if shopAlteration.belongBrand="国际出口"] selected[/#if]>${message("国际出口")}</option>
							<option value="木香居地板"[#if shopAlteration.belongBrand="木香居地板"] selected[/#if]>${message("木香居地板")}</option>
							<option value="大自然工程"[#if shopAlteration.belongBrand="大自然工程"] selected[/#if]>${message("大自然工程")}</option>
							<option value="nature地板"[#if shopAlteration.belongBrand="nature地板"] selected[/#if]>${message("nature地板")}</option>
							<option value="大自然地板"[#if shopAlteration.belongBrand="大自然地板"] selected[/#if]>${message("大自然地板")}</option>
						</select> -->
            		</td>
            			<th>${message("VI版本")}:</th>
            		<td>
            			<select name="viVersion" class="text viVersion">
            				<option></option>
							<option value="2013年以前版本"[#if shopAlteration.infoShop.viVersion="2013年以前版本"] selected[/#if]>${message("2013年以前版本")}</option>
							<option value="2013版本"[#if shopAlteration.infoShop.viVersion="2013版本"] selected[/#if]>${message("2013版本")}</option>
	    					<option value="2017版本"[#if shopAlteration.infoShop.viVersion="2017版本"] selected[/#if]>${message("2017版本")}</option>
						</select>
            		</td>
            		<th>${message("所属部门")}:</th>
            		<td>
            			${message("渠道")}
            			<input type="hidden" name="department" value="渠道" class="text" maxlength="200"  btn-fun="clear" readOnly/>
            			<!-- <select name="department" class="text department" onfocus="this.defOpt=this.selectedIndex" onchange="this.selectedIndex=this.defOpt;">
							<option></option>
							<option value="渠道">${message("渠道")}</option>
							<option value="工程">${message("工程")}</option>
							<option value="nature">${message("nature")}</option>
							<option value="财务帐户">${message("财务帐户")}</option>
							<option value="电商">${message("电商")}</option>
							<option value="其他">${message("其他")}</option>
						</select> -->
            		</td>
            	</tr>
            	<tr>
            		<th>${message("所含品牌")}:</th>
            		<td colspan="3">
            			<div class="shopSign"> 
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="木门" [#if shopAlteration.infoShop.inclusiveBrand?contains("木门")] checked[/#if]/>${message("木门")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="壁纸" [#if shopAlteration.infoShop.inclusiveBrand?contains("壁纸")] checked[/#if]/>${message("壁纸")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="橱衣柜" [#if shopAlteration.infoShop.inclusiveBrand?contains("橱衣柜")] checked[/#if]/>${message("橱衣柜")}</label>
							  &nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="nature" [#if shopAlteration.infoShop.inclusiveBrand?contains("nature")] checked[/#if]/>${message("nature")}</label>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<label><input  class="check js-iname text" name="shopSign" type="checkbox" value="其他" [#if shopAlteration.infoShop.inclusiveBrand?contains("其他")] checked[/#if]/>${message("其他")}</label>
						</div>
            		</td>
            		<th>${message("销售渠道")}:</th>
            		<td>
            			<select name="salesChannel" id="salesChannel" class="text salesChannel">
	    					<option></option>
							<option value="零售"[#if shopAlteration.infoShop.salesChannel="零售"] selected[/#if]>${message("零售")}</option>
							<option value="工程"[#if shopAlteration.infoShop.salesChannel="工程"] selected[/#if]>${message("工程")}</option>
							<option value="家装"[#if shopAlteration.infoShop.salesChannel="家装"] selected[/#if]>${message("家装")}</option>
						</select>
            		</td>
            	</tr>
				<!-- <tr>
					<th>${message("省长意见")}:</th>
					<td colspan="7">
	                    <textarea class="text" name="" style="width:800px;height:160px;"></textarea>
	                </td>
				</tr> -->
			</table>
			[/#if]
			[#if qdzy||qdzys]
			<div class="title-style">${message("渠道部意见")}:[#if node.name?contains("渠道")]<input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/>[/#if]</div>
			<table class="input input-edit">
            	<tr>
            		<th>${message("门店授权编号")}:</th>
            		<td>
            			<input type="text" name="authorizationCode" value="${shopAlteration.authorizationCode}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增档案编号")}:</th>
            		<td>
            			<input type="text" name="increaseArchivesCode" value="${shopAlteration.increaseArchivesCode}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            		<th>${message("新增时间")}:</th>
            		<td>
            			<input id="startTime" name="newTime" value="${shopAlteration.newTime}" class="text" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th>${message("减少档案编号")}:</th>
            		<td>
            			<input type="text" name="decreaseArchivesCode" value="${shopAlteration.decreaseArchivesCode}" class="text" maxlength="200"  btn-fun="clear" />
            		</td>
            	</tr>
            	<tr>
            		<th>${message("减少时间")}:</th>
            		<td>
            			<input id="startTime" name="decreaseTime" value="${shopAlteration.decreaseTime}" class="text" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" readOnly/>
						<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
            		</td>
            		<th></th>
            		<td></td>
            		<th></th>
            		<td></td>
            		<th></th>
            		<td></td>
            	</tr>
            	<tr>
            		<th>${message("门店情况备注")}:</th>
            		<td colspan="7">
	                    <textarea class="text" name="shopCaseNote" style="width:800px;height:160px;">${shopAlteration.shopCaseNote}</textarea>
	                </td>
            	</tr>
			</table>
			[/#if]
			[/#if]
		</div>
		<div class="fixed-top">
		[#if shopAlteration.wfId == null]
			<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
		[/#if]
		[#if shopAlteration.statuss != "cancel"]
			<a href="javascript:void(0);" class="iconButton" id="fenpeiButton" onclick="Cancel(this)">
				<span class="fenpeiIcon">&nbsp;</span>${message("作废")}
			</a>
		[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>