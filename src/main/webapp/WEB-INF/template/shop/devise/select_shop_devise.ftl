<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查询客户")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
	/**初始化多选下拉框*/
	initMultipleSelect();
		
	var cols = [
		{ title:'${message("单号")}', name:'sn' ,width:100, align:'center'},
		{ title:'${message("申请时间")}', name:'create_date', align:'center'},
		{ title:'${message("门店名称")}', name:'shop_name', align:'center'},
		{ title:'${message("单据状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result="";
			if (item.bills_status == 0) result = '<span class="blue">已保存</span>';
			else if (item.bills_status==1) result = '<span class="blue">进行中</span>';
			else if (item.bills_status == 2) result = '<span class="red">已完成</span>';
			else if (item.bills_status == 3) result = '<span class="green">已终止</span>';
			return result;
		}},
		{ title:'${message("门店地址")}', name:'shop_address', align:'center'}
	];
	var multiSelect = false;
	[#if multi==2]
		multiSelect = true;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		[#if multi!=2]checkByDblClickTd:true,[/#if]
		rowCursorPointer:true,
		formQuery:true,
        cols: cols,
        url: 'select_shop_devise_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins: [
            $('#paginator').mmPaginator()
        ]
    });
});
function childMethod(){
	return $mmGrid.selectedRows();
};
</script>
</head>
<body style="min-width: 0px;">
<form id="listForm" action="select_shop_devise_data.jhtml" method="post">
	<input type="hidden" name="multi" value="${multi}" />
	<input type="hidden" name="shopInfoId" value="${shopInfo.Id}" />
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
		    	<dl>
		    		<dt><p>${message("单号")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="sn" name="sn" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
	        </div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
	</form>
</body>
</html>