<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增门店设计")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
/* 调整附件 */
.nowrap > div >.t-edit {
    width: 80%;
    margin: 5px 0 -6px 0;
}
</style>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,2,false,e);
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	$("#deviseAreaId").lSelect();
	$(".area").find("select").each(function(){
		$(this).attr("disabled",true);
	});
	
	// 校验
	$inputForm.validate({
		rules: {
			shopInfoSn: "required",
			highLimit: "required",
			designBrand: "required",
			solidWoodRatio: "required",
            multilayerRatio: "required",
            intensifyRatio: "required",
            qqOrEmail: "required",
            predictConstructionTime: "required",
            predictStartsTime: "required",
            structure1: "required",
            structure2: "required",
            shopRenovationAttribute: "required",
		} ,
	});
	
	// 选择门店
	$("#selectShopInfo").click(function(){
		$("#selectShopInfo").bindQueryBtn({
			type:'shopInfo',
			title:'${message("查询门店")}',
			bindClick:false,
			url:'/shop/shopInfo/select_shopInfo.jhtml',
			callback:function(rows){
				if(rows.length > 0){
					var row = rows[0];
					$(".shopInfoSn").val(row.sn);
					$(".shopInfoId").val(row.id);
					$(".shopInfoShopName").val(row.shop_name)
					$(".applyCityText").html(row.area_name);
					$(".applyCity").val(row.area_name);
					$(".dealerText").html(row.distributor_name);
					$(".dealer").val(row.distributor_name);
					$(".phoneText").html(row.distributor_phone);
					$(".phone").val(row.distributor_phone);
					$(".areaText").html(row.acreage);
					$(".area").val(row.acreage);
					$(".shopAddressText").html(row.address);
					$('input[name="shopAddress"]').val(row.address)// 地址
					$("#deviseAreaId").val(row.area_id);
                    $("#deviseAreaId").attr("treepath",row.area_tree_path);
                    $("#deviseAreaId").lSelect();
                    //切换机构
                    $(".saleOrgName").text(row.sale_org_name==null?"":row.sale_org_name);
                    //锁定地址输入框
                    $(".area").find("select").each(function(){
						$(this).attr("disabled",true);
					});
					// 是否二次设计
					$.ajax({
		       			type:'POST',
		       			url:'/shop/devise/existShopInfo.jhtml',
		       			data:{shopInfoId:row.id},
		       			success:function(data) {
		       				if (data == 1) {
		       					$("#shopAttribute").val("是");
		       					$(".shopAttributeText").html("是");
		       				} else if (data == 0) {
		       					$("#shopAttribute").val("否");
		       					$(".shopAttributeText").html("否");
		       				}
		       			}
		       		})
				}
			}
		});
	});

	// 初始化附件
	initAttach("dealersAttachs", "addDealersAttach", "table-dealers-attach", 0);
	initAttach("storeContractAttachs", "addStoreContractAttach", "table-store-contract-attach", 3);
	initAttach("storePicturesAttachs", "addStorePictureAttach", "table-store-picture-attach", 4);
	initAttach("regionalManagerAttachs", "addRegionalManagerAttach", "table-regional-manager-attach", 1);	
	initAttach("designAttachs", "designAttach", "table-design-attach", 2);	
});

/**
 * 初始化附件，每个参数都是必填的
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param addAttachIdName 前端页面添加附件按钮的id值
 * @param tableIdName 前端页面table中的id值 
 * @param type 后台用来区分不同类型附件
 */
function initAttach(paramAttachs, addAttachIdName, tableIdName, type) {
    var index = 0;
	var attachCols = [				
    	{ title:'${message("附件")}',name:'content',width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues[paramAttachs+'['+index+'].url']=url;
			hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
			hideValues[paramAttachs+'['+index+'].type']=type;  // 装修验收照片附件1
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName: paramAttachs+'['+index+'].name',
				hideValues:hideValues
			});
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
    		index++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $grid=$('#'+tableIdName).mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: attachCols,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#"+addAttachIdName);
	var attachIdnex = 0;
	var option = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$grid.addRow(row,null,1);
	        }
        }
    }
    $addAttach.file_upload(option);
	
	/* 删除附件 */
    var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
}

// 保存
function save(e){
	var str = '您确定要保存吗？';
	var url = 'save.jhtml';
	var $form = $("#inputForm");
	
	if($form.valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= 'edit.jhtml?id='+resultMsg.objx;
				})
			}
		});
	}
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增门店设计")}
	</div>
	<form id="inputForm" action="/shop/devise/save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>${message("单号")}:</th>
					<td>
						<input type="hidden" class="text" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("单据状态")}:</th>
					<td>
						<!-- <input type="text" class="text" name="billsStatus" btn-fun="clear" readonly="readonly" /> -->
					</td>
					<th>${message("门店编码")}:</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="shopInfoId" class="text shopInfoId" value="" btn-fun="clear"/>
							<input type="text" name="shopInfoSn" class="text shopInfoSn" value="" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
							<input type="button" class="iconSearch" value="" id="selectShopInfo"/>
						</span>
					</td>
					<th>${message("申请城市")}:</th>
					<td>
					    <span class="applyCityText"></span>
						<input type="hidden" class="text applyCity" name="applyCity" maxlength="200" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("门店名称")}:</th>
					<td>
						<input type="text" class="test shopInfoShopName" name="shopInfoShopName" btn-fun="clear" />
					</td>
					<th>${message("经销商")}:</th>
					<td>
					    <span class="dealerText"></span>
						<input type="hidden" class="text dealer" name="dealer" btn-fun="clear" />
					</td>
					<th>${message("联系方式")}:</th>
					<td>
					    <span class="phoneText"></span>
						<input type="hidden" class="text phone" name="phone" btn-fun="clear" />
					</td>
					<th>${message("天花限高")}:</th>
					<td>
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t" name="highLimit" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
					</td>
				</tr>
				<tr>
					<th>${message("面积（㎡）")}:</th>
					<td>
					    <span class="areaText" style="text-align: center;display:block;"></span>
					    <input type="hidden" class="text area" name="area" btn-fun="clear" />
					</td>
					<th>${message("设计品牌")}:</th>
					<td>
						<select name="designBrand" class="text">
						    <option value=""></option>
                            <option value="0">${message("大自然综合店")}</option>
                            <option value="1">${message("大自然·三层专卖店")}</option>
                            <option value="2">${message("大自然·实木专卖店")}</option>
                            <option value="3">${message("大自然·强化专卖店")}</option>
                        </select>
					</td>
					<th>${message("门店地址")}:</th>
					<td colspan="3" class="area">
					    <input type="hidden" id ="deviseAreaId" name="deviseArea.id" />
					    <input type="text" name="shopAddress" class="detailed-address" maxlength="200" readonly/>
					</td>
				</tr>
				<tr>
					<th>${message("预订施工时间")}</th>
					<td>
						<input type="text" name="predictConstructionTime" class="text predictConstructionTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
					</td>
					<th>${message("预订开业时间")}</th>
					<td>
						<input type="text" name="predictStartsTime" class="text predictStartsTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
					</td>
					<th>${message("门店结构情况")}:</th>
					<td>
						<select name="structure1" class="text" style="width:49%">
							<option value=""></option>
							<option value="市场内">${message("市场内")}</option>
							<option value="沿街">${message("沿街")}</option>
						</select>
						<select name="structure2" class="text" style="width:49%">
						    <option value=""></option>
							<option value="单层" >${message("单层")}</option>
							<option value="复式" >${message("复式")}</option>
						</select>
					</td>
					<th>${message("是否二次设计")}:</th>
					<td>
					    <span class="shopAttributeText"></span>
						<input type="hidden" name="shopAttribute" id="shopAttribute" class="text" btn-fun="clear" readonly="readonly"></input>
					</td>
				</tr>
				<tr>
					<th>${message("机构")}:</th>
					<td>
					    <span class="saleOrgName"></span>
					</td>
					<th>${message("门店装修属性")}:</th>
					<td>
						<select name="shopRenovationAttribute" class="text" style="width:49%">
						    <option value=""></option>
							<option value="重装店" >${message("重装店")}</option>
							<option value="新店" >${message("新店")}</option>
						</select>
					</td>
					<th>${message("QQ/邮箱")}:</th>
					<td>
						<input type="text" class="text" name="qqOrEmail" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("上样计划")}:</th>
					<td colspan="7">
						<div class="statusList">
							<label><input class="check js-iname text" name="samplePlans" type="checkbox" value="木香居"/>${message("木香居")}</label>&nbsp;&nbsp;&nbsp;
							<label><input class="check js-iname text" name="samplePlans" type="checkbox" value="实木"/>${message("实木")}</label>&nbsp;&nbsp;&nbsp;
							<label><input class="check js-iname text" name="samplePlans" type="checkbox" value="多层" />${message("多层")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="三层" />${message("三层")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="进口三层" />${message("进口三层")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="1530" />${message("1530")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="康德1号" />${message("康德1号")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="强化" />${message("强化")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="戴昆" />${message("戴昆")}</label>&nbsp;&nbsp;&nbsp;
						</div>
					</td>
				</tr>
				<tr>
					<th>${message("上样占比")}:</th>
					<td colspan="3">
					    <span>实木</span>
	                    <input class="t acreage solidWoodRatio" name="solidWoodRatio" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
	                    <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    <span>多层</span>
	                    <input class="t acreage multilayerRatio" name="multilayerRatio" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
	                    <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    <span>强化</span>
	                    <input class="t acreage intensifyRatio" name="intensifyRatio" value="" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
	                    <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
			</table>
			
			<div class="title-style" style="border:1px solid #dcdcdc; padding:10px 15px; font:13px 'Microsoft YaHei';">
				<pre style="font:13px 'Microsoft YaHei';">
提示：
1、经销商在收到公司设计图纸后2个月内必须完成所有装修，竣工后1个月内将门店照片以PDF格式提交到地板事
    业部终端设计部初审，符合最新形象标准的即提供门店验收表单/相片等完整资料到渠道部报销报销；
    不符合最新形象标准的，由经销商根据终地板事业部终端设计部下达整改意见进行整改，再做提报返利报销。
2、需提供资料：
  （1）原始结构图、门店平面尺寸详图、天花（喷淋及空调管道）标高图（电子版）。
  （2）全方位、多角度门店现场相片5张以上（电子版）。
  （3）建材市场/物业装修管理相关规定（电子版）。
  （4）门店租赁合同/房产证（电子扫描版）
3、低于80平方的乡镇门店总部不提供设计。
4、由于经销商个人原因，要求重复设计的门店，将扣除报销总金额的10%处理。
★特别注明：经销商提交至终端设计部的门店面积需真实有效，若有随意拉大尺寸提高实际面积的行为。经现场审计核算若与事实不符，所有后果由经销商本人承担，并追究相关责任人责任。
				</pre>
				<div style="margin: 10px 0;">本人已认真阅读以上内容并严格执行！如未签名确认将不提供设计！</div>
				<div>
					<div style="margin:0 0 10px 0;"><input class="check js-iname" name="reimburse" id="reimburse1" type="radio" value="2" /><label for="reimburse1"> 提供设计、参与装修报销。</label></div>
					<div ><input class="check js-iname" name="reimburse" id="reimburse2" type="radio" value="1" /><label for="reimburse2"> 提供设计、不参与报销。</label></div>
				</div>
				<div style="color:rgb(227, 18, 25); margin:10px 0 0 0;">★提示：参与装修报销的经销商在收到设计图后半年内需要装修完成，并且提交装修验收申请，逾期总部不给报销</div>
		    </div>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
			    <div class="title-style">
					${message("上传附件：门店照片、租赁合同、平面图")}
				</div>
				<tr>
					<th>${message("门店照片")}:</th>
					<td>
						<a href="javascript:;" id="addDealersAttach" class="button">添加附件</a>
					</td>
					<th>${message("租赁合同")}:</th>
					<td>
						<a href="javascript:;" id="addStoreContractAttach" class="button">添加附件</a>
					</td>
					<th>${message("平面图")}:</th>
					<td>
						<a href="javascript:;" id="addStorePictureAttach" class="button">添加附件</a>
					</td>
				</tr>
			</table>
			<div>
	        	<span>${message("门店照片")}</span>
		        <table id="table-dealers-attach" style="width:850px"></table>
		        <span>${message("租赁合同")}</span>
		        <table id="table-store-contract-attach" style="width:850px"></table>
		        <span>${message("平面图")}</span>
				<table id="table-store-picture-attach" style="width:850px"></table>
			</div>
			[#--
			<div class="title-style">
				${message("区域经理上传附件（非必填）：门店照片、租赁合同、平面图")}
				<div class="btns">
					<a href="javascript:;" id="addRegionalManagerAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-regional-manager-attach"></table>
			
			<div class="title-style" >
				${message("省运营管理中心总经理意见：")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 10px 0;">
						<input class="check js-iname" name="regionalManagerOpinions" id="regionalManagerOpinion1" type="checkbox" value="0" />
						<label for="regionalManagerOpinion1">申请设计面积与事实相符</label>
					</div>
					<div style="margin:0 0 10px 0;">
						<input class="check js-iname" name="regionalManagerOpinions" id="regionalManagerOpinion2" type="checkbox" value="1" />
						<label for="regionalManagerOpinion2">同意按公司SI标准设计</label>
					</div>
					<div>
						<div style="margin:0 0 5px 0;">备注:</div>
						<textarea rows="3" name="regionalManagerMemo" style="width:85%; border:1px solid #dcdcdc;padding:5px;"></textarea>
					</div>
				</div>
			</div>
			<!-- 
			<div class="title-style">
				${message("渠道人员审核")}
			</div>
			 -->
			<div class="title-style" >
				${message("地板事业部渠道部审核：")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 10px 0;"><input class="check js-iname" name="floorDivisions" id="floorDivision1" type="checkbox" value="0" /><label for="floorDivision1">&nbsp;2年内未参与装修报销</label></div>
					<div style="margin:0 0 10px 0;"><input class="check js-iname" name="floorDivisions" id="floorDivision2" type="checkbox" value="1" /><label for="floorDivision2">&nbsp;签署2019年经销合同</label></div>
					<div style="margin:0 0 10px 0;"><input class="check js-iname" name="floorDivisions" id="floorDivision3" type="checkbox" value="2" /><label for="floorDivision3">&nbsp;缴纳品牌保证金</label></div>
					<div>
						<div style="margin:0 0 5px 0;">备注:</div>
						<textarea rows="3" name="floorDivisionMemo" style="width:85%; border:1px solid #dcdcdc; padding:5px;"></textarea>
					</div>
				</div>
			</div>
			
			<div class="title-style" >
				${message("设计部确认：")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div>
						<div style="margin:0 0 5px 0;">意见:</div>
						<textarea rows="3" name="designOpinion" style="width:85%; border:1px solid #dcdcdc; padding:5px;"></textarea>
					</div>
				</div>
			</div>
			<div class="title-style">
				${message("设计部上传附件：")}
				<div class="btns">
					<a href="javascript:;" id="designAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-design-attach"></table>
			
			<div class="title-style" >
				${message("经销商确认：")}
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div>
						<div>
							<label>设计图是否满意：</label>
							<select name="dealersSatisfied" class="text" style="width:120px;">
								<option value=""></option>
								<option value="1">${message("是")}</option>
								<option value="0">${message("否")}</option>
							</select>
						</div>
						<div style="margin:10px 0;">
							<label>装修开始日期：</label>
							<input type="text" style="width:190px;" name="decorateStartDate" class="text predictStartsTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
						</div>
						<div style="margin:0 0 5px 0;">意见:</div>
						<textarea rows="3" name="dealersOpinion" style="width:85%; border:1px solid #dcdcdc; padding:5px;"></textarea>
					</div>
					<div style="color:rgb(227, 18, 25); margin:10px 0 0 0;">★提示：需要装修报销的经销商，在收到设计图并确认后，半年内需要装修完成并提交装修验收申请，逾期无法报销。</div>
				</div>
			</div>
		</div> 
		--]
		<div class="fixed-top">
			<!-- <input type="submit" id="submit_button" class="button sureButton" value="${message("保存")}" /> -->
			<input type="button" id="submit_button" onclick="save(this)" class="button sureButton" value="${message("保存")}" />
			<input type="button" id="submit_button" class="button sureButton" value="${message("提交")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}" />
		</div>
	</form>
</body>
</html>