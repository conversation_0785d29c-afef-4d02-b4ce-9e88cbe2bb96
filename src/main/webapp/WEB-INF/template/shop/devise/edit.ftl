<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑门店设计")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<style type="">
.ge-btn {
	background-color: #4CAF50;
	border: none;
	color: white;
	padding: 8px 15px;
	text-align: center;
	text-decoration: none;
	display: inline-block;
	font-size: 13px;
	margin: 5px 5px;
	cursor: pointer;
	border-radius: 5px;
}
/* 调整附件 */
.nowrap > div >.t-edit {
    width: 80%;
    margin: 5px 0 -6px 0;
}
.bottonss{
    border: 1px solid #0000;
    height: 32px;
    width: 66px;
    background-color: #2190e8;
    border-radius: 5px;
    color: #fff;
    font-size: 14px;
    font-family: Microsoft YaHei;
    cursor:pointer;
    text-align:center;
}
.bottonss:hover{
	background-color:#5594c5;
}
.tj{
    position: absolute;
	right: 15px;
}
</style>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,2,false,e);
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	$("#deviseAreaId").lSelect();
	
	$(".area").find("select").each(function(){
		$(this).attr("disabled",true);
	});
	
	// 校验
	$inputForm.validate({
		rules: {
			shopInfoSn: "required",
			highLimit: "required",
			designBrand: "required",
			solidWoodRatio: "required",
			multilayerRatio: "required",
			intensifyRatio: "required",
			qqOrEmail: "required",
            predictStartsTime: "required",
            structure1: "required",
            structure2: "required",
            shopRenovationAttribute: "required",
		} ,
	});
	
	// 选择门店
	$("#selectShopInfo").click(function(){
		$("#selectShopInfo").bindQueryBtn({
			type:'shopInfo',
			title:'${message("查询门店")}',
			bindClick:false,
			url:'/shop/shopInfo/select_shopInfo.jhtml',
			callback:function(rows){
				if(rows.length > 0){
					var row = rows[0];
					$(".shopInfoSn").val(row.sn);
                    $(".shopInfoId").val(row.id);
                    $(".applyCityText").html(row.area_name);
                    $(".applyCity").val(row.area_name);
                    $(".dealerText").html(row.distributor_name);
                    $(".dealer").val(row.distributor_name);
                    $(".phoneText").html(row.distributor_phone);
                    $(".phone").val(row.distributor_phone);
                    $(".areaText").html(row.acreage);
                    $(".area").val(row.acreage);
                    $(".shopAddressText").html(row.address);
                    $('input[name="shopAddress"]').val(row.address)// 地址
                    $("#deviseAreaId").val(row.area_id);
                    $("#deviseAreaId").attr("treepath",row.area_tree_path);
                    $("#deviseAreaId").lSelect();
                    //切换机构
                    $(".saleOrgName").text(row.sale_org_name==null?"":row.sale_org_name);
                    //锁定地址输入框
                    $(".area").find("select").each(function(){
						$(this).attr("disabled",true);
					});
                    // 是否二次设计
                    $.ajax({
                        type:'POST',
                        url:'/shop/devise/existShopInfo.jhtml',
                        data:{shopInfoId:row.id},
                        success:function(data) {
                            if (data == 1) {
                                $("#shopAttribute").val("是");
                                $(".shopAttributeText").html("是");
                            } else if (data == 0) {
                                $("#shopAttribute").val("否");
                                $(".shopAttributeText").html("否");
                            }
                        }
                    })
				}
			}
		});
	});
	
	// 初始化报销或不报销按钮
	if ($('#reimburse1').is(":checked")) {
		// 显示报销按钮
        $('#ys-bx').show();
        $('#ys-nbx').hide();
	} else if ($('#reimburse2').is(":checked")) {
		// 显示不报销按钮
        $('#ys-nbx').show();
        $('#ys-bx').hide();
	} else {
		// 两个都不显示
        $('#ys-nbx').hide();
        $('#ys-bx').hide();
	}

	// 初始化附件
    initAttach("dealersAttachs", "addDealersAttach", "table-dealers-attach", 0, ${dealers_attach});
	initAttach("storeContractAttachs", "addStoreContractAttach", "table-store-contract-attach", 3, ${store_contract_attachs});
	initAttach("storePicturesAttachs", "addStorePictureAttach", "table-store-picture-attach", 4, ${store_pictures_attachs});
    initAttach("managerDealersAttachs", "addManagerDealersAttach", "table-manager-dealers-attach", 1, ${manager_dealers_attach});
	initAttach("managerStoreContractAttachs", "addManagerStoreContractAttach", "table-manager-store-contract-attach", 5, ${manager_store_contract_attachs});
	initAttach("managerStorePicturesAttachs", "addManagerStorePictureAttach", "table-manager-store-picture-attach", 6, ${manager_store_pictures_attachs});
    initAttach("designAttachs", "designAttach", "table-design-attach", 2, ${design_attach});
    
    [#if shopDevise.wfId!=null]
	 	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${shopDevise.wfId}");
	[/#if]
    
});

/**
 * 初始化附件，每个参数都是必填的
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param addAttachIdName 前端页面添加附件按钮的id值
 * @param tableIdName 前端页面table中的id值 
 * @param type 后台用来区分不同类型附件
 * @param attachsData 附件数据
 */
function initAttach(paramAttachs, addAttachIdName, tableIdName, type, attachsData) {
    var attachs_data = attachsData;
    var index = 0;
    var attachCols = [              
        { title:'${message("附件")}',name:'content',width:260,align:'center',renderer:function(val,item,rowIndex,obj){
            if (obj==undefined) {
                var url = item.url;
                var fileObj = getfileObj(item.file_name,item.name,item.suffix);
                /**设置隐藏值*/
                var hideValues = {};
                hideValues[paramAttachs+'['+index+'].id']=item.id;
                hideValues[paramAttachs+'['+index+'].url']=url;
                hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
                hideValues[paramAttachs+'['+index+'].type']=item.type;
                return createFileStr({
                    url : url,
                    fileName : fileObj.file_name,
                    name : fileObj.name,
                    suffix : fileObj.suffix,
                    time : item.create_date,
                    textName: paramAttachs+'['+index+'].name',
                    hideValues:hideValues
                });
                
            } else {
                var url = item.url;
                var fileObj = getfileObj(item.name);
                /**设置隐藏值*/
                var hideValues = {};
                hideValues[paramAttachs+'['+index+'].url']=url;
                hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
                hideValues[paramAttachs+'['+index+'].type']=type; 
                return createFileStr({
                    url : url,
                    fileName : fileObj.file_name,
                    name : fileObj.name,
                    suffix : fileObj.suffix,
                    time : '',
                    textName: paramAttachs+'['+index+'].name',
                    hideValues:hideValues
                });
            }
        }},
        { title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
            return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" >'+val+'</textarea></div>';
        }},
        { title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
            index++;
            return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
        }}
    ];
    var $grid=$('#'+tableIdName).mmGrid({
        fullWidthRows:true,
        height:'auto',
        cols: attachCols,
        items: attachs_data,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#"+addAttachIdName);
    var attachIdnex = 0;
    var option = {
        dataType: "json",
        uploadToFileServer:true,
        uploadSize: "fileurl",
        callback : function(data){
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth()+1;
            var day = date.getDate();
            var time = year+'-'+month+'-'+day;
            for(var i=0;i<data.length;i++){
                var row = data[i].file_info;
                $grid.addRow(row,null,1);
            }
        }
    }
    $addAttach.file_upload(option);
    
    /* 删除附件 */
    var $deleteAttachment = $(".deleteAttachment");
    $deleteAttachment.live("click", function() {
        var $this = $(this);
        $this.closest("tr").remove();
    });
}

// 保存更新
function update(e){
	var str = '您确定要保存吗？';
	var url = 'update.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= 'edit.jhtml?id='+resultMsg.objx;
				})
			}
		});
	}
}

// 生成报销或不报销表单，index = 0：不报销，1：报销
function generate(e, index) {
	var id = $("#did").val();
	var str = '';
	var url = '/shop/devise/generate.jhtml';
	if (index == 0) {
		str = "您确定要生成装修验收表单吗？"
	} else if (index == 1) {
		str = "您确定要生成装修验收及报销表单吗？"
	}
	ajaxSubmit(e,{
		url: url,
		data: {
			"id": id,
			"sign": index
		},
		method: "post",
		isConfirm: true,
		confirmText: str,
		callback: function(resultMsg) {
			$.message_alert(resultMsg.content, 475, 142);
		}
	});
}

// 处理显示报销或不报销表单（暂时不需要，改变报销或不报销状态时，需要保存后才进行修改）
function showBtn(e, index) {
	if (index == 1) {
		// 显示不报销按钮
		$('#ys-nbx').show();
		$('#ys-bx').hide();
	} else if (index == 2) {
		// 显示报销按钮
        $('#ys-bx').show();
		$('#ys-nbx').hide();
	} else {
		// 两个都不显示
		$('#ys-nbx').hide();
        $('#ys-bx').hide();
	}
}


function check_wf(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			$.message_confirm("您确定要审批流程吗？",function(){
				var objTypeId = 10004;
				var modelId = 5;//大自然测试
				//var modelId = 67501;//大自然开发 
				var url="check_wf.jhtml?id=${shopDevise.id}&modelId="+modelId+"&objTypeId="+objTypeId;
				var data = $form.serialize();
				ajaxSubmit(e,{
					method:'post',
					url:url,
					async: true,
					callback: function(resultMsg) {
						$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
							location.reload(true);
						})
					}
				});
			});
		}
	}
function saveform(e){
	$.message_confirm("您确定要提交吗？",function(){
		//获取表单所有数据
		var params = $("#inputForm").serializeArray();
		//定义url
		var url = 'saveform.jhtml?type='+e;
		ajaxSubmit(e,{
			method:'post',
			url:url,
			data:params,
			async: true,
			callback: function(resultMsg) {
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("门店设计")}
	</div>
	<form id="inputForm" action="javascript:void(0);" method="post" type="ajax" validate-type="validate">
		<div class="tabContent" style="padding-bottom: 10px;">
			<table class="input input-edit">
				<tr>
					<th>${message("单号")}:</th>
					<td>
						<span>${shopDevise.sn}</span>
						<input type="hidden" class="text" name="id" id="did" value="${shopDevise.id}" maxlength="200" btn-fun="clear" />
						<input type="hidden" class="text" name="sn" value="${shopDevise.sn}" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("单据状态")}:</th>
					<td>
						[#if shopDevise.billsStatus == 0]
						<span class="blue">已保存</span>
						[#elseif shopDevise.billsStatus == 3]
						<span style="color:orange;">进行中</span>
						[#elseif shopDevise.billsStatus == 1]
						<span class="green">已生效</span>
						[#elseif shopDevise.billsStatus == 2]
						<span class="red">已终止</span>
						[/#if]
						<input type="hidden" class="text" name="billsStatus" value="${shopDevise.billsStatus}" maxlength="200" btn-fun="clear" />
					</td>
					<th>${message("门店编码")}:</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="shopInfoId" class="text shopInfoId" value="${shopDevise.shopInfo.id}" btn-fun="clear"/>
							<input type="text" name="shopInfoSn" class="text shopInfoSn" value="${shopDevise.shopInfo.sn}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
							<input type="button" class="iconSearch" value="" id="selectShopInfo"/>
						</span>
					</td>
					<th>${message("申请城市")}:</th>
					<td>
					    <span class="applyCityText">${shopDevise.applyCity}</span>
						<input type="hidden" class="text applyCity" name="applyCity" value="${shopDevise.applyCity}" maxlength="200" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("门店名称")}:</th>
					<td>
						<input type="text" class="test shopInfoShopName" name="shopInfoShopName" value="${shopDevise.shopInfo.shopName}" btn-fun="clear" />
					</td>
					<th>${message("经销商")}:</th>
					<td>
					    <span class="dealerText">${shopDevise.dealer}</span>
						<input type="hidden" class="text dealer" name="dealer" value="${shopDevise.dealer}" btn-fun="clear" />
					</td>
					<th>${message("联系方式")}:</th>
					<td>
					    <span class="phoneText">${shopDevise.phone}</span>
						<input type="hidden" class="text phone" name="phone" value="${shopDevise.phone}" btn-fun="clear" />
					</td>
					<th>${message("天花限高")}:</th>
					<td>
						<div class="nums-input ov">
							<input class="b decrease" value="-" onmousedown="decrease(this,event)" type="button" />
							<input class="t acreage" name="highLimit" value="${shopDevise.highLimit}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text"  />
							<input value="+" class="b increase" onmousedown="increase(this,event)" type="button" />
						</div>
					</td>
				</tr>
				<tr>
					<th>${message("面积（㎡）")}:</th>
					<td>
					    <span class="areaText" style="text-align: center;display:block;">${shopDevise.area}</span>
                        <input type="hidden" class="text area" name="area" value="${shopDevise.area}" btn-fun="clear" />
					</td>
					<th>${message("设计品牌")}:</th>
					<td>
						<select name="designBrand" class="text">
                            <option value=""></option>
                            <option value="0" [#if shopDevise.designBrand == "0"]selected[/#if]>${message("大自然综合店")}</option>
                            <option value="1" [#if shopDevise.designBrand == "1"]selected[/#if]>${message("大自然·三层专卖店")}</option>
                            <option value="2" [#if shopDevise.designBrand == "2"]selected[/#if]>${message("大自然·实木专卖店")}</option>
                            <option value="3" [#if shopDevise.designBrand == "3"]selected[/#if]>${message("大自然·强化专卖店")}</option>
                        </select>
					</td>
					<th>${message("门店地址")}:</th>
					<td colspan="3" class="area">
					    <input type="hidden" id="deviseAreaId" name="deviseArea.id" value="${shopDevise.deviseArea.id}" treePath="${(shopDevise.deviseArea.treePath)!}" />
					    <input type="text" name="shopAddress" value="${shopDevise.shopAddress}" class="detailed-address" maxlength="200" readonly/>
					</td>
				</tr>
				<tr>
					<th>${message("预订施工时间")}</th>
					<td>
						<input type="text" name="predictConstructionTime" value="${shopDevise.predictConstructionTime}" class="text predictConstructionTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
					</td>
					<th>${message("预订开业时间")}</th>
					<td>
						<input type="text" name="predictStartsTime" value="${shopDevise.predictStartsTime}" class="text predictStartsTime" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
					</td>
					<th>${message("门店结构情况")}:</th>
					<td>
						<select name="structure1" class="text" style="width:49%">
						    <option value=""></option>
							<option value="市场内" [#if shopDevise.structure1 == "市场内"]selected[/#if]>${message("市场内")}</option>
							<option value="沿街" [#if shopDevise.structure1 == "沿街"]selected[/#if]>${message("沿街")}</option>
						</select>
						<select name="structure2" class="text" style="width:49%">
						    <option value=""></option>
							<option value="单层" [#if shopDevise.structure2 == "单层"]selected[/#if]>${message("单层")}</option>
							<option value="复式" [#if shopDevise.structure2 == "复式"]selected[/#if]>${message("复式")}</option>
						</select>
					</td>
					<th>${message("是否二次设计")}:</th>
					<td>
					    <span class="shopAttributeText">${shopDevise.shopAttribute}</span>
						<input type="hidden" name="shopAttribute" value="${shopDevise.shopAttribute}" id="shopAttribute" class="text" btn-fun="clear" readonly="readonly"></input>
					</td>
				</tr>
				<tr>
					<th>${message("机构")}:</th>
					<td>
					    <span class="saleOrgName">${shopDevise.saleOrg.name}</span>
					</td>
					<th>${message("门店装修属性")}:</th>
					<td>
						<select name="shopRenovationAttribute" class="text" style="width:49%">
						    <option value=""></option>
							<option value="重装店" [#if shopDevise.shopRenovationAttribute == "重装店"]selected[/#if]>${message("重装店")}</option>
							<option value="新店" [#if shopDevise.shopRenovationAttribute == "新店"]selected[/#if]>${message("新店")}</option>
						</select>
					</td>
					<th>${message("QQ/邮箱")}:</th>
					<td>
						<input type="text" class="text" name="qqOrEmail" value="${shopDevise.qqOrEmail}" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th>${message("上样计划")}:</th>
					<td colspan="7">
						<div class="statusList"> 
							<label><input class="check js-iname text" name="samplePlans" type="checkbox" value="木香居" [#if sp?seq_contains("木香居") == true]checked[/#if] />${message("木香居")}</label>&nbsp;&nbsp;&nbsp;
							<label><input class="check js-iname text" name="samplePlans" type="checkbox" value="实木" [#if sp?seq_contains("实木") == true]checked[/#if] />${message("实木")}</label>&nbsp;&nbsp;&nbsp;
							<label><input class="check js-iname text" name="samplePlans" type="checkbox" value="多层" [#if sp?seq_contains("多层") == true]checked[/#if] />${message("多层")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="三层" [#if sp?seq_contains("三层") == true]checked[/#if] />${message("三层")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="进口三层" [#if sp?seq_contains("进口三层") == true]checked[/#if] />${message("进口三层")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="1530" [#if sp?seq_contains("1530") == true]checked[/#if] />${message("1530")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="康德1号" [#if sp?seq_contains("康德1号") == true]checked[/#if] />${message("康德1号")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="强化" [#if sp?seq_contains("强化") == true]checked[/#if] />${message("强化")}</label>&nbsp;&nbsp;&nbsp;
					        <label><input class="check js-iname text" name="samplePlans" type="checkbox" value="戴昆" [#if sp?seq_contains("戴昆") == true]checked[/#if] />${message("戴昆")}</label>&nbsp;&nbsp;&nbsp;
						</div>
					</td>
				</tr>
				<tr>
					<th>${message("上样占比")}:</th>
					<td colspan="3">
						<span>实木</span>
						<input class="t acreage" name="solidWoodRatio" value="${shopDevise.solidWoodRatio}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span>多层</span>
						<input class="t acreage" name="multilayerRatio" value="${shopDevise.multilayerRatio}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span>强化</span>
						<input class="t acreage" name="intensifyRatio" value="${shopDevise.intensifyRatio}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;" />
						<span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
			</table>
			
			<div class="title-style" style="border:1px solid #dcdcdc; padding:10px 15px; font:13px 'Microsoft YaHei';">
				<pre style="font:13px 'Microsoft YaHei';">
提示：
1、经销商在收到公司设计图纸后2个月内必须完成所有装修，竣工后1个月内将门店照片以PDF格式提交到地板事
    业部终端设计部初审，符合最新形象标准的即提供门店验收表单/相片等完整资料到渠道部报销报销；
    不符合最新形象标准的，由经销商根据终地板事业部终端设计部下达整改意见进行整改，再做提报返利报销。
2、需提供资料：
  （1）原始结构图、门店平面尺寸详图、天花（喷淋及空调管道）标高图（电子版）。
  （2）全方位、多角度门店现场相片5张以上（电子版）。
  （3）建材市场/物业装修管理相关规定（电子版）。
  （4）门店租赁合同/房产证（电子扫描版）
3、低于80平方的乡镇门店总部不提供设计。
4、由于经销商个人原因，要求重复设计的门店，将扣除报销总金额的10%处理。
★特别注明：经销商提交至终端设计部的门店面积需真实有效，若有随意拉大尺寸提高实际面积的行为。经现场审计核算若与事实不符，所有后果由经销商本人承担，并追究相关责任人责任。
				</pre>
				<div style="margin: 10px 0;">本人已认真阅读以上内容并严格执行！如未签名确认将不提供设计！</div>
				<div>
					<div style="margin:0 0 10px 0;"><input class="check js-iname" name="reimburse" id="reimburse1" type="radio" value="2"  [#if shopDevise.reimburse == 2]checked[/#if] /><label for="reimburse1"> 提供设计、参与装修报销。</label></div>
					<div ><input class="check js-iname" name="reimburse" id="reimburse2" type="radio" value="1" [#if shopDevise.reimburse == 1]checked[/#if] /><label for="reimburse2"> 提供设计、不参与报销。</label></div>
				</div>
				<div style="color:rgb(227, 18, 25); margin:10px 0 0 0;">★提示：参与装修报销的经销商在收到设计图后半年内需要装修完成，并且提交装修验收申请，逾期总部不给报销</div>
		    </div>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
		    <div class="title-style">
				${message("上传附件：门店照片、租赁合同、平面图")}
			</div>
			<tr>
				<th>${message("门店照片")}:</th>
				<td>
				[#if shopDevise.wfId == null]
					<a href="javascript:;" id="addDealersAttach" class="button">添加附件</a>
				[/#if]
				</td>
				<th>${message("租赁合同")}:</th>
				<td>
				[#if shopDevise.wfId == null]
					<a href="javascript:;" id="addStoreContractAttach" class="button">添加附件</a>
				[/#if]
				</td>
				<th>${message("平面图")}:</th>
				<td>
				[#if shopDevise.wfId == null]
					<a href="javascript:;" id="addStorePictureAttach" class="button">添加附件</a>
				[/#if]
				</td>
			</tr>
		</table>
		<div>
	    	<span>${message("门店照片")}</span>
	        <table id="table-dealers-attach" style="width:850px"></table>
	        <span>${message("租赁合同")}</span>
	        <table id="table-store-contract-attach" style="width:850px"></table>
	        <span>${message("平面图")}</span>
			<table id="table-store-picture-attach" style="width:850px"></table>
		</div>
			
			[#if shopDevise.wfId != null]
			[#if qyjl||qyjls]
			<table class="input input-edit" style="width:100%;margin-top:5px;">
			    <div class="title-style">
					${message("区域经理上传附件（非必填）：门店照片、租赁合同、平面图")}[#if node.name?contains("区域经理")]<input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/>[/#if]
				</div>
				<tr>
					<th>${message("门店照片")}:</th>
					<td>
					[#if node.name?contains("区域经理")]
						<a href="javascript:;" id="addManagerDealersAttach" class="button">添加附件</a>
					[/#if]
					</td>
					<th>${message("租赁合同")}:</th>
					<td>
					[#if node.name?contains("区域经理")]
						<a href="javascript:;" id="addManagerStoreContractAttach" class="button">添加附件</a>
					[/#if]
					</td>
					<th>${message("平面图")}:</th>
					<td>
					[#if node.name?contains("区域经理")]
						<a href="javascript:;" id="addManagerStorePictureAttach" class="button">添加附件</a>
					[/#if]
					</td>
				</tr>
			</table>
			<div>
		    	<span>${message("门店照片")}</span>
		        <table id="table-manager-dealers-attach" style="width:850px"></table>
		        <span>${message("租赁合同")}</span>
		        <table id="table-manager-store-contract-attach" style="width:850px"></table>
		        <span>${message("平面图")}</span>
				<table id="table-manager-store-picture-attach" style="width:850px"></table>
			</div>
			[/#if]
			[#if szsh||szshs]
			<div class="title-style" >
				${message("省运营管理中心总经理意见")}:[#if node.name?contains("省长")]<input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/>[/#if]
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 10px 0;">
					    <label><input class="check js-iname" name="regionalManagerOpinions" type="checkbox" value="0" [#if rmo?seq_contains("0") == true]checked[/#if] /> 申请设计面积与事实相符</label>
					</div>
					<div style="margin:0 0 10px 0;">
						<label><input class="check js-iname" name="regionalManagerOpinions" type="checkbox" value="1" [#if rmo?seq_contains("1") == true]checked[/#if] /> 同意按公司SI标准设计</label>
					</div>
					<div>
						<div style="margin:0 0 5px 0;">备注:</div>
						<textarea rows="3" name="regionalManagerMemo" style="width:85%; border:1px solid #dcdcdc;padding:5px;">${shopDevise.regionalManagerMemo}</textarea>
					</div>
				</div>
			</div>
			[/#if]
			[#if qdzy||qdzys]
			<div class="title-style" >
				${message("地板事业部渠道部审核")}:[#if node.name?contains("渠道")]<input type="button" class="bottonss tj" onclick="saveform(3)" value="提交"/>[/#if]
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div style="margin:0 0 10px 0;">
						<label><input class="check js-iname" name="floorDivisions" type="checkbox" value="0" [#if fd?seq_contains("0") == true]checked[/#if] />2年内未参与装修报销</label>
					</div>
					<div style="margin:0 0 10px 0;">
						<label><input class="check js-iname" name="floorDivisions" type="checkbox" value="1" [#if fd?seq_contains("1") == true]checked[/#if] />签署2019年经销合同</label>
					</div>
					<div style="margin:0 0 10px 0;">
						<label><input class="check js-iname" name="floorDivisions" type="checkbox" value="2" [#if fd?seq_contains("2") == true]checked[/#if] />缴纳品牌保证金</label>
					</div>
					<div>
						<div style="margin:0 0 5px 0;">备注:</div>
						<textarea rows="3" name="floorDivisionMemo" style="width:85%; border:1px solid #dcdcdc; padding:5px;">${shopDevise.floorDivisionMemo}</textarea>
					</div>
				</div>
			</div>
			[/#if]
			[#if mdsjs||mdsjss]
			<div class="title-style" >
				${message("设计部确认：")}[#if node.name?contains("设计部")]<input type="button" class="bottonss tj" onclick="saveform(4)" value="提交"/>[/#if]
				<div style="border:1px solid #dcdcdc; margin-top:8px; padding:10px 15px; font:13px 'Microsoft YaHei';">
					<div>
						<div style="margin:0 0 5px 0;">意见:</div>
						<textarea rows="3" name="designOpinion" style="width:85%; border:1px solid #dcdcdc; padding:5px;">${shopDevise.designOpinion}</textarea>
					</div>
				</div>
			</div>
			<div class="title-style">
				${message("设计部上传附件：")}
				<div class="btns">
					<a href="javascript:;" id="designAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-design-attach"></table>
			[/#if]
			[/#if]
		</div>
		<div class="fixed-top">
		[#if shopDevise.wfId == null]
			<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			<input type="button" id="submit_button" onclick="update(this)" class="button sureButton" value="${message("保存")}" />
		[/#if]
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div style="margin: 0px 0 30px 100px;">
		<button onclick="generate(this, 1)" class="ge-btn" id="ys-bx">生成装修验收及报销表单</button>
		<button onclick="generate(this, 0)" class="ge-btn" id="ys-nbx">生成装修验收表单</button>
	</div>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>