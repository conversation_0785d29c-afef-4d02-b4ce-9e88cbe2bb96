<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("出库申请")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script>
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});
$().ready(function() {
	
	// 表单验证
    $('#inputForm').validate({
        rules: {
        	outDate: {required: true},
        },
    });
	
    // 初始化入库申请明细
    initIntoStorage();

});

function editQty(t,e) {
    extractNumber(t,6,false,e);
}

function isNull(str) {
    var a = (str == null || str == "undefined") ? "" : str;
    return a;
}

// 选择产品
function addIntoStorage(e) {
	$("#pop-pro").bindQueryBtn({
		type:'product',
		title:'${message("查询库存商品")}',
		bindClick:false,
		url:'/shop/storage_query/select_storage.jhtml',
		callback:function(rows){
			 if(rows.length > 0) {
             	for (var i = 0; i < rows.length;i++) {
			        var row = rows[i];
			        $mmGrid.addRow(row,null,1);
			        isIndex++;
			    }
			}
		}
	});
}

// 初始化入库单明细
var isIndex = 0;
function initIntoStorage() {
    var cols = [   
        { title:'${message("id")}', hidden:true, name:'id', align:'center',width:20,renderer: function(val,item,rowIndex){
            return '<input type="hidden" name="intoOtOutPro['+isIndex+'].product.id" value="'+ val +'" class="text" btn-fun="clear" />'
        }},
        { title:'${message("产品名称")}', name:'name', align:'center',width:80},
        { title:'${message("产品编码")}', name:'vonder_code', align:'center',width:90},
        { title:'${message("产品描述")}', name:'description', align:'center',width:120},
        { title:'${message("产品分类")}', name:'product_category_name', align:'center',width:80},
        { title:'${message("单位")}', name:'unit', align:'center',width:40},
        { title:'${message("出库数量")}', name:'', align:'center',width:70,renderer: function(val,item,rowIndex){
            var html = '<div class="lh20">'+
                    '<div class="nums-input ov">'+
                    '<input type="button" class="b decrease" value="-" onMouseDown="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
                    '<input type="text" kid="box" class="t"  name="intoOtOutPro['+isIndex+'].number" value="0" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
                    '<input type="button" value="+" class="b increase" onMouseDown="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
                    '</div></div>';
            return html;
        }},
        { title:'${message("备注")}', name:'', align:'center',width:100,renderer: function(val,item,rowIndex){
            return '<input type="text" name="intoOtOutPro['+isIndex+'].memo" class="text" btn-fun="clear" />'
        }},
        { title:'${message("操作")}', name:'', width:40, align:'center',renderer: function(val,item,rowIndex){
            return '<a href="javascript:;" class="btn-delete" onclick="deleteAddress(this)">删除</a>'
        }},
    ];
    $mmGrid = $('#table-m1').mmGrid({
        height:'auto',
        cols: cols,
        checkCol:false,
        fullWidthRows: true
    });
}

function deleteAddress(e){
    var index = $(e).closest("tr").index();
    $.message_confirm('您确定要删除吗？',function(){
        $mmGrid.removeRow(index);
    })
}

function save(e){
    var str = '您确定要保存吗？';
    var url = 'save.jhtml';
    var $form = $("#inputForm");
    
    if (isNull($('#storageId').val()) == '') {
        alert("请选择仓库！");
        return ;
    }
    
    if($form.valid()){
        ajaxSubmit(e,{
            url: url,
            data:$("#inputForm").serialize(),
            method: "post",
            isConfirm:true,
            confirmText:str,
            callback:function(resultMsg){
                $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                    location.href= 'edit.jhtml?id='+resultMsg.objx;
                })
            }
        });
    }
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("出库申请")}
	</div>
	<form id="inputForm" action="#" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="type" value="0"/>
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>${message("出库单号")}:</th>
					<td></td>
					<th>${message("单据状态")}:</th>
					<td></td>
					<th>${message("出库时间")}:</th>
					<td>
					    <input type="text" name="outDate" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" readOnly/>
					</td>
					<th>${message("仓库名称")}:</th>
					<td>
					    <select name="storage.id" id="storageId" class="text">
                            <option value=""></option>
                            [#list oStorage as item]
                            <option value="${item.id}">${item.name}</option>
                            [/#list]
                        </select>
					</td>
				</tr>
				<tr>
					<th>${message("备注")}:</th>
					<td colspan="7">
                        <textarea name="memo" class="text"></textarea>
                    </td>
				</tr>
				<tr><!-- 些tr不需要内容，放在当前 table 的最后一个，用于与下面一个table隔离一个空行 --></tr>
			</table>
			
			<table class="input input-edit" style="width:100%;margin-top:5px;">
                <tr class="barnk border-L1">
                    <th>${message("出库单明细")}</th>
                    <td colspan="7">
                        <a href="javascript:;" class="button" id="pop-pro" onclick="addIntoStorage(this)">${message("添加明细")}</a>
                    </td>
                </tr>
                <tr class="border-L1">
                    <td colspan="8">
                        <div>
                            <table id="table-m1"></table>
                        </div>
                    </td>
                </tr>
            </table>
		</div>
		
		<div class="fixed-top">
			<input type="button" id="submit_button" onclick="save(this)" class="button sureButton" value="${message("保存")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}"/>
		</div>
	</form>
</body>
</html>