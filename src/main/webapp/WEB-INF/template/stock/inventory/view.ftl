<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增库存盘点单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />

<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<style>
	#tab{border="1px solid #d0cdcd";}
	#tab .ssdtop{width: 80px;height: 35px;border: 1px solid #d0cdcd;background-color:#e6e6e6;}
	#tab .content{width: 80px;height: 35px;}
	#tab .conne{text-align:center;}
	#tab .connetop{font-weight:500;}	
</style>
<script type="text/javascript">

function productOrganization(){
	var typesystemDictFlag = $("input.typesystemDictFlag").val();
	if(isNull(typesystemDictFlag) != null){
		$("input.productId").each(function(){
			var $this = $(this);
			var productId = $this.val();
			var $tr = $this.closest("tr");
			if(typesystemDictFlag == 0){
				$tr.find(".productOrganization").empty();
				$tr.find(".productOrganization").append("<option value="+$("input.organizationId").val()+">"+$(".organizationName").text()+"</option>");
				[#if linkStock == 1 ] 
					loadAttQuantity($tr);
				[/#if]
			}else if (typesystemDictFlag == 1){
				if(isNull(productId) != null){
					$.ajax({
						url : '/product/product/findProductOrganizationList.jhtml',
		    			type : "post",
		    			data : {id:productId},
		    			success : function(data) {
		    				var content = JSON.parse(data.content);
		    				if(content.length!=0){
		    					var html = "";
		    					var organizationId = null;
		    					var productOrganizationId = $tr.find(".productOrganization").val();
		    					var isTrue = true;
		    					for(var i=0; i < content.length; i++){
		    						var isDefaults = content[i].is_defaults;
		    						html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';    
	    							if(productOrganizationId==content[i].organization){
	    								isTrue = false;
	    								organizationId = content[i].organization;
	    							}else{
		        						if(i==0){
		    								organizationId = content[i].organization;
		    							}else if(isDefaults==1 && isTrue){
		    								organizationId = content[i].organization;
		    							}
	    							}
		    					}
		    					$tr.find(".productOrganization").empty();
		    					$tr.find(".productOrganization").html(html);
		    					$tr.find(".productOrganization option[value='"+organizationId+"']").attr("selected",true); 
		    					[#if linkStock == 1 ] 
		    						loadAttQuantity($tr);
		    					[/#if]	
		    				}
		    			}
					})
				}
			}
		});
	}
}

//加载库存
function loadAttQuantity($tr){
	[#if storageStockQueryRoles ==0] 
		jurisdictionStockQueryRoles($tr);
		return;
	[/#if]
	//产品经营组织
	var productOrganizationId = $tr.find(".productOrganization").val();
	if(isNull(productOrganizationId) != null){
		//仓库
		var warehouseId = $("input.warehouseId").val();
		//产品
		var productId = $tr.find("input.productId").val();
		//等级
		var productGrade = $tr.find(".productGrade").val();
		//色号
		var colourNumber = $tr.find(".colourNumber").val();
		if(isNull(colourNumber) == null){
			colourNumber = "";
		}
		//含水率
		var moistureContent = $tr.find(".moistureContent").val();
		if(isNull(moistureContent) == null){
			moistureContent = "";
		}
		//批次
		var batchIds = $tr.find("input.batchIds").val();
		if(isNull(batchIds) == null){
			batchIds = "";
		}else{
			batchIds = batchIds.split(';');
		}
		//新旧标识
		var newOldLogo = $tr.find(".newOldLogo").val();
		if(isNull(newOldLogo) == null){
			newOldLogo = "";
		}
		//库位
		var warehouseLocationId = $tr.find(".warehouseLocationId").val();
		if(isNull(warehouseLocationId) == null){
			warehouseLocationId = "";
		}
		var params='&warehouseId='+warehouseId+'&productId='+productId+'&productGrade='+productGrade
		  		+'&organizationId='+productOrganizationId+'&colourNumber='+colourNumber+'&moistureContent='+moistureContent
		  		+'&batchIds='+batchIds+'&newOldLogos='+newOldLogo+"&warehouseLocationId="+warehouseLocationId;
			params = params.substring(1,params.length);
		$.ajax({
			url:'/stock/stock/findViewStock.jhtml?'+params,
   			type : "post",
   			success : function(rows) {
   				var data= $.parseJSON(rows.content);
                   if(data.length>0){
                       for (var i = 0; i < data.length;i++) {
	                        //可用库存箱数
	                       	if(isNull(data[i].totalAttQuantity2) != null) {
	                       		$tr.find(".attQuantity2BoxNum").text(data[i].totalAttQuantity2);		                   
	                       	}else {
	                       		$tr.find(".attQuantity2BoxNum").text(0);		                       		
	                       	}		                     
	                        //可用库存支数
	                       	if(isNull(data[i].totalAttQuantity3) != null) {
	                       		$tr.find(".attQuantity3Num").text(data[i].totalAttQuantity3);
	                       		$tr.find(".regBranchQuantity").val(data[i].totalAttQuantity3);
	                       	}else {
	                       		$tr.find(".attQuantity3Num").text(0);
								$tr.find(".regBranchQuantity").val(0);
	                       	}
	                        //可用库存数量
	                       	if(isNull(data[i].totalAttQuantity1) != null) {
	                       		$tr.find(".attQuantity1Num").text(data[i].totalAttQuantity1);
								$tr.find(".regQuantity").val(data[i].totalAttQuantity1);
	                       	}else {
	                       		$tr.find(".attQuantity1Num").text(0);
								$tr.find(".regQuantity").val(0);
	                       	}
						   //盘点支数
						   var branchQuantity = $tr.find("input.branchQuantity").val();
						   //盘点数量
						   var quantity = $tr.find("input.quantity").val();
	                       	//计算差异支数和数量
						   var regBranchQuantity = $tr.find(".regBranchQuantity").val();
						   var diffBranchQuantity = accSub(branchQuantity,regBranchQuantity);
						   var regQuantity = $tr.find(".regQuantity").val();
						   var diffQuantity = accSub(quantity,regQuantity);
						   $tr.find(".differenceBranchQuantity").val(diffBranchQuantity);
						   $tr.find(".differenceBranchQuantityText").html(diffBranchQuantity);
						   $tr.find(".differenceQuantity").val(diffQuantity);
						   $tr.find(".differenceQuantityText").html(diffQuantity);
                   	}
               	}else{
               		$tr.find(".attQuantity2BoxNum").text(0);	
               		$tr.find(".attQuantity3Num").text(0);
               		$tr.find(".regBranchQuantity").val(0);
               		$tr.find(".attQuantity1Num").text(0);
					$tr.find(".regQuantity").val(0);
               	}
                //获取单据类型判断是否出仓 出仓则提示颜色
                var billTypeId = $("input.billTypeId").val();			              
	            if(billTypeId == 509 || billTypeId == 504) {
                   //获取件数值
                   var boxQuantity = $tr.find("input.boxQuantity").val();
                   //支数
                   var branchQuantity = $tr.find("input.branchQuantity").val();
                   //数量
                   var quantity = $tr.find("input.quantity").val();		                   
                   if(boxQuantity > $tr.find(".attQuantity2BoxNum").text()) {
                   		$tr.addClass("backColor");
                   }else {
                	   $tr.removeClass("backColor");
                   }
                }
   			}
		})
	}
} 

//控制颜色
function addBackColor(t) {
	//控制颜色
	var fileDir = $(t).closest("span").text();			
	if($(t).val()>fileDir) {
		$(t).parents("tr").addClass("backColor");
	}else if($(t).val()<=fileDir) {
		$(t).parents("tr").removeClass("backColor");
	}
}

function countTotal(t){
	var $bInput = $("input.boxQuantity");
	$bInput.each(function(){
        var $tr = $(this).closest("tr");
        var isEqual = null;
        if(t!=undefined){
            isEqual = (t.find(".line_no").text() == $tr.find(".line_no").text());
        }
        var boxQuantity=$(this).val();
        var branchPerBox = $tr.find(".branchPerBox").val();
        var perBranch = $tr.find(".perBranch").val();
        var perBox = $tr.find(".perBox").val();
        var branchQuantity = $tr.find(".branchQuantity").val();
        var scatteredQuantity = $tr.find(".scatteredQuantity").val();//零散支数

		//原数量
		if($tr.find(".regQuantity").val()){
        	var attQuantity1Num = $tr.find(".regQuantity").val();
		}else{
			var attQuantity1Num = 0;
		}
		//原支数
		if($tr.find(".regBranchQuantity").val()){
			var attQuantity3Num = $tr.find(".regBranchQuantity").val();
		}else{
			var attQuantity3Num = 0;
		}

		//正常地板0 壁纸1 铺料2  只有支数3
        var type = productDisc(branchPerBox,perBranch,perBox);
        if(isEqual == null){
        	if(type==2){
        		var a = accDiv(perBox,10);
                quantity = accMul(boxQuantity,a);                
        	}else if(type==3){
        		quantity = accMul(perBranch,branchQuantity);
                $tr.find(".boxQuantity").val("");
                $tr.find(".scatteredQuantity").val("");
                $tr.find(".branchPerBox").val("");
        	}else if(type==0){
            	var branchQuantity=accMul(boxQuantity,branchPerBox);
            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
            	if(isNaN(branchQuantity)){
            		branchQuantity = 0;
        		}
            	var quantity=accMul(branchQuantity,perBranch);
            	//计算差异支数
            	var differenceBranchQuantity=accSub(branchQuantity,attQuantity3Num);

				//计算差异数量
				var differenceQuantity=accSub(quantity,attQuantity1Num);
            }
        	if(isNaN(quantity)){
        		quantity = 0;
        	}
        	$tr.find(".quantity").val(quantity);//平方数
        	$tr.find(".branchQuantity").val(branchQuantity);//支数
        	$tr.find(".differenceQuantity").val(differenceQuantity);//差异数量
        	$tr.find(".differenceQuantityText").html(differenceQuantity);//差异数量
        	$tr.find(".differenceBranchQuantity").val(differenceBranchQuantity);//差异支数
        	$tr.find(".differenceBranchQuantityText").html(differenceBranchQuantity);//差异支数
        }else{
        	if(type==2){
        		var a = accDiv(perBox,10);
                quantity = accMul(boxQuantity,a);
                $tr.find(".quantity").val(quantity);//平方数

        	}else if(type==3){
        		quantity = accMul(perBranch,branchQuantity);
				$tr.find(".quantity").val(quantity);//平方数
             	$tr.find(".branchQuantity").val(branchQuantity);//支数
                $tr.find(".boxQuantity").val("");
                $tr.find(".scatteredQuantity").val("");
                $tr.find(".branchPerBox").val("");
        	}else if(boxQuantity!='' && branchPerBox!=0 && perBranch!=0){
            	var branchQuantity=accMul(boxQuantity,branchPerBox);
            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
            	if(isNaN(branchQuantity)){
            		branchQuantity = 0;
        		}
            	var quantity=accMul(branchQuantity,perBranch);
            	if(isNaN(quantity)){
            		quantity = 0;
            	}
				//计算差异支数
				var differenceBranchQuantity=accSub(branchQuantity,attQuantity3Num);
				if(isNaN(differenceBranchQuantity)){
					differenceBranchQuantity = 0;
				}
				//计算差异数量
				var differenceQuantity=accSub(quantity,attQuantity1Num);
				if(isNaN(differenceQuantity)){
					differenceQuantity = 0;
				}
            	$tr.find(".quantity").val(quantity);//平方数
            	$tr.find(".branchQuantity").val(branchQuantity);//支数
				$tr.find(".differenceQuantity").val(differenceQuantity);//差异数量
				$tr.find(".differenceQuantityText").html(differenceQuantity);//差异数量
				$tr.find(".differenceBranchQuantity").val(differenceBranchQuantity);//差异支数
				$tr.find(".differenceBranchQuantityText").html(differenceBranchQuantity);//差异支数
            }
        }
        
	});

}

function editQty(t,e){
	if($(t).attr("kid")=="quantity"){//平方
		if(extractNumber(t,6,false,e)){
			var $tr = $(t).closest("tr");
			var branch_quantity=0;
			var box_quantity=0;
			
			var quantity=$(t).val();
			var perBranch=$tr.find(".perBranch").val();  //每支单位数
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var scattered=0;
			if(perBranch!=0 && branchPerBox!=0){
				 branch_quantity=quantity/perBranch;
				 box_quantity=parseInt(branch_quantity/branchPerBox);
				 scattered=(branch_quantity%branchPerBox).toFixed(6);
			}
			$tr.find(".boxQuantity").val(box_quantity);
			$tr.find(".branchQuantity").val(branch_quantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
	
			countTotal($tr);
			$(t).val(quantity).toFixed(6);
			addBackColor(t);
		}
	}else{
		if(extractNumber(t,3,false,e)){
			var $tr = $(t).closest("tr");
			
			var branch_quantity=0;
			var box_quantity=0;
			
			if($(t).attr("kid")=="box"){//箱
				$tr.find(".scatteredQuantityStr").html(0);
				$tr.find(".scatteredQuantity").val(0);
			}else if($(t).attr("kid")=="branch"){//支
				var quantity=$(t).val();
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var box=parseInt(quantity/branchPerBox);
				var scattered=quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}else if($(t).attr("kid")=="quantity"){//平方
				var quantity=$(t).val();
				var perBranch=$tr.find(".perBranch").val();  //每支单位数
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var branch_quantity=quantity/perBranch;
				var box_quantity=parseInt(branch_quantity/branchPerBox);
				var scattered=branch_quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box_quantity);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}
			countTotal($tr);
			addBackColor(t);
		}
	}
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $excelImport = $("#excelImport");
	var $addProduct = $("#addProduct");
	var $sbuId = $("#sbuId");
	var $billCategoryId = $("#billCategoryId");
	var $deleteProduct = $("a.deleteProduct");

	// 表单验证
	$inputForm.validate({
		rules: {
			warehouseName: {
				required: true
			},
			saleOrgName: {
				required: true
			}
		} 			
	});
	 var time = new Date();
     var day = ("0" + time.getDate()).slice(-2);
     var month = ("0" + (time.getMonth() + 1)).slice(-2);
     var today = time.getFullYear() + "-" + (month) + "-" + (day);
     $('#needTime').val(today);

	//打开选择产品界面
    $addProduct.click(function(){
   	   var $this = $(this);
	   var $tr =$this.closest("tr");
	   var warehouseId = $("input.warehouseId").val();
	   if (warehouseId==undefined || warehouseId.length == 0) {
			$.message_alert("请选仓库！");
			return false;
	   }
	   //SBU
       var sbuId = $("#sbuId").val();
       if(isNull(sbuId) == null){
    	  $.message_alert("请选sbu！");
		  return false;
       }
       var params='';
	   var typesystemDictFlag = $("input.typesystemDictFlag").val();
	   if(isNull(typesystemDictFlag) != null && typesystemDictFlag == 0){
			var organizationId = $(".organizationId").val();
   	  		if (isNull(organizationId) == null) {
   	  			$.message_alert('经营组织不能为空');
     			return false;
	  		}
   	  		params+='&productOrganizationId='+organizationId;
	   }
       $addProduct.bindQueryBtn({
           type:'product',
           bindClick:false,
           title:'${message("查询产品")}',
           url:'/product/product/selectProductPage.jhtml?multi=2&sbuId='+sbuId+params,
           callback:function(rows){
               if(rows.length>0){
                   for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid.addRow(row,null,1);
					}	
                   countTotal();
                   productOrganization();
               }
           }
       }); 
    })
    

	  //查询仓库
      $("#selectWarehouse").click(function(){
          var saleOrgId = $(".saleOrgId").val();
          if(isNull(saleOrgId) == null){
          	$.message_alert('请选择机构');
              return false;
          }
          var sign = '${sign}';
          $("#selectWarehouse").bindQueryBtn({
              type:'warehouse',
              bindClick:false,
              title:'${message("查询仓库")}',
              url:'/stock/warehouse/select_warehouse.jhtml?saleOrgId='+saleOrgId+'&sign='+sign,
              callback:function(rows){
                  if(rows.length>0){
                      var row = rows[0];
                      $(".warehouseId").val(row.id);
                      $(".enableLocation").val(row.enable_location);                          
                      $(".warehouseName").val(row.name);
					  $("input.organizationId").val(row.management_organization_id);
                      $(".organizationName").text(row.management_organization_name);
					  $(".typesystemDictFlag").val(row.type_system_dict_flag);
                      $(".productionPlantId").val(row.production_plant);
					  $("#enableBatch").val(row.enable_Batch);
					  $("#enableLocation").val(row.enable_location);
                      getSBUInformation(row.id);        //赋值sbu
                      $(".sbuId").val(row.sbuId);
                      $(".sbu").val(row.sbuName);
                      $mmGrid.removeRow();
                      countTotal();
                  }
              }
          });
     })

      

	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml',
		callback:function(rows){
            if(rows.length>0){
                var row = rows[0];
                $("input.saleOrgId").val(row.id);
                $("input.saleOrgName").val(row.name);
            	//清空仓库
            	$("input.warehouseId").val('');             
                $("input.warehouseName").val('');
                //sbu
                $("#sbuId").html("");
                $(".sbuId").val('');
                $(".sbu").val('');
                
                $mmGrid.removeRow();
            }
	    }
	});


    //选择SBU
	$sbuId.change(function(){
		$mmGrid.removeRow();
    });

	var line_no = 1;
	// 删除产品
	$deleteProduct.live("click", function() {
		var index = $(this).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid.removeRow(index);
			countTotal();
			var line_number = 1;
			$("span.line_no").each(function(){
				$(this).html(line_number++);
			});
			$("input.lineNo").each(function(){
				$(this).val(line_number++);
			});
			line_no--;
		})
	});

	var items = ${jsonStr};
	var itemIndex=0;
	var cols = [
			{ title:'${message("行号")}', name:'line_no' ,width:60, align:'center', renderer: function(val,item,rowIndex,obj){									
				return '<span class="line_no">'+ (rowIndex+1) +'</span>'+'<input type="hidden" class="lineNo" name="inventoryItems['+itemIndex+'].lineNo" value="'+(rowIndex+1)+'"/>';
			}},
			{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
				return '<a href="javascript:;" class="deleteProduct btn-delete">删除</a>';
			}},
        	{ title:'${message("12211")}', name:'vonder_code' ,align:'center', width:150, renderer: function(val,item,rowIndex, obj){
        		var html=val+'<input type="hidden" class="text vonderCode" name="inventoryItems['+itemIndex+'].vonderCode"  value="'+val+'">';
        		if(item.productionSchedulingItemId != null && item.productionSchedulingItemId !=''){
        			html+='<input type="hidden" class="text" name="inventoryItems['+itemIndex+'].productionSchedulingItem.id" value="'+item.productionSchedulingItemId+'">';			
    			}	
    			return html;
    		}},
        	{ title:'${message("产品名称")}', name:'name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
    			var productId = '';
    			if(item.product != null){
    				productId = item.product;			
    			}else {
    				productId = item.id;	
    			}
    			var	html = val+'<input type="hidden" name="inventoryItems['+itemIndex+'].product.id"  class="productId productId_'+productId+'" value="'+productId+'" />'+
    				'<input type="hidden" class="text productName" name="inventoryItems['+itemIndex+'].name" readonly value="'+item.name+'" />';
    			return html;
    		}},
        	{ title:'产品描述', name:'product_description', align:'center',width:200 , renderer: function(val,item,rowIndex,obj){
        			if(val){
						return '<span>'+val+'</span>'+'<input type="hidden" name="inventoryItems['+itemIndex+'].description" class="text" value="'+val+'';
					}else{
						return '<span>'+item.description+'</span>'+'<input type="hidden" name="inventoryItems['+itemIndex+'].description" class="text" value="'+item.description+'';
					}
				}},
			{ title:'产品型号',hidden:true, name:'model', align:'center',width:200 ,renderer: function(val,item,rowIndex, obj){
					var html=val+'<input type="hidden" name="inventoryItems['+itemIndex+'].model" class="text" value="'+val+'" />';
				return html;
			}},
    	    { title:'${message("经营组织")}',name:'product_organization_name',width:100, align:'center', renderer: function(val,item,rowIndex,obj){
    	    		var html='';
					html +='<select name="inventoryItems['+itemIndex+'].productOrganization.id" class="text productOrganization">';
					if(obj==undefined){
						var productOrganizationName = '';
						if(item.product_organization_name != null && item.product_organization_name !=''){
							productOrganizationName = item.product_organization_name;
						}
						if(item.product_organization_id != null && item.product_organization_id !=''){
							html+='<option value="'+item.product_organization_id+'" selected="selected" >'+productOrganizationName+'</option> ';
						}
					}
					return html+='</select>';
  		 	}}, 
    		{ title:'${message("产品等级")}', name:'product_grade' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
    			var str='selected="selected"';
    			var html='<select name="inventoryItems['+itemIndex+'].productLevel.id" class="text productGrade" id="productLevel" onchange="product_orl(this)">';
    				[#list productLevelList as products]
	    				if(${products.id}==item.level_Id){
	    					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
	    				}else{
	    					html+='<option value="${products.id}">${products.value}</option> ';
	    				}
    				[/#list]
    				html+='</select>';
    			return html;
     		}},
		//原有数量
		{ title:'${message("原支数")}', name:'reg_branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
				console.log("原支数"+val);
				if(val) {
					var text = '<ul><li><span class="attQuantity3Num">'+val+'</span></li></ul>';
					text += '<input type="hidden" name="inventoryItems[' + itemIndex + '].regBranchQuantity" class="regBranchQuantity text" value="'+val+'" />';
					return text;
				}else{
					var text = '<ul><li><span class="attQuantity3Num">0</span></li></ul>';
					text += '<input type="hidden" name="inventoryItems[' + itemIndex + '].regBranchQuantity" class="regBranchQuantity text" value="0" />';
					return text;
				}
			}},
		{ title:'${message("原数量")}', align:'center',name:'reg_quantity',renderer: function(val,item,rowIndex, obj){
				var quantity = 1;
				if(obj==undefined){
					quantity = val;
				}
				console.log("原数量"+val);
				console.log("原数量"+val);
				if(val){
					var text = '<ul><li>';
					text += '<span class="attQuantity1Num">'+val+'</span></li></ul>';
					text += '<input type="hidden" name="inventoryItems['+itemIndex+'].regQuantity" class="regQuantity text" value="'+val+'" />';
					return text;
				}else{
					var text = '<ul><li>';
					text += '</div></li><li><span class="attQuantity1Num">0</span></li></ul>';
					text += '<input type="hidden" name="inventoryItems['+itemIndex+'].regQuantity" class="regQuantity text" value="0" />';
					return text;
				}
			}},
		//盘点数量
		{ title:'${message("盘点件数")}', name:'box_quantity', align:'center', width:80, renderer:function(val,item,rowIndex,obj){
    			var quantity = 1;
    			if(obj==undefined){
    				quantity = val;
    			}
   				var text = '<ul><li><div class="lh20">'+
   						'<div class="nums-input ov">'+
   			            	'<input type="text" kid="box" class="t boxQuantity"  name="inventoryItems['+itemIndex+'].boxQuantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
   			        	'</div>';
   				return text;
    		}},    		
     		{ title:'${message("盘点支数")}', name:'branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
    			var branchQuantity=0;
    			if(obj==undefined){
    				branchQuantity = val;
    			}
    			var branchPerBox = 1;
    			var perBranch = 1;
    			if(item.branch_per_box != "" && item.branch_per_box != null) {
    				branchPerBox = item.branch_per_box;
    			}
    			if(item.per_branch != null && item.per_branch != ""){
    				perBranch = item.per_branch;
    			}
    			
   				var text = '<ul><li><div class="lh20">'+
   				'<div class="nums-input ov">'+
   	        		'<input type="text" kid="branch" class="t branchQuantity"  name="inventoryItems['+itemIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
   	        		'<input type=hidden class="branchPerBox" name="inventoryItems['+itemIndex+'].branchPerBox"  value="'+branchPerBox+'" /> '+
   					'<input type=hidden class="perBranch" name="inventoryItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
				'</div>';
				return text;
    		}},
     		{ title:'${message("盘点数量")}', align:'center',name:'quantity',renderer: function(val,item,rowIndex, obj){
     			var quantity = 1;
    			if(obj==undefined){
    				quantity = val;
    			}
    			var text = '<ul><li><div class="lh20">'+
    						'<div class="nums-input ov">'+
    			            	'<input type="text" kid="quantity" class="t quantity"  name="inventoryItems['+itemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
    			            	'<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
    			        	'</div>';
    			return text;
    	    }},
    		{ title:'${message("盘点零散支数")}', name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
    			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
    			var branchQuantity='';
    			if(obj==undefined){
    				branchQuantity = val;
    			}
    			if(val){
					var html='<span class="scatteredQuantityStr">'+val+'</span>'+
							'<input type="hidden" name="inventoryItems['+itemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+val+'" />';
					return html;
				}
    			var html='<span class="scatteredQuantityStr">0</span>'+
    				'<input type="hidden" name="inventoryItems['+itemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="0" />';
    			return html;
    		}},   		

			//差异数量
		{ title:'${message("差异支数")}', name:'difference_branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
			if(val){
				var text ='<span class="differenceBranchQuantityText">'+val+'</span>' +
						'<input type="hidden" name="inventoryItems['+itemIndex+'].differenceBranchQuantity" class="differenceBranchQuantity " value="'+val+'" />';
				return text;
			}else {
				var text = '<ul><li>' +
						'</div></div></li><li><span class="differenceBranchQuantityText">0</span></li></ul>' +
						'<input type="hidden" name="inventoryItems[' + itemIndex + '].differenceBranchQuantity" class="differenceBranchQuantity " value="0" />';
				return text;
			}
			}},
		{ title:'${message("差异数量")}', align:'center',name:'difference_quantity',renderer: function(val,item,rowIndex, obj){
				if(val){
					var text ='<span class="differenceQuantityText">'+val+'</span>' +
							'<input type="hidden" name="inventoryItems['+itemIndex+'].differenceQuantity" class="differenceQuantity " value="'+val+'" />';
					return text;
				}else {
					var text = '<ul><li><div class="lh20">' +
							'<span class="differenceQuantityText">0</span>' +
							'<input type="hidden" class="perBox" value="0" />' +
							'<input type="hidden" name="inventoryItems[' + itemIndex + '].differenceQuantity" class="differenceQuantity " value="0" />' +
							'</li></ul>';
					return text;
				}
			}},

    		[#if hiddenBatch !=0]     			
    		        { title:'${message("色号")}', align:'center',width:60,name:'color_numbers',renderer: function(val,item,rowIndex, obj){
							var str='selected="selected"';
							var html='<select name="inventoryItems['+itemIndex+'].colorNumbers.id" class="text colourNumber">';
							html+='<option value="">请选择</option> ';
							[#list colorNumberList as colorNumbers]
							if(${colorNumbers.id}==val){
								html+='<option value="${colorNumbers.id}" '+str+' >${colorNumbers.value}</option> ';
							}else{
								html+='<option value="${colorNumbers.id}">${colorNumbers.value}</option> ';
							}
							[/#list]
							html+='</select>';
							return html;
    		        }},    
    		        { title:'${message("含水率")}', align:'center',width:60,name:'moisture_contents',renderer: function(val,item,rowIndex, obj){
							var str='selected="selected"';
							var html='<select name="inventoryItems['+itemIndex+'].moistureContents.id" class="text moistureContent">';
							html+='<option value="">请选择</option> ';
							[#list moistureContentList as moistureContents]
                            console.log("含水率val："+val);
							if(${moistureContents.id}==val){
								html+='<option value="${moistureContents.id}" '+str+' >${moistureContents.value}</option> ';
							}else{
								html+='<option value="${moistureContents.id}">${moistureContents.value}</option> ';
							}
							[/#list]
							html+='</select>';
							return html;
    		        }},
    		        { title:'${message("批次")}',name:'batch_encoding', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
							var html = '<span class="search" style="position:relative">';
    		        	if(item.warehouse_batch){
							 html = html+ '<input type="hidden" name="inventoryItems['+itemIndex+'].warehouseBatch" class="text batchIds" value="'+item.warehouse_batch+'" />';
						}else{
							html = html+ '<input type="hidden" name="inventoryItems['+itemIndex+'].warehouseBatch" class="text batchIds" value="" />';
						}
    		        	if(val){
							html = html + '<input type="text" name="inventoryItems['+itemIndex+'].batchEncoding" class="text batchEncodings" value="'+val+'" readonly/>';
						}else{
							html = html + '<input type="text" name="inventoryItems['+itemIndex+'].batchEncoding" class="text batchEncodings" value="" readonly/>';
						}
							html = html +'<input type="button" class="iconSearch" value="" id="selectBatch"/>'
    		        		+'</span>';
    		        	 return html;
    		        }},    		        
    		 [/#if]
    		{ title:'${message("库位")}',name:'warehouseLocationCode', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
    				 var warehouseLocation;
    				if(item.warehouse_location){
						warehouseLocation = item.warehouse_location;
					}
    				if(warehouseLocation){
						var html = '<span class="search" style="position:relative">'
								+'<input type="hidden" name="inventoryItems['+itemIndex+'].warehouseLocation.id" class="text warehouseLocationId" value="'+warehouseLocation+'" />'
								+'<input type="text"  class="text warehouseLocationCode" oninput = "processingLocation(this)" value="'+val+'"/>'
								+'<input type="button" class="iconSearch" value="" id="selectStorehouse"/>'
								+'<div class="textDis" id="lineLocation"><ul class="lineLocationData"></ul></div></span>';
						return html;
					}else {
						var html = '<span class="search" style="position:relative">'
								+'<input type="hidden" name="inventoryItems['+itemIndex+'].warehouseLocation.id" class="text warehouseLocationId" value="" />'
								+'<input type="text"  class="text warehouseLocationCode" oninput = "processingLocation(this)" value=""/>'
								+'<input type="button" class="iconSearch" value="" id="selectStorehouse"/>'
								+'<div class="textDis" id="lineLocation"><ul class="lineLocationData"></ul></div></span>';
						return html;
					}

	        }},
    		 { title:'${message("新旧标识")}', align:'center',width:60,name:'new_old_logos',renderer: function(val,item,rowIndex, obj){
		        var str='selected="selected"';
		   		var html='<select name="inventoryItems['+itemIndex+'].newOldLogos.id" class="text newOldLogo">';
 		   		[#list newOldLogosList as newOldLogos]
					 if(${newOldLogos.id}==item.new_old_logos){
						 html+='<option value="${newOldLogos.id}" '+str+' >${newOldLogos.value}</option> ';
					 }else {
						 html += '<option value="${newOldLogos.id}">${newOldLogos.value}</option> ';
					 }
		   		[/#list]
		   		html+='</select>';
		   		return html;
		      }},
			 { title:'${message("备注")}', align:'center',width:100,name:'memo',renderer: function(val,item,rowIndex, obj){
					var html='<input type="text" name="inventoryItems['+itemIndex+'].memo" class="text" value="'+val+'" />';
					return html;
			 }},{ title:'${message("")}',hidden:'true', align:'center', renderer:function(val,item,rowIndex,obj){
				itemIndex++;
				line_no++;
				return '<input type="hidden">';
			}}


	];
	
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
		cols: cols,
		items:items,
		fullWidthRows:true,
		checkCol: true,
		autoLoad: true,
		checkByClickTd:true,
		rowCursorPointer:true,
		multiSelect:true
	 });

	//批次
	$("#selectBatch").live("click",function(){
		if($("#enableBatch").val()){
			$.message_alert("该仓库没有启用批次");
			return false;
		}
		//单据类型
		// var billTypeId = 505;
		var $tr = $(this).closest("tr");
		// //行Id
		// var itemId = $tr.find("input.itemId").val();
		// //仓库
		// var warehouseId = $("input.warehouseId").val();
		// if(isNull(warehouseId) == null){
		// 	$.message_alert("仓库不能为空");
		// 	return false;
		// }
		// //等级
		// var productLevelId = $tr.find(".grade").val();
		// var productGrade = $tr.find(".productGrade").val();
		// if(isNull(productLevelId) == null && isNull(productGrade) == null ){
		// 	$.message_alert("产品等级不能为空");
		// 	return false;
		// }
		// if(isNull(productLevelId) == null){
		// 	productLevelId = productGrade;
		// }
        //
		// //产品
		// var productId = $tr.find(".productId").val();
		// if(isNull(productId) == null){
		// 	$.message_alert("产品Id不能为空");
		// 	return false;
		// }
		//经营组织
		var organizationId = $tr.find(".productOrganization").val();
		var productOrganizationId = $tr.find("input.productOrganizationId").val();
		if(isNull(organizationId) == null && isNull(productOrganizationId) == null){
			$.message_alert("产品行经营组织不能为空");
			return false;
		}
		if(isNull(organizationId) == null){
			organizationId = productOrganizationId;
		}
        //
		// //色号
		// var colorNumbersIds = $tr.find(".colourNumber").val();
		// if(isNull(colorNumbersIds) == null){
		// 	colorNumbersIds = "";
		// }
		// //含水率
		// var moistureContentsIds = $tr.find(".moistureContent").val();
		// if(isNull(moistureContentsIds) == null){
		// 	moistureContentsIds = "";
		// }
		// //批次
		// var warehouseBatchIds = $tr.find(".batchIds").val();
		// if(isNull(warehouseBatchIds)==null) {
		// 	warehouseBatchIds = "";
		// }
		// var params='&warehouseId='+warehouseId+'&productId='+productId+'&organizationId='+organizationId+'&billTypeId='+billTypeId
		// 		+'&productLevelId='+productLevelId+'&colorNumbersIds='+colorNumbersIds+'&moistureContentsIds='+moistureContentsIds
		// 		+'&warehouseBatchIds='+warehouseBatchIds;
        var params='&organizationId='+organizationId+'&batchStatus='+true;
		$(this).bindQueryBtn({
			bindClick:false,
			type : "post",
			title:'选择批次',
			url:'/stock/batch/select_bacth.jhtml?'+params,
			callback:function(rows){
				var allId = '';
				var allName = '';
				var regBranchQuantity = 0;
				var regQuantity = 0;
				if(rows.length>0){
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						if(row){
									allId = row.id;
									allName = row.batch_encoding;
							break;
						}
					}
				}
				$tr.find(".batchIds").val(allId);
				$tr.find(".batchEncodings").val(allName);
				loadAttQuantity($tr);
				countTotal($tr);
			}
		});
	});

    //选择库位
    $("#selectStorehouse").live("click",function(){
		if($("#enableLocation").val()){
			$.message_alert("该仓库没有启用库位");
			return false;
		}
    	getLocationInformation(this);
    }); 

    [#if linkStock == 1 ] 
	  	 //产品经营组织
		 $(".productOrganization").live("change", function() {
			 var $tr = $(this).closest("tr");
			 //批次
			 $tr.find(".batchIds").val('');
			 $tr.find(".batchEncodings").val('');
			 loadAttQuantity($tr); 
		 })
		//产品等级
		$(".grade").live("change", function() {
			var $tr = $(this).closest("tr");
			loadAttQuantity($tr);
		})
		 //产品等级
		 $(".productGrade").live("change", function() {
			 var $tr = $(this).closest("tr");
			 loadAttQuantity($tr); 	
		 }) 
		 //色号
		 $(".colourNumber").live("change", function() {
			 var $tr = $(this).closest("tr");
			 loadAttQuantity($tr); 
		 })
		 //含水率
		 $(".moistureContent").live("change", function() {
			 var $tr = $(this).closest("tr");
			 loadAttQuantity($tr); 
		 })
		 //新旧标识
		 $(".newOldLogo").live("change", function() {
			 var $tr = $(this).closest("tr");
			 loadAttQuantity($tr); 
		 });		 	 
	[/#if]

    //选择库位点击事件
    $(".textDis li").live("click",function(){
        assignmentLi(this);
    });

	var orderFullLink_items = ${inventoryFullLink_json};
	var fullcols = [
		{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
		cols: fullcols,
		items:orderFullLink_items,
		checkCol: false,
		autoLoad: true
	});
});

function select_post(e,organizationId,productionPlantId,callback){
	$(e).bindQueryBtn({
		bindClick:false,
		type:'bacth',
		title:'选择批次',
		url:'/stock/batch/select_bacth.jhtml?organizationId='+organizationId,
		callback:function(rows){
			if(rows.length>0){
				if(callback(rows)==false){
					return false;
				}
			}
		}
	});
}

//处理库位
function processingLocation(e) {
    //获取当前控件的id
    var str = $(e).siblings("div").attr("id");
    ajaxGetLocation(e,str);
}

function deleteInventory(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
	})
}

//获取指定仓库id的sbu信息
function getSBUInformation(warehouseId) {
	if(isNull(warehouseId) != null){
		$.ajax({
			url : '/stock/warehouse/getSBUInformation.jhtml',
			type : "post",
			data : {warehouseId:warehouseId},
			success : function(data) {
				var content = JSON.parse(data.content);
				if(content.total!=0){
					var html = "";					
					for(var i=0; i < content.total; i++){
						html += '<option value="'+ content.content[i].sbu+'">'+content.content[i].sbu_name+'</option>';    						
					}
					$("#sbuId").html("");
					$("#sbuId").html(html);					
				}
			}
		})
	}
	
}

function judgmentInventoryValue(){
	var data = $("#inputForm").serializeObject();
	var flag = true;
	var msg = "";
	//获取是否启用库位
	var enableLocation = $(".enableLocation").val();
	//行信息校验
	$("input.productId").each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		//色号
		var colourNumber = $tr.find(".colourNumber").val();
		if(isNull(colourNumber) == null){
			msg = "请选择色号";
			flag = false;
			return flag;
		}
		//含水率
		var moistureContent = $tr.find(".moistureContent").val();
		if(isNull(moistureContent) == null){
			msg = "请选择含水率";
			flag = false;
			return flag;
		}
		//新旧标识
		var newOldLogo = $tr.find(".newOldLogo").val();
		if(isNull(newOldLogo) == null){
			msg = "请选择新旧标识";
			flag = false;
			return flag;
		}

		//校验支数是否为整数
		var branchQuantity = $tr.find(".branchQuantity").val();
		if(isNull(branchQuantity) != null && String(branchQuantity).indexOf(".")>-1){
			msg = "行明细中的支数不能为小数，请填写正确的支数！";
			flag = false;
			return flag;
		}
	})
	var productId = $("input.productId").val();
	if(isNull(productId) == null){
		msg = "请检查是否添加单据行!";
		flag = false;
	}
	//获取出仓类型
	var billTypeId = $("input.billTypeId").val();

	if($("#inputForm").valid() && flag){
		var str = '您确定要保存吗？';
		$.message_confirm(str,function(){
			Mask();
			ajaxSubmit('',{
				url:'update.jhtml',
				method:"post",
				async: false,
				data:data,
				failCallback:function(resultMsg){				// 访问地址失败，或发生异常没有正常返回
					$.message_alert(resultMsg.rmid+"<br/>"+resultMsg.content);
					UnMask();
				},
				callback: function (resultMsg) {
					location.reload(true);
				}
			});
		});
	}else{
		$.message_alert(msg);
		return flag;
	}
}

//审核
function checkWf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var flag = checkLocation();
	if($form.valid() && flag){
		var url="checkWf.jhtml?id="+${inventory.id};
		var str = '您确定要审核吗？';
		$.message_confirm(str,function(){
			Mask();
			ajaxSubmit($this,{
				url: url,
				method: "post",
				failCallback:function(resultMsg){
					// 访问地址失败，或发生异常没有正常返回
					messageAlert(resultMsg);
					UnMask();
				},
				callback:function(resultMsg){
					location.reload(true);
				}
			});
		});
	}
}

//作废
function cancel(e){
	ajaxSubmit(e,{
		method:'post',
		url:'/stock/inventory/cancel.jhtml?id=${inventory.id}',
		isConfirm:true,
		confirmText:'您确定要作废该盘点库存单吗？',
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	})
}

function product_orl(e){
	var $tr = $(e).closest("tr");
	$tr.find(".boxQuantity").val(0);//盘点件数
	$tr.find(".quantity").val(0);//盘点平方数
	$tr.find(".branchQuantity").val(0);//盘点支数
	$tr.find(".scatteredQuantity").val(0);//盘点零散支数
	$tr.find(".scatteredQuantityStr").html(0);//盘点零散支数
	$tr.find(".differenceQuantity").val(0);//差异数量
	$tr.find(".differenceQuantityText").html(0);//差异数量
	$tr.find(".differenceBranchQuantity").val(0);//差异支数
	$tr.find(".differenceBranchQuantityText").html(0);//差异支数
	countTotal($tr);
	productOrganization();
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑库存盘点单")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<div class="title-style mt0">
				${message("基本信息")}
			</div>
	[#--单据头-基本信息--]
			<table class="input input-edit">
				<input type="hidden" name="id" id="id" value="${inventory.id}" />
				<tr>
					<th>
						${message("库存盘点单")}
					</th>
					<td>
						<input type="hidden" id="enableBatch" value=""/>
						<input type="hidden" id="enableLocation" value=""/>
						<input type="hidden" name="sn"  class="sn" value="${inventory.sn}"/>
						<span>${inventory.sn}</span>
					</td>
					<th>${message("机构")}:</th>
					<td>
						<input type="hidden" name="saleOrgId"  class="saleOrgId" value="${inventory.saleOrg.id}"/>
						<span>${inventory.saleOrg.name}</span>
					</td>
					<th>${message("经营组织")}:</th>
					<td>
						<input type="hidden" name="organizationId"  class="organizationId" value="${inventory.organization.id}"/>
						<span class="organizationName">${inventory.organization.name}</span>
					</td>
					<th>${message("Sbu")}:</th>
					<td>
						<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${inventory.sbu.id}" />
						<span id="sbuName">${inventory.sbu.name}</span>
					</td>
				</tr>
				<tr>
					<th>${message("仓库")}:</th>
					<td>
						<input name="warehouseId" class="text warehouseId" type="hidden" value="${inventory.warehouse.id}" />
						<input type="hidden" name="enableLocation" class="text enableLocation" btn-fun="clear" value="${inventory.warehouse.enableLocation}"/>
						<input type="hidden" class="typesystemDictFlag" value="${inventory.warehouse.typeSystemDict.flag}" />
						<input type="hidden" class="productionPlantId" value="${inventory.warehouse.productionPlant.id}" />
						<span>${inventory.warehouse.name}</span>
					</td>
					<th>${message("创建时间")}:</th>
					<td>
						<span>${inventory.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
					</td>
					<th>${message("创建人")}:</th>
					<td>
						<span>${creator}</span>
					</td>
					<th>${message("单据状态")}:</th>
					<td>
						[#if inventory.status == 0]
							<span class="blue">已保存</span>
						[#elseif inventory.status == 2]
							<span class="green">已审核</span>
						[#elseif inventory.status == 3]
							<span class="red">已作废</span>
						[/#if]
					</td>
				</tr>
				<tr>
					<th>
						${message("GL日期")}:
					</th>
					<td>
						<input type="text" class="text" name="billDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${inventory.billDate}"  />
					</td>
					<th>${message("审核人")}:</th>
					<td>
						<span>${inventory.checkMan.name}</span>
					</td>
				</tr>
				<tr>
					<th>${message("备注")}:</th>
					<td colspan="7">
						<textarea class="text remark"  name="remark">${inventory.remark}</textarea>
					</td>
				</tr>
			</table>

	[#--单据明细--]
	<table class="input input-edit" style="width:100%;margin-top:5px;">
		<div class="title-style">
			${message("单据明细")}:
			<div class="btns">
				<a href="javascript:void(0);" id="addProduct" class="button">${message("添加产品")}</a>
			</div>
		</div>
		<table id="table-m1"></table>
	</table>
			[#-- 全链路 --]
			<div class="title-style">${message("全链路信息")}:</div>
			<table id="table-full"></table>
		[#--按钮--]
		<div class="fixed-top">
			[#if inventory.status != 2 && inventory.status != 3]
			<input type="button" id="submit_button" class="button sureButton" value="${message("1013")}" onclick="judgmentInventoryValue()"/>
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
			[#if inventory.status == 0 ]
				<input type="button" class="button sureButton" value="${message("审核")}" onclick="checkWf(this)" />
				<input type="button" class="button cancleButton" value="${message("作废")}" onclick="cancel(this)" />
			[/#if]
			[#else]
				<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
			[/#if]
		</div>
		[#--加载框--]
		<div id="loading" class="submit_loading " style="display:none">
			<div class="loading_show ">
				<img src="/resources/images/loading_icon.gif">
				<p class="loading_context ">正在提交，请稍后。。。</p>
			</div>
		</div>
		</div>
	</form>
</body>
</html>