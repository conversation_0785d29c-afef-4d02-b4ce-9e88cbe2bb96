<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("计划提报")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function initWfStates(){
	wfStates = {};
	[#list wfStates as wfState]
		wfStates['${wfState}'] = '${message('22222222'+wfState)}';
	[/#list]
}
function edit(id){
	parent.change_tab(0,'edit.jhtml?id='+id+'&isCheck=${isCheck}');
}
function add(){
	parent.change_tab(0,'add.jhtml');
}
$().ready(function() {
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	initWfStates();
	var statuss = {'0':'已保存', '2':'已审核','3':'已失效'};
	var cols = [
		{ title:'${message("要货单号")}', name:'sn' , align:'center', width:120, renderer: function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
		}},
		{ title:'${message("客户")}', name:'store_name' , align:'center',width:220},
		{ title:'${message("sbu")}', name:'sbu_name' , align:'center',width:220},
		{ title:'${message("机构")}', name:'sale_org_name' , align:'center',width:220},
		{ title:'${message("要货月份")}', name:'need_date' , align:'center',width:120, renderer:function(val,item,rowIndex,obj){
			var need_date = item.need_date;
			var html = need_date.substring(0,7);
			return html;
		}},
		{ title:'${message("要货总数量(㎡)")}', name:'total_quantity' , align:'center'},
		{ title:'${message("单据状态")}', name:'status', align:'center', renderer: function(val,item,rowIndex){
			var html = statuss[val];
			if(html!=undefined){
				return html;
			}
		}},
		{ title:'${message("流程状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result = wfStates[item.wf_state];
			if(result!=undefined)return result;
			
		}},
		{ title:'${message("创建人")}', name:'store_member_name' , align:'center'},
		{ title:'明细' ,align: 'center', cols: [
		{ title:'${message("12211")}', name:'vonder_code' ,width:80, isLines:true, align:'center'},
		{ title:'${message("产品名称")}', name:'name' ,width:150, isLines:true, align:'center'},
		{ title:'${message("产品型号")}', name:'model' ,width:80, isLines:true, align:'center'},                         		
		{ title:'${message("要货日期")}', name:'need_date' ,width:80, isLines:true, align:'center',renderer:function(val,item){
		   return val.substring(0,10);
			
		}},                         		
		{ title:'${message("要货平方数")}', name:'quantity' ,width:40, isLines:true, align:'center',renderer:function(val,item){
		 return '<input type="hidden" name="suliang" value="'+val+'">'+val;
		 }},
		 { title:'${message("要货纸张数")}', name:'paper_num' ,width:80, isLines:true, align:'center',renderer:function(val,item){
				var paper_num = val;
				if (val = null || val == '' || obj == 'undefined' || val == 'undefined') {
					paper_num =0;
				}   
				    var page=0;
					var quantity = item.quantity;
					if (!isNaN(quantity) && paper_num !=0) {
						page =accDiv(quantity,paper_num).toFixed(0);
					}else{
						page =0;
					}			
	    return html='<input type="hidden"  name="page" value="'+page+'">'+page;
		 }}
		]},                
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center',width:220 }

	];
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        url: 'list_data.jhtml',
        lineRoot:"items",
        fullWidthRows:true,
        params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	//查询SBU
	$("#selectSbu").bindQueryBtn({
		type:'sbu',
		title:'查询Sbu',
		url:'/basic/sbu/select_sbu.jhtml'
	});
    
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
	
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?isSelect=0&multi=2',
		callback:function(rows){
			if(rows.length>0){
				var mhtml="";
				if($("input[name='storeName']").val() == null){
					var all= "";
				}else{
					var all= $("input[name='storeName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".storeId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('客户【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					all =all +','+ rows[i].name;
					mhtml = '<div><input name="storeId" class="text storeId storeId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
					$(".store").append(mhtml);
				}
				$("input[name='storeName']").attr("value",all);
			}
		}
	});
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('产品【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					allName =allName +','+ rows[i].name  
					vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="closePro(this)"></i></div>'
					$(".product").append(vhtml);
				}
				$("input[name='productName']").attr("value",allName)
			}
		}
	});
});
//条件导出		    
function segmentedExport(e){
	var needConditions = false;//
	var page_url = 'to_condition_export.jhtml';//分页导出统计页面
	var url = 'condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的单！")}';//提示
	var url = 'selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}
function planApplyImport(e){	
	excel_import(e,{
		title:"${message("导入")}",
		url:"/b2b/planApply/import_excel.jhtml",
		template:"/resources/template/order/plan_apply.xls",
		callback:function(){
			$("#searchBtn").click();
		}
	})
}

function closePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".product > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='productName']").attr("value",allName2)
}
function newclosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".store > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='storeName']").attr("value",allName2)
}
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
	<input type="hidden" name="isCheck" id="isCheck" value="${isCheck}" >
		<div class="bar">
			<div class="buttonWrap">
			[#if isCheck != 1]
				<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			[/#if]	
			<div class="flag-wrap flagImp-wrap">
						<a href="javascript:void(0);" class="iconButton" id="export1Button">
							<span class="impIcon">&nbsp;</span>导入导出
						</a>
						<ul class="flag-list">
						    <li><a href="javascript:void(0)" onclick="planApplyImport(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>
							<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
							<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>

						</ul>
			</div>
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>	
					<dt><p>${message("要货单号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="sn" value =""/>
        			</dd>
        		</dl>
        		<dl>	
					<dt><p>${message("机构")}：</p></dt>
        			<dd>
        				<span class="search" style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" value=""/>
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="[#if store??]${store.saleOrg.name}[#else]${saleOrg.name}[/#if]" />
							<input type="button" class="iconSearch" value="" id="selectSaleOrg">
						</span>
        			</dd>
        		</dl>
        		<dl>	
					<dt><p>${message("要货月份")}：</p></dt>
         			<dd>
         				<input id="month" name="month" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM',startDate:'%y-%M'});" type="text" btn-fun="clear"/> -->
         			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("创建人")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="creator" value =""/>
        			</dd>
        		</dl>
        		<dl>
						<dt><p>${message("客户名称")}:</p></dt>
	    				<dd>
	    					<span style="position:relative">
								<input class="text storeName" maxlength="200" type="text" name="storeName" value="" onkeyup="clearSelect(this)">
								<input type="button" class="iconSearch" value="" id="selectStore">
								<div class="pupTitleName  store"></div>
							</span>
	    	</dd></dl>
	    	<dl><dt><p>${message("产品名称")}:</p></dt>
	    				<dd>
	    					<span style="position:relative">
								<input class="text productName" maxlength="200" type="text" name="productName" value="" onkeyup="clearSelect(this)" readonly>
								<input type="button" class="iconSearch" value="" id="selectProduct">
								<div class="pupTitleName product"></div>
							</span>
	    	</dd></dl>
	    	<dl>
			<dt><p>${message("SBU")}:</p></dt>
			<dd>
				<span class="search" style="position:relative">
				<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" />
				<input type="text" name="sbuName" class="text sbuName" maxlength="200" onkeyup="clearSelect(this)" id="sbuName" />
				<input type="button" class="iconSearch" value="" id="selectSbu">
				</span>
			</dd>
			</dl>
			</div>
		<div class="search-btn" style="height:32px"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
			
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>