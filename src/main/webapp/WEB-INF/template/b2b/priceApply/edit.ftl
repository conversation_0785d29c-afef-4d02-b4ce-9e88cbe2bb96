<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑特价单申请")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript">
function editQty(t,e){
	if(extractNumber(t,3,false,e)){
		getItemPrice(t);
		countTotal();
	}
}
function editPrice(t,e){
	if(extractNumber(t,2,false,e)){
		getdiscount(t);
		countTotal();
	}
}
function countTotal(){
	var $input = $("input.quantity");
	var total = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var $priceApply = $tr.find(".priceApply").val();
		var amount = 0;
		if( $priceApply  != 0 && $this.val() != 0){
			amount = Number($this.val())* $priceApply ;
		}
		if(isNaN(amount)){
			amount = 0;
		}
		total = total+amount;
	});
	$(".totalAmount").text(currency(total,true));
}
$().ready(function() {
	var $deleteProduct = $("a.deleteProduct");
	var $selectMember = $("#selectMember");
	var itemIndex = ${priceApply.priceApplyItems?size}
	var $addProduct = $("#addProduct");
	
	[#if priceApply.wfId!=null]
	 $("#wf_area").load("/wf/wf.jhtml?wfid=${priceApply.wfId}");
	[/#if]
	
    $.validator.addClassRules({
		price: {
			required: true
		},
		quantity: {
			required: true
		},
		/*startDate: {
			required: true
		},
		endDate: {
			required: true
		},*/
		storeId: {
			required: true
		},
		storeName: {
			required: true
		}
	});
	//打开选择产品界面
	$addProduct.click(function(){
	var $storeId = $(".storeId").val();
	var type = $("#type option:selected").val();;
	var proGrade = 1;
	if (type == 1) {
		 proGrade = 2;
	}
		if($storeId==""){
			$.message_alert('${message("请选择客户")}');
		}else{
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品',
			url:'/product/product/selectProduct.jhtml?multi=2&storeId='+$storeId+'&productGrade='+proGrade,
			callback:function(rows){
				if(rows.length>0){
					var error = '';
// 					for (var i = 0; i < rows.length;i++) {
// 						var idH = $(".productId_"+rows[i].id).length;
// 						if(idH > 0){
// 							$.message_alert('产品【'+rows[i].name+'】已添加');
// 							return false;
// 						}
// 					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						var shippingWarehouseId='';
						[#if shippingWarehouses?? && shippingWarehouses[0].id??]
						shippingWarehouseId=${shippingWarehouses[0].id};
						[/#if]
						row.str=row.product_category_id+'_'+row.id+'_'+shippingWarehouseId;
						$mmGrid.addRow(row,null,1);
					}
				}
			}
		});
		}	
	})
	
	//打开选择产品系列界面
	$("#addProductCategory").click(function(){	
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品系列',
			url:'/product/product_category/select_productCategory.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var error = '';
// 					for (var i = 0; i < rows.length;i++) {
// 						var idH = $(".productId_"+rows[i].id).length;
// 						if(idH > 0){
// 							$.message_alert('产品【'+rows[i].name+'】已添加');
// 							return false;
// 						}
// 					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						row.product_category_id=row.id;
						row.product_category_name=row.name;
						row.id=null;
						row.name='';
						var shippingWarehouseId='';
						[#if shippingWarehouses?? && shippingWarehouses[0].id??]
						shippingWarehouseId=${shippingWarehouses[0].id};
						[/#if]
						row.str=row.product_category_id+'_0_'+shippingWarehouseId;
						$mmGrid.addRow(row,null,1);
					}
				}
			}
		});
	})
	
	var items = ${jsonStr};
	var cols = [	
		{ title:'${message("产品系列")}', align:'center',width:160 , renderer: function(val,item,rowIndex){
			var id=0;
			if(item.id!=null){
				id=item.id;
			}
			if(item.str==undefined){
				var shippingWarehouseId=item.shipping_warehouse==null?'':item.shipping_warehouse;
				item.str=item.product_category_id+'_'+id+'_'+shippingWarehouseId;
			}
			return '<input type="hidden" shippingWarehouse_id="0" str="'+item.str+'" name="priceApplyItems['+itemIndex+'].productCategory.id" class="productCategoryId productId_'+item.product_category_id+' productId" product_id="'+id+'" product_category_id="'+item.product_category_id+'" value="'+item.product_category_id+'">'+item.product_category_name;
		}},
		{ title:'${message("产品名称")}', align:'center',width:160 , renderer: function(val,item,rowIndex,obj){
			
			var name='';
			if(item.name!=null){
				name=item.name;
			}
			var id=0;
			if(item.id!=null){
				id=item.id;
			}
			
			if(obj==undefined){
				return '<input type="hidden" name="priceApplyItems['+itemIndex+'].product.id" value="'+id+'" class="productId_'+id+'"><input type="hidden" name="priceApplyItems['+itemIndex+'].id" value="'+item.item_id+'">'+name;
			}else{
				return '<input type="hidden" name="priceApplyItems['+itemIndex+'].product.id" value="'+id+'" class="productId_'+id+'"><input type="hidden" name="priceApplyItems['+itemIndex+'].id" value="">'+item.name;
			}
		}},
		{ title:'${message("产品型号")}', name:'model', align:'center',width:160 },
		{ title:'${message("12211")}', name:'vonder_code', align:'center',width:160 },
		{ title:'${message("产品描述")}', name:'description', align:'center',width:160 },
		{ title:'${message("产品等级")}',name:'product_grade',align:'center', renderer: function(val,item,rowIndex){
			if(val == 2 ||($("#type").val()==1)){
			return '<span class="gradeName">二等品</span><input type="hidden" name="priceApplyItems['+itemIndex+'].productGrade" class="grade" value="2">';
			}else{
			return '<span class="gradeName">优等品</span><input type="hidden" name="priceApplyItems['+itemIndex+'].productGrade" class="grade" value="1">';
			}
		}},
		{ title:'${message("销售价")}', name:'price' ,align:'center', width:90,renderer: function(val){
			if(val!=undefined && val!=null && val!=''){
				return '<span class="red">'+currency(val,true)+'</span><input type="hidden" name="priceApplyItems['+itemIndex+'].memberPrice" value="'+val+'">';
			}
			else{
				return '-';
			}
		}},
		{ title:'${message("申请价格")}',name:'item_price', align:'center',renderer:function(val,item,rowIndex,obj){
			var price = '';
			if(obj==undefined){
				price = item.item_price;
			}
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price priceApply"  name="priceApplyItems['+itemIndex+'].price" value="'+price+'" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
        	return html;
			//return '<input type="number" step="0.01" min=0 oninput="getdiscount(this)" name="priceApplyItems['+itemIndex+'].price" value="'+price+'" class="text price priceApply">';
		}},
// 		{ title:'${message("折扣")}',name:'discount', align:'center',renderer:function(val,item,rowIndex,obj){
// 			if (val == '' || val == null) {
// 				val == 0;
// 			}
// 			var discount = currency(val * 10);
// 			return '<span class="discount">'+discount+'</span>';
// 		}},
		{ title:'${message("最小开单数量/m2")}',name:'min_quantity',align:'center',renderer:function(val,item,rowIndex,obj){
			var quantity = 1;
			if(obj==undefined){
				quantity = val;
			}
			var html = '<div class="nums-input ov">'+
        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editQty(this.nextSibling,event)">'+
        	'<input type="text"  class="t quantity"  name="priceApplyItems['+itemIndex+'].minQuantity" value="'+quantity+'" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
    	'</div>';
    	return html;
			
		}},
		{ title:'${message("最大累计数量/m2")}',name:'max_quantity',align:'center',renderer:function(val,item,rowIndex,obj){
			var quantity = 1;
			if(obj==undefined){
				quantity = val;
			}
			var html = '<div class="nums-input ov">'+
        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editQty(this.nextSibling,event)">'+
        	'<input type="text"  class="t quantity"  name="priceApplyItems['+itemIndex+'].maxQuantity" value="'+quantity+'" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
    	'</div>';
    	return html;
		}},
// 		{ title:'${message("申请数量")}', name:'quantity', align:'center',renderer: function(val,item,rowIndex,obj){
// 			var html = '<div class="nums-input ov">'+
//             	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
//             	'<input type="text"  class="t quantity"  name="priceApplyItems['+itemIndex+'].quantity" value="'+val+'" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
//             	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
//         	'</div>';
//         	return html;
// 			//return '<input type="number" step="0.01" name="priceApplyItems['+itemIndex+'].quantity" class="text number" value="'+val+'">';
// 		}},
// 		{ title:'${message("已使用数量")}',align:'center', renderer:function(val,item,rowIndex,obj){
// 			var used_quantity = 0;
// 			if(obj==undefined){
// 				used_quantity = item.used_quantity;
// 			}
// 			return '<span>'+used_quantity+'</span>';
// 		}},
// 		{ title:'${message("金额")}',align:'center', renderer:function(val,item,rowIndex,obj){
// 			if (val == '' || val == null) {
// 				val == 0;
// 			}
// 			var item_prices = item.item_prices;
// 			if(item_prices==undefined){
// 				item_prices = 0;
// 			}
// 			return '<span class="red item_prices">'+currency(item_prices,true)+'</span>';
// 		}},
// 		{ title:'${message("开始时间")}', name:'start_date', align:'center',renderer: function(val,item,rowIndex,obj){
// 			var start_date = '';
// 			if(obj==undefined){
// 				if(item.start_date != "1970-01-01 00:00:00"){
// 				start_date = item.start_date;
// 					if(start_date!=null && start_date.length>10){
// 						start_date = start_date.substring(0,10);
// 					}
// 				}
// 			}
// 			return '<input id="priceApplyItems['+itemIndex+'].startDate" class="text startDate" name="priceApplyItems['+itemIndex+'].startDate" class="text endDate" value="'+start_date+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\',maxDate: \'#F{$dp.$D(\\\''+'priceApplyItems['+itemIndex+'].endDate\\\')}\'});" type="text" btn-fun="clear"/>'
// 		}},
// 		{ title:'${message("结束时间")}', name:'end_date', align:'center',renderer: function(val,item,rowIndex,obj){
// 			var end_date = '';
// 			if(obj==undefined){
// 				if(item.end_date != "2099-12-31 23:59:59"){
// 					end_date = item.end_date;
// 					if(end_date!=null && end_date.length>10){
// 						end_date = end_date.substring(0,10);
// 					}
// 				}
// 			}
// 			return '<input id="priceApplyItems['+itemIndex+'].endDate" name="priceApplyItems['+itemIndex+'].endDate" class="text endDate" value="'+end_date+'" onfocus="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\',minDate: \'#F{$dp.$D(\\\''+'priceApplyItems['+itemIndex+'].startDate\\\')}\'});" type="text" btn-fun="clear"/>'
// 		}},		
		{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
			itemIndex++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteProduct(this)">删除</a>';
		}},
	];
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols,
        items:items,
        fullWidthRows:true,
        checkCol: false,
        autoLoad: true
    });
    
    var orderFullLink_items = ${fullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
    
	$(".shippingWarehouseId").live("change", function() {
		var $tr=$(this).closest("tr");
		var $productCategoryId=$tr.find(".productCategoryId");
		var productCategoryId=$productCategoryId.attr("product_category_id");
		var productId=$productCategoryId.attr("product_id");
		var shippingWarehouseId=$(this).val();
		$productCategoryId.attr("str",productCategoryId+"_"+productId+"_"+shippingWarehouseId);
	});
    
    $("#submit_button").click(function(){
		var count = $("input.productId").length;
		if(count<1){
			$.message_alert("特价明细不能少于一条");
		}else{
			var count=[0];
			var sub=true;
			$(".productCategoryId").each(function(){
				var required=$(this).attr("str");
				if($.inArray(required,count)==-1){
					count.push(required);
				}else{
					$.message_alert("已存在信息相同的特价明细");
					sub=false;
				}
				
			});
			$("form").valid();
			if(sub){
				$("form").submit();
			}
		}
	});
	
    $("form").bindAttribute({
    	isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	 });
	 //查询客户
	 $("#selectStore").click(function(){
    	var saleOrgId = $(".saleOrgId").val();
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			bindClick:false,
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1&',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$(".storeName").val(row.name);
					$(".storeId").val(row.id);
					$(".engineeringName").val('');
					$(".engineeringId").val('');
					$(".saleOrgName").text(row.sale_org_name);
					$(".saleOrgId").val(row.sale_org_id);
				}
			}
		});
	});
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
	
	//打开选择工程界面
	$("#selectEngineering").click(function(){	
		var $storeId = $(".storeId").val();
		if($storeId==""){
			$.message_alert('${message("请选择客户")}');
		}else{
		$("#selectEngineering").bindQueryBtn({
			type:'engineering',
			bindClick:false,
			title:'${message("查询工程")}',
			url:'/basic/engineering/select_list.jhtml?storeId='+$storeId,
		});
		}
	})
	
	$("#type").change(function(){
		var type=$("#type").val();
		if(type==1){ //二等品
			$("input.grade").val(2);
			$("span.gradeName").html("二等品");
		}else{
			$("input.grade").val(1);
			$("span.gradeName").html("优等品");
		}
		
		if(type==3){
			$("#selectEngineering").removeAttr("disabled");
			$(".engineeringId").removeAttr("disabled");
			$(".engineeringName").removeAttr("disabled");
		}else{
			$("#selectEngineering").attr("disabled","disabled");
			$(".engineeringId").attr("disabled","disabled");
			$(".engineeringName").attr("disabled","disabled");
			$(".engineeringId").val('');
			$(".engineeringName").val('');
		}
	});
	
	/**初始化附件*/
    var twContractAttach_items = ${twContractAttach_json};
    var attachIdnex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
			if(obj==undefined){
				var url = item.file_url;
				var fileObj = getfileObj(item.file_name , item.name, item.suffix);
				
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['twContractAttachs['+attachIdnex+'].id']=item.id;
				hideValues['twContractAttachs['+attachIdnex+'].fileUrl']=url;
				hideValues['twContractAttachs['+attachIdnex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'twContractAttachs['+attachIdnex+'].name',
					hideValues: hideValues
				});
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['twContractAttachs['+attachIdnex+'].fileUrl']=url;
				hideValues['twContractAttachs['+attachIdnex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'twContractAttachs['+attachIdnex+'].name',
					hideValues:hideValues
				});
				
			}
			
        
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text file_memo" name="twContractAttachs['+attachIdnex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			attachIdnex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:twContractAttach_items,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row=data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
        var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
});
function deleteProduct(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
		countTotal();
	})
}

function add(wf_index,url){
	location.href=url;
}

function subflag(){
$("#isSubmit").val(1);
}

function check(t,flag){
	var str='您确定要审核吗';
	if(flag==0){
		str='<div>您确定要&nbsp;&nbsp;<b>作废</b>&nbsp;&nbsp;当前特价单吗？<br/>确定:将失效当前特价单下所有的产品特价。<br/>取消:不做任何处理。</div>'
	}
	ajaxSubmit(t,{
		 url: '/b2b/priceApply/check.jhtml?id=${priceApply.id}&flag='+flag,
		 method: "post",
		 isConfirm:true,
		 confirmText : str,
		 callback:function(resultMsg){
		 	$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			});
		 }
	})
}

function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
			var url="/b2b/priceApply/check_wf.jhtml";
			var data = $form.serialize();
			
			ajaxSubmit(e,{
				 url: '/b2b/priceApply/get_config.jhtml?obj_type_id=42&id=${priceApply.id}&objid=${priceApply.id}',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 	if(rows.length==1){
				 		data = data+'&objConfId='+rows[0].id;
				 		ajaxSubmit('',{
							 url: url,
							 method: "post",
							 data: data,
							 callback:function(resultMsg){
								$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
									reflush_wf();
								});
							 }
						})
				 	}else{
				 		var str = '';
					 	for(var i=0;i<rows.length;i++){
					 		var row = rows[i];
					 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					 	}
					 	var content = '<table class="input input-edit" style="width:100%">'
								+'<tbody><tr><th>流程模版</th>'
								+'<td>'
									+'<select class="text" id="objConfId">'
										+str
									+'</select>'
								+'</td>'
							+'</tr></tbody></table>';
						var $dialog_check = $.dialog({
							title:"特价单审核",
							height:'135',
							content: content,
							onOk:function(){
								var objConfId = $("#objConfId").val();
								if(objConfId=='' || objConfId == null){
									$.message_alert("请选择特价单模版");
									return false;
								}
								data = data+'&objConfId='+objConfId;
								
								ajaxSubmit($this,{
									 url: url,
									 method: "post",
									 data: data,
									 callback:function(resultMsg){
										$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
											reflush_wf();
										});
									 }
								})
								
							}
						});
				 	}
				 	
					var h = 150;
					$dialog_check.css("top",h+"px");
				 }
			
		});
	}
}
function getdiscount(e){
	var priceApply=$(e).val();
	var $tr=$(e).closest("tr");
	var productPrice=$tr.find(".productPrice").val();
	var quantity=$tr.find(".quantity").val();
	var num=0.00;
	var itemPrices = 0;
	if(productPrice=="0"){
		num=0.00;
	}else if(priceApply!=""&&priceApply>0){
		num =(priceApply/productPrice)*10;
	}
	$tr.find(".discount").html(currency(num));
	
	if(priceApply != "" && priceApply>0 && quantity != "" && quantity >0){
		itemPrices = priceApply * quantity;
	}
	$tr.find(".item_prices").html(currency(itemPrices,true));
}   
function getItemPrice(e){
	var quantity=$(e).val();
	var $tr=$(e).closest("tr");
	var priceApply=$tr.find(".priceApply").val();
	var itemPrices = 0;
	if(priceApply != "" && priceApply>0 && quantity != "" && quantity >0){
		itemPrices = priceApply * quantity;
	}
	$tr.find(".item_prices").html(currency(itemPrices,true));
}   


</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看特价单")}
	</div>
	<form id="inputForm" action="/b2b/priceApply/save.jhtml" method="post" type="ajax" validate-type="validate" enctype="multipart/form-data">
		<input type="hidden" name="id" id="pid" value="${priceApply.id}" />
		<input type="hidden" name="isSubmit" id="isSubmit" value="" />
		 <div class="tabContent order-info">
		<table class="input input-edit">
			<tr>
				<th>
					${message("特价单编号")}:
				</th>
				<td>
					${priceApply.sn}
				</td>
				<th>${message("客户")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="store.id" class="text storeId" value="${priceApply.store.id}" btn-fun="clear"/>
					<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" value="${priceApply.store.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore">
					</span>
				</td>
				<th>${message("单据状态")}:</th>
				<td>
					[#if priceApply.docStatus == 0]<b class="blue">${message("已保存")}</b>[/#if]
					[#if priceApply.docStatus == 1]<b class="blue">${message("处理中")}</b>[/#if]
					[#if priceApply.docStatus == 2]<b class="green">${message("已处理")}</b>[/#if]
					[#if priceApply.docStatus == 3]<b class="red">${message("已失效")}</b>[/#if]
				</td>
				<th>${message("流程状态")}:</th>
				<td>
					[#if priceApply.wfState??]
					${message("22222222"+priceApply.wfState)}
					[/#if]
				</td>
				
				<!--<th>
					${message("特价单状态")}:
				</th>
				<td>
					[#if priceApply.status == "saved"]${message("17012")}[/#if]
        			[#if priceApply.status == "submitted"]${message("17013")}[/#if]
        			[#if priceApply.status == "audited"]${message("17014")}[/#if]
        			[#if priceApply.status == "rejected"]${message("17015")}[/#if]
        			[#if priceApply.status == "closed"]${message("17016")}[/#if]
				</td>-->
			</tr>
			<tr>
				<th>
					${message("特价类型")}:
				</th>
				<td >
					<select id="type" class="text" name="type"> 
						<option value="0" [#if priceApply.type==0]selected[/#if]>促销</option>
						<option value="1" [#if priceApply.type==1]selected[/#if]>二等品</option>
						<option value="2" [#if priceApply.type==2]selected[/#if]>定制</option>
						<option value="3" [#if priceApply.type==3]selected[/#if]>工程</option>
					</select>
				</td>
				<th>
					${message("工程")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="engineering.id" class="text engineeringId" value="${priceApply.engineering.id}" btn-fun="clear"/>
					<input type="text" name="engineeringName" class="text engineeringName" value="${priceApply.engineering.name}" maxlength="200" onkeyup="clearSelect(this)" [#if priceApply.type!=3]disabled[/#if]  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectEngineering" [#if priceApply.type!=3]disabled[/#if]>
					</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("开始时间")}:
				</th>
				<td id="start">
					<input type="text" class="text startDate" name="startDate" value="${priceApply.startDate?string("yyyy-MM-dd")}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear"/>
				</td>
				
				<th>
					<span class="requiredField">*</span>${message("结束时间")}:
				</th>
				<td id="end">
					<input type="text" class="text endDate" name="endDate" value="${priceApply.endDate?string("yyyy-MM-dd")}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear"/>
				</td>
				<!-- <th>
					${message("总金额")}:
				</th>
				<td>
					<span class="red totalAmount">${currency(priceApply.totalAmount,true)}</span>
				</td> -->
			</tr>
			<tr>
				<th>${message("机构")}:</th>
				<td>
					<input type="hidden" name="saleOrg.id" class="text saleOrgId" btn-fun="clear" value="${priceApply.saleOrg.id}"/>
					<span class="saleOrgName">${priceApply.saleOrg.name}</span>
					
				</td>
				<th>
					<span class="requiredField">*</span>${message("申请人")}:
				</th>
				<td>
					<span class="search" style="position:relative">
                    <input type="hidden" name="storeMemberId" class="text storeMemberId" value="${priceApply.storeMember.id}" btn-fun="clear"/>
                    <input type="text" name="storeMemberName" class="text storeMemberName"  value="${priceApply.storeMember.name}" maxlength="200"  readOnly/>
                    </span>
				</td>
				<th>
					${message("1011")}:
				</th>
				<td>
					${priceApply.createDate?string("yyyy-MM-dd HH:mm:ss")}
				</td>
				<td colspan="2"></td>
			</tr>
			<tr>
				<th>
					${message("申请说明")}:
				</th>
				<td colspan="7"><textarea name="memo" class="text" id="memo">${priceApply.memo}</textarea></td>
			</tr>
			
			<tr class="border-L1">
				<th> ${message("特价单明细")}:
				</th>
				<td colspan="7">
					[#if priceApply.docStatus==0]
						<a href="javascript:;" id="addProductCategory" class="button">选择产品系列</a>
						<a href="javascript:;" id="addProduct" class="button">选择产品</a>
					[/#if]
				</td>
			</tr>
			 <tr class="border-L1">
            	<td colspan="8">
            		<div>
						<table id="table-m1"></table>
					</div>
            	</td>
            </tr>
            <tr class="s-tr"><td colspan="8"></td></tr>
			<tr class="border-L1">
				<th>${message("附件信息")}:</th>
				<td colspan="7"><a href="javascript:;" id="addAttach" class="button">添加附件</a></td>
			</tr>
			<tr class="border-L1">
				<td colspan="8">
					<div  class="w_1135">
					<table id="table-attach"></table>
					</div>
				</td>
			</tr>
			<tr class="border-L1">
				<td colspan="8">
					<div>
						<table id="table-m1"></table>
					</div>
				</td>
			</tr>
			<tr class="border-L2">
				<th>${message("全链路信息")}:</th>
				<td colspan="7"></td>
			</tr>
			<tr class="border-L2">
				<td colspan="8">
					<table id="table-full"></table>
				</td>
			</tr>
			
		</table>
		</div>
	<div class="fixed-top">
	[#if isCheck == 1]
		<a href="javascript:void(0);" onclick="add(2,'/b2b/priceApply/add/${code}.jhtml')" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
		[#if priceApply.docStatus==0 && priceApply.wfId==null]
		<input type="button" id="submit_button" class="button sureButton" value="${message("保存")}">
		[/#if]
		[#if priceApply.docStatus ==0]
			[#if isCheckWf]	
				[#if order.wfId==null]
				<a href="javascript:void(0);" class="button sureButton" onclick="check_wf()">${message("12501")}</a>
				[/#if]
			[#else]
				<a id="shengheButton" class="iconButton" onclick="check(this,1)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			[/#if]
		[/#if]
		[#if (priceApply.docStatus ==0 && priceApply.wfId==null)||priceApply.docStatus ==2]
			<a href="javascript:void(0);" class="button cancleButton" onclick="check(this,0)">${message("作废")}</a>
		[/#if]
		[/#if]
		<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
	</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>