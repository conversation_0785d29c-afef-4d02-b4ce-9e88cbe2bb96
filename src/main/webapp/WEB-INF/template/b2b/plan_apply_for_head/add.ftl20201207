<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增计划提报")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<style type="text/css">
	tr.s-tr,tr.s-tr td{height:10px !important;}
</style>
<script type="text/javascript">

	function setDate(){
		var lineDefaultDate=$("input[name='lineDefaultdate']").val();
	   	var first = $("input.startDate").eq(0);
	   	first.val(lineDefaultDate);
	}
	

	function editQty(t,e,n){
		var rate = 6;
		if(n!=undefined)rate = n;
		if(extractNumber(t,rate,true,e)){
			if($(t).hasClass("adjustQuantity")){
				editLineQuantity(t);
			}
			editTotal();
		}
	}


	function editLineQuantity(t){
		var $this = $(t);
		var $tr = $this.closest("tr");
		var quantity = Number($tr.find(".quantity").val());
		var adjustQuantity = $tr.find(".adjustQuantity").val();
		if(isNaN(adjustQuantity)){
			adjustQuantity = 0;
		}
		var lineQuantity = accAdd(quantity,adjustQuantity);
		if(isNaN(lineQuantity)){
				lineQuantity = 0;
			}
		$tr.find(".lineQuantity").text(lineQuantity);
	}

	function editTotal(){
		var total = 0;
		$("input.quantity").each(function(){
			var $this = $(this);
			var quantity = $this.val();
			total = accAdd(total,quantity);
		});
		$("#totalQ").text(total);
		$("#totalQuantity").val(total);
	}

	function setSbu(){
		$("input.quantity").each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			var productId = $tr.find(".product_id").val();
			var sbu = $tr.find(".sbu").val();
			if(!isEmpty(productId) && isEmpty(sbu)){
				$.ajax({
					url:'/basic/sbu/select_sbu_by_product_id.jhtml',
					async: true,
					type: "post",
					data:{productId:productId},
					success:function(resultMsg) {
						var sbus = resultMsg.objx;
						var html = '<option value="">请选择</option>';
						for(var i=0;i<sbus.length;i++){
							html += '<option value="'+sbus[i].id+'" ';
							if(sbus[i].is_default == true){
								html+=' selected ';
							};
							html += '> '+sbus[i].name+'</option>';
						};
						$tr.find(".sbu").html(html);
					}
				})
			}
		});
	}

	$().ready(function() {
		var $deleteProduct = $("a.deleteProduct");
		var $addProduct = $("#addProduct");
		var $addProductDict = $("#addProductDict");
		$("form").validate({
			rules: {needDate: "required"}
		});
		var itemIndex = 0;
		var cols = [
			{ title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
				return '<span class="line_no"></span>';
			}},
			{ title:'${message("产品编码")}', name:'vonder_code' , align:'center',width:130,renderer: function(val,item,rowIndex,obj){
				var html='';
				if(!isEmpty(item.id)){
					html += '<input type="hidden"  class="product_id" name="planApplyItems['+itemIndex+'].product.id" value="'+item.id+'">'+val;
				}
			    return html;
			}},
			{ title:'${message("产品描述")}', align:'center', width:250, name:'description',renderer: function(val,item,rowIndex,obj){
		 		var str = '';
	        	if(!isEmpty(item.id)){
	        		str = 'readonly="readonly"';
	        	}
	        	return '<input type="text" name="planApplyItems['+itemIndex+'].description" class="text" value="'+val+'" '+str+' />';
		 	}},
		 	{ title:'${message("产品名称")}', name:'name' ,align:'center', width:150, renderer: function(val,item,rowIndex,obj){
		 		if(!isEmpty(item.id)){
		 			return val;
		 		}else{
		 			var html = '<span class="search" style="position:relative">'
	        			+'<input type="hidden" name="planApplyItems['+itemIndex+'].productName.id" class="text productName_id" value=""/>'
	        			+'<input type="text" class="text productName_value" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>'
	        			+'<input type="button" class="iconSearch" value="" onclick="productDict(this,1)"/>'
	        		+'</span>';
	        	 return html;
		 		}
     		}},
     		{ title:'${message("木种/花色")}', name:'wood_type_or_color' ,align:'center', width:150, renderer: function(val,item,rowIndex,obj){
		 		if(!isEmpty(item.id)){
		 			return val;
		 		}else{
		 			var html = '<span class="search" style="position:relative">'
	        			+'<input type="hidden" name="planApplyItems['+itemIndex+'].woodTypeOrColor.id" class="text woodTypeOrColor_id" value=""/>'
	        			+'<input type="text" class="text woodTypeOrColor_value" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>'
	        			+'<input type="button" class="iconSearch" value="" onclick="productDict(this,2)"/>'
	        		+'</span>';
		 			 return html;
		 		}
     		}},
     		{ title:'${message("型号")}', name:'model' ,align:'center', width:120, renderer: function(val,item,rowIndex,obj){
		 		if(!isEmpty(item.id)){
		 			return val;
		 		}else{
		 			var html = '<span class="search" style="position:relative">'
	        			+'<input type="hidden" name="planApplyItems['+itemIndex+'].model.id" class="text model_id" value=""/>'
	        			+'<input type="text" class="text model_value" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>'
	        			+'<input type="button" class="iconSearch" value="" onclick="productDict(this,3)"/>'
	        		+'</span>';
    				return html;
		 		}
     		}},
     		{ title:'${message("规格")}', name:'spec' ,align:'center', width:120, renderer: function(val,item,rowIndex,obj){
		 		if(!isEmpty(item.id)){
		 			return val;
		 		}else{
		 			var html = '<span class="search" style="position:relative">'
	        			+'<input type="hidden" name="planApplyItems['+itemIndex+'].spec.id" class="text spec_id" value=""/>'
	        			+'<input type="text" class="text spec_value" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>'
	        			+'<input type="button" class="iconSearch" value="" onclick="productDict(this,4)"/>'
	        		+'</span>';
    				return html;
		 		}
     		}},
     		{ title:'${message("含水率")}', align:'center', width:110, renderer: function(val,item,rowIndex,obj){
	 			var html='<select name="planApplyItems['+itemIndex+'].moistureContent.id" class="text moistureContent" >';
					[#list moistureContentList as moistureContent]
	    					html+='<option value="${moistureContent.id}">${moistureContent.value}</option> ';
					[/#list]
					html+='</select>';
				return html;
 			}},
     		{ title:'${message("业务类型")}', align:'center', width:110, renderer: function(val,item,rowIndex,obj){
		 			var html='<select name="planApplyItems['+itemIndex+'].businessType.id" class="text businessType" >';
	    				[#list businessTypeList as businessType]
		    					html+='<option value="${businessType.id}">${businessType.value}</option> ';
	    				[/#list]
	    				html+='</select>';
	    			return html;
     		}},
     		{ title:'${message("sbu")}', align:'center', width:100, renderer: function(val,item,rowIndex,obj){
     			var str = 'selected="selected"';
	 			var html='<select id="sbuId" name="planApplyItems['+itemIndex+'].sbu.id" class="text sbu" >';
	 			html+='<option value="">请选择</option>';
	 			if(isEmpty(item.id)){
	 				[#list sbuList as sbu]
		 				if(${sbu.isDefault?string ("true","false")}){
		   					html+='<option value="${sbu.id}" '+str+' >${sbu.name}</option>';
		   				}else{
		   					html+='<option value="${sbu.id}">${sbu.name}</option> ';
		   				}
					[/#list]
	 			}
				html+='</select>';
				return html;
 			}},
			{ title:'${message("工厂")}', name:'factory', align:'center', width:160, renderer:function(val,item,rowIndex,obj){
				val = $("#mainFactoryId").val();
				var str='selected="selected"';
    			var html='<select name="planApplyItems['+itemIndex+'].factory.id" class="text factory" >';
    				html+='<option value="">请选择</option>';
    				[#list factoryList as factory]
	    				if(${factory.id}==val){
	    					html+='<option value="${factory.id}" '+str+' >${factory.value}</option> ';
	    				}else{
	    					html+='<option value="${factory.id}">${factory.value}</option> ';
	    				}
    				[/#list]
    				html+='</select>';
    			return html;
			}},
			{ title:'${message("要货平方数")}', name:'quantity' , align:'center', renderer:function(val,item,rowIndex,obj){
				var html = '<div class="nums-input ov">'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)" >'+
						'<input type="text"  class="t quantity"  name="planApplyItems['+itemIndex+'].quantity" value="'+item.quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)" >'+
						'</div>';
				return html;
			}},
			{ title:'${message("要货日期")}', name:'', align:'center',renderer: function(val,item,rowIndex){
				return '<input  id="planApplyItems['+itemIndex+'].needDate" name="planApplyItems['+itemIndex+'].needDate" class="text startDate" value="'+item.need_date+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\'});" type="text" btn-fun="clear" />'
			}},
			{ title:'${message("类型")}', name:'type' , align:'center',width:80,renderer: function(val,item,rowIndex){
					var text = '';
					if(val==0){
						text = "来自经销商";
					} else if(val == 1){
						text = "来自平台";
					} else if(val == 2){
						text = "来自总部";
					} 
					return '<input type="hidden"  name="planApplyItems['+itemIndex+'].type" value="'+val+'"/>'+text;;
			}},
			{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
				itemIndex++;
				return '<a href="javascript:;" class="btn-delete" onclick="deleteProduct(this)">删除</a>';
				
			}}
		];
		
		$mmGrid = $('#table-m1').mmGrid({
			height:'auto',
			cols: cols,
			autoLoad: true,
			fullWidthRows:true,
			checkCol: false,
			callback:function(){
	        	editTotal();
	         }
		});
		
		$("#selectProject").click(function(){
			$("#selectProject").bindQueryBtn({
				type:'customerProject',
				title:'${message("查询经销商项目")}',
				bindClick:false,
				url:'/b2b/customerProject/select_project.jhtml',
				callback:function(rows){
					if(rows.length>0){
						var row = rows[0];
						$(".customerProjectName").val(row.project_name);
						$(".customerProjectId").val(row.id);
						$("#saleOrgName").text(row.sale_org_name);
					}
				}
			});
		})
		
		
		//打开选择产品界面
		$addProduct.click(function(){
            var sbuId = $("#sbuId option:selected").val();
			$addProduct.bindQueryBtn({
				type:'product',
				bindClick:false,
				title:'查询产品',
				url:'/product/product/selectProductPage.jhtml?multi=2&sbuId='+sbuId,
				callback:function(rows){
					if(rows.length>0){
						for (var i = 0; i < rows.length;i++) {
							var row = rows[i];
							row.type = ${planApplyType};
							$mmGrid.addRow(row,0);
	 						setDate();
		 					setSbu();
		 					lineNo();
						}
					}
				}
			});	
		})
		
		//添加产品词汇
		$addProductDict.click(function(){
			var row = {};
			row.type = ${planApplyType};
			$mmGrid.addRow(row,0);
			setDate();
			lineNo();
		})
	});

	function deleteProduct(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid.removeRow(index);
			editTotal();
			lineNo();
		})
	}
	
	function lineNo(){
		var line_number = 1;
		$("span.line_no").each(function(){
			$(this).html(line_number++);
		});
	}
	
	//主表工厂切换， 明细行的工厂跟着切换
	function renderItemFactory(e){
		$(".factory").val(e.value);
	}
	
	function save(e){
		var $form = $("#inputForm");
		if($form.valid()){
			var str = '您确定要保存吗？';
			$.message_confirm(str,function() {
				Mask();
				ajaxSubmit(e,{
					url: '/b2b/planApply/saveOrUpdate.jhtml',
					data:  $form.serialize(),
					method: "post",
					failCallback:function(resultMsg){	
						// 访问地址失败，或发生异常没有正常返回
						messageAlert(resultMsg)
						UnMask();
					},
					callback:function(resultMsg){
						location.href= '/b2b/planApplyForHead/edit.jhtml?id='+resultMsg.objx;
					}
				})
			});
		}
	}

    function changeAndClearItems(e) {
        $("#table-m1").find('tbody').empty();
    }

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增计划提报")}
	</div>
	<form id="inputForm" method="post" type="ajax" validate-type="validate">
		<input name="planApplyType"  type="hidden"  value="${planApplyType}"  />
    	<input type="hidden" name="lineDefaultdate" value="${lineDefaultdate}"/>
		<div class="tabContent">
    		<table class="input input-edit">
	    		<tr>
	    			<th>${message("要货单号")}:</th>
	    			<td></td>
	    			<th>${message("单据状态")}:</th>
					<td></td>
	    			<th><span class="requiredField">*</span>${message("要货月份")}:</th>
	    			<td>
	    				<input id="month" name="needDate" class="text" value="${needDate}"  onfocus="WdatePicker({dateFmt: 'yyyy-MM',startDate:'%y-%M'});"  type="text" btn-fun="clear" readonly/>
	    			</td>
	    		</tr>
	    		<tr>
	    			<th>${message("要货总数量(㎡)")}:</th>
	    			<td>
	    			  	<span class="red" id="totalQ">0</span>
					 	<input type="hidden" id="totalQuantity" name="totalQuantity" />
	    			</td>
	    			<th>${message("创建人")}:</th>
	    			<td></td>
	    			<th>${message("创建时间")}:</th>
	    			<td></td>
	    		</tr>
	            [#if planApplyType == 3]
		            <tr>
		                <th>${message("经销商项目")}:</th>
		                <td >
		                    <span class="search" style="position:relative">
								<input type="hidden" name="customerProjectId" class="text customerProjectId" value="" btn-fun="clear"/>
								<input type="text" name="customerProjectId" class="text customerProjectName" value="" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
								<input type="button" class="iconSearch" value="" id="selectProject">
							</span>
		                </td>
		                <th>${message("机构")}:</th>
						<td>
							<span id="saleOrgName"></span>
						</td>
		            </tr>
	            [/#if]
                <tr>
				[#if planApplyType == 2]
						<th>
							<span class="requiredField">*</span>${message("工厂")}:
						</th>
						<td>
							<select class="text" name="mainFactoryId" id="mainFactoryId" onchange="renderItemFactory(this)">
								<option value="">${message("请选择")}</option>
								[#list factoryList as factory]
									<option value="${factory.id}">${factory.value}</option>
								[/#list]
							</select>
						</td>

				[/#if]
                    <th>
                        ${message("sbu")}:
                    </th>
                    <td>
                        <select id="sbuId" name="sbuId" class="text" onchange="changeAndClearItems(this)">
                            <option value=""></option>
                            [#list sbuList as sbu]
                                <option value="${sbu.id}">${sbu.name}</option>
                            [/#list]
                        </select>
                    </td>
                </tr>
	    		<tr>
					<th>${message("备注")}:</th>
					<td colspan="7">
						<textarea class="text remark" name="remark" value=""></textarea>
					</td>
				</tr>
    	</table>
		<div class="title-style">
			${message("要货明细")}
			<div class="btns">
				<a href="javascript:;" id="addProduct" class="button">选择产品</a>
	    	</div>
	    	[#if productDictRolesCount > 0 ]
	    		<div class="btns">
					<a href="javascript:;" id="addProductDict" class="button">添加产品词汇</a>
		    	</div>
	    	[/#if]
	    </div>
	    <table id="table-m1"></table>
	</div>
	<div class="fixed-top">
		<input type="button" id="submit_button" class="button sureButton" onclick="save(this)" value='${message("保存")}'>
		<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
	</div>
	</form>
</body>
</html>