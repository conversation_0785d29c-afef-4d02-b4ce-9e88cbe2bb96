<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看关闭单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,6,false,e)
}

function editQty(t,e,n){
	if(extractNumber(t,6,false,e)){
		var branchQuantity=$(t).val();
		var $tr = $(t).closest("tr");
		var perBranch=$tr.find(".perBranch").val();
		var quantity=accMul(branchQuantity,perBranch);
    	if(isNaN(quantity)){
    		quantity = 0;
		}
    	$tr.find(".quantity").val(quantity);//数量
    	$tr.find(".quantityStr").html(quantity);
	}
}
$().ready(function() {

	var order_sn = null;
	var item_json = ${item_json};
	var orderIndex = 0;
	var order_cols = [
		{ title:'${message("产品名称")}', align:'center',name:'name'},
		{ title:'${message("木种花色")}', align:'center',name:'wood_type_or_color'},
		{ title:'${message("产品规格")}', name:'spec' ,align:'center', width:110},
		{ title:'${message("型号")}', align:'center',name:'model'},
		{ title:'${message("描述")}', align:'center',name:'description', width:210},
		{ title:'${message("产品等级")}', name:'levelName' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
 			var html='<span class="text productGradeText">'+item.levelName+'</span>'+'<input type="hidden" name="orderCloseItems['+orderIndex+'].productLevel.id" class=" text" value="'+item.level_Id+'" />';
 			return html;
		}},
		{ title:'${message("下单支数")}', align:'center',name:'branch_quantity'},
		{ title:'${message("下单数量")}', align:'center',name:'quantity'},
		{ title:'${message("单价")}', align:'center',name:'price',renderer: function(val,item,rowIndex){
			return '<span class="red">'+currency(val,true)+'</span><input type="hidden" name="orderCloseItems['+orderIndex+'].price" value="'+item.price+'">';
		}},
		{ title:'${message("关闭支数")}', align:'center',name:'',renderer: function(val,item,rowIndex){
			var quantity=0;
			[#if orderClose.status!=0]
				return item.close_branch_quantity+'<input type=hidden class="perBranch" name="orderCloseItems['+orderIndex+'].branchQuantity" value="'+item.close_branch_quantity+'" />'+
					'<input type=hidden class="perBranch" name="orderCloseItems['+orderIndex+'].perBranch" value="'+item.per_branch+'" />';
			[/#if]
			var h=item.closed_branch_quantity;
			var i=item.ship_branch_quantity;
			var j=item.ship_part_branch_quantity;
			var k=item.shipped_part_branch_quantity;
			var maxQuantity=item.branch_quantity-item.closed_branch_quantity-item.ship_branch_quantity+(item.ship_part_branch_quantity-item.shipped_part_branch_quantity);
			var per_branch='';
			if( item.per_branch == undefined ||  item.per_branch == 0){
			
				per_branch=0;
				return '-'+
				       '<input type="hidden" kid="branch" class="t branchQuantity"  name="orderCloseItems['+orderIndex+'].branchQuantity" value="0"  >'+
					   '<input type=hidden class="perBranch" name="orderCloseItems['+orderIndex+'].perBranch" value="'+per_branch+'" />';
			}else{
				return '<div class="nums-input ov">'+
				'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)" />'+
        		'<input type="text" kid="branch" class="t branchQuantity" name="orderCloseItems['+orderIndex+'].branchQuantity" value="'+item.close_branch_quantity+'" org="'+item.close_branch_quantity+'" maxData="'+maxQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" />'+
        		'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)" />'+
        		'<input type=hidden class="perBranch" name="orderCloseItems['+orderIndex+'].perBranch" value="'+item.per_branch+'" />'+
        		'</div>';
			}
			
		}},
		{ title:'${message("关闭数量")}', align:'center',name:'close_quantity',renderer: function(val,item,rowIndex){
			if( item.per_branch == undefined ||  item.per_branch == 0){
				return '<div class="nums-input ov">'+
					'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)" />'+
        			'<input type="text" class="t quantity" name="orderCloseItems['+orderIndex+'].quantity" value="'+item.closed_quantity+'" maxData="'+item.closed_quantity+'" minData="0"  oninput="editPrice (this,event)" onpropertychange="editQty(this,event)" />'+
        			'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)" />'+
        			'</div>';
			}else{
				return '<span class="quantityStr">'+val+'</span><input type="hidden" class="quantity" name="orderCloseItems['+orderIndex+'].quantity" value="'+val+'">';
			}
		}},
		{ title:'${message("发货通知支数")}', align:'center',name:'havent_ship_branch_quantity'},
		{ title:'${message("发货通知数量")}', align:'center',name:'havent_ship_quantity'},
		{ title:'${message("已发货支数")}', align:'center',name:'shipped_branch_quantity',renderer: function(val,item,rowIndex){
		return item.shipped_full_branch_quantity+item.shipped_part_branch_quantity;
		}},
		{ title:'${message("已发货数量")}', align:'center',name:'shipped_quantity',renderer: function(val,item,rowIndex){
		return item.shipped_full_quantity+item.shipped_part_quantity;
		}},
		 { title:'${message("已关闭数量")}', align:'center',name:'closed_quantity'},
		 { title:'${message("已关闭支数")}', align:'center',name:'closed_branch_quantity'},
		
		
		
		{hidden:true, renderer: function(val,item,rowIndex,obj){
			order_sn = item.sn;
			var html = '';
			var html = '<input type="hidden" name="orderCloseItems['+orderIndex+'].orderItemId" value="'+item.id+'">'+
				'<input type="hidden" name="orderCloseItems['+orderIndex+'].id" value="'+item.close_item_id+'">'+
				'<input type="hidden" name="" value="'+item.closed_branch_quantity+'">'+
				'<input type="hidden" name="orderCloseItems['+orderIndex+'].orderSn" value="'+item.order_sn+'">';
			orderIndex++;
			return html;
			
		}},
	];
	
	var $order = $('#table-order');
	$orderGrid = $order.mmGrid({
		height:'auto',
        cols: order_cols,
        items:item_json,
        checkCol: false,
        autoLoad: true,
        fullWidthRows:true,
        callback:function(){
        	//地区选择
			$("#areaId").lSelect();
			//$order.find("input").prop("disabled",true);
			//$order.find("select").prop("disabled",true);
        }
    });
 
	
	// 表单验证
	$.validator.addClassRules({
		statisticsCategoryName : {
			required: true
		}
	});
	$("form").validate({
		rules: {
			name: "required",
			order: "required",
			sql: "required"
		}
	});
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){			
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	 });
	 
	
	
});

// 审核
function check(e,status){
	var $this = $(e);
	var content = '';
	if(status==2){
		content = '您确定要作废关闭单吗？';
	}else{
		content = '您确定要审核吗？';
	}
	var id = '${orderClose.id}';
	var url="check.jhtml";
	ajaxSubmit($this,{
		 url: url,
		 method: "post",
		 data: {id:id,status:status},
		 isConfirm:true,
		 confirmText:content,
		 callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		 }
	})
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看关闭单")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${orderClose.id}">
		<input type="hidden" class="storeId" name="store.id" value="${orderClose.store.id}">
		<div class="tabContent">
			<table  class="input input-edit">
			<tr>
					<th>
						<span class="requiredField">*</span>${message("关闭单编号")}:
					</th>
					<td>
						${orderClose.sn}
					</td>
					<th>客户</th>
					<td>${orderClose.store.name}</td>
					<th>${message("状态")}:</th>
					<td>
						[#if orderClose.status==0]未审核
						[#elseif orderClose.status==1]已审核
						[#elseif orderClose.status==2]已作废
						[/#if]
						<input type="hidden" name="type" value="${orderClose.type}">
					</td>
					<th>${message("创建时间")}:</th>
					<td>${orderClose.createDate?string("yyyy-MM-dd HH:mm:ss")}</td>
				</tr>
				<tr>
					<th>
						${message("订单编号")}:
					</th>
					<td>
						${orderClose.orderSn}
						<input type="hidden" name="orderSn" value="${orderClose.orderSn}">
					</td>
					<th>
						${message("机构")}:
					</th>
					<td>
						<input type="hidden" name="saleOrgId" value="${orderClose.order.saleOrg.id}">${orderClose.order.saleOrg.name}
					</td>
					<th></th>
                    <td><input type="hidden" name="sbu.id" value="${orderClose.sbu.id}" /></td>
					<th></th>
					<td></td>
					
				</tr>
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			 <tr class="border-L1">
				<th> ${message("订单信息")}:
				</th>
				<td colspan="7">
				</td>
			</tr>
			<tr class="border-L1">
				<td colspan="8">
					<div>
						<table id="table-order"></table>
					</div>
				</td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			[#if orderClose.status==0]
     			<a href="javascript:void(0);" onclick="check(this,2)" class="button cancleButton" id="cancelButton">${message("作废关闭单")}</a>
				<a id="shengheButton" class="iconButton" onclick="check(this,1)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
				<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>