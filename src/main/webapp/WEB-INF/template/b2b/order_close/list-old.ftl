<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
    <link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
    <link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <script type="text/javascript">
        function edit(id){
            parent.change_tab(0,'edit.jhtml?id='+id);
        }
        function add(){
            parent.change_tab(0,'add.jhtml');
        }
        $().ready(function() {


            var statuss = {'0':'未审核', '1':'已审核', '2':'已作废'};
            var order_change_types = {'0':'修改', '1':'作废'};
            var cols = [
                { title:'${message("关闭单编号")}', name:'sn' , align:'center', renderer: function(val,item,rowIndex){
                        return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
                    }},
                { title:'${message("订单编号")}', name:'order_sn' ,align:'center'},
                { title:'${message("客户")}', name:'store_name' ,align:'center' },
                { title:'${message("状态")}', name:'status' ,align:'center', renderer: function(val){
                        var result = statuss[val];
                        if(result!=undefined)return result;
                    }},
                { title:'${message("创建时间")}', name:'create_date' , align:'center' },
            ];

            $mmGrid = $('#table-m1').mmGrid({
                cols: cols,
                fullWidthRows:true,
                autoLoad: true,
                url: 'list_data.jhtml',
                params:function(){
                    return $("#listForm").serializeObject();
                },
                plugins : [
                    $('#paginator').mmPaginator()
                ]
            });

            $("#selectStore").bindQueryBtn({
                type:'store',
                title:'${message("查询客户")}',
                url:'/member/store/select_store.jhtml?type=distributor&isSelect=0'
            });

        });

    </script>
</head>
<body>
<form id="listForm" action="list.jhtml" method="get">
    <div class="bar">
        <div class="buttonWrap">
        </div>
        <div class="search-btn">
            <a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
        </div>
        <div id="searchDiv">
            <div id="search-content" >
                <dl>
                    <dt><p>${message("关闭单编号")}:</p></dt>
                    <dd>
                        <input class="text" maxlength="200" type="text" name="sn" value=""  btn-fun="clear">
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("订单编号")}:</p></dt>
                    <dd>
                        <input class="text" maxlength="200" type="text" name="orderSn" value=""  btn-fun="clear">
                    </dd>
                </dl>
                <dl>
                    <dt><p>${message("客户")}:</p></dt>
                    <dd>
	    					<span style="position:relative">
								<input name="storeId" class="text storeId" type="hidden" value="">
								<input class="text storeName" maxlength="200" type="text" name="storeName" value="" onkeyup="clearSelect(this)">
								<input type="button" class="iconSearch" value="" id="selectStore">
							</span>
                    </dd></dl>
                <dl>
                    <dt><p>${message("状态")}:</p></dt>
                    <dd>
                        <select name="status" class="text">
                            <option value>请选择</option>
                            <option value=0>${message("未审核")}</option>
                            <option value=1>${message("已审核")}</option>
                        </select>
                    </dd>
                </dl>
            </div>
        </div>

    </div>
    <div class="table-responsive">
        <table id="table-m1"></table>
        <div id="body-paginator">
            <div id="paginator"></div>
        </div>
    </div>
</form>
</body>
</html>