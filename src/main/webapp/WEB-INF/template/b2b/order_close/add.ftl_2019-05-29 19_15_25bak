<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看关闭单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>

<style>
select.areaId_select {
    float: left;
}
</style>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,2,false,e)
}

function editQty(t,e,n){
	if(extractNumber(t,3,false,e)){
		var branchQuantity=$(t).val();
		var $tr = $(t).closest("tr");
		var perBranch=$tr.find(".perBranch").val();
		var quantity=accMul(branchQuantity,perBranch);
    	if(isNaN(quantity)){
    		quantity = 0;
		}
    	$tr.find(".quantity").val(quantity);//数量
    	$tr.find(".quantityStr").html(quantity);
	}
}
$().ready(function() {
	$("#orderChangeType").change(function(){
		var $this = $(this);
		if($this.val()==0){
			$(".orderInfo").show();
		}else{
			$(".orderInfo").hide();
		}
	})

	var order_sn = null;
	var order_json = ${orderItem_json};
	var orderIndex = 0;
	var grade={"1":"优等品","2":"二等品","3":"一等品","4":"无等级"};
	var order_cols = [	
		{ title:'${message("木种花色")}', align:'center',name:'wood_type_or_color'},
		{ title:'${message("产品规格")}', name:'spec' ,align:'center', width:110},
		{ title:'${message("产品等级")}', name:'product_grade' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			var result = grade[val];
			if(result==undefined) result='';
 			var html='<span class="text productGradeText">'+result+'</span>'+'<input type="hidden" name="orderCloseItems['+orderIndex+'].productGrade" class=" text" value="'+val+'" />';
 			return html;
		}},
		{ title:'${message("下单支数")}', align:'center',name:'branch_quantity'},
		{ title:'${message("下单数量")}', align:'center',name:'quantity'},
		{ title:'${message("单价")}', align:'center',name:'price',renderer: function(val,item,rowIndex){
			return '<span class="red">'+currency(val,true)+'</span><input type="hidden" name="orderCloseItems['+orderIndex+'].price" value="'+item.price+'">';
		}},
		{ title:'${message("发货通知数量")}', align:'center',name:'havent_ship_branch_quantity'},
		{ title:'${message("已发货数量")}', align:'center',name:'shipped_branch_quantity'},
		{ title:'${message("已关闭支数")}', align:'center',name:'closed_amount'},
		{ title:'${message("关闭支数")}', align:'center',name:'',renderer: function(val,item,rowIndex){
			var quantity=0;
			var per_branch='';
			if( item.per_branch == undefined ||  item.per_branch == 0){
			
				per_branch=0;
				return '-'+
				       '<input type="hidden" kid="branch" class="t branchQuantity"  name="orderCloseItems['+orderIndex+'].branchQuantity" value="0"  >'+
					   '<input type=hidden class="perBranch" name="orderCloseItems['+orderIndex+'].perBranch" value="'+per_branch+'" />';
			}else{
				return '<div class="nums-input ov">'+
				'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)" />'+
        		'<input type="text" kid="branch" class="t branchQuantity" name="orderCloseItems['+orderIndex+'].branchQuantity" value="'+item.max_quantity+'" org="'+quantity+'" maxData="'+item.max_quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" />'+
        		'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)" />'+
        		'<input type=hidden class="perBranch" name="orderCloseItems['+orderIndex+'].perBranch" value="'+item.per_branch+'" />'+
        		'</div>';
			}
			
		}},
		{ title:'${message("关闭数量")}', align:'center',name:'',renderer: function(val,item,rowIndex){
			if( item.per_branch == undefined ||  item.per_branch == 0){
				return '<div class="nums-input ov">'+
					'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)" />'+
        			'<input type="text" class="t quantity" name="orderCloseItems['+orderIndex+'].quantity" value="'+item.quantity+'"  maxData="'+item.quantity+'" minData="0"  oninput="editPrice (this,event)" onpropertychange="editQty(this,event)" />'+
        			'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)" />'+
        			'</div>';
			}else{
				return '<span class="quantityStr">'+item.max_quantity*item.per_branch+'</span><input type="hidden" class="quantity" name="orderCloseItems['+orderIndex+'].quantity" value="'+item.max_quantity*item.per_branch+'">';
			}
			
			
		}},
		
		{hidden:true, renderer: function(val,item,rowIndex,obj){
			order_sn = item.sn;
			var html = '';
			var html = '<input type="hidden" name="orderCloseItems['+orderIndex+'].orderItemId" value="'+item.id+'">'+
				'<input type="hidden" name="orderCloseItems['+orderIndex+'].orderSn" value="${order.sn}">';
			orderIndex++;
			return html;
			
		}},
	];
	
	var $order = $('#table-order');
	$orderGrid = $order.mmGrid({
		height:'auto',
        cols: order_cols,
        items:order_json,
        checkCol: false,
        autoLoad: true,
        fullWidthRows:true,
        callback:function(){
        	//地区选择
			$("#areaId").lSelect();
			//$order.find("input").prop("disabled",true);
			//$order.find("select").prop("disabled",true);
        }
    });
    
	
	// 表单验证
	$.validator.addClassRules({
		statisticsCategoryName : {
			required: true
		}
	});
	$("form").validate({
		rules: {
			name: "required",
			order: "required",
			sql: "required"
		}
	});
	
	$("form").bindAttribute({
    	isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= 'edit.jhtml?id='+resultMsg.objx;
			})
	    }
	 });
	 
	//打开选择产品界面
	var $addProduct = $("#addProduct");
	var storeId = $(".storeId").val();
	$addProduct.click(function(){
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'${message("查询产品")}',
			url:'/product/product/selectProduct.jhtml?multi=2&isPart=0&storeId='+storeId,
			callback:function(rows){
				if(rows.length>0){
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".productId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						
						if(row.apply_price!=undefined && row.apply_price!=null && row.apply_price!=""){
								row.price=row.apply_price;
							}
							else if(row.member_price!=undefined && row.member_price!=null && row.member_price!=""){
								row.price=row.member_price;
							}
						row.order_sn = order_sn;
						$orderItemGrid.addRow(row,null,1);
					}
				}
			}
		});	
	})
	// 删除产品
	var $deleteProduct = $("a.deleteProduct");
	$deleteProduct.live("click", function() {
		var itemId = $(this).attr("itemId");
		var index = $(this).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$orderItemGrid.removeRow(index);
			var $child_trs = $("input.pId[parentId='"+itemId+"']").closest("tr");
			$child_trs.each(function(){
				$orderItemGrid.removeRow($(this).index());
			});
		})
	});
	
	var $addElseProduct = $("#addElseProduct");
    $addElseProduct.click(function(){
		var row=[{order_sn:order_sn,"else":"1"}];
		$orderItemGrid.addRow(row,null,1);
	})
	
});

// 审核
function check(e,status){
	var $this = $(e);
	var content = '';
	if(status==0){
		content = '您确定要作废关闭单吗？';
	}else{
		content = '您确定要审核吗？';
	}
	var id = '${orderChange.id}';
	var url="check.jhtml";
	ajaxSubmit($this,{
		 url: url,
		 method: "post",
		 data: {id:id,status:status},
		 isConfirm:true,
		 confirmText:content,
		 callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		 }
	})
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增关闭单")}
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table  class="input input-edit">
			<tr>
					<th>
						<span class="requiredField">*</span>${message("关闭单编号")}:
					</th>
					<td>
						
					</td>
					<th>客户</th>
					<td>
						${order.store.name}
						<input type="hidden" class="storeId" name="store.id" value="${order.store.id}">
					</td>
					<th>${message("状态")}:</th>
					<td>
						
					</td>
					<th>${message("创建时间")}:</th>
					<td></td>
					
				</tr>
				<tr>
					<th>
						${message("订单编号")}:
					</th>
					<td>
						${order.sn}
						<input type="hidden" name="orderSn" value="${order.sn}">
						<input type="hidden" name="orderId" value="${order.id}">
					</td>
					<th>
						${message("机构")}:
					</th>
					<td>
						<input type="hidden" name="saleOrgId" value="${order.saleOrg.id}">${order.saleOrg.name}
					</td>
					<th></th>
					<td>
					</td>
					<th></th>
					<td></td>
					
				</tr>
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			 <tr class="border-L1">
				<th> ${message("订单信息")}:
				</th>
				<td colspan="7">
				</td>
			</tr>
			<tr class="border-L1">
				<td colspan="8">
					<div>
						<table id="table-order"></table>
					</div>
				</td>
			</tr>
		</table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>