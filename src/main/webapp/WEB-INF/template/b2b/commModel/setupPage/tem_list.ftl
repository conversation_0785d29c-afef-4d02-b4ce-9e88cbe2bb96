<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("模板列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
	<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript">

	$().ready(function() {
		var cols = [
    		{ title:'${message("模块名称")}', name:'moduName' ,align:'left',renderer:function(val,item,rowIndex){
    			return val;
    		}},
			{ title:'${message("模板名称")}', name:'name' ,align:'center' },
			{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },
			{ title:'${message("创建人")}', name:'b_creater' ,align:'center' },
			{ title:'${message("是否启用")}', name:'is_enable' ,align:'center',renderer:function(val){
    			if(isNull(val) != null && val) {
    				return '<span class="trueIcon">&nbsp;</span>';
    			}else{
    				return '<span class="falseIcon">&nbsp;</span>';
    			}
    		}}
    	];

    	$mmGrid = $('#table-m1').mmGrid({
			multiSelect:false,
			autoLoad: true,
			fullWidthRows:true,
			checkByClickTd:true,
			rowCursorPointer:true,
			cols: cols,
			url: '/template/dTemplates/select_templates_data.jhtml',
			params:function(){
				return $("#listForm").serializeObject();
			},
			plugins : [
				$('#paginator').mmPaginator()
			]
        });


	});

	function childMethod(){
		return $mmGrid.selectedRows();
	};

</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap"></div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">
					${message("1004")}
				</a>
			</div>
			<div id="searchDiv">
				 <div id="search-content" >
					 <dl>
						 <dt ><p>${message("模块名称")}:</p></dt>
						 <dd >
							 <input type="text" class="text" name="modelName" btn-fun="clear"/>
						 </dd>
					 </dl>
					 <dl>
						 <dt ><p>${message("模板名称")}:</p></dt>
						 <dd >
							 <input type="text" class="text" name="tempName" btn-fun="clear"/>
						 </dd>
					 </dl>
		    		<dl>
						<dt><p>${message("创建时间")}：</p></dt>
						<dd class="date-wrap">
							<input id="createDate" name="creatDate" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'createDate\')}'});" type="text" btn-fun="clear"/>
						</dd>
					</dl>
	        	</div>
			</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>