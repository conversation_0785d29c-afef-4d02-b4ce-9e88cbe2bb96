<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("模板列查看")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
	<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
	<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
	<script type="text/javascript" src="/resources/js/base/request.js"></script>
	<script type="text/javascript" src="/resources/js/base/global.js"></script>
	<script type="text/javascript">
		$().ready(function() {
			var $inputForm = $("#inputForm");
			// 表单验证
			$.validator.addClassRules({
				name: {
					required: true
				},
				showName: {
					required: true
				},
				dTemplatesName: {
					required: true
				},
				orderby: {
					required: true
				},
				popUpTypeId: {
					required: true
				},
				screenTypeId: {
					required: true
				},
				maximumWidth:{
					required: true
				}
			});
			
			$("form").bindAttribute({
				isConfirm:true,
				callback: function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.href= '/template/dTemplates/template_view.jhtml?id='+resultMsg.objx;
					});
				}
			});

            //查询模板
            $("#selectTemplates").click(function(){
                $("#selectTemplates").bindQueryBtn({
                    type:'templates',
                    title:'${message("查询模板")}',
                    bindClick:false,
                    url:'/template/dTemplates/select_list.jhtml',
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $("input.popUpTemplate").val(row.id);
                            $("input.popUpTemplateName").val(row.name);
                        }else{
                            $("input.popUpTemplate").val('');
                            $("input.popUpTemplateName").val('');
                        }
                    }
                });
            });

		});
		
		//删除
		function deleteDTemplateColumns(e){
		    var str = "您确定要删除？";
	        $.message_confirm(str,function() {
	            ajaxSubmit(e,{
	                url:'deleteDTemplateColumns.jhtml?id='+${dTemplateColumns.id},
	                method:"post",
	                callback:function(resultMsg){
	                	//parent.change_tab(1,'/template/dTemplates/template_view.jhtml?id='+resultMsg.objx);
	                	location.href= '/template/dTemplates/template_view.jhtml?id='+resultMsg.objx;
	                }
	            });
	        });
		}

		function returnView() {
			var templateId = $(".dTemplatesId").val();
			parent.change_tab(0,'/template/dTemplates/template_view.jhtml?id='+templateId);
		}
		
	</script>
</head>
<body>
<div class="pathh">
	&nbsp;${message("列基础数据")}
</div>
<form id="inputForm" action="saveOrUpdate.jhtml" method="post" type="ajax" validate-type="validate">
	<input type="hidden" name="id" value="${dTemplateColumns.id}" />
	<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>
					${message("字段名")}:
				</th>
				<td>
					<input type="text" name="name" class="text name" value="${dTemplateColumns.name}" btn-fun="clear" />
				</td>
				<th>
					<span class="requiredField">*</span>
					${message("显示名")}:
				</th>
				<td>
					<input type="text" name="showName" class="text showName" value="${dTemplateColumns.showName}" btn-fun="clear"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("最大宽度")}:
				</th>
				<td>
					<input type="text" name="maximumWidth" class="text maximumWidth" value="${dTemplateColumns.maximumWidth}" btn-fun="clear"/>
				</td>
                <th>
                    <span class="requiredField">*</span>${message("排序")}:
                </th>
                <td>
                    <input type="text" name="orderby" class="text orderby" value="${dTemplateColumns.orderby}" btn-fun="clear"/>
                </td>
			</tr>
			<tr>
				<th>
					${message("是否默认")}:
				</th>
				<td>
					<select name="isDefault" class="text">
						<option value=0 [#if dTemplateColumns.isDefault == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.isDefault == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
                <th>
                    ${message("是否默认筛选")}:
                </th>
                <td>
                    <select name="defaultScreening" class="text">
                        <option value=0 [#if dTemplateColumns.defaultScreening == false]selected="selected"[/#if]>否</option>
                        <option value=1 [#if dTemplateColumns.defaultScreening == true]selected="selected"[/#if]>是</option>
                    </select>
                </td>
				<th>
					<span class="requiredField">*</span>${message("列类型")}:
				</th>
				<td>
					<select id="columnsTypeId" name="columnsTypeId" class="text columnsTypeId">
						[#list columnsTypeList as columnsType]
							<option value="${columnsType.id}" [#if dTemplateColumns.columnsType.id == columnsType.id]selected="selected"[/#if]>${columnsType.value}</option>
						[/#list]
					</select>
				</td>
                <th>
                    <span class="requiredField">*</span>${message("模板")}:
                </th>
                <td>
                    <input type="hidden" name="dTemplatesId" class="text dTemplatesId"  btn-fun="clear" value="${dTemplateColumns.dTemplates.id}" />
                    <input type="text" class="text dTemplatesName"  btn-fun="clear" value="${dTemplateColumns.dTemplates.name}" readonly="readonly"/>
                </td>
			</tr>
			<tr>
				<th>${message("备注")}:</th>
				<td colspan="7">
					<textarea class="text memo"  name="memo">${dTemplateColumns.memo}</textarea>
				</td>
			</tr>
		</table>
		<div class="title-style">
			&nbsp;${message("基础设置")}
		</div>
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("是否显示")}:
				</th>
				<td>
					<select name="isShowed" class="text">
						<option value=0 [#if dTemplateColumns.isShowed == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.isShowed == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("是否必选条件")}:
				</th>
				<td>
					<select name="requiredCondition" class="text">
						<option value=0 [#if dTemplateColumns.requiredCondition == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.requiredCondition == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					${message("是否允许选择")}:
				</th>
				<td>
					<select name="isSelected" class="text">
						<option value=0 [#if dTemplateColumns.isSelected == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.isSelected == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("是否允许多选")}:
				</th>
				<td>
					<select name="isMultSelected" class="text">
						<option value=0 [#if dTemplateColumns.isMultSelected == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.isMultSelected == true]selected="selected"[/#if]>是</option>
					</select>
				</td>

			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("是否允许导出")}:
				</th>
				<td>
					<select name="isExport" class="text">
						<option value=0 [#if dTemplateColumns.isExport == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.isExport == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					${message("是否只读")}:
				</th>
				<td>
					<select name="isReadonly" class="text">
						<option value=0 [#if dTemplateColumns.isReadonly == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.isReadonly == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					${message("是否列表、数据集")}:
				</th>
				<td>
					<select name="isList" class="text">
						<option value=0 [#if dTemplateColumns.isList == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.isList == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
                <th>
                    ${message("是否系统字段")}:
                </th>
                <td>
                    <select name="isSysColumn" class="text">
                        <option value=0 [#if dTemplateColumns.isSysColumn == false]selected="selected"[/#if]>否</option>
                        <option value=1 [#if dTemplateColumns.isSysColumn == true]selected="selected"[/#if]>是</option>
                    </select>
                </td>
			</tr>
		</table>
        <div class="title-style">
            &nbsp;${message("校验控制")}
        </div>
        <table class="input input-edit">
            <tr>
                <th>
                    ${message("是否校验")}:
                </th>
                <td>
                    <select name="isChecked" class="text">
                        <option value=0 [#if dTemplateColumns.isChecked == false]selected="selected"[/#if]>否</option>
                        <option value=1 [#if dTemplateColumns.isChecked == true]selected="selected"[/#if]>是</option>
                    </select>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("校验类型")}:
                </th>
                <td>
                    <select id="checkedTypeId" name="checkedTypeId" class="text checkedTypeId">
                        <option value="">请选择</option>
                        [#list checkedTypeList as checkedType]
                            <option value="${checkedType.id}" [#if dTemplateColumns.checkedType.id == checkedType.id]selected="selected"[/#if]>${checkedType.value}</option>
                        [/#list]
                    </select>
                </td>
            </tr>
        </table>
		<div class="title-style">
			&nbsp;${message("控件设置")}
		</div>
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("控件类型")}:
				</th>
				<td>
					<select id="popUpTypeId" name="popUpTypeId" class="text popUpTypeId">
						[#list popUpTypeList as popUpType]
							<option value="${popUpType.id}" [#if dTemplateColumns.popUpType.id == popUpType.id]selected="selected"[/#if]>${popUpType.value}</option>
						[/#list]
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("控件是否默认值")}:
				</th>
				<td>
					<select name="controlIsDefault" class="text">
						<option value=0 [#if dTemplateColumns.controlIsDefault == false]selected="selected"[/#if]>否</option>
						<option value=1 [#if dTemplateColumns.controlIsDefault == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("筛选类型")}:
				</th>
				<td>
					<select id="screenTypeId" name="screenTypeId" class="text screenTypeId">
						[#list screenTypeList as screenType]
							<option value="${screenType.id}" [#if dTemplateColumns.screenType.id == screenType.id]selected="selected"[/#if]>${screenType.value}</option>
						[/#list]
					</select>
				</td>
				<th>
					${message("词汇编码")}:
				</th>
				<td>
					<input type="text" name="wordCoding" class="text wordCoding" value="${dTemplateColumns.wordCoding}"  btn-fun="clear"/>
				</td>

			</tr>
			<tr>
				<th>
					${message("筛选弹框模板")}:
				</th>
                <td>
                        <span class="search" style="position:relative">
							<input type="hidden" name="popUpTemplate" class="text popUpTemplate" btn-fun="clear" value="${dTemplateColumns.popUpTemplate}"/>
							<input type="text" class="text popUpTemplateName" maxlength="200" onkeyup="clearSelect(this)" value="${dTemplates.name}" readOnly/>
							<input type="button" class="iconSearch" value="" id="selectTemplates">
					    </span>
                </td>
				<th>
					${message("搜索对应ID字段")}:
				</th>
				<td>
					<input type="text" name="field" class="text wordCoding" value="${dTemplateColumns.field}"  btn-fun="clear"/>
				</td>
                <th>
                    ${message("搜索对应父级ID字段")}:
                </th>
                <td>
                    <input type="text" name="parentParamName" class="text wordCoding" value="${dTemplateColumns.parentParamName}"  btn-fun="clear"/>
                </td>
			</tr>
		</table>
		<div class="title-style">
			&nbsp;${message("配置链接")}
		</div>
		<table class="input input-edit">
			<tr>
				<th>
					${message("链接方法名")}:
				</th>
				<td>
					<input type="text" name="linkMethodName" class="text" btn-fun="clear" value="${dTemplateColumns.linkMethodName}"/>
				</td>
			</tr>
			<tr>
				<th>
					${message("链接")}:
				</th>
				<td colspan="7">
					<input type="text" name="link" class="text" value="${dTemplateColumns.link}" btn-fun="clear"/>
				</td>
			</tr>
			<tr>
				<th>
					${message("链接图标")}:
				</th>
				<td colspan="7">
					<input type="text" name="linkIcon" class="text linkIcon" value="${dTemplateColumns.linkIcon}" btn-fun="clear"/>
				</td>
			</tr>
		</table>
	</div>
	<!--模板参数字段-->
	<div class="fixed-top">
		<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
		<input type="button" onclick="deleteDTemplateColumns(this)" class="button" style="background: firebrick;" value="${message("删除")}">
		<input type="button" onclick="returnView()" class="button" style="background: #2aa05d;" value="${message("返回")}">
		<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
	</div>
</form>
</body>
</html>