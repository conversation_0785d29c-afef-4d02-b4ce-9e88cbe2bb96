<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html><head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link href="/resources/css/common.css" rel="stylesheet" type="text/css">
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css">
<link href="/resources/css/wf.css" rel="stylesheet" type="text/css">
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<style type="text/css">
	.title input{
		width: 50px;
		height: 30px;
		background: #0d89c1;
		color: white;
		border-radius: 5px;
		float: right;
		margin-top: 8px;
	}
</style>
<script>

	$().ready(function(e) {
	
		/**初始化多选的下拉框*/
		initMultipleSelect();
	
	    $("#js-menu dt").click(function(){
	    	var $dl = $(this).closest("dl");
	    	if(!$dl.hasClass("noline_docu")){
	    		$(this).closest("dl").toggleClass("noline_close").toggleClass("noline_open");
	    	}
	    });
	
	
		$("#js-menu dd a").click(function(){
	        $(".client-list dd a").removeClass("on")
	        $(this).addClass("on");
	    });
	
		$("#manage").click(function () {
			parent.change_tab(1,'/template/dModules/model_list.jhtml');
		});
	    
		var cols = [
			{ title:'${message("模板名称")}', name:'name' ,width:200, align:'center', renderer: function(val,item,rowIndex){
				var id = item.id;
				return '<a href="javascript:void(0);" onClick="view(this,'+id+');" class="red">'+val+'</a>';
							
			}},
			{ title:'${message("是否启用")}', name:'is_enable',width:150, align:'center', renderer: function(val,item,rowIndex) {
				if(isNull(val) != null && val) {
    				return '<span class="trueIcon">&nbsp;</span>';
    			}else{
    				return '<span class="falseIcon">&nbsp;</span>';
    			}	
			}},
			{ title:'${message("是否主要模板")}', name:'is_main',width:200, align:'center', renderer: function(val,item,rowIndex) {
				if(isNull(val) != null && val) {
    				return '<span class="trueIcon">&nbsp;</span>';
    			}else{
    				return '<span class="falseIcon">&nbsp;</span>';
    			}	
			}},
			{ title:'${message("是否用户默认模板")}', name:'is_user_default_template', width:100, align:'center', renderer: function(val,item,rowIndex){
				if(isNull(val) != null && val) {
    				return '<span class="trueIcon">&nbsp;</span>';
    			}else{
    				return '<span class="falseIcon">&nbsp;</span>';
    			}	
			}},
			{ title:'${message("版本")}', name:'edition', align:'center', width:80,  renderer: function(val,item,rowIndex){
				return val;
			}},
			{ title:'${message("创建时间")}', name:'create_date', width:150, align:'center'},
			{ title:'${message("备注")}', name:'memo', width:150, align:'center'}
		];
		
		$mmGrid = $('#table-m1').mmGrid({
			autoLoad:true,
			multiSelect:false,
			cols: cols,
			fullWidthRows:true,
			url: 'select_templates_data.jhtml',
			params:function(){
				return $("#listForm").serializeObject();
			},
			plugins : [
				$('#paginator').mmPaginator()
			],
			checkDisabled:function(item,rowIndex){
				if(item.parent!=undefined){
					return true;
				}
			}
	    });
	
		$(".total_num").each(function(){
			var num = $(this).val();
			$(this).closest("dl").find(".total").text(num);
		})
	
	    var $a = $(".wf-a:first").addClass("on");
	    $a.closest("dl").removeClass("noline_close").addClass("noline_open");
	
	    $("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			url:'/member/store/select_store.jhtml'
		});
		treeScoll();
	});

	function add(e) {
	    parent.change_tab(0,'/template/dTemplates/template_add.jhtml');
	}
	
	function view(e,id) {
	    parent.change_tab(0,'/template/dTemplates/template_view.jhtml?id='+id);
	}

	//点击模块
	function clickModule(e,id,parent_id) {
		//赋值当前id
		$("#modelId").val(id);
		//赋值当前父级id
		$("#parentdmodulesId").val(parent_id);
		$mmGrid.load();
	}
</script>
</head>
<body class="tree-contain">
	<div class="flow-boxL">
		<div class="title" >模块管理
			<input type="button" value="管理" id="manage"/>
		</div>
		<div class="client-list" id="js-menu">
			[#list mapList as map]
				<dl>
					<dt>
						<a href="javascript:void(0);" onclick="clickModule(this,'',${map.id})">${map.name}</a>
					</dt>
					<dd>
						[#if (map.mapItemList)??]
							[#list map.mapItemList as mapItem]
								<a href="javascript:void(0);" onclick="clickModule(this,${mapItem.id},'')">${mapItem.name}</a>
							[/#list]
						[/#if]
					</dd>
				</dl>
			[/#list]
		</div>
	</div>
	<label id="labBtn" style="left: 272px;"></label>
	<div class="flow-boxR">
		<form id="listForm" action="" method="get">
			<input type="hidden" name="modelId" id="modelId" value="">
			<input type="hidden" name="parentdmodulesId" id="parentdmodulesId" value="">
			<div class="bar">
				<div class="buttonWrap"></div>
				<a href="javascript:add(this);" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
				<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
				<div id="searchDiv">
					<div id="search-content">
						<dl>
							<dt>
								<p>${message("模板名称")}:</p>
							</dt>
							<dd>
								<input class="text" maxlength="200" type="text" name="tempName" value="" btn-fun="clear"s/>
							</dd>
						</dl>
						<dl>
							<dt>
								<p>${message("是否启用")}:</p>
							</dt>
							<dd>
								<select name="isEnable" class="text">
									<option value=0>否</option>
									<option value=1 selected="selected">是</option>
								</select>
							</dd>
						</dl>
					</div>
				</div>
			</div>
			<div class="table-responsive">
				<table id="table-m1"></table>
				<div id="body-paginator" style="text-align:left;">
					<div id="paginator"></div>
				</div>
			</div>
		</form>
	</div>
</body>
</html>