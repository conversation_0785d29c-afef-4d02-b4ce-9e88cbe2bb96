<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("模板编辑")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<link href="/resources/css/mmGridOld.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGridItem.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<style type="text/css">
	.tabContent {
		margin: 0 5px;
	    border-top: solid 1px #eee;
	    border-left: solid 1px #eee;
	    border-right: solid 1px #eee;
	    padding: 10px;
	    padding-bottom: 50px;
	}
	
	.fixed-top {
	    top: -46px;
	}
	
</style>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript">

    function readOnlyEvent(e,name) {
        var value = $(e).val()
        if (value=="0"){
            $("input[name='"+name+"']").attr("readOnly",true);
        }else {
            $("input[name='"+name+"']").attr("readOnly",false);
        }
    }

		$().ready(function() {
			
			var $inputForm = $("#inputForm");
			// 表单验证
			$inputForm.validate({
				rules: {
					code: "required",
					value: "required"
				},
				submitHandler:function(form){
					return false;
				}
			});
			
			var $selectDModules = $("#selectDModules");
			
			$inputForm.bindAttribute({
				isConfirm:true,
				callback: function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					});
				}
			});

            //模板按钮
            var table_button = [
                { title:'${message("操作")}',align:'center', width:80, renderer:function(val,item){
                        return '<a href="javascript:;" class="btn-delete" onclick="deleteButton(this,'+item.id+')" >删除</a>';
                    }},
                { title:'${message("按钮名称")}', name:'value',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                        var html = '<input type="hidden" class="buttonId" value="'+item.id+'">'+
                            '<a href="javascript:void(0);" onClick="buttonView(this,'+item.d_templates+')" class="red">'+val+'</a>';
                        return html
                    }},
                { title:'${message("跳转链接")}', name:'url',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                        return val;
                    }},
                { title:'${message("关联页面")}', name:'associated_page',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						return val;
					}},
				{ title:'${message("排列顺序")}', name:'sort',align:'center', maxWidth:100, renderer: function(val,item,rowIndex, obj){
						return val;
					}},
                { title:'${message("是否生效")}', name:'is_enable',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                        if(val){
                            return '<span class="trueIcon">&nbsp;</span>';
                        }else{
                            return '<span class="falseIcon">&nbsp;</span>';
                        }
                    }},
            ];

            $table_button = $('#table_button').mmGrid({
                height:'auto',
                autoLoad:true,
                cols: table_button,
                fullWidthRows:true,
                checkCol: false,
                url: '/template/dTemplateButton/list_data.jhtml',
                params:function(){
                    return $("#buttonForm").serializeObject();
                },
                plugins : [
                    $("#buttonPaginator").mmPaginator()
                ]
            });
			
			//模板参数
			var table_parameters = [
				{ title:'${message("操作")}',align:'center', width:80, renderer:function(val,item){
						return '<a href="javascript:;" class="btn-delete" onclick="deleteParameters(this,'+item.id+')" >删除</a>';
				}},
				{ title:'${message("参数名称")}', name:'name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						var html = '<input type="hidden" class="paramterId" value="'+item.id+'">'+
						'<a href="javascript:void(0);" onClick="parametersView(this,'+item.d_templates+')" class="red">'+val+'</a>';
						return html
				}},
				{ title:'${message("参数类型")}', name:'parameters_type_name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						return val;
				}},
				{ title:'${message("校验类型")}', name:'checked_type_name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						return val;
				}},
				{ title:'${message("是否校验")}', name:'is_checked',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						if(val){
							return '<span class="trueIcon">&nbsp;</span>';
						}else{
							return '<span class="falseIcon">&nbsp;</span>';
						}
				}},
				{ title:'${message("是否系统字段")}', name:'is_sys_parameter',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						if(val){
							return '<span class="trueIcon">&nbsp;</span>';
						}else{
							return '<span class="falseIcon">&nbsp;</span>';
						}
				}},
				{ title:'${message("备注")}', name:'memo',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						return val;
				}}
			];

			$table_parameters = $('#table_parameters').mmGrid({
				height:'auto',
	    		autoLoad:true,
	            cols: table_parameters,
	            fullWidthRows:true,
	            checkCol: false,
	            url: '/template/dTemplateParameters/list_data.jhtml',
	            params:function(){
	            	return $("#parameterForm").serializeObject();
	            },
	    		plugins : [
	                $("#parametersPaginator").mmPaginator()
	            ]
	        });
			
			
			//模板列
			var table_columns = [        
				{ title:'${message("操作")}',align:'center', width:80, renderer:function(val,item){
					return '<a href="javascript:;" class="btn-delete" onclick="deleteColumns(this)" >删除</a>';
				}},  
				{ title:'${message("列名称")}', name:'name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
					var html = '<input type="hidden" class="columnsId" value="'+item.id+'">'+
					'<a href="javascript:void(0);" onClick="columnsView(this)" class="red">'+val+'</a>';
					return html
				}},
				{ title:'${message("列类型")}', name:'columns_name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
					return val;
				}},
				{ title:'${message("校验类型")}', name:'checked_name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
					return val;
				}},
				{ title:'${message("列显示名")}', name:'show_name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
					return val;
				}},
				{ title:'${message("是否显示")}', name:'is_showed',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
					if(val){
						return '<span class="trueIcon">&nbsp;</span>';
					}{
						return '<span class="falseIcon">&nbsp;</span>';
					}
				}},
                { title:'${message("排序")}', name:'orderby',align:'center', width:50, renderer: function(val,item,rowIndex, obj){
                        return val;
                    }},
				{ title:'${message("备注")}', name:'memo',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
					return val;
				}}
			];
			
			$table_columns = $('#table_columns').mmGrid({
				height:'auto',
	    		autoLoad:true,
	    		cols: table_columns,
	            fullWidthRows:true,
	            checkCol: false,
	            url: '/template/dTemplateColumns/list_data.jhtml',
	            params:function(){
	            	return $("#columusForm").serializeObject();
	            },
	    		plugins : [
	                $("#columnsPaginator").mmPaginator()
	            ]
	        });
			
	
			
			//用户模板
			var table_users_template = [
				 { title:'${message("操作")}',align:'center', width:80, renderer:function(val,item){
				    	return '<a href="javascript:;" class="btn-delete" onclick="deleteTemplateUsers(this,'+item.id+')">删除</a>';
				 }},                       
                 { title:'${message("用户名")}', name:'store_member_name',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                	 var html = '<a href="javascript:void(0);" onClick="userTemplateColumnsList('+item.id+')" class="red">'+val+'</a>';
					 return html 
                 }},
                 { title:'${message("创建时间")}', name:'create_date',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                         return val;
                 }}
			];

			$table_users_template = $('#table_users_template').mmGrid({
				height:'auto',
	    		autoLoad:true,
	    		cols: table_users_template,
	            fullWidthRows:true,
	            checkCol: false,
	            url: '/template/dTemplateUsers/list_data.jhtml',
	            params:function(){
	            	return $("#usersTemplateForm").serializeObject();
	            },
	    		plugins : [
	                $("#usersTemplatePaginator").mmPaginator()
	            ]
	        });
			
			
			//新增
			$("#addColumus").click(function () {
				parent.change_tab(0,'/template/dTemplateColumns/columns_add.jhtml?dTemplatesId='+${dTemplates.id});
			});

			
			//导入
			$("#columusImport").click(function () {
				excel_import(this,{
					title:"模板列导入",
					url:"/template/dTemplateColumns/import_excel.jhtml?dTemplatesId=${dTemplates.id}",
					template:"/resources/template/dTemplateColumns/dTemplateColumns.xls",
					callback:function(){
						location.reload(true);
					}
				})
			});
			
			//新增参数
			$("#addParameters").click(function () {
				parent.change_tab(0,'/template/dTemplateParameters/parameter_add.jhtml?dTemplatesId='+${dTemplates.id});
			});

			//新增按钮
			$("#addTempButton").click(function () {
				var tempId = $("input[name='id']").val();
				parent.change_tab(0,'/template/dTemplateButton/button_add.jhtml?tempId='+tempId);
			});

			
			//查询模块
			$selectDModules.bindQueryBtn({
				type:'dModules',
				title:'查询模块',
				url:'/template/dModules/select_dModules.jhtml?multi=1&isEmpty=1',
				callback:function(rows){
					if(rows.length>0){
						var row = rows[0];
						$("input.modulesId").val(row.id);
						$("input.modulesName").val(row.name);
					}else{
						$("input.modulesId").val('');
						$("input.modulesName").val('');
					}
				}
			});
			
	
	    	$(".table-responsive").attr("style","width: 100%;height: auto;");
	    	$(".mmg-bodyWrapper").attr("style","height: auto; width: 100%;");
			
		});

		//新增
		function add(e) {
			parent.change_tab(0,'/template/dTemplates/template_add.jhtml');
		}

		//查看按钮
		function buttonView(e,templatesId) {
			var $tr =$(e).closest("tr");
			//当前参数id
			var id = $tr.find(".buttonId").val();
			parent.change_tab(0,'/template/dTemplateButton/button_add.jhtml?tempId='+templatesId+"&id="+id);
		}

		//查看参数
		function parametersView(e,templatesId) {
			var $tr =$(e).closest("tr");
			//当前参数id
			var id = $tr.find(".paramterId").val();
			parent.change_tab(0,'/template/dTemplateParameters/parameter_view.jhtml?templatesId='+templatesId+"&id="+id);
		}
	
		//查看列
		function columnsView(e) {
			var $tr =$(e).closest("tr");
			//当前列id
			var id = $tr.find(".columnsId").val();
			parent.change_tab(0,'/template/dTemplateColumns/columns_view.jhtml?id='+id);
		}
		
		//用户模板列
		function userTemplateColumnsList(usersTemplateId) {
			parent.change_tab(0,'/template/dUserColumns/user_columns_list.jhtml?usersTemplateId='+usersTemplateId);
		}

        //删除按钮关联
        function deleteButton(e,id){
            $.message_confirm('您确定要删除这个按钮吗？',function(){
                ajaxSubmit(e,{
                    url:'/template/dTemplateButton/delete.jhtml?id='+id,
                    method:"post",
                    callback:function(resultMsg){
                        location.reload(true);
                    }
                });
            });
        }
		
		//删除参数
		function deleteParameters(e,id){
			$.message_confirm('您确定要删除这行参数吗？',function(){
				ajaxSubmit(e,{
					url:'/template/dTemplateParameters/deleteParamter.jhtml?id='+id,
					method:"post",
					callback:function(resultMsg){
						location.reload(true);
					}
				});
			});
		}
		
		//删除列
		function deleteColumns(e) {
			var $tr =$(e).closest("tr");
			//当前列id
			var id = $tr.find(".columnsId").val();
			var str = "您确定要删除？";
	        $.message_confirm(str,function() {
	            ajaxSubmit(e,{
	                url:'/template/dTemplateColumns/deleteDTemplateColumns.jhtml?id='+id,
	                method:"post",
	                callback:function(resultMsg){
	                	location.reload(true);
	                }
	            });
	        });
		}
		
		
		//删除用户模板
		function deleteTemplateUsers(e,id) {
			var $tr =$(e).closest("tr");
			var str = "您确定要删除？";
	        $.message_confirm(str,function() {
	            ajaxSubmit(e,{
	                url:'/template/dTemplateUsers/deleteUserTemplate.jhtml?id='+id,
	                method:"post",
	                callback:function(resultMsg){
	                	location.reload(true);
	                }
	            });
	        });
		}
		
		
		
		//删除
		function deleteDTemplates(e){
		    var str = "您确定要删除？";
	        $.message_confirm(str,function() {
	            ajaxSubmit(e,{
	                url:'delete.jhtml?id='+${dTemplates.id},
	                method:"post",
	                callback:function(resultMsg){
	                	parent.change_tab(1,'/template/dTemplates/list.jhtml');
	                }
	            });
	        });
		}
		
</script>
</head>
<body>
<div class="pathh">
	&nbsp;${message("模板编辑")}
</div>
<div class="tabContent">
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" class="text" name="id" value="${dTemplates.id}"/>
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("模板名")}:
				</th>
				<td>
					<input type="text" name="name" class="text" value="${dTemplates.name}"/>
				</td>
				<th>
					<span class="requiredField">*</span>${message("是否启用")}:
				</th>
				<td>
					<select name="isEnable" class="text">
						<option value=0 [#if dTemplates.isEnable == false]selected="selected"[/#if]>否</option>
						<option value=1  [#if dTemplates.isEnable == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("是否主模板")}:
				</th>
				<td>
					<select name="isMain" class="text">
						<option value=0 [#if dTemplates.isMain == false]selected="selected"[/#if]>否</option>
						<option value=1  [#if dTemplates.isMain == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
				<th>
					<span class="requiredField">*</span>${message("是否用户默认模板")}:
				</th>
				<td>
					<select name="isUserDefaultTemplate" class="text">
						<option value=0 [#if dTemplates.isUserDefaultTemplate == false]selected="selected"[/#if]>否</option>
						<option value=1  [#if dTemplates.isUserDefaultTemplate == true]selected="selected"[/#if]>是</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("对应模块")}:
				</th>
				<td>
					<span class="search" style="position:relative">
						<input type="hidden" name="modulesId" class="text modulesId" btn-fun="clear" value="${dTemplates.dModules.id}"/>
						<input type="text" class="text modulesName" maxlength="200" onkeyup="clearSelect(this)" value="${dTemplates.dModules.name}" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectDModules">
					</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("操作类型")}:
				</th>
				<td>
                    <select id="actionTypeId" name="actionTypeId" class="text actionTypeId">
                        [#list actionTypeList as actionType]
                            <option value="${actionType.id}" [#if dTemplates.actionType.id == actionType.id]selected="selected"[/#if]>${actionType.value}</option>
                        [/#list]
                    </select>
				</td>
                <th>${message("是否版本控制")}:
                </th>
                <td>
                    <select name="versionControl" class="text" onchange="readOnlyEvent(this,'edition')">
                        <option value=0 [#if dTemplates.versionControl == false]selected="selected"[/#if]>否</option>
                        <option value=1  [#if dTemplates.versionControl == true]selected="selected"[/#if]>是</option>
                    </select>
                </td>
				<th>
					<span class="requiredField">*</span>${message("版本")}:
				</th>
				<td>
					<input type="text" name="edition" class="text" value="${dTemplates.edition}" readonly/>
				</td>
			</tr>
            <tr>
                <th>${message("是否默认查询")}:
                </th>
                <td>
                    <select name="defaultQuery" class="text">
                        <option value=0 [#if dTemplates.defaultQuery == false]selected="selected"[/#if]>否</option>
                        <option value=1  [#if dTemplates.defaultQuery == true]selected="selected"[/#if]>是</option>
                    </select>
                </td>
                <th>
                    ${message("分页大小")}:
                </th>
                <td>
                    <input type="text" name="pagesize" onkeyup="if(isNaN(value))execCommand('undo')" onafterpaste="if(isNaN(value))execCommand('undo')" value="${dTemplates.pagesize}" class="text" btn-fun="clear"/>
                </td>
            </tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("执行sql")}:
				</th>
				<td colspan="7">
					<textarea rows="3" cols="56" name="execute" style="height: 200px;resize:vertical;" class="text">${dTemplates.execute}</textarea>
				</td>
			</tr>
			<tr>
				<th>
					${message("备注")}:
				</th>
				<td colspan="7">
					<textarea rows="3" cols="56" name="memo" style="height: 120px;resize:vertical;" class="text" >${dTemplates.memo}</textarea>
				</td>
			</tr>
			<tr>
				<th>
					${message("pdf链接")}:
				</th>
				<td colspan="7">
					<input type="text" name="pdfPath" class="text pdfPath" value="${dTemplates.pdfPath}" btn-fun="clear"/>
				</td>
			</tr>
			<tr>
				<th>
					${message("excel链接")}:
				</th>
				<td colspan="7">
					<input type="text" name="excelPath" class="text excelPath"  value="${dTemplates.excelPath}" btn-fun="clear"/>
				</td>
			</tr>
            <tr>
                <th>
                    ${message("导入文件路径")}:
                </th>
                <td colspan="7">
                    <input type="text" name="importPath" value="${dTemplates.importPath}" class="text importPath" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                <th>
                    ${message("导入模板路径")}:
                </th>
                <td colspan="7">
                    <input type="text" name="importTemp" value="${dTemplates.importTemp}" class="text importTemp" btn-fun="clear"/>
                </td>
            </tr>
		</table>
		<div class="fixed-top">
			<a href="javascript:add(this);" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
	        <input type="button" onclick="deleteDTemplates(this)" class="button" style="background: firebrick;" value="${message("删除")}">
	        <input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
    <!--按钮控制-->
    <form id="buttonForm" action="" method="get">
        <input type="hidden" class="text" name="dTemplatesId" value="${dTemplates.id}"/>
        <div class="title-style" style="margin-top: 5px;">
            ${message("按钮控制")}:
            <div class="btns">
                <a href="javascript:;" id="addTempButton" class="button">新增</a>
            </div>
        </div>
        <div class="table-responsive">
            <table id="table_button"></table>
            <div id="body-paginator" style="text-align:left;">
                <div id="buttonPaginator"></div>
            </div>
        </div>
    </form>
	<!-- 模板参数 -->
	<form id="parameterForm" action="" method="get">
		<input type="hidden" class="text" name="dTemplatesId" value="${dTemplates.id}"/>
		<div class="title-style" style="margin-top: 5px;">
			${message("模板参数")}:
			<div class="btns">
				<a href="javascript:;" id="addParameters" class="button">新增</a>
			</div>
			<span style="color: #804315;">(参数提示：$userId 用户Id，$username 登录用户名，$companyInfoId 组织主体，$sbuId SBU，$organizationId 经营组织，$saleOrgId 机构，$warehouseIds 仓库, $warehouseStockIds 仓库库存,$storeIds 客户)</span>
		</div>
		<div class="table-responsive">
			<table id="table_parameters"></table>
	    	<div id="body-paginator" style="text-align:left;">
	    		<div id="parametersPaginator"></div>
	   		</div>
		</div>
	</form>
	<!--模板列-->
	<form id="columusForm" action="" method="get">
		<input type="hidden" class="text" name="dTemplatesId" value="${dTemplates.id}"/>
		<div class="title-style" style="margin-top: 5px;">
			${message("模板列")}:
			<div class="btns">
				<a href="javascript:;" id="addColumus" class="button">新增</a>
			</div>
			<div class="btns">
				<a href="javascript:;" id="columusImport" class="button">导入</a>
			</div>
            <div class="btns">
                <a href="/template/dTemplateColumns/dTemplateColumnsExport.jhtml?tempId=${dTemplates.id}" id="columusExport" class="button">导出</a>
            </div>
		</div>
		<div class="table-responsive">
			<table id="table_columns"></table>
	    	<div id="body-paginator" style="text-align:left;">
	    		<div id="columnsPaginator"></div>
	   		</div>
		</div>
	</form>
	<!-- 模板用户 -->
	<form id="usersTemplateForm" action="" method="get">
		<input type="hidden" class="text" name="dTemplatesId" value="${dTemplates.id}"/>
		<div class="title-style" style="margin-top: 5px;">
			${message("用户模板")}:
		</div>
		<div class="table-responsive">
			<table id="table_users_template"></table>
	    	<div id="body-paginator" style="text-align:left;">
	    		<div id="usersTemplatePaginator"></div>
	   		</div>
		</div>
	</form>
</div>
</body>
</html>