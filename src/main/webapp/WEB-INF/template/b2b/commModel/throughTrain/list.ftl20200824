<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("动态表单列")}</title>
	<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
	<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
	<script type="text/javascript" src="/resources/js/base/request.js"></script>
	<script type="text/javascript" src="/resources/js/base/global.js"></script>
	<script type="text/javascript" src="/resources/js/dynamicForm/formList.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript">
		var userId = ${userId};
		var templateId = ${templateId};
		$().ready(function() {
			traverseList(userId,templateId);
		});

		//返回配置选择字段页
		function  restColumns(e) {
			parent.change_tab(0,'/template/dTemplateUsers/choice_list.jhtml');
		}

	</script>
</head>
<body>
<form id="listForm" action="/template/dUserColumns/selectUserTemplateData.jhtml" method="post">
    <input type="hidden" name="userId" value="${userId}"/>
    <input type="hidden" name="templateId" value="${templateId}"/>
    <input type="hidden" name="jumpPageUrl" value="/b2b/commModel/throughTrain/list"/>
	<div class="bar">
		<div class="buttonWrap">
			<div class="flag-wrap flagImp-wrap">
				<a href="javascript:segmentedExport(this,${userId},${templateId});" class="iconButton" id="export1Button">
					<span class="impIcon">&nbsp;</span>导出
				</a>
			</div>
			<a href="javascript:restColumns();" class="button" id="restButton"><span class="backButton">&nbsp;</span>${message("重新配置")}</a>
		</div>
		<div id="searchDiv">
			<div id="search-content">
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
		<div id="body-paginator" style="text-align: left;">
			<div id="paginator"></div>
		</div>
	</div>
</form>
</body>
</html>