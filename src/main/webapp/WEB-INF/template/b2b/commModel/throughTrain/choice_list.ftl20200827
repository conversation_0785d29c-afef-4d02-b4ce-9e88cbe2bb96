<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("选择表单字段")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/model.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">

	$().ready(function() {
		var rightIndex = -1;
        var $selectDModules = $("#selectDModules");
        var $selectUser = $("#selectUser");

		//双击鼠标将左边当前li移到另一边
		$("#shuttleLeft").on('dblclick', 'li', function() {
			var html = $(this).html();
			$("#shuttleRight").append("<li>"+html+"</li>");
			$(this).remove();
		});

		//双击鼠标将右边当前li移到另一边
		$("#shuttleRight").on('dblclick', 'li', function() {
			var html = $(this).html();
			$("#shuttleLeft").append("<li>"+html+"</li>");
			$(this).remove();
		});

		//单个左右移动
        $("#toRightSingle").click(function() {
            if($("#shuttleLeft .Path").length == 0) return false;
            $("#shuttleLeft").find('.Path').appendTo("#shuttleRight");
            $("#shuttleRight li").removeClass('Path');
            $(this).removeClass('Path');
            $(this).addClass('btnColor');

        });
        $("#toLeftSingle").click(function() {
            if($("#shuttleRight .Path").length == 0) return false;
            $("#shuttleRight .Path").appendTo("#shuttleLeft");
            $("#shuttleLeft li").removeClass('Path');
            $(this).removeClass('Path');
            $(this).addClass('btnColor');
        });

        //shuttleLeft 单击事件
        $("#shuttleLeft").on('click', 'li', function() {
            $(this).siblings('li').removeClass('Path');
            $(this).toggleClass("Path");
            // $("#toRightSingle").toggleClass('btnColor');
            // $("#toRightSingle").toggleClass("Path");
        });

		//shuttleRight 单击事件
		$("#shuttleRight").on('click', 'li', function() {
			rightIndex = $(this).index();
			$(this).siblings('li').removeClass('Path');
			$(this).toggleClass("Path");
            // $("#toLeftSingle").toggleClass('btnColor');
            // $("#toLeftSingle").toggleClass("Path");
		});

		//添加所有可选
		$("#toRight").click(function() {
			var html =  $("#shuttleLeft").html();
			$("#shuttleRight").append(html);
			$("#shuttleLeft").html("");
		});
		//移除所有已选
		$("#toLeft").click(function() {
			var html =  $("#shuttleRight").html();
			$("#shuttleLeft").append(html);
			$("#shuttleRight").html("");
		});

		$('#toTop').click(function() {
			if(rightIndex > -1){
				if (rightIndex != 0){
					$('#shuttleRight li')[rightIndex-1].before($('#shuttleRight li')[rightIndex]);
					//清除
				//	$("#shuttleRight li").removeClass('Path');
                    rightIndex = rightIndex-1;
				}else {
					$.message_alert('当前无法上移！');
				}
			}else {
				$.message_alert('请先选择一个移动项！');
			}
		});
		$('#toBottom').click(function() {
			if(rightIndex > -1){
				if (rightIndex != $('#shuttleRight li').length-1){
					$('#shuttleRight li')[rightIndex+1].after($('#shuttleRight li')[rightIndex]);
					//清除
					//$("#shuttleRight li").removeClass('Path');
					rightIndex = rightIndex+1;
				}else {
					$.message_alert('当前无法下移！');
				}
			}else {
				$.message_alert('请先选择一个移动项！');
			}
		});

		//置顶
        $('#toOne').click(function() {
            if(rightIndex > -1){
                if (rightIndex != 0){
                    $('#shuttleRight li')[0].before($('#shuttleRight li')[rightIndex]);
                    rightIndex = 0;
                }else {
                    $.message_alert('当前无法上移！');
                }
            }else {
                $.message_alert('请先选择一个移动项！');
            }
        });

        //置底
        $('#toLast').click(function() {
            if(rightIndex > -1){
                if (rightIndex != $('#shuttleRight li').length-1){
                    $('#shuttleRight li')[$('#shuttleRight li').length-1].after($('#shuttleRight li')[rightIndex]);
                    rightIndex = $('#shuttleRight li').length-1;
                }else {
                    $.message_alert('当前无法下移！');
                }
            }else {
                $.message_alert('请先选择一个移动项！');
            }
        });



        //查询模块
        $selectDModules.bindQueryBtn({
            type:'dModules',
            title:'查询模块',
            url:'/template/dModules/select_dModules.jhtml?multi=1&isEmpty=1',
            callback:function(rows){
                if(rows.length>0){
                    var row = rows[0];
                    $("input.modulesId").val(row.id);
                    $("input.modulesName").val(row.name);
					$("input.parentdmodulesId").val(row.parentdmodules);
                }else{
                    $("input.modulesId").val('');
                    $("input.modulesName").val('');
					$("input.parentdmodulesId").val('');
                }
            }
        });

        //查询用户
        $selectUser.bindQueryBtn({
            type:'storeMember',
            title:'${message("查询用户")}',
            url:'/member/store_member/select_store_member.jhtml?multi=2',
            callback:function(rows){
                if(rows.length>0){
                    var row = rows[0];
                    $("input.userId").val(row.id);
                    $("input.userName").val(row.name);
                }else{
                    $("input.userId").val('');
                    $("input.userName").val('');
                }
            }
        });

       
        //查询模板
	 	$("#selectTemplates").click(function(){
				 var parentdmodulesId = $(".parentdmodulesId").val();
				 var modelId = $(".modulesId").val();
				 if (isNull(modelId)==null){
						$.message_alert("请先选择模块信息");
						return false;
				 }
				 $("#selectTemplates").bindQueryBtn({
					type:'templates',
					title:'${message("查询模板")}',
					bindClick:false,
					url:'/template/dTemplates/select_list.jhtml?modelId='+modelId+'&parentdmodulesId='+parentdmodulesId,
					callback:function(rows){
						if(rows.length>0){
							var row = rows[0];
							$("input.templatesId").val(row.id);
							$("input.templatesName").val(row.name);
							var userId = $("input.userId").val();
							//获取字段列表
							$.ajax({
								url:'/template/dTemplateColumns/getColumnsByTemplate.jhtml?templateId='+row.id+'&userId='+userId,
								method:"get",
								async: false,
								success : function(resultMsg) {
									var datas = $.parseJSON(resultMsg.content);
									if (datas.length>0){
										//清空原来的
										$("#shuttleLeft").html("");
										$("#shuttleRight").html("");
										for (var i=0;i<datas.length;i++){
											var data = datas[i];
                                            var html = '<li><input type="hidden" name="id" class="text id" value="'+data.id+'"/>'+data.show_name+'<input type="checkbox" name="searchID" class="searchID" value="'+data.id+'"/></li>';
                                            if (isNull(data.id)!=null && isNull(data.ducId)==null){
												$("#shuttleLeft").append(html);
											}else if (isNull(data.id)!=null && isNull(data.ducId)!=null) {
												$("#shuttleRight").append(html);
											}
										}
									}
								}
							});
						}else{
							$("input.templatesId").val('');
							$("input.templatesName").val('');
						}
					}
			   });
		});
	});

	//提交
	function submit(e) {
		//获取li下面的input
		var ids = $("#inputForm").serialize();
        var searchIDs = [];
        $("input[name='searchID']:checked").each(function(i){
            searchIDs.push($(this).val());
        });
        var ids11 = $("#inputForm").serializeArray();
        if (ids11.length==0){
            $.message_alert("选择列表至少要有一个");
        }else {
            var templateId = $(".templatesId").val();
            var userId = $(".userId").val();
            var data = {"ids":ids,"templatesId":templateId,"userId":userId};
            $.ajax({
                type: 'POST',
                url: '/template/dTemplateUsers/saveUserTemplate.jhtml',
                data: data,
                success: function(resultMsg) {
                    // $.message_alert(resultMsg.content);
                    parent.change_tab(1,'/template/dUserColumns/list.jhtml?templateId='+templateId+"&userId="+userId+"&jumpPageUrl=/b2b/commModel/throughTrain/list");
                },
            });
        }
	}
	
	//查看上次配置列表
	function lastConfiguration(e) {
		//获取用户信息
		var templateId = $(".templatesId").val();
		var userId = $(".userId").val();
		parent.change_tab(1,'/template/dUserColumns/list.jhtml?templateId='+templateId+"&userId="+userId+"&jumpPageUrl=/b2b/commModel/throughTrain/list");
	}
	
</script>
	<style type="text/css">
		#choice_title{
			margin-left: 20px;
			width: 98%;
		}
		#choice_title table tr{
			line-height: 50px;
			width: 250px;
			text-align: left;
		}
		#choice_title table td{
			font-size: 14px;
            margin-right: 10px;
		}
		#choice_title table td select,option{
			width: 150px;
			height: 30px;
			border: 1px solid #e2e2e2;
		}

	</style>
</head>
<body>
	<div class="pathh">&nbsp;${message("表单配置")}
	</div>
		<div id="choice_title">
			<table>
				<tr>
					<td>模块：</td>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="modulesId" class="text modulesId" btn-fun="clear" value=""/>
							<input type="hidden" name="parentdmodulesId" class="text parentdmodulesId" btn-fun="clear" value=""/>
							<input type="text" class="text modulesName" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
							<input type="button" class="iconSearch" value="" id="selectDModules">
					    </span>
					</td>
					<td>&nbsp;&nbsp;&nbsp;模板：</td>
                    <td>
                        <span class="search" style="position:relative">
							<input type="hidden" name="templatesId" class="text templatesId" btn-fun="clear" value=""/>
							<input type="text" class="text templatesName" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
							<input type="button" class="iconSearch" value="" id="selectTemplates">
					    </span>
                    </td>
                    <td>&nbsp;&nbsp;&nbsp;用户：</td>
                    <td>
                        <span class="search" style="position:relative">
							<input type="hidden" name="userId" class="text userId" btn-fun="clear" value=""/>
							<input type="text" class="text userName" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
							<input type="button" class="iconSearch" value="" id="selectUser">
					    </span>
                    </td>
				</tr>
			</table>
		</div>

		<div style="width:600px;margin: auto;overflow: hidden;padding: 5px;padding-bottom: 20px;">
			<div style="text-align: center;">
				<div class="box">
					<div class="shutHeader">可选字段</div>
					<ul id="shuttleLeft">
					</ul>
				</div>
				<div id="btn">
                    <button id="toRightSingle" class="btnColor">&gt;</button>
                    <button id="toLeftSingle" class="btnColor">&lt;</button>
					<button id="toRight" class="btnColor">&gt;&gt;</button>
					<button id="toLeft" class="btnColor">&lt;&lt;</button>
				</div>
				<form id="inputForm" action="" method="get">
					<div class="box">
						<div class="shutHeader">已选字段/是否条件</div>
						<ul id="shuttleRight">
						</ul>
					</div>
				</form>
			</div>

			<div id="top_And_bottom">
                <button id="toOne">&uArr;</button>
				<button id="toTop">&and;</button>
				<button id="toBottom">&or;</button>
                <button id="toLast">&dArr;</button>
			</div>
		</div>
	<div class="formButton" style="float: right;">
		<input type="button" onclick="submit(this)"  class="button" value="${message("确认")}">
		<input type="button" onclick="lastConfiguration(this)"  class="button" style="background:darksalmon;" value="${message("上次配置")}">
		<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
	</div>
</body>
</html>