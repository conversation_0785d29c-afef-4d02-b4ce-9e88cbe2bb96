<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("移库")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="/resources/js/utils.js"></script>

	<script type="text/javascript">
function add(){
	parent.change_tab(0,'/b2b/move_library/add.jhtml');
}
$().ready(function() {
	initMultipleSelect();
	//查询来源仓库
	$("#selectIssueWarehouse").click(function(){
       	$("#selectWarehouse").bindQueryBtn({
            type:'warehouse',
            bindClick:false,
			title:'${message("查询仓库")}',
			url:'/stock/warehouse/select_warehouse.jhtml',
			callback:function(rows){
           		if(rows.length>0){
           			var row = rows[0];
           			$(".issueWarehouseId").val(row.id);
           			$(".issueWarehouseName").val(row.name);
           		}
			}
		});
	})
	
	//查询目标仓库
	$("#selectReceiveWarehouse").click(function(){
       	$("#selectWarehouse").bindQueryBtn({
            type:'warehouse',
            bindClick:false,
			title:'${message("查询仓库")}',
			url:'/stock/warehouse/select_warehouse.jhtml',
			callback:function(rows){
           		if(rows.length>0){
           			var row = rows[0];
           			$(".receiveWarehouseId").val(row.id);
           			$(".receiveWarehouseName").val(row.name);
           		}
			}
		});
	})
	
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productName']").val();
				}
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('产品【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					allName =allName +','+ rows[i].name  
					vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="closePro(this)"></i></div>'
					$(".product").append(vhtml);
				}
				$("input[name='productName']").attr("value",allName)
			}
		}
	});
	
	
	var wf_state ={'0':'未启动','1':'审核中','2':'已完成','3':'驳回',} //审批流程
	var move_sttus = {'0':'已保存','1':'审核中','2':'已作废','3':'已审核','4':'已关闭','5':'已完成'}; //移库状态
	var move_sttuss={'0':'未发出','1':'已发出','2':'部分发出'}; //发货状态
	var move_receive_status={'0':'未接收','1':'完成接收 ','2':'部分接收'}; //接收状态
	var cols = [
		{ title:'${message("移库单号")}', name:'sn' ,width:100,align:'center',renderer:function(val,item){
			if(item.move_status!=0 && item.move_status!=2 && item.move_status!=null && item.move_status!=""){
				return '<a href="javascript:void(0);" onclick="check_pdf(this,'+item.id+')" title="查看PDF"><img style="width: 20px;" src="/resources/images/ic_pdf.png"></a>&nbsp;<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/b2b/move_library/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
			}else{
				return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/b2b/move_library/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';			
			}
		}},
		{ title:'${message("来源仓")}', name:'issue_name' ,align:'center',width:80 },
		{ title:'${message("目标仓")}', name:'receive_name' ,align:'center',width:80 },
		{ title:'${message("移库状态")}', name:'move_status' ,width:60, align:'center', renderer: function(val){
			var result = move_sttus[val];
			if(result!=undefined)return result;			
		}},
		{ title:'${message("发出状态")}', name:'move_statuss' ,width:60, align:'center', renderer: function(val){
			var result = move_sttuss[val];
			if(result!=undefined)return result;			
		}},
		{ title:'${message("接收状态")}', name:'move_receive_status' ,width:60, align:'center', renderer: function(val){
			var result = move_receive_status[val];
			if(result!=undefined)return result;			
		}},
		{ title:'${message("流程状态")}', name:'wf_state' ,width:50, align:'center', renderer: function(val){
			var result = wf_state[val];
			if(result!=undefined)return result;			
		}},
		{ title:'${message("需求日期")}', name:'need_date',width:80,align:'center'},
		{ title:'${message("发出日期")}', name:'issue_date',align:'center',width:80},
		{ title:'${message("接收日期")}', name:'receive_date',align:'center',width:80},
		{ title:'${message("SBU")}', name:'sbu_name',align:'center',width:60},
		{ title:'移库单明细' ,align: 'center', cols: [
		           { title:'${message("产品名称")}', name:'product_name' ,width:80, isLines:true, align:'center'},
		           { title:'${message("产品编码")}', name:'vonder_code' ,width:120, isLines:true, align:'center'},
		           { title:'${message("产品描述")}', name:'detail_description' ,width:200, isLines:true, align:'center'},
		           { title:'${message("产品型号")}', name:'product_model' ,width:60, isLines:true, align:'center'},
		           { title:'${message("产品规格")}', name:'product_spec' ,width:80, isLines:true, align:'center'},		           
		           { title:'${message("经营组织")}', name:'product_organization_name' ,width:80, isLines:true, align:'center'},
		           { title:'${message("产品等级")}', name:'levelName' ,width:60, isLines:true, align:'center', renderer: function(val){
		   				return val;			
		   			}},
		           { title:'${message("数量")}', name:'quantity' ,width:80, isLines:true, align:'center'},
		           { title:'${message("箱数")}', name:'box_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("支数")}', name:'branch_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("零散支数")}', name:'scattered_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("发出数量")}', name:'issue_quantity' ,width:60, isLines:true, align:'center'},
				   { title:'${message("发出箱数")}', name:'issue_box_quantity', align:'center', isLines:true, width:60,renderer:function(val,item){
						   var issueBoxQuantitys = 0;
                           if (isNull(item.issue_branch_quantity)!=null) {
							   issueBoxQuantitys = item.issue_branch_quantity / item.branch_per_box;
						   }
						   return '<span class="issueBoxQuantitys">'+Math.floor(issueBoxQuantitys)+'</span>';
                   }},
		           //{ title:'${message("发出箱数")}', name:'issue_box_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("发出支数")}', name:'issue_branch_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("接收数量")}', name:'receive_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("接收箱数")}', name:'receive_box_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("接收支数")}', name:'receive_branch_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("关闭数量")}', name:'close_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("关闭箱数")}', name:'close_box_quantity' ,width:60, isLines:true, align:'center'},
		           { title:'${message("关闭支数")}', name:'close_branch_quantity' ,width:60, isLines:true, align:'center'},
		    	   [#if hiddenBatch !=0]  
		    	 		{ title:'${message("色号")}', name:'color_numbers_name' ,width:50, isLines:true, align:'center'},
		    	 		{ title:'${message("含水率")}', name:'moisture_content_name' ,width:80, isLines:true, align:'center'},
		    	 		{ title:'${message("批次")}', name:'batch_encoding' ,width:80, isLines:true, align:'center'},
		    	   [/#if]  
		           { title:'${message("移库明细备注")}', name:'move_library_item_remarks' ,width:100, isLines:true, align:'center'},
		 ]},
		 { title:'${message("收货人")}', name:'sender',align:'center',width:60},
		 { title:'${message("联系电话")}', name:'mobile',align:'center',width:70},
		 { title:'${message("详细地址")}', name:'address',align:'center',width:120},
		 { title:'${message("创建人")}', name:'store_member_name',align:'center',width:60},
		 { title:'${message("创建时间")}', name:'create_date',align:'center',width:80},
		 { title:'${message("备注")}', name:'move_library_remarks',align:'center',width:100}
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/b2b/move_library/list_data.jhtml',
        lineRoot:"move_library_items",
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	//查询客户
	$("#selectStoreMember").bindQueryBtn({
		type:'storeMember',
		title:'${message("查询用户")}',
		url:'/member/store_member/select_store_member.jhtml?isMember=1'
	});
	
});

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = 'to_condition_export.jhtml';//分页导出统计页面
	var url = '/b2b/move_library/condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
    var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的订单！")}';//提示
	var url = '/b2b/move_library/selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

//查看pdf
function check_pdf(e,id){
	ajaxSubmit(e,{
		url:"checkPdf.jhtml",
		method:"post",
		data:{ids:id},
		async: false,
		callback:function(resultMsg){
			window.open(resultMsg.content);
		}
	});
}

</script>
</head>
<body>
	<form id="listForm" action="/b2b/move_library/list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导出
					</a>
					<ul class="flag-list">
						<li>
							<a href="javascript:void(0)" onclick="exportExcel(this)">
								<i class="flag-imp02"></i>
								${message("选择导出")}
							</a>
						</li>
						<li>
							<a href="javascript:void(0)" onclick="segmentedExport(this)">
								<i class="flag-imp02"></i>
								${message("条件导出")}
							</a>
						</li>
					</ul>
				</div>
				[#if moveLibraryRoles >0] 
					<a href="javascript:add();" class="iconButton" id="addButton">
						<span class="addIcon">&nbsp;</span>
						${message("新增")}
					</a>
				[/#if]
			</div>
			<div id="searchDiv">
	        	<div id="search-content" >
	        		<dl>
					 	<dt><p>${message("移库单号")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="sn" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
					  	<dt><p>${message("状态")}:</p></dt>
					  	<dd>
							<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="${message("已保存")}" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			<label><input  class="check js-iname" name="status" value="0" type="checkbox"  checked />${message("已保存")}</label>
					       			<label><input  class="check js-iname" name="status" value="1" type="checkbox"  />${message("审核中")}</label>
					       			<label><input  class="check js-iname" name="status" value="2" type="checkbox" />${message("已作废")}</label>
					       			<label><input  class="check js-iname" name="status" value="3" type="checkbox"  />${message("已审核")}</label>
					       			<label><input  class="check js-iname" name="status" value="4" type="checkbox"  />${message("已关闭")}</label>
					       			<label><input  class="check js-iname" name="status" value="5" type="checkbox"  />${message("已完成")}</label>
					       		</div>
					       	</div>
		    			</dd>
					</dl>
					<dl>
	        			<dt><p>${message("流程状态")}：</p></dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
									<label><input class="check js-iname" name="wfStates" value="0" type="checkbox"/>${message("未启动")}</label>
									<label><input class="check js-iname" name="wfStates" value="1" type="checkbox"/>${message("审核中")}</label>
									<label><input class="check js-iname" name="wfStates" value="2" type="checkbox"/>${message("已完成")}</label>
									<label><input class="check js-iname" name="wfStates" value="3" type="checkbox"/>${message("驳回")}</label>
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
					<dl>
					  	<dt><p>${message("发货状态")}:</p></dt>
					  	<dd>
							<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			<label><input  class="check js-iname" name="moveSttuss" value="0" type="checkbox" />${message("未发出")}</label>
					       			<label><input  class="check js-iname" name="moveSttuss" value="1" type="checkbox" />${message("已发出")}</label>
					       			<label><input  class="check js-iname" name="moveSttuss" value="2" type="checkbox" />${message("部分发出")}</label>
					       		</div>
					       	</div>
		    			</dd>
					</dl>
					<dl>
					  	<dt><p>${message("接收状态")}:</p></dt>
					  	<dd>
							<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			<label><input  class="check js-iname" name="moveReceiveStatus" value="0" type="checkbox" />${message("未接收")}</label>
					       			<label><input  class="check js-iname" name="moveReceiveStatus" value="1" type="checkbox" />${message("完成接收")}</label>
					       			<label><input  class="check js-iname" name="moveReceiveStatus" value="2" type="checkbox" />${message("部分接收")}</label>
					       		</div>
					       	</div>
		    			</dd>
					</dl>
					<dl>
						<dt><p>${message("来源仓")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="issueWarehouseId" class="text issueWarehouseId" btn-fun="clear" />
							<input type="text" name="issueWarehouseName" class="text issueWarehouseName" maxlength="200" onkeyup="clearSelect(this)" readonly/>
							<input type="button" class="iconSearch" id="selectIssueWarehouse">
							<div class="pupTitleName  warehouse"></div>
							</span>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("目标仓")}:</p></dt> 
						<dd>
							<span class="search" style="position:relative">
								<input type="hidden" name="receiveWarehouseId" class="text receiveWarehouseId" btn-fun="clear" />
								<input type="text" name="receiveWarehouseName" class="text receiveWarehouseName" maxlength="200" onkeyup="clearSelect(this)" readonly/>
								<input type="button" class="iconSearch" id="selectReceiveWarehouse">
								<div class="pupTitleName  warehouse"></div>
							</span>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("产品名称")}:</p></dt>
	    				<dd>
	    					<span style="position:relative">
								<input class="text productName" maxlength="200" type="text" name="productName" value="" onkeyup="clearSelect(this)" readonly>
								<input type="button" class="iconSearch" value="" id="selectProduct">
								<div class="pupTitleName product"></div>
							</span>
	    				</dd>
	    			</dl>
					<dl>
					 	<dt><p>${message("创建人")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="storeMemberName" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
						<dt><p>${message("创建时间")}:</p></dt>
						<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("发出日期")}:</p></dt>
						<dd class="date-wrap">
							<input id="firstIssueDate" name="firstIssueDate" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="lastIssueDate" name="lastIssueDate" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("接收日期")}:</p></dt>
						<dd class="date-wrap">
							<input id="firstReceiveDate" name="firstReceiveDate" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="lastReceiveDate" name="lastReceiveDate" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
					</dl>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>