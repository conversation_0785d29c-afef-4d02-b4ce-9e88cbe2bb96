<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑移库单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />

<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<style>
	#tab{border="1px solid #d0cdcd";}
	#tab .ssdtop{width: 80px;height: 35px;border: 1px solid #d0cdcd;background-color:#e6e6e6;}
	#tab .content{width: 80px;height: 35px;}
	#tab .conne{text-align:center;}
	#tab .connetop{font-weight:500;}
	.mmGrid .nowrap{overflow:visible !important;}
	
</style>
<script type="text/javascript">

	function productOrganization(){
		
		var issueWarehouseId = $("input.issueWarehouseId").val();
		$("input.productId").each(function(){
			var $this = $(this);
			var productId = $this.val();
			var $tr = $this.closest("tr");
			[#if organizationIsNoWarehouse == 0 ] 
				$tr.find(".productOrganizationName").text($(".organizationName").text());
				$tr.find("input.productOrganizationId").val($("input.organizationId").val());
			[#elseif organizationIsNoWarehouse == 1 ] 
				if(productId!=null && productId!='' && productId!=undefined){
					$.ajax({
						url : '/product/product/findProductOrganizationList.jhtml',
		    			type : "post",
		    			data : {id:productId},
		    			success : function(data) {
		    				var content = JSON.parse(data.content);
		    				if(content.length!=0){
		    					var html = "";
		    					var organizationId = null;
		    					var moveLibraryItemId = $tr.find(".moveLibraryItemId").val();
		    					var productOrganizationId = $tr.find(".productOrganization option:selected").val();
		    					var productOrganizationText = $tr.find(".productOrganization option:selected").text();
		    					var isTrue = true;
		    					for(var i=0; i<content.length; i++){
		    						var isDefaults = content[i].is_defaults;
		    						if(moveLibraryItemId !='undefined' && moveLibraryItemId != null && moveLibraryItemId !=''){
		    							if(productOrganizationId !=content[i].organization){
		    								html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';
		    							}
		    						}else{
		    							html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';    
		    							if(productOrganizationId==content[i].organization){
		    								isTrue = false;
		    								organizationId = content[i].organization;
		    							}else{
			        						if(i==0){
			    								organizationId = content[i].organization;
			    							}else if(isDefaults==1 && isTrue){
			    								organizationId = content[i].organization;
			    							}
		    							}
		    						}
		    					}
		    					if(moveLibraryItemId !='undefined' && moveLibraryItemId != null && moveLibraryItemId !=''){
		    							if(productOrganizationId != null && productOrganizationId != '' && productOrganizationId !='undefined'){
		    								organizationId = productOrganizationId;
			    							html += '<option value="'+productOrganizationId+'">'+productOrganizationText+'</option>';
		    							}else{
		    								organizationId = '';
			    							html += '<option value="">请选择</option>';
		    							}	
		    					}
		    					$tr.find(".productOrganization").empty();
		    					$tr.find(".productOrganization").html(html);
	    						$tr.find(".productOrganization option[value='"+organizationId+"']").attr("selected",true);
		    					[#if linkStock == 1 ] 
			    					if(organizationId!=null && organizationId!=''){
			    						//等级
			    						var productGrade = $tr.find(".productGrade").val();
			    						//编码
			    						var vonderCode = $tr.find(".vonderCode").val();
			    						//色号
			    						var colourNumber = $tr.find(".colourNumber").val();
			    						if(colourNumber=='undefined' || colourNumber.length == 0){
			    							colourNumber = "";
			    						}
			    						//含水率
			    						var moistureContent = $tr.find(".moistureContent").val();
			    						if(moistureContent=='undefined' || moistureContent.length == 0){
			    							moistureContent = "";
			    						}
			    						var params='&productGrade='+productGrade+'&vonderCode='+vonderCode+'&colourNumber='+colourNumber
			    		    			   +'&moistureContent='+moistureContent+'&organizationId='+organizationId+'&warehouseId='+issueWarehouseId;
			    							params = params.substring(1,params.length);
		    							$.ajax({
		    								url:'/stock/stock/findViewStock.jhtml?'+params,
		    				    			type : "post",
		    				    			success : function(rows) {
		    				    				var data= $.parseJSON(rows.content);
	    					                    if(data.length>0){
	    					                        for (var i = 0; i < data.length;i++) {
	    					                        	if(data[i].totalOnhandQuantity1 !=null && data[i].totalOnhandQuantity1 !=''){
	    					                        		$tr.find(".onhandQuantity").text(data[i].totalOnhandQuantity1);
	    					                        	}else{
	    					                        		$tr.find(".onhandQuantity").text(0);
	    					                        	}
	    					                        	if(data[i].totalAttQuantity1 !=null && data[i].totalAttQuantity1 !=''){
	    					                        		$tr.find(".attQuantity").text(data[i].totalAttQuantity1);
	    					                        	}else{
	    					                        		$tr.find(".attQuantity").text(0);
	    					                        	} 
	    					                    	}
	    					                	}else{
	    					                		$tr.find(".onhandQuantity").text(0);
	    					                		$tr.find(".attQuantity").text(0);
	    					                	}
		    				    			}
		    							})	  
			    					}
		    					[/#if]
		    				}
		    			}
					})
				}
			[/#if]
		});
	}	

	function countTotal(t){
		var $bInput = $("input.boxQuantity");
		$bInput.each(function(){
	        var $tr = $(this).closest("tr");
	        var isEqual = null;
	        if(t!=undefined){
	            isEqual = (t.find(".line_no").text() == $tr.find(".line_no").text());
	        }
	        var boxQuantity=$(this).val();
	        var branchPerBox=$tr.find(".branchPerBox").val();
	        var issueBoxQuantity = $tr.find(".issueBoxQuantity").val();
	        var perBranch=$tr.find(".perBranch").val();
	        var perBox =$tr.find(".perBox").val();
	        var branchQuantity =$tr.find(".branchQuantity").val();
	        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
	        var type = productDisc(branchPerBox,perBranch,perBox);
	        var volume=$tr.find(".lineVolume").val();
	        var volumeAmount=Number(accMul(boxQuantity,volume)).toFixed(6);
	        if(isNaN(volumeAmount)){
	            volumeAmount = 0;
	        }  
	        $tr.find(".lineVolumeAmounts").html(volumeAmount);//体积
	        var issueVolumeAmount=Number(accMul(issueBoxQuantity,volume)).toFixed(6);
	        $tr.find(".issueVolumeAmounts").html(issueVolumeAmount);//发出体积
	        if(isEqual == null){
	        	if(type==2){
	        		 var a = accDiv(perBox,10);
	                 quantity = accMul(boxQuantity,a);                
	        	}else if(type==3){
	        		quantity = accMul(perBranch,branchQuantity);
	                $tr.find(".boxQuantity").val("");
	                $tr.find(".scatteredQuantity").val("");
	                $tr.find(".branchPerBox").val("");
	        	}else if(type==0){
	        		var branchQuantity=accMul(boxQuantity,branchPerBox);
	        		branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	        		if(isNaN(branchQuantity)){
	        			branchQuantity = 0;
	        		}
	        		var quantity=accMul(branchQuantity,perBranch);
	        	}
	        	if(isNaN(quantity)){
	        		quantity = 0;
	        	}
	        	$tr.find(".quantity").val(quantity);//平方数
	        	$tr.find(".branchQuantity").val(branchQuantity);//支数
	        }else{
	        	if(type==2){
	       		 var a = accDiv(perBox,10);
	                quantity = accMul(boxQuantity,a);
	                $tr.find(".quantity").val(quantity);//数量
	                $tr.find(".quantityStr").html(quantity);
	        	}else if(type==3){
		       		quantity = accMul(perBranch,branchQuantity);
		            $tr.find(".quantity").val(quantity);//数量
		            $tr.find(".quantityStr").html(quantity);
		            $tr.find(".boxQuantity").val("");
		            $tr.find(".scatteredQuantity").val("");
		            $tr.find(".branchPerBox").val("");	           
	        	}else if(type==0){
		       		var branchQuantity=accMul(boxQuantity,branchPerBox);
		       		branchQuantity=accAdd(branchQuantity,scatteredQuantity);
		       		if(isNaN(branchQuantity)){
		       			branchQuantity = 0;
		       		}
		       		var quantity=accMul(branchQuantity,perBranch);
		       		if(isNaN(quantity)){
		       			quantity = 0;
		       		}
		       		$tr.find(".quantity").val(quantity);//平方数
		       		$tr.find(".branchQuantity").val(branchQuantity);//支数
	        	}
	        }
		});
		
		var $input = $("input.quantity");
		$input.each(function(){
	           var $this = $(this);
	           var $tr = $this.closest("tr");
	           var price = Number($tr.find("input.price").val());
	           var amount = Number($this.val())*price;
	           if(isNaN(amount)){
	               amount = 0;
	           }
	           //金额
	           $tr.find(".amountStr").html(currency(amount,true));
	           $tr.find("input.amount").val(amount);
	           var weight=$tr.find(".lineWeight").val();
	           var issueQuantity = $tr.find(".issueQuantity").val();
	           var issueWeightAmount = Number(accMul(issueQuantity,weight)).toFixed(6);
	            if(isNaN(issueWeightAmount)){
	               issueWeightAmount = 0;
	           }
			   $tr.find(".issueWeightAmounts").html(issueWeightAmount);//发出重量
	           var weightAmount=Number(accMul($(this).val(),weight)).toFixed(6);
	           if(isNaN(weightAmount)){
	               weightAmount = 0;
	           }
	           $tr.find(".lineWeightAmounts").html(weightAmount);//重量
	
	     });
	 	 summarizing();
	}

	//Summary calculation method
	function summarizing(){
		var tr = $("#table-move tr");
		var quantity = 0;
		var branchQuantity = 0;
		var boxQuantity = 0;
		var scatteredQuantity = 0;
		var amount = 0;
		var issueQuantity = 0;
		var issueBoxQuantity = 0;
		var issueBranchQuantity = 0;
		var receiveQuantity = 0;
		var receiveBoxQuantity = 0;
		var receiveBranchQuantity = 0;
		var issueWeightAmount=0;
		var issueVolumeAmount=0;
		var lineVolumeAmount=0;
		var lineWeightAmount=0;
		tr.each(function(){
			var $this = $(this);
			quantity += $this.find(".quantity").val()*1;
			branchQuantity += $this.find(".branchQuantity").val()*1;
			boxQuantity += $this.find(".boxQuantity").val()*1;
			scatteredQuantity += $this.find(".scatteredQuantity").val()*1;
			amount += $this.find(".amount").val()*1;
			issueQuantity += $this.find(".issueQuantity").text()*1;
			issueBoxQuantity += $this.find(".issueBoxQuantity").text()*1;
			issueBranchQuantity += $this.find(".issueBranchQuantity").text()*1;
			receiveQuantity += $this.find(".receiveQuantity").text()*1;
			receiveBoxQuantity += $this.find(".receiveBoxQuantity").text()*1;
			receiveBranchQuantity += $this.find(".receiveBranchQuantity").text()*1;
			issueWeightAmount+=$this.find(".issueWeightAmounts").text()*1;
			issueVolumeAmount+=$this.find(".issueVolumeAmounts").text()*1;
			lineVolumeAmount+=$this.find(".lineVolumeAmounts").text()*1;
			lineWeightAmount+=$this.find(".lineWeightAmounts").text()*1;
		})
		$(".tQuantity").text(isNaN(quantity)?0:quantity.toFixed(6));
		$(".tBranchQuantity").text(isNaN(branchQuantity)?0:branchQuantity);
		$(".tBranchPerbox").text(isNaN(boxQuantity)?0:boxQuantity);
		$(".tScatteredQuantity").text(isNaN(scatteredQuantity)?0:scatteredQuantity);
		$(".tAmount").text(isNaN(amount)?0:amount.toFixed(6));
		$(".tIssueQuantity").text(isNaN(issueQuantity)?0:issueQuantity.toFixed(6));
		$(".tIssueBoxQuantity").text(isNaN(issueBoxQuantity)?0:issueBoxQuantity);
		$(".tIssueBranchQuantity").text(isNaN(issueBranchQuantity)?0:issueBranchQuantity);
		$(".tReceiveQuantity").text(isNaN(receiveQuantity)?0:receiveQuantity.toFixed(6));
		$(".tReceiveBoxQuantity").text(isNaN(receiveBoxQuantity)?0:receiveBoxQuantity);
		$(".summarizing").text(isNaN(receiveBranchQuantity)?0:receiveBranchQuantity);
		$(".issueVolumeAmount").text(isNaN(issueVolumeAmount)?0:issueVolumeAmount.toFixed(6));
		$(".issueWeightAmount").text(isNaN(issueWeightAmount)?0:issueWeightAmount.toFixed(6));
		$(".lineVolumeAmount").text(isNaN(lineVolumeAmount)?0:lineVolumeAmount.toFixed(6));
		$(".lineWeightAmount").text(isNaN(lineWeightAmount)?0:lineWeightAmount.toFixed(6));
	}

	function editQty(t,e){
		
		if($(t).attr("kid")=="quantity"){//平方
			if(extractNumber(t,6,false,e)){
				var $tr = $(t).closest("tr");
				var branch_quantity=0;
				var box_quantity=0;
				
				var quantity=$(t).val();
				var perBranch=$tr.find(".perBranch").val();  //每支单位数
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var scattered=0;
				if(perBranch!=0 && branchPerBox!=0){
					 branch_quantity=quantity/perBranch;
					 box_quantity=parseInt(branch_quantity/branchPerBox);
					 scattered=(branch_quantity%branchPerBox).toFixed(6);
				}
				$tr.find(".boxQuantity").val(box_quantity);
				$tr.find(".branchQuantity").val(branch_quantity);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
				
				countTotal($tr);
				$(t).val(quantity).toFixed(6);
				
			}
		}else{
			if(extractNumber(t,3,false,e)){
				var $tr = $(t).closest("tr");
				
				var branch_quantity=0;
				var box_quantity=0;
				
				if($(t).attr("kid")=="box"){//箱
					$tr.find(".scatteredQuantityStr").html(0);
					$tr.find(".scatteredQuantity").val(0);
				}else if($(t).attr("kid")=="branch"){//支
					var quantity=$(t).val();
					var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
					var box=parseInt(quantity/branchPerBox);
					var scattered=quantity%branchPerBox;
					$tr.find(".boxQuantity").val(box);
					$tr.find(".scatteredQuantityStr").html(scattered);
					$tr.find(".scatteredQuantity").val(scattered);
				}else if($(t).attr("kid")=="quantity"){//平方
					var quantity=$(t).val();
					var perBranch=$tr.find(".perBranch").val();  //每支单位数
					var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
					var branch_quantity=quantity/perBranch;
					var box_quantity=parseInt(branch_quantity/branchPerBox).toFixed(6);
					var scattered=branch_quantity%branchPerBox;
					$tr.find(".boxQuantity").val(box_quantity);
					$tr.find(".scatteredQuantityStr").html(scattered);
					$tr.find(".scatteredQuantity").val(scattered);
				}
				
				countTotal($tr);
	
			}
		}
	}
	
	$().ready(function() {
		var $inputForm = $("#inputForm");
		var $addProduct = $("#addProduct");
		// 表单验证
		$inputForm.validate({
			rules: {
				name: "required"
			} ,
			submitHandler:function(form){
				return false;
			}
		});
		// 表单验证
		$.validator.addClassRules({
			oaSn: {
				required: true
			}
		});
		//打开选择产品界面
	    $addProduct.click(function(){
	    	var $this = $(this);
			var $tr =$this.closest("tr");
			var issueWarehouseId = $(".issueWarehouseId").val();
			if (issueWarehouseId==undefined || issueWarehouseId.length == 0) {
				$.message_alert("请选择来源仓");
				return false;
			}
			/* var params='';
      	    [#if organizationIsNoWarehouse == 0 ] 
          	  	var organizationId = $(".organizationId").val();
        	  	if (organizationId=='undefined' || organizationId.length == 0) {
        	  		$.message_alert('经营组织不能为空');
	      			return false;
    	  		}
        	  	params+='&organizationId='+organizationId;
    	    [/#if] */
	        var sbuId = $(".sbuId").val();
            $addProduct.bindQueryBtn({
                type:'product',
                bindClick:false,
                title:'${message("查询产品")}',
                url:'/product/product/selectProduct.jhtml?multi=2&sbuId='+sbuId,
                callback:function(rows){
                    if(rows.length>0){
                        for (var i = 0; i < rows.length;i++) {
							var row = rows[i];
							$move_mmGrid.addRow(row,null,1);
						}	
                        countTotal();
                        summarizing();
                        productOrganization();
                    }
                }
            });
	    })
	    
		//查询来源仓库
		$("#selectIssueWarehouse").click(function(){
			var saleOrgId = $("input.saleOrgId").val();
		    if (saleOrgId=='undefined' || saleOrgId.length == 0) {
				$.message_alert("请选择机构");
				return false;
		    }
			var sbuId = $("#sbuId option:selected").val();
			if(sbuId == null || sbuId=='' || sbuId == 'undefined'){
				$.message_alert("请选择sbu");
				return false;
			}
	        $("#selectWarehouse").bindQueryBtn({
                type:'warehouse',
                bindClick:false,
				title:'${message("查询仓库")}',
				url:'/stock/warehouse/select_warehouse.jhtml?sbuId='+sbuId+'&saleOrgId='+saleOrgId,
				callback:function(rows){
            		if(rows.length>0){
            			var row = rows[0];
            			$(".issueWarehouseId").val(row.id);
            			$(".issueWarehouseName").val(row.name);
            			$(".organizationId").val(row.management_organization_id);
        				$(".organizationName").text(row.management_organization_name);
            			$move_mmGrid.removeRow();
            			summarizing();
            		}
				}
			});
		})
		
		//查询目标仓库
		$("#selectReceiveWarehouse").click(function(){
			var issueWarehouseId = $("input.issueWarehouseId").val();
			if (issueWarehouseId=='undefined' || issueWarehouseId.length == 0) {
				$.message_alert("请选择来源仓");
				return false;
		    }
			var saleOrgId = $("input.saleOrgId").val();
		    if (saleOrgId=='undefined' || saleOrgId.length == 0) {
				$.message_alert("请选择机构");
				return false;
		    }
			var sbuId = $("#sbuId option:selected").val();
			if(sbuId == null || sbuId=='' || sbuId == 'undefined'){
				$.message_alert("请选择sbu");
				return false;
			}
	        $("#selectWarehouse").bindQueryBtn({
                type:'warehouse',
                bindClick:false,
				title:'${message("查询仓库")}',
				url:'/stock/warehouse/select_warehouse.jhtml?sbuId='+sbuId+'&saleOrgId='+saleOrgId,
				callback:function(rows){
            		if(rows.length>0){
            			var row = rows[0];
            			if(issueWarehouseId==row.id){
            				 $.message_alert('来源仓库与目标仓不能一致');
                             return false;
            			}
            			$(".receiveWarehouseId").val(row.id);
            			$(".receiveWarehouseName").val(row.name);
            		}
				}
			});
		})
		
		//查询机构
		$("#selectSaleOrg").bindQueryBtn({
			type:'saleOrg',
			title:'${message("查询机构")}',
			url:'/basic/saleOrg/select_saleOrg.jhtml'
		});
		
		var items=${moveLibraryItem};
		var moveIndex=0;
		var itemStatus={"0":"未移库","1":"部分移库","2":"完全移库"};
			var move_cols = [
		     	  { title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
						return '<a href="javascript:;" class="btn-delete" onclick="deleteShipping(this)" >删除</a>';
				  }},
				  { title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
					    return '<span class="line_no">'+ moveIndex +'</span>';
				  }},
		          { title:'${message("产品编码")}', name:'vonder_code' ,align:'center', width:150, renderer: function(val,item,rowIndex, obj){
		        		var html=val+'<input type="hidden" class="text vonderCode" name="moveLibraryItems['+moveIndex+'].vonderCode" value="'+val+'">';
		        		if(obj==undefined){
		        			html +='<input type="hidden" name="moveLibraryItems['+moveIndex+'].id" class="moveLibraryItemId" value="'+item.id+'" />';
		        		}
		    			return html;
		    	  }},
		    	  { title:'${message("产品名称")}', name:'product_name' ,align:'center', width:200, renderer: function(val,item,rowIndex, obj){
		    			var name = '';
		    			if(item.name==null){
		    				name=item.product_name;
		        		}else{
		        			name=item.name;
		        		}
		    			var productId = '';
		    			if(item.product != null){
		    				productId = item.product;			
		    			}else {
		    				productId = item.id;	
		    			}
		    			var	html = name+'<input type="hidden" name="moveLibraryItems['+moveIndex+'].product.id"  class="productId productId_'+productId+'" value="'+productId+'" />'+
		    				'<input type="hidden" class="text productName" name="moveLibraryItems['+moveIndex+'].productName" readonly value="'+name+'" />';
		    			return html;
		    	  }},  
		    	  { title:'${message("产品描述")}', name:'description' ,align:'center', width:500},
		    	  { title:'${message("产品型号")}', align:'center',name:'model',renderer: function(val,item,rowIndex, obj){
		    			var model = val;
		    			if(obj == undefined){
		    				model = item.product_model;
		    			}
		    			return model; 
		    	  }},
		    	  { title:'${message("规格")}', align:'center',name:'spec',renderer: function(val,item,rowIndex, obj){
		    			var spec = val;
		    			if(obj == undefined){
		    				spec = item.product_spec;
		    			}
		    			return spec; 
		    	  }},
		    	  { title:'${message("单位")}', align:'center',name:'unit',renderer: function(val,item,rowIndex, obj){
		    			var unit = val;
		    			if(obj == undefined){
		    				unit = item.unit;
		    			}
		    			return unit; 
		    	  }},
		     	  { title:'${message("数量")}', align:'center',name:'quantity',renderer: function(val,item,rowIndex, obj){
		     			var quantity = 1;
		    			if(obj==undefined){
		    				quantity = val;
		    			}
		    			var text = '<div class="lh20">'+
		    						'<div class="nums-input ov">'+
		    			            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
		    			            	'<input type="text" kid="box" class="t quantity"  name="moveLibraryItems['+moveIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		    			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
		    			            	'<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
		    			        	'</div>';
		    			text += '</div>';
		    			return text;
		    	  }},
		     	  { title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		    			var branchQuantity='';
		    			if(obj==undefined){
		    				branchQuantity = val;
		    			}
		    			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
		    			if(type==1||type==2){
		    				branchQuantity=0;
		    				return '-'+
		    				'<input type="hidden"  class="t branchQuantity"  name="moveLibraryItems['+moveIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0" >'+
		    				'<input type=hidden class="branchPerBox" name="moveLibraryItems['+moveIndex+'].branchPerBox"  value="'+item.branch_per_box+'" /> '+
	    					'<input type=hidden class="perBranch" name="moveLibraryItems['+moveIndex+'].perBranch" value="'+item.per_branch+'" />';
		    			}else{
		    				var text = '<div class="lh20">'+
		    					'<div class="nums-input ov">'+
		    	        			'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
		    	        			'<input type="text" kid="branch" class="t branchQuantity"  name="moveLibraryItems['+moveIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		    	        			'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
		    	        			'<input type=hidden class="branchPerBox" name="moveLibraryItems['+moveIndex+'].branchPerBox"  value="'+item.branch_per_box+'" /> '+
		    						'<input type=hidden class="perBranch" name="moveLibraryItems['+moveIndex+'].perBranch" value="'+item.per_branch+'" />'+
		    	        			'</div></div>';
		    	    		return text;
		    			}
		    	   }},
		    	   { title:'${message("箱数")}', name:'box_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
		    			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
		    			var quantity = 1;
		    			if(obj==undefined){
		    				quantity = val;
		    			}
		    			if(type==1||type==3){
		    				quantity=0;
		    				return '-'+
		    				'<input type="hidden" kid="box" class="t boxQuantity"  name="moveLibraryItems['+moveIndex+'].boxQuantity" value="'+quantity+'" minData="0"  >';
		    			}else{
		    				var text = '<div class="lh20">'+
		    						'<div class="nums-input ov">'+
		    			            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
		    			            	'<input type="text" kid="box" class="t boxQuantity"  name="moveLibraryItems['+moveIndex+'].boxQuantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		    			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
		    			        	'</div>';
		    				text += '</div>';
		    				return text;
		    			}
		    		}},
		    		{ title:'${message("零散支数")}', name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
		    			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
		    			var branchQuantity='';
		    			if(obj==undefined){
		    				branchQuantity = val;
		    			}
		    			if(type==2||type==3||type==1){
		    				return '-';
		    			}
		    			var html='<span class="scatteredQuantityStr">'+val+'</span>'+
		    				'<input type="hidden" name="moveLibraryItems['+moveIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+val+'" />';
		    			return html;
		    		}},
		    		{ title:'${message("体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
	                        var volume=item.volume;
	                        if(volume==''){
	                            volume=0;
	                        }
	                        return '<span class="lineVolumeAmounts"></span><input class="lineVolume" value="'+volume+'" type="hidden" />';
	                }},
	                { title:'${message("单价")}',[#if auditedUpdateProductPrice==0]hidden:true,[/#if] name:'price' ,align:'center', width:100,renderer:function(val,item,rowIndex, obj){
	                    var html = '<div class="pd">';
	                    var price = item.price == null || item.price=='' ? 0:item.price;
		  					html+= '<div class="lh20"><div class="priceDiv nums-input ov">'+
		  				                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
		  				                '<input type="text" minData="0" name="moveLibraryItems['+moveIndex+'].price" kid="price" class="price t" value="'+price+'" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" />'+
		  				                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
		  			                '</div></div>';
		  			
		  					html+='</div>';
	                    return html;
	                }},
	                { title:'金额',align:'center',[#if auditedUpdateProductPrice==0]hidden:true,[/#if]renderer: function(val,item,rowIndex, obj){
	                      var price = item.price == null || item.price == '' ? 0:item.price;
	                      var quantity = item.quantity == null || item.quantity == '' ? 0:item.quantity;
	                      var amountStr = currency(quantity*price,true);
	                      var html='<sapn class="text red amountStr">'+amountStr+'</span>'+
	      				'<input type="hidden" name="moveLibraryItems['+moveIndex+'].amount" class="amount text" value="'+currency(quantity*price,false)+'" />';
	      				return html;
	                }},
	                { title:'${message("重量")}', name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
	                        var weight=item.weight;
	                        if(weight==''){
	                            weight=0;
	                        }
	                        return '<span class="lineWeightAmounts"></span><input class="lineWeight" value="'+weight+'" type="hidden" />';
	                }},
	                { title:'${message("经营组织")}',width:220, align:'center', renderer: function(val,item,rowIndex,obj){
		     			var html='';
		     			[#if organizationIsNoWarehouse == 0 ] 
		     				html +='<span class="productOrganizationName"></span>'+
		     				       '<input type="hidden" name="moveLibraryItems['+moveIndex+'].productOrganization.id" class="productOrganizationId text" value="" />';
		     			[#elseif organizationIsNoWarehouse == 1]  
		     				html +='<select name="moveLibraryItems['+moveIndex+'].productOrganization.id" class="text productOrganization">';
				     			if(obj==undefined){
				     				var productOrganizationName = '';
				     				if(item.product_organization_name != null && item.product_organization_name !=''){
				     					productOrganizationName = item.product_organization_name;
				     				}
				     				if(item.product_organization_id != null && item.product_organization_id !=''){
				     					html+='<option value="'+item.product_organization_id+'" selected="selected" >'+productOrganizationName+'</option> ';
				     				}
				    			}
		     				html+='</select>';
		     			[/#if]
		        			return html;
		     		}},
		    		{ title:'${message("产品等级")}', name:'level_Id' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		    			var str='selected="selected"';
		    			var html='<select name="moveLibraryItems['+moveIndex+'].productLevel.id" class="text productGrade" id="productGrade">';
		    				[#list productLevelList as products]
		    				if(${products.id}==item.level_Id){
		    					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
		    				}else{
		    					html+='<option value="${products.id}">${products.value}</option> ';
		    				}
		    				[/#list]
		    				html+='</select>';
		    			return html; 
		     		}},
		     		{ title:'${message("发出数量")}', name:'issue_quantity', align:'center', width:100,renderer:function(val,item,rowIndex,obj){
		     			return '<span class="issueQuantitys">'+val+'</span><input class="t issueQuantity" value="'+val+'" type="hidden" />';
		     		}},
		     		{ title:'${message("发出箱数")}', name:'issue_box_quantity', align:'center', width:100,renderer:function(val,item,rowIndex,obj){
		     			return '<span class="issueBoxQuantitys">'+val+'</span><input class="t issueBoxQuantity" value="'+val+'" type="hidden" />';
		     		}},
		     		
		     		{ title:'${message("发出支数")}', name:'issue_branch_quantity' ,align:'center', width:100,renderer:function(val,item,rowIndex,obj){
		     			return '<span class="issueBranchQuantity">'+val+'</span>';
		     		}},
		    		{ title:'${message("发出体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
	                        var volume=val;
	                        if(volume==''){
	                            volume=0;
	                        }
	                        return '<span class="issueVolumeAmounts"></span>';
	                    }},
	                { title:'${message("发出重量")}', name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
	                        var weight=val;
	                        if(weight==''){
	                            weight=0;
	                        }
	                        return '<span class="issueWeightAmounts"></span>';
	                    }},
		     		{ title:'${message("接收数量")}', name:'receive_quantity', align:'center', width:100,renderer:function(val,item,rowIndex,obj){
		     			return '<span class="receiveQuantity">'+val+'</span>';
		     		}},
		     		{ title:'${message("接收箱数")}', name:'receive_box_quantity', align:'center', width:100,renderer:function(val,item,rowIndex,obj){
		     			return '<span class="receiveBoxQuantity">'+val+'</span>';
		     		}},
		    		{ title:'${message("接收支数")}', name:'receive_branch_quantity' ,align:'center', width:100,renderer:function(val,item,rowIndex,obj){
		     			return '<span class="receiveBranchQuantity">'+val+'</span>';
		    		}},
		    		{ title:'${message("移库状态")}', name:'move_item_status' , align:'center', renderer: function(val){
		    			var result = itemStatus[val];
		    			if(result!=undefined)return result;			
		    		}},
		    		[#if hiddenBatch !=0]  
				    	{ title:'${message("色号")}', align:'center',name:'colour_number',renderer: function(val,item,rowIndex, obj){
				    		var colourNumber = item.colour_number==null||item.colour_number==''?'':item.colour_number;
				        	var html='<input type="text" name="moveLibraryItems['+moveIndex+'].colourNumber" class="text colourNumber" value="'+colourNumber+'" />';
							return html;
				        }},    
				        { title:'${message("含水率")}', align:'center',name:'moisture_content',renderer: function(val,item,rowIndex, obj){
				        	var moistureContent = item.moisture_content==null||item.moisture_content==''?'':item.moisture_content;
				        	var html='<input type="text" name="moveLibraryItems['+moveIndex+'].moistureContent" class="text moistureContent" value="'+moistureContent+'" />';
							return html;
				        }},
				        { title:'${message("批次")}', align:'center',name:'batch',renderer: function(val,item,rowIndex, obj){
				        	var moistureContent = item.batch==null||item.batch==''?'':item.batch;
				        	var html='<input type="text" name="moveLibraryItems['+moveIndex+'].batch" class="text" value="'+moistureContent+'" />';
							return html;
				        }},  
					 [/#if]  
				    { title:'${message("库存数量")}',align:'center', width:110, renderer: function(val,item,rowIndex, obj){
				 			return '<span class="onhandQuantity">0</span>';
					}},
				    { title:'${message("可用数量")}',align:'center', width:110, renderer: function(val,item,rowIndex, obj){
						 	return '<span class="attQuantity">0</span>';
					}},    
		    		{ title:'${message("移库明细备注")}', align:'center',name:'move_library_item_remarks',renderer: function(val,item,rowIndex, obj){
		    			var remarks = item.move_library_item_remarks==null||item.move_library_item_remarks==''?'':item.move_library_item_remarks;
		    			var html='<input type="text" name="moveLibraryItems['+moveIndex+'].remarks" class="text" value="'+remarks+'" />';
		    			return html;
		    		}},
		    		{ title:'${message("ERP备注")}', name:'erp_memo' ,align:'center', width:100,renderer:function(val,item,rowIndex,obj){
		    		moveIndex++;
		     			return '<span class="text">'+val+'</span>';
		    		}}
				
		];
		
		$move_mmGrid = $('#table-move').mmGrid({
			height:'auto',
			cols: move_cols,
			items:items,
			fullWidthRows:true,
			checkCol: false,
			autoLoad: true,
			callback: function(){
				summarizing();
				countTotal();
				productOrganization();
			}
		 });
		
		var logistics_items = ${logistics_json!"[]"};
	    var status = {'20':'已确认','30':'已派车','40':'服务中','50':'已完成','90':'已取消'};
	    	var cols = [				
	    	{ title:'${message("物流编号")}', name:'plan_no' ,width:120,align:'center',renderer:function(val,item,rowIndex){
				return '<a href="javascript:;" onClick="check_view(\'/b2b/logistics/view.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
			}},
			
	    	{ title:'${message("状态")}', name:'status' , align:'center', renderer: function(val){
				var result = status[val];
				if(result!=undefined)return result;			
			}},
	    	{ title:'${message("异常状态")}', name:'abnormal_status' ,align:'center',},
			{ title:'${message("货运公司")}', name:'transport_company' ,align:'center'},
			{ title:'${message("运单号")}', name:'transport_waybill_no' ,align:'center'},
			{ title:'${message("起运站")}', name:'start_station' ,align:'center', width:110},
			{ title:'${message("到达站")}', name:'arrival_station' ,align:'center',width:200},
			{ title:'${message("起运时间")}', name:'statr_transport_time' ,align:'center'},
			{ title:'${message("到达时间")}', name:'arrival_transport_time' ,align:'center'},
			{ title:'${message("备注")}', name:'remarks' ,align:'center'},
			{ title:'${message("派单备注")}', name:'dispatch_remarks' ,align:'center'},
		];
		$('#table-logistics').mmGrid({
			fullWidthRows:true,
			height:'auto',
	        cols: cols,
	        items:logistics_items,
	        checkCol: false,
	        autoLoad: true
	    });
		 $("#submit_button").click(function(){
				$("form").submit();
		});
		$("form").bindAttribute({
			isConfirm:true,
			callback: function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= '/b2b/move_library/edit.jhtml?id='+resultMsg.objx;
				});
			}
		 });
		
		//查询客户
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
		});
		
		[#if moveLibrary.wfId!=null]
			$("#wf_area").load("/act/wf/wf.jhtml?wfid=${moveLibrary.wfId}");
		[/#if] 
		
		
		 [#if linkStock == 1 ] 
			//产品经营组织
			 $(".productOrganization").live("change", function() {
					productOrganizationChange(this);	
			 })
			//产品等级
			$(".productGrade").live("change", function() {
					productOrganizationChange(this);	
			 })
			 //色号
			 $("input.colourNumber").live("change", function() {
					productOrganizationChange(this);	
			 })
			 //含水率
			 $("input.moistureContent").live("change", function() {
					productOrganizationChange(this);	
			 })
		[/#if]
		
	});

	//经营组织点击触发事件
	function productOrganizationChange(e){
		var issueWarehouseId = $("input.issueWarehouseId").val();
		var $tr = $(e).closest("tr");
		//产品经营组织
		var productOrganizationId = $tr.find(".productOrganization").val();
		if(productOrganizationId !=null && productOrganizationId !='' && productOrganizationId !='undefined'){
			//等级
			var productGrade = $tr.find(".productGrade").val();
			//编码
			var vonderCode = $tr.find(".vonderCode").val();
			//色号
			var colourNumber = $tr.find(".colourNumber").val();
			if(colourNumber=='undefined' || colourNumber.length == 0){
				colourNumber = "";
			}
			//含水率
			var moistureContent = $tr.find(".moistureContent").val();
			if(moistureContent=='undefined' || moistureContent.length == 0){
				moistureContent = "";
			}
			var params='&productGrade='+productGrade+'&vonderCode='+vonderCode+'&colourNumber='+colourNumber
			   +'&moistureContent='+moistureContent+'&organizationId='+productOrganizationId+'&warehouseId='+issueWarehouseId;
				params = params.substring(1,params.length);
			$.ajax({
				url:'/stock/stock/findViewStock.jhtml?'+params,
	   			type : "post",
	   			success : function(rows) {
	   				var data= $.parseJSON(rows.content);
	                   if(data.length>0){
	                       for (var i = 0; i < data.length;i++) {
	                       	if(data[i].totalOnhandQuantity1 !=null && data[i].totalOnhandQuantity1 !=''){
	                       		$tr.find(".onhandQuantity").text(data[i].totalOnhandQuantity1);
	                       	}else{
	                       		$tr.find(".onhandQuantity").text(0);
	                       	}
	                       	if(data[i].totalAttQuantity1 !=null && data[i].totalAttQuantity1 !=''){
	                       		$tr.find(".attQuantity").text(data[i].totalAttQuantity1);
	                       	}else{
	                       		$tr.find(".attQuantity").text(0);
	                       	} 
	                   	}
	               	}else{
	               		$tr.find(".onhandQuantity").text(0);
	               		$tr.find(".attQuantity").text(0);
	               	}
	   			}
			})	
		}
	}
	
	
	
	
	function deleteShipping(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$move_mmGrid.removeRow(index);
			summarizing();
		})
	}
	
	function check_view(url){
		var w = $(window).width();
		var h = $(window).height();
		var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
		var $dialog = $.dialog({
			title:'${message("查看物流信息")}',
			width:w,
			height:h,
			content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
			ok: null,
			cancel: null,
			onOk: function() {
			
			}
		});
		$dialog.find(".dialogContent").css("height",h+"px");
		$dialog.css("top",0+"px").css("max-height",h+"px");
	}
	
	
	/*
	function check_wf(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			$.message_confirm("您确定要审批流程吗？",function(){
				var url="check_wf.jhtml?id=${moveLibrary.id}&status=1";
				var data = $form.serialize();
				
				ajaxSubmit(e,{
					 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=99&objid=${moveLibrary.id}',
					 method: "post",
					 callback:function(resultMsg){
					 	var rows = resultMsg.objx;
					 	if(rows.length==1){
					 		data = data+'&objConfId='+rows[0].id;
					 		ajaxSubmit('',{
								 url: url,
								 method: "post",
								 data: data,
								 callback:function(resultMsg){
									$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
										reflush_wf();
									});
								 }
							})
					 	}else{
					 		var str = '';
						 	for(var i=0;i<rows.length;i++){
						 		var row = rows[i];
						 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
						 	}
						 	var content = '<table class="input input-edit" style="width:100%">'
									+'<tbody><tr><th>流程模版</th>'
									+'<td>'
										+'<select class="text" id="objConfId">'
											+str
										+'</select>'
									+'</td>'
								+'</tr></tbody></table>';
							var $dialog_check = $.dialog({
								title:"${message("审核")}",
								height:'135',
								content: content,
								onOk:function(){
									var objConfId = $("#objConfId").val();
									if(objConfId=='' || objConfId == null){
										$.message_alert("请选择流程模版");
										return false;
									}
									data = data+'&objConfId='+objConfId;
									
									ajaxSubmit($this,{
										 url: url,
										 method: "post",
										 data: data,
										 callback:function(resultMsg){
											$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
												reflush_wf();
											});
										 }
									})
									
								}
							});
							var h = 150;
							$dialog_check.css("top",h+"px");
					 	}
					 	
					 }
				})
			});
		}
	}*/
	
	function check_wf(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			$.message_confirm("您确定要审批流程吗？",function(){
				var objTypeId = 100024;
				//var modelId = 157512//测试
				var modelId = 1000;//测试
				//var modelId = 57602//正式
				var url="check_wf.jhtml?id=${moveLibrary.id}&modelId="+modelId+"&objTypeId="+objTypeId;
				ajaxSubmit(e,{
					method:'post',
					url:url,
					async: true,
					callback: function(resultMsg) {
						$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
							location.reload(true);
						})
					}
				});
			});
		}
	}
	
	function model(sbuId,versions){
		var json = '{"正式":{"1":"60147","2":"","3":"","4":"60149","5":"60151","6":"57602","7":"57602","8":"57602"},';
		json +='"测试":{"1":"122612","2":"","3":"","4":"122614","5":"122616","6":"","7":"","8":""}}';
		/*
		1	地板中心
		2	Nature
		3	壁纸
		4	进口三层
		5	弹性地板
		6	国产三层
		7	经销商工程
		8	电商*/
		var model = JSON.parse(json);
		return model[versions][sbuId];
	}
	
	function moveCancel(e){
		ajaxSubmit(e,{
			url:'cancel.jhtml?id='+${moveLibrary.id},
			method:"post",
			//data:data,
			isConfirm:true,
			confirmText : '您确定要作废吗？',
			callback:function(resultMsg){
			 	$.message_timer(resultMsg.type,resultMsg.content,5000,function(){
				location.reload(true);
				})
			 }
		});
	}
	
	//审核完成后修改产品单价并计算金额
	function apdateProductPrice(e){
	  	var $this = $(e);
	  	var $form = $("#inputForm");
	  	if($form.valid()){
	  		var url="apdate_ProductPrice.jhtml";
	  		var data = $form.serialize();
	  		ajaxSubmit($this,{
	  			 url: url,
	  			 method: "post",
	  			 data: data,
	  			 isConfirm:true,
	  			 confirmText:'您确定要保存吗？',
	  			 callback:function(resultMsg){
	  				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
	  					location.reload(true);
	  				})
	  			 }
	  		})
	  	}
	}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑移库单")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
	<input type="hidden" name="id" value="${moveLibrary.id}" />
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>${message("移库单号")}:
					</th>
					<td>
						<span>${moveLibrary.sn}</span>
					</td>
					<th>
						<span class="requiredField">*</span>${message("来源仓")}:
					</th>
	            	<td>
	            		<span style="display: none;">
	            			<span class="organizationName" >${moveLibrary.issueWarehouse.managementOrganization.name}</span>
					    	<input type="hidden" class="text organizationId" value="${moveLibrary.issueWarehouse.managementOrganization.id}" />
	            		</span>
	            		<span class="search" style="position:relative">
							<input type="hidden" name="issueWarehouseId" class="text issueWarehouseId" btn-fun="clear" value="${moveLibrary.issueWarehouse.id}"/>
							<input type="text" name="issueWarehouseName" class="text issueWarehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${moveLibrary.issueWarehouse.name}" readOnly/>
							<input type="button" class="iconSearch" value="" id="selectIssueWarehouse">
						</span>
	            	</td>
	            	<th>
	            		<span class="requiredField">*</span>${message("目标仓")}:
	            	</th>
	            	<td>
	            		<span class="search" style="position:relative">
							<input type="hidden" name="receiveWarehouseId" class="text receiveWarehouseId" btn-fun="clear" value="${moveLibrary.receiveWarehouse.id}"/>
							<input type="text" name="receiveWarehouseName" class="text receiveWarehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${moveLibrary.receiveWarehouse.name}" readOnly/>
							<input type="button" class="iconSearch" value="" id="selectReceiveWarehouse">
						</span>
	            	</td>
					<th>${message("移库状态")}:</th>
					<td>
						[#if moveLibrary.moveStatus == "saved"]
							${message("已保存")}
						[#elseif moveLibrary.moveStatus == "cancelled"]
							${message("已作废")}
						[#elseif moveLibrary.moveStatus == "audited"]
							${message("已审核")}
						[/#if]
					</td>
			    </tr>
				<tr>
					<th>
						<span class="requiredField">*</span>${message("机构")}:
					</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${moveLibrary.saleOrg.id }"/>
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${moveLibrary.saleOrg.name }" readOnly/>
							<input type="button" class="iconSearch" value="" id="selectSaleOrg" />
						</span>
					</td>
					<th>
						${message("需求日期")}:
					</th>
					<td>
						<input id="needTime" name="needDate" class="text" value="${moveLibrary.needDate }" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" type="text" btn-fun="clear"/>
					</td>
					<th>
						${message("SBU")}:
					</th>
					<td>
			           	<select id="sbuId" name="sbuId" class="text sbuId">
							[#list sbus as sbu]
								<option value="${sbu.sbu.id}"[#if moveLibrary.sbu.id==sbu.sbu.id]selected[/#if]>${sbu.sbu.name}</option>
							[/#list]
						</select>
			       </td>
					<th>
						${message("发出日期")}:
					</th>
					<td>
						<samp>${moveLibrary.issueDate}</samp>
					</td>
				</tr>
				<tr>
				  	<th>
						${message("接受日期")}:
					</th>
					<td>
						<samp>${moveLibrary.receiveDate}</samp>
					</td>
			    	<th>
						${message("创建人")}:
					</th>
					<td>
						${moveLibrary.storeMember.name}
					</td>
						<th>
						${message("创建日期")}:
					</th>
					<td>
						${moveLibrary.createDate}
					</td>
					<th>
						${message("发货状态")}:
					</th>
					<td>
						[#if moveLibrary.moveStatuss == "nomove"]${message("未移库")}[/#if]
			      		[#if moveLibrary.moveStatuss == "completely"]${message("已移库")}[/#if]
			      		[#if moveLibrary.moveStatuss == "part"]${message("部分移库")}[/#if]
					</td>
				</tr>
				<tr>
					<th>
						${message("接收状态")}:
					</th>
					<td>
						[#if moveLibrary.moveReceiveStatus == "noreceive"]${message("未接收")}[/#if]
			      		[#if moveLibrary.moveReceiveStatus == "completely"]${message("已接收")}[/#if]
			      		[#if moveLibrary.moveReceiveStatus == "part"]${message("部分接收")}[/#if]
					</td>
				</tr>
				<tr>
					<th>
						${message("备注")}:
					</th>
					<td colspan="7">
			            <textarea class="text" name="remarks">${moveLibrary.remarks}</textarea>
			        </td>
				</tr>
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			<div class="title-style">
					${message("移库单明细")}:
				<div class="btns">
					<a href="javascript:void(0);" id="addProduct" class="button">${message("添加产品")}</a>
				</div>
			</div>
			<table id="table-move"></table>
		</table>
		<div style="margin:12px 0px 10px 0px;font-size:15px;"><b>移库单明细汇总：</b></div>
		<table id="tab" style="border:1px solid #d0cdcd;">
			<tr>
				<th class="ssdtop"><div class="connetop">数量</div></th>
				<th class="ssdtop"><div class="connetop">支数</div></th>
				<th class="ssdtop"><div class="connetop">箱数</div></th>
				<th class="ssdtop"><div class="connetop">零散支数</div></th>
				<th class="ssdtop"><div class="connetop">金额</div></th>
				<th class="ssdtop"><div class="connetop">体积</div></th>
				<th class="ssdtop"><div class="connetop">重量</div></th>
				<th class="ssdtop"><div class="connetop">发出数量</div></th>
				<th class="ssdtop"><div class="connetop">发出箱数</div></th>
				<th class="ssdtop"><div class="connetop">发出支数</div></th>
				<th class="ssdtop"><div class="connetop">发出体积</div></th>
				<th class="ssdtop"><div class="connetop">发出重量</div></th>
				<th class="ssdtop"><div class="connetop">接收数量</div></th>
				<th class="ssdtop"><div class="connetop">接收箱数</div></th>
				<th class="ssdtop"><div class="connetop">接收支数汇总</div></th>
			</tr>	
			<tr>
				<td class="content"><div class="conne tQuantity">0</div></td>
				<td class="content"><div class="conne tBranchQuantity">0</div></td>
				<td class="content"><div class="conne tBranchPerbox">0</div></td>
				<td class="content"><div class="conne tScatteredQuantity">0</div></td>
				<td class="content"><div class="conne tAmount">0</div></td>
				<td class="content"><div class="conne lineVolumeAmount">0</div></td>
				<td class="content"><div class="conne lineWeightAmount">0</div></td>
				<td class="content"><div class="conne tIssueQuantity">0</div></td><!--发出数量-->
				<td class="content"><div class="conne tIssueBoxQuantity">0</div></td><!--发出箱数-->
				<td class="content"><div class="conne tIssueBranchQuantity">0</div></td><!--发出支数-->
				<td class="content"><div class="conne issueVolumeAmount">0</div></td>
				<td class="content"><div class="conne issueWeightAmount">0</div></td>
				<td class="content"><div class="conne tReceiveQuantity">0</div></td><!--接收数量-->
				<td class="content"><div class="conne tReceiveBoxQuantity">0</div></td><!--接收箱数-->
				<td class="content"><div class="conne summarizing">0</div></td><!--接收支数汇总-->
			</tr>
		</table>
		<div class="title-style">
			${message("物流信息")}
		</div>
		<table id="table-logistics"></table>
	</div>
	<div class="fixed-top">
	   [#if moveLibrary.moveStatus=='saved']
			[#if moveLibrary.wfId==null]
				<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
				<input type="button" id="submit_button" class="button sureButton" value='${message("保存")}'>
			[/#if]
	   [/#if]
	   [#if  moveLibrary.moveStatus=='saved' && moveLibrary.wfId==null]
			 <a href="javascript:void(0);" class="iconButton" id="fenpeiButton" onclick="moveCancel(this)">
			 	  <span class="fenpeiIcon">&nbsp;</span>${message("作废")}
			 </a>
	   [/#if]
	   [#if apdateProductPrice !=0] 
			[#if moveLibrary.moveStatus == "audited" || moveLibrary.moveStatus == "auditing"] 
				<a href="javascript:void(0);" class="button sureButton" onclick="apdateProductPrice(this)">${message("修改价格")}</a> 
	    	[/#if] 
	   [/#if] 
	   <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
	</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>