<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑移库单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />

<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<style>
	#tab{border="1px solid #d0cdcd";}
	#tab .ssdtop{width: 80px;height: 35px;border: 1px solid #d0cdcd;background-color:#e6e6e6;}
	#tab .content{width: 80px;height: 35px;}
	#tab .conne{text-align:center;}
	#tab .connetop{font-weight:500;}
</style>
<script type="text/javascript">


function countTotal(t){
	
	var $bInput = $("input.boxQuantity");
	$bInput.each(function(){
        var $tr = $(this).closest("tr");
        var isEqual = null;
        if(t!=undefined){
            isEqual = (t.find(".line_no").text() == $tr.find(".line_no").text());
        }
        var boxQuantity=$(this).val();
        
        var branchPerBox=$tr.find(".branchPerBox").val();
        var issueBoxQuantity = $tr.find(".issueBoxQuantity").val();
        var perBranch=$tr.find(".perBranch").val();
        var perBox =$tr.find(".perBox").val();
        var branchQuantity =$tr.find(".branchQuantity").val();
        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
        var type = productDisc(branchPerBox,perBranch,perBox);
         var volume=$tr.find(".lineVolume").val();

        var volumeAmount=Number(accMul(boxQuantity,volume)).toFixed(6);

        if(isNaN(volumeAmount)){
                 volumeAmount = 0;
        }
               
        $tr.find(".lineVolumeAmounts").html(volumeAmount);//体积
        
        var issueVolumeAmount=Number(accMul(issueBoxQuantity,volume)).toFixed(6);
        
        $tr.find(".issueVolumeAmounts").html(issueVolumeAmount);//发出体积
        if(isEqual == null){
        	if(type==2){
        		 var a = accDiv(perBox,10);
                 quantity = accMul(boxQuantity,a);                
        	}else if(type==3){
        		quantity = accMul(perBranch,branchQuantity);
                $tr.find(".boxQuantity").val("");
                $tr.find(".scatteredQuantity").val("");
                $tr.find(".branchPerBox").val("");
        	}else if(type==0){
        		var branchQuantity=accMul(boxQuantity,branchPerBox);
        		branchQuantity=accAdd(branchQuantity,scatteredQuantity);
        		if(isNaN(branchQuantity)){
        			branchQuantity = 0;
        		}
        		
        		var quantity=accMul(branchQuantity,perBranch);
        	}
        	if(isNaN(quantity)){
        		quantity = 0;
        	}
        	$tr.find(".quantity").val(quantity);//平方数
        	$tr.find(".branchQuantity").val(branchQuantity);//支数
        }else{
        	if(type==2){
       		 var a = accDiv(perBox,10);
                quantity = accMul(boxQuantity,a);
                $tr.find(".quantity").val(quantity);//数量
                $tr.find(".quantityStr").html(quantity);
        	}else if(type==3){
	       		quantity = accMul(perBranch,branchQuantity);
	            $tr.find(".quantity").val(quantity);//数量
	            $tr.find(".quantityStr").html(quantity);
	            $tr.find(".boxQuantity").val("");
	            $tr.find(".scatteredQuantity").val("");
	            $tr.find(".branchPerBox").val("");	           
        	}else if(type==0){
	       		var branchQuantity=accMul(boxQuantity,branchPerBox);
	       		branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	       		if(isNaN(branchQuantity)){
	       			branchQuantity = 0;
	       		}
	       		var quantity=accMul(branchQuantity,perBranch);
	       		if(isNaN(quantity)){
	       			quantity = 0;
	       		}
	       		$tr.find(".quantity").val(quantity);//平方数
	       		$tr.find(".branchQuantity").val(branchQuantity);//支数
        	}
        }
	});
	
	
		var $input = $("input.quantity");
	$input.each(function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
 

                var weight=$tr.find(".lineWeight").val();
                var issueQuantity = $tr.find(".issueQuantity").val();
                var issueWeightAmount = Number(accMul(issueQuantity,weight)).toFixed(6);
                 if(isNaN(issueWeightAmount)){
                    issueWeightAmount = 0;
                }
				$tr.find(".issueWeightAmounts").html(issueWeightAmount);//发出重量
				
				
                var weightAmount=Number(accMul($(this).val(),weight)).toFixed(6);

                if(isNaN(weightAmount)){
                    weightAmount = 0;
                }
                $tr.find(".lineWeightAmounts").html(weightAmount);//重量

      });
	summarizing();
	
}

//Summary calculation method
function summarizing(){
	var tr = $("#table-move tr");
	var quantity = 0;
	var branchQuantity = 0;
	var boxQuantity = 0;
	var scatteredQuantity = 0;
	var issueQuantity = 0;
	var issueBoxQuantity = 0;
	var issueBranchQuantity = 0;
	var receiveQuantity = 0;
	var receiveBoxQuantity = 0;
	var receiveBranchQuantity = 0;
	var issueWeightAmount=0;
	var issueVolumeAmount=0;
	var lineVolumeAmount=0;
	var lineWeightAmount=0;
	tr.each(function(){
		var $this = $(this);
		quantity += $this.find(".quantity").val()*1;
		branchQuantity += $this.find(".branchQuantity").val()*1;
		boxQuantity += $this.find(".boxQuantity").val()*1;
		scatteredQuantity += $this.find(".scatteredQuantity").val()*1;
		issueQuantity += $this.find(".issueQuantity").text()*1;
		issueBoxQuantity += $this.find(".issueBoxQuantity").text()*1;
		issueBranchQuantity += $this.find(".issueBranchQuantity").text()*1;
		receiveQuantity += $this.find(".receiveQuantity").text()*1;
		receiveBoxQuantity += $this.find(".receiveBoxQuantity").text()*1;
		receiveBranchQuantity += $this.find(".receiveBranchQuantity").text()*1;
		issueWeightAmount+=$this.find(".issueWeightAmounts").text()*1;
		issueVolumeAmount+=$this.find(".issueVolumeAmounts").text()*1;
		lineVolumeAmount+=$this.find(".lineVolumeAmounts").text()*1;
		lineWeightAmount+=$this.find(".lineWeightAmounts").text()*1;
	})
		$(".tQuantity").text(isNaN(quantity)?0:quantity.toFixed(6));
		$(".tBranchQuantity").text(isNaN(branchQuantity)?0:branchQuantity);
		$(".tBranchPerbox").text(isNaN(boxQuantity)?0:boxQuantity);
		$(".tScatteredQuantity").text(isNaN(scatteredQuantity)?0:scatteredQuantity);
		$(".tIssueQuantity").text(isNaN(issueQuantity)?0:issueQuantity.toFixed(6));
		$(".tIssueBoxQuantity").text(isNaN(issueBoxQuantity)?0:issueBoxQuantity);
		$(".tIssueBranchQuantity").text(isNaN(issueBranchQuantity)?0:issueBranchQuantity);
		$(".tReceiveQuantity").text(isNaN(receiveQuantity)?0:receiveQuantity.toFixed(6));
		$(".tReceiveBoxQuantity").text(isNaN(receiveBoxQuantity)?0:receiveBoxQuantity);
		$(".summarizing").text(isNaN(receiveBranchQuantity)?0:receiveBranchQuantity);
		$(".issueVolumeAmount").text(isNaN(issueVolumeAmount)?0:issueVolumeAmount.toFixed(6));
		$(".issueWeightAmount").text(isNaN(issueWeightAmount)?0:issueWeightAmount.toFixed(6));
		$(".lineVolumeAmount").text(isNaN(lineVolumeAmount)?0:lineVolumeAmount.toFixed(6));
		$(".lineWeightAmount").text(isNaN(lineWeightAmount)?0:lineWeightAmount.toFixed(6));
}

function editQty(t,e){
	
	if($(t).attr("kid")=="quantity"){//平方
		if(extractNumber(t,6,false,e)){
			var $tr = $(t).closest("tr");
			var branch_quantity=0;
			var box_quantity=0;
			
			var quantity=$(t).val();
			var perBranch=$tr.find(".perBranch").val();  //每支单位数
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var scattered=0;
			if(perBranch!=0 && branchPerBox!=0){
				 branch_quantity=quantity/perBranch;
				 box_quantity=parseInt(branch_quantity/branchPerBox);
				 scattered=(branch_quantity%branchPerBox).toFixed(6);
			}
			$tr.find(".boxQuantity").val(box_quantity);
			$tr.find(".branchQuantity").val(branch_quantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
			
			countTotal($tr);
			$(t).val(quantity).toFixed(6);
			
		}
	}else{
		if(extractNumber(t,3,false,e)){
			var $tr = $(t).closest("tr");
			
			var branch_quantity=0;
			var box_quantity=0;
			
			if($(t).attr("kid")=="box"){//箱
				$tr.find(".scatteredQuantityStr").html(0);
				$tr.find(".scatteredQuantity").val(0);
			}else if($(t).attr("kid")=="branch"){//支
				var quantity=$(t).val();
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var box=parseInt(quantity/branchPerBox);
				var scattered=quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}else if($(t).attr("kid")=="quantity"){//平方
				var quantity=$(t).val();
				var perBranch=$tr.find(".perBranch").val();  //每支单位数
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var branch_quantity=quantity/perBranch;
				var box_quantity=parseInt(branch_quantity/branchPerBox).toFixed(6);
				var scattered=branch_quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box_quantity);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}
			
			countTotal($tr);

		}
	}
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $addProduct = $("#addProduct");
	// 表单验证
	$inputForm.validate({
		rules: {
			name: "required",
//			bankCardNo: "required"
		} ,
		submitHandler:function(form){
			return false;
		}
	});
	
	// 表单验证
	$.validator.addClassRules({
		oaSn: {
			required: true
		}
	});
	
	//打开选择产品界面
    $addProduct.click(function(){
    	var $this = $(this);
		var $tr =$this.closest("tr");
        var sbuId = $(".sbuId").val();
       
            $addProduct.bindQueryBtn({
                type:'product',
                bindClick:false,
                title:'${message("查询产品")}',
                url:'/product/product/selectProduct.jhtml?multi=2&sbuId='+sbuId,
                callback:function(rows){
                    if(rows.length>0){
                      /*  for (var i = 0; i < rows.length;i++) {
                            var idH = $(".productId_"+rows[i].id).length;
                            if(rows[i].per_box==null){
                                $.message_alert('产品【'+rows[i].name+'】未维护每箱单位数');
                                return false;
                            }
                            if(rows[i].branch_per_box==null){
                                $.message_alert('产品【'+rows[i].name+'】未维护每箱支数');
                                return false;
                            }
                            if(rows[i].per_branch==null){
                                $.message_alert('产品【'+rows[i].name+'】未维护每支单位数');
                                return false;
                            }
                        }*/
                        for (var i = 0; i < rows.length;i++) {
							var row = rows[i];
							//var pid_str = "storeId=" + $storeId + "&productId="+row.id
							$move_mmGrid.addRow(row,null,1);
						}	
                        countTotal();
                        summarizing();
                    }
                    
                   
                }
            }); 
        
    })
	
	//查询来源仓库
	$("#selectIssueWarehouse").click(function(){
		var sbuId = $("#sbuId option:selected").val();
        if(sbuId==""){
            $.message_alert('请选择SBU');
        }else{
        	$("#selectWarehouse").bindQueryBtn({
                type:'warehouse',
                bindClick:false,
				title:'${message("查询仓库")}',
				url:'/stock/warehouse/select_warehouse.jhtml?sbuId='+sbuId,
				callback:function(rows){
	            		if(rows.length>0){
	            			var row = rows[0];
	            			$(".issueWarehouseId").val(row.id);
	            			$(".issueWarehouseName").val(row.name);
	                			
	            			$move_mmGrid.removeRow();
	            			summarizing();
	            		}
				}
			});
        }
	})
	
	//查询目标仓库
	$("#selectReceiveWarehouse").click(function(){
		var sbuId = $("#sbuId option:selected").val();
		var issueWarehouseId = $(".issueWarehouseId").val();
        if(sbuId==""){
            $.message_alert('请选择SBU');
        }else if(issueWarehouseId==""){
        	 $.message_alert('请选择来源仓');
        }else{
        	$("#selectWarehouse").bindQueryBtn({
                type:'warehouse',
                bindClick:false,
				title:'${message("查询仓库")}',
				url:'/stock/warehouse/select_warehouse.jhtml?sbuId='+sbuId,
				callback:function(rows){
	            		if(rows.length>0){
	            			var row = rows[0];
	            			if(issueWarehouseId==row.id){
	            				 $.message_alert('来源仓库与目标仓不能一致');
	                                return false;
	            			}
	            			$(".receiveWarehouseId").val(row.id);
	            			$(".receiveWarehouseName").val(row.name);
	                			
	            		
	            			
	            		}
				}
			});
        }
	})
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
	var items=${moveLibraryItem};
	var moveIndex=0;
	  var grade={"1":"优等品","2":"二等品","3":"一等品","4":"无等级"};
	  var itemStatus={"0":"未移库","1":"部分移库","2":"完全移库"};
		var move_cols = [
	       
	        	{ title:'${message("12211")}', name:'vonder_code' ,align:'center', width:150, renderer: function(val,item,rowIndex, obj){
	        		
	        		var html=val+'<input type="hidden" class="text" name="moveLibraryItems['+moveIndex+'].vonderCode" value="'+val+'">';
	    			return html;
	    		}},
	    		{ title:'${message("产品名称")}', name:'product_name' ,align:'center', width:200, renderer: function(val,item,rowIndex, obj){
	    			
	    			var name = '';
	    			if(item.name==null){
	    				name=item.product_name;
	        		}else{
	        			name=item.name;
	        		}
	    			var productId = '';
	    			if(item.product != null){
	    				productId = item.product;			
	    			}else {
	    				productId = item.id;	
	    			}
	    			var	html = name+'<input type="hidden" name="moveLibraryItems['+moveIndex+'].product.id"  class="productId productId_'+productId+'" value="'+productId+'" />'+
	    				'<input type="hidden" class="text productName" name="moveLibraryItems['+moveIndex+'].productName" readonly value="'+name+'" />';
	    				
	    		
	    			return html;
	    		}},  
	    		{ title:'${message("产品描述")}', name:'detail_description' ,align:'center', width:100},
	    		{ title:'${message("产品型号")}', name:'product_model' ,align:'center', width:100},
	    		{ title:'${message("产品规格")}', name:'product_spec' ,align:'center', width:100},
	    		{ title:'${message("单位")}', align:'center',name:'unit',renderer: function(val,item,rowIndex, obj){
	    			var unit = val;
	    			if(obj == undefined){
	    				unit = item.unit;
	    			}
	    			return unit; 
	    	    }},
	    		
	     		{ title:'${message("数量")}', align:'center',name:'quantity',renderer: function(val,item,rowIndex, obj){
	     			var quantity = 1;
	    			if(obj==undefined){
	    				quantity = val;
	    			}
	    			var text = '<div class="lh20">'+
	    						'<div class="nums-input ov">'+
	    			            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	    			            	'<input type="text" kid="box" class="t quantity"  name="moveLibraryItems['+moveIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	    			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	    			            	'<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
	    			        	'</div>';
	    			text += '</div>';
	    			return text;
	    	    }},
	     		{ title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
	    			var branchQuantity='';
	    			if(obj==undefined){
	    				branchQuantity = val;
	    			}
	    			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
	    			if(type==1||type==2){
	    				branchQuantity=0;
	    				return '-'+
	    				'<input type="hidden"  class="t branchQuantity"  name="moveLibraryItems['+moveIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0" >'+
	    				'<input type=hidden class="branchPerBox" name="moveLibraryItems['+moveIndex+'].branchPerBox"  value="'+item.branch_per_box+'" /> '+
    					'<input type=hidden class="perBranch" name="moveLibraryItems['+moveIndex+'].perBranch" value="'+item.per_branch+'" />';
	    			}else{
	    				var text = '<div class="lh20">'+
	    					'<div class="nums-input ov">'+
	    	        			'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	    	        			'<input type="text" kid="branch" class="t branchQuantity"  name="moveLibraryItems['+moveIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	    	        			'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	    	        			'<input type=hidden class="branchPerBox" name="moveLibraryItems['+moveIndex+'].branchPerBox"  value="'+item.branch_per_box+'" /> '+
	    						'<input type=hidden class="perBranch" name="moveLibraryItems['+moveIndex+'].perBranch" value="'+item.per_branch+'" />'+
	    	        			'</div></div>';
	    	    		return text;
	    			}
	    		}},
	    		{ title:'${message("箱数")}', name:'box_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
	    			
	    			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
	    			var quantity = 1;
	    			if(obj==undefined){
	    				quantity = val;
	    			}
	    			if(type==1||type==3){
	    				quantity=0;
	    				return '-'+
	    				'<input type="hidden" kid="box" class="t boxQuantity"  name="moveLibraryItems['+moveIndex+'].boxQuantity" value="'+quantity+'" minData="0"  >';
	    			}else{
	    				var text = '<div class="lh20">'+
	    						'<div class="nums-input ov">'+
	    			            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	    			            	'<input type="text" kid="box" class="t boxQuantity"  name="moveLibraryItems['+moveIndex+'].boxQuantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	    			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	    			        	'</div>';
	    				text += '</div>';
	    				return text;
	    			}
	    		}},
	    		{ title:'${message("零散支数")}', name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
	    			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
	    			var branchQuantity='';
	    			if(obj==undefined){
	    				branchQuantity = val;
	    			}
	    			if(type==2||type==3||type==1){
	    				return '-';
	    			}
	    			var html='<span class="scatteredQuantityStr">0</span>'+
	    				'<input type="hidden" name="moveLibraryItems['+moveIndex+'].scatteredQuantity" class="scatteredQuantity text" value="0" />';
	    			return html;
	    		}},
	    		 { title:'${message("体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        var volume=item.volume;
                        if(volume==''){
                            volume=0;
                        }
                        return '<span class="lineVolumeAmounts"></span><input class="lineVolume" value="'+volume+'" type="hidden" />';
                    }},
                { title:'${message("重量")}', name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        var weight=item.weight;
                        if(weight==''){
                            weight=0;
                        }
                        return '<span class="lineWeightAmounts"></span><input class="lineWeight" value="'+weight+'" type="hidden" />';
                    }},
	    		{ title:'${message("产品等级")}', name:'product_grade' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
	    			var str1 = '';
	    			var str2 = '';
	    			var str3 = '';
	    			var str4 = '';
	    			if(val==1)str1 = 'selected';
	    			if(val==2)str2 = 'selected';
	    			if(val==3)str3 = 'selected';
	    			if(val==4)str4 = 'selected';
	    			var html = '<select name="moveLibraryItems['+moveIndex+'].productGrade" class="text productGrade" id="productGrade">'
	    			+'<option value="1" '+str1+'>优等品</option>'
	    			+'<option value="2" '+str2+'>二等品</option>'
	    			+'<option value="3" '+str3+'>一等品</option>'
	    			+'<option value="4" '+str4+'>无等级</option>'
	    			+'</select>';
	    			return html;
	     		}},
	     		{ title:'${message("发出数量")}', name:'issue_quantity', align:'center', width:100,renderer:function(val,item,rowIndex,obj){
	     			return '<span class="issueQuantitys">'+val+'</span><input class="t issueQuantity" value="'+val+'" type="hidden" />';
	     		}},
	     		{ title:'${message("发出箱数")}', name:'issue_box_quantity', align:'center', width:100,renderer:function(val,item,rowIndex,obj){
	     			return '<span class="issueBoxQuantitys">'+val+'</span><input class="t issueBoxQuantity" value="'+val+'" type="hidden" />';
	     		}},
	     		
	     		{ title:'${message("发出支数")}', name:'issue_branch_quantity' ,align:'center', width:100,renderer:function(val,item,rowIndex,obj){
	     			return '<span class="issueBranchQuantity">'+val+'</span>';
	     		}},
	    		{ title:'${message("发出体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        var volume=val;
                        if(volume==''){
                            volume=0;
                        }
                        return '<span class="issueVolumeAmounts"></span>';
                    }},
                { title:'${message("发出重量")}', name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        var weight=val;
                        if(weight==''){
                            weight=0;
                        }
                        return '<span class="issueWeightAmounts"></span>';
                    }},
	     		{ title:'${message("接收数量")}', name:'receive_quantity', align:'center', width:100,renderer:function(val,item,rowIndex,obj){
	     			return '<span class="receiveQuantity">'+val+'</span>';
	     		}},
	     		{ title:'${message("接收箱数")}', name:'receive_box_quantity', align:'center', width:100,renderer:function(val,item,rowIndex,obj){
	     			return '<span class="receiveBoxQuantity">'+val+'</span>';
	     		}},
	    		{ title:'${message("接收支数")}', name:'receive_branch_quantity' ,align:'center', width:100,renderer:function(val,item,rowIndex,obj){
	     			return '<span class="receiveBranchQuantity">'+val+'</span>';
	    		}},
	    		
	    		{ title:'${message("移库状态")}', name:'move_item_status' , align:'center', renderer: function(val){
	    			var result = itemStatus[val];
	    			if(result!=undefined)return result;			
	    		}},
	    		{ title:'${message("移库明细备注")}', align:'center',name:'move_library_item_remarks',renderer: function(val,item,rowIndex, obj){
	    			return '<input type="text" name="moveLibraryItems['+moveIndex+'].remarks" value="'+val+'">'+val;
	    		}},
			{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
				moveIndex++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteShipping(this)" >删除</a>';
			}}
	];
	
	$move_mmGrid = $('#table-move').mmGrid({
		height:'auto',
		cols: move_cols,
		items:items,
		fullWidthRows:true,
		checkCol: false,
		autoLoad: true,
		callback: function(){
			summarizing();
			countTotal();
		}
	 });
	
	 $("#submit_button").click(function(){
			$("form").submit();
	});
	$("form").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/b2b/move_library/edit.jhtml?id='+resultMsg.objx;
			});
		}
	 });
	
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
	[#if moveLibrary.wfId!=null]
	$("#wf_area").load("/wf/wf.jhtml?wfid=${moveLibrary.wfId}");
	[/#if] 
	
	
});

	function deleteShipping(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$move_mmGrid.removeRow(index);
			summarizing();
		})
	}
	
	
	
	function check_wf(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			$.message_confirm("您确定要审批流程吗？",function(){
				var url="check_wf.jhtml?id=${moveLibrary.id}&status=1";
				var data = $form.serialize();
				
				ajaxSubmit(e,{
					 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=99&objid=${moveLibrary.id}',
					 method: "post",
					 callback:function(resultMsg){
					 	var rows = resultMsg.objx;
					 	if(rows.length==1){
					 		data = data+'&objConfId='+rows[0].id;
					 		ajaxSubmit('',{
								 url: url,
								 method: "post",
								 data: data,
								 callback:function(resultMsg){
									$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
										reflush_wf();
									});
								 }
							})
					 	}else{
					 		var str = '';
						 	for(var i=0;i<rows.length;i++){
						 		var row = rows[i];
						 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
						 	}
						 	var content = '<table class="input input-edit" style="width:100%">'
									+'<tbody><tr><th>流程模版</th>'
									+'<td>'
										+'<select class="text" id="objConfId">'
											+str
										+'</select>'
									+'</td>'
								+'</tr></tbody></table>';
							var $dialog_check = $.dialog({
								title:"${message("审核")}",
								height:'135',
								content: content,
								onOk:function(){
									var objConfId = $("#objConfId").val();
									if(objConfId=='' || objConfId == null){
										$.message_alert("请选择流程模版");
										return false;
									}
									data = data+'&objConfId='+objConfId;
									
									ajaxSubmit($this,{
										 url: url,
										 method: "post",
										 data: data,
										 callback:function(resultMsg){
											$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
												reflush_wf();
											});
										 }
									})
									
								}
							});
							var h = 150;
							$dialog_check.css("top",h+"px");
					 	}
					 	
					 }
				})
			});
		}
	}
	
	function moveCancel(e){
	

			ajaxSubmit(e,{
				url:'cancel.jhtml?ids='+${moveLibrary.id},
				method:"post",
				//data:data,
				isConfirm:true,
				confirmText : '您确定要作废吗？',
				callback:function(resultMsg){
				 	$.message_timer(resultMsg.type,resultMsg.content,5000,function(){
					location.reload(true);
					})
				 }
			
			});

		}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑移库单")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
	<input type="hidden" name="id" value="${moveLibrary.id}" />
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("移库单号")}:
				</th>
				<td>
				${moveLibrary.sn}
				</td>
					
				<th><span class="requiredField">*</span>${message("来源仓")}:</th>
            	<td>
            		<span class="search" style="position:relative">
					<input type="hidden" name="issueWarehouseId" class="text issueWarehouseId" btn-fun="clear" value="${moveLibrary.issueWarehouse.id}"/>
					<input type="text" name="issueWarehouseName" class="text issueWarehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${moveLibrary.issueWarehouse.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectIssueWarehouse">
					</span>
            	</td>
            	
            	<th><span class="requiredField">*</span>${message("目标仓")}:</th>
            	<td>
            		<span class="search" style="position:relative">
					<input type="hidden" name="receiveWarehouseId" class="text receiveWarehouseId" btn-fun="clear" value="${moveLibrary.receiveWarehouse.id}"/>
					<input type="text" name="receiveWarehouseName" class="text receiveWarehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${moveLibrary.receiveWarehouse.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectReceiveWarehouse">
					</span>
            	</td>

				<th>${message("状态")}:</th>
				<td>
					[#if moveLibrary.moveStatus == "saved"]${message("已保存")}
					[#elseif moveLibrary.moveStatus == "cancelled"]${message("已作废")}
					[#elseif moveLibrary.moveStatus == "audited"]${message("已审核")}
					[/#if]
				</td>
			
		
	</tr>
	<tr>
			<th>
					<span class="requiredField">*</span>${message("机构")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${moveLibrary.saleOrg.id }"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${moveLibrary.saleOrg.name }" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg" />
					</span>
				</td>
				<th>
				${message("需求日期")}:
			</th>
			<td>
				<input id="needTime" name="needDate" class="text" value="${moveLibrary.needDate }" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" type="text" btn-fun="clear"/>
			</td>
				<th>
				${message("SBU")}:
			</th>
			<td>
            		<select id="sbuId" name="sbuId" class="text sbuId">
						[#list sbus as sbu]
						<option value="${sbu.sbu.id}"[#if moveLibrary.sbu.id==sbu.sbu.id]selected[/#if]>${sbu.sbu.name}</option>
						[/#list]
					</select>
            </td>
				<th>
				${message("发出日期")}:
			</th>
			<td>
				<input id="issueTime" name="issueDate" class="text" readonly value="${moveLibrary.issueDate }"  type="text" btn-fun="clear"/>
			</td>
	</tr>
	<tr>
	  	<th>
				${message("接受日期")}:
			</th>
			<td>
				<input id="receiveTime" name="receiveDate" readonly value="${moveLibrary.receiveDate }"  type="text" btn-fun="clear"/>
			</td>
	    	<th>
				${message("创建人")}:
			</th>
			<td>
				${moveLibrary.storeMember.name}
			</td>
				<th>
				${message("创建日期")}:
			</th>
			<td>
				${moveLibrary.createDate}
			</td>
			<th>
				${message("移库状态")}:
			</th>
			<td>
					[#if moveLibrary.moveStatuss == "nomove"]${message("未移库")}[/#if]
        			[#if moveLibrary.moveStatuss == "completely"]${message("已移库")}[/#if]
        			[#if moveLibrary.moveStatuss == "part"]${message("部分移库")}[/#if]
			</td>
	</tr>
	<tr>
		<th>
			${message("备注")}:
		</th>
		<td colspan="7">
            <textarea class="text" name="remarks">${moveLibrary.remarks}</textarea>
        </td>
	</tr>
		</table>
		
	<table class="input input-edit" style="width:100%;margin-top:5px;">
	<div class="title-style">
			${message("移库单明细")}:
		<div class="btns">
			<a href="javascript:void(0);" id="addProduct" class="button">${message("添加产品")}</a>
		</div>
	</div>
	<table id="table-move"></table>
	</table>
	<div style="margin:12px 0px 10px 0px;font-size:15px;"><b>移库单明细汇总：</b></div>
	<table id="tab" style="border:1px solid #d0cdcd;">
		<tr>
			<th class="ssdtop"><div class="connetop">数量</div></th>
			<th class="ssdtop"><div class="connetop">支数</div></th>
			<th class="ssdtop"><div class="connetop">箱数</div></th>
			<th class="ssdtop"><div class="connetop">零散支数</div></th>
			<th class="ssdtop"><div class="connetop">体积</div></th>
			<th class="ssdtop"><div class="connetop">重量</div></th>
			<th class="ssdtop"><div class="connetop">发出数量</div></th>
			<th class="ssdtop"><div class="connetop">发出箱数</div></th>
			<th class="ssdtop"><div class="connetop">发出支数</div></th>
			<th class="ssdtop"><div class="connetop">发出体积</div></th>
			<th class="ssdtop"><div class="connetop">发出重量</div></th>
			<th class="ssdtop"><div class="connetop">接收数量</div></th>
			<th class="ssdtop"><div class="connetop">接收箱数</div></th>
			<th class="ssdtop"><div class="connetop">接收支数汇总</div></th>
		</tr>	
		<tr>
			<td class="content"><div class="conne tQuantity">0</div></td>
			<td class="content"><div class="conne tBranchQuantity">0</div></td>
			<td class="content"><div class="conne tBranchPerbox">0</div></td>
			<td class="content"><div class="conne tScatteredQuantity">0</div></td>
			<td class="content"><div class="conne lineVolumeAmount">0</div></td>
			<td class="content"><div class="conne lineWeightAmount">0</div></td>
			<td class="content"><div class="conne tIssueQuantity">0</div></td><!--发出数量-->
			<td class="content"><div class="conne tIssueBoxQuantity">0</div></td><!--发出箱数-->
			<td class="content"><div class="conne tIssueBranchQuantity">0</div></td><!--发出支数-->
			<td class="content"><div class="conne issueVolumeAmount">0</div></td>
			<td class="content"><div class="conne issueWeightAmount">0</div></td>
			<td class="content"><div class="conne tReceiveQuantity">0</div></td><!--接收数量-->
			<td class="content"><div class="conne tReceiveBoxQuantity">0</div></td><!--接收箱数-->
			<td class="content"><div class="conne summarizing">0</div></td><!--接收支数汇总-->
		</tr>
	</table>
		</div>
		<div class="fixed-top">
			[#if moveLibrary.moveStatus=='saved']
					[#if moveLibrary.wfId==null]
						<a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
						<input type="button" id="submit_button" class="button sureButton" value='${message("保存")}'>
					[/#if]
			[/#if]
				[#if  moveLibrary.moveStatus=='audited' && moveLibrary.moveStatuss=='nomove']
		 <a href="javascript:void(0);" class="iconButton" id="fenpeiButton" onclick="moveCancel(this)">
		 	     <span class="fenpeiIcon">&nbsp;</span>${message("作废")}
		 </a>
		   [/#if]
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>