<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"]{
  -moz-appearance: textfield;
}
.nowrap .tag{
	position: absolute;
	background: #15D4B5;
	color: #FFF;
	height: 16px;
	line-height: 16px;
	width: 25px;
	text-align: center;
}

</style>
<script type="text/javascript">
function countTotal(){
	
	var totalBoxQuantity = 0;
	var totalBranchQuantity = 0;
	var $bInput = $("input.boxQuantity");
	$bInput.each(function(){
        var $tr = $(this).closest("tr");
        var boxQuantity=$(this).val();
        var branchPerBox=$tr.find(".branchPerBox").val();
        var perBranch=$tr.find(".perBranch").val();
        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
        if(boxQuantity!=''){
        	var branchQuantity=accMul(boxQuantity,branchPerBox);
        	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
        	if(isNaN(branchQuantity)){
        		branchQuantity = 0;
    		}
        	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
        	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
        	$tr.find(".branchQuantity").val(branchQuantity);//支数
        	$tr.find(".branchQuantityStr").html(branchQuantity);
        	var quantity=accMul(branchQuantity,perBranch);
        	if(isNaN(quantity)){
        		quantity = 0;
    		}
        	$tr.find(".quantity").val(quantity);//平方数
        	$tr.find(".quantityStr").html(quantity);
        }
	});
	$("#totalBoxQuantity").text(totalBoxQuantity);
	$("#totalBranchQuantity").text(totalBranchQuantity);
	
	var $input = $("input.quantity");
	var total = 0;
	var totalVolume = 0;
	var totalWeight = 0;
	var totalQuantity = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var price = Number($this.closest("tr").find("input.price").val());
		var saleOrgPrice = Number($this.closest("tr").find("input.saleOrgPrice").val());
		var amount = accMul(Number($this.val()),price).toFixed(2);
		if(isNaN(amount)){
			amount = 0;
		}
		total = accAdd(total,amount).toFixed(2);
		$tr.find(".trprice").html(currency(amount,true));
		
// 		var saleAmount = accMul(Number($this.val()),saleOrgPrice).toFixed(2);
// 		if(isNaN(saleAmount)){
// 			saleAmount = 0;
// 		}
// 		$tr.find(".saprice").html(currency(saleAmount,true));
		
// 		var volume=$tr.find(".lineVolume").val();
// 		var weight=$tr.find(".lineWeight").val();
		
// 		var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
// 		var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);
		
// 		totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
		
// 		if(isNaN(volumeAmount)){
// 			volumeAmount = 0;
// 		}
// 		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
// 		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
		
// 		if(isNaN(weightAmount)){
// 			weightAmount = 0;
// 		}
// 		totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
// 		$tr.find(".lineWeightAmount").html(weightAmount);//重量
		
	});
	$("#amount").text(currency(total,true));
	$("#totalVolume").text(totalVolume);
	$("#totalWeight").text(totalWeight);
	$("#totalQuantity").text(totalQuantity);
	

	[#if hiddenAmount==0]
	$("span.price_span").text("***");
	[/#if]
	
}


var defaultImage = '${companyInfo.logo}';
$().ready(function() {
	var cols = [
		{ title:'${message("产品图片")}', name:'image' , align:'center', renderer: function(val,item,rowIndex){
			var image_str = val;
			if(image_str==undefined || image_str.length==0){
				image_str = defaultImage;
			}
			
			if (item.is_parts) {
				return '<input class="isParts" value="1" type="hidden"/><span class="tag">定制</span><a href="javascript:void(0);" onClick="create_iframe(\'/product/product/content.jhtml?cartItemId='+item.id+'&id='+item.product_id+'&storeId='+item.store_id+'\')" ><img src="'+image_str+'" class="thumb-pic" style="width:60px;height:60px"></a>';
			}
			else {
				return '<input class="isParts" value="0" type="hidden"/><a href="javascript:void(0);" onClick="create_iframe(\'/product/product/content.jhtml?cartItemId='+item.id+'&id='+item.product_id+'&storeId='+item.store_id+'\')" ><img src="'+image_str+'" class="thumb-pic" style="width:60px;height:60px"></a>';
			}			
		}},
		{ title:'${message("产品名称")}', name:'full_name' ,align:'center' },
		{ title:'${message("12211")}', name:'vonder_code' , align:'center',renderer:function(val,item,rowIndex){
			return '<span class="vonder_code">'+val+'</span>';
		} },
		{ title:'${message("产品型号")}', name:'model' , align:'center' },
		{ title:'${message("客户")}', name:'store_name' ,align:'center', renderer: function(val,item,rowIndex){
            return '<input type="hidden" class="item_store_id" value="'+item.store_id+'">'+
            '<input type="hidden" class="text productId" value="'+item.product_id+'">'+
             '<input type="hidden" class="text productGrade" value="'+item.levelId+'">'+
            '<input type="hidden" class="text ci_ids" value="'+item.id+'">'+
            '<input type="hidden" class="item_sale_org_id" value="'+item.sale_org_id+'">' + val;
        }},
        {title:'${message("sbu")}', align:'center',name:'sbu_name',width:130  ,renderer: function(val,item,rowIndex){			
			var str='selected="selected"';
			var html = '<select name="sbuId" class="text sbuId" onchange="product_price(this)">';
				[#list sbus as sbu]
				if(${sbu.id}==item.sbu_id){
					html+='<option value="${sbu.id}" '+str+' >${sbu.name}</option> ';
				}else{
					html+='<option value="${sbu.id}" class="sbuName">${sbu.name}</option> ';
				}
				[/#list]
			return html;
	      }},
        { title:'${message("发货仓")}', name:'warehouseName' ,align:'center',renderer: function(val,item,rowIndex, obj){
			var warehouseName = '';
			var warehouse = '';
			if(obj==undefined){
				warehouseName = (item.warehouseName!=null)?item.warehouseName:'';
				warehouse = (item.warehouse!=null)?item.warehouse:'';
			}
			var html = '<span class="search ">'+
				   	  '<input class="text warehouseId" type="hidden"  value="'+warehouse+'">'+
					  '<input class="text warehouseName" maxlength="200" type="text"  value="'+warehouseName+'" onkeyup="clearSelect(this)" readOnly/>'+
					  '<input type="hidden" class="type" value="">'+
					  '<input type="button" class="iconSearch lineSelectWarehouse" value="">'+
				   '</span>';
			return html;
		} },
		{title:'${message("产品等级")}', align:'center',name:'product_grade',width:130  ,renderer: function(val,item,rowIndex){
			var str='selected="selected"';
			var html='<select name="pg" class="text pg" onchange="product_price(this)">';
				[#list productLevelList as products]
				if(${products.id}==item.levelId){
					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
				}else{
					html+='<option value="${products.id}">${products.value}</option> ';
				}
				[/#list]
				html+='</select>'+'<input type="hidden" class="text productGrade" value="'+item.levelId+'" />';
			return html;
	      }},
        { title:'${message("库存（箱）")}', name:'',align:'center',renderer: function(val,item,rowIndex){
            return '<input type="hidden" class="vonderCode" value="'+item.vonder_code+'"><span class="stockQuantity"></span>';
        }},
        /*{ title:'${message("是否特价")}', name:'is_sale',width:20,align:'center',renderer: function(val,item,rowIndex){
            return '<input type="checkbox" onclick="showSale(this)">'
        }},*/
		{ title:'${message("价格")}', name:'price' , align:'center', renderer: function(val,item,rowIndex){
			if(item.cart_item_price!=undefined){
				val = item.cart_item_price;
				return '<span class="red price_span" val="'+currency(val,true)+'">'+currency(val,true)+'</span>'+'<div class="sale-price" style="display:none"><span class="green">'+currency(val,true)+'</span> <del class="lgray">'+currency(val,true)+'</del></div><input type="hidden" class="price" value="'+val+'"/>'+'<input type="hidden" class="saleOrgPrice" value="1">';
			}else{
				return '<span class="red price_span" val="'+currency(val,true)+'">'+currency(val,true)+'</span>'+'<div class="sale-price" style="display:none"><span class="green">'+currency(val,true)+'</span> <del class="lgray">'+currency(val,true)+'</del></div><input type="hidden" class="price" value="'+val+'"/>'+'<input type="hidden" class="saleOrgPrice" value="1">';				
			}
			
		}},
		/*{ title:'${message("特价单号")}', name:'',align:'center'},*/
// 		{ title:'${message("数量")}', name:'quantity' ,align:'center', renderer: function(val,item,rowIndex){
//             return '<div class="nums-input ov">'+
// 		            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
// 		            	'<input type="text"  class="t quantity" value="'+val+'" minData="0" maxData="1000000000" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
// 		            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
// 		            	'<div class="red msg-tip" style="display:none">* 特价产品可购买2</div>'+
// 	            	'</div>';
//         }},

		{ title:'${message("箱数")}', name:'box_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
			var quantity = 1;
			if(obj==undefined){
				quantity = val;
			}
			var text = '<div class="lh20">'+
						'<div class="nums-input ov">'+
			            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
			            	'<input type="text" kid="box" class="t boxQuantity"  value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
			        	'</div>';
			text += '</div>';
			return text;
		}},
		{ title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			var branchQuantity='';
			if(obj==undefined){
				branchQuantity = val;
			}
			var text = '<div class="lh20">'+
			'<div class="nums-input ov">'+
	        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	        	'<input type="text" kid="branch" class="t branchQuantity" value="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	        	'<input type=hidden class="branchPerBox"  value="'+item.branch_per_box+'" /> '+
				'<input type=hidden class="perBranch"  value="'+item.per_branch+'" />'+
	    	'</div></div>';
	    	return text;
		}},
		{ title:'${message("零散支数")}', name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
			var scatteredQuantity=0;
			if(obj==undefined){
				scatteredQuantity = val;
			}
			var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
				'<input type="hidden" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
			return html;
		}},
		{ title:'${message("平方数")}', name:'quantity', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			var quantity='';
			if(obj==undefined){
				quantity = val;
			}
			var text = '<div class="lh20">'+
			'<div class="nums-input ov">'+
	        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	        	'<input type="text" kid="quantity" class="t quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	        	'<input type="hidden" class="minPriceApplyQuantity" />'+
				'<input type="hidden" class="maxPriceApplyQuantity" />'+
	    	'</div></div>';
	    	return text;
		}},
		{ title:'金额', align:'center',name:'',renderer: function(val,item,rowIndex){
			return '<sapn class="text red trprice">'+currency(item.quantity*item.price,true)+'</span>'
		}},
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
        cols: cols,
        fullWidthRows:true,
        url: 'list_data.jhtml',
        method: 'post',
        params:function(){
        	return $("#listForm").serializeObject();
        },
        callback:function(){	
         	countTotal();
         },
        root: 'content'
    });
    
	var ih = $(window).height() - 42;
	$("#mainiframe").attr("style","border: none;width: 100%;height:"+ih+"px");
	$(".js-cancle").click(function(){
	    $(this).parent().parent().hide(); 
	});
	var $quantity = $("input.quantity");
	var $increase = $("input.increase");
	var $decrease = $("input.decrease");
	var $delete = $(".cancleButton");
	var $demand = $(".demandButton");
	var $custom = $(".customButton");
	
	//删除选中产品
	$delete.live("click",function(){
	    var data =$mmGrid.serializeSelectedIds();
	    if(data.length==0){
	      $.message_alert("${message("请选择需要删除的产品")}");
	      return false;
	    }
	      $.ajax({
	        url: "delete.jhtml",
	        type: "POST",
	        data: data,
	        dataType: "json",
	        cache: false,
	        beforeSend: function() {
	          $delete.prop("disabled", true);
	        },
	        success: function(data) {
	          location.reload(true);
	        },
	        complete: function() {
	          $delete.prop("disabled", false);
	        }
	      });
	});
	
	// 提交
	$demand.live("click",function(){
	      var data =$mmGrid.serializeSelectedIds("ci_ids");
	      
	      if(data.length<=0){
	        $.message_alert("${message("请选择下单产品！")}");
	        return false;
	      }
	      
	      var storeId = $(".selected").eq(0).find(".item_store_id").val();
	      for(i = 0 , j = $(".selected").length ; i < j ;i++ ){
	      	if($(".selected").eq(i).find(".isParts").val() == 0){
	      		$.message_alert("${message("定制订单只能下定制产品！")}");
	        	return false;
	      	}
	      	if($(".selected").eq(i).find(".quantity").val()<=0){
	      		$.message_alert("${message("请选择产品数量！")}");
	        	return false;
	      	}
	      	if ($(".selected").eq(i).find(".item_store_id").val() != storeId) {
	      		$.message_alert("${message("只允许选择相同客户的产品进行下单！")}");
	      		return false;
	      	}
	      }
	      $(this).attr("data-src","http://mk.etwowin.com.cn/b2b/demand_order/list_tb.jhtml?cartId=${cart.id}&"+data);
	      top.open_new_tab(this);
	      //create_iframe('/b2b/demand_order/list_tb.jhtml?cartId=${cart.id}&'+data);
	});
	
	// 提交
	$custom.live("click",function(){
	      var data =$mmGrid.serializeSelectedIds("ci_ids");
	      var rows = $("#table-m1").find(".real-row");
	      var productGrade_id = "";
	      
	      for(var i=0;i<rows.length;i++){
				var $tr = rows.eq(i);
				//判断行状态是否选中
				if($($tr).is('.selected')){
					var id = $tr.find(".ci_ids").val();
					var productGrade = $tr.find(".pg option:selected").val();
					var price = $tr.find("input.price").val();
					productGrade_id += "&productGrade_id"+"="+id+"|"+productGrade+"|"+price;					
				}
		  }
	      
	      if(data.length<=0){
	        $.message_alert("${message("请选择下单产品！")}");
	        return false;
	      }
	      var storeId = $(".selected").eq(0).find(".item_store_id").val();
	      var warehouseId=$(".selected").eq(0).find(".warehouseId").val();
	      var sbuId=$(".selected").eq(0).find(".sbuId").val();
	      var sbuName=$(".sbuId").eq(0).find("option:selected").text();
	      
	      var dataName='';
	      if(warehouseId==null || warehouseId==""){
	      	$.message_alert("${message("请选择仓库！")}");
	        return false;
	      }
	      
	      
	  		if(sbuName == "Nature"){
      			dataName='Nature下单';
      		}else if(sbuName == "进口三层"){
      			dataName='进口三层下单';
      		}
      		else if(sbuName == "地板中心"){
      			dataName='地板中心下单';
      		}
      		else if(sbuName == "壁纸"){
      			dataName='壁纸下单';
     	 	}
	  		
	  		
	      for(i = 0 , j = $(".selected").length ; i < j ;i++ ){
	      if($(".selected").eq(i).find(".isParts").val() == 1){
	      		$.message_alert("${message("正式单只能下非定制产品！")}");
	        	return false;
	      	}
	      	if($(".selected").eq(i).find(".quantity").val()<=0){
	      		$.message_alert("${message("请选择产品数量！")}");
	        	return false;
	      	}
	      	if ($(".selected").eq(i).find(".item_store_id").val() != storeId) {
	      		$.message_alert("${message("只允许选择相同客户的产品进行下单！")}");
	      		return false;
	      	}
	      	if ($(".selected").eq(i).find(".sbuId").val() != sbuId) {
	      		$.message_alert("${message("只允许选择相同sbu进行下单！")}");
	      		return false;
	      	}
	      	if ($(".selected").eq(i).find(".warehouseId").val() != warehouseId) {
	      		$.message_alert("${message("只允许选择相同仓库的产品进行下单！")}");
	      		return false;
	      	}
	      	if($(".selected").eq(i).find(".branchQuantity").val()%1!=0){
	      		$.message_alert("${message("下单支数不允许为小数")}");
	        	return false;
	      	}
	      
	      }
	      $(this).attr("data-src","/b2b/order/list_tb.jhtml?cartId=${cart.id}&"+data+"&warehouseId="+warehouseId+"&sbuId="+sbuId+"&storeId="+storeId+productGrade_id);
	      $(this).attr("data-name",dataName);
	      top.open_new_tab(this);
	      //create_iframe('/b2b/order/info.jhtml?cartId=${cart.id}&'+data);
	});
	
	
	//选择仓库
	$(".lineSelectWarehouse").live("click",function(){
		var $this = $(this);
		var $tr =$this.closest("tr");
		var productId=$tr.find(".productId").val();
//		var productGrade=$tr.find(".productGrade").val();
		var productGrade = $tr.find(".pg option:selected").val();//产品等级
		var itemGrade =$tr.find(".pg option:selected").text();
		var carItemId=$tr.find(".ci_ids").val();
		var saleOrgId=$tr.find(".item_sale_org_id ").val();
		var sbuId=$tr.find(".sbuId option:selected").val();
		
		var storeId = $tr.find(".item_store_id").val();
		var typeSystemDictId=$(".typeSystemDictId").val();
		var businessTypeName = $("#businessTypeName").val();
		var isMember=$(".isMember").val();
		$this.bindQueryBtn({
			type:'warehouse',
            bindClick:false,
			title:'${message("查询仓库")}',
			url:'/stock/warehouse/select_warehouse.jhtml?saleOrgId='+saleOrgId+"&sbuId="+sbuId,
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
        			var ssId = $("input[name='storeId']").val();
        			var itemCode=$tr.find(".vonderCode").val();
        			var organizationId=row.management_organization;
        			if(organizationId!=null){
        				if(organizationId==7){
        					organizationId=125;
        				}
        				else{
        					organizationId=381;
        				}
        			}
	        		$tr.find(".type").val(row.type_system_dict);
	        		$tr.find(".warehouseId").val(row.id);
	        		$tr.find(".warehouseName").val(row.name);
	        		product_price($this);//调用计算金额方法
	        		//实时查询库存
	        		$mmGrid._showLoading();
	        		ajaxSubmit($(row.id),{
						method:'post',
						url:'/stock/stock/stockInfolist_data.jhtml',
						//产品等级,仓库id,产品编码,组织
						data:{itemGrade:itemGrade,warehouseId:row.id,itemCode:itemCode,organizationId:organizationId},
						callback:function(resultMsg) {
							 var data = $.parseJSON(resultMsg.content);
							 if(data.length<1){
							 	$tr.find(".stockQuantity").html(0);
							 }else{
							 	var stockQuantity=data[0].attQuantity2;
							 	if(data[0].isDisplay!='' && data[0].isDisplay=='true' || isMember==0){
							 		$tr.find(".stockQuantity").html(stockQuantity);
							 	}else{
							 	   $tr.find(".stockQuantity").html("***");
							 	}
							 	
							 }

							 $mmGrid._hideLoading();
						}
					});
	        		
        			/*
        			//查询当前库存的商品在价格表的对应价格
                     var price = $tr.find("input.price").val();
   					ajaxSubmit($(row.id),{
   						method:'post',
   						url:'/product/product_price_head/findProductPrice.jhtml',
   						data:{storeId:storeId,productId:productId,warehouseId:row.id,saleOrgId:saleOrgId,productGrade:productGrade},
   						callback:function(resultMsg) {
   							var data = resultMsg.objx;  
   							if (data !=null) {
   								price = data.store_member_price;
   	   							if (isNaN(data.store_member_price)) {
   	   								$.message_alert("${message("请在价格表维护该产品价格")}");
   	   					      		return false;
   								}else {
   									$tr.find("input.price").val(data.store_member_price);
   									$tr.find(".real-price").text(currency(data.store_member_price,true));
   									$tr.find(".sale-price").text(currency(data.store_member_price,true));
   								}
							}else {
								$.message_alert("${message("请在价格表维护该产品价格")}");
								$tr.find("input.price").val(0);
									$tr.find(".real-price").text(currency(0,true));
									$tr.find(".sale-price").text(currency(0,true));
							}
   							
   						}
   					});   */
           		/*	ajaxSubmit($(row.id),{
						method:'post',
						url:'/b2b/cart/updateWarehouse.jhtml',
						data:{warehouseId:row.id,price:price,carItemId:carItemId},
						callback:function(resultMsg) {
			   				  $mmGrid._showLoading();
			        			ajaxSubmit($(row.id),{
									method:'post',
									url:'/stock/stock/stockInfolist_data.jhtml',
									data:{itemGrade:'优等品',warehouseId:row.id,itemCode:itemCode,organizationId:organizationId},
									callback:function(resultMsg) {
										 var data = $.parseJSON(resultMsg.content);
										 if(data.length<1){
										 	$tr.find(".stockQuantity").html(0);
										 }else{
										 	var stockQuantity=data[0].attQuantity2;
										 	$tr.find(".stockQuantity").html(stockQuantity);
										 }  

										 $mmGrid._hideLoading();
									}
								})    
								
						}
					}) 	*/	
				} 
			}
		});
	})
	
	
	/* //选择仓库
	$(".lineSelectWarehouse").live("click",function(){
		var $this = $(this);
		var $tr =$this.closest("tr");
		var productId=$tr.find(".productId").val();
		var $saleOrgId=$tr.find(".item_sale_org_id").val();
		$this.bindQueryBtn({
			type:'warehouse',
            bindClick:false,
			title:'${message("查询仓库")}',
			url:'/stock/warehouse/select_infowarehouse.jhtml?saleOrgId='+$saleOrgId
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
        			var ssId = $("input[name='storeId']").val();
        			var itemCode=$tr.find(".vonderCode").val();
        			var organizationId=row.management_organization;
        			if(organizationId!=null){
        				if(organizationId==7){
        					organizationId=125;
        				}
        				else{
        					organizationId=381;
        				}
        			}
        			$mmGrid._showLoading();
        			ajaxSubmit($(row.id),{
						method:'post',
						url:'/stock/stock/stockInfolist_data.jhtml',
						data:{itemGrade:'优等品',warehouseId:row.id,itemCode:itemCode,organizationId:organizationId},
						callback:function(resultMsg) {
							 var data = $.parseJSON(resultMsg.content);
							 if(data.length<1){
							 	$tr.find(".stockQuantity").html(0);
							 }else{
							 	var stockQuantity=data[0].attQuantity2;
							 	$tr.find(".stockQuantity").html(stockQuantity);
							 }

							 $mmGrid._hideLoading();
						}
					})
					
					$tr.find(".warehouseId").val(row.id);
        			$tr.find(".warehouseName").val(row.name);
					
				}
			}
		});
	})*/

	
});



//金额查询
function product_price(e){
	var $tr = $(e).closest("tr");
	var id = $tr.find(".ci_ids").val();//购物车id
	var storeId = $tr.find(".item_store_id").val();//客户id
	var productId = $tr.find(".productId").val();//产品id
	var warehouseId = $tr.find(".warehouseId").val();//仓库id
	var saleOrgId=$tr.find(".item_sale_org_id ").val();//机构id
	var productGrade = $tr.find(".pg option:selected").val();//产品等级
	var warehouseType = $tr.find(".type").val();//仓库类型
	var sbuId = $tr.find(".sbuId option:selected").val();//sbu ID
	var vonderCode = $tr.find(".vonder_code").text();//产品编码
	//行金额
	var price = $tr.find(".price_span").text();
	var priceSpan = $tr.find(".price").val();
	
	var saleOrgPrice = $tr.find(".saleOrgPrice").val();
	
	ajaxSubmit(e,{
		method:'post',
		url:'/product/product_price_head/findProductPrice.jhtml',
		data:{storeId:storeId,productId:productId,warehouseId:warehouseId,saleOrgId:saleOrgId,
			productGrade:productGrade,warehouseShippingId:warehouseType,sbuId:sbuId},
		callback:function(resultMsg) {
			var data = resultMsg.objx;
			var price = null;
			var saleOrgPrices = null;
			if (data !=null) {
				price = data.store_member_price;
				saleOrgPrices = data.sale_org_price;
				if (isNaN(data.store_member_price)) {
					$.message_alert("${message("请在价格表维护该产品价格")}");
				    return false;
				}else {
					$tr.find("input.price").val(data.store_member_price);
					$tr.find(".price_span").text(currency(data.store_member_price,true));
					$tr.find(".saleOrgPrice").val(data.sale_org_price);
				}
			}else {
				$.message_alert("${message("请在价格表维护该产品价格")}");
				$tr.find("input.price").val(0);
				$tr.find(".price_span").text(0);
				$tr.find(".price_span").val(0);
				$tr.find(".real-price").text(currency(0,true));
				$tr.find(".sale-price").text(currency(0,true));
				$tr.find(".saleOrgPrice").val(0);
			}
			countTotal();
			update(id,sbuId,warehouseId,productGrade,price,saleOrgPrices);
		}
		
	});
	
}

//编辑数量
function update(id,sbu,warehouse,productGrade,price,saleOrgPrices) {
        $.ajax({
          url: "update.jhtml",
          type: "POST",
          data: {id:id,sbuId:sbu,warehouseId:warehouse,
        	  productGrade:productGrade,price:price,saleOrgPrice:saleOrgPrices},
          dataType: "json",
          cache: false,
          success: function(data) {
            
          }
        });

}



function showSale(e){
	if($(e).is(':checked')){
		$(e).closest("tr").find(".real-price").hide();
		$(e).closest("tr").find(".sale-price").show();
		$(e).closest("tr").find(".msg-tip").show();
	}else{
		$(e).closest("tr").find(".real-price").show();
		$(e).closest("tr").find(".sale-price").hide();
		$(e).closest("tr").find(".msg-tip").hide();
	}
}

function editQty(t,e){
	if(extractNumber(t,6,false,e)){
		
		var qy=0;
		var $tr = $(t).closest("tr");
		if($(t).attr("kid")=="box"){
			$tr.find(".scatteredQuantityStr").html(0);
			$tr.find(".scatteredQuantity").val(0);
		}else if($(t).attr("kid")=="branch"){
			var quantity=$(t).val();
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var box=parseInt(quantity/branchPerBox);
			var scattered=quantity%branchPerBox;
			$tr.find(".boxQuantity").val(box);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}else if($(t).attr("kid")=="quantity"){
			var quantity=$(t).val();
			var qy=$(t).val();
			var perBranch=$tr.find(".perBranch").val();  //每支单位数
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var branch_quantity=quantity/perBranch;
			var box_quantity=parseInt(branch_quantity/branchPerBox);
			var scattered=(branch_quantity%branchPerBox).toFixed(6);
			$tr.find(".boxQuantity").val(box_quantity);
			$tr.find(".branchQuantity").val(branch_quantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}
		
		countTotal();
		if($(t).attr("kid")=="quantity"){
			$(t).val(qy);
		}
		var id = $(t).attr("itemId");
		var itemOrg = Number($(t).attr("org"));
		var qty = Number($(t).val());
		if(isNaN(qty)){
			qty = 0;
		}
		
		var $input = $("input.cq[parentId='"+id+"']");
		$input.each(function(){
			var $this = $(this);
			var org = Number($this.attr("org"));
			var value = qty*org/itemOrg;
			$this.val(value);
			$this.next(".qty-text").text(value);
		})
		
		edit(t);
	}
}

// 编辑数量
function edit(t) {
	var $quantity = $(t);
    var $tr = $quantity.closest("tr");
    var quantity = $tr.find(".quantity").val();
      var boxQuantity=$tr.find(".boxQuantity").val();
      var branchQuantity=$tr.find(".branchQuantity").val();
      var scatteredQuantity=$tr.find(".scatteredQuantity").val();
      var id = $mmGrid.row($tr.attr("rowindex")).id;
        $.ajax({
          url: "edit.jhtml",
          type: "POST",
          data: {id:id,quantity:quantity,boxQuantity:boxQuantity,branchQuantity:branchQuantity,scatteredQuantity:scatteredQuantity},
          dataType: "json",
          cache: false,
          beforeSend: function() {
            $quantity.prop("disabled", true);
          },
          success: function(data) {
            
          },
          complete: function() {
            $quantity.prop("disabled", false).focus();
          }
        });

}

//清空购物车的发货仓
function clearWarehouse(){
	$(".warehouseName").val("");
	$(".warehouseId").val("");
}

</script>
</head>
<body>
<form id="listForm" action="list.jhtml" method="get">
  	<input type="hidden" name="cartId" value="${cart.id}" />
  	<input type="hidden" class="hiddenAmount" value="${hiddenAmount}" />
  	<input type="hidden" class="isMember" value="${isMember}" />
  	<div class="bar">
    	<div class="buttonWrap">
		<input type="button" class="button customButton" value="${message("提交订单")}" second-id="cart-order" data-src="" data-name="">
      	[#if isInfoDemandOrder>0]
    	<a href="javascript:void(0);" class="iconButton demandButton" id="addButton" second-id="cart-demand" data-src="" data-name="定制订单下达">
			<span class="addIcon">&nbsp;</span>${message("定制订单下达")}
		</a>
		[/#if]
      	<input type="button" class="button cancleButton" value="${message("删除")}">
    	</div>
    	<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("搜索")}</a> </div>
    	<div id="searchDiv" style="padding:0;border:0px">
    	</div>
  	</div>
  	<div class="table-responsive">
    	<table id="table-m1"></table>
  	</div>
</form>

</body>
</html>