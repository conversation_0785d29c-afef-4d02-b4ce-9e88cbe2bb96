
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>汇总经销商提报</title>
	<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
	<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
	<script type="text/javascript" src="/resources/js/base/request.js"></script>
	<script type="text/javascript" src="/resources/js/base/global.js"></script>
	<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
	<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
	<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript">
		$().ready(function() {
			var cols = [
				{ title:'产品名称', name:'name' ,align:'center'},
				{ title:'产品描述', name:'description', align:'center',width:200 },
				{ title:'产品编码', name:'vonder_code' ,align:'center'},
				{ title:'木种/花色', name:'wood_type_or_color' ,align:'center'},
				{ title:'型号', name:'model' ,align:'center'},
				{ title:'规格', name:'spec' ,align:'center'},
				{ title:'含水率', name:'moisture_content_name' ,align:'center'},
				{ title:'业务类型', name:'business_type_name' ,align:'center'},
				{ title:'${message("sbu")}', name:'sbu_name' , align:'center'},
				{ title:'${message("汇总平方数")}', name:'quantity' , align:'center', renderer:function(val,item,rowIndex,obj){
					return '<span style="font-weight: bold;color: #d6524a">'+ item.quantity +'</span>'
				}},
				{ title:'${message("未汇总数")}', name:'notGathered' , align:'center', renderer:function(val,item,rowIndex,obj){
						return '<span style="font-weight: bold;color: #07ad32">'+ item.notGathered +'</span>'
				}}
			];
			var multiSelect = true;
			$mmGrid = $('#table-m1').mmGrid({
				multiSelect:multiSelect,
				autoLoad: true,
				fullWidthRows:true,
				checkByClickTd:true,
				rowCursorPointer:true,
				formQuery:true,
				cols: cols,
				url: 'findAgentData.jhtml',
				params:function(){
					return $("#listForm").serializeObject();
				},
				plugins : [
					$('#paginator').mmPaginator()
				]
			});
		});
		function childMethod(){
			return $mmGrid.selectedRows();
		};
	</script>
</head>
<body style="min-width:0px;">
<form id="listForm" action="selectProductData.jhtml" method="get">
	<input type="hidden" name="saleOrgId" value="${saleOrgId}">
	<input type="hidden" name="month" value="${month}">
	<div class="bar">
		<div class="buttonWrap"></div>
		<div id="searchDiv">
			<div id="search-content" >
				<dl>
					<dt><p>产品名称：</p></dt>
					<dd>
						<input type="text" class="text" id="name" name="name" value ="" btn-fun="clear" />
					</dd>
				</dl>
				<dl>
					<dt><p>产品编码：</p></dt>
					<dd>
						<input type="text" class="text" id="vonderCode" name="vonderCode" value ="" btn-fun="clear" />
					</dd>
				</dl>
				<dl>
					<dt><p>业务类型：</p></dt>
					<dd>
						<select name="businessTypeId" class="text">
							<option value="">请选择</option> 
							[#list businessTypes as businessType]
								<option value="${businessType.id}">${businessType.value}</option>
							[/#list]
						</select>
					</dd>
				</dl>
			</div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">搜索</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
		<div id="body-paginator" style="text-align:left;">
			<div id="paginator"></div>
		</div>
	</div>
</form>
</body>
</html>