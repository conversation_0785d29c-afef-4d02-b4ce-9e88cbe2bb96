<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("销售发票")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function edit(id){
	parent.change_tab(0,'/b2b/contract/edit/${code}.jhtml?id='+id);
}
function add(){
	parent.change_tab(0,'/b2b/contract/tj_add/${code}.jhtml');
}
$().ready(function() {
	/**初始化多选的下拉框*/<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("销售发票")}</title>
	<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
	<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
	<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
	<script type="text/javascript" src="/resources/js/base/request.js"></script>
	<script type="text/javascript" src="/resources/js/base/global.js"></script>
	<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
	<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	function edit(id){
		parent.change_tab(0,'/b2b/contract/edit/${code}.jhtml?id='+id);
	}
	function add(){
		parent.change_tab(0,'/b2b/contract/tj_add/${code}.jhtml');
	}
	$().ready(function() {
		/**初始化多选的下拉框*/
		initMultipleSelect();
		
		
		var statuss = {'0':'已保存', '1':'已审核', '2':'已作废'};
		var cols = [
			{ title:'${message("合同编号")}', name:'sn' , align:'center', renderer:function(val,item){
				return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
			}},
			{ title:'${message("合同流水号")}', name:'contract_no' ,align:'center'},
			{ title:'${message("合同名称")}', name:'contract_name' ,align:'center'},
			{ title:'${message("合同类型")}', name:'contract_type' ,align:'center'},
			{ title:'${message("合同金额")}' ,name:'contract_amt', align:'center',width:80, renderer:function(val,item,rowIndex,obj){
				var html = '<span class="red contract_amt">'+currency(val,true)+'</span>';
				return html;
			}},
			{ title:'${message("客户名称")}', name:'store_name' ,align:'center'},
			{ title:'${message("销售体系")}', name:'sale_org_type_name' ,align:'center'},
			{ title:'${message("业务员")}', name:'sale_man' ,align:'center'},
			{ title:'${message("机构")}', name:'sale_org_name' ,align:'center'},
			{ title:'${message("项目名称")}', name:'xmmc' ,align:'center'},
			{ title:'${message("项目号")}', name:'xm_no' ,align:'center'},
			{ title:'${message("项目地址")}', name:'xmdz' ,align:'center'},
			{ title:'${message("是否异地")}', name:'is_yd' ,align:'center', renderer:function(val,item){
				if(val=='1'){
					return '<span class="trueIcon">&nbsp;</span>';
				}else if(val=='0'){
					return '<span class="falseIcon">&nbsp;</span>';
				}
			}},
			{ title:'${message("异地分公司")}', name:'yd_company_name' ,align:'center'},
			{ title:'${message("创建时间")}', name:'create_date' ,width:120, align:'center'}
		];
		$mmGrid = $('#table-m1').mmGrid({
			autoLoad: true,
	        cols: cols,
	        fullWidthRows:true,
	        url: '/b2b/contract/list_data.jhtml',
	        params:function(){
	        	return $("#listForm").serializeObject();
	        },
		    plugins : [
	            $('#paginator').mmPaginator()
	        ]
	    });
	    
		//查询客户
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
		});
	});
	</script>
	</head>
	<body>
		<form id="listForm" action="/b2b/contract/list/${code}.jhtml" method="get">
		<input type="hidden" name="isCheck" id="isCheck" value="${isCheck}" >
			<div class="bar">
				<div class="buttonWrap">
				[#if isCheck != 1]
					<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
				[/#if]	
				</div>
				<div id="searchDiv">
	        	<div id="search-content" >
	        		<dl>
	        			<dt><p>${message("客户名称")}：</p></dt>
	        			<dd >
	        				<span class="search" style="position:relative">
								<input type="hidden" name="storeId" class="text storeId" id="storeId" btn-fun="clear" />
								<input type="text" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" id="storeName" />
								<input type="button" class="iconSearch" value="" id="selectStore">
							</span>
	        			</dd>
	        		</dl>
	        		<dl>
			    		<dt ><p>${message("合同流水号")}：</p></dt>
		    			<dd>
		    				<input type="text" class="text" name="sn" value ="" btn-fun="clear" />
		    			</dd>
			    	</dl>
			    	<dl>
			    		<dt><p>${message("合同编号")}：</p></dt>
			    		<dd>
							<input type="text" class="text" name="contract_no" value ="" btn-fun="clear" />
			    		</dd>
			    	</dl>
				</div>
			<div class="search-btn" style="height:32px"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
			</div>
				
			</div>
			<div class="table-responsive">
		        <table id="table-m1"></table>
		        <div id="body-paginator">
		            <div id="paginator"></div>
		        </div>
			</div>
		</form>
	</body>
	</html>
	initMultipleSelect();
	
	
	var statuss = {'0':'已保存', '1':'已审核', '2':'已作废'};
	var cols = [
		{ title:'${message("合同编号")}', name:'sn' , align:'center', renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
		}},
		{ title:'${message("合同流水号")}', name:'contract_no' ,align:'center'},
		{ title:'${message("合同名称")}', name:'contract_name' ,align:'center'},
		{ title:'${message("合同类型")}', name:'contract_type' ,align:'center'},
		{ title:'${message("合同金额")}' ,name:'contract_amt', align:'center',width:80, renderer:function(val,item,rowIndex,obj){
			var html = '<span class="red contract_amt">'+currency(val,true)+'</span>';
			return html;
		}},
		{ title:'${message("客户名称")}', name:'store_name' ,align:'center'},
		{ title:'${message("销售体系")}', name:'sale_org_type_name' ,align:'center'},
		{ title:'${message("业务员")}', name:'sale_man' ,align:'center'},
		{ title:'${message("机构")}', name:'sale_org_name' ,align:'center'},
		{ title:'${message("项目名称")}', name:'xmmc' ,align:'center'},
		{ title:'${message("项目号")}', name:'xm_no' ,align:'center'},
		{ title:'${message("项目地址")}', name:'xmdz' ,align:'center'},
		{ title:'${message("是否异地")}', name:'is_yd' ,align:'center', renderer:function(val,item){
			if(val=='1'){
				return '<span class="trueIcon">&nbsp;</span>';
			}else if(val=='0'){
				return '<span class="falseIcon">&nbsp;</span>';
			}
		}},
		{ title:'${message("异地分公司")}', name:'yd_company_name' ,align:'center'},
		{ title:'${message("创建时间")}', name:'create_date' ,width:120, align:'center'}
	];
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/b2b/contract/list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
});
</script>
</head>
<body>
	<form id="listForm" action="/b2b/contract/list/${code}.jhtml" method="get">
	<input type="hidden" name="isCheck" id="isCheck" value="${isCheck}" >
		<div class="bar">
			<div class="buttonWrap">
			[#if isCheck != 1]
				<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			[/#if]	
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
        			<dt><p>${message("客户名称")}：</p></dt>
        			<dd >
        				<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" id="storeId" btn-fun="clear" />
							<input type="text" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" id="storeName" />
							<input type="button" class="iconSearch" value="" id="selectStore">
						</span>
        			</dd>
        		</dl>
        		<dl>
		    		<dt ><p>${message("合同流水号")}：</p></dt>
	    			<dd>
	    				<input type="text" class="text" name="sn" value ="" btn-fun="clear" />
	    			</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("合同编号")}：</p></dt>
		    		<dd>
						<input type="text" class="text" name="contract_no" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
			</div>
		<div class="search-btn" style="height:32px"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
			
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>