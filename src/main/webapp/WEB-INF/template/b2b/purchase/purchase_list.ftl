<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("ERP采购信息")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />	
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">

function showPage(resultMsg){
	var pageNumber=1;
	var totaNum=0;
	var pageSize=200;
	if(resultMsg!=undefined){
		var content_data = $.parseJSON(resultMsg.content);
		totaNum = content_data.total;
		pageSize = content_data.pageSize;
		pageNumber = content_data.pageNumber;
	}
	
	
	var page = parseInt(totaNum/pageSize);
	if(totaNum%pageSize!=0){
		page = page+1;
	}
	if(page==0){
		page = 1;
	}
	
	
	
	$("#s-page").remove();
/* 	var $str = $('<p id="s-page" style="float:left;line-height:38px;font-size:14px;margin-left:5px">当前第<span style="color:blue">'+pageNumber+'</span>页，共<span style="color:blue">'+page+'</span>页，总数<span style="color:blue">'+totaNum+'</span></p>');
	$str.insertAfter(".pageList .next"); */

}
$().ready(function() {
	
	
	initMultipleSelect();


	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?multi=2&isSelect=0',
		callback:function(rows){
			if(rows.length>0){
				var mhtml="";
				if($("input[name='storeName']").val() == null){
					var all= "";
				}else{
					var all= $("input[name='storeName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".storeId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('客户【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					all =all +','+ rows[i].name;
					mhtml = '<div><input name="storeId" class="text storeId storeId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
					$(".store").append(mhtml);
				}
				$("input[name='storeName']").attr("value",all);
			}
		}
	});
	
	

	
	var status = {'20':'已确认','30':'已派车','40':'服务中','50':'已完成','90':'已取消'};
	var cols = [
		{ title:'${message("CRM单号")}', name:'CUST_PO_NUMBER' ,align:'center' },
		{ title:'${message("erp单号")}', name:'ORDER_NUMBER' ,align:'center' },
		{ title:'${message("采购订单号")}', name:'PO_NUMBER' ,align:'center' },
		{ title:'${message("订单行")}', name:'PO_LINE_NUMBER' , align:'center'},
		{ title:'${message("客户编码")}', name:'ACCOUNT_NUMBER' ,  align:'center'},
		{ title:'${message("客户名称")}', name:'CUSTOMER_NAME', align:'center'},
		{ title:'${message("物料号")}', name:'ITEM_CODE', align:'center'},
		{ title:'${message("物料描述")}', name:'ITEM_DESC', align:'center'},
		{ title:'${message("单位")}', name:'ITEM_UOM', align:'center'},
		{ title:'${message("订单数量")}', name:'ORDER_QTY', align:'center'},
		{ title:'${message("发运数量")}', name:'SHIP_QTY', align:'center'},
		{ title:'${message("供应商名称")}', name:'VENDOR_NAME', align:'center'},
		{ title:'${message("供应商地址")}', name:'VENDOR_ADDRESS', align:'center'},
		{ title:'${message("采购员")}', name:'BUYER', align:'center'},
		{ title:'${message("采购行备注")}', name:'LINE_REMARK', align:'center'},
		{ title:'${message("含税价格")}', name:'MARKET_PRICE', align:'center'},
		{ title:'${message("不含税价格")}', name:'UNIT_PRICE', align:'center'},
		{ title:'${message("SBU")}', name:'SBU', align:'center'},
		{ title:'${message("供应来源")}', name:'SUPPORT_PRI', align:'center'},
		{ title:'${message("转单时间")}', name:'OPREATE_DATE', align:'center'},
		{ title:'${message("需求时间")}', name:'NEED_BY_DATE', align:'center'},
		{ title:'${message("现有量")}', name:'ONHAND_QTY', align:'center'},
		{ title:'${message("采购订购数量")}', name:'PO_QTY', align:'center'},
		{ title:'${message("采购入库数量")}', name:'RCV_QTY', align:'center'},
		{ title:'${message("采购订单状态")}', name:'PO_HEAD_STATUS', align:'center'},
		{ title:'${message("采购订单行状态")}', name:'PO_LINE_STATUS', align:'center'},
		{ title:'${message("销售订单状态")}', name:'ORDER_HEAD_STATUS', align:'center'},
		{ title:'${message("销售订单行状态")}', name:'LINE_STATUS', align:'center'},
		{ title:'${message("转单状态")}', name:'STATUS', align:'center'},
		
		
	];

		$mmGrid = $('#table-m1').mmGrid({
		autoLoad: false,
        cols: cols,
        fullWidthRows:false,
        url: '/b2b/purchase/purchase_list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ],
        callback:function(data,resultMsg){
        	showPage(resultMsg);
        }
        
        
    });
	 showPage();
	
	
});




</script>
</head>
<body>
	<form id="listForm" action="/b2b/purchase/purchase_list.jhtml" method="get">
		<div class="bar">
		<div class="buttonWrap">
				
				</div>

			<div id="searchDiv">
			   <div id="search-content" >
        		<dl>
        			<dt><p>${message("经营组织")}：</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" readonly="readonly"/>
				       		<div class="statusList cs-box" data-value="off">
				       			[#list organizations as organization]
									<label><input class="check js-iname" name="organizationId" value="${organization.code}" type="checkbox"/>${organization.name}</label>
								[/#list]
				       		</div>
					     </div>
        			</dd>
        		</dl>
        		<dl>
					 	<dt><p>${message("供应来源")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="sourcing" value =""  btn-fun="clear"/>
						<dd>
					</dl>
						<dl>
					<dt><p>${message("客户名称")}:</p></dt>
	    			<dd>
	    				<span style="position:relative">
							<input name="storeId" class="text storeId" type="hidden" value="" onkeyup="clearSelect(this)" id="storeId">
							<input class="text  storeName" autocomplete="off" maxlength="200" type="text" name="storeName" value=""  id="storeName" onkeyup="clearSelect(this)" readonly="readonly">
							<input type="button" class="iconSearch" value="" id="selectStore">
							<div class="pupTitleName store"></div>
						</span>
	    			</dd>
	    		</dl>
	    			<dl>
						  <dt><p>${message("转单状态")}:</p></dt>
						  <dd>
								<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
						       		<div class="statusList cs-box" data-value="off">
						       			<label><input  class="check js-iname" name="changeState" value="是" type="checkbox"   />${message("是")}</label>
						       			<label><input  class="check js-iname" name="changeState" value="否" type="checkbox" />${message("否")}</label>						 
						       		</div>
						       	</div>
				    		</dd>
					</dl>
					<dl>
					 	<dt><p>${message("供应商")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="supplier" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
					 	<dt><p>${message("ERP订单号")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="erpSN" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
					 	<dt><p>${message("采购单号")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="purchaseSn" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
					 	<dt><p>${message("物料编码")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="vonderCode" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
					 	<dt><p>${message("LINK发货单号")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="shippingSn" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
					 	<dt><p>${message("业务人员")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="sales" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
						  <dt><p>${message("一揽子价格")}:</p></dt>
						  <dd>
								<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
						       		<div class="statusList cs-box" data-value="off">
						       			<label><input  class="check js-iname" name="packagePrice" value="是" type="checkbox"   />${message("是")}</label>
						       			<label><input  class="check js-iname" name="packagePrice" value="否" type="checkbox" />${message("否")}</label>						 
						       		</div>
						       	</div>
				    		</dd>
					</dl>
				<dl>
					<dt><p>${message("订单创建时间")}:</p></dt>
					<dd class="date-wrap">
							<input id="startTime" name="orderFirstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="orderLastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
					</dd>
				</dl
				<dl>
					<dt><p>${message("转单时间")}:</p></dt>
					<dd class="date-wrap">
							<input id="startTime" name="changeFirstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="changeLastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
					</dd>
				</dl
			
					
				
        		
		     </div>
		     <div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
				</div>
				
			[#--搜索end--]
		</div>
		
		
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>