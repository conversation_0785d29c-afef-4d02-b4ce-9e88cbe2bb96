<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增发票")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<style type="text/css">
	tr.s-tr,tr.s-tr td{height:10px !important;}
</style>
<script type="text/javascript">

	function setDate(){
		var lineDefaultDate = $("input[name='lineDefaultdate']").val();
		var $startDate = $("input.needDate");
		$startDate.each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			var needDate = $tr.find(".needDate").val();
			if (isEmpty(needDate)) {
				$tr.find(".needDate").val(lineDefaultDate);
			}
		});
	}

	function editQty(t,e,n){
		var rate = 6;
		if(n!=undefined)rate = n;
		if(extractNumber(t,rate,true,e)){
			if($(t).hasClass("adjustQuantity")){
				editLineQuantity();
			}
			editTotal();
		}
	}

	function editLineQuantity(){
		$("input.quantity").each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			var adjustQuantity = $tr.find(".adjustQuantity").val();
			var quantity = $this.val();
			if(isNaN(adjustQuantity)){
				adjustQuantity = 0;
			}
			if(isNaN(quantity)){
				quantity = 0;
			}
			var lineTotal = accAdd(quantity,adjustQuantity);
			if(isNaN(lineTotal)){
				lineTotal = 0;
			}
			$tr.find(".lineQuantity").text(lineTotal);
		});
	}

	function editTotal(){
		var total = 0;
		$("input.quantity").each(function(){
			var $this = $(this);
			var quantity = $this.val();
			total = accAdd(total,quantity);
		});
		$("#totalQ").text(total.toFixed(2));
		$("#totalQuantity").val(total.toFixed(2));
	}



	function setSbu(){
		$("input.quantity").each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			var productId = $tr.find(".product_id").val();
			var sbu = $tr.find(".sbu").val();
			if(!isEmpty(productId) && isEmpty(sbu)){
				$.ajax({
					url:'/basic/sbu/select_sbu_by_product_id.jhtml',
					async: true,
					type: "post",
					data:{productId:productId},
					success:function(resultMsg) {			
						var sbus = resultMsg.objx;
						var html = '<option value="">请选择</option>';
						for(var i=0;i<sbus.length;i++){
							html += '<option value="'+sbus[i].id+'" ';
							if(sbus[i].is_default == true){
								html+=' selected ';
							};
							html += '> '+sbus[i].name+'</option>';
						};
						$tr.find(".sbu").html(html);
					}
				})
			}
		});
	}

	$().ready(function() {
        var remark = $("#remark").val();
        if(remark.indexOf('零') >= 0){
            $("#isZeroPlan").val(1);
        }
		var $deleteProduct = $("a.deleteProduct");
		var $addProduct = $("#addProduct");
		var $addProductDict = $("#addProductDict");
		$("form").validate({
			rules: {needDate: "required"}
		});
		var itemIndex = 0;
		var mapList = ${mapList};
	    var cols = [	
			{ title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
				return '<span class="line_no"></span>';
			}},    
			{ title:'${message("产品编码")}', name:'vonder_code' , align:'center',width:130,renderer: function(val,item,rowIndex,obj){
				var html = '<input type="hidden"  name="planApplyItems['+itemIndex+'].type" value="'+item.type+'"/>';
				if(obj==undefined){
					if(!isEmpty(item.product)){
					 	html += val+'<input type="hidden" class="product_id"  name="planApplyItems['+itemIndex+'].product.id" value="'+item.product+'"/>';
					}
				 	html += '<input type="hidden"  name="planApplyItems['+itemIndex+'].id" value="'+item.id+'"/>';
				}else{
					if(!isEmpty(item.id)){
						html += '<input type="hidden" class="product_id"  name="planApplyItems['+itemIndex+'].product.id" value="'+item.id+'">'+val;
					}
				}
				return html;
			}},
			{ title:'${message("产品描述")}', align:'center', width:250, name:'description',renderer: function(val,item,rowIndex,obj){
		 		var str = '';
		 		if(obj==undefined && !isEmpty(item.product)){
		        	str = 'readonly="readonly"';
		 		}else if (obj!=undefined && !isEmpty(item.id)){
		        	str = 'readonly="readonly"';
		 		}
	        	return '<input type="text" name="planApplyItems['+itemIndex+'].description" class="text" value="'+val+'" '+str+' />';
		 	}},
			{ title:'${message("产品名称")}', name:'name' ,align:'center', width:150, renderer: function(val,item,rowIndex,obj){
		 		if((obj==undefined && isEmpty(item.product)) || (obj!=undefined && isEmpty(item.id))){
		 				var product_name_id = isEmpty(item.product_name_id) ? '':item.product_name_id;
		 				var product_name_value = isEmpty(item.product_name_value) ? '':item.product_name_value;
			 			var html = '<span class="search" style="position:relative">'
		        			+'<input type="hidden" name="planApplyItems['+itemIndex+'].productName.id" class="text productName_id" value="'+product_name_id+'"/>'
		        			+'<input type="text" class="text productName_value" maxlength="200" onkeyup="clearSelect(this)" value="'+product_name_value+'" readOnly/>'
		        			+'<input type="button" class="iconSearch" value="" onclick="productDict(this,1)"/>'
		        		+'</span>';
		        	 return html;
		 		}else{
		 			return val;
		 		}
     		}},
     		{ title:'${message("木种/花色")}', name:'wood_type_or_color' ,align:'center', width:150, renderer: function(val,item,rowIndex,obj){
     			if((obj==undefined && isEmpty(item.product)) || (obj!=undefined && isEmpty(item.id))){
	     				var wood_type_or_color_id = isEmpty(item.wood_type_or_color_id) ? '':item.wood_type_or_color_id;
		 				var wood_type_or_color_value = isEmpty(item.wood_type_or_color_value) ? '':item.wood_type_or_color_value;
	     				var html = '<span class="search" style="position:relative">'
		        			+'<input type="hidden" name="planApplyItems['+itemIndex+'].woodTypeOrColor.id" class="text woodTypeOrColor_id" value="'+wood_type_or_color_id+'"/>'
		        			+'<input type="text" class="text woodTypeOrColor_value" maxlength="200" onkeyup="clearSelect(this)" value="'+wood_type_or_color_value+'" readOnly/>'
		        			+'<input type="button" class="iconSearch" value="" onclick="productDict(this,2)"/>'
		        		+'</span>';
		 			 return html;
		 		}else{
		 			return val;
		 		}
     		}},
     		{ title:'${message("型号")}', name:'model' ,align:'center', width:120, renderer: function(val,item,rowIndex,obj){
     			if((obj==undefined && isEmpty(item.product)) || (obj!=undefined && isEmpty(item.id))){
	     				var model_id = isEmpty(item.model_id) ? '':item.model_id;
		 				var model_value = isEmpty(item.model_value) ? '':item.model_value;
	     				var html = '<span class="search" style="position:relative">'
		        			+'<input type="hidden" name="planApplyItems['+itemIndex+'].model.id" class="text model_id" value="'+model_id+'"/>'
		        			+'<input type="text" class="text model_value" maxlength="200" onkeyup="clearSelect(this)" value="'+model_value+'" readOnly/>'
		        			+'<input type="button" class="iconSearch" value="" onclick="productDict(this,3)"/>'
		        		+'</span>';
	    				return html;
		 		}else{
		 			return val;
		 		}
     		}},
     		{ title:'${message("规格")}', name:'spec' ,align:'center', width:120, renderer: function(val,item,rowIndex,obj){
     			if((obj==undefined && isEmpty(item.product)) || (obj!=undefined && isEmpty(item.id))){
     				var spec_id = isEmpty(item.spec_id) ? '':item.spec_id;
	 				var spec_value = isEmpty(item.spec_value) ? '':item.spec_value;
     				var html = '<span class="search" style="position:relative">'
	        			+'<input type="hidden" name="planApplyItems['+itemIndex+'].spec.id" class="text spec_id" value="'+spec_id+'"/>'
	        			+'<input type="text" class="text spec_value" maxlength="200" onkeyup="clearSelect(this)" value="'+spec_value+'" readOnly/>'
	        			+'<input type="button" class="iconSearch" value="" onclick="productDict(this,4)"/>'
	        		+'</span>';
    				return html;
		 		}else{
		 			return val;
		 		}
     		}},
     		{ title:'${message("含水率")}', name:'moisture_content', align:'center', renderer:function(val,item,rowIndex,obj){
				var str = 'selected="selected"';
	   			var html='<select name="planApplyItems['+itemIndex+'].moistureContent.id" class="text moistureContent">';
		   			[#list moistureContentList as moistureContent]
		   				if(${moistureContent.id} == val){
		   					html+='<option value="${moistureContent.id}" '+str+' >${moistureContent.value}</option>';
		   				}else{
		   					html+='<option value="${moistureContent.id}">${moistureContent.value}</option> ';
		   				}
	   				[/#list]
	   				html+='</select>';
	   			return html;
			}},
			{ title:'${message("业务类型")}', name:'business_type', align:'center', renderer:function(val,item,rowIndex,obj){
				var businessTypeName = $(".businessTypeName").val();
				var str = 'selected="selected"';
	   			var html='<select name="planApplyItems['+itemIndex+'].businessType.id" class="text businessType">';
		   			[#list businessTypeList as businessType]
		   				if((obj==undefined && ${businessType.id} == val) || (obj!=undefined && '${businessType.value}' == businessTypeName)){
		   					html+='<option value="${businessType.id}" '+str+' >${businessType.value}</option>';
		   				}else{
		   					html+='<option value="${businessType.id}">${businessType.value}</option> ';
		   				}
	   				[/#list]
	   				html+='</select>';
	   			return html;
			}},
			{ title:'${message("sbu")}', name:'sbu' , align:'center', renderer:function(val,item,rowIndex,obj){
	 			var html='<select id="sbuId" name="planApplyItems['+itemIndex+'].sbu.id" class="text sbu">';
	 			html+='<option value="">请选择</option>';
				if((obj==undefined && isEmpty(item.product)) || (obj!=undefined && isEmpty(item.id))){
					var str = 'selected="selected"';
					[#list sbuList as sbu]
		   				if((obj!=undefined && ${sbu.isDefault?string ("true","false")}) || (obj==undefined && ${sbu.id} == val)){
		   					html+='<option value="${sbu.id}" '+str+' >${sbu.name}</option>';
		   				}else{
		   					html+='<option value="${sbu.id}">${sbu.name}</option> ';
		   				}
	   				[/#list]
		 		}else{
		 			var sbus = $.parseJSON(item['sbus']);
		 			if(!isEmpty(sbus) && sbus.length>0){
		 				for(var i = 0;i < sbus.length;i++){
							var sbu = sbus[i];
							html += '<option value="'+sbu['id']+'" ';
							if(val == sbu['id']){
								html+=' selected ';
							};
							html += '> '+sbu['name']+'</option>';
						};
		 			}
		 		}
				html+='</select>';
				return html;
			}},
			{ title:'${message("要货平方数")}', name:'quantity' , align:'center', renderer:function(val,item,rowIndex,obj){
				var html = '<div class="nums-input ov">'+
		            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
		            	'<input type="text"  class="t quantity"  name="planApplyItems['+itemIndex+'].quantity" value="'+val+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
		        	'</div>';
		        return html;
			}}, 		
			{ title:'${message("要货日期")}', name:'need_date', align:'center',renderer: function(val,item,rowIndex,obj){
				var need_date = '';
				if(obj==undefined){
					if(!isEmpty(item.need_date) && item.need_date.length > 10){
						need_date = item.need_date.substring(0,10);
					}
				}
				return '<input id="planApplyItems['+itemIndex+'].needDate" name="planApplyItems['+itemIndex+'].needDate" class="text needDate" value="'+need_date+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\'});" type="text" btn-fun="clear"/>'
			}},
			{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
				itemIndex++;
				[#if planApply.status == 0]
					return '<a href="javascript:;" class="btn-delete" onclick="deleteProduct(this)">删除</a>';
				[/#if]
			}}
		];
		$mmGrid = $('#table-m1').mmGrid({
			height:'auto',
	        cols: cols,
	        fullWidthRows:true,
	        checkCol: false,
	        items:mapList,
	        autoLoad: true,
	        callback:function(){
	        	editLineQuantity();
	        	editTotal();
	        	lineNo();
	         }
	    });
	    
		//查询客户
		$("#selectStore").click(function(){
			$("#selectStore").bindQueryBtn({
				type:'store',
				title:'${message("查询客户")}',
				bindClick:false,
				url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
				callback:function(rows){
					if(rows.length>0){
						var row = rows[0];
						$(".storeName").val(row.name);
						$(".storeId").val(row.id);
						$("#saleOrgName").text(row.sale_org_name);
						$(".businessTypeName").val(row.business_type_value);
						$mmGrid.removeRow();
					}
				}
			});
		});
	    
		//打开选择产品界面
		$addProduct.click(function(){
			var storeId = $("input.storeId").val();
	        if (isEmpty(storeId)) {
	            $.message_alert("请选择客户");
	            return false;
	        }
			$addProduct.bindQueryBtn({
				type:'product',
				bindClick:false,
				title:'查询产品',
				url:'/product/product/selectProductPage.jhtml?multi=2',
				callback:function(rows){
					if(rows.length>0){
						for (var i = 0; i < rows.length;i++) {
							var row = rows[i];
							row.type = ${planApply.planApplyType};
							$mmGrid.addRow(row,0,1);
	 						setDate();
	 						setSbu();
	 						lineNo();
						}
					}
				}
			});	
		})
		
		//添加产品词汇
		$addProductDict.click(function(){
			var storeId = $("input.storeId").val();
	        if (isEmpty(storeId)) {
	            $.message_alert("请选择客户");
	            return false;
	        }
			var row = {};
			row.type = ${planApply.planApplyType};
			$mmGrid.addRow(row,0,1);
			setDate();
			lineNo();
		})
	});

	function deleteProduct(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid.removeRow(index);
			editTotal();
		})
	}
	
	function lineNo(){
		var line_number = 1;
		$("span.line_no").each(function(){
			$(this).html(line_number++);
		});
	}
	
	function operation(e,type){
		var $form = $("#inputForm");
		var url = '';
		var data = '';
		var str = '';
		if(type == 1){
			//保存
			url = '/b2b/planApply/saveOrUpdate.jhtml';
			data = $("#inputForm").serialize();
			str = '您确定要保存吗？';
		}else if(type == 2){
			//是否确认
			url = '/b2b/planApply/confirm.jhtml';
			data = {id:${planApply.id}};
			str = '是否要确认？';
		}else if(type == 3){
			//作废
			url = '/b2b/planApply/cancel.jhtml';
			data = {id:${planApply.id}};
			str = '您确定要作废吗？';
		}
		if($form.valid()){
			$.message_confirm(str,function() {
				Mask();
				ajaxSubmit(e,{
					url: url,
					data: data,
					method: "post",
					failCallback:function(resultMsg){	
						// 访问地址失败，或发生异常没有正常返回
						messageAlert(resultMsg)
						UnMask();
					},
					callback:function(resultMsg){
						location.reload(true);
					}
				})
			});
		}
	}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("编辑计划提报")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
        <input type="hidden" name="isZeroPlan"  id="isZeroPlan" value="" />
		<input type="hidden" name="id" id="pid" value="${planApply.id}" />
		<input name="planApplyType"  type="hidden"  value="${planApply.planApplyType}"  />
	 	<input type="hidden" name="lineDefaultdate" value="${lineDefaultdate}"/>
		<div class="tabContent">
	    	<table class="input input-edit">
	    		<tr>
	    			<th>${message("要货单号")}:</th>
	    			<td>${planApply.sn}</td>
	     			<th>${message("单据状态")}:</th>
	    			<td>
	    				[#if planApply.status == 0]${message("制作中")}
						[#elseif planApply.status == 1]${message("已确认")}
						[#elseif planApply.status == 2]${message("已作废")}
						[/#if]
	    			</td>
	    			<th>
						<span class="requiredField">*</span>${message("客户")}:
					</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" btn-fun="clear" value="${planApply.store.id}"/>
							<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" value="${planApply.store.name}"  readOnly/>
							<input type="hidden" class="text businessTypeName" value="${planApply.store.businessType.value}" />
							<input type="button" class="iconSearch" value=""  id="selectStore"  />
						</span>
					</td>
					<th>${message("机构")}:</th>
					<td>
						<span id="saleOrgName" >${planApply.saleOrg.name}</span>
					</td>
	    		</tr>
	    		<tr>
	    			<th><span class="requiredField">*</span>${message("要货日期")}:</th>
	    			<td>
	    				<input id="month" name="needDate" class="text" value="${(planApply.needDate?string("yyyy-MM"))!}"   onfocus="WdatePicker({dateFmt: 'yyyy-MM',startDate:'%y-%M'});"  type="text" btn-fun="clear" readonly/>
	    			</td>
	    			<th>${message("要货总数量(㎡)")}:</th>
	    			<td>
	    				<span class="totalQ" id="totalQ">${planApply.totalQuantity}</span>
	    				<input type="hidden" id="totalQuantity" name="totalQuantity" value="${planApply.totalQuantity}"/>
	    			</td>
	    			<th>${message("创建人")}:</th>
	    			<td>${planApply.storeMember.name}</td>
	    			<th>${message("创建时间")}:</th>
	    			<td>${(planApply.createDate?string("yyyy-MM-dd HH:mm:ss"))!}</td>
	    		</tr>
                <tr>
                    <th>
                        ${message("sbu")}:
                    </th>
                    <td>
                        <select id="sbuId" name="sbuId" class="text" onchange="changeAndClearItems(this)">
                            <option value="">--请选择--</option>
                            [#list sbuList as sbu]
                                <option value="${sbu.id}" [#if planApply.sbu.id=sbu.id]selected[/#if]>${sbu.name}</option>
                            [/#list]
                        </select>
                    </td>
                    <th></th>
                    <td></td>
                    <th></th>
                    <td></td>
                    <th></th>
                    <td></td>
                </tr>
	    		<tr>
					<th>${message("备注")}:</th>
					<td colspan="7">
						<textarea class="text remark" id="remark" name="remark" >${planApply.remark}</textarea>
                    </td>
				</tr>
	    	</table>
			<div class="title-style">
				${message("要货明细")}
				<div class="btns">
					<a href="javascript:;" id="addProduct" class="button">选择产品</a>
		    	</div>
		    	[#if productDictRolesCount > 0 ]
		    		<div class="btns">
						<a href="javascript:;" id="addProductDict" class="button">添加产品词汇</a>
			    	</div>
		    	[/#if]
		    </div>
		    <table id="table-m1"></table>
		</div>
		<div class="fixed-top">
			[#if planApply.status ==0]
				<input type="button" class="button sureButton"  onclick="operation(this,1)" value='${message("保存")}'>
				<input type="button" class="button cancleButton" value="${message("作废")}" onclick="operation(this,3)" />
		   		<input type="button" class="button sureButton" value="${message("确认")}" onclick="operation(this,2)" />
		   	[#elseif planApply.status ==1 && !planApply.isPushedToErp]
		   		<input type="button" class="button cancleButton" value="${message("作废")}" onclick="operation(this,3)" />
		   	[/#if]
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>