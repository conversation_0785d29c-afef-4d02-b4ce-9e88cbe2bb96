<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("计划提报-经销商")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/mmGridConfiguration.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/formList.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>	
<script type="text/javascript">
	$().ready(function() {
		//获取按钮控件
		getButtonHtml(0,'${dTemplates.id}');
		//获取筛选html
		parameterList('${userId}','${dTemplates.id}');
		//遍历列表表头
		traverseList('${userId}','${dTemplates.id}','${defaultQuery}','${dTemplates.pdfPath}','${dTemplates.excelPath}');
	});
	//查看
	function edit(id){
		parent.change_tab(0,'edit.jhtml?id='+id);
	}
	//新增
	function add(e){
		//提报周期不再生效
		let isEnabledToAddAndEdit = ${isEnabledToAddOrEdit?string('true','false')};
		if(isEnabledToAddAndEdit==false){
			 $.message_alert("当前时间不在经销商提报时间范围",700,200);
			return;
		}else{
	        parent.change_tab(0,'add.jhtml?planApplyType=${planApplyType}');
	    }
	}
	//审核
	function billPushToErp(e){
		var data =$mmGrid.serializeSelectedIds();
		if(data.length==0){
			$.message_alert("请选择需要同步的要货单号");
			return false;
		}
		var str = "您确定将要同步的单据都是已确认状态？";
		var url = "/b2b/planApply/pushToErp.jhtml?";
		pushToErp(e,data,str,url);
	}
	//零需求提报
    function changeZeroValue(){
        //提报周期不再生效
        let isEnabledToAddAndEdit = ${isEnabledToAddOrEdit?string('true','false')};
        if(isEnabledToAddAndEdit==false){
            $.message_alert("当前时间不在经销商提报时间范围",700,200);
            return;
        }else{
            parent.change_tab(0,'add.jhtml?planApplyType=${planApplyType}&isZeroPlan='+1);
        }

    }
</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<input type="hidden" name="userId" class="userId"  value="${userId}"/>
		<input type="hidden" name="templateId" class="templatesId"  value="${dTemplates.id}"/>
		<input type="hidden" name="pagesize" class="pagesize" value="${pagesize}"/>
		<input type="hidden" name="$memo" class="memo" value="${memo}"/>
		<input type="hidden" name="$planApplyType" class="$planApplyType" value="${planApplyType}"/>
		<div class="bar">
			<div class="buttonWrap"></div>
			<div id="searchDiv">
	        	<div id="search-content" >
	        		<table id="search-table"></table>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>	
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>