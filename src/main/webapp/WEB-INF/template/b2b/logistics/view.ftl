<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看物流信息")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />

<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<style>
	#tab{border="1px solid #d0cdcd";}
	#tab .ssdtop{width: 80px;height: 35px;border: 1px solid #d0cdcd;background-color:#e6e6e6;}
	#tab .content{width: 80px;height: 35px;}
	#tab .conne{text-align:center;}
	#tab .connetop{font-weight:500;}
</style>
<script type="text/javascript">


function check_view(url){
	var w = $(window).width();
	var h = $(window).height();
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
		title:'${message("查看发货信息")}',
		width:w,
		height:h,
		content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
		ok: null,
		cancel: null,
		onOk: function() {
		
		}
	});
	$dialog.find(".dialogContent").css("height",h+"px");
	$dialog.css("top",0+"px").css("max-height",h+"px");
	

}




$().ready(function() {
	var $inputForm = $("#inputForm");
	
	
	
	

	var items=${logisticsDrivers};
		var driver_cols = [
	     		{ title:'${message("司机姓名")}', name:'name', align:'center', width:100},
	    		{ title:'${message("司机号码")}', name:'mobile' ,align:'center', width:100,},
	    		{ title:'${message("车牌号码")}', name:'plate_number', align:'center', width:100},
	    		{ title:'${message("身份证号码")}', name:'id_card_no' ,align:'center', width:100,},
	    		
	];
	
	$driver_mmGrid = $('#table-driver').mmGrid({
		height:'auto',
		cols: driver_cols,
		items:items,
		fullWidthRows:true,
		checkCol: false,
		autoLoad: true,
	 });
	 
	 
	 
	 var items=${logisticsShippings};
		var shipping_cols = [
	     		{ title:'${message("发货单号")}', name:'sn' ,width:120,align:'center',renderer:function(val,item,rowIndex){
					return '<a href="javascript:;" onClick="check_view(\'/b2b/shipping/view.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
				}},
	  	  		{ title:'${message("erp单号")}', name:'erp_sn' ,align:'center', width:100,},
	  	  		{ title:'${message("运费")}', name:'seller_amount', align:'center', width:100},
	    		{ title:'${message("客户")}', name:'store_name', align:'center', width:100},
	    		{ title:'${message("机构")}', name:'sale_org_name' ,align:'center', width:100,},
	    		{ title:'${message("收货人")}', name:'consignee' ,align:'center', width:100,},
	    		{ title:'${message("收货人号码")}', name:'phone' ,align:'center', width:100,},
	    		{ title:'${message("收货地址")}', name:'address' ,align:'center', width:100,},
	    		
	];
	
	$shipping_mmGrid = $('#table-shipping').mmGrid({
		height:'auto',
		cols: shipping_cols,
		items:items,
		fullWidthRows:true,
		checkCol: false,
		autoLoad: true,
	 });
	 
	  var items=${logisticsMoves};
		var move_cols = [
	     		{ title:'${message("移库单号")}', name:'sn' ,width:120,align:'center',renderer:function(val,item,rowIndex){
					return '<a href="javascript:;" onClick="check_view(\'/b2b/move_library/edit.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
				}},
	  	  		{ title:'${message("运费")}', name:'seller_amount', align:'center', width:100},
	    		{ title:'${message("机构")}', name:'sale_org_name' ,align:'center', width:100,},
	    		{ title:'${message("发出仓")}', name:'issue_name' ,align:'center', width:100,},
	    		{ title:'${message("接收仓")}', name:'receive_name' ,align:'center', width:100,},
	    		
	];
	
	$move_mmGrid = $('#table-move').mmGrid({
		height:'auto',
		cols: move_cols,
		items:items,
		fullWidthRows:true,
		checkCol: false,
		autoLoad: true,
	 });
	 
	 
	 
	  var items=${logisticsContainers};
		var container_cols = [
	  	  		{ title:'${message("柜运单号")}', name:'cabinet_waybill_no' ,align:'center', width:100,},
	    		{ title:'${message("封号")}', name:'seal_no', align:'center', width:100},
	    		{ title:'${message("柜号")}', name:'cabinet_no' ,align:'center', width:100,},
	    		
	];
	
	$container_mmGrid = $('#table-container').mmGrid({
		height:'auto',
		cols: container_cols,
		items:items,
		fullWidthRows:true,
		checkCol: false,
		autoLoad: true,
	 });
	 
	 
	 
	   var items=${logisticsTracks};
		var track_cols = [
	  	  		{ title:'${message("操作时间")}', name:'operate_time' ,align:'center', width:100,},
	    		{ title:'${message("操作内容")}', name:'operate_content', align:'center', width:100},
	    		{ title:'${message("操作人")}', name:'operator' ,align:'center', width:100,},
	    		
	];
	
	$container_mmGrid = $('#table-track').mmGrid({
		height:'auto',
		cols: track_cols,
		items:items,
		fullWidthRows:true,
		checkCol: false,
		autoLoad: true,
	 });
	
	

	
	
	
	
});


	
	
	
	
	
	
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看物流")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
	<input type="hidden" name="id" value="${moveLibrary.id}" />
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("物流编号")}:
				</th>
				<td>
				${logistics.planNo}
				</td>
					
				<th><span class="requiredField">*</span>${message("所属部门")}:</th>
            	<td>
            	${logistics.department}
            	</td>
            	
            	<th><span class="requiredField">*</span>${message("状态")}:</th>
            	<td>
            		${logistics.statusName}
            	</td>

				<th><span class="requiredField">*</span>${message("服务商名称")}:</th>
            	<td>
            	${logistics.spName}
            	</td>
			
		
	</tr>
	<tr>
			<th><span class="requiredField">*</span>${message("运输方式")}:</th>
            	<td>
            	${logistics.transportWay}
            	</td>
			<th><span class="requiredField">*</span>${message("异常状态")}:</th>
            	<td>
            	${logistics.abnormalStatus}
            	</td>
			<th>
				${message("预发柜数/车数")}:
			</th>
			<td>
            ${logistics.planContainerNum}
            </td>
			<th>
				${message("总重量")}:
			</th>
			<td>
				${logistics.totalWeight}
			</td>
	</tr>
	<tr>
	  	<th>
				${message("货运公司")}:
			</th>
			<td>
				${logistics.transportCompany}
			</td>
	    	<th>
				${message("运单号")}:
			</th>
			<td>
				${logistics.transportWaybillNo}
			</td>
				<th>
				${message("起运站")}:
			</th>
			<td>
				${logistics.startStation}
			</td>
			<th>
				${message("到达站")}:
			</th>
			<td>
			${logistics.arrivalStation}
			</td>
	</tr>
	<tr>
		<th>
			${message("起运时间")}:
		</th>
		<td>
           ${logistics.statrTransportTime}
        </td>
        <th>
			${message("到达时间")}:
		</th>
		<td>
           ${logistics.arrivalTransportTime}
        </td>
        <th>
			${message("火车号")}:
		</th>
		<td>
           ${logistics.trainNo}
        </td>
        <th>
			${message("所属商家")}:
		</th>
		<td>
           ${logistics.companyName}
        </td>
	</tr>
	<tr>
		<th>
			${message("备注")}:
		</th>
		<td colspan="7">
            ${logistics.remarks}
        </td>
	</tr>
	<tr>
		<th>
			${message("销售备注")}:
		</th>
		<td colspan="7">
            ${logistics.saleRemarks}
        </td>
	</tr>
	<tr>
		<th>
			${message("派单备注")}:
		</th>
		<td colspan="7">
            ${logistics.dispatchRemarks}
        </td>
	</tr>
		</table>
		
	<table class="input input-edit" style="width:100%;margin-top:5px;">
	[#if isMember==0]
	[#if flag==1]
	<div class="title-style">
			${message("发货单信息")}:
	</div>
	<table id="table-shipping"></table>
	[#else]
	
	<div class="title-style">
			${message("移库单信息")}:
	</div>
	<table id="table-move"></table>
	[/#if]
	[/#if]
	<div class="title-style">
			${message("司机信息")}:
	</div>
	<table id="table-driver"></table>
	<div class="title-style">
			${message("货柜信息")}:
	</div>
	<table id="table-container"></table>
	<div class="title-style">
			${message("跟踪信息")}:
	</div>
	<table id="table-track"></table>
	</table>

		</div>
		<div class="fixed-top">
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>