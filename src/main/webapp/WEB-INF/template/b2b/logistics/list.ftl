<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("物流查询")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function add(){
	parent.change_tab(0,'/b2b/move_library/add.jhtml');
}

$().ready(function() {
	
	
	initMultipleSelect();


	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml'
	});
	
	
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productName']").val();
				}
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('产品【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					allName =allName +','+ rows[i].name  
					vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="closePro(this)"></i></div>'
					$(".product").append(vhtml);
				}
				$("input[name='productName']").attr("value",allName)
			}
		}
	});
	
	var status = {'20':'已确认','30':'已派车','40':'服务中','50':'已完成','90':'已取消'};
	var cols = [
		{ title:'${message("物流单号")}', name:'plan_no' ,align:'center',renderer:function(val,item){
				return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/b2b/logistics/view.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'客户信息',align: 'center', cols: [
		     { title:'${message("客户名称")}', name:'name' ,width:60, isLines:true, align:'center'},
		     { title:'${message("erp编码")}', name:'out_trade_no' ,width:60, isLines:true, align:'center'},
		]},
		{ title:'${message("所属部门")}', name:'department' ,align:'center' },
		{ title:'${message("服务商名称")}', name:'sp_name' ,align:'center' },
		{ title:'${message("所属商家")}', name:'company_name' ,align:'center' },
		{ title:'${message("状态")}', name:'status_name' , align:'center'},
		{ title:'${message("异常状态")}', name:'abnormal_status' ,  align:'center'},
		{ title:'${message("预发柜数/车数")}', name:'planContainer_num', align:'center'},
		{ title:'${message("总重量")}', name:'total_weight', align:'center'},
		{ title:'${message("货运公司")}', name:'transport_company', align:'center'},
		{ title:'${message("运单号")}', name:'transport_waybill_no', align:'center'},
		{ title:'${message("起运站")}', name:'start_station', align:'center'},
		{ title:'${message("到达站")}', name:'arrival_station', align:'center'},
		{ title:'${message("起运时间")}', name:'statr_transport_time', align:'center'},
		{ title:'${message("到达时间")}', name:'arrival_transport_time', align:'center'},
		{ title:'${message("火车号")}', name:'train_no', align:'center'},
		{ title:'${message("备注")}', name:'remarks', align:'center'},
		{ title:'${message("销售备注")}', name:'sale_remarks', align:'center'},
		{ title:'${message("派单备注")}', name:'dispatch_remarks', align:'center'},
		
		
		
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/b2b/logistics/list_data.jhtml',
        lineRoot:"order_items",
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	
	
});

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = 'to_condition_export.jhtml';//分页导出统计页面
	var url = '/b2b/move_library/condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
    var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的订单！")}';//提示
	var url = '/b2b/move_library/selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

function change_tab(index,url,flag){
	$("#tab input").removeClass("current").eq(index).addClass("current");
	var $tabContent = $(".tabContent").hide();
	var $iframe = $tabContent.eq(index).show().find("iframe");
	if(flag==undefined){
		$iframe.attr("src",url);
	}else{
		 if($iframe.attr("src")==""){
			$iframe.attr("src",url);
		}
	}
}


</script>
</head>
<body>
	<form id="listForm" action="/b2b/logistics/list.jhtml" method="get">
		<div class="bar">
		
			
		
				

			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
					 	<dt><p>${message("物流单号")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="sn" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
					 	<dt><p>${message("发货单号")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="shippingSn" value =""  btn-fun="clear"/>
						<dd>
					</dl>
					<dl>
					 	<dt><p>${message("订单号")}:</p></dt>
						<dd>
							<input type="text" class="text"  name="orderSn" value =""  btn-fun="clear"/>
						<dd>
					</dl>
						<dl>
						<dt><p>${message("客户")}:</p></dt>
	    				<dd>
	    					<span style="position:relative">
	    					    <input name="storeId" class="text storeId" type="hidden" value="">
								<input class="text storeName" maxlength="200" type="text" name="storeName" value="" onkeyup="clearSelect(this)" readonly/>
								<input type="button" class="iconSearch" value="" id="selectStore">
								[#--<div class="pupTitleName store"></div>--]
							</span>
	    				</dd>
	    			</dl>
	    			<dl>
							<dt><p>${message("起运时间")}:</p></dt>
							<dd class="date-wrap">
								<input id="startTime" name="startFirstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
								<div class="fl">--</div>
								<input id="endTime" name="startLastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
							</dd>
					</dl>
					<dl>
							<dt><p>${message("到达时间")}:</p></dt>
							<dd class="date-wrap">
								<input id="startTime" name="arriveFirstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
								<div class="fl">--</div>
								<input id="endTime" name="arriveLastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
							</dd>
					</dl>
				
        		
			</div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>