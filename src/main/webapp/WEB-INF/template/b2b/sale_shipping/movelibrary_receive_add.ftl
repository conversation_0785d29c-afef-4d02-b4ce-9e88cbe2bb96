<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("移库发接收添加页面")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>
<style type="text/css">
	tr.s-tr, tr.s-tr td {
		height: 10px !important;
	}
	.mefont {
		color: red;
		font-style：italic;
		斜体
		font-weight：bold;
		加粗
		font-size：30px;
		大小
		line-height：30px;
		行高
		font-family：“SimHei”;
		字体
	}
</style>
<script type="text/javascript">

		//处理库位
		function processingLocation(e) {
			//获取当前控件的id
			var str = $(e).siblings("div").attr("id");
			ajaxGetLocation(e,str);
		}

		function productOrganization(){
			var typesystemDictFlag = $("input.typesystemDictFlag").val();
			if(isNull(typesystemDictFlag) != null){
				$("input.productId").each(function(){
					var $this = $(this);
					var productId = $this.val();
					var $tr = $this.closest("tr");
					if(typesystemDictFlag == 0){
						//$tr.find(".productOrganizationName").text($(".organizationName").text());
						$tr.find("input.productOrganizationId").val($("input.organizationId").val());
						[#if linkStock == 1 ] 
							loadAttQuantity($tr);
						[/#if]	
					}else if (typesystemDictFlag == 1){
						[#if linkStock == 1 ] 
							loadAttQuantity($tr);
						[/#if]	
					}
				});	
			}
		}
		
		
		//加载库存
		function loadAttQuantity($tr){
			[#if storageStockQueryRoles ==0] 
				jurisdictionStockQueryRoles($tr);
				return;
			[/#if]
			//产品经营组织
			var productOrganizationId = $tr.find("input.productOrganizationId").val();
			if(isNull(productOrganizationId) != null){
				//仓库
				var warehouseId = $("input.warehouseId").val();
				//产品
				var productId = $tr.find("input.productId").val();
				//等级
				var productGrade = $tr.find(".grade").val();
				//色号
				var colourNumber = $tr.find(".colourNumber").val();
				if(isNull(colourNumber) == null){
					colourNumber = "";
				}
				//含水率
				var moistureContent = $tr.find(".moistureContent").val();
				if(isNull(moistureContent) == null){
					moistureContent = "";
				}
				//批次
				var batchIds = $tr.find("input.batchIds").val();
				if(isNull(batchIds) == null){
					batchIds = "";
				}else{
					batchIds = batchIds.split(';');
				}
				//新旧标识
				var newOldLogo = $tr.find(".newOldLogo").val();
				if(isNull(newOldLogo) == null){
					newOldLogo = "";
				}
				var params='&warehouseId='+warehouseId+'&productId='+productId+'&productGrade='+productGrade
				  		+'&organizationId='+productOrganizationId+'&colourNumber='+colourNumber+'&moistureContent='+moistureContent
				  		+'&batchIds='+batchIds+'&newOldLogos='+newOldLogo;
					params = params.substring(1,params.length);
				$.ajax({
					url:'/stock/stock/findViewStock.jhtml?'+params,
		   			type : "post",
		   			success : function(rows) {
		   				var data= $.parseJSON(rows.content);
		                   if(data.length>0){
		                       for (var i = 0; i < data.length;i++) {
			                        //可用库存箱数
			                       	if(isNull(data[i].totalAttQuantity2) != null) {
			                       		$tr.find(".attQuantity2BoxNum").text(data[i].totalAttQuantity2);		                   
			                       	}else {
			                       		$tr.find(".attQuantity2BoxNum").text(0);		                       		
			                       	}		                     
			                        //可用库存支数
			                       	if(isNull(data[i].totalAttQuantity3) != null) {
			                       		$tr.find(".attQuantity3Num").text(data[i].totalAttQuantity3);
			                       	}else {
			                       		$tr.find(".attQuantity3Num").text(0);		                       		
			                       	}
			                        //可用库存数量
			                       	if(isNull(data[i].totalAttQuantity1) != null) {
			                       		$tr.find(".attQuantity1Num").text(data[i].totalAttQuantity1);
			                       	}else {
			                       		$tr.find(".attQuantity1Num").text(0);		                       		
			                       	}
		                   	}
		               	}else{
		               		$tr.find(".attQuantity2BoxNum").text(0);	
		               		$tr.find(".attQuantity3Num").text(0);
		               		$tr.find(".attQuantity1Num").text(0);
		               	}
		   			}
				})
			}
		}

		function editPrice(t,e){
			extractNumber(t,2,false,e)
		}
		
		//批次列表计算逻辑
	function editQty2(t,e){
		if(extractNumber(t,3,false,e)){
			var $tr = $(t).closest("tr");
			var branchQuantity=$tr.find(".branchQuantityM").val();
			var branchPerBox=$tr.find(".branchPerBoxM").val();
			var perBranch=$tr.find(".perBranchM").val();
			var quantity = $tr.find(".quantityM").val();
			if($(t).attr("kid")=="box"){
				var box=$(t).val();
				branchQuantity=accMul(box,branchPerBox);
				quantity=accMul(branchQuantity,perBranch);
				scattered=(branchQuantity%branchPerBox);
				if(isNaN(branchQuantity)){
	    			branchQuantity = 0;
				}
	    		$tr.find(".branchQuantityM").val(branchQuantity);//支数
	    		$tr.find(".scatteredQuantityStrM").html(scattered);
				$tr.find(".scatteredQuantityM").val(scattered);
				$tr.find(".quantityStrM").html(quantity);
				$tr.find(".quantityM").val(quantity);
			}else if($(t).attr("kid")=="branch"){
				var branch=$(t).val();
				var branchPerBox=$tr.find(".branchPerBoxM").val();  //每箱支数
				quantity=accMul(branch,perBranch);
				var box=parseInt(branch/branchPerBox);
				var scattered=(branch%branchPerBox);
				$tr.find(".boxQuantityM").val(box);
				$tr.find(".scatteredQuantityStrM").html(scattered);
				$tr.find(".scatteredQuantityM").val(scattered);
				$tr.find(".quantityStrM").html(quantity);
				$tr.find(".quantityM").val(quantity);
			}if($(t).attr("kid")=="quantity"){//平方
					var $tr = $(t).closest("tr");
					var branch_quantity=0;
					var box_quantity=0;
					var quantity=$(t).val();
					var perBranch=$tr.find(".perBranchM").val();  //每支单位数
					var branchPerBox=$tr.find(".branchPerBoxM").val();  //每箱支数
					var perBox=$tr.find(".perBoxM").val();
					var type = productDisc(branchPerBox,perBranch,perBox);
					var scattered=0;
					if(perBranch!=0 && branchPerBox!=0){
						 branch_quantity=quantity/perBranch;
						 box_quantity=parseInt(branch_quantity/branchPerBox);
						 scattered=(branch_quantity%branchPerBox).toFixed(6);
					}
					if(type==2){
						box_quantity = modulo(quantity,perBox);
					}
					if(type==3){
						branch_quantity = modulo(quantity,perBranch);
					}
					$tr.find(".boxQuantityM").val(box_quantity);
					$tr.find(".branchQuantityM").val(branch_quantity);
					$tr.find(".scatteredQuantityStrM").html(scattered);
					$tr.find(".scatteredQuantityM").val(scattered);
					$(t).val(quantity);
			}
	    	if(isNaN(quantity)){
	    		quantity = 0;
			}
			$tr.find(".quantityStrM").html(quantity);//数量
	    	$tr.find(".quantityM").val(quantity);
	    			
		}
	}
	
	function countTotal2(t){		
		var $bInput = $("input.quantityM");
	 	$bInput.each(function(){
	         var boxQuantity = 0;
	         var branchQuantity = 0;
	         var $tr = $(this).closest("tr");	
	         var quantity=$(this).val();
	         var branchPerBox=$tr.find(".branchPerBoxM").val();
	         var perBox=$tr.find(".perBoxM").val();
	         var perBranch=$tr.find(".perBranchM").val();
	         var scatteredQuantity=0;//零散支数
	         var perBox = $tr.find(".perBoxM").val();//每箱单位数
	         var type = productDisc(branchPerBox,perBranch,perBox);
	      	if(type==0){
	      	     branchQuantity=accDiv(quantity,perBranch);
	      	     scattered=(branchQuantity%branchPerBox);
				 boxQuantity=accDiv((branchQuantity-scattered),branchPerBox);
				
	         
	         	if(isNaN(branchQuantity)){
	         		branchQuantity = 0;
	     		}
	         	$tr.find(".branchQuantityM").val(branchQuantity);//支数
	         	$tr.find(".branchQuantityStrM").html(branchQuantity);
	         	quantity=accMul(branchQuantity,perBranch);
	         	if(isNaN(quantity)){
	         		quantity = 0;
	     		}
	         	$tr.find(".scatteredQuantityM").val(scattered);//零散支数
	          	$tr.find(".scatteredQuantityStrM").html(scattered);
	          	$tr.find(".boxQuantityM").val(boxQuantity);//箱数
	          	$tr.find(".boxQuantityStrM").html(boxQuantity);
	         }
	      		       
		});
		var $input = $("input.quantityM");
		var totalVolume = 0;
		var totalAmount = 0;
		var totalWeight = 0;
		var totalBoxQuantity = 0;
		var totalBranchQuantity = 0;
		var totalQuantity = 0;
		$input.each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			var quantity = Number($this.val());
			var boxQuantity = Number($tr.find("input.boxQuantityM").val());
			var branchQuantity = Number($tr.find("input.branchQuantityM").val());
			var volume = Number($tr.find("input.volume").val());
			var price = Number($tr.find("input.price").val());
			var weight = Number($tr.find("input.weight").val());
			//行体积=每箱体积（产品体积）* 下单箱数；
			var lineVolume = accMul(boxQuantity,volume);
			//行重量=行重量 * 数量；
			var lineWeight = accMul(quantity,weight);
			if(isNaN(lineVolume)){
				lineVolume = 0;
			}
			totalVolume = totalVolume+lineVolume;
			totalWeight = totalWeight + lineWeight;
			var lineAmount = accMul(quantity,price);
			if(isNaN(lineAmount)){
				lineAmount = 0;
			}
			totalAmount = totalAmount+lineAmount;
			totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	    	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	    	totalQuantity=accAdd(totalQuantity,quantity);
		});

	}
	
		
		function countTotal(t){
			var totalBoxQuantity = 0;
			var totalBranchQuantity = 0;
			var totalVolume = 0;
			var totalWeight = 0;
			var $bInput = $("input.boxQuantity");
			$bInput.each(function(){
		        var quantity = 0;
		        var $tr = $(this).closest("tr");
		        var isEqual = null;
		        var boxQuantity=$(this).val();
		        var branchPerBox=$tr.find(".branchPerBox").val();
		        var perBranch=$tr.find(".perBranch").val();
		        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
		        var perBox = $tr.find(".perBox").val();//
		        var type = productDisc(branchPerBox,perBranch,perBox);
		        if(isEqual==null){
		        	if(type==0){
		            	var branchQuantity=accMul(boxQuantity,branchPerBox);
		            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
		            	if(isNaN(branchQuantity)){
		            		branchQuantity = 0;
		        		}
		            	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
		            	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
		            	$tr.find(".branchQuantity").val(branchQuantity);//支数
		            	$tr.find(".branchQuantityStr").html(branchQuantity);
		            	quantity=accMul(branchQuantity,perBranch);
		            	if(isNaN(quantity)){
		            		quantity = 0;
		        		}
		            	$tr.find(".quantity").val(quantity);//数量
		            	$tr.find(".quantityStr").html(quantity);
		            	var volume=$tr.find(".volume").val();
		            	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
		        		if(isNaN(volumeAmount)){
		        			volumeAmount = 0;
		        		}
		        		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
		        		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
		            }
		          	//辅料计算逻辑	
		        	if(type==2){
		        		var a = accDiv(perBox,10);
		        		quantity = accMul(boxQuantity,a);
		        	}
		          	if(type==1){
		          		quantity = $tr.find(".quantity").val();
		          	}
		          	if(type==3){
		          		var branchQuantity = $tr.find(".branchQuantity").val();
		          		quantity = accMul(branchQuantity,perBranch);
		          	}
		        }else{
		        	if(type==0&&isEqual){
		            	var branchQuantity=accMul(boxQuantity,branchPerBox);
		            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
		            	if(isNaN(branchQuantity)){
		            		branchQuantity = 0;
		        		}
		            	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
		            	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
		            	$tr.find(".branchQuantity").val(branchQuantity);//支数
		            	$tr.find(".branchQuantityStr").html(branchQuantity);
		            	quantity=accMul(branchQuantity,perBranch);
		            	
		            	if(isNaN(quantity)){
		            		quantity = 0;
		        		}
		            	$tr.find(".quantity").val(quantity);//数量
		            	$tr.find(".quantityStr").html(quantity);
		            	var volume=$tr.find(".volume").val();
		            	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
		        		if(isNaN(volumeAmount)){
		        			volumeAmount = 0;
		        		}
		        		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
		        		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
		            }
		          	//辅料计算逻辑	
		        	if(type==2&&isEqual){
		        		var a = accDiv(perBox,10);
		        		quantity = accMul(boxQuantity,a);
		        		$tr.find(".quantity").val(quantity);//数量
		            	$tr.find(".quantityStr").html(quantity);
		        	}
		          	if(type==1&&isEqual){
		          		quantity = $tr.find(".quantity").val();
		          		$tr.find(".quantity").val(quantity);//数量
		            	$tr.find(".quantityStr").html(quantity);
		          	}
		          	if(type==3&&isEqual){
		          		var branchQuantity = $tr.find(".branchQuantity").val();
		          		quantity = accMul(branchQuantity,perBranch);
		          		$tr.find(".quantity").val(quantity);//数量
		            	$tr.find(".quantityStr").html(quantity);
		          	}
		        }
				var volume=$tr.find(".volume").val();
		    	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
				if(isNaN(volumeAmount)){
					volumeAmount = 0;
				}
				totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
				$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
			});
			$("#totalBoxQuantity").text(totalBoxQuantity);
			$("#totalBranchQuantity").text(totalBranchQuantity);
			var $input = $("input.quantity");
			var total = 0;
			var totalQuantity = 0;
			var b = $("#storeBalance").val();
			$input.each(function(){
				var $this = $(this);
				var $tr = $this.closest("tr");
				var $price_box = $tr.find(".price-box");
				var price;
				if($price_box.length==0 || $price_box.prop("checked")==false){
					price = Number($tr.find("input.price").val());
				}else{
					price = Number($tr.find("input.origMemberPrice").val());
				}
				var quantity = $this.val();
				var amount = Number($this.val())*price;
				totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
				if(isNaN(amount)){
					amount = 0;
				}
				total = Number(total)+Number(currency(amount,false));
				$tr.find(".trprice").html(currency(amount,true));//订单行金额
				var weight=$tr.find(".weight").val();
				var weightAmount=Number(accMul($(this).val(),weight)).toFixed(6);
				if(isNaN(weightAmount)){
					weightAmount = 0;
				}
				totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
				$tr.find(".lineWeightAmount").html(weightAmount);//重量
			});
			$("#total").text(currency(total,true));
			$("#totalVolume").text(totalVolume);
			$("#totalWeight").text(totalWeight);
			$("#totalQuantity").text(totalQuantity);
			var storeBalance = $("#storeBalance").val()
			$("#chae").text(currency(storeBalance-total,true));	
		}

		function editQty(t,e){
			if($(t).attr("kid")=="quantity"){//平方
				if(extractNumber(t,6,false,e)){
					var $tr = $(t).closest("tr");
					var branch_quantity=0;
					var box_quantity=0;
					var quantity=$(t).val();
					var perBox = $tr.find(".perBox").val();
					var perBranch=$tr.find(".perBranch").val();  //每支单位数
					var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
					var type = productDisc(branchPerBox,perBranch,perBox);
					var scattered=0;
					if(perBranch!=0 && branchPerBox!=0){
						 branch_quantity=quantity/perBranch;
						 box_quantity=parseInt(branch_quantity/branchPerBox);
						 scattered=(branch_quantity%branchPerBox).toFixed(6);
					}
					if(type==2){
						box_quantity = modulo(quantity,perBox);
					}
					if(type==3){
						branch_quantity = modulo(quantity,perBranch);
					}
					$tr.find(".boxQuantity").val(box_quantity);
					$tr.find(".branchQuantity").val(branch_quantity);
					$tr.find(".scatteredQuantityStr").html(scattered);
					$tr.find(".scatteredQuantity").val(scattered);
					countTotal($tr);
					$(t).val(quantity);
					editLineInfo(t);
				}
			}else{
				if(extractNumber(t,3,false,e)){
					var $tr = $(t).closest("tr");
					var branch_quantity=0;
					var box_quantity=0;
					if($(t).attr("kid")=="box"){//箱
						$tr.find(".scatteredQuantityStr").html(0);
						$tr.find(".scatteredQuantity").val(0);
					}else if($(t).attr("kid")=="branch"){//支
						var quantity=$(t).val();
						var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
						var box=parseInt(quantity/branchPerBox);
						var scattered=quantity%branchPerBox;
						$tr.find(".boxQuantity").val(box);
						$tr.find(".scatteredQuantityStr").html(scattered);
						$tr.find(".scatteredQuantity").val(scattered);
					}else if($(t).attr("kid")=="quantity"){//平方
						var quantity=$(t).val();
						var perBranch=$tr.find(".perBranch").val();  //每支单位数
						var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
						var branch_quantity=quantity/perBranch;
						var box_quantity=parseInt(branch_quantity/branchPerBox);
						var scattered=branch_quantity%branchPerBox;
						$tr.find(".boxQuantity").val(box_quantity);
						$tr.find(".scatteredQuantityStr").html(scattered);
						$tr.find(".scatteredQuantity").val(scattered);
					}
					countTotal($tr);
					editLineInfo(t);
		
				}
			}
		}

		function editLineInfo(t){
			var $tr = $(t).closest("tr");
			var quantity = Number($tr.find(".quantity").val());
			var volume = Number($tr.find("input.volume").val());
			var weight = Number($tr.find("input.weight").val());
			var price = Number($tr.find("input.price").val());
			var lineVolume = accMul(quantity,volume);
			var lineWeight = accMul(quantity,weight);
			var lineAmount = accMul(quantity,price);
			lineVolume = lineVolume.toFixed(6);
			lineVolume = parseFloat(lineVolume);
			lineWeight = lineWeight.toFixed(6);
			lineWeight = parseFloat(lineWeight);
			$tr.find(".volumeSpan").text(lineVolume);
			$tr.find(".weightSpan").text(lineWeight);
			$tr.find(".lineAmountSpan").text(currency(lineAmount,true));
		}
		
		 <!-- 2019-08-05 冯旗 检测备注字数是否超过240字符-->  
		 function listenLength(){
			  var memeo=$(".memo").val();
			  var len = 0;  
			  for (var i=0; i<memeo.length; i++) {   
			     var c = memeo.charCodeAt(i);   
			     //单字节加1   
			     if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
			       len++;   
			     }else {   
			      len+=2;   
			     }   
			  }   
			  if(len>80){
			     $("#isMemo").val("字符长度已超过240!");
			  }else{
			     $("#isMemo").val("");
			  }
		 }
		 
		$().ready(function() {
			var itemIndex = 0;
			var cols = [
				{ title:'${message("行号")}', width:60, align:'center', renderer: function(val,item,rowIndex,obj){
					var lineNo=(rowIndex+1);
					return lineNo+'<input type="hidden" class="lineNo" name="amShippingItems['+itemIndex+'].lineNo" value="'+lineNo+'"/>';
				}},
				{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
					return '<a href="javascript:;" class="btn-delete" onclick="deleteShipping(this)" >删除</a>';
				}},			
				{ title:'${message("产品编码")}', name:'vonder_code' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
					return '<span class="vonderCode">'+val+'</span>';
				}},
				{ title:'${message("产品名称")}', name:'product_name' ,width:100, align:'center'},
				{ title:'${message("产品描述")}', name:'detail_description',width:200, align:'center'},				
				{ title:'${message("经营组织")}', name:'product_organization_name', width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					var productOrganizationName = '';
						if(item.product_organization_name != null && item.product_organization_name !=''){
							productOrganizationName = item.product_organization_name;
						}
						var productOrganizationId = '';
						if(item.product_organization != null && item.product_organization !=''){
							productOrganizationId = item.product_organization;
						}
		 			var html='<span class="productOrganizationName">'+productOrganizationName+'</span>'+
		 				       '<input type="hidden" name="amShippingItems['+itemIndex+'].productOrganization.id" class="productOrganizationId text" value="'+productOrganizationId+'" />';
		    		return html;
				}},
				{ title:'${message("产品级别")}', name:'level_Id' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					var str='selected="selected"';
	    			var html='<select name="amShippingItems['+itemIndex+'].productLevel.id" class="text grade">';
	    				[#list productLevelList as products]
	    				if(${products.id}==item.level_Id){
	    					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
	    				}else{
	    					html+='<option value="${products.id}">${products.value}</option> ';
	    				}
	    				[/#list]
	    				html+='</select>';
	    			return html;
				}},
				{ title:'${message("件数")}',name:'available_receive_box_quantity', width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					if(isNull(val) == null){
						val = 0;
					}	
					var str = '';
					str+='<ul><li><input type="hidden"  name="amShippingItems['+itemIndex+'].movelibraryReceive.id" value="'+item.move_library+'">'+
						'<input type="hidden"  name="amShippingItems['+itemIndex+'].product.id" class="productId" value="'+item.product_id+'">'+
						'<input type="hidden"  name="amShippingItems['+itemIndex+'].moveLibraryItemReceive.id" value="'+item.id+'">'+
						'<div class="nums-input ov">'+
			            	'<input type="text" kid="box" class="t boxQuantity" name="amShippingItems['+itemIndex+'].boxQuantity" value="'+val+'" minData="0" maxData="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
			        	'</div></li><li><span class="attQuantity2BoxNum" >0</span></li></ul>';
					return str;	
				}},
				{ title:'${message("支数")}',name:'available_receive_branch_quantity',align:'center', width:80, renderer: function(val,item,rowIndex, obj){
					if(isNull(val) == null){
						val = 0;
					}	
					var branchPerBox = item.branch_per_box == null ? 0:item.branch_per_box;
					var perBox = item.per_box == null ? 0:item.per_box;
					var perBranch = item.per_branch == null ? 0:item.per_branch;
					var html='<input type=hidden class="branchPerBox" name="amShippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
						'<input type=hidden class="perBox"  value="'+perBox+'" />'+
						'<input type=hidden class="perBranch" name="amShippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
						'<ul><li><div class="nums-input ov">'+
		            	'<input type="text"  kid="branch" class="t branchQuantity"  name="amShippingItems['+itemIndex+'].branchQuantity" value="'+val+'" minData="0" maxData="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		        	'</div></li><li><span class="attQuantity3Num">0</span></li></ul>';
					return html;
				}},
				{ title:'${message("零散支数")}', name:'available_receive_scattered_quantity' ,align:'center', width:60, renderer: function(val,item,rowIndex, obj){
					if(isNull(val) == null){
						val = 0;
					}	
					var html='<span class="scatteredQuantityStr">'+val+'</span>'+
						'<input type="hidden" name="amShippingItems['+itemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+val+'" />';
					return html;
					
				}},
				{ title:'${message("数量")}', name:'available_receive_quantity', align:'center', width:80, renderer:function(val,item,rowIndex,obj){
					if(isNull(val) == null){
						val = 0;
					}	
					var text = '<ul><li><div class="lh20">'+
						'<div class="nums-input ov square">'+
				        	'<input type="text" kid="quantity" class="t quantity"  name="amShippingItems['+itemIndex+'].quantity" value="'+val+'" minData="0" maxData="'+val+'" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
				        	'<input type="hidden" class="minPriceApplyQuantity" />'+
							'<input type="hidden" class="maxPriceApplyQuantity" />'+
				    	'</div></div></li><li><span class="attQuantity1Num">0</span></li>';
				    	return text;
				}},
				[#if hiddenBatch !=0]     			
						{ title:'${message("色号")}', align:'center',width:60,name:'colour_number',renderer: function(val,item,rowIndex, obj){
							var str='selected="selected"';
				   			var html='<select name="amShippingItems['+itemIndex+'].colorNumbers.id" class="text colourNumber">';
				   			html+='<option value="">请选择</option> ';  	
				   				[#list colorNumberList as colorNumbers]    
					   				if(${colorNumbers.id} == item.colour_number_id){
					   					html+='<option value="${colorNumbers.id}" '+str+' >${colorNumbers.value}</option>';
					   				}else{
					   					html+='<option value="${colorNumbers.id}">${colorNumbers.value}</option> ';
					   				}  
				   				[/#list]
				   				html+='</select>';
				   			return html;
				        }},    
				        { title:'${message("含水率")}', align:'center',width:60,name:'moisture_content',renderer: function(val,item,rowIndex, obj){
				        	var str='selected="selected"';
				   			var html='<select name="amShippingItems['+itemIndex+'].moistureContents.id" class="text moistureContent">';
				   			html+='<option value="">请选择</option> ';  	
				   				[#list moistureContentList as moistureContents]    		   				
					   				if(${moistureContents.id} == item.moisture_content_id){
					   					html+='<option value="${moistureContents.id}" '+str+' >${moistureContents.value}</option> ';
					   				}else{
					   					html+='<option value="${moistureContents.id}">${moistureContents.value}</option> ';
					   				}    		   				
				   				[/#list]
				   				html+='</select>';
				   			return html;
				        }},
				        { title:'${message("批次")}', align:'center', width:200, renderer: function(val,item,rowIndex, obj){
	    					var batch = '';
	    					if(isNull(item.batch) != null){
	    						batch = item.batch;
	    					}
	    					var batch_encoding = '';
	    					if(isNull(item.batch_encoding) != null){
	    						batch_encoding = item.batch_encoding;
	    					}
	    		        	 var html = '<span class="search" style="position:relative">'
	    		        		    +'<input type="hidden" name="amShippingItems['+itemIndex+'].batch" class="text batchIds" value="'+batch+'" />'
	    		        		    +'<input type="text" name="amShippingItems['+itemIndex+'].batchEncoding" class="text batchEncodings" value="'+batch_encoding+'" readonly/>'
	    							+'<input type="button" class="iconSearch" value="" id="selectBatch"/>'
	    						+'</span>';
	    					 return html;
	    		        }},
	    		[/#if]
	    		{ title:'${message("库位")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		        	 var html = '<span class="search" style="position:relative">'
		        			+'<input type="hidden" name="amShippingItems['+itemIndex+'].warehouseLocation.id" class="text warehouseLocationId" value="" />'
		        			+'<input type="text"  class="text warehouseLocationCode" value="" oninput = "processingLocation(this)"/>'
		        			+'<input type="button" class="iconSearch" value="" id="selectStorehouse"/>'
		        		+'<div class="textDis" id="lineLocation"><ul class="lineLocationData"></ul></div></span>';
		        	 return html;
		        }},
	    		{ title:'${message("新旧标识")}', align:'center',width:60,name:'newOldLogo',renderer: function(val,item,rowIndex, obj){
			        var str='selected="selected"';
			   		var html='<select name="amShippingItems['+itemIndex+'].newOldLogos.id" class="text newOldLogo">';
			   		[#list newOldLogosList as newOldLogos]    		   				
				   		if(${newOldLogos.id} == item.new_old_logos_id){
		   					html+='<option value="${newOldLogos.id}" '+str+' >${newOldLogos.value}</option> ';
		   				}else{
		   					html+='<option value="${newOldLogos.id}">${newOldLogos.value}</option> ';
		   				}		   				
			   		[/#list]
			   		html+='</select>';
			   		return html;
			      }},
		        { title:'${message("备注")}',name:'move_library_item_remarks',width:100,align:'center',renderer:function(val,item,rowIndex,obj){
					var memo = '';
					if(isNull(val) != null){
						memo = val;
					}
					var html= '<input type="text" name="amShippingItems['+itemIndex+'].memo" class="text" value="'+memo+'" />';
					return html;
				}},
				{ title:'${message("产品型号")}', name:'product_model' ,width:100, align:'center'},
		        { title:'${message("单位")}',width:80, align:'center',name:'product_unit'},
				{ title:'${message("体积")}',name:'product_volume' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					var volume = (val!="")?val:0;
					var lineVolume = volume*item.available_receive_quantity;
					lineVolume = lineVolume.toFixed(6);
					lineVolume = parseFloat(lineVolume);
					var html = '<span class="volumeSpan">'+lineVolume+'</span><input type="hidden" name="amShippingItems['+itemIndex+'].volume" class="text volume" value="'+volume+'" />';
					return html;
				}},
				{ title:'${message("重量")}',name:'product_weight' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					var weight = (val!="")?val:0;
					var lineWeight = weight*item.available_receive_quantity;
					lineWeight = lineWeight.toFixed(6);
					lineWeight = parseFloat(lineWeight);
					var html = '<span class="weightSpan">'+lineWeight+'</span><input type="hidden" name="amShippingItems['+itemIndex+'].weight" class="text weight" value="'+weight+'" />';
					return html;
				}},
				{ title:'${message("来源单据号")}', name:'move_library_sn' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
					itemIndex++;
			  		return val;
				}},
			];
	
		    var param = jQuery.param( $("#dataForm").serializeObject(), true);
		    ajaxSubmit('',{
				method:'post',
				url:'movelibraryItem_list_data.jhtml',
				data: param,
				callback: function(resultMsg) {
					var data = $.parseJSON(resultMsg.content);
				    $movelibraryIssue_mmGrid = $('#table-movelibraryIssue').mmGrid({
						height:'auto',
						fullWidthRows:true,
						autoLoad:true,
						useCountText:true,
						checkByClickTd:true,
						rowCursorPointer:true,
						checkCol:true,
				        cols: cols,
				        items: data,
				        callback:function(){
				        	countTotal();
				        	productOrganization();				        	
				        }
				    });
				}
			});
			


			//选择库位点击事件
			$(".textDis li").live("click",function(){
				assignmentLi(this);
			});
		    
		    //点击复制
		    $("#copyData").click(function(){
		   	 //获取选择信息
		      var selectData = $movelibraryIssue_mmGrid.selectedRows();
		      var idData =$movelibraryIssue_mmGrid.serializeSelectedIds();
			  if(idData.length==0){
				  $.message_alert("请先选择产品或生产单！");
			      return false;
			  }else {
				//列表里复制一条数据
				  if(selectData.length>0){
		              for (var i = 0; i < selectData.length;i++) {
							var data = selectData[i];
							data.line_no++;
							$movelibraryIssue_mmGrid.addRow(data,null,1);
						}	
		              countTotal();
		              productOrganization();
				  }
			  }
		    });
			
		    //查询仓库
		    $("#selectWarehouse").click(function(){
		          var $saleOrgId = $(".saleOrgId").val();
		          var sbuId = $(".sbuId").val();
		          if($saleOrgId==""){
		              $.message_alert('请选择机构');
		          }else{
		              $("#selectWarehouse").bindQueryBtn({
		                  type:'warehouse',
		                  bindClick:false,
		                  title:'${message("查询仓库")}',
		                  url:'/stock/warehouse/select_warehouse.jhtml?saleOrgId='+$saleOrgId+'&sbuId='+sbuId,
		                  callback:function(rows){
		                      if(rows.length>0){
		                          var row = rows[0];
		                          $(".warehouseId").val(row.id);
		                          $(".warehouseName").val(row.name);
		                          $(".typesystemDictFlag").val(row.type_system_dict_flag);
		                          $("input[name='organizationId']").val(row.management_organization_id);
		                          $(".organizationName").text(row.management_organization_name);
		                          productOrganization();
		                      }
		                  }
		              });
		          }
		      })
		      
		      
		[#if warehouse.enableBatch]
        var wBItem_line_no = 1;	
		var wBItemIndex = 0;
		var vWarehouseBatchItemList = null;
		[#if vWarehouseBatchItemList?? && (vWarehouseBatchItemList?size > 0)]
			vWarehouseBatchItemList = ${vWarehouseBatchItemList};
		[/#if]
		//批次列表
		var bach_cols = [	
					{ title:'${message("序号")}', align:'center',width:60 ,renderer: function(val,item,rowIndex){
						html = rowIndex+1+'<input type="hidden" name="batchItem_lineNo"  value="'+rowIndex+1+'" />';
						return html;
					}},
		    		{ title:'${message("生产完成日期")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		        	    var completion_date = item.completion_date;
		        	    if(isNull(completion_date) == null){
		        	    	completion_date = '';
		        	    }
		        		var html = '<span class="completionDate" >'+completion_date+'</span>';
		        		return html;
		         	}},
		    		{ title:'${message("入库日期")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			        	   var documentDate = item.document_date;		
			        	   var dd = "'yyyy-MM-dd'";
			        	   if(isNull(documentDate) == null){
			        		   return '<input type="text" class="text" name="warehouseBatchItems['+wBItemIndex+'].documentDate"  onfocus="WdatePicker({dateFmt:'+dd+'});"  btn-fun="clear" value="${amShipping.billDate}"  />';		       		  
			        	   } else {
			        		   return '<input type="text" class="text" name="warehouseBatchItems['+wBItemIndex+'].documentDate" onfocus="WdatePicker({dateFmt:'+dd+'});"  btn-fun="clear" value="'+documentDate+'"/>';
			        	   }		        		   		        	 
			          }},
		    		{ title:'${message("批次编码")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		    			var amShippingItemId = item.id;
		    			if(obj==undefined){
		    				amShippingItemId = item.am_shipping_item;
		    			}
		    			if(isNull(item.warehouse_batch) != null){
				        	return '<span class="search" style="position:relative">'
				        			+'<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].warehouseBatch.id" class="text batchId batchId_'+amShippingItemId+'_'+item.batch_id+'" value="'+item.warehouse_batch+'" />'
				        			+'<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].product.id" class="text productId" value="'+item.product+'" />'
				        			+'<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].outBatchId" class="text outBatchId" value="'+item.id+'" />'
				        			+item.batch_encoding+'</span>'
			        	}else {				        		
			        		var html = '<span class="search" style="position:relative">'
		        			+'<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].warehouseBatch.id" class="text batchId" value="" />'
		        			+'<input type="hidden" name="organizationId" class="text organizationId" value="'+item.product_organization_id+'" />'
                            +'<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].outBatchId" class="text outBatchId" value="'+item.id+'" />'
		        			+'<input type="text"  class="text batchEncoding" value="" readonly/>'
		        			+'<input type="button" class="iconSearch" value="" id="selectWarehouseBatch"/>'
		        			+'</span>';
			        		return html;
			        	}
			        }},
		    		{ title:'${message("库位")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			        	var warehouseLocationId = item.warehouseLocationId;
			        	if(isNull(warehouseLocationId) == null) {
			        		warehouseLocationId = "";
			        	}
			        	var warehouseLocationCode = item.warehouseLocationCode;
			        	if(isNull(warehouseLocationId) == null) {
			        		warehouseLocationCode = "";
			        	}
			        	if(isNull(warehouseLocationId) != null){
			        	var html = '<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].warehouseLocation.id" class="text warehouseLocationId" value="'+warehouseLocationId+'" />'
			        			+'<span class="search" style="position:relative">'+warehouseLocationCode+'</span>';
			        		return html
			        	}else {
			        		var html = '<span class="search" style="position:relative">'
    			        			+'<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].warehouseLocation.id" class="text warehouseLocationId" value="" />'
    			        			+'<input type="text"  class="text warehouseLocationCode" value="" oninput = "processingLocation(this)"/>'
    			        			+'<input type="button" class="iconSearch" value="" id="selectStorehouse"/>'
    			        		+'<div class="textDis" id="batchLocation"><ul class="batchLocationData"></ul></div></span>';
    			        	 return html;
			        	}
			        	 
			        }},
			        { title:'${message("作业单号")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			        	    var warehouse_batch_sn = item.warehouse_batch_sn;
			        	    if(isNull(warehouse_batch_sn) == null){
			        	    	warehouse_batch_sn = '';
			        	    }
			        		var html = '<span class="warehouseBatchSn" >'+warehouse_batch_sn+'</span>';
			        		return html;
			        }},
			        { title:'${message("经营组织")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		        	    var organization_name = item.organization_name;
		        	    if(isNull(organization_name) == null){
		        	    	organization_name = '';
		        	    }
		        		var html = '<span class="organizationName" >'+organization_name+'</span>';
		        		return html;
		        	}},
		        	{ title:'${message("件数")}', name:'box_quantity',width:80, align:'center',renderer: function(val,item,rowIndex,obj){
			   			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
			   			var max_box=parseInt((item.oi_branch_quantity-item.ship_branch_quantity+item.branch_quantity-item.closed_quantity)/item.branch_per_box);
			   			if(isNaN(max_box)){
			   				max_box = val;
			   			}
			   			var vals = 0;
			   			if(isNull(val)!=null) {
			   				vals = val;
			   			}
			   			
			   			
			   			if(type==1||type==3){
			   				var html = '-'+
			               	'<input type="hidden" kid="box" class="t boxQuantityM"   value="'+vals+'"   >';
			           	return html;
			   			}else{
			   				var html = '<div class="nums-input ov">'+
			   						'<input type="text" kid="box" class="t boxQuantityM"  name="warehouseBatchItems['+wBItemIndex+'].shippedBoxQuantity" value="'+vals+'" minData="0" oninput="editQty2(this,event)" onpropertychange="editQty2(this,event)" >'+
			               	'</div>';
			           	return html;
			   			}   			
		   			}},
			   		{ title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
			   			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
			   			var branchQuantity='';
			   			if(obj==undefined){
			   				branchQuantity = val;
			   			}
			   			var branchPerBox = item.branch_per_box==null?0:item.branch_per_box;
			   			var perBranch = item.per_branch==null?0:item.per_branch;
			   			if(type==1||type==2){
			   				branchQuantity=0;
			   				var html = '-'+
			           		'<input type=hidden class="t branchPerBoxM" name="warehouseBatchItems['+wBItemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
			   				'<input type=hidden class="t perBoxM" value="'+item.per_box+'" />'+
			   				'<input type=hidden class="t perBranchM" name="warehouseBatchItems['+wBItemIndex+'].perBranch" value="'+perBranch+'" />';
			   			return html;
			   			}
			   		
			   			var html = '<div class="nums-input ov">'+
			           		'<input type="text" kid="branch" class="t branchQuantityM"  name="warehouseBatchItems['+wBItemIndex+'].shippedBranchQuantity" value="'+branchQuantity+'" minData="0" maxData="'+(item.oi_branch_quantity-item.ship_branch_quantity+item.branch_quantity-item.closed_quantity)+'" oninput="editQty2(this,event)" onpropertychange="editQty2(this,event)" >'+
			           		'<input type=hidden class="t branchPerBoxM" name="warehouseBatchItems['+wBItemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
			   				'<input type=hidden class="t perBoxM"  value="'+item.per_box+'" />'+
			   				'<input type=hidden class="t 	perBranchM" name="warehouseBatchItems['+wBItemIndex+'].perBranch" value="'+perBranch+'" />'+
			       		'</div>';
			   			return html;
			   		}},
			   		{title:'${message("零散支数")}', name:'branch_scattered' ,align:'center', width:60, renderer: function(val,item,rowIndex, obj){
			   			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
			   			var branchPerBox=item.branch_per_box;
			   			var scatteredQuantity=item.branch_quantity%item.branch_per_box;
			   			if(isNaN(scatteredQuantity)){
			   				scatteredQuantity = 0;
			   			}
			   			if(type==1||type==2||type==3){
			   				return "-";
			   			}
			   			if(isNull(item.shipped_branch_scattered)!=null) {
			   				scatteredQuantity = item.shipped_branch_scattered;
			   			}
			   			var html='<span class="scatteredQuantityStrM">'+scatteredQuantity+'</span>'+
			   				'<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].shippedBranchScattered" class="scatteredQuantityM text" value="'+scatteredQuantity+'" />';
			   			return html;
			   		}},		        			
					{ title:'${message("数量")}', align:'center',name:'quantity',width:80,renderer: function(val,item,rowIndex, obj){	
						var quantity='';
						
						if(obj==undefined){
							quantity = val;
						}else {
							quantity = 0;
						}
						if (val == '' || val == null ) {
							quantity = 0;
						}
							var lostQuantity=item.lost_quantity;
							if(isNull(lostQuantity) == null){
								lostQuantity = 0;
							}	
						
						quantity = quantity.toFixed(6);
						quantity = parseFloat(quantity);
						if(isNull(item.quantity)!=null) {
							quantity = item.quantity;
						}
						if((item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0) && item.length!='' && item.length==0 ){
							quantity=item.quantity;
							var text = 
							'<div class="nums-input ov">'+
					        	'<input type="text"  kid="quantity"  class="t quantityM" name="warehouseBatchItems['+wBItemIndex+'].quantity" value="'+lostQuantity+'" maxData="'+lostQuantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
					    	'</div>';
					    	return text;
						}
						var html='<span class="quantityStrM">'+lostQuantity+'</span>'+
							'<input type="hidden" name="warehouseBatchItems['+wBItemIndex+'].quantity" class="t quantityM" kid="quantity"  value="'+lostQuantity+'" />';
						return html;
			      }},
			      { title:'${message("色号")}', align:'center',width:80,renderer: function(val,item,rowIndex, obj){				        	
			   			var html='<select name="warehouseBatchItems['+wBItemIndex+'].colorNumbers.id" class="text wColorNumbers">';
			   				if(isNull(item.color_numbers) != null) {
			   					html+='<option value="'+item.color_numbers+'" selected="selected" >'+item.color_numbers_name+'</option>';
			   				}else{
			   					html+='<option value="">请选择</option> ';
			   					[#list colorNumberList as colorNumbers]
				   					html+='<option value="${colorNumbers.id}">${colorNumbers.value}</option> ';
				   				[/#list]
			   				}
			   				html+='</select>';
			   			return html;				        								
		        	}},
		        	{ title:'${message("含水率")}', align:'center',width:80,renderer: function(val,item,rowIndex, obj){
				   			var html='<select name="warehouseBatchItems['+wBItemIndex+'].moistureContents.id" class="text wMoistureContents">';
				   			if(isNull(item.moisture_contents) != null) {
				   				var name = "";
				   				if(isNull(item.moisture_contents_name)!=null) {
				   					name = item.moisture_contents_name;
				   				}else {
				   					name = item.moisture_content_name;
				   				}
				   				html+='<option value="'+item.moisture_contents+'" selected="selected" >'+name+'</option>';
				   			}else{
				   				html+='<option value="">请选择</option> ';
				   				[#list moistureContentList as moistureContents]
					   					html+='<option value="${moistureContents.id}">${moistureContents.value}</option> ';
				   				[/#list]
				   			}
			   				html+='</select>';
			   			return html;	
		        	}},
		        	{ title:'${message("新旧标识")}', align:'center',width:80,renderer: function(val,item,rowIndex, obj){	
				   			var html='<select name="warehouseBatchItems['+wBItemIndex+'].newOldLogos.id" class="text wNewOldLogos">';
				   			if(isNull(item.new_old_logos) != null) {
				   				html+='<option value="'+item.new_old_logos+'" selected="selected" >'+item.new_old_logos_name+'</option>';
				   			}else{
				   				html+='<option value="">请选择</option> ';
				   				[#list newOldLogosList as newOldLogos]
				   					html+='<option value="${newOldLogos.id}">${newOldLogos.value}</option> ';
				   				[/#list]
				   			}
				   			html+='</select>';
			   			return html;				        					        	
		         	}},
		         	{ title:'${message("备注")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		        	    var memo = item.memo;
		        	    if(isNull(memo) == null){
		        	    	memo = '';
		        	    }
		        		var html = '<span class="memo" >'+memo+'</span>';
		        		return html;
		         	}},
		    	 	{ title:'操作', name:'', align:'center',renderer: function(val,item,rowIndex){
		    		   wBItem_line_no++;
		    		   wBItemIndex++;
		    		   return '<a href="javascript:;" class="btn-delete deleteBacth" >删除</a>'
		    	  	}}
			   ];
		
		$mmGridBacth = $('#table-bacth').mmGrid({
			height:'auto',
	        cols: bach_cols,
	        items:vWarehouseBatchItemList,
	        fullWidthRows:true,
	        checkCol: false,
	        autoLoad: true,
	        callback:function(){
	       	 countTotal2();
			}
	    });
	[/#if]
		    
		         	//删除批次
        $("a.deleteBacth").live("click", function() {
             var index = $(this).closest("tr").index();
             $.message_confirm('您确定要删除吗？',function(){
            	 $mmGridBacth.removeRow(index);
//                 var line_number = 1;
//                 $("span.line_no").each(function(){
//                     $(this).html(line_number++);
//                 });
//                 wBItem_line_no--;
             })
        });
    	
		     [#if linkStock == 1 ] 
				 //产品等级
				 $(".grade").live("change", function() {
					 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr); 
				 })
				 //色号
				 $(".colourNumber").live("change", function() {
					 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr); 
				 })
				 //含水率
				 $(".moistureContent").live("change", function() {
					 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr); 
				 })
				 //新旧标识
				 $(".newOldLogo").live("change", function() {
					 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr); 
				 })
			[/#if]
			
					
			//选择库位
			$("#selectStorehouse").live("click",function(){          	
				getLocationInformation(this)
			});		
					
			//批次
            $("#selectBatch").live("click",function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
                var url = '/stock/batch/edit_post.jhtml';
                //经营组织
                var organizationId = $tr.find(".productOrganizationId").val();
                if(isNull(organizationId) == null){
        			$.message_alert("单据行项的经营组织不能为空");
        			return false;
        	  	}
                url+='?organizationId='+organizationId;
               	//仓库工厂
                var productionPlantId = $("input.productionPlantId").val();
               	if(isNull(productionPlantId) != null){
               		url+='&productionPlantId='+productionPlantId;
               	}
                //批次
                var batchIds = $tr.find(".batchIds").val();
                if(isNull(batchIds) != null){
                	url+='&bacthIds='+batchIds;
                }
        		var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
        		var $dialog = $.dialog({
       				title:'查询批次',
       				width:1200,
       				height:508,
       				content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
       				onOk: function() {
       					var rows = $("#"+iframeId)[0].contentWindow.childMethod();
       					var allId = '';
       					var allName = '';
       					for(var i=0;i<rows.length;i++){
       						var row = rows[i];
       						if(isNull(row) != null){
       							if(isNull(row.id) != null){
       								if(isNull(allId) != null){
           								allId =allId +';'+ row.id; 
           								allName = allName +';'+ row.batch_encoding;
           							}else{
           								allId = row.id; 
           								allName = row.batch_encoding;
           							}
       							}
       						}
       					}   
       					$tr.find(".batchIds").val(allId);
       					$tr.find(".batchEncodings").val(allName);
       					[#if linkStock == 1 ] 
		   			 		loadAttQuantity($tr); 
		   			 	[/#if]
       				}
       			});
            })
		});	
		
		
		
		
		function select_post(e,organizationId,productionPlantId,callback){
			$(e).bindQueryBtn({
				bindClick:false,
				type:'bacth',
				title:'选择批次',
				url:'/stock/batch/select_bacth.jhtml?organizationId='+organizationId+'&productionPlantId='+productionPlantId,
				callback:function(rows){
					if(rows.length>0){
						if(callback(rows)==false){
							return false;
						}
					}
				}
			});
		}
		
		//点击删除操作
		function deleteShipping(e){
			var index = $(e).closest("tr").index();
			$.message_confirm('您确定要删除吗？',function(){
				$movelibraryIssue_mmGrid.removeRow(index);
				countTotal();
			
			})
		}
		
		function sub(e){
			 var data = $("#inputForm").serializeObject();
			 var result = false;
		   	 //判断备注长度
			 var memeo=$(".memo").val();
		     var len = 0;  
		   	 for (var i=0; i<memeo.length; i++) {   
		   	  	 var c = memeo.charCodeAt(i);   
		   	 	 //单字节加1   
		    	 if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
		    	   	len++;   
		    	 }else {   
		    	  	len+=2;   
		   	 	 }   
		 	 }   
		  	 var url = "movelibrary_issue_save.jhtml";
            //获取menuId
            var menuId = $("#menuId").val();
            //当前单
            var orderIndex = $("#orderIndex").val();
		  	 var urlPath = "/b2b/sale_shipping_nature/movelibrary_receive_view.jhtml?menuId="+menuId+"&orderIndex="+orderIndex+"&id=";
		  	 //校验色号含水率
		  	 judgmentValue2(url,urlPath);
		}

        //查看PDF
        function check_pdf(e,id){
            ajaxSubmit(e,{
                url:"/b2b/move_library/checkPdf.jhtml",
                method:"post",
                data:{ids:id},
                async: false,
                callback:function(resultMsg){
                    window.open(resultMsg.content);
                }
            });
        }
</script>
</head>
<body>
	<div class="pathh">&nbsp;新增入库单</div>
	<form id="dataForm">
		[#list ids as id] 
			<input type="hidden" name="ids" value="${id}"> 
		[/#list] 
		<input type="hidden" name="type" value="1"> 
	</form>
	<form id="inputForm">
		<input type="hidden" name="flag" value="0">
			<div class="tabContent">
				<table class="input input-edit">
					<tr>
						<th>${message("单据号")}:</th>
						<td></td>
                        <th>
                            ${message("来源单号:")}
                        </th>
                        <td>
                            <span id="value">${sourceNo}</span>
                            <a href="javascript:void(0);" onclick="check_pdf(this,${sourceNoId})" title="查看PDF" style="float: right;"><img style="width: 20px;" src="/resources/images/ic_pdf.png"></a>&nbsp;
                        </td>
						<th>
							<span class="requiredField">*</span>
							${message("客户")}:
						</th>
						<td></td>
						
						<th>${message("单据状态")}:</th>
						<td></td>
					</tr>
					<tr>
                        <th><span class="requiredField">*</span>${message("仓库")}:</th>
                        <td>
		            		<span class="search" style="position:relative">
								<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear" value="${warehouse.id}"/>
								<input type="text" name="warehouseName" class="text warehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${warehouse.name}" readOnly/>
								<input type="hidden" class="typesystemDictFlag" value="${warehouse.typeSystemDict.flag}" />
								<input type="hidden" class="productionPlantId" value="${warehouse.productionPlant.id}" />
								<input type="hidden" class="iconSearch" value="" id="selectWarehouse">
							</span>
                        </td>
						<th>${message("机构")}:</th>
						<td>
							<input name="saleOrgId" class="text saleOrgId" type="hidden" value="${saleOrg.id}" />
							<span>${saleOrg.name}</span>
						</td>
						
						<th>经营组织:</th>
						<td>
							<input type="hidden" name="organizationId" class="organizationId" value="${warehouse.managementOrganization.id}">
							<span class="organizationName">${warehouse.managementOrganization.name}</span>
						</td>
						
						<th>${message("创建时间")}:</th>
						<td></td>
					</tr>
					<tr>
                        <th>${message("Sbu")}:</th>
                        <td>
                            <input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbu.id}" />
                            <span id="sbuName">${sbu.name}</span>
                        </td>
		                <th>${message("单据类型")}:</th>
            			<td>
            				<input type="hidden" name="billTypeId" class="text billTypeId"id="billTypeId" btn-fun="clear" value="${billType.id}" />
            				<span id="value">${billType.value}</span>
            			</td>
            			
            			<th>${message("单据类别")}:</th>
            			<td>
            				<input type="hidden" name="billCategoryId" class="text billCategoryId" id="billCategoryId" btn-fun="clear" value="${billCategory.id}" />
            				<span id="value"> ${billCategory.value} </span>
            			</td>
            			
            			<th>${message("审核人")}:</th>
            			<td></td>
					</tr>
					<tr>
                        <th>
                            ${message("GL日期")}:
                        </th>
                        <td>
                            <input type="text" class="text" name="billDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${nowDate}"  />
                        </td>
						<th>
							${message("车号")}:
						</th>
						<td>
							<input type="text" name="wagonNumber" class="text" value="" btn-fun="clear" maxlength="200" />
						</td>
						
						<th>
							${message("柜号")}:
						</th>
						<td>
							<input type="text" name="containerNumber" class="text " maxlength="200" btn-fun="clear" />
						</td>

					</tr>
					<tr>
						<th>备注:</th>
						<td colspan="7">
							<textarea class="text memo"  name="memo">${memo}</textarea>
						</td>
					</tr>
					<tr>
						<td colspan="7">
							<textarea class="mefont isMemo" id="isMemo" value=""></textarea>
						</td>
					</tr>
				</table>
				<div class="title-style">${message("移库接收项")}:
					<div class="btns">
						<a href="javascript:;" id="copyData" class="button">复制</a>
					</div>
				</div>
				<table id="table-movelibraryIssue"></table>
				<div class="title-style">
					[#if warehouse.enableBatch]
						${message("批次列表")}:		
					[/#if]
				</div>
				<table id="table-bacth"></table>

				<div class="title-style">${message("移库接收汇总")}</div>
				<table class="input input-edit">
					<tr>
						<th>${message("接收箱数")}:</th>
						<td>
							<span id="totalBoxQuantity"></span>
						</td>
						<th>${message("接收支数")}:</th>
						<td>
							<span id="totalBranchQuantity"></span>
						</td> 
						<th>${message("接收数量")}:</th>
						<td>
							<span id="totalQuantity"></span>
						</td> 
						<th>${message("接收重量")}:</th>
						<td>
							<span id="totalWeight"></span>
						</td> 
					</tr>
					<tr>
						<th>${message("接收体积")}:</th>
						<td><span id="totalVolume"></span></td>
					</tr>
				</table>
			</div>
			<div class="fixed-top">
                <input type="hidden" id = "menuId" value="${menuId}"/>
                <input type="hidden" id="orderIndex" value="${orderIndex}"/>
                <input type="button" id="next_order" class="button "
                       value="${message("下一单")}" onclick="nextOrder('check_movelibrary_receive.jhtml?','movelibrary_receive_add.jhtml?')">
				<input type="button" id="submit_button" class="button sureButton"
					value="${message("保存")}" onclick="sub(this)"> 
					<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
			</div>
			<div id="loading" class="submit_loading " style="display:none">
				<div class="loading_show ">
					<img src="/resources/images/loading_icon.gif">
					<p class="loading_context ">正在提交，请稍后。。。</p>
				</div>
			</div>
	</form>
</body>
</html>