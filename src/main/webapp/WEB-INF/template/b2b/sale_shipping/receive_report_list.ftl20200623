<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("收发存报表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$().ready(function() {
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	/**************************************时间格式化处理************************************/
	function dateFtt(fmt,date) { 
		 var o = { 
		 "M+" : date.getMonth()+1,     //月份 
		 "d+" : date.getDate(),     //日 
		 "h+" : date.getHours(),     //小时 
		 "m+" : date.getMinutes(),     //分 
		 "s+" : date.getSeconds(),     //秒 
		 "q+" : Math.floor((date.getMonth()+3)/3),  
		 "S" : date.getMilliseconds()    //毫秒 
		 }; 
		 if(/(y+)/.test(fmt)) 
		 fmt=fmt.replace(RegExp.$1, (date.getFullYear()+"").substr(4 - RegExp.$1.length)); 
		 for(var k in o) 
		 if(new RegExp("("+ k +")").test(fmt)) 
		 fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length))); 
		 return fmt; 
	}
	var date =new Date();
	//当月第一天
	var firstDay=dateFtt("yyyy-MM-dd",new Date(date.getFullYear(), date.getMonth(), 1));
	$('#startTime').val(firstDay);
	//当天日期
	var today = dateFtt("yyyy-MM-dd",date);
	$('#endTime').val(today);
	
	//事业部
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询事业部")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='saleOrgName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='saleOrgName']").val();
				}
	
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".saleOrgId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="saleOrgId" class="text saleOrgId saleOrgId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newsaleOrgClosePro(this)"></i></div>';
						$(".saleOrg").append(vhtml);
					}
				}
				$("input[name='saleOrgName']").attr("value",allName);
			}
		}
	});	
	
	//单位
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询单位")}',
		url:'/member/store/select_store.jhtml?multi=2&isSelect=0',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='storeName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='storeName']").val();
				}
	
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".storeId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="storeId" class="text storeId storeId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newstoreClosePro(this)"></i></div>';
						$(".store").append(vhtml);
					}
				}
				$("input[name='storeName']").attr("value",allName);
			}
		}
	});	
	
	//仓库名称
	$("#selectWarehouse").bindQueryBtn({
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='warehouseName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='warehouseName']").val();
				}
	
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".warehouseId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="warehouseId" class="text warehouseId warehouseId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newwarehouseClosePro(this)"></i></div>';
						$(".warehouse").append(vhtml);
					}
				}
				$("input[name='warehouseName']").attr("value",allName);
			}
		}
	});	
	
	//品类
	$("#selectProductCategory").bindQueryBtn({
		type:'productCategory',
		title:'${message("查询品类")}',
		url:'/product/product_category/select_productCategory.jhtml?multi=2&isEnabled=1',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productCategoryName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productCategoryName']").val();
				}
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productCategoryId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="productCategoryId" class="text productCategoryId productCategoryId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newproductCategoryClosePro(this)"></i></div>';
						$(".productCategory").append(vhtml);
					}
				}
				$("input[name='productCategoryName']").attr("value",allName);
			}
		}
	});	
	
	//系列
	$("#selectVProduct").bindQueryBtn({
		type:'vProduct',
		title:'${message("查询系列")}',
		url:'/product/product_category/select_productCategory.jhtml?multi=2&isEnabled=1',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='vProductName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='vProductName']").val();
				}
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".vProductId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="vProductId" class="text vProductId vProductId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newvProductClosePro(this)"></i></div>';
						$(".vProduct").append(vhtml);
					}
				}
				$("input[name='vProductName']").attr("value",allName);
			}
		}
	});	
	
	//货物
	$("#selectproduct").bindQueryBtn({
		type:'product',
		title:'${message("查询货物")}',
		url:'/product/product/selectProduct.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productName']").val();
				}
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newproductClosePro(this)"></i></div>';
						$(".product").append(vhtml);
					}
				}
				$("input[name='productName']").attr("value",allName);
			}
		}
	});	
	
	
	var cols = [
		{ title:'${message("仓库组织")}' ,name:'organizationName', align:'center',width:80},
		{ title:'${message("仓库名称")}' ,name:'warehouseName', align:'center',width:80},
		{ title:'${message("等级")}',name:'levelName', align:'center',width:60},
		{ title:'${message("含水率")}',name:'moisture_content', align:'center',width:80},
		{ title:'${message("新旧标识")}' ,name:'new_old_logo', align:'center',width:80},
		{ title:'${message("色号")}', name:'colour_number' ,align:'center',width:60},
		{ title:'${message("批次编码")}', name:'batch_encoding' ,align:'center',width:80},  
		{ title:'${message("作业单号")}', name:'jobNumber' ,align:'center',width:80},  
		{ title:'${message("完工日期")}', name:'completion_date' ,align:'center',width:80},  
		{ title:'${message("批次备注")}', name:'batch_memo' ,align:'center',width:80},
		{ title:'${message("品类")}', name:'root_product_category_name' ,align:'center',width:80},
		{ title:'${message("系列")}', name:'product_category_name' ,align:'center',width:80},  	
		{ title:'${message("货物编码")}', name:'vonder_code' ,align:'center',width:120},  	
		{ title:'${message("货物名称")}', name:'product_name' ,align:'center',width:80},  	
		{ title:'${message("货物全名")}', name:'full_name' ,align:'center',width:100},  	
		{ title:'${message("型号")}', name:'model' ,align:'center',width:60},  	
		{ title:'${message("规格")}', name:'spec' ,align:'center',width:80},  	
		{ title:'${message("木种花色")}', name:'wood_type_or_color' ,align:'center',width:80},  	
		{ title:'${message("长度mm")}', name:'erp_length' ,align:'center',width:60},  	
		{ title:'${message("宽度mm")}', name:'erp_width' ,align:'center',width:60},  	
		{ title:'${message("厚度mm")}', name:'erp_height' ,align:'center',width:60},  	
		{ title:'${message("期初数量")}', name:'initial_quantity' ,align:'center',width:80},  	
		{ title:'${message("期初支数")}', name:'initial_branch' ,align:'center',width:60},  	
		{ title:'${message("期初件数")}', name:'initial_piece' ,align:'center',width:60},  	
		{ title:'${message("入库数量")}', name:'enter_quantity' ,align:'center',width:80},  	
		{ title:'${message("入库支数")}', name:'enter_branch' ,align:'center',width:60},  	
		{ title:'${message("入库件数")}', name:'enter_piece' ,align:'center',width:60},  
		{ title:'${message("出库数量")}', name:'out_quantity' ,align:'center',width:80},  	
		{ title:'${message("出库支数")}', name:'out_branch' ,align:'center',width:60},  	
		{ title:'${message("出库件数")}', name:'out_piece' ,align:'center',width:60},  
		{ title:'${message("期末数量")}', name:'final_quantity' ,align:'center',width:80},  	
		{ title:'${message("期末支数")}', name:'final_branch' ,align:'center',width:60},
		{ title:'${message("期末件数")}', name:'final_piece' ,align:'center',width:60}
		
	];
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: 'receive_report_list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
});


function newsaleOrgClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".saleOrg > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='saleOrgName']").attr("value",allName2)
};


function newstoreClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".store > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='storeName']").attr("value",allName2)
};


function newwarehouseClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".warehouse > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='warehouseName']").attr("value",allName2)
};


function newproductCategoryClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".productCategory > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='productCategoryName']").attr("value",allName2)
};

function newvProductClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".vProduct > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='vProductName']").attr("value",allName2)
};

function newproductClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".product > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='productName']").attr("value",allName2)
};

//条件导出		    
function segmentedExport(e){
	var needConditions = false;//至少一个条件
	var page_url = 'to_receiveReport_condition_export.jhtml';//分页导出统计页面
	var url = 'receiveReport_condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的订单！")}';//提示
	var url = 'selected_receiveReport_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

</script>
</head>
<body>
	<form id="listForm" action="" method="get">
			<div class="bar">
				<div class="buttonWrap">
					<div class="flag-wrap flagImp-wrap">
						<a href="javascript:void(0);" class="iconButton" id="export1Button"> 
							<span class="impIcon">&nbsp;</span>导出
						</a>
						<ul class="flag-list">
							<li>
								<a href="javascript:void(0)" onclick="exportExcel(this)">
									<i class="flag-imp02"></i>
									${message("选择导出")}
								</a>
							</li>
							<li>
								<a href="javascript:void(0)" onclick="segmentedExport(this)">
									<i class="flag-imp02"> </i>
									${message("条件导出")}
								</a>
							</li>
						</ul>
					</div>
				</div>
			</div>
			<div id="searchDiv">
	        	<div id="search-content" >
					<dl>
	        			<dt><p>${message("单据日期")}:</p></dt>
	        			<dd class="date-wrap">
							<input id="startTime" name="startTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl"> 至  </div>
							<input id="endTime" name="endTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
						</dd>
	        		</dl>
					
					<dl>
	        			<dt><p>${message("仓库组织")}：</p></dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list storeMemberOrganizationList as storeMemberOrganization]
										<label><input class="check js-iname" name="organizationId" value="${storeMemberOrganization.organization.id}" type="checkbox"/>${storeMemberOrganization.organization.name}</label>
									[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
					<dl>
						<dt><p>${message("仓库名称")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear"/>
							<input type="text" name="warehouseName" class="text warehouseId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
							<input type="button" class="iconSearch" id="selectWarehouse">
							<div class="pupTitleName  warehouse"></div>
							</span>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("品类")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="productCategoryId" class="text productCategoryId" btn-fun="clear"/>
							<input type="text" name="productCategoryName" class="text productCategoryId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
							<input type="button" class="iconSearch" id="selectProductCategory">
							<div class="pupTitleName  productCategory"></div>
							</span>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("系列")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="vProductId" class="text vProductId" btn-fun="clear"/>
							<input type="text" name="vProductName" class="text vProductId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
							<input type="button" class="iconSearch" id="selectVProduct">
							<div class="pupTitleName  vProduct"></div>
							</span>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("货物")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="productId" class="text productId" btn-fun="clear"/>
							<input type="text" name="productName" class="text productId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
							<input type="button" class="iconSearch" id="selectproduct">
							<div class="pupTitleName  product"></div>
							</span>
						</dd>
					</dl>
					<dl>
			    		<dt >
			    			<p>${message("木种名称")}：</p>
			    		</dt>
		    			<dd>
		    				<input type="text" class="text" name="woodTypeOrColor" value ="" btn-fun="clear" />
		    			</dd>
			    	</dl>
			    	<dl>
			    		<dt >
			    			<p>${message("货物型号")}：</p>
			    		</dt>
		    			<dd>
		    				<input type="text" class="text" name="model" value ="" btn-fun="clear" />
		    			</dd>
			    	</dl>
				</div>
			    <div class="search-btn" style="height:32px">
			    	<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			    </div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>