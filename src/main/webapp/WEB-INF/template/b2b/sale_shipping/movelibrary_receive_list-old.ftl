<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("移库接收列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript">
	//查看PDF
	function check_pdf(e,id){
		ajaxSubmit(e,{
			url:"/b2b/move_library/checkPdf.jhtml",
			method:"post",
			data:{ids:id},
			async: false,
			callback:function(resultMsg){
				window.open(resultMsg.content);
			}
		});
	}
	
	function editPrice(t,e){
		extractNumber(t,2,false,e)
	}

	function edit(url){
		parent.change_tab(0,url);
	}

	function editQty(t,e){
		if(extractNumber(t,3,false,e)){
			var $tr = $(t).closest("tr");
			var branchQuantity=0;//支数
			var boxQuantity = 0;//箱数
			var branchPerBox=$tr.find(".branchPerBox").val();//每箱支数
			var perBranch=$tr.find(".perBranch").val();
			
			if($(t).attr("kid")=="box"){
				boxQuantity = $(t).val();//箱数
				branchQuantity = accMul(boxQuantity,branchPerBox);//支数
				if(isNaN(branchQuantity)){
	    			branchQuantity = 0;
				}
	    		$tr.find(".branchQuantity").val(branchQuantity);//支数
	    		$tr.find(".scatteredQuantityStr").html(0);//零散支数
				$tr.find(".scatteredQuantity").val(0);
			}else if($(t).attr("kid")=="branch"){
				branchQuantity = $(t).val();//支数
				boxQuantity = parseInt(branchQuantity/branchPerBox);//箱数
				var scattered = branchQuantity%branchPerBox;
				$tr.find(".boxQuantity").val(boxQuantity);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}
			var quantity=accMul(branchQuantity,perBranch);
	    	if(isNaN(quantity)){
	    		quantity = 0;
			}
			$tr.find(".quantity").val(quantity);//数量
	    	$tr.find(".quantityStr").html(quantity);
	    	
	    	var row = $mmGrid.row($tr.index());
	    	if($tr.find(".mmg-check").prop("checked")){
	    		map[row.id] = Number(quantity);
	    		mapBx[row.id] = Number(boxQuantity);
	    		mapBc[row.id] = Number(branchQuantity);
	        	$(".total").text(countTotal());
	    	}else{
	    		if(map[row.id]!=undefined){
	    			map[row.id] = undefined;
	    			mapBx[row.id] = undefined;
	    			mapBc[row.id] = undefined;
	    		}
	    	}
	
		}
	}	

	function calculateTotalBoxQuantity(){
		var $input = $("input.boxQuantity");
		var $checkbox = $tr.find("input.mmg-check:checked");
		var row = $.data($tr[0],'item');
		map[row.id] = num;
		$(".total").text(countTotal());
	
	}
	var map = {};
	var mapBx = {};
	var mapBc = {};
	var total = 0;


	function removeCheck(){
	   $("input.mmg-check").attr("checked",false);//取消全选
	   $mmGrid.deselect();
	}
	
	$().ready(function() {
		//设置旗标
		setupFlag();
		//仓库名称
		$("#selectReceiveWarehouse").bindQueryBtn({
			type:'warehouse',
			title:'${message("查询仓库")}',
			url:'/stock/warehouse/select_warehouse.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var vhtml="";
					if($("input[name='receiveWarehouseName']").val() == null){
						var allName= "";
					}else{
						var allName= $("input[name='receiveWarehouseName']").val();
					}
					var idH ="";
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".receiveWarehouseId_"+rows[i].id).length;
						if(idH == 0){
							allName =allName +','+ rows[i].name;
							vhtml = '<div><input name="receiveWarehouseId" class="text receiveWarehouseId receiveWarehouseId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newwarehouseClosePro(this)"></i></div>';
							$(".receiveWarehouse").append(vhtml);
						}
					}
					$("input[name='receiveWarehouseName']").attr("value",allName);
				}
			}
		});	
		//机构
		$("#selectSaleOrg").bindQueryBtn({
			type:'saleOrg',
			title:'${message("查询事业部")}',
			url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var vhtml="";
					if($("input[name='saleOrgName']").val() == null){
						var allName= "";
					}else{
						var allName= $("input[name='saleOrgName']").val();
					}
		
					var idH ="";
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".saleOrgId_"+rows[i].id).length;
						if(idH == 0){
							allName =allName +','+ rows[i].name;
							vhtml = '<div><input name="saleOrgId" class="text saleOrgId saleOrgId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newsaleOrgClosePro(this)"></i></div>';
							$(".saleOrg").append(vhtml);
						}
					}
					$("input[name='saleOrgName']").attr("value",allName);
				}
			}
		});	
		//查询SBU
		$("#selectSbu").bindQueryBtn({
			type:'sbu',
			title:'${message("查询sbu")}',
			url:'/basic/sbu/select_sbu_filtr.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var vhtml="";
					if($("input[name='sbuName']").val() == null){
						var allName= "";
					}else{
						var allName= $("input[name='sbuName']").val();
					}
		
					var idH ="";
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".sbuId_"+rows[i].id).length;
						if(idH == 0){
							allName =allName +','+ rows[i].name;
							vhtml = '<div><input name="sbuId" class="text sbuId sbuId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newsbuClosePro(this)"></i></div>';
							$(".sbu").append(vhtml);
						}
					}
					$("input[name='sbuName']").attr("value",allName);
				}
			}
		});	
		//产品名称
		$("#selectProduct").bindQueryBtn({
			type:'product',
			title:'${message("查询产品")}',
			url:'/product/product/selectProduct.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var vhtml="";
					if($("input[name='productName']").val() == null){
						var allName= "";
					}else{
						var allName= $("input[name='productName']").val();
					}
					
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".productId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						allName =allName +','+ rows[i].name  
						vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newproductClosePro(this)"></i></div>'
						$(".product").append(vhtml);
					}
					$("input[name='productName']").attr("value",allName)
				}
			}
		});
		
		//创建人
		$("#selectStoreMember").bindQueryBtn({
			type:'creator',
			title:'${message("查询创建人")}',
			url:'/member/store_member/select_store_member.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var vhtml="";
					if($("input[name='creatorName']").val() == null){
						var allName= "";
					}else{
						var allName= $("input[name='creatorName']").val();
					}
					var idH ="";
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".creatorId_"+rows[i].id).length;
						if(idH == 0){
							allName =allName +','+ rows[i].name;
							vhtml = '<div><input name="creatorId" class="text creatorId creatorId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newstoreMemberClosePro(this)"></i></div>';
							$(".storeMember").append(vhtml);
						}
					}
					$("input[name='creatorName']").attr("value",allName);
				}
			}
		});	
		
		var cols = [
			{ title:'${message("单据编号")}', name:'sn' ,width:80, align:'center',renderer:function(val,item){
				var html = "";
				if(isNull(item.receive_flag) != null){
					html += '<i class="icons-flag i-f0'+item.receive_flag+'"></i> '
				}
				html += '<a href="javascript:void(0);" onclick="check_pdf(this,'+item.move_library+')" title="查看PDF"><img style="width: 20px;" src="/resources/images/ic_pdf.png"></a>&nbsp;';
				    html += '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/b2b/move_library/edit.jhtml?id='+item.move_library+'\')" class="red">'+val+'</a>';
				return html    
			}},
			{ title:'${message("机构")}', name:'sale_org_name' ,width:60, align:'center' },
			{ title:'${message("目的仓")}', name:'receive_name' ,width:80, align:'center' },	
			{ title:'${message("SBU")}', name:'sbu_name' ,width:60, align:'center' },	
			{ title:'${message("需求日期")}', name:'need_date' ,width:80, align:'center' },
			{ title:'${message("产品名称")}', name:'product_name', width:80 ,align:'center' },
			{ title:'${message("产品描述")}', name:'detail_description' ,width:200, align:'center' },
			{ title:'${message("产品型号")}', name:'product_model' ,width:60, align:'center' },
			{ title:'${message("产品规格")}', name:'product_spec' ,width:80, align:'center' },
			{ title:'${message("产品编码")}', name:'vonder_code' ,width:120, align:'center' },	
			{ title:'${message("经营组织")}', name:'product_organization_name' ,width:80, align:'center' },
			{ title:'${message("产品等级")}', name:'levelName' ,width:60, align:'center' },
			{ title:'${message("制单箱数")}', name:'available_receive_box_quantity' ,width:100, align:'center', renderer: function(val,item,rowIndex){
				var boxQuantity=parseInt(val);
				if(isNaN(boxQuantity)){
					boxQuantity = 0;
				}
				mapBx[item.id];
				var html = '<div class="nums-input ov">'+
	            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	            	'<input type="text" kid="box" class="t boxQuantity" _id="'+item.id+'" value="'+boxQuantity+'" minData="0" maxData="'+boxQuantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	        	'</div>';
	        	return html;
			}},
			{ title:'${message("支数")}', name:'available_receive_branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
					var branchQuantity=parseInt(val);
					if(isNaN(branchQuantity)){
						branchQuantity = 0;
					}
				    mapBc[item.id];
					var html = '<div class="nums-input ov">'+
	            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	            	'<input type="text" kid="branch" class="t branchQuantity" value="'+branchQuantity+'" minData="0" maxData="'+branchQuantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	            	'<input type="hidden" class="branchPerBox" value="'+item.branch_per_box+'" /> '+
					'<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
					'<input type="hidden" class="perBranch" value="'+item.per_branch+'" />'+
	        	'</div>';
				return html;
			}},
			{ title:'${message("零散支数")}', name:'available_receive_scattered_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
				var scatteredQuantity=parseInt(val);
				if(isNaN(scatteredQuantity)){
					scatteredQuantity = 0;
				}
				var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
					'<input type="hidden" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
				return html;
			}},
			{ title:'${message("数量")}', name:'available_receive_quantity', align:'center', width:120, renderer:function(val,item,rowIndex,obj){
				var quantity = parseFloat(val);
				if(isNaN(quantity)){
					quantity = 0;
				}
				map[item.id];
				var text = 
				'<div class="nums-input ov">'+
		        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
		        	'<input type="text"   class="t quantity" value="'+quantity+'" maxData="'+quantity+'" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
		        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
		    	'</div>';
		    	return text;
			}},
			{ title:'${message("接收数量")}', name:'receive_quantity' ,width:80, align:'center' },
			{ title:'${message("接收箱数")}', name:'receive_box_quantity' ,width:60, align:'center' },
			{ title:'${message("接收支数")}', name:'receive_branch_quantity' ,width:60, align:'center' },
			{ title:'${message("接收日期")}', name:'receive_date' ,width:80, align:'center' },
			[#if hiddenBatch !=0 ] 
				{ title:'${message("含水率")}', name:'moisture_content_name' ,width:80, align:'center' },
				{ title:'${message("色号")}', name:'colour_number_name' ,width:50, align:'center' },
				{ title:'${message("批次")}', name:'batch_encoding' ,width:100, align:'center' },
			[/#if]
			{ title:'${message("创建人")}', name:'store_member_name' ,width:60, align:'center' },
			{ title:'${message("创建日期")}', name:'create_date' ,width:80, align:'center' },
			{ title:'${message("备注")}', name:'move_library_item_remarks' ,width:100, align:'center' }	
		];
		
		$mmGrid = $('#table-m1').mmGrid({
			multiSelect:true,
			autoLoad: true,
			fullWidthRows:true,
			checkByClickTd:true,
			rowCursorPointer:true,
			useCountText:false,
			recordRowId:true,
	        cols: cols,
	        url: 'movelibrary_list_data.jhtml',
	        onSelectRow:function(row,e,val){
	        	if(val){
	        		var $input = $(e).find("input.quantity");
	        		map[row.id] = Number($input.val());
	        		var $boxQuantity=$(e).find("input.boxQuantity").val();
	        		mapBx[row.id] = Number(Number($boxQuantity));
					var $branchQuantity=$(e).find("input.branchQuantity").val();
	        		mapBc[row.id] = Number(Number($branchQuantity));
	        	}else{
	        		map[row.id] = undefined;
	        		mapBx[row.id] = undefined;
	        		mapBc[row.id] = undefined;
	        	}
	        	$(".recordsChecked").text($mmGrid.selecteds.length);
	        	$(".total").text(countTotal());
	        },
	        onSelectAll:function(rows,e,val){
	        	$(e).each(function(index){
	       			var row = rows[index];
	       			if(val){
	       				var $tr = $(this);
	        			var $input = $tr.find("input.quantity");
	        			var value = $input.val();
	       				map[row.id] = Number(value);
	       				var $boxQuantity=$tr.find("input.boxQuantity").val();
	       				mapBx[row.id] = Number(Number($boxQuantity));
	       				var $branchQuantity=$tr.find("input.branchQuantity").val();
	               		mapBc[row.id] = Number(Number($branchQuantity));
	       			}else{
	       				map[row.id] = undefined;
	       				mapBx[row.id] = undefined;
	               		mapBc[row.id] = undefined;
	       			}
	        	})
	        	$(".recordsChecked").text($mmGrid.selecteds.length);
	        	$(".total").text(countTotal());
	        },
	        callback:function(){
	        	$(".recordsChecked").text($mmGrid.selecteds.length);
	        	$(".total").text(countTotal());
	        },
		    params:function(){
	        	return $("#listForm").serializeObject();
	        },
		    plugins : [
	            $('#paginator').mmPaginator()
	        ]
	    }); 
	    
	    $("#moveLibraryBtn").click(function(){
	    	var data = map;
	    	var params = '';
	    	for(var key in map){
	    		var qy = map[key];
	    		var bxQ=mapBx[key];
	    		var bcQ=mapBc[key];
	    		if(qy!=undefined){
		    		params+='&ids='+key;
		    		params+='&quantity='+qy;
	    		}
	    		if(bxQ!=undefined){
	    			params+='&boxQuantity='+bxQ;
	    		}
	    		if(bcQ!=undefined){
	    			params+='&branchQuantity='+bcQ;
	    		}
	    	}
	    	if(params.length==0){
	    		$.message_alert("请选择移库接收明细");
	    		return false;
	    	}
			params = params.substring(1,params.length);    		
			ajaxSubmit(null,{
				url:"/b2b/sale_shipping_nature/check_movelibrary_receive.jhtml?"+params,
				method:"post",
				async: false,
				data:data,
				callback:function(resultMsg){
					var data = $.parseJSON(resultMsg.content);
					for(var key in data){
						var value = data[key];
						if(value!=undefined){
							params+="&"+key+"="+encodeURIComponent(value);
						}
					}
					var h = $(window).height();
					var w = $(window).width()-3;
					var url = "/b2b/sale_shipping_nature/movelibrary_receive_add.jhtml?"+params;
					edit(url);
					
				}
			});
			removeCheck();
	    })
		
	});		

	
	
	//来源仓库
	function newwarehouseClosePro(e){
		$(e).closest("div").remove();
		var allName2 = '';
		$(".receiveWarehouse > div").each(function(){
			allName2 = allName2 +','+  $(this).find("p").html();
		})
		$("input[name='receiveWarehouseName']").attr("value",allName2)
	};
	//机构
	function newsaleOrgClosePro(e){
		$(e).closest("div").remove();
		var allName2 = '';
		$(".saleOrg > div").each(function(){
			allName2 = allName2 +','+  $(this).find("p").html();
		})
		$("input[name='saleOrgName']").attr("value",allName2)
	};
	//SBU
	function newsbuClosePro(e){
		$(e).closest("div").remove();
		var allName2 = '';
		$(".sbu > div").each(function(){
			allName2 = allName2 +','+  $(this).find("p").html();
		})
		$("input[name='sbuName']").attr("value",allName2)
	};
	//产品名称
	function newproductClosePro(e){
		$(e).closest("div").remove();
		var allName2 = '';
		$(".product > div").each(function(){
			allName2 = allName2 +','+  $(this).find("p").html();
		})
		$("input[name='productName']").attr("value",allName2)
	}
	//创建人
	function newstoreMemberClosePro(e){
		$(e).closest("div").remove();
		var allName2 = '';
		$(".storeMember > div").each(function(){
			allName2 = allName2 +','+  $(this).find("p").html();
		})
		$("input[name='creatorName']").attr("value",allName2)
	};


	function countTotal(){
		total = 0;
		for(var key in map) { 
			var value = map[key];
			if(value!=undefined){
				total = total+value;
				total = total.toFixed(6);
				total = parseFloat(total);
			}
		} 
		return total;
	}

	//条件导出		    
	function segmentedExport(e){
		var needConditions = true;//至少一个条件
		var page_url = 'to_movelibrary_condition_export.jhtml';//分页导出统计页面
		var url = 'movelibrary_receive_condition_export.jhtml';//导出的方法
		conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
	}

	//选择导出
	function exportExcel(t){
		var param = $mmGrid.serializeSelectedIds();//参数
			param+='&movelibrarySign=false';
		var tip = '${message("请选择导出的订单！")}';//提示
		var url = 'selected_movelibrary_receive_export.jhtml';//导出的方法
	  	select_export(t,{tip:tip, param:param, url:url});
	}
	
	//设置旗标
	function insertFlag(e){
		//参数
		var ids = $mmGrid.serializeSelectedIds();
		if(ids.length==0){
			$.message_alert("请选择单据行");
			return false;
		}
		ajaxSubmit($(e),{
			method:'post',
			url:'setBillItemFlag.jhtml?flag='+e+'&billType=3',
			data:ids,
			callback: function() {
				$mmGrid.load();
			}
		})
	}
	 
</script>
</head>
<body>
	<form id="listForm" action="movelibrary_issue_list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span> 导入导出
					</a>
					<ul class="flag-list">
						<li>
							<a href="javascript:void(0)" onclick="exportExcel(this)">
								<i class="flag-imp02"></i> ${message("选择导出")}
							</a>
						</li>
						<li>
							<a href="javascript:void(0)" onclick="segmentedExport(this)"> 
								<i class="flag-imp02"></i>
								${message("条件导出")}
							</a>
						</li>

					</ul>
				</div>
			</div>
			<div id="searchDiv">
				<div id="search-content">
				    <input type="hidden" class="text" name="movelibrarySign" value="false" />
					<dl>
						<dt>
							<p>${message("单据编号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="sn" value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt><p>${message("机构名称")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
								<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear"/>
								<input type="text" name="saleOrgName" class="text saleOrgId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
								<input type="button" class="iconSearch" id="selectSaleOrg">
								<div class="pupTitleName  saleOrg"></div>
							</span>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("目的仓库")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
								<input type="hidden" name="receiveWarehouseId" class="text receiveWarehouseId" btn-fun="clear"/>
								<input type="text" name="receiveWarehouseName" class="text receiveWarehouseName" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
								<input type="button" class="iconSearch" id="selectReceiveWarehouse">
								<div class="pupTitleName  receiveWarehouse"></div>
							</span>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("SBU")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
								<input type="hidden" name="sbuId" class="text sbuId" btn-fun="clear"/>
								<input type="text" name="sbuName" class="text sbuId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
								<input type="button" class="iconSearch" id="selectSbu">
								<div class="pupTitleName  sbu"></div>
							</span>
						</dd>
					</dl>
					<dl>
			    		<dt>
			    			<p>${message("产品名称")}:</p>
			    		</dt>
	    				<dd>
	    					<span style="position:relative">
	    						<input type="hidden" name="productId" class="text productId" btn-fun="clear"/>
								<input class="text productName" maxlength="200" type="text" name="productName" value="" onkeyup="clearSelect(this)" readonly>
								<input type="button" class="iconSearch" value="" id="selectProduct">
								<div class="pupTitleName product"></div>
							</span>
	    				</dd>
	    			</dl>
					<dl>
						<dt><p>${message("接收日期")}:</p></dt>
						<dd class="date-wrap">
							<input id="startReceiveDate" name="startReceiveDate" class="text Wdate" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startReceiveDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endReceiveDate\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endReceiveDate" name="endReceiveDate" class="text Wdate" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',endReceiveDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startReceiveDate\')}'});" type="text" btn-fun="clear"/>
						</dd>
					</dl>
					<dl>
						<dt><p>${message("创建人")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
								<input type="hidden" name="creatorId" class="text creatorId" btn-fun="clear"/>
								<input type="text" name="creatorName" class="text creatorName" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
								<input type="button" class="iconSearch" id="selectStoreMember">
								<div class="pupTitleName  storeMember"></div>
							</span>
						</dd>
					</dl>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator" style="text-align: left;">
				<div id="paginator"></div>
			</div>
		</div>
		<div class="floatDiv" style="position: fixed; top: 0; left: 225px; height: 38px; text-align: right;">
			<p class="checked-p" style="line-height: 38px; font-size: 14px;">当前已选择移库接收明细
			<span class="recordsChecked" style="color: blue">0</span>行，总数量
			<span class="total" style="color: blue">0</span> ， 请点击
			<a href="javascript:void(0);" class="iconButton" id="moveLibraryBtn" style="float: none;">添加</a> ， 创建移库接收通知单！
			</p>
		</div>
	</form>
</body>
</html>