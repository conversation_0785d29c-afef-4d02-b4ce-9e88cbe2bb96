<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("退货接收添加页面")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>
    <style type="text/css">
	tr.s-tr, tr.s-tr td {
		height: 10px !important;
	}
	
	.mefont {
		color: red;
		font-style：italic;
		斜体
		font-weight：bold;
		加粗
		font-size：30px;
		大小
		line-height：30px;
		行高
		font-family：“SimHei”;
		字体
	}
</style>
<script type="text/javascript">

	function productOrganization(){
		var typesystemDictFlag = $("input.typesystemDictFlag").val();
		if(isNull(typesystemDictFlag) != null){
			$("input[name='productIds").each(function(){
				var $this = $(this);
				var productId = $this.val();
				var $tr = $this.closest("tr");
				if(typesystemDictFlag == 0){
					$tr.find(".productOrganizationName").text($(".organizationName").text());
					$tr.find("input.productOrganizationId").val($("input.organizationId").val());
					[#if linkStock == 1 ] 
						loadAttQuantity($tr);
					[/#if]	
				}else if (typesystemDictFlag == 1){
					[#if linkStock == 1 ] 
						loadAttQuantity($tr);
					[/#if]	
				}
			});		
		}
	}

	//加载库存
	function loadAttQuantity($tr){
		[#if storageStockQueryRoles ==0] 
			jurisdictionStockQueryRoles($tr);
			return;
		[/#if]
		//产品经营组织
		var productOrganizationId = $tr.find("input.productOrganizationId").val();
		if(isNull(productOrganizationId) != null){
			
			//仓库
			var warehouseId = $("input.warehouseId").val();
			//产品
			var productId = $tr.find("input.productId").val();
			//等级
			var productGrade = $tr.find(".grade").val();
			//色号
			var colourNumber = $tr.find(".colourNumber").val();
			if(isNull(colourNumber) == null){
				colourNumber = "";
			}
			//含水率
			var moistureContent = $tr.find(".moistureContent").val();
			if(isNull(moistureContent) == null){
				moistureContent = "";
			}
			//批次
			var batchIds = $tr.find("input.batchIds").val();
			if(isNull(batchIds) == null){
				batchIds = "";
			}else{
				batchIds = batchIds.split(';');
			}
			//新旧标识
			var newOldLogo = $tr.find(".newOldLogo").val();
			if(isNull(newOldLogo) == null){
				newOldLogo = "";
			}
			var params='&warehouseId='+warehouseId+'&productId='+productId+'&productGrade='+productGrade
			  		+'&organizationId='+productOrganizationId+'&colourNumber='+colourNumber+'&moistureContent='+moistureContent
			  		+'&batchIds='+batchIds+'&newOldLogos='+newOldLogo;
				params = params.substring(1,params.length);
			$.ajax({
				url:'/stock/stock/findViewStock.jhtml?'+params,
	   			type : "post",
	   			success : function(rows) {
	   				var data= $.parseJSON(rows.content);
	                   if(data.length>0){
	                       for (var i = 0; i < data.length;i++) {
		                        //可用库存箱数
		                       	if(isNull(data[i].totalAttQuantity2) != null) {
		                       		$tr.find(".attQuantity2BoxNum").text(data[i].totalAttQuantity2);		                   
		                       	}else {
		                       		$tr.find(".attQuantity2BoxNum").text(0);		                       		
		                       	}		                     
		                        //可用库存支数
		                       	if(isNull(data[i].totalAttQuantity3) != null) {
		                       		$tr.find(".attQuantity3Num").text(data[i].totalAttQuantity3);
		                       	}else {
		                       		$tr.find(".attQuantity3Num").text(0);		                       		
		                       	}
		                        //可用库存数量
		                       	if(isNull(data[i].totalAttQuantity1) != null) {
		                       		$tr.find(".attQuantity1Num").text(data[i].totalAttQuantity1);
		                       	}else {
		                       		$tr.find(".attQuantity1Num").text(0);		                       		
		                       	}
	                   	}
	               	}else{
	               		$tr.find(".attQuantity2BoxNum").text(0);
	               		$tr.find(".attQuantity3Num").text(0);		 
	               		$tr.find(".attQuantity1Num").text(0);	
	               	}
	   			}
			})
		}
	} 


	function countTotal(t){
		var totalBoxQuantity = 0;
		var totalBranchQuantity = 0;
		var totalVolume = 0;
		var totalWeight = 0;
		var $bInput = $("input.boxQuantity");
		$bInput.each(function(){
	        var quantity = 0;
	        var $tr = $(this).closest("tr");
	        var isEqual = null;
	        if(t!=undefined){
	            isEqual = (t.find(".b2bReturnsItemId").val() == $tr.find(".b2bReturnsItemId").val());                	
	        }
	        var boxQuantity=$(this).val();
	        var branchPerBox=$tr.find(".branchPerBox").val();
	        var perBranch=$tr.find(".perBranch").val();
	        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
	        var perBox = $tr.find(".perBox").val();//
	        var type = productDisc(branchPerBox,perBranch,perBox);
	        if(isEqual==null){
	        	if(type==0){
	            	var branchQuantity=accMul(boxQuantity,branchPerBox);
	            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	            	if(isNaN(branchQuantity)){
	            		branchQuantity = 0;
	        		}
	            	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	            	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	            	$tr.find(".branchQuantity").val(branchQuantity);//支数
	            	$tr.find(".branchQuantityStr").html(branchQuantity);
	            	quantity=accMul(branchQuantity,perBranch);
	            	
	            	if(isNaN(quantity)){
	            		quantity = 0;
	        		}
	            	$tr.find(".quantity").val(quantity);//数量
	            	$tr.find(".quantityStr").html(quantity);
	            	var volume=$tr.find(".volume").val();
	            	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
	        		if(isNaN(volumeAmount)){
	        			volumeAmount = 0;
	        		}
	        		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
	        		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
	            }
	          	//辅料计算逻辑	
	        	if(type==2){
	        		var a = accDiv(perBox,10);
	        		quantity = accMul(boxQuantity,a);
	        	}
	          	if(type==1){
	          		quantity = $tr.find(".quantity").val();
	          	}
	          	if(type==3){
	          		var branchQuantity = $tr.find(".branchQuantity").val();
	          		quantity = accMul(branchQuantity,perBranch);
	          	}
	        }else{
	        	if(type==0&&isEqual){
	            	var branchQuantity=accMul(boxQuantity,branchPerBox);
	            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	            	if(isNaN(branchQuantity)){
	            		branchQuantity = 0;
	        		}
	            	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	            	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	            	$tr.find(".branchQuantity").val(branchQuantity);//支数
	            	$tr.find(".branchQuantityStr").html(branchQuantity);
	            	quantity=accMul(branchQuantity,perBranch);
	            	
	            	if(isNaN(quantity)){
	            		quantity = 0;
	        		}
	            	$tr.find(".quantity").val(quantity);//数量
	            	$tr.find(".quantityStr").html(quantity);
	            	var volume=$tr.find(".volume").val();
	            	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
	        		if(isNaN(volumeAmount)){
	        			volumeAmount = 0;
	        		}
	        		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
	        		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
	            }
	          	//辅料计算逻辑	
	        	if(type==2&&isEqual){
	        		var a = accDiv(perBox,10);
	        		quantity = accMul(boxQuantity,a);
	        		$tr.find(".quantity").val(quantity);//数量
	            	$tr.find(".quantityStr").html(quantity);
	        	}
	          	if(type==1&&isEqual){
	          		quantity = $tr.find(".quantity").val();
	          		$tr.find(".quantity").val(quantity);//数量
	            	$tr.find(".quantityStr").html(quantity);
	          	}
	          	if(type==3&&isEqual){
	          		var branchQuantity = $tr.find(".branchQuantity").val();
	          		quantity = accMul(branchQuantity,perBranch);
	          		$tr.find(".quantity").val(quantity);//数量
	            	$tr.find(".quantityStr").html(quantity);
	          	}
	        }
			var volume=$tr.find(".volume").val();
	    	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
			if(isNaN(volumeAmount)){
				volumeAmount = 0;
			}
			totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
			$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
		});
		$("#totalBoxQuantity").text(totalBoxQuantity);
		$("#totalBranchQuantity").text(totalBranchQuantity);
		var $input = $("input.quantity");
		var total = 0;
		var totalQuantity = 0;
		var b = $("#storeBalance").val();
		$input.each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			var $price_box = $tr.find(".price-box");
			var price;
			if($price_box.length==0 || $price_box.prop("checked")==false){
				price = Number($tr.find("input.price").val());
			}else{
				price = Number($tr.find("input.origMemberPrice ").val());
			}
			var quantity = $this.val();
			var amount = Number($this.val())*price;
			totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
			if(isNaN(amount)){
				amount = 0;
			}
			total = Number(total)+Number(currency(amount,false));
			$tr.find(".trprice").html(currency(amount,true));//订单行金额
			var weight=$tr.find(".weight").val();
			var weightAmount=Number(accMul($(this).val(),weight)).toFixed(6);
			if(isNaN(weightAmount)){
				weightAmount = 0;
			}
			totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
			$tr.find(".lineWeightAmount").html(weightAmount);//重量
		});
		$("#total").text(currency(total,true));
		$("#totalVolume").text(totalVolume);
		$("#totalWeight").text(totalWeight);
		$("#totalQuantity").text(totalQuantity);
		var storeBalance = $("#storeBalance").val()
		$("#chae").text(currency(storeBalance-total,true));
	}



	function editQty(t,e){
		if($(t).attr("kid")=="quantity"){//平方
			if(extractNumber(t,6,false,e)){
				var $tr = $(t).closest("tr");
				var branch_quantity=0;
				var box_quantity=0;
				var quantity=$(t).val();
				var perBox = $tr.find(".perBox").val();
				var perBranch=$tr.find(".perBranch").val();  //每支单位数
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var type = productDisc(branchPerBox,perBranch,perBox);
				var scattered=0;
				if(perBranch!=0 && branchPerBox!=0){
					 branch_quantity=quantity/perBranch;
					 box_quantity=parseInt(branch_quantity/branchPerBox);
					 scattered=(branch_quantity%branchPerBox).toFixed(6);
				}
				if(type==2){
					box_quantity = modulo(quantity,perBox);
				}
				if(type==3){
					branch_quantity = modulo(quantity,perBranch);
				}
				$tr.find(".boxQuantity").val(box_quantity);
				$tr.find(".branchQuantity").val(branch_quantity);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
				countTotal($tr);
				$(t).val(quantity);
				editLineInfo(t);
			}
		}else{
			if(extractNumber(t,3,false,e)){
				var $tr = $(t).closest("tr");
				var branch_quantity=0;
				var box_quantity=0;
				if($(t).attr("kid")=="box"){//箱
					$tr.find(".scatteredQuantityStr").html(0);
					$tr.find(".scatteredQuantity").val(0);
				}else if($(t).attr("kid")=="branch"){//支
					var quantity=$(t).val();
					var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
					var box=parseInt(quantity/branchPerBox);
					var scattered=quantity%branchPerBox;
					$tr.find(".boxQuantity").val(box);
					$tr.find(".scatteredQuantityStr").html(scattered);
					$tr.find(".scatteredQuantity").val(scattered);
				}else if($(t).attr("kid")=="quantity"){//平方
					var quantity=$(t).val();
					var perBranch=$tr.find(".perBranch").val();  //每支单位数
					var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
					var branch_quantity=quantity/perBranch;
					var box_quantity=parseInt(branch_quantity/branchPerBox);
					var scattered=branch_quantity%branchPerBox;
					$tr.find(".boxQuantity").val(box_quantity);
					$tr.find(".scatteredQuantityStr").html(scattered);
					$tr.find(".scatteredQuantity").val(scattered);
				}
				countTotal($tr);
				editLineInfo(t);
			}
		}
	}

	function editLineInfo(t){
		var $tr = $(t).closest("tr");
		var quantity = Number($tr.find(".quantity").val());
		var volume = Number($tr.find("input.volume").val());
		var weight = Number($tr.find("input.weight").val());
		var price = Number($tr.find("input.price").val());
		var lineVolume = accMul(quantity,volume);
		var lineWeight = accMul(quantity,weight);
		var lineAmount = accMul(quantity,price);
		lineVolume = lineVolume.toFixed(6);
		lineVolume = parseFloat(lineVolume);
		lineWeight = lineWeight.toFixed(6);
		lineWeight = parseFloat(lineWeight);
		$tr.find(".volumeSpan").text(lineVolume);
		$tr.find(".weightSpan").text(lineWeight);
		$tr.find(".lineAmountSpan").text(currency(lineAmount,true));
	}



	 <!-- 2019-08-05 冯旗 检测备注字数是否超过240字符-->  
	 function listenLength(){
	  	var memeo=$(".memo").val();
	  	var len = 0;  
	    for (var i=0; i<memeo.length; i++) {   
	    	 var c = memeo.charCodeAt(i);   
	    	 //单字节加1   
	     	 if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
	       		len++;   
	     	 } else {   
	      		len+=2;   
	     	 }   
	    }   
	    if(len>80){
	      	$("#isMemo").val("字符长度已超过240!");
	    }else{
	     	$("#isMemo").val("");
	    }
	 }

	var map = {};
	var total = 0;
	$().ready(function() {
		// 地区选择
		$("#areaId").lSelect();
		var itemIndex = 0;
		var cols = [
				{ title:'${message("行号")}',width:60, align:'center', renderer: function(val,item,rowIndex,obj){
					return rowIndex+1;
				}},
				{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
					return '<a href="javascript:;" class="btn-delete" onclick="deleteShipping(this)" >删除</a>';
				}},
				{ title:'${message("产品编码")}', name:'vonder_code' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
					return '<span class="vonderCode">'+val+'</span>';
				}},
				{ title:'${message("产品名称")}', name:'name' ,width:100, align:'center', renderer: function(val,item,rowIndex,obj){
					return val;
				}},
				{ title:'${message("产品描述")}', name:'detail_description',width:200, align:'center'},
				{ title:'${message("经营组织")}',width:100, align:'center', renderer: function(val,item,rowIndex,obj){
					var productOrganizationName = '';
						if(item.product_organization_name != null && item.product_organization_name !=''){
							productOrganizationName = item.product_organization_name;
						}
						var productOrganizationId = '';
						if(item.product_organization_id != null && item.product_organization_id !=''){
							productOrganizationId = item.product_organization_id;
						}
		 			var html='<span class="productOrganizationName">'+productOrganizationName+'</span>'+
		 				       '<input type="hidden" name="amShippingItems['+itemIndex+'].productOrganization.id" class="productOrganizationId text" value="'+productOrganizationId+'" />';
		    		return html;
				}},						 
				{ title:'${message("产品级别")}', name:'level_Id' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					var str='selected="selected"';
	    			var html='<select name="amShippingItems['+itemIndex+'].productLevel.id" class="text grade">';
	    				[#list productLevelList as products]
	    				if(${products.id}==item.level_Id){
	    					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
	    				}else{
	    					html+='<option value="${products.id}">${products.value}</option> ';
	    				}
	    				[/#list]
	    				html+='</select>';
	    			return html;
				}},
				{ title:'${message("件数")}',name:'box_quantity' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					if(isNull(val) == null){
						val = 0;
					}	
					var str = '';
					str+= 
					'<input type="hidden"  name="amShippingItems['+itemIndex+'].b2bReturnsItem.id" class="b2bReturnsItemId" value="'+item.id+'">'+	
					'<input type="hidden"  name="amShippingItems['+itemIndex+'].b2bReturns.id" value="'+item.b2b_returns+'">'+
					'<input type="hidden"  name="amShippingItems['+itemIndex+'].product.id" class="productId" value="'+item.product+'">'+
					'<input type="hidden"  name="productIds"  value="'+item.product+'">'+
					'<ul><li><div class="nums-input ov">'+
		            	'<input type="text" kid="box" class="t boxQuantity"  name="amShippingItems['+itemIndex+'].boxQuantity" value="'+val+'" minData="0" maxData="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		        	'</div></li><li><span class="attQuantity2BoxNum" >0</span></li></ul>';
					return str;	
				}},
				{ title:'${message("支数")}',name:'branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
					if(isNull(val) == null){
						val = 0;
					}	
					var branchPerBox = item.branch_per_box == null ? 0:item.branch_per_box;
					var perBox = item.per_box == null ? 0:item.per_box;
					var perBranch = item.per_branch == null ? 0:item.per_branch;
					var html='<input type=hidden class="branchPerBox" name="amShippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
						'<input type=hidden class="perBox"  value="'+perBox+'" />'+
						'<input type=hidden class="perBranch" name="amShippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
						'<ul><li><div class="nums-input ov">'+
		            	'<input type="text"  kid="branch" class="t branchQuantity" name="amShippingItems['+itemIndex+'].branchQuantity" value="'+val+'" minData="0" maxData="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		        	'</div></li><li><span class="attQuantity3Num">0</span></li></ul>';
					return html;
				}},
				{ title:'${message("零散支数")}',align:'center', width:60, renderer: function(val,item,rowIndex, obj){
					var scatteredQuantity=parseInt(item.branch_quantity % item.branch_per_box);
					if(isNaN(scatteredQuantity)){
						scatteredQuantity = 0;
					}
					var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
						'<input type="hidden" name="amShippingItems['+itemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
					return html;
				}},
				{ title:'${message("数量")}', name:'quantity', align:'center', width:80, renderer:function(val,item,rowIndex,obj){
					if(isNull(val) == null){
						val = 0;
					}
					var text = '<ul><li><div class="lh20">'+
						'<div class="nums-input ov square">'+
				        	'<input type="text" kid="quantity" class="t quantity"  name="amShippingItems['+itemIndex+'].quantity" value="'+val+'" minData="0" maxData="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
				        	'<input type="hidden" class="minPriceApplyQuantity" />'+
							'<input type="hidden" class="maxPriceApplyQuantity" />'+
				    	'</div></div></li><li><span class="attQuantity1Num">0</span></li></ul>';
				    	return text;
				}},
				[#if hiddenBatch !=0] 
						{ title:'${message("色号")}', align:'center',width:60,name:'colour_number',renderer: function(val,item,rowIndex, obj){
							var str='selected="selected"';
							var html='<select name="amShippingItems['+itemIndex+'].colorNumbers.id" class="text colourNumber">';
				   			html+='<option value="">请选择</option> ';  	
				   				[#list colorNumberList as colorNumbers]    
					   				if(${colorNumbers.id} == item.color_numbers_id){
					   					html+='<option value="${colorNumbers.id}" '+str+' >${colorNumbers.value}</option>';
					   				}else{
					   					html+='<option value="${colorNumbers.id}">${colorNumbers.value}</option> ';
					   				}  
				   				[/#list]
				   				html+='</select>';
				   			return html;
				        }},    
				        { title:'${message("含水率")}', align:'center',width:60,name:'moisture_content',renderer: function(val,item,rowIndex, obj){
				        	var str='selected="selected"';
				        	var html='<select name="amShippingItems['+itemIndex+'].moistureContents.id" class="text moistureContent">';
				   			html+='<option value="">请选择</option> ';  	
				   				[#list moistureContentList as moistureContents]    		   				
					   				if(${moistureContents.id} == item.moisture_content_id){
					   					html+='<option value="${moistureContents.id}" '+str+' >${moistureContents.value}</option> ';
					   				}else{
					   					html+='<option value="${moistureContents.id}">${moistureContents.value}</option> ';
					   				}    		   				
				   				[/#list]
				   				html+='</select>';
				   			return html;
				        }},
				        { title:'批次', align:'center', width:200,name:'batch',renderer: function(val,item,rowIndex, obj){
			            	var batch = '';
							if(isNull(item.batch) != null){
								batch = item.batch;
							}
							var batch_encoding = '';
							if(isNull(item.batch_encoding) != null){
								batch_encoding = item.batch_encoding;
							}
				        	 var html = '<span class="search" style="position:relative">'
				        			 	+'<input type="hidden" name="amShippingItems['+itemIndex+'].batch" class="text batchIds" value="'+batch+'" />'
					        		    +'<input type="text" name="amShippingItems['+itemIndex+'].batchEncoding" class="text batchEncodings" value="'+batch_encoding+'" readonly/>'
										+'<input type="button" class="iconSearch" value="" id="selectBatch"/>'			        			 		        		    
								+'</span>';
							 return html;
				         }},
				 [/#if]
				{ title:'${message("库位")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		        	 var html = '<span class="search" style="position:relative">'
		        			+'<input type="hidden" name="amShippingItems['+itemIndex+'].warehouseLocation.id" class="text warehouseLocationId" value="" />'
		        			+'<input type="text"  class="text warehouseLocationCode" oninput = "processingLocation(this)" value="" />'
		        			+'<input type="button" class="iconSearch" value="" id="selectStorehouse"/>'
		        		+'<div class="textDis" id="lineLocation"><ul class="lineLocationData"></ul></div></span>';
		        	 return html;
		        }},	  
				{ title:'${message("新旧标识")}', align:'center',width:100,name:'newOldLogo',renderer: function(val,item,rowIndex, obj){
				    var str='selected="selected"';
				   	var html='<select name="amShippingItems['+itemIndex+'].newOldLogos.id" class="text newOldLogo">';
				   	[#list newOldLogosList as newOldLogos]    		   				
				   		html+='<option value="${newOldLogos.id}">${newOldLogos.value}</option> ';    		   				
				   	[/#list]
				   	html+='</select>';
				   	return html;
				 }},
		        { title:'${message("备注")}',name:'memo',width:100,align:'center',renderer:function(val,item,rowIndex,obj){
					var memo = '';
					if(isNull(val) != null){
						memo = val;
					}
					var html= '<input type="text" name="amShippingItems['+itemIndex+'].memo" class="text" value="'+memo+'" />';
					return html;
				}},
				{ title:'${message("产品型号")}', name:'model' ,hidden:true,width:100, align:'center', renderer: function(val,item,rowIndex,obj){
					return val;
				}},
		        { title:'${message("单位")}',width:80, align:'center',name:'unit'},
				{ title:'${message("体积")}',name:'volume' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					var volume = (val!="")?val:0;
					var lineVolume = volume*item.quantity;
					lineVolume = lineVolume.toFixed(6);
					lineVolume = parseFloat(lineVolume);
					var html = '<span class="volumeSpan">'+lineVolume+'</span>'+
						'<input type="hidden" class="text volume" value="'+volume+'" />';
					return html;
						
				}},
				{ title:'${message("重量")}',name:'weight' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
					var weight = (val!="")?val:0;
					var lineWeight = weight*item.quantity;
					lineWeight = lineWeight.toFixed(6);
					lineWeight = parseFloat(lineWeight);
					var html = '<span class="weightSpan">'+lineWeight+'</span>'+
						'<input type="hidden" class="text weight" value="'+weight+'" />';
					return html;
						
				}},
				{ title:'${message("退货单编号")}', name:'b2b_returns_sn' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
					itemIndex++;
					return val;
				}},
		];
	    
	    var param = jQuery.param( $("#dataForm").serializeObject(), true);
	    ajaxSubmit('',{
			method:'post',
			url:'returnReceiveadd_list_data.jhtml',
			data: param,
			callback: function(resultMsg) {
				var data = $.parseJSON(resultMsg.content);
			    $shipping_mmGrid = $('#table-shipping').mmGrid({
			    	height:'auto',
					fullWidthRows:true,
					autoLoad:true,
					useCountText:true,
					checkCol:true,
			        cols: cols,
			        items: data,
			        callback:function(){
			        	countTotal();
			        	productOrganization();
			        }
			    });
			}
		})
		
		
		$("#openArea").bindQueryBtn({
			type:'area',
			title:'${message("查询地区")}',
			url:'/basic/area/select_area.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var $tr =$this.closest("tr");
					$(".areaId").val(rows[0].id);
					$(".areaName").val(rows[0].full_name);
					$("input[name='zipCode']").val('');
				}
			}
		});
		
		 //打开选择地址界面
	    $("#addReceiveAddress").click(function(){
	        var storeId = $(".storeId").val();
	        if(storeId==""){
	            $.message_alert('请选择客户');
	        }else{
	        	select_store_address(storeId);
	        }
	    });
	
	    //点击li
	    $(".textDis li").live("click",function(){
	        assignmentLi(this);
	    });
	
	    //更换地址
	    function select_store_address(storeId){
	     	$("#addReceiveAddress").bindQueryBtn({
	  	        type:'store',
	  	        bindClick:false,
	  	        title:'${message("更换地址")}',
	  	        url:'/member/store/select_store_address.jhtml?storeId='+storeId,
	  	        callback:function(rows){
	  	            if(rows.length>0){
	  	            	var row = rows[0];
	  	                $("input[name='consignee']").attr("value",row.consignee);
	  	                $("input[name='phone']").attr("value",row.mobile);
	  	                $("input[name='address']").attr("value",row.address);
	  	                $("input[name='zipCode']").attr("value",row.zip_code);
	  	                $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
	  	                $(".select_area").find(".fieldSet").empty();
	  	                var areaId = row.area;
	  	                if(areaId==null)areaId='';
	  	                var areaName = row.area_full_name;
	  	                if(areaName==null)areaName='';
	  	                var treePath = row.tree_path;
	  	                if(treePath==null)treePath='';
	  	                $("input[name='areaId']").val(areaId);
	  	                $("input[name='areaName']").val(areaName);
	  	                
	  	            }
	  	        }   
	  	    });
	     }
	    
	  	//点击复制
	    $("#copyData").click(function(){
		   	  //获取选择信息
		      var selectData = $shipping_mmGrid.selectedRows();
		      var idData =$shipping_mmGrid.serializeSelectedIds();
			  if(idData.length==0){
				  $.message_alert("请先选择产品或生产单！");
			      return false;
			  }else {
				//列表里复制一条数据
				  if(selectData.length>0){
		              for (var i = 0; i < selectData.length;i++) {
							var data = selectData[i];
							data.line_no++;
							$shipping_mmGrid.addRow(data,null,1);
					  }	
		              countTotal();
		              productOrganization();
				  }
			  }
	    });
	    
	    
	    [#if linkStock == 1 ] 
			//产品等级
			$(".grade").live("change", function() {
				 var $tr = $(this).closest("tr");
				 loadAttQuantity($tr); 
			 })
			 //色号
			 $(".colourNumber").live("change", function() {
				 var $tr = $(this).closest("tr");
				 loadAttQuantity($tr); 
			 })
			 //含水率
			 $(".moistureContent").live("change", function() {
				 var $tr = $(this).closest("tr");
				 loadAttQuantity($tr); 
			 })
			 //新旧标识
			 $(".newOldLogo").live("change", function() {
				 var $tr = $(this).closest("tr");
				 loadAttQuantity($tr); 
			 })
		[/#if]
	    
		//选择库位
	    $("#selectStorehouse").live("click",function(){          	
	    	getLocationInformation(this)
	    }); 
	    
		//批次
	    $("#selectBatch").live("click",function(){
	        var $this = $(this);
	        var $tr = $this.closest("tr");
	        var url = '/stock/batch/edit_post.jhtml';
	        //经营组织
	        var organizationId = $tr.find(".productOrganizationId").val();
	        if(isNull(organizationId) == null){
				$.message_alert("单据行项的经营组织不能为空");
				return false;
		  	}
	        url+='?organizationId='+organizationId;
	       	//仓库工厂
	        var productionPlantId = $("input.productionPlantId").val();
	       	if(isNull(productionPlantId) != null){
	       		url+='&productionPlantId='+productionPlantId;
	       	}
	        //批次
	        var batchIds = $tr.find(".batchIds").val();
	        if(isNull(batchIds) != null){
	        	url+='&bacthIds='+batchIds;
	        }
			var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
			var $dialog = $.dialog({
				title:'查询批次',
				width:1200,
				height:508,
				content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
				onOk: function() {
					var rows = $("#"+iframeId)[0].contentWindow.childMethod();
					var allId = '';
					var allName = '';
					for(var i=0;i<rows.length;i++){
						var row = rows[i];
						if(isNull(row) != null){
							if(isNull(row.id) != null){
								if(isNull(allId) != null){
	   								allId =allId +';'+ row.id; 
	   								allName = allName +';'+ row.batch_encoding;
	   							}else{
	   								allId = row.id; 
	   								allName = row.batch_encoding;
	   							}
							}
						}
					}   
					$tr.find(".batchIds").val(allId);
					$tr.find(".batchEncodings").val(allName);
					[#if linkStock == 1 ] 
				 		loadAttQuantity($tr); 
				 	[/#if]
				}
			});
	    });
	});


	//处理库位
	function processingLocation(e) {
	    //获取当前控件的id
	    var str = $(e).siblings("div").attr("id");
	    ajaxGetLocation(e,str);
	}

	function select_post(e,organizationId,productionPlantId,callback){
		$(e).bindQueryBtn({
			bindClick:false,
			type:'bacth',
			title:'选择批次',
			url:'/stock/batch/select_bacth.jhtml?organizationId='+organizationId+'&productionPlantId='+productionPlantId,
			callback:function(rows){
				if(rows.length>0){
					if(callback(rows)==false){
						return false;
					}
				}
			}
		});
	}

    //查看PDF
    function check_pdf(e,id){
        ajaxSubmit(e,{
            url:"/aftersales/b2b_returns/checkPdf.jhtml",
            method:"post",
            data:{ids:id},
            async: false,
            callback:function(resultMsg){
                window.open(resultMsg.content);
            }
        });
    }

	function deleteShipping(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$shipping_mmGrid.removeRow(index);
			countTotal();
		})
	}

	function sub(e){
		 var data = $("#inputForm").serializeObject();
		 var result = false;
	   	 //判断备注长度
		 var memeo=$(".memo").val();
	     var len = 0;  
	   	 for (var i=0; i<memeo.length; i++) {   
	   	  	var c = memeo.charCodeAt(i);   
	   	 	//单字节加1   
	    	if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
	    	   	len++;   
	    	}else {   
	    	  	len+=2;   
	   	 	}   
	 	 }   
	  	 var url = "returnReceivesave.jhtml";
	  	 var urlPath = "/b2b/sale_shipping_nature/returnReceiveview.jhtml?id=";
	  	 //校验色号含水率
	  	 judgmentValue2(url,urlPath);
	}

</script>
</head>
<body>
	<div class="pathh">&nbsp;新增入库单</div>
	<form id="dataForm">
		[#list ids as id] 
			<input type="hidden" name="ids" value="${id}">
		[/#list] 
	</form>
	<form id="inputForm">
		<input type="hidden" name="flag" value="0">
			<div class="tabContent">
				<table class="input input-edit">
					<tr>
						<th>${message("单据号")}:</th>
						<td></td>
                        <th>
                            ${message("来源单号")}:
                        </th>
                        <td>
                            <span id="value"> ${sourceNo} </span>
                            <a href="javascript:void(0);" onclick="check_pdf(this,${sourceNoId})" title="查看PDF" style="float: right;"><img style="width: 20px;" src="/resources/images/ic_pdf.png"></a>&nbsp;
                        </td>
						<th>${message("客户")}:</th>
						<td>
							<input name="storeId" class="storeId" type="hidden" value="${store.id}" />
							<span>${store.name}</span> 
						</td>
						<th>${message("单据状态")}:</th>
						<td></td>

					</tr>
					<tr>
                        <th>仓库:</th>
                        <td>
							<span class="search" style="position: relative">
								<input name="warehouseId" class="text warehouseId" type="hidden" value="${warehouse.id}" />
								<input class="text warehouseName" maxlength="200" type="text" value="${warehouse.name}" onkeyup="clearSelect(this)" readonly/>
								<input type="hidden" class="typesystemDictFlag" value="${warehouse.typeSystemDict.flag}" />
								<input type="hidden" class="productionPlantId" value="${warehouse.productionPlant.id}" />
								<input type="hidden" class="iconSearch" value="" id="selectWarehouse" />
							</span>
                        </td>
						<th>${message("机构")}:</th>
						<td>
							<input name="saleOrgId" type="hidden" value="${saleOrg.id}" />
							<span>${saleOrg.name}</span>
						</td>
						<th>${message("经营组织")}:</th>
						<td>
							<input type="hidden" name="organizationId" class="organizationId" value="${warehouse.managementOrganization.id}">  
							<span class="organizationName">${warehouse.managementOrganization.name}</span>  
						</td>
						<th>${message("创建时间")}:</th>
						<td></td>

					</tr>
					<tr>
                        <th>${message("Sbu")}:</th>
                        <td>
                            <input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbu.id}" />
                            <span id="sbuName">${sbu.name}</span>
                        </td>
		                <th>${message("单据类型")}:</th>
            			<td>
            				<input type="hidden" name="billTypeId" class="text billTypeId"id="billTypeId" btn-fun="clear" value="${billType.id}" />
            				<span id="value">${billType.value}</span>
            			</td>
            			<th>${message("单据类别")}:</th>
            			<td>
            				<input type="hidden" name="billCategoryId" class="text billCategoryId" id="billCategoryId" btn-fun="clear" value="${billCategory.id}" />
            				<span id="value"> ${billCategory.value} </span>
            			</td>
            			<th>${message("审核人")}:</th>
            			<td></td>
					</tr>
					<tr>
                        <th>
                            ${message("GL日期")}:
                        </th>
                        <td>
                            <input type="text" class="text" name="billDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${nowDate}"  />
                        </td>
						<th>
							${message("车号")}:
						</th>
						<td>
							<input type="text" name="wagonNumber" class="text" value="" btn-fun="clear" maxlength="200" />
						</td>
						<th>
							${message("柜号")}:
						</th>
						<td>
							<input type="text" name="containerNumber" class="text " maxlength="200" btn-fun="clear" />
						</td>
					</tr>
					<tr>
						<th>${message("备注")}:</th>
						<td colspan="7">
							<textarea class="text memo"  name="memo">${memo}</textarea>
						</td>
					</tr>
				</table>
				<div class="title-style">${message("退货项")}:
					<div class="btns"><a href="javascript:;" id="copyData" class="button">复制</a>
				</div>
				<table id="table-shipping"></table>
				<div class="title-style">${message("退货汇总")}</div>
				<table class="input input-edit">
					<tr>
						<th>${message("退货箱数")}:</th>
						<td><span id="totalBoxQuantity"></span></td>
						<th>${message("退货支数")}:</th>
						<td><span id="totalBranchQuantity"></span></td>
						<th>${message("退货数量")}:</th>
						<td><span id="totalQuantity"></span></td> 
						<th>${message("退货重量")}:</th>
						<td><span id="totalWeight"></span></td>
					</tr>
					<tr>
						<th>${message("退货体积")}:</th>
						<td><span id="totalVolume"></span></td>
					</tr>
				</table>

				<div class="title-style">${message("收货信息")}
					<div class="btns">
						<a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
					</div>
				</div>
				<table class="input input-edit">
					<tr>
						<th>收货人:</th>
						<td>
							<input type="text" class="text" name="consignee" value="${consignee}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>收货人电话:</th>
						<td>
							<input type="text" class="text" name="phone" value="${phone}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>收货地区邮编:</th>
						<td>
							<input type="text" class="text" name="zipCode" value="${zipCode}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>地址外部编码:</th>
						<td>
							<input type="text" class="text" name="addressOutTradeNo" value="${addressOutTradeNo}" btn-fun="clear" readonly="readonly" />
						</td>
					</tr>
					<tr>
						<th>收货地区:</th>
						<td colspan="3">
							<span class="search" style="position: relative"> 
								<input type="hidden" name="areaId" class="text areaId" value="${area.id}" btn-fun="clear" /> 
								<input type="text" name="areaName" class="text areaName" value="${area.fullName}" maxlength="200" onkeyup="clearSelect(this)" readonly="readonly" /> 
								[#--<input type="button" class="iconSearch" value="" id="openArea">--]
							</span>
						</td>
						<th>收货地址:</th>
						<td colspan="3">
							<input type="text" class="text" name="address" value="${address}" btn-fun="clear" readonly="readonly" />
						</td>
					</tr>
				</table>
			</div>
			<div class="fixed-top">
                <input type="hidden" id = "menuId" value="${menuId}"/>
                <input type="hidden" id="orderIndex" value="${orderIndex}"/>
                <input type="button" id="next_order" class="button "
                       value="${message("下一单")}" onclick="nextOrder('check_movelibrary_receive.jhtml?','movelibrary_receive_add.jhtml?')"/>
                <input type="button" id="submit_button" class="button sureButton" value="${message("保存")}" onclick="sub(this)">
				<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
			</div>
			<div id="loading" class="submit_loading " style="display:none">
				<div class="loading_show ">
					<img src="/resources/images/loading_icon.gif">
					<p class="loading_context ">正在提交，请稍后。。。</p>
				</div>
			</div>
	</form>
</body>
</html>