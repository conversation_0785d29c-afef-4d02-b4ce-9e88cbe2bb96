<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("移库发货查看")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<style type="text/css">
	tr.s-tr, tr.s-tr td {
		height: 10px !important;
	}
	.mefont {
		color: red;
		font-style：italic;
		斜体
		font-weight：bold;
		加粗
		font-size：30px;
		大小
		line-height：30px;
		行高
		font-family：“SimHei”;
		字体
	}	
</style>
<script type="text/javascript">
	
	//自动匹配加载添加批次列表
	function addWarehouseBatchItem(){
		//仓库
		var warehouseId = $("input.warehouseId").val();
		if(isNull(warehouseId) == null){
			return true;
	    }
		//生产工厂
		var productionPlantId = $('input.productionPlantId').val();
		$("input.productId").each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			//单据明细Id
			var itemId = $tr.find("input.itemId").val();
			
			//产品    
			var productId = $this.val();
			if(isNull(productId) == null){
				return false;
		  	}
			//等级
			var grade = $tr.find(".grade").val();
			if(isNull(grade) == null){
				return false;
			}
			//经营组织
			var organizationId = $tr.find(".productOrganization").val();
			if(isNull(organizationId) == null){
				return false;
			}
			//色号
			var colourNumber = $tr.find(".colourNumber").val();
			if(isNull(colourNumber) == null){
				colourNumber = "";
			}
			//含水率
			var moistureContent = $tr.find(".moistureContent").val();
			if(isNull(moistureContent) == null){
				moistureContent = "";
			}
			//批次
			var batchIds = $tr.find("input.batchIds").val();
			if(isNull(batchIds) == null){
				batchIds = "";
			}else{
				batchIds = batchIds.split(';');
			}
			//新旧标识
			var newOldLogo = $tr.find(".newOldLogo").val();
			if(isNull(newOldLogo) == null){
				newOldLogo = "";
			}
			//正式、测试 单据类型
			var billTypeId = $("input.billTypeId").val();
			if(billTypeId == 509) { 
				billTypeId = 507;
			}else if(billTypeId == 504){
				billTypeId = 505;
			}
			//需求数量
			var quantity = $tr.find("input.quantity").val();
			if(isNull(quantity) == null){
				quantity = 0;
			}
			var params='&warehouseId='+warehouseId+'&productionPlantId='+productionPlantId+'&productId='+productId
			   +'&organizationId='+organizationId+'&productLevelId='+grade+'&colorNumbersId='+colourNumber
			   +'&moistureContentsId='+moistureContent+'&warehouseBatchIds='+batchIds+'&newOldLogosId='+newOldLogo
			   +'&billTypeId='+billTypeId;
				params = params.substring(1,params.length);
			loadRequestBatchList(params,quantity,itemId);
		})
	}


	function loadRequestBatchList(params,quantity,itemId){
		$.ajax({
			url:'/stock/warehouse_batch_item/v_warehouse_batch_item_list_data.jhtml?'+params,
				type : "post",
				success : function(data) {
					   var demandQuantity = quantity;
		  			   var rows= $.parseJSON(data.content);
					   if(rows.length>0){
						   for (var i = 0; i < rows.length;i++) {
								  var row = rows[i];
								  row.am_shipping_item_id = itemId;
								  if(demandQuantity <= 0 ){
									   break;
								  }
								  enterQuantity(row);
								  row.quantity = row.available_quantity;
								  if(row.quantity>demandQuantity){
									  row.quantity = demandQuantity;
									  outQuantity(row);
									  $mmGridBacth.addRow(row,null,1);
									  break;
								  }else{
								      demandQuantity = accSub(demandQuantity,row.quantity);  
								      outQuantity(row);
								      $mmGridBacth.addRow(row,null,1);
								      continue;
								 } 
						   }
					   }
				}
		})
	}

	//计算入库可用数量、箱数、支数
	function enterQuantity(row){
		//箱数
		if(isNull(row.per_box) == null || row.per_box<=0){
			  row.i_available_box_quantity = 0;
		}else{
			  row.i_available_box_quantity = parseInt(accDiv(row.available_quantity,row.per_box));
		}
		//支数
		if(isNull(row.per_branch) == null || row.per_branch<=0){
			  row.i_available_branch_quantity = 0;
		}else{
			  row.i_available_branch_quantity = accDiv(row.available_quantity,row.per_branch);
		}
		return row;
	}
	
	//计算出库数量
	function outQuantity(row){
		  //箱数
		  if(isNull(row.per_box) == null || row.per_box<=0){
			  row.shipped_box_quantity = 0;
		  }else{
			  row.shipped_box_quantity = parseInt(accDiv(row.quantity,row.per_box));
		  }
		  //支数
		  if(isNull(row.per_branch) == null || row.per_branch<=0){
			  row.shipped_branch_quantity = 0;
		  }else{
			  row.shipped_branch_quantity = accDiv(row.quantity,row.per_branch);
		  }
		  //零散支数
		  if(isNull(row.shipped_branch_quantity) == null || row.shipped_branch_quantity<=0){
			  row.shipped_branch_scattered = 0;
		  }else{
			  row.shipped_branch_scattered = parseInt(row.shipped_branch_quantity%row.branch_per_box);
		  }
		  return row;
	}

	function productOrganization(){
		var typesystemDictFlag = $("input.typesystemDictFlag").val();
		if(isNull(typesystemDictFlag) != null){
			$("input.productId").each(function(){
				var $this = $(this);
				var productId = $this.val();
				var $tr = $this.closest("tr");
				if(typesystemDictFlag == 0){
					$tr.find(".productOrganization").empty();
					$tr.find(".productOrganization").append("<option value="+$("input.organizationId").val()+">"+$(".organizationName").text()+"</option>");
					[#if linkStock == 1 ] 
						loadAttQuantity($tr);
					[/#if]
				}else if(typesystemDictFlag == 1){
					[#if auditType ==0 ]
						if(isNull(productId) != null){
							$.ajax({
								url : '/product/product/findProductOrganizationList.jhtml',
				    			type : "post",
				    			data : {id:productId},
				    			success : function(data) {
				    				var content = JSON.parse(data.content);
				    				if(content.length!=0){
				    					var html = "";
				    					var organizationId = null;
				    					var productOrganizationId = $tr.find(".productOrganization option:selected").val();
				    					var productOrganizationText = $tr.find(".productOrganization option:selected").text();
				    					for(var i=0; i<content.length; i++){
				    						if(productOrganizationId !=content[i].organization){
			    								html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';
			    							}
				    					}
				    					if(isNull(productOrganizationId) != null){
											organizationId = productOrganizationId;
			    							html += '<option value="'+productOrganizationId+'">'+productOrganizationText+'</option>';
										}else{
											organizationId = '';
			    							html += '<option value="">请选择</option>';
										}
				    					$tr.find(".productOrganization").empty();
				    					$tr.find(".productOrganization").html(html);
			    						$tr.find(".productOrganization option[value='"+organizationId+"']").attr("selected",true);
				    					[#if linkStock == 1 ] 
				    						loadAttQuantity($tr);
				    					[/#if]
				    				}
				    			}
							})
						}
					[#else]
						[#if linkStock == 1 ] 
							loadAttQuantity($tr);
						[/#if]
					[/#if]
				}
			});
		}
	}
	
	//加载库存
	function loadAttQuantity($tr){
		//产品经营组织
		var productOrganizationId = $tr.find(".productOrganization").val();
		if(isNull(productOrganizationId) != null){
			//仓库
			var warehouseId = $("input.warehouseId").val();
			//产品
			var productId = $tr.find("input.productId").val();
			//等级
			var productGrade = $tr.find(".grade").val();
			//色号
			var colourNumber = $tr.find(".colourNumber").val();
			if(isNull(colourNumber) == null){
				colourNumber = "";
			}
			//含水率
			var moistureContent = $tr.find(".moistureContent").val();
			if(isNull(moistureContent) == null){
				moistureContent = "";
			}
			//批次
			var batchIds = $tr.find("input.batchIds").val();
			if(isNull(batchIds) == null){
				batchIds = "";
			}else{
				batchIds = batchIds.split(';');
			}
			//新旧标识
			var newOldLogo = $tr.find(".newOldLogo").val();
			if(isNull(newOldLogo) == null){
				newOldLogo = "";
			}
			var params='&warehouseId='+warehouseId+'&productId='+productId+'&productGrade='+productGrade
			  		+'&organizationId='+productOrganizationId+'&colourNumber='+colourNumber+'&moistureContent='+moistureContent
			  		+'&batchIds='+batchIds+'&newOldLogos='+newOldLogo;
				params = params.substring(1,params.length);
			$.ajax({
				url:'/stock/stock/findViewStock.jhtml?'+params,
	   			type : "post",
	   			success : function(rows) {
	   				var data= $.parseJSON(rows.content);
	                   if(data.length>0){
	                       for (var i = 0; i < data.length;i++) {
		                        //可用库存箱数
		                       	if(isNull(data[i].totalAttQuantity2) != null) {
		                       		$tr.find(".attQuantity2BoxNum").text(data[i].totalAttQuantity2);		                   
		                       	}else {
		                       		$tr.find(".attQuantity2BoxNum").text(0);		                       		
		                       	}		                     
		                        //可用库存支数
		                       	if(isNull(data[i].totalAttQuantity3) != null) {
		                       		$tr.find(".attQuantity3Num").text(data[i].totalAttQuantity3);
		                       	}else {
		                       		$tr.find(".attQuantity3Num").text(0);		                       		
		                       	}
		                        //可用库存数量
		                       	if(isNull(data[i].totalAttQuantity1) != null) {
		                       		$tr.find(".attQuantity1Num").text(data[i].totalAttQuantity1);
		                       	}else {
		                       		$tr.find(".attQuantity1Num").text(0);		                       		
		                       	}
	                   	}
	               	}else{
	               		$tr.find(".attQuantity2BoxNum").text(0);	
	               		$tr.find(".attQuantity3Num").text(0);	
	               		$tr.find(".attQuantity1Num").text(0);		
	               	}
	                //获取单据类型判断是否出仓 出仓则提示颜色
	                var billTypeId = $("input.billTypeId").val();			              
	                if(billTypeId == 509 || billTypeId == 504) {
                	   //获取件数值
	                   var boxQuantity = $tr.find("input.boxQuantity").val();
	                   //支数
	                   var branchQuantity = $tr.find("input.branchQuantity").val();
	                   //数量
	                   var quantity = $tr.find("input.quantity").val();		                   
	                   if(boxQuantity > $tr.find(".attQuantity2BoxNum").text()) {
	                   		$tr.addClass("backColor");
	                   }else {
	                	   $tr.removeClass("backColor");
	                   }
	                }
	   			}
			})
		}
	}
	
	//控制颜色
	function addBackColor(t) {
		//控制颜色
		var fileDir = $(t).closest("span").text();			
		if($(t).val()>fileDir) {
			$(t).parents("tr").addClass("backColor");
		}else if($(t).val()<=fileDir) {
			$(t).parents("tr").removeClass("backColor");;
		}
	}

	//作废
	function close_e(e){
		var $this = $(e);
		var	content = '您确定要作废吗？';
		$.message_confirm(content,function(){
			var url="check.jhtml";
			var data = "id="+${amShipping.id};
			ajaxSubmit($this,{
				 url: url,
				 method: "post",
				 data: data,
				 callback:function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					});
				 }
			})
		});
	}

	function check_pdf(e,id){
		ajaxSubmit(e,{
			url:"checkPdf.jhtml",
			method:"post",
			data:{ids:id},
			async: false,
			callback:function(resultMsg){
				window.open(resultMsg.content);
			}
		});
	}
	
	//生成批次PDF
	function checkBatch_pdf(e,id){
		ajaxSubmit(e,{
			url:"checkBatchPdf.jhtml",
			method:"post",
			data:{ids:id},
			async: false,
			callback:function(resultMsg){
				window.open(resultMsg.content);
			}
		});
	}


	function countTotal(){
	 	var $bInput = $("input.boxQuantity");
	 	$bInput.each(function(){
	         var quantity = 0;
	         var $tr = $(this).closest("tr");	
	         var boxQuantity=$(this).val();
	         var branchPerBox=$tr.find(".branchPerBox").val();
	         var perBox=$tr.find(".perBox").val();
	         var perBranch=$tr.find(".perBranch").val();
	         var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
	         var perBox = $tr.find(".perBox").val();//每箱单位数
	         var type = productDisc(branchPerBox,perBranch,perBox);
	      	if(type==0){
	         	var branchQuantity=accMul(boxQuantity,branchPerBox);
	         	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	         	if(isNaN(branchQuantity)){
	         		branchQuantity = 0;
	     		}
	         	$tr.find(".branchQuantity").val(branchQuantity);//支数
	         	$tr.find(".branchQuantityStr").html(branchQuantity);
	         	quantity=accMul(branchQuantity,perBranch);
	         	if(isNaN(quantity)){
	         		quantity = 0;
	     		}
	         	$tr.find(".quantity").val(quantity);//数量
	          	$tr.find(".quantityStr").html(quantity);
	         }
	      	 if(type==1){
	      		quantity = $tr.find(".quantity").val();
	      	 }
	         //辅料处理逻辑
	         if(type==2){
	        	 var a = accDiv(perBox,10);
	        	 quantity = accMul(boxQuantity,a);
	         }
	         if(type==3){
	        	 var branchQuantity = $tr.find(".branchQuantity").val();
	        	 quantity = accMul(perBranch,branchQuantity);
	         }
	         $tr.find(".quantity").val(quantity);//数量
	       	 $tr.find(".quantityStr").html(quantity);
		});
		var $input = $("input.quantity");
		var totalVolume = 0;
		var totalAmount = 0;
		var totalWeight = 0;
		var totalBoxQuantity = 0;
		var totalBranchQuantity = 0;
		var totalQuantity = 0;
		$input.each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			var quantity = Number($this.val());
			var boxQuantity = Number($tr.find("input.boxQuantity").val());
			var branchQuantity = Number($tr.find("input.branchQuantity").val());
			var volume = Number($tr.find("input.volume").val());
			var price = Number($tr.find("input.price").val());
			var weight = Number($tr.find("input.weight").val());
			//行体积=每箱体积（产品体积）* 下单箱数；
			var lineVolume = accMul(boxQuantity,volume);
			//行重量=行重量 * 数量；
			var lineWeight = accMul(quantity,weight);
			if(isNaN(lineVolume)){
				lineVolume = 0;
			}
			totalVolume = totalVolume+lineVolume;
			totalWeight = totalWeight + lineWeight;
			var lineAmount = accMul(quantity,price);
			if(isNaN(lineAmount)){
				lineAmount = 0;
			}
			totalAmount = totalAmount+lineAmount;
			totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	    	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	    	totalQuantity=accAdd(totalQuantity,quantity);
		});
		$("#totalVolume").text(totalVolume.toFixed(6));
		$("#totalWeight").text(totalWeight);
		$("#amount").text(currency(totalAmount,true));
		$("#totalBoxQuantity").text(totalBoxQuantity);
		$("#totalBranchQuantity").text(totalBranchQuantity);
		$("#totalQuantity").text(totalQuantity);
	}

	function editLineInfo(t){
		var $tr = $(t).closest("tr");
		var quantity = Number($tr.find(".quantity").val());
		var volume = Number($tr.find("input.volume").val());
		var weight = Number($tr.find("input.weight").val());
		var price = Number($tr.find("input.price").val());
		var lineVolume = accMul(quantity,volume);
		var lineWeight = accMul(quantity,weight);
		var lineAmount = accMul(quantity,price);
		$tr.find(".volumeSpan").text(lineVolume);
		$tr.find(".weightSpan").text(lineWeight);
		$tr.find(".lineAmountSpan").text(currency(lineAmount,true));
	}

	function editQty(t,e){
		if(extractNumber(t,3,false,e)){
			var $tr = $(t).closest("tr");
			var branchQuantity=$tr.find(".branchQuantity").val();
			var branchPerBox=$tr.find(".branchPerBox").val();
			var perBranch=$tr.find(".perBranch").val();
			var quantity = $tr.find(".quantity").val();
			if($(t).attr("kid")=="box"){
				var quantity=$(t).val();
				branchQuantity=accMul(quantity,branchPerBox);
				if(isNaN(branchQuantity)){
	    			branchQuantity = 0;
				}
	    		$tr.find(".branchQuantity").val(branchQuantity);//支数
	    		$tr.find(".scatteredQuantityStr").html(0);
				$tr.find(".scatteredQuantity").val(0);
			}else if($(t).attr("kid")=="branch"){
				var quantity=$(t).val();
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var box=parseInt(quantity/branchPerBox);
				var scattered=quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}if($(t).attr("kid")=="quantity"){//平方
					var $tr = $(t).closest("tr");
					var branch_quantity=0;
					var box_quantity=0;
					var quantity=$(t).val();
					var perBranch=$tr.find(".perBranch").val();  //每支单位数
					var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
					var perBox=$tr.find(".perBox").val();
					var type = productDisc(branchPerBox,perBranch,perBox);
					var scattered=0;
					if(perBranch!=0 && branchPerBox!=0){
						 branch_quantity=quantity/perBranch;
						 box_quantity=parseInt(branch_quantity/branchPerBox);
						 scattered=(branch_quantity%branchPerBox).toFixed(6);
					}
					if(type==2){
						box_quantity = modulo(quantity,perBox);
					}
					if(type==3){
						branch_quantity = modulo(quantity,perBranch);
					}
					$tr.find(".boxQuantity").val(box_quantity);
					$tr.find(".branchQuantity").val(branch_quantity);
					$tr.find(".scatteredQuantityStr").html(scattered);
					$tr.find(".scatteredQuantity").val(scattered);
					$(t).val(quantity);
			}
	    	if(isNaN(quantity)){
	    		quantity = 0;
			}
			$tr.find(".quantityStr").html(quantity);//数量
	    	$tr.find(".quantity").val(quantity);
			countTotal();
			editLineInfo(t);
			addBackColor(t);
		}
	}

	 <!-- 2019-08-05 冯旗 检测备注字数是否超过240字符-->  
	 function listenLength(){
		  var memeo=$(".memo").val();
		  var len = 0;  
		  for (var i=0; i<memeo.length; i++) {   
		     var c = memeo.charCodeAt(i);   
		     //单字节加1   
		     if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
		       len++;   
		     }else {   
		      len+=2;   
		     }   
		  }   
		  if(len>80){
		    $("#isMemo").val("字符长度已超过240!");
		  }else{
		   $("#isMemo").val("");
		  }
	 }
	 
	 
	$().ready(function(){
		
		
		$.validator.addClassRules({
			quantity: {
				required: true
			}
		});
		$("#wf_area").load("/wf/wf.jhtml?wfid=${amShipping.wfId}");
		var items = ${jsonStr};
		var itemIndex=0;
		var cols = [	 
			{ title:'${message("编号")}', name:'id' ,width:60, align:'center', renderer: function(val,item,rowIndex,obj){
				var id = item.id;
				if(isNull(id) == null){
					id = "";
				}
				return id;			
			}},
			{ title:'${message("操作")}', align:'center', width:80, renderer:function(val,item){
				var html =  '<a href="javascript:;" class="btn-delete deleteAmShipping">删除</a>';
				[#if amShipping.warehouse.enableBatch]
					if(isNull(item.id) != null){
						html+='<a href="javascript:;" class="btn-delete" onclick="matching_bacth(this)">选择批次</a>';
					}	
				[/#if]
				return html;
			}},
			{ title:'${message("产品编码")}', name:'vonder_code' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
				return '<span class="vonderCode">'+val+'</span><input type="hidden" name="amShippingItems['+itemIndex+'].product.id"  class="productId" value="'+item.product+'">';
			}},
			{ title:'${message("产品名称")}', align:'center',width:100, renderer: function(val,item,rowIndex,obj){
				var style_str = '';
				if(item.bom_flag==2){
					style_str = 'style="margin-left:25px;"';
				}
				var html = '<span '+style_str+'>';
				if(isNull(item.id) != null){
					html +='<input type="hidden" class="itemId" name="amShippingItems['+itemIndex+'].id" value="'+item.id+'">';
				} 
				html += item.name+'</span>';
				return html;
			}},
			{ title:'${message("产品描述")}', name:'description',width:200,align:'center'},
			{ title:'${message("经营组织")}',width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var html='';
				var productOrganizationId = item.product_organization_id;
				html +='<select name="amShippingItems['+itemIndex+'].productOrganization.id" class="text productOrganization">';
	 				if(productOrganizationId != null && productOrganizationId !=''){
	 					html+='<option value="'+productOrganizationId+'" selected="selected" >'+item.product_organization_name+'</option> ';
	 				}
				html+='</select>';
	    		return html;
			}},
			{ title:'${message("产品级别")}', name:'' ,width:80,isLines:true, align:'center',renderer:function(val,item){
			    var str='selected="selected"';
				var html='<select name="amShippingItems['+itemIndex+'].productLevel.id" class="text grade">';
					[#list productLevelList as products]
					if(${products.id}==item.level_Id){
						html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
					}else{
						html+='<option value="${products.id}">${products.value}</option> ';
					}
					[/#list]
					html+='</select>';
				return html;
			}},
			{ title:'${message("件数")}', name:'box_quantity',width:80, align:'center',renderer: function(val,item,rowIndex,obj){
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				var max_box=parseInt((item.oi_branch_quantity-item.ship_branch_quantity+item.branch_quantity-item.closed_quantity)/item.branch_per_box);
				if(isNaN(max_box)){
					max_box = val;
				}
				if(type==1||type==3){
					var html = '-'+
	            	'<input type="hidden" kid="box" class="t boxQuantity"   value="'+val+'"   >';
	        	return html;
				}else{
					var html = '<ul><li><div class="nums-input ov">'+
	            	//'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
	            	'<input type="text" kid="box" class="t boxQuantity"  name="amShippingItems['+itemIndex+'].boxQuantity" value="'+val+'" minData="0" maxData="'+max_box+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	            	//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
	        	'</div></li><li><span class="attQuantity2BoxNum" >0</span></li></ul>';
	        	return html;
				}
			}},
			{ title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				var branchQuantity='';
				if(obj==undefined){
					branchQuantity = val;
				}
				var branchPerBox = item.branch_per_box==null?0:item.branch_per_box;
				var perBranch = item.per_branch==null?0:item.per_branch;
				if(type==1||type==2){
					branchQuantity=0;
					var html = '-'+
	        		'<input type=hidden class="t branchPerBox" name="amShippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
					'<input type=hidden class="t perBox" value="'+item.per_box+'" />'+
					'<input type=hidden class="t perBranch" name="amShippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />';
				return html;
				}
				var html = '<ul><li><div class="nums-input ov">'+
	        		//'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
	        		'<input type="text" kid="branch" class="t branchQuantity"  name="amShippingItems['+itemIndex+'].branchQuantity" value="'+val+'" minData="0" maxData="'+(item.oi_branch_quantity-item.ship_branch_quantity+item.branch_quantity-item.closed_quantity)+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	        		//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
	        		'<input type=hidden class="t branchPerBox" name="amShippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
					'<input type=hidden class="t perBox"  value="'+item.per_box+'" />'+
					'<input type=hidden class="t 	perBranch" name="amShippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
	    		'</div></div></li><li><span class="attQuantity3Num">0</span></li></ul>';
				return html;
			}},
			{title:'${message("零散支数")}', name:'' ,align:'center', width:60, renderer: function(val,item,rowIndex, obj){
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				var branchPerBox=item.branch_per_box;
				var scatteredQuantity=item.branch_quantity%item.branch_per_box;
				if(isNaN(scatteredQuantity)){
					scatteredQuantity = 0;
				}
				if(type==1||type==2||type==3){
					return "-";
				}
				var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
					'<input type="hidden" name="amShippingItems['+itemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
				return html;
			}},
			{ title:'${message("数量")}', name:'quantity', align:'center', width:80, renderer:function(val,item,rowIndex,obj){
				var quantity='';
				if(obj==undefined){
					quantity = val;
				}else {
					quantity = 0;
				}
				quantity = quantity.toFixed(6);
				quantity = parseFloat(quantity);
				if((item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0) && item.length!='' && item.length==0 ){
					quantity=item.quantity;
					var text = 
					'<ul><li><div class="nums-input ov">'+
			        	//'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
			        	'<input type="text"  kid="quantity"  class="t quantity" name="amShippingItems['+itemIndex+'].quantity" value="'+quantity+'" maxData="'+quantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
			        	//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
			    	'</div></li><li><span class="attQuantity1Num">0</span></li></ul>';
			    	return text;
				}
				var html='<span class="quantityStr">'+quantity+'</span>'+
					'<input type="hidden" name="amShippingItems['+itemIndex+'].quantity" class="t quantity" value="'+quantity+'" />';
				return html;
			}},
			[#if hiddenBatch !=0]     			
				{ title:'${message("色号")}', align:'center',width:60,name:'colour_number',renderer: function(val,item,rowIndex, obj){
					var str='selected="selected"';
		   			var html='<select name="amShippingItems['+itemIndex+'].colorNumbers.id" class="text colourNumber">';
			   			html+='<option value="">请选择</option> ';  	
			   			[#list colorNumberList as colorNumbers]
		   				if(${colorNumbers.id} == item.color_numbers){
		   					html+='<option value="${colorNumbers.id}" '+str+' >${colorNumbers.value}</option>';
		   				}else{
		   					html+='<option value="${colorNumbers.id}">${colorNumbers.value}</option> ';
		   				}
		   				[/#list]
		   				html+='</select>';
		   			return html;
		        }},    
		        { title:'${message("含水率")}', align:'center',width:60,name:'moisture_content',renderer: function(val,item,rowIndex, obj){
		        	var str='selected="selected"';
		   			var html='<select name="amShippingItems['+itemIndex+'].moistureContents.id" class="text moistureContent">';
			   			html+='<option value="">请选择</option> ';  	
			   			[#list moistureContentList as moistureContents]
		   				if(${moistureContents.id} == item.moisture_contents){
		   					html+='<option value="${moistureContents.id}" '+str+' >${moistureContents.value}</option> ';
		   				}else{
		   					html+='<option value="${moistureContents.id}">${moistureContents.value}</option> ';
		   				}
		   				[/#list]
		   				html+='</select>';
		   			return html;
		        }},
		        { title:'${message("批次")}', align:'center', width:200, renderer: function(val,item,rowIndex, obj){
   					var batch = '';
   					if(isNull(item.batch) != null){
   						batch = item.batch;
   					}
   					var batch_encoding = '';
   					if(isNull(item.batch_encoding) != null){
   						batch_encoding = item.batch_encoding;
   					}
   		        	 var html = '<span class="search" style="position:relative">'
   		        		    +'<input type="hidden" name="amShippingItems['+itemIndex+'].batch" class="text batchIds" value="'+batch+'" />'
   		        		    +'<input type="text" name="amShippingItems['+itemIndex+'].batchEncoding" class="text batchEncodings" value="'+batch_encoding+'" readonly/>'
   							+'<input type="button" class="iconSearch" value="" id="selectBatch"/>'
   						+'</span>';
   					 return html;
   		        }},
   		    [/#if]
   		    { title:'${message("新旧标识")}', align:'center',width:60,name:'newOldLogo',renderer: function(val,item,rowIndex, obj){
	        	var str='selected="selected"';
	   			var html='<select name="amShippingItems['+itemIndex+'].newOldLogos.id" class="text newOldLogo">';
		   			//html+='<option value="">请选择</option> ';  		
		   			[#list newOldLogosList as newOldLogos]
	   				if(${newOldLogos.id} == item.new_old_logos){
	   					html+='<option value="${newOldLogos.id}" '+str+' >${newOldLogos.value}</option> ';
	   				}else{
	   					html+='<option value="${newOldLogos.id}">${newOldLogos.value}</option> ';
	   				}
	   				[/#list]
	   				html+='</select>';
	   			return html;
	        }},
			{ title:'${message("备注")}', name:'memo', align:'center',width:100, renderer: function(val,item,rowIndex){
					var memos='';
					if(obj==undefined){
						memos = item.memo;
					}
					var html=
						'<input type="text" name="amShippingItems['+itemIndex+'].memo" [#if amShipping.status!=0]readonly[/#if] class="text" value="'+val+'" />';
					return html;
			}},
			{ title:'${message("产品型号")}', name:'model', align:'center',width:80 },
			{ title:'${message("单位")}',width:80, align:'center',name:'unit'},
			{ title:'${message("体积")}',[#if sbuIds ==3 ]hidden:true,[/#if] name:'volume', align:'center',width:80, renderer: function(val,item,rowIndex){
				var volume = (val!="")?val:0;
				var lineVolume = volume*item.box_quantity;
				lineVolume = lineVolume.toFixed(6);
				lineVolume = parseFloat(lineVolume);
				var html = '<span class="volumeSpan">'+lineVolume+'</span>'+
					'<input type="hidden" class="volume text" name="amShippingItems['+itemIndex+'].volume" value="'+volume+'" />';
				return html;
			}},
			{ title:'${message("重量")}',[#if sbuIds ==3 ]hidden:true,[/#if] name:'weight' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var weight = (val!="")?val:0;
				var lineWeight = weight*item.quantity;
				lineWeight = lineWeight.toFixed(6);
				lineWeight = parseFloat(lineWeight);
				var html = '<span class="weightSpan">'+lineWeight+'</span>'+
					'<input type="hidden" class="weight text" name="amShippingItems['+itemIndex+'].weight" value="'+weight+'" />';
				return html;
					
			}},
			{ title:'${message("来源单据号")}', name:'movelibrary_issue_sn', align:'center',width:120, renderer: function(val,item,rowIndex){
				var html = val;
    			if(isNull(item.move_library_item_issue) != null){
    			 html += '<input type="hidden"  name="amShippingItems['+itemIndex+'].movelibraryIssue.id" value="'+item.movelibrary_issue+'">';
    			 html += '<input type="hidden"  name="amShippingItems['+itemIndex+'].moveLibraryItemIssue.id" value="'+item.move_library_item_issue+'">';
    			}
    			itemIndex++;
    			return html;
			}},
		];
		
		$mmGrid = $('#table-m1').mmGrid({
			height:'auto',
	        cols: cols,
	        items:items,
	        fullWidthRows:true,
	        checkByClickTd:true,
			rowCursorPointer:true,
	        checkCol: true,
	        autoLoad: true,
	        onSelectRow:function(row,e,val){
	        	if(val){
	        		//经营组织
	        		row.product_organization_name = $(e).find('.productOrganization option:selected').text();
	        		row.product_organization_id = $(e).find('.productOrganization option:selected').val();
	        		//等级
	        		row.level_Id = $(e).find('.grade option:selected').val();
	        		//色号
	        		row.color_numbers = $(e).find('.colourNumber option:selected').val();
	        		//含水率
	        		row.moisture_contents = $(e).find('.moistureContent option:selected').val();
	        		//批次
	        		row.batch = $(e).find("input.batchIds").val();
	        		row.batch_encoding = $(e).find("input.batchEncodings").val();
	        		//新旧标识
	        		row.new_old_logos = $(e).find('.newOldLogo option:selected').val();
	        		//备注
	        		row.memo = $(e).find("input.memo").val();
	        	}
	        },
	        onSelectAll:function(rows,e,val){
	        	$(e).each(function(index){
	        			var row = rows[index];
	        			if(row){
	        				var $tr = $(this);
	        				//经营组织
	                		row.product_organization_name = $tr.find('.productOrganization option:selected').text();
	                		row.product_organization_id = $tr.find('.productOrganization option:selected').val();
	                		//等级
	                		row.level_Id = $tr.find('.grade option:selected').val();
	                		//色号
	                		row.color_numbers = $tr.find('.colourNumber option:selected').val();
	                		//含水率
	                		row.moisture_contents = $tr.find('.moistureContent option:selected').val();
	                		//批次
	                		row.batch = $tr.find("input.batchIds").val();
	                		row.batch_encoding = $tr.find("input.batchEncodings").val();
	                		//新旧标识
	                		row.new_old_logos = $tr.find('.newOldLogo option:selected').val();
	                		//备注
	                		row.memo = $tr.find("input.memo").val();
	        			}
	        	})
	        },
	        callback:function(){
	        	countTotal();
	        	productOrganization();	
	        	[#if warehouseBatchItemIndex == 0 ] 	
	        		[#if amShipping.warehouse.enableBatch && amShipping.warehouse.autoMatchInventory]
	        			addWarehouseBatchItem();
	        		[/#if]
				[/#if]   
	        }
	    });
		
	    var orderFullLink_items = ${fullLink_json};
		var cols = [				
	    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
			{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
			{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
		];
		$('#table-full').mmGrid({
			fullWidthRows:true,
			height:'auto',
	        cols: cols,
	        items:orderFullLink_items,
	        checkCol: false,
	        autoLoad: true
	    });
	    
		
		$("form").bindAttribute({
			isConfirm:true,
		    callback: function(resultMsg){
		        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
		    }
		 });
		
		[#if linkStock == 1 ] 
				//产品经营组织
				 $(".productOrganization").live("change", function() {
					 var $tr = $(this).closest("tr");
					 //批次
					 $tr.find(".batchIds").val('');
					 $tr.find(".batchEncodings").val('');
					 loadAttQuantity($tr); 
					 var productOrganization = $(this).val();
					 if(isNull(productOrganization) != null){
						 //行Id
						 var Id = $tr.find("input.itemId").val();
						 clearWarehouseBatchItem(Id);
					 }
				 })
				//产品等级
				$(".grade").live("change", function() {
					 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr); 	
					 var grade = $(this).val();
					 if(isNull(grade) != null){
						 //行Id
						 var Id = $tr.find("input.itemId").val();
						 clearWarehouseBatchItem(Id);
					 }
				 })
				 //色号
				 $(".colourNumber").live("change", function() {
					 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr); 
					 var colourNumber = $(this).val();
					 if(isNull(colourNumber) != null){
						 //行Id
						 var Id = $tr.find("input.itemId").val();
						 clearWarehouseBatchItem(Id);
					 }
				 })
				 //含水率
				 $(".moistureContent").live("change", function() {
					 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr); 
					 var moistureContent = $(this).val();
					 if(isNull(moistureContent) != null){
						 //行Id
						 var Id = $tr.find("input.itemId").val();
						 clearWarehouseBatchItem(Id);
					 }
				 })
				 //新旧标识
				 $(".newOldLogo").live("change", function() {
					 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr); 
					 var newOldLogo = $(this).val();
					 if(isNull(newOldLogo) != null){
						 //行Id
						 var Id = $tr.find("input.itemId").val();
						 clearWarehouseBatchItem(Id);
					 }
				 })
		[/#if]
		[#if amShipping.warehouse.enableBatch]
				var wBItem_line_no = 1;	
				var wBItemIndex = 0;
				var vWarehouseBatchItemList = null;
				[#if warehouseBatchItemIndex > 0 ] 	
					vWarehouseBatchItemList = ${vWarehouseBatchItemList};
				[/#if]
				var cols = [				    					   												
							{ title:'${message("序号")}', align:'center',width:60 ,renderer: function(val,item,rowIndex){
	    						html = rowIndex+1+'<input type="hidden" name="batchItem_lineNo"  value="'+rowIndex+1+'" />';
	    						return html;
	    					}},
	    		    		{ title:'${message("明细编号")}', align:'center', name:'line_no', width:60 ,renderer: function(val,item,rowIndex,obj){
	    		    			var amShippingItemId = item.am_shipping_item_id;
	    		    			if(obj==undefined){
	    		    				amShippingItemId = item.am_shipping_item;
	    		    			}
	    		    			var html = '<span>'+amShippingItemId+'</span>';
	    		    				html += '<input type="hidden" name="WarehouseBatchItems['+wBItemIndex+'].amShippingItem.id" class="text amShippingItemId" value="'+amShippingItemId+'" />'
	    		    				html += '<input type="hidden" name="WarehouseBatchItems['+wBItemIndex+'].outAmShippingItem.id"  value="'+amShippingItemId+'" />'
	    		    			 return html;
	    		    		}},
	    		    		{ title:'${message("来源单号")}', name:'am_shipping_sn' ,align:'center',width:80,renderer: function(val,item,rowIndex){
	    		    			var document_no = "";
	    		    			if(isNull(item.document_no)!=null) {
	    		    				document_no = item.document_no;
	    		    			}else if(isNull(item.am_shipping_sn)!=null){
	    		    				document_no = item.am_shipping_sn;
	    		    			}
	    		    			var warehousing_id = item.warehousing_id;
	    		    			if(isNull(warehousing_id)==null){
	    		    				warehousing_id = item.id;
	    		    			}
	    		    			var html = '<span class="am_shipping_sn">'+document_no+'</span>';
	    		    			html += '<input type="hidden" name="WarehouseBatchItems['+wBItemIndex+'].warehousingId"  value="'+warehousing_id+'" />';
	    		    			html += '<input type="hidden" name="WarehouseBatchItems['+wBItemIndex+'].documentNo"  value="'+document_no+'" />';
	    		    			return html;		
	    		    		}},
	    		    		{ title:'${message("批次编码")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
	    		    			var amShippingItemId = item.id;
	    		    			if(obj==undefined){
	    		    				amShippingItemId = item.am_shipping_item;
	    		    			}
	    		    			if(isNull(item.warehouse_batch) != null){
	    				        	return '<span class="search" style="position:relative">'
	    				        			+'<input type="hidden" name="WarehouseBatchItems['+wBItemIndex+'].warehouseBatch.id" class="text batchId batchId_'+amShippingItemId+'_'+item.batch_id+'" value="'+item.warehouse_batch+'" />'
	    				        			+item.batch_encoding+'</span>'
	    			        	}
	    			        }},
	    			        { title:'${message("作业单号")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
				        	    var warehouse_batch_sn = item.warehouse_batch_sn;
				        	    if(isNull(item.warehouse_batch_sn) == null){
				        	    	warehouse_batch_sn = '';
				        	    }
				        		var html = '<span class="warehouseBatchSn" >'+warehouse_batch_sn+'</span>';
				        		return html;
					        }},
					        { title:'${message("经营组织")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
				        	    var organization_name = item.organization_name;
				        	    if(isNull(item.organization_name) == null){
				        	    	organization_name = '';
				        	    }
				        		var html = '<span class="organizationName" >'+organization_name+'</span>';
				        		return html;
				        	}},
	    		        	{ title:'${message("件数")}', name:'box_quantity',width:80, align:'center',renderer: function(val,item,rowIndex,obj){
	    			   			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
	    			   			var max_box=parseInt((item.oi_branch_quantity-item.ship_branch_quantity+item.branch_quantity-item.closed_quantity)/item.branch_per_box);
	    			   			if(isNaN(max_box)){
	    			   				max_box = val;
	    			   			}
	    			   			var vals = 0;
	    			   			if(isNull(item.shipped_box_quantity) != null) {
	    			   				 vals = item.shipped_box_quantity
	    			   			}
	    			   			var iAvailableBoxQuantity = 0;
	    			   			if(item.i_available_box_quantity >= vals){
	    			   				iAvailableBoxQuantity = item.i_available_box_quantity;
	    			   			}else{
	    			   				iAvailableBoxQuantity = vals;
	    			   			}
	    			   			if(type==1||type==3){
	    			   				var html = '-'+
	    			               	'<input type="hidden" kid="box" class="t boxQuantityM"   value="'+vals+'"   >';
	    			           		return html;
	    			   			}else{
	    			   				var html = '<div class="nums-input ov">'+
	    			   						'<input type="text" kid="box" class="t boxQuantityM"  name="WarehouseBatchItems['+wBItemIndex+'].shippedBoxQuantity" value="'+vals+'" minData="0" maxData="'+iAvailableBoxQuantity+'" oninput="editQty2(this,event)" onpropertychange="editQty2(this,event)" >'+
	    			               	'</div>';
	    			           		return html;
	    			   			}   			
	    		   			}},
	    			   		{ title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
	    			   			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
	    			   			var branchQuantity='';
	    			   			if(obj==undefined){
	    			   				branchQuantity = val;
	    			   			}
	    			   			var branchPerBox = item.branch_per_box==null?0:item.branch_per_box;
	    			   			var perBranch = item.per_branch==null?0:item.per_branch;
	    			   			if(type==1||type==2){
	    			   				branchQuantity=0;
	    			   				var html = '-'+
	    			           		'<input type=hidden class="t branchPerBoxM" name="WarehouseBatchItems['+wBItemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
	    			   				'<input type=hidden class="t perBoxM" value="'+item.per_box+'" />'+
	    			   				'<input type=hidden class="t perBranchM" name="WarehouseBatchItems['+wBItemIndex+'].perBranch" value="'+perBranch+'" />';
	    			   			return html;
	    			   			}
	    			   			var vals = 0;
	    			   			if(isNull(item.shipped_branch_quantity)!=null) {
	    			   				 vals = item.shipped_branch_quantity
	    			   			}
	    			   			var iAvailableBranchQuantity = 0;
	    			   			if(item.i_available_branch_quantity >= vals){
	    			   				iAvailableBranchQuantity = item.i_available_branch_quantity;
	    			   			}else{
	    			   				iAvailableBranchQuantity = vals;
	    			   			}
	    			   			var html = '<div class="nums-input ov">'+
	    			           		'<input type="text" kid="branch" class="t branchQuantityM"  name="WarehouseBatchItems['+wBItemIndex+'].shippedBranchQuantity" value="'+vals+'" minData="0" maxData="'+iAvailableBranchQuantity+'" oninput="editQty2(this,event)" onpropertychange="editQty2(this,event)" >'+
	    			           		'<input type=hidden class="t branchPerBoxM" name="WarehouseBatchItems['+wBItemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
	    			   				'<input type=hidden class="t perBoxM"  value="'+item.per_box+'" />'+
	    			   				'<input type=hidden class="t 	perBranchM" name="WarehouseBatchItems['+wBItemIndex+'].perBranch" value="'+perBranch+'" />'+
	    			       		'</div>';
	    			   			return html;
	    			   		}},
	    			   		{title:'${message("零散支数")}', name:'branch_scattered' ,align:'center', width:60, renderer: function(val,item,rowIndex, obj){
	    			   			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
	    			   			var branchPerBox=item.branch_per_box;
	    			   			var scatteredQuantity=item.branch_quantity%item.branch_per_box;
	    			   			if(isNaN(scatteredQuantity)){
	    			   				scatteredQuantity = 0;
	    			   			}
	    			   			if(type==1||type==2||type==3){
	    			   				return "-";
	    			   			}
	    			   			if(isNull(item.shipped_branch_scattered)!=null) {
	    			   				scatteredQuantity = item.shipped_branch_scattered;
	    			   			}
	    			   			var html='<span class="scatteredQuantityStrM">'+scatteredQuantity+'</span>'+
	    			   				'<input type="hidden" name="WarehouseBatchItems['+wBItemIndex+'].shippedBranchScattered" class="scatteredQuantityM text" value="'+scatteredQuantity+'" />';
	    			   			return html;
	    			   		}},	
	    			   		{ title:'${message("出库数量")}', align:'center',name:'quantity',width:80,renderer: function(val,item,rowIndex, obj){	
	    						var quantity='';
	    						if(obj==undefined){
	    							quantity = val;
	    						}else {
	    							quantity = 0;
	    						}
	    						if (val == '' || val == null ) {
	    							quantity = 0;
	    						}
	    						quantity = quantity.toFixed(6);
	    						quantity = parseFloat(quantity);
	    						if(isNull(item.quantity)!=null) {
	    							quantity = item.quantity;
	    						}
	    						if((item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0) && item.length!='' && item.length==0 ){
	    							quantity=item.quantity;
	    							var text = 
	    							'<div class="nums-input ov">'+
	    					        	'<input type="text"  kid="quantity"  class="t quantityM" name="WarehouseBatchItems['+wBItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
	    					    	'</div>';
	    					    	return text;
	    						}
	    						var html='<span class="quantityStrM">'+quantity+'</span>'+
	    							'<input type="hidden" name="WarehouseBatchItems['+wBItemIndex+'].quantity" class="t quantityM" value="'+quantity+'" minData="0"/>';
	    						return html;
	    			        }},
	    			        { title:'${message("可用件数")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
				        	    var i_available_box_quantity = item.i_available_box_quantity;
				        	    if(isNull(i_available_box_quantity) == null){
				        	    	i_available_box_quantity = 0;
				        	    }
				        		var html = '<span class="iAvailableBoxQuantity" >'+i_available_box_quantity+'</span>';
				        		return html;
					        }},
					        { title:'${message("可用支数")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
				        	    var i_available_branch_quantity = item.i_available_branch_quantity;
				        	    if(isNull(item.i_available_branch_quantity) == null){
				        	    	i_available_branch_quantity = 0;
				        	    }
				        		var html = '<span class="iAvailableBranchQuantity" >'+i_available_branch_quantity+'</span>';
				        		return html;
				        	}},
				        	{ title:'${message("可用数量")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
	    			        	var availableQuantity = item.available_quantity;
	    						if(obj==undefined){
	    							availableQuantity = item.i_available_quantity;
	    		    			}
	    						if(isNull(availableQuantity)==null){
	    							availableQuantity = 0;
	    						}
	    						return availableQuantity;
	    			        }},
	    			        { title:'${message("入库数量")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
	    			        	var enterQuantity = item.enter_quantity;
	    						if(obj==undefined){
	    							enterQuantity = item.i_enter_quantity;
	    		    			}
	    						if(isNull(enterQuantity)==null){
	    							enterQuantity = 0;
	    						}
	    						return enterQuantity;
	    			        }},
	    			        { title:'${message("占用数量")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
	    			        	var occupyQuantity = item.occupy_quantity;
	    						if(obj==undefined){
	    							occupyQuantity = item.i_occupy_quantity;
	    		    			}
	    						if(isNull(occupyQuantity)==null){
	    							occupyQuantity = 0;
	    						}
	    						return occupyQuantity;
	    			        }},	
	    			        { title:'${message("色号")}', align:'center',width:80,renderer: function(val,item,rowIndex, obj){				        	
	    			   			var html='<select name="WarehouseBatchItems['+wBItemIndex+'].colorNumbers.id" class="text wColorNumbers">';
	    			   				if(isNull(item.color_numbers) != null) {
	    			   					html+='<option value="'+item.color_numbers+'" selected="selected" >'+item.color_numbers_name+'</option>';
	    			   				}else{
	    			   					html+='<option value="">请选择</option> ';
	    			   					[#list colorNumberList as colorNumbers]
	    				   					html+='<option value="${colorNumbers.id}">${colorNumbers.value}</option> ';
	    				   				[/#list]
	    			   				}
	    			   				html+='</select>';
	    			   			return html;				        								
	    		        	}},
	    		        	{ title:'${message("含水率")}', align:'center',width:80,renderer: function(val,item,rowIndex, obj){
	    				   			var html='<select name="WarehouseBatchItems['+wBItemIndex+'].moistureContents.id" class="text wMoistureContents">';
	    				   			if(isNull(item.moisture_contents) != null) {
	    				   				var name = "";
	    				   				if(isNull(item.moisture_contents_name)!=null) {
	    				   					name = item.moisture_contents_name;
	    				   				}else {
	    				   					name = item.moisture_content_name;
	    				   				}
	    				   				html+='<option value="'+item.moisture_contents+'" selected="selected" >'+name+'</option>';
	    				   			}else{
	    				   				html+='<option value="">请选择</option> ';
	    				   				[#list moistureContentList as moistureContents]
	    					   					html+='<option value="${moistureContents.id}">${moistureContents.value}</option> ';
	    				   				[/#list]
	    				   			}
	    			   				html+='</select>';
	    			   			return html;	
	    		        	}},
	    		        	{ title:'${message("新旧标识")}', align:'center',width:80,renderer: function(val,item,rowIndex, obj){	
	    				   			var html='<select name="WarehouseBatchItems['+wBItemIndex+'].newOldLogos.id" class="text wNewOldLogos">';
	    				   			if(isNull(item.new_old_logos) != null) {
	    				   				html+='<option value="'+item.new_old_logos+'" selected="selected" >'+item.new_old_logos_name+'</option>';
	    				   			}else{
	    				   				html+='<option value="">请选择</option> ';
	    				   				[#list newOldLogosList as newOldLogos]
	    				   					html+='<option value="${newOldLogos.id}">${newOldLogos.value}</option> ';
	    				   				[/#list]
	    				   			}
	    				   			html+='</select>';
	    			   			return html;				        					        	
	    		            }},
	    		            { title:'${message("生产完成日期")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		    		        	    var completion_date = item.completion_date;
		    		        	    if(isNull(completion_date) == null){
		    		        	    	completion_date = '';
		    		        	    }
		    		        		var html = '<span class="completionDate" >'+completion_date+'</span>';
		    		        		return html;
		    		         }},
		    		         { title:'${message("备注")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
		    		        	    var memo = item.memo;
		    		        	    if(isNull(memo) == null){
		    		        	    	memo = '';
		    		        	    }
		    		        		var html = '<span class="memo" >'+memo+'</span>';
		    		        		return html;
		    		        }},
							{ title:'操作', name:'', align:'center',renderer: function(val,item,rowIndex){
								   wBItem_line_no++;
								   wBItemIndex++;
								   return '<a href="javascript:;" class="btn-delete deleteBacth" >删除</a>'
							}}
			    	];		
				
				$mmGridBacth = $('#table-bacth').mmGrid({
					height:'auto',
			        cols: cols,
			        items:vWarehouseBatchItemList,
			        fullWidthRows:true,
			        checkCol: false,
			        autoLoad: true,
			        callback:function(){
					}
			    });
		[/#if]		
				//删除批次列表批次
				$("a.deleteBacth").live("click", function() {
				     var index = $(this).closest("tr").index();
				     $.message_confirm('您确定要删除吗？',function(){
				    	 $mmGridBacth.removeRow(index);
//				         var line_number = 1;
//				         $("span.line_no").each(function(){
//				             $(this).html(line_number++);
//				         });
//				         wBItem_line_no--;
				     })
				});
				
				//删除单据行项
				$("a.deleteAmShipping").live("click", function() {
					var index = $(this).closest("tr").index();
					var $tr = $(this).closest("tr");
					//行Id
					var Id = $tr.find("input.itemId").val();
					$.message_confirm('您确定要删除吗？',function(){
						$mmGrid.removeRow(index);
						clearWarehouseBatchItem(Id);
					}) 
				});

		
		
				//匹配批次明细
		    	$("#matchingBacth").click(function(){
		    		//正式、测试 单据类型
		    		var billTypeId = $("input.billTypeId").val();
		    		if(billTypeId == 509) { 
		    			billTypeId = 507;
		    		}else if(billTypeId == 504){
		    			billTypeId = 505;
		    		}
		    		if(isNull(billTypeId) == null){
		    			$.message_alert("单据类型不能为空");
	    		      	return false;
		    		}
		    		//仓库
		    		var warehouseId = $("input.warehouseId").val();
		    		if(isNull(warehouseId) == null){
		    			$.message_alert("仓库不能为空");
	    		      	return false;
		    		}
		    		//生产工厂
		    		var productionPlantId = $('input.productionPlantId').val();
		    		var selectData = $mmGrid.selectedRows();
		    		var idData =$mmGrid.serializeSelectedIds();
		    		if(idData.length==0){
	    			  	$.message_alert("请先选择单据行项");
	    		      	return false;
		    		}else {			    			
		    			  if(selectData.length>0){
		    		          for (var i = 0; i < selectData.length;i++) {
		    						var data = selectData[i];
		    						clearWarehouseBatchItem(data.id);				
		    				  }
		    		          for (var i = 0; i < selectData.length;i++) {
		    		        	var data = selectData[i];
		    		        	var params= '';
		    		        	params+='&billTypeId='+billTypeId+'&warehouseId='+warehouseId+'&productionPlantId='+productionPlantId;
		    		        	//单据明细Id
		    		        	var itemId = data.id;
		    		        	//产品    
		    		        	var productId = data.product;
		    		        	if(isNull(productId) == null){
		    		        		 continue;
		    		        	}
		    		        	params+='&productId='+productId;
		    		        	//等级
		    		        	var grade = data.level_Id;
		    		        	if(isNull(grade) == null){
		    		        		 continue;
		    		        	}
		    		        	params+='&productLevelId='+grade;
		    		        	//经营组织
		    		        	var organizationId = data.product_organization_id;
		    		        	if(isNull(organizationId) == null){
		    		        		 continue;
		    		        	}
		    		        	params+='&organizationId='+organizationId;
		    		        	//色号
		    		        	var colourNumber = data.color_numbers;
		    		        	if(isNull(colourNumber) != null){
		    		        		params+='&colorNumbersId='+colourNumber;
		    		        	}
		    		        	//含水率
		    		        	var moistureContent = data.moisture_contents;
		    		        	if(isNull(moistureContent) != null){
		    		        		params+='&moistureContentsId='+moistureContent;
		    		        	}
		    		        	//批次
		    		        	var batchIds = data.batch;
		    		        	if(isNull(batchIds) != null){
		    		        		batchIds = batchIds.split(';');
		    		        		params+='&warehouseBatchIds='+batchIds;
		    		    		}
		    		        	//新旧标识
		    		        	var newOldLogo = data.new_old_logos;
		    		        	if(isNull(newOldLogo) != null){
		    		        		params+='&newOldLogosId='+newOldLogo;
		    		    		}
		    		        	params = params.substring(1,params.length);
		    		        	//需求数量
		    		        	var quantity = data.quantity;
		    		        	if(isNull(quantity) == null){
		    		        		quantity = 0;
		    		        	}
		    		        	loadRequestBatchList(params,quantity,itemId); 
		    		          }
		    			  }
		    		  }	 														
				});
							
				
				//批次
	            $("#selectBatch").live("click",function(){
	                var $this = $(this);
	                var $tr = $this.closest("tr");
	                var url = '/stock/batch/edit_post.jhtml';
	                //经营组织
	                var organizationId = $tr.find(".productOrganization").val();
	                if(isNull(organizationId) == null){
	        			$.message_alert("单据行项的经营组织不能为空");
	        			return false;
	        	  	}
	                url+='?organizationId='+organizationId;
	               	//仓库工厂
	                var productionPlantId = $("input.productionPlantId").val();
	               	if(isNull(productionPlantId) != null){
	               		url+='&productionPlantId='+productionPlantId;
	               	}
	                //批次
	                var batchIds = $tr.find(".batchIds").val();
	                if(isNull(batchIds) != null){
	                	url+='&bacthIds='+batchIds;
	                }
	        		var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	        		var $dialog = $.dialog({
	       				title:'查询批次',
	       				width:1200,
	       				height:508,
	       				content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
	       				onOk: function() {
	       					var rows = $("#"+iframeId)[0].contentWindow.childMethod();
	       					var allId = '';
	       					var allName = '';
	       					for(var i=0;i<rows.length;i++){
	       						var row = rows[i];
	       						if(isNull(row) != null){
	       							if(isNull(row.id) != null){
	       								if(isNull(allId) != null){
	           								allId =allId +';'+ row.id; 
	           								allName = allName +';'+ row.batch_encoding;
	           							}else{
	           								allId = row.id; 
	           								allName = row.batch_encoding;
	           							}
	       							}
	       						}
	       					}   
	       					$tr.find(".batchIds").val(allId);
	       					$tr.find(".batchEncodings").val(allName);
	       					[#if linkStock == 1 ] 
		    			 		loadAttQuantity($tr); 
		    			 	[/#if]
	       				}
	       			});
	            })	          
	            
	          //点击复制
	   	     $("#copyData").click(function(){
	   		   	 //获取选择信息
	   		      var selectData = $mmGrid.selectedRows();
	   		      var idData = $mmGrid.serializeSelectedIds();
	   			  if(idData.length==0){
	   				  $.message_alert("请先选择退货项");
	   			      return false;
	   			  }else {
	   				  //列表里复制一条数据
	   				  if(selectData.length>0){
	   		              for (var i = 0; i < selectData.length;i++) {
	   							var data = selectData[i];
	   							data.id = null;
	   							$mmGrid.addRow(data,null,1);
	   					  }	
	   		              countTotal();
	   			          productOrganization();
	   				  }
	   				  $("#addBacth").css('display', 'none');   
	   			  }
	   	    });
	});
	
			//批次列表计算逻辑
			function editQty2(t,e){
				if(extractNumber(t,3,false,e)){
					var $tr = $(t).closest("tr");
					var branchQuantity=$tr.find(".branchQuantityM").val();
					var branchPerBox=$tr.find(".branchPerBoxM").val();
					var perBranch=$tr.find(".perBranchM").val();
					var quantity = $tr.find(".quantityM").val();
					if($(t).attr("kid")=="box"){
						var quantity=$(t).val();
						branchQuantity=accMul(quantity,branchPerBox);
						if(isNaN(branchQuantity)){
			    			branchQuantity = 0;
						}
			    		$tr.find(".branchQuantityM").val(branchQuantity);//支数
			    		$tr.find(".scatteredQuantityStrM").html(0);
						$tr.find(".scatteredQuantityM").val(0);
					}else if($(t).attr("kid")=="branch"){
						var quantity=$(t).val();
						var branchPerBox=$tr.find(".branchPerBoxM").val();  //每箱支数
						var box=parseInt(quantity/branchPerBox);
						var scattered=quantity%branchPerBox;
						$tr.find(".boxQuantityM").val(box);
						$tr.find(".scatteredQuantityStrM").html(scattered);
						$tr.find(".scatteredQuantityM").val(scattered);
					}if($(t).attr("kid")=="quantity"){//平方
							var $tr = $(t).closest("tr");
							var branch_quantity=0;
							var box_quantity=0;
							var quantity=$(t).val();
							var perBranch=$tr.find(".perBranchM").val();  //每支单位数
							var branchPerBox=$tr.find(".branchPerBoxM").val();  //每箱支数
							var perBox=$tr.find(".perBoxM").val();
							var type = productDisc(branchPerBox,perBranch,perBox);
							var scattered=0;
							if(perBranch!=0 && branchPerBox!=0){
								 branch_quantity=quantity/perBranch;
								 box_quantity=parseInt(branch_quantity/branchPerBox);
								 scattered=(branch_quantity%branchPerBox).toFixed(6);
							}
							if(type==2){
								box_quantity = modulo(quantity,perBox);
							}
							if(type==3){
								branch_quantity = modulo(quantity,perBranch);
							}
							$tr.find(".boxQuantityM").val(box_quantity);
							$tr.find(".branchQuantityM").val(branch_quantity);
							$tr.find(".scatteredQuantityStrM").html(scattered);
							$tr.find(".scatteredQuantityM").val(scattered);
							$(t).val(quantity);
					}
			    	if(isNaN(quantity)){
			    		quantity = 0;
					}
					$tr.find(".quantityStrM").html(quantity);//数量
			    	$tr.find(".quantityM").val(quantity);
			    	countTotal2();			
				}
			}
			
			function countTotal2(){		
				var $bInput = $("input.boxQuantityM");
			 	$bInput.each(function(){
			         var quantity = 0;
			         var $tr = $(this).closest("tr");	
			         var boxQuantity=$(this).val();
			         var branchPerBox=$tr.find(".branchPerBoxM").val();
			         var perBox=$tr.find(".perBoxM").val();
			         var perBranch=$tr.find(".perBranchM").val();
			         var scatteredQuantity=$tr.find(".scatteredQuantityM").val();//零散支数
			         var perBox = $tr.find(".perBoxM").val();//每箱单位数
			         var type = productDisc(branchPerBox,perBranch,perBox);
			      	if(type==0){
			         	var branchQuantity=accMul(boxQuantity,branchPerBox);
			         	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
			         	if(isNaN(branchQuantity)){
			         		branchQuantity = 0;
			     		}
			         	$tr.find(".branchQuantityM").val(branchQuantity);//支数
			         	$tr.find(".branchQuantityStrM").html(branchQuantity);
			         	quantity=accMul(branchQuantity,perBranch);
			         	if(isNaN(quantity)){
			         		quantity = 0;
			     		}
			         	$tr.find(".quantityM").val(quantity);//数量
			          	$tr.find(".quantityStrM").html(quantity);
			         }
			      	 if(type==1){
			      		quantity = $tr.find(".quantityM").val();
			      	 }
			         //辅料处理逻辑
			         if(type==2){
			        	 var a = accDiv(perBox,10);
			        	 quantity = accMul(boxQuantity,a);
			         }
			         if(type==3){
			        	 var branchQuantity = $tr.find(".branchQuantityM").val();
			        	 quantity = accMul(perBranch,branchQuantity);
			         }
			         $tr.find(".quantityM").val(quantity);//数量
			       	 $tr.find(".quantityStrM").html(quantity);
				});
				var $input = $("input.quantityM");
				var totalVolume = 0;
				var totalAmount = 0;
				var totalWeight = 0;
				var totalBoxQuantity = 0;
				var totalBranchQuantity = 0;
				var totalQuantity = 0;
				$input.each(function(){
					var $this = $(this);
					var $tr = $this.closest("tr");
					var quantity = Number($this.val());
					var boxQuantity = Number($tr.find("input.boxQuantityM").val());
					var branchQuantity = Number($tr.find("input.branchQuantityM").val());
					var volume = Number($tr.find("input.volume").val());
					var price = Number($tr.find("input.price").val());
					var weight = Number($tr.find("input.weight").val());
					//行体积=每箱体积（产品体积）* 下单箱数；
					var lineVolume = accMul(boxQuantity,volume);
					//行重量=行重量 * 数量；
					var lineWeight = accMul(quantity,weight);
					if(isNaN(lineVolume)){
						lineVolume = 0;
					}
					totalVolume = totalVolume+lineVolume;
					totalWeight = totalWeight + lineWeight;
					var lineAmount = accMul(quantity,price);
					if(isNaN(lineAmount)){
						lineAmount = 0;
					}
					totalAmount = totalAmount+lineAmount;
					totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
			    	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
			    	totalQuantity=accAdd(totalQuantity,quantity);
				});
//				$("#totalVolume").text(totalVolume.toFixed(6));
//				$("#totalWeight").text(totalWeight);
//				$("#amount").text(currency(totalAmount,true));
//				$("#totalBoxQuantity").text(totalBoxQuantity);
//				$("#totalBranchQuantity").text(totalBranchQuantity);
//				$("#totalQuantity").text(totalQuantity);
			}
	
	 //手动匹配批次
	 function matching_bacth(e){
		
		//单据类型
		var billTypeId = $("input.billTypeId").val();
		var $tr = $(e).closest("tr");
		//行Id
		var itemId = $tr.find("input.itemId").val();
		//仓库
		var warehouseId = $("input.warehouseId").val();
		if(isNull(warehouseId) == null){
			$.message_alert("仓库不能为空");
			return false;
		}
		//生产工厂
		var productionPlantId = $('input.productionPlantId').val();
		//等级
		var productLevelId = $tr.find(".grade").val();
		if(isNull(productLevelId) == null){
			$.message_alert("产品等级不能为空");
			return false;
		}
		//产品
		var productId = $tr.find(".productId").val();
		if(isNull(productId) == null){
			$.message_alert("产品Id不能为空");
			return false;
		}
		//经营组织
		var organizationId = $tr.find(".productOrganization").val();
		if(isNull(organizationId) == null){
			$.message_alert("产品行经营组织不能为空");
			return false;
		}
		//色号
		var colorNumbersIds = $tr.find(".colourNumber").val();
		if(isNull(colorNumbersIds) == null){
			colorNumbersIds = "";
		}
		//含水率
		var moistureContentsIds = $tr.find(".moistureContent").val();
		if(isNull(moistureContentsIds) == null){
			moistureContentsIds = "";
		}	
		//批次
		var warehouseBatchIds = $tr.find(".batchIds").val();
		if(isNull(warehouseBatchIds)==null) {
			warehouseBatchIds = "";
		}
		//正式测试 出仓
		if(billTypeId == 509) { 
			billTypeId = 507;
		}else if(billTypeId == 504){
			billTypeId = 505;
		}
		//新旧标识
		var newOldLogosIds = $tr.find(".newOldLogo").val();
		if(isNull(newOldLogosIds) == null){
			newOldLogosIds = "";
		}
		var params='&warehouseId='+warehouseId+'&productId='+productId+'&organizationId='+organizationId+'&productionPlantId='+productionPlantId
					+'&productLevelId='+productLevelId+'&colorNumbersIds='+colorNumbersIds+'&moistureContentsIds='+moistureContentsIds
				    +'&warehouseBatchIds='+warehouseBatchIds+'&newOldLogosIds='+newOldLogosIds+'&billTypeId='+billTypeId;
		$(e).bindQueryBtn({
			bindClick:false,
			type : "post",
			title:'选择匹配批次',
			url:'/stock/batch/batch_stock.jhtml?'+params,
			callback:function(rows){
		        if(rows.length>0){
		            for (var i = 0; i < rows.length;i++) {
		            	var row = rows[i];
						//单据明细行Id
						row.am_shipping_item_id = itemId;
						//数量
						row.quantity = row.available_quantity;
						//箱数
						row.shipped_box_quantity = row.i_available_box_quantity;
					    //支数
						row.shipped_branch_quantity = row.i_available_branch_quantity;
					    //零散支数
					    if((isNull(row.shipped_branch_quantity) != null && row.shipped_branch_quantity>0) &&
					    		( isNull(row.branch_per_box) != null && row.branch_per_box>0 )){
					    	 row.shipped_branch_scattered = parseInt(row.shipped_branch_quantity%row.branch_per_box);
					    }else{
					    	row.shipped_branch_scattered = 0;
					    }
						$mmGridBacth.addRow(row,null,1);
					}	           
		        }
		    }
		});
	 }
	
	 function clearWarehouseBatchItem(Id){
	 		if(isNull(Id) != null){
	 			$("input.amShippingItemId").each(function(){
	 				var $this = $(this);
	 				var $tr = $this.closest("tr");
	 				var index = $this.closest("tr").index();
	 				var amShippingItemId = $this.val();
	 				if(isNull(amShippingItemId) != null && Id == amShippingItemId){
	 					$mmGridBacth.removeRow(index);
	 				}
	 			})
	 			$("#addBacth").css('display', 'none');
	 		}
	 	}

	
	 function select_post(e,organizationId,productionPlantId,callback){
		$(e).bindQueryBtn({
			bindClick:false,
			type:'bacth',
			title:'选择批次',
			url:'/stock/batch/select_bacth.jhtml?organizationId='+organizationId+'&productionPlantId='+productionPlantId,
			callback:function(rows){
				if(rows.length>0){
					if(callback(rows)==false){
						return false;
					}
				}
			}
		});
	}
	
	function check_pdf(e,id){
		ajaxSubmit(e,{
			url:"checkPdf.jhtml",
			method:"post",
			data:{ids:id},
			async: false,
			callback:function(resultMsg){
				window.open(resultMsg.content);
			}
		});
	}
	

	function deleteAmShipping(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid.removeRow(index);
		
		})
	}

	function shippingAudit(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			var url="shipping_audit.jhtml?id="+${amShipping.id};
			ajaxSubmit($this,{
				 url: url,
				 method: "post",
				 isConfirm:true,
				 confirmText:'您确定要审核吗？',
				 callback:function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				 }
			})
		}
	}
	
	// 审核
	function check_wf(e){
		var $this = $(e);
		var $form = $("#inputForm");
		if($form.valid()){
			var url="move_library_issue_check_wf.jhtml?id="+${amShipping.id};
			ajaxSubmit($this,{
				 url: url,
				 method: "post",
				 isConfirm:true,
				 confirmText:'您确定要审核吗？',
				 callback:function(resultMsg){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				 }
			})
		}
	}

	function save(e, flag){		
		var str = "您确定要保存吗？";
		var url = "movelibrary_issue_update.jhtml";
		judgmentValue3(url,'',str);
//		if($("#inputForm").valid()){
//			ajaxSubmit(e,{
//				url: 'movelibrary_issue_update.jhtml',
//				data:$("#inputForm").serialize(),
//				method: "post",
//				isConfirm:true,
//				confirmText:'您确定要保存吗？',
//				callback:function(resultMsg){
//				 	$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
//					location.reload(true);
//					})
//				 }
//			})
//		}
	}

	function add(e){
		location.href= '/b2b/sale_shipping_nature/add.jhtml?billType='+${amShipping.billType.id};
	}
	
</script>
</head>
<body>
	<div class="pathh">&nbsp;${message("查看出库单")}</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" id="pid" value="${amShipping.id}" /> 
		<input type="hidden" name="status" id="status" value="" />
		<input type="hidden" name="flag" value="0">
			<div class="tabContent shipping-info">
				<table class="input input-edit">
					<tr>
						<th>${message("单据号")}:</th>
						<td>
							<span>${amShipping.sn}</span>
						 	<input type="hidden" class="no" name="shippingSn" value="${amShipping.sn}" />
						</td>
						<th>${message("客户")}:</th>
						<td>
							<span>${amShipping.store.name}</span>
							<input type="hidden" class="storeId" value="${amShipping.store.id}">
						</td>
						<th>${message("单据状态")}:</th>
						<td>
							[#if amShipping.status==0] 
								未审核
							[#elseif amShipping.status==1]
								已审核 
							[#elseif amShipping.status==2] 
								已作废 
							[#elseif amShipping.status==3]
								部分发货
							[#elseif amShipping.status==4] 
								完全发货
							[/#if]
						</td>
					</tr>
					<tr>
						<th>${message("机构")}:</th>
						<td>
							<span>${amShipping.saleOrg.name}</span>
						</td>
						<th>${message("经营组织")}:</th>
						<td>
							<input type="hidden" name="organizationId" class="organizationId" value="${amShipping.organization.id}">
							<span class="organizationName">${amShipping.organization.name}</span>
						</td>
						<th>${message("Sbu")}:</th>
						<td>
							<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" value="${amShipping.sbu.id}" />
							<span id="sbuName">${amShipping.sbu.name}</span>
						</td>
					</tr>
					<tr>
						<th>${message("仓库")}:</th>
						<td>
							<input name="warehouseId" class="text warehouseId" type="hidden" value="${amShipping.warehouse.id}" /> 
							<input type="hidden" class="typesystemDictFlag" value="${amShipping.warehouse.typeSystemDict.flag}" /> 
							<input type="hidden" class="productionPlantId" value="${amShipping.warehouse.productionPlant.id}" /> 
							<span>${amShipping.warehouse.name}</span>
						</td>
						<th>${message("创建时间")}:</th>
						<td>
							<span>${amShipping.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
						</td>
						<th>${message("创建人")}:</th>
						<td>
							<span>${amShipping.storeMember.name}</span>
						</td>
					</tr>	
					<tr>
						 <th>${message("单据类型")}:</th>
            			 <td>
            			 	<input type="hidden" name="billTypeId" class="text billTypeId" id="billTypeId" btn-fun="clear" value="${amShipping.billType.id}" /> 
            			 	<span>${amShipping.billType.value}</span>
            			 </td>
            			 <th>${message("单据类别")}:</th>
            			 <td>
            			 	<input type="hidden" name="billCategoryId" class="text billCategoryId" id="billCategoryId" btn-fun="clear" value="${amShipping.billCategory.id}" /> 
            			 	<span>${amShipping.billCategory.value}</span>
            			 </td>
            			 <th>${message("审核人")}:</th>
            			 <td>
            			 	<span>${amShipping.checkStoreMember.name}</span>
            			 </td> 
					</tr>
					<tr>
						<th>
							${message("单据日期")}:
						</th>
						<td>
							<input type="text" class="text" name="billDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" value="${amShipping.billDate}"  />
						</td>
						<th>
							${message("车号")}:
						</th>
						<td>
							<input type="text" name="wagonNumber" class="text" value="${amShipping.wagonNumber}" btn-fun="clear" maxlength="200" />
						</td>
						<th>
							${message("柜号")}:
						</th>
						<td>
							<input type="text" name="containerNumber" class="text" value="${amShipping.containerNumber}" maxlength="200" btn-fun="clear" />
						</td>
					</tr>
					<tr>
						<th>${message("备注")}:</th>
						<td colspan="7">
							<textarea class="text memo"  name="memo">${amShipping.memo}</textarea>
						</td>
					</tr>
					<tr>
						<td colspan="7">
							<textarea class="mefont isMemo" id="isMemo" value=""></textarea>
						</td>
					</tr>
					<tr>
						<th>
							<span class="requiredField">*</span>${message("ERP发运备注")}:
						</th>
						<td colspan="7">${amShipping.erpRemark}</td>
					</tr>
				</table>
				<div class="title-style">${message("发货项")}:
					<div class="btns">
						<a href="javascript:;" id="copyData" class="button">复制</a>
					</div>	
				</div>
				<table id="table-m1"></table>
				<!-- 批次列表-->
				<div class="title-style">
				[#if amShipping.warehouse.enableBatch]
					${message("批次列表")}:										
						<div class="btns">
							<a href="javascript:;" id="matchingBacth" class="button">匹配批次</a>
						</div>
				[/#if]
				</div>
				<table id="table-bacth"></table>
				<div class="title-style">${message("发货汇总")}</div>
				<table class="input input-edit">
					<tr>
						<!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积 -->
						[#if sbu !=3 ]
							<th>${message("发货件数")}:</th>
							<td>
								<span id="totalBoxQuantity"></span>
							</td>
							<th>${message("发货支数")}:</th>
							<td>
								<span id="totalBranchQuantity"></span>
							</td> 
						[/#if]
						<th>${message("发货数量")}:</th>
						<td>
							<span id="totalQuantity"></span>
						</td> 
						[#if sbu !=3 ]
							<th>${message("发货重量")}:</th>
							<td>
								<span id="totalWeight">${amShipping.weight!0}</span>
							</td> 
						[/#if]
					</tr>
					[#if sbu !=3 ]
						<tr>
							<th>${message("发货体积")}:</th>
							<td><span id="totalVolume">${amShipping.volume!0}</span></td>
						</tr>
					[/#if]
				</table>
				<div class="title-style">${message("全链路信息")}:</div>
				<table id="table-full"></table>
			</div>
			<div class="fixed-top">
				 [#if amShipping.status ==0 && auditType !=0 ] 
				 	<input type="button" class="button sureButton" value="${message("审核")}" onclick="check_wf(this)" /> 
				 [/#if] 
				 
				 [#if amShipping.status ==0 && auditType ==0 ] 
				  	<input type="button" class="button sureButton" value="${message("审核")}" onclick="shippingAudit(this)" /> 
				 [/#if] 
				 
				 [#if amShipping.status ==0 && amShipping.wfId==null]
				 		<a href="javascript:void(0);" class="button sureButton" onclick="save(this, 0)">${message("保存")}</a>
					 	<a href="javascript:void(0);" class="iconButton" id="fenpeiButton" onclick="close_e(this)"> 
					 		<span class="fenpeiIcon">&nbsp;</span>${message("作废")}
					 	</a> 
				 [/#if] 
				 
			     [#if amShipping.status !=2] 
			  		<input type="button" class="button pdfButton" value="${message("PDF")}" onclick="check_pdf(this,${amShipping.id})" />&nbsp;
					  <input type="button" class="button pdfButton" value="${message("批次PDF")}" onclick="checkBatch_pdf(this,${amShipping.id})" />
			     [/#if]
				 <!-- <a href="javascript:void(0);" class="button sureButton" onclick="add(this)">${message("新增")}</a> -->
				 <input type="button"onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
			</div>
	</form>
	<div id="wf_area" style="width: 100%"></div>
</body>
</html>