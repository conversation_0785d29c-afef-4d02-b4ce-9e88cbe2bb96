<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看项目文档")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />

<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<style>
	#tab{border="1px solid #d0cdcd";}
	#tab .ssdtop{width: 80px;height: 35px;border: 1px solid #d0cdcd;background-color:#e6e6e6;}
	#tab .content{width: 80px;height: 35px;}
	#tab .conne{text-align:center;}
	#tab .connetop{font-weight:500;}
</style>
<script type="text/javascript">



$().ready(function() {
	
	var $selectProductCategory = $("#selectProductCategory");

	//查询分类
	$("#selectProductCategory").bindQueryBtn({
		type:'productCategory',
		title:'${message("查询分类")}',
		url:'/product/product_category/select_productCategory.jhtml?isEnabled=1',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				var $parent = $selectProductCategory.parent();
				$parent.find("input.productCategoryId").val(row.id);
				$parent.find("input.productCategoryName").val(row.name);
			}
		}
	});
	
	// 表单验证
	$.validator.addClassRules({
		oaSn: {
			required: true
		}
	});
	

 /**初始化订单附件*/
            var projectAttach_items = ${customerProject_json};      
            var projectAttachIndex=0;
            var cols = [
                { title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex, obj){
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['customerProjectAttachs['+projectAttachIndex+'].url']=url;
                        hideValues['customerProjectAttachs['+projectAttachIndex+'].suffix']=fileObj.suffix;

                        return createFileStr({
                            url : url,
                            fileName : fileObj.file_name,
                            name : fileObj.name,
                            suffix : fileObj.suffix,
                            time : '',
                            textName:'customerProjectAttachs['+projectAttachIndex+'].name',
                            hideValues:hideValues
                        });
                    }},
                { title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex, obj){
                        return '<div><textarea class="text file_memo" name="customerProjectAttachs['+projectAttachIndex+'].memo" >'+val+'</textarea></div>';
                    }},
                { title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex, obj){
                        projectAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }}
            ];
            var $amGrid=$('#table-attach').mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: cols,
                items:projectAttach_items,
                checkCol: false,
                autoLoad: true
            });
            
             var $addAttach = $("#addAttach");
            var attachIdnex = 0;
            var option1 = {
                dataType: "json",
                uploadToFileServer:true,
                uploadSize: "fileurl",
                callback : function(data){
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth()+1;
                    var day = date.getDate();
                    var time = year+'-'+month+'-'+day;
                    for(var i=0;i<data.length;i++){
                        var row = data[i].file_info;
                        $amGrid.addRow(row,null,1);
                    }

                }
            }
            $addAttach.file_upload(option1);
            
        var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
            });      

	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});

	$("#openStoreMember").click(function(){
    	$("#openStore").bindQueryBtn({
	        type:'store',
	        bindClick:false,
	        title:'${message("查询区域经理")}',
	        url:'/shop/manager/select_manager.jhtml?memberType=0&isSalesman=true',
	        callback:function(rows){
	            if(rows.length>0){
	                var row = rows[0];
	                $("input[name='storeMemberId']").attr("value",row.id)
	                $("input[name='storeMemberName']").attr("value",row.name);
	            }
	        }
	    });
    })
	
	$("form").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/b2b/customerProject/view.jhtml?id='+resultMsg.objx;
			});
		}
	 });

	
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1'
	});
});

	function deleteShipping(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$move_mmGrid.removeRow(index);
			summarizing();
		})
	}
	
	
	
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看项目文档")}
	</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("项目编号")}:
				</th>
				<td>
				${customerProject.projectNo}
				</td>
					
				<th><span class="requiredField">*</span>${message("客户")}:</th>
					<td><span class="search" style="position: relative"> 
					<inputtype="hidden" name="storeId" class="text storeId" value="${customerProject.store.id}" btn-fun="clear" /> 
					<input type="text" name="storeName" class="text storeName" value="${customerProject.store.name}" maxlength="200" onkeyup="clearSelect(this)" readOnly /> 
					<input type="button" class="iconSearch" value="" id="selectStore"></span>
					</td>
            	
            	<th>${message("机构")}:</th>
					<td><span class="search" style="position: relative"> <input
							type="hidden" name="saleOrgId" class="text saleOrgId"
							btn-fun="clear"
							value="[#if customerProject??]${customerProject.saleOrg.id}[#else]${saleOrg.id}[/#if]" />
							<input type="text" name="saleOrgName" class="text saleOrgName"
							maxlength="200" onkeyup="clearSelect(this)"
							value="[#if customerProject??]${customerProject.saleOrg.name}[#else]${saleOrg.name}[/#if]"
							readOnly /> <input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span></td>
					<th>${message("Sbu")}:</th>
					<td><select id="sbuId" name="sbuId" class="text sbuId">
						[#list sbus as sbu]
						<option value="${sbu.sbu.id}"[#if customerProject.sbu.id==sbu.sbu.id]selected[/#if]>${sbu.sbu.name}</option>
						[/#list]
					</select></td>
							
							
            
	</tr>
	
	<tr>
	
		<th><span class="requiredField">*</span>${message("项目简称")}:</th>
            	<td>
            		<input type="text" name="projectName" class="text projectName" btn-fun="clear" value="${customerProject.projectName}"/>
            	</td>

				<th>${message("项目类别")}:</th>
				<td>
					 <select id="projectTypeName" name="projectTypeId" class="text"> 
							[#list projectTypes as projectType]
								<option value="${projectType.id}" [#if customerProject.projectType.id==projectType.id]selected[/#if]>${projectType.value}</option>
							[/#list]
						</select>
					
				</td>
				
				<th>
					<span class="requiredField">*</span>${message("登记日期")}:
				</th>
						
    			<td>
    				<input id="month" name="registrationDate" class="text" value="${customerProject.registrationDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" type="text" btn-fun="clear"/>
    			</td>
				
			<th>
					${message("设置")}:
				</th>
				<td>
					<label>
						<input type="checkbox" name="isEnabled" [#if customerProject.isEnabled]checked="checked"[/#if] value="true"  />${message("是否启用")}
						<input type="hidden" name="_isEnabled" value="false" />
					</label>                         
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;	
					<input type="hidden" name="isReduceBalance" value="true" />
				</td>
	</tr>
	
	
	<tr>
			
				<th>
				${message("联系人")}:
			</th>
			<td>
            		<input type="text" name="contacts" class="text contacts" btn-fun="clear" value="${customerProject.contacts}"/>
            </td>
				<th>
				${message("联系电话")}:
			</th>
			<td>
				<input type="text" name="phone" class="text phone" btn-fun="clear" value="${customerProject.phone}"/>
			</td>
			<th>
			${message("详细地址")}:
		</th>
		<td colspan="3">
            <input type="text" name="address" class="text address" btn-fun="clear" value="${customerProject.address}"/>
        </td>
	</tr>
	<tr>
		<th>
			${message("备注")}:
		</th>
		<td colspan="7">
            <textarea class="text" name="remarks" value="${customerProject.remarks}">${customerProject.remarks}</textarea>
        </td>
	</tr>
	</table>
	<div class="title-style">${message("业务信息")}</div>
	<table class="input input-edit">
	   <tr>
		<th>
			${message("业务部门")}:
		</th>
		<td >
            <select id="businessDepartmentName" name="businessDepartmentId" class="text"> 
							[#list businessDepartments as businessDepartment]
								<option value="${businessDepartment.id}" [#if customerProject.businessDepartment.id==businessDepartment.id]selected[/#if]>${businessDepartment.value}</option>
							[/#list]
						</select>
        </td>
        <th>
			${message("业务员")}:
		</th>
		<td >
					<span class="search" style="position:relative">
						<input type="hidden" name="storeMemberId" class="text storeMemberId" value="${customerProject.storeMember.id}" btn-fun="clear"/>
						<input type="hidden" name="type" value="${store.type}">
						<input type="text" name="storeMemberName" class="text storeMemberName"  value="${customerProject.storeMember.name}" maxlength="200" onkeyup="clearSelect(this)"  readOnly />
						<input type="button" class="iconSearch" value="" id="openStoreMember"/>
					</span>
        </td>
	</tr>
	</table>
	<div class="title-style">${message("产品信息")}</div>
	<table class="input input-edit">
	   <tr>
		<th>
		${message("12061")}:
		</th>
		<td>
				<span class="search" style="position:relative">
				<input type="hidden" id="productCategoryId" name="productCategoryId" class="text productCategoryId" value="${customerProject.productCategory.id}" btn-fun="clear"/>
				<input type="text" id="productCategoryName" readOnly="true" name="productCategoryName" value="${customerProject.productCategory.name}" class="text productCategoryName" maxlength="200"/>
				<input type="button" class="iconSearch" value="" id="selectProductCategory">
				</span>
				</td>
        <th>
			${message("开发商")}:
		</th>
		<td >
          <input type="text" name="developers" class="text developers" btn-fun="clear" value="${customerProject.developers}"/>
        </td>
        <th>
			${message("交付类型")}:
		</th>
		<td >
           <select id="deliveryTypeName" name="deliveryTypeId" class="text"> 
							[#list deliveryTypes as deliveryType]
								<option value="${deliveryType.id}" [#if customerProject.deliveryType.id==deliveryType.id]selected[/#if]>${deliveryType.value}</option>
							[/#list]
						</select>
        </td>
        <th>
			${message("发包方式")}:
		</th>
		<td >
          <select id="contractingMethodName" name="contractingMethodId" class="text"> 
							[#list contractingMethods as contractingMethod]
								<option value="${contractingMethod.id}" [#if customerProject.contractingMethod.id==contractingMethod.id]selected[/#if]>${contractingMethod.value}</option>
							[/#list]
						</select>
        </td>
	</tr>
	  <tr>
	       <th>
			${message("项目状态")}:
		</th>
		<td >
          
        </td>
	  </tr>
	</table>
	 <div class="title-style">
				${message("附件信息")}:
				<div class="btns">
					<a href="javascript:;" id="addAttach" class="button">添加附件</a>
				</div>
			</div>
	<table id="table-attach"></table>
	

		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>