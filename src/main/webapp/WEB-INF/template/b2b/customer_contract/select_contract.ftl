<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查询经销商合同")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
	function initTypes(){
		types = {};
		[#list types as value]
			types['${value}'] = "${message('11111111'+value)}";
		[/#list]
	}
	$().ready(function() {
		
		/**初始化多选下拉框*/
		initMultipleSelect();
	
		var cols = [
		{ title:'${message("合同名称")}', name:'contract_no' ,width:100, align:'center'},
		{ title:'${message("合同编码")}', name:'contract_name' ,width:100, align:'center'},
	    { title:'${message("客户名称")}', name:'store_name' ,width:100, align:'center'},
	    { title:'${message("机构")}', name:'sale_org_name' ,width:100, align:'center'},
	  
	];
	var multiSelect = false;
	[#if multi==2]
		multiSelect = true;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		rowCursorPointer:true,
        cols: cols,
        url: 'select_contract_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	});
	function childMethod(){
	   return $mmGrid.selectedRows();
	};
</script>
</head>
<body  style="min-width: 0px;">
<form id="listForm" action="select_project_data.jhtml" method="post">
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
		    	<dl>
		    		<dt><p>${message("合同名称")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="contract_name" name="contractName" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("合同档案编码")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="contract_no" name="contractNo" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("合同编号")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="sn" name="sn" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	<dl>
		    		<dt><p>${message("客户名称")}：</p></dt>
		    		<dd>
						<input type="text" class="text" id="store_name" name="storeName" value ="" btn-fun="clear" />
		    		</dd>
		    	</dl>
		    	
	        </div>
		<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
	</form>
</body>
</html>