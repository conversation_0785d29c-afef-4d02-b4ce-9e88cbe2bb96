<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<style>
	.lh20 {
		line-height: 20px;
	}
	
	.ov {
		overflow: hidden
	}
	
	tr.s-tr, tr.s-tr td {
		height: 10px !important;
	}
	
	div.w_1135 {
		width: 1135px;
	}
	
	#atc th, #atc td {
		border: solid 1px #F7F7F7;
	}
	
	.mefont {
		color: red;
		font-style：italic;
		斜体
		font-weight：bold;
		加粗
		font-size：30px;
		大小
		line-height：30px;
		行高
		font-family：“SimHei”;
		字体
	}
</style>
<script type="text/javascript">
		
		function productOrganization(){
			var warehouseId = $("input.warehouseId").val();
			$("input.productId").each(function(){
				var $this = $(this);
				var productId = $this.val();
				var $tr = $this.closest("tr");
				[#if organizationIsNoWarehouse == 0 ] 
					$tr.find(".productOrganizationName").text($("#organizationId").text());
					$tr.find("input.productOrganizationId").val($("input.organizationId").val());
				[#elseif organizationIsNoWarehouse == 1 ] 
					if(productId !=null && productId !='' && productId!='undefined'){
						$.ajax({
							url : '/product/product/findProductOrganizationList.jhtml',
			    			type : "post",
			    			data : {id:productId},
			    			success : function(data) {
			    				var content = JSON.parse(data.content);
			    				if(content.length!=0){
			    					var html = "";
			    					var organizationId = null;
			    					var productOrganizationId = $tr.find(".productOrganization").val();
			    					var isTrue = true;
			    					for(var i=0; i<content.length; i++){
			    						var isDefaults = content[i].is_defaults;
			    						html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';    
		    							if(productOrganizationId==content[i].organization){
		    								isTrue = false;
		    								organizationId = content[i].organization;
		    							}else{
			        						if(i==0){
			    								organizationId = content[i].organization;
			    							}else if(isDefaults==1 && isTrue){
			    								organizationId = content[i].organization;
			    							}
		    							}
			    					}
			    					$tr.find(".productOrganization").empty();
			    					$tr.find(".productOrganization").html(html);
			    					$tr.find(".productOrganization option[value='"+organizationId+"']").attr("selected",true); 
			    					[#if linkStock == 1 ] 
				    					if(organizationId!=null && organizationId!=''){
				    						//等级
				    						var productGrade = $tr.find(".productGrade").val();
				    						//编码
				    						var vonderCode = $tr.find(".vonderCode").text();
				    						//色号
				    						var colourNumber = $tr.find(".colourNumber").val();
				    						if(colourNumber=='undefined' || colourNumber.length == 0){
				    							colourNumber = "";
				    						}
				    						//含水率
				    						var moistureContent = $tr.find(".moistureContent").val();
				    						if(moistureContent=='undefined' || moistureContent.length == 0){
				    							moistureContent = "";
				    						}
				    						var params='&productGrade='+productGrade+'&vonderCode='+vonderCode+'&colourNumber='+colourNumber
				    		    			   +'&moistureContent='+moistureContent+'&organizationId='+organizationId+'&warehouseId='+warehouseId;
				    							params = params.substring(1,params.length);	
			    							$.ajax({
			    								url:'/stock/stock/findViewStock.jhtml?'+params,
			    				    			type : "post",
			    				    			success : function(rows) {
			    				    				var data= $.parseJSON(rows.content);
		    					                    if(data.length>0){
		    					                        for (var i = 0; i < data.length;i++) {
		    					                        	if(data[i].totalOnhandQuantity1 !=null && data[i].totalOnhandQuantity1 !=''){
		    					                        		$tr.find(".onhandQuantity").text(data[i].totalOnhandQuantity1);
		    					                        	}else{
		    					                        		$tr.find(".onhandQuantity").text(0);
		    					                        	}
		    					                        	if(data[i].totalAttQuantity1 !=null && data[i].totalAttQuantity1 !=''){
		    					                        		$tr.find(".attQuantity").text(data[i].totalAttQuantity1);
		    					                        	}else{
		    					                        		$tr.find(".attQuantity").text(0);
		    					                        	} 
		    					                    	}
		    					                	}else{
		    					                		$tr.find(".onhandQuantity").text(0);
		    					                		$tr.find(".attQuantity").text(0);
		    					                	}
			    				    			}
			    							})	 
				    					}
			    					[/#if]	
			    				}
			    			}
						})
					}
				[/#if]
			});
		}
	
        function countTotal(t){
            var totalBoxQuantity = 0;
            var totalBranchQuantity = 0;
            var totalVolume = 0;
            var totalWeight = 0;
            var $bInput = $("input.boxQuantity");
            $bInput.each(function(){
                var $tr = $(this).closest("tr");
                var isEqual = null;
                if(t!=undefined){
                    isEqual = (t.find(".line_no").text() == $tr.find(".line_no").text());
                }
                var boxQuantity=$(this).val();//箱数
                var branchPerBox=$tr.find(".branchPerBox").val();
                var perBranch=$tr.find(".perBranch").val();
                var perBox = $tr.find(".perBox").val();//数量
                var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
                var quantity = $tr.find(".quantity").val();
                var branchQuantity = $tr.find(".branchQuantity").val();
                var type = productDisc(branchPerBox,perBranch,perBox);
                //正常地板0 壁纸1 铺料2  只有支数3
                //辅料逻辑处理
                if(isEqual == null){
                    if(type==2){
                        var a = accDiv(perBox,10);
                        quantity = accMul(boxQuantity,a);
                        $tr.find(".quantity").val(quantity);//数量
                        $tr.find(".quantityStr").html(quantity);
                    }else if(type==3){
                        quantity = accMul(perBranch,branchQuantity);
                        $tr.find(".quantity").val(quantity);//数量
                        $tr.find(".quantityStr").html(quantity);
                        $tr.find(".boxQuantity").val("");
                        $tr.find(".scatteredQuantity").val("");
                        $tr.find(".branchPerBox").val("");
                    }else if(type==0){//正常逻辑
                        var branchQuantity=accMul(boxQuantity,branchPerBox);

                        branchQuantity=accAdd(branchQuantity,scatteredQuantity);
                        if(isNaN(branchQuantity)){
                            branchQuantity = 0;
                        }
                        totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
                        totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
                        $tr.find(".branchQuantity").val(branchQuantity);//支数
                        $tr.find(".branchQuantityStr").html(branchQuantity);
                        var quantity=accMul(branchQuantity,perBranch);
                        if(isNaN(quantity)){
                            quantity = 0;
                        }
                        $tr.find(".quantity").val(quantity);//数量
                        $tr.find(".quantityStr").html(quantity);

                        var volume=$tr.find(".lineVolume").val();

                        var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);

                        if(isNaN(volumeAmount)){
                            volumeAmount = 0;
                        }
                        totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6));
                        $tr.find(".lineVolumeAmount").html(volumeAmount);//体积
                    }
                }else{
                    if(type==2&&isEqual){
                        var a = accDiv(perBox,10);
                        quantity = accMul(boxQuantity,a);
                        $tr.find(".quantity").val(quantity);//数量
                        $tr.find(".quantityStr").html(quantity);
                    }else if(type==3&&isEqual){
                        quantity = accMul(perBranch,branchQuantity);
                        $tr.find(".quantity").val(quantity);//数量
                        $tr.find(".quantityStr").html(quantity);
                        $tr.find(".boxQuantity").val("");
                        $tr.find(".scatteredQuantity").val("");
                        $tr.find(".branchPerBox").val("");
                    }else if(type==0&&isEqual){//正常逻辑
                        var branchQuantity=accMul(boxQuantity,branchPerBox);

                        branchQuantity=accAdd(branchQuantity,scatteredQuantity);
                        if(isNaN(branchQuantity)){
                            branchQuantity = 0;
                        }
                        totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
                        totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
                        $tr.find(".branchQuantity").val(branchQuantity);//支数
                        $tr.find(".branchQuantityStr").html(branchQuantity);
                        var quantity=accMul(branchQuantity,perBranch);
                        if(isNaN(quantity)){
                            quantity = 0;
                        }
                        $tr.find(".quantity").val(quantity);//数量
                        $tr.find(".quantityStr").html(quantity);

                        var volume=$tr.find(".lineVolume").val();

                        var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);

                        if(isNaN(volumeAmount)){
                            volumeAmount = 0;
                        }
                        totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6));
                        $tr.find(".lineVolumeAmount").html(volumeAmount);//体积
                    }
                }
            });
            $("#totalBoxQuantity").text(totalBoxQuantity);
            $("#totalBranchQuantity").text(totalBranchQuantity);

            var $input = $("input.quantity");
            var total = 0;
            var totalQuantity = 0;

            var b = $("#storeBalance").val();
            $input.each(function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
                var $price_box = $tr.find(".price-box");
                var price;
                if($price_box.length==0 || $price_box.prop("checked")==false){
                    price = Number($tr.find("input.price").val());
                }else{
                    price = Number($tr.find("input.origMemberPrice ").val());
                } 
                var quantity = $this.val();
                
                var amount = Number($this.val())*price;

                totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);

                if(isNaN(amount)){
                    amount = 0;
                }
                total = Number(total)+Number(currency(amount,false));
                
                $tr.find(".trprice").html(currency(amount,true));//订单行金额
                

                var weight=$tr.find(".lineWeight").val();

                var weightAmount=Number(accMul($(this).val(),weight)).toFixed(6);

                if(isNaN(weightAmount)){
                    weightAmount = 0;
                }
                totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6));
                $tr.find(".lineWeightAmount").html(weightAmount);//重量

            });
            total= Math.ceil(total);
            $("#total").text(currency(total,true));
            $("#totalVolume").text(totalVolume);
            $("#totalWeight").text(totalWeight);
            $("#totalQuantity").text(totalQuantity);

            var storeBalance = $("#storeBalance").val()
            $("#chae").text(currency(storeBalance-total,true));

			[#if hiddenAmount==0]
				$("#total").text("***");
				$("#balance").text("***");
				$("span.price_span").text("***");
				$(".trprice").text("***");
				$("#chae").text("***");
		    [/#if]
        }

        //金额查询
        function product_price(e){
            var $tr = $(e).closest("tr");
            var productGrades = $tr.find(".productGrades").val();
            var storeId = $("input[name='storeId']").val();
            var productId = $tr.find(".productId").val();
            var warehouseId = $("input[name='warehouseId']").val();//warehouseId
            var saleOrgId = $("input[name='saleOrgId']").val();//saleOrgId
            var productGrade = $tr.find(".productGrade option:selected").val();
            var sbuId = $("input[name='sbuId']").val();//sbuId
            var memberId = $(".memberRankId").val();// memberRankId

            ajaxSubmit(e,{
                method:'post',
                url:'/product/product_price_head/findProductPrice.jhtml',
                data:{storeId:storeId,productId:productId,warehouseId:warehouseId,saleOrgId:saleOrgId,
                    productGrade:productGrade,sbuId:sbuId,memberId:memberId},
                callback:function(resultMsg) {
                    var data = resultMsg.objx;
                    if (data !=null) {
                        if (isNaN(data.store_member_price)) {
                           	$tr.find("#productGrade").val(productGrades);
                            $.message_alert("${message("请在价格表维护该产品价格")}");
                            return false;
                        }else {
                        	//单价
                            $tr.find("input.price").val(data.store_member_price);
                        	//平台结算价
                            $tr.find("input.saleOrgPrice").val(data.sale_org_price);
                            $tr.find(".price_span").val(currency(data.store_member_price,true));
                            $tr.find(".price_span").text(currency(data.store_member_price,true));
                            $tr.find(".productGrades").val(productGrade);
                            //特价单号
                            $tr.find("input.linePriceApplyItemSn").val("");
                        }
                    }else {
                         $tr.find("#productGrade").val(productGrades);
                         $.message_alert("${message("请在价格表维护该产品价格")}");
                         return false;
                    }
                    countTotal();
                    [#if linkStock == 1 ] 
            			productOrganizationChange(e);
            		[/#if]
                }
            });
        }



        function editQty(t,e){
            if($(t).attr("kid")=="quantity"){//平方
                if(extractNumber(t,6,false,e)){
                    var $tr = $(t).closest("tr");
                    var branch_quantity=0;
                    var box_quantity=0;
                    var quantity=$(t).val();
                    var perBranch=$tr.find(".perBranch").val();  //每支单位数
                    var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                    var perbox = $tr.find(".perBox").val();//数量
                    var scattered=0;
                    var type = productDisc(branchPerBox,perBranch,perbox);
                    if(type==0){
                        branch_quantity=quantity/perBranch;
                        box_quantity=parseInt(branch_quantity/branchPerBox);
                        scattered=(branch_quantity%branchPerBox).toFixed(6);
                    }
                    if(type==2){
                        perbox = accDiv(perbox,10);
                        box_quantity = modulo(quantity,perbox);
                    }
                    if(type==3){
                        branch_quantity = modulo(quantity,perBranch);
                    }
                    $tr.find(".boxQuantity").val(box_quantity);
                    $tr.find(".branchQuantity").val(branch_quantity);
                    $tr.find(".scatteredQuantityStr").html(scattered);
                    $tr.find(".scatteredQuantity").val(scattered);
                    countTotal($tr);
                    $(t).val(quantity);
                }
            }else{
                if(extractNumber(t,3,false,e)){
                    var $tr = $(t).closest("tr");
                    var branch_quantity=0;
                    var box_quantity=0;
                    if($(t).attr("kid")=="box"){//箱
                        $tr.find(".scatteredQuantityStr").html(0);
                        $tr.find(".scatteredQuantity").val(0);
                    }else if($(t).attr("kid")=="branch"){//支
                        var quantity=$(t).val();
                        var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                        var box=parseInt(quantity/branchPerBox);
                        var scattered=quantity%branchPerBox;
                        $tr.find(".boxQuantity").val(box);
                        $tr.find(".scatteredQuantityStr").html(scattered);
                        $tr.find(".scatteredQuantity").val(scattered);
                    }else if($(t).attr("kid")=="quantity"){//平方
                        var quantity=$(t).val();
                        var perBranch=$tr.find(".perBranch").val();  //每支单位数
                        var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                        var branch_quantity=quantity/perBranch;
                        var box_quantity=parseInt(branch_quantity/branchPerBox);
                        var scattered=branch_quantity%branchPerBox;
                        $tr.find(".boxQuantity").val(box_quantity);
                        $tr.find(".scatteredQuantityStr").html(scattered);
                        $tr.find(".scatteredQuantity").val(scattered);
                    }
                    countTotal($tr);
                }
            }
        }
        
        
 <!-- 平台结算价-->
 function editPrice(t,e){
	if(extractNumber(t,2,false,e)){
		countTotal();
	}
 }
 
 <!-- 2019-05-17 冯旗 折扣率算单价-->    
 function editAcount(t,e){
    if($(t).attr("kid")=="discount"){
    	 var $tr = $(t).closest("tr");
   		 var discount=$(t).val();
   	 	 var price=$tr.find(".proPriceHeadPrice").val();
     	 var discountPrice = Number(accMul(discount,price))/100;
     	 $tr.find(".price").val(discountPrice);
     	 $tr.find(".price_span").html(currency(discountPrice,true));
   	     countTotal($tr);
    }else if($(t).attr("kid")=="price"){
   		 var $tr = $(t).closest("tr");
    	 var price=$(t).val();
    	 var proPriceHeadPrice =Number($tr.find("input.proPriceHeadPrice ").val());
         var discount = (price/proPriceHeadPrice*100);
         $tr.find(".discount").val(discount);//折扣率
         $tr.find(".discount").html(discount);
         countTotal($tr);
	}            
} 


 <!-- 2019-05-24 冯旗 长宽算数量-->    
 function editNumber(t,e){
        if($(t).attr("kid")=="length"){
	        if(extractNumber(t,6,false,e)){
	        	 var $tr = $(t).closest("tr");
	       		 var length=$(t).val();
	       	 	 var width=$tr.find(".width").val();
	         	 var quantity = accMul(length,width).toFixed(6);
	         	 $tr.find(".quantity").val(quantity);
	         	 $tr.find(".quantity_span").html(quantity);
	       	     countTotal($tr);
	       	 }
        }else if($(t).attr("kid")=="width"){
	        if(extractNumber(t,6,false,e)){
	       		 var $tr = $(t).closest("tr");
	        	 var width=$(t).val();
	        	 var length=$tr.find(".length").val();
	             var quantity = accMul(length,width).toFixed(6);
	             $tr.find(".quantity").val(quantity);
	             $tr.find(".quantity_span").html(quantity);
	             countTotal($tr);
	   		 }
		}        
} 

 <!-- 2019-08-05 冯旗 检测备注字数是否超过240字符-->  
 function listenLength(){
   
  var memeo=$(".memo").val();
  var len = 0;  
    for (var i=0; i<memeo.length; i++) {   
     var c = memeo.charCodeAt(i);   
    //单字节加1   
     if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
       len++;   
     }   
     else {   
      len+=2;   
     }   
    }   
    if(len>80){
      $("#isMemo").val("字符长度已超过240!");
    }else{
     $("#isMemo").val("");
    }
 
 
 }




function updatePrice(id,memberId){
                var id=id;
                var productIds = $(".productIds").val();
                var warehouseId = $(".warehouseId").val();
                var saleOrgId = $(".saleOrgId").val();
 			    var sbuId = $(".sbuId").val();
 			    var memberRankId=memberId;
 				 if(memberRankId == undefined){
    				  memberRankId = "";
  				  }
  				 var models='';
  						var typeSystemDictId=$(".typeSystemDictId").val();
 						 ajaxSubmit($(id),{
                                method:'POST',
                                url:'/product/product/selectProductData.jhtml',
                                data:{storeId:id,productIds:productIds,typeSystemDictId:typeSystemDictId,saleOrgId:saleOrgId,sbuId:sbuId,memberRankId:memberRankId},
                                callback:function(resultMsg) {
                                 
                         		var data = $.parseJSON(resultMsg.content);
                                    var productId;
                                      var rows=$mmGrid.rows();
                                       var index=0;
                                   for(var a=0;a<rows.length;a++){
                                   if(rows[a].product!=undefined){
                                    productId = rows[a].product;
                                   }else{
                                     productId=rows[a].id;
                                   }
                                    var model=rows[a].model;
                                    var mycars=new Array(data.content.length);
                                    for(var i=0;i<data.content.length;i++){
                                     mycars[i]=data.content[i].id;
                                      if(productId==data.content[i].id){                                      
                                          $mmGrid.updateRow(data.content[i],index);
                                          countTotal();   
                                      }
                                    }
								    var isHvae=contains(mycars,productId);
                                    if(!isHvae){
                                         $.message_alert('产品型号为['+model+']的产品在价格表中不存在,请删除后维护价格再选择');
                                         models+=model;
                                          
                                    }								
                                    index++; 
                                  } 
                                  $(".models").val(models);
                                }
                            });
                   


}


function contains(arr, obj) {  
    var i = arr.length;  
    while (i--) {  
        if (arr[i] === obj) {  
            return true;  
        }  
    }  
    return false;  
} 



        function showSale(e){
            var $tr = $(e).closest("tr");
            var $priceApplyText = $tr.find(".priceApplyText");
            if($(e).prop("checked")){
                $tr.find("input.priceApply").prop("disabled",false);
                $tr.find(".msg-tip").show();
                $priceApplyText.each(function(){
                    $(this).text($(this).attr("val"));
                })

                var $price = $tr.find(".price");
                var $price_span = $tr.find(".price_span");
                var $priceApply = $tr.find(".priceApply");
                var aprice = Number($priceApply.val());
                var price = Number($price.attr("org"));
                $price.val(aprice);
                $price_span.text(currency(price,true));
                countTotal();

                $tr.find(".priceApplyDiv").show();
                $tr.find(".priceDiv").hide();
            }else{
                $tr.find("input.priceApply").prop("disabled",true);
                $tr.find(".msg-tip").hide();
                $priceApplyText.text('-');

                var $quantity = $tr.find(".quantity");
                //activity_price($quantity);
                countTotal();

                $tr.find(".priceApplyDiv").hide();
                $tr.find(".priceDiv").show();
            }


        }


        function changeProceForStore(row){
            var id = row.id;
            var pid_str = "storeId="+id;
            $(".productId").each(function(){
                pid_str = pid_str+"&productId="+$(this).val()
            })
            ajaxSubmit($(id),{
                method:'post',
                url:'/b2b/order/get_price.jhtml',
                data:pid_str,
                callback:function(resultMsg) {
                    var data = resultMsg.objx;
                    for(var key in data) {
                        var item = data[key];
                        var $tr = $(".productId_"+key).closest("tr");
                        var $price =  $tr.find(".price");
                        $price.val(item.member_price).attr("org",item.member_price);
                        var price_str = currency(item.member_price,true);
                        $price.parent().find("span.priceText").text(price_str).attr("val",price_str);

                        var $apply_sn_flied = $tr.find(".apply_sn_flied");
                        var $apply_price_flied = $tr.find(".apply_price_flied");
                        var $is_sale_flied = $tr.find(".is_sale_flied");

                        if(item.apply_sn!=undefined){

                            var rowIndex = $apply_sn_flied.attr("rowIndex");

                            $is_sale_flied.html('<input type="checkbox" checked="checked" onclick="showSale(this)" class="price-box">');

                            $apply_sn_flied.html('<input type="hidden" name="orderItems['+rowIndex+'].priceApplySn" class="priceApplySn text priceApply" value="'+item.apply_sn+'" />'+
                                    '<input type="hidden" name="orderItems['+rowIndex+'].priceApplyItem.id" class="priceApplyItem text priceApply" value="'+item.apply_item_id+'" />'+
                                    '<span class="priceApplyText" val="'+item.apply_sn+'">'+item.apply_sn+'</span>'
                            );

                            var $priceApplyDiv = $tr.find(".priceApplyDiv");
                            $priceApplyDiv.find(".origMemberPrice").val(item.apply_price).prop("disabled",false);
                            var applyPrice = currency(item.apply_price,true);
                            $priceApplyDiv.find(".priceApplyText").text(applyPrice).attr("val",applyPrice);
                            $priceApplyDiv.show();
                            $tr.find(".priceDiv").hide();

                            var $quantity = $tr.find(".quantity");
                            $('<div class="red msg-tip">* 特价可购买'+item.apply_useable_quantity+'</div>').insertAfter($quantity);

                        }else{
                            $is_sale_flied.html('-');
                            $apply_sn_flied.html("-");

                            var $priceApplyDiv = $tr.find(".priceApplyDiv");
                            $priceApplyDiv.find(".origMemberPrice").val("");
                            var applyPrice = "-";
                            $priceApplyDiv.find(".priceApplyText").text(applyPrice).attr("val",applyPrice);
                            $priceApplyDiv.hide();
                            $tr.find(".priceDiv").show();

                            $tr.find(".msg-tip").remove();


                        }
                        countTotal();
                    }
                }
            })
        }

        $().ready(function() {
        	
        	/**************************************时间格式化处理************************************/
        	function dateFtt(fmt,date) { 
        		 var o = { 
        		 "M+" : date.getMonth()+1,     //月份 
        		 "d+" : date.getDate(),     //日 
        		 "h+" : date.getHours(),     //小时 
        		 "m+" : date.getMinutes(),     //分 
        		 "s+" : date.getSeconds(),     //秒 
        		 "q+" : Math.floor((date.getMonth()+3)/3), //季度 
        		 "S" : date.getMilliseconds()    //毫秒 
        		 }; 
        		 if(/(y+)/.test(fmt)) 
        		 fmt=fmt.replace(RegExp.$1, (date.getFullYear()+"").substr(4 - RegExp.$1.length)); 
        		 for(var k in o) 
        		 if(new RegExp("("+ k +")").test(fmt)) 
        		 fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length))); 
        		 return fmt; 
        	}
        	
            if(self!=top){
                parent.set_list_tb(1,'',null,true);
            }
            var $areaId = $("#areaId");
            var $addProduct = $("#addProduct");
            var $addContractProduct = $("#addContractProduct");
            var $addElseProduct = $("#addElseProduct");
            var $deleteProduct = $("a.deleteProduct");
            var $inputForm = $("#inputForm");

            $(".typeSystemDictId").val(${type_system_dict_id});
            [#if order.warehouse.typeSystemDict.value == null]
            $(".typeSystemDictValue").html("${type_system_dict_value}");
            [/#if]
            $("input[name='organizationId']").val(${management_organization_id});
            [#if order.warehouse.managementOrganization.name == null]
            $("#organizationId").text("${management_organization_name}");
            [/#if]
            // 表单验证
            $.validator.addClassRules({
                pprice: {
                    required: true
                },
                quantity: {
                    required: true
                },
                pVonderCode: {
                    required: true
                },
                pModel: {
                    required: true
                },
                pName: {
                    required: true
                }
            });

            $("#openStore").click(function(){
                var saleOrgId = $(".saleOrgId").val();
                if(saleOrgId==''){
                    $.message_alert('请选择机构');
                    return false;
                }
                var sbuId = $("#sbuId").val();
                $("#openStore").bindQueryBtn({
                    type:'store',
                    bindClick:false,
                    title:'${message("查询客户")}',
                    url:'/member/store/select_store.jhtml?isMember=1&type=distributor[#if companyId==19]&isSl=1[/#if]',
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $m2_mmGrid.removeRow();
                            if(row.id != $("input[name='storeId']").attr("value") && sbuId !=3 ){
                                $mmGrid.removeRow();
                            }
                            countTotal();
                            $("#businessTypeName option").each(function(){ //遍历全部option
                                if($(this).text() == row.business_type_value){
                                    $(this).attr("selected",true);
                                }
                            });
                            $("input[name='storeId']").attr("value",row.id)
                            $("input[name='storeName']").attr("value",row.name);
                            $("#storeAlias").text(row.alias);
                            $("input[name='consignee']").attr("value",row.store_consignee);
                            $("input[name='phone']").attr("value",row.store_mobile);
                            $("input[name='address']").attr("value",row.store_address);
                            $("input[name='zipCode']").attr("value",row.store_zip_code);
                            $("#outTradeNo").val(row.out_trade_no);
                            $("input[name='saleOrgId']").attr("value",row.sale_org_id);
                            $("#saleOrgName").text(row.sale_org_name);
                            $("input[name='regionalManagerId']").attr("value",row.store_member_id);
                            $("#regionalManagerName").text(row.store_member_name);
                            var areaId = row.store_area;
                            if(areaId==null)areaId='';
                            var areaName = row.area_full_name;
                            if(areaName==null)areaName='';
                            var treePath = row.area_tree_path;
                            if(treePath==null)treePath='';
                            $("input[name='areaId']").val(areaId);
                            $("input[name='areaName']").val(areaName);
                            //可用余额
                            $("#balance").text(currency(0,true));
                            $("#storeBalance").val();
                            //授信
                            $("#credit").text(currency(0,true));
                            //差额
                            $("#chae").text(currency(0,true));
                            //余额
                            $("#yue").text(currency(0,true));
                            //客户经理
                            var id = rows[0].id;
                            ajaxSubmit($(id),{
                                method:'post',
                                url:'select_manager.jhtml',
                                data:{storeId:id},
                                callback:function(resultMsg) {
                                    var rowss = resultMsg.objx;
                                    $("#manager").empty();
                                    for(var i=0;i<rowss.length;i++){
                                        var ro = rowss[i];
                                        $("#manager").prepend("<option value='"+ro.manager+"'>"+ro.manager+"</option>");
                                    }
                                }
                            });
                            //地址外部编码
                            ajaxSubmit($(id),{
                                method:'POST',
                                url:'/member/store/select_store_address_data.jhtml',
                                data:{storeId:id,address:row.store_address},
                                callback:function(resultMsg) {
                                    var data = $.parseJSON(resultMsg.content);//前端
                                    for(var i=0;i<data.content.length;i++){
                                        var storeAddress = data.content[i];
                                        $("input[name='addressOutTradeNo']").attr("value",storeAddress.out_trade_no);
                                    }
                                }
                            });
                            // 查询客户与SBU的价格类型
                            var productIds = $(".productIds").val();
                            ajaxSubmit($(id),{
                                method:'POST',
                                url:'/basic/member_rank/select_store_sbu_data.jhtml',
                                data:{storeId:id},
                                callback:function(resultMsg) {
                                    var ymsbu = $('#sbuName').text();
                                    var data = $.parseJSON(resultMsg.content);
                                    if (data.length > 0){
                                        for(var i = 0; i < data.length; i++){
                                            // 根据客户的SBU来判断当前页面的SBU
                                            if(data[i].sbu_name == $.trim(ymsbu)){
                                                $("#memberRank1").show();
                                                $("#memberRank2").show();
                                                $(".memberRankId").val(data[i].memberRankId);
                                                $(".memberRankName").val(data[i].memberRank);
                                                var memberId= data[i].memberRankId;
	                                            if( productIds != ''&& sbuId ==3 ){
	                          						  updatePrice(id,memberId);
	                          					 } 
                                                break;
                                            }else{
	                                            if( productIds != ''&& sbuId ==3 ){
	                                           		 updatePrice(id,'');
	                                            }
                                                $(".memberRankId").val("");
                                                $(".memberRankName").val(""); 
                                            }
                                        }  
                                    } else {
                                       if( productIds != ''&& sbuId ==3 ){
                                   			 updatePrice(id,'');
                                   	   }
                                       $(".memberRankId").val("");
                                       $(".memberRankName").val("");
                                       $("#memberRank1").hide();
                                       $("#memberRank2").hide();
                                    }
                                }
                            });
                            [#if organizationIsNoWarehouse == 1 ]  
	                            //经营组织余额
	                            var date =new Date();
								//当月第一天
								var firstDay=dateFtt("yyyy-MM-dd",new Date(date.getFullYear(), date.getMonth(), 1));
								$('#startTime').val(firstDay);
								//当天日期
								var today = dateFtt("yyyy-MM-dd",date);
								ajaxSubmit($(id),{
	                               method:'POST',
	                               url:'/finance/balance_report/findvBiBalanceList.jhtml',
	                               data:{storeId:id,saleOrgId:row.sale_org_id,sbuId:sbuId,firstTime:firstDay,lastTime:today},
	                               callback:function(resultMsg) {
	                                   var data = $.parseJSON(resultMsg.content)
	                                   if(data.content.length>0){
	                                	   for (var i = 0; i < data.content.length;i++) {
	                                           $m2_mmGrid.addRow(data.content[i],null,1);
	                                       }
	                                   }
	                               }
	                           });
							[/#if] 	
                        }
                    }
                });
            })

            //地区选择
            $areaId.lSelect();
            
            //打开选择产品界面
            $addProduct.click(function(){
                var $this = $(this);
                var $tr =$this.closest("tr");
                var saleOrgId = $(".saleOrgId").val();
                var sbuId = $(".sbuId").val();
                var memberRankId = $(".memberRankId").val();
                if(memberRankId == undefined){
                    memberRankId = "";
                }
                var storeId = $(".storeId").val();
          	  	if (storeId=='undefined' || storeId.length == 0) {
          	  		$.message_alert('请选择客户');
          			return false;
          	  	}
          	  	var warehouseId = $(".warehouseId").val();
          	  	if (warehouseId=='undefined' || warehouseId.length == 0) {
          	  		$.message_alert('请选择仓库');
	      			return false;
      	  		}
          	  	/* var params='';
          	    [#if organizationIsNoWarehouse == 0 ] 
	          	  	var organizationId = $(".organizationId").val();
	        	  	if (organizationId=='undefined' || organizationId.length == 0) {
	        	  		$.message_alert('经营组织不能为空');
		      			return false;
	    	  		}
	        	  	params+='&organizationId='+organizationId;
        	    [/#if] */	
                $addProduct.bindQueryBtn({
                    type:'product',
                    bindClick:false,
                    title:'${message("查询产品")}',
                    url:'/product/product/selectProduct.jhtml?storeId='+storeId+'&multi=2&isPart=0&typeSystemDictId='+$(".typeSystemDictId").val()+'&isToOrder=true&sbuId='+sbuId+'&memberRankId='+memberRankId,
                    callback:function(rows){
                        var type = true;
                        if(rows.length>0){
                            for (var i = 0; i < rows.length;i++) {
                                var idH = $(".productId_"+rows[i].id).length;
                            }
                            for (var i = 0; i < rows.length;i++) {
                                var row = rows[i];
                                $mmGrid.addRow(row,null,1);
                            }
                            countTotal();
                            editNumber();
                            discountChange();
                            productOrganization();
                        }
                    }
                });
            })
            
            
            //打开选择合同产品界面
            $addContractProduct.click(function(){
                var $this = $(this);
                var $tr =$this.closest("tr");
                var storeId = $(".storeId").val();
                var contractId = $(".contractId").val();
                var saleOrgId = $(".saleOrgId").val();
                var sbuId = $(".sbuId").val();
                var memberRankId = $(".memberRankId").val();
                if(memberRankId == undefined){
                    memberRankId = "";
                }
                if(contractId==""){
                    $.message_alert('请选择合同');
                }else{
                    $addProduct.bindQueryBtn({
                        type:'product',
                        bindClick:false,
                        title:'${message("查询产品")}',
                        url:'/b2b/customerContract/select_contract_item.jhtml?contractId='+contractId,
                        callback:function(rows){
                            var type = true;
                            if(rows.length>0){
                                for (var i = 0; i < rows.length;i++) {
                                    var idH = $(".productId_"+rows[i].id).length;
                                }
                                for (var i = 0; i < rows.length;i++) {
                                    var row = rows[i];
                                    $mmGrid.addRow(row,null,1);
                                }
                                countTotal();
                                editNumber();
                                discountChange();
                            }
                        }
                    });
                }
            })
            
    //查询合同
	$("#selectContract").click(function(){
    	var sbuId = $(".sbuId").val();
    	var storeId = $(".storeId").val();
    	$("#openStore").bindQueryBtn({
	        type:'store',
	        bindClick:false,
	        title:'${message("查询合同")}',
	        url:'/b2b/customerContract/select_contract.jhtml?sbuId='+sbuId+'&storeId='+storeId,
	        callback:function(rows){
	            if(rows.length>0){
	                var row = rows[0];
	                $("input[name='contractId']").attr("value",row.id);
	                $("input[name='contractName']").attr("value",row.contract_name);
	                $("input[name='storeId']").attr("value",row.store_id);
	                $("input[name='storeName']").attr("value",row.store_name);
	                $("input[name='saleOrgId']").attr("value",row.sale_org_id);
                    $("#saleOrgName").text(row.sale_org_name);
	                $("input[name='storeMemberId']").attr("value",row.store_member_id);
	                $("input[name='storeMemberName']").attr("value",row.store_member_name);
	                $("input[name='contacts']").attr("value",row.contacts);
	                $("input[name='phone']").attr("value",row.phone);
	                $("input[name='address']").attr("value",row.address);
	                 $("input[name='distributorId']").attr("value",row.distributor_id);
	                $("input[name='distributorName']").attr("value",row.distributor_name);
	            }
	        }
	    });
    })

        var line_no = 1;
        // 删除产品
        $deleteProduct.live("click", function() {
            var index = $(this).closest("tr").index();
            $.message_confirm('您确定要删除吗？',function(){
                $mmGrid.removeRow(index);
                countTotal();
                var line_number = 1;
                $("span.line_no").each(function(){
                    $(this).html(line_number++);
                });
                line_no--;
            })
        });
        var stockInItemIndex = 0;
    	[#if jsonStr!=null]
    		var orderitem_items = ${jsonStr};
        [#else]
    		var orderitem_items = [];
        [/#if]
            var cols = [
                { title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
                        return '<span class="line_no">'+ line_no +'</span>';
                }},
                { title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
                        return '<a href="javascript:;" class="deleteProduct btn-delete">删除</a>';
                }},
                { title:'${message("12211")}', name:'vonder_code' ,align:'center', width:150, renderer: function(val,item,rowIndex, obj){
               	   	   //木种花色要存
                       var html='<span class="vonderCode">'+val+'</span><input type="hidden" class="text" name="orderItems['+stockInItemIndex+'].woodTypeOrColor" value="'+item.wood_type_or_color+'">';
                       if(item.else==1){
                           html='<input type="text" name="orderItems['+stockInItemIndex+'].vonderCode" class="pVonderCode productId_'+item.vonderCode+' text" value="" />';
                       }
                       return html;
                }},
                { title:'${message("产品描述")}', name:'description' ,align:'center', width:250, renderer: function(val,item,rowIndex, obj){
                        var cartItemId = item.cartItemId;
                        if(cartItemId == undefined || cartItemId == null)cartItemId='';
                        var productId = '';
                        if(item.product != null){
                            productId = item.product;
                        }else {
                            productId = item.id;
                        }
                        var itemId=item.item_id;
                        if(itemId == undefined || itemId == null)itemId='';
                        var product_grade = (item.level_Id!=null)?item.level_Id:498;
                        var str =productId+'_'+product_grade;
                        var url = '/product/product/content.jhtml?flag=0&id='+productId+'&cartItemId='+cartItemId;
                        var	html = '<input type="hidden" name="orderItems['+stockInItemIndex+'].product.id" str="'+str+'" class="productId productId_'+productId+'" value="'+productId+'" />'+
                                  '<input type="hidden" name="orderItems['+stockInItemIndex+'].customerContractItem.id"  class="itemId itemId_'+itemId+'" value="'+itemId+'" />'+ 
                                '<input type="hidden" value="'+item.product_category_id+'" class="productCategory" />'+
                                '<a href="javascript:void(0);" class="red" onclick="top.open_new_tab(this)" data-id="'+productId+'" data-src="'+url+'" data-name="<marquee scrollamount=\'2\'>'+item.name+'</marquee>" reflush="false" data-title="'+item.name+'">'+val+'</a>';
                        return html;
                }},
                { title:'${message("单位")}', align:'center',name:'unit',renderer: function(val,item,rowIndex, obj){
                       var unit = val;
                       if(obj == undefined){
                           unit = item.pUnit;
                       }
                       if(item.unit != undefined&&item.unit!=""){
                           unit = item.unit;
                       }
                       return unit;
                }},
                { title:'${message("产品分类")}', align:'center',name:'product_category_name',renderer: function(val,item,rowIndex, obj){
                        return val;
                }},
                { title:'${message("工厂/供应商")}',[#if sbu.id != 3||isMember==1 ]hidden:true,[/#if] name:'manufactory_name' ,align:'center', width:250,renderer: function(val,item,rowIndex, obj){
                	return '<span class="text">'+item.manufactory_name+'</span>'
                }},
                { title:'${message("供应商型号")}',[#if sbu.id != 3||isMember==1 ]hidden:true,[/#if] name:'supplier' ,align:'center', width:100,renderer: function(val,item,rowIndex, obj){
                	return '<span class="text">'+item.supplier+'</span>'
                }},
                { title:'${message("产品型号")}', name:'model' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
                     var html=val+'';
                     if(item.else==1){
                         html='<input type="text" name="orderItems['+stockInItemIndex+'].model" class="pModel productId_'+item.model+' text" value="" />';
                     }
                     return html;
                 }},
                 { title:'${message("经营组织")}',width:150, align:'center', renderer: function(val,item,rowIndex,obj){
	 	     			var html='';
	 	     			[#if organizationIsNoWarehouse == 0 ] 
	 	     				html +='<span class="productOrganizationName"></span>'+
	 	     				       '<input type="hidden" name="orderItems['+stockInItemIndex+'].productOrganization.id" class="productOrganizationId text" value="" />';
	 	     			[#elseif organizationIsNoWarehouse == 1]  
		 	     			html +='<select name="orderItems['+stockInItemIndex+'].productOrganization.id" class="text productOrganization">';
				     			if(obj==undefined){
				     				var productOrganizationName = '';
				     				if(item.product_organization_name != null && item.product_organization_name !=''){
				     					productOrganizationName = item.product_organization_name;
				     				}
				     				if(item.product_organization_id != null && item.product_organization_id !=''){
				     					html+='<option value="'+item.product_organization_id+'" selected="selected" >'+productOrganizationName+'</option> ';
				     				}
				    			}
		 					html+='</select>';
	 	     			[/#if]
	 	        		return html;
 	     		 }}, 
                 { title:'${message("产品等级")}', name:'level_Id' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                        var str='selected="selected"';
            			var html='<select name="orderItems['+stockInItemIndex+'].productLevel.id" class="text productGrade" id="productGrade" onchange="product_price(this)">';
            				[#list productLevelList as products]
            				if(${products.id}==item.level_Id){
            					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
            				}else{
            					html+='<option value="${products.id}">${products.value}</option> ';
            				}
            				[/#list]
            				html+='</select>'+'<input type="hidden" value="'+ item.level_Id +'" class="t productGrades"/>';
            			return html; 
                 }},
                 { title:'${message("下单箱数")}',[#if sbu.id == 3 ]hidden:true,[/#if] name:'box_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
                        var quantity = 1;
                        if(obj==undefined){
                            quantity = val;
                        }
                        var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);

                        if(type==1||type==3){
                            return '-'+
                                    '<input type="hidden"  class="t boxQuantity"/>'+
                                    '<input type="hidden" name="orderItems['+stockInItemIndex+'].boxQuantity" value="0" >';
                        }else{
                            var text = '<div class="lh20">'+
                                    '<div class="nums-input ov">'+
                                    '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                                    '<input type="text" kid="box" class="t boxQuantity"  name="orderItems['+stockInItemIndex+'].boxQuantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                                    '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                                    '</div>';
                            text += '</div>';
                            return text;
                        }
                    }},
                    { title:'${message("支数")}',[#if sbu.id == 3 ]hidden:true,[/#if] name:'branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                        var branchQuantity='';
                        if(obj==undefined){
                            branchQuantity = val;
                        }
                        var branch_per_box='';
                        var  per_branch='';
                        var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);

                        if(type==1||type==2){
                            branch_per_box=0;
                            per_branch=0;
                            return '-'+'<input type=hidden name="orderItems['+stockInItemIndex+'].perBranch" value="0" />';
                        }else{
                            var text = '<div class="lh20">'+
                                    '<div class="nums-input ov">'+
                                    '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                                    '<input type="text" kid="branch" class="t branchQuantity"  name="orderItems['+stockInItemIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                                    '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                                    '<input type=hidden class="perBranch" name="orderItems['+stockInItemIndex+'].perBranch" value="'+item.per_branch+'" />';
                            if(type==3||type==0){
                                text += '<input type=hidden class="branchPerBox" name="orderItems['+stockInItemIndex+'].branchPerBox" value="'+item.branch_per_box+'" /> ';
                            }
                            text += '</div></div>';
                            return text;
                        }

                    }},
                    { title:'${message("零散支数")}',[#if sbu.id == 3 ]hidden:true,[/#if] name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
                        var branchQuantity='';
                        if(obj==undefined){
                            branchQuantity = val;
                        }
                        var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
                        if(type==2||type==3||type==1){
                            return '-';
                        }
                        var html='<span class="scatteredQuantityStr">0</span>'+
                                '<input type="hidden" name="orderItems['+stockInItemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="0" />';
                        return html;
                    }},
 				    { title:'宽', [#if sbu.id != 3]hidden:true,[/#if] align:'center',name:'length',renderer: function(val,item,rowIndex, obj){ 
		 				 var sbu=0;
		 				 [#if sbu.id != null]
		 					sbu=${sbu.id}
		 				 [/#if]
		                 var length=0;
		                 if(item.length!='' && item.length!=undefined){
		                   	length=item.length;
		                 }
		                 if(isNaN(length)){
		                	 length=0;
		                 }
		                 if(sbu==3 && (item.unit=='m2' || item.pUnit=='m2') ){
						      var hideStr = '';
		   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
								'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
								'<input type="text" name="orderItems['+stockInItemIndex+'].length" kid="length" class="length t"  value="'+length+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
								'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
								'</div></div>';
						    
						 }else{
						      return '-'+'<input type="hidden" name="orderItems['+stockInItemIndex+'].length" kid="length" class="length t"  value="0" />';  
						 }
				   }},
				   { title:'高', [#if sbu.id != 3]hidden:true,[/#if] align:'center',name:'width',renderer: function(val,item,rowIndex, obj){
	                	var sbu=0;
	     				[#if sbu.id != null]
	     					sbu=${sbu.id}
	     				[/#if]
	                	var width=0;
	                	if(item.width!='' && item.width!=undefined){
	                  		width=item.width;
	               	     }
	               	    if(isNaN(width)){
	               			  width=0;
	                	}
	                	
	                	if((item.unit=='m2' || item.pUnit=='m2')  && item.isWare==0 && sbu==3){
	                	 var hideStr = '';
	   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
							'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
							'<input type="text" name="orderItems['+stockInItemIndex+'].width" kid="width" class="width t"  value="'+width+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
							'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
							'</div></div>';
					
					    }else if(sbu==3  && (item.unit=='m2' || item.pUnit=='m2')){
	            		  var hideStr = '';
							return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
							'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
							'<input type="text" name="orderItems['+stockInItemIndex+'].width" kid="width" class="width t"  value="'+width+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
							'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
							'</div></div>';
						 }else{
					      return '-'+'<input type="hidden" name="orderItems['+stockInItemIndex+'].width" kid="width" class="width t"  value="0" />';
					    }
				   }},
                { title:'${message("数量")}', name:'quantity', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
                        var quantity='';
                        if(obj==undefined){
                            quantity = val;
                        }else if(item.per_box!=undefined){
                            quantity = item.per_box/10;
                        }
                        var sbu=0;
         				[#if sbu.id != null]
         					sbu=${sbu.id}
         				[/#if]
                        var width=0;
                	  	if(item.width!='' && item.width!=undefined){
                  			width=item.width;
               			 }
               			 if(isNaN(width)){
               			  width=0;
                		}
                		   var length=0;
              			  if(item.length!='' && item.length!=undefined){
                 			 length=item.length;
              			  }
               			 if(isNaN(length)){
               			  length=0;
              			  }
              			  
                       if(item.unit=='m2' && sbu==3){
                          quantity = accMul(width,length);
                       }
                       if(quantity==''){
                       		quantity=0;
                       }
                        
                        
                       if(item.unit=='m2' && sbu==3){
		                     var text = '<div class="lh20">'+
		                                '<div class="nums-input ov square">'+
		                              	'<span class="text quantity_span" >'+quantity+'</span>'+
		                               '<input type="hidden" kid="quantity" class="t quantity"  name="orderItems['+stockInItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		                               '<input type="hidden" class="minPriceApplyQuantity" />'+
		                                '<input type="hidden" class="maxPriceApplyQuantity" />'+
		                              '<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
		                                '</div></div>';
		                       return text;
                        }else{
	                         var text = '<div class="lh20">'+
	                                '<div class="nums-input ov square">'+
	                                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	                                '<input type="text" kid="quantity" class="t quantity"  name="orderItems['+stockInItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	                                '<input type="hidden" class="minPriceApplyQuantity" />'+
	                                '<input type="hidden" class="maxPriceApplyQuantity" />'+
	                                '<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
	                                '</div></div>';
	                       	 return text;
                        }
                    }},
                { title:'${message("价格表商品原价")}',hidden:'true', name:'price' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
                        var price = item.price;
                        if(obj == undefined && item.price!=''){
                            price = item.price;
                        }else if(item.member_price!=''){
                            price = item.member_price;
                        }
                        
                        if(price==undefined){
                        price=0;
                        }
                        html='<input type="hidden" name="orderItems['+stockInItemIndex+'].proPriceHeadPrice" class="proPriceHeadPrice" value="'+price+'" />'+price;
                        return html;
                }},
        		{title:'平台结算价', align:'center',name:'',[#if isMember!=0]hidden:true,[/#if]renderer: function(val,item,rowIndex){
        		        var sale_org_price=item.sale_org_price==null?'':item.sale_org_price;
        		        [#if editSaleOrgPrice==1]
        		        	return '<div class="nums-input ov">'+
        		            '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
        		            '<input type="text"  class="t text saleOrgPrice"  name="orderItems['+stockInItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
        		            '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
        		            '</div>';
        		        [/#if]
        		        return '<span class="text red saleOrgPriceText">'+currency(item.sale_org_price,true)+'</span><input type="hidden"  class="t text saleOrgPrice"  name="orderItems['+stockInItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" >'
        		}},
                { title:'${message("单价")}', name:'price' ,align:'center', width:100,renderer:function(val,item,rowIndex, obj){
                        var html = '<div class="pd">';
                        var hideStr = '';
                        var disStr = '';
                        var price = item.price;
                        var sale_org_price = item.sale_org_price;
                        if(sale_org_price== null) sale_org_price='';
                        var nprice=0;
                        if(obj == undefined && item.price!=''){
                            price = item.price;
                            nprice = price;
                        }else if(item.member_price!='' && item.member_price!=undefined){
                            price = item.member_price;
                            nprice=price;
                        }else{
                            nprice=item.price;
                        }
                        var fromProPriceHeadPrice = item.fromProPriceHeadPrice;
                        if (fromProPriceHeadPrice != '' && fromProPriceHeadPrice != undefined) {
                            price = fromProPriceHeadPrice;
                        }
			[#if editProductPrice==1]
			html+= '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
                    '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editAcount (this.nextSibling,event)">'+
                    '<input type="text" min=0 name="orderItems['+stockInItemIndex+'].price" kid="price" class="price t" value="'+price+'" org="'+nprice+'" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" />'+
                    '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editAcount (this.previousSibling,event)">'+
                    '</div></div>';

			html+='</div>';
            [#else]
			html+= '<div class="priceDiv" '+hideStr+'>'+
                    '<input type="hidden" min=0 name="orderItems['+stockInItemIndex+'].price" class="price text" value="'+price+'" org="'+nprice+'"  />'+
                    '<span class="red price_span" val="'+currency(price,true)+'">'+currency(price,true)+'</span>'+
                    '</div>';

			html+='</div>';
            [/#if]
                        return html;
                    }},
                 { title:'折扣率%', [#if isMember!=0 || sbu.id != 3]hidden:true,[/#if] align:'center',name:'',renderer: function(val,item,rowIndex, obj){
                 var editDiscount=${editDiscount};
             	 var hideStr = '';
				if(editDiscount==1){
					return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
					'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editAcount (this.nextSibling,event)">'+
					'<input type="text" name="orderItems['+stockInItemIndex+'].discount" kid="discount" class="discount t"  value="100" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)"/>'+
					'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editAcount (this.previousSibling,event)">'+
					'</div></div>';
				
				}else{
				 return '<input type="hidden" name="orderItems['+stockInItemIndex+'].discount"  class="text discount" btn-fun="clear" value="100" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)"/><span class="red discount">'+val+'</span>'
				}
				 }},
                {title:'${message("折扣方案")}',[#if isMember!=0 || sbu.id != 3]hidden:true,[/#if] align:'center',name:'account_type',width:130  ,renderer: function(val,item,rowIndex){			
	 		 	  var html = '<select id="discountCode" name="orderItems['+stockInItemIndex+'].discountCode.id" class="text discountCode">'
				[#list discounts as discount]
					+'<option value="${discount.id}" >${discount.value}</option> '
				[/#list]
				+'</select>';
	      		  return html;
      	  		}},
                { title:'金额', align:'center',name:'',[#if isMember!=0]hidden:true,[/#if]renderer: function(val,item,rowIndex, obj){
                        var amountStr = '';
                        if(obj==undefined){
                            amountStr = currency(item.quantity*item.price,true);
                        }
                        return '<sapn class="text red trprice">'+amountStr+'</span>'
                    }},
                    
                [#if hiddenBatch !=0]  
	                { title:'${message("色号")}', align:'center',name:'colour_number',renderer: function(val,item,rowIndex, obj){
			        	var colourNumber='<input type="text" name="orderItems['+stockInItemIndex+'].colourNumber" class="text colourNumber" value="'+item.colour_number+'" />';
						return colourNumber;
			        }},    
			        { title:'${message("含水率")}', align:'center',name:'moisture_content',renderer: function(val,item,rowIndex, obj){
			        	var moistureContent='<input type="text" name="orderItems['+stockInItemIndex+'].moistureContent" class="text moistureContent" value="'+item.moisture_content+'" />';
						return moistureContent;
			        }},
			        { title:'${message("批次")}', align:'center',name:'batch',renderer: function(val,item,rowIndex, obj){
			        	var batch='<input type="text" name="orderItems['+stockInItemIndex+'].batch" class="text" value="'+item.batch+'" />';
						return batch;
			        }},  
                [/#if]  
                { title:'${message("体积")}',[#if sbu.id == 3 ]hidden:true,[/#if] name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        var volume=val;
                        if(volume==''){
                            volume=0;
                        }
                        return '<span class="lineVolumeAmount"></span><input class="lineVolume" value="'+volume+'" type="hidden" />';
                }},
                { title:'${message("重量")}',[#if sbu.id == 3 ]hidden:true,[/#if] name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        var weight=val;
                        if(weight==''){
                            weight=0;
                        }
                        return '<span class="lineWeightAmount"></span><input class="lineWeight" value="'+weight+'" type="hidden" />';
                }},
                { title:'${message("特价单号")}',[#if sbu.id == 3 ]hidden:true,[/#if] name:'' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        var price_apply_sn = '';
                        var price_apply_id = 0;
                        if(obj==undefined){
                            price_apply_sn = (item.price_apply_sn!=null)?item.price_apply_sn:'';
                            price_apply_id = (item.price_apply_item!=null)?item.price_apply_item:0;
                        }
                        var html = '<span class="search ">'+
                                '<input class="text linePriceApplyItemId linePriceApplyItemId" type="hidden" name="orderItems['+stockInItemIndex+'].priceApplyItem.id" value="'+price_apply_id+'">'+
                                '<input class="text linePriceApplyItemSn linePriceApplyItemSn" maxlength="200" type="text" readonly value="'+price_apply_sn+'" onkeyup="clearSelect(this)">'+
                                '<input type="button" class="iconSearch lineSelectPriceApply" value="">'+
                                '</span>';
                        return html;
                }},
                { title:'交货期',[#if sbu.id == 3 ]hidden:true,[/#if] align:'center',name:'',renderer: function(val,item,rowIndex, obj){
                        var str1 = '';
                        if(val!=''){
                            str1 = val.substring(0,10);
                        }
                        return '<input type="text" class="text" name="orderItems['+stockInItemIndex+'].deliveryTime" value="'+str1+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})"/>';
                }},
                { title:'${message("库存数量")}',align:'center', width:110, renderer: function(val,item,rowIndex, obj){
    	 			return '<span class="onhandQuantity">0</span>';
    		  	}},
    		  	{ title:'${message("可用数量")}',align:'center', width:110, renderer: function(val,item,rowIndex, obj){
    			 	return '<span class="attQuantity">0</span>';
    		    }},
                { title:'${message("卖家备注")}', align:'center',name:'seller_memo',renderer: function(val,item,rowIndex, obj){
                        return '<input type="text" name="orderItems['+stockInItemIndex+'].sellerMemo" class="text" value="'+val+'">'+val;
                }},
                {  title:'${message("")}',hidden:'true', align:'center', renderer:function(val,item,rowIndex,obj){
                        stockInItemIndex++;
                        line_no++;
                        return '<input type="hidden">';
                 }},
            ];
            $mmGrid = $('#table-m1').mmGrid({
                height:'auto',
                cols: cols,
                fullWidthRows:true,
                checkCol: false,
                items:orderitem_items,
                autoLoad: true,
                callback:function(){
                    countTotal();
                    editNumber();
                    productOrganization();
                }
            });
            
            var m2_cols = [ 
				  { title:'${message("经营组织")}' , name:'organization', align:'center', width:100},      
                  { title:'余额', align:'center',renderer: function(val,item,rowIndex, obj){
                          var amountStr = '';
                          if(item.qmys ==null || item.qmys =='' || item.qmys =='undefined'){
                              amountStr = currency(0,true);
                          }else{
                        	  amountStr = currency(item.qmys,true);
                          }
                          return '<sapn class="text">'+amountStr+'</span>'
                   }}, 
                   { title:'可用余额', align:'center',renderer: function(val,item,rowIndex, obj){
                       var amountStr = '';
                       if(item.balance ==null && item.balance =='' && item.balance =='undefined'){
                           amountStr = currency(0,true);
                       }else{
                    	   amountStr = currency(item.balance,true);
                       }
                       return '<sapn class="text">'+amountStr+'</span>'
                	}},
                	{ title:'授信', align:'center',renderer: function(val,item,rowIndex, obj){
                		var amountStr = item.sx;
                        if(item.sx ==null && item.sx =='' && item.sx =='undefined'){
                            amountStr = currency(0,true);
                        }else{
                        	amountStr = currency(item.sx,true);
                        }
                        return '<sapn class="text">'+amountStr+'</span>'
                 	}}
             ];
                     
            $m2_mmGrid = $('#table-m2').mmGrid({
	        		height:'auto',
	        		cols: m2_cols,
	        		fullWidthRows:true,
	        		checkCol: false,
	        		autoLoad: true,
            });
            
            $addElseProduct.click(function(){
                var $storeId = $(".storeId").val();
                if($storeId==""){
                    $.message_alert('${message("请选择客户")}');
                }else{
                    var row=[{"else":"1"}];
                    $mmGrid.addRow(row);
                    countTotal();
                }
            })

            //产品等级
            $(".productGrade").live("change", function() {
                var $tr=$(this).closest("tr");
                var $productId=$tr.find(".productId");
                var productId=$productId.val();
                var productGrade=$(this).val();
                $productId.attr("str",productId+"_"+productGrade);
            });

            /**初始化订单附件*/
                    //var orderAttach_items = ${orderAttach_json};
            var orderAttachIndex=0;
            var cols = [
                { title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex, obj){
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['orderAttachs['+orderAttachIndex+'].url']=url;
                        hideValues['orderAttachs['+orderAttachIndex+'].suffix']=fileObj.suffix;

                        return createFileStr({
                            url : url,
                            fileName : fileObj.file_name,
                            name : fileObj.name,
                            suffix : fileObj.suffix,
                            time : '',
                            textName:'orderAttachs['+orderAttachIndex+'].name',
                            hideValues:hideValues
                        });
                    }},
                { title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex, obj){
                        return '<div><textarea class="text file_memo" name="orderAttachs['+orderAttachIndex+'].memo" >'+val+'</textarea></div>';
                    }},
                { title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex, obj){
                        orderAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }}
            ];
            var $amGrid=$('#table-attach').mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });


            //选择特价
            $(".lineSelectPriceApply").live("click",function(){
                var $this = $(this);
                var $tr =$this.closest("tr");
                var productId=$tr.find(".productId").val();
                var productGrade=$tr.find(".productGrade").val(); //获取Select选择的Value
                var storeId=$(".storeId").val();
                var productCategory=$tr.find(".productCategory").val();
                var typeSystemDictId=$(".typeSystemDictId").val();
                var businessTypeName = $("#businessTypeName").val();
                var saleOrgId = $(".saleOrgId").val();
                var organizationId = $(".organizationId").val();
                $this.bindQueryBtn({
                    bindClick:false,
                    type:'priceApply',
                    title:'${message("查询特价")}',
                    url:'/b2b/priceApply/select_price_apply_item.jhtml?storeId='+storeId+'&productId='+productId+'&productCategoryId='+productCategory+'&typeSystemDictId='+typeSystemDictId+'&businessTypeName='+businessTypeName+'&productGrade='+productGrade+'&saleOrgId='+saleOrgId+'&organizationId='+organizationId,
                    callback:function(rows){
                        if(rows.length>0){
                            var $price_box = $tr.find(".price-box");
                            var price;
                            if($price_box.length==0 || $price_box.prop("checked")==false){
                                $tr.find("input.price").val(rows[0].price);
                                $tr.find("input.saleOrgPrice").val(rows[0].sale_org_sales_price);
                            }else{
                                $tr.find("input.origMemberPrice ").val(rows[0].price);
                            }

                            $tr.find(".price_span").html(currency(rows[0].price,true));
                            $tr.find(".price_span").attr("val",rows[0].price);
                            $tr.find(".priceText").html(currency(rows[0].price,true));
                            $tr.find(".priceText").attr("val",rows[0].price);
                            if (rows[0].level_Id != undefined) {
                                $tr.find(".productGrade").attr("value",rows[0].level_Id);
                            }
                            //设置数量
                            var quantity=$tr.find(".quantity").val();
                            var boxQuantity=$tr.find(".boxQuantity").val();//下单箱数
                            if(quantity<rows[0].min_quantity){
                                $.message_alert('数量【'+quantity+'】小于特价单最小数量【'+rows[0].min_quantity+'】');
                                return false;
                            }else if(quantity>rows[0].max_quantity){
                                $.message_alert('数量【'+quantity+'】大于特价单最大数量【'+rows[0].max_quantity+'】');
                                return false;
                            }
                            $tr.find(".minPriceApplyQuantity").val(rows[0].min_quantity);
                            $tr.find(".maxPriceApplyQuantity").val(rows[0].max_quantity);

                            if(rows[0].type_name=="工程"){
                                $("#businessTypeId option").each(function(){
                                	if($(this).text() == "商业地板"){
                                		$(this).attr("selected","selected");
                                	}
                                });
                            }
                            $tr.find(".linePriceApplyItemId").val(rows[0].id);
                            $tr.find(".linePriceApplyItemSn").val(rows[0].sn);
                            countTotal();
                        }
                    }
                });
            })

            var $addAttach = $("#addAttach");
            var attachIdnex = 0;
            var option1 = {
                dataType: "json",
                uploadToFileServer:true,
                uploadSize: "fileurl",
                callback : function(data){
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth()+1;
                    var day = date.getDate();
                    var time = year+'-'+month+'-'+day;
                    for(var i=0;i<data.length;i++){
                        var row = data[i].file_info;
                        $amGrid.addRow(row,null,1);
                    }

                }
            }
            $addAttach.file_upload(option1);

            var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
            });
            //打开选择地址界面
            $("#addReceiveAddress").click(function(){
                var storeId = $(".storeId").val();
                if(storeId==""){
                    $.message_alert('请选择客户');
                }else{
                    select_store_address(storeId);
                }
            });

            //更换地址
            function select_store_address(storeId){
                $("#addReceiveAddress").bindQueryBtn({
                    type:'store',
                    bindClick:false,
                    title:'${message("更换地址")}',
                    url:'/member/store/select_store_address.jhtml?storeId='+storeId,
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $("input[name='consignee']").attr("value",row.consignee);
                            $("input[name='phone']").attr("value",row.mobile);
                            $("input[name='address']").attr("value",row.address);
                            $("input[name='zipCode']").attr("value",row.zip_code);
                            $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
                            $(".select_area").find(".fieldSet").empty();
                            var areaId = row.area;
                            if(areaId==null)areaId='';
                            var areaName = row.area_full_name;
                            if(areaName==null)areaName='';
                            var treePath = row.tree_path;
                            if(treePath==null)treePath='';
                            $("input[name='areaId']").val(areaId);
                            $("input[name='areaName']").val(areaName);
                            $("input[name='salesAreaName']").val(row.area_full_name);
                        }
                    }
                });
            }
            

            $("form").bindAttribute({
                isConfirm:true,
                callback: function(resultMsg){
                    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                        location.href= 'view.jhtml?readOnly=1&id='+resultMsg.objx;
                    })
                }
            });

            
            //查询机构
            $("#selectSaleOrg").bindQueryBtn({
                type:'saleOrg',
                title:'${message("查询机构")}',
                url:'/basic/saleOrg/select_saleOrg.jhtml'
            });

            
            $("#openArea").bindQueryBtn({
                type:'area',
                title:'${message("查询地区")}',
                url:'/basic/area/select_area.jhtml',
                callback:function(rows){
                    if(rows.length>0){
                        var $tr =$this.closest("tr");
                        $(".areaId").val(rows[0].id);
                        $(".areaName").val(rows[0].full_name);
                        //$("input[name='address']").val('');
                        $("input[name='zipCode']").val('');
                    }
                }
            });

            //查询仓库
            $("#selectWarehouse").click(function(){
                var $saleOrgId = $(".saleOrgId").val();
                var $organizationId = $("#organizationId").val();
                var sbuId = $(".sbuId").val();
                if($saleOrgId==""){
                    $.message_alert('请选择机构');
                }else{
                    $("#selectWarehouse").bindQueryBtn({
                        type:'warehouse',
                        bindClick:false,
                        title:'${message("查询仓库")}',
                        url:'/stock/warehouse/select_warehouse.jhtml?saleOrgId='+$saleOrgId+'&organizationId='+$organizationId+'&sbuId='+sbuId,
                        callback:function(rows){
                            if(rows.length>0){
                                var row = rows[0];
                                $(".warehouseId").val(row.id);
                                $(".warehouseName").val(row.name);
                                $(".typeSystemDictId").val(row.type_system_dict_id);
                                $(".typeSystemDictValue").html(row.type_system_dict_value);
                                $("input[name='organizationId']").val(row.management_organization_id);
                                $("#organizationId").text(row.management_organization_name);
                                var ssId = $("input[name='storeId']").val();
                                var saleOrgId = $("input[name='saleOrgId']").val();
                                //sbu
                                var sbuId = $(".sbuId").val();
                                ajaxSubmit($(row.id),{
                                    method:'post',
                                    url:'/finance/balance/get_balance.jhtml',
                                    data:{storeId:ssId,sbuId:sbuId,saleOrgId:saleOrgId,organizationId:row.management_organization_id},
                                    callback:function(resultMsg) {
                                        var data = resultMsg.objx;
                                        //可用余额
                                        $("#balance").text(currency(data.balance,true));
                                        $("#storeBalance").val(data.balance);
                                        //授信
                                        $("#credit").text(currency(data.credit_amount,true));
                                        //余额
                                        $("#yue").text(currency(data.balance-data.credit_amount,true))
                                    }
                                })
                                if(sbuId !=3){
                                    $mmGrid.removeRow();
                                }
                                countTotal();
                            }
                        }
                    });
                }
            })

            $("#paymentMethodId").change(function(){
                if($("#paymentMethodId").val()==69){
                    $("#paybutton").show();
                }else{
                    $("#paybutton").hide();
                }
            })


            $("#selectMemberRank").click(function(){
                var saleOrgId = $(".saleOrgId").val();
                var sbuId = $(".sbuId").val();
                //查询价格等级
                $("#selectMemberRank").bindQueryBtn({
                    type:'memberRank',
                    bindClick:false,
                    title:'${message("查询价格等级")}',
                    url:'/basic/member_rank/select_memberRank_price.jhtml?saleOrgId='+ saleOrgId + '&sbuId=' + sbuId,
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $(".memberRankId").val(row.id);
                            $(".memberRankName").val(row.name);
                        }
                    }
                });
            });
            
            [#if linkStock == 1 ] 
	          	//产品经营组织
				 $(".productOrganization").live("change", function() {
						productOrganizationChange(this);	
				 })
				//产品等级
				/* $(".productGrade").live("change", function() {
						productOrganizationChange(this);	
				 }) */
				 //色号
				 $("input.colourNumber").live("change", function() {
						productOrganizationChange(this);	
				 })
				 //含水率
				 $("input.moistureContent").live("change", function() {
						productOrganizationChange(this);	
				 })
			[/#if]
        });

    	//经营组织点击触发事件
    	function productOrganizationChange(e){
    		var warehouseId = $("input.warehouseId").val();
    		var $tr = $(e).closest("tr");
    		//产品经营组织
    		var productOrganizationId = $tr.find(".productOrganization").val();
    		if(productOrganizationId !=null && productOrganizationId !='' && productOrganizationId !='undefined'){
    			//等级
    			var productGrade = $tr.find(".productGrade").val();
    			//编码
    			var vonderCode = $tr.find(".vonderCode").text();
    			//色号
    			var colourNumber = $tr.find(".colourNumber").val();
    			if(colourNumber=='undefined' || colourNumber.length == 0){
    				colourNumber = "";
    			}
    			//含水率
    			var moistureContent = $tr.find(".moistureContent").val();
    			if(moistureContent=='undefined' || moistureContent.length == 0){
    				moistureContent = "";
    			}
    			var params='&productGrade='+productGrade+'&vonderCode='+vonderCode+'&colourNumber='+colourNumber
    			   +'&moistureContent='+moistureContent+'&organizationId='+productOrganizationId+'&warehouseId='+warehouseId;
    				params = params.substring(1,params.length);
    			$.ajax({
    				url:'/stock/stock/findViewStock.jhtml?'+params,
    	   			type : "post",
    	   			success : function(rows) {
    	   				var data= $.parseJSON(rows.content);
    	                   if(data.length>0){
    	                       for (var i = 0; i < data.length;i++) {
    	                       	if(data[i].totalOnhandQuantity1 !=null && data[i].totalOnhandQuantity1 !=''){
    	                       		$tr.find(".onhandQuantity").text(data[i].totalOnhandQuantity1);
    	                       	}else{
    	                       		$tr.find(".onhandQuantity").text(0);
    	                       	}
    	                       	if(data[i].totalAttQuantity1 !=null && data[i].totalAttQuantity1 !=''){
    	                       		$tr.find(".attQuantity").text(data[i].totalAttQuantity1);
    	                       	}else{
    	                       		$tr.find(".attQuantity").text(0);
    	                       	} 
    	                   	}
    	               	}else{
    	               		$tr.find(".onhandQuantity").text(0);
    	               		$tr.find(".attQuantity").text(0);
    	               	}
    	   			}
    			})	
    		}
    	} 
        
        function save(e, flag){
            var $input = $("input.price");
            var str;
            var count = 0;
            var err=0;
            var saleOrgName = $("input[name='saleOrgName']").val();
            var models = $("input.models").val();
            var organizationId =  $("input[name='organizationId']").val();
            var warehouseName = $("input[name='warehouseName']").val();
            if(models!=''){
                 $.message_alert('产品型号为['+models+']的产品在价格表中不存在,请删除后维护价格再选择');
                return false;
            }
            $input.each(function(){
                var p = $(this).val();
                var $tr=$(this).closest("tr");
                var minPriceApplyQuantity=$tr.find(".minPriceApplyQuantity").val();
                var maxPriceApplyQuantity=$tr.find(".maxPriceApplyQuantity").val();
                var length = $tr.find(".length").val();
                var width = $tr.find(".width").val();
                var quantity=$tr.find(".quantity").val();
                var branchQuantity=$tr.find(".branchQuantity").val();
                var boxQuantity = $tr.find(".boxQuantity").val();
                var sbu = $("#sbuId").val();
                if(sbu != 3){
                    if(branchQuantity%1!=0){
                        if(branchQuantity != undefined){
                            str='支数不允许是小数，必须为整数';
                            err++;
                            return false;
                        }
                    }
                }
                if(boxQuantity%1!=0){
                    if(boxQuantity != undefined&&!isNaN(boxQuantity)){
                        str='箱数不允许是小数，必须为整数';
                        err++;
                        return false;
                    }
                }
                if(minPriceApplyQuantity!=''&&Number(quantity)<Number(minPriceApplyQuantity)){
                    str='数量【'+quantity+'】小于特价单最小数量【'+minPriceApplyQuantity+'】';
                    err++;
                    return false;

                }
                if(maxPriceApplyQuantity!=''&&Number(quantity)>Number(maxPriceApplyQuantity)){
                    str='数量【'+quantity+'】大于特价单最大数量【'+maxPriceApplyQuantity+'】';
                    err++;
                    return false;
                }
                if(p == 0 ){
                    count++;
                }
            })
            if(err!=0){
                $.message_alert(str);
                return false;
            }
            if(saleOrgName==""){
                str ='机构为空，请检查'
                $.message_alert(str);
                return false;
            }
            if(organizationId==""){
                str ='经营组织为空，请检查'
                $.message_alert(str);
                return false;
            }
            if(warehouseName==""){
                str ='仓库为空，请检查'
                $.message_alert(str);
                return false;
            }
            var errmsg=0;
            var sum = $("input.productId").length;
            if(sum<1){
                $.message_alert("订单明细不能少于一条");
                return false;
            }else{
                var str=[0];
                $(".productId").each(function(){
                    var required=$(this).attr("str");
                    str.push(required);
                });
            }
            if(errmsg!=0){
                return false;
            }
            
            //判断备注长度
			var memeo=$(".memo").val();
	 		var len = 0;  
	   		for(var i=0; i<memeo.length; i++) {   
	   			  	var c = memeo.charCodeAt(i);   
			  		//单字节加1   
			   	    if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
			    		   len++;   
			   		}else{   
			   		   len+=2;   
			   		}   
	  		}   
  		    if(len>80){
	     		 $.message_alert("订单备注超过240个字符！");
	     		 return false;
   		    }
			//	if(count == 0){
            str = '您确定要保存吗？'
			//	}else{
			//		str = '您当前的订单包含价格为&nbsp;&nbsp;<b class="red">0</b>&nbsp;&nbsp;且非赠品的产品!';
			//		$.message_alert(str);
			//		return false;
			//	}
            var url = '';
            if (flag == 0) {
                url = 'save.jhtml?stat=0';
            }
            else {
                url = 'save.jhtml?stat=1';
            }
            var $form = $("#inputForm");
            if($form.valid()){
                ajaxSubmit(e,{
                    url: url,
                    data:$("#inputForm").serialize(),
                    method: "post",
                    isConfirm:true,
                    confirmText:str,
                    callback:function(resultMsg){
                        var orderId=resultMsg.objx;
                        if(resultMsg.type!="success"){
                            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                location.reload(true);
                            })
                            return false;
                        }
                        //flag==1  下达    start --------
                        if(flag==1){
                            //-------begin-------
                            check_wf(orderId);
                            /*
                            var url="check_wf.jhtml";
                            var data = $("#inputForm").serialize();
                            ajaxSubmit('',{
                                url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=47&objid='+orderId,
                                method: "post",
                                callback:function(resultMsg){
                                    var rows = resultMsg.objx;
                                    if(rows.length>=1){
                                        data = data+'&objConfId='+rows[0].id+'&orderId='+orderId;
                                        ajaxSubmit('',{
                                            url: url,
                                            method: "post",
                                            data: data,
                                            callback:function(resultMsg){
                                                $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                                    location.href= 'view.jhtml?readOnly=1&id='+orderId;
                                                });
                                            }
                                        });
                                    }else{
                                        var str = '';
                                        for(var i=0;i<rows.length;i++){
                                            var row = rows[i];
                                            str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
                                        }
                                        var content = '<table class="input input-edit" style="width:100%">'
                                                +'<tbody><tr><th>流程模版</th>'
                                                +'<td>'
                                                +'<select class="text" id="objConfId">'
                                                +str
                                                +'</select>'
                                                +'</td>'
                                                +'</tr></tbody></table>';
                                        var $dialog_check = $.dialog({
                                            title:"${message("正式订单审核")}",
                                            height:'135',
                                            content: content,
                                            onOk:function(){
                                                var objConfId = $("#objConfId").val();
                                                if(objConfId=='' || objConfId == null){
                                                    $.message_alert("请选择流程模版");
                                                    return false;
                                                }
                                                data = data+'&objConfId='+objConfId;

                                                ajaxSubmit('',{
                                                    url: url,
                                                    method: "post",
                                                    data: data,
                                                    callback:function(resultMsg){
                                                        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                                            location.href= 'view.jhtml?readOnly=1&id='+orderId;
                                                        });
                                                    }
                                                })

                                            }
                                        });
                                    }

                                }
                            })
                            */
                            //--------end--------
                        }else{
                            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                location.href= 'view.jhtml?readOnly=1&id='+resultMsg.objx;
                            })
                        }
                        //--------over--------
                    }
                })
            }
        }
        //流程审核方法     创建人：肖光春 时间：2020/02/29 
        function check_wf(orderId){
				var $form = $("#inputForm");
				if($form.valid()){
					$.message_confirm("您确定要审批流程吗？",function(){
						var objTypeId = 100025;
						var modelId = model($("#sbuId").val(),"正式");
//						var modelId = model($("#sbuId").val(),"测试");
						var url="check_wf.jhtml?orderId="+orderId+"&modelId="+modelId+"&objTypeId="+objTypeId;
						ajaxSubmit("",{
							method:'post',
							url:url,
							async: true,
							callback: function(resultMsg) {
								$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
									location.href= 'view.jhtml?readOnly=1&id='+orderId;
								})
							}
						});
				});
			}
		}
		//查找对应流程模板 创建人：肖光春 时间：2020/02/29
		function model(sbuId,versions){
			var json = '{"正式":{"1":"60151","2":"48464","3":"411749","4":"48102","5":"55001","6":"57596","7":"57596","8":"55027"},';
			json +='"测试":{"1":"47997","2":"48464","3":"55016","4":"48102","5":"55001","6":"","7":"","8":"167501"}}';
			var model = JSON.parse(json);
			return model[versions][sbuId];
			/*
			8 55027 大自然电商-订单审核
			3 55016 广东壁高-订单审核
			5 55001 弹性地板-订单审核
			2 48464 Nature-订单审核
			4 48102 进口三层-订单审核
			1 47997 地板中心-订单审核 */
			
		}

        function pay(){
            var num=$("#total").html().substring(1);

            if(num==0){
                $.message_alert("请选择产品");
                return false;
            }
            $("#payamount").val(num);
            $("#payForm").submit();
        }

        function clearSelect(e){
            var $this = $(e);
            var value = $this.val();
            if(value==undefined || value.length==0){
                var $tr = $(e).closest("div.search");
                $this.prev("input").val("");
            }
        }
        
        
     
      
 function discountChange(){
		var discountId=$("#discountId").val();
		var discountName=$("#discountId").find("option:selected").text();
		var $quantity = $("input.quantity");
		$quantity.each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			
			$tr.find(".discountCode").val(discountId); 
		
		});
}

    </script>
</head>
<body>
	<div class="pathh">&nbsp;${message("订单生成")}</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax"
		validate-type="validate">
		<input type="hidden" id="outTradeNo" value="${store.outTradeNo}" /> <input
			type="hidden" name="cartId" value="${cart.id}" /> <input
			type="hidden" class="productIds" value="${productIds}" /> <input
			type="hidden" class="models" value="" /> [#if id != null] <input
			type="hidden" name="orderId" value="${id }" /> [/#if] [#list
		cartItems as cartItem] <input type="hidden" name="ids"
			value="${cartItem.id}" /> [/#list]
		<div class="tabContent order-info">
			<div class="title-style mt0" str="${editProductPrice}">
				${message("基本信息")}</div>
			<table class="input input-edit">
				<tr>
					<th><span class="requiredField">*</span>${message("客户")}:</th>
					<td>
						<span class="search" style="position: relative"> 
							<input type="hidden" name="storeId" class="text storeId" value="${store.id}" btn-fun="clear" />
							<input type="text" name="storeName" class="text storeName" value="${store.name}" maxlength="200" onkeyup="clearSelect(this)" readOnly />
							<input type="button" class="iconSearch" value="" id="openStore">
						</span>
					</td>
					<th>${message("业务类型")}:</th>
					<td>
						<select id="businessTypeName" name="businessTypeId" class="text"> 
							[#list businessTypes as businessType]
								<option value="${businessType.id}" [#if businessTypeId==businessType.id]selected[/#if]>${businessType.value}</option>
							[/#list]
						</select>
					</td>
					<th><span class="requiredField">*</span>${message("机构")}:</th>
					<td><input type="hidden" name="saleOrgId"
						class="text saleOrgId" btn-fun="clear"
						value="[#if store??]${store.saleOrg.id}[#else]${saleOrg.id}[/#if]" />
						<span id="saleOrgName"> [#if store??] ${store.saleOrg.name}
							[#else] ${saleOrg.name} [/#if] </span></td>

					<th><span class="requiredField">*</span>${message("经营组织")}:</th>
					<td><span id="organizationId">${order.warehouse.managementOrganization.name}</span>
						<input type="hidden" name="organizationId"
						class="text organizationId"
						value="${order.warehouse.managementOrganization.id}" /></td>
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("仓库")}:</th>
					<td><span class="search" style="position: relative"> <input
							type="hidden" name="warehouseId" class="text warehouseId"
							btn-fun="clear" value="${warehouseSbuId}" /> <input type="text"
							name="warehouseName" class="text warehouseName" maxlength="200"
							onkeyup="clearSelect(this)" value="${warehouseSbuName}" readOnly />
							<input type="button" class="iconSearch" value=""
							id="selectWarehouse"></span></td>
					<th>${message("仓库类型")}:</th>
					<td><span class="typeSystemDictValue">${order.warehouse.typeSystemDict.value}</span>
						<input type="hidden" name="typeSystemDictId"
						class="typeSystemDictId"
						value="${order.warehouse.typeSystemDict.id}" /></td>
					<th><span class="requiredField">*</span>${message("付款方式")}:</th>
					<td><select class="text" id="paymentMethodId"
						name="paymentMethodId"> [#list paymentMethods as
							paymentMethod] [#if paymentMethod.name=='线下支付'] [#else]
							<option value="${paymentMethod.id}" [#if
								order.paymentMethod.id==paymentMethod.id]selected[/#if]>${paymentMethod.name}</option>
							[/#if] [/#list]
					</select></td>
					<th><span class="requiredField">*</span>${message("配送方式")}:</th>
					<td><select class="text" name="shippingMethodId">
							[#list shippingMethods as sm]
							<option value="${sm.id}" [#if
								order.shippingMethod.id==sm.id]selected[/#if]>${sm.name}</option>
							[/#list]
					</select></td>
				</tr>
				<tr>
					<th>${message("区域经理")}:</th>
					<td>
						<input type="hidden" name="regionalManagerId" class="text storeMemberId" btn-fun="clear" value="${store.storeMember.id}" />
						<span id="regionalManagerName">${store.storeMember.name}</span>
					</td>
					<th>${message("Sbu")}:</th>
					<td>
						<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbu.id}" />
						<span id="sbuName">${sbu.name}</span>
					</td> 
					[#if sbu.id==3]
						<th>${message("折扣方案")}:</th>
						<td>
							<select id="discountId" name="discountId" class="text" onchange="discountChange()"> 
							[#list discounts as discount]
								<option value="${discount.id}">${discount.value}</option>
							[/#list]
							</select>
						</td> 
					[/#if] 
					[#if role]
						<th>
							<span id="memberRank1">${message("价格类型")}:</span>
						</th>
						<td>
							<span id="memberRank2" class="search" style="position: relative">
							 	<input type="hidden" name="memberRankId" class="text memberRankId" id="memberRankId" btn-fun="clear" value="${memberRank.id}" />
								<input class="text memberRankName" maxlength="200" type="text" name="memberRankName" value="${memberRank.name}" onkeyup="clearSelect(this)" readOnly />
								<input type="button" class="iconSearch" value="" id="selectMemberRank" />
								<div class="pupTitleName  memberRank"></div>
							</span>
						</td> 
					[/#if]
				</tr>
				<tr>
					<th>${message("运输方式")}:</th>
					<td>
						<select id="transportTypeName" name="transportTypeId" class="text"> 
							[#list transportTypeList as transportType]
								<option value="${transportType.id}">${transportType.value}</option>
							[/#list]
						</select>
					</td>
					<th>${message("客户简称")}:</th>
					<td>
						<span id="storeAlias">${store.alias}</span>
					</td>
					[#if contractRoles==1]
								<th><span class="requiredField">*</span>${message("合同名称")}:</th>
            		<td>
            		 <span class="search" style="position: relative">
            		  <input  type="hidden" name="contractId" class="text contractId" value="" btn-fun="clear" />
            		  <input type="text" name="contractName" class="text contractName" value=""maxlength="200" onkeyup="clearSelect(this)" readOnly /> 
            		  <input type="button" class="iconSearch" value="" id="selectContract"></span>
            		
            			</td>
            		[/#if]
            		[#if contractRoles==1]
								<th><span class="requiredField">*</span>${message("甲方/经销商")}:</th>
            		<td>
            		 <span class="search" style="position: relative">
            		  <input  type="hidden" name="distributorId" class="text distributorId" value="" btn-fun="clear" />
            		  <input type="text" name="distributorName" class="text distributorName" value="" maxlength="200" onkeyup="clearSelect(this)" readOnly /> 
            		
            			</td>
            		[/#if]
				</tr>
				<tr>
					<th>${message("备注")}:</th>
					<td colspan="7"><textarea class="text memo"
							oninput="listenLength()" name="memo">${order.memo}</textarea></td>
				</tr>
				<tr>
					<td colspan="7"><textarea class="mefont isMemo" id="isMemo"
							value=""></textarea></td>
				</tr>
			</table>
			<div class="title-style" [#if organizationIsNoWarehouse == 1 ] style="display:none;" [/#if]>${message("客户余额")}</div>
			<table class="input input-edit" [#if organizationIsNoWarehouse == 1 ] style="display:none;" [/#if]>
				[#if "${flas}" == "0"]
					<tr>
						<th>${message("可用余额")}:</th>
						<td>
							<span class="red" id="balance">
								${currency(balances,true)}
							</span>
							<input type="hidden" name="storeBalance" id="storeBalance" value="${balances}" />
						</td>
						<th>${message("订单金额")}:</th>
						<td>
							<span class="red" id="total"> 
								[#if order!=null]
									${currency(order.amount, true)} 
								[#else] 
									￥0.00 
								[/#if] 
							</span>
						</td>
						<th>${message("差额")}:</th>
						<td>
							<span class="red" id="chae">${currency(chae,true)}</span>
						</td>
						<th>${message("余额")}:</th>
						<td>
							<span class="red" id="yue">${currency(yue,true)}</span>
						</td>
						<th>${message("授信")}:</th>
						<td>
							<span class="red" id="credit">${currency(credit,true)}</span>
						</td>
					</tr>
				[#else]
					<tr>
						<th>${message("可用余额")}:</th>
						<td>
							<span class="red" id="balance">
								${currency(balance,true)}
							</span>
							<input type="hidden" name="storeBalance" id="storeBalance" value="${balance}" />
						</td>
						<th>${message("订单金额")}:</th>
						<td>
							<span class="red" id="total"> 
								[#if order!=null]
									${currency(order.amount, true)} 
								[#else] 
									￥0.00 
								[/#if]
							</span>
						</td>
						<th>${message("差额")}:</th>
						<td>
							<span class="red" id="chae">
								${currency(chae,true)}
							</span>
						</td>
						<th>${message("余额")}:</th>
						<td>
							<span class="red" id="yue">
								${currency(yue,true)}
							</span>
						</td>
						<th>${message("授信")}:</th>
						<td>
							<span class="red" id="credit">
								${currency(credit,true)}
							</span>
						</td>
					</tr>
				[/#if]
			</table>
			[#if organizationIsNoWarehouse == 1 ] 
				<div class="title-style" >
					${message("经营组织余额")}
				</div>
				<table id="table-m2"></table>
			[/#if]
			<div class="title-style">
				${message("订单明细")}:
				[#if sbu.id!=7]
				<div class="btns">
					<a href="javascript:;" id="addProduct" class="button">选择产品</a>
				</div>
				[#else]
				<div class="btns">
					<a href="javascript:;" id="addContractProduct" class="button">选择合同产品</a>
				</div>
				[/#if]
			</div>
			<table id="table-m1"></table>
			<div class="title-style">${message("订单汇总")}</div>
			<table class="input input-edit">
				<tr>
					<!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积 -->
					[#if sbu.id!=3]
					<th>${message("订单箱数")}:</th>
					<td><span id="totalBoxQuantity"></span></td>
					<th>${message("订单支数")}:</th>
					<td><span id="totalBranchQuantity"></span></td> [/#if]

					<th>${message("订单数量")}:</th>
					<td><span id="totalQuantity"></span></td> [#if sbu.id!=3]
					<th>${message("订单重量")}:</th>
					<td><span id="totalWeight"></span></td> [/#if]
				</tr>
				[#if sbu.id!=3]
				<tr>
					<th>${message("订单体积")}:</th>
					<td><span id="totalVolume"></span></td>
				</tr>
				[/#if]
			</table>
			<div class="title-style">${message("收货信息")}
				<div class="btns">
					<a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
				</div>
			</div>
			<table class="input input-edit">
				<tr class="border-L2">
					<th><span class="requiredField">*</span>${message("收货人")}:</th>
					<td><input type="text" class="text" name="consignee"
						value="[#if order!=null]${order.consignee}[#else]${storeAddress.consignee}[/#if]"
						btn-fun="clear" readOnly></td>
					<th><span class="requiredField">*</span>${message("收货人电话")}:</th>
					<td><input type="text" class="text" name="phone"
						value="[#if order!=null]${order.phone}[#else]${storeAddress.mobile}[/#if]"
						btn-fun="clear" readOnly></td>
					<th>${message("收货地区邮编")}:</th>
					<td><input type="text" class="text" name="zipCode"
						value="[#if order!=null]${order.zipCode}[#else]${storeAddress.zip_code}[/#if]"
						btn-fun="clear" readOnly></td>
					<th>地址外部编码:</th>
					<td><input type="text" class="text" name="addressOutTradeNo"
						value="[#if order!=null]${order.addressOutTradeNo}[#else]${storeAddress.out_trade_no}[/#if]"
						btn-fun="clear" readOnly /></td>
				</tr>
				<tr class="border-L1">
					<th><span class="requiredField">*</span>${message("收货地区")}：</th>
					<td><span class="search" style="position: relative"> <input
							type="hidden" name="areaId" class="text areaId"
							value="[#if order!=null]${order.area.id}[#else]${(storeAddress.area)!}[/#if]"
							btn-fun="clear" /> <input type="text" name="areaName"
							class="text areaName"
							value="[#if order!=null]${order.area.fullName}[#else]${(storeAddress.area_full_name)!}[/#if]"
							maxlength="200" onkeyup="clearSelect(this)" readOnly /> <input
							type="hidden" class="iconSearch" value="" id="openArea"></span>
					</td>
					<th>${message("销售地区")}:</th>
					<td><input type="text" class="text" name="salesAreaName"
						value="${order.salesAreaName}" btn-fun="clear" readonly="readonly" />
					</td>
					<th><span class="requiredField">*</span>${message("收货地址")}:</th>
					<td colspan="3"><input type="text" class="text" name="address"
						value="[#if order!=null]${order.address}[#else]${storeAddress.address}[/#if]"
						btn-fun="clear" readOnly></td>
					<th></th>
					<td></td>
				</tr>
			</table>
			<div class="title-style">
				${message("附件信息")}:
				<div class="btns">
					<a href="javascript:;" id="addAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			[#if saveOrder2Unaudited == 0] <a href="javascript:void(0);"
				class="button sureButton" onclick="save(this, 0)">${message("保存")}</a>
			[/#if] <a href="javascript:void(0);" class="button sureButton"
				onclick="save(this, 1)">${message("支付")}</a> <input type="button"
				onclick="location.reload(true);" class="button resetButton ml15"
				value="${message("重置")}">
		</div>
	</form>
</body>
</html>