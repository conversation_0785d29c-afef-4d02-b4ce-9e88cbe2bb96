<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("发货单查询")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$().ready(function() {
	var invoice_sttuss = {'0':'${message("未审核")}', '1':'${message("已审核")}', '2':'${message("作废")}', '3':'${message("部分发货")}', '4':'${message("完全发货")}'};
	var cols = [
		{ title:'${message("发货单编号")}', name:'shipping_sn' , align:'left',width:'120'},
		{ title:'${message("客户")}', name:'store_name' ,align:'center',nowrap:false},
		{ title:'${message("发货单状态")}', name:'status' , align:'center',width:'75', nowrap:false, renderer: function(val){
			var result = invoice_sttuss[val];
			var str = '';
			if(val==2 || val==4){
				str = '<input type="hidden" class="disabledFlag">'
			}
			if(result!=undefined)return str+result;			
		}},
		{ title:'', name:'id' , align:'center',hidden:true, renderer: function(val){
			return '<input type="checkbox" name="itemIds" value="'+val+'" />';			
		}},
		{ title:'${message("发货项")}' ,align: 'center', cols: [
			{ title:'${message("产品名称")}', name:'name' , align:'left',width:150},
			{ title:'${message("产品型号")}', name:'model' ,align:'center',width:80},
			{ title:'${message("12211")}', name:'vonder_code' , align:'center',width:80},
			{ title:'${message("订单编号")}', name:'order_sn' ,align:'center',width:'120'},
			{ title:'${message("价格")}', name:'price' ,align:'center',width:'60', renderer: function(val){
				return '<span class="red">'+currency(val,true)+'</span>';			
			}},
			{ title:'${message("计划发货数量")}', name:'quantity' ,align:'center',width:'60'},
			{ title:'${message("实际发货数量")}', name:'shipped_quantity' ,align:'center',width:'60'},
			{ title:'${message("已签收数")}', name:'receipt_quantity' ,align:'center',width:'60'},
			{ title:'${message("体积")}', name:'volume' ,align:'center',width:'60'},
		]},
		{ title:'${message("收货人")}', name:'consignee' , align:'center',width:'75',nowrap:false},
		{ title:'${message("收货人电话")}', name:'phone' , align:'center'},
		{ title:'${message("收货地址")}', name:'address' , align:'center',nowrap:false},
		{ title:'${message("配送方式")}', name:'shipping_method' ,align:'center', nowrap:false},
		{ title:'${message("物流快递")}', name:'delivery_corp_name' ,align:'center', nowrap:false},
		{ title:'${message("仓库")}', name:'warehouse_name' ,align:'center', nowrap:false},
		{ title:'${message("预计到货时间")}', name:'shipping_time' ,align:'center', renderer: function(val){
			if(val!=null){
				if(val.length>10){
					return val.substring(0,10);
				}else{
					return val
				}
			}
		}},
		{ title:'${message("运单号")}', name:'tracking_no' , align:'center'},
		{ title:'${message("备注")}', name:'memo' ,align:'center', nowrap:false},
		{ title:'${message("车主姓名")}', name:'driver_name' ,align:'center', nowrap:false},
		{ title:'${message("车主电话")}', name:'driver_phone' ,align:'center'},
		{ title:'${message("车牌号")}', name:'car_number' ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date', width:120 ,align:'center'}
	];
	[#if multi==2]
		var multiSelect = true;
	[#else]
		var multiSelect = false;
	[/#if]
	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad: true,
        url: 'select_shipping_item_data.jhtml',
        rowCursorPointer:true,
        checkByClickTd:true,
        multiSelect:multiSelect,
        method: 'post',
        params:function(){
        	return $("#listForm").serializeObject();
        },
        root: 'content',
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
});
</script>
</head>
<body style="min-width:0px;">
<form id="listForm" method="get">
	<input class="hidden" name="flag" value="${flag}" />
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
			<div id="search-content" >
				<dl>
        			<dt><p>${message("发货单编号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="sn" value =""  btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("订单编号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="orderSn" value =""  btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("产品名称")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="productName" value =""  btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("产品型号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="model" value =""  btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("产品编码")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="vonderCode" value =""  btn-fun="clear" />
        			</dd>
        		</dl>
	    		<dl>
					<dt><p>${message("客户")}:</p></dt>
    				<dd>
    					<input type="text" class="text"  name="storeName" value =""  btn-fun="clear" />
    				</dd>
	    		</dl>
	    		[#if flag!=1]
	    		<dl>
					<dt><p>${message("发货单状态")}:</p></dt>
					<dd>
						<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="${message("已审核")}" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">	
				       			<label><input  class="check js-iname" name="status" value="1" type="checkbox" checked/>${message("已审核")}</label>
				       			<label><input  class="check js-iname" name="status" value="2" type="checkbox"/>${message("作废")}</label>
				       			<label><input  class="check js-iname" name="status" value="3" type="checkbox"/>${message("部分发货")}</label>
				       			<label><input  class="check js-iname" name="status" value="4" type="checkbox"/>${message("完全发货")}</label>
				       		</div>
				       	</div>
					  	
		    		</dd>
				</dl>
				[/#if]
			</div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
        <div id="body-paginator">
            <div id="paginator"></div>
        </div>
	</div>
</form>
<script type="text/javascript">
//子框方法 返回选中的物料的ids字符串
function childMethod(){
   return $mmGrid.selectedRows();
};
</script>
</body>
</html>