<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("发货通知列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript">

function editPrice(t,e){
	extractNumber(t,2,false,e)
}

function check_view(url){
	var w = $(window).width();
	var h = $(window).height();
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
		title:'${message("查看对账单")}',
		width:w,
		height:h,
		content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
		ok: null,
		cancel: null,
		onOk: function() {
		
		}
	});
	$dialog.find(".dialogContent").css("height",h+"px");
	$dialog.css("top",0+"px").css("max-height",h+"px");
	

}
function edit(url){
	parent.change_tab(0,url);
}
function editQty(t,e){
	if(extractNumber(t,3,false,e)){

		var $tr = $(t).closest("tr");
		var branchQuantity=0;//支数
		var boxQuantity = 0;//箱数
		var branchPerBox=$tr.find(".branchPerBox").val();//每箱支数
		var perBranch=$tr.find(".perBranch").val();
		if($(t).attr("kid")=="box"){
			boxQuantity = $(t).val();//箱数
			branchQuantity = accMul(boxQuantity,branchPerBox);//支数
			if(isNaN(branchQuantity)){
    			branchQuantity = 0;
			}
    		$tr.find(".branchQuantity").val(branchQuantity);//支数
    		$tr.find(".scatteredQuantityStr").html(0);//零散支数
			$tr.find(".scatteredQuantity").val(0);
		}else if($(t).attr("kid")=="branch"){
			branchQuantity = $(t).val();//支数
			boxQuantity = parseInt(branchQuantity/branchPerBox);//箱数
			var scattered = branchQuantity%branchPerBox;
			$tr.find(".boxQuantity").val(boxQuantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}
		var quantity=accMul(branchQuantity,perBranch);
    	if(isNaN(quantity)){
    		quantity = 0;
		}
		$tr.find(".quantity").val(quantity);//数量
    	$tr.find(".quantityStr").html(quantity);
    	
    	var row = $mmGrid.row($tr.index());
    	if($tr.find(".mmg-check").prop("checked")){
    		map[row.id] = Number(quantity);
    		mapBx[row.id] = Number(boxQuantity);
    		mapBc[row.id] = Number(branchQuantity);
        	$(".total").text(countTotal());
    	}else{
    		if(map[row.id]!=undefined){
    			map[row.id] = undefined;
    			mapBx[row.id] = undefined;
    			mapBc[row.id] = undefined;
    		}
    	}
	}
}	

function calculateTotalBoxQuantity(){
	var $input = $("input.boxQuantity");
	var $checkbox = $tr.find("input.mmg-check:checked");
	var row = $.data($tr[0],'item');
	map[row.id] = num;
	$(".total").text(countTotal());

}
var map = {};
var mapT = {};
var mapBx = {};
var mapBc = {};
var mapCq = {};
var total = 0;


function removeCheck(){
   //取消全选
   $("input.mmg-check").attr("checked",false);
   $mmGrid.deselect();
}


$().ready(function() {
	
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	//设置旗标
	setupFlag();
	
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?isSelect=0',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$("input.storeId").val(row.id);
				$("input.storeName").val(row.name);
				$mmGrid.load();
			}
		}
	});
	
	$("#selectSupplier").bindQueryBtn({
		type:'supplier',
		title:'${message("查询供应商")}',
		url:'/member/store/select_store.jhtml?type=provider',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$("input.supplierId").val(row.id);
				$("input.supplierName").val(row.name);
				$mmGrid.load();
			}
		}
	});
	//查询机构	
		$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='saleOrgName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='saleOrgName']").val();
				}
				
		
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".saleOrgId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="saleOrgId" class="text saleOrgId saleOrgId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
						$(".saleOrg").append(vhtml);
					}
				}
				$("input[name='saleOrgName']").attr("value",allName);
			}
		}
	});

    //查询SBU
    $("#selectSbu").bindQueryBtn({
        type:'sbu',
        title:'${message("查询Sbu")}',
        url:'/basic/sbu/select_sbu.jhtml?multi=2',
        callback:function(rows){
            if(rows.length>0){
                var vhtml="";
                if($("input[name='sbuName']").val() == null){
                    var allName= "";
                }else{
                    var allName= $("input[name='sbuName']").val();
                }


                var idH ="";
                for (var i = 0; i < rows.length;i++) {
                    var idH = $(".sbuId_"+rows[i].id).length;
                    if(idH == 0){
                        allName =allName +','+ rows[i].name;
                        vhtml = '<div><input name="sbuId" class="text sbuId sbuId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newCloseProSbu(this)"></i></div>';
                        $(".sbu").append(vhtml);
                    }
                }
                $("input[name='sbuName']").attr("value",allName);
            }
        }
    });



	function addQtyBtnEvent(){
		var $input = $("input.boxQuantity");
		$input.keypress(function(event) {
			var key = event.keyCode ? event.keyCode : event.which;
			var value = ""+$(this).val();
			if(value.indexOf(".")>0 && key==46)return false;
			if ((key >= 48 && key <= 57) || key==8|| key==46) {
				return true;
			} else {
				return false;
			}
		});
		$input.keyup(function(event) {
			var $this = $(this);
			var value = $this.val();
			if(value.indexOf("。")>=0){
				$(this).val(value.replace("。", ""));
			}	
		});
		
		$input.focus(function(event) {
			var $this = $(this);
			var $tr = $this.closest("tr");
			var $checkInput = $tr.find("input.mmg-check");
			if(!$checkInput.prop("check")){
				$mmGrid.select($tr.index());
			}
		});
	
		$input.bind("input", function(event) {
			event.stopPropagation();
			if (event.type != "propertychange" || event.originalEvent.propertyName == "value") {
				var $this = $(this);
				var $tr = $this.closest("tr");
				var num = Number($this.val());
				var max = Number($this.attr("max"));
				if(num>max){
					num = max;
					$this.val(num);
				}
				var $checkbox = $tr.find("input.mmg-check");
				if($checkbox.prop("checked")){
					var row = $.data($tr[0],'item');
					map[row.id] = num;
					$(".total").text(countTotal());
				}
			}
		});
	
	}
	
	var cols = [
		{ title:'${message("订单编号")}', name:'order_sn' ,width:100, align:'center',renderer:function(val,item){
			var url = '../order/view.jhtml?isEdit=0&readOnly=1&id='+item.orders;
			var html = "";
			if(isNull(item.flag) != null){
				html += '<i class="icons-flag i-f0'+item.flag+'"></i> '
			}
			html += '<a href="javascript:void(0);" onclick="edit(\''+url+'\')" class="red">'+val+'</a>';
			return html;
		}},
		{ title:'${message("客户名称")}', name:'store_name' ,width:100, align:'center' },
		{ title:'${message("客户简称")}', name:'store_alias' ,width:100, align:'center' },
		{ title:'${message("SBU")}', name:'sb_name' ,width:70, align:'center' },
		{ title:'${message("仓库")}', name:'warehouse_name' ,width:90, align:'center' },
		{ title:'${message("机构")}', name:'sale_org_name' ,width:50, align:'center' },
		{ title:'${message("产品名称")}', name:'name' ,width:80, align:'center'},
		{ title:'${message("产品描述")}', name:'description' ,width:200, align:'center'},
		{ title:'${message("12211")}', name:'vonder_code' ,width:120, align:'center' },
		{ title:'${message("产品型号")}', name:'model' ,width:60, align:'center'},
		{ title:'${message("经营组织")}', name:'product_organization_name' ,width:80, align:'center' },
		{ title:'${message("产品级别")}', name:'levelName' ,width:80, align:'center' },
		{ title:'${message("制单箱数")}', width:100, align:'center', renderer: function(val,item,rowIndex){            
			var maxQuantity = parseInt((item.branch_quantity - item.ship_branch_quantity-item.closed_quantity)/item.branch_per_box);
			if(isNull(maxQuantity) == null){
				maxQuantity = 0;
			}
			quantity = maxQuantity;
			if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
				quantity=0;
				return '-'+
				'<input type="hidden" kid="box" class="t boxQuantity" _id="'+item.id+'" value="'+quantity+'" minData="0" maxData="'+maxQuantity+'" >';
			}else{
				var html = '<div class="nums-input ov">'+
	            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	            	'<input type="text" kid="box" class="t boxQuantity" _id="'+item.id+'" value="'+quantity+'" minData="0" maxData="'+maxQuantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	        	'</div>';
	        	return html;
			}
		}},
		{ title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			var maxQuantity = item.branch_quantity - item.ship_branch_quantity - item.closed_quantity;
			if(isNull(maxQuantity) == null){
				maxQuantity = 0;
			}
			if(isNull(item.closed_quantity) == null){
				item.closed_quantity = 0;
			}
			quantity = maxQuantity;
			if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
				quantity=0;
				return '-'+
				'<input type="hidden" kid="branch" class="t branchQuantity" value="'+quantity+'" minData="0" maxData="'+maxQuantity+'" >'+
				'<input type="hidden" class="branchPerBox" value="'+item.branch_per_box+'" /> '+
				'<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
				'<input type="hidden" class="perBranch" value="'+item.per_branch+'" />'+
				'<input type="hidden" class="closedQuantity" value="'+item.closed_quantity+'" />';
			}else{
				var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
            	'<input type="text" kid="branch" class="t branchQuantity" value="'+quantity+'" minData="0" maxData="'+maxQuantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
            	'<input type="hidden" class="branchPerBox" value="'+item.branch_per_box+'" /> '+
				'<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
				'<input type="hidden" class="perBranch" value="'+item.per_branch+'" />'+
				'<input type="hidden" class="closedQuantity" value="'+item.closed_quantity+'" />'+
        	'</div>';
			return html;
			}
		}},
		{ title:'${message("零散支数")}', align:'center', width:60, renderer: function(val,item,rowIndex, obj){
			var scatteredQuantity=(item.branch_quantity - item.ship_branch_quantity - item.closed_quantity)%item.branch_per_box;
			if(isNaN(scatteredQuantity)){
				scatteredQuantity = 0;
			}
			var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
				'<input type="hidden" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
			return html;
		}},
		{ title:'${message("数量")}', name:'quantity', align:'center', width:80, renderer:function(val,item,rowIndex,obj){
			var quantity = (item.branch_quantity - item.ship_branch_quantity - item.closed_quantity)*item.per_branch;
			if(isNull(quantity) == null){
				quantity = 0;
			}
			quantity = quantity.toFixed(6);
			quantity = parseFloat(quantity);
			if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
				quantity=item.quantity;
				var text = 
				'<div class="nums-input ov">'+
		        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
		        	'<input type="text"   class="t quantity" value="'+quantity+'" maxData="'+quantity+'" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
		        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
		    	'</div>';
		    	return text;
				
			}else{
				var html='<span class="quantityStr">'+quantity+'</span>'+
				'<input type="hidden" name="" class="quantity text" value="'+quantity+'" />';
				return html;	
			}	
		}},
		{ title:'${message("收货人")}', name:'consignee' ,width:60, align:'center' },
		{ title:'${message("收货人电话")}', name:'phone' ,width:80, align:'center' },
		{ title:'${message("收货地区")}', name:'area_full_name' ,width:80, align:'center' },
		{ title:'${message("收货地址")}', name:'address' ,width:200, align:'center' },
		{ title:'${message("工厂/供应商")}',[#if isMember==1]hidden:true,[/#if]  name:'manufactory_name' ,width:250, align:'center'},
		{ title:'${message("供应商型号")}',[#if isMember==1]hidden:true,[/#if]  name:'supplier' ,width:100, align:'center'},
		{ title:'${message("下单支数")}', name:'branch_quantity' ,width:60, align:'center' },
		{ title:'${message("已关闭支数")}', name:'closed_quantity' ,width:60, align:'center' },
		{ title:'${message("已关闭数量")}', name:'closed_quantity_nb' ,width:60, align:'center' },
		{ title:'${message("计划发货日期")}', name:'shipping_time' ,width:80, align:'center' },
		{ title:'${message("实际发货日期")}', name:'shippedTime' ,width:80, align:'center' },
		{ title:'${message("发货支数")}', name:'ship_branch_quantity' ,width:60, align:'center' },
		{ title:'${message("发货数量")}', name:'ship_quantity' ,width:60, align:'center' },
		{ title:'${message("长")}',  name:'length', align:'center', width:60, renderer:function(val,item,rowIndex,obj){
			return val+'<input type="hidden" class="length text" value="'+val+'" />';	
		}},
		{ title:'${message("宽")}',  name:'width', align:'center', width:60, renderer:function(val,item,rowIndex,obj){
			return val+'<input type="hidden" class="width text" value="'+val+'" />';
		}},
		{ title:'${message("体积")}', name:'volume' ,width:80, align:'center'},
		{ title:'${message("交货期")}', name:'delivery_time' ,width:80, align:'center',renderer:function(val){
			if(val!=null){
				var str = val;
				return str.substring(0,10);
			}
		}},
		{ title:'${message("创建人")}', name:'store_member_name' ,width:60, align:'center'},
		{ title:'${message("审核人")}', name:'check_store_member_name' ,width:60, align:'center'},
		{ title:'${message("订单审核日期")}', name:'check_date' ,width:80, align:'center'},
		{ title:'${message("机构")}', name:'sale_org_name' ,width:60, align:'center'},
		{ title:'${message("创建日期")}', name:'create_date', width:80 ,align:'center' },
		[#if hiddenBatch !=0]   
			{ title:'${message("含水率")}', name:'moisture_content_name' ,width:80, align:'center'},
			{ title:'${message("色号")}', name:'colour_number_name' ,width:50, align:'center'},
			{ title:'${message("批次")}', name:'batch_encoding' ,width:80, align:'center'},
		[/#if]  
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:true,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		rowCursorPointer:true,
		useCountText:false,
		recordRowId:true,
        cols: cols,
        url: 'order_item_list_data.jhtml',
        onSelectRow:function(row,e,val){
        	if(val){
        		var $input = $(e).find("input.quantity");
        		map[row.id] = Number($input.val());
         		var $volume=$(e).find("input.volume").val();
        		var $boxQuantity=$(e).find("input.boxQuantity").val();
				var $branchQuantity=$(e).find("input.branchQuantity").val();
				var $closedQuantity=$(e).find("input.closedQuantity").val();
        		mapBx[row.id] = Number(Number($boxQuantity));
        		mapBc[row.id] = Number(Number($branchQuantity));
        		mapCq[row.id] = Number(Number($closedQuantity));
        	}else{
        		map[row.id] = undefined;
        		mapBx[row.id] = undefined;
        		mapBc[row.id] = undefined;
        		mapCq[row.id] = undefined;
        	}
        	$(".recordsChecked").text($mmGrid.selecteds.length);
        	$(".total").text(countTotal());
        },
        onSelectAll:function(rows,e,val){
        	$(e).each(function(index){
        			var row = rows[index];
        			if(val){
        				var $tr = $(this);
	        			var $input = $tr.find("input.quantity");
	        			var value = $input.val();
        				map[row.id] = Number(value);
        				var $boxQuantity=$tr.find("input.boxQuantity").val();
        				var $branchQuantity=$tr.find("input.branchQuantity").val();
        				var $closedQuantity=$tr.find("input.closedQuantity").val();
        				mapBx[row.id] = Number(Number($boxQuantity));
                		mapBc[row.id] = Number(Number($branchQuantity));
                		mapCq[row.id] = Number(Number($closedQuantity));
        			}else{
        				map[row.id] = undefined;
        				mapBx[row.id] = undefined;
                		mapBc[row.id] = undefined;
                		mapCq[row.id] = undefined;
        			}
        	})
        	$(".recordsChecked").text($mmGrid.selecteds.length);
        	$(".total").text(countTotal());
        },
        callback:function(){
        	$(".recordsChecked").text($mmGrid.selecteds.length);
        	$(".total").text(countTotal());
        },
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
    $("#shippingBtn").click(function(){
            
    	var data = map;
    	var params = '';
    	var params2 = '';
    	var params3 = '';
    	var params4 = '';
    	var params5 = '';
    	for(var key in map){
    		var value = map[key];
    		var bxQ=mapBx[key];
    		var bcQ=mapBc[key];
    		var cq=mapCq[key];
    		if(value!=undefined){
	    		params+='&ids='+key;
	    		params2+='&quantity='+value;
    		}
    		if(bxQ!=undefined){
    			params3+='&boxQuantity='+bxQ;
    		}
    		if(bcQ!=undefined){
    			params4+='&branchQuantity='+bcQ;
    		}
    		if(cq!=undefined){
    			params5+='&closedQuantity='+cq;
    		}
    	}
    	if(params.length==0){
    		$.message_alert("请选择发货明细");
    		return false;
    	}
		params = params.substring(1,params.length)+params2+params3+params4+params5;    		
		var rows = $mmGrid.selectedRows();
		if(rows[0].st_id!=null){
			params+='&supplierId='+rows[0].st_id+'&supplierName='+rows[0].st_name;
		}
		var pageType = '${pageType}';
		if(pageType!=null){
			params+='&pageType='+pageType;
		}
		ajaxSubmit(null,{
			url:"check_data.jhtml?"+params,
			method:"post",
			async: false,
			data:data,
			callback:function(resultMsg){
				var data = $.parseJSON(resultMsg.content);
				for(var key in data){
					var value = data[key];
					if(value!=undefined){
						params+="&"+key+"="+encodeURIComponent(value);
					}
				}
				var h = $(window).height();
				var w = $(window).width()-3;
				var url = "shipping_operate_list.jhtml?"+params;
				edit(url);
			}
		});
		removeCheck();
    });
    
    
  	//仓库名称
	$("#selectWarehouse").bindQueryBtn({
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='warehouseName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='warehouseName']").val();
				}
	
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".warehouseId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="warehouseIds" class="text warehouseId warehouseId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newwarehouseClosePro(this)"></i></div>';
						$(".warehouse").append(vhtml);
					}
				}
				$("input[name='warehouseName']").attr("value",allName);
			}
		}
	});	   		
});			

function newwarehouseClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".warehouse > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='warehouseName']").attr("value",allName2)
};

function countTotal(){
	total = 0;
	for(var key in map) { 
		var value = map[key];
		if(value!=undefined){
			total = total+value;
			total = total.toFixed(6);
			total = parseFloat(total);
		}
	} 
	return total;
}

function newclosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".saleOrg > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='saleOrgName']").attr("value",allName2)
};

function newCloseProSbu(e){
    $(e).closest("div").remove();
    var allName2 = '';
    $(".saleOrg > div").each(function(){
        allName2 = allName2 +','+  $(this).find("p").html();
    })
    $("input[name='sbuName']").attr("value",allName2)
};

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = 'to_condition_export_create_order.jhtml';//分页导出统计页面
	var url = 'condition_export_create_order.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的订单！")}';//提示
	var url = 'selected_export_create.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

function check_view(url){
	var w = $(window).width();
	var h = $(window).height();
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
		title:'${message("查看订单")}',
		width:w,
		height:h,
		content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
		ok: null,
		cancel: null,
		onOk: function() {
		
		}
	});
	$dialog.find(".dialogContent").css("height",h+"px");
	$dialog.css("top",0+"px").css("max-height",h+"px");
}

//设置旗标
function insertFlag(e){
	//参数
	var ids = $mmGrid.serializeSelectedIds();
	if(ids.length==0){
		$.message_alert("请选择单据行");
		return false;
	}
	ajaxSubmit($(e),{
		method:'post',
		url:'setFlag.jhtml?flag='+e,
		data:ids,
		callback: function() {
			$mmGrid.load();
		}
	})
}

</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span> 导入导出
					</a>
					<ul class="flag-list">
						<li>
							<a href="javascript:void(0)" onclick="exportExcel(this)">
								<i class="flag-imp02"></i>${message("选择导出")}
							</a>
						</li>
						<li>
							<a href="javascript:void(0)" onclick="segmentedExport(this)"> 
								<i class="flag-imp02"></i>${message("条件导出")}
							</a>
						</li>
					</ul>
				</div>
			</div>
			<div id="searchDiv">
				<div id="search-content">
					<dl>
						<dt>
							<p>${message("客户名称")}:</p>
						</dt>
						<dd>
							<span style="position: relative"> <input name="storeId"
								class="text storeId" type="hidden"> <input
									class="text storeName" maxlength="200" type="text"
									name="storeName" onkeyup="clearSelect(this)" readonly>
										<input type="button" class="iconSearch" value=""
										id="selectStore"></span>
						</dd>
					</dl>
					<!-- 2019-05-16 冯旗加创建人 机构搜索条件 -->
					<dl>
						<dt>
							<p>${message("机构名称")}：</p>
						</dt>
						<dd>
							<span class="search" style="position: relative"> <input
								type="hidden" name="saleOrgId" class="text saleOrgId"
								btn-fun="clear" /> <input type="text" name="saleOrgName"
								class="text saleOrgName" maxlength="200"
								onkeyup="clearSelect(this)" readonly/> <input type="button"
								class="iconSearch" id="selectSaleOrg">
									<div class="pupTitleName  saleOrg"></div></span>
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("创建人")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="storeMemberName" value="${storeMemberName}"
								btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("收货人")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="consignee" value=""
								btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("订单编号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="orderSn" value=""
								btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("产品名称")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="name" value=""
								btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("12211")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="vonderCode" value=""
								btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("产品型号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="model" value=""
								btn-fun="clear" />
						</dd>
					</dl>
					[#if pageType >0]   
						<dl style="display:none">
							<dt>
								<p>${message("单据类型")}：</p>
							</dt>
							<dd>
								<input type="text" class="text" name="pageType" value="${pageType}" btn-fun="clear" />
							</dd>
						</dl>	
					[/#if] 
					<dl>
						<dt>
							<p>${message("仓库名称")}:</p>
						</dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear"/>
							<input type="text" name="warehouseName" class="text warehouseId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
							<input type="button" class="iconSearch" id="selectWarehouse">
							<div class="pupTitleName  warehouse"></div>
							</span>
						</dd>
					</dl>
					
					<dl>
        				<dt>
        					<p>${message("经营组织")}：</p>
        				</dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off" style="overflow:scroll;width:100px;height:300px;">
					       			[#list storeMemberOrganizationList as storeMemberOrganization]
										<label><input class="check js-iname" name="organizationIds" value="${storeMemberOrganization.organization.id}" type="checkbox"/>${storeMemberOrganization.organization.name}</label>
									[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
                    <!-- 2020-11-5 Lan TianLong 添加SBU搜索条件 -->
                    <dl>
                        <dt>
                            <p>${message("SBU")}：</p>
                        </dt>
                        <dd>
							<span class="search" style="position: relative">
                                <input type="hidden" name="sbuId" class="text sbuId" btn-fun="clear" />
                                <input type="text" name="sbuName" class="text sbuName" maxlength="200" onkeyup="clearSelect(this)" readonly/>
                                <input type="button" class="iconSearch" id="selectSbu">
                                <div class="pupTitleName  sbu"></div></span>
                        </dd>
                    </dl>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator" style="text-align: left;">
				<div id="paginator"></div>
			</div>
		</div>
		<div class="floatDiv"
			style="position: fixed; top: 0; left: 225px; height: 38px; text-align: right;">
			<p class="checked-p" style="line-height: 38px; font-size: 14px;">
				当前已选择发货明细 <span class="recordsChecked" style="color: blue">0</span>
				行，总数量 <span class="total" style="color: blue">0</span> ， 请点击 <a
					href="javascript:void(0);" class="iconButton" id="shippingBtn"
					style="float: none;">添加</a> ， 创建发货通知单！
			</p>
		</div>
	</form>
</body>
</html>