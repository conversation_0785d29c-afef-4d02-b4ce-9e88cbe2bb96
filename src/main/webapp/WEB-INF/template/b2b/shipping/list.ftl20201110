<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("发货通知")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript"
	src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function initWfStates(){
	wfStates = {};
	[#list wfStates as wfState]
		wfStates['${wfState}'] = '${message('22222222'+wfState)}';
	[/#list]
}
$().ready(function() {
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml'
	});
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2'
	});
	//查询SBU
	$("#selectSbu").bindQueryBtn({
		type:'sbu',
		title:'${message("查询Sbu")}',
		url:'/basic/sbu/select_sbu.jhtml?multi=2'
	});
		$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('产品【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					allName =allName +','+ rows[i].name  
					vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="closePro(this)"></i></div>'
					$(".product").append(vhtml);
				}
				$("input[name='productName']").attr("value",allName)
			}
		}
	});
	
	initWfStates()
	var invoice_sttuss = {'0':'未审核', '1':'已审核', '2':'已作废', '3':'部分发货', '4':'完全发货'};
	var item_sttuss = {'0':'有效', '1':'有效', '2':'无效', '3':'有效', '4':'有效'};
	var cols = [
		{ title:'${message("发货单编号")}', name:'sn' , align:'center',width:'80', renderer: function(val,item,rowIndex){
			[#if pageType >0]  
				if(item.status!=0 && item.status!=2){
					return '<a href="javascript:void(0);" onclick="check_pdf(this,'+item.id+','+1+')" title="查看PDF"><img style="width: 20px;" src="/resources/images/ic_pdf.png"></a>&nbsp;<a href="javascript:void(0);" onclick="check_pdf(this,'+item.id+','+0+')" title="查看PDF"><img style="width: 10px;" src="/resources/images/ic_pdf.png"></a>&nbsp;<a href="javascript:void(0);" onClick="parent.change_tab(0,\'view.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
				}else{
					return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'view.jhtml?id='+item.id+'&pageType='+${pageType}+'\')" class="red">'+val+'</a>';			
				}
			[#else]
				if(item.status!=0 && item.status!=2){
					return '<a href="javascript:void(0);" onclick="check_pdf(this,'+item.id+','+1+')" title="查看PDF"><img style="width: 20px;" src="/resources/images/ic_pdf.png"></a>&nbsp;<a href="javascript:void(0);" onclick="check_pdf(this,'+item.id+','+0+')" title="查看PDF"><img style="width: 10px;" src="/resources/images/ic_pdf.png"></a>&nbsp;<a href="javascript:void(0);" onClick="parent.change_tab(0,\'view.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
				}else{
					return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'view.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';			
				}
			[/#if]
		}},
		{ title:'${message("ERP单号")}', name:'erp_sn' ,width:'80',align:'center',nowrap:false},
		{ title:'${message("客户")}', name:'store_name' ,width:'100',align:'center',nowrap:false},
		{ title:'${message("客户简称")}', name:'store_alias' ,align:'center',nowrap:false},
		{ title:'${message("sbu")}', name:'sbu_name' ,width:60,align:'center'},
		{ title:'${message("仓库")}', name:'warehouse_name' ,width:80,align:'center', nowrap:false},
		{ title:'${message("发运方式")}', name:'smethod' ,align:'center',width:60},
		{ title:'${message("机构")}', name:'sale_org_name' ,width:50, align:'center' },
		{ title:'${message("发货单状态")}', name:'status' , align:'center',width:'60', nowrap:false, renderer: function(val){
			var result = invoice_sttuss[val];
			if(result!=undefined)return result;			
		}},
		{ title:'${message("收货人")}', name:'consignee' , align:'center',width:'60',nowrap:false},
		{ title:'${message("收货人电话")}', name:'phone' , align:'center',width:'80'},
		{ title:'${message("收货地址")}', name:'address' ,width:'200', align:'center',nowrap:false},
		{ title:'${message("发货项")}' ,align: 'center', cols: [
			{ title:'${message("产品名称")}', name:'name' , align:'center',isLines:true,width:'80', isBegin:true, renderer: function(val,item,rowIndex){
				return val;
			}},
			{ title:'${message("产品型号")}', name:'model' ,align:'center' ,width:60,isLines:true},
			{ title:'${message("12211")}', name:'vonder_code' , align:'center',width:120,isLines:true},
			{ title:'${message("产品描述")}', name:'description' ,align:'center',width:200,isLines:true},
			{ title:'${message("经营组织")}', name:'product_organization_name' ,width:80,align:'center',isLines:true},
			{ title:'${message("订单编号")}', name:'order_sn' ,width:80,align:'center',isLines:true},
			{ title:'${message("体积")}', name:'volume' ,align:'center',isLines:true,width:'60', renderer: function(val,item,rowIndex){
				var volume = (val!="")?val:0;
				var lineVolume = volume*item.box_quantity;
				lineVolume = lineVolume.toFixed(6);
				lineVolume = parseFloat(lineVolume);
				var html = lineVolume+'';
				return html;
			}},
			{ title:'${message("计划发货箱数")}', name:'box_quantity' ,align:'center',isLines:true,width:'60'},
			{ title:'${message("计划发货支数")}', name:'branch_quantity' ,align:'center',isLines:true,width:'60'},
			{ title:'${message("计划发货数量")}', name:'quantity' ,align:'center',isLines:true,width:'80'},
			{ title:'${message("实际发货支数")}', name:'shipped_branch_quantity' ,align:'center',isLines:true,width:'60'},
			{ title:'${message("已签收数")}', name:'receipt_quantity' ,align:'center',isLines:true,width:'60'},
			[#if hiddenBatch !=0]  
				{ title:'${message("色号")}', name:'colour_number_name' ,align:'center' ,width:60,isLines:true},
				{ title:'${message("含水率")}', name:'moisture_content_name' ,align:'center' ,width:60,isLines:true},
				{ title:'${message("批次")}', name:'batch_encoding' ,align:'center' ,width:60,isLines:true},
			[/#if]
			{ title:'${message("状态")}', name:'status' ,align:'center',isLines:true,width:'60',renderer: function(val){
						var result = item_sttuss[val];
						if(result!=undefined)return result;
					}}
		]},
		{ title:'${message("配送方式")}', name:'shipping_method' ,align:'center', nowrap:false,width:'60'},
		{ title:'${message("物流快递")}', name:'delivery_corp_name' ,align:'center', nowrap:false,width:'60'},
		{ title:'${message("计划发货日期")}', name:'shipping_time' ,width:'80',align:'center', renderer: function(val){
			if(val!=null){
				if(val.length>10){
					return val.substring(0,10);
				}else{
					return val
				}
			}
		}},
		{ title:'${message("实际出库时间")}', name:'erp_date' ,width:'80',align:'center', renderer: function(val){
			if(val!=null){
				if(val.length>19){
					return val.substring(0,19);
				}else{
					return val
				}
			}
		}},
		{ title:'${message("备注")}', name:'memo' ,align:'center', nowrap:false,width:'100'},
		{ title:'${message("创建人")}', name:'store_member_name', width:60 ,align:'center' },
		{ title:'${message("创建日期")}', name:'create_date', width:80 ,align:'center' },
		{ title:'${message("单据日期")}', name:'gl_date', width:80 ,align:'center' },
	];
	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad: true,
        url: 'list_data.jhtml',
        lineRoot:"shipping_items",
        method: 'post',
        params:function(){
        	return $("#listForm").serializeObject();
        },
        root: 'content',
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	//仓库名称
	$("#selectWarehouse").bindQueryBtn({
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='warehouseName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='warehouseName']").val();
				}
	
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".warehouseId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="warehouseIds" class="text warehouseId warehouseId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newwarehouseClosePro(this)"></i></div>';
						$(".warehouse").append(vhtml);
					}
				}
				$("input[name='warehouseName']").attr("value",allName);
			}
		}
	});	  
	
});

function newwarehouseClosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".warehouse > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='warehouseName']").attr("value",allName2)
};

function callStart(e){
	var id = $(e).prev("span.search").find("input.warehouseId").val();
	iframePager_asign_warehouse.window.startProcess(e,id);
}

function callStop(e){
	iframePager_asign_warehouse.window.stopProcess(e);
}

function callClose(t,e){
	$(e).closest(".xxDialog").children(".dialogClose").click();
	if(t==1){
		$mmGrid.load();
	}
	
}
function closePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".product > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='productName']").attr("value",allName2)
}

function shippingAudit(){
	
	var data =$mmGrid.serializeSelectedIds();
	if(data.length==0){
		$.message_alert("${message("请选择发货单")}");
		return false;
	}

	var bottomContent =
    	'<input type="button" class="button orderStar" value="开始执行" onclick="callStart(this)" >'+
    	'<input type="button" class="button backButton back-btn-one" value="取消" onclick="callClose(0,this)">'+
    	'<input type="button" class="button orderStop" value="中断执行" style="display:none;" onclick="callStop(this)">'+
    	'<input type="button" class="button backButton back-btn-two" value="返回" style="display:none;" onclick="callClose(1,this)">';

	

	var $content = '<iframe name="iframePager_asign_warehouse" src="/b2b/shipping/shippinglist.jhtml?'+data+'" width="100%" height="482px"><\/iframe>';
	var $dialog_win = $.dialog({
			title:"${message("发货单审核")}",
			width:1200,
			height:570,
			content: $content,
			bottomContent:bottomContent,
			closeIconHide:true,
			ok: null,
			cancel: null,
		});

}

function shippingCancel(e){
	var data =$mmGrid.serializeSelectedIds();
	if(data.length==0){
		$.message_alert("${message("请选择需要作废的发货单")}");
		return false;
	}

	ajaxSubmit(e,{
		url:"cancel.jhtml",
		method:"post",
		data:data,
		isConfirm:true,
		confirmText : '您确定要作废吗？',
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				$mmGrid.load();
			});
		}
	
	});

}

function create_pdf(e){
	ajaxSubmit(e,{
		url:"create.jhtml",
		method:"post",
		data:{id:708},
		async: false,
		callback:function(resultMsg){
			window.open(resultMsg.content);
		}
	
	});

}
/**
function check_pdf(e){
	var rows = $mmGrid.selectedRows();
	var size = rows.length;
	if(size==0){
		$.message_alert("${message("请选择发货单")}");
	}else if(size>1){
		$.message_alert("${message("请选择一张发货单")}");
	}else if(size==1){
		var row = rows[0];
		ajaxSubmit(e,{
			url:"checkPdf.jhtml",
			method:"post",
			data:{ids:row.id},
			async: false,
			callback:function(resultMsg){
				window.open(resultMsg.content);
			}
		});
	}
}
*/
function check_pdf(e,id,sign){
		ajaxSubmit(e,{
			url:"checkPdf.jhtml",
			method:"post",
			data:{ids:id,sign:sign},
			async: false,
			callback:function(resultMsg){
				window.open(resultMsg.content);
			}
		});
}

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = 'to_condition_export.jhtml';//分页导出统计页面
	var url = 'condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的发货单！")}';//提示
	var url = 'selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

</script>
</head>
<body>
	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				[#-- <a href="javascript:void(0);"
					class="iconButton button pdfButton" onclick="check_pdf(this)">
					<span>&nbsp;</span>${message("查看PDF")}
				</a> --]
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i
								class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)"
							onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>

					</ul>
				</div>
				[#-- <a href="javascript:void(0);" class="iconButton"
					onclick="create_pdf(this)"> <span class="fenpeiIcon">&nbsp;</span>${message("测试pdf")}
				</a>--]


			</div>
			<div id="searchDiv">
				<div id="search-content">
					<dl>
						<dt>
							<p>${message("发货单编号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="sn" value=""
								btn-fun="clear" />
						</dd>
					</dl>

					<!-- 2019-05-16 冯旗 加ERP单号搜索 -->
					<dl>
						<dt>
							<p>${message("erp单号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="erpSn" value=""
								btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("客户")}:</p>
						</dt>
						<dd>
							<span style="position: relative"> <input name="storeId"
								class="text storeId" type="hidden" value=""> <input
									class="text storeName" maxlength="200" type="text"
									name="storeName" value="" onkeyup="clearSelect(this)" readonly/>
										<input type="button" class="iconSearch" value=""
										id="selectStore"> [#--
											<div class="pupTitleName store"></div>--] </span>
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("机构名称")}：</p>
						</dt>
						<dd>
							<span class="search" style="position: relative"> <input
								type="hidden" name="saleOrgId" class="text saleOrgId"
								id="saleOrgId" btn-fun="clear" /> <input type="text"
								name="saleOrgName" class="text saleOrgName" maxlength="200"
								onkeyup="clearSelect(this)" id="saleOrgName" readonly/> <input
								type="button" class="iconSearch" value="" id="selectSaleOrg"></span>
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("SBU")}：</p>
						</dt>
						<dd>
							<span class="search" style="position: relative"> <input
								type="hidden" name="sbuId" class="text sbuId" id="sbuId"
								btn-fun="clear" /> <input type="text" name="sbuName"
								class="text sbuName" maxlength="200" onkeyup="clearSelect(this)"
								id="sbuName" readonly/> <input type="button" class="iconSearch"
								value="" id="selectSbu"></span>
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("发货单状态")}:</p>
						</dt>
						<dd>
							<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)"
									class="deleteText close"></a> <input type="text"
									class="text pointer doStatus" value="${message("未审核;已审核")}" autocomplete="off" />
								<div class="statusList cs-box" data-value="off">
									<label><input class="check js-iname" name="status"
										value="0" type="checkbox" checked />${message("未审核")}</label> <label><input
										class="check js-iname" name="status" value="1" type="checkbox"
										checked />${message("已审核")}</label> <label><input
										class="check js-iname" name="status" value="2" type="checkbox" />${message("已作废")}</label>
									<label><input class="check js-iname" name="status"
										value="3" type="checkbox" />${message("部分发货")}</label> <label><input
										class="check js-iname" name="status" value="4" type="checkbox" />${message("完全发货")}</label>
								</div>
							</div>
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("流程状态")}:</p>
						</dt>
						<dd>
							<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)"
									class="deleteText close"></a> <input type="text"
									class="text pointer doStatus" value="${message("未启动;已完成")}" autocomplete="off" />
								<div class="statusList cs-box" data-value="off">
									<label><input class="check js-iname" name="wfState"
										value="0" type="checkbox" checked />${message("未启动")}</label> <label><input
										class="check js-iname" name="wfState" value="1"
										type="checkbox" />${message("审核中")}</label> <label><input
										class="check js-iname" name="wfState" value="2"
										type="checkbox" checked />${message("已完成")}</label> <label><input
										class="check js-iname" name="wfState" value="3"
										type="checkbox" />${message("驳回")}</label>
								</div>
							</div>
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("运单号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="trackingNo" value=""
								btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("创建时间")}:</p>
						</dt>
						<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value=""
								onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});"
								type="text" btn-fun="clear" />
							<div class="fl">--</div>
							<input id="endTime" name="lastTime" class="text" value=""
								onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});"
								type="text" btn-fun="clear" />
						</dd>
					</dl>

					<dl>
						<dt>
							<p>${message("发运时间")}:</p>
						</dt>
						<dd class="date-wrap">
							<input id="firstErpTime" name="firstErpTime" class="text"
								value=""
								onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});"
								type="text" btn-fun="clear" />
							<div class="fl">--</div>
							<input id="lastErpTime" name="lastErpTime" class="text" value=""
								onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});"
								type="text" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("产品名称")}:</p>
						</dt>
						<dd>
							<span style="position: relative"> <input
								class="text productName" maxlength="200" type="text"
								name="productName" value="" onkeyup="clearSelect(this)" readonly>
									<input type="button" class="iconSearch" value=""
									id="selectProduct">
										<div class="pupTitleName product"></div></span>
						</dd>
					</dl>
					[#if pageType >0]   
						<dl style="display:none">
							<dt>
								<p>${message("单据类型")}：</p>
							</dt>
							<dd>
								<input type="text" class="text" name="pageType" value="${pageType}" btn-fun="clear" />
							</dd>
						</dl>	
					[/#if]  
					<dl>
						<dt>
							<p>${message("仓库名称")}:</p>
						</dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear"/>
							<input type="text" name="warehouseName" class="text warehouseId" maxlength="200" onkeyup="clearSelect(this)" readonly="true"/>
							<input type="button" class="iconSearch" id="selectWarehouse">
							<div class="pupTitleName  warehouse"></div>
							</span>
						</dd>
					</dl>
					<dl>
        				<dt>
        					<p>${message("经营组织")}：</p>
        				</dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list storeMemberOrganizationList as storeMemberOrganization]
										<label><input class="check js-iname" name="organizationIds" value="${storeMemberOrganization.organization.id}" type="checkbox"/>${storeMemberOrganization.organization.name}</label>
									[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
                    <dl>
                        <dt>
                            <p>${message("创建人")}：</p>
                        </dt>
                        <dd>
                            <input type="text" class="text" name="storeMemberName" value=""
                                btn-fun="clear" />
                        </dd>
                    </dl>
                    <dl>
                        <dt>
                            <p>${message("订单编号")}：</p>
                        </dt>
                        <dd>
                            <input type="text" class="text" name="orderSn" value=""
                                btn-fun="clear" />
                        </dd>
                    </dl>
                    <dl>
                        <dt>
                            <p>${message("状态")}：</p>
                        </dt>
                        <dd>
                            <div class="checkbox-style">
                                <a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
                                <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                                <div class="statusList cs-box" data-value="off">
                                        <label><input class="check js-iname" name="statusType" value="0,1,3,4" type="checkbox"/>有效</label>
										<label><input class="check js-iname" name="statusType" value="2" type="checkbox"/>无效</label>
                                </div>
                            </div>
                        </dd>
                    </dl>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>

		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator">
				<div id="paginator"></div>
			</div>
		</div>
	</form>
</body>
</html>