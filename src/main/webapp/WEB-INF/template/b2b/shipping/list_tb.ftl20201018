<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>
<script type="text/javascript">

$().ready(function() {
	var h = $(window).height();
	$("iframe").height(h-$("#tab").outerHeight());
		var flag = '${flag}';
		var objid = '${objid}';
		var pageType = '${pageType}';
		var index = 1;
		var url;
		if(objid!=''){
			var index = 0;
			var url ='view.jhtml?id=${objid}&flag=1';
			change_tab(index,url);
		}else if(flag == 0){
			//url ='order_item_list.jhtml?menuId=';
			editDetails(${menuId},'check_data.jhtml?','shipping_operate_list.jhtml?',0,'order_item_list.jhtml?menuId=${menuId}');
		}else if(flag == 1){
			url ='list.jhtml?menuId=${menuId}';
		}else if(flag == 2){
			url ='list_out.jhtml'
		}else if(flag == 3){
			url ='sale_return_list.jhtml?pageType=2'
		}else if(flag == 4){
			url ='order_item_list.jhtml?pageType=1&menuId=${menuId}';
		}else if(flag == 5){
			url ='list.jhtml?pageType=1&menuId=${menuId}';
		}else if(flag == 6){
			url ='list.jhtml?pageType=2&menuId=${menuId}';
		}
		change_tab(index,url);
});

function change_tab(index,url,flag){
	$("#tab input").removeClass("current").eq(index).addClass("current");
	var $tabContent = $(".tabContent").hide();
	var $iframe = $tabContent.eq(index).show().find("iframe");
	if(flag==undefined){
		$iframe.attr("src",url);
	}else{
		 if($iframe.attr("src")==""){
			$iframe.attr("src",url);
		}
		if(flag==0){
			$iframe.attr("src",$iframe.attr("src"));
		} 
	}
}

function set_list_tb(index,url,flag,isClear){
	var $tabContent = $(".tabContent");
	var method;
	if(flag==undefined || flag==null){
		method = "change_tab("+index+",'"+url+"')";
	}else{
		method = "change_tab("+index+",'"+url+"',1)"
	}
	$("#tab input").eq(index).attr("onclick",method);
	var $iframe = $tabContent.eq(index).find("iframe");
	if(isClear == true && $iframe.attr("src")!=""){//是否清除src
		$iframe.attr("src","");
	}
}

</script>
</head>
<body style="overflow:hidden;">
	<div> 
		<ul id="tab" class="tab tab-first">
	        <li>
	            <input type="button" value="${message("常规")}" onclick="change_tab(0,'',1)">
	        </li>
	        <li [#if objid!=null]style="display:none;"[/#if]>
	            <input type="button" value="${message("列表")}" [#if flag==0] onclick="change_tab(1,'order_item_list.jhtml?menuId=${menuId}',0)"[#elseif flag==1] onclick="change_tab(1,'list.jhtml',1)" [#elseif flag==2] onclick="change_tab(1,'list_out.jhtml',1)" [#elseif flag==3] onclick="change_tab(1,'sale_return_list.jhtml?pageType=2',1)" [#elseif flag==4] onclick="change_tab(1,'order_item_list.jhtml?pageType=1',1)"
	             [#elseif flag==5] onclick="change_tab(1,'list.jhtml?pageType=1',1)" [#elseif flag==6] onclick="change_tab(1,'list.jhtml?pageType=2',1)"  [/#if]>
	        </li>
	    </ul>
	    <div class="tabContent" style="display:none;">
			<iframe src="" style="width:100%;"></iframe>
		</div>
		<div class="tabContent" style="display:none;" >
			<iframe src="" style="width:100%;"></iframe>
		</div>
	</div>
</body>
</html>