<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("生成发货通知单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<style type="text/css">
	tr.s-tr,tr.s-tr td{height:10px !important;}
</style>
<script type="text/javascript">

function countTotal(t){
	
	var totalBoxQuantity = 0;
	var totalBranchQuantity = 0;
	var totalVolume = 0;
	var totalWeight = 0;
	var $bInput = $("input.boxQuantity");
	$bInput.each(function(){
        var quantity = 0;
        var $tr = $(this).closest("tr");
        var isEqual = null;
        if(t!=undefined){
            isEqual = (t.find(".orderItemId").val() == $tr.find(".orderItemId").val());                	
        }
        var boxQuantity=$(this).val();
        var branchPerBox=$tr.find(".branchPerBox").val();
        var perBranch=$tr.find(".perBranch").val();
        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
        var perBox = $tr.find(".perBox").val();//
        var type = productDisc(branchPerBox,perBranch,perBox);
        if(isEqual==null){
        	if(type==0){
            	var branchQuantity=accMul(boxQuantity,branchPerBox);
            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
            	if(isNaN(branchQuantity)){
            		branchQuantity = 0;
        		}
            	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
            	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
            	$tr.find(".branchQuantity").val(branchQuantity);//支数
            	$tr.find(".branchQuantityStr").html(branchQuantity);
            	quantity=accMul(branchQuantity,perBranch);
            	
            	if(isNaN(quantity)){
            		quantity = 0;
        		}
            	$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
            	
            	
            	var volume=$tr.find(".volume").val();
            	
            	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
        		
        		if(isNaN(volumeAmount)){
        			volumeAmount = 0;
        		}
        		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
        		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
            }
          	//辅料计算逻辑	
        	if(type==2){
        		var a = accDiv(perBox,10);
        		quantity = accMul(boxQuantity,a);
        	}
          	if(type==1){
          		quantity = $tr.find(".quantity").val();
          	}
          	if(type==3){
          		var branchQuantity = $tr.find(".branchQuantity").val();
          		quantity = accMul(branchQuantity,perBranch);
          	}
        }else{
        	if(type==0&&isEqual){
            	var branchQuantity=accMul(boxQuantity,branchPerBox);
            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
            	if(isNaN(branchQuantity)){
            		branchQuantity = 0;
        		}
            	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
            	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
            	$tr.find(".branchQuantity").val(branchQuantity);//支数
            	$tr.find(".branchQuantityStr").html(branchQuantity);
            	quantity=accMul(branchQuantity,perBranch);
            	
            	if(isNaN(quantity)){
            		quantity = 0;
        		}
            	$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
            	
            	
            	var volume=$tr.find(".volume").val();
            	
            	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
        		
        		if(isNaN(volumeAmount)){
        			volumeAmount = 0;
        		}
        		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
        		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
            }
          	//辅料计算逻辑	
        	if(type==2&&isEqual){
        		var a = accDiv(perBox,10);
        		quantity = accMul(boxQuantity,a);
        		$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
        	}
          	if(type==1&&isEqual){
          		quantity = $tr.find(".quantity").val();
          		$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
          	}
          	if(type==3&&isEqual){
          		var branchQuantity = $tr.find(".branchQuantity").val();
          		quantity = accMul(branchQuantity,perBranch);
          		$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
          	}
        }
   
      	
		
		var volume=$tr.find(".volume").val();
    	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
		if(isNaN(volumeAmount)){
			volumeAmount = 0;
		}
		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
	});
	$("#totalBoxQuantity").text(totalBoxQuantity);
	$("#totalBranchQuantity").text(totalBranchQuantity);
	
	var $input = $("input.quantity");
	var total = 0;
	var totalQuantity = 0;
	
	var b = $("#storeBalance").val();
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var $price_box = $tr.find(".price-box");
		var price;
		if($price_box.length==0 || $price_box.prop("checked")==false){
			price = Number($tr.find("input.price").val());
		}else{
			price = Number($tr.find("input.origMemberPrice ").val());
		}
		var quantity = $this.val();
		var amount = Number($this.val())*price;
		
		totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
		
		if(isNaN(amount)){
			amount = 0;
		}
		total = Number(total)+Number(currency(amount,false));
		$tr.find(".trprice").html(currency(amount,true));//订单行金额
		
		var weight=$tr.find(".weight").val();
    	
		var weightAmount=Number(accMul($(this).val(),weight)).toFixed(6);
		
		if(isNaN(weightAmount)){
			weightAmount = 0;
		}
		totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
		$tr.find(".lineWeightAmount").html(weightAmount);//重量
		
	});
	$("#total").text(currency(total,true));
	$("#totalVolume").text(totalVolume);
	$("#totalWeight").text(totalWeight);
	$("#totalQuantity").text(totalQuantity);
	
	var storeBalance = $("#storeBalance").val()
	$("#chae").text(currency(storeBalance-total,true));
	

	
}



function editQty(t,e){
	
	if($(t).attr("kid")=="quantity"){//平方
		if(extractNumber(t,6,false,e)){
			var $tr = $(t).closest("tr");
			var branch_quantity=0;
			var box_quantity=0;
			
			var quantity=$(t).val();
			var perBox = $tr.find(".perBox").val();
			var perBranch=$tr.find(".perBranch").val();  //每支单位数
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var type = productDisc(branchPerBox,perBranch,perBox);
			var scattered=0;
			if(perBranch!=0 && branchPerBox!=0){
				 branch_quantity=quantity/perBranch;
				 box_quantity=parseInt(branch_quantity/branchPerBox);
				 scattered=(branch_quantity%branchPerBox).toFixed(6);
			}
			if(type==2){
				box_quantity = modulo(quantity,perBox);
			}
			if(type==3){
				branch_quantity = modulo(quantity,perBranch);
			}
			$tr.find(".boxQuantity").val(box_quantity);
			$tr.find(".branchQuantity").val(branch_quantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
			
			
			countTotal($tr);
			$(t).val(quantity);
			editLineInfo(t);
			
			
		}
	}else{
		if(extractNumber(t,3,false,e)){
			var $tr = $(t).closest("tr");
			
			var branch_quantity=0;
			var box_quantity=0;
			
			if($(t).attr("kid")=="box"){//箱
				$tr.find(".scatteredQuantityStr").html(0);
				$tr.find(".scatteredQuantity").val(0);
			}else if($(t).attr("kid")=="branch"){//支
				var quantity=$(t).val();
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var box=parseInt(quantity/branchPerBox);
				var scattered=quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}else if($(t).attr("kid")=="quantity"){//平方
				var quantity=$(t).val();
				var perBranch=$tr.find(".perBranch").val();  //每支单位数
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var branch_quantity=quantity/perBranch;
				var box_quantity=parseInt(branch_quantity/branchPerBox);
				var scattered=branch_quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box_quantity);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}
			
			countTotal($tr);
			editLineInfo(t);

		}
	}
}

function editLineInfo(t){
	var $tr = $(t).closest("tr");
	var quantity = Number($tr.find(".quantity").val());
	var volume = Number($tr.find("input.volume").val());
	var weight = Number($tr.find("input.weight").val());
	var price = Number($tr.find("input.price").val());
	var lineVolume = accMul(quantity,volume);
	var lineWeight = accMul(quantity,weight);
	var lineAmount = accMul(quantity,price);
	lineVolume = lineVolume.toFixed(6);
	lineVolume = parseFloat(lineVolume);
	lineWeight = lineWeight.toFixed(6);
	lineWeight = parseFloat(lineWeight);
	$tr.find(".volumeSpan").text(lineVolume);
	$tr.find(".weightSpan").text(lineWeight);
	$tr.find(".lineAmountSpan").text(currency(lineAmount,true));
}



var map = {};
var total = 0;

$().ready(function() {

	// 地区选择
		$("#areaId").lSelect();
	
	$("#selectWarehouse").click(function(){
		var vId = $(".supplierId").val();
		warehouseWin(this,vId);
	});
	
	$("#selectDriverInfo").click(function(){
		var dId = $("#deliveryId").val();
		driverInfoWin(this,dId);
	})
	
	$("#selectSupplier").click(function(){
		supplierWin(this);
	})
	
	$(".delBtn").live("click",function(){
		var index = $(this).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid.removeRow(index);
		})
	
	})
	
	var $warehouseId = $("input[name='warehouseId']");
	var $warehouseName = $warehouseId.parent().find(".warehouseName");
	initWarehouseId = $warehouseId.val();
	initWarehouseName = $warehouseName.val();
	
	sw = 0;
	var itemIndex = 0;
	var cols = [
			{ title:'${message("行号")}', name:'line_no' ,width:60, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return item.line_no;
				}else{
					return obj.line_no;
				}
			}},
			{ title:'${message("操作")}', hidden:true, name:'ship_quantity' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var html = '';
				var bomFlag = '';
				if(obj==undefined){
					bomFlag = item.bom_flag;
					if(item.bom_flag==2){
						var sourceId= 'id_'+item.id;
						var suiteItemId = item.suite_item_id;
						html = '<a href="javascript:;" class=" btn-delete" onclick="add(this,\''+sourceId+'\','+suiteItemId+')">替换</a>';
					}else{
						html = '-';
					}
				}else{
					bomFlag = 2;
					if(sw==1){
						var sourceId= 'id_'+item.id;
						var suiteItemId = item.suite_item_id;
						html = '<a href="javascript:;" class=" btn-delete" onclick="add(this,\''+sourceId+'\','+suiteItemId+')">替换</a>';
						sw = 0;
					}else{
						html = '-';
					}
				}
				html+= '<input type="hidden"  name="shippingItems['+itemIndex+'].bomFlag" value="'+bomFlag+'">';
				return html;
			}},
			{ title:'${message("产品名称")}', name:'name' ,width:200, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return val;
				}else{
					return item.name;
				}
			}},
			{ title:'${message("产品级别")}', name:'product_grade' ,width:200, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					if(val!=null){
			            if (val == 1) {
							return "优等品";
						}else if (val == 2) {
							return "二等品";
						}else if (val == 3) {
							return "一等品";
						}else if (val == 4) {
							return "无等级";
						}
						}
				}else{
					if(item.product_grade != null){
			            if (item.product_grade == 1) {
							return "优等品";
						}else if (item.product_grade == 2) {
							return "二等品";
						}else if (item.product_grade == 3) {
							return "一等品";
						}else if (item.product_grade == 4) {
							return "无等级";
						}
						}
				}
			}},
			{ title:'${message("12211")}', name:'vonder_code' ,width:100, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return val;
				}else{
					if( item.vonder_code!=null){
						return item.vonder_code;
					}
				}
			}},
			{ title:'${message("工厂/供应商")}',[#if sbua.id != 3||isMember==1]hidden:true,[/#if] name:'manufactory_name' ,width:250, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return val;
				}else{
					if( item.manufactory_name!=null){
						return item.manufactory_name;
					}
				}
			}},
			{ title:'${message("供应商型号")}',[#if sbua.id != 3||isMember==1]hidden:true,[/#if] name:'supplier ' ,width:100, align:'center', renderer: function(val,item,rowIndex,obj){
				return item.supplier;
			}},
			{ title:'${message("产品型号")}', name:'model' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return val;
				}else{
					if( item.model!=null){
						return item.model;
					}
				}
			}},
			{ title:'${message("产品描述")}', name:'description', align:'center'},
			{ title:'${message("单位")}', align:'center',name:'unit'},
			
			{ title:'${message("制单箱数")}',[#if sbua.id == 3]hidden:true,[/#if] name:'ship_box_quantity' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				var str = '';
				var pid= item.product;
				if(pid==null){
					pid=0;
				}
				if(item.bom_flag!=2){
					str+= '<input type="hidden"  name="shippingItems['+itemIndex+'].orderItem.id" value="'+item.id+'">';
				}
				var quantity = parseInt((item.branch_quantity - item.shiped_branch_quantity-item.closed_quantity)/item.branch_per_box);
				if(isNaN(quantity)){
					//临时加的
					quantity = val;
				}
				if(type==1||type==3){
					
					str+= '-'+
					'<input type="hidden"  name="shippingItems['+itemIndex+'].boxQuantity" value="0" >'+
					'<input type="hidden"  name="shippingItems['+itemIndex+'].order.id" value="'+item.orders+'">'+
					'<input type="hidden"  name="shippingItems['+itemIndex+'].product.id" class="product_id_'+pid+'" value="'+pid+'">'+
					'<input type="hidden"  name="productIds" class="product_id_'+pid+'" value="'+pid+'">'+
					'<input type="hidden" id="id_'+item.id+'" max="'+quantity+'" class="orderItemId" orderItemId="'+item.id+'" orderId="'+item.orders+'" productId="'+item.product+'" value="'+item.id+'">'+
					'<input type="hidden" kid="box" class="t boxQuantity" sourceId="id_'+item.id+'"  value="'+val+'" minData="0" maxData="'+quantity+'">';
				}else{
				str+= 
				'<input type="hidden"  name="shippingItems['+itemIndex+'].order.id" value="'+item.orders+'">'+
				'<input type="hidden"  name="shippingItems['+itemIndex+'].product.id" class="product_id_'+pid+'" value="'+pid+'">'+
				'<input type="hidden"  name="productIds" class="product_id_'+pid+'" value="'+pid+'">'+
				'<input type="hidden" id="id_'+item.id+'" max="'+quantity+'" class="orderItemId" orderItemId="'+item.id+'" orderId="'+item.orders+'" productId="'+item.product+'" value="'+item.id+'">'+
				'<div class="nums-input ov">'+
	            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
	            	'<input type="text" kid="box" class="t boxQuantity" sourceId="id_'+item.id+'" name="shippingItems['+itemIndex+'].boxQuantity" value="'+val+'" minData="0" maxData="'+quantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
	        	'</div>';
				
				}
				return str;	
			}},
			
			{ title:'${message("支数")}',[#if sbua.id == 3]hidden:true,[/#if] name:'ship_branch_quantity' ,align:'center', width:110, renderer: function(val,item,rowIndex, obj){
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				var branchQuantity='';
				if(obj==undefined){
					branchQuantity = val;
				}
				var branchPerBox = item.branch_per_box == null?0:item.branch_per_box;
				var perBox = item.per_box == null?0:item.per_box;
				var perBranch = item.per_branch == null?0:item.per_branch;
				if(type==1||type==2){
					branchQuantity=0;
					return '-'+
					'<input type=hidden class="branchPerBox" name="shippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
					'<input type=hidden class="perBox"  value="'+perBox+'" />'+
					'<input type=hidden class="perBranch" name="shippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
					'<input type="hidden"  kid="branch" class="t branchQuantity" sourceId="id_'+item.id+'" name="shippingItems['+itemIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0"  >';
				}else{
// 				var html='<span class="branchQuantityStr">'+branchQuantity+'</span>'+
					var html='<input type=hidden class="branchPerBox" name="shippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
					'<input type=hidden class="perBox"  value="'+perBox+'" />'+
					'<input type=hidden class="perBranch" name="shippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
// 					'<input type="hidden" name="shippingItems['+itemIndex+'].branchQuantity" class="branchQuantity text" value="'+branchQuantity+'" />'+
					'<div class="nums-input ov">'+
	            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
	            	'<input type="text"  kid="branch" class="t branchQuantity" sourceId="id_'+item.id+'" name="shippingItems['+itemIndex+'].branchQuantity" value="'+val+'" minData="0" maxData="'+(item.branch_quantity-item.shiped_branch_quantity-item.closed_quantity)+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
	        	'</div>';
				return html;
				}
			}},
			{ title:'${message("零散支数")}',[#if sbua.id == 3]hidden:true,[/#if] name:'' ,align:'center', width:110, renderer: function(val,item,rowIndex, obj){
				var branchPerBox=item.branch_per_box;
				var scatteredQuantity=item.ship_branch_quantity%item.branch_per_box;
				if(isNaN(scatteredQuantity)){
					scatteredQuantity = 0;
				}
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				if(type==2||type==3||type==1){
					return '-'+'<input type="hidden" name="shippingItems['+itemIndex+'].scatteredQuantity" value="0" />';
				}
				var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
					'<input type="hidden" name="shippingItems['+itemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
				return html;
				
			}},
			
			{ title:'${message("数量")}', name:'ship_quantity', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
				var quantity='';
				if(obj==undefined&&item.per_branch!=undefined){
					quantity = accMul(item.ship_branch_quantity,item.per_branch);
				}
				if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
					quantity=item.ship_quantity;
					
				}
					var text = '<div class="lh20">'+
					'<div class="nums-input ov square">'+
			        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
			        	'<input type="text" kid="quantity" class="t quantity"  name="shippingItems['+itemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
			        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
			        	'<input type="hidden" class="minPriceApplyQuantity" />'+
						'<input type="hidden" class="maxPriceApplyQuantity" />'+
			    	'</div></div>';
			    	return text;				
			}},
 			{ title:'${message("产品价格")}', name:'price' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
 				var html = '<span class="red">'+currency(val,true)+'</span><input type="hidden" class="price" value="'+val+'">';
 				return html;
 			}},
			{ title:'${message("金额")}', name:'volume' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
 				var lineAmount = item.ship_quantity*item.price;
				var html = '<span class="red lineAmountSpan">'+currency(lineAmount,true)+'</span>';
 				return html;
 			}},
			{ title:'${message("体积")}',[#if sbua.id == 3]hidden:true,[/#if] name:'volume' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var volume = (val!="")?val:0;
				var lineVolume = volume*item.ship_quantity;
				lineVolume = lineVolume.toFixed(6);
				lineVolume = parseFloat(lineVolume);
				var html = '<span class="volumeSpan">'+lineVolume+'</span>'+
					'<input type="hidden" class="text volume" value="'+volume+'" />';
				return html;
					
			}},
			{ title:'${message("重量")}',[#if sbua.id == 3]hidden:true,[/#if] name:'weight' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var weight = (val!="")?val:0;
				var lineWeight = weight*item.ship_quantity;
				lineWeight = lineWeight.toFixed(6);
				lineWeight = parseFloat(lineWeight);
				var html = '<span class="weightSpan">'+lineWeight+'</span>'+
					'<input type="hidden" class="text weight" value="'+weight+'" />';
				return html;
					
			}},
			{ hidden:true, name:'line_no' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var lineNo;
				var warehouseId;
				var orderSn;
				var suite_quantity = '';
				var html = '';
				if(obj==undefined){
					lineNo = item.line_no;
					orderSn = item.order_sn;
					if(item.bom_flag==2){
						suite_quantity = item.suite_quantity;
					}
				}else{
					lineNo = obj.line_no;
					orderSn = obj.order_sn;
					suite_quantity = obj.suite_quantity;
				}
				html+= '<input type="hidden" class="lineNo" name="shippingItems['+itemIndex+'].lineNo" value="'+lineNo+'">'+
							'<input type="hidden" class="orderSn"  value="'+orderSn+'">'+
							'<input type="hidden" class="suiteQuantity"  value="'+suite_quantity+'">';
				if(item.suite_item_id!=undefined && item.suite_item_id!=null){
					html+= '<input type="hidden" class="suiteItemId suiteItemId_'+item.suite_item_id+'" value="'+item.suite_item_id+'">';
				}
				if(item.bom_flag==1){
					html+= '<input type="hidden" class="hideInput">';
				}
				return html;
			}},
			
			{ title:'${message("备注")}',name:'seller_memo',width:200,align:'center',renderer:function(val,item,rowIndex,obj){
				var memo='';
				if(obj==undefined){
					memo = item.seller_memo;
				}
				var html=
					'<input type="text" name="shippingItems['+itemIndex+'].memo" class="text" value="'+memo+'" />';
				return html;
				
			}},
			{ title:'${message("订单编号")}', name:'order_sn' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
				itemIndex++;
				if(obj==undefined){
					return item.order_sn;
				}else{
					return obj.order_sn;
				}
			}},
	];
    
    var param = jQuery.param( $("#dataForm").serializeObject(), true);
    ajaxSubmit('',{
		method:'post',
		url:'shipping_operate_list_data.jhtml',
		data: param,
		callback: function(resultMsg) {
			var data = $.parseJSON(resultMsg.content);
			var shippingItems = data.shippingItems;
		
		    
		    $shipping_mmGrid = $('#table-shipping').mmGrid({
				height:'auto',
				fullWidthRows:true,
				autoLoad:true,
				useCountText:false,
				checkCol:false,
		        cols: cols,
		        items: shippingItems,
		        callback:function(){
		        	$(".hideInput").each(function(){
		        		$(this).closest("tr").hide();
		        	});
		        	countTotal();
		        }
		    });
		    
		    
		}
	})
	
	 /**初始化附件*/
    var attachIndex=0;
	var attachcols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues['shippingAttachs['+attachIndex+'].url']=url;
			hideValues['shippingAttachs['+attachIndex+'].suffix']=fileObj.suffix;
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName:'shippingAttachs['+attachIndex+'].name',
				hideValues:hideValues
			});               
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text file_memo" name="shippingAttachs['+attachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			attachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: attachcols,
        checkCol: false,
        autoLoad: true
    });
    
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
    var $deleteAttachment = $(".deleteAttachment");
	
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	$("#openArea").bindQueryBtn({
		type:'area',
		title:'${message("查询地区")}',
		url:'/basic/area/select_area.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var $tr =$this.closest("tr");
				$(".areaId").val(rows[0].id);
				$(".areaName").val(rows[0].full_name);
				//$("input[name='address']").val('');
				$("input[name='zipCode']").val('');
			}
		}
	});
	
	$("#selectInvoice").bindQueryBtn({
		type:'supplier',
		title:'${message("查询发票抬头")}',
		url:'/member/store/select_store_invoice.jhtml?storeId='+${store.id},
		callback:function(rows){
			if(rows.length>0){
				var t=$this.closest("table.input-edit")
				t.find("input[name='invoiceTitle']").val(rows[0].invoice_title);
			}
		}
	});
	
	 //打开选择地址界面
    $("#addReceiveAddress").click(function(){
        var storeId = $(".storeId").val();
        if(storeId==""){
            $.message_alert('请选择客户');
        }else{
        		select_store_address(storeId);
        }
    });
    
    //更换地址
    function select_store_address(storeId){
     $("#addReceiveAddress").bindQueryBtn({
        	        type:'store',
        	        bindClick:false,
        	        title:'${message("更换地址")}',
        	        url:'/member/store/select_store_address.jhtml?storeId='+storeId,
        	        callback:function(rows){
        	            if(rows.length>0){
        	            	var row = rows[0];
        	                $("input[name='consignee']").attr("value",row.consignee);
        	                $("input[name='phone']").attr("value",row.mobile);
        	                $("input[name='address']").attr("value",row.address);
        	                $("input[name='zipCode']").attr("value",row.zip_code);
        	                $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
        	                $(".select_area").find(".fieldSet").empty();
        	                var areaId = row.area;
        	                if(areaId==null)areaId='';
        	                var areaName = row.area_full_name;
        	                if(areaName==null)areaName='';
        	                var treePath = row.tree_path;
        	                if(treePath==null)treePath='';
        	               // $(".select_area").find(".fieldSet").append('<input type="hidden" id="areaId" name="areaId"  value="'+areaId+'" treePath="'+treePath+'" />');
        	                //地区选择
        	               // $("#areaId").lSelect();
        	                $("input[name='areaId']").val(areaId);
        	                $("input[name='areaName']").val(areaName);
        	                
        	            }
        	        }   
        	    });
     }
    

});			

//查询库存
function queryStock(){
	countTotal();
	$("input[name='productIds']").each(function(){
		var $this = $(this);
		var productId = $this.val();
		var $tr = $this.closest("tr");
		var warehouseId = $tr.find(".warehouseId").val();
		if(warehouseId==""){
			$tr.find("#u"+productId).html(0);
			$tr.find("#a"+productId).html(0);
		}else{
			queryLineStock($tr,productId);
		}
	});
}

function queryLineStock(t,productId){
	var $tr = $(t);
	var warehouseId = $tr.find(".warehouseId").val();
	if(warehouseId==""){
			$tr.find("#u"+productId).html(0);
			$tr.find("#a"+productId).html(0);
	}else{
		ajaxSubmit('',{
			url: 'getStock.jhtml?warehouseId='+warehouseId+'&productIds='+productId,
			method: "post",
			async:false,
			callback:function(resultMsg){
				var data = $.parseJSON(resultMsg.content);
				if(data.length>0){
					$tr.find("#u"+productId).html(data[0].useable_stock);
					$tr.find("#a"+productId).html(data[0].actual_stock);
				}else{
					$tr.find("#u"+productId).html(0);
					$tr.find("#a"+productId).html(0);
				}
			}
		})
	}
}

function sub(e){
	 var data = $("#inputForm").serializeObject();
	   var result = false;
	   ajaxSubmit(e,{
			url:"save.jhtml",
			method:"post",
			async: false,
			data:data,
			isConfirm:true,
			confirmText:'您确定要保存吗？',
			callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				//2019-05-16 冯旗 保存完后跳转到view页面
					location.href= '/b2b/shipping/view.jhtml?id='+resultMsg.objx;
				});
			
			}
		});
}

function childMethod(){
   var data = $("#inputForm").serializeObject();
   var result = false;
   ajaxSubmit(null,{
		url:"save.jhtml",
		method:"post",
		async: false,
		data:data,
		callback:function(resultMsg){
			result = true;
		}
	});
	
};

function clearSelectDriverInfo(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("table");
		$this.prev("input").val("");
		$tr.find(".driverInfoPhone").val("");
		$tr.find(".driverInfoCarNumber").val("");
	}
}

function clearSelectSupplier(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("table");
		$this.prev("input").val("");
		$tr.find(".virtualWarehouseName").val("");
		$tr.find(".virtualWarehouseId").val("");
	}
}

//sa
 function query_product(e,suiteItemId,callback){
		$(e).bindQueryBtn({
					type:'product',
					bindClick:false,
					title:'${message("查询替代件")}',
					url:'/b2b/shipping/replace_product_list.jhtml?multi=2&suiteItemId='+suiteItemId,
					callback:function(rows){
						if(callback(rows)==false){
							return false;
						}
					}
		});
}

//仓库弹框
function warehouseWin(e,vId){
	var $saleOrgId = $(".saleOrgId").val();
	var sbuId = $(".sbuId").val();
	var $e=$(e);
	var type;
	var warehouseType=${order.warehouse.typeSystemDict.id};
	if(vId==""){
		type = 0;
	}else{
		type = '';
	}
	$(e).bindQueryBtn({
		bindClick:false,
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?type='+type+'&warehouseType='+warehouseType+'&TypesaleOrgId='+$saleOrgId+'&sbuId='+sbuId,
		callback:function(rows){
			if(rows.length>0){
				var t=$(e).closest("table.input-edit");
				t.find(".warehouseId").val(rows[0].id);
				t.find(".warehouseName").val(rows[0].name);
				
			}
		}
	});
	
	
}
function driverInfoWin(e,dId){
	if (dId = 'undefined') {
		dId = '';
	}
	$(e).bindQueryBtn({
		bindClick:false,
		type:'driverInfo',
		title:'${message("查询车辆信息")}',
		url:'/basic/driver_info/select_driverInfo.jhtml?deliveryCorpId='+dId,
		callback:function(rows){
			if(rows.length>0){
				var t=$(e).closest("table.input-edit");
				t.find("input[name='driverInfoId']").val(rows[0].id);
				t.find(".driverInfoName").val(rows[0].name);
				t.find(".driverInfoPhone").val(rows[0].phone);
				t.find(".driverInfoCarNumber").val(rows[0].car_number);
			}
		}	
	});
}

function supplierWin(e){
	$(e).bindQueryBtn({
		bindClick:false,
		type:'supplier',
		title:'${message("查询供应商")}',
		url:'/member/store/select_store.jhtml?type=provider&isMember=1',
		callback:function(rows){
			if(rows.length>0){
				var t=$(e).closest("table.input-edit")
				t.find("input[name='supplierId']").val(rows[0].id);
				t.find("input[name='supplierName']").val(rows[0].name);
				t.find(".warehouseId").val("");
				t.find(".warehouseName").val("");
			}
		}
	});
}

//统一设置发货仓
//仓库弹框
function allWarehouse(e,vId){
	var $e=$(e);
	var type;
	if(vId==""){
		type = 0;
	}else{
		type = '';
	}
	$(e).bindQueryBtn({
		bindClick:false,
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?type='+type
	});
	
	
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;新增发货单
	</div>
	<form id="dataForm">
		[#list quantity as qty]
			<input type="hidden" name="quantity" value="${qty}">
		[/#list]
		[#list ids as id]
			<input type="hidden" name="ids" value="${id}">
		[/#list]
		[#list boxQuantity as bqty]
			<input type="hidden" name="boxQuantity" value="${bqty}">
		[/#list]
		[#list branchQuantity as brqty]
			<input type="hidden" name="branchQuantity" value="${brqty}">
		[/#list]
		[#list closedQuantity as cqty]
			<input type="hidden" name="closedQuantity" value="${cqty}">
		[/#list]
	</form>
	<form id="inputForm">
		<input type="hidden" name="flag" value="0">
		<div class="tabContent">
    		<table class="input input-edit">
    			<tr>
					<th >
						${message("发货单编号")}:
					</th>
					<td>
						
					</td>
					<th>
						${message("客户")}:
					</th>
					<td>
						<input name="storeId" class="storeId" type="hidden" value="${store.id}">${store.name}
					</td>
					<th>
						${message("发货单状态")}:
					</th>
					<td>
					
					</td>
					<th>仓库:</th>
	        		<td>
						<span class="search" style="position:relative">
							<input name="warehouseId" class="text warehouseId" type="hidden" value="${warehouseId}">
							<input class="text warehouseName" maxlength="200" type="text" value="${warehouseName}" onkeyup="clearSelect(this)">
							<input type="button" class="iconSearch" value="" id="selectWarehouse">
						</span>
	        		</td>
				</tr>
	       		<tr>
	       			<th>承运商:</th>
		        		<td>
		        			<select class="text" name="deliveryId" id="deliveryId">
		        				<option value="">-- 请选择 --</option>
								[#list deliveryCorps as dc]
								<option value="${dc.id}" >${dc.name}</option>
								[/#list]
							</select>
		        		</td>
	        		<th>配送方式:</th>
					<td>
					<select class="text" name="shippingMethod">
							[#list shippingMethods as sm]
							<option value="${sm.name}" >${sm.name}</option>
							[/#list]
						</select>
					</td>
	        		<th>${message("机构")}:</th>
	        		<td>
						<input name="saleOrgId"  type="hidden" value="${saleOrg.id}">${saleOrg.name}
	        		</td>
	        		<th>经营组织:</th>
	        		<td>
						<input type="hidden" name="organizationId" value="${organizationId}">
						${organizationName}
	        		</td>
				</tr>
	        	<tr>
	        		<th>${message("计划发货日期")}:</th>
	        		<td>
		        		<input type="text" class="text" name="shippingTime"  onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" />
		        	</td>
	        		<th>司机姓名:</th>
	        		<td>
	        			<span class="search" style="position:relative">
							<input name="driverInfoId" class="text driverInfoId" type="hidden">
							<input class="text driverInfoName" maxlength="200" type="text"onkeyup="clearSelectDriverInfo(this)" >
							[#--<input type="button" class="iconSearch" value="" id="selectDriverInfo">--]
						</span>
	        		</td>
	        		<th>车主电话:</th>
	        		<td><input type="text" class="text driverInfoPhone" btn-fun="clear"  /></td>
	        		<th>车牌号:</th>
	        		<td>
	        			<input type="text" class="text driverInfoCarNumber" btn-fun="clear"  />
	        		</td>
	        	</tr>
	        	<tr>
	        		<th>${message("发票抬头")}:</th>
	        		<td>
						<span class="search" style="position:relative">
							[#assign invoiceTitle='']
							[#list store.storeInvoiceInfos as invoice]
								[#if invoice_index == 0]
								[#assign invoiceTitle=invoice.invoiceTitle]
								[/#if]
							[/#list]
							<input name="invoiceTitle" class="text invoiceTitle" maxlength="200" type="text" value="${invoiceTitle}" onkeyup="clearSelect(this)">
							<input type="button" class="iconSearch" value=""  id="selectInvoice">
						</span>
	        		</td>
	        		<th>
						${message("创建时间")}:
					</th>
					<td>
						
					</td>
					  <th>${message("Sbu")}:</th>
          	   <td>
            	<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbua.id}"/>
            	<span   id="sbuName">
            	 ${sbua.name}
            	</span>
          	   </td>
	        	<!-- 	<th>
	        			${message("sbu")}:
	        		</th>
        			<td>
	        			<select id="sbuId" name="sbuId" class="text">
	    				[#list sbus as sbu]
	    				<option value="${sbu.sbu.id}"[#if sbuIds==sbu.sbu.id] selected[/#if]>${sbu.sbu.name}</option>
	    				[/#list]
	    				</select>
        			</td> -->
        			<th>
        				${message("发运方式")}:
        			</th>
        			<td>
        				<select id="smethodId" name="smethod" class="text">
        				[#list sbuItems as sbuItem]
	    				<option value="${sbuItem.shippingMethod.id}"[#if sbuItem.isDefault ==true] selected[/#if]>${sbuItem.shippingMethod.value}</option>
	    				[/#list]
	    				</select>
        			</td>
	        	</tr>
	        	<tr>
	        		<th>备注:</th>
	        		<td colspan="7"><textarea class="text" name="memo">${orderMemo}</textarea></td>
	        	</tr> 
        	</table>
			<div class="title-style">
				${message("发货项")}:
			</div>
			<table id="table-shipping"></table>
			
			<div class="title-style">${message("发货汇总")}
			</div>
           <table class="input input-edit">
           	<tr>
           	  <!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积-->
           	  [#if sbua.id != 3]
           		<th>${message("发货箱数")}:</th>
                <td><span id="totalBoxQuantity"></span></td>
                <th>${message("发货支数")}:</th>
                <td><span id="totalBranchQuantity"></span></td>
               [/#if]
               
           		<th>${message("发货数量")}:</th>
                <td><span id="totalQuantity"></span></td>
                
                [#if sbua.id != 3]
                <th>${message("发货重量")}:</th>
                <td><span id="totalWeight"></span></td>
                [/#if]
           </tr>
           
           [#if sbua.id != 3]
			<tr>
            	<th>${message("发货体积")}:</th>
                <td><span id="totalVolume"></span></td>
            </tr>
            [/#if]
         </table>
         
			<div class="title-style">${message("收货信息")}
				<div class="btns">
	                <a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
            	</div>
			</div>
			<table class="input input-edit">
				<tr>
					<th>收货人:</th>
	        		<td>
	        			<input type="text" class="text" name="consignee" value="${consignee}"  btn-fun="clear"  readonly="readonly"/>
	        		</td>
	        		<th>收货人电话:</th>
	        		<td>
	        			<input type="text" class="text" name="phone" value="${phone}" btn-fun="clear" readonly="readonly"/>
	        		</td>
	        		<th>收货地区邮编:</th>
	        		<td>
	        			<input type="text" class="text" name="zipCode" value="${zipCode}"  btn-fun="clear"  readonly="readonly"/>
	        		</td>
	        		<th>地址外部编码:</th>
	        		<td>
	        			<input type="text" class="text" name="addressOutTradeNo" value="${addressOutTradeNo}"  btn-fun="clear"  readonly="readonly"/>
	        		</td>
				</tr>
				<tr>
					<th>收货地区:</th>
	        		<td colspan="3">
	        			<span class="search" style="position:relative">
		                    <input type="hidden" name="areaId" class="text areaId" value="${area.id}" btn-fun="clear"/>
		                    <input type="text" name="areaName" class="text areaName"  value="${area.fullName}" maxlength="200" onkeyup="clearSelect(this)"   readonly="readonly"/>
		                    [#--<input type="button" class="iconSearch" value="" id="openArea">--]
	                    </span>
	        		</td>
	        		<th>收货地址:</th>
	        		<td colspan="3">
	        			<input type="text" class="text" name="address" value="${address}"  btn-fun="clear"  readonly="readonly"/>
	        		</td>
	        	</tr>
			</table>			
			
			<div class="title-style">
				${message("附件信息")}:
				<div class="btns">
					<a href="javascript:;" id="addAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-attach"></table>
			
		</div>
		    <div class="fixed-top">
		        <input type="button" id="submit_button" class="button sureButton" value="${message("保存")}" onclick="sub(this)">
		        <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		    </div>
	</form>
</body>
</html>