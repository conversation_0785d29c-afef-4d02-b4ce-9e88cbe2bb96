<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("发货通知列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">

function editPrice(t,e){
	extractNumber(t,2,false,e)
}

function check_view(url){
	var w = $(window).width();
	var h = $(window).height();
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
		title:'${message("查看对账单")}',
		width:w,
		height:h,
		content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
		ok: null,
		cancel: null,
		onOk: function() {
		
		}
	});
	$dialog.find(".dialogContent").css("height",h+"px");
	$dialog.css("top",0+"px").css("max-height",h+"px");
	

}
function edit(url){
	parent.change_tab(0,url);
}
function editQty(t,e){
	if(extractNumber(t,3,false,e)){
		
	
// 	var $bInput = $("input.boxQuantity");
// 	$bInput.each(function(){
//         var $tr = $(this).closest("tr");
//         var boxQuantity=$(this).val();
//         var branchPerBox=$tr.find(".branchPerBox").val();
        
//         var perBox=$tr.find(".perBox").val();
//         if(boxQuantity!=''){
        		
//         	var branchQuantity=accMul(boxQuantity,branchPerBox);
//         	if(isNaN(branchQuantity)){
//         		branchQuantity = 0;
//     		}
//         	$tr.find(".branchQuantity").val(branchQuantity);//支数
//         	$tr.find(".branchQuantityStr").html(branchQuantity);
//         	var quantity=accMul(boxQuantity,perBox);
//         	if(isNaN(quantity)){
//         		quantity = 0;
//     		}
//         	$tr.find(".quantity_sq").val(quantity);//数量
//         	$tr.find(".quantityStr").html(quantity);
//         }
// 	});

		var $tr = $(t).closest("tr");
		var branchQuantity=0;//支数
		var boxQuantity = 0;//箱数
		var branchPerBox=$tr.find(".branchPerBox").val();//每箱支数
		var perBranch=$tr.find(".perBranch").val();
// 		var branchQuantity=accMul(branchQuantity,branchPerBox);
		if($(t).attr("kid")=="box"){
			boxQuantity = $(t).val();//箱数
			branchQuantity = accMul(boxQuantity,branchPerBox);//支数
			if(isNaN(branchQuantity)){
    			branchQuantity = 0;
			}
    		$tr.find(".branchQuantity").val(branchQuantity);//支数
    		$tr.find(".scatteredQuantityStr").html(0);//零散支数
			$tr.find(".scatteredQuantity").val(0);
		}else if($(t).attr("kid")=="branch"){
			branchQuantity = $(t).val();//支数
			boxQuantity = parseInt(branchQuantity/branchPerBox);//箱数
			var scattered = branchQuantity%branchPerBox;
			$tr.find(".boxQuantity").val(boxQuantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}
		var quantity=accMul(branchQuantity,perBranch);
    	if(isNaN(quantity)){
    		quantity = 0;
		}
		$tr.find(".quantity").val(quantity);//数量
    	$tr.find(".quantityStr").html(quantity);
    	
    	var row = $mmGrid.row($tr.index());
    	if($tr.find(".mmg-check").prop("checked")){
    		map[row.id] = Number(quantity);
    		mapBx[row.id] = Number(boxQuantity);
    		mapBc[row.id] = Number(branchQuantity);
        	$(".total").text(countTotal());
    	}else{
    		if(map[row.id]!=undefined){
    			map[row.id] = undefined;
    			mapBx[row.id] = undefined;
    			mapBc[row.id] = undefined;
    		}
    	}

		

    	
// 	var $bInput = $("input.branchQuantity");
// 	$bInput.each(function(){
//         var $tr = $(this).closest("tr");
//         var boxQuantity=$(this).val();
//         var branchPerBox=$tr.find(".branchPerBox").val();
        
//         var perBox=$tr.find(".perBox").val();
//         if(boxQuantity!=''){
//         	if($(this).attr("kid")=="box"){
        		
//         		var branchQuantity=accMul(boxQuantity,branchPerBox);
//         		if(isNaN(branchQuantity)){
//         			branchQuantity = 0;
//     			}
//         		$tr.find(".branchQuantity").val(branchQuantity);//支数
//         		$tr.find(".branchQuantityStr").html(branchQuantity);
//         	}else if($(this).attr("kid")=="branch"){
//         		var branchQuantity=accMul(boxQuantity,branchPerBox);
//         		if(isNaN(branchQuantity)){
//         			branchQuantity = 0;
//     			}
//         		$tr.find(".boxQuantity").val(boxQuantity);//支数
//         	}
//         	var quantity=accMul(boxQuantity,perBox);
//         	if(isNaN(quantity)){
//         		quantity = 0;
//     		}
//         	$tr.find(".quantity_sq").val(quantity);//数量
//         	$tr.find(".quantityStr").html(quantity);
//         }
// 	});
	}
}	

function calculateTotalBoxQuantity(){
	var $input = $("input.boxQuantity");
	var $checkbox = $tr.find("input.mmg-check:checked");
	var row = $.data($tr[0],'item');
	map[row.id] = num;
	$(".total").text(countTotal());

}
var map = {};
var mapT = {};
var mapBx = {};
var mapBc = {};
var mapCq = {};
var total = 0;


function removeCheck(){
  

   $("input.mmg-check").attr("checked",false);//取消全选
   $mmGrid.deselect();


}
$().ready(function() {

	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?isSelect=0',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$("input.storeId").val(row.id);
				$("input.storeName").val(row.name);
				$mmGrid.load();
			}
		}
	});
	$("#selectSupplier").bindQueryBtn({
		type:'supplier',
		title:'${message("查询供应商")}',
		url:'/member/store/select_store.jhtml?type=provider',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$("input.supplierId").val(row.id);
				$("input.supplierName").val(row.name);
				$mmGrid.load();
			}
		}
	});
	//查询机构	
		$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='saleOrgName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='saleOrgName']").val();
				}
				
		
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".saleOrgId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="saleOrgId" class="text saleOrgId saleOrgId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
						$(".saleOrg").append(vhtml);
					}
				}
				$("input[name='saleOrgName']").attr("value",allName);
			}
		}
	});
	
	function addQtyBtnEvent(){
		var $input = $("input.boxQuantity");
		$input.keypress(function(event) {
			var key = event.keyCode ? event.keyCode : event.which;
			var value = ""+$(this).val();
			if(value.indexOf(".")>0 && key==46)return false;
			if ((key >= 48 && key <= 57) || key==8|| key==46) {
				return true;
			} else {
				return false;
			}
		});
		$input.keyup(function(event) {
			var $this = $(this);
			var value = $this.val();
			if(value.indexOf("。")>=0){
				$(this).val(value.replace("。", ""));
			}	
		});
		
		$input.focus(function(event) {
			var $this = $(this);
			var $tr = $this.closest("tr");
			var $checkInput = $tr.find("input.mmg-check");
			if(!$checkInput.prop("check")){
				$mmGrid.select($tr.index());
			}
		});
	
		$input.bind("input", function(event) {
			event.stopPropagation();
			if (event.type != "propertychange" || event.originalEvent.propertyName == "value") {
				var $this = $(this);
				var $tr = $this.closest("tr");
				var num = Number($this.val());
				var max = Number($this.attr("max"));
				if(num>max){
					num = max;
					$this.val(num);
				}
				var $checkbox = $tr.find("input.mmg-check");
				if($checkbox.prop("checked")){
					var row = $.data($tr[0],'item');
					map[row.id] = num;
					$(".total").text(countTotal());
				}
			}
		});
	
	}
	
	var cols = [
		{ title:'${message("订单编号")}', name:'order_sn' ,width:120, align:'center',renderer:function(val,item){
			var url = '../order/view.jhtml?isEdit=0&readOnly=1&id='+item.orders;
			var html = '<a href="javascript:void(0);" onclick="edit(\''+url+'\')" class="red">'+val+'</a>';
			return html;
		}},
		{ title:'${message("客户名称")}', name:'store_name' ,width:70, align:'center' },
		{ title:'${message("SBU")}', name:'sb_name' ,width:70, align:'center' },
		{ title:'${message("仓库")}', name:'warehouse_name' ,width:90, align:'center' },
		{ title:'${message("机构")}', name:'sale_org_name' ,width:90, align:'center' },
		{ title:'${message("产品名称")}', name:'name' ,width:150, align:'center'},
		{ title:'${message("产品描述")}', name:'description' ,width:150, align:'center'},
		{ title:'${message("制单箱数")}', name:'' ,width:100, align:'center', renderer: function(val,item,rowIndex){
// 			var quantity = item.box_quantity-item.ship_box_quantity;
            
			var maxQuantity = parseInt((item.branch_quantity - item.ship_branch_quantity-item.closed_quantity)/item.branch_per_box);
			quantity = maxQuantity;
			var num = mapBx[item.id];
			if(num!=undefined){
				quantity = num;
			}
			if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
				quantity=0;
				return '-'+
				'<input type="hidden" kid="box" class="t boxQuantity" _id="'+item.id+'" value="'+quantity+'" minData="0" maxData="'+maxQuantity+'" >';
			}else{
			
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
            	'<input type="text" kid="box" class="t boxQuantity" _id="'+item.id+'" value="'+quantity+'" minData="0" maxData="'+maxQuantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
        	'</div>';
        	return html;
			}
			//return '<input type="number" class="text quantity" value="'+quantity+'" min="0" max="'+quantity+'" _id="'+item.id+'">';	
		}},
		{ title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:110, renderer: function(val,item,rowIndex, obj){
// 			var quantity = (item.box_quantity-item.ship_box_quantity)*item.branch_per_box;
// 			var html='<span class="branchQuantityStr">'+quantity+'</span>'+
// 				'<input type=hidden class="branchPerBox" value="'+item.branch_per_box+'" /> '+
// 				'<input type=hidden class="perBox" value="'+item.per_box+'" />'+
// 				'<input type=hidden class="perBranch" value="'+item.per_branch+'" />'+
// 				'<input type="hidden" name="" class="branchQuantity text" value="'+quantity+'" />';
			var maxQuantity = item.branch_quantity - item.ship_branch_quantity - item.closed_quantity;
			quantity = maxQuantity;
			var num = mapBc[item.id];
			if(num!=undefined){
				quantity = num;
			}
			if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
				quantity=0;
				return '-'+
				'<input type="hidden" kid="branch" class="t branchQuantity" value="'+quantity+'" minData="0" maxData="'+maxQuantity+'" >'+
				'<input type="hidden" class="branchPerBox" value="'+item.branch_per_box+'" /> '+
				'<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
				'<input type="hidden" class="perBranch" value="'+item.per_branch+'" />'+
				'<input type="hidden" class="closedQuantity" value="'+item.closed_quantity+'" />';
			}else{
				var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
            	'<input type="text" kid="branch" class="t branchQuantity" value="'+quantity+'" minData="0" maxData="'+maxQuantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
            	'<input type="hidden" class="branchPerBox" value="'+item.branch_per_box+'" /> '+
				'<input type="hidden" class="perBox" value="'+item.per_box+'" />'+
				'<input type="hidden" class="perBranch" value="'+item.per_branch+'" />'+
				'<input type="hidden" class="closedQuantity" value="'+item.closed_quantity+'" />'+
        	'</div>';
			return html;
			}
		}},
		
		{ title:'${message("数量")}', name:'quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
			
			var quantity = (item.branch_quantity - item.ship_branch_quantity - item.closed_quantity)*item.per_branch;
			quantity = quantity.toFixed(6);
			quantity = parseFloat(quantity);
			var num = map[item.id];
			if(num!=undefined){
				quantity = num;
			}
			
			if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
				quantity=item.quantity;
				var text = 
				'<div class="nums-input ov">'+
		        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
		        	'<input type="text"   class="t quantity" value="'+quantity+'" maxData="'+quantity+'" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
		        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
		    	'</div>';
		    	return text;
				
			}else{
				var html='<span class="quantityStr">'+quantity+'</span>'+
				'<input type="hidden" name="" class="quantity text" value="'+quantity+'" />';
				return html;	
			}
				
			
				
		}},
		{ title:'${message("收货人")}', name:'consignee' ,width:70, align:'center' },
		{ title:'${message("收货人电话")}', name:'phone' ,width:90, align:'center' },
		{ title:'${message("收货地区")}', name:'area_full_name' ,width:90, align:'center' },
		{ title:'${message("收货地址")}', name:'address' ,width:90, align:'center' },
		{ title:'${message("经营组织")}', name:'organization_name' ,width:90, align:'center' },
		[#--{ title:'${message("供应商")}', name:'supplier_name' , align:'center'},--]
		{ title:'${message("12211")}', name:'vonder_code' ,width:80, align:'center' },
		{ title:'${message("产品型号")}', name:'model' ,width:80, align:'center'},
		{ title:'${message("工厂/供应商")}',[#if isMember==1]hidden:true,[/#if]  name:'manufactory_name' ,width:250, align:'center'},
		{ title:'${message("产品级别")}', name:'product_grade' ,width:80, align:'center',renderer:function(val){
			if(val!=null){
            if (val == 1) {
				return "优等品";
			}else if (val == 2) {
				return "二等品";
			}else if (val == 3) {
				return "一等品";
			}
			}
		}},
		{ title:'${message("下单支数")}', name:'branch_quantity' ,width:60, align:'center' },
		{ title:'${message("已关闭支数")}', name:'closed_quantity' ,width:60, align:'center' },
		{ title:'${message("已关闭数量")}', name:'closed_quantity_nb' ,width:60, align:'center' },
		{ title:'${message("发货支数")}', name:'ship_branch_quantity' ,width:60, align:'center' },
		{ title:'${message("发货数量")}', name:'ship_quantity' ,width:60, align:'center' },
		{ title:'${message("零散支数")}', name:'' ,align:'center', width:110, renderer: function(val,item,rowIndex, obj){
			var branchPerBox=item.branch_per_box;
			var scatteredQuantity=(item.branch_quantity - item.ship_branch_quantity - item.closed_quantity)%item.branch_per_box;
			if(isNaN(scatteredQuantity)){
				scatteredQuantity = 0;
			}
			var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
				'<input type="hidden" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
			return html;
		}},
		//{ title:'${message("发货仓")}', name:'warehouse_name' ,width:120, align:'center' },
				{ title:'${message("体积")}', name:'volume' ,width:60, align:'center'},
		{ title:'${message("交货期")}', name:'delivery_time' ,width:100, align:'center',renderer:function(val){
			if(val!=null){
				var str = val;
				return str.substring(0,10);
			}
		}},
		{ title:'${message("创建人")}', name:'store_member_name' ,width:120, align:'center'},
		{ title:'${message("审核人")}', name:'check_store_member_name' ,width:120, align:'center'},
		{ title:'${message("订单审核日期")}', name:'check_date' ,width:120, align:'center'},
		{ title:'${message("机构")}', name:'sale_org_name' ,width:120, align:'center'},
		{ title:'${message("创建日期")}', name:'create_date', width:120 ,align:'center' },
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
		useCountText:false,
		recordRowId:true,
        cols: cols,
        url: 'order_item_list_data.jhtml',
        onSelectRow:function(row,e,val){
        	if(val){
        		var $input = $(e).find("input.quantity");
        		map[row.id] = Number($input.val());
         		var $volume=$(e).find("input.volume").val();
//          		 var volume = $volume.toFixed(6);
        		var $boxQuantity=$(e).find("input.boxQuantity").val();
				var $branchQuantity=$(e).find("input.branchQuantity").val();
				var $closedQuantity=$(e).find("input.closedQuantity").val();
				
// 				mapT[Number($boxQuantity)]=Number($branchQuantity);
        		mapBx[row.id] = Number(Number($boxQuantity));
        		mapBc[row.id] = Number(Number($branchQuantity));
        		mapCq[row.id] = Number(Number($closedQuantity));
        		mapSb[row.id] = Number(Number($closedQuantity));
				
        	}else{
        		map[row.id] = undefined;
        		mapBx[row.id] = undefined;
        		mapBc[row.id] = undefined;
        		mapCq[row.id] = undefined;
        	}
        	$(".recordsChecked").text($mmGrid.selecteds.length);
        	$(".total").text(countTotal());
        },
        onSelectAll:function(rows,e,val){
        	$(e).each(function(index){
        			var row = rows[index];
        			if(val){
        				var $tr = $(this);
	        			var $input = $tr.find("input.quantity");
	        			var value = $input.val();
        				map[row.id] = Number(value);
        				
        				var $boxQuantity=$tr.find("input.boxQuantity").val();
        				var $branchQuantity=$tr.find("input.branchQuantity").val();
        				var $closedQuantity=$tr.find("input.closedQuantity").val();
//         				mapT[Number($boxQuantity)]=Number($branchQuantity);
        				mapBx[row.id] = Number(Number($boxQuantity));
                		mapBc[row.id] = Number(Number($branchQuantity));
                		mapCq[row.id] = Number(Number($closedQuantity));
        			}else{
        				map[row.id] = undefined;
        				mapBx[row.id] = undefined;
                		mapBc[row.id] = undefined;
                		mapCq[row.id] = undefined;
        			}
        	})
        	$(".recordsChecked").text($mmGrid.selecteds.length);
        	$(".total").text(countTotal());
        },
        callback:function(){
        	//addQtyBtnEvent();
        	$(".recordsChecked").text($mmGrid.selecteds.length);
        	$(".total").text(countTotal());
        	//$("input.checkAll").prop("disabled",true);
        },
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
    $("#shippingBtn").click(function(){
            
    	var data = map;
    	var params = '';
    	var params2 = '';
    	var params3 = '';
    	var params4 = '';
    	var params5 = '';
    	for(var key in map){
    		var value = map[key];
    		var bxQ=mapBx[key];
    		var bcQ=mapBc[key];
    		var cq=mapCq[key];
    		if(value!=undefined){
	    		params+='&ids='+key;
	    		params2+='&quantity='+value;
    		}
    		if(bxQ!=undefined){
    			params3+='&boxQuantity='+bxQ;
    		}
    		if(bcQ!=undefined){
    			params4+='&branchQuantity='+bcQ;
    		}
    		if(cq!=undefined){
    			params5+='&closedQuantity='+cq;
    		}
    	}
    	if(params.length==0){
    		$.message_alert("请选择发货明细");
    		return false;
    	}
    	
    	//var storeId = $("input[name='storeId']").val();
		//params = params.substring(1,params.length)+params2+'&storeId='+storeId;
		params = params.substring(1,params.length)+params2+params3+params4+params5;    		
		var rows = $mmGrid.selectedRows();
		if(rows[0].st_id!=null){
		params+='&supplierId='+rows[0].st_id+'&supplierName='+rows[0].st_name;
		}
		
		ajaxSubmit(null,{
			url:"check_data.jhtml?"+params,
			method:"post",
			async: false,
			data:data,
			callback:function(resultMsg){
				var data = $.parseJSON(resultMsg.content);
				for(var key in data){
					var value = data[key];
					if(value!=undefined){
						params+="&"+key+"="+encodeURIComponent(value);
					}
				}
				
				var h = $(window).height();
				var w = $(window).width()-3;
				var url = "shipping_operate_list.jhtml?"+params;
				edit(url);
				/**
				var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
				var $dialog = $.dialog({
					title:'${message("生成发货通知单")}',
					width:w,
					height:h,
					content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-88)+"px'><\/iframe>",
					onOk: function() {
						var result = $("#"+iframeId)[0].contentWindow.childMethod();
						if(!result){
							return false;
						}
						total=0;
						map = {};
						
						$mmGrid.load({pageNumner:1});
					}
				});
				$dialog.css({'max-height':h+'px','top':'0px','left':'0px'});
				*/
			}
		});
		removeCheck();
		
		
		
		
    })

				
			
});			

function countTotal(){
	total = 0;
	for(var key in map) { 
		var value = map[key];
		if(value!=undefined){
			total = total+value;
			total = total.toFixed(6);
			total = parseFloat(total);
		}
	} 
	return total;
}

function newclosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".saleOrg > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='saleOrgName']").attr("value",allName2)
};

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = 'to_condition_export_create.jhtml';//分页导出统计页面
	var url = 'condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的订单！")}';//提示
	var url = 'selected_export_create.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

	function check_view(url){
		var w = $(window).width();
		var h = $(window).height();
		var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
		var $dialog = $.dialog({
			title:'${message("查看订单")}',
			width:w,
			height:h,
			content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+(h-50)+"px'><\/iframe>",
			ok: null,
			cancel: null,
			onOk: function() {
			
			}
		});
		$dialog.find(".dialogContent").css("height",h+"px");
		$dialog.css("top",0+"px").css("max-height",h+"px");
		
	
	}

</script>
</head>
<body>

	<form id="listForm" action="list.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>
						导入导出
					</a>
					<ul class="flag-list">
						<li>
							<a href="javascript:void(0)" onclick="exportExcel(this)">
								<i class="flag-imp02"></i>
								${message("选择导出")}
							</a>
						</li>
						<li>
							<a href="javascript:void(0)" onclick="segmentedExport(this)">
								<i class="flag-imp02"></i>
								${message("条件导出")}
							</a>
						</li>

					</ul>
				</div>

			</div>
			<div id="searchDiv">
				<div id="search-content">
					<dl>
						<dt>
							<p>${message("客户名称")}:</p>
						</dt>
						<dd>
							<span style="position: relative">
								<input name="storeId" class="text storeId" type="hidden">
									<input class="text storeName" maxlength="200" type="text" name="storeName"
										onkeyup="clearSelect(this)" readonly
									>
										<input type="button" class="iconSearch" value="" id="selectStore">
							</span>
						</dd>
					</dl>
					<!-- 2019-05-16 冯旗加创建人 机构搜索条件 -->
					<dl>
        			<dt><p>${message("机构名称")}：</p></dt>
        			<dd >
        				<span class="search" style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear"/>
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" />
							<input type="button" class="iconSearch" id="selectSaleOrg">
							<div class="pupTitleName  saleOrg"></div>
						</span>
        			</dd>
        			</dl>
					<dl>
						<dt>
							<p>${message("创建人")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="storeMemberName" value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("收货人")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="consignee" value="" btn-fun="clear" />
						</dd>
					</dl>
					<!--
	    		<dl>
					<dt><p>${message("供应商")}:</p></dt>
    				<dd>
    					<span style="position:relative">
							<input name="supplierId" class="text supplierId" type="hidden">
							<input class="text supplierName" maxlength="200" type="text" name="supplierName" onkeyup="clearSelect(this)">
							<input type="button" class="iconSearch" value="" id="selectSupplier">
						</span>
    				</dd>
	    		</dl>
	    		-->
					<dl>
						<dt>
							<p>${message("订单编号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="orderSn" value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("产品名称")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="name" value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("12211")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="vonderCode" value="" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("产品型号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="model" value="" btn-fun="clear" />
						</dd>
					</dl>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>

		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator" style="text-align: left;">
				<div id="paginator"></div>
			</div>
		</div>
		<div class="floatDiv"
			style="position: fixed; top: 0; left: 120px; height: 38px; text-align: right;"
		>
			<p class="checked-p" style="line-height: 38px; font-size: 14px;">
				当前已选择发货明细
				<span class="recordsChecked" style="color: blue">0</span>
				行，总数量
				<span class="total" style="color: blue">0</span>
				， 请点击
				<a href="javascript:void(0);" class="iconButton" id="shippingBtn" style="float: none;">添加</a>
				， 创建发货通知单！
			</p>
		</div>
	</form>
</body>
</html>